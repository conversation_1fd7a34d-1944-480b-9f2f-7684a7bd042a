deploy-sec:
	AWS_PROFILE=sec npx cdk deploy --require-approval any-change -c config=sec-ops-us-west-2 $(args)
.PHONY: deploy-sec

deploy-dev:
	AWS_PROFILE=dev npx cdk deploy --require-approval any-change -c config=dev-us-west-2 $(args)
.PHONY: deploy-dev

deploy-dev-standard:
	AWS_PROFILE=dev npx cdk deploy --require-approval any-change -c config=dev-standard-us-east-1 $(args)
.PHONY: deploy-dev-standard

deploy-prod:
	AWS_PROFILE=prod npx cdk deploy --require-approval any-change -c config=prod-us-west-2 $(args)
.PHONY: deploy-prod

deploy-prod-standard:
	AWS_PROFILE=prod npx cdk deploy --require-approval any-change -c config=prod-standard-us-east-1 $(args)
.PHONY: deploy-prod-standard
