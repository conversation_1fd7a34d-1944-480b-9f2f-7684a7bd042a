{"awsEnvAccount": {"awsAccountID": "************", "rootRegion": "us-west-2", "targetRegion": "us-east-1", "awsOrgRootAccountID": "************", "notificationEmail": "<EMAIL>"}, "cdkAppInfo": {"app": "core-aws-stack", "environment": "mgt", "version": "0.0.0", "build": "0"}, "awsSecurityAlarms": {"logGroupArn": "arn:aws:logs:us-east-1:************:log-group:management-events-global-cloudtrail:*", "snsTopicArn": "arn:aws:sns:us-east-1:************:SecOps-CloudTrail-Alarms", "logMetricDimensions": {"recipientAccountId": "$.recipientAccountId"}, "targetAccounts": [{"name": "mgt", "id": "************"}, {"name": "dev", "id": "************"}, {"name": "prod", "id": "************"}, {"name": "security", "id": "************"}], "logMetricAlarms": [{"filterPattern": "{($.eventName=DeleteGroupPolicy) || ($.eventName=DeleteRolePolicy) || ($.eventName=DeleteUserPolicy) || ($.eventName=PutGroupPolicy) || ($.eventName=PutRolePolicy) || ($.eventName=PutUserPolicy) || ($.eventName=CreatePolicy) || ($.eventName=DeletePolicy) || ($.eventName=CreatePolicyVersion) || ($.eventName=DeletePolicyVersion) || ($.eventName=AttachRolePolicy) || ($.eventName=DetachRolePolicy) || ($.eventName=AttachUserPolicy) || ($.eventName=DetachUserPolicy) || ($.eventName=AttachGroupPolicy) || ($.eventName=DetachGroupPolicy)}", "alarm": {"name": "secops-iam-policy-changes", "description": "Component: IAM\nReason: Policy change\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"metricName": "secops-iam-policy-changes", "namespace": "LogMetrics", "statistic": "Average"}}}, {"filterPattern": "{($.eventName=CreateTrail) || ($.eventName=UpdateTrail) || ($.eventName=DeleteTrail) || ($.eventName=StartLogging) || ($.eventName=StopLogging)}", "alarm": {"name": "secops-cloudtrail-configuration-changes", "description": "Component: CloudTrail\nReason: Configuration change\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-cloudtrail-configuration-changes", "statistic": "Sum"}}}, {"filterPattern": "{($.eventName=ConsoleLogin) && ($.errorMessage=\"Failed authentication\")}", "alarm": {"name": "secops-management-console-authentication-failures", "description": "Component: IAM\nReason: Management Console authentication failures\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-management-console-authentication-failures", "statistic": "Sum"}}}, {"filterPattern": "{($.eventSource=kms.amazonaws.com) && (($.eventName=DisableKey) || ($.eventName=ScheduleKeyDeletion))}", "alarm": {"name": "secops-disabling-or-scheduled-deletion-of-customer-managed-keys", "description": "Component: KMS\nReason: Disabling or scheduled deletion of customer managed keys\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-disabling-or-scheduled-deletion-of-customer-managed-keys", "statistic": "Sum"}}}, {"filterPattern": "{($.eventSource=s3.amazonaws.com) && (($.eventName=PutBucketAcl) || ($.eventName=PutBucketPolicy) || ($.eventName=PutBucketCors) || ($.eventName=PutBucketLifecycle) || ($.eventName=PutBucketReplication) || ($.eventName=DeleteBucketPolicy) || ($.eventName=DeleteBucketCors) || ($.eventName=DeleteBucketLifecycle) || ($.eventName=DeleteBucketReplication))}", "alarm": {"name": "secops-s3-bucket-policy-changes", "description": "Component: S3\nReason: S3 bucket policy changes\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-s3-bucket-policy-changes", "statistic": "Sum"}}}, {"filterPattern": "{($.eventSource=config.amazonaws.com) && (($.eventName=StopConfigurationRecorder) || ($.eventName=DeleteDeliveryChannel) || ($.eventName=PutDeliveryChannel) || ($.eventName=PutConfigurationRecorder))}", "alarm": {"name": "secops-aws-config-configuration-changes", "description": "Component: AWS Config\nReason:  AWS Config configuration changes\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-aws-config-configuration-changes", "statistic": "Sum"}}}, {"filterPattern": "{($.eventName=AuthorizeSecurityGroupIngress) || ($.eventName=AuthorizeSecurityGroupEgress) || ($.eventName=RevokeSecurityGroupIngress) || ($.eventName=RevokeSecurityGroupEgress) || ($.eventName=CreateSecurityGroup) || ($.eventName=DeleteSecurityGroup)}", "alarm": {"name": "secops-security-group-changes", "description": "Component: EC2\nReason: Security group changes\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-security-group-changes", "statistic": "Sum"}}}, {"filterPattern": "{($.eventName=CreateNetworkAcl) || ($.eventName=CreateNetworkAclEntry) || ($.eventName=DeleteNetworkAcl) || ($.eventName=DeleteNetworkAclEntry) || ($.eventName=ReplaceNetworkAclEntry) || ($.eventName=ReplaceNetworkAclAssociation)}", "alarm": {"name": "secops-network-access-control-lists-nacl", "description": "Component: VPC NACL\nReason: Changes to Network Access Control Lists (NACL)\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-network-access-control-lists-nacl", "statistic": "Sum"}}}, {"filterPattern": "{($.eventName=CreateCustomerGateway) || ($.eventName=DeleteCustomerGateway) || ($.eventName=AttachInternetGateway) || ($.eventName=CreateInternetGateway) || ($.eventName=DeleteInternetGateway) || ($.eventName=DetachInternetGateway)}", "alarm": {"name": "secops-changes-to-network-gateways", "description": "Component: VPC\nReason: Changes to Network Gateways \nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-changes-to-network-gateways", "statistic": "Sum"}}}, {"filterPattern": "{($.eventName=CreateRoute) || ($.eventName=CreateRouteTable) || ($.eventName=ReplaceRoute) || ($.eventName=ReplaceRouteTableAssociation) || ($.eventName=DeleteRouteTable) || ($.eventName=DeleteRoute) || ($.eventName=DisassociateRouteTable)}", "alarm": {"name": "secops-route-table-changes", "description": "Component: VPC\nReason: Changes to Route Tables\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-route-table-changess", "statistic": "Sum"}}}, {"filterPattern": "{($.eventName=CreateVpc) || ($.eventName=DeleteVpc) || ($.eventName=ModifyVpcAttribute) || ($.eventName=AcceptVpcPeeringConnection) || ($.eventName=CreateVpcPeeringConnection) || ($.eventName=DeleteVpcPeeringConnection) || ($.eventName=RejectVpcPeeringConnection) || ($.eventName=AttachClassicLinkVpc) || ($.eventName=DetachClassicLinkVpc) || ($.eventName=DisableVpcClassicLink) || ($.eventName=EnableVpcClassicLink)}", "alarm": {"name": "secops-vpc-changes", "description": "Component: VPC\nReason: Changes to VPC\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-vpc-changes", "statistic": "Sum"}}}, {"filterPattern": "{ $.userIdentity.type = \"Root\" && $.userIdentity.invokedBy NOT EXISTS && $.eventType != \"AwsServiceEvent\" }", "alarm": {"name": "secops-usage-of-root-account", "description": "Component: IAM\nReason: Usage of Root Account\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-usage-of-root-account", "statistic": "Sum"}}}, {"filterPattern": "{ $.eventName = ConsoleLogin && $.additionalEventData.MFAUsed = \"No\" }", "alarm": {"name": "secops-console-sign-in-without-mfa", "description": "Component: IAM\nReason: <PERSON><PERSON><PERSON> Sign-in Without MFA\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-console-sign-in-without-mfa", "statistic": "Sum"}}}, {"filterPattern": "{ ($.eventSource = organizations.amazonaws.com) && (($.eventName = \"AcceptHandshake\") || ($.eventName = \"AttachPolicy\") || ($.eventName = \"CreateAccount\") || ($.eventName = \"CreateOrganizationalUnit\") || ($.eventName = \"CreatePolicy\") || ($.eventName = \"DeclineHandshake\") || ($.eventName = \"DeleteOrganization\") || ($.eventName = \"DeleteOrganizationalUnit\") || ($.eventName = \"DeletePolicy\") || ($.eventName = \"DetachPolicy\") || ($.eventName = \"DisablePolicyType\") || ($.eventName = \"EnablePolicyType\") || ($.eventName = \"InviteAccountToOrganization\") || ($.eventName = \"LeaveOrganization\") || ($.eventName = \"MoveAccount\") || ($.eventName = \"RemoveAccountFromOrganization\") || ($.eventName = \"UpdatePolicy\") || ($.eventName = \"UpdateOrganizationalUnit\")) }", "alarm": {"name": "secops-aws-organizations-changes", "description": "Component: AWS Organizations\nReason: Changes to AWS Organizations\nSource: SecOps CloudTrail", "comparisonOperator": "GreaterThanOrEqualToThreshold", "cloudWatchMetric": {"namespace": "LogMetrics", "metricName": "secops-aws-organizations-changes", "statistic": "Sum"}}}]}}