{"awsEnvAccount": {"awsAccountID": "************", "rootRegion": "us-west-2", "targetRegion": "us-east-1", "awsOrgRootAccountID": "************", "notificationEmail": "<EMAIL>"}, "cdkAppInfo": {"app": "core-aws-stack", "environment": "prod", "version": "0.0.0", "build": "0"}, "dns": {"route53HostedZoneID": "Z06222132KYCTR0G03TIK", "route53HostedZoneName": "prod.getunblocked.com", "cnameRecords": [{"recordName": "alb.prod.getunblocked.com", "domainName": "alb.us-west-2.prod.getunblocked.com", "comment": "cname record pointing to prod public alb endpoint"}, {"recordName": "ml.alb.prod.getunblocked.com", "domainName": "ml-alb-us-west-2.prod.getunblocked.com", "comment": "cname record pointing to prod public alb endpoint"}, {"recordName": "admin.prod.getunblocked.com", "domainName": "adminweb.us-west-2.prod.getunblocked.com", "comment": "cname record pointing to prod adminweb service alb endpoint"}]}, "certRequests": [{"name": "CloudFrontCert", "domainName": "getunblocked.com", "subjectAlternativeNames": ["prod.getunblocked.com", "www.getunblocked.com"], "addRegionWildcardAsSan": false, "useEmailValidation": true}, {"name": "RedirectCloudFrontCert", "domainName": "redirect.prod.getunblocked.com", "subjectAlternativeNames": ["*.getunblocked.com"], "addRegionWildcardAsSan": false, "useEmailValidation": true}, {"name": "SendGridCloudFrontCert", "domainName": "url2410.getunblocked.com", "subjectAlternativeNames": ["email.getunblocked.com"], "addRegionWildcardAsSan": false, "useEmailValidation": true}, {"name": "DocsCloudFrontCert", "domainName": "docs.getunblocked.com", "subjectAlternativeNames": ["getunblocked.com", "docs.prod.getunblocked.com"], "addRegionWildcardAsSan": false, "useEmailValidation": true}], "webAclCloudFront": {"name": "waf-cloudfront", "endpointIPFilterRules": [{"name": "github-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/github"], "ipv4CIDRWhiteList": ["************/22", "*************/22", "************/20", "***********/20"]}, {"name": "gitlab-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/gitlab"], "ipv4CIDRWhiteList": ["***********/28", "***********/24"]}, {"name": "bitbucket-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/bitbucket"], "ipv4CIDRWhiteList": ["**********/28", "************/28", "*************/28", "*************/28", "*************/28", "*************/28", "**************/28", "***************/28", "***************/28", "***************/28", "***************/28", "***************/28", "***************/28", "***************/28"]}, {"name": "assemblyai-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/transcription"], "ipv4CIDRWhiteList": ["************/32"]}, {"name": "stripe-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/stripe"], "ipv4CIDRWhiteList": ["**********/32", "*************/32", "*************/32", "**************/32", "*************/32", "**************/32", "************/32", "*************/32", "*************/32", "**************/32", "**************/32", "*************/32"]}], "rateBasedRules": [{"name": "PusherPerUserRateLimit", "block": true, "limit": 700, "positionalConstraint": "STARTS_WITH", "searchString": "/api/channels/", "aggregateKeyType": "CUSTOM_KEYS", "aggregateKeyHeaders": ["x-unblocked-subject"], "headerTextTransformationType": "LOWERCASE"}, {"name": "PusherPerClientPerUserRateLimit", "block": true, "limit": 400, "positionalConstraint": "STARTS_WITH", "searchString": "/api/channels/", "aggregateKeyType": "CUSTOM_KEYS", "aggregateKeyHeaders": ["x-unblocked-client-id", "x-unblocked-subject"], "headerTextTransformationType": "LOWERCASE"}, {"name": "LogsPerClientPerUserRateLimit", "block": true, "limit": 1000, "positionalConstraint": "STARTS_WITH", "searchString": "/api/logs", "aggregateKeyType": "CUSTOM_KEYS", "aggregateKeyHeaders": ["x-unblocked-client-id", "x-unblocked-subject"], "headerTextTransformationType": "LOWERCASE"}, {"name": "ApiPerClientPerUserRateLimit", "block": false, "limit": 1000, "positionalConstraint": "STARTS_WITH", "searchString": "/api/", "aggregateKeyType": "CUSTOM_KEYS", "aggregateKeyHeaders": ["x-unblocked-client-id", "x-unblocked-subject"], "headerTextTransformationType": "LOWERCASE"}, {"name": "PublicApiRateLimit", "block": true, "limit": 4000, "positionalConstraint": "STARTS_WITH", "searchString": " /api/v1/", "aggregateKeyType": "IP", "headerTextTransformationType": "LOWERCASE"}, {"name": "ApiRateLimit", "block": false, "limit": 4000, "positionalConstraint": "STARTS_WITH", "searchString": " /api/", "aggregateKeyType": "IP", "headerTextTransformationType": "LOWERCASE"}]}, "cloudFront": {"domainNames": ["getunblocked.com", "prod.getunblocked.com", "www.getunblocked.com"], "certName": "CloudFrontCert", "csp": ["default-src 'self' *.clarity.ms c.bing.com", "img-src blob: data: https:", "media-src https:", "style-src 'unsafe-inline' https:", "script-src 'self' *.googletagmanager.com *.intercom.io *.intercomcdn.com *.segment.io *.umami.is *.licdn.com *.segment.com *.redditstatic.com js.stripe.com *.js.stripe.com *.googleapis.com cdn.syftdata.com cdn.jsdelivr.net", "worker-src 'self' blob:", "child-src 'self' blob:", "frame-src *.js.stripe.com js.stripe.com hooks.stripe.com app.vidzflow.com", "connect-src 'self' user-assets-prod-us-west-2.s3.us-west-2.amazonaws.com user-assets-prod-us-east-2.s3.us-east-2.amazonaws.com *.google-analytics.com *.intercom.io *.intercomcdn.com *.segment.io *.sentry.io wss://*.intercom.io user-image-assets-prod-us-west-2.s3.us-west-2.amazonaws.com user-image-assets-prod-us-east-2.s3.us-east-2.amazonaws.com user-image-assets-dev-us-west-2.s3.us-west-2.amazonaws.com user-image-assets-dev-us-east-2.s3.us-east-2.amazonaws.com *.umami.dev *.licdn.com *.ads.linkedin.com *.segment.com ads.reddit.com *.redditstatic.com pixel-config.reddit.com maps.googleapis.com api.stripe.com use.typekit.net", "font-src 'self' data: *"]}, "docsCloudFront": {"domainNames": ["docs.getunblocked.com", "docs.prod.getunblocked.com"], "certName": "DocsCloudFrontCert"}, "sendGridCloudFront": {"domainNames": ["url2410.getunblocked.com", "email.getunblocked.com"], "certName": "SendGridCloudFrontCert"}, "subdomainRedirectCloudFront": {"domainNames": ["redirect.prod.getunblocked.com", "*.getunblocked.com"], "certName": "RedirectCloudFrontCert"}, "staticSites": [{"name": "dashboard", "pathPattern": "/dashboard/*", "s3BucketName": "dashboard.prod.getunblocked.com", "enableVersioning": true}, {"name": "download-assets", "pathPattern": "/download-assets/*", "s3BucketName": "download-assets.prod.getunblocked.com", "enableVersioning": true}, {"name": "releases", "pathPattern": "/releases/*", "s3BucketName": "releases.prod.getunblocked.com", "enableVersioning": true}, {"name": "landing-page", "pathPattern": "/*", "s3BucketName": "landing-page.prod.getunblocked.com", "enableVersioning": true, "corsRule": {"corsAllowedOrigins": ["https://app.unbounce.com", "https://fbbd738990f04734849bb6c0a3c4f686.unbouncepages.com"], "corsAllowedHeaders": ["*"], "corsAllowedMethods": ["GET"], "corsExposeHeaders": ["Access-Control-Allow-Origin"]}}, {"name": "docs", "pathPattern": "/docs/*", "s3BucketName": "docs.prod.getunblocked.com", "enableVersioning": true}]}