{"awsEnvAccount": {"awsAccountID": "************", "rootRegion": "us-west-2", "targetRegion": "us-west-2", "awsOrgRootAccountID": "************", "notificationEmail": "<EMAIL>"}, "cdkAppInfo": {"app": "core-aws-stack", "environment": "mgt", "version": "0.0.0", "build": "0"}, "dns": {"route53HostedZoneID": "Z037881216E05O2IEJW2T", "route53HostedZoneName": "getunblocked.com", "aRecords": [], "cnameRecords": [{"recordName": "dashboard.getunblocked.com", "domainName": "dashboard.prod.getunblocked.com", "comment": "cname record pointing to prod Dashboard static site"}]}, "iam": {"groups": [{"name": "all-envs-cloudwatch-read-access-CDK", "members": ["grafana-datasource"], "policyStatements": [{"actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::************:role/CloudWatchReadOnlyAccessForGrafana", "arn:aws:iam::************:role/CloudWatchReadOnlyAccessForGrafana", "arn:aws:iam::************:role/CloudWatchReadOnlyAccessForGrafana"]}]}, {"name": "all-envs-s3-read-access-CDK", "members": ["deploygod"], "policyStatements": [{"actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::************:role/CrossAccountS3ReadOnlyRole", "arn:aws:iam::************:role/CrossAccountS3ReadOnlyRole", "arn:aws:iam::************:role/CrossAccountS3ReadOnlyRole"]}]}, {"name": "all-envs-k8s-default-namespace-deploy-access-CDK", "members": ["deploybot"], "policyStatements": [{"actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::************:role/K8sDeployerRole", "arn:aws:iam::************:role/K8sDeployerRole"]}]}, {"name": "all-envs-k8s-default-namespace-read-access-CDK", "members": ["deploybot"], "policyStatements": [{"actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::************:role/K8sReaderRole", "arn:aws:iam::************:role/K8sReaderRole"]}]}, {"name": "all-envs-static-site-deploy-access-CDK", "members": ["deploybot"], "policyStatements": [{"actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::************:role/S3StaticSiteDeployerRole-dashboard", "arn:aws:iam::************:role/S3StaticSiteDeployerRole-dashboard", "arn:aws:iam::************:role/S3StaticSiteDeployerRole-landing-page", "arn:aws:iam::************:role/S3StaticSiteDeployerRole-landing-page", "arn:aws:iam::************:role/S3StaticSiteDeployerRole-download-assets", "arn:aws:iam::************:role/S3StaticSiteDeployerRole-download-assets", "arn:aws:iam::************:role/S3StaticSiteDeployerRole-releases", "arn:aws:iam::************:role/S3StaticSiteDeployerRole-releases"]}]}, {"name": "ecr-power-user-sec-ops-account-CDK", "members": ["deploybot"], "policyStatements": [{"actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::************:role/EcrDeployerRole", "arn:aws:iam::************:role/AmazonEC2ContainerRegistryPowerUser"]}]}, {"name": "gh-actions-cache-sec-ops-account-CDK", "members": ["deploybot"], "policyStatements": [{"actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::************:role/IAMS3AccessRole-unblocked-gh-actions-s3-cache"]}]}, {"name": "source-code-backup-sec-ops-account-CDK", "members": ["deploybot"], "policyStatements": [{"actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::************:role/IAMS3AccessRole-unblocked-source-code-backups"]}]}, {"name": "route53-manage-records-mgt-account-CDK", "members": ["deploygod"], "policyStatements": [{"actions": ["route53:ChangeResourceRecordSets"], "resources": ["arn:aws:route53:::hostedzone/*"]}, {"actions": ["route53:ListHostedZones", "route53:ListResourceRecordSets"], "resources": ["*"]}]}, {"name": "ec2-deploybot-user-sec-ops-account-CDK", "members": ["deploybot"], "policyStatements": [{"actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::************:role/Ec2DeploybotRole"]}]}]}}