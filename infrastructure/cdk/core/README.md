# Welcome to your CDK TypeScript project!

This is an AWS CDK Typescript project used to deploying core infrastructure components such as VPC Networking, Databases, Storage, Compute etc to all environments

## Local Environment Setup

1. Setup AWS access and configure AWS CLI: [AWS Access Setup](https://www.notion.so/nextchaptersoftware/AWS-IAM-User-Access-Setup-9df153f43d6b43228d7973df5aa26d40)
    1. Make sure to install `awsp` for role switching
    2. Switch to `dev` environment `awsp dev`
2. Install/Upgrade [node.js](https://nodejs.org/) if needed (AWS CDK uses Node.js >= 10.13.0)
3. Install AWS CDK `npm install -g aws-cdk@latest`
4. Install Typescript `npm install -g typescript`
5. Install dependencies `cd infrastructure/cdk/core && npm install`
6. Verify your setup by running the following commands:

    ```bash

     cdk synth --all -c config=dev-us-west-2

     ##  Out put should be similar to this ##
     # Successfully synthesized to infrastructure/cdk/core/cdk.out
     # Supply a stack id (NetworkStack, DatabaseStack ...) to display its template.

     cdk diff  -c config=dev-us-west-2

     ##  Out put should be similar to this ##
     # Stack NetworkStack
     # There were no differences
     # Stack DatabaseStack
     # There were no differences
     # ...
    ```

7. If you are new to AWS CDK development take a look at [AWS CDK Intro Workshop](https://cdkworkshop.com/20-typescript.html)
8. Familiarize yourself with CDK API([doc](https://docs.aws.amazon.com/cdk/api/v2/docs/aws-construct-library.html))
9. Take a look at CDK examples [here](https://github.com/aws-samples/aws-cdk-examples/tree/master/typescript)

## Deployment and Environment Configurations

-   EKS cluster must be deployed using `eksctl` [instructions](../../eks/README.md) before deploying CDK stack to a new environment-region
-   Each Environment-Region combination must have a dedicated configuration JSON file under `infrastructure/cdk/core/config`.
-   Data model for the configuration JSON is defined in `infrastructure/cdk/core/lib/build-config.ts`.

## Useful commands

The `cdk.json` file tells the CDK Toolkit how to execute your app.

-   `npm run build` compile typescript to js
-   `npm run watch` watch for changes and compile
-   `npm run test` perform the jest unit tests
-   `cdk deploy` deploy this stack to your default AWS account/region
-   `cdk diff` compare deployed stack with current state
-   `cdk synth` emits the synthesized CloudFormation template

### Bootstrap New Region

```
cdk bootstrap aws://${ACCOUNT_ID_HERE}/${REGION_HERE} --context config=${ENV}-${REGION_HERE}

# Example for Dev in us-west-2
cdk bootstrap aws://************/us-west-2 --context config=dev-us-west-2

```

## Useful Links

-   [AWS CDK Best Practices](https://aws.amazon.com/blogs/devops/best-practices-for-developing-cloud-applications-with-aws-cdk/)
-   [Typescript CDK Examples Repo](https://github.com/aws-samples/aws-cdk-examples/tree/master/typescript)
