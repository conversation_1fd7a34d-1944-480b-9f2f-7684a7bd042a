{"name": "core", "version": "0.1.0", "bin": {"core": "bin/core.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "check-lint": "npm run check-pretty && npm run check-eslint", "fix-lint": "npm run fix-pretty && npm run fix-eslint", "check-pretty": "prettier --check .", "fix-pretty": "prettier --write .", "check-eslint": "eslint '**/*.{js,jsx,ts,tsx}'", "fix-eslint": "eslint '**/*.{js,jsx,ts,tsx}' --fix", "update-dependencies": "npx ncu -u"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/js-yaml": "^4.0.9", "@types/node": "24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.3", "jest": "^30.0.5", "npm-check-updates": "^18.0.2", "ts-jest": "^29.4.0", "ts-json-object": "^0.4.1", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "dependencies": {"@aws-cdk/aws-sagemaker-alpha": "^2.208.0-alpha.0", "@aws-quickstart/eks-blueprints": "^1.17.2", "@types/dotenv-safe": "^8.1.6", "aws-cdk": "^2.1023.0", "aws-cdk-lib": "^2.208.0", "constructs": "^10.4.2", "dotenv": "^17.2.1", "source-map-support": "^0.5.21"}, "overrides": {"@aws-quickstart/eks-blueprints": {"aws-cdk": "$aws-cdk", "aws-cdk-lib": "$aws-cdk-lib"}}}