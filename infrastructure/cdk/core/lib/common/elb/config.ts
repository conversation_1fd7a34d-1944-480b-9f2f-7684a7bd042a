import { JSONObject, optional } from 'ts-json-object';

export class HealthCheckConfig extends JSONObject {
    @optional(undefined)
    intervalInSeconds?: number; // Interval in seconds

    @optional(undefined)
    timeoutInSeconds?: number; // Timeout in seconds

    @optional(undefined)
    unhealthyThresholdCount?: number; // Number of consecutive failures

    @optional(undefined)
    healthyThresholdCount?: number; // Number of consecutive successes
}
