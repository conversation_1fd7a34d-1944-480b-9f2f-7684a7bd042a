import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { BucketEncryption, CorsRule, IntelligentTieringConfiguration } from 'aws-cdk-lib/aws-s3';
import { CfnOutput, Duration, RemovalPolicy } from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as kms from 'aws-cdk-lib/aws-kms';
import { PolicyStatement, Role, ServicePrincipal } from 'aws-cdk-lib/aws-iam';

import { S3BucketTier } from './s3/config';

export interface S3BucketConstructProps {
    awsEnvAccount: AwsEnvAccount;
    cdkAppInfo: CdkAppInfo;
    name: string;
    enableVersioning?: boolean;
    excludeEnvSuffix?: boolean;
    blockPublicAccess?: boolean;
    publicReadAccess: boolean;
    bucketTier?: S3BucketTier;
    removalPolicy?: RemovalPolicy;
    autoDeleteObjects?: boolean;
    addAccessLogPolicy?: boolean;
    addLambdaAccessPolicy?: boolean;
    createRootAccountIAMRole?: boolean;
    bucketEncryption?: BucketEncryption;
    cors?: CorsRule[];
    bucketKeyEnabled?: boolean;
    transferAcceleration?: boolean;
    enableBackupRetentionPolicyRule?: boolean;
    enableUserdataRetentionRule?: boolean;
    enableLogRetentionRule?: boolean;
    replicationTargetRegion?: string;
    replicationTargetKMSKeyArn?: string;
    id?: string;
}

export class S3BucketConstruct extends Construct {
    public readonly s3Bucket: s3.Bucket;

    constructor(scope: Construct, id: string, props: S3BucketConstructProps) {
        super(scope, id);
        const tiers: IntelligentTieringConfiguration[] | undefined = props.bucketTier
            ? [
                  {
                      name: props.bucketTier.name,
                      prefix: props.bucketTier.prefix,
                      archiveAccessTierTime: Duration.days(props.bucketTier.archiveAccessTierTimeDays),
                      deepArchiveAccessTierTime: Duration.days(props.bucketTier.deepArchiveAccessTierTimeDays),
                  },
              ]
            : [];

        const qualifiedBucketName = props.excludeEnvSuffix
            ? props.name
            : `${props.name}-${props.cdkAppInfo.environment}-${props.awsEnvAccount.targetRegion}`;

        this.s3Bucket = new s3.Bucket(this, props.id ?? qualifiedBucketName, {
            bucketKeyEnabled: props.bucketKeyEnabled,
            cors: props.cors,
            bucketName: qualifiedBucketName,
            versioned: !!props.enableVersioning,
            publicReadAccess: props.publicReadAccess,
            blockPublicAccess:
                props.blockPublicAccess == false
                    ? {
                          blockPublicAcls: false,
                          blockPublicPolicy: false,
                          ignorePublicAcls: false,
                          restrictPublicBuckets: false,
                      }
                    : s3.BlockPublicAccess.BLOCK_ALL,
            accessControl:
                props.blockPublicAccess && !props.blockPublicAccess ? s3.BucketAccessControl.PUBLIC_READ : undefined,
            removalPolicy: props.removalPolicy ? RemovalPolicy.RETAIN : undefined,
            autoDeleteObjects: props.autoDeleteObjects,
            intelligentTieringConfigurations: tiers,
            encryption: props.bucketEncryption,
            transferAcceleration: props.transferAcceleration,
        });

        // Only one of these rules should be applied at any given time
        // Destructive policies such as backup ad log require the word to be included in bucket name!
        if (props.enableBackupRetentionPolicyRule) {
            // Add data retention policy to backup buckets
            this.addBackupRetentionLifecycleRule(props);
        } else if (props.enableUserdataRetentionRule) {
            // This policy enforces our customer data retention policy
            this.addUserdataRetentionLifecycleRule();
        } else if (props.enableLogRetentionRule) {
            // Applies our log retention policy to buckets
            this.addLogRetentionLifecycleRule(props);
        } else {
            console.warn(`no lifecycle policy has been assigned to ${qualifiedBucketName}`);
        }

        // Create alias needed to grant IAM permissions
        if (props.bucketEncryption && this.s3Bucket.encryptionKey?.keyArn) {
            const bucketKey = kms.Key.fromKeyArn(
                this,
                `${qualifiedBucketName}-kms-key`,
                this.s3Bucket.encryptionKey?.keyArn
            );
            bucketKey.addAlias(`${qualifiedBucketName}-kms-key`);
        }

        if (props.replicationTargetRegion) {
            if (this.s3Bucket.encryptionKey?.keyArn && props.replicationTargetKMSKeyArn) {
                this.setupBucketReplication(props);
            } else {
                console.error('Replication is not allowed for unencrypted buckets');
            }
        }

        // Access log policies
        // Used by system components such as ALBs to publish access logs
        if (props.addAccessLogPolicy) {
            this.addAccessLogPolicy(props);
        }

        // Lambda Access policy
        // Used by Lambda service during function deployment to download deployment package (zip archives)
        if (props.addLambdaAccessPolicy) {
            this.addLambdaAccessPolicy();
        }

        // Create dedicated IAM role for users in root account to access this bucket
        if (props.createRootAccountIAMRole) {
            this.createRootAccountIAMRole(props);
        }

        new CfnOutput(this, `${props.name}-arn`, { value: this.s3Bucket.bucketArn });
    }

    /*
     * Creates IAM policy needed by IAM users under root account to access this bucket
     */
    private createRootAccountIAMRole(props: S3BucketConstructProps) {
        // Create role to be assumed by an IAM user in root account
        const deployerRole = new iam.Role(this, `IAMS3AccessRole-${props.name}`, {
            roleName: `IAMS3AccessRole-${props.name}`,
            assumedBy: new iam.AccountPrincipal(props.awsEnvAccount.awsOrgRootAccountID),
            description: 'Role for granting read, write, list and delete permissions to s3 bucket',
        });

        // Object policies
        this.s3Bucket.addToResourcePolicy(
            new iam.PolicyStatement({
                actions: ['s3:*'],
                resources: [this.s3Bucket.arnForObjects('*')],
                principals: [deployerRole],
            })
        );
        this.s3Bucket.addToResourcePolicy(
            new iam.PolicyStatement({
                actions: ['s3:List*'],
                resources: [this.s3Bucket.bucketArn],
                principals: [deployerRole],
            })
        );
        new CfnOutput(this, `${props.name}-root-account-iam-role-arn`, { value: deployerRole.roleArn });
    }

    /*
     * Creates IAM policy needed by AWS services such as ALB or WAF to publish access logs
     */
    private addAccessLogPolicy(props: S3BucketConstructProps) {
        this.s3Bucket.addToResourcePolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:PutObject'],
                resources: [this.s3Bucket.arnForObjects('*')],
                principals: [new iam.AccountPrincipal(this.getElbPrincipalAccountId(props.awsEnvAccount.targetRegion))],
            })
        );
    }

    /*
     * Creates IAM policy needed by AWS Lambda to deploy zip archives from a bucket
     */
    private addLambdaAccessPolicy() {
        this.s3Bucket.addToResourcePolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:GetObject'],
                resources: [this.s3Bucket.arnForObjects('*')],
                principals: [new iam.ServicePrincipal('lambda.amazonaws.com')],
            })
        );
    }

    /*
     * Create S3 lifecycle rule to ensure we are enforcing our backup retention policy
     * This rule is mainly used for backup (e.g RDS backup bucket) buckets
     * Number of days in this policy is obtained from our compliance policy
     */
    private addBackupRetentionLifecycleRule(props: S3BucketConstructProps) {
        // The following logic requires the word `backup` to be present in bucket name
        // to avoid mistakenly applying this rule to buckets with user data
        if (props.name.toLowerCase().includes('backup')) {
            this.s3Bucket.addLifecycleRule({
                enabled: true,
                expiration: Duration.days(29),
                noncurrentVersionExpiration: Duration.days(1),
                noncurrentVersionsToRetain: 1,
                id: 'backupDataRetentionPolicyRule',
            });
        }
    }

    /*
     * Creates S3 lifecycle rule for versioned buckets that hold customer assets
     */
    private addUserdataRetentionLifecycleRule() {
        this.s3Bucket.addLifecycleRule({
            enabled: true,
            expiredObjectDeleteMarker: true,
            noncurrentVersionExpiration: Duration.days(7),
            noncurrentVersionsToRetain: 4,
            id: 'userdataRetentionRulePolicyRule',
        });
    }

    /*
     * Create S3 lifecycle rule to ensure we are enforcing our backup retention policy
     * This rule is mainly used for backup (e.g RDS backup bucket) buckets
     * Number of days in this policy is obtained from our compliance policy
     */
    private addLogRetentionLifecycleRule(props: S3BucketConstructProps) {
        // The following logic requires the word `log` to be present in bucket name
        // to avoid mistakenly applying this rule to buckets with user data
        if (props.name.toLowerCase().includes('log')) {
            this.s3Bucket.addLifecycleRule({
                enabled: true,
                expiration: Duration.days(90),
                noncurrentVersionExpiration: Duration.days(7),
                noncurrentVersionsToRetain: 1,
                id: 'logRetentionPolicyRule',
            });
        }
    }

    private getElbPrincipalAccountId(region: string): string {
        // These are ALB/ELB principal IDs and not to be confused with our own account IDs
        // https://docs.aws.amazon.com/elasticloadbalancing/latest/application/load-balancer-access-logs.html#access-logging-bucket-permissions
        switch (region) {
            case 'us-west-1': {
                return '************';
            }
            case 'us-west-2': {
                return '************';
            }
            case 'us-east-2': {
                return '************';
            }
            default: {
                // us-east-1 standard region
                return '************';
            }
        }
    }

    /*
     * Setups up cross region bucket replication for encrypted version buckets
     */
    private setupBucketReplication(props: S3BucketConstructProps) {
        const srcBucketName = `${props.name}-${props.cdkAppInfo.environment}-${props.awsEnvAccount.targetRegion}`;
        const destBucketName = `${props.name}-${props.cdkAppInfo.environment}-${props.replicationTargetRegion}`;
        const sourceBucket = this.s3Bucket.bucketArn;
        const sourceObjects = `${sourceBucket}/*`;
        const destinationBucket = `arn:aws:s3:::${destBucketName}`;
        const destinationObjects = `${destinationBucket}/*`;

        const replicaRole = new Role(this, `${srcBucketName}-ReplicationRole`, {
            roleName: `${srcBucketName}-ReplicationRole`,
            assumedBy: new ServicePrincipal('s3.amazonaws.com'),
        });

        replicaRole.addToPolicy(
            new PolicyStatement({
                actions: [
                    's3:ListBucket',
                    's3:GetReplicationConfiguration',
                    's3:GetObjectVersionForReplication',
                    's3:GetObjectVersionAcl',
                    's3:GetObjectVersionTagging',
                    's3:GetObjectRetention',
                    's3:GetObjectLegalHold',
                ],
                resources: [sourceBucket, sourceObjects, destinationBucket, destinationObjects],
            })
        );
        replicaRole.addToPolicy(
            new PolicyStatement({
                actions: ['s3:Replicate*', 's3:ObjectOwnerOverrideToBucketOwner'],
                resources: [sourceObjects, destinationObjects],
            })
        );

        replicaRole.addToPolicy(
            new PolicyStatement({
                actions: ['kms:Encrypt'],
                resources: [props.replicationTargetKMSKeyArn || ''],
            })
        );

        replicaRole.addToPolicy(
            new PolicyStatement({
                actions: ['kms:Decrypt'],
                resources: [this.s3Bucket.encryptionKey?.keyArn || ''],
            })
        );

        const cfnBucket = this.s3Bucket.node.defaultChild as s3.CfnBucket;

        cfnBucket.replicationConfiguration = {
            role: replicaRole.roleArn,
            rules: [
                {
                    destination: {
                        bucket: destinationBucket,
                        account: props.awsEnvAccount.awsAccountID,
                        encryptionConfiguration: {
                            replicaKmsKeyId: props.replicationTargetKMSKeyArn || '',
                        },
                    },
                    status: 'Enabled',
                    sourceSelectionCriteria: {
                        sseKmsEncryptedObjects: {
                            status: 'Enabled',
                        },
                    },
                    id: `${destBucketName}-replication-rule`,
                },
            ],
        };
    }
}
