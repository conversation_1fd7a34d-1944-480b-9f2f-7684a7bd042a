import { array, JSONObject, optional, required } from 'ts-json-object';
import { DockerImageAsset } from '../docker/config';
import { Tag } from '../tag/config';

import { LaunchTemplate, SecurityGroup } from '../ec2/config';
import { EventBridgeRule } from '../events/config';

export class EFSVolumeOptions extends JSONObject {
    @required
    name: string;
    @required
    fileSystemId: string;
    @required
    securityGroupId: string;
    @optional(undefined)
    rootDirectory?: string;
    @optional(undefined)
    accessPointId?: string;
    @optional(false)
    useJobRole?: boolean;
    @optional(true)
    enableTransitEncryption: boolean;
    @required
    containerPath: string;
    @optional(undefined)
    readonly?: boolean;
}

export class ECSVolume extends JSONObject {
    @required
    efsVolumeOptions: EFSVolumeOptions;
}

export class ECSEC2ContainerDefinition extends JSONObject {
    @required
    name: string;
    @required
    cpus: number;
    @optional(0)
    gpus: number;
    @optional(1)
    memoryLimitGB: number;
    @optional(undefined)
    commands?: Array<string>;
    @required
    dockerImage: DockerImageAsset;
    @optional(undefined)
    privileged?: boolean;
    @optional(undefined)
    @array(ECSVolume)
    volumes?: Array<ECSVolume>;
    @optional(undefined)
    /* eslint-disable @typescript-eslint/no-explicit-any */
    environment: any;
}

export class EC2ECSComputeEnvironment extends JSONObject {
    @required
    name: string;
    @required
    instanceType: string;
    @required
    securityGroup: SecurityGroup;
    @optional(undefined)
    launchTemplate?: LaunchTemplate;
    @optional(true)
    replaceComputeEnvironment: boolean;
    @optional(false)
    useOptimalInstanceClasses: boolean;
}

export class ECSJobDefinition extends JSONObject {
    @required
    name: string;
    @required
    ecsEC2ContainerDefinition: ECSEC2ContainerDefinition;
    @optional(undefined)
    retryAttempts?: number;
    @optional(true)
    propagateTags: boolean;
}

/**
 *  This class is used to generate generic secrets
 *  that might be used for third parties etc.
 */
export class Batch extends JSONObject {
    @required
    name: string;
    @required
    ecsJobDefinition: ECSJobDefinition;
    @required
    ec2ECSComputeEnvironment: EC2ECSComputeEnvironment;
    @optional(120)
    timeoutInMin: number;
    @optional(true)
    propagateTags: boolean;
    @optional(undefined)
    tags?: Array<Tag>;
    @optional(undefined)
    @array(EventBridgeRule)
    eventBridgeRules?: Array<EventBridgeRule>;
}
