import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as batch from 'aws-cdk-lib/aws-batch';
import { EC2ECSComputeEnvironment } from './config';
import { SecurityGroupConstruct } from '../ec2/security-group-construct';
import { EC2Helpers } from '../utils/ec2-helpers';
import { LaunchTemplateConstruct } from '../ec2/launch-template-construct';

interface EC2ECSComputerEnvironmentProps {
    ec2ECSComputerEnvironment: EC2ECSComputeEnvironment;
    vpc: ec2.IVpc;
    vpcSubnets: ec2.SubnetSelection;
}

export class EC2EC2ComputeEnvironmentConstruct extends Construct {
    public readonly computeEnvironment: batch.IComputeEnvironment;

    constructor(scope: Construct, id: string, props: EC2ECSComputerEnvironmentProps) {
        super(scope, id);

        const sg = new SecurityGroupConstruct(this, `${props.ec2ECSComputerEnvironment.name}SG`, {
            securityGroup: props.ec2ECSComputerEnvironment.securityGroup,
            vpc: props.vpc,
        });

        const launchTemplate = props.ec2ECSComputerEnvironment.launchTemplate
            ? new LaunchTemplateConstruct(this, `${props.ec2ECSComputerEnvironment.name}LaunchTemplate`, {
                  launchTemplate: props.ec2ECSComputerEnvironment.launchTemplate,
                  vpc: props.vpc,
              })
            : undefined;

        this.computeEnvironment = new batch.ManagedEc2EcsComputeEnvironment(
            this,
            props.ec2ECSComputerEnvironment.name,
            {
                instanceTypes: [EC2Helpers.findInstanceType(props.ec2ECSComputerEnvironment.instanceType)],
                securityGroups: [sg.securityGroup],
                replaceComputeEnvironment: true,
                useOptimalInstanceClasses: false,
                vpc: props.vpc,
                vpcSubnets: props.vpcSubnets,
                // WARNING: Batch Jobs currently do not allow for updates of LaunchTemplate version
                launchTemplate: launchTemplate?.launchTemplate,
            }
        );
    }
}
