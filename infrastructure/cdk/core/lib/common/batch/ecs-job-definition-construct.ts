import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as batch from 'aws-cdk-lib/aws-batch';
import { ECSJobDefinition } from './config';
import { ECSEC2ContainerDefinitionConstruct } from './ecs-ec2-container-definition-construct';
import * as iam from 'aws-cdk-lib/aws-iam';
import { CfnOutput } from 'aws-cdk-lib';

interface ECSJobDefinitionProps {
    ecsJobDefinition: ECSJobDefinition;
    vpc: ec2.IVpc;
    vpcSubnets: ec2.SubnetSelection;
    jobRole?: iam.IRole;
    executionRole?: iam.IRole;
}

export class ECSJobDefinitionConstruct extends Construct {
    public readonly jobDefinition: batch.IJobDefinition;

    constructor(scope: Construct, id: string, props: ECSJobDefinitionProps) {
        super(scope, id);

        const containerDefinition = new ECSEC2ContainerDefinitionConstruct(
            this,
            `${props.ecsJobDefinition.name}ContainerDefinition`,
            {
                vpc: props.vpc,
                vpcSubnets: props.vpcSubnets,
                jobRole: props.jobRole,
                executionRole: props.executionRole,
                ecsEC2ContainerDefinition: props.ecsJobDefinition.ecsEC2ContainerDefinition,
            }
        );

        this.jobDefinition = new batch.EcsJobDefinition(this, props.ecsJobDefinition.name, {
            container: containerDefinition.containerDefinition,
            retryAttempts: props.ecsJobDefinition.retryAttempts,
            propagateTags: props.ecsJobDefinition.propagateTags,
        });

        new CfnOutput(this, `${props.ecsJobDefinition.name}Arn`, {
            exportName: props.ecsJobDefinition.name,
            value: this.jobDefinition.jobDefinitionArn,
        });
    }
}
