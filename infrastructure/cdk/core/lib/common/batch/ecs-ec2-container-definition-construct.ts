import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as batch from 'aws-cdk-lib/aws-batch';
import { ECSEC2ContainerDefinition } from './config';
import { ContainerImageConstruct } from '../ecs/container-image-construct';
import { Size } from 'aws-cdk-lib';
import { BatchHelpers } from '../utils/batch-helpers';

interface ECSEC2ContainerDefinitionProps {
    ecsEC2ContainerDefinition: ECSEC2ContainerDefinition;
    vpc: ec2.IVpc;
    vpcSubnets: ec2.SubnetSelection;
    jobRole?: iam.IRole;
    executionRole?: iam.IRole;
}

export class ECSEC2ContainerDefinitionConstruct extends Construct {
    public readonly containerDefinition: batch.IEcsEc2ContainerDefinition;

    constructor(scope: Construct, id: string, props: ECSEC2ContainerDefinitionProps) {
        super(scope, id);

        const imageAsset = new ContainerImageConstruct(this, props.ecsEC2ContainerDefinition.dockerImage.name, {
            dockerImageAsset: props.ecsEC2ContainerDefinition.dockerImage,
        });

        const volumes = props.ecsEC2ContainerDefinition.volumes
            ? props.ecsEC2ContainerDefinition.volumes.map((volume) => {
                  const securityGroup = ec2.SecurityGroup.fromSecurityGroupId(
                      this,
                      `${props.ecsEC2ContainerDefinition.name}SGLookup`,
                      volume.efsVolumeOptions.securityGroupId
                  );
                  const fileSystem = efs.FileSystem.fromFileSystemAttributes(
                      this,
                      `${props.ecsEC2ContainerDefinition.name}FileSystemLookup`,
                      {
                          securityGroup: securityGroup,
                          fileSystemId: volume.efsVolumeOptions.fileSystemId,
                      }
                  );
                  return BatchHelpers.convertToECSVolume(volume, fileSystem);
              })
            : undefined;

        this.containerDefinition = new batch.EcsEc2ContainerDefinition(
            this,
            `${props.ecsEC2ContainerDefinition.name}-Container`,
            {
                command: props.ecsEC2ContainerDefinition.commands,
                environment: props.ecsEC2ContainerDefinition.environment,
                privileged: props.ecsEC2ContainerDefinition.privileged,
                image: imageAsset.containerImage,
                cpu: props.ecsEC2ContainerDefinition.cpus,
                gpu: props.ecsEC2ContainerDefinition.gpus,
                memory: Size.gibibytes(props.ecsEC2ContainerDefinition.memoryLimitGB),
                jobRole: props.jobRole,
                executionRole: props.executionRole,
                volumes: volumes,
            }
        );
    }
}
