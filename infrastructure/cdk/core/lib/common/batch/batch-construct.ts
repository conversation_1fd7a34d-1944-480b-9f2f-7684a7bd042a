import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as batch from 'aws-cdk-lib/aws-batch';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import { Batch } from './config';
import { CfnOutput } from 'aws-cdk-lib';
import { TagHelpers } from '../utils/tag-helpers';
import { EC2EC2ComputeEnvironmentConstruct } from './ec2-ec2-compute-environment-construct';
import { ECSJobDefinitionConstruct } from './ecs-job-definition-construct';
import { EventBridgeRuleConstruct } from '../events/event-bridge-rule-construct';

interface BatchProps {
    batch: Batch;
    vpc: ec2.IVpc;
    vpcSubnets: ec2.SubnetSelection;
}

export class BatchConstruct extends Construct {
    public readonly jobDefinition: batch.IJobDefinition;
    public readonly jobQueue: batch.IJobQueue;

    constructor(scope: Construct, id: string, props: BatchProps) {
        super(scope, id);

        const batchJobRole = this.createBatchJobRole(props);

        const jobDefinition = new ECSJobDefinitionConstruct(this, `${props.batch.name}JobDefinition`, {
            ecsJobDefinition: props.batch.ecsJobDefinition,
            vpc: props.vpc,
            vpcSubnets: props.vpcSubnets,
            jobRole: batchJobRole,
            executionRole: batchJobRole,
        });

        const computeEnvironment = new EC2EC2ComputeEnvironmentConstruct(
            this,
            `${props.batch.name}ComputeEnvironment`,
            {
                ec2ECSComputerEnvironment: props.batch.ec2ECSComputeEnvironment,
                vpc: props.vpc,
                vpcSubnets: props.vpcSubnets,
            }
        );

        const jobQueueName = `${props.batch.name}JobQueue`;
        const jobQueue = new batch.JobQueue(this, jobQueueName, {
            priority: 1,
            computeEnvironments: [{ order: 1, computeEnvironment: computeEnvironment.computeEnvironment }],
        });
        new CfnOutput(this, `${jobQueueName}Arn`, {
            exportName: jobQueueName,
            value: jobQueue.jobQueueArn,
        });

        if (props.batch.tags) {
            TagHelpers.addTypedTagsToResources(
                props.batch.tags,
                computeEnvironment.computeEnvironment,
                jobDefinition,
                jobQueue
            );
        }

        if (props.batch.eventBridgeRules) {
            this.createEventBridgeRules(props, jobDefinition.jobDefinition, jobQueue);
        }

        this.jobDefinition = jobDefinition.jobDefinition;
        this.jobQueue = jobQueue;
    }

    // Avoid using this as the default service linked role has qualities that we cannot emulate.
    // https://docs.aws.amazon.com/batch/latest/userguide/using-service-linked-roles.html
    // When using our custom role, we are unable to update certain batch compute environment parameters.
    private createBatchServiceRole(props: BatchProps): iam.Role {
        const roleName = `${props.batch.name}Role`;
        const batchServiceRole = new iam.Role(this, roleName, {
            roleName: roleName,
            description: 'IAM role for batch compute service',
            assumedBy: new iam.ServicePrincipal('batch.amazonaws.com'),
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSBatchServiceRole')],
        });
        batchServiceRole.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['sts:AssumeRole'],
                resources: ['*'],
            })
        );
        return batchServiceRole;
    }

    private createBatchJobRole(props: BatchProps): iam.Role {
        const roleName = `${props.batch.name}JobRole`;
        const role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new iam.CompositePrincipal(
                new iam.ServicePrincipal('ec2.amazonaws.com'),
                new iam.ServicePrincipal('ecs.amazonaws.com'),
                new iam.ServicePrincipal('ecs-tasks.amazonaws.com')
            ),
            description: 'IAM role for batch instance execution',
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryReadOnly'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchLogsFullAccess'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore'),
            ],
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'secretsmanager:GetSecretValue',
                    'secretsmanager:DescribeSecret',
                    'secretsmanager:ListSecretVersionIds',
                ],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:ListBucket', 's3:*Object'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['dynamodb:*Item'],
                resources: [`arn:aws:dynamodb:*:*:table/*`],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['sagemaker:InvokeEndpoint', 'sagemaker:DescribeEndpoint', 'sagemaker:ListEndpoints'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['cloudwatch:PutMetricData'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['sts:AssumeRole'],
                resources: ['*'],
            })
        );

        return role;
    }

    private createEventBridgeRules(props: BatchProps, jobDefinition: batch.IJobDefinition, jobQueue: batch.IJobQueue) {
        const batchTarget = new targets.BatchJob(
            jobQueue.jobQueueArn,
            jobQueue,
            jobDefinition.jobDefinitionArn,
            jobDefinition
        );

        props.batch.eventBridgeRules?.forEach((eventBridgeRule) => {
            new EventBridgeRuleConstruct(this, `${props.batch.name}${eventBridgeRule.name}`, {
                eventBridgeRule: eventBridgeRule,
                ruleTargets: [batchTarget],
            });
        });
    }
}
