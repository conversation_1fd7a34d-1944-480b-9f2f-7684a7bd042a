import { Construct } from 'constructs';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { EFSFileSystem } from './config';
import { EFSHelpers } from '../utils/efs-helpers';
import { SecurityGroupConstruct } from '../ec2/security-group-construct';
import { EFSAccessPointConstruct } from './efs-access-point-construct';
import { CfnOutput } from 'aws-cdk-lib';

interface EFSFileSystemProps {
    efsFileSystem: EFSFileSystem;
    vpc: ec2.IVpc;
    vpcSubnets: ec2.SubnetSelection;
}

export class EFSFileSystemConstruct extends Construct {
    public readonly fileSystem: efs.IFileSystem;
    public readonly accessPoints: Array<efs.IAccessPoint>;

    constructor(scope: Construct, id: string, props: EFSFileSystemProps) {
        super(scope, id);

        const performanceMode = props.efsFileSystem.performanceMode
            ? EFSHelpers.convertToPerformanceMode(props.efsFileSystem.performanceMode)
            : undefined;

        const removalPolicy = props.efsFileSystem.removalPolicy
            ? EFSHelpers.convertToRemovalPolicy(props.efsFileSystem.removalPolicy)
            : undefined;

        const lifecyclePolicy = props.efsFileSystem.lifecyclePolicy
            ? EFSHelpers.convertToLifecyclePolicy(props.efsFileSystem.lifecyclePolicy)
            : undefined;

        const outOfInfrequentAccessPolicy = props.efsFileSystem.outOfInfrequentAccessPolicy
            ? EFSHelpers.convertToOutOfInfrequentAccessPolicy(props.efsFileSystem.outOfInfrequentAccessPolicy)
            : undefined;

        const throughputMode = props.efsFileSystem.throughputMode
            ? EFSHelpers.convertToThroughputMode(props.efsFileSystem.throughputMode)
            : undefined;

        const securityGroup = props.efsFileSystem.securityGroup
            ? new SecurityGroupConstruct(this, `${props.efsFileSystem.name}SecurityGroup`, {
                  securityGroup: props.efsFileSystem.securityGroup,
                  vpc: props.vpc,
              }).securityGroup
            : undefined;

        this.fileSystem = new efs.FileSystem(this, props.efsFileSystem.name, {
            vpc: props.vpc,
            vpcSubnets: props.vpcSubnets,
            encrypted: props.efsFileSystem.encrypted,
            fileSystemName: props.efsFileSystem.fileSystemName,
            performanceMode: performanceMode,
            removalPolicy: removalPolicy,
            lifecyclePolicy: lifecyclePolicy,
            outOfInfrequentAccessPolicy: outOfInfrequentAccessPolicy,
            throughputMode: throughputMode,
            securityGroup: securityGroup,
        });

        this.accessPoints = props.efsFileSystem.efsAccessPoints
            ? props.efsFileSystem.efsAccessPoints.map((efsAccessPoint) => {
                  return new EFSAccessPointConstruct(this, `${props.efsFileSystem.name}${efsAccessPoint.name}`, {
                      efsAccessPoint: efsAccessPoint,
                      vpc: props.vpc,
                      fileSystem: this.fileSystem,
                      vpcSubnets: props.vpcSubnets,
                  }).accessPoint;
              })
            : [];

        new CfnOutput(this, `${props.efsFileSystem.name}Id`, { value: this.fileSystem.fileSystemId });
    }
}
