import { Construct } from 'constructs';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { EFSAccessPoint } from './config';
import { EFSHelpers } from '../utils/efs-helpers';

interface EFSAccessPointProps {
    efsAccessPoint: EFSAccessPoint;
    fileSystem: efs.IFileSystem;
    vpc: ec2.IVpc;
    vpcSubnets: ec2.SubnetSelection;
}

export class EFSAccessPointConstruct extends Construct {
    public readonly accessPoint: efs.IAccessPoint;

    constructor(scope: Construct, id: string, props: EFSAccessPointProps) {
        super(scope, id);

        const accessPoint = new efs.AccessPoint(this, 'EfsAccessPoint', {
            fileSystem: props.fileSystem,
            path: props.efsAccessPoint.path,
            createAcl: EFSHelpers.convertToAcl(props.efsAccessPoint.acl),
            posixUser: EFSHelpers.convertToPosixUser(props.efsAccessPoint.posixUser),
        });

        this.accessPoint = accessPoint;
    }
}
