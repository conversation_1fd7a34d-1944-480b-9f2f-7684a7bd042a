import { JSONObject, optional, required } from 'ts-json-object';
import { SecurityGroup } from '../ec2/config';

export type PerformanceMode = 'GENERAL_PURPOSE' | 'MAX_IO';
export type ThroughputMode = 'bursting' | 'provisioned' | 'elastic';
export type RemovalPolicy = 'DESTROY' | 'RETAIN';
export type LifecyclePolicy =
    | 'AFTER_7_DAYS'
    | 'AFTER_14_DAYS'
    | 'AFTER_30_DAYS'
    | 'AFTER_60_DAYS'
    | 'AFTER_90_DAYS'
    | 'AFTER_120_DAYS'
    | 'AFTER_180_DAYS'
    | 'AFTER_365_DAYS'
    | 'AFTER_400_DAYS';
export type OutOfInfrequentAccessPolicy = 'AFTER_1_ACCESS';

export class Acl extends JSONObject {
    @required
    ownerGid: string;

    @required
    ownerUid: string;

    @required
    permissions: string;
}

export class PosixUser extends JSONObject {
    @required
    gid: string;

    @required
    uid: string;
}

export class EFSAccessPoint extends J<PERSON><PERSON>Object {
    @required
    name: string;

    @optional(undefined)
    path?: string;

    @required
    acl: Acl;

    @required
    posixUser: PosixUser;
}

export class EFSFileSystem extends JSONObject {
    @required
    name: string;
    @optional(undefined)
    fileSystemName?: string;
    @optional(undefined)
    performanceMode?: PerformanceMode;
    @optional(undefined)
    throughputMode?: ThroughputMode;
    @optional(undefined)
    encrypted?: boolean;
    @optional(undefined)
    lifecyclePolicy?: LifecyclePolicy;
    @optional(undefined)
    removalPolicy?: RemovalPolicy;
    @optional(undefined)
    outOfInfrequentAccessPolicy?: OutOfInfrequentAccessPolicy;
    @optional(undefined)
    securityGroup?: SecurityGroup;
    @optional(undefined)
    efsAccessPoints?: Array<EFSAccessPoint>;
}
