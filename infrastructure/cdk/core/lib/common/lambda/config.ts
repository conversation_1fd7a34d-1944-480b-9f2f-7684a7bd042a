import { JSONObject, optional, required } from 'ts-json-object';

export class LambdaConfig extends JSONObject {
    @optional(undefined)
    functionName: string;
    @optional(undefined)
    lambdaPath: string;
    @optional(undefined)
    containerPath: string;
    @optional('PYTHON_3_12')
    runtime: string;
    @required
    handler: string;
    @optional(1)
    timeoutInMin: number;
    @optional(undefined)
    /* eslint-disable @typescript-eslint/no-explicit-any */
    environment: any;
    @optional(undefined)
    keyPrefix: string[];
    @optional(undefined)
    keySuffix: string[];
}
