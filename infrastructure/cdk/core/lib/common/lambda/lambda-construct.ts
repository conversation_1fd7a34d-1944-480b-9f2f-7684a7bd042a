import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Bucket, EventType, IBucket, NotificationKeyFilter } from 'aws-cdk-lib/aws-s3';
import { S3EventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { VpcConfig } from 'aws-cdk-lib/aws-stepfunctions-tasks';
import * as s3n from 'aws-cdk-lib/aws-s3-notifications';

export interface LambdaConstructProps {
    name: string;
    lambdaPath?: string | undefined;
    runtime?: lambda.Runtime;
    policies?: (iam.PolicyStatement | iam.IManagedPolicy)[];
    lambdaRole?: iam.Role;
    handler?: string;
    /* eslint-disable @typescript-eslint/no-explicit-any */
    environment?: any;
    region: string;
    timeout?: cdk.Duration;
    bucket?: s3.Bucket | IBucket;
    layerArns?: string[];
    keyPrefix?: string[];
    keySuffix?: string[];
    containerPath?: string | undefined;
    vpcConfig?: VpcConfig;
}

export class LambdaConstruct extends Construct {
    public readonly lambdaFunction: lambda.Function;
    public readonly lambdaRole: iam.Role;

    constructor(scope: Construct, id: string, props: LambdaConstructProps) {
        super(scope, id);

        const lambdaName = `${props.name}-Lambda`;
        const roleName = `${props.name}-Lambda-Role-${props.region}`;

        // Create role from policy or use existing role
        if (props?.policies && props?.policies.length > 0) this.lambdaRole = this.createRole(roleName, props.policies);
        else if (props.lambdaRole) this.lambdaRole = props.lambdaRole;

        this.lambdaFunction = this.createLambda(lambdaName, this.lambdaRole, props);
    }

    private createLambda(lambdaName: string, lambdaRole: iam.Role, props: LambdaConstructProps): lambda.Function {
        const layers = this.loadLayers(lambdaName, props.layerArns);

        let lambdaFunction;
        if (props.containerPath) {
            lambdaFunction = new lambda.DockerImageFunction(this, lambdaName, {
                functionName: lambdaName,
                code: lambda.DockerImageCode.fromImageAsset(props.containerPath),
                timeout: props.timeout != undefined ? props.timeout : cdk.Duration.seconds(60 * 3),
                role: lambdaRole,
                environment: props.environment,
                layers: layers.length > 0 ? layers : undefined,
                vpc: props.vpcConfig?.vpc,
                vpcSubnets: props.vpcConfig?.subnets,
            });
        } else if (props.lambdaPath) {
            lambdaFunction = new lambda.Function(this, lambdaName, {
                functionName: lambdaName,
                code: lambda.Code.fromAsset(props.lambdaPath),
                handler: props.handler != undefined ? props.handler : 'handler.handle',
                runtime: props.runtime != undefined ? props.runtime : lambda.Runtime.PYTHON_3_12,
                timeout: props.timeout != undefined ? props.timeout : cdk.Duration.seconds(60 * 3),
                role: lambdaRole,
                environment: props.environment,
                layers: layers.length > 0 ? layers : undefined,
                vpc: props.vpcConfig?.vpc,
                vpcSubnets: props.vpcConfig?.subnets,
            });
        } else {
            throw new Error('Either a lambdaPath or containerPath must be provided.');
        }

        if (props.bucket) {
            if (this.isBucket(props.bucket)) {
                this.addEventSource(props, props.bucket, lambdaFunction);
            } else {
                this.addEventNotification(props, props.bucket, lambdaFunction);
            }
        }

        return lambdaFunction;
    }

    private isBucket(bucket: Bucket | IBucket): bucket is Bucket {
        return (bucket as Bucket).bucketName !== undefined;
    }

    private isPolicyStatement(policy: iam.PolicyStatement | iam.IManagedPolicy): policy is iam.PolicyStatement {
        return (policy as iam.PolicyStatement).actions !== undefined;
    }

    private addEventSource(props: LambdaConstructProps, bucket: Bucket, lambdaFunction: lambda.Function) {
        const keyPrefixes: NotificationKeyFilter[] =
            props.keyPrefix?.map((prefix) => {
                return {
                    prefix,
                };
            }) ?? [];

        const keySuffixes: NotificationKeyFilter[] =
            props.keySuffix?.map((suffix) => {
                return {
                    suffix,
                };
            }) ?? [];

        const filters = [...keyPrefixes, ...keySuffixes];
        lambdaFunction.addEventSource(
            new S3EventSource(bucket, {
                events: [
                    s3.EventType.OBJECT_CREATED_PUT,
                    s3.EventType.OBJECT_CREATED_COMPLETE_MULTIPART_UPLOAD,
                    s3.EventType.OBJECT_CREATED_COPY,
                ],
                filters: filters,
            })
        );
    }

    private addEventNotification(props: LambdaConstructProps, bucket: IBucket, lambdaFunction: lambda.Function) {
        const keyPrefixes: NotificationKeyFilter[] =
            props.keyPrefix?.map((prefix) => {
                return {
                    prefix,
                };
            }) ?? [];

        const keySuffixes: NotificationKeyFilter[] =
            props.keySuffix?.map((suffix) => {
                return {
                    suffix,
                };
            }) ?? [];

        bucket.addObjectCreatedNotification(new s3n.LambdaDestination(lambdaFunction), ...keySuffixes, ...keyPrefixes);

        bucket.addEventNotification(
            EventType.OBJECT_CREATED_COMPLETE_MULTIPART_UPLOAD,
            new s3n.LambdaDestination(lambdaFunction),
            ...keySuffixes,
            ...keyPrefixes
        );
    }

    private createRole(roleName: string, policies: (iam.IManagedPolicy | iam.PolicyStatement)[]): iam.Role {
        const role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
        });

        role.addManagedPolicy({ managedPolicyArn: 'arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole' });
        for (const policy of policies) {
            if (this.isPolicyStatement(policy)) {
                role.addToPolicy(policy);
            } else {
                role.addManagedPolicy(policy);
            }
        }

        return role;
    }

    private loadLayers(lambdaName: string, layerArns: string[] | undefined): ILayerVersion[] {
        const layers = [];

        if (layerArns != undefined && layerArns.length > 0) {
            let index = 0;
            for (const arn of layerArns) {
                index++;
                layers.push(lambda.LayerVersion.fromLayerVersionArn(this, `${lambdaName}-${index}-layer`, arn));
            }
        }

        return layers;
    }
}
