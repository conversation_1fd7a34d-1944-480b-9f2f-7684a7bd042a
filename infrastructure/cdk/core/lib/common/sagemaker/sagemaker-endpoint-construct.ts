import { Construct } from 'constructs';
import { AwsEnvAccount } from '../../build-config';
import { DockerHelpers } from '../utils/docker-helpers';
import {
    ContainerDefinition,
    ModelData,
    ContainerImage,
    Model,
    EndpointConfig,
    Endpoint,
    InstanceType,
} from '@aws-cdk/aws-sagemaker-alpha';
import { IRole } from 'aws-cdk-lib/aws-iam';
import { Bucket } from 'aws-cdk-lib/aws-s3';
import { Duration } from 'aws-cdk-lib';
import { DockerImageAsset } from '../docker/config';
import { SagemakerAutoScaleConfig } from './config';
import { S3Artifact } from '../s3/config';

export class SagemakerEndpointConstruct extends Construct {
    public endpointName: string;
    public endpoint: Endpoint;

    constructor(
        scope: Construct,
        id: string,
        props: {
            modelDockerImageAsset?: DockerImageAsset;
            modelContainerImage?: ContainerImage;
            modelEnvironment?: Record<string, string>;
            modelS3Artifact?: S3Artifact;
            modelAutoScale?: SagemakerAutoScaleConfig;
            awsEnvAccount: AwsEnvAccount;
            modelName: string;
            endpointConfigName: string;
            endpointName: string;
            executionRole: IRole;
            instanceType: string;
            containerStartupHealthCheckTimeoutInSeconds?: number;
        }
    ) {
        super(scope, id);

        if (props.modelDockerImageAsset == null && props.modelContainerImage == null) {
            throw new Error('Failed to provide a modelDockerImage or modelImageUri for endpoint');
        }

        let modelDockerImage: ContainerImage | undefined = undefined;
        if (props.modelDockerImageAsset) {
            if (props.modelDockerImageAsset.directory) {
                const resolvedBuildArgs = DockerHelpers.resolveBuildArguments(
                    scope,
                    props.modelDockerImageAsset.buildArgs,
                    props.modelDockerImageAsset.secretBuildArgs
                );

                modelDockerImage = ContainerImage.fromAsset(props.modelDockerImageAsset.directory, {
                    assetName: props.modelDockerImageAsset.name,
                    platform: DockerHelpers.findDockerImagePlatform(props.modelDockerImageAsset.platform),
                    buildArgs: resolvedBuildArgs,
                });
            } else {
                throw new Error('Model docker image for sagemaker requires a directory to be provided.');
            }
        }

        let modelData: ModelData | undefined = undefined;
        if (props.modelS3Artifact) {
            const bucket = Bucket.fromBucketName(this, 'sgm_model_bucket', props.modelS3Artifact.bucketName);
            modelData = ModelData.fromBucket(bucket, props.modelS3Artifact.artifactPath);
        }

        const modelImage = props.modelContainerImage ?? modelDockerImage;
        if (!modelImage) {
            throw new Error('There is no valid image provided to generate a sagemaker container from.');
        }

        const containerDefinition: ContainerDefinition = {
            image: modelImage,
            environment: props.modelEnvironment,
            modelData: modelData,
        };

        // Do not add an explicit modelName here.
        // Models with explicit resource names must be manually deleted if there are changes.
        // AWS CDK is unable to delete a resource that is explicitly named.
        const model = new Model(this, props.modelName, {
            role: props.executionRole,
            containers: [containerDefinition],
        });

        const endpointConfig = new EndpointConfig(this, 'sgm_endpoint_config', {
            instanceProductionVariants: [
                {
                    model: model,
                    // Do NOT use model.modelName as it will result in AutoScaling failures because
                    // the value is not static.
                    // https://docs.aws.amazon.com/sagemaker/latest/dg/endpoint-scaling.html
                    variantName: props.modelName,
                    initialVariantWeight: 1.0,
                    instanceType: InstanceType.of(props.instanceType),
                    initialInstanceCount: props.modelAutoScale?.minCapacity ?? 1,
                },
            ],
        });

        const endpoint = new Endpoint(this, 'sgm_endpoint', {
            endpointName: props.endpointName,
            endpointConfig: endpointConfig,
        });

        if (props.modelAutoScale) {
            // https://pypi.org/project/aws-cdk.aws-sagemaker-alpha/
            const modelVariant = endpoint.findInstanceProductionVariant(props.modelName);

            const instanceCount = modelVariant.autoScaleInstanceCount({
                minCapacity: props.modelAutoScale.minCapacity,
                maxCapacity: props.modelAutoScale.maxCapacity,
            });

            // https://docs.aws.amazon.com/sagemaker/latest/dg/endpoint-scaling-loadtest.html
            instanceCount.scaleOnInvocations('sgm_limit_rps', {
                maxRequestsPerSecond: props.modelAutoScale.maxRequestsPerSecond,
                scaleInCooldown: Duration.seconds(props.modelAutoScale.scaleInCoolDownSeconds),
                scaleOutCooldown: Duration.seconds(props.modelAutoScale.scaleOutCoolDownSeconds),
                safetyFactor: props.modelAutoScale.safetyFactor,
            });
        }

        this.endpoint = endpoint;
        this.endpointName = endpoint.endpointName;
    }
}
