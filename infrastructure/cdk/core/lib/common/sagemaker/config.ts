import { JSONObject, optional } from 'ts-json-object';

export class SagemakerAutoScaleConfig extends JSONObject {
    @optional(1)
    minCapacity: number;
    @optional(1)
    maxCapacity: number;
    @optional(30)
    maxRequestsPerSecond: number;
    @optional(300)
    scaleInCoolDownSeconds: number;
    @optional(300)
    scaleOutCoolDownSeconds: number;
    @optional(0.5)
    safetyFactor: number;
}
