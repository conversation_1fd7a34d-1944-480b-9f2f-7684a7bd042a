import { Construct } from 'constructs';
import { ApiGatewayEndpoint } from '../api-gateway/config';
import { AwsEnvAccount } from '../../build-config';
import { LambdaIntegration, RestApi } from 'aws-cdk-lib/aws-apigateway';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as cdk from 'aws-cdk-lib';
import { LambdaConfig } from '../lambda/config';
import { LambdaConstruct } from '../lambda/lambda-construct';
import { LambdaHelpers } from '../utils/lambda-helpers';
import { ApiGatewayEndpointConstruct } from '../api-gateway/api-gateway-endpoint-construct';
import { Endpoint } from '@aws-cdk/aws-sagemaker-alpha';

export class SagemakerApiEndpointLambdaConstruct extends Construct {
    readonly lambda: LambdaConstruct;

    constructor(
        scope: Construct,
        id: string,
        props: {
            awsEnvAccount: AwsEnvAccount;
            apiGateway: RestApi;
            endpoint: Endpoint;
            apiGatewayEndpoint: ApiGatewayEndpoint;
            lambdaConfig: LambdaConfig;
        }
    ) {
        super(scope, id);

        const apiEndpoint = new ApiGatewayEndpointConstruct(this, props.apiGatewayEndpoint.name, {
            apiGateway: props.apiGateway,
            apiGatewayEndpoint: props.apiGatewayEndpoint,
        });

        this.lambda = new LambdaConstruct(this, props.lambdaConfig.functionName, {
            name: props.lambdaConfig.functionName,
            runtime: LambdaHelpers.findLambdaRuntime(props.lambdaConfig.runtime),
            lambdaPath: props.lambdaConfig.lambdaPath,
            handler: props.lambdaConfig.handler,
            environment: {
                ENDPOINT_NAME: props.endpoint.endpointName,
                ...props.lambdaConfig.environment,
            },
            region: props.awsEnvAccount.targetRegion,
            policies: [
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: ['sagemaker:Invoke*'],
                    resources: ['*'],
                }),
            ],
            timeout: cdk.Duration.minutes(props.lambdaConfig.timeoutInMin),
        });

        apiEndpoint.apiResource.addMethod(
            'POST',
            new LambdaIntegration(this.lambda.lambdaFunction, {
                // https://medium.com/@lakshmanLD/lambda-proxy-vs-lambda-integration-in-aws-api-gateway-3a9397af0e6d
                // Proxy integration passes request contents into body field of lambda event
                // Response is in body field of response
                proxy: true,
            })
        );
    }
}
