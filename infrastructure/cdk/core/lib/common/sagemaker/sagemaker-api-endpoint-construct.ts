import { Construct } from 'constructs';
import { ApiGatewayEndpoint } from '../api-gateway/config';
import { AwsIntegration, RestApi } from 'aws-cdk-lib/aws-apigateway';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as cdk from 'aws-cdk-lib';
import { ApiGatewayEndpointConstruct } from '../api-gateway/api-gateway-endpoint-construct';
import { Endpoint } from '@aws-cdk/aws-sagemaker-alpha';

export class SagemakerApiEndpointConstruct extends Construct {
    constructor(
        scope: Construct,
        id: string,
        props: {
            apiGateway: RestApi;
            endpoint: Endpoint;
            apiGatewayEndpoint: ApiGatewayEndpoint;
        }
    ) {
        super(scope, id);

        // Create IAM Role to invoke sagemaker endpoint
        const credentialsRole = new iam.Role(this, `${props.apiGatewayEndpoint.name}-role`, {
            assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
        });

        credentialsRole.attachInlinePolicy(
            new iam.Policy(this, `${props.apiGatewayEndpoint.name}-policy`, {
                statements: [
                    new iam.PolicyStatement({
                        actions: ['sagemaker:Invoke*'],
                        resources: ['*'],
                    }),
                ],
            })
        );

        const apiEndpoint = new ApiGatewayEndpointConstruct(this, props.apiGatewayEndpoint.name, {
            apiGateway: props.apiGateway,
            apiGatewayEndpoint: props.apiGatewayEndpoint,
        });

        apiEndpoint.apiResource.addMethod(
            'POST',
            new AwsIntegration({
                service: 'runtime.sagemaker',
                path: `endpoints/${props.endpoint.endpointName}/invocations`,
                options: {
                    credentialsRole: credentialsRole,
                    integrationResponses: [
                        {
                            statusCode: '200',
                        },
                        { statusCode: '400', selectionPattern: '4\\d{2}' },
                        { statusCode: '500', selectionPattern: '5\\d{2}' },
                    ],
                },
            }),
            {
                methodResponses: [{ statusCode: '200' }, { statusCode: '400' }, { statusCode: '500' }],
            }
        );

        new cdk.CfnOutput(this, `${props.endpoint.endpointName}-Url`, {
            value: props.apiGateway.urlForPath(apiEndpoint.apiResource.path),
        });
    }
}
