import { Construct } from 'constructs';
import { Platform } from 'aws-cdk-lib/aws-ecr-assets';
import { DockerImage } from 'aws-cdk-lib/aws-stepfunctions-tasks';
import { DockerHelpers } from '../utils/docker-helpers';
import { EnvironmentSecret } from '../env/config';

export interface DockerImageConstructProps {
    name: string;
    image?: string;
    directory?: string;
    platform?: Platform;
    buildArgs?: Record<string, string>;
    secretBuildArgs?: Array<EnvironmentSecret>;
}

export class DockerImageConstruct extends Construct {
    public readonly dockerImage: DockerImage;

    constructor(scope: Construct, id: string, props: DockerImageConstructProps) {
        super(scope, id);
        if (props.directory) {
            const resolvedBuildArgs = DockerHelpers.resolveBuildArguments(
                scope,
                props.buildArgs,
                props.secretBuildArgs
            );

            this.dockerImage = DockerImage.fromAsset(this, props.name, {
                directory: props.directory,
                platform: props.platform,
                buildArgs: resolvedBuildArgs,
            });
        } else if (props.image) {
            this.dockerImage = DockerImage.fromRegistry(props.image);
        } else {
            throw new Error('Need to provide either an image or directory to load docker image asset from');
        }
    }
}
