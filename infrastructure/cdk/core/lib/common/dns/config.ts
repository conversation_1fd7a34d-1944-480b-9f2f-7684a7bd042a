import { array, JSONObject, optional, required } from 'ts-json-object';

export class <PERSON><PERSON>ameRecord extends JSON<PERSON>bject {
    @required
    domainName: string;
    @required
    recordName: string;
    @optional('')
    comment: string;
    @optional(1)
    ttlMinutes: number;
}

export class <PERSON><PERSON>ord extends JSON<PERSON>bject {
    @required
    recordName: string;
    @required
    target: string;
    @optional('')
    comment: string;
    @optional(1)
    ttlMinutes: number;
}

export class Dns extends J<PERSON><PERSON>Object {
    @required
    route53HostedZoneID: string;
    @required
    route53HostedZoneName: string;

    @optional([])
    @array(ARecord)
    aRecords: Array<ARecord>;

    @optional([])
    @array(CNameRecord)
    cnameRecords: Array<CNameRecord>;
}
