import { JSONObject, required, optional } from 'ts-json-object';

export class CertRequest extends JSONObject {
    @required
    name: string;

    @required
    domainName: string;

    @optional([])
    subjectAlternativeNames: string[];

    @optional(false)
    addRegionWildcardAsSan: boolean;

    @optional('')
    hostedZoneIDOverride: string;

    @optional('')
    hostedZoneNameOverride: string;

    @optional(false)
    useEmailValidation: boolean;
}
