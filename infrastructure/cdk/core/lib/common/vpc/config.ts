import { array, JSONObject, optional, required } from 'ts-json-object';

export class PeeringTargetVPC extends JSONObject {
    @required
    id: string;
    @required
    name: string;
    @required
    networkCIDR: string;
    @optional
    accountID: string;
    @optional
    region: string;
    @optional('')
    peeringRole: string;
    @optional(true)
    allowICMPinDefaultSG: boolean;
    @optional(false)
    targetIsPeeringConn: boolean;
}

export class VPCConfig extends J<PERSON>NObject {
    @required
    networkCIDR: string;
    @required
    maxAZs: number;
    @optional(0)
    numNATGateways: number;
    @optional(true)
    allowICMPinDefaultSG: boolean;
    @optional([])
    @array(PeeringTargetVPC)
    peeringTarget: Array<PeeringTargetVPC>;
    @optional(true)
    enableS3GatewayEndpoint: boolean;
    @optional(true)
    enableDynamodbEndpoint: boolean;
    @optional(true)
    enableEcrInterfaceEndpoint: boolean;
    @optional(true)
    enableEfsInterfaceEndpoint: boolean;
    @optional(undefined)
    transitGatewayId: string;
    @optional([])
    transitGatewayTargetCIDRs: string[];
    @optional(true)
    enableVpcFlowLogs: boolean;
    @optional([])
    defaultSGMongoAtlasAllowedCIDRs: string[];
}
