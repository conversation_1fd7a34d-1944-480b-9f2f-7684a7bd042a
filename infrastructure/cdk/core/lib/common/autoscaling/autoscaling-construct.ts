import { CfnAutoScalingGroup } from 'aws-cdk-lib/aws-autoscaling/';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as autoscaling from 'aws-cdk-lib/aws-autoscaling';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { AutoScalingGroup } from './config';
import { EC2Helpers } from '../utils/ec2-helpers';
import { LaunchTemplateConstruct } from '../ec2/launch-template-construct';
import { Duration, Stack } from 'aws-cdk-lib';
import { ECSHelpers } from '../utils/ecs-helpers';
import { AutoScalingHelpers } from '../utils/autoscaling-helpers';
import { CloudWatchHelpers } from '../utils/cloudwatch-helpers';

export interface AutosScalingConstructProps {
    vpc: ec2.IVpc;
    autoScalingGroup: AutoScalingGroup;
    vpcSubnets?: ec2.SubnetSelection;
    instanceMaintenancePolicyOverride?: CfnAutoScalingGroup.InstanceMaintenancePolicyProperty;
}

export class AutoScalingConstruct extends Construct {
    readonly autoScalingGroup: autoscaling.AutoScalingGroup;
    readonly region: string;
    readonly props: AutosScalingConstructProps;

    constructor(scope: Construct, id: string, props: AutosScalingConstructProps) {
        super(scope, id);

        this.props = props;
        const autoScalingGroup = props.autoScalingGroup.mixedInstancesPolicy
            ? this.createMixedAutoScalingGroup(props)
            : this.createFixedAutoScalingGroup(props);

        this.addInstanceRolePolicies(props, autoScalingGroup);
        this.addScalingTargets(props, autoScalingGroup);
        this.createDrataAlarms(props);

        this.autoScalingGroup = autoScalingGroup;
        this.region = Stack.of(this).region;
    }

    private createFixedAutoScalingGroup(props: AutosScalingConstructProps): autoscaling.AutoScalingGroup {
        if (props.autoScalingGroup.mixedInstancesPolicy) {
            throw new Error('MixedInstancesPolicy cannot be defined');
        }

        const launchTemplate = props.autoScalingGroup.launchTemplate
            ? new LaunchTemplateConstruct(this, props.autoScalingGroup.launchTemplate.name, {
                  launchTemplate: props.autoScalingGroup.launchTemplate,
                  vpc: props.vpc,
                  role: this.createLaunchTemplateRole(props),
              })
            : undefined;

        const machineImage = launchTemplate
            ? undefined
            : ECSHelpers.convertToMachineImage(props.autoScalingGroup.machineAmiType);

        const instanceType = launchTemplate
            ? undefined
            : EC2Helpers.findInstanceType(props.autoScalingGroup.instanceType);

        const healthChecks = props.autoScalingGroup.healthChecks
            ? AutoScalingHelpers.convertToEC2HealthCheck(props.autoScalingGroup.healthChecks)
            : undefined;

        const asg = new autoscaling.AutoScalingGroup(this, props.autoScalingGroup.name, {
            autoScalingGroupName: props.autoScalingGroup.name,
            vpc: props.vpc,
            vpcSubnets: props.vpcSubnets,
            machineImage: machineImage,
            instanceType: instanceType,
            spotPrice: props.autoScalingGroup.spotPrice?.toString(),
            desiredCapacity: props.autoScalingGroup.desiredCapacity,
            minCapacity: props.autoScalingGroup.minCapacity,
            maxCapacity: props.autoScalingGroup.maxCapacity,
            capacityRebalance: props.autoScalingGroup.capacityRebalance,
            keyName: props.autoScalingGroup.keyName,
            associatePublicIpAddress: props.autoScalingGroup.associatePublicIpAddress,
            launchTemplate: launchTemplate?.launchTemplate,
            healthChecks: healthChecks,
        });

        this.createInstanceRefresher(asg, props.autoScalingGroup.name);
        return asg;
    }

    private createMixedAutoScalingGroup(props: AutosScalingConstructProps): autoscaling.AutoScalingGroup {
        if (!props.autoScalingGroup.mixedInstancesPolicy) {
            throw new Error('MixedInstancesPolicy must be defined');
        }

        const mixedInstancesPolicy = props.autoScalingGroup.mixedInstancesPolicy;
        const launchTemplate = new LaunchTemplateConstruct(this, `${props.autoScalingGroup.name}LaunchTemplate`, {
            launchTemplate: mixedInstancesPolicy.launchTemplate,
            vpc: props.vpc,
            role: this.createLaunchTemplateRole(props),
        });

        const spotAllocationStrategy = mixedInstancesPolicy.instancesDistribution.spotAllocationStrategy
            ? AutoScalingHelpers.convertToSpotAllocationStrategy(
                  mixedInstancesPolicy.instancesDistribution.spotAllocationStrategy
              )
            : undefined;

        const onDemandAllocationStrategy = mixedInstancesPolicy.instancesDistribution.onDemandAllocationStrategy
            ? AutoScalingHelpers.convertToOnDemandAllocationStrategy(
                  mixedInstancesPolicy.instancesDistribution.onDemandAllocationStrategy
              )
            : undefined;

        const launchTemplateOverrides = mixedInstancesPolicy.launchTemplateOverrides
            ? AutoScalingHelpers.convertToLaunchTemplateOverrides(mixedInstancesPolicy.launchTemplateOverrides)
            : undefined;

        const healthChecks = props.autoScalingGroup.healthChecks
            ? AutoScalingHelpers.convertToEC2HealthCheck(props.autoScalingGroup.healthChecks)
            : undefined;

        const asg = new autoscaling.AutoScalingGroup(this, props.autoScalingGroup.name, {
            autoScalingGroupName: props.autoScalingGroup.name,
            vpc: props.vpc,
            vpcSubnets: props.vpcSubnets,
            desiredCapacity: props.autoScalingGroup.desiredCapacity,
            minCapacity: props.autoScalingGroup.minCapacity,
            maxCapacity: props.autoScalingGroup.maxCapacity,
            capacityRebalance: props.autoScalingGroup.capacityRebalance,
            associatePublicIpAddress: props.autoScalingGroup.associatePublicIpAddress,
            mixedInstancesPolicy: {
                instancesDistribution: {
                    onDemandPercentageAboveBaseCapacity:
                        mixedInstancesPolicy.instancesDistribution.onDemandPercentageAboveBaseCapacity,
                    spotMaxPrice: mixedInstancesPolicy.instancesDistribution.spotMaxPrice?.toString(),
                    spotInstancePools: mixedInstancesPolicy.instancesDistribution.spotInstancePools,
                    spotAllocationStrategy: spotAllocationStrategy,
                    onDemandAllocationStrategy: onDemandAllocationStrategy,
                    onDemandBaseCapacity: mixedInstancesPolicy.instancesDistribution.onDemandBaseCapacity,
                },
                launchTemplate: launchTemplate.launchTemplate,
                launchTemplateOverrides: launchTemplateOverrides,
            },
            healthChecks: healthChecks,
        });
        this.createInstanceRefresher(asg, props.autoScalingGroup.name);
        return asg;
    }

    private createLaunchTemplateRole(props: AutosScalingConstructProps): iam.IRole {
        const roleName = `${props.autoScalingGroup.name}LaunchTemplateRole`;
        const role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new iam.CompositePrincipal(
                new iam.ServicePrincipal('ec2.amazonaws.com'),
                new iam.ServicePrincipal('ecs.amazonaws.com'),
                new iam.ServicePrincipal('ecs-tasks.amazonaws.com')
            ),
            description: 'IAM role for auto scaling execution',
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryReadOnly'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchAgentAdminPolicy'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchAgentServerPolicy'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchLogsFullAccess'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore'),
            ],
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'cloudwatch:PutMetricData',
                    'ec2:DescribeVolumes',
                    'ec2:DescribeTags',
                    'logs:PutLogEvents',
                    'logs:DescribeLogStreams',
                    'logs:DescribeLogGroups',
                    'logs:CreateLogStream',
                    'logs:CreateLogGroup',
                ],
                resources: ['*'],
            })
        );

        return role;
    }

    private addInstanceRolePolicies(props: AutosScalingConstructProps, autoScalingGroup: autoscaling.AutoScalingGroup) {
        // Add managed policy to allow ssh through ssm manager
        autoScalingGroup.role.addManagedPolicy(
            iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore')
        );
    }

    private addScalingTargets(props: AutosScalingConstructProps, autoScalingGroup: autoscaling.AutoScalingGroup) {
        if (props.autoScalingGroup.stepMetricScales) {
            props.autoScalingGroup.stepMetricScales.forEach((stepMetricScale) => {
                const cloudWatchMetric = CloudWatchHelpers.convertToCloudWatchMetric(stepMetricScale.cloudWatchMetric);
                const scalingIntervals = AutoScalingHelpers.convertToScalingIntervals(stepMetricScale.scalingSteps);
                const adjustmentType = stepMetricScale.adjustmentType
                    ? AutoScalingHelpers.convertToAdjustmentType(stepMetricScale.adjustmentType)
                    : undefined;
                autoScalingGroup.scaleOnMetric(`${props.autoScalingGroup.name}${stepMetricScale.name}`, {
                    metric: cloudWatchMetric,
                    scalingSteps: scalingIntervals,
                    datapointsToAlarm: stepMetricScale.datapointsToAlarm,
                    evaluationPeriods: stepMetricScale.evaluationPeriods,
                    adjustmentType: adjustmentType,
                    cooldown: stepMetricScale.cooldownInSeconds
                        ? Duration.seconds(stepMetricScale.cooldownInSeconds)
                        : undefined,
                });
            });
        }

        if (props.autoScalingGroup.scheduleScales) {
            props.autoScalingGroup.scheduleScales.forEach((scheduleScale) => {
                const schedule = AutoScalingHelpers.convertToSchedule(scheduleScale.schedule);
                autoScalingGroup.scaleOnSchedule(`${props.autoScalingGroup.name}${scheduleScale.name}`, {
                    schedule: schedule,
                    minCapacity: scheduleScale.minCapacity,
                    maxCapacity: scheduleScale.maxCapacity,
                    desiredCapacity: scheduleScale.desiredCapacity,
                    timeZone: scheduleScale.timeZone,
                });
            });
        }

        if (props.autoScalingGroup.cpuScale) {
            autoScalingGroup.scaleOnCpuUtilization(`${props.autoScalingGroup.name}CPUScaling`, {
                targetUtilizationPercent: props.autoScalingGroup.cpuScale.targetUtilizationPercent,
                cooldown: props.autoScalingGroup.cpuScale.cooldownInSeconds
                    ? Duration.seconds(props.autoScalingGroup.cpuScale.cooldownInSeconds)
                    : undefined,
                estimatedInstanceWarmup: props.autoScalingGroup.cpuScale.estimatedInstanceWarmupInSeconds
                    ? Duration.seconds(props.autoScalingGroup.cpuScale.estimatedInstanceWarmupInSeconds)
                    : undefined,
            });
        }

        if (props.autoScalingGroup.trackMetricScales) {
            props.autoScalingGroup.trackMetricScales.forEach((trackMetricScale) => {
                const cloudWatchMetric = CloudWatchHelpers.convertToCloudWatchMetric(trackMetricScale.cloudWatchMetric);

                autoScalingGroup.scaleToTrackMetric(`${props.autoScalingGroup.name}${trackMetricScale.name}`, {
                    metric: cloudWatchMetric,
                    targetValue: trackMetricScale.targetValue,
                    cooldown: trackMetricScale.cooldownInSeconds
                        ? Duration.seconds(trackMetricScale.cooldownInSeconds)
                        : undefined,
                });
            });
        }
    }

    // These targets must only be applied after associating the load balancer with an auto scaler.
    public addLoadBalancedScalingTargets() {
        if (this.props.autoScalingGroup.requestScale) {
            this.autoScalingGroup.scaleOnRequestCount(`${this.props.autoScalingGroup.name}RequestScaling`, {
                targetRequestsPerMinute: this.props.autoScalingGroup.requestScale.targetRequestsPerMinute,
                cooldown: this.props.autoScalingGroup.requestScale.cooldownInSeconds
                    ? Duration.seconds(this.props.autoScalingGroup.requestScale.cooldownInSeconds)
                    : undefined,
            });
        }
    }

    private createInstanceRefresher(asg: autoscaling.AutoScalingGroup, asgName: string) {
        const cfnAsg = asg.node.defaultChild as autoscaling.CfnAutoScalingGroup;
        cfnAsg.instanceMaintenancePolicy = this.props.instanceMaintenancePolicyOverride ?? {
            minHealthyPercentage: 100,
            maxHealthyPercentage: 200,
        };

        const refreshLambda = new lambda.Function(this, `AsgRefreshLambda-${asgName}`, {
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: 'index.handler',
            code: lambda.Code.fromInline(`
                const AWS = require('aws-sdk');
                const autoscaling = new AWS.AutoScaling();

                exports.handler = async () => {
                  await autoscaling.startInstanceRefresh({
                    AutoScalingGroupName: '${asg.autoScalingGroupName}',
                    Preferences: {
                      MinHealthyPercentage: 100,
                      InstanceWarmup: 300
                    }
                  }).promise();
                };
              `),
            timeout: Duration.seconds(30),
            initialPolicy: [
                new iam.PolicyStatement({
                    actions: ['autoscaling:StartInstanceRefresh'],
                    resources: ['*'], // consider scoping to your ASG ARN
                }),
            ],
        });

        new events.Rule(this, `MonthlyAsgRefreshRule-${asgName}`, {
            schedule: events.Schedule.expression('cron(0 0 4 * ? *)'),
            targets: [new targets.LambdaFunction(refreshLambda)],
        });
    }

    private createDrataAlarms(props: AutosScalingConstructProps) {
        if (!props.autoScalingGroup.notificationEmail) {
            return;
        }
        const snsTopic = new sns.Topic(this, `${props.autoScalingGroup.name}-ASG-Alarms`, {
            displayName: `Topic to deliver alarms related to ${props.autoScalingGroup.name} ASG`,
        });

        snsTopic.addSubscription(new subscriptions.EmailSubscription(props.autoScalingGroup.notificationEmail));

        const alarm = new cloudwatch.Alarm(this, `${props.autoScalingGroup.name}-CPUUtilization`, {
            alarmName: `${props.autoScalingGroup.name}AsgCPUUtilization`,
            alarmDescription: 'ASG host server high CPU',
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            threshold: 80,
            evaluationPeriods: 2,
            metric: new cloudwatch.Metric({
                metricName: 'CPUUtilization',
                namespace: 'AWS/EC2',
                dimensionsMap: {
                    AutoScalingGroupName: props.autoScalingGroup.name,
                },
            }),
            actionsEnabled: true,
        });
        alarm.addAlarmAction(new cw_actions.SnsAction(snsTopic));
    }
}
