import { array, JSONObject, optional, required } from 'ts-json-object';
import { CloudWatchMetric } from '../cloudwatch/config';
import { LaunchTemplate } from '../ec2/config';

export type AmiHardwareType = 'Standard' | 'GPU' | 'ARM64' | 'Neuron';
export type OnDemandAllocationStrategy = 'prioritized' | 'lowest-price';
export type SpotAllocationStrategy =
    | 'capacity-optimized'
    | 'capacity-optimized-prioritized'
    | 'lowest-price'
    | 'price-capacity-optimized';
export type AdjustmentType = 'ChangeInCapacity' | 'PercentChangeInCapacity' | 'ExactCapacity';

export class CronOptions extends JSONObject {
    @optional(undefined)
    minute?: string;
    @optional(undefined)
    hour?: string;
    @optional(undefined)
    day?: string;
    @optional(undefined)
    month?: string;
    @optional(undefined)
    weekDay?: string;
}

export class Schedule extends JSONObject {
    @optional(undefined)
    cronExpression?: string;
    @optional(undefined)
    cronOptions?: CronOptions;
}

export class HealthChecks extends JSONObject {
    @required
    graceInMinutes: number;
}

export class InstancesDistribution extends J<PERSON>NObject {
    @required
    onDemandPercentageAboveBaseCapacity: number;
    @optional(undefined)
    spotMaxPrice?: number;
    @optional(undefined)
    spotInstancePools?: number;
    @optional(undefined)
    spotAllocationStrategy?: SpotAllocationStrategy;
    @optional(undefined)
    onDemandAllocationStrategy?: OnDemandAllocationStrategy;
    @optional(undefined)
    onDemandBaseCapacity?: number;
}

export class LaunchTemplateOverride extends JSONObject {
    @required
    instanceType: string;
}

export class MixedInstancesPolicy extends JSONObject {
    @required
    launchTemplate: LaunchTemplate;
    @required
    instancesDistribution: InstancesDistribution;
    @optional(undefined)
    launchTemplateOverrides?: Array<LaunchTemplateOverride>;
}

export class ScalingInterval extends JSONObject {
    @optional
    lower?: number;
    @optional
    upper?: number;
    @required
    change: number;
}

export class AutoScalingSchedule extends JSONObject {
    @required
    name: string;
    @required
    schedule: Schedule;
    @optional(undefined)
    desiredCapacity?: number;
    @optional(undefined)
    minCapacity?: number;
    @optional(undefined)
    maxCapacity?: number;
    @optional('America/Vancouver')
    timeZone: string;
}

export class BaseAutoScaling extends JSONObject {
    @optional(undefined)
    cooldownInSeconds: number;
    @optional(undefined)
    estimatedInstanceWarmupInSeconds?: number;
}

export class AutoSScalingStepMetric extends BaseAutoScaling {
    @required
    name: string;
    @required
    cloudWatchMetric: CloudWatchMetric;
    @required
    @array(ScalingInterval)
    scalingSteps: Array<ScalingInterval>;
    @optional(undefined)
    datapointsToAlarm?: number;
    @optional(undefined)
    evaluationPeriods?: number;
    @optional(undefined)
    adjustmentType: AdjustmentType;
}

export class AutoScalingCPU extends BaseAutoScaling {
    @required
    name: string;
    @required
    targetUtilizationPercent: number;
}

export class AutoScalingRequest extends BaseAutoScaling {
    @required
    name: string;
    @required
    targetRequestsPerMinute: number;
}

export class AutoScalingTrackMetric extends BaseAutoScaling {
    @required
    name: string;
    @required
    cloudWatchMetric: CloudWatchMetric;
    @required
    targetValue: number;
}

export class AutoScalingGroup extends JSONObject {
    @required
    name: string;
    @required
    machineAmiType: AmiHardwareType;
    @required
    instanceType: string;
    @optional(undefined)
    spotPrice?: number;
    @optional(undefined)
    desiredCapacity?: number;
    @optional(undefined)
    minCapacity?: number;
    @optional(undefined)
    maxCapacity?: number;
    @optional(undefined)
    launchTemplate?: LaunchTemplate;
    @optional(undefined)
    capacityRebalance?: boolean;
    @optional(undefined)
    associatePublicIpAddress?: boolean;
    @optional(undefined)
    keyName?: string;
    @optional(undefined)
    cpuScale?: AutoScalingCPU;
    @optional(undefined)
    requestScale?: AutoScalingRequest;
    @optional(undefined)
    @array(AutoScalingTrackMetric)
    trackMetricScales?: Array<AutoScalingTrackMetric>;
    @optional(undefined)
    @array(AutoSScalingStepMetric)
    stepMetricScales?: Array<AutoSScalingStepMetric>;
    @optional(undefined)
    @array(AutoScalingSchedule)
    scheduleScales?: Array<AutoScalingSchedule>;
    @optional(undefined)
    mixedInstancesPolicy?: MixedInstancesPolicy;
    @optional(undefined)
    healthChecks?: HealthChecks;
    @optional('<EMAIL>')
    notificationEmail?: string;
}
