import { Construct } from 'constructs';
import { AwsEnvAccount } from '../../build-config';
import { SagemakerEndpointConstruct } from '../sagemaker/sagemaker-endpoint-construct';
import { IRole } from 'aws-cdk-lib/aws-iam';
import { ContainerImage, Endpoint } from '@aws-cdk/aws-sagemaker-alpha';
import { MachinelearningHelpers } from '../utils/machinelearning-helpers';
import { HuggingfaceHelpers } from '../utils/huggingface-helpers';
import { SecretHelpers } from '../utils/secret-helpers';
import { DockerEcrRepository, DockerImageAsset } from '../docker/config';
import { EnvironmentSecret } from '../env/config';
import { SagemakerAutoScaleConfig } from '../sagemaker/config';
import { S3Artifact } from '../s3/config';

export class MachineLearningSagemakerEndpointConstruct extends Construct {
    public endpointName: string;
    public endpoint: Endpoint;

    constructor(
        scope: Construct,
        id: string,
        props: {
            modelS3Artifact?: S3Artifact;
            modelDockerImageAsset?: DockerImageAsset;
            modelDeepLearningContainer?: DockerEcrRepository;
            modelEnvironment?: Record<string, string>;
            modelToken?: EnvironmentSecret;
            awsEnvAccount: AwsEnvAccount;
            modelName: string;
            modelTask: string;
            modelAutoScale?: SagemakerAutoScaleConfig;
            executionRole: IRole;
            instanceType: string;
            containerStartupHealthCheckTimeoutInSeconds?: number;
        }
    ) {
        super(scope, id);

        const modelName = MachinelearningHelpers.getModelName(props.modelName);
        const endpointConfigName = MachinelearningHelpers.getEndpointConfigName(props.modelName);
        const endpointName = MachinelearningHelpers.getEndpointName(props.modelName);

        let modelContainerImage: ContainerImage | undefined = undefined;
        if (!props.modelDockerImageAsset) {
            if (props.modelDeepLearningContainer) {
                modelContainerImage = HuggingfaceHelpers.getContainerImage(
                    props.modelDeepLearningContainer.repositoryName,
                    props.modelDeepLearningContainer.tag
                );
            } else {
                throw new Error(
                    "A 'modelDeepLearningContainer' definition must be provided when no 'modelDockerImageAsset' is available. "
                );
            }
        }

        let modelEnvironment = {
            ...props.modelEnvironment,
        };

        // Do NOT define these environment variables if a modelS3Artifact is explicitly defined.
        // SageMaker HuggingFace Inference toolkit will attempt to download the model if these environment variables are provided.
        // That will override the un-tarred contents of the model s3 tar contents.
        // https://github.com/aws/sagemaker-huggingface-inference-toolkit/blob/2237804030f15157f932adfca86f9b06e04f244c/src/sagemaker_huggingface_inference_toolkit/mms_model_server.py#L67
        if (!props.modelS3Artifact) {
            modelEnvironment = {
                ...modelEnvironment,
                HF_MODEL_ID: props.modelName,
                HF_TASK: props.modelTask,
            };
        }

        if (props.modelToken) {
            const modelToken = SecretHelpers.getSecret(this, props.modelToken.secretName);
            modelEnvironment[props.modelToken.name] = modelToken;
        }

        const endpoint = new SagemakerEndpointConstruct(this, modelName, {
            modelAutoScale: props.modelAutoScale,
            modelDockerImageAsset: props.modelDockerImageAsset,
            modelContainerImage: modelContainerImage,
            modelS3Artifact: props.modelS3Artifact,
            modelEnvironment: modelEnvironment,
            awsEnvAccount: props.awsEnvAccount,
            modelName: modelName,
            endpointConfigName: endpointConfigName,
            endpointName: endpointName,
            executionRole: props.executionRole,
            instanceType: props.instanceType,
            containerStartupHealthCheckTimeoutInSeconds: props.containerStartupHealthCheckTimeoutInSeconds,
        });
        this.endpointName = endpointName;
        this.endpoint = endpoint.endpoint;
    }
}
