import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../../build-config';
import { Peer, Port, SubnetSelection, SubnetType, Vpc } from 'aws-cdk-lib/aws-ec2';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { ECSMachineLearningModel, ECSMachineLearningModels } from '../../machine-learning/config';
import { AcmStack } from '../../acm/acm-stack';
import { NetworkStack } from '../../vpc/network-stack';
import { DnsStack } from '../../dns/dns-stack';
import { Dns } from '../dns/config';
import { ELBHelpers } from '../utils/elb-helpers';
import { VPCConfig } from '../vpc/config';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import { EC2ServiceConstruct } from '../ecs/ec2-service-construct';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import { Duration } from 'aws-cdk-lib';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53Targets from 'aws-cdk-lib/aws-route53-targets';

interface LoadBalancer {
    loadBalancer: elbv2.IApplicationLoadBalancer;
    listener: elbv2.IApplicationListener;
}

export interface ECSMachineLearningModelsConstructProps {
    ecsMachineLearningModels: ECSMachineLearningModels;
    acmStack: AcmStack;
    networkStack: NetworkStack;
    dnsStack: DnsStack;
    awsEnvAccount: AwsEnvAccount;
    dns: Dns;
    peerVPCs: VPCConfig[];
    cdkAppInfo: CdkAppInfo;
}

// https://github.com/rajyan/low-cost-ecs/blob/main/src/low-cost-ecs.ts
// https://github.com/sofianhamiti/amazon-ecs-nvidia-triton-cdk/blob/main/stacks/ecs_stack.py
// https://containersonaws.com/pattern/ecs-gpu-scheduling-cdk
export class ECSMachineLearningModelsConstruct extends Construct {
    constructor(scope: Construct, id: string, props: ECSMachineLearningModelsConstructProps) {
        super(scope, id);
        const vpc = props.networkStack.coreVpc;
        const vpcSubnets = vpc.selectSubnets({
            subnetType: SubnetType.PRIVATE_WITH_EGRESS,
        });

        const loadBalancer = this.createLoadBalancer(props, vpc, vpcSubnets);
        for (const machineLearningModel of props.ecsMachineLearningModels.models) {
            this.createMachineLearningModel(props, vpc, vpcSubnets, loadBalancer, machineLearningModel);
        }
    }

    private createMachineLearningModel(
        props: ECSMachineLearningModelsConstructProps,
        vpc: Vpc,
        vpcSubnets: SubnetSelection,
        loadBalancer: LoadBalancer,
        machineLearningModel: ECSMachineLearningModel
    ): ecs.Ec2Service {
        const ec2Service = new EC2ServiceConstruct(this, machineLearningModel.ec2Service.name, {
            ec2Service: machineLearningModel.ec2Service,
            vpc: vpc,
            vpcSubnets: vpcSubnets,
        });

        const healthCheck = machineLearningModel.healthCheck
            ? ELBHelpers.convertToHealthCheck(machineLearningModel.healthCheck)
            : undefined;

        const targetGroup = loadBalancer.listener.addTargets(`${machineLearningModel.ec2Service.name}Listener`, {
            conditions: [elbv2.ListenerCondition.pathPatterns(machineLearningModel.apiPaths)],
            // priority must be unique per target!
            priority: machineLearningModel.apiPriority,
            targets: [ec2Service.ec2Service],
            port: 80,
            healthCheck: healthCheck,
        });

        // These targets can only be applied once auto scaling group has a load balancer associated with it
        ec2Service.addLoadBalancedScalingTargets(targetGroup);

        return ec2Service.ec2Service;
    }

    private createLoadBalancer(
        props: ECSMachineLearningModelsConstructProps,
        vpc: Vpc,
        vpcSubnets: SubnetSelection
    ): LoadBalancer {
        /**
         *  Get ACM cert
         */
        const acmCertificate = props.acmStack.certs.get(props.ecsMachineLearningModels.certName);
        if (acmCertificate == undefined) {
            throw new Error(`failed to retrieve ${props.ecsMachineLearningModels.certName} cert from acm stack`);
        }

        /**
         *  Create security group for ALB instances
         */
        const securityGroup = new ec2.SecurityGroup(this, 'ECSMachineLearningModelStackAlbSG', {
            vpc: vpc,
            allowAllOutbound: true,
            description: 'security group to limit inbound traffic to ML alb instances',
        });

        for (const peerVPC of props.peerVPCs) {
            securityGroup.addIngressRule(
                Peer.ipv4(peerVPC.networkCIDR),
                Port.tcp(443),
                'Allow HTTPs traffic from Peer VPC CIDR'
            );

            peerVPC.peeringTarget.forEach((peeringTarget) => {
                securityGroup.addIngressRule(
                    Peer.ipv4(peeringTarget.networkCIDR),
                    Port.tcp(443),
                    'Allow HTTPs traffic from Peer VPC CIDR'
                );
            });
        }

        // const albLogsName = `aws-test-alb-logs-${props.cdkAppInfo.environment}`;
        // const albLogsBucket = new aws_s3.Bucket(this, albLogsName, {
        //     bucketName: albLogsName,
        //     publicReadAccess: false,
        //     blockPublicAccess: aws_s3.BlockPublicAccess.BLOCK_ALL,
        //     removalPolicy: RemovalPolicy.RETAIN,
        //     autoDeleteObjects: false,
        //     versioned: true,
        // });

        /**
         *  Create and configure ALB
         */
        const lb = new elbv2.ApplicationLoadBalancer(this, 'ECSMachineLearningModelStackALB', {
            securityGroup: securityGroup,
            vpcSubnets: vpcSubnets,
            internetFacing: false,
            vpc: vpc,
            idleTimeout: Duration.seconds(300), // max allowed value: 4000
        });
        // lb.logAccessLogs(albLogsBucket);

        // Add HTTPS listener
        const listener = lb.addListener('ECSMachineLearningModelStackALBListener', {
            port: 443,
            certificates: [acmCertificate],
            defaultAction: elbv2.ListenerAction.fixedResponse(404, {
                contentType: 'text/plain',
                messageBody: 'Your Not found',
            }),
            open: true,
        });

        // Create DNS record
        new route53.ARecord(this, 'ECSMachineLearningModelStackAliasRecord', {
            recordName: props.ecsMachineLearningModels.certDomainName,
            zone: props.dnsStack.myHostedZone,
            target: route53.RecordTarget.fromAlias(new route53Targets.LoadBalancerTarget(lb)),
        });

        return {
            loadBalancer: lb,
            listener: listener,
        };
    }
}
