import { array, JSONObject, optional, required } from 'ts-json-object';
import { AmiHardwareType } from '../autoscaling/config';
import { CloudWatchAgent } from '../cloudwatch/config';
import { ECSConfig } from '../ecs/ecs-config';
import { AutoScalingGroup } from '../autoscaling/config';

export type UserDataType = 'linux' | 'multipart';
export type SecurityGroupPeerType = 'anyIpv4' | 'anyIpv6' | 'ipv4Cidr' | 'ipv6Cidr' | 'securityGroup';
export type SecurityGroupPortType = 'tcp' | 'udp' | 'icmp' | 'udp' | 'all';

export class SecurityGroupPortRule extends JSONObject {
    @required
    port: number;
    @required
    portType: SecurityGroupPortType;
}

export class SecurityGroupPeerRule extends JSONObject {
    @required
    peerType: SecurityGroupPeerType;
    @optional(undefined)
    ipv4Cidr?: string;
    @optional(undefined)
    ipv6Cidr?: string;
    @optional(undefined)
    securityGroupId?: string;
}

export class SecurityGroupRule extends JSONObject {
    @required
    peerRule: SecurityGroupPeerRule;
    @required
    portRule: SecurityGroupPortRule;
}

export class SecurityGroup extends JSONObject {
    @required
    name: string;

    @required
    description: string;

    @optional(undefined)
    allowAllOutbound?: boolean;

    @optional([])
    @array(SecurityGroupRule)
    ingressRules: Array<SecurityGroupRule>;

    @optional([])
    @array(SecurityGroupRule)
    egressRules: Array<SecurityGroupRule>;
}

export class UserDataPart extends JSONObject {
    @optional(undefined)
    contentType?: string;
    @optional(undefined)
    commands?: Array<string>;
    @optional(undefined)
    userDataFilePath?: string;
}

export class UserData extends JSONObject {
    @required
    type: UserDataType;
    @required
    userDataParts: Array<UserDataPart>;
}

export type VolumeType = 'standard' | 'io1' | 'gp2' | 'gp3' | 'st1' | 'sc1';

export class BlockDeviceVolume extends JSONObject {
    @required
    sizeInGB: number;
    @required
    deleteOnTermination: boolean;
    @required
    volumeType: VolumeType;
}

export class BlockDevice extends JSONObject {
    @required
    deviceName: string;
    @required
    volume: BlockDeviceVolume;
}

export class LaunchTemplate extends JSONObject {
    @required
    name: string;
    @optional(undefined)
    keyPairName?: string;
    @optional([])
    blockDevices: Array<BlockDevice>;
    @optional(undefined)
    instanceType?: string;
    @optional(undefined)
    machineAmiType?: AmiHardwareType;
    @optional(undefined)
    associatePublicIpAddress?: boolean;
    @optional(undefined)
    cloudWatchAgent?: CloudWatchAgent;
    @optional(undefined)
    ecsConfig?: ECSConfig;
    @optional(undefined)
    userData?: UserData;
    @optional(undefined)
    securityGroup?: SecurityGroup;
}
export class ElasticIPConfig extends JSONObject {
    @required
    name: string;

    @required
    ip: string;

    @required
    id: string;
}

export class CustomerHTTPProxyConfig extends JSONObject {
    @required
    name: string;

    @optional(true)
    enable: boolean;

    @required
    elasticIps: ElasticIPConfig[];

    @required
    allowedIngressCIDRs: string[];

    // Simple overrides only
    @optional('t3.small')
    instanceType: string;

    @optional('assets/userdata/http-proxy-node.sh')
    userDataScriptPath: string;

    // Example ["SUN:02:00-04:00","WED:01:00-03:00"]
    // IMPORTANT: All times are UTC
    @optional([])
    blackoutWindow: string[];
}

export class CustomerHTTPProxy extends JSONObject {
    @required
    httpProxy: CustomerHTTPProxyConfig[];
}
