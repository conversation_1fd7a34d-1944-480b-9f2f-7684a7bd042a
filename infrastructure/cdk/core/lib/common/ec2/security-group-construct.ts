import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Construct } from 'constructs';
import { SecurityGroup } from './config';
import { EC2Helpers } from '../utils/ec2-helpers';
import { CfnOutput } from 'aws-cdk-lib';

interface SecurityGroupConstructProps {
    securityGroup: SecurityGroup;
    vpc: ec2.IVpc;
}

export class SecurityGroupConstruct extends Construct {
    public readonly securityGroup: ec2.ISecurityGroup;

    constructor(scope: Construct, id: string, props: SecurityGroupConstructProps) {
        super(scope, id);

        const securityGroup = new ec2.SecurityGroup(this, props.securityGroup.name, {
            securityGroupName: props.securityGroup.name,
            vpc: props.vpc,
            allowAllOutbound: props.securityGroup.allowAllOutbound,
            description: props.securityGroup.description,
        });

        props.securityGroup.ingressRules.forEach((rule) => {
            const peer = EC2Helpers.convertToPeer(rule.peerRule);
            if (peer) {
                securityGroup.addIngressRule(peer, EC2Helpers.convertToPort(rule.portRule));
            }
        });

        props.securityGroup.egressRules.forEach((rule) => {
            const peer = EC2Helpers.convertToPeer(rule.peerRule);
            if (peer) {
                securityGroup.addEgressRule(peer, EC2Helpers.convertToPort(rule.portRule));
            }
        });

        this.securityGroup = securityGroup;

        new CfnOutput(this, `${props.securityGroup.name}Id`, { value: securityGroup.securityGroupId });
    }
}
