import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as autoscaling from 'aws-cdk-lib/aws-autoscaling';
import { Construct } from 'constructs';
import { Size } from 'aws-cdk-lib';
import { EbsHelpers } from '../utils/ebs-helpers';
import { EC2Helpers } from '../utils/ec2-helpers';
import { ECSHelpers } from '../utils/ecs-helpers';
import { CloudwatchAgentConstruct } from '../cloudwatch/cloudwatch-agent-construct';
import { UserDataHelpers } from '../utils/user-data-helpers';
import { LaunchTemplate } from './config';
import { ECSConfigConstruct } from '../ecs/ecs-config-construct';
import { SecurityGroupConstruct } from './security-group-construct';

interface LaunchTemplateConstructProps {
    launchTemplate: LaunchTemplate;
    vpc: ec2.IVpc;
    role?: iam.IRole;
}

export class LaunchTemplateConstruct extends Construct {
    readonly launchTemplate: ec2.LaunchTemplate;

    constructor(scope: Construct, id: string, props: LaunchTemplateConstructProps) {
        super(scope, id);

        const keyPair = props.launchTemplate.keyPairName
            ? ec2.KeyPair.fromKeyPairName(this, `${props.launchTemplate.name}KeyPair`, props.launchTemplate.keyPairName)
            : undefined;

        const blockDevices: ec2.BlockDevice[] = props.launchTemplate.blockDevices.map((blockDevice) => {
            const ec2BlockDevice: ec2.BlockDevice = {
                deviceName: blockDevice.deviceName,
                volume: autoscaling.BlockDeviceVolume.ebs(Size.gibibytes(blockDevice.volume.sizeInGB).toGibibytes(), {
                    deleteOnTermination: blockDevice.volume.deleteOnTermination,
                    volumeType: EbsHelpers.convertToEbsDeviceVolumeType(blockDevice.volume.volumeType),
                }),
            };
            return ec2BlockDevice;
        });

        const instancetype = props.launchTemplate.instanceType
            ? EC2Helpers.findInstanceType(props.launchTemplate.instanceType)
            : undefined;

        const machineImage = props.launchTemplate.machineAmiType
            ? ECSHelpers.convertToMachineImage(props.launchTemplate.machineAmiType)
            : undefined;

        const userData = this.createUserData(props);
        const instanceSecurityGroup = this.createInstanceSecurityGroup(props);

        this.launchTemplate = new ec2.LaunchTemplate(this, props.launchTemplate.name, {
            keyPair: keyPair,
            blockDevices: blockDevices,
            instanceType: instancetype,
            machineImage: machineImage,
            role: props.role,
            securityGroup: instanceSecurityGroup,
            associatePublicIpAddress: props.launchTemplate.associatePublicIpAddress,
            userData: userData,
        });
    }

    private createInstanceSecurityGroup(props: LaunchTemplateConstructProps): ec2.ISecurityGroup | undefined {
        if (props.launchTemplate.securityGroup) {
            const securityGroup = new SecurityGroupConstruct(this, props.launchTemplate.securityGroup.name, {
                securityGroup: props.launchTemplate.securityGroup,
                vpc: props.vpc,
            });
            return securityGroup.securityGroup;
        }
        return undefined;
    }

    // Add custom userData.
    // For ECS, it seems CDK will automatically add the necessary:
    // echo ECS_CLUSTER=<cluser> >> /etc/ecs/ecs.config
    private createUserData(props: LaunchTemplateConstructProps): ec2.UserData | undefined {
        let userData = props.launchTemplate.userData
            ? UserDataHelpers.generateUserData(props.launchTemplate.userData)
            : undefined;

        if (props.launchTemplate.cloudWatchAgent) {
            userData = userData ?? ec2.UserData.forLinux();
            const cloudWatchAgent = new CloudwatchAgentConstruct(this, `${props.launchTemplate.name}CloudWatchAgent`, {
                cloudWatchAgent: props.launchTemplate.cloudWatchAgent,
            });

            userData.addCommands(cloudWatchAgent.userData);
        }

        if (props.launchTemplate.ecsConfig) {
            userData = userData ?? ec2.UserData.forLinux();
            const ecsConfig = new ECSConfigConstruct(this, `${props.launchTemplate.name}ECSConfig`, {
                ecsConfig: props.launchTemplate.ecsConfig,
            });

            userData.addCommands(ecsConfig.userData);
        }

        return userData;
    }
}
