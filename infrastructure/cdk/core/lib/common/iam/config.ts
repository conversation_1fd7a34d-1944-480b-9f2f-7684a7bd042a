import { array, JSONObject, optional, required } from 'ts-json-object';

export class IamPolicyStatement extends JSONObject {
    @optional('Allow')
    effect: string;
    @optional(['sts:AssumeRole'])
    actions: string[];
    @required
    resources: string[];
}

export class <PERSON>amGroup extends JSONObject {
    @required
    name: string;
    @optional([])
    members: string[];
    @optional([])
    managedPolicies: string[];
    @optional([])
    @array(IamPolicyStatement)
    policyStatements: Array<IamPolicyStatement>;
}

export class Iam extends JSONObject {
    @optional(false)
    createK8sDeployerRole: boolean;
    @optional(false)
    createK8sReaderRole: boolean;
    @optional(false)
    createCloudWatchReadOnlyRole: boolean;
    @optional(false)
    createCrossAccountAdminReadOnlyRole: boolean;
    @optional(false)
    createEc2DeploybotRole: boolean;
    @optional(false)
    createCrossAccountS3ReadOnlyRole: boolean;
    @optional([])
    peeringAccepterRoleTargetAccounts: string[];

    @optional([])
    @array(IamGroup)
    groups: Array<IamGroup>;
}
