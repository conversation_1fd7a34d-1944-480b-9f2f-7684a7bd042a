import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { IResource, Resource } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { ITransitGateway } from './transit-gateway';

export interface TransitGatewayAttachmentProps {
    vpc: ec2.IVpc;
    transitGateway: ITransitGateway;
    subnets: ec2.SubnetSelection;
}

export interface ITransitGatewayAttachment extends IResource {
    readonly transitGatewayAttachmentId: string;
}

abstract class TransitGatewayAttachmentBase extends Resource implements ITransitGatewayAttachment {
    /**
     * The id of the Attachment
     *
     * @attribute
     */
    public abstract readonly transitGatewayAttachmentId: string;
    public abstract readonly transitGatewayId: string;
}

export interface TransitGatewayAttachmentAttributes {
    transitGatewayId: string;
    transitGatewayAttachmentId: string;
}

/**
 *
 * @resource AWS::EC2::TransitGatewayAttachment
 */
export class TransitGatewayAttachment extends TransitGatewayAttachmentBase {
    public static fromTransitGatewayAttachmentAttributes(
        scope: Construct,
        id: string,
        attrs: TransitGatewayAttachmentAttributes
    ): ITransitGatewayAttachment {
        class Import extends TransitGatewayAttachmentBase {
            public transitGatewayAttachmentId = attrs.transitGatewayAttachmentId;
            public transitGatewayId = attrs.transitGatewayId;
        }

        return new Import(scope, id);
    }

    public readonly transitGatewayAttachmentId: string;
    public readonly transitGatewayId: string;

    constructor(scope: Construct, id: string, props: TransitGatewayAttachmentProps) {
        super(scope, id, {
            physicalName: '',
        });

        const attachment = new ec2.CfnTransitGatewayAttachment(this, 'TgwAttachment', {
            vpcId: props.vpc.vpcId,
            transitGatewayId: props.transitGateway.transitGatewayId,
            subnetIds: props.vpc.selectSubnets(props.subnets).subnetIds,
        });
        this.transitGatewayId = attachment.transitGatewayId;

        this.transitGatewayAttachmentId = attachment.ref;
    }
}
