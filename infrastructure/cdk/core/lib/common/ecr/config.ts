import { array, JSONObject, optional, required, validate } from 'ts-json-object';
import { LambdaConfig } from '../lambda/config';

export class <PERSON>crRepo extends JSONObject {
    @required
    name: string;
    @optional(50)
    maxImageCount: number;
    @optional(false)
    mutableTags: boolean;
}

export class EcrReplicationTarget extends JSONObject {
    @required
    region: string;
    @required
    registryId: string;
}

export class Ecr extends JSONObject {
    @optional('')
    sourceAccount: string;
    @optional(undefined)
    cleanupLambda?: LambdaConfig;

    @optional([])
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    @validate((ecr: Ecr, key: string, value: any) => {
        if (ecr.sourceAccount && value.length > 0) {
            throw new TypeError(`Cannot specify ecrReplicationTargets when ecrSourceAccount is set`);
        }
    })
    @array(EcrReplicationTarget)
    ecrReplicationTargets: Array<EcrReplicationTarget>;

    @optional([])
    @array(EcrRepo)
    ecrRepos: Array<EcrRepo>;
}
