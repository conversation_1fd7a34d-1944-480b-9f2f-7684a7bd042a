import * as path from 'path';
import * as dotenv from 'dotenv';

export function loadEnvironmentFromFile(environment: string): void {
    try {
        // Define the path based on the provided environment
        const filePath = path.join(
            process.env.HOME || process.env.USERPROFILE || '',
            '.secrets/unblocked/cdk',
            environment,
            '.env'
        );

        // Load the file contents into process environment
        dotenv.config({ path: filePath });

        console.log('Environment variables loaded successfully.');
    } catch (error) {
        console.error('Error loading environment variables:', error);
    }
}
