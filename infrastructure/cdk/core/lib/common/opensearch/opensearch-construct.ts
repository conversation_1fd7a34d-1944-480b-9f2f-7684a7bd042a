import { Construct } from 'constructs';
import * as opensearch from 'aws-cdk-lib/aws-opensearchservice';
import { OpenSearch } from './config';
import { SubnetSelection, Vpc, SecurityGroup, Peer, Port, ISubnet } from 'aws-cdk-lib/aws-ec2';
import { OpenSearchHelpers } from '../utils/opensearch-helpers';
import { CfnOutput } from 'aws-cdk-lib';
import { AnyPrincipal, PolicyStatement } from 'aws-cdk-lib/aws-iam';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { VPCConfig } from '../vpc/config';

export interface OpenSearchConstructProps {
    openSearch: OpenSearch;
    vpc: Vpc;
    peerVPCs: Array<VPCConfig>;
}

// https://github.com/aws-samples/opensearch-vpc-cdk/blob/main/lib/opensearch-vpc-cdk-stack.ts
export class OpenSearchConstruct extends Construct {
    public readonly domainEndpoint: string;

    constructor(scope: Construct, id: string, props: OpenSearchConstructProps) {
        super(scope, id);

        const ebsOptions = props.openSearch.ebsOptions
            ? OpenSearchHelpers.convertToEbsOptions(props.openSearch.ebsOptions)
            : undefined;

        const zoneAwareness = props.openSearch.zoneAwareness
            ? OpenSearchHelpers.convertToZoneAwarenessConfig(props.openSearch.zoneAwareness)
            : undefined;

        const removalPolicy = props.openSearch.removalPolicy
            ? OpenSearchHelpers.convertToRemovalPolicy(props.openSearch.removalPolicy)
            : undefined;

        const masterUserSecret = new secretsmanager.Secret(this, props.openSearch.masterUser.password.secretName, {
            secretName: props.openSearch.masterUser.password.secretName,
            generateSecretString: {
                secretStringTemplate: JSON.stringify({ username: props.openSearch.masterUser.username }),
                generateStringKey: 'password',
                excludeCharacters: props.openSearch.masterUser.password.excludeCharacters,
                passwordLength: props.openSearch.masterUser.password.passwordLength,
                requireEachIncludedType: props.openSearch.masterUser.password.requireEachIncludedType,
            },
        });

        // Create a security group for OpenSearch
        const securityGroup = new SecurityGroup(this, 'OpenSearchSecurityGroup', {
            vpc: props.vpc,
            allowAllOutbound: true,
            description: 'Security group for OpenSearch allowing peer VPC access',
        });

        // Add ingress rules for each peer VPC
        for (const peerVPC of props.peerVPCs) {
            securityGroup.addIngressRule(
                Peer.ipv4(peerVPC.networkCIDR),
                Port.tcp(443), // Assuming HTTPS access
                'Allow HTTPS traffic from Peer VPC CIDR'
            );

            for (const peeringTarget of peerVPC.peeringTarget) {
                securityGroup.addIngressRule(
                    Peer.ipv4(peeringTarget.networkCIDR),
                    Port.tcp(443), // Assuming HTTPS access
                    'Allow HTTPS traffic from Peer VPC CIDR'
                );
            }
        }

        const coreSubnetsSet = new Set<ISubnet>([
            ...props.vpc.isolatedSubnets,
            ...props.vpc.privateSubnets,
            ...props.vpc.publicSubnets,
        ]);

        const coreVpcSubnets = props.vpc.selectSubnets({
            subnets: Array.from(coreSubnetsSet).slice(0, props.openSearch.zoneAwareness?.availabilityZoneCount ?? 1),
        });

        const domain = new opensearch.Domain(this, props.openSearch.domainName, {
            domainName: props.openSearch.domainName,
            version: opensearch.EngineVersion.openSearch(props.openSearch.version),
            capacity: {
                dataNodes: props.openSearch.dataNodes.instanceCount,
                dataNodeInstanceType: props.openSearch.dataNodes.instanceType,
                masterNodes: props.openSearch.masterNodes?.instanceCount,
                masterNodeInstanceType: props.openSearch.masterNodes?.instanceType,
                warmNodes: props.openSearch.warmNodes?.instanceCount,
                warmInstanceType: props.openSearch.warmNodes?.instanceType,
            },
            ebs: ebsOptions,
            nodeToNodeEncryption: props.openSearch.enableNodeToNodeEncryption,
            encryptionAtRest: {
                enabled: props.openSearch.enableEncryptionAtRest,
            },
            logging: {
                appLogEnabled: props.openSearch.enableLogging,
                slowSearchLogEnabled: props.openSearch.enableLogging,
                slowIndexLogEnabled: props.openSearch.enableLogging,
            },
            enforceHttps: props.openSearch.enforceHttps,
            removalPolicy: removalPolicy,
            fineGrainedAccessControl: {
                masterUserName: masterUserSecret.secretValueFromJson('username').toString(),
                masterUserPassword: masterUserSecret.secretValueFromJson('password'),
            },
            enableVersionUpgrade: props.openSearch.enableVersionUpgrade,
            enableAutoSoftwareUpdate: props.openSearch.enableAutoSoftwareUpdate,
            useUnsignedBasicAuth: props.openSearch.useUnsignedBasicAuth,
            zoneAwareness: zoneAwareness,
            securityGroups: [securityGroup],
            vpc: props.vpc,
            vpcSubnets: [coreVpcSubnets],
        });

        domain.addAccessPolicies(
            new PolicyStatement({
                principals: [new AnyPrincipal()],
                actions: ['es:ESHttp*'],
                resources: [domain.domainArn + '/*'],
            })
        );

        new CfnOutput(this, 'DomainEndpoint', {
            value: domain.domainEndpoint,
        });

        this.domainEndpoint = domain.domainEndpoint;
    }
}
