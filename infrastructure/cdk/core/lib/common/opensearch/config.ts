import { JSONObject, required, optional } from 'ts-json-object';
import { VolumeType } from '../ec2/config';
import { AwsSecret } from '../asm/config';

export type RemovalPolicy = 'retain' | 'destroy' | 'snapshot';

export type OpenSearchVersion =
    | '2.17'
    | '2.15'
    | '2.13'
    | '2.11'
    | '2.9'
    | '2.7'
    | '2.5'
    | '2.3'
    | '1.3'
    | '1.2'
    | '1.1'
    | '1.0';

export class NodeGroup extends JSONObject {
    @required
    instanceType: string;

    @required
    instanceCount: number;
}

export class EbsOptions extends J<PERSON>NObject {
    @required
    sizeInGB: number;
    @required
    volumeType: VolumeType;
}

export class OpenSearchUser extends JSONObject {
    @required
    username: string;
    @required
    password: AwsSecret;
}

export class ZoneAwareness extends JSONObject {
    @required
    enabled: boolean;
    @required
    availabilityZoneCount: number;
}

export class OpenSearch extends J<PERSON>NObject {
    @required
    domainName: string;

    @required
    version: OpenSearchVersion;

    // Use NodeConfig for data nodes
    @required
    dataNodes: NodeGroup;

    // Use NodeConfig for master nodes
    @optional(undefined)
    masterNodes?: NodeGroup;

    // Use NodeConfig for warm nodes
    @optional(undefined)
    warmNodes?: NodeGroup;

    @optional(undefined)
    enableEncryptionAtRest?: boolean;

    @optional(undefined)
    enableNodeToNodeEncryption?: boolean;

    @optional(undefined)
    enableLogging?: boolean;

    @optional(undefined)
    ebsOptions?: EbsOptions;

    @optional('destroy')
    removalPolicy?: RemovalPolicy;

    @required
    masterUser: OpenSearchUser;

    @optional(true)
    enforceHttps: boolean;

    @optional(true)
    enableVersionUpgrade: boolean;

    @optional(true)
    enableAutoSoftwareUpdate: boolean;

    @optional(true)
    useUnsignedBasicAuth: boolean;

    @optional(undefined)
    zoneAwareness?: ZoneAwareness;
}
