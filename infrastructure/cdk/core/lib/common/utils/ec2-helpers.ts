import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { SecurityGroupPeerRule, SecurityGroupPortRule } from '../ec2/config';
import { EnumHelpers } from './enum-helpers';
import { InstanceSize } from 'aws-cdk-lib/aws-ec2';

export interface InstanceComponents {
    instanceClass: string;
    instanceSize: InstanceSize;
}

export class EC2Helpers {
    public static findInstanceType(instanceClassDotSizeInString: string): ec2.InstanceType {
        const instanceComponents = this.findInstanceComponents(instanceClassDotSizeInString);
        return new ec2.InstanceType(`${instanceComponents.instanceClass}.${instanceComponents.instanceSize}`);
    }

    public static findInstanceComponents(instanceClassDotSizeInString: string): InstanceComponents {
        let tempArray = instanceClassDotSizeInString.split('.');

        // Must be [2,3] length
        // i.e. 'ml.c5.xlarge' or 'c5.xlarge'
        if (tempArray.length < 2 || tempArray.length > 3) {
            throw new Error('Invalid ec2 instance class represented via string');
        }

        tempArray = tempArray.slice(-2);

        const instanceClass = EnumHelpers.findEnumType(ec2.InstanceClass, tempArray[0]);
        const instanceSize = EnumHelpers.findEnumType(ec2.InstanceSize, tempArray[1]);

        if (!instanceClass) {
            console.warn(`The provided instance class is not publicly available in the CDK: ${tempArray[0]}`);
        }
        if (!instanceSize) {
            console.warn(`The provided instance size is not publicly available in the CDK: ${tempArray[1]}`);
        }

        return {
            instanceClass: instanceClass ?? tempArray[0],
            instanceSize: instanceSize,
        };
    }

    public static convertToPort(portRule: SecurityGroupPortRule): ec2.Port {
        switch (portRule.portType) {
            case 'tcp':
                return ec2.Port.tcp(portRule.port);
            case 'udp':
                return ec2.Port.udp(portRule.port);
            case 'icmp':
                return ec2.Port.icmpType(portRule.port);
            case 'all':
                return ec2.Port.allTraffic();
            default:
                throw new Error(`Unsupported port type: ${portRule.portType}`);
        }
    }

    public static convertToPeer(peerRule: SecurityGroupPeerRule): ec2.IPeer {
        switch (peerRule.peerType) {
            case 'anyIpv4':
                return ec2.Peer.anyIpv4();
            case 'anyIpv6':
                return ec2.Peer.anyIpv6();
            case 'ipv4Cidr':
                if (!peerRule.ipv4Cidr) {
                    throw new Error(`IPv4 CIDR is required for peerType 'ipv4Cidr'`);
                }
                return ec2.Peer.ipv4(peerRule.ipv4Cidr);
            case 'ipv6Cidr':
                if (!peerRule.ipv6Cidr) {
                    throw new Error(`IPv6 CIDR is required for peerType 'ipv6Cidr'`);
                }
                return ec2.Peer.ipv6(peerRule.ipv6Cidr);
            case 'securityGroup':
                if (!peerRule.securityGroupId) {
                    throw new Error(`Security Group ID is required for peerType 'securityGroup'`);
                }
                return ec2.Peer.securityGroupId(peerRule.securityGroupId);
            default:
                throw new Error(`Unsupported peer type: ${peerRule.peerType}`);
        }
    }
}
