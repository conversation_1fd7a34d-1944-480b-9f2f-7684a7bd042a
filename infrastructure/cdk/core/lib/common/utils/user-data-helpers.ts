import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as fs from 'fs';
import { UserData, UserDataPart } from '../ec2/config';

export class UserDataHelpers {
    static generateUserData(userdata: UserData): ec2.UserData | ec2.MultipartUserData {
        switch (userdata.type) {
            case 'linux':
                return UserDataHelpers.generateLinuxUserData(userdata.userDataParts);
            case 'multipart':
                return UserDataHelpers.generateMultipartUserData(userdata.userDataParts);
            default:
                throw new Error('Unsupported user data type');
        }
    }

    private static generateLinuxUserData(userDataParts: UserDataPart[]): ec2.UserData {
        const userData = ec2.UserData.forLinux();

        for (const part of userDataParts) {
            if (part.userDataFilePath) {
                const fileContents = fs.readFileSync(part.userDataFilePath, 'utf-8');
                userData.addCommands(fileContents);
            }
            if (part.commands) {
                for (const command of part.commands) {
                    userData.addCommands(command);
                }
            }
        }

        return userData;
    }

    private static generateMultipartUserData(userDataParts: UserDataPart[]): ec2.MultipartUserData {
        const multipartUserData = new ec2.MultipartUserData();

        for (const part of userDataParts) {
            const userData = this.generateLinuxUserData([part]);
            multipartUserData.addUserDataPart(userData, part.contentType);
        }

        return multipartUserData;
    }
}
