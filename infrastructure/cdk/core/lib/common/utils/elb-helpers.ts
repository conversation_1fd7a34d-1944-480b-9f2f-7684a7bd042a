import { HealthCheck } from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as cdk from 'aws-cdk-lib';
import { HealthCheckConfig } from '../elb/config';

export class ELBHelpers {
    public static convertToHealthCheck(config: HealthCheckConfig): HealthCheck {
        return {
            interval: config.intervalInSeconds ? cdk.Duration.seconds(config.intervalInSeconds) : undefined,
            timeout: config.timeoutInSeconds ? cdk.Duration.seconds(config.timeoutInSeconds) : undefined,
            unhealthyThresholdCount: config.unhealthyThresholdCount,
            healthyThresholdCount: config.healthyThresholdCount,
        };
    }
}
