import { CronOptions, Schedule } from '../events/config';
import * as events from 'aws-cdk-lib/aws-events';
import { Duration } from 'aws-cdk-lib';

export class EventHelpers {
    public static convertToCronOptions(cronOptions: CronOptions): events.CronOptions {
        return {
            minute: cronOptions.minute,
            hour: cronOptions.hour,
            day: cronOptions.day,
            month: cronOptions.day,
            weekDay: cronOptions.weekDay,
        };
    }

    public static convertToSchedule(schedule: Schedule): events.Schedule {
        if (schedule.scheduleRateHours) {
            return events.Schedule.rate(Duration.hours(schedule.scheduleRateHours));
        } else if (schedule.scheduleRateMinutes) {
            return events.Schedule.rate(Duration.minutes(schedule.scheduleRateMinutes));
        } else if (schedule.cronOptions) {
            return events.Schedule.cron(EventHelpers.convertToCronOptions(schedule.cronOptions));
        } else {
            throw new Error('There are no event rule schedule options provided');
        }
    }
}
