import * as sfn from 'aws-cdk-lib/aws-stepfunctions';
import { ChoiceCondition, ChoiceOperation, CompoundChoiceCondition } from '../../data-pipelines/config';

export class ChoiceHelpers {
    public static getCombinedChoiceCondition(
        operation: ChoiceOperation | undefined,
        conditions: Array<CompoundChoiceCondition>
    ): sfn.Condition {
        const whenConditions: sfn.Condition[] = conditions.map((condition) =>
            this.getCompoundChoiceCondition(condition)
        );

        switch (operation) {
            case 'Or':
                return sfn.Condition.or(...whenConditions);
            case 'And':
                return sfn.Condition.and(...whenConditions);
            default:
                return sfn.Condition.and(...whenConditions);
        }
    }

    public static getCompoundChoiceCondition(condition: CompoundChoiceCondition): sfn.Condition {
        const resolvedCondition = ChoiceHelpers.getChoiceCondition(condition.condition);
        switch (condition.operation) {
            case 'Not':
                return sfn.Condition.not(resolvedCondition);
            default:
                return resolvedCondition;
        }
    }

    public static getChoiceCondition(condition: ChoiceCondition): sfn.Condition {
        switch (condition.type) {
            case 'StringEquals':
                return sfn.Condition.stringEquals(condition.variable, condition.StringEquals);
            case 'StringLessThan':
                return sfn.Condition.stringLessThan(condition.variable, condition.StringLessThan);
            case 'StringGreaterThan':
                return sfn.Condition.stringGreaterThan(condition.variable, condition.StringGreaterThan);
            case 'StringLessThanEquals':
                return sfn.Condition.stringLessThanEquals(condition.variable, condition.StringLessThanEquals);
            case 'StringGreaterThanEquals':
                return sfn.Condition.stringGreaterThanEquals(condition.variable, condition.StringGreaterThanEquals);
            case 'NumericEquals':
                return sfn.Condition.numberEquals(condition.variable, condition.NumericEquals);
            case 'NumericLessThan':
                return sfn.Condition.numberLessThan(condition.variable, condition.NumericLessThan);
            case 'NumericGreaterThan':
                return sfn.Condition.numberGreaterThan(condition.variable, condition.NumericGreaterThan);
            case 'NumericLessThanEquals':
                return sfn.Condition.numberLessThanEquals(condition.variable, condition.NumericLessThanEquals);
            case 'NumericGreaterThanEquals':
                return sfn.Condition.numberGreaterThanEquals(condition.variable, condition.NumericGreaterThanEquals);
            case 'BooleanEquals':
                return sfn.Condition.booleanEquals(condition.variable, condition.BooleanEquals);
            case 'IsPresent':
                return sfn.Condition.isPresent(condition.variable);
            case 'IsNotPresent':
                return sfn.Condition.isNotPresent(condition.variable);
            default:
                throw new Error(`Unsupported condition type`);
        }
    }
}
