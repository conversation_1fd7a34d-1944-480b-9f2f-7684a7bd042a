import { SagemakerHelpers } from './sagemaker-helpers';
import { ContainerImage } from '@aws-cdk/aws-sagemaker-alpha';

export const REGION_DICT: { [name: string]: string } = {
    'af-south-1': '626614931356',
    'ap-east-1': '871362719292',
    'ap-northeast-1': '763104351884',
    'ap-northeast-2': '763104351884',
    'ap-northeast-3': '364406365360',
    'ap-south-1': '763104351884',
    'ap-southeast-1': '763104351884',
    'ap-southeast-2': '763104351884',
    'ca-central-1': '763104351884',
    'cn-north-1': '727897471807',
    'cn-northwest-1': '727897471807',
    'eu-central-1': '763104351884',
    'eu-north-1': '763104351884',
    'eu-south-1': '692866216735',
    'eu-west-1': '763104351884',
    'eu-west-2': '763104351884',
    'eu-west-3': '763104351884',
    'me-south-1': '217643126080',
    'sa-east-1': '763104351884',
    'us-east-1': '763104351884',
    'us-east-2': '763104351884',
    'us-gov-west-1': '442386744353',
    'us-iso-east-1': '886529160074',
    'us-west-1': '763104351884',
    'us-west-2': '763104351884',
};

export class HuggingfaceHelpers {
    // Images can be found here:
    // https://github.com/aws/deep-learning-containers/blob/master/available_images.md
    public static getContainerImage(repositoryName: string, tag: string): ContainerImage {
        return ContainerImage.fromDlc(repositoryName, tag);
    }

    public static isGpuInstance(instance_type: string): boolean {
        return ['p', 'g'].includes(instance_type.split('.')[1][0].toLowerCase());
    }

    public static getEndpointName(modelName: string): string {
        return `hf-endpoint-${SagemakerHelpers.sanitizeName(modelName)}`;
    }

    public static getModelName(modelName: string): string {
        return `hf-model-${SagemakerHelpers.sanitizeName(modelName)}`;
    }

    public static getEndpointConfigName(modelName: string): string {
        return `hf-endpoint-config-${SagemakerHelpers.sanitizeName(modelName)}`;
    }
}
