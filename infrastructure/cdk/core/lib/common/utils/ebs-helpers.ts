import { EbsDeviceVolumeType } from 'aws-cdk-lib/aws-autoscaling';

export class EbsHelpers {
    public static convertToEbsDeviceVolumeType(value: string): EbsDeviceVolumeType {
        switch (value.toLowerCase()) {
            case EbsDeviceVolumeType.STANDARD:
                return EbsDeviceVolumeType.STANDARD;
            case EbsDeviceVolumeType.IO1:
                return EbsDeviceVolumeType.IO1;
            case EbsDeviceVolumeType.GP2:
                return EbsDeviceVolumeType.GP2;
            case EbsDeviceVolumeType.GP3:
                return EbsDeviceVolumeType.GP3;
            case EbsDeviceVolumeType.ST1:
                return EbsDeviceVolumeType.ST1;
            case EbsDeviceVolumeType.SC1:
                return EbsDeviceVolumeType.SC1;
            default:
                throw new Error(`Invalid EbsDeviceVolumeType string: ${value}`);
        }
    }
}
