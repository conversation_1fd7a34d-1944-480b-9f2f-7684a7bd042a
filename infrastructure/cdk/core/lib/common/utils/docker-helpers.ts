import { Platform } from 'aws-cdk-lib/aws-ecr-assets';
import { Construct } from 'constructs';
import { EnvironmentSecret } from '../env/config';

type dict = {
    [key: string]: string;
};

export class DockerHelpers {
    public static findDockerImagePlatform(platform: string): Platform {
        if (platform == Platform.LINUX_ARM64.platform) {
            return Platform.LINUX_ARM64;
        } else {
            return Platform.LINUX_AMD64;
        }
    }

    public static resolveBuildArguments(
        scope: Construct,
        buildArgs?: Record<string, string>,
        secretBuildArgs?: Array<EnvironmentSecret>
    ): dict {
        const resolvedSecretBuildArgs: dict = {};

        secretBuildArgs?.forEach((secretBuildArg) => {
            const envValue = process.env[secretBuildArg.secretName];
            if (envValue) {
                resolvedSecretBuildArgs[secretBuildArg.name] = envValue;
            } else {
                throw Error(`There is no environment variable secret defined with name: ${secretBuildArg.secretName}`);
            }
        });

        return {
            ...buildArgs,
            ...resolvedSecretBuildArgs,
        };
    }
}
