import * as batch from 'aws-cdk-lib/aws-batch';
import * as efs from 'aws-cdk-lib/aws-efs';
import { ECSVolume, EFSVolumeOptions } from '../batch/config';
import { EcsVolume } from 'aws-cdk-lib/aws-batch';

export class BatchHelpers {
    public static convertToEFSVolumeOptions(
        efsVolumeOptions: EFSVolumeOptions,
        fileSystem: efs.IFileSystem
    ): batch.EfsVolumeOptions {
        return {
            containerPath: efsVolumeOptions.containerPath,
            name: efsVolumeOptions.name,
            readonly: efsVolumeOptions.readonly,
            fileSystem: fileSystem,
            accessPointId: efsVolumeOptions.accessPointId,
            rootDirectory: efsVolumeOptions.rootDirectory,
            enableTransitEncryption: efsVolumeOptions.enableTransitEncryption,
            useJobRole: efsVolumeOptions.useJobRole,
        };
    }

    public static convertToECSVolume(ecsVolume: ECSVolume, fileSystem: efs.IFileSystem): batch.EcsVolume {
        const efsVolumeOptions = BatchHelpers.convertToEFSVolumeOptions(ecsVolume.efsVolumeOptions, fileSystem);
        return EcsVolume.efs(efsVolumeOptions);
    }
}
