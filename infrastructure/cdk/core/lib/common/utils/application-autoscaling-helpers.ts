import { CronOptions, Schedule } from '../autoscaling/config';
import * as autoscaling from 'aws-cdk-lib/aws-applicationautoscaling';

export class ApplicationAutoscalingHelpers {
    // App
    public static convertToCronOptions(cronOptions: CronOptions): autoscaling.CronOptions {
        return {
            minute: cronOptions.minute,
            hour: cronOptions.hour,
            day: cronOptions.day,
            month: cronOptions.day,
            weekDay: cronOptions.weekDay,
        };
    }

    public static convertToSchedule(schedule: Schedule): autoscaling.Schedule {
        if (schedule.cronExpression) {
            return autoscaling.Schedule.expression(schedule.cronExpression);
        } else if (schedule.cronOptions) {
            return autoscaling.Schedule.cron(ApplicationAutoscalingHelpers.convertToCronOptions(schedule.cronOptions));
        } else {
            throw new Error('There are no autoscaling schedule options provided');
        }
    }
}
