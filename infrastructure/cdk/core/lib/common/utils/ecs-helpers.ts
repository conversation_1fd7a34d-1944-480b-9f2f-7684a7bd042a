import * as ecs from 'aws-cdk-lib/aws-ecs';
import {
    AuthorizationConfig,
    CapacityProviderStrategy,
    Container,
    DockerVolumeConfiguration,
    EFSVolumeConfiguration,
    HealthCheck,
    MountPoint,
    PortMapping,
    Volume,
    VolumeHost,
} from '../ecs/config';
import { AmiHardwareType } from '../autoscaling/config';
import { AmiHelpers } from './ami-helpers';
import { Duration } from 'aws-cdk-lib';
import { EnumHelpers } from './enum-helpers';

export class ECSHelpers {
    public static convertToHealthCheck(healthCheck: HealthCheck): ecs.HealthCheck {
        return {
            command: ['CMD-SHELL', 'curl -f http://localhost/ || exit 1'],
            retries: healthCheck.retries,
            startPeriod: healthCheck.startPeriodMinutes ? Duration.minutes(healthCheck.startPeriodMinutes) : undefined,
        };
    }

    public static convertToECSPortMappings(portMappings: Array<PortMapping>): Array<ecs.PortMapping> {
        return portMappings.map((mapping) => {
            return {
                containerPort: mapping.containerPort,
                hostPort: mapping.hostPort,
            };
        });
    }

    public static convertToCapacityProviderStrategies(
        capacityProviderStrategies: Array<CapacityProviderStrategy>
    ): Array<ecs.CapacityProviderStrategy> {
        return capacityProviderStrategies.map((capacityProviderStrategy) => {
            return {
                capacityProvider: capacityProviderStrategy.capacityProvider,
                weight: capacityProviderStrategy.weight,
                base: capacityProviderStrategy.base,
            };
        });
    }

    public static convertToVolume(volume: Volume): ecs.Volume {
        const host = volume.host ? ECSHelpers.convertToHost(volume.host) : undefined;

        const dockerVolumeConfiguration = volume.dockerVolumeConfiguration
            ? ECSHelpers.convertToDockerVolumeConfiguration(volume.dockerVolumeConfiguration)
            : undefined;

        const efsVolumeConfiguration = volume.efsVolumeConfiguration
            ? ECSHelpers.convertToEFSVolumeConfiguration(volume.efsVolumeConfiguration)
            : undefined;

        return {
            name: volume.name,
            dockerVolumeConfiguration: dockerVolumeConfiguration,
            efsVolumeConfiguration: efsVolumeConfiguration,
            host: host,
            configuredAtLaunch: volume.configuredAtLaunch,
        };
    }

    public static convertToHost(host: VolumeHost): ecs.Host {
        return {
            sourcePath: host.sourcePath,
        };
    }

    public static convertToMountPoint(mountPoint: MountPoint): ecs.MountPoint {
        return {
            sourceVolume: mountPoint.sourceVolume,
            containerPath: mountPoint.containerPath,
            readOnly: mountPoint.readOnly,
        };
    }

    public static convertToDockerVolumeConfiguration(
        dockerVolumeConfiguration: DockerVolumeConfiguration
    ): ecs.DockerVolumeConfiguration {
        return {
            driver: dockerVolumeConfiguration.driver,
            driverOpts: dockerVolumeConfiguration.driverOpts,
            autoprovision: dockerVolumeConfiguration.autoprovision,
            scope: EnumHelpers.findEnumType(ecs.Scope, dockerVolumeConfiguration.scope),
        };
    }

    public static convertToAuthorizationConfig(authorizationConfig: AuthorizationConfig): ecs.AuthorizationConfig {
        return {
            accessPointId: authorizationConfig.accessPointId,
            iam: authorizationConfig.iam,
        };
    }

    public static convertToEFSVolumeConfiguration(
        efsVolumeConfiguration: EFSVolumeConfiguration
    ): ecs.EfsVolumeConfiguration {
        const authorizationConfig = efsVolumeConfiguration.authorizationConfig
            ? ECSHelpers.convertToAuthorizationConfig(efsVolumeConfiguration.authorizationConfig)
            : undefined;

        return {
            fileSystemId: efsVolumeConfiguration.fileSystemId,
            rootDirectory: efsVolumeConfiguration.rootDirectory,
            transitEncryption: efsVolumeConfiguration.transitEncryption,
            authorizationConfig: authorizationConfig,
        };
    }

    public static createContainerLinks(container: Container, containerDefinitions: Array<ecs.ContainerDefinition>) {
        if (!container.links) {
            return;
        }

        const sourceContainerDefinition = containerDefinitions.find(
            (containerDefinition) => containerDefinition.containerName === container.name
        );

        if (sourceContainerDefinition) {
            container.links.forEach((link) => {
                const otherContainerDefinition = containerDefinitions.find(
                    (containerDefinition) => containerDefinition.containerName === link.containerName
                );
                if (otherContainerDefinition) {
                    sourceContainerDefinition.addLink(otherContainerDefinition, link.alias);
                }
            });
        }
    }

    public static convertToMachineImage(amiHardwareType: AmiHardwareType): ecs.EcsOptimizedImage {
        return ecs.EcsOptimizedImage.amazonLinux2(AmiHelpers.findAmiType(amiHardwareType), {});
    }
}
