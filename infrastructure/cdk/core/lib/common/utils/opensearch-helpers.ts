import * as opensearch from 'aws-cdk-lib/aws-opensearchservice';
import { EbsOptions, RemovalPolicy, ZoneAwareness } from '../opensearch/config';
import { EbsHelpers } from './ebs-helpers';
import * as core from 'aws-cdk-lib';
import { EnumHelpers } from './enum-helpers';

export class OpenSearchHelpers {
    public static convertToEbsOptions(ebsOptions: EbsOptions): opensearch.EbsOptions {
        return {
            volumeSize: ebsOptions.sizeInGB,
            volumeType: ebsOptions.volumeType
                ? EbsHelpers.convertToEbsDeviceVolumeType(ebsOptions.volumeType)
                : undefined,
        };
    }

    public static convertToRemovalPolicy(removalPolicy: RemovalPolicy): core.RemovalPolicy {
        return EnumHelpers.findEnumType(core.RemovalPolicy, removalPolicy);
    }

    public static convertToZoneAwarenessConfig(zoneAwareness: ZoneAwareness): opensearch.ZoneAwarenessConfig {
        return {
            enabled: zoneAwareness.enabled,
            availabilityZoneCount: zoneAwareness.availabilityZoneCount,
        };
    }
}
