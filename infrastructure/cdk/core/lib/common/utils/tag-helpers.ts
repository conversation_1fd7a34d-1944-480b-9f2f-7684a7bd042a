import { Tags } from 'aws-cdk-lib';
import { IConstruct } from 'constructs';
import { Tag, TagProperties } from '../tag/config';

export class TagHelpers {
    public static addTagsToResources(tags: { [key: string]: string }, ...resources: IConstruct[]): void {
        resources.forEach((resource) => {
            Object.entries(tags).forEach(([key, value]) => {
                Tags.of(resource).add(key, value);
            });
        });
    }

    public static addTypedTagsToResources(tags: Tag[], ...resources: IConstruct[]): void {
        resources.forEach((resource) => {
            tags.forEach((tag) => {
                if (tag.properties) {
                    Tags.of(resource).add(tag.name, tag.value, TagHelpers.convertToDictionary(tag.properties));
                } else {
                    Tags.of(resource).add(tag.name, tag.value);
                }
            });
        });
    }

    public static convertToDictionary(tagProperties: TagProperties): { [key: string]: boolean | string | number } {
        const dictionary: { [key: string]: boolean | string | number } = {};

        if (tagProperties.priority) {
            dictionary['priority'] = tagProperties.priority;
        }
        if (tagProperties.applyToLaunchedInstances) {
            dictionary['applyToLaunchedInstances'] = tagProperties.applyToLaunchedInstances;
        }

        return dictionary;
    }
}
