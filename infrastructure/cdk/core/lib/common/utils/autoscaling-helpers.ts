import * as autoscaling from 'aws-cdk-lib/aws-autoscaling';
import {
    CronOptions,
    LaunchTemplateOverride,
    OnDemandAllocationStrategy,
    ScalingInterval,
    SpotAllocationStrategy,
    AdjustmentType,
    HealthChecks,
    Schedule,
} from '../autoscaling/config';
import { EnumHelpers } from './enum-helpers';
import { EC2Helpers } from './ec2-helpers';
import { Duration } from 'aws-cdk-lib';

export class AutoScalingHelpers {
    public static convertToSpotAllocationStrategy(
        spotAllocationStrategy: SpotAllocationStrategy
    ): autoscaling.SpotAllocationStrategy {
        return EnumHelpers.findEnumType(autoscaling.SpotAllocationStrategy, spotAllocationStrategy);
    }

    public static convertToOnDemandAllocationStrategy(
        onDemandAllocationStrategy: OnDemandAllocationStrategy
    ): autoscaling.OnDemandAllocationStrategy {
        return EnumHelpers.findEnumType(autoscaling.OnDemandAllocationStrategy, onDemandAllocationStrategy);
    }

    public static convertToEC2HealthCheck(healthCheck: HealthChecks): autoscaling.HealthChecks {
        return autoscaling.HealthChecks.ec2({
            gracePeriod: Duration.minutes(healthCheck.graceInMinutes),
        });
    }

    public static convertToLaunchTemplateOverrides(
        launchTemplateOverrides: Array<LaunchTemplateOverride>
    ): Array<autoscaling.LaunchTemplateOverrides> {
        return launchTemplateOverrides.map((launchTemplateOverride) => ({
            instanceType: EC2Helpers.findInstanceType(launchTemplateOverride.instanceType),
        }));
    }

    public static convertToScalingIntervals(
        scalingIntervals: Array<ScalingInterval>
    ): Array<autoscaling.ScalingInterval> {
        return scalingIntervals.map((scalingInterval) => {
            return {
                lower: scalingInterval.lower,
                upper: scalingInterval.upper,
                change: scalingInterval.change,
            };
        });
    }

    public static convertToAdjustmentType(adjustmentType: AdjustmentType): autoscaling.AdjustmentType {
        return EnumHelpers.findEnumType(autoscaling.AdjustmentType, adjustmentType);
    }

    public static convertToCronOptions(cronOptions: CronOptions): autoscaling.CronOptions {
        return {
            minute: cronOptions.minute,
            hour: cronOptions.hour,
            day: cronOptions.day,
            month: cronOptions.day,
            weekDay: cronOptions.weekDay,
        };
    }

    public static convertToSchedule(schedule: Schedule): autoscaling.Schedule {
        if (schedule.cronExpression) {
            return autoscaling.Schedule.expression(schedule.cronExpression);
        } else if (schedule.cronOptions) {
            return autoscaling.Schedule.cron(AutoScalingHelpers.convertToCronOptions(schedule.cronOptions));
        } else {
            throw new Error('There are no autoscaling schedule options provided');
        }
    }
}
