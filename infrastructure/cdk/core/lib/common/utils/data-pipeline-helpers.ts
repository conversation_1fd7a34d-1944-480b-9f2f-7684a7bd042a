import * as sfn from 'aws-cdk-lib/aws-stepfunctions';

import { DataPipelineEnvironment } from '../../data-pipelines/config';

export class DataPipelineHelpers {
    public static getEnvironment(environments: DataPipelineEnvironment[]): { [key: string]: string } {
        const result: { [key: string]: string } = {};

        environments.forEach((env) => {
            if (env.type === 'Literal') {
                result[env.name] = env.value;
            } else if (env.type === 'JsonPath') {
                // Assuming sfn.JsonPath is available in your environment
                // Modify this part based on the actual implementation of sfn.JsonPath
                result[env.name] = sfn.JsonPath.stringAt(env.value);
            }
        });

        return result;
    }
}
