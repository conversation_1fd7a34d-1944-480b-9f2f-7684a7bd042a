import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import { CloudWatchMetric } from '../cloudwatch/config';
import { Duration } from 'aws-cdk-lib';

export class CloudWatchHelpers {
    public static convertToCloudWatchMetric(cloudWatchMetric: CloudWatchMetric): cloudwatch.IMetric {
        return new cloudwatch.Metric({
            namespace: cloudWatchMetric.namespace,
            metricName: cloudWatchMetric.metricName,
            dimensionsMap: cloudWatchMetric.dimensionsMap,
            period: Duration.seconds(cloudWatchMetric.periodInSeconds),
            statistic: cloudWatchMetric.statistic,
        });
    }
}
