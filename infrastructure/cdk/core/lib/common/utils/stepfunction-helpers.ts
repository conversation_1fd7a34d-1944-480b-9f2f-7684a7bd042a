import { State } from 'aws-cdk-lib/aws-stepfunctions';
import * as sfn from 'aws-cdk-lib/aws-stepfunctions';

export class StepfunctionHelpers {
    public static printStepFunctions(startState: State) {
        const stateGraph = new sfn.StateGraph(startState, 'Temporary graph to render to JSON');
        const jsonGraph = stateGraph.toGraphJson();
        console.log(JSON.stringify(jsonGraph, null, 2));
    }
}
