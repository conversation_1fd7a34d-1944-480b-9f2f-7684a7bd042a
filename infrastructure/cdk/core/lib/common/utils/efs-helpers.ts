import * as efs from 'aws-cdk-lib/aws-efs';
import { EnumHelpers } from './enum-helpers';
import {
    Acl,
    LifecyclePolicy,
    PerformanceMode,
    PosixUser,
    RemovalPolicy,
    ThroughputMode,
    OutOfInfrequentAccessPolicy,
} from '../efs/config';
import * as core from 'aws-cdk-lib';

export class EFSHelpers {
    public static convertToPerformanceMode(performanceMode: PerformanceMode): efs.PerformanceMode {
        return EnumHelpers.findEnumType(efs.PerformanceMode, performanceMode);
    }

    public static convertToRemovalPolicy(removalPolicy: RemovalPolicy): core.RemovalPolicy {
        return EnumHelpers.findEnumType(core.RemovalPolicy, removalPolicy);
    }

    public static convertToLifecyclePolicy(lifecyclePolicy: LifecyclePolicy): efs.LifecyclePolicy {
        return EnumHelpers.findEnumType(efs.LifecyclePolicy, lifecyclePolicy);
    }

    public static convertToOutOfInfrequentAccessPolicy(
        outOfInfrequentAccessPolicy: OutOfInfrequentAccessPolicy
    ): efs.OutOfInfrequentAccessPolicy {
        return EnumHelpers.findEnumType(efs.OutOfInfrequentAccessPolicy, outOfInfrequentAccessPolicy);
    }

    public static convertToThroughputMode(throughputMode: ThroughputMode): efs.ThroughputMode {
        return EnumHelpers.findEnumType(efs.ThroughputMode, throughputMode);
    }

    public static convertToAcl(acl: Acl): efs.Acl {
        return {
            ownerGid: acl.ownerGid,
            ownerUid: acl.ownerUid,
            permissions: acl.permissions,
        };
    }

    public static convertToPosixUser(posixUser: PosixUser): efs.PosixUser {
        return {
            gid: posixUser.gid,
            uid: posixUser.uid,
        };
    }
}
