export class IamHelpers {
    static IAM_ROLE_NAME_MAX_LENGTH = 64;

    public static getRoleName(region: string, ...components: string[]): string {
        let componentsStr = components.reduce(
            (accumulator, currentValue) => accumulator + currentValue.substring(0, 31),
            ''
        );

        const maxLength = IamHelpers.IAM_ROLE_NAME_MAX_LENGTH - region.length;
        if (componentsStr.length > maxLength) {
            componentsStr = componentsStr.substring(0, maxLength);
        }
        return componentsStr + region;
    }
}
