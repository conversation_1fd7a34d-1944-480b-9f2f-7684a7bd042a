import { SagemakerHelpers } from './sagemaker-helpers';

export class MachinelearningHelpers {
    public static getEndpointName(modelName: string): string {
        return `ml-endpoint-${SagemakerHelpers.sanitizeName(modelName)}`;
    }

    public static getModelName(modelName: string): string {
        return `ml-model-${SagemakerHelpers.sanitizeName(modelName)}`;
    }

    public static getEndpointConfigName(modelName: string): string {
        return `ml-endpoint-config-${SagemakerHelpers.sanitizeName(modelName)}`;
    }
}
