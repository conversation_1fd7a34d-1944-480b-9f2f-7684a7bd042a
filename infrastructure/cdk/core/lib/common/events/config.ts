import { JSONObject, required, optional } from 'ts-json-object';

export class CronOptions extends JSONObject {
    @optional(undefined)
    minute?: string;
    @optional(undefined)
    hour?: string;
    @optional(undefined)
    day?: string;
    @optional(undefined)
    month?: string;
    @optional(undefined)
    weekDay?: string;
}

export class Schedule extends JSON<PERSON>bject {
    @optional(undefined)
    scheduleRateMinutes?: number;
    @optional(undefined)
    scheduleRateHours?: number;
    @optional(undefined)
    cronOptions?: CronOptions;
}

export class EventBridgeRule extends J<PERSON><PERSON>Object {
    @required
    name: string;
    @required
    schedule: Schedule;
}
