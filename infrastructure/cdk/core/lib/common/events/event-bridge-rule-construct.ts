import { Construct } from 'constructs';
import * as events from 'aws-cdk-lib/aws-events';
import { EventBridgeRule } from './config';
import { EventHelpers } from '../utils/event-helpers';

interface EventBridgeRuleProps {
    eventBridgeRule: EventBridgeRule;
    ruleTargets: Array<events.IRuleTarget>;
}

export class EventBridgeRuleConstruct extends Construct {
    public readonly rule: events.IRule;

    constructor(scope: Construct, id: string, props: EventBridgeRuleProps) {
        super(scope, id);

        this.rule = new events.Rule(this, props.eventBridgeRule.name, {
            ruleName: props.eventBridgeRule.name,
            schedule: EventHelpers.convertToSchedule(props.eventBridgeRule.schedule),
            targets: props.ruleTargets,
        });
    }
}
