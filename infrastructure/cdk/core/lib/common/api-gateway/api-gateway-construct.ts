import * as apigw from 'aws-cdk-lib/aws-apigateway';
import { RestApi } from 'aws-cdk-lib/aws-apigateway';
import { Construct } from 'constructs';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as targets from 'aws-cdk-lib/aws-route53-targets';
import { ApiGateway } from './config';
import { AcmStack } from '../../acm/acm-stack';
import { Dns } from '../dns/config';

export interface ApiGatewayProps {
    apiGateway: ApiGateway;
    acmStack: AcmStack;
    dns: Dns;
}

export class ApiGatewayConstruct extends Construct {
    public readonly apiGateway: RestApi;

    constructor(scope: Construct, id: string, props: ApiGatewayProps) {
        super(scope, id);

        const acmCertificate = props.acmStack.certs.get(props.apiGateway.apiCertName);
        if (acmCertificate == undefined) {
            throw new Error(`failed to retrieve ${props.dns.route53HostedZoneName} cert from acm stack`);
        }

        let deployOptions: apigw.StageOptions | undefined;
        if (props.apiGateway.enableLogs) {
            deployOptions = {
                dataTraceEnabled: true,
                metricsEnabled: true,
            };
        }
        const apiGateway = new apigw.RestApi(this, props.apiGateway.name, {
            restApiName: props.apiGateway.restApiName,
            domainName: {
                domainName: props.apiGateway.apiCertName,
                certificate: acmCertificate,
            },
            deployOptions: deployOptions,
        });

        const envRootHostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'EnvRootHostedZone', {
            zoneName: props.dns.route53HostedZoneName,
            hostedZoneId: props.dns.route53HostedZoneID,
        });

        new route53.ARecord(this, 'AliasRecord', {
            recordName: props.apiGateway.apiCertName,
            zone: envRootHostedZone,
            target: route53.RecordTarget.fromAlias(new targets.ApiGateway(apiGateway)),
        });

        this.apiGateway = apiGateway;
    }
}
