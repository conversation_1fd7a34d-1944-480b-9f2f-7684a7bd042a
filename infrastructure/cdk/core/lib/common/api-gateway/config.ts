import { JSONObject, optional, required } from 'ts-json-object';

export class <PERSON>pi<PERSON>ateway extends JSONObject {
    @required
    name: string;
    @required
    apiCertName: string;
    @required
    restApiName: string;
    @optional(false)
    enableLogs: boolean;
}

export class ApiGatewayEndpoint extends J<PERSON><PERSON>Object {
    @required
    name: string;
    @required
    path: string;
    @optional(undefined)
    allowedOrigins: Array<string>;
    @optional(undefined)
    allowedMethods: Array<string>;
}
