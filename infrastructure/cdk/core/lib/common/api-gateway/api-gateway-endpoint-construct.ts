import * as apigw from 'aws-cdk-lib/aws-apigateway';
import { Cors, RestApi } from 'aws-cdk-lib/aws-apigateway';
import { Construct } from 'constructs';
import { ApiGatewayEndpoint } from './config';

export interface ApiGatewayEndpointProps {
    apiGateway: RestApi;
    apiGatewayEndpoint: ApiGatewayEndpoint;
}

export class ApiGatewayEndpointConstruct extends Construct {
    public readonly apiResource: apigw.IResource;

    constructor(scope: Construct, id: string, props: ApiGatewayEndpointProps) {
        super(scope, id);

        const segments = props.apiGatewayEndpoint.path.split('/').filter(Boolean);

        let resource = props.apiGateway.root;

        for (const segment of segments) {
            resource = props.apiGateway.root.getResource(segment) ?? resource.addResource(segment);
        }

        resource.addCorsPreflight({
            allowOrigins: props.apiGatewayEndpoint.allowedOrigins ?? Cors.ALL_ORIGINS,
            allowMethods: props.apiGatewayEndpoint.allowedMethods ?? ['POST'],
        });

        this.apiResource = resource;
    }
}
