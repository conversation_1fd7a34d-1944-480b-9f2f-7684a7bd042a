import { Construct } from 'constructs';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as s3Deployment from 'aws-cdk-lib/aws-s3-deployment';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as glue from 'aws-cdk-lib/aws-glue';
import { IamHelpers } from '../utils/iam-helpers';

export interface MLOpsPartsRoleProps {
    name: string;
    assetS3Bucket: s3.Bucket;
    timeoutInMin: number;
    etlScriptFileName: string;
    etlScriptFilePath: string;
    region: string;
    etlScriptDestinationS3KeyPrefix: string;
    numberOfWorkers: number;
    maxConcurrentRuns: number;
}

export class GlueJobConstruct extends Construct {
    public readonly role: iam.Role;
    public readonly job: glue.CfnJob;

    constructor(scope: Construct, id: string, props: MLOpsPartsRoleProps) {
        super(scope, id);

        const roleName = IamHelpers.getRoleName(props.region, props.name);

        this.role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new iam.ServicePrincipal('glue.amazonaws.com'),
        });

        this.role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:ListBucket', 's3:*Object'],
                resources: ['*'],
            })
        );

        this.role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                resources: ['*'],
            })
        );

        new s3Deployment.BucketDeployment(this, 'etl-scripts-deployment', {
            sources: [s3Deployment.Source.asset(props.etlScriptFilePath)],
            destinationBucket: props.assetS3Bucket,
            destinationKeyPrefix: props.etlScriptDestinationS3KeyPrefix,
        });

        this.job = new glue.CfnJob(this, props.name, {
            name: `${props.name}`,
            command: {
                name: 'glueetl',
                scriptLocation: `s3://${props.assetS3Bucket.bucketName}/${props.etlScriptDestinationS3KeyPrefix}/${props.etlScriptFileName}`,
                pythonVersion: '3',
            },
            role: this.role.roleArn,
            executionProperty: {
                maxConcurrentRuns: props.maxConcurrentRuns,
            },
            defaultArguments: {
                '--job-language': 'python',
            },
            glueVersion: '2.0',
            workerType: 'Standard',
            numberOfWorkers: props.numberOfWorkers,
            timeout: props.timeoutInMin,
        });
    }
}
