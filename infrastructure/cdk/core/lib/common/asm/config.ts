import { JSONObject, optional, required } from 'ts-json-object';

/**
 *  This class is used to generate generic secrets
 *  that might be used for third parties etc.
 */
export class AwsSecret extends JSONObject {
    @required
    secretName: string;
    @optional(undefined)
    excludeCharacters: string;
    @optional(false)
    excludePunctuation: boolean;
    @required
    passwordLength: number;
    @optional(false)
    requireEachIncludedType: boolean;
}
