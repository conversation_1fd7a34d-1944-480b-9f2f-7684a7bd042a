import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import { Construct } from 'constructs';
import { EC2Service, ECSTaskAutoScaling } from './config';
import * as autoscaling from 'aws-cdk-lib/aws-autoscaling';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import { SubnetSelection, Vpc } from 'aws-cdk-lib/aws-ec2';
import { ECSClusterConstruct } from './ecs-cluster-construct';
import { TaskDefinitionConstruct } from './task-definition-construct';
import { ECSHelpers } from '../utils/ecs-helpers';
import { AutoScalingConstruct } from '../autoscaling/autoscaling-construct';
import { Duration, TimeZone } from 'aws-cdk-lib';
import { CloudWatchHelpers } from '../utils/cloudwatch-helpers';
import { ApplicationTargetGroup } from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import { AutoScalingHelpers } from '../utils/autoscaling-helpers';
import { ApplicationAutoscalingHelpers } from '../utils/application-autoscaling-helpers';

export interface EC2ServiceConstructProps {
    vpc: Vpc;
    vpcSubnets?: SubnetSelection;
    ec2Service: EC2Service;
}

export class EC2ServiceConstruct extends Construct {
    readonly ec2Service: ecs.Ec2Service;
    readonly cluster: ecs.Cluster;
    readonly autoScalingGroup: autoscaling.AutoScalingGroup;
    readonly autoScalingConstruct: AutoScalingConstruct;
    readonly asgCapacityProvider: ecs.AsgCapacityProvider;

    private readonly props: EC2ServiceConstructProps;
    private readonly scalableTaskCount?: ecs.ScalableTaskCount;

    constructor(scope: Construct, id: string, props: EC2ServiceConstructProps) {
        super(scope, id);

        const cluster = new ECSClusterConstruct(this, `${props.ec2Service.name}Cluster`, {
            vpc: props.vpc,
            vpcSubnets: props.vpcSubnets,
            ecsCluster: props.ec2Service.cluster,
        });

        const taskDefinition = new TaskDefinitionConstruct(this, `${props.ec2Service.name}TaskDefinition`, {
            taskDefinition: props.ec2Service.taskDefinition,
        });

        const capacityProviderStrategies = props.ec2Service.capacityProviderStrategies
            ? ECSHelpers.convertToCapacityProviderStrategies(props.ec2Service.capacityProviderStrategies)
            : undefined;

        const healthCheckGracePeriod = props.ec2Service.healthCheckGracePeriodInMinutes
            ? Duration.minutes(props.ec2Service.healthCheckGracePeriodInMinutes)
            : undefined;

        const ec2Service = new ecs.Ec2Service(this, `${props.ec2Service.name}Ec2Service`, {
            serviceName: props.ec2Service.name,
            cluster: cluster.cluster,
            taskDefinition: taskDefinition.taskDefinition,
            capacityProviderStrategies: capacityProviderStrategies,
            healthCheckGracePeriod: healthCheckGracePeriod,
            minHealthyPercent: props.ec2Service.minHealthyPercent,
            maxHealthyPercent: props.ec2Service.maxHealthyPercent,
        });
        ec2Service.node.addDependency(cluster);

        if (props.ec2Service.taskAutoScaling) {
            this.scalableTaskCount = this.addScalingTargets(props, ec2Service, props.ec2Service.taskAutoScaling);
        }

        // Create Drata CPU alarm for ECS service
        this.createDrataAlarms(props, cluster.cluster.clusterName);

        this.ec2Service = ec2Service;
        this.cluster = cluster.cluster;
        this.autoScalingGroup = cluster.autoScalingGroup;
        this.autoScalingConstruct = cluster.autoScalingConstruct;
        this.asgCapacityProvider = cluster.asgCapacityProvider;
        this.props = props;
    }

    private addScalingTargets(
        props: EC2ServiceConstructProps,
        ec2Service: ecs.Ec2Service,
        taskAutoScaling: ECSTaskAutoScaling
    ): ecs.ScalableTaskCount {
        const autoScaleTaskCount = ec2Service.autoScaleTaskCount({
            minCapacity: taskAutoScaling.minCapacity,
            maxCapacity: taskAutoScaling.maxCapacity,
        });

        if (taskAutoScaling.stepMetricScales) {
            taskAutoScaling.stepMetricScales.forEach((stepMetricScale) => {
                const cloudWatchMetric = CloudWatchHelpers.convertToCloudWatchMetric(stepMetricScale.cloudWatchMetric);
                const scalingIntervals = AutoScalingHelpers.convertToScalingIntervals(stepMetricScale.scalingSteps);
                const adjustmentType = stepMetricScale.adjustmentType
                    ? AutoScalingHelpers.convertToAdjustmentType(stepMetricScale.adjustmentType)
                    : undefined;
                autoScaleTaskCount.scaleOnMetric(`${props.ec2Service.name}${stepMetricScale.name}`, {
                    metric: cloudWatchMetric,
                    adjustmentType: adjustmentType,
                    scalingSteps: scalingIntervals,
                    datapointsToAlarm: stepMetricScale.datapointsToAlarm,
                    evaluationPeriods: stepMetricScale.evaluationPeriods,
                    cooldown: stepMetricScale.cooldownInSeconds
                        ? Duration.seconds(stepMetricScale.cooldownInSeconds)
                        : undefined,
                });
            });
        }

        if (taskAutoScaling.scheduleScales) {
            taskAutoScaling.scheduleScales.forEach((scheduleScale) => {
                const schedule = ApplicationAutoscalingHelpers.convertToSchedule(scheduleScale.schedule);
                autoScaleTaskCount.scaleOnSchedule(`${props.ec2Service.name}${scheduleScale.name}`, {
                    schedule: schedule,
                    minCapacity: scheduleScale.minCapacity,
                    maxCapacity: scheduleScale.maxCapacity,
                    timeZone: TimeZone.of(scheduleScale.timeZone),
                });
            });
        }

        if (taskAutoScaling.cpuScale) {
            autoScaleTaskCount.scaleOnCpuUtilization(`${taskAutoScaling.cpuScale.name}CPUScaling`, {
                targetUtilizationPercent: taskAutoScaling.cpuScale.targetUtilizationPercent,
                scaleInCooldown: taskAutoScaling.cpuScale.cooldownInSeconds
                    ? Duration.seconds(taskAutoScaling.cpuScale.cooldownInSeconds)
                    : undefined,
                scaleOutCooldown: taskAutoScaling.cpuScale.cooldownOutSeconds
                    ? Duration.seconds(taskAutoScaling.cpuScale.cooldownOutSeconds)
                    : undefined,
            });
        }

        if (taskAutoScaling.trackMetricScales) {
            taskAutoScaling.trackMetricScales.forEach((trackMetricScale) => {
                const cloudWatchMetric = CloudWatchHelpers.convertToCloudWatchMetric(trackMetricScale.cloudWatchMetric);

                autoScaleTaskCount.scaleToTrackCustomMetric(`${props.ec2Service.name}${trackMetricScale.name}`, {
                    metric: cloudWatchMetric,
                    targetValue: trackMetricScale.targetValue,
                    scaleInCooldown: trackMetricScale.cooldownInSeconds
                        ? Duration.seconds(trackMetricScale.cooldownInSeconds)
                        : undefined,
                    scaleOutCooldown: trackMetricScale.cooldownOutSeconds
                        ? Duration.seconds(trackMetricScale.cooldownOutSeconds)
                        : undefined,
                });
            });
        }

        return autoScaleTaskCount;
    }

    // These targets must only be applied after associating the load balancer with an auto scaler.
    public addLoadBalancedScalingTargets(targetGroup: ApplicationTargetGroup) {
        if (!this.scalableTaskCount) {
            return;
        }

        const taskAutoScaling = this.props.ec2Service.taskAutoScaling;
        if (taskAutoScaling && taskAutoScaling.requestScale) {
            this.scalableTaskCount.scaleOnRequestCount(`${this.props.ec2Service.name}RequestScaling`, {
                targetGroup: targetGroup,
                requestsPerTarget: taskAutoScaling.requestScale.targetRequestsPerMinute,
                scaleInCooldown: taskAutoScaling.requestScale.cooldownInSeconds
                    ? Duration.seconds(taskAutoScaling.requestScale.cooldownInSeconds)
                    : undefined,
                scaleOutCooldown: taskAutoScaling.requestScale.cooldownOutSeconds
                    ? Duration.seconds(taskAutoScaling.requestScale.cooldownOutSeconds)
                    : undefined,
            });
        }
    }

    private createDrataAlarms(props: EC2ServiceConstructProps, clusterName: string) {
        if (!props.ec2Service.cluster.asgCapacityProvider.autoScalingGroup.notificationEmail) {
            return;
        }

        const snsTopic = new sns.Topic(this, `${props.ec2Service.name}-ECS-SVC-Alarms`, {
            displayName: `Topic to deliver alarms related to ECS ${props.ec2Service.name} service`,
        });

        snsTopic.addSubscription(
            new subscriptions.EmailSubscription(
                props.ec2Service.cluster.asgCapacityProvider.autoScalingGroup.notificationEmail
            )
        );

        const alarm = new cloudwatch.Alarm(this, `${props.ec2Service.name}-ECS-SVC-CPUUtilization`, {
            alarmName: `${props.ec2Service.name}ECSServiceCPUUtilization`,
            alarmDescription: `${props.ec2Service.name} ECS service high CPU`,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            threshold: 80,
            evaluationPeriods: 2,
            metric: new cloudwatch.Metric({
                metricName: 'CPUUtilization',
                namespace: 'AWS/ECS',
                dimensionsMap: {
                    ClusterName: clusterName,
                    ServiceName: props.ec2Service.name,
                },
            }),
            actionsEnabled: true,
        });
        alarm.addAlarmAction(new cw_actions.SnsAction(snsTopic));
    }
}
