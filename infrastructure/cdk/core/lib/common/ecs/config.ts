import { array, JSONObject, optional, required } from 'ts-json-object';
import { AutoScalingGroup, ScalingInterval, AdjustmentType, Schedule } from '../autoscaling/config';
import { DockerImageAsset } from '../docker/config';
import { CloudWatchMetric } from '../cloudwatch/config';

export type DockerVolumeConfigurationScope = 'TASK' | 'SHARED';
export type EFSVolumeConfigurationAuthorizationConfigIAM = 'ENABLED' | 'DISABLED';
export type EFSVolumeConfigurationTransitEncryption = 'ENABLED' | 'DISABLED';

export class PortMapping extends JSONObject {
    @required
    containerPort: number;
    @optional(undefined)
    hostPort?: number;
}

export class DockerVolumeConfiguration extends JSONObject {
    @required
    driver: string;
    @optional('TASK')
    scope: DockerVolumeConfigurationScope;
    @optional(undefined)
    autoprovision?: boolean;
    @optional(undefined)
    driverOpts: any;
}

export class AuthorizationConfig extends JSONObject {
    @optional(undefined)
    accessPointId?: string;
    @optional(undefined)
    iam?: EFSVolumeConfigurationAuthorizationConfigIAM;
}

export class EFSVolumeConfiguration extends JSONObject {
    @required
    fileSystemId: string;
    @optional(undefined)
    rootDirectory?: string;
    @optional(undefined)
    transitEncryption?: EFSVolumeConfigurationTransitEncryption;
    @optional(undefined)
    authorizationConfig: AuthorizationConfig;
}

export class VolumeHost extends JSONObject {
    @required
    sourcePath: string;
}

export class Volume extends JSONObject {
    @required
    name: string;
    @optional(undefined)
    host?: VolumeHost;
    @optional(undefined)
    dockerVolumeConfiguration?: DockerVolumeConfiguration;
    @optional(undefined)
    efsVolumeConfiguration?: EFSVolumeConfiguration;
    @optional(undefined)
    configuredAtLaunch?: boolean;
}

export class MountPoint extends JSONObject {
    @required
    containerPath: string;
    @required
    sourceVolume: string;
    @optional(false)
    readOnly: boolean;
}

export class ContainerLink extends JSONObject {
    @required
    containerName: string;
    @required
    alias: string;
}

export class HealthCheck extends JSONObject {
    @optional(undefined)
    startPeriodMinutes?: number;
    @optional(undefined)
    retries?: number;
}

export class LinuxParameters extends JSONObject {
    @optional(undefined)
    sharedMemorySizeInMB?: number;
}

export class Container extends JSONObject {
    @required
    name: string;
    @required
    cpu: number;
    @optional(0)
    gpuCount: number;
    @optional(2)
    memoryLimitGB: number;
    @optional(undefined)
    commands?: Array<string>;
    @required
    dockerImage: DockerImageAsset;
    @optional([])
    @array(PortMapping)
    portMappings?: Array<PortMapping>;
    @optional(undefined)
    /* eslint-disable @typescript-eslint/no-explicit-any */
    environment: any;
    @optional(undefined)
    essential?: boolean;
    @optional(undefined)
    @array(ContainerLink)
    links?: Array<ContainerLink>;
    @optional(undefined)
    linuxParameters?: LinuxParameters;
    @optional(undefined)
    healthCheck?: HealthCheck;
    @optional(undefined)
    @array(MountPoint)
    mountPoints?: Array<MountPoint>;
}

export class TaskDefinition extends JSONObject {
    @required
    name: string;
    @required
    @array(Container)
    containers: Array<Container>;
    @optional(undefined)
    @array(Volume)
    volumes?: Array<Volume>;
}

export class AsgCapacityProvider extends JSONObject {
    @required
    name: string;
    @required
    autoScalingGroup: AutoScalingGroup;
    @optional(true)
    enableManagedScaling?: boolean;
    @optional(true)
    enableManagedDraining?: boolean;
    @optional(true)
    canContainersAccessInstanceRole?: boolean;
    @optional(true)
    spotInstanceDraining?: boolean;
    @optional(undefined)
    maximumScalingStepSize?: number;
    @optional(undefined)
    minimumScalingStepSize?: number;
    @optional(undefined)
    targetCapacityPercent?: number;
    @optional(undefined)
    instanceWarmupPeriod?: number;
}

export class ECSCluster extends JSONObject {
    @required
    name: string;
    @required
    asgCapacityProvider: AsgCapacityProvider;
}

export class CapacityProviderStrategy extends JSONObject {
    @required
    capacityProvider: string;
    @required
    weight: number;
    @optional(undefined)
    base?: number;
}

export class ECSTaskAutoScalingSchedule extends JSONObject {
    @required
    name: string;
    @required
    schedule: Schedule;
    @optional(undefined)
    minCapacity?: number;
    @optional(undefined)
    maxCapacity?: number;
    @optional('America/Vancouver')
    timeZone: string;
}

export class BaseECSTaskAutoScaling extends JSONObject {
    @optional(undefined)
    cooldownInSeconds?: number;
    @optional(undefined)
    cooldownOutSeconds?: number;
}

export class ECSAutoSScalingStepMetric extends BaseECSTaskAutoScaling {
    @required
    name: string;
    @required
    cloudWatchMetric: CloudWatchMetric;
    @required
    @array(ScalingInterval)
    scalingSteps: Array<ScalingInterval>;
    @optional(undefined)
    datapointsToAlarm?: number;
    @optional(undefined)
    evaluationPeriods?: number;
    @optional(undefined)
    adjustmentType: AdjustmentType;
}

export class ECSTaskAutoScalingCPU extends BaseECSTaskAutoScaling {
    @required
    name: string;
    @required
    targetUtilizationPercent: number;
}

export class ECSTaskAutoScalingRequest extends BaseECSTaskAutoScaling {
    @required
    name: string;
    @required
    targetRequestsPerMinute: number;
}

export class ECSTaskAutoScalingTrackMetric extends BaseECSTaskAutoScaling {
    @required
    name: string;
    @required
    cloudWatchMetric: CloudWatchMetric;
    @required
    targetValue: number;
}

export class ECSTaskAutoScaling extends JSONObject {
    @optional(undefined)
    minCapacity?: number;
    @required
    maxCapacity: number;
    @optional(undefined)
    cpuScale?: ECSTaskAutoScalingCPU;
    @optional(undefined)
    requestScale?: ECSTaskAutoScalingRequest;
    @optional(undefined)
    @array(ECSTaskAutoScalingTrackMetric)
    trackMetricScales?: Array<ECSTaskAutoScalingTrackMetric>;
    @optional(undefined)
    @array(ECSAutoSScalingStepMetric)
    stepMetricScales?: Array<ECSAutoSScalingStepMetric>;
    @optional(undefined)
    @array(ECSTaskAutoScalingSchedule)
    scheduleScales?: Array<ECSTaskAutoScalingSchedule>;
}

export class EC2Service extends JSONObject {
    @required
    name: string;
    @required
    cluster: ECSCluster;
    @required
    taskDefinition: TaskDefinition;
    @optional(undefined)
    @array(CapacityProviderStrategy)
    capacityProviderStrategies?: Array<CapacityProviderStrategy>;
    @optional(undefined)
    taskAutoScaling?: ECSTaskAutoScaling;
    @optional(undefined)
    healthCheckGracePeriodInMinutes?: number;
    @optional(undefined)
    minHealthyPercent?: number;
    @optional(undefined)
    maxHealthyPercent?: number;
}
