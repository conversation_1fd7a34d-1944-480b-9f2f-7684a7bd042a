import { Construct } from 'constructs';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import { Container, LinuxParameters, TaskDefinition } from './config';
import { ContainerImageConstruct } from './container-image-construct';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Size } from 'aws-cdk-lib';
import { ECSHelpers } from '../utils/ecs-helpers';

export interface TaskDefinitionConstructProps {
    taskDefinition: TaskDefinition;
}

export class TaskDefinitionConstruct extends Construct {
    readonly taskDefinition: ecs.TaskDefinition;

    constructor(scope: Construct, id: string, props: TaskDefinitionConstructProps) {
        super(scope, id);
        const role = this.createTaskRole(props);
        const taskDefinition = new ecs.Ec2TaskDefinition(this, props.taskDefinition.name, {
            family: props.taskDefinition.name,
            taskRole: role,
            executionRole: role,
        });

        // Must be done before creating mounts
        this.createTaskDefinitionVolumes(props, taskDefinition);

        const containerDefinitions = props.taskDefinition.containers.map((container) => {
            return this.createContainerDefinition(props, taskDefinition, container);
        });

        props.taskDefinition.containers.map((container) => {
            ECSHelpers.createContainerLinks(container, containerDefinitions);
        });

        this.taskDefinition = taskDefinition;
    }

    private createTaskDefinitionVolumes(props: TaskDefinitionConstructProps, taskDefinition: ecs.TaskDefinition) {
        const volumes = props.taskDefinition.volumes
            ? props.taskDefinition.volumes.map((volume) => ECSHelpers.convertToVolume(volume))
            : [];

        volumes.forEach((volume) => taskDefinition.addVolume(volume));
    }

    private createContainerDefinition(
        props: TaskDefinitionConstructProps,
        taskDefinition: ecs.TaskDefinition,
        container: Container
    ): ecs.ContainerDefinition {
        const containerName = `${props.taskDefinition.name}${container.name}`;
        const containerImage = new ContainerImageConstruct(this, `${containerName}Image`, {
            dockerImageAsset: container.dockerImage,
        });

        const portMappings = container.portMappings
            ? ECSHelpers.convertToECSPortMappings(container.portMappings)
            : undefined;

        const linuxParameters = container.linuxParameters
            ? this.createLinuxParameters(props, container, container.linuxParameters)
            : undefined;

        const healthCheck = container.healthCheck ? ECSHelpers.convertToHealthCheck(container.healthCheck) : undefined;

        const containerDefinition = taskDefinition.addContainer(containerName, {
            containerName: container.name,
            image: containerImage.containerImage,
            memoryLimitMiB: Size.gibibytes(container.memoryLimitGB).toMebibytes(),
            cpu: container.cpu,
            gpuCount: container.gpuCount,
            command: container.commands,
            portMappings: portMappings,
            environment: container.environment,
            essential: container.essential,
            logging: new ecs.AwsLogDriver({
                streamPrefix: containerName,
            }),
            linuxParameters: linuxParameters,
            healthCheck: healthCheck,
        });

        const mountPoints = container.mountPoints
            ? container.mountPoints.map((mountPoint) => ECSHelpers.convertToMountPoint(mountPoint))
            : [];

        mountPoints.forEach((mountPoint) => containerDefinition.addMountPoints(mountPoint));

        return containerDefinition;
    }

    private createLinuxParameters(
        props: TaskDefinitionConstructProps,
        container: Container,
        linuxParameters: LinuxParameters
    ): ecs.LinuxParameters {
        const sharedMemorySize = linuxParameters.sharedMemorySizeInMB
            ? Size.mebibytes(linuxParameters.sharedMemorySizeInMB).toMebibytes()
            : undefined;

        return new ecs.LinuxParameters(this, `${container.name}LinuxParameters`, {
            sharedMemorySize: sharedMemorySize,
        });
    }

    private createTaskRole(props: TaskDefinitionConstructProps): iam.Role {
        const roleName = `${props.taskDefinition.name}TaskRole`;
        const role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new iam.CompositePrincipal(
                new iam.ServicePrincipal('ec2.amazonaws.com'),
                new iam.ServicePrincipal('ecs.amazonaws.com'),
                new iam.ServicePrincipal('ecs-tasks.amazonaws.com')
            ),
            description: 'IAM role for task execution',
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryReadOnly'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchLogsFullAccess'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore'),
            ],
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'secretsmanager:GetSecretValue',
                    'secretsmanager:DescribeSecret',
                    'secretsmanager:ListSecretVersionIds',
                ],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:ListBucket', 's3:*Object'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['dynamodb:*Item'],
                resources: [`arn:aws:dynamodb:*:*:table/*`],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['sagemaker:InvokeEndpoint', 'sagemaker:DescribeEndpoint', 'sagemaker:ListEndpoints'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['cloudwatch:PutMetricData'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['sts:AssumeRole'],
                resources: ['*'],
            })
        );

        return role;
    }
}
