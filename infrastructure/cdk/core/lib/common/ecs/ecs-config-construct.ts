import { Construct } from 'constructs';
import { ECSConfig } from './ecs-config';

export interface ECSConfigConstructProps {
    ecsConfig: ECSConfig;
}

export class ECSConfigConstruct extends Construct {
    public readonly userData: string;

    constructor(scope: Construct, id: string, props: ECSConfigConstructProps) {
        super(scope, id);

        const userData = `
 echo ECS_DISABLE_IMAGE_CLEANUP=${props.ecsConfig.disable_image_cleanup} >> /etc/ecs/ecs.config
 echo ECS_IMAGE_CLEANUP_INTERVAL=${props.ecsConfig.cleanup_interval_minutes}m >> /etc/ecs/ecs.config
 echo ECS_IMAGE_MINIMUM_CLEANUP_AGE=${props.ecsConfig.minimum_cleanup_age_minutes}m >> /etc/ecs/ecs.config
        `;

        this.userData = userData;
    }
}
