import { Construct } from 'constructs';
import * as autoscaling from 'aws-cdk-lib/aws-autoscaling';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import { MachineImageType } from 'aws-cdk-lib/aws-ecs';
import { SubnetSelection, Vpc } from 'aws-cdk-lib/aws-ec2';
import { AsgCapacityProvider } from './config';
import { AutoScalingConstruct } from '../autoscaling/autoscaling-construct';

export interface AsgCapacityProviderConstructProps {
    vpc: Vpc;
    vpcSubnets?: SubnetSelection;
    asgCapacityProvider: AsgCapacityProvider;
}

export class AsgCapacityProviderConstruct extends Construct {
    readonly asgCapacityProvider: ecs.AsgCapacityProvider;
    readonly autoScalingGroup: autoscaling.AutoScalingGroup;
    readonly autoScalingConstruct: AutoScalingConstruct;

    constructor(scope: Construct, id: string, props: AsgCapacityProviderConstructProps) {
        super(scope, id);

        // Create a spot fleet
        const autoScalingGroup = new AutoScalingConstruct(this, `${props.asgCapacityProvider.name}AutoScaling`, {
            vpc: props.vpc,
            vpcSubnets: props.vpcSubnets,
            autoScalingGroup: props.asgCapacityProvider.autoScalingGroup,
        });

        const asgCapacityProvider = new ecs.AsgCapacityProvider(this, props.asgCapacityProvider.name, {
            capacityProviderName: props.asgCapacityProvider.name,
            autoScalingGroup: autoScalingGroup.autoScalingGroup,
            enableManagedScaling: props.asgCapacityProvider.enableManagedScaling,
            enableManagedDraining: props.asgCapacityProvider.enableManagedDraining,
            targetCapacityPercent: props.asgCapacityProvider.targetCapacityPercent,
            maximumScalingStepSize: props.asgCapacityProvider.maximumScalingStepSize,
            minimumScalingStepSize: props.asgCapacityProvider.minimumScalingStepSize,
            instanceWarmupPeriod: props.asgCapacityProvider.instanceWarmupPeriod,
            canContainersAccessInstanceRole: props.asgCapacityProvider.canContainersAccessInstanceRole,
            spotInstanceDraining: props.asgCapacityProvider.spotInstanceDraining,
            machineImageType: MachineImageType.AMAZON_LINUX_2,
        });
        asgCapacityProvider.node.addDependency(autoScalingGroup);

        this.asgCapacityProvider = asgCapacityProvider;
        this.autoScalingGroup = autoScalingGroup.autoScalingGroup;
        this.autoScalingConstruct = autoScalingGroup;
    }
}
