import { Construct } from 'constructs';
import { ECSCluster } from './config';
import * as autoscaling from 'aws-cdk-lib/aws-autoscaling';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import { SubnetSelection, Vpc } from 'aws-cdk-lib/aws-ec2';
import { AsgCapacityProviderConstruct } from './asg-capacity-provider-construct';
import { AutoScalingConstruct } from '../autoscaling/autoscaling-construct';

export interface ECSClusterConstructProps {
    vpc: Vpc;
    vpcSubnets?: SubnetSelection;
    ecsCluster: ECSCluster;
}

export class ECSClusterConstruct extends Construct {
    readonly cluster: ecs.Cluster;
    readonly asgCapacityProvider: ecs.AsgCapacityProvider;
    readonly autoScalingGroup: autoscaling.AutoScalingGroup;
    readonly autoScalingConstruct: AutoScalingConstruct;

    constructor(scope: Construct, id: string, props: ECSClusterConstructProps) {
        super(scope, id);

        const asgCapacityProvider = new AsgCapacityProviderConstruct(this, `${props.ecsCluster.name}CapacityProvider`, {
            vpc: props.vpc,
            vpcSubnets: props.vpcSubnets,
            asgCapacityProvider: props.ecsCluster.asgCapacityProvider,
        });

        const cluster = new ecs.Cluster(this, props.ecsCluster.name, {
            vpc: props.vpc,
        });

        cluster.addAsgCapacityProvider(asgCapacityProvider.asgCapacityProvider);

        this.asgCapacityProvider = asgCapacityProvider.asgCapacityProvider;
        this.autoScalingGroup = asgCapacityProvider.autoScalingGroup;
        this.cluster = cluster;
        this.autoScalingConstruct = asgCapacityProvider.autoScalingConstruct;
    }
}
