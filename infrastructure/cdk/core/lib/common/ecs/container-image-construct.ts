import { Construct } from 'constructs';
import { DockerHelpers } from '../utils/docker-helpers';
import { ContainerImage } from 'aws-cdk-lib/aws-ecs';
import { DockerImageAsset } from '../docker/config';

export interface DockerImageConstructProps {
    dockerImageAsset: DockerImageAsset;
}

export class ContainerImageConstruct extends Construct {
    public readonly containerImage: ContainerImage;

    constructor(scope: Construct, id: string, props: DockerImageConstructProps) {
        super(scope, id);
        if (props.dockerImageAsset.directory) {
            const resolvedBuildArgs = DockerHelpers.resolveBuildArguments(
                scope,
                props.dockerImageAsset.buildArgs,
                props.dockerImageAsset.secretBuildArgs
            );

            this.containerImage = ContainerImage.fromAsset(props.dockerImageAsset.directory, {
                platform: DockerHelpers.findDockerImagePlatform(props.dockerImageAsset.platform),
                buildArgs: resolvedBuildArgs,
                file: props.dockerImageAsset.file,
            });
        } else if (props.dockerImageAsset.image) {
            this.containerImage = ContainerImage.fromRegistry(props.dockerImageAsset.image);
        } else {
            throw new Error('Need to provide either an image or directory to load docker image asset from');
        }
    }
}
