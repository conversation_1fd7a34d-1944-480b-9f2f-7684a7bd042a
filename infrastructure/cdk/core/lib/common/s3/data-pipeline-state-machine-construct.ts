import * as cdk from 'aws-cdk-lib';
import { Duration } from 'aws-cdk-lib';
import { SubnetType } from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as sfn from 'aws-cdk-lib/aws-stepfunctions';
import * as tasks from 'aws-cdk-lib/aws-stepfunctions-tasks';
import { TaskStateBase } from 'aws-cdk-lib/aws-stepfunctions/lib/states/task-base';
import { Construct } from 'constructs';
import * as path from 'path';
import { AwsEnvAccount, CdkAppInfo } from '../../build-config';
import {
    BatchSubmitStage,
    DataPipelineStage,
    DataPipelineStateMachine,
    FailStage,
    GlueStage,
    LambdaStage,
    ModelEndpointStage,
    ModelStage,
    ModelValidationStage,
    ProcessStage,
    SubPipelineStage,
    TrainStage,
} from '../../data-pipelines/config';
import { LambdaConfig } from '../lambda/config';
import { NetworkStack } from '../../vpc/network-stack';
import { GlueJobConstruct } from '../glue/glue-job-construct';
import { LambdaConstruct } from '../lambda/lambda-construct';
import { S3BucketConstruct } from '../s3-bucket-construct';
import { ProcessingInput, ProcessingOutput } from '../sagemaker/base-types';
import { SageMakerCreateProcessingJob } from '../sagemaker/create-processing-job';
import { DockerImageConstruct } from '../stepfunctions/docker-image-contruct';
import { ChoiceHelpers } from '../utils/choice-helpers';
import { DockerHelpers } from '../utils/docker-helpers';
import { EC2Helpers } from '../utils/ec2-helpers';
import { IamHelpers } from '../utils/iam-helpers';
import { LambdaHelpers } from '../utils/lambda-helpers';
import { StepfunctionHelpers } from '../utils/stepfunction-helpers';
import { AccountPrincipal } from 'aws-cdk-lib/aws-iam';
import { DataPipelineHelpers } from '../utils/data-pipeline-helpers';
import { S3Bucket } from './config';

interface DataPipelineStateMachineProps {
    awsEnvAccount: AwsEnvAccount;
    cdkAppInfo: CdkAppInfo;
    stateMachine: DataPipelineStateMachine;
    networkStack: NetworkStack;
}

type ChainableStage = sfn.IChainable & sfn.INextable;

export class DataPipelineStateMachineConstruct extends Construct {
    private readonly assetS3Bucket?: s3.Bucket;

    constructor(scope: Construct, id: string, stateMachineProps: DataPipelineStateMachineProps) {
        super(scope, id);
        if (stateMachineProps.stateMachine.assetBucket) {
            this.assetS3Bucket = this.createAssetBucket(stateMachineProps, stateMachineProps.stateMachine.assetBucket);
        }
        this.createStateMachine(stateMachineProps);
    }

    private createAssetBucket(props: DataPipelineStateMachineProps, s3Bucket: S3Bucket): s3.Bucket {
        return new S3BucketConstruct(this, s3Bucket.name, {
            publicReadAccess: s3Bucket.publicReadAccess,
            transferAcceleration: s3Bucket.transferAcceleration,
            awsEnvAccount: props.awsEnvAccount,
            cdkAppInfo: props.cdkAppInfo,
            name: s3Bucket.name,
            removalPolicy: cdk.RemovalPolicy.DESTROY,
            enableVersioning: s3Bucket.enableVersioning,
            enableUserdataRetentionRule: s3Bucket.enableUserdataRetentionRule,
        }).s3Bucket;
    }

    private addStageRetry(stage: DataPipelineStage, state: TaskStateBase) {
        if (stage.retry) {
            const retryProps: sfn.RetryProps = {
                maxAttempts: stage.retry.maxAttempts,
                interval: stage.retry.intervalInSeconds
                    ? cdk.Duration.seconds(stage.retry.intervalInSeconds)
                    : undefined,
                backoffRate: stage.retry.backOffRate,
                errors: stage.retry.errors,
            };
            state.addRetry(retryProps);
        }
    }

    private addStageCatch(stage: DataPipelineStage, state: TaskStateBase) {
        if (stage.catch) {
            const catchStageName = stage.catch.catchStageName;
            const catchProps: sfn.CatchProps = {
                errors: stage.catch.errors,
                resultPath: stage.catch.errorResultJsonPath,
            };
            const reachableStates = sfn.State.findReachableStates(state);
            const filteredEndState = reachableStates.find((endState) => endState.id === catchStageName);
            if (filteredEndState) {
                state.addCatch(filteredEndState, catchProps);
            } else {
                throw new Error(
                    `The specified catch stage '${catchStageName}' could not be found in any of the reachable stages for '${state.id}'`
                );
            }
        }
    }

    private addStageChoice(
        stage: DataPipelineStage,
        defaultNextStage: ChainableStage,
        defaultOtherwiseStage: ChainableStage | undefined = undefined
    ): ChainableStage {
        if (stage.choice) {
            const skipState = defaultOtherwiseStage ?? new sfn.Pass(this, `${stage.choice.name}Skipped`);
            const props = stage.choice;
            const choiceState = new sfn.Choice(this, stage.choice.name);
            const condition = ChoiceHelpers.getCombinedChoiceCondition(props.operation, props.conditions);
            choiceState.when(condition, defaultNextStage).otherwise(skipState);
            return choiceState.afterwards();
        }
        return defaultNextStage;
    }

    private createFailStage(props: DataPipelineStateMachineProps, failStage: FailStage): ChainableStage {
        const failState = new sfn.Fail(this, failStage.name, {
            stateName: failStage.name,
            errorPath: failStage.errorPathJsonPath,
            causePath: failStage.causePathJsonPath,
        }) as unknown as ChainableStage;

        return this.addStageChoice(failStage, failState);
    }

    private createGlueStage(props: DataPipelineStateMachineProps, glueStage: GlueStage): ChainableStage {
        if (this.assetS3Bucket == undefined) {
            throw new Error(`assetBucket has not been configured for data pipeline and is required for glue stage`);
        }

        const fileName = path.basename(glueStage.glueJobFilePath);
        const directory = path.dirname(glueStage.glueJobFilePath);

        const glueJobEtl = new GlueJobConstruct(this, glueStage.name, {
            name: glueStage.name,
            region: props.awsEnvAccount.targetRegion,
            assetS3Bucket: this.assetS3Bucket,
            timeoutInMin: glueStage.timeoutInMin,
            etlScriptFileName: fileName,
            etlScriptFilePath: directory,
            etlScriptDestinationS3KeyPrefix: glueStage.glueJobDestinationS3KeyPrefix,
            numberOfWorkers: glueStage.glueJobNumberOfWorkers,
            maxConcurrentRuns: glueStage.glueJobMaxConcurrentRuns,
        });

        const glueJobTask = new tasks.GlueStartJobRun(this, `${glueStage.name}-Job`, {
            glueJobName: glueJobEtl.job.name ?? `${glueStage.name}-Job`,
            arguments: sfn.TaskInput.fromJsonPathAt(glueStage.glueArgumentsJsonPath),
            taskTimeout: sfn.Timeout.duration(Duration.minutes(glueStage.timeoutInMin)),
            notifyDelayAfter: cdk.Duration.minutes(1),
            integrationPattern: sfn.IntegrationPattern.RUN_JOB,
            resultPath: glueStage.glueResultJsonPath,
        });

        this.addStageRetry(glueStage, glueJobTask);
        this.addStageCatch(glueStage, glueJobTask);
        return this.addStageChoice(glueStage, glueJobTask);
    }

    private createBatchSubmitStage(
        props: DataPipelineStateMachineProps,
        batchSubmitStage: BatchSubmitStage
    ): ChainableStage {
        const batchSubmitRole = this.createSageMakerBatchSubmitRole(props, batchSubmitStage);
        const batchJobEnvironment = batchSubmitStage.batchSubmitEnvironmentJsonPath
            ? sfn.JsonPath.jsonToString(sfn.JsonPath.objectAt(batchSubmitStage.batchSubmitEnvironmentJsonPath))
            : '';
        const batchSubmitEnvironment = batchSubmitStage.batchSubmitEnvironment
            ? DataPipelineHelpers.getEnvironment(batchSubmitStage.batchSubmitEnvironment)
            : {};

        const batchSubmitState = new tasks.BatchSubmitJob(this, batchSubmitStage.name, {
            jobName: sfn.JsonPath.stringAt(batchSubmitStage.batchSubmitJobNameJsonPath),
            jobDefinitionArn: batchSubmitStage.batchSubmitJobDefinitionArn,
            jobQueueArn: batchSubmitStage.batchSubmitJobQueueArn,
            resultPath: batchSubmitStage.batchSubmitResultJsonPath,
            integrationPattern: sfn.IntegrationPattern.RUN_JOB,
            containerOverrides: {
                environment: {
                    BATCH_JOB_ENVIRONMENT: batchJobEnvironment,
                    ...batchSubmitEnvironment,
                },
            },
            credentials: {
                role: sfn.TaskRole.fromRole(batchSubmitRole),
            },
            taskTimeout: sfn.Timeout.duration(Duration.minutes(batchSubmitStage.timeoutInMin)),
        });

        let finalState: ChainableStage = batchSubmitState;
        if (batchSubmitStage.batchCompletionLambdaStage) {
            const lambdaState = this.createLambdaStage(props, batchSubmitStage.batchCompletionLambdaStage);
            finalState = batchSubmitState.next(lambdaState);
        }
        if (batchSubmitStage.batchCompletionFailStage) {
            const failState = this.createFailStage(props, batchSubmitStage.batchCompletionFailStage);
            finalState = finalState.next(failState);
        }

        this.addStageRetry(batchSubmitStage, batchSubmitState);
        this.addStageCatch(batchSubmitStage, batchSubmitState);
        return this.addStageChoice(batchSubmitStage, finalState);
    }

    private createTrainingStage(props: DataPipelineStateMachineProps, trainStage: TrainStage): ChainableStage {
        const trainingRole = this.createSageMakerTrainingRole(props, trainStage);
        const trainingImageAsset = new DockerImageConstruct(this, trainStage.trainDockerImage.name, {
            name: trainStage.trainDockerImage.name,
            image: trainStage.trainDockerImage.image,
            directory: trainStage.trainDockerImage.directory,
            platform: DockerHelpers.findDockerImagePlatform(trainStage.trainDockerImage.platform),
            buildArgs: trainStage.trainDockerImage.buildArgs,
            secretBuildArgs: trainStage.trainDockerImage.secretBuildArgs,
        });

        const trainingInputs: Array<tasks.Channel> = trainStage.trainS3Inputs.map((trainS3Input) => {
            return {
                channelName: trainS3Input.inputName,
                contentType: trainS3Input.contentType,
                dataSource: {
                    s3DataSource: {
                        s3Location: tasks.S3Location.fromJsonExpression(trainS3Input.inputJsonPath),
                        s3DataDistributionType: tasks.S3DataDistributionType.SHARDED_BY_S3_KEY,
                    },
                },
            };
        });

        const trainEnvironment = trainStage.trainEnvironmentJsonPath
            ? sfn.TaskInput.fromJsonPathAt(trainStage.trainEnvironmentJsonPath)
            : undefined;

        const trainVpcConfig = trainStage.enableVPCNetworking ? this.createVpcConfig(props) : undefined;

        const trainState = new tasks.SageMakerCreateTrainingJob(this, trainStage.name, {
            trainingJobName: sfn.JsonPath.stringAt(trainStage.trainJobNameJsonPath),
            role: trainingRole,
            algorithmSpecification: {
                trainingImage: trainingImageAsset.dockerImage,
                trainingInputMode: tasks.InputMode.FILE,
            },
            environment: trainEnvironment?.value,
            hyperparameters: trainStage.trainParameters,
            inputDataConfig: trainingInputs,
            outputDataConfig: {
                s3OutputLocation: tasks.S3Location.fromJsonExpression(trainStage.trainS3Output.outputJsonPath),
            },
            resourceConfig: {
                instanceCount: 1,
                instanceType: EC2Helpers.findInstanceType(trainStage.trainInstanceType),
                volumeSize: cdk.Size.gibibytes(50),
            },
            stoppingCondition: {
                maxRuntime: cdk.Duration.minutes(trainStage.timeoutInMin ?? 60),
            },
            integrationPattern: sfn.IntegrationPattern.RUN_JOB,
            resultPath: trainStage.trainResultJsonPath,
            vpcConfig: trainVpcConfig,
        });

        this.addStageRetry(trainStage, trainState);
        this.addStageCatch(trainStage, trainState);
        return this.addStageChoice(trainStage, trainState);
    }

    private createProcessingStage(props: DataPipelineStateMachineProps, processStage: ProcessStage): ChainableStage {
        const processingRole = this.createSageMakerProcessingRole(props, processStage);
        const processingImageAsset = new DockerImageConstruct(this, processStage.processDockerImage.name, {
            name: processStage.processDockerImage.name,
            image: processStage.processDockerImage.image,
            directory: processStage.processDockerImage.directory,
            platform: DockerHelpers.findDockerImagePlatform(processStage.processDockerImage.platform),
            buildArgs: processStage.processDockerImage.buildArgs,
            secretBuildArgs: processStage.processDockerImage.secretBuildArgs,
        });

        const processingInputs: Array<ProcessingInput> = processStage.processS3Inputs.map((processS3Input) => {
            return {
                inputName: processS3Input.inputName,
                s3Input: {
                    s3Location: tasks.S3Location.fromJsonExpression(processS3Input.inputJsonPath),
                },
            };
        });

        const processingOutputs: Array<ProcessingOutput> = processStage.processS3Outputs.map((processS3Output) => {
            return {
                outputName: processS3Output.outputName,
                s3Output: {
                    s3Location: tasks.S3Location.fromJsonExpression(processS3Output.outputJsonPath),
                },
            };
        });

        const processEnvironment = processStage.processEnvironmentJsonPath
            ? sfn.TaskInput.fromJsonPathAt(processStage.processEnvironmentJsonPath)
            : undefined;

        const processNetworkConfig = processStage.enableVPCNetworking
            ? { vpcConfig: this.createVpcConfig(props) }
            : undefined;

        const processState = new SageMakerCreateProcessingJob(this, processStage.name, {
            processingJobName: sfn.JsonPath.stringAt(processStage.processJobNameJsonPath),
            role: processingRole,
            appSpecification: {
                containerImage: processingImageAsset.dockerImage,
            },
            processingInputs: processingInputs,
            processingOutputConfig: {
                outputs: processingOutputs,
            },
            processingResources: {
                clusterConfig: {
                    instanceCount: 1,
                    instanceType: EC2Helpers.findInstanceType(processStage.processInstanceType),
                    volumeSize: cdk.Size.gibibytes(10),
                },
            },
            stoppingCondition: {
                maxRuntime: cdk.Duration.minutes(processStage.timeoutInMin ?? 60),
            },
            integrationPattern: sfn.IntegrationPattern.RUN_JOB,
            resultPath: processStage.processResultJsonPath,
            environment: processEnvironment,
            networkConfig: processNetworkConfig,
        });

        let finalState: ChainableStage = processState;
        if (processStage.processCompletionLambdaStage) {
            const lambdaState = this.createLambdaStage(props, processStage.processCompletionLambdaStage);
            finalState = processState.next(lambdaState);
        }
        if (processStage.processCompletionFailStage) {
            const failState = this.createFailStage(props, processStage.processCompletionFailStage);
            finalState = finalState.next(failState);
        }

        this.addStageRetry(processStage, processState);
        this.addStageCatch(processStage, processState);
        return this.addStageChoice(processStage, finalState);
    }

    private createLambdaStage(props: DataPipelineStateMachineProps, lambdaStage: LambdaStage): ChainableStage {
        const lambda = this.createLambda(props, lambdaStage);
        // Includes both context and the execution input merged together
        // https://docs.aws.amazon.com/step-functions/latest/dg/input-output-contextobject.html
        const mergedPayload = sfn.TaskInput.fromText(
            sfn.JsonPath.jsonMerge(sfn.JsonPath.objectAt('$'), sfn.JsonPath.objectAt('$$'))
        );
        const lambdaState = new tasks.LambdaInvoke(this, lambdaStage.name, {
            lambdaFunction: lambda,
            resultPath: lambdaStage.lambdaResultJsonPath,
            inputPath: lambdaStage.lambdaInputJsonPath,
            taskTimeout: sfn.Timeout.duration(Duration.minutes(lambdaStage.timeoutInMin)),
            payload: mergedPayload,
        });
        this.addStageRetry(lambdaStage, lambdaState);
        this.addStageCatch(lambdaStage, lambdaState);
        return lambdaState;
    }

    private createModelStage(props: DataPipelineStateMachineProps, modelStage: ModelStage): ChainableStage {
        const modelRole = this.createSageMakerModelRole(props, modelStage);

        const modelImageAsset = new DockerImageConstruct(this, modelStage.modelDockerImage.name, {
            name: modelStage.modelDockerImage.name,
            image: modelStage.modelDockerImage.image,
            directory: modelStage.modelDockerImage.directory,
            platform: DockerHelpers.findDockerImagePlatform(modelStage.modelDockerImage.platform),
            buildArgs: modelStage.modelDockerImage.buildArgs,
            secretBuildArgs: modelStage.modelDockerImage.secretBuildArgs,
        });

        const createModelState = new tasks.SageMakerCreateModel(this, modelStage.name, {
            role: modelRole,
            modelName: sfn.JsonPath.stringAt(modelStage.modelNameJsonPath),
            primaryContainer: new tasks.ContainerDefinition({
                image: modelImageAsset.dockerImage,
                mode: tasks.Mode.SINGLE_MODEL,
                modelS3Location: tasks.S3Location.fromJsonExpression(modelStage.modelS3Input.inputJsonPath),
            }),
            resultPath: modelStage.modelResultJsonPath,
            taskTimeout: sfn.Timeout.duration(Duration.minutes(modelStage.timeoutInMin)),
        });

        this.addStageRetry(modelStage, createModelState);
        this.addStageCatch(modelStage, createModelState);

        return createModelState;
    }

    private createModelValidationStage(
        props: DataPipelineStateMachineProps,
        modelValidationStage: ModelValidationStage,
        modelEndpointConfig: sfn.IChainable,
        finishState: sfn.IChainable
    ): ChainableStage {
        const modelValidationLambda = new LambdaConstruct(
            this,
            modelValidationStage.modelValidationLambda.functionName,
            {
                name: modelValidationStage.modelValidationLambda.functionName,
                runtime: LambdaHelpers.findLambdaRuntime(modelValidationStage.modelValidationLambda.runtime),
                lambdaPath: modelValidationStage.modelValidationLambda.lambdaPath,
                handler: modelValidationStage.modelValidationLambda.handler,
                policies: [
                    new iam.PolicyStatement({
                        effect: iam.Effect.ALLOW,
                        actions: ['sagemaker:Describe*'],
                        resources: ['*'],
                    }),
                ],
                region: props.awsEnvAccount.targetRegion,
                timeout: cdk.Duration.minutes(modelValidationStage.timeoutInMin),
            }
        );

        const choiceAccuracyConditionState = new sfn.Choice(
            this,
            `Accuracy higher(${modelValidationStage.modelValidationErrorThreshold})?`
        )
            .when(
                sfn.Condition.numberLessThan(
                    `${modelValidationStage.modelValidationResultJsonPath}.Payload.Metrics[0].Value`,
                    modelValidationStage.modelValidationErrorThreshold
                ),
                modelEndpointConfig
            )
            .otherwise(finishState);

        const modelValidationState = new tasks.LambdaInvoke(this, modelValidationStage.name, {
            lambdaFunction: modelValidationLambda.lambdaFunction,
            resultPath: modelValidationStage.modelValidationResultJsonPath,
        });

        this.addStageRetry(modelValidationStage, modelValidationState);
        this.addStageCatch(modelValidationStage, modelValidationState);

        return modelValidationState.next(choiceAccuracyConditionState);
    }

    private createModelEndpointConfig(
        props: DataPipelineStateMachineProps,
        modelStage: ModelStage,
        modelEndpointStage: ModelEndpointStage
    ): ChainableStage {
        const modelEndpointConfig = new tasks.SageMakerCreateEndpointConfig(this, `${modelEndpointStage.name}-Config`, {
            endpointConfigName: sfn.JsonPath.stringAt(modelEndpointStage.modelEndpointConfigNameJsonPath),
            productionVariants: [
                {
                    initialInstanceCount: modelEndpointStage.modelEndpointConfigInstanceCount,
                    instanceType: EC2Helpers.findInstanceType(modelEndpointStage.modelEndpointConfigInstanceType),
                    modelName: sfn.JsonPath.stringAt(modelStage.modelNameJsonPath),
                    variantName: 'variant1',
                },
            ],
            resultPath: modelEndpointStage.modelEndpointConfigResultJsonPath,
        });

        return modelEndpointConfig;
    }

    private upsertModelEndpoint(
        props: DataPipelineStateMachineProps,
        modelStage: ModelStage,
        modelEndpointStage: ModelEndpointStage
    ): sfn.IChainable[] {
        const describeModelEndpointLambda = this.createModelEndpointDescribeLambda(
            props,
            modelEndpointStage.modelEndpointDescribeLambda
        );

        const describeModelEndpoint = new tasks.LambdaInvoke(
            this,
            `${modelEndpointStage.modelEndpointDescribeLambda.functionName}-Task`,
            {
                lambdaFunction: describeModelEndpointLambda,
                resultPath: modelEndpointStage.modelEndpointDescribeResultJsonPath,
            }
        );

        const createModelEndpoint = new tasks.SageMakerCreateEndpoint(this, `${modelEndpointStage.name}-Create`, {
            endpointName: sfn.JsonPath.stringAt(modelEndpointStage.modelEndpointNameJsonPath),
            endpointConfigName: sfn.JsonPath.stringAt(modelEndpointStage.modelEndpointConfigNameJsonPath),
            resultPath: modelEndpointStage.modelEndpointResultJsonPath,
        });

        const updateModelEndpoint = new tasks.SageMakerUpdateEndpoint(this, `${modelEndpointStage.name}-Update`, {
            endpointName: sfn.JsonPath.stringAt(modelEndpointStage.modelEndpointNameJsonPath),
            endpointConfigName: sfn.JsonPath.stringAt(modelEndpointStage.modelEndpointConfigNameJsonPath),
            resultPath: modelEndpointStage.modelEndpointResultJsonPath,
        });

        // Need to ensure we're updating existing endpoints if necessary
        const modelEndpointUpsert = new sfn.Choice(this, `${modelEndpointStage.name}-Exists`)
            .when(
                sfn.Condition.stringEquals(
                    `${modelEndpointStage.modelEndpointDescribeResultJsonPath}.Payload.Exists`,
                    'TRUE'
                ),
                updateModelEndpoint
            )
            .otherwise(createModelEndpoint);

        if (modelEndpointStage.modelEndpointInvokeLambda) {
            this.createModelEndpointInvokeLambda(props, modelEndpointStage.modelEndpointInvokeLambda);
        }

        return [describeModelEndpoint, modelEndpointUpsert, modelEndpointUpsert.afterwards()];
    }

    private createModelEndpointDescribeLambda(
        props: DataPipelineStateMachineProps,
        describeLambda: LambdaConfig
    ): lambda.Function {
        const describeModelEndpointLambda = new LambdaConstruct(this, describeLambda.functionName, {
            name: describeLambda.functionName,
            runtime: LambdaHelpers.findLambdaRuntime(describeLambda.runtime),
            containerPath: describeLambda.containerPath,
            handler: describeLambda.handler,
            policies: [
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: ['sagemaker:Describe*'],
                    resources: ['*'],
                }),
            ],
            environment: {
                ...describeLambda.environment,
            },
            region: props.awsEnvAccount.targetRegion,
            timeout: cdk.Duration.minutes(describeLambda.timeoutInMin ?? 1),
        });

        return describeModelEndpointLambda.lambdaFunction;
    }

    private createModelEndpointInvokeLambda(
        props: DataPipelineStateMachineProps,
        invokeLambda: LambdaConfig
    ): lambda.Function {
        const invokeModelEndpointLambda = new LambdaConstruct(this, invokeLambda.functionName, {
            name: invokeLambda.functionName,
            runtime: LambdaHelpers.findLambdaRuntime(invokeLambda.runtime),
            containerPath: invokeLambda.containerPath,
            handler: invokeLambda.handler,
            policies: [
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: ['sagemaker:Invoke*'],
                    resources: ['*'],
                }),
            ],
            environment: {
                ...invokeLambda.environment,
            },
            region: props.awsEnvAccount.targetRegion,
            timeout: cdk.Duration.minutes(invokeLambda.timeoutInMin ?? 1),
        });

        return invokeModelEndpointLambda.lambdaFunction;
    }

    private createSubPipelineStage(
        props: DataPipelineStateMachineProps,
        subPipelineStage: SubPipelineStage
    ): ChainableStage {
        const startState = new sfn.Pass(this, `${subPipelineStage.name}Start`);
        const finishState = new sfn.Pass(this, `${subPipelineStage.name}Finish`);

        const stages: Array<sfn.INextable | sfn.IChainable> = [startState];
        if (subPipelineStage.preProcessStage) {
            const processingStage = this.createProcessingStage(props, subPipelineStage.preProcessStage);
            stages.push(processingStage);
        }
        if (subPipelineStage.batchSubmitStage) {
            const batchSubmitStage = this.createBatchSubmitStage(props, subPipelineStage.batchSubmitStage);
            stages.push(batchSubmitStage);
        }
        if (subPipelineStage.trainStage) {
            const trainingStage = this.createTrainingStage(props, subPipelineStage.trainStage);
            stages.push(trainingStage);
        }
        if (subPipelineStage.postProcessStage) {
            const processingStage = this.createProcessingStage(props, subPipelineStage.postProcessStage);
            stages.push(processingStage);
        }
        if (subPipelineStage.modelStage) {
            const modelStage = this.createModelStage(props, subPipelineStage.modelStage);
            stages.push(modelStage);

            if (subPipelineStage.modelEndpointStage) {
                const modelEndpointConfig = this.createModelEndpointConfig(
                    props,
                    subPipelineStage.modelStage,
                    subPipelineStage.modelEndpointStage
                );
                stages.push(modelEndpointConfig);
                const [describeModelEndpoint, modelEndpointUpsert, modelEndpointUpsertAfter] = this.upsertModelEndpoint(
                    props,
                    subPipelineStage.modelStage,
                    subPipelineStage.modelEndpointStage
                );
                stages.push(describeModelEndpoint);
                stages.push(modelEndpointUpsert);
                stages.push(modelEndpointUpsertAfter);

                if (subPipelineStage.modelValidationStage) {
                    const modelValidationStage = this.createModelValidationStage(
                        props,
                        subPipelineStage.modelValidationStage,
                        modelEndpointConfig,
                        finishState
                    );
                    stages.push(modelValidationStage);
                }
            }
        }

        stages.push(finishState);

        for (let i = 0; i < stages.length - 1; ++i) {
            const stage = <sfn.INextable>stages[i];
            const otherStage = <sfn.IChainable>stages[i + 1];
            // Check if INextable
            if (stage.next !== undefined) {
                // Check if IChainable
                if (otherStage.startState !== undefined) {
                    stage.next(otherStage);
                }
            }
        }

        return this.addStageChoice(subPipelineStage, startState);
    }

    private createStateMachine(props: DataPipelineStateMachineProps): sfn.StateMachine {
        const startState = new sfn.Pass(this, `Start`);
        const finishState = new sfn.Pass(this, `Finish`);

        const stages: Array<sfn.INextable & sfn.IChainable> = [startState];

        if (props.stateMachine.glueStage) {
            const glueStage = this.createGlueStage(props, props.stateMachine.glueStage);
            stages.push(glueStage);
        }

        if (props.stateMachine.preProcessStages) {
            props.stateMachine.preProcessStages.forEach((preProcessStage) => {
                const processingStage = this.createProcessingStage(props, preProcessStage);
                stages.push(processingStage);
            });
        }

        if (props.stateMachine.batchSubmitStages) {
            props.stateMachine.batchSubmitStages.forEach((batchSubmitStage) => {
                const newBatchSubmitStage = this.createBatchSubmitStage(props, batchSubmitStage);
                stages.push(newBatchSubmitStage);
            });
        }

        if (props.stateMachine.subPipelineStages) {
            const parallelStage = new sfn.Parallel(this, `${props.stateMachine.name}-SubPipelines`);
            props.stateMachine.subPipelineStages.forEach((subPipelineStage) => {
                const newSubPipelineStage = this.createSubPipelineStage(props, subPipelineStage);
                parallelStage.branch(newSubPipelineStage);
            });
            stages.push(parallelStage);
        }

        stages.push(finishState);

        for (let i = 0; i < stages.length - 1; ++i) {
            stages[i].next(stages[i + 1]);
        }

        if (props.stateMachine.name == 'CodeIngestionDataPipeline') {
            StepfunctionHelpers.printStepFunctions(startState);
        }

        const stateMachineRole = this.createStateMachineRole(props);
        const stateMachine = new sfn.StateMachine(this, props.stateMachine.name, {
            stateMachineName: props.stateMachine.name,
            definitionBody: sfn.DefinitionBody.fromChainable(startState),
            role: stateMachineRole,
        });

        if (props.stateMachine.triggerLambda) {
            this.createTriggerStateMachineLambda(props, stateMachine, props.stateMachine.triggerLambda);
        }

        return stateMachine;
    }

    private createSageMakerTrainingRole(props: DataPipelineStateMachineProps, trainStage: TrainStage): iam.Role {
        const roleName = IamHelpers.getRoleName(
            props.awsEnvAccount.targetRegion,
            props.stateMachine.name,
            trainStage.name
        );
        const role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new iam.ServicePrincipal('sagemaker.amazonaws.com'),
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchLogsFullAccess'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryFullAccess'),
            ],
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'secretsmanager:GetSecretValue',
                    'secretsmanager:DescribeSecret',
                    'secretsmanager:ListSecretVersionIds',
                ],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:ListBucket', 's3:*Object'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['dynamodb:*Item'],
                resources: [`arn:aws:dynamodb:*:*:table/*`],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                resources: ['*'],
            })
        );

        // Logs are already included as part of managed policy
        // https://docs.aws.amazon.com/sagemaker/latest/dg/sagemaker-roles.html
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['cloudwatch:PutMetricData'],
                resources: ['*'],
            })
        );

        // For VPC Configuration
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'ec2:CreateNetworkInterface',
                    'ec2:CreateNetworkInterfacePermission',
                    'ec2:DeleteNetworkInterface',
                    'ec2:DeleteNetworkInterfacePermission',
                    'ec2:DescribeNetworkInterfaces',
                    'ec2:DescribeVpcs',
                    'ec2:DescribeDhcpOptions',
                    'ec2:DescribeSubnets',
                    'ec2:DescribeSecurityGroups',
                ],
                resources: ['*'],
            })
        );

        return role;
    }

    private createSageMakerProcessingRole(props: DataPipelineStateMachineProps, processStage: ProcessStage): iam.Role {
        const roleName = IamHelpers.getRoleName(
            props.awsEnvAccount.targetRegion,
            props.stateMachine.name,
            processStage.name
        );
        const role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new iam.ServicePrincipal('sagemaker.amazonaws.com'),
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchLogsFullAccess'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryFullAccess'),
            ],
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'secretsmanager:GetSecretValue',
                    'secretsmanager:DescribeSecret',
                    'secretsmanager:ListSecretVersionIds',
                ],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:ListBucket', 's3:*Object'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['dynamodb:*Item'],
                resources: [`arn:aws:dynamodb:*:*:table/*`],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['sagemaker:InvokeEndpoint', 'sagemaker:DescribeEndpoint', 'sagemaker:ListEndpoints'],
                resources: ['*'],
            })
        );

        // Logs are already included as part of managed policy
        // https://docs.aws.amazon.com/sagemaker/latest/dg/sagemaker-roles.html
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['cloudwatch:PutMetricData'],
                resources: ['*'],
            })
        );

        // For VPC Configuration
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'ec2:CreateNetworkInterface',
                    'ec2:CreateNetworkInterfacePermission',
                    'ec2:DeleteNetworkInterface',
                    'ec2:DeleteNetworkInterfacePermission',
                    'ec2:DescribeNetworkInterfaces',
                    'ec2:DescribeVpcs',
                    'ec2:DescribeDhcpOptions',
                    'ec2:DescribeSubnets',
                    'ec2:DescribeSecurityGroups',
                ],
                resources: ['*'],
            })
        );

        return role;
    }

    private createSageMakerModelRole(props: DataPipelineStateMachineProps, modelStage: ModelStage): iam.Role {
        const roleName = IamHelpers.getRoleName(
            props.awsEnvAccount.targetRegion,
            props.stateMachine.name,
            modelStage.name
        );
        const role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new iam.ServicePrincipal('sagemaker.amazonaws.com'),
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchLogsFullAccess'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryFullAccess'),
            ],
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'secretsmanager:GetSecretValue',
                    'secretsmanager:DescribeSecret',
                    'secretsmanager:ListSecretVersionIds',
                ],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:ListBucket', 's3:*Object'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['dynamodb:*Item'],
                resources: [`arn:aws:dynamodb:*:*:table/*`],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                resources: ['*'],
            })
        );

        // Logs are already included as part of managed policy
        // https://docs.aws.amazon.com/sagemaker/latest/dg/sagemaker-roles.html
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['cloudwatch:PutMetricData'],
                resources: ['*'],
            })
        );

        return role;
    }

    private createSageMakerBatchSubmitRole(
        props: DataPipelineStateMachineProps,
        batchSubmitStage: BatchSubmitStage
    ): iam.Role {
        const roleName = IamHelpers.getRoleName(
            props.awsEnvAccount.targetRegion,
            props.stateMachine.name,
            batchSubmitStage.name
        );
        const role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new AccountPrincipal(props.awsEnvAccount.awsAccountID),
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchLogsFullAccess'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryFullAccess'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('AWSBatchFullAccess'),
            ],
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'secretsmanager:GetSecretValue',
                    'secretsmanager:DescribeSecret',
                    'secretsmanager:ListSecretVersionIds',
                ],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['s3:ListBucket', 's3:*Object'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['dynamodb:*Item'],
                resources: [`arn:aws:dynamodb:*:*:table/*`],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                resources: ['*'],
            })
        );

        // Logs are already included as part of managed policy
        // https://docs.aws.amazon.com/sagemaker/latest/dg/sagemaker-roles.html
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['cloudwatch:PutMetricData'],
                resources: ['*'],
            })
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['sts:AssumeRole'],
                resources: ['*'],
            })
        );

        return role;
    }

    private createStateMachineRole(props: DataPipelineStateMachineProps): iam.Role {
        const roleName = IamHelpers.getRoleName(
            props.awsEnvAccount.targetRegion,
            props.stateMachine.name,
            'StateMachine'
        );
        const role = new iam.Role(this, roleName, {
            roleName: roleName,
            assumedBy: new iam.ServicePrincipal('states.amazonaws.com'),
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchLogsFullAccess'),
                iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryFullAccess'),
            ],
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['glue:StartJobRun', 'glue:GetJobRun', 'glue:BatchStopJobRun', 'glue:GetJobRuns'],
                resources: ['*'],
            })
        );
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['lambda:InvokeFunction'],
                resources: ['*'],
            })
        );
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['events:DescribeRule', 'events:PutRule', 'events:PutTargets'],
                resources: ['*'],
            })
        );
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'sagemaker:CreateModel',
                    'sagemaker:DeleteEndpointConfig',
                    'sagemaker:DescribeProcessingJob',
                    'sagemaker:DescribeTrainingJob',
                    'sagemaker:CreateEndpoint',
                    'sagemaker:StopProcessingJob',
                    'sagemaker:StopTrainingJob',
                    'sagemaker:CreateTrainingJob',
                    'sagemaker:CreateProcessingJob',
                    'sagemaker:UpdateEndpoint',
                    'sagemaker:CreateEndpointConfig',
                    'sagemaker:DeleteEndpoint',
                    'sagemaker:AddTags',
                ],
                resources: ['*'],
            })
        );

        return role;
    }

    private createVpcConfig(props: DataPipelineStateMachineProps): tasks.VpcConfig {
        return {
            vpc: props.networkStack.coreVpc,
            subnets: props.networkStack.coreVpc.selectSubnets({
                subnetType: SubnetType.PRIVATE_WITH_EGRESS,
            }),
        };
    }

    private createLambda(props: DataPipelineStateMachineProps, lambdaStage: LambdaStage): lambda.Function {
        const lambdaVpcConfig = lambdaStage.enableVPCNetworking ? this.createVpcConfig(props) : undefined;

        const lambda = new LambdaConstruct(this, lambdaStage.lambda.functionName, {
            name: lambdaStage.lambda.functionName,
            runtime: LambdaHelpers.findLambdaRuntime(lambdaStage.lambda.runtime),
            containerPath: lambdaStage.lambda.containerPath,
            handler: lambdaStage.lambda.handler,
            timeout: cdk.Duration.minutes(lambdaStage.lambda.timeoutInMin ?? 1),
            policies: [
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: ['sagemaker:Describe*'],
                    resources: ['*'],
                }),
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: ['s3:ListBucket', 's3:*Object'],
                    resources: ['*'],
                }),
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: [
                        'secretsmanager:GetSecretValue',
                        'secretsmanager:DescribeSecret',
                        'secretsmanager:ListSecretVersionIds',
                    ],
                    resources: ['*'],
                }),
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: [
                        'ec2:CreateNetworkInterface',
                        'ec2:CreateNetworkInterfacePermission',
                        'ec2:CreateVpcEndpoint',
                        'ec2:DeleteNetworkInterface',
                        'ec2:DeleteNetworkInterfacePermission',
                        'ec2:DescribeDhcpOptions',
                        'ec2:DescribeNetworkInterfaces',
                        'ec2:DescribeRouteTables',
                        'ec2:DescribeSecurityGroups',
                        'ec2:DescribeSubnets',
                        'ec2:DescribeVpcEndpoints',
                    ],
                    resources: ['*'],
                }),
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                    resources: ['*'],
                }),
            ],
            environment: {
                ...lambdaStage.lambda.environment,
            },
            region: props.awsEnvAccount.targetRegion,
            vpcConfig: lambdaVpcConfig,
            keyPrefix: lambdaStage.lambda.keyPrefix,
            keySuffix: lambdaStage.lambda.keySuffix,
        });

        return lambda.lambdaFunction;
    }

    private createTriggerStateMachineLambda(
        props: DataPipelineStateMachineProps,
        stateMachine: sfn.StateMachine,
        triggerLambda: LambdaConfig
    ): lambda.Function {
        const triggerStateMachineLambda = new LambdaConstruct(this, triggerLambda.functionName, {
            name: triggerLambda.functionName,
            runtime: LambdaHelpers.findLambdaRuntime(triggerLambda.runtime),
            lambdaPath: triggerLambda.lambdaPath,
            handler: triggerLambda.handler,
            policies: [
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: ['sagemaker:Describe*'],
                    resources: ['*'],
                }),
            ],
            environment: {
                ...triggerLambda.environment,
                ENDPOINT_NAME: props.stateMachine.endpointName,
                STATE_MACHINE_ARN: stateMachine.stateMachineArn,
            },
            region: props.awsEnvAccount.targetRegion,
            timeout: cdk.Duration.minutes(triggerLambda.timeoutInMin ?? 1),
        });

        this.assetS3Bucket?.grantRead(triggerStateMachineLambda.lambdaFunction);
        stateMachine.grantStartExecution(triggerStateMachineLambda.lambdaFunction);

        return triggerStateMachineLambda.lambdaFunction;
    }
}
