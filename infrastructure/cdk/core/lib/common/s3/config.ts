import { JSONObject, optional, required } from 'ts-json-object';
import { LambdaConfig } from '../lambda/config';

export class S3BucketTier extends JSONObject {
    @required
    name: string;
    @optional(undefined)
    prefix?: string;
    @optional(180)
    archiveAccessTierTimeDays: number;
    @optional(360)
    deepArchiveAccessTierTimeDays: number;
}

export class S3Bucket extends JSONObject {
    @optional(undefined)
    id: string;
    @required
    name: string;
    @optional(undefined)
    bucketKeyEnabled: boolean;
    @optional(false)
    excludeEnvSuffix: boolean;
    @optional(false)
    publicReadAccess: boolean;
    @optional(true)
    blockPublicAccess: boolean;
    @optional(undefined)
    bucketEncryptionType: string;
    @optional(true)
    deleteProtection: boolean;
    @optional(false)
    autoDeleteObjects: boolean;
    @optional(false)
    addAccessLogPolicy: boolean;
    @optional(false)
    addLambdaAccessPolicy: boolean;
    @optional(undefined)
    bucketTier?: S3BucketTier;
    @optional(undefined)
    transferAcceleration?: boolean;
    @optional(false)
    enableVersioning: boolean;
    @optional(undefined)
    triggerLambda: LambdaConfig;
    @optional(false)
    enableBackupRetentionPolicyRule: boolean;
    @optional(false)
    enableUserdataRetentionRule: boolean;
    @optional(false)
    enableLogRetentionRule: boolean;
    @optional(undefined)
    replicationTargetRegion: string;
    @optional(undefined)
    replicationTargetKMSKeyArn: string;
    @optional(false)
    createRootAccountIAMRole: boolean;
}

export class S3Artifact extends JSONObject {
    @required
    bucketName: string;
    @required
    artifactPath: string;
}

export class S3BucketCorsRule extends JSONObject {
    // CORS configuration properties
    @optional(undefined)
    corsAllowedOrigins?: Array<string>; // List of allowed origins for CORS

    @optional(undefined)
    corsAllowedMethods?: Array<string>; // List of allowed methods for CORS (e.g., GET, PUT)

    @optional(undefined)
    corsAllowedHeaders?: Array<string>; // List of allowed headers for CORS

    @optional(undefined)
    corsExposeHeaders?: Array<string>; // List of headers exposed to the browser

    @optional(undefined)
    corsMaxAge?: number; // Time in seconds for how long the results of a preflight request can be cached
}
