import { JSONObject, optional, required } from 'ts-json-object';
import { AwsSecret } from '../asm/config';

export class RedisUser extends JSONObject {
    @required
    userName: string;
    @required
    accessString: string; // More about Redis ACL https://redis.io/topics/acl
    @optional(false)
    isDefaultUser: boolean;
    @required
    secrets: Array<AwsSecret>;
}

export class Redis extends JSONObject {
    @optional('cache.t4g.micro')
    cacheNodeType: string;
    @required
    clusterName: string;
    @optional('7.1')
    engineVersion: string;
    @optional(true)
    autoMinorVersionUpgrade: boolean;
    @optional(true)
    automaticFailoverEnabled: boolean;
    @optional(1)
    numNodeGroups: number;
    @optional(1)
    replicasPerNodeGroup: number;
    @optional('default.redis6.x.cluster.on')
    cacheParameterGroupName: string;
    @optional(false)
    multiAzEnabled: boolean;
    @required
    users: Array<RedisUser>;
    @optional([])
    additionalAllowedCIDRs: string[];
}
