import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { ActiveMQMeshNode } from './config';
import { CfnBroker } from 'aws-cdk-lib/aws-amazonmq';
import { aws_logs } from 'aws-cdk-lib';

export interface ActiveMQMeshNodeConstructProps {
    activeMQMeshNode: ActiveMQMeshNode;
    brokerName: string;
    users: Array<CfnBroker.UserProperty>;
    networkCIDR: string;
    securityGroup: ec2.ISecurityGroup;
    vpc: ec2.IVpc;
    vpcSubnets: ec2.SubnetSelection;
}

export class ActiveMQMeshNodeConstruct extends Construct {
    readonly broker: CfnBroker;

    constructor(scope: Construct, id: string, props: ActiveMQMeshNodeConstructProps) {
        super(scope, id);

        // Create subnet group
        const subnetIds = props.vpcSubnets.subnets?.map((subnet) => subnet.subnetId);
        if (!subnetIds) {
            throw new Error('No VPC subnets have been provided');
        }

        // Required to output logs
        // https://docs.aws.amazon.com/amazon-mq/latest/developer-guide/configure-logging-monitoring-activemq.html#security-logging-monitoring-configure-cloudwatch-permissions
        const policyDocument = {
            Version: '2012-10-17',
            Statement: [
                {
                    Effect: 'Allow',
                    Principal: { Service: 'mq.amazonaws.com' },
                    Action: ['logs:CreateLogStream', 'logs:PutLogEvents'],
                    Resource: 'arn:aws:logs:*:*:log-group:/aws/amazonmq/*',
                },
            ],
        };
        new aws_logs.CfnResourcePolicy(this, `${props.brokerName}-AmazonMQ-Log-Policy`, {
            policyName: `${props.brokerName}-AmazonMQ-Log-Policy`,
            policyDocument: JSON.stringify(policyDocument),
        });

        switch (props.activeMQMeshNode.deploymentMode) {
            case 'SINGLE_INSTANCE':
                console.log('Handling Single Instance Deployment Mode');
                this.broker = this.createActiveMQBroker(props, subnetIds.slice(0, 1));
                break;
            case 'ACTIVE_STANDBY_MULTI_AZ':
                console.log('Handling Active-Standby Multi-AZ Deployment Mode');
                this.broker = this.createActiveMQBroker(props, subnetIds.slice(0, 2));
                break;
            default:
                throw new Error(
                    `Do not support the provided deployment mode: ${props.activeMQMeshNode.deploymentMode}`
                );
        }
    }

    private createActiveMQBroker(props: ActiveMQMeshNodeConstructProps, subnetIds: string[]): CfnBroker {
        // Do NOT specify a name as it affects the ability to replace the instance inline
        const brokerName = `${props.brokerName}-activemq-broker`;
        return new CfnBroker(this, brokerName, {
            autoMinorVersionUpgrade: props.activeMQMeshNode.autoMinorVersionUpgrade,
            brokerName: brokerName,
            deploymentMode: props.activeMQMeshNode.deploymentMode,
            engineType: 'ACTIVEMQ',
            engineVersion: props.activeMQMeshNode.engineVersion,
            hostInstanceType: props.activeMQMeshNode.hostInstanceType,
            publiclyAccessible: false,
            users: props.users,
            subnetIds: subnetIds,
            securityGroups: [props.securityGroup.securityGroupId],
            logs: {
                audit: true,
                general: true,
            },
        });
    }
}
