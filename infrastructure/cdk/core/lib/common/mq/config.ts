import { JSONObject, optional, required } from 'ts-json-object';
import { AwsSecret } from '../asm/config';

export type DeploymentMode = 'SINGLE_INSTANCE' | 'ACTIVE_STANDBY_MULTI_AZ' | 'CLUSTER_MULTI_AZ';

export class <PERSON>MQUser extends J<PERSON><PERSON>Object {
    @required
    userName: string;
    @required
    consoleAccess: boolean;
    @required
    secret: AwsSecret;
}

export class BaseActiveMQ extends JSONObject {
    @optional('mq.t3.micro')
    hostInstanceType: string;
    @optional('SINGLE_INSTANCE')
    deploymentMode: DeploymentMode;
    @optional(true)
    autoMinorVersionUpgrade: boolean;
    @optional('5.17.3')
    engineVersion: string;
}

export class ActiveMQ extends BaseActiveMQ {
    @required
    brokerName: string;
    @required
    configurationFilePath: string;
    @required
    users: Array<ActiveMQUser>;
    @optional([])
    additionalAllowedCIDRs: string[];
}

export class ActiveMQMeshNode extends BaseActiveMQ {}

export class ActiveMQ<PERSON>esh extends JSONObject {
    @required
    meshCount: number;
    @required
    users: Array<ActiveMQUser>;
    @required
    baseBrokerName: string;
    @required
    configurationFilePath: string;
    @required
    activeMQMeshNode: ActiveMQMeshNode;
    @optional([])
    additionalAllowedCIDRs: string[];
}
