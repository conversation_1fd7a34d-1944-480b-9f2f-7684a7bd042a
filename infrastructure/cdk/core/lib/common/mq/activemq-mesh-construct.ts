import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { ActiveMQMesh } from './config';
import { CfnBroker, CfnConfiguration, CfnConfigurationAssociation } from 'aws-cdk-lib/aws-amazonmq';
import { readFileSync } from 'fs';
import { ActiveMQUserConstruct } from './activemq-user-construct';
import { Fn } from 'aws-cdk-lib';
import { ActiveMQMeshNodeConstruct } from './activemq-mesh-node-construct';
import { Peer, Port, SecurityGroup } from 'aws-cdk-lib/aws-ec2';

export interface ActiveMQMeshConstructProps {
    activeMQMesh: ActiveMQMesh;
    networkCIDR: string;
    vpc: ec2.IVpc;
    vpcSubnets: ec2.SubnetSelection;
}

// https://jbcodeforce.github.io/aws-messaging-study/activemq/#mesh
// https://docs.aws.amazon.com/amazon-mq/latest/developer-guide/network-of-brokers.html#nob-topologies
export class ActiveMQMeshConstruct extends Construct {
    readonly openWirePort = 61617;
    readonly consolePort = 8162;
    readonly stompPort = 61614;

    constructor(scope: Construct, id: string, props: ActiveMQMeshConstructProps) {
        super(scope, id);

        const users = this.createUsers(props);
        if (users.length == 0) {
            throw new Error('No users have been defined for mesh.');
        }
        const securityGroup = this.createSecurityGroup(props);
        const brokers = this.createBrokers(props, securityGroup, users);
        brokers.forEach((broker) => {
            this.createBrokerConfiguration(props, broker, brokers, users[0]);
        });
    }

    private createBrokers(
        props: ActiveMQMeshConstructProps,
        securityGroup: ec2.ISecurityGroup,
        users: Array<CfnBroker.UserProperty>
    ): Array<CfnBroker> {
        const activeMQBrokers = [];
        for (let i = 0; i < props.activeMQMesh.meshCount; i++) {
            const brokerName = `${props.activeMQMesh.baseBrokerName}-${i}`;
            const activeMQBroker = new ActiveMQMeshNodeConstruct(this, brokerName, {
                activeMQMeshNode: props.activeMQMesh.activeMQMeshNode,
                vpc: props.vpc,
                networkCIDR: props.networkCIDR,
                vpcSubnets: props.vpcSubnets,
                brokerName: brokerName,
                users: users,
                securityGroup: securityGroup,
            });
            activeMQBrokers.push(activeMQBroker.broker);
        }
        return activeMQBrokers;
    }

    private createUsers(props: ActiveMQMeshConstructProps): Array<CfnBroker.UserProperty> {
        const users: CfnBroker.UserProperty[] = [];
        for (const user of props.activeMQMesh.users) {
            const activeMQUser = new ActiveMQUserConstruct(this, `${user.userName}`, {
                user: user,
            });
            users.push(activeMQUser.user);
        }
        return users;
    }

    private createBrokerConfiguration(
        props: ActiveMQMeshConstructProps,
        broker: CfnBroker,
        brokers: Array<CfnBroker>,
        user: CfnBroker.UserProperty
    ) {
        let networkConnectors = '';
        const username = user.username;

        // Iterate over other brokers to establish network connections
        brokers.forEach((otherBroker, otherIndex) => {
            if (broker.attrId !== otherBroker.attrId) {
                const otherBrokerEndpoints = Fn.join(',', otherBroker.attrOpenWireEndpoints);

                // Create network connections using broker endpoints
                networkConnectors += `
  <networkConnector conduitSubscriptions="false" consumerTTL="1" messageTTL="-1" name="QueueConnectorConnectingToBroker${
      otherIndex + 1
  }" uri="masterslave:(${otherBrokerEndpoints})" userName="${username}">
    <excludedDestinations>
      <topic physicalName="&gt;"/>
    </excludedDestinations>
  </networkConnector>
  <networkConnector conduitSubscriptions="true" consumerTTL="1" messageTTL="-1" name="TopicConnectorConnectingToBroker${
      otherIndex + 1
  }" uri="masterslave:(${otherBrokerEndpoints})" userName="${username}">
    <excludedDestinations>
      <queue physicalName="&gt;"/>
    </excludedDestinations>
  </networkConnector>`;
            }
        });

        const resolvedNetworkConnectors = networkConnectors
            ? `<networkConnectors>${networkConnectors}</networkConnectors>`
            : '';

        const activemqConf = readFileSync(props.activeMQMesh.configurationFilePath, { encoding: 'utf8' });
        const activemqConfNetwork = Fn.sub(activemqConf, {
            networkConnectors: resolvedNetworkConnectors,
        });
        const activemqConfBase64 = Fn.base64(activemqConfNetwork);

        const brokerName = broker.brokerName;
        const configuration = new CfnConfiguration(this, `${brokerName}-configuration`, {
            data: activemqConfBase64,
            engineType: 'ACTIVEMQ',
            engineVersion: props.activeMQMesh.activeMQMeshNode.engineVersion,
            name: `${brokerName}-configuration`,
            description: `Custom Unblocked configuration for ${brokerName} on ActiveMQ 5.17.6`,
        });

        new CfnConfigurationAssociation(this, `${brokerName}-configuration-association`, {
            broker: broker.ref,
            configuration: {
                id: configuration.ref,
                revision: configuration.attrRevision,
            },
        });
    }

    private createSecurityGroup(props: ActiveMQMeshConstructProps): ec2.ISecurityGroup {
        const securityGroupName = `${props.activeMQMesh.baseBrokerName}-sg`;
        const sg = new SecurityGroup(this, securityGroupName, {
            vpc: props.vpc,
            securityGroupName: securityGroupName,
            description: 'security group for activeMQ broker',
        });

        const peers = props.activeMQMesh.additionalAllowedCIDRs.map((cidr) => Peer.ipv4(cidr));
        peers.push(Peer.ipv4(props.networkCIDR));
        peers.push(sg);

        for (const peer of peers) {
            sg.addIngressRule(peer, Port.tcp(this.openWirePort), `allow inbound broker traffic from ${peer.uniqueId}`);
            sg.addIngressRule(
                peer,
                Port.tcp(this.stompPort),
                `allow inbound stomp broker traffic from ${peer.uniqueId}`
            );
            sg.addIngressRule(peer, Port.tcp(this.consolePort), `allow inbound console traffic from ${peer.uniqueId}`);
        }

        return sg;
    }
}
