import { Construct } from 'constructs';
import { ActiveMQUser } from './config';
import { CfnBroker } from 'aws-cdk-lib/aws-amazonmq';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { CfnOutput } from 'aws-cdk-lib';

export interface ActiveMQUserConstructProps {
    user: ActiveMQUser;
}

export class ActiveMQUserConstruct extends Construct {
    readonly user: CfnBroker.UserProperty;

    constructor(scope: Construct, id: string, props: ActiveMQUserConstructProps) {
        super(scope, id);

        const userPassword = new secretsmanager.Secret(this, props.user.secret.secretName, {
            secretName: `${props.user.secret.secretName}`,
            generateSecretString: {
                excludeCharacters: props.user.secret.excludeCharacters,
                excludePunctuation: props.user.secret.excludePunctuation,
                passwordLength: props.user.secret.passwordLength,
            },
        });

        new CfnOutput(this, `secret-arn-for-${props.user.userName}`, {
            value: `${userPassword.secretArn}`,
        });

        this.user = {
            consoleAccess: props.user.consoleAccess,
            username: props.user.userName,
            password: userPassword.secretValue.toString(),
        };
    }
}
