import { Construct } from 'constructs';
import { CloudWatchAgent } from './config';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as fs from 'fs';
import { Fn } from 'aws-cdk-lib';

export interface CloudWatchAgentConstructProps {
    cloudWatchAgent: CloudWatchAgent;
}

export class CloudwatchAgentConstruct extends Construct {
    public readonly ssmParameter: ssm.StringParameter;
    public readonly userData: string;

    constructor(scope: Construct, id: string, props: CloudWatchAgentConstructProps) {
        super(scope, id);

        const cloudWatchAgentConfigRaw = fs.readFileSync(props.cloudWatchAgent.agentConfigPath, 'utf-8');
        const cloudWatchAgentConfig = Fn.sub(cloudWatchAgentConfigRaw, props.cloudWatchAgent.variables);

        const ssmParameter = new ssm.StringParameter(this, `${props.cloudWatchAgent.name}SSM`, {
            parameterName: props.cloudWatchAgent.agentConfigSSM.parameterName,
            stringValue: cloudWatchAgentConfig,
        });

        const userData = `
sudo yum -y install amazon-cloudwatch-agent
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c ssm:${ssmParameter.parameterName} -s
        `;

        this.ssmParameter = ssmParameter;
        this.userData = userData;
    }
}
