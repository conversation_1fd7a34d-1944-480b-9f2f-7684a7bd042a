import { array, JSONObject, optional, required } from 'ts-json-object';
import { SSMParameter } from '../ssm/config';

export type CloudWatchStatistic = 'Minimum' | 'Maximum' | 'Average' | 'Sum';
export type CloudWatchComparisonOperator =
    | 'GreaterThanUpperThreshold'
    | 'LessThanLowerThreshold'
    | 'GreaterThanThreshold'
    | 'LessThanThreshold'
    | 'LessThanOrEqualToThreshold'
    | 'LessThanLowerOrGreaterThanUpperThreshold'
    | 'GreaterThanOrEqualToThreshold';

export class CloudWatchAgent extends JSONObject {
    @required
    name: string;
    @required
    agentConfigSSM: SSMParameter;
    @required
    agentConfigPath: string;
    @required
    variables: Record<string, string>;
}

export class CloudWatchMetric extends JSONObject {
    @required
    namespace: string;
    @required
    metricName: string;
    @required
    dimensionsMap: Record<string, string>;
    @optional(180)
    periodInSeconds: number;
    @optional(undefined)
    statistic?: CloudWatchStatistic;
}

export class CloudWatchAlarm extends JSONObject {
    @required
    name: string;
    @required
    description: string;
    @optional(1)
    threshold: number;
    @optional(1)
    evaluationPeriods: number;
    @required
    cloudWatchMetric: CloudWatchMetric;
    @required
    comparisonOperator: CloudWatchComparisonOperator;
}

export class LogMetricAlarm extends JSONObject {
    @required
    filterPattern: string;
    @optional('1')
    metricValue: string;
    @optional('1')
    defaultValue: string;
    @required
    alarm: CloudWatchAlarm;
}

export class AccountInfo extends JSONObject {
    @required
    name: string;
    @required
    id: string;
}

export class AwsSecurityAlarms extends JSONObject {
    @required
    logGroupArn: string;
    @required
    snsTopicArn: string;
    @optional([])
    @array(LogMetricAlarm)
    logMetricAlarms: Array<LogMetricAlarm>;
    @required
    logMetricDimensions: Record<string, string>;
    @required
    targetAccounts: Array<AccountInfo>;
}
