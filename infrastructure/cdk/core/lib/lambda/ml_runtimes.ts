import * as cdk from 'aws-cdk-lib';
import { Stack, StackProps } from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { LambdaConstruct } from '../common/lambda/lambda-construct';
import { LambdaHelpers } from '../common/utils/lambda-helpers';

interface MlRuntimeLambdaStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
}

export interface MlRuntimeLambdaStackProps extends StackProps {
    buildConfig: MlRuntimeLambdaStackConfig;
}

export class MlRuntimeLambdaStack extends Stack {
    constructor(scope: Construct, id: string, props: MlRuntimeLambdaStackProps) {
        super(scope, id, props);

        const region = props.buildConfig.awsEnvAccount.targetRegion;
        const lambdaFunctionName = `ml-runtime-aws-live-query-${props.buildConfig.cdkAppInfo.environment}-${region}`;

        const AWSLiveQueryRuntimeRole = new iam.Role(this, `${lambdaFunctionName}-Role`, {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            description: 'Role assumed by AWS ML runtime lambda function ',
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
        });

        new LambdaConstruct(this, lambdaFunctionName, {
            name: lambdaFunctionName,
            runtime: LambdaHelpers.findLambdaRuntime('PYTHON_3_13'),
            lambdaPath: 'assets/ml_runtime_lambda/aws_live_query',
            handler: 'main.lambda_handler',
            region: region,
            timeout: cdk.Duration.minutes(1),
            lambdaRole: AWSLiveQueryRuntimeRole,
        });
    }
}
