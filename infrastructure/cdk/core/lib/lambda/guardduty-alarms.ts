import * as cdk from 'aws-cdk-lib';
import { Stack, StackProps } from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { LambdaConstruct } from '../common/lambda/lambda-construct';
import { LambdaHelpers } from '../common/utils/lambda-helpers';

interface GuardDutyAlarmLambdaStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
}

export interface GuardDutyAlarmLambdaStackProps extends StackProps {
    buildConfig: GuardDutyAlarmLambdaStackConfig;
}

export class GuardDutyAlarmLambdaStack extends Stack {
    constructor(scope: Construct, id: string, props: GuardDutyAlarmLambdaStackProps) {
        super(scope, id, props);

        const region = props.buildConfig.awsEnvAccount.targetRegion;
        const accountId = props.buildConfig.awsEnvAccount.awsAccountID;
        const lambdaFunctionName = `guard-duty-alarms-${props.buildConfig.cdkAppInfo.environment}-${region}`;

        // Note the secret SlackAlarmWebhookURL and GrafanaWebhookUrl must be added to AWS Secret Manager manually.
        // Secrets are stored under Secops AWS account in us-west-2
        const lambdaRole = new iam.Role(this, `${lambdaFunctionName}-Role`, {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            description: 'Role assumed by GuardDuty Alarms lambda function ',
            inlinePolicies: {
                CloudWatchLogGroupRetention: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            resources: [
                                `arn:aws:secretsmanager:${region}:${accountId}:secret:SlackAlarmWebhookURL-*`,
                                `arn:aws:secretsmanager:${region}:${accountId}:secret:GrafanaWebhookUrl-*`,
                            ],
                            actions: ['secretsmanager:GetSecretValue'],
                        }),
                    ],
                }),
            },
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
        });

        new LambdaConstruct(this, lambdaFunctionName, {
            name: lambdaFunctionName,
            runtime: LambdaHelpers.findLambdaRuntime('PYTHON_3_13'),
            lambdaPath: 'assets/lambda/guardduty-alarms',
            handler: 'main.lambda_handler',
            region: region,
            timeout: cdk.Duration.minutes(1),
            lambdaRole: lambdaRole,
        });
    }
}
