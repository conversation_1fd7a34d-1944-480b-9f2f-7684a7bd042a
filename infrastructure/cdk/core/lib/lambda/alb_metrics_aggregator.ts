import * as cdk from 'aws-cdk-lib';
import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { LambdaConstruct } from '../common/lambda/lambda-construct';
import { LambdaHelpers } from '../common/utils/lambda-helpers';

interface AlbMetricsAggregatorLambdaStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
}

export interface AlbMetricsAggregatorLambdaStackProps extends StackProps {
    buildConfig: AlbMetricsAggregatorLambdaStackConfig;
}

export class AlbMetricsAggregatorLambdaStack extends Stack {
    constructor(scope: Construct, id: string, props: AlbMetricsAggregatorLambdaStackProps) {
        super(scope, id, props);

        const region = props.buildConfig.awsEnvAccount.targetRegion;
        const lambdaFunctionName = `alb-metrics-aggregator-${props.buildConfig.cdkAppInfo.environment}-${region}`;

        const AlbMetricsAggregatorRole = new iam.Role(this, `${lambdaFunctionName}-Role`, {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            description: 'Role assumed by AWS ML runtime lambda function ',
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
        });

        AlbMetricsAggregatorRole.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'elasticloadbalancing:DescribeTargetGroups',
                    'elasticloadbalancing:DescribeTags',
                    'cloudwatch:GetMetricData',
                    'cloudwatch:PutMetricData',
                    'cloudwatch:ListMetrics',
                    'cloudwatch:GetMetricStatistics',
                    'elasticloadbalancing:Describe*',
                ],
                resources: ['*'],
            })
        );

        const metricAggFunction = new LambdaConstruct(this, lambdaFunctionName, {
            name: lambdaFunctionName,
            runtime: LambdaHelpers.findLambdaRuntime('PYTHON_3_13'),
            lambdaPath: 'assets/lambda/alb-metrics-aggregator',
            handler: 'main.lambda_handler',
            environment: {
                TG_TAG_KEY: 'ingress.k8s.aws/resource',
                DEBUG: 'false',
            },
            region: region,
            timeout: cdk.Duration.minutes(1),
            lambdaRole: AlbMetricsAggregatorRole,
        });

        const periodicRule = new events.Rule(this, `${lambdaFunctionName}PeriodicRule`, {
            schedule: events.Schedule.rate(Duration.minutes(1)),
            targets: [new targets.LambdaFunction(metricAggFunction.lambdaFunction)],
        });
    }
}
