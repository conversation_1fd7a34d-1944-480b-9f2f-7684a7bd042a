import { Stack, StackProps, CfnOutput, RemovalPolicy } from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Dns } from '../common/dns/config';
import { StaticSite } from './config';
import { S3Helpers } from '../common/utils/s3-helpers';

interface StaticSiteStackConfig {
    awsEnvAccount: AwsEnvAccount;
    cdkAppInfo: CdkAppInfo;
    dns: Dns;
    staticSites: Array<StaticSite>;
}

export interface StaticSiteStackProps extends StackProps {
    buildConfig: StaticSiteStackConfig;
}

export class StaticSiteStack extends Stack {
    readonly rootSiteBucket: s3.Bucket;
    readonly buckets = new Map<string, s3.Bucket>();
    readonly errorAssetsBucket: s3.Bucket; // public bucket to serve error images

    constructor(scope: Construct, id: string, props: StaticSiteStackProps) {
        super(scope, id, props);

        for (const siteConfig of props.buildConfig.staticSites) {
            /**
             *  S3 Bucket Setup
             */
            const siteBucket = new s3.Bucket(this, `SiteBucket-${siteConfig.name}`, {
                bucketName: siteConfig.s3BucketName,
                publicReadAccess: false,
                blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
                removalPolicy: RemovalPolicy.RETAIN,
                autoDeleteObjects: false,
                versioned: siteConfig.enableVersioning,
            });

            if (siteConfig.corsRule) {
                const corsRule = S3Helpers.generateCorsRule(siteConfig.corsRule);
                siteBucket.addCorsRule(corsRule);
            }

            this.buckets.set(siteConfig.pathPattern, siteBucket);
            new ssm.StringParameter(this, `${siteConfig.name}`, {
                parameterName: `/static-site-stack/${siteConfig.name}`,
                stringValue: siteBucket.bucketArn,
            });
            if (siteConfig.pathPattern === '/*') {
                this.rootSiteBucket = siteBucket;
            }

            // Create role to be assumed by deploy user in root account
            const deployerRole = new iam.Role(this, `S3StaticSiteDeployerRole-${siteConfig.name}`, {
                roleName: `S3StaticSiteDeployerRole-${siteConfig.name}`,
                assumedBy: new iam.AccountPrincipal(props.buildConfig.awsEnvAccount.awsOrgRootAccountID),
                description: 'Role for granting read, write, list and delete permissions to static site s3 bucket',
            });

            // Had to do this to avoid re-creating the role
            new ssm.StringParameter(this, `${siteConfig.name}-deployer-role`, {
                parameterName: `/static-site-stack/${siteConfig.name}-deployer-role`,
                stringValue: deployerRole.roleArn,
            });

            new CfnOutput(this, `Bucket-${siteConfig.name}`, { value: siteBucket.bucketName });
            this.exportValue(siteBucket.bucketName);

            // Add policy to list cf distros and manage invalidation to deployer role
            deployerRole.addToPrincipalPolicy(
                new iam.PolicyStatement({
                    resources: ['*'],
                    actions: [
                        'cloudfront:CreateInvalidation',
                        'cloudfront:GetInvalidation',
                        'cloudfront:ListInvalidations',
                        'cloudfront:ListDistributions',
                    ],
                })
            );
        }

        // Public bucket to hold assets returned in error cases - default origin for /assets
        this.errorAssetsBucket = new s3.Bucket(this, `StaticErrorAssetsBucket`, {
            bucketName: `static-error-assets-${props.buildConfig.cdkAppInfo.environment}-${props.buildConfig.awsEnvAccount.targetRegion}`,
            bucketKeyEnabled: false,
            //publicReadAccess: true,
            accessControl: s3.BucketAccessControl.PUBLIC_READ,
            removalPolicy: RemovalPolicy.RETAIN,
            autoDeleteObjects: false,
            versioned: true,
        });

        new ssm.StringParameter(this, 'errorAssetsBucket', {
            parameterName: '/static-site-stack/errorAssetsBucket',
            stringValue: this.errorAssetsBucket.bucketArn,
        });
    }
}
