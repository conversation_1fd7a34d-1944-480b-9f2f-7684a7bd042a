import { Stack, StackProps, CfnOutput } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import {
    CfnCacheCluster,
    CfnReplicationGroup,
    CfnSubnetGroup,
    CfnUser,
    CfnUserGroup,
} from 'aws-cdk-lib/aws-elasticache';
import { NetworkStack } from '../vpc/network-stack';
import { Peer, Port, SecurityGroup } from 'aws-cdk-lib/aws-ec2';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { VPCConfig } from '../common/vpc/config';
import { Redis } from '../common/elasticache/config';

interface RedisStackConfig {
    awsEnvAccount: AwsEnvAccount;
    coreVPC: VPCConfig;
    redis: Redis;
    cdkAppInfo: CdkAppInfo;
}

export interface RedisStackProps extends StackProps {
    buildConfig: RedisStackConfig;
    networkStack: NetworkStack;
}

export class RedisStack extends Stack {
    readonly redisSubnetGroup: CfnSubnetGroup;
    readonly ecRedisSG: SecurityGroup;
    readonly ecRedisCache: CfnCacheCluster;
    readonly ecRedisReplicationGroup: CfnReplicationGroup;

    constructor(scope: Construct, id: string, props: RedisStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping RedisStack.');
            return;
        }

        this.addDependency(props.networkStack);

        const redisConfig = props.buildConfig.redis;

        // Create each user and put the password in secret manager
        /*
            --IMPORTANT--
            Source: https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/Clusters.RBAC.html
            ElastiCache automatically configures a default user with user ID and user name "default" and adds it to all user groups.
            You can't modify or delete this user. This user has an access string that permits it to call all commands and access all keys.
            To add proper access control to a cluster, replace this default user with a new one that isn't enabled or uses a strong password.
            To change the default user, create a new user with the user name set to default. You can then swap it with the original default user.
        */
        const userIDs: string[] = [];
        const usersObjects: CfnUser[] = [];
        for (const user of redisConfig.users) {
            const passwords = user.secrets.map(
                (secret) =>
                    new secretsmanager.Secret(this, secret.secretName, {
                        secretName: secret.secretName,
                        generateSecretString: {
                            excludeCharacters: secret.excludeCharacters,
                            excludePunctuation: secret.excludePunctuation,
                            passwordLength: secret.passwordLength,
                        },
                    })
            );

            const cfnUser = new CfnUser(this, `redis-${user.userName}`, {
                engine: 'redis',
                userId: user.isDefaultUser ? 'default-cdk' : user.userName,
                userName: user.userName,
                accessString: user.accessString,
                noPasswordRequired: false,
                passwords: passwords.map((password) => password.secretValue.toString()),
            });
            userIDs.push(cfnUser.userId);
            usersObjects.push(cfnUser);

            const secretArns = passwords.map((secret) => secret.secretArn).join(', ');
            new CfnOutput(this, `secret-arn-for-${user.userName}`, {
                value: `${secretArns}`,
            });
        }

        // Create user group
        const cfnUserGroup = new CfnUserGroup(this, 'redis-users-group', {
            engine: 'redis',
            userGroupId: 'redis-users',
            userIds: userIDs,
        });

        usersObjects.forEach((u) => cfnUserGroup.addDependency(u));

        // Create subnet group
        const subnetIds: string[] = [];
        props.networkStack.coreVpc.isolatedSubnets.forEach((subnet) => subnetIds.push(subnet.subnetId));
        this.redisSubnetGroup = new CfnSubnetGroup(this, 'RedisClusterIsolatedSubnetGroup', {
            cacheSubnetGroupName: 'ecRedisIsolatedSubnetGroup',
            subnetIds: subnetIds,
            description: 'list of isolated subnet ids to be used for redis',
        });

        this.ecRedisSG = new SecurityGroup(this, 'ecRedisSG', {
            vpc: props.networkStack.coreVpc,
            securityGroupName: 'ecRedisSG',
            description: 'security group for a elasticache redis',
        });

        for (const cidr of redisConfig.additionalAllowedCIDRs) {
            this.ecRedisSG.addIngressRule(Peer.ipv4(cidr), Port.tcp(6379), `allow inbound traffic from ${cidr}`);
        }

        this.ecRedisReplicationGroup = new CfnReplicationGroup(this, `RedisReplicaGroup`, {
            engine: 'redis',
            atRestEncryptionEnabled: true,
            transitEncryptionEnabled: true,
            cacheParameterGroupName: redisConfig.cacheParameterGroupName,
            cacheNodeType: redisConfig.cacheNodeType,
            replicasPerNodeGroup: redisConfig.replicasPerNodeGroup,
            numNodeGroups: redisConfig.numNodeGroups,
            automaticFailoverEnabled: redisConfig.automaticFailoverEnabled,
            autoMinorVersionUpgrade: redisConfig.autoMinorVersionUpgrade,
            engineVersion: redisConfig.engineVersion,
            multiAzEnabled: redisConfig.multiAzEnabled,
            replicationGroupDescription: 'elasticache redis cluster replication group',
            securityGroupIds: [this.ecRedisSG.securityGroupId],
            cacheSubnetGroupName: this.redisSubnetGroup.cacheSubnetGroupName,
            userGroupIds: [cfnUserGroup.userGroupId],
        });

        this.ecRedisReplicationGroup.addDependency(this.redisSubnetGroup);
        this.ecRedisReplicationGroup.addDependency(cfnUserGroup);
    }
}
