import * as cdk from 'aws-cdk-lib';
import { Stack, StackProps } from 'aws-cdk-lib';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import { LambdaConstruct } from '../common/lambda/lambda-construct';
import { LambdaHelpers } from '../common/utils/lambda-helpers';

interface CloudwatchEc2AlarmsConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
}

export interface CloudwatchEc2AlarmsStackProps extends StackProps {
    buildConfig: CloudwatchEc2AlarmsConfig;
}

export class CloudwatchEc2AlarmsStack extends Stack {
    constructor(scope: Construct, id: string, props: CloudwatchEc2AlarmsStackProps) {
        super(scope, id, props);

        const snsTopic = new sns.Topic(this, `EC2-ASG-CPUUtilization-Alarms`, {
            displayName: `Topic to deliver alarms related to ASG CPUUtilization`,
        });

        snsTopic.addSubscription(
            new subscriptions.EmailSubscription(props.buildConfig.awsEnvAccount.opsNotificationEmail)
        );

        // Role to grant Lambda function enough permission to list and modify retention on log groups
        const cloudwatchLogRetentionLambdaRole = new iam.Role(
            this,
            `CloudwatchEc2Alarms-${props.buildConfig.awsEnvAccount.targetRegion}-lambda-role`,
            {
                roleName: `CloudwatchEc2Alarms-${props.buildConfig.awsEnvAccount.targetRegion}-lambda-role`,
                assumedBy: new iam.CompositePrincipal(new iam.ServicePrincipal('lambda.amazonaws.com')),
                description: 'Role assumed by lambda function to setup CPU alarms for ASGs',
                managedPolicies: [
                    iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
                ],
                inlinePolicies: {
                    CloudWatchLogGroupRetention: new iam.PolicyDocument({
                        statements: [
                            new iam.PolicyStatement({
                                resources: ['*'],
                                actions: ['ecs:ListClusters', 'ecs:ListTagsForResource'],
                            }),
                            new iam.PolicyStatement({
                                resources: ['*'],
                                actions: ['autoscaling:DescribeAutoScalingGroups'],
                            }),
                            new iam.PolicyStatement({
                                resources: ['*'],
                                actions: [
                                    'cloudwatch:PutMetricAlarm',
                                    'cloudwatch:DescribeAlarms',
                                    'cloudwatch:DeleteAlarms',
                                    'cloudwatch:TagResource',
                                    'cloudwatch:ListTagsForResource',
                                ],
                            }),
                        ],
                    }),
                },
            }
        );

        const ec2AsgAlarmLambda = new LambdaConstruct(this, 'CloudwatchEc2AlarmsLambda', {
            name: 'CloudwatchEc2AlarmsLambda',
            runtime: LambdaHelpers.findLambdaRuntime('PYTHON_3_9'),
            lambdaPath: 'assets/lambda/cloudwatch-ec2-cpu-alarms',
            handler: 'main.handler',
            region: props.buildConfig.awsEnvAccount.targetRegion,
            timeout: cdk.Duration.minutes(3),
            lambdaRole: cloudwatchLogRetentionLambdaRole,
            environment: {
                AWS_ACCOUNT_ID: props.buildConfig.awsEnvAccount.awsAccountID,
                SNS_TOPIC_ARN: snsTopic.topicArn,
                DRYRUN: 'false',
            },
        });

        // Run once every hour
        const eventRule = new events.Rule(this, 'CloudwatchEc2AlarmsLambdaScheduleRule', {
            ruleName: `CloudwatchEc2AlarmsLambdaScheduleRule${props.buildConfig.awsEnvAccount.targetRegion}`,
            schedule: events.Schedule.cron({ minute: '1' }),
        });
        eventRule.addTarget(new targets.LambdaFunction(ec2AsgAlarmLambda.lambdaFunction));
    }
}
