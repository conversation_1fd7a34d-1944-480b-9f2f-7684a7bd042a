import { Construct } from 'constructs';
import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { MetricFilter, LogGroup, FilterPattern } from 'aws-cdk-lib/aws-logs';
import { AwsSecurityAlarms } from '../common/cloudwatch/config';

interface CloudTrailAlarmsStackConfig {
    awsEnvAccount: AwsEnvAccount;
    cdkAppInfo: CdkAppInfo;
    awsSecurityAlarms: AwsSecurityAlarms;
}

export interface CloudTrailAlarmsStackProps extends StackProps {
    buildConfig: CloudTrailAlarmsStackConfig;
}

export class CloudTrailAlarmsStack extends Stack {
    constructor(scope: Construct, id: string, props: CloudTrailAlarmsStackProps) {
        super(scope, id, props);

        const awsSecurityAlarms = props.buildConfig.awsSecurityAlarms;
        if (!awsSecurityAlarms) {
            console.warn('no alarm configs provided. skipping route setup.');
            return;
        }

        // CloudWatch log group for CloudTrail logs - manually created via AWS Console
        const logGroup = LogGroup.fromLogGroupArn(
            this,
            'management-events-global-cloudtrail',
            awsSecurityAlarms.logGroupArn
        );

        // SNS topic for alarms - manually created via AWS Console
        ///const topic = new sns.Topic(this, 'Topic');
        const topic = sns.Topic.fromTopicArn(this, 'SecOps-CloudTrail-Alarms', awsSecurityAlarms.snsTopicArn);

        for (const filter of awsSecurityAlarms.logMetricAlarms) {
            const replaceFirstElement = (obj: Record<string, string>, newValue: string): Record<string, string> => {
                const firstKey = Object.keys(obj)?.[0]; // Get the first key if it exists
                return firstKey ? { ...obj, [firstKey]: newValue } : obj;
            };

            const metricFilter = new MetricFilter(this, `${filter.alarm.cloudWatchMetric.metricName}-MetricFilter`, {
                logGroup: logGroup,
                metricNamespace: filter.alarm.cloudWatchMetric.namespace,
                metricName: filter.alarm.cloudWatchMetric.metricName,
                filterPattern: FilterPattern.literal(filter.filterPattern),
                metricValue: filter.metricValue,
                // ID of account where the event has occurred
                dimensions: awsSecurityAlarms.logMetricDimensions,
            });

            for (const account of awsSecurityAlarms.targetAccounts) {
                const alarm = new cloudwatch.Alarm(
                    this,
                    `${filter.alarm.cloudWatchMetric.metricName}-${account.name}-Alarm`,
                    {
                        alarmName: `${filter.alarm.name}-${account.name}-Alarm`,
                        alarmDescription:
                            filter.alarm.description + `\nAccountName: ${account.name}\nAccountID: ${account.id}\n`,
                        comparisonOperator: filter.alarm.comparisonOperator as cloudwatch.ComparisonOperator, //cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
                        threshold: filter.alarm.threshold,
                        evaluationPeriods: filter.alarm.evaluationPeriods,
                        metric: metricFilter.metric({
                            // ID of the account where the alarm is created
                            account: props.buildConfig.awsEnvAccount.awsAccountID,
                            statistic: filter.alarm.cloudWatchMetric.statistic,
                            period: Duration.seconds(filter.alarm.cloudWatchMetric.periodInSeconds),
                            // ID of account where the event has occurred
                            dimensionsMap: replaceFirstElement(awsSecurityAlarms.logMetricDimensions, account.id),
                        }),
                        actionsEnabled: true,
                    }
                );
                alarm.addAlarmAction(new cw_actions.SnsAction(topic));
            }
        }
    }
}
