import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as path from 'path';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';

interface CloudwatchGlobalLogRetentionConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
}

export interface CloudwatchGlobalLogRetentionStackProps extends StackProps {
    buildConfig: CloudwatchGlobalLogRetentionConfig;
}

export class CloudwatchGlobalLogRetentionStack extends Stack {
    constructor(scope: Construct, id: string, props: CloudwatchGlobalLogRetentionStackProps) {
        super(scope, id, props);

        // Role to grant Lambda function enough permission to list and modify retention on log groups
        const cloudwatchLogRetentionLambdaRole = new iam.Role(
            this,
            `CloudwatchGlobalLogRetention-${props.buildConfig.awsEnvAccount.targetRegion}-lambda-role`,
            {
                roleName: `CloudwatchGlobalLogRetention-${props.buildConfig.awsEnvAccount.targetRegion}-lambda-role`,
                assumedBy: new iam.CompositePrincipal(new iam.ServicePrincipal('lambda.amazonaws.com')),
                description: 'Role assumed by lambda function to setup global log retention policy on all log streams ',
                managedPolicies: [
                    iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
                ],
                inlinePolicies: {
                    CloudWatchLogGroupRetention: new iam.PolicyDocument({
                        statements: [
                            new iam.PolicyStatement({
                                resources: ['*'],
                                actions: [
                                    'logs:DescribeLogGroups',
                                    'logs:DescribeLogStreams',
                                    'logs:PutRetentionPolicy',
                                ],
                            }),
                        ],
                    }),
                },
            }
        );

        // Lambda function to set log retention on every log group and log stream
        const cloudwatchLogRetentionLambdaFunction = new lambda.Function(
            this,
            `CloudwatchGlobalLogRetention${props.buildConfig.awsEnvAccount.targetRegion}`,
            {
                functionName: `CloudwatchGlobalLogRetention${props.buildConfig.awsEnvAccount.targetRegion}`,
                runtime: lambda.Runtime.PYTHON_3_9,
                memorySize: 128,
                timeout: Duration.minutes(15),
                handler: 'main.lambda_handler',
                code: lambda.Code.fromAsset(
                    path.join(__dirname, '../../assets/lambda/cloudwatch-global-logretention-policy')
                ),
                environment: {
                    RETENTION_DAYS: `${props.buildConfig.awsEnvAccount.globalLogRetentionDays}`,
                },
                role: cloudwatchLogRetentionLambdaRole,
            }
        );

        // Run once every hour
        const eventRule = new events.Rule(this, 'CloudwatchGlobalLogRetentionScheduleRule', {
            ruleName: `CloudwatchGlobalLogRetentionScheduleRule${props.buildConfig.awsEnvAccount.targetRegion}`,
            schedule: events.Schedule.cron({ minute: '1' }),
        });
        eventRule.addTarget(new targets.LambdaFunction(cloudwatchLogRetentionLambdaFunction));
    }
}
