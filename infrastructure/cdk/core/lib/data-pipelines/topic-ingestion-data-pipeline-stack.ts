import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { NetworkStack } from '../vpc/network-stack';
import { DataPipelineStateMachineConstruct } from '../common/s3/data-pipeline-state-machine-construct';
import { DataPipeline } from './config';

interface TopicIngestionDataPipelineStackConfig {
    awsEnvAccount: AwsEnvAccount;
    cdkAppInfo: CdkAppInfo;
    topicIngestionDataPipeline: DataPipeline;
}

export interface TopicIngestionDataPipelineStackProps extends StackProps {
    buildConfig: TopicIngestionDataPipelineStackConfig;
    networkStack: NetworkStack;
}

export class TopicIngestionDataPipelineStack extends Stack {
    constructor(scope: Construct, id: string, props: TopicIngestionDataPipelineStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite || !props.buildConfig.topicIngestionDataPipeline) {
            console.log('Skipping TopicIngestionDataPipelineStack.');
            return;
        }

        new DataPipelineStateMachineConstruct(this, `${props.buildConfig.topicIngestionDataPipeline.name}`, {
            awsEnvAccount: props.buildConfig.awsEnvAccount,
            cdkAppInfo: props.buildConfig.cdkAppInfo,
            stateMachine: props.buildConfig.topicIngestionDataPipeline.stateMachine,
            networkStack: props.networkStack,
        });
    }
}
