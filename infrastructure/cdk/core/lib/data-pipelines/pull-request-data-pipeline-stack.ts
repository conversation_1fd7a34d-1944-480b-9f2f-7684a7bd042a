import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { NetworkStack } from '../vpc/network-stack';
import { DataPipelineStateMachineConstruct } from '../common/s3/data-pipeline-state-machine-construct';
import { DataPipeline } from './config';

interface PullRequestDataPipelineStackConfig {
    awsEnvAccount: AwsEnvAccount;
    cdkAppInfo: CdkAppInfo;
    pullRequestDataPipeline: DataPipeline;
}

export interface PullRequestDataPipelineStackProps extends StackProps {
    buildConfig: PullRequestDataPipelineStackConfig;
    networkStack: NetworkStack;
}

export class PullRequestDataPipelineStack extends Stack {
    constructor(scope: Construct, id: string, props: PullRequestDataPipelineStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite || !props.buildConfig.pullRequestDataPipeline) {
            console.log('Skipping PullRequestDataPipelineStack.');
            return;
        }

        new DataPipelineStateMachineConstruct(this, `${props.buildConfig.pullRequestDataPipeline.name}`, {
            awsEnvAccount: props.buildConfig.awsEnvAccount,
            cdkAppInfo: props.buildConfig.cdkAppInfo,
            stateMachine: props.buildConfig.pullRequestDataPipeline.stateMachine,
            networkStack: props.networkStack,
        });
    }
}
