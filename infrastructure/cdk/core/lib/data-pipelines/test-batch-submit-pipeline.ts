import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { NetworkStack } from '../vpc/network-stack';
import { DataPipelineStateMachineConstruct } from '../common/s3/data-pipeline-state-machine-construct';
import { DataPipeline } from './config';

interface TestBatchSubmitPipelineStackConfig {
    awsEnvAccount: AwsEnvAccount;
    cdkAppInfo: CdkAppInfo;
    testBatchSubmitPipeline?: DataPipeline;
}

export interface TestBatchSubmitPipelineStackProps extends StackProps {
    buildConfig: TestBatchSubmitPipelineStackConfig;
    networkStack: NetworkStack;
}

export class TestBatchSubmitPipelineStack extends Stack {
    constructor(scope: Construct, id: string, props: TestBatchSubmitPipelineStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping TestBatchSubmitPipelineStack.');
            return;
        }

        if (!props.buildConfig.testBatchSubmitPipeline) {
            console.log('Skipping TestBatchSubmitPipelineStack.');
            return;
        }

        new DataPipelineStateMachineConstruct(this, `${props.buildConfig.testBatchSubmitPipeline.name}`, {
            awsEnvAccount: props.buildConfig.awsEnvAccount,
            cdkAppInfo: props.buildConfig.cdkAppInfo,
            stateMachine: props.buildConfig.testBatchSubmitPipeline.stateMachine,
            networkStack: props.networkStack,
        });
    }
}
