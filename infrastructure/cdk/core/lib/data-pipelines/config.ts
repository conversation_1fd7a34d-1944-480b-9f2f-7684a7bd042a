import { JSONObject, optional, required } from 'ts-json-object';
import { LambdaConfig } from '../common/lambda/config';
import { DockerImageAsset } from '../common/docker/config';

import { S3Bucket } from '../common/s3/config';

export type ChoiceCondition =
    | { type: 'StringEquals'; variable: string; StringEquals: string }
    | { type: 'StringLessThan'; variable: string; StringLessThan: string }
    | { type: 'StringGreaterThan'; variable: string; StringGreaterThan: string }
    | { type: 'StringLessThanEquals'; variable: string; StringLessThanEquals: string }
    | { type: 'StringGreaterThanEquals'; variable: string; StringGreaterThanEquals: string }
    | { type: 'NumericEquals'; variable: string; NumericEquals: number }
    | { type: 'NumericLessThan'; variable: string; NumericLessThan: number }
    | { type: 'NumericGreaterThan'; variable: string; NumericGreaterThan: number }
    | { type: 'NumericLessThanEquals'; variable: string; NumericLessThanEquals: number }
    | { type: 'NumericGreaterThanEquals'; variable: string; NumericGreaterThanEquals: number }
    | { type: 'BooleanEquals'; variable: string; BooleanEquals: boolean }
    | { type: 'IsPresent'; variable: string; IsPresent: boolean }
    | { type: 'IsNotPresent'; variable: string; IsPresent: boolean };

export type ChoiceUnaryOperation = 'Not';
export type ChoiceOperation = 'And' | 'Or';

export class CompoundChoiceCondition extends JSONObject {
    @required
    condition: ChoiceCondition;
    @optional(undefined)
    operation?: ChoiceUnaryOperation;
}

export type DataPipelineEnvironmentType = 'Literal' | 'JsonPath';

export class DataPipelineEnvironment extends JSONObject {
    @required
    name: string;
    @required
    value: string;
    @required
    type: DataPipelineEnvironmentType;
}

export class DataPipelineStageChoice extends JSONObject {
    @required
    name: string;
    @required
    conditions: Array<CompoundChoiceCondition>;
    @optional(undefined)
    operation?: ChoiceOperation;
}

export class DataPipelineStageRetry extends JSONObject {
    @optional(undefined)
    backOffRate?: number;
    // Example errors found here:
    // https://docs.aws.amazon.com/step-functions/latest/dg/connect-sagemaker.html
    // https://docs.aws.amazon.com/step-functions/latest/dg/concepts-error-handling.html
    @optional(undefined)
    errors?: string[];
    @optional(undefined)
    maxAttempts?: number;
    @optional(undefined)
    intervalInSeconds?: number;
}

export class DataPipelineStageCatch extends JSONObject {
    @optional(undefined)
    errors?: string[];
    @optional(undefined)
    errorResultJsonPath?: string;
    @required
    catchStageName: string;
}

export class DataPipelineInput extends JSONObject {
    @required
    inputName: string;
    @required
    inputJsonPath: string;
}

export class DataPipelineOutput extends JSONObject {
    @required
    outputName: string;
    @required
    outputJsonPath: string;
}

export class DataPipelineTrainInput extends DataPipelineInput {
    @required
    contentType: string;
}

export class DataPipelineStage extends JSONObject {
    @required
    name: string;
    @required
    stage: string;
    @optional(undefined)
    retry?: DataPipelineStageRetry;
    @optional(undefined)
    catch?: DataPipelineStageCatch;
    @optional(undefined)
    choice?: DataPipelineStageChoice;
    @optional(60)
    timeoutInMin: number;
    @optional(false)
    enableVPCNetworking: boolean;
}

export class FailStage extends DataPipelineStage {
    @optional(undefined)
    causePathJsonPath?: string;
    @required
    errorPathJsonPath?: string;
}

export class TrainStage extends DataPipelineStage {
    @required
    trainJobNameJsonPath: string;
    @required
    trainDockerImage: DockerImageAsset;
    @optional(undefined)
    /* eslint-disable @typescript-eslint/no-explicit-any */
    trainParameters?: any;
    @required
    trainS3Inputs: Array<DataPipelineTrainInput>;
    @required
    trainS3Output: DataPipelineOutput;
    @required
    trainResultJsonPath: string;
    @optional('ml.m5.xlarge')
    trainInstanceType: string;
    @optional(undefined)
    trainEnvironmentJsonPath?: string;
}

export class LambdaStage extends DataPipelineStage {
    @required
    lambda: LambdaConfig;
    @required
    lambdaResultJsonPath: string;
    @optional(undefined)
    lambdaInputJsonPath?: string;
}

export class BatchSubmitStage extends DataPipelineStage {
    @required
    batchSubmitJobQueueArn: string;
    @required
    batchSubmitJobDefinitionArn: string;
    @required
    batchSubmitJobNameJsonPath: string;
    @required
    batchSubmitResultJsonPath: string;
    @optional(undefined)
    batchSubmitEnvironmentJsonPath?: string;
    @optional(undefined)
    batchSubmitEnvironment?: Array<DataPipelineEnvironment>;
    @optional(undefined)
    batchCompletionLambdaStage?: LambdaStage;
    @optional(undefined)
    batchCompletionFailStage?: FailStage;
}

export class ProcessStage extends DataPipelineStage {
    @required
    processJobNameJsonPath: string;
    @required
    processDockerImage: DockerImageAsset;
    @optional('ml.m5.xlarge')
    processInstanceType: string;
    @required
    processS3Inputs: Array<DataPipelineInput>;
    @required
    processS3Outputs: Array<DataPipelineOutput>;
    @required
    processResultJsonPath: string;
    @optional(undefined)
    processEnvironmentJsonPath?: string;
    @optional(undefined)
    processCompletionLambdaStage?: LambdaStage;
    @optional(undefined)
    processCompletionFailStage?: FailStage;
}

export class ModelStage extends DataPipelineStage {
    @required
    modelNameJsonPath: string;
    @required
    modelDockerImage: DockerImageAsset;
    @required
    modelS3Input: DataPipelineInput;
    @required
    modelResultJsonPath: string;
    @optional(1)
    timeoutInMin: number;
}

export class ModelEndpointStage extends DataPipelineStage {
    @required
    modelEndpointNameJsonPath: string;
    @required
    modelEndpointResultJsonPath: string;
    @required
    modelEndpointConfigNameJsonPath: string;
    @required
    modelEndpointConfigResultJsonPath: string;
    @optional(1)
    modelEndpointConfigInstanceCount: number;
    @optional('t2.medium')
    modelEndpointConfigInstanceType: string;
    @required
    modelEndpointDescribeResultJsonPath: string;
    @optional(undefined)
    modelEndpointDescribeLambda: LambdaConfig;
    @optional(undefined)
    modelEndpointInvokeLambda?: LambdaConfig;
}

export class ModelValidationStage extends DataPipelineStage {
    @required
    modelValidationLambda: LambdaConfig;
    @required
    modelValidationResultJsonPath: string;
    @optional(0.1)
    modelValidationErrorThreshold: number;
}

export class GlueStage extends DataPipelineStage {
    @optional('')
    glueJobFilePath: string;
    @optional('code/glue')
    glueJobDestinationS3KeyPrefix: string;
    @optional(2)
    glueJobNumberOfWorkers: number;
    @optional(2)
    glueJobMaxConcurrentRuns: number;
    @required
    glueArgumentsJsonPath: string;
    @required
    glueResultJsonPath: string;
}

export class SubPipelineStage extends DataPipelineStage {
    @optional(undefined)
    preProcessStage?: ProcessStage;
    @optional(undefined)
    batchSubmitStage?: BatchSubmitStage;
    @optional(undefined)
    trainStage?: TrainStage;
    @optional(undefined)
    postProcessStage?: ProcessStage;
    @optional(undefined)
    modelStage?: ModelStage;
    @optional(undefined)
    modelEndpointStage?: ModelEndpointStage;
    @optional(undefined)
    modelValidationStage?: ModelValidationStage;
}

export class DataPipelineStateMachine extends JSONObject {
    @required
    name: string;
    @required
    endpointName: string;

    @optional(undefined)
    assetBucket?: S3Bucket;
    @optional(undefined)
    triggerLambda?: LambdaConfig;

    @optional(undefined)
    glueStage?: GlueStage;
    @optional(undefined)
    preProcessStages?: Array<ProcessStage>;
    @optional(undefined)
    batchSubmitStages?: Array<BatchSubmitStage>;
    @optional(undefined)
    subPipelineStages?: Array<SubPipelineStage>;
}

export class DataPipeline extends JSONObject {
    @optional(false)
    disable: boolean;
    @optional
    name: string;

    @optional
    stateMachine: DataPipelineStateMachine;
}
