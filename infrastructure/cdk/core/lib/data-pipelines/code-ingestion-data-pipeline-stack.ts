import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { NetworkStack } from '../vpc/network-stack';
import { DataPipelineStateMachineConstruct } from '../common/s3/data-pipeline-state-machine-construct';
import { DataPipeline } from './config';

interface CodeIngestionDataPipelineStackConfig {
    awsEnvAccount: AwsEnvAccount;
    cdkAppInfo: CdkAppInfo;
    codeIngestionDataPipeline: DataPipeline;
}

export interface CodeIngestionDataPipelineStackProps extends StackProps {
    buildConfig: CodeIngestionDataPipelineStackConfig;
    networkStack: NetworkStack;
}

export class CodeIngestionDataPipelineStack extends Stack {
    constructor(scope: Construct, id: string, props: CodeIngestionDataPipelineStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite || !props.buildConfig.codeIngestionDataPipeline) {
            console.log('Skipping CodeIngestionDataPipelineStack.');
            return;
        }

        new DataPipelineStateMachineConstruct(this, `${props.buildConfig.codeIngestionDataPipeline.name}`, {
            awsEnvAccount: props.buildConfig.awsEnvAccount,
            cdkAppInfo: props.buildConfig.cdkAppInfo,
            stateMachine: props.buildConfig.codeIngestionDataPipeline.stateMachine,
            networkStack: props.networkStack,
        });
    }
}
