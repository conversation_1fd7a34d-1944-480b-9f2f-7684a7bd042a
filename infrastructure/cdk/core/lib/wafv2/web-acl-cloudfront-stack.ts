import { aws_wafv2 as wafv2, Duration, RemovalP<PERSON>y, Stack, StackProps } from 'aws-cdk-lib';
import * as logs from 'aws-cdk-lib/aws-logs';
import { LogGroupClass, RetentionDays } from 'aws-cdk-lib/aws-logs';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';
import { CdkAppInfo } from '../build-config';
import { WebAclCloudFront } from './config';

interface WebAclStackConfig {
    cdkAppInfo: CdkAppInfo;
    webAclCloudFront: WebAclCloudFront;
}

export class WebAclCloudFrontStack extends Stack {
    readonly webAclCloudFront: wafv2.CfnWebACL;
    readonly webAclCloudFrontArn: string;

    constructor(scope: Construct, id: string, props: StackProps, buildConfig: WebAclStackConfig) {
        super(scope, id, props);
        const webAclConfig = buildConfig.webAclCloudFront;

        // Rate limiting rules for public endpoints
        const rateBasedRule = this.createRateBasedRule(webAclConfig);

        // Rule to protect __deepcheck endpoint
        const deepCheckEndpointIPFilterRule = this.createDeepcheckEndpointIPFilterRule(webAclConfig.name);

        // Rule to protect custom endpoints based on IP filters
        const endpointIPFilterRule = this.createEndpointIPFilterRule(webAclConfig);

        const ruleToBlockHubTraffic = this.createRuletToblockHubTraffic(webAclConfig.name);

        const wafLogGroup = new logs.LogGroup(this, `aws-waf-cloudwatch-logs-${buildConfig.cdkAppInfo.environment}`, {
            logGroupName: `aws-waf-logs-${buildConfig.cdkAppInfo.environment}`,
            removalPolicy: RemovalPolicy.RETAIN,
            retention: RetentionDays.THREE_MONTHS,
            logGroupClass: LogGroupClass.STANDARD,
        });

        // Create the WAF and Associate the IP Set
        this.webAclCloudFront = new wafv2.CfnWebACL(this, webAclConfig.name, {
            defaultAction: { allow: {} },

            scope: 'CLOUDFRONT',
            // Defines and enables Amazon CloudWatch metrics and web request sample collection.
            visibilityConfig: {
                cloudWatchMetricsEnabled: true,
                metricName: webAclConfig.name,
                sampledRequestsEnabled: true,
            },
            description: 'WAFv2 ACL for CloudFront',
            name: webAclConfig.name,
            customResponseBodies: this.createCustomResponses(webAclConfig),
            rules: [deepCheckEndpointIPFilterRule, endpointIPFilterRule, ...rateBasedRule, ruleToBlockHubTraffic],
        });

        // S3 Access logs for WAF
        const wafLogsBucket = new s3.Bucket(this, `aws-waf-logs-${buildConfig.cdkAppInfo.environment}`, {
            bucketName: `aws-waf-logs--${buildConfig.cdkAppInfo.environment}`,
            publicReadAccess: false,
            blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
            removalPolicy: RemovalPolicy.RETAIN,
            autoDeleteObjects: false,
            versioned: true,
        });

        wafLogsBucket.addLifecycleRule({
            enabled: true,
            expiration: Duration.days(90),
            noncurrentVersionExpiration: Duration.days(7),
            noncurrentVersionsToRetain: 4,
            id: 'logRetentionPolicyRule',
        });

        new wafv2.CfnLoggingConfiguration(this, `aws-waf-logging-config--${buildConfig.cdkAppInfo.environment}`, {
            logDestinationConfigs: [wafLogGroup.logGroupArn],
            resourceArn: this.webAclCloudFront.attrArn,
            redactedFields: [
                {
                    singleHeader: {
                        Name: 'authorization',
                    },
                },
            ],
        });

        this.webAclCloudFrontArn = this.webAclCloudFront.attrArn;
    }

    /*
     * Create RateBased WAF rules for public endpoints
     *
     */
    private createCustomResponses(
        webAclConfig: WebAclCloudFront
    ): Record<string, wafv2.CfnWebACL.CustomResponseBodyProperty> | undefined {
        const respnses: Record<string, wafv2.CfnWebACL.CustomResponseBodyProperty> = {};

        for (const response of webAclConfig.customResponses) {
            respnses[response.name] = {
                content: response.content,
                contentType: response.contentType,
            };
        }
        return Object.keys(respnses).length > 0 ? respnses : undefined;
    }

    /*
     * Create RateBased WAF rules for public endpoints
     *
     */
    private createRateBasedRule(webAclConfig: WebAclCloudFront): any {
        const rateBasedRules: any[] = [];

        webAclConfig.rateBasedRules.forEach((rule, index) => {
            // Add customer aggregation headers
            let customKeys: any = undefined;
            if (rule.aggregateKeyHeaders) {
                customKeys = [];
                for (const header of rule.aggregateKeyHeaders) {
                    customKeys.push({
                        header: {
                            name: header.toLowerCase(),
                            textTransformations: [
                                {
                                    priority: 0,
                                    type: rule.headerTextTransformationType,
                                },
                            ],
                        },
                    });
                }
            }

            let action: any = { count: {} };
            if (rule.block) {
                action = {
                    block: {
                        customResponse: {
                            responseCode: rule.customResponseCode | 429,
                            responseHeaders: [
                                {
                                    name: 'Retry-After',
                                    value: '10',
                                },
                            ],
                            customResponseBodyKey: rule.customResponseBodyKey,
                        },
                    },
                };
            }

            let scopeDownStatementMatchCondition: any = {
                byteMatchStatement: {
                    searchString: rule.searchString,
                    fieldToMatch: {
                        uriPath: {},
                    },
                    textTransformations: [
                        {
                            priority: 0,
                            type: 'NONE',
                        },
                    ],
                    positionalConstraint: rule.positionalConstraint,
                },
            };

            // Add statements for excluded search strings if any provided
            if (rule.excludedSearchStrings && rule.excludedSearchStrings.length > 0) {
                scopeDownStatementMatchCondition = {
                    andStatement: {
                        statements: [scopeDownStatementMatchCondition],
                    },
                };

                const excludeStatements: any = {
                    notStatement: {
                        statement: {
                            orStatement: {
                                statements: [],
                            },
                        },
                    },
                };
                for (const searchStringRule of rule.excludedSearchStrings) {
                    excludeStatements.notStatement.statement.orStatement.statements.push({
                        byteMatchStatement: {
                            searchString: searchStringRule.searchString,
                            fieldToMatch: {
                                uriPath: {},
                            },
                            textTransformations: [
                                {
                                    priority: 0,
                                    type: 'NONE',
                                },
                            ],
                            positionalConstraint: searchStringRule.positionalConstraint,
                        },
                    });
                }
                scopeDownStatementMatchCondition.andStatement.statements.push(excludeStatements);
            }

            rateBasedRules.push({
                name: rule.name,
                priority: 100 + index,
                statement: {
                    rateBasedStatement: {
                        limit: rule.limit,
                        aggregateKeyType: rule.aggregateKeyType,
                        scopeDownStatement: scopeDownStatementMatchCondition,
                        customKeys: customKeys,
                    },
                },
                action: action,
                visibilityConfig: {
                    sampledRequestsEnabled: true,
                    cloudWatchMetricsEnabled: true,
                    metricName: rule.name,
                },
            });
        });

        return rateBasedRules;
    }

    /*
     * Create IPList filter rule for custom endpoints
     * This rule denies all external traffic except
     *  traffic from whitelisted addresses
     */
    private createEndpointIPFilterRule(webAclConfig: WebAclCloudFront): any {
        const endpointIPFilterStatements: any[] = [];
        for (const rule of webAclConfig.endpointIPFilterRules) {
            const ipSet = new wafv2.CfnIPSet(this, `${rule.name}-ipset`, {
                addresses: rule.ipv4CIDRWhiteList,
                ipAddressVersion: 'IPV4',
                scope: 'CLOUDFRONT',
                name: `${rule.name}-ipset`,
            });

            for (const searchString of rule.searchStrings) {
                endpointIPFilterStatements.push({
                    andStatement: {
                        statements: [
                            {
                                byteMatchStatement: {
                                    fieldToMatch: {
                                        uriPath: {},
                                    },
                                    positionalConstraint: rule.positionalConstraint,
                                    searchString: searchString,
                                    textTransformations: [
                                        {
                                            type: 'NONE',
                                            priority: 0,
                                        },
                                    ],
                                },
                            },
                            {
                                notStatement: {
                                    statement: {
                                        ipSetReferenceStatement: {
                                            arn: ipSet.attrArn,
                                        },
                                    },
                                },
                            },
                        ],
                    }, // end and
                });
            }
        }

        return {
            visibilityConfig: {
                cloudWatchMetricsEnabled: true,
                metricName: `${webAclConfig.name}-endpoint-ip-filter`,
                sampledRequestsEnabled: true,
            },
            priority: 220,
            statement: {
                orStatement: {
                    statements: endpointIPFilterStatements,
                },
            },
            name: 'endpoint-ip-filter',
            action: {
                block: {},
            },
        };
    }

    /*
     * Create Request Header filter rule for deepcheck endpoint.
     * This rule denies all external traffic unless a specific header is set
     */
    private createDeepcheckEndpointIPFilterRule(webAclName: string): any {
        return {
            visibilityConfig: {
                cloudWatchMetricsEnabled: true,
                metricName: `${webAclName}-health-probes-protection-rule`,
                sampledRequestsEnabled: true,
            },
            priority: 210,
            statement: {
                andStatement: {
                    statements: [
                        {
                            byteMatchStatement: {
                                fieldToMatch: {
                                    uriPath: {},
                                },
                                positionalConstraint: 'CONTAINS',
                                searchString: '/__deepcheck',
                                textTransformations: [
                                    {
                                        type: 'NONE',
                                        priority: 0,
                                    },
                                ],
                            },
                        },
                        {
                            notStatement: {
                                statement: {
                                    byteMatchStatement: {
                                        fieldToMatch: {
                                            singleHeader: {
                                                name: 'x-secret-sauce',
                                            },
                                        },
                                        positionalConstraint: 'EXACTLY',
                                        searchString: 'rashinisawesome',
                                        textTransformations: [
                                            {
                                                type: 'NONE',
                                                priority: 0,
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                    ],
                }, // end and
            },
            name: 'health-probes-protection-rule',
            action: {
                block: {},
            },
        };
    }
    private createRuletToblockHubTraffic(webAclName: string): any {
        return {
            visibilityConfig: {
                cloudWatchMetricsEnabled: true,
                metricName: `${webAclName}-block-hub-rule`,
                sampledRequestsEnabled: true,
            },
            priority: 231,
            statement: {
                byteMatchStatement: {
                    fieldToMatch: {
                        headers: {
                            matchPattern: {
                                includedHeaders: ['X-Unblocked-Product-Agent'],
                            },
                            matchScope: 'ALL',
                            oversizeHandling: 'NO_MATCH',
                        },
                    },
                    positionalConstraint: 'CONTAINS',
                    searchString: 'hub',
                    textTransformations: [
                        {
                            type: 'NONE',
                            priority: 0,
                        },
                    ],
                },
            },
            name: 'block_hub',
            action: {
                block: {},
            },
        };
    }
}
