import { RestApi } from 'aws-cdk-lib/aws-apigateway';
import * as iam from 'aws-cdk-lib/aws-iam';
import { ApiGatewayConstruct } from '../common/api-gateway/api-gateway-construct';
import { Construct } from 'constructs';

import { AwsEnvAccount } from '../build-config';
import { Stack, StackProps } from 'aws-cdk-lib';
import { MachineLearningSagemakerEndpointConstruct } from '../common/machine-learning/machine-learning-sagemaker-endpoint-construct';
import { SagemakerApiEndpointConstruct } from '../common/sagemaker/sagemaker-api-endpoint-construct';
import { AcmStack } from '../acm/acm-stack';
import { SagemakerApiEndpointLambdaConstruct } from '../common/sagemaker/sagemaker-api-endpoint-lambda-construct';
import { IRole } from 'aws-cdk-lib/aws-iam';
import { MachinelearningHelpers } from '../common/utils/machinelearning-helpers';
import { Dns } from '../common/dns/config';
import { VPCConfig } from '../common/vpc/config';
import { SagemakerMachineLearningModels, SagemakerMachineLearningModel } from './config';

// policies based on https://docs.aws.amazon.com/sagemaker/latest/dg/sagemaker-roles.html#sagemaker-roles-createmodel-perms
const iamSagemakerActions = [
    'cloudwatch:GetMetricData',
    'cloudwatch:GetMetricStatistics',
    'cloudwatch:ListMetrics',
    'cloudwatch:PutMetricData',
    'ecr:BatchCheckLayerAvailability',
    'ecr:BatchGetImage',
    'ecr:GetAuthorizationToken',
    'ecr:GetDownloadUrlForLayer',
    'kms:Decrypt',
    'kms:Encrypt',
    'kms:GenerateDataKey*',
    'kms:ReEncrypt*',
    'logs:CreateLogGroup',
    'logs:CreateLogStream',
    'logs:DescribeLogStreams',
    'logs:GetLogEvents',
    'logs:PutLogEvents',
    's3:CreateBucket',
    's3:GetBucketLocation',
    's3:GetObject',
    's3:ListBucket',
    's3:PutObject',
    'sagemaker:*',
    'secretsmanager:DescribeSecret',
    'secretsmanager:GetSecretValue',
    'secretsmanager:ListSecretVersionIds',
];

export interface SagemakerMachineLearningModelsStackProps extends StackProps {
    sagemakerMachineLearningModels: SagemakerMachineLearningModels;
    awsEnvAccount: AwsEnvAccount;
    coreVPC: VPCConfig;
    acmStack: AcmStack;
    dns: Dns;
}

export class SagemakerMachineLearningModelsStack extends Stack {
    constructor(scope: Construct, constructId: string, props: SagemakerMachineLearningModelsStackProps) {
        super(scope, constructId, props);

        if (props.awsEnvAccount.coldSite || props.sagemakerMachineLearningModels.disable) {
            console.log('Skipping MachineLearningModelsStack');
            return;
        }

        if (props.sagemakerMachineLearningModels.models.length == 0) {
            return;
        }

        // creates new iam role for sagemaker using `iamSagemakerActions` as permissions or uses provided arn
        const executionRole = new iam.Role(this, 'ml_sagemaker_execution_role', {
            assumedBy: new iam.ServicePrincipal('sagemaker.amazonaws.com'),
        });
        executionRole.addManagedPolicy(iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSageMakerFullAccess'));
        executionRole.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: iamSagemakerActions,
                resources: ['*'],
            })
        );

        const apiGateway = new ApiGatewayConstruct(this, props.sagemakerMachineLearningModels.apiGateway.name, {
            apiGateway: props.sagemakerMachineLearningModels.apiGateway,
            acmStack: props.acmStack,
            dns: props.dns,
        });

        for (const machineLearningModel of props.sagemakerMachineLearningModels.models) {
            this.createMachineLearningModel(props, apiGateway.apiGateway, machineLearningModel, executionRole);
        }
    }

    createMachineLearningModel(
        props: SagemakerMachineLearningModelsStackProps,
        apiGateway: RestApi,
        machineLearningModel: SagemakerMachineLearningModel,
        executionRole: IRole
    ) {
        // SageMaker Endpoint
        const endpoint = new MachineLearningSagemakerEndpointConstruct(
            this,

            MachinelearningHelpers.getEndpointName(machineLearningModel.modelName),
            {
                awsEnvAccount: props.awsEnvAccount,
                modelName: machineLearningModel.modelName,
                modelTask: machineLearningModel.modelTask,
                executionRole: executionRole,
                instanceType: machineLearningModel.instanceType,
                modelDockerImageAsset: machineLearningModel.modelDockerImage,
                modelS3Artifact: machineLearningModel.modelS3Artifact,
                modelToken: machineLearningModel.modelToken,
                modelEnvironment: machineLearningModel.modelEnvironment,
                modelDeepLearningContainer: machineLearningModel.modelDeepLearningContainer,
                modelAutoScale: machineLearningModel.modelAutoScale,
                containerStartupHealthCheckTimeoutInSeconds:
                    machineLearningModel.containerStartupHealthCheckTimeoutInSeconds,
            }
        );

        if (machineLearningModel.modelTriggerLambda) {
            const sagemakerApiEndpointLambda = new SagemakerApiEndpointLambdaConstruct(
                this,
                machineLearningModel.apiGatewayEndpoint.name + '-lambda',
                {
                    awsEnvAccount: props.awsEnvAccount,
                    apiGateway: apiGateway,
                    endpoint: endpoint.endpoint,
                    apiGatewayEndpoint: machineLearningModel.apiGatewayEndpoint,
                    lambdaConfig: machineLearningModel.modelTriggerLambda,
                }
            );
            // Unblock deadlock when we remove lambdas
            // https://www.endoflineblog.com/cdk-tips-03-how-to-unblock-cross-stack-references
            this.exportValue(sagemakerApiEndpointLambda.lambda.lambdaFunction.functionArn);
        } else {
            new SagemakerApiEndpointConstruct(this, machineLearningModel.apiGatewayEndpoint.name + '-sagemaker', {
                apiGateway: apiGateway,
                endpoint: endpoint.endpoint,
                apiGatewayEndpoint: machineLearningModel.apiGatewayEndpoint,
            });
        }
    }
}
