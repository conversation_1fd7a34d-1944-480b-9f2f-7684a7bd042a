import * as cdk from 'aws-cdk-lib/core';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { Dns } from '../common/dns/config';
import { StackProps } from 'aws-cdk-lib';
import { AcmStack } from '../acm/acm-stack';
import { NetworkStack } from '../vpc/network-stack';
import { DnsStack } from '../dns/dns-stack';
import { VPCConfig } from '../common/vpc/config';
import { ECSMachineLearningModels } from './config';
import { ECSMachineLearningModelsConstruct } from '../common/machine-learning/ecs-machine-learning-models-construct';

export interface ECSMachineLearningModelsStackProps extends StackProps {
    ecsMachineLearningModels: ECSMachineLearningModels;
    acmStack: AcmStack;
    networkStack: NetworkStack;
    dnsStack: DnsStack;
    awsEnvAccount: AwsEnvAccount;
    dns: Dns;
    peerVPCs: Array<VPCConfig>;
    cdkAppInfo: CdkAppInfo;
}

export class ECSMachineLearningModelsStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: ECSMachineLearningModelsStackProps) {
        super(scope, id, props);

        if (props.awsEnvAccount.coldSite || !props.ecsMachineLearningModels) {
            console.log('Skipping ECSMachineLearningModelsStack');
            return;
        }

        if (props.ecsMachineLearningModels.models.length == 0) {
            console.log('Skipping ECSMachineLearningModelsStack');
            return;
        }

        new ECSMachineLearningModelsConstruct(this, `ECSMachineLearningModels`, {
            ...props,
        });
    }
}
