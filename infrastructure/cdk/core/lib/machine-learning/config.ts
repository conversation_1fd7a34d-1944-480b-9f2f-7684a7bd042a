import { array, JSONObject, optional, required } from 'ts-json-object';
import { ApiGateway, ApiGatewayEndpoint } from '../common/api-gateway/config';
import { HealthCheckConfig } from '../common/elb/config';
import { LambdaConfig } from '../common/lambda/config';
import { EnvironmentSecret } from '../common/env/config';
import { DockerEcrRepository, DockerImageAsset } from '../common/docker/config';
import { SagemakerAutoScaleConfig } from '../common/sagemaker/config';
import { EC2Service } from '../common/ecs/config';
import { S3Artifact } from '../common/s3/config';
import { strict } from 'node:assert';

export class MachineLearningModel extends JSONObject {
    @optional(false)
    deprecated: boolean;
}

export class SagemakerMachineLearningModel extends MachineLearningModel {
    @required
    modelName: string;
    @required
    modelTask: string;
    @optional(undefined)
    modelEnvironment: Record<string, string>;
    @optional(undefined)
    modelToken?: EnvironmentSecret;
    @optional(undefined)
    modelDockerImage: DockerImageAsset;
    @optional('ml.m5.xlarge')
    instanceType: string;
    @required
    apiGatewayEndpoint: ApiGatewayEndpoint;
    @optional(undefined)
    modelTriggerLambda: LambdaConfig;
    @optional(undefined)
    modelS3Artifact?: S3Artifact;
    @optional(undefined)
    modelDeepLearningContainer?: DockerEcrRepository;
    @optional(undefined)
    modelAutoScale?: SagemakerAutoScaleConfig;
    @optional(undefined)
    containerStartupHealthCheckTimeoutInSeconds?: number;
}

export class ECSMachineLearningModel extends MachineLearningModel {
    @required
    ec2Service: EC2Service;
    @required
    apiPaths: Array<string>;
    @required
    apiPriority: number;
    @optional(undefined)
    healthCheck: HealthCheckConfig;
}

// https://github.com/aws/deep-learning-containers/blob/master/available_images.md#huggingface-text-generation-inference-containers'
export class SagemakerMachineLearningModels extends JSONObject {
    @optional(false)
    disable: boolean;
    @required
    apiGateway: ApiGateway;

    @required
    @array(SagemakerMachineLearningModel)
    models: Array<SagemakerMachineLearningModel>;
}

export class ECSMachineLearningModels extends JSONObject {
    @optional(false)
    disable?: boolean;

    @required
    certName: string;

    @required
    certDomainName: string;

    @required
    @array(ECSMachineLearningModel)
    models: Array<ECSMachineLearningModel>;
}
