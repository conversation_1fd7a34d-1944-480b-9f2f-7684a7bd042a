import { Stack, StackProps } from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import * as path from 'path';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as targets from 'aws-cdk-lib/aws-route53-targets';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import { AcmStack } from '../acm/acm-stack';
import * as origin from 'aws-cdk-lib/aws-cloudfront-origins';
import { Dns } from '../common/dns/config';
import { CloudFront } from '../common/cloudfront/config';

interface SubdomainRedirectsCloudfrontStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    dns: Dns;
    subdomainRedirectCloudFront: CloudFront;
}

export interface SubdomainRedirectsCloudfrontStackProps extends StackProps {
    buildConfig: SubdomainRedirectsCloudfrontStackConfig;
    acmStack: AcmStack;
}

export class SubdomainRedirectsCloudfrontStack extends Stack {
    readonly distribution: cloudfront.Distribution;

    constructor(scope: Construct, id: string, props: SubdomainRedirectsCloudfrontStackProps) {
        super(scope, id, props);

        const cloudFrontConf = props.buildConfig.subdomainRedirectCloudFront;
        if (!cloudFrontConf) return;

        /**
         *  ACM Cert Setup
         */
        const acmCertificate = props.acmStack.certs.get(cloudFrontConf.certName);
        if (acmCertificate == undefined) {
            throw new Error(`failed to retrieve ${props.buildConfig.dns.route53HostedZoneName} cert from acm stack`);
        }

        // Adding getunblocked.com as an http origin
        const rootOrigin = new origin.HttpOrigin('getunblocked.com', {});
        this.distribution = new cloudfront.Distribution(this, `RedirectCloudFrontDistro`, {
            defaultBehavior: {
                origin: rootOrigin,
                originRequestPolicy: cloudfront.OriginRequestPolicy.ALL_VIEWER,
                cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
                compress: false,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
                edgeLambdas: [
                    {
                        // Lambda@Edge: Subdomain redirect script
                        functionVersion: new cloudfront.experimental.EdgeFunction(
                            this,
                            `docs-csp-nonce-viewer-response`,
                            {
                                runtime: lambda.Runtime.NODEJS_18_X,
                                handler: 'index.handler',
                                code: lambda.Code.fromAsset(
                                    path.join(
                                        __dirname,
                                        '../../assets/lambda/cloudfront-subdomain-redirect-viewer-request'
                                    )
                                ),
                            }
                        ),
                        eventType: cloudfront.LambdaEdgeEventType.VIEWER_REQUEST,
                    },
                ],
            },
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            domainNames: cloudFrontConf.domainNames,
            certificate: acmCertificate,
            httpVersion: cloudfront.HttpVersion.HTTP2,
            minimumProtocolVersion: cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
            enableIpv6: true,
        });

        new route53.ARecord(this, `SiteAliasRecord-${props.buildConfig.dns.route53HostedZoneName}`, {
            recordName: `redirect.${props.buildConfig.dns.route53HostedZoneName}`,
            target: route53.RecordTarget.fromAlias(new targets.CloudFrontTarget(this.distribution)),
            zone: props.acmStack.envRootHostedZone,
        });
    }
}
