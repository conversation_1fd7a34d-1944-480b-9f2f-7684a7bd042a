import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as targets from 'aws-cdk-lib/aws-route53-targets';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import { AcmStack } from '../acm/acm-stack';
import * as origin from 'aws-cdk-lib/aws-cloudfront-origins';
import { Dns } from '../common/dns/config';
import { CloudFront } from '../common/cloudfront/config';

interface SendGridCloudFrontStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    dns: Dns;
    sendGridCloudFront: CloudFront;
}

export interface SendGridCloudFrontStackProps extends StackProps {
    buildConfig: SendGridCloudFrontStackConfig;
    acmStack: AcmStack;
}

export class SendGridCloudFrontStack extends Stack {
    readonly distribution: cloudfront.Distribution;

    constructor(scope: Construct, id: string, props: SendGridCloudFrontStackProps) {
        super(scope, id, props);

        // ** Important **
        // We only setup this distribution for production. This is mainly used to provide SSL certs
        // when using branded links with SendGrid. We are still trying to figure out if SendGrid allows us
        // to use separate branded links per environment. Until then, we will only have this resource deployed in prod
        const cloudFrontConf = props.buildConfig.sendGridCloudFront;
        if (!cloudFrontConf) return;

        /**
         *  ACM Cert Setup
         */
        const acmCertificate = props.acmStack.certs.get(cloudFrontConf.certName);
        if (acmCertificate == undefined) {
            throw new Error(`failed to retrieve ${props.buildConfig.dns.route53HostedZoneName} cert from acm stack`);
        }

        // Adding SendGrid origin
        // https://support.sendgrid.com/hc/en-us/articles/4412701748891-How-to-configure-SSL-for-click-tracking-using-CloudFront
        const rootOrigin = new origin.HttpOrigin('sendgrid.net', {});
        this.distribution = new cloudfront.Distribution(this, `SendGridCloudFrontDistro`, {
            defaultBehavior: {
                origin: rootOrigin,
                originRequestPolicy: cloudfront.OriginRequestPolicy.ALL_VIEWER,
                cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
                compress: false,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.ALLOW_ALL,
            },
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            domainNames: cloudFrontConf.domainNames,
            certificate: acmCertificate,
            httpVersion: cloudfront.HttpVersion.HTTP2,
            minimumProtocolVersion: cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
            enableIpv6: true,
        });

        new route53.ARecord(this, `SiteAliasRecord-${props.buildConfig.dns.route53HostedZoneName}`, {
            recordName: `sendgrid.${props.buildConfig.dns.route53HostedZoneName}`,
            target: route53.RecordTarget.fromAlias(new targets.CloudFrontTarget(this.distribution)),
            zone: props.acmStack.envRootHostedZone,
        });
    }
}
