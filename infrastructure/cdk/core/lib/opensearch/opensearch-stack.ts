import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount } from '../build-config';
import { OpenSearchConstruct } from '../common/opensearch/opensearch-construct';
import { OpenSearch } from '../common/opensearch/config';
import { VPCConfig } from '../common/vpc/config';
import { NetworkStack } from '../vpc/network-stack';

export interface OpenSearchStackProps extends StackProps {
    awsEnvAccount: AwsEnvAccount;
    openSearch: OpenSearch;
    networkStack: NetworkStack;
    peerVPCs: Array<VPCConfig>;
}

export class OpenSearchStack extends Stack {
    constructor(scope: Construct, id: string, props: OpenSearchStackProps) {
        super(scope, id, props);

        if (props.awsEnvAccount.coldSite) {
            console.log('Skipping ActiveMQStack');
            return;
        }

        new OpenSearchConstruct(this, props.openSearch.domainName, {
            openSearch: props.openSearch,
            vpc: props.networkStack.coreVpc,
            peerVPCs: props.peerVPCs,
        });
    }
}
