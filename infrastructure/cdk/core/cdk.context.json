{"availability-zones:account=************:region=us-west-2": ["us-west-2a", "us-west-2b", "us-west-2c", "us-west-2d"], "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-06d3d2e362c0260b8:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-06d3d2e362c0260b8", "vpcCidrBlock": "172.16.0.0/16", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-0d7e7f9f1bfece6ba", "cidr": "172.16.32.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0ac7142375640b894"}, {"subnetId": "subnet-08117543510360b64", "cidr": "172.16.0.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0ac7142375640b894"}, {"subnetId": "subnet-01e60537dba4efc14", "cidr": "172.16.64.0/19", "availabilityZone": "us-west-2d", "routeTableId": "rtb-0ac7142375640b894"}]}, {"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-0df69308c1e7af157", "cidr": "172.16.128.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0474d6418918caa93"}, {"subnetId": "subnet-0916a201108173cfa", "cidr": "172.16.96.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0a11b385fb3ebd7f6"}, {"subnetId": "subnet-0228857736575b66b", "cidr": "172.16.160.0/19", "availabilityZone": "us-west-2d", "routeTableId": "rtb-0b2b2667000b6801a"}]}]}, "availability-zones:account=************:region=us-west-2": ["us-west-2a", "us-west-2b", "us-west-2c", "us-west-2d"], "availability-zones:account=************:region=us-west-2": ["us-west-2a", "us-west-2b", "us-west-2c", "us-west-2d"], "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-043ee083c319814a2:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-043ee083c319814a2", "vpcCidrBlock": "10.0.0.0/16", "availabilityZones": [], "subnetGroups": [{"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-0be021e1a48ad1f14", "cidr": "10.0.96.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-06e5421a901ec1b00"}, {"subnetId": "subnet-0a9ddc7f723a530f9", "cidr": "10.0.160.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0fd209c6f8c9c3a9c"}, {"subnetId": "subnet-0a4b84314bb98dc82", "cidr": "10.0.128.0/19", "availabilityZone": "us-west-2d", "routeTableId": "rtb-0da0bffd01408745e"}]}, {"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-0627643c2e877cb56", "cidr": "10.0.0.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-068b0a6db2fb9c6f3"}, {"subnetId": "subnet-01fa037aeb926063e", "cidr": "10.0.64.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-068b0a6db2fb9c6f3"}, {"subnetId": "subnet-009b5355b175ea4b7", "cidr": "10.0.32.0/19", "availabilityZone": "us-west-2d", "routeTableId": "rtb-068b0a6db2fb9c6f3"}]}]}, "availability-zones:account=************:region=us-east-2": ["us-east-2a", "us-east-2b", "us-east-2c"], "key-provider:account=************:aliasName=alias/user-audio-assets-dev-us-west-2-kms-key:region=us-west-2": {"keyId": "4e3c6f17-d1ca-4037-bc2e-1cdbf0397269"}, "key-provider:account=************:aliasName=alias/user-blob-assets-dev-us-west-2-kms-key:region=us-west-2": {"keyId": "c42e0ab3-84ef-487d-9bb7-948b90c35a04"}, "key-provider:account=************:aliasName=alias/user-image-assets-dev-us-west-2-kms-key:region=us-west-2": {"keyId": "0f8f8902-45af-4b0b-976e-2d19ec56350a"}, "key-provider:account=************:aliasName=alias/user-text-assets-dev-us-west-2-kms-key:region=us-west-2": {"keyId": "fb1e1d35-c46f-4054-844e-74ec21f8d314"}, "key-provider:account=************:aliasName=alias/user-video-assets-dev-us-west-2-kms-key:region=us-west-2": {"keyId": "e01cb208-d92d-41ab-9923-f6f222506e4a"}, "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-0ca955b8121f485fe:region=us-east-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0ca955b8121f485fe", "vpcCidrBlock": "172.20.0.0/16", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-0ce3ffcf8fade083b", "cidr": "172.20.0.0/19", "availabilityZone": "us-east-2a", "routeTableId": "rtb-0f8ff935c777c9196"}, {"subnetId": "subnet-0afd9da824e27da2d", "cidr": "172.20.64.0/19", "availabilityZone": "us-east-2b", "routeTableId": "rtb-0f8ff935c777c9196"}, {"subnetId": "subnet-00012c207888c7d76", "cidr": "172.20.32.0/19", "availabilityZone": "us-east-2c", "routeTableId": "rtb-0f8ff935c777c9196"}]}, {"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-0df09dff66b924f4d", "cidr": "172.20.96.0/19", "availabilityZone": "us-east-2a", "routeTableId": "rtb-0fdb134e9b801b818"}, {"subnetId": "subnet-0d41b7ac812ee5bc1", "cidr": "172.20.160.0/19", "availabilityZone": "us-east-2b", "routeTableId": "rtb-0d2ff0b906c5097fc"}, {"subnetId": "subnet-0b844ab6d7637b4b3", "cidr": "172.20.128.0/19", "availabilityZone": "us-east-2c", "routeTableId": "rtb-0557dc5f93971a5bc"}]}]}, "availability-zones:account=************:region=us-east-2": ["us-east-2a", "us-east-2b", "us-east-2c"], "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-0836e6b30b405c98b:region=us-east-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0836e6b30b405c98b", "vpcCidrBlock": "10.20.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-083b4cf3e243ad977", "cidr": "10.20.128.0/19", "availabilityZone": "us-east-2a", "routeTableId": "rtb-0a3f73f968329445b"}, {"subnetId": "subnet-0f7d3363c4ce1db8c", "cidr": "10.20.96.0/19", "availabilityZone": "us-east-2b", "routeTableId": "rtb-0dbec15913b20170b"}, {"subnetId": "subnet-03adeaa3c39002330", "cidr": "10.20.160.0/19", "availabilityZone": "us-east-2c", "routeTableId": "rtb-0afaf891caa44d9a5"}]}, {"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-0d0acaeb91b3ec78c", "cidr": "10.20.32.0/19", "availabilityZone": "us-east-2a", "routeTableId": "rtb-0f1223b902ce39344"}, {"subnetId": "subnet-0af14657fdc53d23a", "cidr": "10.20.0.0/19", "availabilityZone": "us-east-2b", "routeTableId": "rtb-0f1223b902ce39344"}, {"subnetId": "subnet-0b06477270efffe68", "cidr": "10.20.64.0/19", "availabilityZone": "us-east-2c", "routeTableId": "rtb-0f1223b902ce39344"}]}]}, "ssm:account=************:parameterName=/eks-vpc/vpc-id:region=us-west-2": "vpc-01305dee83d623ca9", "vpc-provider:account=************:filter.vpc-id=vpc-01305dee83d623ca9:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-01305dee83d623ca9", "vpcCidrBlock": "172.18.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-0b96ba80c7d7624ae", "cidr": "172.18.0.0/19", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0cebb3015b09eacd5"}, {"subnetId": "subnet-0451ff78310a6cd71", "cidr": "172.18.32.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0e34b35e6c24fe7c4"}, {"subnetId": "subnet-0e034fdcb36c0239e", "cidr": "172.18.64.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0c2c7cf33c01b8039"}]}, {"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-07030fdde41060df7", "cidr": "172.18.96.0/19", "availabilityZone": "us-west-2a", "routeTableId": "rtb-09c71dbd4ee84fce5"}, {"subnetId": "subnet-0d9ac3dbca84b876b", "cidr": "172.18.128.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0342a90cedeab1757"}, {"subnetId": "subnet-05762d2783741c85c", "cidr": "172.18.160.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-06b2956aad2ac523c"}]}]}, "ssm:account=************:parameterName=/network-stack/core-vpc-id:region=us-west-2": "vpc-0af36e7b5c7f77e46", "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-0af36e7b5c7f77e46:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0af36e7b5c7f77e46", "vpcCidrBlock": "172.17.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-03d4072636531b1e6", "cidr": "172.17.96.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-08f5528eb652e601c"}, {"subnetId": "subnet-06016e700b3ad3de9", "cidr": "172.17.112.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-06b13159b5068d702"}, {"subnetId": "subnet-045db94c847be49fe", "cidr": "172.17.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0f5018b37e3211bf7"}]}, {"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-035e13a9137867834", "cidr": "172.17.48.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0fd3365d968ac82c7"}, {"subnetId": "subnet-04185793bcc99abff", "cidr": "172.17.64.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-02bd4c5a70d581610"}, {"subnetId": "subnet-02b697958d7e051fb", "cidr": "172.17.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0666f1d2fd6863106"}]}, {"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-0b540178076be9420", "cidr": "172.17.0.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0dd2223297fb1f9c9"}, {"subnetId": "subnet-0e4b7a65fbd153bb0", "cidr": "172.17.16.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0eb5162c75a0b1645"}, {"subnetId": "subnet-066086fcd4468e0da", "cidr": "172.17.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0514740a05da612d7"}]}]}, "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-01305dee83d623ca9:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-01305dee83d623ca9", "vpcCidrBlock": "172.18.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-0b96ba80c7d7624ae", "cidr": "172.18.0.0/19", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0cebb3015b09eacd5"}, {"subnetId": "subnet-0451ff78310a6cd71", "cidr": "172.18.32.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0e34b35e6c24fe7c4"}, {"subnetId": "subnet-0e034fdcb36c0239e", "cidr": "172.18.64.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0c2c7cf33c01b8039"}]}, {"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-07030fdde41060df7", "cidr": "172.18.96.0/19", "availabilityZone": "us-west-2a", "routeTableId": "rtb-09c71dbd4ee84fce5"}, {"subnetId": "subnet-0d9ac3dbca84b876b", "cidr": "172.18.128.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0342a90cedeab1757"}, {"subnetId": "subnet-05762d2783741c85c", "cidr": "172.18.160.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-06b2956aad2ac523c"}]}]}, "ssm:account=************:parameterName=/network-stack/core-vpc-id:region=us-west-2": "vpc-0f0face0f0796b23b", "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-0f0face0f0796b23b:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0f0face0f0796b23b", "vpcCidrBlock": "10.5.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-027bb3a730c1e29fb", "cidr": "10.5.96.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-088d3d77ac8baa365"}, {"subnetId": "subnet-0e16e830351c939b9", "cidr": "10.5.112.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-00a78bf2c2d7493ce"}, {"subnetId": "subnet-0c8d4eeebe9f78c0e", "cidr": "10.5.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0e59a8fbe53adc59d"}]}, {"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-07790668d75d7fd4a", "cidr": "10.5.0.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-029e92e8a99f823b4"}, {"subnetId": "subnet-0eff0bfbc3b5608c9", "cidr": "10.5.16.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-04f0da08067663a3d"}, {"subnetId": "subnet-0f787380afd3db7c3", "cidr": "10.5.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-058513ed1c954c1cf"}]}, {"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-0625975100f0019b4", "cidr": "10.5.48.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0209b1c4776e79570"}, {"subnetId": "subnet-08e189b3d36c5945e", "cidr": "10.5.64.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0b3dd469e593bb0c1"}, {"subnetId": "subnet-0f4049e3515e0c872", "cidr": "10.5.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0d98ebc6984816215"}]}]}, "ssm:account=************:parameterName=/eks-vpc/vpc-id:region=us-west-2": "vpc-0832662cde25e8ca3", "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-0832662cde25e8ca3:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0832662cde25e8ca3", "vpcCidrBlock": "10.1.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-0435706b26eaa92ab", "cidr": "10.1.0.0/19", "availabilityZone": "us-west-2a", "routeTableId": "rtb-01c3d093342f70441"}, {"subnetId": "subnet-0ed67d38212f858c6", "cidr": "10.1.32.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0af664985f79a5fb9"}, {"subnetId": "subnet-0b47b403eeb79e372", "cidr": "10.1.64.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-02f1aca85b8f76927"}]}, {"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-029a2cb1168bea6c6", "cidr": "10.1.96.0/19", "availabilityZone": "us-west-2a", "routeTableId": "rtb-01340415c2e58d6eb"}, {"subnetId": "subnet-0073832bf74eab3b9", "cidr": "10.1.128.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0ed6df87dfaf548e5"}, {"subnetId": "subnet-0717dadd76a7e9ae4", "cidr": "10.1.160.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-064719fc648b06ee5"}]}]}, "vpc-provider:account=************:filter.vpc-id=vpc-0832662cde25e8ca3:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0832662cde25e8ca3", "vpcCidrBlock": "10.1.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-0435706b26eaa92ab", "cidr": "10.1.0.0/19", "availabilityZone": "us-west-2a", "routeTableId": "rtb-01c3d093342f70441"}, {"subnetId": "subnet-0ed67d38212f858c6", "cidr": "10.1.32.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0af664985f79a5fb9"}, {"subnetId": "subnet-0b47b403eeb79e372", "cidr": "10.1.64.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-02f1aca85b8f76927"}]}, {"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-029a2cb1168bea6c6", "cidr": "10.1.96.0/19", "availabilityZone": "us-west-2a", "routeTableId": "rtb-01340415c2e58d6eb"}, {"subnetId": "subnet-0073832bf74eab3b9", "cidr": "10.1.128.0/19", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0ed6df87dfaf548e5"}, {"subnetId": "subnet-0717dadd76a7e9ae4", "cidr": "10.1.160.0/19", "availabilityZone": "us-west-2c", "routeTableId": "rtb-064719fc648b06ee5"}]}]}, "ssm:account=************:parameterName=/network-stack/core-vpc-id:region=us-east-2": "vpc-01c25dca6e0b5bd7d", "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-01c25dca6e0b5bd7d:region=us-east-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-01c25dca6e0b5bd7d", "vpcCidrBlock": "172.21.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-0c896f652a5a22dbc", "cidr": "172.21.48.0/20", "availabilityZone": "us-east-2a", "routeTableId": "rtb-0013f9762ec6cab82"}, {"subnetId": "subnet-07a4cb2fda057696f", "cidr": "172.21.64.0/20", "availabilityZone": "us-east-2b", "routeTableId": "rtb-04a87998f09e42319"}, {"subnetId": "subnet-08a9d22670709fc54", "cidr": "172.21.80.0/20", "availabilityZone": "us-east-2c", "routeTableId": "rtb-05f48e3495b228350"}]}, {"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-005b6843e1f645176", "cidr": "172.21.0.0/20", "availabilityZone": "us-east-2a", "routeTableId": "rtb-0746ec0695b13ecee"}, {"subnetId": "subnet-041551b67b0219ca3", "cidr": "172.21.16.0/20", "availabilityZone": "us-east-2b", "routeTableId": "rtb-0caf8957d2afd5173"}, {"subnetId": "subnet-0615facbdf509c8d3", "cidr": "172.21.32.0/20", "availabilityZone": "us-east-2c", "routeTableId": "rtb-0fa23e2e7493008e5"}]}, {"name": "isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-01ebe8701eb3a4a16", "cidr": "172.21.96.0/20", "availabilityZone": "us-east-2a", "routeTableId": "rtb-077f1b9c5086167c2"}, {"subnetId": "subnet-0c28e39816d665cb2", "cidr": "172.21.112.0/20", "availabilityZone": "us-east-2b", "routeTableId": "rtb-07cb2632f1474259e"}, {"subnetId": "subnet-0567484b7cd0d5eab", "cidr": "172.21.128.0/20", "availabilityZone": "us-east-2c", "routeTableId": "rtb-0e43fa209f056f5a0"}]}]}, "ssm:account=************:parameterName=/eks-vpc/vpc-id:region=us-east-2": "vpc-0eff6844a45a7d98c", "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-0eff6844a45a7d98c:region=us-east-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0eff6844a45a7d98c", "vpcCidrBlock": "172.20.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-034291bccb48832b2", "cidr": "172.20.96.0/19", "availabilityZone": "us-east-2a", "routeTableId": "rtb-03ab6f1eff73482cb"}, {"subnetId": "subnet-014b4077f42c44da6", "cidr": "172.20.128.0/19", "availabilityZone": "us-east-2b", "routeTableId": "rtb-0d44256bc65268228"}, {"subnetId": "subnet-03c0d591fc9b9b769", "cidr": "172.20.160.0/19", "availabilityZone": "us-east-2c", "routeTableId": "rtb-0aa2079febd33ec90"}]}, {"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-0032e2e43baadbee4", "cidr": "172.20.0.0/19", "availabilityZone": "us-east-2a", "routeTableId": "rtb-016e1c12cf1d86ae3"}, {"subnetId": "subnet-0f5b40452d23ca148", "cidr": "172.20.32.0/19", "availabilityZone": "us-east-2b", "routeTableId": "rtb-022773c5a11f6af90"}, {"subnetId": "subnet-0497c2c2d36e2666d", "cidr": "172.20.64.0/19", "availabilityZone": "us-east-2c", "routeTableId": "rtb-01b5f0bbc15c769d8"}]}]}, "vpc-provider:account=************:filter.vpc-id=vpc-0eff6844a45a7d98c:region=us-east-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-0eff6844a45a7d98c", "vpcCidrBlock": "172.20.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-034291bccb48832b2", "cidr": "172.20.96.0/19", "availabilityZone": "us-east-2a", "routeTableId": "rtb-03ab6f1eff73482cb"}, {"subnetId": "subnet-014b4077f42c44da6", "cidr": "172.20.128.0/19", "availabilityZone": "us-east-2b", "routeTableId": "rtb-0d44256bc65268228"}, {"subnetId": "subnet-03c0d591fc9b9b769", "cidr": "172.20.160.0/19", "availabilityZone": "us-east-2c", "routeTableId": "rtb-0aa2079febd33ec90"}]}, {"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-0032e2e43baadbee4", "cidr": "172.20.0.0/19", "availabilityZone": "us-east-2a", "routeTableId": "rtb-016e1c12cf1d86ae3"}, {"subnetId": "subnet-0f5b40452d23ca148", "cidr": "172.20.32.0/19", "availabilityZone": "us-east-2b", "routeTableId": "rtb-022773c5a11f6af90"}, {"subnetId": "subnet-0497c2c2d36e2666d", "cidr": "172.20.64.0/19", "availabilityZone": "us-east-2c", "routeTableId": "rtb-01b5f0bbc15c769d8"}]}]}, "ssm:account=************:parameterName=/aws/service/ecs/optimized-ami/amazon-linux-2/gpu/recommended/image_id:region=us-west-2": "ami-0e5f705b2f8b60fa3", "ssm:account=************:parameterName=/network-stack/core-vpc-id:region=us-west-2": "vpc-052a14fb3aa70e776", "vpc-provider:account=************:filter.isDefault=false:filter.vpc-id=vpc-052a14fb3aa70e776:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-052a14fb3aa70e776", "vpcCidrBlock": "192.168.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-08fef71ed3582973b", "cidr": "192.168.96.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0d2451fe4cec9914a"}, {"subnetId": "subnet-079efcd17d46e58e5", "cidr": "192.168.112.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-01eb7c8f99f6068f7"}, {"subnetId": "subnet-04ea25ea0093e66a4", "cidr": "192.168.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-02a0db972176ef3b8"}]}, {"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-0ef97dd0c62b7874e", "cidr": "192.168.0.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-003ec55891f98e67a"}, {"subnetId": "subnet-06c3baab4ee6818b7", "cidr": "192.168.16.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0d00605e4e7d9b6e7"}, {"subnetId": "subnet-0b3e0367607da5093", "cidr": "192.168.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0e077cb674b4e7333"}]}]}, "ssm:account=************:parameterName=tgw-id:region=us-west-2": "tgw-08ce9ea2424be06e2", "ssm:account=************:parameterName=coreVpcTgwAttachmentId:region=us-west-2": "tgw-attach-0d98d182c72b70890", "ssm:account=************:parameterName=/static-site-stack/landing-page:region=us-east-1": "arn:aws:s3:::landing-page.dev.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/releases:region=us-east-1": "arn:aws:s3:::releases.dev.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/docs:region=us-east-1": "arn:aws:s3:::docs.dev.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/dashboard:region=us-east-1": "arn:aws:s3:::dashboard.dev.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/download-assets:region=us-east-1": "arn:aws:s3:::download-assets.dev.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/landing-page:region=us-east-1": "arn:aws:s3:::landing-page.prod.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/releases:region=us-east-1": "arn:aws:s3:::releases.prod.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/docs:region=us-east-1": "arn:aws:s3:::docs.prod.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/dashboard:region=us-east-1": "arn:aws:s3:::dashboard.prod.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/download-assets:region=us-east-1": "arn:aws:s3:::download-assets.prod.getunblocked.com", "ssm:account=************:parameterName=/static-site-stack/landing-page-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-landing-page", "ssm:account=************:parameterName=/static-site-stack/releases-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-releases", "ssm:account=************:parameterName=/static-site-stack/docs-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-docs", "ssm:account=************:parameterName=/static-site-stack/dashboard-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-dashboard", "ssm:account=************:parameterName=/static-site-stack/download-assets-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-download-assets", "ssm:account=************:parameterName=/static-site-stack/landing-page-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-landing-page", "ssm:account=************:parameterName=/static-site-stack/releases-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-releases", "ssm:account=************:parameterName=/static-site-stack/docs-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-docs", "ssm:account=************:parameterName=/static-site-stack/dashboard-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-dashboard", "ssm:account=************:parameterName=/static-site-stack/download-assets-deployer-role:region=us-east-1": "arn:aws:iam::************:role/S3StaticSiteDeployerRole-download-assets", "vpc-provider:account=************:filter.vpc-id=vpc-052a14fb3aa70e776:region=us-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-052a14fb3aa70e776", "vpcCidrBlock": "192.168.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "private", "type": "Private", "subnets": [{"subnetId": "subnet-0558ba8bf29d62593", "cidr": "192.168.48.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-095283e3ea0635a0c"}, {"subnetId": "subnet-06973d849088ca159", "cidr": "192.168.64.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-075d860c6878c235e"}, {"subnetId": "subnet-0bf9001944b986c90", "cidr": "192.168.80.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0188eebf1f71509d2"}]}, {"name": "isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-08fef71ed3582973b", "cidr": "192.168.96.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-0d2451fe4cec9914a"}, {"subnetId": "subnet-079efcd17d46e58e5", "cidr": "192.168.112.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-01eb7c8f99f6068f7"}, {"subnetId": "subnet-04ea25ea0093e66a4", "cidr": "192.168.128.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-02a0db972176ef3b8"}]}, {"name": "public", "type": "Public", "subnets": [{"subnetId": "subnet-0ef97dd0c62b7874e", "cidr": "192.168.0.0/20", "availabilityZone": "us-west-2a", "routeTableId": "rtb-003ec55891f98e67a"}, {"subnetId": "subnet-06c3baab4ee6818b7", "cidr": "192.168.16.0/20", "availabilityZone": "us-west-2b", "routeTableId": "rtb-0d00605e4e7d9b6e7"}, {"subnetId": "subnet-0b3e0367607da5093", "cidr": "192.168.32.0/20", "availabilityZone": "us-west-2c", "routeTableId": "rtb-0e077cb674b4e7333"}]}]}}