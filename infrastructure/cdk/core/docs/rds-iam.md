### Logging in via password

```bash
export RDSHOST=<host>
export PG_USER="postgres"
export PGPASSWORD=<password from Secrets Manager>
psql "host=$RDSHOST port=5432 user=$PG_USER"
```

### Setting up IAM role for postgres

Notes:

-   The default `postgres` user will need its role adjusted via psql such that IAM authentication on our service pods will work.
-   For PostgreSQL, if the IAM role (rds_iam) is added to the master user, IAM authentication takes precedence over Password authentication so the master user has to log in as an IAM user.

```bash
GRANT rds_iam to postgres;
```

Once the main admin user `postgres` has `rds_iam` added, passworded login is no longer available.
To allow for passworded login via db tools, you will have to create a unique user on the database using psql.

```bash
create user unblocked with encrypted password '<1Password>';
# postgres requires a db associated with user to login
create database unblocked;
GRANT ALL PRIVILEGES ON DATABASE maindb TO unblocked;
GRANT ALL PRIVILEGES ON DATABASE unblocked TO unblocked;
GRANT rds_superuser to unblocked;
```

### Logging in via iam

You must do this on a pod with a serviceAccount `postgres-access` that has relevant IAM role associated with it.

```bash
export RDSHOST=<host>
export PG_USER="postgres"
export PGPASSWORD="$(aws rds generate-db-auth-token --hostname $RDSHOST --port 5432 --region us-west-2 --username $PG_USER )"
psql "host=$RDSHOST port=5432 user=$PG_USER"
```

### Useful links

[Setting up RDS IAM](https://gist.github.com/quiver/509e1a6e6b54a0148527553502e9f55d)
