# Data Pipeline

## Prerequisites

-   See [Kube README](../../../../docs/kube-setup.md).

## Overview

We are currently using [AWS step functions](https://docs.aws.amazon.com/step-functions/latest/dg/welcome.html) to
represent data pipelines.

Using step functions allows us to generate arbitrary state machines using the following services (and more).

-   [AWS Glue](https://docs.aws.amazon.com/glue/latest/dg/what-is-glue.html)
-   [AWS SageMaker](https://docs.aws.amazon.com/sagemaker/latest/dg/whatis.html)
-   [AWS Lambda](https://docs.aws.amazon.com/lambda/latest/dg/welcome.html)

Step functions also allow for shared input/output state to be passed between the various connected services using a
common JSON construct.

-   [Input and Output Processing](https://docs.aws.amazon.com/step-functions/latest/dg/concepts-input-output-filtering.html)

## Design

We are currently using [AWS CDK](https://docs.aws.amazon.com/cdk/v2/guide/home.html) to manage our data pipelines.

This allows us to not worry about resource generation and cleanup and focus on generating the various actors in the AWS
ecosystem.

We have generated a basic extensible DSL to define our data pipeline found in [build-config.ts](../lib/build-config.ts).
The basic DSL components are:

-   DataPipelineStateMachine
    -   GlueStage
    -   PreProcessStages
    -   SubPipelineStage
        -   TrainStage
        -   ProcessStage
        -   ModelStage
        -   ModelValidationStage

An example for how we define this DSL can be found in [dev-us-west-2.json](../config/dev-us-west-2.json).

This DSL is evaluated in the following CDK
construct [data-pipeline-state-machine-construct.s](../lib/common/s3/data-pipeline-state-machine-construct.ts) to generate
an AWS Step Functions state machine.
