# https://docs.aws.amazon.com/glue/latest/dg/aws-glue-programming-python-samples-legislators.html
# https://docs.aws.amazon.com/glue/latest/dg/aws-glue-api-crawler-pyspark-extensions-dynamic-frame.html#pyspark-apply_mapping-example
# To get auto-complete:
# pip3 install pyspark
# pip3 install git+https://github.com/awslabs/aws-glue-libs.git
import sys

from awsglue.context import GlueContext
from awsglue.job import Job
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from awsglue.dynamicframe import DynamicFrame
from pyspark.context import SparkContext

# Retrieve parameters for the Glue job.
# They must be passed in via arguments:
#   "PreProcessGlue": {
#     "--S3_SCM_INPUT_URI": "s3://scm-data-dev-us-west-2",
#     "--S3_PR_DATA_URI": "s3://pull-request-data-pipeline-sandbox-dev-us-west-2/data",
#   }
args = getResolvedOptions(sys.argv, ["JOB_NAME", "S3_SCM_INPUT_URI", "S3_PR_DATA_URI"])

sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
job = Job(glueContext)
job.init(args["JOB_NAME"], args)

s3_scm_input_uri = args["S3_SCM_INPUT_URI"]

# Write dataframes to the destination datastores.
s3_pr_data_uri = args["S3_PR_DATA_URI"]


def process_scm():
    # Create a PySpark dataframe from the source table.
    dynamic_frame = glueContext.create_dynamic_frame.from_options(
        "s3", {'paths': [s3_scm_input_uri],
               'recurse': True,
               'groupFiles': 'inPartition',
               'groupSize': '1048576'
               },
        format="json",
        format_options={
            "multiline": True,
        }
    )

    # Use AWS Glue ETL transformation functions
    # https://www.bmc.com/blogs/aws-glue-etl-transformations/
    # https://www.ilkkapeltola.fi/2019/04/aws-glue-python-applymapping.html
    selected_fields_frame = dynamic_frame.select_fields(
        paths=["title", "number", "mergeCommitSha", "htmlUrl", "body", "state", "createdAt", "mergedAt"])

    filtered_dynamic_frame = Filter.apply(
        frame=selected_fields_frame,
        f=lambda x: x["mergeCommitSha"] is not None
    )

    glueContext.write_dynamic_frame.from_options(
        frame=filtered_dynamic_frame,
        connection_type="s3",
        connection_options={"path": s3_pr_data_uri},
        format="json",
    )


# write out dataframes from buckets
process_scm()

# Complete the job.
job.commit()
