{"agent": {"metrics_collection_interval": 10, "run_as_user": "root"}, "metrics": {"namespace": "${namespace}", "aggregation_dimensions": [["AutoScalingGroupName"], ["InstanceId"], ["AutoScalingGroupName", "InstanceId"]], "append_dimensions": {"AutoScalingGroupName": "${!aws:AutoScalingGroupName}", "ImageId": "${!aws:ImageId}", "InstanceId": "${!aws:InstanceId}", "InstanceType": "${!aws:InstanceType}"}, "metrics_collected": {"cpu": {"measurement": ["cpu_usage_system"], "metrics_collection_interval": 10, "resources": ["*"], "totalcpu": true}, "disk": {"measurement": ["used_percent"], "metrics_collection_interval": 10, "resources": ["*"]}, "mem": {"measurement": ["mem_used_percent"], "metrics_collection_interval": 10}}}}