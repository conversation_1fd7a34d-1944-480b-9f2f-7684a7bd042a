#!/bin/bash

# Install Squid proxy
sudo yum clean metadata
sudo yum update -y
sudo yum install squid -y
sudo systemctl enable squid

# Create Squid config file
# This allows access from all addresses.

cat <<EOT > /etc/squid/squid.conf
# ACL My IP address
# acl myip src xxx.xxx.xxx.xxx/32
acl all src all
http_access allow all

#
# Recommended minimum configuration:
#
# Example rule allowing access from your local networks.
# Adapt to list your (internal) IP networks from where browsing
# should be allowed
acl localnet src 10.0.0.0/8      # RFC1918 possible internal network
acl localnet src *********/8   # RFC1918 possible internal network
acl localnet src ***********/16  # RFC1918 possible internal network
acl localnet src fc00::/7        # RFC 4193 local private network range
acl localnet src fe80::/10       # RFC 4291 link-local (directly plugged) machines
acl SSL_ports port 443
acl Safe_ports port 80          # http
acl Safe_ports port 21          # ftp
acl Safe_ports port 443         # https
acl Safe_ports port 70          # gopher
acl Safe_ports port 210         # wais
acl Safe_ports port 1025-65535  # unregistered ports
acl Safe_ports port 280         # http-mgmt
acl Safe_ports port 488         # gss-http
acl Safe_ports port 591         # filemaker
acl Safe_ports port 777         # multiling http
acl CONNECT method CONNECT
#
# Recommended minimum Access Permission configuration:
#
# Deny requests to certain unsafe ports
http_access deny !Safe_ports
# Deny CONNECT to other than secure SSL ports
http_access deny CONNECT !SSL_ports
# Only allow cachemgr access from localhost
http_access allow localhost manager
http_access deny manager
# We strongly recommend the following be uncommented to protect innocent
# web applications running on the proxy server who think the only
# one who can access services on "localhost" is a local user
#http_access deny to_localhost
#
# INSERT YOUR OWN RULE(S) HERE TO ALLOW ACCESS FROM YOUR CLIENTS
#
# Example rule allowing access from your local networks.
# Adapt localnet in the ACL section to list your (internal) IP networks
# from where browsing should be allowed
http_access allow localnet
http_access allow localhost
# And finally deny all other access to this proxy
http_access deny all
# Squid normally listens to port 3128
http_port 3128
# Uncomment and adjust the following to add a disk cache directory.
cache_dir ufs /var/spool/squid 10240 16 256

# Enable caching for specific HTTP requests
#refresh_pattern ^http://.*\.example\.com/.* 100 50% 1440

# Leave coredumps in the first cache dir
coredump_dir /var/spool/squid
#
# Add any of your own refresh_pattern entries above these.
#
#refresh_pattern ^ftp:        1440    20%    10080
#refresh_pattern ^gopher:    1440    0%    1440
#refresh_pattern -i (/cgi-bin/|\?) 0    0%    0
#refresh_pattern .        0    20%    4320
refresh_pattern .        1440 100%   1440
EOT

sudo systemctl start squid
sudo systemctl status squid
