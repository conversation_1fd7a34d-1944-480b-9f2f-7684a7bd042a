import os
import base64
import subprocess
import json

def lambda_handler(event, context):
    try:
        # Extract executor context and code from the payload
        executor_context = event.get("executor_context", {})
        env_vars = executor_context.get("env", {})
        encoded_code = event.get("code")

        if not encoded_code:
            return {
                "code": 1,
                "output": "Error: No code provided in the payload."
            }

        # Decode the Base64 encoded string
        try:
            decoded_code = base64.b64decode(encoded_code).decode("utf-8")
        except Exception as e:
            return {
                "code": 1,
                "output": f"Error decoding Base64 code: {str(e)}"
            }

        # Write the decoded code to a file under /tmp
        script_path = "/tmp/executor_script.py"
        try:
            with open(script_path, "w") as script_file:
                script_file.write(decoded_code)
        except Exception as e:
            return {
                "code": 1,
                "output": f"Error writing code to file: {str(e)}"
            }

        # Prepare the environment variables for the subprocess
        env = os.environ.copy()
        env.update(env_vars)


        # Run the Python code using a subprocess
        try:
            process = subprocess.Popen(
                ["python3", script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env
            )
            stdout, stderr = process.communicate()
            exit_code = process.returncode
        except Exception as e:
            return {
                "code": 1,
                "output": f"Error executing the Python code: {str(e)}"
            }

        # Prepare the output and response
        output = stdout.decode("utf-8") + stderr.decode("utf-8")
        return {
            "code": exit_code,
            "output": output
        }

    except Exception as e:
        return {
            "code": 1,
            "output": f"Unexpected error: {str(e)}"
        }
