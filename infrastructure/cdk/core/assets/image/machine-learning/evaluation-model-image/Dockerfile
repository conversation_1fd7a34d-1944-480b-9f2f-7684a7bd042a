FROM --platform="linux/amd64" python:3.11-slim-bookworm

# Necessary for bertopic
RUN apt-get update \
  && apt-get clean  \
  && apt-get autoclean \
  && apt-get autoremove

# Use Poetry to pin dependencies for sanity,
# and keep the development environment consistent with the production environment.
RUN pip3 install poetry==2.0.1

# Set up JFrog artifactory configuration such that we can pull from our private repositories
ARG JFROG_PASSWORD
RUN poetry config  http-basic.jfrog-server "<EMAIL>" "$JFROG_PASSWORD"

WORKDIR /home/<USER>/
# Install dependencies
COPY . /home/<USER>/

RUN POETRY_VIRTUALENVS_CREATE=false poetry install --no-cache --no-interaction
RUN poetry config virtualenvs.create false

# Define an entrypoint script for the docker image
ENTRYPOINT ["poetry", "run", "python", "/home/<USER>/src/evaluation_processor/fast_api.py"]

# https://docs.aws.amazon.com/sagemaker/latest/dg/your-algorithms-inference-code.html
# Sagemaker will override this and always use serve.
# Use ENTRYPOINT!
CMD ["serve"]
