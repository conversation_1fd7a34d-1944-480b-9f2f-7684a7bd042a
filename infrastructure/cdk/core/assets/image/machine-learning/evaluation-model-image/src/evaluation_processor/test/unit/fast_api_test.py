import unittest
from fastapi.testclient import TestClient
from evaluation_processor.fast_api import app

# Create a test client
test_client = TestClient(app=app)


class TestEvaluationProcessorFastAPI(unittest.TestCase):
    def test_ping(self):
        """Test the /ping endpoint to ensure the API is running."""
        response = test_client.get("/ping")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"message": "ok"})

    def test_evaluation_basic(self):
        """Test the evaluation endpoint with a basic request."""
        payload = {
            "input": "What is the capital of France?",
            "output": "The capital of France is Paris.",
            "context": [
                {"content": "France is a country in Europe. The capital is Paris."}
            ],
            "prompt_instructions": "# Instructions\n## Answer concisely\n## Provide factual information",
        }
        response = test_client.post("/invocations", json=payload)
        self.assertEqual(response.status_code, 200)
        self.assertIn(
            "test_results", response.json()
        )  # Ensure evaluation scores are in response

    def test_evaluation_missing_fields(self):
        """Test the evaluation endpoint with missing fields in the request."""
        payload = {
            "input": "What is the capital of France?",
            "output": "The capital of France is Paris.",
        }
        response = test_client.post("/invocations", json=payload)
        self.assertEqual(
            response.status_code, 422
        )  # Expecting validation error due to missing fields

    def test_evaluation_multiple_context(self):
        """Test the evaluation endpoint with multiple context documents."""
        payload = {
            "input": "Who discovered gravity?",
            "output": "Isaac Newton discovered gravity.",
            "context": [
                {"content": "Isaac Newton formulated the laws of motion and gravity."},
                {"content": "Gravity is a force that pulls objects toward the Earth."},
            ],
            "prompt_instructions": "# Instructions\n## Provide historical accuracy\n## Be clear and concise",
        }
        response = test_client.post("/invocations", json=payload)
        self.assertEqual(response.status_code, 200)
        self.assertIn(
            "test_results", response.json()
        )  # Ensure response contains evaluation scores

    def test_evaluation_malformed_input(self):
        """Test the evaluation endpoint with malformed JSON input."""
        payload = "{invalid_json: true}"  # Incorrect JSON format
        response = test_client.post(
            "/invocations", data=payload, headers={"Content-Type": "application/json"}
        )
        self.assertEqual(response.status_code, 422)  # Expecting a 402


if __name__ == "__main__":
    unittest.main()
