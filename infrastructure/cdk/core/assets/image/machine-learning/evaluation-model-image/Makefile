check: format test

run:
	poetry run python ./src/evaluation_processor/fast_api.py

format:
	poetry run black src

test:
	PYTHONPATH=./src poetry run pytest ./src/evaluation_processor/test/unit

docker-build:
	$(eval JFROG_PASSWORD := $(shell security find-generic-password -a "<EMAIL>" -l "poetry-repository-jfrog-server" -w 2>/dev/null))
	@JFROG_PASSWORD=$(JFROG_PASSWORD) docker compose build

docker-run:
	OPENAI_API_KEY=$(OPENAI_API_KEY) docker compose up

.PHONY: check format test
