[build-system]
build-backend = 'poetry.core.masonry.api'
requires = ['poetry-core']

[tool]
[tool.black]
extend-exclude = '__pycache__'
include = '\.pyi?$'
line-length = 120

[tool.poetry]
authors = []
description = ''
name = 'e5-mistral-embedding-model-image'
readme = 'README.md'
version = '0.1.0'

[tool.poetry.dependencies]
accelerate = '^0'
awscli = '^1'
boto3 = '^1'
botocore = '^1'
fastapi = "^0"
hf-transfer = '^0'
huggingface = '^0'
huggingface_hub = "0.25.2"
langchain = '^0'
numpy = '<2'
python = ">=3.10.12,<3.14"
retrying = '^1'
sentence-transformers = '2.2.2'

tenacity = '^9'
torch = '^2'
tqdm = '^4'
unblocked-embedding-utils = "^0"
uvicorn = "^0"

[tool.poetry.group]
[tool.poetry.group.dev]
[tool.poetry.group.dev.dependencies]
black = '^24'
pytest = '^8'

[[tool.poetry.packages]]
from = 'src'
include = 'e5_mistral_embedding'

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
