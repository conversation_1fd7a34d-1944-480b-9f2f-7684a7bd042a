check: format test

run:
	poetry run python ./src/e5_mistral_embedding/e5_mistral_main.py

docker-build:
	$(eval JFROG_PASSWORD := $(shell security find-generic-password -a "<EMAIL>" -l "poetry-repository-jfrog-server" -w 2>/dev/null))
	@JFROG_PASSWORD=$(JFROG_PASSWORD) docker-compose build

format:
	poetry run black src

test:
	poetry run pytest ./src/e5_mistral_embedding/test/unit


.PHONY: check format test
