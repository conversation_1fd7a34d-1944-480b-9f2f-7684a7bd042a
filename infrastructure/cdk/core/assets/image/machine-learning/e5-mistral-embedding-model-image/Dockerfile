FROM --platform="linux/amd64" nvidia/cuda:12.9.0-devel-ubuntu22.04

# Necessary for bertopic
RUN apt-get update \
  && apt-get clean  \
  && apt-get autoclean \
  && apt-get autoremove

# Install Git
RUN apt-get install -y git

RUN apt-get -y install python3
RUN apt-get -y install python3-pip
RUN apt-get -y install python3-pip
RUN apt-get -y install python-is-python3

# Use Poetry to pin dependencies for sanity,
# and keep the development environment consistent with the production environment.
RUN pip3 install poetry==2.1.2

# Set up JFrog artifactory configuration such that we can pull from our private repositories
ARG JFROG_PASSWORD
RUN poetry config  http-basic.jfrog-server "<EMAIL>" "$JFROG_PASSWORD"

WORKDIR /home/<USER>/
# Install dependencies
COPY . /home/<USER>/

# The install of torch is required for using CUDA (problem with poetry installation)
RUN POETRY_VIRTUALENVS_CREATE=false poetry install --no-cache --no-interaction --without dev \
    && pip3 install torch==2.7.0 --upgrade --no-cache-dir
RUN poetry config virtualenvs.create false

# Build with ninja
RUN pip3 uninstall -y ninja && pip install ninja

# Cannot install this via poetry
RUN MAX_JOBS=4 pip3 install flash-attn==2.8.0.post2 --no-build-isolation --no-cache-dir

# Define an entrypoint script for the docker image
ENTRYPOINT ["poetry", "run", "python", "/home/<USER>/src/e5_mistral_embedding/e5_mistral_main.py"]

# https://docs.aws.amazon.com/sagemaker/latest/dg/your-algorithms-inference-code.html
# Sagemaker will override this and always use serve.
# Use ENTRYPOINT!
CMD ["serve"]

