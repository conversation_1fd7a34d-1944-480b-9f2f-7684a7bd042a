import os
from pathlib import Path
from typing import Any, Optional

import uvicorn
from fastapi import FastAP<PERSON>, HTTPException

from embedding_utils.embedding_endpoint.embedding_endpoint_models import EmbeddingOutputResponse, EmbeddingInputsRequest
from e5_mistral_embedding.e5_mistral_inference import E5MistralInference

app = FastAPI()

_e5_mitral_inference = E5MistralInference()


@app.get("/ping")
async def ping():
    return {"message": "ok"}


@app.post("/", response_model=EmbeddingOutputResponse)
@app.post("/invocations", response_model=EmbeddingOutputResponse)
def invocations(inputs_request: EmbeddingInputsRequest) -> Any:
    try:
        return _e5_mitral_inference.get_embeddings(
            embedding_type=inputs_request.embedding_type,
            docs=inputs_request.inputs,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def start_server():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    log_config_path = f"{current_dir}/log_conf.yaml"
    # port 8080 required by sagemaker
    # https://docs.aws.amazon.com/sagemaker/latest/dg/your-algorithms-inference-code.html
    uvicorn.run(f"{Path(__file__).stem}:app", host="0.0.0.0", port=8080, reload=False, log_config=log_config_path)
