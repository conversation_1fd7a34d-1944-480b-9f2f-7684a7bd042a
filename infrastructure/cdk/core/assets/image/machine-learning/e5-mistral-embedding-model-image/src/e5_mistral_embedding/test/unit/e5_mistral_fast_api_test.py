import unittest

from embedding_utils.embedding_endpoint.embedding_endpoint_models import EmbeddingInputsRequest, EmbeddingOutputResponse

from e5_mistral_embedding.e5_mistral_fast_api import app
from fastapi.testclient import TestClient

test_client = TestClient(app=app)


class TestE5MistralFastAPI(unittest.TestCase):
    def test_e5_mistral_fast_api(self):
        message1 = "What is your name and where is <PERSON>?"
        inputs = EmbeddingInputsRequest(
            inputs=[message1],
            version="2",
            embedding_type="QUERY",
        )
        inputs_json = inputs.model_dump()

        response = test_client.post("/invocations", json=inputs_json)
        self.assertEqual(response.status_code, 200)
        output_response = EmbeddingOutputResponse.model_validate(response.json())
        self.assertEqual(len(output_response.dense_vectors), 1)
        self.assertEqual(len(output_response.sparse_vectors), 1)
