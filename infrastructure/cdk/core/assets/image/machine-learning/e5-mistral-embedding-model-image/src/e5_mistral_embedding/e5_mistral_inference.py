from embedding_utils.e5_mistral.e5_mistral_bm25 import E5MistralBM25EmbeddingGenerator
from embedding_utils.embedding_endpoint.embedding_endpoint_models import EmbeddingType
from embedding_utils.embedding_models import Embeddings


class E5MistralInference:
    _embedding_generator = E5MistralBM25EmbeddingGenerator()

    def get_embeddings(self, embedding_type: EmbeddingType, docs: list[str]) -> Embeddings:
        return self._embedding_generator.get_embeddings(embedding_type=embedding_type, docs=docs)
