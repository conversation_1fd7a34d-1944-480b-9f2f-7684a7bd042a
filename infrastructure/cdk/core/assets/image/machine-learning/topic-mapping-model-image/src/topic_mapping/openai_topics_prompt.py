from langchain.prompts import PromptTemplate
from langchain.chains.base import Chain

from langchain_core.prompts import Base<PERSON>romptTemplate, HumanMessagePromptTemplate
from langchain_core.prompts.chat import BaseMessagePromptTemplate
from llm_prompt_utils.llm_prompt import LLMPrompt


class OpenAITopicsPrompt(LLMPrompt):
    _llm_chain: Chain

    _prompt = """
You are given a list of SOURCE TOPICS and a single DOCUMENT, and it is your job to analyze the DOCUMENT to determine if the SOURCE TOPIC is a REPRESENTED SOURCE TOPIC.
A REPRESENTED SOURCE TOPIC is a SOURCE TOPIC that is strongly semantically related to the DOCUMENT with a confidence score greater than 0.

SOURCE TOPICS:
{{topics}}

DOCUMENT:
{{document}}

Follow these instructions when outputting:
1. The confidence score should reflect the certainty of the REPRESENTED SOURCE TOPIC with 1 being very certain and 0 being very uncertain.
2. REPRESENTED SOURCE TOPICs must be in the SOURCE TOPICS list. Never output anything that is not in the SOURCE TOPICS list.
3. Provide your answer in JSON structure like this:
{"topics": [{"topic": "Topic1", "confidence_score":0.8}, {"topic": "Topic2", "confidence_score":0.5}]}
4. At the end, do a final check to make sure that the REPRESENTED SOURCE TOPICs are be in the SOURCE TOPICS list. Never output anything that is not in the SOURCE TOPICS list.
"""

    _inputs = ["topics", "document"]

    def get_prompt_template(self) -> BaseMessagePromptTemplate:
        return HumanMessagePromptTemplate.from_template(
            template=self._prompt, input_variables=self._inputs, template_format="jinja2"
        )

    def get_inputs(self) -> list[str]:
        return self._inputs
