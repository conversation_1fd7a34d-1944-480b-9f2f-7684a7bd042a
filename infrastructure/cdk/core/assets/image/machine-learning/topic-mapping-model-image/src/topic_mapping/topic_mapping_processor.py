import json
import logging
from typing import List, Optional

from embedding_utils.instructor.instructor_bm25 import Instructor<PERSON>25EmbeddingGenerator
from llm_prompt_utils.openai_inference_chain import OpenAIInferenceChain

from pinecone_utils.pinecone_index_config import IndexConfig, Metric
from pinecone_utils.pinecone_vector_store import PineconeVectorStore
from vector_store_utils.vector_store_types import V<PERSON><PERSON><PERSON><PERSON>, MetadataFilter

from topic_mapping.openai_topics_prompt import OpenAITopicsPrompt
from topic_mapping.openai_constants import OPENAI_API_KEY, DEFAULT_OPENAI_CHAT_MODEL
from topic_mapping.pinecone_constants import PINECONE_API_KEY


class TopicMappingProcessor:
    __openai_topics_chain = OpenAIInferenceChain(
        llm_human_prompt=OpenAITopicsPrompt(),
        openai_api_key=OPENAI_API_KEY,
        model_name=DEFAULT_OPENAI_CHAT_MODEL,
        temperature=0,
        model_kwargs={
            "response_format": {"type": "json_object"},
            "seed": 42,
        },
    )
    __embedding_generator = InstructorBM25EmbeddingGenerator()

    def __init__(
            self,
            pinecone_namespace: str,
            pinecone_hybrid_index: str,
            pinecone_dimension: int = 768,
            pinecone_metric: Metric = Metric.DOTPRODUCT,
            repo_id: Optional[str] = None,
            api_key: str = PINECONE_API_KEY,
    ):
        self.__repo_id = repo_id
        self.__namespace = pinecone_namespace
        # instantiate the new PineconeVectorStore
        index_cfg = IndexConfig(
            name=pinecone_hybrid_index,
            dimension=pinecone_dimension,
            metric=pinecone_metric,
        )
        self.__vector_store = PineconeVectorStore(
            api_key=api_key,
            index_config=index_cfg,
        )

    def get_topic_mappings(
            self,
            document: str,
            alpha: float,
            top_k: int,
    ) -> List[dict]:
        # build an optional repo_id filter
        filters = (
            MetadataFilter(key="repo_id", value=self.__repo_id)
            if self.__repo_id
            else None
        )

        # construct and run the vector query
        vec_q = VectorQuery(
            embedder=self.__embedding_generator,
            query_text=document,
            top_k=top_k,
            alpha=alpha,
            filters=filters,
        )
        matches = self.__vector_store.query(q=vec_q, namespace=self.__namespace)

        # collect all unique topics from metadata
        all_doc_topics = set()
        for m in matches:
            topics_json = m.metadata.get("topics", "[]")
            doc_topics = json.loads(topics_json)
            all_doc_topics.update(doc_topics)

        # ask OpenAI to map document → topics
        results = self.__openai_topics_chain.predict(
            inputs={
                "document": document,
                "topics": list(all_doc_topics),
            },
            return_only_outputs=True,
            response_format={"type": "json_object"},
        )

        logging.info(
            f"Document: {document!r}, "
            f"Topic Filter: {all_doc_topics!r}, "
            f"OpenAI result: {results!r}"
        )

        topic_mappings = json.loads(results.get("text", "{}"))
        return topic_mappings.get("topics", [])
