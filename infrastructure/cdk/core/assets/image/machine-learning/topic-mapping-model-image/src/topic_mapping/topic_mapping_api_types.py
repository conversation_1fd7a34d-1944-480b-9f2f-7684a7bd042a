from typing import Optional

from pydantic import BaseModel


class TopicMappingParameters(BaseModel):
    alpha: float
    top_k: int


class TopicMappingConfiguration(BaseModel):
    pinecone_namespace: str
    pinecone_hybrid_index: str
    repo_id: Optional[str] = None


class TopicMappingRequest(BaseModel):
    inputs: list[str]
    parameters: TopicMappingParameters
    configuration: TopicMappingConfiguration


class TopicMapping(BaseModel):
    topic: str
    confidence_score: float


class TopicMappingResponse(BaseModel):
    topic_mappings: list[list[TopicMapping]]
