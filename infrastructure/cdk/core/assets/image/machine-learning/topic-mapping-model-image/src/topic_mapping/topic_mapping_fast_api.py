# main.py
import os
from pathlib import Path
from typing import Any

import uvicorn
from fastapi import FastAPI, HTTPException

from topic_mapping.topic_mapping_api_types import TopicMappingRequest, TopicMappingResponse
from topic_mapping.topic_mapping_inference import TopicMappingInference

app = FastAPI()

_topic_mapping_inference = TopicMappingInference()


@app.get("/ping")
async def ping():
    return {"message": "ok"}


@app.post("/invocations", response_model=TopicMappingResponse)
def invocations(topic_mapping_request: TopicMappingRequest) -> Any:
    try:
        all_topic_mappings = _topic_mapping_inference.get_topic_mappings(
            topic_mapping_request=topic_mapping_request,
        )

        output = {
            "topic_mappings": all_topic_mappings,
        }

        return output
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def start_server():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    log_config_path = f"{current_dir}/log_conf.yaml"
    # port 8080 required by sagemaker
    # https://docs.aws.amazon.com/sagemaker/latest/dg/your-algorithms-inference-code.html
    uvicorn.run(f"{Path(__file__).stem}:app", host="0.0.0.0", port=8080, reload=False, log_config=log_config_path)
