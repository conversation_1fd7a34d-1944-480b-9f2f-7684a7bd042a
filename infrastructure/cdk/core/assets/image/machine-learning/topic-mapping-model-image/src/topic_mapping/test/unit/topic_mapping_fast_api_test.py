import re
import unittest
from fastapi.testclient import TestClient

from ...topic_mapping_api_types import TopicMappingResponse
from ...topic_mapping_fast_api import app

model_dir = "./test-data"

test_client = TestClient(app=app)


class TestTopicMappingInferenceHandler(unittest.TestCase):
    _repo_id = "d7b55614-cd0b-4e33-90fc-0bc4a7841546"
    _p_namespace = "9fae09b0-f090-4e04-8358-811b1c320da0"
    _p_hybrid_index = "dev-unblocked-cluster-topics"
    _configuration = {}

    # Hack seeing as G<PERSON><PERSON><PERSON><PERSON> is a horrible security tool that can't figure out the difference between namespaces and keys
    def substitute_name_inplace(self, text: str):
        # Using re.sub to replace occurrences of "p_" with "pinecone_" in-place
        return re.sub(r"\bp_(\w*)", r"pinecone_\1", text)

    def setUp(self):
        self._configuration = {
            # "repo_id": self._repo_id,
            self.substitute_name_inplace("p_namespace"): self._p_namespace,
            self.substitute_name_inplace("p_hybrid_index"): self._p_hybrid_index,
        }

    def test_topic_mapping_embedding_with_kubernetes(self):
        message1 = "Can we describe how we use autoscaling with kubernetes? In particular how does kubernetes deployment work? Also how do we scale using helm charts?"
        input_data = {
            "inputs": [message1],
            "configuration": self._configuration,
            "parameters": {
                "alpha": 0.5,
                "top_k": 30,
            },
        }

        response = test_client.post("/invocations", json=input_data)
        self.assertEqual(response.status_code, 200)
        output_response = TopicMappingResponse.parse_obj(response.json())
        self.assertTrue(len(output_response.topic_mappings) > 0)

    def test_topic_mapping_embedding_with_sourcemark(self):
        message1 = """
        The SourceMark engine is a key component of Next Chapter Software Inc's system that provides APIs to obtain sourcemarks that are up-to-date with respect to the Git workspace. The engine is primarily defined in the ISourceMarkProvider interface, which includes methods such as:

getSourceMarkLatestPoint: Given a sourcemark ID, returns the up-to-date point for the sourcemark with respect to the Git workspace.
getSourceMarkStreamForFile: Given a file, returns a stream of up-to-date points for every sourcemark in the file.
addActiveFiles: Sets the list of files in use by the IDE, so marks for these files can be prioritized and preloaded.
recalculateRepo: Fully recalculates a repo, computing updated sourcemark points.

The main implementation of this interface is the SourceMarkProvider class. It uses the SourceMarkManager to get a scheduler for a given repo, which can then be used to calculate points for sourcemarks.
The resolution algorithm, as described in "SourceMark Resolution Algorithm (aka How the Sourcemark Engine Works)", involves iterating over sourcemarks, checking if they are up-to-date, and if not, calculating the latest topological source point and updating the sourcemark range based on Git diff hunks.
A key optimization, as described in PR #970, is to use Git tree SHAs instead of commit SHAs and to cache marks by file and points by mark. This allows for O(1) lookup of marks for a file as long as the current commit has been previously fully calculated at least once.
        """
        input_data = {
            "inputs": [message1],
            "configuration": self._configuration,
            "parameters": {
                "alpha": 0.8,
                "top_k": 30,
            },
        }

        response = test_client.post("/invocations", json=input_data)
        self.assertEqual(response.status_code, 200)
        output_response = TopicMappingResponse.parse_obj(response.json())
        self.assertTrue(len(output_response.topic_mappings) > 0)

    def test_topic_mapping_embedding_no_repo_id(self):
        message1 = "Can we describe how we use autoscaling with kubernetes?"
        input_data = {
            "inputs": [message1],
            "configuration": self._configuration,
            "parameters": {
                "alpha": 0.5,
                "top_k": 3,
            },
        }

        response = test_client.post("/invocations", json=input_data)
        self.assertEqual(response.status_code, 200)
        output_response = TopicMappingResponse.parse_obj(response.json())
        self.assertTrue(len(output_response.topic_mappings) > 0)
        self.assertTrue(len(output_response.topic_mappings[0]) > 0)

    def test_topic_mapping_for_empty_sparse_vector(self):
        message1 = "What is your name?"
        input_data = {
            "inputs": [message1],
            "parameters": {"alpha": 0.8, "top_k": 15},
            "configuration": self._configuration,
        }

        response = test_client.post("/invocations", json=input_data)
        self.assertEqual(response.status_code, 200)
        output_response = TopicMappingResponse.parse_obj(response.json())
        self.assertEqual(len(output_response.topic_mappings), 1)
        self.assertTrue(len(output_response.topic_mappings[0]) == 0)

    def test_topic_mapping_embedding_empty_string(self):
        input_data = {
            "inputs": [""],
            "configuration": self._configuration,
            "parameters": {
                "alpha": 0.5,
                "top_k": 3,
            },
        }

        response = test_client.post("/invocations", json=input_data)
        self.assertEqual(response.status_code, 200)
        output_response = TopicMappingResponse.parse_obj(response.json())
        self.assertEqual(len(output_response.topic_mappings), 0)

