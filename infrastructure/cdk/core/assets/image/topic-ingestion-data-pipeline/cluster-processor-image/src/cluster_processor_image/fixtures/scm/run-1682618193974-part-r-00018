{"title": "Move MockObjects to parent package", "number": 4846, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4846"}
{"title": "Stop retry logs", "number": 4847, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847", "body": "When a log request fails, we should not retry as it causes an large loop of requests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847#pullrequestreview-1296530144", "body": ""}
{"comment": {"body": "Attempt starts at 0. aka for the first requests.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847#discussion_r1105076496"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847#pullrequestreview-1304255694", "body": ""}
{"comment": {"body": "nit, might be better to have this work the same as for `isRefreshRequest`, ie store the variable and use it in the if statement below, that makes it clear that the logic is similar", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847#discussion_r1110406390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847#pullrequestreview-1304256198", "body": ""}
{"comment": {"body": "Don't we want to *always* return false here for log requests, regardless of the index?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847#discussion_r1110406737"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847#pullrequestreview-1304256245", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847#pullrequestreview-1304310203", "body": ""}
{"comment": {"body": "No. The retry function is misnamed IMO.\r\n\r\nThis is called on the first request as well. So if we return false regardless of index, we will never make log requests.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4847#discussion_r1110445049"}}
{"title": "Gradle 8.0.1 upgrade", "number": 4848, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4848", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| gradle (source) | major | 7.6 -> 8.0 |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Potential Fix for missing files at end of tutorial", "number": 4849, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849", "body": "When going through the onboarding tutorial for the first time as the first user, there was a chance that no top files would appear in the final step.\nThis is due to a race condition on when getTopFiles is called & PR ingestion starts.\ngetTopFiles depends on the initialization of SMStore. If SMStore is initialized before PR ingestion occurs (expected behaviour), this leads to getTopFiles returning empty.\nSince we call getTopFiles eagerly in the tutorial, which is immediately after repo installation, there's a chance that getTopFiles returns empty when it could potentially return files once PR ingestion has ingested some files.\nThis is ultimately a design problem as there's no guarantee that PR ingestion has pulled in data before we get to the end of tutorial. In the meantime, the workaround is to try calling getTopFiles again right before we display the top files page if the initial request returned empty."}
{"comment": {"body": "@kaych Updated the code to be stream based. Should take a second look.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#issuecomment-1428911754"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296561935", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296607248", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296621541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296645454", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296661651", "body": ""}
{"comment": {"body": "The general approach here makes sense -- you could maybe use `xstream.periodic` instead of a manual timer and trigger stream?\r\n\r\n```\r\nxstream.periodic(ms).filter(() => this.isPolling)\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#discussion_r1105163258"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296664677", "body": ""}
{"comment": {"body": "I'm guessing the benefit to having this as a stream is that once repos update we resolve immediately?  How important is that?  The code might be easier to read as a retried promise, since it's meant to resolve to a value.  I don't feel particularly strongly about this though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#discussion_r1105167026"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296665909", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296668806", "body": ""}
{"comment": {"body": "Done with streams for two reasons.\r\n1. To be reactive\r\n2. Able to get repo data without caching the data locally", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#discussion_r1105172435"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296669768", "body": ""}
{"comment": {"body": "> The general approach here makes sense -- you could maybe use xstream.periodic instead of a manual timer and trigger stream?\r\n\r\nOne thing I like about the existing approach is the ability to immediately trigger a fetch. I didn't think that was reproducible with stream.periodic. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#discussion_r1105173688"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#pullrequestreview-1296670909", "body": ""}
{"comment": {"body": "Ah I missed that it was also triggered on a user action", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4849#discussion_r1105175145"}}
{"title": "Move clearCalculatedSourcePoints to SourceMarkStore", "number": 485, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/485"}
{"title": "Add BitbucketCommit to allow getting the full commit sha for a merged pull request", "number": 4850, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4850", "body": "Bitbucket pull requests only include a truncated version of the merge commit SHA, so when a pull request is merged we'll need to hit the get commit operation to get the merged-at date and full commit SHA which we'll need for the sourcemark."}
{"comment": {"body": "This get commit endpoint requires repo read access though...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4850#issuecomment-1428816931"}}
{"comment": {"body": "> This get commit endpoint requires repo read access though...\r\n\r\nTurns out we have no choice. If we want WRITE access to PRs, then we automatically get WRITE access to their source. Totally fucked up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4850#issuecomment-1428874583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4850#pullrequestreview-1296574818", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4850#pullrequestreview-1296620499", "body": ""}
{"title": "Use a fallback service for generating insight to topics mapping", "number": 4851, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4851", "body": "PowerML endpoint can be flaky.\nWe need to make it more resilient."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4851#pullrequestreview-1296606119", "body": ""}
{"comment": {"body": "So this will get topic mappings for every `insightTopicsServices`? If you just want to call the search version if power ml fails you can do `insightTopicsServices.any { }` but up to you.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4851#discussion_r1105130896"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4851#pullrequestreview-1296607299", "body": ""}
{"comment": {"body": "No, this will return at the first success point.\r\nIf powerml succeeds, there is no call to searchService as the return is an early abort.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4851#discussion_r1105131779"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4851#pullrequestreview-1296608453", "body": ""}
{"comment": {"body": "Unless I'm misunderstanding your question :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4851#discussion_r1105132653"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4851#pullrequestreview-1296609897", "body": ""}
{"comment": {"body": "Sorry, yah I missed the return statement", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4851#discussion_r1105133821"}}
{"title": "Shut down poller unless it has been recently focused", "number": 4852, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852", "body": ""}
{"comment": {"body": "Pausing before shutting down the poller makes sense, but I don't think it solves the root problem?  We also need to ensure that a first poll for each channel occurs?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852#issuecomment-1428879950"}}
{"comment": {"body": "> Pausing before shutting down the poller makes sense, but I don't think it solves the root problem? We also need to ensure that a first poll for each channel occurs?\r\n\r\n30 seconds is more than enough time for the first poll to take place.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852#issuecomment-1428893448"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852#pullrequestreview-1296617154", "body": ""}
{"comment": {"body": "nit, if we stored the \"date when we should stop polling\" (ie, unfocused time plus 30 seconds) instead of the date when the window became unfocused, then this logic becomes a bit simpler to follow I think...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852#discussion_r1105139130"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852#pullrequestreview-1296617562", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852#pullrequestreview-1296619103", "body": ""}
{"comment": {"body": "Storing the event allows us to configure shutdown-per-store configuration values in the future, so prefer it this way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852#discussion_r1105140486"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852#pullrequestreview-1296620224", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4852#pullrequestreview-1296667180", "body": ""}
{"title": "Remove DropdownButton", "number": 4853, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4853", "body": "Remove dead code."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4853#pullrequestreview-1296636233", "body": ""}
{"title": "Add ability to send invite emails from admin console", "number": 4854, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4854"}
{"title": "chore(deps): update plugin io.freefair.github.dependency-submission to v6.6.2", "number": 4855, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4855", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.freefair.github.dependency-submission | 6.6.1 -> 6.6.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nfreefair/gradle-plugins\n\n### [`v6.6.2`](https://togithub.com/freefair/gradle-plugins/releases/tag/6.6.2)\n\n[Compare Source](https://togithub.com/freefair/gradle-plugins/compare/6.6.1...6.6.2)\n\n#### What's Changed\n\n-   Bump pluginVersion from 6.6 to 6.6.1 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/660](https://togithub.com/freefair/gradle-plugins/pull/660)\n-   Bump sass-embedded-host from 1.10.0 to 1.11.0 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/662](https://togithub.com/freefair/gradle-plugins/pull/662)\n-   Bump gradle-node-plugin from 3.5.0 to 3.5.1 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/668](https://togithub.com/freefair/gradle-plugins/pull/668)\n-   Bump classgraph from 4.8.152 to 4.8.153 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/664](https://togithub.com/freefair/gradle-plugins/pull/664)\n-   Bump mockito-core from 4.10.0 to 4.11.0 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/671](https://togithub.com/freefair/gradle-plugins/pull/671)\n-   Bump spring-boot-dependencies from 2.7.6 to 2.7.7 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/667](https://togithub.com/freefair/gradle-plugins/pull/667)\n-   Bump aspectjrt from ******* to 1.9.19 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/665](https://togithub.com/freefair/gradle-plugins/pull/665)\n-   Bump sass-embedded-host from 1.10.0 to 1.11.0 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/663](https://togithub.com/freefair/gradle-plugins/pull/663)\n-   Bump maven-model from 3.8.6 to 3.8.7 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/675](https://togithub.com/freefair/gradle-plugins/pull/675)\n-   Bump classgraph from 4.8.153 to 4.8.154 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/676](https://togithub.com/freefair/gradle-plugins/pull/676)\n-   Bump maven-model from 3.8.6 to 3.8.7 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/680](https://togithub.com/freefair/gradle-plugins/pull/680)\n-   Bump maven-core from 3.8.6 to 3.8.7 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/677](https://togithub.com/freefair/gradle-plugins/pull/677)\n-   Bump maven-plugin-api from 3.8.6 to 3.8.7 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/679](https://togithub.com/freefair/gradle-plugins/pull/679)\n-   Bump assertj-core from 3.23.1 to 3.24.0 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/681](https://togithub.com/freefair/gradle-plugins/pull/681)\n-   Bump assertj-core from 3.23.1 to 3.24.1 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/683](https://togithub.com/freefair/gradle-plugins/pull/683)\n-   Bump junit-bom from 5.9.1 to 5.9.2 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/684](https://togithub.com/freefair/gradle-plugins/pull/684)\n-   Bump plantuml from 1.2022.14 to 1.2023.0 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/687](https://togithub.com/freefair/gradle-plugins/pull/687)\n-   Bump junit-bom from 5.9.1 to 5.9.2 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/685](https://togithub.com/freefair/gradle-plugins/pull/685)\n-   Bump mavenPluginToolsVersion from 3.7.0 to 3.7.1 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/691](https://togithub.com/freefair/gradle-plugins/pull/691)\n-   Bump spring-boot-dependencies from 2.7.7 to 2.7.8 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/695](https://togithub.com/freefair/gradle-plugins/pull/695)\n-   Bump assertj-core from 3.24.1 to 3.24.2 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/690](https://togithub.com/freefair/gradle-plugins/pull/690)\n-   Bump maven-plugin-annotations from 3.7.0 to 3.7.1 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/694](https://togithub.com/freefair/gradle-plugins/pull/694)\n-   Bump plantuml from 1.2023.0 to 1.2023.1 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/697](https://togithub.com/freefair/gradle-plugins/pull/697)\n-   Bump org.apache.maven:maven-model from 3.8.7 to 3.9.0 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/704](https://togithub.com/freefair/gradle-plugins/pull/704)\n-   Bump de.larsgrefer.sass:sass-embedded-host from 1.11.0 to 1.12.0 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/708](https://togithub.com/freefair/gradle-plugins/pull/708)\n-   Bump org.glassfish.jaxb:jaxb-runtime from 2.3.7 to 2.3.8 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/706](https://togithub.com/freefair/gradle-plugins/pull/706)\n-   Bump org.apache.maven:maven-plugin-api from 3.8.7 to 3.9.0 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/705](https://togithub.com/freefair/gradle-plugins/pull/705)\n-   Bump org.apache.maven:maven-model from 3.8.7 to 3.9.0 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/710](https://togithub.com/freefair/gradle-plugins/pull/710)\n\n**Full Changelog**: https://github.com/freefair/gradle-plugins/compare/6.6.1...6.6.2\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency software.amazon.awssdk:bom to v2.20.3", "number": 4856, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4856", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.20.2 -> 2.20.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.20.3`]()\n\n[Compare Source]()\n\n#### **AWS Account**\n\n-   ### Features\n    -   This release of the Account Management API enables customers to view and manage whether AWS Opt-In Regions are enabled or disabled for their Account. For more information, see \n\n#### **AWS AppConfig Data**\n\n-   ### Features\n    -   AWS AppConfig now offers the option to set a version label on hosted configuration versions. If a labeled hosted configuration version is deployed, its version label is available in the GetLatestConfiguration response.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n-   ### Bugfixes\n    -   Keep precedence of options when passed to ProfileFileSupplier.aggregate\n\n#### **Amazon Import/Export Snowball**\n\n-   ### Features\n    -   Adds support for EKS Anywhere on Snowball. AWS Snow Family customers can now install EKS Anywhere service on Snowball Edge Compute Optimized devices.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Refactor GitHub and GitLab into ScmAuthApiFactory", "number": 4857, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4857"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4857#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4857#pullrequestreview-**********", "body": "LGTM. If you want to let me know when this lands, I'll exercise our posting-back-to-GitHub functionality to make sure everything works."}
{"title": "Add metric dimension cloudwatch", "number": 4858, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4858", "body": "This PR breaks down CloudWatch alarms per account so we could narrow down the event source easier."}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.20.4", "number": 4859, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4859", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.20.3 -> 2.20.4 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.20.3 -> 2.20.4 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.20.3 -> 2.20.4 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.20.3 -> 2.20.4 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.20.3 -> 2.20.4 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime (source) | 2.20.3 -> 2.20.4 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.20.3 -> 2.20.4 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.20.3 -> 2.20.4 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.20.3 -> 2.20.4 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.20.4`]()\n\n[Compare Source]()\n\n#### **AWS DataSync**\n\n-   ### Features\n    -   With this launch, we are giving customers the ability to use older SMB protocol versions, enabling them to use DataSync to copy data to and from their legacy storage arrays.\n\n#### **Amazon AppConfig**\n\n-   ### Features\n    -   AWS AppConfig now offers the option to set a version label on hosted configuration versions. Version labels allow you to identify specific hosted configuration versions based on an alternate versioning scheme that you define.\n\n#### **Amazon Elastic Compute Cloud**\n\n-   ### Features\n    -   With this release customers can turn host maintenance on or off when allocating or modifying a supported dedicated host. Host maintenance is turned on by default for supported hosts.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Instrumented tests with Thundra", "number": 486, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/486", "body": "\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/486#pullrequestreview-901031835", "body": ""}
{"title": "Basic gutter icon", "number": 4860, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4860", "body": "\nBasic setup to render gutter icons.\nCurrently hard-coded until we have models and store to back it."}
{"comment": {"body": "Planning on moving the IDE project into the `projects/app` module to get the benefits of our other Kotlin libraries. Would like to get this in before doing that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4860#issuecomment-1431709632"}}
{"comment": {"body": "I know you guys are used to typescript but as the code grows can you start namespacing the files, which is idiomatic in java land?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4860#issuecomment-1442110024"}}
{"comment": {"body": "> I know you guys are used to typescript but as the code grows can you start namespacing the files, which is idiomatic in java land?\r\n\r\nYeah as we work on this and this becomes more complicated we will be organizing this a bit more deliberately.  For now there's not much in here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4860#issuecomment-1442167995"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4860#pullrequestreview-1300397641", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4860#pullrequestreview-1300401069", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4860#pullrequestreview-1300516898", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4860#pullrequestreview-1311646703", "body": ""}
{"comment": {"body": "Be careful with this. You can hold on to editor references and they may never be gc'd. Consider weak references. \n\nIt entirely depends on how good your code is at cleaning up the reference list when appropriate (i.e. editor lifecycle)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4860#discussion_r1115921324"}}
{"title": "Introduce ScmUserApiFactory and ScmUserApi", "number": 4861, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4861", "body": "Replaces:\n- GitHubUserApiFactory\n- GitLabUserApiFactory"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4861#pullrequestreview-1300148726", "body": ""}
{"comment": {"body": "Should be ScmPullRequestReview.\r\n\r\nI'll fix in follow up PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4861#discussion_r1107566017"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4861#pullrequestreview-1300189432", "body": ""}
{"comment": {"body": "Done here https://github.com/NextChapterSoftware/unblocked/pull/4867", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4861#discussion_r1107601804"}}
{"title": "Bitbucket auth flow", "number": 4862, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4862", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4862#pullrequestreview-1300140963", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4862#pullrequestreview-1300142202", "body": ""}
{"comment": {"body": "These are all for you @davidkwlam - enjoy!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4862#discussion_r1107559683"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4862#pullrequestreview-1302644653", "body": ""}
{"title": "Fix up React dependencies", "number": 4863, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4863", "body": "Renovate auto-updated our version of React, but not the types.  This updates the types and brings react and react-dom up to parity.\nThis fixes a bunch of warnings displayed when running npm install"}
{"comment": {"body": "Renovate cannot update stuff that requires manual intervention.\r\nGood news is, this is a force function to keep our dependencies from getting stale.\r\nThanks for doing this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4863#issuecomment-1431776937"}}
{"comment": {"body": "> Renovate cannot update stuff that requires manual intervention.\r\n> Good news is, this is a force function to keep our dependencies from getting stale.\r\n> Thanks for doing this.\r\n\r\nYeah I don't mind having something push us a bit to keep dependencies up to date, this is a particularly tricky scenario where multiple dependencies have to be updated in sync or bugs will happen...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4863#issuecomment-1431788094"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4863#pullrequestreview-1300067651", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4863#pullrequestreview-1300106527", "body": ""}
{"title": "fix(deps): update sentryversion to v6.14.0", "number": 4864, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4864", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.sentry:sentry-logback | 6.13.1 -> 6.14.0 |  |  |  |  |\n| io.sentry:sentry-log4j2 | 6.13.1 -> 6.14.0 |  |  |  |  |\n| io.sentry:sentry | 6.13.1 -> 6.14.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ngetsentry/sentry-java\n\n### [`v6.14.0`](https://togithub.com/getsentry/sentry-java/blob/HEAD/CHANGELOG.md#6140)\n\n[Compare Source](https://togithub.com/getsentry/sentry-java/compare/6.13.1...6.14.0)\n\n##### Features\n\n-   Add time-to-full-display span to Activity auto-instrumentation ([#2432](https://togithub.com/getsentry/sentry-java/pull/2432))\n-   Add `main` flag to threads and `in_foreground` flag for app contexts  ([#2516](https://togithub.com/getsentry/sentry-java/pull/2516))\n\n##### Fixes\n\n-   Ignore Shutdown in progress when closing ShutdownHookIntegration ([#2521](https://togithub.com/getsentry/sentry-java/pull/2521))\n-   Fix app start span end-time is wrong if SDK init is deferred ([#2519](https://togithub.com/getsentry/sentry-java/pull/2519))\n-   Fix invalid session creation when app is launched in background ([#2543](https://togithub.com/getsentry/sentry-java/pull/2543))\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Fix bug with restoring archive pull requests", "number": 4865, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4865", "body": "Address this exception\n(columns%3A!(message)%2Cfilters%3A!(('%24state'%3A(store%3AappState)%2Cbool%3A(minimum_should_match%3A1%2Cshould%3A!((match_phrase%3A(_logzio_logceptions%3Aa90f2e68be07a395aae24b9ad5e05d39))))%2Cmeta%3A(alias%3AIllegalStateException%2Cdisabled%3A!f%2Ckey%3Abool%2ClogzInsights%3A!t%2Cnegate%3A!f%2Ctype%3Acustom%2Cvalue%3A'%7B%22minimum_should_match%22%3A1%2C%22should%22%3A%5B%7B%22match_phrase%22%3A%7B%22_logzio_logceptions%22%3A%22a90f2e68be07a395aae24b9ad5e05d39%22%7D%7D%5D%7D')))%2Cindex%3A'logzioCustomerIndex*'%2Cinterval%3Aauto%2Cquery%3A(language%3Alucene%2Cquery%3A'')%2Csort%3A!())&_g=(filters%3A!()%2CrefreshInterval%3A(pause%3A!t%2Cvalue%3A0)%2Ctime%3A(from%3Anow-1d%2Cto%3A'2023-02-15T09%3A10%3A00.000Z'))&accountIds=411850&switchToAccountId=411850&discoverTab=logz-exceptions-tab"}
{"title": "Focus refresh file source mark", "number": 4866, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4866", "body": "Added stream for visible files.\nWhenever one refocuses VSCode, it will recalculate source marks for visible editors.\nActive File:\n\nVisible File:\n"}
{"comment": {"body": "Updated with new focus manager.\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/*********-8bb9b9e4-ec5d-43e8-9832-3ac4173a1e8a.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4866#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4866#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4866#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4866#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4866#pullrequestreview-1300341209", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4866#pullrequestreview-1300344044", "body": ""}
{"title": "Use generic ScmPullRequestReview instead of GitHubPullRequestReview", "number": 4867, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4867", "body": "Needs to be done for all the other GitHub* types."}
{"title": "Use new React DOM API", "number": 4868, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4868", "body": "React 18 updated the react-dom API and deprecated the old one.  I made a small wrapper to log failures and allow simple one-method rendering.  This will get rid of all the errors/warnings in the console."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4868#pullrequestreview-1300249913", "body": "This guy is on a roll!!!\nGo Matt, GOOOOOO"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4868#pullrequestreview-1300322007", "body": ""}
{"title": "Refactor team member pages", "number": 4869, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4869", "body": "Minor refactor before making additional changes"}
{"title": "Removes duplicate thread ID in create/update APIs", "number": 487, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/487", "body": "Presume this was a bug. Since the threadId is already required in the path, no need to duplicate in the body."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/487#pullrequestreview-901320720", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/487#pullrequestreview-901320877", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/487#pullrequestreview-901819989", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/487#pullrequestreview-901993631", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/487#pullrequestreview-902011396", "body": ""}
{"title": "Persist user OAuth refresh token on Identity model", "number": 4870, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4870"}
{"title": "Move JB Plugin", "number": 4871, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4871", "body": "Move jet brains extension into projects/apps/ide to follow existing pattern.\nMotivation: shared config, shared code reuse, consistency, shared CI."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4871#pullrequestreview-1300448323", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4871#pullrequestreview-1300457336", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4871#pullrequestreview-1300459268", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4871#pullrequestreview-1300460444", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4871#pullrequestreview-1300460987", "body": ""}
{"title": "enable S3 bucket version", "number": 4872, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4872", "body": "Enabled bucket versioning for all customer asset buckets\nEnabled bucket versioning for our software releases bucket"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4872#pullrequestreview-1300520100", "body": ""}
{"title": "Fix archivePullRequests logic", "number": 4873, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4873"}
{"title": "Show the remaining pull requests yet to be fully ingested", "number": 4874, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4874"}
{"title": "Do not have time to address this properly, so log debug as info", "number": 4875, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4875", "body": "Log clietn debug as info until I can fix this properly via custom threshold filter."}
{"title": "Add ability to store s3 metadata into data assets", "number": 4876, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4876"}
{"title": "Revert \"Show the remaining pull requests yet to be fully ingested (#4874)\"", "number": 4877, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4877", "body": "This reverts commit 2231d1a03b5ba4f6b9ecb10fe13575786e88fe40."}
{"title": "Add s3 metadata during glue run for topics", "number": 4878, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4878"}
{"title": "Add Bitbucket comment class", "number": 4879, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879", "body": "Turns out the only way to get the (short) commit sha for a comment is to parse it from the code link.\nNotes\n- the endpoint to get pull request comments includes both code comments and top-level comments. The only difference between the two is the presence of the inline property, which specifies the file and line the code refers to\n- every comment (TLC or code) can have a reply. More over even code comment replies can have nested replies, which differs from GitHub where all threads are flat."}
{"comment": {"body": "> every comment (TLC or code) can have a reply. More over even code comment replies can have nested replies, which differs from GitHub where all threads are flat.\r\n\r\nIt get's worse:\r\n1. each comment can have many levels of nested replies, so you can reply to a reply of a reply ...\r\n    <img width=\"779\" alt=\"Screenshot 2023-02-15 at 22 24 58\" src=\"https://user-images.githubusercontent.com/1798345/219285454-439c34c9-b19e-47ee-8f02-811935d7e3d2.png\">\r\n\r\n2. they have 3 scopes where comments can be added: top-level comments, code-level comments, and **file-level** comments\r\n    <img width=\"711\" alt=\"Screenshot 2023-02-15 at 22 28 04\" src=\"https://user-images.githubusercontent.com/1798345/219285937-9343592a-b6d0-425a-b97f-761af435a3cf.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#issuecomment-1432590488"}}
{"comment": {"body": "> file-level comments\r\n\r\n@richiebres Thanks for the heads up. I guess in that case we can to just set `line` to 1.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#issuecomment-1432591449"}}
{"comment": {"body": "> > file-level comments\r\n> \r\n> @richiebres Thanks for the heads up. I guess in that case we can to just set `line` to 0.\r\n\r\noooh, actually we can do a `FileMark`, which is designed for this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#issuecomment-1432592351"}}
{"comment": {"body": "Ok having put some thought into this, I'm going to make some db updates to store the properties that indicate whether a comment is a reply to a reply. We'll flatten the thread before we return it to clients, but if/when we update our UI to allow multiple levels of replies we'll have all the data to support it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#issuecomment-1433457417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#pullrequestreview-1300809331", "body": ""}
{"comment": {"body": "> `https://api.bitbucket.org/2.0/repositories/davidkwlam/emptyrepo/diff/davidkwlam/emptyrepo:048a9f0a7a21..e510469f6f13?path=blah.txt`\r\n\r\nI might have this wrong... but I thought the first hash is the old base commit, and the second hash (`e510469f6f13` in this case) is the hash of new change commit. So we should be using the second one?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#discussion_r1108048846"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#pullrequestreview-1300823875", "body": ""}
{"comment": {"body": "The first one is the one we want. Yup, Bitbucket ftw.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#discussion_r1108058186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#pullrequestreview-1300824145", "body": ""}
{"comment": {"body": "e510469f6f13 in this case is the old base commit.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#discussion_r1108058397"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#pullrequestreview-1300839640", "body": ""}
{"comment": {"body": "wow", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#discussion_r1108069147"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4879#pullrequestreview-1301957960", "body": ""}
{"title": "Verbosely log only our own packages", "number": 488, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/488", "body": "Also fix an Exposed warning about SHA-1 column types"}
{"title": "Extra Spec File", "number": 4880, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4880", "body": "Introduce a spec file where one can introduce client side models.\nHaving these specced out will allow for a common language used across multiple languages.\nE.g. communication between IntelliJ (Kotlin) + Agent (TS)"}
{"comment": {"body": "Due to how duck typing works, these will just work with existing TS models.\r\n\r\nThe downside is there is duplication. One way we remove duplication more \"nested\" models.\r\nFor example, instead of redefining the `MessageAggregate` model, it could reference the existing `Message` model, removing the need to duplicate the properties.\r\n\r\n`ThreadAggregate` woul utilize this `MessageAggregate` model as `messageAggregate` so it would still need to redefine the rest of the `Thread` properties. The API `Message` model would now be nested one \"level\" deeper.`ThreadInfoAggregate.messageAggregate.message`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4880#issuecomment-1433430272"}}
{"comment": {"body": "What are the implications for versioning?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4880#issuecomment-1433443490"}}
{"comment": {"body": "> What are the implications for versioning?\r\n\r\nThe same story as today. If our API models are out of sync with the client, we need to handle that. \r\n\r\nThese \"extra\" models are purely client side. Similar to our protobuf models, as long as we ship the clients of these specs together, versioning shouldn't be a problem.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4880#issuecomment-1433455269"}}
{"title": "Deserialize next field for paginated Bitbucket responses", "number": 4881, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4881", "body": "We'll need this for pagination when bulk ingesting pull requests from Bitbucket."}
{"title": "Add prod s3 metadata", "number": 4882, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4882"}
{"title": "Add web titles as subtitles for threads in the Hub", "number": 4883, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4883", "body": "\n"}
{"comment": {"body": "@kaych can we add a bit more specificity to our dashboard page headers?\r\n- Unblocked - Mine\r\n- Unblocked - Recommended\r\n- Unblocked - Explore\r\n- Unblocked - [Sourcemark]\r\n- Unblocked - [Richie Bresnan]\r\n- Unblocked - Recently Deleted\r\n- Unblocked - Insight\r\n- Unblocked - Search Results for [search term]\r\n\r\nCurrently the titles that get slurped up just say \"Unblocked\".", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4883#issuecomment-1433717560"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4883#pullrequestreview-1302292951", "body": ""}
{"title": "Add search to mobile", "number": 4884, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4884", "body": "\nAlso:\n* Fix a couple of bugs in the current search/filter UI \n* Fix some small viewport layout issues in PR views"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4884#pullrequestreview-1308062291", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4884#pullrequestreview-1308063513", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4884#pullrequestreview-1308075874", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4884#pullrequestreview-1308242948", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4884#pullrequestreview-1308244366", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4884#pullrequestreview-1308671272", "body": ""}
{"comment": {"body": "Has numerous bug fixes for React 18 compatibility.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4884#discussion_r1113938314"}}
{"title": "Add minimum histogram threshold for slack", "number": 4885, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4885"}
{"title": "Add prCommentParentId property to track nested replies", "number": 4886, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4886", "body": "Bitbucket pull request comments allow for replies to have replies:\n\nUnblocked doesn't show comments like this (yet) but let's add a couple of properties to store these nested reply details. If/when we update our UI to support this type of nesting we'll have all the data we need to recreate it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4886#pullrequestreview-1302301853", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4886#pullrequestreview-1302306630", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4886#pullrequestreview-1302307143", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4886#pullrequestreview-1302325186", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4886#pullrequestreview-1302327723", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4886#pullrequestreview-1302328859", "body": ""}
{"title": "AddMoreLogging", "number": 4887, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4887", "body": "Add minimum histogram threshold for slack\nAdd more logging during slack ingestion for topics"}
{"title": "Add more logging for slack ingestion fo topics", "number": 4888, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4888"}
{"title": "Refactor ScmPullRequestComment for top level comments and file comments", "number": 4889, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4889", "body": "This will let us transform Bitbucket and GitHub pull request comments to the three main types of comments (top-level, file, and code). All comments are pretty much the same, with file and code comments having additional properties to specify the file and lines."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4889#pullrequestreview-1302461140", "body": ""}
{"comment": {"body": "This file contains the meat of the changes, all other changes are just renaming.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4889#discussion_r1109145952"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4889#pullrequestreview-1303687042", "body": ""}
{"title": "Video Channel Stores", "number": 489, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/489", "body": "Stores for video channel models"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/489#pullrequestreview-902253142", "body": ""}
{"title": "Make jetbrains webview build inremental", "number": 4890, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4890"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4890#pullrequestreview-1302516073", "body": ""}
{"title": "Private.yml Refactor + Support for Extra Aggregate Models", "number": 4891, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891", "body": "Refactored model definitions out of Private.yml and into individual files. \n\nReduces size of 7k LOC file to 4k\nAllows for better composition of models. Will allow for composition of API models for client side models (Aggregate models)\n\nAPIs & API specific models (parameters, security schemas) will still remain in Private.yml"}
{"comment": {"body": "Not fully clear what's going on, but definitely would not like duplication between these spec files and the private.yml spec file.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1433966738"}}
{"comment": {"body": "> Not fully clear what's going on, but definitely would not like duplication between these spec files and the private.yml spec file.\r\n\r\nWe're definitely not duplicating the spec content -- the difficulty is in generating deduplicated output.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1434967520"}}
{"comment": {"body": "@matthewjamesadam still don\u2019t follow.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1435007197"}}
{"comment": {"body": "Chatted IRL with Jeff. I get it now. The PR is incomplete: the idea is to pull refactor private.yml, so there is no duplication. All good. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1435055981"}}
{"comment": {"body": "Confused about the extra concept but happy to chat IRL about it to clarify.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1435424509"}}
{"comment": {"body": "> Confused about the extra concept but happy to chat IRL about it to clarify.\r\n\r\nFor IntelliJ, we need a common language / model to communicate between TS world and Kotlin world.\r\nCommunication is done by going from a TS model -> JSON -> piped through protobuf -> JSON -> Kotlin model.\r\n\r\nHaving these aggregate models live in a spec gives us a specced models for type safety & serialization.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1435425386"}}
{"comment": {"body": "> > Confused about the extra concept but happy to chat IRL about it to clarify.\r\n> \r\n> For IntelliJ, we need a common language / model to communicate between TS world and Kotlin world. Communication is done by going from a TS model -> JSON -> piped through protobuf -> JSON -> Kotlin model.\r\n> \r\n> Having these aggregate models live in a spec gives us a specced models for type safety & serialization.\r\n\r\nI guess I just feel the word `extra` is overloaded/vague. Why not `modelAggregates.yml` or `aggregates.yml` then? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1438852856"}}
{"comment": {"body": "> \r\n\r\nI'm not sure if it will be *just* aggregates in the future so had the naming intentionally vague. The naming of this spec can be changed anytime so will re-evaluate once this is built out some more.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1438911346"}}
{"comment": {"body": "If there are more specs in the future, maybe it'd warrant a new yml file? Like why go through the process of separating out/organizing these files if we're just gonna have another catch-all file of vague models \ud83e\udd14 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1439007625"}}
{"comment": {"body": "Chatted IRL with @jeffrey-ng . I get it now. The PR is incomplete: the idea is to pull refactor private.yml, so there is no duplication. All good.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#issuecomment-1518051492"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304074914", "body": ""}
{"comment": {"body": "@matthewjamesadam @kaych Due to duck typing, aggregate models defined in the extra.yml spec will just work with existing code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110235064"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304190612", "body": ""}
{"comment": {"body": "Updated task to remove API directories if \"component\" files change.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110349119"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304225896", "body": ""}
{"comment": {"body": "I thought we could get rid of these _index.yml files?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110384791"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304233732", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304238858", "body": ""}
{"comment": {"body": "Why did these macOS generated files change?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110393867"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304243217", "body": ""}
{"comment": {"body": "so this changed from VideoDraftCodeReferences. Is that intentional?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110397729"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304305785", "body": ""}
{"comment": {"body": "That's a mistake... I had made the update to fix this but it looks like codegen did *not* occur.\r\n\r\n`OpenApiIncrementalCleanTask` was referencing the incorrect folder to cleanup on code changes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110441906"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304319686", "body": ""}
{"comment": {"body": "Running into some problems here.\r\n\r\nS005\r\nThis is due to a single model that we do *not* use in our API requests, LoginState.  It is used by our backend to pass data to our web UI in a query parameter. Since this model isn't used in our API, we need to explicitly reference it in the schema section for codegen purposes.\r\n\r\nI think this exception is okay as there shouldn't be too many models in this schema section anymore.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110452681"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304322230", "body": ""}
{"comment": {"body": "Z001\r\n\r\nThis is more of a problem... Our integration with Zally passes a string representation of our schema to the linter. This doesn't handled all the relative imports, causing this z001 error.\r\n\r\nMight be related to these:\r\nhttps://github.com/zalando/zally/issues/1357\r\nhttps://github.com/zalando/zally/issues/1271", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110454604"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304322441", "body": ""}
{"comment": {"body": "Let me know if these need to be resolved before this PR goes in.\r\n@richiebres @rasharab ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110454777"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304352135", "body": ""}
{"comment": {"body": "nit: rename? `extra` is a very vague term? `modelAggregates.yml` ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110463929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304419944", "body": ""}
{"comment": {"body": "Maybe we can just remove LoginState. I'll take a look...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110474065"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1304449046", "body": ""}
{"comment": {"body": "So `LoginState` is actually used on both the server and the client. I don't think we should remove it from the spec.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1110476302"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#pullrequestreview-1306513147", "body": ""}
{"comment": {"body": "Yeah. We can't remove it. \r\n\r\nIt's not technically used in an API but is still a shared model between API and clients", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4891#discussion_r1112458609"}}
{"title": "adding macosvm + readme for unblocked macos testing", "number": 4892, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4892", "body": "download vm files:\n- s3://macos-virtual-machines/13.2.1/13.2.1_aux.img\n- s3://macos-virtual-machines/13.2.1/13.2.1_disk.img\n- s3://macos-virtual-machines/13.2.1/13.2.1_config.img\nrun the virtual machine\n./macosvm -g ./13.2.1_config.json"}
{"comment": {"body": "https://macos-virtual-machines.s3-us-west-2.amazonaws.com/readme.html", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4892#issuecomment-1434005792"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4892#pullrequestreview-1307969382", "body": ""}
{"title": "Refactor OAuth client config", "number": 4893, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4893"}
{"title": "Basic Kotlin client codegen", "number": 4894, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4894", "body": "Added basic hook to generate client data models (No APIs atm)\nCurrently being used for IntelliJ plugin"}
{"comment": {"body": "\r\nCurrently being generated using moshi for JSON serialization. Do we want to move this to kotlinx? Would require custom templates similar to backend.\r\n\r\nHere's an example\r\n```\r\n/**\r\n *\r\n * Please note:\r\n * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).\r\n * Do not edit this file manually.\r\n *\r\n */\r\n\r\n@file:Suppress(\r\n    \"ArrayInDataClass\",\r\n    \"EnumEntryName\",\r\n    \"RemoveRedundantQualifierName\",\r\n    \"UnusedImport\"\r\n)\r\n\r\npackage com.nextchaptersoftware.api.client.models\r\n\r\n\r\nimport com.squareup.moshi.Json\r\n\r\n/**\r\n * \r\n *\r\n * Values: vscode,hub,safari,chrome,dashboard,intellij\r\n */\r\n\r\nenum class AgentType(val value: kotlin.String) {\r\n\r\n    @Json(name = \"vscode\")\r\n    vscode(\"vscode\"),\r\n\r\n    @Json(name = \"hub\")\r\n    hub(\"hub\"),\r\n\r\n    @Json(name = \"safari\")\r\n    safari(\"safari\"),\r\n\r\n    @Json(name = \"chrome\")\r\n    chrome(\"chrome\"),\r\n\r\n    @Json(name = \"dashboard\")\r\n    dashboard(\"dashboard\"),\r\n\r\n    @Json(name = \"intellij\")\r\n    intellij(\"intellij\");\r\n\r\n    /**\r\n     * Override toString() to avoid using the enum variable name as the value, and instead use\r\n     * the actual value defined in the API spec file.\r\n     *\r\n     * This solves a problem when the variable name and its value are different, and ensures that\r\n     * the client sends the correct enum values to the server always.\r\n     */\r\n    override fun toString(): String = value\r\n\r\n    companion object {\r\n        /**\r\n         * Converts the provided [data] to a [String] on success, null otherwise.\r\n         */\r\n        fun encode(data: kotlin.Any?): kotlin.String? = if (data is AgentType) \"$data\" else null\r\n\r\n        /**\r\n         * Returns a valid [AgentType] for [data], null otherwise.\r\n         */\r\n        fun decode(data: kotlin.Any?): AgentType? = data?.let {\r\n          val normalizedData = \"$it\".lowercase()\r\n          values().firstOrNull { value ->\r\n            it == value || normalizedData == \"$value\".lowercase()\r\n          }\r\n        }\r\n    }\r\n}\r\n\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4894#issuecomment-1434897302"}}
{"comment": {"body": "Class\r\n\r\n```\r\n/**\r\n *\r\n * Please note:\r\n * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).\r\n * Do not edit this file manually.\r\n *\r\n */\r\n\r\n@file:Suppress(\r\n    \"ArrayInDataClass\",\r\n    \"EnumEntryName\",\r\n    \"RemoveRedundantQualifierName\",\r\n    \"UnusedImport\"\r\n)\r\n\r\npackage com.nextchaptersoftware.api.client.models\r\n\r\n\r\nimport com.squareup.moshi.Json\r\n\r\n/**\r\n * \r\n *\r\n * @param id The ID of a resource that can be retrieved from the service.\r\n * @param name \r\n * @param contentLength \r\n * @param contentType \r\n * @param threadId The ID of a resource that can be retrieved from the service.\r\n * @param messageId The ID of a resource that can be retrieved from the service.\r\n * @param isDeleted \r\n */\r\n\r\n\r\ndata class Asset (\r\n\r\n    /* The ID of a resource that can be retrieved from the service. */\r\n    @Json(name = \"id\")\r\n    val id: java.util.UUID,\r\n\r\n    @Json(name = \"name\")\r\n    val name: kotlin.String,\r\n\r\n    @Json(name = \"contentLength\")\r\n    val contentLength: kotlin.Int,\r\n\r\n    @Json(name = \"contentType\")\r\n    val contentType: kotlin.String,\r\n\r\n    /* The ID of a resource that can be retrieved from the service. */\r\n    @Json(name = \"threadId\")\r\n    val threadId: java.util.UUID? = null,\r\n\r\n    /* The ID of a resource that can be retrieved from the service. */\r\n    @Json(name = \"messageId\")\r\n    val messageId: java.util.UUID? = null,\r\n\r\n    @Json(name = \"isDeleted\")\r\n    val isDeleted: kotlin.Boolean? = null\r\n\r\n)\r\n\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4894#issuecomment-1434898278"}}
{"comment": {"body": "Yes, please use Kotlin serialization if possible.\r\n\r\nOther use case is the `UnblockedClientApi` used for API testing, which is currently hand crafted and brittle.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4894#issuecomment-1434953953"}}
{"comment": {"body": "Yes. Moshi serialization performance isn't as good as kotlinx on top of i do not want to bring in another serialization package dependency and it's associated nuances.\n\nI had to do the same for the server side code, and it wasnt too bad. About a days work.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4894#issuecomment-1435003919"}}
{"comment": {"body": "Client code-gen should be much simpler than server code-gen. Rashin and I have lot's of experience with mustache, so we can help.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4894#issuecomment-1435054695"}}
{"comment": {"body": "Updated client code-get to use kotlinx. Added custom data-class template to remove '@deprecated' property to models.\r\n\r\nExample: \r\n```\r\n/**\r\n *\r\n * Please note:\r\n * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).\r\n * Do not edit this file manually.\r\n *\r\n */\r\n\r\n@file:Suppress(\r\n    \"ArrayInDataClass\",\r\n    \"EnumEntryName\",\r\n    \"RemoveRedundantQualifierName\",\r\n    \"UnusedImport\"\r\n)\r\n\r\npackage com.nextchaptersoftware.api.client.models\r\n\r\nimport com.nextchaptersoftware.api.client.models.LegacySourcePoint\r\n\r\nimport kotlinx.serialization.*\r\nimport kotlinx.serialization.descriptors.*\r\nimport kotlinx.serialization.encoding.*\r\n\r\n/**\r\n * \r\n *\r\n * @param id The ID of a resource that can be retrieved from the service.\r\n * @param repoId The ID of a resource that can be retrieved from the service.\r\n * @param sourcePoint \r\n */\r\n@Serializable\r\n\r\ndata class NewSourceMark (\r\n\r\n    /* The ID of a resource that can be retrieved from the service. */\r\n    @SerialName(value = \"id\") @Required val id: kotlin.String,\r\n\r\n    /* The ID of a resource that can be retrieved from the service. */\r\n    @SerialName(value = \"repoId\") @Required val repoId: kotlin.String,\r\n\r\n    @Deprecated(message = \"This property is deprecated.\")\r\n    @SerialName(value = \"sourcePoint\") @Required val sourcePoint: LegacySourcePoint\r\n\r\n)\r\n\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4894#issuecomment-1438967947"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4894#pullrequestreview-1308026059", "body": ""}
{"title": "Removing sandboxed ml files", "number": 4895, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4895", "body": "\nMoved to above repository."}
{"title": "JB agent protobuf generation and call", "number": 4896, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896#pullrequestreview-1307936031", "body": ""}
{"comment": {"body": "Do we need to wait for the agentAPI to start, similar to line 46?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896#discussion_r1113409330"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896#pullrequestreview-1307937009", "body": ""}
{"comment": {"body": "No because we `.await()` just below here, within the coroutine", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896#discussion_r1113410062"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896#pullrequestreview-1307940715", "body": ""}
{"comment": {"body": "Idea would be to update RepoStore at this point?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896#discussion_r1113412632"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896#pullrequestreview-1307941530", "body": ""}
{"comment": {"body": "Yes exactly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896#discussion_r1113413253"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4896#pullrequestreview-1307945341", "body": ""}
{"title": "Stop polling from Hub when laptop is locked or asleep", "number": 4897, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4897", "body": "Also increased polling interval to 5 seconds"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4897#pullrequestreview-1304219889", "body": ""}
{"title": "Do not remove pr links from text", "number": 4898, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4898"}
{"title": "Optimize sanitizer script for powerml", "number": 4899, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4899"}
{"title": "Clean up files", "number": 49, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/49", "body": "Remove bs files and move makefile down one level."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/49#pullrequestreview-854839637", "body": ""}
{"title": "Command + Enter to send message", "number": 490, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/490", "body": "Refactor out message editor button.\nAllow for Command + Enter to send message."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/490#pullrequestreview-902012878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/490#pullrequestreview-902091054", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/490#pullrequestreview-902252677", "body": ""}
{"title": "Add Bitbucket comment transformers", "number": 4900, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4900"}
{"title": "Use client asset repo submodule", "number": 4901, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4901", "body": "I've moved the large client assets (the VSCode onboarding videos) into a submodule here: .\nThis PR adds that repo as a submodule at /shared/clientAssets and uses the assets from there.  The point of doing this now is to stop the bleeding and ensure any future videos are added in a submodule, which can be swapped out etc in the future if it gets too big."}
{"comment": {"body": "need to update installer build to pull the real assets in?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4901#issuecomment-1435383304"}}
{"comment": {"body": "No this still uses real assets for every build -- it doesn't change our build at all, just moves where the assets are pulled from", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4901#issuecomment-1435383746"}}
{"comment": {"body": "Not sure what the point of that is, but you'll have to update the GitHub action to clone submodules since it does not clone submodule by default.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4901#issuecomment-1435387905"}}
{"comment": {"body": "> Not sure what the point of that is, but you'll have to update the GitHub action to clone submodules since it does not clone submodule by default.\r\n\r\nAs in the description, the point is to stop the bleeding -- every time we rev the onboarding videos this gets worse, so for now this will stop it from getting worse, or at least let us clean up the mess by changing the submodules around.  If you don't think it's worthwhile that's fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4901#issuecomment-1435388771"}}
{"comment": {"body": "Actually, I guess it's a win since we would only invoke submodule clone for VSCode CI.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4901#issuecomment-1435388954"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4901#pullrequestreview-1308317529", "body": ""}
{"title": "Remove Server and Date default headers", "number": 4902, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4902", "body": "Request:\ncurl -X GET '' -i\nResponse:\nserver: Ktor/debug\ndate: Sat, 18 Feb 2023 01:10:49 GMT"}
{"title": "Ability to refresh user profiles", "number": 4903, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4903", "body": "Manually for now, will be background job."}
{"title": "Use RSA-0496 by default", "number": 4904, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4904", "body": "Note: must run migration before this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4904#pullrequestreview-1307969713", "body": "out of principle, I approve"}
{"title": "Adds user secret to admin web so that it can run user secret migration and profile refresh", "number": 4905, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4905"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4905#pullrequestreview-1304614216", "body": ""}
{"title": "Adding support for a cold region", "number": 4906, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4906", "body": "Created new configuration files for dev and prod us-east-2\nBootstraped new regions for CDK deployments\nModified our core environment deployment function to avoid deploying expensive compoents to cold-site\nCreated replica S3 buckets in cold sites\nAdded functionality to create replication rules for customer asset buckets\nUpdated a couple of outdated functions that CDK was complaining about\nEnabled bucket versioning on customer asset buckets (Required for SOC2)\n\nNote: I have applied the user data retention lifecycle rule only to us-east-2 (replica buckets) to make sure they work as expected before applying them to our main buckets."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4906#pullrequestreview-1308091456", "body": ""}
{"title": "[RFC] Add SourcePointModel.commitShortSha", "number": 4907, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4907", "body": "Bitbucket only provides the short sha for comments. I'm thinking we store this as a separate property on SourcePointModel and set commitHash to a sentinel value, then have the API return the short sha for SourcePoint.commitHash (API model) whenever this sentinel value is detected.\nAlternatively, we could rename this property to commitHashString and store either the full or short sha in this property depending on what we're given, and then deprecate commitHash.\nWe would need to update the sourcemark engine to handle short shas, but only before we onboard a bitbucket customer."}
{"comment": {"body": "Closing for https://github.com/NextChapterSoftware/unblocked/pull/4908", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4907#issuecomment-1438930411"}}
{"title": "Model short hashes in DB", "number": 4908, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4908", "body": "Limitation is that odd length hashes cannot be faithfully persisted as binary,\nso we coerce the odd length hash to even length by dropping one character first."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4908#pullrequestreview-1307964631", "body": ""}
{"title": "Minor decryption script", "number": 4909, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4909"}
{"title": "All DAO models extend EntityExtensions", "number": 491, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/491", "body": "cleanup"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/491#pullrequestreview-902105134", "body": ""}
{"title": "Rename to ScmPRComment", "number": 4910, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4910"}
{"title": "Add default security headers to CloudFront", "number": 4911, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911", "body": "This will add the following to all responses:\nX-Content-Type-Options: nosniff\nStrict-Transport-Security: max-age=15724800; includeSubdomains\nX-Frame-Options: SAMEORIGIN\nReferrer-Policy: strict-origin\nThis also REMOVES the Server header from all responses.\nIn a follow-up PR, we're going to introduce:\nPermissions-Policy: fullscreen=(self)\nContent-Security-Policy: fml"}
{"comment": {"body": "> why not do this from application code, see `CustomDefaultHeaders`?\r\n\r\nI has to apply to static assets from S3, including index.html. We will allow the API to define its own content security and permissions policies, or alternatively we can turn off override completely and duplicate a bunch of this in custom headers as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#issuecomment-1439131948"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#pullrequestreview-1308208089", "body": "~why not do this from application code, see CustomDefaultHeaders?~\nnvm, I'm an idiot"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#pullrequestreview-1308212098", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#pullrequestreview-1308214368", "body": ""}
{"comment": {"body": "Do we need to add this for the other paths ? Like customer assets or dashboard etc ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#discussion_r1113603473"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#pullrequestreview-1308215570", "body": ""}
{"comment": {"body": "Nah let's not complicate it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#discussion_r1113604543"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#pullrequestreview-1308216091", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#pullrequestreview-1308216351", "body": ""}
{"comment": {"body": "I applied this to default so I thought this would carry across all paths?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#discussion_r1113605091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#pullrequestreview-1308217206", "body": ""}
{"comment": {"body": "Default here means / but since we add a separate behaviour for each sub path they might not inherit it. Let's get this deployed then we can test it. \n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#discussion_r1113605690"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#pullrequestreview-1308219434", "body": ""}
{"comment": {"body": "That would be very upsetting \ud83d\ude2d", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#discussion_r1113607332"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#pullrequestreview-1308222203", "body": ""}
{"comment": {"body": "Welcome to my world \n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4911#discussion_r1113609446"}}
{"title": "Temp disable zally", "number": 4912, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4912", "body": "Zally lint has an older version of SwaggerParser which breaks parsing relative ref ()\nWill disable until Zally updates versions.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4912#pullrequestreview-1308218555", "body": ""}
{"title": "VSCode: remove hop to display sidebar UI", "number": 4913, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4913", "body": "We used to briefly display the sidebar UI, and hop back to the explorer UI, so that the sidebar badge would display.  But we removed sidebar badges, so this is not needed anymore."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4913#pullrequestreview-1308223365", "body": ""}
{"comment": {"body": "@jeffrey-ng  / @kaych I don't know why we were doing this before, do we still need this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4913#discussion_r1113610271"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4913#pullrequestreview-1308240646", "body": ""}
{"comment": {"body": "I think we still need this.\r\n\r\nThis is necessary when a new VSCode instance is launched by the ProjectSelector step using `commands.executeCommand('vscode.openFolder', uri, { forceNewWindow: false });`\r\n\r\nWhen that occurs, we want an unblocked sidebar to be open for continuity. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4913#discussion_r1113622383"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4913#pullrequestreview-1308240878", "body": ""}
{"title": "Re-encrypt user SCM secrets", "number": 4914, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4914", "body": "Changes\n\nDB model changes\nadded binary rawAccessToken\nadmin web\nshow encrypted access tokens\nmigration to re-encrypt secrets for a person's identities\nmigration ro Re-encrypt secrets for all identities\n\nMigration plan\n\n[x] run in LOCAL\n[x] run in DEV\n[x] run in PROD on people in our team\n[x] run in PROD for all people\n\nRollback plan\n\nthe legacy accessToken field values are untouched, so rollback plan is simply revert this change"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4914#pullrequestreview-1308244792", "body": ""}
{"comment": {"body": "@pwerry fyi introduces new low-level utility module called `lib-crypto` that only depends on bouncycastle. This object is a placeholder for AES, similar to `RSACryptoSystem`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4914#discussion_r1113625277"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4914#pullrequestreview-1308257045", "body": "This makes me happy."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4914#pullrequestreview-1308299281", "body": ""}
{"comment": {"body": "consider renaming. `new` will get old pretty fast \ud83d\ude43", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4914#discussion_r1113665218"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4914#pullrequestreview-1308299868", "body": ""}
{"comment": {"body": "yeah, will be removed in next commit -- after the migration has run\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4914#discussion_r1113665633"}}
{"title": "Apply security policy to all paths", "number": 4915, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4915", "body": "The linter picked up a bunch of non-linted changes. \nMy changes are all in cloudfront-stack.ts"}
{"comment": {"body": "Be careful as we need to validate caching behaviour with assets. Shouldn't affect it I suppose.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4915#issuecomment-1439223117"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4915#pullrequestreview-1308263238", "body": ""}
{"comment": {"body": "Here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4915#discussion_r1113638610"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4915#pullrequestreview-1308263315", "body": ""}
{"comment": {"body": "here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4915#discussion_r1113638707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4915#pullrequestreview-1308263469", "body": ""}
{"comment": {"body": "here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4915#discussion_r1113638865"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4915#pullrequestreview-1308283867", "body": ""}
{"title": "Adds permissions-policy to security headers", "number": 4916, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4916", "body": "TIL: permissions-policy is a deny list, which is possibly the biggest design error in all of web security history: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4916#pullrequestreview-1308366124", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4916#pullrequestreview-1308366514", "body": ""}
{"comment": {"body": "What's the concern with fullscreen?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4916#discussion_r1113715105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4916#pullrequestreview-1308381265", "body": ""}
{"comment": {"body": "No concern. This is box-ticking and frankly this header is idiotic", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4916#discussion_r1113726069"}}
{"title": "Prevent VSCode sidebar from wedging on startup", "number": 4917, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4917", "body": "Fixes \nFixes the issue a customer was running into here: \nThe problem seems to be the following:  VSCode publishes a command for every view labelled MySpecialView.focus, which causes that view to be opened and focused.  This does not work correctly when the extension is starting up, at least not with our extension insight view.\nWe had previously done this by running a second (manually-defined) command first, which runs that predefined command.  That seems to work fine, for some reason.  I refactored/simplified this code and removed the manual command (because it did not appear to do anything useful before)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4917#pullrequestreview-**********", "body": ""}
{"title": "Add powerml with summaries", "number": 4918, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4918"}
{"title": "Setup Generic Auth Classes", "number": 4919, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4919", "body": "Setup generic auth classes which should allow us to trigger auth operations (refreshAuth) in other stores without passing around client specific abstractions (e.g. TokenProvider)\nThis was specifically done so that we can refactor RepoStore from VSCode into shared/ide. RepoStore in VSCode currently calls the refreshAuth implementation within VSCode which handles injecting the token provider."}
{"comment": {"body": "Closing in favour of a simpler solution: https://github.com/NextChapterSoftware/unblocked/pull/4921", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4919#issuecomment-**********"}}
{"title": "Update Person model to include memberships", "number": 492, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492", "body": "Instead of an identity, include team memberships instead of identity for a person\nCan easily map from team memberships -> Identity if necessary\nOn the clients, we typically reference individuals based on their TeamMembership ID.\nFor example, if we wanted to associate a message with the current user, we would need a teamMemberID which a person does not contain.\nAlso added reference to the team a teamMember belongs to."}
{"comment": {"body": "This is largely to make looking up and mapping the current person within the ThreadParticipant list easier, correct?  I'm fine with this, my only question is whether we should remove Identity completely at this point, and move its properties onto TeamMember, since Identity then only exists as a property on TeamMember.  Not a big deal either way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#issuecomment-1061020939"}}
{"comment": {"body": "> This is largely to make looking up and mapping the current person within the ThreadParticipant list easier, correct? I'm fine with this, my only question is whether we should remove Identity completely at this point, and move its properties onto TeamMember, since Identity then only exists as a property on TeamMember. Not a big deal either way.\r\n\r\nYup this makes sense:\r\n- `Person` model contains `List<TeamMember>` instead of `List<Identity>`\r\n- Remove `Identity.id` property from API, since it cannot be referenced as an API resource\r\n- Use `TeamMember` IDs everywhere on the client.\r\n\r\n\r\nJust one question for @pwerry : since we include the identity ID in the token claim, does it matter that we are removing the Identity ID from the API responses?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#issuecomment-1061049344"}}
{"comment": {"body": "> Just one question for @pwerry : since we include the identity ID in the token claim, does it matter that we are removing the Identity ID from the API responses?\r\n\r\nI don't think so. The identity is pulled from the authorization header and isn't used anywhere in the API. Summary - this is a good API change that we should apply everywhere. \r\n\r\nOn whether we should also consider (again) whether we should roll `Identity` into `TeamMember`: let's think through the implications of multiple identities per team member down the road (email, SSO, etc). Maybe this isn't something we should worry about right now and we should just go ahead and fold `Identity` into `TeamMember`. I don't think what we have is complicated, but in many cases it means an unnecessary DB round-trip to load the `TeamMember` from the `Identity` to do access checks\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#issuecomment-1061061392"}}
{"comment": {"body": "> fold Identity into TeamMember\r\n\r\nNo, don't do this part. I see no benefit. The structure is a way of organizing the data. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#issuecomment-1061070981"}}
{"comment": {"body": "@pwerry How does this affect GodMode? Should it take a teamMember ID instead?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#issuecomment-1061073915"}}
{"comment": {"body": "> @pwerry How does this affect GodMode? Should it take a teamMember ID instead?\r\n\r\nAh! Godmode does need the identity ID. And this means we actually _do_ need to continue to expose the `Identity.id` in the API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#issuecomment-1061083957"}}
{"comment": {"body": "Should be ready for review. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#issuecomment-1061280716"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#pullrequestreview-902111540", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#pullrequestreview-902114591", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/492#pullrequestreview-903203952", "body": "Nice work.\nYour IDE settings are unnecessarily wrapping lines thoughout."}
{"title": "update powerml docker file", "number": 4920, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4920"}
{"title": "Remove TokenProvider input from RefreshAuth", "number": 4921, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4921", "body": "Keep a reference of TokenProvider in AuthStore.\nThis enables the usage of RefreshAuth within shared code without handling TokenProviders."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4921#pullrequestreview-**********", "body": ""}
{"title": "Add csp in report only mode to test dashboard", "number": 4922, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4922", "body": "This should do nothing except dump some stuff into the web console when there are CSP errors"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4922#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is saying the following:\r\n1. Allow all resources as long as they're using TLS\r\n2. With the exception of scripts, which are only allowed to load from origin and widget.intercom.io", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4922#discussion_r1113726858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4922#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Only report for now until we squash all the CSP issues", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4922#discussion_r1113727077"}}
{"title": "AES crypto system", "number": 4923, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4923"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4923#pullrequestreview-1308381451", "body": "@pwerry this is me asking chat-GPT to do it. close, but garbage."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4923#pullrequestreview-1308424749", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4923#pullrequestreview-1310190882", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4923#pullrequestreview-1310442995", "body": "Short and sweet"}
{"title": "Cleanup user token encryption", "number": 4924, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4924", "body": "Cleanup user token encryption\nFollow up from #4914.\nremove legacy accessToken"}
{"title": "Overshoot CSP tightening to get error reporting", "number": 4925, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4925", "body": "Still in report only mode so this is just for documentation purposes while we iterate towards a tight policy"}
{"title": "Whack-a-mole time", "number": 4926, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4926"}
{"title": "Apply CSP for realz", "number": 4927, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4927"}
{"title": "include subdomains in CSP", "number": 4928, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4928"}
{"title": "Add img blob and wss connect directives to CSP", "number": 4929, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4929"}
{"title": "Deploy web dashboard to prod", "number": 493, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/493", "body": "This change would dashboard to prod. We still need to figure out how to introduce testing but for now this should help with my work. \nBoth Dev and Prod Dashbaords are available at /dashboard. we currently limit access to them via WAF IP filters."}
{"comment": {"body": "Where is the dashboard build sourced from?  Right now we have to make a separate dashboard build for dev and prod...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/493#issuecomment-1061077069"}}
{"comment": {"body": "Right now we are building Dev target for both. `npm run build:dev`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/493#issuecomment-1061083558"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/493#pullrequestreview-903282481", "body": ""}
{"title": "Add CSP for realz", "number": 4930, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4930", "body": "This adds the CSP. Running in report only mode shows no errors for all dashboard pages using the following policy:\ndefault-src 'self'; img-src blob: data: https:; style-src 'unsafe-inline' https:; script-src 'self' *.intercom.io *.intercomcdn.com; connect-src 'self' *.intercom.io *.sentry.io wss://*.intercom.io; font-src *\nWill revert immediately if this breaks anything"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4930#pullrequestreview-1308518520", "body": ""}
{"title": "Remove restrictions for img and font srcs", "number": 4931, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4931"}
{"title": "powerml wordnet", "number": 4932, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4932"}
{"title": "img-src * apparently doesn't include blob: and data: schemes", "number": 4933, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4933"}
{"title": "Reduce topic summaries", "number": 4934, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4934"}
{"title": "Fix VSCode sidebar rendering", "number": 4935, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4935", "body": "The problem is that createValueStream makes an assumption that undefined items should never be sent by default.\nThis needs to be cleaned up -- I think the behaviour should be more explicit -- but changing createValueStream's default behaviour is risky as it's used everywhere and I don't think the contract in this area is super obvious to all code using it.  So this is a bit of a hacky way to fix this -- use null to imply \"unset\" and then map immediately."}
{"title": "Include topic summary on topics page", "number": 4936, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4936", "body": "We have the backend changes necessary for adding topic descriptions.\nWe now want to display it on the frontend.\nThis one addresses the topic description on the topic page.\nThe remaining work is to add a hover description in the topics list.\n"}
{"comment": {"body": "@rasharab https://github.com/NextChapterSoftware/unblocked/pull/4963", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4936#issuecomment-1440845800"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4936#pullrequestreview-1308536786", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4936#pullrequestreview-1308540432", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4936#pullrequestreview-1308542544", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4936#pullrequestreview-1308560072", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4936#pullrequestreview-1308597378", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4936#pullrequestreview-1309545271", "body": ""}
{"title": "[Just In Case] - Revert \"Add CSP for realz\"", "number": 4937, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4937", "body": "Reverts NextChapterSoftware/unblocked#4930"}
{"title": "Remove unsafe-inline from style-src (report only)", "number": 4938, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4938"}
{"title": "Remove report only policy checking removal of unsafe-inline (we have to keep it)", "number": 4939, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4939"}
{"title": "Introduce SourceMark data class and create SPs in thread creation", "number": 494, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/494"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/494#pullrequestreview-902189858", "body": ""}
{"comment": {"body": "So, we weren't actually creating a SP up till now when creating threads through the API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/494#discussion_r821058513"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/494#pullrequestreview-902234230", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/494#pullrequestreview-902252941", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/494#pullrequestreview-902264125", "body": ""}
{"title": "Drop IdentityModel.accessToken column", "number": 4940, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4940", "body": "No longer used. See #4914 and #4924.\nMigration plan:\n\n[x] local\n[x] dev\n[x] prod"}
{"title": "Automated RDS backups to S3 and DB alarms", "number": 4941, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4941", "body": "Added lambda function to create RDS snapshots and export them to S3\nAdded a replica bucket to cold site and configured bucket replications between hot and cold regions\nAdded DB CPU, IO and Free Disk alarms (required by drata)\nAdded an SNS topic to deliver alarms to environment admin email account All changes have been deployed to both dev and prod.\n\nI hate compliance work! I really do!"}
{"title": "Remove unused GitHubAppApi functions", "number": 4942, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4942"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4942#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4942#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4942#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4942#pullrequestreview-**********", "body": ""}
{"title": "Fix bug where search UI wouldn't start up", "number": 4943, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4943", "body": "I removed a single symbol when I was cleaning up the code and missed this. Oops."}
{"title": "Remove unused imports", "number": 4944, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4944"}
{"title": "Fix lint", "number": 4945, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4945"}
{"title": "V4User.searchPullRequestsWithComments returns a generic result", "number": 4946, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4946", "body": "Right now its returning a GitHub-specific GraphQL response, which makes it difficult to abstract. This PR changes it so that it only returns what the caller needs: a list of PR numbers, plus a cursor for the next page if there is one."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4946#pullrequestreview-1309955583", "body": ""}
{"title": "IDE RepoStore", "number": 4947, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4947", "body": "Moving VSCode RepoStore to shared/IDE."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4947#pullrequestreview-1309882170", "body": ""}
{"comment": {"body": "Needs to be set per client type.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4947#discussion_r1114760795"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4947#pullrequestreview-1310135817", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4947#pullrequestreview-1310141586", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4947#pullrequestreview-1310149102", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4947#pullrequestreview-1310194824", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4947#pullrequestreview-1310194967", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4947#pullrequestreview-1310221644", "body": ""}
{"title": "adjust the alarms to make them less noisy", "number": 4948, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4948"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4948#pullrequestreview-1309896852", "body": ""}
{"title": "Style validator was not working with private.yml", "number": 4949, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4949"}
{"title": "deploy agora s3 bucket and user to all envs", "number": 495, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/495", "body": "Added Agora S3 stack to our App environment so we get it deployed to prod as well. \nRenamed the bucket to include environment name. S3 bucket names are global and this was causing a conflict."}
{"comment": {"body": "I wonder if we can figure out a way to share this across the codebase so the api-service can load it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/495#issuecomment-1061167004"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/495#pullrequestreview-902288184", "body": ""}
{"title": "Move to filecollection for style task", "number": 4950, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4950"}
{"title": "make them less noisy", "number": 4951, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4951", "body": "Make these alarms less noisy"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4951#pullrequestreview-1309995058", "body": ""}
{"title": "V4Org.pullRequestReviewThreads returns a generic result", "number": 4952, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4952", "body": "Similar to https://github.com/NextChapterSoftware/unblocked/pull/4946, this function needs to return a generic response before we can abstract it for the work to support Bitbucket and GitLab."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4952#pullrequestreview-1310049931", "body": ""}
{"title": "chore(deps): update definitelytyped", "number": 4953, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4953", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/chrome (source) | ^0.0.213 -> ^0.0.217 |  |  |  |  |\n| @types/node (source) | 18.13.0 -> 18.14.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Immortal: This PR will be recreated if closed unmerged. Get config help if that's undesired.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Signing in with an SCM account provides access to the teams for that SCM account only", "number": 4954, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4954", "body": "We currently merge SCM accounts on the backend when the SCM account email matches. However,\nwe found that this leads to unexpected behaviour.\nConsider the scenario:\n- user creates Unblocked account using GitHub with \"<EMAIL>\"\n- user creates Unblocked account using Bitbucket with \"<EMAIL>\" (same email)\nOld behaviour:\n- user signs into Unblocked using GitHub, sees all Bitbucket and GitHub teams\n- user signs into Unblocked using Bitbucket, sees all Bitbucket and GitHub teams\nNew behaviour:\n- user signs into Unblocked using GitHub, sees only GitHub teams\n- user signs into Unblocked using Bitbucket, sees only Bitbucket teams\nIf we want to show a merged view of teams regardless of the SCM account they\nsign in with, then we need to adequately explain when this merge takes place\nand provide the ability to unmerge the SCM accounts."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4954#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4954#pullrequestreview-**********", "body": ""}
{"title": "chore(deps): update dependency nicklockwood/swiftformat to from: \"0.50.9\"", "number": 4955, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4955", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| nicklockwood/SwiftFormat | patch | from: \"0.50.8\" -> from: \"0.50.9\" |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnicklockwood/SwiftFormat\n\n### [`v0.50.9`]()\n\n[Compare Source]()\n\n-   Added Github actions log reporter (see `--reporter` option for details)\n-   Fixed bug where `redundantType` sometimes stripped in cases where it couldn't be inferred\n-   The `redundantType` rule now supports removing type in more cases where supported\n-   Made SwiftFormat for Xcode instructions dynamic according to OS version\n-   Fixed bug where a trailing comma could be left behind by `opaqueGenericParameters` rule\n-   Fixed bug where `wrapAttributes` rule sometimes wrapped inline attributes like `@MainActor`\n-   Improved support for `// swiftformat:options` comment directives\n-   Removed deprecated options from the example `.swiftformat` file\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency io.honeycomb:honeycomb-opentelemetry-sdk to v1.4.2", "number": 4956, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4956", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.honeycomb:honeycomb-opentelemetry-sdk | 1.4.1 -> 1.4.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nhoneycombio/honeycomb-opentelemetry-java\n\n### [`v1.4.2`]()\n\n##### Maintenance\n\n-   maint: add smoke tests for grpc/http exporter protocols for agent only ([#391]()) | [@JamieDanielson]()\n-   maint(deps): bump otel to 1.22.1 ([#393]()) | [@vreynolds]()\n-   maint(deps): bump junit-jupiter-engine from 5.9.1 to 5.9.2 ([#390]())\n-   maint(deps): bump io.grpc:grpc-netty-shaded from 1.50.2 to 1.53.0 ([#395]())\n-   maint(deps): bump junit-bom from 5.9.1 to 5.9.2 ([#386]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency io.lettuce:lettuce-core to v6.2.3.release", "number": 4957, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4957", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.lettuce:lettuce-core | 6.2.2.RELEASE -> 6.2.3.RELEASE |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nlettuce-io/lettuce-core\n\n### [`v6.2.3.RELEASE`]()\n\n[Compare Source]()\n\n##### :green_book: Links\n\n-   Reference documentation: \n-   Javadoc: \n\n##### :lady_beetle: Bug Fixes\n\n-   Fallback to RESP2 hides potential authentication configuration problems [#2313]()\n-   Accept slots as String using `CLUSTER SHARDS` [#2325]()\n-   Handle unknown endpoints in MOVED response [#2290]()\n-   `RedisURI.applySsl()` does not retain `SslVerifyMode` [#2328]()\n-   Apply `SslVerifyMode` in `RedisURI.applySsl()` [#2329]()\n\n##### :bulb: Other\n\n-   Avoid using port 7443 in Lettuce tests [#2326]()\n-   Update netty.version to 4.1.89.Final [#2311]()\n-   Fix duplicate word occurrences [#2307]()\n\n##### :heart: Contributors\n\nWe'd like to thank all the contributors who worked on this release!\n\n-   [@Emibergo02]()\n-   [@jacob-pro]()\n-   [@liyuntao]()\n-   [@rbowen]()\n-   [@rstosick]()\n-   [@vijay-kota]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "TestJava19", "number": 4958, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4958", "body": "Java 19\nTest ci"}
{"title": "fix(deps): update dependency org.jsoup:jsoup to v1.15.4", "number": 4959, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4959", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.jsoup:jsoup (source) | 1.15.3 -> 1.15.4 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Create Pull Request form", "number": 496, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496", "body": "Build command and form to create pull request (with mocked data)\n\n\n\n\nIntegrate drag and drop rows for the notes\n\nAdd ExpandableSection component for use/re-use\nAdd BranchTag component for use/re-use\nNOTE: API to create pull requests needs to be added (https://github.com/NextChapterSoftware/unblocked/pull/500)\nNOTE: To create the PR, we'll need to first update the note messages with the latest changes, and then post the data to the pull request as comments/issues \nThere's missing sourcemark data to populate both the code block in each note thread as well as the sourcepoint reference (TODOs added in the code)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-905149550", "body": ""}
{"comment": {"body": "What's this about?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r823203066"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-905151194", "body": ""}
{"comment": {"body": "react dnd has an internal dependency on the process lib and it's unresolved in the vscode environment (I didnt notice this in the first PR because I wasn't using react dnd in vscode) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r823206139"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-905177107", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-905179767", "body": ""}
{"comment": {"body": "We might want to combine this line and the one above, so we `find` the first block that is a paragraph *and* has content.  So if someone enters a bunch of empty lines or something at the top, we still end up with a usable title.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r823234548"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-907681251", "body": ""}
{"comment": {"body": "This should maybe be a max-width?  We'll want it to be able to scale down somewhat?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r825039077"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-907686248", "body": ""}
{"comment": {"body": "This will be tricky when it is eventually implemented, as the webview itself shouldn't be fetching messages, the outer extension command code should be the one responsible for that...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r825043947"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-907725107", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-907835392", "body": ""}
{"comment": {"body": "This might be easier to do with something like this?\r\n\r\n```\r\nnoteComments.filter(noteComment => noteComment.isSelected).map(noteComment => [noteComment.node, noteComment.message]))\r\n```\r\n?\r\n\r\nWith this algorithm as-is I think there is a chance that if noteComment.message is falsy, the arrays will not be pairwise any more.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r825151158"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-907841947", "body": ""}
{"comment": {"body": "nit: Is there a reason why we're hoisting the expanded state to the parent?  We could probably just have the state for each row stored in this component?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r825155974"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-907846574", "body": "Sorry for taking forever to review this.  I added a bunch of minor comments which you can take or leave as you'd like, but this looks really good -- it's a complicated UI and I think the way it's done is great "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-909274171", "body": ""}
{"comment": {"body": "it is a max width on implementation \r\n![image](https://user-images.githubusercontent.com/13431372/158243601-afc0457e-b7c6-4c81-b9db-09419c230795.png)\r\n\r\ni just it's just a matter of if we want to make that a mixin to constrain it or not?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r826291553"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-909384129", "body": ""}
{"comment": {"body": "I was running into issues having the open/close state persist correctly with the corresponding rows after dragging. I think the problem has something to do with the Drag and Drop component and how it caches the index of each component but I wasn't able to track what was causing the exact bug. I'll make a note to try to dig into this further when we revisit the pull request feature?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r826368751"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-909388914", "body": ""}
{"comment": {"body": "I wonder how much if would matter if it's not pairwise at this point... i.e. if the message is falsy, then we just wouldn't update the message before converting it from a note into a prComment. And the only way it would be falsy would be if we failed to fetch the message for the note thread (so there'd be nothing for the user to update anyways) and that's probably a separate error we should handle", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r826373746"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-909390245", "body": ""}
{"comment": {"body": "Oh interesting.  I bet what's happening is that after the DnD, React believes each of the components is a new component instance, so it generates new state for each one.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r826374771"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#pullrequestreview-909397712", "body": ""}
{"comment": {"body": "Updated the line above. This second line is really just for typescript's sake (i.e. the `parsingBlock` is still typed to have an optional content and unknown content case despite the .find())\r\n\r\nI think the helper will be rewritten into a switch onto the $case to support more types (i.e. code blocks, links) so I'm not too concerned with this bit right now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/496#discussion_r826380055"}}
{"title": "fix(deps): update opentelemetryversion to v1.23.1", "number": 4960, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4960", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.opentelemetry:opentelemetry-sdk | 1.23.0 -> 1.23.1 |  |  |  |  |\n| io.opentelemetry:opentelemetry-extension-kotlin | 1.23.0 -> 1.23.1 |  |  |  |  |\n| io.opentelemetry:opentelemetry-exporter-otlp | 1.23.0 -> 1.23.1 |  |  |  |  |\n| io.opentelemetry:opentelemetry-exporter-logging | 1.23.0 -> 1.23.1 |  |  |  |  |\n| io.opentelemetry:opentelemetry-sdk-testing | 1.23.0 -> 1.23.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nopen-telemetry/opentelemetry-java\n\n### [`v1.23.1`](https://togithub.com/open-telemetry/opentelemetry-java/blob/HEAD/CHANGELOG.md#Version-1231-2023-02-15)\n\n-   Fix bug that broke `AutoConfiguredOpenTelemetrySdk`'s shutdown hook.\n    ([#5221](https://togithub.com/open-telemetry/opentelemetry-java/pull/5221))\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Rename TopicSource.Curated to TopicSource.Approved", "number": 4961, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4961", "body": "We will be making some changes to make topics with TopicSource.Curated source as the approved topics for a customer. This will let us pre-populate the list of approved topics for a customer during onboarding.\nTo better reflect this change, lets rename the TopicSource.Curated enum to TopicSource.Approved."}
{"title": "JB Token Transport layer", "number": 4962, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962", "body": "Refactor Agent into its own service.\nAdd Token Service to receive and send tokens to agent."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310304086", "body": ""}
{"comment": {"body": "Temporary for testing purposes. Need to move this to central \"Agent initialization\" which isn't coupled with webview.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1114997073"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310311962", "body": ""}
{"comment": {"body": "That's what `ProjectService` is for, it can live in there and run as part of the bootup promise.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115000538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310315679", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310320546", "body": ""}
{"comment": {"body": "This is bidirectional in that tokens are sent to the agent on startup, and are received from the agent whenever the token updates?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115004343"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310326634", "body": ""}
{"comment": {"body": "Ah never mind I see you refactored this out, all good", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115006985"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310327268", "body": ""}
{"comment": {"body": "When you have functions that run things asynchronously, it's generally good practice to include `async` in the name. \r\n\r\nFor example in this case you would call this `runWithAgentAsync`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115007275"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310331568", "body": ""}
{"comment": {"body": "I would have figured `launch` is a good way of indicating that, since what we're doing is effectively a `launch` on a particular coroutine with particular data?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115009281"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310332262", "body": ""}
{"comment": {"body": "ie, `launch` has a particular set of understood behaviour which we are following here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115009575"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310332612", "body": ""}
{"comment": {"body": "Unused?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115009727"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310336026", "body": ""}
{"comment": {"body": "Nice pattern here! Only thing to note is that `agentAPI` is going to boot on instance initialization. If you desire `lazy` behaviour instead that is achievable with a small modification.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115011285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310341362", "body": ""}
{"comment": {"body": "I wrote that originally -- it's probably fine either way, it could be lazy but pretty much anyone referencing instances of this class are going to want the agent API immediately", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115013625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310341981", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310342430", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310342743", "body": ""}
{"comment": {"body": "I added this as a reference -- we'll need to use the API without launching a separate coroutine and I wanted to have this here for later.  We can remove it or not, doesn't matter", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115014193"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310344223", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310347924", "body": ""}
{"comment": {"body": "Yes actually nvm that's better because you're not getting a `Deferred` out the other end here, just a `Job`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115016455"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310351979", "body": ""}
{"comment": {"body": "The more I play with this, the trickier it gets.\r\n\r\nI had originally specced this out as two operations.\r\n`GetTokens`\r\n`UpdateTokens`\r\n\r\nThe agent would get tokens on demand and update them as necessary (since the Agent would be making the auth requests)\r\n\r\nI was doing this with the assumption that Agent as the \"client\". This would mean requests would flow from the agent -> IntelliJ which would return the tokens in a response.\r\n\r\nThis is *not* how things are architected. Therefore, I moved towards a single bidirectional stream. There are still some technical problems to figure out with this though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115020178"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310354415", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310355951", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310358649", "body": ""}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#discussion_r1115026421"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4962#pullrequestreview-1310359819", "body": ""}
{"title": "Add topic descriptions to dashboard", "number": 4963, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4963", "body": "\n\nalso add button to clear filters in the mobile dashboard:\n"}
{"comment": {"body": "Can we try the secondary text colour for the description?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4963#issuecomment-1440866967"}}
{"comment": {"body": "> Can we try the secondary text colour for the description?\r\n\r\n<img width=\"745\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/220772914-0b694497-1a77-4dd6-a56b-537de95d7308.png\">\r\n@benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4963#issuecomment-1440888292"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4963#pullrequestreview-1310310696", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4963#pullrequestreview-1310365822", "body": ""}
{"title": "chore(deps): update aws-cdk monorepo to v2.66.0", "number": 4964, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4964", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| aws-cdk | 2.64.0 -> 2.66.0 |  |  |  |  |\n| aws-cdk-lib | 2.64.0 -> 2.66.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-cdk\n\n### [`v2.66.0`]()\n\n[Compare Source]()\n\n##### Features\n\n-   **cloudwatch:** parse all metrics statistics and support long format ([#23095]()) ([853e3d6]()), closes [#23074]() [40aws-cdk/aws-cloudwatch/lib/metric.ts#L295-L296]()\n-   **core:** Size.bytes() ([#24136]()) ([9b2a45a]()), closes [#24106]()\n-   **efs:** support file system policy ([#24196]()) ([5e0f44b]()), closes [#24042]()\n-   **logs:** Add support for multiple parse and filter statements in QueryString ([#24022]()) ([75eb933]())\n-   **stepfunctions:** removal policy for state machines ([#24105]()) ([5f33a26]())\n\n##### Bug Fixes\n\n-   **apigateway:** rest api deployment does not depend on authorizers ([#23215]()) ([12e13c1]())\n-   **cognito:** changing `installLatestAwsSdk` breaks Client Secret reference ([#23798]()) ([844d407]()), closes [#23796]()\n-   **ecs:** validate ecs healthcheck ([#24197]()) ([89802a9]())\n-   **eks:** nested OCI repository names for private ECR helmchart deployments are not properly handled ([#23378]()) ([72f2a95]())\n-   **lambda:** RuntimeManagementMode.FUNCTION_UPDATE has wrong value ([#24252]()) ([fdb0cf1]())\n\n***\n\n#### Alpha modules (2.66.0-alpha.0)\n\n##### Features\n\n-   **apigatewayv2:** allow websockets routes to return response to client ([#22984]()) ([f8fe1d2]())\n-   **lambda-python:** add optional poetry bundling exclusion list parameter ([#23670]()) ([53beeae]()), closes [#22585]() [#22585]()\n-   **redshift:** optionally reboot Clusters to apply parameter changes  ([#22063]()) ([f61d950]()), closes [#22009]() [#22055]() [#22059]()\n\n##### Bug Fixes\n\n-   **servicecatalogappregistry:** Allow user to control stack id via stack name for Application stack ([#24171]()) ([0c7c7e4]()), closes [#24160]()\n\n### [`v2.65.0`]()\n\n[Compare Source]()\n\n##### Features\n\n-   **autoscaling:** L2 construct for enabling capacity rebalance of autoscaling ([#24025]()) ([d2c63f5]()), closes [#22625]()\n-   **chatbot:** support guardrail policies ([#24114]()) ([4c72a7d]()), closes [#20788]()\n-   **core:** Allow passing Docker build secrets ([#23778]()) ([74512fa]()), closes [#14910]() [#14395]()\n-   **elbv2:** add metrics to INetworkTargetGroup and IApplicationTargetGroup ([#23993]()) ([6a9e43f]()), closes [#23853]() [#10850]()\n-   **lambda:** add insights version ********* ([#23836]()) ([5272908]())\n\n##### Bug Fixes\n\n-   **bootstrap:** remove Security Hub finding S3.10 ([#24175]()) ([a1da757]()), closes [/docs.aws.amazon.com/securityhub/latest/userguide/securityhub-standards-fsbp-controls.html#fsbp-s3-10]()\n-   **codedeploy:** unable to remove alarms from deployment group ([#23308]()) ([eee005f]())\n-   **codepipeline:** x-env ECS deployment lacking support stack-dependency ([#24053]()) ([adfe4fa]()), closes [#24050]() [#24051]()\n-   **core:** messages are displayed multiple times per construct ([#24019]()) ([57770bb]()), closes [#9565]()\n-   **ec2:** enable set throughput param to CfnVolume ([#24118]()) ([32781f8]()), closes [#24107]() [#24107]()\n-   **elbv2:** healthcheck interval is overly restrictive ([#24157]()) ([4f83e02]()), closes [#24156]()\n-   **iam:** PrincipalWithConditions.addCondition fails with a new key ([#23782]()) ([8951d01]()), closes [#23781]()\n-   **iam:** SamlConsolePrincipal does not work in China [#22091]() ([#24034]()) ([2902043]())\n-   **pipelines:** SelfMutation CodeBuild project not accessible ([#24073]()) ([5942978]())\n-   **rds:** database proxies use ids as their resource names directly (under feature flag) ([#23703]()) ([03a0f79]()), closes [#18578]()\n-   **s3:** logging bucket blocks KMS_MANAGED encryption ([#23514]()) ([1e8926f]())\n\n***\n\n#### Alpha modules (2.65.0-alpha.0)\n\n##### Features\n\n-   **glue:** support Ray jobs ([#23822]()) ([8de50d6]())\n-   **redshift:** IAM roles can be attached to a cluster, post creation ([#23791]()) ([1a46808]()), closes [#22632]()\n-   **synthetics:** support runtime 3.9 ([#24101]()) ([9d23cad]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency com.expediagroup.graphql to v6.4.0", "number": 4965, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4965", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.expediagroup.graphql | 6.3.5 -> 6.4.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nExpediaGroup/graphql-kotlin\n\n### [`v6.4.0`]()\n\n#### Minor Changes\n\n-   feat: add support for federation v2.3 ([#1674]()) [@dariuszkuc]()\n-   feat: pass graphQLContext as fallback if deprecated context is null ([#1652]() [#1653]() ) [@samuelAndalon]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency org.openapi.generator to v6.4.0", "number": 4966, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4966", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.openapi.generator | 6.3.0 -> 6.4.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Add lambda function to enforce global log retention", "number": 4967, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4967", "body": "Added a new stack to create IAM role and Lambda function which scans all the log groups and sets a maximum retention of 90 days on each one. Special thanks to ChatGPT for writing the actual function!!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4967#pullrequestreview-1310372561", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4967#pullrequestreview-1310373283", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4967#pullrequestreview-1310383582", "body": ""}
{"comment": {"body": "We should consider using our lambda functor for this to standardize lambda instantiation.\r\n\r\n                new LambdaConstruct(this, bucketConfig.triggerLambda.functionName, {\r\n                    name: bucketConfig.triggerLambda.functionName,\r\n                    runtime: lambda.Runtime.PYTHON_3_7,\r\n                    lambdaPath: bucketConfig.triggerLambda.lambdaPath,\r\n                    handler: bucketConfig.triggerLambda.handler,\r\n                    environment: {\r\n                        ...bucketConfig.triggerLambda.environment,\r\n                    },\r\n                    policies: [\r\n                        new iam.PolicyStatement({\r\n                            resources: ['*'],\r\n                            actions: ['logs:CreateLogGroup', 'logs:CreateLogStream', 'logs:PutLogEvents'],\r\n                        }),\r\n                        new iam.PolicyStatement({\r\n                            resources: [bucket.s3Bucket.arnForObjects('*')],\r\n                            actions: ['s3:Get*', 's3:List*', 's3:Put*'],\r\n                        }),\r\n                        new iam.PolicyStatement({\r\n                            resources: [`arn:aws:kms:*:${props.buildConfig.awsEnvAccount.awsAccountID}:key/*`],\r\n                            actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],\r\n                        }),\r\n                    ],\r\n                    timeout: cdk.Duration.minutes(bucketConfig.triggerLambda.timeoutInMin),\r\n                });\r\n            }", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4967#discussion_r1115049232"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4967#pullrequestreview-**********", "body": "I'll do a cleanup pass later on this to use the construct. This is okay for now."}
{"title": "chore(deps): update node.js to v19.7.0", "number": 4968, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4968", "body": "This PR contains the following updates:\n| Package | Type | Update | Change |\n|---|---|---|---|\n| node | final | minor | 19.6.0-slim -> 19.7.0-slim |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnodejs/node\n\n### [`v19.7.0`](https://togithub.com/nodejs/node/releases/tag/v19.7.0): 2023-02-21, Version 19.7.0 (Current), @MylesBorins\n\n[Compare Source](https://togithub.com/nodejs/node/compare/v19.6.1...v19.7.0)\n\n##### Notable Changes\n\n-   \\[[`60a612607e`](https://togithub.com/nodejs/node/commit/60a612607e)] - **deps**: upgrade npm to 9.5.0 (npm team) [#46673](https://togithub.com/nodejs/node/pull/46673)\n-   \\[[`7d6c27eab1`](https://togithub.com/nodejs/node/commit/7d6c27eab1)] - **deps**: add ada as a dependency (Yagiz Nizipli) [#46410](https://togithub.com/nodejs/node/pull/46410)\n-   \\[[`a79a8bf85a`](https://togithub.com/nodejs/node/commit/a79a8bf85a)] - **doc**: add debadree25 to collaborators (Debadree Chatterjee) [#46716](https://togithub.com/nodejs/node/pull/46716)\n-   \\[[`0c2c322ee6`](https://togithub.com/nodejs/node/commit/0c2c322ee6)] - **doc**: add deokjinkim to collaborators (Deokjin Kim) [#46444](https://togithub.com/nodejs/node/pull/46444)\n-   \\[[`9b23309f53`](https://togithub.com/nodejs/node/commit/9b23309f53)] - **doc,lib,src,test**: rename --test-coverage (Colin Ihrig) [#46017](https://togithub.com/nodejs/node/pull/46017)\n-   \\[[`8590eb4830`](https://togithub.com/nodejs/node/commit/8590eb4830)] - **(SEMVER-MINOR)** **lib**: add aborted() utility function (Debadree Chatterjee) [#46494](https://togithub.com/nodejs/node/pull/46494)\n-   \\[[`164bfe82cc`](https://togithub.com/nodejs/node/commit/164bfe82cc)] - **(SEMVER-MINOR)** **src**: add initial support for single executable applications (Darshan Sen) [#45038](https://togithub.com/nodejs/node/pull/45038)\n-   \\[[`f3908411fd`](https://togithub.com/nodejs/node/commit/f3908411fd)] - **(SEMVER-MINOR)** **src**: allow optional Isolate termination in node::Stop() (Shelley Vohr) [#46583](https://togithub.com/nodejs/node/pull/46583)\n-   \\[[`c34bac2fed`](https://togithub.com/nodejs/node/commit/c34bac2fed)] - **(SEMVER-MINOR)** **src**: allow blobs in addition to `FILE*`s in embedder snapshot API (Anna Henningsen) [#46491](https://togithub.com/nodejs/node/pull/46491)\n-   \\[[`683a1f8f3e`](https://togithub.com/nodejs/node/commit/683a1f8f3e)] - **(SEMVER-MINOR)** **src**: allow snapshotting from the embedder API (Anna Henningsen) [#45888](https://togithub.com/nodejs/node/pull/45888)\n-   \\[[`658d2f4710`](https://togithub.com/nodejs/node/commit/658d2f4710)] - **(SEMVER-MINOR)** **src**: make build_snapshot a per-Isolate option, rather than a global one (Anna Henningsen) [#45888](https://togithub.com/nodejs/node/pull/45888)\n-   \\[[`6801d3753c`](https://togithub.com/nodejs/node/commit/6801d3753c)] - **(SEMVER-MINOR)** **src**: add snapshot support for embedder API (Anna Henningsen) [#45888](https://togithub.com/nodejs/node/pull/45888)\n-   \\[[`e77d538d32`](https://togithub.com/nodejs/node/commit/e77d538d32)] - **(SEMVER-MINOR)** **src**: allow embedder control of code generation policy (Shelley Vohr) [#46368](https://togithub.com/nodejs/node/pull/46368)\n-   \\[[`633d3f292d`](https://togithub.com/nodejs/node/commit/633d3f292d)] - **(SEMVER-MINOR)** **stream**: add abort signal for ReadableStream and WritableStream (Debadree Chatterjee) [#46273](https://togithub.com/nodejs/node/pull/46273)\n-   \\[[`6119289251`](https://togithub.com/nodejs/node/commit/6119289251)] - **test_runner**: add initial code coverage support (Colin Ihrig) [#46017](https://togithub.com/nodejs/node/pull/46017)\n-   \\[[`a51fe3c663`](https://togithub.com/nodejs/node/commit/a51fe3c663)] - **url**: replace url-parser with ada (Yagiz Nizipli) [#46410](https://togithub.com/nodejs/node/pull/46410)\n\n##### Commits\n\n-   \\[[`731a7ae9da`](https://togithub.com/nodejs/node/commit/731a7ae9da)] - **async_hooks**: add async local storage propagation benchmarks (Chengzhong Wu) [#46414](https://togithub.com/nodejs/node/pull/46414)\n-   \\[[`05ad792a07`](https://togithub.com/nodejs/node/commit/05ad792a07)] - **async_hooks**: remove experimental onPropagate option (James M Snell) [#46386](https://togithub.com/nodejs/node/pull/46386)\n-   \\[[`6b21170b10`](https://togithub.com/nodejs/node/commit/6b21170b10)] - **benchmark**: add trailing commas in `benchmark/path` (Antoine du Hamel) [#46628](https://togithub.com/nodejs/node/pull/46628)\n-   \\[[`4b89ec409f`](https://togithub.com/nodejs/node/commit/4b89ec409f)] - **benchmark**: add trailing commas in `benchmark/http` (Antoine du Hamel) [#46609](https://togithub.com/nodejs/node/pull/46609)\n-   \\[[`ff95eb7386`](https://togithub.com/nodejs/node/commit/ff95eb7386)] - **benchmark**: add trailing commas in `benchmark/crypto` (Antoine du Hamel) [#46553](https://togithub.com/nodejs/node/pull/46553)\n-   \\[[`638d9b8d4b`](https://togithub.com/nodejs/node/commit/638d9b8d4b)] - **benchmark**: add trailing commas in `benchmark/url` (Antoine du Hamel) [#46551](https://togithub.com/nodejs/node/pull/46551)\n-   \\[[`7524871a9b`](https://togithub.com/nodejs/node/commit/7524871a9b)] - **benchmark**: add trailing commas in `benchmark/http2` (Antoine du Hamel) [#46552](https://togithub.com/nodejs/node/pull/46552)\n-   \\[[`9d9b3f856f`](https://togithub.com/nodejs/node/commit/9d9b3f856f)] - **benchmark**: add trailing commas in `benchmark/process` (Antoine du Hamel) [#46481](https://togithub.com/nodejs/node/pull/46481)\n-   \\[[`6c69ad6d43`](https://togithub.com/nodejs/node/commit/6c69ad6d43)] - **benchmark**: add trailing commas in `benchmark/misc` (Antoine du Hamel) [#46474](https://togithub.com/nodejs/node/pull/46474)\n-   \\[[`7f8b292bee`](https://togithub.com/nodejs/node/commit/7f8b292bee)] - **benchmark**: add trailing commas in `benchmark/buffers` (Antoine du Hamel) [#46473](https://togithub.com/nodejs/node/pull/46473)\n-   \\[[`897e3c2782`](https://togithub.com/nodejs/node/commit/897e3c2782)] - **benchmark**: add trailing commas in `benchmark/module` (Antoine du Hamel) [#46461](https://togithub.com/nodejs/node/pull/46461)\n-   \\[[`7760d40c04`](https://togithub.com/nodejs/node/commit/7760d40c04)] - **benchmark**: add trailing commas in `benchmark/net` (Antoine du Hamel) [#46439](https://togithub.com/nodejs/node/pull/46439)\n-   \\[[`8b88d605ca`](https://togithub.com/nodejs/node/commit/8b88d605ca)] - **benchmark**: add trailing commas in `benchmark/util` (Antoine du Hamel) [#46438](https://togithub.com/nodejs/node/pull/46438)\n-   \\[[`2c8c9f978d`](https://togithub.com/nodejs/node/commit/2c8c9f978d)] - **benchmark**: add trailing commas in `benchmark/async_hooks` (Antoine du Hamel) [#46424](https://togithub.com/nodejs/node/pull/46424)\n-   \\[[`b364b9bd60`](https://togithub.com/nodejs/node/commit/b364b9bd60)] - **benchmark**: add trailing commas in `benchmark/fs` (Antoine du Hamel) [#46426](https://togithub.com/nodejs/node/pull/46426)\n-   \\[[`e15ddba7e7`](https://togithub.com/nodejs/node/commit/e15ddba7e7)] - **build**: add GitHub Action for coverage with --without-intl (Rich Trott) [#37954](https://togithub.com/nodejs/node/pull/37954)\n-   \\[[`c781a48097`](https://togithub.com/nodejs/node/commit/c781a48097)] - **build**: do not disable inspector when intl is disabled (Rich Trott) [#37954](https://togithub.com/nodejs/node/pull/37954)\n-   \\[[`b4deb2fcd5`](https://togithub.com/nodejs/node/commit/b4deb2fcd5)] - **crypto**: don't assume FIPS is disabled by default (Michael Dawson) [#46532](https://togithub.com/nodejs/node/pull/46532)\n-   \\[[`60a612607e`](https://togithub.com/nodejs/node/commit/60a612607e)] - **deps**: upgrade npm to 9.5.0 (npm team) [#46673](https://togithub.com/nodejs/node/pull/46673)\n-   \\[[`6c997035fc`](https://togithub.com/nodejs/node/commit/6c997035fc)] - **deps**: update corepack to 0.16.0 (Node.js GitHub Bot) [#46710](https://togithub.com/nodejs/node/pull/46710)\n-   \\[[`2ed3875eee`](https://togithub.com/nodejs/node/commit/2ed3875eee)] - **deps**: update undici to 5.20.0 (Node.js GitHub Bot) [#46711](https://togithub.com/nodejs/node/pull/46711)\n-   \\[[`20cb13bf7f`](https://togithub.com/nodejs/node/commit/20cb13bf7f)] - **deps**: update ada to v1.0.1 (Yagiz Nizipli) [#46550](https://togithub.com/nodejs/node/pull/46550)\n-   \\[[`c0983cfc06`](https://togithub.com/nodejs/node/commit/c0983cfc06)] - **deps**: copy `postject-api.h` and `LICENSE` to the `deps` folder (Darshan Sen) [#46582](https://togithub.com/nodejs/node/pull/46582)\n-   \\[[`7d6c27eab1`](https://togithub.com/nodejs/node/commit/7d6c27eab1)] - **deps**: add ada as a dependency (Yagiz Nizipli) [#46410](https://togithub.com/nodejs/node/pull/46410)\n-   \\[[`7e7e2d037b`](https://togithub.com/nodejs/node/commit/7e7e2d037b)] - **deps**: update c-ares to 1.19.0 (Michal Zasso) [#46415](https://togithub.com/nodejs/node/pull/46415)\n-   \\[[`a79a8bf85a`](https://togithub.com/nodejs/node/commit/a79a8bf85a)] - **doc**: add debadree25 to collaborators (Debadree Chatterjee) [#46716](https://togithub.com/nodejs/node/pull/46716)\n-   \\[[`6a8b04d709`](https://togithub.com/nodejs/node/commit/6a8b04d709)] - **doc**: move bcoe to emeriti (Benjamin Coe) [#46703](https://togithub.com/nodejs/node/pull/46703)\n-   \\[[`a0a6ee0f54`](https://togithub.com/nodejs/node/commit/a0a6ee0f54)] - **doc**: add response.strictContentLength to documentation (Marco Ippolito) [#46627](https://togithub.com/nodejs/node/pull/46627)\n-   \\[[`ffdd64dce3`](https://togithub.com/nodejs/node/commit/ffdd64dce3)] - **doc**: remove unused functions from example of `streamConsumers.text` (Deokjin Kim) [#46581](https://togithub.com/nodejs/node/pull/46581)\n-   \\[[`c771d66864`](https://togithub.com/nodejs/node/commit/c771d66864)] - **doc**: fix test runner examples (Richie McColl) [#46565](https://togithub.com/nodejs/node/pull/46565)\n-   \\[[`375bb22df9`](https://togithub.com/nodejs/node/commit/375bb22df9)] - **doc**: update test concurrency description / default values (richiemccoll) [#46457](https://togithub.com/nodejs/node/pull/46457)\n-   \\[[`a7beac04ba`](https://togithub.com/nodejs/node/commit/a7beac04ba)] - **doc**: enrich test command with executable (Tony Gorez) [#44347](https://togithub.com/nodejs/node/pull/44347)\n-   \\[[`aef57cd290`](https://togithub.com/nodejs/node/commit/aef57cd290)] - **doc**: fix wrong location of `requestTimeout`'s default value (Deokjin Kim) [#46423](https://togithub.com/nodejs/node/pull/46423)\n-   \\[[`0c2c322ee6`](https://togithub.com/nodejs/node/commit/0c2c322ee6)] - **doc**: add deokjinkim to collaborators (Deokjin Kim) [#46444](https://togithub.com/nodejs/node/pull/46444)\n-   \\[[`31d3e3c486`](https://togithub.com/nodejs/node/commit/31d3e3c486)] - **doc**: fix -C flag usage ( Kevin Deng) [#46388](https://togithub.com/nodejs/node/pull/46388)\n-   \\[[`905a6756a3`](https://togithub.com/nodejs/node/commit/905a6756a3)] - **doc**: add note about major release rotation (Rafael Gonzaga) [#46436](https://togithub.com/nodejs/node/pull/46436)\n-   \\[[`33a98c42fa`](https://togithub.com/nodejs/node/commit/33a98c42fa)] - **doc**: update threat model based on discussions (Michael Dawson) [#46373](https://togithub.com/nodejs/node/pull/46373)\n-   \\[[`9b23309f53`](https://togithub.com/nodejs/node/commit/9b23309f53)] - **doc,lib,src,test**: rename --test-coverage (Colin Ihrig) [#46017](https://togithub.com/nodejs/node/pull/46017)\n-   \\[[`f192b83800`](https://togithub.com/nodejs/node/commit/f192b83800)] - **esm**: misc test refactors (Geoffrey Booth) [#46631](https://togithub.com/nodejs/node/pull/46631)\n-   \\[[`7f2cdd36cf`](https://togithub.com/nodejs/node/commit/7f2cdd36cf)] - **http**: add note about clientError event (Paolo Insogna) [#46584](https://togithub.com/nodejs/node/pull/46584)\n-   \\[[`d8c527f24f`](https://togithub.com/nodejs/node/commit/d8c527f24f)] - **http**: use v8::Array::New() with a prebuilt vector (Joyee Cheung) [#46447](https://togithub.com/nodejs/node/pull/46447)\n-   \\[[`fa600fe003`](https://togithub.com/nodejs/node/commit/fa600fe003)] - **lib**: add trailing commas in `internal/process` (Antoine du Hamel) [#46687](https://togithub.com/nodejs/node/pull/46687)\n-   \\[[`4aebee63f0`](https://togithub.com/nodejs/node/commit/4aebee63f0)] - **lib**: do not crash using workers with disabled shared array buffers (Ruben Bridgewater) [#41023](https://togithub.com/nodejs/node/pull/41023)\n-   \\[[`a740908588`](https://togithub.com/nodejs/node/commit/a740908588)] - **lib**: delete module findPath unused params (sinkhaha) [#45371](https://togithub.com/nodejs/node/pull/45371)\n-   \\[[`8b46c763d9`](https://togithub.com/nodejs/node/commit/8b46c763d9)] - **lib**: enforce use of trailing commas in more files (Antoine du Hamel) [#46655](https://togithub.com/nodejs/node/pull/46655)\n-   \\[[`aae0020e27`](https://togithub.com/nodejs/node/commit/aae0020e27)] - **lib**: enforce use of trailing commas for functions (Antoine du Hamel) [#46629](https://togithub.com/nodejs/node/pull/46629)\n-   \\[[`da9ebaf138`](https://togithub.com/nodejs/node/commit/da9ebaf138)] - **lib**: predeclare Event.isTrusted prop descriptor (Santiago Gimeno) [#46527](https://togithub.com/nodejs/node/pull/46527)\n-   \\[[`35570e970e`](https://togithub.com/nodejs/node/commit/35570e970e)] - **lib**: tighten `AbortSignal.prototype.throwIfAborted` implementation (Antoine du Hamel) [#46521](https://togithub.com/nodejs/node/pull/46521)\n-   \\[[`8590eb4830`](https://togithub.com/nodejs/node/commit/8590eb4830)] - **(SEMVER-MINOR)** **lib**: add aborted() utility function (Debadree Chatterjee) [#46494](https://togithub.com/nodejs/node/pull/46494)\n-   \\[[`5d1a729f76`](https://togithub.com/nodejs/node/commit/5d1a729f76)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46624](https://togithub.com/nodejs/node/pull/46624)\n-   \\[[`cb9b9ad879`](https://togithub.com/nodejs/node/commit/cb9b9ad879)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#46513](https://togithub.com/nodejs/node/pull/46513)\n-   \\[[`17b82c85d9`](https://togithub.com/nodejs/node/commit/17b82c85d9)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46504](https://togithub.com/nodejs/node/pull/46504)\n-   \\[[`bb14a2b098`](https://togithub.com/nodejs/node/commit/bb14a2b098)] - **meta**: move one or more collaborators to emeritus (Node.js GitHub Bot) [#46411](https://togithub.com/nodejs/node/pull/46411)\n-   \\[[`152a3c7d1d`](https://togithub.com/nodejs/node/commit/152a3c7d1d)] - **process**: print versions by sort (Himself65) [#46428](https://togithub.com/nodejs/node/pull/46428)\n-   \\[[`164bfe82cc`](https://togithub.com/nodejs/node/commit/164bfe82cc)] - **(SEMVER-MINOR)** **src**: add initial support for single executable applications (Darshan Sen) [#45038](https://togithub.com/nodejs/node/pull/45038)\n-   \\[[`f3908411fd`](https://togithub.com/nodejs/node/commit/f3908411fd)] - **(SEMVER-MINOR)** **src**: allow optional Isolate termination in node::Stop() (Shelley Vohr) [#46583](https://togithub.com/nodejs/node/pull/46583)\n-   \\[[`bdba600d32`](https://togithub.com/nodejs/node/commit/bdba600d32)] - **src**: remove icu usage from node_string.cc (Yagiz Nizipli) [#46548](https://togithub.com/nodejs/node/pull/46548)\n-   \\[[`31fb2e22a0`](https://togithub.com/nodejs/node/commit/31fb2e22a0)] - **src**: add fflush() to SnapshotData::ToFile() (Anna Henningsen) [#46531](https://togithub.com/nodejs/node/pull/46531)\n-   \\[[`c34bac2fed`](https://togithub.com/nodejs/node/commit/c34bac2fed)] - **(SEMVER-MINOR)** **src**: allow blobs in addition to `FILE*`s in embedder snapshot API (Anna Henningsen) [#46491](https://togithub.com/nodejs/node/pull/46491)\n-   \\[[`c3325bfc0d`](https://togithub.com/nodejs/node/commit/c3325bfc0d)] - **src**: make edge names in BaseObjects more descriptive in heap snapshots (Joyee Cheung) [#46492](https://togithub.com/nodejs/node/pull/46492)\n-   \\[[`3c5db8f419`](https://togithub.com/nodejs/node/commit/3c5db8f419)] - **src**: avoid leaking snapshot fp on error (Tobias Nieen) [#46497](https://togithub.com/nodejs/node/pull/46497)\n-   \\[[`1a808a4aad`](https://togithub.com/nodejs/node/commit/1a808a4aad)] - **src**: check return value of ftell() (Tobias Nieen) [#46495](https://togithub.com/nodejs/node/pull/46495)\n-   \\[[`f72f643549`](https://togithub.com/nodejs/node/commit/f72f643549)] - **src**: remove unused includes from main thread (Yagiz Nizipli) [#46471](https://togithub.com/nodejs/node/pull/46471)\n-   \\[[`60c2a863da`](https://togithub.com/nodejs/node/commit/60c2a863da)] - **src**: use string_view instead of std::string& (Yagiz Nizipli) [#46471](https://togithub.com/nodejs/node/pull/46471)\n-   \\[[`f35f6d2218`](https://togithub.com/nodejs/node/commit/f35f6d2218)] - **src**: use simdutf utf8 to utf16 instead of icu (Yagiz Nizipli) [#46471](https://togithub.com/nodejs/node/pull/46471)\n-   \\[[`00b81c7afe`](https://togithub.com/nodejs/node/commit/00b81c7afe)] - **src**: replace icu with simdutf for char counts (Yagiz Nizipli) [#46472](https://togithub.com/nodejs/node/pull/46472)\n-   \\[[`683a1f8f3e`](https://togithub.com/nodejs/node/commit/683a1f8f3e)] - **(SEMVER-MINOR)** **src**: allow snapshotting from the embedder API (Anna Henningsen) [#45888](https://togithub.com/nodejs/node/pull/45888)\n-   \\[[`658d2f4710`](https://togithub.com/nodejs/node/commit/658d2f4710)] - **(SEMVER-MINOR)** **src**: make build_snapshot a per-Isolate option, rather than a global one (Anna Henningsen) [#45888](https://togithub.com/nodejs/node/pull/45888)\n-   \\[[`6801d3753c`](https://togithub.com/nodejs/node/commit/6801d3753c)] - **(SEMVER-MINOR)** **src**: add snapshot support for embedder API (Anna Henningsen) [#45888](https://togithub.com/nodejs/node/pull/45888)\n-   \\[[`95065c3185`](https://togithub.com/nodejs/node/commit/95065c3185)] - **src**: add additional utilities to crypto::SecureContext (James M Snell) [#45912](https://togithub.com/nodejs/node/pull/45912)\n-   \\[[`efc59d0843`](https://togithub.com/nodejs/node/commit/efc59d0843)] - **src**: add KeyObjectHandle::HasInstance (James M Snell) [#45912](https://togithub.com/nodejs/node/pull/45912)\n-   \\[[`a8a2d0e2b1`](https://togithub.com/nodejs/node/commit/a8a2d0e2b1)] - **src**: add GetCurrentCipherName/Version to crypto_common (James M Snell) [#45912](https://togithub.com/nodejs/node/pull/45912)\n-   \\[[`6cf860d3d6`](https://togithub.com/nodejs/node/commit/6cf860d3d6)] - **src**: back snapshot I/O with a std::vector sink (Joyee Cheung) [#46463](https://togithub.com/nodejs/node/pull/46463)\n-   \\[[`e77d538d32`](https://togithub.com/nodejs/node/commit/e77d538d32)] - **(SEMVER-MINOR)** **src**: allow embedder control of code generation policy (Shelley Vohr) [#46368](https://togithub.com/nodejs/node/pull/46368)\n-   \\[[`7756438c81`](https://togithub.com/nodejs/node/commit/7756438c81)] - **stream**: add trailing commas in webstream source files (Antoine du Hamel) [#46685](https://togithub.com/nodejs/node/pull/46685)\n-   \\[[`6b64a945c6`](https://togithub.com/nodejs/node/commit/6b64a945c6)] - **stream**: add trailing commas in stream source files (Antoine du Hamel) [#46686](https://togithub.com/nodejs/node/pull/46686)\n-   \\[[`633d3f292d`](https://togithub.com/nodejs/node/commit/633d3f292d)] - **(SEMVER-MINOR)** **stream**: add abort signal for ReadableStream and WritableStream (Debadree Chatterjee) [#46273](https://togithub.com/nodejs/node/pull/46273)\n-   \\[[`f91260b32a`](https://togithub.com/nodejs/node/commit/f91260b32a)] - **stream**: refactor to use `validateAbortSignal` (Antoine du Hamel) [#46520](https://togithub.com/nodejs/node/pull/46520)\n-   \\[[`6bf7388b62`](https://togithub.com/nodejs/node/commit/6bf7388b62)] - **stream**: allow transfer of readable byte streams (MrBBot) [#45955](https://togithub.com/nodejs/node/pull/45955)\n-   \\[[`c2068537fa`](https://togithub.com/nodejs/node/commit/c2068537fa)] - **stream**: add pipeline() for webstreams (Debadree Chatterjee) [#46307](https://togithub.com/nodejs/node/pull/46307)\n-   \\[[`4cf4b41c56`](https://togithub.com/nodejs/node/commit/4cf4b41c56)] - **stream**: add suport for abort signal in finished() for webstreams (Debadree Chatterjee) [#46403](https://togithub.com/nodejs/node/pull/46403)\n-   \\[[`b844a09fa5`](https://togithub.com/nodejs/node/commit/b844a09fa5)] - **stream**: dont access Object.prototype.type during TransformStream init (Debadree Chatterjee) [#46389](https://togithub.com/nodejs/node/pull/46389)\n-   \\[[`6ad01fd7b5`](https://togithub.com/nodejs/node/commit/6ad01fd7b5)] - **test**: fix `test-net-autoselectfamily` for kernel without IPv6 support (Livia Medeiros) [#45856](https://togithub.com/nodejs/node/pull/45856)\n-   \\[[`2239e24306`](https://togithub.com/nodejs/node/commit/2239e24306)] - **test**: fix assertions in test-snapshot-dns-lookup\\* (Tobias Nieen) [#46618](https://togithub.com/nodejs/node/pull/46618)\n-   \\[[`c4ca98e786`](https://togithub.com/nodejs/node/commit/c4ca98e786)] - **test**: cover publicExponent validation in OpenSSL (Tobias Nieen) [#46632](https://togithub.com/nodejs/node/pull/46632)\n-   \\[[`e60d3f2b1d`](https://togithub.com/nodejs/node/commit/e60d3f2b1d)] - **test**: add WPTRunner support for variants and generating WPT reports (Filip Skokan) [#46498](https://togithub.com/nodejs/node/pull/46498)\n-   \\[[`217f2f6e2a`](https://togithub.com/nodejs/node/commit/217f2f6e2a)] - **test**: add trailing commas in `test/pummel` (Antoine du Hamel) [#46610](https://togithub.com/nodejs/node/pull/46610)\n-   \\[[`641e1771c8`](https://togithub.com/nodejs/node/commit/641e1771c8)] - **test**: enable api-invalid-label.any.js in encoding WPTs (Filip Skokan) [#46506](https://togithub.com/nodejs/node/pull/46506)\n-   \\[[`89aa161173`](https://togithub.com/nodejs/node/commit/89aa161173)] - **test**: fix tap parser fails if a test logs a number (Pulkit Gupta) [#46056](https://togithub.com/nodejs/node/pull/46056)\n-   \\[[`faba8d4a30`](https://togithub.com/nodejs/node/commit/faba8d4a30)] - **test**: add trailing commas in `test/js-native-api` (Antoine du Hamel) [#46385](https://togithub.com/nodejs/node/pull/46385)\n-   \\[[`d556ccdd26`](https://togithub.com/nodejs/node/commit/d556ccdd26)] - **test**: make more crypto tests work with BoringSSL (Shelley Vohr) [#46429](https://togithub.com/nodejs/node/pull/46429)\n-   \\[[`c7f29b24a6`](https://togithub.com/nodejs/node/commit/c7f29b24a6)] - **test**: add trailing commas in `test/known_issues` (Antoine du Hamel) [#46408](https://togithub.com/nodejs/node/pull/46408)\n-   \\[[`a66e7ca6c5`](https://togithub.com/nodejs/node/commit/a66e7ca6c5)] - **test**: add trailing commas in `test/internet` (Antoine du Hamel) [#46407](https://togithub.com/nodejs/node/pull/46407)\n-   \\[[`0f75633086`](https://togithub.com/nodejs/node/commit/0f75633086)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#46575](https://togithub.com/nodejs/node/pull/46575)\n-   \\[[`ddf5002782`](https://togithub.com/nodejs/node/commit/ddf5002782)] - **test_runner**: parse non-ascii character correctly (Mert Can Altn) [#45736](https://togithub.com/nodejs/node/pull/45736)\n-   \\[[`5b748114d2`](https://togithub.com/nodejs/node/commit/5b748114d2)] - **test_runner**: allow nesting test within describe (Moshe Atlow) [#46544](https://togithub.com/nodejs/node/pull/46544)\n-   \\[[`c526f9f70a`](https://togithub.com/nodejs/node/commit/c526f9f70a)] - **test_runner**: fix missing test diagnostics (Moshe Atlow) [#46450](https://togithub.com/nodejs/node/pull/46450)\n-   \\[[`b31aabb101`](https://togithub.com/nodejs/node/commit/b31aabb101)] - **test_runner**: top-level diagnostics not ommited when running with --test (Pulkit Gupta) [#46441](https://togithub.com/nodejs/node/pull/46441)\n-   \\[[`6119289251`](https://togithub.com/nodejs/node/commit/6119289251)] - **test_runner**: add initial code coverage support (Colin Ihrig) [#46017](https://togithub.com/nodejs/node/pull/46017)\n-   \\[[`6f24f0621e`](https://togithub.com/nodejs/node/commit/6f24f0621e)] - **timers**: cleanup no-longer relevant TODOs in timers/promises (James M Snell) [#46499](https://togithub.com/nodejs/node/pull/46499)\n-   \\[[`1cd22e7d19`](https://togithub.com/nodejs/node/commit/1cd22e7d19)] - **tools**: fix bug in `prefer-primordials` lint rule (Antoine du Hamel) [#46659](https://togithub.com/nodejs/node/pull/46659)\n-   \\[[`87df34ac28`](https://togithub.com/nodejs/node/commit/87df34ac28)] - **tools**: fix update-ada script (Yagiz Nizipli) [#46550](https://togithub.com/nodejs/node/pull/46550)\n-   \\[[`f62b58a623`](https://togithub.com/nodejs/node/commit/f62b58a623)] - **tools**: add a daily wpt.fyi synchronized report upload (Filip Skokan) [#46498](https://togithub.com/nodejs/node/pull/46498)\n-   \\[[`803f00aa32`](https://togithub.com/nodejs/node/commit/803f00aa32)] - **tools**: update eslint to 8.34.0 (Node.js GitHub Bot) [#46625](https://togithub.com/nodejs/node/pull/46625)\n-   \\[[`f87216bdb2`](https://togithub.com/nodejs/node/commit/f87216bdb2)] - **tools**: update lint-md-dependencies to rollup@3.15.0 to-vfile@7.2.4 (Node.js GitHub Bot) [#46623](https://togithub.com/nodejs/node/pull/46623)\n-   \\[[`8ee9e48560`](https://togithub.com/nodejs/node/commit/8ee9e48560)] - **tools**: update doc to remark-html@15.0.2 to-vfile@7.2.4 (Node.js GitHub Bot) [#46622](https://togithub.com/nodejs/node/pull/46622)\n-   \\[[`148c5d9239`](https://togithub.com/nodejs/node/commit/148c5d9239)] - **tools**: update lint-md-dependencies to rollup@3.13.0 vfile-reporter@7.0.5 (Node.js GitHub Bot) [#46503](https://togithub.com/nodejs/node/pull/46503)\n-   \\[[`51c6c61a58`](https://togithub.com/nodejs/node/commit/51c6c61a58)] - **tools**: update ESLint custom rules to not use the deprecated format (Antoine du Hamel) [#46460](https://togithub.com/nodejs/node/pull/46460)\n-   \\[[`a51fe3c663`](https://togithub.com/nodejs/node/commit/a51fe3c663)] - **url**: replace url-parser with ada (Yagiz Nizipli) [#46410](https://togithub.com/nodejs/node/pull/46410)\n-   \\[[`129c9e7180`](https://togithub.com/nodejs/node/commit/129c9e7180)] - **url**: remove unused `URL::ToFilePath()` (Yagiz Nizipli) [#46487](https://togithub.com/nodejs/node/pull/46487)\n-   \\[[`9a604d67c3`](https://togithub.com/nodejs/node/commit/9a604d67c3)] - **url**: remove unused `URL::toObject` (Yagiz Nizipli) [#46486](https://togithub.com/nodejs/node/pull/46486)\n-   \\[[`d6fbebda54`](https://togithub.com/nodejs/node/commit/d6fbebda54)] - **url**: remove unused `setURLConstructor` function (Yagiz Nizipli) [#46485](https://togithub.com/nodejs/node/pull/46485)\n-   \\[[`17b3ee33c2`](https://togithub.com/nodejs/node/commit/17b3ee33c2)] - **vm**: properly support symbols on globals (Nicolas DUBIEN) [#46458](https://togithub.com/nodejs/node/pull/46458)\n\n### [`v19.6.1`](https://togithub.com/nodejs/node/releases/tag/v19.6.1): 2023-02-16, Version 19.6.1 (Current), @RafaelGSS\n\n[Compare Source](https://togithub.com/nodejs/node/compare/v19.6.0...v19.6.1)\n\nThis is a security release.\n\n##### Notable Changes\n\nThe following CVEs are fixed in this release:\n\n-   **[CVE-2023-23919](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23919)**: OpenSSL errors not cleared in error stack (Medium)\n-   **[CVE-2023-23918](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23918)**: Experimental Policies bypass via `process.mainModule.require`(High)\n-   **[CVE-2023-23920](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-23920)**: Insecure loading of ICU data through ICU_DATA environment variable (Low)\n\nMore detailed information on each of the vulnerabilities can be found in [February 2023 Security Releases](https://nodejs.org/en/blog/vulnerability/february-2023-security-releases/) blog post.\n\nThis security release includes OpenSSL security updates as outlined in the recent\n[OpenSSL security advisory](https://www.openssl.org/news/secadv/20230207.txt) and `undici` security update.\n\n##### Commits\n\n-   \\[[`97d9d55d2f`](https://togithub.com/nodejs/node/commit/97d9d55d2f)] - **build**: build ICU with ICU_NO_USER_DATA_OVERRIDE (RafaelGSS) [nodejs-private/node-private#374](https://togithub.com/nodejs-private/node-private/pull/374)\n-   \\[[`8ac90e6372`](https://togithub.com/nodejs/node/commit/8ac90e6372)] - **crypto**: clear OpenSSL error on invalid ca cert (RafaelGSS) [nodejs-private/node-private#368](https://togithub.com/nodejs-private/node-private/pull/368)\n-   \\[[`10a4c47e3a`](https://togithub.com/nodejs/node/commit/10a4c47e3a)] - **deps**: update undici to 5.19.1 (Node.js GitHub Bot) [#46634](https://togithub.com/nodejs/node/pull/46634)\n-   \\[[`b10fc75e4a`](https://togithub.com/nodejs/node/commit/b10fc75e4a)] - **deps**: update undici to 5.18.0 (Node.js GitHub Bot) [#46502](https://togithub.com/nodejs/node/pull/46502)\n-   \\[[`e9b64ea8b9`](https://togithub.com/nodejs/node/commit/e9b64ea8b9)] - **deps**: update undici to 5.17.1 (Node.js GitHub Bot) [#46502](https://togithub.com/nodejs/node/pull/46502)\n-   \\[[`66a24cec47`](https://togithub.com/nodejs/node/commit/66a24cec47)] - **deps**: cherry-pick Windows ARM64 fix for openssl (Richard Lau) [#46573](https://togithub.com/nodejs/node/pull/46573)\n-   \\[[`d8559aa6f5`](https://togithub.com/nodejs/node/commit/d8559aa6f5)] - **deps**: update archs files for quictls/openssl-3.0.8+quic (RafaelGSS) [#46573](https://togithub.com/nodejs/node/pull/46573)\n-   \\[[`dc477f547d`](https://togithub.com/nodejs/node/commit/dc477f547d)] - **deps**: upgrade openssl sources to quictls/openssl-3.0.8+quic (RafaelGSS) [#46573](https://togithub.com/nodejs/node/pull/46573)\n-   \\[[`2aae197670`](https://togithub.com/nodejs/node/commit/2aae197670)] - **lib**: makeRequireFunction patch when experimental policy (RafaelGSS) [nodejs-private/node-private#358](https://togithub.com/nodejs-private/node-private/pull/358)\n-   \\[[`6d17b693ec`](https://togithub.com/nodejs/node/commit/6d17b693ec)] - **policy**: makeRequireFunction on mainModule.require (RafaelGSS) [nodejs-private/node-private#358](https://togithub.com/nodejs-private/node-private/pull/358)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Update delete topic logic", "number": 4969, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4969", "body": "Allow deleting any topic that was not created by a customer."}
{"title": "Include thread participants for threads", "number": 497, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497", "body": "Include thread participants in the thread api model."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-902268784", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-902271978", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-902398154", "body": "need to make a decision on participant storage ..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-902620520", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-903197368", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-903346649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-903376412", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-903480355", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-903492996", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-903676022", "body": ""}
{"comment": {"body": "Why ignore?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#discussion_r822139777"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-903689344", "body": ""}
{"comment": {"body": "The unique index guarantees we don't insert dupes. The insert ignore here means we can just insert it and if it fails we can continue. Though it will ignore any failure reason, so maybe we should do a check and insert if not exists instead.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#discussion_r822149419"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#pullrequestreview-903860297", "body": ""}
{"comment": {"body": "Actually, turns out that `insertIgnore` does exactly what we want it to do: it'll fail loudly if there is a foreign key violation, but silent ignores if the insert fails on a uniqueness constraint. \r\n\r\nSo this will fail (what we want):\r\n\r\n```\r\nThreadParticipantModel.insertIgnore {\r\n    it[this.team] = teamId\r\n    it[this.thread] = threadId\r\n    it[this.member] = UUID.randomUUID()\r\n}\r\n```\r\n\r\nbut this will be fine if the record clashes with an existing record (i.e. it already exists):\r\n\r\n```\r\nThreadParticipantModel.insertIgnore {\r\n    it[this.team] = teamId\r\n    it[this.thread] = threadId\r\n    it[this.member] = teamMemberId\r\n}\r\n```\r\n\r\nFYI the SQL it generates is:\r\n\r\n```\r\nINSERT INTO threadparticipantmodel (\"createdAt\", id, \"member\", \"modifiedAt\", team, thread) \r\nVALUES ('2022-03-08T20:44:34.388574', 'dda99400-00be-4bab-8944-5f1b630db372', 'a001e946-923f-43d2-a3e5-27e5bdf20d9a', '2022-03-08T20:44:34.388578', 'ba12843a-0465-44da-a99f-2bb29348d867', '74cd4043-bc68-4bf6-9a9b-7245b639dceb') \r\nON CONFLICT DO NOTHING\r\n```\r\n\r\nthe key being `ON CONFLICT DO NOTHING`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/497#discussion_r822283883"}}
{"title": "chore(deps): update plugin com.expediagroup.graphql to v6.4.0", "number": 4970, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4970", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.expediagroup.graphql | 6.3.5 -> 6.4.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nExpediaGroup/graphql-kotlin\n\n### [`v6.4.0`]()\n\n#### Minor Changes\n\n-   feat: add support for federation v2.3 ([#1674]()) [@dariuszkuc]()\n-   feat: pass graphQLContext as fallback if deprecated context is null ([#1652]() [#1653]() ) [@samuelAndalon]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Refactor to use CryptoUtils", "number": 4971, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4971"}
{"title": "Add flags and endpoints for post-onboarding tasks", "number": 4972, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972", "body": "\nAdd flags to the Person model to keep track of a new user's progress per the post-onboarding checklist. \n* hasViewedTopFile will be manually set by the /person/hasViewedTopFile endpoint\n* hasCreatedNote will be set upon the user's first thread POST\n* hasCreatedWalkthrough will be set upon the user's first walkthrough POST"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1310499580", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1311853874", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1311866003", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1312232044", "body": ""}
{"comment": {"body": "Note: the intention here is that this would replace the current `Person.hasSeenTutorial` flag -- I think there needs to be a migration to move that boolean value to this model and then revving the API to remove or deprecate that field from the Person model.\r\n\r\nI think this can probably be done separately though -- ie it's not a blocker for the rest of this work to go in/we can keep depending on the Person flag for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#discussion_r1116309343"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1312232152", "body": ""}
{"comment": {"body": "@davidkwlam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#discussion_r1116309413"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1312317912", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1312328393", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1314331350", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1314331715", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1314336821", "body": ""}
{"comment": {"body": "```suggestion\r\n           return@suspendedTransaction personDAO.hasSeenTopFile.also { \r\n               if (it != hasSeenTopFile) {\r\n                   personDAO.hasSeenTopFile = hasSeenTopFile\r\n               }\r\n           }\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#discussion_r1117754140"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1314337782", "body": ""}
{"comment": {"body": "Need to test the falsey case", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#discussion_r1117755738"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1314338426", "body": ""}
{"comment": {"body": "Are we polling on this, or just calling once at startup?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#discussion_r1117756366"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1314339645", "body": ""}
{"comment": {"body": "If polling, then I think it would be better to just include in the `GET /person` call, since that is already polled.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#discussion_r1117757389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1314339766", "body": "Minor nits barely worth paying attention to"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1314361763", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1316037440", "body": ""}
{"comment": {"body": "@pwerry @kaych @matthewjamesadam this will impact whether we need a pusher channel for `GET /person/onboardingStatus` https://github.com/NextChapterSoftware/unblocked/pull/5028", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#discussion_r1119040930"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#pullrequestreview-1316152504", "body": ""}
{"comment": {"body": "Chatted IRL. Decision is to keep this and the pusher change but make any necessary changes to the client to update the pusher polling frequency based on which view is in focus.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4972#discussion_r1119107550"}}
{"title": "API service returns TopicSource.Approved topics", "number": 4973, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4973"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4973#pullrequestreview-1312264104", "body": "I'm okay with this.\nthank you."}
{"title": "Added test scheme to bypass SkipCORS in tests for test clients, reordered plugins", "number": 4974, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4974", "body": "YOLO"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4974#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4974#pullrequestreview-**********", "body": ""}
{"title": "Refactor ScmAuthApi", "number": 4975, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4975"}
{"title": "Refactored token providers", "number": 4976, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4976", "body": "Refactored token providers to cache values in memory. Reduces load on native storage implementations. Also allows for push based token providers (e.g. JetbrainsTokenProvider)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4976#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Prettier not working?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4976#discussion_r1116063297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4976#pullrequestreview-**********", "body": ""}
{"title": "Cleanup old migration", "number": 4977, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4977"}
{"title": "Test Bitbucket Cloud in DEV", "number": 4978, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4978"}
{"title": "Introduce ScmAppApiFactory to abstract \"App\" API", "number": 4979, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4979"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4979#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Note: it still returns the `GitHubAppApi`.\r\n\r\nShould return an `ScmAppApi` instead. I'll do that in follow up once I figure out what makes to expose for an SCM agnostic interface.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4979#discussion_r1115220723"}}
{"title": "Rip out Thundra", "number": 498, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/498"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/498#pullrequestreview-902297158", "body": ""}
{"title": "Fix OAuth code exchange", "number": 4980, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4980", "body": "OAuth implementation require a code exchange API parameter called redirect_uri\nthat matches one of the pre-configured redirect URLs declared in the OAuth app\nconfiguration.\nGitHub is a bit lenient here, which is why we did not catch until now with GitLab\nand Bitbucket."}
{"title": "alarm is too noisy", "number": 4981, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4981"}
{"title": "chore(deps): update plugin org.openapi.generator to v6.4.0", "number": 4982, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4982", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.openapi.generator | 6.3.0 -> 6.4.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency com.expediagroup:graphql-kotlin-client-serialization to v6.4.0", "number": 4983, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4983", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.expediagroup:graphql-kotlin-client-serialization | 6.3.5 -> 6.4.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nExpediaGroup/graphql-kotlin\n\n### [`v6.4.0`]()\n\n#### Minor Changes\n\n-   feat: add support for federation v2.3 ([#1674]()) [@dariuszkuc]()\n-   feat: pass graphQLContext as fallback if deprecated context is null ([#1652]() [#1653]() ) [@samuelAndalon]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency net.logstash.logback:logstash-logback-encoder to v7.3", "number": 4984, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4984", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| net.logstash.logback:logstash-logback-encoder | 7.2 -> 7.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Use OAuthTokens class for SCM user APIs and persist refresh token", "number": 4985, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4985"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4985#pullrequestreview-1310745429", "body": ""}
{"comment": {"body": "Currently null because of GitHub, but once we enable refreshing tokens for GitHub Apps then this will be non null", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4985#discussion_r1115324929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4985#pullrequestreview-1310746283", "body": ""}
{"comment": {"body": "now persisting encrypted refresh tokens", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4985#discussion_r1115325263"}}
{"title": "chore(deps): update dependency @types/node to v18.14.1", "number": 4986, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4986", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/node (source) | 18.14.0 -> 18.14.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update protobufversion to v3.22.0", "number": 4987, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4987", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.google.protobuf:protoc (source) | 3.21.12 -> 3.22.0 |  |  |  |  |\n| com.google.protobuf:protobuf-kotlin (source) | 3.21.12 -> 3.22.0 |  |  |  |  |\n| com.google.protobuf:protobuf-java-util (source) | 3.21.12 -> 3.22.0 |  |  |  |  |\n| com.google.protobuf:protobuf-java (source) | 3.21.12 -> 3.22.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nprotocolbuffers/protobuf\n\n### [`v3.22.0`]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency @testing-library/react to v14", "number": 4988, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4988", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @testing-library/react | ^13.4.0 -> ^14.0.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ntesting-library/react-testing-library\n\n### [`v14.0.0`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   Prevent \"missing act\" warning for queued microtasks ([#1137]()) ([f78839b]())\n\n##### Features\n\n-   Bump `@testing-library/dom` to 9.0.0 ([#1177]()) ([6653c23]())\n-   Drop support for Node.js 12.x ([#1169]()) ([9b7a1e2]())\n\n##### BREAKING CHANGES\n\n-   See \n-   Minimum supported Node.js version is now 14.x\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "[RFC] Trigger topic generation once bulk ingestion is complete", "number": 4989, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4989", "body": "Alternatively we could fire an event and have it be handled in the topic service if we don't want to call this from the SCM service."}
{"comment": {"body": "Doing this in the topic service instead", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4989#issuecomment-1442227688"}}
{"title": "David has inspired me to add proper permissions", "number": 4990, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4990"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4990#pullrequestreview-1311892846", "body": ""}
{"title": "Add logging to capture CORS data in prod", "number": 4991, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4991", "body": "Will immediately revert when I get logs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4991#pullrequestreview-1311992050", "body": ""}
{"title": "Add background job to handle topic generation events", "number": 4992, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4992"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4992#pullrequestreview-1312012101", "body": ""}
{"title": "Enable bucket versioning and lifecyle rules", "number": 4993, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4993", "body": "Added a new type of lifecycle rule for log buckets to remove data after 90 days\nEnabled bucket versioning on any buckets that hold user data or logs\nEnabled user data (customer) data lifecyle rule on buckets that hold temporary user data. This rule makes sure delete markers and older versions of objects are removed before 30 days"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4993#pullrequestreview-1312033299", "body": ""}
{"comment": {"body": "I don't like destructive config params that default to enabled! \r\n\r\nChanged our config to explicitly enable this wherever needed. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4993#discussion_r1116173380"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4993#pullrequestreview-1312037445", "body": ""}
{"title": "Move to custom ktlint gradle plugin with gradle 8 support", "number": 4994, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4994", "body": "https://github.com/JLLeitschuh/ktlint-gradle/pull/634\nThat pr actuallly fixes the problem.\nI forked it and did a local maven build of the plugin here:\nhttps://github.com/NextChapterSoftware/ktlint-gradle"}
{"title": "removed clashing configs", "number": 4995, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4995", "body": "expiredObjectDeleteMarker was clashing with the other configs. Removed it from Backup and Log retention policy rules"}
{"title": "remove the bad config from waf logs bucket", "number": 4996, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4996"}
{"title": "JB AuthStore", "number": 4997, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4997", "body": "Setup basic AuthStore in agent. Auth status is then piped to Kotlin plugin.\n\nNext step is setting up auth sidebar."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4997#pullrequestreview-1312150628", "body": ""}
{"comment": {"body": "Adding this seems to break things?\r\n\r\ngrpc calls seem to hang.\r\nThey are initiated on the Kotlin side but never return.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4997#discussion_r1116251683"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4997#pullrequestreview-1316486417", "body": ""}
{"comment": {"body": "I'm not 100% sure what this service is meant for and how it would differ from the other project-level services?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4997#discussion_r1119348256"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4997#pullrequestreview-1316494966", "body": ""}
{"title": "Add array support to exposed", "number": 4998, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4998"}
{"title": "Remove CORS debug logging", "number": 4999, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4999"}
{"title": "Break", "number": 5, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5"}
{"title": "Shuffle files and folders in VSCode project", "number": 50, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/50", "body": "This makes the VSCode project match the Web project, more or less."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/50#pullrequestreview-854864802", "body": ""}
{"comment": {"body": "I'm wondering if it's worth centralizing these path definitions.  For all of the .ts-based config files (everything except tsconfig) we could store the set of root paths in a single file and reference it everywhere...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/50#discussion_r786354147"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/50#pullrequestreview-855724970", "body": ""}
{"comment": {"body": "I don't think the alias will be identical between the clients. \r\nDo we refactor out the subset that are the same? We could. I don't have preferences either way.\r\n\r\n\r\n ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/50#discussion_r786972940"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/50#pullrequestreview-855725576", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/50#pullrequestreview-855748273", "body": ""}
{"comment": {"body": "Oh, I meant \"factor out\" per project -- at least then there are only one or two places that the roots are specified in each project, not 4.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/50#discussion_r786989595"}}
{"title": "Add create PR models and endpoints", "number": 500, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500"}
{"comment": {"body": "Closing for now until we revisit this feature.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#issuecomment-1090888784"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#pullrequestreview-902365832", "body": ""}
{"comment": {"body": "We'll need to convert this to GitHub/whateverscm markdown", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#discussion_r821182867"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#pullrequestreview-903266069", "body": ""}
{"comment": {"body": "Not clear to me what ID is.\r\n\r\nIf it\u2019s a DB ID in Unblocked then why would we need to persist this, as opposed to just sending the PR body to GitHub? Does this imply that we should suck down and persist all other PRs from the repo from GitHub? Any thoughts on how this is related to other models in the system like threads or messages?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#discussion_r821841335"}}
{"comment": {"body": "From design, I think we need to show the `prNumber` too right? So let\u2019s add that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#discussion_r821842286"}}
{"comment": {"body": "Yeah, server should do this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#discussion_r821843680"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#pullrequestreview-903322164", "body": ""}
{"comment": {"body": "Yeah I kind of just based the patterning off of other endpoints, but I don't think the id is necessary. I can remove\r\n\r\nRe: threads and messages, I don't think there's a close relation there as those are more tied to PR Comments. We still need a way to create a PR for the PR as you go feature, but the rest of the system only interacts with with PRs via PR Comments (for now at least). So there is a `prComment` Thread type, and messages are tied to those Threads. \r\n\r\nI suppose there's a case to be made of whether we're duping properties on the `PullRequestComment` and `PullRequest` models but since we don't really fetch the PullRequest I don't know if that's a big problem or not. Open to suggestions on what to change though", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#discussion_r821881472"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#pullrequestreview-903421713", "body": ""}
{"comment": {"body": "Ok, took a look at your related PR (https://github.com/NextChapterSoftware/unblocked/pull/496) and I remember now that we also need to post comments to the PR. The GitHub API does not allow for creating both the PR and the comments in one shot, which means that the server needs to maintain state while we make multiple requests. The client can also upload a PR comment to the PR after the PR has been created; and (I think) this also needs to work for PRs not created by Unblocked client.\r\n\r\nSo I think we need a bunch of APIs for this flow:\r\n- Given a {repo, current branch} fetch open PRs\r\n  - backend could either (1) fetch on demand from GitHub [high latency, since we cannot poll frequently], or (2) sync all repo PRs via webhooks [low latency, but requires that we store every PR]. Maybe we need to do option 2 anyway for \r\nPR ingestion @davidkwlam?\r\n- Given a {repo, current branch} create a PR for current branch, if no PR exists\r\n- Given a {repo, current branch, notes} create a PR for current branch with PR comments for each note, if no PR exists\r\n- Given a {repo, current branch, PR, note} create a PR comment from the note\r\n\r\nSince we have a dependency on GitHub, we need to make the creation resilient on backend, by persisting the creation requests an scm-service to push to GitHub.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#discussion_r821953957"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#pullrequestreview-903597148", "body": ""}
{"comment": {"body": "> Given a {repo, current branch} fetch open PRs\r\n\r\nIs this to inform the client whether there's an open PR/s on the current branch or not? I'd assume it's rare but interesting scenario re: if there's more than one open PR on the current branch, would we show the ingested comments for all the open PRs??\r\n\r\n> Given a {repo, current branch} create a PR for current branch, if no PR exists\r\n> Given a {repo, current branch, notes} create a PR for current branch with PR comments for each note, if no PR exists\r\n\r\nCould we consolidate these into one POST endpoint? ie with an optional list of notes (or threadIds)\r\n\r\n> Given a {repo, current branch, PR, note} create a PR comment from the note\r\n\r\nDo we need a separate API endpoint for this? This is essentially createThread but with the prComment thread type I think, i.e. could the backend know that given these conditions to post a pr comment to GH? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#discussion_r822080709"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#pullrequestreview-903844238", "body": ""}
{"comment": {"body": "> > Given a {repo, current branch} fetch open PRs\r\n> \r\n> Is this to inform the client whether there's an open PR/s on the current branch or not?\r\n\r\nYes.\r\n\r\n> I'd assume it's rare but interesting scenario re: if there's more than one open PR on the current branch, would we show the ingested comments for all the open PRs??\r\n\r\nYes, don't see why not. The multi-PR per-branch is not useful for development; it's more of a release engineering activity where bug fixes are ported to different release branches, so there normally only be PR comments on one of the PRs.\r\n\r\n\r\n> > Given a {repo, current branch} create a PR for current branch, if no PR exists\r\n> > Given a {repo, current branch, notes} create a PR for current branch with PR comments for each note, if no PR exists\r\n> \r\n> Could we consolidate these into one POST endpoint? ie with an optional list of notes (or threadIds)\r\n\r\nYup, definitely.\r\n\r\n\r\n> > Given a {repo, current branch, PR, note} create a PR comment from the note\r\n> \r\n> Do we need a separate API endpoint for this? This is essentially createThread but with the prComment thread type I think, i.e. could the backend know that given these conditions to post a pr comment to GH?\r\n\r\nYeah, we can use `createThread`, but I think we're missing some fields. The server right now has no idea what the PR or branch is.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/500#discussion_r822271335"}}
{"title": "Fix misplaced sourcemark by considering the best candidate based on multi-line similarity", "number": 5000, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5000", "body": "Should be a lot more precise, especially when there has been a large code refactor.\n"}
{"title": "A compromised API service could send users to a bad upgrade payload", "number": 5001, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5001", "body": "Simple hostname validation check. Upgrades are always hosted within our domain so hardcoding the hostname is fine. macOS TLS then guarantees domain name validation checks on the outbound request. \nFor an attacker to get around this check, they would have to deploy a bad payload to our S3 bucket. The most likely vector for this attack is through GitHub CI/CD. Remediation in this scenario is basically zero."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5001#pullrequestreview-**********", "body": ""}
{"title": "Add topic keywords", "number": 5002, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5002"}
{"title": "add cpu monitoring to resources under secops account", "number": 5003, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5003", "body": "Adding cpu alarms for OpenVPN, Bastion Host and Gradle Cache node. Required for SOC2 stuff. \nI have already deployed these changes. I know we can eliminate some code redundancy here but frankly it's not worth the effort."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5003#pullrequestreview-**********", "body": ""}
{"title": "Aggressive CORS logging", "number": 5004, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5004", "body": "Reverts NextChapterSoftware/unblocked#4999 and adds more"}
{"comment": {"body": "> dog on a bone\r\n\r\nLast crack at this I swear. End of day distraction", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5004#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5004#pullrequestreview-**********", "body": "dog on a bone"}
{"title": "Remove jetbrains duplicate eslint and treat react-hooks/exhaustive-deps as errors", "number": 5005, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5005"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5005#pullrequestreview-1312327988", "body": ""}
{"title": "Fix deployment", "number": 5006, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5006"}
{"title": "Remove state in storage context", "number": 5007, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5007"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5007#pullrequestreview-1312365165", "body": "This is fine with how we're using the context now I think, since we're not relying on any reactivity in the stored values.  We can revisit if we need that later."}
{"title": "Fix topic generation", "number": 5008, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5008"}
{"title": "Forgot a change", "number": 5009, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5009", "body": "Fix topic generation\nFi xtopics"}
{"title": "SourceMark API interface", "number": 501, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/501", "body": "Implementation will come later. Just want to be clear on the interface. The IPC will plugin into this."}
{"title": "Increase topic generation", "number": 5010, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5010"}
{"title": "Add forward proxy support to all services that were missing it", "number": 5011, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5011"}
{"title": "Removes aggressive CORS logging", "number": 5012, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5012"}
{"title": "we were using the wrong metrics namespace", "number": 5013, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5013"}
{"title": "chore(deps): update aws-cdk monorepo to v2.66.1", "number": 5014, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5014", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| aws-cdk | 2.66.0 -> 2.66.1 |  |  |  |  |\n| aws-cdk-lib | 2.66.0 -> 2.66.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-cdk\n\n### [`v2.66.1`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   Correct SamlConsolePrincipal for non-China ([#24277]()) ([d562871]()), closes [#24243]()\n\n***\n\n#### Alpha modules (2.66.1-alpha.0)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "approved topics improvements", "number": 5015, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5015"}
{"title": "Add secrets service", "number": 5016, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5016"}
{"comment": {"body": "Add to docker compose local stack", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5016#issuecomment-1444038808"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5016#pullrequestreview-1313918495", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5016#pullrequestreview-1313934719", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5016#pullrequestreview-1314043986", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5016#pullrequestreview-1314098805", "body": ""}
{"title": "Update admin console topics page", "number": 5017, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5017"}
{"title": "Upgrade java", "number": 5018, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5018"}
{"title": "Change action name for github", "number": 5019, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5019"}
{"title": "Fix code block styling and add custom panel icons", "number": 502, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/502", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/502#pullrequestreview-904840399", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/502#pullrequestreview-904841461", "body": ""}
{"title": "Update clearing logic in SearchContext", "number": 5020, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5020"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5020#pullrequestreview-**********", "body": ""}
{"title": "Refactor IDE/sourcemark integration", "number": 5021, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5021", "body": "Moves much of the IDE sourcemark integration code from /vscode into /shared/ide.\n\nMoved SourceMarkEngine and SourceMarkProvider -- top level API for the SM engine\nMoved FileSourceMarkStream -- stream that returns SMs for a file.  I also refactored this code quite a bit to be more stream-centric, it's cleaner and easier to follow now, and works better with varying input APIs\nRemoved FocusTracker -- it was duplicated functionality from FocusStream.\nAdded IDEWorkspace -- an abstraction for workspace/project services.  Right now provides editor events, but this could grow quite a bit\nMoved associated tests and mocks\n\nNext PR will be similar, moving more things"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5021#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Instead of having new @shared-ide, should we use something like this? Not necessary for this Pr. Just a though since I'm doing something similar.\r\n` \"@/shared-ide/*\": [\"shared/ide/*\"]`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5021#discussion_r1119354215"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5021#pullrequestreview-1316495084", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5021#pullrequestreview-1316497382", "body": ""}
{"comment": {"body": "Does that work in all contexts?  ie, we define these mappings in 4 places (tsconfig, webpack config, storybook, and jest) -- I'm not sure that kind of matching will work everywhere?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5021#discussion_r1119355992"}}
{"title": "Remove topics feature flag", "number": 5022, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5022"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5022#pullrequestreview-1314132091", "body": ""}
{"title": "Fix diff codegen", "number": 5023, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5023"}
{"title": "Use Java 19 for all macOS builders", "number": 5024, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5024"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5024#pullrequestreview-1314169148", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5024#pullrequestreview-1314169602", "body": ""}
{"title": "Adding service name as common name to certificates", "number": 5025, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5025", "body": "Adding service name (e.g apiserivce) as common name to all pod certificates."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5025#pullrequestreview-1314313979", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5025#pullrequestreview-1314314387", "body": ""}
{"title": "Update ChannelPoller tests to use mock timer", "number": 5026, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5026"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5026#pullrequestreview-1314348856", "body": ""}
{"comment": {"body": "This ensures that if the poll happens precisely on the next polling boundary, that we will still poll.  This didn't result in runtime bugs in the product because by the time this was hit we would always be beyond the interval, but when writing precise tests with mock timers this makes for nicer test conditions (ie, you can guarantee you poll right on the polling boundary).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5026#discussion_r1117773071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5026#pullrequestreview-**********", "body": ""}
{"comment": {"body": "this is a cool utility", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5026#discussion_r1117800283"}}
{"title": "backfillRecommendations runs for all team members", "number": 5027, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5027", "body": "Since we want recommended threads exist before team members onboard, this logic will need to run for every team member and not just those with unblocked accounts. That means this logic could take a little while."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5027#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5027#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5027#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5027#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5027#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5027#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5027#pullrequestreview-**********", "body": "thanks davey"}
{"title": "Create pusher channel for /person/onboardingStatus", "number": 5028, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5028"}
{"comment": {"body": "See https://github.com/NextChapterSoftware/unblocked/pull/4972#discussion_r1117757389", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5028#issuecomment-1444828816"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5028#pullrequestreview-1318957377", "body": "LGTM pending any other concerns"}
{"title": "chore(deps): update plugin io.freefair.github.dependency-submission to v6.6.3", "number": 5029, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5029", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.freefair.github.dependency-submission | 6.6.2 -> 6.6.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nfreefair/gradle-plugins\n\n### [`v6.6.3`]()\n\n[Compare Source]()\n\n**Full Changelog**: \n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Scheduler runs only when Git tree changes, backs off with inactivity, and throttles down eventually", "number": 503, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/503", "body": "Completes Source Mark Scheduler section of plan\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/503#pullrequestreview-903613813", "body": ""}
{"title": "chore(deps): update dependency com.github.johnrengelman.shadow to v8", "number": 5030, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5030", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.johnrengelman.shadow | 7.1.2 -> 8.0.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "SCM OAuth token refresh APIs", "number": 5031, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5031"}
{"comment": {"body": "Blocked by this\r\nhttps://linear.app/unblocked/issue/UNB-1022/loginexchange-is-calling-apiauthexchangeauthcode-twice-at-login", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5031#issuecomment-1446557817"}}
{"comment": {"body": "> Blocked by this\r\n> https://linear.app/unblocked/issue/UNB-1022/loginexchange-is-calling-apiauthexchangeauthcode-twice-at-login\r\n\r\nAddressed by #5035.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5031#issuecomment-1446924435"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5031#pullrequestreview-1314856630", "body": ""}
{"title": "Do not downsample Honeycomb auth-service events", "number": 5032, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5032", "body": "Default was 64, now 1."}
{"title": "Engagement score in admin web", "number": 5033, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5033"}
{"title": "Adding new ALB record under the shared region", "number": 5034, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5034", "body": "We need to migrate our ALB record to standard region so we could make our DR story simpler. I'll do the CloudFront cutover later tonight."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5034#pullrequestreview-1316267404", "body": ""}
{"title": "Reduce multiple exchange requests", "number": 5035, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5035", "body": "React StrictMode  will cause useEffect hooks to call twice. \nThis is problematic for useEffects that make post requests twice within useEffects. \nThis only occurs in local development. Production builds (aka deployed dev & prod) will not have these issues.\nAdded a cache to dedupe exchange requests for local dev."}
{"comment": {"body": "@matthewjamesadam @kaych If you're aware of any other cases where we use useEffect like this, please let me know or make an update to make it safe.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5035#issuecomment-1446902555"}}
{"comment": {"body": "This is hilarious...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5035#issuecomment-1446904786"}}
{"comment": {"body": "Hmm... it's pretty unusual that we run mutating operations in a `useEffect`, so I can see why we've never noticed this locally before.\r\n\r\nI don't love the idea of making a local cache and more complicated code to work around a bug we only see in local dev..... this is maybe telling us that this pattern is not great, but I can' think of a better option off the top of my head.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5035#issuecomment-1446931223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5035#pullrequestreview-1316265727", "body": ""}
{"title": "Fix bug that resulted in file editor not splitting correctly", "number": 5036, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5036"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5036#pullrequestreview-1316285086", "body": ""}
{"title": "chore(deps): update dependency @types/node to v18.14.2", "number": 5037, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5037", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/node (source) | 18.14.1 -> 18.14.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update activemqversion to v5.17.4", "number": 5038, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5038", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.apache.activemq:activemq-pool (source) | 5.17.3 -> 5.17.4 |  |  |  |  |\n| org.apache.activemq:activemq-client (source) | 5.17.3 -> 5.17.4 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Video Panel Action for Hub IPC", "number": 5039, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5039"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5039#pullrequestreview-1316293355", "body": ""}
{"comment": {"body": "This is a *slight* race. Could we only send this event *after* the video panel opens?\r\n\r\nAlso, this should return an error if video panel fails to open.\r\nWould be nice for VSCode to understand *when* the panel opens.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5039#discussion_r1119210592"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5039#pullrequestreview-1316304426", "body": ""}
{"comment": {"body": "There's no real error path in the chain of execution to open the video panel, so error reporting from the Hub side probably doesn't make sense. The only errors that might occur here are communication errors via IPC, or if the Hub itself isn't running. \r\n\r\nBut we can certainly delay the response until the video panel is open.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5039#discussion_r1119220899"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5039#pullrequestreview-1316311464", "body": ""}
{"comment": {"body": "Actually I just remembered that to effectively \"wait\" for execution to complete, we need to either:\r\n1. Refactor HubIPC to use GRPC async (like the video app) OR\r\n2. Use a dirty semaphore (gross)\r\n\r\nI think we should defer this until we refactor to use async. I will add a comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5039#discussion_r1119225891"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5039#pullrequestreview-1316508849", "body": ""}
{"title": "Jeff/vscode deployment", "number": 504, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504#pullrequestreview-903503679", "body": ""}
{"comment": {"body": "I think we can get rid of the build step here?  `package:dev` runs `build:dev` already?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504#discussion_r822013879"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504#pullrequestreview-903506987", "body": ""}
{"comment": {"body": "This is fine for now but I think I'd prefer if we defined the version number in the extension's package.json (or somewhere else in our code) -- it feels odd to update a CI script to bump the version, and changing this file will cause every project to be rebuilt.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504#discussion_r822016283"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504#pullrequestreview-903507109", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504#pullrequestreview-903527628", "body": ""}
{"comment": {"body": "I've changed it so that package does *not* build to allow us to set the version of the /dist/package.json", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504#discussion_r822030451"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504#pullrequestreview-903557202", "body": ""}
{"comment": {"body": "Ah I see, OK sounds good.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/504#discussion_r822051908"}}
{"title": "chore(deps): update dependency com.github.johnrengelman.shadow to v8.1.0", "number": 5040, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5040", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.johnrengelman.shadow | 8.0.0 -> 8.1.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update plugin com.github.johnrengelman.shadow to v8", "number": 5041, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5041", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.johnrengelman.shadow | 7.1.2 -> 8.1.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Engagement metrics are greatly under-reporting", "number": 5042, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5042", "body": "We were incorrectly limiting the metrics that are used for activity in admin web."}
{"title": "Search page can find members who do not have accounts", "number": 5043, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5043"}
{"title": "Basic tunnel service", "number": 5044, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5044"}
{"title": "Unsubscribe from pusher for removed/deleted teams", "number": 5045, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5045"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5045#pullrequestreview-**********", "body": ""}
{"title": "Change \"Create Topic\" to \"Add Topic\"", "number": 5046, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5046", "body": "As per Dennis' request."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5046#pullrequestreview-**********", "body": ""}
{"title": "Rename label", "number": 5047, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5047"}
{"title": "Replace PersonModel.hasCreatedNote with  PersonModel.hasCreatedThread", "number": 5048, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5048"}
{"comment": {"body": "Nevermind, seems like there are bigger changes needed. Let's just close this for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5048#issuecomment-1447274166"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5048#pullrequestreview-1316570809", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5048#pullrequestreview-1316573044", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5048#pullrequestreview-1316573908", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5048#pullrequestreview-1316574415", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5048#pullrequestreview-1316574508", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5048#pullrequestreview-1316575091", "body": ""}
{"title": "[Onboarding] Add agentType header and dismiss property", "number": 5049, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5049", "body": "Naming up in the air"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5049#pullrequestreview-1316629221", "body": "@davidkwlam gets the final say on this but LGTM"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5049#pullrequestreview-1318414786", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5049#pullrequestreview-1318774947", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5049#pullrequestreview-1318891046", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5049#pullrequestreview-1318907335", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5049#pullrequestreview-1318916283", "body": ""}
{"title": "Introduce SourceMark snippet DB models", "number": 505, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/505", "body": "Note:\n- src not encrypted, encoded as JSON in db\n- not required to upload snippets yet to avoid disruption\nNext:\n- api to get /  set snippets (#508 and #511 )\n"}
{"comment": {"body": "A couple comments on this...\r\n1. Is it worth storing a language identifier here?  We can try to derive it from the extension but that is not always accurate.  Maybe this doesn't matter for April?\r\n2. Is the JSON formatting primarily so you can store line numbers?  Do we need the line numbers, since we can derive them from the source point bounds?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/505#issuecomment-1062301716"}}
{"comment": {"body": "> 1. Is it worth storing a language identifier here?  We can try to derive it from the extension but that is not always accurate.  Maybe this doesn't matter for April?\r\n\r\nYup, we can do that in a follow up PR. The SourcePoint already has the file extension, but I presume the IDE can give us a better language identifier.\r\n\r\n\r\n> 2. Is the JSON formatting primarily so you can store line numbers?  Do we need the line numbers, since we can derive them from the source point bounds?\r\n\r\nThe Source Point bounds are not 1:1 with the snippet, because we add lines of context before and after the user selected area. So you cannot derive one from the other.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/505#issuecomment-1062338910"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/505#pullrequestreview-904797818", "body": ""}
{"title": "Update the topic source instead of creating a duplicate", "number": 5050, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5050", "body": "This will move a topic into the Approved list instead of creating a copy. This does mean we might recreate a topic if we re-run topic generation but I confirmed with Doug that is OK for now, since this is meant to help him during topic curation."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5050#pullrequestreview-1318754793", "body": ""}
{"title": "Add client bits for onboarding toast", "number": 5051, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051", "body": "NOTE: This is all client logic with mock data, and is manually hidden from the UI. Just wanted to get these first bits in before hooking up to the API and into the current onboarding flow.\n\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1316627651", "body": ""}
{"comment": {"body": "temporary; manually hide in the UI for now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1119449426"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318294539", "body": ""}
{"comment": {"body": "Would be worth checking how this page handles responsively.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120589947"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318312111", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318332303", "body": ""}
{"comment": {"body": "Does this run distinct & compactMap *after* the promises resolve?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120614240"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318342518", "body": ""}
{"comment": {"body": "This feels quite expensive to compute every 5 seconds. Does this need to poll?\r\n\r\nIf yes, we should disable polling if the page is in the background / unfocused.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120621003"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318349309", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318351214", "body": ""}
{"comment": {"body": "should bubble this up to the UI as a window error as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120627171"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318352014", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318392312", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318397380", "body": ""}
{"comment": {"body": "Could look into mime types?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120655171"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318730954", "body": ""}
{"comment": {"body": "You wrote this afaik, I just pulled it into this file\r\n\r\nI think it stops polling as soon as values are returned (line 53)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120843448"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318746156", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318784511", "body": ""}
{"comment": {"body": "Just as a side note, this pattern of creating a stream in a module and subscribing to it in the same place is pretty odd.  It's probably generally an antipattern -- if the stream is both produced and consumed in the same place it may be better to just not have a stream at all.  I haven't looked at this code in depth or anything, but in this case we might just be better off with a local timer for polling or something.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120891237"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318784821", "body": ""}
{"comment": {"body": "(Nothing to do with this PR, just a note for future reference)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120891575"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318848404", "body": ""}
{"comment": {"body": "It's pretty fine. It's just blocks and text right now. Will revisit once we add videos/gifs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120951183"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318893536", "body": ""}
{"comment": {"body": "\r\nI missed that stopPolling \ud83e\udd26 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1120995841"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318900964", "body": ""}
{"comment": {"body": "Yeah. Rewriting this to be non-stream based would be pretty simple. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#discussion_r1121003182"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5051#pullrequestreview-1318901458", "body": ""}
{"title": "add powerml v2", "number": 5052, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5052"}
{"title": "Fix powerml image", "number": 5053, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5053"}
{"title": "Fix casing", "number": 5054, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5054"}
{"title": "AddChunked", "number": 5055, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5055", "body": "Fix casing\nAdd chunked\nUpdate"}
{"title": "OAuth token refresh service", "number": 5056, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#pullrequestreview-1322553603", "body": ""}
{"comment": {"body": "This confused me initially because it's not clear that the lock is \"locked\". I expected this:\r\n```kotlin\r\nval lock = getDistributedRefreshLock(identityId)\r\nlock.obtain()\r\n...etc\r\nlock.release\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#discussion_r1123653692"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#pullrequestreview-1322565901", "body": ""}
{"comment": {"body": "A pattern like would be ideal, where the \"use\" code block takes care of acquiring and releasing the lock. This would make it impossible for a caller to misuse the lock. Unfortunately, I couldn't figure out how to do this with Redis operations because they are suspending. I'm sure there's a way, just couldn't spend more time on it right now.\n\n\n\n```\nlock.use {\n  // do stuff in locked region\n}\n```\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#discussion_r1123661835"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#pullrequestreview-1322568911", "body": ""}
{"comment": {"body": "Should be be retrying heavily in this method to increase the probability of success?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#discussion_r1123663815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#pullrequestreview-1322829418", "body": ""}
{"comment": {"body": "Chatted IRL.\n\n\n\nWe decided that the best approach (in follow up) is to try to remediate when the refresh token becomes corrupt. We can _detect_ that the token is corrupt by catching HTTP responses for this call; when it responses with 401 then we know that our persisted refresh token is invalid. We mark the identity as \"needsReauthorization\" if the invalid refresh token matches the persisted refresh token (doing this transactionally to avoid refresh races). We can render a reauthorization button in a banner in the clients to prompt the user to reauthorize. Then everything works again.\n\n\n\n```\ntokenRefresher.refreshAccessTokens(requireNotNull(oldTokens.refreshToken))\n```\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#discussion_r1123832853"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#pullrequestreview-1322844279", "body": ""}
{"comment": {"body": "[https://linear.app/unblocked/issue/UNB-1036/remediation-of-corrupt-scm-refresh-tokens](https://linear.app/unblocked/issue/UNB-1036/remediation-of-corrupt-scm-refresh-tokens) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#discussion_r1123846728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#pullrequestreview-1323045149", "body": ""}
{"comment": {"body": "https://gist.github.com/richiebres/7f57a66ce676abc7643a6c615c9307dc", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5056#discussion_r1123988037"}}
{"title": "Add more internal users and teams", "number": 5057, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5057"}
{"title": "Better version compare", "number": 5058, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5058"}
{"title": "DNS recrod migration and bug fixes", "number": 5059, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5059", "body": "Added the DNS stack to our standard region\nMoved alb CNAME DNS records to standard region (makes it possible to deploy during a DR)\nRemoved the old CNAME records from Dev and Prod\nAdd a conditional to handle missing peering connection (happens during DR setup)"}
{"comment": {"body": "Merging this because I want to avoid accidental reverts. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5059#issuecomment-1447695055"}}
{"title": "Polling only when authenticated", "number": 506, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/506", "body": "Setup polling from data cache stores to only occur when authenticated.\nAlso fixed bug with Sidebar & Auth which was causing unnecessary renders."}
{"comment": {"body": "I'm wondering if we're solving this at the wrong level... I'm not sure if this is necessarily better, but we could just handle this at the lowest-possible level, where if you try to make an API call and no token is available, the API call throws an error (a known error type that higher levels can understand).  Then the ChannelPoller can operate as normal, and if it gets this error it will just skip the response...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/506#issuecomment-1063168169"}}
{"comment": {"body": "> I'm wondering if we're solving this at the wrong level... I'm not sure if this is necessarily better, but we could just handle this at the lowest-possible level, where if you try to make an API call and no token is available, the API call throws an error (a known error type that higher levels can understand). Then the ChannelPoller can operate as normal, and if it gets this error it will just skip the response...\r\n\r\nJust to be clear, I'll leave this up to your judgement, what we have here does look good to me.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/506#issuecomment-1063199419"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/506#pullrequestreview-903704135", "body": ""}
{"comment": {"body": "Fix cross fetch issue where `Response` type was unavailable with runtime crash.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/506#discussion_r822160715"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/506#pullrequestreview-903704972", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/506#pullrequestreview-903713308", "body": ""}
{"title": "enable versioning on more buckets", "number": 5060, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5060", "body": "Enabling bucket versioning on remaining S3 buckets to address a Drata failing test."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5060#pullrequestreview-1318348438", "body": ""}
{"title": "IntelliJ Agent  Webview Communication", "number": 5061, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061", "body": "Setup Agent  Webview communication with existing WebviewContentController & RenderWebview pattern.\nRefactored majority of WebviewContentController & RenderWebview into shared / ide.\nFor IntelliJ, communication between the two flows through Kotlin with a \"TunnelService\"\n"}
{"comment": {"body": "This might be splitting hairs but I have a suggestion to simplify coroutine context management.\r\n\r\n1. Have `ProjectCoroutineService` implement the `CoroutineScope` interface. \r\n2. Provide a `coroutineContext` override like this:\r\n```kotlin\r\noverride val coroutineContext: CoroutineContext\r\n        get() = SupervisorJob() + Dispatchers.IO\r\n```\r\n3. (This step can be skipped) For convenience, create an abstract class that looks like this:\r\n```kotlin\r\nopen class ProjectCoroutineScoped(project: Project) : CoroutineScope {\r\n    private val projectCoroutineService = project.getService(ProjectCoroutineService::class.java)\r\n    override val coroutineContext: CoroutineContext\r\n        get() = projectCoroutineService.coroutineContext\r\n}\r\n```\r\n4. If you did (3), have your classes like `TokenService` simply inherit from the convenience class, otherwise just duplicate what's in the convenience class.\r\n5. Instead of calling `coroutineScope.launch`, now you can just call `launch { }` ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#issuecomment-1452727287"}}
{"comment": {"body": "> This might be splitting hairs but I have a suggestion to simplify coroutine context management.\r\n> \r\n> 1. Have `ProjectCoroutineService` implement the `CoroutineScope` interface.\r\n> 2. Provide a `coroutineContext` override like this:\r\n> \r\n> ```kotlin\r\n> override val coroutineContext: CoroutineContext\r\n>         get() = SupervisorJob() + Dispatchers.IO\r\n> ```\r\n> \r\n>  3. (This step can be skipped) For convenience, create an abstract class that looks like this:\r\n> \r\n> ```kotlin\r\n> open class ProjectCoroutineScoped(project: Project) : CoroutineScope {\r\n>     private val projectCoroutineService = project.getService(ProjectCoroutineService::class.java)\r\n>     override val coroutineContext: CoroutineContext\r\n>         get() = projectCoroutineService.coroutineContext\r\n> }\r\n> ```\r\n> \r\n>  4. If you did (3), have your classes like `TokenService` simply inherit from the convenience class, otherwise just duplicate what's in the convenience class.\r\n> 5. Instead of calling `coroutineScope.launch`, now you can just call `launch { }`\r\n\r\nI'll take a look into this in a subsequent PR. Don't quite follow *how* to use the launch. IntelliJ was complaining.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#issuecomment-1453016313"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318426741", "body": ""}
{"comment": {"body": "Grpc Pipeline that transfers data between Agent + Kotlin", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120671677"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318428185", "body": ""}
{"comment": {"body": "Two way stream that allows Agent to instantiate a WebViewController & eventually a WebViewController to instantiate a Webview.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120672497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318430317", "body": ""}
{"comment": {"body": "Client side helper to define data coming into and out of the agent into the GRPC tunnel.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120673609"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318434101", "body": ""}
{"comment": {"body": "Service coupled with GRPC Webview streams. \r\n\r\nNotifies agent to instantiate WebViewControllers. May need to update this in the future to handle deinit as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120675596"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318435450", "body": ""}
{"comment": {"body": "Manual trigger to launch matching WebViewController in Agent.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120676346"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318439975", "body": ""}
{"comment": {"body": "Service that facilitates transferring raw data through Kotlin between Agent and Webviews.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120678917"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318441528", "body": ""}
{"comment": {"body": "Setup API that allows Webview to send messages back to Kotlin.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120679774"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318443626", "body": ""}
{"comment": {"body": "There is a slight disconnect of when Webview is loaded & this API is setup.\r\n\r\nTherefore, webview will have a message queue that is instantiated locally on load. This will be flushed and sent through the API once setup.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120681018"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318444239", "body": ""}
{"comment": {"body": "Typically used for the Webview's \"Load\" event.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120681379"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318450500", "body": ""}
{"comment": {"body": "WebviewAPI was changed to be an accessor function.\r\nThis is primarily for the IntelliJ implementation where `window.postMessage` is \"updated\" after `RenderWebview` is called.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120685132"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1318451807", "body": ""}
{"comment": {"body": "Majority of this file was pulled from VSCode. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1120685966"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322617708", "body": ""}
{"comment": {"body": "The `outgoing` streams being created *in this class* does not make much sense to me.  This class is the consumer of the outgoing streams, not the producer.\r\n\r\nI would imagine this module either takes in the `outgoing` streams when it is being created, or otherwise pulls the correct streams from whoever is producing the data?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1123694824"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322621331", "body": ""}
{"comment": {"body": "You can see this in how the transferOutgoingStream and sendTunnelPayload methods work -- we have a function just to update the stream that exists within this class.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1123696892"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322623824", "body": ""}
{"comment": {"body": "Do we expect these to be shared between all IDEs?  IE should these commands and view props exist in `/shared/ide/discussion` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1123698436"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322639682", "body": ""}
{"comment": {"body": "This generic approach works as long as Kotlin doesn't need to understand any of the typing and messages (going either way).  I am wondering if it will need to though, in order to take IDE actions.  In that case we might want to split these to be fully typed:\r\n\r\n```\r\nlaunchSidebar(stream SidebarRequest) returns (stream SidebarResponse)\r\nlaunchDiscussion(stream DiscussionRequest) returns (stream DiscussionResponse)\r\n```\r\netc", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1123709613"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322734693", "body": ""}
{"comment": {"body": "Agreed. I think we can revisit this when we actually implement the sidebars.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1123771204"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322735651", "body": ""}
{"comment": {"body": "Potentially. I'd like to do this when I focus on the DiscussionThread itself in another PR. For now, this was used just to test out the communication.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1123772035"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322749153", "body": ""}
{"comment": {"body": "I'm not sure *who* would own these outgoing streams though. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1123780249"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322781371", "body": ""}
{"comment": {"body": "This will probably become more clear as we fill out the implementation for all these views.  Ultimately I would expect that whoever implements a view or piece of functionality will supply the thread when their webview is created.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1123800972"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322798523", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322842124", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322909243", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322943662", "body": ""}
{"comment": {"body": "This makes sense -- there are maybe different ways to handle the timing but this should work fine.  Drop the console logs?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#discussion_r1123911977"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322944223", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1322944786", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5061#pullrequestreview-1323138115", "body": ""}
{"title": "Fix sidebar item context click actions", "number": 5062, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5062", "body": "The state being stored in the row components caused a rerender of the row on each click, causing the menu to rerender and close. Storing the state in the parent components seems to fix this.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5062#pullrequestreview-1318553090", "body": ""}
{"title": "Fix flickering in topic pills", "number": 5063, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5063", "body": "Fixes flickering in the topic pill list in the VSCode explorer insights panel.\nThe topic stream has a mid-stream async operation, to fetch topics for a file, whenever the sourcemarks for a file update.  While this async operation was running the stream state would be loading.\nWith this change, we only use the loading state for the initial load."}
{"comment": {"body": "Thank you!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5063#issuecomment-1448916993"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5063#pullrequestreview-1318469638", "body": ""}
{"title": "Add lock unit tests", "number": 5064, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5064"}
{"title": "chore(deps): update dependency @types/chrome to ^0.0.218", "number": 5065, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5065", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/chrome (source) | ^0.0.217 -> ^0.0.218 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Setup Startup Service", "number": 5066, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5066", "body": "Decouple service initiation from Webviews."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5066#pullrequestreview-1318864950", "body": ""}
{"comment": {"body": "Runs on Project load", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5066#discussion_r1120966898"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5066#pullrequestreview-1322946226", "body": ""}
{"title": "Fix scripts", "number": 5067, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5067"}
{"title": "Sourcemark engine matches moved code faster", "number": 5068, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5068", "body": "This is an accuracy-vs-speed tradeoff. This change aims for speed.\nStill passes the regression test added in #5000."}
{"comment": {"body": "Reran Expo and unblocked benchmarks. Expo is ~10% slower relative to the previous benchmark run, which was ~5 months ago. Seems fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5068#issuecomment-1449371557"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5068#pullrequestreview-1319018181", "body": ""}
{"title": "Visualize user engagement events in admin web", "number": 5069, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5069", "body": "Very basic first attempt at generating an activity timeline. Improve by:\n1. coarse grouping of mulitple events by date\n2. better visuals to make easier to parse. see examples in linear.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5069#pullrequestreview-1319097785", "body": ""}
{"title": "Implement findSourceMarks and getSourceMarkLatestPoint API endpoints", "number": 507, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/507", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/507#pullrequestreview-903707678", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/507#pullrequestreview-903770909", "body": "cool"}
{"title": "Add region env var", "number": 5070, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5070", "body": "Added SERVICE_REGION env var to deployments \nModified GitHub action to supply the region value \nRenamed all environment specific values.yaml files to include region in the file name"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5070#pullrequestreview-1320445622", "body": ""}
{"title": "Drop unused PersonModel columns", "number": 5071, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5071", "body": "Needs https://github.com/NextChapterSoftware/unblocked/pull/5049 to be merged first"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5071#pullrequestreview-1319145095", "body": ""}
{"title": "Use updated hub folder name", "number": 5072, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5072"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5072#pullrequestreview-1320457161", "body": ""}
{"title": "Change deployment strategy for backend services", "number": 5073, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5073", "body": "Slack thread: \n\nModified deployment spec and default values.yaml  to use \"Recreate\" as the default deployment strategy. This is mainly intended for our backend services.\nModified values.yaml file for all front-end services to set deployment strategy to 20% rolling update\nDid the same change for Encryption and Secret service. Even though they are internal they still need to be HA\nAdded affinity configuration to auth service. It was missing one.\nUpdated helm charts with latest changes described above\nRemoved deployment concurrency restriction from GH action deployment workflow\nFixed the region env var. I forgot to supply the value in my last PR"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5073#pullrequestreview-1320501580", "body": ""}
{"comment": {"body": "I didn't want to remove this because it was a pain to find out how to do it. So I just commented it out. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5073#discussion_r1122217892"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5073#pullrequestreview-1320502644", "body": ""}
{"comment": {"body": "Moving forward this is the default deployment strategy unless the deployment config (values.yaml) overrides it. We basically assume every service is a backend service until stated otherwise. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5073#discussion_r1122218605"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5073#pullrequestreview-1320503606", "body": ""}
{"comment": {"body": "This is our old configuration I just moved it up a few levels so we are now specifying them per deployment when needed. (e.g for front-end deployments like API service in this case) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5073#discussion_r1122219297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5073#pullrequestreview-1320601815", "body": ""}
{"title": "Refactor explorer insight streams", "number": 5074, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074", "body": "Moves most of the data handling for explorer insights into /shared/ide/sidebar.\n\nNew FileInsightStream stream, which is the stream of insights for the active file (ie, the top-level stream for the insights panel)\nAdd active file streams to IDEWorkspace\nRemoved CurrentFilePane and RelatedPullRequestsPane and DiffStatRenderer as we don't use them at all."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#pullrequestreview-1320793782", "body": ""}
{"comment": {"body": "This was moved -- it was also heavily changed so I think git didn't recognize it as a move.\r\n\r\nThe main change here is that I removed the diff calculating.  We don't display the diffs anywhere anymore so it was a lot of extra work and code complexity for no benefit.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#discussion_r1122420227"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#pullrequestreview-1320794157", "body": ""}
{"comment": {"body": "This is extracting a few common bits from /vscode as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#discussion_r1122420509"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#pullrequestreview-1320882025", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#pullrequestreview-1320884389", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#pullrequestreview-1320885813", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#pullrequestreview-1320886384", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#pullrequestreview-1322400826", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5074#pullrequestreview-1322849756", "body": ""}
{"title": "Don't record a ContentView event right after an upgrade", "number": 5075, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5075", "body": "Fixes UNB-1033\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5075#pullrequestreview-1320589533", "body": ""}
{"comment": {"body": "The basis for this logic is to replicate the conditions for presenting the `MainContentView`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5075#discussion_r1122276362"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5075#pullrequestreview-1320600336", "body": ""}
{"title": "chore(deps): update dependency nicklockwood/swiftformat to from: \"0.51.0\"", "number": 5076, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5076", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| nicklockwood/SwiftFormat | minor | from: \"0.50.9\" -> from: \"0.51.0\" |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnicklockwood/SwiftFormat\n\n### [`v0.51.0`]()\n\n[Compare Source]()\n\n-   Added `hoistAwait` and `hoistTry` rules to hoist inline `await`/`try` to start of expression\n-   Extended `redundantPattern` rule to remove redundant `let` in patterns\n-   The `wrapMultilineStatementBraces` rules is now applied more consistently\n-   Updated `redundantReturn`/`Closure` rules to support `if`/`switch` expressions in Swift 5.8\n-   Added `conditionalAssignment` rule to assign variables using `if`/`switch` expressions in Swift 5.8\n-   Updated `redundantType` rule to support `if`/`switch` expression assignment Swift 5.8\n-   Extended `redundantSelf` rule to support implicit `self` in eligible closures in Swift 5.8\n-   SwiftFormat now ignores `.swiftformat` files when explicit `--config` file is provided\n-   Added `--wrapenumcases with-values` option to only wrap enum cases with values\n-   Added `--wrapeffects` option for wrapping function effects\n-   Removed unsafe `preferDouble` rule\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "we were not backing up RDS because of a stupid extra param", "number": 5077, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5077", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5077#pullrequestreview-1320589245", "body": ""}
{"title": "Fix main", "number": 5078, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5078"}
{"title": "chore(deps): update public.ecr.aws/lambda/python docker tag to v3.10", "number": 5079, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5079", "body": "This PR contains the following updates:\n| Package | Type | Update | Change |\n|---|---|---|---|\n| public.ecr.aws/lambda/python | final | minor | 3.9 -> 3.10 |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Expose source snippets in API", "number": 508, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/508", "body": "(Sorry about the whitespace changes. New IDE settings updated this, but at least now it's consistent with the rest of the file. To review cleanly, click this link https://github.com/NextChapterSoftware/unblocked/pull/508/files?w=1)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/508#pullrequestreview-*********", "body": ""}
{"title": "Backup Unblocked code to s3", "number": 5080, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5080", "body": "Created a bucket under secops account\nSetup a GH workflow to push a copy of our code to it ever night\n\nTested it and worked like a charm"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5080#pullrequestreview-**********", "body": ""}
{"title": "Fix all TS imports forever", "number": 5081, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5081", "body": "Fix TS imports forever everywhere always.\n\nSet up a lint rule so that we have fixed import grouping and ordering.  We group first by node modules (react), then (@api and @models), then @otherAliases, then @fortawesome, then ./relativePaths, then StyleFiles.scss.\nThis auto-fixes, so Cmd+S in VSCode works automatically.  No more manual import fixing ever.\nnpm run fix-lint will also auto-fix this"}
{"comment": {"body": "How do you invoke auto-fixing in IntelliJ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5081#issuecomment-**********"}}
{"comment": {"body": "> How do you invoke auto-fixing in IntelliJ?\r\n\r\nEnabling Prettier and ESLint in the IntelliJ Ultimate settings seems to work as expected.  I haven't tested it too deeply.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5081#issuecomment-1450969991"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5081#pullrequestreview-1320773636", "body": "You complete me."}
{"title": "Guaranteed QoS for Webhook pods", "number": 5082, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5082", "body": "Setting request = limit resource values will tell Kubernetes that this pod requires guaranteed QoS. This should prevent other pods CPU/Mem bursts taking down Webhook service. \nPrior to adding the new Kube host to prod I saw a bunch of these restarts. We should avoid having Webhook service impacted by any other services."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5082#pullrequestreview-1320748615", "body": "You complete me."}
{"title": "fix(deps): update dependency @floating-ui/react to ^0.20.0", "number": 5083, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5083", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @floating-ui/react (source) | ^0.19.0 -> ^0.20.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nfloating-ui/floating-ui\n\n### [`v0.20.0`]()\n\n[Compare Source]()\n\n#### New Features\n\n-   feat: [`FloatingArrow`]() component ([#2195]())\n\n    Provides the ability to render a customizable `` arrow element within a floating element that is automatically positioned, without needing to setup complex styles.\n\n### [`v0.19.2`]()\n\n[Compare Source]()\n\n#### Bug Fixes\n\n-   fix(useTypeahead): allow typeahead when `currentTarget` contains `target` ([#2173]())\n\n-   fix(types): re-export middleware options types ([#2175]())\n\n-   fix(useTransitionStyles): apply initial styles in `unmounted` status ([#2177]())\n\n    This allows a `transition-delay` prop style to work correctly on the first/initial render.\n\n-   fix(useHover): ensure scrolling away from reference after hovering it closes floating element when `safePolygon` is being used with a close `delay` and the cursor did not move ([#2178]())\n\n-   fix(useHover): prevent floating element from closing when it overlaps the reference element when using `safePolygon` ([#2180]())\n\n-   fix: allow `platform` object to be passed to the `useFloating` hook ([#2176]())\n\n-   fix(types): add `@deprecation` notice to non-ref nested element setters ([#2175]())\n\n### [`v0.19.1`]()\n\n[Compare Source]()\n\n#### Bug Fixes\n\n-   fix: dependency issue with Yarn PnP ([#2157]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Change admin console deployment strategy to rolling update", "number": 5084, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5084", "body": "Moving forward we will tread admin console as a user facing service."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5084#pullrequestreview-**********", "body": ""}
{"title": "[UNB-1151] Hub login buttons", "number": 5085, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085", "body": "Add support for multiple providers.\nCurrently does not support shortcut to \"onboarded\" enterprise providers. Will require Hub to keep a \"cookie\".\n\n\n"}
{"comment": {"body": "@jeffrey-ng the chevron looks too big to me, and I think the text needs to be offset by the width of the icon itself. The text should appear centered, not the entire container. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-**********"}}
{"comment": {"body": "The light mode buttons look way too bright to me. @benedict-jw Should we apply some transparency to those so the background vibrancy pops through just a little?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-**********"}}
{"comment": {"body": "@jeffrey-ng can you please add a video/gif demonstrating expand and collapse?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-**********"}}
{"comment": {"body": "@pwerry the button styles are modelled after the secondary button style. I'll try the transparent style and see how it looks. Can you try that in code or are these custom styled @jeffrey-ng ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1451281414"}}
{"comment": {"body": "Added a bit of opacity. Didn't animate the actual \"opening\" as there were some weird animations with pushing everything up. The current animations are just for the chevron.\r\n\r\n<img width=\"641\" alt=\"CleanShot 2023-03-02 at 09 09 48@2x\" src=\"https://user-images.githubusercontent.com/1553313/222501622-98e5933c-2664-4b5a-be3e-1413e396dfaa.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1452229445"}}
{"comment": {"body": "\r\nhttps://user-images.githubusercontent.com/1553313/222504009-2c653637-50ad-492a-b9b9-2a4a0a4e00a1.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1452237463"}}
{"comment": {"body": "> Added a bit of opacity. Didn't animate the actual \"opening\" as there were some weird animations with pushing everything up. The current animations are just for the chevron.\r\n\r\nI've seen this as well and don't really have a good fix for it. It's a bug in the interplay between SwiftUI and NSPopover's content view\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1452458866"}}
{"comment": {"body": "I think the buttons look much better but as with all things vibrancy it will really depend on what's behind it. Curious what it looks like with a white background?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1452461999"}}
{"comment": {"body": "> I think the buttons look much better but as with all things vibrancy it will really depend on what's behind it. Curious what it looks like with a white background?\r\n\r\n@pwerry This is the secondary system button style I was referring to. \r\n![CleanShot 2023-03-02 at 11 52 52@2x](https://user-images.githubusercontent.com/13353189/222537591-cf403746-7d46-4833-9a2b-8ca5bf4c010a.png)\r\n\r\n\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1452464120"}}
{"comment": {"body": "Haven't been able to find a great recreation of secondary based on system colours.\r\nHere's on a white background.\r\n<img width=\"718\" alt=\"CleanShot 2023-03-02 at 15 00 55@2x\" src=\"https://user-images.githubusercontent.com/1553313/222579558-06f06be1-d998-46e8-a63c-2339ba0f8ec0.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1452689325"}}
{"comment": {"body": "@pwerry @benedict-jw \r\nI gave up. Just copied the static colours for now as I don't want this blocking...\r\n\r\n<img width=\"778\" alt=\"CleanShot 2023-03-24 at 09 18 13@2x\" src=\"https://user-images.githubusercontent.com/1553313/227582673-de3ca23e-26ba-4864-a369-9a93f5d49786.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1483071084"}}
{"comment": {"body": "Hmm we should verify how these static colours will appear in front of various backgrounds, as the popover window has a vibrant material. @pwerry can you assist here? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1483086230"}}
{"comment": {"body": "I feel like I've missed something obvious... does `.secondary` not work as a foreground color for the buttons?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#issuecomment-1504218961"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#pullrequestreview-1320812073", "body": ""}
{"comment": {"body": "Let me know if you think these extensions should be moved... I'm not too sure where to place these atm.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#discussion_r1122432708"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#pullrequestreview-1320958390", "body": ""}
{"comment": {"body": "I'm a big fan of extension containment to where the extension is actually used. And right now it's just `LoginView`, so I think it's fine. As soon as we start making use of this elsewhere we should pull it out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#discussion_r1122544300"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#pullrequestreview-1322518236", "body": ""}
{"comment": {"body": "Why are these state properties?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#discussion_r1123630125"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#pullrequestreview-1322791941", "body": ""}
{"comment": {"body": "Right. No need for state", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#discussion_r1123807714"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5085#pullrequestreview-1381871718", "body": ""}
{"title": "Fix powerml code", "number": 5086, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5086"}
{"title": "Fix PersonModel api bug", "number": 5087, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5087"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5087#pullrequestreview-1320873686", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5087#pullrequestreview-1320873791", "body": ""}
{"title": "changing deployment strategy so rollbacks show as job failures", "number": 5088, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5088", "body": "Changing the deployment strategy to the good old rollover but with a twist. We sett the max surge and max unavailable to 100% to achieve the same effect of a Recreate strategy."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5088#pullrequestreview-1320885790", "body": ""}
{"title": "Add back setting hasSeenTutorial", "number": 5089, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5089", "body": "For now let's keep setting this field, we can remove if we no longer need this field."}
{"title": "Fix Zally lint and treat warnings as errors", "number": 509, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/509", "body": "We keep regressing, so enforce that all of the SHOULD violations are fixed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/509#pullrequestreview-903950658", "body": "MK Ultra level OCD"}
{"title": "Redefine has seen tutorial on person", "number": 5090, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5090", "body": "A person has seen a tutorial if they have seen the tutorial in VSCode or IntelliJ.\nDeprecate updatePersonTutorial, because this is fully covered by updateOnboardingStatus."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5090#pullrequestreview-1320911492", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5090#pullrequestreview-1320911532", "body": ""}
{"title": "Use OAuth refresh flow", "number": 5091, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5091"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5091#pullrequestreview-1322289396", "body": "looks good"}
{"title": "Fix text editor ordering", "number": 5092, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5092", "body": "Follow up to https://github.com/NextChapterSoftware/unblocked/pull/5036\nThe bug described in #5036 only happens when there are no visible text editors. i.e. when there's no currently active text editor to open a Beside column to. So treat these differently depending on whether there are visible text editors.\nAlternatively, if we wanted to truly make the ordering of the editors consistent, we could, in the empty editor case, open an empty tab and backfill the discussion to Beside and the code to Active. Since I think the case where users open discussions with zero active text editors open is rare, this ie likely not necessary for now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5092#pullrequestreview-1322725063", "body": "Looks good."}
{"title": "Add Person.hasSeenTutorial property with tests", "number": 5093, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5093"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5093#pullrequestreview-1320944297", "body": ""}
{"title": "Implements Authorization Interceptor for GRPC service to service auth", "number": 5094, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5094", "body": "Client usage:\nkotlin\n        val hello = helloRequest {\n            auth = authBundle {\n                teamId = team.toString()\n                identity = user.toString()\n                jwt = token\n            }\n            message = \"hi\"\n        }\nMethod authorizer now introspects the received message for the auth bundle and validates its fields against the jwt.\nProjects that make use of lib-service-grpc will need to include the proto definition for auth.proto to import the AuthBundle type."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5094#pullrequestreview-1322963344", "body": ""}
{"title": "Add grpc authorization primitives and service wrappers", "number": 5095, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5095"}
{"title": "Only consider recent client version adoption", "number": 5096, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5096", "body": "Don't care version adoption for clients of users that have churned out."}
{"title": "Fix selected git contributors for video walkthrough", "number": 5097, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5097", "body": "We were sending walkthrough notifications to all contributors... Not just selected."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5097#pullrequestreview-1322395645", "body": ""}
{"title": "Set both hasCreatedWalkthrough flags", "number": 5098, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5098", "body": "Creating a walkthrough isn't specific to either VSCode or IntelliJ so we should set both flags here whenever a walkthrough is created for the first time."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5098#pullrequestreview-1322527509", "body": ""}
{"title": "Make ValueStream initial value behaviour clear", "number": 5099, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5099", "body": "When ValueStream's type is optional the initial value behaviour was a bit ambiguous:\n* If an initial value is set to a defined value, all subscribers would receive that value\n* If an initial value is undefined, new subscribers would not receive anything\n* If updateValue(undefined) was called, existing subscribers would receive a stream update with value of undefined, but new subscribers afterwards would still receive nothing.\nThis fixes the inconsistency.  Add tests.  Also, remove wrapping the current value in an object, and replace the getter with a function instead."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5099#pullrequestreview-1322441973", "body": ""}
{"comment": {"body": "@jeffrey-ng this is the only place that the current value accessor is used.  Is testing this by simply viewing a thread with a video sufficient?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5099#discussion_r1123578633"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5099#pullrequestreview-1322497042", "body": ""}
{"comment": {"body": "Yeah. I would try video & image just to be safe.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5099#discussion_r1123615693"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5099#pullrequestreview-1322790774", "body": ""}
{"title": "Use Exposed ORM and add initial models", "number": 51, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#pullrequestreview-854950208", "body": ""}
{"comment": {"body": "Is a user without at least 1 identity valid?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#discussion_r786418988"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#pullrequestreview-854954165", "body": ""}
{"comment": {"body": "Does this need to be indexed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#discussion_r786421927"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#pullrequestreview-854954493", "body": "Minor nits"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#pullrequestreview-854955311", "body": ""}
{"comment": {"body": "Probably, but let's drive new index creation only when needed. Right now we have no queries.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#discussion_r786422784"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#pullrequestreview-854956706", "body": ""}
{"comment": {"body": "I debated this and realized that an ingested PR conversation would have a bunch of identity-less users. These are users that have never logged into the system. This is if we model all chat members as `UserModel` records.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#discussion_r786423814"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#pullrequestreview-854958025", "body": ""}
{"comment": {"body": "I wonder if chats need `Participant` entities with 1-1 optional mapping to `User`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#discussion_r786424815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#pullrequestreview-854966641", "body": ""}
{"comment": {"body": "If User is tied to immutable PR chats then they can't be deleted", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#discussion_r786431403"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#pullrequestreview-854973376", "body": ""}
{"comment": {"body": "Yeah, all good thoughts. For now you ok to leave as is and defer till we start implementing the Chat models? Easy to change later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#discussion_r786436322"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#pullrequestreview-854974064", "body": ""}
{"comment": {"body": "All good as long as it won't cause us engineering pain in the next few weeks \ud83d\ude42", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/51#discussion_r786436788"}}
{"title": "Video Channel Service", "number": 510, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510", "body": "Summary\nAdd a high level, this PR adds the business logic for Video Channel management. \nNext steps\n\nAgora auth\nBackground job that cleans abandoned channels\nAPI delegate implementation and API  DB model transformers\nRedis channel heartbeats\n\nSee comments for more details."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-903945551", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904700242", "body": ""}
{"comment": {"body": "Moved this out to here because it will eventually pull in a store...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822884856"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904701175", "body": ""}
{"comment": {"body": "Will remove when I implement the delegate", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822885466"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904702842", "body": ""}
{"comment": {"body": "In many places (but not all), it is appropriate to perform a cleanup if there's a dangling channel with no participants. The service should make this state impossible though...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822886633"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904724398", "body": ""}
{"comment": {"body": "This should cascade to participants", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822901591"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904748314", "body": ""}
{"comment": {"body": "Needed because the API Channel model contains list of participants", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822918661"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904751315", "body": ""}
{"comment": {"body": "Channel has changed if any of the participants has changed. Intention is for push to mirror this logic", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822920796"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904754371", "body": ""}
{"comment": {"body": "Trying to create a channel when one already exists but has no participants is an illegal state. Transactions should ensure the creator (host) is added to the channel when it's created.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822923036"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904755729", "body": ""}
{"comment": {"body": "Client was trying to create a channel that they don't own (illegal)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822924060"}}
