{"title": "change instancer type", "number": 4588, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4588"}
{"title": "fix(deps): update luceneversion to v9.5.0", "number": 4589, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4589", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.apache.lucene:lucene-queryparser (source) | 9.4.2 -> 9.5.0 |  |  |  |  |\n| org.apache.lucene:lucene-highlighter (source) | 9.4.2 -> 9.5.0 |  |  |  |  |\n| org.apache.lucene:lucene-core (source) | 9.4.2 -> 9.5.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "No more Ids with capital 'D'", "number": 459, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/459", "body": "While we're at it..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/459#pullrequestreview-898303606", "body": ""}
{"title": "Temporarily disable team member sort", "number": 4590, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4590", "body": "Not working due to unstable comparator function.\n\nUnclear what's happening. Seems to be affecting new teams, or newly backfilled team members."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4590#pullrequestreview-1277682258", "body": ""}
{"title": "Add encryption service", "number": 4591, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#pullrequestreview-1277773845", "body": ""}
{"comment": {"body": "Should we make a different Postgres user for this ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#discussion_r1092311461"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#pullrequestreview-1277774222", "body": ""}
{"comment": {"body": "Same as my other comment for the user ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#discussion_r1092311846"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#pullrequestreview-1277774352", "body": ""}
{"comment": {"body": "We'll change this ove rtime.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#discussion_r1092311941"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#pullrequestreview-1277774539", "body": ""}
{"comment": {"body": "change over time, this is just skeleton.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#discussion_r1092312070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#pullrequestreview-1277775307", "body": "Looks good but a small comment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#pullrequestreview-1277782395", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#pullrequestreview-1277785423", "body": ""}
{"comment": {"body": "I'll look into creating a separate user/role and edit this out of band. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4591#discussion_r1092319526"}}
{"title": "Deprecate FeatureNewExplorerInsightsUI feature flag", "number": 4592, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4592", "body": "Hides it."}
{"title": "Dependabot worklfows", "number": 4593, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4593"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4593#pullrequestreview-1277791744", "body": "nice, thank you!"}
{"title": "Open source test data + results", "number": 4594, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4594", "body": "Put the test data/results in the tree. No prod changes."}
{"title": "chore(deps): update test packages (major)", "number": 4595, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4595", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @testing-library/react | ^12.1.2 -> ^13.0.0 |  |  |  |  |\n| @testing-library/react-hooks | ^7.0.2 -> ^8.0.0 |  |  |  |  |\n| @types/jest (source) | ^27.0.3 -> ^29.0.0 |  |  |  |  |\n| jest (source) | ^27.4.3 -> ^29.0.0 |  |  |  |  |\n| mocha (source) | ^9.2.2 -> ^10.0.0 |  |  |  |  |\n| ts-jest (source) | ^27.1.3 -> ^29.0.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ntesting-library/react-testing-library\n\n### [`v13.4.0`](https://togithub.com/testing-library/react-testing-library/releases/tag/v13.4.0)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v13.3.0...v13.4.0)\n\n##### Features\n\n-   **renderHook:** allow passing of all render options to renderHook ([#1118](https://togithub.com/testing-library/react-testing-library/issues/1118)) ([27a9584](https://togithub.com/testing-library/react-testing-library/commit/27a9584629e28339b9961edefbb2134d7c570678))\n\n### [`v13.3.0`](https://togithub.com/testing-library/react-testing-library/releases/tag/v13.3.0)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v13.2.0...v13.3.0)\n\n##### Features\n\n-   Use `globalThis` if available ([#1070](https://togithub.com/testing-library/react-testing-library/issues/1070)) ([c80809a](https://togithub.com/testing-library/react-testing-library/commit/c80809a956b0b9f3289c4a6fa8b5e8cc72d6ef6d))\n\n### [`v13.2.0`](https://togithub.com/testing-library/react-testing-library/releases/tag/v13.2.0)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v13.1.1...v13.2.0)\n\n##### Features\n\n-   Export RenderHookOptions type ([#1062](https://togithub.com/testing-library/react-testing-library/issues/1062)) ([46b28ad](https://togithub.com/testing-library/react-testing-library/commit/46b28ade730f97a49a253d630f5b97c17ff24f6e))\n\n### [`v13.1.1`](https://togithub.com/testing-library/react-testing-library/releases/tag/v13.1.1)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v13.1.0...v13.1.1)\n\n##### Bug Fixes\n\n-   **TS:** export interface RenderHookResult ([#1049](https://togithub.com/testing-library/react-testing-library/issues/1049)) ([9171163](https://togithub.com/testing-library/react-testing-library/commit/9171163fccf0a7ea43763475ca2980898b4079a5))\n\n### [`v13.1.0`](https://togithub.com/testing-library/react-testing-library/releases/tag/v13.1.0)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v13.0.1...v13.1.0)\n\n##### Features\n\n-   Add `renderHook` ([#991](https://togithub.com/testing-library/react-testing-library/issues/991)) ([9535eff](https://togithub.com/testing-library/react-testing-library/commit/9535eff82ada685c410b3b25ef3e2313ea3a86aa))\n\n### [`v13.0.1`](https://togithub.com/testing-library/react-testing-library/releases/tag/v13.0.1)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v13.0.0...v13.0.1)\n\n##### Bug Fixes\n\n-   Specify a non-\\* version for [@types/react-dom](https://togithub.com/types/react-dom) ([#1040](https://togithub.com/testing-library/react-testing-library/issues/1040)) ([2a889e8](https://togithub.com/testing-library/react-testing-library/commit/2a889e80658ce93882c5ba253ea65f5542ece2d0))\n\n### [`v13.0.0`](https://togithub.com/testing-library/react-testing-library/releases/tag/v13.0.0)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v12.1.5...v13.0.0)\n\n##### Features\n\n-   Add support for React 18 ([#1031](https://togithub.com/testing-library/react-testing-library/issues/1031)) ([ccd8a0d](https://togithub.com/testing-library/react-testing-library/commit/ccd8a0d97dd9da0a420f2cf012a24d414d1646ed))\n\n##### BREAKING CHANGES\n\n-   Drop support for React 17 and earlier. We'll use the new [`createRoot` API](https://togithub.com/reactwg/react-18/discussions/5) by default which comes with a set of [changes while also enabling support for concurrent features](https://togithub.com/reactwg/react-18/discussions/4).\n    To opt-out of this change you can use `render(ui, { legacyRoot: true } )`. But be aware that the legacy root API is deprecated in React 18 and its usage will trigger console warnings.\n\n### [`v12.1.5`](https://togithub.com/testing-library/react-testing-library/releases/tag/v12.1.5)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v12.1.4...v12.1.5)\n\n##### Bug Fixes\n\n-   Only supports React < 18 ([#1041](https://togithub.com/testing-library/react-testing-library/issues/1041)) ([9e2b5db](https://togithub.com/testing-library/react-testing-library/commit/9e2b5dbb4632799ae38f1341cb79ef35d1bd6652))\n\n### [`v12.1.4`](https://togithub.com/testing-library/react-testing-library/releases/tag/v12.1.4)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v12.1.3...v12.1.4)\n\n##### Bug Fixes\n\n-   Match runtime type of baseElement in TypeScript types ([#1023](https://togithub.com/testing-library/react-testing-library/issues/1023)) ([96ed8da](https://togithub.com/testing-library/react-testing-library/commit/96ed8dafa5d02add2168a3da65d1cc0ffe6d6d1f))\n\n### [`v12.1.3`](https://togithub.com/testing-library/react-testing-library/releases/tag/v12.1.3)\n\n[Compare Source](https://togithub.com/testing-library/react-testing-library/compare/v12.1.2...v12.1.3)\n\n##### Bug Fixes\n\n-   Add `@types/react-dom` as a direct dependency ([#1001](https://togithub.com/testing-library/react-testing-library/issues/1001)) ([149d9a9](https://togithub.com/testing-library/react-testing-library/commit/149d9a9af3addeb6c49696867b05b87afe0d0b3c))\n\n\n\n\ntesting-library/react-hooks-testing-library\n\n### [`v8.0.1`](https://togithub.com/testing-library/react-hooks-testing-library/releases/tag/v8.0.1)\n\n[Compare Source](https://togithub.com/testing-library/react-hooks-testing-library/compare/v8.0.0...v8.0.1)\n\n##### Bug Fixes\n\n-   **server:** remove act around server renderer to fix support for older versions of react ([e2461ca](https://togithub.com/testing-library/react-hooks-testing-library/commit/e2461ca4b5ab45813527f5e2478c4e8552f16c51)), closes [#607](https://togithub.com/testing-library/react-hooks-testing-library/issues/607)\n\n### [`v8.0.0`](https://togithub.com/testing-library/react-hooks-testing-library/releases/tag/v8.0.0)\n\n[Compare Source](https://togithub.com/testing-library/react-hooks-testing-library/compare/v7.0.2...v8.0.0)\n\n##### Bug Fixes\n\n-   **types:** move types to optional peer dependencies ([19ac8dd](https://togithub.com/testing-library/react-hooks-testing-library/commit/19ac8dde5c16f53d963277543997fa7a7ffd5fe4))\n\n##### BREAKING CHANGES\n\n-   **types:** type dependencies will not longer be automatically installed.  If `@types/react` is not already listed in your package.json, please install it with `npm install --save-dev @types/react@^17`.\n\n\n\n\nfacebook/jest\n\n### [`v29.4.1`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2941)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.4.0...v29.4.1)\n\n##### Features\n\n-   `[expect, jest-circus, @jest/types]` Implement `numPassingAsserts` of testResults to track the number of passing asserts in a test ([#13795](https://togithub.com/facebook/jest/pull/13795))\n-   `[jest-core]` Add newlines to JSON output ([#13817](https://togithub.com/facebook/jest/pull/13817))\n-   `[@jest/reporters]` Automatic log folding in GitHub Actions Reporter ([#13626](https://togithub.com/facebook/jest/pull/13626))\n\n##### Fixes\n\n-   `[@jest/expect-utils]` `toMatchObject` diffs should include `Symbol` properties ([#13810](https://togithub.com/facebook/jest/pull/13810))\n-   `[jest-runtime]` Handle missing `replaceProperty` ([#13823](https://togithub.com/facebook/jest/pull/13823))\n-   `[@jest/types]` Add partial support for `done` callbacks in typings of `each` ([#13756](https://togithub.com/facebook/jest/pull/13756))\n\n### [`v29.4.0`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2940)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.3.1...v29.4.0)\n\n##### Features\n\n-   `[expect, @jest/expect-utils]` Support custom equality testers ([#13654](https://togithub.com/facebook/jest/pull/13654))\n-   `[jest-config, jest-worker]` Use `os.availableParallelism` if available to calculate number of workers to spawn ([#13738](https://togithub.com/facebook/jest/pull/13738))\n-   `[@jest/globals, jest-mock]` Add `jest.replaceProperty()` that replaces property value ([#13496](https://togithub.com/facebook/jest/pull/13496))\n-   `[jest-haste-map]` ignore Sapling vcs directories (`.sl/`) ([#13674](https://togithub.com/facebook/jest/pull/13674))\n-   `[jest-resolve]` Support subpath imports ([#13705](https://togithub.com/facebook/jest/pull/13705), [#13723](https://togithub.com/facebook/jest/pull/13723), [#13777](https://togithub.com/facebook/jest/pull/13777))\n-   `[jest-runtime]` Add `jest.isolateModulesAsync` for scoped module initialization of asynchronous functions ([#13680](https://togithub.com/facebook/jest/pull/13680))\n-   `[jest-runtime]` Add `jest.isEnvironmentTornDown` function ([#13741](https://togithub.com/facebook/jest/pull/13741))\n-   `[jest-test-result]` Added `skipped` and `focused` status to `FormattedTestResult` ([#13700](https://togithub.com/facebook/jest/pull/13700))\n-   `[jest-transform]` Support for asynchronous `createTransformer` ([#13762](https://togithub.com/facebook/jest/pull/13762))\n\n##### Fixes\n\n-   `[jest-environment-node]` Fix non-configurable globals ([#13687](https://togithub.com/facebook/jest/pull/13687))\n-   `[@jest/expect-utils]` `toMatchObject` should handle `Symbol` properties ([#13639](https://togithub.com/facebook/jest/pull/13639))\n-   `[jest-mock]` Fix `mockReset` and `resetAllMocks` `undefined` return value([#13692](https://togithub.com/facebook/jest/pull/13692))\n-   `[jest-resolve]` Add global paths to `require.resolve.paths` ([#13633](https://togithub.com/facebook/jest/pull/13633))\n-   `[jest-resolve]` Correct node core module detection when using `node:` specifiers ([#13806](https://togithub.com/facebook/jest/pull/13806))\n-   `[jest-runtime]` Support WASM files that import JS resources ([#13608](https://togithub.com/facebook/jest/pull/13608))\n-   `[jest-runtime]` Use the `scriptTransformer` cache in `jest-runner` ([#13735](https://togithub.com/facebook/jest/pull/13735))\n-   `[jest-runtime]` Enforce import assertions when importing JSON in ESM ([#12755](https://togithub.com/facebook/jest/pull/12755) & [#13805](https://togithub.com/facebook/jest/pull/13805))\n-   `[jest-snapshot]` Make sure to import `babel` outside of the sandbox ([#13694](https://togithub.com/facebook/jest/pull/13694))\n-   `[jest-transform]` Ensure the correct configuration is passed to preprocessors specified multiple times in the `transform` option ([#13770](https://togithub.com/facebook/jest/pull/13770))\n\n##### Chore & Maintenance\n\n-   `[@jest/fake-timers]` Update `@sinonjs/fake-timers` ([#13612](https://togithub.com/facebook/jest/pull/13612))\n-   `[docs]` Improve custom puppeteer example to prevent worker warnings ([#13619](https://togithub.com/facebook/jest/pull/13619))\n\n### [`v29.3.1`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2931)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.3.0...v29.3.1)\n\n##### Fixes\n\n-   `[jest-config]` Do not warn about `preset` in `ProjectConfig` ([#13583](https://togithub.com/facebook/jest/pull/13583))\n\n##### Performance\n\n-   `[jest-transform]` Defer creation of cache directory ([#13420](https://togithub.com/facebook/jest/pull/13420))\n\n### [`v29.3.0`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2930)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.2.2...v29.3.0)\n\n##### Features\n\n-   `[jest-runtime]` Support WebAssembly (Wasm) imports in ESM modules ([#13505](https://togithub.com/facebook/jest/pull/13505))\n\n##### Fixes\n\n-   `[jest-config]` Add config validation for `projects` option ([#13565](https://togithub.com/facebook/jest/pull/13565))\n-   `[jest-mock]` Treat cjs modules as objects so they can be mocked ([#13513](https://togithub.com/facebook/jest/pull/13513))\n-   `[jest-worker]` Throw an error instead of hanging when jest workers terminate unexpectedly ([#13566](https://togithub.com/facebook/jest/pull/13566))\n\n##### Chore & Maintenance\n\n-   `[@jest/transform]` Update `convert-source-map` ([#13509](https://togithub.com/facebook/jest/pull/13509))\n-   `[docs]` Mention `toStrictEqual` in UsingMatchers docs. ([#13560](https://togithub.com/facebook/jest/pull/13560))\n\n### [`v29.2.2`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2922)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.2.1...v29.2.2)\n\n##### Fixes\n\n-   `[@jest/test-sequencer]` Make sure sharding does not produce empty groups ([#13476](https://togithub.com/facebook/jest/pull/13476))\n-   `[jest-circus]` Test marked as `todo` are shown as todo when inside a focussed describe ([#13504](https://togithub.com/facebook/jest/pull/13504))\n-   `[jest-mock]` Ensure mock resolved and rejected values are promises from correct realm ([#13503](https://togithub.com/facebook/jest/pull/13503))\n-   `[jest-snapshot]` Don't highlight passing asymmetric property matchers in snapshot diff ([#13480](https://togithub.com/facebook/jest/issues/13480))\n\n##### Chore & Maintenance\n\n-   `[docs]` Update link to Jest 28 upgrade guide in error message ([#13483](https://togithub.com/facebook/jest/pull/13483))\n-   `[jest-runner, jest-watcher]` Update `emittery` ([#13490](https://togithub.com/facebook/jest/pull/13490))\n\n### [`v29.2.1`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2921)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.2.0...v29.2.1)\n\n##### Features\n\n-   `[@jest/globals, jest-mock]` Add `jest.Spied*` utility types ([#13440](https://togithub.com/facebook/jest/pull/13440))\n\n##### Fixes\n\n-   `[jest-environment-node]` make `globalThis.performance` writable for Node 19 and fake timers ([#13467](https://togithub.com/facebook/jest/pull/13467))\n-   `[jest-mock]` Revert [#13398](https://togithub.com/facebook/jest/pull/13398) to restore mocking of setters ([#13472](https://togithub.com/facebook/jest/pull/13472))\n\n##### Performance\n\n-   `[*]` Use sha1 instead of sha256 for hashing ([#13421](https://togithub.com/facebook/jest/pull/13421))\n\n### [`v29.2.0`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2920)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.1.2...v29.2.0)\n\n##### Features\n\n-   `[@jest/cli, jest-config]` A seed for the test run will be randomly generated, or set by a CLI option ([#13400](https://togithub.com/facebook/jest/pull/13400))\n-   `[@jest/cli, jest-config]` `--show-seed` will display the seed value in the report, and can be set via a CLI flag or through the config file ([#13400](https://togithub.com/facebook/jest/pull/13400))\n-   `[jest-config]` Add `readInitialConfig` utility function ([#13356](https://togithub.com/facebook/jest/pull/13356))\n-   `[jest-core]` Allow `testResultsProcessor` to be async ([#13343](https://togithub.com/facebook/jest/pull/13343))\n-   `[@jest/environment, jest-environment-node, jest-environment-jsdom, jest-runtime]` Add `getSeed()` to the `jest` object ([#13400](https://togithub.com/facebook/jest/pull/13400))\n-   `[expect, @jest/expect-utils]` Allow `isA` utility to take a type argument ([#13355](https://togithub.com/facebook/jest/pull/13355))\n-   `[expect]` Expose `AsyncExpectationResult` and `SyncExpectationResult` types ([#13411](https://togithub.com/facebook/jest/pull/13411))\n\n##### Fixes\n\n-   `[babel-plugin-jest-hoist]` Ignore `TSTypeQuery` when checking for hoisted references ([#13367](https://togithub.com/facebook/jest/pull/13367))\n-   `[jest-core]` Fix `detectOpenHandles` false positives for some special objects such as `TLSWRAP` ([#13414](https://togithub.com/facebook/jest/pull/13414))\n-   `[jest-mock]` Fix mocking of getters and setters on classes ([#13398](https://togithub.com/facebook/jest/pull/13398))\n-   `[jest-reporters]` Revert: Transform file paths into hyperlinks ([#13399](https://togithub.com/facebook/jest/pull/13399))\n-   `[@jest/types]` Infer type of `each` table correctly when the table is a tuple or array ([#13381](https://togithub.com/facebook/jest/pull/13381))\n-   `[@jest/types]` Rework typings to allow the `*ReturnedWith` matchers to be called with no argument ([#13385](https://togithub.com/facebook/jest/pull/13385))\n\n##### Chore & Maintenance\n\n-   `[*]` Update `@babel/*` deps, resulting in slightly different stack traces for `each` ([#13422](https://togithub.com/facebook/jest/pull/13422))\n\n##### Performance\n\n-   `[jest-runner]` Do not instrument v8 coverage data if coverage should not be collected ([#13282](https://togithub.com/facebook/jest/pull/13282))\n\n### [`v29.1.2`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2912)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.1.1...v29.1.2)\n\n##### Fixes\n\n-   `[expect, @jest/expect]` Revert buggy inference of argument types for `*CalledWith` and `*ReturnedWith` matchers introduced in 29.1.0 ([#13339](https://togithub.com/facebook/jest/pull/13339))\n-   `[jest-worker]` Add missing dependency on `jest-util` ([#13341](https://togithub.com/facebook/jest/pull/13341))\n\n### [`v29.1.1`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2911)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.1.0...v29.1.1)\n\n##### Fixes\n\n-   `[jest-mock]` Revert [#13145](https://togithub.com/facebook/jest/pull/13145) which broke mocking of transpiled ES modules\n\n### [`v29.1.0`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2910)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.0.3...v29.1.0)\n\n##### Features\n\n-   `[expect, @jest/expect]` Support type inference for function parameters in `CalledWith` assertions ([#13268](https://togithub.com/facebook/jest/pull/13268))\n-   `[expect, @jest/expect]` Infer type of `*ReturnedWith` matchers argument ([#13278](https://togithub.com/facebook/jest/pull/13278))\n-   `[@jest/environment, jest-runtime]` Allow `jest.requireActual` and `jest.requireMock` to take a type argument ([#13253](https://togithub.com/facebook/jest/pull/13253))\n-   `[@jest/environment]` Allow `jest.mock` and `jest.doMock` to take a type argument ([#13254](https://togithub.com/facebook/jest/pull/13254))\n-   `[@jest/fake-timers]` Add `jest.now()` to return the current fake clock time ([#13244](https://togithub.com/facebook/jest/pull/13244), [#13246](https://togithub.com/facebook/jest/pull/13246))\n-   `[@jest/mock]` Add `withImplementation` method for temporarily overriding a mock ([#13281](https://togithub.com/facebook/jest/pull/13281))\n-   `[expect]` Export `toThrow*` matchers ([#13328](https://togithub.com/facebook/jest/pull/13328))\n\n##### Fixes\n\n-   `[jest-circus, jest-jasmine2]` Fix error messages for Node's `assert.throes` ([#13322](https://togithub.com/facebook/jest/pull/13322))\n-   `[jest-haste-map]` Remove `__proto__` usage ([#13256](https://togithub.com/facebook/jest/pull/13256))\n-   `[jest-mock]` Improve `spyOn` typings to handle optional properties ([#13247](https://togithub.com/facebook/jest/pull/13247))\n-   `[jest-mock]` Fix mocking of getters and setters on classes ([#13145](https://togithub.com/facebook/jest/pull/13145))\n-   `[jest-snapshot]` Throw useful error when an array is passed as property matchers ([#13263](https://togithub.com/facebook/jest/pull/13263))\n-   `[jest-snapshot]` Prioritize parser used in the project ([#13323](https://togithub.com/facebook/jest/pull/13323))\n-   `[jest-transform]` Attempt to work around issues with atomic writes on Windows ([#11423](https://togithub.com/facebook/jest/pull/11423))\n\n### [`v29.0.3`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2903)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.0.2...v29.0.3)\n\n##### Features\n\n-   `[@jest/environment, jest-runtime]` Allow passing a generic type argument to `jest.createMockFromModule()` method ([#13202](https://togithub.com/facebook/jest/pull/13202))\n-   `[expect]` Expose `ExpectationResult` type ([#13240](https://togithub.com/facebook/jest/pull/13240))\n-   `[jest-snapshot]` Expose `Context` type ([#13240](https://togithub.com/facebook/jest/pull/13240))\n-   `[@jest/globals]` Add `jest.Mock` type helper ([#13235](https://togithub.com/facebook/jest/pull/13235))\n\n##### Fixes\n\n-   `[jest-core]` Capture `execError` during `TestScheduler.scheduleTests` and dispatch to reporters ([#13203](https://togithub.com/facebook/jest/pull/13203))\n-   `[jest-resolve]` Make sure to resolve module paths after looking at `exports` ([#13242](https://togithub.com/facebook/jest/pull/13242))\n-   `[jest-resolve]` Improve error on module not found deep in the `require` stack ([#8704](https://togithub.com/facebook/jest/pull/8704))\n-   `[jest-snapshot]` Fix typings of snapshot matchers ([#13240](https://togithub.com/facebook/jest/pull/13240))\n\n##### Chore & Maintenance\n\n-   `[*]` Fix inconsistent workspace prefixes ([#13217](https://togithub.com/facebook/jest/pull/13217))\n-   `[jest-haste-map]` Expose a minimal public API to TypeScript ([#13023](https://togithub.com/facebook/jest/pull/13023))\n\n### [`v29.0.2`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2902)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.0.1...v29.0.2)\n\n##### Features\n\n-   `[jest-transform]` Expose `TransformFactory` type ([#13184](https://togithub.com/facebook/jest/pull/13184))\n\n##### Fixes\n\n-   `[babel-plugin-jest-hoist]` Support imported `jest` in mock factory ([#13188](https://togithub.com/facebook/jest/pull/13188))\n-   `[jest-mock]` Align the behavior and return type of `generateFromMetadata` method ([#13207](https://togithub.com/facebook/jest/pull/13207))\n-   `[jest-runtime]` Support `jest.resetModules()` with ESM ([#13211](https://togithub.com/facebook/jest/pull/13211))\n\n### [`v29.0.1`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2901)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.0.0...v29.0.1)\n\n##### Fixes\n\n-   `[jest-snapshot]` Pass `snapshotFormat` through when diffing snapshots ([#13181](https://togithub.com/facebook/jest/pull/13181))\n\n### [`v29.0.0`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2900)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v28.1.3...v29.0.0)\n\n##### Features\n\n-   `[expect]` \\[**BREAKING**] Differentiate between `MatcherContext` `MatcherUtils` and `MatcherState` types ([#13141](https://togithub.com/facebook/jest/pull/13141))\n-   `[jest-circus]` Add support for `test.failing.each` ([#13142](https://togithub.com/facebook/jest/pull/13142))\n-   `[jest-config]` \\[**BREAKING**] Make `snapshotFormat` default to `escapeString: false` and `printBasicPrototype: false` ([#13036](https://togithub.com/facebook/jest/pull/13036))\n-   `[jest-config]` \\[**BREAKING**] Remove undocumented `collectCoverageOnlyFrom` option ([#13156](https://togithub.com/facebook/jest/pull/13156))\n-   `[jest-environment-jsdom]` \\[**BREAKING**] Upgrade to `jsdom@20` ([#13037](https://togithub.com/facebook/jest/pull/13037), [#13058](https://togithub.com/facebook/jest/pull/13058))\n-   `[@jest/globals]` Add `jest.Mocked`, `jest.MockedClass`, `jest.MockedFunction` and `jest.MockedObject` utility types ([#12727](https://togithub.com/facebook/jest/pull/12727))\n-   `[jest-mock]` \\[**BREAKING**] Refactor `Mocked*` utility types. `MaybeMockedDeep` and `MaybeMocked` became `Mocked` and `MockedShallow` respectively; only deep mocked variants of `MockedClass`, `MockedFunction` and `MockedObject` are exported ([#13123](https://togithub.com/facebook/jest/pull/13123), [#13124](https://togithub.com/facebook/jest/pull/13124))\n-   `[jest-mock]` \\[**BREAKING**] Change the default `jest.mocked` helpers behavior to deep mocked ([#13125](https://togithub.com/facebook/jest/pull/13125))\n-   `[jest-snapshot]` \\[**BREAKING**] Let `babel` find config when updating inline snapshots ([#13150](https://togithub.com/facebook/jest/pull/13150))\n-   `[@jest/test-result, @jest/types]` \\[**BREAKING**] Replace `Bytes` and `Milliseconds` types with `number` ([#13155](https://togithub.com/facebook/jest/pull/13155))\n-   `[jest-worker]` Adds `workerIdleMemoryLimit` option which is used as a check for worker memory leaks >= Node 16.11.0 and recycles child workers as required ([#13056](https://togithub.com/facebook/jest/pull/13056), [#13105](https://togithub.com/facebook/jest/pull/13105), [#13106](https://togithub.com/facebook/jest/pull/13106), [#13107](https://togithub.com/facebook/jest/pull/13107))\n-   `[pretty-format]` \\[**BREAKING**] Remove `ConvertAnsi` plugin in favour of `jest-serializer-ansi-escapes` ([#13040](https://togithub.com/facebook/jest/pull/13040))\n-   `[pretty-format]` Allow to opt out from sorting object keys with `compareKeys: null` ([#12443](https://togithub.com/facebook/jest/pull/12443))\n\n##### Fixes\n\n-   `[jest-config]` Fix testing multiple projects with TypeScript config files ([#13099](https://togithub.com/facebook/jest/pull/13099))\n-   `[@jest/expect-utils]` Fix deep equality of ImmutableJS Record ([#13055](https://togithub.com/facebook/jest/pull/13055))\n-   `[jest-haste-map]` Increase the maximum possible file size that jest-haste-map can handle ([#13094](https://togithub.com/facebook/jest/pull/13094))\n-   `[jest-runtime]` Properly support CJS re-exports from dual packages ([#13170](https://togithub.com/facebook/jest/pull/13170))\n-   `[jest-snapshot]` Make `prettierPath` optional in `SnapshotState` ([#13149](https://togithub.com/facebook/jest/pull/13149))\n-   `[jest-snapshot]` Fix parsing error from inline snapshot files with `JSX` ([#12760](https://togithub.com/facebook/jest/pull/12760))\n-   `[jest-worker]` When a process runs out of memory worker exits correctly and doesn't spin indefinitely ([#13054](https://togithub.com/facebook/jest/pull/13054))\n\n##### Chore & Maintenance\n\n-   `[*]` \\[**BREAKING**] Drop support for Node v12 and v17 ([#13033](https://togithub.com/facebook/jest/pull/13033))\n-   `[docs]` Fix webpack name ([#13049](https://togithub.com/facebook/jest/pull/13049))\n-   `[docs]` Explicit how to set `n` for `--bail` ([#13128](https://togithub.com/facebook/jest/pull/13128))\n-   `[docs]` Update Enzyme URL ([#13166](https://togithub.com/facebook/jest/pull/13166))\n-   `[jest-leak-detector]` Remove support for `weak-napi` ([#13035](https://togithub.com/facebook/jest/pull/13035))\n-   `[jest-snapshot]` \\[**BREAKING**] Require `rootDir` as argument to `SnapshotState` ([#13150](https://togithub.com/facebook/jest/pull/13150))\n\n### [`v28.1.3`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2813)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v28.1.2...v28.1.3)\n\n##### Features\n\n-   `[jest-leak-detector]` Use native `FinalizationRegistry` when it exists to get rid of external C dependency ([#12973](https://togithub.com/facebook/jest/pull/12973))\n\n##### Fixes\n\n-   `[jest-changed-files]` Fix a lock-up after repeated invocations ([#12757](https://togithub.com/facebook/jest/issues/12757))\n-   `[@jest/expect-utils]` Fix deep equality of ImmutableJS OrderedSets ([#12977](https://togithub.com/facebook/jest/pull/12977))\n-   `[jest-mock]` Add index signature support for `spyOn` types ([#13013](https://togithub.com/facebook/jest/pull/13013), [#13020](https://togithub.com/facebook/jest/pull/13020))\n-   `[jest-snapshot]` Fix indentation of awaited inline snapshots ([#12986](https://togithub.com/facebook/jest/pull/12986))\n\n##### Chore & Maintenance\n\n-   `[*]` Replace internal usage of `pretty-format/ConvertAnsi` with `jest-serializer-ansi-escapes` ([#12935](https://togithub.com/facebook/jest/pull/12935), [#13004](https://togithub.com/facebook/jest/pull/13004))\n-   `[docs]` Update spyOn docs ([#13000](https://togithub.com/facebook/jest/pull/13000))\n\n### [`v28.1.2`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2812)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v28.1.1...v28.1.2)\n\n##### Fixes\n\n-   `[jest-runtime]` Avoid star type import from `@jest/globals` ([#12949](https://togithub.com/facebook/jest/pull/12949))\n\n##### Chore & Maintenance\n\n-   `[docs]` Mention that jest-codemods now supports Sinon ([#12898](https://togithub.com/facebook/jest/pull/12898))\n\n### [`v28.1.1`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2811)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v28.1.0...v28.1.1)\n\n##### Features\n\n-   `[jest]` Expose `Config` type ([#12848](https://togithub.com/facebook/jest/pull/12848))\n-   `[@jest/reporters]` Improve `GitHubActionsReporter`s annotation format ([#12826](https://togithub.com/facebook/jest/pull/12826))\n-   `[@jest/types]` Infer argument types passed to `test` and `describe` callback functions from `each` tables ([#12885](https://togithub.com/facebook/jest/pull/12885), [#12905](https://togithub.com/facebook/jest/pull/12905))\n\n##### Fixes\n\n-   `[@jest/expect-utils]` Fix deep equality of ImmutableJS OrderedMaps ([#12763](https://togithub.com/facebook/jest/pull/12899))\n-   `[jest-docblock]` Handle multiline comments in parseWithComments ([#12845](https://togithub.com/facebook/jest/pull/12845))\n-   `[jest-mock]` Improve `spyOn` error messages ([#12901](https://togithub.com/facebook/jest/pull/12901))\n-   `[jest-runtime]` Correctly report V8 coverage with `resetModules: true` ([#12912](https://togithub.com/facebook/jest/pull/12912))\n-   `[jest-worker]` Make `JestWorkerFarm` helper type to include methods of worker module that take more than one argument ([#12839](https://togithub.com/facebook/jest/pull/12839))\n\n##### Chore & Maintenance\n\n-   `[docs]` Updated docs to indicate that `jest-environment-jsdom` is a separate package [#12828](https://togithub.com/facebook/jest/issues/12828)\n-   `[docs]` Document the comments used by coverage providers [#12835](https://togithub.com/facebook/jest/issues/12835)\n-   `[docs]` Use `docusaurus-remark-plugin-tab-blocks` to format tabs with code examples ([#12859](https://togithub.com/facebook/jest/pull/12859))\n-   `[jest-haste-map]` Bump `walker` version ([#12324](https://togithub.com/facebook/jest/pull/12324))\n\n### [`v28.1.0`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2810)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v28.0.3...v28.1.0)\n\n##### Features\n\n-   `[jest-circus]` Add `failing` test modifier that inverts the behavior of tests ([#12610](https://togithub.com/facebook/jest/pull/12610))\n-   `[jest-environment-node, jest-environment-jsdom]` Allow specifying `customExportConditions` ([#12774](https://togithub.com/facebook/jest/pull/12774))\n\n##### Fixes\n\n-   `[expect]` Adjust typings of `lastCalledWith`, `nthCalledWith`, `toBeCalledWith` matchers to allow a case there a mock was called with no arguments ([#12807](https://togithub.com/facebook/jest/pull/12807))\n-   `[@jest/expect-utils]` Fix deep equality of ImmutableJS Lists ([#12763](https://togithub.com/facebook/jest/pull/12763))\n-   `[jest-core]` Do not collect `SIGNREQUEST` as open handles ([#12789](https://togithub.com/facebook/jest/pull/12789))\n\n##### Chore & Maintenance\n\n-   `[docs]` Specified documentation about `--filter` CLI docs ([#12799](https://togithub.com/facebook/jest/pull/12799))\n-   `[@jest-reporters]` Move helper functions from `utils.ts` into separate files ([#12782](https://togithub.com/facebook/jest/pull/12782))\n-   `[jest-resolve]` Replace `process.versions.pnp` type declaration with `@types/pnpapi` devDependency ([#12783](https://togithub.com/facebook/jest/pull/12783))\n\n### [`v28.0.3`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2803)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v28.0.2...v28.0.3)\n\n##### Fixes\n\n-   `[jest-config]` Normalize `reporters` option defined in presets ([#12769](https://togithub.com/facebook/jest/pull/12769))\n-   `[@jest/reporters]` Fix trailing slash in matching `coverageThreshold` key ([#12714](https://togithub.com/facebook/jest/pull/12714))\n-   `[jest-resolve]` Fix (experimental) ESM module mocking for re-exports ([#12766](https://togithub.com/facebook/jest/pull/12766))\n-   `[@jest/transform]` Throw better error if an invalid return value if encountered ([#12764](https://togithub.com/facebook/jest/pull/12764))\n\n##### Chore & Maintenance\n\n-   `[docs]` Fix typo in `--shard` CLI docs ([#12761](https://togithub.com/facebook/jest/pull/12761))\n\n### [`v28.0.2`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2802)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v28.0.1...v28.0.2)\n\n##### Features\n\n-   `[jest-worker]` Add `JestWorkerFarm` helper type ([#12753](https://togithub.com/facebook/jest/pull/12753))\n\n##### Fixes\n\n-   `[*]` Lower Node 16 requirement to 16.10 from 16.13 due to a [Node bug](https://togithub.com/nodejs/node/issues/40014) that causes memory and performance issues ([#12754](https://togithub.com/facebook/jest/pull/12754))\n\n### [`v28.0.1`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2801)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v28.0.0...v28.0.1)\n\n##### Features\n\n-   `[jest-resolve]` Expose `ResolverOptions` type ([#12736](https://togithub.com/facebook/jest/pull/12736))\n\n##### Fixes\n\n-   `[expect]` Add missing dependency `jest-util` ([#12744](https://togithub.com/facebook/jest/pull/12744))\n-   `[jest-circus]` Improve `test.concurrent` ([#12748](https://togithub.com/facebook/jest/pull/12748))\n-   `[jest-resolve]` Correctly throw an error if `jsdom` test environment is used, but not installed ([#12749](https://togithub.com/facebook/jest/pull/12749))\n\n##### Chore & Maintenance\n\n-   `[jest-serializer]` Remove deprecated module from source tree ([#12735](https://togithub.com/facebook/jest/pull/12735))\n\n### [`v28.0.0`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2800)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v27.5.1...v28.0.0)\n\n##### Features\n\n-   `[babel-jest]` Export `createTransformer` function ([#12399](https://togithub.com/facebook/jest/pull/12399))\n-   `[expect]` Expose `AsymmetricMatchers`, `MatcherFunction` and `MatcherFunctionWithState` interfaces ([#12363](https://togithub.com/facebook/jest/pull/12363), [#12376](https://togithub.com/facebook/jest/pull/12376))\n-   `[jest-circus]` Support error logging before retry ([#12201](https://togithub.com/facebook/jest/pull/12201))\n-   `[jest-circus, jest-jasmine2]` Allowed classes and functions as `describe` and `it`/`test` names ([#12484](https://togithub.com/facebook/jest/pull/12484))\n-   `[jest-cli, jest-config]` \\[**BREAKING**] Remove `testURL` config, use `testEnvironmentOptions.url` instead ([#10797](https://togithub.com/facebook/jest/pull/10797))\n-   `[jest-cli, jest-core]` Add `--shard` parameter for distributed parallel test execution ([#12546](https://togithub.com/facebook/jest/pull/12546))\n-   `[jest-cli]` \\[**BREAKING**] Remove undocumented `--timers` option ([#12572](https://togithub.com/facebook/jest/pull/12572))\n-   `[jest-config]` \\[**BREAKING**] Stop shipping `jest-environment-jsdom` by default ([#12354](https://togithub.com/facebook/jest/pull/12354))\n-   `[jest-config]` \\[**BREAKING**] Stop shipping `jest-jasmine2` by default ([#12355](https://togithub.com/facebook/jest/pull/12355))\n-   `[jest-config, @jest/types]` Add `ci` to `GlobalConfig` ([#12378](https://togithub.com/facebook/jest/pull/12378))\n-   `[jest-config]` \\[**BREAKING**] Rename `moduleLoader` to `runtime` ([#10817](https://togithub.com/facebook/jest/pull/10817))\n-   `[jest-config]` \\[**BREAKING**] Rename `extraGlobals` to `sandboxInjectedGlobals` ([#10817](https://togithub.com/facebook/jest/pull/10817))\n-   `[jest-config]` \\[**BREAKING**] Throw an error instead of showing a warning if multiple configs are used ([#12510](https://togithub.com/facebook/jest/pull/12510))\n-   `[jest-config]` \\[**BREAKING**] Do not normalize long deprecated configuration options `preprocessorIgnorePatterns`, `scriptPreprocessor`, `setupTestFrameworkScriptFile` and `testPathDirs` ([#12701](https://togithub.com/facebook/jest/pull/12701))\n-   `[jest-cli, jest-core]` Add `--ignoreProjects` CLI argument to ignore test suites by project name ([#12620](https://togithub.com/facebook/jest/pull/12620))\n-   `[jest-core]` Pass project config to `globalSetup`/`globalTeardown` function as second argument ([#12440](https://togithub.com/facebook/jest/pull/12440))\n-   `[jest-core]` Stabilize test runners with event emitters ([#12641](https://togithub.com/facebook/jest/pull/12641))\n-   `[jest-core, jest-watcher]` \\[**BREAKING**] Move `TestWatcher` class to `jest-watcher` package ([#12652](https://togithub.com/facebook/jest/pull/12652))\n-   `[jest-core]` Allow using Summary Reporter as stand-alone reporter ([#12687](https://togithub.com/facebook/jest/pull/12687))\n-   `[jest-environment-jsdom]` \\[**BREAKING**] Upgrade jsdom to 19.0.0 ([#12290](https://togithub.com/facebook/jest/pull/12290))\n-   `[jest-environment-jsdom]` \\[**BREAKING**] Add default `browser` condition to `exportConditions` for `jsdom` environment ([#11924](https://togithub.com/facebook/jest/pull/11924))\n-   `[jest-environment-jsdom]` \\[**BREAKING**] Pass global config to Jest environment constructor for `jsdom` environment ([#12461](https://togithub.com/facebook/jest/pull/12461))\n-   `[jest-environment-jsdom]` \\[**BREAKING**] Second argument `context` to constructor is mandatory ([#12469](https://togithub.com/facebook/jest/pull/12469))\n-   `[jest-environment-node]` \\[**BREAKING**] Add default `node` and `node-addon` conditions to `exportConditions` for `node` environment ([#11924](https://togithub.com/facebook/jest/pull/11924))\n-   `[jest-environment-node]` \\[**BREAKING**] Pass global config to Jest environment constructor for `node` environment ([#12461](https://togithub.com/facebook/jest/pull/12461))\n-   `[jest-environment-node]` \\[**BREAKING**] Second argument `context` to constructor is mandatory ([#12469](https://togithub.com/facebook/jest/pull/12469))\n-   `[jest-environment-node]` Add all available globals to test globals, not just explicit ones ([#12642](https://togithub.com/facebook/jest/pull/12642), [#12696](https://togithub.com/facebook/jest/pull/12696))\n-   `[@jest/expect]` New module which extends `expect` with `jest-snapshot` matchers ([#12404](https://togithub.com/facebook/jest/pull/12404), [#12410](https://togithub.com/facebook/jest/pull/12410), [#12418](https://togithub.com/facebook/jest/pull/12418))\n-   `[@jest/expect-utils]` New module exporting utils for `expect` ([#12323](https://togithub.com/facebook/jest/pull/12323))\n-   `[@jest/fake-timers]` \\[**BREAKING**] Rename `timers` configuration option to `fakeTimers` ([#12572](https://togithub.com/facebook/jest/pull/12572))\n-   `[@jest/fake-timers]` \\[**BREAKING**] Allow `jest.useFakeTimers()` and `projectConfig.fakeTimers` to take an options bag ([#12572](https://togithub.com/facebook/jest/pull/12572))\n-   `[jest-haste-map]` \\[**BREAKING**] `HasteMap.create` now returns a promise ([#12008](https://togithub.com/facebook/jest/pull/12008))\n-   `[jest-haste-map]` Add support for `dependencyExtractor` written in ESM ([#12008](https://togithub.com/facebook/jest/pull/12008))\n-   `[jest-mock]` \\[**BREAKING**] Rename exported utility types `ClassLike`, `FunctionLike`, `ConstructorLikeKeys`, `MethodLikeKeys`, `PropertyLikeKeys`; remove exports of utility types `ArgumentsOf`, `ArgsType`, `ConstructorArgumentsOf` - TS builtin utility types `ConstructorParameters` and `Parameters` should be used instead ([#12435](https://togithub.com/facebook/jest/pull/12435), [#12489](https://togithub.com/facebook/jest/pull/12489))\n-   `[jest-mock]` Improve `isMockFunction` to infer types of passed function ([#12442](https://togithub.com/facebook/jest/pull/12442))\n-   `[jest-mock]` \\[**BREAKING**] Improve the usage of `jest.fn` generic type argument ([#12489](https://togithub.com/facebook/jest/pull/12489))\n-   `[jest-mock]` Add support for auto-mocking async generator functions ([#11080](https://togithub.com/facebook/jest/pull/11080))\n-   `[jest-mock]` Add `contexts` member to mock functions ([#12601](https://togithub.com/facebook/jest/pull/12601))\n-   `[@jest/reporters]` Add GitHub Actions reporter ([#11320](https://togithub.com/facebook/jest/pull/11320), [#12658](https://togithub.com/facebook/jest/pull/12658))\n-   `[@jest/reporters]` Pass `reporterContext` to custom reporter constructors as third argument ([#12657](https://togithub.com/facebook/jest/pull/12657))\n-   `[jest-resolve]` \\[**BREAKING**] Add support for `package.json` `exports` ([#11961](https://togithub.com/facebook/jest/pull/11961), [#12373](https://togithub.com/facebook/jest/pull/12373))\n-   `[jest-resolve]` Support package self-reference ([#12682](https://togithub.com/facebook/jest/pull/12682))\n-   `[jest-resolve, jest-runtime]` Add support for `data:` URI import and mock ([#12392](https://togithub.com/facebook/jest/pull/12392))\n-   `[jest-resolve, jest-runtime]` Add support for async resolver ([#11540](https://togithub.com/facebook/jest/pull/11540))\n-   `[jest-resolve]` \\[**BREAKING**] Remove `browser?: boolean` from resolver options, `conditions: ['browser']` should be used instead ([#12707](https://togithub.com/facebook/jest/pull/12707))\n-   `[jest-resolve]` Expose `JestResolver`, `AsyncResolver`, `SyncResolver`, `PackageFilter`, `PathFilter` and `PackageJSON` types ([#12707](https://togithub.com/facebook/jest/pull/12707), ([#12712](https://togithub.com/facebook/jest/pull/12712))\n-   `[jest-runner]` Allow `setupFiles` module to export an async function ([#12042](https://togithub.com/facebook/jest/pull/12042))\n-   `[jest-runner]` Allow passing `testEnvironmentOptions` via docblocks ([#12470](https://togithub.com/facebook/jest/pull/12470))\n-   `[jest-runner]` Expose `CallbackTestRunner`, `EmittingTestRunner` abstract classes and `CallbackTestRunnerInterface`, `EmittingTestRunnerInterface` to help typing third party runners ([#12646](https://togithub.com/facebook/jest/pull/12646), [#12715](https://togithub.com/facebook/jest/pull/12715))\n-   `[jest-runner]` Lock version of `source-map-support` to 0.5.13 ([#12720](https://togithub.com/facebook/jest/pull/12720))\n-   `[jest-runtime]` \\[**BREAKING**] `Runtime.createHasteMap` now returns a promise ([#12008](https://togithub.com/facebook/jest/pull/12008))\n-   `[jest-runtime]` Calling `jest.resetModules` function will clear FS and transform cache ([#12531](https://togithub.com/facebook/jest/pull/12531))\n-   `[jest-runtime]` \\[**BREAKING**] Remove `Context` type export, it must be imported from `@jest/test-result` ([#12685](https://togithub.com/facebook/jest/pull/12685))\n-   `[jest-runtime]` Add `import.meta.jest` ([#12698](https://togithub.com/facebook/jest/pull/12698))\n-   `[@jest/schemas]` New module for JSON schemas for Jest's config ([#12384](https://togithub.com/facebook/jest/pull/12384))\n-   `[@jest/source-map]` Migrate from `source-map` to `@jridgewell/trace-mapping` ([#12692](https://togithub.com/facebook/jest/pull/12692))\n-   `[jest-transform]` \\[**BREAKING**] Make it required for `process()` and `processAsync()` methods to always return structured data ([#12638](https://togithub.com/facebook/jest/pull/12638))\n-   `[jest-test-result]` Add duration property to JSON test output ([#12518](https://togithub.com/facebook/jest/pull/12518))\n-   `[jest-watcher]` \\[**BREAKING**] Make `PatternPrompt` class to take `entityName` as third constructor parameter instead of `this._entityName` ([#12591](https://togithub.com/facebook/jest/pull/12591))\n-   `[jest-worker]` \\[**BREAKING**] Allow only absolute `workerPath` ([#12343](https://togithub.com/facebook/jest/pull/12343))\n-   `[jest-worker]` \\[**BREAKING**] Default to advanced serialization when using child process workers ([#10983](https://togithub.com/facebook/jest/pull/10983))\n-   `[pretty-format]` New `maxWidth` parameter ([#12402](https://togithub.com/facebook/jest/pull/12402))\n\n##### Fixes\n\n-   `[*]` Use `sha256` instead of `md5` as hashing algortihm for compatibility with FIPS systems ([#12722](https://togithub.com/facebook/jest/pull/12722))\n-   `[babel-jest]` \\[**BREAKING**] Pass `rootDir` as `root` in Babel's options ([#12689](https://togithub.com/facebook/jest/pull/12689))\n-   `[expect]` Move typings of `.not`, `.rejects` and `.resolves` modifiers outside of `Matchers` interface ([#12346](https://togithub.com/facebook/jest/pull/12346))\n-   `[expect]` Throw useful error if `expect.extend` is called with invalid matchers ([#12488](https://togithub.com/facebook/jest/pull/12488))\n-   `[expect]` Fix `iterableEquality` ignores other properties ([#8359](https://togithub.com/facebook/jest/pull/8359))\n-   `[expect]` Fix print for the `closeTo` matcher ([#12626](https://togithub.com/facebook/jest/pull/12626))\n-   `[jest-changed-files]` Improve `changedFilesWithAncestor` pattern for Mercurial SCM ([#12322](https://togithub.com/facebook/jest/pull/12322))\n-   `[jest-circus, @jest/types]` Disallow undefined value in `TestContext` type ([#12507](https://togithub.com/facebook/jest/pull/12507))\n-   `[jest-config]` Correctly detect CI environment and update snapshots accordingly ([#12378](https://togithub.com/facebook/jest/pull/12378))\n-   `[jest-config]` Pass `moduleTypes` to `ts-node` to enforce CJS when transpiling ([#12397](https://togithub.com/facebook/jest/pull/12397))\n-   `[jest-config]` \\[**BREAKING**] Add `mjs` and `cjs` to default `moduleFileExtensions` config ([#12578](https://togithub.com/facebook/jest/pull/12578))\n-   `[jest-config, jest-haste-map]` Allow searching for tests in `node_modules` by exposing `retainAllFiles` ([#11084](https://togithub.com/facebook/jest/pull/11084))\n-   `[jest-core]` \\[**BREAKING**] Exit with status `1` if no tests are found with `--findRelatedTests` flag ([#12487](https://togithub.com/facebook/jest/pull/12487))\n-   `[jest-core]` Do not report unref-ed subprocesses as open handles ([#12705](https://togithub.com/facebook/jest/pull/12705))\n-   `[jest-each]` `%#` is not replaced with index of the test case ([#12517](https://togithub.com/facebook/jest/pull/12517))\n-   `[jest-each]` Fixes error message with incorrect count of missing arguments ([#12464](https://togithub.com/facebook/jest/pull/12464))\n-   `[jest-environment-jsdom]` Make `jsdom` accessible to extending environments again ([#12232](https://togithub.com/facebook/jest/pull/12232))\n-   `[jest-environment-jsdom]` Log JSDOM errors more cleanly ([#12386](https://togithub.com/facebook/jest/pull/12386))\n-   `[jest-environment-node]` Add `MessageChannel`, `MessageEvent` to globals ([#12553](https://togithub.com/facebook/jest/pull/12553))\n-   `[jest-environment-node]` Add `structuredClone` to globals ([#12631](https://togithub.com/facebook/jest/pull/12631))\n-   `[@jest/expect-utils]` \\[**BREAKING**] Fix false positives when looking for `undefined` prop ([#8923](https://togithub.com/facebook/jest/pull/8923))\n-   `[jest-haste-map]` Don't use partial results if file crawl errors ([#12420](https://togithub.com/facebook/jest/pull/12420))\n-   `[jest-haste-map]` Make watchman existence check lazy+async ([#12675](https://togithub.com/facebook/jest/pull/12675))\n-   `[jest-jasmine2, jest-types]` \\[**BREAKING**] Move all `jasmine` specific types from `@jest/types` to its own package ([#12125](https://togithub.com/facebook/jest/pull/12125))\n-   `[jest-jasmine2]` Do not set `duration` to `0` for skipped tests ([#12518](https://togithub.com/facebook/jest/pull/12518))\n-   `[jest-matcher-utils]` Pass maxWidth to `pretty-format` to avoid printing every element in arrays by default ([#12402](https://togithub.com/facebook/jest/pull/12402))\n-   `[jest-mock]` Fix function overloads for `spyOn` to allow more correct type inference in complex object ([#12442](https://togithub.com/facebook/jest/pull/12442))\n-   `[jest-mock]` Handle overridden `Function.name` property ([#12674](https://togithub.com/facebook/jest/pull/12674))\n-   `[@jest/reporters]` Notifications generated by the `--notify` flag are no longer persistent in GNOME Shell. ([#11733](https://togithub.com/facebook/jest/pull/11733))\n-   `[@jest/reporters]` Move missing icon file which is needed for `NotifyReporter` class. ([#12593](https://togithub.com/facebook/jest/pull/12593))\n-   `[@jest/reporters]` Update `v8-to-istanbul` ([#12697](https://togithub.com/facebook/jest/pull/12697))\n-   `[jest-resolver]` Call custom resolver with core node.js modules ([#12654](https://togithub.com/facebook/jest/pull/12654))\n-   `[jest-runner]` Correctly resolve `source-map-support` ([#12706](https://togithub.com/facebook/jest/pull/12706))\n-   `[jest-worker]` Fix `Farm` execution results memory leak ([#12497](https://togithub.com/facebook/jest/pull/12497))\n\n##### Chore & Maintenance\n\n-   `[*]` \\[**BREAKING**] Drop support for Node v10 and v15 and target first LTS `16.13.0` ([#12220](https://togithub.com/facebook/jest/pull/12220))\n-   `[*]` \\[**BREAKING**] Drop support for `typescript@3.8`, minimum version is now `4.3` ([#11142](https://togithub.com/facebook/jest/pull/11142), [#12648](https://togithub.com/facebook/jest/pull/12648))\n-   `[*]` Bundle all `.d.ts` files into a single `index.d.ts` per module ([#12345](https://togithub.com/facebook/jest/pull/12345))\n-   `[*]` Use `globalThis` instead of `global` ([#12447](https://togithub.com/facebook/jest/pull/12447))\n-   `[babel-jest]` \\[**BREAKING**] Only export `createTransformer` ([#12407](https://togithub.com/facebook/jest/pull/12407))\n-   `[docs]` Add note about not mixing `done()` with Promises ([#11077](https://togithub.com/facebook/jest/pull/11077))\n-   `[docs, examples]` Update React examples to match with the new React guidelines for code examples ([#12217](https://togithub.com/facebook/jest/pull/12217))\n-   `[docs]` Add clarity for module factory hoisting limitations ([#12453](https://togithub.com/facebook/jest/pull/12453))\n-   `[docs]` Add more information about how code transformers work ([#12407](https://togithub.com/facebook/jest/pull/12407))\n-   `[docs]` Add upgrading guide ([#12633](https://togithub.com/facebook/jest/pull/12633))\n-   `[expect]` \\[**BREAKING**] Remove support for importing `build/utils` ([#12323](https://togithub.com/facebook/jest/pull/12323))\n-   `[expect]` \\[**BREAKING**] Migrate to ESM ([#12344](https://togithub.com/facebook/jest/pull/12344))\n-   `[expect]` \\[**BREAKING**] Snapshot matcher types are moved to `@jest/expect` ([#12404](https://togithub.com/facebook/jest/pull/12404))\n-   `[jest-cli]` Update `yargs` to v17 ([#12357](https://togithub.com/facebook/jest/pull/12357))\n-   `[jest-config]` \\[**BREAKING**] Remove `getTestEnvironment` export ([#12353](https://togithub.com/facebook/jest/pull/12353))\n-   `[jest-config]` \\[**BREAKING**] Rename config option `name` to `id` ([#11981](https://togithub.com/facebook/jest/pull/11981))\n-   `[jest-create-cache-key-function]` Added README.md file with basic usage instructions ([#12492](https://togithub.com/facebook/jest/pull/12492))\n-   `[@jest/core]` Use `index.ts` instead of `jest.ts` as main export ([#12329](https://togithub.com/facebook/jest/pull/12329))\n-   `[jest-environment-jsdom]` \\[**BREAKING**] Migrate to ESM ([#12340](https://togithub.com/facebook/jest/pull/12340))\n-   `[jest-environment-node]` \\[**BREAKING**] Migrate to ESM ([#12340](https://togithub.com/facebook/jest/pull/12340))\n-   `[jest-haste-map]` Remove legacy `isRegExpSupported` ([#12676](https://togithub.com/facebook/jest/pull/12676))\n-   `[@jest/fake-timers]` Update `@sinonjs/fake_timers` to v9 ([#12357](https://togithub.com/facebook/jest/pull/12357))\n-   `[jest-jasmine2, jest-runtime]` \\[**BREAKING**] Use `Symbol` to pass `jest.setTimeout` value instead of `jasmine` specific logic ([#12124](https://togithub.com/facebook/jest/pull/12124))\n-   `[jest-phabricator]` \\[**BREAKING**] Migrate to ESM ([#12341](https://togithub.com/facebook/jest/pull/12341))\n-   `[jest-resolve]` \\[**BREAKING**] Make `requireResolveFunction` argument mandatory ([#12353](https://togithub.com/face\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Immortal: This PR will be recreated if closed unmerged. Get config help if that's undesired.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Dont hide TeamMemberView and PullRequestView in dashboard", "number": 4596, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4596"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4596#pullrequestreview-1277933237", "body": ""}
{"title": "TopicIngestionEndpoint", "number": 4597, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4597", "body": "Topic ingestion endpoint parsing\nUpdate"}
{"title": "Do not clear GitHub user display names on sync", "number": 4598, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4598", "body": "Annoyingly the GitHub GET /orgs/:id/members API does not return user display names.\nInstead we need to use GitHub App User Auth with the GET /user or GET /user/:id APIs\nfor each authenticated Unblocked GitHub user. We could schedule this via a low frequency\nbackground poll, or trigger an event to do this on login so that it's only done for active\nusers."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4598#pullrequestreview-1278018992", "body": ""}
{"comment": {"body": "this was the bug fix", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4598#discussion_r1092479825"}}
{"title": "use casing", "number": 4599, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4599"}
{"title": "Add basic openapi generator code for kotlin service", "number": 46, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/46", "body": "This pr adds the following;\n\nMigrating openapi generator template for api service (overrides and additions based off ) \nTests for api code\n\nWe're using 2.0.0-beta version of ktors, so I had to update some imports here and there on the template overrides to reference changes.\n"}
{"comment": {"body": "This is basically a migration of OctoberDemo changes over. Just merging in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/46#issuecomment-1014949685"}}
{"title": "Disable LongParameterList rule", "number": 460, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/460"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/460#pullrequestreview-898323960", "body": ""}
{"title": "Use impersonation context to render name", "number": 4600, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4600"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4600#pullrequestreview-1278082029", "body": ""}
{"title": "Fixbuild", "number": 4601, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4601", "body": "use casing\nFix build"}
{"title": "[WIP] Add TopicModel.keywords property", "number": 4602, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4602", "body": "TODO: test full text search lookup of insights when one of the elements in TopicModel.keywords has a space (i.e. might need to wrap it in quotes)"}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/5002", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4602#issuecomment-1447073040"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4602#pullrequestreview-1278161257", "body": ""}
{"comment": {"body": "This query means we'll map all insights that contains at least one of the keywords in `TopicModel.keywords`. If we want to change this to only map insights that contain all keywords, then change `OR` to `AND`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4602#discussion_r1092578725"}}
{"title": "Temp fix for dashboard syntax hilighting", "number": 4603, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4603", "body": "cc @jeffrey-ng, esbuild-loader breaks this.  I don't know why.  I'm disabling it on the dashboard for now.\nFrom comments like this:\n\nI think that esbuild-loader should generally support web workers, but I can't get it to work as expected."}
{"comment": {"body": "This plugin might fix this, I don't know: https://github.com/chialab/rna/tree/5dc93d356b8856c56c5fbd441c73374ee62a7aad/packages/esbuild-plugin-meta-url", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4603#issuecomment-1411224670"}}
{"comment": {"body": "Haha \ud83d\udc4d \r\n\r\nESbuild-loader doesn't actually support esbuild plugins... We would need to move away from web pack entirely.\r\nForkTsCheckerWebpackPlugin", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4603#issuecomment-1412439644"}}
{"comment": {"body": "Yeah I tried `esbuild-plugin-meta-url` out and it didn't work.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4603#issuecomment-1412554817"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4603#pullrequestreview-1279684939", "body": ""}
{"title": "AddSageMakerTrigger", "number": 4604, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4604", "body": "Add sagemaker triggers\nUpdate"}
{"title": "Dont recommend bots as topic experts", "number": 4605, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4605"}
{"title": "Fix for repo installation upsert failure due to host mismatch", "number": 4606, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4606"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4606#pullrequestreview-1278207194", "body": "Superstar in the making."}
{"title": "This should do it...", "number": 4607, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4607", "body": "Fixes CPU drain. The GIF rendering was happening regardless of the view state even when the hub was closed. CPU pinned between 20-30%..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4607#pullrequestreview-1278221262", "body": ""}
{"title": "Update sagemaker resources", "number": 4608, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4608"}
{"title": "Update", "number": 4609, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4609"}
{"title": "Git diff unit tests", "number": 461, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/461"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/461#pullrequestreview-898617095", "body": ""}
{"title": "Fix for GHE team members being deleted randomly", "number": 4610, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4610", "body": "Fucking hard-coded GitHub."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4610#pullrequestreview-1278347100", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4610#pullrequestreview-**********", "body": "I owe you a coffee"}
{"title": "powerml array", "number": 4611, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4611"}
{"title": "make indexable", "number": 4612, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4612"}
{"title": "indexable", "number": 4613, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4613"}
{"title": "Backfill externalTeamId on GitHub provider Identities", "number": 4614, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4614", "body": "Tested in local stack, works fine.\nWill run in DEV first.\nThen run in PROD.\nThis migration is super low risk, no deletion occurs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4614#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4614#pullrequestreview-**********", "body": ""}
{"title": "Remove duplicate identities", "number": 4615, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4615"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4615#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Always deletes the newer identity.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4615#discussion_r1092769764"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4615#pullrequestreview-**********", "body": "Retroactive approve!"}
{"title": "Cleanup identity migration no longer needed", "number": 4616, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4616"}
{"title": "Remap team member references before identity delete", "number": 4617, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4617"}
{"comment": {"body": "You are appreicated riche", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4617#issuecomment-1411554475"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4617#pullrequestreview-1278526043", "body": ""}
{"title": "Falco security setup", "number": 4618, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4618", "body": "Falco core and Sidekick have been deployed to both environments.\n    - Added readme with installation steps as well as links to docs about how to manage Falco rules\n    - Connected Falco sidekick to Slack (security-alarms-dev and security-alarms channels)\n    - Created separate Webhook integration endpoints on Grafana to receive alarms from Falco and page the oncall\n    - Updated Global network policy to allow API accesss from falco namespace.\n    - Deleted the old unused network policies\n    - Added Falco dashboard to Grafana (needs a bit of work to support multiple envs)\nI tried using Alertmanager, Grafana and Loki integrations but non worked as expected!\nThis should wrap up our Falco work for now.\n\nFalco Overview\nDev: #security-alarms-dev\nProd: #security-alarms"}
{"comment": {"body": "This is pretty neat!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4618#issuecomment-1412458848"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4618#pullrequestreview-1279550240", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4618#pullrequestreview-1279582275", "body": ""}
{"title": "Reduce very spammy messages", "number": 4619, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4619", "body": "This change removes 79% of log messages, based on last 2 days of logz history."}
{"title": "Implement ThreadUnread", "number": 462, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462", "body": "implements Unread api endpoints\ncreates/updates ThreadUnreads whenever a message is created\nattempts to start separating the database layer from the api layer, at least for ThreadUnread ()\n\nUnread Message Synchronization\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899261387", "body": ""}
{"comment": {"body": "These lines are the meat of the changes. \r\n\r\n1.) Update all `ThreadUnread.latestMessage` to this new message because it is the latest\r\n2.) Update `ThreadUnread.latestReadMessage` for the message's author to this new message. If `ThreadUnread` doesn't exist, then create it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r818890513"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899391364", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899392640", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899410079", "body": "partial feedback..."}
{"comment": {"body": "weird, why?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r818988827"}}
{"comment": {"body": "Let's see how we can make these fields non-nullable, because in reality they are _always_ non-null. Maybe throw if null in the `asDataClass()` function above?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819010700"}}
{"comment": {"body": "why?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819012927"}}
{"comment": {"body": "Right now this could be an object (singleton) since it has no state. If we make it an object, then we avoid the need to plumb an instance of it around to the Application module or APIImpl class.\r\n\r\nThe only reason I can think of to make this a class is so that we can mock it for ApiImplTest class. Is that the intention? If so then we should re-write the ApiImplTest class to work on mock ThreadUnreadStore with APIs that return ThreadUnread data class fixtures.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819017647"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899415166", "body": "Approved with comments. I will replicate the data layer stuff when I implement the Video API and we'll see how it scales :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899420487", "body": ""}
{"comment": {"body": "This index is for the `getUnreads` operation for retrieving all unread threads for a team member.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r818996039"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899426632", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899451906", "body": ""}
{"comment": {"body": "I see. related to this: https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819017647", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819018580"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899496820", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r818996039", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819049783"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899499328", "body": ""}
{"comment": {"body": "Yup, it's to allow mocking it in the module via `UnblockedApiClient`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819051522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899503303", "body": ""}
{"comment": {"body": "We pass in a `mock<ThreadUnreadStore>` but maybe we should just make `ThreadUnreadStore` an interface that can be implemented.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819054323"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899568196", "body": ""}
{"comment": {"body": "I'll make `createdAt` and `modifiedAt` non-null with default values, but not sure if we should be reading these fields when creating a new record or just let the insert statement populate them. Thinking the latter...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819099446"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899568679", "body": ""}
{"comment": {"body": "Well, maybe we should read in `createdAt` and just never touch `modifiedAt` for any model", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819099763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899593097", "body": ""}
{"comment": {"body": "Ugh, I see. Dammit.\r\n\r\nFor new object creation the client should never be responsible for populating the `modifiedAt`.\r\n\r\nHowever, the client sometimes (eg: PR ingestion) needs to specify the `createdAt` at some historical time. Struggling with how to do this properly... maybe leave as nullable after all so that the client can choose to set the values or not.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819115551"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899597006", "body": ""}
{"comment": {"body": "Yeah, sounds good: never touch `modifiedAt`, and if `createdAt` is not null then set it, otherwise let the DB auto populate.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#discussion_r819118127"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899628726", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899633935", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899649390", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899650156", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899701617", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/462#pullrequestreview-899714242", "body": ""}
{"title": "Attempt to debug migration", "number": 4620, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4620", "body": "Run synchronously\nAdd logs"}
{"title": "Migration debugging", "number": 4621, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4621"}
{"title": "Move back to TSLoader for web workers", "number": 4622, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4622", "body": "Seems like ESBuild doesn't support importing web workers\n"}
{"title": "Add activeMembersMapStream to TeamMemberStore", "number": 4623, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4623", "body": "Add a new stream with bot users filtered out, using the new teamMember.identity.isBot property.\nAreas where we filter out bot accounts:\n* Mentions/git collaborators list (we also manually filter out !teamMember.isCurrentMember here) \n* Adding experts to topic (in NewTopic flow as well as the topic management in the sidebar) \nThis PR only leverages the filtered map on the clients, not the stores. If there is a bot id in the topic experts list or a discussion participants list, we still want to show their user information."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4623#pullrequestreview-**********", "body": ""}
{"comment": {"body": "If this is for active, check for `isCurrentMember` as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4623#discussion_r1093607914"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4623#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4623#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Should move this filter function into a single place as well. Noticed it's being used in a few places for activeMembers", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4623#discussion_r1093614626"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4623#pullrequestreview-1279767652", "body": ""}
{"comment": {"body": "so ideally I think that's what we should do. As it stands, slack users are !isCurrentMember but we still want to show them for experts. It's a little tricky but we figured it would be the least disruptive to only filter out bots for now until we get the identity work figured out", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4623#discussion_r1093665603"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4623#pullrequestreview-1280030494", "body": ""}
{"title": "Add log thresholds", "number": 4624, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4624", "body": "Appenders should have specific thresholds.\nWe should not restrict seeing trace logs out in console.output for services if I'm observing from kubernetes."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4624#pullrequestreview-1279612374", "body": ""}
{"comment": {"body": "wait, what?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4624#discussion_r1093561337"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4624#pullrequestreview-1279641560", "body": ""}
{"comment": {"body": "You were right (again).\r\n\r\nThere is no longer a global filter on the stdout appender.\r\nThe root appender is WARN.\r\nThe next chapter software appender is TRACE.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4624#discussion_r1093580754"}}
{"title": "Add team member url to expert tooltip", "number": 4625, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4625", "body": "This may be part of onboarding.\n\nNote: This is using DashboardUrl right now as topics and experts do not have server-generated links. There's an open PR for this work https://github.com/NextChapterSoftware/unblocked/pull/4368. Will update this code once that work is done."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4625#pullrequestreview-1279670910", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4625#pullrequestreview-1279670971", "body": ""}
{"title": "Loosens constraints on several fields", "number": 4626, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626", "body": "Safe to ignore API break since this just loosens constraints. Some of these changes might mask real bugs. Please validate"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626#pullrequestreview-1279751661", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626#pullrequestreview-1280078596", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626#pullrequestreview-1280082925", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626#pullrequestreview-1280084239", "body": "Add comments to minLength: 0 since those are fishy and are probably all bugs."}
{"comment": {"body": "possible to add test?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626#discussion_r1093878548"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626#pullrequestreview-1280087191", "body": ""}
{"comment": {"body": "The test would look something like this:\r\n- Spec A \r\n- Clone Spec A -> Spec B, with modified maxLength that breaks compatibility\r\n- Verify diff fails without sanitize\r\n- Verify diff passes with sanitize", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626#discussion_r1093880678"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626#pullrequestreview-1282057728", "body": ""}
{"comment": {"body": "I couldn't get the parser to pick up response content. Punting for now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4626#discussion_r1095194308"}}
{"title": "Identity migration: Special handling for ThreadParticipantModel", "number": 4627, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4627", "body": "Problem\n- cannot rename a ThreadParticipantModel.member because the new member may already exist,\n  which violates the ThreadParticipantModel unique constraint.\nSolution\n- for each ThreadParticipantModel row involving the old member ensure that the exact same\n  row exists for the new member, ignoring the insert if it already exists.\n- the delete of the old identity will cascade to the old member, which will in turn cascade\n  to the old ThreadParticipantModel row, so everything gets nicely cleaned up."}
{"title": "Lift intellij config readme to root", "number": 4628, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4628"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4628#pullrequestreview-1279809594", "body": ""}
{"title": "chore(deps): update dependency @types/chrome to ^0.0.211", "number": 4629, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4629", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/chrome (source) | ^0.0.210 -> ^0.0.211 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Adding new DNS names and Certs for ALBs", "number": 463, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/463", "body": "Added new cert for alb.{env}.getunblocked.com (Cloudfront will talk to this endpoint)\nAdded above DNS record pointing it at ALB address for each env-region\nChanged the Web Dashboard CI workflow to use the new CloudFront Distro ID\nThis is more prep work for my next PR changing helm specs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/463#pullrequestreview-899380872", "body": ""}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.19.28", "number": 4630, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4630", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.19.27 -> 2.19.28 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.19.27 -> 2.19.28 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.19.27 -> 2.19.28 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.19.27 -> 2.19.28 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.19.27 -> 2.19.28 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime (source) | 2.19.27 -> 2.19.28 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.19.27 -> 2.19.28 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.19.27 -> 2.19.28 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.19.27 -> 2.19.28 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.19.28`]()\n\n[Compare Source]()\n\n#### **AWS AppSync**\n\n-   ### Features\n    -   This release introduces the feature to support EventBridge as AppSync data source.\n\n#### **AWS CRT-based S3 Client**\n\n-   ### Bugfixes\n    -   Fixed an issue where port number was not added to host header. See [#3721]() and [#3682]().\n\n#### **AWS CloudTrail**\n\n-   ### Features\n    -   Add new \"Channel\" APIs to enable users to manage channels used for CloudTrail Lake integrations, and \"Resource Policy\" APIs to enable users to manage the resource-based permissions policy attached to a channel.\n\n#### **AWS CloudTrail Data Service**\n\n-   ### Features\n    -   Add CloudTrail Data Service to enable users to ingest activity events from non-AWS sources into CloudTrail Lake.\n\n#### **AWS Ground Station**\n\n-   ### Features\n    -   DigIF Expansion changes to the Customer APIs.\n\n#### **AWS IoT**\n\n-   ### Features\n    -   Added support for IoT Rules Engine Cloudwatch Logs action batch mode.\n\n#### **AWS Outposts**\n\n-   ### Features\n    -   Enabled FIPS endpoints for GovCloud (US) regions in SDK.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **AWS SecurityHub**\n\n-   ### Features\n    -   New fields have been added to the AWS Security Finding Format. Compliance.SecurityControlId is a unique identifier for a security control across standards. Compliance.AssociatedStandards contains all enabled standards in which a security control is enabled.\n\n#### **AWS Support**\n\n-   ### Features\n    -   This fixes incorrect endpoint construction when a customer is explicitly setting a region.\n\n#### **Access Analyzer**\n\n-   ### Features\n    -   Enabled FIPS endpoints for GovCloud (US) regions in SDK.\n\n#### **Amazon Connect Participant Service**\n\n-   ### Features\n    -   Enabled FIPS endpoints for GovCloud (US) regions in SDK.\n\n#### **Amazon Elastic Compute Cloud**\n\n-   ### Features\n    -   This launch allows customers to associate up to 8 IP addresses to their NAT Gateways to increase the limit on concurrent connections to a single destination by eight times from 55K to 440K.\n\n#### **Amazon Kinesis**\n\n-   ### Features\n    -   Enabled FIPS endpoints for GovCloud (US) regions in SDK.\n\n#### **Amazon OpenSearch Service**\n\n-   ### Features\n    -   Amazon OpenSearch Service adds the option for a VPC endpoint connection between two domains when the local domain uses OpenSearch version 1.3 or 2.3. You can now use remote reindex to copy indices from one VPC domain to another without a reverse proxy.\n\n#### **Amazon Polly**\n\n-   ### Features\n    -   Amazon Polly adds two new neural American English voices - Ruth, Stephen\n\n#### **Amazon SageMaker Service**\n\n-   ### Features\n    -   Amazon SageMaker Automatic Model Tuning now supports more completion criteria for Hyperparameter Optimization.\n\n#### **CodeArtifact**\n\n-   ### Features\n    -   This release introduces a new DeletePackage API, which enables deletion of a package and all of its versions from a repository.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update aws-cdk monorepo to v2.63.0", "number": 4631, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4631", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| aws-cdk | 2.62.2 -> 2.63.0 |  |  |  |  |\n| aws-cdk-lib | 2.62.2 -> 2.63.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-cdk\n\n### [`v2.63.0`]()\n\n[Compare Source]()\n\n##### Features\n\n-   **cfnspec:** cloudformation spec v109.0.0 ([#23868]()) ([8ee97b0]())\n-   **cfnspec:** cloudformation spec v109.0.0 ([#23929]()) ([39f8a30]())\n-   **core:** add creation policy configuration for appstream ([#23607]()) ([8909a04]()), closes [#23604]()\n-   **core:** allow asset bundling on docker remote host / docker in docker ([#23576]()) ([afce30a]()), closes [#8799]()\n-   **stepfunctions:** task and heartbeat timeout specified by a path ([#23755]()) ([26e48c7]()), closes [#15531]()\n\n##### Bug Fixes\n\n-   **appsync:** sanitized datasource name isn't exported ([#23802]()) ([0b25265]())\n-   imports from ESM modules cannot find correct type definitions ([#23870]()) ([356a128]())\n-   **eks:** reuse chart name as chart dir for helmchart deployment from OCI repository ([#23392]()) ([070f5ec]())\n-   `aws-cdk-lib` imports from ESM modules are broken ([#23846]()) ([cf2e498]()), closes [#23813]()\n\n***\n\n#### Alpha modules (2.63.0-alpha.0)\n\n##### Features\n\n-   **synthetics:** Adding DeleteLambdaResourcesOnCanaryDeletion prop to the canary L2 ([#23820]()) ([45c191e]())\n-   **redshift:** support default role for redshift clusters ([#22551]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update slate monorepo to ^0.90.0", "number": 4632, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4632", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| slate | ^0.88.1 -> ^0.90.0 |  |  |  |  |\n| slate-react | ^0.88.2 -> ^0.90.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nianstormtaylor/slate\n\n### [`v0.90.0`](https://togithub.com/ianstormtaylor/slate/releases/tag/slate%400.90.0)\n\n[Compare Source](https://togithub.com/ianstormtaylor/slate/compare/<EMAIL>@0.90.0)\n\n##### Patch Changes\n\n-   [#5278](https://togithub.com/ianstormtaylor/slate/pull/5278) [`9c4097a2`](https://togithub.com/ianstormtaylor/slate/commit/9c4097a26fa92718e6f4fc1f984a70fb5af42ca2) Thanks [@kylemclean](https://togithub.com/kylemclean)! - Revert to using inline styles for default editor styles\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency com.google.api-client:google-api-client to v2", "number": 4633, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4633", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.google.api-client:google-api-client | 1.35.2 -> 2.2.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ngoogleapis/google-api-java-client\n\n### [`v2.2.0`]()\n\n##### Features\n\n-   Remove deprecated OOB flow ([#2242]()) ([bb494ee]())\n\n### [`v2.1.4`]()\n\n##### Bug Fixes\n\n-   **deps:** Update dependency com.google.api-client:google-api-client to v2.1.3 ([#2239]()) ([a830476]())\n-   **deps:** Update dependency com.google.cloud:libraries-bom to v26.4.0 ([#2240]()) ([710a5ad]())\n-   **deps:** Update dependency org.apache.httpcomponents:httpclient to v4.5.14 ([#2236]()) ([aa2b08c]())\n-   **deps:** Update dependency org.apache.httpcomponents:httpcore to v4.4.16 ([#2237]()) ([5de8cc2]())\n\n##### Documentation\n\n-   Adding deprecated annotation and guide for migration ([#2244]()) ([e505c26]())\n\n### [`v2.1.3`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   Media upload to have applicationName as User-Agent ([#2227]()) ([2595de0]())\n\n### [`v2.1.2`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   Make Details field extends GenericJson so that GoogleJsonError should include any arbitrary error info from services ([#2210]()) ([60939b1]())\n\n### [`v2.1.1`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   Pinning commons-codec dependency in google-api-client ([#2201]()) ([27e94c0]())\n\n### [`v2.1.0`]()\n\n[Compare Source]()\n\n##### Features\n\n-   Next release from main branch is 2.1.0 ([#2179]()) ([0b39ce1]())\n\n##### Bug Fixes\n\n-   **deps:** Update dependency com.google.api-client:google-api-client to v2.0.1 ([#2176]()) ([7cb91f7]())\n-   **deps:** Update dependency com.google.cloud:libraries-bom to v26.1.4 ([#2177]()) ([0e3be64]())\n-   Update and declare commons-codec dependency ([#2195]()) ([ad7f8ae]())\n\n### [`v2.0.1`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   Add error description to batch emptiness validation ([#2109]()) ([2668dd1]())\n-   **deps:** Update dependency com.google.api-client:google-api-client to v2 ([#2108]()) ([570a162]())\n-   **deps:** Update dependency com.google.appengine:appengine-api-1.0-sdk to v2.0.10 ([#2174]()) ([9077b4a]())\n-   **deps:** Update dependency com.google.appengine:appengine-api-1.0-sdk to v2.0.6 ([#2124]()) ([51adc54]())\n-   **deps:** Update dependency com.google.appengine:appengine-api-1.0-sdk to v2.0.7 ([#2131]()) ([6892bb2]())\n-   **deps:** Update dependency com.google.appengine:appengine-api-1.0-sdk to v2.0.8 ([#2140]()) ([bb6f19c]())\n-   **deps:** Update dependency com.google.cloud:libraries-bom to v26.1.0 ([#2126]()) ([3d0e0ff]())\n-   **deps:** Update dependency com.google.cloud:libraries-bom to v26.1.1 ([#2134]()) ([15ce062]())\n-   **deps:** Update dependency com.google.cloud:libraries-bom to v26.1.2 ([#2143]()) ([da2f6f3]())\n\n### [`v2.0.0`]()\n\n[Compare Source]()\n\n#####  BREAKING CHANGES\n\n-   remove deprecated class ([#1666]())\n\n##### Bug Fixes\n\n-   **deps:** update dependency com.google.api-client:google-api-client to v1.35.2 ([#2101]()) ([c6aa5b7]())\n-   **deps:** update dependency com.google.cloud:libraries-bom to v26 ([#2103]()) ([a0323fb]())\n-   remove deprecated class ([#1666]()) ([88fd7f3]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.19.29", "number": 4634, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4634", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:s3 (source) | 2.19.28 -> 2.19.29 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.19.28 -> 2.19.29 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.19.29`]()\n\n[Compare Source]()\n\n#### **AWS Identity and Access Management**\n\n-   ### Features\n    -   Documentation updates for AWS Identity and Access Management (IAM).\n\n#### **AWS MediaTailor**\n\n-   ### Features\n    -   The AWS Elemental MediaTailor SDK for Channel Assembly has added support for program updates, and the ability to clip the end of VOD sources in programs.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   EC2 Instance Metadata Client is now generally available - []()\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon DevOps Guru**\n\n-   ### Features\n    -   This release adds filter support ListAnomalyForInsight API.\n\n#### **Amazon Forecast Service**\n\n-   ### Features\n    -   This release will enable customer select INCREMENTAL as ImportModel in Forecast's CreateDatasetImportJob API. Verified latest SDK containing required attribute, following \n\n#### **Amazon Simple Notification Service**\n\n-   ### Features\n    -   Additional attributes added for set-topic-attributes.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency software.amazon.awssdk:bom to v2.19.29", "number": 4635, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4635", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.19.28 -> 2.19.29 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.19.29`]()\n\n[Compare Source]()\n\n#### **AWS Identity and Access Management**\n\n-   ### Features\n    -   Documentation updates for AWS Identity and Access Management (IAM).\n\n#### **AWS MediaTailor**\n\n-   ### Features\n    -   The AWS Elemental MediaTailor SDK for Channel Assembly has added support for program updates, and the ability to clip the end of VOD sources in programs.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   EC2 Instance Metadata Client is now generally available - []()\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon DevOps Guru**\n\n-   ### Features\n    -   This release adds filter support ListAnomalyForInsight API.\n\n#### **Amazon Forecast Service**\n\n-   ### Features\n    -   This release will enable customer select INCREMENTAL as ImportModel in Forecast's CreateDatasetImportJob API. Verified latest SDK containing required attribute, following \n\n#### **Amazon Simple Notification Service**\n\n-   ### Features\n    -   Additional attributes added for set-topic-attributes.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency io.agora:authentication to v2", "number": 4636, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4636", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.agora:authentication (source) | 1.6.1 -> 2.0.0.2-ci-test |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "UNB-944 Topics: Filter out common stopwords + developer terms", "number": 4637, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4637", "body": "This filters out topics after generation but before we save it to the database."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4637#pullrequestreview-1279937421", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4637#pullrequestreview-1279986810", "body": ""}
{"title": "Identity migration: Upsert new team member if necessary", "number": 4638, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4638"}
{"title": "fix(deps): update dependency io.github.microutils:kotlin-logging to v3", "number": 4639, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4639", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.github.microutils:kotlin-logging | 2.1.23 -> 3.0.5 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\noshai/kotlin-logging\n\n### [`v3.0.5`](https://togithub.com/oshai/kotlin-logging/releases/tag/3.0.5)\n\n[Compare Source](https://togithub.com/oshai/kotlin-logging/compare/3.0.4...3.0.5)\n\n#### What's Changed\n\n-   build(deps): bump kotlin multiplatform from 1.7.20 to 1.7.21 by [@yeikel](https://togithub.com/yeikel) in [https://github.com/oshai/kotlin-logging/pull/261](https://togithub.com/oshai/kotlin-logging/pull/261)\n-   upgrade kotlin to 1.8.0 by [@oshai](https://togithub.com/oshai) in [https://github.com/oshai/kotlin-logging/pull/267](https://togithub.com/oshai/kotlin-logging/pull/267)\n-   update links by [@oshai](https://togithub.com/oshai) in [https://github.com/oshai/kotlin-logging/pull/268](https://togithub.com/oshai/kotlin-logging/pull/268)\n\n**Full Changelog**: https://github.com/oshai/kotlin-logging/compare/3.0.4...3.0.5\n\n### [`v3.0.4`](https://togithub.com/oshai/kotlin-logging/releases/tag/3.0.4)\n\n[Compare Source](https://togithub.com/oshai/kotlin-logging/compare/3.0.3...3.0.4)\n\n#### What's Changed\n\n-   set jvmTarget back to 8 by [@oshai](https://togithub.com/oshai) in [https://github.com/MicroUtils/kotlin-logging/pull/258](https://togithub.com/MicroUtils/kotlin-logging/pull/258)\n-   add ktfmtCheck to CI by [@oshai](https://togithub.com/oshai) in [https://github.com/MicroUtils/kotlin-logging/pull/259](https://togithub.com/MicroUtils/kotlin-logging/pull/259)\n\n**Full Changelog**: https://github.com/MicroUtils/kotlin-logging/compare/3.0.3...3.0.4\n\n### [`v3.0.3`](https://togithub.com/oshai/kotlin-logging/releases/tag/3.0.3)\n\n#### What's Changed\n\n-   add \"Automatic-Module-Name\" for [#223](https://togithub.com/oshai/kotlin-logging/issues/223) by [@oshai](https://togithub.com/oshai) in [https://github.com/MicroUtils/kotlin-logging/pull/256](https://togithub.com/MicroUtils/kotlin-logging/pull/256)\n-   reword slf4j usage for [#229](https://togithub.com/oshai/kotlin-logging/issues/229) by [@oshai](https://togithub.com/oshai) in [https://github.com/MicroUtils/kotlin-logging/pull/255](https://togithub.com/MicroUtils/kotlin-logging/pull/255)\n-   add simple native test (main) by [@oshai](https://togithub.com/oshai) in [https://github.com/MicroUtils/kotlin-logging/pull/219](https://togithub.com/MicroUtils/kotlin-logging/pull/219)\n\n**Full Changelog**: https://github.com/MicroUtils/kotlin-logging/compare/3.0.1...3.0.3\n\n### [`v3.0.0`](https://togithub.com/oshai/kotlin-logging/releases/tag/3.0.0)\n\n[Compare Source](https://togithub.com/oshai/kotlin-logging/compare/2.1.23...3.0.0)\n\n#### What's Changed\n\n**Major version upgrade to 3.0.0 to reflect upgrade of slf4j to 2.x.**\n\n-   Upgrade slf4j 1.x->2.x by [@yeikel](https://togithub.com/yeikel) in [https://github.com/MicroUtils/kotlin-logging/pull/234](https://togithub.com/MicroUtils/kotlin-logging/pull/234)\n-   fix kdoc of mu.KotlinLogging.logger by [@aivantsov](https://togithub.com/aivantsov) in [https://github.com/MicroUtils/kotlin-logging/pull/230](https://togithub.com/MicroUtils/kotlin-logging/pull/230)\n-   remove 1.x links from readme by [@oshai](https://togithub.com/oshai) in [https://github.com/MicroUtils/kotlin-logging/pull/231](https://togithub.com/MicroUtils/kotlin-logging/pull/231)\n-   build(deps): bump log4j from 2.17.1 to 2.18.0 by [@yeikel](https://togithub.com/yeikel) in [https://github.com/MicroUtils/kotlin-logging/pull/235](https://togithub.com/MicroUtils/kotlin-logging/pull/235)\n-   Create SECURITY.md by [@oshai](https://togithub.com/oshai) in [https://github.com/MicroUtils/kotlin-logging/pull/237](https://togithub.com/MicroUtils/kotlin-logging/pull/237)\n-   Bump gradle wrapper to 7.5.1 by [@yeikel](https://togithub.com/yeikel) in [https://github.com/MicroUtils/kotlin-logging/pull/238](https://togithub.com/MicroUtils/kotlin-logging/pull/238)\n\n#### New Contributors\n\n-   [@aivantsov](https://togithub.com/aivantsov) made their first contribution in [https://github.com/MicroUtils/kotlin-logging/pull/230](https://togithub.com/MicroUtils/kotlin-logging/pull/230)\n-   [@yeikel](https://togithub.com/yeikel) made their first contribution in [https://github.com/MicroUtils/kotlin-logging/pull/235](https://togithub.com/MicroUtils/kotlin-logging/pull/235)\n\n**Full Changelog**: https://github.com/MicroUtils/kotlin-logging/compare/2.1.23...3.0.0\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Switch everything to ALB at once (all or nothing)", "number": 464, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/464", "body": "This PR includes a lot of duplicate changes but they boil down to the following:\n\nConvert existing services to ClusterIP (NodePort) and remove DNS/Cert stuff from each\nCreate an ingress for each service with proper path and DNS/Cert configs\nUpdated base helmchart\nRegenerate all helmcharts\n\nNote: All ingress deployments are combined into a single ALB by the controller (using alb groups.name annotation). ALB will take care of routing traffic to each NodePort service based on provided path configuration"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/464#pullrequestreview-*********", "body": ""}
{"title": "fix(deps): update dependency org.mockito:mockito-inline to v5", "number": 4640, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4640", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.mockito:mockito-inline | 4.11.0 -> 5.1.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nmockito/mockito\n\n### [`v5.1.1`](https://togithub.com/mockito/mockito/releases/tag/v5.1.1)\n\n*Changelog generated by [Shipkit Changelog Gradle Plugin](https://togithub.com/shipkit/shipkit-changelog)*\n\n##### 5.1.1\n\n-   2023-01-30 - [1 commit(s)](https://togithub.com/mockito/mockito/compare/v5.1.0...v5.1.1) by Andriy Redko\n-   StackWalker.Option not found on Mockito 5.1.0 [(#2891)](https://togithub.com/mockito/mockito/pull/2891)\n-   StackWalker.Option not found on Mockito 5.1.0 [(#2890)](https://togithub.com/mockito/mockito/issues/2890)\n\n### [`v5.1.0`](https://togithub.com/mockito/mockito/releases/tag/v5.1.0)\n\n[Compare Source](https://togithub.com/mockito/mockito/compare/v5.0.0...v5.1.0)\n\n*Changelog generated by [Shipkit Changelog Gradle Plugin](https://togithub.com/shipkit/shipkit-changelog)*\n\n##### 5.1.0\n\n-   2023-01-29 - [12 commit(s)](https://togithub.com/mockito/mockito/compare/v5.0.0...v5.1.0) by Andriy Redko, Ashley, Rbert Papp, Stephan Schroevers, Tim te Beek, dependabot\\[bot]\n-   Fixes some mistakes and missing details in documentation [(#2889)](https://togithub.com/mockito/mockito/pull/2889)\n-   Bump com.diffplug.spotless from 6.13.0 to 6.14.0 [(#2888)](https://togithub.com/mockito/mockito/pull/2888)\n-   Clean up JDK-8 related code [(#2883)](https://togithub.com/mockito/mockito/pull/2883)\n-   Feat: reified mock overloads [(#2882)](https://togithub.com/mockito/mockito/pull/2882)\n-   Clean up JDK-8 related code [(#2879)](https://togithub.com/mockito/mockito/issues/2879)\n-   Bump assertj-core from 3.24.1 to 3.24.2 [(#2875)](https://togithub.com/mockito/mockito/pull/2875)\n-   Make sure the tests use mock maker with intended member accessor [(#2872)](https://togithub.com/mockito/mockito/pull/2872)\n-   Bump com.diffplug.spotless from 6.12.1 to 6.13.0 [(#2871)](https://togithub.com/mockito/mockito/pull/2871)\n-   Remove broken link from `CONTRIBUTING.md` [(#2870)](https://togithub.com/mockito/mockito/pull/2870)\n-   Update outdated badge 3.x to 5.x [(#2869)](https://togithub.com/mockito/mockito/pull/2869)\n-   Broken link in `CONTRIBUTING.md` [(#2868)](https://togithub.com/mockito/mockito/issues/2868)\n-   Set current version to 5.x in README and highlight changes [(#2867)](https://togithub.com/mockito/mockito/pull/2867)\n-   Annotate `Mockito#{mock,spy}(T... reified)` with `@SafeVarargs` [(#2866)](https://togithub.com/mockito/mockito/pull/2866)\n-   Make sure the tests use mock maker with intended member accessor [(#2855)](https://togithub.com/mockito/mockito/issues/2855)\n-   Improve examples for InOrder [(#2843)](https://togithub.com/mockito/mockito/pull/2843)\n\n### [`v5.0.0`](https://togithub.com/mockito/mockito/releases/tag/v5.0.0)\n\n### Mockito 5: prepare for future JDK versions\n\nFor a while now, we have seen an increase in problems/incompatibilities with recent versions of the JDK due to our usage of JVM-internal API.\nMost notably, JDK 17 made some changes which are incompatible with the current subclass mockmaker.\nTherefore, to prepare for the future of JDK, we are making some core changes to ensure Mockito keeps on working.\n\n#### Switch the default mockmaker to `mockito-inline`\n\nBack in Mockito 2.7.6, we published a new mockmaker based on the \"inline bytecode\" principle.\nThis mockmaker creates mocks manipulating bytecode equivalent within the original class such that its method implementations hook into the normal Mockito machinery.\nAs a comparison, the subclass mockmaker generates \"real\" subclasses for mocks, to mimic the same behavior.\nWhile the approaches are similar, the inline mockmaker avoids certain restrictions that the JDK imposes.\nFor example, it does not violate module boundaries (introduced in JDK 9, but more heavily used in JDK 17) and avoids the leaking of the creation of the subclass.\n\nMassive thanks to community member [@reta](https://togithub.com/reta) who implemented this change.\n\nNote: this does not affect `mockito-android` nor testing on Android.\n\n##### When should I still be using the subclass mockmaker?\n\nThere are legitimate remaining use cases for the subclass mockmaker.\nFor example, on the Graal VM's native image, the inline mockmaker will not work and the subclass mockmaker is the appropriate choice.\nAdditionally, if you would like to avoid mocking final classes, using the subclass mockmaker is a possibibility.\nNote however that if you solely want to use the subclass mockmaker to avoid mocking final, you will run into the above mentioned issues on JDK 17+.\nWe want to leave this choice up to our users, which is why we will keep on supporting the subclass mockmaker.\n\nIf you want to use the subclass mockmaker instead, you can use the new `mockito-subclass` artifact (published [on Maven Central](https://search.maven.org/artifact/org.mockito/mockito-subclass) along with all our other artifacts).\n\n#### Update the minimum supported Java version to 11\n\nMockito 4 supports Java 8 and above.\nSimilar to other open source projects, we are moving away from JDK 8 and to newer versions.\nThe primary reason for moving away from JDK 8 is the increasing maintenance costs with keeping our own infrastructure working.\nLately we have been running into more and more JDK 8 breakages.\nAdditionally, while we want to support the newest JDK API's, our current solution to support both JDK 8 and newer versions causes [issues with the `SecurityManager`](https://togithub.com/mockito/mockito/issues/2798).\nSince we want Mockito to work on the newest version and more and more businesses adopting JDK 11, we have decided to make the switch as well.\n\nMassive thanks to community member [@reta](https://togithub.com/reta) who implemented this change.\n\n##### What should I do if I still run JDK 8?\n\nFor JDK 8 and below, you can keep on using Mockito 4.\nThis is similar to if you are using JDK 6, for which you can keep on using Mockito 2.\nThe changes in Mockito 5 (for now) are primarily focused on the latest JDK versions, which means the API differences between Mockito 4 and 5 are minimal.\nHowever, over time this will most likely widen, so we do recommend adopting JDK 11 in the future.\n\n#### New `type()` method on `ArgumentMatcher`\n\nOne of our most used public API's for customizing Mockito is the [`ArgumentMatcher` interface](https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/ArgumentMatcher.html).\nThe interface allows you to define a custom matcher, which you can pass into method arguments to provide more targeted matches.\nOne major shortcoming of the `ArgumentMatcher` was the lack of varargs support.\nThere were many, many issues filed related to varargs and Mockito unable to handle them.\n\nCommunity member [@big-andy-coates](https://togithub.com/big-andy-coates) put in a lot of effort to come up with an appropriate solution, including fully implementing and comparing 2 approaches.\nUltimately, we decided that introducing a new `type()` method on `ArgumentMatcher` is the best solution.\nAs a result, it is now possible to update your custom matchers to implement varargs support, if you so desire.\nNote that `ArgumentMatcher` is still a `@FunctionalInterface` and can therefore still be written as a lambda.\n\nMassive thanks to community member [@big-andy-coates](https://togithub.com/big-andy-coates) who implemented this change.\n\n##### What is the effect of this new method?\n\nFor varargs methods, there was previously a way to only match zero arguments, or two or more arguments, by using the exact number of matchers, i.e.\n\n```java\nlong call(String... args);\n\n// Will match calls with exactly zero arguments:\nwhen(mock.call()).thenReturn(0L);\n\n// Will match calls with exactly two arguments:\nwhen(mock.call(any(), any())).thenReturn(0L);\n```\n\nBut following the pattern to match exactly one argument:\n\n```java\nwhen(mock.call(any())).thenReturn(0L);\n```\n\ndoesn't work, as `any` is \"vararg aware\", so Mockito matched the `any` against *each element* of the varargs parameter, meaning it will match any number of arguments, i.e. the above would of matched all of these:\n\n```java\nmock.call();\nmock.call(\"a\");\nmock.call(\"a\", \"b\");\n```\n\nWith the new `type` method, it's now possible to differentiate matching calls with any exact number of arguments, or to match any number of arguments.\n\n```java\n// Match any number of arguments:\nwhen(mock.call(any(String[].class))).thenReturn(1L);\n// Match invocations with no arguments:\nwhen(mock.call()).thenReturn(1L);\n// Match invocations with exactly one argument:\nwhen(mock.call(any())).thenReturn(1L);\n// Alternative to match invocations with exactly one argument:\nwhen(mock.call(any(String.class))).thenReturn(1L);\n// Match invocations with exactly two arguments:\nwhen(mock.call(any(), any())).thenReturn(1L);\n```\n\nTherefore, if you want to match 0 or more arguments, use `any(String[].class)`.\nIf you want to match an exact number of arguments, use `any(String.class)` (and specify as many `any` matchers as arguments you want to match on).\n\nIn a similar fashion, the behavior of `ArgumentCaptor.forClass` has changed as well.\nIf you want to capture all arguments, use an `ArgumentCaptor` for `String[]`, otherwise `String`:\n\n```java\n// Will capture 1 string\n@Captor private ArgumentCaptor captor;\n// Will capture all strings\n@Captor private ArgumentCaptor captor;\n```\n\nFor more information, see the description and conversation in [pull request 2835](https://togithub.com/mockito/mockito/pull/2835) and [pull request 2807](https://togithub.com/mockito/mockito/pull/2807).\n\n##### Do I need to implement this new method?\n\nNo, you don't need to.\nMockito 5 declares a default implementation, returning `Void.type` as the type of an `ArgumentMatcher`.\nThis essentially means that Mockito will not consider the type when handling varargs.\nHowever, if you do return a specific type, Mockito will consider this when matching arguments.\nAs a result, this new method is not a source-breaking change, but is a bytecode-breaking change.\nAll code working on Mockito 4 should work as-is when recompiled with Mockito 5.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "cleanup enqueue", "number": 4641, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4641"}
{"title": "Update read-only API access", "number": 4642, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4642", "body": "Added:\n- getVideoChannelsForMe\n- getVideoChannel\n- getRecording\n- getSlackTeams\n- getSlackConfiguration\n- getSlackChannels\nFrom slack:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4642#pullrequestreview-1279990077", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4642#pullrequestreview-1280017683", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4642#pullrequestreview-1280017788", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4642#pullrequestreview-1280049349", "body": ""}
{"title": "IntelliJ Thread with injected theme", "number": 4643, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643", "body": "Base styling for Discussion Thread. More of a test on how integrated web views can look.\nDoes not replicate IntelliJ Designs atm.\nTODO: Import font from editor or manually import from file.\n"}
{"comment": {"body": "This PR for IntelliJ has a bunch of duplicated code. The question was if / we want to dedupe right now?\r\n\r\nMy thought was this is still in the \"trial\" phase of web view. \r\nIf we've decided to actually use web views, I will immediately go in and dedupe the components. Didn't want to spend the time with the refactor if we decide *not* to re-use the components.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#issuecomment-1414217998"}}
{"comment": {"body": "> This PR for IntelliJ has a bunch of duplicated code. The question was if / we want to dedupe right now?\r\n> \r\n> My thought was this is still in the \"trial\" phase of web view. If we've decided to actually use web views, I will immediately go in and dedupe the components. Didn't want to spend the time with the refactor if we decide _not_ to re-use the components.\r\n\r\nThat 100% makes sense", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#issuecomment-1414218877"}}
{"comment": {"body": "Very nice \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#issuecomment-1414273080"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#pullrequestreview-1280032342", "body": ""}
{"comment": {"body": "IIRC we ended up with separate CodeBlock components for web/vscode because they were structured/styled so differently. If this is basically the same as vscode I wonder if we should move a base component under `shared/`. Or.. a `shared-ide/` ??? (I guess this is more of a general point for all the components in this PR)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#discussion_r1093843337"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#pullrequestreview-1280050700", "body": ""}
{"comment": {"body": "I've been planning on adding a shared space for IDE code.  Maybe we need a `/shared/ide` folder that will have children of different kinds:\r\n\r\n`/shared/ide/stores` -- I think a lot of streams and caches that live in VSCode should end up here\r\n`/shared/ide/components` -- shared views?\r\n`/shared/ide/discussion` / `/shared/ide/insights` -- if we want domain-specific folders?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#discussion_r1093856783"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#pullrequestreview-1280063704", "body": ""}
{"comment": {"body": "I'm still not 100% sure on CodeBlock but in general, agree with Matt.\r\n\r\nThere's going to be general shared components but also a subset that's specific to IDEs but not web.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#discussion_r1093866405"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#pullrequestreview-1281612405", "body": ""}
{"comment": {"body": "This seems like a no-op ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#discussion_r1094895221"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#pullrequestreview-1281618052", "body": ""}
{"comment": {"body": "Overall I do agree with Kay, this feels like a lot of duplicated code.  I'll leave it up to you to figure out when we should dedupe.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#discussion_r1094898189"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#pullrequestreview-1281622771", "body": ""}
{"comment": {"body": "Another one to dedupe -- this one could maybe even go in shared webComponents?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#discussion_r1094903086"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4643#pullrequestreview-1281790302", "body": ""}
{"title": "fix(deps): update dependency react-merge-refs to v2", "number": 4644, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4644", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| react-merge-refs | ^1.1.0 -> ^2.0.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ngregberge/react-merge-refs\n\n### [`v2.0.1`]()\n\n[Compare Source]()\n\n### [`v2.0.0`]()\n\n[Compare Source]()\n\n#####  BREAKING CHANGES\n\n-   The package now exports ESM only and a named export.\n\n**Before:**\n\n```js\nimport mergeRefs from \"react-merge-refs\";\n```\n\n**After:**\n\n```js\nimport { mergeRefs } from \"react-merge-refs\";\n```\n\n##### Features\n\n-   ESM only & named export ([a928d61]())\n\n##### Bug Fixes\n\n-   fix incorrect CJS / ESM distribution ([#17]()) ([048c4ea]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Backfill Identity Names", "number": 4645, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4645", "body": "Backfill of null displayName column on Identity from the optionally associated customDisplayName column on Person."}
{"title": "Added clockwise stop words", "number": 4646, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646", "body": "@davidkwlam @rasharab @dennispi : excludes specific topics based on the clockwise test. we will make these tools better as we onboard more partners."}
{"comment": {"body": "@rasharab + @davidkwlam a pretty amazing dynamic duo. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#issuecomment-1412896102"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#pullrequestreview-1280038355", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#pullrequestreview-1280040797", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#pullrequestreview-1280040884", "body": "Minor question"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#pullrequestreview-1280041572", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#pullrequestreview-1280042474", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#pullrequestreview-1280046848", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#pullrequestreview-1280050101", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#pullrequestreview-1280050333", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4646#pullrequestreview-1280052075", "body": ""}
{"title": "Try removing slack histogram", "number": 4647, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4647"}
{"title": "Start moving GitHub-specific logic to client-scm", "number": 4648, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4648"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4648#pullrequestreview-1280113670", "body": ""}
{"title": "Identity migration: skip remapping member if no need", "number": 4649, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4649"}
{"title": "Remove ThreadParticipantModel", "number": 465, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/465", "body": "Cleanup following: https://github.com/NextChapterSoftware/unblocked/pull/438"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/465#pullrequestreview-899434103", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/465#pullrequestreview-899489038", "body": ""}
{"title": "Re-enable main compat check", "number": 4650, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4650"}
{"title": "More Clockwise filter words", "number": 4651, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4651", "body": "It looks like their Slack channels are seriously impacting the PowerML results? If you look at the histogram and bert as a comparsion -- which has some good topics in there?"}
{"comment": {"body": "@cancelself they have all slack channels enabled, so I wouldn't be surprised if we're including their equivalent of `random`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4651#issuecomment-1412976971"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4651#pullrequestreview-1280098778", "body": ""}
{"title": "shuffle mapping", "number": 4652, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4652"}
{"title": "Facepalm", "number": 4653, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4653"}
{"title": "Identity uniqueness enforced by DB constraint", "number": 4654, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4654"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4654#pullrequestreview-1280105483", "body": ""}
{"comment": {"body": "Possible this doesn't replace the old index but instead creates a new one, so might be worth checking and dropping the old (non-unique) index after this rolls out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4654#discussion_r1093893992"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4654#pullrequestreview-1280106919", "body": ""}
{"comment": {"body": "Also! If the above is true, it's possible this new index has the same name as the old one, in which case it might not even create it. But hopefully exposed is smart enough to just modify the index, if that is possible", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4654#discussion_r1093895142"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4654#pullrequestreview-1280107053", "body": ""}
{"comment": {"body": "Had to split up the change actually. You cannot simultaneously mark a field as non-null and change its unique index constraint. Closed and reopen 2 PRs.\n\n\n\nI'll check the DB to see if the old index is nuked \ud83d\udc4d", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4654#discussion_r1093895238"}}
{"title": "Remove VSCode declaration that we require badges proposal", "number": 4655, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4655", "body": "This causes an error on startup, and I'm wondering if it is causing one of our customer's VSCode issues.  Since VSCode shipped the proposal we don't need these."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4655#pullrequestreview-1280128888", "body": ""}
{"title": "Identity externalTeamId is non-null", "number": 4656, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4656"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4656#pullrequestreview-1280108512", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4656#pullrequestreview-1280108620", "body": ""}
{"title": "Identity uniqueness enforced by DB constraint", "number": 4657, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4657", "body": "Wait for parent PR to be deployed first."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4657#pullrequestreview-1280109164", "body": ""}
{"title": "Re-enable team member sort in admin web", "number": 4658, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4658", "body": "It probably broke due to duplicate identities and members.\nAdded a tie-breaker on UUID just in case.\nAlso cleanup unused migrations."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4658#pullrequestreview-1280113221", "body": ""}
{"title": "Add missing dependencies to callback", "number": 4659, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4659", "body": "There were missing dependencies in the creation command, causing us to miss added contributors to invite during the note creation. (Specifically, the contributors list wasn't being properly updated causing the lookup to be incomplete during the check for emails to invite)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4659#pullrequestreview-1280153310", "body": ""}
{"comment": {"body": "This is the only code change, the rest of the diff comes from prettier", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4659#discussion_r1093927795"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4659#pullrequestreview-1280341197", "body": ""}
{"title": "Fix hostnames in ingress config", "number": 466, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/466", "body": "Since we are using CNAMES I had to add a few alternate hostnames. Tested it manually and it works!\nAlso added Ingress deploy permission for CI/CD"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/466#pullrequestreview-899487431", "body": ""}
{"title": "AddPowerMLMapping", "number": 4660, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4660", "body": "Add powerml mapping framework for topics\nAdd power ml stuff"}
{"title": "powerml topic permissions", "number": 4661, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4661"}
{"title": "Filter out slack bot messsages", "number": 4662, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4662"}
{"title": "slack message replies verifier", "number": 4663, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4663"}
{"title": "Fix message replies handling", "number": 4664, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4664"}
{"title": "we are hitting memory overcommitment errors. This should address that", "number": 4665, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4665"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4665#pullrequestreview-1280390115", "body": ""}
{"title": "Limit the number of concurrent service deploys", "number": 4666, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4666", "body": "We went from 2 services 1 year ago to 18 services today. This is causing overcommitment alarms on Kubernetes. \nThis change would limit the number of concurrent deploy jobs to help avoid those. Otherwise we have to waste a lot of resources sitting idle most of the time just to accommodate deployments. \nIf this turns out to be very slow I will change the roll-over percentage in our helm chart to 100%"}
{"title": "Bump http-cache-semantics from 4.1.0 to 4.1.1 in /infrastructure/cdk/core", "number": 4667, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4667", "body": "Bumps http-cache-semantics from 4.1.0 to 4.1.1.\n\nCommits\n\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "set the default priority level for slack messages to notice", "number": 4668, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4668", "body": "This change is to suppress debug messages on security alarm channels:\n\nGrafana escalation already has it set to notice \nChanges have been deployed to both Dev and Prod"}
{"title": "Attempt serialized transactions", "number": 4669, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4669"}
{"title": "Update server sub-domain and base path", "number": 467, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/467", "body": "for routing\n/api/channels  pusher-service\n/api/logs      logs-service\n/api/etc       etc-service\n/api/*         api-service"}
{"comment": {"body": "I updated the client app settings, where we override these for different environments.  The code here is a bit messy as it's integrated into the build settings.  We can merge this in as-is and I will clean this up in a follow-on PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/467#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/467#pullrequestreview-*********", "body": ""}
{"title": "Alter resource requirements", "number": 4670, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4670"}
{"title": "chore(deps): update kotlin monorepo to v1.8.10", "number": 4671, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4671", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.jetbrains.kotlin.plugin.jpa (source) | 1.7.20 -> 1.8.10 |  |  |  |  |\n| org.jetbrains.kotlin.plugin.serialization (source) | 1.7.20 -> 1.8.10 |  |  |  |  |\n| org.jetbrains.kotlin.jvm (source) | 1.7.20 -> 1.8.10 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nJetBrains/kotlin\n\n### [`v1.8.0`]()\n\n##### Analysis API\n\n-   [`KT-50255`]() Analysis API: Implement standalone mode for the Analysis API\n\n##### Analysis API. FIR\n\n-   [`KT-54292`]() Symbol Light classes: implement PsiVariable.computeConstantValue for light field\n-   [`KT-54293`]() Analysis API: fix constructor symbol creation when its accessed via type alias\n\n##### Android\n\n-   [`KT-53342`]() TCS: New AndroidSourceSet layout for multiplatform\n-   [`KT-53013`]() Increase AGP compile version in KGP to 4.1.3\n-   [`KT-54013`]() Report error when using deprecated Kotlin Android Extensions compiler plugin\n-   [`KT-53709`]() MPP, Android SSL2: Conflicting warnings for `androidTest/kotlin` source set folder\n\n##### Backend. Native. Debug\n\n-   [`KT-53561`]() Invalid LLVM module: \"inlinable function call in a function with debug info must have a !dbg location\"\n\n##### Compiler\n\n##### New Features\n\n-   [`KT-52817`]() Add `@JvmSerializableLambda` annotation to keep old behavior of non-invokedynamic lambdas\n-   [`KT-54460`]() Implementation of non-local break and continue\n-   [`KT-53916`]() Support Xcode 14 and new Objective-C frameworks in Kotlin/Native compiler\n-   [`KT-32208`]() Generate method annotations into bytecode for suspend lambdas (on invokeSuspend)\n-   [`KT-53438`]() Introduce a way to get SourceDebugExtension attribute value via JVMTI for profiler and coverage\n\n##### Performance Improvements\n\n-   [`KT-53347`]() Get rid of excess allocations in parser\n-   [`KT-53689`]() JVM: Optimize equality on class literals\n-   [`KT-53119`]() Improve String Concatenation Lowering\n\n##### Fixes\n\n-   [`KT-53465`]() Unnecessary checkcast to array of reified type is not optimized since Kotlin 1.6.20\n-   [`KT-49658`]() NI: False negative TYPE_MISMATCH on nullable type with `when`\n-   [`KT-48162`]() NON_VARARG_SPREAD isn't reported on \\*toTypedArray() call\n-   [`KT-43493`]() NI: False negative: no compilation error \"Operator '==' cannot be applied to 'Long' and 'Int'\" is reported in builder inference lambdas\n-   [`KT-54393`]() Change in behavior from 1.7.10 to 1.7.20 for java field override.\n-   [`KT-55357`]() IllegalStateException when reading a class that delegates to a Java class with a definitely-not-null type with a flexible upper bound\n-   [`KT-55068`]() Kotlin Gradle DSL: No mapping for symbol: VALUE_PARAMETER SCRIPT_IMPLICIT_RECEIVER on JVM IR backend\n-   [`KT-51284`]() SAM conversion doesn't work if method has context receivers\n-   [`KT-48532`]() Remove old JVM backend\n-   [`KT-55065`]() Kotlin Gradle DSL: Reflection cannot find class data for lambda, produced by JVM IR backend\n-   [`KT-53270`]() K1: implement synthetic Enum.entries property\n-   [`KT-52823`]() Cannot access class Thread.State after upgrading to 1.7 from 1.6.1 using -Xjdk-release=1.8\n-   [`KT-55108`]() IR interpreter: Error occurred while optimizing an expression: VARARG\n-   [`KT-53547`]() Missing fun IrBuilderWithScope.irFunctionReference\n-   [`KT-54884`]() \"StackOverflowError: null\" caused by Enum constant name in constructor of the same Enum constant\n-   [`KT-47475`]() \"IncompatibleClassChangeError: disagree on InnerClasses attribute\": cross-module inlined WhenMappings has mismatched InnerClasses\n-   [`KT-55013`]() State checker use-after-free with XCode 14.1\n-   [`KT-54802`]() \"VerifyError: Bad type on operand stack\" for inline functions on arrays\n-   [`KT-54707`]() \"VerifyError: Bad type on operand stack\" in inline call chain on a nullable array value\n-   [`KT-48678`]() Coroutine debugger: disable \"was optimised out\" compiler feature\n-   [`KT-54745`]() Restore KtToken constructors without tokenId parameter to preserve back compatibility\n-   [`KT-54650`]() Binary incompatible ABI change in Kotlin 1.7.20\n-   [`KT-52786`]() Frontend / K2: IndexOutOfBoundsException when opting in to K2\n-   [`KT-54004`]() Builder type inference does not work correctly with variable assignment and breaks run-time\n-   [`KT-54581`]() JVM: \"VerifyError: Bad type on operand stack\" with generic inline function and `when` inside try-catch block\n-   [`KT-53794`]() IAE \"Unknown visibility: protected/*protected and package*/\" on callable reference to protected member of Java superclass\n-   [`KT-54600`]() NPE on passing nullable Kotlin lambda as Java's generic SAM interface with `super` type bound\n-   [`KT-54463`]() Delegating to a field with a platform type causes java.lang.NoSuchFieldError: value$delegate\n-   [`KT-54509`]() Ir Interpreter: unable to evaluate string concatenation with \"this\" as argument\n-   [`KT-54615`]() JVM: Internal error in file lowering: java.lang.AssertionError: Error occurred while optimizing an expression\n-   [`KT-53146`]() JVM IR: unnecessary checkcast of null leads to NoClassDefFoundError if the type isn't available at runtime\n-   [`KT-53712`]() Add mode to prevent generating JVM 1.8+ annotation targets (TYPE_USE, TYPE_PARAMETER)\n-   [`KT-54366`]() K2: no JVM BE specific diagnostics (in particular CONFLICTING_JVM_DECLARATIONS) in 1.8\n-   [`KT-35187`]() NullPointerException on compiling suspend inline fun with typealias to suspend function type\n-   [`KT-54275`]() K2: \"IllegalArgumentException: KtParameter is not a subtype of class KtAnnotationEntry for factory REPEATED_ANNOTATION\"\n-   [`KT-53656`]() \"IllegalStateException: typeParameters == null for SimpleFunctionDescriptorImpl\" with recursive generic type parameters\n-   [`KT-46727`]() Report warning on contravariant usages of star projected argument from Java\n-   [`KT-53197`]() K2: 'init' hides member of supertype 'UIComponent' and needs 'override' modifier\n-   [`KT-53867`]() K2: `@JvmRecord` does not compile to a java record\n-   [`KT-53964`]() K2 is unable to work with Java records\n-   [`KT-53349`]() K2: TYPE_MISMATCH caused by non-local return\n-   [`KT-54100`]() \"Type variable TypeVariable(P) should not be fixed\" crash in code with errors\n-   [`KT-54212`]() K2: cannot calculate implicit property type\n-   [`KT-53699`]() K2: Exception during IR lowering in code with coroutines\n-   [`KT-54192`]() Warn about unsupported feature on generic inline class parameters\n-   [`KT-53723`]() Friend modules aren't getting passed to cache build during box tests\n-   [`KT-53873`]() K2: Duplicated diagnostics reported from user type ref checkers\n-   [`KT-50909`]() \"VerifyError: Bad type on operand stack\" caused by smartcasting for nullable inline class property in class\n-   [`KT-54115`]() Restore Psi2IrTranslator constructor from 1.7.20\n-   [`KT-53908`]() K2: Self-referencing generics in Java class causes New Inference Error (IE: class Foo\\>)\n-   [`KT-53193`]() K2: compile error on project that compiles fine with normal 1.7.10\n-   [`KT-54062`]() K2 Invalid serialization for type-aliased suspend function type with extension receiver\n-   [`KT-53953`]() Forbid usages of super or super if in fact it accesses an abstract member\n-   [`KT-47473`]() NI: Missed UPPER_BOUND_VIOLATED diagnostics if use type aliases with type parameters\n-   [`KT-54049`]() K2: false positive MANY_IMPL_MEMBER_NOT_IMPLEMENTED\n-   [`KT-30054`]() Wrong approximation if nullable anonymous object with implemented interface is used\n-   [`KT-53751`]() Postpone IgnoreNullabilityForErasedValueParameters feature\n-   [`KT-53324`]() Implement Enum.entries lowering on K/N\n-   [`KT-44441`]() K2: report redeclaration error if there is a Java class with the same name as the Kotlin class\n-   [`KT-53807`]() No warning about declaringClass on an enum value\n-   [`KT-53493`]() K2: `val on function parameter` counts as just warning\n-   [`KT-53435`]() K2: \"IllegalArgumentException: class KtValueArgument is not a subtype of class KtExpression for factory ANNOTATION_ARGUMENT_MUST_BE_CONST\" if string in nested annotation is concatenated\n-   [`KT-52927`]() AssertionError: LambdaKotlinCallArgumentImpl\n-   [`KT-53922`]() Make Enum.entries unstable feature to poison binaries\n-   [`KT-53783`]() Exception during psi2ir when declaring expect data object\n-   [`KT-53622`]() \\[OVERLOAD_RESOLUTION_AMBIGUITY] when enum entry called 'entries' is present in K2\n-   [`KT-41670`]() JVM IR: AbstractMethodError when using inheritance for fun interfaces\n-   [`KT-53178`]() K2: implement diagnostics for serialization plugin\n-   [`KT-53804`]() Restore old and incorrect logic of generating InnerClasses attributes for kotlin-stdlib\n-   [`KT-52970`]() Default value constant in companion object works on JVM and JS, but fails on native\n-   [`KT-51114`]() FIR: Support DNN checks\n-   [`KT-27936`]() Write InnerClasses attribute for all class names used in a class file\n-   [`KT-53719`]() Parsing regression on function call with type arguments and labeled lambda\n-   [`KT-53261`]() Evaluate effect from  inline for primitive types\n-   [`KT-53706`]() K2: Context receivers are not resolved on properties during type resolution stage\n-   [`KT-39492`]() Kotlin.Metadata's packageName field cannot be an empty string\n-   [`KT-53664`]() Ir Interpreter: unable to evaluate name of function reference marked with JvmStatic from another module\n-   [`KT-52478`]() \\[Native] Partial linkage: Building native binary from cached KLIBs fails if one library depends on removed nested callable member from another one\n-   [`KT-48822`]() CompilationException: Back-end (JVM) Internal error: Failed to generate expression: KtProperty - ConcurrentModificationException\n-   [`KT-50281`]() IllegalStateException: unsupported call of reified inlined function\n-   [`KT-50083`]() Different error messages in android and JVM (Intrinsics.checkNotNullParameter).\n-   [`KT-53236`]() Support Enum.entries codegen on JVM/IR BE\n-   [`KT-41017`]() FIR: should we support smartcast after null check\n-   [`KT-53202`]() \"ISE: Descriptor can be left only if it is last\" after direct invoke optimization on a capturing lambda\n-   [`KT-46969`]() `@BuilderInference` with nested DSL scopes cause false-positive scope violation in Kotlin 1.5\n-   [`KT-53257`]() FIR: Improper context receiver argument is chosen when there are two extension receiver candidates\n-   [`KT-53090`]() Anonymous function and extension function literals are generated as classes even with -Xlambdas=indy\n-   [`KT-53208`]() K2: Cannot get annotation for default interface method parameter when compiled with `-Xuse-k2`\n-   [`KT-53184`]() K2: NoSuchMethodError on KProperty1.get() referenced via nullable typealias\n-   [`KT-53198`]() K2: Return type mismatch: expected kotlin/Unit, actual kotlin/Unit?\n-   [`KT-53100`]() Optimization needed: (CONSTANT_PRIMITIVE(x: T?)) => x\n-   [`KT-49875`]() \\[FIR] Support infering PRIVATE_TO_THIS visibility\n-   [`KT-53024`]() Refactor FIR renderer to composable architecture\n-   [`KT-50995`]() \\[FIR] Support SAM with receiver plugin\n-   [`KT-53148`]() K1: introduce warning for inline virtual member in enum\n-   [`KT-49847`]() Devirtualization fails to eliminate boxing in function reference context\n-   [`KT-52875`]() Extension function literal creation with `-Xlambdas=indy` fails with incorrect arguments\n-   [`KT-53072`]() INVALID_IF_AS_EXPRESSION error isn't shown in the IDE (LV 1.8)\n-   [`KT-52985`]() Native: a function with type `T?` returned a `kotlin.Unit` instead of `null`\n-   [`KT-52020`]() FIR warning message includes internal rendering\n-   [`KT-48778`]() -Xtype-enhancement-improvements-strict-mode not respecting `@NonNull` annotation for property accesses?\n\n##### IDE\n\n##### Fixes\n\n-   [`KTIJ-22357`]() CCE class org.jetbrains.kotlin.fir.types.impl.FirImplicitTypeRefImpl cannot be cast to class org.jetbrains.kotlin.fir.types.FirResolvedTypeRef in K2\n-   [`KT-55150`]() Argument for `@NotNull` parameter 'scope' of org/jetbrains/kotlin/resolve/AnnotationResolverImpl.resolveAnnotationType must not be null\n-   [`KTIJ-22165`]() IDE notification to promote users to migrate to the new Kotlin/JS toolchain\n-   [`KTIJ-22166`]() IDE notification (or something else) about JPS and Maven support for Kotlin/JS is deprecated\n-   [`KT-53543`]() Rework light classes for file facade\n-   [`KT-48773`]() Investigate the possibility of removing dependency on old JVM backend in light classes\n-   [`KTIJ-19699`]() IDE: False positive type mismatch in Java code for Kotlin nested class non-direct inheritor from external library\n-   [`KT-51101`]() FIR IDE: Exception on \"Show Type Info\" action\n-   [`KTIJ-22295`]() MPP, IDE: False positive UPPER_BOUND_VIOLATED when JVM module implements the generic interface from MPP module and the type parameter is not equal to itself.\n-   [`KT-51656`]() FIR IDE: ProgressCancelled exception is masked in the compiler during resolve\n-   [`KT-51315`]() FIR IDE: move out base modules from fe10 plugin to reuse in k2 plugin\n-   [`KTIJ-22323`]() K2: ISE during resolve of stdlib calls from the stdlib\n-   [`KTIJ-21391`]() Generate -> Override methods : don't delegate to abstract methods\n-   [`KT-53097`]() Extract common part of light classes to another module\n-   [`KTIJ-22354`]() FIR LC: annotation owner is always null\n-   [`KTIJ-22157`]() Kotlin call resolver leaks user code when reporting exception\n\n##### IDE. Completion\n\n-   [`KTIJ-22552`]() Kotlin: 'for loop' postfix completion doesn't work - \"Fe10SuggestVariableNameMacro must be not requested from main classloader\"\n-   [`KTIJ-22503`]() Support code completion for data objects\n\n##### IDE. Debugger\n\n-   [`KT-51755`]() Compilation exception with scripting compilation during debug session\n-   [`KTIJ-21963`]() Debugger / IR: Expression evaluation of the debugger doesn't work\n\n##### IDE. Decompiler, Indexing, Stubs\n\n-   [`KTIJ-22750`]() Initialize Kotlin stub element types lazily\n-   [`KTIJ-18094`]() IDE: \"AssertionError: Stub count doesn't match stubbed node length\" with minified Android AAR library\n-   [`KTIJ-17632`]() IndexOutOfBoundsException: Cannot decompile a class located in minified AAR\n\n##### IDE. Gradle Integration\n\n-   [`KT-48135`]() In the IDE import, reuse dependency granular source set KLIBs across multi-project build to avoid duplicate external libraries\n-   [`KTIJ-22345`]() False positive unresolved reference for members of subclasses of expect classes.\n-   [`KT-53514`]() HMPP: False positive for `None of the following functions can be called with the arguments supplied.` with Enum in common module\n-   [`KT-51583`]() Gradle 7.4+ | SamplesVariantRule interference: Could not resolve all files for configuration ':kotlinKlibCommonizerClasspath'\n-   [`KTIJ-21077`]() Dependency matrix does not work with Jetpack compose / multiplatform projects\n\n##### IDE. Inspections and Intentions\n\n-   [`KTIJ-19531`]() Adapt changes about new rules for method implementation requirements\n-   [`KTIJ-22087`]() Support IDE inspections for upcoming data objects\n-   [`KTIJ-20510`]() Quick fix to implement and call correct super method in case of inheritance with defaults\n-   [`KTIJ-20170`]() Provide quickfix for deprecated resolution to private constructor of sealed class\n-   [`KTIJ-22630`]() FIR IDE: Lazy resolve exception after invocation of `Override members` action on value class\n-   [`KT-49643`]() Intentions: \"Implement members\" fails when base type function declaration uses unresolved generic types\n\n##### IDE. JS\n\n-   [`KTIJ-22167`]() Make JS IR default in projects created by wizard\n-   [`KTIJ-22332`]() Wizard: Kotlin/JS projects: cssSupport DSL should be updated\n\n##### IDE. KDoc\n\n-   [`KTIJ-22324`]() K2 IDE: implement reference resolve inside KDocs\n\n##### IDE. Multiplatform\n\n-   [`KTIJ-19566`]() New Project Wizard: Update HMPP-related flags in multiplatform wizards\n\n##### IDE. Navigation\n\n-   [`KT-51314`]() FIR IDE: show Kotlin declarations in search symbol\n-   [`KTIJ-22755`]() Find usage for constructor from kotlin library doesn't work for secondary constructor usages\n\n##### IDE. Script\n\n-   [`KTIJ-22598`]() Add warning for standalone scripts in source roots\n-   [`KT-54325`]() .settings.gradle.kts and .init.gradle.kts are reported as standalone scripts\n\n##### IDE. Structural Search\n\n-   [`KTIJ-21986`]() KSSR: \"CodeFragment with non-kotlin context should have fakeContextForJavaFile set:  originalContext = null\" warning shows up when replacing\n\n##### IDE. Tests Support\n\n-   [`KT-50269`]() FIR IDE: Allow running tests via gutter\n\n##### IDE. Wizards\n\n-   [`KTIJ-23537`]() Wizard: projects with Android modules require higher sdkCompileVersion\n-   [`KTIJ-23525`]() Wizard: Compose multiplatform: project won't build and require higher compileSdkVersion\n-   [`KTIJ-22763`]() New Project Wizard: remove deprecated Android extensions plugin from Android target in the project constructor\n-   [`KTIJ-22481`]() Wizard: Kotlin -> Browser application (gradle groove). Build error\n\n##### JavaScript\n\n##### Fixes\n\n-   [`KT-55097`]() KJS / IR + IC: Using an internal function from a friend module throws an unbound symbol exception\n-   [`KT-54406`]() Kotlin/JS: build with dependencies fails with \"Could not find \"kotlin\" in \\[~/.local/share/kotlin/daemon]\"\n-   [`KT-53074`]() Make JS IR BE default in toolchain (gradle & CLI)\n-   [`KT-50589`]() UTF-8 Instability in kotlin.js.map\n-   [`KT-54934`]() KJS / IR + IC: Suspend abstract function stubs are generated with unstable lowered ic signatures\n-   [`KT-54895`]() KJS / IR + IC: broken cross module references for function default param wrappers\n-   [`KT-54520`]() KJS / IR Allow IdSignature clashes\n-   [`KT-54120`]() JS IR + IC: pointless invalidation of dependent code after modifying companions\n-   [`KT-53986`]() KJS / IR + IC: compiler produces different JS file names with IC and without IC\n-   [`KT-54010`]() JS IR + IC: Force IC cache invalidation after updating language version or features\n-   [`KT-53931`]() KJS / Gradle: Regression with 1.7.20-RC: ReferenceError: println is not defined\n-   [`KT-53968`]() Kotlin/JS: no UninitializedPropertyAccessException on access to non-initialized lateinit property defined in dependencies\n-   [`KT-54686`]() KJS / IR: Incorrect generation of signatures when one of argument is nested class\n-   [`KT-54479`]() KJS / IR + IC: Adding or removing companion fields leads java.lang.IllegalStateException in the compiler IC infrastructure\n-   [`KT-54382`]() KJS / IR: Wrong type check for inheritors of suspend functions\n-   [`KT-54323`]() KJS / IR + IC: Intrinsics from stdlib may lose their dependencies in incremental rebuild\n-   [`KT-53361`]() KJS / IR: No debug info is generated for in-line js code\n-   [`KT-53321`]() Implement Enum.entries lowering on JS/IR\n-   [`KT-53112`]() KJS IR turn on IC infra by default\n-   [`KT-50503`]() Kotlin/JS: IR + IC: compileTestDevelopmentExecutableKotlinJs fails with ISE: \"Could not find library\" after removing module dependency\n-   [`KT-54011`]() JS IR + IC: EnumEntries don't work well when IC is enabled\n-   [`KT-53672`]() KJS / IR: \"IndexOutOfBoundsException: Index 0 out of bounds for length 0\" caused by function reference to extension function of reified type variable\n-   [`KT-43455`]() KJS: IR. Incremental compilation problem with unbound symbols\n-   [`KT-53539`]() KJS: Exported class inherited non-exported class shows warning\n-   [`KT-53443`]() KJS/IR: NullPointerException caused by anonymous objects inside lambdas\n-   [`KT-52795`]() K/JS and K/Native IR-validation/compilation errors for a valid kotlin code\n-   [`KT-52805`]() KJS/IR: Invalid call of inline function in `also` block\n-   [`KT-51151`]() KJS / IR: Wrong overloaded generic method with receiver is called\n-   [`KT-52830`]() KJS/IR: Sourcemap disabling doesn't work\n-   [`KT-52968`]() KJS / IR: Buggy generation of overridden methods\n-   [`KT-53063`]() KJS / IR + IC: undefined cross module reference for implemented interface functions\n-   [`KT-51099`]() KJS / IR + IC: Cache invalidation doesn't check generic class variance annotations (in, out)\n-   [`KT-51090`]() KJS / IR + IC: Cache invalidation doesn't check suspend qualifier\n-   [`KT-51088`]() KJS / IR + IC: Cache invalidation doesn't check class qualifiers (data, inline)\n-   [`KT-51083`]() KJS / IR + IC: Cache invalidation doesn't check inline function which was non inline initially\n-   [`KT-51896`]() KJS / IR + IC: Cache invalidation doesn't trigger rebuild for fake overridden inline functions\n\n##### Language Design\n\n-   [`KT-48385`]() Deprecate confusing grammar in when-with-subject\n-   [`KT-48516`]() Forbid `@Synchronized` annotation on suspend functions\n-   [`KT-41886`]() Ability to require opt-in for interface implementation, but not for usage\n-   [`KT-34943`]() OVERLOAD_RESOLUTION_AMBIGUITY inconsistent with the equivalent Java code\n-   [`KT-51334`]() Implement type-bound label `this@Type`\n\n##### Libraries\n\n##### New Features\n\n-   [`KT-21007`]() Provide Kotlin OSGI Bundle with extensions for JRE8 (and JRE7)\n-   [`KT-54082`]() Comparable and subtractible TimeMarks\n-   [`KT-52928`]() Provide copyToRecursively and deleteRecursively extension functions for java.nio.file.Path\n-   [`KT-49425`]() Update OptIn documentation to reflect latest design changes\n-   [`KT-54005`]() Allow calling `declaringJavaClass` on Enum\n-   [`KT-52933`]() rangeUntil members in built-in types\n\n##### Performance Improvements\n\n-   [`KT-53508`]() Cache typeOf-related KType instances when kotlin-reflect is used\n\n##### Fixes\n\n-   [`KT-51907`]() Switch JVM target of the standard libraries to 1.8\n-   [`KT-54835`]() Document that Iterable.all(emptyCollection) returns TRUE.\n-   [`KT-54168`]() Expand on natural order in comparator docs\n-   [`KT-53277`]() Stabilize experimental API for 1.8\n-   [`KT-53864`]() Review deprecations in stdlib for 1.8\n-   [`KT-47707`]() Remove the system property and the brittle `contains` optimization code itself\n-   [`KT-52336`]() Different behavior on JVM and Native in stringBuilder.append(charArray, 0, 1)\n-   [`KT-53927`]() Remove deprecation from ConcurrentModificationException constructors\n-   [`KT-53152`]() Introduce EnumEntries to stdlib as backing implementation of Enum.entries\n-   [`KT-53134`]() stdlib > object Charsets > not thread safe lazy initialization\n-   [`KT-51063`]() Gradle project with JPS runner: \"JUnitException: Failed to parse version\" JUnit runner internal error with JUnit\n-   [`KT-52908`]() Native: setUnhandledExceptionHook swallows exceptions\n\n##### Native\n\n-   [`KT-51043`]() Kotlin Native: ObjC-Interop: kotlin.ClassCastException: null cannot be cast to kotlin.Function2\n-   [`KT-50786`]() Native: prohibit suspend calls inside autoreleasepool {}\n-   [`KT-52834`]() Implement test infrastructure for K2/Native\n\n##### Native. C Export\n\n-   [`KT-36878`]() Reverse C Interop: incorrect headers generation for primitive unassigned type arrays\n-   [`KT-53599`]() \\[Reverse C Interop] Provide box/unbox API for unsigned primitive types\n-   [`KT-41904`]() Kotlin/Native : error: duplicate member for interface and function with the same name\n-   [`KT-42830`]() \\[Reverse C Interop] Add API to get value of boxed primitives\n-   [`KT-39496`]() K/N C: optional unsigned types as function parameters crash the compiler\n-   [`KT-39015`]() Cannot compile native library with nullable inline class\n\n##### Native. C and ObjC Import\n\n-   [`KT-54738`]() Cocoapods cinterop: linking platform.CoreGraphics package\n-   [`KT-54001`]() Kotlin/Native: support header exclusion in cinterop def files\n-   [`KT-53151`]() Native: Custom declarations in .def don't work with modules, only headers\n\n##### Native. ObjC Export\n\n-   [`KT-53680`]() Obj-C refinement annotations\n-   [`KT-54119`]() Native: runtime assertion failed due to missing thread state switch\n-   [`KT-42641`]() Don't export generated component\\* methods from Kotlin data classes to Obj-C header\n\n##### Native. Platform Libraries\n\n-   [`KT-54225`]() Native: update to Xcode 14.1\n-   [`KT-54164`]() Native: commonizer fails on CoreFoundation types\n-   [`KT-39747`]() Why is there no WinHttp API in Kotlin/Native's Windows API?\n\n##### Native. Runtime\n\n-   [`KT-49228`]() Kotlin/Native: Allow to unset unhandled exception hook\n-   [`KT-27305`]() Fix **FILE** macro inside `RuntimeCheck` and `RuntimeAssert`\n\n##### Native. Runtime. Memory\n\n-   [`KT-54498`]() Deprecation message of 'FreezingIsDeprecated' is not really helpful\n-   [`KT-53182`]() New memory manager: Unexpected memory usage on IOS\n\n##### Native. Stdlib\n\n-   [`KT-52429`]() Small Usability Improvements for Worker API\n\n##### Reflection\n\n-   [`KT-54629`]() Incorrectly cached class classifier\n-   [`KT-54611`]() `KTypeImpl`  does not take into account class loader from the `classifier` property\n-   [`KT-48136`]() Make `Reflection.getOrCreateKotlinPackage` use cache when `kotlin-reflect` is used\n-   [`KT-50705`]() Use ClassValue to cache KClass objects in kotlin-reflect\n-   [`KT-53454`]() Properly cache the same class's KClass when it's loaded by multiple classloaders in getOrCreateKotlinClass\n\n##### Specification\n\n-   [`KT-54210`]() Update Kotlin specification to mention that since 1.8 generics in value classes are allowed\n\n##### Tools. CLI\n\n-   [`KT-54116`]() Add JVM target bytecode version 19\n-   [`KT-53278`]() Support values 6 and 8 for -Xjdk-release\n-   [`KT-46312`]() CLI: Kotlin runner should use platform class loader to load JDK modules on Java 9+\n\n##### Tools. Commonizer\n\n-   [`KT-54310`]() Commonizer fails on 1.8.0-dev K/N distributions\n-   [`KT-48576`]() \\[Commonizer] platform.posix.pselect not commonized in Ktor\n\n##### Tools. Compiler Plugins\n\n-   [`KT-46959`]() Kotlin Lombok: Support generated builders (`@Builder`)\n-   [`KT-53683`]() Unresolved reference compilation error occurs if a file is annotated with `@` Singular and has any guava collection type :  ImmutableTable, ImmutableList or else\n-   [`KT-53657`]() \\[K2] Unresolved reference compilation error occurs if a field is annotated with `@` Singular and has type NavigableMap without explicit types specification\n-   [`KT-53647`]() \\[K2] Unresolved reference compilation error occurs if a field is annotated with `@` Singular and has type Iterable\n-   [`KT-53724`]() Param of the `@` Singular lombok annotation ignoreNullCollections=true is ignored by kotlin compiler\n-   [`KT-53451`]() \\[K2] References to methods generated by `@` With lombok annotation can't be resolved with enabled K2 compiler\n-   [`KT-53721`]() \\[K2] There is no compilation error while trying to add null as a param of the field with non-null type\n-   [`KT-53370`]() Kotlin Lombok compiler plugin can't resolve methods generated for java boolean fields annotated with `@` With annotation\n\n##### Tools. Compiler plugins. Serialization\n\n-   [`KT-54878`]() JVM/IR:  java.lang.ClassCastException: class org.jetbrains.kotlin.ir.types.impl.IrStarProjectionImpl cannot be cast to class org.jetbrains.kotlin.ir.types.IrTypeProjection on serializer\\>()\n-   [`KT-55340`]() Argument for kotlinx.serialization.UseSerializers does not implement KSerializer or does not provide serializer for concrete type\n-   [`KT-55296`]() Improve exceptions in serialization plugin\n-   [`KT-55180`]() KJS: regression in serialization for Kotlin 1.8.0-beta\n-   [`KT-53157`]() Recursion detected in a lazy value under LockBasedStorageManager in kotlinx.serialization\n-   [`KT-54297`]() Regression in serializable classes with star projections\n-   [`KT-49660`]() kotlinx.serialization: IndexOutOfBoundsException for parameterized sealed class\n-   [`KT-43910`]() JS IR: Serialization with base class: \"IndexOutOfBoundsException: Index 0 out of bounds for length 0\"\n\n##### Tools. Daemon\n\n-   [`KT-52622`]() Kotlin/JS, Kotlin/Common compilations start Kotlin daemon incompatible with Kotlin/JVM compilation on JDK 8\n\n##### Tools. Gradle\n\n##### New Features\n\n-   [`KT-27301`]() Expose compiler flags via Gradle lazy properties\n-   [`KT-53357`]() Change single build metrics property\n-   [`KT-50673`]() Gradle: KotlinCompile task(s) should use `@NormalizeLineEndings`\n-   [`KT-34464`]() Kotlin build report path not clickable in the IDE\n\n##### Performance Improvements\n\n-   [`KT-51525`]() \\[Gradle] Optimize evaluating args for compile tasks\n-   [`KT-52520`]() Remove usage of reflection from CompilerArgumentsGradleInput\n\n##### Fixes\n\n-   [`KT-48843`]() Add ability to disable Kotlin daemon fallback strategy\n-   [`KT-55334`]() kaptGenerateStubs passes wrong android variant module names to compiler\n-   [`KT-55255`]() Gradle: stdlib version alignment fails build on dynamic stdlib version.\n-   [`KT-55363`]() \\[K1.8.0-Beta] Command line parsing treats plugin parameters as source files\n-   [`KT-54993`]() Raise kotlin.jvm.target.validation.mode check default level to error when build is running on Gradle 8+\n-   [`KT-54136`]() Duplicated classes cause build failure if a dependency to kotlin-stdlib specified in an android project\n-   [`KT-50115`]() Setting toolchain via Java extension does not configure 'kotlinOptions.jvmTarget' value when Kotlin compilation tasks are created eagerly\n-   [`KT-55222`]() Migrate AndroidDependencyResolver to the new Gradle API\n-   [`KT-55119`]() There is no validation for different jvmTarget and targetCompatibility values in multiplatform projects with jvm target and used java sources\n-   [`KT-55102`]() Compile java task fails with different target version in pure kotlin project\n-   [`KT-54995`]() \\[1.8.0-Beta] compileAppleMainKotlinMetadata fails on default parameters with `No value passed for parameter 'mustExist'`\n-   [`KT-35003`]() Automatically set targetCompatibility for kotlin-jvm projects to work with gradle 6 metadata\n-   [`KT-45335`]() kotlinOptions.jvmTarget conflicts with Gradle variants\n-   [`KT-48798`]() Android: going from one to more than one productFlavor causes inputs of commonSourceSet$kotlin_gradle_plugin property of compileKotlin task to change\n-   [`KT-55019`]() Gradle sync: UnknownConfigurationException when adding implementation dependencies to a Kotlin with Java compilation\n-   [`KT-55004`]() jvmTarget value is ignored by depending modules if a task \"UsesKotlinJavaToolchain\" is configured for all project modules using allProjects {}\n-   [`KT-54888`]() Add Gradle property to suppress kotlinOptions.freeCompilerArgs modification on execution phase\n-   [`KT-54399`]() Undeprecate 'kotlinOptions' DSL\n-   [`KT-54306`]() Change the naming of newly added Compiler\\*Options classes and interfaces\n-   [`KT-54580`]() KotlinOptions in AbstractKotlinCompilation class are deprecated\n-   [`KT-54653`]() java.lang.NoClassDefFoundError: kotlin/jdk7/AutoCloseableKt exception if a dependency to the kotlin-stdlib is added\n-   [`KT-52624`]() Compatibility with Gradle 7.3 release\n-   [`KT-54703`]() Stdlib substitution does not work with JPMS modules\n-   [`KT-54602`]() Prevent leaking Gradle Compile DSL types into compiler cli runtime\n-   [`KT-54439`]() Project failed to sync Native LaguageSettings to compiler options in afterEvaluate\n-   [`KT-53885`]() Bump minimal supported Gradle version to 6.8.3\n-   [`KT-53773`]() Protect and system properties can contain sensitive data\n-   [`KT-53732`]() Add custom values limits for build scan reports\n-   [`KT-52623`]() Compatibility with Gradle 7.2. release\n-   [`KT-51831`]() Gradle: remove `kotlin.compiler.execution.strategy` system property\n-   [`KT-51679`]() Change deprecation level to error for KotlinCompile setClasspath/getClasspath methods\n-   [`KT-54335`]() Kotlin build report configuration. There is no validation for SINGLE_FILE output if the required kotlin.build.report.single_file property is empty or absent\n-   [`KT-54356`]() Kotlin build report configuration. Wrong path is used for the property kotlin.internal.single.build.metrics.file\n-   [`KT-53617`]() KotlinCompilerExecutionStrategy value  is ignored by depending modules if configure once for all project modules using allProjects {}\n-   [`KT-53823`]() Kotlin Gradle Plugin uses deprecated Gradle API: Provider.forUseAtConfigurationTime()\n-   [`KT-54142`]() Increase Kotlin Gradle plugin Gradle target API to 7.5\n-   [`KT-50161`]() Android variant filter breaks KotlinCompile cache compatibility\n-   [`KT-54113`]() LanguageSettings to KotlinNativeLink.toolOptions sync are executed on the wrong context\n-   [`KT-53830`]() Versions of kotlin-stdlib-jdk8 and  kotlin-stdlib-jdk7 aren't overrided if added as transitive dependencies to kotlin-stdlib\n-   [`KT-54112`]() Missing target input on KotlinNativeLink task\n-   [`KT-45879`]() Documentation: Wrong kotlin languageVersion \"1.6 (EXPERIMENTAL)\"\n-   [`KT-54103`]() Remove JvmTarget.JVM\\_1\\_6 from generated Gradle compiler type\n-   [`KT-52959`]() KMP code is breaking Gradle project isolation\n-   [`KT-50598`]() MULTIPLE_KOTLIN_PLUGINS_SPECIFIC_PROJECTS_WARNING is only shown on first build\n-   [`KT-53246`]() Gradle: Special characters in paths of errors and warnings should be escaped\n-   [`KT-47730`]() How to avoid stdlib coming from Kotlin gradle plugin\n-   [`KT-52209`]() Corrupted cache and non-incremental build if produce caches \"in process\" and restore then compiling with kotlin daemon\n-   [`KT-41642`]() \"TaskDependencyResolveException: Could not determine the dependencies\" when trying to apply stdlib\n-   [`KT-53390`]() Drop usage of -Xjava-source-roots when passing java sources required for Kotlin compilation\n-   [`KT-52984`]() Kotlin Gradle plugin is misbehaving by resolving DomainObjectCollection early\n-   [`KT-38622`]() Non-incremental compilation because of R.jar with Android Gradle plugin 3.6\n-   [`KT-38576`]() AnalysisResult.RetryWithAdditionalRoots crashes during incremental compilation with java classes in classpath\n\n##### Tools. Gradle. Cocoapods\n\n-   [`KT-54314`]() Cocoapods: Signing pod dependency for Xcode 14\n-   [`KT-54060`]() Xcode 14: disable bitcode embedding for Apple frameworks\n-   [`KT-53340`]() Change default linking type for frameworks registered by cocoapods plugin\n-   [`KT-53392`]() Deprecate and delete downloading pod dependencies by direct link\n-   [`KT-53695`]() Build of macOS application fails if a framework is integrated via Cocoapods plugin\n\n##### Tools. Gradle. JS\n\n-   [`KT-53367`]() KJS: Migrate cssSupport API\n-   [`KT-45789`]() KJS / IR: Transitive NPM dependencies are not included in PublicPackageJsonTask output\n-   [`KT-55099`]() K/JS: Second declaration of JS target without compiler type report warning incorrectly\n-   [`KT-52951`]() \\[KGP/JS] Browser test target registration via properties\n-   [`KT-52950`]() KJS: Report if yarn.lock was updated during built\n-   [`KT-53374`]() KJS / Gradle: Implement IDEA sync detection logic via ValueSource to improve configuration cache support\n-   [`KT-53381`]() Kotlin/JS: with erased kotlin-js-store/ and reportNewYarnLock = true the task kotlinUpgradeYarnLock always fails\n-   [`KT-53788`]() KJS / Gradle: Disable Gradle build cache for KotlinJsDce when development mode is enabled\n-   [`KT-53614`]() Kotlin/JS upgrade npm dependencies\n\n##### Tools. Gradle. Multiplatform\n\n##### New Features\n\n-   [`KT-53396`]() Support 'implementation platform()' by gradle kotlin mpp plugin for JVM target\n-   [`KT-40489`]() MPP / Gradle: support BOM (enforcedPlatform) artifacts in source set dependencies DSL\n\n##### Performance Improvements\n\n-   [`KT-52726`]() \\[MPP] Optimize caching/performance/call-sites of 'compilationsBySourceSets'\n\n##### Fixes\n\n-   [`KT-54634`]() MPP: Test Failure causes: `KotlinJvmTest$Executor$execute$1 does not define failure`\n-   [`KT-35916`]() Gradle MPP plugin: Configurations for a main compilation and its default source set have different naming\n-   [`KT-46960`]() Repeated kotlin/native external libraries in project\n-   [`KT-27292`]() MPP: jvm { withJava() }: Gradle build: Java source under Kotlin root is resolved while building, but does not produce output class files\n-   [`KT-34650`]() Naming clash in MPP+Android: androidTest vs androidAndroidTest\n-   [`KT-54387`]() Remove MPP alpha stability warning\n-   [`KT-31468`]() Targets disambiguation doesn't work if a depending multiplatform module uses `withJava()` mode\n-   [`KT-54090`]() Take an Apple test device from the device list\n-   [`KT-54301`]() KotlinToolingVersionOrNull: IllegalArgumentException\n-   [`KT-53256`]() Implement K/N compiler downloading for KPM\n-   [`KT-45412`]() KotlinCompilation: Make sure .kotlinSourceSets and .allKotlinSourceSets include the default source set\n-   [`KT-49202`]() Tests on android target can't be executed in multiplatform project if dependency to kotlin-test framework is provided as a single dependency and tests configured to be executed via Junit5\n\n##### Tools. Gradle. Native\n\n##### New Features\n\n-   [`KT-43293`]() Support Gradle configuration caching with Kotlin/Native\n-   [`KT-53107`]() Add arm64 support for watchOS targets (Xcode 14)\n\n##### Fixes\n\n-   [`KT-53704`]() Native cinterop: eager header path calculation\n-   [`KT-54814`]() Kotlin/Native: Github Actions: Testing watchOSX64 with Xcode 14  Invalid device: Apple Watch Series 5\n-   [`KT-54627`]() Native: :commonizeNativeDistribution with configuration cache enabled fails even when set to warn on JDK 17\n-   [`KT-54339`]() Link tasks fail if Gradle Configuration Cache is enabled\n-   [`KT-53191`]() Native cinterop sync problem with gradle\n-   [`KT-54583`]() watchosDeviceArm64 target shouldn't register test tasks\n-   [`KT-52303`]() Gradle / Native: Build tasks ignore project.buildDir\n-   [`KT-54442`]() Gradle iOS test tasks fail if a device is not selected explicitly\n-   [`KT-54177`]() Gradle: Deprecate `enableEndorsedLibs` flag\n-   [`KT-47355`]() Support macos target for FatFramework task\n-   [`KT-53339`]() MPP / CocoaPods: The static framework fails to install on a real iOS device\n-   [`KT-31573`]() Missing description for Native Gradle tasks\n-   [`KT-53131`]() Gradle Sync: \"NoSuchElementException: Array contains no element matching the predicate\" with CocoaPods\n-   [`KT-53686`]() Task assembleReleaseXCFramework fails with \"error: the path does not point to a valid framework\" if project name contains a dash\n\n##### Tools. Incremental Compile\n\n-   [`KT-54144`]() New IC: \"IllegalStateException: The following LookupSymbols are not yet converted to ProgramSymbols\" when changing an inline function with custom JvmName\n-   [`KT-53871`]() New IC: \"IllegalStateException: The following LookupSymbols are not yet converted to ProgramSymbols\" when changing an inline property accessor\n-   [`KT-19804`]() Relocatable IC caches\n\n##### Tools. JPS\n\n-   [`KT-45474`]() False positive NO_ELSE_IN_WHEN on sealed class with incremental compilation\n-   [`KT-54228`]() Switching abstract to sealed classes causes incremental issue\n-   [`KT-38483`]() JPS: Stopping compilation causes IDE CompilationCanceledException\n-   [`KT-50310`]() False positive NO_ELSE_IN_WHEN on incremental build when adding sealed classes\n-   [`KT-48813`]() Move cache version to compiler\n-   [`KTIJ-921`]() JPS: FileNotFoundException on project build in mixed Kotlin/Scala project\n\n##### Tools. Kapt\n\n-   [`KT-54187`]() JVM IR + kapt: incorrect modifier `final` is generated for nested enum\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency org.openapi.generator to v6.3.0", "number": 4672, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4672", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.openapi.generator | 6.2.1 -> 6.3.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update plugin org.openapi.generator to v6.3.0", "number": 4673, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4673", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.openapi.generator | 6.2.1 -> 6.3.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update slate monorepo", "number": 4674, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4674", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @prezly/slate-commons | ^0.76.1 -> ^0.77.0 |  |  |  |  |\n| @prezly/slate-lists | ^0.76.1 -> ^0.77.0 |  |  |  |  |\n| slate | ^0.88.1 -> ^0.90.0 |  |  |  |  |\n| slate-react | ^0.88.2 -> ^0.90.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nprezly/slate\n\n### [`v0.77.2`](https://togithub.com/prezly/slate/compare/v0.77.1...v0.77.2)\n\n[Compare Source](https://togithub.com/prezly/slate/compare/v0.77.1...v0.77.2)\n\n### [`v0.77.1`](https://togithub.com/prezly/slate/compare/v0.77.0...v0.77.1)\n\n[Compare Source](https://togithub.com/prezly/slate/compare/v0.77.0...v0.77.1)\n\n### [`v0.77.0`](https://togithub.com/prezly/slate/releases/tag/v0.77.0)\n\n[Compare Source](https://togithub.com/prezly/slate/compare/v0.76.3...v0.77.0)\n\n##### What's Changed\n\n-   \\[QRF-903] Upgrade - [@prezly/sdk](https://togithub.com/prezly/sdk) version by [@e1himself](https://togithub.com/e1himself) in [https://github.com/prezly/slate/pull/384](https://togithub.com/prezly/slate/pull/384)\n\n**Full Changelog**: https://github.com/prezly/slate/compare/v0.76.3...v0.77.0\n\n### [`v0.76.3`](https://togithub.com/prezly/slate/releases/tag/v0.76.3)\n\n[Compare Source](https://togithub.com/prezly/slate/compare/v0.76.2...v0.76.3)\n\n-   Update slate version in packages by [@aspirisen](https://togithub.com/aspirisen)  [`c73adf8`](https://togithub.com/prezly/slate/commit/c73adf8e29c2ee60448724fa16a7ebf913c39cf5)\n\n**Full Changelog**: https://github.com/prezly/slate/compare/v0.76.2...v0.76.3\n\n### [`v0.76.2`](https://togithub.com/prezly/slate/releases/tag/v0.76.2)\n\n[Compare Source](https://togithub.com/prezly/slate/compare/v0.76.1...v0.76.2)\n\n#### What's Changed\n\n-   \\[QRF-915] Decrease slate version by [@aspirisen](https://togithub.com/aspirisen) in [https://github.com/prezly/slate/pull/383](https://togithub.com/prezly/slate/pull/383)\n\n**Full Changelog**: https://github.com/prezly/slate/compare/v0.76.1...v0.76.2\n\n\n\n\nianstormtaylor/slate\n\n### [`v0.90.0`](https://togithub.com/ianstormtaylor/slate/releases/tag/slate-react%400.90.0)\n\n[Compare Source](https://togithub.com/ianstormtaylor/slate/compare/<EMAIL>@0.90.0)\n\n##### Minor Changes\n\n-   [#5278](https://togithub.com/ianstormtaylor/slate/pull/5278) [`9c4097a2`](https://togithub.com/ianstormtaylor/slate/commit/9c4097a26fa92718e6f4fc1f984a70fb5af42ca2) Thanks [@kylemclean](https://togithub.com/kylemclean)! - Revert to using inline styles for default editor styles\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Immortal: This PR will be recreated if closed unmerged. Get config help if that's undesired.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Added a few more words", "number": 4675, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4675"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4675#pullrequestreview-1281537680", "body": ""}
{"title": "Don't return suggestions if query is blank", "number": 4676, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4676"}
{"title": "Remove several instances of eslint-disable", "number": 4677, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677", "body": "Using eslint-disable has caused quite a few bugs in the client (ie causing us to miss missing dependencies); add some code to memoize the callbacks so that the frequent rerenders shouldn't happen and we can wean off of littering the code with eslint-disables.\nI haven't removed all of them (some of them are explicit; ie so that the code only runs once, or so that we can add an extra dependency to trigger the callback) but this takes out a big chunk. Notably, setItem, getItem, openModal, and closeModal should all be able to be callback dependencies. \nAlso: explicitly send back undefined in the searchInsights and searchInsightSuggestions APIs if the string is empty."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677#pullrequestreview-1282121604", "body": ""}
{"comment": {"body": "There are a bunch of changes here that have caused new build warnings?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677#discussion_r1095241764"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677#pullrequestreview-1286075928", "body": ""}
{"comment": {"body": "Argh I couldn't get these specific fns to work with the full dependency list. Will need to add back the `eslint-disable` for the fns in this file to avoid the warnings.\r\n\r\nThe alternative would be to keep the build warnings so that we don't lose track of the issue...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677#discussion_r1097957262"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677#pullrequestreview-1286089786", "body": ""}
{"comment": {"body": "This should fix https://linear.app/unblocked/issue/UNB-970/topic-page-filter-toggle-state-changes-repeatedly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677#discussion_r1097966597"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677#pullrequestreview-1286093444", "body": ""}
{"comment": {"body": "Leave the warnings I think, it's a real problem we need to fix?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677#discussion_r1097968999"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4677#pullrequestreview-1286093950", "body": ""}
{"title": "Enable autocomplete globally", "number": 4678, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4678"}
{"title": "Remove override styling", "number": 4679, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4679"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4679#pullrequestreview-1281674645", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4679#pullrequestreview-1281681779", "body": ""}
{"comment": {"body": "Can you add `font-size: 0` to the .team_member_dropdown here to fix the alignment \r\n<img width=\"324\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/216421762-d3ef4777-3ddf-4f12-9ec5-0fe4a594debe.png\">\r\n<img width=\"372\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/216421796-f1cefa5f-9188-4df1-bcd3-4aebf9aa719a.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4679#discussion_r1094943719"}}
{"title": "modify paths to have api prefix. Also fix dashboard path in CF", "number": 468, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/468", "body": "The dashboard path fix is allow both /dashboard and /dashboard/ to serve index.html"}
{"comment": {"body": "Damn I had it on auto-merge. Sorry saw your review late. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/468#issuecomment-1058552346"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/468#pullrequestreview-899571851", "body": "Probably better if you merge this PR into #467, so that we can land it atomically."}
{"title": "Dont index archived pull requests", "number": 4680, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4680"}
{"title": "Fix Alignment", "number": 4681, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4681", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4681#pullrequestreview-1281711907", "body": ""}
{"title": "Use contains for search suggestion if query is longer than 2 characters", "number": 4682, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4682"}
{"title": "Added more stop words and the duckdb topic results", "number": 4683, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4683", "body": "@rasharab is going to exclude any food related slack channels, but adding a few more words. @dennispi wanted to get topics for the duckdb open source projects"}
{"comment": {"body": "https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/80997df6-a472-4aa2-a4a1-c376be775a93", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4683#issuecomment-1414296558"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4683#pullrequestreview-1282123500", "body": ""}
{"comment": {"body": "@dennispi : we reintroduced the histogram filter and we are removing all the lunch slack channels. We'll keep tuning until the results are good. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4683#discussion_r1095243149"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4683#pullrequestreview-1282168904", "body": ""}
{"title": "Fix runtime failures due to an optional attribute", "number": 4684, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4684", "body": "externalTeamId was made non-optional.\nIt hsould not be treated as optional anywhere."}
{"title": "Add default to hide cursor during recording", "number": 4685, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4685"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4685#pullrequestreview-1281931682", "body": "Thank you!"}
{"title": "Add GitLab merge request, notes, and user data classes", "number": 4686, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4686", "body": "Probably the first of many such pull requests\nGitLabDiscussions are just grouped notes/comments. This is a convenient way to get notes/comments that are grouped into threads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4686#pullrequestreview-1283902822", "body": ""}
{"title": "Update swift codegen", "number": 4687, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4687", "body": "I reviewed the changes, and they're all good:\n\nGets rid of a bunch of code duplication\nAdds validation codegen, but doesn't actually call the validation (left up to the programmer)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4687#pullrequestreview-1281923984", "body": ""}
{"title": "Add topic mapping source type", "number": 4688, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4688"}
{"title": "Log waitForSourceMarksLoaded to detect with sourcemark engine is stuck", "number": 4689, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4689"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4689#pullrequestreview-1281962627", "body": ""}
{"title": "Set createdAt for ingested PR comments", "number": 469, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/469", "body": "Also sets the Thread's createdAt to the createdAt of the first message"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/469#pullrequestreview-899672799", "body": ""}
{"title": "Make async", "number": 4690, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4690"}
{"title": "revoke prod DB access", "number": 4691, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4691"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4691#pullrequestreview-1281984318", "body": ""}
{"title": "Add some logging to help troubleshoot repo and SM issues", "number": 4692, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4692", "body": "This is for helping figure out why VSCode is getting wedged overnight.  I will probably remove a couple of the more spammy ones here afterwards."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4692#pullrequestreview-1282059852", "body": ""}
{"title": "increase timeout", "number": 4693, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4693"}
{"title": "Update topic service instance count", "number": 4694, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4694"}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.19.30", "number": 4695, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4695", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.19.29 -> 2.19.30 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.19.29 -> 2.19.30 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.19.29 -> 2.19.30 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.19.29 -> 2.19.30 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.19.29 -> 2.19.30 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime (source) | 2.19.29 -> 2.19.30 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.19.29 -> 2.19.30 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.19.29 -> 2.19.30 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.19.29 -> 2.19.30 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.19.30`]()\n\n[Compare Source]()\n\n#### **AWS CRT HTTP Client**\n\n-   ### Features\n    -   Allow users to enable Post Quantum TLS via `AwsCrtAsyncHttpClient.builder().postQuantumTlsEnabled(true).build()`.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **AWS Single Sign-On Admin**\n\n-   ### Features\n    -   Enabled FIPS endpoints for GovCloud (US) regions in SDK.\n\n#### **Amazon AppConfig**\n\n-   ### Features\n    -   AWS AppConfig introduces KMS customer-managed key (CMK) encryption of configuration data, along with AWS Secrets Manager as a new configuration data source. S3 objects using SSE-KMS encryption and SSM Parameter Store SecureStrings are also now supported.\n\n#### **Amazon Connect Service**\n\n-   ### Features\n    -   Enabled FIPS endpoints for GovCloud (US) regions in SDK.\n\n#### **Amazon Elastic Compute Cloud**\n\n-   ### Features\n    -   Documentation updates for EC2.\n\n#### **Amazon Keyspaces**\n\n-   ### Features\n    -   Enabled FIPS endpoints for GovCloud (US) regions in SDK.\n\n#### **Amazon QuickSight**\n\n-   ### Features\n    -   QuickSight support for Radar Chart and Dashboard Publish Options\n\n#### **Amazon Redshift**\n\n-   ### Features\n    -   Enabled FIPS endpoints for GovCloud (US) regions in SDK.\n\n#### **Elastic Load Balancing**\n\n-   ### Features\n    -   The GWLB Flex Health Check project updates the default values of healthy-threshold-count from 3 to 5 and unhealthy-threshold-count from 3 to 2\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Update JetBrains plugin skeleton and build", "number": 4696, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4696", "body": "Move ts_proto lib into the root npm package.json\nMake a grade task to ensure root npm dependencies\nUpdate JetBrains plugin icon and description\nAdd /jetbrains root to VSCode clients workspace"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4696#pullrequestreview-**********", "body": "Love it, thanks!"}
{"title": "IntelliJ Sidebar styling", "number": 4697, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4697", "body": "Copied explorer sidebar logic to shared + IntelliJ. Didn't update VSCode to use new shared logic for now to prevent regressions while testing out IntelliJ.\n\n"}
{"comment": {"body": "Is it possible to add icons in the sidebar component header? We could put the filter icon up there. It's what we wanted to do in VSCode but couldn't due to a bug on their end. Also, what is the icon with the circle and 4 dots?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4697#issuecomment-1414501820"}}
{"comment": {"body": "> Is it possible to add icons in the sidebar component header? We could put the filter icon up there. It's what we wanted to do in VSCode but couldn't due to a bug on their end.\r\n\r\nDo you mean this section?\r\n<img width=\"252\" alt=\"CleanShot 2023-02-02 at 15 21 17@2x\" src=\"https://user-images.githubusercontent.com/1553313/216473275-dec1f6f7-511a-425c-857d-60b97c6c9308.png\">\r\n\r\n>  Also, what is the icon with the circle and 4 dots? \r\n\r\nIt's a debug icon. It launches the webview inspector.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4697#issuecomment-1414504138"}}
{"comment": {"body": "> > Is it possible to add icons in the sidebar component header? We could put the filter icon up there. It's what we wanted to do in VSCode but couldn't due to a bug on their end.\r\n> \r\n> Do you mean this section? \r\n\r\nYep, exactly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4697#issuecomment-1414506601"}}
{"comment": {"body": "> > > Is it possible to add icons in the sidebar component header? We could put the filter icon up there. It's what we wanted to do in VSCode but couldn't due to a bug on their end.\r\n> > \r\n> > \r\n> > Do you mean this section?\r\n> \r\n> Yep, exactly.\r\n\r\nI believe we can. For example, that Circle Icon with 4 dots is something we added.\r\nI'll need to check how much control we have over this though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4697#issuecomment-1414525495"}}
{"comment": {"body": "Merging this in for Demo purposes.\r\n\r\nShouldn't affect anything in dev + prod as it's purely an IntelliJ change.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4697#issuecomment-1426102213"}}
{"title": "Adds a token chain id to the refresh token for invalidation purposes", "number": 4698, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4698", "body": "This is done is a way that is backwards compatible with current tokens in flight. The token chain id is copied from the old refresh token to the new one.\nThe token chain id will be used to do redis lookups for an issued-at value, which is then used to invalidate refresh tokens. Using a token chain ensures that only a single client is logged out (rather than all of them)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4698#pullrequestreview-1282119299", "body": ""}
{"comment": {"body": "we're going to replace the failover with a hard fail 7 days after we deploy, right?\r\n```suggestion\r\n        val tokenChainId = authorizationToken[Jwt.tokenChainIdKey]?.asUUIDOrNull() ?: error(\"Expected chainId\")\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4698#discussion_r1095240011"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4698#pullrequestreview-1283529368", "body": ""}
{"comment": {"body": "Yup! Added a comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4698#discussion_r1096134551"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4698#pullrequestreview-1283539347", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4698#pullrequestreview-1283541387", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4698#pullrequestreview-1283542276", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4698#pullrequestreview-1283587510", "body": ""}
{"title": "Treat tutorial event as engagement", "number": 4699, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4699", "body": "Part of "}
{"title": "rename ecr repo to api", "number": 47, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/47", "body": "Renamed ECR repo from core-apps to api. Had a brief chat with Rashin and we plan to use one per service docker images which would require multiple repositories. \nThis change has been deployed to Dev.\nDepending on how many repo/services we end up having, we might change the configuration file to be more generic and accept multiple repo configs under a single key. For now explicit repo creation is reasonable."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/47#pullrequestreview-854812969", "body": ""}
{"title": "Put client environment configs in one place", "number": 470, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/470", "body": "This moves the client environment configs (the API base path and the dashboard URL) into one place for easier future maintenance.  The webpack configs still specify the environment we operate in."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/470#pullrequestreview-899666374", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/470#pullrequestreview-899724155", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/470#pullrequestreview-899781411", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/470#pullrequestreview-899830703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/470#pullrequestreview-900792417", "body": ""}
{"title": "Lowercase topic name before comparison", "number": 4700, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4700"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4700#pullrequestreview-1282324174", "body": ""}
{"title": "Update topics_to_ignore.txt", "number": 4701, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4701"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4701#pullrequestreview-1282325451", "body": ""}
{"title": "Added more words", "number": 4702, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4702"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4702#pullrequestreview-1283442255", "body": ""}
{"title": "Add TopicSourceType.Curated enum", "number": 4703, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4703"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4703#pullrequestreview-1283547040", "body": ""}
{"title": "Logout API", "number": 4704, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4704"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4704#pullrequestreview-1283896409", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4704#pullrequestreview-1283903214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4704#pullrequestreview-1283905089", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4704#pullrequestreview-1283910003", "body": ""}
{"comment": {"body": "@jeffrey-ng I changed this to be the refresh token because we can think of this as the token that has \"token write access\". It also neatly avoids the expiry issue since refresh tokens are longer lived. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4704#discussion_r1096386385"}}
{"title": "Correct Button Focus in LoginManifest", "number": 4705, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4705", "body": "When entering Github Org name, pressing return navigated user back to previous page.\nThis occurred since there were multiple buttons within the form. By default, buttons are of type submit.\nChange the \"back\" button type so there's only a single submit button."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4705#pullrequestreview-1283563602", "body": "cool, thanks"}
{"title": "Add ability to toggle topic ingestion", "number": 4706, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4706", "body": "We need topic channel overrides to determine which channels we actually we do want to generate topics off of."}
{"title": "Fixes unread count bug in Hub", "number": 4707, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4707", "body": "The bug here is a consequence of SwiftUI's lifecycle. New instances of the NSViewRepresentable template can get created, but the actual underlying view is passed between NSViewRepresentable instances, then updateNSView is called on the new NSViewRepresentable instance with the previous NSTextView instance."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4707#pullrequestreview-1283724513", "body": ""}
{"comment": {"body": "Should this and makeNSView() call through to common code so setting properties on the view is done consistently?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4707#discussion_r1096287563"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4707#pullrequestreview-1283729704", "body": ""}
{"comment": {"body": "Yeah let's do that. Friday brain is not feeling very DRY", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4707#discussion_r1096290646"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4707#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4707#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Oh nooo automerge", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4707#discussion_r1096292073"}}
{"title": "Engagement metrics should take Hub and VSCode-Sidebar interactions into account", "number": 4708, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4708"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4708#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This LGTM -- but @jeffrey-ng is probably implementing this so he should weigh in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4708#discussion_r1096293815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4708#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4708#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Looks good. The only concern was we would need to call this multiple times. Once per team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4708#discussion_r1096296091"}}
{"title": "chore(deps): update node.js to v19.6.0", "number": 4709, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4709", "body": "This PR contains the following updates:\n| Package | Type | Update | Change |\n|---|---|---|---|\n| node | final | minor | 19.5.0-slim -> 19.6.0-slim |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnodejs/node\n\n### [`v19.6.0`](https://togithub.com/nodejs/node/releases/tag/v19.6.0): 2023-02-02, Version 19.6.0 (Current), @ruyadorno\n\n[Compare Source](https://togithub.com/nodejs/node/compare/v19.5.0...v19.6.0)\n\n##### Notable changes\n\n##### ESM: Leverage loaders when resolving subsequent loaders\n\nLoaders now apply to subsequent loaders, for example: `--experimental-loader ts-node --experimental-loader loader-written-in-typescript`.\n\n##### Upgrade npm to 9.4.0\n\nAdded `--install-strategy=linked` option for installations similar to pnpm.\n\n##### Other notable changes\n\n-   \\[[`a7c9daa497`](https://togithub.com/nodejs/node/commit/a7c9daa497)] - **(SEMVER-MINOR)** **fs**: add statfs() functions (Colin Ihrig) [#46358](https://togithub.com/nodejs/node/pull/46358)\n-   \\[[`34d70ce615`](https://togithub.com/nodejs/node/commit/34d70ce615)] - **(SEMVER-MINOR)** **vm**: expose cachedDataRejected for vm.compileFunction (Anna Henningsen) [#46320](https://togithub.com/nodejs/node/pull/46320)\n-   \\[[`b4ac794923`](https://togithub.com/nodejs/node/commit/b4ac794923)] - **(SEMVER-MINOR)** **v8**: support gc profile (theanarkh) [#46255](https://togithub.com/nodejs/node/pull/46255)\n-   \\[[`d52f60009a`](https://togithub.com/nodejs/node/commit/d52f60009a)] - **(SEMVER-MINOR)** **src,lib**: add constrainedMemory API for process (theanarkh) [#46218](https://togithub.com/nodejs/node/pull/46218)\n-   \\[[`5ad6c2088e`](https://togithub.com/nodejs/node/commit/5ad6c2088e)] - **(SEMVER-MINOR)** **buffer**: add isAscii method (Yagiz Nizipli) [#46046](https://togithub.com/nodejs/node/pull/46046)\n-   \\[[`fbdc3f7316`](https://togithub.com/nodejs/node/commit/fbdc3f7316)] - **(SEMVER-MINOR)** **test_runner**: add reporters (Moshe Atlow) [#45712](https://togithub.com/nodejs/node/pull/45712)\n\n##### Commits\n\n-   \\[[`524eec70e2`](https://togithub.com/nodejs/node/commit/524eec70e2)] - **benchmark**: add trailing commas (Antoine du Hamel) [#46370](https://togithub.com/nodejs/node/pull/46370)\n-   \\[[`f318a85408`](https://togithub.com/nodejs/node/commit/f318a85408)] - **benchmark**: remove buffer benchmarks redundancy (Brian White) [#45735](https://togithub.com/nodejs/node/pull/45735)\n-   \\[[`6186b3ea14`](https://togithub.com/nodejs/node/commit/6186b3ea14)] - **benchmark**: introduce benchmark combination filtering (Brian White) [#45735](https://togithub.com/nodejs/node/pull/45735)\n-   \\[[`5ad6c2088e`](https://togithub.com/nodejs/node/commit/5ad6c2088e)] - **(SEMVER-MINOR)** **buffer**: add isAscii method (Yagiz Nizipli) [#46046](https://togithub.com/nodejs/node/pull/46046)\n-   \\[[`8c6c4338a6`](https://togithub.com/nodejs/node/commit/8c6c4338a6)] - **build**: export more OpenSSL symbols on Windows (Mohamed Akram) [#45486](https://togithub.com/nodejs/node/pull/45486)\n-   \\[[`d795d93901`](https://togithub.com/nodejs/node/commit/d795d93901)] - **build**: fix MSVC 2022 Release compilation (Vladimir Morozov (REDMOND)) [#46228](https://togithub.com/nodejs/node/pull/46228)\n-   \\[[`8e363cf8e8`](https://togithub.com/nodejs/node/commit/8e363cf8e8)] - **crypto**: include `hmac.h` in `crypto_util.h` (Adam Langley) [#46279](https://togithub.com/nodejs/node/pull/46279)\n-   \\[[`c1f3e13c65`](https://togithub.com/nodejs/node/commit/c1f3e13c65)] - **deps**: update acorn to 8.8.2 (Node.js GitHub Bot) [#46363](https://togithub.com/nodejs/node/pull/46363)\n-   \\[[`813b160bd7`](https://togithub.com/nodejs/node/commit/813b160bd7)] - **deps**: upgrade npm to 9.4.0 (npm team) [#46353](https://togithub.com/nodejs/node/pull/46353)\n-   \\[[`9c2f3cea70`](https://togithub.com/nodejs/node/commit/9c2f3cea70)] - **deps**: update undici to 5.15.0 (Node.js GitHub Bot) [#46213](https://togithub.com/nodejs/node/pull/46213)\n-   \\[[`312e10c1e3`](https://togithub.com/nodejs/node/commit/312e10c1e3)] - **deps**: update to uvwasi 0.0.15 (Colin Ihrig) [#46253](https://togithub.com/nodejs/node/pull/46253)\n-   \\[[`c7024eec16`](https://togithub.com/nodejs/node/commit/c7024eec16)] - **doc**: correct the `sed` command for macOS in release process docs (Juan Jos) [#46397](https://togithub.com/nodejs/node/pull/46397)\n-   \\[[`996bac044b`](https://togithub.com/nodejs/node/commit/996bac044b)] - **doc**: include webstreams in finished() and Duplex.from() parameters (Debadree Chatterjee) [#46312](https://togithub.com/nodejs/node/pull/46312)\n-   \\[[`891d18d55c`](https://togithub.com/nodejs/node/commit/891d18d55c)] - **doc**: pass string to `textEncoder.encode` as input (Deokjin Kim) [#46421](https://togithub.com/nodejs/node/pull/46421)\n-   \\[[`968db213f8`](https://togithub.com/nodejs/node/commit/968db213f8)] - **doc**: add tip for session.post function (theanarkh) [#46354](https://togithub.com/nodejs/node/pull/46354)\n-   \\[[`a64d7f4e31`](https://togithub.com/nodejs/node/commit/a64d7f4e31)] - **doc**: add documentation for socket.destroySoon() (Luigi Pinca) [#46337](https://togithub.com/nodejs/node/pull/46337)\n-   \\[[`975788899f`](https://togithub.com/nodejs/node/commit/975788899f)] - **doc**: fix commit message using test instead of deps (Tony Gorez) [#46313](https://togithub.com/nodejs/node/pull/46313)\n-   \\[[`1d44017f52`](https://togithub.com/nodejs/node/commit/1d44017f52)] - **doc**: add v8 fast api contribution guidelines (Yagiz Nizipli) [#46199](https://togithub.com/nodejs/node/pull/46199)\n-   \\[[`e2698c05fb`](https://togithub.com/nodejs/node/commit/e2698c05fb)] - **doc**: fix small typo error (0xflotus) [#46186](https://togithub.com/nodejs/node/pull/46186)\n-   \\[[`f39fb8c001`](https://togithub.com/nodejs/node/commit/f39fb8c001)] - **doc**: mark some parameters as optional in webstreams (Deokjin Kim) [#46269](https://togithub.com/nodejs/node/pull/46269)\n-   \\[[`7a9af38128`](https://togithub.com/nodejs/node/commit/7a9af38128)] - **doc**: update output of example in `events.getEventListeners` (Deokjin Kim) [#46268](https://togithub.com/nodejs/node/pull/46268)\n-   \\[[`729642f30b`](https://togithub.com/nodejs/node/commit/729642f30b)] - **esm**: delete preload mock test (Geoffrey Booth) [#46402](https://togithub.com/nodejs/node/pull/46402)\n-   \\[[`7aac21e90a`](https://togithub.com/nodejs/node/commit/7aac21e90a)] - **esm**: leverage loaders when resolving subsequent loaders (Mal Nison) [#43772](https://togithub.com/nodejs/node/pull/43772)\n-   \\[[`a7c9daa497`](https://togithub.com/nodejs/node/commit/a7c9daa497)] - **(SEMVER-MINOR)** **fs**: add statfs() functions (Colin Ihrig) [#46358](https://togithub.com/nodejs/node/pull/46358)\n-   \\[[`1ec6270efa`](https://togithub.com/nodejs/node/commit/1ec6270efa)] - **http**: res.setHeaders first implementation (Marco Ippolito) [#46109](https://togithub.com/nodejs/node/pull/46109)\n-   \\[[`d4370259e9`](https://togithub.com/nodejs/node/commit/d4370259e9)] - **inspector**: allow opening inspector when `NODE_V8_COVERAGE` is set (Moshe Atlow) [#46113](https://togithub.com/nodejs/node/pull/46113)\n-   \\[[`b966ef9a42`](https://togithub.com/nodejs/node/commit/b966ef9a42)] - **lib**: remove unnecessary ObjectGetValueSafe (Chengzhong Wu) [#46335](https://togithub.com/nodejs/node/pull/46335)\n-   \\[[`2b06d66289`](https://togithub.com/nodejs/node/commit/2b06d66289)] - **lib**: cache parsed source maps to reduce memory footprint (Chengzhong Wu) [#46225](https://togithub.com/nodejs/node/pull/46225)\n-   \\[[`c38673df91`](https://togithub.com/nodejs/node/commit/c38673df91)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46399](https://togithub.com/nodejs/node/pull/46399)\n-   \\[[`c10e602547`](https://togithub.com/nodejs/node/commit/c10e602547)] - **meta**: update AUTHORS (Node.js GitHub Bot) [#46303](https://togithub.com/nodejs/node/pull/46303)\n-   \\[[`9dc026b14a`](https://togithub.com/nodejs/node/commit/9dc026b14a)] - **meta**: add .mailmap entry (Rich Trott) [#46303](https://togithub.com/nodejs/node/pull/46303)\n-   \\[[`7c514574f7`](https://togithub.com/nodejs/node/commit/7c514574f7)] - **meta**: move evanlucas to emeritus (Evan Lucas) [#46274](https://togithub.com/nodejs/node/pull/46274)\n-   \\[[`3a3a6d87f1`](https://togithub.com/nodejs/node/commit/3a3a6d87f1)] - **module**: move test reporter loading (Geoffrey Booth) [#45923](https://togithub.com/nodejs/node/pull/45923)\n-   \\[[`4ae2492a33`](https://togithub.com/nodejs/node/commit/4ae2492a33)] - **readline**: fix detection of carriage return (Antoine du Hamel) [#46306](https://togithub.com/nodejs/node/pull/46306)\n-   \\[[`43cad78b7a`](https://togithub.com/nodejs/node/commit/43cad78b7a)] - **src**: stop tracing agent before shutting down libuv (Santiago Gimeno) [#46380](https://togithub.com/nodejs/node/pull/46380)\n-   \\[[`360a3f3094`](https://togithub.com/nodejs/node/commit/360a3f3094)] - **src**: get rid of fp arithmetic in ParseIPv4Host (Tobias Nieen) [#46326](https://togithub.com/nodejs/node/pull/46326)\n-   \\[[`e7b507a8cf`](https://togithub.com/nodejs/node/commit/e7b507a8cf)] - **src**: use UNREACHABLE instead of CHECK(falsy) (Tobias Nieen) [#46317](https://togithub.com/nodejs/node/pull/46317)\n-   \\[[`4c59b60ee8`](https://togithub.com/nodejs/node/commit/4c59b60ee8)] - **src**: add support for ETW stack walking (Jos Dapena Paz) [#46203](https://togithub.com/nodejs/node/pull/46203)\n-   \\[[`640d111f95`](https://togithub.com/nodejs/node/commit/640d111f95)] - **src**: refactor EndsInANumber in node_url.cc and adds IsIPv4NumberValid (Miguel Teixeira) [#46227](https://togithub.com/nodejs/node/pull/46227)\n-   \\[[`fb7bee2b6e`](https://togithub.com/nodejs/node/commit/fb7bee2b6e)] - **src**: fix c++ exception on bad command line arg (Ben Noordhuis) [#46290](https://togithub.com/nodejs/node/pull/46290)\n-   \\[[`18c95ec4bd`](https://togithub.com/nodejs/node/commit/18c95ec4bd)] - **src**: remove unreachable UNREACHABLE (Tobias Nieen) [#46281](https://togithub.com/nodejs/node/pull/46281)\n-   \\[[`35bf93b01a`](https://togithub.com/nodejs/node/commit/35bf93b01a)] - **src**: replace custom ASCII validation with simdutf one (Anna Henningsen) [#46271](https://togithub.com/nodejs/node/pull/46271)\n-   \\[[`8307a4bbcd`](https://togithub.com/nodejs/node/commit/8307a4bbcd)] - **src**: replace unreachable code with static_assert (Tobias Nieen) [#46250](https://togithub.com/nodejs/node/pull/46250)\n-   \\[[`7cf0da020a`](https://togithub.com/nodejs/node/commit/7cf0da020a)] - **src**: use explicit C++17 fallthrough (Tobias Nieen) [#46251](https://togithub.com/nodejs/node/pull/46251)\n-   \\[[`d52f60009a`](https://togithub.com/nodejs/node/commit/d52f60009a)] - **(SEMVER-MINOR)** **src,lib**: add constrainedMemory API for process (theanarkh) [#46218](https://togithub.com/nodejs/node/pull/46218)\n-   \\[[`2e5e7a9261`](https://togithub.com/nodejs/node/commit/2e5e7a9261)] - **stream**: remove brandchecks from stream duplexify (Debadree Chatterjee) [#46315](https://togithub.com/nodejs/node/pull/46315)\n-   \\[[`9675863461`](https://togithub.com/nodejs/node/commit/9675863461)] - **stream**: fix readable stream as async iterator function (Erick Wendel) [#46147](https://togithub.com/nodejs/node/pull/46147)\n-   \\[[`232bdd5d16`](https://togithub.com/nodejs/node/commit/232bdd5d16)] - **test**: add trailing commas in `test/node-api` (Antoine du Hamel) [#46384](https://togithub.com/nodejs/node/pull/46384)\n-   \\[[`4cc081815d`](https://togithub.com/nodejs/node/commit/4cc081815d)] - **test**: add trailing commas in `test/message` (Antoine du Hamel) [#46372](https://togithub.com/nodejs/node/pull/46372)\n-   \\[[`b83c5d9deb`](https://togithub.com/nodejs/node/commit/b83c5d9deb)] - **test**: add trailing commas in `test/pseudo-tty` (Antoine du Hamel) [#46371](https://togithub.com/nodejs/node/pull/46371)\n-   \\[[`8a45c9d231`](https://togithub.com/nodejs/node/commit/8a45c9d231)] - **test**: fix tap escaping with and without --test (Pulkit Gupta) [#46311](https://togithub.com/nodejs/node/pull/46311)\n-   \\[[`367dc41299`](https://togithub.com/nodejs/node/commit/367dc41299)] - **test**: set common.bits to 64 for loong64 (Shi Pujin) [#45383](https://togithub.com/nodejs/node/pull/45383)\n-   \\[[`7385edc7d0`](https://togithub.com/nodejs/node/commit/7385edc7d0)] - **test**: s390x zlib test case fixes (Adam Majer) [#46367](https://togithub.com/nodejs/node/pull/46367)\n-   \\[[`d5d837bdee`](https://togithub.com/nodejs/node/commit/d5d837bdee)] - **test**: fix logInTimeout is not function (theanarkh) [#46348](https://togithub.com/nodejs/node/pull/46348)\n-   \\[[`a1d79546ac`](https://togithub.com/nodejs/node/commit/a1d79546ac)] - **test**: avoid trying to call sysctl directly (Adam Majer) [#46366](https://togithub.com/nodejs/node/pull/46366)\n-   \\[[`747f3689e0`](https://togithub.com/nodejs/node/commit/747f3689e0)] - **test**: avoid left behind child processes (Richard Lau) [#46276](https://togithub.com/nodejs/node/pull/46276)\n-   \\[[`940484b7aa`](https://togithub.com/nodejs/node/commit/940484b7aa)] - **test**: add failing test for readline with carriage return (Alec Mev) [#46075](https://togithub.com/nodejs/node/pull/46075)\n-   \\[[`d13116a719`](https://togithub.com/nodejs/node/commit/d13116a719)] - **test,crypto**: add CFRG curve vectors to wrap/unwrap tests (Filip Skokan) [#46406](https://togithub.com/nodejs/node/pull/46406)\n-   \\[[`398a7477b3`](https://togithub.com/nodejs/node/commit/398a7477b3)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#46267](https://togithub.com/nodejs/node/pull/46267)\n-   \\[[`8b473affe8`](https://togithub.com/nodejs/node/commit/8b473affe8)] - **test_runner**: make built in reporters internal (Colin Ihrig) [#46092](https://togithub.com/nodejs/node/pull/46092)\n-   \\[[`a49e17e22b`](https://togithub.com/nodejs/node/commit/a49e17e22b)] - **test_runner**: report `file` in test runner events (Moshe Atlow) [#46030](https://togithub.com/nodejs/node/pull/46030)\n-   \\[[`fbdc3f7316`](https://togithub.com/nodejs/node/commit/fbdc3f7316)] - **test_runner**: add reporters (Moshe Atlow) [#45712](https://togithub.com/nodejs/node/pull/45712)\n-   \\[[`6579de8c47`](https://togithub.com/nodejs/node/commit/6579de8c47)] - **tools**: update eslint to 8.33.0 (Node.js GitHub Bot) [#46400](https://togithub.com/nodejs/node/pull/46400)\n-   \\[[`bf62da55ad`](https://togithub.com/nodejs/node/commit/bf62da55ad)] - **tools**: update doc to unist-util-select@4.0.3 unist-util-visit@4.1.2 (Node.js GitHub Bot) [#46364](https://togithub.com/nodejs/node/pull/46364)\n-   \\[[`b0acf55197`](https://togithub.com/nodejs/node/commit/b0acf55197)] - **tools**: update lint-md-dependencies to rollup@3.12.0 (Node.js GitHub Bot) [#46398](https://togithub.com/nodejs/node/pull/46398)\n-   \\[[`88b904cf24`](https://togithub.com/nodejs/node/commit/88b904cf24)] - **tools**: require more trailing commas (Antoine du Hamel) [#46346](https://togithub.com/nodejs/node/pull/46346)\n-   \\[[`4440b3ef87`](https://togithub.com/nodejs/node/commit/4440b3ef87)] - **tools**: update lint-md-dependencies (Node.js GitHub Bot) [#46302](https://togithub.com/nodejs/node/pull/46302)\n-   \\[[`e75faff4bd`](https://togithub.com/nodejs/node/commit/e75faff4bd)] - **tools**: allow icutrim.py to run on python2 (Michael Dawson) [#46263](https://togithub.com/nodejs/node/pull/46263)\n-   \\[[`e460d16d73`](https://togithub.com/nodejs/node/commit/e460d16d73)] - **url**: refactor to use more primordials (Antoine du Hamel) [#45966](https://togithub.com/nodejs/node/pull/45966)\n-   \\[[`b4ac794923`](https://togithub.com/nodejs/node/commit/b4ac794923)] - **(SEMVER-MINOR)** **v8**: support gc profile (theanarkh) [#46255](https://togithub.com/nodejs/node/pull/46255)\n-   \\[[`34d70ce615`](https://togithub.com/nodejs/node/commit/34d70ce615)] - **(SEMVER-MINOR)** **vm**: expose cachedDataRejected for vm.compileFunction (Anna Henningsen) [#46320](https://togithub.com/nodejs/node/pull/46320)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Shiki-based syntax hilighter for VSCode", "number": 471, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/471", "body": "Use Shiki as a syntax hilighter for VSCode.  I added quite a lot of commenting to CodeToHtmlRenderer explaining how it works, read through that to get a better idea of what's happening.\nMost of this PR is dealing with packaging issues: Shiki is meant to be used on a node server, and doesn't play well with bundlers.  It expects to find language files and the Onigiri WASM runtime in the node_modules subfolder.  So in this PR, instead, we copy those files into a /shiki extension subfolder, and load the files from there."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/471#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Oh I noticed this in the UI -- without failing over to a transparent border the input would increase in size by 2px whenever it got focus, and the rest of the UI would shift around", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/471#discussion_r819184650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/471#pullrequestreview-900816678", "body": ""}
{"comment": {"body": "I think this may be the better option for the long term.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/471#discussion_r819989814"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/471#pullrequestreview-900816713", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/471#pullrequestreview-900827125", "body": ""}
{"comment": {"body": "My gut feeling is that it will probably give us longer-term happiness, but it also involves quite a bit of effort to get working correctly.  I tried to set this up and realized it would be a fair bit of work to translate the VSCode themes to something highlight.js would understand, so I shelved it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/471#discussion_r819997329"}}
{"title": "DRY it up", "number": 4710, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4710"}
{"title": "Add ability to curate topics", "number": 4711, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4711"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4711#pullrequestreview-1283779527", "body": ""}
{"title": "Add VSCode logs to help troubleshoot webview startup", "number": 4712, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4712", "body": "More logs to try to figure out why our sidebar views aren't loading for one customer"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4712#pullrequestreview-1283742707", "body": ""}
{"comment": {"body": "Moved this intentionally.  `setExtensionContext` should be the first thing to be called I think", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4712#discussion_r1096300091"}}
{"title": "chore(deps): update aws-cdk monorepo to v2.63.1", "number": 4713, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4713", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| aws-cdk | 2.63.0 -> 2.63.1 |  |  |  |  |\n| aws-cdk-lib | 2.63.0 -> 2.63.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-cdk\n\n### [`v2.63.1`]()\n\n[Compare Source]()\n\n##### Reverts\n\n-   **cdk-assets:** packaging assets is broken on Node older than 14.17 ([#23994]()) ([1976f1a]()), closes [#23859]()\n\n***\n\n#### Alpha modules (2.63.1-alpha.0)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.19.31", "number": 4714, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4714", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.19.30 -> 2.19.31 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.19.30 -> 2.19.31 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.19.30 -> 2.19.31 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.19.30 -> 2.19.31 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.19.30 -> 2.19.31 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime (source) | 2.19.30 -> 2.19.31 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.19.30 -> 2.19.31 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.19.30 -> 2.19.31 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.19.30 -> 2.19.31 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.19.31`]()\n\n[Compare Source]()\n\n#### **AWS Proton**\n\n-   ### Features\n    -   Add new GetResourcesSummary API\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon Redshift**\n\n-   ### Features\n    -   Corrects descriptions of the parameters for the API operations RestoreFromClusterSnapshot, RestoreTableFromClusterSnapshot, and CreateCluster.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Fix VSCode launch watch mode", "number": 4715, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4715", "body": "Forking the TS build broke this, this fixes it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4715#pullrequestreview-1284023646", "body": ""}
{"title": "Fix typo", "number": 4716, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4716"}
{"title": "Turn into buttons", "number": 4717, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4717"}
{"title": "Clean up buttons", "number": 4718, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4718"}
{"title": "SourceMarkScheduler construction logging and race fix", "number": 4719, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719", "body": "Possible fix for: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283925627", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283925703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283927285", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283928328", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283929154", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283929732", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283932019", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283932066", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283934964", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4719#pullrequestreview-1283936967", "body": ""}
{"title": "[WIP] Rename isDeactivated to isCurrentMember and correctly set for PR Ingestion", "number": 472, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472", "body": "We only set TeamMemberModel.isActive to true if the identity is not anonymous (i.e. Person model exists for identity model)\nI forget why we decided to rename."}
{"comment": {"body": "Done by Pete https://github.com/NextChapterSoftware/unblocked/pull/634", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-*********", "body": ""}
{"comment": {"body": "```suggestion\r\n        A team member can become de-activated if they (i) leave, or (ii) are removed, or (iii) are suspended from a team.\r\n        A team member is not active until they have an Unblocked account.\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#discussion_r819277255"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-900508822", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-902383502", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/472#pullrequestreview-902396322", "body": ""}
{"title": "Drop unused token claim (externalId), previously used for allowListing based on github membership", "number": 4720, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4720"}
{"comment": {"body": "merged. I'll verify", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4720#issuecomment-1416572698"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4720#pullrequestreview-1283950840", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4720#pullrequestreview-1283950884", "body": ""}
{"title": "Throttle engagement metrics so that we record at most once per 3 hours", "number": 4721, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4721"}
{"title": "Debug logging for GitHub code exchange failure", "number": 4722, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4722", "body": "GitHub login failure that has happened several times over the last 14 days caused by:\nField 'access_token' is required for type with serial name 'com.nextchaptersoftware.scm.github.GitHubUserAccessTokenResponse', but it was missing"}
{"title": "chore(deps): update dependency @types/chrome to ^0.0.212", "number": 4723, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4723", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/chrome (source) | ^0.0.211 -> ^0.0.212 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Adds GitLab provider types", "number": 4724, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4724", "body": "@pwerry Note that we no longer consider enum additions breaking:\nhttps://github.com/NextChapterSoftware/unblocked/pull/4724/commits/1d981dbfaec6dd10975fc3c322d06b5a412c506f"}
{"title": "Remove limit for search when mapping insights to topics", "number": 4725, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4725"}
{"title": "chore(deps): update aws-cdk monorepo to v2.63.2", "number": 4726, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4726", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| aws-cdk | 2.63.1 -> 2.63.2 |  |  |  |  |\n| aws-cdk-lib | 2.63.1 -> 2.63.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-cdk\n\n### [`v2.63.2`]()\n\n[Compare Source]()\n\n***\n\n#### Alpha modules (2.63.2-alpha.0)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency @types/node to v18.11.19", "number": 4727, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4727", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/node (source) | 18.11.18 -> 18.11.19 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency com.github.doyaaaaaken:kotlin-csv-jvm to v1.8.0", "number": 4728, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4728", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.doyaaaaaken:kotlin-csv-jvm | 1.7.0 -> 1.8.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ndoyaaaaaken/kotlin-csv\n\n### [`v1.8.0`](https://togithub.com/doyaaaaaken/kotlin-csv/releases/tag/1.8.0)\n\n[Compare Source](https://togithub.com/doyaaaaaken/kotlin-csv/compare/1.7.0...1.8.0)\n\n#### What's Changed\n\n-   Replace missing fields with empty string by [@popcornAC](https://togithub.com/popcornAC) in [https://github.com/doyaaaaaken/kotlin-csv/pull/117](https://togithub.com/doyaaaaaken/kotlin-csv/pull/117)\n-   change to use assertSoftly in all tests by [@satou-aaaaa](https://togithub.com/satou-aaaaa) in [https://github.com/doyaaaaaken/kotlin-csv/pull/116](https://togithub.com/doyaaaaaken/kotlin-csv/pull/116)\n-   Update README.md by [@Skaldebane](https://togithub.com/Skaldebane) in [https://github.com/doyaaaaaken/kotlin-csv/pull/114](https://togithub.com/doyaaaaaken/kotlin-csv/pull/114)\n-   Fix typo by [@satou-aaaaa](https://togithub.com/satou-aaaaa) in [https://github.com/doyaaaaaken/kotlin-csv/pull/115](https://togithub.com/doyaaaaaken/kotlin-csv/pull/115)\n\n#### New Contributors\n\n-   [@satou-aaaaa](https://togithub.com/satou-aaaaa) made their first contribution in [https://github.com/doyaaaaaken/kotlin-csv/pull/115](https://togithub.com/doyaaaaaken/kotlin-csv/pull/115)\n-   [@popcornAC](https://togithub.com/popcornAC) made their first contribution in [https://github.com/doyaaaaaken/kotlin-csv/pull/117](https://togithub.com/doyaaaaaken/kotlin-csv/pull/117)\n-   [@Skaldebane](https://togithub.com/Skaldebane) made their first contribution in [https://github.com/doyaaaaaken/kotlin-csv/pull/114](https://togithub.com/doyaaaaaken/kotlin-csv/pull/114)\n\n**Full Changelog**: https://github.com/doyaaaaaken/kotlin-csv/compare/1.7.0...1.8.0\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Adds GitLab OAuth app config and secrets", "number": 4729, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4729"}
{"title": "longer paths should have higher priority (lowest number)", "number": 473, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/473"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/473#pullrequestreview-900771364", "body": ""}
{"title": "Allow passing a null limit for topic mapping", "number": 4730, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4730"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4730#pullrequestreview-1337827149", "body": ""}
{"comment": {"body": "You are lovely", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4730#discussion_r1134538376"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4730#pullrequestreview-1355382025", "body": ""}
{"comment": {"body": "@rasharab : You are lovely too! Sent from IntelliJ!\n\n\n\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/1f09e8c0-2607-40db-91ed-b39a7a07a68b)\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4730#discussion_r1146701091"}}
{"title": "add modals inputs and ability to send emails to identiies", "number": 4731, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4731"}
{"title": "Fix build", "number": 4732, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4732"}
{"title": "Add redis store for token chains", "number": 4733, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4733", "body": "Not plugged in yet. Planning to use for logout in the auth service"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4733#pullrequestreview-1286041095", "body": ""}
{"title": "Updated Client User Engagement Metrics (Not Hub)", "number": 4734, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734", "body": "Add support for https://github.com/NextChapterSoftware/unblocked/pull/4708\nAdd user engagement tracking to the following:\n1. Dashboard content is visible (Chat.tsx)\n2. VSCode Sidebar content is visible (SidebarWebview)\n3. VSCode Explorer content is visible (ExplorerInsightsWebview)\nWhen these views are opened and the client is focused, send metrics event.\nIf nothing has changed and the view is still open / focused after an hour (adjustable interval), send metrics event again.\nfixes https://linear.app/unblocked/issue/UNB-674/engagement-metrics-should-take-hub-and-vscode-sidebar-interactions"}
{"comment": {"body": "> In the web case, can we call the content event API for the Topics (\"Explore\") list view and the Expert list view?\r\n\r\nAdded. In future, add separate events to understand which pages / features are used.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#issuecomment-1419960131"}}
{"comment": {"body": "Remove the empty `IdeSidebarPoller.ts` file?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#issuecomment-1419967103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286098459", "body": ""}
{"comment": {"body": "Do we really still need the thread/PR metrics when we have the \"content\" metric?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#discussion_r1097972409"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286104820", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286111878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286113246", "body": ""}
{"comment": {"body": "From my understanding, \"Content\" Metric is used for the general listings.\r\nIn dashboard, it's sent when the user visits the \"Mine\" page (aka chat.tsx)\r\nIn Hub, it will send an event when the popover is opened.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#discussion_r1097982598"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286114871", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286129426", "body": ""}
{"comment": {"body": "The content metric event is a weaker signal, since it means that the user is looking at a _list_ of topics, experts, or insights. So I think it's useful to capture both.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#discussion_r1097993755"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286131901", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286133409", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286135487", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286136544", "body": "In the web case, can we call the content event API for the Topics (\"Explore\") list view and the Expert list view?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286157496", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286159664", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286160277", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286191714", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286197713", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286199076", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286201224", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286201826", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-1286201906", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4734#pullrequestreview-**********", "body": ""}
{"title": "Add ConnectivityStream", "number": 4735, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4735", "body": "Add a stream that hits the shallow health check every five seconds, and returns a true/false indicator if we have connectivity to the API or not.  Log this in VSCode.\nThe idea is that this can eventually be used for displaying connectivity failure banners/popups etc.\nI had to make some changes to the base API code a bit to allow hitting the health check as a plain fetch call, without retrying logic etc."}
{"title": "Send Metrics for Hub Content", "number": 4736, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4736", "body": "On WindowOpen, send metrics event for each team.\nfixes "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4736#pullrequestreview-**********", "body": ""}
{"title": "Command to enable VSCode extension host debugger", "number": 4737, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4737"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4737#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Confused on how this enables the node debugger?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4737#discussion_r1098026698"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4737#pullrequestreview-**********", "body": ""}
{"title": "Move logic into scmservice module", "number": 4738, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4738", "body": "This will pull references to GitHub classes out of the PR ingestion module. Next PR will update this logic to convert GitHub classes to abstract classes so that the rest of the PR ingestion module can be purged of references to GitHub* classes."}
{"title": "Installer builds will now create VSCode sourcemap artifacts", "number": 4739, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4739"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4739#pullrequestreview-1286216366", "body": ""}
{"title": "Video Models", "number": 474, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474", "body": "Defines DB models for Video Chat state management, both for channels and recordings. See diffs for explanations of classes and fields"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899817882", "body": ""}
{"comment": {"body": "Feedback welcome on this one. The idea is that messages and video recordings are 1:1. However, a VideoRecording is not necessary tied to a message. In the PR walkthrough flow, a video is tied to a PR description and not a thread message for example.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#discussion_r819275303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899818297", "body": ""}
{"comment": {"body": "This allows us to build UX into the client to inform other participants that a recording is in progress", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#discussion_r819275595"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899819171", "body": ""}
{"comment": {"body": "This is needed to inform other clients about screen sharing status, as well as to allow the service to prioritize screen sharing users in the recording layout", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#discussion_r819276254"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899819409", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899820063", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899820269", "body": ""}
{"comment": {"body": "This is the id of the asset that gets uploaded to S3. Agora generates this ID after the recording is uploaded and hands it back to us", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#discussion_r819277114"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899821100", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899825250", "body": ""}
{"comment": {"body": "\u2705 I think this is preferable to the alternative.\r\n\r\nThe alternative is to annotate an optional `message` reference on the VideoRecordingModel. Consider loading a thread with N messages, which would require that we lookup the VideoRecordingModel by N message IDs. That's actually pretty costly given how rare messages will be relative the non-video messages.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#discussion_r819280781"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899837746", "body": ""}
{"comment": {"body": "Question: any reason why some models don't implement `EntityExtensions`? Do all of our models implement createdAt/modifiedAt?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#discussion_r819290451"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-899840755", "body": ""}
{"comment": {"body": "They all do - EntityExtensions just provides conveniences for `findModifiedSince`. VideoRecording is not something that we'll implement a push channel for so I've implemented against the base class instead. Maybe it makes sense to roll all of this up to the base class though?\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#discussion_r819292624"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-900457284", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-900473137", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-900478011", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-900481141", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/474#pullrequestreview-900768627", "body": ""}
{"title": "UNB-939: Setting LSMinSysVer:", "number": 4740, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4740", "body": " for "}
{"comment": {"body": "Please work.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4740#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4740#pullrequestreview-**********", "body": ""}
{"title": "Feature flags for providers", "number": 4741, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4741", "body": "This allows us sanely stage the activation of new SCMs (GitLabs and Bitbucket) in each environment as we build them out.\n"}
{"title": "Throw on forbidden so that we can retry after token is refreshed", "number": 4742, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4742"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4742#pullrequestreview-**********", "body": ""}
{"title": "Add infra for powerml", "number": 4743, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4743"}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.19.32", "number": 4744, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4744", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.19.31 -> 2.19.32 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.19.31 -> 2.19.32 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.19.31 -> 2.19.32 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.19.31 -> 2.19.32 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.19.31 -> 2.19.32 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime (source) | 2.19.31 -> 2.19.32 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.19.31 -> 2.19.32 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.19.31 -> 2.19.32 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.19.31 -> 2.19.32 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.19.32`]()\n\n[Compare Source]()\n\n#### **AWS CRT-based S3 Client**\n\n-   ### Bugfixes\n    -   Fix an issue where maxConcurrency configured by the user on the builder was not honored.\n\n#### **AWS Compute Optimizer**\n\n-   ### Features\n    -   AWS Compute optimizer can now infer if Kafka is running on an instance.\n\n#### **AWS Elemental MediaConvert**\n\n-   ### Features\n    -   The AWS Elemental MediaConvert SDK has added improved scene change detection capabilities and a bandwidth reduction filter, along with video quality enhancements, to the AVC encoder.\n\n#### **AWS Outposts**\n\n-   ### Features\n    -   Adds OrderType to Order structure. Adds PreviousOrderId and PreviousLineItemId to LineItem structure. Adds new line item status REPLACED. Increases maximum length of pagination token.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon Connect Customer Profiles**\n\n-   ### Features\n    -   This release deprecates the PartyType and Gender enum data types from the Profile model and replaces them with new PartyTypeString and GenderString attributes, which accept any string of length up to 255.\n\n#### **Amazon Fraud Detector**\n\n-   ### Features\n    -   My AWS Service (Amazon Fraud Detector) - This release introduces Cold Start Model Training which optimizes training for small datasets and adds intelligent methods for treating unlabeled data. You can now train Online Fraud Insights or Transaction Fraud Insights models with minimal historical-data.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Treat API 4xx as warnings, API 5xx as errors", "number": 4745, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4745", "body": "Logs are filled with spurious errors from 4xx cases."}
{"title": "Introduce ScmPullRequest and ScmUser", "number": 4746, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746", "body": "The existing pull request ingestion logic operates on GitHub-specific models (the objects returned by the GitHub API). We want to update ingestion logic to operate pull requests from any provider. \nTo achieve that, we will replace those GitHub-specific models with abstract models that can be created from the various provider-specific models via transformers.\nThis PR introduces two new abstract classes to replace their GitHub equivalents in our ingestion logic:\n\nScmPullRequest which replaces GitHubPullRequest\nScmUser which replaces GitHubUser\n\nThere are other GitHub-specific models (i.e. TLCs, code comments, reviews) yet to be replaced but let's start with these two classes, as doing those as well would explode the size of this PR."}
{"comment": {"body": "Was speaking with Richie, can we potentially make this even more generic, like IntegrationUser or something of that sort.\r\nI am worried about how many user classes we're going to have across integrations, but might not be a problem.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#issuecomment-**********"}}
{"comment": {"body": "@rasharab can you point me to a data class for other users? `SlackUserModel`? Just want to get a sense for how to make this more generic if we're going to convert it to an `IntegrationUser`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#issuecomment-1421725166"}}
{"comment": {"body": "> @rasharab can you point me to a data class for other users? `SlackUserModel`? Just want to get a sense for how to make this more generic if we're going to convert it to an `IntegrationUser`\r\n\r\nThought about a bit more. I actually think we're already in a good place:\r\n- The common _DB model_ is already `com.nextchaptersoftware.db.models.Identity` for all SCM and slack integrations.\r\n- The common _API model_ is already `com.nextchaptersoftware.api.models.Identity` for all SCM and slack integrations.\r\n\r\nFound these Slack-specific user model:\r\n1. `SlackUserModel` \u2014 this is really pretty lightweight and not worth refactoring\r\n1. `com.slack.api.model.User` \u2014 this is from the Slack SDK, so we can't refactor it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#issuecomment-1421920257"}}
{"comment": {"body": "As long as the integration-specific User models are confined to their own integration-specific package and are not exported (marked `internal`) then it'll remain pretty clean.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#issuecomment-1421921648"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288175709", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288222760", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288226629", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288226788", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288228285", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288228677", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288233201", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288233689", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288234456", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288234963", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288240646", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288243313", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288259614", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288279587", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4746#pullrequestreview-1288281623", "body": ""}
{"title": "fix(deps): update sentryversion to v6.13.1", "number": 4747, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4747", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.sentry:sentry-logback | 6.13.0 -> 6.13.1 |  |  |  |  |\n| io.sentry:sentry-log4j2 | 6.13.0 -> 6.13.1 |  |  |  |  |\n| io.sentry:sentry | 6.13.0 -> 6.13.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ngetsentry/sentry-java\n\n### [`v6.13.1`](https://togithub.com/getsentry/sentry-java/blob/HEAD/CHANGELOG.md#6131)\n\n[Compare Source](https://togithub.com/getsentry/sentry-java/compare/6.13.0...6.13.1)\n\n##### Fixes\n\n-   Fix transaction performance collector oom ([#2505](https://togithub.com/getsentry/sentry-java/pull/2505))\n-   Remove authority from URLs sent to Sentry ([#2366](https://togithub.com/getsentry/sentry-java/pull/2366))\n-   Fix `sentry-bom` containing incorrect artifacts ([#2504](https://togithub.com/getsentry/sentry-java/pull/2504))\n\n##### Dependencies\n\n-   Bump Native SDK from v0.5.3 to v0.5.4 ([#2500](https://togithub.com/getsentry/sentry-java/pull/2500))\n    -   [changelog](https://togithub.com/getsentry/sentry-native/blob/master/CHANGELOG.md#054)\n    -   [diff](https://togithub.com/getsentry/sentry-native/compare/0.5.3...0.5.4)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency @types/node to v18.13.0", "number": 4748, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4748", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/node (source) | 18.11.19 -> 18.13.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "[UNB-973] Fix participant section breakpoint", "number": 4749, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4749"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4749#pullrequestreview-1287665308", "body": ""}
{"title": "Cleanup old unused AWS resources and CDK code", "number": 475, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475", "body": "Restructured the CloudFront Stack to have landing page as default origin\nChanged how we add extra behaviors. Now we have to add each extra behavior explicitly in the code (no more looping over list of buckets)\nRemoved old CloudFront distros, DNS records and permission objects\nCleaned up config objects\nRemoved old DNS records in management account\nRemoved unused config params\nAdded a lambda function to return custom error responses for /dashboard. This is needed for SPA sites \n\nAll these changes have been deployed to both Dev and Prod. They work as expected.\nUpdate:\nClarification:\n- Custom Error Lambda function in this code are used to deal with some SPA site issues. We were initially doing the same thing but using CloudFront custom errors where 404 and 403 error were being redirected to index.html with a 200 status code. This simply a cleaner way of doing that.\n- Path re-write function is used to map a sub-path like /dashboard to /dashboard/index.html this is also a limitation of hosting static sites on S3\nNew Changes:\n    - Removed IP filter lambda function\n    - Added WAF to CloudFront instead of IP filter lambda\n    - Updated WAF rule to do the same job as IP filter lambda\n    - Added a custom header to WAF as well as custom error lambda. This is to allow traffic coming from Lambda function to hit restricted dashboard site. Once we go public and dashboard is no longer behind an IP filter we can remove it. \n    - Made the custom error lambda generic. It's no longer site specific\n    - Removed more old certs\n    - Updated CloudFront Cert to include www.getunblocked.com domain\n    This PR is now ready to be merged"}
{"comment": {"body": "There's a lot going on in this PR that doesn't quite match the description. Ex: what's the purposes behind the response code re-writing etc? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#issuecomment-1059573341"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-900772341", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-900785927", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-900787015", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-902043204", "body": ""}
{"comment": {"body": "This is a header I added to allow CloudFront to pass through WAF when making requests to load a custom error page. WAF was blocking based on IP only and that was causing CF to be blocked too. \r\n\r\nWe will be using this for local-dev too. For now it's an easy way for getting around IP filter if someone is working from home. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#discussion_r820953209"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-902045384", "body": ""}
{"comment": {"body": "I added a couple of error pages to landing site for better and cleaner error handling. We used to get an ugly message from Lambda before this. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#discussion_r820954780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-902046328", "body": ""}
{"comment": {"body": "`/dashboard*` part is to make sure `https://getunblocked.com/dashboard` and `https://getunblocked.com/dashboard/` both work as expected", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#discussion_r820955457"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-902047175", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-902048924", "body": ""}
{"comment": {"body": "The new WAF rules do the following:\r\n- NOT statement makes sure we apply the rule only to Dashboard \r\n- AND rule applies IP filter \r\n- The `byteMatchStatement` looks for the special header to allow CF traffic on custom errors through ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#discussion_r820957377"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-902072335", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-902074453", "body": ""}
{"comment": {"body": "nit: maybe pull the value into an exported const for reference by the WAF config", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#discussion_r820975682"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#pullrequestreview-902080918", "body": ""}
{"comment": {"body": "Lambda edge does not support env vars unfortunately and this had to be coded in. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/475#discussion_r820980325"}}
{"title": "[UNB-961] Fix up invite dialog with ability to skip", "number": 4750, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4750", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4750#pullrequestreview-1290205368", "body": ""}
{"title": "Login-as-user should not impersonate the Intercom user", "number": 4751, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4751"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4751#pullrequestreview-1287880373", "body": ""}
{"comment": {"body": "Does a token refresh after killing impersonation result in intercom reloading?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4751#discussion_r1099121504"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4751#pullrequestreview-1287880545", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4751#pullrequestreview-1287881365", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4751#pullrequestreview-1287882923", "body": ""}
{"comment": {"body": "@matthewjamesadam or @jeffrey-ng can confirm, but my understanding is that every refresh token event triggers a getPerson event, which will in turn disable/enable intercom now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4751#discussion_r1099123910"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4751#pullrequestreview-**********", "body": ""}
{"comment": {"body": "It's all good; confirmed it works via local stack. Intercom is correctly unloaded and reloaded as soon as you impersonate and stop impersonating respectively.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4751#discussion_r1099153027"}}
{"title": "Basic Health monitor", "number": 4752, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4752", "body": "Added a health status monitor where one can log the current state of the stores.\nHopefully helps with debugging any issues with broken stream states.\nCurrently hooked up in VSCode:\nAuth\nTeam\nPerson\nCurrentFile\nRepo\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4752#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4752#pullrequestreview-**********", "body": ""}
{"comment": {"body": "The only issue that might happen with this is that we will always automatically activate these streams on startup.  It might be a bit unexpected, since everywhere else we treat streams as \"on demand\" and only activate them when someone actually needs them.\r\n\r\nNot sure if there's any way around this.  We could only activate the health monitor the first time the command is run, wait a second, then show the output..... kinda odd though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4752#discussion_r1100813830"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4752#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4752#pullrequestreview-**********", "body": ""}
{"title": "chore(deps): update actions/setup-java action to v3.10.0", "number": 4753, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4753", "body": "This PR contains the following updates:\n| Package | Type | Update | Change |\n|---|---|---|---|\n| actions/setup-java | action | minor | v3.9.0 -> v3.10.0 |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nactions/setup-java\n\n### [`v3.10.0`](https://togithub.com/actions/setup-java/releases/tag/v3.10.0)\n\n[Compare Source](https://togithub.com/actions/setup-java/compare/v3.9.0...v3.10.0)\n\nIn scope of this release we added support for Oracle JDK ([https://github.com/actions/setup-java/pull/450](https://togithub.com/actions/setup-java/pull/450)).\n\n```yaml\nsteps:\n - name: Checkout\n  uses: actions/checkout@v3\n - name: Setup-java\n  uses: actions/setup-java@v3\n  with:\n   distribution: oracle\n   java-version: 11\n```\n\n##### Supported distributions\n\nCurrently, the following distributions are supported:\n\n| Keyword | Distribution | Official site | License\n|-|-|-|-|\n| `temurin` | Eclipse Temurin | [Link](https://adoptium.net/) | [Link](https://adoptium.net/about.html)\n| `zulu` | Azul Zulu OpenJDK | [Link](https://www.azul.com/downloads/zulu-community/?package=jdk) | [Link](https://www.azul.com/products/zulu-and-zulu-enterprise/zulu-terms-of-use/) |\n| `adopt` or `adopt-hotspot` | AdoptOpenJDK Hotspot | [Link](https://adoptopenjdk.net/) | [Link](https://adoptopenjdk.net/about.html) |\n| `adopt-openj9` | AdoptOpenJDK OpenJ9 | [Link](https://adoptopenjdk.net/) | [Link](https://adoptopenjdk.net/about.html) |\n| `liberica` | Liberica JDK | [Link](https://bell-sw.com/) | [Link](https://bell-sw.com/liberica_eula/) |\n| `microsoft` | Microsoft Build of OpenJDK | [Link](https://www.microsoft.com/openjdk) | [Link](https://docs.microsoft.com/java/openjdk/faq)\n| `corretto` | Amazon Corretto Build of OpenJDK | [Link](https://aws.amazon.com/corretto/) | [Link](https://aws.amazon.com/corretto/faqs/)\n| `oracle` | Oracle JDK | [Link](https://www.oracle.com/java/technologies/downloads/) | [Link](https://java.com/freeuselicense)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Updated CreateWalkthrough", "number": 4754, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4754", "body": "Max width for content.\nAdded border for empty contributor list."}
{"comment": {"body": "<img width=\"415\" alt=\"CleanShot 2023-02-07 at 15 04 32@2x\" src=\"https://user-images.githubusercontent.com/1553313/217386958-74faca03-5eb7-48f5-bfeb-c1fdca98d49e.png\">\r\n\r\nFix tabs for mobile", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4754#issuecomment-1421591957"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4754#pullrequestreview-1288126134", "body": ""}
{"title": "Deploy SCM secrets to Auth Service", "number": 4755, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4755", "body": "Auth service currently requires SCM OAuth client secret in orde to perform the OAuth code exchange,\nwhich means that the SCM and Auth services now have access to the SCM secrets.\nThis improves the current situation, where SCM secrets are available to all services.\nHowever, we could do better and restrict SCM secrets so that they are deployed to the SCM service only.\nIn order to acheive this, we would need secure service-to-service synchronous communication so that\nthe auth-service could offload OAuth code exchange to the SCM-service.\nFixes auth-service crash-looping:\n```\nc.s.h.ConfigException: Error loading config because:\n- Could not instantiate 'com.nextchaptersoftware.scm.config.ScmConfig' because:\n\n    - 'gitlab': - Could not instantiate 'com.nextchaptersoftware.scm.config.GitLabConfig' because:\n\n        - 'clientSecret': Missing from config\n\n    - 'webhookAuthentication': - Could not instantiate 'com.nextchaptersoftware.scm.config.WebhookAuthentication' because:\n\n        - 'githubHMACSecret': Missing from config\nat c.s.h.ConfigLoader$returnOrThrow$1.invoke(ConfigLoader.kt:215)\nat c.s.h.ConfigLoader$returnOrThrow$1.invoke(ConfigLoader.kt:212)\nat c.s.h.fp.ValidatedKt.getOrElse(Validated.kt:115)\nat c.s.h.ConfigLoader.returnOrThrow(ConfigLoader.kt:212)\nat c.n.s.c.ScmConfig.clinit(ScmConfig.kt:76)\n... 15 common frames omitted\n\nWrapped by: j.l.ExceptionInInitializerError: null\n    at c.n.a.ModuleKt.module$default(Module.kt:45)\n    at c.n.a.ApplicationKt$main$1$2.invoke(Application.kt:41)\n    at c.n.a.ApplicationKt$main$1$2.invoke(Application.kt:34)\n    at c.n.s.ServerKt$startServer$1.invoke(Server.kt:19)\n    at c.n.s.ServerKt$startServer$1.invoke(Server.kt:13)\n    at i.k.s.e.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:324)\n    at i.k.s.e.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:313)\n    at i.k.s.e.ApplicationEngineEnvironmentReloading.avoidingDoubleStartup(ApplicationEngineEnvironmentReloading.kt:341)\n    at i.k.s.e.ApplicationEngineEnvironmentReloading.instantiateAndConfigureApplication(ApplicationEngineEnvironmentReloading.kt:313)\n    at i.k.s.e.ApplicationEngineEnvironmentReloading.createApplication(ApplicationEngineEnvironmentReloading.kt:150)\n    at i.k.s.e.ApplicationEngineEnvironmentReloading.start(ApplicationEngineEnvironmentReloading.kt:280)\n    at i.k.s.n.NettyApplicationEngine.start(NettyApplicationEngine.kt:211)\n    at c.n.service.ServerKt.startServer(Server.kt:23)\n    at c.n.a.ApplicationKt.main(Application.kt:34)\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4755#pullrequestreview-1288138276", "body": ""}
{"title": "Validate the refresh token chain", "number": 4756, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1288194514", "body": ""}
{"comment": {"body": "Eagerly bail if any of the required infos are missing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1099431851"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1288197392", "body": ""}
{"comment": {"body": "This is important: prevents a rewind attack where an attacker could send a super old refresh token to move the invalidation window back to that point and then start using previously invalid tokens again.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1099433751"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1288198161", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1289851329", "body": "nits"}
{"comment": {"body": "nice test, nesting clients is a slick idea.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100586591"}}
{"comment": {"body": "I think you can still call the legacy (albeit simple) validation:\r\n```suggestion\r\n                    jwt.refreshTokenValidator(it)\r\n```\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100595290"}}
{"comment": {"body": "log the attack", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100600562"}}
{"comment": {"body": "It's possible for the `set` call on this line to obliterate an _earlier_ min-IAT value, leaving a tiny window for an attacker to use a refresh token minted just before this token has been deny-listed.\r\n\r\nIdeally, the `getTokenChainMinIAT` and `setTokenChainMinIAT` calls should be transactional, or in a locked region; but the window is tiny.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100607295"}}
{"comment": {"body": "Curious why you are keeping this API?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100609002"}}
{"comment": {"body": "reduce the delay to keep tests fast? should still work with 1 millisecond delay? same below ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100610404"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1289916431", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1289965677", "body": "Pushed some changes"}
{"comment": {"body": "The validator was changed to do token chain validation, which will result in a 401 in some cases (like if old tokens are used). This is explicitly meant to circumvent those checks.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100656646"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1290058305", "body": ""}
{"comment": {"body": "I tried reducing this to 100ms but it's failing. I'm digging into why because it's probably indicative of a bug.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100721062"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1290062551", "body": ""}
{"comment": {"body": "nvm, got confused", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100723951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1290062891", "body": ""}
{"comment": {"body": "nvm, was confused by unrelated changes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100724181"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#pullrequestreview-1290073001", "body": ""}
{"comment": {"body": "Figured it out - Jwt `issuedAt` field is second precision", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4756#discussion_r1100731465"}}
{"title": "Fix db locks", "number": 4757, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4757"}
{"title": "Clean up powerml scripts", "number": 4758, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4758"}
{"title": "Open source 2", "number": 4759, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4759"}
{"title": "Rename ambiguous model and api 'teamMember' property to 'author'", "number": 476, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476", "body": "teamMember is a type, not a descriptive property name. This will result in a DB drop"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#pullrequestreview-900487370", "body": "."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#pullrequestreview-900489930", "body": ""}
{"comment": {"body": "Hmm. I can see why this would be necessary if there where two properties that were both team member types. But in this case it\u2019s not ambiguous right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#discussion_r819753757"}}
{"comment": {"body": "this rename makes sense \u2705 \r\n\r\nbut I would also rename the PG column ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#discussion_r819754534"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#pullrequestreview-900502305", "body": ""}
{"comment": {"body": "woops missed that", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#discussion_r819763169"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#pullrequestreview-900509397", "body": ""}
{"comment": {"body": "`teamMemberId` doesn't say anything about its semantics, although in this case I guess there's only one possible meaning? Similar logic applies for database property renaming so I'm striving for some consistency...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#discussion_r819768405"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#pullrequestreview-900510987", "body": ""}
{"comment": {"body": "I don't really understand why this was re-generated with 4 spaces instead of 2. I don't think the IDE did this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#discussion_r819769535"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#pullrequestreview-900584064", "body": ""}
{"comment": {"body": "database properties are different, because it's typed on the KT side", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#discussion_r819820625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#pullrequestreview-900585187", "body": ""}
{"comment": {"body": "Up to you. I think either of these are good:\r\n- teamMemberId\r\n- authorId", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#discussion_r819821442"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#pullrequestreview-900586001", "body": ""}
{"comment": {"body": "Feels like this should be a lint rule or something, package-lock should only be changed with corresponding package.json changes... I'd just drop this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/476#discussion_r819822011"}}
{"title": "Add deduped map to TeamMemberStore", "number": 4760, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4760", "body": "This replaces the hack code that compares team member display names\nNote that this is also temporary code (i.e. to be removed once identity is fixed), just a less hack-y implementation than what was there before\nLeverages a fuzzy string match to find user matches, and maps the id to a single user given a match\ni.e. Given users { id: 1, name: \"Ben\" } and { id: 2, name: \"Ben Ng\" }, the map will read { 1: { id: 2, name: \"Ben Ng\" }, 2: { id: 2, name: \"Ben Ng\" } } (the logic skews bias towards users that already have Unblocked accounts) \n\n\nThis is attached to the new activeMembersStream which is used sparingly and primarily in Topic related UI code and mentions"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4760#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Updated root rendering per React 18 (dependency bot updated us to 18 a week ago)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4760#discussion_r1100548413"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4760#pullrequestreview-**********", "body": ""}
{"comment": {"body": "What's the decision with this threshold? Why not use the default 0.7?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4760#discussion_r1107693841"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4760#pullrequestreview-**********", "body": ""}
{"comment": {"body": "For the most part, I believe these team members are only used for display purposes?\r\n\r\nThe only time we actually use these in an API operation would be setting experts? In that case, we would dedupe the the GH & Slack member into a single member and use that resolved member for expert... I think that's okay?\r\n\r\nFor example, when ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4760#discussion_r1107699969"}}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.19.33", "number": 4761, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4761", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.19.32 -> 2.19.33 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.19.32 -> 2.19.33 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.19.32 -> 2.19.33 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.19.32 -> 2.19.33 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.19.32 -> 2.19.33 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime (source) | 2.19.32 -> 2.19.33 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.19.32 -> 2.19.33 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.19.32 -> 2.19.33 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.19.32 -> 2.19.33 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.19.33`]()\n\n[Compare Source]()\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   -   ProfileCredentialsProvider and ProfileTokenProvider can reload credentials when disk profile changes\n        -   Updated DefaultCredentialsProvider chain for reloading credentials\n        -   Service support classes store ProfileFile as a Supplier interface\n        -   SdkClientOption and SdkExecutionAttributes constants for PROFILE_FILE have been deprecated in favor of PROFILE_FILE_SUPPLIER\n    -   Updated endpoint and partition metadata.\n\n#### **AWS Transfer Family**\n\n-   ### Features\n    -   Updated the documentation for the ImportCertificate API call, and added examples.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Move GitHub config and secrets to SCM", "number": 4762, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4762", "body": "GitHub secrets no longer available globally\nNow accessible in Auth, SCM, and Admin services"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4762#pullrequestreview-1288361528", "body": ""}
{"title": "try agian", "number": 4763, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4763"}
{"title": "chore(deps): update dependency esbuild-loader to v3", "number": 4764, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4764", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| esbuild-loader | ^2.21.0 -> ^3.0.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nesbuild-kit/esbuild-loader\n\n### [`v3.0.0`]()\n\n[Compare Source]()\n\n#### Migration guide\n\n-   `ESBuildMinifyPlugin` was renamed to `EsbuildPlugin` to indicate it can be used as a general interface to esbuild (not just a minifier, but transformer).\n    Import `EsbuildPlugin` instead:\n\n```diff\n- const { ESBuildMinifyPlugin } = require('esbuild-loader')\n+ const { EsbuildPlugin } = require('esbuild-loader')\n```\n\n-   You no longer need to pass in `loader` to the loader. It will now detect which loader to use based on the file extension. This also means you can consolidate `esbuild-loader` rules for different file types into one.\n    > ** Note:** For this to work, be sure to follow file extension conventions such as using `.jsx`/`.tsx` for files that contain JSX.\n\n-   You can now use the `tsconfig` property to pass in a tsconfig file, and it will even resolve `extends`\n\n-   tsconfig.json `includes`/`excludes`/`files` are now respected so it will only apply the `tsconfig.json` to matching files. However, TypeScript compilation will always work regardless of `tsconfig.json`.\n\n-   If Webpack's target is `web`, `EsbuildPlugin` will default to using `format: iife` unless otherwise specified. This change was made to [prevent window pollution]().\n\n-   Make sure your Node.js version is at least v16\n\n#### Changes\n\n##### Bug Fixes\n\n-   apply tsconfig only to matching files ([#310]()) ([7c68769]())\n\n##### Build System\n\n-   add exports map and bundle with pkgroll ([#265]()) ([f907edd]())\n\n##### Code Refactoring\n\n-   esbuild to auto-detect which loader to use ([#304]()) ([378c74e]())\n-   remove `sourcemap` option ([#305]()) ([53cbc73]())\n-   rename `ESBuildMinifyPlugin` to `EsbuildPlugin` ([#307]()) ([b052cdd]())\n\n##### Continuous Integration\n\n-   update testing to Node 16 ([#302]()) ([43f1154]())\n\n##### Features\n\n-   `tsconfig` option ([#311]()) ([92b49e9]())\n-   enhanced tsconfig loading behavior ([#309]()) ([c6bb06e]())\n-   **plugin:** default `format` to `iife` ([a9e8e7e]())\n\n##### BREAKING CHANGES\n\n-   `tsconfig.json` is now only applied to files it matches (via `include`/`exclude`/`files`)\n-   **plugin:** Plugin default format is now `iife` when Webpack's target is `web` & esbuild's target is below `esnext`\n-   `ESBuildMinifyPlugin` renamed to `EsbuildPlugin`; `MinifyPluginOptions` renamed to `EsbuildPluginOptions`\n-   `sourcemap` option removed\n-   Default value of loader now auto-detects which loader to use based on the extension of the file\n-   Increase supported Node.js to v16\n-   exports map and bundled files\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Explain what last active is in admin web", "number": 4765, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4765", "body": "\nfixes "}
{"title": "Onboarding update 2023", "number": 4766, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4766", "body": "Update assets\nUpdate content\n\nAggregate status bar counts to be a single insights count (similar logic used in the last slide of onboarding)\n\n\nNote: this flow is still feature flagged"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4766#pullrequestreview-1289775460", "body": ""}
{"title": "UNB-962 Fix topic table height", "number": 4767, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4767", "body": "also, newly created threads in vscode should open in the beside view column"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4767#pullrequestreview-1289817083", "body": ""}
{"title": "Adjust admin language", "number": 4768, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4768", "body": "Safari annoyingly thinks the admin web is not in English.\n\n"}
{"title": "UNB-969 - Workaround for safari lineclamp bug", "number": 4769, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4769", "body": "This codepen explains the problem quite well. We use the line-clamp css property to cut off the text in the insight cards:\n\n(Chrome)\nThere are issues with this css property in Safari browsers, where in the case of multiple paragraphs, the 'clamped' paragraph blocks end up overlapping the main block. \n\n(Safari)\nAs a workaround, only display the first block of the PR description in the insight cards. This gets around the overlapping blocks.\n\n(Safari)\nA knockoff consequence here is that if the first block of a description is only one line long, it would only ever display one line:\n"}
{"comment": {"body": "Based on the codepen, did you try the manual approach of `Removing the p elements loses the paragraph structure, even after adding br elements`?\r\n\r\nI think the downsides stated can be worked around.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4769#issuecomment-1423345896"}}
{"comment": {"body": "> Based on the codepen, did you try the manual approach of `Removing the p elements loses the paragraph structure, even after adding br elements`?\r\n> \r\n> I think the downsides stated can be worked around.\r\n\r\nYes, see the `stripParagraph` arg. It didn't work as intended (as in, it still overlapped). I ran this PR's solution by Ben and he was onboard.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4769#issuecomment-1424613185"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4769#pullrequestreview-1290077705", "body": ""}
{"comment": {"body": "Topic rows should be linkable", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4769#discussion_r1100735404"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4769#pullrequestreview-1296668562", "body": ""}
{"title": "A couple of Dashboard infra fixes", "number": 477, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/477", "body": "Update CloudFront ID for Dev\nFix import path in index.html\nAdd /dashboard/ subdir to S3 sync command"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/477#pullrequestreview-900785752", "body": ""}
{"title": "Delete Agora", "number": 4770, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4770"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4770#pullrequestreview-1290118279", "body": ""}
{"title": "Unblock Jeff -- write out JAR resources to local filesystem for now", "number": 4771, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4771"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4771#pullrequestreview-1290139754", "body": ""}
{"title": "Initial reference ingestion logging", "number": 4772, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4772", "body": "Logging for potential race condition outlined here: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4772#pullrequestreview-1290206910", "body": ""}
{"title": "Replace Author with ScmUser", "number": 4773, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4773"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4773#pullrequestreview-1290196998", "body": "Cool, getting there  \nMight be helpful as a goal. This is how we know we are \"done\":\n1. no references to \"github\" in lib-pringestion\n2. all GitHub* classes in client-scm are marked as internal"}
{"title": "fix for ALB log buckets in prod", "number": 4774, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774", "body": "Added a flag to force using absolute bucket names instead of qualified bucket names\nSet the correct name for prod alb logs buckets.\nI have manually deployed this to prod and all is working fine now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774#pullrequestreview-1290256649", "body": ""}
{"comment": {"body": "excludeSuffix?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774#discussion_r1100850900"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774#pullrequestreview-1290256938", "body": "Minor comment, thanks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774#pullrequestreview-1290341513", "body": ""}
{"comment": {"body": "It got merged before I get a chance to change it. Btw NoXYZ and ExcludeXYX is the best example of how DevOps/System Engineers think vs Software Engineers ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774#discussion_r1100914875"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774#pullrequestreview-1290341797", "body": ""}
{"comment": {"body": "We like explicit flags because the logic available to us is normally very limited.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774#discussion_r1100915090"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774#pullrequestreview-1290402656", "body": ""}
{"comment": {"body": "I think that's a lovely way of saying you're adorable. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4774#discussion_r1100959286"}}
{"title": "enable termination protection for internet facing ALBs", "number": 4775, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4775", "body": "Enabling delete protection on all ALBs. Our ALBs are managed by a Kubernetes controller if a faulty configuration goes out causing it to be deleted we would need up to 48 hours to have the new DNS being updated everywhere. \nThis should prevent that"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4775#pullrequestreview-1290212257", "body": ""}
{"title": "make liveness a bit less agrressive", "number": 4776, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4776", "body": "Changing liveness and startup probe (__deepcheck) timeouts to 7 seconds instead of 4. (I will update grafana probes to 10)\nI felt 7 is more reasonable and would like to avoid making these checks too forgiving which could mask a lot of issues. \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4776#pullrequestreview-1290234598", "body": ""}
{"title": "chore(deps): update aws-cdk monorepo to v2.64.0", "number": 4777, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4777", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| aws-cdk | 2.63.2 -> 2.64.0 |  |  |  |  |\n| aws-cdk-lib | 2.63.2 -> 2.64.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-cdk\n\n### [`v2.64.0`]()\n\n[Compare Source]()\n\n##### Features\n\n-   **cfnspec:** cloudformation spec v109.0.0 ([#23968]()) ([5d59134]())\n-   **cfnspec:** cloudformation spec v109.0.0 ([#23984]()) ([affe040]())\n-   **cli:** --hotswap will not use CFN anymore, --hotswap-fallback to fall back if necessary ([#23653]()) ([a5317ca]()), closes [#22784]() [#21773]() [#21556]() [#23640]()\n-   **elbv2:** add metrics to INetworkLoadBalancer and IApplicationLoadBalancer ([#23853]()) ([cb889bc]()), closes [#10850]()\n-   **iam:** implement IGrantable to Policy and ManagedPolicy ([#22712]()) ([d3df40f]()), closes [#10308]()\n-   **lambda:** enable RuntimeManagementConfig  ([#23891]()) ([be4f971]()), closes [#23890]()\n-   **s3:** allow configuring S3 Object Lock ([#23744]()) ([bdcd6c8]()), closes [#5247]() [#21738]()\n\n##### Bug Fixes\n\n-   Use the correct LB full name when creating metrics for imported LBs ([#23972]()) ([16c23b7]()), closes [#23853]()\n-   **cdk-assets:**  asset concurrency leaves a corrupted archive ([#24026]()) ([989454f]())\n-   **cdk-assets:** packaging assets is broken on Node older than 14.17 ([#23994]()) ([5bde92c]()), closes [#23859]()\n-   **codedeploy:** cross-region referenced groups use wrong config ([#23986]()) ([390ec78]())\n-   **core:** cross-stack reference error doesn't include violation ([#23987]()) ([c7ad66f]())\n-   **ec2:** Cannot deploy VPC flow log with other resources that requires bucket policies  ([#23889]()) ([e646ad5]()), closes [#18985]()\n-   **pipelines:** cannot configure actionName for all sources ([#24027]()) ([9cd639b]())\n-   **s3:** infer bucketWebsiteUrl and bucketDomainName suffixes from bucket region ([#23919]()) ([252f052]())\n-   **s3-deployment:** wrong URL in BucketDeployment.deployedBucket.bucketWebsiteUrl ([#24055]()) ([ece46db]()), closes [#23354]()\n\n***\n\n#### Alpha modules (2.64.0-alpha.0)\n\n##### Features\n\n-   **cloud9:** support setting environment owner ([#23878]()) ([08a2f36]()), closes [#22474]()\n-   **redshift:** Tables can include comments ([#23847]()) ([46cadd4]()), closes [#22682]()\n\n##### Bug Fixes\n\n-   **servicecatalogappregistry:** default stack name is not meaningful and causes conflict when multiple stacks deployed to the same account-region ([#23823]()) ([420b5ff]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.20.0", "number": 4778, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4778", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.19.33 -> 2.20.0 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.19.33 -> 2.20.0 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.19.33 -> 2.20.0 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.19.33 -> 2.20.0 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.19.33 -> 2.20.0 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime (source) | 2.19.33 -> 2.20.0 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.19.33 -> 2.20.0 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.19.33 -> 2.20.0 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.19.33 -> 2.20.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.20.0`]()\n\n[Compare Source]()\n\n#### **AWS Backup**\n\n-   ### Features\n    -   This release added one attribute (resource name) in the output model of our 9 existing APIs in AWS backup so that customers will see the resource name at the output. No input required from Customers.\n\n#### **AWS CRT HTTP Client**\n\n-   ### Features\n    -   The AWS CRT HTTP Client is now generally available. Visit [Dev Guide]() for more information.\n\n#### **AWS Glue**\n\n-   ### Features\n    -   DirectJDBCSource + Glue 4.0 streaming options\n\n#### **AWS Lake Formation**\n\n-   ### Features\n    -   This release removes the LFTagpolicyResource expression limits.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon CloudFront**\n\n-   ### Features\n    -   CloudFront Origin Access Control extends support to AWS Elemental MediaStore origins.\n\n#### **Amazon S3**\n\n-   ### Bugfixes\n    -   Bug fix for Default Md5 checksum was getting skipped even when new http checksum was not set to any checksum algorithm.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Add HAR support to VSCode to record all node-fetch requests", "number": 4779, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4779", "body": "This pr does several things for VSCode extension:\n1. It adds a dependency on our own forked module to allow for HAR generation of all HTTP requests using node-fetch. It instruments the low-level http.Agent used by node-fetch.\n\n2. Adds a rolling file writer for the HAR (similar to how we do logs). It uses debouncing to reduce thrashing the disk too much.\n3. It adds a setting to control HAR generation. By default, HAR generation is DISABLED, and is simply a pass through to node-fetch."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4779#pullrequestreview-1291828810", "body": ""}
{"comment": {"body": "Debounce reduces disk thrashing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4779#discussion_r1101907943"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4779#pullrequestreview-1291829508", "body": ""}
{"comment": {"body": "By default, har diagnostics is disabled, and has to be enabled via settings.\r\nIt is effectively a pass-through to node-fetch when false.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4779#discussion_r1101908369"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4779#pullrequestreview-1291838481", "body": ""}
{"title": "Setup Team & TeamMember integration to VSCode", "number": 478, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478", "body": "Add team fetching and team member fetching to vscode.\nCurrently integrated workarounds due to missing team context. Should be fixed in the near future with updated API specs.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902096438", "body": ""}
{"comment": {"body": "This will be fixed in subsequent PR as we just added teamID to thread model.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r820991520"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902127149", "body": ""}
{"comment": {"body": "So you're not actually implementing the if-modified-since headers in this PR?  That will come in a follow-up?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r821013845"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902134367", "body": ""}
{"comment": {"body": "We have to eventually handle cases where your personal team membership changes, though I think based on our discussions earlier this will be driven by changes to the team claims in the token rather then through our API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r821019074"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902136977", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902138916", "body": ""}
{"comment": {"body": "This implies we hold onto the team data when we log out -- not sure if this is correct or not? Should we be clearing the team membership when we don't have valid auth anymore?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r821022127"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902140456", "body": ""}
{"comment": {"body": "I'm not sure this is the right approach.  Team membership definitely needs to be something we subscribe and keep live so we know when other team members change...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r821023086"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902140820", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902168328", "body": ""}
{"comment": {"body": "Follow up. Have some questions on the backend that I want in a separate PR. Can add todo if helps.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r821043192"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902169841", "body": ""}
{"comment": {"body": "Agreed. I'm not sure who/what holds onto this right now...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r821044282"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902189480", "body": ""}
{"comment": {"body": "I think this means we should be folding this into AuthStore to some extent.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r821058208"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902247487", "body": ""}
{"comment": {"body": "At that point we'll just use the per-team ThreadModel ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r821098731"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902251749", "body": "Looks good -- lots of stuff we'll clean up as we make the data stores nicer but this works nicely with what we have now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#pullrequestreview-902316500", "body": ""}
{"comment": {"body": "Yup.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/478#discussion_r821148846"}}
{"title": "Fix admin web language properly", "number": 4780, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4780", "body": "Missed some from https://github.com/NextChapterSoftware/unblocked/pull/4768"}
{"title": "Add ScmOrg abstract class and tweak ScmUser", "number": 4781, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4781", "body": "Will be used in next change.\nAlso minor ScmUser changes\n- remove provider. provider is insufficient in enterprise cases.\n- rename name to displayName\n- id must be String, since Bitbucket ids are not numbers"}
{"title": "Encapsulate GitLab classes in client-SCM model", "number": 4782, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4782"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4782#pullrequestreview-**********", "body": ""}
{"title": "Archive low relevance pull requests", "number": 4783, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4783", "body": "We need to check the contents of the pull request description in addition to the comment counts.\nThe logic here will restore pull requests if a conversation is later opened up for it."}
{"title": "Introduce ScmPullRequestComment and ScmPullRequestFile classes", "number": 4784, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4784", "body": "Replaces references to GitHubPullRequestReviewComment with ScmPullRequestComment and GitHubPullRequestReviewFile with ScmPullRequestFile."}
{"comment": {"body": "@richiebres Let's tackle that after we've replaced all references to GitHub classes in PR ingestions and have ingestion for GitLab/BitBucket working. Marking it internal would require cleaning up some tests (which we def want) but I'd like to first deliver the bits that are blocking customer adoption.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4784#issuecomment-1424972073"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4784#pullrequestreview-1292153432", "body": "Can we make any of the GitHub classes internal?\n- GitHubPullRequestReviewComment\n- GitHubPullRequestReviewFile"}
{"title": "Add topic experts for curated topics", "number": 4785, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4785"}
{"title": "Remove trace logging from ktor/auth", "number": 4786, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4786", "body": "Not needed afaict. Can't remember why we added. Spam."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4786#pullrequestreview-1291832637", "body": ""}
{"title": "Fix race condition in VSCode RepoStore", "number": 4787, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4787"}
{"comment": {"body": "Closing -- will add into Jeff's PR here: https://github.com/NextChapterSoftware/unblocked/pull/4788/files", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4787#issuecomment-1424686269"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4787#pullrequestreview-1291811196", "body": ""}
{"comment": {"body": "This was the key problem -- we were synchronously mapping over the collection and running an asynchronous method.  I don't know how eslint didn't catch this -- maybe there's a rule we need to enable?  This change makes the loop itself synchronous, returning a promise that will resolve asynchronously.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4787#discussion_r1101896053"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4787#pullrequestreview-1291820039", "body": ""}
{"comment": {"body": "ugh...\r\n\r\nI think the intention should be to loop across all the teams to generate a list of promises and then Promise.all.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4787#discussion_r1101902076"}}
{"title": "Update Repo Store", "number": 4788, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4788", "body": "Refactor VSCode Repo Store to be completely stream based."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4788#pullrequestreview-1293817266", "body": ""}
{"title": "Putting the chat app in the bin, and refactored the project file to only reference relevant projects and schemes", "number": 4789, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4789"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4789#pullrequestreview-1291943488", "body": ""}
{"comment": {"body": "why not remove?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4789#discussion_r1101985641"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4789#pullrequestreview-1292119440", "body": ""}
{"title": "INFO logging for exposed", "number": 479, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/479"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/479#pullrequestreview-900831768", "body": ""}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.20.1", "number": 4790, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4790", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.20.0 -> 2.20.1 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.20.0 -> 2.20.1 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.20.0 -> 2.20.1 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.20.0 -> 2.20.1 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.20.0 -> 2.20.1 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime (source) | 2.20.0 -> 2.20.1 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.20.0 -> 2.20.1 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.20.0 -> 2.20.1 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.20.0 -> 2.20.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.20.1`]()\n\n[Compare Source]()\n\n#### **AWS Migration Hub Refactor Spaces**\n\n-   ### Features\n    -   This release adds support for creating environments with a network fabric type of NONE\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon Chime SDK Meetings**\n\n-   ### Features\n    -   Documentation updates for Chime Meetings SDK\n\n#### **Amazon CloudWatch Evidently**\n\n-   ### Features\n    -   Updated entity overrides parameter to accept up to 2500 overrides or a total of 40KB.\n\n#### **Amazon EMR Containers**\n\n-   ### Features\n    -   EMR on EKS allows configuring retry policies for job runs through the StartJobRun API. Using retry policies, a job cause a driver pod to be restarted automatically if it fails or is deleted. The job's status can be seen in the DescribeJobRun and ListJobRun APIs and monitored using CloudWatch events.\n\n#### **Amazon Lex Model Building V2**\n\n-   ### Features\n    -   AWS Lex now supports Network of Bots.\n\n#### **Amazon Lex Runtime V2**\n\n-   ### Features\n    -   AWS Lex now supports Network of Bots.\n\n#### **Amazon Lightsail**\n\n-   ### Features\n    -   Documentation updates for Lightsail\n\n#### **Amazon WorkDocs**\n\n-   ### Features\n    -   Doc only update for the WorkDocs APIs.\n\n#### **Amazon WorkSpaces**\n\n-   ### Features\n    -   Removed Windows Server 2016 BYOL and made changes based on IAM campaign.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update grpcversion to v1.53.0", "number": 4791, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4791", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.grpc:protoc-gen-grpc-java | 1.52.1 -> 1.53.0 |  |  |  |  |\n| io.grpc:grpc-stub | 1.52.1 -> 1.53.0 |  |  |  |  |\n| io.grpc:grpc-protobuf | 1.52.1 -> 1.53.0 |  |  |  |  |\n| io.grpc:grpc-netty-shaded | 1.52.1 -> 1.53.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update stylelint monorepo (major)", "number": 4792, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4792", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| stylelint (source) | ^14.2.0 -> ^15.0.0 |  |  |  |  |\n| stylelint-config-standard | ^29.0.0 -> ^30.0.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nstylelint/stylelint\n\n### [`v15.0.0`](https://togithub.com/stylelint/stylelint/blob/HEAD/CHANGELOG.md#1500)\n\n[Compare Source](https://togithub.com/stylelint/stylelint/compare/14.16.1...15.0.0)\n\n[Migrating to `15.0.0` guide](docs/migration-guide/to-15.md).\n\n-   Removed: Node.js 12 support ([#6477](https://togithub.com/stylelint/stylelint/pull/6477)) ([@ybiquitous](https://togithub.com/ybiquitous)). (BREAKING)\n-   Removed: support for processors ([#6479](https://togithub.com/stylelint/stylelint/pull/6479)) ([@ybiquitous](https://togithub.com/ybiquitous)). (BREAKING)\n-   Removed: `syntax` option ([#6420](https://togithub.com/stylelint/stylelint/pull/6420)) ([@fpetrakov](https://togithub.com/fpetrakov)). (BREAKING)\n-   Changed: `extends` in `overrides` to merge to be consistent with `plugins` behaviour ([#6380](https://togithub.com/stylelint/stylelint/pull/6380)) ([@jasikpark](https://togithub.com/jasikpark)). (BREAKING)\n-   Changed: type definitions to reorganize ([#6510](https://togithub.com/stylelint/stylelint/pull/6510)) ([@ybiquitous](https://togithub.com/ybiquitous)). (BREAKING)\n-   Changed: type names to be more consistent ([#6503](https://togithub.com/stylelint/stylelint/pull/6503)) ([@ybiquitous](https://togithub.com/ybiquitous)). (BREAKING)\n-   Deprecated: stylistic rules handled by Prettier ([#6504](https://togithub.com/stylelint/stylelint/pull/6504)) ([@ybiquitous](https://togithub.com/ybiquitous)).\n-   Added: `declaration-property-value-no-unknown` rule ([#6511](https://togithub.com/stylelint/stylelint/pull/6511)) ([@jeddy3](https://togithub.com/jeddy3)).\n-   Added: `media-feature-name-unit-allowed-list` rule ([#6550](https://togithub.com/stylelint/stylelint/pull/6550)) ([@mattxwang](https://togithub.com/mattxwang)).\n-   Added: `function-url-quotes` autofix ([#6558](https://togithub.com/stylelint/stylelint/pull/6558)) ([@mattxwang](https://togithub.com/mattxwang)).\n-   Added: `ignore: [\"custom-elements\"]` to `selector-max-type` ([#6588](https://togithub.com/stylelint/stylelint/pull/6588)) ([@muddv](https://togithub.com/muddv)).\n-   Added: `ignoreFunctions: []` to `unit-disallowed-list` ([#6592](https://togithub.com/stylelint/stylelint/pull/6592)) ([@mattxwang](https://togithub.com/mattxwang)).\n-   Added: deprecated rule warnings ([#6561](https://togithub.com/stylelint/stylelint/pull/6561)) ([@ybiquitous](https://togithub.com/ybiquitous)).\n-   Added: message arguments to `declaration-property-unit-allowed-list` ([#6570](https://togithub.com/stylelint/stylelint/pull/6570)) ([@mattxwang](https://togithub.com/mattxwang)).\n-   Fixed: `overrides.files` in config to allow basename glob patterns ([#6547](https://togithub.com/stylelint/stylelint/pull/6547)) ([@ybiquitous](https://togithub.com/ybiquitous)).\n-   Fixed: `at-rule-no-unknown` false positives for `@scroll-timeline` ([#6554](https://togithub.com/stylelint/stylelint/pull/6554)) ([@mattxwang](https://togithub.com/mattxwang)).\n-   Fixed: `function-no-unknown` false positives for interpolation and backticks in CSS-in-JS ([#6565](https://togithub.com/stylelint/stylelint/pull/6565)) ([@hudochenkov](https://togithub.com/hudochenkov)).\n-   Fixed: `keyframe-selector-notation` false positives for named timeline ranges ([#6605](https://togithub.com/stylelint/stylelint/pull/6605)) ([@kimulaco](https://togithub.com/kimulaco)).\n-   Fixed: `property-no-unknown` false negatives for newer custom syntaxes ([#6553](https://togithub.com/stylelint/stylelint/pull/6553)) ([@43081j](https://togithub.com/43081j)).\n-   Fixed: `selector-attribute-quotes` false positives for \"never\" ([#6571](https://togithub.com/stylelint/stylelint/pull/6571)) ([@mattxwang](https://togithub.com/mattxwang)).\n-   Fixed: `selector-not-notation` autofix for \"simple\" option ([#6608](https://togithub.com/stylelint/stylelint/pull/6608)) ([@Mouvedia](https://togithub.com/Mouvedia)).\n\n\n\n\nstylelint/stylelint-config-standard\n\n### [`v30.0.1`](https://togithub.com/stylelint/stylelint-config-standard/blob/HEAD/CHANGELOG.md#3001)\n\n[Compare Source](https://togithub.com/stylelint/stylelint-config-standard/compare/30.0.0...30.0.1)\n\n-   Fixed: updated to [`stylelint-config-recommended@10.0.1`](https://togithub.com/stylelint/stylelint-config-recommended/releases/tag/10.0.1)\n\n### [`v30.0.0`](https://togithub.com/stylelint/stylelint-config-standard/blob/HEAD/CHANGELOG.md#3000)\n\n[Compare Source](https://togithub.com/stylelint/stylelint-config-standard/compare/29.0.0...30.0.0)\n\n-   Removed: `stylelint` less than `15.0.0` from peer dependencies.\n-   Removed: 64 rules deprecated in [`stylelint@15.0.0`](https://togithub.com/stylelint/stylelint/releases/tag/15.0.0). For details, see the [migration guide](https://togithub.com/stylelint/stylelint/blob/15.0.0/docs/migration-guide/to-15.md).\n-   Fixed: `length-zero-no-unit` to ignore custom properties.\n-   Fixed: `value-no-vendor-prefix` to ignore `-webkit-inline-box`.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Immortal: This PR will be recreated if closed unmerged. Get config help if that's undesired.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Enable model creation in prod", "number": 4793, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4793"}
{"title": "Introduce ScmPullRequestReview", "number": 4794, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4794"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4794#pullrequestreview-1292238882", "body": ""}
{"title": "Refactor some code", "number": 4795, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4795"}
{"title": "Update onboarding assets", "number": 4796, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4796", "body": "Remove unused videos (and references)\nReplace current videos with smaller assets\nFix layout issue"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4796#pullrequestreview-1292233848", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4796#pullrequestreview-1292234648", "body": ""}
{"title": "Add searchV2 to vscode sidebar", "number": 4797, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797", "body": "\n\nFixes bug in the Explorer Insights panel selected state persisting on context click\nDon't allow users to 'delete' Slack thread insights (this never did anything, just remove the option) \nRefactor InsightStreamStore and helper fns\nRemoved the 'show unreads only' menu from the navigation bar (doesn't make as much sense in a search interface)\nMake thread ordering consistent (i.e. code  unblocked)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#pullrequestreview-1296655623", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#pullrequestreview-1296661432", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#pullrequestreview-1296661895", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#pullrequestreview-1296662969", "body": ""}
{"comment": {"body": "Is it safe to assume that the thread is always opened in the first editor group?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#discussion_r1105164926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#pullrequestreview-1296667998", "body": "FYI a bunch of this sidebar logic will be moving to shared."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#pullrequestreview-1300237156", "body": ""}
{"comment": {"body": "No but imo it's safe to assume there will always be at least one editor group open; this ensures consistency re: where and how the threads are opened. \r\n\r\nI don't feel super strongly about this so I can remove this bit if necessary but what we have now is also not great; i.e. if you're focused in the second editor group, opening the thread will open a *new* group and can continue chaining", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#discussion_r1107644412"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#pullrequestreview-1300307285", "body": ""}
{"comment": {"body": "We can get this in as is. Need to see the behaviour first person to really understand what's going on...\r\n\r\nI think we could be smarter about this as we are aware of how many editors are open and which ones are active.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#discussion_r1107690350"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4797#pullrequestreview-1300307322", "body": ""}
{"title": "Introduce ScmWeb abstraction to eliminate API-service dependency on SCM config loading", "number": 4798, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4798", "body": "API service doesn't really depend on SCM config, it just depends on SCM web URLs parts\nand must not consume any secrets. Introduces ScmWeb, ScmWebConfig, ScmNoAuth, and\nfriends.\nThis un-fucks the API-service initialization."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4798#pullrequestreview-1292264447", "body": ""}
{"title": "chore(deps): update dependency nicklockwood/swiftformat to from: \"0.50.8\"", "number": 4799, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4799", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| nicklockwood/SwiftFormat | minor | from: \"0.49.0\" -> from: \"0.50.8\" |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnicklockwood/SwiftFormat\n\n### [`v0.50.8`]()\n\n[Compare Source]()\n\n-   The `redundantBackticks` rule no longer removes required comments around `self`\n-   Associated type headerdoc comments are now handles correctly by the `docComments` rule\n-   Fixed mangled comments when using the `sortedSwitchCases` rule\n-   Hex, octal or binary literals are now sorted correctly in `sortedSwitchCases` rule\n-   Fixed regression in closed brace indentation (introduced in 0.50.7)\n-   Fixed unsafe semicolon removal after inferred `var` properties\n-   Added fileHeader placeholder documentation\n\n### [`v0.50.7`]()\n\n[Compare Source]()\n\n-   Fixed parsing of regex literals preceded by `try` or `await`\n-   Fixed required parens being removed around `await` keyword\n-   Fixed indent for nested, wrapped parameters\n\n### [`v0.50.6`]()\n\n[Compare Source]()\n\n-   Fixed regression in `fileHeader` rule where blank lines were removed after header\n-   Fixed globs matching when command-line tool is invoked from a directory such as `/var/tmp`\n-   Fixed bug in parsing regex literals beginning with `^` character\n\n### [`v0.50.5`]()\n\n[Compare Source]()\n\n-   Fixed incorrect macOS command line binary that accidentally shipped with 0.50.4\n\n### [`v0.50.4`]()\n\n[Compare Source]()\n\n-   Added Swift package command plugin\n-   Added `docComments` rule to convert between regular and documentation comments\n-   Fixed `redundantLet` rule incorrectly stripping `let` inside Result Builders\n-   Fixed `void` rule in cases where `Void` has been locally shadowed\n-   Fixed `fileHeader` rule when file only contains header comment\n-   Fixed unexpected indent and spurious `wrap` warning for blank lines\n-   Fixed parsing bug in `redundantSelf` rule\n\n### [`v0.50.3`]()\n\n[Compare Source]()\n\n-   Fixed bug where `redundantFileprivate` rule could break Array extensions using type sugar\n-   Fixed bug and crash in `wrapSingleLineComments` rule relating to long URLs\n-   Improved `wrapSingleLineComments` handling of comments containing long URLs\n-   The `opaqueGenericParameters` rule is now correctly applied to initializers and subscripts\n-   Added some known issues for `opaqueGenericParameters` and `genericExtensions` to README\n\n### [`v0.50.2`]()\n\n[Compare Source]()\n\n-   Fixed `redundantImports` dropping `@_implementationOnly` or `@_exported` annotations\n-   Fixed `blankLineAfterImports` bug affecting `@_implementationOnly` or `@_exported` imports\n-   Fixed case where regex literals were incorrectly interpreted as division operators\n-   Fixed bug with `genericExtensions` and nested generics\n-   Fixed crash in `opaqueGenericParameters` rule\n\n### [`v0.50.1`]()\n\n[Compare Source]()\n\n-   Fixed bug in `opaqueGenericParameters` where type constraint depended on another type parameter\n-   Fixed crash in `opaqueGenericParameters` rule where type constraint contained closure type\n-   Fixed bug where `opaqueGenericParameters` broke variadic parameter expressions\n-   Fixed several bugs in `wrapSingleLineComments` rule\n-   Fixed crash in `andOperator` rule\n\n### [`v0.50.0`]()\n\n[Compare Source]()\n\n-   Added `genericExtensions` rule for simplifying conditional type extensions in Swift 5.7\n-   Added `markTypes` support for type definitions in extensions\n-   Added `opaqueGenericParameters` rule  to use opaque generic parameter syntax where equivalent\n-   Added `blankLineAfterImports` rule\n-   Added `redundantOptionalBinding` rule for simplifying `if let` expressions in Swift 5.7\n-   Added `--enumnamespaces structs-only` option\n-   Added `wrapSingleLineComments` rule\n-   A `--swiftversion` in the `.swiftformat` config now takes precedence over `.swift-version` file\n-   Multiline string interpolations can now wrap inside parenthesized expression\n-   Comma-delimited options in descendent `.swiftformat` config files are no longer merged\n-   SwiftFormat now requires a minimum of Swift 5.1 to build\n\n### [`v0.49.18`]()\n\n[Compare Source]()\n\n-   Fixed bug in `unusedArguments` when argument is shadowed in a `switch` case statement\n-   Fixed `enumNamespaces` rule breaking `open` class declarations\n-   Fixed `redundantLet` removing `let` incorrectly in `async let` statement\n-   Fixed indent regression when using `--xcodeindentation` option\n\n### [`v0.49.17`]()\n\n[Compare Source]()\n\n-   Fixed unexpected token error occurring at end of scope after a `<<` operator\n-   Fixed bug where function arguments named `async:` would expectedly be indented\n-   SwiftFormat command-line tool now logs the location and version of .swift-version files it encounters\n-   Added Docker image (thanks to Arthur Semenyutin for the script, see README for details)\n\n### [`v0.49.16`]()\n\n[Compare Source]()\n\n-   Fixed `async let` indenting regression (broken in 0.49.15)\n\n### [`v0.49.15`]()\n\n[Compare Source]()\n\n-   Fixed illegal wrapping of ternary expressions inside single-line string interpolation\n-   Fixed bug where `await case` was incorrectly interpreted as ending the current scope\n-   Fixed issue where `async throws` was indented incorrectly\n-   Fixed bug where a pair of less-than, greater-than operators could be interpreted as generics\n-   Fixed case where `andOperator` rule could introduce parser ambiguity\n\n### [`v0.49.14`]()\n\n[Compare Source]()\n\n-   Fixed `unusedArguments` rule incorrectly removing `async` keyword from closure arguments\n-   Fixed `unusedArguments` not being applied correctly to throwing closures\n-   Fixed assertion failure when parsing `@unchecked Sendable` enum\n-   Fixed assertion failure after applying typeSugar rule to array/dictionary extensions\n-   Fixed line indent after `wrapAttributes` rule is applied\n-   Fixed issue where redundantClosure would break build for Void closures calling [@discardableResult]() functions\n-   Added `--typeblankline` option for `blankLinesAtStartOfScope` and `blankLinesAtEndOfScope` rules\n-   Added support for Xcode `SCRIPT_INPUT_FILE` arguments\n\n### [`v0.49.13`]()\n\n[Compare Source]()\n\n-   Fixed `for...in` mistaken for closure `in` in indent rule\n-   Fixed incorrect spacing around `@MainActor`\n\n### [`v0.49.12`]()\n\n[Compare Source]()\n\n-   Fixed bug with parsing ternary chains containing chevron\n-   Added another fix for `/` operator\n-   Fixed indent after wrapped closure `in`\n-   Improved rule search in SwiftFormat for Xcode app\n-   Fixed enum popups in SwiftFormat for Xcode options\n-   Added prebuilt SPM binary target\n\n### [`v0.49.11`]()\n\n[Compare Source]()\n\n-   Fixed parsing of prefix `/` operator (as used in CasePath library)\n-   Fixed bug with indenting of trailing closures after a conditional statement\n-   Fixed bug with `wrapMultilineStatementBraces` rule\n-   Added Swift 5.6 and 5.7 to supported versions\n\n### [`v0.49.10`]()\n\n[Compare Source]()\n\n-   Added preliminary support for Swift 5.7 regular expression literals\n-   Fixed conflict between `wrapMultilineStatementBraces` and `indent` rules\n-   Fixed bug where arguments referenced using `$` prefix were incorrectly marked as unused\n-   Fixed `enumNamespaces` bug where `class` modifiers were mistakenly converted to `enum`\n-   Fixed bug where `preferKeyPath` mangled functions using multiple trailing closure syntax\n-   Unterminated string literals are now treated as an error\n\n### [`v0.49.9`]()\n\n[Compare Source]()\n\n-   Fixed bug where trailing comma was incorrectly added inside collection types\n-   Fixed some cases where `redundantVoidReturnType` failed to remove `Void`\n-   Fixed `unusedArguments` regression introduced in 0.49.8\n\n### [`v0.49.8`]()\n\n[Compare Source]()\n\n-   Fixed `redundantInit` rule removing required init when instantiating type variables\n-   Fixed `unusedArguments` incorrectly marking shadowed parameters as used or unused in some cases\n\n### [`v0.49.7`]()\n\n[Compare Source]()\n\n-   Redundant `self` is now correctly removed in `if let` assignments\n-   Fixed infinite recursion bug that would cause formatting to time out for some inputs\n-   Lint failure now returns an error code when using stdin, matching behavior for file inputs\n-   Fixed bug where parens were incorrectly removed around optional ranges\n-   Updated `unusedParens` rule for new shorthand `if let` syntax in Swift 5.7\n-   Updated indenting of function chains to match latest Xcode behavior\n-   Fixed build error on macOS 10.11 and earlier\n\n### [`v0.49.6`]()\n\n[Compare Source]()\n\n-   Fixed bug where `redundantParens` rule removed required parens in `any` type expressions\n-   Fixed whitespace behavior around `some`/`any` keywords\n-   Fixed crash when `// swiftformat:sort` was applied to an enum with only one case\n-   SwiftFormat can now be built on Windows\n\n### [`v0.49.5`]()\n\n[Compare Source]()\n\n-   Fixed bug where `redundantClosure` incorrectly inlined throwing closures\n-   Fixed bug where in `--exclude` path matching failed when using `--stdinpath`\n-   Fixed a bug with `typeSugar` rule when overriding stdlib types locally in your code\n-   Multiline statement braces are now unwrapped if `wrapMultilineStatementBraces` disabled\n-   Added `// swiftformat:sort` directive to sort declarations by name\n-   You can now use `--disable all` or `--enable all` as shorthand for all rules\n-   The rules in the `Rules.md` file are now grouped by their default/enabled status\n\n### [`v0.49.4`]()\n\n[Compare Source]()\n\n-   Fixed creation date being modified on formatted files\n-   Fixed case where a closure inside an if condition was mistaken for the body\n-   Fixed `blockComments` rule removing leading `*`s used as bullet points\n-   Fixed bug when parsing a raw string containing three consecutive unescaped quotes\n-   Fixed spurious warning about unused `--wrapparameters` option\n-   Fixed edge case when using `--allman` indenting\n\n### [`v0.49.3`]()\n\n[Compare Source]()\n\n-   Fixed required `let` being removed inside View Builders\n-   Fixed `blockComments` rule mangling code on next line after comment (really this time)\n-   Fixed unsafe removal of `self` inside `if` statements containing postfix operators\n-   Fixed `--selfrequired` behavior inside interpolated strings\n-   Fixed indenting of labelled trailing closures\n\n### [`v0.49.2`]()\n\n[Compare Source]()\n\n-   Fixed literal values being incorrectly removed by `redundantType` rule\n-   Fixed `redundantSelf` rule removing `self` from shadowed variables after an `as`/`is` condition\n-   Fixed bug where `redundantClosure` rule could break the build for certain `Void` closures\n-   Fixed parsing error in function calls followed by a subscript\n-   Fixed `blockComments` rule mangling code on next line after comment\n-   The `redundantClosure` rule is no longer applied if closure calls a method that returns `Never`\n-   Fixed meaningless warning for deprecated options\n\n### [`v0.49.1`]()\n\n[Compare Source]()\n\n-   Fixed bug in `unusedArguments` when argument is shadowed in a `switch` case statement\n-   Fixed `enumNamespaces` rule breaking `open` class declarations\n-   Fixed `redundantLet` removing `let` incorrectly in `async let` statement\n-   Fixed indent regression when using `--xcodeindentation` option\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Setup API client for Web", "number": 48, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48", "body": "Setup basic API client interface for web.\nGenerated API code not checked in.\nRunning start or build will automatically try generating code. Currently depending on gradle caching capabilities.\nStill need to setup client configs (differentiate dev/prod) that will provide base urls, keys, headers, etc...\nAlso potentially custom templates."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48#pullrequestreview-854829994", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48#pullrequestreview-854841399", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48#pullrequestreview-854841956", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48#pullrequestreview-854843777", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48#pullrequestreview-854843922", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48#pullrequestreview-854844748", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48#pullrequestreview-854844879", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48#pullrequestreview-854845196", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/48#pullrequestreview-855721584", "body": ""}
{"title": "Point local publicpath to absolute", "number": 480, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/480", "body": "We updated publicPath to relative for deployment purposes https://github.com/NextChapterSoftware/unblocked/pull/477\nThis broke local dev (somewhat expected) Here's the fix."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/480#pullrequestreview-*********", "body": ""}
{"comment": {"body": "For dev and prod this is going under `/dashboard`, I think we might need to:\r\n* Update our webpack configs for those environments (ugh)\r\n* Update our routing to handle this correctly?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/480#discussion_r820003152"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/480#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/480#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Yeah. I'm not 100% sure if the deployed service is working. We'll need to test it out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/480#discussion_r820005119"}}
{"title": "GitLab Cloud Sign-in", "number": 4800, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4800", "body": "\nGitLab OAuth applications\n"}
{"title": "Flag on new onboarding", "number": 4801, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4801", "body": "Remove feature flag for new onboarding"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4801#pullrequestreview-********60", "body": ""}
{"title": "Deprecate old onboarding feature flag", "number": 4802, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4802", "body": "And add new flag for @matthewjamesadam"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4802#pullrequestreview-1293833518", "body": ""}
{"title": "Add logger to streams", "number": 4803, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803", "body": "Add stream operator that logs events of stream as things change.\nCan be manually added to any stream with compose or as part of any ValueStream / ValueCacheStream."}
{"comment": {"body": "<img width=\"865\" alt=\"CleanShot 2023-02-10 at 12 08 29@2x\" src=\"https://user-images.githubusercontent.com/1553313/218187884-308c7642-b7e9-484e-98c3-443399070caf.png\">\r\n<img width=\"853\" alt=\"CleanShot 2023-02-10 at 12 08 20@2x\" src=\"https://user-images.githubusercontent.com/1553313/218187893-8107b5f2-cf0d-4819-8acc-c54b3404f949.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803#issuecomment-1426283603"}}
{"comment": {"body": "Would you mind adding this for the SourceMarkStore; rationale is that there was a failure last week where everything was working in the extension except for SourceMarks because the stream never completed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803#issuecomment-1426294603"}}
{"comment": {"body": "> Would you mind adding this for the SourceMarkStore; rationale is that there was a failure last week where everything was working in the extension except for SourceMarks because the stream never completed.\r\n\r\nAdded SM Store to log out # of Marks\r\n<img width=\"840\" alt=\"CleanShot 2023-02-10 at 13 10 08@2x\" src=\"https://user-images.githubusercontent.com/1553313/218198143-cb43e83f-ab48-4c14-9298-e6e78738d8fd.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803#issuecomment-1426340306"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803#pullrequestreview-1293922805", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803#pullrequestreview-1293938487", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803#pullrequestreview-1294145705", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803#pullrequestreview-1294146248", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803#pullrequestreview-1294146829", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4803#pullrequestreview-1294163889", "body": ""}
{"title": "Fix build issues due to lint", "number": 4804, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4804"}
{"title": "chore(deps): update plugin org.jetbrains.intellij to v1.13.0", "number": 4805, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4805", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.jetbrains.intellij | 1.12.0 -> 1.13.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update opentelemetryversion to v1.23.0", "number": 4806, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4806", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.opentelemetry:opentelemetry-sdk | 1.22.0 -> 1.23.0 |  |  |  |  |\n| io.opentelemetry:opentelemetry-extension-kotlin | 1.22.0 -> 1.23.0 |  |  |  |  |\n| io.opentelemetry:opentelemetry-exporter-otlp | 1.22.0 -> 1.23.0 |  |  |  |  |\n| io.opentelemetry:opentelemetry-exporter-logging | 1.22.0 -> 1.23.0 |  |  |  |  |\n| io.opentelemetry:opentelemetry-sdk-testing | 1.22.0 -> 1.23.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nopen-telemetry/opentelemetry-java\n\n### [`v1.23.0`](https://togithub.com/open-telemetry/opentelemetry-java/blob/HEAD/CHANGELOG.md#Version-1230-2023-02-10)\n\nThis release is a notable release for metrics:\n\n-   The base2 exponential histogram aggregation has been marked as stable. To use, configure\n    your `MetricExporter` with a default aggregation\n    of `Aggregation.base2ExponentialBucketHistogram()` for histogram instruments. If using OTLP\n    exporter with autoconfigure,\n    set `OTEL_EXPORTER_OTLP_METRICS_DEFAULT_HISTOGRAM_AGGREGATION=BASE2_EXPONENTIAL_BUCKET_HISTOGRAM`.\n    If using configuring OTLP programatically,\n    use `Otlp*MetricExporterBuilder#setDefaultAggregationSelector(DefaultAggregationSelector)`.\n-   The metrics SDK undergone significant internal refactoring which results in reduced complexity and\n    significantly reduced memory allocation during metric collection / export.\n\n##### API\n\n##### Logs\n\n-   WARNING: Split out Event API from Log API. Event API is now published in `opentelemetry-api-events`.\n    API / SDK usage has also changed - see PR for details.\n    ([#5049](https://togithub.com/open-telemetry/opentelemetry-java/pull/5049))\n\n##### SDK\n\n-   Add shutdown / close to `OpenTelemetrySdk`\n    ([#5100](https://togithub.com/open-telemetry/opentelemetry-java/pull/5100))\n\n##### Metrics\n\n-   Base2 exponential histogram aggregations are now stable. Add `base2ExponentialBucketHistogram()`\n    to `Aggregation`.\n    ([#5143](https://togithub.com/open-telemetry/opentelemetry-java/pull/5143))\n-   Promote exponential histogram data interfaces to stable package\n    ([#5120](https://togithub.com/open-telemetry/opentelemetry-java/pull/5120))\n-   Add Base2 prefix to internal exponential histogram classes\n    ([#5179](https://togithub.com/open-telemetry/opentelemetry-java/pull/5179))\n-   Add MaxScale config parameter to `Base2ExponentialBucketHistogram`\n    ([#5044](https://togithub.com/open-telemetry/opentelemetry-java/pull/5044))\n-   Add close method to `MetricReader`\n    ([#5109](https://togithub.com/open-telemetry/opentelemetry-java/pull/5109))\n-   Reuse `AggregatorHandle` with cumulative temporality to reduce allocations\n    ([#5142](https://togithub.com/open-telemetry/opentelemetry-java/pull/5142))\n-   Delete notion of accumulation to reduce allocations\n    ([#5154](https://togithub.com/open-telemetry/opentelemetry-java/pull/5154))\n-   Delete bound instruments\n    ([#5157](https://togithub.com/open-telemetry/opentelemetry-java/pull/5157))\n-   Reuse aggregation handles for delta temporality to reduce allocations\n    ([#5176](https://togithub.com/open-telemetry/opentelemetry-java/pull/5176))\n\n##### Exporter\n\n-   Add proper shutdown implementations for all exporters\n    ([#5113](https://togithub.com/open-telemetry/opentelemetry-java/pull/5113))\n\n##### SDK Extensions\n\n-   WARNING: Remove deprecated autoconfigure exemplar filter names. Previous names `none`, `all`\n    , `with_sampled_trace` have been removed. Use `always_off`, `always_on`, `trace_based` instead.\n    ([#5098](https://togithub.com/open-telemetry/opentelemetry-java/pull/5098))\n-   Add autoconfigure support for \"none\" option for propagator value\n    ([#5121](https://togithub.com/open-telemetry/opentelemetry-java/pull/5121))\n-   Add autoconfigure support for `parentbased_jaeger_remote` sampler\n    ([#5123](https://togithub.com/open-telemetry/opentelemetry-java/pull/5123))\n-   Autoconfigure closes up autoconfigured resources in case of exception\n    ([#5117](https://togithub.com/open-telemetry/opentelemetry-java/pull/5117))\n-   View file based config has switched from snakeyaml to snakeyaml-engine for YAML parsing.\n    ([#5138](https://togithub.com/open-telemetry/opentelemetry-java/pull/5138))\n-   Fix autoconfigure resource providers property docs\n    ([#5135](https://togithub.com/open-telemetry/opentelemetry-java/pull/5135))\n\n##### Testing\n\n-   WARNING: Merge `opentelemetry-sdk-metrics-testing` into `opentelemetry-sdk-testing`. Stop\n    publishing `opentelemetry-sdk-metrics-testing`.\n    ([#5144](https://togithub.com/open-telemetry/opentelemetry-java/pull/5144))\n-   Sort spans by start time (parents before children as tiebreaker) to avoid common causes for flaky\n    tests\n    ([#5026](https://togithub.com/open-telemetry/opentelemetry-java/pull/5026))\n-   Add resource assertion methods to `SpanDataAssert` and `MetricAssert`\n    ([#5160](https://togithub.com/open-telemetry/opentelemetry-java/pull/5160))\n\n##### Semantic Conventions\n\n-   Update semconv to 1.18.0\n    ([#5188](https://togithub.com/open-telemetry/opentelemetry-java/pull/5188))\n    ([#5134](https://togithub.com/open-telemetry/opentelemetry-java/pull/5134))\n\n##### OpenTracing Shim\n\n-   Refactor to remove internal objects `BaseShimObject` and `TelemetryInfo`\n    ([#5087](https://togithub.com/open-telemetry/opentelemetry-java/pull/5087))\n-   WARNING: Minimize public surface area of OpenTracingShim. Remove `createTracerShim()`\n    , `createTracerShim(Tracer)`, `createTracerShim(Tracer, OpenTracingPropagators)`.\n    Add `createTracerShim(TracerProvder,TextMapPropagator,TextMapPropagator)`.\n    ([#5110](https://togithub.com/open-telemetry/opentelemetry-java/pull/5110))\n\n##### Project tooling\n\n-   Add OWASP dependency check\n    ([#5177](https://togithub.com/open-telemetry/opentelemetry-java/pull/5177))\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Respect client debug flag in VSCode", "number": 4807, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4807", "body": "The intended behaviour is that in regular mode, we log warnings and errors only into console, file, and logz.io, but when the client debug flag is enabled, we log all flags to all of these targets, with higher throttling amounts.\nSome practical results of this:\n* We can't use log.debug(blah) to do temporary debugging -- but that's probably fine\n* We can feel free to be much more verbose with debug/info logs, as by default nobody will see them and they won't negatively impact system performance\nThis code is a bit messy -- this is a pretty quick effort to get this in today, we can improve this later."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4807#pullrequestreview-1294061363", "body": ""}
{"comment": {"body": "Fetch new client configs every 10 minutes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4807#discussion_r1103274383"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4807#pullrequestreview-1294062422", "body": ""}
{"comment": {"body": "This allow changing throttle amounts dynamically, so we can swap throttle amounts whenever the client flag changes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4807#discussion_r1103275002"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4807#pullrequestreview-1294137285", "body": ""}
{"title": "Fix onboarding notification step behaviour", "number": 4808, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4808", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4808#pullrequestreview-1294065645", "body": "Awesome!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4808#pullrequestreview-1294112110", "body": ""}
{"comment": {"body": "Shouldn't we stop the monitor when authStatus === authorized?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4808#discussion_r1103296443"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4808#pullrequestreview-1294122143", "body": ""}
{"comment": {"body": "I think that's the intent behind this logic. Basically wait until the authorization status becomes `.authorized`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4808#discussion_r1103300054"}}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.20.2", "number": 4809, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4809", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.20.1 -> 2.20.2 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.20.1 -> 2.20.2 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.20.1 -> 2.20.2 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.20.1 -> 2.20.2 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.20.1 -> 2.20.2 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime (source) | 2.20.1 -> 2.20.2 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.20.1 -> 2.20.2 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.20.1 -> 2.20.2 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.20.1 -> 2.20.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.20.2`]()\n\n[Compare Source]()\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon Connect Service**\n\n-   ### Features\n    -   This update provides the Wisdom session ARN for contacts enabled for Wisdom in the chat channel.\n\n#### **Amazon Elastic Compute Cloud**\n\n-   ### Features\n    -   Adds support for waiters that automatically poll for an imported snapshot until it reaches the completed state.\n\n#### **Amazon Polly**\n\n-   ### Features\n    -   Amazon Polly adds two new neural Japanese voices - Kazuha, Tomoko\n\n#### **Amazon SageMaker Service**\n\n-   ### Features\n    -   Amazon SageMaker Autopilot adds support for selecting algorithms in CreateAutoMLJob API.\n\n#### **Amazon Simple Notification Service**\n\n-   ### Features\n    -   This release adds support for SNS X-Ray active tracing as well as other updates.\n\n#### **Auto Scaling**\n\n-   ### Features\n    -   You can now either terminate/replace, ignore, or wait for EC2 Auto Scaling instances on standby or protected from scale in. Also, you can also roll back changes from a failed instance refresh.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Add teamID to thread model", "number": 481, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/481", "body": "Add teamID to thread API model."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/481#pullrequestreview-900841113", "body": ""}
{"title": "Show all VSCode prod debug ports", "number": 4810, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4810", "body": "There are scenarios where there are multiple possible debug ports, this will display all of them."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4810#pullrequestreview-1294252321", "body": ""}
{"title": "Add BitBucketPullRequest and BitBucketUser models", "number": 4811, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4811"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4811#pullrequestreview-1294320066", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4811#pullrequestreview-1294320384", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4811#pullrequestreview-1294328425", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4811#pullrequestreview-1294739964", "body": ""}
{"title": "Lower severity of Sourcemark Snippet issue", "number": 4812, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4812", "body": "Not really an error"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4812#pullrequestreview-1294296974", "body": ""}
{"title": "Add 'isInsider' field to monitoring for excluding internal traffic", "number": 4813, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4813", "body": "Add to MDC logging context, API status pages, and tracing (Honeycomb).\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4813#pullrequestreview-1294280552", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4813#pullrequestreview-1294283833", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4813#pullrequestreview-1294292174", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4813#pullrequestreview-1294384978", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4813#pullrequestreview-1294385085", "body": ""}
{"title": "Add initial value to webviewEventHandler stream", "number": 4814, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4814"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4814#pullrequestreview-1294306023", "body": ""}
{"title": "Jetbrains CI runs lint", "number": 4815, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4815"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4815#pullrequestreview-1294341675", "body": ""}
{"title": "Bump cacheable-request and got in /infrastructure/cdk/core", "number": 4816, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4816", "body": "Bumps cacheable-request and got. These dependencies needed to be updated together.\nUpdates cacheable-request from 7.0.2 to 10.2.7\n\nRelease notes\nSourced from cacheable-request's releases.\n\nv10.2.6\nFix for memory leak on Listeners\nThe listener was not being removed on response and just error but new handlers were being added causing a memory leak.\nline 220 in src/index.ts was modified to remove the listener on response also \n         if (this.cache instanceof Keyv) {\n                const cachek = this.cache;\n                cachek.once('error', errorHandler);\n                ee.on('error', () = cachek.removeListener('error', errorHandler));\n                ee.on('response', () = cachek.removeListener('error', errorHandler));\n            }\n\nWhat's Changed\n\nupgrading jest and components to latest by @jaredwray in jaredwray/cacheable-request#220\nupgrading @types/jest to 29.2.6 by @jaredwray in jaredwray/cacheable-request#221\nfixing listener memory leak - issue #222 by @jaredwray in jaredwray/cacheable-request#223\n\nFull Changelog: \nv10.2.5\nTypes definition issue with http-cache-sematics as that type definition needs to be in dependencies. Thanks @Maxim-Mazurok\nWhat's Changed\n\nMove @types/http-cache-semantics from dev to deps by @Maxim-Mazurok in jaredwray/cacheable-request#219\n\nFull Changelog: \nv10.2.4\nMinor updates with one exception is that we removed @types/http-cache-semantics from the main dependencies as it does not look to be needed.\nWhat's Changed\n\nupgrading typescript to 4.9.4 by @jaredwray in jaredwray/cacheable-request#214\nupgrading jest types and eslint for jest to latest by @jaredwray in jaredwray/cacheable-request#215\nupgrading sqlite3 to 5.1.4 by @jaredwray in jaredwray/cacheable-request#216\nremoving @types/http-cache-semantics from the dependencies and moving by @jaredwray in jaredwray/cacheable-request#217\n\nFull Changelog: \nv10.2.3 Maintenance Release\nUpgrading core modules in the system such as keyv and also a minor fix to an uncaught exception that we were seeing referenced here: sindresorhus/got#1925\nAdditional update is moving normalize-url to 8.0.0 which after testing it looks to not affect anything but will post the release notes here: \n\n\n... (truncated)\n\n\nCommits\n\nSee full diff in compare view\n\n\n\nMaintainer changes\nThis version was pushed to npm by jaredwray, a new releaser for cacheable-request since your current version.\n\n\nUpdates got from 12.4.1 to 12.5.3\n\nRelease notes\nSourced from got's releases.\n\nv12.5.3\n\nFix abort event listeners not always being cleaned up (#2162)  3cc40b5\n\n\nv12.5.2\n\nImprove TypeScript 4.9 compatibility (#2163)  39f83b6\n\n\nv12.5.1\n\nFix compatibility with TypeScript and ESM  3b3ea67\nFix request body not being properly cached (#2150)  3e9d3af\n\n\nv12.5.0\n\nDisable method rewriting on 307 and 308 status codes (#2145)  e049e94\nUpgrade dependencies  8630815 f0ac0b3 4c3762a\n\n\n\n\n\nCommits\n\n2a4b4e7 12.5.3\n3cc40b5 Fix abort event listeners not always being cleaned up (#2162)\n5f278d7 12.5.2\n39f83b6 Improve TypeScript 4.9 compatibility (#2163)\n623229f Simplify CookieJar docs (#2155)\n07afa3c Make GitHub Actions workflow more secure (#2154)\na4482a5 12.5.1\n3b3ea67 Fix compatibility with TypeScript and ESM\n3e9d3af Fix request body not being properly cached (#2150)\n6c7ebab Minor tweaks for ESM strict mode\nAdditional commits viewable in compare view\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Fix topics tags in vscode", "number": 4817, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4817", "body": "Was trying to use useCapability in shared code, which didn't work for vscode. Have to pass the flag value into each client view\nNOTE: Will need to do the same for jetbrains code if/when we release it and the flag is still active"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4817#pullrequestreview-1296127180", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4817#pullrequestreview-1296127184", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4817#pullrequestreview-1300126314", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4817#pullrequestreview-1300130296", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4817#pullrequestreview-1300131952", "body": ""}
{"title": "EmailFollowup", "number": 4818, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4818", "body": "Email followup\nAdd email followup logic"}
{"title": "Fix template", "number": 4819, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4819"}
{"title": "Linter prevents using DAO/SQL models outside the DB package", "number": 482, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/482", "body": "Expected to fail, because we haven't addressed these issues yet."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/482#pullrequestreview-900891366", "body": "Good stuff!"}
{"title": "Fix identity processing", "number": 4820, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4820"}
{"title": "Update ses template", "number": 4821, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4821"}
{"title": "Reduce service counts in prod.", "number": 4822, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4822"}
{"title": "Resize eks nodes", "number": 4823, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4823", "body": "Dev and Prod have been configured to use c5a.2xlarge nodes with 16GB ram. We are still at the same capacity as before node resize\nUpdate falco rules to exclude some false positives that showed up during deployment\nReduced pod counts in Dev\n\nAll EKS changes have been deployed to both Dev and Prod\n All Falco rule changes have been deployed to both Dev and Prod"}
{"title": "Add default JUnit test timeout", "number": 4824, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4824", "body": "Prevents CI actions from timing out.\nCan use @Timeout annotation to override on individual tests where necessary:\n```kotlin\n@Timeout(60) // timeout in seconds\n@Test\nfun slow test() {  }\n```\nOr annotate the test class to override on every test in the class."}
{"title": "Bitbucket classes are internal", "number": 4825, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4825"}
{"title": "Add default JUnit test timeout", "number": 4826, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4826", "body": "Prevents CI actions from timing out.\nCan use @Timeout annotation to override on individual tests where necessary:\n```kotlin\n@Timeout(60) // timeout in seconds\n@Test\nfun slow test() {  }\n```\nOr annotate the test class to override on every test in the class."}
{"title": "Add 'isInsider' field to monitoring for excluding internal traffic", "number": 4827, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4827", "body": "Add to MDC logging context and tracing (Honeycomb).\n"}
{"title": "Fix GitLab OAuth", "number": 4828, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4828"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4828#pullrequestreview-1294898612", "body": ""}
{"comment": {"body": "TODO: pull from `state`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4828#discussion_r1103963383"}}
{"title": "Update tracing for isInsider across all frontend services", "number": 4829, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4829", "body": "Code duplication :("}
{"title": "SourcePoint data model", "number": 483, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/483", "body": "Basically move DB stuff from SourceMarkService to SourceMarkStore"}
{"title": "Adding CloudTrail alarms for SecOps", "number": 4830, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4830", "body": "Notion Doc with all the info about this work: \n\nAdded config file for standard region (us-east-1) under management account\nNew config file includes a list of CloudWatch alarms required by CIS benchmark 1.4\nModified IAM and DNS stacks to be optional\nAdded a new stack to create CloudWatch metric filters and CloudWatch alarms\nAdded new config data structures to support above changes\n\nChanges have been deployed and tested. We now receive security pages for events defined in this code change using both emails and on-call escalations"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4830#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Does this early return need to be logged (for debugging purposes?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4830#discussion_r1104844479"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4830#pullrequestreview-**********", "body": ""}
{"comment": {"body": "ditto", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4830#discussion_r1104844568"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4830#pullrequestreview-1296185798", "body": "Minor comments"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4830#pullrequestreview-1296198740", "body": ""}
{"comment": {"body": "It does. I'll add it in a separate PR or better I might move it to the main CDK entry point file.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4830#discussion_r1104853685"}}
{"title": "Additional logging for tour", "number": 4831, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4831", "body": "Add additional logs to debug empty files issue in the final step of the tour."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4831#pullrequestreview-1296184723", "body": ""}
{"title": "Minor email improvements", "number": 4832, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4832"}
{"title": "Use AbortController instead of AbortSignal", "number": 4833, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4833"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4833#pullrequestreview-1296272820", "body": ""}
{"title": "Add logging and refactor onboarding return typing", "number": 4834, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4834"}
{"comment": {"body": "Closing this for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4834#issuecomment-1428860306"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4834#pullrequestreview-1296272267", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4834#pullrequestreview-1296274059", "body": "Would hold off on this for now. \nIn situations where we cannot resolve files, I think an error / complete state would be better. With this PR, the spinner will never disappear."}
{"title": "Fix powerml topic mapping across sources", "number": 4835, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4835", "body": "PowerML was just taking the first approved topic.\nThis doesnt really work well when there are multiople source types."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4835#pullrequestreview-1296334988", "body": ""}
{"title": "Second attempt to fix notifications onboarding flow...", "number": 4836, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4836", "body": "Title is maybe a little misleading. This mostly adds some additional logging to help track down what's happening on Ben's machine"}
{"title": "chore(deps): update dependency @types/chrome to ^0.0.213", "number": 4837, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4837", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/chrome (source) | ^0.0.212 -> ^0.0.213 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Do not send Slack announcements for internal teams or users", "number": 4838, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4838", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4838#pullrequestreview-1296374693", "body": ""}
{"comment": {"body": "fyi @pwerry ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4838#discussion_r1104972099"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4838#pullrequestreview-1296381122", "body": ""}
{"comment": {"body": "heh good idea", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4838#discussion_r1104976485"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4838#pullrequestreview-1296381995", "body": ""}
{"title": "Add Bitbucket OAuth secrets", "number": 4839, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4839", "body": "Applications are registered here:\n\nMore info on our workspace:\n"}
{"title": "Remove internal -- doesn't do anthing", "number": 484, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/484"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/484#pullrequestreview-902014817", "body": ""}
{"title": "benedict-bb is an internal user", "number": 4840, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4840"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4840#pullrequestreview-1296441875", "body": ""}
{"title": "Add ability to view topics for a single thread", "number": 4841, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4841", "body": "Added ability to see topics for a given thread and to have them link back to the topic page."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4841#pullrequestreview-1296446780", "body": ""}
{"title": "chore(deps): update plugin org.cyclonedx.bom to v1.7.4", "number": 4842, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4842", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.cyclonedx.bom | 1.7.3 -> 1.7.4 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Fixes system settings launch bug", "number": 4843, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4843", "body": "The async requestAuthorization function will block until the user interacts with the notification permissions toast. Very strange, but we can ignore that behaviour since we always dump the user to system settings."}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.20.3", "number": 4844, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4844", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:sts (source) | 2.20.2 -> 2.20.3 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.20.2 -> 2.20.3 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.20.2 -> 2.20.3 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.20.2 -> 2.20.3 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.20.2 -> 2.20.3 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.20.2 -> 2.20.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.20.3`]()\n\n[Compare Source]()\n\n#### **AWS Account**\n\n-   ### Features\n    -   This release of the Account Management API enables customers to view and manage whether AWS Opt-In Regions are enabled or disabled for their Account. For more information, see \n\n#### **AWS AppConfig Data**\n\n-   ### Features\n    -   AWS AppConfig now offers the option to set a version label on hosted configuration versions. If a labeled hosted configuration version is deployed, its version label is available in the GetLatestConfiguration response.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n-   ### Bugfixes\n    -   Keep precedence of options when passed to ProfileFileSupplier.aggregate\n\n#### **Amazon Import/Export Snowball**\n\n-   ### Features\n    -   Adds support for EKS Anywhere on Snowball. AWS Snow Family customers can now install EKS Anywhere service on Snowball Edge Compute Optimized devices.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Offer to restart VSCode when we detect a dead proxy", "number": 4845, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4845", "body": "Because of this issue:  -- whenever a proxy (VPN, Proxyman, etc) shuts down or changes configuration, any open VSCode extensions start to fail.\nThis could affect customers, but definitely seems to be affecting Dennis, so to reduce noise this PR tries to detect this scenario and displays a UI to restart VSCode.\n"}
