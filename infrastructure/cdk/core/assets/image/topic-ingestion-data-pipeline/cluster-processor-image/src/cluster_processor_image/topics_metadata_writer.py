import dataclasses
import json
import logging
import os

from cluster_processor_image.path_constants import PROCESS_OUTPUT_DIRECTORY, PROCESS_OUTPUT_METADATA_BASE_NAME
from cluster_processor_image.topics_metadata import TopicsMetadata


class TopicsMetadataWriter:
    def writer_topics_metadata(self, topics_metadata: TopicsMetadata):
        pass


class FileTopicsMetadataWriter(TopicsMetadataWriter):
    __output_directory: str
    __output_base_name: str

    def __init__(
        self,
        output_directory: str = PROCESS_OUTPUT_DIRECTORY,
        output_base_name: str = PROCESS_OUTPUT_METADATA_BASE_NAME,
    ):
        self.__output_directory = output_directory
        self.__output_base_name = output_base_name

    def writer_topics_metadata(self, topics_metadata: TopicsMetadata):
        if not os.path.exists(self.__output_directory):
            os.mkdir(self.__output_directory)

        try:
            # Convert the instance to a dictionary
            topics_metadata_dict = dataclasses.asdict(topics_metadata)
            output_file = os.path.join(self.__output_directory, f"{self.__output_base_name}.json")
            with open(output_file, "w") as f:
                json.dump(topics_metadata_dict, f)
        except Exception as e:
            logging.exception(f"Failed to process topics metadata. Exception: {str(e)}")
