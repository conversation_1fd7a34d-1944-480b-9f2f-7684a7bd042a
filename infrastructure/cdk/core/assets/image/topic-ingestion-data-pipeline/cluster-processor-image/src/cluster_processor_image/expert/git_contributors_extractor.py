import logging

import pandas
from pandas import Data<PERSON>rame

from cluster_processor_image.dataframe_constants import CONTRIBUTIONS_COLUMN
from cluster_processor_image.expert.git_commit_utils import GitCommitUtils
from cluster_processor_image.expert.git_dataframe_constants import (
    GIT_AUTHOR_COLUMN,
    GIT_INSERTIONS_COLUMN,
    GIT_DELETIONS_COLUMN,
    GIT_EMAIL_COLUMN,
)


class GitContributorsExtractor:
    def __init__(self, repo_dir: str):
        self.git_utils = GitCommitUtils(repo_dir)

    def get_top_authors_for_files(self, *file_paths: str, top_n: int = 5) -> DataFrame:
        file_commits_df = self.git_utils.get_file_commits(list(file_paths))

        if file_commits_df.empty:
            logging.warning(f"No commits found for the specified files.")
            return []

        # Group by author and sum their insertions and deletions across all files
        author_contributions = (
            file_commits_df.groupby(GIT_EMAIL_COLUMN)[[GIT_INSERTIONS_COLUMN, GIT_DELETIONS_COLUMN]].sum().reset_index()
        )

        # Calculate a total contributions column (sum of insertions and deletions)
        author_contributions[CONTRIBUTIONS_COLUMN] = (
            author_contributions[GIT_INSERTIONS_COLUMN] + author_contributions[GIT_DELETIONS_COLUMN]
        )

        # Sort by contributions in descending order
        sorted_authors = author_contributions.sort_values(by=CONTRIBUTIONS_COLUMN, ascending=False).head(top_n)

        # Get the top n authors
        top_authors = sorted_authors.head(top_n)

        top_authors = pandas.merge(top_authors, file_commits_df, on=GIT_EMAIL_COLUMN, how="inner")[
            [GIT_AUTHOR_COLUMN, GIT_EMAIL_COLUMN, CONTRIBUTIONS_COLUMN]
        ]

        return top_authors
