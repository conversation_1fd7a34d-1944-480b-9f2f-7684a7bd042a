{"title": "timeout cache restore", "number": 5837, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5837"}
{"title": "Keep on playing with these test parameters", "number": 5838, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5838"}
{"title": "More Refactor", "number": 5839, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5839"}
{"title": "Team scope restrictions", "number": 584, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584", "body": "PR walkthrough in files"}
{"comment": {"body": "Nice!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#issuecomment-1068274024"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#pullrequestreview-909607751", "body": ""}
{"comment": {"body": "Not for this PR or it would have exploded", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#discussion_r826539501"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#pullrequestreview-909610234", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#pullrequestreview-909612002", "body": ""}
{"comment": {"body": "This is the magic that results in a 401 response when the team claims don't match the teamId in the path", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#discussion_r826542920"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#pullrequestreview-909613943", "body": ""}
{"comment": {"body": "The requirement of team scope validation checks means that the bearer token generator for the client needs to embed the list of teams. \r\n\r\nThis made me realize it's always better for test classes to specify their inputs rather than pull them from default values generated by this class, so I refactored the test classes using `UnblockedApiClient` to always pass the values they want to use for identity and team scope.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#discussion_r826544468"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#pullrequestreview-909662832", "body": ""}
{"comment": {"body": "@jeffrey-ng What do you think we should do here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#discussion_r826582939"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#pullrequestreview-910483550", "body": ""}
{"comment": {"body": "We need to build a team store for a variety of reasons -- I am adding this as a fixed const in another PR, we're currently using it in a few places.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#discussion_r827169653"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#pullrequestreview-910572309", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/584#pullrequestreview-910605080", "body": ""}
{"title": "Segregate save from restore", "number": 5840, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5840"}
{"title": "Fix typos in admin", "number": 5841, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5841"}
{"title": "Fix issue where build image artifacts was not being downloaded", "number": 5842, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5842"}
{"title": "RevertGradle2", "number": 5843, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5843", "body": "Revert gradle upgrade for now\nRevet gradle"}
{"title": "Github action cache is a composite step and step timeouts do not work", "number": 5844, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5844", "body": "We are definitely moving to s3\nThis is ridiculous and amateur hour not having a timeout work on composite actions like action/cache."}
{"title": "[WIP] Update minimum threshold for determining thread relevancy", "number": 5845, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5845"}
{"title": "fix(deps): update dependency constructs to v10.2.5", "number": 5846, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5846", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| constructs | 10.2.4 -> 10.2.5 |  |  |  |  |\n\nRelease Notes\n\naws/constructs\n\n### [`v10.2.5`]()\n\n[Compare Source]()\n\n##### [10.2.5]() (2023-04-24)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update sentry-javascript monorepo to v7.49.0", "number": 5847, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5847", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @sentry/browser (source) | 7.19.0 -> 7.49.0 |  |  |  |  |\n| @sentry/node (source) | 7.19.0 -> 7.49.0 |  |  |  |  |\n| @sentry/react (source) | 7.19.0 -> 7.49.0 |  |  |  |  |\n| @sentry/tracing (source) | 7.19.0 -> 7.49.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ngetsentry/sentry-javascript\n\n### [`v7.49.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7490)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.48.0...7.49.0)\n\n##### Important Changes\n\n-   **feat(sveltekit): Read adapter output directory from `svelte.config.js` ([#7863](https://togithub.com/getsentry/sentry-javascript/issues/7863))**\n\nOur source maps upload plugin is now able to read `svelte.config.js`. This is necessary to automatically find the output directory that users can specify when setting up the Node adapter.\n\n-   **fix(replay): Ensure we normalize scope breadcrumbs to max. depth to avoid circular ref ([#7915](https://togithub.com/getsentry/sentry-javascript/issues/7915))**\n\nThis release fixes a potential problem with how Replay captures console logs.\nAny objects logged will now be cut off after a maximum depth of 10, as well as cutting off any properties after the 1000th.\nThis should ensure we do not accidentally capture massive console logs, where a stringified object could reach 100MB or more.\n\n-   **fix(utils): Normalize HTML elements as string ([#7916](https://togithub.com/getsentry/sentry-javascript/issues/7916))**\n\nWe used to normalize references to HTML elements as POJOs.\nThis is both not very easily understandable, as well as potentially large, as HTML elements may have properties attached to them.\nWith this change, we now normalize them to e.g. `[HTMLElement: HTMLInputElement]`.\n\n##### Additional Features and Fixes\n\n-   feat(browser): Simplify stack parsers ([#7897](https://togithub.com/getsentry/sentry-javascript/issues/7897))\n-   feat(node): Add monitor upsert types ([#7914](https://togithub.com/getsentry/sentry-javascript/issues/7914))\n-   feat(replay): Truncate network bodies to max size ([#7875](https://togithub.com/getsentry/sentry-javascript/issues/7875))\n-   fix(gatsby): Don't crash build when auth token is missing ([#7858](https://togithub.com/getsentry/sentry-javascript/issues/7858))\n-   fix(gatsby): Use `import` for `gatsby-browser.js` instead of `require` ([#7889](https://togithub.com/getsentry/sentry-javascript/issues/7889))\n-   fix(nextjs): Handle braces in stack frame URLs ([#7900](https://togithub.com/getsentry/sentry-javascript/issues/7900))\n-   fix(nextjs): Mark value injection loader result as uncacheable ([#7870](https://togithub.com/getsentry/sentry-javascript/issues/7870))\n-   fix(node): Correct typo in trpc integration transaciton name ([#7871](https://togithub.com/getsentry/sentry-javascript/issues/7871))\n-   fix(node): reduce deepReadDirSync runtime complexity ([#7910](https://togithub.com/getsentry/sentry-javascript/issues/7910))\n-   fix(sveltekit): Avoid capturing \"Not Found\" errors in server `handleError` wrapper ([#7898](https://togithub.com/getsentry/sentry-javascript/issues/7898))\n-   fix(sveltekit): Detect sentry release before creating the Vite plugins ([#7902](https://togithub.com/getsentry/sentry-javascript/issues/7902))\n-   fix(sveltekit): Use `sentry.properties` file when uploading source maps ([#7890](https://togithub.com/getsentry/sentry-javascript/issues/7890))\n-   fix(tracing): Ensure we use s instead of ms for startTimestamp ([#7877](https://togithub.com/getsentry/sentry-javascript/issues/7877))\n-   ref(deprecate): Deprecate `timestampWithMs` ([#7878](https://togithub.com/getsentry/sentry-javascript/issues/7878))\n-   ref(nextjs): Don't use Sentry Webpack Plugin in dev mode ([#7901](https://togithub.com/getsentry/sentry-javascript/issues/7901))\n\n### [`v7.48.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7480)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.47.0...7.48.0)\n\n##### Important Changes\n\n-   **feat(node): Add `AsyncLocalStorage` implementation of `AsyncContextStrategy` ([#7800](https://togithub.com/getsentry/sentry-javascript/issues/7800))**\n    -   feat(core): Extend `AsyncContextStrategy` to allow reuse of existing context ([#7778](https://togithub.com/getsentry/sentry-javascript/issues/7778))\n    -   feat(core): Make `runWithAsyncContext` public API ([#7817](https://togithub.com/getsentry/sentry-javascript/issues/7817))\n    -   feat(core): Add async context abstraction ([#7753](https://togithub.com/getsentry/sentry-javascript/issues/7753))\n    -   feat(node): Adds `domain` implementation of `AsyncContextStrategy` ([#7767](https://togithub.com/getsentry/sentry-javascript/issues/7767))\n    -   feat(node): Auto-select best `AsyncContextStrategy` for Node.js version ([#7804](https://togithub.com/getsentry/sentry-javascript/issues/7804))\n    -   feat(node): Migrate to domains used through `AsyncContextStrategy` ([#7779](https://togithub.com/getsentry/sentry-javascript/issues/7779))\n\nThis release switches the SDK to use [`AsyncLocalStorage`](https://nodejs.org/api/async_context.html#class-asynclocalstorage) as the async context isolation mechanism in the SDK for Node 14+. For Node 10 - 13, we continue to use the Node [`domain`](https://nodejs.org/api/domain.html) standard library, since `AsyncLocalStorage` is not supported there. **Preliminary testing showed [a 30% improvement in latency and rps](https://togithub.com/getsentry/sentry-javascript/issues/7691#issuecomment-1504009089) when making the switch from domains to `AsyncLocalStorage` on Node 16.**\n\nIf you want to manually add async context isolation to your application, you can use the new `runWithAsyncContext` API.\n\n```js\nimport * as Sentry from '@sentry/node';\n\nconst requestHandler = (ctx, next) => {\n  return new Promise((resolve, reject) => {\n    Sentry.runWithAsyncContext(async () => {\n      const hub = Sentry.getCurrentHub();\n\n      hub.configureScope(scope =>\n        scope.addEventProcessor(event =>\n          Sentry.addRequestDataToEvent(event, ctx.request, {\n            include: {\n              user: false,\n            },\n          })\n        )\n      );\n\n      try {\n        await next();\n      } catch (err) {\n        reject(err);\n      }\n      resolve();\n    });\n  });\n};\n```\n\nIf you're manually using domains to isolate Sentry data, we strongly recommend switching to this API!\n\nIn addition to exporting `runWithAsyncContext` publicly, the SDK also uses it internally where we previously used domains.\n\n-   **feat(sveltekit): Remove `withSentryViteConfig` ([#7789](https://togithub.com/getsentry/sentry-javascript/issues/7789))**\n    -   feat(sveltekit): Remove SDK initialization via dedicated files ([#7791](https://togithub.com/getsentry/sentry-javascript/issues/7791))\n\nThis release removes our `withSentryViteConfig` wrapper we previously instructed you to add to your `vite.config.js` file. It is replaced Vite plugins which you simply add to your Vite config, just like the `sveltekit()` Vite plugins. We believe this is a more transparent and Vite/SvelteKit-native way of applying build time modifications. Here's how to use the plugins:\n\n```js\n// vite.config.js\nimport { sveltekit } from '@sveltejs/kit/vite';\nimport { sentrySvelteKit } from '@sentry/sveltekit';\n\nexport default {\n  plugins: [sentrySvelteKit(), sveltekit()],\n  // ... rest of your Vite config\n};\n```\n\nTake a look at the [`README`](https://togithub.com/getsentry/sentry-javascript/blob/develop/packages/sveltekit/README.md) for updated instructions!\n\nFurthermore, with this transition, we removed the possibility to intialize the SDK in dedicated `sentry.(client|server).config.js` files. Please use SvelteKit's [hooks files](https://togithub.com/getsentry/sentry-javascript/blob/develop/packages/sveltekit/README.md#2-client-side-setup) to initialize the SDK.\n\nPlease note that these are **breaking changes**! We're sorry for the inconvenience but the SvelteKit SDK is still in alpha stage and we want to establish a clean and SvelteKit-friendly API before making the SDK stable. You have been [warned](https://togithub.com/getsentry/sentry-javascript/blob/eb921275f9c572e72c2348a91cb39fcbb8275b8d/packages/sveltekit/README.md#L20-L24) ;)\n\n-   **feat(sveltekit): Add Sentry Vite Plugin to upload source maps ([#7811](https://togithub.com/getsentry/sentry-javascript/issues/7811))**\n\nThis release adds automatic upload of source maps to the SvelteKit SDK. No need to configure anything other than adding our Vite plugins to your SDK. The example above shows you how to do this.\n\nPlease make sure to follow the [`README`](https://togithub.com/getsentry/sentry-javascript/blob/develop/packages/sveltekit/README.md#uploading-source-maps) to specify your Sentry auth token, as well as org and project slugs.\n\n**- feat(replay): Capture request & response headers ([#7816](https://togithub.com/getsentry/sentry-javascript/issues/7816))**\n\nReplay now captures the `content-length`, `content-type`, and `accept` headers from requests and responses automatically.\n\n##### Additional Features and Fixes\n\n-   feat(browser): Export request instrumentation options ([#7818](https://togithub.com/getsentry/sentry-javascript/issues/7818))\n-   feat(core): Add async context abstraction ([#7753](https://togithub.com/getsentry/sentry-javascript/issues/7753))\n-   feat(core): Add DSC to all outgoing envelopes ([#7820](https://togithub.com/getsentry/sentry-javascript/issues/7820))\n-   feat(core): Cache processed stacks for debug IDs ([#7825](https://togithub.com/getsentry/sentry-javascript/issues/7825))\n-   feat(node): Add checkin envelope types ([#7777](https://togithub.com/getsentry/sentry-javascript/issues/7777))\n-   feat(replay): Add `getReplayId()` method ([#7822](https://togithub.com/getsentry/sentry-javascript/issues/7822))\n-   fix(browser): Adjust `BrowserTransportOptions` to support offline transport options ([#7775](https://togithub.com/getsentry/sentry-javascript/issues/7775))\n-   fix(browser): DOMException SecurityError stacktrace parsing bug ([#7821](https://togithub.com/getsentry/sentry-javascript/issues/7821))\n-   fix(core): Log warning when tracing extensions are missing ([#7601](https://togithub.com/getsentry/sentry-javascript/issues/7601))\n-   fix(core): Only call `applyDebugMetadata` for error events ([#7824](https://togithub.com/getsentry/sentry-javascript/issues/7824))\n-   fix(integrations): Ensure httpclient integration works with Request ([#7786](https://togithub.com/getsentry/sentry-javascript/issues/7786))\n-   fix(node): `reuseExisting` does not need to call bind on domain ([#7780](https://togithub.com/getsentry/sentry-javascript/issues/7780))\n-   fix(node): Fix domain scope inheritance ([#7799](https://togithub.com/getsentry/sentry-javascript/issues/7799))\n-   fix(node): Make `trpcMiddleware` factory synchronous ([#7802](https://togithub.com/getsentry/sentry-javascript/issues/7802))\n-   fix(serverless): Account when transaction undefined ([#7829](https://togithub.com/getsentry/sentry-javascript/issues/7829))\n-   fix(utils): Make xhr instrumentation independent of parallel running SDK versions ([#7836](https://togithub.com/getsentry/sentry-javascript/issues/7836))\n\n### [`v7.47.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7470)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.46.0...7.47.0)\n\n##### Important Changes\n\n-   **feat(browser)**: Add captureUserFeedback ([#7729](https://togithub.com/getsentry/sentry-javascript/issues/7729))\n\nThis release adds a new API, `Sentry.captureUserFeedback`, to browser-side SDKs that allows you to send user feedback to Sentry without loading and opening Sentry's user feedback dialog. This allows you to obtain user feedback however and whenever you want to and simply send it to Sentry using the SDK.\n\nFor instance, you can collect feedback, whenever convenient as shown in this example:\n\n```js\nconst eventId = Sentry.captureMessage('User Feedback');\nconst user = Sentry.getCurrentHub().getScope().getUser();\nconst userFeedback = {\n  event_id: eventId;\n  email: user.email\n  name: user.username\n  comments: 'I really like your App, thanks!'\n}\nSentry.captureUserFeedback(userFeedback);\n```\n\nNote that feedback needs to be coupled to an event but as in the example above, you can just use `Sentry.captureMessage` to generate one.\n\nYou could also collect feedback in a custom way if an error happens and use the SDK to send it along:\n\n```js\nSentry.init({\n  dsn: '__DSN__',\n  beforeSend: event => {\n    const userFeedback = collectYourUserFeedback();\n    const feedback = {\n      ...userFeedback,\n      event_id: event.event_id.\n    }\n    Sentry.captureUserFeedback(feedback);\n    return event;\n  }\n})\n```\n\n-   **feat(tracing)**: Deprecate `@sentry/tracing` exports ([#7611](https://togithub.com/getsentry/sentry-javascript/issues/7611))\n\nWith this release, we officially deprecate all exports from the `@sentry/tracing` package, in favour of using them directly from the main SDK package. The `@sentry/tracing` package will be removed in a future major release.\n\nPlease take a look at the [Migration docs](./MIGRATION.md/#remove-requirement-for-sentrytracing-package-since-7460) for more details.\n\n##### Additional Features and Fixes\n\n-   feat(sveltekit): Add partial instrumentation for client-side `fetch` ([#7626](https://togithub.com/getsentry/sentry-javascript/issues/7626))\n-   fix(angular): Handle routes with empty path ([#7686](https://togithub.com/getsentry/sentry-javascript/issues/7686))\n-   fix(angular): Only open report dialog if error was sent ([#7750](https://togithub.com/getsentry/sentry-javascript/issues/7750))\n-   fix(core): Determine debug ID paths from the top of the stack ([#7722](https://togithub.com/getsentry/sentry-javascript/issues/7722))\n-   fix(ember): Ensure only one client is created & Replay works ([#7712](https://togithub.com/getsentry/sentry-javascript/issues/7712))\n-   fix(integrations): Ensure HttpClient integration works with Axios ([#7714](https://togithub.com/getsentry/sentry-javascript/issues/7714))\n-   fix(loader): Ensure JS loader works with tracing & add tests ([#7662](https://togithub.com/getsentry/sentry-javascript/issues/7662))\n-   fix(nextjs): Restore tree shaking capabilities ([#7710](https://togithub.com/getsentry/sentry-javascript/issues/7710))\n-   fix(node): Disable `LocalVariables` integration on Node < v18 ([#7748](https://togithub.com/getsentry/sentry-javascript/issues/7748))\n-   fix(node): Redact URL authority only in breadcrumbs and spans ([#7740](https://togithub.com/getsentry/sentry-javascript/issues/7740))\n-   fix(react): Only show report dialog if event was sent to Sentry ([#7754](https://togithub.com/getsentry/sentry-javascript/issues/7754))\n-   fix(remix): Remove unnecessary dependencies  ([#7708](https://togithub.com/getsentry/sentry-javascript/issues/7708))\n-   fix(replay): Ensure circular references are handled ([#7752](https://togithub.com/getsentry/sentry-javascript/issues/7752))\n-   fix(sveltekit): Don't capture thrown `Redirect`s as exceptions ([#7731](https://togithub.com/getsentry/sentry-javascript/issues/7731))\n-   fix(sveltekit): Log error to console by default in `handleErrorWithSentry` ([#7674](https://togithub.com/getsentry/sentry-javascript/issues/7674))\n-   fix(tracing): Make sure idle transaction does not override other transactions ([#7725](https://togithub.com/getsentry/sentry-javascript/issues/7725))\n\nWork in this release contributed by [@de-don](https://togithub.com/de-don) and [@TrySound](https://togithub.com/TrySound). Thank you for your contributions!\n\n### [`v7.46.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7460)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.45.0...7.46.0)\n\n##### Important Changes\n\n-   **feat(sveltekit)**: Add Performance Monitoring for SvelteKit\n    -   feat(sveltekit): Add meta tag for backend -> frontend ([#7574](https://togithub.com/getsentry/sentry-javascript/issues/7574))\n    -   fix(sveltekit): Explicitly export Node SDK exports ([#7644](https://togithub.com/getsentry/sentry-javascript/issues/7644))\n    -   fix(sveltekit): Handle nested server calls in `sentryHandle` ([#7598](https://togithub.com/getsentry/sentry-javascript/issues/7598))\n    -   ref(sveltekit): Split up universal and server load wrappers ([#7652](https://togithub.com/getsentry/sentry-javascript/issues/7652))\n\nThis release adds support for Performance Monitoring in our SvelteKit SDK for the client/server. We've also changed how you should initialize your SDK. Please read our updated [SvelteKit README instructions](./packages/sveltekit/README.md) for more details.\n\n-   **feat(core)**: Add `ignoreTransactions` option ([#7594](https://togithub.com/getsentry/sentry-javascript/issues/7594))\n\nYou can now easily filter out certain transactions from being sent to Sentry based on their name.\n\n```ts\nSentry.init({\n  ignoreTransactions: ['/api/healthcheck', '/ping'],\n})\n```\n\n-   **feat(node)**: Undici integration ([#7582](https://togithub.com/getsentry/sentry-javascript/issues/7582))\n    -   feat(nextjs): Add Undici integration automatically ([#7648](https://togithub.com/getsentry/sentry-javascript/issues/7648))\n    -   feat(sveltekit): Add Undici integration by default ([#7650](https://togithub.com/getsentry/sentry-javascript/issues/7650))\n\nWe've added an integration that automatically instruments [Undici](https://togithub.com/nodejs/undici) and Node server side fetch. This supports Undici `v4.7.0` or higher and requires Node `v16.7.0` or higher. After adding the integration outgoing requests made by Undici will have associated spans and breadcrumbs in Sentry.\n\n```ts\nSentry.init({\n  integrations: [new Sentry.Integrations.Undici()],\n})\n```\n\nIn our Next.js and SvelteKit SDKs, this integration is automatically added.\n\n-   **feat(node)**: Add Sentry tRPC middleware ([#7511](https://togithub.com/getsentry/sentry-javascript/issues/7511))\n\nWe've added a new middleware for [trpc](https://trpc.io/) that automatically adds TRPC information to Sentry transactions. This middleware is meant to be used in combination with a Sentry server integration (Next.js, Express, etc).\n\n```ts\nimport { initTRPC } from '@trpc/server';\nimport * as Sentry from '@sentry/node';\n\nconst t = initTRPC.context().create();\nconst sentryMiddleware = t.middleware(\n  Sentry.Handlers.trpcMiddleware({\n    attachRpcInput: true,\n  }),\n);\n\nconst sentrifiedProcedure = t.procedure.use(sentryMiddleware);\n```\n\n-   **feat(tracing)**: Remove requirement for `@sentry/tracing` package\n\nWith `7.46.0` you no longer require the `@sentry/tracing` package to use tracing and performance monitoring with the Sentry JavaScript SDKs. The `@sentry/tracing` package will be removed in a future major release, but can still be used with no changes.\n\nPlease see the [Migration docs](./MIGRATION.md/#remove-requirement-for-sentrytracing-package-since-7460) for more details.\n\n-   **fix(node)**: Convert debugging code to callbacks to fix memory leak in `LocalVariables` integration ([#7637](https://togithub.com/getsentry/sentry-javascript/issues/7637))\n\nThis fixes a memory leak in the opt-in [`LocalVariables` integration](https://blog.sentry.io/2023/02/01/local-variables-for-nodejs-in-sentry/), which adds local variables to the stacktraces sent to Sentry. The minimum recommended version to use the `LocalVariables` is now `7.46.0`.\n\n##### Additional Features and Fixes\n\n-   feat(node): Auto discovery only returns integrations where dependency loads ([#7603](https://togithub.com/getsentry/sentry-javascript/issues/7603))\n-   feat(node): Sanitize URLs in Span descriptions and breadcrumbs (PII) ([#7667](https://togithub.com/getsentry/sentry-javascript/issues/7667))\n-   feat(replay): Add `responseStatus`, `decodedBodySize` to perf entries ([#7613](https://togithub.com/getsentry/sentry-javascript/issues/7613))\n-   feat(replay): Add experiment to capture request/response bodies ([#7589](https://togithub.com/getsentry/sentry-javascript/issues/7589))\n-   feat(replay): Capture replay mutation breadcrumbs & add experiment ([#7568](https://togithub.com/getsentry/sentry-javascript/issues/7568))\n-   feat(tracing): Ensure `pageload` transaction starts at timeOrigin ([#7632](https://togithub.com/getsentry/sentry-javascript/issues/7632))\n-   fix(core): Remove `abs_path` from stack trace (reverting [#7167](https://togithub.com/getsentry/sentry-javascript/issues/7167)) ([#7623](https://togithub.com/getsentry/sentry-javascript/issues/7623))\n-   fix(nextjs): Add loading component type to server component wrapping ([#7639](https://togithub.com/getsentry/sentry-javascript/issues/7639))\n-   fix(nextjs): Don't report `NEXT_NOT_FOUND` and `NEXT_REDIRECT` errors ([#7642](https://togithub.com/getsentry/sentry-javascript/issues/7642))\n-   fix(nextjs): Rewrite `abs_path` frames ([#7619](https://togithub.com/getsentry/sentry-javascript/issues/7619))\n-   fix(nextjs): Show errors and warnings only once during build ([#7651](https://togithub.com/getsentry/sentry-javascript/issues/7651))\n-   fix(nextjs): Use Next.js internal AsyncStorage ([#7630](https://togithub.com/getsentry/sentry-javascript/issues/7630))\n-   fix(nextjs): Gracefully handle undefined `beforeFiles` in rewrites ([#7649](https://togithub.com/getsentry/sentry-javascript/issues/7649))\n\nWork in this release contributed by [@aldenquimby](https://togithub.com/aldenquimby) and [@bertho-zero](https://togithub.com/bertho-zero). Thank you for your contributions!\n\n### [`v7.45.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7450)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.44.2...7.45.0)\n\n-   build(cdn): Ensure ES5 bundles do not use non-ES5 code ([#7550](https://togithub.com/getsentry/sentry-javascript/issues/7550))\n-   feat(core): Add trace function ([#7556](https://togithub.com/getsentry/sentry-javascript/issues/7556))\n-   feat(hub): Make scope always defined on the hub ([#7551](https://togithub.com/getsentry/sentry-javascript/issues/7551))\n-   feat(replay): Add `replay_id` to transaction DSC ([#7571](https://togithub.com/getsentry/sentry-javascript/issues/7571))\n-   feat(replay): Capture fetch body size for replay events ([#7524](https://togithub.com/getsentry/sentry-javascript/issues/7524))\n-   feat(sveltekit): Add performance monitoring for client load ([#7537](https://togithub.com/getsentry/sentry-javascript/issues/7537))\n-   feat(sveltekit): Add performance monitoring for server load ([#7536](https://togithub.com/getsentry/sentry-javascript/issues/7536))\n-   feat(sveltekit): Add performance monitoring to Sveltekit server handle ([#7532](https://togithub.com/getsentry/sentry-javascript/issues/7532))\n-   feat(sveltekit): Add SvelteKit routing instrumentation ([#7565](https://togithub.com/getsentry/sentry-javascript/issues/7565))\n-   fix(browser): Ensure keepalive flag is correctly set for parallel requests ([#7553](https://togithub.com/getsentry/sentry-javascript/issues/7553))\n-   fix(core): Ensure `ignoreErrors` only applies to error events ([#7573](https://togithub.com/getsentry/sentry-javascript/issues/7573))\n-   fix(node): Consider tracing error handler for process exit ([#7558](https://togithub.com/getsentry/sentry-javascript/issues/7558))\n-   fix(otel): Make sure we use correct hub on finish ([#7577](https://togithub.com/getsentry/sentry-javascript/issues/7577))\n-   fix(react): Handle case where error.cause already defined ([#7557](https://togithub.com/getsentry/sentry-javascript/issues/7557))\n\n### [`v7.44.2`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7442)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.44.1...7.44.2)\n\n-   fix(cdn): Fix ES5 CDN bundles ([#7544](https://togithub.com/getsentry/sentry-javascript/issues/7544))\n\n### [`v7.44.1`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7441)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.44.0...7.44.1)\n\n-   ref(core): Move beforeEnvelope to client ([#7527](https://togithub.com/getsentry/sentry-javascript/issues/7527))\n\n### [`v7.44.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7440)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.43.0...7.44.0)\n\nThis release introduces the first alpha version of `@sentry/sveltekit`, our newest JavaScript SDK for Sveltekit. Check out the [README](./packages/sveltekit/README.md) for usage instructions and what to expect from this alpha release.\n\n-   feat(replay): Add `request_body_size` & `response_body_size` to fetch/xhr ([#7407](https://togithub.com/getsentry/sentry-javascript/issues/7407))\n-   feat(replay): Add additional properties for UI clicks ([#7395](https://togithub.com/getsentry/sentry-javascript/issues/7395))\n-   feat(replay): Reduce time limit before pausing a recording ([#7356](https://togithub.com/getsentry/sentry-javascript/issues/7356))\n-   feat(replay): Upgrade `rrweb` and `rrweb-player` ([#7508](https://togithub.com/getsentry/sentry-javascript/issues/7508))\n-   feat(replay): Use new afterSend hook to improve error linking ([#7390](https://togithub.com/getsentry/sentry-javascript/issues/7390))\n-   feat(serverless): Publish lambda layer for Node 16/18 ([#7483](https://togithub.com/getsentry/sentry-javascript/issues/7483))\n-   feat(sveltekit): Add wrapper for client load function ([#7447](https://togithub.com/getsentry/sentry-javascript/issues/7447))\n-   feat(sveltekit): Add wrapper for server load function ([#7416](https://togithub.com/getsentry/sentry-javascript/issues/7416))\n-   feat(sveltekit): Add server-side `handleError` wrapper ([#7411](https://togithub.com/getsentry/sentry-javascript/issues/7411))\n-   feat(sveltekit): Introduce client-side `handleError` wrapper ([#7406](https://togithub.com/getsentry/sentry-javascript/issues/7406))\n-   feat(sveltekit): Add SvelteKit client and server `init` functions ([#7408](https://togithub.com/getsentry/sentry-javascript/issues/7408))\n-   feat(sveltekit): Inject `Sentry.init` calls into server and client bundles ([#7391](https://togithub.com/getsentry/sentry-javascript/issues/7391))\n-   feat(tracing): Expose `BrowserTracing` in non-tracing bundles ([#7479](https://togithub.com/getsentry/sentry-javascript/issues/7479))\n-   fix(core): Permanent idle timeout cancel finishes the transaction with the last finished child\n-   fix(integrations): Handle lower-case prefix windows paths in `RewriteFrames` ([#7506](https://togithub.com/getsentry/sentry-javascript/issues/7506))\n-   fix(next): Guard against missing serverSideProps ([#7517](https://togithub.com/getsentry/sentry-javascript/issues/7517))\n-   fix(nextjs): Fix broken server component wrapping because of interrupted promise chain ([#7456](https://togithub.com/getsentry/sentry-javascript/issues/7456))\n-   fix(nextjs): Fix runtime error for static pages ([#7476](https://togithub.com/getsentry/sentry-javascript/issues/7476))\n-   fix(profiling): Catch sendProfile rejection ([#7446](https://togithub.com/getsentry/sentry-javascript/issues/7446))\n-   fix(replay): Never capture file input changes ([#7485](https://togithub.com/getsentry/sentry-javascript/issues/7485))\n-   fix(serverless): Explicitly export node package exports ([#7457](https://togithub.com/getsentry/sentry-javascript/issues/7457))\n-   fix(vue): Do not depend on `window.location` for SSR environments ([#7518](https://togithub.com/getsentry/sentry-javascript/issues/7518))\n\n**Replay `rrweb` changes:**\n\n`@sentry-internal/rrweb` was updated from 1.105.0 to 1.106.0:\n\n-   feat: Ensure password inputs are always masked ([#78](https://togithub.com/getsentry/rrweb/pull/78))\n-   fix: Ensure text masking for updated attributes works ([#83](https://togithub.com/getsentry/rrweb/pull/83))\n-   fix: Ensure unmaskTextSelector is used for masked attributes ([#81](https://togithub.com/getsentry/rrweb/pull/81))\n-   fix: Mask  values for selects & radio/checkbox value ([#75](https://togithub.com/getsentry/rrweb/pull/75))\n\nWork in this release contributed by [@woochanleee](https://togithub.com/woochanleee) and [@baked-dev](https://togithub.com/baked-dev). Thank you for your contribution!\n\n### [`v7.43.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7430)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.42.0...7.43.0)\n\n-   feat(nextjs): Run source map upload in Vercel develop and preview environments ([#7436](https://togithub.com/getsentry/sentry-javascript/issues/7436))\n-   feat(types): Add `profilesSampler` option to node client type ([#7385](https://togithub.com/getsentry/sentry-javascript/issues/7385))\n-   fix(core): Avoid using `Array.findIndex()` as it is ES5 incompatible ([#7400](https://togithub.com/getsentry/sentry-javascript/issues/7400))\n-   fix(nextjs): Add better error messages for missing params during next build ([#7434](https://togithub.com/getsentry/sentry-javascript/issues/7434))\n-   fix(nextjs): Don't crash build when auth token is missing\n-   fix(node): Revert to dynamic `require` call to fix monkey patching ([#7430](https://togithub.com/getsentry/sentry-javascript/issues/7430))\n-   fix(types): Fix node types & add E2E test ([#7429](https://togithub.com/getsentry/sentry-javascript/issues/7429))\n\n### [`v7.42.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7420)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.41.0...7.42.0)\n\n-   feat(core): Add lifecycle hooks ([#7370](https://togithub.com/getsentry/sentry-javascript/issues/7370))\n-   feat(core): Emit hooks for transaction start/finish ([#7387](https://togithub.com/getsentry/sentry-javascript/issues/7387))\n-   feat(nextjs): Connect traces for server components ([#7320](https://togithub.com/getsentry/sentry-javascript/issues/7320))\n-   feat(replay): Attach an error `cause` to send exceptions ([#7350](https://togithub.com/getsentry/sentry-javascript/issues/7350))\n-   feat(replay): Consider user input in form field as \"user activity\" ([#7355](https://togithub.com/getsentry/sentry-javascript/issues/7355))\n-   feat(replay): Update rrweb to 1.105.0 & add breadcrumb when encountering large mutation ([#7314](https://togithub.com/getsentry/sentry-javascript/issues/7314))\n-   feat(tracing): Expose cancelIdleTimeout and add option to make it permanent ([#7236](https://togithub.com/getsentry/sentry-javascript/issues/7236))\n-   feat(tracing): Track PerformanceObserver interactions as spans ([#7331](https://togithub.com/getsentry/sentry-javascript/issues/7331))\n-   fix(core): Ensure `originalException` has type `unknown` ([#7361](https://togithub.com/getsentry/sentry-javascript/issues/7361))\n-   fix(core): Avoid using `Object.values()` ([#7360](https://togithub.com/getsentry/sentry-javascript/issues/7360))\n-   fix(react): Make redux integration be configurable via `normalizeDepth` ([#7379](https://togithub.com/getsentry/sentry-javascript/issues/7379))\n-   fix(tracing): Record LCP and CLS on transaction finish ([#7386](https://togithub.com/getsentry/sentry-javascript/issues/7386))\n-   ref(browser): Improve type safety of breadcrumbs integration ([#7382](https://togithub.com/getsentry/sentry-javascript/issues/7382))\n-   ref(node): Parallelize disk io when reading source files for context lines ([#7374](https://togithub.com/getsentry/sentry-javascript/issues/7374))\n-   ref(node): Partially remove dynamic `require` calls ([#7377](https://togithub.com/getsentry/sentry-javascript/issues/7377))\n\n**Replay `rrweb` changes:**\n\n`@sentry-internal/rrweb` was updated from 1.104.1 to 1.105.0 ([#7314](https://togithub.com/getsentry/sentry-javascript/issues/7314)):\n\n-   feat: Add `onMutation` option to record ([#70](https://togithub.com/getsentry/rrweb/pull/69))\n-   fix: Ensure `` is masked ([#69](https://togithub.com/getsentry/rrweb/pull/69))\n\n### [`v7.41.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7410)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.40.0...7.41.0)\n\n-   feat: Ensure we use the same default `environment` everywhere ([#7327](https://togithub.com/getsentry/sentry-javascript/issues/7327))\n-   feat(profiling): Add JS self profiling in the browser ([#7273](https://togithub.com/getsentry/sentry-javascript/issues/7273))\n-   feat(vue): Allow to set `routeLabel: 'path'` to opt-out of using name ([#7326](https://togithub.com/getsentry/sentry-javascript/issues/7326))\n-   fix(profiling): Guard from throwing if profiler constructor throws ([#7328](https://togithub.com/getsentry/sentry-javascript/issues/7328))\n-   fix(react): Use namespace import for react router v6 ([#7330](https://togithub.com/getsentry/sentry-javascript/issues/7330))\n-   fix(remix): Correctly parse `X-Forwarded-For` Http header ([#7329](https://togithub.com/getsentry/sentry-javascript/issues/7329))\n\nWork in this release contributed by [@OliverJAsh](https://togithub.com/OliverJAsh). Thank you for your contribution!\n\n### [`v7.40.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7400)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.39.0...7.40.0)\n\n-   feat(nextjs): Automatically resolve source of errors in dev mode ([#7294](https://togithub.com/getsentry/sentry-javascript/issues/7294))\n-   feat(vue): Log errors to the console by default ([#7310](https://togithub.com/getsentry/sentry-javascript/issues/7310))\n-   fix(ember): Disable performance in FastBoot ([#7282](https://togithub.com/getsentry/sentry-javascript/issues/7282))\n-   fix(serverless): Capture custom tags in error events of GCP functions ([#7298](https://togithub.com/getsentry/sentry-javascript/issues/7298))\n-   fix(serverless): Capture custom tags in GCP Background and CloudEvent function error events ([#7301](https://togithub.com/getsentry/sentry-javascript/issues/7301))\n\n### [`v7.39.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7390)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.38.0...7.39.0)\n\nThis release adds a new package, `@sentry/angular-ivy`, which is our Angular SDK with full support for Angular's rendering engine, Ivy.\n\nThis release also adds a new `enableTracing` option, which can be used instead of `tracesSampleRate` for an easier setup.\nRelated to this, the `hasTracingEnabled` utility function was moved from `@sentry/tracing` to `@sentry/core`.\nThe old export from `@sentry/tracing` has been deprecated and will be removed in v8.\n\n-   feat(angular): Add Ivy-compatible Angular SDK package ([#7264](https://togithub.com/getsentry/sentry-javascript/issues/7264))\n-   feat(core): Add source map images to `debug_meta` ([#7168](https://togithub.com/getsentry/sentry-javascript/issues/7168))\n-   feat(loader): Make lazy-loading configurable ([#7232](https://togithub.com/getsentry/sentry-javascript/issues/7232))\n-   feat(nextjs): Add performance monitoring to server components ([#7242](https://togithub.com/getsentry/sentry-javascript/issues/7242))\n-   feat(nextjs): Default to `VERCEL_ENV` as environment ([#7227](https://togithub.com/getsentry/sentry-javascript/issues/7227))\n-   feat(replay): Add more default block filters ([#7233](https://togithub.com/getsentry/sentry-javascript/issues/7233))\n-   feat(tracing): Add `enableTracing` option ([#7238](https://togithub.com/getsentry/sentry-javascript/issues/7238))\n-   fix(core): Exclude client reports from offline queuing ([#7226](https://togithub.com/getsentry/sentry-javascript/issues/7226))\n-   fix(nextjs): Export serverside data-fetcher wrappers from client ([#7256](https://togithub.com/getsentry/sentry-javascript/issues/7256))\n-   fix(replay): Fix timestamps on LCP ([#7225](https://togithub.com/getsentry/sentry-javascript/issues/7225))\n\n**Replay `rrweb` changes:**\n\n`@sentry-internal/rrweb` was updated from 1.103.0 to 1.104.1 ([#7238](https://togithub.com/getsentry/sentry-javascript/issues/7238)):\n\n-   feat: Export `typings/types` ([#60](https://togithub.com/getsentry/rrweb/pull/60))\n-   feat: Remove `autoplay` attribute from audio/video tags ([#59](https://togithub.com/getsentry/rrweb/pull/59))\n-   fix: Exclude `modulepreload` as well ([#52](https://togithub.com/getsentry/rrweb/pull/52))\n-   fix: Handle removed attributes ([#65](https://togithub.com/getsentry/rrweb/pull/65))\n-   fix: Masking inputs on change when `maskAllInputs:false` ([#61](https://togithub.com/getsentry/rrweb/pull/61))\n-   fix: More robust `rootShadowHost` check ([#50](https://togithub.com/getsentry/rrweb/pull/50))\n-   fix: Textarea value is being duplicated ([#62](https://togithub.com/getsentry/rrweb/pull/62))\n\n### [`v7.38.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7380)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.37.2...7.38.0)\n\n-   feat: Put `abs_path` into stack frame object ([#7167](https://togithub.com/getsentry/sentry-javascript/issues/7167))\n-   feat(integrations): Deprecate `Offline` integration ([#7063](https://togithub.com/getsentry/sentry-javascript/issues/7063))\n-   feat(otel): Convert exception otel events to sentry errors ([#7165](https://togithub.com/getsentry/sentry-javascript/issues/7165))\n-   feat(replay): Change LCP calculation ([#7187](https://togithub.com/getsentry/sentry-javascript/issues/7187))\n-   feat(tracing): Support Apollo/GraphQL with NestJS ([#7194](https://togithub.com/getsentry/sentry-javascript/issues/7194))\n-   feat(tracing): Track `PerformanceResourceTiming.renderBlockingStatus` ([#7127](https://togithub.com/getsentry/sentry-javascript/issues/7127))\n-   feat(tracing|core): Remove transaction name change recording ([#7197](https://togithub.com/getsentry/sentry-javascript/issues/7197))\n-   fix(browser): Ensure dedupe integration ignores non-errors ([#7172](https://togithub.com/getsentry/sentry-javascript/issues/7172))\n-   fix(core): Skip empty integrations ([#7204](https://togithub.com/getsentry/sentry-javascript/issues/7204))\n-   fix(nextjs): Fix faulty import in Next.js .d.ts ([#7175](https://togithub.com/getsentry/sentry-javascript/issues/7175))\n-   fix(otel): Make otel.kind be a string ([#7182](https://togithub.com/getsentry/sentry-javascript/issues/7182))\n-   fix(react): Make fallback render types more accurate ([#7198](https://togithub.com/getsentry/sentry-javascript/issues/7198))\n-   fix(replay): Debounced flushes not respecting `maxWait` ([#7207](https://togithub.com/getsentry/sentry-javascript/issues/7207), [#7208](https://togithub.com/getsentry/sentry-javascript/issues/7208))\n-   ref(replay): Improve logging for stopped replay ([#7174](https://togithub.com/getsentry/sentry-javascript/issues/7174))\n\nWork in this release contributed by [@lucas-zimermann](https://togithub.com/lucas-zimermann). Thank you for your contribution!\n\n### [`v7.37.2`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7372)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.37.1...7.37.2)\n\nThis release includes changes and fixes around text masking and blocking in Replay's `rrweb` dependency. See versions [1.102.0](https://togithub.com/getsentry/rrweb/releases/tag/1.102.0) and [1.103.0](https://togithub.com/getsentry/rrweb/releases/tag/1.103.0).\n\n-   feat: Check `blockSelector` for blocking elements as well\n-   feat: With maskAllText, mask the attributes: placeholder, title, `aria-label`\n-   feat: fix masking on `textarea`\n-   feat: Add `maskAllText` option\n\nSDK Changes:\n\n-   fix(replay): Fix svgs not getting unblocked  ([#7132](https://togithub.com/getsentry/sentry-javascript/issues/7132))\n\n### [`v7.37.1`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7371)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.37.0...7.37.1)\n\n-   fix(browser): Support `async` in stack frame urls ([#7131](https://togithub.com/getsentry/sentry-javascript/issues/7131))\n-   fix(nextjs): Make api route identifier stricter ([#7126](https://togithub.com/getsentry/sentry-javascript/issues/7126))\n-   fix(node): Don't rely on `this` in http integration ([#7135](https://togithub.com/getsentry/sentry-javascript/issues/7135))\n-   fix(replay): Fix missing fetch/xhr requests ([#7134](https://togithub.com/getsentry/sentry-javascript/issues/7134))\n-   fix(tracing): Export `defaultStackParser` from tracing CDN bundles ([#7116](https://togithub.com/getsentry/sentry-javascript/issues/7116))\n\n### [`v7.37.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7370)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.36.0...7.37.0)\n\n-   feat: Add source map debug ids ([#7068](https://togithub.com/getsentry/sentry-javascript/issues/7068))\n-   feat(browser): Add IndexedDb offline transport store ([#6983](https://togithub.com/getsentry/sentry-javascript/issues/6983))\n-   feat(nextjs): Add auto-wrapping for server components ([#6953](https://togithub.com/getsentry/sentry-javascript/issues/6953))\n-   feat(nextjs): Improve client stack traces ([#7097](https://togithub.com/getsentry/sentry-javascript/issues/7097))\n-   feat(replay): Improve rrweb error ignoring ([#7087](https://togithub.com/getsentry/sentry-javascript/issues/7087) & [#7094](https://togithub.com/getsentry/sentry-javascript/issues/7094))\n-   feat(replay): Send client_report when replay sending fails ([#7093](https://togithub.com/getsentry/sentry-javascript/issues/7093))\n-   fix(node): `LocalVariables`, Improve frame matching for ESM ([#7049](https://togithub.com/getsentry/sentry-javascript/issues/7049))\n-   fix(node): Add lru cache to http integration span map ([#7064](https://togithub.com/getsentry/sentry-javascript/issues/7064))\n-   fix(replay): Export Replay from Sentry namespace in full CDN bundle ([#7119](https://togithub.com/getsentry/sentry-javascript/issues/7119))\n\nWork in this release contributed by [@JamesHenry](https://togithub.com/JamesHenry). Thank you for your contribution!\n\n### [`v7.36.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7360)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.35.0...7.36.0)\n\nThis Release re-introduces the accidentally removed but still deprecated `maskInputOptions` option for Session Replay.\nFurthermore, replays are now stopped instead of paused when a rate limit is encountered.\n\n-   feat(replay): Add back deprecated `maskInputOptions` ([#6981](https://togithub.com/getsentry/sentry-javascript/issues/6981))\n-   feat(replay): Stop recording when hitting a rate limit ([#7018](https://togithub.com/getsentry/sentry-javascript/issues/7018))\n-   fix(integrations): Report `BaseClient` integrations added after init ([#7011](https://togithub.com/getsentry/sentry-javascript/issues/7011))\n-   fix(replay): Don't mangle private rrweb property ([#7033](https://togithub.com/getsentry/sentry-javascript/issues/7033))\n-   fix(replay): Fix feature detection of PerformanceObserver ([#7029](https://togithub.com/getsentry/sentry-javascript/issues/7029))\n\n### [`v7.35.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7350)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.34.0...7.35.0)\n\nSession Replay is deprecating privacy options in favor of a more streamlined API. Please see the [Replay migration guide](https://togithub.com/getsentry/sentry-javascript/blob/master/packages/replay/MIGRATION.md) for further information.\nAdditionally, the following configuration options will no longer be configurable: `slimDOMOptions`, `recordCanvas`, `inlineStylesheet`, `collectFonts`, `inlineImages`.\n\n-   feat(browser): Track if cdn or npm bundle ([#6976](https://togithub.com/getsentry/sentry-javascript/issues/6976))\n-   feat(core): Add aria label to breadcrumb attributes ([#6955](https://togithub.com/getsentry/sentry-javascript/issues/6955))\n-   feat(core): Add Offline Transport wrapper ([#6884](https://togithub.com/getsentry/sentry-javascript/issues/6884))\n-   feat(loader): Add SENTRY_SDK_SOURCE to track loader stats ([#6985](https://togithub.com/getsentry/sentry-javascript/issues/6985))\n-   feat(loader): Sync loader with Sentry template ([#7001](https://togithub.com/getsentry/sentry-javascript/issues/7001))\n-   feat(replay): Deprecate privacy options in favor of a new API, remove some recording options ([#6645](https://togithub.com/getsentry/sentry-javascript/issues/6645))\n-   feat(replay): Move sample rate tags into event context ([#6659](https://togithub.com/getsentry/sentry-javascript/issues/6659))\n-   fix(nextjs): Add isomorphic versions of `ErrorBoundary`, `withErrorBoundary` and `showReportDialog` ([#6987](https://togithub.com/getsentry/sentry-javascript/issues/6987))\n-   fix(nextjs): Don't modify require calls in wrapping loader ([#6979](https://togithub.com/getsentry/sentry-javascript/issues/6979))\n-   fix(nextjs): Don't share I/O resources in between requests ([#6980](https://togithub.com/getsentry/sentry-javascript/issues/6980))\n-   fix(nextjs): Inject client config into `_app` instead of `main` ([#7009](https://togithub.com/getsentry/sentry-javascript/issues/7009))\n-   fix(nextjs): Use Proxies to wrap to preserve static methods ([#7002](https://togithub.com/getsentry/sentry-javascript/issues/7002))\n-   fix(replay): Catch style mutation handling & null events in rrweb ([#7010](https://togithub.com/getsentry/sentry-javascript/issues/7010))\n-   fix(replay): Handle compression failures more robustly ([#6988](https://togithub.com/getsentry/sentry-javascript/issues/6988))\n-   fix(replay): Only call `scope.getLastBreadcrumb` if available ([#6969](https://togithub.com/getsentry/sentry-javascript/issues/6969))\n-   fix(utils): Account for null prototype during normalization ([#6925](https://togithub.com/getsentry/sentry-javascript/issues/6925))\n-   ref(replay): Log warning if sample rates are all undefined ([#6959](https://togithub.com/getsentry/sentry-javascript/issues/6959))\n\nWork in this release contributed by [@boblauer](https://togithub.com/boblauer). Thank you for your contribution!\n\n### [`v7.34.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7340)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.33.0...7.34.0)\n\nThis release adds automatic injection of the Next.js SDK into serverside `app` directory bundles, allowing users to call the Sentry SDK in server components.\n\n-   feat(nextjs): Add SDK to serverside `app` directory ([#6927](https://togithub.com/getsentry/sentry-javascript/issues/6927))\n-   fix(replay): Do not renew session in error mode ([#6948](https://togithub.com/getsentry/sentry-javascript/issues/6948))\n-   fix(replay): Handle compression worker errors more gracefully ([#6936](https://togithub.com/getsentry/sentry-javascript/issues/6936))\n-   fix(replay): fix path separator substitution to replay all `\\` ([#6932](https://togithub.com/getsentry/sentry-javascript/issues/6932))\n-   fix(replay): ignore errors in CSSStyleSheetObserver ([getsentry/rrweb#16](https://togithub.com/getsentry/rrweb/issues/16))\n\nWork in this release contributed by [@mdtro](https://togithub.com/mdtro). Thank you for your contribution!\n\n### [`v7.33.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7330)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.32.1...7.33.0)\n\nWith this release, the sample rate for Session Replays will default to 0. We recommend explicitly setting the sample rate via the `replaysSessionSampleRate` and `replaysOnErrorSampleRate` options.\n\n-   feat(replay): Remove default sample rates for replay ([#6878](https://togithub.com/getsentry/sentry-javascript/issues/6878))\n-   feat(replay): try/catch around stopRecording ([#6856](https://togithub.com/getsentry/sentry-javascript/issues/6856))\n-   fix(nextjs): Mark multiplexer targets as entrypoints ([#6919](https://togithub.com/getsentry/sentry-javascript/issues/6919))\n\n### [`v7.32.1`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7321)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.32.0...7.32.1)\n\n-   fix(nextjs): Make SDK multiplexer more resilient ([#6905](https://togithub.com/getsentry/sentry-javascript/issues/6905))\n\n### [`v7.32.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7320)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.31.1...7.32.0)\n\n-   build(replay): Stop preserving modules ([#6817](https://togithub.com/getsentry/sentry-javascript/issues/6817))\n-   feat(nextjs): Add browser SDK to `app` directory browser bundle ([#6812](https://togithub.com/getsentry/sentry-javascript/issues/6812))\n-   feat(node): Use `includeLocalVariables` option to enable `LocalVariables` integration ([#6874](https://togithub.com/getsentry/sentry-javascript/issues/6874))\n-   feat(node): Add option to capture local variables for caught exceptions via LocalVariables integration ([#6876](https://togithub.com/getsentry/sentry-javascript/issues/6876))\n-   feat(replay): Add `flush` method to integration ([#6776](https://togithub.com/getsentry/sentry-javascript/issues/6776))\n-   feat(replay): Handle worker loading errors ([#6827](https://togithub.com/getsentry/sentry-javascript/issues/6827))\n-   feat(replay): Lower the flush max delay from 15 seconds to 5 seconds ([#6761](https://togithub.com/getsentry/sentry-javascript/issues/6761))\n-   feat(tracing): Promote `enableLongTask` to option of `BrowserTracing` ([#6837](https://togithub.com/getsentry/sentry-javascript/issues/6837))\n-   fix(core): Fix replay client report data category ([#6891](https://togithub.com/getsentry/sentry-javascript/issues/6891))\n-   fix(nextjs): Fix SDK multiplexer loader on Windows ([#6866](https://togithub.com/getsentry/sentry-javascript/issues/6866))\n-   fix(otel): Use http/grpc status over span status ([#6879](https://togithub.com/getsentry/sentry-javascript/issues/6879))\n-   fix(react): Add children prop for Profiler ([#6828](https://togithub.com/getsentry/sentry-javascript/issues/6828))\n-   fix(react): Make wrapCreateBrowserRouter generic ([#6862](https://togithub.com/getsentry/sentry-javascript/issues/6862))\n-   fix(remix): Make sure the domain is created before running. ([#6852](https://togithub.com/getsentry/sentry-javascript/issues/6852))\n-   ref(nextjs): Remove NFT build time exclusions ([#6846](https://togithub.com/getsentry/sentry-javascript/issues/6846))\n-   ref(replay): Avoid duplicate debounce timers ([#6863](https://togithub.com/getsentry/sentry-javascript/issues/6863))\n-   ref(replay): Remove unused `initialFlushDelay` option ([#6867](https://togithub.com/getsentry/sentry-javascript/issues/6867))\n-   ref(replay): Send SDK version in Replay events ([#6814](https://togithub.com/getsentry/sentry-javascript/issues/6814))\n\nWork in this release contributed by [@h3rmanj](https://togithub.com/h3rmanj). Thank you for your contribution!\n\n### [`v7.31.1`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7311)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.31.0...7.31.1)\n\n-   build(replay): Provide full browser+tracing+replay bundle ([#6793](https://togithub.com/getsentry/sentry-javascript/issues/6793))\n-   feat(nextjs): Disable NextJS perf monitoring when using otel ([#6820](https://togithub.com/getsentry/sentry-javascript/issues/6820))\n-   fix(nextjs): Add back browser field in package.json ([#6809](https://togithub.com/getsentry/sentry-javascript/issues/6809))\n-   fix(nextjs): Connect Edge API route errors to span ([#6806](https://togithub.com/getsentry/sentry-javascript/issues/6806))\n-   fix(nextjs): Correctly handle ts middleware files ([#6816](https://togithub.com/getsentry/sentry-javascript/issues/6816))\n\n### [`v7.31.0`](https://togithub.com/getsentry/sentry-javascript/blob/HEAD/CHANGELOG.md#7310)\n\n[Compare Source](https://togithub.com/getsentry/sentry-javascript/compare/7.30.0...7.31.0)\n\nThe Next.js SDK now supports error and performance monitoring for Next.js [middleware](https://nextjs.org/docs/advanced-features/middleware) and [Edge API routes](https://nextjs.org/docs/api-routes/edge-api-routes).\nTo set it up, add a `sentry.edge.config.js` or `sentry.edge.config.ts` file to the root of your project and initialize the SDK:\n\n```js\n// sentry.edge.config.js or sentry.edge.config.ts\n\nimport * as Sentry from \"@sentry/nextjs\";\n\nconst SENTRY_DSN = process.env.SENTRY_DSN || process.env.NEXT_PUBLIC_SENTRY_DSN;\n\nSentry.init({\n  dsn: SENTRY_DSN || \"YOUR DSN HERE\",\n  tracesSampleRate: 1.0,\n});\n```\n\nThe Next.js will automatically instrument Edge API routes and middleware.\nIf you want to opt out of automatic instrumentation of middleware can use the `autoInstrumentMiddleware` option in the `sentry` object of your Next.js configuration:\n\n```javascript\nconst moduleExports = {\n  sentry: {\n    autoInstrumentMiddleware: false,\n  },\n};\n```\n\nMiddleware can be manually instrumented by using the `wrapMiddlewareWithSentry` function.\n\n-   feat(nextjs): Add Edge Runtime SDK ([#6752](https://togithub.com/getsentry/sentry-javascript/issues/6752))\n-   feat(nextjs): Add optional options argument to `withSentryConfig` as an alternative to the `sentry` property ([#6721](https://togithub.com/getsentry/sentry-javascript/issues/6721))\n-   feat(nextjs): Add edge route and middleware wrappers ([#6771](https://\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency io.github.reactivecircus.cache4k:cache4k-jvm to v0.11.0", "number": 5848, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5848", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.github.reactivecircus.cache4k:cache4k-jvm | 0.10.0 -> 0.11.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nreactivecircus/cache4k\n\n### [`v0.11.0`](https://togithub.com/reactivecircus/cache4k/blob/HEAD/CHANGELOG.md#0110)\n\n[Compare Source](https://togithub.com/reactivecircus/cache4k/compare/0.10.0...0.11.0)\n\n##### Added\n\n-   New event listener APIs ([@darkxanter](https://togithub.com/darkxanter)) - [35](https://togithub.com/ReactiveCircus/cache4k/pull/35)\n\n##### Fixed\n\n-   Downgrade stately to 1.2.5 to fix duplicate class error - [36](https://togithub.com/ReactiveCircus/cache4k/pull/36)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "add s3 bucket and roles for caching", "number": 5849, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5849", "body": "Add unblocked-gh-actions-s3-cache-sec-ops-us-west-2 bucket for caching\nAdded new IAM roles to be used for reading/writing/deleting objects to the bucket\nCreated IAM groups under root account and assigned the roles to them\nAdded deploybot user to the new IAM groups.\n\nNext step is to validate this by updating our source code backup GitHub action job and see if it works. If that job passes it means the new roles are working as expected.\nBucket Name: unblocked-gh-actions-s3-cache-sec-ops-us-west-2\nRole to assume: arn:aws:iam::************:role/IAMS3AccessRole-unblocked-gh-actions-s3-cache\nI have already deployed these changes locally. We need this PR to be merged before anything causes the S3 bucket to be deleted by another workflow run."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5849#pullrequestreview-**********", "body": "Thank you!!!"}
{"title": "Attempt to use separate fetch libraries", "number": 585, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/585"}
{"title": "Drop TeamSettingsModel.topLevelCommentsThreadsSupported column", "number": 5850, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5850", "body": "No longer needed since we've enabled it for all teams."}
{"title": "Update source backup job", "number": 5851, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5851", "body": "Updated this workflow to use the deploybot user with scoped permissions. I used this to test the new S3 bucket role as well."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5851#pullrequestreview-1398416899", "body": ""}
{"comment": {"body": "I don't like using the cdk deploy user outside of infra workflow. Modified it to use the regular deploybot user which has scoped permissions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5851#discussion_r1175536921"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5851#pullrequestreview-1398438693", "body": ""}
{"title": "Gradle cache s3", "number": 5852, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5852", "body": "We are moving away from using github cache which has proven to be unreliable.\nWe are now going to be using a custom s3 cache implementation.\n"}
{"title": "Fix /repos push channel", "number": 5853, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5853", "body": "This will happen as long as the query used by the PushChannelService is not the same as the query used by the operation.  This gap exists for a number of operations/channels so we should fix that."}
{"comment": {"body": "> This will happen as long as the query used by the PushChannelService is not the same as the query used by the operation. This gap exists for a number of operations/channels so we should fix that.\r\n\r\n100% agree. Very easy to make mistakes here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5853#issuecomment-1520728235"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5853#pullrequestreview-1398676283", "body": "thanks Dave"}
{"title": "Add node distribution info to version manifest", "number": 5854, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#pullrequestreview-1398740113", "body": ""}
{"comment": {"body": "nit, might be worth putting 'Mac' in there somewhere", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#discussion_r1175748093"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#pullrequestreview-1398829181", "body": ""}
{"comment": {"body": "good point!\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#discussion_r1175796063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#pullrequestreview-1401061020", "body": ""}
{"comment": {"body": "Should we introduce a fallback version ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#discussion_r1177271298"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#pullrequestreview-1401062067", "body": ""}
{"comment": {"body": "Oh never mind this is at build time", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#discussion_r1177272028"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#pullrequestreview-1401062134", "body": ""}
{"comment": {"body": "For what purpose?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#discussion_r1177272163"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#pullrequestreview-1401063393", "body": ""}
{"comment": {"body": "Weird my comment via Unblocked wasn't posted here. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#discussion_r1177274303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#pullrequestreview-1401063731", "body": ""}
{"comment": {"body": "Reposting:\r\nI thought this is an application config which is used to retrieve the node version during install. Then realized this is used during the installer build job so no need for a fallback.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#discussion_r1177274580"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5854#pullrequestreview-1401064020", "body": "Looks good to me."}
{"title": "Remove trace logging", "number": 5855, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5855", "body": "Not clear what the benefit of having these is?\nWe filled logz quota on Friday.\n"}
{"title": "IntelliJ webview visibility + Metrics", "number": 5856, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856", "body": "Setup webview visibility trackers in IntelliJ.\nExtension WebviewController will pass webview events to corresponding Agent WebviewContentController, making it available to commands.\nEnables metrics polling."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856#pullrequestreview-1402551132", "body": ""}
{"comment": {"body": "It's a little odd that we make a separate value stream, when it's just being updated to the value from the incomingVisibilityStream.  You could just do something like:\r\n\r\n```\r\nthis.isVisibleStream = IDEAgent.incomingVisibilityStream\r\n    .compose(filterDefined)\r\n    .filter((payload) => payload.key === this.key)\r\n    .map(payload => payload.isVisible)\r\n```\r\n\r\nAnd you could subscribe to it here and hold the current value in a variable instead of a current value stream...  though note that this subscription is never revoked, which is maybe going to cause problems.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856#discussion_r1178235633"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856#pullrequestreview-1402554013", "body": ""}
{"comment": {"body": "I'm confused -- this is a new property, but there's still a `_isVisible` in the base class, and they aren't kept in sync?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856#discussion_r1178237495"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856#pullrequestreview-1402609183", "body": ""}
{"comment": {"body": "Was an oversight. Updated.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856#discussion_r1178273025"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856#pullrequestreview-1402624353", "body": ""}
{"comment": {"body": "I honestly can't follow the lifetime/disposal/subscribe/unsubscribe behavior in this class, it is super complicated.  This maybe needs comments or some simplification?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856#discussion_r1178281659"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5856#pullrequestreview-1402699714", "body": ""}
{"title": "UNB-1189 Fix slack container width", "number": 5857, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5857"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5857#pullrequestreview-1398883693", "body": ""}
{"title": "Allow hub to set its own tutorial state", "number": 5858, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5858", "body": "The hub is now going to set its own tutorial flag after tutorial has been completed, which should impact the global Person tutorial status"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5858#pullrequestreview-1400417586", "body": ""}
{"title": "Update text editor styling", "number": 5859, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5859", "body": "\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5859#pullrequestreview-1398920045", "body": ""}
{"title": "Find TeamMember by Git Email", "number": 586, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/586", "body": "API to find team member by git email.\nWill be used for this UI.\n"}
{"comment": {"body": "@pwerry should weigh in here -- I think the idea is that instead of adding a new API we would add the array of hashed emails onto the TeamMember model...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/586#issuecomment-1068469354"}}
{"comment": {"body": "> @pwerry should weigh in here -- I think the idea is that instead of adding a new API we would add the array of hashed emails onto the TeamMember model...\r\n\r\nThat's correct - no new API needed. I'm going to add the hashed email values to the TeamMember object", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/586#issuecomment-1068470629"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/586#pullrequestreview-910829665", "body": "No new endpoint needed. I'll post a PR soon with the proposed API changes"}
{"title": "Update spacing and quoteblock", "number": 5860, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5860", "body": "Update quoteblock to render proper vertical line.\nUpdates spacing between threads & PRs.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5860#pullrequestreview-1398953967", "body": ""}
{"title": "Implement Jira OAuth token exchange", "number": 5861, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5861", "body": "The idea here is to use the existing authExchange operation (currently used just by Slack) to drive oauth token exchange. We'd then use the access token to list the Jira sites that we've been granted access to, save that to the database, then ask the user to install our marketplace app. When they install our marketplace app, we'll then get a web hook which gives us creds to generate JWTs to access the API. \nThe reason to ask for OAuth is because we need to know the unblocked team to which a Jira site should be linked. Relying solely on the marketplace app installation web hook does not provide enough information.\nTechnically, we could use the user's Oauth access token received via the exchange to ingest Jira issues (and we could fall back to that) but its ultimately better to have them install the marketplace app since the app creds are not user-bound.\nTODO before merging\n create Unblocked Jira OAuth integrations (local, dev, and prod) in  (<NAME_EMAIL>) and add the secrets to our vault\nTODO in later PRs\n- create a database model to persist the Jira site details and (encrypted) creds (a JiraSiteModel similar to SlackTeamModel)\n- upsert Jira provider identities for each team member"}
{"title": "[RFC] Refactor identity related API models", "number": 5862, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862", "body": "remove IDs that should no be exposed to clients:\nIdentity.id\n\nPerson.id\n\n\nremove fields that are unnecessary\n\ndrop Identity.username is only applicable for some providers. Makes no sense for Bitbucket, or Slack for example.\ndrop Identity.isBot. We should just drop bots, unless we label messages from these differently in the UI. Can always add it back later.\n\ndrop Message.authorTeamMemberId. This is replaced with an Identity struture.\n\n\nadd Identity structure as profile to Message, replacing TeamMember.id\n\n\nthe consequence is that the GET /teamMembers response can be limited to only real team members (no non-SCM users, no bot users).\n\n\nadd linkedProfiles (Identity[]) to TeamMember model in order to render linked profiles in Expert pages."}
{"comment": {"body": "This change is messy. Alternatives using primary member concept #5898.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Bug fix.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1175899974"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-1398961381", "body": ""}
{"comment": {"body": "@jeffrey-ng not clear to me why we created this `participants` array. It was not used.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1175900504"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-1400306972", "body": ""}
{"comment": {"body": "We probably do want to log whatever ID we have for the logged-in user here, so we can track who is logged in for debugging", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1176764571"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-1400435896", "body": ""}
{"comment": {"body": "There is no ID, so I'll log the email instead similar to other changes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1176850297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-1400466582", "body": ""}
{"comment": {"body": "I think we use this id to link to the dashboard team member view (i.e. if you click on a name in the message). If we just have the profile, how should the client find the team member id to link to? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1176870372"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-1400474176", "body": ""}
{"comment": {"body": "Makes me think we do need the identity id (to use to look up from the team members). I don't think it's any more unsafe than having thread ids and message ids on the ThreadInfoAggregate. They're all typed as strings but are necessary reference points?\r\n\r\nAlternatively I think this would be covered if we took the inverse API approach (i.e. `isPrimary`/`primaryTeamMember`) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1176875372"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-1400491094", "body": ""}
{"comment": {"body": "> Makes me think we do need the identity id\n\nYou mean teamMemberId?\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1176884500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-1400492998", "body": ""}
{"comment": {"body": "Meant identity.id on the identity so the client could look for the team member with the identity.id in its list of identity profiles \r\n\r\nOr like you said we keep the authorId property on the message (i.e. don't deprecate it). This feels a little weirder though; i.e. when someone signs up with Unblocked, would we have to backfill all their messages to include their primary team member id? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1176885784"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-1400497653", "body": ""}
{"comment": {"body": "I'll update to keep the teamMemberId. It would be non-null in the SCM case, null otherwise.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1176888971"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#pullrequestreview-1402668713", "body": ""}
{"comment": {"body": "@jeffrey-ng is this a bug?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5862#discussion_r1178309413"}}
{"title": "Add resource arn", "number": 5863, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5863", "body": "We need the resource arn to do list buckets."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5863#pullrequestreview-1398967520", "body": ""}
{"title": "Node downloader gh actions job", "number": 5864, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864", "body": "Added a script which checks for the latest node version and uploads it to the proper directories in S3 if we don't have a copy of it. I also added a Github action job to run the ingestion every night. \nThis script can be ran for a local machine as well. Later I will add an extra optional flag to specify explicit versions just in case if we ever have to ingest a specific binary. For more info check the comments at the top of the script. \nTested it and works as expected. node-v20.0.0-darwin-*.tar.gz was ingested by this script."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864#pullrequestreview-1399001539", "body": ""}
{"comment": {"body": "We probably want to use the LTS version, which unfortunately isn't easily indexable. Latest usually isn't widely adopted and can expose random bugs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864#discussion_r1175926705"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864#pullrequestreview-1399002473", "body": "Approved in principle but we'll probably need to manually pull in some LTS versions"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864#pullrequestreview-1399149571", "body": ""}
{"comment": {"body": "Let me see if I can introduce a file with desired versions list ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864#discussion_r1176018989"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864#pullrequestreview-1400037134", "body": ""}
{"comment": {"body": "This is the versions file where we can add the LTS versions or any version we like and the script will make sure the version specified here is uploaded to S3.\r\n\r\nIt doesn't support deleting from S3 which is for the best considering that would risk breaking our installer. These binaries are small enough that it would virtually cost us nothing keeping all of them on S3.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864#discussion_r1176592332"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864#pullrequestreview-1400047060", "body": ""}
{"comment": {"body": "Edits to the versions.json file should trigger this workflow. This way we don't have to wait for the nightly run to sync the data.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5864#discussion_r1176598791"}}
{"title": "Add Hub Installed onboarding status flag", "number": 5865, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5865", "body": "All clients can call this, and vscode/intellij/hub/dashboard get get the status flag"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5865#pullrequestreview-1400342714", "body": ""}
{"title": "Set gradle cache", "number": 5866, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5866"}
{"title": "Update separator colour", "number": 5867, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5867", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5867#pullrequestreview-1400308539", "body": ""}
{"title": "Move to a variant of caching using zstd", "number": 5868, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5868", "body": "Better caching performance"}
{"title": "Record IDE panel open/close events", "number": 5869, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5869", "body": "We record these events for the sidebar and insights panels:\n- IDE panel open\n- IDE panel remain open\n- IDE panel close\nHowever, only the open event is an indicator of engagement or activity."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5869#pullrequestreview-1400245902", "body": ""}
{"title": "Client mapped data streams", "number": 587, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/587"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/587#pullrequestreview-910848580", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/587#pullrequestreview-910849796", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/587#pullrequestreview-910853331", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/587#pullrequestreview-910853348", "body": "Overall looks good! I can imagine how this would make things a lot better.\nI have a branch right now that updates a lot of the sidebar data. I can integrate this in and wrap things up there."}
{"title": "Record metrics for internal users, but filter out for graphs", "number": 5870, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5870", "body": "Motivation is to test engagement for our users."}
{"comment": {"body": "Yeah I\u2019ll add tests.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5870#issuecomment-1522064656"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5870#pullrequestreview-1400244368", "body": "LGTM. I'd consider adding a test that passes a non-empty excludeIds list to the impacted UserEngagementStore functions just to be absolutely sure it works. \nAlthough it sounds like you just want to check that user engagement is working? Another option is to just log the event at the MetricsService level and don't save to the db?"}
{"title": "Admin: Improvements", "number": 5871, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5871", "body": "Admin: Toggle descriptions are optional\nAdmin: Remove isBot toggle because this is managed by SCM sync"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5871#pullrequestreview-1400040203", "body": ""}
{"comment": {"body": "Thoughts @davidkwlam. This flag is obliterated by SCM sync.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5871#discussion_r1176594236"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5871#pullrequestreview-1400224380", "body": ""}
{"comment": {"body": "\ud83d\udc4d thanks for cleaning up. We have `toggleIgnoreThreads` which we can set for identities that are not marked as bots but are actually bots (like https://admin.prod.getunblocked.com/teams/90dab8a5-e88a-4490-b8ec-a201a555b351/members/a10ccb34-d4cd-4bd5-a7cc-77aa7ec8488c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5871#discussion_r1176709059"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5871#pullrequestreview-1400224772", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5871#pullrequestreview-1400224956"}
{"title": "Fix findInstallationsAndRepos API 500s", "number": 5872, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5872", "body": "Some developer opened unblocked with an enterprise SCM repo.\nNot enabled yet, so it's failing for 501."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5872#pullrequestreview-1400231719", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5872#pullrequestreview-1400359788", "body": ""}
{"title": "Add ability to cleanup cache", "number": 5873, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5873"}
{"title": "Fix test from #5872", "number": 5874, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5874"}
{"title": "Fix person ID bug affecting PR mutation operations", "number": 5875, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5875", "body": "This is used to toggle mutation operations on pull request messages,\nlike edit or delete message, gated by the author of the message.\nThe bug was that the PR would never be treated as being owned by the\nauthorized user, and therefore mutation operation would always be hidden."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5875#pullrequestreview-1400603534", "body": ""}
{"title": "[UNB-1199] update tab styling to match native", "number": 5876, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5876", "body": "Update Tab styling in IntelliJ\nUpdated context to inject webview focus state into webviews.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5876#pullrequestreview-1400477333", "body": ""}
{"comment": {"body": "This is blocked by https://github.com/NextChapterSoftware/unblocked/pull/5856", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5876#discussion_r1176877045"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5876#pullrequestreview-1402723279", "body": ""}
{"comment": {"body": "I think maybe the focused state should be a context value outside of WebviewContext -- that way the base components can pull that state, and we don't need IDE-specific component versions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5876#discussion_r1178339983"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5876#pullrequestreview-1402724353", "body": ""}
{"comment": {"body": "... or maybe there is a pure CSS solution here?  Set a CSS class (focused/unfocused) at the root of the view, and then the per-client styling can use it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5876#discussion_r1178340772"}}
{"title": "Get all uninvited team members when sociallyConnected == false", "number": 5877, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5877"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5877#pullrequestreview-**********"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5877#pullrequestreview-**********", "body": ""}
{"comment": {"body": "You need to filter the SCM members only. This collection will contain Slack/JIRA etc members.\r\n\r\nYou could use the `Team.provider` to filter.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5877#discussion_r1176908085"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5877#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Thanks for saving my bacon \ud83d\ude4f", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5877#discussion_r1176926108"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5877#pullrequestreview-**********", "body": ""}
{"comment": {"body": "ah, I think you saved my ass with this PR!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5877#discussion_r1176927887"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5877#pullrequestreview-**********", "body": ""}
{"title": "Implement Integrate dashboard topic", "number": 5878, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5878", "body": "IntelliJ will now open topics in dashboard.\nDid some cleanup to remove unused code."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5878#pullrequestreview-1400624157", "body": ""}
{"title": "Don't throttle all metrics", "number": 5879, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5879", "body": "We were throttling all metrics so that we would record at most one event\nof a specific type per user per 3 hours period. This should've been just\nfor these continuously sampled metrics:\n\nIdeSidebarViewed\nIdeInsightsViewed"}
{"title": "Update sidebar data", "number": 588, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588", "body": "Update Sidebar's state management and include authstore stream."}
{"comment": {"body": "\ud83c\udf89 looks good!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#issuecomment-1071086359"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#pullrequestreview-912302321", "body": ""}
{"comment": {"body": "Where are you thinking of taking this?  It does seem a bit strange to have zustand stores *and* streams for the same data, but maybe it's fine.\r\n\r\nWe could make this a totally generic function, that given a zustand store, returns a stream for that store, then it can be reused elsewhere.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#discussion_r828467895"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#pullrequestreview-912325687", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#pullrequestreview-912329535", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#pullrequestreview-912332014", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#pullrequestreview-912336934", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#pullrequestreview-912338232", "body": ""}
{"comment": {"body": "Haven't fully thought through if Zustand can go away. I think it still has it's still useful for sync data access but maybe everything should be async??\r\n\r\nI think we can revisit this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#discussion_r828494646"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#pullrequestreview-912342760", "body": ""}
{"comment": {"body": "Yeah I'm fine deferring this, just wondering what you were thinking.\r\n\r\nIf the main benefit it's providing is synchronous access, we can easily build a helper that holds the most recently-published data in a synchronously-available map, that we would attach as a sink to the stream.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#discussion_r828498190"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#pullrequestreview-912350488", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/588#pullrequestreview-913376321", "body": ""}
{"title": "Surface user descriptions in the dashboard", "number": 5880, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5880", "body": ""}
{"comment": {"body": "With live data:\r\n<img width=\"477\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/234434787-e023d14a-5f62-4095-8828-4b52d6e8465c.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5880#issuecomment-1522570796"}}
{"comment": {"body": "(Note that this UI is gated by the same flag as the topic descriptions)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5880#issuecomment-1522571109"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5880#pullrequestreview-1404481852", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5880#pullrequestreview-1404483633", "body": ""}
{"title": "Add linear graphql", "number": 5881, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5881", "body": "Adds linear graphql and generalizes some graphql common functionality."}
{"title": "Sidebar should load in bottom left tool window section", "number": 5882, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882", "body": "Updates main sidebar to be declaratively setup. This allows us to use the \"secondary\" property which places the sidebar into the bottom left.\n"}
{"comment": {"body": "Awesome! Does this work for the new UI too? This is probably one worth checking if it does the right thing in the new UI.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882#issuecomment-1522418530"}}
{"comment": {"body": "> Awesome! Does this work for the new UI too? This is probably one worth checking if it does the right thing in the new UI.\r\n\r\nYup. To be cleaer, this is what *bottom-left* means in the new UI.\r\nThere's a faint divider above the Unblocked Icon. Tool Windows in this section appear in the bottom half of the left section.\r\n\r\n<img width=\"659\" alt=\"CleanShot 2023-04-25 at 15 03 52@2x\" src=\"https://user-images.githubusercontent.com/1553313/234414562-628fd5d3-b418-4ee4-a852-9c5e01136be1.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882#issuecomment-1522479497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882#pullrequestreview-1400716167", "body": ""}
{"comment": {"body": "All this logic was moved to SidebarToolWindowService", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882#discussion_r1177023853"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882#pullrequestreview-1402713547", "body": ""}
{"comment": {"body": "So this only displays on startup, until we resolve the initial auth/general state?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882#discussion_r1178334673"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882#pullrequestreview-1402714557", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882#pullrequestreview-1402743177", "body": ""}
{"comment": {"body": "Correct. I don't think there's a need for this \"general\" loading state outside of the tool window initialization.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5882#discussion_r1178350896"}}
{"title": "Ci dependabot fixes", "number": 5883, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5883"}
{"title": "Temporarily log engagement scores", "number": 5884, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5884"}
{"title": "Update java docs", "number": 5885, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5885"}
{"title": "Increase java heap memory", "number": 5886, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5886"}
{"title": "UNB-1203: Experts: Display TeamMember.description", "number": 5887, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5887", "body": "Add changes to com.nextchaptersoftware.api.models.converters for the API."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5887#pullrequestreview-1400838966", "body": "lgtm but deferring to @davidkwlam to be sure"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5887#pullrequestreview-1400853364", "body": ""}
{"title": "Improve logging and stuff", "number": 5888, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5888"}
{"title": "Remove logging", "number": 5889, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5889"}
{"comment": {"body": "@pwerry @kaych You can view invites sent and dismissed in admin console on the **_Team_** and **_Team Member_** pages. It looks like this:\r\n\r\n<img width=\"1417\" alt=\"Screenshot 2023-04-25 at 16 41 23\" src=\"https://user-images.githubusercontent.com/1798345/234430779-1f66ca20-8228-405b-a69f-fedd5ed44e7f.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5889#issuecomment-1522549312"}}
{"comment": {"body": "You can also reset:\r\n\r\n<img width=\"285\" alt=\"Screenshot 2023-04-25 at 16 42 44\" src=\"https://user-images.githubusercontent.com/1798345/234430926-5a1ba643-f75b-46e5-ad71-0f21a186d976.png\">\r\n\r\n<img width=\"278\" alt=\"Screenshot 2023-04-25 at 16 42 59\" src=\"https://user-images.githubusercontent.com/1798345/234430960-bc6eaa2b-3f8b-436d-bbb1-1f5e8356b813.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5889#issuecomment-1522549809"}}
{"comment": {"body": "Yeah ignore - it's working now and my theory is that Kay was hitting an old dev api-service node. We weren't trusting our own eyes when we looked at the admin console lists \ud83d\ude05", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5889#issuecomment-1522550997"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5889#pullrequestreview-1400903874", "body": "Did you figure it out?"}
{"title": "Move shared libs to temporary core project", "number": 589, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/589", "body": "Changes:\n- Moved all the shared code (except plugins packageto a new Gradle project at/projects/core- Added a newbuild.gradle.ktsfile for core project and moved necessary dependency and build logic to it \n- Added the new project as a subproject to Gradle settings \n- Importedcoreproject inapiserviceas a dependency \n- Moved resources + submodules formapiservice` to new core project \nOur plan is to slowly start taking packages out of core and implement the structure described here: \nNotes: \n- I wasn't able to move the tests to new core project. All test packages work fine but we end up stalling on DB operator extension tests \n- My subsequent PR(s) will move APIservice to /project/services \nI did a local run of ./gradlew check and all tests passed"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/589#pullrequestreview-910865954", "body": "Overall this looks it's going in the right direction. Let's work together to figure out the testing issues"}
{"comment": {"body": "This is a smell for me - if we're having to up-reference files then it feels like something that should live in the gradle file a level up. Otherwise the jar should be pulled down if it's specific to this service", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/589#discussion_r827441373"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/589#pullrequestreview-910894589", "body": ""}
{"title": "[unb-1193] style issues with get started unblocked", "number": 5890, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5890", "body": "Update styling with GetStarted Toast in IntelliJ\n"}
{"comment": {"body": "Button is styled as a primary. Based on designs, that's not what we want?\r\n\r\nIn VSCode, we have the continue button styled as Primary. In IntelliJ, I don't think that's the case? If so, we either need to pass different button styles into the entire sidebar based on platform... Or we manually override with css.\r\n\r\nThoughts?\r\n\r\nHere's the design for reference. (Will be updating text)\r\n<img width=\"434\" alt=\"CleanShot 2023-04-25 at 17 06 17@2x\" src=\"https://user-images.githubusercontent.com/1553313/234433833-61e800dd-22b5-4210-b01f-c0827762bfd5.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5890#issuecomment-1522565666"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5890#pullrequestreview-1402707021", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5890#pullrequestreview-1402707869", "body": ""}
{"comment": {"body": "Is this value dynamic?  If it's static (per-IDE?) it feels like something that should be defined in the views, not in the extension/agent...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5890#discussion_r1178331794"}}
{"title": "Send welcome emails again", "number": 5891, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5891"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5891#pullrequestreview-1400925225", "body": ""}
{"title": "Added TopicExpertStore().findTopicExpertise", "number": 5892, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5892", "body": "and used the results in the ExpertSummaryService"}
{"comment": {"body": "PineconeInsightQueryService doesn't support topics yet, but passing them in. Change the expert service to just pass in the username. We can make this query better after we get this wired up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5892#issuecomment-1524254337"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5892#pullrequestreview-1404458780", "body": ""}
{"comment": {"body": "this is only going to return insights where the username is in the message body, I think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5892#discussion_r1179426124"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5892#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5892#pullrequestreview-**********", "body": ""}
{"comment": {"body": "this is using the semantic search provider and I think we are embedding the usernames in the search. seems like it is working. we can revisit when we add support for topics too (the semantic search provider doesn't support them yet).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5892#discussion_r1179432436"}}
{"title": "Invitees must be members of the team", "number": 5893, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5893", "body": "More efficient query\nNo need to check that is SCM provider, we just need isCurrentMember"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5893#pullrequestreview-**********", "body": ""}
{"title": "Revert inadvertent HealthCheck change in #5610", "number": 5894, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5894"}
{"title": "Try more heap changes", "number": 5895, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5895"}
{"title": "Revert", "number": 5896, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5896", "body": "Revert \"Try more heap changes (#5895)\"\nTry again"}
{"title": "Fix code-generated responses that have no return type", "number": 5897, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5897", "body": "The lack of a return type means that ktor is unable to determine the response\ncontent type, and the response length. This manifests as a 406, because the\n\"unknown\" response type cannot possibly match any acceptable content specified\nby a client.\nThis addresses API operations that have no return type API, typically those are\noperations that return 202, 204, and sometimes 200.\nWith this fix it doesnt matter what Accept header is passed in the request."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5897#pullrequestreview-1401273150", "body": ""}
{"title": "Introduce primary member", "number": 5898, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5898", "body": "A primary member is a direct member of a team.\nKey concepts\n\n\nPrimary member\n  A direct member of the team. If the team is a GitHub team, then these will be the GitHub members of that team only. Confusingly, Slack or JIRA identities are also represented as team member API models for legacy reasons; these non-primary team members may have an optional primary member.\n\n\nCurrent member\n  A primary member that is currently part of the team, as opposed to a past member.\n\n\nPast member\n  A primary member that is not currently part of the team, as opposed to a current member.\n\n\nHas Account\n  A primary member that has an Unblocked account. This means that they have signed into the service at least once.\n\n\nMotivation and alternatives\n\n\n\n5862"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5898#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Do we plan on deprecating this as well? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5898#discussion_r1178153076"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5898#pullrequestreview-**********", "body": ""}
{"comment": {"body": "> This takes precedence over the `description` field.\r\n\r\nNot sure if this has any meaning in terms of the API. Precedence is only relevant to the client I think? \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5898#discussion_r1178155152"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5898#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Correct, was just trying to provide context. If it's misleading I can remove.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5898#discussion_r1178238994"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5898#pullrequestreview-1402617587", "body": ""}
{"comment": {"body": "Possibly later, but not now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5898#discussion_r1178276961"}}
{"title": "Update spacing in tree height", "number": 5899, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5899", "body": "Bumped up padding by 2px in each direction to better match existing rows in IntelliJ\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5899#pullrequestreview-1402452383", "body": ""}
{"title": "Add theming prototype", "number": 59, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/59", "body": "Sass implementation of theming. Essentially a color theme map in theme.scss is defined which then gets used in the scss code when using themed fn within the mixin:\n@include themeColors() {\n    color: themed($colorAlias);\n}\nThe idea would be that this code would replace most (all?) instances of where we reference color values. Note that the fn has to reference the exact key from the theme map. There are ways to write more mixins to abstract this concept but I wasn't sure if necessary.\nThis is more of a proof of concept for how this could work. There's no dark theme defined yet because there's no lib for it. Storybook seems to have some support for custom theming (). Some details still need to be hashed out."}
{"comment": {"body": "An alternative to this would be to use css variables directly.\r\nThat may get us \"live\" dark mode (aka if someone switches from light to dark, no need to refresh) but I don't think that's necessary and probably adds more complexity.\r\n\r\nOverall I think this looks good?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/59#issuecomment-1015690848"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/59#pullrequestreview-855794754", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/59#pullrequestreview-855795161", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/59#pullrequestreview-855796510", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/59#pullrequestreview-855810887", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/59#pullrequestreview-857097103", "body": ""}
{"title": "Adds scm verified email list to IdentityModel", "number": 590, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/590", "body": "Next up - implementing SHA236 hash function to transform the emails for the API response"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/590#pullrequestreview-910898731", "body": ""}
{"comment": {"body": "Super dumb question(s): why are these emails needed and how do we get them?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/590#discussion_r827466940"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/590#pullrequestreview-910900979", "body": ""}
{"comment": {"body": " Ah second question is answered in the code changes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/590#discussion_r827467571"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/590#pullrequestreview-910901970", "body": ""}
{"comment": {"body": "They're retrieved from the GitHub user /emails endpoint, and we need them for 2 reasons:\r\n1. Notifications and alerts\r\n2. Connecting local commits to Unblocked users", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/590#discussion_r827468317"}}
{"title": "Reduce parallelization", "number": 5900, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5900"}
{"title": "Add Jira as a new provider type", "number": 5901, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5901", "body": "We'll be creating identities for team members as part of the work to ingest Jira issues."}
{"comment": {"body": "Tests needed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5901#issuecomment-**********"}}
{"comment": {"body": "@pwerry I could? I'm just adding an enum, there's no logic except for the converter between the API and backend version.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5901#issuecomment-**********"}}
{"comment": {"body": "> @pwerry I could? I'm just adding an enum, there's no logic except for the converter between the API and backend version.\r\n\r\nI c - it looks like there's a handful of tests that check for type conversions, provider activation, identity providers, and provider capabilities. I'm not fussed about those tests because they seem pretty low value. Up to you!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5901#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5901#pullrequestreview-**********", "body": ""}
{"title": "Update spacing in explorer insights header", "number": 5902, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5902", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5902#pullrequestreview-**********", "body": ""}
{"title": "Software is hard. Quotes are hard. FML", "number": 5903, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5903"}
{"comment": {"body": "Ci is pain, that's for sure...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5903#issuecomment-**********"}}
{"comment": {"body": "Reason no 8099 why YAML sucks ass. Not even simple syntax checking is possible.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5903#issuecomment-1524063162"}}
{"title": "Add JiraConfig and OAuth 2.0 integration secrets", "number": 5904, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5904", "body": "The OAuth 2.0 Integrations can be found at  (login <EMAIL> in shared 1Password vault)."}
{"title": "Get integration installations", "number": 5905, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905", "body": "Add API to get the installation states for 3rd party integrations (i.e. Slack, JIRA). \nThis would be used to populate this onboarding UI as well as the settings UIs:\n\nProperties:\n* isInstalled - whether the integration has been installed with Unblocked\n* installUrl - url to install integration with Unblocked\n* isUserAuthed - whether the user has authed with this integration \n* authUrl - url to auth with the integration for identity resolution\n* provider - which Provider this is applicable to\nWe would also need an additional flag on the Person model to determine whether or not to show this connection UI at all. The designs show an affordance for the user the ability to skip this step, at which point we should not show the user this UI again, even if they haven't connected anything. (Presumably we would have a separate but similar settings route for the user to auth)."}
{"comment": {"body": "@richiebres the `InstallationV2` model seems like it's very SCM-specific (i.e. it reads more like an `ScmInstallation`). I suppose the new endpoint was a way to decouple these things instead of overloading one endpoint ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905#issuecomment-**********"}}
{"comment": {"body": "What do `fullPath` or `enterpriseProviderId` mean for 3rd party integrations? \r\n\r\nI also don't think user specific auth states have much meaning for scm installations", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905#issuecomment-**********"}}
{"comment": {"body": "See description. We need a way to keep track of whether the individual user has oauthed via Slack/Jira/etc (ie separate from the app installation) to show certain connection UIs. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905#issuecomment-**********"}}
{"comment": {"body": "Closing after in-person discussion. Will open up a new PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905#pullrequestreview-**********", "body": "Not sure we need a new endpoint. Most of what we need is in listInstallations endpoint. I would try to find out what is needed to modify this to make it work."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905#pullrequestreview-1402891798", "body": ""}
{"comment": {"body": "Just a heads up: for Jira there's just the `authUrl` for connecting the user identity. The app itself is installed through the marketplace, which we want to have happen after they connect their identity. The url for that depends on the Jira site that the user oauths against, so we won't know the `installUrl` until after they oauth (and the client doesn't really need to know, since the server will provide a redirect url to that marketplace listing). Lmk if that doesn't make sense :).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905#discussion_r1178446004"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905#pullrequestreview-1402944459", "body": ""}
{"comment": {"body": "@benedict-jw you should probably be aware of this ...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5905#discussion_r1178464437"}}
{"title": "Remove trailing comma from manifest json", "number": 5906, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5906"}
{"title": "Filter icon only in explorer insights tab", "number": 5907, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5907", "body": "Update SidebarToolWindowService to only show filter icon on explorer insights tab.\n\nAlso Fixes UNB-1118\n"}
{"comment": {"body": "Fixes UNB-1118\r\n\r\nhttps://user-images.githubusercontent.com/1553313/234719191-33a91698-8fa0-40d5-9d12-d08f7bc2e706.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5907#issuecomment-1524131880"}}
{"title": "Add node info to VersionInfo API response", "number": 5908, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5908"}
{"title": "Fix jira secret", "number": 5909, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5909", "body": "Should be JIRA_CLIENT_SECRET"}
{"title": "Adds SHA 256 hashing function", "number": 591, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/591"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/591#pullrequestreview-910903691", "body": ""}
{"title": "Test some changes", "number": 5910, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5910"}
{"title": "IntelliJ tries to launch node from Hub path, falls back to system node if fails", "number": 5911, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#pullrequestreview-1404426869", "body": ""}
{"comment": {"body": "Not urgent, but we probably will need to think through alternatives to this.  I'm not sure what that would look like, but this feels super fragile.\r\n\r\nIs there a command line we can run to get the container folder?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#discussion_r1179402716"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#pullrequestreview-1404427890", "body": ""}
{"comment": {"body": "We should maybe only do this on dev builds?  Running whatever node happens to be installed on customer machines would be a rich source of random hard to understand bugs... again, not urgent, something for later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#discussion_r1179403573"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#pullrequestreview-1404427986", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#pullrequestreview-1404447516", "body": ""}
{"comment": {"body": "Curious about why you think this is fragile? No command that I know of to get the container directory of an app. Using commands like `mdfind` to construct a container path would be equally fragile. \r\n\r\nApp containers are ubiquitous on macOS, and always exist at the same location. The only way this _wouldn't_ work is if the user moved the container manually, or manipulated the OS in some way to be entirely non-standard. I don't think we need to concern ourselves with this use-case for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#discussion_r1179418390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#pullrequestreview-1404449361", "body": ""}
{"comment": {"body": "The thinking here is that attempting to run at least _some_ node instance is better than a hard fail, but maybe that's the wrong approach and we should fast-fail instead?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#discussion_r1179419732"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#pullrequestreview-1404450641", "body": ""}
{"comment": {"body": "It's just not best practices to build assumptions about OS behaviour like this into an app.  Not an urgent issue at all.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5911#discussion_r1179420509"}}
{"title": "Download and install node if needed", "number": 5912, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5912", "body": "Summary\nThere are two parts to this PR, Node download and install, and a slight refactor of how the download and install mechanism works generally for all plugins\nNode Download and Install\n\nNode is downloaded if it doesn't exist to ~/Library/Containers/com.nextchatpersoftware.UnblockedHub/Data/Documents\nThe installer is run, which unpacks node to ~/Library/Containers/com.nextchatpersoftware.UnblockedHub/Data/Library/Application Support/Node\nWe shell out to xattr to remove quarantine flags on node binary\n\nStep (3) will require some validation via IDE debugging (it works on my machine, but unsure if it will work for others)\nRefactor\nThis PR also slightly re-works the plugin update mechanism in the following ways:\n1. Plugin install now only happens if you have completed onboarding. Downloading still happens either way. This is a necessary component of the new onboarding flow, where plugin installation doesn't happen until much later in the flow.\n2. The logic that determines whether a plugin download is needed now looks to the download version file instead of the installed version file. \n3. Launching the installer now depends on whether there is a download/installed version mismatch. In hindsight this is what we always should have done."}
{"title": "Helm charts", "number": 5913, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5913"}
{"title": "Add admin console pages", "number": 5914, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5914"}
{"title": "Fix linear issue paging", "number": 5915, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5915"}
{"title": "Fix indexing", "number": 5916, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5916"}
{"title": "DataService", "number": 5917, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5917", "body": "Data service\ndata"}
{"title": "Format ktlint on save", "number": 5918, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5918", "body": "I don't think there is any reason not to?"}
{"comment": {"body": "> only downside I can think of is that it could be too slow, maybe? but if any problems, we can revert later.\r\n\r\nI've used this for awhile and never noticed any performance issues.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5918#issuecomment-1526021021"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5918#pullrequestreview-1404437114", "body": ""}
{"comment": {"body": "This diff looks a bit funny, but these are the project settings that produce it.  I'm guessing the defaults are enbleKtlint=true, treatAsErrors=true, and lintAfterReformat=true ?\r\n\r\n<img width=\"1012\" alt=\"Screenshot 2023-04-27 at 9 13 07 AM\" src=\"https://user-images.githubusercontent.com/2133518/234925301-6e8f02fe-5401-48ea-b583-2d8b7c1ae02d.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5918#discussion_r1179411364"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5918#pullrequestreview-1404442423", "body": "only downside I can think of is that it could be too slow, maybe? but if any problems, we can revert later."}
{"title": "Add service deployment", "number": 5919, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5919"}
{"title": "Add hashed emails to teammember api", "number": 592, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592"}
{"comment": {"body": "So if the client wants to map a git email to a TeamMember, we need to do the following:\r\n1. Locally hash git email\r\n2. Load all team members for a team\r\n3. Iterate through each member\r\n4. Iterate through each member's list of hashed emails and match against locally hashed email\r\n\r\n??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#issuecomment-1069309644"}}
{"comment": {"body": "> So if the client wants to map a git email to a TeamMember, we need to do the following:\r\n> \r\n> 1. Locally hash git email\r\n> 2. Load all team members for a team\r\n> 3. Iterate through each member\r\n> 4. Iterate through each member's list of hashed emails and match against locally hashed email\r\n> \r\n> ??\r\n\r\nYes.  The assumption is that clients will be syncing all the TeamMember data anyways (to render thread lists, etc), so the network cost of this is zero.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#issuecomment-1069322402"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#pullrequestreview-910961703", "body": ""}
{"comment": {"body": "1 liner :) @davidkwlam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#discussion_r827517382"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#pullrequestreview-912295382", "body": ""}
{"comment": {"body": "Should this be a required field?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#discussion_r828463504"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#pullrequestreview-912295593", "body": ""}
{"comment": {"body": "I think we should always have at least one email", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#discussion_r828463664"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#pullrequestreview-912302818", "body": ""}
{"comment": {"body": "I can make it required, but the client will need to support this being empty. It's entirely possible that the user chooses not to publish any emails for their user (think SSO or other identity provider)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#discussion_r828468309"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/592#pullrequestreview-912304016", "body": ""}
{"title": "Update glue etl processing", "number": 5920, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5920"}
{"title": "Fix issue with webview visibility in IntelliJ", "number": 5921, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5921", "body": "GRPC from Kotlin -> TS was having issues with boolean values === false.\nThey would be stripped out and TS client stream dies due to missing stream...\nAlso fixes issue with missing tool window event type."}
{"title": "Add code InlineElement", "number": 593, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/593", "body": "Adds support for code InlineElements"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/593#pullrequestreview-911927133", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/593#pullrequestreview-911932164", "body": "SingleTick?"}
{"title": "Fetch and display anchor sourcemark on vscode", "number": 594, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/594", "body": "* Currently using our own API to fetch the anchor SM -- this will be replaced by the local SM db once that is set up\n* Will do web in next PR\n* Removed ability to createThread from the temp view on the web since we can do it on vscode now and the args didn't make sense anymore (I'll remove this view altogether in the web PR)\n* READ: There's additional API work needed to save the language id of the code snippet via our API (I'll add this in a separate PR) so that other clients know how to style/syntax highlight the code snippets\n    * Note: we're inferring from the file extension for now and should probably still do this as a backup"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/594#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Aren't these just `path.basename` and `path.extname` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/594#discussion_r828460080"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/594#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Hmm I guess we want to use these outside of the node environment...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/594#discussion_r828460635"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/594#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/594#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/594#pullrequestreview-912345156", "body": ""}
{"comment": {"body": "Yeah that was the idea. I can use path and pass it in for now and revisit when I dig into other clients ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/594#discussion_r828500029"}}
{"title": "Client mapped data streams", "number": 595, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/595"}
{"comment": {"body": "Force merging due to CI being down.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/595#issuecomment-1069405996"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/595#pullrequestreview-912005991", "body": ""}
{"title": "Setup Contributors in KnowledgeCreation", "number": 596, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596", "body": "\nPulled in Git code from OctoberDemo to fetch log & blame.\nCurrently parsing Git data and generating GitContributors for UI.\nTODO\nMap git emails to unblocked identities."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-912290942", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-912292132", "body": ""}
{"comment": {"body": "can we extract this into a separate helper? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#discussion_r828461122"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-912292626", "body": ""}
{"comment": {"body": "in shared/ presumably?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#discussion_r828461485"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-912294301", "body": ""}
{"comment": {"body": "Ah cool that we got this working... this GitContributor shape was built temporarily, so if the shape doesn't make sense feel free to muck around with it or replace it entirely with something that works better", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#discussion_r828462746"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-912296431", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-912321942", "body": ""}
{"comment": {"body": "I'd prefer to understand *where* and *how* we plan on using this before refactoring it out.\r\naka when we eventually need to use this in other locations, will refactor then as there's quite a bit of specific VSCode / local logic.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#discussion_r828482162"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-912322435", "body": ""}
{"comment": {"body": "Think it was okay since the model was quite representative of the view. Liked how there wasn't much logic in the view.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#discussion_r828482514"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-912335924", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-913309269", "body": ""}
{"comment": {"body": "It might make this simpler/shorter if you just edit the bits of the object inline instead of recreating on every iteration of the loop?  It'd be more efficient as well...\r\n\r\n```\r\nexistingPerson.lastCommittedAt = (a > b) ? a : b;\r\nexistingPerson.totalCommits = existingPerson.totalCommits + 1\r\n```\r\n\r\nand you'd need to set the lastContribution person outside of the loop:\r\n\r\n```\r\nbaseContributorsMap.get(blameEmail)?.hasLatestContribution = true\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#discussion_r829235313"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-913310215", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-913311906", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-913340409", "body": ""}
{"comment": {"body": "On that last one, don't think you can assign on an optional property like that. But I get the point. Updating.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#discussion_r829256447"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#pullrequestreview-913340410", "body": ""}
{"comment": {"body": "On that last one, don't think you can assign on an optional property like that. But I get the point. Updating.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/596#discussion_r829256448"}}
{"title": "Make image an InlineElement", "number": 597, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597", "body": "This makes it more inline with how Markdown handles images "}
{"comment": {"body": "Sample of inline image:\r\n\r\n\r\n<img width=\"849\" alt=\"CleanShot 2022-03-16 at 14 46 52@2x\" src=\"https://user-images.githubusercontent.com/1553313/158696607-def33d78-c1cc-41ac-be3c-29dc41cc5b62.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597#issuecomment-1069667877"}}
{"comment": {"body": "The goal for our own editor was never to support inline images, because it leads to somewhat goofy editor behaviour like shown above... hmmm", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597#issuecomment-1069669882"}}
{"comment": {"body": "> The goal for our own editor was never to support inline images, because it leads to somewhat goofy editor behaviour like shown above... hmmm\r\n\r\nAgreed but Github *does* support this. Therefore, we need to at least be able to render these inline images.\r\n\r\nI still think we can enforce it so that *our* editor cannot make inline images.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597#issuecomment-1069679600"}}
{"comment": {"body": "> > The goal for our own editor was never to support inline images, because it leads to somewhat goofy editor behaviour like shown above... hmmm\r\n> \r\n> Agreed but Github _does_ support this. Therefore, we need to at least be able to render these inline images.\r\n> \r\n> I still think we can enforce it so that _our_ editor cannot make inline images.\r\n\r\nSure, but then I would expect `ParagraphInlineElement` to not contain an Image option.  If we ever get into a scenario where we edit a document containing an inline image, we'd just translate it to a block-level image.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597#issuecomment-1069682453"}}
{"comment": {"body": "Agree, inline image elements look super goofy so it makes sense if we didn't have that in our editor. \r\n\r\nIf we go with the top-level image block route, we'll need to decide on how to handle inline images when ingesting PR comments. Dealing with paragraphs with inline images seems easy enough (split into multiple paragraph blocks).\r\n\r\nLists are a little trickier...\r\n\r\n1. This is a list\r\n2. This list item has ![](https://media.giphy.com/media/d9ltR6odFmQsE/giphy.gif) an inline image\r\n3. This is the last list item", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597#issuecomment-1069723788"}}
{"comment": {"body": "FWIW Slack renders inline images as links and appends them to the bottom of the message:\r\n\r\n<img width=\"1183\" alt=\"CleanShot 2022-03-16 at 16 10 43@2x\" src=\"https://user-images.githubusercontent.com/1924615/158706088-c318bbcc-f1be-4c9e-adef-5e50d97962b8.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597#issuecomment-1069730316"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597#pullrequestreview-912292965", "body": ""}
{"comment": {"body": "Changing numbers here is a no-no, but we don't have customers right now :).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597#discussion_r828461710"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/597#pullrequestreview-913363141", "body": ""}
{"title": "Creating Utils gradle project", "number": 598, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/598", "body": "Moved nowWithMicrosecondPrecision from db.common to utils package ( a metric ton of imports had to be changed)\nMoved security, http and utils packages to a new project\nAdded a new trimmed down version of build.gradle.kts for the new project\nImported the project in core build.gradle and settings.gradle\n\nVerified locally and all tests passed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/598#pullrequestreview-912304861", "body": ""}
{"title": "Temporarily disable throwAPIError", "number": 599, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/599", "body": "Temp disable to unblock.\nDue to "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/599#pullrequestreview-912388581", "body": ""}
{"comment": {"body": "@kaych I think there's a bug with anchor resolution here. Throwing a 404 and prevents main page from rendering.\r\n\r\nCurrently working around.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/599#discussion_r828533825"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/599#pullrequestreview-912408301", "body": ""}
{"title": "Vscode integrations", "number": 6, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/6"}
{"title": "Workflows run when workflow definitions change", "number": 60, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/60", "body": "So that we can test #56"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/60#pullrequestreview-855839100", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/60#pullrequestreview-855839527", "body": ""}
{"title": "Update LatestSourcePointReq to include isOriginal bool", "number": 600, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/600", "body": "The web dashboard client needs to be able to fetch the latest sourcepoint for an anchorSourceMarkId, but it doesn't have any information about commit hashes or file hashes. This change adds a new arg to the request body to request the original source point, which is what the web dashboard needs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/600#pullrequestreview-912425458", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/600#pullrequestreview-912624779", "body": ""}
{"title": "Revert \"Add code InlineElement (#593)\"", "number": 601, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/601", "body": "I just realized FormattedText has an is_code field which covers the inline code case. Maybe let's back this change out from yesterday and just use FormattedText for inline code elements?"}
{"comment": {"body": "It's possible to have bold/italic code elements so that's a strong argument for removing this https://spec.commonmark.org/dingus/?text=hey%20%60there%60%0A%0A_hey%20%60there%60_%0A%0A__hey%20%60there%60__", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/601#issuecomment-1071116018"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/601#pullrequestreview-913416080", "body": ""}
{"title": "Extract TeamMember from context", "number": 602, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/602", "body": "This is the final bit of logic that ensures we're dealing with the correct team"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/602#pullrequestreview-913434167", "body": ""}
{"title": "Fetch and render threads on web", "number": 603, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603", "body": "only show 'All Conversations' right now in Navigator\nfetch and display the anchor sourcemark of the first message (anchor message)\nstream in and display participant data\nlink from the conversation view to the thread view, fetch and display anchor sourcemark in the thread view\n\n\nNOTES:\n* I kept the mocked out Chat view for now\n* There needs to be ensuing work to fix the stories which only display the mocked out views (how do we mock out the data stores??) -- I think this can be done in the next PR?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603#pullrequestreview-913552771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603#pullrequestreview-913675157", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603#pullrequestreview-913676480", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603#pullrequestreview-913678019", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603#pullrequestreview-913739815", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603#pullrequestreview-913751519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603#pullrequestreview-913752291", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603#pullrequestreview-913753318", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/603#pullrequestreview-913755139", "body": "Ready to go once we address the stream subscription / hook issues."}
{"title": "Fix small styling issues", "number": 604, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/604", "body": "Scroll discussion thread in VSCode\nScroll discussion thread in Web\nMaximum height for code blocks in ask a question\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/604#pullrequestreview-913646833", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/604#pullrequestreview-913757856", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/604#pullrequestreview-913758113", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/604#pullrequestreview-913765169", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/604#pullrequestreview-913765476", "body": ""}
{"title": "Create more subprojects", "number": 605, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/605", "body": "What the heck is this ? I have taken out individual packages from core and added them to new Gradle projects as described in this doc \nNew Gradle projects:\nClients Project:\n  - Redis\n  - SCM\nModels Project: \n  - Db \n  - models \nLibs Project:\n  - Pringestion \n  - Sourcemarks\nUtils Project:\n - config \n - logs\n - http (some exception stuff that are global)\n - security \n - utils (date time stuff)\nTo do this I had to break the dependency between Sourcemarks -> db and scm-> db. Basically I had to change those packages so we could eliminate some circular dependencies (not entirely circular but it becomes circular when we separate projects). \nHere's what I changed in the Kotlin code:\n- I moved SCM Provider enum class to DB package in RepoModel.kt (like we have done for video stuff)\n- Moved SourceMarkVisibility and SourceMarkLifecycle to RepoModel.kt in db package \n- Moved the Hash class out of Hash class out of source marks and added it to utils package (it was used all over the place). \n- I also updated all imports related to changes above accordingly \nHere's what changed in Gradle config files:\n- Removed unused dependencies in each new project \n- Removed OpenAPI, GraphQL and Proto generations wherever they were not needed \n- Added necessary inter-project dependencies \nFinally I moved Graphql submodule to it's new and hopefully final location under clients Gradle project. \n```\nOn your next pull from master once this is merged\ngit submodule update --init --recursive\nrm -r projects/core\n```\nI have tested this locally and all integration tests passed but I would appreciate it if someone else could give it a go as well."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/605#pullrequestreview-913821301", "body": ""}
{"title": "Add languageId to the SourceSnippet", "number": 606, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/606"}
{"comment": {"body": "Closing. Going with a different approach for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/606#issuecomment-1072778617"}}
{"title": "Save relative path and concatenate full path for navigation", "number": 607, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/607", "body": "We should not be saving the full system path onto the sourcemark object - instead, save the relative path for display, and then find the repo root in vscode when needing to navigate.\nRelative path in the code block:"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/607#pullrequestreview-913912499", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/607#pullrequestreview-913912596", "body": ""}
{"title": "adding Kay to ip whitelist", "number": 608, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/608"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/608#pullrequestreview-913873378", "body": ""}
{"title": "Add lastMessageCreatedAt to Thread API", "number": 609, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/609"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/609#pullrequestreview-914698884", "body": "A couple of comments"}
{"comment": {"body": "Needs a DB drop since this is a non-nullable field", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/609#discussion_r830237509"}}
{"title": "Remove yaml anchors", "number": 61, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/61"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/61#pullrequestreview-855873009", "body": ""}
{"title": "Convert GitHub pull request markdown to message proto", "number": 610, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610", "body": "Includes some changes to the proto spec"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#pullrequestreview-913999119", "body": ""}
{"comment": {"body": "List items can have multiple blocks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#discussion_r829735877"}}
{"comment": {"body": "This models `<br>` in a paragraph block", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#discussion_r829736093"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#pullrequestreview-914791291", "body": ""}
{"comment": {"body": "So list blocks can contain *any* block item? \r\nCould theoretically hold another top level ListBlock", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#discussion_r830296161"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#pullrequestreview-914792137", "body": ""}
{"comment": {"body": "HorizontalLineBlock would also be strange but I guess technically possible", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#discussion_r830296739"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#pullrequestreview-914853671", "body": ""}
{"comment": {"body": ">Could theoretically hold another top level ListBlock\r\n\r\nYup, that's how nested lists work. This is a list block item that contains a list block\r\n\r\n1. List Item for top level block\r\n    - list item for a block nested inside the top level block", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#discussion_r830340939"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#pullrequestreview-914854079", "body": ""}
{"comment": {"body": ">HorizontalLineBlock would also be strange but I guess technically possible\r\n\r\nYeah, though in reality I don't think it would ever happen", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#discussion_r830341243"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#pullrequestreview-914855259", "body": ""}
{"comment": {"body": "- this is a list block item with\r\n  ```\r\n  a code block\r\n  ```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#discussion_r830342060"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/610#pullrequestreview-914856376", "body": "I'll work on the client side of this as soon as this is in."}
{"title": "Cleanup leftover shared core", "number": 611, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/611", "body": "TransactionVendorInterface was duplicated so removed it \nMoved the hashing stuff to security package under utils project\n\nRan tests locally and all passed"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/611#pullrequestreview-914698243", "body": ""}
{"title": "Setup Identity Matcher", "number": 612, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612", "body": "\nMatch email from git to identity in team member using hashed emails."}
{"comment": {"body": "While you're in this bit of code, do you mind also removing the rounded corners in the collaborators section (CollaboratorsSection.scss:9-16) and also changing the code-commit icon to import from the pro-solid lib", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612#issuecomment-1072746659"}}
{"comment": {"body": "> While you're in this bit of code, do you mind also removing the rounded corners in the collaborators section (CollaboratorsSection.scss:9-16) and also changing the code-commit icon to import from the pro-solid lib\r\n<img width=\"291\" alt=\"CleanShot 2022-03-18 at 13 44 38@2x\" src=\"https://user-images.githubusercontent.com/1553313/159081087-6c5a6ef5-c88d-4481-a201-93dcf6a5cf06.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612#issuecomment-1072785597"}}
{"comment": {"body": "For follow up, let's do this:\r\nhttps://github.com/NextChapterSoftware/unblocked/issues/696", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612#issuecomment-1081269711"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612#pullrequestreview-914882594", "body": ""}
{"comment": {"body": "Nice", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612#discussion_r830362901"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612#pullrequestreview-916441539", "body": ""}
{"comment": {"body": "This feels like the wrong pattern for a one-shot process like this.  If we want a cache of team members, maybe TeamMemberStore should export a synchronously-available cache?  In TeamStore something like, `TeamMemberStreams` gets an additional `TeamMember[]` property, and the `TeamMemberStreamStore.toObject` method sets up a listener that monitors `listStream`, and populates the new property?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612#discussion_r831606964"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612#pullrequestreview-916441599", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/612#pullrequestreview-917983469", "body": ""}
{"title": "Pass file extension as language id on web", "number": 613, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/613"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/613#pullrequestreview-916401306", "body": ""}
{"title": "Fix unnecessary channel polling", "number": 614, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/614", "body": "If a channel doesn't support polling (ie, getTeamMembers), the client would fetch anyways.  With this change, if a service doesn't respond with a lastModified timestamp for a channel, we assume that channel doesn't support polling and we don't fetch."}
{"comment": {"body": "Needs a unit test...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/614#issuecomment-1072785199"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/614#pullrequestreview-914861121", "body": ""}
{"title": "adding new docker repo for scm service", "number": 615, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/615"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/615#pullrequestreview-914875847", "body": ""}
{"title": "Spin up scm service", "number": 616, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616", "body": "Some additional dependency refactoring can be done for common classes. See file diff for explanation about which files were copied as-is and which were modified from apiservice"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-914871802", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-914871994", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-914872138", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-914872278", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-914872578", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-914872728", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-914872890", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/616#pullrequestreview-*********", "body": ""}
{"title": "added service account with access to Redis and Postgres for scm service", "number": 617, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/617", "body": "Changes have been deployed to both Dev and Prod Kube clusters."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/617#pullrequestreview-*********", "body": ""}
{"comment": {"body": "we should be consistent with naming. I've been calling this thing `scm-service`, which is what we have in the notion doc https://www.notion.so/nextchaptersoftware/Service-Architecture-Decomposition-f65d933de2f94c2487de73043efe33f5\r\n\r\nIs there a limitation here that would prevent us from naming this `scm-service`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/617#discussion_r830357154"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/617#pullrequestreview-*********", "body": ""}
{"comment": {"body": "ditto here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/617#discussion_r830357307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/617#pullrequestreview-914876870", "body": ""}
{"comment": {"body": "just following the patter we have had for both pusher and API service. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/617#discussion_r830358366"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/617#pullrequestreview-914884019", "body": ""}
{"title": "Add error log on schema update failure", "number": 618, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/618", "body": "The exposed framework just creates a WARN level log when schema migration fails \n(columns:!(message),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'logzioCustomerIndex',key:environment,negate:!f,params:(query:dev),type:phrase),query:(match_phrase:(environment:dev)))),index:'logzioCustomerIndex',interval:auto,query:(language:lucene,query:schema),sort:!(!('@timestamp',desc)))&_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now%2Fd,to:now%2Fd))&discoverTab=logz-logs-tab&accountIds=411850&switchToAccountId=411850"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/618#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/618#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Such a shame exposed doesn't dump out a reason - but I guess we could figure that out via our custom validator?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/618#discussion_r830393007"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/618#pullrequestreview-*********", "body": ""}
{"comment": {"body": "The WARN logs will tell us what failed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/618#discussion_r830400428"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/618#pullrequestreview-*********", "body": ""}
{"comment": {"body": "But we could probably make that `updateSchema` return the pending scheme upgrade commands", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/618#discussion_r830400782"}}
{"title": "Update ThreadModel.lastMessageCreatedAt when ingesting PRs", "number": 619, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/619", "body": "This logic needs refactoring so that it uses the stores, but just fixing this for now before we do that."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/619#pullrequestreview-914921414", "body": ""}
{"title": "Add readme for web", "number": 62, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62"}
{"comment": {"body": "Feel free to add anything else you believe to be necessary.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#issuecomment-1015763922"}}
{"comment": {"body": "Any ideas on how we can appropriately share a token for font awesome?\r\nhttps://fontawesome.com/v5.15/how-to-use/on-the-web/setup/using-package-managers", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#issuecomment-1015775355"}}
{"comment": {"body": "> Any ideas on how we can appropriately share a token for font awesome?\r\n> https://fontawesome.com/v5.15/how-to-use/on-the-web/setup/using-package-managers\r\n\r\nDiscussing this in Slack, I think FA's rules mean everyone needs their own token", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#issuecomment-1015781559"}}
{"comment": {"body": "> > Any ideas on how we can appropriately share a token for font awesome?\r\n> > https://fontawesome.com/v5.15/how-to-use/on-the-web/setup/using-package-managers\r\n> \r\n> Discussing this in Slack, I think FA's rules mean everyone needs their own token\r\n\r\nUpdated doc.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#issuecomment-1016986526"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#pullrequestreview-855892845", "body": ""}
{"comment": {"body": "We could set up a suggested plugins file to make this easy: https://tattoocoder.com/recommending-vscode-extensions-within-your-open-source-projects/", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#discussion_r787091730"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#pullrequestreview-855897530", "body": ""}
{"comment": {"body": "Oh interesting. Will look into this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#discussion_r787095108"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#pullrequestreview-858549977", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#pullrequestreview-858567937", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/62#pullrequestreview-858644408", "body": ""}
{"title": "API service builds and deploys when changes are made to the projects folder", "number": 620, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/620", "body": "We moved most of the projects out of apiservice"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/620#pullrequestreview-914928381", "body": ""}
{"title": "Adds ktor managed background job plugin", "number": 621, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621", "body": "When Ktor receives a SIGTERM event, it attempts a graceful shutdown by unloaded its modules and plugins. If we wire up the background job executor as a plugin, we get the benefit of Ktor's lifecycle machinery."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#pullrequestreview-915254274", "body": ""}
{"comment": {"body": "Only one thread is needed for background tasks, because coroutines :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#discussion_r830760144"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#pullrequestreview-915254561", "body": ""}
{"comment": {"body": "Eventually we'll have to do some other context stuff here, but not today", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#discussion_r830760390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#pullrequestreview-915254970", "body": ""}
{"comment": {"body": "Try and wait for completion. This is done serially, but we probably want to launch a bunch of coroutines to do this fanning out. Open to opinions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#discussion_r830760672"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#pullrequestreview-915255338", "body": ""}
{"comment": {"body": "This proves the Ktor shutdown mechanics cause this to be invoked automatically", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#discussion_r830760916"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#pullrequestreview-915256428", "body": ""}
{"comment": {"body": "@mahdi-torabi  let's figure out how to bring in a common test module so we don't have to do this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#discussion_r830761685"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#pullrequestreview-915256777", "body": ""}
{"comment": {"body": "Harmless, just want to have the scmservice do _something_ before we hook in the real jobs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#discussion_r830761964"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#pullrequestreview-915261252", "body": ""}
{"comment": {"body": "Linter complaining that I wasn't using `nowWithMicrosecondPrecision()`. @mahdi-torabi I couldn't pull in `:projects:utils` to the `:projects:libs` gradle dependencies in 5 minutes and gave up. Something's not right about that...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#discussion_r830765241"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/621#pullrequestreview-916358634", "body": "LGTM!"}
{"title": "Repo spec update", "number": 622, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622", "body": "Updating Repo spec to support focusing clients (VSCode + WebExtension) to their current workspace while allowing dashboard remain non-repo specific.\nAdd repoIDs to getThreads, getUnreads"}
{"comment": {"body": "Question: Can we update FindRepoRequest to better support web extension? Or new endpoint?\r\n\r\nWould be cool if given a GitHub url, it returns the relevant repos. There can be multiple types of URLs for a single file where we may want to render Unblocked data.\r\nE.g.\r\n`https://github.com/NextChapterSoftware/unblocked/blob/main/gradlew.bat`\r\n`https://github.com/NextChapterSoftware/unblocked/blob/cleaned_shared_lib_move/gradlew.bat`\r\n`https://github.com/NextChapterSoftware/unblocked/blob/559a37588973cf9dd6b6b496b79316e1fe478a2a/gradlew.bat`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#issuecomment-1074224970"}}
{"comment": {"body": "> Question: Can we update FindRepoRequest to better support web extension? Or new endpoint?\r\n> \r\n> Would be cool if given a GitHub url, it returns the relevant repos. There can be multiple types of URLs for a single file where we may want to render Unblocked data. E.g. `https://github.com/NextChapterSoftware/unblocked/blob/main/gradlew.bat` `https://github.com/NextChapterSoftware/unblocked/blob/cleaned_shared_lib_move/gradlew.bat` `https://github.com/NextChapterSoftware/unblocked/blob/559a37588973cf9dd6b6b496b79316e1fe478a2a/gradlew.bat`\r\n\r\n@jeffrey-ng yes, I agree we can be lenient. Client should be able to pass any html web url, not just \"clone\" urls.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#issuecomment-1081261277"}}
{"comment": {"body": "@davidkwlam Was a merge conflict with some service code. I took your changes but may have missed some things so please take a look.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#issuecomment-1083363121"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-916144069", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-916146908", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-916363019", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-916405058", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-916408546", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-917529162", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-917597323", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-918010790", "body": ""}
{"comment": {"body": "Should we make this required? If we do keep it optional, what should the behaviour be when `repoIds` isn't passed to `getThreads` or `getUnreads`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#discussion_r832715818"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-919390075", "body": ""}
{"comment": {"body": "I'd probably vote to make it required, as the usages I can see for these APIs are all repo-centric.  I guess this brings up a separate question, which is how these interact with the `teamId` property.  The options are:\r\n\r\n1. Remove teamId, which means clients can make cross-team requests.  The service will need to verify that the repos being requested are actually accessible by the client's user\r\n\r\n2. Enforce that all repos must be owned by the given teamId, which means clients would make multiple requests in a cross-team workspace, and the service would have to make sure that the repos being fetched are owned by the given team", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#discussion_r833678821"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-923462649", "body": ""}
{"comment": {"body": "@pwerry Should this wait on your \"security\" proposal to go from teamID to repoID?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#discussion_r836649578"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-923477258", "body": ""}
{"comment": {"body": "I was imaginging it would grab *everything* for the user.\nThat would be used in the web dashboard situation where there are no repoIDs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#discussion_r836659946"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-926678780", "body": ""}
{"comment": {"body": "Did we come to a resolution on this? Right now the implementation won\u0014't work if the repos list is empty (i.e. it will return nothing)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#discussion_r838934330"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-926679879", "body": ""}
{"comment": {"body": "Let's just merge as is and revisit when necessary. Not returning everything seems like a sane default.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#discussion_r838935117"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/622#pullrequestreview-926680340", "body": ""}
{"title": "Include PR details for threads ingested from PR comments", "number": 623, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/623", "body": "Also correctly sets Thread.threadType"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/623#pullrequestreview-916608302", "body": ""}
{"title": "Use custom headers for LastModified and IfModifiedSince", "number": 624, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/624", "body": "As per \nLastModified and IfModifiedSince are standard headers and have standard behaviour that browsers expect.  Our usage of these headers is non-standard, and the browser behaviour was preventing the dashboard UI from working correctly, so we'll switch to custom headers X-Unblocked-Last-Modified and X-Unblocked-If-Modified-Since"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/624#pullrequestreview-916282493", "body": ""}
{"comment": {"body": "It'd be nice to factor this out into a common ref, might look into this next.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/624#discussion_r831488924"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/624#pullrequestreview-916284169", "body": ""}
{"comment": {"body": "See this: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Expose-Headers\r\n\r\nThis boils down to ktor sending clients the `Access-Control-Expose-Headers` header, which tells browsers that they should allow accessing the `X-Unblocked-Last-Modified` header.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/624#discussion_r831490071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/624#pullrequestreview-916324188", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/624#pullrequestreview-916324875", "body": ""}
{"title": "adding gzip compression to cloudfront", "number": 625, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/625", "body": "Added flags to enable gzip compression on dashboard endpoint\nCopied the same caching policy as dashboard endpoint for landing page endpoint (with compression flags)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/625#pullrequestreview-916383199", "body": ""}
{"title": "Optimize dev dashboard build", "number": 626, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/626", "body": "Enable optimizations for dev dashboard build. This enables minification and tree-shaking\nChange the devtool option to source-map, so we generate separate source map files instead of inlining\nAdd analyzer npm scripts"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/626#pullrequestreview-916420296", "body": ""}
{"title": "Return latestModified header for getUnreads", "number": 627, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/627", "body": "Not sure why I forgot to add this when I implemented this endpoint..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/627#pullrequestreview-916410045", "body": ""}
{"title": "Rename VSCode extension", "number": 628, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/628", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/628#pullrequestreview-916432900", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/628#pullrequestreview-916433211", "body": ""}
{"title": "Implement getRepos api endpoint", "number": 629, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/629", "body": "findOrCreateRepo will come in a separate PR"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/629#pullrequestreview-916605295", "body": ""}
{"comment": {"body": "This kind of thing is where a protocol based language like Swift really shines. Instead of doing this we could extend a class to implement an `ApiModelable`, then pass the data object directly, then pull out lastModified etc generically. I can't see a clean way to do this in Kotlin", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/629#discussion_r831729473"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/629#pullrequestreview-916611548", "body": ""}
{"comment": {"body": "Yeah it's too bad we don't have duck typing. Quack quack.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/629#discussion_r831734142"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/629#pullrequestreview-916613068", "body": ""}
{"comment": {"body": "![image](https://user-images.githubusercontent.com/858772/159401281-f5dca525-5ce9-40b0-9708-0547a8a9114d.jpeg)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/629#discussion_r831735336"}}
{"title": "Remove unnecessary files generated during openapi generation", "number": 63, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/63"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/63#pullrequestreview-855894838", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/63#pullrequestreview-855895417", "body": ""}
{"title": "Use teams instead of team in paths", "number": 630, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/630", "body": "My bad. Resources should be plural."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/630#pullrequestreview-916524207", "body": ""}
{"title": "Remove sourceMarkGroupIds query parameter", "number": 631, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/631", "body": "Not used https://github.com/NextChapterSoftware/unblocked/pull/622#discussion_r831389754"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/631#pullrequestreview-917551650", "body": ""}
{"title": "Fix loading state for unauthenticated sidebar", "number": 632, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/632", "body": "When auth is finished initializing, but user is not authenticated, threadAndParticipants stream will not be initialized.\nThis causes a dead end loading state where user is prevented from logging in."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/632#pullrequestreview-917573764", "body": ""}
{"title": "Adds SCM install fields to data models", "number": 633, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633", "body": "I think this is all we need for now. The relationship between SCM Install and Team is 1:1, so the fields are in the Team table. Eventually we might want to add the installation slug so we can do things like determine whether the install has the correct permissions when there are errors. isScmConnected is also added to the Repo table so we can provide hints to PR ingestion and the clients that action needs to be taken to \"re-connect\".\nFor now we can assume that if the correct permissions haven't been granted, isScmConnected is false"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#pullrequestreview-917604871", "body": ""}
{"comment": {"body": "The formatter did this \ud83e\udd37 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#discussion_r832434104"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#pullrequestreview-917606777", "body": ""}
{"comment": {"body": "Auto-format", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#discussion_r832435411"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#pullrequestreview-917607240", "body": ""}
{"comment": {"body": "I will do this in a followup PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#discussion_r832435735"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#pullrequestreview-917642208", "body": "LGTM. I might consider keeping RepoModel unchanged if isScmConnected can't differ from TeamModel.isScmConnect but up to you"}
{"comment": {"body": "Is it possible for `TeamModel.isScmConnected` != `Repo.isScmConnected`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#discussion_r832459927"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#pullrequestreview-917689001", "body": ""}
{"comment": {"body": "Yes. It's possible to remove a Repo from the App Install. When installing, you select a set of Repos in the Org. It's definitely possible to remove (or add) a repo later on. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#discussion_r832493499"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#pullrequestreview-924848902", "body": ""}
{"comment": {"body": "@pwerry maxLineLength is configured in detekt.yml to be 140, so this shouldn't be happening.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/633#discussion_r837633524"}}
{"title": "Rename TeamMember.isDeactivated to isCurrentMember", "number": 634, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/634"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/634#pullrequestreview-917685960", "body": ""}
{"comment": {"body": "@davidkwlam This logic was missing from the implementation - make sense?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/634#discussion_r832491335"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/634#pullrequestreview-917792011", "body": ""}
{"comment": {"body": "The api model has a `isCurrentMember` field so I'm not sure if clients expect the results to include all team members, is current or not.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/634#discussion_r832560510"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/634#pullrequestreview-917793005", "body": ""}
{"comment": {"body": "Oh sorry, ignore me. Yes this is the right move", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/634#discussion_r832561223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/634#pullrequestreview-917796722", "body": ""}
{"title": "Fix TeamMembersIssue", "number": 635, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/635", "body": "Fixed issue where team members did not appear in thread UI.\nRelated to https://github.com/NextChapterSoftware/unblocked/pull/614\nWith that change, we no longer polled on channels that did not reply with lastModified. \nIn VSCode, the poller fetched the data for TeamMembers which was cached in the stream store. When we tried fetching the data again from the Zustand store, the poller refused to fetch as it had already done so for the stream store.\nThe \"quick\" fix here was to go from Zustand -> team store. \nLong term, we should remove DataCacheStore and move towards convergence on streams. Will wait for @matthewjamesadam to comment on that one."}
{"comment": {"body": "Maybe hold off on merging this?  I'm working in a similar area removing the DataStoreCaches and I think my work will fix this in a conflicting way...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/635#issuecomment-1077865657"}}
{"comment": {"body": "> Maybe hold off on merging this? I'm working in a similar area removing the DataStoreCaches and I think my work will fix this in a conflicting way...\r\n\r\nCan do.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/635#issuecomment-1078279124"}}
{"comment": {"body": "No longer an issue.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/635#issuecomment-1083454447"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/635#pullrequestreview-920622923", "body": ""}
{"title": "Add scmservice helm and rework CI jobs to deploy it", "number": 636, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636", "body": "Modified existing workflows to use SSH tunnelling using our Bastion host instead of VPN. This would allow us to do concurrent deploys. \nRemoved dependencies for sequential deploys. All service deploys happen at once upon a successful build \nAdded helm charts and values.yaml files for SCM service (note I will be moving all helm charts to projects directories once I have added pusher service) \nRemoved the old reusable Github action and replaced it with a new one that supports deploys via SSH \n\nNote: all helm files were generated by Ansible. I just updated values.yaml files."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#pullrequestreview-917792319", "body": ""}
{"comment": {"body": "I think you can just supply the key directly instead of creating the `id_rsa` file?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#discussion_r832560733"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#pullrequestreview-917795644", "body": ""}
{"comment": {"body": "I tried that with `ssh-agent` and `ssh-add` but the issue is that we override the Kubernetes endpoint using `/etc/hosts` and because of that require binding to port 443. Linux doesn't like that and makes us run SSH as root but that makes running `ssh-agent` and `ssh-add` painful. \r\n\r\nI basically spent my morning on the other approach but got nowhere ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#discussion_r832563089"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#pullrequestreview-917796621", "body": ""}
{"comment": {"body": "https://stackoverflow.com/a/68514677\r\nThis is what I was originally trying to do. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#discussion_r832563777"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#pullrequestreview-917796727", "body": ""}
{"comment": {"body": "Why do we need the finer-grained match?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#discussion_r832563881"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#pullrequestreview-917799157", "body": ""}
{"comment": {"body": "\ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#discussion_r832565603"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#pullrequestreview-917799444", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#pullrequestreview-917801491", "body": ""}
{"comment": {"body": "That matches literally two files which are those specific service files. I couldn't go any finer :P ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/636#discussion_r832567232"}}
{"title": "Atomic helm deploys", "number": 637, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/637", "body": "Deployments went into a bad state again. I'll clean them manually for now but this is a change that should prevent it from happening in the future. \n\nAdded --atomic flag to enable auto-rollbacks \n\n--atomic                       if set, upgrade process rolls back changes made in case of failed upgrade. The --wait flag will be set automatically if --atomic is used"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/637#pullrequestreview-917845555", "body": ""}
{"title": "Filter fetched threads in sidebar by mine/team", "number": 638, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638", "body": "Refactored ThreadStore to pipe down three streams of info: all: all threads, mine: threads involving current person, team: threads that don't involve current person\nRefactored and simplified web Chats.tsx component (could probably be renamed??)\nRefactored vscode Sidebar into two webviews, one purely for Auth (AuthSidebar) and one for post-auth UI (Sidebar) - also involves separating the logic from the single webview provider into two \nthe sidebar views switch depending on the unblocked:authenticated context -- if this pattern works well, we could extend it to other parts of the extension that ought to be gated by auth as well\nDeleted a couple of unused views per new designs"}
{"comment": {"body": "Just a few comments. Most can be handled in a separate PR. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917899186", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917900342", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917901735", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917907820", "body": ""}
{"comment": {"body": "Needs to handle the authenticated state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r832640947"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917909651", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917913982", "body": ""}
{"comment": {"body": "Let's pull this out. I think we need to refine the logic below and add some tests for just this function.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r832645414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917980655", "body": ""}
{"comment": {"body": "fwiw this was code added previously, I just moved it to a new file. I'd want to handle this in a separate PR ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r832694497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917985259", "body": ""}
{"comment": {"body": "This is how we're doing summary view?\r\nIs this temporary or what we're doing from now on?\r\n\r\nThis is *super* expensive. For every thread we want to display, we have to load *all its messages just to get the first value. Explains why the initial loading state takes a while.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r832697553"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917986899", "body": ""}
{"comment": {"body": "Suggest subscribing to streams within the \"ChatsForType\". Doing so here will add unnecessary subscribers. Instead of having two subscriptions, there will be four.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r832699136"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917987256", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917987421", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917989998", "body": ""}
{"comment": {"body": "I think this is basically what we talked about a few weeks ago re: whether we want the threads api to include the first message or have a separate api to only fetch the first message. May be a good time to revisit this conversation?? FWIW I agree, I think either of these two options is better than fetching all the messages just to grab the first one", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r832701656"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-917997894", "body": ""}
{"comment": {"body": "I did try this but the data for the view doesn't refresh accordingly when calling `useStream` inside the subcomponent. I wonder if it's something to do with passing in the Stream as a prop ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r832705626"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-919037328", "body": ""}
{"comment": {"body": "Yeah. I wouldn't pass the stream. Just pass the teamID and call `ThreadStreams.get` on the teamID to get the stream. There should only be a single instance of the stream so no worry of duplication.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r833447998"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-919461481", "body": ""}
{"comment": {"body": "The logic for determining the `unblocked:authenticated` state is spread out in too many files I think -- I think it should just be the VSCode AuthStore that manages this state completely?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r833726631"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#pullrequestreview-919465495", "body": ""}
{"comment": {"body": "This seems almost identical to `AllDiscussionsTreeViewDataProvider` -- should we make a single class that takes in the few differing properties (the root object key, title, etc) as arguments?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/638#discussion_r833729459"}}
{"title": "Fix deployment triggers and health probes", "number": 639, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/639", "body": "Changed service deployment workflow to trigger on action.yaml changes\nModified health probe frequencies\nRemoved initial delay from liveness and readiness probes. They don't make sense since we have a startup probe\nadded thresholds for check failures. Porbes were calling it too early resulting in service restarts on new deployments\nRan Ansible playbook to updated base helm charts and commited tgz archives for each service"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/639#pullrequestreview-917982389", "body": ""}
{"title": "Github actions for deploying apiservice to kube", "number": 64, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64", "body": "This pr moves us to using a combination of AWS ECR + EKS.\nJOB 1 \n1. We build via gradle the shadowJar and output that as a job-level output in the pipeline.\nJOB 2:\n1. We set up AWS creds for central ECR repository.\n2. We build an image using the jar from JOB 1.\n3. We push to central ECR repo\n4. We record image tag as output.\nJOB 3:\n1. We set up AWS creds for dev environment.\n2. We use helm to install apiservice charts specifying image repository and tag from JOB 2.\n\napiservice-charts-5bff56c576-7pj9w        0/1     CrashLoopBackOff   5          5m12s\nCurrently crashing because of RDS, but it's running at least...\nTODO:\n1. We need to make this more generic. (i.e. per service type) That's going to to take some thinking..."}
{"comment": {"body": "> We need to make this more generic. (i.e. per service type) That's going to to take some thinking...\r\n\r\nWe can refactor steps into custom actions which can be saved in `.github/actions/`. Haven't ever tried this, but should be able to parameterize. See https://docs.github.com/en/actions/creating-actions/about-custom-actions#choosing-a-location-for-your-action", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#issuecomment-1017040914"}}
{"comment": {"body": "I think I'm planning on splitting up the work. That's a follow up", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#issuecomment-1017071592"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-856007056", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-856058866", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-856059055", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857539089", "body": "My helm is non-existent, so can't give you a good review of the chart files right now unfortunately."}
{"comment": {"body": "Do we need to checkout source code? Looks like we're just pulling the artifact from GitHub and pushing into ECR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#discussion_r788274420"}}
{"comment": {"body": "should this run only on `main`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#discussion_r788274572"}}
{"comment": {"body": "needed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#discussion_r788274720"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857542618", "body": ""}
{"comment": {"body": "Dockerfile is there.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#discussion_r788276122"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857542674", "body": ""}
{"comment": {"body": "Might build image in first job, not sure.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#discussion_r788276174"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857542988", "body": ""}
{"comment": {"body": "Yeah, for the helm charts.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#discussion_r788276389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857543647", "body": ""}
{"comment": {"body": "Yes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#discussion_r788276853"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857618312", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857619728", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857623993", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857626597", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-857629686", "body": "I can't speak to GitHub action stuff. I literally have zero experience with them. Helm stuff look good to me but left a couple of minor comments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/64#pullrequestreview-858599324", "body": ""}
{"title": "Add gh install redis lock", "number": 640, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/640"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/640#pullrequestreview-917944058", "body": ""}
{"comment": {"body": "I tried to factor this out using the `java-test-fixtures` gradle plugin but it stomps all over the normal `implementation` dependencies. I think fixing this is going to require a gruesome test jar hack, or else we just drop all the \"test helpers\" into a separate project and pull them in with `testImplementation`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/640#discussion_r832668129"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/640#pullrequestreview-918164941", "body": ""}
{"title": "Change bother to unblocked in ci-vscode.yml", "number": 641, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/641", "body": "Related to https://github.com/NextChapterSoftware/unblocked/pull/628"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/641#pullrequestreview-918025726", "body": ""}
{"title": "Take first 100 characters of first message for thread title", "number": 642, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/642"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/642#pullrequestreview-918037193", "body": ""}
{"title": "Small nit styling fixes", "number": 643, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/643", "body": "Vscode code block - only the snippet should scroll, not the entire block (ie pin the code header)\nGet rid of some unused code \nRename web navigator to match vscode"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/643#pullrequestreview-919393536", "body": ""}
{"title": "Copies apiservice changes to scmservice. This is brittle...", "number": 644, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/644"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/644#pullrequestreview-918131036", "body": ""}
{"title": "Adds external ID to RepoModel to associate with SCM", "number": 645, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/645"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/645#pullrequestreview-918164236", "body": ""}
{"title": "Sort threads by lastMessageCreatedAt desc", "number": 646, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/646", "body": "Threads returned by the API service are sorted such that those with the most recent messages come first"}
{"comment": {"body": "Actually, I don't think we should do this. Clients will be querying this with the `X-Unblocked-If-Modified-Since` header so they'll need to sort by `lastMessageCreatedAt` field on the client side regardless.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/646#issuecomment-1076000538"}}
{"comment": {"body": "Yeah clients need to do the sorting, at least for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/646#issuecomment-1076535366"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/646#pullrequestreview-918258454", "body": ""}
{"comment": {"body": "I predict this will require an index", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/646#discussion_r832899890"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/646#pullrequestreview-918259305", "body": ""}
{"comment": {"body": "This is necessary because the map messes up the ordering from the previous query. We still want the `orderBy` in the previous query in case we start paginating those results (which we don't do right now but I\r\n positive we'll need to at some point)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/646#discussion_r832900540"}}
{"title": "Add deleteMessage operation", "number": 647, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647", "body": "Adds a deleteMessage operation so that clients can allow deleting messages from a thread\nAdd validations to ensure only the message author can update or delete a message"}
{"comment": {"body": "Spec changes look fine to me", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647#issuecomment-1076860137"}}
{"comment": {"body": "Pete pointed out this can't be a hard delete since clients will need to be notified of deletes and we can't send updates for objects that no longer exist in our database.\r\n\r\nWe should either clear the `messageContent` field or add a new `isDeleted` field -- basically anything that updates `modifiedAt` and signals to clients that a message should not be shown in a thread. \r\n\r\n@matthewjamesadam @jeffrey-ng @kaych any thoughts? Ultimately it comes down to how clients want to handle this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647#issuecomment-1076871591"}}
{"comment": {"body": "> Pete pointed out this can't be a hard delete since clients will need to be notified of deletes and we can't send updates for objects that no longer exist in our database.\r\n> \r\n> We should either clear the `messageContent` field or add a new `isDeleted` field -- basically anything that updates `modifiedAt` and signals to clients that a message should not be shown in a thread.\r\n> \r\n> @matthewjamesadam @jeffrey-ng @kaych any thoughts? Ultimately it comes down to how clients want to handle this.\r\n\r\nI think we should do both:\r\n* We should add a flag indicating if the message has been deleted or not -- this is what the client will use to decide whether to render the message\r\n* We should remove the content on deleted messages -- this is for people who deleted the message because it accidentally contained secrets or something else they want to redact.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647#issuecomment-1076876145"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647#pullrequestreview-919503632", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647#pullrequestreview-919505281", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647#pullrequestreview-919506625", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647#pullrequestreview-919566425", "body": ""}
{"comment": {"body": "I'm not 100% sure if making this optional is the right move.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647#discussion_r833804060"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/647#pullrequestreview-920964985", "body": ""}
{"title": "Move pull request ingestion to background service", "number": 648, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648", "body": "Super basic first pass at moving PR ingestion to the SCM service. Next step is to break down PR ingestion into smaller chunks so that ingestion does all happen in one go of the job instance.\nTested locally and this works"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#pullrequestreview-919469973", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#pullrequestreview-919504609", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#pullrequestreview-919505049", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#pullrequestreview-919506288", "body": ""}
{"comment": {"body": "I'm changing the API for this a little bit so that `job` is a `BackgroundJob` instead of a closure. No action here just a heads up for what's coming. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#discussion_r833757863"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#pullrequestreview-919507816", "body": ""}
{"comment": {"body": "Are we going to use `PullRequestIngestion` in the general case where new pull requests have been created? I'll assume this is temporary?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#discussion_r833759031"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#pullrequestreview-919508130", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#pullrequestreview-919548826", "body": ""}
{"comment": {"body": "Yeah temporary. We'll need to refactor this logic to support new pull requests created after the first ingestion that runs during onboarding.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/648#discussion_r833790542"}}
{"title": "Use providerExternalInstallationId in PullRequestIngestion", "number": 649, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#pullrequestreview-919502559", "body": ""}
{"comment": {"body": "This will change as we uninstall/reinstall", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#discussion_r833755090"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#pullrequestreview-919503102", "body": ""}
{"comment": {"body": "I guess we want to load this from config though?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#discussion_r833755517"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#pullrequestreview-919504193", "body": ""}
{"comment": {"body": "Any suggestions? We're hooped even if we keep this in the config files", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#discussion_r833756234"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#pullrequestreview-919507400", "body": ""}
{"comment": {"body": "The plan is to update this field on reinstall, yeah?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#discussion_r833758739"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#pullrequestreview-919508640", "body": ""}
{"comment": {"body": "Yup - when the install work is finished this should get populated automatically ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#discussion_r833759645"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/649#pullrequestreview-919508937", "body": ""}
{"title": "Skip workflow if targetted files have not changed", "number": 65, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/65", "body": "Allows us to require all status check in monorepo."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/65#pullrequestreview-855943373", "body": ""}
{"title": "Very basic setup with Extension", "number": 650, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650", "body": "\nBasic setup for web extension.\nAdded message passing with port manager.\nBasic Auth and Thread loading.\nTODO:\n* Issues with content + background lifecycle. If one refreshes the page, previous instances of connections are not 100% cleaned up."}
{"comment": {"body": "Updated UI\r\n<img width=\"350\" alt=\"CleanShot 2022-03-24 at 15 25 02@2x\" src=\"https://user-images.githubusercontent.com/1553313/160021457-4929b0e8-dd56-448e-8ab5-c8c91de92d8e.png\">", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#issuecomment-1078447227"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-1106503035", "body": ""}
{"comment": {"body": "test4\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/bbc8919e-0ae8-4143-951a-d141ac5ad7c9?message=453b4993-2fba-404c-a71c-9da32140b222).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r970120187"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-1106503102", "body": ""}
{"comment": {"body": "test5\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/bbc8919e-0ae8-4143-951a-d141ac5ad7c9?message=3b27ddce-b0b1-49db-9bfe-e2e3febc7196).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r970120235"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-919562167", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-919573933", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-919576123", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920500415", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920508420", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920509663", "body": ""}
{"comment": {"body": "There's a file above this `AuthMessageTypes.ts` that isn't referenced anywhere, it can be removed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834475071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920843920", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920849506", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920891328", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920912932", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920954977", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920955922", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920957779", "body": ""}
{"comment": {"body": "This file seems to still be here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834780338"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920959481", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920976261", "body": ""}
{"comment": {"body": "Removed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834793828"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920988301", "body": ""}
{"comment": {"body": "Some very rough ideas for the future, which may or may not make sense:\r\n\r\n1. Since the expectation is that this is used in a react component, this could maybe be wrapped in a hook:\r\n\r\n```\r\nfunction usePortManager(listener: PortListenerFn<BT>)\r\n```\r\n\r\n2. It might be worth wrapping the UIs in Context that auto-provide the tab IDs, instead of drilling them down everywhere?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834803600"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-920996334", "body": ""}
{"comment": {"body": "currentContentListeners is an array we're editing inline, technically we don't need to do this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834806200"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921001711", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921003255", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921004582", "body": ""}
{"comment": {"body": "I don't understand this much -- it looks like the background gets notified that tabs have changed, so tries to auth if needed when we hit GH (makes sense), but then sends another message back to the browser tabs?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834820316"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921005254", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921006774", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921007043", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921007832", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921008000", "body": ""}
{"comment": {"body": "Both of these make sense. \r\nHad thought of doing the context provider as well.\r\n\r\nJust wanted to keep this PR as \"lean\" as possible. (This PR isn't lean at all \ud83e\udd23)\r\n\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834829400"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921020296", "body": ""}
{"comment": {"body": "We probably should factor this out into its own file...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834844118"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921021003", "body": ""}
{"comment": {"body": "Yup. This is how the background notifies the content that it should potentially render.\r\naka on every URL change, check if we should render the sidebar.\r\n\r\nThe sidebar will then open a port with the background to continue the conversation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834844634"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921021086", "body": ""}
{"comment": {"body": "Another idea to discuss for the future: I'm wondering for the \"content\" views, if we need a loading state at all -- if we send the DataCacheState<Thread> across, it already contains an initialized state?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834844703"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921022254", "body": ""}
{"comment": {"body": "Another thing to discuss for the future: if our data is primarily streamed, maybe we should support sending streams as separate things, so we don't have to mux/join streams like this?  This is relevant to webviews too.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834845637"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921022668", "body": ""}
{"comment": {"body": "Is this expected, or an error case?  ie, are we expecting that the first time we render here we will append our own div?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834845908"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921023297", "body": ""}
{"comment": {"body": "This is expected. Our sidebar is appended to a top level div we add just under the body.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834846383"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921023443", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921023634", "body": ""}
{"comment": {"body": "rem?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834846619"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921024207", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921025053", "body": ""}
{"comment": {"body": "Should this filename (& sidebar.scss) be capitalized?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r834847733"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921025268", "body": "  !"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#pullrequestreview-921842302", "body": ""}
{"comment": {"body": "Will be coming back in a separate PR to handle styling and the cleanup related.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/650#discussion_r835404136"}}
{"title": "Fix query for PR ingested threads", "number": 651, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/651"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/651#pullrequestreview-919564965", "body": ""}
{"comment": {"body": "I forgot this. Without this check, PR ingestion won't happen if there is *any* thread.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/651#discussion_r833802912"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/651#pullrequestreview-919566594", "body": ""}
{"comment": {"body": "Ahhhh good catch. Went totally over my head.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/651#discussion_r833804233"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/651#pullrequestreview-919566643", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/651#pullrequestreview-919567878", "body": ""}
{"comment": {"body": "I'm pretty sure we'll eventually land on another (better) way to check whether we should ingest PRs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/651#discussion_r833805213"}}
{"title": "Add overlay functionality to data streams", "number": 652, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/652", "body": "This is part one of replacing DataCacheStore with DataCacheStream:\n\nReplace the DataCacheStore unit tests with tests on DataCacheStream\nDataCacheStream has no overlay functionality, and we'll need it so I added it.  There is a DataCacheStreamOverlay helper class that represents all overlaid data, it internally generates a stream of the overlay data, and a helper to overlay it onto a base stream.\n\nNext PR will actually replace the stores, I think."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/652#pullrequestreview-919567825", "body": ""}
{"comment": {"body": "There are a bunch of changes from `1` to `2` like this -- this happens because since the Stream is signed up for immediately, the initial (!initialized) value is always sent.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/652#discussion_r833805168"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/652#pullrequestreview-920597343", "body": ""}
{"title": "Remove duplicate authors", "number": 653, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/653", "body": "This prevents attempts to create duplicate identities"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/653#pullrequestreview-919572487", "body": ""}
{"title": "Create pusher service", "number": 654, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654", "body": "Overview\nI started the pusher service by using Peter's work in scm service as a template. From there it was just modifying a few paths and moving implementation code from api-service. Rest of this PR is basically moving db tests and modifying Gradle files to get cross project shared test dependencies to work. \nChanges\n\nAdded new Gradle config to export tests in each project as a jar \nCleaned up a whole bunch of unused plugins and dependencies along with some cosmetic changes to Gradle task defintions\nMoved DB tests (required by pusher tests and api service tests) to models project next to db package code\nCreated a new project for pusherservice at projects/services/pusherservice \nCreated entry point service application and added all necessary required plugins/utils/resources\nCleaned up all references to pusher in apiservice \nModified dependencies in apiservice and new pusherservice to create dependency on models project test jar\nMoved pusher tests out of apiservice and into the new pusher project\nChanged CICD workflow to deploy new pusherservice instead of a duplicate of apiservice \nAdded a new route /apm/pusher serving health endpoints for public (Grafana APM checks) consumption. I will add the same change to all other services in a separate PR. This will allow us to do APM checks on all public facing services.\n\nTODOs and Caveats\n\nSome code (mainly util classes in both tests and actual source) are duplicated. I will try to deduce most of that in my next PR to move all shared tests to their respective packages \nThis PR only moves DB tests to address a dependency issue. All other shared tests will be moved in my next PR\nWe still need to find a cleaner approach to config management"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920569725", "body": ""}
{"comment": {"body": "remove these if they're not being used", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834517388"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920570941", "body": ""}
{"comment": {"body": "This feels pretty dirty \ud83d\ude03", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834518251"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920573397", "body": ""}
{"comment": {"body": "I am replacing them all in my next PR. Just using these as a reminder for where I should edit \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834520063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920573991", "body": ""}
{"comment": {"body": "It's dirty but It's the cleanest you can get considering current state of tooling for this problem space. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834520490"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920610022", "body": ""}
{"comment": {"body": "btw I am pretty sure we can apply these changes for test jars at the top level build.gradle instead of every project but I couldn't get it to work. This is one of those things I'll leave for Rashin since he has more experience with Gradle", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834546186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920618767", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920620872", "body": ""}
{"comment": {"body": "We should start pulling common plugins into their own module, or drop into libs. Not sure if CORS will change across services for example", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834554012"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920622502", "body": ""}
{"comment": {"body": "I would model this class after the one in the apiservice rather than the scmservice so we capture uuids", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834555235"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920624810", "body": ""}
{"comment": {"body": "Model after means copy-paste and fix imports ? right ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834556912"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920669821", "body": ""}
{"comment": {"body": "I actually am not sure if CORS is even needed since we have everything under the same domain now. I'll leave this for others to tackle at a later time. A bit over my head. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834588996"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920705771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920705943", "body": ""}
{"comment": {"body": "Done. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#discussion_r834611532"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/654#pullrequestreview-920716913", "body": "Let it rip"}
{"title": "Add logging to PollingBackgroundJob", "number": 655, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/655"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/655#pullrequestreview-920633341", "body": ""}
{"title": "Update Jeff's IP", "number": 656, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/656"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/656#pullrequestreview-920663897", "body": ""}
{"title": "Clip the thread title for the tab", "number": 657, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/657", "body": "Manually clip the thread title string to a sensible length for the tab title (right now the tab width stretches to the full width of the title string)\nIdeally we should display the full string and limit the width of the tab but I couldn't figure out a way to do this via the extension API. Even in the regular vscode editor settings, there's no easy way to manually size the tabs, there's setting to set the sizing from fit to shrink but this doesn't really apply to our use case"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/657#pullrequestreview-920750063", "body": ""}
{"comment": {"body": "in the designs we need to actually move the title into the first comment for ThreadType.Discussion types but I'm not sure how to reasonably do this/if this should even be done on the client. It would involve needing to convert the title text into a block, unencoding the first message, adding the title block to the message blocks, and then reencoding the message(???)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/657#discussion_r834634781"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/657#pullrequestreview-920804140", "body": ""}
{"comment": {"body": "Agreed. Should not be a client thing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/657#discussion_r834674631"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/657#pullrequestreview-920804361", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/657#pullrequestreview-920895979", "body": ""}
{"comment": {"body": "I think the title should continue to be a property on Thread, as it is today -- we could implement this in the client by, when rendering the messages for a thread, adding the title as a \"sub header\" item of the first message.  ie, we never edit message content, we just add this as an additional optional renderable item, like we do for the anchor code block.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/657#discussion_r834734139"}}
{"title": "fix a copy-paste mistake", "number": 658, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/658", "body": "Fix a small package naming mistake"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/658#pullrequestreview-920750895", "body": ""}
{"title": "Fix logging", "number": 659, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/659", "body": "I'm certain there's a better way to do this."}
{"comment": {"body": "Is this still needed after Rashin's changes to logger ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/659#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/659#pullrequestreview-*********", "body": ""}
{"title": "Cdk refactor - make ecr shared and replicate it to every account", "number": 66, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/66", "body": "Removed old ECR repos creation per account. All repos are created under a single (sec-ops) account and then replicated to all other regions/environments \nAdded SecOpsBuildConfig to support deployment configs for a new environment type \nAdded ecr registry stack for sec-ops shared account along with replication rules \nCreated ECR policy to allow replication from sec-ops account in consumer accounts \nDid some code cleanup and refactoring to make the build configs more readable \nReplaced the old main function which controls what stacks to deploy based on environment types \n\nThese changes have been deployed. We now have working ECR replication. I'll provide IAM creds for our automation account out of band using 1password"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/66#pullrequestreview-*********", "body": ""}
{"title": "Remove thread DataCacheStore", "number": 660, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/660", "body": "Removes thread-model DataCacheStore, replacing it with streams usage."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/660#pullrequestreview-*********", "body": ""}
{"comment": {"body": "FYI @jeffrey-ng @kaych this is the easiest way to memoize the latest results from a stream.  This fixes the team membership caching issues, and the nice thing is you can memoize at any point in the stream.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/660#discussion_r834705559"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/660#pullrequestreview-*********", "body": ""}
{"title": "change pusher path", "number": 661, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/661", "body": "Change pusher path so we could use /api/channels prefix on Application Loadbalancers when routing traffic. This is  to forward different paths to services responsible for those sets of functionality"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/661#pullrequestreview-*********", "body": ""}
{"title": "GH Org Installs", "number": 662, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662", "body": "Summary\nInitially implemented against the GitHub /app/installations API with a since parameter, but we discovered that since is only applied to createdAt and not updatedAt, which defeats the purpose. Therefore we are currently ripping down all the installs and manually checking the updatedAt value for further processing.\nProcessing includes org data, org members, and repos. Anonymous identities are created for users who are not already in the system. Users already in the system are wired up to the team. \nCaveats\nWe've learned it is not possible to obtain the root commit sha for a repo from GitHub without content permissions. For now this value is being set to some random sha (and not currently used in the system). We may be able to fix this in the future by allowing clients to pass us the sha value during findOrCreateRepo(), and then assign a confidence score as more clients roll in. \nOr alternatively, if we introduce the concept of a RepoMember, each user can have their own root sha? (This might cause problems if we're doing sourcemark things on the backend, so we might need both)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-920931608", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-920932603", "body": ""}
{"comment": {"body": "Continuation offset currently stored in redis. If the database is dropped we will also have to do a redis flush @mahdi-torabi @davidkwlam. I could possibly write some detection logic on startup and flush redis but that seems dangerous", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r834761719"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-920933982", "body": ""}
{"comment": {"body": "Only pulling first 100 members and repos for now. We'll need a generalized stream implementation (flow?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r834762736"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-920936096", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-920937321", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-920939507", "body": ""}
{"comment": {"body": "@davidkwlam this was the refactor I was talking about", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r834766475"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-920945756", "body": ""}
{"comment": {"body": "This is the actual Job class, currently lacking tests. I've tested it live and it works.\r\n\r\n@davidkwlam at the end of a successful install ingestion we need to signal PR ingestion to kick off for the set of repos in the org. We're getting into job scheduling territory here.\r\n\r\nThe quick and dirty thing would be to simply run PR ingestion on every repo in the whole system, with some continuation cursor tracked for each repo\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r834771088"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-922616897", "body": ""}
{"comment": {"body": "If you can just manually do this, I'd forget about adding the detection logic. DB drops should be rare.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r836062335"}}
{"comment": {"body": "Yeah the even quicker and dirtier approach would be to run it on all repos that are scum connected, skipping those where pr ingestion has already run (so constantly poll the repos table)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r836064053"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-923324379", "body": ""}
{"comment": {"body": "Yup fair. I'll add a comment to this class.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r836551459"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-923326542", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-923332468", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-923371804", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-923569000", "body": ""}
{"comment": {"body": "transaction block not necessary since the call will create one", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r836725037"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-923572160", "body": ""}
{"comment": {"body": "The transaction block is to pull out the team object from the RepoDAO", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r836727325"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/662#pullrequestreview-923572471", "body": ""}
{"title": "Sort threads by lastMessageCreatedAt", "number": 663, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/663", "body": "sort threads on the client to sort latest first (per last comment in https://github.com/NextChapterSoftware/unblocked/pull/646)\nsome small clean up items"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/663#pullrequestreview-920942016", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/663#pullrequestreview-920942124", "body": ""}
{"title": "Improve local dev experience", "number": 664, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/664", "body": "These should take some of the pain out of local testing and development. Take a look at the bottom of root README file for full list.\n\nAdded new make targets (described in root README) to help with local lint, build and test tasks. They also take care of DB drops between runs\nAdded a new docker compose file to run docker containers with all Kotlin services. It also takes care of mapping tasks on one endpoint just like we do in Dev\nAdded necessary config files for docker environment\nAdded environment file services.env to pass common env vars to local service containers launched by compose\nAdded nginx config and container  to take care of path mappings\nAlso changed ports in scmservice and pusherservice back to 8080 for consistency"}
{"comment": {"body": "Wondering if we could sneak a `flush-redis` make target in here with:\r\n\r\n`docker exec -it unblocked_redis_1 redis-cli FLUSHALL`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/664#issuecomment-1079310793"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/664#pullrequestreview-922097268", "body": ""}
{"title": "Add hover/selected thread styles", "number": 665, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/665", "body": "actual implementation of the sidebar detecting the webview panel state will require a more complicated implementation, leaving off for now\nthis will add a hover coloring state, the selected state classname is there but awaiting implementation"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/665#pullrequestreview-922127324", "body": ""}
{"title": "Adding APM health check paths to all service", "number": 666, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/666", "body": "Each service gets a subpath under /apm e.g /apm/pusherservice. We can use these paths to allow external traffic for /__deepcheck and /__shallowcheck for each service. \nI am planning to have external monitoring (Grafana checks) watch each service's availability. We need to make sure no user traffic is dropped during rollovers and keep monitoring our services for that.\nNote: Without a dedicated like the ones above ALB will always route checks to API service since that is our catch all service. With these path changes we can route each service's checks to it directly using the ALB."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/666#pullrequestreview-922190451", "body": ""}
{"title": "Move API service to its final resting place", "number": 667, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/667", "body": "Moved API service directory to projects/services\nUpdated gradle.settings with new project path\nMoved helm charts for each service to its respective directory under /projects\nUpdated helm ansible playbooks with new paths\nUpdated CI/CD workflow for services to use the new paths\nUpdated local env docker compose file with new path for Api service dockerfile\nUpdated make targets with new path for apiservice project"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/667#pullrequestreview-923492143", "body": "Thank you!!!"}
{"title": "Replace Message DataCacheStore with DataCacheStream", "number": 668, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/668", "body": "Replace Message DataCacheStore with streams.  Message objects now get an Author TeamMember annotation.  A lot of this is plumbing and mock changes because we're now passing around annotated ThreadAndParticipants and MessageAndAuthor objects in more places."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/668#pullrequestreview-923571327", "body": "Feels a lot cleaner."}
{"title": "increasing Kubernetes resources for all services", "number": 669, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/669", "body": "More horsepower"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/669#pullrequestreview-*********", "body": ""}
{"title": "Fix bug in check-for-changes job", "number": 67, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/67"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/67#pullrequestreview-*********", "body": ""}
{"title": "Dasboard: fix dev public path", "number": 670, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/670", "body": "I guess this never worked and nobody noticed?  This is preventing the DEV dashboard from loading the syntax hilighting library."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/670#pullrequestreview-*********", "body": ""}
{"title": "Implements findRepo", "number": 671, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/671", "body": "Summary\nWill accept the rootCommitSha but currently ignores it and only uses the repo url.  Performs access checks at the team level."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/671#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/671#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/671#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/671#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/671#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/671#pullrequestreview-923812782", "body": ""}
{"comment": {"body": "Let's update `FindRepoRequest` to have an optional rootCommitSha?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/671#discussion_r836894584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/671#pullrequestreview-923812834", "body": ""}
{"title": "Threads query does not filter on ThreadParticipant.modifiedAt", "number": 672, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/672", "body": "Its no longer necessary since:\n1) ThreadParticipant is never modified, just created\n2) ThreadParticipant is only created when a message is added to a thread, and adding a message to a thread will always update the Thread.lastMessageCreatedAt thus Thread.modifiedAt will be updated too\nThis was causing a bug where the getThreads operation was returning the incorrect results based on the X-Unblocked-If-Modified-Since header."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/672#pullrequestreview-923499826", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/672#pullrequestreview-923553352", "body": ""}
{"title": "Cleanup Makefile", "number": 673, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/673", "body": "the Phony attribute was getting unwieldy.\nsplit it so it's per make target.\nAlso, I don't think it's a good idea to do per project clean. The root clean should invoke all submodule cleans, and if it doesn't , I'll fix it.\nSame with lint and detekt."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/673#pullrequestreview-923411984", "body": ""}
{"comment": {"body": "@mahdi-torabi you know why you did it per subproject?\r\nThis shouldn't be necessary in gradle land.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/673#discussion_r836614194"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/673#pullrequestreview-923418260", "body": ""}
{"comment": {"body": "It noticed some build directories where not being cleaned. Checking again it seems that the clean was being done but some files (not jars or caches though) still remain but that should be fine. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/673#discussion_r836618920"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/673#pullrequestreview-923418638", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/673#pullrequestreview-923419205", "body": ""}
{"comment": {"body": "I'll address that. Thanks!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/673#discussion_r836619767"}}
{"title": "Simple styling for web extension sidebar", "number": 674, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/674", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/674#pullrequestreview-923462591", "body": ""}
{"comment": {"body": "The file being imported here (`Button.scss`) is empty, we should remove it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/674#discussion_r836649533"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/674#pullrequestreview-923464881", "body": ""}
{"title": "add more logging for helm deploys", "number": 675, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/675", "body": "Adding debug flag to helm command so we get more output when deployments fail\n\nUpdate: \nRemoved deployment sequencing change. Richie will add a delay to make sure other services wait while one is doing schema update."}
{"title": "Fix deleteMessage", "number": 676, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/676", "body": "We need to:\n1) Check that we're never allowing deleting a message if its the only message in a thread\n2) Update Thread.lastMessageCreatedAt\n3) Groom thread participants & thread unreads\n4) Don't actually delete the message content from db, just don't return it in the API on the outbound"}
{"comment": {"body": "Closing in favour of https://github.com/NextChapterSoftware/unblocked/pull/1781", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/676#issuecomment-1157166880"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/676#pullrequestreview-940209842", "body": ""}
{"comment": {"body": "Test comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/676#discussion_r848914883"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/676#pullrequestreview-940230080", "body": ""}
{"comment": {"body": "Another test comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/676#discussion_r848930874"}}
{"title": "Implement unreads into clients", "number": 677, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677", "body": "* Stream unread properties onto the ThreadAndParticipants model (we should probably rename this?? ThreadAggregate ?)\nNOTES:\n* I haven't added the badge to the unblocked sidebar icon for unreads (can do that in a separate PR)\n* [DO NOT MERGE] - this is waiting on the implementation of the /unreads pusher channel to test before merging"}
{"comment": {"body": "Test test @benedict-jw everyone else ignore this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#issuecomment-1083558913"}}
{"comment": {"body": "Again notifying @benedict-jw everyone else have a nice day \ud83c\udf66 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#issuecomment-1083579997"}}
{"comment": {"body": "[theme.light.txt](https://github.com/NextChapterSoftware/unblocked/files/8384381/theme.light.txt)\r\n\r\nTesting ignore this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#issuecomment-1083656184"}}
{"comment": {"body": "<img width=\"170\" alt=\"Screen Shot 2022-03-21 at 1 29 35 PM\" src=\"https://user-images.githubusercontent.com/2133518/160936501-fe5fdb3e-08bc-455d-8fcd-acbf9ff0c685.png\">\r\n\r\nMore stuff to ignore sorry", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#issuecomment-1083656862"}}
{"comment": {"body": "@matthewjamesadam I wonder if that would be handled by the useEffect block on web as well as the listener in the DiscussionThreadCommand on vscode. i.e. the code to update the thread from unread to read should run when the unread status changes when viewing the DiscussionThread views. Either way I'm happy to dig deeper once this first one goes in", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#issuecomment-1086266359"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#pullrequestreview-928152957", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#pullrequestreview-928161028", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#pullrequestreview-928167597", "body": ""}
{"comment": {"body": "This is a nice way of shimming the ID in here, it's a bit unfortunate that you have to kind of mock the original raw response with the amended values.  I'll think through if there might be a better way to support alternate ID values and transformed data for the streams...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#discussion_r840011148"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/677#pullrequestreview-928170453", "body": "I think we still might have a bit more work to fully support unread state -- I think this PR doesn't handle the case when you already have a thread view open, and another person adds a message to the thread.  In that case, the thread will appear as unread.  If you're currently viewing the thread, I think the thread should never appear as unread, but that will be a little tricky to implement and we can do that in a followup PR."}
{"title": "Temporary fix for app install id in fixtures", "number": 678, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/678"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/678#pullrequestreview-923565869", "body": ""}
{"title": "Improve GitHub regex to match on web paths", "number": 679, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/679"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/679#pullrequestreview-923664867", "body": ""}
{"title": "Paths Changes Filter requires checkout on push trigger", "number": 68, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/68", "body": "Fixes action failures on main:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/68#pullrequestreview-856014064", "body": "Is this going to end up checking out the repos twice?"}
{"title": "Resize kube nodes in dev", "number": 680, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/680", "body": "Had to resize Kube nodes in Dev to an issue with pod counts on nodes. EC2 instances have a limit on number of pods they can run ()\nThis was causing a lot of alarms and crashlooping pods\nNote: I have already deployed this to Dev Kube cluster"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/680#pullrequestreview-923779363", "body": ""}
{"title": "Remove DataCacheStore", "number": 681, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/681"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/681#pullrequestreview-923684051", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/681#pullrequestreview-923724703", "body": ""}
{"title": "Clean gradle dependencies to finish up API service migration", "number": 682, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/682", "body": "Removed all unused dependencies\nRemoved duplicate detekt.yaml file and modified gradle to use the file a few levels up the tree.\n\nbuilds clean locally."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/682#pullrequestreview-923718208", "body": ""}
{"title": "Polls installs every hour for org/repo updates", "number": 683, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/683"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/683#pullrequestreview-925059925", "body": ""}
{"title": "Expose APM endpoints for shallow checks", "number": 684, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/684", "body": "This PR adds an endpoint for each public service's APM checks. These checks are used by Grafana to monitor our infra availability. e.g to make sure we don't throw 502s during deployment rollovers.\nWe are only exposing shallow checks via Exact path matching. Deep checks are too expensive and someone could use them to DDoS our service."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/684#pullrequestreview-925007266", "body": ""}
{"title": "Revert \"Clean gradle dependencies to finish up API service migration\"", "number": 685, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/685", "body": "Reverts NextChapterSoftware/unblocked#682"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/685#pullrequestreview-923768262", "body": ""}
{"title": "Move to using a specialized config package shared across services", "number": 686, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/686", "body": "Weve had a longstanding problem where we were replicating config files across ervices.\nthis should not be done.\nTo avoid this:\n1. Create a config package\n2. All services are dependent on config package\n3. Any wonky overrides related to running in docker etc. should be done via environment variables.\nTESTING:\n1. Tested each service independent of next.\n2. Tested each service ran correctly via docker using \"run-local-stack\""}
{"comment": {"body": "> Nice! I think Mahdi was hoping for some per-service override capabilities, but this is what is needed for now to get ourselves unstuck\r\n\r\nYup.\r\nGoing to add that next.\r\nIt's pretty trivial to get working...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/686#issuecomment-1081215533"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/686#pullrequestreview-923810473", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/686#pullrequestreview-923815506", "body": ""}
{"comment": {"body": "I think the pusherservice configs might be broken. Probably best to C&P the scmservice config", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/686#discussion_r836896633"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/686#pullrequestreview-923816370", "body": ""}
{"comment": {"body": "It looks like both were broken. I mean neither managed to come up healthy ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/686#discussion_r836897302"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/686#pullrequestreview-923816566", "body": "Nice! I think Mahdi was hoping for some per-service override capabilities, but this is what is needed for now to get ourselves unstuck"}
{"title": "Update ThreadStore ForThread", "number": 687, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/687", "body": "Update DataCacheStream Typing to help include initializedState for ThreadStore ForThread"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/687#pullrequestreview-923835009", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/687#pullrequestreview-923835456", "body": ""}
{"title": "support persistent data in  run-local-stack", "number": 689, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/689", "body": "Moving forward to clean database in local stack we need to run make drop-database explicitly.\nThis should make life easier for frontend folks when using local stack."}
{"comment": {"body": "I just realized that this change is not needed. The current setup already supports it. When rebuilding binaries we have no choice but to drop the data otherwise tests fail which applies to `build-local-stack` as well since it calls the build task. \r\n\r\nFor local use, after the initial build users can just keep running `run-local-stack`. Since it runs in foreground Ctl+c takes care of tearing it down and you can run `run-local-stack` again over and over. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/689#issuecomment-1081222423"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/689#pullrequestreview-923830166", "body": ""}
{"title": "minor: gradle updates", "number": 69, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/69"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/69#pullrequestreview-856088660", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/69#pullrequestreview-856088735", "body": ""}
{"comment": {"body": "I LOVE YOU", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/69#discussion_r787232603"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/69#pullrequestreview-856088774", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/69#pullrequestreview-856089889", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/69#pullrequestreview-856103774", "body": ""}
{"title": "Improve logging", "number": 690, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/690"}
{"title": "update", "number": 691, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/691"}
{"title": "Use elasticache master/replica client", "number": 692, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/692", "body": "Per: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/692#pullrequestreview-923899154", "body": ""}
{"comment": {"body": "I think we need to use RedisClusterconfiguration.\r\n\r\nhttps://docs.spring.io/spring-data/redis/docs/current/api/org/springframework/data/redis/connection/RedisClusterConfiguration.html\r\n\r\nhttps://github.com/spring-projects/spring-data-redis/issues/1470", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/692#discussion_r836961165"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/692#pullrequestreview-923907049", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/692#pullrequestreview-923907969", "body": ""}
{"comment": {"body": "I think the MasterReplica helper drives this, but could be wrong. I'll review before merging", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/692#discussion_r836967530"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/692#pullrequestreview-923913270", "body": ""}
{"comment": {"body": "Actually let's merge this now to see if it fixes things. I'll follow up this evening", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/692#discussion_r836970881"}}
{"title": "Generalize service health checks", "number": 693, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/693", "body": "Remove code dupe.\nMove to modern java arguments (probably should dedupe this as well)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/693#pullrequestreview-923875037", "body": ""}
{"comment": {"body": "Good idea", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/693#discussion_r836943121"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/693#pullrequestreview-923875341", "body": ""}
{"title": "[BUG FIX] Fix redis cluster handling and increase probe durations and initial check times", "number": 697, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/697", "body": "This pr addresses the following:\n1. For redis clusters, we should be using RedisClusterClients.\n2. Because memory/cpu resources are currently limited for services, the boot up time is slow and the probes fail. We need to increase probe delays and up the limits of some of the services.\nTesting:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/697#pullrequestreview-923983653", "body": "We are blessed to have Rashin the Machine back"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/697#pullrequestreview-923989974", "body": ""}
{"comment": {"body": "Such a shame that the client library can't figure this out based on the configuration node response", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/697#discussion_r837029985"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/697#pullrequestreview-923990839", "body": ""}
{"comment": {"body": "AgreeD!\r\nI looked man... :(", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/697#discussion_r837030343"}}
{"title": "Fix redis health check warning", "number": 698, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/698"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/698#pullrequestreview-923952836", "body": ""}
{"title": "Revert \"Use elasticache master/replica client\"", "number": 699, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/699", "body": "Reverts NextChapterSoftware/unblocked#692\nDidn't work: \ni.l.c.RedisException: Master is currently unknown: [RedisMasterReplicaNode [redisURI=rediss://unblocked:********************@clustercfg.rer1l154blnqa1l2.eokkoa.usw2.cache.amazonaws.com:6379, role=REPLICA]]\nWill try cluster config"}
{"comment": {"body": "Will close this when Rashin wins: https://github.com/NextChapterSoftware/unblocked/pull/697", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/699#issuecomment-**********"}}
{"title": "vscode Green", "number": 7, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/7"}
{"title": "refactor tests", "number": 70, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/70", "body": "(just breaking up a larger PR. nothing interesting here.)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/70#pullrequestreview-856090921", "body": ""}
{"comment": {"body": "I fucking love this construction technique.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/70#discussion_r787234299"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/70#pullrequestreview-856091002", "body": ""}
{"title": "resize pods", "number": 700, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/700"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/700#pullrequestreview-924051758", "body": ""}
{"title": "Add SourceMark agent gRPC server", "number": 701, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701", "body": "Not the full implementation here, just getting the working bits added first while we work on the proto spec."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#pullrequestreview-924879948", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#pullrequestreview-924881743", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#pullrequestreview-924883399", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#pullrequestreview-924901010", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#pullrequestreview-925191391", "body": ""}
{"comment": {"body": "Are we keeping this around for testing?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#discussion_r837878118"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#pullrequestreview-925192416", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#pullrequestreview-925194081", "body": ""}
{"comment": {"body": "Yeah we can delete", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#discussion_r837880072"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#pullrequestreview-925194531", "body": ""}
{"comment": {"body": "I'll remove it in another PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/701#discussion_r837880371"}}
{"title": "Cleanup code dupe for service health checks", "number": 702, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/702", "body": "The health checks for all the services was essentially shared but duped.\nTo avoid any further code dupe, we are moving to a shared health checker across all services."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/702#pullrequestreview-925001358", "body": "nice"}
{"title": "Improve local development story", "number": 703, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/703", "body": "Changed volume names in local stack docker compose file to avoid conflicts with the ones created and used for Gradle tests\nRenamed/added make targets to support both full (with tests) and fast (no tests) builds\nFast builds retain db content (meant for front-end engineering)\nFull builds cleans db (meant for local full stack engineering)\nAdded names to docker images built by compose\nUpdated README to reflect these new changes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/703#pullrequestreview-925186781", "body": ""}
{"comment": {"body": "These seem wrong?  The build and run commands are the same?\r\n\r\nAnd the 'front end development run' command looks wrong?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/703#discussion_r837874805"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/703#pullrequestreview-925187435", "body": " !"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/703#pullrequestreview-925287148", "body": ""}
{"comment": {"body": "Oops! Fixed it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/703#discussion_r837947496"}}
{"title": "Deploy to prod again!", "number": 704, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/704", "body": "Changed prod node size to allow for more pods\nIncreased  pod replica counts so we could see multi-pod in action\nModified github actions workflows to enable prod deploys\n\nNOTE: I have already dropped prod DB so services would hopefully come up clean"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/704#pullrequestreview-925130885", "body": ""}
{"title": "Add ability to have per-environment log configuration", "number": 705, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/705", "body": "This pr addresses the following:\n1. We now have the ability to specify how we want to log per environment.\n2. We add a local logback config that does not log to logz.io\n3. We add a local logback-test config that does not log to logz.io.\nTESTING:\n1. Validated that logback-local was being loaded.\n12:31:34,359 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-local.xml] at [file:/Users/<USER>/chapter2/unblocked/projects/config/build/resources/main/logback-local.xml]\n\nValidated that for tests logback-test was being loaded.\n\n12:33:06,151 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [jar:file:/Users/<USER>/chapter2/unblocked/projects/config/build/libs/config-test-1.0.0.jar!/logback-test.xml]"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/705#pullrequestreview-925139334", "body": "Thanks for taking care of this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/705#pullrequestreview-925145045", "body": ""}
{"title": "Rename APM health path and moved it under /api", "number": 706, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/706", "body": "Modified all instances of /apm/SERVICE_NAME to /api/health/SERVICE_NAME. This is because Cloudfront would return an error when asking for /apm. We only have 3 CF origins namely / for landing page, /dashboard for static dashboard site and /api for all traffic that needs to be forwarded to ALB\nReflected the change above in ALB configuration"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/706#pullrequestreview-925177951", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/706#pullrequestreview-925178816", "body": ""}
{"title": "Cleanup config", "number": 707, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/707"}
{"title": "Get port from command line", "number": 708, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/708"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/708#pullrequestreview-925226636", "body": ""}
{"title": "prod has more pods and needs more time to rollover", "number": 709, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/709"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/709#pullrequestreview-925238092", "body": ""}
{"title": "Introduce team membership models", "number": 71, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/71", "body": "new model with tests\n\n"}
{"comment": {"body": "Is it valid for a user to have no team? Our product is fundamentally tied to org/repo membership, since teams and users are bound to a provider. In other words, a user and team must have _at-least-one_ identity provider, and _at-test-one_ of those providers has to be the same between a team and a user. Do we need to model that?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/71#issuecomment-**********"}}
{"comment": {"body": "> Is it valid for a user to have no team?\r\n\r\nYes. Valid to have 0, 1, or many teams.\r\n\r\n> Our product is fundamentally tied to org/repo membership, since teams and users are bound to a provider. In other words, a user and team must have _at-least-one_ identity provider, and _at-test-one_ of those providers has to be the same between a team and a user. Do we need to model that?\r\n\r\nI think this is modeled by the `TeamMembership`, which i\u2019ll rename `TeamMember`. So if you replace _TeamMember_ for _User_ in your comment then it\u2019s true. In other words: chat participants are team members, not users; sourcemarks are created by team members, not users; etc.\r\n\r\nI\u2019ll rename some stuff. lmk if this makes sense?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/71#issuecomment-**********"}}
{"comment": {"body": "> I\u2019ll rename some stuff. lmk if this makes sense?\r\n\r\n@pwerry updated in https://github.com/Chapter2Inc/codeswell/pull/71/commits/2f1d5e837185f1a8abda1a0853e6b28f21238893", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/71#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/71#pullrequestreview-857118760", "body": "Makes more sense now thanks! Please update diagram in the description"}
{"comment": {"body": "Hurray for engagement metrics!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/71#discussion_r787976437"}}
{"comment": {"body": "I'll add a provider parameter to login", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/71#discussion_r787977858"}}
{"title": "Make sure service name is included in logs", "number": 710, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/710"}
{"title": "Fix platform.version log field", "number": 711, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/711"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/711#pullrequestreview-925263503", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/711#pullrequestreview-925263869", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/711#pullrequestreview-925265603", "body": ""}
{"title": "Implements video participant heartbeats to support video cleaner service", "number": 712, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/712"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/712#pullrequestreview-925268607", "body": ""}
{"comment": {"body": "prefer `single`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/712#discussion_r837933608"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/712#pullrequestreview-925269676", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/712#pullrequestreview-925272582", "body": ""}
{"comment": {"body": "I tend to as well but the idea is to bubble a 404 via the exception, which we can't do with `NoSuchElementException`. Would have to wrap and re-throw. Not sure which is better? Or is the idea to guard against a multi-match?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/712#discussion_r837936494"}}
{"title": "minor doc changes", "number": 713, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/713"}
{"title": "Local logging shows metadata in yellow", "number": 714, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/714", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/714#pullrequestreview-925329797", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/714#pullrequestreview-925333085", "body": ""}
{"comment": {"body": "I've always had a great appreciation for your choice in colors richie.\r\nThis pr makes me happy.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/714#discussion_r837981057"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/714#pullrequestreview-925333113", "body": ""}
{"title": "Add a heartbeat mechanism to the SourceMark agent", "number": 715, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/715"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/715#pullrequestreview-925350412", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/715#pullrequestreview-925353498", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/715#pullrequestreview-925356543", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/715#pullrequestreview-925579807", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/715#pullrequestreview-926403414", "body": ""}
{"title": "SourceMark agent passes teamId to API service", "number": 716, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/716", "body": "Get the API client working again"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/716#pullrequestreview-925354241", "body": ""}
{"title": "Allow wildcard hostnames for given schemes", "number": 717, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/717", "body": "Jeff this will do what you need. Use with care..."}
{"comment": {"body": "Going to merge this in first.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/717#issuecomment-1082592083"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/717#pullrequestreview-925474586", "body": " Should only be used for the chrome extension."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/717#pullrequestreview-926294161", "body": ""}
{"comment": {"body": "I would say that the namespace feels off. This is a ktor plugin.\n\nMaybe next chapter software.ktor.plugins\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/717#discussion_r838664580"}}
{"title": "Recording service", "number": 718, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718", "body": "Summary\nImplements recording service DB interactions without Agora integration. Agora API calls will come in a follow-up PR.\nThe plan is to get the db hooks and background clean tasks in place and then layer on the Agora bits. There is not upload task here - Agora handles that for us."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-925509836", "body": ""}
{"comment": {"body": "Sorry for this beast, I'm going to dice it up when we bring in Agora integration", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838111727"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-925510574", "body": ""}
{"comment": {"body": "This is fragile until we have reliable events. If the processing step fails we'll be stuck here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838112317"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-925593378", "body": ""}
{"comment": {"body": "not for now, but we need a general-purpose way of preventing this attack. otherwise we have to dup that code in 100s of places.\r\n```\r\nGET /teams/:attackingTeamId/sub-resources/:victimSubResourceId\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838803227"}}
{"comment": {"body": "assuming this block is for idempotency.\r\n\r\nthere's a problem right now which is that the new recording is created after this transactional block. for this to work as intended the new recording DB object must be created as part of this block. the consequence is that two agora recordings could be started if the client retries quickly enough.\r\n\r\nnot sure if you want to fix this now, but one approach would be to stage the recording creation:\r\n\r\n- STAGE 1: idempotently create recording DB object in this trx, with an initial state of `CREATING`. this prevents creating duplicate DB records.\r\n- STAGE 2: start agora recording -- can this be done idempotently on Agora side if we use the same external ID?\r\n- STAGE 3: transactionally save the external ID, and update the recording state to `RECORDING`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838819734"}}
{"comment": {"body": "Will this be called by the `stopRecording` operation? If so, then you're right, since the API service could be killed etc, even if we have graceful shutdown. Better to have a kafka event for sure.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838827890"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926527756", "body": ""}
{"comment": {"body": "oh, I guess row-level security solves this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838829390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926544627", "body": ""}
{"comment": {"body": "I think this is best left to when we introduce a generalized eventing system, but in the short term this code could be moved to a background service that handles video state processing via a redis lock. It polls for recordings in the `Processing` state", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838841509"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926545655", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926546341", "body": ""}
{"comment": {"body": "Yup we'll have to audit and dump all of these cross-team checks once we solve isolation", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838842768"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926547451", "body": ""}
{"comment": {"body": "agree: best left to when we introduce a generalized eventing system", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838843533"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926562331", "body": ""}
{"comment": {"body": "The `externalResourceId` comes from Agora during the `aquire` phase, so this would have to be done in STAGE 1. This proposal makes sense, will adopt.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r838854351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926796418", "body": ""}
{"comment": {"body": "Updated - does this look right now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r839018576"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926796920", "body": ""}
{"comment": {"body": "This is the idempotent part", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r839018968"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926797122", "body": ""}
{"comment": {"body": "And this should prevent racing to create multiple instances", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r839019120"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926800066", "body": ""}
{"comment": {"body": "This call is not idempotent, but the new resource id doesn't invalidate the old one. One possible optimization is to add the resource Id outside the current transaction, but then we can't add a unique constraint to the resourceId field", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r839021276"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-926851449", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-929625492", "body": ""}
{"comment": {"body": "@richiebres @davidkwlam @rasharab this is a necessary change, but it's going to bork the DB. How do we deal with this? \r\n\r\nAlternative is to ignore uniqueness. Removing an index _shouldn't_ cause issues...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r841158757"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-931028663", "body": ""}
{"comment": {"body": "In theory the DDL change should only be blocked if there are non-unique value in the existing VideoRecording table, but not sure if exposed will be more conservative and reject.\r\n\r\nYou could test by syncing to `main` blowing away the DB; then syncing to this PR and running the app locally to migrate (without running tests are the test will drop the DB entirely). If it runs ok, then it will also run ok in DEV, and PROD.\r\n\r\nIf that doesn't work, then not opposed to dropping the DEV and PROD dbs again.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r842197061"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-931038702", "body": "great tests"}
{"comment": {"body": "optional: _might_ be a better pattern to find by recordingId and teamId. Then we don't need this ForbiddenException. This approach will be closer to row-level security approach. deferrable, since we'll be auditing all SQL calls eventually anyway.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r842201730"}}
{"comment": {"body": "optional: totally safe to move this outside, before this transaction block. to simply.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r842202948"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-931066151", "body": ""}
{"comment": {"body": "Yeah I'm going to come back to this an optimize the hell out of it once we do row-level security stuff. Fetching all participants just to find the channel host is dumb. Could have just done a creator comparison", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r842220096"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-931066567", "body": ""}
{"comment": {"body": "Yup I'll come back to this when we start that process. No harm here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r842220449"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-931070330", "body": ""}
{"comment": {"body": "This is what Exposed logged when I attempted to upgrade:\r\n```\r\n16:46:36 | WARN  | Exposed: Indices missed from database (will be created): \r\n{  } \r\n16:46:36 | WARN  | Exposed: \t\tUnique Index 'videorecordingmodel_externalresourceid_unique' for 'videorecordingmodel' on columns com.nextchaptersoftware.db.models.VideoRecordingModel.externalResourceId \r\n{  } \r\n16:46:36 | WARN  | Exposed: Indices exist in database and not mapped in code on class 'videorecordingmodel': \r\n{  } \r\n16:46:36 | WARN  | Exposed: \t\tUnique Index 'videorecordingmodel_externalrecordingid_externalresourceid_uniq' for 'videorecordingmodel' on columns com.nextchaptersoftware.db.models.VideoRecordingModel.externalRecordingId, com.nextchaptersoftware.db.models.VideoRecordingModel.externalResourceId \r\n{  } \r\n16:46:38 | DEBUG | c.n.p.Monitoring: Application started: io.ktor.server.application.Application@2349f14d \r\n{  } \r\n```\r\n\r\nIt didn't crash, but does this mean the previous index is still there? If so won't this break since one of the fields is now nullable?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r842223498"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#pullrequestreview-931079761", "body": ""}
{"comment": {"body": "looks good. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/718#discussion_r842230446"}}
{"title": "Add richie CIDR to WAF allowlist", "number": 719, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/719"}
{"comment": {"body": "@mahdi-torabi should we consider moving away from blocking ips? \r\nI say this because a lot of us do not have static ips and it's just getting annoying managing this per team member.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/719#issuecomment-1083323356"}}
{"comment": {"body": "@rasharab I'd love to move away from IP filtering. This is a temp thing until we go beta. We do already have a special header that if set would let your requests pass through WAF. Not sure how we can use that in our daily workflow though", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/719#issuecomment-1083460332"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/719#pullrequestreview-926348368", "body": ""}
{"comment": {"body": "this is mine", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/719#discussion_r838702527"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/719#pullrequestreview-926350026", "body": ""}
{"comment": {"body": "this is Jeff's IP copied from the DEV config, for consistency. he forgot to update PROD config when he last edited", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/719#discussion_r838703730"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/719#pullrequestreview-926534279", "body": ""}
{"title": "Hide annoying KtorExperimentalLocationsAPI warnings", "number": 72, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/72", "body": "```\n\nTask :apiservice:compileKotlin\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/api/AuthApiDelegateImpl.kt: (10, 29): This locations API is experimental. It could be changed or removed in future releases.\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/api/AuthApiDelegateImpl.kt: (11, 91): This locations API is experimental. It could be changed or removed in future releases.\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/plugins/Routing.kt: (11, 5): This locations API is experimental. It could be changed or removed in future releases.\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/plugins/Routing.kt: (11, 13): This locations API is experimental. It could be changed or removed in future releases.\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/plugins/Routing.kt: (15, 9): This locations API is experimental. It could be changed or removed in future releases.\n```\n\nFrom: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/72#pullrequestreview-*********", "body": ""}
{"title": "Move SM app to separate gradle subproject", "number": 720, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/720"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/720#pullrequestreview-*********", "body": ""}
{"title": "Gradle refactor", "number": 721, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/721", "body": "Trying to get the sub-project configs to be as terse as possible. Lot's of duplication right now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/721#pullrequestreview-*********", "body": "I'm just approving this out of an abundance of @richiebres awesomeness....\nThank you!!!"}
{"title": "Add nginx proxy for services running in intellij", "number": 722, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/722"}
{"title": "Video recording api", "number": 723, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/723", "body": "This will be changing as I address feedback from https://github.com/NextChapterSoftware/unblocked/pull/718"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/723#pullrequestreview-931063848", "body": ""}
{"title": "Hook up ability to delete messages", "number": 724, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724", "body": "NOTES:\n* Context menu styling is just a simple box for now since there are no mocks for this yet \n* For now, we hide the context menu entirely if it is the anchor message (ie the first message) \n    * This will be updated once we add more functionality to the context menu\n* Temporarily(?) using the web window.confirm dialog - can probably replace with our own popup service once it is implemented\n* There's a backend issue with deleting the last message, but is to be handled by https://github.com/NextChapterSoftware/unblocked/pull/676\nThis PR also addresses a few issues:\n* There was a race condition between the two places calling initializeAuth - removed the call in the AuthSidebarWebviewProvider\n* Update useStream to take a useMemo-like protocol to prevent it from running on every render"}
{"comment": {"body": "Updated to use the headlessui Popover as well as the Dialog for the modals ~(the latter only lives in `web/` right now since I wrote a context API for handling modals)~ (moved to shared)\r\n![image](https://user-images.githubusercontent.com/13431372/161162317-b39431ad-532d-4ce2-9f70-56b05e38668a.png)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-926825789", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-927869202", "body": ""}
{"comment": {"body": "I'm assuming we're filtering on the backend but this is just to remove items immediately?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r839800909"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-927875163", "body": ""}
{"comment": {"body": "If not, we should update backend to handle the filtering", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r839805267"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-927878263", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-927878646", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-927922855", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-927923208", "body": ""}
