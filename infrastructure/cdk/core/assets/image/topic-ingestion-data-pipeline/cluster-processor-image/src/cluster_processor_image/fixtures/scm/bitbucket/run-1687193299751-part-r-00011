{"title": "Removed unnecessary pip install in coverage", "number": 720, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/720", "body": "run_coverage.sh unnecessarily installed a requirements.txt file using pip.\nThe coverage doesnt need it, and it broke another unrelated build"}
{"title": "Feature/BIS-7260 2nd cfo instance is zero slope", "number": 721, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/721", "body": "Add 2nd CFO model instance: 0 phone slope model\n\nIt forces phone temperature to be 0 in this model and works with the prior to force 0 phone slope\n\n\n\nGet rid of unused code in dialog testing agent and unused variable (channels_sum)\n\nLast rename of submodels to instances\n\n"}
{"comment": {"body": "Why do we need to have it as part of the model?  \nCan\u2019t we just init the two models with different priors?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/721/_/diff#comment-126593166"}}
{"title": "lower 2 lobes threshold in training", "number": 722, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/722", "body": ""}
{"title": "Training will perform validation classifications at the end to deactivate evil features", "number": 723, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723", "body": "Training validation classifications\nrefactor timing unittests\n\n"}
{"comment": {"body": "Beautiful work! Very professional! ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126617258"}}
{"comment": {"body": "This is external interface - is there a better name?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126709859"}}
{"comment": {"body": "Is it enough for 10 classifications?\n\nLets make the number of needed packets to be 1000 just to be on the safe side.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126933123"}}
{"comment": {"body": "Can this logic be inside `fingerprinter.c`? This is relevant to many other projects, such as testing agent and standalone master", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126937680"}}
{"comment": {"body": "*`fingerprinting.c`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126937692"}}
{"comment": {"body": "I\u2019m aware. For now they will all still work, they will just not show the hidden classifications. I hope I\u2019ll be able to find time to make it work on them as well", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126937835"}}
{"comment": {"body": "Right now classifications are just 25 packets, so this is on the safe side.  \n  \nAlso, we never guaranteed we\u2019ll finish training in 1500 packets, so if it takes a few more packets, no harm done \\(1500 packets should be just 150 seconds at 10 p/s\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126937852"}}
{"comment": {"body": "We raise this whenever the internal classification function returns an error - we prefer Bosch don\u2019t know we perform classifications during training - so we can\u2019t let them know the actual error.  \nWhat other name do you suggest?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126937874"}}
{"comment": {"body": "_bosch\\_integration\\\\python\\_tools\\\\system\\_tests\\\\devices\\_standalones\\\\master\\_standalone\\\\fingerprinter\\_api.py_ and _bosch\\_integration\\\\python\\_tools\\\\system\\_tests\\\\devices\\_standalones\\\\master\\_standalone\\\\fingerprinter\\_loop.py_ would appreciate an update as well :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126939425"}}
{"comment": {"body": "It will work for now, it will just not show classifications, I\u2019ll try to find some time later to do it, but not in this PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126939992"}}
{"comment": {"body": "It wouldn\u2019t work since its defined and API changed:\n\n```\nfingerprinting_training_callback_t = ctypes.CFUNCTYPE(None,\n                                                      ctypes.POINTER(pyf.pyf_st_int.Levl_ModelProgress_internal_st),\n                                                      ctypes.c_int32,  # pyf.pyf_st.Levl_TrainRslt_et,\n                                                      ctypes.c_uint32,\n                                                      ctypes.c_int32,\n                                                      )\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126940033"}}
{"comment": {"body": "Oh didn\u2019t realize it was actually using ctypes\u2026. I\u2019ll take a look", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/723/_/diff#comment-126940237"}}
{"title": "Feature/BIS-7226 hill more outliers", "number": 724, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/724", "body": "Implement more hill outliers\n"}
{"title": "Feature/BIS-7407 add non participation stats for", "number": 725, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/725", "body": "Collect non participation stats for each feature\n"}
{"title": "Auto connect and disconnect the device from the VM for macs", "number": 726, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/726", "body": ""}
{"title": "This used to be a PR for 0 phone slope, but then it took an arrow to its knee", "number": 727, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/727", "body": "~~Add 2nd CFO model instance~~\nAdd code for CFO 0 phone slope feature with an MATCH OR relationship with CFO feature, but nothing computes it for now. Next PR would introduce actual usage.\n\nGet rid of code:\n\nGet rid of unused code in dialog testing agent\nchannel_sum\n\n\n\nUpdate monitor to support new tier of CFO models\n\nFix hydra-ctypes relationship and add unittests\n\n"}
{"title": "Add another large scale tests with -wifi-all on bosch_boards", "number": 728, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/728", "body": ""}
{"title": "Fix CFO draw bug", "number": 729, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/729", "body": ""}
{"title": "Demo app now supports Nexus, Galaxy S8 and Galaxy S9 phones", "number": 73, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/73", "body": ""}
{"comment": {"body": "`CheckHeatingChange`\\(\\) should receive the battery temperature and not the CPU temperature", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/73/_/diff#comment-77706314"}}
{"title": "Yet another 0 phone slope PR", "number": 730, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/730", "body": "Introduce 2nd instance of CFO model: 0 phone slope\n\nImplemented with simple no wifi model and simple wifi model (duplication of no wifi model)\nRefactored to duplicate as little code as possible\nReverted CFO draw to 2 instances\nCurrent model is called regular model\n\n\n\nFix some warning in hill code region\n\n\n"}
{"comment": {"body": "Looks good!  \nIs there any reason while the build fails? I don\u2019t see any failing tests..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/730/_/diff#comment-126929435"}}
{"comment": {"body": "```\n~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\nregression tests - online training is a Catch v2.7.2 host application.\nRun with -? for options\n\n-------------------------------------------------------------------------------\nregression tests - online training\n-------------------------------------------------------------------------------\n/root/build/libfingerprinting/tests/regression/main_online_training.cpp:619\n...............................................................................\n\n/root/build/libfingerprinting/tests/regression/main_online_training.cpp:410: FAILED:\n  REQUIRE( classify_res == LEVL_CLASSIFY_NOT_MATCH )\nwith expansion:\n  0 == 1\n  \n===============================================================================\ntest cases:       1 |       0 passed | 1 failed\nassertions: 1564912 | 1564911 passed | 1 failed\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/730/_/diff#comment-126929624"}}
{"title": "Add CFO Channel slope to failing features list in monitor", "number": 731, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/731", "body": ""}
{"title": "Add quick large scale pipeline", "number": 732, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/732", "body": ""}
{"title": "Feature/BIS-7304 lo leakage test for relay detec", "number": 733, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/733", "body": "Added lo leakage to feature list.\nAdded LO leakage feature for relay detection. fft should be validated\nUpdated external struct sizes and solved another conflict\nAll issues with structs integrity were fixed and now nothing can go wrong\nUpdated thresholds created in training to support some extreme non-relay packets\nFixed unit test to include lo leakage in the group of features that require full training session (1500 packets).\n\n"}
{"comment": {"body": "Please change this to our standard LEVL header", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/733/_/diff#comment-126942236"}}
{"comment": {"body": "You probably should zero your features fields like all the other features do", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/733/_/diff#comment-126942256"}}
{"comment": {"body": "Why move all features index \\(you need to change the indexes in the python also\\) and not just add it to the end?\n\nAnyway this is not a critical comment, not must to fix", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/733/_/diff#comment-126942300"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc}  Please fix the remaining comments", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/733/_/diff#comment-127001937"}}
{"title": "Allow both o-train and o-train-from-left-training; limit cpus to 48; add must-participate flag", "number": 734, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/734", "body": "Allow both o-train and o-train-from-left-training to run together\nLimit cpus used to 48\nAdd must-participate flag for features that must participate in decisions so classification will be considered in results\n\n"}
{"title": "Change AGC level to 4-6 for instfreq", "number": 735, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/735", "body": ""}
{"comment": {"body": "```python\nchannels = tuple(range(INSTFREQ_MIN_CHANNEL , INSTFREQ_MAX_CHANNEL +1))\nagcs = tuple(range(INSTFREQ_MIN_AGC, INSTFREQ_MAX_AGC+1))\n```\n\nSo that you don\u2019t have to modify this code again.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/735/_/diff#comment-126942033"}}
{"comment": {"body": "Build already passed, thank you though", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/735/_/diff#comment-126942639"}}
{"title": "Fix wifi flag not created correctly for 0 phone slope", "number": 736, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/736", "body": ""}
{"title": "Bugfix/crssi staturation", "number": 737, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/737", "body": "Fixed CRSSI window getting stuck\nSmaller crssi progress\n\n"}
{"comment": {"body": "What\u2019s the reason for changing it to double?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/737/_/diff#comment-126945585"}}
{"comment": {"body": "We observed a loss of precision", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/737/_/diff#comment-126945595"}}
{"title": "Quick s3", "number": 738, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/738", "body": ""}
{"comment": {"body": "Reuse the S3 client so enumerating etags is faster ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/738/_/diff#comment-126945695"}}
{"comment": {"body": "Great! :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/738/_/diff#comment-126945701"}}
{"title": "Feature/new sigma", "number": 739, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739", "body": "New sigma filtering model."}
{"comment": {"body": "Best EM implementation I\u2019ve ever seen. @{5b6154f66366b42ca0def662} , look at this majestic creature!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127049921"}}
{"comment": {"body": "What\u2019s the runtime with this additional dc removal?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127050172"}}
{"comment": {"body": "And It\u2019s also super fast.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127050676"}}
{"comment": {"body": "What\u2019s needed to remove the xfails?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127050920"}}
{"comment": {"body": "I got around 50ms-54ms for feature extraction, but I do not know if these 4ms are added because of me or since I\u2019ve merged with other features in the develop branch.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127051246"}}
{"comment": {"body": "@{5bdac2ad92e2727e0939d08b}  and I need to investigate these recordings.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127052834"}}
{"comment": {"body": ":laughing: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127056207"}}
{"comment": {"body": "Does @{5bdac2ad92e2727e0939d08b} know that you use the biased standard deviation estimation?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127057587"}}
{"comment": {"body": "From what i\u2019ve seen, the biased version is similar to what np.std does, this was one of the inconsistencies that we\u2019ve found while comparing C and Python", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127092362"}}
{"comment": {"body": ":heart_eyes: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/739/_/diff#comment-127248733"}}
{"title": "Feature/restore static analysis", "number": 74, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/74", "body": "restoring static analysis with updated KW server IP address\nfix issues\nfix jenkins error\nupdating KW IP\ncorrect IP in another place"}
{"comment": {"body": "Looks good!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/74/_/diff#comment-77834793"}}
{"title": "Removed usage of a removed parameter from lib.configure_features", "number": 740, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/740", "body": ""}
{"title": "Added connection playback to the master simulator", "number": 741, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/741", "body": ""}
{"comment": {"body": "I don\u2019t think that we should endorse genocide ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/741/_/diff#comment-126977361"}}
{"comment": {"body": "The Israeli cyber industry has no moral compass", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/741/_/diff#comment-126981214"}}
{"title": "Feature/BIS-7475 create instfreq dataset and run", "number": 742, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/742", "body": "Add a test for instfreq with wifi all and online train (from rest of train pickle and from other pickles) - with a flag that dismisses all classifications in which InstFreq did not participate (so results include 100% participation).\nAlso add comments to clarify test sections."}
{"title": "Lowered CRSSI thersholds", "number": 743, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/743", "body": ""}
{"title": "Feature/make sure st works", "number": 744, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/744", "body": "Increase dumper size\nFix timestamp factor\nHandle EEPROM full gracefully\nUpdate packets storer to have a packet pool to hold packets until the holy quadruplet is available (captured packet, feature extraction, metadata and progress)\n\n"}
{"comment": {"body": "? `raise NotImplementedException`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/744/_/diff#comment-127014387"}}
{"comment": {"body": "Done in 845d23950c1089c010740ca59d6e058e46737d7f", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/744/_/diff#comment-127018537"}}
{"title": "Feature/cfo wifi finalize temperature based only", "number": 745, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/745", "body": "CFO wifi major update is based on new temperatures (and not just new packets)\nCFO wifi: also validate that wifi progress has new packets and not just reload of the model stored packets\n\n"}
{"title": "Fix order of features", "number": 746, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/746", "body": "Instfreq should be at position 7, lo_leakage at position 10. NUM_VOTING_FEATURES is 5."}
{"comment": {"body": "~~Update monitor accordingly~~  Nevermind", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/746/_/diff#comment-127049578"}}
{"comment": {"body": "No need. The numbers of the features remain the same.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/746/_/diff#comment-127050092"}}
{"title": "CFO Channel Slope to work only when wifi is off", "number": 747, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/747", "body": ""}
{"comment": {"body": "Also update the `cfo_channel_slope_draw.py` for the monitor", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/747/_/diff#comment-127063716"}}
{"comment": {"body": "Good catch - thanks", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/747/_/diff#comment-127064188"}}
{"title": "Added LO leakage failures to monitor display", "number": 748, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/748", "body": ""}
{"title": "Added pytest to monitor requirements.txt", "number": 749, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/749", "body": ""}
{"title": "Feature/BIS-234 Klocwork & code metrics", "number": 75, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/75", "body": "trying klocwork custom config\nklocwork: removing import-config\nextracting metrics failures\npython script messes up the URL\nadded recursive function to test CI\n\nRevert \"added recursive function to test CI\"\nThis reverts commit 3e7632249f31ce357064c9a9b226bf0870cb6425.\n\n\nfix merge"}
{"comment": {"body": "The PR looks OK, but the build failed - is this a problem in the PR?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/75/_/diff#comment-77835024"}}
{"comment": {"body": "There are 11 open issues regarding code metrics: [http://*************:8080/blue/organizations/jenkins/bosch\\_integration/detail/PR-75/1/tests](http://*************:8080/blue/organizations/jenkins/bosch_integration/detail/PR-75/1/tests)\n\nI didn\u2019t fix them", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/75/_/diff#comment-77836491"}}
{"comment": {"body": "I fixed some of the errors :slight_smile: \n\nTo fix the others we need to go a bit more work.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/75/_/diff#comment-78031030"}}
{"comment": {"body": "Bless you", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/75/_/diff#comment-78032909"}}
{"title": "Instfreq doesn't need so many packets! - Amir Sivan, 2019", "number": 750, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/750", "body": ""}
{"title": "Monitor now displays which module triggered an update", "number": 751, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/751", "body": ""}
{"comment": {"body": "Good job!\n\nMaybe also add an assertion or some other hardcodeness that `voting_table_index_et` is corresponding with this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/751/_/diff#comment-127163431"}}
{"comment": {"body": "Make sure to also update standalone master API and in `\"bosch_integration\\python_tools\\system_tests\\devices_standalones\\master_standalone\\fingerprinter_api.py\"`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/751/_/diff#comment-127163635"}}
{"comment": {"body": "Thanks Gilad, this change was due a long time ago\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/751/_/diff#comment-127245579"}}
{"title": "Fixed ability to run DB pickles", "number": 752, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/752", "body": ""}
{"title": "Fix inheritance", "number": 753, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/753", "body": ""}
{"title": "Fixed bug in relay detect that caused it to crash in some scenarios.", "number": 754, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/754", "body": ""}
{"comment": {"body": "Best EM implementation ever", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/754/_/diff#comment-127204687"}}
{"comment": {"body": "Misra made me do it :disappointed: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/754/_/diff#comment-127205048"}}
{"title": "Added online training to the master simulator", "number": 755, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/755", "body": ""}
{"comment": {"body": "What\u2019s behind removal of error prints?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/755/_/diff#comment-127212168"}}
{"comment": {"body": "It doesn\u2019t look good", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/755/_/diff#comment-127212472"}}
{"comment": {"body": "`reset_feature_extract()` as well", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/755/_/diff#comment-127219467"}}
{"comment": {"body": "Cancel timeout calls the timeout callback immediately. Is that what you want to do?\n\nYou could reuse `RestartableTimer` I\u2019ve implemented in `\"python_tools\\system_tests\\devices_standalones\\master_standalone\\fingerprinter_loop.py\"` if you don\u2019t want to trigger the callback\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/755/_/diff#comment-127220011"}}
{"comment": {"body": "`cancel` is not documented to call the callback and I couldn\u2019t reproduce this behavior:\n\n```python\nIn [6]: t=threading.Timer(30, lambda a, k: print(a, k), args=[1], kwargs=dict(a=3))\nIn [7]: t.start()\n# Wait 3 secs?\nIn [8]: t.cancel()\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/755/_/diff#comment-127224088"}}
{"comment": {"body": "Cool. I had this behavior earlier for some other reason, I guess.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/755/_/diff#comment-127227225"}}
{"title": "Feature/model size reduction in cfo", "number": 756, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/756", "body": "Save more model memory by:\n\nChange no wifi models xtx matrices to symmatrices\nAggregated packets weight now holds the inverse root of the weight (cfo std)\nRemoved unused em_prior in wifi model\n\n\n\nSome change to catch to make it work under VSCode (moving to it because clion is freaking slow and hogs memory)\n\n\n"}
{"comment": {"body": "> Some change to catch to make it work under VSCode \\(moving to it because clion is freaking slow and hogs memory\\)\n\nI offer a free `Welcome to Linux` workshop. We\u2019ll get you up and running on your brand new Linux\u2122 machine in no time. Just imagine - fully customizable desktop environment, great performance, great stability, seamless docker integration, and most importantly - no random Windows Updates / BSODS.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/756/_/diff#comment-127237488"}}
{"comment": {"body": "Does it come with drivers for ST and IAR?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/756/_/diff#comment-127238208"}}
{"comment": {"body": ":joy: :joy: :joy: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/756/_/diff#comment-127247441"}}
{"title": "Feature/increase phone temperature penalty", "number": 757, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/757", "body": "fix LO leakage in large scale\nrun also large scale on all boards in quick tests\n\n"}
{"title": "Fix Timing bug in monitor", "number": 758, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/758", "body": "Jitter not calculated correctly in monitor\nAdd aging to Timing graph in monitor\n\n"}
{"title": "Some minor ui updates for the iphone app", "number": 759, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/759", "body": ""}
{"comment": {"body": "This code is copied from levl repo. Make sure to also update the repo there \\(functions `parse_bosch_data_from_demoded_data` and `parse_sensors_from_data`\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/759/_/diff#comment-127558006"}}
{"comment": {"body": "[https://bitbucket.org/levl/levl/pull-requests/127/fixed-demoded-data-parsing-to-support-ios/diff](https://bitbucket.org/levl/levl/pull-requests/127/fixed-demoded-data-parsing-to-support-ios/diff)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/759/_/diff#comment-127560731"}}
{"title": "Code optimizations in CFO extraction", "number": 76, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/76", "body": "Eliminate excessive loop in linear regression.\nUnrolling the loops of the convolution in the modulate GSFK.\nSelecting Gauss Coefficient that is a power of 2."}
{"comment": {"body": "Could swapping the for loop orders accelerate things a bit here? \n\nYou can run for\\(int j= 0, x=0\u2026.\\) first, than you can extract the bit \\(internal\\_packet\\_info->bits\\[j\\]\\*2-1\\) and then run the for \\(int i =0; i <GAUSS\u2026\\)\n\nI think this way you \u201cextract the bit\u201d only numSym times instead of numSym\\*GAUSS\\_NS. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/76/_/diff#comment-77988644"}}
{"comment": {"body": "Super cool! How much time did it save?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/76/_/diff#comment-77988767"}}
{"comment": {"body": "It wasn\u2019t the easiest code to understand before the optimizations, now it\u2019s even harder. Maybe some comments explaining that you are performing convolution here could help?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/76/_/diff#comment-77990218"}}
{"comment": {"body": "Add a comment in [0c21f57](https://bitbucket.org/levl/bosch_integration/commits/0c21f57c443ef42820d503d2141001d41a261aa4?at=develop)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/76/_/diff#comment-77992606"}}
{"comment": {"body": "Quite a lot.  \nIt shortened the CFO extraction time from 44ms to 17ms and the total feature extraction time from 76ms to 49ms.\n\nThe surprising part is that the power of 2 optimization was the one that gave most of the benefit.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/76/_/diff#comment-77993126"}}
{"comment": {"body": "I wonder if changing the `internal_packet_info->bits[j]*2-1` to lookup table would benefit :thinking: \n\nlike so:\n\n                    static int8_t bit2num[] = {-1, 1};\r\n                    int64_t val = (bit2num[internal_packet_info->bits[j]]) * m_gauss_PS_Int[i];", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/76/_/diff#comment-78050180"}}
{"comment": {"body": "also, `internal_packet_info->bits[j]` is unsigned and the other rvalues are signed. Could the signed ones be implicitly cast to unsigned and then cause issues?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/76/_/diff#comment-78081901"}}
{"title": "Increase time between beacon restart to allow timing feature work", "number": 760, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/760", "body": ""}
{"title": "feature/gcovr_gives_false_positive", "number": 761, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/761", "body": "Add script to validate coverage report\nAdd coverage\n\nTheres an open issue with gcovr here:  - which is the reason for this PR\n\nCreated from Atlassian for VS Code"}
{"title": "raise android version", "number": 762, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/762", "body": ""}
{"title": "Feature/BIS-7626 enlarge aging", "number": 763, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/763", "body": "Set aging to be 6 secs instead of 4.\nThere was a failing integration test for aging that was very difficult to find when running in parallel (Jenkins just got stuck) - test modified to support 6 instead of 4 secs for aging.\n"}
{"comment": {"body": "Any idea why the jenkins was stuck because of the failing test?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/763/_/diff#comment-127355125"}}
{"comment": {"body": "The power tripped overnight, Omer sorted it out.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/763/_/diff#comment-127355330"}}
{"comment": {"body": "No. It seems the parallel is not working 100%. It\u2019s also somehow skipping a couple of failing regression tests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/763/_/diff#comment-127355812"}}
{"comment": {"body": "Good work Sigal!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/763/_/diff#comment-127358162"}}
{"title": "Speedup download_regression_datasets.sh", "number": 764, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/764", "body": ""}
{"title": "Some organization for failing regressions. These need to be fixed sometime", "number": 765, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/765", "body": ""}
{"comment": {"body": "Do you know why were they not reported?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/765/_/diff#comment-127367925"}}
{"comment": {"body": "`\"regression tests using only hill feature\"` was failing but was not part of `--parallel` so we never noticed. It was not `#if 0`'d from the non-parallel run\n\n`regression tests train on phones classify with relay` was commented out of `--parallel` but not `#if 0`'d from the non-parallel run\n\nThese two tests failed whenever you tried to run the tests without parallel.\n\nThey need to be fixed, but for now, I \u201cx-failed\u201d them to stop causing confusion when trying to run regression locally without `--parallel`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/765/_/diff#comment-127369719"}}
{"title": "MasterSimulator: Training timeout is now indicated to the user", "number": 766, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/766", "body": ""}
{"title": "feature/model_size_reduction_in_cfo_part_2", "number": 767, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/767", "body": "Remove phone temperature histogram from 0 phone slope model\nAdd python ctypes cast 0 phone slope to regular no wifi\nReduce model size\nadd vscode settings\n\n\nCreated from Atlassian for VS Code"}
{"title": "Feature/lo Leakage fixes for v4 rc3", "number": 768, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/768", "body": "Moved noise window, added time decimation and low pass filter\nFixed remaining comments\nAccidentally trashed a test in classification but everything is ok now!\nVERY liberal thresholds to handle 10E6 precentile in regression tests. Separation is greatly damaged.\nAdded case for lo leakage training, fixed deep copy in all previous tests, improved too liberal threshold just a bit\n\n"}
{"comment": {"body": "oh boy :disappointed: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/768/_/diff#comment-127415786"}}
{"title": "fix quick large scale every 2 hours", "number": 769, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/769", "body": ""}
{"comment": {"body": "Excellent change :\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/769/_/diff#comment-127462283"}}
{"title": "Changed 8x to support new Bosch beacon format while still supporting the legacy format", "number": 77, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/77", "body": ""}
{"title": "feature/cfo_model_size_reduction_the_saga", "number": 770, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/770", "body": "Truncate phone temperature in aggregated packets\nReduce CFO size in aggregated packets\n\n\nCreated from Atlassian for VS Code"}
{"comment": {"body": "I think we need some safety band for the temperature for slightly going out of range, let\u2019s do a range of 50C \\(-5 to \\+45\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/770/_/diff#comment-127488518"}}
{"comment": {"body": "We can't input such temperatures to our system. It's blocked way earlier in the flow", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/770/_/diff#comment-127488989"}}
{"comment": {"body": "Yes. You are correct.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/770/_/diff#comment-127489363"}}
{"title": "Support dummy (non participating) anchors in the master simulator util", "number": 771, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/771", "body": ""}
{"title": "Perform reclassification after finalize to validate the model still works (CFO only)", "number": 772, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772", "body": ""}
{"comment": {"body": "what do you plan with this `#if 0`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127608924"}}
{"comment": {"body": "Well the else if is just good practice in case `push_features_and_classify` someday returns an error. Today it can\u2019t, so MISRA complains. MISRA is sometimes very stupid.\n\nThis is also a good example of why 100% coverage sometimes forces you to make your code worse", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127609414"}}
{"comment": {"body": "That\u2019s about the fourth or fifth definition of `CHANNEL_RANGE_LOW` and `CHANNEL_RANGE_HIGH`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127609415"}}
{"comment": {"body": "board range is -40 to 85", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127610014"}}
{"comment": {"body": "For crying out loud. Great work with the packing but maintenance is hell", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127610114"}}
{"comment": {"body": "I just moved it from Levl\\_fingerprinting.c to here, I didn\u2019t add a new one", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127610167"}}
{"comment": {"body": "Add c\\+\\+ guards here", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127610212"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4}  said it might be more in the future, so I kept it future-proof", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127610342"}}
{"comment": {"body": "I agree, I wish I didn\u2019t have to do it. Ideas for improvement will be happily accepted.\n\n\u200c\n\nThis part is the most problematic one:\n\n![](https://bitbucket.org/repo/x8eLRbo/images/859923750-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127610577"}}
{"comment": {"body": "You and your enums cause a size of odd number..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127610644"}}
{"comment": {"body": "I don\u2019t understand how it\u2019s related", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127610855"}}
{"comment": {"body": "Would nesting the if help with MISRA instead of elseif?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127610984"}}
{"comment": {"body": "Still unreachable because push features never errors", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127617787"}}
{"comment": {"body": "bit fields?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127618708"}}
{"comment": {"body": "I tried to avoid bit-fields because they\u2019re not defined very well and I didn\u2019t care to see how MISRA reacts to them.\n\nThe maintenance hell part is mostly keeping track of the possible ranges / bit sizes for each field \\(and knowing which fields should even be here in the first place\\), not the bit fiddling itself.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127619218"}}
{"comment": {"body": "I declare myself a MISRA god for not getting a single MISRA error on this file", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127619874"}}
{"comment": {"body": ":clap: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127621700"}}
{"comment": {"body": ":disappointed: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127626724"}}
{"comment": {"body": "Unittests would be the best approach", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127626945"}}
{"comment": {"body": "I undeclare myself\u2026 Stupid Full Static analysis\n\n![](https://bitbucket.org/repo/x8eLRbo/images/3929215885-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/772/_/diff#comment-127627477"}}
{"title": "feature/fix_some_0_slope_stuff", "number": 773, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/773", "body": "Add separate event for model validation started\n\nBring of speed to hydras\n\n\nForgot to store the progress\n\n\nTried to fix replay - doesn't work with the new model validation :( also it's too slow to test locally\n\n\n\nCreated from Atlassian for VS Code"}
{"title": "Reverted a616f14909328f2f3c030271f5be05c7d4fe150c for testing_agent", "number": 774, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/774", "body": ""}
{"title": "Fixed testing agent sending the model twice", "number": 775, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/775", "body": ""}
{"title": "Feature/kill sigma", "number": 776, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/776", "body": "May it rest in peace.\n"}
{"comment": {"body": "Press F to pay respects", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/776/_/diff#comment-127670644"}}
{"comment": {"body": "F", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/776/_/diff#comment-127878443"}}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/246459570-sigmarip.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/776/_/diff#comment-127878606"}}
{"comment": {"body": "F", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/776/_/diff#comment-127887080"}}
{"title": "ITD 185", "number": 777, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/777", "body": "Note that data is now called blabla_v5.pickle"}
{"title": "Valgrind fixes", "number": 778, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/778", "body": "Unittests now run under valgrind when running on nightly.\nAny valgrind error will fail the build. Ive done the best I can to eliminate the errors.\nMost of them was just due to unittests not initializing the data properly, but one of them is due to BIS-7731, which is yet unsolved (and will fail the build).\n"}
{"comment": {"body": "Good work Gilad! Let\u2019s look tomorrow on the failing test", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/778/_/diff#comment-127878155"}}
{"comment": {"body": "Good catch", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/778/_/diff#comment-127886010"}}
{"title": "The app_start_stop.py script can now use shelter instead of TID", "number": 779, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/779", "body": ""}
{"title": "Pressing the model-reset button will now set the first_train_command to true to allow retraining after the button has been pressed", "number": 78, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/78", "body": ""}
{"title": "Test ux", "number": 780, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/780", "body": "Test qt_ux_001 and qt_ux_003 mostly working\n\n"}
{"comment": {"body": "~~test\\_qt\\_ux\\_002 doesn\u2019t do anything at the moment and is incomplete.~~", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/780/_/diff#comment-127880576"}}
{"comment": {"body": "what is being tested here? there are not assertions. Last `print(\"Not match!\")` shouldn\u2019t happen", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/780/_/diff#comment-127886304"}}
{"comment": {"body": "It\u2019s incomplete atm", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/780/_/diff#comment-127886308"}}
{"comment": {"body": "Fixed ux\\_002", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/780/_/diff#comment-127887505"}}
{"title": "Feature/BIS-7722 support calibration in large sc", "number": 781, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/781", "body": "Support calibration tests in large scale tests using -check-calibration\nAdd a calibration test\n\n"}
{"title": "Feature/lo leakage fixes for v4 rc4", "number": 782, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/782", "body": "Decimation normalization + truncation window size 5->9\nChanged decimation implementation so that static analysis will not MISTAKENLY think Its overflowing\nTrying to tighten back latency constraints in duration test (70  65)\nChanged thresholds and fixed decimation\n\n"}
{"title": "Implemented test 183 and 184", "number": 783, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/783", "body": "Someone needs to upload the data, who should I send it to?"}
{"title": "Instfreq now only AGC 4-5", "number": 784, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/784", "body": ""}
{"comment": {"body": "Also removed leftover `noise_iq` from sigma", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/784/_/diff#comment-127888085"}}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/1484029142-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/784/_/diff#comment-127888909"}}
{"title": "Tests ITD 183, 184", "number": 785, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/785", "body": "ITD 184 passes\nITD 183 fails in 3/4 cases"}
{"title": "feature/1_0_phone_slope_model", "number": 786, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/786", "body": "Fingerprinter loop in a separate process. Replay and Standalone Master are now somewhat faster\nConsolidate 2 models from 0 phone slope instance into 1\n\n\nCreated from Atlassian for VS Code"}
{"comment": {"body": "If the zero slope\u2019s enablement is the same as in the regular instance \\(meaning now it\u2019s enabled whenever regular instance has is wifi enabled or is no wifi enabled\\), why do we need it here?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/786/_/diff#comment-127890941"}}
{"comment": {"body": "You\u2019re right; it\u2019s redundant currently.\n\nIt\u2019s a placeholder for the future for when we disable one of the instances", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/786/_/diff#comment-127891106"}}
{"comment": {"body": "Looks good!\n\nCan you resize the Model to the new struct size?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/786/_/diff#comment-127892383"}}
{"comment": {"body": "I thought I'd do it later, after I remove the em_params attribute. Do you want me to do it now?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/786/_/diff#comment-127892436"}}
{"comment": {"body": "No rush. We can do it later :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/786/_/diff#comment-127892459"}}
{"title": "Fix bug in large scale", "number": 787, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/787", "body": ""}
{"title": "last_finalization_results bug fix", "number": 788, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/788", "body": ""}
{"title": "Amirs ITD tests", "number": 789, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/789", "body": "ITDS 177-180 and 186-187\nlast_finalization_results bug fix\nClassify with phone temperature and rtc_now=0\nadd tests ITD_186 ITD_187\n\n"}
{"comment": {"body": "There\u2019s a lot of duplicate code across these tests. Please try to define functions / share as much code as possible.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/789/_/diff#comment-127892555"}}
{"comment": {"body": "Done, less duplicate code", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/789/_/diff#comment-127979342"}}
{"title": "China demo - used the least significant bit of the temperatures in beacons to allow phone to indicate whether to calc CFO or not", "number": 79, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/79", "body": ""}
{"comment": {"body": "Looks good, did you verify with some print that `cfo_st.should_use` is actually false sometimes?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/79/_/diff#comment-78255539"}}
{"comment": {"body": "I was at home so I couldn't test. Will try now", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/79/_/diff#comment-78255579"}}
{"title": "Test lo leakage", "number": 790, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/790", "body": "add ITDs 174 175 176\n\n"}
{"comment": {"body": "When do you plan to do this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/790/_/diff#comment-127897238"}}
{"comment": {"body": "@{5b7bbc6cc5e21441d0929b7e} ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/790/_/diff#comment-127897423"}}
{"title": "Feature/feature extraction performance", "number": 791, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791", "body": "Added number of packets per second indicator to the monitor.\nLibrary will now be compiled with O3. Minor runtime improvements.\n\n"}
{"comment": {"body": "~~IDK why eclipse for mac does this\u2026~~ fixed\n\nThey are identical except for adding O3.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127895950"}}
{"comment": {"body": "Any chance to keep it clean?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127897285"}}
{"comment": {"body": "Do you think the compiler is smart enough to put `((cfloat32_t)1.0f/(cfloat32_t)DECIMATION_WINDOW)` in a const?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127897299"}}
{"comment": {"body": "FLTO means that we\u2019re depending on bosch to do the optimizations. Is this sufficient for our purposes?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127897381"}}
{"comment": {"body": "[Yes](https://godbolt.org/z/tac5GW)\n\n![](https://bitbucket.org/repo/x8eLRbo/images/541062539-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127897972"}}
{"comment": {"body": "They are supposed to compile us with all available optimizations anyway.\n\nWe just need to talk with them about the -O3 since they\u2019ve asked us to be compiled with -Os", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127898039"}}
{"comment": {"body": "Will do.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127898062"}}
{"comment": {"body": "Spaces instead of tabs :frowning2: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127971463"}}
{"comment": {"body": "Cool", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127971510"}}
{"comment": {"body": "catch has benchmarking utils. Would that help? [https://github.com/catchorg/Catch2/blob/master/docs/benchmarks.md#top](https://github.com/catchorg/Catch2/blob/master/docs/benchmarks.md#top)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-127972654"}}
{"comment": {"body": "Switch to linux.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-128011395"}}
{"comment": {"body": "Not really, doing this would require me to use the same packet over and over again\u2026\n\nRunning regression does a better job.\n\nThis is only here so we could run clion\u2019s profiler easily.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/791/_/diff#comment-128012011"}}
{"title": "Feature/fix xfail", "number": 792, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/792", "body": "remove not needed tests\n\n\n"}
{"comment": {"body": "remove", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/792/_/diff#comment-127897211"}}
{"comment": {"body": "what\u2019s the reason behind this hard-coded 0?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/792/_/diff#comment-127897217"}}
{"title": "Feature/new not enough data behaviour", "number": 793, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/793", "body": "Implemented new SRS123 and QT-CLASSIFY-051\nImplemented QT-CLASSIFY-052\ntests;\nrefactored neat tests\nadd timestamps in get_features_from_pickle\nnot sure what is going on now cuz C needs to be modified\nfails, but it should. C stuff will be changed and then this will pass\nadd tests 51 and 52\nremoved tests 51 and 52 from test_classification.py\nrename\nAdded correct datasets for integration tests\nFinished SRS36/123 tests\n\n"}
{"comment": {"body": "Please fix code duplication in tests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/793/_/diff#comment-127926715"}}
{"title": "feature/cfo_model_size_reduction_saga_the_new_hope", "number": 794, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/794", "body": "Smaller model size\nRemove em new prior\n\n\nCreated from Atlassian for VS Code"}
{"comment": {"body": "\u05d2\u05d0\u05d5\u05df", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/794/_/diff#comment-127925921"}}
{"title": "ITDs 181 182", "number": 795, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/795", "body": "tests 190_1 and 190_2 implemented and pass\nmodified tests 189 and 190. both pass\nadded ITDs 181,182,188,189\nITD 182 - fails due to issue with cfo model. needs investigation\n\n"}
{"comment": {"body": "I think lib.reset\\_feature\\_extract\\(\\) should be added as well, so the progress for hill is cleared", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/795/_/diff#comment-127943225"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/795/_/diff#comment-127944984"}}
{"title": "Bitmap for feature validity", "number": 796, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/796", "body": ""}
{"comment": {"body": "We can change the cfo\\_est to float32", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/796/_/diff#comment-127918010"}}
{"comment": {"body": "Yes I\u2019m aware, there\u2019s a lot of stuff that can be done, I\u2019ll do it in other pull requests cause this one is large enough", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/796/_/diff#comment-127918273"}}
{"comment": {"body": "Awesome :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/796/_/diff#comment-127918502"}}
{"comment": {"body": ":clap:  Great!  \nJust some coverage uses to fix.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/796/_/diff#comment-127918867"}}
{"title": "Fixed hydras to generate correct enum size and put it in docker.", "number": 797, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/797", "body": ""}
{"title": "Revert \"Feature/new not enough data behaviour (pull request #793)\"", "number": 798, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/798", "body": "This reverts pull request #793.\n\nImplemented new SRS123 and QT-CLASSIFY-051 Implemented QT-CLASSIFY-052 tests; refactored neat tests add timestamps in get_features_from_pickle not sure what is going on now cuz C needs to be modified fails, but it should. C stuff will be changed and then this will pass add tests 51 and 52 removed tests 51 and 52 from test_classification.py rename Added correct datasets for integration tests Finished SRS36/123 tests\n\n\n"}
{"title": "Feature/lo leakage optimizations", "number": 799, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/799", "body": "Testing regression tests with th_2\nRemoved lo leakage from regression tests because a number of extreme packets failed tests entirely. Feature statistics must be examined in large scale and manual tests.\n\nth_2 = {frequency stdev threshold 0.46, peak bias 1.55}\n\nlarge scale same imei results: 99.901% TPR\nlarge scale check relay results: < 20% FPR\nmanual testing dont reproduce FN.\n\n"}
{"title": "Feature/FIN-269 Refactor preprocessing", "number": 8, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/8", "body": "setting preprocessing as a feature extraction module moving files and tests around\n*fixing CI file\n\ncreating tests_commons\n\nfixing cmakelist for preprocessing executable\n\n\n\n*merging CMakefiles and removing static libs created from modules for easier integration"}
{"title": "More code optimizations", "number": 80, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/80", "body": "Preprocessing is rotating the buffer by copying instead of doing in-place rotation, without increasing memory consumptiom.\nMoved div operation outside of loop for calculation of cfo variance."}
{"comment": {"body": "Not really true for the very last sample being overridden by the previous one. But do we care about it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/80/_/diff#comment-78258014"}}
{"comment": {"body": "Yes. You are correct. There is some unexpected effect on the last sample.  \n Not very important as the last sample is a noise sample.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/80/_/diff#comment-78258038"}}
{"comment": {"body": "We just need to make it look like noise so no side effect occur. Maybe duplicate the previous sample?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/80/_/diff#comment-78258111"}}
{"comment": {"body": "kinda redundant since the compiler knows how to optimize `float_to_q31`. Do you still want it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/80/_/diff#comment-78258141"}}
{"comment": {"body": "I take it back. There isn\u2019t any problem here.  \nWe first read and then we write so there isn\u2019t any issue here. The last sample will first be read and then overwritten.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/80/_/diff#comment-78258156"}}
{"comment": {"body": "Yeah. It\u2019s more clear this way.  \nIt\u2019s kinda wired to see a float conversion in a code that does not use floats at all..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/80/_/diff#comment-78258321"}}
{"comment": {"body": "I take it back as well", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/80/_/diff#comment-78258442"}}
{"title": "Feature/new not enough data behaviour", "number": 800, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/800", "body": "Fixed SRS123 condition\n\nUnrevert \"Revert \"Feature/new not enough data behaviour (pull request #793)\"\"\nThis reverts commit f5ab6612a3931bd041449bac4e5c0fbec9b91e4c.\n\n\nFixed struct integrity.\n\n\n"}
{"comment": {"body": "Please reduce code duplication", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/800/_/diff#comment-127943014"}}
{"comment": {"body": "i feel like this should be nested, for clarification\n\n```\nif(enough_time_passed_from_start_of_classification) {\n   if(max_recent_packets_reached && aggregate_recent_packets_reached) {\n      res = LEVL_CLASSIFY_MATCH;\n   }\n}\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/800/_/diff#comment-128057455"}}
{"comment": {"body": "Your clarification is wrong, though", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/800/_/diff#comment-128057681"}}
{"comment": {"body": "I guess I could write it as:\n\n```\nif (max_recent_packets_reached) {\n  res = LEVL_CLASSIFY_MATCH;\n} else if (aggregate_recent_packets_reached && enough_time_passed_from_start_of_classification) {\n  res = LEVL_CLASSIFY_MATCH;\n}\n```\n\nI\u2019m not sure that\u2019s better", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/800/_/diff#comment-128057907"}}
{"comment": {"body": "Yeah it\u2019s good enough like it is I think\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/800/_/diff#comment-128058305"}}
{"comment": {"body": "makes more sense IMHO.  \nNot critical enough", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/800/_/diff#comment-128058385"}}
{"title": "ITD 183, 184 for slope zero", "number": 801, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/801", "body": "Tests ITD 183, 184\n\n"}
{"title": "feature/cfo_model_size_reduction_the_fallen_struct", "number": 802, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/802", "body": "Move adapters to a separate file\nRemove cfo wifi model and build it using adapter\n\n\nCreated from Atlassian for VS Code"}
{"title": "Smaller featex pt2", "number": 803, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/803", "body": "Feature smaller integer widths\nFeature is 256 \n\n"}
{"title": "Added Levl_GetDynamicStackSize", "number": 804, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/804", "body": ""}
{"title": "Implemented QT_QUALITY_015 and not QT_QUALITY_016", "number": 805, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/805", "body": ""}
{"title": "Feature/fix xfail", "number": 806, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/806", "body": "remove not needed tests\nremove xfail\nremove xfail\nRemoved code added by mistake\nFixed some xfailes\n\n"}
{"title": "Feature/BIS-7694 calibration to better support c", "number": 807, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/807", "body": "Improve and fix and basically implement missing calibration code."}
{"title": "feature/cfo_model_size_reduction_saga_the_two_towers", "number": 808, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/808", "body": "Back to 6 anchors\nbitmaptize feature_deactivation_map\nRemove conf interval in model common\nRemove unused variables and padding\n\n\nCreated from Atlassian for VS Code"}
{"title": "Fixed a lil' bit of uninitialized memory access", "number": 809, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/809", "body": "Nothing important, but it was tripping valgrind"}
{"title": "Feature/compile time support for 83", "number": 81, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/81", "body": "preparing baseband correction for x83\nless branches in demodulation and preamble extraction\nput back loop unrolling\ninline functions instead"}
{"title": "Feature/size report on st", "number": 810, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/810", "body": "Added train_classify_for_size_report project to the ST\nAdded missing project and updated build scripts\nFix for jenkins size reports\nLib size test will now fail if the file's size is 0\nJenkins fixes\n\n"}
{"comment": {"body": "Great work!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/810/_/diff#comment-128125267"}}
{"title": "Updated app version", "number": 811, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/811", "body": "new app version that can receive shelter intents from app_start script"}
{"title": "Feature/lo leakage optimizations 2", "number": 812, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/812", "body": "PTAR implementation\nTidying up, removed per packet energy normalization and found major bug that caused lo_leakage to fail and deactivate during validation\nFixed issue with lo leakage not being disabled in classification although its disabled by configuration.\n\n"}
{"title": "Feature/deactivation fixes", "number": 813, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/813", "body": "Many deactivation improvements, much better tests\nFix UX hill and RSSI test bug\n\n"}
{"title": "fix min num packets in voting table", "number": 814, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/814", "body": ""}
{"title": "Changed CRSSI to return need-more-data", "number": 815, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/815", "body": ""}
{"title": "feature/fix_itd_182", "number": 816, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/816", "body": "ITD 182 now does feature extraction\n\n\nCreated from Atlassian for VS Code"}
{"title": "Moved valgrind runs to another jenkins project", "number": 817, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/817", "body": ""}
{"title": "Feature/BIS-7912 calibration wifi check if possi", "number": 818, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/818", "body": "Calibration - if wifi two lobes, disable wifi's model\nFix calibration zero slope; fix case of calibration with enabled=false\nFix one lobe using master wifi model - should use master no wifi model\n\n"}
{"title": "Added calibration to master simulator", "number": 819, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/819", "body": ""}
{"comment": {"body": "Great!  \nCan you also apply this to stderr?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/819/_/diff#comment-128224797"}}
{"comment": {"body": "This would cause all `tqdm` logs to be outputted as well, I prefer not.\n\nThe logs look good like this.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/819/_/diff#comment-128546769"}}
{"title": "Support new Bosch format where advertising is in connectable mode", "number": 82, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/82", "body": "Attached the request for the change we got from Bosch:\nFirstly, a small request. Please make sure theres a SW version on the files you send us. The DA14683 software should print its version on UART.\nThis is to make sure were using the correct SW.\nDaniel used a BLE sniffer to identify the difference between packets sent by your phone app and ours.\nBosch PK App:\n\nHeres the complete message\n0d 03 00 00 00 00 07 1e aa d6 be 89 8e 40 1c 20 9b c4 56 e5 57 02 01 02 12 ff a6 02 be 11 44 65 43 32 73 36 64 6b 41 48 38 0b 54 4a 8d 9c\n\n\nLevl Trainer App:\n"}
{"comment": {"body": "Can we have a short summary of the differences so it\u2019s easier for us to understand your changes?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/82/_/diff#comment-78459234"}}
{"comment": {"body": "Sure.\n\nIn the previous code we always assumed there is a single AD section which had the manufacturer data and is located just after the bluetooth address.  \nIn the new code, we traverse over all the AD sections in the packet and check their type \\(there is a type of manufacturer data\\) and once we have AD from the manufacturer data we parse it and find if it\u2019s a Bosch Packet. All other AD sections are ignored.\n\n  \nThe structure of AD section is the following <size: 1 byte> <type: 1 byte> <data: size-1 bytes>", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/82/_/diff#comment-78460529"}}
{"comment": {"body": "Is this legacy format or we still get here when we do training?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/82/_/diff#comment-78463837"}}
{"comment": {"body": "This is where we do training through the app.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/82/_/diff#comment-78464022"}}
{"comment": {"body": "OK so it\u2019s just a misleading comment - \u201cOld legacy format\u2026\u201d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/82/_/diff#comment-78464315"}}
{"comment": {"body": "Our app transmits the old legacy format :slight_smile:   \nThe Bosch app is using the new format. As they requested to support both, we didn\u2019t see any need to update our app.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/82/_/diff#comment-78464719"}}
{"title": "Feature/lo leakage change classification rules", "number": 820, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/820", "body": "Now returns NO_MATCH whenever there's one non-matching stable channel (8 packets), but MATCH only if all channels are stable and matching. All other cases: MORE_DATA_NEEDED\n\n"}
{"title": "Bugfix: Submodels that have not been trained are not going through feature validation", "number": 821, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/821", "body": "During after-training validation ignore packets from anchor that are not valid\nFix coverage by adding tests\nincrease number of packets for calibration\n\n"}
{"comment": {"body": "Remember to open Bug in Jira - \u201cTraining may be stuck in deadlock when training phase is done on a different anchor than validation phase\u201d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/821/_/diff#comment-128299834"}}
{"title": "feature/load_anchor_simulator_model_to_monitor", "number": 822, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/822", "body": "Add model loading to replay\nFix running with scissors\nFix covert to monitor\n\n\nMerge branch 'feature/master_simulator_calibration' of  into feature/load_anchor_simulator_model_to_monitor\n\n\nStart handling anchor simulator in monitor\n\n\n\nCreated from Atlassian for VS Code"}
{"comment": {"body": "From what I\u2019ve tried, this does not always work.\n\nDid you test to see if it\u2019s doing what it\u2019s supposed to?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/822/_/diff#comment-128421061"}}
{"comment": {"body": "It worked on Windows. Kill didn\u2019t", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/822/_/diff#comment-128422648"}}
{"comment": {"body": "Very very cool", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/822/_/diff#comment-128422717"}}
{"title": "remove xfail", "number": 823, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/823", "body": ""}
{"title": "Monitor state graph", "number": 824, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/824", "body": ""}
{"title": "Minor valgrind fixes", "number": 825, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/825", "body": ""}
{"title": "Feature/fuzzing", "number": 826, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/826", "body": "Added fuzzing target\nFixed more errors found in fuzzing\nAdded feature-fuzzing\nFixed failing system test due to wrong validation error\nAdded model fuzzing\nAdded unittests for fuzzing-related fixes\nAdded check for valid cfo_wifi_online_training_container_num_packets\nReduced size of model-fuzzing target\nAdded a fuzzing stage to jenkins\nFixed botched merge\nFixed sanity checks throwing packets with LEVL_LENGTH_UNKNOWN\nFixed fuzzing test case generator to work from any directory\nFurther fixes from botched merge\nFixed run_fuzzing not fetching dataset\nChanged fuzzers to use AFL's persistent mode\nRe-enabled captured-packet fuzzing\nFixed crashes in fuzzing targets\nSwitched fuzzing to use MSAN instead\nTrying to fix CI\nRemoved fuzzing from CI\n\n"}
{"title": "Feature/fuzzing", "number": 827, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/827", "body": "Added some fuzzing targets and fixed discovered bugs."}
{"comment": {"body": "Is this reflected in the EIS?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/827/_/diff#comment-128834659"}}
{"comment": {"body": "It isn\u2019t. Can I just edit the EIS?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/827/_/diff#comment-128834721"}}
{"comment": {"body": "Huh, it seems like the previous two errors are also missing from the EIS", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/827/_/diff#comment-128834758"}}
{"comment": {"body": "Email @{5a4500fe0cacf235de82a9d4} ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/827/_/diff#comment-128834775"}}
{"comment": {"body": "Next time make sure the PR description only contains interesting commits / general overview of what was done and not every commit message", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/827/_/diff#comment-128834835"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} @{5a49d431ef77662a7583f8f0} ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/827/_/diff#comment-128834983"}}
{"title": "Fixed some system tests", "number": 828, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/828", "body": ""}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/783668903-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/828/_/diff#comment-128832788"}}
{"title": "Turn off periodic trigger of QLST", "number": 829, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/829", "body": "Created from Atlassian for VS Code"}
{"title": "Merge pull request #92 from the bosch demo branch to the develop branch", "number": 83, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/83", "body": "All details here:\n"}
{"comment": {"body": "that also means that every advertiser that we have \\(android phones, BLE dongles and dialog advertisers\\) and every recording dialog project should change accordingly, right?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/83/_/diff#comment-78612518"}}
{"comment": {"body": "No.\n\nIt\u2019s supports everything backwards.\n\nThe issue was that Bosch while transmiting their packets sent with an additonal AD section containting advertisiment flags which make the location of our AD \\(the manufacturer data\\) not constat.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/83/_/diff#comment-78613025"}}
{"comment": {"body": "OK, cool", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/83/_/diff#comment-78614480"}}
{"comment": {"body": "Looks good but we need to make a recording and a test for this\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/83/_/diff#comment-78618259"}}
{"title": "Feature/correct hydra enum size", "number": 830, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/830", "body": "Fixed hydras to generate correct enum size and put it in docker.\nFixed struct integrity\n\n"}
{"title": "Implemented more validity checks for CRSSI", "number": 831, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/831", "body": ""}
{"title": "Feature/auto ctypes", "number": 832, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/832", "body": "Most ctypes related definitions are now auto-generated from the compiled shared-object."}
{"comment": {"body": "Looks amazing! Great job!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/832/_/diff#comment-128930163"}}
{"title": "feature/add_cfo_unittests", "number": 833, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/833", "body": "Update test for 0 phone slope\nSome refactor for testability\n\n\nCreated from Atlassian for VS Code"}
{"title": "Updated check when extracting feature to avoid ill-posed state in training/classify", "number": 834, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/834", "body": "Made sure that not all 6 weakest samples are zeroes."}
{"title": "Protect code from bad params", "number": 835, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/835", "body": ""}
{"title": "Protect condition param", "number": 836, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/836", "body": ""}
{"title": "Feature/BIS-8093 cfo channel slope support wifi", "number": 837, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/837", "body": "CFO channel slope should also be enabled for wifi one lobe (in addition to no wifi).\ni.e. this feature is disabled only for IPhones (no CFO model) and wifi with two lobes."}
{"title": "Prevent oversize queries in pickles creator", "number": 838, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/838", "body": "This is a protection of the pickles creator against large queries (i.e. large billing) done by mistake."}
{"title": "Fix call to pickles creator in Jenkins", "number": 839, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/839", "body": "Behavior of the -condition flag was previously changed so that and should not longer be added at the beginning of the condition - fixing the Jenkins usage of this flag to remove the and."}
{"title": "try1 xray publish", "number": 84, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/84", "body": ""}
{"title": "Bosch SVW anchor", "number": 840, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/840", "body": "I'm introducing dg_configENABLE_BOSCH_SVW_DA14695AA_SUPPORT configuration that will work for the SVW anchors. After this PR, make sure to delete the __pycache__ directories in your main_loop_9x. You can run rm -rf $(find . -type d -name __pycache__) in that directory.\n\nAdd -B to launch configurations to not create pycache Add configuration (with SDK patch) for SVW flash\nApply NVM limitation when it's SVW anchor\nCI to build SVW project\n\n\nCreated from Atlassian for VS Code"}
{"title": "Enable Hill in tests", "number": 841, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/841", "body": "There were several tests that Hill was disabled in since it was not yet stable - enable Hill now since its already stable."}
{"title": "Feature/comcast app/preconfiguration", "number": 842, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/842", "body": "Add preconfiguration to trainreset, I hope I didn't break anything\nHandle zero slope\nMATE10LITE and samsungs can now be separated\nUpdate regression tests\n\n"}
{"comment": {"body": "Very nice", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/842/_/diff#comment-148673678"}}
{"comment": {"body": "IKR?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/842/_/diff#comment-148674100"}}
{"title": "pattern to be 31 bits instead of 32 bits", "number": 843, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/843", "body": "Last bit used for wifi_settings_enabled"}
{"title": "Another mask", "number": 844, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/844", "body": ""}
{"title": "Gilad/auth0 ble", "number": 845, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/845", "body": "Goes with \n"}
{"title": "Higher IQ task priority", "number": 846, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/846", "body": "Using this, packet output rate is raised from 4-5 packets/sec to 6-7 packets/sec"}
{"title": "Feature/comcast app/preconfiguration", "number": 847, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/847", "body": "1 lobe preconfiguration\nHydra update\nHydra\nFix preconfiguration passing to library NO PARTICIPATION for CFO w/ and w/o 0slope\n1 lobe wifi flag works\n\n"}
{"title": "ChannelSlope feature participate only when wifi 1 lobe preconfiguration", "number": 848, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/848", "body": "Temporary workaround for "}
{"title": "IQ Print task priority back to lowest", "number": 849, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/849", "body": "With the higher priority, during IQ capture, sometimes the board would miss messages from the controller/master/pi, causing the connection to timeout.\nRolling back the priority fixes this starvation"}
{"title": "Revert \"try1 xray publish (pull request #84)\"", "number": 85, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/85", "body": "This reverts pull request #84.\n\ntry1 xray publish"}
{"title": "Removed limit on packet count", "number": 850, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/850", "body": "This limit was to give the board/rpi time to talk, but it turns it was not necessary and does more harm than good."}
{"title": "Decimation of 16Mhz -> 8Mhz", "number": 851, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/851", "body": "Add 16mhz -> 8mhz decimation\nadd decimation on board agent\nadd library support for decimated input\nmore generic way to handle decimation\nnow have the abillity to send full buffers\ncancel out unwanted indentation change\n\n"}
{"comment": {"body": "I\u2019m pretty sure that\u2019s not the only change in the python region.\n\nCould you re-run hydra generation.sh?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/851/_/diff#comment-153317049"}}
{"comment": {"body": "Did you see that the index selection mattered for the decimation? I don\u2019t see a reason for this. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/851/_/diff#comment-153317248"}}
{"comment": {"body": "Yeah. If the length of the first part is odd, we insert one sample more than needed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/851/_/diff#comment-153317804"}}
{"comment": {"body": "I don\u2019t think it will affect to much. It doesn\u2019t change any structures, just the input buffer for CapturePacket \\(which the struct has a pointer to it\\). All internal structs are using LEVL\\_SAMPLE\\_RATE\\_INTERNAL\\_MHZ \n\nI re-run ./generate\\_hydra\\_structs.sh and it didn\u2019t generate anything new ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/851/_/diff#comment-153318499"}}
{"title": "Make hydra great again", "number": 852, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/852", "body": "Following 4 beacons changes.\nSide note: @{5b72a213e72afd064c8a4ebd} didnt create these changes. They were done on lableft. So no glory to @{5b72a213e72afd064c8a4ebd} \n"}
{"comment": {"body": "Glory to the horde, though?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/852/_/diff#comment-153340560"}}
{"title": "Changes for support of online badge-in/badge-out", "number": 853, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/853", "body": "Toggling lock opened/closed locally has changed to changing a persistent user attribute online - present/non-present."}
{"comment": {"body": "Too many formatting diffs. Please revert the unnecessary new lines. It\u2019s unreadable this way.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/853/_/diff#comment-153344842"}}
{"comment": {"body": "Done btw, sorry about that.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/853/_/diff#comment-153414835"}}
{"title": "Fixed off-by-one", "number": 854, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/854", "body": ""}
{"title": "CONNECTION RSSI feature doesn't receive enough packets", "number": 855, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/855", "body": "This is due to too few connection packets. Changelog:\n\nSome task priority change to allow IQ print be not idle priority\nDon't start UART Monitor task since I don't see any benefit in it\nReplace print crssi boolean with semphore since I thought that it's more correct\nCentral (dialog) to control connection params\n\n"}
{"title": "LO Leakage can haz less training packets", "number": 856, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/856", "body": "Since were training with 600 packets and not 1500 packets, LO leakage needs to use less packets for training.\nDoing some tests revealed no false negative with this feature."}
{"title": "New IMEI in list", "number": 857, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/857", "body": ""}
{"title": "Python3.8 changed the way dlls are loaded and it affects windows. Somehow search paths are fucked up so it's overridden with new python3.8 parameter", "number": 858, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/858", "body": ""}
{"comment": {"body": "Beautiful, clean, concise. This is art, not code.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/858/_/diff#comment-153485087"}}
{"title": "New user ID format", "number": 859, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/859", "body": ""}
{"title": "Feature/compile time support for 83", "number": 86, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/86", "body": "Duplicate x83 configurations\nAdd HW specific behavior for x83 USB DK (Switch & LEDs)"}
{"comment": {"body": "Added a configuration for x83 USB DK since pins for switch & LEDs are different.\n\nLet me know if have other suggestions for that", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/86/_/diff#comment-80750002"}}
{"comment": {"body": "I\u2019m cancelling this PR since there\u2019s a way to detect USB DK in runtime", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/86/_/diff#comment-80875951"}}
{"title": "Removed usage of base64", "number": 860, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/860", "body": ""}
{"comment": {"body": "Remove `print_b64` also?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/860/_/diff#comment-153737948"}}
{"comment": {"body": "Sure", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/860/_/diff#comment-153738843"}}
{"title": "CCP-179: dont train cfo if temperature range is large", "number": 861, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/861", "body": "temperature_histogram: calculate range of data in histogram\nCFO model: don't enable model in training if there's too much temperature variance in train. Allow enabling it later in online training\nFix disconnection sequence: first report the master the disconnected device and then delete the memory. Now the disconnected device is not 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000\n\n"}
{"comment": {"body": "for 000000000000000000000000000000000000000000000000000000000000000000000000000, I mean:\n\n![](https://bitbucket.org/repo/x8eLRbo/images/3550556709-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/861/_/diff#comment-153796754"}}
{"title": "Pass nauth reason to user", "number": 862, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/862", "body": ""}
{"title": "Added support for sending device's BT MAC on connection", "number": 863, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/863", "body": "Pretty straightforward, title says it all."}
{"title": "Support transferring BT name", "number": 864, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/864", "body": "Added a new message in the device connection procedure to transfer the configured BT name"}
{"comment": {"body": "Would we want it to be modified by the user? Or allow some other source of the name?\n\nWe could generalize it with Friendly Name instead of BT name", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/864/_/diff#comment-154322103"}}
{"comment": {"body": "Do you have a specific idea as to how this will work? because this is a session attribute so it sounds weird that it will be configured in the lock\u2026.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/864/_/diff#comment-154324459"}}
{"comment": {"body": "Nope. Probably a question out of scope. I\u2019ll leave it.\n\nGood job.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/864/_/diff#comment-154328410"}}
{"comment": {"body": "Please retarget this PR to `ble-production/v2`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/864/_/diff#comment-154337436"}}
{"title": "Feature/CCP-193 support more than 40 degrees", "number": 865, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/865", "body": "Increase temperature histogram size and phone temperature accepted range\nOnly 1 anchor support\nHydra\n\n"}
{"title": "Comcast app", "number": 866, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/866", "body": "Increase temperature histogram size and phone temperature accepted range\nOnly 1 anchor\nHydra\n\n"}
{"title": "Read constant size from ble message", "number": 867, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/867", "body": "Coupled with "}
{"comment": {"body": "what about null terminator? e.g. forcing it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/867/_/diff#comment-154790707"}}
{"comment": {"body": "I have the change locally, will push on the next opportunity.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/867/_/diff#comment-154798983"}}
{"title": "Added missing break", "number": 868, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/868", "body": ""}
{"title": "Only run doxygen on relevant files", "number": 87, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/87", "body": ""}
{"title": "BIS-307 auto test reporting", "number": 88, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/88", "body": "Added stage to publish unittests results to Jira"}
{"title": "fix generating tests report to Jira for develop branch", "number": 89, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/89", "body": ""}
{"title": "CFO training model", "number": 9, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/9", "body": "update math.c from NXP project\nfix compilation\nfix warning\nadded linear regression functions\nadded cfo model\ncmake update\nsmall updates"}
{"comment": {"body": "how about some MISRA compliance and unit testing?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/9/_/diff#comment-70321660"}}
{"comment": {"body": "* use const wherever possible\n* coding style issues\n\n    * static m\\_ for module-local variables\n    * \\{\\} in every for/if/else\n    \n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/9/_/diff#comment-70325261"}}
{"comment": {"body": "There is a test for the main function in this file \\(the linear regression which use the rest\\)\n\nFixed some MISRA issues, will fix the rest in next pushes.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/9/_/diff#comment-70413743"}}
{"title": "fix generating tests report to Jira for develop branch", "number": 90, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/90", "body": ""}
{"title": "fix generating tests report to Jira for develop branch", "number": 91, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/91", "body": ""}
{"title": "temporary disable test reporting to Jira", "number": 92, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/92", "body": ""}
{"title": "fix generating tests report to Jira for develop branch", "number": 93, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/93", "body": ""}
{"title": "fix generating tests report to Jira for develop branch", "number": 94, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/94", "body": ""}
{"title": "Feature/compile time support for 83", "number": 95, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/95", "body": "Duplicate x83 configurations\nAdd HW specific behavior for x83 USB DK (Switch & LEDs)\nDiscover USB DK during runtime"}
{"title": "removing execution key from the tests creation command", "number": 96, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/96", "body": ""}
{"title": "Add configuration interfaces for the fingerprinting library", "number": 97, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/97", "body": ""}
{"comment": {"body": "Should we also add log level config - or will it be a different PR?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/97/_/diff#comment-81266060"}}
{"comment": {"body": "I will do it in a different PR..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/97/_/diff#comment-81266080"}}
{"comment": {"body": "This was before your commit, but shouldn\u2019t we return immediately if valid\\_packet==false? ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/97/_/diff#comment-81266135"}}
{"comment": {"body": "Yes. We should.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/97/_/diff#comment-81266166"}}
{"comment": {"body": "Fixed in [e6a28f5](https://bitbucket.org/levl/bosch_integration/commits/e6a28f5705925598c9faf1671f7e8678b02b7119)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/97/_/diff#comment-81266190"}}
{"comment": {"body": "If you want to update `g_feature_cfg` for only one feature it would be nice to have a GetTrainConfig\\(\\) function", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/97/_/diff#comment-81266254"}}
{"comment": {"body": "Add the get functions in the commit [971d163](https://bitbucket.org/levl/bosch_integration/commits/971d163fb38ed25fb97da363db4e07cb46a719fd)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/97/_/diff#comment-81266387"}}
{"title": "Feature/BIS-615 fix code metrics", "number": 98, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/98", "body": "trying klocwork custom config\nklocwork: removing import-config\nextracting metrics failures\npython script messes up the URL\nadded recursive function to test CI\n\nRevert \"added recursive function to test CI\"\nThis reverts commit 3e7632249f31ce357064c9a9b226bf0870cb6425.\n\n\nfix merge\n\nFix code metrics errors for fast_math.c\nFixed code metrics errors on q31_math.c and q15_math.c\nfix external server IP\nfix metric issue 2326\ncalc_cfo_model's params are now in a struct\nChange cfo_model_features_to_matrix's params to struct\nrefactor transient_extract_impl to reduce number of functions it's calling"}
{"title": "Feature/FIN-386 BIS 296 project fixes", "number": 99, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/99", "body": "merge CMakelists of tests, except for regression\nFix jenkinsfile\nTry to parallize more in Jenkinsfile\nfix jenkinsfile\nTry other type of parallelization\nFix titles of stages\nfix cpp guards\nFix assignment from incompatible pointer type"}
{"title": "Deepak/test bbpipeline", "number": 1, "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/1", "body": "Testing pipeline\nTesting pipeline\nTesting pipeline\nTesting pipeline\n\n"}
{"title": "LDI-1003 glue SDLC", "number": 2, "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/2", "body": "LDI-1003 Refactor data ingestion jobs & refactor repo as TF modules \n- Remove old job versions and redundant bitbucket-pipelines.yml \n- Refactor job into terraform modules, add READMEs, fix issues \n- Add module fixes \n- Add v2_0_data_catalog_feature_store initial module"}
{"title": "LDI-165 Add default partner_id value", "number": 3, "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/3", "body": "Null or empty values for partner_id from source streams is unexpected behaviour and considered a bug, therefore a manual handling added to move those records in the separate /partner_id=missing_partner_id/ partition"}
{"comment": {"body": "@{712020:84231a92-7832-42a7-a0df-492308919d56} sorry for misleading you\n\ni forgot that we are filtering null values from the sender \n\ni prefer to send and explicit partner id\\(when directing you to send 0 i assumed we can send explicit 0\\) than assuming null means lab-recording", "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/3/_/diff#comment-400215903"}}
{"comment": {"body": "As discussed, updated with `missing_partner_id` value for occurrences when null or empty value produced due to a bug, PCAP processing will produce separate value while parsing", "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/3/_/diff#comment-401904804"}}
{"title": "fix AWS IAM policy ARNs", "number": 4, "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/4", "body": ""}
{"title": "LDI-1276 Add L2 Typing Model true_fingerprints job", "number": 5, "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/5", "body": ""}
{"comment": {"body": "this will become redundant in case we\u2019ll drop `year-month-day-hour` partition schema and start using `yyyy-MM-dd'H'HH`", "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/5/_/diff#comment-401870450"}}
{"title": "LDI-165 Add correct true-fingerprints dedupe", "number": 6, "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/6", "body": ""}
{"title": "Fix repartitioning job broad permissions", "number": 7, "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/7", "body": "Removed AmazonS3FullAccess role from repartitioning jobs"}
{"title": "L278: glue job generating l2 typing model", "number": 8, "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/8", "body": "Implementation of glue job generating l2 typing model by \n\nRetrieving true_fingerprints glue table data\nInner joining it with devices_db data\nUnifying it with latest l2 typing model glue table data\nDeduplicating dataset\nSaving dataset as a new partition in glue table\nCreating the l2 typing model csv file\n\n"}
{"comment": {"body": "This variable is never used, purgeTable not called", "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/8/_/diff#comment-404711063"}}
{"comment": {"body": "api call is redundant here, method `purge_table` does all of that out of the box\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/8/_/diff#comment-404715268"}}
{"comment": {"body": "As Devices DB contain duplicate records, we must eliminate them before the join\n\n![](https://bitbucket.org/repo/q7e4g74/images/*********-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/8/_/diff#comment-*********"}}
{"title": "LDI-1276 Fix data generation", "number": 9, "htmlUrl": "https://bitbucket.org/levl/sparrow-glue-jobs/pull-requests/9", "body": "This fixes the data issue: initially flow was finding only unique mac addresses with the most recent timestamp which is not exactly correct taking into account that we need to find all unique sets of platform_type + l2_fingerprint + model_number , which cannot be done only by mac_addr, as same address can come from different sources and have produce different sets of platform_type + l2_fingerprint + model_number.  \nTherefore we have to join the data first even though it will produce duplicates from the duplicated mac_addr records but it will also have the complete dataset available at the moment of run, the duplicate records are filtered out afterwards specifically by  platform_type + l2_fingerprint + model_number\n"}
{"title": "LDI-165 Add Terraform module and supporting lambdas", "number": 1, "htmlUrl": "https://bitbucket.org/levl/sparrow-lab-recordings/pull-requests/1", "body": "LDI-165 Add list-files and notify-slack lambdas\nLDI-165 Add list-files lambda parameters and validation, add README's\nLDI-165 Add terraform module of the whole pipeline\nLDI-165 Fix Container Name override\nLDI-165 Fix Container logging\nLDI-165 Fix execution role permissions\nLDI-165 Update module's README.md\n\n"}
{"title": "LDI-165 Fix Backfill SFN syntax", "number": 2, "htmlUrl": "https://bitbucket.org/levl/sparrow-lab-recordings/pull-requests/2", "body": ""}
{"title": "LDI-165 Add Firehose stream definition into the module", "number": 3, "htmlUrl": "https://bitbucket.org/levl/sparrow-lab-recordings/pull-requests/3", "body": "The PCAP processing requires its own Firehose stream to be able to populate RAW events S3 directory, this is the same directory where events from actual devices are stored by Cujo Lens System stream.\nDue to the same destination, streams must have same configuration to avoid data reading issues. This is something that cannot be easily automated because of the fact that original stream is provisioned in different AWS account without any permissions to access it. \nHence the configuration should be controlled by SLAs and team communication to ensure the streams aligned."}
{"title": "Feature/api server", "number": 1, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/1", "body": "Windows stuff\nswagger server added\nserver changes\nfix tests\n\n"}
{"title": "Fix config structure", "number": 10, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/10", "body": "this pr will change a little bit the configuration structure. \n\n"}
{"title": "helm action return errors messages", "number": 100, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/100", "body": ""}
{"title": "Feature/helm errors", "number": 101, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/101", "body": "removed topology keys from service\nupdate helm deployment version\n\n"}
{"title": "update helm deployment version", "number": 102, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/102", "body": ""}
{"title": "update helm deplpoyment version", "number": 103, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/103", "body": ""}
{"title": "fix stderr condition", "number": 104, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/104", "body": ""}
{"title": "fix yaml parsing error", "number": 105, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/105", "body": ""}
{"title": "update api package", "number": 106, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/106", "body": ""}
{"title": "Bugfix/MEROSP-1042 swagger ui   devices tenant", "number": 107, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/107", "body": "fixed classification bug\nlint\n\n"}
{"title": "added option for chart version in tenant post and update", "number": 108, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/108", "body": ""}
{"comment": {"body": "let\u2019s also promote to latest eros-api version", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/108/_/diff#comment-312426717"}}
{"title": "support latest version of api and helm deployment tools", "number": 109, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/109", "body": ""}
{"title": "added remove devices, improve tests", "number": 11, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/11", "body": ""}
{"comment": {"body": "Good reuse of existing code! Good job!", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/11/_/diff#comment-233364306"}}
{"title": "[Snyk] Security upgrade python from 3.9.7-slim to 3.10-slim", "number": 110, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/110", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.10-slim, as this image has only 49 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 714  | Directory Traversal SNYK-DEBIAN11-DPKG-2847942   | No Known Exploit   |\n|    | 614  | Loop with Unreachable Exit Condition (Infinite Loop) SNYK-DEBIAN11-OPENSSL-2426309   | No Known Exploit   |\n|    | 714  | OS Command Injection SNYK-DEBIAN11-OPENSSL-2807596   | No Known Exploit   |\n|    | 833  | OS Command Injection SNYK-DEBIAN11-OPENSSL-2933518   | No Known Exploit   |\n|    | 833  | OS Command Injection SNYK-DEBIAN11-OPENSSL-2933518   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.9.7-slim to 3.10.4-slim-bullseye", "number": 111, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/111", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.10.4-slim-bullseye, as this image has only 49 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 714  | Directory Traversal SNYK-DEBIAN11-DPKG-2847942   | No Known Exploit   |\n|    | 614  | Loop with Unreachable Exit Condition (Infinite Loop) SNYK-DEBIAN11-OPENSSL-2426309   | No Known Exploit   |\n|    | 714  | OS Command Injection SNYK-DEBIAN11-OPENSSL-2807596   | No Known Exploit   |\n|    | 833  | OS Command Injection SNYK-DEBIAN11-OPENSSL-2933518   | No Known Exploit   |\n|    | 833  | OS Command Injection SNYK-DEBIAN11-OPENSSL-2933518   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Client api", "number": 112, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/112", "body": "update python version + push to cujo ecr step\nupdate python version + push to cujo ecr step\nfix dockerfile\nfix dockerfile\nadd new customer api with vaults and devices api only\n\n"}
{"title": "Bugfix/MEROSP-1200 swagger   remove tenant api", "number": 113, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/113", "body": "catch k8s failed containers\ncatch k8s failed containers\n\n"}
{"title": "fix typo", "number": 114, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/114", "body": ""}
{"title": "Client api", "number": 115, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/115", "body": "add client api helm chart + bb step to build and push it\ntrigger build\nchange chart version\n\n"}
{"title": "refactor dump and remove body, add prefix folder name", "number": 116, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/116", "body": ""}
{"title": "enable cors for client api", "number": 117, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/117", "body": ""}
{"title": "Feature/MEROSP-1275 cujo demo reflect our perfor", "number": 118, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/118", "body": "add static vault list api endpoint\nadd cors settings\nfix get prev macs\nremoved custom pipeline\nremove comments\n\n"}
{"title": "translate agentserial to uuid upon env var TRANSFORM_AGENT_SERIAL_TO_UUID", "number": 119, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/119", "body": "adds the agent serial to uuid translation \ncontrolled by boolean environment variable (defaulted to not apply the transformation meaning by default keeps previous behavior)"}
{"title": "Feature/EROS-15 api functionality   feature requ", "number": 12, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/12", "body": ""}
{"title": "add device typing results to device schema + fixes to client helm chart", "number": 120, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/120", "body": ""}
{"title": "set hostname to hidden if not given", "number": 121, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/121", "body": ""}
{"title": "MEROSP-1325 extend device model", "number": 122, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/122", "body": "add l2 caps\nadd last_evt_radio_details\nadd vpn_type\n\n"}
{"title": "remove flake - temp", "number": 123, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/123", "body": ""}
{"title": "MEROSP-1325 extend device model", "number": 124, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/124", "body": "trigger build image\nuse eros-api 0.59.0\nfix device_type field\n\n"}
{"title": "MEROSP-1325 extend device model", "number": 125, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/125", "body": "change os range to ui\nset hiddname hostname to devices without hostnames\nrestore custom pipeline\ntrigger push\nadd bannd and l2 caps\nWIP\ntest settings\nadd last_evt_radio_details\nfix flake8\nbuild client\ntmp build every change in the repo\nstructure the object in swagger / yaml\nadd vpn_type\nupdate eros api version\nremove flake - temp\ntrigger build image\nuse eros-api 0.59.0\nfix device_type field\nadd last connected time\n\n"}
{"title": "Feature/MEROSP-1325 v1.5 poc demo ui preparation", "number": 126, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/126", "body": "change os range to ui\nset hiddname hostname to devices without hostnames\nrestore custom pipeline\ntrigger push\nadd bannd and l2 caps\nWIP\ntest settings\nadd last_evt_radio_details\nfix flake8\nbuild client\ntmp build every change in the repo\nstructure the object in swagger / yaml\nadd vpn_type\nupdate eros api version\nremove flake - temp\ntrigger build image\nuse eros-api 0.59.0\nfix device_type field\nadd last connected time\nfix device model\n\n"}
{"title": "fix lint", "number": 127, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/127", "body": ""}
{"title": "fix type vendor", "number": 128, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/128", "body": ""}
{"title": "add vpn type", "number": 129, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/129", "body": ""}
{"title": "fix tenant api", "number": 13, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/13", "body": ""}
{"title": "Test new api", "number": 130, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/130", "body": "test new api version\ntrigger build client api\ntest api\ntest api\ntest api\ntest api\ntest api\nremove pipeline\n\n"}
{"title": "[Snyk] Security upgrade urllib3 from 1.26.4 to 1.26.5", "number": 131, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/131", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nvirtualenv 20.15.1 has requirement filelock=3.2, but you have filelock 3.0.12.\njsonschema 3.2.0 requires pyrsistent, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  479/1000  Why?* Has a fix available, CVSS 5.3  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-URLLIB3-1533435 |  urllib3: 1.26.4 - 1.26.5  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "[Snyk] Security upgrade setuptools from 39.0.1 to 65.5.1", "number": 132, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/132", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nvirtualenv 20.15.1 has requirement filelock=3.2, but you have filelock 3.0.12.\njsonschema 3.2.0 requires pyrsistent, which is not installed.\nflask-marshmallow 0.14.0 requires marshmallow, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  441/1000  Why?* Recently disclosed, Has a fix available, CVSS 3.1  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3113904 |  setuptools: 39.0.1 - 65.5.1  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "MEROSP-2191 - fix remove_device method (remove invocation of non existing method signature)", "number": 133, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/133", "body": "remove invocation of delete_items(vault_id=self.vault_id, device_id=device_id), replace with get by device_id and then delete by mac_address"}
{"title": "MEROSP-789 vault swagger", "number": 134, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/134", "body": "MEROSP-789-vault-swagger\n\n\nsplit client api\n\nGenerate up to date swagger\nAdd services\nRemove bl from controllers, invoke services instead\nAdd agents endpoints (vaults via agent_serial)\nAdd debug vault endpoint\n\n\n\nSplit backoffice api from client api (remove vaults and devices apis)\n\nUpdate swagger\nAdd services for bl\nRemove bl from controllers, invoke services\n\n\n\n\n\n"}
{"title": "[Snyk] Security upgrade certifi from 2020.12.5 to 2022.12.7", "number": 135, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/135", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\ntest-requirements.txt\n\n\n\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  626/1000  Why?* Recently disclosed, Has a fix available, CVSS 6.8  | Insufficient Verification of Data Authenticity  SNYK-PYTHON-CERTIFI-3164749 |  certifi: 2020.12.5 - 2022.12.7  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "MEROSP-2491 - tenant/{}/status - 400 -> 404 on tanant not found", "number": 136, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/136", "body": ""}
{"title": "MEROSP-789", "number": 137, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/137", "body": "rest api server - port -> 5000"}
{"title": "MEROSP-789", "number": 138, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/138", "body": "swagger.yml url"}
{"title": "MEROSP-789 vault swagger", "number": 139, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/139", "body": "client-configuration-api - swagger.yml url client-configuration-api bump rollme hash [skip ci] bumped helm Chart.yaml version bitbucket-pipelines.yml - deployClientToBranch - set image.tag to branch name\n\n"}
{"comment": {"body": "wh? you should use only eros-api.. where it being used?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/139/_/diff#comment-355034669"}}
{"title": "add session id in db", "number": 14, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/14", "body": ""}
{"title": "[Snyk] Security upgrade setuptools from 39.0.1 to 65.5.1", "number": 140, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/140", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nvirtualenv 20.15.1 has requirement filelock=3.2, but you have filelock 3.0.12.\njsonschema 3.2.0 requires pyrsistent, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  551/1000  Why?* Recently disclosed, Has a fix available, CVSS 5.3  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3180412 |  setuptools: 39.0.1 - 65.5.1  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "MEROSP-789", "number": 141, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/141", "body": "correct vault_id references & remove unused ones - self.vault_id -> vault_id change client-configuration-api port to be configurable with environment variable with a default value"}
{"title": "MEROSP-2702", "number": 142, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/142", "body": "Update device schema in swagger add device adapter align changed \\ removed fields"}
{"comment": {"body": "@{6370afc2f48fbd9b62d2ceb9} this change breaks UI compatibility. Does this change is required? If yes, can we make it similar to our current result\\_log fields, so it will have additional values?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/142/_/diff#comment-357164958"}}
{"title": "MEROSP-2729 get device 500", "number": 143, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/143", "body": "MEROSP-2729\n\nFix get_device_info 500 error\nRemove device service from backoffice api\n\n\n\nflake 8\n\n\n"}
{"comment": {"body": "is it safe ?  \nwhat happens if no devices ?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/143/_/diff#comment-357886922"}}
{"title": "MEROSP-2729", "number": 144, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/144", "body": "null saftey"}
{"title": "MEROSP-2739 - status field + MEROSP-2740 - vault \\ device removal status 201 -> 202", "number": 145, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/145", "body": "MEROSP-2739\nget_device_info - device adapter env - add vault to VAULTS_LIST\n\n\nMEROSP-2740 - vault \\ device removal status - 201 -> 202\n\n\n"}
{"title": "bitbucket-pipelines - pushApiToCujoECR - fix syntax", "number": 146, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/146", "body": ""}
{"title": "bitbucket-pipelines - dev: make steps parallel", "number": 147, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/147", "body": ""}
{"title": "bitbucket-pipelines - pushApiToCujoECR - fix syntax", "number": 148, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/148", "body": ""}
{"title": "bitbucket-pipelines - pushApiToCujoECR - fix syntax", "number": 149, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/149", "body": ""}
{"title": "Api improvements", "number": 15, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/15", "body": "start to change api\nfixed tests\n\n"}
{"comment": {"body": "From CRUD point of view - shouldn\u2019t HTTP DELETE be used for deletion of agents?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/15/_/diff#comment-*********"}}
{"comment": {"body": "API: agents/<agent\\_id> DELETE will remove the resource agent with agent\\_id\n\nAPI: agents can\u2019t be invoked with DELETE as we are not removing the resource \\(DELETE don\u2019t have payload\\), thus PUT is used to perform cleanup to the underlying resources", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/15/_/diff#comment-*********"}}
{"title": "MEROSP-2154 test and staging env changes", "number": 150, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/150", "body": ""}
{"title": "LDI-84 - fix device deletion. use named parameters", "number": 151, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/151", "body": ""}
{"title": "[Snyk] Security upgrade python from 3.9.7-slim to 3.10.9-slim", "number": 152, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/152", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.10.9-slim, as this image has only 47 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 714  | Directory Traversal SNYK-DEBIAN11-DPKG-2847942   | No Known Exploit   |\n|    | 714  | Out-of-bounds Read SNYK-DEBIAN11-LIBTASN16-3061097   | No Known Exploit   |\n|    | 614  | Loop with Unreachable Exit Condition (Infinite Loop) SNYK-DEBIAN11-OPENSSL-2426309   | No Known Exploit   |\n|    | 714  | OS Command Injection SNYK-DEBIAN11-OPENSSL-2807596   | No Known Exploit   |\n|    | 714  | OS Command Injection SNYK-DEBIAN11-OPENSSL-2933518   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade ipython from 7.31.1 to 8.10.0", "number": 153, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/153", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nvirtualenv 20.15.1 has requirement filelock=3.2, but you have filelock 3.0.12.\njsonschema 3.2.0 requires pyrsistent, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  603/1000  Why?* Proof of Concept exploit, Recently disclosed, Has a fix available, CVSS 4.2  | Remote Code Execution (RCE)  SNYK-PYTHON-IPYTHON-3318382 |  ipython: 7.31.1 - 8.10.0  |  No  | Proof of Concept \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Bump eros-api version", "number": 154, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/154", "body": "This is with the changes related to{: data-inline-card='' }"}
{"title": "LDI-458 Update minor python version to fix critical vulnerabilities", "number": 155, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/155", "body": ""}
{"comment": {"body": "Why not using same base as classifier?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/155/_/diff#comment-369576175"}}
{"comment": {"body": "We could use the same image as classifier however classifier image is on python 3.10 and client-configuration-api is on 3.9. We might want to do thorough testing if we want to go there. Kept this 3.9 for now and increased patch version only to remove high priority vulnerabilities first and we can make this similar to classifier later on \\(from backlog\\).\n\nIf you think we should go there, let me know. I\u2019ll change the base image", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/155/_/diff#comment-369702522"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} preferred to unite to same docker base image to make sure we have same security checks on all containers.\n\nIf it won\u2019t go smooth, please advise.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/155/_/diff#comment-369807810"}}
{"comment": {"body": "@{5f82bf320756940075db755e} I suggest that we make python version consistent across all of our images later. We could create a ticket and put it in backlog, probably take it up in the next sprint. At that point, backend developers can also test out thoroughly this python upgrade before release. \n\nSince right now our first priority is to fix vulnerability, this change can go faster. Moreover, the image that classifier uses right now has one critical vulnerability reported. We should probably fix that and once we have some clarity, we can change this image to make it same as classifier otherwise we could end up introducing the same issue here. In this regard, I have already opened up a case with AWS support to get some guidance. Awaiting the response. ", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/155/_/diff#comment-369851932"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} Ok, do we have now zero critical issues in all scans here?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/155/_/diff#comment-370561828"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Yes", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/155/_/diff#comment-370588436"}}
{"title": "[Snyk] Security upgrade werkzeug from 2.0.1 to 2.2.3", "number": 156, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/156", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nvirtualenv 20.15.1 has requirement filelock=3.2, but you have filelock 3.0.12.\njsonschema 3.2.0 requires pyrsistent, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  416/1000  Why? Recently disclosed, Has a fix available, CVSS 2.6  | Access Restriction Bypass  SNYK-PYTHON-WERKZEUG-3319935 |  werkzeug: 2.0.1 - 2.2.3  |  No  | No Known Exploit \n  |  661/1000  Why?* Recently disclosed, Has a fix available, CVSS 7.5  | Denial of Service (DoS)  SNYK-PYTHON-WERKZEUG-3319936 |  werkzeug: 2.0.1 - 2.2.3  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Feature/LDI-458 fix all critical vulnerabilities", "number": 157, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/157", "body": "fix test requirements\n\n\n"}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} please approve. I have also aligned with our snyk code analysis.\n\n@{6370afc2f48fbd9b62d2ceb9} why we have different dockerfile for backoffice and clientapi? we don\u2019t need different images. burden of maintanance.", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/157/_/diff#comment-370640147"}}
{"title": "[Snyk] Fix for 4 vulnerabilities", "number": 158, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/158", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nvirtualenv 20.15.1 has requirement filelock=3.2, but you have filelock 3.0.12.\njsonschema 3.2.0 requires pyrsistent, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  603/1000  Why? Proof of Concept exploit, Recently disclosed, Has a fix available, CVSS 4.2  | Remote Code Execution (RCE)  SNYK-PYTHON-IPYTHON-3318382 |  ipython: 7.31.1 - 8.10.0  |  No  | Proof of Concept \n  |  509/1000  Why? Has a fix available, CVSS 5.9  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3180412 |  setuptools: 39.0.1 - 65.5.1  |  No  | No Known Exploit \n  |  416/1000  Why? Recently disclosed, Has a fix available, CVSS 2.6  | Access Restriction Bypass  SNYK-PYTHON-WERKZEUG-3319935 |  werkzeug: 2.0.1 - 2.2.3  |  No  | No Known Exploit \n  |  661/1000  Why?* Recently disclosed, Has a fix available, CVSS 7.5  | Denial of Service (DoS)  SNYK-PYTHON-WERKZEUG-3319936 |  werkzeug: 2.0.1 - 2.2.3  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "support removal of agent id", "number": 159, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/159", "body": ""}
{"comment": {"body": "@{606d973d3e6ea000685ed65f}  is there no 200 response ?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/159/_/diff#comment-371959244"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} this is temorary until the eros-api gets a bump ?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/159/_/diff#comment-371959801"}}
{"comment": {"body": "202 represent \u201cAccepted\u201d which means that the deletion request sent successfully to DynamoDB, because its an async request we\u2019re not validating it thus the absent of 200.", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/159/_/diff#comment-371959848"}}
{"comment": {"body": "No need to relay on eros-api for this one, the logic wont change i\u2019d rather copy a small piece of code that most likely wont change than tie myself to eros-api dependencies\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/159/_/diff#comment-371960692"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} got it", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/159/_/diff#comment-371961230"}}
{"title": "Dev ops/config maps", "number": 16, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/16", "body": "template the config map\nsave kube files to artifacts\n\n"}
{"title": "LDI-8 code scan improvements and snyk fixes", "number": 160, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/160", "body": "Improve code scanning, bump packages and fix snyk alerts"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35646](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35646){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/160/_/diff#comment-379570225"}}
{"title": "handle prev macs non existing", "number": 161, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/161", "body": ""}
{"title": "bump version", "number": 162, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/162", "body": ""}
{"title": "Feature/LDI-354 reduce device record size", "number": 163, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/163", "body": "This PR updates the api version that matches all the changes that were made in the LDI-354 issue in the classifier, which handled the db reduction of record sizes"}
{"title": "LDI-284 update to latest eros-api in client api", "number": 164, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/164", "body": ""}
{"title": "LDI-734: add ability to deploy client api to cujo stage", "number": 165, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/165", "body": ""}
{"title": "LDI-734: add ability to deploy client api to cujo stage", "number": 166, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/166", "body": ""}
{"title": "LDI-1310 - bump eros-api pacakge dependncy", "number": 167, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/167", "body": "bump dependency in eros-api\nremove codecov (using python-lcov instead)"}
{"comment": {"body": "related Pull requests -   \neros-automation [https://bitbucket.org/levl/eros-automation/pull-requests/532](https://bitbucket.org/levl/eros-automation/pull-requests/532){: data-inline-card='' }   \neros-classifier [https://bitbucket.org/levl/eros-classifier/pull-requests/1400/ldi-1310-get-all-available-platform-name](https://bitbucket.org/levl/eros-classifier/pull-requests/1400/ldi-1310-get-all-available-platform-name){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/167/_/diff#comment-404706660"}}
{"title": "engine and session maker out of context", "number": 17, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/17", "body": ""}
{"comment": {"body": "make sure that when exception occurs the engine is not recreated", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/17/_/diff#comment-235075797"}}
{"comment": {"body": "did you mean to print the variables `db name, user, password, db host`?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/17/_/diff#comment-235077522"}}
{"comment": {"body": "no\u2026but I can", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/17/_/diff#comment-235083016"}}
{"title": "Bugfix/reports csv issue", "number": 18, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/18", "body": "fixed csv file issue, added option for no duration\nflake\n\n"}
{"title": "Fix get device list", "number": 19, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/19", "body": "fix get deivce bug\nadded agents list\n\n"}
{"title": "add automation environment", "number": 2, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/2", "body": "add automation environment"}
{"title": "handle result topic in create and delete", "number": 20, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/20", "body": ""}
{"title": "Dev", "number": 21, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/21", "body": "initial commit\nInitial Bitbucket Deployments configuration\ncicd\nmakefile\ntests\nadd compose environment\nwait_for_postgres\nflake8\ntest_env_file\n-\ntests\ntests\ndeploy service\n-\naws region as variable\n\n"}
{"title": "split service from deployment k8s", "number": 22, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/22", "body": ""}
{"title": "split service from deployment k8s", "number": 23, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/23", "body": ""}
{"title": "Multi region", "number": 24, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/24", "body": "changes\ntypo fix\n\n"}
{"title": "Dev", "number": 25, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/25", "body": "changes\ntypo fix\n\n"}
{"title": "fix multirigion", "number": 26, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/26", "body": ""}
{"title": "Dev", "number": 27, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/27", "body": "fix multirigion\ntypo\n\n"}
{"title": "fix s3_prefix default to cluster name", "number": 28, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/28", "body": ""}
{"title": "fix s3_prefix default to cluster name", "number": 29, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/29", "body": ""}
{"title": "Dev", "number": 3, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/3", "body": "deploy to dev\nswagger server added\nserver changes\nfix tests\nci fix\nfix BIBUCKET_BRANCH for docker tag\nfix BIBUCKET_BRANCH for docker tag\nnew pipe\nfix makefile\nfixes for ci\nfix env var\ndb script\nflake fix\ndeploy hack - dont merge\ntypo\nfix port\nadded some logs\n+history\ntest stale image\ndeploy test\nchange revision in deploy to dev\ntypo\ntest\ntest2\nadd automation environment\nfix makefile\npr changes\nadded rollout to all deploys\n\n"}
{"title": "Fix the PG HOST in config map", "number": 30, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/30", "body": ""}
{"title": "Fix the PG HOST in config map", "number": 31, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/31", "body": ""}
{"title": "added snapshot env vars", "number": 32, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/32", "body": "\n\nadded snapshot env vars\n\n"}
{"title": "added snapshot env vars", "number": 33, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/33", "body": "\n\nadded snapshot env vars\n\n"}
{"title": "Api split", "number": 34, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/34", "body": "\n\n\nApi split\nedit bb-pp configs\nfix to tenant id pass\nfixes to configmap parmas\nadded option to choose deployment type\ndeploy admin on default\nadd cicd deployment\nelaborated about snapshot\n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} why have you removed those file?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/34/_/diff#comment-*********"}}
{"title": "removed decision file report", "number": 35, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/35", "body": "\n\nremoved decision file report\n\n"}
{"title": "kafka retention policy and cicd master branch pipeline ref", "number": 36, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/36", "body": "add topic retention policy\nfix retention policy syntax\nfix retention policy syntax\nchange rp to 1 hour per topic\nauto deploy redis\ncustom ron deploy\nchange branch ref to master\nchange branch ref to master\nremoved pipelines\nremoved dask and change values to snapshot params\nadd kafka rp\n\n"}
{"title": "Dev", "number": 37, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/37", "body": "add topic retention policy\nfix retention policy syntax\nfix retention policy syntax\nchange rp to 1 hour per topic\nauto deploy redis\ncustom ron deploy\nchange branch ref to master\nchange branch ref to master\nremoved pipelines\nremoved dask and change values to snapshot params\nadd kafka rp\nadd kafka rp\nadd kafka rp\nadd kafka rp\n\n"}
{"title": "add pg and redis hostname", "number": 38, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/38", "body": ""}
{"title": "fix pg host ref", "number": 39, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/39", "body": "add pg and redis hostname\nfix redis name\nadd kafka env var\npoint to infra kafka\nfix pg host ref\n\n"}
{"title": "start watching...", "number": 4, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/4", "body": "alot\n"}
{"title": "add pg port", "number": 40, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/40", "body": ""}
{"title": "Micro services deploy", "number": 41, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/41", "body": "change kafka poll interval\nchange kafka poll interval\n\n"}
{"title": "change kafka poll interval", "number": 42, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/42", "body": "add pg and redis hostname\nfix redis name\nadd kafka env var\npoint to infra kafka\nfix pg host ref\nadd pg port\nchange kafka poll interval\nchange kafka poll interval\nchange kafka poll interval\n\n"}
{"title": "Db schema integration", "number": 43, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/43", "body": "changes to support multi dbs\nfix build\nmove the db_host in deploy config, added update to exiting tenant\nfix tests\n[skip ci] try to make tests step faster\n\n"}
{"comment": {"body": "make docker\\_build", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/43/_/diff#comment-250490454"}}
{"title": "Dev", "number": 44, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/44", "body": "add pg and redis hostname\nfix redis name\nadd kafka env var\npoint to infra kafka\nfix pg host ref\nadd pg port\nchange kafka poll interval\nchange kafka poll interval\nchange kafka poll interval\nadd tenant name to config map\nadd debug for topic creation\nadd debug for topic creation\nrm topic rp\nrm topic rp\nchanges to support multi dbs\nfix build\nmove the db_host in deploy config, added update to exiting tenant\nfix tests\n[skip ci] try to make tests step faster\nfix db context for partial api\nno more device_\nget tenant vaults in get tenant api\n\n"}
{"title": "Backoffice ns", "number": 45, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/45", "body": "\n\nchange default deploy to backoffice namespace\n\n"}
{"comment": {"body": "can you verify those are assigned to different clusters? eros-dev for dev branch and eros-prod for master", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/45/_/diff#comment-251796834"}}
{"comment": {"body": "it goes to the deployment that i assign it and takes the cluster name", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/45/_/diff#comment-251798537"}}
{"title": "fix list vaults", "number": 46, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/46", "body": ""}
{"title": "detailed device, history per device", "number": 47, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/47", "body": ""}
{"comment": {"body": "@{6085103f5797db006947d59a} remove the DeviceUISchema", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/47/_/diff#comment-253109308"}}
{"title": "Helm chart", "number": 48, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/48", "body": "\n\ndeploy helm chart\nadd helm chart and configmap env var\nfix pipeline trigger\nadd custom stack\nadd api configmap\nchange pipe ref to master branch\n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} Bitbucket pipelines not suppose to change? to deploy the helm? where you identify if it\u2019s customer or backoffice api?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/48/_/diff#comment-253109099"}}
{"comment": {"body": "fixed", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/48/_/diff#comment-253200307"}}
{"title": "Bug fix/list vaults", "number": 49, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/49", "body": "this merge will fix the list vaults bug which didn't show data for empty vaults, and another issue that I had with list all tenants.\n  fixed list tenants\nfixed list vaults and added test\n\n"}
{"comment": {"body": "why create vault here? what is the case / api?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/49/_/diff#comment-253761540"}}
{"comment": {"body": "an empty one for the list\\_vaults bug that didn\u2019t display empty vaults.", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/49/_/diff#comment-253762973"}}
{"title": "Api improvements", "number": 5, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/5", "body": "some api fixes\ntests fixed\n\n"}
{"title": "add git commit and user to ci", "number": 50, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/50", "body": "add pg and redis hostname\nfix redis name\nadd kafka env var\npoint to infra kafka\nfix pg host ref\nadd pg port\nchange kafka poll interval\nchange kafka poll interval\nchange kafka poll interval\nadd tenant name to config map\nadd debug for topic creation\nadd debug for topic creation\nrm topic rp\nrm topic rp\nchanges to support multi dbs\nfix build\nmove the db_host in deploy config, added update to exiting tenant\nfix tests\n[skip ci] try to make tests step faster\nfix db context for partial api\nno more device_\nget tenant vaults in get tenant api\nalign swagger client specs with admin\nwait for topic deletation\nchange default deploy to backoffice namespace\nchange default deploy to backoffice namespace\nfix deployment ref\ndelete irrelvent deployments\ndeploy helm chart\ndeploy helm chart\ndeploy helm chart\ndeploy helm chart\ndeploy helm chart\nfix list vaults\nfix query to get devices and agents count\nremove unused\ndetailed device, history per device\nfixed get device detailed to allow none values\nremoved DeviceUI\nformat history msg\nflake\nadd helm chart and configmap env var\nfix pipeline trigger\nfix pipeline trigger\nadd custom stack\nadd custom stack\nadd custom stack\nadd api configmap\nchange pipe ref to master branch\nchange pipe ref to master branch\nremoved some models, removed flag from devices\nfix tests\nfixed customer swagger.yaml\nadded customer tests infra\nfixed list devices\nmodel changes\nmodified charts\nfix typo\nfix api bb piepline\nfix api bb piepline\nfix values path\nfix values\ntests for customer api\nfix\nfix\nfix pp url\nfix pp branch ref\nfix url\nfix cicd branch ref\nfix kafka and pipeline url defaults\nfix release name\nfix pipeline\nfix pipeline\nrun tests on changes only\nnew chart version\nchart fix\n\nRevert \"Merged in helm_chart (pull request #48)\"\nThis reverts commit d472e36cd5bd2b2031bf2b06d44786c6f1662ae4, reversing changes made to 06b34a1504230865638ac60449a89d4f723aa56c.\n\n\nadd git commit and user to ci\n\n\n"}
{"title": "Ui api adaptions2", "number": 51, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/51", "body": "added fields, fixed some delete issues\nsome fixes\n\n"}
{"title": "Infrastructure/redis db accessor", "number": 52, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/52", "body": "device management through redis\nflake8 fix\nremove devices and vault from both pg and redis\nadd redis for testings\nadd remove all_vaults\nbatched remove_all_vaults\nflake8\nimplement redisdbaccessor on vaults_controller\nflake8\n\n"}
{"comment": {"body": "consider reusing the remove vault function", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/52/_/diff#comment-254531804"}}
{"comment": {"body": "we should probably use prefix of vault\\_id for devices and thus with scan prefix delete all it\u2019s vaults", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/52/_/diff#comment-254531898"}}
{"comment": {"body": "do we duplicate code from classifier redis db\\_engine?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/52/_/diff#comment-254532054"}}
{"title": "Helm chart", "number": 53, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/53", "body": "test api chart\ntest api chart\ntest api chart\ntest api chart\ntest api chart\ntest api chart\nadd update flag\nadd update flag\nremoved pg and redis ns prefix\nadd init container\nchange pg name\nchange pg name\nremove local staff\n\n"}
{"title": "Bug/helm chart metrics", "number": 54, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/54", "body": "add pipfile to gitignore\nexpose metrics\nhard code metrics service cto clusterip\n\n"}
{"title": "test redis connection before saving", "number": 55, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/55", "body": ""}
{"title": "Hotfix/pass redis host", "number": 56, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/56", "body": "\n\npass redis host\n\n"}
{"title": "redis tests", "number": 57, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/57", "body": "fix redis engine"}
{"title": "point pytest to new rp", "number": 58, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/58", "body": ""}
{"title": "Feature/deploy pg", "number": 59, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/59", "body": "\n\ndeplpy pg with backoffice api\ndeplpy pg with backoffice api, fix conflicts\n\n"}
{"title": "fixed models", "number": 6, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/6", "body": ""}
{"title": "Feature/api security", "number": 60, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/60", "body": "deploy flask server with ssl\ndeploy api with ssl\ndeploy api with ssl\npoint to test branch cicd\nremove cicd branch ref\n\n"}
{"title": "trying to fix session maker", "number": 61, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/61", "body": ""}
{"title": "Dev", "number": 62, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/62", "body": "trying to fix session maker\nfix drop\nadded engine status logs\nincreasing sql engine pool size\n\n"}
{"title": "stick to old db scheme", "number": 63, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/63", "body": ""}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim", "number": 64, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/64", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.9.7-slim, as this image has only 38 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Licenses split", "number": 65, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/65", "body": "install test packages from pipeline\nadd bb clone dir\nfix packages\nupdated pluggy version\nmodifying makefile\nadd flake test to test env\n\n"}
{"title": "Feature/support cors", "number": 66, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/66", "body": "ignore Pipfile\nsuppoer cors\napply cors policy\n\n"}
{"title": "Bug fix/vaults", "number": 67, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/67", "body": "remove tenant id when working in tenant api\nmodify tests\n\n"}
{"title": "fix redis connection", "number": 68, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/68", "body": ""}
{"title": "new config keys", "number": 69, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/69", "body": ""}
{"title": "name validation", "number": 7, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/7", "body": ""}
{"title": "send UI_IMAGE_TAG to deployment", "number": 70, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/70", "body": ""}
{"title": "added params to answer", "number": 71, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/71", "body": ""}
{"title": "typing default values", "number": 72, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/72", "body": ""}
{"title": "remove unused pakcages and split to test pytest", "number": 73, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/73", "body": ""}
{"title": "fix os model issue", "number": 74, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/74", "body": ""}
{"title": "redis delete fix", "number": 75, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/75", "body": ""}
{"title": "fix list agents docs", "number": 76, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/76", "body": ""}
{"title": "fix history to show hw_mac or mac_addr or None", "number": 77, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/77", "body": ""}
{"title": "msk", "number": 78, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/78", "body": ""}
{"title": "use clusterip for customer config and lb for admin", "number": 79, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/79", "body": "for costs reason\nchanging customer config to use clusterIP (we will use port fwd to access it)\n"}
{"comment": {"body": "it\u2019s for dev or prod?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/79/_/diff#comment-273747540"}}
{"title": "Name validation", "number": 8, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/8", "body": "fix test\nfix util.py\nflake fix\nflake fix\n\n"}
{"title": "set backoffice ssl certificate on pipeline deploy", "number": 80, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/80", "body": "set backoffice ssl certificate on pipeline deploy"}
{"title": "[Snyk] Security upgrade ipython from 7.23.1 to 7.31.1", "number": 81, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/81", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nvirtualenv 20.13.0 has requirement filelock=3.2, but you have filelock 3.0.12.\njsonschema 3.2.0 requires pyrsistent, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n | Arbitrary Code Execution  SNYK-PYTHON-IPYTHON-2348630 |  ipython: 7.23.1 - 7.31.1  |  No  | No Known Exploit \nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the effected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "[Snyk] Security upgrade ipython from 7.23.1 to 7.31.1", "number": 82, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/82", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nvirtualenv 20.13.0 has requirement filelock=3.2, but you have filelock 3.0.12.\njsonschema 3.2.0 requires pyrsistent, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  696/1000  Why?* Recently disclosed, Has a fix available, CVSS 8.2  | Arbitrary Code Execution  SNYK-PYTHON-IPYTHON-2348630 |  ipython: 7.23.1 - 7.31.1  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the effected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Feature/telegraf to cloudwatch", "number": 83, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/83", "body": "telegraf metrics fwd\n[skip ci] bumped helm Chart.yaml version\n\n"}
{"title": "remove alot unused", "number": 84, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/84", "body": ""}
{"title": "add ingress to customer api", "number": 85, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/85", "body": ""}
{"title": "remove init container", "number": 86, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/86", "body": "remove init container"}
{"title": "changed default l2 path", "number": 87, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/87", "body": ""}
{"title": "param support for multiplatform l2 typing model", "number": 88, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/88", "body": ""}
{"title": "new mac vendor db", "number": 89, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/89", "body": ""}
{"title": "all", "number": 9, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/9", "body": ""}
{"title": "managae topics from the api, raw, result, decision. error logs", "number": 90, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/90", "body": ""}
{"title": "Refactor helm chart", "number": 91, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/91", "body": "test cicd\nedit bb pipelines\nfix bb pipelines syntax\ncreate namespace in backoffice deployment\nchange cicd branch\nhard coded kafka address\nadd deployment namespace to values file\n\n"}
{"title": "Refactor helm chart", "number": 92, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/92", "body": "test cicd pipeline\nadd deploy ns to bb pipelines\n\n"}
{"title": "typo", "number": 93, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/93", "body": ""}
{"title": "Helm tenant deployment locally", "number": 94, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/94", "body": "deploy teanant locally\nadd README file\nremove certs from code\ndisabled pg in api customer deployment\n\n"}
{"comment": {"body": "what the install step for?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/94/_/diff#comment-*********"}}
{"comment": {"body": "can I add different path to config file?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/94/_/diff#comment-284715516"}}
{"comment": {"body": "Not at the moment", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/94/_/diff#comment-284715829"}}
{"comment": {"body": "it should placed in classifer, as localy we are working in classifeir..", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/94/_/diff#comment-284715847"}}
{"comment": {"body": "Duplicate it between these repos ? most of this code will be used as api utils too ", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/94/_/diff#comment-284716408"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} it can be used as package from api or similar", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/94/_/diff#comment-284716772"}}
{"comment": {"body": "renamed to Setup", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/94/_/diff#comment-284716945"}}
{"title": "Helm tenant deployment", "number": 95, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/95", "body": "\n\ndeploy tenants using helm\n\n"}
{"comment": {"body": "why do we need to git clone here? is there a better way to do it? it\u2019s not part of apt?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/95/_/diff#comment-287996952"}}
{"comment": {"body": "tenant\\_id uuid created in the local helm package randomally?", "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/95/_/diff#comment-287997586"}}
{"title": "change defaults, added scoring to configuration", "number": 96, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/96", "body": ""}
{"title": "Hotfix/config file", "number": 97, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/97", "body": "fix bb pipelines\nreturn downloadable values file\nfix swagger file\n\n"}
{"title": "upgrade awscli", "number": 98, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/98", "body": ""}
{"title": "fix devices bugs", "number": 99, "htmlUrl": "https://bitbucket.org/levl/eros-configuration-api/pull-requests/99", "body": ""}
{"title": "fix env filesg", "number": 1, "htmlUrl": "https://bitbucket.org/levl/l2-typing-model-generation/pull-requests/1", "body": ""}
{"title": "Adeed CI/CD model", "number": 2, "htmlUrl": "https://bitbucket.org/levl/l2-typing-model-generation/pull-requests/2", "body": ""}
{"title": "add models version home dir & set aws credentials from .sh file", "number": 1, "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/1", "body": ""}
{"title": "send sns message + bitbucket pipl", "number": 2, "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/2", "body": "in Bitbucket pipelines > step=sendSNSMessage > last command in script running send_sns_message.py > Need to add method parameters"}
{"title": "MEROSP-2126 bitbucket pipelines - create models zip + send SNS message", "number": 3, "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/3", "body": "These updates were done after changing models hierarchy\nsrc > v1/v2/v3 > models > ."}
{"title": "update to latest classifier's models", "number": 4, "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/4", "body": ""}
{"title": "Dev to Master (primary merge)", "number": 5, "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/5", "body": "add models version home dir & set aws credentials from .sh file\nadd tags pipeline\nfix s3 base path\nsend sns message + bitbucket pipl\nsend params to send_sns_message\nadd get_docker_tag.sh\ncheck pipline\nset send sns message step to run in piplines\nfix models.tar.gz s3 path\nfetch tag artifact\nadd tag fetch to send sns message step\nupdate versions hierarchy & deployment envs\nfix bitbucket pipelines\ncheck s3 file path exists, if not create models zip\nfirst step cannot be manual\nupdate bitbucket pipelines definitions\nbitbucket pipelines updates\nbitbucket pipelines branches > dev fix\nremove branches > dev frombitbucket pipelines\nadd env step to bitbucket pipelines\nfix env step name\nupdate env step artifact\nfix path variable name\nadd git cli\nadding env vars + removing the specific use of python-slim image\nfix env vars\nadd environments pipelines\nadding case when using an already created models.tar.gz file\nupdate Lambda to extract version from models file name\nfix lambda to extract file name from s3 file path without extensions\nadd file version and creation timestamp to metadata\nfix file creation timestamp command\nremove space in metadata definition\nfix sending sns error\nadd metadata to presigned url\nfix getting s3 file metadata\nadd missing params for generating presigned usrl\nfix generating presigned usrl\nfix generating presigned url - get object\nsend_sns_message updates\nsend_sns_message with metadata\nupdate to latest classifier's models\n\n"}
{"title": "update tagging strategy", "number": 6, "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/6", "body": ""}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} you should merge it to Dev first, then align Dev with Master", "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/6/_/diff#comment-358474856"}}
{"comment": {"body": "Updated target branch to dev", "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/6/_/diff#comment-358537948"}}
{"title": "update tagging strategy", "number": 7, "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/7", "body": ""}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a}   \nyou can omit the if-else and do something like that:  \n\n```\nif [ -z \"${ENV_IMAGE_TAG}\" ]; then\n  TAG=$(git describe --always --abbrev=7)\n  if [ ! -z \"${BITBUCKET_BRANCH}\" ]; then\n    TAG=${BITBUCKET_BRANCH}-$(git describe --always --abbrev=7)\n  fi\nfi\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/7/_/diff#comment-358593236"}}
{"comment": {"body": "@{6335b848a84c7f79c387915c} raised PR here - [https://bitbucket.org/levl/eros-models/pull-requests/8](https://bitbucket.org/levl/eros-models/pull-requests/8){: data-inline-card='' } once merged, the changes will be reflected in this pr", "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/7/_/diff#comment-358666853"}}
