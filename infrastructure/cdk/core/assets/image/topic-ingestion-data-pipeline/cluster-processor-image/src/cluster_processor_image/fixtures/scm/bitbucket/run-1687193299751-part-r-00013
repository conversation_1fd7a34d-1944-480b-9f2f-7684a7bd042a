{"comment": {"body": "Yeah. We can fix it later", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/169/_/diff#comment-152602906"}}
{"comment": {"body": "Nice catch. Wonder how it does even work..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/169/_/diff#comment-152603008"}}
{"title": "Gilad/faster recsys 2 the ping strikes back", "number": 17, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/17", "body": "After the last PR I noticed that even when increasing the ping timeout, the router sometimes dont respond in time (or at all).\nThis led to the recording system killing the recording even when everything is ok.\nI reached the conclusion that ultimately the ping doesnt matter:\n\nMonitoring threads can run all of the time and handle router downtime.\nRecording threads will die on their own when the router goes down (since it will stop maintaining the SSH tunnel)\n\nThis PR also makes sure that the API bundle transferred to the browser is cached on the server, to reduce the amount of locks when multiple people are viewing the console.\n"}
{"title": "Moved auth to docker-file", "number": 170, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/170", "body": ""}
{"title": "Add colors for prints of features return statuses", "number": 171, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/171", "body": "\n\n"}
{"title": "Operation_replay works", "number": 172, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/172", "body": ""}
{"comment": {"body": "Wouldn\u2019t it be a little bit more coherent to use pathlib at this stage?\n\n```\n(Path(__file__).absolute().parent.parent.parent.parent.parent / 'bosch_integration').absolute()\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/172/_/diff#comment-152641108"}}
{"comment": {"body": "No. It\u2019s hideous. :disappointed: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/172/_/diff#comment-152647048"}}
{"title": "Fixed crash when auth-server would refuse user", "number": 173, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/173", "body": "Previously the server would try to extract the ID token even if it was missing."}
{"title": "Small fix in connection handling", "number": 174, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/174", "body": "Condition for adding users to DB was always met."}
{"title": "Move re-connected clients to the end of the \"queue\"", "number": 175, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/175", "body": ""}
{"title": "Connectivity is limited on filtered RSSI value", "number": 176, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/176", "body": "Introduce a filtered RSSI value, just for a slightly more stable values"}
{"title": "Faster adv refresh rate", "number": 177, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/177", "body": "I saw that in each time a phone starts to transmit, there is a nonzero probability (lets call it x) that a phone wont actually transmit (without returning any failure code).\nBecause of our current timeout if this event happens two times in a row - it will fail a training operation. To minimize this probability I decreased the adv refresh rate to something much lower.\n\nIve check with a couple phones that this does not decrease the throughput rate of packets."}
{"title": "Gilad/connection timeout", "number": 178, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/178", "body": "Added timeout in case a connection is not established\nUpdated app version\n\n"}
{"comment": {"body": "what\u2019s the reason for `BluetoothGatt.CONNECTION_PRIORITY_LOW_POWER`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/178/_/diff#comment-152913236"}}
{"comment": {"body": "Also, the app now proceeds to discover services once it completes the MTU exchange. What about making sure that connection priority is also exchanged? Or even proceed to service discovery without waiting for the exchanges", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/178/_/diff#comment-152914048"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66}\n\nNote that previously we also requested for low connection priority, we just did it at a point where there wasn\u2019t any connection yet \\(right after calling connectGatt\\).\n\nI want to say that I saw stability/speed improvement after fixing that, but it\u2019s really hard to tell.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/178/_/diff#comment-152932351"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} Missed that.\n\nGJ.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/178/_/diff#comment-152932813"}}
{"title": "Fast connection to the lock", "number": 179, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/179", "body": ":airplane:"}
{"title": "Feature/wifi slopes", "number": 18, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/18", "body": "Feature is testing-ready. Well push it to ccpilot infra for QA and live testing.\nMain file (trainer and classifier): workspace/wifi_slopes/ccpilot_wifi_slopes.py\nDepends on:\n\nsdk_fft.py (feature extraction)\ncalculators.py (helper file)\nwifiutils.py (CFRPacket preprocessing)\n\n"}
{"title": "Improve classification speed", "number": 180, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/180", "body": "Set number of beacons to be 4 to increase throughput of packets\n\nSome minor fixes, each one of them remove some 100s millis from the runtime. Together they make a big difference:\n\ndecrease sleep in Network monitoring and status reporting loops on PI agent\nremove db read from classification hot path\nno need to log BLE disconnect/connect events to db\nput events in queue before db access\n\n"}
{"comment": {"body": "How many packets/seconds do we reach now?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/180/_/diff#comment-153171708"}}
{"comment": {"body": "Why aren\u2019t we interested in saving this event for ble? Or is it just worth the sacrifice to save some time?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/180/_/diff#comment-153173118"}}
{"comment": {"body": "Around 10-11 on phones that supports 4 beacons.\n\nIf we would decimation on the dialog \\(from 16mhz to 8mhz\\) I think we can reach even a higher rate. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/180/_/diff#comment-153173476"}}
{"comment": {"body": "It\u2019s just not a very interesting event to show to the user and it\u2019s usually paired with Authentication passed / failed.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/180/_/diff#comment-153174936"}}
{"title": "Switched from manual DB connections to using context managers", "number": 181, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/181", "body": "This replaces the manual closing of DB connections to using context managers.\nPreviously there were a few places where we wouldnt close the connection on exception."}
{"comment": {"body": "When I think about it, we don\u2019t have any handling of db errors in our code.   \nIf for some reason, the db write fails, it leaves the system in inconsistent state. I wonder how we never saw any bugs around that.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/181/_/diff#comment-153208335"}}
{"comment": {"body": "I guess it's because we\u2019re writing to a robust DB engine on the same machine, and our load isn\u2019t that big.\n\nAlso AFICT we don\u2019t do anything particularly dangerous inside the lock regions.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/181/_/diff#comment-153224458"}}
{"title": "Various bugfixes - CCP-149/160/162/163", "number": 182, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/182", "body": "Set of bugs caused by races of changes the user status between the training / main data_processor threads\n\nThe symptoms where Authentication failed error when stopping training or Authentication failed after retrying training.\n"}
{"comment": {"body": "Should this ever actually happen outside the condition of `if not mac in self.users_dict`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/182/_/diff#comment-153343350"}}
{"title": "Production mode for server/comcast.py", "number": 183, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/183", "body": "Production/testing environment, enabled CORS\nAPI for UI\nProduction mode for comcast.py\n\n"}
{"comment": {"body": "These are just placeholder values until the actual values of what to show where are decided tomorrow.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/183/_/diff#comment-153312238"}}
{"title": "Solve connection problems with board", "number": 184, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/184", "body": "Retry connect 3 times before giving up.\nUsing mGatt.close() instead of disconnect() and not together to prevent leaking BLE resources\n\n"}
{"title": "Production now uses HTTP and port 1234 because VPN is assumed", "number": 185, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/185", "body": ""}
{"title": "Enhance API with human readable device manufacturer and name, as well as AD user name", "number": 186, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/186", "body": ""}
{"comment": {"body": "Do we always have a non-None idtoken?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/186/_/diff#comment-153337536"}}
{"title": "Feature/transform lock to badging", "number": 187, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/187", "body": "Toggling lock opened/closed locally has changed to changing a persistent user attribute online - present/non-present.\nUsed the existing connection stream to pass badge operations, which is not desirable. Choose to do so for the sake of minimum changes, but this should be changed soon.\n"}
{"title": "Fixed issue where the app will forcibly reconnect to the lock", "number": 188, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/188", "body": "Also sort device list so that connectable devices appear on top."}
{"title": "Don't let the app control the connection params", "number": 189, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/189", "body": "Its LOW POWER setting is connection interval between 100ms and 125 ms"}
{"title": "Fix recsys hogging", "number": 19, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/19", "body": "recsys data throughput wasnt stable and probably missed packets.\nwith these packets, data throughput is constant 300KBps\n"}
{"comment": {"body": "Does it really make sense to enable compression for all queries?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/19/_/diff#comment-143772809"}}
{"comment": {"body": "Don\u2019t see any reason not to. I didn\u2019t see it stressing any side of the tunnel", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/19/_/diff#comment-143773167"}}
{"title": "Android UI adjustments per Shira's recommendations", "number": 190, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/190", "body": ""}
{"title": "Support mate10 mate20 light phones", "number": 191, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/191", "body": "Transmit a single beaocn on devices that don't support mutli advertisments\nupdate app version\n\n"}
{"title": "Add Samsung Galaxy S20 configuration", "number": 192, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/192", "body": ""}
{"title": "disconnect in case of failure to set MTU", "number": 193, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/193", "body": ",Otherwise might get stuck in lock2phone protocol forever"}
{"comment": {"body": "How beneficial is this debug log?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/193/_/diff#comment-153514044"}}
{"comment": {"body": "This is the cmd that fails if there are issues with the MTU, so if it doesn\u2019t appear it means we can tell the MTU is not big enough.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/193/_/diff#comment-153515550"}}
{"title": "Fixed filter expression when using 2.4GHz", "number": 194, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/194", "body": "Also disable non-static behaviour of the static scheduler"}
{"title": "Gilad/auth expiry", "number": 195, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195", "body": "Device ID is now 128bit.\nInitial registration can expire.\nFailure reason is returned to lock (not yet used - next PR).\nUPN is not used an ID as-is. It is now hashed by the auth server and passed to the phone as an opaque ID.\nAuth server can be run as prod/dev on the same machine cert renewal crypto buzzwords.\nTried to fix some incorrect usage (in jargon) of user/device ids, seeing as now we have both.\n\n"}
{"comment": {"body": "what does this `<< : *auth-server-mixin` syntax mean?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-*********"}}
{"comment": {"body": "call `docker-compose down` before that?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-*********"}}
{"comment": {"body": "[https://docs.docker.com/compose/compose-file/#extension-fields](https://docs.docker.com/compose/compose-file/#extension-fields)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-*********"}}
{"comment": {"body": "Too much credit is given to this email :stuck_out_tongue: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-*********"}}
{"comment": {"body": "It doesn\u2019t really matter, it can be left empty and it would still work, it just makes letsencrypt happier when there\u2019s an email", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-*********"}}
{"comment": {"body": "We need to make sure that this requirement is reflected to comcast. Is @{5a4500fe0cacf235de82a9d4} aware of this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-153636758"}}
{"comment": {"body": "splat; it's like `**{}` in python", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-153641990"}}
{"comment": {"body": "depends if I can `down` only one service, I'll check that.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-153642147"}}
{"comment": {"body": "He asked for it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-153642399"}}
{"comment": {"body": "it looks like there\u2019s no need\n\n![](https://bitbucket.org/repo/8X5z9dk/images/3933847948-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-153647495"}}
{"comment": {"body": "I don\u2019t think that there\u2019s <NAME_EMAIL>", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/195/_/diff#comment-153648642"}}
{"title": "Fixed inability to create base64-named tables", "number": 196, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/196", "body": ""}
{"comment": {"body": "Marvelous", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/196/_/diff#comment-153724556"}}
{"title": "Gilad/debase 64", "number": 197, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/197", "body": "Removed usage of base64 for user identifiers\nFurther encoding fixes\nFurther Further encoding fixes\n\n"}
{"title": "Set connection interval to be balanced", "number": 198, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/198", "body": "This seems to be a good balance between:\n\nNumber of packets captured per seconds (~10 for various phones)\nEnough readings for connection RSSI feature to return a result\n\n"}
{"title": "Added script to run docker locally (not on pi)", "number": 199, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/199", "body": ""}
{"title": "Executable", "number": 2, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/2", "body": ""}
{"title": "Misc. recsys changes", "number": 20, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/20", "body": "Errors\nThe system now detects when the router is in Not-Associated mode, which happens right after changing HTMODE/SSID/whatever.\nThe system handled this state before but couldnt display any useful errors.\nRadiotap parsing\nRadioTap parsing for DB insertion is again stripped down to almost nothing with the addition of radiotap_parse_quick\nSSId changes\nMade sure that the HT-Mode is re-set after changing the SSID from the web interface.\n(previously it was set to 20MHz non-HT after calling wifi)\n"}
{"title": "Fixed text display of badge-ins", "number": 200, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/200", "body": ""}
{"title": "Fixed login screen when using dark mode", "number": 201, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/201", "body": "Also, fix issue where the progress bar is not shown correctly after initial login.\n(also added debug version indication)"}
{"title": "New frontend API", "number": 202, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/202", "body": "Overall stable UI/API.\n\nFixed DB initialization and some other stuff that were not updated\nAPI edge case horrors\nForget device now works\n\nStill a bit of work left:\n\nDevice BT name\nDevice BT address\nAlerts\nAlerts dismissal button\n\nBut this PR is messy enough as it is so theyll be added separately.\n\n"}
{"comment": {"body": "`exc_info=True`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/202/_/diff#comment-153844487"}}
{"comment": {"body": "Art.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/202/_/diff#comment-153847698"}}
{"comment": {"body": "The only possible issue you could have is us not using `y['id_token']['name']` which actually exists. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/202/_/diff#comment-153854603"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} I agree. This is a piece of art ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/202/_/diff#comment-153854806"}}
{"comment": {"body": "Unreadable code is tight", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/202/_/diff#comment-153918919"}}
{"title": "Display auth fail reason to user", "number": 203, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/203", "body": "Also solves:\n\nRunning setup.sh copying server logs and filling up the router\nBADGENONE issue\nIDTOKENNONE issue\nConfusing Authenticating text in the app\n\n"}
{"comment": {"body": "See also [https://bitbucket.org/levl/bosch\\_integration/pull-requests/862/pass-nauth-reason-to-user](https://bitbucket.org/levl/bosch_integration/pull-requests/862/pass-nauth-reason-to-user)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/203/_/diff#comment-153851211"}}
{"comment": {"body": "Suggestion that you don\u2019t have to adopt: write the failure event to history in db with the reason so that alarms will be more insightful.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/203/_/diff#comment-153906680"}}
{"comment": {"body": "Don\u2019t forget app version", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/203/_/diff#comment-153908122"}}
{"title": "Added scripts to save logs", "number": 204, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/204", "body": "For qa"}
{"comment": {"body": "when are the scripts supposed to run?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/204/_/diff#comment-153917852"}}
{"comment": {"body": "After QA finds a bug.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/204/_/diff#comment-153917995"}}
{"comment": {"body": "Aren\u2019t we already saving all the logs from the docker to a file?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/204/_/diff#comment-153919829"}}
{"title": "Feature/better operation replay", "number": 205, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/205", "body": "Operation replay can have multiple files\nLog BLE model update also for classification\n\n"}
{"comment": {"body": "What does this actually do?  \nDo you use the model else where?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/205/_/diff#comment-153946479"}}
{"comment": {"body": "Now multiple operations can be used. For example, training, classification, classification, classification. So an online training can create a new model and affect the next classification.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/205/_/diff#comment-153947374"}}
{"title": "Dashboard api side", "number": 206, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/206", "body": "Added support for displaying MAC address on BLE. On Wifi, mac address is saved in addition to the key.\nCoupled with .\n"}
{"title": "Updated app version", "number": 207, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/207", "body": ""}
{"comment": {"body": "Inspiring", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/207/_/diff#comment-153958912"}}
{"comment": {"body": "Makes you wake up in the morning with a smile", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/207/_/diff#comment-153959633"}}
{"title": "Gilad/deviceuuid", "number": 208, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/208", "body": "Moved app to use UUID instead of randint\nShw did in ui\n\n"}
{"title": "Fixed voodoo", "number": 209, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/209", "body": ""}
{"title": "Fixed some layout issues", "number": 21, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/21", "body": ""}
{"title": "Disconnect from lock when activity is paused", "number": 210, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/210", "body": "Solves CCP-189, CCP-190"}
{"title": "Auto serial port detection for comcastpi.py", "number": 211, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/211", "body": "Port scanner\nBetter USB mounting to Docker\n\n"}
{"comment": {"body": "Can you merge that? So we can make sure that we test this as wel..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/211/_/diff#comment-154303976"}}
{"title": "Updated hydras to v3.1.0", "number": 212, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/212", "body": "(router uses new hydras feature)"}
{"title": "Fix CCP-194", "number": 213, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/213", "body": "Fill in missing parameter"}
{"title": "Additional failure reasons", "number": 214, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/214", "body": "Internal error - on exception thrown\nOperation timed out\n\n"}
{"title": "Mainly support BT name transfer", "number": 215, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/215", "body": "Update dashboard to present bluetooth name configured in the Android device. All components beside libfingerprinting should be updated.\nHistory events will now save the device address and name at the time of the event.\nCorresponds with ."}
{"comment": {"body": "Please retarget this PR to `ble-production/v2`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/215/_/diff#comment-154337201"}}
{"comment": {"body": "Per Mich\u2019s request, used device\\_info for the most part.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/215/_/diff#comment-154350612"}}
{"title": "6018cfr", "number": 216, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/216", "body": "(Mostly @{5b72a213e72afd064c8a4ebd} 's hard work)\nAdds to support for IPQ6018.\nScripts that access the router now prompt the user to choose one of the routers."}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} lol wut", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/216/_/diff#comment-154338393"}}
{"comment": {"body": "is this relevant?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/216/_/diff#comment-154353247"}}
{"comment": {"body": "spaces in name?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/216/_/diff#comment-154353818"}}
{"comment": {"body": "Yes, the server crashes without it \\(because of wifi, not ble\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/216/_/diff#comment-154356749"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} OK", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/216/_/diff#comment-154356877"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/216/_/diff#comment-154375494"}}
{"title": "Fixed false reconnection events", "number": 217, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/217", "body": "Previously all devices would repeatedly reconnect"}
{"comment": {"body": "I wanted to avoid filling up the queues with badging requests on wifi and ended up making a logical mistake. Maybe still restrict to ble?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/217/_/diff#comment-154516306"}}
{"comment": {"body": "Nevermind, I think the wifi agent always sends None.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/217/_/diff#comment-154516951"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} i don\u2019t think I follow, buy I'm going to merge anyway", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/217/_/diff#comment-154519271"}}
{"title": "Added the frontend to be part of the monorepo", "number": 218, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/218", "body": "Will later add it to be part of our ccpilot/run_server.sh script to further simplify launching"}
{"title": "Pilot launch frontend automatically", "number": 219, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/219", "body": "Now when running the pilots ./run_server.sh script, with either -p (for production) or -s (for staging), it will also run an nginx and certbot container to serve the frontend and continuously refresh its HTTPS certificate.\nNote that the frontend still needs to be manually generated beforehand using the ccpilot/frontend/generate_static.sh script. This is because the Angular JS devs never heard of the concept If no file changed please dont take your sweet time realizing it and rebuilding some of my stuff, and thus it takes a lot of time to build and would be annoying when automated (it would mean waiting like 30 seconds every time we run ./run_server.sh).\nAnother thing that needs to be done manually once per server is the ccpilot/docker/letsencrypt/init-letsencrypt.sh which generates the initial certificate needed to get the server going. Note that before running this script, you need to bash source one of the ./staging.sh or ./production.sh environment definitions, depending on whether you want to generate certificates for the staging or production server.\nRunning the frontend server locally for non-production or staging purposes (testing, development) can be done manually without the need for nginx and with a convenient auto-refresh:\n```\nInstall NPM\nnavigate to ccpilot/frontend:\nInstall dependencies\nnpm i \nGlobally install the \"ng\" angular cli\nsudo npm i -g @angular/cli \nServes the current angular project on http://localhost:4200\nng serve\nAfter the first time, only ng serve is needed to get the local server started\n```\n"}
{"comment": {"body": "Maybe create a docker container for this file?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/219/_/diff#comment-154790863"}}
{"comment": {"body": "Definitely a good idea, will do it sometime", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/219/_/diff#comment-154798665"}}
{"title": "Added files to handle radiotap recordings from the db", "number": 22, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/22", "body": "Just some helper functions Ive used."}
{"comment": {"body": "~~Why is pickle of packets ever relevant?~~\n\nNever mind me, I forgot we\u2019re adding metadata to the packets.\n\nIt would be nice if this can be done via recording ID and not date.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/22/_/diff#comment-144121718"}}
{"comment": {"body": "merge this", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/22/_/diff#comment-144751418"}}
{"title": "Import libfingerprinting to monorepo", "number": 220, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220", "body": "Import lib files and fix .gitignore\nAdd docker lib build scripts and other lib related scripts\nMake ccpilot depend on this repos libfingerprinting rather than assume bosch is cloned nearby\n\n"}
{"comment": {"body": "We\u2019re also missing the dialog project\\(s\\). I don\u2019t think we should split them at this stage", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154791082"}}
{"comment": {"body": "Also, how well did you test this? Any open issues/test requests?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154791199"}}
{"comment": {"body": "The single dialog project relating to comcast does not use libfingerprinting.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154792903"}}
{"comment": {"body": "For now I only made sure the lib imports successfully inside the Docker container, I haven\u2019t made any changes to the library code so fingerprinting-wise I don\u2019t think there\u2019s anything to test", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154793191"}}
{"comment": {"body": "it does use dialog boilerplate which depends on libfingerprinting", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154793239"}}
{"comment": {"body": "As @{5dc7c317ea86a50c6c4c7244} said, the Dialog project used in the Comcast pilot does not make use of the fingerprinting library so I don\u2019t see any issues in separating them. Of-course we\u2019ll import the dialog projects to the mono repo as well but that\u2019s a separate issue.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154793275"}}
{"comment": {"body": "The whole point is consolidation of the project, isn\u2019t it?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154793289"}}
{"comment": {"body": "libdialogboilerplate does not depend on libfingerprinting", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154793439"}}
{"comment": {"body": "I beg to differ:\n\n![](https://bitbucket.org/repo/8X5z9dk/images/3680126830-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154794029"}}
{"comment": {"body": "Interesting. I guess we\u2019ll have to move the dialog project to comcast ASAP. I\u2019ll do it in a separate pull request and once they\u2019re both ready we\u2019ll merge both of them", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154794566"}}
{"comment": {"body": "Cool. I appreciate that", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/220/_/diff#comment-154796179"}}
{"title": "Bugfix/bt name security fix", "number": 221, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/221", "body": "Allowing UTF-8 for name and sending fixed-size message\nAdding a little info when serial can't attach\n\nCoupled with: "}
{"title": "Fix classification in wifi", "number": 222, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/222", "body": ""}
{"title": "Some history events fixes", "number": 223, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/223", "body": "Return history events from db in reverse order.\nLimit number of event records to be retrieved.\nRemove expiry events from alerts (only actual failures that are supposed to be malicious are reported).\nChange history events key to be UUID.\nAdded hidden property for DB history event, alert dismiss button activates it.\nReturning connection duration (was constant).\n\n"}
{"comment": {"body": "A general question: what would happen if there\u2019s a collision in the UUID?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/223/_/diff#comment-154796574"}}
{"comment": {"body": "It should return a duplicate key error so we can potentially try again.\n\nBut does adding one or two more bits matter? When the 128 bits are based on MAC and time?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/223/_/diff#comment-154797110"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} IDK. WTYT?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/223/_/diff#comment-154797185"}}
{"title": "Remove nginx spam", "number": 224, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/224", "body": ""}
{"title": "Manually select port for comcastapi.py", "number": 225, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/225", "body": "Automatic detection does not work on MacOS\n"}
{"comment": {"body": "Changed to auto-detect to work also with macos", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/225/_/diff#comment-154812037"}}
{"title": "Dialog and libfingerprinting to monorepo", "number": 226, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/226", "body": ""}
{"comment": {"body": "\u200c\n\nCan you remove the build artifacts like this:\n\n![](https://bitbucket.org/repo/8X5z9dk/images/4011316438-image.png)\n?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/226/_/diff#comment-154819344"}}
{"comment": {"body": "Yeah sorry missed those", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/226/_/diff#comment-154822058"}}
{"title": "Gilad/dbopt", "number": 227, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/227", "body": "This PR:\n\nRemoves almost all string formatting in db_accessor in favour of standard parameters\nRemoved DB locks and stores the connection thread-local storage\n\n"}
{"comment": {"body": "We can also remove the thread-local storage if we use the DBContext object to execute the queries, but it\u2019s probably not needed now.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/227/_/diff#comment-154820029"}}
{"comment": {"body": "What\u2019s the purpose of removing the DB locks?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/227/_/diff#comment-154820650"}}
{"comment": {"body": "It causes more harm than good.\n\nThere\u2019s no reason to lock because each transaction is atomic anyway, and we had to add `sleep`s in the past because one thread was using the DB too much.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/227/_/diff#comment-154820920"}}
{"title": "Dialog9x fixes", "number": 228, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/228", "body": "Fix generate_hydra_structs.sh\nAdd missing changes from bosch_integration\n\n"}
{"comment": {"body": "Are all the tests you have moved into the repository actually running?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/228/_/diff#comment-154825713"}}
{"comment": {"body": "Nope, just added them to make ./generate_hydra_structs.sh work", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/228/_/diff#comment-154825838"}}
{"comment": {"body": "oh ok", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/228/_/diff#comment-154825915"}}
{"title": "Build before run", "number": 229, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/229", "body": "\n\nAutomatically build the fingerprinting lib before running the server with bluetooth\n\n"}
{"title": "Readable server logs for training and classification", "number": 23, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/23", "body": ""}
{"comment": {"body": "What\u2019s the reason for this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/23/_/diff#comment-144750483"}}
{"comment": {"body": "For display in the debug logs", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/23/_/diff#comment-144751573"}}
{"title": "Update pilot LED colouring", "number": 230, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/230", "body": "Blue = Open for connections\nPurple = In connection\nGreen = Authorization successful\nRed = Authorization rejected"}
{"title": "Fix RadioTap/agent", "number": 231, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/231", "body": "Radiotaps on the server would previously enter a hot infinite loop on error.\nFixed that and removed scapy dep.\nAgent would fail to recover on certain situations because some function never returned a value."}
{"title": "Nitzan/clock skews", "number": 232, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/232", "body": "first visualization of clock skews\nadded split and overlap\nadded outlier\nfix outliers\nplot update\nadded tools\nfix bug and rearrange code\nadded duration loop\ndata from db script and faster hampel\nrefactoring and first model\nadded MLA and max sampling\nrefactoring and fixes\nupdate\nadded TCP timestamp and refactoring\nfrom csv to pickle\nadded clock_freq\nclock_freq_analysis\nadded data from yml and Grisha's cm\nfixed bugs in yml script\nminor changes and refactoring\nadded clock_freq_feature\nadjusted imports and requirements\nseparate classes for ICMP and TCP\nadded logs for debugging\ndecrease train time\nfixed unwrap bug\nadded logs for debugging TCP slope estimation\nTCP estimation fix try\nfix status bug\nauth status ble only\nAnother auth_res fix for wifi\nfix None model bug in classification\nbetter debug log\nchange minimum\nfix None clock_freq estimation\nfix None classification\nlogging color\nadded outliers rejection and logs\nupdate requirements\nfixed debug\nfixed None model\nchanged minimums\nminor logs changes\nChanged logging level to INFO to avoid the numba library JIT prints\nadded timeout logic\nfixed always return bytes for LEVL_TRAINING_COMPLETE result\n\n"}
{"comment": {"body": "Model is a bytes object, no need to decode it into a string.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/232/_/diff#comment-154953674"}}
{"comment": {"body": "model should be a `bytes` object \\(and not a `str`ing\\). no need to encode it into `bytes`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/232/_/diff#comment-154953827"}}
{"title": "Made the app logout automatically on session expiry", "number": 233, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/233", "body": "Also added a more visible disconnect button"}
{"title": "Added app icon", "number": 234, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/234", "body": "Changed app name to Levl Physical Access\nDevice list is less jittery\nChanged app icon to this ugly beast:\n\n\n"}
{"comment": {"body": "Is this the place to put reviews also about the logo? :joy: \n\nI think just having the \u201cV\u201d from the LEVL logo without the waveform symbol would look much cleaner", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/234/_/diff#comment-155011099"}}
{"comment": {"body": "lol, sure", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/234/_/diff#comment-155013378"}}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/3390169107-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/234/_/diff#comment-155014065"}}
{"title": "Feature/dhcp fingerprinting", "number": 235, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/235", "body": "Parse DHCP packets\nDHCP training + classification\nFix new CFRPacket\n\n"}
{"comment": {"body": "If we  miss the dhcp packet, does that means the training will be stuck forever?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/235/_/diff#comment-155038922"}}
{"comment": {"body": "We shouldn\u2019t have a reason to miss the packet, unlike the association packet.\n\nI think that if we run into such problem, then we can think of something. Like put it in not participating mode.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/235/_/diff#comment-155041264"}}
{"title": "Bugfix CCP-204: Disconnect device on board disconnect", "number": 236, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/236", "body": ""}
{"title": "Start training only after receiving authentication response", "number": 237, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/237", "body": ""}
{"title": "Bugfix CCP-206: don't hangout forever for lost connectivity between server and pi agent", "number": 238, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/238", "body": ""}
{"title": "Ble production/v2", "number": 239, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/239", "body": "We should probably merge this branch periodically, or it will get harder to do."}
{"title": "BLE  - Add classification timestamp and allow a more elaborate query", "number": 24, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/24", "body": "For BLE UI, only classified devices appear.\nFor BLE Client (board), we also need:\n(1) Classification timestamp (so client would know if the result its getting belongs to the new classification or the previous one);\n(2) Ready for Classification state (so client would know that the training is done).\nSo, added another query to Flask and the required data to db."}
{"title": "Gilad/merge bleprodv2", "number": 240, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/240", "body": "Periodic merge of BLE changes into master.\nWe should probably be more proactive with keeping these branches in sync"}
{"title": "LO Leakage algorithm enhancement to avoid FN", "number": 241, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/241", "body": "The change is: Each feature extraction results in a truncated complex window (it was absed before the change). In training and classification, in order to get the PTAR for the average window over all packets in session, we do the following: On each packet, we push not only the original window to a running average container, but different windows, generated by multiplying the entire window sample-wise with the conjugate of each bin separately. That is, we get a number of windows as the number of bins in the window. When enough packets were received, we go over all of the complex average windows we have collected and find the one with the largest PTAR. If there is a fixed frequency that contains an actual signal (LO leakage), it will be further amplified relative to the noise with this method.\n\nSizes of feature and progress structs were updated accordingly.\nThere was an issue with dwarf2ctypes support of cfloat32_t that was fixed here (thanks Grisha and Gilad)\nLO leakage unit tests that rely on feature structure are broken for now.\n\n"}
{"comment": {"body": "we need to remember to backport this to bosch\\_integration", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/241/_/diff#comment-155242449"}}
{"comment": {"body": "this is still wrong", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/241/_/diff#comment-155242549"}}
{"comment": {"body": "This one is generated by hydra and there\u2019s no need to change it since it doesn\u2019t rely on types for alignment.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/241/_/diff#comment-155242938"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} missed that it is a hydra file", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/241/_/diff#comment-155245016"}}
{"comment": {"body": "Just so it\u2019s clear:\n\nThe `bosch_integration` repo is now relevant ONLY to the Bosch project. Our \u201cofficial\u201d, up-to-date BLE libfingerprinting is and will be in the `comcast` repo.\n\nSo we need to think whether we actually want this change for Bosch\u2019s V5 or not to decide whether it needs to be backported", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/241/_/diff#comment-155248196"}}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} We need to backport this since this is a bug in **dwarf2ctypes.py**. Nothing fancy", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/241/_/diff#comment-155250086"}}
{"title": "Fixed router id and added Nuriel's commands", "number": 242, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/242", "body": ""}
{"title": "Add led current limiting for dialog project", "number": 243, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/243", "body": ""}
{"comment": {"body": "Never create your Eclipse workspaces inside the repo directory, it causes tons of problems. Create them somewhere else entirely", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/243/_/diff#comment-155263485"}}
{"comment": {"body": "BUMP", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/243/_/diff#comment-155462458"}}
{"title": "Added ability to turn off fingerprinting", "number": 244, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/244", "body": "Double click Levl logo to disable the reason for our existence."}
{"title": "G6 is actually 2 lobes", "number": 245, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/245", "body": ""}
{"title": "Added UI label to show router name", "number": 246, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/246", "body": ""}
{"title": "Change presence indication to per user and not per device", "number": 247, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247", "body": ""}
{"comment": {"body": "I think the other way around, mac is true for the wifi case but a general name for this field is better. This is true for other tables as well.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247/_/diff#comment-155521592"}}
{"comment": {"body": "I cannot think of a worst name for this field that user\\_id.\n\nWe will find a better name and will remove all the reference for a user when we don\u2019t actually mean a user.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247/_/diff#comment-155522378"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Yeah, `user_id` is'\u2019t good but neither is `mac`.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247/_/diff#comment-155522941"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} we\u2019re already using device\\_id in some places", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247/_/diff#comment-155526085"}}
{"comment": {"body": "In an optimal world we would probably want to have a devices table and a users table with a one-to-many relation from the users to the devices, such that all user-specific attributes/state, including presence, would reside in the users table.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247/_/diff#comment-155526306"}}
{"comment": {"body": "Don\u2019t have to return the dict, right?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247/_/diff#comment-155527871"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} It\u2019s just here to prevent addressing a device with user\\_id and an actually user with a user\\_id in the same function..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247/_/diff#comment-155529756"}}
{"comment": {"body": "You right, I don\u2019t have. But it doesn\u2019t hurt..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247/_/diff#comment-155529910"}}
{"comment": {"body": "Yeah. We should go with such design for the wifi pilot.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/247/_/diff#comment-155531263"}}
{"title": "Feature/CCP-50 handle any number of 20MHz segments", "number": 248, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/248", "body": "CFRSlope changes:\n\nApply diff between valid subcarriers and mark np.nan for invalid subcarriers\nfilter segments by packet_bw field from CFRPacket\n\n\n\nLarge scale changes:\n\nCatch exceptions in first packet as well\nLog errors to files\nbetter time names\nCount duration of script\nConfusion matrix counts only MATCH and NOT_MATCH events for of specific features. NEED MORE DATA is not counted\n\n\n\n\n"}
{"comment": {"body": "Is this type annotation no longer relevant?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/248/_/diff#comment-155840706"}}
{"comment": {"body": "still relevant, but unexpected complications occurred. like expecting levlcompute to have python 3.8 and not python 3.6.9 :disappointed: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/248/_/diff#comment-155841819"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} \n\n![](https://bitbucket.org/repo/8X5z9dk/images/1579905119-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/248/_/diff#comment-155842509"}}
{"title": "Gilad/tim dialog demo", "number": 249, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/249", "body": "\n(Server) Added a CLI flag that disables authentication\n(Auth Server) Auth server uses sub instead of upn (Because upn is not always available)\n(App) Added app variant that uses the demo auth server and has a constant device ID.\n(App) Fixed buttons in dark mode\nAdded script that can automatically setup reverse-proxy with TLS given that the domain is configured in tls_config/rnd.yml.\n"}
{"comment": {"body": "I don\u2019t want to make any more changes into ble-production/v2, unless it\u2019s bug fixes or issues related to the pilot.\n\nCan you merge it into master?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/249/_/diff#comment-155561497"}}
{"comment": {"body": "Sure", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/249/_/diff#comment-155563306"}}
{"title": "js to reference proper ids", "number": 25, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/25", "body": "(Fixes bug - history not shown when user is pressed in UI)"}
{"title": "Salt ssidSalt SSID to generate a unique SSID each setup of the AP", "number": 250, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/250", "body": ""}
{"comment": {"body": "Should be done at the `setup.sh` layer, not at the `gen_envs.py` layer, because `gen_envs.py` generates artifacts that **are** tracked by Git", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/250/_/diff#comment-155577229"}}
{"comment": {"body": "Note that that the recording system also makes use of these `.env` files, so it too needs to add some extra salt", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/250/_/diff#comment-155577474"}}
{"comment": {"body": "Got it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/250/_/diff#comment-*********"}}
{"title": "Disable LoL and minor app UI fixes", "number": 251, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/251", "body": ""}
{"title": "Gilad/more tls", "number": 252, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/252", "body": "Added more servers to tls-config. Moved old UI to 1234\nRemoved :80 from classifier\n\n\nOld UX (not UIVidor) is always on port :1234, never :80.\nThe various -p/-s flags were too confusing.\n"}
{"title": "Feature/fingerbank classification", "number": 253, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/253", "body": "Fingerbank wrapper\nSmall refactor to display info \nCall fingerbank during training \nUpdate fingerbank result to DB\n\n"}
{"title": "BLE Authorization failure UI", "number": 254, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/254", "body": ""}
{"comment": {"body": "This image looks too good in contrast with the other bitmaps in the UI", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/254/_/diff#comment-*********"}}
{"title": "Support ax by default on Cyps", "number": 255, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/255", "body": "Updated setup scripts to set ax mode on cypresses."}
{"title": "Gilad/tim dialog demo", "number": 256, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/256", "body": "This solves some UI issues:\n\nWhen an imposter is stealing the device ID of the attacked device, the UI will show the device name of the imposter even though the classification failed. (it is completely overwritten).\n  We did not catch onto it because we used fix-model which doesnt change the device ID (and rightfully so)\n  We should clean up the handling of device identifiers.\n\nIn the alerts pane:\n\nThe table was too large for Tims small screen, so I removed the IP column.\nThe event time was always set to the server time due to a copypasta error\nEvent description was too informative.\n\n\n\n\nThis also adds the classification failure reason to the event history.\n"}
{"title": "Gilad/native pinger", "number": 257, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/257", "body": "Added ping_server replacement in C\nAdded pinger to setup\nChanged recording system to use new pinger\n\n"}
{"comment": {"body": "So is the change just for the recording system at this point?  \nThe production system keeps using the python code?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/257/_/diff#comment-156298261"}}
{"comment": {"body": "Whoops, forgot to update", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/257/_/diff#comment-156298309"}}
{"comment": {"body": "@{557058:f7a3b23e-03ba-40b3-9ce6-b139e8b20556} The new pinger is now also used in the pilot", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/257/_/diff#comment-156299814"}}
{"comment": {"body": "Is this still being used?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/257/_/diff#comment-156301269"}}
{"comment": {"body": "Not as far as I know, but it\u2019s pretty useful for prototyping.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/257/_/diff#comment-156301468"}}
{"title": "Salt AP's SSID with a random string upon each setup of the AP", "number": 258, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/258", "body": ""}
{"title": "Fixed vdim packet starvation", "number": 259, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/259", "body": "The calls to cfg80211tool broke something in the sounding reports.\nIn practice were getting reports with these settings off on the cypress."}
{"title": "recsys: Ping server now sends timestamp- instead of echo-", "number": 26, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26", "body": ""}
{"comment": {"body": "how did you test this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144761840"}}
{"comment": {"body": "We ran it on the router.  \nIt works as in:\n\n* We can see the replies in the radiotap-db\n* It brings CFRs to ~92CFR/s\n\nBut after opening this PR Noam noticed an issue that affects another feature, so I\u2019m still working on it.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144761894"}}
{"comment": {"body": "Does it work now? How did you test it?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144762592"}}
{"comment": {"body": "Opened wireshark, ran it locally, and saw that I get a response from the pingee.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144762690"}}
{"comment": {"body": "I\u2019m waiting for the recsys to be clear to test it live.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144762699"}}
{"comment": {"body": "what does the double `-t` do?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144796078"}}
{"comment": {"body": "`-t` ensures that SSH allocates a TTY,  double`-t` doubly ensures it.\n\nIt is used to make sure that when the SSH session dies, so does the process on the target. Otherwise there would be ghost pingers.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144812516"}}
{"comment": {"body": "Does it work now? How did you test it?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144817008"}}
{"comment": {"body": "It works now. We\u2019ve seen that it we do receive the same amount of Noam-Packets as before.\n\nNitzan is now worried that this is not keeping devices out of sleep mode.  \nIt\u2019s not what I\u2019ve seen, but I think we\u2019re probably going to scrap this. :\\(", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144817265"}}
{"comment": {"body": "Maybe interleave between timestamp and echo pings?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144817352"}}
{"comment": {"body": "Yeah, that is one of the propositions.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-144817498"}}
{"comment": {"body": "Tested with @{5d74d49897d8980d8eacd7f8} and all is well", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-145973115"}}
{"comment": {"body": "No idea what this is and why you want me to review it so I\u2019ll just give superficial review\n\n![](https://bitbucket.org/repo/8X5z9dk/images/3140515049-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-145973471"}}
{"comment": {"body": "It requests timestamps", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-145974153"}}
{"comment": {"body": "Yeah I noticed. No idea what\u2019s the purpose of this", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-145974504"}}
{"comment": {"body": "Did it affect CFR rate? Do all devices respond to the timestamp request?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-145974946"}}
{"comment": {"body": "A regular ping is called an ICMP \u201cEcho request\u201d, which requests the target to respond with no data, basically.\n\nICMP specifies a few other types. One of them is \u201ctimestamp request\u201d, which is basically the same, but asks the target to respond with ms since midnight UTC.\n\nNitzan needs this for some kind of fingerprinting", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-145975953"}}
{"comment": {"body": "All except iPhones, which we just realized we need to check", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/26/_/diff#comment-145976103"}}
{"title": "Large scale changes", "number": 260, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/260", "body": "Add caching to queries done through data_index\nFix date usage in large scale\n\n"}
{"comment": {"body": "BUMP\n\nI need this merged so that I can test [https://bitbucket.org/levl/comcast/pull-requests/265](https://bitbucket.org/levl/comcast/pull-requests/265)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/260/_/diff#comment-156332016"}}
{"title": "fixed classification timeout", "number": 261, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/261", "body": "fixed the timeout for clock freq feature classification"}
{"comment": {"body": "There\u2019s already `return Classifier.Result.LEVL_CLASSIFY_MORE_DATA_NEEDED` two lines below", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/261/_/diff#comment-156312482"}}
{"comment": {"body": "yes, apparently me and Gilad did the same fix for that line separately and it was duplicated when i merged\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/261/_/diff#comment-156312886"}}
{"title": "Fixed frequent crash in the recsys", "number": 262, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/262", "body": ""}
{"comment": {"body": "what\u2019s the reason for the failure? And shouldn\u2019t you retry before giving up?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/262/_/diff#comment-156313162"}}
{"comment": {"body": "What\u2019s the side effect of this crash?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/262/_/diff#comment-156313175"}}
{"comment": {"body": "This happens quite a lot due to network connectivity problems.  \nThis happens, for example, when the router dies, or something is changed in its preferences.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/262/_/diff#comment-156313275"}}
{"comment": {"body": "In the pilot: mostly nothing.\n\nIn the recsys: this kills a background thread that polls the router and requires the user to restart the recsys.\n\nTake note that the handling of this error is identical to the one in `get_iwconfig`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/262/_/diff#comment-156313378"}}
{"title": "Always rebuild agent", "number": 263, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/263", "body": "Solved linking problems"}
{"title": "No exit", "number": 264, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/264", "body": ""}
{"title": "Nitzan/expand cfr parser", "number": 265, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/265", "body": "fixed missing 53rd carrier for 80MHz\nfixed mixed chains bug + added support for 40MHz\n\n"}
{"comment": {"body": "Please make sure this doesn\u2019t break any existing code, i.e wait for approval from ALL of these people, after they tested their CFR code didn\u2019t break from this:\n\n@{5bdac2ad92e2727e0939d08b}   \n@{5b41d9de10d57114135eca66}   \n@{5b7bbc6cc5e21441d0929b7e}   \n@{5b6154f66366b42ca0def662}\n\nAlso please try running the ccpilot yourself and see if it trains/classifies without crashing", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/265/_/diff#comment-156325922"}}
{"comment": {"body": "Right now I don\u2019t have any active feature that\u2019s running on CFR, only proofs of concept in various states of relevance. And in any case, none are at 40 or 80 MHz. So go for it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/265/_/diff#comment-156326107"}}
{"comment": {"body": "This also changes the behavior of 20MHz.  \n  \nPlease checkout `nitzan/expand_cfr_parser` and run your relevant workspaces to make sure they still function \\(if you want to, of-course\\), then approve this PR", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/265/_/diff#comment-156326166"}}
{"comment": {"body": "Please return a tuple here instead of a generator \\(I know it was a generator before, I hated it\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/265/_/diff#comment-156326577"}}
{"comment": {"body": "You can also ask @{5de37bc12fd6260cf27cb79c} to approve", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/265/_/diff#comment-156326629"}}
{"comment": {"body": "Looks like it\u2019s not gonna work for me anyway. i haven\u2019t used get\\_chains in like a month and a half so I can\u2019t run with it, even from before Nitzan\u2019s change.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/265/_/diff#comment-156330541"}}
{"title": "Fix name bug with code from master", "number": 266, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/266", "body": ""}
{"title": "Slowed down pings", "number": 267, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/267", "body": ""}
{"title": "Fix wifi traces", "number": 268, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/268", "body": "Trace file name without colon\nDump trace file even when not finished training\nFix unpickling: unpickler should be reinstantized after every load\n\n"}
{"title": "New app icons", "number": 269, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/269", "body": ""}
{"title": "Fix timeout bug", "number": 27, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/27", "body": "Fixed bug: disable of timeout was always turned on, training was not killed when messages stopped coming."}
{"title": "Merge all requirements into one file for convenience", "number": 270, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/270", "body": "For future reference, generated with:\ncat **/requirements.txt | awk '{print tolower($0)}' | sort | uniq | sed '/^#/d' | c -f '#' 0 | sort | uniq | c -f '=' 0 | sort | uniq | c -f ' ' 0 | sort | uniq  requirements.txt\n(c is an alias for choose, a rust utility)"}
{"comment": {"body": "Can we add tensorflow? My project with Noam uses it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/270/_/diff#comment-156464171"}}
{"comment": {"body": "Also, sklearn and scikit-learn are the same AFAIK, docs say pip install scikit-learn. Should we remove sklearn?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/270/_/diff#comment-156465642"}}
{"comment": {"body": "I\u2019m not sure you have enough `sort`s in your query", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/270/_/diff#comment-156512309"}}
{"comment": {"body": "I\u2019m pretty sure", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/270/_/diff#comment-156548133"}}
{"title": "Minor UI adjustments: Correctly count the number of online and offline devices", "number": 271, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/271", "body": ""}
{"comment": {"body": "This breaks wifi but I guess we\u2019ll handle it soon.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/271/_/diff#comment-156480579"}}
{"comment": {"body": "`token is not None` is implied from the way you now get \u201cpresence\u201d, no harm in another check.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/271/_/diff#comment-156480812"}}
{"comment": {"body": "Yeah. We will indeed handle it soon.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/271/_/diff#comment-156481406"}}
{"title": "Gilad/continous classification", "number": 272, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272", "body": "This adds background classification and initial motion detection code.\n(Both features can be turned off using feature flags.)\nThis is done by asking the router for CFRs with a lower rate for non-classifying devices.\nMotion detection is still not shown in the UI, but it is now part of the device repository."}
{"comment": {"body": "Nice comments", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156686108"}}
{"comment": {"body": "I\u2019d hate to reap all the rewards. Gal should get some of the ~shame~ fame", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156686322"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} might be relevant to you somehow?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156686449"}}
{"comment": {"body": "you should gracefully terminate the thread instead", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156693522"}}
{"comment": {"body": "This _is_ graceful termination.\n\nThis function is implemented by yours truly and uses an event \\(Threads don\u2019t have a terminate function\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156694032"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} I\u2019m missing the `join`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156703890"}}
{"comment": {"body": "Beautiful", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156704108"}}
{"comment": {"body": "I suggest adding a background classification flag to the tracing of the classification. Maybe even change the infix from `_C_` to `_CB_`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156704830"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Oh, ok", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156711125"}}
{"comment": {"body": "Add maxassoc to `/etc/config/wireless` to enforce limit", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156981667"}}
{"comment": {"body": "This will be really bad for recordings though", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156982809"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156990641"}}
{"comment": {"body": "Join was added inside the terminate function", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156990807"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} Add a recording vs system prompt to `./setup.sh`? would most likely be very useful in the future", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156991522"}}
{"comment": {"body": "Replace True False with enum?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-156991839"}}
{"comment": {"body": "A bit overkill for something that is used locally a few lines later.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-157000392"}}
{"comment": {"body": "Comments should say why, not what", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-157076807"}}
{"comment": {"body": "\\(Explain that this is to give the newly connected device indirect prioritization over reclassifying devices\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-157076992"}}
{"comment": {"body": "Attack on the system: Keep connecting devices to prevent the system from doing reclassifications!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-157077111"}}
{"comment": {"body": "DataProcessor is messy enough as it is. Can we somehow delegate this to an external class?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-157077516"}}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} What can be done about this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-157077580"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} :thumbsdown: \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/272/_/diff#comment-157085606"}}
{"title": "Gilad/fix iteration mutation error", "number": 273, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/273", "body": ""}
{"title": "verbose CapFP", "number": 274, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/274", "body": ""}
{"comment": {"body": "Remember that you also have traces for debugging. Real-time info might be too cluttered with all of the debug info.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/274/_/diff#comment-156706809"}}
{"comment": {"body": " It\u2019s an event that happens once per session so I\u2019m not too worried about being cluttered", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/274/_/diff#comment-156728104"}}
{"title": "added support for A-MSDU frames and extended minimum duration for ICMP", "number": 275, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/275", "body": ""}
{"title": "Added colors", "number": 276, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/276", "body": ""}
{"title": "added HE capabilities", "number": 277, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/277", "body": ""}
{"title": "Update wifi_settings to work with AX", "number": 278, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/278", "body": ""}
{"title": "Added select-dialog to cfg scripts", "number": 279, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/279", "body": ""}
{"title": "Duration field", "number": 28, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/28", "body": "Ignore everything except classifiers.py"}
{"comment": {"body": "whose responsibility to integrate it into ccpilot?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/28/_/diff#comment-144947589"}}
{"comment": {"body": "Mich will decide soon", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/28/_/diff#comment-145820342"}}
{"comment": {"body": "@{557058:f7a3b23e-03ba-40b3-9ce6-b139e8b20556} ping", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/28/_/diff#comment-146176547"}}
{"comment": {"body": "pong", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/28/_/diff#comment-146179830"}}
{"comment": {"body": "The next one from the dev team to get free will get the task to implement this.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/28/_/diff#comment-146180002"}}
{"title": "New matlab code from qualcomm", "number": 280, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/280", "body": ""}
{"comment": {"body": "@{5de37bc12fd6260cf27cb79c} ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/280/_/diff#comment-156964928"}}
{"title": "ignore reserved bits", "number": 281, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/281", "body": ""}
{"title": "Enable more features by default", "number": 282, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/282", "body": ""}
{"title": "Fixed missing association packets", "number": 283, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/283", "body": ""}
{"title": "Feature/custom splash screen", "number": 284, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284", "body": "Add support for nodogsplash, which creates a splash screen for new devices\n\nRouter FW comes with nodogsplash v0.9-beta9.9.9 which has major issues.\nAdd nodogsplash v1.0.2 which was compiled from sources\n\n\n\nAdd custom splash screen services by our api endpoint at /splashpage\n\nall.sh handles the updates required for the new endpoint. Note that the endpoint must be reachable by the connecting device. For example, if the connected device is on 10.0.1.x network and the endpoint is on 10.9.x.x, it wouldnt work. But if the endpoint is e.g. cwd.levl.tech, it would.\n\n\n\nShow in the splash screen that the device is onboarding. In the end, show a button to continue\n\nFix fingerbank usage with user agent\nAdd some tests for parsing\n\nThere are probably bugs and edge cases not covered. Do your best to review this code.\n"}
{"comment": {"body": "Please move this to a flask template", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-*********"}}
{"comment": {"body": "This is a temporary implementation of the services HTML page.. It\u2019ll change", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-*********"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Okay", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-*********"}}
{"comment": {"body": "Code is ready to re-review", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-*********"}}
{"comment": {"body": "TEMPLATE TEMPLATE TEMPLATE", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-*********"}}
{"comment": {"body": "What is the meaning of this score?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-*********"}}
{"comment": {"body": "I\u2019m not sure it\u2019s the right enum to use. The AuthStatus was originally meant to track the device stage in the authentication process.  \nThe follow had been: not authenticated \u2192 Authenticating \u2192 Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-*********"}}
{"comment": {"body": "Fingerbank\u2019s confidence in the classification they returned. For example with S8, with just DHCP fingerprint, they returned Google Android with 87 score, but with DHCP and user agent, they returned S8 with 34 score.\n\nThere\u2019s more info in their documentation: [https://api.fingerbank.org/api\\_doc/2/combinations/interrogate.html](https://api.fingerbank.org/api_doc/2/combinations/interrogate.html)\n\nSince they return just a single classification and it\u2019s just for UI purposes, we can\u2019t really discard their result and replace it with something else. So with threshold of 1, we generally accept every result.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-*********"}}
{"comment": {"body": "It\u2019s not really an abuse of the enum, since that\u2019s what nodogsplash is doing - it\u2019s authenticating. And we wish to synchronize the states between the server and nodogsplash.\n\nI know that we\u2019re actually authorizing instead of authenticating, but that\u2019s NDS\u2019s terminology. I could create an enum for authorization with 2 states: not authorized and authorized.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-*********"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} Just for you, I removed 1 HTML page. We\u2019ll have to live with the remaining one.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-157573138"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} your generosity knows no bounds", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/284/_/diff#comment-157573305"}}
{"title": "Received DHCP packet missing required parameters from iPhone, should log and ignore", "number": 285, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/285", "body": "Grisha spotted the issue."}
{"title": "All feature enabled + timeout mechaism", "number": 286, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/286", "body": "remove fingerback asserts\nadd timeout training/classification mechanism\nenable all features\napply timeout\n\n"}
{"title": "Recover RadioTap stream on server restart", "number": 287, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/287", "body": ""}
{"comment": {"body": "What\u2019s the general scenario you\u2019re trying to fix?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/287/_/diff#comment-157277762"}}
{"comment": {"body": "Server restarts while the agent keeps running. \\(or there\u2019s a network problem\\)\n\nThe \\(streamer\\)agent will try to reconnect with the server without losing data or restarting the stream.  \nThe problem is that the server parses the radiotap stream using scapy/dpkt/whatever, and they expect a file-header at the beginning of a stream. \n\nAfter the server restarts, the agent will try to reconnect and give the server data from the middle of the stream, skipping the file-header.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/287/_/diff#comment-157278640"}}
{"title": "Fixed constant-reclassifications", "number": 288, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/288", "body": ""}
{"title": "Fix \"ghost\" disconnected devices", "number": 289, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/289", "body": ""}
{"title": "Combined index_ble.[js|html] and index_ble.[js|html].", "number": 29, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/29", "body": "`ble_enabled` value is now being transferred to the JS file."}
{"comment": {"body": "typo \\(talbe\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/29/_/diff#comment-145014498"}}
{"comment": {"body": "Woops, Fixed in next PR :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/29/_/diff#comment-145120042"}}
{"title": "Timeout using timestamps instead of reading radio tap headers", "number": 290, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/290", "body": ""}
{"comment": {"body": "what if there are no radio\\_tap/cfrpacket events at all?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/290/_/diff#comment-157360247"}}
{"comment": {"body": "Yeah, this should probably be called at the top of the event loop, where there\u2019s a timeout.\n\n\\(where the periodic state management is called\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/290/_/diff#comment-157360596"}}
{"comment": {"body": "I\u2019ll do that in a separate PR. There is a bug waiting for this.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/290/_/diff#comment-157361029"}}
{"title": "Gilad/tim dialog demo", "number": 291, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/291", "body": "Fixed device-events not updating in real time\nWhen auth is off, the server still talks with the auth server to get the user name.\nMade sure that device events are hidden when the device model is deleted."}
{"title": "Pre-association cache", "number": 292, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/292", "body": "Fixed the behaviour of the pre-association cache. The packets were in an actual race with the ap_connections_agent status updates. If packets arrive before Training/Classification is started, the packets are read through the cache. Otherwise, they are directly stream to the training/classification thread as any other radiotap packet.\nAdd DHPC packets to cached packets\nClassification didnt actually read packets from the pre-asocciation cache - WTF?\n\n"}
{"comment": {"body": "This means that this feature can return \u201cmore data needed\u201d even after a match or a mismatch is decided, no?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/292/_/diff#comment-157780764"}}
{"comment": {"body": "No.  \nThe outer loop is not calling features that return a final state any further \\(MATCHING/NOT MACTHING/TRAINING DONE\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/292/_/diff#comment-157781319"}}
{"title": "Spalsh Page + Dual band 2.4GHz/5Ghz", "number": 293, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293", "body": "Contains Grishas PR\nFixed flows when to redirect user to splash page in training / classification\nSupport dual band 2.4GHz / 5Ghz both on agent and on server\nFeatures to specify support of 2.4GHz / 5GHz\nReduce memory on AP\n\n\n"}
{"comment": {"body": "All this `$2` and `$1` stuff is getting messy, maybe we should move to `getopt` like in `run_server.sh`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157836529"}}
{"comment": {"body": "This is another entire instance of Python \\(a lot of memory\\) just for a very simple functionality. Maybe we should merge the `_all.sh` agents?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157836838"}}
{"comment": {"body": "Maybe we can just drop the `--no-radiotap` flag.\nIt does nothing after this change anyway.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157840847"}}
{"comment": {"body": "Do we really want the users to be able to access the router over SSH?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157841932"}}
{"comment": {"body": "It\u2019s already dropped :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157842849"}}
{"comment": {"body": "I\u2019ve cleared around 25MB\\+ of memory, so for now we are good, but we do need to take care of all those agents.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157842879"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Opened `https://levltech.monday.com/boards/600814058/pulses/614893773` ticket", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157843450"}}
{"comment": {"body": "We should be more explicit in the class name. Authorized or authenticated? What\u2019s the difference between this and `AuthStatus`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157843733"}}
{"comment": {"body": "Is this a security issue to leave it open?\n\nNodogsplash talk about it as it should be intentionally open in case we block ourselves somehow.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157848552"}}
{"comment": {"body": "Should this exist?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157848572"}}
{"comment": {"body": "I forgot to remove this part as `$authtarget` is the link to authorize the device and it shouldn\u2019t be available on the splash page", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/293/_/diff#comment-157848607"}}
{"title": "Feature/rogue ap simple detection", "number": 294, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/294", "body": "Added simple rogue AP detection process that alerts if we get a message with src addr identical to our BSSID. To do so, bssid is sent from ap agent and data processor pushes every radiotap packet (with addr2 field) to the rogue detection process\nUpdated ble to not include rogue ap detection\n\n"}
{"comment": {"body": "Please do not merge this PR until #293 is merged because they conflict", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/294/_/diff#comment-157839670"}}
{"comment": {"body": "Doesn't that mean that it's disabled for now?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/294/_/diff#comment-157843255"}}
{"comment": {"body": "Notice that this is the BLE docker compose.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/294/_/diff#comment-157843653"}}
{"comment": {"body": "How would you investigate issues with the algorithm? Are you able to extract traces and replay them?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/294/_/diff#comment-157955711"}}
{"comment": {"body": "I thought about the need to add this as an operation but didn\u2019t get to it. Would like to include in a different PR.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/294/_/diff#comment-157958167"}}
{"comment": {"body": "So it turns out we can just get the BSSID by reading the `/sys/class/net/ath0/address` file.  \nThe \u201cNot-Associated\u201d case is identified by the `ath0` dir not existing.\n\nNo need to even shell-out. \\(EDIT: No need to fix this, just FYI\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/294/_/diff#comment-158246534"}}
{"comment": {"body": "BTW if we redefine `History Event` to subclass `IntEnum` instead of `Enum` we can drop the `.value` everywhere.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/294/_/diff#comment-158249373"}}
{"title": "Make ax work persistently", "number": 295, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/295", "body": "Setting HE when AX, making sure all.sh cooporates\n\n"}
{"title": "Fix none", "number": 296, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/296", "body": "Check for none\n\n"}
{"title": "Return MATCH is all features either MATCH or NO_MODEL", "number": 297, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/297", "body": ""}
{"title": "gilad/levl-malware", "number": 298, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/298", "body": "Update Guy's PR to use the latest version of the smartlock protocol"}
{"title": "improve stability", "number": 299, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/299", "body": "should deal with the bug from CCP-229"}
{"title": "Motion Detect", "number": 3, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/3", "body": "Push a first version of the motion detection offline and online algorithm \n\n"}
{"title": "Added option to fix a model to use for all classifications.", "number": 30, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/30", "body": ""}
{"title": "Feature/remove py scripts", "number": 300, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/300", "body": "One scripts to rule them all\nupdate all.sh\n\n"}
{"title": "Merge ap_monitor_all.py into ap_connections_agent.py", "number": 301, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/301", "body": ""}
{"comment": {"body": "Maybe it's time to rename `ap_connections_agent.py` to `ap_agent.py`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/301/_/diff#comment-157876657"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/301/_/diff#comment-157876793"}}
{"title": "Feature/monitoring other channels", "number": 302, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/302", "body": "AP agent also streams br-demo interface for protocols DHCP, DNS and ARP\nAdd Ethernet socket on port 6103 \nParse Ethernet packet and pass to DHCP fingerprinter\n\n"}
{"comment": {"body": "`EthernetStreamTcpHandler` IS-A `RadiotapStreamTcpHandler` ? I don\u2019t think it is!\n\n![](https://bitbucket.org/repo/8X5z9dk/images/3330792993-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/302/_/diff#comment-157879415"}}
{"comment": {"body": "This is the best code in existence", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/302/_/diff#comment-157879736"}}
{"comment": {"body": "I\u2019ll rename `RadiotapStreamTcpHandler` to `RadiotapStreamMixinTcpHandler` later", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/302/_/diff#comment-157880769"}}
{"title": "Feature/init params in operation", "number": 303, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/303", "body": "Save init params in trace\nReplay with init params\n\n"}
{"title": "Gilad/recsys motion", "number": 304, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/304", "body": "Added with_motion and scheduling_type  as DB columns"}
{"title": "Feature/force splash page", "number": 305, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/305", "body": "\n\nAdd authentication process which runs in parallel during training\n\nBlocks training state until conditions are fulfilled (for wifi: splash page access and model update)\n\nFor 2.4g devices, authorize immediately\n\n\n\nFlask should show the proper page during every stage\n\nTraining state\nClassification state\nAuthorized (which should redirect to external website)\n\n\n\nUpdate NDS to v4.5.1\n\n\n\n"}
{"title": "Feature/fix dhcp usage", "number": 306, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/306", "body": "Unmiss DHCP from Ethernet\nError handling\n\n"}
{"title": "Added unique trace name for reclassifications", "number": 307, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/307", "body": ""}
{"comment": {"body": "I suggest explicit arguments, if possible", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/307/_/diff#comment-158075504"}}
{"comment": {"body": "these shortcuts are quite hideous, to be frank", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/307/_/diff#comment-158077690"}}
{"title": "Add a CSD detection model", "number": 308, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/308", "body": ""}
{"title": "Python3 builtin in firmware", "number": 309, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/309", "body": ""}
{"comment": {"body": "screw american routers!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/309/_/diff#comment-158094586"}}
{"comment": {"body": "Router life matters", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/309/_/diff#comment-158094951"}}
{"comment": {"body": "I reverted the router firmware to the previous one until we manage to build a firmware for the American router\u2026  \n  \nThis PR will wait until we flash both routers with the new firmware", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/309/_/diff#comment-*********"}}
{"title": "Add more elaborate logs; prints->logs", "number": 31, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/31", "body": ""}
{"title": "Gilad/wifi ui", "number": 310, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/310", "body": "Add wifi display info\nAdded manufacturer\n\n"}
{"comment": {"body": "This parses the  fingerbank response to get some more useful data.\n\nThis also displays the IP in the IP column if we got one from NDS", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/310/_/diff#comment-*********"}}
{"comment": {"body": "Are you discarding android devices that don't have accurate description? ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/310/_/diff#comment-*********"}}
{"comment": {"body": "No, I am discarding 2 specific device types that does not make sense. Note that it\u2019s not the device model.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/310/_/diff#comment-*********"}}
{"title": "COLORIZE !", "number": 311, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/311", "body": "(Gilad:)\nAdded colors to training prints and to two indications that helps catch DHCP/Caps packets."}
{"comment": {"body": "Easter eggs all over the place", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/311/_/diff#comment-*********"}}
{"title": "Replaced streamer_agent with nc when possible", "number": 312, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/312", "body": "The semantics with which we use streamer_agent for radiotaps connections are identical to those of nc, so its better to use it instead of a python process.\n(also fixed minor UI bug)"}
{"title": "Twoway snr fixes", "number": 313, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/313", "body": "Changed minimum number of averaged packets in decision range that are required for classification\nComment fix\n\n"}
{"title": "Added hostname from dhcp request", "number": 314, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/314", "body": ""}
{"comment": {"body": "What's with deleting rogue AP code?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/314/_/diff#comment-158322552"}}
{"comment": {"body": "What are you talking about?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/314/_/diff#comment-158327003"}}
{"comment": {"body": "Nevermind it was BitBucket playing tricks on my mind", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/314/_/diff#comment-158331749"}}
{"title": "Parameter update for Two-Way SNR feature", "number": 315, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/315", "body": "Updated variance parameter for which extrapolation is made. We now require more variance in data to extrapolate."}
{"comment": {"body": "This is the most amazing code I've ever seen, this changes everything.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/315/_/diff#comment-158336486"}}
{"title": "Rogue AP fixes and improvements", "number": 316, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/316", "body": "Small fixes + rate limiting detection reports (if rogue ap detected, wouldn't be tested again for 5 min)\n\n"}
{"title": "Disable background and reclassifications", "number": 317, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/317", "body": ""}
{"title": "Don't splash page", "number": 318, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/318", "body": ""}
{"title": "Some basic Ethernet parsing capabilities", "number": 319, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/319", "body": ""}
{"title": "Tee inputs", "number": 32, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32", "body": "Recording is now usable and is enabled by default (can also be cancelled using the -q flag)."}
{"comment": {"body": "The only thing that bothers me is that the processes sync is only based on getting the current date and time and given the low resolution of `sleep`, we might get reordered packets.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145679582"}}
{"comment": {"body": "I partially agree, but I'm not sure that there is another way, also the diffs are pretty large and note that I am not only relying on the sleep method but checks for the exact time I should transmit the packet in.\n\neven though, I don't think that synchronization between the processes is crucial for us here in that resolution\u2026 it\u2019s only important not to reorder the packets inside each stream, which I don't.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145823030"}}
{"comment": {"body": "Please approve.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145823054"}}
{"comment": {"body": "how big are the diffs?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145823376"}}
{"comment": {"body": "It does matter a little because it\u2019s better that the CFR\u2019s of a station arrive before the station gets marked as connected \\(both events are communicated via different ports\\). Although it can be solved if we add a pre-association cache for CFR\u2019s \\(there\u2019s already a mechanism for other types of data\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145823762"}}
{"comment": {"body": "@{5b02c344cd95416ee040ad9c} Theoretically, sync between the processes could matter. Like if a cfr packet was received before or after the connection message - this would cause the results to be at least not bit-exact consistent. Theoretically that is, if the diffs are small enough to enable this case.\n\nHaving said that - If the diffs are supposed to be around 10msec, which is the python sleep resolution for non-realtime systems, and given a solution is not straight forward - let\u2019s start using this feature and modify it later if needed.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145823845"}}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} You beat me to it. Anyway, I don\u2019t think that this justifies updating the logic on the server itself. Only if there are other reasons in addition.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145823912"}}
{"comment": {"body": "In seconds:\n\n```\nwaiting 0.010765\nwaiting 0.017445\nwaiting 0.005835\nwaiting 0.020044\nwaiting 0.006326\nwaiting 0.022948\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145824578"}}
{"comment": {"body": "That\u2019s 120 words per minute right there.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145824606"}}
{"comment": {"body": "It could theoretically happen with a real router as well so it\u2019s nice to have", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/32/_/diff#comment-145824776"}}
{"title": "Install remote router", "number": 320, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/320", "body": "Some basic Ethernet parsing capabilities\nremote router configuration\n\n"}
{"title": "identify any device on connection and not just device going through training", "number": 321, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/321", "body": ""}
{"title": "Add standards tracking in capabillities", "number": 322, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/322", "body": ""}
{"comment": {"body": "Can you please use an enum instead?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/322/_/diff#comment-158506602"}}
{"comment": {"body": "Did you observe that this doesn\u2019t change?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/322/_/diff#comment-158506761"}}
{"comment": {"body": "I think it\u2019s an overkill for a very definite strings that is used just once", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/322/_/diff#comment-158509159"}}
{"comment": {"body": "Yes. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/322/_/diff#comment-158509180"}}
{"title": "scripts to generate certificates on DC", "number": 323, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/323", "body": ""}
{"comment": {"body": "Can we have the certificate passwordless? To install the certificate, we need to provide this password", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/323/_/diff#comment-158618215"}}
{"comment": {"body": "Yes. Microsoft does not allow to export certificate without a password.  \nWe can remove the password later with doing something like this:\n\n[https://serverfault.com/questions/515833/how-to-remove-private-key-password-from-pkcs12-container](https://serverfault.com/questions/515833/how-to-remove-private-key-password-from-pkcs12-container)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/323/_/diff#comment-158621275"}}
{"title": "Gilad/wifi auth0", "number": 324, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324", "body": "Splash page redirects to Auth0 for authentication\n"}
{"comment": {"body": "you have a conflict here", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324/_/diff#comment-158577621"}}
{"comment": {"body": "thx", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324/_/diff#comment-158582620"}}
{"comment": {"body": "would be awkward to see that the auth server, which had sent this message, is not available. I think that this condition is redundant", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324/_/diff#comment-158702708"}}
{"comment": {"body": "is it safe to not guard this `state_token_map ` with some lock?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324/_/diff#comment-158703137"}}
{"comment": {"body": "This event is sent by the flask", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324/_/diff#comment-158703240"}}
{"comment": {"body": "I think it\u2019s safe?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324/_/diff#comment-158703349"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} OOOHHH", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324/_/diff#comment-158703840"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} NOW IT ALL MAKES SENSE. DISREGARD MY COMMENT", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324/_/diff#comment-158704101"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} looks like flask runs with with a single thread and can support just a single request at time, so it should be safe. good job", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/324/_/diff#comment-158705370"}}
{"title": "Nodogosplash", "number": 325, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/325", "body": "Nodogsplash back in action\nFix flow with blocking training until user is authorized\nFix flow with redirecting user back to NDS to be authenticated\nAllow HTTPS\n\n\n"}
{"title": "Feature/mitm arp detection", "number": 326, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/326", "body": "MITM detection feature based on observing multiple MACs replying to ARP requests for a specific IP. ARPs are being sent periodically from router similarly to the pings. Only ARP packets are being processed in server side, and its done in the main thread to avoid coherency issues with users current state. This is important because if a MITM attempt was detected during an operation, we want to fail that operation. Were only expecting ~100 ARP packets per second anyway for now, so there is no fear for any performance issues.\n"}
{"comment": {"body": "Is the comment correct? 100 ARPs per second? That\u2019s quite a lot", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/326/_/diff#comment-158795317"}}
{"comment": {"body": "It\u2019s effectively less right now, around 10. But 100/sec isn\u2019t that large considering the quick logic performed on the data.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/326/_/diff#comment-158796046"}}
{"title": "small fix in two way snr classification", "number": 327, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/327", "body": "The number of participating packets in the classification decision included packets that were out of the model AP SNR range before the fix"}
{"title": "Nitzan/clock skews", "number": 328, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/328", "body": "workspace stuff\nfind_nearest accepts multiple values\nwhen timeout training return LEVL_TRAINING_MORE_DATA_NEEDED instead of LEVL_TRAINING_COMPLETE\n\n"}
{"title": "I present to you: presence", "number": 329, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/329", "body": ""}
{"comment": {"body": "You must be some HTML programmer", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/329/_/diff#comment-158693540"}}
{"title": "iq_parser is called repeatedly even when no data arrives - remove endless logs", "number": 33, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/33", "body": "Well, Grish was right, removing the log so it doesnt fill the entire file (debug will also be quite bad).\nWhen a connection exists, it repeatedly tries to get data (even if there is none) by calling iq_parser."}
{"title": "Timeout motion-detect on no-cfrs / disconnection", "number": 330, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/330", "body": ""}
{"comment": {"body": "as far as I see, also `self._last_res` should change to `MotionState.UNDECIDED`. Maybe even reset the whole algorithm", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/330/_/diff#comment-158712743"}}
{"title": "iPhone profile", "number": 331, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/331", "body": "add sshpass to remote runner\nios profile\n\n"}
{"title": "Feature/persistent quarantine and release", "number": 332, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/332", "body": "Quarantine state is saved parallel to classification state and is stored in DB per device. Also enabled the unquarantine feature.\n"}
{"title": "Feature/install certificates windows and iphone", "number": 333, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/333", "body": "Once training completes, user is greeted with a certificate page, where the user can download a windows powershell script for Windows or an xml file for iphone\n\nCall dc.ccpilot.levl.tech to generate certificate per user\nInsert the certificate into the appropriate OS format\n\nUser is expected to perform disconnection after downloading the certificates and then reconnect to perform classifcation\nMissing:\n\nReal username instead of dummy Radiustest\nDesign for splash page\n\n"}
{"comment": {"body": "Gonna have to count on you on this one\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/333/_/diff#comment-158815552"}}
{"comment": {"body": "I wrote it so this works", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/333/_/diff#comment-158816764"}}
{"comment": {"body": "Can you please store the generated certificate in persistent way so we can track and use them later?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/333/_/diff#comment-158825960"}}
{"comment": {"body": "Saved in DB, both name and certificate data", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/333/_/diff#comment-158885323"}}
{"title": "off by one", "number": 334, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/334", "body": "Fixes not all windows used in training"}
{"comment": {"body": "REVIEW THIS", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/334/_/diff#comment-158853477"}}
{"title": "Splashier pages", "number": 335, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/335", "body": ""}
{"comment": {"body": "Put this at the start of the function, would you?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/335/_/diff#comment-158935171"}}
{"comment": {"body": "no prints, only logging", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/335/_/diff#comment-158936627"}}
{"comment": {"body": "this print", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/335/_/diff#comment-158938093"}}
{"title": "CFRSlopes disable when there's motion", "number": 336, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/336", "body": "\nSend motion updates to wifi trainer and wifi classifier. If there's SELF MOTION during such operation, record it.\nCFRSlope in Wifi trainer is cancelled if there's motion (returns NEED MORE DATA indefinately) CFRSlope in Wifi classifier is cancelled and returns MATCH"}
{"title": "Ui details patches", "number": 337, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/337", "body": "Some overfitted conditions to support missing/misleading info items for common devices\nSome more ui fixes\n\nMissing persistent hostname saving, will be added soon.\n"}
{"comment": {"body": "I wouldn\u2019t say that it\u2019s very unreadable, but you could shorten to `event.hostname not in (\u201d-\u201d, None, self.users_dict[event.device].get('device_name'))`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/337/_/diff#comment-158934454"}}
{"title": "Weekend changes", "number": 338, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/338", "body": "Parsing of EAP protocol to get the cert details\nAndroid onboarding app\nOnboarding spalsh page changes\n./extract_mac.sh only passes authenticated macs to the system\n\n"}
{"title": "Fixed a None dereference and some HTML titles", "number": 339, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/339", "body": "Device name should also stop disapearing"}
{"title": "Generic setup", "number": 34, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/34", "body": ""}
{"title": "Sign auto-generated APK app", "number": 340, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/340", "body": ""}
{"title": "hardcode number of totalDevices", "number": 341, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/341", "body": ""}
{"comment": {"body": "More like softcode", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/341/_/diff#comment-159444600"}}
{"comment": {"body": "Mediumcode", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/341/_/diff#comment-159449940"}}
{"title": "Gal/csd detection caps", "number": 342, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/342", "body": "Add a CSD detection model\nMinor cosmetic changes\nSet the rx_chains param to be configurable\nAdd the 5G band activation with the CSD detection feature\nmodify params to comply with 400 packets per event\nAdd a CSD radio tap verification to the phy-based detection\nMinor improvements\n\n"}
{"title": "Add AD auth", "number": 343, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/343", "body": "Also:\n\nfixed cert hardcoded SSID\nmove forget to data_processor\n\n"}
{"title": "NoNodogsplash", "number": 344, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/344", "body": "Dont start NDS:\n\nAP side: disable NDS via environmental variable\nServer side: handle case when there's no data from nds\n\n\n\nCustom redirection to splashpage:\n\nRouter has small HTTP server\nWhen accesing go.levl.tech, it redirects to the routers webserver than redirects back to the servers splashpage with the users MAC address\n\n\n\nRemove id_token logic,\n\nNot asking for username/password\n\n\n\n\n"}
{"comment": {"body": "Splashpage is now at [https://go.levl.tech](https://go.levl.tech) which forwards the user to [http://********:8083](http://********:8083) which forwards to user to the real splashpage.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/344/_/diff#comment-159673062"}}
{"title": "do not accept probe requests and fix oui_type", "number": 345, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/345", "body": ""}
{"title": "Hide username for WiFi for now", "number": 346, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/346", "body": ""}
{"title": "CPU usage reduction", "number": 347, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/347", "body": "do not override status\nno nds ion startup\nfinish train on jsut model\ndisable icmp feature\nless mess in debug log\nno probs in capfp\nstats printing mechanism\nremove CPU strain\ncaption\ncollection MAC stats\nrevert no NDS changes\n\n"}
{"title": "Don't show blank splashpage screen when the user is authenticated.", "number": 348, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/348", "body": ""}
{"title": "Feature/rssi ui indication", "number": 349, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/349", "body": "SNR is being extracted from radiotap and saved (short memory running average) per user, frontend presents appropriate indication in connectivity column.\n"}
{"title": "Setup Python files now git-lfs", "number": 35, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/35", "body": ""}
{"comment": {"body": "Merge this", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/35/_/diff#comment-147339121"}}
{"title": "Users count will only present users of online devices", "number": 350, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/350", "body": ""}
{"title": "Feature/quarantine with iptables", "number": 351, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/351", "body": "Blocking now works with iptables when nds doesnt work\n\n\n"}
{"title": "on the router, server is now supporting multiple requests", "number": 352, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/352", "body": ""}
{"title": "mac", "number": 353, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/353", "body": ""}
{"title": "Upd and ver", "number": 354, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/354", "body": ""}
{"title": "Apply filter to DHCP", "number": 355, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/355", "body": ""}
{"title": "Fix motion detector flooding the data processor", "number": 356, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/356", "body": ""}
{"title": "Feature/live editing", "number": 357, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/357", "body": "Add Admin interface to update model and/or certificate\nUpdate users_dict when such update occurs\nAdd reload all from DB\n\nExample calls:\n\n\nUpdate model\ncurl -d 'mac=34:29:12:d3:a9:ddmodel={\"IEEE802Capabillites\": \"W3siMTkxIjogM30sIFtudWxsLCBudWxsLCBudWxsLCBbMywgMjNdXSwgWzY0LCAzNiwgNDAsIDQ0LCA0OCwgNTIsIDU2LCA2MF0sIFtbMTEzMDYzMDEsIDFdLCBbMjA3MjIsIDBdXSwgWyI4MDIuMTFuIiwgIjgwMi4xMWFjIl1d\", \"CFRSlopeFrequency\": \"gASVygAAAAAAAAB9lCiMBXRoZXRhlEc//On9oBHOoYwJZnJlcXVlbmN5lEdAf0AAAAAAAIwObWF4X2xvY2F0aW9uX3iUjBVudW1weS5jb3JlLm11bHRpYXJyYXmUjAZzY2FsYXKUk5SMBW51bXB5lIwFZHR5cGWUk5SMAmY4lEsASwGHlFKUKEsDjAE8lE5OTkr/////Sv////9LAHSUYkMInkHYJXgvGMCUhpRSlIwObWF4X2xvY2F0aW9uX3mUaAZoDEMIqU+cSzMbOUCUhpRSlHUu==\", \"TwoWaySNR\": null, \"VDim\": null, \"DHCPFingerprinting\": \"eyJESENQRlAiOiBbMSwgMywgNiwgMTUsIDI2LCAyOCwgNTEsIDU4LCA1OSwgNDNdLCAiZGhjcF92ZW5kb3IiOiAiSFVBV0VJOmFuZHJvaWQ6U05FIiwgImRoY3BfaG9zdG5hbWUiOiAiSFVBV0VJX01hdGVfMjBfbGl0ZS01MDExZCJ9\"}' -X POST http://localhost:1234/admin/update_user \n\n\n\n\nReload user (like after changing something in the postgres DB)\n\ncurl http://localhost:1234/admin/reload_users?mac=34:29:12:d3:a9:dd \n\n\nUpdate certificate\n\ncurl -F \"mac=11:22:33:44:55:66\" -F \"certificate_name=47328648723\" -F \"certificate=@cert.pfx\" -X POST http://localhost:1234/admin/update_user\n"}
{"title": "Radius fast retry", "number": 358, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/358", "body": ""}
{"title": "Autosec", "number": 359, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359", "body": "Firmware files generation\nOther goodies\n\n"}
{"comment": {"body": "r and R arguments could be easily confused. How about r and l? (l for load)\r\nOr any other combination would be file", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160280297"}}
{"comment": {"body": "what's this delay used for?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160280478"}}
{"comment": {"body": "What's the reason for moving delay to the start of the loop? The usual design is `while() { do something; sleep;}`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160281057"}}
{"comment": {"body": "Welcoming PR, but I wish that your PR wouldn't consist of 90% formatting. It's a bit tedious", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160281320"}}
{"comment": {"body": "Same point here about the delay in loop", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160281794"}}
{"comment": {"body": "inconsistent arguments formatting here", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160282058"}}
{"comment": {"body": "nc was replaced back with the streamer.py due to not closing connections or similar. Was this fixed?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160282921"}}
{"comment": {"body": "I can't say I favor using an empty list as an indicator for no process created. A list implicitly tells that there could be more than 1 processes.\r\n\r\nYou could replace the None with a dummy interface which returns some \"safe\" value when the subprocess is not assigned. \r\nWhat do you think?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160284724"}}
{"comment": {"body": "shouldn't the arper and the pinger reside in the same location?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160285419"}}
{"comment": {"body": "Fixed in 250d1e5a", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160294961"}}
{"comment": {"body": "Because we use continue, there are several \u201cends\u201d to the loop, so it\u2019s cleaner to put it at the start instead of all the possible \u201cends\u201d", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160295194"}}
{"comment": {"body": "Same answer", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160295258"}}
{"comment": {"body": "wdym", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160295327"}}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} First I see this\n\n![](https://bitbucket.org/repo/8X5z9dk/images/1734966591-image.png)\nthen I see this \n\n![](https://bitbucket.org/repo/8X5z9dk/images/2156011469-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160295670"}}
{"comment": {"body": "the `_stream_subprocess` variables hold a list of processes - one for `tcpdump`, one for the corresponding `nc`  \nIt has to be a list because we iterate over it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160296520"}}
{"comment": {"body": "That fix was insufficient, this fix is proper.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160296728"}}
{"comment": {"body": "I\u2019m sorry but it\u2019s way more tedious formatting code manually, CCP-15", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160297779"}}
{"comment": {"body": "I used the same letter because they\u2019re related and do the opposite of one another - I find it makes it easier to remember. It\u2019s pretty common to differentiate between upper and lower case letters in CLI\u2019s.  \n  \nI personally prefer it this way, like this comment if you think I should change it anyway", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160298690"}}
{"comment": {"body": "Yes, but this entire firmware-non-firmware duality is very tedious to test after changing \\(need to test if it still works after flashing and without flashing using the regular setup, generating new firmware is pain\\). It works right now, so I prefer to leave it like this", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160299612"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} It\u2019s depends on the line\u2019s length. If it fits within the limit, it\u2019s put in the same line. If it doesn\u2019t, each argument gets it own line", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160300675"}}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} I\u2019m afraid of somebody mistakenly restoring a DB without saving the previous one. I hope it doesn\u2019t happen", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160302306"}}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} Proper fixes are tight", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160302709"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} It can\u2019t happen because it\u2019ll most likely fail on the `if [ -z ${CCP_BKUP+x} ]; then` if you had no intent to restore", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/359/_/diff#comment-160305281"}}
{"title": "Removing DB locking mechanism temporarily until a better solution is in place", "number": 36, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/36", "body": "Its causing the server to freeze up during boot and we want to start testing.\nI have a solution for this in my remove_multirprocessing branch but its not ready for merging yet"}
{"comment": {"body": "Doesn\u2019t this restore@{5b02c344cd95416ee040ad9c} problems?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/36/_/diff#comment-145820064"}}
{"comment": {"body": "Maybe, but with it the server doesn\u2019t even boot up", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/36/_/diff#comment-145820070"}}
{"comment": {"body": "As I said, it\u2019s not ideal and it\u2019s temporarily until a better solution is in place", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/36/_/diff#comment-145820082"}}
{"comment": {"body": "It means anyway that the system can\u2019t be tested, due to what @{5b02c344cd95416ee040ad9c} raised", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/36/_/diff#comment-145820093"}}
{"title": "Gilad/mac2serial", "number": 360, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360", "body": "Removed users_dict in favour of sessions and devices\nsessions are indexed by a transient_id, which is mac address (or the generated ble-id)\ndevices are indexed by a device_id, which is permanent (certificate serial / the same ble id)\nWhen a device is connected, a session is created and it is stuck in just_connected_state.\nIt enters onboarding if the user goes to the landing page.\nIt enters classification if we can catch the radius requests containing the certificate serial number.\nWhen we do, we attach the device to the session.\n"}
{"comment": {"body": "`json.dumps` into what?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160300060"}}
{"comment": {"body": "Are you planning on removing the logging here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160301220"}}
{"comment": {"body": "Safe to assume that `device.device_record` is not `None` here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160334173"}}
{"comment": {"body": "`device_record` is never none", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160334624"}}
{"comment": {"body": "It starts as \u201cfloating\u201d == without a device id, and we either fill it up or replace it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160334863"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} ok, so `device_id `is assured to not be `None` here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160335359"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} I guess it can technically be `None` if we\u2019re running on wifi mode, but isn\u2019t this an exclusive BLE data structure?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160335695"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} This should only be called on BLE if I\u2019m not mistaken", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160335908"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} So that\u2019s fine", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160336077"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} Yeah coolz", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160336446"}}
{"comment": {"body": "Initialized twice?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160348798"}}
