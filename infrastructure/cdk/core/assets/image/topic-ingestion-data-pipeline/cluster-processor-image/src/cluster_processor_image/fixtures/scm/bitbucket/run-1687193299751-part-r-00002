{"title": "LDI-110 implement classifier prior logic", "number": 1234, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1234", "body": "Apply prior logic only if general_os is in white list configuration, currently includes Apple OS and Android.\nA slight simplification of prior_logic_simulation.\n{: data-inline-card='' } \n{: data-inline-card='' } \n{: data-inline-card='' }"}
{"title": "Permission Test", "number": 1235, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1235", "body": ""}
{"title": "Permission Test", "number": 1236, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1236", "body": ""}
{"title": "Permission Test", "number": 1237, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1237", "body": ""}
{"title": "LDI-576 Model Version Test 1", "number": 1238, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1238", "body": "Permission Test\nPermission Test\n\n"}
{"title": "Permission Test", "number": 1239, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1239", "body": ""}
{"title": "Features unitests - Phase 1", "number": 124, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/124", "body": "Phase 1 of adding more unitests (netbios transaction id, icmp mclr, dhcpv4 handshake) and fixing current unitests.\n\n"}
{"title": "Hotfix/update rp uuid", "number": 1240, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1240", "body": "update models latest version\nLDI-794: add to hel charts and bitbucket pipelines for cujo-stage\nupdate rp_uuid\n\n"}
{"title": "LDI-849", "number": 1241, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1241", "body": "As part of {: data-inline-card='' }\nwe wanted to:\n\nRemove metrics thread\n\nReduce I/O - by metrics batching\n\nUse BufferdList class to act as the metrics client internal buffer.\nUse micro batches to optimize shipped metrics batch size.\nadd new and more in-depth tests\n\n\n\n"}
{"comment": {"body": "Can you explain what caused the memory leak and how your solution corrects this?  \nDo you have a dedicated test to check this?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1241/_/diff#comment-380882085"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/169](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/169){: data-inline-card='' } - Regression\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/168](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/168){: data-inline-card='' } - API\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/167](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/167){: data-inline-card='' } - Smoke\n\n\u200c\n\nMetrics working in Grafana:\n\n[https://grafana.vpc.eros-prod.levltech.io/d/hLft52I4z/eros-dashboard?orgId=1&from=1679635347288&to=1679637334166&var-Tenant=test-22653-ldi-849-release-p1-ldi-849-releasx-ldi-84x&var-Event=CONNECTED&var-Event=DISCONNECTED&var-Event=ONGOING](https://grafana.vpc.eros-prod.levltech.io/d/hLft52I4z/eros-dashboard?orgId=1&from=1679635347288&to=1679637334166&var-Tenant=test-22653-ldi-849-release-p1-ldi-849-releasx-ldi-84x&var-Event=CONNECTED&var-Event=DISCONNECTED&var-Event=ONGOING)\n\n![](https://bitbucket.org/repo/o5KReBa/images/3989613053-Capture.PNG)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1241/_/diff#comment-381146380"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} please reply to the above comment ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1241/_/diff#comment-381458602"}}
{"comment": {"body": "Hey @{6252eba45d1e700069ad0104} , maybe 'memory leak' may not be the accurate term. The previous state was a thread that flushed the metrics out. Between the main thread and the metrics thread, there was a queue that the main thread pushed the metrics to, and the metrics thread pulled the metrics to flush. It turned out that the rate at which the queue was growing was not in sync with the CPU time that the metrics thread got, which caused the queue to explode and consume a lot of memory. The solution was to remove this thread and batch these metrics when sending them. The dedicated test for this was the \u201cscale-test\u201d we were running. In initial tests, we saw significant improvement in memory consumption.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1241/_/diff#comment-381462077"}}
{"title": "spellcheck", "number": 1242, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1242", "body": "api: {: data-inline-card='' }\nsmoke: {: data-inline-card='' }\nregression: {: data-inline-card='' }"}
{"comment": {"body": "what this add/changes/requires?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1242/_/diff#comment-383639043"}}
{"comment": {"body": "`black` support of new python features, specifically in our case `match` which is the equivalent `switch` `case` in `c`. @{63b6c1aaf3e7004f77ff2273} started using it recently. It is already in `dev`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1242/_/diff#comment-383770992"}}
{"title": "Release/phase 1", "number": 1243, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1243", "body": "Merged in set_mdns_info_category_to_model (pull request #1226)\nSet mdns info category to model\n\n[skip ci] bumped eros-chart new version 0.87.0, build 12112\nSet MDNS_INFO feature's category to MODEL\nMerged dev into set_mdns_info_category_to_model\n\nApproved-by: Ariel Tohar Approved-by: Ophir Carmi\n\n\nMerged in set_cujo_regression_pipeline_in_release_phase_1 (pull request #1232)\nSet cujo regression pipeline\n\nSet cujo regression pipeline\n\nApproved-by: Shimon Goulkarov Approved-by: Ron Cohen\n\n\nupdate rp_uuid\n\n\nMerged in LDI-849_release-p1 (pull request #1241)\nLDI-849\n\ncherry pick for release branch from LDI-849\ncherry pick for release branch from LDI-849\nreport metircs client flushing latency\n\nApproved-by: Shimon Goulkarov Approved-by: Daniel Katz\n\n\n"}
{"title": "askey l2 fp", "number": 1244, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1244", "body": "askey l2 fp"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/401](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/401){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/402](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/402){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/403](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/403){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1244/_/diff#comment-381478967"}}
{"title": "Commented Model Version Step", "number": 1245, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1245", "body": ""}
{"title": "added missing xb7 l2 fingerprint", "number": 1246, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1246", "body": "added missing xb7 l2 fingerprint"}
{"comment": {"body": "good job:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1246/_/diff#comment-382077722"}}
{"comment": {"body": "we need this fix in release/phase\\_1 branch", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1246/_/diff#comment-382357830"}}
{"comment": {"body": "the values are not sorted as defined and may be difficult to show difference between versions later on\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1246/_/diff#comment-382363267"}}
{"comment": {"body": "fixed. thanks.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1246/_/diff#comment-382372028"}}
{"comment": {"body": "A-[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/562](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/562){: data-inline-card='' } \n\nR-[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/561](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/561){: data-inline-card='' } \n\nS-[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/560](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/560){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1246/_/diff#comment-382382877"}}
{"title": "Permission Test", "number": 1247, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1247", "body": ""}
{"title": "Permission Test", "number": 1248, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1248", "body": ""}
{"title": "bitbucket-pipelines.yml edited online with Bitbucket", "number": 1249, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1249", "body": "bitbucket-pipelines.yml edited online with Bitbucket"}
{"title": "Dev ops/no db performance test (Ariel)", "number": 125, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/125", "body": "Fixing types, names and added cosmetic changes to the current flow.\nAdded two unitests that checks the NotificationItem build mechanism.\n(The changes in pytest.ini are temporary until the report-portal problem is fixed)."}
{"comment": {"body": "why not sink?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/125/_/diff#comment-244708012"}}
{"comment": {"body": "to be able to unit test it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/125/_/diff#comment-244765700"}}
{"comment": {"body": "let\u2019s move the metrics to new file: classification\\_metrics.py", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/125/_/diff#comment-247492906"}}
{"title": "add report-portal client fixed version", "number": 1250, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1250", "body": ""}
{"title": "permission test", "number": 1251, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1251", "body": ""}
{"title": "permission test", "number": 1252, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1252", "body": ""}
{"title": "permission test", "number": 1253, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1253", "body": ""}
{"title": "permission test", "number": 1254, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1254", "body": ""}
{"title": "permission test", "number": 1255, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1255", "body": ""}
{"title": "drop features with 'None' vaule field", "number": 1256, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1256", "body": "We should drop features that contain None string in the value field"}
{"comment": {"body": "can we add UT for this as discussed?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1256/_/diff#comment-382440397"}}
{"comment": {"body": "yes sure im on it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1256/_/diff#comment-382445379"}}
{"comment": {"body": "not critical  \nbut for clarity I\u2019d extract these conditions to a specific method with returns boolean value, than call it on each item in res  \n`r in res if \"value\" in r and r[\"value\"] not in (\"\", \"None\", None)`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1256/_/diff#comment-382656866"}}
{"comment": {"body": "API - [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/895](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/895){: data-inline-card='' } \n\nSmoke -  [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/894](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/894){: data-inline-card='' } \n\nRegression - [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/898](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/898){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1256/_/diff#comment-382727624"}}
{"title": "permission test", "number": 1257, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1257", "body": ""}
{"title": "LDI-576 Model Version Test 1", "number": 1258, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1258", "body": "permission test\npermission test\n\n"}
{"title": "permission test", "number": 1259, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1259", "body": ""}
{"title": "Updated report portal", "number": 126, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/126", "body": ""}
{"title": "LDI-576 Model Version Test 1", "number": 1260, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1260", "body": "permission test\npermission test\n\n"}
{"title": "permission test", "number": 1261, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1261", "body": ""}
{"title": "LDI-576 Model Version Test 1", "number": 1262, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1262", "body": "permission test\npermission test\n\n"}
{"title": "LDI-934 Now filling the mdns_hostname value in the data_catalog", "number": 1263, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1263", "body": "It turns out that we left the mdns_hostname field empty in the datacatalog. This PR fixes this and adds a relevant test to make sure its valid.\nThanks @{621df03094f7e20069fd6ab2} for finding this bug :slight_smile:"}
{"comment": {"body": "Regression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/819](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/819){: data-inline-card='' } \n\nAPI [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/817](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/817){: data-inline-card='' } \n\nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/818](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/818){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1263/_/diff#comment-382693003"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} can you redirect to release branch?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1263/_/diff#comment-382710936"}}
{"comment": {"body": "Sure, I opened a separate PR for the release branch because changing the target branch added a lot of changes from dev\n\n[https://bitbucket.org/levl/eros-classifier/pull-requests/1264](https://bitbucket.org/levl/eros-classifier/pull-requests/1264){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1263/_/diff#comment-382715003"}}
{"title": "LDI-934 Now filling the mdns_hostname value in the data_catalog", "number": 1264, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1264", "body": "This is the same logic described here, just for the release branch as well :slight_smile:"}
{"comment": {"body": "API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/989](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/989){: data-inline-card='' } \n\nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/990](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/990){: data-inline-card='' } \n\nRegression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/991](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/991){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1264/_/diff#comment-382791036"}}
{"title": "Adding back a deleted json file model", "number": 1265, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1265", "body": "The model must maintain back compatibility as the user-case of updating the model without updating the classifier code is one of the main use-cases of the solution.\n{: data-inline-card='' } \n{: data-inline-card='' } \n{: data-inline-card='' } \n{: data-inline-card='' }"}
{"title": "support flushing to kinesis by size (MB), WIP", "number": 1266, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1266", "body": "WIP, should add ut and test in CI env."}
{"title": "added l2 fp2 from GDE", "number": 1267, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1267", "body": "added l2 fp2 from GDE"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1086](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1086){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1085](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1085){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1087](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1087){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1267/_/diff#comment-383632079"}}
{"title": "LDI-935 - Support Kinesis batching by size (bytes)", "number": 1268, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1268", "body": "For cost optimization purposes, we want to be able to flush our analytics by size (bytes/MB) to fit in AWS's window of a single batch and avoid sending a single/unoptimized batch of events. (The price is the same for a single event or a 1MB batch of events that can hold 100K or more events.)\nRegression - {: data-inline-card='' }\nSmoke - {: data-inline-card='' } \nAPI - {: data-inline-card='' }"}
{"comment": {"body": "1. `i` can be zero and then you we break with `last_index == -1` did you mean to do that?\n2. if you are using `last_index + 1` then assign `last_index = i` and use `last_index`  \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1268/_/diff#comment-383634370"}}
{"comment": {"body": "1. if i is 0 when we reach the breaking condition \u2192 the first element is bigger than the max buffer size and ive covered it in line 111 \\(covered in UT also\\)\n2.  are you referring line 125/126 ? im not understanding your question..\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1268/_/diff#comment-383635097"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} Thanks for info! \n\n* What is the desired behaviour in case of small environment's? like GDE / STG how long it will take to data arrive/ship to DWH? do we have time limit?\n* What is our current average events size? feature, result, error..\n\n\u200c\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1268/_/diff#comment-383638963"}}
{"comment": {"body": "@{5f82bf320756940075db755e} The current implementation still allows batching by buffer length/size, which is backward compatible. However, in a production environment, a time limit should not be used to ensure cost optimization based on batch size \\(please correct me if I'm mistaken\\). When working in a development environment, the desired behavior can either stick to the old mechanism or set the maximum buffer size in bytes plus micro-batch size to match the desired number of events per batch, as shown below. This means that the number of desired feature logs multiplied by the size in bytes should equal the maximum buffer size in bytes, among other considerations.\n\nPlease note that the average size of each log, based on Python memory usage \\(and not the actual \"clean\" size of the event\\), is as follows:\n\n* Feature log: 640 bytes per event on average\n* Result log: 48 bytes per event on average\n* Error log: 640 bytes per event on average\n\nThe amount of time required for the data to reach our data warehouse \\(DWH\\) depends on the level of traffic.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1268/_/diff#comment-383648141"}}
{"comment": {"body": "Thanks! You are right that in scale environment the time is not relevant and size of data is the correct metric for cost effective batching. Can you please add suggested configuration parameters for production and for small environment. The only downside is not testing the same flush mechanism trigger in all environments. Let\u2019s make sure we have UT for all cases", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1268/_/diff#comment-383667954"}}
{"comment": {"body": "@{5f82bf320756940075db755e} We can test the flush mechanism across all our environments by adjusting the buffer size to trigger more frequent flushing. One way to do this is by setting the following parameters:\n\n* `KINESIS_MULTILINE_EVENTS=True`\n* `KINESIS_MICRO_BATCH_SIZE=100000`\n* `KINESIS_FLUSH_BY_SIZE=True`\n* `KINESIS_MAX_BUFFER_SIZE_IN_BYTES=100000`\n\nIn this example, we've set the maximum buffer size to 100kb, which should work well for smaller environments.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1268/_/diff#comment-383763576"}}
{"title": "new askey fps", "number": 1269, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1269", "body": ""}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1418](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1418){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1419](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1419){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1420](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1420){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1269/_/diff#comment-383927277"}}
{"title": "Bugfix/exception in nonrandom device name", "number": 127, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/127", "body": "The nonrandom_device_name feature extraction accesses the parsed data from the dhcp parser, mdns parser and netbios parser. It accessed the data the same way, but the dhcp parser returns a dataclass while the mdns and netbios parsers returned a dictionary.\nThis PR unifies NetBios and MDNS parsers to also return dataclases, so accessing their fields will be done in the same way.\nNote: This was originally intended to be a part of a bigger PR, that changes all of our parsers to return unified dataclasses instead of simple dictionaries, but this exception happened and this change is pushed now. The rest of our parsers will be changed soon."}
{"title": "added missing xb7 l2 finger prints", "number": 1270, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1270", "body": "added missing xb7 l2 finger prints"}
{"comment": {"body": "R - [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1561](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1561){: data-inline-card='' } \n\nS -[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1560](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1560){: data-inline-card='' } \n\nA- [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1559](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1559){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1270/_/diff#comment-385432836"}}
{"title": "LDI-915 pipeline optimization: run unit tests in parallel", "number": 1271, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1271", "body": ""}
{"comment": {"body": "Reducing time around 30% - [https://bitbucket.org/levl/eros-classifier/pipelines/results/12337](https://bitbucket.org/levl/eros-classifier/pipelines/results/12337){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1271/_/diff#comment-385445636"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} I think those @{5dbeb866c424110de52552cc} referred as used in Makefile", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1271/_/diff#comment-387118267"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} this should be applied to release/phase\\_1 as well  \n", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1271/_/diff#comment-392467092"}}
{"title": "bitbucket-pipelines.yml edited online with Bitbucket", "number": 1272, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1272", "body": "bitbucket-pipelines.yml edited online with Bitbucket"}
{"comment": {"body": "this PR should be declined", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1272/_/diff#comment-386436888"}}
{"title": "added missing fps xb7", "number": 1273, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1273", "body": ""}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1980](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1980){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1981](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1981){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1982](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1982){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1273/_/diff#comment-386508336"}}
{"title": "LDI-550 Ridge hostname typing", "number": 1274, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274", "body": "New ML hostname typing v1 April 23 - Hostname typing classification using Ridge model:\n  Input: Hostname\n  output: specific device/ list of devices/ empty list  \nNew ML hostname typing v2 April 23 - Add function converts list of specific_name (devices) into rows in the DataBase.\n\n"}
{"comment": {"body": "why do you have `.sav` file? shouldn\u2019t it need to be `.pkl`?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-387286417"}}
{"comment": {"body": "branch is not sync with dev.  \nprobably will solve some of Ophir\u2019s remarks", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-396855578"}}
{"comment": {"body": "do we want to use pickle?  \ncan\u2019t we implement it otherway?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-396856025"}}
{"comment": {"body": "do we want to use binary file?  \ncan\u2019t we implement it otherway?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-396856499"}}
{"comment": {"body": "@{63e8b1312661cde223366498} This is important, please sync with dev \\(merge dev to your branch\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-397480599"}}
{"comment": {"body": "Merged and pushed ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-397491075"}}
{"comment": {"body": "Consider using list comprehension instead", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-397491090"}}
{"comment": {"body": "After discussion, we decided to keep these formats for now, and once the Sparrow implementation will start, we will define together with automation team the optimal way to implement. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-397492727"}}
{"comment": {"body": "It would be better to define first  \nMODELS\\_PATH = `f\"models/src/{MODELS_META_VERSION}/models/models/hostname_typing/\u201d`  \nand use it in the 3 following definitions \\(or even instead of these 3 while the file names would be written in the load\\_model\\).  \nThis can be also useful for testing - see later comment in load\\_model function,", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-*********"}}
{"comment": {"body": "I suggest to pass the load model also a models\\_path parameter with a default value of \u201cMODELS\\_PATH\u201d.  \nThis can be used by the unit tests to set a path to fixed models in test resources to guarantee you won\u2019t need to fix the tests following models update.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-*********"}}
{"comment": {"body": "I can do that.  \nIs this the formal format in the company?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-*********"}}
{"comment": {"body": "Got it  \nThanks", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-*********"}}
{"comment": {"body": "@{63e8b1312661cde223366498} this is the way it was done in the prior logic.  \nI\u2019m not aware of formal format specifications for models' loading.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-*********"}}
{"comment": {"body": "@{63b6c1aaf3e7004f77ff2273} I see. I will talk with you on Sunday to understand this part better.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-*********"}}
{"comment": {"body": "Is it supposed to be \u201ci pod\u201c or \u201cipod\u201c? \\(with or without space\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-*********"}}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/2613663795-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-404408971"}}
{"comment": {"body": "Doesn\u2019t matter.\n\nIt works in both cases.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1274/_/diff#comment-404413823"}}
{"title": "LDI-981 extend icmp 6 mclr feature to support mac addresses", "number": 1275, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275", "body": "added support for mac addresses in icmp6_mclr feature\nremoved _is_global_addr and everything related\ndecreased the size of the feature by saving only the part with the info\n\n"}
{"comment": {"body": "I don\u2019t think it works anymore for all 3 types", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-387285344"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} @{5ed4f1de9a64eb0c1e78f73b} We are changing here how the features is stored in the DB. Does it effects the backward compatibility of the classifier? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-387288727"}}
{"comment": {"body": "It definitely doesn\u2019t work with `ingestion_event.bin`. Also, many debugging scripts we use don\u2019t work.. we try, if they throw an error we try and fix it. This is a result of frequent change to the format of the log files, API, etc.\n\nThis is how it worked when I needed to reply an event folder ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-387321855"}}
{"comment": {"body": "From what I see, the only real change is in the icmp\\_mclr field, which changes from List to Set. Those types does not break the reading / writing to the db", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-387644525"}}
{"comment": {"body": "FYI It is now list again", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-387652791"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} The values themselves are different \\(we are dropping the prefix of the MCL value\\)\n\n`ff02::1:ffdb:712b\"` \u2192 `\"db:712b\"`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-387653048"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} I added a limit to the list, but we should probably create a proper class that will be used for all the features.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-387656459"}}
{"comment": {"body": "Updated 27.4.2023\n\n[Regression\\_prod:automation:2872-dev-ldi-981-extend-icmp-6-mclr-feax-classifier::LDI-981-extend-icmp-6-mclr-feature-to-support-mac-addresses](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/3277)\n\n[API:automation:30202-dev-ldi-981-extend-icmp-6-mclr-fex-classifier::LDI-981-extend-icmp-6-mclr-feature-to-support-mac-addresses](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/3275)\n\n[Smoke:automation:13562-dev-ldi-981-extend-icmp-6-mclr-fex-classifier::LDI-981-extend-icmp-6-mclr-feature-to-support-mac-addresses](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/3274)\n\n[Acceptance\\_prod:automation:19473-dev-ldi-981-extend-icmp-6-mclr-fex-classifier::LDI-981-extend-icmp-6-mclr-feature-to-support-mac-addresses](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/3276)\n\n1 test did not pass acceptance because it improved:\n\n[https://bitbucket.org/levl/eros-automation/pull-requests/511](https://bitbucket.org/levl/eros-automation/pull-requests/511){: data-inline-card='' }", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-388503873"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} a unit test is needed?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-389124152"}}
{"comment": {"body": "Like", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-389125722"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} no", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1275/_/diff#comment-389128399"}}
{"title": "minor", "number": 1276, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1276", "body": "moving config of black to its config file"}
{"title": "LDI-915 optimize pipeline with docker caching", "number": 1277, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1277", "body": "Pipeline time is reduced to 11-13 mins - {: data-inline-card='' }\nRegression tests:\n{: data-inline-card='' } \n{: data-inline-card='' } \n{: data-inline-card='' }"}
{"title": "LDI-1036 pip requirements validator", "number": 1278, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1278", "body": "adding pip requirements validator, checks for duplicate packages and missing packages between test and regular requirements.txt\n"}
{"title": "reverse LDI_990", "number": 1279, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1279", "body": ""}
{"comment": {"body": "@{63b2a7992c70aae1e6faa958} now it passes:\n\n![](https://bitbucket.org/repo/o5KReBa/images/3481496104-Screenshot%202023-04-20%20at%2016.41.40.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1279/_/diff#comment-388474457"}}
{"title": "Features unitests - Phase 2", "number": 128, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/128", "body": "Enabled the skipped and failed unitests we had :)"}
{"title": "Set ssm log level to debug", "number": 1280, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1280", "body": "scale test deploy\nset ssm log levl to debug sensitive data\n\n"}
{"title": "LDI-972", "number": 1281, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1281", "body": "The identification will rule out devices based on candidates before prior logic was applied.\n\nLDI-972\nLDI-972 fix classification ubit tests\nLDI-972 fix pipeline unit tests\n\n{: data-inline-card='' } \n{: data-inline-card='' } \n{: data-inline-card='' }"}
{"comment": {"body": "Hey :slight_smile: can you please explain a bit more about the issue here, what had to be fixed and why?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1281/_/diff#comment-389012218"}}
{"comment": {"body": "In the process of identification device candidates from the vault are filtered out if their type doesn\u2019t match the type of the connecting device.  \nThe problem was that this filtering was done based on the type of the connecting device, after prior logic possibly excluded some typing candidates. This means that an existing device in the vault db maybe filtered out despite it matches the connecting device.  \nThe new code fixes that by filtering out db devices based on the typing candidates of the connecting device, before prior logic was applied. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1281/_/diff#comment-389013716"}}
{"comment": {"body": "nice!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1281/_/diff#comment-390756997"}}
{"comment": {"body": "@{63b6c1aaf3e7004f77ff2273} Looking good, and the flow is not as complicated as I first thought.\n\nI do want that we will have a confluence page about such algorithmic changes, it doesn\u2019t have to be a long document, just an explanation with a real scenario example and some high level documentation", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1281/_/diff#comment-390836216"}}
{"comment": {"body": "There is a confluence page on the identification process:  \n[https://levltech.atlassian.net/wiki/spaces/IRND/pages/8058044433/System+Identification+Algorithm+-+V1](https://levltech.atlassian.net/wiki/spaces/IRND/pages/8058044433/System+Identification+Algorithm+-+V1){: data-inline-card='' }   \nI\u2019ll update it by tomorrow.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1281/_/diff#comment-390837517"}}
{"comment": {"body": "Thanks!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1281/_/diff#comment-391077333"}}
{"title": "LDI-1065", "number": 1282, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1282", "body": "\n\n"}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} please add description and one pager for the design.\n\n@{637f5c5e3e79f12e572115d7} FYI", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1282/_/diff#comment-389021074"}}
{"comment": {"body": "I pressed \u201cChanges requested\u201d because of the changes in pre-commit config", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1282/_/diff#comment-389028775"}}
{"comment": {"body": "ok\n\ni\u2019ll revert it\n\ni\u2019ll schedule a meeting to discuss these policies - it\u2019s too strict for me", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1282/_/diff#comment-389032144"}}
{"comment": {"body": "Hey @{5fd5d5149edf2800759cc96d} , is this PR still relevant?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1282/_/diff#comment-394795483"}}
{"title": "macOS wispr", "number": 1283, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1283", "body": "macOS wispr"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3571](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3571){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3572](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3572){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3574](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3574){: data-inline-card='' }  \nAP [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3574](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3574){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1283/_/diff#comment-390736686"}}
{"comment": {"body": "approved by @{5f82bf320756940075db755e} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1283/_/diff#comment-390764798"}}
{"title": "modify deb repositories to use from snapshots in debian 9", "number": 1284, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1284", "body": ""}
{"comment": {"body": "Great catch! Why not use same strategy for local docker work?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1284/_/diff#comment-389288958"}}
{"comment": {"body": "Can you please add some info why this happened? We are using tagged docker version..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1284/_/diff#comment-389289122"}}
{"comment": {"body": "It\u2019s not required in local docker work. our images \\(local and registry\\) are based on python:3.10-slim which is based on latest debian however `docker/compose:debian-1.29.2` image is based on old debian \\(debian 9 -stretch\\) which has reached it\u2019s EOL already. Looks like the remote URLs for repositories stopped working they may have removed those repository links which prevented `apt-get update` command to fetch metadata files. Right now, we are pointing to one of the latest snapshots but in long term, we should use an image which is based on latest debian.  \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1284/_/diff#comment-389427508"}}
{"comment": {"body": "Hey @{5f82bf320756940075db755e}   \n@{634e54561db4d2ebcf611e5a} answer in slack channel:  \nReason for failing \u2192 This PR fixes our pipeline which is failing on apt-get update step. Seems like debian has removed repository lists for debian 9 so this temporary fix points to the latest snapshot. A permanent fix would be to upgrade image that uses latest debian but seems like the provider \\(docker\\) hasn't published an image with latest debian so we might have to build our own and find another way but atleast this unblocks the pipeline for now", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1284/_/diff#comment-389427526"}}
{"comment": {"body": "@{5f82bf320756940075db755e} We are using tagged version however the image hasn\u2019t been updated since 2 years and it\u2019s based on older debian which has reached its EOL already. They may have removed the links for repositories so pointing this to latest snapshot for now. For permanent solution, we should use/create an image with latest debian", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1284/_/diff#comment-389428307"}}
{"comment": {"body": "Thanks @{634e54561db4d2ebcf611e5a} @{637f5c5e3e79f12e572115d7} !! ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1284/_/diff#comment-389428592"}}
{"title": "LDI-1066", "number": 1285, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1285", "body": "\n\n"}
{"title": "LDI-1085-reduce-pandas-usage: reduce pandas usage and removing obsolete files", "number": 1286, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1286", "body": "Dont use pd for pandas so they wouldn't use it. It is slow, and not meant for operational code.\nremove some obsolete files\n\n"}
{"comment": {"body": "Can you back up your claim that `pd` is \"slow and not meant for operational code\"?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1286/_/diff#comment-390209085"}}
{"comment": {"body": "```\nfrom collections import defaultdict\nimport pandas as pd\nimport timeit\n\ndef logical_and(row_indices_a: tuple, row_indices_b: tuple) -> tuple:\n    retval = []\n    a = 0\n    b = 0\n    len_a = len(row_indices_a)\n    len_b = len(row_indices_b)\n    while True:\n        if a >= len_a or b >= len_b:\n            break\n        first = row_indices_a[a]\n        second = row_indices_b[b]\n        if first == second:\n            retval.append(first)\n            a += 1\n            b += 1\n        elif first < second:\n            a += 1\n        else:\n            b += 1\n    return tuple(retval)\n\ndef get_row_numbers(db: dict, key_1: str, key_2: str) -> tuple:\n    try:\n        return db[key_1][key_2]\n    except KeyError:\n        return ()\n\n\ndef index_df(df: pd.DataFrame):\n    retval = {}\n    for col in df:\n        col_dict = defaultdict(list)\n        for i, val in enumerate(df[col]):\n            if val != \"\":\n                col_dict[val].append(i)\n        retval[col] = col_dict\n    return retval\n\n\n\nclass DataframeQuery:\n    def __init__(self, data):\n        self.data = data\n\n    def filter(self, key, value):\n        self.data = [x for x in self.data if x[key] == value]\n        return self\n\n    def val(self):\n        return self.data\n\nclass DataframeIndex:\n    def __init__(self, df, lookup_columns = ['identifier', 'internal_name', 'canonical_name']):\n        self.df = df\n        self.index = {}\n        for col in lookup_columns:\n            self.index[col] = defaultdict(list)\n\n        for i in range(len(df)):\n            line = dict(df.iloc[i])\n            for col in lookup_columns:\n                val = line[col]\n                self.index[col][val].append(line)\n\n    def query(self, key, value):\n        return DataframeQuery(self.index[key][value])\n\n\nIDENTIFIER = 'SM-S908E'\n\nif __name__ == '__main__':\n    csv_file = \"/Users/<USER>/work/eros-classifier/models/src/v1/models/models/devices_db/devices_db_tracked.csv\"\n\n    df = pd.read_csv(csv_file, dtype=str)\n\n    index_dict = {}\n    for identifier, identifier_df in df.groupby(['identifier']):\n        index_dict[identifier] = identifier_df\n\n    df = pd.read_csv(csv_file, dtype=str)\n\n    def lookup1():\n        return df.query(f'canonical_name == \"Galaxy S20\" and vendor == \"Samsung\"')\n    print(\"df_query took %.4f\" % timeit.timeit(lookup1, number=1000), \"seconds\")\n\n    df = pd.read_csv(csv_file, dtype=str)\n    my_index = index_df(df)\n    def lookup2():\n        cond1 = get_row_numbers(my_index, 'canonical_name', 'Galaxy S20')\n        cond2 = get_row_numbers(my_index, 'vendor', 'Samsung')\n        return df.iloc[list(logical_and(cond1, cond2))]\n\n    print(\"indexed_df took %.4f\" % timeit.timeit(lookup2, number=1000), \"seconds\")\n\n    my_index = DataframeIndex(df)\n    def lookup3():\n        return my_index.query('canonical_name', \"Galaxy S20\").filter('vendor', 'Samsung').val()\n\n    print(\"not_using_df took %.4f\" % timeit.timeit(lookup3, number=1000), \"seconds\")\n\n    df = pd.read_csv(csv_file, dtype={'identifier':'category', 'canonical_name':'category', 'internal_name':'category', 'vendor':'category'}, low_memory=False)\n    def lookup4():\n        return df[(df.canonical_name == \"Galaxy S20\") & (df.vendor == \"Samsung\")]\n\n    print(\"pandas_index took %.4f\" % timeit.timeit(lookup4, number=1000), \"seconds\")\n```\n\nresults:  \n\n```\ndf_query took 2.4006 seconds\nindexed_df took 0.3483 seconds\nnot_using_df took 0.0029 seconds\npandas_index took 0.4775 seconds\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1286/_/diff#comment-390216042"}}
{"comment": {"body": "and another one:  \n\n```\nimport timeit\nimport pandas as pd\n\n\ndef using_pandas_nunique(df):\n    return df[\"A\"].nunique() == 1\n\n\ndef using_set(df):\n    return len(set(df[\"A\"])) == 1\n\n\nif __name__ == '__main__':\n    df1 = pd.DataFrame({\"A\": [1, 2, 3, 1, 1, 2, 3, 1, 1, 3, 4]})\n\n    def using_pandas_nunique1():\n        using_pandas_nunique(df1)\n\n    def using_set1():\n        using_set(df1)\n\n    print(\"example 1\")\n    print(\"=========\")\n    print(\"results are same? : \", \"Yes\" if using_pandas_nunique(df1) == using_set(df1) else \"No\")\n    print(\"result is\", using_pandas_nunique(df1))\n    print(\"using pandas nunique took %.4f\" % timeit.timeit(using_pandas_nunique1, number=100000), \"seconds\")\n    print(\"using set took %.4f\" % timeit.timeit(using_set1, number=100000), \"seconds\")\n\n    df2 = pd.DataFrame({\"A\": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]})\n\n    def using_pandas_nunique2():\n        using_pandas_nunique(df2)\n\n    def using_set2():\n        using_set(df2)\n\n\n    print()\n    print(\"example 2\")\n    print(\"=========\")\n    print(\"results are same? : \", \"Yes\" if using_pandas_nunique(df2) == using_set(df2) else \"No\")\n    print(\"result is\", using_pandas_nunique(df2))\n    print(\"using pandas nunique took %.4f\" % timeit.timeit(using_pandas_nunique2, number=100000), \"seconds\")\n    print(\"using set took %.4f\" % timeit.timeit(using_set2, number=100000), \"seconds\")\n\n    df3 = pd.DataFrame({\"A\": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0]})\n\n    def using_pandas_nunique3():\n        using_pandas_nunique(df3)\n\n    def using_set3():\n        using_set(df3)\n\n    print()\n    print(\"example 3\")\n    print(\"=========\")\n    print(\"results are same? : \", \"Yes\" if using_pandas_nunique(df3) == using_set(df3) else \"No\")\n    print(\"result is\", using_pandas_nunique(df3))\n    print(\"using pandas nunique took %.4f\" % timeit.timeit(using_pandas_nunique3, number=100000), \"seconds\")\n    print(\"using set took %.4f\" % timeit.timeit(using_set3, number=100000), \"seconds\")\n```\n\nresults:  \n\n```\nexample 1\n=========\nresults are same? :  Yes\nresult is False\nusing pandas nunique took 2.4200 seconds\nusing set took 0.4043 seconds\n\nexample 2\n=========\nresults are same? :  Yes\nresult is True\nusing pandas nunique took 2.3208 seconds\nusing set took 0.3997 seconds\n\nexample 3\n=========\nresults are same? :  Yes\nresult is False\nusing pandas nunique took 2.4200 seconds\nusing set took 0.4203 seconds\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1286/_/diff#comment-390742867"}}
{"title": "Merged in fix-pipeline (pull request #1284)", "number": 1287, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1287", "body": "modify deb repositories to use from snapshots in debian 9\n\nmodify deb repositories to use from snapshots in debian 9\nfix kafka container version\n\nApproved-by: Shimon Goulkarov Approved-by: Yoav Rapaport Approved-by: shakedAhronoviz"}
{"title": "LDI-821 implementation of mac randomization behavior logic", "number": 1288, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1288", "body": "LDI-821 added filtering based on mac-r behavior\nLDI-821 fixed reject function & added test\n\n"}
{"title": "LDI-744 Updating l2 model with new askey recordings", "number": 1289, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1289", "body": ""}
{"comment": {"body": "Can we please re-check why the diff between the files is so big? Is it intentional? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1289/_/diff#comment-390743614"}}
{"comment": {"body": "yes, i am fixing it now", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1289/_/diff#comment-390743987"}}
{"comment": {"body": "pz look now@{5ed4f1de9a64eb0c1e78f73b} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1289/_/diff#comment-390745511"}}
{"comment": {"body": ":slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1289/_/diff#comment-390745642"}}
{"comment": {"body": "R[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3644](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3644){: data-inline-card='' } \n\nA[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3643](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3643){: data-inline-card='' } \n\nAPI[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3642](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3642){: data-inline-card='' } \n\nSmoke[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3641](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3641){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1289/_/diff#comment-390765240"}}
{"title": "Bugfix/wps uid extraction", "number": 129, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/129", "body": "Now extracting the wps_uid feature instead of returning the entire wps uid struct"}
{"title": "add / change metrics", "number": 1290, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1290", "body": ""}
{"comment": {"body": "what is the purpose of this class?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1290/_/diff#comment-390866769"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} @{637f5c5e3e79f12e572115d7} can you split this PR, putting in separate the consumed vaults metrics implementation? Metrics have to be correct and once we have cache, we might get situations when those consumed vaults numbers won\u2019t be accurate. \n\n@{637f5c5e3e79f12e572115d7} @{5fd5d5149edf2800759cc96d} FYI", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1290/_/diff#comment-391050274"}}
{"title": "LDI-1105: unit tests for bonjour_uid.mac_vendor_csv_to_apple_macs", "number": 1291, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1291", "body": ""}
{"title": "wispr_16_5", "number": 1292, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1292", "body": ""}
{"comment": {"body": " S[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3878](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3878){: data-inline-card='' } \n\nAPI[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3879](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3879){: data-inline-card='' } \n\nA [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3880](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3880){: data-inline-card='' } \n\nR[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3881](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3881){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1292/_/diff#comment-390866875"}}
{"title": "LDI-1105 ut", "number": 1293, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1293", "body": "unit tests and usage of classifier strings\n\n"}
{"title": "delete rows without model from askey L2", "number": 1294, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1294", "body": ""}
{"comment": {"body": "API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4160](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4160){: data-inline-card='' } \n\nS[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4161](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4161){: data-inline-card='' } \n\nR[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4162](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4162){: data-inline-card='' } \n\nA[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4163/980029](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4163/980029){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1294/_/diff#comment-390940541"}}
{"title": "LDI-1105 ut", "number": 1295, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1295", "body": "use classifier_strings.py to hold the names of all environment variables.\nadded validator for enforcement\n\n"}
{"title": "glinet data vodafone", "number": 1296, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1296", "body": "l2 data for glinet"}
{"comment": {"body": "branch to branch\n\nA [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4674](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4674){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4675](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4675){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4677](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4677){: data-inline-card='' }   \nAP [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4676](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4676){: data-inline-card='' }", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1296/_/diff#comment-391230191"}}
{"comment": {"body": "need to merge [https://bitbucket.org/levl/eros-automation/pull-requests/514](https://bitbucket.org/levl/eros-automation/pull-requests/514){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1296/_/diff#comment-391236247"}}
{"title": "LDI-1085 reduce pandas usage 2", "number": 1297, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1297", "body": "remove obsolete files\n\n"}
{"title": "LDI-821 fixed mac_randomization_policy_db to use classifier_strings.py", "number": 1298, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1298", "body": "This pipeline fails: {: data-inline-card='' } \nThis PR fixes the issue"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/3120692783-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1298/_/diff#comment-392475960"}}
{"title": "LDI-915 pipeline optimizations", "number": 1299, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1299", "body": "LDI-915 pipeline optimization: run unit tests in parallel\n\nrun unit tests in parallel\nMerged in LDI-915-docker-cache (pull request #1277)\n\nLDI-915 optimize pipeline with docker caching\n\nremove hardcoded tag\noptimize pipeline with docker caching\n\nApproved-by: Yoav Rapaport * LDI-915 update convention\nApproved-by: Shimon Goulkarov Approved-by: Yoav Rapaport"}
{"title": "Features leftover", "number": 13, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/13", "body": "Fix interface\nAdd logs\n\n"}
{"title": "Feature/unitests - Phase 3", "number": 130, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/130", "body": "Added more tests to our matching tests - Android, Windows, Apple and Linux OS."}
{"title": "LDI-1115 missing data glinet2", "number": 1300, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1300", "body": "data for L2"}
{"title": "Revert \"Merged in LDI-915 (pull request #1271)\"", "number": 1301, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1301", "body": "This reverts commit 234cf17eb239a5cf67e8852ff945751eb90b4678."}
{"title": "LDI-1125 remove L1 code", "number": 1302, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1302", "body": "remove all L1 code, cfo, cfr and slopes\n\n"}
{"comment": {"body": "Please tag the commit prior to removal for ease of retrieval and comment it in the ticket.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1302/_/diff#comment-394319250"}}
{"comment": {"body": "done: tag name `before-removing-L1-code`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1302/_/diff#comment-394323184"}}
{"title": "LDI-1115 devices db for vodafone", "number": 1303, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1303", "body": "LDI-1115 update devices db full for vodafone due to missing new devices\n"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5733](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5733){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5732](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5732){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5736](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5736){: data-inline-card='' }   \nAP [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5737](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5737){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1303/_/diff#comment-393594504"}}
{"title": "LDI-600 added probe support", "number": 1304, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1304", "body": ""}
{"comment": {"body": "Please also approve [https://bitbucket.org/levl/eros-automation/pull-requests/516](https://bitbucket.org/levl/eros-automation/pull-requests/516){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1304/_/diff#comment-392917305"}}
{"title": "LDI-600 added probe support", "number": 1305, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1305", "body": "Changed the name of the branch because it was too long\nPlease also approve {: data-inline-card='' }"}
{"comment": {"body": "Same branch name regression\n\n![](https://bitbucket.org/repo/o5KReBa/images/1232831531-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1305/_/diff#comment-393622154"}}
{"title": "LDI-1117: updated eros-collector-interfaces version", "number": 1306, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1306", "body": ""}
{"comment": {"body": "@{6265307b185ac200692f9bd9} automation repo also should be bumped to this version", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1306/_/diff#comment-394319138"}}
{"comment": {"body": "done: [https://bitbucket.org/levl/eros-automation/pull-requests/518](https://bitbucket.org/levl/eros-automation/pull-requests/518){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1306/_/diff#comment-394321939"}}
{"title": "LDI-1135 use docker image for docker-compose", "number": 1307, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1307", "body": ""}
{"title": "LDI-1135: fix pipeline,skip tests", "number": 1308, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1308", "body": ""}
{"title": "LDI-1136: reduce complexity", "number": 1309, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1309", "body": "break function into smaller functions\nreduce indent in for loop by using continue\n\n"}
{"title": "Removed report portal until its fixed", "number": 131, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/131", "body": ""}
{"title": "Feature/merges from release phase 1", "number": 1310, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1310", "body": "Merge of release/phase_1 branch latest to dev branch"}
{"comment": {"body": "I pressed the \u201cChanges requested\u201d button because of the change in `file_validators/main.py`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1310/_/diff#comment-393635109"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} It\u2019s in purpose.. we didn\u2019t agree on this enforcement. We will remove it for now\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1310/_/diff#comment-393646422"}}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/3385946939-Screenshot%202023-05-11%20at%2012.24.07.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1310/_/diff#comment-393646552"}}
{"comment": {"body": "every value that appears more than once should be in a variable. it\u2019s basics. it can create bugs otherwise.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1310/_/diff#comment-393661223"}}
{"title": "LDI-1066: add configuration for filtering kinesis records", "number": 1311, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1311", "body": "\n\nREGRESSION: \nAPI: \nSMOKE: "}
{"comment": {"body": "same", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1311/_/diff#comment-394316590"}}
{"comment": {"body": "nice!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1311/_/diff#comment-394316705"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} can you please share a confluence one pager describing the design?\n\nAlso, there is ongoing configuration documentation, let\u2019s make sure it\u2019s added there. \n\n@{637f5c5e3e79f12e572115d7} FYI", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1311/_/diff#comment-394319044"}}
{"comment": {"body": "[https://levltech.atlassian.net/wiki/spaces/TS/pages/8421376012/Export+to+data+warehouse+filtering](https://levltech.atlassian.net/wiki/spaces/TS/pages/8421376012/Export+to+data+warehouse+filtering){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1311/_/diff#comment-394815334"}}
{"comment": {"body": "helm charts?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1311/_/diff#comment-395225310"}}
{"title": "LDI-1148 fix model update automation pipeline", "number": 1312, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1312", "body": ""}
{"title": "Release/phase 1", "number": 1313, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1313", "body": "Merged in set_mdns_info_category_to_model (pull request #1226)\nSet mdns info category to model\n\n[skip ci] bumped eros-chart new version 0.87.0, build 12112\nSet MDNS_INFO feature's category to MODEL\nMerged dev into set_mdns_info_category_to_model\n\nApproved-by: Ariel Tohar Approved-by: Ophir Carmi\n\n\nMerged in set_cujo_regression_pipeline_in_release_phase_1 (pull request #1232)\nSet cujo regression pipeline\n\nSet cujo regression pipeline\n\nApproved-by: Shimon Goulkarov Approved-by: Ron Cohen\n\n\nupdate rp_uuid\n\n\nMerged in LDI-849_release-p1 (pull request #1241)\nLDI-849\n\ncherry pick for release branch from LDI-849\ncherry pick for release branch from LDI-849\nreport metircs client flushing latency\n\nApproved-by: Shimon Goulkarov Approved-by: Daniel Katz\n\n\nupdate charter-dev pipeline\n\n\nMerged in LDI-940 (pull request #1250)\nadd report-portal client fixed version\n\nadd report-portal client fixed version\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov\n\n\nMerged in LDI-932 (pull request #1256)\ndrop features with 'None' vaule field\n\ndrop features with 'None' vaule field\nadd ut\nchange regression in release branch to his eq in automation repo\n\nApproved-by: Tamir Raz Approved-by: Ariel Tohar Approved-by: Shimon Goulkarov Approved-by: Yoav Rapaport Approved-by: Daniel Katz\n\n\nMerged in bugfix/LDI-934-missing-mdns-hosname-datacatalog (pull request #1264)\nLDI-934 Now filling the mdns_hostname value in the data_catalog\n\nLDI-934 Now filling the mdns_hostname value in the data_catalog\nMerge branch 'release/phase_1' into bugfix/LDI-934-missing-mdns-hosname-datacatalog\n\nApproved-by: Shimon Goulkarov Approved-by: Dror Bendet\n\n\nMerged in LDI-935 (pull request #1268)\nLDI-935 - Support Kinesis batching by size (bytes)\n\nsupport flushing to kinesis by size (MB), WIP\nsupport flushing to kinesis by size (MB)\nfixing the way shuold_flush sizing the buffer\nfix typing\nfix typing\nrestore default env var values\n\nApproved-by: Shimon Goulkarov Approved-by: Yoav Rapaport Approved-by: Daniel Katz Approved-by: Itai Zolberg\n\n\n[skip ci] bumped eros_classes_api package version, build 12314, new version 0.70.3\n\nset ssm log levl to debug sensitive data\nLDI-1048: fix test codeconv version due to their pypi issues \n\nMerged in fix-pipeline-release (pull request #1287)\nMerged in fix-pipeline (pull request #1284)\n\nMerged in fix-pipeline (pull request #1284)\n\nmodify deb repositories to use from snapshots in debian 9\n\nmodify deb repositories to use from snapshots in debian 9\nfix kafka container version\n\nApproved-by: Shimon Goulkarov Approved-by: Yoav Rapaport Approved-by: shakedAhronoviz\nApproved-by: Shimon Goulkarov Approved-by: Yoav Rapaport\n\n\nLDI-1135: fix pipeline,skip tests\n\nLDI-1135: fix pipeline,skip tests\n\n"}
{"title": "LDI-1142 Extract vendor information from L2 data", "number": 1314, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314", "body": "Created following {: data-inline-card='' } \nThe purpose is to extract vendor information from L2 data - for Apple, Samsung, and Huawei devices.\nThis will improve resolution in some cases and will prevent miss detection in others."}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/3448246823-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-394337278"}}
{"comment": {"body": "Can you please add a short description about the goal of this task? :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-394338526"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-394345487"}}
{"comment": {"body": "By the way, nothing was changed in the regression?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-394349038"}}
{"comment": {"body": "Nothing changed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-394443766"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} Another thing to consider - would a having a separate feature for the L2 OUI detection might be the better approach?\n\nIn this way we can put a different confidence score for the two features, which would allow us to give different scores and more granularity in our overall typing algorithm.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-394482289"}}
{"comment": {"body": "It is a lot of overhead for a small addition with the same use as the mac\\_vendor class.  \nI will try to separate the features with inheritance.  \n  \nBTW, we should also separate features for each hostname source, MDNS, NetBIOS, and DHCP instead of one HostnameTyping feature, they have different properties.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-394490860"}}
{"comment": {"body": "Let\u2019s put this as an improvement to work on", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-396263388"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} what is the status here?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-396263505"}}
{"comment": {"body": "It was set aside because of LDI-1129 but it is in testing now.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-396280016"}}
{"comment": {"body": "nice!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1314/_/diff#comment-396833303"}}
{"title": "LDI-978: Fixing and adding more metrics", "number": 1315, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1315", "body": "Fix existing metrics and add new ones for better visibility.\nTweak some environment variables.\nAdjust unit tests for the new metrics.\n\n"}
{"comment": {"body": "I\u2019m not sure about this recursive approach. Maybe we can do something more similar to this? Maybe we can talk about this offline\n\n```\nfor k, v in device_record.items():\n    size += len(v)\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1315/_/diff#comment-394441694"}}
{"comment": {"body": "It\u2019ll work only if all the values were string, we can ignore the case that one of the dict values are dict and drop the recursive approach for now.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1315/_/diff#comment-394444431"}}
{"title": "LDI-1147 bonjour id typing", "number": 1316, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1316", "body": "This adds support for typing based on the inferred vendor OUI in the bonjour id.\nThe model was created by @{712020:4071c37d-2923-418c-a90e-e4a163756bee} using cujo data. In the future it should become an automated pipeline."}
{"comment": {"body": "Regression passed against same branch name:\n\nAcceptance: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6407?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6407?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6406?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6406?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6405?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6405?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6404?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6404?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1316/_/diff#comment-394518149"}}
{"title": "LDI-1115 fixed bad fp", "number": 1317, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1317", "body": "fixed bad fp"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6650](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6650){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6651](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6651){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6652](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6652){: data-inline-card='' }   \nAP [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6653](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6653){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1317/_/diff#comment-394576209"}}
{"title": "LDI-1171: add flake8-boolean-trap", "number": 1318, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1318", "body": ""}
{"title": "LDI-1105: more unit tests", "number": 1319, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1319", "body": ""}
{"title": "Feature/unitests - Phase 4", "number": 132, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/132", "body": "Added full session parsing unitests from real devices - Windows, Linux, Apple, Android."}
{"title": "LDI-1173 allow for identifier that starts with a digit in some cases", "number": 1320, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1320", "body": "Allow for identifier that starts with a digit if it is also 8+ characters long and has a letter somewhere in it, to increase efficacy for those devices."}
{"comment": {"body": "Acceptance [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6870?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6870?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\nRegression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6869?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6869?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\nAPI [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6868?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6868?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6867?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6867?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1320/_/diff#comment-394807309"}}
{"title": "LDI-1136: reducing complexity", "number": 1321, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1321", "body": ""}
{"title": "LDI-1177: more return value typing hints", "number": 1322, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1322", "body": ""}
{"title": "LDI-1177: more typing hints for return value of functions", "number": 1323, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1323", "body": ""}
{"title": "LDI-1125 remove L1 code", "number": 1324, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1324", "body": "LDI-1125: remove leftovers of l1 code\n\n\n"}
{"title": "LDI-1177", "number": 1325, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1325", "body": "update models latest version\nLDI-794: add to hel charts and bitbucket pipelines for cujo-stage\n\nMerged in set_mdns_info_category_to_model (pull request #1226)\nSet mdns info category to model\n\n[skip ci] bumped eros-chart new version 0.87.0, build 12112\nSet MDNS_INFO feature's category to MODEL\nMerged dev into set_mdns_info_category_to_model\n\nApproved-by: Ariel Tohar Approved-by: Ophir Carmi\n\n\nMerged in set_cujo_regression_pipeline_in_release_phase_1 (pull request #1232)\nSet cujo regression pipeline\n\nSet cujo regression pipeline\n\nApproved-by: Shimon Goulkarov Approved-by: Ron Cohen\n\n\nupdate rp_uuid\n\nupdate rp_uuid\n\nMerged in LDI-849_release-p1 (pull request #1241)\nLDI-849\n\ncherry pick for release branch from LDI-849\ncherry pick for release branch from LDI-849\nreport metircs client flushing latency\n\nApproved-by: Shimon Goulkarov Approved-by: Daniel Katz\n\n\nupdate charter-dev pipeline\n\n\nMerged in LDI-940 (pull request #1250)\nadd report-portal client fixed version\n\nadd report-portal client fixed version\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov\n\n\nMerged in LDI-932 (pull request #1256)\ndrop features with 'None' vaule field\n\ndrop features with 'None' vaule field\nadd ut\nchange regression in release branch to his eq in automation repo\n\nApproved-by: Tamir Raz Approved-by: Ariel Tohar Approved-by: Shimon Goulkarov Approved-by: Yoav Rapaport Approved-by: Daniel Katz\n\n\nMerged in bugfix/LDI-934-missing-mdns-hosname-datacatalog (pull request #1264)\nLDI-934 Now filling the mdns_hostname value in the data_catalog\n\nLDI-934 Now filling the mdns_hostname value in the data_catalog\nMerge branch 'release/phase_1' into bugfix/LDI-934-missing-mdns-hosname-datacatalog\n\nApproved-by: Shimon Goulkarov Approved-by: Dror Bendet\n\n\nMerged in LDI-935 (pull request #1268)\nLDI-935 - Support Kinesis batching by size (bytes)\n\nsupport flushing to kinesis by size (MB), WIP\nsupport flushing to kinesis by size (MB)\nfixing the way shuold_flush sizing the buffer\nfix typing\nfix typing\nrestore default env var values\n\nApproved-by: Shimon Goulkarov Approved-by: Yoav Rapaport Approved-by: Daniel Katz Approved-by: Itai Zolberg\n\n\n[skip ci] bumped eros_classes_api package version, build 12314, new version 0.70.3\n\nset ssm log levl to debug sensitive data\nLDI-1048: fix test codeconv version due to their pypi issues \n\nMerged in fix-pipeline-release (pull request #1287)\nMerged in fix-pipeline (pull request #1284)\n\nMerged in fix-pipeline (pull request #1284)\n\nmodify deb repositories to use from snapshots in debian 9\n\nmodify deb repositories to use from snapshots in debian 9\nfix kafka container version\n\nApproved-by: Shimon Goulkarov Approved-by: Yoav Rapaport Approved-by: shakedAhronoviz\nApproved-by: Shimon Goulkarov Approved-by: Yoav Rapaport\n\n\nLDI-1135: fix pipeline,skip tests\n\nLDI-1135: fix pipeline,skip tests\nLDI-1135: revert release hardcoded in pipelines\nLDI-1135: fix strings import\nLDI-1177-return-typing-hints-2 nump the models folder\n\n"}
{"comment": {"body": "Jira issue is the wrong one", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1325/_/diff#comment-395123157"}}
{"title": "LDI-1182: matchmaker: go over only enabled features", "number": 1326, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1326", "body": ""}
{"title": "Feature/LDI-1159 remove old schema transformation", "number": 1327, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1327", "body": "older (M1 events) events are being saved to pcap files.\nthese files are no longer used. removed the save operation for old events during Event message conversion \n"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/196463467-Screenshot%202023-05-21%20at%2013.12.56.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1327/_/diff#comment-396264670"}}
{"title": "LDI-1180: add send-message-to-slack step", "number": 1328, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1328", "body": ""}
{"title": "LDI-1189 fix of wisper of macos13", "number": 1329, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1329", "body": ""}
{"comment": {"body": "R [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/7693](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/7693){: data-inline-card='' } \n\nA[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/7692](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/7692){: data-inline-card='' } \n\nAPI [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/7691](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/7691){: data-inline-card='' } \n\nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/7690](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/7690){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1329/_/diff#comment-395266140"}}
{"title": "Added types hinting where possible in several important places in our classification process", "number": 133, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/133", "body": "Nothing major, just small improvements :)"}
{"title": "Bugfix/LDI-1158 missing app.log file", "number": 1330, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1330", "body": "Remove fileHandlers from logger when (Stream) pipeline operator fails.\nThis is to fix the app.log file handlers not being closed on exceptions during the pipeline operators\n"}
{"comment": {"body": "@{712020:53b4b0dd-2aa8-4b28-a065-b5a63a5b390b} there was a bug which the app log was removed, please double check with @{5dbeb866c424110de52552cc} to make sure it won\u2019t slip back..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1330/_/diff#comment-395469927"}}
{"comment": {"body": "This task was exactly for that.  \nsolve the bug and make sure the application logs return  ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1330/_/diff#comment-395470388"}}
{"comment": {"body": "Has this been tested yet?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1330/_/diff#comment-395526926"}}
{"comment": {"body": "Yes. Manual tests.  \nflow was.  \n1\\. added an exception to a map method from event\\_driven\\_pipeline.  \n2\\. ran unit tests with \u2018break\u2019 in code.  \n3\\. validated app.logs are not closed.  \n4\\. rebased with my fix, performed the same test and validated that open file handlers are closed.  \n  \nwill add a unit-test for it and submit a new revision", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1330/_/diff#comment-395527906"}}
{"comment": {"body": "Thanks.  \nDid you check the completeness of the log file \\(in the happy path\\)?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1330/_/diff#comment-395530457"}}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/2907699952-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1330/_/diff#comment-398967368"}}
{"comment": {"body": "We will address UT in a separate task [https://levltech.atlassian.net/browse/LDI-1351](https://levltech.atlassian.net/browse/LDI-1351){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1330/_/diff#comment-398969220"}}
{"title": "Bugfix/LDI-1118", "number": 1331, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1331", "body": "Event coming from cujo can sometime have a DI message of unknown type (for us)\nWe need to be to ignore DI messages from unknown types when trying to decode DI messages.\n"}
{"comment": {"body": "Please update the title to be more descriptive :pray: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1331/_/diff#comment-397092528"}}
{"comment": {"body": "Which of the PRs is the relevant one? Please remove the 2nd one", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1331/_/diff#comment-397092760"}}
{"title": "LDI-1180 slack channel for model updates", "number": 1332, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1332", "body": "Jira: {: data-inline-card='' } \nName of slack channel: lndi-pipelines-notifications\nslack channel type: private\nNotification message:\n\n"}
{"comment": {"body": "Why is the slack channel private? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1332/_/diff#comment-395330775"}}
{"comment": {"body": "Since it\u2019s not relevant to most Levl workers, only those who\u2019re relevant to LNDI pipelines", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1332/_/diff#comment-395454723"}}
{"comment": {"body": "Analysts & Shimon & Mich are also relevant \\(maybe Tamir and Ariel as well\\).  \nit\u2019s not only for the LNDI pipelines, it\u2019s also for the models and knowing the status and changes over the envs of the classifier", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1332/_/diff#comment-395457443"}}
{"title": "LDI-1065 - Dynamodb write thorugh cache", "number": 1333, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1333", "body": "\nDescription\nimplement a DynamoDB write through cache with the ability to store and retrieve items from the cache or the backend table in order to improve the performance and reduce the number of read requests to the backend table.\nThe user story requires the implementation of a class called DynamoDBCacheTable with the following interface\n\nget_partition method: This method retrieves the partition from the cache or the backend table based on the provided key. If the partition is not present in the cache or forced to be retrieved from the backend, then it retrieves the partition from the backend table and updates the cache.\nput_item method: This method stores the provided item in the cache and the backend table.\ndelete_item method: This method deletes the provided item from the cache and the backend table.\nget_item method: This method retrieves the item from the cache or the backend table based on the provided key and sort_key. If the item is present in the cache and not expired, then it returns the item. Otherwise, it retrieves the item from the backend table and updates the cache.\nclean_cache method: This method cleans the cache of expired items.\n\nthe cache should report the following metrics:\n\ndynamodb_cache_size: A Gauge metric that tracks the size of the cache in bytes.\ndynamodb_cache_hits: A Counter metric that tracks the number of cache hits.\ndynamodb_cache_evictions: A Counter metric that tracks the number of cache evictions.\ndynamodb_cache_backend_read_partition: A Counter metric that tracks the number of times a partition is read from the backend table.\ndynamodb_cache_backend_read_item: A Counter metric that tracks the number of times an item is read from the backend table.\ndynamodb_cache_backend_write_item: A Counter metric that tracks the number of times an item is written to the backend table.\ndynamodb_cache_backend_partition_size: A Histogram metric that tracks the size of a partition in bytes.\ndynamodb_cache_backend_item_size: A Histogram metric that tracks the size of an item in bytes.\ndynamodb_cache_items_per_partition_count: A Histogram metric that tracks the number of items per partition in the cache.\n\n"}
{"title": "LDI-1118 remove unknown DI messages", "number": 1334, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1334", "body": "when trying to decode DI messages, ignore and remove all messages of unknown type\n(currently last known type is 21)\n\n"}
{"comment": {"body": "why do we need to run all the events? and not filter one by one once handled? it\u2019s unnecessary processing?\n\nand why it\u2019s relevant to save debug? what happened if it\u2019s disabled?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1334/_/diff#comment-396265053"}}
{"comment": {"body": "this function is called only when the conversion from an ingestion event python object to a python dict fails.  \nThis will fail if we get an unknown DI message, i.e enum larger than what we support \\(example [https://bitbucket.org/levl/eros-data-collector/src/0eafd8721ff33bcdf43ac3d3f6edd3f5762d027d/api/src/collector_api/schemas/di_message.proto#lines-27](https://bitbucket.org/levl/eros-data-collector/src/0eafd8721ff33bcdf43ac3d3f6edd3f5762d027d/api/src/collector_api/schemas/di_message.proto#lines-27){: data-inline-card='' } \\)\n\n\u200c\n\nThis will be enabled only when save debug data \\(EROS\\_DEBUG\\) is true.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1334/_/diff#comment-396265750"}}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/2783698118-Screenshot%202023-05-25%20at%2012.49.04.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1334/_/diff#comment-397546046"}}
{"title": "LDI-1129 remove platform type from l2 model", "number": 1335, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1335", "body": "LDI-1129 removed platform info from inference\nLDI-1129 fixed test\n\n"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9410](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9410){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9409](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9409){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9417](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9417){: data-inline-card='' }   \nAP [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9414](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9414){: data-inline-card='' } \n\n![](https://bitbucket.org/repo/o5KReBa/images/1362173225-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1335/_/diff#comment-396271572"}}
{"comment": {"body": "Passed local regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1335/_/diff#comment-396271998"}}
{"comment": {"body": "merge this PR after merging [https://bitbucket.org/levl/eros-classifier/pull-requests/1336](https://bitbucket.org/levl/eros-classifier/pull-requests/1336){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1335/_/diff#comment-396275193"}}
{"title": "LDI-990 new L2 fps", "number": 1336, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1336", "body": "update l2 model"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9170](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9170){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9169](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9169){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9171](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9171){: data-inline-card='' }   \nAP [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9172](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9172){: data-inline-card='' }\n\n![](https://bitbucket.org/repo/o5KReBa/images/3464408331-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1336/_/diff#comment-396273510"}}
{"title": "LDI-1224: fix vscode local env", "number": 1337, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1337", "body": ""}
{"title": "Bugfix/LDI-1215 models os identified to low res", "number": 1338, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1338", "body": "LDI-1215/ mdns_rpvr.csv edited online with Bitbucket - add new rpvr fingerprint\nLDI-1215/ added 2 l2 fingerprints for apple watch series 3 to l2_model_mp.csv\n\n"}
{"comment": {"body": "attached 4 reports:\n\nRegression:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/9488](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/9488){: data-inline-card='' } \n\nAcceptance:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9487](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9487){: data-inline-card='' } \n\nAPI:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9486](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9486){: data-inline-card='' } \n\nSmoke:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9485](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9485){: data-inline-card='' } \n\nFYI\n\n@{640494c293cf2599462f47b8} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1338/_/diff#comment-396501007"}}
{"title": "LDI-1201 adding platform_type_mapping model", "number": 1339, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1339", "body": "lDI-1201adding new platform_type_mapping model"}
{"title": "Filter features by OS", "number": 134, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/134", "body": "Now using the features only when they are relevant, depend on the os.\nIn addition, when adding this component, I found two tests that were logically wrong so they had to be removed."}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} it should be filter by OS :wink:", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/134/_/diff#comment-245855425"}}
{"comment": {"body": "As a general design note, I think we should approach this in a more systematic way. Meaning, have the mapping of \u201cOS \u2192 enabled features\u201d be defined in some seperate configuration, and a generic code to use that mapping here. Or else we\u2019ll end up with a pilot-like code base.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/134/_/diff#comment-245935622"}}
{"title": "LDI-1199 schema translation", "number": 1340, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1340", "body": "LDI-1199: support LEVL_LAB\nLDI-1199: advance eros-collector-interfaces version\n\n\n"}
{"comment": {"body": "Are there any other occurences of `ACTIVATE_SCHEMA_TRANSLATION` that were not removed? bitbucket\\_pipelines.yaml, mapconfig, values.yaml?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1340/_/diff#comment-396850475"}}
{"comment": {"body": "no", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1340/_/diff#comment-396851720"}}
{"comment": {"body": "Can we / should we move this to `process_event_msg`?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1340/_/diff#comment-398378420"}}
{"title": "LDI-1255 adding apple watch l2 fps", "number": 1341, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1341", "body": ""}
{"comment": {"body": "Acceptence: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10079](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10079){: data-inline-card='' } \n\nRegression:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10078](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10078){: data-inline-card='' } \n\nSmoke:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10077](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10077){: data-inline-card='' } \n\nApi:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10076](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10076){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1341/_/diff#comment-396869014"}}
{"title": "LDI-978 rebase dev", "number": 1342, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1342", "body": "Rebase release branch and dev branch"}
{"comment": {"body": "remove. the failure is a real bug.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1342/_/diff#comment-397131643"}}
{"comment": {"body": "I\u2019ll open separate ticket for this bug, we cant be blocked by this right now.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1342/_/diff#comment-397225811"}}
{"title": "Bugfix/LDI-1216 Fix band swap in L2 fingerpring", "number": 1343, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1343", "body": "Problem\nApple watch devices fingerprints were received as if on 5G network when they only support 2.4G\nwe noticed that when deciding on the correct band supported_rates were used instead of supported channels\nSolution\n\nfix the band decision to use supported_channels\nSince model building relied on wrong band conclusion logic, we duplicated fingerpringts for some models to work on both bands such that they will match both the old and the new lookup.\n\nTesting\nadded unit test"}
{"title": "Feature/LDI-1161 DWH to contain the CUJO agent_id identifier", "number": 1344, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1344", "body": "Adding in result log, feature log, and error log extraction of agent_id (on all) and agent_version (result and feature) for debbug and system analysis on the DWH side.\n\nLDI-1264: add agent_id and agent_version on the relevant schema\nLDI-1265: support export of the agent_id and agent_veraion on result_log and feature_log\nLDI-1265: fix datacatalog metadata and UT\nLDI-1265: revert version.yaml from bad merges\n\n\n\nMissing: \n\nsparrow and terraform integration to add in the kinesis and Athena\nIntegration with Cujo LENS (only agent_id requires obfuscation)\n\n"}
{"comment": {"body": "Regression passed:\n\n![](https://bitbucket.org/repo/o5KReBa/images/1600781615-Screenshot%202023-05-24%20at%2013.35.05.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1344/_/diff#comment-397208861"}}
{"comment": {"body": "Maybe, create some kind of Mock or Kafka manager", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1344/_/diff#comment-397489031"}}
{"title": "LDI-1130 adding Tizen dhcp fps", "number": 1345, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1345", "body": ""}
{"comment": {"body": "Regression:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10670](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10670){: data-inline-card='' } \n\nAcceptance:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10667](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10667){: data-inline-card='' } \n\nSmoke:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10666](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10666){: data-inline-card='' } \n\nAPI:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10665](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10665){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1345/_/diff#comment-397187647"}}
{"title": "Hotfix/LDI-1259 quick fix add missing platform", "number": 1346, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1346", "body": "add support for new PlatformTypes (see in ticket)\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/2489761954-Screenshot%202023-05-24%20at%2019.12.28.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1346/_/diff#comment-397325432"}}
{"title": "LDI-978: fix cujo aws auth", "number": 1347, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1347", "body": ""}
{"title": "LDI-1288: making regular flake8 and pre-commit less strict and allowing offline grooming of the code", "number": 1348, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1348", "body": "no yaml check in pre commit\nless strict function complexity threshold\nallows return after else\nno python file length validation\n\nallows offline grooming of the code for the static-analysis enthusiastic"}
{"title": "LDI-915 fix auth issue in image push to cujo step", "number": 1349, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1349", "body": ""}
{"title": "Feature/automated tests trigger", "number": 135, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/135", "body": "add report portal for development ut - parser test\nsupport pytest code coverage with html reports\ngitignore and rename wronf gile name\ntrigger automation tests\n\n"}
{"title": "LDI-1142 revert rename to not break LDNI", "number": 1350, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1350", "body": "LDNI was broken by the change in the name of the mac vendor blacklist file. This reverts the change.\n"}
{"comment": {"body": "acceptance [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11598?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11598?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\nregression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11597?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11597?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\nsmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11596?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11596?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\napi [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11595?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11595?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1350/_/diff#comment-397549262"}}
{"title": "LDI-38 dhcpv6 fp data model", "number": 1351, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1351", "body": "LDI-38 added dhcpv6 fps data model\nLDI-38 added missing dhcpv4 fp for tizen\n\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/245371997-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1351/_/diff#comment-397564345"}}
{"title": "LDI-165 Pcap processor step function", "number": 1352, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1352", "body": "LDI-165 Refactor PCAP processor to use command line args instead of Kafka; add test JsonWriter instead of Firehose for now\nLDI-165 Fix test mock\nLDI-165 Fix json uploading\nLDI-165 Fix json uploading\nLDI-165 Trying to resolve S3 permission issue\nLDI-165 Add JsonLinesS3Writer input Parameters\nLDI-165 Fix pipeline input parameters\n\n"}
{"title": "LDI-1289 Deduplication scenario - fixing get_merged_result() and added some tests", "number": 1353, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1353", "body": "We had a bug in get_updated_fields() method in DHCPv4Handshake class, which mainly affected the usage of get_merged_result() in the same class.\nIn the case of multiple values in the new value to be merged, the logic failed to unify the dictionaries correctly, leading to dropping out some (ssid, ip address) data.\n\nCalling get_merged_result() would not merge them correctly, and drop out two elements from new_val instead of dropping out just one element.\nThank you @{6252eba45d1e700069ad0104}  for finding this bug!"}
{"title": "LDI-1130 added fp and UT", "number": 1354, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1354", "body": "LDI-1130 added fp and UT"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12016](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12016){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12017](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12017){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12019](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12019){: data-inline-card='' }   \nAC [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12018](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12018){: data-inline-card='' }\n\n![](https://bitbucket.org/repo/o5KReBa/images/3804960708-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1354/_/diff#comment-398377135"}}
{"title": "LDI-1199: move schema translation", "number": 1355, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1355", "body": "move schema translation to a more appropriate location"}
{"title": "LDI-1167: update MDNS_INFO & MDNS_HOSTNAME value type from dict to str", "number": 1356, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1356", "body": ""}
{"comment": {"body": "Jira ticket: [https://levltech.atlassian.net/browse/LDI-1167?atlOrigin=eyJpIjoiYmQyZGQ4Y2Q4MzU5NDBhMzhmODMzMzQwZWNlMTczM2IiLCJwIjoiaiJ9](https://levltech.atlassian.net/browse/LDI-1167?atlOrigin=eyJpIjoiYmQyZGQ4Y2Q4MzU5NDBhMzhmODMzMzQwZWNlMTczM2IiLCJwIjoiaiJ9){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1356/_/diff#comment-398543965"}}
{"comment": {"body": "nice!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1356/_/diff#comment-398544405"}}
{"comment": {"body": "How it affects cujo obfuscation? Do they need to support this change? It\u2019s contract change", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1356/_/diff#comment-398546390"}}
{"comment": {"body": "@{5f82bf320756940075db755e}   \nThe value type wasn\u2019t really changed, since it was being saved as a string representation of the dictionary  \nNonetheless, I\u2019ll verify it with cujo just to make sure no changes are needed in LENS", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1356/_/diff#comment-398548418"}}
{"comment": {"body": "You are right for values that don\u2019t require obfuscation logic, in this case cujo have logic on the values", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1356/_/diff#comment-398549226"}}
{"comment": {"body": "this is cujo replay:\n\n![](https://bitbucket.org/repo/o5KReBa/images/1847875644-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1356/_/diff#comment-398558846"}}
{"title": "LDI 1306 XIAOMI WATCH DATA", "number": 1357, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1357", "body": "LDI-1306 add Xiaomi Watch S1 and its fp\nLDI-1306 fix typo\n\n"}
{"comment": {"body": "please go over the imports and remove the ones that are not in use, for example `rand_hex_7`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1357/_/diff#comment-398774910"}}
{"comment": {"body": "the tests in this file will not run on ci pipeline. look for the `test_pipeline_device_classification_2.py` **in** `Makefile` and add a line for this file", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1357/_/diff#comment-398780241"}}
{"comment": {"body": "well done!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1357/_/diff#comment-398780319"}}
{"comment": {"body": "API[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13025](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13025){: data-inline-card='' } \n\nS[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13026](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13026){: data-inline-card='' } \n\nR[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13028](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13028){: data-inline-card='' } \n\nA[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13027](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13027){: data-inline-card='' }   ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1357/_/diff#comment-398936983"}}
{"title": "LDI-1306: fixing regex of branch name", "number": 1358, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1358", "body": "ticket name assumed commit names include LDI-number, now it can be also LDI_number"}
{"title": "LDI-1350 remove obsolete files", "number": 1359, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1359", "body": ""}
{"title": "add docker tag to the ci rp and for local git revision and user name", "number": 136, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/136", "body": "\n\nadd docker tag to the ci rp and for local git revision and user name\n\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/9472188-Screen%20Shot%202021-09-09%20at%2018.11.01.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/136/_/diff#comment-247564296"}}
{"title": "LDI-1327 mismatch l2 ddb", "number": 1360, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1360", "body": "LDI-1327 fixed L2 and devices db\n"}
{"comment": {"body": "same branch name\n\nA [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13591](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13591){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13592](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13592){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13593](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13593){: data-inline-card='' }   \nAC [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13594](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13594){: data-inline-card='' }\n\n![](https://bitbucket.org/repo/o5KReBa/images/4019194876-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1360/_/diff#comment-398996271"}}
{"title": "LDI-1366: spellcheck", "number": 1361, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1361", "body": "fixing typos\n\n"}
{"title": "LDI-1330 fixed default values in devices db", "number": 1362, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1362", "body": "LDI-1330 fixed default values in devices db"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13668](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13668){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13667](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13667){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13670](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13670){: data-inline-card='' }   \nAC [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13672](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13672){: data-inline-card='' }\n\n![](https://bitbucket.org/repo/o5KReBa/images/26336574-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1362/_/diff#comment-399031098"}}
{"title": "LDI-165 Add option to control firehose additional context", "number": 1363, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1363", "body": ""}
{"title": "LDI-1310 adding platform_type_mapping.csv to models", "number": 1364, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1364", "body": "LDI-1310 adding platform_type_mapping.csv to models"}
{"title": "LDI-1370 prior logic adjustment", "number": 1365, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1365", "body": "Adjust prior logic issue where a collection of all Apple devices would return iPhone, due to missing statistics on Apple Watches. To make sure it doesnt happen, fake statistics were added to Apple Watches and the consumer category prior was disabled.\nTo remain consistent with our current response for Apple Watches, limited the returned model list result length to 2.\nAlso in this PR, also cherry-picked {: data-inline-card='' } to pass regression tests."}
{"comment": {"body": "R: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14619](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14619){: data-inline-card='' } \n\nA: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14618](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14618){: data-inline-card='' } \n\nS: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14605](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14605){: data-inline-card='' } \n\nA:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14609](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14609){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1365/_/diff#comment-399353026"}}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/420603283-Screen%20Shot%202023-05-31%20at%2014.24.32.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1365/_/diff#comment-399353358"}}
{"title": "LDI-1172 remove false homtom counts", "number": 1366, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1366", "body": "When collecting prior logic statistics, instances of Huawei P30 were falsely doubly counted as HOMTOM P30 Pro, this leads use to give a false HOMTOM result in some cases, as detailed in the bug report. This PR removes the false counts."}
{"comment": {"body": "Acceptance [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14769](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14769){: data-inline-card='' } \n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14770](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14770){: data-inline-card='' } \n\nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14768](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14768){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14767](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14767){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1366/_/diff#comment-399395987"}}
{"title": "LDI-978: remove unused metrics", "number": 1367, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1367", "body": ""}
{"title": "Merged in LDI-1370-prior-logic-adjustment (pull request #1365)", "number": 1368, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1368", "body": "LDI-1370 prior logic adjustment\n\nLDI-1370 adjust prior logic\nLDI-1370 Add Apple watch statistics to avoid prior between consumer categories\nLDI-1370 fix tests\nLDI-1370 add fake prior statistics for Apple Watches\nLDI-1370 update display threshold\nLDI-1370 revert and limit entries in model field\nLDI-1370 actually limit the amount of devices in the result list\nMerged in LDI-1330_fix_ddb (pull request #1362)\n\nLDI-1330 fixed default values in devices db\n\nLDI-1330 fixed default values in devices db\nMerged dev into LDI-1330_fix_ddb\n\nApproved-by: Ophir Carmi Approved-by: Liad Nahmias * LDI-1370 merge changes from dev\n\nLDI-1370 missing updates from dev\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov"}
{"comment": {"body": "degredation?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1368/_/diff#comment-399401019"}}
{"comment": {"body": "degredation?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1368/_/diff#comment-399401034"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} it\u2019s not synced, and far behind dev", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1368/_/diff#comment-401884575"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} do you see any conflicts? the purpose is to merge to dev those changes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1368/_/diff#comment-401887573"}}
{"comment": {"body": "S: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17068](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17068){: data-inline-card='' } \n\nA: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17069](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17069){: data-inline-card='' } \n\nA: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17072](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17072){: data-inline-card='' } \n\nR: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17073](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17073){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1368/_/diff#comment-401902610"}}
{"comment": {"body": "I updated l2 model today, for example, so it probably will have conflicts", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1368/_/diff#comment-401904760"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} I have updated the models in the source branch from dev.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1368/_/diff#comment-401905170"}}
{"title": "LDI-1216 - uncomment unit-test for pcap-processor", "number": 1369, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1369", "body": "Problem\noriginal test used hard-coded 5G as interface type change hard-coded value to 2.4G (the real interface type) this requires fixing the expected results\nSolution\nchanged expected results file and uncommented the test\nTesting\nUnit test + regression passed"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/199704154-Screenshot%202023-06-01%20at%209.27.33.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1369/_/diff#comment-399591830"}}
{"title": "Dev ops/no db performance test ariel", "number": 137, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/137", "body": "\n\nFixing types in device_decision_notification.py\nAdded android test as well\nFixed android unitest\nBrought back logs\nredis DAL\nredis db\ndisable match\nredis connection string\nredis connstring\nlogging level info\nbug fix\nworker setup log\nrewrite match in python instead of sql\nbug fixes\nfix hostname redis\nadd logs\nv\ncontains\n-\nconvert ts to float calc\nseparate the redis write and read replicas\nadded env vars\nremove scheduler env_vars\n-\nadded docker cache\nbug fix\nlogs\nadd metrics\nremove shard id label from metric for now\nuse redis set instead of redis keys\ndefault pod count\nremove vault as label\nadd generate_latest_length metrics\nsend sample of metrics to different topic\nchange key schema to contains entity type\nbug fix\nchanged db objects to fit dataclass and sqlalchemy\nadded Huawei to android hosts\nfixed classification and notification tests\ntests fix\nflake fix, added redis to docker-comopse\nfix docker-comopse\ntests\nflake\nfixed tests\nfix tests\ncreate postgres tables\nchange log level\nget_vauult_id for tests\npass only match result to notification pipeline\nfix struct and tests\nprint to find failure\nfix seerializartion and add UT\n\n"}
{"title": "LDI-1374: remove deprecated identification scores", "number": 1370, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1370", "body": ""}
{"comment": {"body": "@{6265307b185ac200692f9bd9} Isn\u2019t it going to cause backward compatibility issues because of the change of model files?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1370/_/diff#comment-399403862"}}
{"comment": {"body": "when are we updating the code in the environments? dev is ok with this change for a while", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1370/_/diff#comment-399405082"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} also in dev we are not changing interface/contracts", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1370/_/diff#comment-399405746"}}
{"comment": {"body": "it was a small bug I fixed in `dev` branch. I didn\u2019t mean dev environment. no need to check disabled/unexisting features scores. when we will update the code to current `dev` branch, we will be able to change this file too.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1370/_/diff#comment-399406669"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} see this merged PR:  \n[https://bitbucket.org/levl/eros-classifier/pull-requests/1379](https://bitbucket.org/levl/eros-classifier/pull-requests/1379){: data-inline-card='' }   \nit also breaks backward compatibility.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1370/_/diff#comment-402413631"}}
{"title": "LDI-1169 filter hostname ngrams", "number": 1371, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1371", "body": "This fixes a bug were Pixel 7 was falsely reported to be Pixel 2 due to the hostname android 2.\nThe more general issue is that single-letter ngrams and also purely numeric ngrams can increase the score of a match and make it preferred over other options. This PR adds a couple of checks to ensure such issues will no longer arise.\nIt should be noted that the hostname typing system is scheduled to be replaced very soon, but since its not 100% certain when it will be ready, this ensures we have a fallback without known severe bugs.\n\nAcceptance {: data-inline-card='' } \nSmoke {: data-inline-card='' } \nAPI {: data-inline-card='' } \nRegression {: data-inline-card='' } \n"}
{"title": "LDI-1382 fixed l2 data model", "number": 1372, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1372", "body": "Fixed vendors according devices db"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15048](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15048){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15049](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15049){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15051](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15051){: data-inline-card='' }   \nA [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15052](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15052){: data-inline-card='' }\n\n![](https://bitbucket.org/repo/o5KReBa/images/182271106-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1372/_/diff#comment-399626454"}}
{"title": "Bugfix/LDI-1382 fix l2 model vendors", "number": 1373, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1373", "body": "fixed  vendors and removed wrong macbook fps\n"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15291](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15291){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15290](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15290){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15293](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15293){: data-inline-card='' }   \nA [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15292](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15292){: data-inline-card='' }\n\n![](https://bitbucket.org/repo/o5KReBa/images/1304942630-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1373/_/diff#comment-399688639"}}
{"title": "LDI-1387: fixing PEP8 alerts", "number": 1374, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1374", "body": "ran code inspection in pycharm and fix its alerts. no enforcement here.\n\n"}
{"title": "LDI-165 Add configurable partner_id for PCAP extraction", "number": 1375, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1375", "body": "Null partner_id causes downstream repartitioning job to fail as the column used as partition, while empty/null partition values are forbidden."}
{"title": "LDI-1388 fixed l2 and ddb", "number": 1376, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1376", "body": "fixed l2 and ddb for Galaxy S21 FE"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/2746566642-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1376/_/diff#comment-399741946"}}
{"title": "LDI-1331", "number": 1377, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1377", "body": "LDI-1331 add l2 fp\nLDI-1331 iphone fixed name\nLDI-1331 deleted wrong iphone\n\n"}
{"comment": {"body": "Passed all phases:\n\nSmoke\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15499](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15499){: data-inline-card='' } \n\nAPI\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15498](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15498){: data-inline-card='' } \n\nRegression\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15500](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15500){: data-inline-card='' } \n\nAcceptance\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15501](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15501){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1377/_/diff#comment-399766770"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15498](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15498){: data-inline-card='' } API\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15499](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15499){: data-inline-card='' } S\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15500](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15500){: data-inline-card='' } R\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15501](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15501){: data-inline-card='' } A", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1377/_/diff#comment-399766848"}}
{"title": "LDI-1419 adding more l2 fingerprints to l2_model_mp.csv", "number": 1378, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1378", "body": "LDI-1419 adding more l2 fingerprints to l2_model_mp.csv"}
{"comment": {"body": "regression:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16018](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16018){: data-inline-card='' } \n\napi:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16015](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16015){: data-inline-card='' } \n\nsmoke:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16016](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16016){: data-inline-card='' } \n\nacceptance:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16018](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16018){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1378/_/diff#comment-400230472"}}
{"title": "LDI-1226 typing scores cleanup", "number": 1379, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1379", "body": "Taking care each typing module has a distinct score to fix an issue that several conflicting modules can cancel each other.\nThe TypeClassifier code was simplified accordingly.\nAdditionally, several typing modules were removed from typing_scores.json. These modules do not appear in TypeClassifierConfiguration since they are not in use or not typing module.\n\n"}
{"title": "Rebase redis branch", "number": 138, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/138", "body": "test\nremove metrics\ncd Itai\n-\ntry\n-\ntry2\ntest2\nflake\nnodeSelectors\nflatten\nflake\nparallel processing\nprocess level parallelism\nadd invocation counters\nscale down to 1 pod for now\nadd some counters\nremove counters\nremove counters\nlint\nAdding pipeline hit counter metric\nfix import\nadd done processing metric\ntest1-start_to_end single machine\ntest2_partition level parallel\ntest3_dask_cluster_partition_level processing\ntest2.1 partition level processing processes level\ntest2.2 batchsize and poll interval\ntest3.2 - dask scheduler + batchsize and poll interval\ntest2.3\n-\nadd logging\ntest\n-\n-\n-\ntest4\nadd pid label to the metrics\ninterval + batch size change\nbug fix\ndeclare labels to metrics\nper session paralleling\nscale be\nlabels\ntest5\nresources limit\nremove resources limit\ntenant name == topic name\n-\nreturn of the metrics\nbug fix\nfix the fix bug\nrefactor metrics\n-\n\nMerged in feature/partition_level_processing_2 (pull request #98)\nFeature/partition level processing 2\n\n_2\nlimit resources\nmetrics retry\nbug fix\nflake8\nlabel metrics\nbug fix\nmetrics\nremove log\nMerge branch 'feature/partition_level_processing' of bitbucket.org:levl/eros-classifier into feature/partition_level_processing_2\n\n\n\nMerged in feature/partition_level_processing_2 (pull request #99)\nFeature/partition level processing 2\n\ntest - no device logs\ndeiable new device logs\ndisable all db writes\ndisable tests\nchange density cofnig\nchange max_batch size\npoll every 500 ms\nrestore the db writes\nrestore tests\nMerge branch 'feature/partition_level_processing' of bitbucket.org:levl/eros-classifier into feature/partition_level_processing_2\n\n\n\ndask based docker\n\n-\ndevice_classification.py edited online with Bitbucket\ndevice_classification.py edited online with Bitbucket\nprocess levle parallelism\nflake8\ndask cluster\n-\n-\ndevice_classification.py edited online with Bitbucket\ndevice_classification.py edited online with Bitbucket\nupdate dask address\ngroup.instance.id consumer config\ndecrease poll interval\nfrom_kafka one by one\ntemporary remove tests\ntest\n-\ntest2\n-\n--\ntest poll\npoll\nadd sink\n-\n-\nfrom kafka\n-\nfrom kafka batched\nkafka python test\nadd logging\nsleep if no msg\nadd logs\nexcetion handeling\ntopic=itai\ntopic name from env var\ninternal address for kafka\nerror handeling\npipe instead of call chain\npoll parameters\nsupport async io to macthmaker\nstart to eend\nrequest resources\nflake8\nmanual deploy to itai\nsupport async io metrics wrapper\nsupport async io metrics wrapper\nseperate logs from db\nmatch result indevice session\nremove async\nimport relative to root\nmap each task separately\nformat decision\nlogging + sending metrics in case of exception\nserialzie datetime\nbug fix\ntemp remove tests\nrds\nno db test\npostgres host\nchangfe the batch size for no db purpose\nlint\ndatetime.datetime\n-\ndont select\n-\nresize\nkafka polling metrics\n-\n-\nclean parameters from pipeline\nadd logs\nadd logs\nlogs\nadjust batch size\nadd logs\nread write with out logs\n-\nlogs default to None\n-\nrestructrure the where clause to optimize match\nadd boto3\nremove report portal plugin for local test\nremove obsolete configuration keys\nremove obsolete configuration keys\nadapt pipeline to kafka_python consumer\nremove pip packages from setup\nremove defaults logs\nadd logs to the match result\ntry optimize query\nimplement notifications\nremove analytics step\nupdate requirements\nimplement notification\nrevert query optimization\nupdate deployment\nserialize dataclass\nparametrizedf pipeline name in metrics\nadd device_dhcpv4_transaction_id\nNow returning the notification\nAdded test_pipeline_device_notification.py file\nFixing DeviceDecisionLog\nFixing types in device_classification_api\nAdded NotificationEventsTypes\nRemoved logs\nlogging level to info\nPrinting device json\nFixing DeviceDecisionLog and DeviceHistoryLog usgae\nUpdated db_scheme\nBrought back the tables in db_scheme\nAdded an iPhone dump file and updated the unitest\nNow running unitests\nRemoved report-portal from pytest.ini\nFixing types in device_decision_notification.py\nAdded android test as well\nFixed android unitest\nBrought back logs\nredis DAL\nredis db\ndisable match\nredis connection string\nredis connstring\nlogging level info\nbug fix\nworker setup log\nrewrite match in python instead of sql\nbug fixes\nfix hostname redis\nadd logs\nv\ncontains\n-\nconvert ts to float calc\nseparate the redis write and read replicas\nadded env vars\nremove scheduler env_vars\n-\nadded docker cache\nbug fix\nlogs\nadd metrics\nremove shard id label from metric for now\nuse redis set instead of redis keys\ndefault pod count\nremove vault as label\nadd generate_latest_length metrics\nsend sample of metrics to different topic\nchange key schema to contains entity type\nbug fix\nchanged db objects to fit dataclass and sqlalchemy\nadded Huawei to android hosts\nfixed classification and notification tests\ntests fix\nflake fix, added redis to docker-comopse\nfix docker-comopse\ntests\nflake\nfixed tests\nfix tests\ncreate postgres tables\nAdded redis configuration\nchange log level\nget_vauult_id for tests\nFixed types comparison\nchange log level\nFixed wrong check condition\nFiltering by os only if os exists\npass only match result to notification pipeline\nFixed kafka address\nfix struct and tests\nprint to find failure\nfix seerializartion and add UT\nfix merges\nrearange the vault_id uuid match for testing\n\n"}
{"title": "LDI-1444 cleaning irrelevant band 5 from old apple watches", "number": 1380, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1380", "body": "LDI-1440 cleaning irrelevant band 5 from old apple watches"}
{"title": "Feature/LDI-1419 adding new l2 fingerprints   sc", "number": 1381, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1381", "body": "LDI-1419 fixed l2 model\n"}
{"comment": {"body": "API\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16340](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16340){: data-inline-card='' } \n\nSmoke\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16341](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16341){: data-inline-card='' } \n\nAcceptance:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16343](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16343){: data-inline-card='' } \n\nRegression:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16342](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16342){: data-inline-card='' } \n\n\u200c\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1381/_/diff#comment-400801589"}}
{"title": "LDI-1459 adding new l2 fingerprints to l2_model_mp.csv", "number": 1382, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1382", "body": "LDI-1459 adding new l2 fingerprints to l2_model_mp.csv"}
{"comment": {"body": "Smoke:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16415](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16415){: data-inline-card='' } \n\nAPI:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16416](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16416){: data-inline-card='' } \n\nRegression:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16417](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16417){: data-inline-card='' } \n\nAcceptance:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16418](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16418){: data-inline-card='' } \n\n\u200c\n\n\u200c\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1382/_/diff#comment-400881965"}}
{"title": "Feature/LDI-1465 adding new l2 fingerprints   sc", "number": 1383, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1383", "body": "LDI-1465 adding l2 finger prints to l2_model_mp.csv\nLDI-1465 addig ios 17 rpvr to mdns_rpvr.csv (LDI-1452)\n\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/2126022709-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1383/_/diff#comment-401843239"}}
{"title": "LDI-382 Add mac addresses removal from raw radiotap data", "number": 1384, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1384", "body": "TL:DR \nRemove mac addresses from the radiotap packet that is saved to the data catalog, based on their index which is collected while parsing the packet.\n\nWhen saving the radiotap data to the data catalog, we save the raw radiotap packet. The issue with that is that this packet can contain up to 4 mac addresses that are visible and cause a violation of privacy. In order to solve this, a logic that collect the placement of these addresses in the radiotap parser was added, along with all the necessary code that turns this data to parsed data inside the parsed fields struct so it can be visible when the data catalog rows are created. Additional logic was added where the data catalog rows are created, which remove the aforementioned mac addresses whose placement in the packet is now supplied.\nThe removal is actually to replace the mac addresses with zeros, in order to keep the structure of the raw data.\nTwo unit tests where added, one that verifies the collection of the placements, and one that test the removal of the addresses based on these placements.\nThere is a test that should be added that checks a packet that has 4 mac addresses in it. It wasnt added because such a packet wasnt available."}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16819](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16819){: data-inline-card='' }   \nAcceptance passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16820](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16820){: data-inline-card='' }   \nSmoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16818](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16818){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1384/_/diff#comment-401829513"}}
{"comment": {"body": "can with encrypt the Mac addresses with a known key \\(AES encryption for example\\) instead of making it all zeros, for debug purposes?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1384/_/diff#comment-401847476"}}
{"comment": {"body": "Wasn\u2019t part of the task definition in the ticket which clearly states \u201cthose values have to trimmed out of the data as those are consider PII\u201d and \u201cand **all of them** needs to be scrubbed out.\u201d  \n@{5a4500fe0cacf235de82a9d4} Your input on this matter will be appreciated . If these values  will be encrypted, how will the encryption key be managed?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1384/_/diff#comment-401891344"}}
{"comment": {"body": "It\u2019s a good suggestion @{6265307b185ac200692f9bd9} , but it\u2019s complex logic for Cujo to obfuscate \\(probably will require us to deliver some script\\). @{6252eba45d1e700069ad0104} we have a way of hashing the MAC addresses \\(partial hashing\\). [https://getcujo.atlassian.net/wiki/spaces/LEVL/pages/3886645261/LEVL+-+Governance+Process+-+Data+Models+Features+log](https://getcujo.atlassian.net/wiki/spaces/LEVL/pages/3886645261/LEVL+-+Governance+Process+-+Data+Models+Features+log){: data-inline-card='' } LEVL-OBFS-02.   \nBut, valid point to understand if it\u2019s really required @{5a4500fe0cacf235de82a9d4} @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} @{640494c293cf2599462f47b8} ?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1384/_/diff#comment-401892649"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} @{6265307b185ac200692f9bd9} \n\nWe already have the MAC address of the device \\(pseudonymized\\) provided in the metadata of the record that goes to our data warehouse, so anything we do here is going to be redundant.\n\nSo for short, we don\u2019t need anything else than just zeroing the MAC addresses fields here.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1384/_/diff#comment-401893726"}}
{"title": "LDI-1380", "number": 1385, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1385", "body": "add metrics on buffered list \n\nwriter write latency\nwriter write count\nwriter write size\n\n\n\navoid writing these metrics when draining the buffered list as it causes inifinite indirect loop\n\n\n"}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17471](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17471){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17470](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17470){: data-inline-card='' }   \nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17472](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17472){: data-inline-card='' }   \nAcceptance: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17473](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17473){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1385/_/diff#comment-401940325"}}
{"title": "LDI-165 add partner id config", "number": 1386, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1386", "body": "As discussed with @{5fd5d5149edf2800759cc96d} : added parsing with default value 42\n"}
{"title": "LDI-1458 analytics scripts folder", "number": 1387, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1387", "body": "l2 model\nl2 limitation\n\nregression tests arent relevant here"}
{"title": "LDI-1216 - complete remaining issues", "number": 1388, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1388", "body": "remove inline comments\nfix unit test resource\n\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/2755715809-Screenshot%202023-06-12%20at%2010.47.16.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1388/_/diff#comment-402519693"}}
{"comment": {"body": "Are messages encoded in base64? As mentioned, I think it is preferable to even have 2 separate binary files, one per packet, to avoid introducing another format for saving resources for tests. Because right now we either have pcaps or single-packet binary files, or encoded events \\(which have dedicated de/serializers\\).", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1388/_/diff#comment-402588098"}}
{"comment": {"body": "the messages in `rt_packets.json` are just the DI messages which come as base64.  \nThe file contains an ASSOC and PROBE requests.  \n  \nI don\u2019t have real event to use, so I read some event\\_message file, and change it inline.  \nI can separate the `rt_packets` file but it will still be a \u201cnew\u201d way of generating resource for test", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1388/_/diff#comment-402591458"}}
{"comment": {"body": "@{712020:53b4b0dd-2aa8-4b28-a065-b5a63a5b390b} Ok I thought these were actual packets because of the name. If that\u2019s not the case then we\u2019re good.\n\n\\(Consider changing the filename to indicate that it contains events\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1388/_/diff#comment-404636320"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} will do", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1388/_/diff#comment-404707408"}}
{"title": "Bugfix/LDI-1478 verify model apple oui names", "number": 1389, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1389", "body": "fixed OUI and added devices to devices db\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/4161397953-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1389/_/diff#comment-402426104"}}
{"title": "Rebase redis branch ariel", "number": 139, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/139", "body": "Flake fixes\ntest_android_icmp_mclr_match check\nNot counting false value features\nDebug print\n\n"}
{"title": "LDI-1494 adding l2 from qa to l2_model_mp.csv", "number": 1390, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1390", "body": "LDI-1494 adding l2 from qa to l2_model_mp.csv"}
{"title": "LDI-1460", "number": 1391, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1391", "body": "An ad hoc patch to prevent some of the issues caused by lack of reliable statistical data in the specific_names_distribution model.      \n{: data-inline-card='' }   \n\n"}
{"title": "LDI-1484 Add mock db support", "number": 1392, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392", "body": "TL:DR  add a mock module for the dynamo db, so the system can function without running the docker-compose script that initialize services\n\nAfter these changes, there is no need to run the docker-compose scrip before running the system or a unit test or the manual test tool from the eros-classifier/tests_utils/test_utils.pywith the following configuration:\nUSE_SCHEMA_REGISTRY=False - for the schema registry\nMOCK_DYNAMO_DB=True - for the dynamo db\nIS_LOCAL_TEST=True - for the model loader\nPRODUCE_TO_KINESIS=False - for the kinesis writer\n\n\nAdd a module that bundle the dynamo db functions\nAdd a mock module that use mainly the functions of the real module but mocks them with a decorator. The only difference is when initializing the modules.\nAdd a configuration that allows to choose to mock the dynamo db. This will work with unit tests as well as the system itself.\nChange the unit tests and several calls to the dynamo db logic in other part of the system so it supports the changes.\n\n"}
{"comment": {"body": "@{6252eba45d1e700069ad0104} if you have already set `DevicesInventory` as `DeviceInventoryMock`, why do you need to re-implement the logic of each function? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402665935"}}
{"comment": {"body": "1. It is not a re-implementation, because the mock calls the regular module so the logic remains the same.\n2. The mock happens when the function is decorated with ` @mock_dynamodb.`   \n  But I cannot decorate the regular module because I want it to function with the real db.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402671402"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} Yeah, I see that we have an inheritance on static class.. suggest separating to a base interface to get the API defined and then the impls\u2026 even to catch if some logic is not covered by one of those impls..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402673975"}}
{"comment": {"body": "@{5f82bf320756940075db755e} If there is a missing implementation, the test running with the mockup will fail.  \nI think it is premature to define such a base class, especially since it doesn't serve the purpose of mocking the DB service.  \nAs I see it, when the building blocks of the system will be separated, it will be good to check how this part can be better defined in order to support future changes.   \n  ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402824022"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} defining interface is the basic, it was prior this change in someway, as single class. This extraction of dynamodb was defined as module separation. \n\nAnother thing also, please remove the docker compose dynamodb dockers.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402828391"}}
{"comment": {"body": "@{5f82bf320756940075db755e} I agree with the suggested pattern. When we have multiple alternative implementations for an interface, it stands to reason to create an ABC defining the API \\(`DeviceRepository`, no implementation at all\\), and inherriting classes implementing the different alternatives. And it is a chance to solidify this API, so I suggest to do so", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402833912"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} I think that it makes more sense to take a system that has little to no encapsulation and first define basic boundaries and organize how the different modules can function together and only then define each module in a more fine grained way.  \nBut if you both think that making this abstract class is what will serve the bigger purpose better then I\u2019ll do it.  \nPlease note that the MockDevicesInventory will have to inherit from the regular DevicesInventory if I want to be able to call its functions instead of re-implementing all of them. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402837476"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Isn\u2019t it good to have the option to work with the GUI of the local dynamodb when debugging?  \nIt will not be available if we move the dynamodb from the docker-compose ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402840543"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} it\u2019s interesting suggestion. We can have it as a side compose that not started by default as part of other containers. Similar to metrics compose, it\u2019s not running for all tests as default, also this why it\u2019s separate compose.\n\nOne question, if i run system with mock db, can i use the gui to access the tables, or i will need to rerun with the compose dynamodb?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402843973"}}
{"comment": {"body": "@{5f82bf320756940075db755e} there is no gui for the mockup", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402845163"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} it\u2019s a very good suggestion. Can you split the dynamodb from the default  compose, so it can be used for debug purposes, and not run as default with UT and/or the system regression, etc..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402852072"}}
{"comment": {"body": "@{5f82bf320756940075db755e} So the UT in the CI/CD should run with the mockup?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1392/_/diff#comment-402861001"}}
{"title": "LDI-1405 add test for reduced ua", "number": 1393, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1393", "body": ""}
{"comment": {"body": "API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19130](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19130){: data-inline-card='' } \n\nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19131](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19131){: data-inline-card='' }\n\nA [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19132](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19132){: data-inline-card='' }\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19133](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19133){: data-inline-card='' } R", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1393/_/diff#comment-402905347"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-3/19722](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-3/19722){: data-inline-card='' }  R\n\nA [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-3/19721](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-3/19721){: data-inline-card='' } \n\nAPI [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-3/19719](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-3/19719){: data-inline-card='' } \n\nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-3/19718](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-3/19718){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1393/_/diff#comment-404411673"}}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1393/_/diff#comment-404411898"}}
{"title": "LDI-1406 add modules for ios 16.6", "number": 1394, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1394", "body": ""}
{"comment": {"body": "API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19188](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19188){: data-inline-card='' } \n\ns [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19189](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19189){: data-inline-card='' } \n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19190](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19190){: data-inline-card='' } A\n\n R [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19191/3313519](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/19191/3313519){: data-inline-card='' }  ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1394/_/diff#comment-403227403"}}
{"title": "LDI-1066 : Export to data warehouse filtering", "number": 1395, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1395", "body": "Design : \nfrom the classifier export perspective , I want to be able to filter export records to the DWH (via kinesis) based on various criteria\nGiven a list of messages , partner_id and vault_id blacklists and partner_id whitelist, I want to be able to filter the messages in the following priority\n\nexclude message from partner_id that exists in the blacklist\nexclude message from vault_id that exists in the blacklist\ninclude message from vault_id that exists in the whitelist\nfilter the rest statistically (assuming vault distribution is fixed)\n\nThe filter function should include messages from other partners based on a percentile value. The percentile value will determine the likelihood of a message being included, with lower percentiles resulting in fewer messages being included.\nBy using this filter function, I can control the cost of the path kinesis->lens->firehose->sparrow DWH\n{: data-inline-card='' } \n"}
{"comment": {"body": "remove all 3 lines please", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1395/_/diff#comment-404396492"}}
{"comment": {"body": "revert please. there is a way to fix this unit test. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1395/_/diff#comment-404396915"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} @{637f5c5e3e79f12e572115d7} Let\u2019s make sure to follow the requirements:\n\n1. Split the portion per stream\n2. Remove all the configurations in separate PR, except the portion per stream\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1395/_/diff#comment-404505839"}}
{"title": "TESTS/LDI-1489/Check Device Schema", "number": 1396, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396", "body": "\nAdded unit test that checks DynamoDB data (randomly chosen ) to check the device schema.\nThe json strings from data field in DynamoDB will be converted to Device obj, the object converted back to json format and 2 json files compared for the same data\n\n\nLDI-1489: Create new test validating Device Schema\nLDI-1489: Add input file for new test validating Device Schema.\n\nSigned-off-by: <NAME_EMAIL>\n"}
{"comment": {"body": "[@Oleg Shtilerman](https://levltech.slack.com/team/U054LA48B8C) looks great! One important point, we have also a static comparison test of deviceclass for all its keys, test\\_all\\_device\\_fields\\_are\\_properly\\_defined. This test purpose was to find any change on the existing key in deviceclass and to fail in such a scenario. This test has a bug, as it didn\u2019t catch the desired event change with prev\\_macs.  \nYour approach also verifies serialize/deserialize on random device class samples, but because of our dynamic design, it won\u2019t catch all the changes that can occur. Thus it should be a mix of both approaches.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404416958"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Thanks! The function `test_all_device_fields_are_properly_defined` is verifying that Device class contains the given list of fields. But if Device has more fields - it\u2019ll not cause failure.  \n\nAny case, I see the rows from DynamoDB table contains some fields not existing in Device object, like **is\\_ipv4\\_static** or **low\\_res\\_user\\_agent**, and I checked only 10 rows. From the other side, Device object contains about 20 fields more than DynamoDB row. \n\nSo I think we need the list of mandatory fields for dynamo\\_db row\n\n\u200c\n\n\u200c\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404431371"}}
{"comment": {"body": "no need of this `else`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404596643"}}
{"comment": {"body": "remove `, \"r\"` as you don\u2019t specify all default values of arguments to this function.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-*********"}}
{"comment": {"body": "1. better to use json file\n\n2\\. when using json file please prettify the file, you can use the output of `python -m json.tool <filename>`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-*********"}}
{"comment": {"body": "function name is a little off, can you make sure it has valid syntax?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-*********"}}
{"comment": {"body": "Isn\u2019t there some standard python function/package that will satisfy this functionality?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-*********"}}
{"comment": {"body": "Can this test name be more descriptive?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-*********"}}
{"comment": {"body": "It\u2019s possible to have different fields saved in dynamodb than ones reflected in the device class. See [https://levltech.atlassian.net/wiki/spaces/TS/pages/8371634205/DB+Devices+Schema+versions](https://levltech.atlassian.net/wiki/spaces/TS/pages/8371634205/DB+Devices+Schema+versions){: data-inline-card='' }. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-*********"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404673082"}}
{"comment": {"body": "Each row  of this file -  json from **data** column from DynamoDB table . We can change this input.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404674649"}}
{"comment": {"body": "ok", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404675409"}}
{"comment": {"body": "Fixed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404679035"}}
{"comment": {"body": "Deepdiff? But it is overkilling\u2026 We need only check equality. IMO it is good enough ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404679458"}}
{"comment": {"body": "fixed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404680289"}}
{"comment": {"body": "@{712020:2353429c-f6e0-4843-96bb-a3a7b28da4cc} yes, see this:  \n[https://jsonlines.org](https://jsonlines.org){: data-inline-card='' } maybe change the extension of the file from `txt` to `jsonl`  \nI meant:  \n\n```\n[\n<your file content>\n]\n```\n\nand then it is a json file", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404682050"}}
{"comment": {"body": "@{712020:2353429c-f6e0-4843-96bb-a3a7b28da4cc} `Deepdiff` is a great tool", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404682469"}}
{"comment": {"body": "we use Deepdiff", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404711611"}}
{"comment": {"body": "Thanks, @{5dbeb866c424110de52552cc} \n\nBut if we\u2019ll see such case - we need to investigate it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404711632"}}
{"comment": {"body": "Thanks, @{5dbeb866c424110de52552cc} But if we\u2019ll see such case - we need to investigate it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404711749"}}
{"comment": {"body": "replace `len(diffs) < 1` with `not diffs`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404721128"}}
{"comment": {"body": "replace `len(missing_keys) < 1` with `not missing_keys`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404721243"}}
{"comment": {"body": "ok", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1396/_/diff#comment-404724294"}}
{"title": "LDI-978 remove kinesis thread", "number": 1397, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1397", "body": "Remove Kinesis thread and fix slice by size in MB in Kinesis streams."}
{"comment": {"body": "this function is too complex which can lead to bugs. try to break this function to a few smaller ones please.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1397/_/diff#comment-404596056"}}
{"comment": {"body": "We need to support by time as well, it can be in minutes with configuration", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1397/_/diff#comment-404601384"}}
{"comment": {"body": "Done already @{5f82bf320756940075db755e}  \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1397/_/diff#comment-404601806"}}
{"comment": {"body": "added here time aspect as well\n\n @{5f82bf320756940075db755e} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1397/_/diff#comment-404601965"}}
{"comment": {"body": "Can you also add configuration?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1397/_/diff#comment-404602379"}}
{"comment": {"body": "Its the same configuration as it was before called -\n\n```\nKINESIS_TTF\n```\n\n@{5f82bf320756940075db755e} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1397/_/diff#comment-404603038"}}
{"comment": {"body": "Thanks!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1397/_/diff#comment-404611811"}}
{"title": "LDI-550 fix clone lfs", "number": 1398, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1398", "body": "When the latest commit has a large file changed, the pipeline sometimes fails to clone the repo. Fixing it by enabling LFS for all steps"}
{"title": "LDI-1397 Add test for all the schema fields validation", "number": 1399, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1399", "body": ""}
{"comment": {"body": "why?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1399/_/diff#comment-404761062"}}
{"comment": {"body": "please move this data to a json file and read it here", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1399/_/diff#comment-404761258"}}
{"title": "adjust the pipelines under piplines and remove classifier sub folder", "number": 14, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/14", "body": ""}
{"title": "Redis : Fixing query_builder, matching features update", "number": 140, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/140", "body": "This PR fixes the logic across our matching algorithm. \nThe query_builder now returns only the matched features, and it now checks only existing features.\nIn addition, our matchmaker failed to update the relevant features after finding a matching device, and this is also fixed here.\nAll of our unitests pass now :)"}
{"title": "LDI-1310 get all available platform name", "number": 1400, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400", "body": "Update platformType resolution \nwas enum  now string"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/3732817043-Screenshot%202023-06-19%20at%2015.40.48.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404704860"}}
{"comment": {"body": "@{712020:53b4b0dd-2aa8-4b28-a065-b5a63a5b390b} you are breaking an API here. It\u2019s used in Automation code and in other events\u2026 what is your approach here?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404705119"}}
{"comment": {"body": "True.  \nbut that\u2019s the task, changing the enum type to string type.  \nbut I\u2019m also bumping the dependency in eros-automation and eros-configuration-api  \n[https://bitbucket.org/levl/eros-configuration-api/pull-requests/167](https://bitbucket.org/levl/eros-configuration-api/pull-requests/167){: data-inline-card='' }   \n[https://bitbucket.org/levl/eros-automation/pull-requests/532](https://bitbucket.org/levl/eros-automation/pull-requests/532){: data-inline-card='' }   \nafter this bump automation tests are passing \\(api tests failed previously\\)  \n  \n  \nif there\u2019s other places to update it please let me know.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404706191"}}
{"comment": {"body": "@{712020:53b4b0dd-2aa8-4b28-a065-b5a63a5b390b} This change applies an update to our API on the following deployments.  \n@{5dbeb866c424110de52552cc} @{637f5c5e3e79f12e572115d7} please share your thoughts", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404708240"}}
{"comment": {"body": "@{5f82bf320756940075db755e} I Spoke with Yoav.  \nhe showed me this page with dependencies diagram   \n[https://levltech.atlassian.net/wiki/spaces/TS/pages/8367833093/eros-classifier+repo+design#Analysis-of-eros-classifier-dependencies-on-the-package-and-on-the-module-level](https://levltech.atlassian.net/wiki/spaces/TS/pages/8367833093/eros-classifier+repo+design#Analysis-of-eros-classifier-dependencies-on-the-package-and-on-the-module-level){: data-inline-card='' }   \n  \nAlso I made sure and eros-automation doesn\u2019t use the `PlatformType` directly \\(no imports and such\\)  \nand also the eros-configuration-api doesn\u2019t seem to have any references to `PlatformType` ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404747010"}}
{"comment": {"body": "@{712020:53b4b0dd-2aa8-4b28-a065-b5a63a5b390b} I Just now noticed this change. This also has the potential to break our current db entries. This change is more critical than the previous one as we are already in production and have entries in GDE. Suggest discussing with @{5dbeb866c424110de52552cc} and @{637f5c5e3e79f12e572115d7} team for the best approach to fixing this properly.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404755834"}}
{"comment": {"body": "@{712020:53b4b0dd-2aa8-4b28-a065-b5a63a5b390b} Thanks for great analysis! seems you are solid on this :wink:", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404756011"}}
{"comment": {"body": "what is the connection to this file in this PR?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404756949"}}
{"comment": {"body": "there is no real need of this member being inside type classifier. please move it to a different class/file.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404757211"}}
{"comment": {"body": "see other comment. please remove this import as the platform types should not be in type classifier", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404757391"}}
{"comment": {"body": "please move all string that appear more than once to a variable. there is a file for strings - `classifier_strings.py` or alike", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404757853"}}
{"comment": {"body": "very nice job done!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1400/_/diff#comment-404757911"}}
{"title": "LDI-1501 - add re-association data processing", "number": 1401, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1401", "body": "TL:DR  Add support for the re-association packets that can be used to get L2 fingerprints, including data catalog export behavior\n\n\nChange the condition in the L2 typing features so the data that is parsed from re-association packets is used to produce l2 features.\nAdd a new type of fingerprint  and the code that produce it when encountering a re-association packet.\nAdd a temporary line to the l2 fingerprints model file (the new type from the previous point), in order to test that given a fingerprint, the classifier will use it and find the correct device type.\nChange the condition in the data catalog so the re-association is exported. It was decided that the DataType will be the same as association data.\nAdd unit tests to check the classification and export behavior.\n\n"}
{"title": "LDI-1536: remove get_device_item function", "number": 1402, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1402", "body": ""}
{"title": "LDI-1463", "number": 1403, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1403", "body": "Task:\n{: data-inline-card='' } \nDesign\n{: data-inline-card='' } \nRegression tests\n{: data-inline-card='' } {: data-inline-card='' } {: data-inline-card='' } {: data-inline-card='' } \n\n"}
{"comment": {"body": "move it after the `*,` so you can use default value `{}`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1403/_/diff#comment-404767446"}}
{"comment": {"body": "revert all these changed after using default value", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1403/_/diff#comment-404767525"}}
{"title": "Added some unitests that reproduce the updating matching features bug", "number": 141, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/141", "body": ""}
{"title": "Fixed the types given to the DecisionLog, from string to int", "number": 142, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/142", "body": ""}
{"title": "Removed datetime_to_float() method and usage", "number": 143, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/143", "body": ""}
{"title": "Fixing bytes / string mismatch in the notification mechanism", "number": 144, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/144", "body": ""}
{"title": "fix some tests and added support for archiver", "number": 145, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/145", "body": ""}
{"title": "db_schema_package", "number": 146, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/146", "body": ""}
{"title": "Ci cd smoke tests", "number": 147, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/147", "body": "add docker tag to the ci rp and for local git revision and user name\nci docker tag empty?\nci docker tag fix error on shel\nfix docker network name\nanother aproach for docker_tag\ntry perf_counter for measurement the step execution\nfix perf ocunter comparison\nbitbucket tag\nbitbucket tag aligned\nremove rp to test if adds overhead on logging performance\nget back rp to test if adds overhead on logging performance\nrmeove logging on performance UT\nfix flake8\nAdd variables to automated smoke test\n\n"}
{"title": "Ios 15 bonjour", "number": 148, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/148", "body": "Changed the bonjour uid comparison to match ios 15, as was changed in our pilot system here.\n\n"}
{"title": "Dev ops/helm chart", "number": 149, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/149", "body": "classifier helm chart\nadd local helm charts\nfixed umbrella chart\nfix charts values\n\n"}
{"comment": {"body": "why the configuration api charts are in classifier repository?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/149/_/diff#comment-251718744"}}
{"title": "device classification interface", "number": 15, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/15", "body": "adjust the pipelines under piplines and remove classifier sub folder\ndevice session classification interface defintion and folder structure alignment\n\n"}
{"comment": {"body": "Mounting `.` and replacing everything previously copied to image is not a good idea. We can\u2019t reliably test the image if some local changes happened.\n\nRebuilding the image takes a few seconds so I don\u2019t think that time consumption is a factor here", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/15/_/diff#comment-226315439"}}
{"comment": {"body": "you can also see in the CI that the classifier image is rebuilt instead of using the artifact, which is not what we want", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/15/_/diff#comment-226320906"}}
{"comment": {"body": "apply this change to the debugger settings .vscode/launch.json\n\n and to pytest .vscode/settings.json", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/15/_/diff#comment-226325530"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} rebuilding and passing update source code are 2 different issues.\n\nI agree regarding the rebuild, that is not mandatory, but in such case it even more important that lcoal changes are effectively passed to the container.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/15/_/diff#comment-226327631"}}
{"title": "Archiver bug fix", "number": 150, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/150", "body": "step to fix\nfixed random, filter, max files logic\n\n"}
{"title": "Dev ops/1M vaults dev", "number": 151, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/151", "body": "Merged in db_schema_package (pull request #146)\ndb_schema_package\n\ndb_schema_package\n\nApproved-by: Itai Zolberg\n\n\nfix metrics lables\n\nsupport only uuid\n\n"}
{"title": "Dev ops/1M vaults dev", "number": 152, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/152", "body": "seeder\nadd logs\nadd batch size env var\nremove try-except blcok\nfix count_polling_exceptions metric\nsupport device backward compatability\ndb_seeder metrics\nchange the default redis host\nsupport only uuid in vault id\n\n"}
{"title": "add devices to vault devices list", "number": 153, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/153", "body": ""}
{"title": "add helm charts", "number": 154, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/154", "body": "\n\nadd helm charts\n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} why there is same helm for snapshots? but used different in umbrella\n\n![](https://bitbucket.org/repo/o5KReBa/images/1864205250-Screen%20Shot%202021-10-06%20at%2022.27.14.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/154/_/diff#comment-253108163"}}
{"comment": {"body": "fixed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/154/_/diff#comment-253195737"}}
{"title": "Integrate ConnectionEvent", "number": 155, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/155", "body": "After taking the very last decisions regarding the structure of the new ConnectionEvent, we are not able to put this PR for review.\nNote that this is a very large PR, which basically changes most of our classifier code. Most of the changes are not dramatic, and their goal is to match the new structure.\nThis PR will probably keep getting some small changes here and there, while we start revewing it. Not all the job is completly done, and some integration tests in test_archiver.py still need to get a face-lift."}
{"comment": {"body": "change name to api", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/155/_/diff#comment-254408198"}}
{"comment": {"body": "insert to decision log", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/155/_/diff#comment-254409158"}}
{"comment": {"body": "Maybe flatten `MatchResult`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/155/_/diff#comment-254410635"}}
{"comment": {"body": "All of the functions rely on the event\u2026 Extend the event instead of defining them here.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/155/_/diff#comment-254413039"}}
{"comment": {"body": "As we have discussed in the design, those API remain internal.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/155/_/diff#comment-254519590"}}
{"title": "The metrics", "number": 156, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/156", "body": "fix archiver put data to return json, added snapshot metric\nrearange all prometheus metrics in one place, names refactoring\nfix imports and added missing lables\n\n"}
{"title": "add logs to debug and align makefile", "number": 157, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/157", "body": ""}
{"comment": {"body": "Can you imitate the way it is done in Dockerfile? \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/157/_/diff#comment-254148353"}}
{"title": "Testops smoke tests", "number": 158, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/158", "body": "add logs to debug and align makefile\nrevert makefile\nmove the automation smoke test after docker image push\n\n"}
{"title": "Dev ops/1M vaults dev", "number": 159, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/159", "body": "use k8s job\ndevelopment setup\n\n"}
{"title": "adapt parser to device session interface", "number": 16, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/16", "body": ""}
{"title": "Ci cd ct flow", "number": 160, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/160", "body": "add logs to debug and align makefile\nrevert makefile\nmove the automation smoke test after docker image push\nmove the automation smoke test after docker image push\nadd tenant perfix to automated tests using docker_tag\nadd tenant perfix to automated tests using docker_tag\n\n"}
{"title": "Helm charts", "number": 161, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/161", "body": "add helm charts\nadd helm charts\nadd helm charts\nadd helm charts\nadd helm charts\nfix chart path\nfix values path\nremove batch size\nedit bitbucket pipeline\nedit bitbucket pipeline\nremove archiver-decision chart\nmodified charts\nadd archiver values\nrename chart\nremoved deployment file\nadd deployment file\nremove deployment file\nfix image tag\ntest exception\ntest exception\nrecreate pods\ntest exception\nadd rollme attr\nadd rollme attr\nfix execption\nfix archiver-decision\nedit classifier charts\ntest ron deploy\ntest ron deploy\ntest ron deploy\npulled from dev\ntest s3 repo path\nedit bb pipelines\nchange topic notification\ntest s3 fetch pipeline\ntest s3 fetch pipeline\nedit bb pipelines\n\n"}
{"title": "Bug/helm chart db exporter service", "number": 162, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/162", "body": "\n\nexpose db exporter port\nadd pipefile to gitignore\nreduce scraping tome to 1s\nrename\n\n"}
{"title": "fix dev tag fot CT", "number": 163, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/163", "body": "\n\nfix dev tag\n\n"}
{"title": "Connection event missing tests", "number": 164, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/164", "body": "Fixed test_iphone_bonjour_uid_match_ios15\nbring back test_archiver\nfix classification pipeline tests\nfix notification pipeline tests\n\n"}
{"title": "Verify connection event", "number": 165, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/165", "body": "Hard-coded added ConnectionEvent structure, will be changed soon\nRemoved testing until we stabilize the integration\nRemoved more RawSession references\nFixed mac address getter\nUpdated the methods in device_classification_api.py\nTemp patch to handle the ssid\nFixed time calculation\nAdded debug print\nRemoved debug prints\nFixed device decision duration time calculation\nPut fixed session id until we fix this problem\nUpdated get_session_id() to use the real session id from the connection event\nUpdated the connection_event and platform_types files with the new generated code from eros-data-collector\nBrought back the unitests to the pipeline\nAdded debug prints\nFixed the unitests inner logic to match the new structure\nChanged the unitests in test_session_parser.py to match the new structure\nUpdated ConnectionEvent with the new structure\nUpdated the ssid getting\nAdded get_ssid() method to DeviceSession\nUpdated the unitests that relied on ssid matching\nAdded debug prints\nUpdated unitests to work with the new logic\nNow using the new ConnectionResult instead of DeviceSession\nNetbios: tuple -> list\nFixing transient_id and connected_devices initialization\nFixed wrong connection event members getting and setting\nFixed get_ssid()\nAdded debug print\nFixing netbios unpacking\nFixed time calculation\nFixed nonrandom_device_name type in features.py\nQuery builder small fixes for the new structure\nAdded debug assertion\nUpdated the notification fields to contain correct data\nDisabled the notification-pipeline unitest for now, until they'll be re-recorded with the new structure\nIntegrated the new structure of the parsed data\nChanged process_session() to get as parameters the mac address and the device type as well\nFixing the unitests in tets_netbios_tid.py\nFixing the icmp ns unitests\nFixed the unitests of icmp mclr\nFixed the unitests of dhcpv6 duid\nFixed the unitests of dhcpv4 handshake\nFixed the device type identification unitests\nFixed parsed data to connection event matching\nStarted fixing the unitests in test_session_parser\nFixed the unitests in test_session_parser\nFixed dns_parser.py to not override strings\nFixed dns unitests in test_ethernet_parser.py\nAdded debug print\nAttempting to change the parsed packets re-creation\nNow setting None value to features if needed to\nAdded debug print\nFixing format_decision\nFixing captive_portal.py objects creation\nSetting the parsed data as a return value\nAdded debug prints\nSetting None to features if needed\nRemoved debug prints\nNow returning features.NonrandomDeviceName from nonrandom_device_name feature\nNow returning features.Dhcpv4Handshake type result from dhcpv4_handshake feature\nNow returning features.Dhcpv6Duid type from dhcpv6 feature\nAdded debug log to fix the http bug\nFixed http extraction bug\nFixed select_device_with_highest_score() to get the device creation time properly\nAdded debug print in the radiotap parser\nNow parsing and extracting radiotap packets with the new structure\nNow writing the radiotap parsing results to the connection event only if they exist\nSeperated the DHCPv4 features - given_ip and transaction_id\nPatch fix in phyngerbank.py\nRemvoing redundant assignment of the parsed_data\nAdd full-fledged parsing and features description\nSimplified dhcpv4_handshake.py\nBrought back the unitests and flake to the pipeline\nFlake fix\nFixed the imports of parsing.py and features.py\nupdate missing types in parsing\nMix up in types\nfix rt_caps\nfix typo in features api\nFixing more imports\nAssigning the filled parsing and features data classes when creating a ConnectionResult instance\nseparate dhcp handshake from transaction id in proto\nid_device_name_default > is_device_name_default\nFilling None result for invalid features\nAdded debug print to print the sessions we get with the new structure\nRe-enabled test_iphone_session\nGetting the ConnectionEvent from the data-collector\nUpdated data-collector version to 0.4.1 (the version with the updated ConnectionEvent that contains the empty parsed fields and empty features)\nFixed path in test_archiver\nFixed all the paths to use the ConnectionEvent from the data-collector\nRe-enabled test_iphone_session\nRemoved debug printing from docker_test_ci\nTemporary adding an iphone_session to see if the integration test passes\nAttempting to fix the decode problem in test_iphone_session\nAdded iphone_session_serialized.pickle with the new ConnectionEvent structure\nRe-enabled test_android_session with a new serialized session file\nRe-enabled test_linux_session\nRe-enabled test_windows_session\nRe-enabled report portal\nDisabled report portal\nMatched pytest.ini with the version of dev branch\nConflict fix in device_classification_api.py\nFixed test_iphone_bonjour_uid_match_ios15\nFixed test_iphone_bonjour_uid_match_ios15\nbring back test_archiver\nfix classification pipeline tests\nfix notification pipeline tests\nfix notification pipeline tests with pipeline\n\nMerged in connection_event_missing_tests (pull request #164)\nConnection event missing tests\n\nFixed test_iphone_bonjour_uid_match_ios15\nbring back test_archiver\nMerge branch 'integrate_connection_event' of bitbucket.org:levl/eros-classifier into integrate_connection_event\nMerge branch 'integrate_connection_event' into connection_event_missing_tests\nfix classification pipeline tests\nfix notification pipeline tests\nfix notification pipeline tests with pipeline\n\n\n\nfix netbios type in db schema\n\nfix decision pipeline with device usage\nadd debug in decision pipeline\nrevert netbios to int in devices\nfix flake for db scheme\nalign matchmaker with netbios int device\nmove the test to manual trigger separated from push step\nrename automation test from the ci tests steps\n\n"}
{"title": "Hotfix/helm chart path", "number": 166, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/166", "body": "\n\nadjust new s3 repo structure\n\n"}
{"title": "Feature/push charts", "number": 167, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/167", "body": "!!temporary until implementation of version bump pipe!!\n\nbuild and push helm charts to s3 repo\n\n"}
{"title": "Print failed on caps_cmp = None", "number": 168, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/168", "body": "Remove excessive print that also caused exception when caps_cmp is None"}
{"title": "out of package import fix", "number": 169, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/169", "body": ""}
{"title": "Add matching with ICMP NS and BONJOUR UID", "number": 17, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/17", "body": "Some changes include PR , but PR 16 requires broader changes.\nUpdate DB scheme to include these new features.\nIntegration test now includes an android matching"}
{"comment": {"body": "I\u2019m adding these .env changes as vscode testing extension loaded `.env` in the root directory. If you have a better solution, do tell", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/17/_/diff#comment-226433261"}}
{"title": "change to lower case the automaiton CT tag", "number": 170, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/170", "body": ""}
{"title": "Typing service", "number": 171, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/171", "body": "eros_typing initial commit\nrefactoring\ncombine struct\ntyping steps\nstep\npipeline skeleton\ntypo fix\n\n"}
{"comment": {"body": "@{6085103f5797db006947d59a} Let\u2019s move to utils or the train folder. ass this i snot part of the service/feature.\n\nWill be used in Sparrow", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/171/_/diff#comment-259333018"}}
{"comment": {"body": "@{6085103f5797db006947d59a} Is this part of packet\\_parser logic? parsing the UA?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/171/_/diff#comment-259333100"}}
{"comment": {"body": "@{6085103f5797db006947d59a} let\u2019s add in the Feature also type, to identify between identification and typing features \\(as thre are different results\\).\n\n\u200c\n\ntype = \u2018Typing\u2019 ot alike..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/171/_/diff#comment-259333144"}}
{"title": "Hotfix/batch size", "number": 172, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/172", "body": "expose db exporter port\nadd pipefile to gitignore\nreduce scraping tome to 1s\nhard code clusterip for service type - metrics\n\nRevert \"hard code clusterip for service type - metrics\"\nThis reverts commit e01a87ad21b88a787f219d3153c3144a326b5c85.\n\n\nrename\n\nscraping to 30s\nbuild and push helm charts to s3 repo\nbuild and push helm charts to s3 repo\nbuild and push helm charts to s3 repo\nfix conflicts\nchange pipe trigger to manual\nfix deployments batch size var name\n\n"}
{"title": "add mdns to protobuf schemas", "number": 173, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/173", "body": "eros_typing initial commit\nrefactoring\ncombine struct\ntyping steps\nstep\npipeline skeleton\ntypo fix\nadd mdns to protobuf schemas\n\n"}
{"title": "support apple mdns typing model and refactor the device typing", "number": 174, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/174", "body": "eros_typing initial commit\nrefactoring\ncombine struct\ntyping steps\nstep\npipeline skeleton\ntypo fix\ncaptive portal and renaming\nadded apple lookup skeleton\nsupported protocols\nrealese date and test fix\nmake load_model not abstract\n\nMerged in feature/mdns_device_info (pull request #173)\nadd mdns to protobuf schemas\n\nadd mdns to protobuf schemas\nsupport parsing mdns device info answer with UT\nappend parsed mdns device info to connection event\n\n\n\nsupport apple mdns typing model and refactor the device typing\n\n\n"}
{"comment": {"body": "Where do you fill the value of `parsed_data.eth.mdns_query_fields.mdns_device_info`?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/174/_/diff#comment-257176227"}}
{"comment": {"body": "Where is the parsing itself of mdns device info from the packet with `dnslib`?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/174/_/diff#comment-257176588"}}
{"comment": {"body": "If `mdns_device_info` was added to `devices` table, the methods in matchmaker.py should update it as well when inserting a new device / updating an existing device", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/174/_/diff#comment-257176785"}}
{"comment": {"body": "in previous PR :wink:", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/174/_/diff#comment-257190333"}}
{"title": "Hotfix/pytest ini", "number": 175, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/175", "body": "\n\npoint pytest to new rp\n\n"}
{"title": "update devices list in example connection events used for tests", "number": 176, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/176", "body": ""}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9-slim", "number": 177, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/177", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.9-slim, as this image has only 37 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity | Priority Score / 1000 | Issue | Exploit Maturity |\n| --- | --- | --- | --- |\n|  | 614 | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893 | No Known Exploit |\n|  | 500 | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601 | No Known Exploit |\n|  | 714 | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403 | No Known Exploit |\n|  | 714 | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403 | No Known Exploit |\n|  | 614 | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406 | No Known Exploit |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0rc1-slim-buster", "number": 178, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/178", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.10.0rc1-slim-buster, as this image has only 70 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Feature/typing training pipeline", "number": 179, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/179", "body": "WIP\nflake8\nuse single dict for features\nflake8\nWIP\n\nRevert \"Merge branch 'typing_package' of bitbucket.org:levl/eros-classifier into feature/typing_training_pipeline\"\nThis reverts commit 3a15c4125b69bbcecd53ff6379477b0200fa2ce5, reversing changes made to 3676963331c6b5c340fb266d6e6722095b0a56ec.\n\n\nflake8\n\nrp config\nskip tests\nflake8\nrename variables\npytest + flake8\nfirehose integration\nWIP\nadd import\nworking version\nflake8\nflake8\nremove firehose from init\n-\ntemp remove tests\nlambda interface\nadd region\ninstall lambda ric\n\n"}
{"title": "Hotfix/ssdp user agent linux", "number": 18, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/18", "body": "adapt parser to device session interface\nraise to 50msec\nSSDP is Linux fix\n\n"}
{"title": "Integration/to typing package", "number": 180, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/180", "body": "WIP\nflake8\nuse single dict for features\nflake8\nWIP\n\nRevert \"Merge branch 'typing_package' of bitbucket.org:levl/eros-classifier into feature/typing_training_pipeline\"\nThis reverts commit 3a15c4125b69bbcecd53ff6379477b0200fa2ce5, reversing changes made to 3676963331c6b5c340fb266d6e6722095b0a56ec.\n\n\nflake8\n\nrp config\nskip tests\nflake8\nrename variables\npytest + flake8\nfirehose integration\nWIP\nadd import\nworking version\nflake8\nflake8\nremove firehose from init\n-\ntemp remove tests\nlambda interface\nadd region\ninstall lambda ric\nadd traces\nremove metrics\nserialize pcap datum\nrename file name\nlog warning\nrestore ci tests\nadd timing\nflake8\ntemp remove tests\nrestore tests\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim-buster", "number": 181, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/181", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.9.7-slim-buster, as this image has only 70 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Fix for 2 vulnerabilities", "number": 182, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/182", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nhvplot 0.7.2 requires holoviews, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  611/1000  Why? Recently disclosed, Has a fix available, CVSS 6.5  | Incorrect Access Control  SNYK-PYTHON-DASK-1767103 |  dask: 2021.6.0 - 2021.10.0  |  No  | No Known Exploit \n  |  654/1000  Why?* Has a fix available, CVSS 8.8  | Deserialization of Untrusted Data  SNYK-PYTHON-NETWORKX-1062709 |  networkx: 2.5.1 - 2.6  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the effected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Dev aligned with master", "number": 183, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/183", "body": "Add more DB fields\nNew fields\nAdd more filters to bypass\nand now with dhcp_requested_ip\nadd bonjour and icmp6 ns features\nAdd matching and integration test with android device\nFix types\nflake8\nadapt parser to device session interface\nraise to 50msec\nadd bonjour and ICMP ns feature extraction unittests\nadded snapshot deployment\nfix typo\nMerge with new interface\nFix tests and formatting\nArchiver in fixture\nSSDP is Linux fix\nSSDP is Linux fix\nfixed topic\nerror handling + logs\nNow parsing DHCP ACK messages as well, mistakenly forgotten\nfixed logs\nadd logs and enable logging info\nadd logs and enable logging info\nImproved the choosing procedure of the parsing method\nForgot NB port\nlogging not used\nRemoved unnecessery lambda\nRemoved unnecessery print\nWrong key fix\nCreate db package\nKeep moving files around\nMove around\nMatching query will return the matching features\nAdd os filtering\nExtract mac address from session\nlocal changes\nFix test with DB cleanup setup Fix OS usage\nFix flake8 and icmpns\nAnother format\nStandartize Android OS Levl ID is 128-bit UUID\nDB: add last update DHCPv6: add windows flow\nAdd windows matching unittest\nFix tests\nWIP\nhistory devices\nLoad android device repository to DB and add query\nremove that file uploads until it's tested\nadd metrics decorator\nAdd non-random device name for windos and android with tests Store hostname in DB\nfix ci errors\nClear comments for flakey flake\nextract metrics to utils py\nmetrics decoration on all pipelines\nbug fix\nFix DHCPv4 IP usage Use DHCPv4 requested IP feature\nCleanup files\nCollect metrics in matching\nFix DHCPv4 requested IP usage\nadd history devices\nadd history devices - fix flake8\nadd history devices - fix cr\nFix test\nimpl\nFix DB create\ndon't use metrics\nRemove metrics\nimpl\nCreate DeviceHistory if not exist\nFormat file\nfix lint issues\nautomation ennvironment\nexecute pipeline as dask distributed\nadded deploy rollout\nnotifications\nMerged dev into dev_tools/distributed\nextract decision to json file\nrename notification\nremove bad tests\nrefator the serialization of device session as member function\nfix flake8\nfix tests with dask\nadding more functionality for pull and put data archiver by path &adding a test in test archaive using path\nsqs integration\nadd logging\nadd integration env\nyaml fix\nfix integration pipeline\nformat decisions list\nhack notification metrics\nmark out tests until local kafka dependency is fixed\n30 seconds timeout per integration test\nsolving some comments for the previous pull request\nRevert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\"\nskip integration test\nchange error logging to info\nadd kafka callback\nparse_session() does not longer belong to a class, it's stateless\nfix radio tap set() serialization\nfix radio tap set() serialization\n\nMerged in dev_tools/local_kafka (pull request #44)\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/decision_log (pull request #46)\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\n\nfix deployment decision snapshot\n\n\nMerged in amir_archiver (pull request #39)\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\n\n\nMerged in feature/gitignore_vscode (pull request #49)\nIgnore vscode helper file\n\nignore vscode helkper file\n\nApproved-by: Tamir Raz\n\n\nMerged in dev_tools/metrics (pull request #50)\nDev tools/metrics\n\nfix dask prom\nfix metrics instanciation\nfix flake8\ntimeout + input as byte like\nlist of sessions\nflake8\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev_tools/metrics\nrelocate pytest.ini\ndisable kafka integrated tests\nmetric fixes\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in dev_tools/per_developer_environment (pull request #51)\nDev tools/per_developer_environment\n\nMerge branch 'dev_tools/metrics' of bitbucket.org:levl/eros-classifier into dev_tools/per_developer_environment\ndeploy parallely\nuse summary instead of Histogram to measure duration\nflatten analytics\nuse histogram again\ntest\nrename\nrename2\nsummary histogram test\nrename\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in vault_id (pull request #48)\nupdate schemas to contain tenant and vault id\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into vault_id\nupdate schemas to contain tenant and vault id\nadded tenant and vault from session\nfix some tests\nchanges\nMerge branch 'dev' into vault_id\ntests fix\ntests fix\nadded test helper\nremoved insert to vaults\n\nApproved-by: Shimon Goulkarov\n\n\nhotfix db host\n\nchange DASK default value in code to false\nhot fix uuid to str\n\nMerged in hotfix_get_pipeline_configuration (pull request #53)\nchange pipeline name init\n\nchange pipeline name init\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bug/feature_none_exception (pull request #55)\nBug/feature none exception\n\nignore vscode helkper file\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nfix exception on features filter in case None\nfix code review comemnts\n\nApproved-by: Gregory kovelman\n\n\nfix flake\n\nchange to updated db hostname default\n\nMerged in match_against_tenant_vault (pull request #58)\nMatch against tenant vault\n\nadded Device = get_device_model()\nget tenant_name from env\nfix flake\ninit Device schema from env\nfix typo\nflake fix\nfix filter\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in migration_aws (pull request #59)\nMigration aws\n\nMerged in dev (pull request #19)\n\nDev\n\nfix radio tap set() serialization\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerged in hotfix/serialize_rt_sets (pull request #40)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar Approved-by: Itai Zolberg\n\nMerged in simplify_packet_parser (pull request #41)\n\nparse_session() does not longer belong to a class, it should be stateless\nApproved-by: Shimon Goulkarov\n\nfix radio tap set() serialization\nMerged in hotfix/serialize_rt_sets (pull request #42)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar\n\nMerged in dev_tools/local_kafka (pull request #44)\n\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\nMerged in feature/decision_log (pull request #46)\n\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\nfix deployment decision snapshot\nMerged in amir_archiver (pull request #39)\n\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\nApproved-by: Nadav Livni Approved-by: Itai Zolberg\n\nMerge branch 'master' of bitbucket.org:levl/eros-classifier into dev\nfix flake\nchange to updated db hostname default\n\nApproved-by: Nadav Livni\n\n\ndeploy yo ca-central-1\n\nremove canada region\n\nMerged in hotfix/get_mac_address (pull request #60)\nfix function\n\nfix function\nMerged dev into hotfix/get_mac_address\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/metrics_latency (pull request #61)\nFeature/metrics latency\n\nNow calculating the decision time and the time the device spent in the pipeline\nRemoved unnecessery generate_latest()\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\ntemporary remove column\ntemp remove column\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\n\nApproved-by: Ariel Tohar\n\n\nMerged in feature_label_metrics (pull request #62)\nFeature label metrics\n\nNow calculating the decision time and the time the device spent in the pipeline\nRemoved unnecessery generate_latest()\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\ntemporary remove column\ntemp remove column\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\nAdding labels to metrics\nAdding labels to metrics\nFix labeling\nFix UT\n\nApproved-by: Ariel Tohar Approved-by: Gregory kovelman\n\n\nsupport tenant name label in metrics\n\n\nMerged in step_metrics_label (pull request #63)\nStep metrics label\n\nMerged in dev (pull request #19)\n\nDev\n\nfix radio tap set() serialization\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerged in hotfix/serialize_rt_sets (pull request #40)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar Approved-by: Itai Zolberg\n\nMerged in simplify_packet_parser (pull request #41)\n\nparse_session() does not longer belong to a class, it should be stateless\nApproved-by: Shimon Goulkarov\n\nfix radio tap set() serialization\nMerged in hotfix/serialize_rt_sets (pull request #42)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar\n\nMerged in dev_tools/local_kafka (pull request #44)\n\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\nMerged in feature/decision_log (pull request #46)\n\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\nfix deployment decision snapshot\nMerged in amir_archiver (pull request #39)\n\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\nApproved-by: Nadav Livni Approved-by: Itai Zolberg\n\nMerge branch 'master' of bitbucket.org:levl/eros-classifier into dev\nfix flake\nchange to updated db hostname default\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nsupport tenant name label in metrics\n\nApproved-by: Gregory kovelman\n\n\nMerged in feature/fix_matching (pull request #64)\nmatching fixes\n\nvault id is not a matching filter but a general filter change how matching features looked in decision log\nfix tests\nfix tests\n\nApproved-by: Ariel Tohar Approved-by: Nadav Livni\n\n\nMerged in DataArchiver_bucket_name-fix (pull request #65)\nchanged DataArchiver bucket_name to one in a new S3 account\n\nchanged DataArchiver bucket_name to one in a new S3 account\nFix UT with updated bucket name\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nAdd metrics for session and logging\n\nFix flake\nconvert metrics to summary\nconvert metrics to summary - fix flake\nconvert metrics to summary - fix lenght got raw_session\nadd scheduler to config of deployment\ndask deployment scheduler\ndefault dask scheduler\n\nMerged in feature/performance_tests (pull request #66)\nFeature/performance tests\n\nsupport tenant name label in metrics\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nAdd metrics for session and logging\nFix flake\nconvert metrics to summary\nconvert metrics to summary - fix flake\nconvert metrics to summary - fix lenght got raw_session\nadd scheduler to config of deployment\ndask deployment scheduler\ndefault dask scheduler\n\nApproved-by: Nadav Livni\n\n\nMerged in feature/fix_matching (pull request #68)\nFeature/fix matching\n\nvault id is not a matching filter but a general filter change how matching features looked in decision log\nfix tests\nfix tests\nFixed the wrong thing :(\nperms\nconflic\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov\n\n\nLink session id and devices\n\n\nMerged in feature/fix_matching (pull request #69)\nFeature/fix matching\n\nvault id is not a matching filter but a general filter change how matching features looked in decision log\nfix tests\nfix tests\nFixed the wrong thing :(\nperms\nconflic\nFix source of decision\nmerge\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/session_id_link_to_devices (pull request #70)\nFeature/session id link to devices\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nLink session id and devices\nfix session id\nupdate UT with session_id\nfix tests\nMerge branch 'feature/session_id_link_to_devices' of bitbucket.org:levl/eros-classifier into feature/session_id_link_to_devices\n\nConflicts: matching/tests/test_matching.py\n\nfix tests\nrevert last commit\nfix devie_type NoneType exception\nMerge branch 'feature/session_id_link_to_devices' of bitbucket.org:levl/eros-classifier into feature/session_id_link_to_devices\n\nApproved-by: Nadav Livni\n\n\nhot fix deploy\n\nadded sed -i s|{{scheduler}}||g to all deployments\n\nMerged in feature/device_decision_time (pull request #72)\nNow writing the decision time to the DB\n\nNow writing the decision time to the DB\ninteger -> float\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/prev_rand_mac (pull request #67)\nFeature/prev_rand_mac\n\nUpdating instead of overriding the list of rand macs\nUpdating to a list in device history log table\nAttemp to add a column to Device table\nParameter fix\nQuery fix\nFlake fix\nMerge branch 'dev' of  into feature/prev_rand_mac\nRemoved update_table_if_required() method, we'll do iot manually for now\nFixed session.decision usage\nMerge branch 'dev' of  into feature/prev_rand_mac\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bugfix/none_list_prev_rand_mac (pull request #74)\nTraceback fix - None prev mac addressees\n\nTraceback fix with none prev rand mac address\n\n\n\nMerged in proto_update (pull request #73)\nnew proto\n\nnew proto\nMerged dev into proto_update\n\nApproved-by: Shimon Goulkarov Approved-by: Itai Zolberg\n\n\nMerged in bugfix/EROS_69_wrong_os_filter (pull request #75)\nBugfix/EROS-69 wrong os filter\n\nNow filtreing by the device os only if exists\nHandling None type as well\nstr compare fix\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/prev_rand_mac_list_fix (pull request #76)\nPrev rand Mac - Not adding the address to the previous addresses list if it already exists\n\nNot adding the rand mac address to the previous addresses list if it already exists\n\nApproved-by: Gregory kovelman\n\n\nMerged in bugfix/resualt_topic_issue (pull request #77)\nset _result_topic to SESSION_BATCH_SNAPSHOT_RESULT\n\nset _result_topic to SESSION_BATCH_SNAPSHOT_RESULT\nMerged dev into bugfix/resualt_topic_issue\n\nApproved-by: Shimon Goulkarov\n\n\nMove DB engine and sessionmaker to global\n\n\nMerged in feature/update_icmp_ns_when_matching (pull request #78)\nUpdate icmp ns addresses when matching a device\n\nNow updating icmp ns addresses when matching a device\nUsing set.union instead of +=\n\nApproved-by: Shimon Goulkarov\n\n\nflake fix\n\nfix ut\n\nMerged in hotfix/db_connections_issue (pull request #79)\nHotfix/db connections issue\n\ndefault dask scheduler\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nLink session id and devices\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMove DB engine and sessionmaker to global\nflake fix\nfix ut\nMerge branch 'hotfix/db_connections_issue' of bitbucket.org:levl/eros-classifier into dev\n\nApproved-by: Nadav Livni Approved-by: Gregory kovelman\n\n\nMerged in bugfix/EROS_65_update_device_hostname (pull request #80)\nNow updating the device name when matching a device\n\nNow updating the device name when matching a device\nMerge branch 'dev' of  into bugfix/EROS_65_update_device_hostname\n\nApproved-by: Gregory kovelman\n\n\nfix the lag time metric\n\n\nMerged in feature/metrics_lag_time (pull request #82)\nFeature/metrics lag time\n\nLink session id and devices\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMove DB engine and sessionmaker to global\nflake fix\nfix ut\nMerge branch 'hotfix/db_connections_issue' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nfix the lag time metric\n\nApproved-by: Gregory kovelman\n\n\naws_region as variable\n\n\nMerged in new_datalake (pull request #83)\ns3 bucket name as env var\n\ns3 bucket name as env var\nfix test\nfix flake and test\n\nApproved-by: Shimon Goulkarov\n\n\ntypo fix\n\nsed fix\n\nMerged in DatArchiver-add-ability-to-pull-exact-namber-off-files-from-s3 (pull request #85)\nDatArchiver add ability to pull exact namber off files from s3\n\nDatArchiver: added ability to pull exact number off files from s3\nfix conflict\nMerge branch 'dev' into DatArchiver-add-ability-to-pull-exact-namber-off-files-from-s3\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/perfromance_metrics (pull request #90)\nAdding pipeline hit counter metric\n\nAdding pipeline hit counter metric\nfix import\nadd done processing metric\n\nApproved-by: Tamir Raz\n\n\nAdding more labels to metrics for more accurate dashboards\n\nfix matchmaker metrics labels\nFix labels example\n\nMerged in feature/metrics_labeling (pull request #91)\nAdding more labels to metrics for more accurate dashboards\n\nAdding more labels to metrics for more accurate dashboards\nfix matchmaker metrics labels\nFix labels example\n\nApproved-by: Ariel Tohar\n\n\nMerged in feature/scoring_features (pull request #93)\nFeature/scoring features\n\nMerge branch 'dev' of  into feature/scoring_features\nHard coded disabling some features to see if we decide correctly, will be reverted shortly after the test\nflake\nBrought back hostname\nForcing using the scoring logic\nmatch_dhcp_requested_ip -> match_dhcpv4_handshake\nDebug prints\nDebug prints\nAdded score to prev rand mac feature as well\nBrought back all original logic\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in packages_cicd (pull request #92)\nAWS code aretifact integration\n\ntry to fix build\ntypo\ntypo\nfix docker build, fixed dataarchiver push\nusing package instead of copying code\nMerge branch 'dev' into packages_cicd\nuse make to build\ndont use make to build\nfix build\nfix build\n\nApproved-by: Shimon Goulkarov\n\n\nbump manualy data archiver\n\n\nMerged in data_archiver_version_bump (pull request #96)\nData archiver version bump\n\nAdding more labels to metrics for more accurate dashboards\nfix matchmaker metrics labels\nFix labels example\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nbump manualy data archiver\n\n\n\nupdate artifactory on dev push as well\n\nbump manualy data archiver\nfix package push\nforce upload\nfix pipeline\nskip for tests\n[skip ci] bumped data archiver package version, build 952, new version 0.1.3\nfixed package push\nbump minor\nadded slack message on package update\n\nMerged in feature/updating_bonjour_uid (pull request #81)\nNow updating the bonjour uid(s) when matching a device\n\nNow updating the bonjour uid(s) when matching a device\nMerge branch 'dev' of  into feature/updating_bonjour_uid\nNow using the same code for updating the bonjour, rand macs and icmp ns addresses\nMerge branch 'dev' of  into feature/updating_bonjour_uid\nNow appending both strings and lists of features as required\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman Approved-by: Itai Zolberg\n\n\nReplace minio with cloudserver\n\nless changes comparing to dev\neven less changes comparing to dev\nincrease docker memory\n\nMerged in feature/db_migrator (pull request #95)\ndatabase versioning\n\nrevert schema package\nmigrator opreations\nalembic is better\nbuild and push alembic on changes\nreadme\nMerge branch 'dev' into feature/db_migrator\nMerge branch 'dev' into feature/db_migrator\nadded versions dir\nremove unused\ndummy file\n\nApproved-by: Shimon Goulkarov\n\n\nfix pipe\n\n\nMerged in db_upgrade_test (pull request #102)\nnew version\n\nnew version\n\n\n\nremoved dummy file\n\ntest\ntest\nfix alembic push\nfix alembic push\nfix alembic push\nfix alembic push\n[skip ci] revert pipeline comments\n[skip ci] fix alembic action line\n\nMerged in async_db (pull request #101)\nAsync db\n\nasync step\ntests\nfix tests\nfix tests2\nMerge branch 'dev' into async_db\nfix typo\nMerge branch 'dev' into async_db\nremove unused\nMerge branch 'dev' into async_db\nMerged dev into async_db\n\nApproved-by: Shimon Goulkarov\n\n\n[skip ci] fix pipeline\n\nfix deployment file, added db host\n\nMerged in feature/partition_level_processing (pull request #103)\nFeature/partition level processing\n\ntest poll\npoll\nadd sink\n-\n-\nfrom kafka\n-\nfrom kafka batched\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/partition_level_processing\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/partition_level_processing\n\nApproved-by: Shimon Goulkarov\n\n\nRevert \"Merged in async_db (pull request #101)\"\nThis reverts commit fafb820dea4d73ff301f32d1dd5cd2a59d0a0587.\n\n\nMerged in revert_async_io_db (pull request #106)\nRevert \"Merged in async_db (pull request #101)\"\n\nRevert \"Merged in async_db (pull request #101)\"\n\nThis reverts commit fafb820dea4d73ff301f32d1dd5cd2a59d0a0587.\n\n\nbump archiver back\n\nbump archiver back\nbump archiver\n[skip ci] bumped data archiver package version, build 1114, new version 0.3.4\n\nMerged in bugfix/decision_log_prev_mac (pull request #107)\nChanged RandMac inner feature name\n\nChanged the feature name to be contained in the returned string value from the query builder\nprev_mac -> prev_rand_mac\n\nApproved-by: Gregory kovelman\n\n\nMerged in feature/hw_mac (pull request #110)\nFeature/hw_mac\n\nAdded hw_mac feature\nFixed table name\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in snapshot_optimization (pull request #112)\nSnapshot optimization\n\nsolved deployment conflict\nsolved deployment conflict\nremoved ron from bb-pp\nMerged master into snapshot_optimization\nadding Ron to deployments\nadding Ron to deployments\nadding Ron to deployments\nadded snapshot env vars\nadded snapshot env vars\nresovled dev conflicts\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bugfix/dhcp_fingerprinting (pull request #115)\nBugfix/dhcp fingerprinting\n\nAdded logs to debug the problem\ndhcp_fingerprinting -> DHCPFP\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in fix_dhcpfp_comparison (pull request #116)\nFix dhcpfp comparison\n\nFixed the dhcpfp comparison types\nDebug prints\nFixed dhcpfp identifiers\nRemoved unused method\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/icmp_mclr (pull request #108)\nFeature/icmp6 mclr\n\nRemoving alembic upgrade file\nflake\nComment to see if it triggers the alembic build\nMerge branch 'dev' of  into feature/icmp_mclr\nFix query_builder / feature string mismatch\nMerge fix\nMerge branch 'dev' of  into feature/icmpv6_mclr\nEdited the strings in the query_builder to match the feature names\nAdded the relvant alembic configration to add the icmp mclr column\nflake fixes\n\n\n\nMerged in feature/wps_uid_matching (pull request #114)\nFeature/wps uid\n\nStarted implementing the wps uid matching feature\nMerge branch 'dev' of  into feature/wps_uid_matching\nMerge branch 'dev' of  into feature/wps_uid_matching\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bugfix/wps_parsing (pull request #118)\nBugfix/wps parsing\n\nParsing the right part of the radiotap\nAdded dumps method for RadioTap\nFixed serializing\nFixed icmp mclr parsing\nUnitest fix\nlist -> set\nDisabled ICMP6_MCLR\nDebug prints\nFixed the filter on icmp mclr\nAdded combine_icmp_mclr_lists\n\n\n\nadd report portal for development ut - parser test\n\nsupport pytest code coverage with html reports\n\nMerged in bugfix/ipv4_none (pull request #120)\nBugfix/DHCP hostname extraction\n\nAdded debug prints\nAdded more debug prints\nTaking the hostname only if exists\nRemoved debug logs\n\nApproved-by: Shimon Goulkarov\n\n\ngitignore and rename wronf gile name\n\n\nMerged in bugfix/icmp_mclr_comparing (pull request #121)\nBugfix/icmp mclr comparing\n\nAdded bebug logs\nNow parsing and saving the mclr addresses\nUsing the mclr as a matcher\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bugfix/eros-82-hw-mac (pull request #122)\nBugfix/eros-82 hw mac update\n\nAdded debug logs\nImproved types hinting\nNow updating the hw mac addr when matching a device\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/prev_transaction_id (pull request #113)\nFeature/prev dhcp transaction id\n\nFixed name matching\nMerge branch 'dev' of  into feature/prev_transaction_id\nflake fix\nFixed string / int mismatching\nMerge branch 'dev' of  into feature/prev_transaction_id\nMerge branch 'dev' of  into feature/prev_transaction_id\ndhcpv4_transaction_id: String -> BigInteger\nTypo fix\nChanged the query condition\nNow using postgres and_\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/code_coverage (pull request #119)\nFeature/code coverage\n\nadd report portal for development ut - parser test\nsupport pytest code coverage with html reports\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\ngitignore and rename wronf gile name\n\nApproved-by: Ariel Tohar\n\n\nMerged in feature/nb_tid (pull request #123)\nFeature/netbiod transaction ID\n\nImport fix\nFlake fixes\nChanged the netbios transaction id from String to BigInteger\nFixed the case of None transaction id\nAdded debug prints\nTrying to solve the condition comparing\nBigInteger -> Integer\nquery builder cosmetic change\nMerge branch 'dev' of  into feature/nb_tid\ndatetime / timestamp fix\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in dhcp_parser_to_dataclasses (pull request #117)\nDHCP parser to return dataclasses\n\nNow returning dataclasses instead of raw dicts when parsing dhcp packets\nFixed test_session_parser.py\nFlake fixes\nMerge branch 'dev' of  into dhcp_parser_to_dataclasses\nMerge branch 'dev' of  into dhcp_parser_to_dataclasses\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in update_report_portal (pull request #126)\nUpdated report portal\n\nUpdated report portal\nAutomation -> Development\n\nApproved-by: Tamir Raz Approved-by: Shimon Goulkarov\n\n\nMerged in bugfix/exception_in_nonrandom_device_name (pull request #127)\nBugfix/exception in nonrandom device name\n\nUnified NetBios and MDNS parsers to also return dataclases, as the dhcp parser does, so accessing their fields will be done in the same way\npytest.ini update\nFlake fix\nName fixing\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/add_unitests (pull request #124)\nFeatures unitests - Phase 1\n\nAdded more unitests to test_netbios_tid.py\nAdded test_icmp6_mclr.py\nRemoved pandas usage from test_dhcpv6_duid.py\nAdded test_dhcpv4_handshake.py\nFixed icmp mclr test comparison\nMerge branch 'dev' of  into feature/add_unitests\nMerge fixes\nRemoved report-portal to see if the unitests pass\nMerge branch 'dev' of  into feature/add_unitests\nMerge fixes\n\nApproved-by: Tamir Raz Approved-by: Shimon Goulkarov\n\n\nMerged in feature/enable_unitest (pull request #128)\nFeatures unitests - Phase 2\n\nBringing back xfail unitests\nAdded logs to dhcpv6_duid\nUpdated test_dhcpv6_duid_happy_path\nEnabled test_dhcpv6_duid_unhappy_path\nFixed dhcpv6_duid\nEnabled the unitests in test_device_type_identification.py\nFixed netbios hostname extraction\nMerge branch 'dev' of  into feature/enable_unitest\n\nApproved-by: Tamir Raz\n\n\nMerged in disable_report_portal (pull request #131)\nRemoved report portal until its fixed\n\nRemoved report portal until its fixed\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bugfix/wps_uid_extraction (pull request #129)\nBugfix/wps uid extraction\n\nNow extracting the wps_uid feature instead of returning the entire struct and dict\nReverted pytest.ini changes\nMerge branch 'dev' of  into bugfix/wps_uid_extraction\nChanged extract_wps_uid() call\n\nApproved-by: Tamir Raz\n\n\nMerged in feature/unitests_phase_3 (pull request #130)\nFeature/unitests - Phase 3\n\nDisables report portal\nAdded test_iphone_bonjour_uid_match\nAdded icmp ns and mclr tests\nAdded multiple windows unitests\nFixed decision checking\nwps_uid extraction fix\nAdded linux tests\nAdded more Windows tests\nAdded more Android tests\nMerge branch 'dev' of  into feature/unitests_phase_3\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/add_more_session_parsing_tests (pull request #132)\nFeature/unitests - Phase 4\n\nAdded session parsing tests from real connection of all types\nImproved the tests condition checks\nImproved type hinting\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in improve_types_hinting (pull request #133)\nAdded types hinting where possible in several important places in our classification process\n\nAdded types hinting where possible in several important places in our classification process\n\nApproved-by: Shimon Goulkarov\n\n\ntrigger automation tests\n\nrun only tests without the build of automation pipelines\nmove to dev, staging, master produciton commits the automateed smoke tests\n\nMerged in feature/automated_tests_trigger (pull request #135)\nFeature/automated tests trigger\n\nadd report portal for development ut - parser test\nsupport pytest code coverage with html reports\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\ngitignore and rename wronf gile name\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\ntrigger automation tests\nrun only tests without the build of automation pipelines\nmove to dev, staging, master produciton commits the automateed smoke tests\n\nApproved-by: Tamir Raz\n\n\nadd rp to dev ut\n\nadd docker tag to the ci rp and for local git revision and user name\nci docker tag empty?\nci docker tag fix error on shel\nfix docker network name\nanother aproach for docker_tag\ntry perf_counter for measurement the step execution\nfix perf ocunter comparison\nbitbucket tag\nbitbucket tag aligned\nremove rp to test if adds overhead on logging performance\nget back rp to test if adds overhead on logging performance\nrmeove logging on performance UT\nfix flake8\n\nMerged in feature/ci_enhancments (pull request #136)\nadd docker tag to the ci rp and for local git revision and user name\n\nfix docker network name\nanother aproach for docker_tag\ntry perf_counter for measurement the step execution\nfix perf ocunter comparison\nbitbucket tag\nbitbucket tag aligned\nremove rp to test if adds overhead on logging performance\nget back rp to test if adds overhead on logging performance\nrmeove logging on performance UT\nfix flake8\n\nApproved-by: Ariel Tohar\n\n\nMerged in archiver_suffort_maxfiles_filterd (pull request #145)\nfix some tests and added support for archiver\n\nfix some tests and added support for archiver\nrandom choice\n\n\n\n[skip ci] bumped data archiver package version, build 1626, new version 0.4.4\n\n\nMerged in rebase_redis_branch (pull request #138)\nRebase redis branch\n\ntry to fix notification pipeline\nfix partitions issue\nfix test\nfix when duplicate device\nMerge branch 'dev' of  into rebase_redis_branch\nchange kafka log to warnning and added logs to notification pipe\nload android repo on load\nfix unitests to tests android repo in memory\npartition by tenant id is not needed\nMerged in db_schema_package (pull request #146)\n\ndb_schema_package\n\ndb_schema_package\n\nApproved-by: Itai Zolberg\nApproved-by: Nadav Livni\n\n\nfix dev pipeline\n\nAdd variables to automated smoke test\nAdd variables to automated smoke test\nAdd REDIS to all deployments\nAdd REDIS to all deployments\nHard-coded added ConnectionEvent structure, will be changed soon\nRemoved testing until we stabilize the integration\nRemoved more RawSession references\nFixed mac address getter\nUpdated the methods in device_classification_api.py\n\nMerged in ci_cd_smoke_tests (pull request #147)\nCi cd smoke tests\n\nremove rp to test if adds overhead on logging performance\nget back rp to test if adds overhead on logging performance\nrmeove logging on performance UT\nfix flake8\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nAdd variables to automated smoke test\nAdd variables to automated smoke test\nAdd REDIS to all deployments\nAdd REDIS to all deployments\n\nApproved-by: Ariel Tohar\n\n\nTemp patch to handle the ssid\n\nFixed time calculation\nAdded debug print\nRemoved debug prints\nFixed device decision duration time calculation\nPut fixed session id until we fix this problem\nfix to kafka address\nUpdated get_session_id() to use the real session id from the connection event\nUpdated the connection_event and platform_types files with the new generated code from eros-data-collector\nBrought back the unitests to the pipeline\nAdded debug prints\nFixed the unitests inner logic to match the new structure\nChanged the unitests in test_session_parser.py to match the new structure\nUpdated ConnectionEvent with the new structure\nUpdated the ssid getting\nAdded get_ssid() method to DeviceSession\nUpdated the unitests that relied on ssid matching\nAdded debug prints\nUpdated unitests to work with the new logic\n\nMerged in ios_15_bonjour (pull request #148)\nIos 15 bonjour\n\nChanged the bonjour uid comparison to match ios 15\nMerge branch 'dev' of  into ios_15_bonjour\nFixing unitest\nAdded an ios-15 bonjour match unitest\n\nApproved-by: Shimon Goulkarov\n\n\nNow using the new ConnectionResult instead of DeviceSession\n\nNetbios: tuple -> list\nFixing transient_id and connected_devices initialization\nFixed wrong connection event members getting and setting\nFixed get_ssid()\nAdded debug print\nFixing netbios unpacking\nFixed time calculation\nFixed nonrandom_device_name type in features.py\n\nMerged in archiver_bug_fix (pull request #150)\nArchiver bug fix\n\nstep to fix\nfixed random, filter, max files logic\n\nApproved-by: Shimon Goulkarov\n\n\n[skip ci] bumped data archiver package version, build 1741, new version 0.5.4\n\nQuery builder small fixes for the new structure\nAdded debug assertion\nUpdated the notification fields to contain correct data\nDisabled the notification-pipeline unitest for now, until they'll be re-recorded with the new structure\n[skip ci] bumped data archiver package version, build 1781, new version 0.6.4\nIntegrated the new structure of the parsed data\nChanged process_session() to get as parameters the mac address and the device type as well\nFixing the unitests in tets_netbios_tid.py\nFixing the icmp ns unitests\nFixed the unitests of icmp mclr\nFixed the unitests of dhcpv6 duid\nFixed the unitests of dhcpv4 handshake\nFixed the device type identification unitests\nFixed parsed data to connection event matching\nStarted fixing the unitests in test_session_parser\nFixed the unitests in test_session_parser\nFixed dns_parser.py to not override strings\nFixed dns unitests in test_ethernet_parser.py\nAdded debug print\nAttempting to change the parsed packets re-creation\nNow setting None value to features if needed to\nadd helm charts\nAdded debug print\nFixing format_decision\nFixing captive_portal.py objects creation\nSetting the parsed data as a return value\nAdded debug prints\nSetting None to features if needed\nRemoved debug prints\nNow returning features.NonrandomDeviceName from nonrandom_device_name feature\nNow returning features.Dhcpv4Handshake type result from dhcpv4_handshake feature\nNow returning features.Dhcpv6Duid type from dhcpv6 feature\nAdded debug log to fix the http bug\nFixed http extraction bug\nFixed select_device_with_highest_score() to get the device creation time properly\nAdded debug print in the radiotap parser\nNow parsing and extracting radiotap packets with the new structure\nNow writing the radiotap parsing results to the connection event only if they exist\nSeperated the DHCPv4 features - given_ip and transaction_id\nPatch fix in phyngerbank.py\nupdate rp\n\nMerged in helm_charts (pull request #154)\nadd helm charts\n\nremove deployment file\nfix image tag\ntest exception\ntest exception\nrecreate pods\ntest exception\nadd rollme attr\nadd rollme attr\nfix execption\nfix archiver-decision\n\nApproved-by: Shimon Goulkarov Approved-by: Itai Zolberg\n\n\nRevert \"Merged in helm_charts (pull request #154)\"\nThis reverts commit 82ab502e3eb14452d102c6fd8014a1536106f534.\n\n\nRemvoing redundant assignment of the parsed_data\n\n\nMerged in dev_ops/1M_vaults_dev (pull request #152)\nDev ops/1M vaults dev\n\n1M configuraation\nlogging level error\nset resources\nparallel seeders\nbug fix\nget pytest.ini from dev\n-\nrollback distributed seeding\nimport fix\nflake8\n\nApproved-by: Shimon Goulkarov\n\n\nAdd full-fledged parsing and features description\n\nSimplified dhcpv4_handshake.py\nBrought back the unitests and flake to the pipeline\nFlake fix\nFixed the imports of parsing.py and features.py\nupdate missing types in parsing\nMix up in types\nfix rt_caps\nfix typo in features api\nFixing more imports\nAssigning the filled parsing and features data classes when creating a ConnectionResult instance\nseparate dhcp handshake from transaction id in proto\nid_device_name_default > is_device_name_default\nFilling None result for invalid features\nAdded debug print to print the sessions we get with the new structure\nRe-enabled test_iphone_session\nGetting the ConnectionEvent from the data-collector\nUpdated data-collector version to 0.4.1 (the version with the updated ConnectionEvent that contains the empty parsed fields and empty features)\nFixed path in test_archiver\nFixed all the paths to use the ConnectionEvent from the data-collector\nRe-enabled test_iphone_session\nRemoved debug printing from docker_test_ci\nTemporary adding an iphone_session to see if the integration test passes\nAttempting to fix the decode problem in test_iphone_session\nAdded iphone_session_serialized.pickle with the new ConnectionEvent structure\nRe-enabled test_android_session with a new serialized session file\nRe-enabled test_linux_session\nRe-enabled test_windows_session\nRe-enabled report portal\nDisabled report portal\nMatched pytest.ini with the version of dev branch\nConflict fix in device_classification_api.py\nadd logs to debug and align makefile\nFixed test_iphone_bonjour_uid_match_ios15\nFixed test_iphone_bonjour_uid_match_ios15\nrevert makefile\n\nMerged in feature/debuggability (pull request #157)\nadd logs to debug and align makefile\n\nadd logs to debug and align makefile\nrevert makefile\n\nApproved-by: Itai Zolberg\n\n\nmove the automation smoke test after docker image push\n\nmove the automation smoke test after docker image push\n\nMerged in testops_smoke_tests (pull request #158)\nTestops smoke tests\n\nadd logs to debug and align makefile\nrevert makefile\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nmove the automation smoke test after docker image push\nmove the automation smoke test after docker image push\n\nApproved-by: Nadav Livni\n\n\nadd tenant perfix to automated tests using docker_tag\n\nadd tenant perfix to automated tests using docker_tag\nfix prefix to docker_tag and short pipelien UUID\nuse the shorten uuid\nalign to alphanumeric prefix\n\nMerged in ci_cd_ct_flow (pull request #160)\nCi cd ct flow\n\nrevert makefile\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nmove the automation smoke test after docker image push\nmove the automation smoke test after docker image push\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nadd tenant perfix to automated tests using docker_tag\nadd tenant perfix to automated tests using docker_tag\nfix prefix to docker_tag and short pipelien UUID\nuse the shorten uuid\nalign to alphanumeric prefix\n\nApproved-by: Tamir Raz\n\n\nMerged in dev_ops/1M_vaults_dev (pull request #159)\nDev ops/1M vaults dev\n\n-\nrollback distributed seeding\nimport fix\nflake8\nuse k8s job\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev_ops/1M_vaults_dev\ndevelopment setup\n-\ntemplate image name of db exporter\nMerged dev into dev_ops/1M_vaults_dev\n\nApproved-by: Ron Cohen Approved-by: Nadav Livni\n\n\nMerged in helm_charts (pull request #161)\nHelm charts\n\ntest s3 repo path\nedit bb pipelines\nchange topic notification\ntest s3 fetch pipeline\ntest s3 fetch pipeline\nedit bb pipelines\nalign with dev\nedit templates\nedit templates\nremove tests step\n\n\n\nchange CT name\n\n\nMerged in the_metrics (pull request #156)\nThe metrics\n\nflake\nfixed exporter\nlogs for tests\ntrying to fix metrics\nremove redis metrics\ntest\nremove unused\nadd vault id to vaults set\nMerge branch 'dev' into the_metrics\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into the_metrics\n\nApproved-by: Nadav Livni\n\n\n[skip ci] bumped data archiver package version, build 2060, new version 0.7.4\n\nfix merege changes\nbring back test_archiver\nfix classification pipeline tests\nfix notification pipeline tests\nadd db-exporter cm and tenant name\nfix typo db-exporter cm\nfix typo db-exporter cm\n\nMerged in hotfix/ct_fix_docker_tag (pull request #163)\nfix dev tag fot CT\n\nfix dev tag\n\nApproved-by: Tamir Raz Approved-by: Ron Cohen\n\n\nfix notification pipeline tests with pipeline\n\n\nMerged in connection_event_missing_tests (pull request #164)\nConnection event missing tests\n\nFixed test_iphone_bonjour_uid_match_ios15\nbring back test_archiver\nMerge branch 'integrate_connection_event' of bitbucket.org:levl/eros-classifier into integrate_connection_event\nMerge branch 'integrate_connection_event' into connection_event_missing_tests\nfix classification pipeline tests\nfix notification pipeline tests\nfix notification pipeline tests with pipeline\n\n\n\nfix netbios type in db schema\n\nfix decision pipeline with device usage\nadd debug in decision pipeline\nrevert netbios to int in devices\nfix flake for db scheme\nalign matchmaker with netbios int device\nmove the test to manual trigger separated from push step\nrename automation test from the ci tests steps\nalign Apple type\nsupport captive portal\nfix flake8 for tests\nadd logging\n\nMerged in hotfix/helm_chart_path (pull request #166)\nHotfix/helm chart path\n\nadjust new s3 repo structre\nadjust new s3 repo structre\nadjust new s3 repo structre\nadjust new s3 repo structre\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into bump_helm_charts\nscraping to 30s\nrename\nRevert \"hard code clusterip for service type - metrics\"\n\nThis reverts commit e01a87ad21b88a787f219d3153c3144a326b5c85.\n\nhard code clusterip for service type - metrics\nreduce scraping tome to 1s\n\nApproved-by: Shimon Goulkarov\n\n\nfix l2 model caps extraction\n\n\nMerged in verify_connection_event (pull request #165)\nVerify connection event\n\nrevert netbios to int in devices\nfix flake for db scheme\nalign matchmaker with netbios int device\nmove the test to manual trigger separated from push step\nrename automation test from the ci tests steps\nalign Apple type\nsupport captive portal\nfix flake8 for tests\nadd logging\nfix l2 model caps extraction\n\nApproved-by: Ariel Tohar\n\n\nbump archiver\n\nbump archiver\n[skip ci] bumped data archiver package version, build 2118, new version 0.10.4\n\nMerged in fix_print_failure_on_none (pull request #168)\nPrint failed on caps_cmp = None\n\nremove excessive print that also caused exception when caps_cmp is None\n\nApproved-by: Ariel Tohar\n\n\nMerged in bug_fix/db_schema (pull request #169)\nout of package import fix\n\nout of package import fix\nfix name\n\nApproved-by: Shimon Goulkarov\n\n\nfix pipeline\n\nforce db schema build\n[skip ci] bumped db schema package version, build 2134, new version 0.2.1\n\nMerged in feature/push_charts (pull request #167)\nFeature/push charts\n\nRevert \"hard code clusterip for service type - metrics\"\n\nThis reverts commit e01a87ad21b88a787f219d3153c3144a326b5c85.\n\nrename\nscraping to 30s\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into bump_helm_charts\nbuild and push helm charts to s3 repo\nbuild and push helm charts to s3 repo\nbuild and push helm charts to s3 repo\nfix conflicts\nfix conflicts\nchange pipe trigger to manual\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bug/helm_char_db_exporter_service (pull request #162)\nBug/helm chart db exporter service\n\nexpose db exporter port\nadd pipefile to gitignore\nreduce scraping tome to 1s\nhard code clusterip for service type - metrics\nRevert \"hard code clusterip for service type - metrics\"\n\nThis reverts commit e01a87ad21b88a787f219d3153c3144a326b5c85.\n\nrename\nscraping to 30s\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into bug/helm_char_db_exporter_service\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\n\n\nMerged in feature/lower_case_ct_tag (pull request #170)\nchange to lower case the automaiton CT tag\n\nchange to lower case the automaiton CT tag\n\nApproved-by: Tamir Raz\n\n\nMerged in hotfix/batch_size (pull request #172)\nHotfix/batch size\n\nrename\nscraping to 30s\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into bump_helm_charts\nbuild and push helm charts to s3 repo\nbuild and push helm charts to s3 repo\nbuild and push helm charts to s3 repo\nfix conflicts\nfix conflicts\nchange pipe trigger to manual\nfix deployments batch size var name\n\nApproved-by: Itai Zolberg Approved-by: Shimon Goulkarov\n\n\nMerged in hotfix/pytest_ini (pull request #175)\nHotfix/pytest ini\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into bump_helm_charts\nbuild and push helm charts to s3 repo\nbuild and push helm charts to s3 repo\nbuild and push helm charts to s3 repo\nfix conflicts\nfix conflicts\nchange pipe trigger to manual\nfix deployments batch size var name\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into hotfix/pytest_ini\npoint pytest to new rp\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in update_pipeline_test_sessions (pull request #176)\nupdate devices list in example connection events used for tests\n\nupdate devices list in example connection events used for tests\n\nApproved-by: Nadav Livni\n\n\nupdated reportportal uuid\n\nfix archiver tests\n[skip ci] bumped data archiver package version, build 2230, new version 0.11.4\n\n"}
{"title": "Integration/lab upload typing", "number": 184, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/184", "body": "reapply lab upload pipeline\nfix import\n\n"}
{"title": "add l2 features extraction", "number": 185, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/185", "body": ""}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0rc1-slim-buster", "number": 186, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/186", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\ndb/alembic/Dockerfile\n\nWe recommend upgrading to python:3.10.0rc1-slim-buster, as this image has only 70 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim", "number": 187, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/187", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.9.7-slim, as this image has only 38 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim", "number": 188, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/188", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\ndb/alembic/Dockerfile\n\nWe recommend upgrading to python:3.9.7-slim, as this image has only 38 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "remove restricted license module", "number": 189, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/189", "body": ""}
{"title": "Dev", "number": 19, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/19", "body": "added snapshot deployment\nfix typo\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim-buster", "number": 190, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/190", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.9.7-slim-buster, as this image has only 70 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "nadav test", "number": 191, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/191", "body": "Some types hints in radiotap_parser.py\nAdded cfr data to parsing.py file\nNow appending mocked data to CfrFields from parsing.py\nAdded cfo to features.py\nChanged cfr.type to UintField in the protobuf\nAdded cfo to the db_schema, might change the type later on\nAdded test placeholder\nAdded cfo_structs.py file, with all the needed classes and definitions for building the CFO model\nAdded a simple usage of CFO_OneClassClassifier_v3_1 just to see if we integrate with it well\nFlake fixes\nAdded scipy to requirements.txt\nAdded sklearn to requirements.txt\nCreated CFO directory under features_implementation\nNow calling CFO_OneClassClassifier_v3_1.train() in cfo.py\n\n"}
{"title": "Licenses split", "number": 192, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/192", "body": "\n\nsplit requirements file and modify makefile\nchange flake test ci\nflake ignore eggs\ncleanup\nclean old changes\n\n"}
{"title": "WIP / Integrate cfo model", "number": 193, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193", "body": "While the work on this PR is still in progress (As can be seen by multiple TODOs along the code), we can start examine the logic integrated here.\nThis PR adds the CFO component to our classifier.\nUpon a new connection, we take the estimated-CFO packets and the actual-CFO packets. Depends on the exact true labeling result, we decide whether to classify, train or online train.\nSome notes:\n\nAs for now, some components are not yet implemented, in the CFO side. For e.g, we need to finish the implementation of the decision between two matching devices and choose the closest one to get a higher score.\nAfter we investigate the devices CFO-wise, we combine the lists of the candidates we were left with from the true labeling, and the list of devices with their CFO results.\n\nIt was decided for now to build the CFO model both for the estimated and actual values, but to classify only against the estimated model.\nThe negative-devices-filtering we do is not yet complete. We need to decide how to filter the bonjour uids by the first three bytes, since its a list and not a single value. In addition, the versioning filtering needs to be implemented.\n\n\nUnitests and System-tests will be added once we have the agent send real CFO data.\n\nThe cfo_structs.py is all copied from the comcast repository, from research_master branch. I assume it was already reviewed there, so we dont need to do it again.\n\n"}
{"comment": {"body": "I see the different types of the cfo value and I want to clarrify that the true CFO is a signed integer and the estimated CFO is a float.  \nI\u2019d just treat them both as floats and keep this convention.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262279359"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} @{5b41d9de10d57114135eca66} @{5dbeb866c424110de52552cc} please verify correctness of the algorithmic logic ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262351293"}}
{"comment": {"body": "let\u2019s unify under the cfo model folder", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262351377"}}
{"comment": {"body": "`sqlalchemy.Float` instead of `sqlalchemy.String`/`sqlalchemy.Integer`?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262424592"}}
{"comment": {"body": "don\u2019t forget to run `CFO_Estimator_Filter_v3_1.filter` on the inputs", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262425441"}}
{"comment": {"body": "But SerializeModel\\(\\) returns a string", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262425689"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} oh, these are the models. sorry about the confusion. disregard that.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262425908"}}
{"comment": {"body": "good idea on input length checking, but the system should be able to work with only one of the types available, e.g. only estimated cfo is available", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262428321"}}
{"comment": {"body": "what about adding a separate OS version field to the device type structure?\n\nAs far as I know, typing is also able to detect android versions", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262430062"}}
{"comment": {"body": "This check is both for estimated or actual, no matter which is required", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262451952"}}
{"comment": {"body": "Added :\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262463665"}}
{"comment": {"body": "From what I saw, we separate the versions with the wispr component only for iOS. The filter I\u2019m doing for Android devices is only be the vendor. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262467297"}}
{"comment": {"body": "Can be instantiated only if needed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262520391"}}
{"comment": {"body": "export to separate file to allow cpe support.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262522451"}}
{"comment": {"body": "can be moved out of loop", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262524352"}}
{"comment": {"body": "and even in init", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262524401"}}
{"comment": {"body": "can skip deserialization when performing training? if so, do it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262525494"}}
{"comment": {"body": "Remove if unnecessary", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/193/_/diff#comment-262526701"}}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.8-slim-buster", "number": 194, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/194", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.9.8-slim-buster, as this image has only 71 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "fix matching to fit new typing", "number": 195, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/195", "body": ""}
{"title": "Improve pcap parse timing", "number": 196, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/196", "body": "improve pcap processing\ncreate typing model\n\n"}
{"title": "Classifier configmap update", "number": 197, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/197", "body": "default versions\nrelease date\n\n"}
