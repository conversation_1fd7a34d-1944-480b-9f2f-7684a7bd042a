{"comment": {"body": "the service is not available in Canada\u2026 anyway, master @{5f82bf320756940075db755e} wants it in us-east-2 so maybe we will move it.", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/16/_/diff#comment-237146392"}}
{"title": "Feature/LDI-408 support parsing agent id in coll", "number": 160, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/160", "body": "LDI-408: Add custom step in bitbucket to build and test only python package\nLDI-408: Add agent id header parsing and UT, add enum for old ingestion event message type\n\n"}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/160/_/diff#comment-367659057"}}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/160/_/diff#comment-367659260"}}
{"title": "Feature/LDI-595 support agent id on levl cpe age", "number": 161, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/161", "body": "\n\nLDI-595: Support agent_id on kafka headers in agent dispatch and sending on the wire\n\n"}
{"comment": {"body": "please add unit tests. did you check it works?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/161/_/diff#comment-373091511"}}
{"comment": {"body": "I am adding a comment to remind that this PR should be merged in order to finish the support of the agent id field", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/161/_/diff#comment-375374158"}}
{"title": "Feature/LDI-94 add ask1 config", "number": 162, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/162", "body": "In order to get new updated SSH configuration please perform next steps:\n\nClone repository\n\nRun:\n\ncd eros-data-collector\nmake install\n\n\n\n\n"}
{"title": "LDI-1117 support new di message type", "number": 163, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/163", "body": "LDI-1117: support new types and rename old\n"}
{"title": "LDI-1131 separate build", "number": 164, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/164", "body": "LDI-1131: separating build for ut and build for routers in pipeline\n\n"}
{"title": "LDI-100 compile pcapd", "number": 165, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/165", "body": "adding some scripts of charter opensync usage"}
{"comment": {"body": "Can you please check the scripts here: [https://storage.googleapis.com/levl-software-tools/askey\\_wifi6/scripts\\_2023\\_04\\_24.tar.gz](https://storage.googleapis.com/levl-software-tools/askey_wifi6/scripts_2023_04_24.tar.gz).  \nThese were used for installation in the past month, and specifically SSH enablement was modified to work properly on boot.", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/165/_/diff#comment-397203361"}}
{"comment": {"body": "Amendments will be done in a separate PR\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/165/_/diff#comment-397234929"}}
{"title": "[Snyk] Security upgrade tornado from 6.2 to 6.3.2", "number": 166, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/166", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\ncpe/utils/json/nlohmann/doc/mkdocs/requirements.txt\n\n\n\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  556/1000  Why?* Recently disclosed, Has a fix available, CVSS 5.4  | Open Redirect  SNYK-PYTHON-TORNADO-5537286 |  tornado: 6.2 - 6.3.2  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "LDI-1199: adding LEVL_LAB", "number": 167, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/167", "body": ""}
{"comment": {"body": "The v0\\_ing was for this purpose, our agent. let\u2019s remove one of them if redundant\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/167/_/diff#comment-396619152"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} @{5dbeb866c424110de52552cc} another note, do we still need this notation? haven\u2019t we updated the schema to be as Cujo? partner\\_id can define that it\u2019s our LAB\u2026\n\nI want to make sure that we remove dependencies and not add new ones...", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/167/_/diff#comment-396784254"}}
{"comment": {"body": "@{5f82bf320756940075db755e} This is aimed to make the `activate_schema_translation` configuration unnecessary. We add this type such that the classifier will detect it and do the translation. We don\u2019t have support for the new schema on the CPE.\n\nRegarding `V0_INGESTION`, I think you are right, this was probably a placeholder for what we now call `LEVL_LAB`. So no need to add a new one, we can use this one. @{6265307b185ac200692f9bd9} ", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/167/_/diff#comment-396805625"}}
{"title": "LDI-1287: splitting the cpe build to reduce ci pipeline duration", "number": 168, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/168", "body": ""}
{"title": "LDI-1384: porting of scripts", "number": 169, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/169", "body": "charter routers scripts from google storage to s3"}
{"comment": {"body": "This version is different from the one in S3 - which one is the correct one?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/169/_/diff#comment-404504191"}}
{"comment": {"body": "You are right. When this PR will me merged I\u2019ll manually upload this script to S3", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/169/_/diff#comment-404594748"}}
{"title": "Set Kafka API Version", "number": 17, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/17", "body": "Sometimes can't connect to broker without api_version, setting latest by default\n"}
{"title": "Sometimes can't connect to broker without api_version, setting latest by default", "number": 18, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/18", "body": ""}
{"title": "Bugfix/Include DHCP ACK in the session", "number": 19, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/19", "body": "Now forcing to add the DHCP ACK packet to the session we send, after it was found out it was missing from it"}
{"title": "Testifier", "number": 2, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/2", "body": "phase one.\nonly pull data from s3 and push it to kafka"}
{"comment": {"body": "@{6085103f5797db006947d59a} can you align the pull request and CI to merge", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/2/_/diff#comment-226005468"}}
{"comment": {"body": "we have too many places with topic names, we should think of more centralized configuration. otherwise, one can change here and not on the backend side\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/2/_/diff#comment-226328366"}}
{"title": "Logging and API Version", "number": 20, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/20", "body": "Logging only on state changes\nSaving cyclic logs to /tmp\nRemoved kafka api version\n\n"}
{"title": "Bugfix/session exceeding max queue size", "number": 21, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/21", "body": "Increased the queue size to 500, to avoid cases where some packets are trimmed and not sent in the session"}
{"title": "Removed the condition in session_manager that makes us skip all the data we collected", "number": 22, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/22", "body": "When type_obj is created, its empty by definition, since we havent filled it yet. Checking if its empty makes us skip all the adding procedure, and we send empty data structs to the classifer."}
{"title": "Create connection event api", "number": 23, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/23", "body": "Add \"connection_event\" struct that will replace RawSession\nparsing schema with full breakdown of fields\nsession_manager basic support in building new connection event\nfill and send ConnectionEvent\nRemoved the condition in session_manager that makes us skip all the data we collected\ncreate connection between devices list and originating interfaces\nMake connected devices a dict from macs to device_info including iface index\nDelete unused device list class\nadd 2nd layer in parsing struct - packet types\ncomplete feature set in connection event proto\nempty parsing and features structures in edge\nTemporary edit in bitbucket-pipelines to bump the data-collector version\n[skip ci] bumped api package version, build 169, new version 0.4.1\nRemoved the patch from bitbucket-pipelines\nadd conversion tool RawSession -> ConnectionEvent\n\n"}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} @{5dbeb866c424110de52552cc} Very clean job and code.. well done.", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/23/_/diff#comment-254524725"}}
{"title": "Native CPE build env", "number": 24, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/24", "body": "build docker env and use it to build hello world both for host and for ipq6018 target\nadd CI integration and use ECR build image\n\n"}
{"title": "Now calling clean before build to make sure we actually re-build", "number": 25, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/25", "body": ""}
{"title": "Added googletest support with an initial test", "number": 26, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/26", "body": ""}
{"comment": {"body": "1. Make `mac_buffers`  independant library, with cmake being accessed from top level CmakeLists.txt.\n2. Add make routine to run all unit tests and call it in `bitbucket-pipelines.yml` under the `cpe-test` step.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/26/_/diff#comment-256739563"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} how the test results can be integrated to ReportPortal? junit xml or similar?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/26/_/diff#comment-256825570"}}
{"title": "Added initial versions of general definitions file and buffer_manager.h to provide the proper API", "number": 27, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/27", "body": ""}
{"title": "dispatch skeleton wrapper - not tested", "number": 28, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/28", "body": "upload to codeartifact\ntypo\nforce upload\nforce upload\nforce upload\nforce upload\nforce upload\nforce upload\nforce upload\nforce upload\ntest\ntest\ntest\ntest\ntest\ntest\n[skip ci] bumped package version, new version 0.1.1\npushing package\nadded package name in commit message\nfix build\nfix build\nfix build\nSometimes can't connect to broker without api_version, setting latest by default\nfixed package git push\n[skip ci] bumped api package version, build 143, new version 0.2.1\nadded alck message on package update\n[skip ci] bumped api package version, build 144, new version 0.3.1\nbetter message\n[skip ci] fix package pipe\nNow forcing to add the DHCP ACK packet to the session we send, after it was found out it was missing from it\nLogging only on state changes + saving cyclic logs to /tmp + removed kafka api ver\nIncreased the queue size to 500, to avoid cases where some packets are trimmed and not sent in the session\nAdd \"connection_event\" struct that will replace RawSession\nparsing schema with full breakdown of fields\nsession_manager basic support in building new connection event\nfill and send ConnectionEvent\nRemoved the condition in session_manager that makes us skip all the data we collected\ncreate connection between devices list and originating interfaces\nMake connected devices a dict from macs to device_info including iface index\nDelete unused device list class\nadd 2nd layer in parsing struct - packet types\ncomplete feature set in connection event proto\nempty parsing and features structures in edge\nTemporary edit in bitbucket-pipelines to bump the data-collector version\n[skip ci] bumped api package version, build 169, new version 0.4.1\nRemoved the patch from bitbucket-pipelines\nadd conversion tool RawSession -> ConnectionEvent\nfix unused flake files\nChange agent config type in ConnectionEvent\nbuild docker env and use it to build hello world both for host and for ipq6018 target\nrevert agent_config type change, will be done later\nadd CI integration and use ECR build image\nNow calling clean before build to make sure nothing is cached and ignored\ndispatch skeleton wrapper - not tested\n\n"}
{"comment": {"body": "[https://github.com/edenhill/librdkafka/blob/master/examples/producer.cpp](https://github.com/edenhill/librdkafka/blob/master/examples/producer.cpp){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/28/_/diff#comment-256958066"}}
{"title": "Py agent: Connected devices data", "number": 29, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/29", "body": "Fill in the connected devices list with mock data containing only the current device\n"}
{"title": "Agent Integration - Initial", "number": 3, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/3", "body": "Basic agent with packet buffering per mac, extraction upon dhcp ack\n"}
{"comment": {"body": "we need to remove now\\(\\) usage as we have discussed, otherwise won\u2019t be able to replay data to agent as well..", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/3/_/diff#comment-229748839"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} i have moved the merge to dev branch and not to master.. there is a conflict.. let\u2019s fix that and merge to dev", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/3/_/diff#comment-229750154"}}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9-slim", "number": 30, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/30", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.9-slim, as this image has only 37 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity | Priority Score / 1000 | Issue | Exploit Maturity |\n| --- | --- | --- | --- |\n|  | 614 | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893 | No Known Exploit |\n|  | 500 | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601 | No Known Exploit |\n|  | 714 | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403 | No Known Exploit |\n|  | 714 | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403 | No Known Exploit |\n|  | 614 | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406 | No Known Exploit |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Feature/buffers manager implementation", "number": 31, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/31", "body": "This PR adds the implementation of the BuffersManager.\nAfter integrating with the googletest library in our previous PR, buffers_manager_tests.cpp was added as well. Those unitests run on every pipeline and can also run manually.\n\n"}
{"title": "Feature/dispatch kafka", "number": 32, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/32", "body": "Now calling clean before build to make sure nothing is cached and ignored\ndispatch skeleton wrapper - not tested\nDirectories fix\nAdded initial code to dispatcher_tests.cpp\ncpe -> CPE, plus other cosmetic changes\nAdded CMakeLists to dispatch directory\nNow compiling dispatcher code as well\nSeparated kafkaDispatch to kafkaDispatch.h and kafkaDispatch.cpp\nFixed the methods to match the interface\nAdded a test to our kafka dispatcher\nNames fixing\nRemoving unique_ptr usages for now\nTrying to link cmakelist with rdkafka library\nAdded missing rdkafka++ library install to the Dockerfile\n\n"}
{"title": "Connection Handling Components", "number": 33, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/33", "body": "Wlanconfig parsing - Functions to parse a single wlanconfig sta list print.\nDevices state - In-memory storage mechanism for last events of each device (mac). The connection handler adds events to it based on changes in extracted connections data and the connection manager reads and removes events from it.\nConnection handler - Holds local set of connections, calls parser to get new set of connections. compares sets and deduces connection events, disconnection events and event updates.\n\n"}
{"title": "Feature/working kafka dispatch", "number": 34, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/34", "body": "dispatch skeleton wrapper - not tested\nDirectories fix\nAdded initial code to dispatcher_tests.cpp\ncpe -> CPE, plus other cosmetic changes\nAdded CMakeLists to dispatch directory\nNow compiling dispatcher code as well\nSeparated kafkaDispatch to kafkaDispatch.h and kafkaDispatch.cpp\nFixed the methods to match the interface\nAdded a test to our kafka dispatcher\nNames fixing\nRemoving unique_ptr usages for now\nTrying to link cmakelist with rdkafka library\nAdded missing rdkafka++ library install to the Dockerfile\nRemoved unnecessery library targeting\nTrying to see if we compile without ipq6018\nAttempting to set debug and release mode to decide if to compile the unitests or not\nRemoved debug and release for now\nDisabled ipq6018 compilation for now\nNow running the dispatcher unitest in the pipeline\nNow running the dispatcher unitest in the pipeline\nReplacing the while condition to an if condition to see if it solves the problem\nadding thread::join() to see if it solves the exception\nAdded debug prints\nTrying to move the poll outside of the thread\nNow printing if not delivering the message\nAttempting to wait until the message is sent\nAdded more debug logs\nChanging the thread to use while loop to see if it is affects the callback\nAnother attempt to change the polling calls\nfix compilation in rdkafka c++ lib and linkage\nworking dispatch library C++\n\n"}
{"title": "Ongoing user agent event", "number": 35, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/35", "body": ""}
{"comment": {"body": "[https://danielmiessler.com/study/tcpdump/](https://danielmiessler.com/study/tcpdump/){: data-inline-card='' }\n\ntcpdump\u00a0**-vvAls0**\u00a0|\u00a0**grep**\u00a0**'User-Agent:'**\n\nlet\u2019s see if this wirks better. to catch user agents", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/35/_/diff#comment-258499994"}}
{"comment": {"body": "@{6085103f5797db006947d59a} what is this for?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/35/_/diff#comment-258659380"}}
{"comment": {"body": "ask @{5dbeb866c424110de52552cc} ", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/35/_/diff#comment-259820036"}}
{"comment": {"body": "@{6085103f5797db006947d59a} Should be removed", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/35/_/diff#comment-261779436"}}
{"title": "Feature/cpp static analysis", "number": 36, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/36", "body": "dispatch skeleton wrapper - not tested\nDirectories fix\nAdded initial code to dispatcher_tests.cpp\ncpe -> CPE, plus other cosmetic changes\nAdded CMakeLists to dispatch directory\nNow compiling dispatcher code as well\nSeparated kafkaDispatch to kafkaDispatch.h and kafkaDispatch.cpp\nFixed the methods to match the interface\nAdded a test to our kafka dispatcher\nNames fixing\nRemoving unique_ptr usages for now\nTrying to link cmakelist with rdkafka library\nAdded missing rdkafka++ library install to the Dockerfile\nRemoved unnecessery library targeting\nTrying to see if we compile without ipq6018\nAttempting to set debug and release mode to decide if to compile the unitests or not\nRemoved debug and release for now\nDisabled ipq6018 compilation for now\nNow running the dispatcher unitest in the pipeline\nNow running the dispatcher unitest in the pipeline\nReplacing the while condition to an if condition to see if it solves the problem\nadding thread::join() to see if it solves the exception\nAdded debug prints\nTrying to move the poll outside of the thread\nNow printing if not delivering the message\nAttempting to wait until the message is sent\nAdded more debug logs\nChanging the thread to use while loop to see if it is affects the callback\nAnother attempt to change the polling calls\nfix compilation in rdkafka c++ lib and linkage\nworking dispatch library C++\nAdd basic UT for kafka dispatch\nadd cppcheck on source code\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0rc1-slim-buster", "number": 37, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/37", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.10.0rc1-slim-buster, as this image has only 70 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0rc1-slim-buster", "number": 38, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/38", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.10.0rc1-slim-buster, as this image has only 70 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Small fixes in connectivity components and env", "number": 39, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/39", "body": "missing fixes to PR comments and some other clean ups"}
{"comment": {"body": "Unfortunate reformatting of this file and mac buffers unit tests. Had to add because of small changes, couldn\u2019t cherry pick them.", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/39/_/diff#comment-258810307"}}
{"title": "Dev", "number": 4, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/4", "body": "initial commit\nreplay main & dockerfile\nreplay.py edited\nreadme\nfix typo\nREADME.md\nremoved remote\nfixed acording to archiver update\napply CI/CD policy dev branch -> dev namespace master branch -> staging (manually to production) namespace all others CI only\ninject topic name\nfix kafka address\nsome fixes\nfixed readme\nfew changes\nadd .env file for testifier\nREADME.md edited\nREADME.md edited\nflake8 fix\nflake fix\nmerge master\nrestore files\nrestore from master\nadded run.sh file and fixed dockerfile\nremoved printing from unimplemented functions\n\n"}
{"title": "Stdin to TCP UDS Socket App + wlanconfig Extractor", "number": 40, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/40", "body": "Small application to bridge stdout to uds tcp socket. \nusage: stdin_to_uds dest_socket_path\n  Included compiled binary to ipq6018 (LFS): cpe/utils/netcat/build/ipq6018/stdin_to_uds\nnew extract_macs.sh with more details that assumes ath0 and ath1, and concatanates all connections to one comma-separated output.\n\n"}
{"title": "main thread with hardcoded configuration and connection detection", "number": 41, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/41", "body": ""}
{"title": "intergate dispatch with device state to ship the connections", "number": 42, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/42", "body": ""}
{"comment": {"body": "Can\u2019t \u201cdevice state\u201d be the only one who depends on dispatch?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/42/_/diff#comment-259325847"}}
{"comment": {"body": "Sure, it can and it should, once we restructure the cmake.. \n\nI will try to get to this today.. ", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/42/_/diff#comment-259325957"}}
{"title": "Feature/add handlers", "number": 43, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/43", "body": "Adding the Ethernet & RadioTap handlers, along with the bash extractor. The extractor is both for Ethernet and RadioTap packets extracting, depends on the first parameter it gets ('eth' / rt').\nThe API of the handlers (process_data() method) is copied from the libpcap library (as described here). Even though we dont actually use that library now, it is convenient and clear enough for us to copy it.\nAs for now there are two very basic unitests that checks mainly the mac address extracting."}
{"comment": {"body": "We should put aside a TODO to support .1q headers. We won\u2019t encounter them now but they\u2019re unavoidable in the future.", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/43/_/diff#comment-259325320"}}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0-slim-buster", "number": 44, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/44", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.10.0-slim-buster, as this image has only 70 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0-slim-buster", "number": 45, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/45", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.10.0-slim-buster, as this image has only 70 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "fix: Dockerfile to reduce vulnerabilities", "number": 46, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/46", "body": "The following vulnerabilities are fixed with an upgrade: -  -  -  -  - "}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim-buster", "number": 47, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/47", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.9.7-slim-buster, as this image has only 70 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Feature/protobuf integration", "number": 48, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/48", "body": "Protobuf integration.\nGenerated the ConnectionEvent related code with the Protobuf library and creating a simple thin event in devices_state, to check E2E ability.\nThis PR purpose is adding all the last components needed to prove our E2E communication. More changes are yet to come, including making the event more real like.\nMost of the changes needed to make this component work are committed to our docker, as can be seen when compiling to ipq6018 in our CMakeLists files."}
{"comment": {"body": "This is the path to the required headers? But doesn\u2019t it contain a all lot of other files?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/48/_/diff#comment-259828396"}}
{"title": "[Snyk] Security upgrade ubuntu from xenial to xenial-20210114", "number": 49, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/49", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/dhcpd6-server/Dockerfile\n\nWe recommend upgrading to ubuntu:xenial-20210114, as this image has only 51 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-UBUNTU1604-LIBGCRYPT20-1585790   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "minor hot fix", "number": 5, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/5", "body": ""}
{"title": "[Snyk] Security upgrade ubuntu from xenial to impish-20211015", "number": 50, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/50", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/dhcpd6-server/Dockerfile\n\nWe recommend upgrading to ubuntu:impish-20211015, as this image has only 11 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-UBUNTU1604-LIBGCRYPT20-1585790   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim", "number": 51, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/51", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.9.7-slim, as this image has only 38 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Feature/fill events with data", "number": 52, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/52", "body": "Now calling clean before build to make sure nothing is cached and ignored\nAdded googletest support with an initial test\nIgnoring googletest directory in flake\nIgnoring inner test files in googletest repo\nRemoved the local clone of googletest; Using FetchContent instead\nAdded initial versions of general definitions file and buffer_manager.h to provide the proper API\ndispatch skeleton wrapper - not tested\nParsing gtest results in the pipeline\nRemoved unnecessery command from cpe-test\nSniffedData -> CapturedData\nAdded raw_data_len member to CapturedData. Setting a default size to the CircularQueue in the constructor\nRemoved the mutex usage from buffers_manager.h\nBuffers Manager implementation and initial unitests\nFied BuffersManager::get_buffers() time compares\nAdded unitests documentation\nAdded CMakeLists to mac_bufers as well\nFixing makefile\nMinor documentation and compilation fixes\nDirectories fix\nAdded initial code to dispatcher_tests.cpp\ncpe -> CPE, plus other cosmetic changes\nAdded CMakeLists to dispatch directory\nNow compiling dispatcher code as well\nSeparated kafkaDispatch to kafkaDispatch.h and kafkaDispatch.cpp\nFixed the methods to match the interface\nAdded a test to our kafka dispatcher\nNames fixing\nRemoving unique_ptr usages for now\nTrying to link cmakelist with rdkafka library\nAdded missing rdkafka++ library install to the Dockerfile\nRemoved unnecessery library targeting\nTrying to see if we compile without ipq6018\nAttempting to set debug and release mode to decide if to compile the unitests or not\nRemoved debug and release for now\nDisabled ipq6018 compilation for now\nNow running the dispatcher unitest in the pipeline\nNow running the dispatcher unitest in the pipeline\nReplacing the while condition to an if condition to see if it solves the problem\nadding thread::join() to see if it solves the exception\nAdded debug prints\nTrying to move the poll outside of the thread\nNow printing if not delivering the message\nAttempting to wait until the message is sent\nAdded more debug logs\nChanging the thread to use while loop to see if it is affects the callback\nAnother attempt to change the polling calls\nfix compilation in rdkafka c++ lib and linkage\nadd connection handler, wlanconfig parser and devices state in-memory store\nbuild right components\nworking dispatch library C++\nAdd basic UT for kafka dispatch\nmissing fixes to PR comments and some other clean ups\nsmall application to bridge stdout to uds tcp socket\nmain thread with hardcoded configuration and connection detection only\nAdded handlers implementation\nAdded the extractor script\nRemoving spaces from wc output\nFixing netcat call in the extractor\nsmall fixes\nAdded a first test to the handlers class that checks ethernet basic parsing\nintergate dispatch with device state to ship the connections\nAdded radiotap parsing unitest\nAdding UtHandlers as a friend class of BuffersManager\nmac address to lowercase\nNow running the handlers unitests in the pipeline\nGenerated h/cc files from the schemas. Added the generation command to the makefile\nRe-generated the protobuf files with the docker version of protobuf to avoid include changes between versions\nCompiling protobuf with arm as well\nMoved the generated protobuf classes under cpe directory. Created an initial ConnectionEvent in devices_state we can work with\npcap handling + socket and sources support generalization\nupdate wlanconfig pcap scrip\nupdate wlanconfig extraction with usecond precision + date utility\nNow serializing the connection event and sending it through the dispatcher\nUpdated the generated protobuf files with the updated version\nUpdated the CMake of connection_handler with the protobuf library as well\nsimplified CMakeLists\nNow using unique_ptr when creating the event to send to kafka\nAdding debug print\nRemoved unique ptr usage\nRemoved unnecessery memory allocation of the serialized message string\nAdded documentation about memory management\nNow compiling the main loop as well|\nadd state manager\nNow creating ConnectionEvent(s) in state_manager.cpp\nFixing read_func initialization\nFixed double assigning of agent_info\nNow initializing the kafka dispatcher\nFixed reading from socket, give up when no data waiting\ninitial setup.sh script to copy the required files to the provide AP\nAdded logger and json - for now compiling inside the makefile will move to docker\n\n"}
{"title": "Feature/cfo estimator", "number": 53, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/53", "body": "Add TFLite-Micro installation scripts\nAdd CFO estimator v2 (for 5GHZ AX80 + 2.4GHz AX20 packets)\n\n"}
{"title": "split requierments file", "number": 54, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/54", "body": ""}
{"title": "Feature/static analysis cpp", "number": 55, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/55", "body": "\n\nadd cppcheck CI\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.8-slim-buster", "number": 56, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/56", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.9.8-slim-buster, as this image has only 71 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "add disconenction event", "number": 57, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/57", "body": ""}
{"title": "Feature/csi slopes preprocessing", "number": 58, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/58", "body": "Add CFR slopes handler stub for later integration\n\nAdd CFR slopes preprocessing math ready for integration\n\nWith tests for 5ghz packets and 2.4ghz packets\n\n\n\nSome refactoring to chains since its a common procedure\n\n\n"}
{"title": "Feature/cpp check updated ci", "number": 59, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/59", "body": "add in comemnts the blackduck runner\nadd cppcheck\n\n"}
{"title": "few changes", "number": 6, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/6", "body": "few changes\n\n"}
{"title": "Feature/add ongoing event", "number": 60, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/60", "body": "Added support with ongoing events.\nNote: I still didnt remove the debug logs from this branch. While we still look over this and test it, I prefer them to still be here. They will be removed before we merge it."}
{"title": "Now filling connection_ts and event_sent_ts in the connection event", "number": 61, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/61", "body": ""}
{"title": "Not sending a connection event for already connected devices if the agent just started running", "number": 62, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/62", "body": "This goal is achieved in two ways:\n\nNot sending an event if there arent any ethernet or radiotap packets\nChecking the connection association duration since the last sampling and making sure its not more than 1 seconds\n\n"}
{"title": "Feature/add cfr handlers", "number": 63, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/63", "body": "Cfo estimator and slopes\n\n"}
{"title": "Plain cfo extraction", "number": 64, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/64", "body": "first building blocks for cfr service\nadd base for tests\nbase for cfr packet struct v5\nignore build\nSome fixes\ncfr basic parsing and test\nfix build\nfix expected result and check magic\nstart to add dependency on tflite-micro\ndir structure\nadd get_chains for 80mhz\ncmakelists.txt\nworking build\npaths fix\nFix\ngot chains\nget_norm_amp\nfinish estimator pipeline\nmodel works for 5ghz 80mhz cfr packet input\nstream parser with test\nPut the data in a separate file\nstart working on compiling tflite-micro library\nAdd installer/configuration for tflite-micro\nwith regard to new paths\ncleanup\nAdd LFSed library\nlfs the model\ncleanup and more tests\nFixed linking issues on host\nAdd v2 models and have 2 models: 5g and 24g Update cosmetics to estimator\nFinish support for 2.4ghz Add unittest for 2.4ghz\nprepare template for slopes preprocessing\nSeparate internals from dispatcher, for UT purposes\nbound checking and aliasing\nMore error checking\nsave progress\nMore code for slopes\nAdd helper code; refactor\nTemplates\nadd pearson stuff\nreally add pearson stuff\ndone with internal functions\nwrap it up to start tests\ntests are failing, lol\ntests are passing for some reason\nAdd test for 5ghz 80mhz\nmakefile to run new test\nFix compilation issues\nCFRslopes to handle the rolling window internally\nAdd out_of_range handling\ncfr service cfo extraction functionality\nextract required cfr sample data and integrate physical data buffering into event_processor\ncfo in mac buffers\nadd physical data to conection event\nfix physical packet in buffers\nNow filling connection_ts and event_sent_ts in the connection event\nfix dispatcher reconnections\nmake it link with cfo estimator\nAdd cfr slopes handler\nserialize cfo to float\nserialized everything\nupdate ap_config\nfix dispatcher timestaping\nAdd missing define\nNot sending a connection event for already connected devices if the agent just started running\nMake a mess with definitions\n\n"}
{"title": "Release/alpha l1 l7", "number": 65, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/65", "body": ""}
{"title": "cleanup dispatch kafka and cppcheck", "number": 66, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/66", "body": ""}
{"title": "Add bash pinger", "number": 67, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/67", "body": "will start pinging for 10 seconds after device connection.\nStart script with cfr_starter.sh IFACE BANDWIDTH\nwhere iface={0, 1}\nwhere bandwidth={0, 1, 2}"}
{"title": "CFRSlopes: Fixed missing assignment by reference", "number": 68, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/68", "body": "Also fix CFO sign convertion\n\n"}
{"title": "compiled protobuf artifacts with original protibuf as well", "number": 69, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/69", "body": ""}
{"comment": {"body": "@{5f82bf320756940075db755e} Can this be deleted?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/69/_/diff#comment-311880675"}}
{"title": "automation env", "number": 7, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/7", "body": "automation env"}
{"title": "Read temperatures from platform", "number": 70, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/70", "body": ""}
{"title": "fix bash removing newline", "number": 71, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/71", "body": ""}
{"title": "CFR Service - ignore zero len data returned from handler", "number": 72, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/72", "body": "zero len processed data means invalid data - ignore"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim-buster", "number": 73, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/73", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.9.7-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Fix slopes filter", "number": 74, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/74", "body": "Correct nan filter\nCorrect mean with nan\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.8.12-slim-buster", "number": 75, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/75", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.8.12-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 400  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "ccpcheck fixes", "number": 76, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/76", "body": ""}
{"title": "Feature/deal with higher cfr rate", "number": 77, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/77", "body": "Handle CFR rate of 5ms interval\nAdd placeholder for reading rate from configuration\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim-buster", "number": 78, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/78", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.9.7-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-DEBIAN10-LIBGCRYPT20-1582894   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to rc-slim-buster", "number": 79, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/79", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:rc-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-DEBIAN10-LIBGCRYPT20-1582894   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Raw session add vault id", "number": 8, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/8", "body": "Changes from branches:\n\nagent_config\nraw_session_add_vault_id\n\nChanges:\n\nReorganized agent to separate code from platform-specific config\nAdded IDs, update proto version\nConform to terminology\nSet parameters (IDs, topic name) in setup\nAdd essid extraction\nRun module in startup script\n\n"}
{"comment": {"body": "@{5dbeb866c424110de52552cc} can you fix the conflicts?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/8/_/diff#comment-230847339"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/8/_/diff#comment-230847458"}}
{"title": "Bugfix / Temperature assignment", "number": 80, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/80", "body": "When looking at the CFO-related bugs that were opened recently, we can see that for some of the bugs we couldnt match a model because of weird values in the model itself. From time to time we saw enormous values that just dont make sense.\nIt didnt always reproduce, but after several attempts it turned out that we got those values from the agent, and it wasnt an inner problems in the classifier.\nWhen adding logs to both classifier and agent, I saw the following output:\n| Classifier | Agent |\n| --- | --- |\n| avg_cpe_temperature() 55.16666793823242 | metadata.avg_cpe_temperature is 55.1667 |\n| avg_cpe_temperature() 55.16666793823242 | metadata.avg_cpe_temperature is 55.1667 |\n| avg_cpe_temperature() 1.5587790342525485e+38 | Failed reading temperature file #0 [error] Failed extracting batch metadata |\n| avg_cpe_temperature() 55.16666793823242 | metadata.avg_cpe_temperature is 55.1667 |\n| avg_cpe_temperature() 55.16666793823242 | metadata.avg_cpe_temperature is 55.1667 |\nWhen looking at the agent code, we can see that if we have a problem with reading the temperature, we dont fill that value and keep sending the event. It causes us send garbage values from the memory that will later be used in the calculation of the CFO model.\nSome notes:\n\nThough the reading failure is indeed a problem, we need to come up with a solution for those cases. Every file system has its own limitations and it does make sense that a reading attempts can fail from time to time, even if its rare.\nEven though assigning the temperature to 0 is does not represent the actual temperature value, out of 200-400 packets that we usually get in a connection it shouldnt affect the final model that much\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0rc1-slim-buster", "number": 81, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/81", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.10.0rc1-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-DEBIAN10-LIBGCRYPT20-1582894   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0-slim-buster", "number": 82, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/82", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.10.0-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-DEBIAN10-LIBGCRYPT20-1582894   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Agent handling of quick band transition", "number": 83, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/83", "body": "In the case of fast band transition keeping the same MAC, we get both disconnection and connection in the same timestamp (based on wlanconfig stations report). Up until now, we handled this case as if the disconnection arrived after the connection, which made us disregard the connection.\nIve also added a small buffer to the backward data extraction interval for these cases, to account for the variation/delay in wlanconfig report time. This is to make sure that we get all radiotap packets."}
{"title": "Migrate full setup for CPE agent", "number": 84, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/84", "body": "The best of all setup worlds, now for cpe.\nThe old (and renewed) wizard is available in cpe/utils/ipq_setup/setup.sh\n\nSupports setting and saving credentials.\nNew: Optional automatic rsync to AP in firmware mode.\n\nFOR NOW, the build artifacts (levl_agent_main, cfr_service_main) must still exist locally, either by building them or by extracting them from pre-prepared archive.\nOnce firmware mode setup is done, youll have a new /levl dir on the AP.\n\nThe ag alias leads to /levl/utils, which is the location of the important scripts.\nAfter update you can either run /levl/bootstrap.sh manually or reboot the AP (bootstrap.sh runs on boot too).\n\nOnce bootstrap.sh ran at least once, you can run run.sh from /levl/utils. Two important changes were done in run.sh:\n\nIt also accounts for applying some configurations, which should only be done once (if there are no changes in configuration). If you already ran run.sh once during the AP uptime, you can skip these configurations (~20 seconds) and run the agent only by using run.sh -q.\nIt hangs after starting all of the scripts (instead of running in background), which means that you dont need to use kill.sh, just pressing ctrl+c should kill all of the processes.\n\n\n\n"}
{"title": "Mock temperature extraction for tests", "number": 85, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/85", "body": "temperature reading func as StreamParser argument, use mock in tests\nexclude cpe from flake\nfix static analysis\n\n"}
{"title": "Kafka broker mock for ut", "number": 86, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/86", "body": "add local kafka broker to be used for dispatcher ut\nadd missing dockerfile for kafka broker mock\nuse extra-index-url for codeartifact; enablde docker-compose for ut step\n\n"}
{"comment": {"body": "name convention for function", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/86/_/diff#comment-270211048"}}
{"comment": {"body": "I was following `dr_msg_cb`, how should it be different?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/86/_/diff#comment-270402607"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} You are right.. ", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/86/_/diff#comment-270691043"}}
{"title": "[Snyk] Security upgrade ubuntu from xenial to xenial-20210416", "number": 87, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/87", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/dhcpd6-server/Dockerfile\n\nWe recommend upgrading to ubuntu:xenial-20210416, as this image has only 52 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-UBUNTU1604-LIBGCRYPT20-1585790   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "DHCP ACK address extracting - don't take broadcast address", "number": 88, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/88", "body": "When looking for the DHCP ack and extracting the mac address, don't consider the broadcast address that we sometimes see in this packet as the right address"}
{"title": "Make writing to UDS nonblocking", "number": 89, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/89", "body": "Potential solution for all of the following open bugs:\n\n\n\n\nThe stdin_to_uds process blocked on write() at sporadic incidents. Sometimes for a number of seconds, sometimes endlessly. I suspect that some kind of race condition with other, concurrent accesses to the socket. Making the fd nonblocking could create a very small delay in the writes in case the socket is busy, but will untangle the mutual locks."}
{"title": "Create temp parameters dir if not exists", "number": 9, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/9", "body": ""}
{"title": "Removed duplicate documentation from cpp file, it's already in the header", "number": 90, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/90", "body": ""}
{"title": "Removed more duplicate documentation that's already in the header files", "number": 91, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/91", "body": ""}
{"title": "Update dev pipeline", "number": 92, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/92", "body": ""}
{"title": "Restore levlagent", "number": 93, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/93", "body": "By default, after setup, agent will run in background."}
{"title": "add new routers config and old certs", "number": 94, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/94", "body": ""}
{"title": "update script to allow downloading required artifacts from s3", "number": 95, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/95", "body": ""}
{"title": "Handle EAgain on non-blocking socket writes", "number": 96, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/96", "body": "Don't crash on write eagain. Try to write to the socket several more times"}
{"title": "Added connectivity_only_setup.sh script", "number": 97, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/97", "body": "Added connectivity_only_setup.sh script, that allow us to generate a minimal squashfs directory, in order to create a thin image as possible for the APs.\nThis script should be only used once for each router. After generating the basic squashfs directory, creating a suitable image from it in levlcompute, and running sysupgrade with it on the router, the full setup.sh script should be used."}
{"title": "[Snyk] Security upgrade ubuntu from xenial to xenial-20210114", "number": 98, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/98", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/dhcpd6-server/Dockerfile\n\nWe recommend upgrading to ubuntu:xenial-20210114, as this image has only 52 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-UBUNTU1604-LIBGCRYPT20-1585790   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n|    | 614  | Allocation of Resources Without Limits or Throttling SNYK-UBUNTU1604-SYSTEMD-1320131   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "SSL support for kafka dispatcher", "number": 99, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/99", "body": "To get SSL working:\n\nA directory containing AP-specific cert and key  was added in /levl/config/ssl.\nAn OpenVPN certificate to the cluster was added (alongside the existing certificate - 2 tunnels will be created).\n\nImportant: Currently, the certificates (MKS SSL and cluster VPN) are for prod environment. WIll be updated to dev once services will be up and running in dev env.\nThe VPN certificate must be password protected, so a new file /etc/openvpn/auth.txt was added. The password value is being set during setup, and has a default value.\n2 New fields were introduced to ap_config.json under eros_info->dispatch:\n\nkafka_ssl: Should be on or off. on by default.\nssl_assets_path: Refers to \"/levl/config/ssl\".\n\n"}
{"title": "docker build", "number": 1, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/1", "body": ""}
{"title": "MEROSP-1325 minor ui fixes", "number": 10, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/10", "body": "restore vaults tab\nEST\n\n\n"}
{"title": "add total device count", "number": 11, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/11", "body": ""}
{"title": "fix refresh rate", "number": 12, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/12", "body": ""}
{"title": "Feature/MEROSP-1325 v1.5 poc demo ui preparation", "number": 13, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/13", "body": "fix refresh checkbox\nfix glowing effect\nadd more col\n\n"}
{"title": "fix date", "number": 14, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/14", "body": ""}
{"title": "Feature/MEROSP-1325 v1.5 poc demo ui preparation", "number": 15, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/15", "body": "fix hidden hostname fade\nformat caps\n\n"}
{"title": "change col names + sort devices list", "number": 16, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/16", "body": ""}
{"title": "build on tag creation", "number": 17, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/17", "body": ""}
{"title": "create ids to all col's", "number": 18, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/18", "body": ""}
{"title": "[Snyk] Security upgrade moment from 2.27.0 to 2.29.4", "number": 19, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/19", "body": "Snyk has created this PR to fix one or more vulnerable packages in the npm dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\n\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\npackage.json\npackage-lock.json\n\n\n\nVulnerabilities that will be fixed\nWith an upgrade:\nSeverity                   | Issue                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|:-------------------------|:-------------------------|:-------------------------\n | Directory Traversal  SNYK-JS-MOMENT-2440688 |  No  | No Known Exploit \n | Regular Expression Denial of Service (ReDoS)  SNYK-JS-MOMENT-2944238 |  No  | Proof of Concept \nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "API definition transferred by SwaggerHub", "number": 2, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/2", "body": ""}
{"title": "MEROSP-789", "number": 20, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/20", "body": "vaults route - vaults -> vaults/debug devices route - devices -> vaults/{id}/devices remove device capabilities\nremove Device Capabilities section"}
{"title": "MEROSP-2693", "number": 21, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/21", "body": "add step to push to cujo docker image\nadd step to push to cujo docker image\nadd step to push to cujo docker image\nadd step to push to cujo docker image\n\n"}
{"title": "align-ui-to-api", "number": 22, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/22", "body": "type_os -> os type_model -> model type_vendor -> vendor\ndevice_type  typing_result\nlast_connected_time -> updated_on  \nremove ip column\nmac_addr -> mac\nprev_rand_mac_addresses -> prev_macs"}
{"title": "ci cd", "number": 3, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/3", "body": ""}
{"title": "[Snyk] Security upgrade node from latest to 17-slim", "number": 4, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/4", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\ndocker/Dockerfile\n\nWe recommend upgrading to node:17-slim, as this image has only 36 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 500  | Use After Free SNYK-DEBIAN11-GLIBC-1296898   | No Known Exploit   |\n|    | 500  | Improper Input Validation SNYK-DEBIAN11-PYTHON39-1290158   | No Known Exploit   |\n|    | 500  | Improper Input Validation SNYK-DEBIAN11-PYTHON39-1290158   | No Known Exploit   |\n|    | 500  | Improper Input Validation SNYK-DEBIAN11-PYTHON39-1290158   | No Known Exploit   |\n|    | 500  | Improper Input Validation SNYK-DEBIAN11-PYTHON39-1290158   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade nginx from latest to 1.21.4", "number": 5, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/5", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to nginx:1.21.4, as this image has only 65 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity | Priority Score / 1000 | Issue | Exploit Maturity |\n| --- | --- | --- | --- |\n|  | 500 | Use After Free SNYK-DEBIAN10-GLIBC-1296899 | No Known Exploit |\n|  | 500 | Use After Free SNYK-DEBIAN10-GLIBC-1296899 | No Known Exploit |\n|  | 500 | Integer Overflow or Wraparound SNYK-DEBIAN10-GLIBC-1315333 | No Known Exploit |\n|  | 500 | Integer Overflow or Wraparound SNYK-DEBIAN10-GLIBC-1315333 | No Known Exploit |\n|  | 400 | Incorrect Privilege Assignment SNYK-DEBIAN10-SYSTEMD-345391 | No Known Exploit |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "add ui helm chart", "number": 6, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/6", "body": "add eros ui helm chart"}
{"title": "nginx cors", "number": 7, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/7", "body": ""}
{"title": "now shows status", "number": 8, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/8", "body": ""}
{"title": "removed analytics and vaults tabs + default redirect to devices", "number": 9, "htmlUrl": "https://bitbucket.org/levl/eros-ui/pull-requests/9", "body": ""}
{"title": "Update setup.sh with new repos", "number": 1, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/1", "body": ""}
{"title": "Cluster monitoring", "number": 10, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/10", "body": "\n\nadd kafka rp and prometheus slack alerts\n\n"}
{"title": "MEROSP-1132 Allow DNS for clusterIP in external-DNS chart", "number": 100, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/100", "body": ""}
{"comment": {"body": "Closing this as the changes have already been merged to master as a part of another PR. ", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/100/_/diff#comment-339017089"}}
{"title": "MEROSP-1915 Allow DNS for clusterIP in external-DNS chart", "number": 101, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/101", "body": ""}
{"title": "Ecs", "number": 102, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/102", "body": "change long to bigint\nfix api and pipeline\nallow ssm to eks\n\n"}
{"title": "MEROSP-1915 Add telegraf DNS entry for internal IP", "number": 103, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/103", "body": ""}
{"title": "telegraf", "number": 104, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/104", "body": ""}
{"title": "MEROSP-1930 Add telegraf services for input/output", "number": 105, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/105", "body": ""}
{"title": "add telegraf services for input/output", "number": 106, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/106", "body": ""}
{"title": "Ecs", "number": 107, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/107", "body": "wip\nchange ssm path\n\n"}
{"title": "MEROSP-1132 cujo tag standard", "number": 108, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/108", "body": ""}
{"title": "MEROSP-1995 cleanup telegraf chart and dns variable", "number": 109, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/109", "body": ""}
{"title": "Microservices update", "number": 11, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/11", "body": "\n\nremove redis and pg (moved to ci cd)\n\n"}
{"title": "MEROSP-1995 cleanup telegraf chart and dns variable", "number": 110, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/110", "body": ""}
{"title": "scaling tg files", "number": 111, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/111", "body": ""}
{"comment": {"body": "Run `terraform fmt -recursive` and `terragrunt hclfmt` to fix code formatting. \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/111/_/diff#comment-340783420"}}
{"title": "MEROSP-2031 add allure helm chart", "number": 112, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/112", "body": ""}
{"title": "MEROSP-1132 Fix command not found pipeline failure", "number": 113, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/113", "body": ""}
{"title": "MEROSP-1132 enable full cloning in pipeline", "number": 114, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/114", "body": "By default, pipeline clones the last 50 commits, in case no tag is found in the last 50 commits, the commit ID will be considered as a tag which we do not want. We want to keep using the previous-tag-increment-commit hash format therefore the PR enables full closing to avoid such situation."}
{"title": "MEROSP-488 update ingress health check success codes", "number": 115, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/115", "body": ""}
{"title": "MEROSP-488 update ingress health check success codes", "number": 116, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/116", "body": ""}
{"title": "Ecs", "number": 117, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/117", "body": "align ddb\nwip\n\n"}
{"title": "Feature/MEROSP-2001 support data model json sche", "number": 118, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/118", "body": "\n\nMEROSP-2001: align error log schema and table - breaking change\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6551](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6551)", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/118/_/diff#comment-342739686"}}
{"title": "add http listener+https elb to telegraf", "number": 119, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/119", "body": ""}
{"title": "Classifier chart first draft", "number": 12, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/12", "body": "classifier helm chart first draft\n\n"}
{"title": "MEROSP-488 convert private subnets into public", "number": 120, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/120", "body": "terraform plan output on eros-prod infrastructure\n```\ntls_private_key.key: Refreshing state... [id=726a770be25ccaf4897b0bb5052ee0b4b87dafa5]\ntls_cert_request.csr: Refreshing state... [id=a309e3e5cd75e262a5c7cfc22ff304ea8d1b8768]\naws_glue_registry.eros: Refreshing state... [id=arn:aws:glue:ca-central-1:************:registry/eros-prod]\naws_kms_key.kms: Refreshing state... [id=d3fd1fd9-47ac-4bfa-9519-2745b39c9f89]\naws_kinesis_stream.error_log_stream: Refreshing state... [id=arn:aws:kinesis:ca-central-1:************:stream/prod_error_log_stream]\naws_iam_role.firehose_role: Refreshing state... [id=eros-prod-msk-log-stream-firehose-role]\naws_msk_configuration.example: Refreshing state... [id=arn:aws:kafka:ca-central-1:************:configuration/eros-prod/027921e7-8048-49ba-8a88-3313eb4bde03-4]\naws_iam_role.v1_device_intelligence_log_role: Refreshing state... [id=v1_prod_device_intelligence_log_role]\naws_iam_policy.lambda-cloudwatch-policy: Refreshing state... [id=arn:aws:iam::************:policy/lambda-cloudwatch-policy]\naws_iam_role.v1_error_log_role: Refreshing state... [id=v1_prod_error_log_role]\naws_kinesis_stream.decision_log_stream: Refreshing state... [id=arn:aws:kinesis:ca-central-1:************:stream/prod_decision_log_stream]\naws_vpc.eros: Refreshing state... [id=vpc-08ba1e55aef58cf92]\naws_kinesis_stream.result_stream: Refreshing state... [id=arn:aws:kinesis:ca-central-1:************:stream/prod_result_stream]\naws_dynamodb_table.devices: Refreshing state... [id=devices]\naws_cloudwatch_log_group.cw_log_group: Refreshing state... [id=msk_prod_broker_logs]\naws_s3_bucket.datalake: Refreshing state... [id=eros-prod.levl.datalake]\naws_iam_role.v1_device_intelligence_result_role: Refreshing state... [id=v1_prod_device_intelligence_result_role]\naws_dynamodb_table.devices_mac: Refreshing state... [id=devices_mac]\naws_iam_role.v1_device_intelligence_raw_role: Refreshing state... [id=v1_prod_device_intelligence_raw_role]\naws_iam_role.eks-eros-cluster-role: Refreshing state... [id=eros-prod]\naws_iam_role.eros-utilities-nodegroup-role: Refreshing state... [id=eros-prod-utilities-node-role]\naws_iam_role.eros-data-catalog-nodegroup-role: Refreshing state... [id=eros-prod-data-catalog-node-role]\naws_cloudwatch_event_rule.athena-query-rule[\"deduped_devices\"]: Refreshing state... [id=deduped_devices]\naws_cloudwatch_event_rule.athena-query-rule[\"model_resolution\"]: Refreshing state... [id=model_resolution]\naws_cloudwatch_event_rule.athena-query-rule[\"missing_strong_identifier\"]: Refreshing state... [id=missing_strong_identifier]\naws_cloudwatch_event_rule.athena-query-rule[\"os_resolution\"]: Refreshing state... [id=os_resolution]\naws_cloudwatch_event_rule.athena-query-rule[\"user_agent\"]: Refreshing state... [id=user_agent]\naws_cloudwatch_event_rule.athena-query-rule[\"identification_level\"]: Refreshing state... [id=identification_level]\naws_kinesis_stream.raw_stream: Refreshing state... [id=arn:aws:kinesis:ca-central-1:************:stream/prod_raw]\naws_iam_role.lambda-role: Refreshing state... [id=terraform-20220502120438006500000001]\naws_iam_role.eros-compute-nodegroup-role: Refreshing state... [id=eros-prod-compute-node-role]\naws_iam_policy.lambda-policy: Refreshing state... [id=arn:aws:iam::************:policy/lambda-athena-query]\naws_glue_schema.DeviceIntelligenceErrorLog: Refreshing state... [id=arn:aws:glue:ca-central-1:************:schema/eros-prod/DeviceIntelligenceErrorLog]\naws_ec2_client_vpn_endpoint.eros-vpn: Refreshing state... [id=cvpn-endpoint-0b11348e43eef8170]\naws_glue_schema.DeviceIntelligenceResult: Refreshing state... [id=arn:aws:glue:ca-central-1:************:schema/eros-prod/DeviceIntelligenceResult]\naws_glue_schema.DeviceIntelligenceLog: Refreshing state... [id=arn:aws:glue:ca-central-1:************:schema/eros-prod/DeviceIntelligenceLog]\naws_iam_role_policy_attachment.eros-utilities-nodegroup-role-AmazonEKSWorkerNodePolicy: Refreshing state... [id=eros-prod-utilities-node-role-2022012016145816470000000d]\naws_iam_role_policy_attachment.eros-cluster-AmazonEKSVPCResourceController: Refreshing state... [id=eros-prod-20220120161459820100000014]\naws_iam_role_policy_attachment.eros-utilities-nodegroup-role-AmazonS3FullAccess: Refreshing state... [id=eros-prod-utilities-node-role-2022012016145816470000000c]\naws_iam_role_policy_attachment.eros-utilities-nodegroup-role-AWSGlueSchemaRegistryFullAccess: Refreshing state... [id=eros-prod-utilities-node-role-20220502120439329700000003]\naws_iam_role_policy_attachment.eros-utilities-nodegroup-role-AmazonEKS_CNI_Policy: Refreshing state... [id=eros-prod-utilities-node-role-20220120161458166200000011]\naws_iam_role_policy_attachment.eros-utilities-nodegroup-role-AmazonEC2ContainerRegistryReadOnly: Refreshing state... [id=eros-prod-utilities-node-role-2022012016145816490000000e]\naws_iam_role_policy_attachment.eros-utilities-nodegroup-role-AmazonSSMFullAccess: Refreshing state... [id=eros-prod-utilities-node-role-20221019142721587000000002]\naws_iam_role_policy_attachment.eros-utilities-nodegroup-role-CloudWatchFullAccess: Refreshing state... [id=eros-prod-utilities-node-role-20220120161458382800000012]\naws_iam_role_policy_attachment.eros-cluster-AmazonEKSClusterPolicy: Refreshing state... [id=eros-prod-20220120161459781400000013]\naws_iam_role_policy_attachment.eros-utilities-nodegroup-role-AmazonAthenaFullAccess: Refreshing state... [id=eros-prod-utilities-node-role-20220324083244424500000001]\naws_vpc_endpoint.s3-vpc-endpoint: Refreshing state... [id=vpce-031d4d70998973b4d]\naws_subnet.eros_msk_private[0]: Refreshing state... [id=subnet-04bc4b9d4253dad6f]\naws_iam_role_policy_attachment.eros-utilities-nodegroup-role-AmazonDynamoDBFullAccess: Refreshing state... [id=eros-prod-utilities-node-role-20220201134010073200000002]\naws_subnet.eros_msk_private[2]: Refreshing state... [id=subnet-02d4c28a3074ee3a6]\naws_subnet.eros_msk_private[1]: Refreshing state... [id=subnet-0ce1bea62f2dc1e23]\naws_security_group.eros-cluster: Refreshing state... [id=sg-0ab717d0b22da89e3]\naws_vpc_endpoint.dynamodb-vpc-endpoint: Refreshing state... [id=vpce-073d81086092dfdff]\naws_subnet.eros_eks_public[2]: Refreshing state... [id=subnet-0d943618ed244d775]\naws_subnet.eros_eks_public[0]: Refreshing state... [id=subnet-0358134d1659cbc26]\naws_subnet.eros_eks_public[1]: Refreshing state... [id=subnet-09625ea19f645a0f5]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-AWSGlueSchemaRegistryFullAccess: Refreshing state... [id=eros-prod-data-catalog-node-role-20220810144352180200000002]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-AmazonSSMFullAccess: Refreshing state... [id=eros-prod-data-catalog-node-role-20221019142721568700000001]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-AmazonEKSWorkerNodePolicy: Refreshing state... [id=eros-prod-data-catalog-node-role-20220810144352594100000006]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-AmazonEC2ContainerRegistryReadOnly: Refreshing state... [id=eros-prod-data-catalog-node-role-20220810144352598900000008]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-AmazonS3FullAccess: Refreshing state... [id=eros-prod-data-catalog-node-role-20220810144352599000000009]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-CloudWatchFullAccess: Refreshing state... [id=eros-prod-data-catalog-node-role-20220810144352009100000001]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-AmazonEKS_CNI_Policy: Refreshing state... [id=eros-prod-data-catalog-node-role-20220810144352546900000004]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-AmazonDynamoDBFullAccess: Refreshing state... [id=eros-prod-data-catalog-node-role-20220810144352549300000005]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-AmazonKinesisFirehoseFullAccess: Refreshing state... [id=eros-prod-data-catalog-node-role-20220810144352370000000003]\naws_internet_gateway.eros: Refreshing state... [id=igw-0badc7d3fd6dfcee5]\naws_iam_role_policy_attachment.eros-data-catalog-nodegroup-role-AmazonAthenaFullAccess: Refreshing state... [id=eros-prod-data-catalog-node-role-20220810144352594100000007]\naws_acmpca_certificate.kafka_msk_client_cert: Refreshing state... [id=arn:aws:acm-pca:ca-central-1:************:certificate-authority/0cb79b38-74d4-4a3c-8c08-db63fd6c91e9/certificate/d10ed388236b8df96e0f16c5bd0dcb62]\naws_iam_role_policy_attachment.lambda-athena-policy-attach: Refreshing state... [id=terraform-20220502120438006500000001-20220502120439916600000004]\naws_iam_role_policy_attachment.lambda-athena-policy-attach-basic-role: Refreshing state... [id=terraform-20220502120438006500000001-20220502120439918700000005]\naws_lambda_function.query-athena[\"identification_level\"]: Refreshing state... [id=identification_level]\naws_lambda_function.query-athena[\"os_resolution\"]: Refreshing state... [id=os_resolution]\naws_ec2_client_vpn_authorization_rule.all_groups: Refreshing state... [id=cvpn-endpoint-0b11348e43eef8170,10.0.0.0/16]\naws_lambda_function.query-athena[\"missing_strong_identifier\"]: Refreshing state... [id=missing_strong_identifier]\naws_lambda_function.query-athena[\"model_resolution\"]: Refreshing state... [id=model_resolution]\naws_lambda_function.query-athena[\"user_agent\"]: Refreshing state... [id=user_agent]\naws_lambda_function.query-athena[\"deduped_devices\"]: Refreshing state... [id=deduped_devices]\naws_iam_role_policy_attachment.eros-compute-nodegroup-role-AmazonDynamoDBFullAccess: Refreshing state... [id=eros-prod-compute-node-role-20220120161456482000000009]\naws_iam_role_policy_attachment.eros-compute-nodegroup-role-AWSGlueSchemaRegistryFullAccess: Refreshing state... [id=eros-prod-compute-node-role-20220502120438793500000002]\naws_iam_role_policy_attachment.eros-compute-nodegroup-role-AmazonKinesisFirehoseFullAccess: Refreshing state... [id=eros-prod-compute-node-role-20220120161456379000000008]\naws_iam_role_policy_attachment.eros-compute-nodegroup-role-AmazonS3FullAccess: Refreshing state... [id=eros-prod-compute-node-role-20220120161456345900000005]\naws_iam_role_policy_attachment.eros-compute-nodegroup-role-AmazonEKS_CNI_Policy: Refreshing state... [id=eros-prod-compute-node-role-20220120161456326000000003]\naws_iam_role_policy_attachment.eros-compute-nodegroup-role-AmazonSSMFullAccess: Refreshing state... [id=eros-prod-compute-node-role-20221019143853514700000001]\naws_iam_role_policy_attachment.eros-compute-nodegroup-role-AmazonAthenaFullAccess: Refreshing state... [id=eros-prod-compute-node-role-20220312134606282700000001]\naws_iam_role_policy_attachment.eros-compute-nodegroup-role-AmazonEKSWorkerNodePolicy: Refreshing state... [id=eros-prod-compute-node-role-2022012016145728000000000a]\naws_iam_role_policy_attachment.eros-compute-nodegroup-role-AmazonEC2ContainerRegistryReadOnly: Refreshing state... [id=eros-prod-compute-node-role-20220120161456365400000007]\naws_vpc_endpoint_route_table_association.s3-route-table-association: Refreshing state... [id=a-vpce-031d4d70998973b4d278470484]\naws_eks_cluster.eros: Refreshing state... [id=eros-prod]\naws_vpc_endpoint_route_table_association.dynamodb-route-table-association: Refreshing state... [id=a-vpce-073d81086092dfdff278470484]\naws_glue_catalog_table.glue_tables_v1_os_resolution[0]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_os_resolution_by_os]\naws_glue_catalog_table.glue_tables_v1_os_resolution[2]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_os_resolution_by_cpe_type]\naws_glue_catalog_table.glue_tables_v1_os_resolution[1]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_os_resolution_by_data_health]\naws_glue_catalog_table.glue_tables_v1_os_resolution[3]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_os_resolution_by_vendor]\naws_glue_catalog_table.glue_tables_v1_os_resolution[4]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_os_resolution_by_band]\naws_glue_catalog_table.glue_tables_v1_newly_generated_levl_ids[4]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_newly_generated_levl_ids_by_band]\naws_glue_catalog_table.glue_tables_v1_newly_generated_levl_ids[0]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_newly_generated_levl_ids_by_os]\naws_glue_catalog_table.glue_tables_v1_newly_generated_levl_ids[2]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_newly_generated_levl_ids_by_cpe_type]\naws_glue_catalog_table.glue_tables_v1_newly_generated_levl_ids[1]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_newly_generated_levl_ids_by_data_health]\naws_glue_catalog_table.glue_tables_v1_newly_generated_levl_ids[3]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_newly_generated_levl_ids_by_vendor]\naws_glue_catalog_table.glue_tables_v1_missing_strong_identifier[1]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_missing_strong_identifier_by_data_health]\naws_glue_catalog_table.glue_tables_v1_missing_strong_identifier[4]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_missing_strong_identifier_by_band]\naws_glue_catalog_table.glue_tables_v1_missing_strong_identifier[0]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_missing_strong_identifier_by_os]\naws_glue_catalog_table.glue_tables_v1_missing_strong_identifier[3]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_missing_strong_identifier_by_vendor]\naws_glue_catalog_table.glue_tables_v1_missing_strong_identifier[2]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_missing_strong_identifier_by_cpe_type]\naws_glue_catalog_table.glue_tables_v1_user_agent_count_by_vendor: Refreshing state... [id=************:eros_prod_datawarehouse:v1_user_agent_count_by_vendor]\naws_glue_catalog_table.glue_tables_v1_deduped_devices[1]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_deduped_devices_by_data_health]\naws_glue_catalog_table.glue_tables_v1_deduped_devices[3]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_deduped_devices_by_vendor]\naws_glue_catalog_table.glue_tables_v1_deduped_devices[2]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_deduped_devices_by_cpe_type]\naws_glue_catalog_table.glue_tables_v1_deduped_devices[4]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_deduped_devices_by_band]\naws_glue_catalog_table.glue_tables_v1_deduped_devices[0]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_deduped_devices_by_os]\naws_athena_workgroup.metrics: Refreshing state... [id=metrics]\naws_athena_workgroup.lambda: Refreshing state... [id=lambda]\naws_athena_database.datawarehouse: Refreshing state... [id=eros_prod_datawarehouse]\naws_kinesis_firehose_delivery_stream.msk_log_stream: Refreshing state... [id=arn:aws:firehose:ca-central-1:************:deliverystream/eros-prod-msk-log-stream]\naws_glue_catalog_table.glue_tables_v1_identification_level[4]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_identification_level_by_band]\naws_glue_catalog_table.glue_tables_v1_identification_level[0]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_identification_level_by_os]\naws_glue_catalog_table.glue_tables_v1_identification_level[2]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_identification_level_by_cpe_type]\naws_glue_catalog_table.glue_tables_v1_identification_level[1]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_identification_level_by_data_health]\naws_glue_catalog_table.glue_tables_v1_identification_level[3]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_identification_level_by_vendor]\naws_glue_catalog_table.glue_tables_v1_model_resolution[2]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_model_resolution_by_cpe_type]\naws_route_table.eros: Refreshing state... [id=rtb-0e2aaac0da1226024]\naws_glue_catalog_table.glue_tables_v1_model_resolution[4]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_model_resolution_by_band]\naws_glue_catalog_table.glue_tables_v1_model_resolution[0]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_model_resolution_by_os]\naws_glue_catalog_table.glue_tables_v1_model_resolution[1]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_model_resolution_by_data_health]\naws_glue_catalog_table.glue_tables_v1_model_resolution[3]: Refreshing state... [id=************:eros_prod_datawarehouse:v1_model_resolution_by_vendor]\naws_lambda_permission.allow_cloudwatch[\"deduped_devices\"]: Refreshing state... [id=AllowExecutionFromCloudWatch]\naws_lambda_permission.allow_cloudwatch[\"model_resolution\"]: Refreshing state... [id=AllowExecutionFromCloudWatch]\naws_lambda_permission.allow_cloudwatch[\"os_resolution\"]: Refreshing state... [id=AllowExecutionFromCloudWatch]\naws_lambda_permission.allow_cloudwatch[\"user_agent\"]: Refreshing state... [id=AllowExecutionFromCloudWatch]\naws_lambda_permission.allow_cloudwatch[\"missing_strong_identifier\"]: Refreshing state... [id=AllowExecutionFromCloudWatch]\naws_lambda_permission.allow_cloudwatch[\"identification_level\"]: Refreshing state... [id=AllowExecutionFromCloudWatch]\naws_eks_node_group.eros_utilities_nodegroup: Refreshing state... [id=eros-prod:utilities]\naws_cloudwatch_event_target.lambda-func[\"os_resolution\"]: Refreshing state... [id=os_resolution-lambda]\naws_cloudwatch_event_target.lambda-func[\"identification_level\"]: Refreshing state... [id=identification_level-lambda]\naws_cloudwatch_event_target.lambda-func[\"deduped_devices\"]: Refreshing state... [id=deduped_devices-lambda]\naws_cloudwatch_event_target.lambda-func[\"missing_strong_identifier\"]: Refreshing state... [id=missing_strong_identifier-lambda]\naws_cloudwatch_event_target.lambda-func[\"model_resolution\"]: Refreshing state... [id=model_resolution-lambda]\naws_cloudwatch_event_target.lambda-func[\"user_agent\"]: Refreshing state... [id=user_agent-lambda]\naws_security_group.sg: Refreshing state... [id=sg-0af96aa801a68add7]\naws_ec2_client_vpn_network_association.default[1]: Refreshing state... [id=cvpn-assoc-09375ab44b40badee]\naws_ec2_client_vpn_network_association.default[2]: Refreshing state... [id=cvpn-assoc-0334f66bdc60606dc]\naws_ec2_client_vpn_network_association.default[0]: Refreshing state... [id=cvpn-assoc-0e67a1567679913f3]\naws_eks_node_group.eros_data_catalog_nodegroup: Refreshing state... [id=eros-prod:data-catalog]\naws_eks_node_group.eros_compute_nodegroup: Refreshing state... [id=eros-prod:compute]\naws_glue_catalog_table.v2_device_intelligence_error_log: Refreshing state... [id=************:eros_prod_datawarehouse:v1_device_intelligence_error_log]\naws_eks_identity_provider_config.cluster_oidc: Refreshing state... [id=eros-prod:eros-prod_oidc]\naws_glue_catalog_table.v1_device_intelligence_raw: Refreshing state... [id=************:eros_prod_datawarehouse:v3_device_intelligence_raw]\naws_glue_catalog_table.v1_device_intelligence_log: Refreshing state... [id=************:eros_prod_datawarehouse:v3_device_intelligence_log]\naws_glue_catalog_table.v2_device_intelligence_result: Refreshing state... [id=************:eros_prod_datawarehouse:v1_device_intelligence_result]\naws_glue_catalog_table.v1_device_intelligence_error_log: Refreshing state... [id=************:eros_prod_datawarehouse:v3_device_intelligence_error_log]\naws_glue_catalog_table.v1_device_intelligence_result: Refreshing state... [id=************:eros_prod_datawarehouse:v3_device_intelligence_result]\naws_athena_named_query.deduped_devices_by_cpe_type: Refreshing state... [id=6ffe70d9-12a0-4ad1-8a56-da77df9f4391]\naws_athena_named_query.missing_strong_identifiers_by_cpe_type: Refreshing state... [id=730a01a6-a7af-49a7-a9ba-f4388c3c6726]\naws_athena_named_query.missing_strong_identifiers_by_band: Refreshing state... [id=4059675b-6ae2-4477-9e62-cf63ec6753f7]\naws_athena_named_query.deduped_devices_by_os: Refreshing state... [id=79932388-670d-4a3e-b2ea-64100ba7169b]\naws_athena_named_query.model_resolution_by_band: Refreshing state... [id=7adb2df3-935f-4bb0-9d54-7d97bd35eccb]\naws_athena_named_query.identification_level_by_os: Refreshing state... [id=a0075e17-553d-4322-8eda-7497e233b051]\naws_athena_named_query.os_resolution_by_vendor: Refreshing state... [id=cfc81b81-2707-4178-ba97-abd2e2e41d1b]\naws_athena_named_query.deduped_devices_by_vendor: Refreshing state... [id=1a373463-3f74-45e3-8f8d-f8d742f4f10a]\naws_athena_named_query.model_resolution_by_os: Refreshing state... [id=e9c7d147-7f25-428a-8f67-0b2fb170a194]\naws_athena_named_query.identification_level_by_band: Refreshing state... [id=a51a1edd-c72b-4fe3-bf15-d1af4805fdb9]\naws_athena_named_query.identification_level_query_by_vendor: Refreshing state... [id=7c714673-519e-435a-a54c-bdb274f4107e]\naws_athena_named_query.missing_strong_identifiers_by_os: Refreshing state... [id=bbf9ca0a-bc41-4b8e-9755-eb793819ddfe]\naws_athena_named_query.identification_level_by_cpe_type: Refreshing state... [id=3220180b-2972-4638-a4bd-7ad4f3067e3d]\naws_athena_named_query.model_resolution_by_cpe_type: Refreshing state... [id=9b91e407-29b7-45b3-bdf6-bd8e961005f8]\naws_athena_named_query.user_agent_count_by_vendor: Refreshing state... [id=19bebd04-ea4e-46fd-bc10-7499c52cf77c]\naws_athena_named_query.model_resolution_by_vendor: Refreshing state... [id=810cd90c-26eb-4009-80ad-f14382c91e1a]\naws_athena_named_query.os_resolution_by_os: Refreshing state... [id=861d4c11-e5ae-4ae2-af15-d099e053015e]\naws_athena_named_query.deduped_devices_by_band: Refreshing state... [id=24960623-df68-49ff-a757-f9260bd6e906]\naws_athena_named_query.os_resolution_by_band: Refreshing state... [id=8a908ed8-b4dd-435d-ba02-6dd3c2664dd0]\naws_athena_named_query.os_resolution_by_cpe_type: Refreshing state... [id=443e7ce3-20cf-48f3-b676-0f83bf022de0]\naws_athena_named_query.identification_level_by_os_labmda: Refreshing state... [id=9c0797da-49d0-456a-a52a-95e683f1c0f3]\naws_athena_named_query.os_resolution_by_vendor_labmda: Refreshing state... [id=285ec123-b12b-45c0-a395-01befac0466e]\naws_athena_named_query.deduped_devices_by_os_labmda: Refreshing state... [id=9121d6ba-bcca-43a3-bc2b-7902f7d04029]\naws_athena_named_query.missing_strong_identifiers_by_os_labmda: Refreshing state... [id=2b3245a6-1459-4dc0-a797-630cd7d6902c]\naws_athena_named_query.model_resolution_by_os_labmda: Refreshing state... [id=3ed7060a-69e0-45b7-9af9-9ff281a34d47]\naws_athena_named_query.model_resolution_by_vendor_labmda: Refreshing state... [id=b1e92fc5-6310-4972-b716-42fa6f5930f1]\naws_athena_named_query.deduped_devices_by_band_labmda: Refreshing state... [id=0aaab896-fd29-43e2-9e1f-6021adcd8ed1]\naws_athena_named_query.os_resolution_by_os_labmda: Refreshing state... [id=6d7deddb-4a18-45d9-992d-d8b89866dbe6]\naws_athena_named_query.os_resolution_by_band_labmda: Refreshing state... [id=56ba6b02-77a2-44f6-af1f-5ba10f83ca5e]\naws_athena_named_query.user_agent_count_by_vendor_lambda: Refreshing state... [id=8c3dbb22-6053-47e3-8d8a-e9b354e83e09]\naws_athena_named_query.os_resolution_by_cpe_type_labmda: Refreshing state... [id=ecde420b-1f00-4ffe-a550-6a42bb3385e6]\naws_athena_named_query.deduped_devices_by_vendor_labmda: Refreshing state... [id=40f51914-fa44-4116-ac20-bbbc9672fc60]\naws_athena_named_query.identification_level_by_cpe_type_lambda: Refreshing state... [id=62b6b2ce-ef03-49f9-939b-380e35fb7fff]\naws_athena_named_query.missing_strong_identifiers_by_band_labmda: Refreshing state... [id=f860bcd3-531f-4b58-a046-240497720bc7]\naws_athena_named_query.model_resolution_by_band_labmda: Refreshing state... [id=8a481c7e-824b-46db-9187-56b70ff44092]\naws_athena_named_query.deduped_devices_by_cpe_type_labmda: Refreshing state... [id=4a6205e7-6baa-4f95-b28b-bc8752e3cb5f]\naws_athena_named_query.identification_level_by_band_labmda: Refreshing state... [id=f309e9d4-12f6-4a49-8864-0044ef88979d]\naws_athena_named_query.model_resolution_by_cpe_type_labmda: Refreshing state... [id=29d5e7c2-c10e-4a9f-a15b-80cb09704ec6]\naws_athena_named_query.missing_strong_identifiers_by_cpe_type_labmda: Refreshing state... [id=61a0e443-e78c-446e-991b-fd4928f36a11]\naws_athena_named_query.identification_level_by_vendor_labmda: Refreshing state... [id=2198076c-6fe9-4c10-bd5d-ba89bf999938]\naws_route_table_association.eros-eks[0]: Refreshing state... [id=rtbassoc-0aa3257e79cf60cb7]\naws_msk_cluster.msk: Refreshing state... [id=arn:aws:kafka:ca-central-1:************:cluster/eros-prod/59af0a68-2049-4f88-be53-4c5719376687-4]\naws_glue_catalog_table.v2_device_intelligence_log: Refreshing state... [id=************:eros_prod_datawarehouse:v1_device_intelligence_log]\naws_kinesis_firehose_delivery_stream.v1_device_intelligence_raw_delivery_stream: Refreshing state... [id=arn:aws:firehose:ca-central-1:************:deliverystream/eros-prod-raw]\naws_kinesis_firehose_delivery_stream.v1_device_intelligence_log_delivery_stream: Refreshing state... [id=arn:aws:firehose:ca-central-1:************:deliverystream/eros-prod-decision-log]\naws_kinesis_firehose_delivery_stream.v1_error_log_delivery_stream: Refreshing state... [id=arn:aws:firehose:ca-central-1:************:deliverystream/eros-prod-error-log]\nnull_resource.v1_onboarding_vs_classification_by_vault_view: Refreshing state... [id=6348377242802854901]\nnull_resource.v1_monitoring_view: Refreshing state... [id=4215284908866597908]\naws_kinesis_firehose_delivery_stream.v1_device_intelligence_result_delivery_stream: Refreshing state... [id=arn:aws:firehose:ca-central-1:************:deliverystream/eros-prod-result-client]\nNote: Objects have changed outside of Terraform\nTerraform detected the following changes made outside of Terraform since the last \"terraform apply\":\n# aws_dynamodb_table.devices has changed\n  ~ resource \"aws_dynamodb_table\" \"devices\" {\n        id             = \"devices\"\n        name           = \"devices\"\n      ~ tags           = {\n          + \"Name\"               = \"devices\"\n          + \"cujo:backup-plan\"   = \"default\"\n          + \"cujo:budget-code\"   = \"cto-rd-qa\"\n          + \"cujo:contact-email\" = \"<EMAIL>\"\n          + \"cujo:cost-center\"   = \"tbc\"\n          + \"cujo:deployment\"    = \"cc1-rd-cl-ci\"\n        }\n      ~ tags_all       = {\n          + \"Name\"               = \"devices\"\n          + \"cujo:backup-plan\"   = \"default\"\n          + \"cujo:budget-code\"   = \"cto-rd-qa\"\n          + \"cujo:contact-email\" = \"<EMAIL>\"\n          + \"cujo:cost-center\"   = \"tbc\"\n          + \"cujo:deployment\"    = \"cc1-rd-cl-ci\"\n        }\n        # (7 unchanged attributes hidden)\n    # (4 unchanged blocks hidden)\n}\n\n# aws_dynamodb_table.devices_mac has changed\n  ~ resource \"aws_dynamodb_table\" \"devices_mac\" {\n        id             = \"devices_mac\"\n        name           = \"devices_mac\"\n      ~ tags           = {\n          + \"Name\"               = \"devices_mac\"\n          + \"cujo:backup-plan\"   = \"default\"\n          + \"cujo:budget-code\"   = \"cto-rd-qa\"\n          + \"cujo:contact-email\" = \"<EMAIL>\"\n          + \"cujo:cost-center\"   = \"tbc\"\n          + \"cujo:deployment\"    = \"cc1-rd-cl-ci\"\n        }\n      ~ tags_all       = {\n          + \"Name\"               = \"devices_mac\"\n          + \"cujo:backup-plan\"   = \"default\"\n          + \"cujo:budget-code\"   = \"cto-rd-qa\"\n          + \"cujo:contact-email\" = \"<EMAIL>\"\n          + \"cujo:cost-center\"   = \"tbc\"\n          + \"cujo:deployment\"    = \"cc1-rd-cl-ci\"\n        }\n        # (7 unchanged attributes hidden)\n  ~ point_in_time_recovery {\n      ~ enabled = false - true\n    }\n\n    # (3 unchanged blocks hidden)\n}\n\n# aws_iam_role.eros-compute-nodegroup-role has changed\n  ~ resource \"aws_iam_role\" \"eros-compute-nodegroup-role\" {\n        id                    = \"eros-prod-compute-node-role\"\n      ~ managed_policy_arns   = [\n          + \"arn:aws:iam::aws:policy/AmazonSSMFullAccess\",\n            # (9 unchanged elements hidden)\n        ]\n        name                  = \"eros-prod-compute-node-role\"\n        tags                  = {}\n        # (8 unchanged attributes hidden)\n    # (1 unchanged block hidden)\n}\n\n# aws_s3_bucket.datalake has changed\n  ~ resource \"aws_s3_bucket\" \"datalake\" {\n        id                                   = \"eros-prod.levl.datalake\"\n      ~ policy                               = jsonencode( # whitespace changes\n            {\n                Id        = \"AWSLogDeliveryWrite20150319\"\n                Statement = [\n                    {\n                        Action    = \"s3:PutObject\"\n                        Condition = {\n                            ArnLike      = {\n                                \"aws:SourceArn\" = \"arn:aws:logs:ca-central-1:************:\"\n                            }\n                            StringEquals = {\n                                \"aws:SourceAccount\" = \"************\"\n                                \"s3:x-amz-acl\"      = \"bucket-owner-full-control\"\n                            }\n                        }\n                        Effect    = \"Allow\"\n                        Principal = {\n                            Service = \"delivery.logs.amazonaws.com\"\n                        }\n                        Resource  = \"arn:aws:s3:::eros-prod.levl.datalake/logs/msk-/AWSLogs/************/\"\n                        Sid       = \"AWSLogDeliveryWrite\"\n                    },\n                    {\n                        Action    = \"s3:GetBucketAcl\"\n                        Condition = {\n                            ArnLike      = {\n                                \"aws:SourceArn\" = \"arn:aws:logs:ca-central-1:************:*\"\n                            }\n                            StringEquals = {\n                                \"aws:SourceAccount\" = \"************\"\n                            }\n                        }\n                        Effect    = \"Allow\"\n                        Principal = {\n                            Service = \"delivery.logs.amazonaws.com\"\n                        }\n                        Resource  = \"arn:aws:s3:::eros-prod.levl.datalake\"\n                        Sid       = \"AWSLogDeliveryAclCheck\"\n                    },\n                ]\n                Version   = \"2012-10-17\"\n            }\n        )\n        tags                                 = {}\n        # (19 unchanged attributes hidden)\n    }\nUnless you have made equivalent changes to your configuration, or ignored the relevant attributes using ignore_changes, the following plan may include\nactions to undo or respond to these changes.\n\nTerraform used the selected providers to generate the following execution plan. Resource actions are indicated with the following symbols:\n  + create\n  ~ update in-place\nTerraform will perform the following actions:\n# aws_dynamodb_table.devices will be updated in-place\n  ~ resource \"aws_dynamodb_table\" \"devices\" {\n        id             = \"devices\"\n        name           = \"devices\"\n      ~ tags           = {\n          - \"Name\"               = \"devices\" -> null\n          - \"cujo:backup-plan\"   = \"default\" -> null\n          - \"cujo:budget-code\"   = \"cto-rd-qa\" -> null\n          - \"cujo:contact-email\" = \"<EMAIL>\" -> null\n          - \"cujo:cost-center\"   = \"tbc\" -> null\n          - \"cujo:deployment\"    = \"cc1-rd-cl-ci\" -> null\n        }\n      ~ tags_all       = {\n          - \"Name\"               = \"devices\"\n          - \"cujo:backup-plan\"   = \"default\"\n          - \"cujo:budget-code\"   = \"cto-rd-qa\"\n          - \"cujo:contact-email\" = \"<EMAIL>\"\n          - \"cujo:cost-center\"   = \"tbc\"\n          - \"cujo:deployment\"    = \"cc1-rd-cl-ci\"\n        } -> (known after apply)\n        # (7 unchanged attributes hidden)\n    # (4 unchanged blocks hidden)\n}\n\n# aws_dynamodb_table.devices_mac will be updated in-place\n  ~ resource \"aws_dynamodb_table\" \"devices_mac\" {\n        id             = \"devices_mac\"\n        name           = \"devices_mac\"\n      ~ tags           = {\n          - \"Name\"               = \"devices_mac\" -> null\n          - \"cujo:backup-plan\"   = \"default\" -> null\n          - \"cujo:budget-code\"   = \"cto-rd-qa\" -> null\n          - \"cujo:contact-email\" = \"<EMAIL>\" -> null\n          - \"cujo:cost-center\"   = \"tbc\" -> null\n          - \"cujo:deployment\"    = \"cc1-rd-cl-ci\" -> null\n        }\n      ~ tags_all       = {\n          - \"Name\"               = \"devices_mac\"\n          - \"cujo:backup-plan\"   = \"default\"\n          - \"cujo:budget-code\"   = \"cto-rd-qa\"\n          - \"cujo:contact-email\" = \"<EMAIL>\"\n          - \"cujo:cost-center\"   = \"tbc\"\n          - \"cujo:deployment\"    = \"cc1-rd-cl-ci\"\n        } -> (known after apply)\n        # (7 unchanged attributes hidden)\n    # (4 unchanged blocks hidden)\n}\n\n# aws_glue_schema.DeviceIntelligenceErrorLog will be updated in-place\n  ~ resource \"aws_glue_schema\" \"DeviceIntelligenceErrorLog\" {\n        id                    = \"arn:aws:glue:ca-central-1:************:schema/eros-prod/DeviceIntelligenceErrorLog\"\n      ~ schema_definition     = jsonencode( # whitespace changes\n            {\n                doc       = \"Error log containing all available data of the erros occurred\"\n                fields    = [\n                    {\n                        name = \"error_timestamp\"\n                        type = {\n                            logicalType = \"timestamp-millis\"\n                            type        = \"long\"\n                        }\n                    },\n                    {\n                        name = \"error_log_id\"\n                        type = {\n                            logicalType = \"uuid\"\n                            type        = \"string\"\n                        }\n                    },\n                    {\n                        name = \"error_level\"\n                        type = \"string\"\n                    },\n                    {\n                        name = \"error_label\"\n                        type = \"string\"\n                    },\n                    {\n                        default = null\n                        name    = \"error_backtrace\"\n                        type    = [\n                            \"null\",\n                            \"string\",\n                        ]\n                    },\n                    {\n                        default = null\n                        name    = \"tenant_id\"\n                        type    = [\n                            \"null\",\n                            {\n                                logicalType = \"uuid\"\n                                type        = \"string\"\n                            },\n                        ]\n                    },\n                    {\n                        default = null\n                        name    = \"vault_id\"\n                        type    = [\n                            \"null\",\n                            {\n                                logicalType = \"uuid\"\n                                type        = \"string\"\n                            },\n                        ]\n                    },\n                    {\n                        default = null\n                        name    = \"source_event_timestamp\"\n                        type    = [\n                            \"null\",\n                            {\n                                logicalType = \"timestamp-millis\"\n                                type        = \"long\"\n                            },\n                        ]\n                    },\n                    {\n                        default = null\n                        name    = \"source_event_id\"\n                        type    = [\n                            \"null\",\n                            {\n                                logicalType = \"uuid\"\n                                type        = \"string\"\n                            },\n                        ]\n                    },\n                    {\n                        default = null\n                        name    = \"source_event_type\"\n                        type    = [\n                            \"null\",\n                            \"string\",\n                        ]\n                    },\n                ]\n                name      = \"DeviceIntelligenceErrorLog\"\n                namespace = \"levl.device_intelligence.error.v1\"\n                type      = \"record\"\n            }\n        )\n        tags                  = {}\n        # (10 unchanged attributes hidden)\n    }\n# aws_route_table_association.eros-eks[1] will be created\n  + resource \"aws_route_table_association\" \"eros-eks\" {\n      + id             = (known after apply)\n      + route_table_id = \"rtb-0e2aaac0da1226024\"\n      + subnet_id      = \"subnet-09625ea19f645a0f5\"\n    }\n# aws_route_table_association.eros-eks[2] will be created\n  + resource \"aws_route_table_association\" \"eros-eks\" {\n      + id             = (known after apply)\n      + route_table_id = \"rtb-0e2aaac0da1226024\"\n      + subnet_id      = \"subnet-0d943618ed244d775\"\n    }\n# aws_subnet.eros_eks_public[0] will be updated in-place\n  # (moved from aws_subnet.eros_eks_private[0])\n  ~ resource \"aws_subnet\" \"eros_eks_public\" {\n        id                                             = \"subnet-0358134d1659cbc26\"\n      ~ tags                                           = {\n          ~ \"Name\"                            = \"eros-prod-eks-private-subnet\" -> \"eros-prod-eks-public-subnet\"\n            # (2 unchanged elements hidden)\n        }\n      ~ tags_all                                       = {\n          ~ \"Name\"                            = \"eros-prod-eks-private-subnet\" -> \"eros-prod-eks-public-subnet\"\n            # (2 unchanged elements hidden)\n        }\n        # (14 unchanged attributes hidden)\n    }\n# aws_subnet.eros_eks_public[1] will be updated in-place\n  # (moved from aws_subnet.eros_eks_private[1])\n  ~ resource \"aws_subnet\" \"eros_eks_public\" {\n        id                                             = \"subnet-09625ea19f645a0f5\"\n      ~ tags                                           = {\n          ~ \"Name\"                            = \"eros-prod-eks-private-subnet\" -> \"eros-prod-eks-public-subnet\"\n            # (2 unchanged elements hidden)\n        }\n      ~ tags_all                                       = {\n          ~ \"Name\"                            = \"eros-prod-eks-private-subnet\" -> \"eros-prod-eks-public-subnet\"\n            # (2 unchanged elements hidden)\n        }\n        # (14 unchanged attributes hidden)\n    }\n# aws_subnet.eros_eks_public[2] will be updated in-place\n  # (moved from aws_subnet.eros_eks_private[2])\n  ~ resource \"aws_subnet\" \"eros_eks_public\" {\n        id                                             = \"subnet-0d943618ed244d775\"\n      ~ tags                                           = {\n          ~ \"Name\"                            = \"eros-prod-eks-private-subnet\" -> \"eros-prod-eks-public-subnet\"\n            # (2 unchanged elements hidden)\n        }\n      ~ tags_all                                       = {\n          ~ \"Name\"                            = \"eros-prod-eks-private-subnet\" -> \"eros-prod-eks-public-subnet\"\n            # (2 unchanged elements hidden)\n        }\n        # (14 unchanged attributes hidden)\n    }\nPlan: 2 to add, 6 to change, 0 to destroy.\n\n Warning: Argument is deprecated\n \n   with aws_ec2_client_vpn_network_association.default,\n   on vpn.tf line 25, in resource \"aws_ec2_client_vpn_network_association\" \"default\":\n   25:   security_groups        = [aws_security_group.eros-cluster.id, aws_eks_cluster.eros.vpc_config[0].cluster_security_group_id]\n \n Use the security_group_ids attribute of the aws_ec2_client_vpn_endpoint resource instead.\n \n (and 3 more similar warnings elsewhere)\n\n\nNote: You didn't use the -out option to save this plan, so Terraform can't guarantee to take exactly these actions if you run \"terraform apply\" now.\n```\n"}
{"title": "MEROSP-2178", "number": 121, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/121", "body": "template the athena queries and lambdas to coexist with eros-prod\nalign to cujo env\nalign to cujo env\n\n"}
{"title": "MEROSP-488 convert private subnet to public", "number": 122, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/122", "body": ""}
{"title": "Ecs", "number": 123, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/123", "body": "wip\nwip\n\n"}
{"title": "[WIP] MEROSP-2154 modularize tf code for eros-test env", "number": 124, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/124", "body": ""}
{"comment": {"body": "Do not merge until the test environment is ready", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/124/_/diff#comment-344579408"}}
{"comment": {"body": "Declining this as this is no longer the PR that we will refer to create test env. We\u2019ll be creating test env from cujo", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/124/_/diff#comment-348337376"}}
{"title": "MEROSP-2001: fix features schema and erro schema", "number": 125, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/125", "body": ""}
{"title": "sqs terraform definitions + cujo variables", "number": 126, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/126", "body": ""}
{"title": "Feature/MEROSP-2001 support data model json sche", "number": 127, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/127", "body": "Revert feature log table name to datacatlog name"}
{"title": "MEROSP-2001: revert feature table to datacatalog", "number": 128, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/128", "body": ""}
{"title": "aligning cujo  master", "number": 129, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/129", "body": "multi env dynamo db\nuse cujo cidr\nadding liat\nrestore s3 bucket\nadding cujo vars\nuse cujo cidr\nWIP\nsupport raw\nadd pcx routes and add vars\nfixed\nupdate the map roles\nadd cujo values\ndeploy add repos\navro sbs parquest\nadd cujo ingress values file\nadded cujo config for alb\nupgrade chart version\nfix\nadd cluster secret permission\nreview changes\nathena dont eat long\nfix indent\nadd kinesis write permissions\nworks\nworks\nmap fix\nadd updated ssl certificate\npoint to datalake bucket name\ncert arn\nhosted zone id\nchange superset port\nchange hosted zone id\nrevert superset to port 9090\nadding <EMAIL>\nupdate shimi users\nrename analytics tables\nuse v2 partition\nadd firehose\nreduce buffer_interval to 60 sec in favour of smoke test\n\nRevert \"reduce buffer_interval to 60 sec in favour of smoke test\"\nThis reverts commit 4a1e1f542d4b96cff095be2327ae2ab998f67b56.\n\n\nadd michaelfro@levltech.<NAME_EMAIL>\n\nadd nodes to the utilities node group\n\nMerged in MEROSP-1915-eros-cujo (pull request #104)\ntelegraf\nApproved-by: <NAME_EMAIL>\n\n\nadd meet.dave to the user-roles\n\nchange node groups to on demand\nadd telegraf services for input/output\nchange hardcoded dns\ncleanup telegraf chart and dns variable\nfix telegraf connectivity issue\nremove tg cache\nupdate ingress health check success codes\ntemplate the athena queries and lambdas to coexist with eros-prod\nalign to cujo env\nalign to cujo env\n\nMerged in MEROSP-488-cujo (pull request #122)\nMEROSP-488 convert private subnet to public\n\nconvert private subnet to public\n\nApproved-by: Itai Zolberg\n\n\nchange deafult to cujo\n\nterragrunt\nscale delete fix\nformatting\nscale\nwip\nfix ci build and push charts bug\nMEROSP-2001: revert feature table to datacatalog\nadd data catalog stream to local vars\nrename datacatalg table\nalign with eros conf\nalign with eros conf\nscale test changes\nscale changes\nscale changes\nadd https traffic for ecs tasks\n\n"}
{"title": "Redis", "number": 13, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/13", "body": "increate prometheus storage space\nperformance cluster\nnew vpc id\nredis chart\npg config\nupdate vpc id\nadd cache node group\ntag cache machine with application role = process\nadd grafana dashboards\nconfigure redis\nremove telegraf from 'all' list\ninstall aws elb to support node-group\ninstall external-dns to support node-group\ninstall service monitor to its application namespace to allow sbs\n\n"}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} conflicts, can\u2019t be merged", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/13/_/diff#comment-252790123"}}
{"title": "change data catalog dp res to month", "number": 130, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/130", "body": ""}
{"title": "remove hour + day from fh prefix", "number": 131, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/131", "body": ""}
{"title": "support events deaggregation", "number": 132, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/132", "body": ""}
{"title": "Cujo-Dev: TF+TG LNDI models resources & values", "number": 133, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/133", "body": "SQS resource - TF\nLambda resource - TF\nS3 resource - TF\nIAM resource for SQS / Lambda / S3 permissions - TF\nUpdate classifiers input parameters (MODELS_UPDATE_INTERVAL_IN_SEC) - TG\nUpdate classifiers SSM Parameter store (MODELS_S3_FILE_PATH) - TF\nvars + versions + local_vars - TF\nTG values >> Environments: Dev, cujo-test, cujo-gde, rd-ci, rd-stg\n\nNote: all environments were deployed to cujo\n"}
{"title": "Schema translation should be off in Cujo environmnets", "number": 134, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/134", "body": "Added the ACTIVATE_SCHEMA_TRANSLATION that is off by default. In the Cujo env we will exclusively receive M2 events."}
{"title": "MEROSP-2154 eros-test env changes", "number": 135, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/135", "body": "infra changes for eros-test env\nadd helm values for eros-test env\n\n"}
{"title": "support new line delimiter", "number": 136, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/136", "body": ""}
{"title": "fix timestamp", "number": 137, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/137", "body": ""}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} same PR in sparrow?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/137/_/diff#comment-350848001"}}
{"title": "fix timestamp", "number": 138, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/138", "body": ""}
{"title": "MEROSP-2240 align eros infrastructure tf", "number": 139, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/139", "body": "dynamodb.tf changes\nvpn.tf changes\nathena.tf changes\n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} Please check now on.", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/139/_/diff#comment-357124433"}}
{"title": "Restructure files", "number": 14, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/14", "body": "pyflink\ndask+flink\ndelete .eks**\npromote gitignore to the root level\n-\nnode selection\nexpose kafka metrics to prometheus\nconfigure dask\n-\nper core configuration\ncleanups\nenable kafka metrics\nadd benchmarks\ndask benchmarks\ndeploy elk\ncreate namespaces\nworker per node configuration\ncluster configuration\npostgres deployment\nadd s3 permissions\nadd nadav\nadd packages to the workers\nadd scrape config to prometheus\nincrease storage capacity\nuse values file to expose /metrics\nremove apm-server and metric beat\nexpose kafka to the world via elb\nadd elk exporter\nexpose kafka to the world\nscrape analytics\nUpdate setup.sh with new repos\nadd gal\nworking dahsboard\nadd pg dashboard\nadd sergey\nnew charts\nfixing versions\nelk chart\nupdated exported\ncleanup + new script\nadded debug flag\nadded docs\nadded port-forword script\nAdded k8s-dashboard\nadd qa\ndeployment changes\nadded external dns\nupdate metric_version of prometheus_client\nadd chart\nchart lock\nadd to deploy\naws load balancer controller\nremoved report-portal\nupdated domain\nnew account updates\nupdates to charts scripts and values\ngroup name\nnew repos\nupdate values\nchange region to ca-central-1\ncanada settings and new ingress chart\ningress updates\ncleanup\ncleanup\ncleanup\nremove ingress from all op\nnew dns config\nchange to update alb chart\nupdate spot prices and node froup labeling\nschedule match by label not node froup name\nincrease default max connections remove node selector\nadd grafana classification dashboard\nadd superset devices dashboard\nsuperset dashboards\nallow delete topic for backoffice-api\nfixed prod name to production\nnew vpc id\nexternaldns role\nadd superset to be auth by gsuite\nproper install of gsuite integration\nupdate spot prices to ca-central-1\nchagnes to filebeat values\nchagnes to filebeat values\nchagnes to filebeat values\nlogstash conf\nlogstash conf\nlogstash conf\nFixed ingress url and addedd Logstash\nadded storage to prometheus\nadded storage to prometheus\nremoved vs setting\nadd automation namespace\nchange vpc id to the new create one\nconfigure 3 brokers for kafka cluster\nnot installing dask in the default\nadd kafka rp and prometheusslack alerts\nadd kafka disk alert\nrm kafka rp\nadd db disk alert\n\nMerged in microservices-update (pull request #11)\nMicroservices update\n\nincreate prometheus storage space\nperformance cluster\nnew vpc id\nredis chart\nMerged master into performance_cluster\nremove redis and pg\n\nApproved-by: Itai Zolberg\n\n\nignore pipfile\n\nrestructure files\nignore pycache\n\n"}
{"title": "athena.tf,dynamodb.tf,vpn.tf changes", "number": 140, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/140", "body": ""}
{"title": "MEROSP-2240 align cujo tf", "number": 141, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/141", "body": "tf/.terraform.lock.hcl\nadded dynamodb.tf changes\nadded changes for vpn.tf\nadded athena.tf changes\n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} Can you please review it now? I have removed the changes related to `.terraform.lock.hcl` and added this file to `.gitignore`. Kindly let me know if this looks good to you or not.", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/141/_/diff#comment-357123334"}}
{"title": "rename cujo kinesis to prod", "number": 142, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/142", "body": ""}
{"title": "rename prod kinesis to dev", "number": 143, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/143", "body": ""}
{"title": "MEROSP-2323 Use image based lambda function", "number": 144, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/144", "body": ""}
{"comment": {"body": "do we want latest or configurable?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/144/_/diff#comment-353573374"}}
{"comment": {"body": "`874994064657.dkr.ecr.eu-west-1.amazonaws.com/lndi-models-sync:latest` ? `...sync:6b1004f`", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/144/_/diff#comment-353573627"}}
{"comment": {"body": "This is already configurable as it\u2019s defined in variable file of terragrunt \\(`terragrunt.hcl`\\) this can be changed later once we publish lambda with proper version and before applying `terragrunt apply`", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/144/_/diff#comment-353589095"}}
{"comment": {"body": "Same here. It\u2019s part of terragrunt values. So can be changed later", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/144/_/diff#comment-353589301"}}
{"title": "Add KINESIS_PORTION to classifier", "number": 145, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/145", "body": ""}
{"title": "renaming data health values", "number": 146, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/146", "body": ""}
{"comment": {"body": "can we merge this one ? @{6265307b185ac200692f9bd9} ", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/146/_/diff#comment-357059163"}}
{"title": "Msk improvements replics auto topic creation", "number": 147, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/147", "body": "Its should be release a non required resources from Claster, buy not creating many replics of configuration map\n"}
{"title": "update MSK config", "number": 148, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/148", "body": ""}
{"title": "MEROSP-2646", "number": 149, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/149", "body": "test msk\nWIP\nAdded dynamic_partitioning_configuration for new firehoses\n\n"}
{"comment": {"body": "why the lock file has changed?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/149/_/diff#comment-353992188"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} Ideally `tf/.terraform.lock.hcl` should be in `.gitignore` .   \nWhen I am doing terraform apply from local, it is changing. ", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/149/_/diff#comment-353995857"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} @{6371fc00f48fbd9b62d30647} Probably because the binary that got installed in Deepak\u2019s machine has different architecture than what\u2019s already defined in lock file.\n\n@{6371fc00f48fbd9b62d30647} Can we check the version of provider installed on your machine just to make sure it\u2019s not different than what\u2019s defined in lock file?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/149/_/diff#comment-354521209"}}
{"title": "add pv to grafana", "number": 15, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/15", "body": ""}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} this is still relevant? ", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/15/_/diff#comment-271178998"}}
{"title": "Added v4 firehose streams for cujo", "number": 150, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/150", "body": ""}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} Can you please review it now? I have removed the changes related to `.terraform.lock.hcl` and added this file to `.gitignore`. Kindly let me know if this looks good to you or not.", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/150/_/diff#comment-357056919"}}
{"title": "MEROSP-1741 expose akhq url", "number": 151, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/151", "body": ""}
{"comment": {"body": "no need for certificates in eros-cujo msk", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/151/_/diff#comment-354521454"}}
{"comment": {"body": "@{6085103f5797db006947d59a} without certs it does not work. it goes into infinite loop of loading. we require certificate for cujo as well\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/151/_/diff#comment-354527601"}}
{"title": "MEROSP-1741 Add akhq chart", "number": 152, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/152", "body": ""}
{"comment": {"body": "do we need the jks files for eros-cujo msk?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/152/_/diff#comment-354646961"}}
{"comment": {"body": "Yes we need that otherwise akhq won\u2019t be able to connect and it will be stuck in infinite loop", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/152/_/diff#comment-354920019"}}
{"title": "Non-Breaking changes", "number": 153, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/153", "body": ""}
{"title": "fix ssm permission in eks compute node group", "number": 154, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/154", "body": ""}
{"title": "MEROSP-2602 cujo CD", "number": 155, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/155", "body": "Test tf and tg\nTest tf and tg\n\n"}
{"title": "increase msk storage", "number": 156, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/156", "body": ""}
{"title": "add MODELS_TAG env var with initial models version", "number": 157, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/157", "body": ""}
{"comment": {"body": "I doubt this is in compliance with cujo\u2019s tag standard. see attached pdf in this ticket - [https://levltech.atlassian.net/browse/MEROSP-1132](https://levltech.atlassian.net/browse/MEROSP-1132){: data-inline-card='' }   \nshould we just make it `1.0.0`?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/157/_/diff#comment-358849437"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a}   \nI\u2019m not sure if a 1.0.0 tag is sufficient, since there can be similar tags for different meta versions  \nbut i agree that it might cause problems with cujo tag standard, so i\u2019ll change it to be 1.0.0 format", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/157/_/diff#comment-358883703"}}
{"title": "fix unclosed bracket", "number": 158, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/158", "body": ""}
{"title": "set models tag in format 1.0.0", "number": 159, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/159", "body": ""}
{"title": "Feature/terraform cluster creation", "number": 16, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/16", "body": "\n\nincrease prometheus storage space\nperformance cluster\nnew vpc id\nredis chart\npg config\nupdate vpc id\nadd cache node group\nrestore cluster creation\nvpc+eks\nprivate eks , add vpn , add vpc s3 access\nadd eks managed sg to vpn\nfix kafka version\nremoved charts\ndeleted old files , add argocd helm chart , add tf certs logic\n\n"}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} \n\n1. Why we still have argocd?\n2. Old cluster creation should work\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/16/_/diff#comment-271178677"}}
{"title": "MEROSP-2688 helm chart and infra changes for bitbucket self-hosted runner", "number": 160, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/160", "body": ""}
{"title": "MEROSP-2688 helm chart and infra changes for bitbucket self-hosted runner", "number": 161, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/161", "body": ""}
{"title": "MEROSP-2688 fix optional repository for runners", "number": 162, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/162", "body": ""}
{"title": "fix optional repository for runners", "number": 163, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/163", "body": ""}
{"title": "update lndi lambda tg values", "number": 164, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/164", "body": ""}
{"title": "remove models tag env var + update models meta version env var name", "number": 165, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/165", "body": ""}
{"title": "MEROSP-2641", "number": 166, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/166", "body": "wip\nadd filter events list\nadd filter events list\n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} @{637f5c5e3e79f12e572115d7} please coordinate with cujo prior deploying this change, as this might require their support", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/166/_/diff#comment-360791472"}}
{"title": "add ListBucket permissions to s3 bucket", "number": 167, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/167", "body": ""}
{"title": "fix s3 list permissions", "number": 168, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/168", "body": ""}
{"title": "LDI-243 fix superset not accessible through LB", "number": 169, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/169", "body": ""}
{"title": "Feature/scripts", "number": 17, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/17", "body": "\n\nmodify msk certs script\n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} please add:\n\n1. example of invocation\n2. output path of the cert fles\n3. \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/17/_/diff#comment-277867883"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/17/_/diff#comment-277873385"}}
{"title": "LDI-243 fix superset not accessible through LB", "number": 170, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/170", "body": ""}
{"title": "fix failed to adjust OOM score for shim", "number": 171, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/171", "body": ""}
{"title": "fix failed to adjust OOM score for shim", "number": 172, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/172", "body": ""}
{"title": "LDI-249 infra deploy", "number": 173, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/173", "body": "Pipeline Changes\nAdded Conditions\nRemoved Conditions\nRemoved Conditions\n\n"}
{"title": "LDI-126 TF module for agent lifecycle lambda infra", "number": 174, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/174", "body": ""}
{"title": "MEROSP-2602 cujo CD", "number": 175, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/175", "body": "test pipeline\nMinor Changes\n\n"}
{"title": "LDI-54 pipelines for shutting down env", "number": 176, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/176", "body": ""}
{"comment": {"body": "@{6371fc00f48fbd9b62d30647} does Bitbucket pipelines supports cron trigger?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/176/_/diff#comment-373914495"}}
{"comment": {"body": "Yes it does [https://confluence.atlassian.com/bitbucket/scheduled-builds************************.html](https://confluence.atlassian.com/bitbucket/scheduled-builds************************.html){: data-inline-card='' }\n\n![](https://bitbucket.org/repo/p9y6yb4/images/1444292751-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/176/_/diff#comment-373915636"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} can we have the code in script that called from Bitbucket pipelines? Similar to what we are doing with makefile? @{5fd5d5149edf2800759cc96d} FYI", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/176/_/diff#comment-373917978"}}
{"comment": {"body": "Done. Please review and let me know if there are any changes", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/176/_/diff#comment-374217640"}}
{"title": "fix error log ts extraction", "number": 177, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/177", "body": ""}
{"title": "fix error log ts extraction at cujo", "number": 178, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/178", "body": ""}
{"title": "LDI-468 pipeline to manually trigger scan of an ECR image", "number": 179, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/179", "body": ""}
{"title": "dynamo init", "number": 18, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/18", "body": ""}
{"title": "LDI-468 pipeline to manually scan an ECR image", "number": 180, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/180", "body": ""}
{"title": "add lambda grafana dashboard", "number": 181, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/181", "body": ""}
{"title": "Tagging-Schema Changes", "number": 182, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/182", "body": "Tagging Schema changes for TG"}
{"title": "LDI-492 Tagging Schema TF", "number": 183, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/183", "body": "Tagging Schema TF changes\nTagging Schema Changes for TF\nTagging schema changes for TF\n\n"}
{"title": "cache changes", "number": 184, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/184", "body": ""}
{"comment": {"body": "If we are already pushing an image to ECR, is there a reason we need artifact for the same? Maybe we could do a docker pull when using it instead of using an artifact, it should be faster", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/184/_/diff#comment-371923689"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} previously as I tested , using docker pull was taking more time instead of using the artifacts. So I have removed the steps for pushing the image to ECR and have gone for the artifacts based approach. \n\nBelow link contains the data that I collected while doing the test for different approach:\n\n[https://docs.google.com/spreadsheets/d/1kYuZn6avF2vbynEhb5e-yxiBSM3JwtMkxTnD7z4zS9c/edit#gid=0](https://docs.google.com/spreadsheets/d/1kYuZn6avF2vbynEhb5e-yxiBSM3JwtMkxTnD7z4zS9c/edit#gid=0){: data-inline-card='' } \n\nPlease let me know in case of any queries.\n\nThanks", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/184/_/diff#comment-371945006"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} can you please have a look and approve this?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/184/_/diff#comment-372276527"}}
{"title": "cache changes", "number": 185, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/185", "body": ""}
{"title": "fix scale scripts", "number": 186, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/186", "body": ""}
{"title": "LDI-778 decrease ELK disk size", "number": 187, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/187", "body": ""}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} \n\nCan you prepare the retantion policy, remove all logs older than 30 days from kibana.", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/187/_/diff#comment-374889406"}}
{"comment": {"body": "I\u2019ll create an ILM policy in Elasticsearch for both environments that removes logs older than 30 days. For now, I increased disk space to unlock and need to get this merge to sync the code with existing EKS cluster configuration.\n\nILM reference - [https://www.elastic.co/guide/en/elasticsearch/reference/current/index-lifecycle-management.html](https://www.elastic.co/guide/en/elasticsearch/reference/current/index-lifecycle-management.html){: data-inline-card='' }", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/187/_/diff#comment-374895601"}}
{"title": "LDI-778 decrease ELK disk size", "number": 188, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/188", "body": ""}
{"title": "increase eros-cujo msk disk size", "number": 189, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/189", "body": ""}
{"title": "Dev", "number": 19, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/19", "body": "dynamo init\ndynamo\ns3 and dynamo db endpoits\ntable will be created in tenant creation\ncreate devices table\nreplicas-later\nsingle az cluster\n\n"}
{"title": "Scale test changes", "number": 190, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/190", "body": "LDI-2 - multiple containers in task\nTNQ - temp - (don't push)\nLDI 2 - scale test configurations\nLDI 2 - wip\nwip\nwip\nwip\nwip\nwip\nwip\nwip\nwip\nLDI 2 - wip\nLDI 2 - wip\nwip\nwip\nfix tg-plan\nfix permission\nfix permission\nfix permission\nwip\nwip\nwip\nwip\nLDI 2 - add healthcheck port parameter\nLDI-2 - scale-test pipeline\nLDI-2 - scale_test pipeline\nLDI-2 - scale_test pipeline\nLDI-2 - scale_test pipeline\nLDI-2 - scale_test pipeline\nLDI-2 - scale_test pipeline\nLDI-2 - scale_test pipeline\nAdded CLUSTER_NAME in pipeline envs\nLDI-2 - scale_test pipeline\nLDI-2 - scale_test pipeline\nchange image tag\nbitbucket-pipelines.yml edited online with Bitbucket\ntag dunamo db resources for cost analysis\nbuild tg artifact\n-\n-\nscale test config changes\nchange image tag\nchange image tag - 1.5.22\ncheck tag - 1.5.21\ncheck tag - 1.5.21\nprepare for longevity - 10 tasks\nprepare for longevity - update image\nprepare for longevity - update image\nprepare for longevity - adjust number of workers\nchange partitations\nchange image\nfix tf lock\nfix tg destroy kafka topic\nchange image\nchange partitations and capacity\nlast image tag\nresolve conflicts\n\n"}
{"comment": {"body": "what is it ?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/190/_/diff#comment-378529842"}}
{"comment": {"body": "what is it ?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/190/_/diff#comment-378529884"}}
{"title": "update aws-auth cm for ci node group", "number": 191, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/191", "body": ""}
{"title": "update aws-auth cm for ci node group", "number": 192, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/192", "body": ""}
{"title": "LDI-844 comcast tg files", "number": 193, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/193", "body": "add comcast tg files\nupdate comcast tg variables\n\n"}
{"title": "LDI-845 add charter tg files", "number": 194, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/194", "body": ""}
{"title": "increase eros-cujo msk storage", "number": 195, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/195", "body": ""}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} what is this change about?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/195/_/diff#comment-379747954"}}
{"comment": {"body": "This was a minor bug found in akhq where it\u2019s asking user to set `mskCertJKS` however the validation was happening against `mskCertJSK` so there was a typo in variable name", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/195/_/diff#comment-379749088"}}
{"title": "update msk retention", "number": 196, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/196", "body": ""}
{"title": "update msk config", "number": 197, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/197", "body": ""}
{"title": "setup msk disk usage alarm", "number": 198, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/198", "body": ""}
{"title": "setup msk disk usage alarm", "number": 199, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/199", "body": ""}
{"title": "working dahsboard", "number": 2, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/2", "body": ""}
{"comment": {"body": "can we add postgres dashboard too?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/2/_/diff#comment-226324549"}}
{"comment": {"body": "Done.", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/2/_/diff#comment-226327549"}}
{"title": "Feature/telegraf to cloudwatch", "number": 20, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/20", "body": "WIP\nworking version\n\n"}
{"title": "LDI-915 Increase disk size on ci node group", "number": 200, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/200", "body": ""}
{"title": "LDI-915 increase disk size of ci node group", "number": 201, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/201", "body": ""}
{"title": "Adding new users to userMapRoles.yaml", "number": 202, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/202", "body": ""}
{"title": "[WIP] classifier auto scaling based on multiple metrics", "number": 203, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/203", "body": ""}
{"comment": {"body": "why 1 ? @{634e54561db4d2ebcf611e5a} ", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/203/_/diff#comment-398390052"}}
{"comment": {"body": "The values here are just an example for now. We can change it as per the requirement as we go along with the testing. Please let me know if you\u2019d like to change it to some other value. ", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/203/_/diff#comment-398391890"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} the defaults should be similar to deployment or our baseline.", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/203/_/diff#comment-398392095"}}
{"title": "LDI-1161:add agent_id and agent_version", "number": 204, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/204", "body": ""}
{"title": "LDI-1156 increase ci node group for reportportal", "number": 205, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/205", "body": ""}
{"title": "Updated -version of scale_dashboard", "number": 206, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/206", "body": ""}
{"title": "remove cache nodegroup", "number": 21, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/21", "body": ""}
{"title": "add cloudwatch metrics manifests", "number": 22, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/22", "body": ""}
{"title": "Feature/k8s ingress", "number": 23, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/23", "body": "add cloudwatch metrics manifests\nremoved k8s api metrics\nadd ingress to monitoring tools\nremoved non relevant files\n\n"}
{"title": "Feature/analytics", "number": 24, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/24", "body": "analytics infra\nWIP\n4 instances\ninfra update\nworking schemas\nadd alb config script , modified ingress template\nglue tables\n\n"}
{"title": "enable topic deletion", "number": 25, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/25", "body": ""}
{"title": "Master", "number": 26, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/26", "body": "WIP\nvpc+eks\nWIP\nWIP, private eks , add vpn , add vpc s3 access\nadd eks managed sg to vpn\nfix kafka version\nrefactoring\nWIP msk done\nremoved charts\ndeleted old files , add argocd helm chart , add tf certs logic\nclean old structure\nWIP\nadd scripts for certs\ndelete test files\nfix script name\nfix msk script\nfix script\nfix vpn script\nfix msk script\npatch user map configmap\nadd devops-ci user\nhelm install argocd + externaldns\nargocd values\nWIP\nuse t type for utilities\nWIP\nopen eks to the world\nremove argocd installation\ninclude telegraf remove kafka\nadd cloudwatch dynamo and firehose policies\nstatsd telegraf config\ntelegraf collect(d) agent metrics configuration\nupgrade superset to the 1.2.0\nremove obsolete file\nrefactor cluster vars\nrename dev cluster\ndont install postgres and redis as infrastructure component\ntelegraf udp config to cloudwatch\ninstall infrastructure components\nignore local state\ntf extensions\nobsolete aws components\nresolve conflict\nupdate readme\nrefactor client vpn script\nrefactor client vpn script\nfix passphrasebug\nfix passphrasebug + add modifier to new vpn endpoint\nmodify msk certs script\nwrap get cert with try execpt\n\nMerged in feature/telegraf_to_cloudwatch (pull request #20)\nFeature/telegraf to cloudwatch\n\nWIP\nworking version\nupdate telegraf classes\nMerged master into feature/telegraf_to_cloudwatch\n\nApproved-by: Itai Zolberg\n\n\nMerged in feature/dynmodb_remove_cache_nodegroup (pull request #21)\nremove cache nodegroup\n\nremove cache nodegroup\nbug fix\n\nApproved-by: Shimon Goulkarov\n\n\nfixed nodegroup security\n\nrestore grafana files\ndockerize all\nfix conflicts\nfix readme format\nfix readme format\nfix readme format\nadd info to readme\nupdate grafana classification dashboard\n\nMerged in feature/analytics (pull request #24)\nFeature/analytics\n\nMerge branch 'master' of bitbucket.org:levl/eros-infrastructure into feature/analytics\nWIP\n4 instances\ninfra update\nworking schemas\nadd alb config script , modified ingress template\nglue tables\nMerge branch 'feature/analytics' of bitbucket.org:levl/eros-infrastructure into feature/analytics\nMerge branch 'master' of bitbucket.org:levl/eros-infrastructure into feature/analytics\nMerged in enable_topic_deletion (pull request #25)\n\nenable topic deletion\n\nMerge branch 'master' of bitbucket.org:levl/eros-infrastructure into enable_topic_deletion\nenable topic deletion\nMerged feature/analytics into enable_topic_deletion\n\nApprove\n\n\n"}
{"title": "Add Glue shcema and table for Athena - Error Log", "number": 27, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/27", "body": ""}
{"title": "schema changes", "number": 28, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/28", "body": ""}
{"comment": {"body": "Let\u2019s unite with my PR to make single terraform apply?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/28/_/diff#comment-284122789"}}
{"title": "Bug/vpn passphrase encoding", "number": 29, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/29", "body": "\n\nconvert passphrase from string to byte object\n\n"}
{"title": "Monitoring chart refactor", "number": 3, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/3", "body": "new charts\nfixing versions\nelk chart\nupdated exported\ncleanup + new script\nadded debug flag\n\n"}
{"title": "change subents annotation", "number": 30, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/30", "body": "\n"}
{"comment": {"body": "does all elasticsearch in elk namespace?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/30/_/diff#comment-286429954"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/30/_/diff#comment-286430317"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} let\u2019s rollout and merge", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/30/_/diff#comment-287997807"}}
{"title": "update tf and add athena views scripts", "number": 31, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/31", "body": ""}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} the limit should be by time, not by rows", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/31/_/diff#comment-289347037"}}
{"title": "Update prom alarms", "number": 32, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/32", "body": "updated prom stack version\nbring back prom alerts for some basic eros metrics\n\n"}
{"title": "Grafana dashboards for Eros", "number": 33, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/33", "body": "Added JSONs for dashboards\n\neros_all.json includes all of the metrics in one board\nThe other files divide the metrics over several screens such that they will fit in the window and will be used in a Grafana playlist\n\n\n\nAttempt to update the chart to have persistent storage for Grafana.\n\n\n"}
{"title": "give utilities NG access to athena", "number": 34, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/34", "body": ""}
{"title": "Add storage claim for Grafana", "number": 35, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/35", "body": ""}
{"title": "Tf athena lambda", "number": 36, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/36", "body": "\n\nadd athena query lambda cron function\n\n"}
{"title": "Update charts", "number": 37, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/37", "body": "\n\nupdate lb and DNS charts to a new version\n\n"}
{"title": "align schema to decision duration changes", "number": 38, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/38", "body": ""}
{"title": "Feature/deduped devices rate metric", "number": 39, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/39", "body": "Added the deduped_devices lambda queries and the sql-exporter side queries.\nThis code refers to Identification service operational KPIs, section (b):\nThe rate of de-deduped devices - the number of devices that has more than a single MAC address.\n"}
{"title": "New account migration", "number": 4, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/4", "body": "Those updates/cleanup were made during the last workweek."}
{"comment": {"body": "please add the following namespaces to support per developer environment  \nitai\n\nariel\n\ntamir\n\nshimon\n\nnadav\n\ngrisha\n\ndavid\n\namir\n\nsergey\n\ngal", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/4/_/diff#comment-*********"}}
{"comment": {"body": "what is the equivalent solution for per user access grant?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/4/_/diff#comment-*********"}}
{"comment": {"body": "Ok, I\u2019ll keep it clean and add it as a chart so we could mange it properly ", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/4/_/diff#comment-*********"}}
{"comment": {"body": "I really prefer to manage everything in one place via the okta-aws integration, if we can give the role we assign to the developer permissions by group it would be the preferred way in my opinion.\n\nIn any case this is related to the cli sso auth problem, still cannot authenticate cli applications. ", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/4/_/diff#comment-230270421"}}
{"title": "Bugfix/Identification level rate metric", "number": 40, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/40", "body": "Fixed the identification level queries to get only deduped events and ignore N_A identification level values.\nThis code refers to Identification service operational KPIs, section (d):\nThe rate of of de-duped identification events for each level for device identification"}
{"title": "Feature/newly generated levl ids", "number": 41, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/41", "body": "Added the newly generated levl ids lambda queries.\nThis code refers to Identification service operational KPIs, section (a):\nThe rate of devices assigned with a newly generated LEVL-IDs"}
{"title": "Feature/strong identifiers metric", "number": 42, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/42", "body": "Added the strong identifiers lambda queries and the sql-exporter side queries.\nThis code refers to Identification service operational KPIs, section (e):\nThe rate of device that do not had not captured a strong identifier"}
{"title": "Renamed glue_table_v1_newly_generated_levl_ids.tf -> glue_tables_v1_newly_generated_levl_ids.tf", "number": 43, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/43", "body": ""}
{"title": "Feature/OS resolution metric", "number": 44, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/44", "body": "Added the tables, lambda queries and sql-exporter side queries for the os_resolution metric"}
{"title": "Bugfix/Not counting rows with empty vendors", "number": 45, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/45", "body": ""}
{"title": "Feature/model resolution metric", "number": 46, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/46", "body": "Added the relevant lambda queries, sql-exporter side queries and tables definitions for the model_resolution metric"}
{"title": "add new queries and lambdas", "number": 47, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/47", "body": ""}
{"title": "Fixed some wrong fields accesses in the queries", "number": 48, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/48", "body": ""}
{"title": "Beautify schemas", "number": 49, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/49", "body": "add new queries and lambdas\nbeautify schemas\n\n"}
{"comment": {"body": "remove?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/49/_/diff#comment-301367923"}}
{"title": "new repos", "number": 5, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/5", "body": ""}
{"title": "Fixed a wrong field access in os_resolution metric", "number": 50, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/50", "body": ""}
{"title": "add pv to superset", "number": 51, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/51", "body": ""}
{"title": "Matched the device_data_health changes that were made in the classifier", "number": 52, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/52", "body": "Refer to this PR\nThis change is made here to update the new shcema in Athena"}
{"title": "Ingress latency", "number": 53, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/53", "body": "fix ingress latency\nadd alb annotion to eks subnets\n\n"}
{"title": "fix some sql files", "number": 54, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/54", "body": ""}
{"title": "Fix queries after schema change", "number": 55, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/55", "body": "Following MEROSP-777, this PR fixes all the queries according to the new device intelligence result schema that was merged yesterday"}
{"title": "add nodes roles to aws auth cm", "number": 56, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/56", "body": "deleted ArgoCD helm chart,\nI've added new mapRoles to our aws-auth configmap,\ndue to the new EKS version Nodes need permission to access the EKS cluster.\n"}
{"title": "Hotfix/align schema decision log", "number": 57, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/57", "body": "align schema to decision duration changes\nupdate decision log schema\n\n"}
{"title": "fix identification level by cpe type", "number": 58, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/58", "body": ""}
{"title": "changing names of timestamps to be without units (usec)", "number": 59, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/59", "body": ""}
{"title": "new vpc id", "number": 6, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/6", "body": ""}
{"title": "change fields expected types according to schema", "number": 60, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/60", "body": ""}
{"title": "MEROSP-1016 Updated the assoc and probe fields", "number": 61, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/61", "body": "Refer to  :slight_smile:"}
{"title": "updated result schema", "number": 62, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/62", "body": ""}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} @{5dbeb866c424110de52552cc} @{5ed4f1de9a64eb0c1e78f73b} can you double check the schema? it was fixed today.. and now reverted\u2026\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/62/_/diff#comment-307640844"}}
{"comment": {"body": "ive copied it from the latest commit on release/cujo branch", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/62/_/diff#comment-307641523"}}
{"title": "Feature/cujo schema align order", "number": 63, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/63", "body": "align schema to decision duration changes\nupdate decision log schema\nalign cujo schema\n\n"}
{"title": "Feature/MEROSP-823 change schema - new fields", "number": 64, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/64", "body": "Add 4 fields for data collection during service logic\n\n"}
{"comment": {"body": "Re-target to master :slight_smile: dev branch is irrelevant in this repo", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/64/_/diff#comment-312978689"}}
{"title": "Update decision log table path in S3", "number": 65, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/65", "body": "Refer to  :slight_smile:"}
{"title": "Fixed all the lamda queries according to the updated schema", "number": 66, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/66", "body": "All the queries are now simpler, and extract the needed data only from the decision log (and not the result log as well).\nIn addition, some changes had to be made to match all the recent changes that were made."}
{"title": "Updated device intelligece result table path from v1 to v2", "number": 67, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/67", "body": ""}
{"title": "Updated the S3 path of the error log table from v1 to v2", "number": 68, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/68", "body": ""}
{"title": "fix datasource uid", "number": 69, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/69", "body": "grafana datasource uid should be prometheus\nand not hardcoded, please note @{5dbeb866c424110de52552cc}  \n"}
{"title": "Filebeat logs", "number": 7, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/7", "body": "chagnes to filebeat values\nchagnes to filebeat values\nchagnes to filebeat values\nlogstash conf\nlogstash conf\nlogstash conf\nFixed ingress url and addedd Logstash\n\n"}
{"title": "review changes", "number": 70, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/70", "body": ""}
{"title": "Add features config fields to decision log schema", "number": 71, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/71", "body": "Added features config fields to schema\nwas prior used field was missing\nRemove uuid from tenant id (just string)\n\n"}
{"comment": {"body": "how does this affects all the current data? are we breaking here backward compatibility?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/71/_/diff#comment-317043262"}}
{"comment": {"body": "No, since it\u2019s just a logical type. As far as Glue and Athena are concerned, this is a string.", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/71/_/diff#comment-317044217"}}
{"title": "MEROSP-1148 change schema v3", "number": 72, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/72", "body": "Add WS Discovery field to intelligence log v3\n"}
{"title": "MEROSP-1148 change schema", "number": 73, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/73", "body": "Add WS Discovery field to intelligence log\n"}
{"title": "MEROSP-1277 Added the hostname group field to the decision log", "number": 74, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/74", "body": "Refer to this PR :slight_smile:"}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} should this be applied to eros-cujo too? \n\nif so need to cherrypick it to cujo branch\n\non a separate PR \\(regardless of who does the cherry picking\\)", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/74/_/diff#comment-334717960"}}
{"title": "Change WS Discovery data type to list of strings", "number": 75, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/75", "body": ""}
{"title": "Change WS Discovery data type to list of strings", "number": 76, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/76", "body": ""}
{"comment": {"body": "Please redirect to master @{6252eba45d1e700069ad0104} ", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/76/_/diff#comment-320812909"}}
{"title": "Cujo fix analytics table version names", "number": 77, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/77", "body": "Rename tables version part to match existing ETL queries\n"}
{"title": "terraformed all firehose", "number": 78, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/78", "body": ""}
{"title": "fix s3 pathes", "number": 79, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/79", "body": ""}
{"title": "add automation namespace", "number": 8, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/8", "body": ""}
{"title": "MEROSP-1446 terragrunt", "number": 80, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/80", "body": "terragrunt initial commit\nadded cujo tf files\nset ecs classifier service tg code\nfinished kinesis + firehose tg\nfix helath check\n\n"}
{"title": "add dockerfile and bitbucket pipelines", "number": 81, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/81", "body": ""}
{"title": "fix validate function", "number": 82, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/82", "body": ""}
{"title": "add trgger condition", "number": 83, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/83", "body": ""}
{"title": "MEROSP-1446 terragrunt", "number": 84, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/84", "body": "fix some vars\nrevert to cujo's pathes\n\n"}
{"title": "Add rpvr & change ws discovery type", "number": 85, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/85", "body": ""}
{"title": "MEROSP-1446 terragrunt", "number": 86, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/86", "body": "fix validate pipeline\ntrigger pipeline\n\n"}
{"title": "back to levl path", "number": 87, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/87", "body": ""}
{"title": "fix api + remove tg/tf cache", "number": 88, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/88", "body": ""}
{"title": "align result table with currnent schema", "number": 89, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/89", "body": ""}
{"title": "Performance cluster", "number": 9, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/9", "body": "increate prometheus storage space\nperformance cluster\nnew vpc id\n\n"}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} cna we merge?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/9/_/diff#comment-252790205"}}
{"title": "change long to bigint", "number": 90, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/90", "body": ""}
{"title": "akhq chart", "number": 91, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/91", "body": "@{6085103f5797db006947d59a} does it required?"}
{"title": "Feature/MEROSP-1631", "number": 92, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/92", "body": "install telegraf cluster (helm chart) with udp endpoint and prometheus client\ninstall service monitor to scrape to prometheus\n\n"}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} missing the prometheus changes?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/92/_/diff#comment-337262829"}}
{"comment": {"body": "@{5f82bf320756940075db755e} what prometheus changes needed?\n\nservicemonitor inject the scraping job to prometheus", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/92/_/diff#comment-337529968"}}
{"title": "Ecs", "number": 93, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/93", "body": "wip\nfix cujo's pr comments\nadd new cujo's files\n\n"}
{"title": "change cujo's aws region to ours", "number": 94, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/94", "body": ""}
{"title": "MEROSP-1077", "number": 95, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/95", "body": "add parameters (note set the name yet ( need to be injected from the tg)\nparameter store tf\nString\nchange cujo's aws region to ours\n\n"}
{"title": "add latest users onboarded for kubectl", "number": 96, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/96", "body": ""}
{"comment": {"body": "@{5f82bf320756940075db755e} \n\nmake sure to apply it once merge.\n\n```\nkubectl apply -f userMapRoles.yaml \n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/96/_/diff#comment-338552446"}}
{"comment": {"body": "Did you mean to tag @{5f82bf320756940075db755e} ?", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/96/_/diff#comment-338566841"}}
{"comment": {"body": "yes, sorry", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/96/_/diff#comment-338567468"}}
{"title": "MEROSP-1901 Added missing fields to v3", "number": 97, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/97", "body": "The following fields were missing from our v3 glue schema:\n['device_model_hostname_group'\n'device_model_ws_discovery_uuid'\n'identification_process_info_features_mode'\n'identification_process_info_features_validity'\n'identification_process_info_features_is_mode_default'\n'context_process_info_features_mode'\n'context_process_info_features_validity'\n'context_process_info_features_is_mode_default']\nThis PR adds them."}
{"title": "MEROSP-1882: support latest ML2 result schema data model", "number": 98, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/98", "body": ""}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} doe this the right convention for int? I didn\u2019t find any reference and in other examples we are using bigint..", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/98/_/diff#comment-338847769"}}
{"comment": {"body": "@{5f82bf320756940075db755e} you should use \u201cinteger\u201d   \n[https://prestodb.io/docs/current/language/types.html](https://prestodb.io/docs/current/language/types.html){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/98/_/diff#comment-338852718"}}
{"title": "Change the sort key in the table to device mac address", "number": 99, "htmlUrl": "https://bitbucket.org/levl/eros-infrastructure/pull-requests/99", "body": "Add a second table with the name devices_mac that uses a sort key from the device mac address\n"}
{"title": "merge frontend changes with latest", "number": 1, "htmlUrl": "https://bitbucket.org/levl/frontend_ui/pull-requests/1", "body": ""}
{"title": "LDI-12 lambda code", "number": 1, "htmlUrl": "https://bitbucket.org/levl/agent-lifecycle-lambda/pull-requests/1", "body": "inital lambda deletion logic\ninital lambda deletion logic\n\n"}
{"comment": {"body": "Let\u2019s add metrics, ut, try except for robustness", "htmlUrl": "https://bitbucket.org/levl/agent-lifecycle-lambda/pull-requests/1/_/diff#comment-367839274"}}
{"comment": {"body": "Sure thats inital draft", "htmlUrl": "https://bitbucket.org/levl/agent-lifecycle-lambda/pull-requests/1/_/diff#comment-367839556"}}
{"title": "LDI-130 Add unit tests in CI", "number": 2, "htmlUrl": "https://bitbucket.org/levl/agent-lifecycle-lambda/pull-requests/2", "body": "Add unit tests in CI\nadd region to boto3 obj init\nupdate requirements-test.txt\n\n"}
{"title": "LDI-130", "number": 3, "htmlUrl": "https://bitbucket.org/levl/agent-lifecycle-lambda/pull-requests/3", "body": "Add unit tests in CI\nTest\nTest\nadd region to boto3 obj init\nadd region to boto3 obj init\nchange default aws region\nchange default aws region\nadd logs to debug\nremove filter exp\nremove logs\n\n"}
{"title": "LDI-1040 glue job run status", "number": 1, "htmlUrl": "https://bitbucket.org/levl/glue-job-run-status/pull-requests/1", "body": "LDI-1040 Add lambda code, add README.md\nLDI-1040 Add terraform module for monitoring\n\n"}
{"title": "LDI-1368 Make monitoring lambda to handle multiple sources and targets", "number": 2, "htmlUrl": "https://bitbucket.org/levl/glue-job-run-status/pull-requests/2", "body": "LDI-1368 Update lambda to use file-based configuration\nLDI-1368 Fix lambda code issues\nLDI-1368 Update terraform module, update README.md\n\n"}
{"title": "skeleton python package with ci/cd and local, remote envs vscode support", "number": 1, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/1", "body": ""}
{"title": "L2 chipset", "number": 10, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/10", "body": "added chipset and fixed int/float/inf/nan\nhandles chipset\n\n"}
{"comment": {"body": "put this file in git lfs", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/10/_/diff#comment-321823583"}}
{"title": "new model analysis", "number": 11, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/11", "body": "new model analysis"}
{"title": "Devices db oneplus 10 pro lab", "number": 12, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/12", "body": "updating android devices\nMEROSP-1630 new db after android 13\n\n"}
{"title": "Devices db additional info 2", "number": 13, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/13", "body": "supported_tx_power columns\nfixed tx power and 5ghz band devices\nbackup - 802.11r/v/k\nbackup\n\n"}
{"title": "MEROSP-1692 apple devices sep 22", "number": 14, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/14", "body": "new apple devices\nnew apple devices. need to fix maxmial os version\n\n"}
{"comment": {"body": "@{621df03094f7e20069fd6ab2}  what is that?", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/14/_/diff#comment-352842575"}}
{"comment": {"body": "an old PR we forgot", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/14/_/diff#comment-352844938"}}
{"comment": {"body": "delete PR if not needed?", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/14/_/diff#comment-354544015"}}
{"title": "User agent analyzer based on aho-corasick sting matching", "number": 15, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/15", "body": ""}
{"comment": {"body": "use relative paths", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/15/_/diff#comment-334200481"}}
{"comment": {"body": "what does CG mean? can you make it more verbose?", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/15/_/diff#comment-334200663"}}
{"title": "Bugfix/MEROSP-1922 classifier should prepend ve", "number": 16, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/16", "body": "Add a function that adds a user_facing_name column to the devices db. This column is the same as specific_name for apple devices and devices that already have the vendor name as part of the specific_name. For all other devices, the value in the new column is a concatenation of the vendor, the space character and the specific_name.\n"}
{"title": "Bugfix/MEROSP-1922 classifier should prepend ve", "number": 17, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/17", "body": "strip vendor from specific name\n\n"}
{"title": "Devices DB modifications in preparation for MEROSP-1922", "number": 18, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/18", "body": "Replace LGE with LG  and apply logic to canonical name column"}
{"title": "Ipad iphone confussion", "number": 19, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/19", "body": ""}
{"comment": {"body": "did you merge instances of the same type?", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/19/_/diff#comment-354952722"}}
{"comment": {"body": "yes, line 28", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/19/_/diff#comment-354954785"}}
{"title": "Chunks", "number": 2, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/2", "body": "updates\nadding chunks\n\n"}
{"title": "Icmp timestamp analysis", "number": 20, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20", "body": "Initial exploration of timing features"}
{"comment": {"body": "Can you add the data and the artifacts so that it\u2019s reproducable?\n\nthe pcap files should be stored as git-lfs files \\(ask @{62e7c839e50f2f2a395430c2} \\)", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-362575342"}}
{"comment": {"body": "or store these files somewhere in our cloud and add a link to them in the code\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-362576519"}}
{"comment": {"body": "might as well", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-362576658"}}
{"comment": {"body": "is it possible to do it with tcpdump?", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-362581854"}}
{"comment": {"body": "I agree with @{63553fd9b0b6ef035648ab9a} 's suggestion. I think it will be wasteful for everyone to sync all these many gigabytes of data in git-lfs. Currently the files are on recsys. Maybe they should be on S3 instead, if anyone has a particular suggestion/preference they are welcome to share it.", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-362581923"}}
{"comment": {"body": "I\u2019ve just saw this morning on the train a mlops youtube about version control of data:  \n[https://www.youtube.com/watch?v=kZKAuShWF0s&list=PL7WG7YrwYcnDBDuCkFbcyjnZQrdskFsBz&index=2](https://www.youtube.com/watch?v=kZKAuShWF0s&list=PL7WG7YrwYcnDBDuCkFbcyjnZQrdskFsBz&index=2){: data-inline-card='' }   \nShe recommends using dvc which is integrated with git.  \nIt handles version control of data without uploading the data to the code repository.  \nI\u2019ll check it, present it and if agreed we can use it. ", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-362584052"}}
{"comment": {"body": "We have a griffin bucket: [https://s3.console.aws.amazon.com/s3/buckets/griffin.algo.levl.data?region=ca-central-1&tab=objects](https://s3.console.aws.amazon.com/s3/buckets/griffin.algo.levl.data?region=ca-central-1&tab=objects)", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-362584427"}}
{"comment": {"body": "Probably, but I\u2019m not sure.  \nIt was convenient to work with TShark, since it possible to open the pcap file in WireShark, select each field I\u2019m interested in, right click, and copy the field name to the command. ", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-362586423"}}
{"comment": {"body": "good Idea. maybe for now just upload the data to S3. And once we bring here an MLOPS person, we can discuss with him.", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-363505352"}}
{"comment": {"body": "Yes, I\u2019ve uploaded to S3 and updated the code to read the files from there.  \nThis would not prevent data version control \\(by dvc or another tool\\) since these tools enable to keep the data on S3. ", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/20/_/diff#comment-363549176"}}
{"title": "remove unneeded f strings", "number": 21, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/21", "body": ""}
{"title": "LDI-517 Add code and example rules for statistically filtering the devices db", "number": 22, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22", "body": "This PR is made in continuation to the approved design of LDI-512, with the goal of creating a filtered devices db csv that does not have devices that are very uncommon and that product can control the rules by which devices are filtered through some simple value editing in tables."}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2}   \n1\\. you have index in the csvs, I think it this column should be removed.  \n2\\. from where is the statistics?  \n3\\. **grace\\_months** - has no file extension, what type file should it be \\(some kind of text\\)", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371243418"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} These are the statistics files generated by @{63b2a7992c70aae1e6faa958} from cujo data and the same format used by @{63b6c1aaf3e7004f77ff2273} in the prior logic implementation. I don\u2019t want to break it here. If we want to remove the index then let\u2019s do it across the board.\n\nI\u2019ll add an extension to the grace months", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371243619"}}
{"comment": {"body": "Thank you", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371243867"}}
{"comment": {"body": "can you add \\(even as a comment here\\) a result file?  \n`devices_db_filtered.csv`", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371251036"}}
{"comment": {"body": "[https://drive.google.com/file/d/1vOkxAJ3RFmpseuMErAg8uJttDNVZyQT2/view?usp=share_link](https://drive.google.com/file/d/1vOkxAJ3RFmpseuMErAg8uJttDNVZyQT2/view?usp=share_link){: data-inline-card='' } \n\nOf course it is a bit nonsense because I made up the rules as an example. Once product defines the rules they wants to have the result will reflect that.", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371251204"}}
{"comment": {"body": "make sure you keep the last rows\n\n```\nLenovo,Laptop,ThinkPad,,,,,Windows,Windows,,,,,,,,,,,,,,,,,,,,,,,,,,,,\nGoogle,Laptop,Chromebook,,,,,ChromeOS,ChromeOS,,,,,,,,,,,,,,,,,,,,,,,,,,,,\nSamsung,Laptop,Chromebook,,,,,ChromeOS,ChromeOS,,,,,,,,,,,,,,,,,,,,,,,,,,,,\n```\n\nthey have been added manually in order to complainte to Windows and ChromeOS\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371252123"}}
{"comment": {"body": "Fixed", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371252434"}}
{"comment": {"body": "Is it safe to assume that the devices\\_db in the code is updated?  \nIf not, it may be better to use a load\\_model function which may take it from S3.", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371256972"}}
{"comment": {"body": "After I\u2019ll merge prior\\_logic you would be able to access these data frames by:  \n`TypingConflictResolvertyping_probabilities[tier_name]`", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371257373"}}
{"comment": {"body": "Maybe it would be better to always sum over the vendors count, since it includes counts of devices which were not classified at the detailed levels. This way the percentages would be accurate and consistent over the different tiers.", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371258085"}}
{"comment": {"body": "Maybe it would be better to unify this loop with the previous one. It would require checking if a threshold exist for a given level but on the other hand it would reduce a significant repeating code. ", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371258526"}}
{"comment": {"body": "Maybe it would be better to move these 2 lines to the beginning of the section dealing with grace months filtering. ", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371258638"}}
{"comment": {"body": "where probabilities? :slight_smile: \n\nAll I see are frequencies.", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371338220"}}
{"comment": {"body": "A better pattern for storing configurations is having them in a JSON file. It\u2019s human-readable, can contain multiple configurations, supports data checks. It can be easily replaced with some external database, instead of storing them in the codebase.\n\nI suggest to put the thresholds and the grace months files in a single JSON file.", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/22/_/diff#comment-371339187"}}
{"title": "LDI-1304 cfo simulation", "number": 23, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/23", "body": "General simulation code for a weakID feature and a specific implementation for the proposed CFO algorithm ({: data-inline-card='' } )"}
{"title": "Added initial files", "number": 3, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/3", "body": "fixed ignore files\nAdded initial files"}
{"comment": {"body": "It\u2019s not a good idea to store many files in git \\(i\u2019m referencing to the \\*.html files\\). It\u2019s cluterring the PR, making the git process work harder \\(as there are many more files\\) and we can\u2019t focus on the actual code diffs.\n\nMove the HTML files to S3 or zip them before commiting them", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/3/_/diff#comment-290596166"}}
{"comment": {"body": "good job on the git-lfs :\\)", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/3/_/diff#comment-290596433"}}
{"comment": {"body": "where is this package used?", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/3/_/diff#comment-295256274"}}
{"title": "Nitzan/add ipq cfr", "number": 4, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/4", "body": "added ipq_cfr core scripts\nminor lint fix\nminor lint fix\nadded bitbucket pipeline\n\n"}
{"comment": {"body": "Great work!", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/4/_/diff#comment-293119975"}}
{"title": "Grisha/metrics and indexes", "number": 5, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/5", "body": "Implementation of the metrics and indexes calculation\nReference is this confluence page: \nSome updates to the example context package\n\n"}
{"comment": {"body": "PR is ready. I\u2019ll appreciate the code review", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/5/_/diff#comment-294807661"}}
{"comment": {"body": "Adding Itai, Nadav, Tamir\n\nRegards,\nShimon\n\n\u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d9\u05d5\u05dd \u05d4\u05f3, 14 \u05d1\u05d0\u05e4\u05e8\u05f3 2022, 15:53, \u05de\u05d0\u05ea Gregory kovelman \u200f<", "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/5/_/diff#comment-294817626"}}
{"title": "Dror wispr", "number": 6, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/6", "body": "wispr agent from levl-compute\n--wip-- [skip ci]\n--wip-- [skip ci]\nwispr agent\nwispr agent\n--wip-- [skip ci]\nnew watch pcaps and changed Mac OS X to macOS in final result regarding MEROSP-962\nMEROSP-1009: added iPadOS and watchOS manually\n\n"}
{"title": "Device db", "number": 7, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/7", "body": "added microsoft to the process\ncode styling\ncode styling\nbackup\nbackup2\nbackup2\nfixed lfs\nadded dummy windows device and minor code changes for lint\nfixed default values and print\nnew print\nlint fix\nlint fix for split os\nmanual patch for POCO MEROSP-976 issue\nfix poco in code\nfixed MEROSP-1010 bug manually, need to try in code - had problems running ; lint fixing\nbugfix MEROSP-1022: added new column of specific_os\nbugfix MEROSP-1022: added new column of specific_os code - need to check\nbugfix MEROSP-1022 in the code\n\n"}
{"title": "MEROSP-1168 canonical", "number": 8, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/8", "body": "--wip-- [skip ci]\n--wip-- [skip ci]\n--wip-- [skip ci]\nnothing new\n--wip-- [skip ci]\n--wip-- [skip ci]\nl2 data and code\nnew data collection\napple watch 7 label fixing and new recordings\ncompare the new data from michael to the old version\nnew compare after samsung fixing\nfixed android model name and vreated new data\nconcat multiple locations of l2 model\ncomparion of model between glinet and engenius\nnew data\nnew data\nnew model after band fix\ncreated croostab matrix in notebook and new reports\nnew data\nnew model ; glinet statistics\nnew model\nfixed device_db\nnew model\nfixed flake8\nmore flake8 fix\nfix flake8 - put value to bare except\nupgrade pip\ndata drom surface, iphone 5 and dry run\nignore venv folder\nlfs reports directory\n\n"}
{"title": "Devices db additional info", "number": 9, "htmlUrl": "https://bitbucket.org/levl/griffin-algo/pull-requests/9", "body": "additional info - {: data-inline-card='' } \nbackup\n\n"}
{"title": "Feature/run hwtests on jenkins", "number": 1, "htmlUrl": "https://bitbucket.org/levl/bosch_integration_restore/pull-requests/1", "body": "Enabled system tests through Jenkins\nTemporarly removed static analysis"}
{"title": "Api split", "number": 1, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/1", "body": "added api deployment\nadd api service yaml file\nadd configmap\nadd archiver api deployment\n\n\n"}
{"title": "Helm chart", "number": 10, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/10", "body": "\n\ndeploy helm chart\nadd helm deploy pipeline\nfix umbrella chart\nadd tenant id to pipeline\nfix api deployment\noverride redis an pg name\nadd batch size var\nfix notification chart\nadd custom stack\nfix chart\n\n"}
{"comment": {"body": "the maestro\\(@{5fd5d5149edf2800759cc96d} \\) should verify this ", "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/10/_/diff#comment-252726314"}}
{"title": "remove deployment attribute from pipeline", "number": 11, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/11", "body": "remove deployment attribute from pipeline"}
{"title": "Helm chart", "number": 12, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/12", "body": "add api topic var\ntest api chart\ntest api chart\ntest api chart\ntest api chart\ntest api chart\nadd update flag\nfix api chart\nadd metrics chart mv eros-classifier chart\nadd image tag modifier\nremoved image tag modifier\nedit classifier charts\nedit classifier charts\ndelete chart when removing tenant\nremove delete ns before creating\nadd init containers\nadd api init container\nchange topic notification\nfetch chart from s3\n\n"}
{"title": "adjust new s3 repo structure", "number": 13, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/13", "body": ""}
{"title": "add redis vars to bb pipelines, pass redis vars to api", "number": 14, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/14", "body": ""}
{"title": "updating chart dependency", "number": 15, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/15", "body": "\n\nupdating chart dependency\n\n"}
{"title": "Bug/image hash", "number": 16, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/16", "body": "add ecr image tag\nadd debug print\nadd region\nfix image tag\n\n"}
{"title": "Helm chart", "number": 17, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/17", "body": "\n\nadd eros ui to umbrella chart\nbump eros-chart\n\n"}
{"title": "Helm chart", "number": 18, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/18", "body": "add eros ui to umbrella chart\nfix values file\n\n"}
{"comment": {"body": "eros-dev\n\nmonitoring\n\ntag: master", "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/18/_/diff#comment-262367050"}}
{"title": "add ui image hash", "number": 19, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/19", "body": ""}
{"title": "Micro services deploy", "number": 2, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/2", "body": "\n\n\nauto deploy charts\nadd dev/prod different deployments\nset api server image tag to dev (static)\nset api image tag to repo env var\n\n"}
{"title": "classifier typing config", "number": 20, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/20", "body": ""}
{"title": "Update chart version", "number": 21, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/21", "body": "update charts to 0.1.3\nsupport configmap update pod restart\n\n"}
{"title": "Feature/docker tag", "number": 22, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/22", "body": "modify docker tag\n\n"}
{"title": "Hotfix/remove eros ui", "number": 23, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/23", "body": "bump version to 0.1.1\nupdate chart\nupdate 0.5.0\nbug fixes\n\n"}
{"title": "update version", "number": 24, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/24", "body": ""}
{"title": "dynamodb helm support", "number": 25, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/25", "body": ""}
{"title": "bitbucket-pipelines.yml edited online with Bitbucket", "number": 26, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/26", "body": "bitbucket-pipelines.yml edited online with Bitbucket"}
{"title": "Refactor eros chart", "number": 27, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/27", "body": "refactor eros-chart\nadd sparrow to custom stack\nupdate eros-chart\nfix issues\n\n"}
{"title": "add monitoring connector for error log", "number": 28, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/28", "body": ""}
{"title": "Feature/error log bi", "number": 29, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/29", "body": "add monitoring connector for error log\nbump chart version = 0.14.0\n\n"}
{"title": "point api to tenant pg", "number": 3, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/3", "body": ""}
{"title": "add kafka hook values", "number": 30, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/30", "body": ""}
{"title": "added scoring to helm set", "number": 31, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/31", "body": ""}
{"title": "Kafka topics", "number": 32, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/32", "body": "add reqiure topics\nfix syntax\n\n"}
{"title": "update chart to 0.17.0", "number": 33, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/33", "body": ""}
{"title": "add redis password env var", "number": 4, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/4", "body": ""}
{"title": "change deployment order", "number": 5, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/5", "body": ""}
{"title": "delete old serviceMonitor", "number": 6, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/6", "body": ""}
{"title": "fix delete old serviceMonitor", "number": 7, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/7", "body": ""}
{"title": "fix delete old serviceMonitor", "number": 8, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/8", "body": ""}
{"title": "Micro services deploy", "number": 9, "htmlUrl": "https://bitbucket.org/levl/eros-cicd/pull-requests/9", "body": "fix fail chart deployment\nfix fail chart deployment\n\n"}
{"title": "LDI-1482 Add performancs testing pipeline", "number": 530, "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/530", "body": "LDI-1482- add pipeline with variable input\n\n"}
{"comment": {"body": "@{712020:c10cdc62-de4d-4eb4-9a5c-586bd482bfba} We need this file to be plain for local work dont template it, use sed or something else to change these values\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/530/_/diff#comment-404728736"}}
{"title": "LDI-550 Ridge hostname typing", "number": 531, "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531", "body": "LDI-550_Ridge_hostname_typing\nLDI-550_Ridge_hostname_typing\nLDI-550_Ridge_hostname_typing\n\n"}
{"comment": {"body": "The device is OnePlus and it\u2019s not in the result option.  \nWhat data are we missing?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392058"}}
{"comment": {"body": "The device is OnePlus 7 and we are returning 6 or 10.  \nI don\u2019t think this is the expected result", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392077"}}
{"comment": {"body": "Is the new result a differnet device?  \nis it the correct one?  \nthe model resolution didn\u2019t change", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392095"}}
{"comment": {"body": "Is the new result a differnet device?  \nis it the correct one?  \nthe model resolution didn\u2019t change", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392100"}}
{"comment": {"body": "Is the new result a differnet device?  \nis it the correct one?  \nthe model resolution didn\u2019t change", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392104"}}
{"comment": {"body": "Is the new result a differnet device?  \nis it the correct one?  \nthe model resolution didn\u2019t change", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392111"}}
{"comment": {"body": "Is the new result a differnet device?  \nis it the correct one?  \nthe model resolution didn\u2019t change", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392122"}}
{"comment": {"body": "why there was a downgrade for the resolution?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392152"}}
{"comment": {"body": "why there was a downgrade for the resolution?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392157"}}
{"comment": {"body": "why there was a downgrade for the resolution?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392164"}}
{"comment": {"body": "why there was a downgrade for the resolution?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392189"}}
{"comment": {"body": "why there was a downgrade for the resolution?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392198"}}
{"comment": {"body": "why there was a downgrade for the resolution?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392211"}}
{"comment": {"body": "why there was a downgrade for the resolution?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404392221"}}
{"comment": {"body": "We are adding l2 signature today.   \nShould fix this.", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404393722"}}
{"comment": {"body": "These are two different phone models  \nThe correct one is `Samsung Galaxy A22 5G`\n\n@{63e8b1312661cde223366498} the device hostname is `Galaxy-A22-5G`, do you have any idea why it didn\u2019t return the `Samsung Galaxy A22 5G`?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404400938"}}
{"comment": {"body": "@{63e8b1312661cde223366498} Does it mean currently that the hostname typing is actually not working on the OnePlus devices? \\(I think we talked about this the other day\\)", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404409187"}}
{"comment": {"body": "The l2 signature will fix the oneplus nord test.  \nAs we discussed, the hostname will return list of devices,   \nand we need to apply the Cujo-Levl naming conversion on the prior logic data.  \nIt should fix the other oneplus issues.", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404409898"}}
{"comment": {"body": "![](https://bitbucket.org/repo/LXEBxqB/images/1532579771-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404410451"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} The naming convention for oneplus devices is not translated correctly from cujo to levl. This is a problem for both prior logic and ml hostname typing. For each to fail on it\u2019s own is not so bad, but here they fail together \\(due to same data issue\\) and cause errors.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404410604"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} how do we fix this issue?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404411060"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} fix the translation  \nthere is a dictionary Sonya created and Nika updated", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404411116"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} I wasn\u2019t clear. @{63e8b1312661cde223366498} are you working on fixing the translation?", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404411208"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} I am working with @{621df03094f7e20069fd6ab2} on fixing the translation.   \n", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404413641"}}
{"comment": {"body": "It looks like it\u2019s an artifact of the prior logic \\(Probably OnePlus 6 and OnePlus 10 are the most popular OnePlus devices\\).  \nOnce we adjust it, we should fallback to return just a OnePlus mobile devices.", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404413758"}}
{"comment": {"body": "Part of a long scenario:\u00a0\n\nIn previous connection that machine got resolution=6 so once the hostname changed to 21s it still was recognized as 21.\u00a0", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404413887"}}
{"comment": {"body": "The hostname model returned list of 2 devices.  \n", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404413939"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} Galaxy A22 5G is not appears to be in Cujo\u2019s DB.\n\nOnly A22 - less then 1% of train DB", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/531/_/diff#comment-404414085"}}
{"title": "LDI-1310 - bump eros-api package", "number": 532, "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/532", "body": "update eros-api dependencies to use new platformType (was enum  now string)\nclassifier pull request - {: data-inline-card='' }"}
{"comment": {"body": "\u200c\n\nrelated Pull requests -   \neros-configurationa-api [https://bitbucket.org/levl/eros-configuration-api/pull-requests/167](https://bitbucket.org/levl/eros-configuration-api/pull-requests/167){: data-inline-card='' }   \neros-classifier \u200b[eros-classifier: #1400 LDI-1310 get all available platform nameOPEN](https://bitbucket.org/levl/eros-classifier/pull-requests/1400/ldi-1310-get-all-available-platform-name)", "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/532/_/diff#comment-404706781"}}
{"title": "LDI-1463", "number": 533, "htmlUrl": "https://bitbucket.org/levl/eros-automation/pull-requests/533", "body": "Tests updates following {: data-inline-card='' }"}
