{"title": "Feature/jenkins integration", "number": 1, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/1", "body": "Added docker & Jenkins"}
{"title": "Store packets dataframe in DB", "number": 10, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/10", "body": "Classification progress will be in next PR"}
{"title": "Multiclassify", "number": 100, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/100", "body": "Support classification on multiple files, server-side\n\n\nUpdated Physec client to support multiclassification\n\n\n"}
{"title": "Feature/FIN-667 local continous tests", "number": 11, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/11", "body": "Add gcloud docker\nRun local integration tests (and BQ replay)"}
{"comment": {"body": "~~Why not just put it in~~ `gcloud/requirements.txt`~~? Dockerfile\u2019s should contain as little steps as possible, because every step creates a layer in the Docker image and makes it larger~~\n\n\u200c\n\nEDIT: NVM got it", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/11/_/diff#comment-94664466"}}
{"comment": {"body": "Who these credentials belong to? Your user? A special user created for this purpose via IAM \\(that\u2019s the way it should be\\)?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/11/_/diff#comment-94664743"}}
{"comment": {"body": "there's a requirements.txt for the cloud functions \\(in the repo root - must reside there\\) and additional requirements.txt for the local testing. I wouldn't join them and I don't see another way \\(I dont like `cat requirements.txt > requirements.txt`\\)", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/11/_/diff#comment-********"}}
{"comment": {"body": "Please add .gitignore that ignores `gcloud/requirements_additional.txt` ", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/11/_/diff#comment-********"}}
{"comment": {"body": "It\u2019s fine I don\u2019t like `cat requirements.txt >> requirements.txt` too", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/11/_/diff#comment-********"}}
{"comment": {"body": "Compute Engine default service account. It\u2019s special enough", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/11/_/diff#comment-********"}}
{"comment": {"body": "Glad to see some system tests! good work\n\na few comments:\n\n* training and classification should be separate tests\n* The test should work with test cloud env and not our production cloud env \\(test bucket, test table in bigquery\\)\n\n", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/11/_/diff#comment-********"}}
{"title": "Feature/FIN-685 physec parallel featex", "number": 12, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/12", "body": "For now this feature works but its impractical due to Cloud Functions limitations so it has been added but disabled. It needs to be improved\n\nFeature extraction delegation support\nDefault to 0.0.0 version if version is not available\nDon't assert GOOGLE_APPLICATION_CREDENTIALS if already running inside cloud function"}
{"title": "Transmit with changing power levels", "number": 13, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/13", "body": ""}
{"comment": {"body": "How often is it changing the tx power?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/13/_/diff#comment-94922833"}}
{"comment": {"body": "I actually worked really hard on making the service change the power level every 15 seconds but it behaved very weird. Then I realized it's because MainActivity itself already starts and stops the service automatically every 15 seconds, so I took advantage of that to just pass the service a different power level each time it's restarted", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/13/_/diff#comment-94957626"}}
{"title": "Added optional IMEI parameter to iq capture script", "number": 14, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/14", "body": "For tracking our internal recordings using the tools we provide to Physec"}
{"title": "Feature/FIN-680 physec convert pickles to jsons before upload", "number": 15, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/15", "body": "Added script to convert recorded .pickles to .json\nServer will now accept JSON instead of .pickle\nTests will now download .json instead of .pickle"}
{"title": "Model encryption", "number": 16, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/16", "body": ""}
{"title": "Added a fingerprinting CLI", "number": 17, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/17", "body": ""}
{"comment": {"body": "Please approve", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/17/_/diff#comment-95318487"}}
{"comment": {"body": "dump request ID as well for easier logging?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/17/_/diff#comment-95323729"}}
{"comment": {"body": "The request ID is already part of the packed model, isn\u2019t it?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/17/_/diff#comment-95323988"}}
{"comment": {"body": "Yes, but they don\u2019t know it", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/17/_/diff#comment-95324286"}}
{"comment": {"body": "I\u2019ll just add it to the file name along with the timestamp and the version ", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/17/_/diff#comment-95324895"}}
{"comment": {"body": "Nice and compact `model_20190318-105950_v0.4.1_f9c0d712-85e0-4cab-8150-693916d73638.bin`", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/17/_/diff#comment-95326132"}}
{"comment": {"body": "Beautiful", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/17/_/diff#comment-95337492"}}
{"title": "Feature/periodic server check", "number": 18, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/18", "body": "\n\nAdd periodic regression tests on production server at 6AM each day\nReplace multiprocessing with joblib\nAdd BigQuery querying class to help with extracting info from BQ\nAppend request ID to stored model (for easy backtracking when classifying)\nAdd testing API key to download files from testing bucket\nFixes and refactors"}
{"comment": {"body": "Maybe add something that completely simulates Physec with their API key? Not full regression, just a simple test to make sure nothing regarding their path has broken \\(Their storage server, their API key, etc\\)", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/18/_/diff#comment-95283460"}}
{"comment": {"body": "I\u2019ll add tomorrow recordings from the newest FW into the physec\\_levl storage and then add such shorter regression tests.", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/18/_/diff#comment-95284083"}}
{"title": "Feature/igal physec", "number": 19, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/19", "body": "log fw_ver to bigquery\nAdd cloud version as a column in bigquery"}
{"title": "Added pickler", "number": 2, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/2", "body": ""}
{"title": "Feature/stricter regression tests", "number": 20, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/20", "body": "Add custom logging function option in local cloud \nRun regression tests in local cloud function and compare feature extraction result\nRun faster without streaming prints\nFix worker batch allocation"}
{"comment": {"body": "approve pls", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/20/_/diff#comment-95415409"}}
{"title": "Feature/non serverless bringup", "number": 21, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/21", "body": "gcloud docker build simpler, removed parallelization\nAdded flask server\nUpdate URL to new server\nAdded HTTPS support"}
{"comment": {"body": "Do we have a testing environment/domain?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/21/_/diff#comment-95658471"}}
{"comment": {"body": "No :disappointed:   \nI\u2019ll just serve it on the same domain with a different port so we don\u2019t have to deal with HTTPS again", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/21/_/diff#comment-95670207"}}
{"comment": {"body": "so much meaning in one URL", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/21/_/diff#comment-95679682"}}
{"title": "Fix record_id when endpoint=None", "number": 22, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/22", "body": "Testing is with endpoint=None"}
{"title": "fix bigquery", "number": 23, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/23", "body": ""}
{"comment": {"body": "It\u2019ll be more concise if you handle this inside `rec_system_packet_parse` since `rec_system_packet_parse` is already doing string manipulations inside", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/23/_/diff#comment-95704885"}}
{"comment": {"body": "Will fix in next commit", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/23/_/diff#comment-95718461"}}
{"title": "Add fields to bigquery", "number": 24, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/24", "body": ""}
{"title": "Feature/non serverless tagging", "number": 25, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/25", "body": "run_server.sh versioning"}
{"title": "Feature/speedup", "number": 26, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/26", "body": "\n\nFix rotate\nDon't process data\nDisable rising transient\nPreamble search in smaller area\nMore graceful error if requests fail\nNo HTTPS needed on testing server (it's accessed via an SSH tunnel anyway)"}
{"title": "Add model to bigquery", "number": 27, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/27", "body": ""}
{"title": "Add classification results to bigquery", "number": 28, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/28", "body": ""}
{"comment": {"body": "Direct edit to levl, don\u2019t forget to create a pull request in levl too", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/28/_/diff#comment-95891882"}}
{"comment": {"body": "Right, I created a PR in levl repository\u2026", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/28/_/diff#comment-95893653"}}
{"title": "Feature/FIN-699 physec bigquery shouldnt delay response", "number": 29, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/29", "body": "Will log only after response\nlog_request_internal\nLocal cloud function will not send response until it is logged in BigQuery"}
{"title": "Feature/replay functionality", "number": 3, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/3", "body": "Fix dumping requests and responses to bigquery\nAdd mocking of server response and run cloud logic locally\nAdd replay of bigquery logs\nHandle main errors more gracefully"}
{"title": "fix classification bug", "number": 30, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/30", "body": ""}
{"title": "fix writing classification results to BQ", "number": 31, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/31", "body": ""}
{"title": "fix uploading model to BQ", "number": 32, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/32", "body": ""}
{"title": "Feature/dataflow feature extraction", "number": 33, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/33", "body": "Update Levl packge pathings (only whats needed for feature extraction)\nConvert whats needed to Python 3.5\nFix extraction  types and errors\nAdd levl_cloud package with dataflow API to upload to BQ DB\nAdd dataflow.py for DB uploads\n\nLet me know if you think some stuff can be simplified"}
{"comment": {"body": "`fingerprinting.py` has `def loadall_pickle_bytes(pickle_bytes):` that does the same thing", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/33/_/diff#comment-96134052"}}
{"comment": {"body": "Most of this pull request should be in the levl repository", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/33/_/diff#comment-96134071"}}
{"comment": {"body": "If you could prepare a diagram of what\u2019s exactly going on here - what\u2019s running the code, where do the packets come from, how they are separated, where they are processed, where do the results go, etc, etc it would be helpful, because right now I can\u2019t review this without putting a lot of effort into understanding all that from the code", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/33/_/diff#comment-96134223"}}
{"comment": {"body": "You\u2019re right; but putting this into Levl will be some effort I\u2019ll put once I get insights on this PR.\n\nThis work is not quite relevant to physec, but it was more effort to start over in Levl repo.", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/33/_/diff#comment-96134286"}}
{"comment": {"body": "There\u2019s not much flow change, expect that recordings are called with the dataflow\\_job.py and not with db\\_schema.py/bigquery\\_utils.py.\n\nMost of the work is fixing DB-related values and propagating parameters.\n\nThe dataflow example is replacing \\(non-existent\\) code for uploading recordings: loadall \u2192 `dialog_rec_system_handle` \u2192 df.to\\_json\\(orient=\u201drecords\u201d\\) \u2192  `upload_json`", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/33/_/diff#comment-96134372"}}
{"comment": {"body": "I\u2019ll delete the one in `fingerprinting.py`", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/33/_/diff#comment-96134376"}}
{"title": "fix uploading regression tests to bigquery", "number": 34, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/34", "body": ""}
{"title": "Feature/FIN-700 run bosch regression on physec", "number": 35, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/35", "body": "Regression\nFixed leaky pool (will pull request in Levl)"}
{"comment": {"body": "`import dill as pickle`", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/35/_/diff#comment-96133347"}}
{"comment": {"body": "`import dill as json`", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/35/_/diff#comment-96133365"}}
{"comment": {"body": "what does `convp `mean?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/35/_/diff#comment-96133374"}}
{"comment": {"body": "This script is just a quick hack to scatter the existing regression pickles into separate pickles for each IMEI. It might very well be not be useful in the future so I didn\u2019t put much effort into it. I just committed it because deleting it seemed stupid", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/35/_/diff#comment-96133398"}}
{"comment": {"body": "Looking good!", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/35/_/diff#comment-96133407"}}
{"comment": {"body": "regression test is best test", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/35/_/diff#comment-96248670"}}
{"title": "Fix import issue", "number": 36, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/36", "body": ""}
{"title": "Print request ID", "number": 37, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/37", "body": ""}
{"comment": {"body": "Please keep `Classifcation` for [backwards compatibility](https://xkcd.com/1172/)", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/37/_/diff#comment-96389418"}}
{"comment": {"body": "[Always forward, never back](https://pbs.twimg.com/media/C9MqCAyWsAEg2WA.jpg)", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/37/_/diff#comment-96390151"}}
{"title": "Squashed 'levl/' changes from c25a212d..17a90013", "number": 38, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/38", "body": ""}
{"title": "More logs", "number": 39, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/39", "body": ""}
{"title": "Fix classification call", "number": 4, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/4", "body": "Remove some stubs Add more descriptions"}
{"title": "iqjson *.pickle support", "number": 40, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/40", "body": ""}
{"title": "Added receiver ID to iqcapt", "number": 41, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/41", "body": ""}
{"title": "Feature/better errors", "number": 42, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/42", "body": "Better file not found in bucket error\nBetter invalid model version error\nPatch updates to the version numbers will not break backwards compatibility"}
{"title": "Add classify res for new features to BQ", "number": 43, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/43", "body": ""}
{"title": "fix tests", "number": 44, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/44", "body": ""}
{"title": "dont upload the pca model to fix bigquery upload", "number": 45, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/45", "body": ""}
{"title": "fix premable sometimes float and sometimes int when uploading to bigquery", "number": 46, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/46", "body": ""}
{"title": "Feature/igal physec", "number": 47, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/47", "body": "add model file name and fix inst_freq report\nmake tests shorter\nfix bigquery when receiver id is empty"}
{"comment": {"body": "What\u2019s the reason for removing the replay test?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/47/_/diff#comment-97043239"}}
{"comment": {"body": "It is a temporary fix - I wanted to ask you to fix it properly when you have time\u2026\n\nCan you?\n\n\u200c\n\nThanks", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/47/_/diff#comment-97044004"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/47/_/diff#comment-97046363"}}
{"title": "Feature/optimize upload", "number": 48, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/48", "body": "Fix bigquery upload/download using some new modules in levl repo (to be pushed there)\n\nDownload large BigQuery queries using GCS (save query to temp table  download table to temp file in GCS  download file from GCS  load json)\nAdd GCS file downloader class in db directory\n\n\n\nReplay works"}
{"title": "Feature/pull changes from levl", "number": 49, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/49", "body": "Changes from LEVL repo"}
{"title": "Feature/deploy command", "number": 5, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/5", "body": "Deploy current tag with google SDK\ntag = version (should be semver i.e 0.2.7)"}
{"title": "make phone always transmit in tx_power 3", "number": 50, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/50", "body": ""}
{"title": "correction that makes sure that enough data packets are processed", "number": 51, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/51", "body": "in training, so classification is only performed on agc and channels with enough data"}
{"comment": {"body": "Amir, Do the same pull request in LEVL repository", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/51/_/diff#comment-97594310"}}
{"title": "Added online inst_freq plotting", "number": 52, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/52", "body": "main_old.py is not a file I created, i've just renamed it, no need to review.\nThe file i've created is main.py."}
{"comment": {"body": "Is this used?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/52/_/diff#comment-97602464"}}
{"comment": {"body": "don\u2019t we have AGCs outside these values?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/52/_/diff#comment-97602736"}}
{"comment": {"body": "Nope, the firmware filters them out.", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/52/_/diff#comment-97603640"}}
{"title": "Feature/fix annoying master fail", "number": 53, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/53", "body": "Master would still fail (due to WIP regression tests configurations), but now due to regression tests and not infrastructure"}
{"title": "Feature/fix packet parse", "number": 54, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/54", "body": "For some reason more packets that expected failed due to multiple \\xb1\\x11 patterns (we extract the imei after we find this pattern). In this fix we iterate over all the occurrences of this pattern and try to parse the imei.\n\n\ntry to parse imei for every \\xbe\\x11 in the packet\nraise exception if couldnt find \\xbe\\x11 followed by correct imei"}
{"comment": {"body": "Please pull request in the Levl repository and not in this repository", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/54/_/diff#comment-98707279"}}
{"title": "Feature/better regression physec", "number": 55, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/55", "body": "Regression tests are now per board"}
{"comment": {"body": "This is probably not on purpose, right?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/55/_/diff#comment-100916837"}}
{"title": "slope model update with cfo consideration", "number": 56, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/56", "body": "Disregards calssify events where CFO is too far from cfo average at training\nChanged parameters in combined model, to call from slope model"}
{"comment": {"body": "is the name change intentional? merge seems informative.", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/56/_/diff#comment-100963788"}}
{"title": "Feature/BIS-3412 support physec research", "number": 57, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/57", "body": "fix upload to bigquery\ndisable regression because it gives too much false positives"}
{"title": "Squashed 'levl/' changes from 6b2e52e4..8c1aaf16", "number": 58, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/58", "body": "8c1aaf16 Merged in merge_amir_slope_fix (pull request #60) 8abc3969 Amirs fix for slope - return answer only for matching cfos f272491a Amirs fix for slope - return answer only for matching cfos\ngit-subtree-dir: levl git-subtree-split: 8c1aaf16978e6ba1b2e46f7096a95a10a7ae774e"}
{"title": "fix bigquery", "number": 59, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/59", "body": ""}
{"title": "fix return value of classification func", "number": 6, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/6", "body": ""}
{"title": "print feature result for each feature", "number": 60, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/60", "body": ""}
{"title": "Add some info to investigate slope", "number": 61, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/61", "body": ""}
{"title": "Add some info to investigate slope", "number": 62, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/62", "body": ""}
{"title": "Feature/minimum packet count", "number": 63, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/63", "body": "Minimum packets count 5000 for training 200 for classification\nSlope now deterministic"}
{"comment": {"body": "no kidding", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/63/_/diff#comment-101194454"}}
{"comment": {"body": "we should make it a system configuration and changeable via parameter. not critical for this PR", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/63/_/diff#comment-101194777"}}
{"comment": {"body": "Do we have server-side verification for this?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/63/_/diff#comment-101195989"}}
{"title": "fix logging", "number": 64, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/64", "body": ""}
{"title": "X fail reg3", "number": 65, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/65", "body": ""}
{"title": "fix physec logging to bigquery", "number": 66, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/66", "body": ""}
{"title": "Feature/BIS-3412 support physec research", "number": 67, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/67", "body": "Changes from LEVL: Timing + H features"}
{"title": "fix bigquery logs", "number": 68, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/68", "body": ""}
{"comment": {"body": "Can you explain why you did what you did?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/68/_/diff#comment-102059091"}}
{"comment": {"body": "It\u2019s complicated removing fields from bigquery or changing their type\u2026\n\nAdd another field with the correct type is the easiest solution", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/68/_/diff#comment-102069020"}}
{"title": "fix model api", "number": 69, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/69", "body": ""}
{"title": "Feature/FIN-678 IF Beacons", "number": 7, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/7", "body": "Beacons now contain special string that makes sure there are at-least 3 00111110's in the pre-scramble bits no matter what adv channel"}
{"title": "Feature/BIS-3412 support physec research", "number": 70, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/70", "body": "Fix timing model\nAdd classification debug info to Bigquery logging"}
{"comment": {"body": "Leftovers", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/70/_/diff#comment-102545823"}}
{"comment": {"body": "I inserted it on purpose, because I need it every time to debug\u2026 I guess others will use it as well", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/70/_/diff#comment-102545848"}}
{"title": "Feature/igal physec", "number": 71, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/71", "body": "Add timing"}
{"comment": {"body": "Is the PR adding or removing the timing model?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/71/_/diff#comment-105220850"}}
{"comment": {"body": ":slight_smile: Sorry, the PR is about removing timing", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/71/_/diff#comment-105231555"}}
{"title": "Physec support for adv intents", "number": 72, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/72", "body": ""}
{"title": "Made renewing physec certificate easier", "number": 73, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/73", "body": ""}
{"title": "Add usage example for app_phone_api", "number": 74, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/74", "body": ""}
{"title": "Feature/BIS-4563 million packets support", "number": 75, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/75", "body": "increase inactive timeout to support big packets file\nincrease feature extraction parallelism to support big packets file\nadded filter for more than 100k packets"}
{"comment": {"body": "Great!\n\nCan you also add a rejection of huge files \\(64GB\\+\\) by returning an error from the server?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/75/_/diff#comment-109809340"}}
{"comment": {"body": "OK, I will add it in next PR ", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/75/_/diff#comment-109811035"}}
{"title": "Feature/bucket sync cache", "number": 76, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/76", "body": "Added bucket sync background task to Physec server\nBucket cache now mounted in containers\nPython script will use cache"}
{"title": "Added more physec testing servers", "number": 77, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/77", "body": ""}
{"title": "Feature/BIS-4628 fix physec timing", "number": 78, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/78", "body": "enable timing\nAdd flags to bigquery"}
{"comment": {"body": "Right. Makes sense", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/78/_/diff#comment-110056910"}}
{"title": "Feature/grisha hamellech 2", "number": 79, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/79", "body": "Squashed 'levl/' changes from 831c3640..60f7b07b\n60f7b07b edited upload file 132edb34 Merge branch 'master' of  85c95541 added new phones, fixed column names to fir schema - will later format again c468d857 edited dataflow file e0c90cc1 edited dataflow file c3f80702 upload edit b1478a9f upload edit 1ffbcb44 edited dataflow file a8e2f8a3 edited dataflow file ca196b81 upload edit a1466502 upload edit 1b37150f Merged in feature/db_versioning (pull request #85) 8614ccc5 Merged in feature/option_for_power_reset_with_external_relay (pull request #86) 5c43682c Port should have persistant lifespan b73c03d1 Added note of limitation in help 0818d7cb Add reset via arduino script update dialog-capture to have a board controller which monitors the board for activity, kicks the power if packets don't arrive and resends the filters d955b47c Removed unused import 7f22e337 Renamed version columns to lower snake case dccdb588 Fixed versioning to support Python3.5 c86bf1cc Version columns 0edcb493 Added option to take another part of the packet for the packet alignment 5cf91662 Version info passed as parameter to db_scehma functions 4bbe0131 Moved versioning to dataflow.py 702e7a5d Added versioning to db_schema.py 69098f82 Removed redundant documentation f94266b9 Removed TODO f9397f55 Bad / f18267e6 Origin must contain dd1a4757 BadOrigin 9c39db63 added new phones e6d1fa78 Fixed unpushed tag error 73851c7a Fixed unpushed tag error 4c0873af Fixed split 3307eafa Bad print 7a21fd41 Fixed print d404e427 Fixed bug a5cb0082 Will now check if tag is pushed 4ab5fb89 Fixed bug c6d30b1d Dirty now also means unpushed 15199676 Added documentation and git-describe field 04c926fe Make it strict again fc34713c Fixed dirty/unpushed 130ce72a Added OS 8ae121a5 Reporting versions for important libraries eea665fe Reformatting 82ea9c7d Remove newline from end of Git output 36758e8c Added glob 105f5159 Implemented commit_is_pushed_to_bitbucket dbe11bf9 Will show changes 59b56da1 Added initial versioning library 8c0c4f74 Merge branch 'master' of  7b1752cf added new phones 15e79f90 Merged in FIN-813-db_record_pickle_dump (pull request #83) 0c75c35a Merge branch 'master' of  84872980 added new phone addc4ecc moved line 09b9deb9 deleted comment aa5311d1 finished dumping to json 5ecb88cd changed ble backup table source to the new table 8288f623 changed format to json d959dde6 dumped json to storage 93b80cfb dumped json to storage a7857ec1 Merged in FIN-808-change_pipeline_for_new_table (pull request #82) 78eac6ab Merge branch 'master' of  d86bb5b8 fixed grisha's comments fcb7110a deleted notebook 3bd000a3 fixed null transmitter in movement 08edc428 fixed null record_name f041a9f3 fixed field names df049fa3 fixed field names ce895b89 fixed field names 090fc082 fixed field names daf6bab3 fixed field names 2025ae58 fixed field names 11e755f5 fixed table names 6b4fdcc9 fixed scheme 2b7af478 fixed testing table name d2661dfe fixed table names and flattened nested fields cf2323c5 Merged in BIS-4553-fix-demoded_data-to-support-bad (pull request #81) 73b41d75 fix capture-packets script to support cases of non-bosch data 6a2bee97 Merge branch 'master' of  f9a8538f Merged in FIN-771-failed_packets_fix (pull request #77) a6759162 Merge remote-tracking branch 'origin/master' into FIN-771-failed_packets_fix 8b6155d4 resolved conflict a3c6b399 Merge branch 'master' of  8f6ea098 added new phones d240a687 Merged in BIS-4415-levl-enable-timing-and-adjust-to-2-b (pull request #79) 0ead44b8 add beacon_stream_id to packet 93ad3e08 united generated record id to a single variable 1c3bd538 Merge branch 'master' of  66e9f9a2 added new phones 85189227 Merged in feature/fix_statmodels_import (pull request #80) bb96dd4d Use consistent path to OLS 1ae03a59 Merged in feature/BIS-4292_matrix_inversion_stuff (pull request #78) edcaa786 compute timediff per imei and beacon_stream_id 2996c21c Added nxp iq capture template project 2911779d Scale channel numbers 05e4e31d Set proper hardware_timediff_from_prev_packet in pickles so this info can be uploaded to db 92e92261 fix filter to iq_recording_agent; add beacon_stream_id to DB ae2a5a55 resolved conflicts 75b49c35 resolved conflicts 5005e630 resolved conflicts bb8db07a resolved conflicts 6906cdca deleted line at the end of imeis 5ab12d5e deleted notebooks 9fdf3c41 deleted notebooks e63f5481 removed faulty log timestamp 6a3ad5be notebook ff7df568 Merge branch 'master' of  d327c9b7 removed faulty log timestamp 434fa0ad notebooks ffa92e72 Merged in feature/evaluate_progress_indication (pull request #76) 3bb4834d Modified Jenkins launch scripts to be more friendly b082d60d Will now determine iPhone packets according to the packet len instead of a flag passed to the function to enable combined iPhone and Android recording pickles. 980cba19 Merge branch 'ble_data_table_backup' 900aacfa fixed pipeline ba8a8887 fixed pipeline c1a00c3d fixed pipeline 0e8daaa4 fixed pipeline d01acb55 fixed pipeline b17a150e fixed pipeline 1cade268 fixed pipeline 28e4cd79 fixed pipeline aa8a79e0 fixed pipeline 6b1dff41 fixed pipeline 264f4a3b Plot progress indication b4924465 fixed pipeline 7f50a4d0 fixed pipeline 6b3e109f fixed pipeline 6b219125 fixed double code c82bb922 fixed pipeline 386ecc3c fixed pipeline e55d45d8 fixed pipeline 58a570c0 added new phones bc55f4f6 notebook ea4e7570 Cleanup + dialog tof demo working out of the box 3a6cdd0e added new phones f74ca354 notebook 0d8765fd added new phones and a timestamp for fail logs 74435931 added new phones eeb09f47 added new phones 593dc0f0 Small fix for dialog script to prevent it from getting unfiltered packets on startup 9559f4c3 fixed moto6play name 5f280177 Merged in feature/iphone_recordings_parse (pull request #75) 004e122e fix conflict b932cfa3 add iphone_packets flag to dataflow default call bc09047f added thermal_state column f546a200 Merge branch 'master' of  700cf3c6 added new phones 4e4b133e add thermal state, override existing not used for iphone field 165e9ef8 add exception for base64 decode c48c07d2 add iphone parsing f6badc90 remove blank line cda339ba minor fix 5b06b8a2 added new phones 4821386b added new phones 287ebafb Merged in fix_record_id (pull request #73) 90e4ece0 trying to resolve conflict a76965ba trying to resolve conflict a96fe4c4 Merged in BIS-3920_hardware_timestamp_type_fix (pull request #74) c698a938 Merged in feature/update_bt_dongle_recording (pull request #72) dfaa6faf removed check print 30b699d4 changed hardware_timestamp type from int to int64 a724a47b fix bug 7f3c0340 update main script of bt_dongle d2327528 added new galaxy S7s 7cb6f485 Update BT dongles 680957ec fix line 462db6a0 Added option for shelter filtering afdf46e6 fix record id 3de8a30b added new devices c6a8e28b added new nexus 6p phone ce11337f Merged in danielle (pull request #71) bb4b9c07 Updated jenkins startup script 1460b47e Added dockerfile for the jenkins master 13bac526 added table backup script and google API key fb19626c deleted backup script from folder def6b695 changed upload file d43a7c05 Added packet start index field b069d315 New phone 7c7cd4f2 Update number of workers 8a9e6a47 New P10 phone dbf65d77 Updat devices dict 42edb09b Add missing phones 218abc68 Merged in feature/h_feature (pull request #64) a838024d Renamed file a0c181e0 Add H param to CombinedModels 43da23a0 Merge branch 'master' into feature/h_feature 17105196 Multiprocessing f48647c3 Merge branch 'master' into feature/h_feature 125e0711 Update model testing b728b631 H model investigation 8e0e87f2 H feature notebook\ngit-subtree-dir: levl git-subtree-split: 60f7b07b6e4204769c004f441b371a43dd223dff\n\n\nSquashed 'levl/' changes from 60f7b07b6..70f3a5629\n70f3a5629 Merged in falling_transient_update_extract (pull request #88) a7f7b5639 Merged in feature/fix_bugs (pull request #89) 83fb53040 Propagate field changes into fingerprinting models 28ffc56c1 update falling transient length to be relative to end of data 6f5dc15b9 added model to devices file\ngit-subtree-dir: levl git-subtree-split: 70f3a5629b4d8705ed96bb4e804d0b7d0ffc3cc7\n\n\nFix tables differences between physec logs and ble recordings \n\nEnable H param to physec models"}
{"title": "Refuse to perform classification on invalid version", "number": 8, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/8", "body": ""}
{"comment": {"body": "Open debate: since we\u2019re doing semantic versioning and we follow the major.minor.build, we could have some backward compatibility with versions not so different from current version. That means we reject different major \\(or maybe even minor\\) version differences and not all version changes.\n\nSounds reasonable?", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/8/_/diff#comment-94602971"}}
{"comment": {"body": "It was brought up before and we decided to strictly don't have any backwards compatibility for simplicity concerns.\n\n\u200c\n\nI'm not generally against it, I just think that if we go through with actual semver we need to thoroughly review the changes between tags before deciding whether it's backwards compatible or not - unless it's extremely obvious that the changes are shallow and don't affect the fingerprinting performance and behavior in any way - we will update the major version parts.", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/8/_/diff#comment-94603694"}}
{"comment": {"body": "It\u2019s true; I was just thinking about possible small backward-compatible API fixes that wouldn\u2019t affect the general behavior. Not critical.", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/8/_/diff#comment-94604163"}}
{"comment": {"body": "I merge it for now, if we want later we can make it less strict", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/8/_/diff#comment-94612596"}}
{"title": "Local matrix run", "number": 80, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/80", "body": ""}
{"title": "Feature/rising transient", "number": 81, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/81", "body": "Squashed 'levl/' changes from 70f3a5629..e17de641c\ne17de641c Merge branch 'master' of  c20203c85 fixed column names\ngit-subtree-dir: levl git-subtree-split: e17de641ca4f2f02754e3ba7f6211a5580c4a1c3\n\n\nAdd rising transient to model\n\nFix new columns"}
{"comment": {"body": "What is this", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/81/_/diff#comment-110056829"}}
{"comment": {"body": "This should have performance implications, I hope they\u2019re not too bad", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/81/_/diff#comment-110056834"}}
{"comment": {"body": "Stuff that came from levl master", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/81/_/diff#comment-110056847"}}
{"comment": {"body": "We just have to accept our fates", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/81/_/diff#comment-110056858"}}
{"title": "Inst freq final", "number": 82, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/82", "body": ""}
{"comment": {"body": "Bested code ever!", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/82/_/diff#comment-110057718"}}
{"title": "Refactor levl_logger.py per_feature_classification_res to add new features", "number": 83, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/83", "body": ""}
{"comment": {"body": "`classify_res['combined_result'] = int(classification_result == 'match')` \n\nSee if you can make this shorter", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/83/_/diff#comment-110085313"}}
{"comment": {"body": "```\nint(classification_result == 'match')\nvs\n1 if classification_result == 'match' else 0\n```\n\nI like the second version more", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/83/_/diff#comment-110089777"}}
{"title": "Will now use the new bucket", "number": 84, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/84", "body": ""}
{"title": "Feature/inst freq outlier bug", "number": 85, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/85", "body": "Fixed inst freq outlier issue\nImprove physec20 scripts"}
{"title": "Error reporting to bigquery", "number": 86, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/86", "body": "make logging for errors also"}
{"title": "Print physec results by feature", "number": 87, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/87", "body": ""}
{"title": "update timing model pvalue threshold", "number": 88, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/88", "body": ""}
{"title": "Feature/force known board cfo slope", "number": 89, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/89", "body": "Add custom regression for boards 2770, 2740, 0074.AB, 0063.AB\n*"}
{"comment": {"body": "The list here includes both \u201cslope\u201d and \u201cinst\\_freq\u201d - this does not look correct\u2026", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/89/_/diff#comment-111835195"}}
{"comment": {"body": "it\u2019s commented out anyway", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/89/_/diff#comment-111835295"}}
{"title": "Only accept valid API keys", "number": 9, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/9", "body": ""}
{"comment": {"body": "local\\_cloud\\_function.py is also using the api\\_key. Change there as well", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/9/_/diff#comment-94523792"}}
{"comment": {"body": "Done \\(a6fb8b24e283ce2c1a100f9e99d078956bcde994\\)", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/9/_/diff#comment-94524755"}}
{"comment": {"body": "Tests are failing :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/9/_/diff#comment-94533592"}}
{"title": "Inst. freq compensation", "number": 90, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/90", "body": "Remove CFO compensation\nChange p-value combination method to stouffer"}
{"title": "Restored script back to its working state", "number": 91, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/91", "body": ""}
{"comment": {"body": "I think that this file is obsolete. We\u2019ve moved to physec20\\_train\\_classify\\_multi.py", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/91/_/diff#comment-110675091"}}
{"comment": {"body": "physec20\\_train\\_classify\\_multi does something else", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/91/_/diff#comment-110958042"}}
{"title": "Inst freq training_diff_pwr_mean per bucket", "number": 92, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/92", "body": ""}
{"title": "Inst freq for new data", "number": 93, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/93", "body": ""}
{"title": "Inst freq compensation", "number": 94, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/94", "body": ""}
{"title": "fix bigquery logging", "number": 95, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/95", "body": ""}
{"title": "Made Physec backend significantly faster", "number": 96, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/96", "body": "tl;dr - Added redis cache to Physec, BigQuery log now actually non-blocking, less frequent rsync \nAll these improvements make the backend much faster.\n\nLogs were done in a threaing.Thread instead of a multiprocessing.Process, that caused Gunicorn to only respond after the thread died. That means Physec actually had to wait for us to finish logging to BigQuery before they got a response\n\n\n\nrsync locked the bucket cache and it ran every 10 seconds for almost 10 seconds That means that most requests had to wait for rsync to do basically nothing for up to 10 seconds (because most of the time there are no new files in the bucket). Now rsync will run only every 5 minutes. This has the disadvantage that Physec has to wait up to 5 minutes before they can run a new uploaded file. They dont upload single files too often so its not a big deal. They usually upload in batches.\n\n\n\nAdded a Redis server that will hold pickles of feature dataframes. This way trainings take a very short time if the same file is trained twice."}
{"title": "Use model hash as GCM nonce instead of os.urandom for deterministic model encryption", "number": 97, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/97", "body": ""}
{"comment": {"body": "It seems that there are still non-deterministic elements to the model, so this change will only be relevant once we fix those.", "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/97/_/diff#comment-113870528"}}
{"title": "Fixed renew cert", "number": 98, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/98", "body": ""}
{"title": "Add log of each scenario input and results", "number": 99, "htmlUrl": "https://bitbucket.org/levl/physec_server/pull-requests/99", "body": ""}
{"title": "skeleton for the LEVL repository", "number": 1, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/1", "body": ""}
{"comment": {"body": "Awesome work!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/1/_/diff#comment-72869781"}}
{"comment": {"body": "1. bin/ - what should it hold except for plotting tools?\n2.  bin/ - Would be great if we decide which plotting tools present results in a consistent way \\(e.g. show exactly the same bar plots for classification results\\).\n3. bin/ - same as above, for exploratory phase, so we can communicate preliminary results easier.\n4.  python/ - didn\u2019t we agree that there is python code associated with \\(e.g.\\) dialog and other that is for research and they should not be stored together?\n5. python/research\\_infra/ - wouldn\u2019t this hold code for plotting that\u2019s also in bin/?\n\n", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/1/_/diff#comment-72986390"}}
{"comment": {"body": "You deleted plotter.py, loader.py and demodulator.py but kept packet\\_plot.py and packet\\_tools.py, which depends on them.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/1/_/diff#comment-72990774"}}
{"comment": {"body": "Seems that you\u2019re right. plotter.py and loader.py are very similar to the function we already have in funcs.py and planned to move. I\u2019ll restore them but we need to organize the code there.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/1/_/diff#comment-72991203"}}
{"title": "Feature/BIS-298 recording system on multiple hosts", "number": 10, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/10", "body": "some refactoring to main oven script starting to add pyro4-based communication with remote PC\nsome rule enforcement\nremove mistakes from previous commit\nfixed communcation with slave over Pyro4 add deserializators\nalso ignoring 2nd lab PC's internal BT\nfixed indentation\nfixed blacklisting\nfix typos\n\nfix regression with phones recording\nadd option to work without slaves\n\n\ncleaner interface commenting prints\n\nminor changes: lowered advertising interval and reduced waiting time"}
{"comment": {"body": "Cool use of the Pyro4 lib!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/10/_/diff#comment-74731656"}}
{"title": "FIN-859 sine recording scheme", "number": 100, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/100", "body": "Added sine scheme to pipeline using Grishas sine parser.\nNow theres a sine recording flag that can be used to upload sine recordings to the new scheme using existing scripts."}
{"comment": {"body": "what about a distinctive job name for sine recording instead of `feat-ext`?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/100/_/diff#comment-114724121"}}
{"comment": {"body": "Done :\\)", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/100/_/diff#comment-114757726"}}
{"comment": {"body": "Please review/approve :\\)", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/100/_/diff#comment-115198515"}}
{"comment": {"body": "Still pending my verification of sine feature extraction\u2026", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/100/_/diff#comment-115198524"}}
{"comment": {"body": "Is it still pending?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/100/_/diff#comment-119619722"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/100/_/diff#comment-119619896"}}
{"comment": {"body": "Woohoo!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/100/_/diff#comment-124564332"}}
{"title": "fix bug in processed packet in coarse cfo", "number": 101, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/101", "body": ""}
{"title": "move dc removal to outside function", "number": 102, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/102", "body": ""}
{"comment": {"body": "Don\u2019t forget to put a tag and update [https://jira.levltech.com:8090/display/IRND/BLE\\+Recording\\+Database\\+Versioning](https://jira.levltech.com:8090/display/IRND/BLE+Recording+Database+Versioning)", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/102/_/diff#comment-114942720"}}
{"comment": {"body": "How do I put a tag?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/102/_/diff#comment-115214760"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/102/_/diff#comment-115221579"}}
{"title": "WiFi X CFO X EM X Bayes", "number": 103, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/103", "body": "Implements an algorithm for learning CFO model in the presence of WiFi activity. The issues this code solves is that when WiFi is active we may observe two different CFO clusters. This code clusters CFO data and performs regression on each cluster while sharing regression slopes but allowing a difference in regression bias. Updates to the model are done in a Bayesian fashion."}
{"title": "WiFi X CFO", "number": 104, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/104", "body": "Implements an algorithm for learning CFO model in the presence of WiFi activity. The issues this code solves is that when WiFi is active we may observe two different CFO clusters. This code clusters CFO data and performs regression on each cluster while sharing regression slopes but allowing a difference in regression bias. Updates to the model are done in a Bayesian fashion."}
{"comment": {"body": "Beautiful code!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/104/_/diff#comment-115346754"}}
{"comment": {"body": "Much OOP!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/104/_/diff#comment-115346765"}}
{"comment": {"body": "Very hack!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/104/_/diff#comment-115346789"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} \u05d0\u05d4\u05d1\u05ea???", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/104/_/diff#comment-115348915"}}
{"title": "update scope recording scripts", "number": 105, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/105", "body": ""}
{"title": "plot number of packets per phone during recording", "number": 106, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/106", "body": "The graph shows how many packets have been received from each phone.\nThe user can run the script with a new argument that specifies ones in how many packets per phone they would like to see the graph."}
{"comment": {"body": "Why False if plot\\_count is None? shouldnt it be 0?\n\nSecond thought - if this number is not going to DB than what you wrote is OK.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/106/_/diff#comment-116241324"}}
{"comment": {"body": "I dont undertsand this If:\n\nIf the value is None than you put there None again?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/106/_/diff#comment-116241753"}}
{"comment": {"body": "is this option mutually exclusive with plot option? Can they work together?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/106/_/diff#comment-116241898"}}
{"comment": {"body": "It won\u2019t be for the DB, just for visualization for the user.  \nIt\u2019s False if the user didn\u2019t use the flag so that they\u2019d be no plot.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/106/_/diff#comment-116242162"}}
{"comment": {"body": "A string representation so that the graph can use it as a label \\(it won\u2019t use None values\\), it\u2019s mostly for the case of iPhones not sending their IMEI.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/106/_/diff#comment-116242579"}}
{"comment": {"body": "They work fine together :\\)", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/106/_/diff#comment-116242858"}}
{"title": "Wifi scope records updates", "number": 107, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/107", "body": "I added a sample_rate field for the script:\n-sample-rate=10G  ---> sample rate will be... 10G. Otherwise - 200M :slight_smile:"}
{"title": "wifi scope correction", "number": 108, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/108", "body": ""}
{"title": "FIN-868 metadata app", "number": 109, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/109", "body": "Added wifi packet generator app.\n\n"}
{"title": "FIN-390 scope record phone jitter", "number": 11, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11", "body": "Added python packet_tools\nAdded scope controller and ToF jitter WIP code.\nupdated scope class\nupdate scope class\nupdated class scope\nAdded code for tof measurement using scope. Not completely working yet.\nAdded support for toggling the state using intents for the TOFSlaveApp."}
{"comment": {"body": "this is running on PC, right? can this be pythonized instead?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74813623"}}
{"comment": {"body": "Cool! \n\nDo you plan to fix pep conventions? Mostly many capital letters in variable names.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74813743"}}
{"comment": {"body": "Yes, it is planned to be, I need to sit with Nuriel and convert its code to python.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74813891"}}
{"comment": {"body": "Yes, its out of scope \\(pun not intended\\) for this branch, the code was only relocated and fixed to run on python3.\n\nWe should arrange a refactor / replacement of the research infrastructure code.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74814099"}}
{"comment": {"body": "After another thought I think it\u2019s better to have a different class for intent and then you dont have to make the app \u2018singleTop\u2019 - that way it will not call the whole OnCreate method for every intent\u2026 not a must to fix now thought\u2026", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74846750"}}
{"comment": {"body": "These kind of math functions probably can be a helper class?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74847398"}}
{"comment": {"body": "Very cool recording app! I am sure we will use it a lot!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74847629"}}
{"comment": {"body": "This actually was a bug, this line should be in the Activity tag.  \nOnly noticed that after i\u2019ve started using the automatic recording system.  \nAnyway i fixed it on another branch.\n\nAbout the comment, i dont think its calling the whole onCreate func, i think it just calls onNewIntent instead.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74858783"}}
{"comment": {"body": "@nurielr  These functions should maybe lay in the research infra code, don\u2019t thay?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74859086"}}
{"comment": {"body": "I think it calls the OnCreate, can you put a break point to make sure?\n\nAnyway if it does not call the OnCreate you should remove the handleIntent code from there\u2026", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74863062"}}
{"comment": {"body": "It\u2019s there because sometimes the app is not open, so sending the intent to it causes it to open for the first time, and then i do want to handle the intent.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/11/_/diff#comment-74864250"}}
{"title": "Pipeline DC and downsampling changes", "number": 110, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/110", "body": "\n\nFix DC offset calculation area\nAdded support for downsampling in Dialog pipeline\nAdded flag to enable LPF \n\n"}
{"comment": {"body": "I am adding a couple of changes", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/110/_/diff#comment-120876888"}}
{"title": "Merge CFO WiFi Model into fingerprint", "number": 111, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/111", "body": "As title states.\n\nThis *does not* include the polynomial fit for board temperature model, nor does it contain handling the case where WiFi is off (cfo_model.py was left untouched). These will be added progressively.\nThe EM stuff happen in em.py. In oop.py there are classes to handle the prior, outliers and regression parameters.\nCurrently the prior assumes board temperature is given in 1/100 degrees Celsius, we can change this later when the board temperature polynomial fit is added to the model. There is a short testing scheme in cfo_wifi_model.py that generates synthetic data, we may or may not want to eliminate it.\nIf you think it is better to dump all code in one file this can easily be done.\n\n\nPLEASE IGNORE EVERYTHING IN WORKSPACE!!!"}
{"comment": {"body": "* We shouldn\u2019t have duplications of em.py and oop.py.\n* It\u2019s fine to split it into multiple files. But maybe add some prefix to those name? or put them inside another directory?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/111/_/diff#comment-121621796"}}
{"comment": {"body": "Lets put them in a deeper directory", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/111/_/diff#comment-121664128"}}
{"comment": {"body": "* But it\u2019s in my workspace. I can remove them but it shouldn\u2019t bother anyone.\n* Done.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/111/_/diff#comment-121682516"}}
{"comment": {"body": "It is a good habit to add a test with real IQ recordings for each new feature\u2026\n\nNot necessarily in this PR though\u2026\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/111/_/diff#comment-122564417"}}
{"comment": {"body": "I beg to differ. If I use real data I would need to supply it, either as a pickle file or as a query. The former means cluttering our repo with data files. The latter requires me to make sure they querying our DB for the data and saving it will work on every machine \\(Windows/Mac/Linux/Jenkins/whatever\\) and add the querying scripts/functions to the pull request, making it bigger, harder to understand and slower to approve.\n\nFake data, on the other hand is easily generated on the fly, does not need to be saved and its generation process is relatively easily understood. Any algorithm that makes it to fingerprint is assumed \u201cgood\u201d and tested on plenty of real data. Fake data only helps us make sure there are no bugs in the code and that the it runs smoothly.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/111/_/diff#comment-122567846"}}
{"title": "Relay detection models", "number": 112, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/112", "body": "add a packet balancing check mechanism\nadd an interchannel timing model\nupdate relay detection models\n\n"}
{"comment": {"body": "please add Mich and Igal as reviewers", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/112/_/diff#comment-121699478"}}
{"comment": {"body": "I've already added mich before. I'll add Igal now", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/112/_/diff#comment-121700917"}}
{"comment": {"body": "this section belongs in a workspace folder", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/112/_/diff#comment-121862266"}}
{"comment": {"body": "remove from pull request and put in different branch. this will be added in a different PR after we verify this works and as valuable to the system.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/112/_/diff#comment-121887899"}}
{"comment": {"body": "remove from pull request and put in different branch. this will be added in a different PR after we verify this works and is valuable to the system.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/112/_/diff#comment-121887995"}}
{"title": "add comment to scope controller and remove comments from lf_capture", "number": 113, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/113", "body": ""}
{"title": "Add finer alignment after fine CFO", "number": 114, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/114", "body": ""}
{"title": "FIN-871 add agc count to dialog capture", "number": 115, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/115", "body": "Added AGC count to dialog-capture monitor graph (total for recording, not per phone)\n\n"}
{"title": "Upgrade dataflow script", "number": 116, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/116", "body": "Changed DataFlow script to run from CMD rather than editing the file each time\nGUI for automatic DataFlow uploads\n\nAfter changes it is possible to:\n\nUpload all files from a certain bucket at once\nSpecify a board id for all the recordings that are being uploaded if they werent named by the new convention: Each record name ends with _[board id]\nControl all upload settings\nPaste a list of files to upload at once\n\n\n\nIn order to use the GUI, each user would have to locally change their repo path\n\nAlso added configuration to recording script to auto-generate name without missing details\n\n"}
{"title": "BIS-7247 rssi graphs", "number": 117, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/117", "body": "Add live plotting of RSSI levels of all packets (connection, advertisement) over time of recording.\nWorks with or without connection setup."}
{"comment": {"body": "See if you can reduce code duplication here. `adv_rssi_3*` is repeating", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/117/_/diff#comment-129289141"}}
{"comment": {"body": "Fixed", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/117/_/diff#comment-129712195"}}
{"title": "Added RSSI histogram plotting to dialog-capture.py", "number": 118, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/118", "body": ""}
{"comment": {"body": "What\u2019s the difference between this and [PR 117](https://bitbucket.org/levl/levl/pull-requests/117/bis-7247-rssi-graphs/diff)?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/118/_/diff#comment-125739456"}}
{"comment": {"body": "You\u2019re right, I\u2019ve missed that. I\u2019ll talk with Danielle to see what needs to be done.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/118/_/diff#comment-125741934"}}
{"comment": {"body": "They plot different things AFAICT. Her code plots RSSI at the different advertisement channels.  \nMine plots connection RSSI as received by both the board and the phone.\n\nI\u2019ll clarify a bit and re-open this.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/118/_/diff#comment-125854639"}}
{"title": "Feature/dialog capture rssi output", "number": 119, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/119", "body": "Added RSSI histogram plotting to dialog-capture.py\nAdded offset to the connection RSSI\nSeperated connection_rssi_plot from packet_plot\ndialog-capture: added markers for connection-rssi relay detection\n\n"}
{"comment": {"body": "@{5b41d9de10d57114135eca66} That\u2019s unrelated to Danielle\u2019s PR.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/119/_/diff#comment-126060775"}}
{"comment": {"body": "What\u2019s wrong with these prints?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/119/_/diff#comment-126068730"}}
{"title": "InstFreq", "number": 12, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/12", "body": "Added scope controller and ToF jitter WIP code.\nupdated scope class\nupdate scope class\nupdated class scope\nupdated scope class for Dima and Guy's timing recording and added pass band sampling to reduce computation time\nupdate inst freq"}
{"comment": {"body": "This file/class is too big - you should probably split it\u2026", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/12/_/diff#comment-74854619"}}
{"comment": {"body": "There are a lot of conflicts due to some of the changes already in master.  \nmainly in Scope\\_controller that gay merged an older version of it and in SPC.  \nYou should checkout master in to your branch to resolve it and then do a fixed version", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/12/_/diff#comment-75146726"}}
{"comment": {"body": "this folder name is super long.   \nthink about when trying to import from it.  \nI would consider a shorter name - maybe just modulation\\_index? or freq offset?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/12/_/diff#comment-75147494"}}
{"comment": {"body": "why not or?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/12/_/diff#comment-75186878"}}
{"comment": {"body": "can you explain the difference in a comment?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/12/_/diff#comment-75187183"}}
{"title": "Froze some google-cloud requirements", "number": 120, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/120", "body": ""}
{"title": "Feature/dialog capture rssi output", "number": 121, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/121", "body": "Added RSSI histogram plotting to dialog-capture.py\nAdded offset to the connection RSSI\nSeperated connection_rssi_plot from packet_plot\ndialog-capture: added markers for connection-rssi relay detection\nAdded relay detection histogram\nparameter changes to crssi\ndialog-capture: plotting RSSI delta\ndialog-capture: Fixed rssi delta offset\ndialog-capture: Added a training step for the connection rssi\nAdded average distance from trained value\nLowered training length in crssi\nFixed display bug\nAdded seperate bounds for Majority/Averages\nUncommented a print\nAdded parsing of connection RSSI in adv-packets\n\n"}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Sorry for closing and opening this PR so many times. It\u2019s more mature now and changes less of the surrounding code.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/121/_/diff#comment-126309729"}}
{"title": "Updated all requirements to be the same as on Danielle's laptop", "number": 122, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/122", "body": ""}
{"title": "Generalized relay detection models", "number": 123, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/123", "body": "Add the sigma noise model and update the combined model accordingly"}
{"title": "Sigma noise model", "number": 124, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/124", "body": "Add the sigma noise model and update the combined model accordingly"}
{"title": "fix dialog capture after rssi fixes", "number": 125, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/125", "body": ""}
{"title": "FIN-885 fob scheme", "number": 126, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/126", "body": "New forb keyfob DB scheme:\n\npacket parsing\ndataflow pipeline\nupload automation\n\nThis branch includes some changed done in upgrade_dataflow_script PR that is still waiting approval. \n"}
{"title": "Fixed demoded-data parsing to support ios", "number": 127, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/127", "body": ""}
{"title": "Dongle fix", "number": 128, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/128", "body": "Update dongle to new advertisement format\nFix import\npacket update\nAdded start stop and device selection to dongle advertising script\nFixed dongles\n\n"}
{"title": "BIS-8125 unified pipeline", "number": 129, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/129", "body": "Until now we had a flag for sine recordings in the ble pipeline and a duplicated pipeline for ford keyfobs. Things that changed:\n\nsine parse was extracted from ble parse and now every table has its own db_schema.py\na single generic pipeline streams data to all tables, using a new Table class to configure the table and packet processing\nevery scheme has its own tab in uploader.py \nupload utils module was created for generic functions that all db schemes use\n\n"}
{"title": "Features models", "number": 13, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13", "body": "Added features models and all features combined model python code.\nIm not very happy with the combined model design,  so suggestions (that preferably dont involve complete redesign) are very welcome.\nFeel free to come to my office and discuss the code if you have questions/suggestions."}
{"comment": {"body": "test\\_packets function appears in many places - sometimes the input is \u2018test\\_packets\\_df\u2019 and sometimes just array of values - can it have the same prototype in all places? maybe also derive from same abstract class?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-74991456"}}
{"comment": {"body": "Overall it looks very nice - if I understand correctly `CombinedModel` is the only class I should use from outside when working with the model library? ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-74992297"}}
{"comment": {"body": "That's a good point. I didn\u2019t want to pass test\\_packet\\_df to the normal models in order to keep those simple and maintain the possibility of using it with arrays instead of making data frames.\n\nBut it\u2019s really complicate things in combined model. Let\u2019s discuss it when you have time.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-74992453"}}
{"comment": {"body": "Shouldn\u2019t clean\\_ourliers also be a parameter so you can pass to all the models?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-74993484"}}
{"comment": {"body": "Yes, unless you want to test a specific feature instead the whole system.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-75008236"}}
{"comment": {"body": "As discussed, currently we call the clean outliers in the train method of every feature. \n\nMaybe will pass in the future if we decide to change the behavior.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-75008945"}}
{"comment": {"body": "BTW, if we use heating app, we need to add \u201csent\\_to\\_cloud\u201d.  \nThink if it\u2019s better to use it here, or in the scripts using this", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-75152437"}}
{"comment": {"body": "Why not add a \u201ccfo\\_var\u201d column to the df and making sure it\u2019s a float?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-75152606"}}
{"comment": {"body": "We can save var instead of std in the db. Anyway, \\+0.001 to avoid division by zero, python 3 is ok with 1/int", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-75153910"}}
{"comment": {"body": "what do you think about changing the module name to general\\_feature?\n\n\\(just for simplicity\\)", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-75186467"}}
{"comment": {"body": "Not a big fan, I guess it may be confused with general feature implementation. ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-75707367"}}
{"comment": {"body": "Will probably add it here. Let\u2019s discuss again when we\u2019ll use it.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/13/_/diff#comment-75709037"}}
{"title": "Sfo horizontal model", "number": 130, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/130", "body": "Integrated sfo horizontal model"}
{"title": "Feature/refactor auto recordings system", "number": 14, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/14", "body": "Started refactoring\nRefactored all automatic recordings code, made it more welcoming for changes, focusing on the possibility to insert more apps that would like to perform automatic recordings. Added layers of abstractions above bluetooth devices to eliminate the bond between phones and other bluetooth devices (such as dongles).\nFixed bugs in automatic recording system after refactor.\nReadded support for the ble dongles after the refactor, not tested yet.\nAdded support for TOF slave app automatic recordings.\nMinor bug fix for TOFSlaveApp\nFixed device description object type on recording.\nNow oven will run only when using the levl-lab computer to prevent interruptions of the recordings in the lab.\nWhen oven is not present, only one temperature will be used.\nFixed bugs when using BT dongles"}
{"comment": {"body": "No need to leave comment", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/14/_/diff#comment-76096171"}}
{"comment": {"body": "Great PR, but still has some conflicts\u2026", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/14/_/diff#comment-76096197"}}
{"comment": {"body": "Broken support with multiple PCs - not urgent.\n\nToF not tested.\n\nPlease approve anyway", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/14/_/diff#comment-78997796"}}
{"title": "Optimize cfo model running time", "number": 15, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/15", "body": "The new cfo model was slow compared to the old one so I optimized it a bit. Not sure why some of the optimizations worked\n\npandas su***\nuse pandas nunique instead of ours\nuse pandas nunique instead of ours take 2\nstatsmodels.api isnt great either\nminor"}
{"title": "Feature/BIS-242 multi dialog recording", "number": 16, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/16", "body": "updated bosch_capture_iq to SDK 1.0.14.1081 and same settings as bosch_integration commit d598ae1cf3663a783af58e20ce7a803672a85749\nupdating version of FW to 0.2.0\nmodifying hook for RF_DIAL_Handler since setup wouldn't let the project compile if it's undefined\nadding .gitignore adding workspace for bosch advertisements on dialog\nWIP adding functions\nadding advertising main that compiles\ngot it advertising\nadding periodic temperature reading\nfixed communcation with slave over Pyro4 add deserializators\n\nRevert \"fixed communcation with slave over Pyro4\"\nThis reverts commit 1d5ab3f9ee30d39f7df5a8f3f831905457738b83.\n\n\nupdated regular packet recording software with updated FW packet format\n\nmissing to previous commit\nadv data changes with temperature\nfixing adv project settings\nfixed bosch adv project settings (compiler version and sleep behavior) adding custom UART protocol on both embedded and python\nadded commands to hadle advertise start/stop over UART\nadded responses to uart messages\nadded better error handling for uart messages added dynamic channel advertising\nminor refactoring\nmoving advertiser to common place\nadded automatic discovery of dialog USB DK as listener added application to launch advertising and listen to it added arguments to application\nminor refactoring updated stack size in dialog adv project due to stack overflow error handling\nfixing arguments manual listeners\nstating to add animated plot of realtime data - not working ATM\ngeneralizing broadcasters\nadded dialog broadcaster class using new broadcasters abstraction\nmissing parameter to previous commit\nusing alternative method for reading lines from serial since people say that it's with higher throughput\nbug fix in serial reading\ndialog advertisement: remove assert\ntuning project parameters to make things work faster and more reliably\nadding scenario parameter refactoring data saving\nAdded plot for rssi and temperature\nfixed merge\nlowered data passed through queues added some UART error handling\noptimizing workload by working with the advertiser only when needed\nfixed bug in optimizing serial usage\nadded plot for several boards\nserial port: splitting dialog worker to 2 workers: rx and tx\nputting back replacement of pyserial's readline for lower cpu usage putting serial port access in different processes\nfix file save overwrite improve starting of serial port threads remove redundant pause function\nincrease buffer sizes\ngoing back to serial multithreading and not multiprocessing\nadding processing task to offload calculation and plot management\nrefactored plotter allowing more features displaying most recent data continously\nreplacing plot lines with marker to display no connectivity\nsynchronized packet plotting\nsynchronizing display time between all packets\nadding legend\nfix grid, minor change\nsplitting to multiple plots adding plot settings and refactoring"}
{"comment": {"body": "Main changes:\n\n* Updated Dialog embedded SDK\n* Added dialog advertiser project \\(ble\\_bosch\\_adv\\)\n* added python multi-dialog project under python/recording/multi\\_dialog\\_recording\n\n", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/16/_/diff#comment-75854662"}}
{"comment": {"body": "This class can be removed", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/16/_/diff#comment-75863870"}}
{"comment": {"body": "Need to send all new packets to the plot", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/16/_/diff#comment-75868112"}}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/16/_/diff#comment-75869554"}}
{"comment": {"body": "Nice and compact :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/16/_/diff#comment-75869746"}}
{"comment": {"body": "will be done in next PR", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/16/_/diff#comment-75897518"}}
{"comment": {"body": "will be done in next PR", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/16/_/diff#comment-75897538"}}
{"title": "Models bootstrap", "number": 17, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/17", "body": "Code to enumerate on models train and tests\n\nbootstrap build models is ready\ndont sort hardware timestamp in timing feature, assume the data is sorted by time\nadd feature getter\nminor\nbug fix in signal processing utils\nmore bugfix and convention fixes in utils\nbootstrap_test_model is done\nadded train_test\ncomments for train_test\ntiming model better handling of empty hardware timestamp\nadded wrapper function for bootstrap\nadded mse\nadded res to matrix"}
{"comment": {"body": "IINW, you currently need to import the default  FeatureStruct to use it.\n\nDo you think we can change it to be more simple?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/17/_/diff#comment-75893393"}}
{"comment": {"body": "Added the option to pass either None - all features will be used, or list of selected features - those features will be used with default params or list of FeatureStructs if you need to change the default params of some features.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/17/_/diff#comment-76099704"}}
{"title": "Feature/BIS-242 multi dialog recording 2", "number": 18, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/18", "body": "starting to add uart task to offload UART printing\nchanging recording project to use triple buffers when printing to UART\nrefactoring and cleaning up iq capture embedded project\nbug fix\nfixed issues from previous PR\nmake CLI arguments more consistent\nminor\nminor refactoring at plotting module add CFO plot fix missing auto-detection stage\nexpanding PC->dialog protocol to control advertising interval in ms\nminor"}
{"title": "Hotfix/phones name mapping", "number": 19, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/19", "body": "Added phone names mapping"}
{"comment": {"body": "@levlkobi has all imeis file in the bigquery infra. maybe we should combine those.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/19/_/diff#comment-76096390"}}
{"title": "Feature/BIS-237 updating recording setup", "number": 2, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/2", "body": "adding snapshot of repositoryBIS-237 bosch_capture_iq commit 0b933da3adff1f1c24e0f45fcc6883e7ec8746a9\ncopying io_infra python files from production repo\nadding oven API converting code to python 3 and fixing paths adding oven recording flow (missing is DB access and timing feature)\nrefactoring added error handling when ADB device is offline"}
{"comment": {"body": "Good job!\n\nDo you plan to fix documentations and python conventions?\n\nSome of the python code in dialog\\_capture should be in other directories \\(demod\\_fsk for example\\).  Do you plan to fix it?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/2/_/diff#comment-73043238"}}
{"comment": {"body": "Yes, I plan to do that once we push all relevant code", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/2/_/diff#comment-73163138"}}
{"comment": {"body": "cool!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/2/_/diff#comment-73165148"}}
{"title": "Feature/bigquery", "number": 20, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20", "body": "Bigquery utils and support for dialog recordings at the moment\nin the next versions:\n\nmore doc strings\nScope and blade support\nautomation recording integration"}
{"comment": {"body": "What does the \u201cproblem with regular demod\\_packet\u201d comment mean? If there is a access\\_position key in a packet\\_dict we have a problem with the demodulation?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77166514"}}
{"comment": {"body": "packet\\_start calculated on the not aligned packet, could it be a problem? \n\nMaybe packet\\_dict\\[\u201caligned\\_packet\u201d\\]\\[packet\\_start:packet\\_end\\] isn\u2019t aligned? If so, did you consider running demod\\_func on aligned\\_packet insted of pp\\_packet in board\\_pipeline?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77168096"}}
{"comment": {"body": "if i use regular demod packet :  packet\\_dict\\[\u201caccess\\_position\u201d\\] is a list and not an int.  \nsync\\_demod\\_packet returns an int\n\nif i have no access\\_position then I had a problem with the demodulation", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77169318"}}
{"comment": {"body": "It looks very good that we have this utility\u2026\n\nDo we plan that people outside of your team will use it? If so Maybe there should be an API file with usage explanation\u2026", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77187871"}}
{"comment": {"body": "please give an option to load directly from memory", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77213706"}}
{"comment": {"body": "Done.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77218216"}}
{"comment": {"body": "much obliged", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77220492"}}
{"comment": {"body": "Could you add comments that explain the file. I think we decided to use Docstring ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77221398"}}
{"comment": {"body": "comments as well", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77221644"}}
{"comment": {"body": "we should add comments. Docstring conventions?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77221714"}}
{"comment": {"body": "maybe we should add comments to each function ?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77222073"}}
{"comment": {"body": "Do we have 4 Mate10?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77224131"}}
{"comment": {"body": "lets look at it tomorrow", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77224737"}}
{"comment": {"body": "aren\u2019t these estimated in the pipeline before? ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77225530"}}
{"comment": {"body": "regarding the comment, if we keep the align position the same for the batches of recordings, then I think we don\u2019t really need it.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77226920"}}
{"comment": {"body": "There are 2, Dual-sim", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77286442"}}
{"comment": {"body": "they are in the dialog one, but necessarily in all pipelines ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77286491"}}
{"comment": {"body": "I plan on doing it.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77288176"}}
{"comment": {"body": "This are the JSONs for bigquery connection. ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/20/_/diff#comment-77288304"}}
{"title": "Feature/BIS-365 Multi master dialog recording system", "number": 21, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/21", "body": "minor comments\nadd dialog advertiser dialog advertiser is added via CLI params\nbug fix in preprocess pipeline\nlimit of calculations\nfix serial RX thread not closing\nstarting work on jlink serial  com port mapping\nadd pairing between segger serial number and comport in windows\nsome refactoring to system in system_access to allow piped calls add linux side of dialog dk lookup\nintegrated serial number instead of port number into oven flow minor bugfixes\nminor refactoring to quiet discovery to close advertisers on exit\nsome refactoring and updates to linux lookup\nfix bug in thread management (thanks to yair)\nreset dialog advertiser when there's error in the advertisement start"}
{"comment": {"body": "The exception handling is great!\n\nShould we print something to know that we had an exception?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/21/_/diff#comment-77541619"}}
{"comment": {"body": "There is nothing do to here for us right? ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/21/_/diff#comment-77541632"}}
{"comment": {"body": "I don\u2019t see how that would benefit us right now as it\u2019ll only clutter the terminal screen.\n\nThere are abundant sources for exceptions", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/21/_/diff#comment-77541673"}}
{"comment": {"body": "I think that that guy had a point - such as in changing the serial port\u2019s answer or performing writes to it. It\u2019s better to be safe and add a delay than being sorry", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/21/_/diff#comment-77541698"}}
{"title": "Hotfix/demod data", "number": 22, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/22", "body": "add scramle table\nadded iphones to list\ndemod_packet support for empty data packets from phone\ndata to bytes fix for length 0 % 8"}
{"title": "added support for preamble in data packets", "number": 23, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/23", "body": ""}
{"title": "BIS-301 extract locked agc value from dialog", "number": 24, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/24", "body": "dialog-capture.py will now reuse dialog_port.py io_infra, dialog-capture.py pickle format has now been changed!\nAdded dialog-capture.py output to .gitignore\nWill now also record locked AGC value for each packet, cmd parsing now easier to change\ndialog-capture.py rewritten with better CLI and pickle format more similar to dialog_oven_recording\ndialog-capture.py folder name will now contain IMEI and board-ID; also added an example CLI call for dialog-capture.py"}
{"comment": {"body": "what's the idea behind removing the `self` from `self.state` on a couple of occasions here?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/24/_/diff#comment-79024507"}}
{"comment": {"body": "Thanks for refactoring this as well!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/24/_/diff#comment-79024545"}}
{"comment": {"body": "please advance the `FW_VERSION_STR` ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/24/_/diff#comment-79024910"}}
{"comment": {"body": "No reason, just a merge conflict resolution error. Will fix.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/24/_/diff#comment-79275082"}}
{"title": "Hotfix/fix tof app", "number": 25, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/25", "body": "fix tof python app\nupdate tof android app\nfix dialog_dk lookup in windows\nfix tof python app"}
{"comment": {"body": "Cool!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/25/_/diff#comment-79082959"}}
{"title": "InstFreq", "number": 26, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/26", "body": "added instantaneous frequency estimation methods\nfixed scope controller\nadded esg controller\n\nignore files in workspace"}
{"comment": {"body": "I downloaded a python git repo called python-ivi, for to communication with the ESG. No need to review the code there.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/26/_/diff#comment-79732514"}}
{"comment": {"body": "I suggest to change to self-contained import so that we don\u2019t rely on project settings\n\n    import sys\r\n    sys.path.append(\"./python-ivi/\")\n    \n    import ivi", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/26/_/diff#comment-79752000"}}
{"comment": {"body": "it works without the sys.path? what does it solve?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/26/_/diff#comment-79764603"}}
{"comment": {"body": "maybe signal\\_processing utils is a better place for this func?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/26/_/diff#comment-79796989"}}
{"comment": {"body": "`AttributeError: 'Figure' object has no attribute 'plot'`\n\npython 3.6\n\nremove the second plt.plot in the parenthesis ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/26/_/diff#comment-80602472"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/26/_/diff#comment-81437585"}}
{"title": "FIN-486 Phone multiple masters", "number": 27, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/27", "body": "Added code to plot data from the TOFSlaveApp recordings.\nProgress with connection interval feature research\nContinued research.\nNow printing the pretty name of the phone\nAdded timediff plot script\nNow the phone sends more data which causes it to use the MD bit to send more packets we can later calculate ToF on.\nFixed for ToF phone app to not crash.\nAdded compatibility with Android Wear (watch) for the ToF slave app.\nAdded possibility for the ToFSlaveApp to communicate with multiple masters. And removed landscape mode from the app."}
{"title": "TOFSlaveApp can now be switched to be aggressive/non-aggressive on the fly (can also be controlled via intent by adding an \"aggressive\" boolean extra, defaults to true if extra not found)", "number": 28, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/28", "body": ""}
{"comment": {"body": "care to also add API to python\u2019s phone\\_app\\_api if it\u2019s supported?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/28/_/diff#comment-79971234"}}
{"comment": {"body": "This is out of the scope of this pull request. It\u2019s a good idea. Create a Jira issue or do it", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/28/_/diff#comment-79974230"}}
{"title": "add queries", "number": 29, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/29", "body": "Hi,\nI suggest we should write a script or two for randomized retrieval of data from bigquery for the testing of models.\nThe questions is how should we retrieve/organizedata for a model testing, when we don't have a specific recording we want to examine."}
{"comment": {"body": "ignore workspace code", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/29/_/diff#comment-79987266"}}
{"comment": {"body": "The goal is to add a wrapper to query multiple devices in a more convenient way?\n\nIt didn\u2019t work for me. need to add spaces in few places, the ones I found: after RSSI in meta\\_data\\_select, after str\\(channel\\) in query, maybe there are few more.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/29/_/diff#comment-79992329"}}
{"comment": {"body": "weird it didn\u2019t work. I will fix. The purpose of that function is what you said. However, I would like if we can think of automated queries that we can add for model testing, so that we test data over random times, temp, etc.. I think we can build a small set of queries people can use for testing new models, without thinking too much of what to query every time \\(in addition to the regular queries we use today\\)", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/29/_/diff#comment-79995963"}}
{"comment": {"body": "Kobi please approve", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/29/_/diff#comment-95042966"}}
{"title": "FIN-365 levl repo move blade code", "number": 3, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/3", "body": "Moved blade code\nCommitted changes of my workspace code"}
{"title": "Hotfix/scope", "number": 30, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/30", "body": "scope_to_db function for Amirs raw files from scope"}
{"title": "Feature/FIN-518 capture iq can filter", "number": 31, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/31", "body": "Fix printing to UART issue\ncopy uart_protocol from ble_bosch_adv project Add simple flow to filtering\nDialog returns message when filtering changed Start refactor of communication with dialog to add IMEI filter protocol\nFurther refactoring\nFix dialog-capture with new API\nFix bad usage\nbump capture IQ FW version\nStart skeleton for feature presentation\nfix paths and issues\nEnter to start and enter to stop works\nAdd packet processing flow\nminor fix for processing flow\nBetter messages Processing packets\nAdd loading training packets from pickle file using CLI param -o Prepare option for online training\nfix paths\nFixes\nCleanup\nFix typo Offload packet pipelining to another process Use new CaptureIQ API\nAdd -i CLI param to filter phone IMEI"}
{"comment": {"body": "Should the third param be `m_incoming_msg_len` - 1?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/31/_/diff#comment-81492395"}}
{"comment": {"body": "My mistake you decreased one the line before", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/31/_/diff#comment-81492648"}}
{"comment": {"body": "No, since the line before is `--m_incoming_msg_len;`", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/31/_/diff#comment-81492774"}}
{"comment": {"body": "Nice work", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/31/_/diff#comment-81515046"}}
{"title": "BIS-367 recording iphones with ibeacons", "number": 32, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/32", "body": "Disabled BLE event reporting & handling because it causes the board to freeze sometimes (faulty heap management)\nAdded a script that serves custom IMEI-dependant MyBeacon configurations in HTTP to allow easy configuration of MyBeacon on new iPhone devices that we want to record\nAdded support for recording of iBeacons on Dialog\nBump Capture IQ version to 0.2.5 Add filtering of beacons based on IMEI Update PC software for new version"}
{"comment": {"body": "\u00a9 @omer_levltech ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/32/_/diff#comment-82005813"}}
{"comment": {"body": "Best code designed ever", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/32/_/diff#comment-82005852"}}
{"comment": {"body": "@omer_levltech So MyBeacon does exactly what we did with our iPhone app? :man_facepalming: ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/32/_/diff#comment-84433286"}}
{"comment": {"body": "It allows you to manually broadcast custom \\(you can set UUID, major and minor\\) beacons, but it doesn\u2019t support automation.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/32/_/diff#comment-85024334"}}
{"comment": {"body": "\u05dc\u05d0 \u05d4\u05d1\u05e0\u05ea\u05d9, \u05d0\u05ea\u05d4 \u05e8\u05d5\u05e6\u05d4 \u05e9\u05d0\u05e1\u05ea\u05db\u05dc \u05e2\u05dc \u05d4\u05e4\u05d5\u05dc \u05e8\u05d9\u05e7\u05d5\u05d5\u05e1\u05d8 \u05d4\u05d6\u05d4?? \u05dc\u05d0 \u05e0\u05e8\u05d0\u05d4 \u05dc\u05d9 \u05e9\u05d9\u05e9 \u05dc\u05d9 \u05d4\u05e8\u05d1\u05d4 \u05de\u05d4\n\u05dc\u05ea\u05e8\u05d5\u05dd (\u05dc\u05e4\u05d7\u05d5\u05ea \u05d1\u05de\u05d1\u05d8 \u05e9\u05d8\u05d7\u05d9).", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/32/_/diff#comment-85799516"}}
{"title": "AGC is read when reading data", "number": 33, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/33", "body": ""}
{"title": "Demo/guy/bosch access rssi fix", "number": 34, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/34", "body": "Added RSSI calibration fix for reconnections.\nMinor upgrades to the tof model."}
{"title": "Fixed aggression bug.", "number": 35, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/35", "body": "Also enabled printf for floats."}
{"title": "Upgraded FingerToFSlaveApp with the new version of the original ToFSlaveApp", "number": 36, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/36", "body": ""}
{"title": "Dialog Pipeline 300%~ speedup", "number": 37, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/37", "body": "demod_fsk will only generate the stream it was asked to and not all 4\nget_freq will now request the specific bit-stream it needs from demod_fsk\nWill not use StatsModels formula parsing for OLS as it is extremely slow\ndemod_fsk simplified\ndemod_packet will use generator expression for streams to not generate streams it doesnt need\ndialog_pipeline now supports disabling of rotation if the input buffer is already rotated beforehand (a rotated buffer is much faster to process because to overcome rotation the buffer is duplicated)"}
{"comment": {"body": "where did this `min(sample_rate, 4)` come from? isn\u2019t sample rate always larger than 4?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/37/_/diff#comment-94925551"}}
{"comment": {"body": "It's just a copy of `demod_rate` ffrom the original form of the function ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/37/_/diff#comment-94954695"}}
{"comment": {"body": "IC", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/37/_/diff#comment-94957259"}}
{"comment": {"body": "Great work!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/37/_/diff#comment-94969590"}}
{"title": "All Levl diff from physec repo", "number": 38, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38", "body": "@{5b02c344cd95416ee040ad9c} @{5a49d431ef77662a7583f8f0} @{5b72a213e72afd064c8a4ebd} and @{5b41d9de10d57114135eca66} are irresponsible and they modified the copy of the Levl repository inside the Physec repository directly, instead of making the changes upstream.\nThis pull request contains all that diff"}
{"comment": {"body": "Is \u2018fw\\_ver' field in the BQ DB?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-95762628"}}
{"comment": {"body": "Hard thing to say \u201cirresponsible\u201d", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-95824640"}}
{"comment": {"body": "Right, I just added this field to the ble\\_data table - so this should be OK.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-95832863"}}
{"comment": {"body": "Never take me seriously", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-95843322"}}
{"comment": {"body": "what is the reason for this change? Did we test it on some json files we create in db\\_schema?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133633"}}
{"comment": {"body": "@igal_leveltech @gregory-levl ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133643"}}
{"comment": {"body": "What is the purpose of this change? Where do we pass preamble\\_location!=None ?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133732"}}
{"comment": {"body": "When you know in advance where the preamble is going to be you can pass `preamble_location` as a tuple of start index and end index to save the algorithm a lot of processing, in our case \\(Physec, Dialog 9x\\) it\u2019s always between 0 and 1500  \n  \nSo to answer you question: In the Levl repository it\u2019s always None.  \nIn the Physec repository we pass `(0, 1500)`", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133744"}}
{"comment": {"body": "I didn\u2019t touch it. Never thought that it\u2019s different on Levl repo.\n\nAnyway, I worked with this on physec DBs and it worked like magic.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133776"}}
{"comment": {"body": "Not sure I understand. Are we plan to change it in levl repo? if so, we should see if we can upload jsons not from data flow", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133862"}}
{"comment": {"body": "Sounds good. \n\nJust to make sure, for the algo to work we need all the access code bits \\(512\\) be inside the area we pass to get\\_preamble. Is it the case?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133906"}}
{"comment": {"body": "You mean 512 samples? It\u2019s the responsibility of the user to make sure they pass enough samples to the algorithm. In my case I made sure that the preamble length calculation stays the same whether I send only 0:1500 or the entire packet", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133944"}}
{"comment": {"body": "yes 512 samples. ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133987"}}
{"comment": {"body": "Blame @guyklevl ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96133992"}}
{"comment": {"body": "just curious,what is the reason for the \\(str\\(y\\) for y in x\\) instead of max\\(str,x\\) change? is it suppose to be faster or just style issue?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/38/_/diff#comment-96134095"}}
{"title": "slopes model files and notebooks", "number": 39, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39", "body": ""}
{"comment": {"body": "could be simplified to `from . import Model`", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39/_/diff#comment-95895122"}}
{"comment": {"body": "I don\u2019t think we should store the notebooks in git \\(definitely not notebooks names Untitled.ipynb\\).\n\nDev guys, what do you think the right way here?  ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39/_/diff#comment-96128458"}}
{"comment": {"body": "agree with dima", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39/_/diff#comment-96128756"}}
{"comment": {"body": "Did you consider writing this code more generic to work also for example with sample rate of 8? \n\nMaybe passing sample\\_rate as a param and using ranges sample\\_rate dependent?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39/_/diff#comment-96128814"}}
{"comment": {"body": "Yes, I could do that.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39/_/diff#comment-96129242"}}
{"comment": {"body": "Do you intentionally avoid the /dx ? If so, slop is probably not a good name. ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39/_/diff#comment-96129255"}}
{"comment": {"body": "I did, because the dy/dx is extremely sensitive to the length of the slope, and for slopes that are sometimes 5 and sometimes 6 samples long the performance is poor. I assume constant number of samples and so the dx is redundant.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39/_/diff#comment-96129296"}}
{"comment": {"body": "Why is it redundant? Some of the arrays in high\\_rans and low\\_rans are smaller than the default slope\\_length in slope\\_calc func \\(and you have the extreme\\_point\\_x\\) so I guess that dx may vary. \n\nAm I missing something? ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39/_/diff#comment-96130821"}}
{"comment": {"body": "It is redundant, because in each segment I aim to measure the slope along the same number of samples, for every phone. When I add the dx into consideration, I get a very large variation in some cases for values that are supposed to be very close to one another, when, for instance, the peak is about the same height as the sample next to it, so very slight noise will cause the peak to be detected in another sample. In segments that have slopes along 5 or 6 or 7 samples, this one sample variation makes a huge difference.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/39/_/diff#comment-96131441"}}
{"title": "update signal processing", "number": 4, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/4", "body": "I added signal processing tool box"}
{"comment": {"body": "you need to change the naming conventions in some of the files \\(and foldername\\).  \nModules should have short, all-lowercase names and Function names should be lowercase, with words separated by underscores", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/4/_/diff#comment-********"}}
{"comment": {"body": "Some comments in the code would help \\(me\\) a lot", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/4/_/diff#comment-********"}}
{"comment": {"body": "It seems like you\u2019re not using this in the function", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/4/_/diff#comment-********"}}
{"comment": {"body": "\u200c\n\nwhat happened to filter from bank?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/4/_/diff#comment-********"}}
{"title": "update classification return value with each feature result", "number": 40, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/40", "body": ""}
{"title": "PCA feature per agc", "number": 41, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/41", "body": "Added the pca model to production. \nNote that I also added inst_freq calculation to our pipeline because both pca and slopes needs it so its probably a good idea to do it before. Will add the column to DB if the reviewers agree.\n-\n\n\nadd minimum distance for max to min temperature device\nadded pca model\nadd inst freq extraction to compute_packet_features\nseparate the pca model per agc\nadd inst_freq to db columns\nadd inst_freq by default"}
{"comment": {"body": "The PCAModel is not called from the combined model - right?\n\nLets add it to combined model \\(not necessarily in this PR\\) with some flag to disable it so we can look at the classification results  for the feature\u2026 ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/41/_/diff#comment-96241549"}}
{"comment": {"body": "Did you test it for both sample rates? 8 & 16?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/41/_/diff#comment-96242143"}}
{"comment": {"body": "The way we differentiate to get instant frequency should depend on sample rate. E.g. for sample\\_rate == 8 use clean\\_phase\\[4:\\] - clean\\_phase\\[:-4\\] and for sample\\_rate == 16 use clean\\_phase\\[8:\\] - clean\\_phase\\[:-8\\].", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/41/_/diff#comment-96243657"}}
{"comment": {"body": "Sounds reasonable, but we ran all the tests both for 16 and 8 sample rates with 4 \\(at least the tests on last records\\). \n\nI suggest to stay with 4 since there was no good reason to use 4 \\(instead of 2 or 5\\) for instant freq with sample rate 8 and that the setting we tested.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/41/_/diff#comment-96336687"}}
{"comment": {"body": "I ran some tests, it looked ok but maybe I missed something. Something with this lines looks weird to you?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/41/_/diff#comment-96336997"}}
{"comment": {"body": "No, just made sure\u2026", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/41/_/diff#comment-96354857"}}
{"comment": {"body": "added to combined\\_model", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/41/_/diff#comment-96366026"}}
{"title": "More levl diff from physec", "number": 42, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/42", "body": ""}
{"comment": {"body": "Check out the file it\u2019s deleting", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/42/_/diff#comment-96354712"}}
{"comment": {"body": "I don\u2019t think the ipynb should be in levl, so it\u2019s ok. Don\u2019t delete the slope\\_model.py though", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/42/_/diff#comment-96354904"}}
{"comment": {"body": "ok, seems that your reverted Amir\u2019s commit somehow. can you restore the slop\\_model.py and db\\_schema.py?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/42/_/diff#comment-96355060"}}
{"comment": {"body": "My bad. fixed", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/42/_/diff#comment-96422745"}}
{"comment": {"body": "Status of this PR?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/42/_/diff#comment-96690240"}}
{"comment": {"body": "I\u2019ll merge it sometime today once I fix @dimabl \u2019s comment and he approves the pull request.   \n  \nIt doesn\u2019t change too much so you can keep working without it, shouldn\u2019t have too many conflicts", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/42/_/diff#comment-96690459"}}
{"title": "Feature/dataflow framework and recording", "number": 43, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/43", "body": "Copy directory from physec branch/dataflow\nAdjust paths/package for levl/python path only\nMore documentation"}
{"title": "Fix q always even", "number": 44, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/44", "body": ""}
{"comment": {"body": "Make sure to also change in bosch\\_integration", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/44/_/diff#comment-97077386"}}
{"comment": {"body": "Where?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/44/_/diff#comment-97078078"}}
{"comment": {"body": "python\\_tools\\\\system\\_tests\\\\board\\_instance.py, python\\_tools\\\\io\\_infra\\\\io\\_tools.py", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/44/_/diff#comment-97082998"}}
{"comment": {"body": "Most approved PR of the year. Congratulations!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/44/_/diff#comment-97101056"}}
{"comment": {"body": "Ain\u2019t code duplication wonderful?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/44/_/diff#comment-97115307"}}
{"title": "IQ crc report", "number": 45, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/45", "body": "Will optionally call a given function with a report indicating whether it seems like IQ has been modified using CRC hidden in the noise before the packet by the Physec agent"}
{"comment": {"body": "@omer_levltech is `testbus_modem` a typo?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/45/_/diff#comment-97209619"}}
{"title": "Feature/sinus recording", "number": 46, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/46", "body": "Sinus recording for 2 dialogs in oven\nWorkspace notebook"}
{"title": "Fix typo", "number": 47, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/47", "body": ""}
{"title": "Fix signness", "number": 48, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/48", "body": ""}
{"title": "Missing CRC report handler usage", "number": 49, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/49", "body": ""}
{"title": "Feature/BIS-237 updating recording setup", "number": 5, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/5", "body": "cleaning up prints error handling with disconnections/timeouts refactoring, correcting paths\nbug fix not saving recordings"}
{"title": "Changes from physec", "number": 50, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/50", "body": ""}
{"comment": {"body": "Beautiful code!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/50/_/diff#comment-97604378"}}
{"comment": {"body": ":heart_eyes: ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/50/_/diff#comment-97852238"}}
{"comment": {"body": "Are there performance issues? Polling on queues is wasteful; another option is 1 queue with AGC as a field of the passing message instead of multiple queues, 1 per AGC", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/50/_/diff#comment-97858119"}}
{"comment": {"body": "There is not queue polling.\n\nThere are some performance issues when running all channels on the same process, At first i\u2019ve separated each channel to another process, but this makes it difficult to plot them all to the same figure, so Sigal combined them to a single plot again.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/50/_/diff#comment-97905553"}}
{"comment": {"body": "@sigald \n\nAnother way to address the performance issues receiving from multiple channels is to process each channel in a different process \\(the same way i\u2019ve implemented it at first\\) and then instead of plotting it from the same process, just pass it to an other queue to send it to a process which is only responsible of plotting the feature.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/50/_/diff#comment-97906121"}}
{"comment": {"body": "```\nwhile True:\n  for agc:\n    if queue[agc].empty():\n      continue\n```\n\nis polling on the queues", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/50/_/diff#comment-97908242"}}
{"comment": {"body": "You are right,  I added plt.pause and it is responsive and works good enough\u2026\n\nIf you have another improvement you suggest talk to me\u2026", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/50/_/diff#comment-97991703"}}
{"comment": {"body": "@guyklevl I don\u2019t know if the performance issue is due to processing the packets or plotting them. I think it\u2019s the fact that when we plot, we go through each packet and then sleep a bit when we plot. This is my suspect.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/50/_/diff#comment-98059268"}}
{"title": "Fixed db processing scripts processing functions", "number": 51, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/51", "body": ""}
{"title": "Feature/couple of recording fixes", "number": 52, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/52", "body": "Turn dataflow argument to kargs\nReturn IMEI exception\nUpdate test DB\nValidate (basic) fields before inputting them into bigquery\nCatch failed feature extraction and failed DB field updates and print their indexes for further investigation (example jobs with both type of failures:  , )"}
{"title": "upated scope controller so that if an input value is None, it takes its value from what is set on the scope display", "number": 53, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/53", "body": ""}
{"comment": {"body": "please check this", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/53/_/diff#comment-98238665"}}
{"title": "add is_transmitter_moving and phone_app_version", "number": 54, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/54", "body": ""}
{"title": "fix inst freq structure size", "number": 55, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/55", "body": ""}
{"title": "Feature/BIS-3450 levl add fields to packet", "number": 56, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/56", "body": "add is_transmitter_moving and phone_app_version\nadd correct parsing of demoded_data to db_schema - replace looking for 'L'\nget correct chuck size of the demoded_data, add another validation\nallow proper parsing of demoded_data whether it's payload only or with header and CRC"}
{"comment": {"body": "Were you able to run dialog\\_rec\\_system\\_handle with the new parser?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/56/_/diff#comment-98452060"}}
{"comment": {"body": "Yes, I created recordings and used dataflow.py to parse them and upload the data into BigQuery", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/56/_/diff#comment-99172424"}}
{"title": "Fix packet parse", "number": 57, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/57", "body": "For some reason more packets than expected failed due to multiple \\xb1\\x11 patterns (we extract the imei after we find this pattern). In this fix we iterate over all the occurrences of this pattern and try to parse the imei.\n\n\niterate over \\xbe\\x11 pattern indexes\nbetter naming"}
{"comment": {"body": "Don\u2019t you think it\u2019s worth putting the changes in a separate function?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/57/_/diff#comment-98709611"}}
{"comment": {"body": "I actually wanted to add it to get\\_transmitter\\_dict but IMHO the code became less readable since we have also the transmitter\\_power which is \u201cindex\u201d dependent. \n\nSo I left it as is. ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/57/_/diff#comment-98710243"}}
{"comment": {"body": "progress?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/57/_/diff#comment-99079806"}}
{"comment": {"body": "no\u2026 waiting for approves. ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/57/_/diff#comment-99080980"}}
{"comment": {"body": "my approval is not good enough? :disappointed: ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/57/_/diff#comment-99081158"}}
{"comment": {"body": "It is for me.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/57/_/diff#comment-99081491"}}
{"title": "Preprocess with coarse CFO estimation", "number": 58, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/58", "body": "added in pipeline a preprocess function with coarse cfo estimation\nfixed in demodulator the range to return the physical layer symbols\nadded to demod fsk the option to return the soft phase differences for debugging demodulation"}
{"comment": {"body": "need to be fixed also in sync\\_demod\\_packet?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/58/_/diff#comment-99604667"}}
{"comment": {"body": "is it OK that `cfo_est = board_cfo - 1`  when coarse estimation fails?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/58/_/diff#comment-99605112"}}
{"comment": {"body": "no. sync\\_demod\\_packet retrieves the bits from this function", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/58/_/diff#comment-99612492"}}
{"comment": {"body": "changed to return -1", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/58/_/diff#comment-99613243"}}
{"title": "Changed Amir made to slope_model.py in the Physec repository that we now merge back to Levl", "number": 59, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/59", "body": ""}
{"title": "FIN367 preprocessing", "number": 6, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/6", "body": "copying demodulation from sdr\nedits in de/modulation\nskeleton for rest of preprocess\nmodulate func by naming conventions\nalignment to tools\nsome changes in dialog\n\nSome merging still needed in Dialog receiver - with Nuriel"}
{"comment": {"body": "very good job!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/6/_/diff#comment-74035872"}}
{"title": "Amirs fix for slope - return answer only for matching cfos", "number": 60, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/60", "body": ""}
{"title": "allow filtering of imei in dialog-capture", "number": 61, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/61", "body": ""}
{"comment": {"body": "minor issue, but `processing_queue.put` could potentially be called before `processing_queue` gets created.\n\nI advise to initialize `DialogCaptureIQ` later", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/61/_/diff#comment-101891118"}}
{"title": "Alow filtering several imeis; fix according to CR comment", "number": 62, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/62", "body": ""}
{"comment": {"body": "The dialog is unlikely to be able to receive so many packets consecutively. Please add a sleep of some dozens of ms between packets.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/62/_/diff#comment-101908054"}}
{"comment": {"body": "Right - adding", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/62/_/diff#comment-101920701"}}
{"comment": {"body": "Lots of magic numbers everywhere \\(in `demoded_data.py` as well\\). See if you can somehow make it clearer with variables", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/62/_/diff#comment-101964054"}}
{"comment": {"body": "ok", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/62/_/diff#comment-101977303"}}
{"title": "fix timing for physec", "number": 63, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/63", "body": ""}
{"title": "Feature/h feature", "number": 64, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/64", "body": "H feature notebook with investigation\nAdd H param to CombinedModels"}
{"title": "fix test_packets api", "number": 65, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/65", "body": ""}
{"title": "fix two bugs", "number": 66, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/66", "body": "clean filtering table even when no -imei is used, so if -imei is used and then removed, board would not save last filter;\nfix board getting stuck by taking longer interval between filter messages (100ms  200ms) (Thanks Grisha! :))"}
{"title": "fix timing feature", "number": 67, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/67", "body": ""}
{"comment": {"body": "Looks good", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/67/_/diff#comment-102290940"}}
{"comment": {"body": "The comment was there on purpose for debugging, this function should return only `sum(res) == len(res)`", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/67/_/diff#comment-102314528"}}
{"comment": {"body": "I changed the expected return value to be a tuple", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/67/_/diff#comment-102319847"}}
{"comment": {"body": "A size-1 tuple? \u201csum\\(res\\)==len\\(res\\)\u201d is either 0 or 1, this function shouldn\u2019t return the variable \u201cres\u201d", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/67/_/diff#comment-102320442"}}
{"title": "fix slope test_packets() return value", "number": 68, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/68", "body": ""}
{"title": "fix small bug in timing model", "number": 69, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/69", "body": ""}
{"title": "Feature extraction", "number": 7, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/7", "body": "added transient extraction algorithm\nmove preamble_extraction by iq funct to preamble extraction + transient wip\nfixes in transient\nadded demod_fsk\nadded calc preamble length function\nadd init empty files\ncfo file\nadded cfo extraction code\npycharm minor studd\nuse demod_fsk from demodulator\nfew comments and minor changes\nadd non production cfo extraction algorithm"}
{"comment": {"body": "You need to change the params names to fit the updated modulate names.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/7/_/diff#comment-74344000"}}
{"comment": {"body": "why the median?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/7/_/diff#comment-74345547"}}
{"comment": {"body": "I will change it when we\u2019ll integrate our branches. It works with the modulate\\_GFSK I copied from sdr repo to this file.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/7/_/diff#comment-74347398"}}
{"comment": {"body": "Tried to get aligned data, If for example I demodulated the access code from packet index 22, 24 and 26 so I assume that 22 is before the maximum \\(where the access code really starts\\), 26 is after and 24 is about right, if we look at 22 instead of 24 more chances we demodulated the preamble wrong. I don\u2019t really like this code too but I saw that taking the first split instead is worse \\(the preamble is less stable\\). It\u2019s might be better to align the data after finding the access code and then demodulating again the bits before it. I prefer not to do it in this push since I\u2019ve already tested the function as is.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/7/_/diff#comment-74349505"}}
{"title": "small fix to timing model treshold", "number": 70, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/70", "body": ""}
{"title": "Add start_idx field to packet parser", "number": 71, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/71", "body": "Added packet start index field to the parser"}
{"comment": {"body": "Looks good :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/71/_/diff#comment-102987584"}}
{"title": "Feature/update bt dongle recording", "number": 72, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/72", "body": "Update BT dongles compatibility with current behaviors\nupdate main script of bt_dongle"}
{"title": "Fix record id auto-generate", "number": 73, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/73", "body": "The generated record_id caused parallel job to have the same record_id. Changed it to manually enter a record_id before a new upload."}
{"title": "BIS-3920 hardware timestamp type fix", "number": 74, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/74", "body": "changed hardware_timestamp type from int to int64"}
{"comment": {"body": "Are those new phones added to the dataset?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/74/_/diff#comment-103817677"}}
{"comment": {"body": "Yes, it should already be updated on the master branch", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/74/_/diff#comment-103818219"}}
{"title": "Feature/iphone recordings parse", "number": 75, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/75", "body": "\n\nadd iphone parsing\nadd exception for base64 decode\nadd thermal state, override existing not used for iphone field"}
{"title": "Plot progress indication", "number": 76, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/76", "body": ""}
{"title": "FIN-771 failed packets fix", "number": 77, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/77", "body": "Changed pipeline to write failed packets not just to logs, but to a new table called failed_ble_data (ignore the deleted notebook at the beginning)"}
{"comment": {"body": "It\u2019s best to separate this section to a function instead of duplicating the code.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/77/_/diff#comment-107322577"}}
{"comment": {"body": "This would be a little tricky because of the condition with log\\_to\\_test\\_db and because the script will fail if we use the same message for both writes `\"Failed DF to BigQuery\"`/`'Write to BigQuery'` and `\"Distribute DFs to multiple elements (if needed)\"`/`\"Distribute list to multiple elements (if needed)\"`. Is it worth creating a single function using conditions and formatting for two different cases?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/77/_/diff#comment-107331637"}}
{"comment": {"body": "If they\u2019re not logically meant to do the same thing then its okay to leave it like this.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/77/_/diff#comment-107577983"}}
{"title": "Scale channel numbers", "number": 78, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/78", "body": "To coincide with bosch_integration development"}
{"title": "BIS-4415 levl enable timing and adjust to 2 b", "number": 79, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/79", "body": "fix filter to iq_recording_agent; add beacon_stream_id to DB\nSet proper hardware_timediff_from_prev_packet in pickles so this info can be uploaded to db"}
{"title": "Feature/BIS-243 recording system based on usb dongle", "number": 8, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/8", "body": "add initialization of module bluegiga_advertiser add  snapshot of commit a83c3cd00bde1a9aeeb0290255def900e04d420c\nfix implementation issues in bgapi module and adding some synchronization advertising bosch packet\nWIP: reading temperature from BLE112\nfinalizing temperature reading API\nadding imei replacement\nmoved bluegiga project to io_infra\nstarting to add API for handling ble112 dongles\nrefactoring, object orienting upgrading oven recording project with variable advertisers\nfixing paths and types\nlogging is now not a singleton more synchronization in BLE112 API\ncorrected function usage\ncorrected bytes to string conv\nadding snapshot of  commit 3b6f390e096f1dab11d27305ab509ce7d59e1dbc\nfixed advertisement packet in ble112\nprogressing with bluezero, dbus and stuff\nshifted BT USB dongle access to hcitool/hciconfig\nadding more info about the dongles\nwrapped up, updated API integrated into oven flow\nadded error handling and mac address blacklisting\nfixing types of bt dongle device\nadded missing temperature usage\nfixed temperature again reduced adv interval"}
{"title": "Use consistent path to OLS", "number": 80, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/80", "body": "Statmodels v0.10.0 doesnt have the used import anymore, breaking work with Dataflow.\nReplaced with consistent import"}
{"comment": {"body": "Not enough reviewers", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/80/_/diff#comment-108090473"}}
{"comment": {"body": "Didn\u2019t want to spam everybody\u2019s mail", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/80/_/diff#comment-108090707"}}
{"title": "fix capture-packets script to support cases of non-bosch data", "number": 81, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/81", "body": "Fixes situations where demoded_data cannot be parsed, imei and beacon_stream_id cannot be parsed as well and timediff cannot be computed."}
{"title": "FIN-808 change pipeline for new table", "number": 82, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/82", "body": "Changed pipeline to fit new scheme:\n\nNew ble, ble testing and failed ble packet tables\nreceiver' and transmitter  tx' and rx\nFlattened struct columns: tx, transients, est_data_positions\nRemoved record keywords except for heating app type (also flattened)\n\nFixed bug from last update - record name was not passed to df"}
{"comment": {"body": "what\u2019s the use of `heating_app_type`?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/82/_/diff#comment-108454880"}}
{"comment": {"body": "It was used in the past for stating when the phone was heated using the CPU stressing app. We kept it so we have a documentation of which records used it.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/82/_/diff#comment-108528982"}}
{"title": "FIN-813 db record pickle dump", "number": 83, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/83", "body": "Changed pipeline to dump every finished record to a json file in a new bucket, levl_db_record_jsons, in addition to writing it to BigQuery.\nAlso, fixed source table name in table backup script to the new ble table."}
{"comment": {"body": "Looks good to me :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/83/_/diff#comment-108747812"}}
{"title": "Integrate coarse CFO est into pipeline", "number": 84, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/84", "body": ""}
{"comment": {"body": "Please review", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/84/_/diff#comment-110048895"}}
{"comment": {"body": "Probably should be disabled for Physec.\n\nApart from it looks good!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/84/_/diff#comment-110050072"}}
{"comment": {"body": "Please approve", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/84/_/diff#comment-110089064"}}
{"comment": {"body": "Where does the actual estimation happen?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/84/_/diff#comment-110090050"}}
{"comment": {"body": "Probably easier if you set the default coarse\\_cfo\\_est = 0 and just add it regardless.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/84/_/diff#comment-110090210"}}
{"comment": {"body": "in `cfo_coarse_est` function. It\u2019s called by `preprocess_dialog_signal_start_idx`. The data then propagates to `estimate_and_clean_cfo` for fine estimation.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/84/_/diff#comment-110090838"}}
{"comment": {"body": "Shouldn\u2019t that be included in the pull request as well?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/84/_/diff#comment-110092461"}}
{"comment": {"body": "`cfo_coarse_est` was already there. This PR connects it to the pipeline flow, thus adding the missing coarse CFO estimation.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/84/_/diff#comment-110092975"}}
{"title": "Feature/db versioning", "number": 85, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/85", "body": "See "}
{"comment": {"body": "Nice :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/85/_/diff#comment-109323106"}}
{"title": "Add reset via arduino script", "number": 86, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/86", "body": "update dialog-capture to have a board controller which monitors the board for activity, kicks the power if packets don't arrive and resends the filters"}
{"comment": {"body": "What if we\u2019re recording more than one board? Do we use several Arduinos? How do tell which one belongs to which board?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/86/_/diff#comment-109423310"}}
{"comment": {"body": "Not supported right now and probably won\u2019t support in the near future, but it\u2019s a good question.  \nMaybe pairing boards with relays behind 2 port USB hub and then map these USB connections.\n\nHopefully we won\u2019t need it if we get a solution from Bosch about these crashes in low temperatures.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/86/_/diff#comment-109431447"}}
{"comment": {"body": "Should we add a note in the help line that only one board with relay supported?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/86/_/diff#comment-109435645"}}
{"comment": {"body": "Really cool feature!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/86/_/diff#comment-109435665"}}
{"comment": {"body": "Added note in help in b73c03d128517f68203d73a16cd68ace147a40d0", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/86/_/diff#comment-109441172"}}
{"title": "Feature/connectivity parsing", "number": 87, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/87", "body": "Refactored parse_sensors_from_data\nFixed shelter filtering bug"}
{"comment": {"body": "Please approve", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/87/_/diff#comment-110506712"}}
{"title": "update falling transient length to be relative to end of data", "number": 88, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/88", "body": ""}
{"comment": {"body": "This change requires a new upload version [https://jira.levltech.com:8090/display/IRND/BLE\\+Recording\\+Database\\+Versioning](https://jira.levltech.com:8090/display/IRND/BLE+Recording+Database+Versioning)", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/88/_/diff#comment-110053145"}}
{"title": "Propagate field changes into fingerprinting models", "number": 89, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/89", "body": ""}
{"title": "Few visualization and utils funcs", "number": 9, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/9", "body": "going to merge since it's not production code\n\nadded some data ploting utils\nmore data plots\nadded research_utils.py\nadded table func"}
{"title": "Feature/add rising transient", "number": 90, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/90", "body": "Update rising transient calculation Add rising transient model"}
{"title": "FIN-834 change agc locked", "number": 91, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/91", "body": "changed agc lock to start after access code and give median instead of max"}
{"comment": {"body": "The dialog packets come with '`agc_locked`' field already. Is it not better to use it?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/91/_/diff#comment-110534713"}}
{"comment": {"body": "True, it wasn\u2019t used so far, so I changed for now the calculation from the vector that was being used. Do you know how the one in the packet is calculated? @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} , what do you think?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/91/_/diff#comment-110618005"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} I actually don\u2019t know how they extract it. However we can compare the two. They are probably the same, and if they are, we can take it from the board, and reduce runtime.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/91/_/diff#comment-110930333"}}
{"comment": {"body": "agc\\_locked is the AGC after the access address in the packet, it\u2019s better to use the agc\\_locked value rather than median/max", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/91/_/diff#comment-110932733"}}
{"comment": {"body": "Make sure to create a new UploadVersion tag before merging! [https://jira.levltech.com:8090/display/IRND/BLE\\+Recording\\+Database\\+Versioning](https://jira.levltech.com:8090/display/IRND/BLE+Recording+Database+Versioning)", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/91/_/diff#comment-110932750"}}
{"comment": {"body": "Please approve - we ended up keeping the original value from the packet. \n\n@{5b72a213e72afd064c8a4ebd} can you add the tag please?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/91/_/diff#comment-112669457"}}
{"comment": {"body": "Easily done by going to the page of the commit you wish to tag \\(either the latest commit on this pull request or the merge-commit created after the merge, doesn\u2019t matter which one of those two\\) on Bitbucket and using this button:\n\n![](https://bitbucket.org/repo/bajn5o8/images/505884165-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/91/_/diff#comment-112669483"}}
{"title": "Fix path issue", "number": 92, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/92", "body": ""}
{"title": "Retry shelter command to set shelter", "number": 93, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/93", "body": "Best thing we can do before making it a reliable protocol, which would take time."}
{"comment": {"body": "What is the scenario when you see that it is not reliable?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/93/_/diff#comment-110934438"}}
{"comment": {"body": "Anchors. Sometimes they don\u2019t receive the shelter command and then don\u2019t filter by shelter.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/93/_/diff#comment-110934476"}}
{"comment": {"body": "Did you see that when working with them manually? or only in automatic testing?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/93/_/diff#comment-110934925"}}
{"comment": {"body": "No automatic testing for anchors yet..\n\n@{5cbc1fb4fdcd39100fda97de} couldn\u2019t work with the anchor in the oven, then I saw that it\u2019s due to anchor not receiving the shelter command.", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/93/_/diff#comment-110935004"}}
{"comment": {"body": "OK", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/93/_/diff#comment-110957835"}}
{"title": "Return parsing of recordings from version < 4", "number": 94, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/94", "body": "Code is required for parsing older recordings. Specifically, when we run levl fingerprint using packets from db, the large scale tests script uses this parsing to correct the temperature."}
{"title": "Imported Physec master back into Levl (Inst Freq)", "number": 95, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/95", "body": ""}
{"title": "How to waste time 101", "number": 96, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/96", "body": ""}
{"comment": {"body": "Can you put this validation and printing in a validate\\_proper\\_configuration\\(\\) func?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/96/_/diff#comment-111547398"}}
{"comment": {"body": "Thanks for this addition! \\(:", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/96/_/diff#comment-111547705"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/96/_/diff#comment-111548498"}}
{"comment": {"body": "Great PR!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/96/_/diff#comment-111561548"}}
{"comment": {"body": "Better than last season Game of Thrones!", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/96/_/diff#comment-111583932"}}
{"title": "New! Chinese USB relay controller in yellow box", "number": 97, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/97", "body": ""}
{"title": "Feature/dialog capture sine", "number": 98, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/98", "body": "dialog capture now accepts -sine-capture param and plots it accordingly\nIncrease reset duration\n\nScript would show sine like this (along with -plot arguments):"}
{"title": "Feature/sine files parsing", "number": 99, "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/99", "body": "Add preprocessing of sine files"}
{"comment": {"body": "You said 0.98, did you mean 0.9999?", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/99/_/diff#comment-113689984"}}
{"comment": {"body": "The 0.98 is in line 63.\n\n0\\.9999 is tested after possible correction attempt", "htmlUrl": "https://bitbucket.org/levl/levl/pull-requests/99/_/diff#comment-113690969"}}
{"title": "Feature/improve tests", "number": 1, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1", "body": "Add testing reporting to Bitbucket pipelines.\nNow the failed tests are displayed in the Tests tab, like so:\n\n"}
{"title": "Dev", "number": 10, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/10", "body": "base refactor\nrefactor\ntests\nbuild\nflake8\nrefactor pipeline\nflask run\nref\nbranches\ndev deploy\nFeature stub\nPython issues\nError\nabc.abstractstaticmethod\ninitial commit\nremoved local folder\nstructure fix\nfix open path\nUpdate interface\nCreate device type identification module\nUpdate interface\nCI fixes\nCI fixes\nHave DHCPv6 dummy implementation\nAdd unittest for DHCPv6_DUID\nRename\nupdate package in setup\nFix FeatureReturnCode usage\nPEP8 fix\nPEP\nPEP!\nparsed, not processed\nAdd netbios hostname identification\nssdp user agents\nAdd captive portal\ndont use zlib for now, folder structure update\nfixing pipeline\ns3 pipline fix\nminor\nEmpty commit\nFix empty commit\nrename src to features\nCorrect python package structure\nAll this trouble for conftest.py in root directory\nSummarize combiner flow\nAdd missing phyngerbank\nAdd NB tests\nDTI owns FE, not implements\nflatten feature extraction directory\nFix makefile\nFix paths of packet parser and classifier features:device type is using keys from packet_parser\nFFS paths in docker don't work\nAdd matching module with DB scheme and example flow\nlint: remove unused imports\nremove unused\nremove unused\nremove unused\nWIP\nintegrate kafka with archiver layer\nfix flake error - not used import\nDockerignore dask requirements in requirements.txt back to one package rules them all for now\nflake8 fixes\nadd postgres local docker-compose\nrename volume\nlint\nfix import\npackage struct fix\nWIP\nadded eros-data-archiver to setup.py\nseperate source from pipeline\nsupport multiple pipelines\nmove to pipelines init\nrename source folder to src for now\nadd pipeline unit tests\nexclude .eggs folder\nstabilie package structure\nlint\nlocal stack\nWIP - tests structure - not green yet\nremove device_tests until its green\nremove batch tests until its green\nadd classifier to the local env\nFix to the pipeline\nuse minio instead of local-stack to mock aws-s3\nWindows like import streamz more\nup date local stack with minio\nremoved features from this branch for now\nComplete flow with DHCPv6 as a bypass feature given the current packet parser interface\nMissing change\nrename main package\nlint\nTry to clean things up - still not working\nMove files Make docker_test work\nnotification pipeline\ndisable failed tests\nupdate bitbucket pipeline\n-\nalign\n-\nnotifications configuration\nconfigure topics\nremove test\ndisable failed tests\nquote\nboto config\nremove stale code\nadd producer_config\nkafka producer and consumer configs\nmodify topic names\nWait for postgres in test Cancel unittests\nFix merge issues\nsome fixes\nFor wait for postgres fix fix flake8\nSome env fixes\nwith DB in pipeline\nFix\nno tabs for you\nAdd docker-compose\nNeed dcompose badly\nsetup.py edited online with Bitbucket\nInstall dcompose\nDisable further pipeline steps\nremove docker-cli\nDCOMPOSE\nxfail tests\ndont wait\n\n"}
{"title": "Replace minio with cloudserver", "number": 100, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/100", "body": ""}
{"title": "MEROSP-2230 LENS  Integration - fix schema mismatches", "number": 1000, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1000", "body": "LENS Integration\nAdded kinesis adapter for:\n\nFiltering None valued field - There is no need to send None fields to kinesis, by not sending these fields were improving I/O consumption and being more predictable with field types.\nConverting timestamp from DateTime object to integer (ms) to be aligned with Cujos format\nCast the value field at feature log to be a string to prevent inconsistencies (previously send either string or list, depending on the feature).\nadd WISPER_AGENT to feature log enum - random bug ? was missing for some reason.\nRemove job_id from kinesis context - no reason for that, it was removed from Firehose partition keys \n\n"}
{"comment": {"body": "why is this bytes when it is str?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1000/_/diff#comment-355230828"}}
{"comment": {"body": "All kinesis inputs should be binary, the record holds all that data as a binary string", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1000/_/diff#comment-355232475"}}
{"title": "Cujo data 14 15 2022", "number": 1001, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1001", "body": "PR for cujo"}
{"title": "Feature/MEROSP-2619 save debug data support for new schema", "number": 1002, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1002", "body": "MEROSP-2619: save pcap files for old type events\nMEROSP-2619: Remove write_event_to_pcaps from to_session_files_middle, and minor changes for previously added code\nMEROSP-2619: Add save original kafka record\nMEROSP-2644: add decode_fields function and remove usage of OrderedDic from json.loads\nMEROSP-2619: fixes after Tamir's comments\nMEROSP-2619: fix\n\n"}
{"title": "MEROSP-2655 compare entire random mac to list of known macs", "number": 1003, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1003", "body": "We only compare the first character of the current random mac against the list of previous random macs, causing us to never match on random mac address. We should check the entire address."}
{"comment": {"body": "Regressions passed locally", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1003/_/diff#comment-354670408"}}
{"title": "Cujo data 14 15 2022 l2", "number": 1004, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1004", "body": "new l2 fp from cujo\n"}
{"title": "using mock to test metrics", "number": 1005, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1005", "body": "The propose of this unit tests is to verify all metrics are sent to the cloud, without catching it in the cloud.\ndesign review:\n{: data-inline-card='' }\n\nsmoke passed:\n{: data-inline-card='' }\nAPI is broken (not my fault)\nlocal regression passed"}
{"comment": {"body": "@{6265307b185ac200692f9bd9} please add the design Confluence reference page to the description", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1005/_/diff#comment-354767366"}}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1005/_/diff#comment-354768818"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1005/_/diff#comment-354775082"}}
{"title": "help function added to EventMsg", "number": 1006, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1006", "body": ""}
{"comment": {"body": "Can you please explain why this function should be added to the API?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1006/_/diff#comment-354785258"}}
{"comment": {"body": "I too think that loading from file doesn\u2019t belong there but in test\\_utils. I do think that event\\_msg can potentially provide a function to read from a `KafkaRecord`, but that\u2019s as far as the modifications in this class should go.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1006/_/diff#comment-354942101"}}
{"comment": {"body": "its a matter of design.\n\nin my opinion, if someone new is coming to the code, and wants to create a test,run a pipeline or anything else without using kafka and real events. this function should be in a known class and not in a multi super powerful and messy module called utils.py ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1006/_/diff#comment-354946520"}}
{"comment": {"body": "anyway, we need it for the scale tests with the new events, so put it wherever you decide but it should be in the code", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1006/_/diff#comment-354946745"}}
{"comment": {"body": "Now that it\u2019s implemented like this, my strong suggestion is to move this to be a `classmethod` of `EventMsg` - `EventMsg.from_kafka_record()`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1006/_/diff#comment-354962697"}}
{"comment": {"body": "I agree, I think all the functions that create the different types that can be injected into the system should be grouped together at the API level since they can and should serve all the modules that wish to interact with the classifier.  \nThe thing that bothers me is the fact that most of the functions are in said monstrous utils file which will only become more confusing if the new function will reside in the API all by itself.  \nI think all the functions should be moved to a better place along with the new function and become a part of the API.  ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1006/_/diff#comment-354975627"}}
{"comment": {"body": "Nevermind, let\u2019s be consistent with `convert_to_event_msg` being under `KafkaCommChannel` and move towards merging this PR.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1006/_/diff#comment-355198033"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} :joy: you got confused with your own comments..\n\nthe first commit was a function in EventMsg\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1006/_/diff#comment-355244441"}}
{"title": "MEROSP-2668 check l2 match vendor", "number": 1007, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1007", "body": "Currently the classifier only extracts the canonical name from the L2 model when cross-checking the feature vector. In some cases, several vendors may have families of devices (i.e. canonical names) of the same same. For example P40 is a canonical name of Huawei devices but also of LNMBBS, PCD Argentina and Vankyo devices.\nThe L2 model table also has a vendor column. By cross-checking this column in parallel to the canonical name, we will be able to get more exact matches."}
{"comment": {"body": "please prettify all jsons. you can use `python -m json.tool <input_file> > <output_file>`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1007/_/diff#comment-355227091"}}
{"comment": {"body": "I have mixed feelings about this change, specifically because it changes the amount of data we\u2019ll save in the DB from now on. For example, instead of saving _**\"iPhone 8\"**_ \\(8 bytes\\) we\u2019ll now save _**\\{\"model\\_vendor\": \"Apple\", \"model\\_name\": \"iPhone 8\"\\}**_ \\(51 bytes\\). For two results we\u2019ll save 100 bytes instead of 16. I think we need to find something in between, we\u2019re on the edge of storage amount anyway", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1007/_/diff#comment-355233838"}}
{"comment": {"body": "What do you think of storing it as a concatenated string \\(\u201cApple iPhone 8\u201d\\) and then splitting them for the devices db comparison?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1007/_/diff#comment-355234772"}}
{"comment": {"body": "It\u2019s a good direction to follow. Just make sure that we\u2019re not saving extra details and appendices to our db records.\n\nYou can add a debug breakpoint in dynamo\\_device\\_engine.py::upsert\\_device\\(\\) and see exactly what we\u2019re writing as a record to make sure your change worked", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1007/_/diff#comment-355235554"}}
{"comment": {"body": "I have now done it this way.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1007/_/diff#comment-355249043"}}
{"comment": {"body": "The massive changes to the json files are now reverted.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1007/_/diff#comment-355249121"}}
{"comment": {"body": "Regression passed locally \n\n![](https://bitbucket.org/repo/o5KReBa/images/1330594195-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1007/_/diff#comment-355249173"}}
{"comment": {"body": "Smoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11807](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11807){: data-inline-card='' } \n\nEnough API tests passed according to @{62c1676dce5a604dbfb32cba} : [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11806](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11806){: data-inline-card='' } \n\nRegression also passed on cloud: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11791](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11791){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1007/_/diff#comment-355262184"}}
{"title": "Feature/MEROSP-2591 update resolve ip", "number": 1008, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1008", "body": "Implement ip address resolution in order to close the data gap due to the new schema.\nIPV4 - resolve to single address\nThe sources for the addresses are:  ARP, DHCP and every IP address found in every packet from the IP layer ('raw' IP).\nAll of these addresses are then crossed, and if the intersection is only one address, this will be the chosen one.\nIf there is more than one address in the intersection or there is none, the following priority is used:\n* If there are addresses from the raw IP source, take the most recent\n* If there are no raw IP addresses, choose the DHCP address if available\n* If there is no DHCP address choose the ARP address   \nIPV6 - resolve to a list of addresses\nThe sources for the addresses are:  ICMP, DHCP and every IP address found in every packet from the IP layer ('raw' IP).\nThe addresses from the raw IP source are added to the ICMP and DHCP list of addresses.\nThe following priority is used:\n* If there are addresses from ICMP, choose them along the raw addresses\n* If there are no ICPM addresses, choose the DHCP along the raw addresses\n* If there are no ICMP or DHCP, return only the raw addresses \n"}
{"title": "[Snyk] Security upgrade setuptools from 39.0.1 to 65.5.1", "number": 1009, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1009", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\ntextsearch 0.0.24 requires anyascii, which is not installed.\ndataclasses-avroschema 0.26.1 requires dacite, which is not installed.\ndataclasses-avroschema 0.26.1 requires fastavro, which is not installed.\ndataclasses-avroschema 0.26.1 requires inflect, which is not installed.\ndataclasses-avroschema 0.26.1 requires faker, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  551/1000  Why?* Recently disclosed, Has a fix available, CVSS 5.3  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3180412 |  setuptools: 39.0.1 - 65.5.1  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Async db", "number": 101, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/101", "body": "async sql alchemy\naiopg\nasyncpg==0.23.0\nfix requirements\nstep\nasync step\ntests\nfix tests\nfix tests2\nfix typo\n\n"}
{"title": "[Snyk] Security upgrade setuptools from 39.0.1 to 65.5.1", "number": 1010, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1010", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nhelm_deployment/requirements.txt\n\n\n\nWarning\n```\nkubernetes 10.0.1 requires pyyaml, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  551/1000  Why?* Recently disclosed, Has a fix available, CVSS 5.3  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3180412 |  setuptools: 39.0.1 - 65.5.1  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "MEROSP-2650: Fix classifier consumer group to reduce cross cluster rebalance", "number": 1011, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1011", "body": "MEROSP-2650: Fix classifier consumer group to reduce cross cluster rebalance:\nFor all classifier deployments we are using static group_id (_consumer_group_id = classifier_config.get(\"CONSUMER_GROUP_ID\", pipeline_name)), even id the classifier belongs to different namespace.\nThis can cause rebalances cross namespaces, aka anti-pattern for multi-tenancy.\n\nSome readings to illustrate the problem:\n{: data-inline-card='' } \nIf a service has multiple consumers that subscribe to mutually exclusive topics but that share the same {: data-inline-card='' }  then any rebalance triggered by any one consumer would still affect the other consumers in the group. In the following scenario Consumer A is subscribed to topic abc, whilst Consumer B is subscribed to topic def. They are in the same consumer group foo. If Consumer A takes too long to process a batch and times out then it is removed from the consumer group triggering a rebalance. All partition assignments in the group are revoked and reassigned, including those for Consumer B. \n\n"}
{"comment": {"body": "@{5f82bf320756940075db755e}   \nWhy not f'\\{topic\\}-\\{group\\_id\\}'", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1011/_/diff#comment-356994765"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12806](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12806){: data-inline-card='' }  - regression\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12802](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12802){: data-inline-card='' }  - smoke\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1011/_/diff#comment-356994902"}}
{"comment": {"body": "very good catch :-\\)\n\nnicely done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1011/_/diff#comment-356994916"}}
{"comment": {"body": "Good comment, this happens only once on classifier init, so I will change on different PR..\n\n[https://www.scivision.dev/python-f-string-speed/](https://www.scivision.dev/python-f-string-speed/){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1011/_/diff#comment-356995007"}}
{"title": "fixing reporter - error logs", "number": 1012, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1012", "body": "some of the errors where not being sent to the cloud\nregression passed:\n{: data-inline-card='' }\nAPI is broken,\nSmoke is on fire."}
{"comment": {"body": "@{6265307b185ac200692f9bd9} can you validate the error log actually working now?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1012/_/diff#comment-357070902"}}
{"comment": {"body": "`test_error_logs_corrupted_data` checks that `DeviceIntelligenceErrorLog` is produced. it passes on dev\n\nalso showed Peleg how to reproduce it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1012/_/diff#comment-357117269"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} @{6265307b185ac200692f9bd9}\n\nNow that the only source for \u201cold\u201d events is our lab routers, we should consider the following: Add a 3rd possible value for the \u201ct\u201d flag in the kafka header \\(e.g. \u201cLAB\u201d\\) that will only be used by our routers. That way we can get rid of the configuration flag \u201cactivate schema\\_translation\u201d, and the if..else in `convert_event_msg()` \\(utils.py:65\\) will support the 3rd type by implementing translation there. This will let us remove the translation from the comm channel and converge all \u201cevent parsing\u201d errors into one function `convert_event_msg` that is already on the pipeline side and creates an erronous connection\\_result with the required error log. In that case `EventMsg` will not have to carry any logs and will serve its original goal.\n\nSo we should decide on this change before introducing the change in this PR. What do you think?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1012/_/diff#comment-357296515"}}
{"comment": {"body": "let me revert the change in event msg. I was too much righteous here. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1012/_/diff#comment-357298837"}}
{"comment": {"body": "Let\u2019s not call it something generic like \u2018LAB\u2019 as there are might be future versions as well, explicit version type would be better, for example: \u2018ING\\_V1\u2019 as it related to specific schema\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1012/_/diff#comment-357315490"}}
{"comment": {"body": "does this assumption holds or real data?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1012/_/diff#comment-357318268"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1012/_/diff#comment-357318523"}}
