{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} Maybe it will be useful to keep the same structure as the `FILTER_UI_DEVICES_BY_TIME` above and subordinate this check to some global flag.  And sorry for bringing this up now, but can\u2019t we reuse the parsing in `_get_device_type_from_fingerbank` \\(add the types to `BAD_FINGERBANK_DEVICE_TYPES` or `FINGERBANK_DEVICE_TYPES_RENAMES` or some new group and use the result\\)?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-*********"}}
{"comment": {"body": "\\+1", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-*********"}}
{"comment": {"body": "Are we waiting on anything to merge this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-*********"}}
{"comment": {"body": "The only matter is the UI refresh t/o default.. AP and Switch mode", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-*********"}}
{"comment": {"body": "This is done on purpose.\n\nYou can\u2019t just raise the logging level. It\u2019s not C/C\\+\\+ that you can \u201cundefine\u201d the debug print out, you will burn an enormous amount of processing time to do string comprehension.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-*********"}}
{"comment": {"body": "Got it.  \nLet\u2019s merge it and solve it later.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-205131788"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} I understand that string formatting is done anyway, but what\u2019s the actual cost? Is it worth writing software much more complicated for the extra few cycles?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-205134409"}}
{"comment": {"body": "If not done today, we will merge..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-205136868"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} It was noticeable in the performance task we have done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-205137110"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} It\u2019s a debug log per a print operation, so it is probably around ~100 time heavier, so when you have a very large number of devices it becomes the majority of your CPU usage.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-205145429"}}
{"title": "Update server domain list", "number": 778, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/778", "body": ""}
{"title": "Use the built-in python csv moudle to automatically escape all data", "number": 779, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/779", "body": ""}
{"title": "Fix bug: disconnected user during train/classify should not appear in UI", "number": 78, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/78", "body": "Modified the state change so that:\n(1) It would be done only if the user still exists\n(2) It would be done by data_processor only, in the same thread where it handles states, to prevent race conditions"}
{"comment": {"body": "It\u2019s weird that data\\_processor has to handle \u201cinternal data\u201d, isn\u2019t it?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/78/_/diff#comment-148071356"}}
{"comment": {"body": "What do you mean?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/78/_/diff#comment-148071453"}}
{"comment": {"body": "@{5c98864d67af62601c20d976} This change makes data\\_processor not only the handler of data from AP, but also the handler of data from inner processes. This looked strange to me, design-wise. I guess we can go on with this fix and talk about processes' roles later.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/78/_/diff#comment-148071588"}}
{"comment": {"body": "As discussed over phone, data\\_processor is now the only one responsible for modifying users status. We should consider changing its name later on so it would be clearer.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/78/_/diff#comment-148071793"}}
{"comment": {"body": "Isn\u2019t it redundant to pass `user_to_classification_result` now?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/78/_/diff#comment-148074015"}}
{"title": "Set a manual IPv6 prefix so devices can send IPv6 SN, instead of trying to pull IPv6 prefix from the GW", "number": 780, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/780", "body": ""}
{"title": "Reenable IPV6 NS wait criteria", "number": 781, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/781", "body": ""}
{"title": "Windows DHCPv6 in prelinemary condition even in IPv4 only networks", "number": 782, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/782", "body": ""}
{"title": "implement fingerbank bypass", "number": 783, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/783", "body": "WIP\nRemove management threads\nRemove some logs to debug and fix enum conversion exception\nadd agent simulator script\nAdd metrics to Telegraf and some initial stats\ninitial test for pcap writer with offload queue\ncompose fix\n\nRevert \"Merge branch 'itai/refactor' of bitbucket.org:levl/comcast into feature/scale_and_performance\"\nThis reverts commit ebd5fecb8baded0260f5f7e439dd720d82d7b750, reversing changes made to f71b16cec40e1f05f9fa29a63bad2b8593a9d307.\n\n\nignore redis files\n\nlogging collection and presentation\nfix path\njsom formatted logging\nadd elk\nformat log as elk json\nadd json elk formatter\nRemove management threads\nhack to make it run\nextract standard logging fields\nConnetion event count + critical print in logs\nbypass fingerbank\ndisable logging\nuse redis queue\nadd worker process\nadd conenction events to metrics\nMonitor and optimize periodic event management flow\nremove iterating all session in main loop\nremove eaptls from main loop\nAdd cache statistics\nreduce number of DHCP packet parses\nDo not process DHCP directly. Also forgotten insert_packet logic\nremove double checking of tcp timestamps due to merge\nremove iteration on users list inside data path\nrefain from accessing session dict multiple times\nDo some processing on the AP-system\ncommented out cache time measurements\nstream_endpoints writing pickles to redis queue\nadd debug and extentions recommendations\ncleanups\nfix term color bug\nraise Empty exception\nstart data processor\nseparate processes\nseparate process\nremove iteration of all sessions for reclassification\nHandle model update inside training thread\nremove ModelUpdate event\nremove session cache\nFor now just DHCP packets in handle_connected for fingerbank because it's eats too much time\ncomments out logging that are dependent on number of devices\nupdate intervals\nremoved unused var\nAdd KPIs\nDo not call fingerbank\nfix LB function\nscale to 3 data_processor\nAdd Logging imporvements and code cleanup\nmerge with shimon_10k_devices\nFix docker compose worker partitions\nFix scale workers\nTagging metrics woth partition\nLoad balancing function and 4 workers\nfix filebeat file path\nfilebeat fixed\nfix filebeat config\nfix elk deploy\ndeploy elk\nprofiler resutls fixes\nremove filter by last seen and asizeof - ptofiling\nSE metrics per 10 seconds and not on each packet - profiling\nbuffer before redis\nlog critical\nfix agent script\nRemove some unnecessary DB writes and making sure no redundant writes are made when device isn't really saved yet. Spread out some writes that can be delayed/missed\nrestore print_stats\nredis batch put from stream endpoints\nRemove DNS queries extraction on just_connected, it is done on connection and later on every packet\nDon't query db for each session X on each periodic state management round. Do it once per round, save it in data_processor and use it inside the session's state\ndon't cast dict from cursor twice. Don't iterate sessions for reclassification. Remove unnecessary logging\nDo not do a busy wait on the Redis queue\nbatch get\nAdd batch get\nrmeove batch usage for now\nAdded db_accessor call that returns only records modified since {}. modified means that attributes that are used for filtering operations were modified. In order to do that, added a timestamp column in the devices table. DeviceRecord is not aware of that, it is purely a db_accessor auxiliary item.\nreturn batch wirh 50 only on put and 2 seconds db get to scale workers\nsmall fixes\nMany telegraf metric sends that grow with # of sessions and devices\nget qsize only periodically, not on every event\nreduce redis querying\nIf we classify a device that is affine to another worker, we can't update it in shared state. So, devices dict will forever only contain affine devices. All system-wide devices access is done via DB (for now). In this case, the path to updating device parameters if just_connected linked a session to an existing device went through the devices dict. Now it will not, it will only go to DB. This means that affine devices in devices dict could have some outdated parameters.\nazure APM logs\nSend disconnection event instead of changing state to disconnected\nadd env files to docker and enable management threads and UI with flags\nmerge fixes\nenable telegraf state ap monitor and server\nimplement fingerbank bypass\n\n"}
{"comment": {"body": "you could go more pythonic and return either `fingerprint` or `None`.  \nThen it\u2019s easy to ask whether there\u2019s an answer, without subscripting.  \njust `if result` instead of `if result[0]`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/783/_/diff#comment-*********"}}
{"title": "NetBIOS transaction id feature", "number": 784, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/784", "body": "We wish to find matching Windows devices by their NetBIOS transation id.\nThe transaction id is sent upon a new connection, and sometimes keeps updating after the connection is established.\nWhen a device disconnects and re-connects, whether to the same SSID or another one, and whether it changes its mac or not, we see a very minor increase in that transaction id.\nThe logic in this PR searches for that minor increase, while looking at a small gap of time that represents that disconnection and immediate re-connection.\nSince we extract the transaction id from NetBIOS packets, I separated the extraction of NetBIOS hostname extraction and NetBIOS transaction id extraction.\n"}
{"comment": {"body": "I suggest you reduce the passes to a single pass over the cache", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/784/_/diff#comment-204870484"}}
{"title": "itai/scale_and_performance", "number": 785, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/785", "body": "bug fix - complete interface change\n\n\nuse azure agent for log shipping to azure insights\n\n\nMerge branch 'feature/shimon_10k_devices' of bitbucket.org:levl/comcast into itai/scale_and_performance\n\n\nlogging level error\n\n\ngraphana metrics\n\n\nfix merge bugs\n\n\nadd thread id to logging\n\n\n\nCreated from Atlassian for VS Code"}
{"comment": {"body": "what\u2019s the reason for this import?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/785/_/diff#comment-205231041"}}
{"comment": {"body": "We should remove it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/785/_/diff#comment-205248520"}}
{"comment": {"body": "@{5f82bf320756940075db755e} it\u2019s still here", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/785/_/diff#comment-205255316"}}
{"title": "Feature/ICMPv6 MCLR", "number": 786, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/786", "body": "Add ICMP MCLR as a feature:\n\nAdd ICMP6 MCLR parsing\nAdd icmpv6 MCLR filtering to agents\nAdd ICMP MCLR ongoing tracking\n\n\n\nRefactor extract_info_from_caches return value\n\n\nThis is to make it more clear about what return values exist and what their types are\n\nI dont have much information about the types. If you know something I have listed as Any, please let me know\n\n\n\n\n\nFix bug in ICMP6 NS ongoing training\n\n\nInfo about this feature:\nResearch Confluence page: \nSoftware documentation: \nWhere I saw this working:\n\nSamsung S9 HW mac leakage: right after restart, the device would share the 3 lower bytes of the HW mac to whatever wireless connection its connecting to (2.4GHz/5GHz). This allows learning the device\nWhen connecting to our SSID after being connected to 3rd party SSID.\n  E.g. connected to LEVL  connected to ENT  connected to LEVL  connected to IOT would work with this feature\n\n"}
{"comment": {"body": "PR is ready for review after merger with master", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/786/_/diff#comment-205820664"}}
{"comment": {"body": "the http filters working in AP?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/786/_/diff#comment-205876603"}}
{"comment": {"body": "i didn't see any problem, so yes", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/786/_/diff#comment-205881611"}}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/3904719188-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/786/_/diff#comment-205889669"}}
{"title": "itai/scale_and_performance", "number": 787, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/787", "body": "Created from Atlassian for VS Code"}
{"title": "Performance changes merge to SNA", "number": 788, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/788", "body": ""}
{"comment": {"body": "24 or 48?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/788/_/diff#comment-206040021"}}
{"comment": {"body": "24", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/788/_/diff#comment-206044204"}}
{"comment": {"body": "@{5f82bf320756940075db755e} I think that it was edited to 48 hours in your pr yesterday", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/788/_/diff#comment-206044495"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} yes, and we have changed it in SNA to 24h in place..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/788/_/diff#comment-206046135"}}
{"title": "Itai/scale and performance back to master", "number": 789, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/789", "body": "SNA quick fixes\nUsing VERBOSE config\ndocker config\nOnly verbose prints in print_stats\nskip unrelevant stuff for non-AP based system\ndisable last connected filtering logic\nfix\nremove debug print\nrevert package-lock.json\nrevert\nVerbose flag by type of deployment\ndisable verbose\nFix metrics load and add partition name dummy to work with dashboard\nBasic changes to relief load - increase sampling interval on client side, decrease ui per-device log size to 10\nreduce caching in flask to 5sec\nReduce size of history logs query\nAllow 2 sec timeout for HTTP request\nfilter_devices_by_dhcpv6_identifiers_win10 wasn't changed to be not static\nFix undefined session access\nAdd mechanism to save session-device linking events to DB with broad metadata to provide insight on the decision process\nDon't access device when there's no device\nSplit up common.py\nDeviceAuthStatus back to common.py\nsmall fixes\nlast fixes for now\nRevmoing bonjour_uid from our mandatory condition for now\nAdd connection_timestamp as indexed field, add data extracted from cache, set decision_id and write it to log upon save\nNow showing routers, aps, switches, etc in the UI\nMoved the logic to a proper method\nMissing ,\ndevice_is -> is_device\nNot presenting devices that were not active in the last 24 hours\n\nRevert \"Merged in decision_logging (pull request #767)\"\nThis reverts commit 75222649661cb1113ffd3c72a1d9231f64c2f918, reversing changes made to ****************************************.\n\n\nFix merge mistake in struct/Session, set default jsonification method for caches data\n\nApply search filter only on ENTER key press\n\nRevert \"Revert \"Merged in decision_logging (pull request #767)\"\"\nThis reverts commit 8cb176e175c6ae161bf73211741a7e103dbd9eab.\n\n\nAdd previous mac address to the device in UI so it can searchable\n\ntypo\nAdded MINIMIZE_DISPLAYED_DEVICES_UI flag to docker-compose to determine if we want to filter out devices or not, just to make the future merge with master to be easier\nDefault to false\nNow filtering only by time\ndecision_log table was not created on upgrade\nLimit the number of previous mac addresses to return\nEnable serializing of dicts with dataclasses keys\nUse the built-in python csv moudle to automatically escape all data\nStarted implementing the feature of using netbios transaction id to detect devices\nAdded netbios_transaction_id to preliminary condition\nprocess_netbios_register -> process_netbios\nNetBiosExtraction fix\nAdded trasaction id parsing to the preassociation cache\nAdded transaction id to the mandatory condition for windows_10 devices\nNow saving the timestamp of the netbios transaction iid\nReturn value fixes\nAnother return value fix\nNone value fix\nOngoing update for transaction id\nNow saving the transaction id timestamp to the db\nRemoving netbios transaction id timestamp\nAdded a filter method in just_connected_state to find the matching candidates\nRemoved transaction id timestamp\nlog fix\nReturn value fix\nHighlight for easier debugging\nLogs fixing\nstr -> int\nDidn't handle well the case of candidate device with non intersecting attribute\nconsider the case of no matching device after select_device_with_highest_priority\nSet a manual IPv6 prefix so devices can send IPv6 SN, instead of trying to pull IPv6 prefix for the GW\nReenable IPV6 NS wait criteria\nWindows DHCPv6 in prelinemary condition even in IPv4 only networks\nfix flow\nDocumentation updates\nChanged back un-necessery change\nAdd SNA Hotel#2 env\nMoved the transaction id to be part of the param_list so it would behave as a feature and not as an identifier\nExtra parameter\nMissing assignment\nIncreased timeout. Fixed cmp_func usage to be safer\nFix merge\npersist redis to host\n\n"}
{"title": "Added exception handling for the locking mechanism", "number": 79, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/79", "body": "Minor bugfix."}
{"title": "Disable hostname feature - Android", "number": 790, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/790", "body": "Added a flag to disable the matching hostname feature for Android devices. It might fail us in our upcoming pilot, where theyll most likely change the name of the devices to see if it affects our levl ID decision making."}
{"title": "remove hack", "number": 791, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/791", "body": "\n\nremove hack\n\n"}
{"title": "Add filter and UI text for alst devices 48h", "number": 792, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/792", "body": "\n\nAdd filter and UI text for alst devices 48h\n\n"}
{"title": "Added missing %s when inserting a device to the db", "number": 793, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/793", "body": "We had 44 parameters, only 43 %s"}
{"title": "Feature/master merge 10k", "number": 794, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/794", "body": "Add ICMP6 MCLR parsing Refactor extract_info_from_caches return value\nAdd icmpv6 MCLR filtering\nAdd ICMP MCLR as a feature Add ICMP MCLR ongoing tracking\nRuntime fixes\nFix variable name\nUpdate caps_model_type\nPerformance fixes cherry-pick\nPrints to human readable\neliminate gazillion calls to fingerbank\nincrease last seen update interval\nFinalize optimization changes merge\niterate sessions twice\nhave a copy of the sessions dict as it can change due to dismiss_inactive_sessions_of_device\nreduce prints in JustConnectedStates\nFix variable\nMove extractinfo struct to a seperate file\nFix circular dependency and jsonizing\nFix serialization of ExtractValues\nFix ICMP6 NS bug in ongoing training\nchange decision_log recor id to 128-bit\ndecision_id is actually 16 characters in DB, so not gonna change this :(\nAdded a flag to disable the matching hostname feature for android devices since it might fail us in our upcoming pilot\nMissing brackets\nCleanup\nAdd filter and UI text for alst devices 48h\nfilter disable by default\nreturn back session mac search\n\n"}
{"title": "TP-LINK APs to not be identified as windows devices", "number": 795, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/795", "body": "Our APs connect from time to time to our system as devices, and some of the TP-LINK APs are identified as windows devices because of their returned device type model from the fingerbank: \n[data_processor.py:212] User d8:07:b6:e1:cb:cb got device_info {'display_device_manufacturer': 'TP-LINK TECHNOLOGIES CO.,LTD.', \n'display_device_model': 'Operating System/Windows OS/Windows Phone OS/Windows Phone 8.0', 'device_type_model': 'Operating System/Windows OS/Windows Phone OS/Windows Phone 8.0', \n'device_type_manufacturer': 'TP-LINK TECHNOLOGIES CO.,LTD.', 'device_addr': 'd8:07:b6:e1:cb:cb'}\nAfter we identify them as a Windows device, we expect the netbios_transaction_id to arrive, and because of that we are giving up on their connection. Even though we dont even show them on the dashboard now, it really spams our logs and increases the amount of given up devices.\nThis patch solves it by explicitly not considering TP-LINK devices as Windows devices.\n(The changes in data_processor are just nice to have after I needed more data in those logs earlier this week)"}
{"title": "Feature/eg programming", "number": 796, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/796", "body": "4 EnGenius programming\n4 EnGenius programming\n\n"}
{"comment": {"body": "add autogenerated files as well? after running setup.sh", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/796/_/diff#comment-206352074"}}
{"title": "Feature/static csi", "number": 797, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/797", "body": "Static CFR related changelog:\n\nAdd CFR cache for static CFR\n\nAdd static CFR algorithm\n\nAdd static CFR as a matching feature\n\n\n\nFix ap_agent when working with 2.4ghz\n\nRaspberry pi is also linix\n\n\n\nGeneral changelog:\n\nICMP TS feature also for linux devices (RPies)\nautogenerate values pattern in db_accessor.add_device\nFix premature disconnection in physical layer after dhcp timeout (and before give_up timeout)\n\n\n\nConfluence documentation: "}
{"comment": {"body": "You\u2019re welcome to review this PR", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/797/_/diff#comment-207291832"}}
{"comment": {"body": "why you have removed the just connected state?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/797/_/diff#comment-207369900"}}
{"comment": {"body": "This is the list of states that would **not** receive CFRs.\n\nWe need CFRs during JUST\\_CONNECTED state.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/797/_/diff#comment-207371467"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} @{5a4500fe0cacf235de82a9d4} we should discuss static IP use case and features", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/797/_/diff#comment-207379440"}}
{"title": "Feature/10k devices snap", "number": 798, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/798", "body": "WIP\nRemove management threads\nRemove some logs to debug and fix enum conversion exception\nadd agent simulator script\nAdd metrics to Telegraf and some initial stats\ninitial test for pcap writer with offload queue\ncompose fix\n\nRevert \"Merge branch 'itai/refactor' of bitbucket.org:levl/comcast into feature/scale_and_performance\"\nThis reverts commit ebd5fecb8baded0260f5f7e439dd720d82d7b750, reversing changes made to f71b16cec40e1f05f9fa29a63bad2b8593a9d307.\n\n\nignore redis files\n\nlogging collection and presentation\nfix path\njsom formatted logging\nadd elk\nformat log as elk json\nadd json elk formatter\nRemove management threads\nhack to make it run\nextract standard logging fields\nConnetion event count + critical print in logs\nbypass fingerbank\ndisable logging\nuse redis queue\nadd worker process\nadd conenction events to metrics\nMonitor and optimize periodic event management flow\nremove iterating all session in main loop\nremove eaptls from main loop\nAdd cache statistics\nreduce number of DHCP packet parses\nDo not process DHCP directly. Also forgotten insert_packet logic\nremove double checking of tcp timestamps due to merge\nremove iteration on users list inside data path\nrefain from accessing session dict multiple times\nDo some processing on the AP-system\ncommented out cache time measurements\nstream_endpoints writing pickles to redis queue\nadd debug and extentions recommendations\ncleanups\nfix term color bug\nraise Empty exception\nstart data processor\nseparate processes\nseparate process\nremove iteration of all sessions for reclassification\nHandle model update inside training thread\nremove ModelUpdate event\nremove session cache\nFor now just DHCP packets in handle_connected for fingerbank because it's eats too much time\ncomments out logging that are dependent on number of devices\nupdate intervals\nremoved unused var\nAdd KPIs\nDo not call fingerbank\nfix LB function\nscale to 3 data_processor\nAdd Logging imporvements and code cleanup\nmerge with shimon_10k_devices\nFix docker compose worker partitions\nFix scale workers\nTagging metrics woth partition\nLoad balancing function and 4 workers\nfix filebeat file path\nfilebeat fixed\nfix filebeat config\nfix elk deploy\ndeploy elk\nprofiler resutls fixes\nremove filter by last seen and asizeof - ptofiling\nSE metrics per 10 seconds and not on each packet - profiling\nbuffer before redis\nlog critical\nfix agent script\nRemove some unnecessary DB writes and making sure no redundant writes are made when device isn't really saved yet. Spread out some writes that can be delayed/missed\nrestore print_stats\nredis batch put from stream endpoints\nRemove DNS queries extraction on just_connected, it is done on connection and later on every packet\nDon't query db for each session X on each periodic state management round. Do it once per round, save it in data_processor and use it inside the session's state\ndon't cast dict from cursor twice. Don't iterate sessions for reclassification. Remove unnecessary logging\nDo not do a busy wait on the Redis queue\nbatch get\nAdd batch get\nrmeove batch usage for now\nAdded db_accessor call that returns only records modified since {}. modified means that attributes that are used for filtering operations were modified. In order to do that, added a timestamp column in the devices table. DeviceRecord is not aware of that, it is purely a db_accessor auxiliary item.\nreturn batch wirh 50 only on put and 2 seconds db get to scale workers\nsmall fixes\nMany telegraf metric sends that grow with # of sessions and devices\nget qsize only periodically, not on every event\nreduce redis querying\nIf we classify a device that is affine to another worker, we can't update it in shared state. So, devices dict will forever only contain affine devices. All system-wide devices access is done via DB (for now). In this case, the path to updating device parameters if just_connected linked a session to an existing device went through the devices dict. Now it will not, it will only go to DB. This means that affine devices in devices dict could have some outdated parameters.\nazure APM logs\nSend disconnection event instead of changing state to disconnected\nadd env files to docker and enable management threads and UI with flags\nmerge fixes\nenable telegraf state ap monitor and server\nimplement fingerbank bypass\nadd base env file\nbug fixes\nadd metgric\nadd api key tag\nmerge fixes\nadd thread id to logging\nfix merge bugs\ngraphana metrics\nlogging level error\nfb metrics fix and CR fix\nremove vscode launch file from git\npcap writer CR updates before merges\ndb update fields return fix\nuse azure agent for log shipping to azure insights\nbug fix - complete interface change\nsend metrics to azure apm\nwork around the error when batch_put\nremove azure_handler\npersist redis to host\nfix merge\nfix pcap writer flag\nfix merge netbios broke\nfix merges with db\nremove hack\nfingerbank bypass for dns, http\nCR fixes\nmerge fix db accessor for filtered by time\nperformance changes\nfix db accessor merge and default info log level\n\n"}
{"title": "SNA - small algorithmic adjustments", "number": 799, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/799", "body": "Compare MAC address part of DUID for DHCP DUIDv6 comparison. Some esoteric devices change the timestamp porting of it rather quickly.\nFilter using OS classification instead of using the DHCP fingerprint as we have noticed changes in the DHCP fingerprint of devices.\n\n"}
{"title": "Feature/recording server client", "number": 8, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/8", "body": "Add an option to send CFRs from the server to some PC inside levl.\n\nSimple publisher, subscriber threads\nintegrate into worker\n\nThis is on purpose without SSH tunnels since it looks like throughput via SSH in Windows is very low.\npublisher & subscriber work. Integration into cfr_worker.py would be tested later."}
{"comment": {"body": "Don\u2019t know whether you care or not but this supports only a single connection. If you do care, I suggest using `socketserver.ThreadingTCPServer` as a higher-level abstraction for a TCP server that handles multiple clients, automatically, each in it\u2019s own thread.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/8/_/diff#comment-142685260"}}
{"comment": {"body": "Here is an example, it\u2019s super simple:\n\n```\nimport socketserver\n\nclass MyHandler(socketserver.StreamRequestHandler):\n    def handle(self):\n        logging.info(f\"New connection from {self.client_address}\")\n\n        # Do something continuously with self.wfile for tx and self.rfile for rx...\n\n        # Return from function to disconnect client\n        logging.info(f\"Disconnecting {self.client_address}\")\n\nwith socketserver.ThreadingTCPServer((\"0.0.0.0\", port), MyHandler) as server:\n    server.serve_forever()\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/8/_/diff#comment-142685786"}}
{"comment": {"body": "Good idea, but not in the current scope", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/8/_/diff#comment-142697416"}}
{"title": "Added nordic/comcast flavours", "number": 80, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/80", "body": "This necessary because the different demos use different features."}
{"comment": {"body": "Yafe meod", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/80/_/diff#comment-148087443"}}
{"comment": {"body": "Toda Raba", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/80/_/diff#comment-*********"}}
{"title": "fix cert", "number": 800, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/800", "body": ""}
{"title": "Brought brack dhcp model, so we'll have at least a model created", "number": 801, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/801", "body": "Without it data_processor:handle_model_update() won't be called and we won't insert the device to our DB or save it to our devices in shared server state"}
{"title": "Feature/fingerbank bypass", "number": 802, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/802", "body": "add vscode internal tool to ignore\nfingerbank bypass\n\n"}
{"comment": {"body": "This condition overrides [https://bitbucket.org/levl/comcast/pull-requests/795](https://bitbucket.org/levl/comcast/pull-requests/795){: data-inline-card='' } since the TP-Link detection is based on the MAC address.\n\nCan we expand the bypass to work with mac addresses? We have the `mac_to_vendor` module that can help us.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/802/_/diff#comment-*********"}}
{"comment": {"body": "We already using `pytelegraf` above, let\u2019s stick with one client.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/802/_/diff#comment-*********"}}
{"title": "Updated the ip address of the aruba switch", "number": 803, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/803", "body": ""}
{"title": "fingerbank key update", "number": 804, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/804", "body": ""}
{"title": "Fixed netbios_id / bonjour uid mismatch", "number": 805, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/805", "body": "The parameters order was mixed. And we set the bonjour uid as the NetBIOS Transaction ID"}
{"title": "we'll do verbose for now as scale is small", "number": 806, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/806", "body": ""}
{"title": "disable by default pcap recording in stream endpoint", "number": 807, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/807", "body": "Disable PCAP recording by default\nAdd to gitignore vscode autocompletion file\n\n"}
{"title": "Mich/nec fixes", "number": 808, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/808", "body": "iOS not part of permissive mode\nUsing same clock source for the last_seen logic for the connection_timestamp and the incoming data timetamps\ndo not change UI behaviour\n\nFixing bugs:\nCCP-581, CCP-583 and CCP-584\n"}
{"comment": {"body": "not using `data.capture_ts` here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/808/_/diff#comment-207180707"}}
{"comment": {"body": "how are 583 and 584 fixed?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/808/_/diff#comment-207180980"}}
{"comment": {"body": "We have a mix-up of the times we are using. We sometimes take the packet time and sometimes the server time.\n\nHere the first var is use for the algorithm when we use the packet time. The second one is for display in the UI where we use the server time.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/808/_/diff#comment-207182894"}}
{"comment": {"body": "It\u2019s not 100% fixed but that\u2019s also an issue.  \nAs I mentioned 583 and 584 can be cause either by the last seen feature or filter by SNMP.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/808/_/diff#comment-207182925"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Could you add some comment somewhere or even modify the variable name so that we don\u2019t repeat this bug?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/808/_/diff#comment-207185485"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Comments added", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/808/_/diff#comment-207189751"}}
{"title": "Updated the aruba switch IP in the snmp config", "number": 809, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/809", "body": "This fix in in release/pilots_nec, we need it in the master too"}
{"title": "Refinement of Cap-FP by using large scale evaluation", "number": 81, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/81", "body": "Ignoring reserved bits in caps filed, which makes vht_caps match accross all smps_modes and subtypes\n\nConfusion matrix attached\n\n"}
{"comment": {"body": "what can explain phone #15\u2019s FN?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/81/_/diff#comment-148075025"}}
{"comment": {"body": "Is that valid? Couldn\u2019t find a reference to it in the spec", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/81/_/diff#comment-148075120"}}
{"comment": {"body": "[https://www.oreilly.com/library/view/80211ac-a-survival/9781449357702/ch03.html](https://www.oreilly.com/library/view/80211ac-a-survival/9781449357702/ch03.html)\n\nUnder management frames", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/81/_/diff#comment-148075204"}}
{"comment": {"body": ":man_shrugging: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/81/_/diff#comment-148075209"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Oh, didn\u2019t realise that was optional", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/81/_/diff#comment-148075531"}}
{"comment": {"body": "reuse `wifi_slopes/generate_data.py`-`mac2inventory_id`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/81/_/diff#comment-148075684"}}
{"comment": {"body": "And iphone #76 \\(or #75, i can\u2019t really differ\\)? Can the devices change behavior?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/81/_/diff#comment-148075840"}}
{"title": "Pilot_NEC -> master", "number": 810, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/810", "body": ""}
{"title": "initial simple get debug api on the decision log", "number": 811, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/811", "body": "disable by default pcap recording in stream endpoint\nadd rest api for decision log\ninitial decision log debug api implementation\n\n"}
{"comment": {"body": "non documented debug API, with hash secret ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/811/_/diff#comment-207196879"}}
{"comment": {"body": "In what scenario can `FileNotFoundError ` be thrown?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/811/_/diff#comment-207200160"}}
{"comment": {"body": "send\\_file can throw", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/811/_/diff#comment-207201915"}}
{"title": "Raise netbios allowed timegap from 20 to 60 seconds", "number": 812, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/812", "body": "Due to bug \nMore info inside that Jira issue"}
{"title": "Add flag for netbios mandatory disable", "number": 813, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/813", "body": "Add flag for netbios mandatory disable\n\n"}
{"title": "Nec/fix last seen", "number": 814, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/814", "body": "After looking at some weird behaviour regarding to time conflicts, such as CCP-574, this PR comes to unify our last seen savings as much as possible. Hopefully it will solve it.\n"}
{"title": "Adding stats to pcap writer", "number": 815, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/815", "body": "Adding stats to pcap writer\n"}
{"title": "add stats to snmp thread", "number": 816, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/816", "body": ""}
{"title": "Disabled premissive mode for ap configuration as well. It doesn't add much at this point", "number": 817, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/817", "body": "The permissive mode is disabled in our switch mode and now should be dibbled in the ap configuration. We can see it causes some models bugs, as in CCP-591. This logic doesn't add much at this point, especially when we have more features to rely on."}
{"title": "Updated _get_device_type_from_fingerbank()", "number": 818, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/818", "body": "In CCP-592, we got back from the fingerbank for a Linux device:\n'device_type_model': 'Operating System/Linux OS/Debian-based Linux'\nIn order to _get_device_type_from_fingerbank() recognize the type and not return a '-', we can add it to our known types"}
{"comment": {"body": "If the device\\_model\\_str='Operating System/Linux OS/Debian-based Linux'\n\nShouldn\u2019t it return true here anyways as it contains the string \u2018Linux OS\u2019?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/818/_/diff#comment-*********"}}
{"comment": {"body": "We examine the last part of Y/Y/Y device model, so we look at the 'Debian-based Linux' part", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/818/_/diff#comment-*********"}}
{"title": "Fixed channel_mhz -> self.channel_mhz", "number": 819, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/819", "body": ""}
{"title": "Feature/fix CCP-61", "number": 82, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/82", "body": "Allow more robust handling of single 20MHz segment\n\n"}
{"title": "Fixed an exception when time filtering the cache", "number": 820, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/820", "body": "Fixed an exception when time filtering the cache, it happens now because the CFRCache is the only one that is time_filterable and we see it happen in the static_csi feature"}
{"title": "add stats strem endpoint", "number": 821, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/821", "body": ""}
{"title": "Feature/802 11 seq num identifier", "number": 822, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/822", "body": "AP/third side set in config (not runtime), runtime info used for validation\nAdded 802.11 seq num monotonic progress feature\n\nTODO (all are expected in next PR):\n\ntime-limit seq num participation.\nadd probe request grid feature.\nrestrained saving to DB of seq num for both live sessions and disconnected ones.\n\n"}
{"title": "Include only initial HTTP PUT/GET request and a bit of other protocols", "number": 823, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/823", "body": ""}
{"comment": {"body": "Can you elaborate on the necessity?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/823/_/diff#comment-207852472"}}
{"comment": {"body": "The previous filter was bringing all HTTP traffic so for example if there was a big file downloaded/uploaded over HTTP we would capture all the packets. This happened a few times in production system and it\u2019s effectively killing our server \\(and what we have done so far so to turn off HTTP completely\\)\n\nThe first packet sent is the request and all following packet are continuation packets. This filter works such that we only capture the first request packet\\).\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/823/_/diff#comment-207853882"}}
{"title": "Pinger was already running in 200hz rate. No need to increase that rate", "number": 824, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/824", "body": ""}
{"title": "Fix out of range", "number": 825, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/825", "body": "David saw this bug\n\n"}
{"title": "Feature/static ip behavior change for iphone", "number": 826, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/826", "body": "icmp ts for android 10 and linux\n\ndoesnt work for iphones at all\nnow wont work for static IP until OS classification is fixed to work in static IP\n\n\n\nRevert skip_additional_params for static ip\n\nDisable static cfr by default (need to enable it manually for raspberry pi testing)\n\n"}
{"title": "fix typo in aruba_agent", "number": 827, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/827", "body": "ccplilot  ccpilot\nand some other documentation that should be removed"}
{"title": "Feature/static csi ongoing training", "number": 828, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/828", "body": "The goal is to gather CFRs for devices and then build a model when disconnecting, if possible.\nSome limitations:\n\nThe AP supports gathering CFR for up to 10 devices, though more than 10 can be connected.\nNewly connected devices have higher priority of CFR gathering\n\nSo the priority scheme is basically to give priority to connecting devices and then for the remaining slots, select random devices\nChangelog:\n\n\nRefactor monitor priority to support the above. It now has state\n\nNeed to remove old cooperation code\n\n\n\nIn disconnection, try to build a static CFR model\n\n\nUnrelated:\n\nFix radiotap logging\n\n"}
{"comment": {"body": "what it adds?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/828/_/diff#comment-207992833"}}
{"comment": {"body": "can you add the time for differential update", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/828/_/diff#comment-207992931"}}
{"comment": {"body": "Correct headers for wireshark for correct packet display", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/828/_/diff#comment-207993127"}}
{"comment": {"body": "What do you mean?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/828/_/diff#comment-207993294"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} we haven\u2019t merged the differential devices update and get from ikeja branch.. dismiss my comment", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/828/_/diff#comment-208032353"}}
{"title": "Updated rnd.yml and server_list with the new server to sdi", "number": 829, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/829", "body": "We did the setup today with Tim for the new agent + server to singledigits, updating our servers list with it"}
{"title": "Gilad/CCP-46 ap agent watchdog", "number": 83, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/83", "body": "\n\nRestart streamers when CFR rate is too low (except when we dont need to monitor anyone)\nKill pingers when device disconnects\nTrack CFR recordings across runs.\nSome refactoring\n\n"}
{"title": "DHCP_GOT_HOSTNAME shouldn't work when in static IP", "number": 830, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/830", "body": "Issue raised from \nsince dhcp is not expected when in static ip"}
{"title": "management hb stats", "number": 831, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/831", "body": ""}
{"title": "odhcpd no limits", "number": 832, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/832", "body": "Purpose: modify the APs odhcpd (DHCPv6 server) to send RA (router advertisement) in a smaller interval than 3 seconds, to improve responsiveness of the system.\nThe code (and the standard  ) forbid the maximum value to be less than 4, and the minimum value is derived from the maximum value.\nI have modified the odhcpd version that comes with the QSDK and removed the limit for the maximum value. The minimum value is 0.75 of the maximum value.\nThe setting now would have the RA interval to be in 0.75-1 seconds range. Changing the ra_maxintervalto 2 would change the interval to 1.5-2 seconds.\nAnyway, to change this, change cwd to /q2/qca-networking-2019-spf-11-0_qca_oem.git/qsdk/build_dir/target-arm_cortex-a7_musl-1.1.16_eabi/odhcpd-2016-10-09/\nModify the file src/router.c lines circa 430 and compile with:\nC_INCLUDE_PATH=/q2/qca-networking-2019-spf-11-0_qca_oem.git/qsdk/staging_dir/target-arm_cortex-a7_musl-1.1.16_eabi/usr/include/:$C_INCLUDE_PATH make.\nThe new file would be in .\nThere has got to be a better way, though."}
{"comment": {"body": "In odhcpd the sky is the limit :sunglasses: \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/832/_/diff#comment-208445013"}}
{"title": "Device type/os identification on static IP + better static IP identification flow", "number": 833, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/833", "body": "Including:\n\nIdentification of device os/type by captive portal access or other parameters.   \nAdd parsing of SSDP packet to identify type\nDo not wait timeout to detect whether it is a static IP\nAdd UI display for static IP mode\n\n"}
{"comment": {"body": "Can these fields be possibly `None`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/833/_/diff#comment-208730997"}}
{"comment": {"body": "Should not be None but you can never know..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/833/_/diff#comment-208742361"}}
{"title": "Fix mandatory condition bad comparison", "number": 834, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/834", "body": ""}
{"title": "reduce give up timeout and once give up logic per session", "number": 835, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/835", "body": ""}
{"comment": {"body": "I don\u2019t think that 6 seconds is enough, but we\u2019ll see", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/835/_/diff#comment-208750556"}}
{"title": "Don't use extended caps for caps model since its not that important", "number": 836, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/836", "body": "Issue raised in bugs  "}
{"title": "Added more server metrics", "number": 837, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/837", "body": "This PR adds useful information that we can access easily and monitor.\nThis is a good base to start with, that will continue to grow as we work on it.\n\nFrom now on, well try our best to represent each feature and logic we add with a matching metric."}
{"comment": {"body": "a better pattern would be to just count devices based on their `session.os_classification_group` property.\n\nyou can init a dict `a = defaultdict(int)` and just `a[session.os_classification_group.name + \"_devices_count\"] += 1`.\n\n\\(I\u2019m aware of the android hiccup.\\)\n\nand then just dump the dict with `**a`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/837/_/diff#comment-208749845"}}
{"comment": {"body": "Great start!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/837/_/diff#comment-208749851"}}
{"comment": {"body": "I\u2019d add the source port as well, since a single agent can connect via multiple ports", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/837/_/diff#comment-208749976"}}
{"comment": {"body": "Love the dashboard name, btw. Guardian Angel.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/837/_/diff#comment-208848326"}}
{"title": "Feature/remove static ip specific flow", "number": 838, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/838", "body": "Static IP changes:\n\nRemove static ip-specific conditions (skip_additional_param_matching)\n\n\n\nPEP 8 fixes\n\n\n"}
{"title": "Rename Smartphone -> Mobile Device in UI", "number": 839, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/839", "body": "This is done because we cannot distinguish a smartphone from a tablet, as both are mobile devices."}
{"title": "Packetout", "number": 84, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/84", "body": "Do we call it timeout or packetout?"}
{"title": "Static location changes", "number": 840, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/840", "body": "Minimum of packets for model is 100\nEnable static CFR by default\n\n"}
{"title": "Remove asizeof from logging since it took several seconds to calculate sizes of session variables", "number": 841, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/841", "body": "During training/classification, the session object would hold very complicated objects (such as decision_log) which are very large.\nRunning asizeof on session would take a lot of time (several seconds) to calculate size.\nThis would block the data path and not process new packets, causing the packet queue spikes.\nThe resolution would be to remove the asizeof for now.\nExample profiling:\n\n"}
{"title": "Initial device model identification by caps (aka Phyngerbank)", "number": 842, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/842", "body": "Another small change in this PR: - Add DNS hostname as captive portal identification.\n"}
{"comment": {"body": "did you delete `coarse_levl_id_structs.py`? Bitbucket is showing a weird diff", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/842/_/diff#comment-*********"}}
{"comment": {"body": "It was renamed to structs.py and edited.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/842/_/diff#comment-*********"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} right. I was mistaken with `ccpilot/structs.py`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/842/_/diff#comment-*********"}}
{"title": "Catch exception in static_cfr as seen in bug CCP-608", "number": 843, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/843", "body": "At least dont crash"}
{"title": "RPi flow changes", "number": 844, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/844", "body": "Static CFR for RPi only\nPrioritize RPis for CFR gathering - make sure they are always gathered, even when they are already classified\n\n"}
{"title": "Device IP feature revival", "number": 845, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/845", "body": "In many home & office networks, the dhcp lease time is quite long, and sometimes lasts even more than a week. After our integration with singledigits, we saw that their lease time is very small (matter of minutes), and devices kept changing their IP addresses. For that reason, they started getting reused addresses of each other quite soon, so we had to remove the feature of comparing the IP address to match between devices.\nThis PR brings this feature back, with the following logic:\nIf a device connected in the time gap of another device dhcp lease, and they share the same IP address - we can use that as a feature. Otherwise, AKA the lease is over, we cant trust this address as a matching, because it might be the same address that is reused for another device."}
{"comment": {"body": "We can implement this with a compare function provided with the feature \\(instead of `cmp_exact`\\).\n\nSee if you can reduce the complexity of the code with removing duplicated code", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/845/_/diff#comment-209124685"}}
{"comment": {"body": "Will do. I\u2019ll work on it later this week, right now QA really needs it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/845/_/diff#comment-209171246"}}
{"title": "Reduce NetBIOS paring to be more efficient", "number": 846, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/846", "body": "This PR comes to be more efficient and reduce the number of times we go over our cache to update the NetBIOS Transaction ID value. Its very similar to the logic we added to the ICMP ts feature - instead of going over the cache all the time, do it in 30 seconds time interval (and in the very first seconds of the connection).\n"}
{"title": "Copy only relevant files in router setup", "number": 847, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/847", "body": ""}
{"title": "Add entry for PHYngerbank for PI without VHT caps", "number": 848, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/848", "body": ""}
{"title": "Enable pcap recording by default", "number": 849, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/849", "body": ""}
{"title": "Fixed some minor comments", "number": 85, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/85", "body": "Some very minor fixes regarding forgotten comments from a previous PR."}
{"title": "Feature/probe req time grid", "number": 850, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/850", "body": "added probe requests grid model that is searching for the phase, within a 1 minute grid, of a device's background probe request scanning. Added the option to get a preassoc cache packet list by subtype key, also added external visibility for consecutive internal swap caches."}
{"title": "bugfix/seq_num_info_attempted_update_when_not_declared", "number": 851, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/851", "body": "seq num info is only extracted when rpi now, parameter update on classification decision should act accordingly. Otherwise data doesnt exist."}
{"title": "Exception fix: transaction_id -> transaction_ids", "number": 852, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/852", "body": "The log printed the length of the None variable instead of the list of values"}
{"title": "Feature/ap logs and metrics", "number": 853, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/853", "body": "Introduction\n\n\nCreate infra to collect logs and metrics from the AP\n\nCollectD is used for metrics gathering. Its sending the metrics to the servers telegraf, which in turn ships to influxDB in monitoring.levl.tech\nLogs are gathered from the system logs and from the ap_agent.py into the servers logstash\n\n\n\nDetails\n\n\nCollectD v5.4.2 was built in qca-networking-2019-spf-11-0_qca_oem.git/qsdk\n\n\nAlong with some plugins:\n\nthermal\ncpu\ninterface\nmemory\ndf\ndisk\nnetwork\nvmem\n\n\n\nInfo about plugins: \n\n\nCollectD configuration is in /etc/collectd.conf\n\nCurrent basic usage gathers some memory and CPU metrics. We might need to expand that\n\n\n\n\n\nall.sh configures that target server (for metrics and logs) every time\n\n\n"}
{"comment": {"body": "@{5b41d9de10d57114135eca66} what is the resolution of collectd collection interval?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/853/_/diff#comment-209237023"}}
{"comment": {"body": "30 seconds. It\u2019s in the `collectd.conf` config file as `Interval`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/853/_/diff#comment-209237595"}}
{"title": "iPhone to not match RPIs", "number": 854, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/854", "body": ""}
{"comment": {"body": "does this sufficient? we can also strengthen with other classifications \\(akanot apple, not android and etc..\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/854/_/diff#comment-209398846"}}
{"comment": {"body": "Will do in a later PR", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/854/_/diff#comment-209407653"}}
{"title": "Bugfix CCP609 - Do not insert old legacy Auth events into queue", "number": 855, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/855", "body": ""}
{"title": "Bugfix CCP-610: Generate CFR model only once per connection attempt and only for RPIs", "number": 856, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/856", "body": ""}
{"comment": {"body": "I disagree with this scheme.  \nThe agreed scheme was that we build a model with the most packets with can use.\n\nAn alternative solution is to build a model only when requested to use it, when doing the actual decision in `_coarse_id_decision`. But follow the number of packets as a mandatory/preliminary condition  \n", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/856/_/diff#comment-209410092"}}
{"comment": {"body": "You can\u2019t just use \u201cthe most packets\u201d available when you have a computation that takes so much CPU and dependent directly on the number of packet. We will set a maximum packet limit anyway.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/856/_/diff#comment-209412642"}}
{"comment": {"body": "You can count the number of packets that it passed a minimum threshold and then build the model only when necessary", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/856/_/diff#comment-209415330"}}
{"comment": {"body": "It\u2019s already limited to 500 packets", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/856/_/diff#comment-209415429"}}
{"title": "Highest score matching based on weighted sum", "number": 857, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/857", "body": "Update feature weights and the mechanism to find highest priority device (after all of the filtering) - using sum of weights (weights were chosen accordingly). Also changed the choice in the event of an overall tie to be non-random: device with latest creation time will be chosen (questionable systemic choice which we should discuss)."}
{"comment": {"body": "nothing possibly could go wrong :man_shrugging:", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/857/_/diff#comment-209408381"}}
{"comment": {"body": "A log here that prints the total score for each candidate won\u2019t hurt", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/857/_/diff#comment-209409666"}}
{"comment": {"body": "The scoring system is better than the current one; The tie breaking is very problematic of course.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/857/_/diff#comment-209409969"}}
{"comment": {"body": "Sure", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/857/_/diff#comment-209410049"}}
{"comment": {"body": "Sure", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/857/_/diff#comment-209410198"}}
{"comment": {"body": "Oh nvm there\u2019s a log 2 lines below in case the score is non-zero", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/857/_/diff#comment-209410468"}}
{"title": "Add sleep of 10 seconds", "number": 858, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/858", "body": "Thread was busy waiting"}
{"title": "Move all agent out of ccpilot so ccpilot will not appear on agent/ap", "number": 859, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/859", "body": ""}
{"comment": {"body": "move this to `common/__init__.py` instead? It\u2019ll otherwise clutter the root directory too much", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/859/_/diff#comment-209484829"}}
{"title": "Feature/CCP-23 tee based tests", "number": 86, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/86", "body": "Added regression tests based on tee replaying.\n  Temporarily added some pickles (~10MB), will be moved to git-lfs later.\nDeal with prune collision and added option to remove image\nUpdated recording to support current models data requirements.\n\n"}
{"title": "Fix RPI disconnection network", "number": 860, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/860", "body": ""}
{"title": "Feature/static cfr model built in decision", "number": 861, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/861", "body": "The model building was taking too long, so we did it once per device. But we want to use as many packets as possible, then its best to build the model when concluding the decision.\nChangelog:\n\nMove static cfr model building to decision stage\nFix syntax in metrics_db_mediator.py\n\n"}
{"title": "extend bonjour uid by 1 byte", "number": 862, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/862", "body": ""}
{"comment": {"body": "Great catch! Have you verified the fix?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/862/_/diff#comment-*********"}}
{"comment": {"body": "Yes, it works for the devices I checked.\n\nAlso, there is a draft protocol about the Owner option and what we see match the \"Compact EDNS0 'Owner' Option\" with len=18 from [https://tools.ietf.org/html/draft-cheshire-edns0-owner-option-01](https://tools.ietf.org/html/draft-cheshire-edns0-owner-option-01)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/862/_/diff#comment-*********"}}
{"title": "RPI alg fixes - part I", "number": 863, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/863", "body": ""}
{"comment": {"body": "add `device_icmp6_mclr` as well?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/863/_/diff#comment-209695648"}}
{"comment": {"body": "Not really relevant in the RPI usecase", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/863/_/diff#comment-209695923"}}
{"comment": {"body": "What is nice about NS in RPI is that are persistent through reboot \\(per SSID\\) :\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/863/_/diff#comment-209696889"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} \n\n![](https://bitbucket.org/repo/8X5z9dk/images/160922999-1585315264_noiceb99.gif)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/863/_/diff#comment-209702495"}}
{"title": "API KEY for management endpoints", "number": 864, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/864", "body": "Added initial authentication with API KEY for management flask.\nAll management routes are protected with auth decorator checking header Client-Token for which generated several uuid keys and mapped for now in management flask.\ntriggering and telegraf mechanism updated accordingly.\nImproved several logics of alerts and emailing"}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/3160112976-Screen%20Shot%202021-03-03%20at%2023.39.30.png)\nExample of postman configuration for the API Key", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/864/_/diff#comment-209609287"}}
{"title": "Static CFR check for None", "number": 865, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/865", "body": "some mich found in testing"}
{"title": "Add charter's AP config", "number": 866, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/866", "body": ""}
{"comment": {"body": "Can you change the SSID names to something explicit?\n\nLike \u201cLEVL Eval 5GHz\u201d and \u201cLEVL Eval 2.4GHz\u201d", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/866/_/diff#comment-209717967"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/866/_/diff#comment-209721473"}}
{"title": "Fix ongoing updates", "number": 867, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/867", "body": "ICMP TS ongoing - don't update the model is it's None\n  This behavior happened in  but this PR is not the solution for that bug.\n  Shouldnt update the model anyway if its None\nOngoing updates (icmp_ts, netbios_transaction) should read from write cache since thats the target to writing after JUST_CONNECTED state\n\n"}
{"title": "Feature/timeout from device activity detection", "number": 868, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/868", "body": "Start detection timers only after we get an ipv4 indication and not from connection\n\nThis is to mitigate sessions where the wifi is busy and the device cant send ipv4 messages until some time passed\n\n\n\nIPv4 information may come from DHCP or ARP messages\n\n\n"}
{"title": "Feature/timeout from device activity detection", "number": 869, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/869", "body": "Start detection timers only after we get an ipv4 indication and not from connection\n\nThis is to mitigate sessions where the wifi is busy and the device cant send ipv4 messages until some time passed\n\n\n\nIPv4 information may come from DHCP or ARP messages. Its assumed that if we get an ARP packet without DHCP, then its a static IP scenario.\n\nIn that matter, the DHCP timeout is now redundant, but itll be removed in another PR\n\nDHCP timestamp is from the first ACK with the last transaction ID\n\nSince devices may send multiple DHCP requests with the same transaction ID, but the first one chronologically indicates the connection best\n\n\n\nARP timestamp is the first ARP seen in this connection\n\n\n\n\nNote:\n\nReference clock for IPv4 connection is changed for netbios transaction ID and IPv4 lease time changed to IPv4 detection since I believe that its more related to that timestamp. Its open to discussion\n\n"}
{"title": "Fixed tests failed by CCP-46", "number": 87, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/87", "body": ""}
{"title": "fix agent service", "number": 870, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/870", "body": ""}
{"title": "Remove pycache dirs", "number": 871, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/871", "body": "This slightly reduces the image size and allows the squashfs to be valid again. A more comprehensive solution should be provided to distance us from partition size limits."}
{"title": "Mich/stability and performance", "number": 872, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872", "body": "Add parsing of MacOS types of bonjour UID to support also the unique identification of macbooks in static IPv4/IPv6 scenarios.\nDisabled background capturing of CFR. This is extermely CPU intensive and clogs the system. This is very noticeable when there are a lot of devices in the system and the system approaches 100% cpu. We might want to add something to sample the CFR of devices once in a while. \nIncreased CFR read buffer as the buffer size was way too small. This improves dramatically the CPU usage of CFR parsing but its still not enough.\nMerge and fixes in IPv4 assignment detection code and in the gave up logic. (@{5b41d9de10d57114135eca66} , This PR also includes the contents of PR-869)\nOther optimisations for better packet flow in data path\n\n"}
{"comment": {"body": "It disables the RPi static location ongoing feature", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210250140"}}
{"comment": {"body": "what the reason for this change?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210281506"}}
{"comment": {"body": "by how many cpu cycles does this improve?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210282365"}}
{"comment": {"body": "What about removing the items that are too old? For faster iterations on next calls", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210282610"}}
{"comment": {"body": "Without it, we don\u2019t have the DHCP ACK in the cache", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210283244"}}
{"comment": {"body": "It saves around 30% of the cpu time of parsing the packet in inserting it to the queue. From 23 usec to 16 usec.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210283322"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} We\u2019ve always had DHCP ACKs, also in AP mode.  \nOtherwise the following code in `coarse_levl_id.py` wouldn\u2019t work:\n\n![](https://bitbucket.org/repo/8X5z9dk/images/240077822-image.png)\nAs far as I remember, this code used to handle the case when the switch changed the source mac address.  \nThis case is not relevant here, no?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210285903"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} It\u2019s actually a revert of a previous commit that made the ACK not appear any more in the caches:\n\n41a6293c8c7478bffb7aa04485780c1fb72e5a6b", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210286166"}}
{"comment": {"body": "Ah", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210286238"}}
{"comment": {"body": "Will do it in a later stage. Wanted to keep it simple for now.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/872/_/diff#comment-210288589"}}
{"title": "Charter RC1", "number": 873, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/873", "body": "Merge of PR-864 and PR-872"}
{"title": "Feature/filtfilt", "number": 874, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/874", "body": "Add server_hostname tag for collectd data\n\nSo that we can differentiate between different APs (as they all report the same hostname)\n\n\n\nFilter out ICMP packet in AP side\n\nThus removes ~75% of redundant data\n\n\n\nDon't deepcopy in ICMP TS ongoing update\n\nIt takes a lot of time to do that\n\n\n\n"}
{"comment": {"body": "It\u2019s a strange syntax.\n\nI would assume that we should do something like \\(icmp and icmp\\[icmptype..\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/874/_/diff#comment-*********"}}
{"comment": {"body": "Nope. Valid: [https://hackertarget.com/tcpdump-examples/#icmp-type](https://hackertarget.com/tcpdump-examples/#icmp-type)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/874/_/diff#comment-*********"}}
{"title": "Revert commit 6ccc7e7 for removal of local address filtering", "number": 875, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/875", "body": "Devices can solicit each other and we can mistake that for test for address occupancy"}
{"title": "Don't access vector if it's empty", "number": 876, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/876", "body": "David saw this exception:\nclassifier_1      |   File \"/usr/local/lib/python3.8/threading.py\", line 932, in _bootstrap_inner\nclassifier_1      |     self.run()\nclassifier_1      |   File \"/usr/local/lib/python3.8/threading.py\", line 870, in run\nclassifier_1      |     self._target(*self._args, **self._kwargs)\nclassifier_1      |   File \"/root/src/ccpilot/processes/data_processor.py\", line 1466, in process_data\nclassifier_1      |     ret_periodic_state = state.periodic_state_management(transient_id, state_enum_to_state)\nclassifier_1      |   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 1002, in periodic_state_management\nclassifier_1      |     state_change: Optional[Tuple[ClassificationStatus, None]] = self._periodic_state_management_internal(user, state_enum_to_state)\nclassifier_1      |   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 643, in _periodic_state_management_internal\nclassifier_1      |     ret: ExtractInfoReturnValue = extract_info_from_caches(session_preassoc_caches)\nclassifier_1      |   File \"/root/src/ccpilot/processes/models/common/coarse_levl_id.py\", line 244, in extract_info_from_caches\nclassifier_1      |     probe_req_grid = _extract_probe_request_grid(session_preassoc_caches[\"Dot11MngCache\"])\nclassifier_1      |   File \"/root/src/ccpilot/processes/models/common/coarse_levl_id.py\", line 223, in _extract_probe_request_grid\nclassifier_1      |     return extract_pr_grid_model([packets])\nclassifier_1      |   File \"/root/src/probe_req_grid_fingerprinting/__init__.py\", line 18, in extract_pr_grid_model\nclassifier_1      |     ts_vec_filtered.append(ts_vec[-1])\nclassifier_1      | IndexError: index -1 is out of bounds for axis 0 with size 0\nThis fix should mitigate such out of bounds access"}
{"comment": {"body": "Can you put a try/catch around the entire function so it we don\u2019t crash in any other erorr?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/876/_/diff#comment-210300591"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/876/_/diff#comment-210302416"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} This should have been mitigated, thanks\u2026 But there\u2019s also an underlying issue here - I didn\u2019t come across this exception because there are \\*always\\* probe request packets. Was this a wired connection?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/876/_/diff#comment-210378580"}}
{"comment": {"body": "It shouldn\u2019t be as charter demo should be wireless only. You\u2019re welcome to ask David", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/876/_/diff#comment-210384353"}}
{"title": "Static IP improvements", "number": 877, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/877", "body": "Extract packets also from IPv6 sources: DNS, mDNS (Bonjour)\n\nWe saw benefit from this when devices delayed IPv4 packets but sent IPv6 packets\n\n\n\nOnly do static IPv4 lookup for devices that send ARP reply\n\nDevices with bad internet configuration, which we require to be valid, dont send ARP replies. A follow up PR will disconnect these devices altogether\n\n\n\nFix logstash.tar.gz\n\nGit LFS issues. Contents havent changed\n\n\n\nStaticmethod fixes\n\n\n"}
{"title": "throw devices and conenctions metrics", "number": 878, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/878", "body": "For cases when devices cant connect to a network, we dont want them to remain session forever and kick them after 30 seconds\nAdded metrics for connections decision and added to Grafana panel\n\n"}
{"title": "Bugfix CCP-617 - display known infromation when fingerbank onlu use MAC Address for identification", "number": 879, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/879", "body": "Includes:\n\nBug fix for CCP-617 - Do not display false information when only partial fingerprint data is available\nExtend history logs with more device type data\n\n"}
{"comment": {"body": "`banck` - is it a fusion of `bank` and `ack`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/879/_/diff#comment-*********"}}
{"title": "Fix bug when layer is none", "number": 88, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/88", "body": "In some cases d11.getlayer(Raw) returns none, we shouldnt throw, just ignore the packet."}
{"title": "CCP-621 - Removing the DHCP_GOT_HOSTNAME from the solid identifiers in ap mode", "number": 880, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/880", "body": ""}
{"title": "remove ccpilot comments", "number": 881, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/881", "body": "Also force pycache removal to avoid errors when none exists"}
{"title": "Feature/bonjour is a set", "number": 882, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/882", "body": "Weve seen that the bonjour UID may be a list of mac addresses and not just a single value.\nItll be a better scheme to treat them as such and try to intersect between then, just like ICMP NS and ICMP MCLR features.\nChangelog:\n\nBonjour UID is now a list and not a single value\nMatching is now with intersection\nAdded Ongoing bonjour\n\n"}
{"comment": {"body": "I made `serialize_and_update_device_field` for updating a single complex type device field, you can use that next time.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/882/_/diff#comment-210452946"}}
{"title": "fix charter domain", "number": 883, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/883", "body": ""}
{"title": "refresh rate in flask to 1 sec", "number": 884, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/884", "body": ""}
{"title": "TCP TS iOS support with 1 packet, s it always static - NOT TO MERGE YET", "number": 885, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/885", "body": "Enable TCP TS for iOS with 1 packet only\nFix the below exception as well:\n\nclassifier_1      | 2021-03-08 20:11:15.336  [metrics_db_mediator.py:86] Failed to send DB-based metrics: max() arg is an empty sequence\nclassifier_1      | Traceback (most recent call last):\nclassifier_1      |   File \"/root/src/ccpilot/metrics_db_mediator.py\", line 68, in write_decision_logs_metrics\nclassifier_1      |     matching_parameters_dict, average_decision_time_last_day, max_decision_time_last_day = self.get_stats_from_decision_logs(matching_parameters_dict)\nclassifier_1      |   File \"/root/src/ccpilot/metrics_db_mediator.py\", line 30, in get_stats_from_decision_logs\nclassifier_1      |     max_decision_time = max([decision_row.decision_duration for decision_row in decision_rows if decision_row.decision_duration is not None]) if len(decision_rows) > 0 else 0\nclassifier_1      | ValueError: max() arg is an empty sequence\n"}
{"title": "Charter/bugfixes", "number": 886, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/886", "body": "Add vendor OUIs to RPI identification. This should fix CCP-623\nRevert to a single bonjour UID per packet\n\n"}
{"comment": {"body": "You need to update `ccpilot/processes/models/mdns_ongoing.py` as well as it calls `process_bonjour_apple_uid` and expects a list as the output.\n\nIt doesn\u2019t crash since `str` supports `len` and iterations, but the flow would be corrupt", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/886/_/diff#comment-210665133"}}
{"comment": {"body": "Updated. Nice catch.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/886/_/diff#comment-210684851"}}
{"comment": {"body": "I agree about this change. Can you remove unused code where tracking is also started? Like in like 1272", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/886/_/diff#comment-210687870"}}
{"comment": {"body": "Done.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/886/_/diff#comment-210691835"}}
{"title": "Set and update dhcpv6 duid values outside of just connected", "number": 887, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/887", "body": ""}
{"comment": {"body": "what\u2019s the reason for demorgen here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/887/_/diff#comment-210666461"}}
{"comment": {"body": "I wanted to include training/classification and it\u2019s shorter like this.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/887/_/diff#comment-210671475"}}
{"comment": {"body": "let\u2019s remove the DUID from mandatory for static connections only", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/887/_/diff#comment-210673973"}}
{"title": "Now waiting for an association packet before moving on with the connection", "number": 888, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/888", "body": "When looking at the logs from charter.demo.levl.tech, an interesting thing happen multiple times: \nWe got information about a device that was connected from the AP tree, and its association packet arrived half a second afterwards. Right in the middle of that time gap, we began our logic in just_connected_state and extracted data from the cashes. Since no association packet of the current connection arrived yet, we did not filter the data and in fact got data that belonged to all previous connections. That caused us to look at old ARP messages and mistakenly think they belong to the current session  We set wrong time values and set session.ipv4_detection_timestamp field to be a lot larger than it should be.\nThe solution to that case is to wait with our logic until we find an association packet of the current connection. We do have a timeout that disconnected a device if we don't get the caps after two seconds, but in such cases that timeout is not enough because we did get the timeout, we just got it a bit late."}
{"title": "Fix always disconnecting devices after session restore", "number": 889, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/889", "body": "Add missing field in session restore sync"}
{"title": "Choose Reflections", "number": 89, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/89", "body": "Add a Bernoulli choice of reflections so that number of reflections is independent of sample rate.\n"}
{"title": "Fix double model transition static IP and DHCP", "number": 890, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/890", "body": ""}
{"title": "Don't process disassociating devices", "number": 891, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/891", "body": "When we decide to disassociate devices, it takes time for the message to the get to the AP and actually disconnect the device. In the meantime, we keep processing that device (and often decide to disconnect it again).\nSince every disassociation decision we log it, we have duplicated of disassociation decisions.\nBest is to ignore events from disassociating devices, to lessen processing and reduce logs."}
{"title": "Change IPv4 detection time source", "number": 892, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/892", "body": "Use the server timestamp when event received in server.\nCreate the server_ts as soon as possible and not after processing of the packet\n\n"}
{"title": "remove 24h filter in UI", "number": 893, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/893", "body": ""}
{"title": "Bugfix CCP-629 - prevent stravation of data processor by periodic state management", "number": 894, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/894", "body": ""}
{"title": "\"Router Unreachable\" Monitoring", "number": 895, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/895", "body": "Change router unreachable logic: one live interface is enough for the router to be considered reachable; pop expired interfaces after 30 days (as we've seen new interface keys (randomized macs) replacing active ones on reboots). Remove eth1 from br-demo on cypresses for now."}
{"title": "Fix connectivity issues for S10/S20 with Android11", "number": 896, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/896", "body": ""}
{"title": "Add google's dnsv6 to AP", "number": 897, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/897", "body": "This should help the case where the device uses DHCPv6 but static IPv4 and it wants to access the internet"}
{"comment": {"body": "Do you really see the DNS now get\u2019s this address?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/897/_/diff#comment-210997365"}}
{"comment": {"body": "Yup. From phone connected to AP traffic:\n\n![](https://bitbucket.org/repo/8X5z9dk/images/**********-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/897/_/diff#comment-*********"}}
{"title": "History logs update for static IP scenario", "number": 898, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/898", "body": "History logs use the same function for model inference as UI\n\nPreviously, theyd output - in device type for static IP devices (due to no Fingerbank information)\n\n\n\nSome PEP8 cleanup\n\n\n"}
{"title": "time filter preassoc cache based on earliest assoc", "number": 899, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/899", "body": "Time-filter based on the earliest between the assoc and reassoc packet. This will solve the filtering problem in CCP-625. The actual issue is that we get very rapid disconnections and reassociations. The ap agent isnt quick enough to detect the disconnections and they arent reported to server. We could solve this by completing missed events from the log, for example, but this is a more comprehensive task.\n"}
{"title": "Added BLE smartlock app and script", "number": 9, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9", "body": "Added an android app for the BLE demo.\nDialog project still in bosch repo due to Circumstances."}
{"comment": {"body": "what\u2019s the purpose of keys.json?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9/_/diff#comment-*********"}}
{"comment": {"body": "It\u2019s a DB of keys", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9/_/diff#comment-143017173"}}
{"comment": {"body": "Is there a way to lock this version/commit with the appropriate commit of the bosch integration branch/commit?  \nI\u2019d suggest a submodule, but most people are against it and it does look like it\u2019ll clutter stuff", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9/_/diff#comment-143017981"}}
{"comment": {"body": "Not sure.\n\nIdeally I would want to extract it out of the bosch repo, but there\u2019s too much things and it would take forever.\n\nI guess we could tag identical tags across the repos after testing them.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9/_/diff#comment-143018187"}}
{"comment": {"body": "`Pod` folder and `[project_name].xcworkspace` should not be included in the git repo as they include dependencies which you download using `pod install`.\n\nYou **can** keep `Podfile.lock` as it specifies which versions you\u2019ve used while developing the app.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9/_/diff#comment-143258494"}}
{"comment": {"body": "please rename smartlock\\_firmware/nordic\\_local to something more indicative and hardware-related.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9/_/diff#comment-146443146"}}
{"comment": {"body": "What's more related to hardware than firmware?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9/_/diff#comment-146445804"}}
{"comment": {"body": "hardware revision. such as NRF52833", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9/_/diff#comment-146450938"}}
{"comment": {"body": "I see, sure", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/9/_/diff#comment-146470394"}}
{"title": "Gilad/less agressive agent reset", "number": 90, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/90", "body": "Made agent resets a tad less aggressive\nAdded info log\n\n"}
{"title": "Now updating the netbios transaction id when linking device to an existing session", "number": 900, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/900", "body": "Until now we were updating the NetBIOS Transaction ID as part of the extracted_parameters_dict when we created a new model. We need to update it when linking a device to an existing session as well."}
{"title": "update timezone", "number": 901, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/901", "body": ""}
{"title": "Release/charter poc", "number": 902, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/902", "body": "and google's dnsv6\nNow updating the netbios transaction id when linking device to an existing session\nPEP8 cleanup history logs use the same function for model inferrence as UI\ntime filter based on the earliest between the assoc and reassoc packet.\nStarting ongoing training for netbios transaction id from start_tracking() method\nupdate timezone\nadd charter fb key\n\n"}
{"title": "Windows 10 identification", "number": 903, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/903", "body": "We had some devices that were not identified as Windows devices and we didnt use the matching features as a result. session.device_record() is initialized and we didnt really got to the check of identification.os"}
{"comment": {"body": "Good catch!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/903/_/diff#comment-211275701"}}
{"title": "Guglielmo prep", "number": 904, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/904", "body": "Add Guglielmo server info\nFix Switch mode connections\n\n"}
{"title": "Do not identify MacOS without bonjour UID as a Win 10", "number": 905, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/905", "body": ""}
{"title": "Hotfix/netbios diff update", "number": 906, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/906", "body": "NETBIOS transaction diff update to 64\n"}
{"title": "Bugfix: ignore en5 mac address of bonjour UID of en5", "number": 907, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/907", "body": ""}
{"title": "Bugfix CCP-638: Try parsing more than a single NB packet", "number": 908, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/908", "body": ""}
{"title": "Some auxiliary file changes", "number": 909, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/909", "body": "Update ipq_config/*_env.sh files\nInclude restore_log.py to deserialize Decision log JSON files\n\n"}
{"title": "Save agent log to log file", "number": 91, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/91", "body": ""}
{"title": "Hotfix/no intenet quarantine", "number": 910, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/910", "body": "Add Quarantine devices that don't have any internet connectivity\nFix ecpetion on return\n\n"}
{"comment": {"body": "Device with no Internet connectivity will be added to uI with Quarantine state and can be removed manualy, or will be removed automatically on reconnect\n\n![](https://bitbucket.org/repo/8X5z9dk/images/3256922758-Screen%20Shot%202021-03-14%20at%2017.56.50.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/910/_/diff#comment-211860224"}}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/73551280-Screen%20Shot%202021-03-14%20at%2019.12.13.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/910/_/diff#comment-211864228"}}
{"title": "DHCP IPv4 match update", "number": 911, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/911", "body": "Enable IPv4 feature only if the device explicitly requested it (as seen as a missing field of DHCP server identifier field in DHCP Request)\nPEP8 fixes\n\n"}
{"title": "Feature/charter to master", "number": 912, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/912", "body": "and google's dnsv6\nNow updating the netbios transaction id when linking device to an existing session\nPEP8 cleanup history logs use the same function for model inferrence as UI\ntime filter based on the earliest between the assoc and reassoc packet.\nStarting ongoing training for netbios transaction id from start_tracking() method\nupdate timezone\nadd charter fb key\nEdited _is_windows_10() method to use the identification os correctly\nDo not identify MacOS without bonjour UID as a Win 10\nNETBIOS transaction diff update to 50\nNETBIOS transaction diff update to 64\nBugfix: ignore en5 mac address of bonjour UID of en5\nBugfix CCP-638: Try parsing more than a single NB packet\nrevert FB key to common\n\n"}
{"title": "Identify Apple devices by association vendor instead of bonjour", "number": 913, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/913", "body": "After identifying Windows device by mistake as an Apple device because they had a bonjour uid, identify Apple devices by their association vendor name"}
{"comment": {"body": "How did you come up with those OUI numbers? Did you check all the devices we have?\n\nI think it\u2019s kinda risky to do an exact match for the list of vendors. Why don\u2019t we just find the Apple OUI in the list?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/913/_/diff#comment-211868498"}}
{"comment": {"body": "Fixed to check if the profile is a subset", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/913/_/diff#comment-211870371"}}
{"comment": {"body": "Can you verify it identifies correctly our oldest Apple device in the lab \\(the old Macbook Air and iPhone 6\\)?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/913/_/diff#comment-211871106"}}
{"title": "Use all seen netbios transaction id range", "number": 914, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/914", "body": "Use first nb transaction id seen for comparison, and last seen for committed model"}
{"title": "Add API 1.2.2 with missing repsonse code 412 on device delete", "number": 915, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/915", "body": "\n"}
{"title": "IPv4 lease test will be per SSID", "number": 916, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/916", "body": "Devices that request previously owned IPv4 will request the same IP per SSID.\nSo need to split logic to check each SSID independently. Each device will now own a separate IPv4 lease per SSID, which is stored in a new column.\nAlso updated tags of metrics in stream_endpoint and buffered_file_write"}
{"comment": {"body": "Looking great :slight_smile: Did you update our Guardian Angel with the new data?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/916/_/diff#comment-212226350"}}
{"comment": {"body": "I\u2019ve updated Data recording Queue and Packets panels in Classifier Metrics dashboard", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/916/_/diff#comment-212235548"}}
{"title": "Handle NB Transaction ID Wraparound", "number": 917, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/917", "body": "Wraparound would have caused swapped transaction ID values (Thanks Grisha for noticing)"}
{"title": "CFR Recording", "number": 918, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918", "body": "Record all CFR to pcap. separate \"recorder\" from stream_endpoint"}
{"comment": {"body": "Were you able to open the CFR pcap files with Wireshark? Were the packets parsed OK?\n\nI assume that if we use custom plugins, we\u2019d need a way to distinguish these pcaps. Maybe with header `DLT_USER3`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212211113"}}
{"comment": {"body": "It\u2019s also redundant to have another module for pcap writing as buffered\\_file\\_writer is hardcoded to write with dpkt", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212225348"}}
{"comment": {"body": "Agree, we can reuse the pcap writer and enhance to support different parsers/outputs", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212239754"}}
{"comment": {"body": "Will do", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212248046"}}
{"comment": {"body": "I was able to open the files and see proper raw data per timestamp. The packets themselves obviously weren\u2019t parsed. I mean, parsed as ethernet. I\u2019ll use a different link layer type - why `DLT_USER3` of all?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212248907"}}
{"comment": {"body": "Because `DLT_USER1` is too obvious and `DLT_USER2` is de-facto used by Apple, so `DLT_USER3` seems available.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212249536"}}
{"comment": {"body": "I\u2019m afraid those changes break a lot of code.\n\nThe `RawCFRIterator` is used also by the agent \\(to get the rate of CFRs\\) and by the recording system. So by adding the additional parameter we break all of this code.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212261867"}}
{"comment": {"body": "Thanks. The parameter is no longer mandatory", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212269733"}}
{"comment": {"body": "We still need to import the buffered\\_file\\_writer in the agent.  \nMaybe we can make a callback or something like?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212270729"}}
{"comment": {"body": "on it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212271067"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/918/_/diff#comment-212283915"}}
{"title": "Adding API key for Guglielmo", "number": 919, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/919", "body": ""}
{"title": "Fixed call to thread.kill", "number": 92, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/92", "body": "Also fixes a keyerror in capfp and some pep8 violations"}
{"title": "Update the last seen when we update the last connected time, just in case", "number": 920, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/920", "body": "When running Guglielmo testing, we saw some devices that their last_seen value stayed un-set and we showed the following time in the UI:\n\nIf the connection is very fast, and we dont have to wait for more device-related packets to arrive, we might not get to update_last_seen and this field stays at its initial value (as happened above).\nThis PR handles this case, to update the last_seen when we update the last_connected value, just in case. The last_seen value will continue to update later on, depends on the device activity."}
{"title": "Release/charter poc", "number": 921, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/921", "body": "Add API 1.2.2 with missing repsonse code 412 on device delete\nIPv4 leases requests will be indepently tested per SSID\nWraparound would have caused swapped transaction ID values (Thanks Grisha for noticing)\nAvoid unnecessary shifts\nRecord CFR to pcap, separate \"recorder\" from stream_endpoint\nDecrease buffer size for cfr - fewer data, don't want to miss it\nChange ipv4_leases to ipv4_leases_store scheme to reuse code\nUpdate metrics of stream_endpoint and buffered_file_writer to include better tags\nForgot file to previouscommit\nRemove redundat pcap_recording, use custom link layer type for CFR\ncfr recording cfr_stream_parser is off by default\nRemove dependency of cfr_stream_parser in buffered_file_writer\n\n"}
{"title": "Guglielmo TZ set for Italy Bologna", "number": 922, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/922", "body": "\n\nGuglielmo TZ set for Italy Bologna\n\n"}
{"title": "Fingerbank query: ignore random mac address", "number": 923, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/923", "body": "Fix for CCP-645: \nSome PEP8 fixes\n\n"}
{"comment": {"body": "It\u2019s one of the downsides of using a crowd-sourced DB, sometime you have entries that make no sense :upside_down: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/923/_/diff#comment-*********"}}
{"comment": {"body": "That\u2019s why Apple should enter this market and do some validation to those DB entries.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/923/_/diff#comment-*********"}}
{"title": "Fix pr grid model deserialization from DB", "number": 924, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/924", "body": "Tried to concatenate list (loaded from db after json dumps->loads) with the object which is tuple\n"}
{"title": "Try to mitigate exception in management_processor.py", "number": 925, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/925", "body": "If there's an exception of access to shared_state, do retry\n\nTry to workaround this exception:\n\u001b[36mclassifier_1      |\u001b[0m ----------------------------------------\n\u001b[36mclassifier_1      |\u001b[0m Exception happened during processing of request from ('*********', 51568)\n\u001b[36mclassifier_1      |\u001b[0m Traceback (most recent call last):\n\u001b[36mclassifier_1      |\u001b[0m   File \"/usr/local/lib/python3.8/socketserver.py\", line 316, in _handle_request_noblock\n\u001b[36mclassifier_1      |\u001b[0m     self.process_request(request, client_address)\n\u001b[36mclassifier_1      |\u001b[0m   File \"/usr/local/lib/python3.8/socketserver.py\", line 347, in process_request\n\u001b[36mclassifier_1      |\u001b[0m     self.finish_request(request, client_address)\n\u001b[36mclassifier_1      |\u001b[0m   File \"/usr/local/lib/python3.8/socketserver.py\", line 360, in finish_request\n\u001b[36mclassifier_1      |\u001b[0m     self.RequestHandlerClass(request, client_address, self)\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/threadhttp/__init__.py\", line 19, in __init__\n\u001b[36mclassifier_1      |\u001b[0m     super().__init__(*args, **kwargs)\n\u001b[36mclassifier_1      |\u001b[0m   File \"/usr/local/lib/python3.8/socketserver.py\", line 720, in __init__\n\u001b[36mclassifier_1      |\u001b[0m     self.handle()\n\u001b[36mclassifier_1      |\u001b[0m   File \"/usr/local/lib/python3.8/http/server.py\", line 427, in handle\n\u001b[36mclassifier_1      |\u001b[0m     self.handle_one_request()\n\u001b[36mclassifier_1      |\u001b[0m   File \"/usr/local/lib/python3.8/http/server.py\", line 415, in handle_one_request\n\u001b[36mclassifier_1      |\u001b[0m     method()\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/threadhttp/__init__.py\", line 51, in do_POST\n\u001b[36mclassifier_1      |\u001b[0m     json_dump = self.json_func(self.client_address, parts, params, post_body).encode('utf-8')\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/management_endpoint.py\", line 197, in get\n\u001b[36mclassifier_1      |\u001b[0m     return json.dumps(resolver(json.loads(body), client_address)) if resolver is not None else json.dumps({})\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/management_processor.py\", line 100, in connection_response_resolver\n\u001b[36mclassifier_1      |\u001b[0m     auth_resolve = authentication_monitor()\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/management_processor.py\", line 75, in authentication_monitor\n\u001b[36mclassifier_1      |\u001b[0m     return {\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/management_processor.py\", line 75, in dictcomp\n\u001b[36mclassifier_1      |\u001b[0m     return {\n\u001b[36mclassifier_1      |\u001b[0m RuntimeError: OrderedDict mutated during iteration\n\u001b[36mclassifier_1      |\u001b[0m ----------------------------------------\nIt happens often when devices connect/disconnect. \nThis is due to accessing a variable shared between two threads. I think this solution is cheaper than adding a mutex.\n"}
{"title": "CCP-646: Fix loading of object", "number": 926, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/926", "body": "Handle also json dump of None\n"}
{"title": "Verify shapes in CFR model building", "number": 927, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/927", "body": "Handle the exception of:\n\n./2021-03-15--14-28-19/docker_output.logs-\u001b[36mclassifier_1 |\u001b[0m 2021-03-16 07:05:27.587  [static_cfr.py:157] Exception caught in static CFR\n./2021-03-15--14-28-19/docker_output.logs:\u001b[36mclassifier_1 |\u001b[0m Traceback (most recent call last):\n./2021-03-15--14-28-19/docker_output.logs-\u001b[36mclassifier_1 |\u001b[0m File \"/root/src/ccpilot/processes/models/common/static_cfr.py\", line 155, in static_cfr_build_model\n./2021-03-15--14-28-19/docker_output.logs-\u001b[36mclassifier_1 |\u001b[0m df = np.asarray([np.asarray(list(get_chains.get_chains(packet))) for packet in cfrs])\n./2021-03-15--14-28-19/docker_output.logs-\u001b[36mclassifier_1 |\u001b[0m File \"/usr/local/lib/python3.8/site-packages/numpy/core/_asarray.py\", line 102, in asarray\n./2021-03-15--14-28-19/docker_output.logs-\u001b[36mclassifier_1 |\u001b[0m return array(a, dtype, copy=False, order=order)\n./2021-03-15--14-28-19/docker_output.logs-\u001b[36mclassifier_1 |\u001b[0m ValueError: could not broadcast input array from shape (2,53) into shape (2,)\n\n"}
{"title": "Release/charter poc", "number": 928, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/928", "body": "Fingerbank query: ignore random mac address\nTried to concatenate list (loaded from db after json dumps->loads) with the object which is tuple\nIf there's an exception of access to shared_state, do retry\nDecorate with more retries Log sleep events and post a metric about it\nHandle also json dump of None\nVerify shapes in CFR model building\n\n"}
{"title": "Master", "number": 929, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/929", "body": "Add API 1.2.2 with missing repsonse code 412 on device delete\nIPv4 leases requests will be indepently tested per SSID\nWraparound would have caused swapped transaction ID values (Thanks Grisha for noticing)\nAvoid unnecessary shifts\nRecord CFR to pcap, separate \"recorder\" from stream_endpoint\nDecrease buffer size for cfr - fewer data, don't want to miss it\nChange ipv4_leases to ipv4_leases_store scheme to reuse code\nUpdate metrics of stream_endpoint and buffered_file_writer to include better tags\nForgot file to previouscommit\nRemove redundat pcap_recording, use custom link layer type for CFR\ncfr recording cfr_stream_parser is off by default\nRemove dependency of cfr_stream_parser in buffered_file_writer\n\n"}
{"title": "Gilad/fix recsys ping start", "number": 93, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/93", "body": "Fixed various bugs caused by the addition of dual-band recording"}
{"title": "Fixed Bologna -> Rome, it didn't exist in the pytz available options", "number": 930, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/930", "body": ""}
{"title": "Workaround iptables race condition in AP by disabling quarantine on server", "number": 931, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/931", "body": "Disable quarantine report to AP\nShallow copy should make it easier for less retries and resolve mismatches\n\n"}
{"title": "Fix migrate issue from charter_rc13", "number": 932, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/932", "body": ""}
{"title": "Fix CFR exception: Different method of shape comparison", "number": 933, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/933", "body": "\nclassifier_1      |   return array(a, dtype, copy=False, order=order, subok=True)\nclassifier_1      | Exception in thread Thread-5:\nclassifier_1      | Traceback (most recent call last):\nclassifier_1      |   File \"/usr/local/lib/python3.8/site-packages/numpy/lib/arraysetops.py\", line 284, in unique\nclassifier_1      |     consolidated = ar.view(dtype)\nclassifier_1      |   File \"/usr/local/lib/python3.8/site-packages/numpy/core/_internal.py\", line 459, in _view_is_safe\nclassifier_1      |     raise TypeError(\"Cannot change data-type for object array.\")\nclassifier_1      | TypeError: Cannot change data-type for object array.\nclassifier_1      |\nclassifier_1      | The above exception was the direct cause of the following exception:\nclassifier_1      |\nclassifier_1      | Traceback (most recent call last):\nclassifier_1      |   File \"/usr/local/lib/python3.8/threading.py\", line 932, in _bootstrap_inner\nclassifier_1      |     self.run()\nclassifier_1      |   File \"/usr/local/lib/python3.8/threading.py\", line 870, in run\nclassifier_1      |     self._target(*self._args, **self._kwargs)\nclassifier_1      |   File \"/root/src/ccpilot/processes/data_processor.py\", line 1498, in process_data\nclassifier_1      |     ret_periodic_state = state.periodic_state_management(transient_id, state_enum_to_state)\nclassifier_1      |   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 1047, in periodic_state_management\nclassifier_1      |     state_change: Optional[Tuple[ClassificationStatus, None]] = self._periodic_state_management_internal(user, state_enum_to_state)\nclassifier_1      |   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 1025, in _periodic_state_management_internal\nclassifier_1      |     return self._coarse_id_decision(ret.caps_model, ret.dhcpfp, ret.dhcpv6_duid, ret.given_ipv4, hw_mac, rand_mac, ret.requested_ipv4, session,\nclassifier_1      |   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 1095, in _coarse_id_decision\nclassifier_1      |     static_cfr_model = static_cfr.static_cfr_build_model(cfr_packets)\nclassifier_1      |   File \"/root/src/ccpilot/processes/models/common/static_cfr.py\", line 156, in static_cfr_build_model\nclassifier_1      |     most_pr = np.unique(list(map(lambda x: x.shape, chains)), axis=0)[0]  # Find out the shape that appears the most\nclassifier_1      |   File \"__array_function__ internals\", line 5, in unique\nclassifier_1      |   File \"/usr/local/lib/python3.8/site-packages/numpy/lib/arraysetops.py\", line 295, in unique\nclassifier_1      |     raise TypeError(msg.format(dt=ar.dtype)) from e\nclassifier_1      | TypeError: The axis argument to unique is not supported for dtype object\n"}
{"title": "Feature/fix cfr fix", "number": 934, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/934", "body": "Revert post collection CFR filtering\nFilter CFR packets in preassociation cache\n\n\n"}
{"title": "eg1 on ch 40 by default to make setup less error prone for Nir", "number": 935, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/935", "body": "Just wanna make the setup process for recsys easier for Nir, isntead of him having to config eg1 and git stash it every time. If we already do this by hand all the time it should be changed to default."}
{"title": "Add tim 3rd engenius", "number": 936, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/936", "body": "Add configuration for tim's 3rd Engenius AP.\n"}
{"title": "Feature/periodic flushing and fixes", "number": 937, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/937", "body": "Changelog:\n\njson only if device_static_cfr_model exists\nAdd periodic flushing pcap writing (every 30 seconds)\nHandle unicode parsing exception\nAdd staticmethod decleration\n\nNotes:\nFix the following exception:\n\nclassifier_1      | 2021-03-19 11:43:09.778 C [data_processor.py:1525] Error occurred during handling of message\nclassifier_1      | Traceback (most recent call last):\nclassifier_1      |   File \"/root/src/ccpilot/processes/data_processor.py\", line 1518, in process_data\nclassifier_1      |     self._process_incoming_event(event, event_map, mitm_arp_detector, cooperation_manager, state_enum_to_state)\nclassifier_1      |   File \"/root/src/ccpilot/processes/data_processor.py\", line 1564, in _process_incoming_event\nclassifier_1      |     self.preassociation_cache_manager.process_packet(user, data)\nclassifier_1      |   File \"/root/src/ccpilot/processes/preassociation_cache.py\", line 560, in process_packet\nclassifier_1      |     self.station_caches[mac].process_packet(data)\nclassifier_1      |   File \"/root/src/ccpilot/processes/preassociation_cache.py\", line 484, in process_packet\nclassifier_1      |     any(cache.process_packet(self.mac, data) for cache in self.caches[write_cache_ind].values())\nclassifier_1      |   File \"/root/src/ccpilot/processes/preassociation_cache.py\", line 484, in genexpr\nclassifier_1      |     any(cache.process_packet(self.mac, data) for cache in self.caches[write_cache_ind].values())\nclassifier_1      |   File \"/root/src/ccpilot/processes/preassociation_cache.py\", line 356, in process_packet\nclassifier_1      |     ssdp_ua = SSDPCache.is_ssdp_query(data)\nclassifier_1      |   File \"/root/src/ccpilot/processes/preassociation_cache.py\", line 349, in is_ssdp_query\nclassifier_1      |     ssdp_ua = SSDPFingerprinting.process_ssdp_discover(pkt)\nclassifier_1      |   File \"/root/src/ssdp_fingerprinting/__init__.py\", line 54, in process_ssdp_discover\nclassifier_1      |     ssdp_ua = ssdp_user_agent_parser(udp.data)\nclassifier_1      |   File \"/root/src/ssdp_fingerprinting/__init__.py\", line 12, in ssdp_user_agent_parser\nclassifier_1      |     ssdp_header =  data[UDP_HEADER_SIZE:].decode('utf-8')\nclassifier_1      | UnicodeDecodeError: 'utf-8' codec can't decode byte 0xc0 in position 33: invalid start byte\n"}
{"title": "IPQ Platform Hardening", "number": 938, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/938", "body": "Password enforcement on serial and ssh according to AP config; Stop luci on firmware \"builds\""}
{"title": "Add plume config", "number": 939, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/939", "body": "Targeting to release/charter_poc because plume branch should be identical to charter (up to specific configuration), and in any case master should be updated from charter soon."}
{"title": "Fixed comcastpi connecting to wrong server", "number": 94, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/94", "body": "Fixed BLE client reaching to wrong endpoint\nFixed comcastpi logs\n\n"}
{"title": "Ignore some icmpv6 ns packets", "number": 940, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/940", "body": "Now ignoring icmpv6 ns packets that caused FP in Guglielmo, they can be sent by anyone and to anyone and can't be a parameter in our decision\n"}
{"title": "Account for training time in displayed time reading in UI/history logs", "number": 941, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/941", "body": ""}
{"comment": {"body": "Can you explain why we\u2019re doing that for Charter and not other for other pilots?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/941/_/diff#comment-*********"}}
{"comment": {"body": "It\u2019s for all the pilots. It was always in the code, somehow it\u2019s probably got lost in the merges..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/941/_/diff#comment-*********"}}
{"comment": {"body": "It will show in training above 20 seconds by default\u2026 also in switch mode\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/941/_/diff#comment-*********"}}
{"title": "Merge cahrter to master", "number": 942, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/942", "body": "Fingerbank query: ignore random mac address\nTried to concatenate list (loaded from db after json dumps->loads) with the object which is tuple\nIf there's an exception of access to shared_state, do retry\nDecorate with more retries Log sleep events and post a metric about it\nHandle also json dump of None\nVerify shapes in CFR model building\nShallow copy should make it easier for less retries and resolve mismatches Disable quarantine report to AP\nFix migrate issue from charter_rc13\nDifferent method of shape comparison\nFilter CFR packets in preassociation requests\njson if if device_static_cfr_model exists\nAdd periodic flushing pcap writing\nHandle unicode parsing exception\nstaticmethod\nPassword enforcement on serial and ssh according to AP config; Stop luci on firmware \"builds\"\nAlso remove eth1 from demo network (disable it essentially)\nAdd plume config\n30 second interval\nCleanup unused code\nUpdate from release/charter_poc after hardening\nAccount for training time in displayed time reading in UI/history logs\nOnly for AP mode\n\n"}
{"title": "charter/ignore ns packets by their option field", "number": 943, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/943", "body": "Now in the more general case, ignore ns packets that has an enabled icmpv6 option, which means they can be sent by anyone and to anyone and can't be a parameter in our decision"}
{"title": "Updated Guglielmo ns extraction to match the one in charter", "number": 944, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/944", "body": "After updating it in our charter versions, we need to add this fix to Guglielmo asap and upgrade their system"}
{"title": "Refactor matching flow", "number": 945, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945", "body": "JustConnected became a monolith and is very difficult to modify flow.\nIve moved the flow to a separate flow and extracted to functions to enable easier flow redirection.\nSo right now, its just a refactor. No flow change, except for mac check for android and windows becoming a solid identifier (as it should be).\nChange log:\n\nMove matching flow and helper functions to separate files\nRandom mac filtering is now part of the solid identifiers\nbreak down functions in matching flow\n\n"}
{"comment": {"body": "Let\u2019s add this to master", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-216734442"}}
{"comment": {"body": "Let\u2019s merge charter first, since there\u2019s a big diff between charter and master", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-216741598"}}
{"comment": {"body": " charter merged to master, you can change the pull to master", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217006436"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217006956"}}
{"comment": {"body": "combine `cmp_close_transaction_id_and_time` and `cmp_close_bonjour_sleep_counter_and_time` to one function with built-in wraparound support? I guess that DHCP transaction ID should also support wraparound.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217882964"}}
{"comment": {"body": "Maybe treat dhcpv6duid for windows the same way it is treated otherwise - as a unique identifier. After all, the uppermost filtering logic itself is the same. We need to set the unique identifier support accordingly and choose which one to use based on device type.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217883155"}}
{"comment": {"body": "Is this a leftover?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217883751"}}
{"comment": {"body": "This function doesn\u2019t use `self`\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217883914"}}
{"comment": {"body": "vscode autocomplete", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217884144"}}
{"comment": {"body": "I\u2019ll `staticmethod` it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217884192"}}
{"comment": {"body": "Maybe, if anything, take this opportunity to bind all the parameters in one object \\(maybe rely on the cache result class in some way\\)? and maybe also use `device_os_classification` and discard all of the `is_os` booleans?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217884321"}}
{"comment": {"body": "What do you propose? Turn this into a matching function for `filter_devices_by_unique_identifiers` to use?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217884422"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Yeah, take the part of it that\u2019s the matching logic.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217884525"}}
{"comment": {"body": "We can do this for another PR..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217884568"}}
{"comment": {"body": "In another PR\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217884604"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} That\u2019s true also for most of my other suggestions. Don\u2019t feel obligated. This is a very helpful refactor as-is!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217884605"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} No worries", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/945/_/diff#comment-217884686"}}
{"title": "Updated rnd.yml and server_list with the second sdi hotel details", "number": 946, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/946", "body": ""}
{"title": "Support osx", "number": 947, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/947", "body": "Officially added the Ruby utility to the same directory (it makes the setup easier for the end-user, less error-prone. dir-name:.r_macchanger)\nupdated some macos / osx commands that retrieve information from the machine.\nremoved unnecessary sleeping times from the Darwin spoofer functions.   \nRenamed the script according to Michaels docs. \n\n"}
{"comment": {"body": "If all these sleeps are unnecessary, just remove them. Nothing special to keep here.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/947/_/diff#comment-214444796"}}
{"comment": {"body": "typo?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/947/_/diff#comment-214444875"}}
{"comment": {"body": "Why\u2019d you remove that?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/947/_/diff#comment-214445118"}}
{"comment": {"body": "found it unnecsary and over complicating during tests. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/947/_/diff#comment-214583532"}}
{"comment": {"body": "you\u2019re right. will do. thx.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/947/_/diff#comment-214583626"}}
{"comment": {"body": "probably. not sure why it was able to run this way.   \nwill check.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/947/_/diff#comment-214583874"}}
{"title": "Update autogenerated files", "number": 948, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/948", "body": ""}
{"title": "Increase Seq Num Forward Threshold", "number": 949, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/949", "body": "In case the updated value device value is bigger than the first value seen in cache, it is supposed to be only a low-single-digit difference (~5). But we should take a bigger confidence interval.\n"}
{"title": "Fixed None dereference", "number": 95, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/95", "body": ""}
{"title": "Bonjour sleep counter matching feature", "number": 950, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/950", "body": "Add Bonjour sleep counter parsing\nUse bonjour sleep counter as a matching feature with low priority and time limitation\nAdd ongoing update for bonjour sleep counter\n\n"}
{"title": "Hotfix/icmp ts rpi disable", "number": 951, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/951", "body": "\n\nICMP TS only for Android 10\n\n"}
{"title": "disable static CFR", "number": 952, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/952", "body": "Now in the more general case, ignore ns packets that has an enabled icmpv6 option, which means they can be sent by anyone and to anyone and can't be a parameter in our decision\nICMP TS only for Android 10\nICMP TS only for Android 10\nICMP TS only for Android 10 - fix comparison\nICMP TS only for Android 10 - fix exception\ndisable static CFR\n\n"}
{"title": "Feature/static cfr phase and rssi", "number": 953, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/953", "body": "Use phase and RSSI as part of the static CFR metric\nAllow static cfr classification for all device types\nSupport DB migration - ignore old models.\nReenable feature, background collection and ongoing training (on disconnection). Reduce background CFR Rate.\n\n"}
{"comment": {"body": "@{5dbeb866c424110de52552cc} let\u2019s enable back the CFR and also the background ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/953/_/diff#comment-215095615"}}
{"title": "Release/charter poc", "number": 954, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/954", "body": "Take a bigger confidence interval in forward seq num delta threshold\nAdd Bonjour sleep counter parsing Use bonjour sleep counter as a matching feature with low priority Add ongoing update for bonjour sleep counter\nFix ongoing check for not None\nGive 10 minutes allowed timegap\nComment\nICMP TS only for Android 10\nICMP TS only for Android 10\nwindows blank UID is not unique\nVerify that uid is not None\nFix order of diff (new minus old, not old minus new)\nICMP TS only for Android 10 - fix comparison\nICMP TS only for Android 10 - fix exception\ndisable static CFR\nsleep number: comparison must be when both bonjour uids are from en5 uids\noops\nOOPSIE\n\n"}
{"title": "Autogenerated files", "number": 955, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/955", "body": ""}
{"title": "Fixed _is_windows_10() method to first check the device type manufacturer before checking the type model", "number": 956, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/956", "body": "In PR-795, we edited our is_windows_10() method to handle the case where some TP-LINK APs were identified by mistake as windows devices, because of of their returned device type model from the fingerbank. For e.g:\n[data_processor.py:216] User e4:c3:2a:61:bb:1e got device_info {'display_device_manufacturer': 'TP-LINK TECHNOLOGIES CO.,LTD.', \n'display_device_model': 'Operating System/Windows OS/Windows Phone OS/Windows Phone 8.0', 'device_type_model': 'Operating System/Windows OS/Windows Phone OS/Windows Phone 8.0', \n'device_type_manufacturer': 'TP-LINK TECHNOLOGIES CO.,LTD.', 'device_addr': 'e4:c3:2a:61:bb:1e'}\nSince that fix the method was changed a bit, and instead of first checking the device type model (which will mistakenly identify it as a windows device), we need to first check the device type manufacturer, so we return False right away."}
{"title": "Fix compare function of bonjour sleep counter", "number": 957, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/957", "body": ""}
{"title": "Update metrics with new features", "number": 958, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/958", "body": "Fixed our matching feature counter by not zero the keys' values in every iteration.\nUpdated the metrics with new features that were missing.\nAs for the pulling time interval, well update the metrics every 10 seconds, with the last changes that happened in that period of time (instead of pulling everything every 5 or 10 minutes like we used to)."}
{"title": "Add also requested_ipv4 into condition of device_ipv4_leases_store", "number": 959, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/959", "body": ""}
{"comment": {"body": "The \u2018!=\u2019 will be evaluated before the \u2018and\u2019, better to put parentheses", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/959/_/diff#comment-215202404"}}
{"title": "Gilad/CCP-63 watchdog on wifi reset", "number": 96, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/96", "body": "Agent now recovers when the ssid changes (and should almost always recover on calls to wifi)"}
{"title": "Fix static cfr db migration", "number": 960, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/960", "body": "Fix condition to load saved models\n"}
{"title": "Fix static cfr db migration - again", "number": 961, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/961", "body": "Last fix could have resulted in false keeping of old model so used more precise test"}
{"title": "Fix static cfr db migration - once again", "number": 962, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/962", "body": "Last fix could also run into problems (future ones for newly learned models) if amp/phase data is not sufficient. Different fix\n"}
{"title": "WPS UUID feature implementation", "number": 963, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/963", "body": ""}
{"comment": {"body": "also check that `vendor_oui` is `00:50:f2` \\(Microsoft corp.\\) \\(`20722` in `int`ish\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/963/_/diff#comment-215381491"}}
{"comment": {"body": "Are you sure it\u2019s always the case?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/963/_/diff#comment-215382168"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} aye: [https://gitlab.com/kalilinux/packages/wig-ng/blob/kali/master/helpers/wps.py#L156](https://gitlab.com/kalilinux/packages/wig-ng/blob/kali/master/helpers/wps.py#L156){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/963/_/diff#comment-215382637"}}
{"comment": {"body": "maybe it\u2019s better to import that `wps.py` file. It\u2019s GPL", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/963/_/diff#comment-215383059"}}
{"comment": {"body": "I\u2019m seeing that `UUID_R_ELEMENT_TYPE = 0x1048` is also a unique identifier \\(UUID provided by the registrar, the AP\\).\n\nProbably not relevant to charter scenario, but it\u2019s interesting point to research", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/963/_/diff#comment-215386882"}}
{"comment": {"body": "Add also the other UUID element in efb098d8d8b143cc6828f94c331658578441e68c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/963/_/diff#comment-215409955"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Done in 8dce4ad6f436bc1275ffbc38c03b88fd3573f6a1", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/963/_/diff#comment-215410148"}}
{"title": "Charter/wps refinement - WPS UUID as a matching param", "number": 964, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/964", "body": ""}
{"comment": {"body": "Let\u2019s add solid DUID in RPi", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/964/_/diff#comment-215452930"}}
{"comment": {"body": "please remove this commented line", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/964/_/diff#comment-215454283"}}
{"title": "Feature/fix arp reply not with interface mac address", "number": 965, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/965", "body": "Weve seen this behavior in RPIs that after running macchanger script (and restarting the interface and reconnecting to the wifi), that the ARP replies would come with the correct random mac in in the ethernet layer, but the ARP sender would be the HW mac:\n\nThe expected behavior until now is that the ethernet mac is the same as sender mac.\nSince we wait for the IP indication in the ARP sender field, we wouldnt get an IP resolution and the device would go into Connectivity Error state.\nThe change is to look also at the ethernet mac to get the IP resolution indication.\nIn the future, we could use this behavior as a feature, since the RPi is leaking its HW mac.\n\n"}
{"title": "if static_cfr_model, less logs", "number": 966, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/966", "body": ""}
{"title": "Release/charter poc", "number": 967, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/967", "body": "Now in the more general case, ignore ns packets that has an enabled icmpv6 option, which means they can be sent by anyone and to anyone and can't be a parameter in our decision\nTake a bigger confidence interval in forward seq num delta threshold\nAdd Bonjour sleep counter parsing Use bonjour sleep counter as a matching feature with low priority Add ongoing update for bonjour sleep counter\nFix ongoing check for not None\nGive 10 minutes allowed timegap\nComment\nICMP TS only for Android 10\nICMP TS only for Android 10\nwindows blank UID is not unique\nVerify that uid is not None\nFix order of diff (new minus old, not old minus new)\nICMP TS only for Android 10 - fix comparison\nICMP TS only for Android 10 - fix exception\ndisable static CFR\nsleep number: comparison must be when both bonjour uids are from en5 uids\noops\nUse phase and RSSI as part of the static CFR metric\nOOPSIE\nComplete \"compare_models()\", take only first 2 chains for rssi, remove deprecated \"classify\" func\nDeserialize model to tuple\nAllow static cfr classification for all device types\nDB migration support for updated static CFR model\nEnabled back static CFR + background, reduced background CFR rate by 10.\nFix compare function of bonjour sleep counter\nless blank lines\nAdd also requested_ipv4 into condition of device_ipv4_leases_store\nFix condition to load saved models\nAdd mac in print\nLast fix could have resulted in false keeping of old model so used more precise test\nLast fix could also run into problems (future ones for newly learned models) if amp/phase data is not sufficient. Different fix\nWPS UID feature\nUse also UUID_R\nVerify mircosoft OUI\nRevert UUID_R\nWPS_UID as a matching param\npass is_raspberry_pi param\nCancel ipv4 lease for RPI\nDUID for solid identifier for RPI\narp_map to return eth mac as well arp map is now a set of ip, mac tuples\nFix return value\nif static_cfr_model, less logs\n\n"}
{"title": "Master -> Guglielmo (metrics refinement)", "number": 968, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/968", "body": ""}
{"title": "DONT MERGE YET - Removed one of the hosts that caused an iPhone to be identified as an android device", "number": 969, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/969", "body": "Yesterday we had a bug in our beloved bunny server where an iPhone was identified as an android device. When looking at the logs, we can see that:\nReturn from caches: caps_model None dhcp DhcpFP(dhcpfp=[1, 121, 3, 6, 15, 119, 252], dhcp_vendor=None, dhcp_transaction_id=264453712, dhcp_hostname='Ariels-iPhone') requested_ip None dhcpv6 None mdns_hostname None netbios_hostname None netbios_transaction_id None bonjour_uid 1 icmp_rx_ts_delta_list None syn_timestamps 14 http_headers 7 \ndns_queries {'32-courier.push.apple.com', 'www.icloud.com', 'tpop-api.twitter.com', 'api-stream.twitter.com', 'e14868.dsce9.akamaiedge.net', 'gsp10-ssl.apple.com', 'gsp10-ssl.ls-apple.com.akadns.net', 'cs45.wac.edgecastcdn.net', 'app-measurement.com', \n'a1931.dscgi3.akamai.net', 'gsp10-ssl.ls.apple.com', 'init.ess.apple.com', 'apple.com', 'iphone-ld.apple.com', 'pbs.twimg.com', 'api.twitter.com', 'video.twimg.com', 'gsp85-ssl.ls.apple.com', 'connectivitycheck.gstatic.com', 'www.apple.com', 'api-34-0-0.twitter.com', \n'cl4.apple.com', '', 'gsp85-ssl.ls2-apple.com.akadns.net', 'updates-http.cdn-apple.com', 'cl3.apple.com', 'a239.gi3.akamai.net', 'updates-http.g.aaplimg.com', 'probe-t.twitter.com', 'proxsee.pscp.tv'} icmpv6 [] icmp6_mclr {'45f473'} static_cfr 0 seq_num_info None ssdp_user_agents [] probe_req_grid None\nAnd more specific, connectivitycheck.gstatic.com appeared here, which made us make that decision, and is therefore removed in this PR as a strong android identifier."}
{"title": "Feature/CCP-23 tee based regression tests", "number": 97, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/97", "body": "Setting up a jenkins slave with git-lfs support was a pain :sweat:.\nThis is the same PR as the previous one, only with fixes and responses to all PR comments, and now pickles are only incorporated as lfs-tracked.\n\nAdded regression tests based on tee, fixed prune collision. Back in PR-86 state\nAdd pickles as lfs-tracked\n\nSmall fixes and response to PR comments\n\nRemoved merging regression junit reports, at least for now\nRemoved TODOs in code\nFixed indentation\n\n\n\n"}
{"comment": {"body": "Anyway they can be ran in parallel?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/97/_/diff#comment-148449092"}}
{"comment": {"body": "This multiplier might distort server-side time constants \\(timeouts and the like\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/97/_/diff#comment-148449334"}}
{"comment": {"body": "They will collide, will work only if I build multiple test env containers. sanity takes < 10s right now, so it\u2019s ok. But as discussed earlier, we should be able to manage multiple containers or even hold a pool of them.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/97/_/diff#comment-148450617"}}
{"comment": {"body": "True. The multiplier and the recordings themselves were tailored to fit the current tests. For every future test that relies on tee replay, this should be taken into consideration. In the large test case added here this was essential, as x3 rate saves 2 min. Parallelization, as you mentioned above, would ease the timing constraints and allow some of the tests to not use the multiplier.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/97/_/diff#comment-148454091"}}
{"title": "Log failed connections reports in management endpoint", "number": 970, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/970", "body": "Handle corrupted jsons by logging and returning. This is following , which we couldnt recreate and have no further info on."}
{"title": "Release/charter poc", "number": 971, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/971", "body": "Use phase and RSSI as part of the static CFR metric\nComplete \"compare_models()\", take only first 2 chains for rssi, remove deprecated \"classify\" func\nDeserialize model to tuple\nAllow static cfr classification for all device types\nDB migration support for updated static CFR model\nEnabled back static CFR + background, reduced background CFR rate by 10.\nFix compare function of bonjour sleep counter\nless blank lines\nAdd also requested_ipv4 into condition of device_ipv4_leases_store\nFix condition to load saved models\nAdd mac in print\nLast fix could have resulted in false keeping of old model so used more precise test\nLast fix could also run into problems (future ones for newly learned models) if amp/phase data is not sufficient. Different fix\nWPS UID feature\nUse also UUID_R\nVerify mircosoft OUI\nRevert UUID_R\nWPS_UID as a matching param\npass is_raspberry_pi param\nCancel ipv4 lease for RPI\nDUID for solid identifier for RPI\narp_map to return eth mac as well arp map is now a set of ip, mac tuples\nFix return value\nif static_cfr_model, less logs\nHandle corrupted jsons by logging and returning\n\n"}
{"title": "Always test the ARP table response in AP agent", "number": 972, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/972", "body": ""}
{"title": "Feature/master to charter 210331", "number": 973, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/973", "body": "Add configuration for tim's 3rd AP and corresponding server\nAdding Guglielmo server\nFix IPv4 resolve for Enterprise mode\nUpdate ipq_config/*_env.sh files Include restore_log.py to deserialize Decision log files\nrevert FB key to common\nUpdated the metrics counters with new features\nNow counting the right parameters by the matching connection\nNow counting the feature only if it was a match\neg1 on ch 40 by default to make setup less error prone for Nir\nAdd config for Tim's 3rd 2X2 engenius\nUpdated rnd.yml and server_list with the second sdi hotel details\nUpdate autogenerated files\nAutogenerated files\nSimplified the dictionary that we send\n\n"}
{"title": "Split CFR cache to cache per band", "number": 974, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/974", "body": "Split CFR pre assoc cache to bands. This way we don't mix 2.4 and 5 CFRs on device band switch (time filtering is not accurate as we dont have AP synced timestamp).\n  This was done to handle \nAlso, ongoing wasn't enabled for non-rpi.\n\n"}
{"comment": {"body": "Why don\u2019t we have timestamps on the CFRs?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/974/_/diff#comment-215761968"}}
{"comment": {"body": "We don\u2019t have something similar to radiotap\u2019s capture timestamp.  \nWe thought about adding it, but we\u2019d need to modify the CFR transport, which is more complicated and riskier than just modifying the caches.\n\nIt\u2019ll be a good change to modify the CFRs to have the same transport as tcpdump\u2019s. We could do it for future release.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/974/_/diff#comment-215766920"}}
{"comment": {"body": "Why the timestamp in the CFR isn\u2019t good enough?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/974/_/diff#comment-215767689"}}
{"comment": {"body": "We don\u2019t have UTC timestamp coupled to the CFR", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/974/_/diff#comment-215769424"}}
{"comment": {"body": "We did in the past. Have something changed?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/974/_/diff#comment-215939965"}}
{"comment": {"body": "Never had one, AFAIK. We have timestamp in microseconds since boot", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/974/_/diff#comment-215946256"}}
{"title": "AP agent to ping all arp probes and arp announcements", "number": 975, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/975", "body": ""}
{"title": "update cfr presnce detection files before merge", "number": 976, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/976", "body": ""}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} The limitations we have observed:\n\n1. Works only on channel 40\n2. We have one iPhone with false positives\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/976/_/diff#comment-222532207"}}
{"comment": {"body": "1. The restriction for channel 40 is a matter of the primary segment which is hard-coded to \\[1\\]. We should use Grisha\u2019s LUT to map the primary segment by the channel. \n2. That\u2019s the iPhone12 which is new with respect to this work. I\u2019ll explore that further \\[nevertheless, the threshold is adaptive anyways, so I expect the algorithm to work fine after some time \\(~30 secs\\)\\].\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/976/_/diff#comment-222544098"}}
{"title": "Merge charter to plume", "number": 977, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/977", "body": ""}
{"comment": {"body": "We need to make sure we test the upgrade scenarios of the plume system", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/977/_/diff#comment-216431984"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/977/_/diff#comment-216436947"}}
{"title": "Feature/rest api add server timestamp", "number": 978, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/978", "body": "Add last-modified header to API GETs\nUpdated server list\n\n"}
{"comment": {"body": "This is a timestamp after the request have already been executed \\(db lookup\\)\n\nWe probably need to get the timestamp of when the request arrives to the server.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/978/_/diff#comment-216433630"}}
{"comment": {"body": "Let\u2019s add in all APIs we exposed the last-modified as request arrival timestamp in UTC", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/978/_/diff#comment-216441862"}}
{"comment": {"body": "changed to request time", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/978/_/diff#comment-216442491"}}
{"comment": {"body": "changed [https://app.swaggerhub.com/apis-docs/Levl-Technologies/LEVLDeviceIntelligence/1.2.2](https://app.swaggerhub.com/apis-docs/Levl-Technologies/LEVLDeviceIntelligence/1.2.2) APIs to request time", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/978/_/diff#comment-216442504"}}
{"title": "Physical Layer Neural Network", "number": 979, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/979", "body": "First commit of one-class and multi-class largescale code\nAdded path for shared database\nAdded note about file deletion\n\n"}
{"comment": {"body": "add comment regarding that the processing is fixed for channel 40 and using specific antenna", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/979/_/diff#comment-217040985"}}
{"comment": {"body": "add comment regarding that the processing is fixed for channel 40 and using specific antenna", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/979/_/diff#comment-217041112"}}
{"comment": {"body": "I think we should somehow connect the preprocessing to the classifiers or reference the preprocessing used from the classifiers. So it won\u2019t be confusing if we add other preprocessing methods to fit to other classifiers. Such as when we add Gal\u2019s preprocessing and Gal\u2019s classifiers. Lets discuss.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/979/_/diff#comment-217069799"}}
{"comment": {"body": "I changed the code such that the preprocessing is defined within the classifier file and the reference db uses the preprocessing function defined in the classifier.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/979/_/diff#comment-218038277"}}
{"comment": {"body": "Furthermore, I added something I call \u201c**preprocessing\\_uid**\u201d which translates the current preprocessing implementation into a unique string. I use this string to save preprocessed data such that when the preprocessing is changed \\(trying different number of antennas, normalization etc\\) a different uid will be computed and we **won\u2019t have to delete** REF\\_TEMP\\_FILES or processed\\_temp\\_data folders.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/979/_/diff#comment-218038731"}}
{"comment": {"body": "It looks good noam, you can sync with master and push :thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/979/_/diff#comment-218521272"}}
{"title": "Add verbose prints on classification", "number": 98, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/98", "body": ""}
{"title": "Feature/ui analytics merges", "number": 980, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/980", "body": "Add paginator for UI\nModify default page sizes\nfogot files\nDo not insert old legacy Auth events into queue\nWork in progress\nAPI SIDE\nBar to not overflow\nChange order of device types\nDisplay data with fixed percision\nChange order\nRemove alerts tab\nUI Analytics merges\n\n"}
{"comment": {"body": "what data is actually needed? 90% or 95%?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/980/_/diff#comment-216995686"}}
{"title": "Caps model for RapberryPI zero", "number": 981, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/981", "body": ""}
{"comment": {"body": "Why not adding another profile in RPi above? What I am missing?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/981/_/diff#comment-216503146"}}
{"comment": {"body": "Well Technically they are different PI models, but we can add them under the same device.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/981/_/diff#comment-216503898"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Add a note of which profile suites which device?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/981/_/diff#comment-216509293"}}
{"title": "Charter changes", "number": 982, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/982", "body": "Add last-modified header to API GETs\nUpdated server list\nLast modified field for the history log report is the request timestamp\nIPv4 features also for RPi\nHistory log in UTC timestamp\nDisable static location, seq num, ICMP TS by default\n\n"}
{"comment": {"body": "Also:\n\n* Re-enabled IPv4 lease feature for Rapsberry PI\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/982/_/diff#comment-216514375"}}
{"comment": {"body": "Are you talking about this [https://bitbucket.org/levl/comcast/pull-requests/982#Lccpilot/processes/states/just_connected_state.pyT1217](https://bitbucket.org/levl/comcast/pull-requests/982#Lccpilot/processes/states/just_connected_state.pyT1217){: data-inline-card='' }  ?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/982/_/diff#comment-216514671"}}
{"comment": {"body": "Yes. Haven\u2019t noticed that :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/982/_/diff#comment-216514749"}}
{"comment": {"body": "Just noticed that - shouldn\u2019t the time in the filename be the client time anyway?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/982/_/diff#comment-216517561"}}
{"comment": {"body": "Nah. UTC is da best", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/982/_/diff#comment-216519269"}}
{"title": "Save server logs in detached mode", "number": 983, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/983", "body": "docker_output.logs wasn't updated when running dcompose with -d. Now it will."}
{"title": "bugfix - check dst port in netbios parsing", "number": 984, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/984", "body": "We wrongly looked at the src port all this time. We didn't notice so far because in many cases (for some reason) sport=dport=137.\nWill cherry-pick to release branches after approval."}
{"title": "fix netbios parsing to check dst port", "number": 985, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/985", "body": "We wrongly looked at the src port all this time. We didn't notice so far because in many cases (for some reason) sport=dport=137.\nWill update plume and guglielmo after merge."}
{"title": "NetBIOS timediff to 20 minutes", "number": 986, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/986", "body": ""}
{"title": "Support ICMPv6 NS for Linux Devices", "number": 987, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/987", "body": "Linux devices should also consider icmpv6 ns. Also grouped all android 10 params under one \"if\" and fixed icmpv6 ns wait criteria.\n"}
{"title": "Release/charter poc", "number": 988, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/988", "body": "Linux devices should also consider icmpv6 ns. Also grouped all android 10 params under one \"if\"\nMinimize condition\n\n"}
{"title": "Master", "number": 989, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/989", "body": "Now in the more general case, ignore ns packets that has an enabled icmpv6 option, which means they can be sent by anyone and to anyone and can't be a parameter in our decision\nTake a bigger confidence interval in forward seq num delta threshold\nAdd Bonjour sleep counter parsing Use bonjour sleep counter as a matching feature with low priority Add ongoing update for bonjour sleep counter\nFix ongoing check for not None\nGive 10 minutes allowed timegap\nComment\nICMP TS only for Android 10\nICMP TS only for Android 10\nwindows blank UID is not unique\nVerify that uid is not None\nFix order of diff (new minus old, not old minus new)\nICMP TS only for Android 10 - fix comparison\nICMP TS only for Android 10 - fix exception\ndisable static CFR\nsleep number: comparison must be when both bonjour uids are from en5 uids\noops\nUse phase and RSSI as part of the static CFR metric\nOOPSIE\nComplete \"compare_models()\", take only first 2 chains for rssi, remove deprecated \"classify\" func\nDeserialize model to tuple\nAllow static cfr classification for all device types\nDB migration support for updated static CFR model\nEnabled back static CFR + background, reduced background CFR rate by 10.\nFix compare function of bonjour sleep counter\nless blank lines\nAdd also requested_ipv4 into condition of device_ipv4_leases_store\nFix condition to load saved models\nAdd mac in print\nLast fix could have resulted in false keeping of old model so used more precise test\nLast fix could also run into problems (future ones for newly learned models) if amp/phase data is not sufficient. Different fix\nWPS UID feature\nUse also UUID_R\nVerify mircosoft OUI\nRevert UUID_R\nWPS_UID as a matching param\npass is_raspberry_pi param\nCancel ipv4 lease for RPI\nDUID for solid identifier for RPI\narp_map to return eth mac as well arp map is now a set of ip, mac tuples\nFix return value\nif static_cfr_model, less logs\nHandle corrupted jsons by logging and returning\nAlways test the ARP table response\nDivide CFR pre assoc cache to bands. This way we don't mix 2.4 and 5 CFRs on device band switch (time filtering is not accurate with no ap synced timestamp). Also, ongoing wasn't enabled for non-rpi\nAdd new features to metrics\nUpdate server list\nAP agent to ping all arp probes and arp announcements\nAdd last-modified header to API GETs\nUpdated server list\nReplace single launch of arp monitor process with ARPMonitor module that has process WD and limits arps to once per second at most\nComments\nComment\nLast modified field for the history log report is the request timestamp\nLast modified time is of request timestamp\nCaps model for RapberryPI zero\nIPv4 features also for RPi\nHistory log in UTC timestamp\nDisable static location, seq num, ICMP TS by default\nComments regarding the tech\ndocker_output.logs wasn't updated when running dcompose with -d\ndocker_output.logs wasn't updated when running dcompose with -d\nWe wrongly looked at the src port all this time. We didn't notice so far because in many cases (for some reason) sport=dport=137\n\n"}
{"title": "Fixed bug when bssid is zero-length", "number": 99, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/99", "body": "The value of bssid when the interface is not valid can apparently be empty, because briefly after calling wifi, iwconfig cannot find ath0."}
{"title": "Feature/cfr capture timestamped", "number": 990, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/990", "body": "This is the work for getting capture timestamp of CFR to be as close as possible to actual capture timestamp, based on the APs clock.\nAlso make transport to be based on pcap, to make stuff easier and have code reuse.\n\n\nChangelog:\n\nOptimize CFR reading from stream\nStreamer agent to stream pcap format\nCapture timestamp of CFR packet based on wizardry calculations\n\n\n\n"}
{"comment": {"body": "Can we get rid of `affix_stream`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/990/_/diff#comment-220427121"}}
{"comment": {"body": "no, it\u2019s used in the AP for reading CFRs", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/990/_/diff#comment-220442441"}}
{"comment": {"body": "Didn\u2019t we agreed to drop the additional port?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/990/_/diff#comment-220475467"}}
{"comment": {"body": "I closed the original port with an assert to not support backward compatibility. \n\nI think it's better than using the original port and not detecting different protocols ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/990/_/diff#comment-220492320"}}
{"title": "Feature/docker emnhanmcents", "number": 991, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/991", "body": "Add paginator for UI\nModify default page sizes\nfogot files\nDo not insert old legacy Auth events into queue\nWork in progress\nAPI SIDE\nBar to not overflow\nChange order of device types\nDisplay data with fixed percision\nChange order\nRemove alerts tab\nUI Analytics merges\nFix device name update and analytics API key\nResolve lazy load devices events bug\nRemove commented code\nsupport backward device id api\nupdate cr comments\nSupport log rotate on docker logging and limit the max size\n\n"}
{"title": "Fix source of phase calculation", "number": 992, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/992", "body": ""}
{"title": "Charter to master", "number": 993, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/993", "body": ""}
{"title": "Feature/store dhcp pickle in db", "number": 994, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/994", "body": "Part of the replay effort is to have the dhcp transitions pickle in the DB, so its easier to snapshot the state.\nIve used SQLAlchemy for DB access instead of the regular SQL syntax.\n\nMigrate pickle to table\nShift to sqlalchemy ORM\n\n"}
{"title": "UI analytics per device", "number": 995, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/995", "body": ""}
{"title": "Release/charter poc", "number": 996, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/996", "body": "Add paginator for UI\nModify default page sizes\nfogot files\nDo not insert old legacy Auth events into queue\nWork in progress\nAPI SIDE\nBar to not overflow\nChange order of device types\nDisplay data with fixed percision\nChange order\nRemove alerts tab\nUI Analytics merges\nFix device name update and analytics API key\nResolve lazy load devices events bug\nRemove commented code\nsupport backward device id api\nupdate cr comments\nSupport log rotate on docker logging and limit the max size\nFix source of phase calculation\nFix UI conenciton duration string format\nFix analytics devices count\n\n"}
{"title": "no need to wait for DUID for RPi, we have WPS", "number": 997, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/997", "body": ""}
{"title": "Feature/merge master guglielmo", "number": 998, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/998", "body": "Add paginator for UI\nModify default page sizes\nfogot files\nDo not insert old legacy Auth events into queue\nMove matching flow and helper functions to seperate files\nNow in the more general case, ignore ns packets that has an enabled icmpv6 option, which means they can be sent by anyone and to anyone and can't be a parameter in our decision\nWork in progress\nRandom mac filtering is now part of the solid identifiers\nbreak down functions in matching flow\nRND_MAC is just for android HW_MAC also for windows\nMinor esthatics\nAPI SIDE\nBar to not overflow\nChange order of device types\nDisplay data with fixed percision\nChange order\nRemove alerts tab\nTake a bigger confidence interval in forward seq num delta threshold\nAdd Bonjour sleep counter parsing Use bonjour sleep counter as a matching feature with low priority Add ongoing update for bonjour sleep counter\nFix ongoing check for not None\nGive 10 minutes allowed timegap\nComment\nICMP TS only for Android 10\nICMP TS only for Android 10\nwindows blank UID is not unique\nVerify that uid is not None\nFix order of diff (new minus old, not old minus new)\nICMP TS only for Android 10 - fix comparison\nICMP TS only for Android 10 - fix exception\ndisable static CFR\nsleep number: comparison must be when both bonjour uids are from en5 uids\noops\nUse phase and RSSI as part of the static CFR metric\nOOPSIE\nComplete \"compare_models()\", take only first 2 chains for rssi, remove deprecated \"classify\" func\nDeserialize model to tuple\nAllow static cfr classification for all device types\nDB migration support for updated static CFR model\nEnabled back static CFR + background, reduced background CFR rate by 10.\nFix compare function of bonjour sleep counter\nless blank lines\nAdd also requested_ipv4 into condition of device_ipv4_leases_store\nFix condition to load saved models\nAdd mac in print\nLast fix could have resulted in false keeping of old model so used more precise test\nLast fix could also run into problems (future ones for newly learned models) if amp/phase data is not sufficient. Different fix\nWPS UID feature\nUse also UUID_R\nVerify mircosoft OUI\nRevert UUID_R\nWPS_UID as a matching param\npass is_raspberry_pi param\nCancel ipv4 lease for RPI\nDUID for solid identifier for RPI\narp_map to return eth mac as well arp map is now a set of ip, mac tuples\nFix return value\nif static_cfr_model, less logs\nHandle corrupted jsons by logging and returning\nAlways test the ARP table response\nDivide CFR pre assoc cache to bands. This way we don't mix 2.4 and 5 CFRs on device band switch (time filtering is not accurate with no ap synced timestamp). Also, ongoing wasn't enabled for non-rpi\nAdd new features to metrics\nUpdate server list\nAP agent to ping all arp probes and arp announcements\nAdd last-modified header to API GETs\nUpdated server list\nReplace single launch of arp monitor process with ARPMonitor module that has process WD and limits arps to once per second at most\nComments\nComment\nLast modified field for the history log report is the request timestamp\nLast modified time is of request timestamp\nMissing CFR model logs\nMore missing\nUI Analytics merges\nCaps model for RapberryPI zero\nIPv4 features also for RPi\nHistory log in UTC timestamp\nDisable static location, seq num, ICMP TS by default\nComments regarding the tech\nFix merge issues\ndocker_output.logs wasn't updated when running dcompose with -d\ndocker_output.logs wasn't updated when running dcompose with -d\nMore conflict\nFix exceptions\nException\nWe wrongly looked at the src port all this time. We didn't notice so far because in many cases (for some reason) sport=dport=137\nFix device name update and analytics API key\nResolve lazy load devices events bug\nLinux devices should also consider icmpv6 ns. Also grouped all android 10 params under one \"if\"\nRemove commented code\nsupport backward device id api\nMinimize condition\nupdate cr comments\nSupport log rotate on docker logging and limit the max size\nFix source of phase calculation\nFix UI conenciton duration string format\nFix analytics devices count\n\n"}
{"title": "For analytics UI, filter only classification events", "number": 999, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/999", "body": ""}
{"title": "Data analysis", "number": 1, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/1", "body": "update scripts and create lf_utils\nrefactoring infrastructure\n\n"}
{"title": "Data analysis", "number": 10, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/10", "body": "try fix alpha\nall working\nlf_rssi_recorder\nadded extended thd_rms and bandnoise\nfix bandpower function and added new model class\nreport games\n\n"}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} For sample rate of 2MHz and an offset of approximately 50 samples \\(as we have observed\\), we get delay in seconds of 50 / 2e6 =~ 2.5e-5, so the starting point for the time shift is off by an order of magnitude.", "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/10/_/diff#comment-134103744"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8}  I think we\u2019re missing a factor of sample\\_rate in the second entry of precursor, since we need to differentiate by delay\\_sec and not by delay\\_samples.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/10/_/diff#comment-134103785"}}
{"comment": {"body": "yes and i intended to convert 46 samples delay so 46 / 2e6 = 2.3e-5 = 23e-6 as i wrote in the code", "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/10/_/diff#comment-134103942"}}
{"comment": {"body": "Ah yes you are correct.", "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/10/_/diff#comment-134105111"}}
{"title": "First Version of Classifier", "number": 11, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/11", "body": "First version of the decay parameter classifier. It needs a range of CFOs in training in order to function properly."}
{"title": "changes to bandpower again", "number": 12, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/12", "body": ""}
{"title": "Optimizing Offset, Making Figures, Refactoring. Last Commit.", "number": 13, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/13", "body": "Target is not fractal anymore and offset is optimized for (at least for oversampling 20. Lower oversampling rates may require more care). Assuming all data is where it should be, just run runner.sh and then python make_figs.py from python/infra to make images."}
{"title": "Better Demod", "number": 14, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/14", "body": "Used demoding with Bit rate 3.905 KBit / S (just because it looks good in the pictures). Code runs but I did NOT check classification results. Added figure to show demodulation. It is generated by make_figs.py\nTODO:\n\nAllow using the non-default packet.\nMove the demoding function somewhere else, potentially. \n\n"}
{"title": "Yair", "number": 2, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/2", "body": "\n\nmake framework (more) robust to bad pickles, save processed data on the go so if we crash we dont have to redo everything again and add flag to allow us to regenrate everything, even if saved files exist\n\nsome changes to framework - add description and search for templates in a directory\n\n"}
{"comment": {"body": "why the tkinter is commented?\n\nthis was meant the rise folder browser to pick folder if not picked ", "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/2/_/diff#comment-130319609"}}
{"comment": {"body": "Because it doesn\u2019t work on levlcompute. Maybe you can ssh -X or something \\(I did not check\\) but both of us work from command line so I thought it is redundant.", "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/2/_/diff#comment-130471025"}}
{"title": "Merge Decay Parameter to Master", "number": 3, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/3", "body": "Pretty self explanatory"}
{"title": "Debuggung and Reconstructing", "number": 4, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/4", "body": "refactor and replace nelder-mead with grid search\ntrying to generate previous success but to no avail\nwas able to reconstruct previous success\n\n"}
{"title": "Data analysis", "number": 5, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/5", "body": "update .gitignore\nfix bug in signal_to_analytic and added siggen testing\n\n"}
{"title": "Feature on the Right Scale with Fast Optimization", "number": 6, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/6", "body": "Feature:\nUpdated feature, derivative for faster optimization, parameter now measured on the same scale using the same units (Hz) regardless of oversampling rate.\n\nInfrastructure:\nRuns on levlcompute, does not ignore car data, added script (runner.sh) to run on levlcompute, added killer to kill python processes (when you want to kill a job you started using nohup)\n\n"}
{"title": "Find another smoothing parameter beta", "number": 7, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/7", "body": "There are some comments in the decay_parameter file, regarding stuff that needs to be considered (mainly - the normalization we use). We currently do not use derivatives. They are good (IMHO) but there is one test that fails and I am unable to make it work. So we do derivative free optimization currently. Kernels are good for sure - tests pass with errors of order  1e-16."}
{"title": "Data analysis", "number": 8, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/8", "body": "checking thd feature scripts\nadded thd to features\nkill length assert\nfix length mismatch\nchange bandpower from db to linear\ncfo input added to feature_extract and other improvements\n\n"}
{"title": "Reconstruction is back, and will be made better", "number": 9, "htmlUrl": "https://bitbucket.org/levl/lf_relay_detection/pull-requests/9", "body": "not much\nUse previous normalization scheme, for now (i.e. normalize amplitude by energy, do not normalize demoded data\nrunner\nrearrange and refactor\nrunner\nchanged crucial bug where gauss kernel used alpha instead of beta as input\nblabla\nall is bad\nback in businessgit add processing_utils.py git add processing_utils.py\nreconstrution is back but it is not as good as before\n\n"}
{"title": "Dev", "number": 1, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/1", "body": "cd\nadd aws\naws configure\nmkdir\ntf 1.0.9\ndeploy\ncd\nartifact .terraform\nremove cat\ninit\nchange schema\n\n"}
{"title": "Fix kafka address and pipeline name", "number": 10, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/10", "body": "updating kafka address and pipeline name\n\n"}
{"title": "updating kafka address and git ignore idea", "number": 11, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/11", "body": ""}
{"title": "more updating kafka address and fixing pipeline name", "number": 12, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/12", "body": ""}
{"comment": {"body": "readme file?", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/12/_/diff#comment-303681939"}}
{"comment": {"body": "added", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/12/_/diff#comment-303888397"}}
{"title": "Merge from master", "number": 13, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/13", "body": "cd\nadd aws\naws configure\nmkdir\ntf 1.0.9\ndeploy\ncd\nartifact .terraform\nremove cat\ninit\nchange schema\nv2\nadd suffix _raw\nupdate extracted features scehma\ntraining model\nWIP\nupdate model view\nadjust tables location on s3\nWIP\nbug fixes\nsparrow RC1.0\nv1\nrename\nrename firehose stream\nadd missing fields operating_mode_notif,he_caps,rm_caps\nrename stream names to shorter\nremove timestamps temporary due to bug\nbug fix\nremove TBD features\nresolve conflicts\nreturn of the timestamp\ndev\nupdate processor image version\n\nMerged in feature/tf_workspaces (pull request #4)\nFeature/tf workspaces\n\nupdate image\nWIP\nadd target file\nworking flow\nlatest and greatest\nWIP\nbackfill WIP\nMerged in multi_platform_schemas (pull request #3)\n\nAdding platform type and config fields to schemas\n\nAdding platform type and config fields to schemas\nMerged feature/tf_workspaces into multi_platform_schemas\n\nApproved-by: Itai Zolberg * Merge branch 'dev' of bitbucket.org:levl/sparrow-infrastructure into feature/tf_workspaces\n\nMerge branch 'feature/tf_workspaces' of bitbucket.org:levl/sparrow-infrastructure into feature/tf_workspaces\n\n\n\nbuild pipeline\n\nbuild\nchange image name for lambda\nbug fix\nrename v1_extracted_features_table to v1_lab_recordings_extracted_features_table\nadded fields : tag_list , directed\nadd user_agent source column\nupdate image\ninstall athena views\nWIP\nsparrow charts\nsparrow helm charts WIP\nWIP\nWIP\nWIP\nremove unused resources\nremove unused chart\nl2model\nfix\nWIP\nreturn of the firehose\nworking e2e\nhourly cron\nfirehose with dynamic paritioning\nupdate views to support multi platform etc\ninstall\nmerge\nremove duplicate columns\n\nMerged in MEROSP-839-capfp-changes-2 (pull request #9)\nadded capfp features\n\nadded capfp features\n\n\n\nMerged in fix-kafka-address-and-pipeline-name (pull request #10)\nFix kafka address and pipeline name\n\nupdating kafka address and pipeline name\n\nApproved-by: Itai Zolberg\n\n\n"}
{"title": "Adding features to views", "number": 14, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/14", "body": "adding platform invariant features to views\n\n"}
{"comment": {"body": "What is the integration process for this? Can tf be applied before data is updated?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/14/_/diff#comment-304776867"}}
{"comment": {"body": "it is applied already", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/14/_/diff#comment-304777142"}}
{"title": "align kafka address", "number": 15, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/15", "body": ""}
{"title": "Adding features to views", "number": 16, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/16", "body": "\n\nrenaming ax support\n\n"}
{"title": "added configs", "number": 17, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/17", "body": ""}
{"title": "Data catalog", "number": 18, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/18", "body": "WIP\nfeaturestore data catalog table\nrename jobid\nadd metadata back\nWIP\nrestore TLS_CERTS\ndata catalog v1\ncomplete data catalog firehose code\ndata tables\ndata catalog v1\n\n"}
{"title": "datacatalog schema", "number": 19, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/19", "body": ""}
{"title": "Dev", "number": 2, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/2", "body": "cd\nadd aws\naws configure\nmkdir\ntf 1.0.9\ndeploy\ncd\nartifact .terraform\nremove cat\ninit\nchange schema\nv2\nadd suffix _raw\nupdate extracted features scehma\ntraining model\nWIP\nupdate model view\nadjust tables location on s3\nWIP\nbug fixes\nsparrow RC1.0\nv1\nrename\nrename firehose stream\nadd missing fields operating_mode_notif,he_caps,rm_caps\nrename stream names to shorter\nremove timestamps temporary due to bug\nbug fix\n\n"}
{"title": "wip", "number": 20, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/20", "body": ""}
{"title": "add versioning to the device db  table", "number": 21, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/21", "body": ""}
{"title": "adjust tf to l2 model creation in data catalog", "number": 22, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/22", "body": "changed v1_typing_model_view\nadded v1_typing_model_tagged_view\n\n"}
{"title": "Deepak/pipeline", "number": 23, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/23", "body": "added bitbucket-pipelines.yml\nupdate branch name\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\ndockerfile created online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\ndockerfile edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nupdate files\nupdate yaml and dockerfile\nupdate docker file\nadded workspace\nupdate pipeline\nupdate release tag\n\n"}
{"comment": {"body": "please change that to dev branch.", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/23/_/diff#comment-347250731"}}
{"comment": {"body": "Please attach Jira issue  @{6371fc00f48fbd9b62d30647} ", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/23/_/diff#comment-347250816"}}
{"comment": {"body": "@{6371fc00f48fbd9b62d30647} when PR to dev you need to push code tha is \u201cproduction ready\u201d ", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/23/_/diff#comment-347250887"}}
{"comment": {"body": "@{6371fc00f48fbd9b62d30647} PR name must start with te correspondig JIA ticketd \\(so we have full tracability jira->git->jira\\)", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/23/_/diff#comment-347250971"}}
{"comment": {"body": "@{6371fc00f48fbd9b62d30647} missing feature: tf apply \\(cross environments\\)", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/23/_/diff#comment-347251447"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} @{606d973d3e6ea000685ed65f} Noted all the changes you have suggested. I am closing this PR and will raise another PR with branch name `MEROSP-1898` as per your suggestions and also will take care of other changes in that PR.\n\nLet me know for any issues.  \n", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/23/_/diff#comment-347262286"}}
{"title": "MEROSP-1898", "number": 24, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/24", "body": "added bitbucket-pipelines.yml\nupdate branch name\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\ndockerfile created online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\ndockerfile edited online with Bitbucket\nbitbucket-pipelines.yml edited online with Bitbucket\nupdate files\nupdate yaml and dockerfile\nupdate docker file\nadded workspace\nupdate pipeline\nupdate release tag\nFixed docker memory\nFixed docker memory\nRemoved memory and added var file\nAdded apply\nFinal changes\n\n"}
{"comment": {"body": "@{6371fc00f48fbd9b62d30647} missing deployment steps \\(sparrow-prod and sparrow-dev\\)", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/24/_/diff#comment-347276713"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} I have added the deployment step. I have only added for sparrow-dev `deployment` intentionally, once you review, I will add for sparrow-prod as well. Also only used feature branch to test out, will update for dev and master accordingly.\n\nAlso added the manual trigger for `apply` step. \n\nPlease review the pipeline and provide your feedback so that I can make the changes accordingly. \n\nThanks @{634e54561db4d2ebcf611e5a} for helping me out in this.", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/24/_/diff#comment-347901066"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} I am done with my final changes. I have tested the pipeline for dev and prod environments and seems to be working fine.\n\nI have made changes as per our discussion over the call related to branching strategy. But I have removed the `default` pipeline steps which were common to both dev. We can discuss on this if needed over call.\n\nRight now I have restricted it to `dev` and `master` branch. Let me know how you want to run it for other branches. \n\nPlease review and we should be good to merge it to `dev` branch. Let me know for any feedback.", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/24/_/diff#comment-348110616"}}
{"title": "MEROSP-2154 changes for sparrow-test env", "number": 25, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/25", "body": ""}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} did you mean sparrow-test env?  \ncan you add a little bit of description ? or link to issue?  \ni\u2019m missing the context", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/25/_/diff#comment-369255018"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} This PR first introduced to make sparrow infrastructure modular enough so that new environments like testing and staging can utilize existing code and easily create infra.\n\nLater the same branch was used to remove firehose, glue tables and introduce step functions as we discussed. I think this is the same thing you worked on test branch some time back. As we discussed, this is the new sparrow-infrastructure that should be referred to from now on and should be moved to dev.\n\nI have rebased the same branch and removed conflicts with dev branch. Could you please review this PR before it can be merged and a new infrastructure is created in new production account.", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/25/_/diff#comment-*********"}}
{"title": "fix timestamp", "number": 26, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/26", "body": ""}
{"title": "Change kinesis arn", "number": 27, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/27", "body": "fix timestamp\nremove day+hour from features\nrename cujo kinesis to dev\n\n"}
{"title": "Dev", "number": 28, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/28", "body": "\n\naws configure\ntf 1.0.9\ndeploy\nartifact .terraform\nchange schema\nadd suffix _raw\nupdate extracted features scehma\ntraining model\nWIP\nupdate model view\nadjust tables location on s3\nbug fixes\nsparrow RC1.0\nrename firehose stream\nadd missing fields operating_mode_notif,he_caps,rm_caps\nrename stream names to shorter\nremove timestamps temporary due to bug\nbug fix\nremove TBD features\nresolve conflicts\nreturn of the timestamp\ndev\nupdate processor image version\n\nMerged in feature/tf_workspaces (pull request #4)\nFeature/tf workspaces\n\nupdate image\nWIP\nadd target file\nworking flow\nlatest and greatest\nWIP\nbackfill WIP\nMerged in multi_platform_schemas (pull request #3)\n\nAdding platform type and config fields to schemas\n\nAdding platform type and config fields to schemas\nMerged feature/tf_workspaces into multi_platform_schemas\n\nApproved-by: Itai Zolberg * Merge branch 'dev' of bitbucket.org:levl/sparrow-infrastructure into feature/tf_workspaces\n\nMerge branch 'feature/tf_workspaces' of bitbucket.org:levl/sparrow-infrastructure into feature/tf_workspaces\n\n\n\nbuild pipeline\n\nchange image name for lambda\nbug fix\nrename v1_extracted_features_table to v1_lab_recordings_extracted_features_table\nadded fields : tag_list , directed\nadd user_agent source column\nupdate image\ninstall athena views\nsparrow charts\nsparrow helm charts WIP\nremove unused resources\nremove unused chart\nl2model\nreturn of the firehose\nworking e2e\nhourly cron\nfirehose with dynamic paritioning\nupdate views to support multi platform etc\ninstall\nmerge\nremove duplicate columns\n\nMerged in MEROSP-839-capfp-changes-2 (pull request #9)\nadded capfp features\n\nadded capfp features\n\n\n\nMerged in fix-kafka-address-and-pipeline-name (pull request #10)\nFix kafka address and pipeline name\n\nupdating kafka address and pipeline name\n\nApproved-by: Itai Zolberg\n\n\nMerged in merge_from_master (pull request #13)\nMerge from master\n\njobid\ngeneric pipline with query reading\nfix query\ntemplating\nchanged PIPELINE_CREATE_METHOD:\nfixed helms\nlatest\nlatest\nMerged in fix-kafka-address-and-pipeline-name-master (pull request #11)\n\nupdating kafka address and git ignore idea\n\nupdating kafka address and git ignore idea\n\nApproved-by: Shimon Goulkarov * Merge branch 'master' of bitbucket.org:levl/sparrow-infrastructure into merge_from_master\nApproved-by: Ophir Carmi\n\n\nMerged in fix-kafka-address-and-pipeline-name-2 (pull request #12)\nmore updating kafka address and fixing pipeline name\n\nfixed helms\nlatest\nlatest\nmore updating kafka address and fixing pipeline name\nchanged install shell script to py script, trying to fix l2model workers\nMerged in fix-kafka-address-and-pipeline-name-master (pull request #11)\n\nupdating kafka address and git ignore idea\n\nupdating kafka address and git ignore idea\n\nApproved-by: Shimon Goulkarov * Merge branch 'master' of bitbucket.org:levl/sparrow-infrastructure into merge_from_master\n\nMerge remote-tracking branch 'origin/merge_from_master' into fix-kafka-address-and-pipeline-name-2\nadded readme for install helm charts\nMerge branch 'dev' into fix-kafka-address-and-pipeline-name-2\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in adding-features-to-views (pull request #14)\nAdding features to views\n\nadding platform invariant features to views\nfixing install.py and l2model\nroll back\nadding ax_supported feature\n\nApproved-by: Tamir Raz\n\n\nuse hotfix_sparrow_bugfixes tag\n\nremove partitions from lab-recordings extracted features\nusing_local_model_files\nusing_local_model_files\n\nMerged in data_catalog (pull request #18)\nData catalog\n\nadd metadata back\nWIP\nrestore TLS_CERTS\ndata catalog v1\ncomplete data catalog firehose code\ndata tables\ndata catalog v1\nMerged in MEROSP-1509-datacatalog-firhose-kinesis- (pull request #20)\n\nwip\n\nwip\nkinesis source\nstream\n\nApproved-by: Ron Cohen * Merged in MEROSP-1506-datacatalog-glue (pull request #19)\ndatacatalog schema\n\ndatacatalog schema\n\nApproved-by: Ron Cohen * add kinesis access to firehose role\n\n\nMerged in MEROSP-1641 (pull request #21)\nadd versioning to the device db table\n\nadd versioning to the device db table\n\n\n\nadd new glue tables + firehoses\n\nalign with new schemas + kinesis\nalign with eros conf\nadded temp table for dror's usage until shimi code is integrated to the data catalog\nchange data catalog dp res to month\n\n"}
{"title": "multi-deployment", "number": 29, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/29", "body": "Multi-deployment changes"}
{"title": "Adding platform type and config fields to schemas", "number": 3, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/3", "body": "TODO:\n\nManual transformations\nManual update of model view.\n\n"}
{"title": "LDI-380", "number": 30, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/30", "body": "glue jobs ran periodically on accumulated amount of data (no bookmark was enabled) hence caused duplication of prior data"}
{"title": "LDI-469 backend changes for sparrow prd", "number": 31, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/31", "body": ""}
{"title": "Tagging Schema for TF", "number": 32, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/32", "body": ""}
{"title": "LDI-288 l2 typying model", "number": 33, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/33", "body": "Added Backend Configuration\ncreated glue and lambda resource\nadd glue job for l2\nadded glue table\n\n"}
{"title": "Custom Pipeline", "number": 34, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/34", "body": ""}
{"title": "LDI-948 Add repartition jobs v2.0 configuration, resolve LDI-380", "number": 35, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/35", "body": "Added versions, main differences with V1.0:\n\ndata_catalog_feature_store job uses STANDARD worker type instead of FLEX assuming this ETL is crucial for downstream jobs\nAutoscaling and job bookmarks enabled, which effectively resolves duplicates from LDI-380\nRole permissions are a bit limited from AWSGlueConsoleFullAccess to AWSGlueServiceRole which also later can also be reduced to only required glue and s3 permissions to specific jobs and paths.\nAdded Glue script parameters to control input/output data paths and database/table names as well as Group Size.\nGlue Scripts for now reside in  (see V2.0 folders)\n\nFeel free to request any changes according to established guidelines/approaches which I might have missed\n"}
{"comment": {"body": "This PR is disregarded in favour of [https://levltech.atlassian.net/browse/LDI-1003](https://levltech.atlassian.net/browse/LDI-1003){: data-inline-card='' } and corresponding TF modules, new PR will be created once it\u2019s decided on the TF approach and process", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/35/_/diff#comment-389452294"}}
{"title": "Added Tags for test environment", "number": 36, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/36", "body": ""}
{"title": "LDI-1040 Add job monitoring module invocation", "number": 37, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/37", "body": "Must be merged  AFTER {: data-inline-card='' } - contains simply invocation of monitoring module which creates all necessary resources"}
{"title": "LDI-165 sparrow_lab_recordings module for test ENV", "number": 38, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/38", "body": ""}
{"title": "LDI-1080 module infra design", "number": 39, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/39", "body": "Added l2-typing-model modules\nAdded v1 device model\nAdded v1 device model module\nAdded v1 device model module\nAdded v1 device model module\nAdded v1 device model module\nAdded v1 device model module\nAdded v1 device model module\nAdded Common Module\nAdded Common Module\nAdded Common Module\nAdded module for test env\nBitbucket Pipeline Changes\nBitbucket Pipeline Changes\nBitbucket Pipeline Changes\nAdded Terraform Modules\nAdded Terraform Modules\n\n"}
{"comment": {"body": "Can we add `terraform.tf` file in all modules specifying the provider and terraform version that this module supports? for example:  \n\n```\nterraform {\n  required_providers {\n    aws = {\n      version = \">= 2.7.0\"\n      source = \"hashicorp/aws\"\n    }\n  }\n}\n```\n\nThis would ensure that correct provider/terraform version is used and avoids automated updates which could result into issues due to breaking changes", "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/39/_/diff#comment-404650607"}}
{"title": "Feature/tf workspaces", "number": 4, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/4", "body": "workspaces\nvalidation flow\nWIP\nfixes\nWIP\nWIP\nWIP\nWIP\nupdate raw sessions schema\nWIP\nerrors prefix\nupdate lambda image\npersistance image\nbug fix\nWIP\nremove sorting\nWIP\nWIP\nWIP\nbug fixes\nupdate image\nWIP\nadd target file\nworking flow\nlatest and greatest\nWIP\nbackfill WIP\n\n"}
{"title": "Dev", "number": 5, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/5", "body": "cd\nadd aws\naws configure\nmkdir\ntf 1.0.9\ndeploy\ncd\nartifact .terraform\nremove cat\ninit\nchange schema\nv2\nadd suffix _raw\nupdate extracted features scehma\ntraining model\nWIP\nupdate model view\nadjust tables location on s3\nWIP\nbug fixes\nsparrow RC1.0\nv1\nrename\nrename firehose stream\nadd missing fields operating_mode_notif,he_caps,rm_caps\nrename stream names to shorter\nremove timestamps temporary due to bug\nbug fix\nremove TBD features\nresolve conflicts\nreturn of the timestamp\ndev\nupdate processor image version\n\nMerged in feature/tf_workspaces (pull request #4)\nFeature/tf workspaces\n\nupdate image\nWIP\nadd target file\nworking flow\nlatest and greatest\nWIP\nbackfill WIP\nMerged in multi_platform_schemas (pull request #3)\n\nAdding platform type and config fields to schemas\n\nAdding platform type and config fields to schemas\nMerged feature/tf_workspaces into multi_platform_schemas\n\nApproved-by: Itai Zolberg * Merge branch 'dev' of bitbucket.org:levl/sparrow-infrastructure into feature/tf_workspaces\n\nMerge branch 'feature/tf_workspaces' of bitbucket.org:levl/sparrow-infrastructure into feature/tf_workspaces\n\n\n\nbuild pipeline\n\nbuild\nchange image name for lambda\nbug fix\nrename v1_extracted_features_table to v1_lab_recordings_extracted_features_table\nadded fields : tag_list , directed\n\n"}
{"title": "Sparrow 1.0", "number": 6, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/6", "body": "cd\nadd aws\naws configure\nmkdir\ntf 1.0.9\ndeploy\ncd\nartifact .terraform\nremove cat\ninit\nchange schema\nv2\nadd suffix _raw\nupdate extracted features scehma\ntraining model\nWIP\nupdate model view\nadjust tables location on s3\nWIP\nbug fixes\nsparrow RC1.0\nv1\nrename\nrename firehose stream\nadd missing fields operating_mode_notif,he_caps,rm_caps\nrename stream names to shorter\nremove timestamps temporary due to bug\nbug fix\nremove TBD features\nresolve conflicts\nreturn of the timestamp\ndev\nupdate processor image version\n\nMerged in feature/tf_workspaces (pull request #4)\nFeature/tf workspaces\n\nupdate image\nWIP\nadd target file\nworking flow\nlatest and greatest\nWIP\nbackfill WIP\nMerged in multi_platform_schemas (pull request #3)\n\nAdding platform type and config fields to schemas\n\nAdding platform type and config fields to schemas\nMerged feature/tf_workspaces into multi_platform_schemas\n\nApproved-by: Itai Zolberg * Merge branch 'dev' of bitbucket.org:levl/sparrow-infrastructure into feature/tf_workspaces\n\nMerge branch 'feature/tf_workspaces' of bitbucket.org:levl/sparrow-infrastructure into feature/tf_workspaces\n\n\n\nbuild pipeline\n\nbuild\nchange image name for lambda\nbug fix\nrename v1_extracted_features_table to v1_lab_recordings_extracted_features_table\nadded fields : tag_list , directed\nadd user_agent source column\nupdate image\ninstall athena views\n\n"}
{"title": "Feature/devices db", "number": 7, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/7", "body": "fixed typos, aws athena named query instead of null_resource\ntypos\nuse naming\nwispr view\nlatest\ndhcp view\n\n"}
{"title": "added more capfp features", "number": 8, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/8", "body": ""}
{"title": "added capfp features", "number": 9, "htmlUrl": "https://bitbucket.org/levl/sparrow-infrastructure/pull-requests/9", "body": ""}
{"title": "Feature/notification integration", "number": 1, "htmlUrl": "https://bitbucket.org/levl/eros-incoming-webhook/pull-requests/1", "body": "add integration env\nadjust makefile\nfix rollout restart\n\n"}
{"title": "aws region as variable", "number": 2, "htmlUrl": "https://bitbucket.org/levl/eros-incoming-webhook/pull-requests/2", "body": ""}
{"title": "Add interfaces package", "number": 1, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/1", "body": "Created the interfaces package\nCollector runs interfaces, which naively writes generated sessions to kafka sessions topics.\nAdd black auto formatting on save\n\nImmediate TODO:\n\nAdd package publishing and updating to/from package repo, so it could be used as an artifact by other code repos.\n\nCopy the following changes to all other repos:\n\nblack formatting\nclone with git lfs\n\n\n\n"}
{"title": "backend endpoint 'api' domain name", "number": 10, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/10", "body": "backend endpoint 'api' domain name\n+x permissions for run_all.sh\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.8-slim-buster", "number": 100, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/100", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.8-slim-buster, as this image has only 85 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 571  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331803   | No Known Exploit   |\n|    | 571  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331813   | No Known Exploit   |\n|    | 571  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331818   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.9-slim-buster", "number": 101, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/101", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.9.9-slim-buster, as this image has only 85 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 571  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331803   | No Known Exploit   |\n|    | 571  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331813   | No Known Exploit   |\n|    | 571  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331818   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0rc1-slim-buster", "number": 102, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/102", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.10.0rc1-slim-buster, as this image has only 85 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 571  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331803   | No Known Exploit   |\n|    | 571  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331813   | No Known Exploit   |\n|    | 571  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331818   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.8-slim-buster", "number": 103, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/103", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.9.8-slim-buster, as this image has only 87 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 667  | Integer Overflow or Wraparound SNYK-DEBIAN10-EXPAT-2331813   | No Known Exploit   |\n|    | 571  | Buffer Overflow SNYK-DEBIAN10-GLIBC-2340915   | No Known Exploit   |\n|    | 571  | Buffer Overflow SNYK-DEBIAN10-GLIBC-2340923   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Kafka ssl support - completion", "number": 104, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/104", "body": "update kafka endpoints, vpn certs\nRequire ack from leader broker only; add optional higher verbosity from rdkafka; update dogfood certs; update endpoint config\nchange bootstrap commands order\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0rc1-slim-buster", "number": 105, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/105", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nagent/src/streamer_agent/hw/workstation/Dockerfile\n\nWe recommend upgrading to python:3.10.0rc1-slim-buster, as this image has only 88 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 571  | Buffer Overflow SNYK-DEBIAN10-GLIBC-2340915   | No Known Exploit   |\n|    | 571  | Buffer Overflow SNYK-DEBIAN10-GLIBC-2340923   | No Known Exploit   |\n|    | 571  | Buffer Overflow SNYK-DEBIAN10-GLIBC-2340923   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Feature/MEROSP-448 quic packets capture", "number": 106, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/106", "body": "As we continue to improve our typing procedure, this PR adds the ability to capture QUIC packets to gain more user agent data."}
{"title": "update_certs_2_7_10_13", "number": 107, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/107", "body": ""}
{"title": "Updated eg3-4 certificates", "number": 108, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/108", "body": ""}
{"title": "Average CPE Temperature field in event", "number": 109, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/109", "body": "This info will still be available as part of the physical data packets. This is the value we will want to use in the future (one reading per session is sufficient).\nSeparated temperature extraction and added rate limit on file reading (should avoid timely errors we experienced extracting temperatures). This is all done in a TempReader class. We use it in cfr stream parser and state manager."}
{"comment": {"body": "Do we have config for the temp reading measurments?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/109/_/diff#comment-280725585"}}
{"comment": {"body": "Currently not. Not sure it should be, it is enough to assume that the temperature doesn\u2019t change much in one second, while this interval is enough to spread out our reads. So I think it could be an hardcoded constant.", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/109/_/diff#comment-280832553"}}
{"comment": {"body": "What we should have is a configuration of the platform - it affects `kNumThermalZones` if it\u2019s hawkeye, and much more for more platforms.", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/109/_/diff#comment-280832753"}}
{"title": "Tap Config, Raw Session Send TS , Agent README", "number": 11, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/11", "body": "Update workstation configuration\nSession header will contatin the ts of the actual send time of the raw session object\nadd readme to agent\n\n"}
{"title": "Capture WLAN Beamforming data", "number": 110, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/110", "body": "Update filter to capture wlan action no ack messages carrying beamforming info."}
{"title": "Download AP secrets from AWS Secrets Manager", "number": 111, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/111", "body": "All of the APs OVPN and SSL (for MSK) secrets are now saved in AWS Secrets Manager. Those secrets are encrypted there using the aws/secretsmanager key from KMS.\nUpon setup.sh, the secrets are downloaded, saved to dest AP/folder, and cleaned from project tree. The user must have logged-in AWS credentials so that setup would work."}
{"title": "add missing dirs to save secrets in", "number": 112, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/112", "body": ""}
{"title": "Collect Raw Data on Ongoing & Disconnect", "number": 113, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/113", "body": "We now extract buffered packets on all activity events:\n\nConnection (-5 : +5 seconds, as it was)\nOngoing, triggered by HTTP (-5 : +1 seconds)\nDisconnection (-5 : 0 seconds)\n\n\n\nInstead of having build_event functions for each type, we have one function that receives the event type, time boundaries and validation function as arguments.\n\n\n"}
{"title": "Add Hawkeye Engenius AP", "number": 114, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/114", "body": ""}
{"title": "Fixed SecretsManager Region", "number": 115, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/115", "body": "Always access secretsmanager in ca-central-1"}
{"title": "Firewall updated to support both VPNs", "number": 116, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/116", "body": "Depending on boot process, tun0/tun1 iface names were assigned to Cluster VPN / On-Prem VPN randomly. In case tun1 got the CWQA VPN, firewall rules didnt apply to it and SSH wasnt available."}
{"title": "Firewall updated to support both VPNs", "number": 117, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/117", "body": "Last configuration could still result in rejection."}
{"title": "Platform type config", "number": 118, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/118", "body": "Update collector API with more platform types.\nUse platform type in sent event.\nUpdate american hawkeye config.\n\n"}
{"title": "collector-api 0.8.1 to match proto version 1.1", "number": 119, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/119", "body": "Some changes from ver 0.7.1 (schema 1.1) were not included in the generated py files. 0.8.1 will be aligned to events generated in current agent."}
{"title": "Removed Scapy Dependency", "number": 12, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/12", "body": "Due to GPL considerations"}
{"title": "add kafka send api with key for hashed paritions", "number": 120, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/120", "body": ""}
{"title": "Cujo updates with l1 removed - WIP", "number": 121, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/121", "body": "Update connection handler to extract interface data\nHandle wired connections based on ethernet triggers\nSave interfaces data on new connections, save phy_mode\nUpdate pb, update session manager event creation (field names, connected devices, interfaces data), fix bugs\nRemove prints\nremove physical code\n\n"}
{"comment": {"body": "@{5dbeb866c424110de52552cc} because we don\u2019t know the difference between wired and indirect connection, let\u2019s update the interfaces.proto enum to WIRED\\_OR\\_INDIRECT instead of WIRED", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/121/_/diff#comment-295597453"}}
{"comment": {"body": "move comment to line 83", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/121/_/diff#comment-295598601"}}
{"comment": {"body": "remove iostream", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/121/_/diff#comment-295598819"}}
{"title": "Fix some issues with connection extraction", "number": 122, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/122", "body": "Connection list was not line spearated as expected. This caused a wrong offset reading the input on certain cases.\nMode wasnt written to event.\nGet updated interface data on disconnections too.\n\n"}
{"comment": {"body": "nice!", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/122/_/diff#comment-298668187"}}
{"title": "Updated the kafka address after the change that was made this week", "number": 123, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/123", "body": ""}
{"title": "Cujo updates excluding schema", "number": 124, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/124", "body": "Update connection handler to extract interface data\nHandle wired connections based on ethernet triggers (ARP, ICMP_NS, DHCP)\nSave interfaces data on new or removed connections, including phy_mode that wasnt saved up till now\nUpdate session manager event creation (field names, connected devices, interfaces data)\nRemove L1 code almost entirely (all that is left is the readiness of the agents main to accept L1 data)\n\n"}
{"title": "Updating the kafka address again, after the recent change from the weekend", "number": 125, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/125", "body": ""}
{"title": "ingestion schema", "number": 126, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/126", "body": ""}
{"comment": {"body": "what is the meaning of \u201coneof\u201d here?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/126/_/diff#comment-302082944"}}
{"comment": {"body": "Each added \u201cparsed field\u201d  will only have one of the fields below. This allows saving space in the message.  \n[https://developers.google.com/protocol-buffers/docs/proto3#:~:text=allow%20extensions.-,Oneof,-If%20you%20have](https://developers.google.com/protocol-buffers/docs/proto3#:~:text=allow%20extensions.-,Oneof,-If%20you%20have){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/126/_/diff#comment-302964167"}}
{"title": "Feature/kafka headers", "number": 127, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/127", "body": "Add eg25\nadd kafka send api with key for hashed paritions\nfix code review comments\nadding kafka headers to api with key\n\n"}
{"title": "Feature/updated schema with key and headers", "number": 128, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/128", "body": "Last PR () didnt include some of the changes I thought were there.\n\nSend key and headers\nUpdate message creation to support IngestionEvent\n\nSo dev for now still sends old connection events, this branch will be merged to dev once classifier is error-free and ready to accept new ingestion events.\n"}
{"title": "Feature/MEROSP-911 get ipv6 from ack message and", "number": 129, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/129", "body": "ethernet handler: added is_dhcpv6_reply and unit tests\n\n"}
{"title": "Fix dictionary change during iteration problem", "number": 13, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/13", "body": "Removing a debug print that iterated over data that is changed in the streamers data collecting. We dont really need that print, and the current iteration is safe"}
{"title": "Add GLinet Agent Support", "number": 130, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/130", "body": "Add 2 GLinet configuration in rnd.yml (gl1 Artemis & gl2 Bumblebee).\nAdd cmake configuration for mips 74kc v5.\nUpdate build docker image to 0.8.5 (was updated on ECR to include MIPS 74kc v5 toolchain and target build). Build statically-linked libraries.\n\nSupport the following configuration changes for glinet:\n\nWIFI interfaces named wlan*\nDifferent PCIe paths included in wireless config\nVLANs on ethernet ports\ndnsmasq instead of odhcpd for DHCPv5 server\n\n\n\nInclude interfaces configuration as part of rnd.yml params instead of statically set in ap_config.json.\n\nAdjust extraction script and setup to support different architectures and driver sets (ArmV7 vs. MIPS74kc, Atheros and MAC80211).\n\n* Ignore all the files under cpe/utils/ipq_setup/config/envs/, they are automatically generated (Im not sure why we dont git ignore them).\n"}
{"comment": {"body": "replace with:  \n`const uint16_t offset = ((len & 0xFF) << 8) + (len >> 8) + kHdrLen;`", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/130/_/diff#comment-308379582"}}
{"title": "Fix Engenius Setup After GLinet Merges", "number": 131, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/131", "body": "Missing date executable for ipq6018\nDidnt use virtual AP in wireless config\nEngenius false demo port\n\n"}
{"title": "performaning stations extractor every 1 sec instead of every 100ms", "number": 132, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/132", "body": ""}
{"comment": {"body": "the title is wrong. instead we perform watchdog on extractors every 10 sec.", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/132/_/diff#comment-310316934"}}
{"title": "fixing bug of stoi of invalid input while parsing iw", "number": 133, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/133", "body": ""}
{"title": "fixing number of lines of parser", "number": 134, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/134", "body": ""}
{"title": "fixing indentation of bootstrap sh", "number": 135, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/135", "body": ""}
{"title": "Fix Interface Details Parsing", "number": 136, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/136", "body": "ESSID was unnecessarily read, caused error decoding channel"}
{"title": "Fix Interface Details Parsing Unit Test", "number": 137, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/137", "body": "mock cmd interface should have also been updated to extract only bssid and channel (no essid)."}
{"title": "removing obsolete files", "number": 138, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/138", "body": ""}
{"title": "Make sure that order of configuration doesn't affect the mode lookup", "number": 139, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/139", "body": "Order of uci show can apparently be shuffled. Use sort to make it fixed."}
{"title": "Stream recovery", "number": 14, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/14", "body": "Restart stream upon errors - to temporarily handle  or similar MemoryError exceptions.\nThe error occurs due to corrupted data read from the tcpdump stream. dpkt reads a pcap message header indicating a very large packet size, and soon enough tries to read it, which obviously breaks virtual memory boundaries, and thus the error. Given how pcap is structured, we cant recover iterating over it in the same run, so until we find where does the glitch originate from, we restart the subprocess.\n"}
{"title": "Fix unrecognized fields in wireless config", "number": 140, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/140", "body": "Don't set band in wireless config for platforms other than glinet*"}
{"title": "merge of two other PRs", "number": 141, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/141", "body": "Make sure that order of configuration doesn't affect the mode lookup\nDon't set band in wireless config for platforms other than glinet*\n\n"}
{"title": "add IPv4 DNS server for non-glinet only", "number": 142, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/142", "body": "We had issues with devices connected to engenius routers not having internet. It was probably due to the routers not publishing a DNS server. This should fix the issue."}
{"title": "update kafka endpoint", "number": 143, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/143", "body": ""}
{"title": "adding git lfs to make install", "number": 144, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/144", "body": ""}
{"title": "Agent Default Configuration Updates", "number": 145, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/145", "body": "change default max_after_connection_aggr_time_sec\nupdate kafka endpoint\n\n"}
{"title": "IPv6 enabled on all interfaces", "number": 146, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/146", "body": "We still had some issues with IPv6 being partially disabled. This change should fix that."}
{"title": "Add WS Discovery port to collect udp & tcp packets", "number": 147, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/147", "body": ""}
{"title": "Support 8 New GLiNet Routers", "number": 148, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/148", "body": "Support for 8 new GLiNet routers\nAdd base setup files for GLiNet to be copied befor setup.sh (since we don't have a full image)\n\n"}
{"title": "added prints when bssid and essid are empty", "number": 149, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/149", "body": "handle return value better"}
{"comment": {"body": "Maybe we can add some sleeps here between the iterations? Can it help the performane?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/149/_/diff#comment-322496325"}}
{"comment": {"body": "Why did you choose to break the `while (true)` instead of calling `read_func` until it returns \u201cI had enough\u201d?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/149/_/diff#comment-322499116"}}
{"comment": {"body": "because if there is a failure inside the read function, for example: bussid is empty, it will not return the error. and I don\u2019t like walrus", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/149/_/diff#comment-322502532"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} what do you say about this?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/149/_/diff#comment-322502666"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} \n\n![](https://bitbucket.org/repo/959qq9e/images/3867559862-200w.gif)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/149/_/diff#comment-322727086"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} @{5ed4f1de9a64eb0c1e78f73b} I think it\u2019s ok because these reads are bound by the allowance. So we have the main loop the epolls \\(and inherently sleeps if there\u2019s no new data\\), then we go over the different sockets and handle data from them **up to a limit governed by the allowance**, and then \u201cmanage the state\u201d. So in the bottom line, \\(1\\) state management is not starved and \\(2\\) the resource consumption is determined directly by the incloming data rate, not more.", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/149/_/diff#comment-322728321"}}
{"title": "makesure sts login is done to the respective aws_region and not to us-east-2", "number": 15, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/15", "body": "makesure sts login is done to the respective aws_region and not to us-east-2"}
{"title": "Add tools to communicate with the XB7 router and run the Cujo agent", "number": 150, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/150", "body": "Add a remote terminal serial python class to facilitate the ssh configuration.\nAdd a bash script that execute the command necesary to enable the ssh connection to the user's host machine.\nAdd a bash script that starts the Cujo agent on the router.\nAdd a bash script that register devices to the agent for their CSI data to be collected and reported.\nAdd a bash script to configure an tftp connection to transmit an image to flash the router with.\nAdd a python script that runs the enable shh script on the router and transmit script 3. & 4. so they can be executed by the user at will.  \n\n\n/XB7+Router+Bring-up\n"}
{"title": "MEROSP-1856: support in agent updated kafka headers with message type - hradcoded until schema impl", "number": 151, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/151", "body": ""}
{"title": "MEROSP-1856: support tags pipelines", "number": 152, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/152", "body": ""}
{"title": "Fix agent crash due to socket read errors", "number": 153, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/153", "body": "Don't exit on socket read errors. We assume the root cause for the issue is that the producer (extractor) crashed and closed the connection. The extractor side should always recover, so the agent shouldnt crash in this case. If the epoll loop will keep running, we should just continue reading data upon client re-connection."}
{"title": "Add recovery scripts for glinet", "number": 154, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/154", "body": "While we do have a full SDK for the GLinet AR750S platform, we havent patched the FW image the same way we did with Engenius routers. That is, were basing our agent on the original GLinet FW, supported by some missing files that once transferred, allow us to run the usual setup.sh.\nThis PR organizes this process to allow independent GLinet bring up. It is very useful when the router starts to act up and we want to bring it to a stable state.\nThe process defined here supports recovery from (1) brick; (2) factory; (3) Levl installed. Follow the insructions in the .md."}
{"title": "[Snyk] Security upgrade wheel from 0.36.2 to 0.38.0", "number": 155, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/155", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  551/1000  Why?* Recently disclosed, Has a fix available, CVSS 5.3  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-WHEEL-3092128 |  wheel: 0.36.2 - 0.38.0  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "[Snyk] Security upgrade setuptools from 56.1.0 to 65.5.1", "number": 156, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/156", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\ncodecov 2.1.11 requires coverage, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  441/1000  Why?* Recently disclosed, Has a fix available, CVSS 3.1  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3113904 |  setuptools: 56.1.0 - 65.5.1  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Feature/MEROSP-1790 m2 ingestion events conversion", "number": 157, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/157", "body": "Created new schemas (device fingerprints - for ongoing; updated ingestion events - for connection/disconnection).\nCreated class M2IngestionEventTranslator to translate the schemas from old ingestion event to both types of new events.\nSeparate channel and schema conversion, create class KafkaM2Translator that can translate an old kafka record fully (headers and bytes) using the KafkaRecord class\n\nMove KafkaRecord to be part of the collector API as it is the originates from the edge.\n\n\nAdd tools to convert between internally used KafkaRecord and kafka-pythons ConsumerRecord.\n\nAlso create a ProducerRecord that contains the minimal fields that are relevant to the message when using the producer's send().\n\n\n\nAdd tools to serialize and deserialize a KafkaRecord to/from file to be able to save all of the data needed to queue data into the system. This lets the user not worry about inferring the message type.\n\n\n\n\nBring back some previously removed files: capture.py, connection_event.py, features.py, interfaces.py, parsing.py, platform_types.py.\n\n\n"}
{"title": "[Snyk] Fix for 2 vulnerabilities", "number": 158, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/158", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  551/1000  Why? Recently disclosed, Has a fix available, CVSS 5.3  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3180412 |  setuptools: 56.1.0 - 65.5.1  |  No  | No Known Exploit \n  |  551/1000  Why?* Recently disclosed, Has a fix available, CVSS 5.3  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-WHEEL-3180413 |  wheel: 0.36.2 - 0.38.0  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Bugfix/LDI-337 Kafka Header Crash", "number": 159, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/159", "body": "Cujo added a new Kafka header struct, which we will fully support in the next sprint (very soon :slight_smile: ).\nFor now, well make sure not to crash upon receiving it, so we wont delay too much our testing and recordings.\nThanks @{5f82bf320756940075db755e}  :slight_smile:"}
{"comment": {"body": "please change all `'` to `\"`. it helps when searching for things in the repo. not needing to search `\"agent_serial\"` and `'agent_serial'`. Also in English there is no meaning of `'` as quotation marks in a sentence. I will add it to the lint of this repo. thought it is classifier", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/159/_/diff#comment-366122738"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/159/_/diff#comment-366124192"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} can you add UT with header that not expected and another with UT that fails on decode..\n\n\u200c\n\nTo make sure robustness in this side, it can be separate PR to promote this to merges in GDE", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/159/_/diff#comment-366175408"}}
{"comment": {"body": "Yes, good idea, I will in a separate PR", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/159/_/diff#comment-366182724"}}
{"title": "AWS code aretifact integration", "number": 16, "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/16", "body": ""}
{"comment": {"body": "@{6085103f5797db006947d59a} Is it ok that the repo is in us-east-1?", "htmlUrl": "https://bitbucket.org/levl/eros-data-collector/pull-requests/16/_/diff#comment-237142275"}}
