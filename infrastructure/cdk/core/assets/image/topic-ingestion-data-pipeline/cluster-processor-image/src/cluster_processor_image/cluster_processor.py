from pandas import <PERSON><PERSON>rame
from typing import List
import hdbscan
import pandas as pd
from sklearn.cluster import KMeans

from integration_utils.message import MessageInterface
from cluster_processor_image.cluster_types import EmbeddedDoc
from cluster_processor_image.dataframe_constants import DOC_COLUMN, CLUSTER_COLUMN, EMBEDDING_COLUMN, DOC_ID_COLUMN


class ClusterProcessor:
    def get_clusters(self, messages: [MessageInterface], embeddings: List[EmbeddedDoc]) -> DataFrame:
        pass


class HDBScanProcessor(ClusterProcessor):
    def get_clusters(self, messages: [MessageInterface], embeddings: List[EmbeddedDoc]) -> DataFrame:
        actual_embeddings = [a.embedding for a in embeddings]

        actual_docs = [a.message.get_text() for a in embeddings]
        actual_doc_ids = [a.message.get_id() for a in embeddings]
        hdb = hdbscan.HDBSCAN(min_samples=5, min_cluster_size=3, prediction_data=True).fit(actual_embeddings)

        # create matrix of [[doc, embedding, clusterId]]
        df = pd.DataFrame(
            {
                DOC_COLUMN: actual_docs,
                DOC_ID_COLUMN: actual_doc_ids,
                EMBEDDING_COLUMN: actual_embeddings,
                CLUSTER_COLUMN: hdb.labels_,
            }
        )
        df = df.query(f"{CLUSTER_COLUMN} != -1")  # Remove documents that are not in a cluster
        return df


class KMeansProcessor(ClusterProcessor):
    def get_clusters(self, messages: [MessageInterface], embeddings: List[EmbeddedDoc]) -> DataFrame:
        actual_embeddings = [a.embedding for a in embeddings]

        actual_docs = [a.message.get_text() for a in embeddings]
        actual_doc_ids = [a.message.get_id() for a in embeddings]
        model = KMeans(n_clusters=15, init="k-means++", max_iter=200, n_init=10)
        model.fit(actual_embeddings)
        # create matrix of [[doc, embedding, clusterId]]
        df = pd.DataFrame(
            {
                DOC_COLUMN: actual_docs,
                DOC_ID_COLUMN: actual_doc_ids,
                EMBEDDING_COLUMN: actual_embeddings,
                CLUSTER_COLUMN: model.labels_,
            }
        )
        return df
