import logging

from embedding_utils.embedding_generator import Embedding<PERSON>enerator
from embedding_utils.embedding_models import SafeDenseVector, SafeSparseVector, EmbeddingType
from vector_store_utils.vector_interface import VectorInterface


class TopicVector(VectorInterface):
    uuid: str
    cluster_id: int
    content: str
    topics: str
    dense_vector: SafeDenseVector
    sparse_vector: SafeSparseVector

    def __init__(
        self,
        repo_id: str,
        doc_uuid: str,
        doc_cluster_id: int,
        doc_content: str,
        doc_topics: str,
        embedder: EmbeddingGenerator,
    ):
        self.repo_id = repo_id
        self.uuid = doc_uuid
        self.cluster_id = doc_cluster_id
        self.topics = doc_topics

        # Add Metadata
        self.content = doc_content

        embeddings = embedder.get_embeddings(
            embedding_type=EmbeddingType.DOCUMENT,
            docs=[self.content],
        )

        self.dense_vector = embeddings.dense_vectors[0]
        self.sparse_vector = embeddings.sparse_vectors[0]

    def get_dense_vector(self) -> SafeDenseVector:
        return self.dense_vector

    def get_sparse_vector(self) -> SafeSparseVector:
        return self.sparse_vector

    def to_dict(self) -> dict:
        data = {
            "id": str(self.uuid),
            "values": self.dense_vector,
            "metadata": {
                # Indexed metadata
                "repo_id": self.repo_id,
                "cluster_id": self.cluster_id,
                # Not indexed metadata
                "file_content": self.content,  # TODO richie encrypt
                "topics": self.topics,  # TODO richie encrypt
            },
        }

        sparse_vector = self.sparse_vector
        if sparse_vector is not None:
            data["sparse_values"] = sparse_vector
        else:
            logging.warning(f"Skipping sparse vector upload.")

        return data
