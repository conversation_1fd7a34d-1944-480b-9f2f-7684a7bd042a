{"comment": {"body": "in next PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/902/_/diff#comment-343829972"}}
{"title": "MEROSP-2177 removing obsolete code and adding unit tests", "number": 903, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/903", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "setting threshold for unit test coverage", "number": 904, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/904", "body": "pipeline will fail if coverage is too low"}
{"title": "Performance improvments [NOT FOR MERGE YET]", "number": 905, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/905", "body": "Im using this branch for scale tests.\nthere are some changes that we would want to get in dev.\nplease follow and add comments"}
{"title": "fix build helm chart ci step and trigger step", "number": 906, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/906", "body": "\n\nfix build chart ci step and trigger step\n\n"}
{"title": "Bugfix/MEROSP-2194 mistakes in os version detec", "number": 907, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907", "body": "The OS version detection from user agents produced wrong results in some rare cases, detailed in .\nThe changes here aim to fix these issues by:\n\nDoing a case-sensitive match on the OS name\nAdding more limitations to the version detection regex (max 3 digits between dots, version must be followed by a sensible character like a space or semicolon, etc.)\nTranslating android API version to OS versions ()\nNot allowing the OS name match to be preceded by a dash (-) since in this case the following number is likely an app version rather than OS version.\n\n"}
{"comment": {"body": "please write a detailed description", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-344184539"}}
{"comment": {"body": "why is this method no static method?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-344185229"}}
{"comment": {"body": "if it is two columns csv it should be a json and you can read it into dict very easily", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-344187221"}}
{"comment": {"body": "ok", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-344210301"}}
{"comment": {"body": "Because it needs to call  `self.os_postprocessing `which relies on class variables.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-344210621"}}
{"comment": {"body": "I think this structure is easier to maintain since the key and value each have a clear header. \n\nA json with contents \\{\u201c21\u201d:\u201d5.0\u201d, \u201c22\u201d: \u201c5.1\u201d, \u2026\\} may be confusing as to what each number means.\n\nPlease let me know what you think on this aspect and if you have a suggestion on how to make a json more readable.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-344218965"}}
{"comment": {"body": "Regression tests passed [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8549](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8549){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-344219898"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} all simple two-column csv files are now read with the csv library instead of pandas", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-345161050"}}
{"comment": {"body": " I pressed changes requested because all methods should be static methods in user agent analyzer.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-345169595"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} I made it so everything is called as a `self.` property because each unit test defines the variables a bit differently so it would become complicated to use environment variables. To be sure: the static methods \\(that don\u2019t depend on other class methods or class variables\\) should be kept with `@staticmethod`or changed to have an unused `self` input for uniformity?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-347261487"}}
{"comment": {"body": "Passed regressions after merging with dev:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1839](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1839){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/907/_/diff#comment-349500262"}}
{"title": "Bugfix/MEROSP-2201 model typing.optionalstr   s", "number": 908, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/908", "body": "MEROSP-2201: fix model string instead of list and add ut for the use case\nNoticed we have the issue with new tags strategy, fixed in this PR as well:\n\nrelease tag will be set only when we create tag and the other will work as before\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8679](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8679){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/908/_/diff#comment-344271815"}}
{"title": "typo", "number": 909, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/909", "body": ""}
{"title": "Adding more labels to metrics for more accurate dashboards", "number": 91, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/91", "body": "\n"}
{"title": "Bugfix/push helm charts", "number": 910, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/910", "body": "There was a bug with git access , fixed + bumped eros-chart with the telegraf changes\n"}
{"title": "MEROSP-2218: fix tagging for deployments", "number": 911, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/911", "body": ""}
{"title": "MEROSP-2083 telegraf client not able to", "number": 912, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/912", "body": "cujo using /write at the end of their telegraf endpoint\n"}
{"comment": {"body": "very good!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/912/_/diff#comment-344567454"}}
{"title": "Bugfix/telegraf port", "number": 913, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/913", "body": "change telegraf port not collide with schema registry\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9136](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9136){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/913/_/diff#comment-344631750"}}
{"title": "change telegraf creds var to json", "number": 914, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/914", "body": "this var should pass as a JSON for it to work. The previous state was a bug."}
{"title": "MEROSP-2229", "number": 915, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/915", "body": "change telegraf creds var to json\nfix ci build and push charts bug\n\n"}
{"title": "Feature/MEROSP-2063 pipeline re organization", "number": 916, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/916", "body": "Introduction\nThis PR is stepping stone to achieving the following goals:\n\nSupport the new incoming event schema and parsed data format to enable an integration with Cujo\nRefactor code that is already affected by the change mentioned above in order to improve stability and modularity   \n\nThe change required to fulfill the aforementioned goals is a major one and can only be preformed under the condition of\ncontinuous system stability, i.e. unit tests and regression tests are passing, excluding those that become irrelevant under\nthe new introduced logic.  \nUnder those constraints, the changes made are by no mean a complete design or good enough solution, rather a compromise \nto bear with until the next PR. \nPlease find the details about the missing and incomplete parts in the TODO section.   \nMain Changes\n\n\n\nRefactor  Pipeline\n\nTake the first part, where the pipeline is created and the events are received and encapsulate the part of the communication channel where all the messages arrive. create a separation of the communication and the logic executed.\nTurn KafkaMsg to general EventMsg which is independent from the channel it arrives on. Change all the names and types of supporting functions in utils, test utils etc.\nTake the internal logic part of the pipeline and put in a different file in order to group together all the relevant functions. Rename functions as necessary. \n  Remove some update logic from the different event pipes function and put in the new file instead, but\n  still not completed as some updates still happen inside the match maker for example.\nChange device_creation_ts to actual meaning and add the ip indication to the session API.\nGroup all the logic of the pipeline in one file. Taking all the logic that resides inside the matching service, scattered in various file, find the commonality and use it to organize the logic in a more orderly and readable structure.\n  Renaming functions but making the maximal minimal step that keeps the system stable.\nIntroduce a new service that resolve attributes of a device in a session such as its IP address. \n  The logic was separated from the match maker which is why its unit tests are based on existing tests that used to belong to the match maker. \nTake the feature extraction process out of the type classifier. \n  Unify the API for all the service main function, but this require more work as the types that each service use internally is slightly different and probably not for justified reasons. \n  Will be refactored along a deeper change in the Feature class and all its derivatives.\n\n\n\nNew logic  Schema Integration\n\nTranslation of schema, from old schema to new, before the entry point to the pipeline logic, with a switch to enable/disable \n  this feature once Cujo provide us with native new-schema events.\n  Caused a refactor in test tools that generate sessions from various sources\nrerouting the parsing flow of incoming data, as it does not arrive fully raw, rather partially digested for certain packet types. \n  Raw change that should be refactored \nChanges in connection result and event classes to fit the new schema\n\n\n\nUnit tests  refactored to use the new API, skipped until removed or rewritten or moved to a more fitting suite\n\n\nMisc  bugs and default values\n\nFix bug, unit test dependency due to usage of global vault id.\nFix bug in comparison flags. The logic that return whether the details were updated returned false whenever one attribute was equal to another, which is the exact opposite of what is required.\nMake connection event None as default since the logic inside classifier relies on it in order to identify an occurrence of error\nMake default log list as empty list\n\n\n\nTODO\n\nRenaming of files, classes and functions.\n      Among those:  pipeline.py, EventMsg, convert_to_event_msg, new_parsing_logic.py,system_msg.pyetc.\nEncapsulation of the parsing process, cleaning irrelevant tests and repurposing those that exist.\nCompletion of the support of all types of parsed data  \nReturn test coverage: parsing unit tests and old schema processing tests were skipped\nComplete the transfer of all the peripherals of the system such as save debug data to the new schema\nInsert logic to the pipeline that closes the gap between missing data that does not arrive in a going event\nInsert a layer of abstraction between the pipeline logic and the event messaging format that will allow future changes\n  to remain closed in one place.  \n\n"}
{"title": "[Snyk] Security upgrade setuptools from 39.0.1 to 65.5.1", "number": 917, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/917", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\ndataclasses-avroschema 0.26.1 requires fastavro, which is not installed.\ndataclasses-avroschema 0.26.1 requires dacite, which is not installed.\ndataclasses-avroschema 0.26.1 requires inflect, which is not installed.\ndataclasses-avroschema 0.26.1 requires faker, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  441/1000  Why?* Recently disclosed, Has a fix available, CVSS 3.1  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3113904 |  setuptools: 39.0.1 - 65.5.1  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "[Snyk] Security upgrade setuptools from 39.0.1 to 65.5.1", "number": 918, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/918", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nhelm_deployment/requirements.txt\n\n\n\nWarning\n```\nkubernetes 10.0.1 requires pyyaml, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  441/1000  Why?* Recently disclosed, Has a fix available, CVSS 3.1  | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3113904 |  setuptools: 39.0.1 - 65.5.1  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "MEROSP-2244 fixing misc. stuff that pycharm code inspection alerts on", "number": 919, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/919", "body": "fixing misc. stuff that pycharm code inspection alerts on\nregression passed (almost):\n{: data-inline-card='' }"}
{"comment": {"body": "What were its concerns here?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/919/_/diff#comment-346122407"}}
{"comment": {"body": "default values are evaluated only once - on the init of the system. As opposed to the common thought - that they are evaluated on each call to the function.  \nthe fix here does not affect the functionality \\(because the global vars are evaluated only once\\), but it is a good practice to not use mutable default values.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/919/_/diff#comment-346149189"}}
{"title": "AWS code aretifact integration", "number": 92, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/92", "body": ""}
{"title": "Feature/MEROSP-2063 pipeline re order", "number": 920, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/920", "body": "Reorder pipeline\n\nTake the first part, where the pipeline is created and the events are received and encapsulate the part of the communication channel where all the msgs arrive. create a sapparation of the communication and the logic executed.\nTurn KafkaMsg to general EventMsg which is independent from the channel it arrive on. Change all the names and types of supporting fuinctions in utils and test utils etc.\nTake the internal logic part of the pipeline and put in a different file in order to group together all the relevant functions. rename functions as necessary. remove some update logic from the different event pipes function and put in the new file instead. still not completed as some updates still happen inside the match maker for example.\nChange device_creation_ts to actual meaning and add the ip indication to the session API.\nMake default log list as empty list\nGroup all the logic of the pipeline in one file. Taking all the logic that resides inside the matching service, scattered in various file, find the commonality and use it to organize the logic in a more orderly and readable structure. Renaming functions but making the maximal minimal step that keeps the system stable.\nIntroduce a new service that resolve attributes of a device in a session such as its IP address. The logic was separated from the match maker which is why the unit tests are based on existing tests that used to belong to the match maker. 3.Take the feature extraction process out of the type classifier. Unify the API for all the service but this require more work as the types that each service use internally is slightly different and probably not for justified reasons. Will be refactored along a deeper change in the Feature class and all its derivatives.\nFix bug, unit test dependency due to usage of global vault id.\nFix bug in comparison flags. The logic that return whether the details were updated returned false whenever one attribute was equal to another, which is the exact opposite of what is required.\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/latest/9660](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/latest/9660){: data-inline-card='' } regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/920/_/diff#comment-345771406"}}
{"title": "MEROSP-2290 Don't force bucket name suffix", "number": 921, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/921", "body": "We need to have the ability to control the bucket were creating instead of **.levl.datalake\n"}
{"comment": {"body": "did you changed it everywhere needed?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/921/_/diff#comment-346143992"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/921/_/diff#comment-346149171"}}
{"title": "MEROSP-2238 reducing log level of several packet parser warnings", "number": 922, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/922", "body": "so the log won't be spammed\nregression passed:\n{: data-inline-card='' }"}
{"title": "export list of ipv6", "number": 923, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/923", "body": "with a limit of max number of addresses: 6\nregression passed:\n{: data-inline-card='' }"}
{"comment": {"body": "Looking good :slight_smile: Can you please update the new ipv6 field type [here](https://levltech.atlassian.net/wiki/spaces/~************************/pages/8278933564/DB+Schema+-+Device+Class)?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/923/_/diff#comment-346571136"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/923/_/diff#comment-346574542"}}
{"title": "adding typing hints for return values of functions", "number": 924, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/924", "body": "and a small refactor of packet_parser function\nregression passed:\n{: data-inline-card='' }"}
{"comment": {"body": "@{6252eba45d1e700069ad0104}   \nMaybe integrate that after Liat? or Liat, maybe you take these changes first?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/924/_/diff#comment-347253854"}}
{"comment": {"body": "I think it\u2019s better to merge these changes to my branch, then merge my PR and then this one.   \nWhat do you think?  \nThe other option is to merge this PR first and then mine but I am afraid that git will force me to re-merge most of the files\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/924/_/diff#comment-347259583"}}
{"comment": {"body": "Why the tests are removed?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/924/_/diff#comment-347468843"}}
{"comment": {"body": "because no one is using sparrow forwarder and it is broken", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/924/_/diff#comment-347485598"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} , @{6085103f5797db006947d59a}  - is the sparrow forwarder pipeline being run?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/924/_/diff#comment-347697803"}}
{"comment": {"body": "nevermind, I fixed sparrow forwarder", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/924/_/diff#comment-*********"}}
{"title": "Feature/MEROSP-2004 parsing new data format", "number": 925, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/925", "body": "\nNew logic  Schema Integration\n\nTranslation of schema, from old schema to new, before the entry point to the pipeline logic, with a switch to enable/disable\n\nthis feature once Cujo provide us with native new-schema events.\n2. Rerouting the parsing flow of incoming data, as it does not arrive fully raw, rather partially digested for certain packet types.\n3. Changes in connection result and event classes to fit the new schema\n4. Add parsing backward compatibility\n\nA patchy solution, call the old logic or the new one based on a boolean. The connection result and event objects hold both the raw data and new formatted di messages.\nSupport Cujo's HTTP UA-only format\n\n"}
{"title": "Feature/MEROSP-2003 context gap in schema", "number": 926, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/926", "body": "Integrate the actual DeviceFingerprints schema\nThe DeviceFingerprints schema differ from the IngestionEvent schema by certain data \nthat the former does not posess. This data relate to the context of a device connection,\nsuch as the platform type it connected from and the frequency band used. \nSince this data is necessary along the pipeline when classifying a device for example, we\nclose this gap by saving this data as additional fields in the device that we encountered\nduring the preceding connection event and update the session from the device when\nprocessing an ongoing event. This solution is purely functional and can be improved.\n"}
{"title": "added dec to telemtric client", "number": 927, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/927", "body": ""}
{"comment": {"body": "who is using this? can the name of the function be more understandable?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/927/_/diff#comment-347431503"}}
{"comment": {"body": "it is an api implementation we copied from `prometheus` \n\n[https://github.com/prometheus/client_python#gauge](https://github.com/prometheus/client_python#gauge){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/927/_/diff#comment-347439937"}}
{"comment": {"body": "Im using it in another branch and got an exception when tried to use dec ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/927/_/diff#comment-347441367"}}
{"comment": {"body": "report portal is not working but regression passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/927/_/diff#comment-347441571"}}
{"comment": {"body": "@{6085103f5797db006947d59a} In terms of brevity, can you just add descriptions to the functions if we\u2019re imitating some public API?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/927/_/diff#comment-347703494"}}
{"title": "MEROSP-2358 return typing hints", "number": 928, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/928", "body": "regression passed, in allure"}
{"title": "override export logs asdict func", "number": 929, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/929", "body": "As part of LENS integration, we are expected to pass timestamp as epoch formant (ms) integer."}
{"comment": {"body": "very nice work,  well done!!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/929/_/diff#comment-347771702"}}
{"comment": {"body": "Are those objects' `asdict` used only when we serialize them for the client?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/929/_/diff#comment-347783341"}}
{"comment": {"body": "I suggest another improvement to avoid having these functions be aware of specific field names. Can we make the objects inherit from/add a mixin of a class that implements an `asdict` that converts all `datetime` fields?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/929/_/diff#comment-347784037"}}
{"comment": {"body": "Yes, it's mainly used for Kinesis Firehose \\(treats them as jsons\\) ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/929/_/diff#comment-347841517"}}
{"comment": {"body": "Converting this field doesn\u2019t mean that we would convert other fields of type Datetime, if this will be the case in the future we should do it ,agreed.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/929/_/diff#comment-347849077"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} Got it. My only concern that this very specific \u201chack\u201d will not be understood in the future, so can you please just add comments explaining the motivation?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/929/_/diff#comment-348049110"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Sure!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/929/_/diff#comment-348115417"}}
{"title": "Feature/scoring features", "number": 93, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/93", "body": "Now, each feature has a score related to it. If needed, well choose the device with the highest score as our matching result"}
{"title": "MEROSP-2364 more typing hints", "number": 930, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/930", "body": ""}
{"title": "scale branch changes", "number": 931, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/931", "body": "basically changes that helps to monitor\n+ few bugs we had in logging config, configuration loading\n+ dynamo flag to skip mac query\n+ improvements in docker build mechanism(not going to help in Bitbucket pipelines but helps locally and will work great in Jenkins\n+ different kinds of flakes.\n"}
{"title": "MEROSP-2419 - Bring back profiling metrics to match()", "number": 932, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/932", "body": "Profiling and metrics decorators were removed at some point, but we still want to keep track of them."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/117](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/117){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/932/_/diff#comment-347900894"}}
{"title": "MEROSP-1297 hostname classification", "number": 933, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/933", "body": "Updated hostname logic without ML classifier\n"}
{"title": "Bugfix/MEROSP-2426 classifier crash on disconnection event", "number": 934, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/934", "body": "We had a bug in our disconnection pipeline when we got a disconnection event without a connection event prior to it. In that case, we didnt get any matching device from the DB, and kept going with the code as usual, assuming that the device is valid."}
{"title": "run local regression without report portal", "number": 935, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/935", "body": "Why do want to run default without report portal?\nOphir: @{5f82bf320756940075db755e} fixed\nThanks!"}
{"comment": {"body": "It\u2019s fine by me that this is the default configuration, can you just add another make target for running _with_ reportal?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/935/_/diff#comment-347781154"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/935/_/diff#comment-347783719"}}
{"title": "Bugfix/MEROSP-1855 - align the latest rp uuid", "number": 936, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/936", "body": "\n\nMEROSP-1855: align the latest rp uuid\n\n"}
{"title": "adding new line at the end of jsons", "number": 937, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/937", "body": ""}
{"title": "Bugfix/MEROSP-2421 classifier crash - Quic", "number": 938, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/938", "body": "We got an exception when parsing a Quic packet. Unfortunately the session itself was not saved, and this scenario was not re-produced since. In order to avoid this of crash again, I added a try-except section to protect us. In addition, a debug print was added to help us investigate the problem even further, if (when) we see it next time.\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/216](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/216)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/938/_/diff#comment-348054580"}}
{"title": "removing obsolete sparrow_forwarder", "number": 939, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/939", "body": "@{5fd5d5149edf2800759cc96d} said its ok\nregression passed:\n{: data-inline-card='' }"}
{"title": "Merge dev to master 07 21", "number": 94, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/94", "body": "\n\n\nMerged in feature/metrics_labeling (pull request #91)\nAdding more labels to metrics for more accurate dashboards\n\nAdding more labels to metrics for more accurate dashboards\nfix matchmaker metrics labels\nFix labels example\n\nApproved-by: Ariel Tohar\n\n\nMerged in feature/scoring_features (pull request #93)\nFeature/scoring features\n\nMerge branch 'dev' of  into feature/scoring_features\nHard coded disabling some features to see if we decide correctly, will be reverted shortly after the test\nflake\nBrought back hostname\nForcing using the scoring logic\nmatch_dhcp_requested_ip -> match_dhcpv4_handshake\nDebug prints\nDebug prints\nAdded score to prev rand mac feature as well\nBrought back all original logic\n\nApproved-by: Shimon Goulkarov\n\n\n"}
{"title": "more typing hints and raising coverage th", "number": 940, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/940", "body": ""}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/940/_/diff#comment-348145996"}}
{"title": "kinesis to support multi line record - NOT FOR MERGE", "number": 941, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/941", "body": "As part of the integration steps with Cujo, our kinesis should send messages as an aggregate batch of X messages this code will:\n\nSlice the messages list to X kinesis acceptable batches\nevents will be  separated with a new line between each message\nPut to kinesis only desired batch size record\nAdd buffer flush before closing the process to prevent data loss in case of a crash.  \n\nTHIS IS A WIP\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} please add unit test", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/941/_/diff#comment-348335776"}}
{"title": "Fix assignment to identity_is_levl_id_dedup in result log", "number": 942, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/942", "body": "Apperantly this flag was always set to false. As long as the list of deduplicated devices non-empty, this flag should be set to true."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/544](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/544){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/942/_/diff#comment-348353354"}}
{"title": "Bugfix/MEROSP-2468 dogfood crash - None fields", "number": 943, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/943", "body": "We had a crash yesterday in the dogfood that happened when we updated the DB schema with new fields. When we got an ongoing event for a device that was created in the system without those fields, we tried to move on with our logic with those fields as None (their default value), resulting in failures later on when we tried to access them.\nThis PR comes to protect us in those cases - if we extract a device from the DB and we found some mandatory fields with None values, well drop that ongoing event and not continue with it."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/607?item0Params=page.sort%3Dstatus%252CASC](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/607?item0Params=page.sort%3Dstatus%252CASC){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/943/_/diff#comment-348370393"}}
{"title": "add active schema translation var", "number": 944, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/944", "body": ""}
{"title": "more typing hints and removing obsolete code", "number": 945, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/945", "body": "regression passed (almost):\n"}
{"title": "Bring back kafka structs for backwards compatability", "number": 946, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/946", "body": "Some structs were moved and are added here to not break the interface of eros-api users."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/818](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/818){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/946/_/diff#comment-348505399"}}
{"comment": {"body": "we need to decide whether this is a singleton or not. currently there is a mix here", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/946/_/diff#comment-348983406"}}
{"comment": {"body": "You\u2019re right. The only reason it\u2019s here is because we want to keep backward compatability so I don\u2019t want to modify it. We should eventually have a plan to deprecate it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/946/_/diff#comment-349008854"}}
{"title": "Take care of a translation error over a new ingetsion event that gets translated", "number": 947, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947", "body": "Catch the exception when trying to translate an event that is not translatable. \n  Create an empty EventMsg that will be detected in the pipeline while it is being processed.\nEnsure the save debug data event type is correct for future saved events\nAdd a unit test with corresponding env file and calls in the makefile. \n\n"}
{"comment": {"body": "We should also fix general exception in case the pipeline fails and not caught, otherwise it will continue to stop the main thread\n\n```\n    def start(self):\n        open_telemetry_client.start()\n        while self._run_flag:\n            records = self._communication_channel.poll()\n            if records:\n                self._logic_to_execute.emit(records)\n\n        open_telemetry_client.join()\n        logging.info(\"Stream Processing Runner Done!\")\n```\n\n\u200c\n\n```\n        runner = StreamProcessingRunner(source, com_channel)\n\n        runner.start()\n    except Exception as e:\n        logging.fatal(e, exc_info=True)\n        raise e\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-348685884"}}
{"comment": {"body": "I think we should discuss the consequences of silencing every error.   \nIt is true that this will prevent runtime exceptions and will keep the system up and running,   \nbut at the price of having less to no awareness about errors that may indicate a dysfunctional  system.  \nIMHO we should take care of the error handling in the system as a part of a larger change that divide the   \nsystem to closed modules with fine grained errors and a designated pipeline to catch, report and deal with   \nthose errors.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-348996667"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} It\u2019s not silencing errors, it\u2019s not crashing and stopping the classifier, which is critical problem. all errors should be added to metrics and error log for further handling and investigation/fix, but in no way we can allow the classifier to crash/stop working. system is not operational.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-348998113"}}
{"comment": {"body": "I added a catch block for each record processing.  \nIf an error will occur, the system will continue to process the next record.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-348999325"}}
{"comment": {"body": "in previous impl, we had also metrics both for such exceptions, let\u2019s bring those back\n\n\u200c\n\n```\n        except Exception as e:\n            count_kafka_polling_exceptions.labels(tenant_name=_tenant_name).inc()\n            logging.exception(e)\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-348999971"}}
{"comment": {"body": "This is not a Kafka polling exception, that error is being updated inside the poll method in line 236. Another metric should be used here, for general pipeline exception.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-349002837"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Ofcourse, didn't mean to use same one. just notice, that before we had one and now not, so we need to add a metric for this case..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-349003351"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/latest/1244](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/latest/1244){: data-inline-card='' } Regression passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-349005087"}}
{"comment": {"body": "@{5f82bf320756940075db755e} OK", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-349005148"}}
{"comment": {"body": "@{5f82bf320756940075db755e} When I went back to the previous version, I found the following  code  \nwhich wraps the emit function with a try except and then increase the ` count_kafka_polling_exceptions.labels(tenant_name=_tenant_name).inc()`, so following @{5dbeb866c424110de52552cc} 's suggestion, I will put a new metric `pipeline_fatal_errors_counter `definition and increase it instead of the old metric.  \n\n```\n\n    def start(self):\n\n        open_telemetry_client.start()\n\n        while self._run_flag:\n\n            start = time.time()\n\n            end = 0\n\n            records = []\n\n            try:\n\n                msg = self._consumer.poll(timeout_ms=_poll_interval * 1000)\n\n                if msg is not None:\n\n                    for consumer_record in msg.values():\n\n                        for record in consumer_record:\n\n                            headers = KafkaHeaders(**{k: v.decode(\"utf-8\") for k, v in record.headers})\n\n                            records.append(KafkaMsg(msg=record.value, headers=headers))\n\n                    count_started_batch_size.labels(tenant_name=_tenant_name).set(len(records))\n\n                    count_kafka_polling.labels(tenant_name=_tenant_name).inc()\n\n                    if records:\n\n                        self._stream.emit(records)\n\n                    else:\n\n                        logging.debug(f\"no messages {msg}\")\n\n            except Exception as e:\n\n                count_kafka_polling_exceptions.labels(tenant_name=_tenant_name).inc()\n\n                logging.exception(e)\n\n            finally:\n\n                logging.debug(f\">>> wait {start - end}\")\n\n                end = time.time()\n\n                logging.debug(\n\n                    f\">>> pace {len(records) / (end - start)} latency {end - start}  batch size {len(records)} \\\n\n                    batch_size {self._max_batch_size} poll_interval {self._poll_interval}\"\n\n                )\n\n                kafka_poll_batch_latency.labels(tenant_name=_tenant_name).observe(end - start)\n\n        open_telemetry_client.join()\n\n        logging.info(\"Stream Processing Runner Done!\")\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-349006545"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/latest/1265?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/latest/1265?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED){: data-inline-card='' } Regression almost passed, except for an allure exception", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/947/_/diff#comment-349009199"}}
{"title": "Create valid default values in agent info for any possible usage", "number": 948, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/948", "body": "Having None default values can cause problems when casting values to known classes (enums and such)."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/920](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/920){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/948/_/diff#comment-348982075"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} if we have real bug or somehow the platform type will remain unknown, how the system will behave? we should be very careful adding those defaults because of translation entries..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/948/_/diff#comment-348993737"}}
{"comment": {"body": "If the platform info will remain unknown, we will keep dropping sessions, and parsed data only, with no patform info, will be exported to the data catalog. It shouldn\u2019t remain unknown though, as the next connection event should fill it. If we want to have better input sanitation, it\u2019s a much bigger task that should be approached from a wider scope, I don\u2019t want to patch that.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/948/_/diff#comment-348995066"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} exactly, we have to make sure we are dropping unknown platform type and add metric and not just supporting backward compatability. since you adding in the enum, it\u2019s just declaring that you are supporting this, and it\u2019s not the case. it should be unknown and dropped if not get real type.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/948/_/diff#comment-348995263"}}
{"comment": {"body": "@{5f82bf320756940075db755e} The question is whether we still want to keep already parsed data from events that aren\u2019t being fully processed. We eventually agreed that we do. We should definitely add a metric for disconnection and ongoing event that arrive with no prior connection event.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/948/_/diff#comment-349001076"}}
{"title": "Feature/MEROSP-2324 periodic device deduplication", "number": 949, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/949", "body": "TL:DR\nTrigger the match making service every th device connection event, so it will perform a de-duplication, even if the MAC address exist in the DB.\nThe same is true if a certain amount of time passed between the current connection and the last one.\n\n\nAfter changing the pipeline logic to skip the matching process in case the device mac address is found in the database, we lost the ability to detect cases of device duplication models, e.g. in the case of band hops.\n  In order to ensure those duplicates models are found and reduced to one device, an additional logic was added to the pipeline and to each device.\n  The changes consist of the following:\n  1. Add a connection counter to each new device. This counter is updated every time the pipeline retrieve the device from the database when it connects.\n  2. Add a configurable env variable to decide the number of connections that will trigger the de-duplication process.\n  3. Add a unit test and designated env file and change the makefile\n  4. Add a time stamp field to the device that gets updated every call to matching on the device\n  5. Add another trigger in the pipeline to the de duplication in case the time interval between the current matching and the last\n  one is greater than the defined limit\n  6. Add unit test and the configuration parameter which is set to default 24H but if a negative or zero value is inserted, will receive a\n  value of 1 second.\n\n"}
{"title": "database versioning", "number": 95, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/95", "body": ""}
{"comment": {"body": "@{6085103f5797db006947d59a} looks good! Please add example snippet in code, folder examples\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/95/_/diff#comment-237380891"}}
{"title": "more typing hints", "number": 950, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/950", "body": "regression passed:\n"}
{"title": "Fix crash in classifier when an icmp ns packet is sent with an empty ip", "number": 951, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/951", "body": "Change the logic that calls the wrong function when the ip is emty. This should happen only when the ip is None and not when it is an empty binary string.\nAdd a verification inside the icmp ns parsing that will report this case using a new system error about required yet missing data.\nAdd a unit test that will be skipped until the transition to new schema is completed. The reason for this is that if the event is injected with the current system configuration, the test will always fail since the system will attempt to translate it even though it is already in the new schema format\nAdd an explicit unit test that calls the parsing function\n\n"}
{"comment": {"body": "does it packet parser limitation or icmp ns feature? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/951/_/diff#comment-349296948"}}
{"comment": {"body": "Look at line 36, we can only use addresses other than `::`. So the parse needs an address and the payload itself.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/951/_/diff#comment-349316561"}}
{"comment": {"body": "And this is specific to some packet types, icmpv6 among them \\(mdns hostname is also required to depend on ip\\).", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/951/_/diff#comment-349316995"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2366](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2366){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/951/_/diff#comment-349886684"}}
{"title": "typing hints", "number": 952, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/952", "body": "regression passed:\n{: data-inline-card='' }"}
{"comment": {"body": "@{5dbeb866c424110de52552cc} @{6265307b185ac200692f9bd9} We are in development freeze. Only selected bugs/features will be merged until dev is stable back.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/952/_/diff#comment-349724119"}}
{"title": "spelling", "number": 953, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/953", "body": "API: {: data-inline-card='' }\nSmoke: {: data-inline-card='' }\nregression: {: data-inline-card='' }"}
{"comment": {"body": "@{5dbeb866c424110de52552cc} @{6265307b185ac200692f9bd9} We are in development freeze. Only selected bugs/features will be merged until dev is stable back.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/953/_/diff#comment-349724071"}}
{"title": "MEROSP-2501 update models with new devic", "number": 954, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/954", "body": "added new Apple TV\nfixed minor tvOS\n\n"}
{"comment": {"body": "AppleTV [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2099](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2099){: data-inline-card='' } passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/954/_/diff#comment-349640801"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} you are welcome to merge your PR, once you have updated classifier as well.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/954/_/diff#comment-349725213"}}
{"title": "Don't drop valid events with no ARP/DHCP ACK", "number": 955, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/955", "body": "The connection pipeline expects to have some kind of IP indication in the parsed data. That indication is expressed in having at least 1 DHCP ACK or ARP message.\nIn the updated (M2) ingestion events, those 2 packet types arent supported. So we are left with no IPv4 resolution (and no IPv6 resolution, but thats to be handled in another issue). Thus the event handling is terminated and we move on to the next event.\nThe IP indication check is (at least) temporarily removed.\nTODO:\n\nAn Ethernet data health minimum threshold should be defined, such that we dont process event with high chance of false results.\nAn IP resolution mechanism should be implemented.\nBring back skipped tests.\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2190](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2190){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/955/_/diff#comment-349804754"}}
{"comment": {"body": "Why those specific unit test are failing?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/955/_/diff#comment-349807597"}}
{"comment": {"body": "Because they specifically test events with no ethernet data that are expected to return an error log with the no IP indication label.\n\nWhen we add an alternative IP resolution mechanism we can restore these. We should discuss what\u2019s a proper minimal data health metric for Ethernet packets and add other tests too.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/955/_/diff#comment-349808623"}}
{"title": "Accept more varied platform type strings", "number": 956, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/956", "body": "Unexpected platform type string was received from Cujo. We force the received string to be identical to our internal platform type enum naming. While the string shouldnt have changed, some conversion should be made to separate internal and external names.. \n\nA mapping was added between potential string names received in the event to our internal platform type enum. \nIf the string dont point to a valid enum, we resort to ENG_IPQ6018 and increase a metric.  \n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2204](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2204)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/956/_/diff#comment-349808796"}}
{"title": "forgot to return on error", "number": 957, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/957", "body": ""}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} This is following your change, note.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/957/_/diff#comment-349869332"}}
{"title": "MEROSP-1518 Added a test that checks the problem in that issue", "number": 958, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/958", "body": "After merging the new logic that @{5d74d49897d8980d8eacd7f8} added to the hostname feature, it turned out that some old bugs were solved thanks to those algorithmic improvements :)\nThis PR adds a test that checks the problem we had in MEROSP-1518."}
{"comment": {"body": "Passed local regression :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/958/_/diff#comment-350329088"}}
{"title": "MEROSP-1297 hostname classification - ML stage", "number": 959, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/959", "body": "added ML model\nhostname_ml_classifier.py - the ml classifier wrapper code to be used by the hostname module\nml_classifier.pkl - the trained sklearn classifier\nml_vocabulary.json - the vocabulary for the string vectorizer (may change to csv containing only the strings without the integers)\n\n\n"}
{"comment": {"body": "use `inv_len = 1 / res_dict[\"hostname_len\"]`\n\nand\n\n`res_dict[\"freq_numbers\"] = res_dict[\"n_digits\"] * inv_len`\n\n\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/959/_/diff#comment-350171701"}}
{"comment": {"body": "please prettify ml\\_vocabulaary.json", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/959/_/diff#comment-350172228"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} Does it fixes the skipped regression tests failure?\n\nCan you open a PR on eros-automation to fix that?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/959/_/diff#comment-350206358"}}
{"comment": {"body": "It will change the behavior of some of the tests according to the assigned hostname groups\n\nFor the one\u2019s that pass i\u2019ll remove the skip", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/959/_/diff#comment-350214859"}}
{"comment": {"body": "This is a PR that \u201cunskip\u201d the tests that pass  \n[https://bitbucket.org/levl/eros-automation/pull-requests/402](https://bitbucket.org/levl/eros-automation/pull-requests/402){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/959/_/diff#comment-350860896"}}
{"comment": {"body": "passed regression on same branch  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/3783](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/3783){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/959/_/diff#comment-350969891"}}
{"title": "Data archiver version bump", "number": 96, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/96", "body": "Adding more labels to metrics for more accurate dashboards\nfix matchmaker metrics labels\nFix labels example\nbump manualy data archiver\n\n"}
{"title": "Bugfix/MEROSP-2495 Quic parsing traceback from dogfood", "number": 960, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/960", "body": "This PR adds another handling case of corrupted Quic data that caused a crash that was discovered in the dogfood earlier this week"}
{"comment": {"body": "Regression passed locally.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/960/_/diff#comment-350325975"}}
{"title": "Feature/MEROSP-2448 Fixing mDNS hostname extraction", "number": 961, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/961", "body": "This PR fixes the mDNS hostname extraction.\nUp until now, we took the hostname after examining the following fields of the packet:\nif questions != 1 or answers != 0 or authority_prs != 0 or additional_prs != 0:\n            return None\nIt made us losing a lot of data.\nAfter discussing the logic with research team, the following stages were suggested:\n\nThis PR adds the two rules from the rights (Answers part, Authoritative nameservers part).\nAfter looking at the regression, more than 300 sessions were assigned with a hostname. Before that logic, they had none.\nIn addition, this logic fixes MEROSP-2055 (duplicate Dell XPS bug).\nMore information can be found here.\nThis PR does not affect any performance (we still parse the same amount of mDNS packets, just take the data from a different place in the packet).\nImportant - This branch should be merged along with the branch in eros-automation as well."}
{"title": "feature/MEROSP-2500-prepare-classifier-for-user-", "number": 962, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/962", "body": "OS version information is set to be frozen at version 10 for Android and 10.0 for Windows in chromium-based browsers (Google Chrome, Microsoft Edge, Samsung Internet, Opera, etc.) {: data-inline-card='' }\n. It is not clear at this point if this change is/will be done also in other browsers (Safari, Firefox) and/or in non-Mozila user agent structures (Dalvik for example).\nNo matter what we do, this will reduce the performance of the classifier. Still, we need to make sure we opt to give no information, instead of giving a wrong answer to the OS version.\nThis PR blacklists the precise versions that the freezing is done on."}
{"comment": {"body": "Passed regressions [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2948](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2948){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/962/_/diff#comment-350275458"}}
{"comment": {"body": "No changes there?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/962/_/diff#comment-350332416"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} At the time no. After I synced with dev and re-ran suddently there were many failures that I didn\u2019t really understand [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3160?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3160?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED){: data-inline-card='' } . Now I\u2019m trying to re-run ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/962/_/diff#comment-350833816"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} still failing in the cloud but locally I can\u2019t reproduce \\(tests pass locally\\). Any tips on what I may be doing wrong? [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3382?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3382?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/962/_/diff#comment-350835604"}}
{"title": "MEROSP-2501 update models with new devices", "number": 963, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/963", "body": "added new Apple TV\nfixed minor tvOS\nfor Mich request at MEROSP-2443\nadded apple watch ultra and fix apple tv\nnew devices\nadded quotation marks\nfixed pre commit for csv files - by Ophir\nadded comma\nfixed l2 model with iPhone 14 family\n\n\nneed to merge automation as well {: data-inline-card='' }"}
{"comment": {"body": "passed regression\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3188](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3188){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/963/_/diff#comment-350320236"}}
{"title": "MEROSP-2154 pipeline for eros-test", "number": 964, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/964", "body": ""}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} can you check if we can put all this pipe in a function?\n\nwe have this code in many places. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/964/_/diff#comment-352841892"}}
{"comment": {"body": "Yes we can and we should. A more modular implementation is developed here recently - [https://bitbucket.org/levl/eros-models/src/MEROSP-2126-bitbucket-pipelines-send-sns-message/bitbucket-pipelines.yml](https://bitbucket.org/levl/eros-models/src/MEROSP-2126-bitbucket-pipelines-send-sns-message/bitbucket-pipelines.yml){: data-inline-card='' } If we follow this approach, we only have to define a step once and call using different deployments and variables. \n\nI\u2019ll create another ticket and assign it to myself", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/964/_/diff#comment-354618085"}}
{"title": "MEROSP-1790 Align Dev with collector-api 0.11.15", "number": 965, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/965", "body": "Updated collector api ver to 0.11.15, which is the latest on dev."}
{"comment": {"body": "Regression?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/965/_/diff#comment-350832802"}}
{"comment": {"body": "Running", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/965/_/diff#comment-350832872"}}
{"comment": {"body": "cant the class have all default None values?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/965/_/diff#comment-350832966"}}
{"comment": {"body": "and just pass headers and value or whatever has a real value", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/965/_/diff#comment-350832976"}}
{"comment": {"body": "@{6085103f5797db006947d59a} This is the exact kafka-python class `ConsumerRecord`. It is defined as a `namedtuple` thus all arguments must be explicitly passed. Other than in this test, this is filled inside the package and we read ready made consumer records.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/965/_/diff#comment-350833602"}}
{"comment": {"body": "Passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3412](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3412){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/965/_/diff#comment-350837836"}}
{"title": "MEROSP-2526 Configuration Strings for global usage", "number": 966, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/966", "body": "This file will be used to hold common used strings\n@{6265307b185ac200692f9bd9} Good idea!\n\n"}
{"comment": {"body": "very nice work done!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/966/_/diff#comment-350844442"}}
{"comment": {"body": "@{637f5c5e3e79f12e572115d7} @{6085103f5797db006947d59a} We are in development freeze. Only selected bugs/features will be merged until dev is stable back.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/966/_/diff#comment-350849656"}}
{"comment": {"body": "no problem.. it was a gesture to @{6265307b185ac200692f9bd9} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/966/_/diff#comment-350932415"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3611?launchesParams=page.page%3D2](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3611?launchesParams=page.page%3D2){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/966/_/diff#comment-350933410"}}
{"comment": {"body": "Why we need to hold all these env var statically  ? its all ready a pain to add a new env var, now we need to add all the new ones here in addition to all the other places ? \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/966/_/diff#comment-361124898"}}
{"comment": {"body": "because it\u2019s error-prone otherwise. one will have a minor mistake like `\"TESTS_SET_TYPE\"` instead of `\"TEST_SET_TYPE\"` and he/she won\u2019t understand why doesn\u2019t it work", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/966/_/diff#comment-361125730"}}
{"title": "added feature_log to debug data", "number": 967, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/967", "body": ""}
{"title": "Fix activate translation flag", "number": 968, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/968", "body": "Fixed confusing naming.\nAdded flag to helm_deployment/src/deployment_utils/values.yaml\n\n"}
{"title": "Add missing configuration fields to bitbucket pipelines", "number": 969, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/969", "body": "New configuration fields used in a deployment from Bitbucket didnt come into effect because the translation didnt appear in the bitbucket-pipelines.yml file"}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4373](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4373){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/969/_/diff#comment-351231068"}}
{"title": "Update artifactory in dev", "number": 97, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/97", "body": "Adding more labels to metrics for more accurate dashboards\nfix matchmaker metrics labels\nFix labels example\nbump manualy data archiver\nupdate artifactory on dev push as well\n\n"}
{"title": "fixed s3 path", "number": 970, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/970", "body": ""}
{"title": "Bugfix/translator default value", "number": 971, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/971", "body": "\n\nfix default value to off for the translator\n\n"}
{"title": "MEROSP-2589 adding metrics for missing data in device and session", "number": 972, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/972", "body": "adding metrics when\n\nsession was sent with missing data on connection event\ndevice is read from db with missing data\n\nregression passed locally (almost)."}
{"comment": {"body": "I understand you update the device from  the  session and then use the same function you added to the device to check if data is missing, but  I find it confusing that you query the device and update a metric about the session.  \nSince the actual issue is context data missing in the session, I think there should be a function in the session class that checks for missing context and the device should not even be updated if the data is missing.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/972/_/diff#comment-351612623"}}
{"comment": {"body": "I agree, but the task wasn\u2019t to exit if the data is missing.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/972/_/diff#comment-351619347"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} OK but can you query the session about the missing data and not the device after you update it?  \n if **device**.is\\_ap\\_context\\_data\\_missing\\(\\):  \n        **session\\_**context\\_missing\\_data.labels\\(tenant\\_name=\\_tenant\\_name\\).inc\\(1\\)  \nThe gap between checking for missing data on the device when it is actually missing in the session and then updating a metric about the session is what bothers me.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/972/_/diff#comment-351633583"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/972/_/diff#comment-351643197"}}
{"title": "Fix all classifier builds in makefile", "number": 973, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/973", "body": "Support buildkit docker build with pip from external environment on all classifier builds. Some were broken while others were updated."}
{"title": "Hotfix/kafka default consumer", "number": 974, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/974", "body": "\n\nrestore to default values for kafka consumer\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4683](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4683){: data-inline-card='' }  passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/974/_/diff#comment-351390240"}}
{"title": "move imports", "number": 975, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/975", "body": "moving imports from the middle of the file to the start of the file"}
{"title": "Hotfix/fix metrics values chart", "number": 976, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/976", "body": "\n\nalign charts with metrics host\n\n"}
{"title": "iOS 16.2 new OS models", "number": 977, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/977", "body": "MEROSP-2501\niOS 16.2 new OS models for Cujo"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4858](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4858){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/977/_/diff#comment-351522913"}}
{"comment": {"body": "do we have session that can be added in regression/UT for iOS 16,2?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/977/_/diff#comment-351523409"}}
{"title": "Change data health numbering to meet published interface", "number": 978, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/978", "body": "As described in {: data-inline-card='' }.\nAutomation PR: {: data-inline-card='' }"}
{"comment": {"body": "@{5dbeb866c424110de52552cc} can u explain what each levle indicates? it\u2019s not clear from the linked confluence page", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/978/_/diff#comment-351605311"}}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/4072042-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/978/_/diff#comment-351605776"}}
{"comment": {"body": "so why can it be in the code? Healthy instead of H?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/978/_/diff#comment-351615069"}}
{"comment": {"body": "Because the above is a client-facing definition and the internal one was always H/C/N. You are right though and we should change it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/978/_/diff#comment-351618691"}}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4929](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/4929){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/978/_/diff#comment-351636467"}}
{"title": "MEROSP-2597 renaming data health values", "number": 979, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/979", "body": "\n\nrenaming data health values\n\nregression passed locally (almost)"}
{"comment": {"body": "Fix decision logs in automation? While these fields aren\u2019t checked today, it is best to keep distance to minimum.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/979/_/diff#comment-351686996"}}
{"comment": {"body": "[https://bitbucket.org/levl/eros-automation/pull-requests/410](https://bitbucket.org/levl/eros-automation/pull-requests/410){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/979/_/diff#comment-351692099"}}
{"comment": {"body": "does this is a break change?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/979/_/diff#comment-352054111"}}
{"comment": {"body": "no", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/979/_/diff#comment-352837446"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} have you update grafana dashboards with this change? metrics definition is interfaces as any other..\n\n@{5dbeb866c424110de52552cc} @{6265307b185ac200692f9bd9} please check asap and if not rollback the merge!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/979/_/diff#comment-352837958"}}
{"title": "Feature/partition level processing 2", "number": 98, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/98", "body": "_2\nlimit resources\nmetrics retry\nbug fix\nflake8\nlabel metrics\nbug fix\nmetrics\nremove log\n\n"}
{"title": "Support getting ICMPv6 payload only on DiMessages", "number": 980, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/980", "body": "It was expected that ICMPv6 headers will be included in the Cujo agent payload for packet types NDP 132/135/143, this change was made but wasnt merged to release. This requires CDI adjustments. I think its best to avoid all that and make the adjustment on our end (supporting DiMessages with ICMPv6 payload only).\nThe change suggested here allows our internal parsing functions to remain the same. Before passing the data to them we just add the ICMPv6 type to the wrapping header.\nWhen used, the translator should support the expected payload, data collector PR: \nRegression tests should update translated events, automation PR:"}
{"title": "MEROSP-2596", "number": 981, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/981", "body": "WIP\nadd kinesis keys to cm\n\n"}
{"title": "MEROSP-2596", "number": 982, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/982", "body": "WIP\nadd kinesis keys to cm\nremove clear buffer\nchange kinesis env var to strings\n\n"}
{"title": "change data_link_type to return string", "number": 983, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/983", "body": ""}
{"title": "Bugfix/MEROSP-1786 test merosp 958 macbook", "number": 984, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/984", "body": "added the missing rpvr and fixed test and typing score\nneed to merge at automation as well {: data-inline-card='' }"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6452](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6452){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/984/_/diff#comment-352855567"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} , plese remove commit messages and replace with one sentence description.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/984/_/diff#comment-352861234"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} thanks for the change, though you could add a `break` here instead of the `len` check.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/984/_/diff#comment-352973608"}}
{"comment": {"body": "no I can\u2019t. it fails all the tests. I can use:  \n\n```\nif score_row_indices:\n  res.score_row_indices = score_row_indices\n  if len(res.score_row_indices) == 1:\n    break\n```\n\nif you want", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/984/_/diff#comment-352975869"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} `if len(res.score_row_indices) > 0` wouldn\u2019t work?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/984/_/diff#comment-352979054"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} no.[https://bitbucket.org/levl/eros-classifier/pipelines/results/9947](https://bitbucket.org/levl/eros-classifier/pipelines/results/9947){: data-inline-card='' }", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/984/_/diff#comment-352980073"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} OK. I understand that my assumption was wrong. I\u2019ll let @{62e7c839e50f2f2a395430c2} handle this", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/984/_/diff#comment-352983817"}}
{"comment": {"body": "passed after Ophir\u2019s new commit  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6817](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6817){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/984/_/diff#comment-353003520"}}
{"title": "Bugfix/MEROSP-2600 Data Health Fixes", "number": 985, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/985", "body": "This PR adds some Data Health fixes and puts things in the right order.\nThe following decision for calculating a data health for a session is as follows (See confluence page as well):\n\nSome changes that were made:\n\nWere no longer looking at DHCPv4 ack packets since they wont arrive when well be in production.\nAs for DHCPv4 req - we might get static ip connections so were not looking for them either as an indication.\nAs for DHCPv6 messages - since it started to be randomized for iOS / windows devices, it doesnt add us important information so we dont need to use it as an indication as well\nFrom now on we will pay attention to the type of the session when we calculate the data health - ongoing events will never contain as much data as connection events so there is no point of looking at them the same way.\nDisconnection events will no longer affect the device_data_health, since theyre always healthy.\n\n"}
{"comment": {"body": "Very good description, please add to confluence and share with qa, backend and research teams to validate the approach.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/985/_/diff#comment-352871144"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6771](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6771)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/985/_/diff#comment-352987534"}}
{"title": "metrics", "number": 986, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/986", "body": "Im missing labels of actual version of the models (from the yaml file in the zip)"}
{"title": "Bugfix/MEROSP-2605 iphone 14 164 165 166 is ide", "number": 987, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/987", "body": "added missing l2 fp fro iPhone 14 family\n"}
{"title": "MEROSP-1297 hostname classification", "number": 988, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/988", "body": "fix, refine and add regexes & updated frequent_hostnames.csv\nadded logic to prioritize non-unique > locally unique > globally unique\n"}
{"comment": {"body": "Please change to:\n\n```\nif not hostname or ormalized_hostname in NonrandomDeviceName.non_unique_set:\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/988/_/diff#comment-353995082"}}
{"comment": {"body": "Passed local regression\n\n![](https://bitbucket.org/repo/o5KReBa/images/226168912-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/988/_/diff#comment-353995195"}}
{"title": "removing more spam logging", "number": 989, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/989", "body": "local regression passed"}
{"title": "Feature/partition level processing 2", "number": 99, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/99", "body": "_2\nlimit resources\nmetrics retry\nbug fix\nflake8\nlabel metrics\nbug fix\nmetrics\nremove log\nbug fix\n-\nmetrics labels\n-\n-\n-\ntest - no device logs\ndeiable new device logs\ndisable all db writes\ndisable tests\nchange density cofnig\nchange max_batch size\npoll every 500 ms\nrestore the db writes\nrestore tests\n\n"}
{"title": "fixing macbook pro", "number": 990, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/990", "body": "this is a roll back to a previous commit. Some tests one regression failed. MacBook Pro identified as MacBook. \nthe problem is that wiser agent model has no data on Macos so there is a contradiction between higher feature (RPVR) and lower feature (WISPR) that reduces the resolution."}
{"comment": {"body": "Explain the problem and fix?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/990/_/diff#comment-353355558"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/990/_/diff#comment-353356569"}}
{"comment": {"body": "local regression passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/990/_/diff#comment-353362531"}}
{"title": "Feature/MEROSP-2616 dhcpv4 leases with time limits", "number": 991, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/991", "body": "Re-enable ipv4_leases feature by default.\nExtract given address from request.\nAdd time limits to ipv4_leases, 15 minutes by default. If too much time has passed since the last DHCP handshake, devices won't be matched.\nThe feature is used both for identification and as a display attribute of the device. The first use case applies only if the DHCP request did not arrive after a DHCP offer, while the second use case applies even in that scenario. The feature extraction result was adjusted to support both cases.\nA change in the DB Device schema was made to support saving the timestamp along with the IP address.\nThe feature data was added a timestamp, but the decision log wasnt changed to avoid further schema changes.\n\nAutomation PR: {: data-inline-card='' }"}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/991/_/diff#comment-353557203"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8978](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8978){: data-inline-card='' } Reg  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8979](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8979){: data-inline-card='' } Smoke  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8974](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8974){: data-inline-card='' } API", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/991/_/diff#comment-354009255"}}
{"title": "iPhone 14 and Galaxy A52 L2 fp", "number": 992, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/992", "body": "iPhone 14 and Galaxy A52 L2 fp"}
{"comment": {"body": "passed [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8086?launchesParams=page.page%3D1](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8086?launchesParams=page.page%3D1){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/992/_/diff#comment-353584732"}}
{"title": "Bugfix/MEROSP-2610 watch low", "number": 993, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/993", "body": "added missing l2 fp apple wathc 6\n"}
{"title": "MEROSP-2132 Upgrade classifier's models version", "number": 994, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994", "body": "Classifier upgrade:\n\n\nOn Kafka pooling loop, every X seconds (configurable) we \n\nCheck s3 for existence of models.tar.gz file (s3 path is configurable)\nIf not exist - continue Kafka pooling\n\nIf exist\n\nsend AWS head_object request to get file metadata\nextract ETag from metadata\ncheck if current classifiers ETag equals or not to s3 files ETag (On first classifier upload it has no ETag, so s3 files ETag != None)\nif ETag equals - continue Kafka pooling\n\nIf ETag NOT equals\n\ndownload models.tar.gz file to local temp directory\n\ncalculate downloaded files checksum (ETag) and check if equals to s3 files ETag\n\nif NOT equal - possible corruption is detected - continue Kafka pooling\nif equals - unzip downloaded file\n\n\n\ncheck unzipped tree for classifiers meta version directory (configurable)\n\nif meta version directory NOT exist - continue Kafka pooling\n\nif exist\n\nbackup current classifiers models tree to temp directory\ncopy meta versions models tree to classifiers models tree location\ninitialize classifier models with new version (upload to memory)\n\n\n\n\n\nif new models version were successfully initialized\n\nupdate classifiers ETag with new models ETag\nupdate classifiers version with new models version\ncontinue Kafka pooling\n\n\n\nif failed to initialize new models version\n\n\nrevert to previous models version\n\nremove classifiers models tree\ncopy models tree from temp local backup to classifiers models tree\ninitialize classifier models with previous version (upload to memory)\ncontinue Kafka pooling\n\n\n\n\n\n\n\n\n\n\n\nConfluence LNDI: LNDI Design\nConfluence Data Catalog: Data Catalog Design"}
{"comment": {"body": "please reduce the number of lines in the try execpt and use narrower exception and remove noqa", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353634087"}}
{"comment": {"body": "very nice commnenting", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353634243"}}
{"comment": {"body": "no broad execption please", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353634424"}}
{"comment": {"body": "did you remove the old model loader in `models` dir?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353634980"}}
{"comment": {"body": "No  \nIt wasn\u2019t really a class, more like a utility with functions  \nIt\u2019s located under: eros-classifier/model\\_loader", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353657537"}}
{"comment": {"body": "There no way to verify the possible exceptions that can be thrown by init models  \nThis is why I\u2019ve used Exception instead of specific exceptions", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353666973"}}
{"comment": {"body": "it catches also the boot / init scenario of classifier, worth adding in the comment", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353796148"}}
{"comment": {"body": "@{6335b848a84c7f79c387915c} please add description and reference to the design page in the PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353796338"}}
{"comment": {"body": "metrics decorator for latency", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353796933"}}
{"comment": {"body": "please remove `eros-classifier/models/model_loader.py` if no one uses it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-353908453"}}
{"comment": {"body": "deleted", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-354554680"}}
{"comment": {"body": "comment added, describing classifier cases", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-354555200"}}
{"comment": {"body": "description & references to design doc were added", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-354557955"}}
{"comment": {"body": "metrics will be added in a different branch & PR \\(branched from this branch\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-354558015"}}
{"comment": {"body": "download models method was refactored", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/994/_/diff#comment-354561471"}}
{"title": "latest flake8 versions", "number": 995, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/995", "body": "local regression passed"}
{"title": "MEROSP-2230 aggregate kinesis records", "number": 996, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/996", "body": "As part of the integration steps with Cujo, our kinesis should send messages as an aggregate batch of X messages this code will:\n\nSlice the messages list to X kinesis acceptable batches\nevents will be separated with a new line between each message\nPut to kinesis only desired batch size record\n\n"}
{"title": "MEROSP-2645 Add explicit UTC timezone information to updated_on attribute", "number": 997, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/997", "body": "For some time-dependant features, such as NetbiosTransactionID, inconsistent handling of timezones can cause the feature to fail due to larger-than-expected time differences.\nIn some cases, the classifier uses\ndatetime.datetime.utcnow()\nWhich does not return explicit timezone information.\nInstead, using\ndatetime.datetime.now(datetime.timezone.utc)\nWill provide a more explicit datetime object that will be interpreted consistently."}
{"comment": {"body": "why is it different?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/997/_/diff#comment-353936378"}}
{"comment": {"body": "Regressions passed locally\n\n![](https://bitbucket.org/repo/o5KReBa/images/2852456934-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/997/_/diff#comment-353981497"}}
{"comment": {"body": "[https://blog.ganssle.io/articles/2019/11/utcnow.html](https://blog.ganssle.io/articles/2019/11/utcnow.html){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/997/_/diff#comment-353982663"}}
{"title": "Bugfix/MEROSP-1922 Typing - Include vendor name in the description", "number": 998, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/998", "body": "MEROSP-1922 describes a minor typing change in our product scope - adding the vendor name to the typing description result for all devices, except Apple devices. For example: Galaxy S20  Samsung Galaxy S20.\nThis PR adds this change, and strips the vendor name from the start of the canonical_name and specific_name columns of the devices db.\nIn addition, this PR fixes the LG / LGE confusion that also came up from a product point of view (mac_vendor.csv changes).\nPlease note that there is a matching PR in eros-automation that fixes all the (many) places in the regression that had to be fixes according to this change."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8720](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8720)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/998/_/diff#comment-353937450"}}
{"comment": {"body": "Please also add somke and API tests links, or at least validate that they passed successfully :pray: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/998/_/diff#comment-353989779"}}
{"comment": {"body": "You\u2019re correct, API link - [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8716](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8716){: data-inline-card='' }, Smoke link - [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8677](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/8677){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/998/_/diff#comment-353990502"}}
{"title": "MEROSP-789 add client configuration api chart", "number": 999, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/999", "body": "MEROSP-789 - add client-configuration-api chart to eros-charts\n\n"}
{"title": "Guyk wip", "number": 1, "htmlUrl": "https://bitbucket.org/levl/ti_cc2640r2_mvp/pull-requests/1", "body": "Connected to LNA interrupts falling edge\nAfter seeing results from scope, it seems like it fits us.\n\n\nImproved accuracy of the timer\n\nTried to make timer readings more accurate.\n\nRATGPO1 (sync_found) interrupt now works at the right time.\nAdded ble_user_config file with the right overrides. The file is at patched_external directory but being compiled by the stack_library project."}
{"comment": {"body": "care to remove commented lines? makes it harder to follow the changes", "htmlUrl": "https://bitbucket.org/levl/ti_cc2640r2_mvp/pull-requests/1/_/diff#comment-80526402"}}
{"comment": {"body": "what changed here?", "htmlUrl": "https://bitbucket.org/levl/ti_cc2640r2_mvp/pull-requests/1/_/diff#comment-80526409"}}
{"comment": {"body": "According to his comment \u201cAdded ble\\_user\\_config file with the right overrides. The file is at patched\\_external directory but being compiled by the stack\\_library project. \u201c The overrides are supposed to be here but they\u2019re not. This file is identical to the original. He probably commited the wrong file", "htmlUrl": "https://bitbucket.org/levl/ti_cc2640r2_mvp/pull-requests/1/_/diff#comment-80526597"}}
{"title": "Long range rx now tracks channel changes.", "number": 1, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/1", "body": ""}
{"title": "Reduced RAM usage", "number": 10, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/10", "body": "Reduced total RAM usage by about 26KiB.\nSince we were only asked to reduce 8KiB, Ive re-added some of the space by introducing an unused buffer in the library."}
{"title": "xorshift fix", "number": 11, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/11", "body": ""}
{"comment": {"body": "Beautiful", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/11/_/diff#comment-132683729"}}
{"title": "Objfuscation script", "number": 12, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/12", "body": ""}
{"title": "Feature/cleanup for release", "number": 13, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/13", "body": "No report structs\nDevice information in separate file Using xorshift128+ as PRNG\n\n"}
{"title": "Regression tests", "number": 14, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/14", "body": ""}
{"title": "Added ctypes generation script", "number": 15, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/15", "body": ""}
{"title": "Release branch", "number": 16, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/16", "body": "Fixed packet filter\nremove padding buffer\nLibrary for device configuration\nprepare code for sending out\n\n"}
{"comment": {"body": "Inconsistent indentation :disappointed: ", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/16/_/diff#comment-133145799"}}
{"comment": {"body": "What was wrong with xorshift?", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/16/_/diff#comment-133145823"}}
{"title": "CFO calculation algorithm optimisation to 32 bit", "number": 17, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/17", "body": "Also not calculation any more variance on CFO as its not used\n\n"}
{"title": "Added MCU project that transmits fob beacons", "number": 2, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/2", "body": "Only supports short-range"}
{"title": "Sine rec", "number": 3, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/3", "body": "Added a sine recording mode that outputs CFO estimation through JTag"}
{"title": "Feature/falling transient", "number": 4, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/4", "body": "Fix project stuff \n\nAdd transient extraction code from bosch_integration commit 58c299f5b7f57a9da94961fcc9f850d57df3b40d\n\nAdd CLion project for libfingerprinting library\nAdd python wrapper to falling transient part of the libfingerprinting\n\n\n\nAdd HW timer that's triggered when access address event received and trigger on timeout the IQ capture\n\nSome refactoring for handling state machine\nSaving packet before handling it \nRemoved dependency that DMA event must come before RX event\nFound a way to delay RXdone event with RXEN_DLY register\n\n\n\nFalling transient isnt integrated, but the algorithm implementation works\n"}
{"comment": {"body": "You\u2019ve mixed spaces and tabs", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/4/_/diff#comment-131067840"}}
{"title": "Feature/falling transient", "number": 5, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/5", "body": "Add release build\nAdd python env to run libfingerprinting\n\n\n"}
{"comment": {"body": "I see the repo is starting to take shape.\n\nMaybe move the eclipse projects to a subdirectory? Something like what was in bosch", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/5/_/diff#comment-131393973"}}
{"comment": {"body": "Maybe change that to a SMB share, or open a SimpleHTTPServer on your PC", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/5/_/diff#comment-131394331"}}
{"comment": {"body": "I agree, but I think we should postpone it to when we start to get paid", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/5/_/diff#comment-131412537"}}
{"comment": {"body": "It\u2019s an example workspace, even if it\u2019s called falling transient test. I don\u2019t give much significance to this pickle", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/5/_/diff#comment-131412952"}}
{"title": "Fixed read_cfo_temp to always read the correct number of bytes from jlink", "number": 6, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/6", "body": ""}
{"title": "Feature/lo leakage", "number": 7, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/7", "body": "LO leakage for valeo demo. Main changes:\n\nMove from float to fixed - changed most of the math (including dsp) to CMSIS q15.\nNo Decimation - frame from noise directly transformed.\nSimpler integration in classification process.\n\nConstants:\n\nFFT size is 256.\nfeature frame is 13 bins wide.\nConsidering 1 peak in frame when calculating noise floor.\nCurrently, peak should be 3 times the noise amplitude for no match.\n\n\n\nTested on BladeRFs with EVB.\n"}
{"comment": {"body": "Did you measure the added run-time for this feature?", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/7/_/diff#comment-131716018"}}
{"comment": {"body": "12ms per packet, ~15% of current classification time.", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/7/_/diff#comment-131717380"}}
{"title": "Fixed RX in the TRX units", "number": 8, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/8", "body": ""}
{"comment": {"body": "We've got to do something about the conflicting IDEs", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/8/_/diff#comment-131794398"}}
{"comment": {"body": "Great job! ", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/8/_/diff#comment-131794561"}}
{"comment": {"body": "I think it\u2019s just the SDK versions, maybe we can just sync on that", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/8/_/diff#comment-131869827"}}
{"title": "Residual dc correction with lo leakage", "number": 9, "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/9", "body": "Added sine recording side project\nAdd codedphy to fake keyfob\nAdd TRX antenna hack\n\nAdd H and falling transient models, integrated with LO leakage\n\nTotal runtime: 41ms for data packet and 58ms for adv packet in Release configuration\n\n\n\nAdd CFO model for TRX4\n\nComes from model dumping in python_tools\\models_repository\\models_collection.py\n\n\n\nPython tools:\n\nSome python wrappers for the fingerprinting library\nswd_interface to real some realtime data\n\n\n\n\n"}
{"comment": {"body": "We could maybe use the two buttons to switch between short- and long-range?  \nThat would practically turn the devkit into a fob.", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/9/_/diff#comment-132412159"}}
{"comment": {"body": "Good idea. I\u2019ll do it in a followup PR", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/9/_/diff#comment-132413710"}}
{"comment": {"body": "No faults were found so far, so I\u2019m merging", "htmlUrl": "https://bitbucket.org/levl/valeo_fingerprinting/pull-requests/9/_/diff#comment-132608014"}}
{"title": "Receiver Ids created online with Bitbucket", "number": 1, "htmlUrl": "https://bitbucket.org/levl/sdr/pull-requests/1", "body": "Receiver Ids created online with Bitbucket"}
{"title": "Idan/data prep for typing", "number": 1, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/1", "body": "Initial commit for dataprep for typing\nflake reformat and more compartmentalization\nblack formatting to all files\nsome more flake reformatting\nadded readme\n\n"}
{"title": "Add more IDs of devices", "number": 10, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/10", "body": ""}
{"title": "Add new devices", "number": 11, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/11", "body": "Added new devices\n"}
{"comment": {"body": "Thanks!", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/11/_/diff#comment-378883391"}}
{"title": "Scripts for tracking recordings and inventory", "number": 12, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/12", "body": "Scripts for tracking recordings and inventory\n\ncreate_id_mac_model creates clean inventory csv for the specified model types\ncreate_summary_csv creates a csv of the different required recordings in the given dataset and their current recording state (recording ids of completed recordings)\n\n"}
{"comment": {"body": "sanitate?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/12/_/diff#comment-379553445"}}
{"comment": {"body": "how do the output CSVs look like? can you document the schema?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/12/_/diff#comment-379553693"}}
{"comment": {"body": "Ok. will do", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/12/_/diff#comment-379572968"}}
{"comment": {"body": "what\u2019s the status of this PR?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/12/_/diff#comment-387110002"}}
{"comment": {"body": "Waiting for some approval lol.  \nIt is outdated anyway and can be canceled. The updated version of this code is in idan/supcon\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/12/_/diff#comment-387112118"}}
{"comment": {"body": "Then it\u2019s best to decline this PR and open a new PR with supcon", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/12/_/diff#comment-387113028"}}
{"title": "Shaked/sagemaker training", "number": 13, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/13", "body": "add run sagemaker pipeline preprocessing and training\n\n\nAdded supcon training pipeline (for image dataset)\nSimulate supcon identity evaluation with images\nScripts for tracking recordings and inventory\ninclude model_types in gitignore\ndocumentation and bug fixes\nadded vs code launch json for debugging added bin/bash to bash scripts\nadded hdf5 dataset, dataloader and creation script\nchanged hdf5 out path to include project version and dataset\nWorking trainig with csi data Added resnet1d - resnet architecture with 1d data Added supcon_no_aug - supcon training without augmentations\nDynamic importing of model, loss and loader\nadded h5py to requirements\nfixed bug in call dataframe_arrays_to_dask_array\nmerged chain real and imag to one column and added data transformations\nbug fixes\nAdded supcon evaluation on csi data\nadded conf mant to per platform to identity eval\nfixed bug in recordings tracking\nmoved arg parsing to main scope\n\nimproved gsheets utils and create_summary_csv\n\nonly download requested routers and temeratures\nadded new identity datasets\n\n\n\nadded subsets arg to save_to_hdf5 closed dask cluster fix\n\nchanged loss temp to arbitrary args added model checkpointing to wandb (only save)\nadded wandb resume to training (without model loading from wandb) Improved supcon_eval\nsagemaker training\nfix errors\nfix errors\nfix errors\nfix errors\nfixed run_module_nohup pid log problem\ndownload_data bug_fix\n\nchanged default num_workers to None (i.e num cpus)\n\nfixed set_model bug\nupdated wandb version\n\n\n\nfix bb pipeline bug\n\nchange versions\nEval with split on recording id\nmarge sagemaker\nfix flake errors\n\n"}
{"title": "Idan/supcon", "number": 14, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/14", "body": "This is a first full working version of preprocessing training and evaluation for supervised contrastive learning for the identity problem.\nThis code includes a modular training pipeline that received the model, loss, data loaders and transformations as input arguments.\nThis code does not yet contain the sage-maker pipeline.\n\nRelevant Links:\nML Research Code Compartmentalization\nDevice Identification using Representation learning\nPapers with Code - Supervised Contrastive Learning"}
{"title": "Feature/LDI-885_and_LDI-1087", "number": 15, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/15", "body": "Added model checkpointing and loading from wandb\n\nAdd model, loss, and optimizer args dynamically to arg parse\n"}
{"comment": {"body": "oh, really?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/15/_/diff#comment-390834665"}}
{"comment": {"body": "why do you need the `unknown_args`?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/15/_/diff#comment-390841325"}}
{"comment": {"body": "I added the functionality of inferring arguments from the model/loss/dataloaders class/function interface.\nThe unknown args are parsed by a sub parser that is automatically created after importing these objects. see dynamic_import_and_parse_args", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/15/_/diff#comment-390841640"}}
{"title": "fix debian repository", "number": 16, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/16", "body": ""}
{"title": "Grisha/LDI-958 move typing model to griffin", "number": 17, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/17", "body": "Added typing classifier, along with its evalution and other QOL changes. Copied the MPL3, preprocessing step and evaluation functions from comcast repo.\nMain changes:\n\nChanged in the model the conv layers and removed padding=\"valid\" .\nTraining accepts some hyperparameters (learning rate, etc)\n\nEvaluation will be done in a new wandb run\n\nTraces back to the input model (with its run) and the test dataset\nCapable of doing inference from torchscript\n\nArtifacts path in s3:\n\nIf the model is 3gqhrfr9 and the dataset is typing_3/test, then: \ns3://griffin-ml/phy/2/artifacts/typing_3/test/3gqhrfr9/v2/dch8rcv2/performance/\n  where dch8rcv2 is the new run\n\n\n\n\n\nExample evaluation: \nExample model lineage: \nQOL changes:\n\nSave to hdf5 with xarray, for multiprocessing writes\nFix previous merge issue with arguments with save_to_hdf5.py\nUse dataset/model logging (in some parts), for traceability\nAlso logging model as torchscript and ONNX format\n\n"}
{"title": "LDI-1135 use latest image for docker-compose", "number": 18, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/18", "body": ""}
{"comment": {"body": "@{640059b30e0ddcdce18ce2c5} @{5fd5d5149edf2800759cc96d} ", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/18/_/diff#comment-394327438"}}
{"title": "LDI-1113-remove-apex-dependecy", "number": 19, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/19", "body": "Removed apex dependency and replaced it with pytorch\nreplaced apex.parallel.convert_syncbn_model\n with torch.nn.SyncBatchNorm.convert_sync_batchnorm"}
{"comment": {"body": "Good riddance. Great job! ", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/19/_/diff#comment-394347235"}}
{"title": "Idan/data prep for typing", "number": 2, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/2", "body": "boilerplate\nworking ci without code\ndownload and curate data for typing\n\n"}
{"title": "Idan/sm argpars quick fix", "number": 20, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/20", "body": "Some quick fixes to start working with sage maker.\nSome of these changes should later be implemented in the sagemaker-wrapper instead\n\nquick fix for sagemaker args parsing issue\nremoved apex\nAdded loss print for sm sweep\nChanged run name to include id instead of ver\n\n"}
{"title": "upgrade sagemaker-wrapper", "number": 21, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/21", "body": "upgrade sagemaker-wrraper to 0.28 version\nstore sagemaker script and files in sagemaker-utils\nadd user_name to WANDB init\n\n"}
{"title": "Feature/LDI-949 integrate training pipeline with", "number": 22, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/22", "body": "Added Two training pipelines:\n1. Vanilla PyTorch\n2. PyTorch Lightning"}
{"title": "Grisha/split inference from typing evaluation", "number": 23, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/23", "body": "Extract inference code from evaluation\n\nInference supports by default:\n\nLocal torchscript model\nWandb torchscript artifact\n\n\n\nTyping multiclass evaluation needs evaluated dataset only\n\n\n"}
{"title": "Idan/eval many models", "number": 24, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/24", "body": "evaluation of runs according to run list\nadded run counter\nfixed accidental n epochs change !!! Important fix !!!\nchanged default n epochs to 100\nremove devices without id in create_id_mac_model\n\n"}
{"title": "Bugfix/LDI-1347 remove duplicate recording name", "number": 25, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/25", "body": "Log duplicate recording names into csv (by Grisha)\nAdded script that sets the recording names of duplicate recordings in big query according to their counterparts in the recording sheets\n\n"}
{"title": "Bugfix/LDI-1372 fix model save path and log dir", "number": 26, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/26", "body": "fixed save_dir/log_dir/model_dir confusion\n"}
{"title": "Added added optimizer dynamic importing", "number": 27, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/27", "body": "added kwargs to dynamic_import_and_parse_args\nAdded limit_train_batches\nAdded limit_val_batches (only with lightinig atm)\nRenamed learning_rate to lr (to match optimizers api)\nadded some type hinting\nremoved ambiguous args from lightning_trainer\n\n"}
{"comment": {"body": "what\u2019s the benefit of using `lr` instead of `learning_rate`?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/27/_/diff#comment-399711983"}}
{"comment": {"body": "what happens if number is exceeded? error message? number of workers is reduced?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/27/_/diff#comment-399718191"}}
{"comment": {"body": "if you\u2019re inputing `2`, then `limit_train_batches` is `2.0` and then the number of batches in pytorch lightning is doubled?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/27/_/diff#comment-399718775"}}
{"comment": {"body": "That this is the convention for the optimizer class \\(by pytorch\\) and then we don\u2019t need to do a conversion/duplication of the argument so the parser for the optimizer will recognize it.", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/27/_/diff#comment-400203276"}}
{"comment": {"body": "This number wont be exceeded. It is a limit tells the training pipeline to stop when reaching it \\(even if you may have more data\\). if the not specified the default is all the available data", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/27/_/diff#comment-400203367"}}
{"comment": {"body": "No, It will only do 2. I tested this.", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/27/_/diff#comment-400206298"}}
{"title": "Grisha/LDI-1139 integrate sagemaker into typing", "number": 28, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/28", "body": "Sagemaker:\n\n\nAdd typing training loop to sagemaker init\n\nFuture work will have same training loop as the identity\n\n\n\nAccept certain sagemaker parameters and env variables that are relevant to typing\n\nInstall sagewrapper function would check if already exists before installation\nIn typing loop, Wandb runs would be grouped under the hyperopts job name\n\nOther:\n\nAdd dataset evaluation step\n\nFixes for Pytorch lightning 2.0.0, but downgrade to ver 1.9.3 because of issues started with ver 2.0.0\n\nSome HPO jobs are crashing with error \"[rank: 0] Received SIGTERM: 15\"\n\n\n\nIn PL, they removed the best_k tag and its now just best\n\n\n\n"}
{"comment": {"body": "Maybe use average meter instead?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/28/_/diff#comment-400699881"}}
{"comment": {"body": "Use f sting `f\u201dConverting dataset {subset} from {subset_path} to {out_hdf5_path}\"`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/28/_/diff#comment-400702288"}}
{"comment": {"body": "Like!", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/28/_/diff#comment-400703117"}}
{"comment": {"body": "I understand the memory overhead. it\u2019s neglegible right now", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/28/_/diff#comment-400729970"}}
{"comment": {"body": "when logging, it\u2019s best to let the logger do the string formating. If, for example, the logger is set to not print `info`, then the string formatting is not performed. With f-string, the formatting is always performed and always has an overhead.", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/28/_/diff#comment-400731277"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} The overhead here is negligible and we don\u2019t run with not print.  \nThe new f string format is more readable and upholds the current convention", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/28/_/diff#comment-400801961"}}
{"comment": {"body": "@{63553fd9b0b6ef035648ab9a} It\u2019s not a good practice to use f-strings in logging. For example: [https://google.github.io/styleguide/pyguide.html#3101-logging](https://google.github.io/styleguide/pyguide.html#3101-logging){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/28/_/diff#comment-400807803"}}
{"title": "Master fixes", "number": 29, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/29", "body": ""}
{"title": "Grisha/version 12.0", "number": 3, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/3", "body": "Add eg7 sheet\nfix script\n\n"}
{"title": "Feature/LDI-1349 add validation set to identity", "number": 30, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/30", "body": "Added Validation to lightning trainer and torch trainer\n"}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/30/_/diff#comment-401269060"}}
{"comment": {"body": "Thanks!", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/30/_/diff#comment-401415534"}}
{"title": "Feature/LDI-1328 add learning rate scheduler to", "number": 31, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/31", "body": "Added learning schedulers to both torch and lightning.\nThe any scheduler implementing the torch.optim.lr_scheduler._LRScheduler can be dynamically imported .\nuse the lr_scheduler_class argument to specify which scheduler to use.\nuse the lr_scheduler_intervelto specify when to call the scheduler.step(), either every epoch or every step.  \nWarning: The lightning trainer calls the scheduler.step() at the beginning of each step/epoch instead of at the end (as advised by pytorch).\nThis may modify the the behavior of different schedulers depending on their implementation.\nThis is part of the internal code of pytorch and I havent yet found a way to overcome it except to give a scheduler that works well when called at the beginning of each step/epoch"}
{"title": "Idan/data representation", "number": 4, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/4", "body": "changed dtypes in schema to reduce size\nfixup! changed dtypes in schema to reduce size\ncreated a gsheets util module\nfixup! changed dtypes in schema to reduce size\nadded inventoy support to gsheet util\nfix run_job script venv activation\nfix run_job script venv activation\nfixed csi_server_timestamp dtype\nfixed csi_server_timestamp dtype\nadded eg7 and gcloud service account\nadded eg7 and gcloud service account\nflake8 remove import unused import\nflake8 remove import unused import\nchange chains real and imag to int16\nchange chains real and imag to int16\nchange chains real and imag to int16\n\nAdded channel extraction from rec name to gsheets_util\n\nchange recording_temperature type to int64\n\n\n\nadded center freqency to channel conversion. added partiotioning in config\n\n\n"}
{"title": "Grisha/use directories", "number": 5, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/5", "body": "Move files around\nfix import paths\ndownload_data works\nFIX TYPO\nfix path\n\n"}
{"title": "Grisha/typing dataset", "number": 6, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/6", "body": "add typing dataset\nFix configuration to relevant description path\n\n"}
{"comment": {"body": "@{63553fd9b0b6ef035648ab9a} approve?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/6/_/diff#comment-*********"}}
{"comment": {"body": "yes.  \nIt does not let me press approve as it is already merged", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/6/_/diff#comment-372020288"}}
{"title": "Idan/data representation", "number": 7, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/7", "body": "freeze test-requirements\nignore flake ANN001\nignore flake ANN101,ANN201,ANN204\n\n"}
{"title": "Grisha/update ds descriptions", "number": 8, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/8", "body": "Allow multiple splits per curated data\n\n"}
{"title": "Idan/identity efficacy", "number": 9, "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/9", "body": "\nAdded Efficacy calculation for the identity problem.\n"}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/9/_/diff#comment-373560452"}}
{"comment": {"body": "Thank you!", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/9/_/diff#comment-373563335"}}
{"comment": {"body": "Good job, @{640059b30e0ddcdce18ce2c5} !", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/9/_/diff#comment-375901736"}}
{"comment": {"body": "where is this data taken from? what does it mean?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/9/_/diff#comment-377881507"}}
{"comment": {"body": "Do we necessarily want to use simple KNN, or do we want to to use also weighted KNN?", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/9/_/diff#comment-377882863"}}
{"comment": {"body": "This KNN is used only for \"fair\" evaluation of the different representations created by the different ml models. The real production classifier will probably be much more sophisticated than a plain KNN and wont also be evaluated on all values of K. This KNN is for establishing a baseline evaluation.", "htmlUrl": "https://bitbucket.org/levl/griffin-ml/pull-requests/9/_/diff#comment-377884699"}}
{"title": "Added slope channel", "number": 1, "htmlUrl": "https://bitbucket.org/levl/levl_gameshow/pull-requests/1", "body": ""}
{"title": "Feature/FIN-249 Preprocessing stage in C", "number": 1, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/1", "body": "*first attempt at Cython & C\n*updating embedded projects and C exports\nmoving around files trying to make pipelines work\nmaybe now pipelines would work\ncorrecting cmake dependency\njust adding placeholder for clean_cfo\n*adding basic test env with cmake & Catch2 unittests env\n*updated cmakelists for CI\nfixing pipelines yml\n*renaming files\n*renaming them back\n*trying to fix pipelines make failure\n*another try at cmakelist\n*not running tests as a seperate step\n*pipelines should get test reports\n*fixing yml indentation\n\ndone implementation of clean_cfo\n\nadded helper for complex numbers\nadded unittests for complex numbers and clean_cfo\n\n\n\nsome refactoring to complex functions fixing gcc compilation issue\n\n*moving files around\n*cleanup of files\nstarting to create env for lib export for ARM added lib header\n*fixed pipeline\nmoving build files around\nanother pipeline fix\nanother try at pipelines & ARM\n*corrected ARM package to be installed\n*added missing package to pipelines\nanother try\n*skipping ARM building for now\nexpanding complex functions adding rolling mean and tests (WIP)\nadding some comments finishing implementation for rolling mean\nmoving files to commons directory adding circular buffer rotation function and some tests *slightly modifiction API to allocate more memory\n*CI should also be updated with updated format\nfixing issue with GCC\ntypo fix\nintegrating preprocessing flow into embedded project embedded project links succesfully ARM library in current directory structure\n\n"}
{"comment": {"body": "better style", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/1/_/diff#comment-69969447"}}
{"title": "Feature/FIN-271 Preprocessing access code extraction and alignment", "number": 10, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/10", "body": "*creating mock for preamble extraction\n*adding demod FSK sample and comments\nmissing comment in previous commit and fixing comments adding module access code location with plain C impl and added very small test\n*added missing include\n*fixed preprocessing type casting\n*fixed tests\n\nreplaced naive implementation with optimized bit search\n\ncompleted test and documentation\n\n\n\nfixed some misra issues and added comments\n\n*integrated access code location in preprocessing stage"}
{"comment": {"body": "We agreed not to use q31\\_sub / q31\\_add right?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/10/_/diff#comment-70433702"}}
{"comment": {"body": "we\u2019ll integrate later", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/10/_/diff#comment-70446162"}}
{"title": "Feature/BIS-633 replace capnproto", "number": 100, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/100", "body": "Add hydra library Save progress with creating datasets\nMinor fix to dataset creation\nSave progress\nFix alignment and endianess issues\nFix dataset building and access to it\nAdd dataset load validation\nUpdate CMakelist to help debugging"}
{"comment": {"body": "Add a README.md inside the hydra directory with a link to the GitHub page you took it from. \n\nWrite down the commit hash you copied the files from as-well.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/100/_/diff#comment-81617457"}}
{"comment": {"body": "Do you prefer this over specifying it as a subrepo?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/100/_/diff#comment-81678592"}}
{"comment": {"body": "Do you mean subtree or submodule? Because I\u2019d rather we don\u2019t use any of them. We have no use of the Hydra history, we just need to note where it came from.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/100/_/diff#comment-81725485"}}
{"comment": {"body": "How come the news files are double the size of the previous ones? Compression?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/100/_/diff#comment-81745707"}}
{"comment": {"body": "Apparently they do: [https://github.com/capnproto/capnproto/blob/291f1d86080f6827478d40995918ee4a019909ea/doc/encoding.md#packing](https://github.com/capnproto/capnproto/blob/291f1d86080f6827478d40995918ee4a019909ea/doc/encoding.md#packing)\n\nRLE compression but just for zeroes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/100/_/diff#comment-81748557"}}
{"comment": {"body": "Fixed in efe653daad26dba46162f167805fb1c379ff9334", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/100/_/diff#comment-81756047"}}
{"title": "Choose whether to use a constant slope or use a inferred temperature model", "number": 101, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/101", "body": ""}
{"comment": {"body": "Does this need to be external API? Can it be internal?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/101/_/diff#comment-81747430"}}
{"comment": {"body": "Those are internal functions. Made them static in [4eddc24](https://bitbucket.org/levl/bosch_integration/commits/4eddc24b4f848f04d52ba4da2200dd77aa6b751a?at=feature/selection_of_cfo_model_slope).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/101/_/diff#comment-81748254"}}
{"comment": {"body": "Not for this PR but we need to organize our configuration. \n\nThere should be a very clear list of configuration types \\(with/without temperature model, etc\u2026\\) and for each one we need to have a struct of all the constants\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/101/_/diff#comment-81768323"}}
{"title": "Regression tests by features", "number": 102, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/102", "body": "Expand the regression tests to test the separation capabilities for each feature independently."}
{"comment": {"body": "isnt `buffer_len + buffer_len/2` is out of bounds of raw\\_iq\\_buffer?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/102/_/diff#comment-82120808"}}
{"comment": {"body": "No, since raw\\_iq\\_buffer is of size 2\\*`buffer_len\u00a0`  ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/102/_/diff#comment-82121987"}}
{"comment": {"body": "Very nice code,\n\nConsider adding a comment explaining these:\n\n    +#define LABEL1  (1 << 0)\n    +#define LABEL2  (1 << 1)\n\nIt\u2019s very confusing for anyone not familiar enough with our code", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/102/_/diff#comment-82129200"}}
{"title": "Hello DA1469X", "number": 103, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103", "body": "Before starting heavy development on the automatic system tests using 9x, I wanted to first bring all the existing 9x to develop for review.\nThis pull request contains the DA1469x SDK (version 10.0.1.39) and an example project that runs our fingerprinting solution on the new hardware (using 16Mhz downsampling to 8Mhz at the moment).\nIf you need help running this code and trying it out yourself, talk with me and Ill help you set it up.\nIssues:\n\nScans on all channels instead of just one (BIS-761)\nNo way to extract corresponding packet data and metadata after rx_en falling edge interrupt and thus no way to filter (FIN-543)\nFingerprinting library fails to find access code most of the time (FIN-542)\nSmall corruptions in IQ (missing samples) (FIN-541)\nNo support to extract board temperature yet (BIS-749)\n\nCommit messages - \n\nAdded 9x project from the a3b69dfb88969fc0163b2c56da90de581244f30b commit at the 9x_bringup repo\nImroved .gitignore for 9x and removed useless files\nAdded Debug9x and Release9x configurations for libfingerprinting. Modified it to support 9x (by downsampling at the moment) if DA1469X is defined\n9x - SDK restored to its original pure state, patch-files added instead. Disabled sleep\n\nDisabled Cortex-M33 deepsleep\n(cherry picked from commit 11ed6ab6fef804dbc482e0ee2639a3442677f498)\n\n\nble_iq_advertiser_task.c will now use LEVL_CAPTURE_PACKET_SIZE as defined in LEVL_fingerprinting.h as CAPTURE_PACKET_SIZE\n\nAdded levl_timers to 9x\nAdded sync_found_isr\n9x - deleted irrelevant projects, moved ble_beacon_iq_capture to projects directly\n9x - Renamed ble_beacon_iq_capture to ble_base_project\nDisabled running from RAM (too complicated and messy to support both)\n9x will now only define configs if they're not already defined somewhere else\n9x - Refactored diag logic to diag.h and diag.c\n9x - Most boilerplate is now in a library. Actual \"project\" is minimal and just links with the library that even provides the main function."}
{"comment": {"body": "You and your \u201csmall\u201d PRs\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82571520"}}
{"comment": {"body": "Which correction offset this is going to use?\n\nIt\u2019s looks like there is a missing entry for the 69x at `m_boardsource_2_baseband_correction_parameter_map`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82760247"}}
{"comment": {"body": "Don\u2019t know the correction for now. We will need to figure it out. It may be related to FIN-542", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82760270"}}
{"comment": {"body": "You know the board source in the input packet so wouldn\u2019t it be better the select the function in run-time?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82760274"}}
{"comment": {"body": "Looks really good! Great work!\n\nJust a general question, why the need to split the main loop into two projects?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82760323"}}
{"comment": {"body": "So we can make several projects \\(TOF, testing agent, fingerprinting demo, etc.\\) on top of the same boilerplate \\(SDK, main function initializing hardware, etc\\).\n\nI put the boilerplate in a library project and the projects themselves will have just a single task file \\(and any other files specific to them\\) and they just link with the boilerplate", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82760350"}}
{"comment": {"body": "The mhz16\\_IQ buffer doesn\u2019t exist for 8x, it\u2019s too big. We\u2019d maybe want to compile a separate configuration for 9x anyway for FPU optimizations", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82760370"}}
{"comment": {"body": "I see.  \nSo maybe have the define as the sample rate instead of the HW configuration. We also need to control the size of the input buffers to be different between 68x and 69x.\n\nWe need to figure out a way of how to mange both 8MHz and 16Mhz together without causing a lot of headaches..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82760412"}}
{"comment": {"body": "Forgot to mention an important reason why we need to compile two versions anyway - Cortex M0 vs Cortex M33\u2026\n\nAs for this - \u201cSo maybe have the define as the sample rate instead of the HW configuration\u201d:\n\nWe should have some `levl_platform.h` file where we define several constants like HAS\\_FPU, SAMPLE\\_RATE \\(and probably more in the future\\) then\n\n```c\n#ifdef DA1469x\n\n# define HAS_FPU (1)\n\n# define SAMPLE_RATE (16)\n\n#else\n\n \u2026\n\n#endif\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82760466"}}
{"comment": {"body": "We need a similar bosch\\_capture\\_iq project for 9x", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82760750"}}
{"comment": {"body": "Indeed a good job!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/103/_/diff#comment-82812498"}}
{"title": "Feature/BIS-625 get report of worst metrics", "number": 104, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104", "body": "Adding report of code metrics.\nActual report can be viewed on the Jenkins branch page, same way as code coverage.\n"}
{"comment": {"body": "Overall the table is great - it is what we need and it is automatic.\n\nThere are a few metrics missing - what can we do about it?\n\n* Number of GOTO - easily counted by running grep\u2026\n* call graph recursions\n* number of sw functions\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83094604"}}
{"comment": {"body": "Is the actual report \\(not the table\\) stored somewhere so we can see previous versions result?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83095022"}}
{"comment": {"body": "GOTOs and recursions are always 0 since we don\u2019t permit builds with these \\(MISRA compliance\\).\n\nI\u2019ll add the number of SW function", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83095165"}}
{"comment": {"body": "Kinda yes, but Klocworks stores the reports of the last 20 builds \\(configurable\\). I could add the report/whatever as a jenkins artifact", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83095425"}}
{"comment": {"body": "Yes, please add it, it will be usefull", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83101393"}}
{"comment": {"body": "OK!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83111728"}}
{"comment": {"body": "Fixed in 8416c81abcaaa832edd03a385ae767d72ecf14dd", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83180889"}}
{"comment": {"body": "Fixed in 8416c81abcaaa832edd03a385ae767d72ecf14dd", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83180939"}}
{"comment": {"body": "We also need worst case stack size\u2026 Is this parameter calculated now somehow?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83330417"}}
{"comment": {"body": "Not that I know of. I\u2019ll look into this in this issue: [https://jira.levltech.com:8443/jira/browse/BIS-30](https://jira.levltech.com:8443/jira/browse/BIS-30)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/104/_/diff#comment-83451845"}}
{"title": "Feature/BIS-785 hide library interface", "number": 105, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105", "body": "Hide internal data structures from external API\nChange structures (mostly in CFO model world) to not contain pointers since they differ between 32-bit and 64-bit"}
{"comment": {"body": "We don\u2019t really care if it\u2019s the exact size, just that it\u2019s big enough. It might be better to check that it\u2019s only big enough if we\u2019ll want to make the external structs larger to hide the size of the internal structs", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83283168"}}
{"comment": {"body": "It\u2019s better we just add the src directory to the include path instead of using relative includes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83284061"}}
{"comment": {"body": "Would work as well.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83295086"}}
{"comment": {"body": "Not sure what you mean", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83296593"}}
{"comment": {"body": "I\u2019m accepting your suggestion", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83297416"}}
{"comment": {"body": "You better", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83297497"}}
{"comment": {"body": "I\u2019m pausing this PR since images built on the jenkins server don\u2019t work although it works on my PC.   \n Seeing a hardfault when first calling `voting_table_init` in `Levl_ClassifyReset`. Could be due to alignment issues.\n\nWill continue this PR when fixed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83451885"}}
{"comment": {"body": "Fixed in 98aa163c8b8b51f1f7469dbee1e91eab5fdf09f1", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83452414"}}
{"comment": {"body": "Alignment issues fixed. You may continue commenting and approving", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83452497"}}
{"comment": {"body": "Why use uint32\\_t instead of uint8\\_t?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83454378"}}
{"comment": {"body": "It\u2019s due to a comment I wrote. It\u2019s to force the compiler to put the struct in a dword-aligned memory \\(we need it to do so because when we cast to our internal struct we need it to be uint32\\_t aligned as it\u2019s our biggest member\\)  \n Maybe a comment explaining why we\u2019re using uint32\\_t\u2019s here would be nice\n\nNote that it can also be done with non-portable GCC directives, or we can simply tell the user to make sure those structs are allocated in aligned memory, but I think this is easier", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83454389"}}
{"comment": {"body": "Got it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83454407"}}
{"comment": {"body": "Forcing alignment. I used uint8\\_t and it caused the compiler to have unaligned arrays, causing unaligned access when casting to internal interface.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83454513"}}
{"comment": {"body": "We could also check alignment in our library during runtime \\(by checking pointer address is product of 4\\). Not sure if it\u2019s worth the effort, although it\u2019s a few lines of code.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83454543"}}
{"comment": {"body": "Commented in 8465b9af67c3dbd24e586c7c1573f080f00295dd", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/105/_/diff#comment-83454893"}}
{"title": "Code coverage to 100% - part I", "number": 106, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/106", "body": "To raise the coverage I either:\n\nAdd new tests to cover the untested paths\nRemoved unused and unreachable code"}
{"comment": {"body": "what\u2019s the reason behind the removal of the saturation?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/106/_/diff#comment-83451987"}}
{"comment": {"body": "It was an unused / unreachable code.\n\nAlso we don\u2019t know how the external code will behave it if gets a saturated results, which leaves us as the same place as with just returning a result that is an overflow.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/106/_/diff#comment-83452782"}}
{"comment": {"body": "Shouldn\u2019t we then test how our code behaves if we get very high values that cause saturation?  \nIt\u2019s also good practice to saturate multiplications instead of letting them overflow", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/106/_/diff#comment-83452812"}}
{"comment": {"body": "There are a lot of assumption about the values of the input, in many case when it\u2019s input is int8 and int16 and so we can use 32 bits numbers safely.\n\nI don\u2019t see why q31 should be treated differently just because it\u2019s a basic type.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/106/_/diff#comment-83454327"}}
{"comment": {"body": "We should look at each case where saturation was used and determine if it\u2019s a likely scenario or not and consider how it may affect the algoirthm  \n  \nAlso, with floating point types this is no longer an issue \\(numbers can get very large with floats\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/106/_/diff#comment-83454375"}}
{"title": "Feature/BIS-387 integration tests infra", "number": 107, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/107", "body": "Added Windows SWIG support to repo (not actually used for integration tests)\nAdded pyfingerprinting_reborn based on ctypes, added wrappers to it\nAdded training integration tests and some other API tests\nChanged libfingerprintings logic to provide model on the last packet"}
{"title": "Cfo model add features", "number": 108, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/108", "body": "Added logic for relearning temperature device slop when outside of a given range.\nAdded logic for increasing the confidence interval when the temperature is far from the one we saw during the training.\n\n\n\nadd min,max known temperature logic in cfo model\nremove board_std, device_std 50 instead of 0\nremove board temperature std\nthe updated regression for cfo model works with 1d data\nremove cfo_vars_inverse from cfo_model_features_to_matrix struct\nadd min and max device temperature slops\nfix min and max temperature init + fix in intercept fix when updating the temperature slop\nminor\nmove 50 to define\ntest fix\nadd test for outside of bound slop\nmore informative variable names\nmove anothe constant to define\nuse average temperature instead of (max+min)/2 for slop out of bound fix\nmove value to static\nadd unitest for predicting outside of known temperature range"}
{"comment": {"body": "Do we have tests for all of these different cases?\n\n* `(cfo_model_state->device_temperature_const == -1) && res->temperature_device > cfo_model_state->max_allowed_slop`\n* `(cfo_model_state->device_temperature_const == -1) && res->temperature_device < cfo_model_state->min_allowed_slop`\n* `cfo_model_state->device_temperature_const != -1`\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/108/_/diff#comment-83686470"}}
{"comment": {"body": "Now we do", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/108/_/diff#comment-83688599"}}
{"title": "9x, this time including the system testing infrastructure (previous 9x pull request canceled)", "number": 109, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109", "body": "Hopefully this is the last unreviewable pull request I make (the previous one is contained in this one)\n\nAdded 9x project from the a3b69dfb88969fc0163b2c56da90de581244f30b commit at the 9x_bringup repo\nImroved .gitignore for 9x and removed useless files\nAdded Debug9x and Release9x configurations for libfingerprinting. Modified it to support 9x (by downsampling at the moment) if DA1469X is defined\n9x - SDK restored to its original pure state, patch-files added instead. Disabled sleep\n\nDisabled Cortex-M33 deepsleep\n(cherry picked from commit 11ed6ab6fef804dbc482e0ee2639a3442677f498)\n\n\nble_iq_advertiser_task.c will now use LEVL_CAPTURE_PACKET_SIZE as defined in LEVL_fingerprinting.h as CAPTURE_PACKET_SIZE\n\nAdded levl_timers to 9x\nAdded sync_found_isr\n9x - deleted irrelevant projects, moved ble_beacon_iq_capture to projects directly\n9x - Renamed ble_beacon_iq_capture to ble_base_project\nDisabled running from RAM (too complicated and messy to support both)\n9x will now only define configs if they're not already defined somewhere else\n9x - Refactored diag logic to diag.h and diag.c\n9x - Most boilerplate is now in a library. Actual \"project\" is minimal and just links with the library that even provides the main function.\nStarted blank testing agent\nBasic event reporting & handling, example of test-fixture \"board_agent\"\nImported some generic files from the china demo to the boilerplate library\nProject now works with off-the-shelf Eclipse-MCU\nFixed weird Eclipse bug by changing the include guard of custom_config_qspi.ht\nAdded feature extraction and packet events, improved event handling Python logic\n9x Basic test example working\nAgent will now use bosch demo protocol, improved example tests\nPytest moved fixtures to their own directory\nAdded advertising phone\nFix systest demo review changes\nFixed annoying numpy warning and better error when no boards are connected\n9x - Moved a lot of code away from ble_iq_advertiser_task.c into the boilerplate library\nIQ capture improved (ft.ex success rate significantly improved), fixed RFMON wrapper bugs, IQ print fixed, will now make use of both rx_en-fall and sync_found interrupts to capture\nIncreased packet count in example tests because we now get many more packets, removed advertising_phone because it doesn't work well and slowsa tests down for now\n9x tests - Added classification progress event to agent\n9x tests - Added training progress & model events\n9x - Added 2 real tests, deleted demo tests, improved phone model selection\n9x tests - Added another real test\n9x - Fixed observer bug\nDownsampling no longer part of the library\n9x - Improved boilerplate compile time\nfixed jlink_wrapper.py bug\nIQ rotation will no longer be performed by the fingerprinting library\n9x testing - Added CRC to events, disabled IQ print\n9x - Added IQ capture project, moved a lot of common code to boilerplate\n9x - Changed testing agent to user more boillerplate\n9x - Moved events inside boilerplate\n\n9x testing - several improvements to testing infrastructure\n\nAdded stream-like event reporting\nInterface to CRC interface simplified\nClassification progress will now also report current classification result\nEvents now contain a counter to detect missing events\nAdded lots of improvements and documentation to board_instance.py\n\n\n\n9x testing - Added constant size of 30 to data_len in fingerprinting_process_packet to help library calculate falling transient length\n\nFixed preaemble unittests\nFixed regression tests builder CapturedPacket struct\nUpdated regression datasets"}
{"comment": {"body": "Big work! Great to see it passed our unitests :\\). Were they executed on your code?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84072243"}}
{"comment": {"body": "No, for that to happen we\u2019d have to create automatic build for the library and the agent, connect S9 and S8 and a 9x to the Jenkins lab slave and have the script automatically program the board with the compiled software. That\u2019s quite a lot of work just so the tests run automatically, so I think we\u2019ll keep that for later.  \n  \nThe tests pass because finally my changes to the fingerprinting library don\u2019t break anything", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84072740"}}
{"comment": {"body": "Wow! That\u2019s a lot of changes!  \nGreat job for managing to finish this all around :slight_smile: \n\nI will go over the important changes to the library and the testing infra.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84087503"}}
{"comment": {"body": "Just a comment on that. This struct is part of the external interfaces so we need to keep it in sync with the definiton in the EIS document.  \nIt\u2019s not important for now \\(as before your change it was till not compatible\\), we can do it later.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84087716"}}
{"comment": {"body": "I don\u2019t seem to find the implementation of this function. Did you forgot to commit it by any chance?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84088990"}}
{"comment": {"body": "Leftover no longer relevant declaration I forgot to remove", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84090143"}}
{"comment": {"body": "Actual down sampling is left to the user", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84090244"}}
{"comment": {"body": "what\u2019s the purpose of this field?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84119203"}}
{"comment": {"body": "`packet_data` is an array, not an uint64", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84119489"}}
{"comment": {"body": "what\u2019s the reason behind `board_source` not being an enum?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84119599"}}
{"comment": {"body": "I\u2019d like to hear the story behind this", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84119838"}}
{"comment": {"body": "\ufeffRfmon contains an overflow register that indicates that some samples are missing. This field will be useful for debugging test results and might be useful in the future to give less weight to packets that may contain errors. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84120256"}}
{"comment": {"body": "That's definitely a mistake. I'll fix. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84120321"}}
{"comment": {"body": "\ufeffDidnt find a way to specify an exact size for ctypes enums. I also changed it to an explicitly sized type in the corresponding c field. Enums are too unpredictable and can change sizes on a compiler whim", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84120419"}}
{"comment": {"body": "Long story short numpy warns me about something I do on purpose so I suppress the warning which only occurs a single time during tests by raising on purpose and suppressing it\n\n\u200c\n\nRight now the timestamps of events are in 24 bit microseconds which is just half a second. That means it often overflows. Subtracting two timestamps regardless of whether an overflow occurred yields a correct result as long as no more than half a second passed. Numpy raises an annoying warning when the subtraction overflows but it only does it once. That's why I do it in the beginning of the tests on purpose while suppressing the warning so it doesn\u2019t show up on actual test results", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84120875"}}
{"comment": {"body": "I think we should do the downsampling inside the library:\n\n1. If we might not support 16MHz in V1, we will sure do support in V2 and I don\u2019t think we need to bother bosch with preprocessing code of the IQ. I just want them to send us the direct samples they got from the chip.\n2. The \u201cproper\u201d way to do downsampling is to apply a low pass filter of the data before the decimation to prevent aliasing effects. So the best place to do it is after dc offset removal \\(which is a low pass filter operation\\).\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/109/_/diff#comment-84130894"}}
{"title": "CFO extraction", "number": 11, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/11", "body": "*first attempt at Cython & C\n*updating embedded projects and C exports\nmoving around files trying to make pipelines work\nmaybe now pipelines would work\ncorrecting cmake dependency\njust adding placeholder for clean_cfo\n*adding basic test env with cmake & Catch2 unittests env\n*updated cmakelists for CI\nfixing pipelines yml\n*renaming files\n*renaming them back\n*trying to fix pipelines make failure\n*another try at cmakelist\n*not running tests as a seperate step\n*pipelines should get test reports\n*fixing yml indentation\n\ndone implementation of clean_cfo\n\nadded helper for complex numbers\nadded unittests for complex numbers and clean_cfo\n\n\n\nsome refactoring to complex functions fixing gcc compilation issue\n\n*moving files around\n*cleanup of files\nstarting to create env for lib export for ARM added lib header\n*fixed pipeline\nmoving build files around\nanother pipeline fix\nanother try at pipelines & ARM\n*corrected ARM package to be installed\n*added missing package to pipelines\nanother try\n*skipping ARM building for now\nexpanding complex functions adding rolling mean and tests (WIP)\nadding some comments finishing implementation for rolling mean\nmoving files to commons directory adding circular buffer rotation function and some tests *slightly modifiction API to allocate more memory\n*CI should also be updated with updated format\nfixing issue with GCC\ntypo fix\nintegrating preprocessing flow into embedded project embedded project links succesfully ARM library in current directory structure\nremoving python references moving preprocessing files around\n*minor .gitignore change\n*adding skeleton for feature extraction module\nlib build scripts are now smarter added windows script to build ARM lib\n*splitting types_helper to q31_math and q15_math to mitigate #undef\n*optimizing rolling mean routine\nrenaming rolling mean to dc removal\n*fixing regression of dc removal\n*creating stub of falling transient\n*starting to implement helper functions for falling transient\n*fixing feature extraction cmakelist\nAdded calc_freq_offset()\nupdate math.c from NXP project\nfix compilation\nfix warning\nadding fast math library with fast sqrt added q31 statistics library with mean, var and std *starting to add falling transient\nfixed int32 to q31\nAdded extract packet function\nfix warning\nfix compilation\nadding more fast math and q31 functions updating statistics API *adding \"extra macros\" file, until a better name is found\nadded uphase()\n*naive transient detection implementation finished. requires testing\nadded get_alignment_correction()\n\nbugfix in transient extraction\n\nadded input valiation in transient extraction\nadded unittest to transient extraction\n\n\n\nadded demod_fsk()\n\naccidentally implement rising transient extraction so refactored to support both rising and falling based on API input added simple tests\n*renaming to transient extraction\nCherry picked ee2d915\n*some comments\n*another compilation issue\n*fixing whitespace (tab -> 4 spaces)\n\nremoving extra_macros.h and moving min/max to fast_math\n\nsome refactoring to mitigate code duplication in weighted diff\nadding comments and making code clearer\n\n\n\nadding documentation to some modules\n\nadding more compliance with MISRA\n\n\n\nremoving mock file\n\nadded unittest for transient extraction with real packet\n\n\n\nfixing circular buffer refactoring - unused variable adding tests for coverage\n\nremoving libfingerprinting_naive and libfingerprinting_device_id projects fixing definitions not letting embedded project to compile with lib *updating lib build script to create make file project\n*fixing compilation of arm lib and embedded project\n\nfixing embedded project\n\ntested preprocessing embedded result with PC result (requires refactoring)\n\n\n\n*minor code reuse in embedded project\n\nadded linear regression functions\nsetting preprocessing as a feature extraction module moving files and tests around\n*fixing CI file\n\ncreating tests_commons\n\nfixing cmakelist for preprocessing executable\n\n\n\n*merging CMakefiles and removing static libs created from modules for easier integration\n\n*creating mock for preamble extraction\n*adding demod FSK sample and comments\nadded cfo model\ncmake update\nsmall updates\nupdated the pipeline\ngit ignore some idea files\nmaybe needed\nanother ignores\nfix conflict take 2\nI didn't touch it, its your git!\nmissing comment in previous commit and fixing comments adding module access code location with plain C impl and added very small test\n*added missing include\n*fixed preprocessing type casting\n*fixed tests\nfixed some code style comments\nadded some consts and comments\ndeleted in develop\n\nreplaced naive implementation with optimized bit search\n\ncompleted test and documentation\n\n\n\nfixed some misra issues and added comments\n\n*integrated access code location in preprocessing stage\ncompilation fix\nfix review comments\nremoved q31 from cfo extraction\nmoving all cfo functions to fast_math\nmoving all cfo functions to fast_math\nspiting search pattern insert sample behavior minor static analysis warnings removal\nAdded cfo preprocessing\nadded internal_packet.h\nfix reviews\nfix reviews"}
{"title": "Feature/BIS-614 fix misra issues", "number": 110, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110", "body": "Update library to comply with MISRA 2012 (C99) Am. 1.\nOpen issues:\n\nMISRA.DEFINE.FUNC (LEVL_PRINTF macro, static_assert macros)\nMISRA.CAST.OBJ_PTR_TO_OBJ_PTR.2012 (external to internal conversion, IQ extraction from circular buffer) - will be dealt when merged with 9x\nMISRA.UNION (captured packet IQ union) - will be dealt when merged with 9x\nMISRA.LITERAL.NULL.PTR.CONST.2012 - Klocworks false positive; Need to update tool/ignore issue"}
{"comment": {"body": "Wow a lot of changes :slight_smile: \n\nHow many errors does it bring us down to?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84149413"}}
{"comment": {"body": "From 970 down to 28", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84151016"}}
{"comment": {"body": "Can you still keep the original float sequence as a comment?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84151358"}}
{"comment": {"body": "\u0421ool!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84152207"}}
{"comment": {"body": "Sure", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84154573"}}
{"comment": {"body": "Fixed in 85427a6bd234a038eff265d53cd80f2ef44d27f8", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84159441"}}
{"comment": {"body": "Awesome!\n\nIs there still a lot of work battling the remaining 28 issues?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84159921"}}
{"comment": {"body": "Not much work:\n\n* 4: [MISRA.DEFINE.FUNC](http://ec2-3-120-109-79.eu-central-1.compute.amazonaws.com:8080/documentation?topic=reference/misra.define.func.htm&product=Insight&checker.help=true)\u00a0\\(LEVL\\_PRINTF macro, static\\_assert macros\\) - need to deviate\n* 17: MISRA.CAST.OBJ\\_PTR\\_TO\\_OBJ\\_PTR.2012 \\(external to internal conversion, IQ extraction from circular buffer\\) - deviate or will not be compatible with 8x \\(requires more memory\\)\n* 2: [MISRA.UNION](http://ec2-3-120-109-79.eu-central-1.compute.amazonaws.com:8080/documentation?topic=reference/misra.union.htm&product=Insight&checker.help=true)\u00a0\\(captured packet IQ union\\) - deviate or will not be compatible with 8x \\(requires more memory\\)\n* 5: [MISRA.LITERAL.NULL.PTR.CONST.2012](http://ec2-3-120-109-79.eu-central-1.compute.amazonaws.com:8080/documentation?topic=reference/misra.literal.null.ptr.const.2012.htm&product=Insight&checker.help=true)\u00a0- Klocwork\u2019s false positive; Need to update tool/ignore issue  \n    \n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84246717"}}
{"comment": {"body": "PR update:  \n Merged with 9x big merger so some MISRA issues were fixed.  \n Note that interface is also using Klocwork-specific macro `__KLOCWORK__` so that it ignores the union. Once we get rid of the union completely, this define can be safely removed.\n\nReduced to 26 issues, without the 2 [MISRA.UNION](http://ec2-3-120-109-79.eu-central-1.compute.amazonaws.com:8080/documentation?topic=reference/misra.union.htm&product=Insight&checker.help=true) issues.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84247806"}}
{"comment": {"body": "Have you been able to verify that the running time of our system didn\u2019t change after all those updates?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84249653"}}
{"comment": {"body": "Yes, not something serious.\n\nAfter changes:\n\n    Max duration: 47.000000 ms\n    Average duration: 45.098113 ms\n    Std duration: 1.481265 ms\n    \n\nBefore:\n\n    Max duration: 47.000000 ms\n    Average duration: 43.879245 ms\n    Std duration: 1.559071 ms\n    ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/110/_/diff#comment-84249694"}}
{"title": "Feature extraction integration test", "number": 111, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/111", "body": "Feature extraction integration tests\n\n\n\npreamble first test+helper func to convert raw db data to c input\nmore preamble tests\nmore packets\nfinished preamble test\nfalling transient test\nfinished falling transient mean test\nfalling transient finished\nadd cfo test\ncfo packets\nremove some code duplication"}
{"comment": {"body": "Do you think that this function is general enough to be used in other tests?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/111/_/diff#comment-84246894"}}
{"comment": {"body": "It might be. \n\nI suggest merging all the tests together and then go over the places where we can use this function \\(or some modification of this function\\)\n\nWDUT?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/111/_/diff#comment-84247018"}}
{"comment": {"body": "GEFN", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/111/_/diff#comment-84247044"}}
{"comment": {"body": "I am not sure if we should check the exact tests with an exact condition now or not?\n\nMaybe we can do now what you said where, after the c code freeze we save the current c results and compare to them?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/111/_/diff#comment-84247049"}}
{"comment": {"body": "Not sure what you mean. Why shouldn\u2019t we check the result?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/111/_/diff#comment-84247136"}}
{"comment": {"body": "so the condition should be : `all(np.abs(np.array(all_res) - res) == 0)`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/111/_/diff#comment-84247266"}}
{"comment": {"body": "Oh, I see. Sounds good, will update after the freeze.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/111/_/diff#comment-84247756"}}
{"comment": {"body": "I think we should we check that all returned success==true", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/111/_/diff#comment-84252300"}}
{"title": "integration tests", "number": 112, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/112", "body": "valid transmitter and receiver Temperature tests\nvalid CRC test\nfeature extraction cfo exact test"}
{"title": "Fix ctypes", "number": 113, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/113", "body": "There was an issue with the python interface.\nOnce this PR is merged, make sure your PRs comply with the update structure"}
{"title": "Nuriel tests", "number": 114, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/114", "body": "\n\nadded tests of device and board temperatures\nupdate crc tests\nupdate cfo exact test\nadded helper function to change iq to raw fromat bytes format"}
{"comment": {"body": "There are few conflicts with test\\_feature\\_extraction in my branch - test\\_cfo\\_exact func name seems like the only real one.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/114/_/diff#comment-84251814"}}
{"comment": {"body": "fixed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/114/_/diff#comment-84252090"}}
{"comment": {"body": "Should we also test that all FeatureExtract\\(\\) returned success = true?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/114/_/diff#comment-84252161"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/114/_/diff#comment-84434733"}}
{"title": "Added documentation to the functions", "number": 115, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/115", "body": ""}
{"title": "Feature/BIS-614 enforce misra issues", "number": 116, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/116", "body": "Start enforcing MISRA compliance from this PR and on. \nPermitted deviations (in this PR) are:\n\n\n"}
{"title": "HW classification tests", "number": 117, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/117", "body": ""}
{"comment": {"body": "Guy - you merged a PR that did not pass its tests\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/117/_/diff#comment-84587910"}}
{"comment": {"body": "The build failed since it does not have a Dialog 9x attached to the testing PC.\n\nI\u2019ll attach one and re-run the build when I\u2019ll be at the office.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/117/_/diff#comment-84593259"}}
{"title": "9x testing - Modified, added and documented all feature extraction tests", "number": 118, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/118", "body": ""}
{"comment": {"body": "Wouldn\u2019t it be better to wait until we get 100 valid samples? There\u2019s a chance to get a bad packet once in a while.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/118/_/diff#comment-84292446"}}
{"comment": {"body": "We need to somehow make sure that we dont get too many invalid CFOs\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/118/_/diff#comment-84297720"}}
{"comment": {"body": "Are these all the changes? didnt you have changes for the infrastructure?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/118/_/diff#comment-84298335"}}
{"comment": {"body": "Is is legitimate to require all of them to be valid?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/118/_/diff#comment-84313940"}}
{"comment": {"body": "Not for the feature extraction, we did have some changes for the training tests on another branch.\n\nThis is actually\u00a0[@Omer Tuchfeld](https://bitbucket.org/omer_levltech)\u00a0\u2018s changes, I\u2019ve just opened the pull request so we could merge the tests back to the develop branch.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/118/_/diff#comment-84314172"}}
{"comment": {"body": "It is legitimate if it works :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/118/_/diff#comment-84618518"}}
{"title": "Main loop for 9x fixes after MISRA changes to code", "number": 119, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/119", "body": ""}
{"comment": {"body": "How is this related to MISRA changes?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/119/_/diff#comment-84324861"}}
{"comment": {"body": "Well. This specific one isn\u2019t..  \nIt just a fix that someone forgot to do while changing the interface.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/119/_/diff#comment-84325638"}}
{"title": "CFO extraction", "number": 12, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/12", "body": ""}
{"comment": {"body": "missing unittests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/12/_/diff#comment-70649167"}}
{"comment": {"body": "right, Dima will add the unittests\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/12/_/diff#comment-70650116"}}
{"comment": {"body": "add m\\_ to member variables, as per coding style", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/12/_/diff#comment-70650231"}}
{"title": "Feature/BIS-815 klocwork report", "number": 120, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/120", "body": "Theres a link to the klocwork report in the build artifacts. Look for static_analysis_link.html. Open this file and click the link (or right-click and open in new tab).\nAlso fixed the number displayed for Static path count metric."}
{"comment": {"body": ":thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/120/_/diff#comment-84372510"}}
{"title": "Feature/FIN-559 code quality tests", "number": 121, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/121", "body": "Add code quality tests and relevant changes to the project to make some of them work. A couple of tests fail"}
{"comment": {"body": "Please decide if should add branch coverage or remove the test", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/121/_/diff#comment-84488966"}}
{"comment": {"body": "We\u2019ll remove the test later once we reach 100%", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/121/_/diff#comment-84491924"}}
{"comment": {"body": "Why do we need the non-stripped version?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/121/_/diff#comment-84595269"}}
{"comment": {"body": "To be able to debug the stripped version by loading symbols from the non-stripped version", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/121/_/diff#comment-84598564"}}
{"title": "Feature/FIN-559 IIT feature training", "number": 122, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/122", "body": "Add ITD 001 to 016 tests in C++ env (due to requirement to observe progress)\nAdd implementation (except for return errors)\nThis implementation causes the regression tests to fail so theyre marked as mayfail"}
{"title": "Remove SWIG from codebase", "number": 123, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/123", "body": ""}
{"title": "Feature/channel in cfo model", "number": 124, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/124", "body": "Added BLE channel to cfo model\n\n\nadd channel to cfo_model struct, add function to convert channel index to frequency\nrevert the const board temperature change\nadd option to const channel and board temperature with same mechanism as device temperature\nuse the new interface in main_loop\nuse the new interface in main_loop take 2\nadd channel to model WIP\nfix unitests to run with the channel in cfo model\nfix cfo unit tests continue\nfix bug + add unitest for training with channel\nremove some duplicated code + mode matrix nrow, ncol to be unsigned\nfix integration tests"}
{"comment": {"body": "@gregory-levl \n\nIs this MISRA compliant? shouldn\u2019t it have brackets for the cast?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/124/_/diff#comment-84905899"}}
{"comment": {"body": "You should make sure that idx\\_data is not bigger than MODEL\\_SIZE.\n\nSame for the if before.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/124/_/diff#comment-84906251"}}
{"comment": {"body": "It\u2019s valid. You can\u2019t cast a complex expression \\(nrow is unsigned and 1 is signed\\), so first cast the unsigned and then the complex expression is valid.\n\nAnyway, trust Klocwork until you get a false positive", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/124/_/diff#comment-84907021"}}
{"comment": {"body": "I remember something about the order of the operators, like you can\u2019t do \n\n    1 * 2 + 3\n\nyou must do \n\n    (1 * 2) + 3\n\nDoesn\u2019t it apply to castings too?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/124/_/diff#comment-84907248"}}
{"comment": {"body": "For cast, no. For other cases, like what you said and `i <= nrow - 1`, it\u2019s required \\(turn to `i <= (nrow - 1)`\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/124/_/diff#comment-84907873"}}
{"comment": {"body": "We already have the code analysis integrated into the Jenkins so if the build is passing it means it\u2019s MISRA compatible :wink:   \n\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/124/_/diff#comment-84912734"}}
{"comment": {"body": "There isn\u2019t anything he can actually do with a wrong value as there are no exit paths here\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/124/_/diff#comment-84929660"}}
{"title": "Feature/BIS-815 klocwork report update", "number": 125, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/125", "body": "Break bosch_integrations branches into 3 Klocwork projects: regular/develop/master"}
{"title": "Test pull request", "number": 126, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/126", "body": ""}
{"title": "CRC verification on incoming packets", "number": 127, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/127", "body": "First step: Validate CRC on all incoming packet"}
{"comment": {"body": "Can you add cpp protection?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/127/_/diff#comment-84862030"}}
{"comment": {"body": "Copyright is missing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/127/_/diff#comment-84862073"}}
{"comment": {"body": "What about unit tests for CRC24?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/127/_/diff#comment-84862327"}}
{"comment": {"body": "Fixed in 388fe2fff7dce66630f1b4716f3dda46702f162a", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/127/_/diff#comment-84863379"}}
{"comment": {"body": "There is nothing to protect :slight_smile: \n\nBut anyway fixed in 388fe2fff7dce66630f1b4716f3dda46702f162a", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/127/_/diff#comment-84863412"}}
{"comment": {"body": "I\u2019m not familiar enough, but do we have the packet\\_len field in 9x?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/127/_/diff#comment-84863612"}}
{"comment": {"body": "Add unit test in ef5431cff4fe8d15cb515a4985524f8ee856d8a4", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/127/_/diff#comment-84864506"}}
{"comment": {"body": "No. There isn\u2019t.\n\nWe have to figure it out from the data. Upcoming in part II.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/127/_/diff#comment-84864558"}}
{"title": "Feature/BIS-921 update EIS", "number": 128, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128", "body": "Update interface to comply with latest EIS\nCode coverage is also calculated for python integration tests"}
{"comment": {"body": "Coverage before integration tests:\n\n* Lines: 97.4 %\n* Branches: 91.3 %\n\nCoverage after integration tests:\n\n* Lines: 98.6 %\n* Branches: 94.0 %\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84869120"}}
{"comment": {"body": "Very good!\n\n:ok_hand: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84905609"}}
{"comment": {"body": "Can you also change the interface calls in the main\\_loop for the 9x hardware?\n\nCan be done as a part of a separate PR..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84905663"}}
{"comment": {"body": "Why not enum?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84907049"}}
{"comment": {"body": "Not defined in EIS as enum\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84907126"}}
{"comment": {"body": "Done in 9b403f44101d78a7cf986abda6899d09d52cdca7", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84910180"}}
{"comment": {"body": "Please add the error codes:\n\nLEVL\\_FEATURE\\_ERR\\_PHONE\\_TEMP\\_OUT\\_OF\\_BOUNDS  \n\nLEVL\\_FEATURE\\_ERR\\_RECEIVER\\_TEMP\\_OUT\\_OF\\_BOUNDS  \n\nLEVL\\_FEATURE\\_ERR\\_INVALID\\_CHANNEL  \n\nLEVL\\_FEATURE\\_ERR\\_INVALID\\_RSSI  ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84912236"}}
{"comment": {"body": "Future PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84912455"}}
{"comment": {"body": "Training errors are missing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84912584"}}
{"comment": {"body": "Classification errors also missing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84912880"}}
{"comment": {"body": "Future PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84914790"}}
{"comment": {"body": "Future PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/128/_/diff#comment-84914887"}}
{"title": "Release branches have different project in static analysis", "number": 129, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/129", "body": "Release branches have different project in static analysis"}
{"title": "History voting table for classification", "number": 13, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/13", "body": "Implementation of a voting table to keep the last sliding window of N votes and return an aggregated result of the voting."}
{"comment": {"body": "We need to decide if structs end with \\_st or \\_t", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/13/_/diff#comment-70648911"}}
{"comment": {"body": "Need to add test that voting\\_history\\_size < MAX\\_VOTING\\_SIZE", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/13/_/diff#comment-70648932"}}
{"comment": {"body": "should be \u201c<\u201c and not \u201c<=\u201d in  \u2018i <= voting\\_table->num\\_votes\u2019", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/13/_/diff#comment-70648984"}}
{"title": "Feature/hw testing", "number": 130, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/130", "body": "Simply added the HW tests"}
{"comment": {"body": "[os.name](http://os.name) == \u2018posix\u2019 is the way to actually check for mac/linux vs windows", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/130/_/diff#comment-85022631"}}
{"comment": {"body": "I think this code came from Dialog.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/130/_/diff#comment-85133408"}}
{"comment": {"body": "This code came from the LEVL repo.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/130/_/diff#comment-85133706"}}
{"comment": {"body": "This code came from the LEVL repo", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/130/_/diff#comment-85133738"}}
{"comment": {"body": "This code came from the LEVL repo.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/130/_/diff#comment-85133776"}}
{"comment": {"body": "Why was the -1 removed?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/130/_/diff#comment-85136935"}}
{"comment": {"body": "It was removed due to buffer overflow by one byte. Without the one byte is actually the correct size.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/130/_/diff#comment-85158693"}}
{"title": "Infering the channel number from the CRC and scrambler", "number": 131, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/131", "body": "When a channel number is not provided to the library, the library will try to enumerate the different ADV channels (37,38,39) and see if it matches (passes CRC after applying the scrambler)."}
{"comment": {"body": "Can you add documentation to this API?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/131/_/diff#comment-84923721"}}
{"comment": {"body": "You can also return the LEVL\\_FEATURE\\_ERR\\_INVALID\\_CHANNEL  error if the channel is not correct", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/131/_/diff#comment-84924994"}}
{"comment": {"body": "I add a return code of LEVL\\_FEATURE\\_ERR\\_CANNOT\\_DETECT\\_PACKET. \\(commit [bf7fd12](https://bitbucket.org/levl/bosch_integration/commits/bf7fd120e2daf4e0eac73da86460deff0aedf06c)\\)\n\nThe LEVL\\_FEATURE\\_ERR\\_INVALID\\_CHANNEL is for supplying a channel number that is our of the legal BLE channel range..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/131/_/diff#comment-84933117"}}
{"comment": {"body": "Fixed in [4d96d49](https://bitbucket.org/levl/bosch_integration/commits/4d96d496d78af63846254ee764892fa10cd03ce9)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/131/_/diff#comment-84934933"}}
{"comment": {"body": "Unittests for this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/131/_/diff#comment-84935674"}}
{"comment": {"body": "I\u2019ll added them in a future PR.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/131/_/diff#comment-84936085"}}
{"title": "Feature/BIS-921 update EIS part 2", "number": 132, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/132", "body": "Updated feature validation stages\nAdded some ITD"}
{"comment": {"body": "You can also have `LEVL_FEATURE_ERR_CANNOT_DETECT_PACKET` error here and in that case you will return incorrect error", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/132/_/diff#comment-84956092"}}
{"comment": {"body": "LEVL\\_TRAIN\\_TIMEOUT  error is missing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/132/_/diff#comment-84957296"}}
{"comment": {"body": "LEVL\\_CLASSIFY\\_TIMEOUT  is missing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/132/_/diff#comment-84957416"}}
{"comment": {"body": "Please put the copyright that all the other files have", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/132/_/diff#comment-84957938"}}
{"comment": {"body": "Actually thats a test file - it doesnt matter", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/132/_/diff#comment-84958075"}}
{"title": "ITD-057 test", "number": 133, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/133", "body": "Adding integration tests"}
{"title": "Caputed packets according to EIS and support 9x packets", "number": 134, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/134", "body": "Major changes:\n\nInput packets updated to the interface in EI\nPackets size updated to 8000 on 9x \nLibrary for tests are now compiled using 32 bits compilation\nRegression data set taken from 9x oven records with all ADV channels\nTransient length is now measured from end of data to the noise floor\nFixed classification with channel model"}
{"comment": {"body": "About `Levl_Classify_Validate` and `Levl_Train_Validate`, what\u2019s the reason for not validating the feature set as well? The only way to get an invalid feature set is if Bosch fed the library a bad input even though FeatureExtract said that it\u2019s a bad feature set. Might as well be an critical error \\(due to a bug in their system\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/134/_/diff#comment-85019032"}}
{"comment": {"body": "What\u2019s the reason behind this change?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/134/_/diff#comment-85019086"}}
{"comment": {"body": "Using the transient length between the end of the packet until the falling part is much more stable. The standard deviation is smaller and hence the regression tests can actually pass because the transient differentiate between devices.. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/134/_/diff#comment-85019281"}}
{"comment": {"body": "We will validate the feature set once we have encryption so we would be able to validate when we got a bad input..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/134/_/diff#comment-85019293"}}
{"title": "Unit code coverage complete", "number": 135, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/135", "body": "All units have 100% code and branch coverage.\nThe remaining coverage of all fail cases of LEVL_fingerprint.c will be covered in the integration tests."}
{"title": "Infer packet length from data while demodulating", "number": 136, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/136", "body": ""}
{"comment": {"body": "Are there any tests covering this feature?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/136/_/diff#comment-85024381"}}
{"title": "Feature/BIS-921 update EIS part 3", "number": 137, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/137", "body": "Fix python interface\nFix klocwork HTTPS path"}
{"title": "Fix demod FSK ranges", "number": 138, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/138", "body": "Fix critical regression with demod fsk"}
{"title": "Bugfix/BIS-1068 Demodulated data not calculated", "number": 139, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/139", "body": "Fix cmake\nApply correct range to demodulation end index"}
{"title": "Timing feature extraction", "number": 14, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/14", "body": "Extraction of the timing interval feature between two consecutive BLE packets."}
{"comment": {"body": "In case \u2018valid\\_interval\u2019 is false the \u2018interval\\_out\u2019 param will be uninitialized.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/14/_/diff#comment-70649019"}}
{"title": "Interface now receives signed values of temperatures", "number": 140, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/140", "body": ""}
{"comment": {"body": "Please update system tests as well", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/140/_/diff#comment-85028905"}}
{"comment": {"body": "Done in a769b7b4bb283e6c47d2b976fe5b94b58f033f17", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/140/_/diff#comment-85029056"}}
{"title": "Tests/ITD-082 083 rom ram validation", "number": 141, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/141", "body": "Build will now fail is we use too match RAM/ROM in the arm library\nSize report will be checked for Bosch limits inside test_code_quality.py\nFixed test_code_quality.py PyCharm formatting errors and renamed test_code_quality tests to match their Jira numbers"}
{"title": "Nuriel 9x test", "number": 142, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/142", "body": "update to 9x\nupdate\nupdate capture tests\nupdate\nupdate\nupdate\nupdate\nupdate"}
{"comment": {"body": "Please use comments in commit that we can actually understand what was changed\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/142/_/diff#comment-85048235"}}
{"comment": {"body": "ok, sry", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/142/_/diff#comment-85055675"}}
{"title": "Test/integration feature extraction 9x", "number": 143, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/143", "body": "Most of the changes are new packets (which you dont see here)\n\n\n\nsupport 9x\npreamble tests update\nfix transient exact test\nfinished feature_extraction tests\nremoved unused pickles\nsome black magic\nminor"}
{"title": "Feature/FIN-559 download integration tests datasets", "number": 144, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/144", "body": "Download datasets from levl-regression-inputs/integration_tests_datasets  to local folder libfingerprinting\\tests\\integration_tests\\datasets before integration tests and delete it once the tests are done"}
{"title": "Tests/training classification interface tests", "number": 145, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/145", "body": "Implemented training to classifcation interface tests, ITD-033 ITD-041 ITD-042 ITD-043 ITD-044 ITD-045"}
{"comment": {"body": "what\u2019s the reason for including time.h?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/145/_/diff#comment-85111482"}}
{"title": "Test/qt classify train", "number": 146, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/146", "body": "added QT-CLASSIFY-005 tests\nupdate the test\ndone with QT-005\nadded test QT-006\nadded QT-007\nadded QT-TRAIN-002\nadd QT-TRAIN-011\nfix names, add mayfail flag to fail tests"}
{"title": "Feature/BIS-921 update EIS part 4", "number": 147, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/147", "body": "Start timeout implementation\nAdd watchdog module\nFix impl\nMove ITD-057 test to another file due to regression\nAdd timeout to classification Fix MISRA issue\nregression tests's timestamp will initialize to 0 to not timeout\nReturn real error when doing feature extraction\nAdd QTD 003, QTD 004, QTD 009\nAdd tests QTD classify 003, 004, 009"}
{"title": "Tests/training valid thresholds itd 090 092", "number": 148, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/148", "body": "Training build_model will now only work if all features passed their valid count thresholds, implemented tests ITD-090-092 to test it\nRemoved CFO should_use"}
{"comment": {"body": "what\u2019s the reason for importing time.h?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/148/_/diff#comment-85116277"}}
{"comment": {"body": "My commit messages are very informative \\(usually\\):\n\n* Fixed missing includes time.h on Windows \\(they were indirectly included by Catch2 on Unix via signal.h\\)\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/148/_/diff#comment-85116500"}}
{"title": "Hotfix/preamble fix", "number": 149, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/149", "body": "check preamble lenght for a 64 bits window, update max_length when finished scanning with valid preamble\nallow extracting preamble when the access code starts in the beginning of the packet\nunitest for preamble update"}
{"title": "added cfo detection", "number": 15, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/15", "body": ""}
{"comment": {"body": "reviewing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/15/_/diff#comment-70650692"}}
{"comment": {"body": "\u2018packet\\_meta\\_information\u2019 is not the best name. It is more environment information\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/15/_/diff#comment-70651253"}}
{"comment": {"body": "I will not insist on it but maybe we should have a struct for predict\\_cfo and predict\\_cfo\\_conf\\_interval?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/15/_/diff#comment-70651268"}}
{"comment": {"body": "I don't mind changing it to any other meaningful name. It includes stuff like channel, rssi and timestamp so it\u2019s not environmental only. \n\nWDUT?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/15/_/diff#comment-70651857"}}
{"comment": {"body": "call it \u201cpacket captured information\u201d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/15/_/diff#comment-70651906"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/15/_/diff#comment-70651953"}}
{"title": "Encryption functionality", "number": 150, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/150", "body": ""}
{"title": "Will now check for no progress in training for specific features instead of the entire struct", "number": 151, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/151", "body": ""}
{"title": "Restore removal of deleteion", "number": 152, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/152", "body": ""}
{"title": "Add QTD capture 009", "number": 153, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/153", "body": ""}
{"title": "Feature/sdd unit completion", "number": 154, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/154", "body": "Fixed documentation\n\nAdd new units to match the SDD\n\nDemod fsk\nBosch packet filtering\n\n\n\nImplemented missing unit tests."}
{"title": "fix test name", "number": 155, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/155", "body": ""}
{"title": "Encryption ITD tests", "number": 156, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/156", "body": ""}
{"title": "H test", "number": 157, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/157", "body": "update non bosch\ncoverage test\nadded h test"}
{"title": "Feature/BIS-1176 itd 062 itd 074", "number": 158, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/158", "body": "Add ITD 074\nAdd ITD 062\nFix tests not actually calling assertions"}
{"title": "fix = bug in classify_validate", "number": 159, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/159", "body": ""}
{"title": "Feature/FIN-264 Preamble extraction", "number": 16, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/16", "body": "*copying reference C code\nchanging implementation of preamble length extraction to backward traversal from access code position added test for preamble\nadding some comments and order adding tests"}
{"comment": {"body": "at this stage you use m\\_cur\\_bit which is not assigned and put it in m\\_prev\\_bit. Isn\u2019t it better to explicitly assign it to 0 in lines 6-7 instead of assuming static will assign to 0 by itself?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/16/_/diff#comment-70651519"}}
{"comment": {"body": "at this point we don\u2019t care about the m\\_prev\\_bit value and we\u2019re not doing comparison until the actual search later", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/16/_/diff#comment-70651579"}}
{"comment": {"body": "We might want to have printf on error checking\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/16/_/diff#comment-70651646"}}
