{"title": "Add client flag for VSCode download during install/update", "number": 5413, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5413"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5413#pullrequestreview-1361636429", "body": ""}
{"title": "chore(deps): update dependency lambda-local to v2.0.3", "number": 5414, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5414", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| lambda-local | 2.0.2 -> 2.0.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nashiina/lambda-local\n\n### [`v2.0.3`]()\n\n[Compare Source]()\n\n-   Fix `callbackWaitsForEmptyEventLoop` on recent NodeJS versions (>=16) ([#217]())\n-   Support clientContext as an object ([#223]())\n-   Remove useless warning ([#224]())\n-   Update dependencies\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency nicklockwood/swiftformat to from: \"0.51.3\"", "number": 5415, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5415", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| nicklockwood/SwiftFormat | patch | from: \"0.51.2\" -> from: \"0.51.3\" |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnicklockwood/SwiftFormat\n\n### [`v0.51.3`]()\n\n[Compare Source]()\n\n-   Fixed `hoistTry` and `hoistAwait` rule breaking string interpolations\n-   Fixed bug where `opaqueGenericParameters` rule would remove non-redundant generic type\n-   Fixed parsing bug with trailing closures on optional methods\n-   Fixed `redundantSelf` rule parsing bug affecting string literals\n-   Updated if / switch expression features to be enabled only in Swift 5.9+\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency portfinder to v1.0.32", "number": 5416, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5416", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| portfinder | 1.0.28 -> 1.0.32 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nhttp-party/node-portfinder\n\n### [`v1.0.32`]()\n\n[Compare Source]()\n\n### [`v1.0.31`]()\n\n[Compare Source]()\n\n### [`v1.0.30`]()\n\n[Compare Source]()\n\n### [`v1.0.29`]()\n\n[Compare Source]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency sass-loader to v13.2.2", "number": 5417, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5417", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| sass-loader | 13.2.0 -> 13.2.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nwebpack-contrib/sass-loader\n\n### [`v13.2.2`]()\n\n[Compare Source]()\n\n### [`v13.2.1`]()\n\n[Compare Source]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency style-loader to v3.3.2", "number": 5418, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5418", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| style-loader | 3.3.1 -> 3.3.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nwebpack-contrib/style-loader\n\n### [`v3.3.2`]()\n\n[Compare Source]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency stylelint-config-prettier to v9.0.5", "number": 5419, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5419", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| stylelint-config-prettier | 9.0.3 -> 9.0.5 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nprettier/stylelint-config-prettier\n\n### [`v9.0.5`]()\n\n[Compare Source]()\n\nAs of Stylelint v15, you may not need this package anymore. [#140]()\n\n### [`v9.0.4`]()\n\n[Compare Source]()\n\nBugfixes:\n\n-   Supported internal API refactor. ([#134](), [#135](), [#136]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Added k8s vault secret", "number": 542, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/542", "body": "Existing password in Action secrets was for local. Added a new secret for k8s vault password."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/542#pullrequestreview-906460304", "body": ""}
{"title": "chore(deps): update dependency terser-webpack-plugin to v5.3.7", "number": 5420, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5420", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| terser-webpack-plugin | 5.3.1 -> 5.3.7 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nwebpack-contrib/terser-webpack-plugin\n\n### [`v5.3.7`]()\n\n[Compare Source]()\n\n### [`v5.3.6`]()\n\n[Compare Source]()\n\n### [`v5.3.5`]()\n\n[Compare Source]()\n\n### [`v5.3.4`]()\n\n[Compare Source]()\n\n### [`v5.3.3`]()\n\n[Compare Source]()\n\n### [`v5.3.2`]()\n\n[Compare Source]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Refactor TopicsView table", "number": 5421, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5421", "body": "Update topics view table to align with designs:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5421#pullrequestreview-1361823094", "body": ""}
{"title": "Allow updating existing topic trending calculations when run from the admin console", "number": 5422, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5422"}
{"title": "Download VSCode plugin from S3 instead of unpacking from installer", "number": 5423, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5423"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5423#pullrequestreview-1361934384", "body": ""}
{"comment": {"body": "I've confirmed that this does not result in duplicate events. You either get the Create event, or you get the Modified event, but not both", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5423#discussion_r1151169634"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5423#pullrequestreview-1361942022", "body": ""}
{"comment": {"body": "This is very cheeky, but it works! Missing plugin directories look like they're always at the same level as this file. \r\n\r\nThis will break if IntelliJ stops shipping this file but I don't see that happening any time soon", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5423#discussion_r1151174749"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5423#pullrequestreview-1361954837", "body": ""}
{"comment": {"body": "Hard to say how fragile this might be, too -- fine for now, I wonder how consistent this is across the Jetbrains IDEs, and whether it's an internal detail they may change or not", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5423#discussion_r1151183699"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5423#pullrequestreview-1361954919", "body": ""}
{"title": "Fix missing topics", "number": 5424, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5424", "body": "Fix potential issue with missing topics in explorer sidebar.\nIf sourcemarks / threads stream emits an empty list, we would try to get topics for the file. API service would return an empty list of topics which would get cached.\nThe next time the threads stream comes around and is populated, we will grab from the fetched result instead of retrying the request."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5424#pullrequestreview-1361732338", "body": ""}
{"title": "FixApprovedTopics", "number": 5425, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5425", "body": "Approved topics should be only approve topics source\nLint"}
{"title": "chore(deps): update dependency typescript to v4.9.5", "number": 5426, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5426", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| typescript (source) | 4.9.4 -> 4.9.5 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nMicrosoft/TypeScript\n\n### [`v4.9.5`](): TypeScript 4.9.5\n\n[Compare Source]()\n\nFor release notes, check out the [release announcement]().\n\nDownloads are available on:\n\n-   [npm]()\n-   [NuGet package]()\n\n#### Changes:\n\n-   [`69e88ef`]() Port ignore deprecations to 4.9 ([#52419]())\n-   [`daf4e81`]() Port timestamp fix to 4.9 ([#52426]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency webpack to v5.76.3", "number": 5427, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5427", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| webpack | 5.76.0 -> 5.76.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nwebpack/webpack\n\n### [`v5.76.3`](https://togithub.com/webpack/webpack/releases/tag/v5.76.3)\n\n[Compare Source](https://togithub.com/webpack/webpack/compare/v5.76.2...v5.76.3)\n\n#### Bugfixes\n\n-   Non-javascript files will correctly **not** be imported when using `experiments.outputModule` (ES Module Output) by [@snitin315](https://togithub.com/snitin315) in [https://github.com/webpack/webpack/pull/16809](https://togithub.com/webpack/webpack/pull/16809)\n-   Limit console output progress bar length to 40 when no columns provided by [@snitin315](https://togithub.com/snitin315) in [https://github.com/webpack/webpack/pull/16810](https://togithub.com/webpack/webpack/pull/16810)\n-   Add missing NodeJS Builtin Modules support for `inspector/promises`, `readline/promises`, and `stream/consumers` by [@ShenHongFei](https://togithub.com/ShenHongFei) in [https://github.com/webpack/webpack/pull/16841](https://togithub.com/webpack/webpack/pull/16841)\n-   webpack bin/cli now properly respects `NODE_PATH` env variable by [@snitin315](https://togithub.com/snitin315) in [https://github.com/webpack/webpack/pull/16808](https://togithub.com/webpack/webpack/pull/16808)\n-   Improve typos in `resolveResourceErrorHints` by [@snitin315](https://togithub.com/snitin315) in [https://github.com/webpack/webpack/pull/16806](https://togithub.com/webpack/webpack/pull/16806)\n-   Add missing `loaders` token support to `moduleFilenameTemplate` function call by [@pgoldberg](https://togithub.com/pgoldberg) in [https://github.com/webpack/webpack/pull/16756](https://togithub.com/webpack/webpack/pull/16756)\n-   Add gaurd condition for `enabledLibraryTypes` in internal `ContainerPlugin` by [@PengBoUESTC](https://togithub.com/PengBoUESTC) in [https://github.com/webpack/webpack/pull/16635](https://togithub.com/webpack/webpack/pull/16635)\n\n#### New Contributors\n\n-   [@ShenHongFei](https://togithub.com/ShenHongFei) made their first contribution in [https://github.com/webpack/webpack/pull/16841](https://togithub.com/webpack/webpack/pull/16841)\n-   [@pgoldberg](https://togithub.com/pgoldberg) made their first contribution in [https://github.com/webpack/webpack/pull/16756](https://togithub.com/webpack/webpack/pull/16756)\n-   [@PengBoUESTC](https://togithub.com/PengBoUESTC) made their first contribution in [https://github.com/webpack/webpack/pull/16635](https://togithub.com/webpack/webpack/pull/16635)\n\n**Full Changelog**: https://github.com/webpack/webpack/compare/v5.76.2...v5.76.3\n\n### [`v5.76.2`](https://togithub.com/webpack/webpack/releases/tag/v5.76.2)\n\n[Compare Source](https://togithub.com/webpack/webpack/compare/v5.76.1...v5.76.2)\n\n#### Bugfixes\n\n-   Fix bug where a missing semicolon in generated bundle output for `publicPathRuntime` would cause concatenated runtime errors by [@snitin315](https://togithub.com/snitin315) in [https://github.com/webpack/webpack/pull/16811](https://togithub.com/webpack/webpack/pull/16811)\n-   Remove redundant semicolons generated in bundle runtime code after `onScriptComplete` function by [@ahaoboy](https://togithub.com/ahaoboy) in [https://github.com/webpack/webpack/pull/16347](https://togithub.com/webpack/webpack/pull/16347)\n-   Fix bug where `RealContentHashPlugin` was not respecting `output.hashSalt`'s ability to cause a force recalculation of `[contenthash]` for emitted assets by [@dmichon-msft](https://togithub.com/dmichon-msft) [#16789](https://togithub.com/webpack/webpack/issues/16789)\n\n#### Performance\n\n-   Improve memory and runtime performance of sourcemaps via hoisting Regular Expression literals to stored variables by [@TheLarkInn](https://togithub.com/TheLarkInn) in [https://github.com/webpack/webpack/pull/15722](https://togithub.com/webpack/webpack/pull/15722)\n-   Correct v8 deoptimization in `ModuleGraph` due to instance property declarations occurring outside of constructor by [@snitin315](https://togithub.com/snitin315) in [https://github.com/webpack/webpack/pull/16830](https://togithub.com/webpack/webpack/pull/16830)\n\n#### Developer Experience\n\n-   Improved internal typings to match `webpack-sources` typings for `Source` instances by [@snitin315](https://togithub.com/snitin315) in [https://github.com/webpack/webpack/pull/16805](https://togithub.com/webpack/webpack/pull/16805)\n-   Update repo examples to include missing quotation by [@snitin315](https://togithub.com/snitin315) in [https://github.com/webpack/webpack/pull/16812](https://togithub.com/webpack/webpack/pull/16812)\n\n#### New Contributors\n\n-   [@ahaoboy](https://togithub.com/ahaoboy) made their first contribution in [https://github.com/webpack/webpack/pull/16347](https://togithub.com/webpack/webpack/pull/16347)\n\n**Full Changelog**: https://github.com/webpack/webpack/compare/v5.76.1...v5.76.2\n\n### [`v5.76.1`](https://togithub.com/webpack/webpack/releases/tag/v5.76.1)\n\n[Compare Source](https://togithub.com/webpack/webpack/compare/v5.76.0...v5.76.1)\n\n#### Fixed\n\n-   Added `assert/strict` built-in to `NodeTargetPlugin`\n\n#### Revert\n\n-   Improve performance of `hashRegExp` lookup by [@ryanwilsonperkin](https://togithub.com/ryanwilsonperkin) in [https://github.com/webpack/webpack/pull/16759](https://togithub.com/webpack/webpack/pull/16759)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency zustand to v3.7.2", "number": 5428, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5428", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| zustand | 3.7.0 -> 3.7.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\npmndrs/zustand\n\n### [`v3.7.2`](https://togithub.com/pmndrs/zustand/releases/tag/v3.7.2)\n\n[Compare Source](https://togithub.com/pmndrs/zustand/compare/v3.7.1...v3.7.2)\n\nThis fixes an issue in `persist` for some use cases.\n\n#### What's Changed\n\n-   fix(middleware/persist): Merge storage value with the latest store state by [@zerofirework](https://togithub.com/zerofirework) in [https://github.com/pmndrs/zustand/pull/894](https://togithub.com/pmndrs/zustand/pull/894)\n\n#### New Contributors\n\n-   [@disambiguator](https://togithub.com/disambiguator) made their first contribution in [https://github.com/pmndrs/zustand/pull/876](https://togithub.com/pmndrs/zustand/pull/876)\n-   [@zerofirework](https://togithub.com/zerofirework) made their first contribution in [https://github.com/pmndrs/zustand/pull/894](https://togithub.com/pmndrs/zustand/pull/894)\n\n**Full Changelog**: https://github.com/pmndrs/zustand/compare/v3.7.1...v3.7.2\n\n### [`v3.7.1`](https://togithub.com/pmndrs/zustand/releases/tag/v3.7.1)\n\n[Compare Source](https://togithub.com/pmndrs/zustand/compare/v3.7.0...v3.7.1)\n\nThis fixes a type issue with `zustand/context` in v3.7.0.\n\n#### What's Changed\n\n-   fix(context): revert removing overloads in `UseContextStore` by [@devanshj](https://togithub.com/devanshj) in [https://github.com/pmndrs/zustand/pull/817](https://togithub.com/pmndrs/zustand/pull/817)\n-   fix(middleware/devtools): deprecate serialize.options by [@dai-shi](https://togithub.com/dai-shi) in [https://github.com/pmndrs/zustand/pull/828](https://togithub.com/pmndrs/zustand/pull/828)\n\n**Full Changelog**: https://github.com/pmndrs/zustand/compare/v3.7.0...v3.7.1\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update plugin com.github.johnrengelman.shadow to v8.1.1", "number": 5429, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5429", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.johnrengelman.shadow | 8.1.0 -> 8.1.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "add kubernetes pip package required by ansible", "number": 543, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/543", "body": "TASK [deploy k8s secrets] ******************************************************\n[371]()\nAn exception occurred during task execution. To see the full traceback, use -vvv. The error was: ModuleNotFoundError: No module named 'kubernetes'\n[372]()\nfailed: [localhost] (item=/home/<USER>/work/unblocked/unblocked/secrets/k8s/assets/secrets.env.yaml) = {\"ansible_loop_var\": \"item\", \"changed\": false, \"error\": \"No module named 'kubernetes'\", \"item\": \"/home/<USER>/work/unblocked/unblocked/secrets/k8s/assets/secrets.env.yaml\", \"msg\": \"Failed to import the required Python library (kubernetes) on fv-az200-776's Python /opt/pipx/venvs/ansible-core/bin/python. Please read the module documentation and install it in the appropriate location. If the required library is installed, but Ansible is using the wrong Python interpreter, please consult the documentation on ansible_python_interpreter\"}\nAdded pip package for Kubernetes. This is my last attempt. If this doesn't work I'll disable secrets deployments for now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/543#pullrequestreview-906512680", "body": ""}
{"title": "chore(deps): update test packages", "number": 5430, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5430", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @testing-library/react-hooks | 8.0.0-alpha.1 -> 8.0.1 |  |  |  |  |\n| @types/jest (source) | 29.4.0 -> 29.5.0 |  |  |  |  |\n| jest (source) | 29.4.1 -> 29.5.0 |  |  |  |  |\n| jest-environment-jsdom | 29.4.1 -> 29.5.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ntesting-library/react-hooks-testing-library\n\n### [`v8.0.1`](https://togithub.com/testing-library/react-hooks-testing-library/releases/tag/v8.0.1)\n\n[Compare Source](https://togithub.com/testing-library/react-hooks-testing-library/compare/v8.0.0...v8.0.1)\n\n##### Bug Fixes\n\n-   **server:** remove act around server renderer to fix support for older versions of react ([e2461ca](https://togithub.com/testing-library/react-hooks-testing-library/commit/e2461ca4b5ab45813527f5e2478c4e8552f16c51)), closes [#607](https://togithub.com/testing-library/react-hooks-testing-library/issues/607)\n\n### [`v8.0.0`](https://togithub.com/testing-library/react-hooks-testing-library/releases/tag/v8.0.0)\n\n[Compare Source](https://togithub.com/testing-library/react-hooks-testing-library/compare/v8.0.0-alpha.1...v8.0.0)\n\n##### Bug Fixes\n\n-   **types:** move types to optional peer dependencies ([19ac8dd](https://togithub.com/testing-library/react-hooks-testing-library/commit/19ac8dde5c16f53d963277543997fa7a7ffd5fe4))\n\n##### BREAKING CHANGES\n\n-   **types:** type dependencies will not longer be automatically installed.  If `@types/react` is not already listed in your package.json, please install it with `npm install --save-dev @types/react@^17`.\n\n\n\n\nfacebook/jest\n\n### [`v29.5.0`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2950)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.4.3...v29.5.0)\n\n##### Features\n\n-   `[jest-changed-files]` Support Sapling ([#13941](https://togithub.com/facebook/jest/pull/13941))\n-   `[jest-circus, @jest/cli, jest-config]` Add feature to randomize order of tests via CLI flag or through the config file([#12922](https://togithub.com/facebook/jest/pull/12922))\n-   `[jest-cli, jest-config, @jest/core, jest-haste-map, @jest/reporters, jest-runner, jest-runtime, @jest/types]` Add `workerThreads` configuration option to allow using [worker threads](https://nodejs.org/dist/latest/docs/api/worker_threads.html) for parallelization ([#13939](https://togithub.com/facebook/jest/pull/13939))\n-   `[jest-cli]` Export `yargsOptions` ([#13970](https://togithub.com/facebook/jest/pull/13970))\n-   `[jest-config]` Add `openHandlesTimeout` option to configure possible open handles warning. ([#13875](https://togithub.com/facebook/jest/pull/13875))\n-   `[@jest/create-cache-key-function]` Allow passing `length` argument to `createCacheKey()` function and set its default value to `16` on Windows ([#13827](https://togithub.com/facebook/jest/pull/13827))\n-   `[jest-message-util]` Add support for [AggregateError](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/AggregateError) ([#13946](https://togithub.com/facebook/jest/pull/13946) & [#13947](https://togithub.com/facebook/jest/pull/13947))\n-   `[jest-message-util]` Add support for [Error causes](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause) in `test` and `it` ([#13935](https://togithub.com/facebook/jest/pull/13935) & [#13966](https://togithub.com/facebook/jest/pull/13966))\n-   `[jest-reporters]` Add `summaryThreshold` option to summary reporter to allow overriding the internal threshold that is used to print the summary of all failed tests when the number of test suites surpasses it ([#13895](https://togithub.com/facebook/jest/pull/13895))\n-   `[jest-runtime]` Expose `@sinonjs/fake-timers` async APIs functions `advanceTimersByTimeAsync(msToRun)` (`tickAsync(msToRun)`), `advanceTimersToNextTimerAsync(steps)` (`nextAsync`), `runAllTimersAsync` (`runAllAsync`), and `runOnlyPendingTimersAsync` (`runToLastAsync`) ([#13981](https://togithub.com/facebook/jest/pull/13981))\n-   `[jest-runtime, @jest/transform]` Allow V8 coverage provider to collect coverage from files which were not loaded explicitly ([#13974](https://togithub.com/facebook/jest/pull/13974))\n-   `[jest-snapshot]` Add support to `cts` and `mts` TypeScript files to inline snapshots ([#13975](https://togithub.com/facebook/jest/pull/13975))\n-   `[jest-worker]` Add `start` method to worker farms ([#13937](https://togithub.com/facebook/jest/pull/13937))\n-   `[jest-worker]` Support passing a URL as path to worker ([#13982](https://togithub.com/facebook/jest/pull/13982))\n\n##### Fixes\n\n-   `[babel-plugin-jest-hoist]` Fix unwanted hoisting of nested `jest` usages ([#13952](https://togithub.com/facebook/jest/pull/13952))\n-   `[jest-circus]` Send test case results for `todo` tests ([#13915](https://togithub.com/facebook/jest/pull/13915))\n-   `[jest-circus]` Update message printed on test timeout ([#13830](https://togithub.com/facebook/jest/pull/13830))\n-   `[jest-circus]` Avoid creating the word \"testfalse\" when `takesDoneCallback` is `false` in the message printed on test timeout AND updated timeouts test ([#13954](https://togithub.com/facebook/jest/pull/13954))\n-   `[jest-environment-jsdom]` Stop setting `document` to `null` on teardown ([#13972](https://togithub.com/facebook/jest/pull/13972))\n-   `[@jest/expect-utils]` Update `toStrictEqual()` to be able to check `jest.fn().mock.calls` ([#13960](https://togithub.com/facebook/jest/pull/13960))\n-   `[@jest/test-result]` Allow `TestResultsProcessor` type to return a Promise ([#13950](https://togithub.com/facebook/jest/pull/13950))\n\n##### Chore & Maintenance\n\n-   `[jest-snapshot]` Remove dependency on `jest-haste-map` ([#13977](https://togithub.com/facebook/jest/pull/13977))\n\n### [`v29.4.3`](https://togithub.com/facebook/jest/releases/tag/v29.4.3)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.4.2...v29.4.3)\n\n#### Features\n\n-   `[expect]` Update `toThrow()` to be able to use error `cause`s ([#13606](https://togithub.com/facebook/jest/pull/13606))\n-   `[jest-core]` allow to use `workerIdleMemoryLimit` with only 1 worker or `runInBand` option ([#13846](https://togithub.com/facebook/jest/pull/13846))\n-   `[jest-message-util]` Add support for [error `cause`s](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause) ([#13868](https://togithub.com/facebook/jest/pull/13868) & [#13912](https://togithub.com/facebook/jest/pull/13912))\n-   `[jest-runtime]` Revert `import assertions` for JSON modules as it's been relegated to Stage 2 ([#13911](https://togithub.com/facebook/jest/pull/13911))\n\n#### Fixes\n\n-   `[@jest/expect-utils]` `subsetEquality` should consider also an object's inherited string keys ([#13824](https://togithub.com/facebook/jest/pull/13824))\n-   `[jest-mock]` Clear mock state when `jest.restoreAllMocks()` is called ([#13867](https://togithub.com/facebook/jest/pull/13867))\n-   `[jest-mock]` Prevent `mockImplementationOnce` and `mockReturnValueOnce` bleeding into `withImplementation` ([#13888](https://togithub.com/facebook/jest/pull/13888))\n-   `[jest-mock]` Do not restore mocks when `jest.resetAllMocks()` is called ([#13866](https://togithub.com/facebook/jest/pull/13866))\n\n#### New Contributors\n\n-   [@brodo](https://togithub.com/brodo) made their first contribution in [https://github.com/facebook/jest/pull/13868](https://togithub.com/facebook/jest/pull/13868)\n-   [@DannyNemer](https://togithub.com/DannyNemer) made their first contribution in [https://github.com/facebook/jest/pull/13878](https://togithub.com/facebook/jest/pull/13878)\n-   [@ghusse](https://togithub.com/ghusse) made their first contribution in [https://github.com/facebook/jest/pull/13846](https://togithub.com/facebook/jest/pull/13846)\n-   [@broofa](https://togithub.com/broofa) made their first contribution in [https://github.com/facebook/jest/pull/13911](https://togithub.com/facebook/jest/pull/13911)\n\n**Full Changelog**: https://github.com/facebook/jest/compare/v29.4.2...v29.4.3\n\n### [`v29.4.2`](https://togithub.com/facebook/jest/blob/HEAD/CHANGELOG.md#2942)\n\n[Compare Source](https://togithub.com/facebook/jest/compare/v29.4.1...v29.4.2)\n\n##### Features\n\n-   `[@jest/core]` Instrument significant lifecycle events with [`performance.mark()`](https://nodejs.org/docs/latest-v16.x/api/perf_hooks.html#performancemarkname-options) ([#13859](https://togithub.com/facebook/jest/pull/13859))\n\n##### Fixes\n\n-   `[expect, @jest/expect]` Provide type of `actual` as a generic argument to `Matchers` to allow better-typed extensions ([#13848](https://togithub.com/facebook/jest/pull/13848))\n-   `[jest-circus]` Added explicit mention of test failing because `done()` is not being called in error message ([#13847](https://togithub.com/facebook/jest/pull/13847))\n-   `[jest-runtime]` Handle CJS re-exports of node core modules from ESM ([#13856](https://togithub.com/facebook/jest/pull/13856))\n-   `[jest-transform]` Downgrade `write-file-atomic` to v4 ([#13853](https://togithub.com/facebook/jest/pull/13853))\n-   `[jest-worker]` Ignore IPC messages not intended for Jest ([#13543](https://togithub.com/facebook/jest/pull/13543))\n\n##### Chore & Maintenance\n\n-   `[*]` make sure to exclude `.eslintcache` from published module ([#13832](https://togithub.com/facebook/jest/pull/13832))\n-   `[docs]` Cleanup incorrect links in CHANGELOG.md ([#13857](https://togithub.com/facebook/jest/pull/13857))\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Immortal: This PR will be recreated if closed unmerged. Get config help if that's undesired.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency constructs to v10.1.293", "number": 5431, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5431", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| constructs | 10.1.272 -> 10.1.293 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/constructs\n\n### [`v10.1.293`]()\n\n[Compare Source]()\n\n##### [10.1.293]() (2023-03-28)\n\n### [`v10.1.292`]()\n\n[Compare Source]()\n\n##### [10.1.292]() (2023-03-27)\n\n### [`v10.1.291`]()\n\n[Compare Source]()\n\n##### [10.1.291]() (2023-03-26)\n\n### [`v10.1.290`]()\n\n[Compare Source]()\n\n##### [10.1.290]() (2023-03-25)\n\n### [`v10.1.289`]()\n\n[Compare Source]()\n\n##### [10.1.289]() (2023-03-24)\n\n### [`v10.1.288`]()\n\n[Compare Source]()\n\n##### [10.1.288]() (2023-03-23)\n\n### [`v10.1.287`]()\n\n[Compare Source]()\n\n##### [10.1.287]() (2023-03-22)\n\n##### Bug Fixes\n\n-   `engine` specifies a higher version than it needs ([#1615]()) ([aa8f8e5]())\n\n### [`v10.1.286`]()\n\n[Compare Source]()\n\n##### [10.1.286]() (2023-03-22)\n\n### [`v10.1.285`]()\n\n[Compare Source]()\n\n##### [10.1.285]() (2023-03-21)\n\n### [`v10.1.284`]()\n\n[Compare Source]()\n\n##### [10.1.284]() (2023-03-21)\n\n### [`v10.1.283`]()\n\n[Compare Source]()\n\n##### [10.1.283]() (2023-03-20)\n\n### [`v10.1.282`]()\n\n[Compare Source]()\n\n##### [10.1.282]() (2023-03-19)\n\n### [`v10.1.281`]()\n\n[Compare Source]()\n\n##### [10.1.281]() (2023-03-18)\n\n### [`v10.1.280`]()\n\n[Compare Source]()\n\n##### [10.1.280]() (2023-03-17)\n\n### [`v10.1.279`]()\n\n[Compare Source]()\n\n##### [10.1.279]() (2023-03-16)\n\n### [`v10.1.278`]()\n\n[Compare Source]()\n\n##### [10.1.278]() (2023-03-15)\n\n### [`v10.1.277`]()\n\n[Compare Source]()\n\n##### [10.1.277]() (2023-03-14)\n\n### [`v10.1.276`]()\n\n[Compare Source]()\n\n##### [10.1.276]() (2023-03-13)\n\n### [`v10.1.275`]()\n\n[Compare Source]()\n\n##### [10.1.275]() (2023-03-12)\n\n### [`v10.1.274`]()\n\n[Compare Source]()\n\n##### [10.1.274]() (2023-03-11)\n\n### [`v10.1.273`]()\n\n[Compare Source]()\n\n##### [10.1.273]() (2023-03-10)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Bump people page limit to 150 because we've exceeded 100 users", "number": 5432, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5432", "body": "Not the right approach, but this quick hack fixes the problem for now.\nShould hook up pagination to data tables instead."}
{"title": "GitHub Team API now uses GitHub App Auth", "number": 5433, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5433", "body": "This in turn allows us to rip out all the GitHub-specific code paths."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5433#pullrequestreview-1361850091", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5433#pullrequestreview-1361854315", "body": ""}
{"title": "Set default env value", "number": 5434, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5434", "body": "Default prod value"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5434#pullrequestreview-1361868341", "body": ""}
{"title": "Update email templates", "number": 5435, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5435", "body": "In dark mode, using pure white can cause issues with the color rendering:\n\n\nalso update other templates to remove thread title and actions image"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5435#pullrequestreview-1361928941", "body": "Thank you!!!!"}
{"title": "Don't allow deprecated APIs in TS code", "number": 5436, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436", "body": "Don't allow any deprecated APIs in typescript code.\n\nAdd a new OpenAPI template file for the typescript-fetch API, this allows us to generate @deprecated markers for all APIs correctly\nAdd eslint rules to disallow using deprecated APIs\nFix all the places we were using deprecated APIs\n\nI will look into submitting the typescript-fetch template change upstream to the openAPI generator project."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1361957887", "body": ""}
{"comment": {"body": "Updated to use the somewhat newish VSCode tabGroups API", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#discussion_r1151185960"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1361958389", "body": ""}
{"comment": {"body": "@kaych we were using a deprecated API for floating UIs a lot -- I think I've fixed this right but I'm not 100% sure?  It has a pretty large blast radius", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#discussion_r1151186325"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1361958791", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1361959317", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1361959939", "body": ""}
{"comment": {"body": "Note: we're using a lot of old stuff here in the web extension, we can fix this stuff if we ever resurrect this project", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#discussion_r1151187417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1362001337", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1362024450", "body": ""}
{"comment": {"body": "I don't think this actually works. i.e. the mention dropdown does not appear below the input text ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#discussion_r1151232175"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1362033486", "body": ""}
{"comment": {"body": "Figured it out. I think all the places you use `refs.reference` and `refs.floating` it should be `refs.setReference` and `refs.setFloating` (see: https://github.com/floating-ui/floating-ui/issues/2202)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#discussion_r1151238534"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1362034500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1362095095", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1362097059", "body": ""}
{"comment": {"body": "Right -- annoying that the types match for those two values.  I think I've fixed this, but I'm not 100% sure -- there are still a couple places using refs.reference and refs.floating because you were reading from them, I think those are intentional though...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#discussion_r1151281545"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1363560243", "body": ""}
{"comment": {"body": "The changes here are from upstream -- this brings `runtime.mustache` in sync with the latest version of openapi-generator", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#discussion_r1152229935"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1363819895", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5436#pullrequestreview-1365530916", "body": ""}
{"title": "fix(deps): update dependency dev.failsafe:failsafe to v3.3.1", "number": 5437, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5437", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| dev.failsafe:failsafe (source) | 3.3.0 -> 3.3.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nfailsafe-lib/failsafe\n\n### [`v3.3.1`]()\n\n##### Improevments\n\n-   Issue [#358]() - Added full java module descriptors to Failsafe jars.\n-   Issue [#361]() - Released execution references inside Failsafe provided CompletableFutures.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency edu.stanford.nlp:stanford-corenlp to v4.5.3", "number": 5438, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5438", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| edu.stanford.nlp:stanford-corenlp (source) | 4.5.2 -> 4.5.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Remove old onboarding code", "number": 5439, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5439", "body": "Remove old tutorial wizard code."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5439#pullrequestreview-1361969297", "body": ""}
{"title": "Screw k8s secrets", "number": 544, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/544"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/544#pullrequestreview-906526769", "body": "For now... "}
{"title": "Setup IntelliJ Notification Service", "number": 5440, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440", "body": "Setup basic notification service for customer logging purposes.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-1366112299", "body": ""}
{"comment": {"body": "Notifications that I created for updates are using the `STICKY_BALLOON` notification type, which I think is appropriate for updates but not for arbitrary notifications. May I suggest that we pass through the desired type of notification through GRPC, and then add new notification groups to the plugin.xml that we can use to map to the requested types?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#discussion_r1153915470"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-1366113083", "body": ""}
{"comment": {"body": "Remove this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#discussion_r1153916056"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-1366113115", "body": ""}
{"comment": {"body": "Sorry I realize that `Notification Type` is a bit overloaded here. More concretely, take a look at plugin.xml for the \"Unblocked\" notification group. We need to replicate that for other display types", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#discussion_r1153916088"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-1366114119", "body": ""}
{"comment": {"body": "I wonder if we shouldn't be sending notifications directly to the IDEAgent like this, but through an abstraction (like the IDEWorkspace) -- that way shared IDE code can display notifications regardless of which IDE we are in, and they will display as notifications correctly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#discussion_r1153916885"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Hmm based on the NotificationGroup, it looks like we've setup *all* our notifications to show up as `STICKY_BALLOON`, which I think is okay / expected.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#discussion_r1154937408"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think it might make more sense to just store the provider in the `setProvider` call below, and then `notify()` here can just call `this.currentProvider.notify(stuff)`?\r\n\r\nThat way any other calls that flow in this direction (agent -> IDE) can reuse that value.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#discussion_r1156206091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Oh and this implies that `IDEWorkspaceProvider.notify` would be a plain function:\r\n\r\n`IDEWorkspaceProvider.notify(arg1, arg2)` ...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#discussion_r1156207047"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-**********", "body": ""}
{"comment": {"body": "FWIW I'm not sure this is the kind of error we want to randomly be popping up in a notification.  The end result will look kind of sloppy and isn't actionable...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#discussion_r1156208543"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-**********", "body": "A couple last bits of feedback but looks good"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5440#pullrequestreview-**********", "body": ""}
{"title": "More JetBrains lint", "number": 5441, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5441"}
{"title": "Styling nits", "number": 5442, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5442", "body": " and others"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5442#pullrequestreview-1362025193", "body": ""}
{"title": "Fix missing default for env service", "number": 5443, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5443", "body": "Add default to prod for env service."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5443#pullrequestreview-1362017120", "body": ""}
{"title": "Test why JetBrains project isn't linting properly", "number": 5444, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5444"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5444#pullrequestreview-1362049571", "body": ""}
{"comment": {"body": "This would fail the service build, if it ran subsequently", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5444#discussion_r1151249703"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5444#pullrequestreview-1362050313", "body": ""}
{"comment": {"body": "Yeah, just using this to test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5444#discussion_r1151250065"}}
{"title": "Add user-defined description to topic model", "number": 5445, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5445", "body": "Adds a new property called userDefinedDescription which will be used to store any description that is created or updated by the user. Topic.description will continue to contain the generated description, and the client will show this in the UI if userDefinedDescription is null.\nThe reason for having two fields is to support the designs where generated descriptions are highlighted with an ML tag. If a user edits a topic description, we'll save that to userDefinedDescription (leaving description untouched) and the expectation is that the UI will not show the ML tag."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5445#pullrequestreview-1365689856", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5445#pullrequestreview-1365695736", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5445#pullrequestreview-1365754926", "body": ""}
{"title": "Pull Request insights topic mapping", "number": 5446, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5446", "body": "I was not aware that an insight can point to either a thread or a pullrequest (two different tables), and we have to handle it properly."}
{"title": "Remove the Jetbrains plugin version upper bound", "number": 5447, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5447", "body": "Warning\nPlugin 'Unblocked' (version '1.0.832') is not compatible with the current version of the IDE, because it requires build 223.* or older but the current build is IU-231.8109.175"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5447#pullrequestreview-1362080642", "body": ""}
{"title": "Redirect to topics view after delete", "number": 5448, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5448", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5448#pullrequestreview-1365720455", "body": ""}
{"title": "Break dependency on LintTask to FromatTask", "number": 5449, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5449", "body": "This breaks CI and this sort of implicit dependency is a bad idea.\nOn CI, we do not want linting to format the files as those changes will not be submitted to main."}
{"comment": {"body": "Makes sense. Thanks!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5449#issuecomment-1487826115"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5449#pullrequestreview-1362094404", "body": ""}
{"title": "Hit API for downloading and updating SourceMarks", "number": 545, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/545", "body": "Yeah super hacky but would like to get this in so that Richie and start running the demo. \nNext PR will fix auth try to consolidate SourceMarkApiClient with UnblockedApiClient"}
{"comment": {"body": "^ haha", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/545#issuecomment-1064577985"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/545#pullrequestreview-906551899", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/545#pullrequestreview-906552118", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/545#pullrequestreview-906561564", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/545#pullrequestreview-906563702", "body": "great work"}
{"title": "Cleanup github actions for jetbrains", "number": 5450, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5450", "body": "The defualt build task that gradle provides calls the check task.\nOur kotlinter plugin adds a lint dependency on the check task so, in effect, we do not need to call lint task explicitly."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5450#pullrequestreview-1362098521", "body": ""}
{"title": "Refactor GitHubInstallationHandler to remove more code", "number": 5451, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5451"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5451#pullrequestreview-1362099505", "body": ""}
{"title": "Remove all JetBrains ext version constraints", "number": 5452, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5452", "body": "With both since and until constraints\n\nWarning\nPlugin 'Unblocked' (version '1.0.832') is not compatible with the current version of the IDE, because it requires build 223.* or older but the current build is IU-231.8109.175\n\nWith just the since constraint\nIDE seems to be treating the since constraint as an until constraint. Makes no sense.\n\nWarning\nPlugin 'Unblocked' (version '1.0.833') is not compatible with the current version of the IDE, because it requires build 221.* or older but the current build is IU-231.8109.175\n\nThis change\nRemove all constrains. Let's see what happens.\n"}
{"comment": {"body": "We should be running the plugin verifier task in CI to determine the correct bounds.\r\nhttps://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html#tasks-runpluginverifier", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5452#issuecomment-1487971770"}}
{"title": "Debugging to figure out why this is happening in DEV", "number": 5453, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5453", "body": "json\n{\n  \"level\": \"ERROR\",\n  \"message\": \"Failed to refresh team members\",\n  \"platform\": {\n    \"version\": \"cb752276585f4c999de54a971a324f3f7964cd39\",\n    \"buildNumber\": \"19947\"\n  },\n  \"environment\": \"dev\",\n  \"@timestamp\": \"2023-03-29T05:22:47.991+0000\",\n  \"service\": \"scmservice\",\n  \"thread_name\": \"DefaultDispatcher-worker-129\",\n  \"teamId\": \"2ba92041-cb22-4070-aa5c-d82d4098e7c4\",\n  \"logger_name\": \"com.nextchaptersoftware.scmservice.jobs.ScmTeamMaintenanceJob\",\n  \"stack_trace\": \"j.l.UnsupportedOperationException: Empty collection can't be reduced.\n        at c.n.m.MembershipMaintenance$createTeamMembers$2.invokeSuspend(MembershipMaintenance.kt:165)\n        at c.n.m.MembershipMaintenance$createTeamMembers$2.invoke(MembershipMaintenance.kt)\n        at c.n.m.MembershipMaintenance$createTeamMembers$2.invoke(MembershipMaintenance.kt)\n        at c.n.d.c.Database$suspendedTransaction$2$1.invokeSuspend(Database.kt:170)\n        at c.n.d.c.Database$suspendedTransaction$2$1.invoke(Database.kt)\n        at c.n.d.c.Database$suspendedTransaction$2$1.invoke(Database.kt)\n        at o.j.e.s.t.e.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:129)\n        at k.c.j.i.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)\n        at k.c.DispatchedTask.run(DispatchedTask.kt:106)\n        at k.c.i.LimitedDispatcher.run(LimitedDispatcher.kt:42)\n        at k.c.s.TaskImpl.run(Tasks.kt:95)\n        at k.c.s.CoroutineScheduler.runSafely(CoroutineScheduler.kt:570)\n        at k.c.s.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:750)\n        at k.c.s.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:677)\n        at k.c.s.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)\\n\"\n}"}
{"title": "Fix lib-maintenence test build", "number": 5454, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5454"}
{"title": "Fix jetbrains version", "number": 5455, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5455", "body": "Turns out that the default version is set based on the intellij.version, which is set to \"2022.1.4\".\nxml\nidea-version since-build=\"221.6008\" until-build=\"221.*\" /\nSo we need to set an explicit version that is much more lenient.\nFollow up\nWe should be running the plugin verifier task in CI to determine the correct bounds.\n"}
{"title": "Allow service build to run manually", "number": 5456, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5456", "body": "Motivation\n\nResult\nYou'll now see this banner in Actions tab.\n"}
{"title": "Add iOS client type", "number": 5457, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363590569", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363594605", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363669332", "body": ""}
{"comment": {"body": "your IDE line length is not configured properly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152300133"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363674403", "body": ""}
{"comment": {"body": "<img width=\"992\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/228626319-7029847a-7e57-4ea8-acb8-fd67f2b00df1.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152303450"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363862518", "body": ""}
{"comment": {"body": "default is 120 I think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152424180"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363916468", "body": ""}
{"comment": {"body": "Yes, the IDE default is 120.\n\n\n\nBut we changed the editor config default (the one used by detekt/lint) to 140.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152453868"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363932933", "body": ""}
{"comment": {"body": "\ud83d\udc4d Updated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152467816"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363938300", "body": ""}
{"comment": {"body": "I don't see this document in any README -- not sure if I missed it, or if it's not there.  Can we add it, or commit something to the repo so that this is the default setting?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152471538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363984967", "body": ""}
{"comment": {"body": "```\ndetekt.yml:    maxLineLength: 140\n```\n\nIt is checked in, and the default.\n\n\n\nThe problem is that we do not persist IntelliJ settings (the information declared in the **.idea** directory). We should do this.\n\n\n\nWe should also persist in the **.editorconfig**.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152500093"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363986162", "body": ""}
{"comment": {"body": "In general (not specific to this property) our project IDE settings should be shared. Everyone has a different setup, which often diverges like this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152500793"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363988515", "body": ""}
{"comment": {"body": "See [https://editorconfig.org](https://editorconfig.org)\n\nhttps://editorconfig.org", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152501959"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#pullrequestreview-1363994719", "body": ""}
{"comment": {"body": "[https://github.com/NextChapterSoftware/unblocked/pull/5464](https://github.com/NextChapterSoftware/unblocked/pull/5464)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5457#discussion_r1152504847"}}
{"title": "Add ability to view all pull requests for team/repo and reindex pull requests", "number": 5458, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5458", "body": "requests"}
{"title": "Use stream for pullRequestFile pagination", "number": 5459, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5459", "body": "No change in behaviour, just uses streams instead of pages.\nStopping criteria is now a limit of the cumulative items, rather than page-specific."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5459#pullrequestreview-**********", "body": ""}
{"title": "Fix SourceMarkProvider auth", "number": 546, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/546"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/546#pullrequestreview-906595801", "body": "thanks!"}
{"title": "Add installer background image", "number": 5460, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5460"}
{"comment": {"body": "Didn't work out because the installer's image scaling is completely broken. Oh well", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5460#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5460#pullrequestreview-**********", "body": ""}
{"title": "Add more links for pull requets", "number": 5461, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5461"}
{"title": "Add links to repos page", "number": 5462, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5462"}
{"title": "Jetbrains agent logging and error handling", "number": 5463, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5463", "body": "Set up logging for the agent (log.*) -- the implementation is shared with VSCode as we want the same behaviour\nSet up Sentry for the agent\nSet up node global error handlers.  This allows the agent to continue running when exceptions aren't caught, etc.  This is the same implementation that VSCode's extension process has."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5463#pullrequestreview-1365706382", "body": ""}
{"title": "Set max_line_length attribute in .editorconfig", "number": 5464, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5464", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5464#pullrequestreview-1364030424", "body": "Wonder if this will conflict with prettier settings..."}
{"title": "Replace persistence of GitHub page numbers in PR ingestion", "number": 5465, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5465", "body": "Plan\n\nReplace GitHub-specific pagination with something SCM-generic (this change)\nDefine ScmRepoApi interface (#5469)\nReplace hardcoded usage of GitHubAppApi.V3Org in lib-pringestion with ScmRepoApi\nImplement ScmRepoApi interface for Bitbucket and GitLab\n\nDependencies\n\n[x] #5476\n[x] #5477\n[x] GitHub APIs used for PR ingest use batched-stream pagination (acdda92bfcd19cf958f3c03913fc06d1fb36ba28)\n[x] PR ingest uses batched-stream-based GitHub APIs (e9e09f0ac138dc162e6aa47df362343ee0fe35fe)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5465#pullrequestreview-1364031387", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5465#pullrequestreview-1364068185", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5465#pullrequestreview-1366042463", "body": "Great stuff right here"}
{"title": "Fix video file SCM HTML link", "number": 5466, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5466", "body": "Will point to HEAD of the repo on their configured default branch."}
{"comment": {"body": "Thanks \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5466#issuecomment-1489428987"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5466#pullrequestreview-1364071039", "body": ""}
{"title": "Was supposed to be 140", "number": 5467, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5467"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5467#pullrequestreview-1364124453", "body": ""}
{"title": "Revert \"Set max_line_length attribute in .editorconfig\"", "number": 5468, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5468", "body": "Reverts NextChapterSoftware/unblocked#5464\nThis is fucking everything up."}
{"title": "Add SCM repo API interface methods", "number": 5469, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5469"}
{"title": "Use admin auth", "number": 547, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/547", "body": "I'm not sure if the refresh token logic here works, but using the token from the API works."}
{"comment": {"body": "> I'm not sure if the refresh token logic here works, but using the token from the API works.\r\n\r\nYou can test the refresh token flow, by reducing the access token TTL to something really small (like 20 seconds) as a test.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/547#issuecomment-1064634200"}}
{"comment": {"body": "Thanks @richiebres yup, I see in the server logs that the refresh token logic is working here after I drop the TTL to 10 seconds", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/547#issuecomment-1064638432"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/547#pullrequestreview-906641823", "body": "looks good to me\n@pwerry you wanna  ?"}
{"title": "Show raw paginated pull requests response from GitHub", "number": 5470, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5470"}
{"title": "Order pull requests by created asc", "number": 5471, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5471"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5471#pullrequestreview-1364188061", "body": "Might interfere with any ingestion that is in progress as this is deployed. Low probability though, and we can always just re ingest"}
{"title": "[RFC] Persist IDE project settings for consistency", "number": 5472, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5472", "body": "Goal is to keep project settings consistent:\n - linter settings\n - code formatting settings\n - JDK language level\n - modules\nPersonal preferences are not persisted.\nUses this to seed our ignore settings:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5472#pullrequestreview-1365484207", "body": ""}
{"comment": {"body": "Hard to know for sure, but with this many ignored files, is it possible to opt-in to the ones we actually want to store in git?  Something like:\r\n\r\n```\r\n.idea/**\r\n!.idea/codeStyles/Project.xml\r\n!.idea/detekt.xml\r\n```\r\n?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5472#discussion_r1153496462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5472#pullrequestreview-1365509281", "body": ""}
{"comment": {"body": "Workspace xml is always personal preferences. So it should always be ignored. Stuff like arrangement of your tool windows.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5472#discussion_r1153513010"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5472#pullrequestreview-1365521551", "body": "For the love of god please merge this"}
{"title": "Populate SCM API repo factory and implementation", "number": 5473, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5473", "body": "Blocked on #5465 from completing the GitHubRepoApi implementation."}
{"title": "Introduce batched streaming", "number": 5474, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5474", "body": "Batched streaming returns a list of items and the next page URL, to be used\nby calling clients in cases where it is cost advantageous to cache the next\npage API in the event that the long-running stream is interrupted.\nTo use safely: the caller must process all of the items in the batch\nbefore persisting the next page, otherwise items will be lost if there is an\ninterruption mid-batch."}
{"title": "Fix link events in summary views", "number": 5475, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5475", "body": ""}
{"comment": {"body": "Wonder if this should be a default part of the dropdown items? -- can't imagine we'd ever want clicking to propagate?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5475#issuecomment-1490736791"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5475#pullrequestreview-1365689165", "body": ""}
{"title": "DB model change to declare nullable next batch fields (next API page URLs)", "number": 5476, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5476"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5476#pullrequestreview-1365864491", "body": ""}
{"title": "Admin: PR Ingestion page shows next batch of items", "number": 5477, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5477", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5477#pullrequestreview-1365863784", "body": ""}
{"title": "Install the plugins after the hub restarts", "number": 5478, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5478", "body": "Summary\nPer the PR title, the biggest change is the order of installation. The complexity is in passing information back and forth from the Hub to the installer app. Since the Hub is sandboxed, it can't do anything cool like launch the installer with args or read the result. So instead we just echo the result on disk through two \"version\" files:\n- .version\n- .version.installed\nTo simplify, I made the installer as dumb as possible. If it finds installer files, it will install them, delete them, and mark the latest version.\nAll the business logic for deciding whether to perform a plugin upgrade is in the Hub, and it works as follows:\n\nCheck to see if current version of the hub is equal to the lates version from the versions API. If so then\nLook for the .version.installed file. If it's >= the current hub version, so need to do anything. If not then\nLook for the existence of a new plugin file already downloaded. If one exists then inspect the .version file to see if it's a new plugin version. If so, then launch installer, if not then\nDownload plugin and write new version to .version file\nLaunch installer\n\nTested under all different conditions - seems to be working as expected.\nFirst time upgrade/install\nThere is a bit of added complexity here. \nFirstly, on first time install we need to add an additional \"flag\" for \"processing complete\". That will be fairly trivial to add. In the mean time, plugin installation is protected through client feature flags.\nSecond, the initial upgrade will result in a double download and install. There are ways to avoid this, but it seems like more work that it's worth. Current customers won't see this at all because vscode plugin download is disabled for them. When we turn it on, they will already have the updated install code and it will just work.\nFollow up\nThere's one more task to do after this, which is to remove the vscode plugin from the installer. We can't do this until the new onboarding flow is released, but it will be totally painless for customers when we do."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5478#pullrequestreview-1366225165", "body": ""}
{"comment": {"body": "Not sure if I misunderstood a prior conversation. I thought the approach was to have the new hub version handle the installation of jetbrains / vscode.\r\n\r\naka if we are on version 1 and version 2 comes out.\r\nVersion 2 hub would would download and install first. Version 2 hub would then download & install version 2 jetbrains / vscode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5478#discussion_r1153993355"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5478#pullrequestreview-1367336515", "body": ""}
{"title": "Add Topic.displayName", "number": 5479, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5479", "body": "Adds a displayName property to topic. The idea here is that Topic.name is assigned at creation and immutable, so that when insights are mapped to topics, that name (which should be generated) can be used as the unique key for looking up the topic. \nTopic.displayName will be for users to allow them to rename a topic without touching Topic.name."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5479#pullrequestreview-1366102427", "body": ""}
{"comment": {"body": "The description can change based off the how we design the prompts", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5479#discussion_r1153908492"}}
{"comment": {"body": "Let's hold off on removing this field as it is used for backend equality comparison. Referring to keywords. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5479#discussion_r1153909575"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5479#pullrequestreview-1367411366", "body": ""}
{"comment": {"body": "Ah this function is for when customer's update topic details, and I figure we don't want to allow them to edit the autogenerated description. The `upsertTopics` function is the one that's called by the `TopicIngestionService` and that will change the autogenerated description as we re-ingest topics.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5479#discussion_r1154779629"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5479#pullrequestreview-1367412886", "body": ""}
{"comment": {"body": "Ditto to the above. We don't want to allow customers to edit keywords (for now) so I'm just removing it so that the only place we do update keywords is in `upsertTopics`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5479#discussion_r1154780776"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5479#pullrequestreview-1369568491", "body": ""}
{"title": "Crash fix: populate point cache with tree-same commit points", "number": 548, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/548"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/548#pullrequestreview-906623115", "body": ""}
{"title": "[UNB-1067] Setup Create Insight Flow in IntelliJ", "number": 5480, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480", "body": "Setup create insight flow in IntelliJ\n\n"}
{"comment": {"body": "The kotlin LGTM, but as the bulk of this PR in in TS-client land I will have to leave it to Matt to approve", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#issuecomment-1494683950"}}
{"comment": {"body": "https://linear.app/unblocked/issue/UNB-1067/create-note-workflow", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#issuecomment-1498127350"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1366114604", "body": ""}
{"comment": {"body": "This feels like an anti-pattern. Construction of objects should not result in side effects. Maybe we should use factories for this kind of thing?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1153917275"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382104401", "body": ""}
{"comment": {"body": "FYI https://github.com/NextChapterSoftware/unblocked/pull/5616/files#diff-5df49317d615e90d8ecbcc3cec254953bbb226d4fcf053eb3801d6a7acb3f1edR310-R311", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164644566"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382105797", "body": ""}
{"comment": {"body": "specifically to address this^", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164645351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382156557", "body": ""}
{"comment": {"body": "Updated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164678538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382252664", "body": ""}
{"comment": {"body": "I don't quite understand why we need this protection here?  Are we accidentally re-triggering this command?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164748878"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382255633", "body": ""}
{"comment": {"body": "This feels like a fair bit of code duplicated from VSCode -- not sure if we can do anything about that?  Most of this code should be able to be deduped eventually?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164750995"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382256078", "body": ""}
{"comment": {"body": "Can we replace this default with the explicit commands that aren't implemented?  A default statement here means it's very easy to miss unimplemented items if we change the actions in the list", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164751328"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382262596", "body": ""}
{"comment": {"body": "Two things here:\r\n- This should maybe be moved into `EditorService.recalculateEditors`.  EditorSourceMarkManager is meant to track SMs on an editor, `EditorService` generally tracks open/viewed editors and attaches monitoring/listening as required.\r\n- AFAICT this listener is not removed.  I don't know for sure but this might keep the listener and possibly the editor alive indefinitely.  You can either manually deregister in `EditorService.recalcualteEditors`, as we already have lots of code there for tracking new/removed editors, or possibly you can use the second argument here to automatically deregister the listener when the editor is disposed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164755512"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382265789", "body": ""}
{"comment": {"body": "Curious, why are we doing a bunch of markup/hilighting work here?   Shouldn't we just be updating the gutter icon here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164758561"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382284681", "body": ""}
{"comment": {"body": "Should we be using this in vscode too?  I'm guessing this is largely duplicated code?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164771190"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382285650", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382289246", "body": ""}
{"comment": {"body": "Yeah. If user clicks the button twice, I don't want to retrigger this logic.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164774563"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382292127", "body": ""}
{"comment": {"body": "You're correct. I would like to update VSCode but two key differences which make things tricky.\r\n\r\n1. `webviewEventHandler` is deeply integrated in VSCode throughout the insight command.\r\n2. Need to refactor out VSCode document interactions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164776405"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382292254", "body": ""}
{"comment": {"body": "2 is possible, 1 is trickier...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164776507"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382292989", "body": ""}
{"comment": {"body": "This is used in VSCode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1164776968"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382712230", "body": ""}
{"comment": {"body": "Updated.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1165054389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#pullrequestreview-1382716014", "body": ""}
{"comment": {"body": "The gutter icon is held by the highlighter? Maybe I'm doing too much work... Let's sync up on this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5480#discussion_r1165056215"}}
{"title": "Remove PR ingestion page cursors and add migration for schema", "number": 5482, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5482", "body": "Ran schema migration successfully locally"}
{"title": "Update repo ingest progress on repos page", "number": 5483, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5483", "body": ""}
{"title": "Remove completed migration", "number": 5484, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5484"}
{"title": "PR ingest uses abstract ScmRepoApi instead of GitHub classes directly", "number": 5485, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5485"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5485#pullrequestreview-1366258243", "body": ""}
{"title": "Use repoExternalId instead of owner/repoName for GitHub", "number": 5486, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5486", "body": "More stable to use invariant repo IDs that do not change when the repo is renamed."}
{"title": "Add scm serialzation", "number": 5487, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5487"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5487#pullrequestreview-1367327652", "body": ""}
{"comment": {"body": "Cannot serialize/deserialize base class type without it being a sealed class rather than interface. \r\nWill have to investigate how to get this to work with sealed interfaces...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5487#discussion_r1154727565"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5487#pullrequestreview-1367342263", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5487#pullrequestreview-1367342681", "body": ""}
{"title": "Hook up Bitbucket to PR ingestion", "number": 5488, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5488", "body": "The pull request API is enough to at least pull in the top-level PR object.\nComments and review will come later."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5488#pullrequestreview-1367564221", "body": ""}
{"title": "Fix jetbrains build configuration issues", "number": 5489, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5489"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5489#pullrequestreview-1367385226", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5489#pullrequestreview-1367469522", "body": ""}
{"title": "Add batch put sourcepoints", "number": 549, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549", "body": "Will need to replace upstreamPoint with upstreamPoints in SourceMarksCalculator"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549#pullrequestreview-906668268", "body": ""}
{"comment": {"body": "since sourcemarkID is required, probably better like this? then you don't need `NewSourcePoint`\r\n```\r\nPUT /sourcemarks/{sourceMarkId}/sourcepoint\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549#discussion_r824311172"}}
{"comment": {"body": "lol - wtf is this. stop working so hard? \ud83d\ude06 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549#discussion_r824311670"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549#pullrequestreview-906670430", "body": ""}
{"comment": {"body": "lol", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549#discussion_r824312886"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549#pullrequestreview-906670917", "body": ""}
{"comment": {"body": "Ah this endpoint is for creating new sourcepoints for multiple sourcemarks?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549#discussion_r824313297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549#pullrequestreview-906684411", "body": ""}
{"comment": {"body": "oooh, I see. yeah what you have is better \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/549#discussion_r824323856"}}
{"title": "Remove old histogram code", "number": 5490, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5490", "body": "We no longer need this code as it's being done via pipeline."}
{"title": "chore(deps): update dependency org.jetbrains.intellij.plugins:gradle-intellij-plugin to v1.13.3 - autoclosed", "number": 5491, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5491", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.jetbrains.intellij.plugins:gradle-intellij-plugin | 1.13.2 -> 1.13.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update plugin org.jetbrains.intellij to v1.13.3", "number": 5492, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5492", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.jetbrains.intellij | 1.13.2 -> 1.13.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.20.50", "number": 5493, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5493", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom | 2.20.3 -> 2.20.50 |  |  |  |  |\n| software.amazon.awssdk:sts | 2.20.3 -> 2.20.50 |  |  |  |  |\n| software.amazon.awssdk:sfn | 2.20.3 -> 2.20.50 |  |  |  |  |\n| software.amazon.awssdk:sqs | 2.20.3 -> 2.20.50 |  |  |  |  |\n| software.amazon.awssdk:ses | 2.20.3 -> 2.20.50 |  |  |  |  |\n| software.amazon.awssdk:sagemakerruntime | 2.20.3 -> 2.20.50 |  |  |  |  |\n| software.amazon.awssdk:s3 | 2.20.3 -> 2.20.50 |  |  |  |  |\n| software.amazon.awssdk:rds | 2.20.3 -> 2.20.50 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder | 2.20.3 -> 2.20.50 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update hopliteversion to v2.7.4", "number": 5494, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5494", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.sksamuel.hoplite:hoplite-hocon | 2.7.2 -> 2.7.4 |  |  |  |  |\n| com.sksamuel.hoplite:hoplite-core | 2.7.2 -> 2.7.4 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nsksamuel/hoplite\n\n### [`v2.7.3`]()\n\n-   Add better yaml errors when invalid file [#354]()\n-   Fix formatting for secrets which contain new lines [#363]()\n-   Moves `GcpSecretManagerPreprocessor` from azure to gcp package [#362]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update protobufversion to v3.22.2", "number": 5495, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5495", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.google.protobuf:protoc (source) | 3.22.0 -> 3.22.2 |  |  |  |  |\n| com.google.protobuf:protobuf-kotlin (source) | 3.22.0 -> 3.22.2 |  |  |  |  |\n| com.google.protobuf:protobuf-java-util (source) | 3.22.0 -> 3.22.2 |  |  |  |  |\n| com.google.protobuf:protobuf-java (source) | 3.22.0 -> 3.22.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nprotocolbuffers/protobuf\n\n### [`v3.22.2`]()\n\n### [`v3.22.1`]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Make targets for \"base\" stack and \"frontend\" stack", "number": 5496, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5496", "body": "Also, clean-docker should not prune volumes (postgres). This is probably unexpected\nmost of the time, as it will lead to local data loss."}
{"title": "PR ingestion clears next batch URLs once the collection has been entirely drained", "number": 5497, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5497", "body": "This normally doesn't have any effect on the behaviour on the system because once\nbulk ingestion has completed then the next batch URLs are ignored anyway. However,\nit's good to clear these out to avoid confusion."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5497#pullrequestreview-1367563175", "body": ""}
{"title": "Prevent IDE fighting with ktlint over alias import order", "number": 5498, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5498", "body": "Problem\n- ktlint orders all imports lexicographical\n- by default the IDE orders alias imports before non-alias imports\nResolve dispute by forcing IDE to align with ktlint rules and lexicographically order."}
{"title": "Extend the IP filter rule to include SendInBlue", "number": 5499, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499", "body": "~This is a quick and dirty fix to address our security issue. I will rework IP filter rules to make them easier to read/update and manage.~\nReworked this PR based on review feedback. \n- Changed the config to allow specifying new rules for custom endpoints\n- Split the deepcheck and custom endpoint rules.\nI have tested it locally against dev. Works as expected. For Webhook endpoints I tested it by adding my own IP address to IP whitelist and then tested against the endpoint. IP filtering is working as expected.\nLinear task: "}
{"html_url": "https://github.com/NextChapterSoftware/unblocked/pull/5499#issuecomment-1495313622", "body": "top-level comment @richiebres @davidkwlam \n(testing)"}
{"comment": {"body": "@pwerry,\r\nThe application side filter might be something we still need to do if we are to support Webhooks from hosted SCMs. Clients need to let us know about their IPs so we allow their web hooks through. Alternatively we can come up with non-guessable web hook urls for them or something along that line. \r\n\r\n\r\n@richiebres \r\nFor automated testing, I am going to add some Grafana probes which should normally fail and would alarm on a success. \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#issuecomment-1496289206"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-1367664872", "body": ""}
{"comment": {"body": "Each hook endpoint should have it's own IP allowlist.\r\n\r\nThis change means that an attacker from send-in-blue source IP is allowed to make requests to our GitHub hook endpoint. We'll be adding GitLab and Bitbucket IP ranges soon too, which will make this worse.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1154937268"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-1367667153", "body": ""}
{"comment": {"body": "We had a conversation to make this a setting on Webhook service and moving out of the WAF. That way it's easier to track. Also if at any point we decide to allow web hook from hosted SCMs or any third-party service we can allow users to configure their own IP allow list for their endpoint", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1154939159"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-1367668084", "body": ""}
{"comment": {"body": "I actually had the change you are talking about but scrapped it because it felt messy to create it in that way. I can do it if you think that's still a better approach compared to filtering on Webhook service itself \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1154939882"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-1367668543", "body": ""}
{"comment": {"body": "Where is the code for filtering on webhook service itself?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1154940205"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-1367669233", "body": ""}
{"comment": {"body": "Not written yet. We were just talking about how should we do it long term. This is just a quick fix to address the security issue. \r\n\r\nBtw we need to do the same for Gitlab and Bitbucket cloud. As you can see the list is already growing ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1154940789"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-1367669836", "body": ""}
{"comment": {"body": "I know, that's what I said above:\n\n\n\n> We'll be adding GitLab and Bitbucket IP ranges soon too, which will make this worse.\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1154941249"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-**********", "body": ""}
{"comment": {"body": "So this change strengthens SendInBlue, but weakens GitHub.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1154941764"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Ok I'll modify the code to add separate IPset and rule for each provider. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1154942530"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Cool, thanks.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1154943497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Did the changes you mentioned. It's ready for review", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#discussion_r1157364087"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-**********", "body": "Thanks Mahdi this is appreciated. I'm still not convinced the application is the right place for IP filtering (for the next step). Even allowing traffic to traverse to the application can expose framework vulnerabilities, so ideally this stays at the WAF"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5499#pullrequestreview-1371351113", "body": "Thanks! Is there a way to test this in an automated way?"}
{"title": "Fix more lint and upgrade deps", "number": 55, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/55"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/55#pullrequestreview-855008681", "body": ""}
{"title": "Delete calculated source points", "number": 550, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/550", "body": "This is intended to be called for debugging purposes only, where it is useful to remove calculated source points while we are still iterating on the algorithm. Will probably remove after a few weeks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/550#pullrequestreview-906680759", "body": ""}
{"title": "Adds more PR SCM APIs", "number": 5500, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5500", "body": "Implementation will follow."}
{"title": "Hook up GitLab to PR ingestion", "number": 5501, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5501", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5501#pullrequestreview-1367679043", "body": ""}
{"title": "Update topic page for Doug ", "number": 5502, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5502", "body": "This updates the topic page to better reflect what a customer sees to help with curation of topics during onboarding new customers. Follow up PR to allow removing insights from a topic."}
{"title": "chore(deps): update kotlin monorepo to v1.8.20", "number": 5503, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5503", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.jetbrains.kotlin.plugin.jpa (source) | 1.8.10 -> 1.8.20 |  |  |  |  |\n| org.jetbrains.kotlin.plugin.serialization (source) | 1.8.10 -> 1.8.20 |  |  |  |  |\n| org.jetbrains.kotlin.jvm (source) | 1.8.10 -> 1.8.20 |  |  |  |  |\n| org.jetbrains.kotlin:kotlin-gradle-plugin (source) | 1.8.10 -> 1.8.20 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nJetBrains/kotlin\n\n### [`v1.8.20`](): Kotlin 1.8.20\n\n##### Changelog\n\n##### Analysis API\n\n-   [`KT-55510`]() K2: Lost designation for local classes\n-   [`KT-55191`]() AA: add an API to compare symbol pointers\n-   [`KT-55487`]() K2: symbol pointer restoring doesn't work for static members\n-   [`KT-55336`]() K2 IDE: \"java.lang.IllegalStateException: Required value was null.\" exception while importing a compiled JPS project\n-   [`KT-55098`]() AA: KtDeclarationRenderer should render a context receivers\n-   [`KT-51181`]() LL API: errors for SAM with suspend function from another module\n-   [`KT-50250`]() Analysis API: Implement Analysis API of KtExpression.isUsedAsExpression\n-   [`KT-54360`]() KtPropertySymbol: support JvmField in javaSetterName and javaGetterName\n\n##### Analysis API. FE1.0\n\n-   [`KT-55825`]() AA FE1.0: stackoverflow when resolution to a function with a recursive type parameter\n\n##### Analysis API. FIR\n\n-   [`KT-54311`]() K2: proper implementation of KtSymbolPointer\n-   [`KT-50238`]() Analysis API: Implement KSymbolPointer for KtSymbol\n\n##### Analysis API. FIR Low Level API\n\n-   [`KT-52160`]() FIR: Substitution overrides on FirValueParameter-s are incorrectly unwrapped\n-   [`KT-55566`]() LL FIR: Tests in `compiler/testData/diagnostics/tests/testsWithJava17` fail under LL FIR\n-   [`KT-55339`]() LL FIR: Missing RECURSIVE_TYPEALIAS_EXPANSION error in function type alias\n-   [`KT-55327`]() LL FIR: Diverging UNRESOLVED_REFERENCE errors in recursive local function test\n-   [`KT-54826`]() KtSymbolPointer: migrate from IdSignature to our own solution\n\n##### Android\n\n-   [`KT-54464`]() MPP, Android SSL2: Add a flag for suppressing warning in case of using Android Style folders\n\n##### Backend. Wasm\n\n-   [`KT-38924`]() Wasm support in nodejs\n-   [`KT-56160`]() Getting WebAssembly.CompileError in browsers not supported GC and other required proposals\n-   [`KT-46773`]() Implement an experimental version of the Kotlin/Wasm compiler backend\n-   [`KT-56584`]() K/Wasm: Can't link symbol class\n-   [`KT-56166`]() Fix compatibility with Firefox Nightly\n-   [`KT-55589`]() Basic support of WASI\n-   [`KT-53790`]() Reading from \"node:module\" is not handled by plugins error with Kotlin/Wasm 1.7.20-Beta\n\n##### Compiler\n\n##### New Features\n\n-   [`KT-54535`]() Implement custom equals and hashCode for value classes in Kotlin/JVM\n-   [`KT-55949`]() Release experimental `@Volatile` support in native\n-   [`KT-44698`]() Frontend (K2): print file name/line on compiler crash/exception\n-   [`KT-54666`]() K2: Allow to skip specifying type arguments for members from raw type scope\n-   [`KT-54524`]() Implement Java synthetic property references in compiler\n-   [`KT-54024`]() K2: support -Xlink-via-signatures mode\n\n##### Performance Improvements\n\n-   [`KT-33722`]() JVM: Result API causes unnecessary boxing\n-   [`KT-53330`]() Optimize for-loops and contains over open-ended ranges with until operator (`..<`) for all backends\n-   [`KT-54415`]() JVM BE: performance loss related to multi-field inline class lowering\n-   [`KT-48759`]() Infix compareTo boxes inline classes\n-   [`KT-55033`]() Make org.jetbrains.kotlin.resolve.calls.inference.model.NewConstraintSystemImpl#runTransaction an inline function\n-   [`KT-54501`]() Improve code generation for inline classes with custom equals\n\n##### Fixes\n\n-   [`KT-56965`]() K/N: linkDebugFrameworkIosArm64 tasks failing with UnsupportedOperationException: VAR name:disposables type:com.badoo.reaktive.disposable.CompositeDisposable \\[val]\n-   [`KT-56611`]() Native: new native caches are broken when KONAN_DATA_DIR is defined to a directory inside ~/.gradle\n-   [`KT-55251`]() Enum.entries compilation error should be more specific\n-   [`KT-56527`]() K2: \"AssertionError: Assertion failed\" during compilation in SequentialFilePositionFinder\n-   [`KT-56526`]() InvalidProtocolBufferException on reading module metadata compiled by K2 in 1.8.20\n-   [`KT-57388`]() Kapt+JVM_IR: \"RuntimeException: No type for expression\" for delegated property\n-   [`KT-53153`]() Synthetic Enum.entries can be shadowed by user-defined declarations\n-   [`KT-51290`]() \"AssertionError: Parameter indices mismatch at context\" with context receivers\n-   [`KT-57242`]() Equals behaviour for value classes implementing interfaces is different between 1.8.10 and 1.8.20-RC\n-   [`KT-57261`]() \"IllegalArgumentException was thrown at: MemoizedInlineClassReplacements.getSpecializedEqualsMethod\" when comparing non-inline class instance with an inline class instance\n-   [`KT-57107`]() Handling of Windows line endings CRLF broken in latest snapshot with K2\n-   [`KT-57117`]() K2: Compiler reports invalid columns in diagnostics in case of crlf line endings\n-   [`KT-56500`]() The type parameter TYPE_PARAMETER name:E index:0 variance: superTypes:\\[kotlin.Any?] reified:false is not defined in the referenced function FUN LOCAL_FUNCTION_FOR_LAMBDA\n-   [`KT-56258`]() VerifyError: Bad local variable type when using -Xdebug\n-   [`KT-54455`]() Unexpected result of equality comparison of inline class objects\n-   [`KT-56251`]() Generic Java synthetic property references don't work in K2\n-   [`KT-55886`]() K2: Wrong code location mapping with Windows line endings\n-   [`KT-43296`]() FIR: Complicated interaction between smart cast and inference leads to false-positive diagnostic\n-   [`KT-57053`]() Problem around anonymous objects in inline functions\n-   [`KT-54950`]() NoSuchMethodError on calling 'addAll' on inline class implementing mutable list\n-   [`KT-56815`]() compileKotlin task is stuck with while(true) and suspend function\n-   [`KT-56847`]() Unresolved reference to Java annotation in Kotlin class with the same name packages\n-   [`KT-52459`]() Context receivers: AbstractMethodError caused by Interface method with both an extension and a context receiver is overriden incorrectly in subclasses\n-   [`KT-56215`]() JVM: Object extension function nullable receiver null check false negative when object is null\n-   [`KT-56188`]() K/N: AssertionError when casting SAM wrapper with generic type parameter\n-   [`KT-56033`]() Restore 'isMostPreciseContravariantArgument' function signature for compatibility\n-   [`KT-56407`]() Backend Internal error: Exception during IR lowering during `:daemon-common-new:compileKotlin`\n-   [`KT-55887`]() K2. \"IllegalStateException: org.jetbrains.kotlin.ir.expressions.impl.IrErrorCallExpressionImpl is not expected\" on adding kotlin.plugin.jpa\n-   [`KT-56701`]() K2 (with LightTree) reports syntax errors without additional information\n-   [`KT-56649`]() K2 uses 0-index for line numbers rather than 1-index\n-   [`KT-54807`]() K2. Support `@OnlyInputTypes` diagnostic checks (`contains` like calls)\n-   [`KT-51247`]() \"AssertionError: org.jetbrains.kotlin.ir.expressions.impl.IrFunctionReferenceImpl\" caused by context receiver functional types\n-   [`KT-55436`]() K1: implement warning about shadowing of the derived property by the base class field\n-   [`KT-56521`]() Static scope initializers sometimes not called when first accessed from interop\n-   [`KT-49182`]() Strange cast from Unit to String\n-   [`KT-55288`]() False negative WRONG_ANNOTATION_TARGET on type under a nullability qualifier\n-   [`KT-33132`]() Cannot override the equals operator twice (in a class and its subclass) unless omitting the operator keyword in the subclass\n-   [`KT-56061`]() K1 does not report error on inconsistent synthetic property assignment\n-   [`KT-55483`]() K2: Fir is not initialized for FirRegularClassSymbol java/lang/invoke/LambdaMetafactory\n-   [`KT-55125`]() Difference in generated bytecode for open suspend functions of generic classes\n-   [`KT-54140`]() SOE at `IrBasedDescriptorsKt.makeKotlinType` with mixing recursive definitely not nullable type with nullability\n-   [`KT-56224`]() Clarify message \"Secondary constructors with bodies are reserved for for future releases\" for secondary constructors in value classes with bodies\n-   [`KT-54662`]() K2: Assign operator ambiguity on synthetic property from java\n-   [`KT-54507`]() K2: Wrong `implicitModality` for interface in `FirHelpers`\n-   [`KT-55912`]() \"UnsupportedOperationException: Unsupported const element type kotlin.Any\" caused by `kotlin` fqn in annotation\n-   [`KT-56018`]() \\[K2/N] Fir2Ir does not take value parameters annotations from FIR to IR\n-   [`KT-56091`]() \\[K2/N] Fix various property annotations\n-   [`KT-54209`]() K2: false positive deprecation on a class literal with deprecated companion\n-   [`KT-55977`]() \\[K2/N] Suspend function reference type is wrongly serialized to klib\n-   [`KT-55493`]() K2: False-negative VAL_REASSIGNMENT\n-   [`KT-55372`]() K2: false-negative INVISIBLE_MEMBER for call of static method of package-private Java grandparent class\n-   [`KT-55371`]() K2: compiled code fails trying to call static method of package-private Java grandparent class\n-   [`KT-55408`]() K2: can't access indirectly inherited from a package-private class Java members through a type alias\n-   [`KT-55116`]() K2: store static qualifiers in dispatch receiver field\n-   [`KT-55996`]() K2: cannot switch the light tree mode off with -Xuse-fir-lt=false\n-   [`KT-55368`]() K2/MPP: Metadata compiler\n-   [`KT-54305`]() K1: implement warning \"synthetic setter projected out\"\n-   [`KT-52027`]() \"NullPointerException\" when using context receivers with inline fun\n-   [`KT-55984`]() Stack allocated array is not cleaned between loop iterations\n-   [`KT-52593`]() Provide Alpha support for JS in the K2 platform\n-   [`KT-54656`]() NoSuchMethodError on invoking Java constructor which takes an inline value class as a parameter\n-   [`KT-56015`]() Remove unnecessary stack traces for special checks for ObjC interop\n-   [`KT-55606`]() K2. Infix operator \"in\" works on ConcurrentHashMap when it's declared through another class\n-   [`KT-53884`]() K2: \"IllegalStateException: Fir is not initialized for FirRegularClassSymbol com/appodeal/consent/Consent.a\" when importing this class\n-   [`KT-54502`]() Synthetic extensions on raw types work differently from regular getter calls\n-   [`KT-49351`]() FIR: Raw type scopes are unsupported\n-   [`KT-49345`]() FIR: Properly support raw types in type parameter upper bounds\n-   [`KT-55733`]() K2. Reference resolve works incorrectly for classes declared through typealias\n-   [`KT-46369`]() FIR: Investigate raw types for arrays\n-   [`KT-41794`]() \\[FIR] Implement raw type based scope\n-   [`KT-55181`]() K2. No compilation error on throwing not throwable\n-   [`KT-55398`]() Kotlin inline nested inline lambda's inline variable will inline not correctly\n-   [`KT-55359`]() K2. No error when secondary constructor does not delegate to primary one\n-   [`KT-55759`]() K2: Unresolved reference of `serializer` if library linking is used (with kotlinx.serialization plugin)\n-   [`KT-54705`]() Kotlin scripting doesn't support files with UTF-8 BOM\n-   [`KT-51753`]() FIR: various errors due to expect/actual mapping absence in translator\n-   [`KT-44515`]() FIR DFA: extract non-null info from anonymous object's initialization\n-   [`KT-55018`]() K2 / serialization: FIR2IR fails on local companion\n-   [`KT-55284`]() Refactor org.jetbrains.kotlin.diagnostics.KtDiagnosticReportContextHelpersKt#reportOn(...)\n-   [`KT-55693`]() K2. Type inference changed in k2\n-   [`KT-54742`]() K2: lambda with conditional bare `return` inferred to return Any, not Unit\n-   [`KT-54332`]() Add deprecation warning for false-negative TYPE_MISMATCH for KT-49404\n-   [`KT-55509`]() Invisible fake overrides are listed among lazy IR class members\n-   [`KT-55597`]() K2. `This type has a constructor, and thus must be initialized here` error is missed for anonymous object inherits class with no-arg constructor\n-   [`KT-54357`]() \"ClassCastException: class org.jetbrains.kotlin.resolve.scopes.receivers.ExtensionReceiver cannot be cast to class org.jetbrains.kotlin.resolve.scopes.receivers.ContextClassReceiver\" with anonymous object extending a class with a context receiver\n-   [`KT-51397`]() \"VerifyError: Bad type on operand stack\" with context receivers\n-   [`KT-54905`]() KLIB check on compiled with pre-release version\n-   [`KT-55615`]() K2 often does not expand type aliases in annotation position\n-   [`KT-54522`]() K2: ambiguity between operator candidates on += (plusAssign) to reassigned var of MutableList type\n-   [`KT-54300`]() K2: No \"Projections are not allowed for immediate arguments of a supertype\" for projection in supertypes of an anonymous object\n-   [`KT-55495`]() K2: support lateinit intrinsic applicability checker\n-   [`KT-55494`]() MPP. Error when building for native: Compilation failed: Global 'kclass:io.ktor.serialization.$deserializeCOROUTINE$0' already exists\n-   [`KT-54980`]() K2: Explicit type arguments in calls with the wrong number of type arguments are not resolved\n-   [`KT-54730`]() K2: type aliases to generic functional interfaces attempt to re-infer explicitly specified type parameters\n-   [`KT-55611`]() IC / MPP: Optional internal annotations are not visible on incremental builds\n-   [`KT-55324`]() K2: ControlFlowGraphBuilder fails with index out of bounds exception\n-   [`KT-55656`]() K2: PRIMARY_CONSTRUCTOR_DELEGATION_CALL_EXPECTED being a warning causes a NPE in runtime\n-   [`KT-51277`]() \"NoSuchElementException: Collection contains no element matching the predicate\" with context receivers and star projection\n-   [`KT-52791`]() Class with multiple context receivers fails -Xvalidate-ir with \"Validation failed in file\"\n-   [`KT-55071`]() Shared Native Compilation: Calls from intermediate common source set cannot use default parameters declared in expect common functions\n-   [`KT-52193`]() Native: Unable to call primary constructor with default values in an actual class without passing the values, in nativeMain source set\n-   [`KT-54573`]() K2: untouched implicit types in delegated constructor call of data class with `@JvmRecord`\n-   [`KT-55037`]() Support jspecify annotations moved to the new package org.jspecify.annotations in jspecify 0.3\n-   [`KT-48989`]() JVM / IR: \"IllegalStateException: Bad exception handler end\" when first parameter of inline function is nullable with \"try/catch/finally\" default value and second parameter tries to call toString() on the first\n-   [`KT-55231`]() K2: Contract declarations are not passed to checkers\n-   [`KT-54411`]() False positive: INFERRED_TYPE_VARIABLE_INTO_POSSIBLE_EMPTY_INTERSECTION on kotlinx.coroutines code\n-   [`KT-55005`]() Inconsistent behavior of array set operation in Kotlin 1.7.21\n-   [`KT-44625`]() Property backing/delegate field annotations are not serialized/deserialized for non-JVM targets\n-   [`KT-42490`]() Receiver annotations are not serialized/deserialized for non-JVM targets\n-   [`KT-53441`]() K2: cannot access static method of package-private Java grandparent class\n-   [`KT-54197`]() \\[K2] Exception from inliner for inline function with context receiver\n-   [`KT-55246`]() Disable 'CustomEqualsInValueClasses' feature in 1.9 language version\n-   [`KT-55247`]() Disable 'InlineLateinit' feature in 1.9 language version\n-   [`KT-53957`]() K2 and -Xlambdas=indy: LambdaConversionException on reference to method with both context and extension receivers\n-   [`KT-55421`]() K2: get rid of potentially redundant call of preCacheBuiltinClassMembers from getIrClassSymbol\n-   [`KT-52815`]() Compiler option -Xjdk-release fails to compile mixed projects\n-   [`KT-52236`]() Different modality in psi and fir\n-   [`KT-54921`]() K2: cannot access static field of package-private Java parent class\n-   [`KT-53698`]() K2: FIR2IR fails on call of inivisble extension function with Suppress\n-   [`KT-53920`]() K2: \"NoSuchElementException: Key `org.jetbrains.kotlin.fir.resolve.dfa.cfg.ClassExitNode@ef115ab` is missing in the map\" with unreachable code and anonymous object\n-   [`KT-55358`]() INTEGER_OPERATOR_RESOLVE_WILL_CHANGE is not reported in return positions of functions\n-   [`KT-51475`]() \"ArrayIndexOutOfBoundsException: Index 4 out of bounds for length 4\" with context(Any) on inline function with contract\n-   [`KT-51951`]() \"IllegalStateException: No receiver\" caused by implicit invoke on typealias context receiver\n-   [`KT-52373`]() Context receivers: ClassCastException: function with dispatch, context, and extension receivers produces this when a parameter's default is included\n-   [`KT-54220`]() K2: compiler fails on compiling plus expression on unsigned int\n-   [`KT-54692`]() K2: compiler fails on compiling unsigned shifts\n-   [`KT-54824`]() K2: missing smartcast after two levels of aliasing and a reassignment\n-   [`KT-53368`]() Out of bounds read in sse version of String::hashCode\n-   [`KT-54978`]() K2: Property accesses with explicit type arguments pass frontend checkers\n-   [`KT-51863`]() ClassCastException when using context receivers with named argument.\n-   [`KT-55123`]() JvmSerializableLambda is not applicable in common code in multiplatform projects\n-   [`KT-45970`]() Missing deprecation warnings for constant operators calls in property initializers\n-   [`KT-54851`]() K2: analysis of as/is contains multiple errors that result in missing diagnostics\n-   [`KT-54668`]() K2: Inference error in body of lazy property with elvis with Nothing in RHS\n-   [`KT-55269`]() FIR2IR: Static functions and nested classes are missing from Fir2IrLazyClass\n-   [`KT-55026`]() K2: Function hides internal constructor from another module\n-   [`KT-53070`]() Update intellij testdata fixes for FIR and merge it to master\n-   [`KT-53492`]() No parameter null check generated for constructor taking an inline class type\n-   [`KT-50489`]() Smart cast may lead to failing inference\n-   [`KT-55160`]() Kotlin's fragment element types must not extend `IStubFileElementType`\n-   [`KT-55143`]() K2: INAPPLICABLE_JVM_NAME in JVM does not work for inline classes\n-   [`KT-47933`]() Report warning if kotlin.annotation.Repeatable is used together with java.lang.annotation.Repeatable\n-   [`KT-55035`]() FIR: do not use FirValueParameter for FirFunctionalTypeRef\n-   [`KT-55095`]() Wrong containingDeclarationSymbol in type parameter from Enhancement\n-   [`KT-53946`]() K2: don't resolve Enum.declaringClass and Enum.getDeclaringClass\n-   [`KT-54673`]() K2. \"Superclass is not accessible\" from interface error for sealed interfaces\n-   [`KT-55074`]() OptIn false negative: constructor call with default argument value\n-   [`KT-54260`]() K2: \"AssertionError: No modifier list, but modifier has been found by the analyzer\" when annotated annotation and AllOpen plugin\n-   [`KT-55034`]() FIR: provide information about containing function/constructor to FirValueParameter\n-   [`KT-54744`]() K2: reassigning a var erases smartcast info of a variable derived from the old value\n-   [`KT-53988`]() K2: False negative \"The expression cannot be a selector (occur after a dot)\"\n-   [`KT-53983`]() K2 crashes with NPE when 'this' is used inside enum class constructor\n-   [`KT-54910`]() Can not declare typed equals operator in inline class with \"Nothing\" return type\n-   [`KT-54909`]() Usage of custom typed equals operator in generic inline class is type-unsafe\n-   [`KT-53371`]() Properly resolve FIR to get fully resolved annotations\n-   [`KT-53519`]() FIR: argument mapping for annotations on value parameter is not properly built\n-   [`KT-54827`]() MPP: \"java.lang.IndexOutOfBoundsException: Index: 0\" during compilation of `androidMain` target\n-   [`KT-54417`]() K2: move receiver-targeted annotations to KtReceiverParameterSymbol and remove it from FirProperty receiver type\n-   [`KT-54972`]() K2: Local functions with multiple type arguments are broken\n-   [`KT-54762`]() Private constructor is accessible from a public inline function via `@PublishedAPI` annotation\n-   [`KT-54832`]() Deprecate incorrect callable references resolution behavior for KT-54316\n-   [`KT-54732`]() DirectedGraphCondensationBuilder.paint fails with StackOverflowError during linkReleaseFrameworkIos64\n-   [`KT-54897`]() K2: value class with private constructor stripped by jvm-abi-gen cannot be used in another module\n-   [`KT-54784`]() NPE from IrSourceCompilerForInlineKt.nonLocalReturnLabel on non-local break and continue in anonymous initializers and in scripts\n-   [`KT-54840`]() Field for const property on interface companion object loses deprecated status when copied to interface\n-   [`KT-53825`]() class files are generated when compilation fails with platform declaration clash\n-   [`KT-54526`]() K2: Raw type scope is lost after exiting from elvis\n-   [`KT-54570`]() K2: False-positive OVERLOAD_RESOLUTION_AMBIGUITY in case of combination of raw types\n-   [`KT-52157`]() Annotation on type parameter isn't present in the symbol loaded from the library\n-   [`KT-54318`]() VerifyError on `{ null }` in catch block\n-   [`KT-54654`]() K2: Implicit types leaks into delegated member\n-   [`KT-54645`]() K2: Clash of two inherited classes with the same name\n-   [`KT-53255`]() \\[FIR2IR] StackOverflowError with long when-expression conditions\n-   [`KT-48861`]() No warning on incorrect usage of array type annotated as Nullable in Java\n-   [`KT-54539`]() `@Deprecated` on members of private companion object is no longer needed\n-   [`KT-54403`]() Unexpected behaviour on overridden typed equals in inline class\n-   [`KT-54536`]() Unexpected result of comparison of inline class instances\n-   [`KT-54603`]() ClassCastException on comparison of inline classes with custom equals\n-   [`KT-54401`]() Unhandled exception on compilation inline class with 'equals' from 'Any' returning 'Nothing'\n-   [`KT-54378`]() K2: smart cast breaks subtyping in case with complex projections\n-   [`KT-53761`]() Reified type not propagated to supertype token through two inline functions\n-   [`KT-53876`]() Manually instantiated annotations with unsigned arrays are not equal\n-   [`KT-51740`]() NO_VALUE_FOR_PARAMETER: Consider increasing error highlighting range\n-   [`KT-54084`]() ClassCastException when trying to call a context receiver's method\n-   [`KT-51282`]() IllegalAccessError: Compiler for JVM 1.8+ makes lambdas access unaccessible classes when using `@JvmMultifileClasses`\n-   [`KT-53479`]() False positive \"Cannot access 'runCatching' before superclass constructor has been called\"\n-   [`KT-50950`]() JVM IR: \"AssertionError: FUN SYNTHETIC_GENERATED_SAM_IMPLEMENTATION\" when using bound reference to suspend SAM function\n-   [`KT-49364`]() \"VerifyError: Bad type on operand stack\" on cast which \"can never succeed\" from ULong to Int\n-   [`KT-51478`]() Inapplicable receiver diagnostic expected when there are two context receiver candidates\n\n##### Docs & Examples\n\n-   [`KT-32469`]() `@Synchronized` on extension method doesn't generate instance lock\n\n##### IDE\n\n##### New Features\n\n-   [`KTIJ-24378`]() Update Kotlin plugin to 1.8.0 in IDEA 223.2\n\n##### Performance Improvements\n\n-   [`KT-55445`]() KtUltraLightClassModifierList.hasModifierProperty requires resolve for PsiModifier.PRIVATE\n\n##### Fixes\n\n-   [`KTIJ-24657`]() Disable pre-release and other metadata checks in IDE\n-   [`KT-55929`]() Unresolved dependencies for intermediate multiplatform SourceSets\n-   [`KTIJ-24179`]() Bundle Kotlin 1.8.0 with Intellij IDEA 2022.3.2\n-   [`KTIJ-23547`]() K2 IDE: Functional type: explicit parameter name VS ParameterName annotation\n-   [`KTIJ-23347`]() K2 IDE. False positive \"Symbol fun intFun(): Unit is invisible\" in tests\n-   [`KT-55862`]() Can't resolve kotlin-stdlib-js sources in IDE\n-   [`KTIJ-23587`]() K2: SOE in delegate field resolution\n-   [`KT-55782`]() \\[SLC] Typealiases are not exapnded in arguments of annotations\n-   [`KT-55778`]() \\[SLC] Incorrect determination of useSitePostion for types of local declarations\n-   [`KT-55780`]() \\[SLC] No approximation of anonymous and local types in members\n-   [`KT-55743`]() K2 SLC: SymbolLightClassForClassOrObject must have a name\n-   [`KT-55604`]() Descriptor leak\n-   [`KT-55502`]() SLC: drop redundant 'final' modifier from synthetic static enum members\n-   [`KT-55497`]() LC: drop `@NotNull` annotation from parameter from synthetic Enum.valueOf\n-   [`KT-55496`]() SLC: generated synthetic enum methods by symbols instead of manual creation\n-   [`KT-55481`]() SLC: implement correct java annotations for annotation classes (Retention, Target, etc.)\n-   [`KT-55470`]() SLC: implement light class for RepeatableContainer\n-   [`KT-55442`]() SLC: 'isInheritor' for 'DefaultImpls' should work correctly\n-   [`KTIJ-23449`]() K2: \"parent must not be null\" from SymbolLightClassBase.getContext()\n-   [`KT-40609`]() IDE: False positive \"Exception is never thrown...\" in Java when Kotlin getter is annotated with Throws\n-   [`KT-54051`]() Migrate symbol light classes from KtSymbol to KtElement\n\n##### IDE. Completion\n\n-   [`KTIJ-22503`]() Support code completion for data objects\n-   [`KTIJ-22361`]() ISE java.lang.IllegalStateException: Expected FirResolvedTypeRef with ConeKotlinType but was FirImplicitTypeRefImpl  on K2\n\n##### IDE. Debugger\n\n-   [`KTIJ-24259`]() Debugger is stuck in an infinite loop in an Android project\n-   [`KTIJ-24003`]() Smart step into doesn't work for Java synthetic properties references\n-   [`KTIJ-24039`]() Support smart step into for property setters\n\n##### IDE. Decompiler, Indexing, Stubs\n\n-   [`KTIJ-24351`]() Kotlin Bytecode tool window: NullPointerException during inlining of inline function with object literal\n\n##### IDE. Gradle Integration\n\n-   [`KTIJ-24616`]() Gradle Integration: \"NoSuchMethodError: 'java.util.Collection org.jetbrains.kotlin.idea.projectModel.KotlinCompilation.getDeclaredSourceSets\" during sync fail after updating Kotlin IJ Plugin to 1.8.20-Beta\n-   [`KT-55347`]() Broken IDE sync for js: java.lang.IllegalStateException: Accessing Compile Dependencies Transformations is not yet initialised\n-   [`KTIJ-23781`]() TCS: Gradle Sync: Support friend\\&dependsOn via IdeaKotlinSourceDependency\n\n##### IDE. Gradle. Script\n\n-   [`KT-56941`]() Gradle KTS / Navigation: Go to declaration for Java types doesn't work\n\n##### IDE. Inspections and Intentions\n\n-   [`KTIJ-23404`]() K2 IDE. Platform type is inserted as type parameter for \"Change return type\" intention\n-   [`KTIJ-24319`]() \"Set module version to \\*\" quickfix isn't working\n-   [`KTIJ-23225`]() \"Change package\" intention unintentionally and intractably replaces text inside of critical strings and comments\n-   [`KTIJ-23892`]() UsePropertyAccessSyntaxInspection should also suggest replacing getter method references with method synthetic properties referencies after Kotlin 1.9\n-   [`KTIJ-22087`]() Support IDE inspections for upcoming data objects\n-   [`KTIJ-24286`]() Constant conditions: false positive \"Cast will always fail\" with cast of java.lang.String to kotlin.String\n-   [`KTIJ-23859`]() ConvertObjectToDataObjectInspection support more hashCode and toString cases\n-   [`KTIJ-23760`]() Get rid of `readResolve` logic in ConvertObjectToDataObjectInspection\n\n##### IDE. KDoc\n\n-   [`KTIJ-24342`]() KDoc: First line break character is swallowed when pasted\n\n##### IDE. Misc\n\n-   [`KTIJ-24370`]() Remove link to k2.xml from plugin.xml in kt-223 branches\n-   [`KTIJ-24210`]() Compatibility issue with the CUBA plugin\n\n##### IDE. Multiplatform\n\n-   [`KTIJ-21205`]() MPP: Kotlin not configured error is shown for K/N sources if Android target is presented\n-   [`KT-52172`]() Multiplatform: Support composite builds\n-   [`KT-56198`]() Multiplatform;Composite Builds: import fails if single jvm target multiplatform project consume included jvm build\n-   [`KTIJ-24147`]() MPP: NullPointerException: versionString must not be null\n\n##### IDE. Refactorings. Move\n\n-   [`KTIJ-24243`]() Move declarations: \"Search in comments and strings\" and \"Search for text occurrences\" options are always enabled when files are moved\n\n##### IDE. Script\n\n-   [`KT-56632`]() Script configuration cannot be loaded for embedded code snippets\n\n##### IDE. Wizards\n\n-   [`KTIJ-24562`]() Android target created by wizard contains AGP higher than supported\n-   [`KTIJ-24402`]() Changes \"Browser Application for Kotlin/Wasm\" wizard template\n-   [`KTIJ-23525`]() Wizard: Compose multiplatform: project won't build and require higher compileSdkVersion\n\n##### JavaScript\n\n##### New Features\n\n-   [`KT-54118`]() Kotlin/JS IR: keep declarations with non-minified names\n-   [`KT-35655`]() Investigate could we use \"names\" field in SourceMaps to improve debug experience\n\n##### Fixes\n\n-   [`KT-55971`]() KJS: Result of suspend function cannot be assigned to property of dynamic value\n-   [`KT-52374`]() KJS / IR: caling suspend function as dynamic ignores the rest of the expression\n-   [`KT-56884`]() KJS: \"Top-level declarations in .d.ts files must start with either a 'declare' or 'export' modifier.\" caused by enum and array inside the companion object\n-   [`KT-51122`]() Provide fully-qualified method name in Kotlin/JS source maps\n-   [`KT-56602`]() KJS / Serialization: polymorphicDefaultDeserializer unbound on Kotlin 1.8.20-Beta\n-   [`KT-56580`]() KJS: languageVersionSettings string is unstable\n-   [`KT-56581`]() KJS: Lock file for incremental cache\n-   [`KT-56582`]() KJS: Function type interface reflection crashes the compiler in incremental build\n-   [`KT-55720`]() KJS: `ReferenceError: SuspendFunction1 is not defined` with 1.8 when importing`kotlin.coroutines.SuspendFunction1`\n-   [`KT-56469`]() KJS: BE Incremental rebuild spoils source map comment\n-   [`KT-55930`]() KJS: A recursive callable reference of the inline function leads broken cross module references\n-   [`KT-31888`]() Kotlin/JS: make possible to call `main()` in main run tasks, but not in test tasks\n-   [`KT-51581`]() FIR: support JS backend\n-   [`KT-55786`]() KJS: Rewriting of secondary constructors if they are protected\n-   [`KT-52563`]() KJS / IR: Invalid TypeScript generated for class extending base class with private constructor\n-   [`KT-55367`]() KJS / IR + IC: Moving an external declaration between different JsModules() doesn't rebuild the JS code\n-   [`KT-55240`]() KJS: \"NoSuchElementException: No element of given type found\" caused by `@JsExport` and `Throwable's` child class\n-   [`KT-54398`]() KJS / IR + IC: Support \\*.d.ts generation\n-   [`KT-55144`]() KJS / IR + IC: Modifying an inline function which is used as a default param in another inline function doesn't invalidate a caller\n-   [`KT-54134`]() KJS / IR: \"TypeError: Cannot read properties of undefined\" in js block wrapped with suspend functions around\n-   [`KT-54911`]() KJS / IR + IC: invalidate all klib dependencies after removing it\n-   [`KT-54912`]() KJS / IR + IC: Commit cache header only in the end (after lowering)\n-   [`KT-52677`]() Native: StackOverFlow during \"kotlin.ir.util.RenderIrElementVisitor$renderTypeAnnotations$1.invoke\"\n-   [`KT-54480`]() KJS: \"Exported declaration contains non-consumable identifier\" warning when exporting modules as default\n-   [`KT-41294`]() KJS: Weird behaviour of j2v8 in test infra\n-   [`KT-54173`]() Kotlin/JS + IR: failed to provide `keep` setting to avoid DCE remove of default interface function from implementing object\n\n##### Language Design\n\n-   [`KT-55451`]() Preview of lifting restriction on secondary constructor bodies for value classes\n-   [`KT-54621`]() Preview of Enum.entries: modern and performant replacement for Enum.values()\n-   [`KT-54525`]() Preview of Java synthetic property references\n-   [`KT-55337`]() Preview of data objects\n-   [`KT-55344`]() Deprecate `@Synchronized` in platforms except JVM\n\n##### Libraries\n\n-   [`KT-35508`]() EXC_BAD_ACCESS(code=2, address=0x16d8dbff0) crashes on iOS when using a sequence (from map() etc.)\n-   [`KT-56794`]() Libraries: \"Recursively copying a directory into its subdirectory is prohibited\" Path.copyToRecursively fails on copying from one ZipFileSystem to another ZipFileSystem\n-   [`KT-55935`]() \\[Kotlin/JVM] Path.copyToRecursively does not work across file systems\n-   [`KT-55978`]() Provide Common Base64 encoding in stdlib\n-   [`KT-46211`]() \\[Kotlin/Native] Stack overflow crash in Regex classes with simple pattern and very large input\n-   [`KT-31066`]() Add Closeable & use to common stdlib\n-   [`KT-55609`]() Introduce experimental kotlin.concurrent.Volatile annotation\n-   [`KT-39789`]() Segfault in Kotlin/Native regex interpreter\n-   [`KT-53310`]() Native: HashMap/HashSet doesn't reclaim storage after removing elements\n\n##### Native\n\n-   [`KT-56443`]() Native link task reports w: Cached libraries will not be used for optimized compilation\n-   [`KT-55938`]() \\[Kotlin/Native] Inline functions accessing ObjC class companion cause compiler to crash when building static caches in 1.8.20 dev build\n\n##### Native. C and ObjC Import\n\n-   [`KT-55303`]() Objective-C import: improve `-fmodules` flag discoverability.\n-   [`KT-39120`]() Cinterop tool doesn't support the -fmodules compiler argument\n-   [`KT-40426`]() Incorrect Objective-C extensions importing that prevents UIKit usage\n-   [`KT-55653`]() Since Kotlin 1.8.0 NSView.resetCursorRects doesn't exist anymore and cannot override it\n-   [`KT-54284`]() Kotlin/Native: cinterop produces non-deterministic metadata\n\n##### Native. ObjC Export\n\n-   [`KT-56350`]() Kotlin/Native: restore \"use Foundation\" in generated Objective-C frameworks\n-   [`KT-55736`]() Native: exporting suspend function from a cached dependency to Objective-C crashes with \"Suspend functions should be lowered out at this point\"\n-   [`KT-53638`]() Native: support disabling mangling globally for Swift names in generated Objective-C header\n-   [`KT-53069`]() SOE on K/N framework build for Arm64\n-   [`KT-53317`]() ObjCName annotation is not applied to an extension receiver in Objective-C export\n\n##### Native. Stdlib\n\n-   [`KT-53064`]() Native: provide stdlib API to obtain memory management statistics\n\n##### Reflection\n\n-   [`KT-27585`]() Flaky IllegalPropertyDelegateAccessException: Cannot obtain the delegate of a non-accessible property. Use \"isAccessible = true\" to make the property accessible\n-   [`KT-55178`]() Improve performance of KCallable.callBy\n-   [`KT-53279`]() Reflection: \"KotlinReflectionInternalError: Method is not supported\" caused by `@Repeatable` annotation deserialization at runtime if it's repeated and contains arrays\n-   [`KT-44977`]() Reflection: ClassCastException caused by annotations with \"AnnotationTarget.TYPE\" usage on array attributes access\n\n##### Tools. CLI\n\n-   [`KT-57077`]() `1.8.20-RC-243` shows Java 19 warnings even if configured with Java 17 toolchain\n-   [`KT-56992`]() Performance test regression in Gradle when switching to Kotlin 1.8.20\n-   [`KT-56789`]() Metaspace memory leak in CoreJrtFileSystem\n-   [`KT-56925`]() Remove warning about assignment plugin\n-   [`KT-54652`]() Enable -Xuse-fir-lt by default when -Xuse-k2 is turned on, provide way to disable\n-   [`KT-55784`]() Unable to format compilation errors with ansi colors in compilation server\n-   [`KT-54718`]() K2: Compiler crashes with \"IllegalArgumentException: newPosition > limit\"\n-   [`KT-54337`]() CLI: compiling module-info.java without explicitly specified JDK home leads to a weird error\n\n##### Tools. Commonizer\n\n-   [`KT-47429`]() \\[Commonizer] OKIO support\n-   [`KT-51517`]() C Interop Commonizer Fails On Classifier That Doesn't Exist\n\n##### Tools. Compiler Plugins\n\n##### Fixes\n\n-   [`KT-53590`]() K2 Allopen does not look for transitive meta-annotations\n-   [`KT-56487`]() Add more methods to DescriptorSerializerPlugin\n-   [`KT-54020`]() \\[K2] \\[NEW_INFERENCE_NO_INFORMATION_FOR_PARAMETER] error in case 'static Name' param was added to `@AllArgsConstructor` annotation and an empty list is set as a constructor param value\n-   [`KT-53096`]() Create a pack of compiler utilities for generating declarations from plugins\n-   [`KT-55248`]() K2/PluginAPI: getCallableNamesForClass/generateClassLikeDeclaration are not called for synthetic companions of local classes\n-   [`KT-54756`]() Deprecate \"legacy\" mode of jvm-abi-gen plugin\n-   [`KT-55233`]() jvm-abi-gen strips out InnerClass attributes\n-   [`KT-54994`]() K2 plugin API: Compile-time constants are not evaluated before IR\n-   [`KT-55023`]() K2 plugin API: Compilation with Kotlin daemon fails after certain number of tries\n-   [`KT-55286`]() K2: Parcelize plugin sometimes can't find nested objects in current class\n-   [`KT-54500`]() Private type aliases can be referenced from public declarations, but are stripped by jvm-abi-gen\n\n##### Tools. Compiler plugins. Serialization\n\n-   [`KT-56738`]() Unexpected SERIALIZER_NOT_FOUND when compiling against binary with enum\n-   [`KT-56990`]() \"AssertionError: SyntheticAccessorLowering should not attempt to modify other files\" in kotlinx-serialization using `@Serializer` and List argument\n-   [`KT-54441`]() Prohibit implicit serializer customization via companion object\n-   [`KT-49983`]() Implement prototype of kotlinx.serialization for K2 compiler\n-   [`KT-48733`]() \"AssertionError: Unexpected IR element found during code generation\" caused by Serialization and annotation with default parameter\n-   [`KT-54297`]() Regression in serializable classes with star projections\n\n##### Tools. Gradle\n\n##### New Features\n\n-   [`KT-54691`]() Kotlin Gradle Plugin libraries alignment platform\n-   [`KT-54492`]() Send gradle build errors from idea\n-   [`KT-55540`]() Add compilation speed metric in build reports\n-   [`KT-55541`]() Validate FUS metrics values on Gradle side\n\n##### Performance Improvements\n\n-   [`KT-54836`]() Kotlin/JVM Gradle plugin creates task eagerly on Gradle 7.3+\n-   [`KT-55995`]() Add ability to perform precise compilation task outputs backup\n-   [`KT-54579`]() Kapt tasks slow down significantly on Windows when running with JDK 17 compared to JDK 11\n-   [`KT-54588`]() KotlinCompile: Avoid calling `FileCollection.getFiles()` multiple times\n\n##### Fixes\n\n-   [`KT-57296`]() Build statistics sending errors in case of buildSrc directory usage with kotlin-dsl plugin applied\n-   [`KT-56645`]() Gradle: KGP reports an incorrect resources processing task name for JVM projects\n-   [`KT-55824`]() Deprecate `commonMain.dependsOn(anything)` in user scripts\n-   [`KT-56221`]() Gradle KTS: False positive `Val cannot be reassigned` when using an extension and its property with an implicit `set` operator\n-   [`KT-55452`]() Values of the compiler arguments set via KotlinCompile task configuration are duplicated by the KaptGenerateStubs task\n-   [`KT-55565`]() Consider de-duping or blocking standard addition of freeCompi\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"comment": {"body": "Blocked by this bug:\r\nhttps://youtrack.jetbrains.com/issue/KT-57647/Serialization-IllegalAccessError-Update-to-static-final-field-caused-by-serializable-value-class?s=update-to-static-final-field-attempted-from-a-different-method-constructor-impl-than-the-initializer-method-clinit-when", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5503#issuecomment-1518145441"}}
{"title": "Refactor SCM comment APIs", "number": 5504, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5504", "body": "I'll add implementation pullRequestAllComments for GitLab and Bitbucket next."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5504#pullrequestreview-1367715663", "body": ""}
{"title": "Fix incorrect scroll to top behaviour", "number": 5505, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5505", "body": "useScrollToTop was taking in a function that returned a ref instead of the ref itself, and this function was passed in as a dependency in useEffect.  The function was being recreated on every render, so the effect was triggering on every render.\nRefs are stable (that's the whole point of a ref), so we can just pass them in directly."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5505#pullrequestreview-1367725378", "body": ""}
{"title": "OpenAPI kotlin client models should generate proper dates", "number": 5506, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5506", "body": "Our client kotlin API libraries were generating date properties as Strings.  I'm not sure why this was happening, as the generator docs say it should default to java8 dates "}
{"title": "Send pull request ingestion event for GitLab and Bitbucket during bulk ingestion", "number": 5507, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5507"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5507#pullrequestreview-1367750561", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5507#pullrequestreview-1367750834", "body": "nicely done"}
{"title": "Process Bitbucket PRs in descending order for bulk ingestion", "number": 5508, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5508", "body": "Newest PRs are ingested first.\nTurns out Bitbucket Cloud APIs have pretty flexible sorting:\n"}
{"title": "Fix deps in scroll fn", "number": 5509, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5509"}
{"title": "Fix logging", "number": 551, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/551"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/551#pullrequestreview-906684012", "body": ""}
{"title": "Fix emoji in titles", "number": 5510, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5510"}
{"title": "Don't reload topic page on topic deletion", "number": 5511, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5511"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5511#pullrequestreview-1369406558", "body": ""}
{"title": "Disable descriptions for non-demo teams", "number": 5512, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5512"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5512#pullrequestreview-1369413541", "body": ""}
{"title": "Add manual flag to show topic descriptions", "number": 5513, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5513"}
{"comment": {"body": "> Looks valid. This will _hide_ descriptions be default right?\r\n\r\nYes. This way we can view/approve the content in the admin console first before exposing it in the UI. I'll manually turn it on for our team once this deploys.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5513#issuecomment-1494674229"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5513#pullrequestreview-1369435749", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5513#pullrequestreview-1369439944", "body": "Looks valid. This will hide descriptions be default right?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5513#pullrequestreview-1369443881", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5513#pullrequestreview-1369444437", "body": ""}
{"title": "Revert \"Disable descriptions for non-demo teams (#5512)\"", "number": 5514, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5514", "body": "This reverts commit 565e83288d705c1ef7274ab8be766efedb7a7ed1.\n"}
{"title": "Fi xslack emoji", "number": 5515, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5515", "body": "Fix slack emoji in title"}
{"title": "Fix eslint monorepo setup", "number": 5516, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5516", "body": "This helps when opening the root unblocked folder in VSCode, it ensures eslint parses the root folder locations correctly."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5516#pullrequestreview-1369559104", "body": ""}
{"title": "chore(deps): update dependency nicklockwood/swiftformat to from: \"0.51.7\"", "number": 5517, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5517", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| nicklockwood/SwiftFormat | patch | from: \"0.51.3\" -> from: \"0.51.7\" |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnicklockwood/SwiftFormat\n\n### [`v0.51.7`]()\n\n[Compare Source]()\n\n-   Fixed `redundantSelf` incorrectly inserting `self` for local variables declared in capture list\n-   Fixed `blankLineAfterImports` rule inserting blank line before `@_spi` imports\n-   Fixed `fileHeader` rule ignoring headers containing URLs\n\n### [`v0.51.6`]()\n\n[Compare Source]()\n\n-   Required `self` is now preserved in function bodies inside closures with `[weak self]` captures\n-   Fixed bug with `hoistTry` inside chains of concatenated interpolated strings\n-   Fixed indenting of dot-prefixed identifiers inside `#else` and `#elseif` blocks\n-   Fixed parsing bug in `redundantSelf` rule\n\n### [`v0.51.5`]()\n\n[Compare Source]()\n\n-   Added `--baseconfig` option to replicate old `--config` behavior\n-   Fixed `self` being incorrectly inserted inside capture list\n-   Fixed indenting of `.init` inside `#if` statements\n-   Fixed `redundantInit` glitch inside `#if` statements\n-   Fixed `redundantSelf` inside `if case` expressions\n-   Fixed `hoistTry` for strings containing multiple interpolation clauses\n-   Fixed redundant parens not being removed after `return` keyword\n-   Fixed spacing after attribute when using `--funcattributes same-line`\n-   Fixed false positive in collection literals for `unusedArguments`\n-   Fixed file access permissions errors not being reported\n\n### [`v0.51.4`]()\n\n[Compare Source]()\n\n-   Limited `redundantReturn` inside if / switch expressions to Swift 5.9+\n-   Fixed `hoistTry` and `hoistAwait` inside multiline string literals\n-   Fixed invalid indenting of blank lines inside multiline string literals\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Show archive state for pull request in admin console", "number": 5518, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5518"}
{"title": "Set correct defaults for explorer insights panel in Jetbrains", "number": 5519, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5519"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5519#pullrequestreview-1373410969", "body": ""}
{"title": "Run in DEV stack", "number": 552, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/552"}
{"title": "Topic scm ingestion", "number": 5520, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5520"}
{"title": "getPullRequestsForCommits does not return archived pull requests", "number": 5521, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5521"}
{"comment": {"body": "Hi @davidkwlam\r\n\r\ntry searching for this text", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5521#issuecomment-1495377116"}}
{"title": "Fix issue with Insights panel refreshing", "number": 5522, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5522", "body": "Fixes issue where PR Insight view would \"refresh\" and navigate to the first tab at the top of the page.\nSource of issue is within PullRequestInfoStream. Whenever it was updated upstream, its mapStreamAsync function would trigger. This would momentarily change the stream's result to \"loading\", causing the PRView within the webview to unmount, losing all its local state. Once the stream changes back to loaded, the PRView remounts.\nThis PR includes multiple improvements:\n1. Lift selected tab state out of webview and into extension. State shouldn't be held within the webview\n2. Whenever auth refreshes, person updates as well. Dedupe the PersonStream results as there are many downstream subscribers that shouldn't be triggered on a no-op person event.\n3. Debounce SourceMarkSnippetStream"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5522#pullrequestreview-1369895240", "body": ""}
{"comment": {"body": "Snippet styling is immutable for the same sourcemark. Cache results so that subsequent PRInfoStream events are more performant.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5522#discussion_r1156522227"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5522#pullrequestreview-1369896143", "body": ""}
{"comment": {"body": "This debounce is primarily to prevent MapStreamAsync from flipping between loaded -> loading -> loaded.\r\n\r\nThis sequence of states causes PRView within webview to mount -> unmount -> mount causing some jarring UX.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5522#discussion_r1156522870"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5522#pullrequestreview-1371362420", "body": ""}
{"comment": {"body": "As discussed, we can drop this, remove the second arg from the `mapStreamAsync` call above, and use `.startsWith` instead", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5522#discussion_r1157498861"}}
{"title": "Fix up glue etl", "number": 5523, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5523"}
{"title": "disabled weak ciphers", "number": 5524, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5524", "body": "Before: \nWe were getting the same results for dev.getunblocked.com but I forgot to save the output!\n```\nnmap --script ssl-enum-ciphers -p 443 getunblocked.com\nStarting Nmap 7.93 (  ) at 2023-04-03 17:16 PDT\nNmap scan report for getunblocked.com (*************)\nHost is up (0.038s latency).\nOther addresses for getunblocked.com (not scanned): ************** ************* **************\nrDNS record for *************: server-108-139-79-74.dxb53.r.cloudfront.net\nPORT    STATE SERVICE\n443/tcp open  https\n| ssl-enum-ciphers:\n|   TLSv1.2:\n|     ciphers:\n|       TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A\n|       TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A\n|       TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A\n|       TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384 (ecdh_x25519) - A\n|       TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256 (ecdh_x25519) - A\n|     compressors:\n|       NULL\n|     cipher preference: server\n|   TLSv1.3:\n|     ciphers:\n|       TLS_AKE_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A\n|       TLS_AKE_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A\n|       TLS_AKE_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A\n|     cipher preference: server\n|_  least strength: A\nNmap done: 1 IP address (1 host up) scanned in 3.29 seconds\n```\nAfter:\n```\nnmap --script ssl-enum-ciphers -p 443 dev.getunblocked.com\nStarting Nmap 7.93 (  ) at 2023-04-03 17:39 PDT\nNmap scan report for dev.getunblocked.com (*************)\nHost is up (0.036s latency).\nOther addresses for dev.getunblocked.com (not scanned): ************ ************* ************\nrDNS record for *************: server-13-35-169-127.fjr50.r.cloudfront.net\nPORT    STATE SERVICE\n443/tcp open  https\n| ssl-enum-ciphers:\n|   TLSv1.2:\n|     ciphers:\n|       TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A\n|       TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A\n|       TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A\n|     compressors:\n|       NULL\n|     cipher preference: server\n|   TLSv1.3:\n|     ciphers:\n|       TLS_AKE_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A\n|       TLS_AKE_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A\n|       TLS_AKE_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A\n|     cipher preference: server\n|_  least strength: A\nNmap done: 1 IP address (1 host up) scanned in 2.11 seconds\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5524#pullrequestreview-1370005424", "body": ""}
{"title": "Only display a single update toast by first dismissing the first one", "number": 5525, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5525"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5525#pullrequestreview-1371343366", "body": ""}
{"comment": {"body": "I kept having this pop up again too -- I dunno what this is, and if we should commit it or gitignore it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5525#discussion_r1157486274"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5525#pullrequestreview-1371346429", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5525#pullrequestreview-1371351745", "body": ""}
{"comment": {"body": "Let's just commit it for now. If it causes churn or chaos then we should exempt", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5525#discussion_r1157491820"}}
{"title": "Implement comment APIs for GitLab and Bitbucket", "number": 5526, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526", "body": "GitLab uses discussion API, and use it to fabricate thread\nCreate and use ScmPrComment sealed class to represent top-, file-, and line-level comments"}
{"html_url": "https://github.com/NextChapterSoftware/unblocked/pull/5526#issuecomment-1495376272", "body": "Hi @davidkwlam \ntry searching for this text"}
{"comment": {"body": "@rasharab can you take a look at the SCM-data services changes. Any impact?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#issuecomment-1496232559"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#pullrequestreview-1370170192", "body": ""}
{"comment": {"body": "So `ScmPullRequestCommentThread.file`  will be null for top level comments. We chatted about this earlier today and I mentioned that the PullRequestIngestionService right now will ignore when a thread doesn't have a file but that I would update it to accept these threads with null `file` as long as the thread is a top-level comment.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156711084"}}
{"comment": {"body": "Could replace `discussion.notes.firstOrNull()?.id` with `firstNote.id`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156712449"}}
{"comment": {"body": "Ah we skip deleting the first message in a thread. Only if it doesn't have replies do we delete the message and its thread. The right thing to do is to make the second message the first message by updating `MessageModel.nestedParentId` for all other replies.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156714498"}}
{"comment": {"body": "In this PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156716248"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#pullrequestreview-1370177643", "body": ""}
{"comment": {"body": "I'm not sure its the most important thing to fix right now, if you want to create a linear task and assign to me I can own (though there may already be one)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156716612"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#pullrequestreview-1370218007", "body": ""}
{"comment": {"body": "ya", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156744110"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#pullrequestreview-1370219685", "body": ""}
{"comment": {"body": "`nestedParentId` is always null for GitHub, at least right now. Are you saying that we should backfill this for GitHub and GitLab?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156745351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#pullrequestreview-1370222189", "body": ""}
{"comment": {"body": "Yup, I'll change so that the comments are of type `ScmPrComment` instead of `ScmPrComment.CodeLevel`, then filter out the `ScmPrComment.TopLevel` comments and handle in a dummy function that you can implement later. That make sense?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156747008"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#pullrequestreview-1370225001", "body": ""}
{"comment": {"body": "Oh sorry, my bad -- ignore my comment, you're right. `MessageModel.nestedParentId` is only set for Bitbucket comments where a message is a reply to another reply in the same thread. This field is not set for GitHub and GitLab.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156748892"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#pullrequestreview-1370225792", "body": ""}
{"comment": {"body": "So I guess we could just delete the first comment in a thread, but I'd have to really understand the impact of that before I'd be comfortable deploying that change.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1156749389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#pullrequestreview-1371417075", "body": ""}
{"comment": {"body": "Yup, sounds good", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1157534167"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#pullrequestreview-1371427267", "body": ""}
{"comment": {"body": "I believe we can re-write this now because GitLab comments are created with an `inReplyToId` in this PR? If true then we can remove the `else` clause.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5526#discussion_r1157540284"}}
{"title": "Allow removing an insight from a topic from the admin console", "number": 5527, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5527", "body": "So that we can clean up topic mappings when we onboard a new customer"}
{"title": "adding more IP filters for gitlab, bitbucket and transcription service", "number": 5528, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5528", "body": "Added ip filter rule for Gitlab \nAdded ip filter rule for Bitbucket \nAdded IP filter rule for AssemblyAI \nSlack uses signature validation so we don't need to whitelist IP ranges for it."}
{"comment": {"body": "Each environment has its own WAF deployment and we don't share configuration or resourced between environments. This also helps us test things before publishing them to prod. Honestly it's a small cost for now. If at any point we see the list grow too large then I can make a re-usable resource and move it there. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5528#issuecomment-1496391914"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5528#pullrequestreview-1371488121", "body": "Any way to avoid the duplication: dev/prod IPs are identical."}
{"title": "Fix missing TranscriptionHooks in API docs", "number": 5529, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5529"}
{"title": "Fix Git runner race", "number": 553, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/553"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/553#pullrequestreview-906741852", "body": ""}
{"title": "Improve oai prompt", "number": 5530, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5530"}
{"title": "Add migration to backfill topic keywords", "number": 5531, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5531", "body": "First step before I add the ability to add to edit keywords in the admin console."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5531#pullrequestreview-1371609864", "body": "you clmplete me."}
{"title": "Fix Auth Issue", "number": 5532, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5532", "body": "Fixes auth issue where the cached token was not updating properly.\nThis caused unexpected 401s which were never refreshed."}
{"comment": {"body": "@rasharab For your enjoyment.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5532#issuecomment-1496466456"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5532#pullrequestreview-1371674772", "body": ""}
{"title": "Rotate sendinblue", "number": 5533, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5533"}
{"title": "Add openai client support from services", "number": 5534, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5534"}
{"title": "Disallow unsafe method capture", "number": 5535, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5535", "body": "Add eslint rule that disallows unsafe method capture.  This would have prevented https://github.com/NextChapterSoftware/unblocked/pull/5532#event-8928345474\nRule is described here: https://typescript-eslint.io/rules/unbound-method\nWe should generally be using arrow functions when making a lambda with class method calls."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5535#pullrequestreview-1371746194", "body": ""}
{"comment": {"body": "This is weird syntax, but this declares that anyone implementing this interface will *not* use `this` within this function.  This allows the function to be captured.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5535#discussion_r1157735966"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5535#pullrequestreview-1371764214", "body": ""}
{"title": "Add ability to update topic keywords from admin console", "number": 5536, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5536", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5536#pullrequestreview-1371901861", "body": ""}
{"title": "File is not required for getRecommendedTeamMembers", "number": 5537, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5537"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5537#pullrequestreview-1371776283", "body": ""}
{"title": "Add topics push channel", "number": 5538, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5538"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5538#pullrequestreview-1373763907", "body": ""}
{"title": "[RFC] Add ability to filter getTopics results by expert", "number": 5539, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5539", "body": "The clients need the ability to get the list of topics where a team member is an expert. This PR proposes modifying the getTopics operation to take an optional expert query parameter, which is the UUID of a team member. When present the topics returned will be just those where expert is an expert.\nIf this is not the ideal approach, we can come up with another endpoint."}
{"title": "[DO NOT MERGE] Introduce Git cache for performance", "number": 554, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/554", "body": "Interestingly, this made absolutely zero different. Not even 1 second faster on a 3 min baseline run."}
{"title": "Topic editing UI", "number": 5540, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540", "body": "\n\n\nsmall viewports:\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#pullrequestreview-1373640380", "body": ""}
{"comment": {"body": "This is used for resizing the `textarea` below. `textarea` elements do not naturally resize to the size of its content, so we use this hidden ref to manually set its height.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#discussion_r1158991373"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#pullrequestreview-1373826779", "body": ""}
{"comment": {"body": "Having both descriptions here is going to cause some mistakes (ie I would always just render `Topic.description` without thinking twice)... I wonder if we should provide a more ergonomic model here for general client use, which we would create during aggregation?\r\n\r\n```\r\nTopic {\r\n    description: string // resolved description\r\n    descriptionSources: {\r\n      generated: string // ML-generated description\r\n      userDefined?: string // user-defined description, if provided\r\n    }\r\n}\r\n```\r\n\r\nThen 99% of the code would use `TopicAggregate.description` everywhere, and the editing UI would know to use `TopicAggregate.descriptionSources` for its editing UI...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#discussion_r1159118023"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#pullrequestreview-1373839587", "body": ""}
{"comment": {"body": "This logic is complicated -- I can't really tell what it does.  Maybe we should move this up into the code (ie out of the JSX) so it'll be easier to read?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#discussion_r1159126160"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#pullrequestreview-1373873454", "body": ""}
{"comment": {"body": "I think we can probably just add an extra property to map the ML description to so that it's clear what the field represents. Similarly we can map the resolved description to the `topic.description` property.\r\n\r\ni.e.\r\n```\r\ndescription: // this is the resolved description\r\n    type: string\r\n    minLength: 1\r\n    maxLength: 280\r\n  mlGeneratedDescription: // this is the API topic.description\r\n    type: string\r\n    minLength: 1\r\n    maxLength: 280\r\n  userDefinedDescription: // this is the API topic.userDefinedDescription\r\n    type: string\r\n    minLength: 1\r\n    maxLength: 280", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#discussion_r1159153578"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#pullrequestreview-1375242341", "body": ""}
{"comment": {"body": "Note that this will overwrite the in-progress members (`experts`/`setExperts`) with the Topic's members any time topic refreshes for any reason -- because `Topic.expertMembers` is an array this is compared using reference equality.  I suspect under some conditions this will result in the user's selected experts getting thrown out, though I dunno if it will actually happen much in practice.  Do we need this, since we're initializing the selected members when we initialize the `experts` state?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#discussion_r1160039609"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#pullrequestreview-1375265699", "body": ""}
{"comment": {"body": "We have a `AutoSizingTextArea` component, I think that might do the same thing?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#discussion_r1160053368"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#pullrequestreview-1375266352", "body": ""}
{"comment": {"body": "(and if it doesn't, maybe it should?  ie maybe we should factor this into that class?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#discussion_r1160053776"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#pullrequestreview-1375269768", "body": "Nicely done! "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#pullrequestreview-1375423217", "body": ""}
{"comment": {"body": "I was running into some weird issues here where the list of topic experts would resolve incompletely on first load (i.e. when the `experts` state is defined).\r\n<img width=\"758\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/230472197-71df6b22-16e6-4f1d-8a4e-4d7e6526f1a4.png\">\r\n\r\nIt never updates again after fully resolving in my experience but I suspect this may change once we add the topics pusher channel. \r\n\r\nI'll make a note to revisit this when that PR gets merged.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5540#discussion_r1160157175"}}
{"title": "Only run lightweight PR ingestion for GitHub and GHE", "number": 5541, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5541", "body": "There are two alternative approaches:\nIngestion Job Type | GitHub | All other SCMs\n-- | -- | --\nLightweight onboarding | runs for each PR that has a comment, triggers a PR ingestion event for each PR, with priority. | does not run at all (this change)\nBulk onboarding | collects all PRs, and all comments. does not trigger PR ingestion events. TODO reverse PR list direction DAVE. | collects all PRs, but no comments. triggers a PR ingestion event for each PR, without priority.\nSync | runs periodically | TODO does not currently work RICHIE\nEvent | runs on each PR event, collecting PRs, review, comments; or just reviews | TODO does not currently work DAVE"}
{"title": "Add secrets", "number": 5542, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5542"}
{"title": "allPullRequests retrieves in desc order of created", "number": 5543, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5543"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5543#pullrequestreview-1371942651", "body": ""}
{"title": "Admin: Remove team dups from search results", "number": 5544, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5544"}
{"title": "Remove repoId from getRecommendedTeamMembers", "number": 5545, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5545", "body": "Not necessary."}
{"title": "Moving away from hardcoded oepnai tokens", "number": 5546, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5546"}
{"title": "Abstract the ScmPullRequestService", "number": 5547, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547", "body": "Allows the PR ingest sync and event jobs to work with an abstract SCM."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#pullrequestreview-1372118902", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#pullrequestreview-1372121375", "body": ""}
{"comment": {"body": "@davidkwlam still need to do this one.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#discussion_r1157998352"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#pullrequestreview-1373262330", "body": ""}
{"comment": {"body": "yup, that's the main thing I'm tackling this morning", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#discussion_r1158736728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#pullrequestreview-1373362333", "body": ""}
{"comment": {"body": "Is this just here as a safeguard? The flow should terminate once it comes across a PR where `batch.items.any { it.updatedAt > since }` is false, so this check is not strictly necessary?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#discussion_r1158804712"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#pullrequestreview-1373369411", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#pullrequestreview-1373370952", "body": ""}
{"comment": {"body": "Ah it'll terminate but the page may still contain PRs where updated < since \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#discussion_r1158810454"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#pullrequestreview-1373474404", "body": ""}
{"comment": {"body": "exactly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5547#discussion_r1158877302"}}
{"title": "Remove deprecated class", "number": 5548, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5548"}
{"title": "JetBrains gutter icon popup", "number": 5549, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549", "body": "When you click on a gutter icon, we now immediately open the thread (if there is one thread on the line), or display a popover (if there are multiple threads on the line).  Most of the PR is for the popover.  Lots of issues for very little code:\n\nThe popover had to be written in Swing.  I tried building it using HTML but the popover never sized to content correctly.  This might be something to investigate in the future.\nThere's no built in component that async loads images, so I made one.  Ended up adding ktor client for this, might not be worth it if it brings in a lot of dependencies?\nBuild some date utils mimicking our TS helpers\nThe Swing layout components are horribly broken.  I settled on the GridBagLayout as it seemed to be the best-behaved.  If we build anything more complicated then this we should look at alternatives to using raw Swing, as this took ages to get working reliably, and it's still kinda broken.\n\nAlso fixes \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373376261", "body": ""}
{"comment": {"body": "This is the same as `DateTime.recentRelative` in TS.  I tried to write a unit test for this but mocking out all the TZ crap was a hassle.  I'll add it in a future PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1158814211"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373379775", "body": ""}
{"comment": {"body": "This `ImageUtil` method is a helper built into IntelliJ to handle rendering retina images correctly.  If you use the built in Swing methods and components, everything renders non-retina \ud83e\udd2a ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1158816613"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373394629", "body": ""}
{"comment": {"body": "Changes in this file ensure that editor tracking happens before indexing completes, and ensures that the initially visible editors have correct SM tracking, solves https://linear.app/unblocked/issue/UNB-1068/populate-insight-window-and-gutter-icons-on-load", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1158827036"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373681371", "body": ""}
{"comment": {"body": "Oh my... I can see why this was frustrating...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159014430"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373683354", "body": ""}
{"comment": {"body": "As a starting point, here's what gpt gave me :)\r\n\r\n`testImplementation \"io.mockk:mockk:1.12.0\"`\r\n\r\n```\r\npackage com.nextchaptersoftware.jetbrains\r\n\r\nimport io.mockk.every\r\nimport io.mockk.mockkObject\r\nimport io.mockk.unmockkAll\r\nimport kotlinx.datetime.*\r\nimport kotlin.test.AfterTest\r\nimport kotlin.test.BeforeTest\r\nimport kotlin.test.Test\r\nimport kotlin.test.assertEquals\r\n\r\nclass InstantExtensionTest {\r\n    private val fixedInstant = Instant.fromEpochMilliseconds(1649193754000) // 2023-04-05T10:49:14Z\r\n    private val fixedTimeZone = TimeZone.of(\"UTC\")\r\n\r\n    @BeforeTest\r\n    fun setup() {\r\n        mockkObject(Clock.System)\r\n        every { Clock.System.now() } returns fixedInstant\r\n\r\n        mockkObject(TimeZone)\r\n        every { TimeZone.currentSystemDefault() } returns fixedTimeZone\r\n    }\r\n\r\n    @AfterTest\r\n    fun tearDown() {\r\n        unmockkAll()\r\n    }\r\n\r\n    @Test\r\n    fun `test a few seconds ago`() {\r\n        val instant = fixedInstant\r\n        val result = instant.toAgoString()\r\n        assertEquals(\"a few seconds ago\", result)\r\n    }\r\n\r\n    @Test\r\n    fun `test a minute ago`() {\r\n        val instant = fixedInstant.minus(1.minutes)\r\n        val result = instant.toAgoString()\r\n        assertEquals(\"a minute ago\", result)\r\n    }\r\n\r\n    @Test\r\n    fun `test minutes ago`() {\r\n        val instant = fixedInstant.minus(10.minutes)\r\n        val result = instant.toAgoString()\r\n        assertEquals(\"10 minutes ago\", result)\r\n    }\r\n\r\n    @Test\r\n    fun `test an hour ago`() {\r\n        val instant = fixedInstant.minus(1.hours)\r\n        val result = instant.toAgoString()\r\n        assertEquals(\"an hour ago\", result)\r\n    }\r\n\r\n    @Test\r\n    fun `test hours ago`() {\r\n        val instant = fixedInstant.minus(5.hours)\r\n        val result = instant.toAgoString()\r\n        assertEquals(\"5 hours ago\", result)\r\n    }\r\n\r\n    @Test\r\n    fun `test yesterday`() {\r\n        val instant = fixedInstant.minus(1.days).toLocalDateTime(fixedTimeZone).atStartOfDayIn(fixedTimeZone).toInstant()\r\n        val result = instant.toAgoString()\r\n        assertEquals(\"yesterday\", result)\r\n    }\r\n\r\n    @Test\r\n    fun `test same year different month`() {\r\n        val instant = fixedInstant.minus(60.days).toLocalDateTime(fixedTimeZone).atStartOfDayIn(fixedTimeZone).toInstant()\r\n        val expected = instant.toLocalDateTime(fixedTimeZone).date.toJavaLocalDate().format(MonthDayPattern)\r\n        val result = instant.toAgoString()\r\n        assertEquals(expected, result)\r\n    }\r\n\r\n    @Test\r\n    fun `test different year`() {\r\n        val instant = fixedInstant.minus(400.days).toLocalDateTime(fixedTimeZone).atStartOfDayIn(fixedTimeZone).toInstant()\r\n        val expected = instant.toLocalDateTime(fixedTimeZone).date.toJavaLocalDate().format(MonthYearPattern)\r\n        val result = instant.toA\r\n\r\n```\r\n\r\n... I think it gave up at the end as it was getting too long.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159015720"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373683701", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373735083", "body": ""}
{"comment": {"body": "I bet you spent no time at all figuring this out \ud83d\ude05", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159051162"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373736532", "body": ""}
{"comment": {"body": "I had to try a couple dozen different combinations of Icons, Images, and ImageIcon classes (IntelliJ has a bunch of subclasses of each, but none are documented).  This combination happens to work, though I couldn't tell you why.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159052209"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373760350", "body": ""}
{"comment": {"body": "We should use argument labels when calling these functions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159069684"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373763243", "body": ""}
{"comment": {"body": "Any particular reason why?  `add` is pretty self-evident to me, the arguments are clearly typed so there's not any ambiguity?   Maybe I'm misreading this...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159071486"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373764257", "body": ""}
{"comment": {"body": "There's an `addAll` variant that takes two constraint arguments. Not clear just looking at this code which one gets used", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159072099"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373764734", "body": ""}
{"comment": {"body": "Ah.  `addAll` is my own extension.  `add` is used for one component, `addAll` for a collection of components...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159072410"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373765733", "body": ""}
{"comment": {"body": "All good - this is a nit", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159073155"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373767151", "body": ""}
{"comment": {"body": "No problem -- if this isn't easily readable/understandable then we probably should change either the API or explicitly add labels", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159074069"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#pullrequestreview-1373768900", "body": ""}
{"comment": {"body": "I'd vote for labels. API makes sense to me, we just need to explain what the arguments are used for", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5549#discussion_r1159075379"}}
{"title": "Add canonical route to ktor app logging context", "number": 555, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/555"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/555#pullrequestreview-906781175", "body": ""}
{"title": "Scan for secrets on pull requests", "number": 5550, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5550", "body": "Uses GitGuardian to scan for secrets. This is a free service for teams < 25 engineers."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5550#pullrequestreview-1373498510", "body": "This is awesome..."}
{"title": "Add recommended team members invite toast", "number": 5551, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5551", "body": ""}
{"comment": {"body": "Holding off on merging this until https://github.com/NextChapterSoftware/unblocked/pull/5611 is added and integrated.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5551#issuecomment-1504107733"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5551#pullrequestreview-1373400955", "body": ""}
{"comment": {"body": "see: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1680641366393089", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5551#discussion_r1158831288"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5551#pullrequestreview-1373468035", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5551#pullrequestreview-1373782742", "body": ""}
{"comment": {"body": "test @davidkwlam \n\n\n\n```\nRendering is the process of taking data\nand displaying it in a visual format, \nsuch as on a webpage or in a mobile \napplication. This process involves \ntaking data from a source, such as a \ndatabase, and transforming it into a \nformat that can be displayed on a screen. \nRendering can involve a variety of \ntechniques, such as code styling, data \nloading, and webview rendering. Rendering \nalso involves creating elements, such as \ndropdown menus and anchor elements, and \ntypecasting the matching props into the \nelements.\n```\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5551#discussion_r1159085677"}}
{"title": "Removes cmd-shift-k install from customer builds", "number": 5552, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5552"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5552#pullrequestreview-1373596864", "body": ""}
{"title": "IntelliJ Promise Proxy Service", "number": 5553, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553", "body": "Setup a promise proxy service from Agent -> IntelliJ\nDue to limitations with how our grpc is setup, we are unable to have promises that originate from the Agent to the Extension.\nUsing a two way directional stream, create a Promise Proxy which allows the agent to make promise-like requests to the agent.\nThe first use-case is to introduce a StorageProxy which allows the client to store arbitrary string values in IntelliJ. Moved token storage from keychain to this new storage system."}
{"comment": {"body": "The PromiseProxy is a service we introduced for both web & web-extension. It defines a Promise-based proxy system that allows a client and a service to communicate with each other by sending and receiving messages through a transport layer.\r\n\r\n The core classes PromiseProxyClient and PromiseProxyService provide the functionality to register, send, and receive messages in the form of Promises, which simplifies asynchronous communication between the client and service. The code also includes a PromiseProxyTransport interface to define the actual mechanism for sending data over the transport layer, which allows users to plug in their own transport implementations. \r\n\r\nMessages are sent as JSON strings, and the code includes error handling and optional timeouts for message handling. Additionally, it leverages the uuid library to generate unique identifiers for messages, enabling proper tracking and resolution of pending requests.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#issuecomment-1498196614"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373651783", "body": ""}
{"comment": {"body": "I think there's likely to be a more condense way to type this class but was unable to do so... Was fighting a lot with kotlinX.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1158999247"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373653961", "body": ""}
{"comment": {"body": "Would have used a StateFlow but `A slow collector skips fast updates, but always collects the most recently emitted value.` \r\nThis means if a large amount of events were emitted in a short timeframe, events may be dropped...\r\n\r\nUsing SharedFlow with an arbitrarily large buffer to bypass the issue.\r\nhttps://kotlinlang.org/api/kotlinx.coroutines/kotlinx-coroutines-core/kotlinx.coroutines.flow/-state-flow/", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159000757"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373731036", "body": ""}
{"comment": {"body": "Updated Notification to use new Proxy .", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159048358"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373740540", "body": ""}
{"comment": {"body": "Requests / Responses passed through the PromiseProxy should be defined in extra.yml", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159055909"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373742091", "body": ""}
{"comment": {"body": "No longer depend on IntelliJ to push tokens to agent.\r\n\r\nSince we now have can have promises from agent -> extension, block on get requests.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159057087"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373760661", "body": ""}
{"comment": {"body": "We were probably already transitively pulling these dependencies in from our API model dependency, I'm guessing we need this here so we can directly use this for the proxy model serialization?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159070013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373761950", "body": ""}
{"comment": {"body": "Yeah. Needed to explicitly pull this in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159070770"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373776310", "body": ""}
{"comment": {"body": "Do we expect that this is how we will run all agent -> extension commands?  ie should `openFile` also run through this proxy?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159080686"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373777714", "body": ""}
{"comment": {"body": "I don't see this alias being used anywhere?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159081743"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373777824", "body": ""}
{"comment": {"body": "Same here -- do we remove these?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159081825"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373780987", "body": ""}
{"comment": {"body": "I wonder if `runFn` should be a coroutine?  That would allow us to run async operations easily...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159084481"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373790758", "body": ""}
{"comment": {"body": "So the preferred way to do this is to use `runCatching { ... } .onFailure { logStuff }`.  That won't trigger the detekt error", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159091914"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373791419", "body": ""}
{"comment": {"body": "Multiple nested try/catch is hard to parse too -- one catch clause should be sufficient here...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159092510"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373793646", "body": ""}
{"comment": {"body": "The seems wrong?  Errors should be returned (like we're doing in handleError below)?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159093913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373793992", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373802300", "body": ""}
{"comment": {"body": "Polymorphic serialization with kotlinx is a bit gross so no shortcuts here I don't think", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#discussion_r1159101336"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5553#pullrequestreview-1373822998"}
{"title": "Move libraries around", "number": 5554, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5554"}
{"title": "Try again", "number": 5555, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5555"}
{"title": "Cleanup etag column", "number": 5556, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5556"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5556#pullrequestreview-1373765536", "body": ""}
{"title": "Fix min OS version check in installer", "number": 5557, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5557"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5557#pullrequestreview-1373800624", "body": ""}
{"title": "[Bug Fix] Move bot archiving logic to outside of the transaction", "number": 5558, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5558", "body": "This was preventing bot threads from being archived (since the logic was running before the thread was actually committed). I've added unit tests to show this is working now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5558#pullrequestreview-1373828075", "body": ""}
{"title": "Add ability to invoke openai from services", "number": 5559, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5559"}
{"title": "Background upstream API requests", "number": 556, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/556", "body": "Much faster in DEV:\n21:27:25 | INFO  | c.n.s.SourceMarkCalculator: recalculated 331 of 331 SourceMarks in this batch\n21:27:25 | INFO  | c.n.s.SourceMarkCalculator: elapsed time: 1m 14.292s"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/556#pullrequestreview-906780880", "body": ""}
{"title": "Add apple-app-site-assocation file to ", "number": 5560, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560"}
{"comment": {"body": "After some more research I discovered that Universal Links are a Safari only technology, so closing the door on this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#issuecomment-1499598527"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#pullrequestreview-1375180552", "body": ""}
{"comment": {"body": "Maybe prefix the path with the environment?  `/dev/finish-install` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#discussion_r1159998266"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#pullrequestreview-1375180701", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#pullrequestreview-1375184377", "body": ""}
{"comment": {"body": "do these need to be prefixed by `dashboard`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#discussion_r1160000828"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#pullrequestreview-1375188621", "body": ""}
{"comment": {"body": "Hmm good question.  If it's on `/dashboard` that gives us the ability for the dashboard to handle this as a fallback... not sure if that's what we actually want or not", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#discussion_r1160003786"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#pullrequestreview-1375296615", "body": ""}
{"comment": {"body": "Why is `./dist/landing/.well-known/` repeated ? If you look at the previous command we are making two different directories. You can just add `./dist/landing/.well-known/` to the end of that. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#discussion_r1160074291"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#pullrequestreview-1375297718", "body": "Approved but I made a minor comment. There seem to be a duplicate directory path."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#pullrequestreview-1375495508", "body": ""}
{"comment": {"body": "We can do pretty much anything here - what is best?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#discussion_r1160205356"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#pullrequestreview-1375496519", "body": ""}
{"comment": {"body": "Oops sorry this is my bad. Copy and paste error", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5560#discussion_r1160205977"}}
{"title": "Update", "number": 5561, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5561"}
{"title": "Add associated domains entitlement to app", "number": 5562, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5562"}
{"comment": {"body": "After some more research I discovered that Universal Links are a Safari only technology, so closing the door on this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5562#issuecomment-1499598782"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5562#pullrequestreview-1375184198", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5562#pullrequestreview-1375185677", "body": ""}
{"comment": {"body": "Hmm so this means both dev and prod apps will handle *both* dev and prod domains?  We can't make it so dev hubs only handle the dev domain, etc, I'm guessing?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5562#discussion_r1160001728"}}
{"title": "Capture universal link callback", "number": 5563, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5563"}
{"comment": {"body": "After some more research I discovered that Universal Links are a Safari only technology, so closing the door on this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5563#issuecomment-1499599045"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5563#pullrequestreview-1375528579", "body": ""}
{"title": "Reenable tests", "number": 5564, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5564"}
{"title": "Add CSP rule for intercomcnd", "number": 5565, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5565"}
{"title": "Archive low relevance threads for team member", "number": 5566, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5566"}
{"title": "Fix local stack init", "number": 5567, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5567", "body": "Move to location recommended for local stack hooks\n"}
{"title": "Archive low relevance threads for all PRs", "number": 5568, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5568", "body": "Before we we're only doing it for merged PRs, but really we should do for all."}
{"title": "Admin: Raw SCM API responses are abstract", "number": 5569, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5569", "body": "remove github APIs\nuse abstract SCM APIs"}
{"title": "SourcePoints now use batch upload", "number": 557, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/557", "body": "Way fewer API requests.\n22:01:58 | INFO  | c.n.s.SourceMarkCalculator: recalculated 331 of 331 SourceMarks in this batch\n22:01:58 | INFO  | c.n.s.SourceMarkCalculator: elapsed time: 1m 7.687s"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/557#pullrequestreview-906797047", "body": ""}
{"title": "Hack abstract SCM threads into existance by ignoring SourceMark fields", "number": 5570, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5570", "body": "Now have PR code-level threads for GitLab and Bitbucket.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5570#pullrequestreview-1375362982", "body": ""}
{"comment": {"body": "Is the comment above still valid?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5570#discussion_r1160117318"}}
{"comment": {"body": "`strict`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5570#discussion_r1160117584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5570#pullrequestreview-1375387845", "body": ""}
{"comment": {"body": "yes, unfortunately ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5570#discussion_r1160133421"}}
{"title": "MakeDeploymentResilient", "number": 5571, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5571", "body": "make deployment resilient\nDeply"}
{"title": "Update theme on IDE change", "number": 5572, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5572", "body": "When IntelliJ changes appearance, update webview theme.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5572#pullrequestreview-1384246734", "body": ""}
{"comment": {"body": "I think this might need to be deregistered when webviewController shuts down?  If we don't, UIManager will keep a reference back to this class and keep it alive forever...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5572#discussion_r1166045684"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5572#pullrequestreview-1384247096", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5572#pullrequestreview-1384472481", "body": ""}
{"comment": {"body": "Updated so we deregister on dispose.\r\n\r\nBut I don't think we ever have a situation where the webview controller actually \"shuts\" down? None of our tool windows are removed atm.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5572#discussion_r1166184082"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5572#pullrequestreview-1385766402", "body": ""}
{"comment": {"body": "That might be true -- I do think your change didn't actually make any difference though, because we're not calling the new Dispose method?  Dispose isn't automatically called, except for services (because IntelliJ calls it)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5572#discussion_r1167038344"}}
{"title": "scale up pusher because it's a high impact service", "number": 5573, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5573", "body": "When pusher is having trouble dashboard doesn't load. This makes it a high impact service. We just an issue with the pusher deployment and it ended up taking both instances down. \nAdding more pods should make the rollover more gradual."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5573#pullrequestreview-1375563023", "body": ""}
{"title": "Check recommended topics for equality", "number": 5574, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5574"}
{"title": "Add custom URL scheme for hub", "number": 5575, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5575", "body": "Scheme looks like this:\nunblocked://exchange?code=single-use-code"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5575#pullrequestreview-1375606149", "body": ""}
{"title": "Revert \"Make helm resilient (#5571)\"", "number": 5576, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5576", "body": "This reverts commit afdb98c86a2e98fe8776d56478ca21c9eb781e28."}
{"title": "Don't retry channel poll requests", "number": 5577, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5577", "body": "Don't retry channel poll requests.  The channel poller naturally retries every second, and blocking /modifiedSince will cause other calls participating in channel polling.\nThis is a very quick fix.  I don't love how we centralize the logic here in BaseAPI, it means testing and mocking this is very challenging.  I think this is an area we should refactor at some point.\nBackground: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5577#pullrequestreview-1375606316", "body": ""}
{"title": "Add single use code generate/exchange endpoint", "number": 5578, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578", "body": "Implementation to follow"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578#pullrequestreview-1377846485", "body": ""}
{"comment": {"body": "Isn't our existing exchange token single use?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578#discussion_r1161907179"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578#pullrequestreview-1377847343", "body": ""}
{"comment": {"body": "Why can't we ureuse the existing `/login/exchange` endpoint for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578#discussion_r1161907951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578#pullrequestreview-1379707588", "body": ""}
{"comment": {"body": "Nope", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578#discussion_r1163089166"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578#pullrequestreview-1379709716", "body": ""}
{"comment": {"body": "Think of the exchange token as the most sensitive token. It's re-usable, and it's long-lived. It's not suitable for this use-case.\r\n\r\nOne other difference: exchange token has scope and is generally a higher scoped token than the auth token. We haven't really got into scoping or \"duplicating\" the exchange token for the exchange code in this PR (we're just creating a new one), but we could.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578#discussion_r1163090639"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5578#pullrequestreview-1379846697", "body": ""}
{"title": "Add topics to TeamMemberView", "number": 5579, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579", "body": "Add filter by topics dropdown to Team Members view\n\n\n\nAdd team member side menu with expertise (note: this will eventually include the user ML description)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#pullrequestreview-1377838710", "body": ""}
{"comment": {"body": "You can remove this `useEffect` by adding the processing to the stream above:\r\n\r\n```\r\nconst topics = useStream(\r\n    () => TopicStreams.getTopicsForTeamMember(...).compose(filterReady).map(topicData => topicData.topics)\r\n)\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#discussion_r1161902285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#pullrequestreview-1377840443", "body": ""}
{"comment": {"body": "Same here -- can get rid of useEffect by processing in the stream hook", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#discussion_r1161903322"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#pullrequestreview-1377841207", "body": ""}
{"comment": {"body": "Not a today issue but IMO, not something we should be doing on the client.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#discussion_r1161903783"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#pullrequestreview-1377844033", "body": ""}
{"comment": {"body": "Same here:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/5579/files#r1161902285", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#discussion_r1161905598"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#pullrequestreview-1377844333", "body": ""}
{"comment": {"body": "I think this is probably fine -- the actual mapping (ie the logic that defines who is an expert in what) is defined by the service/ML logic, all we're doing here is aggregating between the different data sources...  the point where we would want to change this is probably the point where we want to not download and cache all the user data (ie once we support teams with thousands of users)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#discussion_r1161905835"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#pullrequestreview-1377844845", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#pullrequestreview-1377846111", "body": ""}
{"comment": {"body": "We started work to add an [API](https://github.com/NextChapterSoftware/unblocked/pull/5539) for this but Matt suggested this approach instead since we have everything in the clients right now. I don't feel super strongly either way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#discussion_r1161907033"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#pullrequestreview-1378261081", "body": ""}
{"comment": {"body": "Merging this in for now, will address these comments once the topic push channel is merged/fixed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5579#discussion_r1162174622"}}
{"title": "Update Refresh Logic", "number": 558, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558", "body": "Based on conversations from \nPreviously, we were refreshing token after a 401 occurred.\nUpdated to refresh token before a 401 with some wiggle room. Currently 5 minute wiggle room."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558#pullrequestreview-907870896", "body": ""}
{"comment": {"body": "Is this effectively a whitelist of API requests we expect to allow to work unauthed?  It's unfortunate that there's no way to query the `security` properties for the API call here.\r\n\r\nMight want to make this clear by putting this in a set or array called `noAuthURLs` or something...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558#discussion_r825178564"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558#pullrequestreview-907872300", "body": ""}
{"comment": {"body": "OH never mind, I get it now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558#discussion_r825179639"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558#pullrequestreview-907874134", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558#pullrequestreview-907875096", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558#pullrequestreview-907876028", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558#pullrequestreview-907876350", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/558#pullrequestreview-907876447", "body": ""}
{"title": "Derive original sourcepoints from GitLab version diffs", "number": 5580, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5580", "body": "Every time a commit is pushed to a GitLab merge request a new version of the\ncommits and diffs in the MR are recorded. The latest version is most interesting\nto us, since the commits in the latest version are the only commits that will be\npersisted in Git, on merge.\nSee also:\n - \n - "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5580#pullrequestreview-1375672463", "body": ""}
{"comment": {"body": "I'm guessing this is ordered from latest to oldest? Worth adding a sort order just to be safe?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5580#discussion_r1160325027"}}
{"comment": {"body": "Worth adding a test for this logic? \ud83e\udd17 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5580#discussion_r1160327105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5580#pullrequestreview-1375676066", "body": ""}
{"comment": {"body": "ya, in progress", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5580#discussion_r1160327817"}}
{"title": "Exchange code implementation", "number": 5581, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5581"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5581#pullrequestreview-1377854066", "body": ""}
{"comment": {"body": "Is there a way to differentiate between invalid code & expired code?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5581#discussion_r1161912424"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5581#pullrequestreview-1377854861", "body": ""}
{"title": "Pinecone and Milvus Vector DB clients", "number": 5582, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5582"}
{"title": "Upgrade Jackson to fix ", "number": 5583, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5583", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5583#pullrequestreview-1376483082", "body": ""}
{"title": "Update pull request ingestion to support creating top-level comment threads", "number": 5584, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5584", "body": "This only enables creating top-level comment threads for Bitbucket and GitLab. \nFor GitHub, we'll need to convert the existing top-level comments stored as PullRequestCommentModel into threads (i.e. a migration) before creating threads from GitHub top-level comments in the PullRequestIngestionService. That will come in another PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5584#pullrequestreview-1379898689", "body": ""}
{"title": "Try refactoring", "number": 5585, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5585"}
{"title": "Use topic push channel in TS clients", "number": 5586, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5586", "body": "The shape of the stream output changed slightly, so a lot of files had to change trivially."}
{"title": "Add S3 image asset buckets to CSP for upload and connect-src data loading", "number": 5587, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5587"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5587#pullrequestreview-1378042163", "body": ""}
{"title": "Return last modified header for getTopics operation", "number": 5588, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5588", "body": "For the new topics push channel"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5588#pullrequestreview-1378072721", "body": "LGTM"}
{"title": "Diff hunk parser on server to process raw diffs as snippets from SCM", "number": 5589, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5589"}
{"comment": {"body": "> Will this deprecate `ScmPrComment.LineLevel.sourceSnippet` or would this be a preprocessor?\r\n\r\n- The `DiffHunkParser` added here will be used directly for Bitbucket and GItLab, because each of those SCMs returns a full Git diff.\r\n- GitHub does some pre-processing on the diff (not sure why exactly), but hopefully I can use `DiffHunkParser` in ScmPrComment.LineLevel.sourceSnippet too. Will not completely replace the logic though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5589#issuecomment-1502320689"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5589#pullrequestreview-1378103214", "body": "Will this deprecate ScmPrComment.LineLevel.sourceSnippet or would this be a preprocessor?"}
{"title": "Wire up more CLI options", "number": 559, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/559"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/559#pullrequestreview-906828836", "body": ""}
{"title": "Extract code snippets for Bitbucket code comments", "number": 5590, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5590"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5590#pullrequestreview-1380112351", "body": ""}
{"title": "Add topic summaries", "number": 5591, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5591"}
{"title": "Jetbrains: capture editor events for SM processing", "number": 5592, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5592", "body": "Capture edit and save events in Jetbrains IDEs, so that as users edit and save files, the sourcemarks update.\n\nAdd a new API to the agent so the IDE can notify the agent when edits and saves happen\nThe existing agent machinery causes sourcemarks to update\n\nThere is a problem with in-memory updates where the sourcemarks are not reliably updating -- as far as I can tell the correct file content is being written.  Might need Richie to look into this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5592#pullrequestreview-1379841672", "body": ""}
{"comment": {"body": "Would consider increasing the buffer capacity as we don't want to drop any events here...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5592#discussion_r1163175257"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5592#pullrequestreview-1379842169", "body": ""}
{"comment": {"body": "Or are we okay with dropping events as long as the latest value is correct?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5592#discussion_r1163175606"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5592#pullrequestreview-1379844110", "body": ""}
{"comment": {"body": "Yes.  This is a trigger (the type of the flow is Unit), and the values are debounced, so we only need a final value to tell us when editing is \"complete\".  So I think this is fine as-is.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5592#discussion_r1163176915"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5592#pullrequestreview-1379846070", "body": ""}
{"title": "Use DiffHunkParser for GitHub snippets", "number": 5593, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5593", "body": "Related to https://github.com/NextChapterSoftware/unblocked/pull/5589#issuecomment-1502320689\nLot's of changes here, but I have a good understanding now of what GitHub is doing, and added some tests to validate. There are some result differences relative to the previous implementation, but in all cases that I could find these are improvements in accuracy."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5593#pullrequestreview-1378395117", "body": ""}
{"comment": {"body": "bug fix - my bad", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5593#discussion_r1162262040"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5593#pullrequestreview-1380104664", "body": ""}
{"title": "Standardize search indexing context for threads / pull requests", "number": 5594, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5594", "body": "We should be using standardized models for search / embeddings for text."}
{"title": "[unb-1136] Open unresolved file", "number": 5595, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595", "body": "Add general support for opening unresolved git file.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378163605", "body": ""}
{"comment": {"body": "[These error cases should bring up](https://linear.app/unblocked/issue/UNB-1137/source-mark-resolution-failure-fault-ui)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162107448"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378242700", "body": ""}
{"comment": {"body": "This is going to wreck my poor 16GB machine... not sure what to do here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162161811"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378242951", "body": ""}
{"comment": {"body": "Not sure why this is duplicated?  We add git4idea above?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162161958"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378243335", "body": ""}
{"comment": {"body": "?? We probably should investigate this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162162212"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378249069", "body": ""}
{"comment": {"body": "What does this do?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162166410"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378252176", "body": ""}
{"comment": {"body": "I think the request should include the git root, as the agent will know this, and this way we don't need to try to deduce it here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162167973"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378252343", "body": ""}
{"comment": {"body": "Just added a screenshot. This is how banners for editors are injected.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162168077"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378254690", "body": ""}
{"comment": {"body": "Ah interesting.\r\n\r\nI wasn't expecting this to be done through some notification API, was expecting something more like `Editor.setHeaderComponent`, but this makes sense and might conflict less with other things.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162169780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378255345", "body": ""}
{"comment": {"body": "This is less of a git service in general, than a git VFS service, right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162170289"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378256073", "body": ""}
{"comment": {"body": "Yup. Am looking into this right now... Something about the JSON being cut off and therefore invalid.\r\n\r\nIt's not a regression of this PR through. Tested it out on main.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162170805"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378256428", "body": ""}
{"comment": {"body": "That was my original thought going into this... Much more overhead to do this but I guess it may scale better.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162171067"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378258917", "body": ""}
{"comment": {"body": "Is there a non-deprecated API we can implement here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162172957"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378259189", "body": ""}
{"comment": {"body": "`unblocked-git` ?  `git` is a legit url scheme...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162173189"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378261417", "body": ""}
{"comment": {"body": "Are there restrictions on the encoded string for this?  Can we just encode this into a JSON object or something, instead of all this manual separator and encoding/decoding?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162174858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378261821", "body": ""}
{"comment": {"body": "Ah never mind that probably doesn't make much sense....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162175196"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378263054", "body": ""}
{"comment": {"body": "These functions could probably just be moved into `UnresolvedFileService`?  This doesn't hold any state, it probably doesn't make much sense as a project service?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162176104"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378264169", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378266401", "body": ""}
{"comment": {"body": "This encoding is actually used for the path which is used by the file system to store the temp virtual file. So it needs to output a String.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162178441"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#pullrequestreview-1378274882", "body": ""}
{"comment": {"body": "Currently setup as project service as the VirtualFile needs a reference to the Project itself. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5595#discussion_r1162183885"}}
{"title": "[VERY WIP] Update the topics push channel to take the same query parameters as the getTopics operation", "number": 5596, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5596", "body": "So that the response is symmetric"}
{"title": "Filter discussion participants by tab", "number": 5597, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5597", "body": "In the IDE, we were showing all the participants of a given PR, resulting in possible dupes (re: identity problem). Instead, filter the participants for the given tab type (we already do this in dashboard)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5597#pullrequestreview-1379988031", "body": ""}
{"title": "Add search insight embeddings", "number": 5598, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5598", "body": "This now allows us for to store all of our embeddings in our vector database.\nWill have to figure out how we want to namespace the data.\nWill enable this in dev/prod soon."}
{"title": "Disable powerml", "number": 5599, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5599"}
{"title": "Refactor GitHub workflows", "number": 56, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56", "body": "Changes\n\nsplits the Web and VSCode workflows, which are different\nunsplits the main and PR workflows, which should be near identical\n\nMotivation\n\nlots of CI failures on main: \nConfusingly named duplicate workflows: \n\n"}
{"comment": {"body": "Interesting to note that *this* PR has no CI runs.  Does it make sense to set all the workflows to run on a change in `/.github/workflows` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#issuecomment-1015638119"}}
{"comment": {"body": "> Interesting to note that _this_ PR has no CI runs. Does it make sense to set all the workflows to run on a change in `/.github/workflows` ?\r\n\r\n@matthewjamesadam Done. It'll work going forward, but does not take effect on this PR, because the default branch workflows are used.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#issuecomment-1015675389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855047674", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855747125", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855754225", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855773957", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855797039", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855811893", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855829785", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855836865", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855837087", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855846238", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855846604", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855850488", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/56#pullrequestreview-855868955", "body": ""}
{"title": "Remove putSourcePoint operation", "number": 560, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/560", "body": "No longer used. We can use the putSourcePoints operation to do the same thing."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/560#pullrequestreview-907463768", "body": ""}
{"title": "Fix openai prompts for casing", "number": 5600, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5600", "body": "Add fewshot case examples"}
{"title": "Admin: Button to reingest a PR", "number": 5601, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5601"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5601#pullrequestreview-**********", "body": ""}
{"title": "GitHub unsuspend event should trigger an idempotent installation handler", "number": 5602, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5602"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5602#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5602#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think the `suspend` event should set `TeamModel.providerExternalInstallationId` to `null`. But I need to confirm so will do in another change.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5602#discussion_r1162264275"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5602#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Is that the only thing? I'm wondering if we need to introduce a `TeamState` value, possibly with an affordance for \"Pending Approval\", \"Processing\", etc?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5602#discussion_r1162265389"}}
{"title": "Sourcemark engine handles short SHAs", "number": 5603, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5603"}
{"title": "Bitbucket connect page shows Workspaces where user is an Owner", "number": 5604, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5604", "body": "We were showing all workspaces for a user, whether they were an owner,\ncollaborator, or member. The problem is that only owners can list\nworkspace members and list repos and install webhooks, so everything breaks\nunless the user is an owner.\nThis change makes it impossible to onboard a Bitbucket Workspace without the\nfirst user being an owner."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5604#pullrequestreview-1379674758", "body": ""}
{"title": "this should take care of setuid false positives in dev", "number": 5605, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5605", "body": "Modified the rule to deal with this noisy false positive alarm:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5605#pullrequestreview-1380007389", "body": ""}
{"title": "Fix VSCode debugging breakpoints", "number": 5606, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5606", "body": "Now comparing DEV and LOCAL:\ndiff vscode/webpack.{dev,local}.js\nShows that they differ only where expected:\n```\n16c16\n<                 ENVIRONMENT: JSON.stringify('dev'),\n\n\n            ENVIRONMENT: JSON.stringify('local'),\n\n20d19\n<\n29c28\n<                 ENVIRONMENT: JSON.stringify('dev'),\n\n\n\n            ENVIRONMENT: JSON.stringify('local'),\n\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5606#pullrequestreview-1379736334", "body": ""}
{"title": "Add Hub restart resilience to installer in case macOS freaks out", "number": 5607, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5607"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5607#pullrequestreview-1380046703", "body": "Cool, thanks"}
{"comment": {"body": "Do these errors ship to Logz?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5607#discussion_r1163306260"}}
{"title": "Move PR aggregate types into OpenAPI YML extras", "number": 5608, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5608", "body": "Allows these types to be used in kotlin."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5608#pullrequestreview-1379985043", "body": ""}
{"title": "Sample topic data better", "number": 5609, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5609", "body": "We can't inordinately sample 60000 documents.\nWe need to have a max cap of documents to process."}
{"title": "Fix DiscussionThread Story", "number": 561, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/561", "body": "Fixes DiscussionThread Story. Cyclical import with the index.ts\nSlight updates to thread view UI. Mainly moving send button to right."}
{"comment": {"body": "can you pull main? there are some fixes I just merged in that address some of the same things", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/561#issuecomment-1065495864"}}
{"comment": {"body": "> can you pull main? there are some fixes I just merged in that address some of the same things\r\n\r\nPulled but didn't really notice any differences?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/561#issuecomment-1065577431"}}
{"comment": {"body": "@jeffrey-ng \r\n![image](https://user-images.githubusercontent.com/********/*********-b450e14d-bbe5-4368-9db6-8445cef8e15c.png)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/561#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/561#pullrequestreview-*********", "body": ""}
{"title": "Add discussion level invite to Unblocked banner", "number": 5610, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610", "body": "If the discussion has participants that are !hasAccount, show banner to allow affordance to invite them to Unblocked.\n\n"}
{"comment": {"body": "Collapse all message editor inputs by default:\r\nhttps://user-images.githubusercontent.com/********/*********-af614d7a-031d-4cb5-91de-e3ccd2ad7fab.mp4\r\n\r\nAdd invite banner to PR threads:\r\nhttps://user-images.githubusercontent.com/********/*********-5384802d-4e7b-4cd0-9605-1d455bd25df5.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#issuecomment-**********"}}
{"comment": {"body": "vscode:\r\n\r\nhttps://user-images.githubusercontent.com/********/*********-8cb3393f-6d60-4e4f-942c-26e0cdf7e31f.mp4\r\n\r\nhttps://user-images.githubusercontent.com/********/*********-e6ea86da-1d1b-462c-b2b8-5a0fad6b6d12.mp4\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#pullrequestreview-**********", "body": ""}
{"comment": {"body": "So if you want to avoid this kind of thing here, you can do what we did with the other in-webview assets and have them as inline SVG react components.\r\n\r\nI need to figure out a way to fix this...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#discussion_r1169065127"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#pullrequestreview-1388687029", "body": ""}
{"comment": {"body": "Do we typically add this to all the client styles at the same time?  Right now `tight` will only work in VSCode?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#discussion_r1169100806"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#pullrequestreview-1388853447", "body": ""}
{"comment": {"body": "As a side note, and separate from this PR -- having to pass this callback through so many layers makes me wonder if this should be async instead?  Most of these layers could probably be async, then the top-level code would await, and take the resolveCb action itself after.  Possibly we could even have an `openModal` variant that is async as well and resolves with the selected value...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#discussion_r1169209027"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I'm confused -- the label here says we will invite `teamMembersWithoutAccount.length` people, but in the actual code we invite `contributesWithoutAccount`?  Plus the avatar stack is from `teamMembersWithoutAccount` ?\r\n\r\nThis doesn't seem right, I'd assume they should all be `contributesWithoutAccount`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#discussion_r1169212362"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#pullrequestreview-**********", "body": ""}
{"comment": {"body": "OK I think I understand a bit -- teamMemberWithoutAccount have avatars so we have to use that in the avatar stack.  Probably should use `contributesWithoutAccount` in the count label though?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#discussion_r1169215527"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#pullrequestreview-**********", "body": ""}
{"comment": {"body": "You can show this as a popup error in JetBrains -- `SendNotification` (in `AgentNofificationProxy`) will do this.\r\n\r\n@jeffrey-ng this is partly why we should plumb this through IDEWorkspace, so this code can be identical (and refactored) in both IDEs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#discussion_r1169217324"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5610#pullrequestreview-**********", "body": ""}
{"title": "Get recommended team member invites based on your network connections", "number": 5611, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611", "body": "Part of product invite flows:\nWe want to add an affordance for users to invite team members in their recommended network (if they aren't on Unblocked). \n\n\nWhen the user has sent the invites, we want the UI to disappear so there needs to be a flag to make this possible. \n~The end goal is for the flag to reset itself (after some indeterminate amount of time) given that the TeamMember still has users in their network that require inviting. Then the clients will just read off of this flag and render the UI as normal.~\nDashboard: https://github.com/NextChapterSoftware/unblocked/pull/5551"}
{"comment": {"body": "Add a screenshot of the UI that this change will drive and perhaps a few notes about how this flag should be reset after a couple weeks?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#issuecomment-1504077247"}}
{"comment": {"body": "@davidkwlam @pwerry Just chatted with Dennis. He thinks once the invites are sent, we shouldn't show the banner ever again (i.e. we shouldn't pester the inviter to repeat the same action). I think that means we still need this flag but we don't need the second half of figuring out how to flip it back.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#issuecomment-1504279097"}}
{"comment": {"body": "> @davidkwlam @pwerry Just chatted with Dennis. He thinks once the invites are sent, we shouldn't show the banner ever again (i.e. we shouldn't pester the inviter to repeat the same action). I think that means we still need this flag but we don't need the second half of figuring out how to flip it back.\r\n\r\nI see disadvantages to the one off invite approach:\r\n1. What if more team members subsequently join the company and become strongly related to the user?\r\n2. What if the interaction with team members in the company changes over time such that the strongly connected members change?\r\n3. How do you know _when_ to show this type of invite? Concern is that it will be incomplete soon after onboarding as the recommendation are built asynchronously.\r\n\r\n**Alternative proposal**\r\nThe server would continuously vend a list of team members as candidates invitee where:\r\n - they do not have an unblocked account\r\n - the have a strong network connection with the current user\r\n - they have not already been invited or dismissed by you.\r\n\r\nOnce an invite is sent we record the event in a `MemberInvite` table (with columns: {team senderMember inviteeMember eventType} where eventType is one of {invited dismissed}). Still trivial to implement, but addresses the problems above because it is always up to date wrt the current network state and past invites sent.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#issuecomment-**********"}}
{"comment": {"body": "> > @davidkwlam @pwerry Just chatted with Dennis. He thinks once the invites are sent, we shouldn't show the banner ever again (i.e. we shouldn't pester the inviter to repeat the same action). I think that means we still need this flag but we don't need the second half of figuring out how to flip it back.\r\n> \r\n> I see disadvantages to the one off invite approach:\r\n> \r\n> 1. What if more team members subsequently join the company and become strongly related to the user?\r\n> 2. What if the interaction with team members in the company changes over time such that the strongly connected members change?\r\n> 3. How do you know _when_ to show this type of invite? Concern is that it will be incomplete soon after onboarding as the recommendation are built asynchronously.\r\n\r\nTo be clear, what I was saying is that it feels unkind to ask the person who made the invite to be reminded to follow-up with the person they've **already** invited. They've made the \"introductions\" and its up to us to have a compelling set of emails/reminders to the person they invited to convert them into an unblocked user.\r\n\r\nTo your scenarios @richiebres (what does \"_strongly connected_\" mean here?)\r\n1.  This is a net new team member that hasn't been previously invited? Sure, I think that's fine.\r\n2. This is fine too, sounds like a case of (1)\r\n3. We talked about this as well. I'm going to write this up tomorrow, but we already have a bunch of data around end user usage. I think we set a few trip wires (opened a thread in the IDE _x_ times, opened the hub and clicked on a thread _y_ times, created a note/video etc) that we can use to determine if a user would be willing to invite/recommend. \r\n\r\nMy analogy here was: You'd only recommend a restaurant after you've been there and formed an opinion for yourself.. same principle here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#issuecomment-1504688610"}}
{"comment": {"body": "> To your scenarios @richiebres (what does \"_strongly connected_\" mean here?)\r\n\r\n@dennispi Defined through the social comment graph that powers recommendations. For example, we would only recommend invitees for `khalib` if their relevance score was above say 30%.\r\n\r\n<img width=\"1167\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/231362802-4a4ce65f-6d40-4a1b-9604-5624a8d7ba9b.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#issuecomment-1504694055"}}
{"comment": {"body": "> 3\\. We talked about this as well. I'm going to write this up tomorrow, but we already have a bunch of data around end user usage. I think we set a few trip wires (opened a thread in the IDE _x_ times, opened the hub and clicked on a thread _y_ times, created a note/video etc) that we can use to determine if a user would be willing to invite/recommend.\r\n>\r\n> My analogy here was: You'd only recommend a restaurant after you've been there and formed an opinion for yourself.. same principle here.\r\n\r\n@dennispi Agree. I have ideas for an engagement score based on existing activity data that we already record. We'd only show the invite dialog for highly engaged users. See https://www.notion.so/nextchaptersoftware/Engagement-Score-bc769924dc4d4bd08c296d8c0bbd31a0.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#issuecomment-1504699366"}}
{"comment": {"body": "> @dennispi Agree. I have ideas for an engagement score based on existing activity data that we already record. We'd only show the invite dialog for highly engaged users. See https://www.notion.so/nextchaptersoftware/Engagement-Score-bc769924dc4d4bd08c296d8c0bbd31a0.\r\n\r\nThe devil is in the details here (which specific metrics over what timeframe) but yes, I agree, this is what I think we should use to back this feature and some of the email campaigns (cc @rasharab @benedict-jw )", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#issuecomment-1504717396"}}
{"comment": {"body": "Discussed with @kaych and @benedict-jw ...\r\n\r\n### Changes\r\n- Modify as `getTeamMemberInvitees`: now parameterizable by these parameters:\r\n  - `recommendedInvitees: Boolean` // meaning only show members that are strongly connected to me\r\n  - `requireEngagement: Boolean` // meaning only get results when I am an engaged user\r\n- Introduce `sendTeamMemberInvites` which takes a list of these objects:\r\n  - `{ email: String, teamMember: UUID }`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#issuecomment-1513640360"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1380132497", "body": ""}
{"comment": {"body": "I wonder if we should rename this to express the desired client effect, since this is really the service telling the client what to do rather than the client understanding the real backend state:\r\n\r\n`shouldDisplayRecommendedInvites`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1163366103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1380173682", "body": ""}
{"comment": {"body": "The inverse approach here would mean we would need to run a migration on every user to see if they have recommended teammates that require inviting, no? i.e. the flag would need a default value here instead of starting falsey. I'm not opposed to the idea I just want to understand the scope here ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1163394700"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1380183272", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1380185535", "body": ""}
{"comment": {"body": "I think the way the backend resolves the truthiness of the flag is a bit arbitrary. We can either choose to migrate, or not, and I don't think the naming affects that choice.\r\n\r\nThis is really just a naming nit that speaks to the semantics of this flag. For example, they might have previously sent recommended invites, but we might also want to show the recommended invites UI again. Setting `hasSentRecommendedInvites` to false in that context just to show the UI feels a little confusing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1163403001"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-**********", "body": "Looks good."}
{"comment": {"body": "we seem to use camel case by convention\r\n```suggestion\r\n  /teams/{teamId}/members/recommendedForInvite:\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1164496844"}}
{"comment": {"body": "```suggestion\r\n        is actively engaged with the product, the current user is strongly connected to the invitee,\r\n        and the invitee does not already have an Unblocked account.\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1164498690"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-**********", "body": ""}
{"comment": {"body": "@kaych The API is functional in a basic way: it'll return peers that do not have accounts.\r\n\r\nHowever, it is currently limited as explained in these comments.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1167283266"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-**********", "body": ""}
{"comment": {"body": "> TODO take current user product engagement into account;\r\n\r\n@dennispi Do we need this before merging this work in? i.e. without it we will always show the banner if the current user has recommended team members without Unblocked accounts, even if the user has just onboarded. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1169046859"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-**********", "body": ""}
{"comment": {"body": "boolean?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1170526952"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-**********", "body": ""}
{"comment": {"body": "thanks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1170527501"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-**********", "body": ""}
{"comment": {"body": "fixed https://github.com/NextChapterSoftware/unblocked/pull/5611/commits/697b1f5afa30d17bf57fb4cc0029991624efd140", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1170531355"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1390974840", "body": ""}
{"comment": {"body": "@rasharab does this check if the invitee has already received an email invite?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1170596350"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392478021"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392487218", "body": ""}
{"comment": {"body": "I'm guessing this should reference `Email.yml` instead of duplicating it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171605983"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392489294", "body": ""}
{"comment": {"body": "I'm assuming this is the email address, not the email content?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171607813"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392491101", "body": ""}
{"comment": {"body": "@kaych @richiebres I haven't seen the workflow that uses this, who is expected to call this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171609039"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392560390", "body": ""}
{"comment": {"body": "email address", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171646430"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392561692", "body": ""}
{"comment": {"body": "User will have the ability to dismiss an invite. Using the `x` buttons.\r\n\r\n<img width=\"357\" alt=\"dismiss invite\" src=\"https://user-images.githubusercontent.com/1798345/233152597-476b813a-552d-4be5-9409-d70b1224ce0c.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171647660"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392563834", "body": ""}
{"comment": {"body": "OK I get it, thanks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171649654"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392568271", "body": ""}
{"comment": {"body": "done https://github.com/NextChapterSoftware/unblocked/pull/5611/commits/e12bcb30757544e9de01a99361939a4f967e55a0", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171653788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392601918", "body": ""}
{"comment": {"body": "@benedict-jw FYI this is what I had in mind for dismissing invitees \u2191 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171679235"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392603346", "body": ""}
{"comment": {"body": "Do we need the remove at this level? Right after this you have to enter emails right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171680150"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392630098", "body": ""}
{"comment": {"body": "So in order to dismiss, the user needs to click **Send Invites** first?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171696589"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392644744", "body": ""}
{"comment": {"body": "Yes, and on the subsequent screen, we can add the ability to clear the row. Are you trying to dismiss the entire panel, or dismiss certain individuals?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171705686"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392647318", "body": ""}
{"comment": {"body": "FWIW I don't think adding the ability to dismiss needs to be in the first pass. Dennis was ok with shipping this UI without dismissals ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171706932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392647921", "body": ""}
{"comment": {"body": "Fine to add to the API to future proof but we can take some time to figure out the best treatment for it IMO ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171707345"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#pullrequestreview-1392652305", "body": ""}
{"comment": {"body": "Dismiss certain individuals.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5611#discussion_r1171710406"}}
{"title": "Admin: Add PR search and fix PR bugs", "number": 5612, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5612"}
{"title": "[UNB-1071] Setup IntelliJ dialog infrastructure", "number": 5613, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613", "body": "Setup basic dialog infrastructure from Agent -> IntelliJ\nImplemented Archive & Restore dialogs\n"}
{"comment": {"body": "There's a lot we can do with the styling... \r\nWhenever you have some time, we can update the \"standard\" dialog styling. @benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#issuecomment-1504258904"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#pullrequestreview-1380216070", "body": ""}
{"comment": {"body": "Because the promise is waiting for user interaction, we do *not* want to timeout the promise.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#discussion_r1163421686"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#pullrequestreview-1380216843", "body": ""}
{"comment": {"body": "Most basic of UIs. Should update this before release\r\nhttps://linear.app/unblocked/issue/UNB-1160/style-standard-dialogs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#discussion_r1163422271"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#pullrequestreview-1384259947", "body": ""}
{"comment": {"body": "This is effectively duplicating logic between jetbrains and vscode, isn't it?\r\n\r\nIf showDialog() was an API on IDEWorkspace, then this code could be shared between both IDEs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#discussion_r1166054786"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#pullrequestreview-1384267601", "body": ""}
{"comment": {"body": "We might be able to do this without having to build this ourselves.\r\n\r\n`JBPopupFactory.getInstance().createConfirmation(...)` or something else in JBPopupFactory should give us a modal with text content and some options...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#discussion_r1166059218"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#pullrequestreview-1384268662", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#pullrequestreview-1384439248", "body": ""}
{"comment": {"body": "Saw this but didn't support Title / Description. \r\n\r\nUp to using this if having both is unnecessary.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5613#discussion_r1166161572"}}
