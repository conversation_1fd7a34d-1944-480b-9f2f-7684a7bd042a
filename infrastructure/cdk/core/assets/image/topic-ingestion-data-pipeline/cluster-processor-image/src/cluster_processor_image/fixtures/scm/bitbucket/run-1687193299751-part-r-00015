{"comment": {"body": "delete if unused code\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189519333"}}
{"comment": {"body": "so if we receive just the netbios this connection, and on the next connection we receive just mdns, it\u2019s ok that we use different names \\(due to capitalization\\) every time?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189519427"}}
{"comment": {"body": "delete if unused code", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189519445"}}
{"comment": {"body": "what\u2019s your take on merging with PR #559, specifically the `wait_for...` terms? Do we want to wait for mdns/netbios?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189519895"}}
{"comment": {"body": "I agree.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189531544"}}
{"comment": {"body": "We do not.  \nWired device don\u2019t have random mac address. This entire feature is for wireless.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189531639"}}
{"comment": {"body": "The comparison of the host names happens with a case insensitive comparison so it doesn\u2019t matter how we capture the hostname.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189531694"}}
{"comment": {"body": "There is some problem here currently that this doesn\u2019t work but I just want to temporary disable it, will fix it later.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189531990"}}
{"comment": {"body": "Right now I think we shouldn\u2019t implement the wait. I\u2019m afraid of devices getting stuck in a waiting status for a long time./", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189532419"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} kinda redundant checking for `wireless` when androids and windows 10 random mac are wireless-only, no?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189534212"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} right", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189534240"}}
{"comment": {"body": "Sorry for the late response, but this will not work for 'device\\_prev\\_mac\\_addr\\_wireless\\_store'. I suggest the following:\n\n```\nfor key, value, priority, cmp_func in params_list:\r\n\tif value and value != 'None':\r\n\t\tstored_attr_dict = device_dict[key] if 'store' in key else device_dict\r\n\t\tsession_attr_dict = value if 'store' in key else {key: value}\r\n\t\tsession_attr_key = set(session_attr_dict.keys()).pop()\r\n\t\tstored_value, session_value = tuple(attr_dict.get(session_attr_key) for attr_dict in (stored_attr_dict, session_attr_dict)\r\n\t\tlogging.info(f\"comparing key {session_attr_key} value {session_value} stored_value {stored_value}\") \r\n\t\tif stored_value is None:\r\n\t\t\tunknown_attributes.append((key, value))\r\n\t\telif cmp_func(session_value, stored_value):\r\n\t\t\tintersecting_attributes.append((key, value, priority))\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189545923"}}
{"comment": {"body": "bump\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189640406"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Makes sense. I suggest you introduce your own PR and discuss it there", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189643573"}}
{"title": "Release/cablelabs poc", "number": 562, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/562", "body": "Winodws 10 further fixes\nignore mDNS hostname in decision\nstrip also null terminators\nFix comparison between netbios and mdns names\ntemporary disable ipv4 matching due to wierd reallocation of IPs in the AP\nupdate device name in identifier\nComparing the wrong values..\nwindows logic without matching attributes\nRemoved capfp from setup.sh\nGet the wlanconfig mode based on regex instead of fixed position\nJust some typing adjustments\ncode review fixes\n\n"}
{"title": "Extended capabilities model to CableLabs devices", "number": 563, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563", "body": "Add two new capabilities into the device identification model:\n\nRM capabilities\nRSN capabilities\n\nThere were tested against all the DB to not cause significant FN.\nAdd other capabilities parsing that are not used in the model as they might cause FN and they need to be refined:\n\nCapabilities in fixed parameters\nHT capabilities\nLength of extended capabilities\nFix use of all 4 bytes of VHT-caps instead of 2 bytes\n\n\nAlso separated the HE caps from the other caps as CableLabs is testing devices with downgrading their standard/bandwidth."}
{"comment": {"body": "should keep?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189699194"}}
{"comment": {"body": "Yeah. I will move to use this mask after a bit of more testing and verification.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189709202"}}
{"comment": {"body": "What is up now with `supported_standards`? Do we not rely on that anymore?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189718178"}}
{"comment": {"body": "The part starting here could probably be written in a more generalized way. Newly added attributes only emphasize that.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189724163"}}
{"comment": {"body": "I can\u2019t follow the changes and can\u2019t debug. What is the expected behavior of devices? Which devices should have which values?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189724215"}}
{"comment": {"body": "redundant if", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189727757"}}
{"comment": {"body": "Does different data requires a new script? Or something else has fundamentally changed?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189729422"}}
{"comment": {"body": "In terms of what we used to call \u201ccoarse ID\u201d, what made you move from VHT and HE to RM and EXT? Is it just empirically lower FN?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189730192"}}
{"comment": {"body": "I agree. @{5a4500fe0cacf235de82a9d4} let\u2019s make tomorrow morning code review to drill down on the flows changed.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189804058"}}
{"comment": {"body": "We are still using VHT and HE. CableLabs are testing also in a mode that they are downgrading the standards that their devices are using \\(meaning an 802.11ac device might come up as a 802.11n device\\) so the VHT and HE are now used only as filters.  \n  \nRM and EXT are new features that don\u2019t cause substantial FN over all the set of devices.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189919015"}}
{"comment": {"body": "No.  \nCableLabs are testing also in a mode that they are downgrading the standards that their devices are using \\(meaning an 802.11ac device might come up as a 802.11n device\\) so using it might cause FN.  \n  \nOne other option to apply this logic in system when the user can\u2019t downgrade the standards by condiguration \\(Such as iphones or android\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189919698"}}
{"comment": {"body": "100% agree.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189919765"}}
{"comment": {"body": "Sure. I\u2019m available for it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/563/_/diff#comment-189924326"}}
{"title": "Small fix in attributes extraction", "number": 564, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/564", "body": "Support for 'device_prev_mac_addr_wireless_store' lost in  , this should restore it."}
{"title": "Fixed extract mac printing when in transition state (connected but not authneticated yet)", "number": 565, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/565", "body": ""}
{"comment": {"body": "I thought that `and((\"0x\"$16),1) == 1` was sufficient. What did you see?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/565/_/diff#comment-189656637"}}
{"comment": {"body": "Tamir add another condition yesterday \\(the match `IEEE80211`\\) and the conditions weren\u2019t nested. so when `and((\"0x\"$16),1) == 0` it skipped printing the first 3 parameters but printed the 4th one..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/565/_/diff#comment-189658577"}}
{"title": "Logread should also be killed after closing all.sh", "number": 566, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/566", "body": ""}
{"title": "Wait for DHCPv6 if we discover that the device is dhcpv6 capable", "number": 567, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/567", "body": "and minor rename\nFollowing this bug: "}
{"comment": {"body": "I\u2019ve seen old windows devices that CableLabs are using that do not send DHCPv6 at all. We need to look into a bit more before pushing it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/567/_/diff#comment-189768599"}}
{"title": "Fix query param return code in case it's not supported", "number": 568, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/568", "body": ""}
{"comment": {"body": "retarget to master? since this is relevant to everybody", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/568/_/diff#comment-189928083"}}
{"comment": {"body": "After i will insert to aruba, I will merge to master as well.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/568/_/diff#comment-189928394"}}
{"title": "Continue logging into log file even afer the shell the launched run_server.sh was closed", "number": 569, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/569", "body": ""}
{"comment": {"body": "Was this even an issue? I didn\u2019t notice we had truncated logs\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/569/_/diff#comment-189935587"}}
{"comment": {"body": "Yeah. We have truncated logs. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/569/_/diff#comment-189935952"}}
{"title": "Convert some multiprocessing to seperate scripts", "number": 57, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/57", "body": "Converted multiprocessing queues to Redis queues\nConverted shared dictionaries to Redis dictionaries\nDatabase insertion now its own program (receives insertions on a Redis queue, inserts to DB)\nData endpoints now their own program (Listens on the various TCP ports, inserts to Redis Queue). This used to be part of the data_processor, now it doesnt worry about TCP - it just listens for data on a Redis queue instead of multiple TCP sockets.\ndata_processor re-organized and simplified\nComplete rewrite for the docker-compose file. Now uses an .env file for organization and synchronization of different parameters between files\nDatabase access simplified - no more connecting/disconnecting for every action - theres one singleton connection per process and its automatically managed/created on the first query, and then reused by further queries.\nFixed race condition where 2 processes can attempt to create the database tables at the same time, now only the management process attempts to create the tables, the rest wait for them to be created before performing any query.\nWaitForTraining / ReadyForClassification states now under WaitForState parent that contains most of their shared code\nRemoved a lot of try/except blocks that added a lot of confusing indentation to the code and masked important exceptions.\nFix bug where no_data_after_connection_timeout_mechanism would trigger on connection rather on classification / training start\nUserOperationData training/classification queue merged into a single queue\nAdded flag to allow for persistent database between server runs. Also added a flag that allows resetting said database.\nWiFi Classification/Training timeout will go back to the ready for states like in BLE failed states\nOther random fixes/improvements\n\nI tried to keep old behavior as similar as possible, forgive me if I made mistakes, as this is a very big change. Let me know if you notice bad behavior.\n"}
{"title": "Add fixes for API for cablelabs", "number": 570, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/570", "body": ""}
{"title": "Timeout due to APTree missing: increase from 5 seconds to 30 seconds", "number": 571, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/571", "body": ""}
{"title": "Feature/router logs on server", "number": 572, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/572", "body": "Introducing forwarding router logs to server and storing them over ELK (Elastisearch, Logstash, Kibani) stack.\nBasically, logstash receives, stores and parses the logs, Elastisearch gives pretty UI (and maybe more) and Kibani gives customizable graphs for insights.\nall.sh will now start redirecting the logs to the server to port 5000.\nChange log:\n\nAdd docker-elk commit \nAdded basic parser for the logread messages for Logstash\nRouter to send the log messages directly to server once all.sh runs\n\nTo access the logs on the server:\n\nAccess http://server:5601 or port-forward 5601 to your PC and access http://localhost:5601\nUsername and password: elastic/changeme\nHead over to Kibani/Discover\n\nIm still learning my ways around this system. Ask questions if you have any.\nExample UI:\n\nConnectivity schematic (red is not implemented):\n\n"}
{"comment": {"body": "1. Can you add to the diagram which is our modules and which is existing? \n2. logd or syslog?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/572/_/diff#comment-190034888"}}
{"comment": {"body": "1. Added. Though the red blocks are not yet defined/implemented.\n2. `logd` is the service, `syslog` is the protocol\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/572/_/diff#comment-190036681"}}
{"comment": {"body": ":thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/572/_/diff#comment-190037627"}}
{"title": "David-qa-scripts", "number": 573, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/573", "body": "Cross platform mac and bands randomization script. Designed to be easy to use and consistent on all platforms supported: Linux, macOS, & Windows. \n\n\nadded automation for MAC qa\nadded ip randomization\ncomment IP randomization & added BW rand\nno ip randomization\nmerged mac_changer and move_between_bands\nbugs fixed\nbugs fixed2\nfixed coutner problem\nfixed bands problem\n.\ndependencies\nmac changer version for Tim\nhard coded ssid\n..\nnew version for CL script 'levl_mac_and_bands_changer.sh'\nnmcli to solvr dhcp problem for MAC randomization\namerican configuraitons\ntwo =\nadded nmcli to mac changer function\necho usage & disable networking if bands is on\nedit the --help\nedit Help\nedit Help\ncreated a copy with the added name\ndemo script\nfixed small SSID issue\nadded extra sllep before 2.4 connectino\nremoved installation of packages, added checking for packages, created internal version where the other has names removed, added interval minimum of 30 sec\nDifferent usage of nmcli to recover connection\nAdd print when connecting\nfinal linux script\nremoved commented commands\nremoved comments mentioning our routers\npython cross-platform version for levl_mac_bands_changer\nremoved 'selfs' and moved import into win32 scope\nself issue\nWIP fixing sudo commands issue\nfixed some issues with Linux execution\nfixes for darwin & linux spoofers\nwindows fixes\nxml files\nfix adding profile\nfix adding profile 2\nreformat mac address for windows\ninstead :\npowershell fix\nset-netadapter fixes\ncosmetics\nfinal changes\n\n"}
{"comment": {"body": "Good job!\n\nIt\u2019s also best to have a clear list of changes in the PR description. No need to include the text of every commit.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/573/_/diff#comment-190284070"}}
{"comment": {"body": "Thanks Grisha! The list of changes automatically generated by bitbucket, and I wasn\u2019t sure what is the best practice in this situation. This will be edited. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/573/_/diff#comment-190453341"}}
{"title": "UI indication when hostname is missing", "number": 574, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/574", "body": "In case the hostname was not provided by the device (whether it was because the device doesn't use DHCP or because the network is unencrypted or for any other reason), the text is grayed out and a tooltip with some info is displayed when hovering over the device name. If we get messages that should contain the hostname but it is not included, we write Hidden Name; If we dont even get those, we write -. To avoid overloading the name string itself, a new \"device_name_state\" field is introduced - it is saved persistently and published to frontend.\n\n"}
{"comment": {"body": "Do we really get no name on device \\(aka NO\\_SOURCE\\)?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/574/_/diff#comment-190447921"}}
{"comment": {"body": "We set `NO_SOURCE` only when the name is empty and the device uses a static IP and isn\u2019t windows. In these cases it\u2019s likely that we didn\u2019t get a message containing the hostname in the first place, so it isn\u2019t an issue of hiding the name for privacy reasons, rather the scenario not allowing us to retrieve the data.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/574/_/diff#comment-190460182"}}
{"title": "Feature/add decision time and bands to logs", "number": 575, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/575", "body": "Add Device detection duration in history (and history file)\nAdd network bandwidth to log\n\n"}
{"comment": {"body": "If we are pushing it to the CableLabs system, we need to have the db updated there..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/575/_/diff#comment-191006134"}}
{"title": "Aruba agent rebase latest changes - NOT FOR REVIEW", "number": 576, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/576", "body": "agent local changes\nno need to run in 100% CPU\ndo not run in infinite loop\nAdd try/except on packet parsing to not crash agent\nreactivate IPv4 DHCP lease following\ndo not hold mutex while doing API call\nAdd session id to device detials report\nAgent to send session id\nSplit DHCP and keepalive thread into seperate threads\nmutex around device expiry\nsession id as part of identifier\nremoved hardcoded devices\nwait for windows to resolve name\nremoved non-used code\nuse also ICMP and ICMPv6\nAdd last connections and last connected timestamp fields tp DB\nmaintain a full list of previous random mac addresses\nAdd lastConnected column to the UI\nHighlight last connected changes\nDHCP cloud handling\nsend empty AP tree\nRemove status column from UI\ndo not crash on None\nRemove the Status title in UI\nnon-working last seen\n\n"}
{"comment": {"body": "Is it still relevant?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/576/_/diff#comment-193844199"}}
{"title": "RSSI the force awakens", "number": 577, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/577", "body": "Bring back SNR indication in UI. Change base value to RSSI instead of SNR in dBm. Add actual RSSI value to UI.\n\n"}
{"comment": {"body": "Looks cool! But is it readable? Might be too small \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/577/_/diff#comment-191047699"}}
{"comment": {"body": "Esoteric requirements result in esoteric font size\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/577/_/diff#comment-*********"}}
{"title": "History logging issues band standard duration", "number": 578, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/578", "body": "Initial commit. Change detection duration, standard and bandwidth to not be part of the persistent device. They belong and/or are deduced from the session (including device_info) only. Updated logs to contain the phymode info\nMinor UI logos fixing\nlinux log\nAndroid device type\nquery syntax error\nReturn device type laptop also for linux\ndo not filter dhcpv6 packets by type\ntoo much text\nAdd operation duration to the log\nLast Seen -> Last Active\n\n"}
{"comment": {"body": "Would\u2019ve settle for the logo alone because we set the icon size by height and this one turns out pretty large overall, but that\u2019s a matter of taste.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/578/_/diff#comment-*********"}}
{"comment": {"body": "Is non solicit ok?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/578/_/diff#comment-*********"}}
{"comment": {"body": "Yeah. The DUID is present in all packets and many times we catch it much faster if we look at all the packets.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/578/_/diff#comment-*********"}}
{"comment": {"body": "Now that the icons presented are not necessarily of manufacturers, it might be more accurate and \\(more importantly\\) space efficient to have seemingly one \u2018device model / os\u2019 column with 2 virtual columns containing the icons next to the description. Just an idea for some future UI enhancements round.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/578/_/diff#comment-191178581"}}
{"comment": {"body": "Yeah. We definitely should do that.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/578/_/diff#comment-191178998"}}
{"comment": {"body": "We also need to change text in `preassiciation_cache.py` in `DHCPv6Cache`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/578/_/diff#comment-191284788"}}
{"title": "CableLabs PoC - merge back to master", "number": 579, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/579", "body": "Initial commit. A tooltip with some info is displayed when hovering over the device name and the text is grayed out in case the hostname was not provided by the device (whether it was because the device doesn't use DHCP or because the network is unencrypted or for any other reason). For this purpose a new \"device_name_state\" field is saved persistently\nBoolean logic rulez\nInitial commit. Bring back SNR indication in UI. Change base value to RSSI instead of SNR in dBm. Add actual RSSI value to UI.\nNow with the files\nSnrScale -> RssiScale\nThis line somehow survived\nFix conflict resulted in error\nUnify string representation towards frontend\nDidn't include html update\nFinal touches\nAdd Device detection duration in history (and history file)\nAdd network bandwidth to log\nupdate DB schema for upgrade\nfix upgrade paht\nInitial commit. Change detection duration, standard and bandwidth to not be part of the persistent device. They belong and/or are deduced from the session (including device_info) only. Updated logs to contain the phymode info\nMinor UI logos fixing\nlinux log\nAndroid device type\nquery syntax error\nReturn device type laptop also for linux\ndo not filter dhcpv6 packets by type\ntoo much text\nAdd operation duration to the log\nLast Seen -> Last Active\nBW string also in create model\n\n"}
{"title": "Added lock log viewer", "number": 58, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/58", "body": "App can now pull logs from the lock.\nCurrently logs are volatile for simplicity, also taking into account that these would be cloud based in the future.\n\n\n"}
{"title": "Feature/add rssi to logs and fix history of forgotten devices", "number": 580, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/580", "body": "Show all history events, also of forgotten devices\nAdd RSSI to logs\nHide disconnection of forgotten devices\n\n"}
{"comment": {"body": "Also a code for upgrade of the DB is missing here..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/580/_/diff#comment-*********"}}
{"comment": {"body": "it\u2019s there. `db_accessor.py` line 286.\n\nI tested the upgrade\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/580/_/diff#comment-*********"}}
{"comment": {"body": "Oh. Sorry. I think I missed that.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/580/_/diff#comment-*********"}}
{"title": "Add RSSI to device events in dashboard", "number": 581, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/581", "body": ""}
{"title": "Sliding scale of time to also include dummy authorization state", "number": 582, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/582", "body": ""}
{"comment": {"body": "Nominated for Best Feature Of The Year award", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/582/_/diff#comment-191349214"}}
{"comment": {"body": "Feature of the decade :wink:", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/582/_/diff#comment-191360884"}}
{"title": "Flipped logic for hiding disconnection event", "number": 583, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/583", "body": ""}
{"title": "Origin/aruba agent rebase from master mich cloud state ios", "number": 584, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/584", "body": "Added password for singledigits\nNot adding disconnection events to db history\nNot running handle_connections in management_endpoint at all, we had a bug where the agent stopped for a second, and once it got an empty connections report it disconnected all devies even though we always send an empty connections report. There is no reason to run this code anyway\nNot forgetting the keepalive status\nForget device is enabled in any connection state\nNot reading disconnection events from db\nNot showing disconnection events on UI\nAuthorized -> connected\nios as android 10\nNow treating ios as windows 10 and android 10\nIOS condition fix\nUniformed the apple device check\n\n"}
{"title": "Aruba - UI improvements and fixes", "number": 585, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/585", "body": "Writing the last connected and last seen values to the DB so they won't be erased when we re-run the server.\nEdited the management flask so we also present classification success logs to the user.\nImproved our detection whether we run on an aruba or not, so the flow will go smoothly from the very first connection to the agent sent and not just from the first authorized device\nChanged naming in the code: is_aruba  is_third_side_agent\n\n"}
{"comment": {"body": "have you tested upgrade scenario?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/585/_/diff#comment-191710621"}}
{"comment": {"body": "it\u2019s third party, not third side.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/585/_/diff#comment-191812705"}}
{"title": "CCP-426 - 2 models for same iPhone", "number": 586, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/586", "body": "Now waiting for dhcpv6 (if available) and not moving on until we get it\n\n"}
{"comment": {"body": "Reverting this PR because the bug just re-produced, will open it shortly after I\u2019ll figure it out", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/586/_/diff#comment-191944690"}}
{"title": "Support a system with no IPv6/DHCPv6", "number": 587, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587", "body": "Main changes:\n\nConfiguration flag DISABLE_IPV6 to disable all IPv6 logic in the system. \nWait for device type identification to complete before identifying a device (was a race condition that we had already in the system).\nUse DHCP transaction numbers to identify iOS devices\nSome minor UI changes\n\n"}
{"comment": {"body": "what\u2019s the reason for adding http packets? and how does this affect the system load? truncating the packets might be also appropriate \\(with -s param to tcpdump\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-192333972"}}
{"comment": {"body": "it\u2019s not really appropriate for Macs, so I wouldn\u2019t call the function `apple_device`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-192334062"}}
{"comment": {"body": "where do we use this new field `client_mac_addr`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-192334210"}}
{"comment": {"body": "Why it\u2019s not appropriate for mac as well?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-192335280"}}
{"comment": {"body": "Preparation for air conditioning :wink:\n\nWe have a full day capture of their logs and the http \\(not https\\) traffic is quite minimal so it shouldn\u2019t effect the system load.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-192335355"}}
{"comment": {"body": "We will use it soon enough :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-192335485"}}
{"comment": {"body": "could you provide a more elaborate name? such as `DISABLE_IPV6_DECISION`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-192340528"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} there\u2019s `Mac OS` for macs", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-*********"}}
{"comment": {"body": "the switch replaces the source MAC address in DHCP packets and the client MAC can be found in client MAC address", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-*********"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} I will look into it but all the Macbooks I\u2019ve tested returned \u201cApple OS\u201d from the fingerbank.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/587/_/diff#comment-*********"}}
{"title": "Hospitallity/dhcp client mac addr", "number": 588, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/588", "body": "Now taking the source mac address from the dhcp part of packet and not from the header\n\n"}
{"comment": {"body": "What about the caches we use to pull packets from to the just\\_connected logic?  \nThey also need to store packets by the client mac address from the packet itself..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/588/_/diff#comment-*********"}}
{"title": "Hospitality - Now highlighting last connected column in the UI", "number": 589, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/589", "body": "In addition to the existing columns that we highlight in the UI upon a change, now highlighting the last-connected column as well. Actually, we already had this logic, but it just needed a little change to work."}
{"title": "Feature/tee to cfrpackets", "number": 59, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/59", "body": "Convert recordings to pickles of CFRPackets; one pickle per event.\nI have got to have this for feature investigation."}
{"title": "Don't display DHCP renewals as connection events", "number": 590, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/590", "body": "Detect DHCP renewals (as opposed to new connections) by checking if the DHCP ACK arrived at the expected lease renewal or rebinding time. We keep some information on the DHCP lease as part of the session's device_info. We also handle the case of multiple adjacent replies from DHCP servers and only address the first ACK.\nImportant: This is a relatively simple solution and it does reduce the chances of having a false connection without overly increasing the chances of a false non-connection, but it is not that robust. Assuming uniformly distributed connection times, there is about 10% chance of considering an actual connection as a renewal (this number gets smaller as the lease time increases). In a more relevant scenario of stress testing with quick connections/disconnections, this method will do a good work and shouldnt raise any false renewals. There is also the issue of relying on shady renew/rebind parameters: If we have multiple DHCP servers and a rebind request, or (as we experienced here) multiple DHCP servers with the same IP and a renew request, we might get multiple ACK replies with different renew/rebind parameters. We will only consider the ones in the first message, but the device might rely on another servers parameters. In that case we will lose sync at least for one renew interval and the chances that we will display a false connection increase.\nBottom line is, we should strongly consider the slightly more difficult and/or sensitive mechanism relying on pairing of DISCOVER and ACK pairs. Done right, it is more robust than the method introduced here."}
{"title": "Support Android version", "number": 591, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/591", "body": ""}
{"title": "Bonjour UID feature on apple devices", "number": 592, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/592", "body": ""}
{"title": "Cherry pick hidden name ui", "number": 593, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/593", "body": ""}
{"title": "Better device display on UI", "number": 594, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/594", "body": ""}
{"title": "Now ignoring requested_ipv4 and using given_ipv4 as much as possible", "number": 595, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/595", "body": ""}
{"title": "shorter timeouts", "number": 596, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/596", "body": ""}
{"title": "Fix roiuter setup python copy", "number": 597, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/597", "body": ""}
{"title": "Removing the bonjour identifier for now to avoid false positives with the models", "number": 598, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/598", "body": ""}
{"title": "Filter only outgoing HTTP traffic", "number": 599, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/599", "body": ""}
{"title": "Improved radiotap handling", "number": 6, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/6", "body": "recsys: Temporariliy disabled RadioTaps for Grisha\nrecsys: More resilient threads\nCopied maas.py to router in setup.sh\nrecsys: No more radiotap on GUI thread\nrecsys: Fixed access to undefined data member\n\n"}
{"title": "Docker logs in tee folder", "number": 60, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/60", "body": ""}
{"title": "Add filitering by HW mac for apple devies so MacOS devices don't cause FN", "number": 600, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/600", "body": ""}
{"title": "Hack for now, wearable samsung", "number": 601, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/601", "body": ""}
{"title": "Hospitality/MacOS hack", "number": 602, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/602", "body": "Hack for now, displaying Laptop and MacOS in case of an Apple device with an MBP in its name\nShorter string for Audio / Imaging device\n\n"}
{"title": "Hospitality/Exceptions fixing", "number": 603, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/603", "body": "Those changes are already in singledigits (locally), lets merge them so we have the most updated and safe code"}
{"title": "Drop radiotap on bad checksum", "number": 604, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/604", "body": "Drop radiotap packets\nAdd bad fcs stats with rssi indication\nAdd print to stats that actually are printed\nRemove print of entire packet\n\n"}
{"title": "Clear cache only on association packets, do not use re-assocications", "number": 605, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/605", "body": ""}
{"title": "Fine tune to Device type, model and etc...", "number": 606, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/606", "body": ""}
{"title": "Update Frontend interval to 1sec", "number": 607, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/607", "body": ""}
{"title": "Update the events count to UI only for last 2 days and support duration in history per device", "number": 608, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/608", "body": ""}
{"comment": {"body": "we can try also last day", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/608/_/diff#comment-193370851"}}
{"comment": {"body": "minimum 10 events seems enough", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/608/_/diff#comment-193370924"}}
{"title": "Recsys display and parsing updates", "number": 609, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/609", "body": "add parsed bf data to schema and upload it\nAdd SU/MU total packets to recsys console\n\n"}
{"comment": {"body": "Fix conflicts and merge", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/609/_/diff#comment-246075141"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Done \\+ tested and fixed.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/609/_/diff#comment-246099456"}}
{"title": "Fixed bug where all.sh didn't output tcpdump", "number": 61, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/61", "body": "Added set -e to all.sh so this doesnt happen in the future. It silently failed on a bash if statement so tcpdump didnt run even without the --no-monitor flag"}
{"title": "Add TLS handshake capturing", "number": 610, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/610", "body": "Add TLS handshakes capturing\nesacpe string\n\n"}
{"comment": {"body": "do we care about https traffic not over port 443?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/610/_/diff#comment-193920569"}}
{"comment": {"body": "please sync with `xb_agent.sh` as well :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/610/_/diff#comment-193920683"}}
{"comment": {"body": "It\u2019s not relevant for the xb\\_agent.sh as this data is not captured there..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/610/_/diff#comment-193921023"}}
{"comment": {"body": "Probably not. The interesting things we saw were over port 443.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/610/_/diff#comment-193921103"}}
{"title": "Hotfix/shimon linux cl", "number": 611, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/611", "body": "Linux will wait for DHCPv6 like Apple\nUpdate Linux DUID DHPCv6 wait and add logging\n\n"}
{"title": "TCP base timestamp classification", "number": 612, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/612", "body": "Added TCP SYN to tcpdump filter in Aruba agent.\nExtracting IP addresses and TCP timestamps (if exist) from ethernet packets.\nAdded TCP cache that now holds recent TCP SYN requests that contain timestamps\n\nAdded 2 parameters to the device: device_static_tcp_timestamp and device_base_tcp_timestamp.\n\nThe first one is being used as a solid identifier and is True if the standard deviation of tcp base timestamps we calculate from 3 TCP sessions is smaller than 1 second;\nThe second one is only being used as an additional parameter with weight 20 and approximate comparison (1 second delta) only if the base timestamp is actually static.\n\n\n\n"}
{"comment": {"body": "let\u2019s also wrap here in a functions:\n\n1. eth layer\n2. ip layer\n3. tcp layer\n\nand etc..\n\n\u200c\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/612/_/diff#comment-194827332"}}
{"comment": {"body": "Looks good.  \nA few things:\n\n1. I think we want to limit the effect of this feature only to android 10\\+ devices. We haven\u2019t really studied how it behaves in other operating systems.\n2. I think we need to follow the timestamps also out of the just connected states. If a device is connected for a long time, we could easily loose sync and not identify it.  \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/612/_/diff#comment-195186814"}}
{"title": "Timeout for missing caps", "number": 613, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/613", "body": ""}
{"comment": {"body": "Nice catch", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/613/_/diff#comment-194799015"}}
{"title": "Wait for windows 10 hostname resolution", "number": 614, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/614", "body": ""}
{"title": "Wait for windows 10 hostname resolution", "number": 615, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/615", "body": ""}
{"title": "Enable IPv6 hospitality", "number": 616, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/616", "body": ""}
{"title": "Feature/project genesis phase 1", "number": 617, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/617", "body": "Genesis phase 1 implementation\n\n\nCan train, classify, store artifacts and some logs\n\nClassifier is Multiclass classifier\n\n\n\nGenesis is its own service, hosting all supplement services:\n\nmlflow server\nminio server\nredis server\nrediscommander server\nrabbitmq server\n\n\n\nGenesis service\n\nAPI transport: AMQP\nAPI provider: nameko\n\nAPIs:\n\n\ndata_endpoint\n\nJust puts CFRs in redis\n\n\n\ntraining_start\n\nAsync command to start training device belonging to a group\n\n\n\nclassification_get\n\nSync command to get classification result for device belonging to a group\n\n\n\n\n\nTraining:\n\nWorks with Celery (over AMQP transport)\n\nStarts worker per group\n\nChecks that the devices in that group have enough data, based on DeviceClustering module\nOn enough data, starts training a classifier on that group\nStores artifacts and logs in redis and mlflow\n\n\n\nClassification:\n\nChecks that the device can be tested (have group model) and has enough data\nreplies CANT DECIDE, HAVE CANDIDATE, NO CANDIDATE\n\nIn the beginning, genesis is commanded to not classify\n\nTheres HTTP GET api to change to classify as well with http://serverserver:8080/admin/genesis_state?state=1\n\n\n\nSupplement services:\n\n\nMLFlow\n\nFramework for archiving ML related logs, artifacts, for reproducibility, and useful UI\nClears some logs from the console\nUses DB (local sqlite3 file) and artifacts store (minio, file store service with s3-like API)\n\n\n\nRedis\n\nGood file store\nhas RedisCommander for web interface to the datastore\n\n\n\nRabbitMQ\n\nBroker for AMQP transport\n\n\n\n\nIve put old code in /genesis_service/old for some reference. Ill delete it in the future, probably in phase 2"}
{"comment": {"body": "@{5b41d9de10d57114135eca66} I know that in the design we have used the genesis phrase, but in the code let's work with naming of the feature, such as nn\\_multiclass or similar", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/617/_/diff#comment-194840639"}}
{"comment": {"body": "GET shouldn\u2019t change anything in the system.\n\nPlease add PUT for updates and GET  to show the current value..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/617/_/diff#comment-195271212"}}
{"title": "Release/hospitality ipv6", "number": 618, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/618", "body": "Enable IPv6 hospitality\nupdate function name\n5 events -> 100 events\n\n"}
{"title": "Feature/shimon icmp ts", "number": 619, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/619", "body": "ICMP Timestamps Android 10 fingerprinting\nParse ICMP TS reply\nFix exceptions\nFix ICMP TS reply parser\n\n"}
{"comment": {"body": "`AssocProbe` doesn\u2019t behave like the other caches. It doesn\u2019t have a list of packets in its cache, just 1 packet", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/619/_/diff#comment-195518877"}}
{"title": "Feature/fix env variable", "number": 62, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/62", "body": "management_processor.py was looking for MGMT_PROCESSOR_MAX_CONCURRENT_CFR.\na small piggy back to give progress to tee_to_cfrpackets.py.\n\n"}
{"title": "Set up CI with Azure Pipelines", "number": 620, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/620", "body": ""}
{"title": "Handle windows 10 changing DCHPv6 DUIDs (Bug fix CCP-428)", "number": 621, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/621", "body": "For windows machine, parse the LLT-DUID to HW mac address and timestamp.\nThe following rules are applied (per device in the filter process):\n\nIf mac addresses match, its a match\nIf the input timestamp is older than the stored timestamp, its obviously cant be the same device so reject it\nIf the input timestamp is new (less than 48), we cant use it to reject older devices.\n\n"}
{"comment": {"body": "we need this code for SDI and NEC as well", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/621/_/diff#comment-195831553"}}
{"comment": {"body": "Yes. Indeed.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/621/_/diff#comment-195835969"}}
{"title": "Hospitality NEC/snmp walk", "number": 622, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/622", "body": "Add Mac Map from switches via SNMP and encode that into the ssid field of connected devices\nFlow:\n\nServer to send IPs (and authenticated) of switches to read mac table from (via SNMP)\nagent returns periodically the result the SNMPWalk to the server (piggypacking on the empty APTree update)\nServer parses the update and sends out an SNMPMapEvent\nDataProcessor updates the ssid field for devices in JustConnected state\nSuch Android10 devices wait until the SSID update\n\nBonus:\nDockerize the aruba_agent.py.\nExample run (in ccpilot/ap_agents): API_ENDPOINT=cwd0.levl.tech MIRROR_INTERFACE=enx00e04c0c1cb3 MONITOR_INTERFACE=enx00e04c0c1cb3 docker-compose -f aruba_agent.docker-compose.yml up --build\n"}
{"title": "Hospitality NEC/arping", "number": 623, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/623", "body": "Added arping mechanism so now well be able to actually know which devices disconnected from our system. The server sends the agent a list of all connected devices and the agent is responsible of sending arping messages to them. If the server doesnt get back arp messages in time, it disconnects the device.\nIn addition, this PR changes the connection type of the devices from wired to wireless, to support the android features."}
{"title": "Revert pr 623", "number": 624, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/624", "body": ""}
{"title": "Hospitality/arping", "number": 625, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/625", "body": "Added arping mechanism so now well be able to actually know which devices disconnected from our system. The server sends the agent a list of all connected devices and the agent is responsible of sending arping messages to them. If the server doesnt get back arp messages in time, it disconnects the device.\nIn addition, this PR changes the connection type of the devices from wired to wireless, to support the android features.\n"}
{"comment": {"body": "its' not only hospitality topology \\(nec are more wider service provider for example\\)\n\n\u2018Levl-network-1\u2019 or something", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/625/_/diff#comment-195834459"}}
{"comment": {"body": "typo: default", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/625/_/diff#comment-195835289"}}
{"comment": {"body": "There should be an indentation here, but I fixed it in my PR", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/625/_/diff#comment-195835407"}}
{"comment": {"body": "it\u2019s not a good practice to have such short-named aliases. using `datetime` is just fine", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/625/_/diff#comment-195835672"}}
{"title": "Fix merge issues with comparator", "number": 626, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/626", "body": "Added an initial implementation of an agent to the aruba switch\nMoved aruba_agent to the appropiate directory\nAdded dummy bssids, ssid, iface_map, etc for now\nUpdating ap_tree with new mac connections\nMinor fixes\nNow using scapy instead of pcapy\nasdict\nMinor changes\nNow listening to ARP messages to detect disconnections. Still need to decide if this solution is the best for us, since it takes some time to find out that a device was disconnected\nUpdated wifi_trainer and wifi_classifier dhcp model with network TYPE_UNKNOWN so a real model will be built and we don't just wait for a timeout\nChanged back the connection type of all devices to WIRED since it's the most similiar situation. Updated the management flask to still present a WiFi symbol in that case\nLess spam\nNow supporting mac change and network change\nAdded idle state\nDecreased idle time to 10 seconds\nMade sure the user actually see the device as online before moving to idle state. Increased the time allowed for a device to not send any communication before subjecting it as offline\nUpdated requirements.txt\nUpdating idle count in the UI\nPatch - if a device is in WAITING_FOR_TRAINING, it goes straight to TRAINING to avoid a potential long wait\njust connected - not waiting for cap if are in Aruba state\nNot relevant to this branch, trying to avoid an exception\nUsing only DHCP (v4) to detect connections\nRemoved unnecessery sys changes\ninit.py\nNot showing Network type on frontend event message\nRemoved connectivity symbol from UI for now, maybe we need to consider to put another icon there\nNow saving the ap tree json to recover in case the agent stops\nRecovering if agent or server went down, including netcat and tcpdump recover\nNow handling android 10 too\nFixed - devices are no loqnger stuck in ready for classification\nFixed a disconnection problem\nREST API - Device lookup by MAC address\nDisabled Dummy model for now. Fixed idle presentation in the upper dashboard (count)\nChanged the saving file to have an absolute path\nSupport empty history file in API\nMinor agent fixed, fixing IP display problem in the UI as well\nAdded sna server to rnd.yml, fix minor bug in the flask\nFix query param return code in case it's not supported\nAdded singledigits server configuration\nTrying to handle another case of empty IP\nNow sniffing traffic only from the relevant interface\nFixed another bug that happened in managemnet_flask in case the last seen time from the agent was with 0 miliseconds, and the parsing of that time should be different\nA bit of a hack, but trying to handle cases where we don't get a requested ip in the dhcp messages\nUpdating the potential datetime error solution in the aruba agent as well\nNow listening to port 5353 and port 137 in the aruba_agent\ndisable fingerprinting\ndo not accept identification in permissive mode\nno point in disconnecting wired devices\ndo not filter dhcpv6 packets by type\nagent local changes\nno need to run in 100% CPU\ndo not run in infinite loop\nAdd try/except on packet parsing to not crash agent\nreactivate IPv4 DHCP lease following\ndo not hold mutex while doing API call\nAdd session id to device detials report\nAgent to send session id\nSplit DHCP and keepalive thread into seperate threads\nmutex around device expiry\nsession id as part of identifier\nremoved hardcoded devices\nwait for windows to resolve name\nremoved non-used code\nuse also ICMP and ICMPv6\nAdd last connections and last connected timestamp fields tp DB\nmaintain a full list of previous random mac addresses\nAdd lastConnected column to the UI\nHighlight last connected changes\nDHCP cloud handling\nsend empty AP tree\nRemove status column from UI\ndo not crash on None\nRemove the Status title in UI\nFix last_seen in Server\nFix last_seen\ndisable threads\nHighlight in green recent connection times\ncolor device id\nshow IP addreess of offline devices\nMinor UI logos fixing\nUI: AP -> Agent\nlinux log\nLast Seen -> Last Active\nRemoved status name from device api\nFix empty list DHCPv6 exception\nMinor referenece before assignment issue\nWaiting for dhcpv6 for ios devices\nAndroid device type\nAdded password for sna\nTimezone NY\nAdded password for singledigits\nNot adding disconnection events to db history\nNot running handle_connections in management_endpoint at all, we had a bug where the agent stopped for a second, and once it got an empty connections report it disconnected all devies even though we always send an empty connections report. There is no reason to run this code anyway\nNot forgetting the keepalive status\nForget device is enabled in any connection state\nNot reading disconnection events from db\nNot showing disconnection events on UI\nAuthorized -> connected\nios as android 10\nNow treating ios as windows 10 and android 10\nIOS condition fix\nUniformed the apple device check\nWriting the last connected and last seen time to the DB so they won't be erased when we re-run the server. Edited the management flask so we also present classification success logs to the user. Improved our detection whether we run on an aruba or not, so the flow will go smoothly from the very first AP sent and not just from the first authorized connection\nMinor fix to avoid exceptions\nChange IOS condition to use the explicit method to do so\noops\nis_aruba -> is_third_side_agent\nmore appropriate name\nDisable wait for dhcpv6 in non IPv6 networks\nparse transaction ID from dhcp packets\nAdd tracking for dhcp transcation ID for iOS devices\nfingerbank manual caller\nLinux OS -> type laptop\npath update\nRefinement of device model\nAdd DNS and HTTP traffic\nParse client mac addr\nAmazon Fire OS\nAmazon Fire\ninteger priority\nNow taking the source mac address from the dhcp packet and not header\ndict syntax\nNow highlighting last connected column in the UI\nDetect DHCP renewals (as opposed to new connections) by checking if the DHCP ACK arrived at the expected lease renewal time. We keep some information on the DHCP lease as part of the session's device_info.\nFix error from parent branch\nRemove frontend idle state in management flask\nRemove more references of the idle state\nNow storing all dhcp messages in the preassociation cache, with the mac address not taken from the header\noops\nHandle the weird case of 2 DHCP servers racing to reply with ACK\nWe should look at rebind and renew times, and not at lease time\nFinal modifications\nAdd parsing for bonjour UIDs\nbonjour UID to hex\nUpdated the real mac when dealing with the fingerbank\nbonjour uid feature\nInitial commit. A tooltip with some info is displayed when hovering over the device name and the text is grayed out in case the hostname was not provided by the device (whether it was because the device doesn't use DHCP or because the network is unencrypted or for any other reason). For this purpose a new \"device_name_state\" field is saved persistently\nBoolean logic rulez\ncherry-picked android version into hospitallity\nBetter device display in UI\nGradual escalation of use of dhcp parameteres: if renewal time is not set or didn't indicate an actual renwal - use rebind time. If it isn't set or doesn't indicate a renewal - use 0.5 * lease time as interval with a doubled margin of error\nfix merge issue\nNow ignoring requested_ipv4 and using given_ipv4 as much as possible\nshorter timeouts\nUpdating param_list only if the requested_ipv4 is not None\nNow ignoring requested_ipv4 and using given_ipv4 as much as possible\nUpdating param_list only if the requested_ipv4 is not None\nSearching for dhcp request before adding a new connection\nRemoving unnecessery code\nFix android 10 behaviour\nCheck packets in the write cache\nRemoving the bonjour identifier for now to avoid false positives with the models\nFilter only outgoing HTTP traffic\nAdd filitering by HW mac for apple devies so MacOS devices don't cause FN\nHack for now, displaying Laptop and MacOS in case of an Apple device with an MBP in its name\nShorter string for Audio device\nHack for now, wearable samsung\nExceptions fixing\nFine tune to Device type, model and etc...\nUpdate Frontend interval to 1sec\nUpdate the events count to UI only for last 2 days and support duration in history per device\nImprove ipad device model type\nAdded an initial implementation of snmp walk that will go through all the connected mac addresses\nAdded a ConnectionsMonitor thread that will collect all the connections, because now we have multiple capture agents that will run simulanitly\ndisable lease logic\nMultiple captures - both snmp and dhcp\nThree interfaces\nStarted writing the infrastructure for updating the ssid from the ap tree\nChanged network type to be wireless and disbled un-needed training and classification features\nssid is now updated from the agent\nRemoved unnecessery debug prints\nPrint fix\nAdded TCP SYN to tcpdump filter in Aruba agent, Extracting IP addresses and TCP timestamps (if exist) from ethernet packets, Added TCP cache that now holds recent TCP SYN requests that contain timestamps, Added 2 parameters to the device: device_static_tcp_timestamp and device_base_tcp_timestamp. The first one is being used as a solid identifier and is True if the standard deviation of tcp base timestamps we calculate from at least 3 TCP sessions is smaller than 1 second; The second one is only being used as an additional parameter with weight 20 and approximate comparison (1 second delta) only if the base timestamp is actually static.\nFix typo\nFix typo in name\nHard-coded setting the network type to be wireless. Since the real ssid, bssid and iface_name are not known in the data_processor when creating the ConnectionEvent(s), the network type will not be extracted when comparing the data in data_processor:284\nBrought back DhcpInfo\nmore prints\ndatetime fix\nap_tree last_seen -> timestamp\nint -> float\nEnable IPv6 hospitality\nStarted integrating the ArpMonitor\nRemoved snmp logic\nupdate function name\nSupport Dot1Q parsing (introduced when port mirroring a switch that uses vlans)\n5 events -> 100 events\nAdded arping logic so we disconnect devices in the server side when we don't see any response from them for two seconds\nFix parsing + move from datetime to timestamp\nBrought back status column\nseperated the mirror port and the internet port\nICMP Timestamps Android 10 fingerprinting\nParse ICMP TS reply\nFixed the is_third_side_agent condition\nFix exceptions\nCondition fix\nFix typo\nFix ICMP TS reply parser\nFix solid identifier exception\nFix calculation, save ref server timestamp as well\nChanged saving timestamps to db\nAdd hping query\nadding hpinger file\nIncreased timeout to 10 seconds\nNow disconnecting only non apple devices\nSince apple devices cannot be disconnected, brought back the condition that doesnt filter out connected devices when trying to link to an existing device\n\nRevert \"Brought back status column\"\nThis reverts commit 4a9df9c843265df05540c6308f6c97a094c794aa.\n\n\nHighlighting last seen\n\nNow handling disconnections\nRemoved is_real_ssid for now, it's not connected to the logic in this branch\nSupport ICMP TS updates Currently able to match between bands, but also identifies different devices as same\nConflict fix\nremove timezone and add mandatory icmp for android 10\nFix the TIMEZONE and align the server_ts and rx_ts with same time unit:\nCode review fixes\nIncreased timeout for arping\nhpinger optimization to reduce packets on the wire\nMade the arp_disconnection_detection more generic\ntime-filtering caches based on DHCP if association isn't available\nTake device tick rate into consideration\nAdd dynamic update of tcp timestamps and using more robust tick rate ratio\nDynamic update of tcp ts info: Always saving info from one packet back to avoid collision with connection event\nchange slipped away from last commit\nAnother missed conflict\nChanged arping timeout to 3 seconds\nseperated mirror and monitor ports\nRemoved unnecessery condition\nRevert \"Hospitality NEC/arping (pull request #623)\"\nFix merge issues with comparator\n\n"}
{"title": "Disable SNMP map configuration by default", "number": 627, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/627", "body": ""}
{"comment": {"body": "Agent as well\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/627/_/diff#comment-*********"}}
{"title": "Device type/model features", "number": 628, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/628", "body": "Add new sources of capturing data:\n\n\nHTTP User agents\n\n\nDNS query requests\n\n\nImplemented device manufacturer and model filtering capabilities (DNS data is still not used here)\n\nExtend DHCP filtering feature to also include whether the hostname is hidden in the dhcp request or not\n\n\n"}
{"comment": {"body": "~~can fingerbank return a model that is a subset of another model?~~\n\nI see later that yes", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/628/_/diff#comment-*********"}}
{"comment": {"body": "if the user agents is an opportunistic feature, why wait for it?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/628/_/diff#comment-*********"}}
{"comment": {"body": "Do we actually plan on altering an existing DB? If so, we should also take care of `device_static_tcp_timestamp`, `device_last_tcp_timestamps`, `device_icmp_ts_delta`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/628/_/diff#comment-*********"}}
{"comment": {"body": "I think it\u2019s okay to wait in the preliminary condition, it has a short timeout and it allows us to get at least some information before moving on.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/628/_/diff#comment-*********"}}
{"comment": {"body": "I guess this list doesn\u2019t get too long, bit it still seems weird to not use a hash map when we know we\u2019re gonna iterate over it every time we have a potential new entry.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/628/_/diff#comment-195996183"}}
{"comment": {"body": "We are working on a more high-level algorithm on how we are going to wait for features. So for now that should be fine.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/628/_/diff#comment-196193516"}}
{"comment": {"body": "What\u2019s the scenario for devices to have multiple user agents? Using multiple browsers?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/628/_/diff#comment-196214209"}}
{"comment": {"body": "Devices have multiple user\\_agents even for the same browser. We want to capture them all as only some of them contains useful information.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/628/_/diff#comment-196215109"}}
{"title": "Remove ICMP from solid identifier and from mandatory condition to wait forever", "number": 629, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/629", "body": "Remove ICMP from solid identifier and from mandatory condition to wait forever\n"}
{"title": "Bugfix/fix model bug", "number": 63, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/63", "body": "Added plot script to show where the packets are in the recording and added an option to start replaing a recording from the middle\nFixed a bug with the fixed model\n\n"}
{"comment": {"body": "Too many linebreaks", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/63/_/diff#comment-147092279"}}
{"comment": {"body": "Trying to reduce code size", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/63/_/diff#comment-147095431"}}
{"title": "Nec/fix last seen and arping issue", "number": 630, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/630", "body": "last seen was taken from the old device record, which caused the disconnection timeout to immediately expire as soon as the session was linked to the device. the relevant last seen parameter for disconnections should be a session parameter. \n"}
{"title": "Don't drop Dot1Q ARP packets", "number": 631, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/631", "body": "We dropped ARP packets that arrived with VLAN tag, now checking type in an inclusive way."}
{"title": "Add UI MAC randomization indication", "number": 632, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/632", "body": "\n\nAdd UI MAC randimization indication\n\n"}
{"title": "Mac randomisaztion script interface changes + Adaptation to Windows 7", "number": 633, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/633", "body": ""}
{"title": "Bug fix CCP-451/455/458 - Parse only client side messages DHCPv6 messages", "number": 634, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/634", "body": "The issue was that the DHCPv6 server was running on the agent side and the system have mistaken server packets coming out of the agent as client packet and use other devices DUIDs for identification"}
{"title": "Ignore SSID if SSID is none", "number": 635, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/635", "body": "SSID is None if it's some default value"}
{"title": "Update ICMP logic and fix ICMP trigger", "number": 636, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/636", "body": "\n\nUpdate ICMP logic and fix ICMP trigger\n\n"}
{"title": "Fix exception", "number": 637, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/637", "body": "\n\nFix exception\n\n"}
{"title": "Hospitality NEC/snmp walk", "number": 638, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/638", "body": "Multiple changes:\n\n\nSupport SNMP and Aruba environment\n\nAruba supports SNMP v2c, not v3 like with the HP and the client, but SNMP behavior is similar\nChanged config file from csv to yaml due to different fields\n\n\n\nSSID changes: another location that needs to be removed when no SSID\n\n\n"}
{"title": "hpinger interval update", "number": 639, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/639", "body": "\n\nhpinger interval update\n\n"}
{"title": "Fix setup", "number": 64, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/64", "body": "Fix setup file location\nFixed setup.sh issues\n\n"}
{"title": "Shimon arp table fix", "number": 640, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/640", "body": "\nFix exception on non existing arp_table object\n"}
{"comment": {"body": "Does it also change the pinging interval to 10 seconds?  \nIf so, we completely loose the information we want to collect here.. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/640/_/diff#comment-196306787"}}
{"comment": {"body": "no it\u2019s only hpinger logic.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/640/_/diff#comment-196306876"}}
{"title": "Fix DNS parser tarceback when not enough data", "number": 641, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/641", "body": "\n\nFix DNS parser tarceback when not enough data\n\n"}
{"title": "Don't insert DHCP message twice", "number": 642, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/642", "body": ""}
{"title": "Nec poc/add logs", "number": 643, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/643", "body": "Add logstash to server (needs port 5000 open) and save logs next to docker_output.logs in a file called logstash.log\nAruba agent to send logs to logstash\n\n"}
{"title": "Filter None values in all_cons", "number": 644, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/644", "body": "Saw this in  ."}
{"title": "better docker resolution for agent", "number": 645, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/645", "body": ""}
{"title": "Update ICMP interval", "number": 646, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/646", "body": ""}
{"title": "Change logstash to OSS version (without licenses issue)", "number": 647, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/647", "body": ""}
{"title": "Defensive comparison of manufacturer names", "number": 648, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/648", "body": ""}
{"title": "TCP timestamp only working on android 10", "number": 649, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/649", "body": ""}
{"comment": {"body": "This was already the case because \\(just\\_connected\\_state.py:630\\):\n\n```\ndevice_static_tcp_timestamp = is_tcp_timestamps_static if is_android_10 else None\n```\n\nAnd whenever the solid parameter value is None, devices are not filtered. Which actually brings up the point - this parameter is the first boolean solid identifier, we should change the condition in `iterate_devices_params`:\n\n```\ndef iterate_devices_params(devices: Iterable[DeviceRecord], identifier_tup: Tuple[str, str, str, str]):\n    # Get an iterable of device attribute's values for all devices in device_lst and a specific attribute.\n    identifier_name, identifier_key, identifier_value, identifier_cmp = identifier_tup\n    if identifier_value:\n        for dev in devices:\n            if (stored_dev_value := getattr(dev, identifier_key)):\n                yield stored_dev_value, dev\n```\n\n`if identifier_value:` => `if identifier_value is not None`\n\nand make sure that none of the other identifiers can be `False` while they should be `None`.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/649/_/diff#comment-196431101"}}
{"title": "Concurrent CFR fix", "number": 65, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/65", "body": ""}
{"title": "Rebase from hospitality", "number": 650, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/650", "body": ""}
{"title": "Revert \"Don't insert DHCP message twice (pull request #642)\"", "number": 651, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/651", "body": "Reverting PR-642.\nThere are some weird race conditions on the pre-assocication caches entries that we need to solve that this PR presumably exposed. Reverting for now."}
{"comment": {"body": "Gotta love those exposed bugs", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/651/_/diff#comment-196431454"}}
{"title": "Fix Android 10 wait status issues", "number": 652, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/652", "body": "ARPs/ICMP requests are not sent because the threads are sleeping. Need to wake them up.\n"}
{"comment": {"body": "In which case the sleep not returned?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/652/_/diff#comment-196386357"}}
{"comment": {"body": "It returns eventually but it can take up to the sleep interval which is 10 seconds.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/652/_/diff#comment-196386766"}}
{"comment": {"body": "typo in `Sening`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/652/_/diff#comment-196431818"}}
{"title": "All caches are not timefiltered", "number": 653, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/653", "body": "Since various packets (such as DHCPv6 SOLICIT) can arrive before the DHCPv4 packets, we cannot assume that we can time filter them based on DHCPv4 REPLY (or even REQUEST.\nThis should mitigate CCP-462, CCP-471, CCP-467, CCP-480 where the DHCPv6 solicit came before DHCPv4 packets."}
{"comment": {"body": "Can we do it with a parameter to the caches?   \nI don\u2019t want to break the behaviour of the wireless solution..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/653/_/diff#comment-196449395"}}
{"comment": {"body": "How critical is that? To do that, I need to add a way to sync with the agent before creating the caches, which would take some time. I prefer that over creating an env variable for the server configuration", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/653/_/diff#comment-196452010"}}
{"comment": {"body": "It\u2019s not cirtical for now. But let\u2019s add a TODO in the code to not forget about this.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/653/_/diff#comment-196453100"}}
{"title": "Check for disconnected devices only every 100ms", "number": 654, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/654", "body": "This should resolve bug CCP-482"}
{"title": "Increase priority of Mac address features above priority of statistical indetifying features", "number": 655, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/655", "body": "This is done to mitigate CCP-489.\nThere were matches of 2 device but it preferred the ICMP match above the Mac address match:\nclassifier_1   | 2020-12-29 13:02:27.490 I [just_connected_state.py:316] Got 2 device records with overlapping coarse identifiers\nclassifier_1   | 2020-12-29 13:02:27.490 I [just_connected_state.py:97] Evaluating 97a1a11124 matching_attrs [('device_prev_rand_macs', '06:48:ab:9f:d3:ca', 5)] device_priority 5\nclassifier_1   | 2020-12-29 13:02:27.490 I [just_connected_state.py:97] Evaluating d18509429a matching_attrs [('device_icmp_ts_delta', 0.02509455119862276, 10)] device_priority 10"}
{"title": "Nec poc/snmp behavior", "number": 656, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/656", "body": "Improve SNMP behavior following CCP-487 (SNMP data was not received often enough)\n\n\nSNMP larger responses (change from 10 to up to 100 at a time, in practice, up to 41 each time due to 1500 bytes MTU)\n\nSNMP timeout in case of error or snmp walk not finishing and return whatever was collected at that time\n\nI saw that it take 700ms to collect 80 mac addresses, so 2 seconds is more than enough for this use case\nHopefully on the next request, it would finish\n\n\n\n"}
{"title": "Shimon agent disconnections", "number": 657, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/657", "body": "Add logging to agent for debug\nmove snmp prober to thread\nsnmp prober fixes\n\n"}
{"title": "Compare only known types for Fingerbank  Manufacturer comparison. 2. Add Redmin to known list as Xioami", "number": 658, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/658", "body": "Happend with new Redmi 9A phone where our system rejected Redmi' vs Xiaomi manufacturers:\n2020-12-29 11:11:10.046 I [just_connected_state.py:163] Ignoring device due to mismatch for manufacturer/model. device_id: 5a83ccea63, stored manufacturer: Xiaomi Communications Co Ltd stored model: Phone, Tablet or Wearable/Generic Android/Redmi Android/Redmi 9A, input manufacturer: Xiaomi Communications Co Ltd input model: Phone, Tablet or Wearable/Generic Android/Redmi Android/Redmi 9A\n"}
{"comment": {"body": "I see that it rejected Redmi vs Redmi, which is understandable since we didn\u2019t list Redmi as a possible manufacturer.\n\nBut why convert Redmi to Xiaomi when we have the extra information that it\u2019s Redmi?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/658/_/diff#comment-*********"}}
{"comment": {"body": "It\u2019s rejected Redmi vs Xiaomi - we got the Redmi from the user agent in the fingerbank while the MAC address did belong to Xiomai in the mac address lookup, so normalizing both to Xiaomi did the trick.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/658/_/diff#comment-*********"}}
{"comment": {"body": "Sounds like manufacturer should also have a hierarchy. Prioritize Redmi over Xiaomi.\n\nWhat do you think?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/658/_/diff#comment-*********"}}
{"comment": {"body": "We should have some knowledge base of manufactures in the long run.  \nBut right I think we should be good enough with a small flat list of the biggest 10-15 manufacturers. \n\nOne of the things also implemented in this PR is that we do not reject devices that do not appear in this small list.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/658/_/diff#comment-*********"}}
{"title": "Fix disconnections after resetting agent / server", "number": 659, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/659", "body": "This PR provides a solution for scenarios where the agent or server are restarting / disconnecting while running. As for now, if the agent or server restarted, the last seen values of the devices remained as the same as before the reset, so after booting again - they will be disconnected because the time keeps ticking.\nThis PR updates the last seen time of the connected devices when we boot after such disconnections. In addition, it removes redundant logic (such as storing last_router_keepalive_time from the server state - we dont need it anymore, the existing logic in management_endpoint does the same job for us)."}
{"comment": {"body": "I believe that it\u2019s up to to the snapshots module to set the `last_seen` field upon restoration since it\u2019s a lie that the last\\_seen update is due to agent reconnection. See the `restore_sessions_cache` two lines up", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/659/_/diff#comment-196512492"}}
{"title": "rsync is confusing", "number": 66, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/66", "body": ""}
{"title": "Nec poc/log pcap", "number": 660, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/660", "body": "Use Tee to save to pcap file for ethernet/radiotap streams.\nStore that file in the server recordings directory:\n\nfile can be then downloaded and opened with wireshark\n"}
{"title": "Remove the disconnection logic of devices, just use the last_seen parameter for rejecting devices", "number": 661, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/661", "body": "Also removed some prints that flood the logs"}
{"comment": {"body": "could be shortened to `return KNOWN_ANDROIDS.get(android_manufacturer)`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/661/_/diff#comment-196615319"}}
{"title": "Replace Tee with dpkt writer", "number": 662, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/662", "body": "Tee corrupted data for some reason"}
{"comment": {"body": "will we have different pcap per server run?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/662/_/diff#comment-196609966"}}
{"comment": {"body": "yes. different pcap per server run per agent connection", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/662/_/diff#comment-196610083"}}
{"title": "Add NEC setup", "number": 663, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/663", "body": ""}
{"title": "Give up on devices after a longer timeout", "number": 664, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/664", "body": ""}
{"title": "Fix the agent reachable indication (on startup)", "number": 665, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/665", "body": ""}
{"title": "Nec poc/add tcp syn ongoing training", "number": 666, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/666", "body": "Move code from just_connected and coarse_id to tcp.... \nadd logic to create a tcp model outside the just_connected state if there's enough data\nStore in the session if it's android 10 tcp syn stuff are just for android 10\n\n"}
{"comment": {"body": "Sorry for breaking the convention", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/666/_/diff#comment-196673007"}}
{"comment": {"body": "In the case there\u2019s no model, say we\u2019re in the middle of session-device linking in `just_connected_state.py`, and `session.device_record.device_static_tcp_timestamp` is still `None`, wouldn\u2019t this create a race condition to update the model in the DB with possibly contradicting \\(or at least not identical\\) data?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/666/_/diff#comment-196674775"}}
{"comment": {"body": "I know that that\u2019s the simplest way to use the building blocks you had, but I hate the fact that we just add packets and keep extracting all of them over and over again. What if we somehow hash packets in a unique way and `@lru_cache` the functions that \"extract\" stuff from caches?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/666/_/diff#comment-196675095"}}
{"comment": {"body": "it might, but can it affect the calculation result?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/666/_/diff#comment-196675322"}}
{"comment": {"body": "Oh, nevermind, in this case it wouldn\u2019t help since the function is triggered by a new packet. So the only way out here is to save the dict directly. But in functions that extract stuff from caches periodically this could still be useful.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/666/_/diff#comment-196675395"}}
{"comment": {"body": "We MUST, as we see that cwqa RAM exploded twice today", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/666/_/diff#comment-196675399"}}
{"comment": {"body": "@{5f82bf320756940075db755e} I don\u2019t think that it can change the result entirely \\(or in any meaningful way for that matter\\) - static/dynamic decision will be the same; numeric timestamp result might shift slightly but it wouldn\u2019t hurt the feature. So this is not urgent, but still is preferable to have this process only _complete_ missing data after we found the matching device, and not compete with the basic mechanism.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/666/_/diff#comment-*********"}}
{"title": "Make sure the connections list we send back to the agent is unique", "number": 667, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/667", "body": ""}
{"title": "Add debug tp filter outliers by diff in ICMP", "number": 668, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/668", "body": ""}
{"title": "CCP-500 CCP-501 Not changing the session in the fingerbank after the classification is over", "number": 669, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/669", "body": ""}
{"comment": {"body": "We do need to collect the data as we need to collect any data that helps us identify the device \\(for example user agents that we manage to catch\\).  \nWe introduced it in PR-628\n\nDo you know why it causes display issues?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/669/_/diff#comment-*********"}}
{"comment": {"body": "I see that in some cases, _data\\_processor:handle\\_input\\_for\\_fingerbank\\(\\)_ processes data of a MAC address after the device already changed it. Since the session still exists, the flow continues and _device\\_type\\_classify\\(\\)_ is called, which triggers a _UserDeviceTypeClassificationEvent_ event -> _add\\_display\\_info\\(\\)_ is called \\(in _handle\\_user\\_device\\_type\\_classification_\\) for a mac address that was already changed. And then it changes the mac address in the UI of the existing session.\n\nIt reproduces quite often even with one iPhone \\(12 for example\\) connected: Connect an iPhone \u2192 Change to private address \u2192 Change back \u2192 the mac address appears correctly but after a couple of seconds it shows the last mac address instead of the new one", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/669/_/diff#comment-*********"}}
{"title": "Allow some handling of devices that don't transmit 80Mhz", "number": 67, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/67", "body": "Better detection of invalid formats and don't throw exceptions.\nLab-left is such annoying device.\nOther devices could exist in noisy environments."}
{"title": "Nec/support boolean params and universal agent timezone", "number": 670, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/670", "body": "Two small unrelated changes:\n\nSupport boolean parameter values by not skipping False values\nMake agent rely on universal time so that no local agent behavior affects features using tcpdumps capture timestamps.\n\n"}
{"comment": {"body": "I am also adding the server\\_ts in ICMP, let\u2019s combine them to general one for all the packets ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/670/_/diff#comment-196782311"}}
{"title": "Add ICMP TS delta debug", "number": 671, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/671", "body": "\n\nAdd ICMP TS delta debug\n\n"}
{"title": "Nec/ease tcp requirements", "number": 672, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/672", "body": "\n\nSmall refactor for tcp_timestamps.py\nWait for only 2 sessions instead of 3 to increase participation chances\n\n"}
{"title": "Shimon icmp filter", "number": 673, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/673", "body": "Filter same packets ICMP\nICMP filtering\nFilktered ICMP\nFix exceptions\n\n"}
{"title": "Neighbour solicitation feature for Android 10", "number": 674, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/674", "body": "Each android 10 connecting to a network tries to assign a random IPv6 address to itself. It first checks if this address is free using ICMPv6 Neighbour Solicitation. This address is same across network and mac addresses (until device reboot)."}
{"comment": {"body": "Why only 3 last octets?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/674/_/diff#comment-196986822"}}
{"comment": {"body": "Only the last 3 octects are participating in the IPv6 solicitation as they are the device identifier while the first 3 octets are the manufacturer identifier\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/674/_/diff#comment-196987582"}}
{"title": "tcp - aggregate packets when is_android_10 undetermined", "number": 675, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/675", "body": "Fix recent change, don't return when is_android_10 is None"}
{"title": "Merge all new code back to hospitality", "number": 676, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/676", "body": ""}
{"title": "Merge nec to hospitality", "number": 677, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/677", "body": "The purpose of this PR is to align the hospitality code (that was used for sna, single-digits demos) back with our current nec-demo code.\nThe merge itself had no conflicts to begin with.\nAn ACTIVE_ON_NETWORK configuration flag was added (feel free to suggest any other name). That flag determines whether we send a response to the agent, so when its disabled the agent wont get a response  wont attempt to arp, hping or perform snmp actions. In addition, that flag is responsible for deciding if we include the ICMP feature in the mandatory condition.\nI assume well probably find out more minor things when we actually try to test it later on today, but reviewing this will be great"}
{"title": "Restore PCAP writing", "number": 678, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/678", "body": "Restore pcap writing with BufferedFileWriteDelegate\nNo more blocking writes on network thread; Less writes on IO with increased buffer (from 8KB to 1MB)"}
{"comment": {"body": "We need to add sleeping here - this is potentially doing a busy wait and might clog the CPU.  \n  \nOne option is making the queue.get call blocking and adding a small timeout \\(like 1 sec\\) to wake up when there are new entries in the queue.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/678/_/diff#comment-197008426"}}
{"comment": {"body": "Never mind. I though I\u2019ve seen block=False :sweat_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/678/_/diff#comment-197008533"}}
{"comment": {"body": "We should limit the file size as it can be very huge.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/678/_/diff#comment-197009392"}}
{"comment": {"body": "limited to 10k items", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/678/_/diff#comment-197010648"}}
{"title": "CCP-517 - fixed last seen device filtering", "number": 679, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/679", "body": "We are keeping last_seen information of a device in two locations in the session:\n\ndevice_info.last_seen\ndevice_record.last_seen\n\nIf we have multiple session device_record is shared between sessions (and used to update the DB for the device record) while device_info is unique per session. What happend in issue CCP-517 is that we were using the global instance instead of the local - which cause one session to override the last seen status of a different session. In turn caused to a device to reject iteself on the last seen filtering.\nBonus points: We have been updating last_seen on disconnection event. It was removed."}
{"comment": {"body": "Just to note, the update of last\\_seen on disconnection made sense in a comcast/cablelabs-like configuration.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/679/_/diff#comment-197010998"}}
{"comment": {"body": "And another note, `session.device_info` was taken hostage \\(by me\\) for server needs for some reason. Its original purpose is to hold read-only device details sent from the agent. On a separate PR I will move `DeviceDetails.last_seen`, `DeviceDetails.last_dhcp_update_time` and `DeviceDetails.dhcp_info` to reside directly under `Session`.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/679/_/diff#comment-197011664"}}
{"comment": {"body": "We use DeviceDetails.last\\_seen to also update the last seen value in the DB, so it\u2019s something that is being saved persistently for a device, so I\u2019m not sure how easy it will be to move.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/679/_/diff#comment-197013149"}}
{"comment": {"body": "Can you refer me?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/679/_/diff#comment-197013381"}}
{"comment": {"body": "Line 766 in update\\_last\\_seen func\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/679/_/diff#comment-197013410"}}
{"comment": {"body": "Only `DeviceRecord.last_seen` is saved to DB there, no?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/679/_/diff#comment-197013516"}}
{"comment": {"body": "Yeah.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/679/_/diff#comment-197013966"}}
{"title": "Management processor now ONLY raises events for data_processor, removed dummy user, will ignore data for non-prioritized macs", "number": 68, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/68", "body": "Management processor now ONLY raises events for data_processor\nRemoved dummy user\nWill ignore data for non-prioritized macs\n\nNote that all the messy connect_to_db and disconnect_to_db and the if levl_config.protocol == 'wifi': statements are only temporary until the #57 PR is merged.\n"}
{"comment": {"body": "How is the remove of the dummy user is related to the fix?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/68/_/diff#comment-147732255"}}
{"comment": {"body": "This fix will probably solve the parallel connections lead to no train/classify bug, since there problem seemed to be in the management processor, probably some race condition.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/68/_/diff#comment-147742440"}}
{"comment": {"body": "It's not used anymore and it made this diff [https://bitbucket.org/levl/comcast/pull-requests/68/management-processor-now-only-raises/diff#Lccpilot/processes/](https://bitbucket.org/levl/comcast/pull-requests/68/management-processor-now-only-raises/diff#Lccpilot/processes/data_processor.pyF198)data\\_processor.pyF198 really awkward so I've just removed it because I didn't feel like trying to think how to make it work with the new changes ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/68/_/diff#comment-147742594"}}
{"comment": {"body": "@{5d5960d9c812c40d27bfb81e} Please test that.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/68/_/diff#comment-147747354"}}
{"title": "SNMP changes to support new HP switch", "number": 680, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/680", "body": "\n\n\nUse 802.1q MIB instead of 802.1d\n\nOld HP and Aruba support this MIB\n\n\n\nAdd new hp switch env\n\n\ndont forget to enable hp-2 (uncomment in snmp_servers.yaml ) if you want to use it!"}
{"title": "Rebase from release/nec_poc", "number": 681, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/681", "body": ""}
{"title": "Make sure device_ids are not None before deciding to skip fingerbank", "number": 682, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/682", "body": "This solve CCP-518"}
{"title": "Support multiple NS requets", "number": 683, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/683", "body": "Some devices such as Pixel3 (in CCP-521) or Nokia 3.4 (in CCP-520) are sending 2 ICMPv6 neighbour solicitations on connection instead of a single one."}
{"comment": {"body": "are there devices that don\u2019t send ICMPv6 neighbour solicitations packets?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/683/_/diff#comment-*********"}}
{"comment": {"body": "On IPv6 network you can either do DHCPv6 or get IPv6 with SLAAC \\(which involved doing the ICMPv6 NS\\). As android do not implement DHCPv6 it must use SLAAC.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/683/_/diff#comment-*********"}}
{"title": "Do not clog fingerbank with reqeusts", "number": 684, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/684", "body": "When multiple requests are sent to fingerbank in a very short time, the fingerbank start to return Connection refused error. Which in turn causes devices to be stuck in authorization.\nThis solved issues like CCP-519, CCP-518, CCP-515, CCP-516\nSo this PR do two things:\n\nIt decreases the number of fingerbank requests - the filtering mechanism did not work on device with dhcp_vendor=None (apple devices) which caused a fingerbank request for each and every DHCP packet\nImplemented a retry mechanism \n\n"}
{"comment": {"body": "~~What about using caching with @lru\\_cache? This should mitigate some issues~~\n\nScratch that. We need caching mechanism as retrying just 3 times doesn\u2019t scale enough with devices. 3 times might not be enough for more connecting devices.\n\nAs fingerbank responses wouldn\u2019t change \\(in span of even days\\), we can and should cache the responses to not send the same requests again.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/684/_/diff#comment-*********"}}
{"comment": {"body": "we need to make sure that on hight retransmit won\u2019t be blocked by fingerbank rate limit", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/684/_/diff#comment-*********"}}
{"comment": {"body": "This library [https://github.com/ionrock/cachecontrol](https://github.com/ionrock/cachecontrol){: data-inline-card='' }  or this library [https://github.com/reclosedev/requests-cache](https://github.com/reclosedev/requests-cache){: data-inline-card='' }  should be suitable", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/684/_/diff#comment-*********"}}
{"comment": {"body": "I completely agree.  \nWe should definitely add a chacing layer before hitting the external servers. I would add to the open tasks for the PoC.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/684/_/diff#comment-*********"}}
{"title": "Rebase from release/nec_poc", "number": 685, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/685", "body": ""}
{"title": "Nec/dismiss inactive sessions of device", "number": 686, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/686", "body": "In CCP-500, we had a problem regarding to handling an event right after a device changed its mac address. Since we keep sessions with the same device_id even though they are no longer relevant, we might get weird timing problems of editing a DeviceRecord details with an event that affects older sessions.\nThe purpose of this PR is to remove such sessions when a device changes its mac address. We no longer need them.\n"}
{"comment": {"body": "This PR is obviously required, but we should note that we did have cases in which one device had multiple active sessions, like when we connect a laptop with multiple network interfaces, and all are linked to the same device. Just popping the session leaves this connection dangling, and it might cause issues when the latest \u201cinterface\u201d disconnects, for example. But this is just a note; The scenario of one device with multiple sessions isn\u2019t well defined in the system anyway, and it is less relevant in the hospitality configuration where we don\u2019t have disconnections.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/686/_/diff#comment-197096282"}}
{"title": "Nec poc/restore pcap recording", "number": 687, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/687", "body": "Introduce BufferedFileWriteDelegate\n\nLike logrotate, but no number of files limitation and adds compression\nAllows delegation of writing pcaps, while creating new files after file size limit reached, with fewer iops and finishes with compression \n\n\n\nRestore PCAP recording\n\n\n"}
{"title": "Reset JustConnected timeout detectors on (re-)connection of a device", "number": 688, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/688", "body": "This resolves issue CCP-524 because the system was not waiting enough to capture the ICMPv6 packets (due to the timeout being expired immediately)"}
{"comment": {"body": "caps timeout not exist in this branch", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/688/_/diff#comment-197125684"}}
{"comment": {"body": "They are referred to under the handle\\_disconnect ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/688/_/diff#comment-197126459"}}
{"title": "More rebase", "number": 689, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/689", "body": ""}
{"title": "Send temperature from companion", "number": 69, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/69", "body": "Added button to app to transmit UDP packets with the temperature.\nRequested by Amir for analysis.\n"}
{"comment": {"body": "veryMuchCancelK10xBye\n\nedit: in python, we tend to call this method `stop`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/69/_/diff#comment-147791833"}}
{"comment": {"body": "Python has no power in this realm", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/69/_/diff#comment-147792852"}}
{"title": "Feature/protect from bitdefender", "number": 690, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/690", "body": "Bitdefender does not allow running external scripts. So lets not call external scripts, but call them external scripts internally. Apparently this is enough"}
{"comment": {"body": "Very interesting, this not blocked?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/690/_/diff#comment-197142233"}}
{"comment": {"body": "Nope", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/690/_/diff#comment-197144228"}}
{"title": "Added culomns to the devices table if they dont exist so we can recover from our old hospitality systems", "number": 691, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/691", "body": ""}
{"title": "Bind POCO Android layer 2 model to Xiaomi", "number": 692, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/692", "body": "\n, "}
{"comment": {"body": "This fixes it only for the Poco phone, but there is a deeper underlying issue that needs to be dealt with:  \nThe intended behaviour of the code was to not reject manufacturers of devices that are unknown because we can\u2019t link all sub-manufacturers to all of their manufacturers and the system shouldn\u2019t fail when it first run into a never-seen-before device.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/692/_/diff#comment-*********"}}
{"comment": {"body": "Thanks. Is it this code\u2019s fault?\n\n```\ndef comparate_fingerbank_detection_layers(dev1_model, dev2_model):\n    layers1 = dev1_model.split(\"/\")\n    layers2 = dev2_model.split(\"/\")\n    for i in range(min(len(layers1), len(layers2))):\n        if i == 2:\n            if layers1[i].endswith(' Android') and layers2[i].endswith(' Android'):\n                if _infer_known_android(layers1[i]) != _infer_known_android(layers2[i]):\n                    return False\n        else:\n            if layers1[i] != layers2[i]:\n                return False\n\n    return True\n```\n\nWe get to this function because the inferred manufacturer is `None` \\(because POCO doesn\u2019t exist in the map\\), so we don\u2019t return in the previous 3 checks in `canonical_device_model_compare`.\n\nBut here, in the 2nd layer, we don\u2019t check if the result of `_infer_known_android` is `None`, so we compare `None` with `\"Xiaomi\"`  and return `False`.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/692/_/diff#comment-*********"}}
{"comment": {"body": "Yeah. Good catch! We need to verify that both `_infer_known_android` are not None, before doing the comparison. You sending an additional PR?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/692/_/diff#comment-197311089"}}
{"title": "Feature/dismiss inactive sessions", "number": 693, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/693", "body": "Merged to release/nec_poc, doing this PR to merge to feature/merge_nec_to_hospitality as well."}
{"title": "If a model name is unknown, don't fail the inferred manufacturer matching", "number": 694, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/694", "body": ""}
{"title": "Nec poc/fixes", "number": 695, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/695", "body": "un-gitlfs lge.png\ndhcpv6_ns is now mandatory\nFingerbank more relaxed retries\nFix types and functions signature\n\n"}
{"comment": {"body": "Should we update the timeout in just\\_connected accordingly? If waiting for a device-type classification is mandatory, and it might take some time, maybe we should add another timeout constant to handle the fingerbank pressure scenario?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/695/_/diff#comment-*********"}}
{"comment": {"body": "for now we limited the retries to be less than 30 seconds", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/695/_/diff#comment-*********"}}
{"title": "Nec poc/monitor me", "number": 696, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/696", "body": "Add monitoring to both server and agent with telegraf\n\n\nServer is acting as telegraf proxy\n\nServer connects its telegraf to monitrong.levl.tech influx\nAgent connects its telegraf to servers telegraf\n\n\n\n\n\nSmall refactor to aruba agent docker\n\n\nUsing logspout instead of direct docker logging engine\n\nAllowing to start the agent independently of server status\nLogs are also visible on agents stdout\n\nDownside: were losing syslog timestamp accuracy (only seconds resolution), but we have that timestamp from pythons logging\n\nThey cant merge 2 year old pull requests: https://github.com/gliderlabs/logspout/pull/401\n\n\n\n\n\nOn our way to self-contained agent\n\nNo more mounting. Copy all the files to the container so that we can deploy it at some point\n\n\n\nRun the agent now with: HOST_HOSTNAME=$(hostname) API_ENDPOINT=********** MIRROR_INTERFACE=enx00e04c0c1cb3 MONITOR_INTERFACE=enx00e04c0c1cb3 docker-compose up --build in the ccpilot/ap_agents/aruba_agent directory\n\nor API_ENDPOINT=********** MIRROR_INTERFACE=enx00e04c0c1cb3 MONITOR_INTERFACE=enx00e04c0c1cb3 ./run_agent.sh in that directory\nIf you get an error from docker saying that aruba agent already exists, run docker system prune\n\n\n\n\n\n"}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Can we do the proxy over VPN to avoid opening port 8086 in the server?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/696/_/diff#comment-197784385"}}
{"comment": {"body": "this is definitely a problem, but we don\u2019t have VPNs between agents.\n\nUsing https would be easier, but then working with servers without domains \\(or whatever is required for https\\) wouldn\u2019t work. yet monitoring.levl.tech doesn\u2019t work with https", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/696/_/diff#comment-197791082"}}
{"comment": {"body": "When using VPN, we won\u2019t need to open the port in the server\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/696/_/diff#comment-198362386"}}
{"comment": {"body": "I\u2019ve be clarified that we can use VPNs and have VPNs on most agents, which I wasn\u2019t aware of.\n\nWe can close the 6xxx/5000/8086 ports on the servers", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/696/_/diff#comment-198366834"}}
{"title": "Release/nec poc", "number": 697, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/697", "body": "Restore pcap writing with BufferedFileWriteDelegate\nLimit max items in queue\nenable writing\nLimit file size\nFixes\nMove to a seperate module and add compression\nlast fixes\nPopping old sessions with same device id after a device changed its mac address\nChange method name\nFix compression\nuntrack lge.png\nun-lfs lge.png\ndhcpv6_ns is now mandatory\nFix types and function signature\nrelax fingerbank retries\ntuned params\nIncreate to total of 19.1 seconds of fingerbanking\n\n"}
{"title": "TCP session to be defined by IPs", "number": 698, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/698", "body": "Immediate solution for  would be to define a session using the 2-tuple (src ip, dst ip) only. We will gather some statistics to see how much it hurts the feature's participation ratio, and revise the solution as necessary.\n\n"}
{"comment": {"body": "what about `nec_poc` branch?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/698/_/diff#comment-*********"}}
{"comment": {"body": "Now that the merge to `release/hospitality_poc` is done \\(2 min ago\\), I think that\u2019s the only one we\u2019re working with. No?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/698/_/diff#comment-*********"}}
{"title": "TCP timestamp filtering disable flag", "number": 699, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/699", "body": "Add flag DISABLE_TCP_TIMESTAMP_FILTERING, will not add device_static_tcp_timestamp to solid identifiers when == 1"}
{"title": "Added ability to choose band in recording system UI", "number": 7, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/7", "body": ""}
{"title": "Demoted capfp logs from errors", "number": 70, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/70", "body": ""}
{"title": "Hospitality poc/fixes from production", "number": 700, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/700", "body": "Fix android version appearing after device model in UI when it's not just Android string ()\nlast_network_type and last_network_source should load enum values from db, not integers\nDor doesn't like that the model is in another line after model type (  )\n\n"}
{"title": "Configuration Updates - Tim's Engenius + wifi dev server", "number": 701, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/701", "body": "Add Tim's 2 Engenius APs and corresponding servers, add 4th wifi-dev server for office network.\n"}
{"comment": {"body": "would be hard to pronounce \u201cw-d-4\u201d. Much harder than \u201cc-w-d-4\u201d", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/701/_/diff#comment-197614120"}}
{"comment": {"body": "this is the first bud of removing the word \u201ccomcast\u201d from the repo completely! \"A journey of a thousand miles begins with a single step\"\u00a0\\([Chinese](https://en.wikipedia.org/wiki/Chinese_language):\u00a0\u5343\u91cc\u4e4b\u884c\uff0c\u59cb\u65bc\u8db3\u4e0b;\u00a0[pinyin](https://en.wikipedia.org/wiki/Pinyin):\u00a0_Qi\u0101nl\u01d0 zh\u012b x\u00edng, sh\u01d0y\u00fa z\u00fa xi\u00e0_;\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/701/_/diff#comment-197615752"}}
{"comment": {"body": "snicky", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/701/_/diff#comment-197622119"}}
{"title": "Feature/shimon icmp ts update ongoing", "number": 702, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/702", "body": "ICMP TS ongoing update model\nICMP TS ongoing training\n\n"}
{"comment": {"body": "Do not merge yet. Not finished all the testing.. I will merge when done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/702/_/diff#comment-198429343"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/702/_/diff#comment-198486750"}}
{"title": "Itai/enable debugging", "number": 703, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/703", "body": "enable debugging on vscode docker-compose python setup\nPending: check this setup on pycharm(by whoever is using it)\n\n"}
{"comment": {"body": "since we have currently branched between `master` and `release/hospitality_poc`, I suggest to retarget to `release/hospitality_poc` or rebase to `release/hospitality_poc`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/703/_/diff#comment-198274589"}}
{"title": "Fix pcap writer bug", "number": 704, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/704", "body": "The previous writer was not creating an appropriate pcap header for the pcap file splits, so you couldnt just open the pcap files (from the 2nd file) in wireshark.\nChanged to restarting dpkt writer instead of internal file pointer"}
{"title": "Fix CCP-540", "number": 705, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/705", "body": "Base windows detection on fingerbank result and not on dhcpfp since cheap routers also send MSFT 5.0 in their DHCP"}
{"title": "Time limited caches", "number": 706, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/706", "body": "I am pushing part of my work so that we enjoy the benefits of the time limited cache. A deeper refactor of the caches (with only design and efficiency implications, not functionality like this one) will be included in a second PR.\n\n\nPackets in cache now have an expiration time.\n\nThis is based on cachetools' TTLCache.\nCurrently, each packet expires after 60 seconds and each subtype cache holds up to a 1000 packets (FIFO).\n\n\n\nEach captured packet (Radiotap/Ethernet) is an instance of CapturedPacket. This common class holds the raw data, the capture timestamp and a one-time pseudo-random nonce for each packet. This allows to calculate a unique, reproducible hash for each packet. In the second PR I will use this to cache requests on static packet lists (tuples really).\n\nStill in the Radiotap/__init__.py department - replaced the types dict that was overloaded with attributes with the EthernetFields dataclass.\n\n\n\n"}
{"comment": {"body": "this makes me ponder whether we need our own parsing since we\u2019re deferring to `dpkt`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/706/_/diff#comment-198485343"}}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/706/_/diff#comment-198485677"}}
{"comment": {"body": "We eventually use dpkt for most of the packets now\u2026 And dpkt seems to be bearable in terms of performance. So yeah, we could drop the `memoryview` parsing soon.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/706/_/diff#comment-198486182"}}
{"comment": {"body": "By \u201csoon\u201d I don\u2019t imply that something is stopping us now. Just not in this PR, I\u2019ve added it to the backlog list.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/706/_/diff#comment-*********"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} yeah, no one meant soon as in soon. We\u2019ll do that \u201csoon\u201d.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/706/_/diff#comment-*********"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} The soon list.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/706/_/diff#comment-*********"}}
{"title": "Dynamic retry backoff for fingerbank requests", "number": 707, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/707", "body": "Spread backoff such that if there is a sudden burst of requests, the retry times will move away from each other and the chances of reaching the fingerbank rate limit decrease."}
{"title": "Small improvements - mac changer script", "number": 708, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/708", "body": "Very small changes in the script:\n\nInstead of just printing to the screen, from now on well create a log file and write the logs there as well.\nRetries and exceptions handling - Tim had two exceptions when running the script - in get_curr_ssid() and forget_network() methods. From now well not crash there. We do need to remember that if we fail on those methods, we might not change the mac for that iteration. But at least the next iteration might succeed.\n\n"}
{"comment": {"body": "where is the skipping part?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/708/_/diff#comment-*********"}}
{"comment": {"body": "it might be worth the effort to refactor both changes since they both follow the `retry until success or die` pattern", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/708/_/diff#comment-198934402"}}
{"comment": {"body": "missing skipping as well here", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/708/_/diff#comment-200150478"}}
{"title": "ICMP TS logs cleanup", "number": 709, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/709", "body": "Removed spamming log."}
{"title": "Added option to change server of comcastpi", "number": 71, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/71", "body": ""}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/1996286578-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/71/_/diff#comment-147760489"}}
{"comment": {"body": "Can\u2019t be bothered to implement it at the moment", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/71/_/diff#comment-147767055"}}
{"title": "Capture exception when queue is full in pcap is full", "number": 710, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/710", "body": ""}
{"title": "more items in pcap writing queue", "number": 711, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/711", "body": "Since we have RAM to spare, allow more items in buffer in case of packet bursts"}
{"title": "fix wrong env variable passed to docker-compose", "number": 712, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/712", "body": ""}
{"title": "Make devices connects much faster. Do not wait for uneeded primelinery criteria", "number": 713, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/713", "body": ""}
{"comment": {"body": "`tcp_timestamps_wait_criteria`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/713/_/diff#comment-199184295"}}
{"comment": {"body": "removed for now", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/713/_/diff#comment-199185558"}}
{"comment": {"body": "`android_10_real_ssid_wait_criteria` required in passive env?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/713/_/diff#comment-199185985"}}
{"comment": {"body": "No.  \nBut this wait criteria is already disabled in line 495 if we are running in passive mode.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/713/_/diff#comment-199191400"}}
{"title": "Hospitality/more data in log", "number": 714, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/714", "body": "Added more data to our log file:\n\nConnection time - when did we recognise the device?\nDecision time - when did we set the levl id?\nDecision duration - How much time did it take?\n\n\nIn addition, added it to the events messages as well:\n\n"}
{"title": "Hard coded set the network type to be ethernet in the log file", "number": 715, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/715", "body": ""}
{"title": "Itai/notifications", "number": 716, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/716", "body": "enable debugging environment\nnotifications infrastructure\nnotifications api\nintegrate notifications of device_connected event\nnotification flask implementation example (stub)\nuse data_json and data_class to serialize messages\n\n"}
{"comment": {"body": "Should this reside under the debug docker-compose? We have a main `docker-compose.yml` ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/716/_/diff#comment-199698481"}}
{"comment": {"body": "Is it appropriate to have `session` \\(and its friends\\) as a singleton? ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/716/_/diff#comment-199698645"}}
{"comment": {"body": "Was this generated somehow?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/716/_/diff#comment-199874506"}}
{"comment": {"body": "No, this file is documented via swagger and we can generate client/server code from it\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/716/_/diff#comment-199917493"}}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d}\n\n1. rebase with merges branch\n2. please add to run\\_server.sh debug option to run the notifiers\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/716/_/diff#comment-201828890"}}
{"title": "Feature/shimon sdi merges", "number": 717, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/717", "body": "Add flag DISABLE_TCP_TIMESTAMP_FILTERING, will not add device_static_tcp_timestamp to solid identifiers when == 1\ndon't use additional parameter as well\n\n"}
{"title": "Now using the dhcp time to the as the initial connection timestamp", "number": 718, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/718", "body": ""}
{"title": "Release/singledigits update 14012021", "number": 719, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/719", "body": "Make devices connects much faster. Do not wait for uneeded primelinery criteria\nAdded device_detection_duration_seconds field to the db and log table\nAdded connection timestamp to the log table\ndb_accessor fixes\nconnection: time->timestamp\nTypes fixes\nTime to US zone and format\nTimestamp -> Event time\nAdded another column of the time we decided on the levl id\nTrimmed so we won't see microseconds\nMissing space\nHard coded set the network type to be ethernet in the log file\nNow printing decision duration in the events messages as well\nMissing )\nAdded the dhcp request time to the connection event\nException fix\nAnother exception fix\n\n"}
{"title": "Feature/wifi slopes classifier", "number": 72, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/72", "body": "Update wifi slopes feature to make it more accurate.\nResearch page: \n\nInteresting pages:\n\nworkspace/wifi_slopes/calculators.py\nworkspace/wifi_slopes/ccpilot_wifi_slopes.py\nworkspace/wifi_slopes/sdk_fft.py\n\n"}
{"comment": {"body": "Nudge", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/72/_/diff#comment-148070763"}}
{"comment": {"body": "We could just use scipy.interpolate.interp1d \\(with kind=\u201dquadratic\u201d\\) here", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/72/_/diff#comment-148070866"}}
{"comment": {"body": "I should have written that `qint` returns the estimated maximum of the interpolation. `scipy.interpolate.interp1d` just interpolates, but doesn\u2019t give the local maximum.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/72/_/diff#comment-148070928"}}
{"comment": {"body": "When we are finally going to emerge out of workspace? :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/72/_/diff#comment-148070954"}}
{"comment": {"body": ":see_no_evil: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/72/_/diff#comment-148070964"}}
{"title": "Release/hospitality poc", "number": 720, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/720", "body": "Added an initial implementation of an agent to the aruba switch\nMoved aruba_agent to the appropiate directory\nAdded dummy bssids, ssid, iface_map, etc for now\nUpdating ap_tree with new mac connections\nMinor fixes\nNow using scapy instead of pcapy\nasdict\nMinor changes\nNow listening to ARP messages to detect disconnections. Still need to decide if this solution is the best for us, since it takes some time to find out that a device was disconnected\nUpdated wifi_trainer and wifi_classifier dhcp model with network TYPE_UNKNOWN so a real model will be built and we don't just wait for a timeout\nChanged back the connection type of all devices to WIRED since it's the most similiar situation. Updated the management flask to still present a WiFi symbol in that case\nLess spam\nNow supporting mac change and network change\nAdded idle state\nDecreased idle time to 10 seconds\nMade sure the user actually see the device as online before moving to idle state. Increased the time allowed for a device to not send any communication before subjecting it as offline\nUpdated requirements.txt\nUpdating idle count in the UI\nPatch - if a device is in WAITING_FOR_TRAINING, it goes straight to TRAINING to avoid a potential long wait\njust connected - not waiting for cap if are in Aruba state\nNot relevant to this branch, trying to avoid an exception\nUsing only DHCP (v4) to detect connections\nRemoved unnecessery sys changes\ninit.py\nNot showing Network type on frontend event message\nRemoved connectivity symbol from UI for now, maybe we need to consider to put another icon there\nNow saving the ap tree json to recover in case the agent stops\nRecovering if agent or server went down, including netcat and tcpdump recover\nNow handling android 10 too\nFixed - devices are no loqnger stuck in ready for classification\nFixed a disconnection problem\nREST API - Device lookup by MAC address\nDisabled Dummy model for now. Fixed idle presentation in the upper dashboard (count)\nChanged the saving file to have an absolute path\nSupport empty history file in API\nMinor agent fixed, fixing IP display problem in the UI as well\nAdded sna server to rnd.yml, fix minor bug in the flask\nFix query param return code in case it's not supported\nAdded singledigits server configuration\nTrying to handle another case of empty IP\nNow sniffing traffic only from the relevant interface\nFixed another bug that happened in managemnet_flask in case the last seen time from the agent was with 0 miliseconds, and the parsing of that time should be different\nA bit of a hack, but trying to handle cases where we don't get a requested ip in the dhcp messages\nUpdating the potential datetime error solution in the aruba agent as well\nNow listening to port 5353 and port 137 in the aruba_agent\ndisable fingerprinting\ndo not accept identification in permissive mode\nno point in disconnecting wired devices\ndo not filter dhcpv6 packets by type\nagent local changes\nno need to run in 100% CPU\ndo not run in infinite loop\nAdd try/except on packet parsing to not crash agent\nreactivate IPv4 DHCP lease following\ndo not hold mutex while doing API call\nAdd session id to device detials report\nAgent to send session id\nSplit DHCP and keepalive thread into seperate threads\nmutex around device expiry\nsession id as part of identifier\nremoved hardcoded devices\nwait for windows to resolve name\nremoved non-used code\nuse also ICMP and ICMPv6\nAdd last connections and last connected timestamp fields tp DB\nmaintain a full list of previous random mac addresses\nAdd lastConnected column to the UI\nHighlight last connected changes\nDHCP cloud handling\nsend empty AP tree\nRemove status column from UI\ndo not crash on None\nRemove the Status title in UI\nFix last_seen in Server\nFix last_seen\ndisable threads\nHighlight in green recent connection times\ncolor device id\nshow IP addreess of offline devices\nMinor UI logos fixing\nUI: AP -> Agent\nlinux log\nLast Seen -> Last Active\nRemoved status name from device api\nFix empty list DHCPv6 exception\nMinor referenece before assignment issue\nWaiting for dhcpv6 for ios devices\nAndroid device type\nAdded password for sna\nTimezone NY\nAdded password for singledigits\nNot adding disconnection events to db history\nNot running handle_connections in management_endpoint at all, we had a bug where the agent stopped for a second, and once it got an empty connections report it disconnected all devies even though we always send an empty connections report. There is no reason to run this code anyway\nNot forgetting the keepalive status\nForget device is enabled in any connection state\nNot reading disconnection events from db\nNot showing disconnection events on UI\nAuthorized -> connected\nios as android 10\nNow treating ios as windows 10 and android 10\nIOS condition fix\nUniformed the apple device check\nWriting the last connected and last seen time to the DB so they won't be erased when we re-run the server. Edited the management flask so we also present classification success logs to the user. Improved our detection whether we run on an aruba or not, so the flow will go smoothly from the very first AP sent and not just from the first authorized connection\nMinor fix to avoid exceptions\nChange IOS condition to use the explicit method to do so\noops\nis_aruba -> is_third_side_agent\nmore appropriate name\nDisable wait for dhcpv6 in non IPv6 networks\nparse transaction ID from dhcp packets\nAdd tracking for dhcp transcation ID for iOS devices\nfingerbank manual caller\nLinux OS -> type laptop\npath update\nRefinement of device model\nAdd DNS and HTTP traffic\nParse client mac addr\nAmazon Fire OS\nAmazon Fire\ninteger priority\nNow taking the source mac address from the dhcp packet and not header\ndict syntax\nNow highlighting last connected column in the UI\nDetect DHCP renewals (as opposed to new connections) by checking if the DHCP ACK arrived at the expected lease renewal time. We keep some information on the DHCP lease as part of the session's device_info.\nFix error from parent branch\nRemove frontend idle state in management flask\nRemove more references of the idle state\nNow storing all dhcp messages in the preassociation cache, with the mac address not taken from the header\noops\nHandle the weird case of 2 DHCP servers racing to reply with ACK\nWe should look at rebind and renew times, and not at lease time\nFinal modifications\nAdd parsing for bonjour UIDs\nbonjour UID to hex\nUpdated the real mac when dealing with the fingerbank\nbonjour uid feature\nInitial commit. A tooltip with some info is displayed when hovering over the device name and the text is grayed out in case the hostname was not provided by the device (whether it was because the device doesn't use DHCP or because the network is unencrypted or for any other reason). For this purpose a new \"device_name_state\" field is saved persistently\nBoolean logic rulez\ncherry-picked android version into hospitallity\nBetter device display in UI\nGradual escalation of use of dhcp parameteres: if renewal time is not set or didn't indicate an actual renwal - use rebind time. If it isn't set or doesn't indicate a renewal - use 0.5 * lease time as interval with a doubled margin of error\nfix merge issue\nNow ignoring requested_ipv4 and using given_ipv4 as much as possible\nshorter timeouts\nUpdating param_list only if the requested_ipv4 is not None\nNow ignoring requested_ipv4 and using given_ipv4 as much as possible\nUpdating param_list only if the requested_ipv4 is not None\nSearching for dhcp request before adding a new connection\nRemoving unnecessery code\nFix android 10 behaviour\nCheck packets in the write cache\nRemoving the bonjour identifier for now to avoid false positives with the models\nFilter only outgoing HTTP traffic\nAdd filitering by HW mac for apple devies so MacOS devices don't cause FN\nUser agent DB column\nAdd user agent parsing\nmore progress\nHack for now, displaying Laptop and MacOS in case of an Apple device with an MBP in its name\nShorter string for Audio device\nHack for now, wearable samsung\nExceptions fixing\nFine tune to Device type, model and etc...\nUpdate Frontend interval to 1sec\nUpdate the events count to UI only for last 2 days and support duration in history per device\nImprove ipad device model type\nAdded an initial implementation of snmp walk that will go through all the connected mac addresses\nAdded a ConnectionsMonitor thread that will collect all the connections, because now we have multiple capture agents that will run simulanitly\ndisable lease logic\nMultiple captures - both snmp and dhcp\nThree interfaces\nStarted writing the infrastructure for updating the ssid from the ap tree\nChanged network type to be wireless and disbled un-needed training and classification features\nssid is now updated from the agent\nRemoved unnecessery debug prints\nPrint fix\nAdded TCP SYN to tcpdump filter in Aruba agent, Extracting IP addresses and TCP timestamps (if exist) from ethernet packets, Added TCP cache that now holds recent TCP SYN requests that contain timestamps, Added 2 parameters to the device: device_static_tcp_timestamp and device_base_tcp_timestamp. The first one is being used as a solid identifier and is True if the standard deviation of tcp base timestamps we calculate from at least 3 TCP sessions is smaller than 1 second; The second one is only being used as an additional parameter with weight 20 and approximate comparison (1 second delta) only if the base timestamp is actually static.\nFix typo\nFix typo in name\nHard-coded setting the network type to be wireless. Since the real ssid, bssid and iface_name are not known in the data_processor when creating the ConnectionEvent(s), the network type will not be extracted when comparing the data in data_processor:284\nBrought back DhcpInfo\nmore prints\ndatetime fix\nap_tree last_seen -> timestamp\nint -> float\nEnable IPv6 hospitality\nStarted integrating the ArpMonitor\nRemoved snmp logic\nupdate function name\nSupport Dot1Q parsing (introduced when port mirroring a switch that uses vlans)\n5 events -> 100 events\nAdded arping logic so we disconnect devices in the server side when we don't see any response from them for two seconds\nFix parsing + move from datetime to timestamp\nBrought back status column\nseperated the mirror port and the internet port\nICMP Timestamps Android 10 fingerprinting\nParse ICMP TS reply\nFixed the is_third_side_agent condition\nFixed the is_third_side_agent condition\nFix exceptions\nCondition fix\nCondition fix\nFix typo\nFix ICMP TS reply parser\nFix solid identifier exception\nFix calculation, save ref server timestamp as well\nChanged saving timestamps to db\nAdd hping query\nadding hpinger file\nIncreased timeout to 10 seconds\nNow disconnecting only non apple devices\nSince apple devices cannot be disconnected, brought back the condition that doesnt filter out connected devices when trying to link to an existing device\n\nRevert \"Brought back status column\"\nThis reverts commit 4a9df9c843265df05540c6308f6c97a094c794aa.\n\n\nHighlighting last seen\n\nNow handling disconnections\nRemoved is_real_ssid for now, it's not connected to the logic in this branch\nSupport ICMP TS updates Currently able to match between bands, but also identifies different devices as same\nIntroduce SNMP sync to management endpoint. It'll release connection events when there's updated SNMP attributes about it\nPut the SNMPSYnc module between management processor and data_processor Aruba agent to run snmp probing based on the snmp servers given by the server\nConflict fix\nremove timezone and add mandatory icmp for android 10\nFix the TIMEZONE and align the server_ts and rx_ts with same time unit:\nCode review fixes\nIncreased timeout for arping\nhpinger optimization to reduce packets on the wire\nMade the arp_disconnection_detection more generic\njoin\nFIxes\ntime-filtering caches based on DHCP if association isn't available\nArgs\nargs\ntimestamps\ntimestamps\ncolored logs\nTake device tick rate into consideration\nAdd dynamic update of tcp timestamps and using more robust tick rate ratio\nfix db\ndo not hold duplicate user agents\nrename user_agents -> user_agent_parser\nUse of user_agent in fingerbank query\nAdd DNS parser\nAdd DNS cache\nDynamic update of tcp ts info: Always saving info from one packet back to avoid collision with connection event\nUse DHCP hostname availability as a feature\nchange slipped away from last commit\nAnother missed conflict\nChanged management_snmp to send SNMP maps, like AP Info Data processor to change SSID of devices in just_connected state justconnected: for android 10, wait for ssid update\nCleaner PR\nFix iface\nChanged arping timeout to 3 seconds\nHandle bytes\nseperated mirror and monitor ports\nFix key\nIface\nDockerize aruba_agent Run with \"API_ENDPOINT=cwd0.levl.tech INTERFACE=enx00e04c0c1cb3 docker-compose -f docker-compose.aruba_agent.yml up --build\"\nRemoved unnecessery condition\nRevert \"Hospitality NEC/arping (pull request #623)\"\n\nRevert \"Revert pr 623 (pull request #624)\"\nThis reverts pull request #624.\nRevert pr 623\n\n\nChanged back to one interface\n\nBrought back two interfaces\ndpkt\nPrint fix\nFix merge issues with comparator\nSNMP: load configuration from csv\nImport fix\nDisable SNMP map configuration by default\nDon't send snmp server to agent\nFor some reason, getting sometimes empty HTTP requests\nminor fixes\nmissed import\nbetter rule on when to update fingerbank returned model information\nremoved some import cancer\nUsing device manufacture and model information for filtering\nAdd collections of DNS queries\nAdd collections of DNS queries\nrevert merge artifiact\nremoved out commented code\nCorrect properties changed logic in fingerbank update\nFixed debug prints\nRemove leftovers\nRemove ICMP from solid identifier and from mandatory condition to wait forever\nlast seen was taken from the old device record, which caused the disconnection timeout to imeediately expire as soon as the session was linked to the device. the relevant last seen parameter for disconnections should be a session parameter. Added it under device_info\nSome small fixes\nReverting tcp db write change\nWe dropped ARP packets that arrived with VLAN tag\nUpdate signature\nAdd UI MAC randimization indication\nRemove log\nParse only client side messages\nSolid identifiers should be filtered regradless\nremoved the DHCP hostname existance from dhcp string\nStart transition to yaml based SNMP configuration\nIgnore SSID if SSID is none SSID is None if it's some default value\nUpdate ICMP logic and fix ICMP trigger\nFix exception\nRead yaml and complete flow\nRequirements\nFix docker name of aruba agent\nFix requirement\nFix reading from yaml Repair some SSID logic\nnon-mutables params\nhpinger interval update\nquick fix empty manufacturer name\nNo remove device on keep flow\nmore empty model fixes\nDon't insert DHCP message twice\nFix exception on non existing arp_table object\nUpdate hpinger logix for monitor and connection improvements\nfix hpinger comparison\nFix DNS parser tarceback when not enough data\nAdd logstash to server Aruba agent to send logs to logstash\nlogstash, not elasticsearch\nfix paths/ports\nFilter None values in all_cons\nRename to logstash.log to agent_logs.log\nFix typo\nbetter resolution\nUpdate ICMP interval\nChange logstash to OSS version (without licenses issue)\nDefensive comparison of manufacturer names\nSolid identifier for whether DHCP hostname is available in DHCP or not\nTCP timestamp only working on android 10\nfix merge\nRevert \"Don't insert DHCP message twice (pull request #642)\"\nWakeup Hpinger\nWakeup arp deteciont\nSimplified waiting logic\nHandle better None situation\ntypo\nEnable logging\nTypo\nExtend log\nEnable SNMP\nAll caches are not timefiltered\nCheck for disconnected devices only every 100ms\nAdd logging to agent for debug\nAdd #TODO\nUse Tee to save to pcap\naruba by default\nIncrease priority of Mac address features above priority of statistical indetifying features\nBefore considering a device as disconnected, see if the agent is alive and sending ap trees\nAdded AGENT_KEEPALIVE_TIMEGAP_SEC to uniform all the places we use this magic number\nNow printing ROUTER_UNREACHABLE from management_endpoint\nRouter unreachable on startup as well\nmove snmp prober to thread\nAruba agent SNMP larger requests and give timeout\nagent restart always\nRestart\nTypo\ncomment\nsnmp prober fixes\nCompare only known types for Fingerbank  Manufacturer comparison. 2. Add Redmin to known list as Xioami\nAdd back thelogging of arp\nFix identation\nexception of runner fixed\nNo need for LGE special case any more\nFix results\nFix snmpwalk\nUpdating devices last seen on server startup\nFix stdoutoput\nfix piping issues Better file name\nRemove except package\nFix - not using last seen logic if not in hospitality mode\nRemoved disconnection by last seen + add last seen to stats\nRemove flooding debug prints\nActually using last_seen timestamp to filter out devices\nRemoved force updated of last_seen\nRemove disconnect check\nHack to not crash on dup model\nKill tee on closure\nFix another model/manufacturer match bug\nReturned back log print\nlog print\nRevert tee for a moment\npush\nmore\nReplace TeeMITM with pcapwriter\nmove stuff around and add telegraf config\nGive up timeout\nShorten prints strings\nAdd NEC setup\nnec user and pass\nlower threshold\nFixed the agent reachable UI indication on startup\nFixed bug CCP-496 - timeout detector was not reseted - caused disconnection of device too soon\nMove code from just_connected and coarse_id to tcp.... add logic to create a tcp model outside the just_connected state if there's enough data\nStore in the session if it's android 10 tcp syn stuff are just for android 10\nFixes\nAdd when device is not android 10\nAdd debug tp filter outliers by diff in ICMP\nMake sure the connections list we send back to the agent is unique\nChanged solid identifier condition to check if value is not None\n\nRevert \"Changed solid identifier condition to check if value is not None\"\nThis reverts commit abc619f9890195cc3da23d24e476e35743511134.\n\n\nDo not write anything in hot path\n\nHard crash the system on huge event queue\nSupport boolean parameter values by not skipping False values\nIncreese crash thresh\nMake agent rely on universal time, use that in tcp capturing\nAdd ICMP TS delta debug\nCCP-500 CCP-501 Not changing the session in the fingerbank if the session is not the updated one\nSmall refactor for tcp_timestamps.py\nlog when updating model\nCapture ICMPv6 NS\nFilter same packets ICMP\nICMP filtering\nFilktered ICMP\nICMPv6 parsing\nFix exceptions\nneighbor solitation feature\nremoved uneeded print\nMissong commits\nignore local addresses\nneighbor solitation below prev mac addresses\nprints\nFix recent change, don't return when is_android_10 is None\nremove duplicate icmpv6 filter\nFix annoying IMCP typos\naruba agent: change logging to logspout aruba agent: self contained files\nfix paths\nChange context and file paths for aruba files\nwifi_settings\ntimeout\nlogstash restart\nrfc5424\nDisabled SNMP for hospitality\ncleanup\nRename\nAdded ACTIVE_ON_NETWROK configration flag\nFix ACTIVE_ON_NETWORK flag\nRestore pcap writing with BufferedFileWriteDelegate\nFlag fixes\nRestore pcap writing with BufferedFileWriteDelegate\nDo not update last_seen on disconnection event\nUpdate last_seen only on in a single location\nUse local (per session) device_info.last_seen and global device_record.last_seen. 2) Filter out current session\n1) Use session timestamp in handle_incoming_dhcp_connection 2) Update DB only on permanent sessions\nMatch timestamp format\nLimit max items in queue\nenable writing\nUse 802.1q MIB instead of 802.1d Add new hp switch env\nverbose fingerbank erros\nMore prints\nVerify device_ids are not None before skipping fingerbank\nline break\nline break\ndevice_info -> device_id\nFingerbank print exceptions\nMake sure device_ids are not None before comparing them\nSupport multiple NS requets\nDo not set gazillion requests on devices with no dhcp_vendor (mainly apple devices)\nImpelemnt fingerbank retries\nLimit file size\nFixes\nMove to a seperate module and add compression\nlast fixes\nPopping old sessions with same device id after a device changed its mac address\nChange method name\nFix compression\nReset JustConnected timeout detectors on (re-)connection of a device\nremoved timeout_detector_caps\nAdded culomns to the devices table if they dont exist so we can recover from our old hospitality systems\nsyslog -> logspout\nrevert logstash changes\ntelegraf to run in the name of the hostname\nBind POCO Android layer 2 model to Xiaomi\nIf a model name is unknown, don't fail the inferred manufacturer matching\nPopping old sessions with same device id after a device changed its mac address\nChange method name\nuntrack lge.png\nun-lfs lge.png\ndhcpv6_ns is now mandatory\nFix types and function signature\nsave telegraf\nrelax fingerbank retries\ntuned params\ntry to pass agent telegraf\nmerge\nIncreate to total of 19.1 seconds of fingerbanking\nTCP session defined by IPs\nmount host rootfs into telegraf\nadd script to run agent to make things more simple\nmove to requirements.txt better docker-compose practice\nfix dockerfile context\nmissing file\nAdd flag DISABLE_TCP_TIMESTAMP_FILTERING, will not add device_static_tcp_timestamp to solid identifiers when == 1\nFix 10 appearing after device model in UI when it's not just Android\ndon't use additional parameter as well\nlast_network_type and last_network_source should load enum values from db, not integers\nDor doesn't like that the model is in another line after model type\nICMP TS ongoing update model\nICMP TS ongoing training\nBase class CapturedPackets for captured packets, which holds a unique identifier per packet. Created TTLPacketList which replaces the lists holding different packet types in the preassociation cache.\nVarious fixes\nChanged to restarting dpkt writer instead of internal file pointer\nBase windows detection on fingerbank result and not on dhcpfp since cheap routers also send MSFT 5.0 in their DHCP\ndocker compose down\nChanged TTLPacketList exceptions\nAdd str method\nSpread backoff such that if there is a sudden burst of requests, the retry times will move away from each other and the chances of reaching the fingerbank rate limit decrease\nRadio_T_ap\nICMP TS fix start trigger\nFix ICMP TS update exception\nUse tuple in CapturedPacket hash\nAdd unit tests for custom preassociation_cache structures\nICMP TS logs cleanup\nCapture exception when queue is full in pcap is full\nMake zero http request exception not print entire noisy traceback\n\"pass\" was left by mistake\nmore items in queue\nfix wrong env variable passed to docker-compose\n\n"}
{"title": "Release/cablelabs poc", "number": 721, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/721", "body": "Wait for DHCPv6 if we discover that the device is dhcpv6 capable. Minor rename\nAlso wait for dhcpv6 in wired connections\nDHCPV6: only wait for apple mobile devices\nFix apple in\nAriel just keeps finding them bugs\nonboarding time\ncommit back python3.8openwrt.tar.gz hopefully correctly such that it won't be modified for no reason\nDrop radiotap packets\nAdd bad fcs stats with rssi indication\nAdd print to stats that actually are printed\nRemove print of entire packet\nClear cache only on association packets, do not use re-assocications\nLinux will wait for DHCPv6 like Apple\nUpdate Linux DUID DHPCv6 wait and add logging\nTimeout for missing caps\nAdd wired use case as well\nWait for windows 10 hostname resolution\nSupport history log files for 30 days and not only 7 (TODO: retern all history data)\nFixed wierd '1' as hostname\nspecial handle windows10 duids\nfixed missing ':'\nmore fixes\nfixed timestamp pos\nMore fixes\nLile's S10 android hack\n\nr"}
{"comment": {"body": "Some fixes are hacks and not for merge.\n\nThis is for reference to add features in hospitality \u2192 master branch", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/721/_/diff#comment-*********"}}
{"title": "Dns for filtering and type display", "number": 722, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/722", "body": "Add DNS data extraction, concluding os/manufacturer and saving to DB (the same way it is done with fingerbank device type classifier results).\nUnify device type filtering in just_connected with general feature filtering function.\nAdded UDP to init Ethernet parsing\n\n* Credit to Mich for DNS extraction and caching code\n"}
{"comment": {"body": "Forgot to update unit tests in `dns_fingerprinting/__init__.py`, PR will be updated.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/722/_/diff#comment-*********"}}
{"comment": {"body": "So a couple of fundamental issues:\n\n1. The entire premise of why we want to use this DNS data is that those are the domains the device contacts automatically after a connection to a network and we want to differentiate them with  user generated traffic. So we must capture only requests sent a few seconds after the device connection as a best effort to get just those.\n2. Fingerbank also gets input from the domains the device contacts, so we might also want to use this functionality.  See [https://api.fingerbank.org/api\\_doc/2/combinations/interrogate.html](https://api.fingerbank.org/api_doc/2/combinations/interrogate.html)\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/722/_/diff#comment-*********"}}
{"comment": {"body": "Regarding the 1st point, for devices used by actual users I did see DNS queries triggered by apps. So if, for example, the user has the Huawei website open in the browser on his Apple device and he connects to the AP, we still might see \u201chuawei\u201d queries. This is just to point out that there is still a minor chance of false type classifications even if we only look at the first few seconds \\(this also depends of course on how unique and not-user-facing are the domains in the tree\\). But I see the premise, and I will limit the operating time of the feature. I will also add the info to the fingerbank queries.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/722/_/diff#comment-*********"}}
{"comment": {"body": "Don\u2019t we also need to iterate the DHCPv6 and user agents cache as well?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/722/_/diff#comment-*********"}}
{"comment": {"body": "We should. I took the existing DHCP walk but it\u2019s a good chance to add other types.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/722/_/diff#comment-*********"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/722/_/diff#comment-*********"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/722/_/diff#comment-*********"}}
{"title": "Hospitality poc/ulimits", "number": 723, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/723", "body": "Add ulimits to both server (classifier service) and agent (aruba_agent service)\nAdd .env file to aruba_agent, if you find that more comfortable, for the parameters received by the agent\n\nI was really annoyed with the need to clone on the agent computer, so I gave the option to run the agent from the development PC\nSupplement document: \nChangelog:\n\nMake aruba agent completely volumeless\n\nAdd .dockerignore\n\nNote that this is very important to maintain, as the image generation of the agent loads everything (but doesnt actually copy) in the comcast/ directory to create the image, so keep the scanning to minimum\n\n\n\nAdd instrinsic way for telegraf to get host's hostname\n\nremove one redundant print in agent\n\n"}
{"title": "Configuration that interfered with my debugging", "number": 724, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/724", "body": "We don't need motion processes in hospitality\nDon't crash server when there are too many events\n\n"}
{"title": "Removing null terminator from dhcp_vendor and dhcp_hostname", "number": 725, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/725", "body": "After examining the logs from our beloved bunny, we saw a problem that reproduced with some of our APs that connected to the server - their device name (that comes from the DHCP hostname), and dhcp_str (that comes from the dhcp_vendor) had a null terminator char in the end, which caused problems when inserting them to our Postgres db:\n\n\n\n(The majority of devices didnt had a null terminator in those fields.)\nInstead of editing the writing actions to the db (which will cause us to have different data in the server and in the db), Itll be simpler to just trim the null terminator (if exists) when we first read the data from the packets."}
{"comment": {"body": "Nice catch!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/725/_/diff#comment-200065211"}}
{"comment": {"body": "Redirected PR to the updated branch that contains all the change from hospitality that should be merged into master", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/725/_/diff#comment-200168823"}}
{"title": "Data_processor runtime improvements", "number": 726, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/726", "body": "Two changes that make data_processor handle 1.2k ethernet packets per second instead of 0.4k.\nDon't deepcopy caches as they can be enormous\nIve seen bursts of data causing the caches to explode.\nCopying these enormous have taken up to 40% of the runtime of data_processor.\nSafety of the change:\nThe purpose of the original deepcopy was to not modify the original caches in subsequent processing. But the current code doesnt do so, so its safe.\nSince reading and writing to the caches are done in the same thread, its also safe.\nOffload logging to a separate thread\nWriting to stdout took ~30% of the data_processor runtime.\nBest is to not block data_processor for io tasks. Writes to stdout are done in separate thread.\n"}
{"title": "Removing null termination and other chars that should not be in our dhcp vendor and hostname", "number": 727, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/727", "body": "After merging this to merge_hospitality_into_master branch, opening this PR so this change will also be in release/hospitality_poc branch."}
{"title": "ICMP NS ongoing update of device model - please review", "number": 728, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/728", "body": ""}
{"title": "Ignore dhcpv6 packets that don't contain a duid", "number": 729, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/729", "body": "When looking at the logs from our beloved bunny this morning, the following exception had the nerve to pop up:\n[36mclassifier_1      |[0m 2021-01-20 07:08:00.319 E [data_processor.py:1284] Error occurred during handling of message\n[36mclassifier_1      |[0m Traceback (most recent call last):\n[36mclassifier_1      |[0m   File \"/root/src/ccpilot/processes/data_processor.py\", line 1275, in process_data\n[36mclassifier_1      |[0m     self._process_incoming_event(event, event_map, mitm_arp_detector, cooperation_manager, state_enum_to_state, users)\n[36mclassifier_1      |[0m   File \"/root/src/ccpilot/processes/data_processor.py\", line 1375, in _process_incoming_event\n[36mclassifier_1      |[0m     self.handle_input_for_fingerbank(user, data)\n[36mclassifier_1      |[0m   File \"/root/src/ccpilot/processes/data_processor.py\", line 914, in handle_input_for_fingerbank\n[36mclassifier_1      |[0m     duid_mac_address = dhcp_fingerprinting.DHCPFingerprinting.extract_mac_from_dhcpv6_duid(dhcpv6_fp_res['duid'])\n[36mclassifier_1      |[0m   File \"/root/src/dhcp_fingerprinting/__init__.py\", line 234, in extract_mac_from_dhcpv6_duid\n[36mclassifier_1      |[0m     if not duid.startswith(\"0001\"):  # DUID's type must be link-layer address plus time\n[36mclassifier_1      |[0m AttributeError: 'NoneType' object has no attribute 'startswith'\nThat exception occurred when we tried to parse a None duid. When looking at that packet in our recorded pcap, we can see that in fact we didnt get a duid (we only got a server identifier and not a client identifier) as a response to a dhcpv6 information request packet:\n\nWe need to ignore such packets and not let them cause an exception.\n"}
{"title": "React", "number": 73, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/73", "body": "Started adding react to the system\nAdded all users table, fixed bugs, and made the client-side work with react.\nRemoved unused file\n\nIve booked a meeting with a crash course in react for all of us so this will be a bit more clear for everyone :slight_smile:"}
{"title": "Add pcap recording flag (on by default) and filtering for eth packets with all zeroes src mac", "number": 730, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/730", "body": ""}
{"title": "Release/hospitality poc", "number": 731, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/731", "body": "Add pcap recording flag (on by default) and filtering for eth packets with all zeroes src mac\nfix typo\n\n"}
{"title": "Hotfix/exception stats shimon", "number": 732, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/732", "body": "catch stats exception whcihc causing infinite loop on data processor stats printinig and system crash on memory\ntcp ts debug log\n\n"}
{"title": "Fingerbank - Use mac address from the dhcp part of the packet", "number": 733, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/733", "body": "Following our integration with singledigits back in December, we added a fix to our code of taking the mac address from the dhcp layer of the message. That fix was added after we saw that we are using the mac address of the switch instead of the real mac address of the connected device.\nWhen looking at the bunny dashboard, we can see that the following device is switching names back and forth with the switch:\n\n\nAnd when investigating the logs, we can see that the hostname is changed because we are parsing the dhcp message from the android device but take the mac address of the switch, which triggers a hostname-update event:\n\nThis PR should fix this case - we anyway parse the dhcp messages when handling the fingerbank data, so we can get the real mac address of the device and avoid that mistake."}
{"title": "Merge hospitality into master", "number": 734, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/734", "body": "In this PR we made minimal changes to have the functionality of both configurations working. What mainly differs one configuration from the other is the is_third_side_agent flag inferred from the ap tree sent by the agent.\nThe UI is generated once and its layout is set in runtime according to the operating configuration. \nThere are a few tasks left here, some more urgent and other are nice-to-have. Right now the branch is in working condition, so we want to first move back to master before we accumulate any new gaps.\nIn the more prioritized TODO list:\n\nDivide configuration to easy-to-understand master scenarios/configurations and set flags accordingly.\nAdd time filtering to cache according to system configuration.\nExhaust capturable Ethernet protocols on QCA APs.\n\n"}
{"title": "Exceptions fixes from bunny", "number": 735, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/735", "body": "Some exceptions that happened tonight in our bunny server:\n\n\n\n\n"}
{"comment": {"body": "Those looks like flow errors. Can you find the root cause of why it happend.\n\nThose values should not be None and checking if there are not None might just fix the symptomp and not the cause.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/735/_/diff#comment-201289479"}}
{"comment": {"body": "**The problem**: We got exceptions tonight in bunny due to a None\u00a0**device\\_info**\u00a0and a None\u00a0**device\\_type\\_classifier**. They should not be None - device\\_info is initialised in the creation of the ConnectionEvent, device\\_type\\_classifier is set in handle\\_connected\\(\\).  \nIt turns out that the problem lies in\u00a0**data\\_processor:dismiss\\_inactive\\_sessions\\_of\\_device\\(\\)**.  \n**Bug description**:\n\n* Device 4c:6a:f6:b9:2d:fd was connected and got a levl id [58b780fa9](https://bitbucket.org/levl/comcast/commits/58b780fa9).\n* It later connected again, with the same mac address, and created a connection event.\n* The new connection was linked to the existing [58b780fa9](https://bitbucket.org/levl/comcast/commits/58b780fa9), as expected.\n* Our mechanism of removing old sessions of the same device removed the session of 4c:6a:f6:b9:2d:fd.\n* When trying to change classification state to READY\\_FOR\\_CLASSIFICATION, the session didn\u2019t exist so it created a new one, with empty fields, and we left out with a None device\\_info and None device\\_type\\_classifier.\n\nThe purpose of data\\_processor:dismiss\\_inactive\\_sessions\\_of\\_device\\(\\) was to remove duplicate sessions of the same device. But there is a bug in the implementation because we want to remove the duplicate session only if it belongs to a different mac. If the old sessions has the same mac as the new one, nothing should be removed because it\u2019s gonna be updated anyway \\(it's a dictionary, the keys are unique\\).  \nTo sum up, we need to fix the implementation of data\\_processor:dismiss\\_inactive\\_sessions\\_of\\_device\\(\\) to remove the old session only if it has a different mac address.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/735/_/diff#comment-201364568"}}
{"comment": {"body": "Closing this PR, opened a new one with the full fix for that situation", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/735/_/diff#comment-201387135"}}
{"title": "Fixed dismiss_inactive_sessions_of_device() to not remove session of the same mac address as the new connection", "number": 736, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/736", "body": "The problem: We got exceptions tonight in bunny due to a Nonedevice_infoand a Nonedevice_type_classifier. They should not be None - device_info is set in the creation of the ConnectionEvent, device_type_classifier is set in handle_connected().\n\n\n\n\n\nIt turns out that the problem lies indata_processor:dismiss_inactive_sessions_of_device().\nBug description:\n\nDevice 4c:6a:f6:b9:2d:fd was connected and got a levl id 58b780fa9.\nIt later connected again, with the same mac address, and created a connection event.\nThe new connection was linked to the existing 58b780fa9, as expected.\nOur mechanism of removing old sessions of the same device removed the session of 4c:6a:f6:b9:2d:fd.\nWhen trying to change classification state to READY_FOR_CLASSIFICATION, the session didnt exist so it created a new one (line 1474), with empty fields, and we left out with a None device_info and None device_type_classifier.\n\nThe purpose of data_processor:dismiss_inactive_sessions_of_device() was to remove duplicate sessions of the same device. But there is a bug in the implementation because we want to remove the duplicate session only if it belongs to a different mac. If the old session has the same mac as the new one, nothing should be removed because its gonna be updated anyway and not be duplicated - it's a dictionary and the keys are unique.\nThis PR fixes that method to not remove the old session if it has the same mac address as the new one."}
{"comment": {"body": "we wanted to remove pervious MAC sessions for the device or in between connection as well?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/736/_/diff#comment-201400761"}}
{"comment": {"body": "Good work on the thorough investigation!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/736/_/diff#comment-201415869"}}
{"title": "Adding metrics to TICK stack", "number": 737, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/737", "body": "Adding metrics to TICK stack"}
{"comment": {"body": "How long does it take to collect the data and then send the metrics to the telegraf server?\n\nWe\u2019ve just changed logging to be non-blocking, so it might be worth to offload the IO tasks to a different thread", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/737/_/diff#comment-201552030"}}
{"comment": {"body": "The stats are gathered today already by print\\_stats every 10 seconds.\n\nThe metrics client send to udp socket the data, it\u2019s like sending to queue\u2026 and the offload done by telegraf worker", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/737/_/diff#comment-201558927"}}
{"title": "Wireless algorithm adjustments + docker profiles", "number": 738, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/738", "body": "Some adjustment to what to use in wireless setting.\nAlso split docker file into profiles with configuration per mode. Usage when running a server:\n./run_server -p ap (default) or  ./run_server -p switch or ./run_server -p switch_active"}
{"comment": {"body": "Why do use this flag here? isn\u2019t it related to ACTIVE\\_ON\\_NETWORK?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/738/_/diff#comment-201597318"}}
{"comment": {"body": "I\u2019ve splitted it into two flags because we are using the ICMP both on active switch scenarios as well on the AP\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/738/_/diff#comment-201598850"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} But now the two are equal in each scenario\u2026 and they really mean the same thing - `ACTIVE_ON_NETWORK` is used to instruct the agent to send ICMP requests, the reply to which is used when `USE_ICMP6_TIMESTAMPS` is on. is the 6 a typo?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/738/_/diff#comment-201600357"}}
{"title": "Drop port 8080 outside and add /api prefix for REST API endpoints", "number": 739, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/739", "body": ""}
{"comment": {"body": "typo in connection", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/739/_/diff#comment-201910895"}}
{"title": "Fix priority", "number": 74, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/74", "body": "Seems like tcpdump creates so much data the server cant handle it in time, creating a huge backlog and a delay in event processing. For now running ./all.sh with --no-monitor will disable tcpdump and fix the problem temporarily until we find a way to crunch the backlog more quickly\n\nWarn if there's too much backlog\nall.sh print current CFR monitoring information for debugging/logging purposes\nDisable set_no_data_after_connection_timeout_mechanism, see CCP-58\n\n"}
{"comment": {"body": "~~is this the best form of logging for these messages?~~", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/74/_/diff#comment-147821262"}}
{"title": "Updated docker-compose with the features for the physical configuration", "number": 740, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/740", "body": "After the separation to ap / switch / active-switch configurations, update the ap configuration with the features that are relevant to the physical layer (they are all turned off in the main docker-compose, except for the DHCP model)."}
{"title": "Fix notifications exception", "number": 741, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/741", "body": ""}
{"title": "Feature/threads join except", "number": 742, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/742", "body": "Fix notifications exception\nWait for all non daemon threads to exit and catch exception\n\n"}
{"title": "Limit DNS-based fingerbank queries", "number": 743, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/743", "body": "Fingerbank only accepts 5 domain names in the interrogate api. Use only first 5 and avoid excessive queries."}
{"title": "Copy from PR 621 -windows 10 changed duid", "number": 744, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/744", "body": "In PR 621, @{5a4500fe0cacf235de82a9d4} fixed bug #428 from cablelabs, where windows 10 devices changed their duid and than created another model. \nThe behaviour now is as follows:\nFor windows machine, parse the LLT-DUID to HW mac address and timestamp.\nThe following rules are applied (per device in the filter process):\n\nIf mac addresses match, its a match\nIf the input timestamp is older than the stored timestamp, its obviously cant be the same device so reject it\nIf the input timestamp is new (less than 48), we cant use it to reject older devices.\n\n"}
{"title": "NOT FOR MERGES", "number": 745, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/745", "body": "WIP\nRemove management threads\nRemove some logs to debug and fix enum conversion exception\nadd agent simulator script\nAdd metrics to Telegraf and some initial stats\ninitial test for pcap writer with offload queue\ncompose fix\n\nRevert \"Merge branch 'itai/refactor' of bitbucket.org:levl/comcast into feature/scale_and_performance\"\nThis reverts commit ebd5fecb8baded0260f5f7e439dd720d82d7b750, reversing changes made to f71b16cec40e1f05f9fa29a63bad2b8593a9d307.\n\n\nignore redis files\n\nlogging collection and presentation\nfix path\njsom formatted logging\nadd elk\nformat log as elk json\nadd json elk formatter\nextract standard logging fields\n\n"}
{"title": "Inconsistency using devices_lst and devices_set", "number": 746, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/746", "body": "Used devices_lst when checking connectivity state, but the filtered devices iterable is actually devices_set."}
{"comment": {"body": "Nice catch!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/746/_/diff#comment-202668274"}}
{"title": "Feature/shimon 10k devices", "number": 747, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/747", "body": "WIP\nRemove management threads\nRemove some logs to debug and fix enum conversion exception\nadd agent simulator script\nAdd metrics to Telegraf and some initial stats\ninitial test for pcap writer with offload queue\ncompose fix\n\nRevert \"Merge branch 'itai/refactor' of bitbucket.org:levl/comcast into feature/scale_and_performance\"\nThis reverts commit ebd5fecb8baded0260f5f7e439dd720d82d7b750, reversing changes made to f71b16cec40e1f05f9fa29a63bad2b8593a9d307.\n\n\nignore redis files\n\nlogging collection and presentation\nfix path\njsom formatted logging\nadd elk\nformat log as elk json\nadd json elk formatter\nRemove management threads\nhack to make it run\nextract standard logging fields\nConnetion event count + critical print in logs\nbypass fingerbank\ndisable logging\nuse redis queue\nadd worker process\nadd conenction events to metrics\nMonitor and optimize periodic event management flow\nremove iterating all session in main loop\nremove eaptls from main loop\nAdd cache statistics\nreduce number of DHCP packet parses\nDo not process DHCP directly. Also forgotten insert_packet logic\nremove double checking of tcp timestamps due to merge\nremove iteration on users list inside data path\nrefain from accessing session dict multiple times\nDo some processing on the AP-system\ncommented out cache time measurements\nstream_endpoints writing pickles to redis queue\nadd debug and extentions recommendations\ncleanups\nfix term color bug\nraise Empty exception\nstart data processor\nseparate processes\nseparate process\nremove iteration of all sessions for reclassification\nHandle model update inside training thread\nremove ModelUpdate event\nremove session cache\nFor now just DHCP packets in handle_connected for fingerbank because it's eats too much time\ncomments out logging that are dependent on number of devices\nupdate intervals\nAdd KPIs\n\n"}
{"title": "Fix unpacking", "number": 748, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/748", "body": "\u001b[36mclassifier_1      |\u001b[0m self.run()\n\u001b[36mclassifier_1      |\u001b[0m   File \"/usr/local/lib/python3.8/threading.py\", line 870, in run\n\u001b[36mclassifier_1      |\u001b[0m 2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device 5e029c3bff hostname Galaxy-s8-31\n\u001b[36mclassifier_1      |\u001b[0m 2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device cd56a7a23e hostname lableft\n\u001b[36mclassifier_1      |\u001b[0m 2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device 9156f8a569 hostname Galaxy-S8-29\n\u001b[36mclassifier_1      |\u001b[0m     self._target(*self._args, **self._kwargs)\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/data_processor.py\", line 1343, in process_data\n\u001b[36mclassifier_1      |\u001b[0m     2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device cc3c696da6 hostname Shimon-Galaxy-S8\n\u001b[36mclassifier_1      |\u001b[0m 2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device 822a92c505 hostname Galaxy-S8-num5\n\u001b[36mclassifier_1      |\u001b[0m ret_periodic_state = state.periodic_state_management(transient_id, state_enum_to_state)2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device 702bff4e12 hostname raspberrypi\n\u001b[36mclassifier_1      |\u001b[0m\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 732, in periodic_state_management\n\u001b[36mclassifier_1      |\u001b[0m 2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device 6050a371da hostname Nuriels-MBP\n\u001b[36mclassifier_1      |\u001b[0m     state_change: Optional[Tuple[ClassificationStatus, None]] = self._periodic_state_management_internal(user, state_enum_to_state)\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 713, in _periodic_state_management_internal\n\u001b[36mclassifier_1      |\u001b[0m     return self._coarse_id_decision(cap, dhcpfp, dhcpv6_duid, given_ipv4, hw_mac, rand_mac, requested_ipv4, session,\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 842, in _coarse_id_decision\n\u001b[36mclassifier_1      |\u001b[0m     if device_record := self.get_device_from_db_by_coarse_identifiers(user, session.connection_timestamp, ssid, device_caps_str, device_dhcp_str, dhcp_hostname,\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 292, in get_device_from_db_by_coarse_identifiers\n\u001b[36mclassifier_1      |\u001b[0m     2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device e3915524c6 hostname DanielsWorkMBP\n\u001b[36mclassifier_1      |\u001b[0m if matching_device := self.filter_devices_by_dhcpv6_identifiers_win10(devices_set, device_dhcpv6_duid):\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 140, in filter_devices_by_dhcpv6_identifiers_win10\n\u001b[36mclassifier_1      |\u001b[0m     2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device b0857b5f13 hostname Michaels-MBP-2\n\u001b[36mclassifier_1      |\u001b[0m for stored_duid, stored_device in iterate_devices_params(devices_set_tmp_copy, tup):\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 69, in iterate_devices_params\n\u001b[36mclassifier_1      |\u001b[0m 2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device d2ca184a28 hostname Galaxy-S8-32\n\u001b[36mclassifier_1      |\u001b[0m     2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device bcf5b2e885 hostname DESKTOP-6459N4F\n\u001b[36mclassifier_1      |\u001b[0m not_none_in_session_group = tuple(identifier_value is not None for _, identifier_value in identifier_group)\n\u001b[36mclassifier_1      |\u001b[0m   File \"/root/src/ccpilot/processes/states/just_connected_state.py\", line 69, in \n\u001b[36mclassifier_1      |\u001b[0m     2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device 9c67c47025 hostname Galaxy-S10\n\u001b[36mclassifier_1      |\u001b[0m not_none_in_session_group = tuple(identifier_value is not None for _, identifier_value in identifier_group)\n\u001b[36mclassifier_1      |\u001b[0m ValueError: too many values to unpack (expected 2)2021-01-28 16:22:50.453  [just_connected_state.py:283] Candidate device e62355af99 hostname J7-no13\n\n"}
{"title": "Should pass only the iterable of tuples of (attribute name, attribute value)", "number": 749, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/749", "body": ""}
{"title": "Remove usage of scapy in capfp", "number": 75, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75", "body": "Sorry for branch name, my keyboards broken"}
{"comment": {"body": "Does this fix the backlog?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75/_/diff#comment-147844473"}}
{"comment": {"body": "Nope, but it\u2019s capped to 100 for some reason and only happened once", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75/_/diff#comment-147844545"}}
{"comment": {"body": "? We only start warning about backlog when we reach 100\\+", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75/_/diff#comment-147844852"}}
{"comment": {"body": "Highest I get is ~120 and it\u2019s infrequent", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75/_/diff#comment-147845077"}}
{"comment": {"body": "Alright", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75/_/diff#comment-147845145"}}
{"comment": {"body": "Other features still need to create scapy objects, right?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75/_/diff#comment-147845244"}}
{"comment": {"body": "They might, not sure.\n\nI haven\u2019t removed any scapy functionality \\(still able to convert our radiotaps to theirs\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75/_/diff#comment-147845955"}}
{"comment": {"body": "Alright. Maybe it will be wise to cache the scapy conversion so it doesn\u2019t happen again and again for every feature. Irrelevant to this PR though", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75/_/diff#comment-147846328"}}
{"comment": {"body": "Add these tests to Jenkinsfile? Nobody is running them ATM \\(I think\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/75/_/diff#comment-147846921"}}
{"title": "Add six Engenius routers", "number": 750, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/750", "body": "Add configuration for new APs. Also added logic to enable multiple hostnames (addresses) per SSH host based on nc scanning. Later we noticed that it, quite predictably, makes setup much slower, and can even fail it sometimes. So it is currently only used for the american hawkeye, which is the only AP that has a LAN port mapped by default (all other APs have their 2nd Ethernet port mapped to br-demo (QCA) or dont have a 2nd port at all (Engenius)).\n"}
{"title": "Wrong walrus usage in DNS", "number": 751, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/751", "body": "Mistake in walrus use, caused wrong print + wrong comparison with existing values"}
{"title": "Remove old duplicate AP config files", "number": 752, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/752", "body": ""}
{"title": "Common packages were missing on recsys docker after merge", "number": 753, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/753", "body": ""}
{"title": "Update generated files after PR-753", "number": 754, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/754", "body": ""}
{"title": "Access data fix - dns type classification", "number": 755, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/755", "body": "We had the following exception in bunny: \nTraceback (most recent call last):\n[36mclassifier_1      |[0m   File \"/root/src/ccpilot/processes/data_processor.py\", line 1402, in process_data\n[36mclassifier_1      |[0m     self._process_incoming_event(event, event_map, mitm_arp_detector, cooperation_manager, state_enum_to_state, users)\n[36mclassifier_1      |[0m   File \"/root/src/ccpilot/processes/data_processor.py\", line 1423, in _process_incoming_event\n[36mclassifier_1      |[0m     handler(event)\n[36mclassifier_1      |[0m   File \"/root/src/ccpilot/processes/data_processor.py\", line 649, in handle_dns_device_type_classification\n[36mclassifier_1      |[0m     if (child := dns_fingerprinting.compare_os_names_equal_or_nested(stored_os, session_os)) and child.name == session_os:\n[36mclassifier_1      |[0m AttributeError: 'str' object has no attribute 'name'\nSince dns_fingerprinting.compare_os_names_equal_or_nested returns a string, we don't need to access the name field and we can just use it as it is"}
{"title": "Check inner dhcpfp fields before moving on", "number": 756, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/756", "body": "After investigating the logs from bunny, we can see that we had two devices that created a double model.\nWhen looking at the logs, we can see the following interesting things. First, a second model was created due to non matching dhcp:\nIgnoring device due to mismatch DHCP. device_id: bb54273242, stored value: ,None, input value: 1,3,28,6,15,44,46,47,31,33,121,43,None\nThe problem is that the dhcp_str of the original device was None. When looking at the data that we got from the cache, we can see the following state:\nReturn from caches caps_model None dhcp ([], None, 2214690247) requested_ip None dhcpv6 None mdns_hostname None bonjour_uid None icmp_rx_ts_delta_list None syn_timestamps None user_agents [] dns_queries set() icmpv6 []\nThe problem is that in such case (empty dhcp vendor), well just move in the preliminary condition on and wont stop because the dhcp itself is not None. But a None dhcp vendor will cause a None device_dhcp_str, which will cause such problems."}
{"comment": {"body": "This is not correct. dhcp\\_vendor can be None.\n\nFor example for all Apple products it will be the case.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/756/_/diff#comment-203425047"}}
{"comment": {"body": "Can you extract from the captures the DHCP packet that caused this scenario?  \nI want to take a look on it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/756/_/diff#comment-*********"}}
{"title": "SNA fixes", "number": 757, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/757", "body": ""}
{"comment": {"body": "let\u2019s add flag to enable disable the android hostnames", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/757/_/diff#comment-*********"}}
{"comment": {"body": "Sure.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/757/_/diff#comment-*********"}}
{"title": "Better traceability for fingerbank logs", "number": 758, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/758", "body": "Add operation ID to each fingerbank query and use it in logging so we can keep trace of queries vs. responses in a multiple-device environment. This is just an improvement until we have proper logging."}
{"title": "SNA performance quick fixes", "number": 759, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/759", "body": ""}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} those are the changes currently in sna?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/759/_/diff#comment-*********"}}
{"comment": {"body": "The essence is the same.\n\nIn SNA I made much dirty by just deletion all the debug logs and not using a flag.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/759/_/diff#comment-*********"}}
{"title": "Add formatting for the logging module", "number": 76, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/76", "body": ""}
{"title": "Itai/scale and performance", "number": 760, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/760", "body": "New profile for no IPv6\nNew profile for no IPv6\nBonjour is mandatory when no IPv6\nWTF????\nremoved old files\nicmpv6_ns_wait_criteria should not be enabled when no IPv6\nUsing android 10 hostnames as long as they are not the default hostname\nremove from LFS\nCommited as non LFS files\nOops. Forgot to commit\nWTT no. 2\nOnly wait for bonjour UID on implicit iphone/ipad behaviour\nHW_MAC rule extend to android 10\nClear device type resolved state on new connection + debug prints\nAdd operation ID to each fingerbank query so we can keep trace of queries vs. responses in a multi-device environment. This is just an improvement until we have proper logging\n\n"}
{"comment": {"body": "This logic should be fixed with more robust logic and levlfingerbank proxy service", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/760/_/diff#comment-*********"}}
{"title": "Fix metrics load and add partition name dummy to work with dashboard", "number": 761, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/761", "body": ""}
{"title": "SNA - Ease UI load", "number": 762, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/762", "body": "Basic changes to relief load - increase sampling interval on client side, decrease ui per-device log size to 10"}
{"comment": {"body": "How much better the UI will be now? ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/762/_/diff#comment-*********"}}
{"comment": {"body": "This is what we want to check.. update SNA and see how it works..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/762/_/diff#comment-*********"}}
{"title": "reduce caching in flask to 5sec", "number": 763, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/763", "body": "\n\nreduce caching in flask to 5sec\n\n"}
{"title": "Fix undefined session access", "number": 764, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/764", "body": "\n\nFix undefined session access\n\n"}
{"title": "Split up common.py to prevent IP leakage", "number": 765, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/765", "body": ""}
{"comment": {"body": "Pheww now we can remove dacite from setup.sh.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/765/_/diff#comment-204324351"}}
{"title": "Feature/project genesis phase 2", "number": 766, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/766", "body": "Integration of OneClass into genesis flow\n\nIncludes OCC dataset\n\n\n\nAdd cancel training/model flow\n\n\n"}
{"title": "Decision logging", "number": 767, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767", "body": "Add mechanism to save session-device linking events to DB with broad metadata to provide insight on the decision process\nTODO:\n\nDont let just_connected access the log structs directly, init everything in decision log and provide comfortable functions to update metrics.\nExtract some prioritized data to be indexed in DB.\nScripts for extracting data from DB, including whatever inferring is required from multiple fields, completing each other.\n\nWhat is saved?\n\nMost of the session data\nMost of the device data if exists\nDecision result, timestamp, duration.\nHow many entries it took to the periodic management state.\nHow many devices did we start with\nWhen was the matching device last connected to another session.\nWhat unique identifiers were tested and whether they matched.\nWhat solid identifiers were tested and how many devices were filtered by each.\nHow many devices were filtered by last seen / connectivity.\nHow many devices were filtered due to hw/rand MACs\nWhat weighted/prioritized identifers (param list) were used - keys, weights, how many devices (out of those remaining at this point) matched each, which attributes matched the winning device (if there is one).\nIntersecting parameters statistics - how many devices matched the identifiers, and an histogram of number of devices vs. number of intersecting attributes.\nHow many devices are being considered despite having None matching attributes.\nFinal decision statistics - an histogram of number of devices with their respective total score.\n\n"}
{"comment": {"body": "you can use `dataclass_json` module to do all that work for you", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204348549"}}
{"comment": {"body": "We already don\u2019t use everything saved in the dataclass, and we will probably want to refine it further. I guess I could dict it and then pop stuff if needed.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204349533"}}
{"comment": {"body": "The decision log is very local to each `just_connected` iteration as it is not persistent. I suggest to save in the function stack.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204349904"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} What fields are not used? Can they be removed?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204350055"}}
{"comment": {"body": "linix\n\n![](https://bitbucket.org/repo/8X5z9dk/images/1301358715-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204350565"}}
{"comment": {"body": "There\u2019s aggregated info on consecutive calls and I believe there will be more as we expand it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204350586"}}
{"comment": {"body": "can we write the counters to telegraf as well?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204351122"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Complex stuff under the session mainly.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204351159"}}
{"comment": {"body": "I would just use the log once it is finalized.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204351416"}}
{"comment": {"body": "Can you make sure the decision\\_id is printed to the logs so we can link it to the entry in this table, while debugging?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204351880"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Then we should split into persistent and transient. Other than `state_mgmt_entries`, the fields are rewritten at each `just_connected` call", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204352344"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Also, let\u2019s do a brainstorm if you think this is going to expand?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204352530"}}
{"comment": {"body": "hmmm it was basically a DB only field\u2026 I\u2019ll look up how if I can read it. If not, maybe I\u2019ll just create it myself.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204352610"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Agree and agree", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204352829"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} The log is for detailed debug, the metrics can give us real time stats on which logics working most of the time and etc\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204352991"}}
{"comment": {"body": "@{5f82bf320756940075db755e} The metrics being collected here only take a few milliseconds \\(it has a session scope, and more so a `get_device_from_db_by_coarse_identifiers` scope\\). Once the decision is done, we can send from the `LogDecision` class just the subset of metrics we\u2019re interested in in monitoring.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204353979"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Probably self generated will be best", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/767/_/diff#comment-204355897"}}
{"title": "Revmoing bonjour_uid from our mandatory condition for now", "number": 768, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/768", "body": "After our recent giving-up sessions in sna, the bonjour_uid feature is removed from our mandatory condition for now."}
{"title": "Filter out APs, routers, switches, etc", "number": 769, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/769", "body": "In order to decrease the amount of devices sent and presented in our SNA servers dashboards, we can filter out any of the less interesting devices. The main goal for now is to have a quick solution that would lower the amount of data that our dashboard handles. Later on, it will probably be a filter option in the UI, sent by another endpoint or something similar"}
{"title": "Fix backlog and run-time issues", "number": 77, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/77", "body": "Removed all multiprocessing from the code. Swapped it with threading. inter-process communication was extremely wasteful and accounted for 90% of the run-time. \nFixed DB locking issues\nStop training/classification feature once it reaches a final states. remove commented code\n\n"}
{"title": "SNA - Not presenting devices that were not active in the last 24 hours", "number": 770, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/770", "body": "After filtering our APs, routers and switches, we can also filter out devices that were not active in the last 24 hours and lower even more the amount of data sent to the dashboard."}
{"comment": {"body": "`timedelta` has `days` parameter :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/770/_/diff#comment-*********"}}
{"comment": {"body": "oops it\u2019s probably better that way\n\nupdating :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/770/_/diff#comment-*********"}}
{"title": "Revert \"Merged in decision_logging (pull request #767)\"", "number": 771, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/771", "body": "This reverts commit 75222649661cb1113ffd3c72a1d9231f64c2f918, reversing changes made to ****************************************."}
{"title": "Add total and listed devices in UI - NOT TO MERGE YET!", "number": 772, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/772", "body": "\n\nAdd total and listed devices in UI\n\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/1944454733-Screen%20Shot%202021-02-08%20at%2023.39.46.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/772/_/diff#comment-204480647"}}
{"comment": {"body": "what? there are no 24 minutes in a day", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/772/_/diff#comment-204564217"}}
{"comment": {"body": "Good catch, I have tested with minutes and left it there\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/772/_/diff#comment-204565225"}}
{"title": "Decision logging", "number": 773, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/773", "body": "Add mechanism to save session-device linking events to DB with broad metadata to provide insight on the decision process\nTODO:\n\nDont let just_connected access the log structs directly, init everything in decision log and provide comfortable functions to update metrics.\nExtract some more prioritized data to be indexed in DB.\nScripts for extracting data from DB, including whatever inferring is required from multiple fields, completing each other.\nOnce we go into coarse_id_decision, hold everything in stack. No need to rely on session. But keep option for inter-periodic-management-cycle data.\nSend relevant metrics using telegraf.\n\nWhat is saved?\n\nMost of the session data\nMost of the device data if exists\nDecision result, timestamp, duration.\nHow many entries it took to the periodic management state.\nHow many devices did we start with\nWhen was the matching device last connected to another session.\nWhat unique identifiers were tested and whether they matched.\nWhat solid identifiers were tested and how many devices were filtered by each.\nHow many devices were filtered by last seen / connectivity.\nHow many devices were filtered due to hw/rand MACs\nWhat weighted/prioritized identifers (param list) were used - keys, weights, how many devices (out of those remaining at this point) matched each, which attributes matched the winning device (if there is one).\nIntersecting parameters statistics - how many devices matched the identifiers, and an histogram of number of devices vs. number of intersecting attributes.\nHow many devices are being considered despite having None matching attributes.\nFinal decision statistics - an histogram of number of devices with their respective total score.\nData extracted from caches\nconnection timestamp\n\n"}
{"title": "Make search not stuck on large number of devices by applying search predicate only on ENTER key press", "number": 774, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/774", "body": ""}
{"comment": {"body": "The fact that this is also how we clear the search is not the best UX. But is wasn\u2019t that good beforehand either.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/774/_/diff#comment-204569910"}}
{"comment": {"body": "We probably need a small \u201cX\u201d button to clear the search results.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/774/_/diff#comment-204571759"}}
{"title": "Add previous mac address to the device in UI so it can searchable", "number": 775, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/775", "body": ""}
{"title": "Sna/add flag to filter devices by time", "number": 776, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/776", "body": "Added FILTER_UI_DEVICES_BY_TIME flag to our docker-compose to determine whether we want to filter out devices or not by their last seen time. Set it to False now, so we do show them by default. Note that we still filter our routers, APs, etc.\n(Bonus - itll make our future merge with master easier)\n"}
{"comment": {"body": "Is this for internal use or intended for any client configuration?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/776/_/diff#comment-204607063"}}
{"comment": {"body": "As for now, we want to show all devices in the SNA dashboard. Maybe in the future we\u2019ll decide on specific configuration for each of our clients", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/776/_/diff#comment-204607712"}}
{"title": "Merge SNA fixes to master", "number": 777, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777", "body": "SNA quick fixes\nUsing VERBOSE config\ndocker config\nOnly verbose prints in print_stats\nskip unrelevant stuff for non-AP based system\ndisable last connected filtering logic\nfix\nremove debug print\nrevert package-lock.json\nrevert\nVerbose flag by type of deployment\ndisable verbose\nFix metrics load and add partition name dummy to work with dashboard\nBasic changes to relief load - increase sampling interval on client side, decrease ui per-device log size to 10\nreduce caching in flask to 5sec\nReduce size of history logs query\nAllow 2 sec timeout for HTTP request\nfilter_devices_by_dhcpv6_identifiers_win10 wasn't changed to be not static\nFix undefined session access\nAdd mechanism to save session-device linking events to DB with broad metadata to provide insight on the decision process\nDon't access device when there's no device\nSplit up common.py\nDeviceAuthStatus back to common.py\nsmall fixes\nlast fixes for now\nRevmoing bonjour_uid from our mandatory condition for now\nAdd connection_timestamp as indexed field, add data extracted from cache, set decision_id and write it to log upon save\nNow showing routers, aps, switches, etc in the UI\nMoved the logic to a proper method\nMissing ,\ndevice_is -> is_device\nNot presenting devices that were not active in the last 24 hours\n\nRevert \"Merged in decision_logging (pull request #767)\"\nThis reverts commit 75222649661cb1113ffd3c72a1d9231f64c2f918, reversing changes made to ****************************************.\n\n\nFix merge mistake in struct/Session, set default jsonification method for caches data\n\nApply search filter only on ENTER key press\n\nRevert \"Revert \"Merged in decision_logging (pull request #767)\"\"\nThis reverts commit 8cb176e175c6ae161bf73211741a7e103dbd9eab.\n\n\nAdd previous mac address to the device in UI so it can searchable\n\ntypo\nAdded MINIMIZE_DISPLAYED_DEVICES_UI flag to docker-compose to determine if we want to filter out devices or not, just to make the future merge with master to be easier\nDefault to false\nNow filtering only by time\ndecision_log table was not created on upgrade\nLimit the number of previous mac addresses to return\nEnable serializing of dicts with dataclasses keys\n\n"}
{"comment": {"body": "Change to param: \n\nSwitch - 5sec\n\nAP - 1 sec", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-204669292"}}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/919932347-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-204671176"}}
{"comment": {"body": "This should be based on the same mechanism that will be implemented in response to [https://bitbucket.org/levl/comcast/pull-requests/777#comment-204669292](https://bitbucket.org/levl/comcast/pull-requests/777#comment-204669292){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-204959654"}}
{"comment": {"body": "I don\u2019t really get the VERBOSE method here.   \nWe\u2019re just masking `logging.info` when we can just raise the logging level. Or we can change the logs we want to mask to `logging.debug`.\n\nLeaving it as-is is quite unpleasant. To change the logs level is just 1 liner.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/777/_/diff#comment-204961738"}}
