{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155812168", "body": ""}
{"comment": {"body": "debug printing for view trace. I'll remove this soon\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/883c596e-c28b-4d80-be2d-45d34af3b032?message=3cb19440-50e2-4ed8-ae8b-4d904461a05c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1005181260"}}
{"title": "Change 'discussions' to 'insights' in Hub", "number": 3422, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3422", "body": "\n\n"}
{"comment": {"body": "I think we can just say \"Open Insights in...\"", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3422#issuecomment-1291221351"}}
{"comment": {"body": "> I think we can just say \"Open Insights in...\"\r\n\r\nUpdated!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3422#issuecomment-1291223881"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3422#pullrequestreview-1155652746", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3422#pullrequestreview-1155653183", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3422#pullrequestreview-1155653914", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3422#pullrequestreview-1155654174", "body": ""}
{"title": "Dont do it in the constructor", "number": 3423, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3423"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3423#pullrequestreview-1155654289", "body": ""}
{"title": "Add slack channel selection on load", "number": 3424, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3424", "body": "A few problems needed to be solved:\n1. State changes are not causing re-renders on dropdown.\n2. Need to make loading text sticky on bottom of list."}
{"comment": {"body": "Closing this one as a dupe for https://github.com/NextChapterSoftware/unblocked/commit/ebab3227d3350af036bb626a269a3e9a7b8e59d2", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3424#issuecomment-1292630517"}}
{"comment": {"body": "More like an inspiration :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3424#issuecomment-1292636235"}}
{"title": "Remove log attribute", "number": 3425, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3425"}
{"comment": {"body": "Thank you!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3425#issuecomment-1291245524"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3425#pullrequestreview-1155672755", "body": ""}
{"title": "Setup basic web navigation for repo references", "number": 3426, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426", "body": "Generate and open urls for RepoFileRefernces on web.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#pullrequestreview-1155681836", "body": ""}
{"comment": {"body": "We should try sending repoID back from source mark so we can support source marks from multiple repos", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#discussion_r1005069095"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#pullrequestreview-1155682133", "body": ""}
{"comment": {"body": "RepoData is necessary to generate URLs from relative paths.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#discussion_r1005069366"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#pullrequestreview-1157239193", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#pullrequestreview-1157240085", "body": ""}
{"comment": {"body": "We don't need to dedupe the other reference types?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#discussion_r1006153698"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#pullrequestreview-1157244889", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#pullrequestreview-1157245393", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#pullrequestreview-1157264755", "body": ""}
{"comment": {"body": "Note for the future: we're using this pattern in a couple of places now, will be nice to refactor this into its own component so we don't need to repeat the styles and icon import etc in different places", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3426#discussion_r1006169839"}}
{"title": "TopicService: Add identity to TopicModel + API Test", "number": 3427, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3427", "body": "Now I just need to figure out how to run this new test by itself, it isn't clear to me how TopicApiDelegateImplTest is getting run. \nTest with Gradle only seems to run the compat test?"}
{"title": "Do not cache shadowjar", "number": 3428, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3428", "body": "Fixes problem with zip64 causing caching to go bork bork because the files are so large.\nDisable zip64 jar caching."}
{"title": "Use new FileMark-supported thread and message APIs", "number": 3429, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429", "body": "Allows creation of a Blue Bubble with arbitrary numbers of FileMarks\nGZIP encoded snippets for blue bubbles"}
{"comment": {"body": "@jeffrey-ng could you jump on and help test this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#issuecomment-1291441987"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1155764559", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1155782966", "body": "can someone say bresninator?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1156887633", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1156888587", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1156890413", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1156892168", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1156894823", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1156900515", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1156902883", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1156907116", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1158609833", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1158630852", "body": ""}
{"comment": {"body": "We should have the real snippet for this? At least when creating a blue bubble in VSCode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#discussion_r1007113814"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1158632712", "body": ""}
{"comment": {"body": "What\u2019s it used for? We don\u2019t even have messages on this overlay, so why do we need snippets?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#discussion_r1007115058"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1158634247", "body": ""}
{"comment": {"body": "Isn\u2019t this just for rendering the sidebar item?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#discussion_r1007116096"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1158694358", "body": ""}
{"comment": {"body": "If we are rendering the overlay (if we haven't yet fetched the actual thread from the service) this will be used to render in the Sidebar, but will also be used to render in the discussion view.  So it might be visible for a brief window of time if you display the thread immediately after creation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#discussion_r1007157500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1158763088", "body": ""}
{"comment": {"body": "Ok, but you could say the same about messages, so why are they empty?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#discussion_r1007199351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1158862601", "body": ""}
{"comment": {"body": "I could dig up the history for this but I'm not sure why the messages would be empty.  I would have expected the overlay would contain the initial message content.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#discussion_r1007243126"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1158980329", "body": ""}
{"comment": {"body": "I tested this quite a bit. No matter how quickly I navigate to the discussion it appears fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#discussion_r1007322304"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#pullrequestreview-1159026694", "body": ""}
{"comment": {"body": "I should have made this clear -- when you display a thread, the client will always ensure that the latest ThreadInfo is loaded and eventually displayed, so the worst case is that the overlay data is displayed for a very short period of time.  I do think it's probably not worth worrying about much for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3429#discussion_r1007360360"}}
{"title": "FixStuff2", "number": 343, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/343", "body": "Fix version"}
{"title": "Reduce jar size for standford models", "number": 3430, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3430"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3430#pullrequestreview-1155777041", "body": "Thank you!"}
{"title": "Disable docker caching", "number": 3431, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3431"}
{"title": "Trigger on workflow change", "number": 3432, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3432"}
{"title": "Allow updates from helper process on Ventura", "number": 3433, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3433", "body": "We need to get this out to customers as quickly as possible so that we catch them before they update to Ventura. Apple makes the claim that apps signed by the same team should be able to update each other without jumping through this hoop, but that's clearly wrong, or at least there's more to the story (probably sandboxing issues)\nOnly place I could find where this is documented:\n"}
{"comment": {"body": "> Simply add the NSUpdateSecurityPolicy you want to allow. Within NSUpdateSecurityPolicy, add \u201cAllowProcesses\u201d, a dictionary mapping team identifiers to an array of signing identifiers.\r\n\r\nfrom the transcript.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3433#issuecomment-1291503174"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3433#pullrequestreview-1155860066", "body": ""}
{"comment": {"body": "Our WWDR team ID. This whole thing just feels sooooo janky and slammed in at the last minute. It wasn't even in the Betas...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3433#discussion_r1005220189"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3433#pullrequestreview-1155864901", "body": "Makes sense. I see no risk."}
{"title": "Add AllowedPackages to security exception so installer can do its thing", "number": 3434, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3434"}
{"title": "Bumpity Bump", "number": 3435, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3435"}
{"title": "Try Apple's broken-looking configuration for NSUpdateSecurityPolicy", "number": 3436, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3436"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3436#pullrequestreview-1156874372", "body": ""}
{"comment": {"body": "The documentation specifies adding wrapper dictionary around the array of app ids", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3436#discussion_r1005889541"}}
{"title": "Bumpity Bump", "number": 3437, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3437"}
{"title": "Revert", "number": 3438, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3438"}
{"title": "Verification bump", "number": 3439, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3439"}
{"title": "Fix header params being included in locations", "number": 344, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/344"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/344#pullrequestreview-882276853", "body": ""}
{"title": "Add stream for slack channels loading state", "number": 3440, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3440", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3440#pullrequestreview-1157071357", "body": ""}
{"comment": {"body": "Freaking sweet.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3440#discussion_r1006034528"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3440#pullrequestreview-1157072382", "body": ""}
{"comment": {"body": "Sweet again :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3440#discussion_r1006034945"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3440#pullrequestreview-1157076834", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3440#pullrequestreview-1157226887", "body": ""}
{"title": "Show multiple FileMarks in admin web", "number": 3441, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3441"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3441#pullrequestreview-1157180344", "body": ""}
{"title": "Create PullRequestTopicHistogramService", "number": 3442, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3442"}
{"title": "Trying all the things for install fix", "number": 3443, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3443", "body": "Does the following:\n1. Adds the app group to all embedded apps\n2. Adds the team-identifier entitlement  to all the apps\n3. Adds the NSUpdateSecurityPolicy exception to all the apps\n4. Sets all apps to be UI agents and drops the LSBackground setting for the helper app"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3443#pullrequestreview-1157121936", "body": ""}
{"comment": {"body": "Re-arranged some things here. Now we'll kill the hub before we attempt to write over it with the new package. We probably should have done this from the start", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3443#discussion_r1006075919"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3443#pullrequestreview-1157122576", "body": ""}
{"title": "Show recommended peers in admin web", "number": 3444, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3444", "body": "Just playing around social network model to show recommended peers.\nMight drive some funtionality in future, might go nowhere."}
{"title": "add more reader instances", "number": 3445, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3445", "body": "We currently have a t3.medium with an older copy of prod DB setup for ML work. The team needs access to latest data. Looking at the replica instance, its CPU usage doesn't exceed 15% so instead of spending money on something that is barely used we should add another read replica. \nPusher has been hammering the read replica so by doing this for a price difference of 2 cents an hour we double our read-only capacity. Also the team gets access to latest data for their research queries"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3445#pullrequestreview-**********", "body": ""}
{"title": "Implement getRecommendedTopics", "number": 3446, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3446"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3446#pullrequestreview-**********", "body": ""}
{"title": "Update dashboard routes with insights", "number": 3447, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3447", "body": "Replace /discussions routes with /insights\nAdd Redirects for old routes \nFormat routing better leveraging the Outlet / component"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3447#pullrequestreview-**********", "body": ""}
{"title": "Add Unconnected Permissions State", "number": 3448, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448", "body": "Setup VSCode to reconnect to video app after permissions mode are validated.\n"}
{"comment": {"body": "@pwerry Updated to address the comments we had IRL.\r\n* Permission state is now generated from SystemPreferencesManager\r\n* On stream close, video app will now force close itself.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#issuecomment-**********"}}
{"comment": {"body": "The TS code makes sense, but I think the fact that the state (of both the connection and the walkthrough) is spread across so many models makes me think that we should be reorganizing the code a bit.  I think we should move the state out of the commands and into one class that can manage the entirety of the state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#issuecomment-1297406444"}}
{"comment": {"body": "> The TS code makes sense, but I think the fact that the state (of both the connection and the walkthrough) is spread across so many models makes me think that we should be reorganizing the code a bit. I think we should move the state out of the commands and into one class that can manage the entirety of the state.\r\n\r\nI think I agree with this. On the swift side, you could potentially encapsulate the state and its valid transitions in an `enum`, and then localize all of the state management code to the GRPC block responsible for the bidirectional stream handling.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#issuecomment-1297414903"}}
{"comment": {"body": "Makes sense. Let me get in the other PRs first.\r\nA good time to do some refactoring that Matt suggested a few PRs ago which should handle all the feedback.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#issuecomment-1297678100"}}
{"comment": {"body": "Refactored to move all the VSCode state within `WalkthroughManager`\r\n\r\nIn the Video app, all the state should already be held within a single CVS `WalkthroughIPCService.sessionPublisher`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#issuecomment-1299320119"}}
{"comment": {"body": "Swift side looks ok to me. Will leave it to @matthewjamesadam to give the thumbs up on the VSCode side", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#issuecomment-1301275392"}}
{"comment": {"body": "@matthewjamesadam Hoping to merge this in today so we can get a build out for dogfooding.\r\n\r\nCurrently a bug that prevents video app initial launch that this PR should fix.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#issuecomment-1302366139"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1162233626", "body": ""}
{"comment": {"body": "Doesn't this mean that both groups have exited? In other words, the network stream will have died but the channel state publisher will still be alive, so this will never get hit right? I suspect this needs to be solved via some kind of cancellation or flag.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#discussion_r1009656929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1162235293", "body": ""}
{"comment": {"body": "This could potentially get called many times with the same value. Is that ok?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#discussion_r1009658179"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1162239024", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1162242218", "body": ""}
{"comment": {"body": "So the idea is that the video app sets the state to `requiresPermissionRestart`, then closes the connection (shuts down), and VSCode is supposed to track that state and re-start the app?  Does the video app know for sure that VSCode has received this state before it shuts down (I haven't dug into the swift code too much yet, so maybe that's the case?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#discussion_r1009662562"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1162250613", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1164048759", "body": ""}
{"comment": {"body": "It should be okay. Would just send unnecessary duplicate events to VSCode. Added `removeDuplicates()` to fix situation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#discussion_r1010900584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1164077702", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1164083074", "body": ""}
{"comment": {"body": "The video app is *not* aware that VSCode has received the event.\r\nWe could have VSCode reply back to the Hub but what benefit does that provide?\r\n\r\nThis `requiresPermissionRestart` state is triggered the moment the Hub launches the permissions view. Hub restarting is also handled by the system, not hub itself...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#discussion_r1010925013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1164139873", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1168960656", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1168962844", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3448#pullrequestreview-1168997584", "body": "Some suggestions we discussed:\n* Moving the WalkthroughManager to be more of a walkthrough instance, that is created and manages the state for a single video walkthrough instance\n* That WalkthroughManager should be the one that passes the references to the API, instead of the ActiveFileManager doing that\n* Probably other things, I forget..."}
{"title": "Slightly different NSUpdateSecurityPolicy config", "number": 3449, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3449", "body": "Holding onto this if current configuration doesn't work"}
{"title": "Add treeview components", "number": 345, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/345", "body": "Since the existing VScode Tree View is pretty much unconfigurable, we are going to have to do our implementation.\nTo that end, this pr includes a basic subset of components for a TreeView Editor.\n\nA TreeItem which is a node item in the tree.\nA TreeItemCaret which indicates expansion of tree node.\n\nNext Steps:\n1. Planning to add a TreeView component.\n2. Planning on piggy-backing off existing VSCode node provider models for tree view.\n3. Planning on webview with this stuff.\n4. Styling.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/345#pullrequestreview-882291752", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/345#pullrequestreview-882336406", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/345#pullrequestreview-882345407", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/345#pullrequestreview-882352571", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/345#pullrequestreview-882353923", "body": ""}
{"title": "Bump", "number": 3450, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3450"}
{"title": "GET threads/mine is returning 500 if any of the threads are missing sourcepoints", "number": 3451, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3451", "body": "\n\nThe goal is to goal prevent API from throwing 500. I'll fix the core issue next."}
{"title": "Update create blue bubble forms", "number": 3452, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452", "body": "Remove title field from blue bubbles; double block content as title\nNOTE: There's some temporary client logic to handle this but this should ultimately be handled by the backend (linear task: )\n\n\nRemove the title field from the message editing form \nCollapse the code block in thread creation by default (only in vscode)\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1158943106", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1159065237", "body": ""}
{"comment": {"body": "If we're getting rid of titles, what purpose does this callback have?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#discussion_r1007394237"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1159068333", "body": ""}
{"comment": {"body": "Update API so that title is either optional or removed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#discussion_r1007396936"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1159100716", "body": ""}
{"comment": {"body": "Not totally getting rid of titles yet, only from the UI. i.e. we save the block content as the title so that it still gets rendered in different parts of the UI. This is a similar pattern we do with PR threads -- i.e. PR threads still have title fields as well", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#discussion_r1007421333"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1160477927", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1160479356", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1160479645", "body": "Just one comment."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1160501419", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1160783631", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3452#pullrequestreview-1160785191", "body": ""}
{"title": "Fix Snippet check failure", "number": 3453, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3453", "body": "HTTP request bomb results in VSCode hang, caused by PUT /logs, caused by sourcepoint snippet check failure\n"}
{"comment": {"body": "Already fixed.\r\nhttps://linear.app/unblocked/issue/UNB-713#comment-23b0f265", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3453#issuecomment-1293028147"}}
{"title": "Reduce chunk size", "number": 3454, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3454"}
{"title": "Throttle network logging", "number": 3455, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3455", "body": "Throttle the amount of logs we send over the network from VSCode, to a maximum of 5 per second for each context."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3455#pullrequestreview-1157428136", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3455#pullrequestreview-1157490807", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3455#pullrequestreview-1157514945", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3455#pullrequestreview-1159074482", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3455#pullrequestreview-1159083180", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3455#pullrequestreview-1159121660", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3455#pullrequestreview-1159151187", "body": ""}
{"title": "Remove debug logging", "number": 3456, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3456"}
{"title": "Add upsert functionality to Exposed and fix/optimize upserts", "number": 3457, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3457", "body": "The way we are doing upserts is plain wrong in the SQL world because we are not providing enough locking pragmas and using the correct isolation to ensure concurrent updates do not hit transactional conflicts (duplicate key conflicts)\nI actually noticed this out in the wild so it's not just theory.\n\nThe correct approach is to use the Postgres Insert On Conflict paradigm.\nTo achieve this:\n1. We add extension functions to do upserts.\n2. We updated the Entity Cache to handle upsert statements.\n3. We update some non-critical code (slack models) to test out this logic.\nThis highly optimizes the logic as we do not have to do a select/insert/fetch."}
{"title": "Drop chunk size to 5", "number": 3458, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3458"}
{"title": "Need more admin console memory", "number": 3459, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3459"}
{"title": "update", "number": 346, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/346"}
{"title": "udpate", "number": 3460, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3460"}
{"title": "AddHoneycomb", "number": 3461, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3461", "body": "udpate\nadd honeycomb to admin"}
{"title": "EV based feature flags for VSCode", "number": 3462, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3462", "body": "Temporary system to enable feature flags per team on VSCode"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3462#pullrequestreview-1158823985", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3462#pullrequestreview-1158988875", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3462#pullrequestreview-1159097394", "body": ""}
{"title": "Lemmatize and remove common words from topics send from client", "number": 3463, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3463", "body": "Applies the same common word filtering and lemmatization to the topics received from the client as we do to PR and slack content. \nThis means the response from the server may not be a strict subset of the inputs. For example if one the input topics is sourcemarks it will be returned as sourcemark by the server.\nAlso this will occur in the API server so we're using the same Mr. Stanford library as the admin console."}
{"comment": {"body": "Might need to find another library to lemmatize, one that isn't such a memory hog.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3463#issuecomment-1293908681"}}
{"comment": {"body": "Question.\r\nYou mention this is happening in the api service.\r\nIs this doing post-processing during service calls?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3463#issuecomment-1293909911"}}
{"comment": {"body": "This is ... doing it synchronously. If this is a concern we can not do this if @matthewjamesadam can do this on the client side.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3463#issuecomment-1293913990"}}
{"comment": {"body": "I don't think the client can do this because we'll get inconsistent lemmatization results", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3463#issuecomment-1293917118"}}
{"comment": {"body": "> LMK if you want help looking at alternative libraries\r\n\r\nYeah please, this library is a hog and was crashing our services", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3463#issuecomment-1293918737"}}
{"comment": {"body": "> This is ... doing it synchronously. If this is a concern we can not do this if @matthewjamesadam can do this on the client side.\r\n\r\nIt is if the post-processing is heavy-weight as it can affect concurrent api calls.\r\nMy concerns are primarily:\r\n1. The library efficiency\r\n2. The library memory load.\r\n\r\nI would like to understand those better.\r\nAlso, how frequently this call will be made.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3463#issuecomment-1293921633"}}
{"comment": {"body": "I'm okay to get this going but question whether this is the best long-term approach.\r\nI'm not entirely sure if this can be done as some sort of background scraper that regularly persists the data. \ud83e\udd37 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3463#issuecomment-1293932095"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3463#pullrequestreview-1158840311", "body": "LMK if you want help looking at alternative libraries"}
{"title": "Add basic top-level slack thread url", "number": 3464, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3464", "body": "Adding top level slack thread url.\nBoth client and backend."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3464#pullrequestreview-1158850433", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3464#pullrequestreview-1158851912", "body": ""}
{"comment": {"body": "tiny nit: feels like this can just be `slackUrl` since it's on a Thread object ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3464#discussion_r1007238838"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3464#pullrequestreview-1158852126", "body": ""}
{"title": "Fix CreateThread snippet persistence on point", "number": 3465, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3465", "body": "The new DB point should get the snippet from API point or the parent API mark."}
{"title": "Adding breakpoint for message top margin", "number": 3466, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3466", "body": "When we changed the message top margin for the wide viewport, the narrow viewport was also affected as a breakpoint didn't exist. This fixes it. It goes from 8pts to 2pts."}
{"comment": {"body": "This is already addressed in this PR:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/3452/files#diff-de6e147c2b659ff01606ad168df5864e5becdc634a5e2ff6222d0790dc7e90c1R42", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3466#issuecomment-1294079081"}}
{"comment": {"body": "Ah okay I'll close then.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3466#issuecomment-1294079924"}}
{"title": "trace logging", "number": 3467, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3467"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3467#pullrequestreview-1159086761", "body": ""}
{"title": "Restart walkthrough app after recording permissions granted", "number": 3468, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3468", "body": "Preamble\nThere doesn't seem to be a (documented) method of untangling the TCC subject from the host app. The impact is that it's Unblocked.app asking for permissions for Camera/Mic/Recording, and Unblocked Video.app asking for Accessibility permissions, even though it's really Unblocked Video.app actually using those permissions. Seems like a bug in TCC.\nSummary\nThis PR does 3 things:\n1. Polls the background for accessibility and recording permissions updates from the video app. We're doing this because on Monterey the video app can continue on without restarting once recording permissions are granted to the host app (Unblocked.app).\n2. On Ventura a restart is required, but it will restart the wrong app (Unblocked.app instead of Unblocked Video.app). This PR drops a default in shared user prefs that the video app needs a restart, then the hub picks that up when it restarts and restarts the video app\n3. A sandbox breakout is required to do this, so there's a temporary-exception entitlement added that is scoped to the video app.\nVerified that this works."}
{"comment": {"body": "To clarify, VSCode still needs to handle the situation where Video app restarts due to permissions being updated?\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/3473", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3468#issuecomment-1297478286"}}
{"comment": {"body": "> To clarify, VSCode still needs to handle the situation where Video app restarts due to permissions being updated?\r\n> #3473\r\n\r\nYes. This _ensures_ that the video app is restarted after recording permissions are set", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3468#issuecomment-1297479278"}}
{"comment": {"body": "FYI we are definitely heading towards no sandbox for the hub in the future. But there's a bunch of work needed to get this to work so I'm punting for now and just using sandbox exceptions where required...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3468#issuecomment-1297483086"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3468#pullrequestreview-1162387803", "body": ""}
{"title": "up api service", "number": 3469, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3469"}
{"title": "Fix unstable test", "number": 347, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/347"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/347#pullrequestreview-882301324", "body": ""}
{"title": "Fix broken dashboard redirect", "number": 3470, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3470", "body": "\nFix redirect links; should also default to redirecting back to index instead of Nothing to see here"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3470#pullrequestreview-1159096644", "body": ""}
{"title": "SourceMark resolution faulted incorrectly when a trusted point existed for a commit outside tree", "number": 3471, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3471", "body": "I previously introduced an optimization to skip sanitization of a mark (which is expensive) if\nwe had already sanitized at least one point in the repo. See #3309.\nHowever, if the sanitized point happened to be a commit that was subsequently forcibly\noverwritten by another commit (due to rebase, common for Graphite workflows) then the sanitizer\nskipped sanitization of points are actually in the tree. The SM engine then thought that there\nwere no trusted points in the tree.\nSimple fix; only skip point sanitization if we have already sanitized at least one point in\nthe tree, not repo.\n"}
{"title": "rename stuff", "number": 3472, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3472"}
{"title": "Backup video app launch", "number": 3473, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473", "body": "Backup launch command for Walkthrough App when open -b fails."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#pullrequestreview-1162267904", "body": ""}
{"comment": {"body": "Do none of these commands actually fail with an error code?  They all exit with code zero, but log stuff to stderr?  So strange.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#discussion_r1009679904"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#pullrequestreview-1162270616", "body": ""}
{"comment": {"body": "When is this needed?  It's worth a comment explaining the scenario this is actually used (ie, where `open -b` fails)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#discussion_r1009681720"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#pullrequestreview-1162271577", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#pullrequestreview-1162274401", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#pullrequestreview-1162278270", "body": ""}
{"comment": {"body": "I would just grab the first thing rooted in the `/Applications` directory, but we need to be mindful of the development environment where you might potentially have lots of these in DerivedData. Maybe we need a way to switch between these during development of the VSCode extension?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#discussion_r1009686359"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#pullrequestreview-1162278634", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#pullrequestreview-1162431837", "body": ""}
{"comment": {"body": "Open -b can fail the if the video app has never been opened.\r\n\r\nSomething about the bundle ID not registered. Correct me if I'm wrong @pwerry ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#discussion_r1009786557"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#pullrequestreview-1162432189", "body": ""}
{"comment": {"body": "Can we just open-and-close the thing automatically on install?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#discussion_r1009786789"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#pullrequestreview-1168967147", "body": ""}
{"comment": {"body": "We could do that but the logic would be pretty close to the same. The installer doesn't guarantee the location it actually drops things so any scripts would have to go hunting in the same way this code is working", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3473#discussion_r1014312046"}}
{"title": "Dont return single character topics", "number": 3474, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3474"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3474#pullrequestreview-1159150112", "body": ""}
{"title": "Video metadata file icons", "number": 3475, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3475", "body": "Added File icons.\nManually mapping file types -> Icons.\nUsing list from \ngroupings could be as broad as:\nimages\ncode\nvideos\nother files (blank)\n"}
{"comment": {"body": "If we cannot grab from VSCode, do we want to go with the generic approach everywhere?\r\n@benedict-jw \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3475#issuecomment-1294232198"}}
{"comment": {"body": "Updated to generic icons from FA.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3475#issuecomment-1297854876"}}
{"comment": {"body": "There are still changes to `package-lock.json` without any changes to `package.json` -- are these just minor build updates?  Or should we revert them?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3475#issuecomment-1298852052"}}
{"comment": {"body": "> There are still changes to `package-lock.json` without any changes to `package.json` -- are these just minor build updates? Or should we revert them?\r\n\r\nReverted", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3475#issuecomment-1298920732"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3475#pullrequestreview-1163833568", "body": ""}
{"title": "Backfill thread titles with block content if CreateThreadRequest.title is empty", "number": 3476, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3476"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3476#pullrequestreview-1160450759", "body": ""}
{"comment": {"body": "What would be a case where this falls back to `(no title)`? Is it dependent on the content type? i.e. if it's an image? \r\n\r\nRichie had a [good suggestion](https://linear.app/unblocked/issue/UNB-704#comment-3850867b) to add some sort of generic description in the case where the title is just made up of a content type that we cant translate into text. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3476#discussion_r1008328673"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3476#pullrequestreview-1160457755", "body": ""}
{"comment": {"body": "Almost all content can be converted to plain text, i.e. an image will be converted to the url. Probably for those types we should do something similar to what Richie suggested: `Image/Video from Richie`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3476#discussion_r1008332544"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3476#pullrequestreview-1160463339", "body": ""}
{"comment": {"body": "Yeah this isn't exclusive to our threads, we should probably have a general rule for all content types (i.e. PR threads, slack threads, etc. Especially pertinent for Slack threads because those assets are authed so having the image url as the title will have very little meaning)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3476#discussion_r1008336435"}}
{"title": "Bump chunk size up to 25", "number": 3477, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3477"}
{"title": "Increase slack polling", "number": 3478, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3478"}
{"title": "Add whitelist for slack UI", "number": 3479, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3479", "body": "enable for our team first, if it works as expected I'll make another PR to uncomment Expo's team"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3479#pullrequestreview-1159175511", "body": ""}
{"comment": {"body": "Call this `slackEnabledTeams` ?  Maybe doesn't matter?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3479#discussion_r1007477877"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3479#pullrequestreview-1159177585", "body": ""}
{"title": "Use correct java", "number": 348, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/348"}
{"comment": {"body": "Rashin's is better https://github.com/NextChapterSoftware/unblocked/pull/349\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/348#issuecomment-1039694108"}}
{"title": "Enable slack for expo team", "number": 3480, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3480"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3480#pullrequestreview-1159271479", "body": ""}
{"title": "Message mentions is slow", "number": 3481, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3481", "body": "Should be using bagtch insert\nSome slow queries"}
{"title": "Optimize user token", "number": 3482, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3482", "body": "Token doesn't really change unless user changes it.\nWith that in mind, We should be caching token."}
{"title": "Bump prod admin console memory", "number": 3483, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3483"}
{"title": "Be smart with mentions", "number": 3484, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3484"}
{"title": "Remote controlled client config", "number": 3485, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485", "body": "Proposal\nGet remote-controlled client config for the currently authorized user. This API operation takes no explicit parameters. The remote config service implicitly uses the authorized person, identity, and team(s) to control behaviour.\nThis API is user-centric, not team-centric in order to provide a consistent experience to the authorized user. The consequence for a user who is a current member of multiple teams is that property conflicts can occur for team properties. The remote config service resolves the conflicts deterministically.\nSample response\njson\n{\n  \"capabilities\": {\n    \"VideoFeatureEnabled\": true,\n    \"NetworkLoggingEnabled\": false\n  },\n  \"quantities\": {\n    \"NetworkLoggingLimitPerMinute\": 60,\n    \"ApiRetryBackoffMilliseconds\": 300,\n    \"MaxSidebarThreads\": 20\n  }\n}\nAdmin web changes\nNew Config page for global config. The Team and Person pages are also updated.\n\n"}
{"comment": {"body": "When would we call this?\r\n\r\n* Push-service based update?  I think this is unnecessary\r\n* Once on login, and cleared on logout?  This would have corner-case bugs (team membership may not match the flags) -- but maybe that is OK for now.\r\n* Every time we get a new token (I think this is strictly the most correct, as the config will match the user's memberships) -- in this case I'd vote we add this data onto `/person`, as we already call that API whenever the token updates, and it's already personalized.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#issuecomment-1295188993"}}
{"comment": {"body": "> When would we call this?\r\n> \r\n> * Push-service based update?  I think this is unnecessary\r\n\r\nAgree, pusher is overkill.\r\n\r\n> * Once on login, and cleared on logout?  This would have corner-case bugs (team membership may not match the flags) -- but maybe that is OK for now.\r\n\r\nGood call.\r\n\r\n> * Every time we get a new token (I think this is strictly the most correct, as the config will match the user's memberships) -- in this case I'd vote we add this data onto `/person`, as we already call that API whenever the token updates, and it's already personalized.\r\n\r\nI don't want to couple person and config, because they are separate logical concepts.\r\n\r\n\r\nHow about we refresh at:\r\n1. ~startup, obviously~\r\n2. logout/login\r\n3. periodically\u2014say once per hour\u2014so that config changes on the server are pushed to the client in a reasonable amount of time.\r\n\r\nUpdate(edit):\r\n1. on refresh\r\n2. throttled at most every hour", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#issuecomment-1295310988"}}
{"comment": {"body": "> 3. periodically\u2014say once per hour\u2014so that config changes on the server are pushed to the client in a reasonable amount of time.\r\n\r\nIf we pin anything privacy related to config this won't be frequent enough. Not saying we want to do that, but just to highlight that a long update period means we can't add any \"urgent\" values to config\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#issuecomment-1295326586"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160321566", "body": ""}
{"comment": {"body": "I do wonder if the backend should return a config manifest that is capable of describing configuration for various permutations of team/identity/repo etc. Then the client can make a decision about which one to use based on the current environment?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#discussion_r1008239234"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160324351", "body": ""}
{"comment": {"body": "I think that will complicate things and I'm not sure of the benefit we get.  We'd have to duplicate the resolution logic in multiple clients instead of having it in once place in the API endpoint.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#discussion_r1008241256"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160324401", "body": ""}
{"comment": {"body": "Does codegen provide type checking for the values? I've never seen this `additionalProperties` attribute before :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#discussion_r1008241286"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160328332", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160330041", "body": ""}
{"comment": {"body": "Ah nvm I see at least the Swift code does provide type checking", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#discussion_r1008245364"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160335584", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160340931", "body": ""}
{"comment": {"body": "The client isn\u2019t even aware of the identity, and would like to keep it that way. Trying to optimize for simplicity.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#discussion_r1008252699"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160437164", "body": ""}
{"comment": {"body": "Kotlin is certainly type-safe, haven't tried TS yet, but worst case we can explicitly cast the `capabilities` values to boolean, etc.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#discussion_r1008319500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160443558", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160444043", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160444098", "body": ""}
{"comment": {"body": "The generated TS code is fine, for example:\r\n```\r\n    capabilities: { [key: string]: boolean; };\r\n```\r\n\r\nwhich is an object mapping strings to booleans.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#discussion_r1008324328"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160468984", "body": ""}
{"comment": {"body": "Yup fair. The model should allow us to work around environment based snags by introducing additional context parameters", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#discussion_r1008340335"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160478085", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160480707", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160514439", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160566994", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160569620", "body": ""}
{"comment": {"body": "Given me a practical example of why we'd need this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#discussion_r1008385981"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1160833140", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1165978916", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1165982808", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1166027034", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3485#pullrequestreview-1166043856", "body": ""}
{"title": "Always make sure there are teams to nav", "number": 3486, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3486"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3486#pullrequestreview-1160460262", "body": ""}
{"title": "Optimize slack ingestion", "number": 3487, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3487", "body": "Determined that a large amount of time was used to get message permalink.\nThat permalink can easily be deterministically resolved once we have the base protocol and host information.\nCut local ingestion from 13 minutes to 4 minutes."}
{"title": "Save identity for topic", "number": 3488, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3488"}
{"title": "MinorCleanup", "number": 3489, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3489", "body": "Optimize slack ingestion\nupdate"}
{"title": "Add readme", "number": 349, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/349"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/349#pullrequestreview-882316362", "body": ""}
{"title": "update", "number": 3490, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3490"}
{"title": "Increase slack ingestion memory", "number": 3491, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3491"}
{"title": "Slack dashboard bug fixes", "number": 3492, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3492", "body": "Fix search results self-refreshing on search (refreshing itself to the full list despite the input value)\nAllow option to not close the dropdown on item click (i.e. for multiple selects)\nFix autofocus on the text search input"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3492#pullrequestreview-1160719160", "body": "Where there's a will, there's a kay!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3492#pullrequestreview-1160761459", "body": ""}
{"title": "Allow getRecommendedTopics when impersonating", "number": 3493, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3493"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3493#pullrequestreview-1160706759", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3493#pullrequestreview-1160711216", "body": ""}
{"title": "SlackPullRequests", "number": 3494, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3494", "body": "Should be parsing all slack pull requests in a message.\nOptimize pull requsts"}
{"title": "Revert \"Fix open api code gen (#3252)\"", "number": 3495, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3495", "body": "This reverts commit 24b51c7868e874a6cd059e475705b20652f4f57b.\nBroke the hub build."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3495#pullrequestreview-1160732894", "body": ""}
{"title": "Sort files", "number": 3496, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3496"}
{"title": "Let's slurp EVERYTHING", "number": 3497, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497", "body": "This attempts to flip the accessibility switch on every app that the user brings to the foreground and will slurp all the URLs.\nTested and confirmed working with:\n- Slack\n- Notion\n- Linear\n- VSCode\n- Safari"}
{"comment": {"body": "We will also need to think about the implications of the URLs for the feature.\r\n\r\nAre we sure we can open these links in the browser?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#issuecomment-1295619986"}}
{"comment": {"body": "> We will also need to think about the implications of the URLs for the feature.\r\n> \r\n> Are we sure we can open these links in the browser?\r\n\r\nI agree - I think when we're dogfooding it and weird things show up it will help us make decisions about what to include and not include. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#issuecomment-1295623421"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#pullrequestreview-1160830811", "body": ""}
{"comment": {"body": "Magic \ud83d\ude05", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#discussion_r1008565196"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#pullrequestreview-1160831055", "body": ""}
{"comment": {"body": "This seems rather random. Are we sure this is static? \r\nChrome doesn't utilize standard accessibility identifiers?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#discussion_r1008565431"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#pullrequestreview-1160831265", "body": ""}
{"comment": {"body": "Add an explanation here to what this does for future reference?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#discussion_r1008565583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#pullrequestreview-1160831372", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#pullrequestreview-1160833200", "body": "We're going to need a good story to explain this from a privacy standpoint.\nI think an allow list makes sense? We can enumerate which apps we currently \"observe\" and have toggles in a setting page one day."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#pullrequestreview-1160835960", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#pullrequestreview-1160837456", "body": ""}
{"comment": {"body": "This is a special custom accessibility attribute. I've tested this across multiple chrome instances and tabs and it appears to be stable. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3497#discussion_r1008569718"}}
{"title": "Support Transcriptions in Video", "number": 3498, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498", "body": "Add basic support for Video transcription.\nTranscription rows highlight as video plays\n\n"}
{"comment": {"body": "Can we get this in please?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#issuecomment-1310388373"}}
{"comment": {"body": "\r\nhttps://user-images.githubusercontent.com/1553313/201159541-eee9e26c-c273-4b7b-859b-505d45721986.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#issuecomment-1310607204"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#pullrequestreview-1162257934", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#pullrequestreview-1166055874", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#pullrequestreview-1166057815", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#pullrequestreview-1166136160", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#pullrequestreview-1166137758", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#pullrequestreview-1167633548", "body": ""}
{"comment": {"body": "We already have a `useDebounce` plus a `Debouncer`.  I think we need to rationalize the set of tools we have available here.\r\n\r\nIt looks like `useDebounce` isn't actually used anywhere.  Can we replace it with this implementation?  Or maybe the hook should use Debouncer so there's only one implementation?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#discussion_r1013376519"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#pullrequestreview-1168957373", "body": ""}
{"comment": {"body": "I think debouncing and throttling are slightly different.\r\n\r\nThrottling will delay executing a function up to a certain point. aka 1 request every n seconds.\r\n\r\nDebouncing will delay executing a function until there's a n seconds of downtime. aka 1 request until n seconds are done.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#discussion_r1014305357"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#pullrequestreview-1174944746", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3498#pullrequestreview-1176179764", "body": ""}
{"title": "Bump displayed number of recommendations", "number": 3499, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3499"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3499#pullrequestreview-1160793376", "body": ""}
{"title": "User icons and stack", "number": 35, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-852363206", "body": ""}
{"comment": {"body": "We're assigning \"Unknown User\" in two places. I think we centralize this default value", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r784418183"}}
{"comment": {"body": "This div will have the class `user_icon`. So will the child element `<UserIcon/>`. Is that expected?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r784419410"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-852412825", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853178381", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853180817", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853255024", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853258427", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853261367", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853262421", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853268137", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853268603", "body": ""}
{"comment": {"body": "updated ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r785090435"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853273843", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853275597", "body": ""}
{"comment": {"body": "Since we're returning undefined now, we need a default here.\r\nThink this will be rendering an empty circle right now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r785095228"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853275941", "body": ""}
{"comment": {"body": "Nevermind... title will always be set due to line 17.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r785095480"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853277131", "body": ""}
{"comment": {"body": "@jeffrey-ng ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r785096328"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853292136", "body": ""}
{"comment": {"body": "Ahh sorry. Must have missed this.\r\n\r\nCurious to the change?\r\nFrom what I can tell, doesn't this allow for arbitrary import sort? \r\n\r\n(I don't have a strong preference tbh. Just curious)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r785106722"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853296206", "body": ""}
{"comment": {"body": "Is the idea to group based on where things are imported?\r\n\r\nIn this case '@components' isn't a third party, it's still our code. Just an alias.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r785109736"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853298726", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853308034", "body": ""}
{"comment": {"body": "Yeah I guess this is a good time to figure out what general groupings we want. \r\n\r\nI like react/external libs being sorted to the top\r\nthen internal modules (ie `@components` or `@utils`)\r\nthen files local to this folder (i.e. `./View.scss`)\r\n\r\nI think there could be a case to group all media assets together though, i.e. fontawesome icons and `@assets`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r785118604"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853370649", "body": ""}
{"comment": {"body": "Proposed groupings:\r\n\r\n1. React/external imports\r\n2. Internal imports\r\n3. Assets\r\n4. Local files", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r785162399"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#pullrequestreview-853423170", "body": ""}
{"comment": {"body": "That makes sense to me.  I doubt that can be enforced through a lint policy though... ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/35#discussion_r785199498"}}
{"title": "This pr adds code generator for header parameters", "number": 350, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/350", "body": "Tested this with Peter's If-Modified-Since header paramter.\nAlso cleaned shit up. :)\nFYI (if not required):\nsuspend fun login(context: PipelineContextUnit, ApplicationCall, input: Paths.login, ifModifiedSince: kotlin.String?)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/350#pullrequestreview-883485061", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/350#pullrequestreview-883485391", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/350#pullrequestreview-883485667", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/350#pullrequestreview-883486813", "body": "Wizardry"}
{"title": "Update TopicsPage", "number": 3500, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3500"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3500#pullrequestreview-1160848695", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3500#pullrequestreview-1160848770", "body": ""}
{"title": "Dedupe incoming video references", "number": 3501, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3501", "body": "webFileReference can be quite noisy and send multiple events in a row for the same source.\nThis should help dedupe incoming video references.\nSince references only have a start date and not end date, this should be safe to remove."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3501#pullrequestreview-1160911526", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3501#pullrequestreview-1160912304", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3501#pullrequestreview-1162167070", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3501#pullrequestreview-1162255645", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3501#pullrequestreview-1162308297", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3501#pullrequestreview-1162663416", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3501#pullrequestreview-1163825415", "body": ""}
{"title": "Unit tests for snippets and fix web extension createThread 500s", "number": 3502, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3502", "body": "fixes "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3502#pullrequestreview-1162204385", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3502#pullrequestreview-1162221921", "body": ""}
{"comment": {"body": "This was the fix. Was using wrong algorithm: `deflate` instead of `gzip`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3502#discussion_r1009650437"}}
{"title": "Adds embedded video app as dependency, and magically things now build", "number": 3503, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3503"}
{"title": "Whitelist userclouds team for slack", "number": 3504, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3504"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3504#pullrequestreview-1162288107", "body": "lgtm"}
{"title": "Dedupe file marks", "number": 3505, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3505", "body": "When one navigates to the same file multiple times within a walkthrough, we generate multiple file references.\nWe do not want duplicate file marks when viewing the same file multiple times."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3505#pullrequestreview-1162310947", "body": "makes sense"}
{"title": "Minor admin console changes", "number": 3506, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3506"}
{"title": "Move invite teammates page to its own base route", "number": 3507, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3507", "body": "per designs:\n\nThis PR just moves existing views around and adds a new base route (i.e. no new functionality)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3507#pullrequestreview-1162316744", "body": ""}
{"title": "Fix location definition", "number": 3508, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3508", "body": "currently the dev/prod dashboard routes will be broken because we need the react router definition of location, not the window location definition"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3508#pullrequestreview-1162342961", "body": ""}
{"title": "Reduce polling rate for ingestions", "number": 3509, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3509"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3509#pullrequestreview-1162579847", "body": ""}
{"title": "Add logic to retrieve paginated comments from the GitHub rest api", "number": 351, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/351", "body": "Adds a service class to retrieve comments from the rest api. Handles pagination."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/351#pullrequestreview-883497569", "body": ""}
{"title": "Upgrade admin deps", "number": 3510, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3510"}
{"title": "Upgrade kotlin releases", "number": 3511, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3511"}
{"title": "Cleanup notes/ directory in vscode", "number": 3512, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3512", "body": "Now that we're rebranding discussions into notes in the UI, this is a good time to remove all instances of old code associated with the previous iteration of 'notes' to avoid confusion (this was a half-baked feature anyway/was never implemented in the API)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3512#pullrequestreview-1162505635", "body": ""}
{"title": "Generalize search index event to support indexing non-thread objects", "number": 3513, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3513", "body": "Almost everything is a rename. The main change is in SearchEventPayload."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3513#pullrequestreview-1162554695", "body": "I'm in love!!!"}
{"title": "update", "number": 3514, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3514"}
{"title": "Upgrade to openapi 6.2.0", "number": 3515, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515", "body": "This is an extension of Jeff/Matt work to get us to 6.2.0.\nWe needed to also get Unblocked Hub building and this PR includes these changes.\nNamely:\n1. Introduce a template fix that they have fixed in mainline but have not released yet for:\nhttps://github.com/OpenAPITools/openapi-generator/pull/13617\n2. Introduce minor api change fixes in Hub code.\nTesting:\nConfirmed Unblocked Hub works properly via Xcode build and run."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#pullrequestreview-1162599155", "body": ""}
{"comment": {"body": "Why do we need this now? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#discussion_r1009898552"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#pullrequestreview-1162599636", "body": ""}
{"comment": {"body": "Introduce a template fix that they have fixed in mainline but have not released yet for:\r\nhttps://github.com/OpenAPITools/openapi-generator/pull/13617", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#discussion_r1009898893"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#pullrequestreview-1162600380", "body": ""}
{"comment": {"body": "Otherwise it generates code:\r\n`requiresAuthentication: truetrue`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#discussion_r1009899423"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#pullrequestreview-1162601968", "body": "You had me at openapi"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#pullrequestreview-1162602035", "body": ""}
{"comment": {"body": "What's this new boolean flag used for?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#discussion_r1009900662"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#pullrequestreview-1162604194", "body": ""}
{"comment": {"body": "Great question :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#discussion_r1009902264"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#pullrequestreview-1162605915", "body": ""}
{"comment": {"body": "https://github.com/OpenAPITools/openapi-generator/pull/13321", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#discussion_r1009903483"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#pullrequestreview-1162607776", "body": ""}
{"comment": {"body": "The requestBuilder init that that you're invoking is really designed to be called internally be openapi code.\r\nBasically, the requestBuilder will dynamically choose request generation based off how that boolean is configured.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3515#discussion_r1009904853"}}
{"title": "Replace blue bubble icons", "number": 3516, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3516", "body": "vscode:\n\n\ndashboard:\n\n\nextension:\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3516#pullrequestreview-1162619646", "body": ""}
{"comment": {"body": "I don't think this icon is actually used in vscode but replacing for thoroughness.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3516#discussion_r1009914224"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3516#pullrequestreview-1162633802", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3516#pullrequestreview-1162634049", "body": ""}
{"comment": {"body": "Your call, feel free to leave it or remove it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3516#discussion_r1009924606"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3516#pullrequestreview-1162634576", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3516#pullrequestreview-1162661408", "body": ""}
{"title": "Sets up recorder for camera mixing", "number": 3517, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3517"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3517#pullrequestreview-1165787808", "body": ""}
{"title": "Replace threadId property with id property", "number": 3518, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3518"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3518#pullrequestreview-1163597680", "body": "This guy is on a roll"}
{"title": "Fix swift incremental", "number": 3519, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3519"}
{"title": "Add logic to retrieve paginated files from the GitHub rest api", "number": 352, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/352", "body": "Helper class that will hit the GitHub rest API to get file hashes for files in a pull request until either all files have been retrieved or there are no more files in the pull request. This will be used by the PullRequestReviewThreadService."}
{"comment": {"body": "Going to merge this since I'm blocked, will address any comments in a follow up PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/352#issuecomment-1040913012"}}
{"title": "Add files", "number": 3520, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3520"}
{"title": "Revert \"HardcodeCreds (#3399)\"", "number": 3521, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3521", "body": "This reverts commit 6c74d781b4020ac7621b1d9c96588a5c37482b24.\nConflicts:\nprojects/libs/lib-slack-ingestion/src/main/kotlin/com/nextchaptersoftware/slack/ingestion/fixtures/SlackIngestionFixtures.kt"}
{"title": "Update Hub Notes Icon", "number": 3522, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3522", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3522#pullrequestreview-1166000967", "body": ""}
{"comment": {"body": "I think this might be redundant but ok to keep", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3522#discussion_r1012290584"}}
{"title": "Move to openapi release with bug fixes we need", "number": 3523, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3523", "body": "They literally released this today.\n\nWe need one of the swift generator fixes.\nThis addresses one of the annoyances with 6.2.0, where we had to maintain a custom runtime.mustache file for swift generation."}
{"title": "Add getTopicRelatedInsights operation", "number": 3524, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524", "body": "Adding a GET operation to support this UI:\n\nResponse is an array of heterogenous objects, which for now is one of two types: ThreadInfo and PullRequest. This is modelled with a Content object that has optional thread and pullRequest fields since oneOf isn't an option for us.\nNotes:\n- Turns out repoID(s) are not required here since topicId is an ID for the database model which has a repo property, so all topics in the database are repo scoped\n- Content.pullRequest is a PullRequest object instead of a PullRequestInfo (with threads and TLCs) which matches the existing getPullRequests"}
{"comment": {"body": "@matthewjamesadam renamed the operation to `getTopicRelatedInsights` and `Content` to `Insight`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#issuecomment-1302699551"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163836326", "body": ""}
{"comment": {"body": "i think ThreadInfo already has a pull request?\r\nDo we need the extra pull request attribute?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#discussion_r1010758836"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163839529", "body": "This look sokay to me, but the resident api expert Mr. Bresnan might have additional commentary."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163842199", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163843397", "body": ""}
{"comment": {"body": "<img width=\"713\" alt=\"image\" src=\"https://user-images.githubusercontent.com/332509/199309778-ed61d594-bd8b-4557-ad97-a28ae31f093f.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#discussion_r1010763536"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163848504", "body": "Suggest breaking this up for reusability."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163859437", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163865241", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163865815", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163866729", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163868569", "body": ""}
{"comment": {"body": "@rasharab the intention here is that this API is returning a set of values, each one of which is *either* a thread or a PR, so the expectation is that one of these properties will be set.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#discussion_r1010781082"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163871350", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163877402", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163893844", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163896769", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163899968", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163910075", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163933337", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1163957241", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1164203888", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1164210588", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1167642264", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3524#pullrequestreview-1167695955", "body": ""}
{"title": "Cleanup unused secrets", "number": 3525, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3525", "body": "SCM secrets should no longer hold on to user secrets token (its not in its own secrets file)"}
{"title": "Add SearchEventPayload.Type.PullRequest enum", "number": 3526, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3526", "body": "To allow sending a pull request index event whenever a pull request is updated via GitHub webhooks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3526#pullrequestreview-1164021834", "body": "Roll baby, roll"}
{"title": "Fix multiple sourcemark icons in web extension", "number": 3527, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527", "body": "Was using the faMessages icon in web extension; refactor out logic from vscode gutter icon rendering to share with multiple clients\n\nresult:\n\n\nAlso cleaned up the styling in each sourcemark row\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#pullrequestreview-1164096677", "body": ""}
{"comment": {"body": "If threadInfos is empty, the fallback `IconForThread(threadInfos[0])` will throw a null pointer before it gets here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#discussion_r1010934713"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#pullrequestreview-1164100299", "body": ""}
{"comment": {"body": "Is this discussion and pull request?\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#discussion_r1010937203"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#pullrequestreview-1164100964", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#pullrequestreview-1164102008", "body": ""}
{"comment": {"body": "Should this have a `Multi` suffix as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#discussion_r1010938030"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#pullrequestreview-1164102284", "body": ""}
{"comment": {"body": "Similar to `discussionPullRequest`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#discussion_r1010938175"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#pullrequestreview-1164113077", "body": ""}
{"comment": {"body": "it's this icon:\r\n<img width=\"48\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199350048-b58aecad-83d0-430d-9336-41146617eb9c.png\">\r\n\r\nI don't feel super strongly about it but personally I don't think it's necessary as I think the name is descriptive enough (i.e. the name has the two types)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#discussion_r1010946070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#pullrequestreview-1164114903", "body": ""}
{"comment": {"body": "Updated so the fallback is a callback. I think(?) that should handle this scenario? i.e. the callback is only called given the if condition ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#discussion_r1010947280"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#pullrequestreview-1164135310", "body": ""}
{"comment": {"body": "If we're putting in the effort to consolidate the logic to get multi icons, doing the same with single icons would help with this case as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#discussion_r1010963024"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3527#pullrequestreview-1166091444", "body": ""}
{"title": "Upgrade cdk and gradle build cache node", "number": 3528, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3528"}
{"title": "Fixes text contrast adjustments dependending on vibrancy in Ventura", "number": 3529, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3529", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3529#pullrequestreview-1164123454", "body": ""}
{"comment": {"body": "Add a reference to the radar here for context.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3529#discussion_r1010953442"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3529#pullrequestreview-1164123706", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3529#pullrequestreview-1164124537", "body": ""}
{"comment": {"body": "Wish it was that simple but now that we're on the outside we can't even view each other's feedback reports.\n\n\n\nFB11742402", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3529#discussion_r1010954358"}}
{"title": "Discussion thread view", "number": 353, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353", "body": "Compose Discussion Thread view\nA MessageView for single messages\nA ThreadView encompasses a list of MessageViews\nA DiscussionThread contains the ThreadView as well as the header section, list of participants, and MessageEditor for input\n\n\nAdd functionality to clear the MessageEditor manually \nsee note about why this needs to be done this way\n\n\nAdd EditableInput component\nNOTE: this is all built with a list of flat mock data and built on the assumption that the API/data stores will funnel in and update the data for the UI. I'd like to refactor these mocked models into the shared directory to cut down on repetition (even with live API data set up, we'll need the mocked data for the storybooks) but I can do that in the next PR \n\nViewColumn.Beside view\n\nFull view with participant section"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#pullrequestreview-883703264", "body": ""}
{"comment": {"body": "This is necessary? Is there some weird css dependency with flex going on?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#discussion_r807357245"}}
{"comment": {"body": "Not quite following what's going on here.\r\nWe're just trying to fetch the first paragraph element from the Editor's model?\r\n\r\nWe can refine this when we introduce the actual MessageContent models", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#discussion_r807360535"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#pullrequestreview-883755019", "body": ""}
{"comment": {"body": "yes necessary - it's overriding the `pre` element width which was overflowing outside of its container\r\nhttps://weblog.west-wind.com/posts/2016/feb/15/flexbox-containers-pre-tags-and-managing-overflow", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#discussion_r807393538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#pullrequestreview-883755982", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#pullrequestreview-883773992", "body": ""}
{"comment": {"body": "Yeah this is all temporary -- basically a way to translate the slate models to a simple string for verification", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#discussion_r807408280"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#pullrequestreview-883812593", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#pullrequestreview-885246357", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#pullrequestreview-885247371", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#pullrequestreview-885250481", "body": ""}
{"comment": {"body": "Would it be worth debouncing these types of messages if they're reacting to an input's onChange?\r\n\r\nTyping in the input may cause multiple events. Not sure if this is an issue.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#discussion_r808530119"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/353#pullrequestreview-885250966", "body": ""}
{"title": "Move infra deployment to node 18", "number": 3530, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3530", "body": "Also moves us from a dead github action that's on an old version of node."}
{"title": "Initial Python Tools", "number": 3531, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3531"}
{"comment": {"body": "Adding a notebook to seed our data pipeline with powerml and our own explorations. need to wire up the proto buffer deserialization after I figure out where to hook into make correctly. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3531#issuecomment-1299324371"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3531#pullrequestreview-1164191834", "body": ""}
{"title": "Send index event for pull request", "number": 3532, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3532", "body": "Event should be sent whenever a pull request, TLC, or review is created/updated."}
{"title": "New VSCode Insights Explorer panel", "number": 3533, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533", "body": "Add new Insights panel to the VSCode explorer bar.  This new panel is called ExplorerInsights.  It supersedes the old panels (ExplorerRelatedPullRequests, ExplorerCurrentFile).  For now this new panel is enabled in dev, while the old panels are still displayed in prod.\nRight now this only displays the insights for the file, and the associated PRs, sorted by recency or comment count.  The new provider merges the existing streams together and sorts them, and the new Webview displays them in a list.  I added a new model (called Insight for now, though I might rename that to match our API models eventually) that represents either a Thread or a PR.\nSome parts of this are reused from existing code, some parts are brand new.\n"}
{"comment": {"body": "> called Insight for now, though I might rename that to match our API models eventually\r\n\r\nIMO Insight has more meaning than Content so I'd vote to keep it (and replace the API model maybe(?))", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#issuecomment-**********"}}
{"comment": {"body": "> IMO Insight has more meaning than Content so I'd vote to keep it (and replace the API model maybe(?))\r\n\r\ncc @davidkwlam I don't really mind one way or the other.  Insight is maybe more precisely meaningful to us, but is also more of a marketing-style term.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#issuecomment-1301372359"}}
{"comment": {"body": "> Is there a UI for the unauthed state that I'm missing?\r\n\r\nYes -- the unauthed/uninstalled UIs are generic panels we reuse, you can see how we reference them in the `package.extension.json` file.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#issuecomment-1301375966"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166025498", "body": ""}
{"comment": {"body": "Writing correct stable sorting methods is obnoxious, this helper makes it easier and safer.  The tests have awkward syntax and don't really show what this does particularly well, you can see its usage in `InsightSort.ts`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012307548"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166025977", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166026260", "body": ""}
{"comment": {"body": "This allows the selected thread/PR to be joined into combined streams, which is kind of nice.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012308093"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166026395", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166026950", "body": ""}
{"comment": {"body": "Some of this is copied from `CurrentFilePane.tsx` -- that file will be removed once we drop the old panels.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012308628"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166028121", "body": ""}
{"comment": {"body": "Note: the approach here is to join *all* the data we need into a single stream, so that when any value changes (threads, PRs, sort criteria, selected thread/PR) the stream updates and we re-render.  I think the end structure is pretty nice and clean.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012309441"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166029747", "body": ""}
{"comment": {"body": "Some of this was taken from `RelatedPullRequestsPane.tsx` -- we will remove that file once the old panels are dropped.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012310635"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166030146", "body": ""}
{"comment": {"body": "But note that this suffix is now shared between PRs and Threads, since both of them display the count/date suffixes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012310940"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166053631", "body": "Is there a UI for the unauthed state that I'm missing?"}
{"comment": {"body": "IMO this would be much more legible if it were a switch statement", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012329221"}}
{"comment": {"body": "feel like you can probably color this and knock the opacity down so it matches the select text\r\n\r\nor better yet just put the color/opacity on the parent `.explorer_insights__sort`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012329871"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166067510", "body": ""}
{"comment": {"body": "This was intentional -- the designs show the text and the chevron with different colours.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012337550"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166068885", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166069208", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166069484", "body": ""}
{"comment": {"body": "New insight panel definitions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012338781"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166070020", "body": ""}
{"comment": {"body": "I'll figure out a way to restructure this -- it was copied from elsewhere, and the content is long enough that embedding it in a switch statement might be awkward, but I'll figure out something.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012339204"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166070666", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166088937", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166098512", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166098746", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166102409", "body": ""}
{"comment": {"body": "I moved this into a switch statement and it actually became harder to read.  The main syntactic issue is that we want to treat the \"no insights\" case as equal to the other top-level states as it is a separate view...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012362687"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166105751", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166136435", "body": ""}
{"comment": {"body": "where is this context variable actually being set to the feature flag value? \r\n\r\nalso I see all our context variables are prefixed with `unblocked-vscode` -- should this be the same? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012388638"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166137207", "body": ""}
{"comment": {"body": "The variables are set automatically on startup in extension.ts.\n\n\n\nYes the flags should probably all use unblocked-vscode as an extension.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012389234"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166137833", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1166138370", "body": ""}
{"comment": {"body": "should we enable this for local too? I know most of us run against dev but I don't see why not \ud83e\udd14 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1012390112"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#pullrequestreview-1167503589", "body": ""}
{"comment": {"body": "I'll add it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3533#discussion_r1013287028"}}
{"title": "Render camera pip", "number": 3534, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3534", "body": "Uses Core Image filters and transforms to composite the camera and screen buffers"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3534#pullrequestreview-1165719222", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3534#pullrequestreview-1165782550", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3534#pullrequestreview-1165783921", "body": "Honestly, this is not an area that I'm proficient on. \nTrusting you on the recorder logic and API usage."}
{"title": "Replace title with description editor in Video Walkthrough", "number": 3535, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535", "body": "Replaced title field with message editor for description.\n"}
{"comment": {"body": "One bug I noticed was the server generated title included the video URL.\r\n<img width=\"623\" alt=\"CleanShot 2022-11-02 at 08 57 14@2x\" src=\"https://user-images.githubusercontent.com/1553313/199539075-98b27d72-0257-4227-80d8-7378fe482e46.png\">\r\n\r\n@davidkwlam ?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#issuecomment-1300759081"}}
{"comment": {"body": "@jeffrey-ng I'll fix", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#issuecomment-1300978970"}}
{"comment": {"body": "@jeffrey-ng which theme are you using? looks like the MessageEditor doesn't look that great in this one :( ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#issuecomment-1301056046"}}
{"comment": {"body": "> @jeffrey-ng which theme are you using? looks like the MessageEditor doesn't look that great in this one :(\r\n\r\nhttps://marketplace.visualstudio.com/items?itemName=marlosirapuan.nord-deep", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#issuecomment-1301058563"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#pullrequestreview-1165812431", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#pullrequestreview-1165814515", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#pullrequestreview-1165815760", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#pullrequestreview-1166019701", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#pullrequestreview-1166033210", "body": ""}
{"comment": {"body": "Feels like we should be importing this from @knowledge?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#discussion_r1012313251"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3535#pullrequestreview-1166037390", "body": ""}
{"title": "TranscriptionService", "number": 3536, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3536", "body": "Add basic transcription servicw\nTry dev deployment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3536#pullrequestreview-1165771332", "body": ""}
{"comment": {"body": "Thanks for this!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3536#discussion_r1012130583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3536#pullrequestreview-1165774704", "body": ""}
{"comment": {"body": "I think this guy needs sqs resource access right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3536#discussion_r1012132947"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3536#pullrequestreview-1165776051", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3536#pullrequestreview-1165782121", "body": "Some dangling bits here (sqs queue specifically) but all stuff we can get to on subsequent PRs. Thanks for this!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3536#pullrequestreview-1165786616", "body": ""}
{"comment": {"body": "Yup, going to add it in next pr.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3536#discussion_r1012141128"}}
{"title": "Add related links section to walkthroughs", "number": 3537, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3537", "body": "Added related links section with some basic updates to code references row to allow for URL icons\n\n"}
{"comment": {"body": "@benedict-jw Icons will need to be hardcoded.\r\n\r\nAlso using browser (https://fontawesome.com/icons/browser?s=regular&f=classic) as unknown urls for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3537#issuecomment-1301028982"}}
{"comment": {"body": "nit but can you even out the spacing between the h4s and their respective immediate ensuing divs? \r\n<img width=\"451\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199606566-49403c8d-ae00-4025-a121-a62f517fb9e1.png\">\r\n\r\n(i.e. conform distance between the h4s and the sections below) \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3537#issuecomment-1301314523"}}
{"comment": {"body": "Slightly updated icons and spacing.\r\n<img width=\"745\" alt=\"CleanShot 2022-11-02 at 16 28 56@2x\" src=\"https://user-images.githubusercontent.com/1553313/199621435-f18f0187-d4ef-42bc-8be0-84d8805fb3de.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3537#issuecomment-1301489901"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3537#pullrequestreview-1166031559", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3537#pullrequestreview-1166094369", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3537#pullrequestreview-1166135289", "body": ""}
{"title": "[UNB-723] Fix color of pr pane subtext", "number": 3538, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3538", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3538#pullrequestreview-1165803071", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3538#pullrequestreview-**********", "body": ""}
{"title": "Add transcription queue", "number": 3539, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3539"}
{"title": "Add Elasticache Redis", "number": 354, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/354", "body": "Added a new stack for deploying Elasticache secure Redis clusters\nAdded config classes for new Redis Stack\nAdded config for creating IAM Mapped service accounts to help mounting AWS secret manager secrets on Kube\nUpdated EKS README to include AWS Secrets CSI driver installation instructions\nAdded docs about how to use CSI driver to mount secrets from secret manager\nUpdated Dev and Prod CDK configs to include new Redis stuff\n\nThe Redis cluster deployed by this code comes with both at rest as well as in-transit encryption. It uses Redis over TLS and each user can be scoped to one or more key prefixes. Auth has been enabled and all access to Redis requires password. Secrets for Redis users are stored in AWS Secret Manager"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/354#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/354#pullrequestreview-*********", "body": ""}
{"title": "Fix build", "number": 3540, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3540"}
{"title": "Add transcriptino event processors", "number": 3541, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3541", "body": "This is the canonical model by which we are enqueuing/dequeueing across various services."}
{"title": "fix", "number": 3542, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3542"}
{"title": "Send index event on upsert", "number": 3543, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3543"}
{"title": "Api and webhook queue access", "number": 3544, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3544"}
{"title": "Index pull request during bulk ingestion", "number": 3545, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3545"}
{"title": "Slack threads should be readonly in Unblocked", "number": 3546, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3546", "body": "Currently slack threads are rendering as normal threads, but since we can't currently write back to slack threads, we should remove the message editor:\nbefore:\n\nafter:\n\nOther:\n* Add an ExternalLink reusable component to dedupe having the same styles everywhere (it's essentially a link with an faArrowUpRightFromSquare icon next to it)\n* Add a header with slack data to the vscode thread:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3546#pullrequestreview-1166032873", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3546#pullrequestreview-1166033109", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3546#pullrequestreview-1166047875", "body": ""}
{"title": "Add queues to services", "number": 3547, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3547"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3547#pullrequestreview-1166052315", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3547#pullrequestreview-1166052480", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3547#pullrequestreview-1166052623", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3547#pullrequestreview-1166052809", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3547#pullrequestreview-1166052839", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3547#pullrequestreview-1166053640", "body": ""}
{"title": "Clean up rogue log", "number": 3548, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3548", "body": "is bleeding into production: \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3548#pullrequestreview-1166093424", "body": ""}
{"title": "STandardize notification queues", "number": 3549, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3549", "body": "Move notification service over to how we're doing event queues in other services."}
{"title": "Fix merge issue between two simultanously merged PRs", "number": 355, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/355"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/355#pullrequestreview-883710047", "body": ""}
{"title": "Replace ThreadSearchModel with SearchModel", "number": 3550, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3550", "body": "SearchModel will let us search different objects instead of just Threads through a SearchableModel enum property.\nThis will be used for topics V1 to search both threads and pull requests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3550#pullrequestreview-1167445022", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3550#pullrequestreview-1167445311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3550#pullrequestreview-1167446207", "body": ""}
{"title": "Fixports", "number": 3551, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3551"}
{"title": "Message deserialization + jsonlines", "number": 3552, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3552", "body": "This reorders the message.proto to support python code gen. Hope that doesn't break another generator, but we are going to find out."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3552#pullrequestreview-1166377980", "body": ""}
{"title": "Hide less used tabs to declutter", "number": 3553, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3553"}
{"title": "Trace instead of debug", "number": 3554, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3554"}
{"title": "Add client config store for TS", "number": 3555, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555", "body": "Add a new store that fetches and publishes client configurations.  This doesn't remove any of the existing flag logic, we need to first add and configure the flags we need in the service before doing that, so there will be a series of follow-on PRs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#pullrequestreview-1167399713", "body": ""}
{"comment": {"body": "These tests bring me so much joy matt.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#discussion_r1013215773"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#pullrequestreview-1167400669", "body": "Can someone give this man a hug."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#pullrequestreview-1167437812", "body": ""}
{"comment": {"body": "\ud83d\udcaf", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#discussion_r1013241856"}}
{"comment": {"body": "This itself could be a remote config\r\n\r\n![](https://i.imgflip.com/6zefi8.jpg)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#discussion_r1013246981"}}
{"comment": {"body": "Would an equivalent concept make sense for quantities? How do you see clients subscribing to quantity changes?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#discussion_r1013249591"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#pullrequestreview-1167455650", "body": ""}
{"comment": {"body": "The `ClientConfigStore` already supports subscribing for quantities -- any TS code can call `ClientConfigStore.instance.getQuantityStream` to get a stream of values for a particular quantity.\r\n\r\nThis code here is specific to VSCode and feature flags -- we auto-populate VSCode context values for any set capabilities.  This makes it easier to enable/disable VSCode UIs based on flags without having any boilerplate.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#discussion_r1013253802"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#pullrequestreview-1167465951", "body": ""}
{"comment": {"body": "I see. Got it, thanks.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3555#discussion_r1013261064"}}
{"title": "New feature flags", "number": 3556, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556", "body": "Add new flags for:\n* New onboarding UI\n* Video walkthrough\n* New explorer insights UI"}
{"comment": {"body": "I added a date to some of the descriptions, who knows, maybe that will help people in the future \ud83d\ude06 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#issuecomment-1302532693"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#pullrequestreview-1167449481", "body": ""}
{"comment": {"body": "I took a guess that these were supposed to be increasing.  I don't know what they're for.  This feels like a bit of a maintenance challenge, especially as we figure out how to remove these flags.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#discussion_r1013249513"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#pullrequestreview-1167459819", "body": ""}
{"comment": {"body": "The requirement is that they are _unique_ values \u2014 they do not necessarily need to be increasing \u2014 however the enum constructor will catch this by failing at test time, so we don't need to worry about this. By convention, start at 1 and increase.\r\n\r\nThe only thing to watch out for is removing an enum and then _reusing_ that previously removed DB ordinal. This will cause the flags to conflict in unexpected ways and bad things will happen. This is analogous to reusing ordinals in protobufs. The best advise is to not delete these flags.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#discussion_r1013256755"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#pullrequestreview-1167470235", "body": ""}
{"comment": {"body": "Protobuf has a `reserved` keyword to support this type of activity.\r\n\r\nOne idea is to add an `obsolete` property to the enum, which we can use to hide the config key in the admin UI. Then we don't need to delete anything, and we can keep the config UI in admin web clean. I can do this later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#discussion_r1013263938"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#pullrequestreview-1167475626", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#pullrequestreview-1167490519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#pullrequestreview-1167492578", "body": ""}
{"comment": {"body": "Maybe numeric enums aren't the right primitive for this?  Can we store and reference them as the strings themselves, which would be very unlikely to conflict?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#discussion_r1013279176"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#pullrequestreview-1167493236", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#pullrequestreview-1167495134", "body": ""}
{"comment": {"body": "Strings can be renamed, same problem, just less efficient.\n\n\n\nDon't think it's going to be a problem in practice. We've been using this pattern since January.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#discussion_r1013280954"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3556#pullrequestreview-1167523210", "body": ""}
{"title": "Make sure we launch the installer app bundled with the running app", "number": 3557, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3557"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3557#pullrequestreview-1167477317", "body": "Can we say Peter the wolf?"}
{"title": "Foreground VSCode after completion", "number": 3558, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3558", "body": "After recording a video, foreground IDE to display create video flow."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3558#pullrequestreview-1167527842", "body": ""}
{"title": "Update relative DateTime helper", "number": 3559, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559", "body": "per feedback from Ben, make relative time ago helper show X time ago up to a year, then show MM YYYY \n\nshow full date in title:\n"}
{"comment": {"body": "Chatted with Ben, new logic is as follows:\r\n* Relative time up to 1 day: `X <minutes/hours> ago`\r\n* 1 day ago: `Yesterday`\r\n* Over 1 day but under a year: MMM D, `May 17`, `Oct 4`\r\n* Over 1 year: MMM YYYY, `Feb 2021`, `Jun 2020`\r\n\r\nExample:\r\n<img width=\"355\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199846582-cae9487b-0984-4d8c-8ea1-ff4fb6e1724c.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#issuecomment-1302746461"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#pullrequestreview-1167559047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#pullrequestreview-1167567947", "body": "needs tests"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#pullrequestreview-1167571052", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#pullrequestreview-1167743391", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#pullrequestreview-1167743447", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#pullrequestreview-1167743472", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#pullrequestreview-1169067089", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#pullrequestreview-1169183246", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3559#pullrequestreview-1169249952", "body": ""}
{"title": "Update PullRequestReviewThreadService to use rest apis to get comments and file hashes", "number": 356, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/356", "body": "This way we can get the commit IDs and file hashes, since we can't get those through the graphql api."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/356#pullrequestreview-883796927", "body": ""}
{"title": "Add local s3 buckets for assets", "number": 3560, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3560"}
{"title": "Use search model for searching threads", "number": 3561, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3561", "body": "Not to be merged until we reindex"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3561#pullrequestreview-1167625873", "body": ""}
{"title": "Remove ineffective sourcemark caches", "number": 3562, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3562", "body": "There were several cache design problems with the existing tree hash cache and the uncommitted point\ncache that probably means these caches were fairly useless, and just consumed resources needlessly.\nProblems\n\n\nIt's a write-through cache, which by definition means it is not correlated with read patterns.\n\n\nUnbounded memory growth. Could be addressed with LRU.\n\n\nQuestionable effectiveness, since it's invalidated by the top-level tree hash which changes often.\n  I suspect this is actually totally useless, and a better cache would be a read through cache based\n  on FilePath and FileContentHash only.\n\n\nResults\nRunning on the unblocked repo\n- no runtime difference\n- slightly less memory used\nBefore\nusage: {\n    rss: 90.890625,\n    heapUsed: 43.42457962036133,\n    heapTotal: 58.125,\n    maxRSS: 79.9375,\n    systemCPUTime: 7.0903529999999995,\n    userCPUTime: 38.353993,\n    wallClock: 201.20693104200063,\n    minorPageFault: 186022,\n    majorPageFault: 0\n}\nAfter\nusage: {\n    rss: 59.1875,\n    heapUsed: 43.261234283447266,\n    heapTotal: 39.78125,\n    maxRSS: 52.125,\n    systemCPUTime: 7.3009710000000005,\n    userCPUTime: 39.148971,\n    wallClock: 200.69948800000176,\n    minorPageFault: 186924,\n    majorPageFault: 0\n  }"}
{"comment": {"body": "I'm really sad...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3562#issuecomment-1302632131"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3562#pullrequestreview-1167626552", "body": ""}
{"title": "We should stop breaking the installer", "number": 3563, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3563"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3563#pullrequestreview-1167652809", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3563#pullrequestreview-1167653652", "body": "Long Live the Installer."}
{"title": "cleanup docker", "number": 3564, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3564"}
{"title": "[BREAKS API ON MAIN] Add asset id to video models", "number": 3565, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565"}
{"comment": {"body": "Confirmed that the only breaking change here is updateMessage", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#issuecomment-1304259403"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1167741362", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1167741931", "body": "Good stuff"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1167772457", "body": ""}
{"comment": {"body": "Adding a new optional property to a PUT request body is considered backward incompatible.\n\n[https://github.com/OpenAPITools/openapi-diff/issues/264#issuecomment-1015181421](https://github.com/OpenAPITools/openapi-diff/issues/264#issuecomment-1015181421)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1013479692"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1167782107", "body": ""}
{"comment": {"body": "Good news is no one is using this stuff. :) But fair point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1013487381"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1167783085", "body": ""}
{"comment": {"body": "Yeah, I know. Can't see a way around this other than (i) versioning a la \"V2\" suffix, or (ii) obsoleting all clients.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1013488147"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1169053188", "body": ""}
{"comment": {"body": "Transcribing our in-person conversation for posterity.\n\n\n\n### Short term proposal\n\nWe don't care about breaking customers right now. Therefore, turn the breaking detection off for all versions that customers are currently using:\n\n\n\n1. set the APICompatTest.EARLIEST_SUPPORTED_NUMBER to the latest version; and\n\n2. obsolete all stable clients forcing an upgrade; and\n\n3. actually fix obsoletion ([https://linear.app/unblocked/issue/UNB-373/reject-requests-with-410-for-obsolete-clients](https://linear.app/unblocked/issue/UNB-373/reject-requests-with-410-for-obsolete-clients))\n\n\n\n### Long term proposal\n\nWe care about breaking customers. Therefore, version the API to instead of breaking clients, and figure out how to do this painlessly.\n\n\n\n### Other options considered\n\n\n\n- **Relax PUT body check**. This is a viable option only if we make sure (through integration tests) that all legacy client PUT body requests do not clobber new data fields on update. Discounted because this is tedious and error prone, and developers would have to understand this subtle issue ahead of time, which is unlikely.\n\n\n\n- **Separate content into REST like resources**. For example, rather than sending disparate fields {message, video} in one **PUT /message/:id** API call; make two concurrent retryable API calls {**PUT /message/:id**, **PUT /message/:id/video**} each with their own payload. Downside is that we need to handle the case when one of the concurrrent calls does not succeed.\n\n\n\ncc @matthewjamesadam as I know you \u2764\ufe0f this stuff.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1014351626"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1169055144", "body": ""}
{"comment": {"body": "Makes sense!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1014353689"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1169058720", "body": ""}
{"comment": {"body": "Testing markdown going in the other direction:\r\n\r\n### Header 3\r\n- List item 1\r\n- List item 2\r\n- List item 3\r\n\r\n_italic_", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1014357401"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1169060986", "body": ""}
{"comment": {"body": "Now trying markdown from unblocked:\n\n\n\n### Header 3\n\n- List item 1\n\n- List item 2\n\n- List item 3\n\n\n\n_italic_", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1014359905"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1169223522", "body": ""}
{"comment": {"body": "State should be required.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1014489193"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1169224074", "body": ""}
{"comment": {"body": "Can we specify that this is an override?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1014489590"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1169251641", "body": ""}
{"comment": {"body": "Updated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1014510269"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1169252167", "body": ""}
{"comment": {"body": "I'm not sure the API spec is the best place to define field use in the UX, but we can add that to the client code for sure", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1014510697"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#pullrequestreview-1169262723", "body": ""}
{"comment": {"body": ":(\r\n\r\nbreaks all compatibility ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3565#discussion_r1014519137"}}
{"title": "Turn accessibility back on for electron apps", "number": 3566, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3566"}
{"title": "Add slack runtime fixture", "number": 3567, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3567"}
{"title": "Move most flags to new capabilities system", "number": 3568, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3568", "body": "Move all TS feature flags except slack to the new flag system.  Slack requires a bit more work and I'll do that in the next PR.\n\nnotes, pullRequests, logoutButton, codelens were all dropped\nnewOnboarding was mapped to the FeatureNewOnboardingUI flag\nshowDemoUser is removed and we use isDeveloperBuild instead\nvideo was mapped to the FeatureVideoWalkthrough flag\ninsightsPanel was mapped to the FeatureNewExplorerInsightsUI flag"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3568#pullrequestreview-1169045831", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3568#pullrequestreview-1169059423", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3568#pullrequestreview-1169060585", "body": ""}
{"comment": {"body": "Where are these values actually being set?\r\nThis was from a previous PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3568#discussion_r1014359445"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3568#pullrequestreview-1169064938", "body": ""}
{"comment": {"body": "Yeah the previous PR had code that subscribed to the client flags and auto-published them as VSCode context values", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3568#discussion_r1014364226"}}
{"title": "Fix workflows", "number": 3569, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3569", "body": "Github has deprecated pre node-16 actions and also set-output.\n"}
{"title": "Build web/vscode CI on 'shared' folder change", "number": 357, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/357", "body": "I noticed the VSCode extension on the tip of main doesn't build -- turns out VSCode/web builds aren't running if a PR only changes /shared.  Fixed that, and the build-breaking bug."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/357#pullrequestreview-883829821", "body": ""}
{"title": "Another old action that needs to be updated", "number": 3570, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3570", "body": "Need to update another action to node 16 as node 12 is being deprecated on github."}
{"title": "[UNB-706] Fix spacing", "number": 3571, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3571"}
{"comment": {"body": "https://linear.app/unblocked/issue/UNB-706/message-editor-paragraph-spacing-is-too-small", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3571#issuecomment-1304008957"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3571#pullrequestreview-1169068527", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3571#pullrequestreview-1169068726", "body": ""}
{"title": "Use new slack feature flag in TS clients", "number": 3572, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572", "body": "This gets rid of the last of the old flag system RIP"}
{"comment": {"body": "Okay, can someone give htis man a hugh.\r\nSeriously.\r\nThank you.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#issuecomment-1306083412"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#pullrequestreview-1169090289", "body": ""}
{"comment": {"body": "Normally we don't need to centralize the flags like this, but because this flag is used in a bunch of places...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#discussion_r1014389607"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#pullrequestreview-1170956076", "body": ""}
{"comment": {"body": "To clarify, this hook cannot be currently used within vscode web views correct? aka this also should not be used in shared components which may find themselves in web views.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#discussion_r1015807020"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#pullrequestreview-1170956170", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#pullrequestreview-1171015262", "body": ""}
{"comment": {"body": "Correct.  We can fix this eventually but it's surprisingly hard to do correctly.  For now this is only being used in the dashboard.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#discussion_r1015847498"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#pullrequestreview-1171028219", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#pullrequestreview-1171068009", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3572#pullrequestreview-1171088116", "body": ""}
{"title": "UPdate kube context", "number": 3573, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3573"}
{"title": "Delete old toggle code", "number": 3574, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3574", "body": "Richie told me that the old way was wrong.\nAS usual, Richie is not off from the truth.\nThank you richie."}
{"comment": {"body": "Context: The problem with checkbox-based toggles is that it only sends you a value event when _checked_, so we never get a value event for _unchecked_. Radio-based toggles send you a value for each state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3574#issuecomment-1304105502"}}
{"title": "Fix bug in persisting null sourcepoint snippets", "number": 3575, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3575", "body": "Fix bug in persisting null sourcepoint snippets\nWe incorrectly persisted null as \"null\". This broke downstream API resulting in 500s in DEV:\nkotlinx.serialization.json.internal.JsonDecodingException:\nExpected start of the object '{', but had 'EOF' instead at path: $ JSON input: null\nThe inscrutable problem is that object.encode() produces \"null\" when the object is optional,\nwithout compiler warning or error, even though it's rarely what was intended IMO."}
{"title": "Adopt new SourceMark APIs in VSCode", "number": 3576, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3576", "body": "Adopt new SourceMark APIs in VSCode\n\nVSCode is now aware of SourceMarks/SourcePoints and FileMarks/FilePoints\nDeals with sparse file-path and sparse snippets\nUnzips compressed and encoded snippets for SourceMarks on-demand\nUses more efficient upload API with better error handling"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3576#pullrequestreview-1169248007", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3576#pullrequestreview-1169258262", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3576#pullrequestreview-1169259077", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3576#pullrequestreview-1169635925", "body": ""}
{"title": "Add thread titles for threads anchored around images", "number": 3577, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3577", "body": "We spit out a basic thread title for image\nWe generate an image url"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3577#pullrequestreview-1169252494", "body": ""}
{"comment": {"body": "do we have to worry about escaping, or is this encoded?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3577#discussion_r1014510953"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3577#pullrequestreview-1169253416", "body": ""}
{"comment": {"body": "Good question. Pretty sure encoded.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3577#discussion_r1014511653"}}
{"title": "Fix permissions and media stream initialization race", "number": 3578, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3578"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3578#pullrequestreview-1169252948", "body": ""}
{"comment": {"body": "I added this because when we were polling for permissions changes it was causing the UI to rapid fire update", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3578#discussion_r1014511294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3578#pullrequestreview-1169253427", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3578#pullrequestreview-1181577227", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3578#pullrequestreview-1181579216", "body": ""}
{"title": "Dont show image and video urls in thread titles", "number": 3579, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3579"}
{"comment": {"body": "@jeffrey-ng yeah, just for titles. Posting back to GitHub is markdown, so would include links.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3579#issuecomment-1347534522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3579#pullrequestreview-1214492416", "body": "Is this just used for titles? Not used for uploading back to GH? (More of a concern for images than videos)"}
{"title": "Add database info to readme", "number": 358, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/358"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/358#pullrequestreview-883807349", "body": ""}
{"title": "Add Pill component", "number": 3580, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3580", "body": "Add a simple component for rendering pills/tokens/chips/etc"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3580#pullrequestreview-1170957951", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3580#pullrequestreview-1170960487", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3580#pullrequestreview-1171119009", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3580#pullrequestreview-1171190623", "body": ""}
{"title": "Make null the default value when serialized object does not have field", "number": 3581, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3581", "body": "The joys of JSON. This is a new field added on Friday but existing (serialized) data in the database does not have this field, so this should fix this error: \n"}
{"comment": {"body": "Maybe the JSON deserializer has the option to do this automatically if the field is missing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3581#issuecomment-1305982532"}}
{"comment": {"body": "Ah yeah in `kotlinx.serialization.json.Json.kt`:\r\n\r\n```\r\n    /**\r\n     * Specifies whether `null` values should be encoded for nullable properties and must be present in JSON object\r\n     * during decoding.\r\n     *\r\n     * When this flag is disabled properties with `null` values without default are not encoded;\r\n     * during decoding, the absence of a field value is treated as `null` for nullable properties without a default value.\r\n     *\r\n     * `true` by default.\r\n     */\r\n    @ExperimentalSerializationApi\r\n    public var explicitNulls: Boolean = json.configuration.explicitNulls\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3581#issuecomment-1305984377"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3581#pullrequestreview-1170870783", "body": ""}
{"title": "Fix missing code block and snippet styling", "number": 3582, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3582"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3582#pullrequestreview-1170889164", "body": ""}
{"comment": {"body": "Should maybe add a comment describing why the two properties are different...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3582#discussion_r1015759295"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3582#pullrequestreview-1170889298", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3582#pullrequestreview-1170894193", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3582#pullrequestreview-1170900312", "body": ""}
{"comment": {"body": "Done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3582#discussion_r1015766919"}}
{"title": "Api Cleanup", "number": 3583, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3583"}
{"title": "Use threadInfo.mark instead of deprecated threadInfo.sourceMark", "number": 3584, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3584", "body": "Use threadInfo.mark instead of deprecated threadInfo.sourceMark\n\n\nRefactor types, per "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3584#pullrequestreview-1170932792", "body": ""}
{"title": "Fix VSCode webview debugging", "number": 3585, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3585", "body": "I think the source-map devtool simply doesn't work for webviews.  I don't know why.  So I switched webviews back to use eval-source-map.\nThe problem we can into before is that eval-based source maps load very slowly when you start up VSCode, especially if you have any breakpoints set, which makes the repl loop very slow.  This PR mitigates this by changing the debugger launch parameters so that we only load sourcemaps for the extension.js bundle, which is the only one we debug within VSCode itself anyways."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3585#pullrequestreview-1170946692", "body": ""}
{"title": "Add additional logging context", "number": 3586, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3586", "body": "Add additional logging to issues when hub attempts to update versions."}
{"comment": {"body": "We'll need to get a new build for this as the issue is with your main hub app, not built.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3586#issuecomment-1306265392"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3586#pullrequestreview-1170951463", "body": "Do we need to trigger another build for this to get invoked? I am in a good state, but I just did a build, so if we I++ the version, this code should get run?"}
{"title": "[VSCode] Filter out walkthrough when rendering", "number": 3587, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3587", "body": "Walkthroughs should not be rendered in VSCode gutter.\nThis filtering is done at the rendering layer instead of store layer as we want threads to still populate insight sidebars / insight counts.\nTODO: Will have separate PR for web extension."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3587#pullrequestreview-1170968115", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3587#pullrequestreview-1170970500", "body": ""}
{"title": "AssemblyAi", "number": 3588, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3588", "body": "AssemblyAI is my friend\nAdd secrets"}
{"title": "Support byte range requests", "number": 3589, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3589", "body": "Trying to pass Range headers through to S3."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3589#pullrequestreview-1171137324", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3589#pullrequestreview-1171137425", "body": ""}
{"title": "Comment and files api calls use correct token", "number": 359, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359", "body": "The old V3 client (now called V3App) should only be used for getting installation IDs. This creates a new V3Org client that is like the V4 client but for the rest API."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883831449", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883838269", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883839421", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883839684", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883878528", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883878993", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883884949", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883888176", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883889697", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883890152", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883950609", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/359#pullrequestreview-883952196", "body": ""}
{"title": "CleanupDependencies", "number": 3590, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3590", "body": "Cleanup slack modules\nRename"}
{"title": "Token input component", "number": 3591, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591", "body": "Generic input component that registers text inputs as search tokens by hitting Enter or Comma:\n\nHandles basic expected keydown interactions (i.e. Backspace to delete, arrow left/right to navigate):\n\nThe component also takes in a list of suggestions that it can display as autocomplete items:\n\n(Will be part of the new Search UI)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#pullrequestreview-1181736121", "body": ""}
{"comment": {"body": "I think having the value and selected tokens owned at this level might ben an antipattern.  Whoever uses this component is definitely going to want to understand the selected items and text value, so they will likely have to hold this in state, which means we would have two states for the same value.\r\n\r\n(This is the same old controlled vs uncontrolled components argument I guess)\r\n\r\nThe parent code already has to pass in `tokens` and `value`, so if we just use those directly instead of turning them into state, this should work as expected as a controlled component.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#discussion_r1023395517"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#pullrequestreview-1199803214", "body": ""}
{"comment": {"body": "In the case of this component, the set tokens and the input value are separate entities; the value is the actual string value in the input component and the tokens are the list of saved strings.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#discussion_r1036296420"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#pullrequestreview-1199807626", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#pullrequestreview-1199815645", "body": ""}
{"comment": {"body": "Why do we need keep the string value state here? As Matt mentioned, I think the parent should be handling that state entirely.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#discussion_r1036305143"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#pullrequestreview-1199834098", "body": ""}
{"comment": {"body": "why would the parent care about what the user input is? the parent only cares about the saved tokens afaik", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#discussion_r1036317562"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#pullrequestreview-1199907026", "body": ""}
{"comment": {"body": "I had misinterpreted this component then. I thought *both* token and values were important to the parent.\r\n\r\nFor example, this could be used in a search context where both tokens and the current text input would be used.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#discussion_r1036368509"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#pullrequestreview-1199911347", "body": ""}
{"comment": {"body": "Had something like this in mind https://primer.style/react/TextInputWithTokens\r\n\r\nThe props are the source of truth. They're not used only as the initial value.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#discussion_r1036371531"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#pullrequestreview-1200046679", "body": ""}
{"comment": {"body": "Discussed this with Kay -- I do have a preference for controlled components where the props are the source of truth, but in this case it probably doesn't matter much. We can refactor this to be a controlled component in the future if we end up holding this state in parent components.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#discussion_r1036465773"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3591#pullrequestreview-1200056088", "body": ""}
{"title": "Remove ThreadSearchModel", "number": 3592, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3592", "body": "Need to create another PR to drop this table after this has been merged"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3592#pullrequestreview-1172584691", "body": ""}
{"comment": {"body": "smart", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3592#discussion_r1016942027"}}
{"title": "ADd transcirption queue", "number": 3593, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3593"}
{"title": "Add title support for web url references", "number": 3594, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3594", "body": "Support for web titles was added to API spec but not implemented.\nVideo app will now generate titles based on accessibility.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3594#pullrequestreview-1174869502", "body": ""}
{"title": "parse authorization query string", "number": 3595, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3595", "body": "Adding support for authorization query string. Positive cases for local test are broken so I couldn't validate this 100% but it has limited impact and no one is using assets so we won't need to disable prod deploys"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3595#pullrequestreview-1171244742", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3595#pullrequestreview-1171246803", "body": ""}
{"comment": {"body": "Instead of checking for the type, we could just verify the value itself.\r\n`if (params.authorization !== 'undefined')`\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3595#discussion_r1016008031"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3595#pullrequestreview-1171247169", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3595#pullrequestreview-1273694490", "body": ""}
{"comment": {"body": "Agreed!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3595#discussion_r1089571348"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3595#pullrequestreview-1273694545", "body": ""}
{"comment": {"body": "@rasharab thi sis good stuff\n\n\n\n1) adf\n2) asdffdasdas", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3595#discussion_r1089571389"}}
{"title": "Add transcription webhooks", "number": 3596, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3596"}
{"title": "Guard against empty list so dashboard at least loads", "number": 3597, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3597", "body": "The sourcePoints array should always have at least one sourcePoint but on the edge case that there isn't anything in there (see: ), at least load the dashboard and just hide the code block."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3597#pullrequestreview-1171249212", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3597#pullrequestreview-1171249361", "body": "thanks!"}
{"title": "Implement getTopics", "number": 3598, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3598", "body": "This will return topics that were  in the topic selection UI, deduped on name."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3598#pullrequestreview-1172551878", "body": "I think we'll probably need a limit and sorting on this API (sort by score, or number of  s?)"}
{"title": "small fix", "number": 3599, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3599", "body": "I'll put in a proper fix for this with my next PR. This should do for now. \nHTTP 500s were caused by missing Bearer string when taking tokens from query strings"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3599#pullrequestreview-1171303650", "body": ""}
{"title": "Update Icon with sizes", "number": 36, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36", "body": "Following up on https://github.com/Chapter2Inc/codeswell/pull/30#discussion_r783550877\nRefactor Icon to include sizes."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#pullrequestreview-852403192", "body": ""}
{"comment": {"body": "@benedict-jw Would be nice to decide on a set of sizes.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#discussion_r784442878"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#pullrequestreview-853271401", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#pullrequestreview-853356079", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#pullrequestreview-853360220", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#pullrequestreview-853361029", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#pullrequestreview-853382964", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#pullrequestreview-853416496", "body": ""}
{"comment": {"body": "I'm wondering if this means that using abstract sizes (`small`, `medium`, `large`) is a mistake -- what if Ben introduces a 20-point icon?  `medium-large` ?   I don't feel strongly about this but if he adds another size we might want to just let icons have concrete sizes (`size-14`, `size-16`, etc...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#discussion_r785194876"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#pullrequestreview-853461831", "body": ""}
{"comment": {"body": "Wouldn't that mean we could hypothetically add an infinite number of sizing consts? At that point I guess I don't see much of a difference between adding a variable alias for each value vs using px and not using variables at all \r\n\r\nI do agree that the abstract size aliases only work if we really constrain ourselves with what we use (i.e. if we add a new value, how reusable will it be, and/or should we just change the value of the `medium` icon, etc)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#discussion_r785227765"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#pullrequestreview-853473321", "body": ""}
{"comment": {"body": "That's true.  I don't really mind either way, I think we'll know when things get out of hand (`icon-medium-largish-but-not-too-large`) and we can course-correct then.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/36#discussion_r785236724"}}
{"title": "[RFC] Message as proto", "number": 360, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360", "body": "POC\nThis is the equivalent of the JSON representation, but much more terse to define, neater because it supports oneof, is faster to serialize, and smaller on the wire.\nCodegen\nGradle task generates this.\nbuild/generated/source/proto/main/\n java\n  com\n      nextchaptersoftware\n          common\n              model\n                  MessageOuterClass.java\n js\n  block_blocklist_blockquote.js\n  blockcode.js\n  blockhorizontalline.js\n  blockimage.js\n  blocktext.js\n  formattedsegment.js\n  formattedtext.js\n  inlineelement.js\n  link.js\n  message.js\n  plainsegment.js\n kotlin\n  com\n      nextchaptersoftware\n          common\n              model\n                  BlockCodeKt.kt\n                  BlockHorizontalLineKt.kt\n                  BlockImageKt.kt\n                  BlockKt.kt\n                  BlockListKt.kt\n                  BlockQuoteKt.kt\n                  BlockTextKt.kt\n                  FormattedSegmentKt.kt\n                  FormattedTextKt.kt\n                  InlineElementKt.kt\n                  LinkKt.kt\n                  MessageKt.kt\n                  MessageOuterClassKt.kt\n                  PlainSegmentKt.kt\n ts\n     Message.ts\nYou can also run command line codegen:\nsh\nbrew install protoc\nnpm install -g ts-proto\nmkdir -p kotlin js ts\nprotoc -I=protos/ --kotlin_out=kotlin --js_out=js --ts_out=ts protos/Message.proto"}
{"comment": {"body": "Need to fix up the build scripts, but I can do that in follow up. Let's get this in so we iterate \u2705 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#issuecomment-1043640990"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-884881118", "body": ""}
{"comment": {"body": "Shouldn't this be in a top-level folder like the API specs?  Or just in the `/api` folder?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r808264851"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-884883965", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-884884957", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-884913307", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-884916773", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-884918859", "body": ""}
{"comment": {"body": "Allowing arbitrary blocks inside a block quote allows things like headers inside a block quote -- not sure if that's what we want?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r808301005"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-884954115", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-884954717", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-884958567", "body": ""}
{"comment": {"body": "Top-level of the monorepo is a mess. It contains `shared`, `api`, `sharedConfigs`, `openapi`, `docs`, `archtecture-design` -- each of which are shared code. Need to do some restructuring, but outside scope of this PR.\r\n\r\nThe reason I added a new top-level `common` directory for this PR is just to isolate for simplicity of review.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r808331156"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-885108540", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-885110172", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-885111458", "body": ""}
{"comment": {"body": "Supported in Markdown:\r\n\r\n\r\n> ### Header3 within a block quote one-level\r\n>> #### Header4 within a block quote two-levels", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r808429988"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886449607", "body": ""}
{"comment": {"body": "Can we move this stuff into /api (or rename both to /spec) ?  I appreciate the root folders are messy but adding another one makes things worse?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809394192"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886452304", "body": ""}
{"comment": {"body": "Move this up to the root package.json ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809395493"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886454566", "body": ""}
{"comment": {"body": "Curious, what's the difference between a string and a google.protobuf.StringValue?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809396472"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886459184", "body": ""}
{"comment": {"body": "Oh looks like it's already in there -- we should remove this package.json and the package-lock", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809399651"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886461694", "body": ""}
{"comment": {"body": "Yup. That's already planned work for Richie and I. We'll be tackling this in a separate PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809402462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886462012", "body": ""}
{"comment": {"body": "Idea is to consolidate API and common where it makes sense.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809402763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886466388", "body": ""}
{"comment": {"body": "Protobufs have some slight nuances for \"optional\" types.\r\n\r\n\"Optional\" doesn't actually make the field optional. From my understanding this is mainly for \"backwards compatibility\".\r\nhttps://github.com/stephenh/ts-proto/issues/139\r\n\r\nTherefore to get optinonality in our codegen, they suggest using these well known / wrapper types.\r\nhttps://github.com/stephenh/ts-proto#well-known-types\r\n\r\n`optional google.protobuf.StringValue language = 2;` will get generated as `string | undefined`\r\n`optional string language = 2;` will get generated as `string`\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809406801"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886472642", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886473649", "body": ""}
{"comment": {"body": "We should probably log and render nothing for now, we'll probably forget to change this and it will look pretty embarrassing in rendered output?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809412245"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886476952", "body": ""}
{"comment": {"body": "yes. That will be the case. I want to keep this in for now while I work on this part of the codebase but will definitely be cleaning this up.\r\n\r\nWill be tests to catch this case.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809414607"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886482010", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886483151", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886509823", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886558528", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886571315", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886572287", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886573050", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886573312", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886580297", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886614580", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886693090", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886695694", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886696628", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886707939", "body": "Going to approve this first as there doesn't seem to be anymore conversation on this.\nFeel free to leave any feedback here and I can jump on it. Would like to start building on this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886708607", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886717464", "body": ""}
{"comment": {"body": "Ohh no dev-dependencies :) can go into a future PR of course", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809587747"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-886721742", "body": ""}
{"comment": {"body": "I would think this should be a runtime dependency, since messages are de/encoded at runtime.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r809591138"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#pullrequestreview-887563602", "body": ""}
{"comment": {"body": "Because we build everything into bundles, there are no real runtime dependencies, at least not in the way node thinks of it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/360#discussion_r810199339"}}
{"title": "revert the heaeder change to see if 502s go away", "number": 3600, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3600"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3600#pullrequestreview-1171492653", "body": ""}
{"title": "Introduce TreeCache to improve speed of sourcemark lookup by file", "number": 3601, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3601", "body": "Reworked caching with a new TreeCache that invalidates itself when the top-level\nGit tree hash changes. This allows us to cache commits safely and immutably,\nwhich greatly reduces calls to these Git command invocations:\ngit branch --contains SHA\ngit cat-file -e SHA^{commit}\nResults (on private.yml)\nBefore\ncount     sum     max     avg cmd\n     76    1002      26    13.2 git branch --contains SHA\n     43     821      35    19.1 git cat-file -e SHA^{commit}\n      1      40      40    40.0 git diff --full-index --ignore-all-space --no-prefix --dst-prefix=||| --unified=0 --diff-filter=ct --diff-filter=ct SHA --\n      1      38      38    38.0 git diff --diff-filter=DR --find-renames=50% --name-status SHA --\n      4      33       9     8.3 git show SHA:FILE\nAfter\ncount     sum     max     avg cmd\n      4      41      12    10.3 git show SHA:FILE\n      1      39      39    39.0 git diff --diff-filter=DR --find-renames=50% --name-status SHA --\n      1      37      37    37.0 git diff --full-index --ignore-all-space --no-prefix --dst-prefix=||| --unified=0 --diff-filter=ct --diff-filter=ct SHA --"}
{"title": "Fix draft icon for notes and add dropdown for slack threads", "number": 3602, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3602"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3602#pullrequestreview-1172682556", "body": ""}
{"title": "[BREAKS API ON MAIN] Temp disable asset auth", "number": 3603, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3603", "body": "Due to video issues, we are unable to properly handle authenticating longer streaming videos with our tokens which expire after a minute.\nTemporarily disabling auth for Get Auth assets to unblock streaming video feature.\nSolution is to introduce a service which vends longer lived tokens which have claims on the specific assets."}
{"comment": {"body": "Closing. Using scoped tokens instead:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/3607", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3603#issuecomment-1307876248"}}
{"title": "[BREAKS API ON MAIN] Change getTopicRelatedInsights to a POST", "number": 3604, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3604", "body": "This PR just changes getTopicRelatedInsights to a POST. It's not being used right now by any clients, so we're OK to break.\nThis PR does not yet filter results by the sourcemark IDs and commit hashes (provided by the client) but we'll do that in another PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3604#pullrequestreview-1172675084", "body": ""}
{"title": "Add transcription webhook processing", "number": 3605, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3605"}
{"title": "[BREAKS API ON MAIN] Add transcription proto", "number": 3606, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#pullrequestreview-1172793462", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#pullrequestreview-1172828345", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#pullrequestreview-1172829935", "body": ""}
{"comment": {"body": "So this is a *real* breaking change now.\r\n\r\nI think we may need to update `EARLIEST_SUPPORTED_NUMBER`?\r\nSimilar to https://github.com/NextChapterSoftware/unblocked/pull/3565\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#discussion_r1017108541"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#pullrequestreview-1172830570", "body": ""}
{"comment": {"body": "Correctimundo. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#discussion_r1017108949"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#pullrequestreview-1172865940", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#pullrequestreview-1172885951", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#pullrequestreview-1172910055", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3606#pullrequestreview-1173362376", "body": ""}
{"title": "Introduce scoped auth token for long-lived resource access", "number": 3607, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607", "body": "Scoped tokens are special:\n\n\nThey only allow access to the exact resource (method + resourcePath) defined in token scope.\n  The entire resourcePath must match exactly.\n\n\nThey are long-lived. By default, the same expiry as the refresh token is used,\n  but we can optionally restrict this to a shorter lifetime by introducing\n  a maxLifetimeSeconds client query parameter or header in the future if there is a need.\n\n\nThe server has an allowlist of permitted scopes. Attempts to get a scoped token for any\n  other resource will fail. This is the initial set:\n\nGET /api/assets/teams/:id/:id"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607#pullrequestreview-1172841750", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607#pullrequestreview-1172843297", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607#pullrequestreview-1172846036", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607#pullrequestreview-1172847943", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607#pullrequestreview-1172849780", "body": ""}
{"comment": {"body": "Will we validate that the user has access to the requested resource?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607#discussion_r1017121728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607#pullrequestreview-1172853233", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607#pullrequestreview-1172854697", "body": ""}
{"comment": {"body": "yes, server will do that", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3607#discussion_r1017125181"}}
{"title": "[BREAKS API ON MAIN] Implement vending of scope token", "number": 3608, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608", "body": "API breaking is a non-event, I just removed two HTTP methods that are irrelevant for APIs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608#pullrequestreview-1172866545", "body": ""}
{"comment": {"body": "This is the key difference between an auth-token and a scoped-auth-token:\r\n1. they have a scope claim\r\n2. they are long lived", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608#discussion_r1017133547"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608#pullrequestreview-1173005902", "body": ""}
{"comment": {"body": "There are already some variants of this in STringExtensions.kt in lib-common.\r\nI would love to clean this shit up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608#discussion_r1017230985"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608#pullrequestreview-1173006431", "body": ""}
{"comment": {"body": "I did. now only one", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608#discussion_r1017231329"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608#pullrequestreview-1173008092", "body": ""}
{"comment": {"body": "Lol, I\"m blind. thank you.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608#discussion_r1017232197"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3608#pullrequestreview-1173010869", "body": "This looks good.\nCan someone give this guy a back rub."}
{"title": "[BREAKS API ON MAIN] Make TopicRelatedInsightsRequest fields optional", "number": 3609, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3609", "body": "When both fields are optional, no filtering on commit hashes or thread IDs will be applied."}
{"title": "Add VSCode Webview state API", "number": 361, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361", "body": "Webviews in VSCode are meant to be transient -- when you click on another tab, your webview is supposed to be destroyed, because web views are expensive and you can have a lot of tabs open.  Because webviews can have state (transient view-related state), VSCode provides an API for storing and restoring state as views are destroyed and recreated: \nThis PR provides a drop-in replacement for useState: useWebviewState(key, initialValue).  The key is a string that uniquely identifies the value within that webview.  As the state is modified, it is written to the Webview's state object.  When the webview is rendered, we pull values as needed from the state object.\nThe state object itself is a Recordstring, JsonValue -- that is, maps the state keys to the state values.  One key is special (props), that is used to store the last-rendered view props.  When the tab is re-focused, the result is a full set of both permanent view props and transient webview state, so the view can be rendered.\nSome caveats:\n\nOnly state that is JSON-serializable can be stored this way, since ultimately VSCode stores this in JSON.  So, no classes, no Maps, no Sets.  Plain typed objects and primitives.\nThe key is required because otherwise there is no reliable way to map state values back to state usages when deserializing.  I looked at whether React provides facilities for hooks for this, but didn't find anything promising.\nThere is one final piece to this puzzle: for webview panels, we need to implement a vscode.WebviewPanelSerializer, which will allow webview panels to re-open correctly when VSCode restarts."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-883955898", "body": ""}
{"comment": {"body": "Add source maps to VSCode webview bundles, so you can debug webviews.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#discussion_r807536434"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-884952577", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-884955177", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-884968333", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-885134709", "body": ""}
{"comment": {"body": "Interesting. Does this mean that the child webview component could conceivably send back a 'rerender' command and force rerender the UI? And if so, is that something we'd want to guard against or is it a good tool to have? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#discussion_r808446694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-885135796", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-885137097", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-885137304", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-885312888", "body": ""}
{"comment": {"body": "No this is unidirectional -- the extension is telling the webview to re-render, given its latest values.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#discussion_r808580155"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-885313162", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-885340495", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-886250144", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/361#pullrequestreview-886250333", "body": ""}
{"title": "Clip long thread title in tooltip", "number": 3610, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3610", "body": "On the chance that the first paragraph of a given thread is very long, we should clip the thread title string in the tooltip to be at a legible length. \nbefore:\n\nafter:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3610#pullrequestreview-1173013297", "body": ""}
{"title": "Add kotlin flag for dashboard topics UI", "number": 3611, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3611", "body": "Add capability flag for the topics/insights UI(s) in the web dashboard"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3611#pullrequestreview-1172897651", "body": ""}
{"title": "Filter topic search results by commit hashes and thread ids", "number": 3612, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3612"}
{"title": "[BREAKS API ON MAIN] Change getScopedAccess to POST", "number": 3613, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3613", "body": "Forgot that GET can't take a request body."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3613#pullrequestreview-1173085631", "body": ""}
{"title": "Optimize point upload payload", "number": 3614, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3614", "body": "When uploading optional booleans, false is equivalent to not sending a field.\nAdds up when we send 100,000s of points."}
{"title": "Scoped Authed video assets", "number": 3615, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615", "body": "We implemented the video assets in a similar manner to image assets. The plan was to use useAssetUrl to auth the GET request. This used a helper to download the entire asset at once (e.g. image) with an auth token and then created an objectURL.\nThis was okay for images since we needed to download the entire image before displaying the image.\nThis is not the case for video We do not want to download the entire video before the user can watch.\nTherefore, we need to use a streaming urls. This led to another problem where these streaming urls were only valid for 1 minute as they used our standard Auth Tokens. Video media requests (which used byte-range to stream) longer than 1 minute would eventually 401, causing the video to stop loading.\nTherefore, scoped auth tokens (https://github.com/NextChapterSoftware/unblocked/pull/3607) were introduced so we could utilize long lived auth tokens (7 days) and watch long videos :) \n\nParses messages and generates scoped auth tokens for each video metadata resource\nScoped auth tokens are injected into VideoBlockRenderer using contexts (similar to auth / team members)"}
{"comment": {"body": "Large caveat.\r\n\r\nDue to Github's media-src CSP, video not working in web extension atm... Exploring options.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#issuecomment-1309387804"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1173412901", "body": "good from my side"}
{"comment": {"body": "\ud83d\udc4d matches server allow list:\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/blob/7d3a76ff57feb4e925f83cab18de9b29b0938975/projects/libs/lib-security/src/main/kotlin/com/nextchaptersoftware/security/ScopedResource.kt#L42-L44", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1017503969"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174459918", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174467766", "body": ""}
{"comment": {"body": "Should use logger?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1018221607"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174476524", "body": ""}
{"comment": {"body": "Would it be possible to generate tokens for the `/api/assets/teams/:id` scope so that we don't need to continuously create new tokens for every new video we view?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1018227678"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174506188", "body": ""}
{"comment": {"body": "I remember having issues with using Logger in shared components? I'm not sure if that's changed..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1018248703"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174511857", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174522887", "body": ""}
{"comment": {"body": "I think creating a new token for every video was a feature we wanted? Tokens should be scoped to the individual asset / url .", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1018259980"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174901378", "body": ""}
{"comment": {"body": "We definitely do not want to give blanket long-lived access to wildcard resources.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1018520500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174913406", "body": ""}
{"comment": {"body": "If you import from `webUtils/log` it seems to work OK in the scenarios I've tried.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1018529855"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174916750", "body": ""}
{"comment": {"body": "I think a stream might be the wrong representation for this.  This is a stream that publishes a map of all asset tokens we've ever requested, and after a day (or however long the token lives for) the tokens will stop working.  The stream will also contain tokens that we don't actually need for a given webview.\r\n\r\nThe requesting code in the discussion thread really wants to treat this as more of an async request.  Maybe this API should be an async method that returns the set of tokens for a particular set of assets (as required in the discussion thread), and internally this class can cache the values for some period of time (a few hours, or whatever) ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1018532554"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174918239", "body": ""}
{"comment": {"body": "Ah... now that I look at this, I can see why this might not work as-is, specifically it wouldn't work in the dashboard.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1018533700"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174918774", "body": ""}
{"comment": {"body": "nit: Not sure Standard/Backup is very meaningful, `VideoWithMetadata`, `VideoWitthoutMetadata` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#discussion_r1018534082"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3615#pullrequestreview-1174925604", "body": "Looks good.\nWe will definitely need some maintenance on the published stream, as the values will expire and (I think) cause bugs."}
{"title": "[BREAKS API ON MAIN] Cleanup api", "number": 3616, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3616"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3616#pullrequestreview-1174377887", "body": ""}
{"title": "Fix styling", "number": 3617, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3617", "body": "Before\n\nAfter\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3617#pullrequestreview-1174945068", "body": ""}
{"title": "Minor cleanup", "number": 3618, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3618"}
{"title": "Update xcode walkthrough icon", "number": 3619, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3619", "body": "\nAdd walkthrough specific icon.\nUpdate description to be based off Marks instead of deprecated sourceMarks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3619#pullrequestreview-1174624684", "body": "thanks, nice work."}
{"comment": {"body": "This shouldn't really ever happen, but in the unlikely event that it does, I think this should just be an empty string.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3619#discussion_r1018329506"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3619#pullrequestreview-1174625905", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3619#pullrequestreview-1174766082", "body": ""}
{"title": "Fix issues with openAipClean running always", "number": 362, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/362", "body": "This pr addresses a fine point of gradle tasks.\nIf you create a gradle task that is not defined as a specific type i.e. task(type: Delete), it will always run.\nAlso cleaning up other stuff."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/362#pullrequestreview-884953115", "body": ""}
{"comment": {"body": "This guarantees that deleting the apitOutputDir is only done on a clean invocation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/362#discussion_r808327063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/362#pullrequestreview-884958785", "body": ""}
{"title": "Create migration to drop ThreadSearchModel table from the database", "number": 3620, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3620", "body": "Part two of https://github.com/NextChapterSoftware/unblocked/pull/3592\nI've tested this locally and it dropped the table without issues. Once merged I'll do this in dev first and verify there are no issues before running in prod."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3620#pullrequestreview-1174947454", "body": ""}
{"title": "TranscriptionModelChanges", "number": 3621, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3621", "body": "ADd transcriptoin versioning\nUpdate"}
{"title": "Swift Format", "number": 3622, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3622", "body": "Need formatting...\nCan wait until Peter comes back to verify everything okay."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3622#pullrequestreview-1175898227", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3622#pullrequestreview-1181706789", "body": ""}
{"title": "Filter apps from being monitored", "number": 3623, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3623", "body": "Ignore certain apps from being captured.\nMain ones are VSCode (and its cousins) as file marks are already being captured by the extension."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3623#pullrequestreview-1174872022", "body": ""}
{"title": "New stop word list", "number": 3624, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3624", "body": "Still tuning this, but want to test something. \nBasically this creates a new stop word list by creating \"gowords\" that are most likely to be use in a developer project. \nThis list is generated from word count of the entire unblocked db including partners (see the primary unblocked notebook) and then hand tuned for now.\nThis passes the local build test, but I am not too sure how to run this locally yet? Going to take a stab at that now.\nBasically you run dictionary.ipynb to get a new client/EnglishTopWords.json + service/english_top_words.txt."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3624#pullrequestreview-**********", "body": ""}
{"title": "Add topics to explorer insights UI", "number": 3625, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625", "body": "Add topics to the explorer insights panel.\n\nAdd TopicStore, which is a store that fetches and caches the topics for a team\nRefactor ExplorerInsightsWebviewProvider to allow sourcing from unfiltered and filtered data\nUpdate UI panel to display topics in pills\n\nNote: right now the topic data is not filtered by the currently-selected file, that will come in the next PR.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This was a preexisting bug in this stream -- the PR stream could get stuck in the 'uninitialized' state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#discussion_r1018504584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Do we want a \"disabled\" state if not clickable?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#discussion_r1018542852"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#pullrequestreview-**********", "body": ""}
{"comment": {"body": "not just a cursor but maybe background / opacity /etc..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#discussion_r1018542970"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#pullrequestreview-1174931860", "body": ""}
{"comment": {"body": "There's no design for that right now... we can add it whenever we need it I think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#discussion_r1018543419"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#pullrequestreview-1176234857", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#pullrequestreview-1176237885", "body": ""}
{"comment": {"body": "Quite the stream....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#discussion_r1019441817"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#pullrequestreview-1176239279", "body": ""}
{"comment": {"body": "If you ahve any suggestions on ways of making this better let me know -- I should probably add some comments to make the behaviour more clear", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#discussion_r1019442780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#pullrequestreview-1176242367", "body": ""}
{"comment": {"body": "Effectively the stream takes in the set of commits and threads relevant to the current file, and async-maps these via the API (getTopicRelatedInsights) -- so that we end up getting hte set of insights relevant to this file.\n\n\n\nThe second bit is solely about mapping ThreadInfos to ThreadInfoAggregates, which is unfortunate.  Maybe extracting these operations into separate functions would help.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#discussion_r1019444794"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3625#pullrequestreview-1176245755", "body": "Streams are getting quite complex. This would have been much more troublesome without them though..."}
{"title": "Create extractUnigramsAndBigrams function", "number": 3626, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3626", "body": "The idea here is to use this function to extract 1-gram and 2-grams from a list of filepaths provided by the client.\nFor example, the client would send to the API service (via the getRecommendedTopics operation) a list of file paths like:\n[\n    \"api/service/module\",\n    \"test/SearchService\",\n]\nand the function would produce\n[\n    \"api\",\n    \"service\",\n    \"module\",\n    \"test\",\n    \"search\",\n    \"api service\",\n    \"service module\",\n    \"test search\",\n    \"search service\",\n]\nwhich we would then score against the histogram generated from PR and slack data before returning to the client as recommended topics.\nWe still need to update our histogram generation logic to extract 2-grams from PR and slack data (to come in a separate PR)."}
{"comment": {"body": "Looks good.  One thing I'm not sure about is what we want to use as the canonical representation of bigrams -- are they strings with a space (`source marks`) or are they an array of two strings (`['source', 'marks']`).  I guess this depends on how we end up using this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3626#issuecomment-1309532327"}}
{"comment": {"body": "@matthewjamesadam good point. For our current use case, a string with a space makes sense since full text search works well with strings containing spaces. \r\n\r\nFor down the road, if we need to transform these strings into arrays for better topic association we can transform them easily.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3626#issuecomment-1309543117"}}
{"comment": {"body": "I'm having a hard time separating what's long term code and what's experimental?\r\nI wonder if we should add a Java annotation for these sort of things so we can easily look them up later.\r\n\r\nI ask because I if it is **not** experimental, is the long term plan to move away from doing these sort of heavy weight operations being performed directly from the apiservice?\r\n\r\n@davidkwlam @matthewjamesadam ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3626#issuecomment-1310398000"}}
{"comment": {"body": "Let's move this into a topic service. Can you show me the steps to create a new service after scrum?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3626#issuecomment-1310481559"}}
{"comment": {"body": "> I'm having a hard time separating what's long term code and what's experimental? I wonder if we should add a Java annotation for these sort of things so we can easily look them up later.\r\n> \r\n> I ask because I if it is **not** experimental, is the long term plan to move away from doing these sort of heavy weight operations being performed directly from the apiservice?\r\n> \r\n> @davidkwlam @matthewjamesadam ?\r\n\r\nThis is a good question -- we have two streams of development happening right now:\r\n\r\n1) This stream involves updating our existing histogram/counting algorithms to give better topic results.  This is still somewhat experimental (in that, if the results are no better, we'll probably throw it out), but we feel reasonably confident that this will make things better.\r\n2) Doug is working on a separate stream exploring using other ML and algorithmic tools to approach the problem from different angles.  This is entirely experimental.\r\n\r\nI think it makes sense to put a bit of work into making sure the architecture for this makes sense, as we're likely to do something like this work regardless of the approach.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3626#issuecomment-1310634289"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3626#pullrequestreview-1174892216", "body": "Lgtm"}
{"title": "Add transcription backend", "number": 3627, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3627"}
{"title": "Add asset helpers", "number": 3628, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3628"}
{"title": "Make my life easier", "number": 3629, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3629"}
{"title": "Fix up zallyLint", "number": 363, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/363", "body": "We want to run zallyLint against multiple specs (private.yml and message.yml in jeff's pr)"}
{"title": "update", "number": 3630, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3630"}
{"title": "Refactor sourcemark module to shared for shim app reuse", "number": 3631, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#pullrequestreview-**********", "body": ""}
{"comment": {"body": "The downside with this is that in VSCode our extension may use a different git instance then the rest of VSCode does.  So for instance anyone using the VSCode extension on Windows (which has happened before) will stop working.\r\n\r\nCould we pass the provider into the constructor, and VSCode could use the existing logic to pass in either the VSCode or backup git provider?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#discussion_r1019619877"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#pullrequestreview-**********", "body": ""}
{"comment": {"body": "(Could be deferred to a later PR to fix, too)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#discussion_r1019620649"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#pullrequestreview-**********", "body": ""}
{"comment": {"body": "What would happen in this case? VSCode-specific Git functionality would be covered by the VSCode Git provider (which would continue to work) and the regular Git functionality (mostly sourcemarks) would be covered by `which git` (which would also continue to work).\r\n\r\nNot actually clear to me how this would break.\r\n\r\nI can fix \u2014 just means a bit of plumbing \u2014 but trying to understand the failure scenario.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#discussion_r1019629102"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#pullrequestreview-**********", "body": ""}
{"comment": {"body": "`which git` only works on MacOS, so the people somehow using this on Windows would stop being able to use sourcemarks.  It's definitely not the highest priority.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#discussion_r1019633172"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#pullrequestreview-**********", "body": ""}
{"comment": {"body": "ah", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#discussion_r1019633691"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#pullrequestreview-1176536839", "body": "Looks good, thanks! "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#pullrequestreview-1176649782", "body": ""}
{"comment": {"body": "Fixing this in follow-up, since it's not urgent.\r\n\r\nMy plan is to just support Windows git lookup.\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/3643", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3631#discussion_r1019723314"}}
{"title": "AddServiceDocs", "number": 3632, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3632", "body": "update\nupdate"}
{"title": "Show per-file, topic-filtered content in explorer insights UI", "number": 3633, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3633", "body": "Filter the set of displayed topics so only relevant topics for the current file are displayed\nWhen using a topic filter, only show the set of insights that are relevant to the current file"}
{"comment": {"body": "I'm merging this so we can make a build to try out.  Please leave review comments if you see any issues.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3633#issuecomment-1311012433"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3633#pullrequestreview-1176488157", "body": ""}
{"comment": {"body": "Had to swap this around to support cases where there are *no* topics for a given file, so we don't display the topic bar", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3633#discussion_r1019608903"}}
{"title": "Add transcription tests", "number": 3634, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3634"}
{"title": "Enable extractUnigramsAndBigrams function", "number": 3635, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3635"}
{"title": "Fix minor admin error", "number": 3636, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3636"}
{"title": "Asset info", "number": 3637, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3637"}
{"title": "add topic service", "number": 3638, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3638", "body": "First pr related to topic service.\nnext pr is to add ci deployment."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3638#pullrequestreview-1176583987", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3638#pullrequestreview-1176594084", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3638#pullrequestreview-1176598480", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3638#pullrequestreview-1176608775", "body": ""}
{"title": "Deploy tpoic service", "number": 3639, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3639"}
{"title": "Create repo in runtime fixtures", "number": 364, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/364", "body": "Haven't tested this works"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/364#pullrequestreview-885032543", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/364#pullrequestreview-885104636", "body": ""}
{"comment": {"body": "@davidkwlam yes, it is correct\r\n```sh\r\ngit rev-list --max-parents=0 HEAD\r\n```\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/364#discussion_r808425104"}}
{"title": "Revert \"Create migration to drop ThreadSearchModel table from the dat", "number": 3640, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3640", "body": "abase (#3620)\"\nThis reverts commit 9adef99082d655d1d0b65ecec9d3ff853d3c2407."}
{"title": "update", "number": 3641, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3641"}
{"title": "FixAnnotation", "number": 3642, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3642", "body": "Fix annotation\nupdate"}
{"title": "Utility to find Git on Mac and Windows", "number": 3643, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3643", "body": "Based on VSCode implementation:\n\nNote\nTested ok on macOS; did not test on Windows.\nRelated\nRelated to, but does not fix, this:\n"}
{"comment": {"body": "> This is all copied from VSCode right?\r\n\r\nMostly (like 90%). Changed a few things to fit in with our process module.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3643#issuecomment-1315680993"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3643#pullrequestreview-1181283527", "body": "This is all copied from VSCode right?"}
{"title": "Only remove separators for API call", "number": 3644, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3644"}
{"title": "Ran npm audit fix", "number": 3645, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3645"}
{"comment": {"body": "Every time I run this I get a different result. And running `npm install` after rewrites part of the package-lock.\r\n\r\nI don't trust this enough or understand what it's doing. abandon.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3645#issuecomment-1314207485"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3645#pullrequestreview-1179525109", "body": "Is it normal for there to be no associated package.json changes?  Also a bit odd that there are way more additions then removals but maybe that's normal..."}
{"title": "Generated stopwords using ratings + initial BERT training", "number": 3646, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3646", "body": "Our previous stopwords were generated via len(all_unblocked_db_text). This set of stopwords is generate from 10hr of BERT time over the 290,000 PR title+descriptions and the ratings."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3646#pullrequestreview-1179545850", "body": ""}
{"title": "FixPlurals", "number": 3647, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3647", "body": "Bad plural for meadata\nFix build"}
{"title": "Move transcription keys out of global config", "number": 3648, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3648"}
{"title": "Show all topics for team in explorer insights UI", "number": 3649, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3649", "body": "This is a temporary measure for now -- because we don't have a good way of generating the set of topics relevant to a file, we will show the top 20 topics for the team."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3649#pullrequestreview-1179717312", "body": ""}
{"title": "Allow getting all threads", "number": 365, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/365", "body": "For the lovely matt"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/365#pullrequestreview-885028937", "body": ""}
{"title": "added new queue and queue perms", "number": 3650, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3650", "body": "Add the new queue for topic_recommendations \nModify API service EKS/IAM permissions to allow publishing messages to the new queue \nModify Topic service EKS/IAM permissions to consuming messages from queue. \n\nOnce this change is deployed via CI/CD I still need to deploy the EKS permissions manually."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3650#pullrequestreview-1179796186", "body": ""}
{"title": "Moves the sourcemark interface down to shared layer for reuse", "number": 3651, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3651", "body": "Just refactoring, no behaviour change."}
{"title": "Add basic scaffolding for service generation", "number": 3652, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3652", "body": "We now are using ansible template functionality to generate a kotlin scaffolding for a new service.\nMuch less error prone.\nAs richie once said to me, do it right once, do it right forever."}
{"title": "VSCode scrollbars overlay content", "number": 3653, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3653", "body": "This CSS property is only supported in Chrome, but since VSCode only uses Chrome it's fine.  I didn't modify anything outside of the VSCode UI."}
{"title": "Update queues", "number": 3654, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3654"}
{"title": "Local stack support hub", "number": 3655, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3655"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3655#pullrequestreview-1179900107", "body": ""}
{"title": "Add make targets", "number": 3656, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3656"}
{"title": "Revert \"VSCode scrollbars overlay content (#3653)\"", "number": 3657, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3657", "body": "This reverts commit f78dac653cc4390c7cb15c7152339f0f28b52f70."}
{"comment": {"body": "Say it ain't so Matt. Please. Say it ain't so", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3657#issuecomment-1314543547"}}
{"comment": {"body": "> Say it ain't so Matt. Please. Say it ain't so\r\n\r\nSadly, it is so.  We'll fix this some day, but not today.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3657#issuecomment-1314545853"}}
{"title": "Return PR comment counts in getTopicRelatedInsights operation", "number": 3658, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3658"}
{"title": "getRankedTopics does not count frequencies in input", "number": 3659, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3659"}
{"title": "VSCode 'Extract topics with bigrams' command", "number": 3660, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3660", "body": "Remove the old commands for extracting topics with only local data, or only PR or slack data.  We will always use all data we have.\nAdd command to extract topics including bigrams, which returns the full folder set, unparsed and unfiltered.  The only folders we filter are .git and node_modules."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3660#pullrequestreview-1180060214", "body": ""}
{"title": "Headless sourcemark agent", "number": 3661, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3661", "body": "A node app that runs a headless version of the sourcemark agent.\nSeveral things are missing for this to actually be useful:\n\nauth\ndatastore for consuming SM API content\nRPC for interfacing with VSCode"}
{"comment": {"body": "Can someone give this a man a high five?????", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3661#issuecomment-1314673019"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3661#pullrequestreview-1180107252", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3661#pullrequestreview-1181269279", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3661#pullrequestreview-1181350579", "body": ""}
{"title": "Fix bug in SM store cache", "number": 3662, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3662", "body": "Key was always:\njs\n'sourcemarks.[object Object].[object Object]'\nNot fully sure what the impact was, but definitely a bug."}
{"title": "Headless source agent: CI", "number": 3663, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3663", "body": "Bundling this with the VSCode CI job for now as it seems pretty wasteful to create a separate CI pipeline for this.\nEventually, we'll definitely have a separate CI pipeline.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3663#pullrequestreview-1181437773", "body": ""}
{"comment": {"body": "Need at least one test or jest will puke", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3663#discussion_r1023182093"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3663#pullrequestreview-1181628151", "body": ""}
{"title": "Add app version to settings menu", "number": 3664, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3664", "body": ""}
{"comment": {"body": "magic", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3664#issuecomment-1315896860"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3664#pullrequestreview-1181571249", "body": ""}
{"title": "Add Sentry to clients", "number": 3665, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665", "body": "For VSCode:\n1. We've added sentry transport to logger\n2. We've added sentry to catch unhandled node exceptions\nFor Web:\n1. We've added sentry to catch unhandled exceptions in React.\nTODO:\n1. Add to extension.\n2. Figure out if we can log to sentry via transport in web world.\n3. Write a doc about when to use Sentry vs Logz.io vs Honeycomb."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181601454", "body": "awesome thank you  \nThe next thing I'd love to see is Release integration, where Sentry correlates error spikes with the ProductNumber that we expose in prod. This will help us track newly introduced regressions quickly.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181610673", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181611648", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181626660", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181667106", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181723920", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181745605", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181746167", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181746499", "body": ""}
{"comment": {"body": "If we want logged errors in webviews to hit sentry we'll also need to add this to `VSCodeWebviewLogger` I think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#discussion_r1023403049"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181747883", "body": ""}
{"comment": {"body": "Actually we probably just want to add this to the base logger so all logged errors in all clients go to sentry?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#discussion_r1023404175"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181837886", "body": ""}
{"comment": {"body": "I haven't tested the sentry logger from webviews as it'll require sentry-node dependency.\r\nWill have to add polyfills for that.\r\nI'll investigate in another pr how much work this'll be versus me writing my own web-friendly sentry transporter.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#discussion_r1023472313"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1181943540", "body": ""}
{"comment": {"body": "I suspect we could copy `winston-transport-sentry-node` and just replace its usage of `sentry-node` with `sentry-browser` and it would mostly work...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#discussion_r1023549592"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1182835597", "body": ""}
{"comment": {"body": "Wrap with ThrottlingTransport here too?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#discussion_r1024172095"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#pullrequestreview-1183069385", "body": ""}
{"comment": {"body": "Agreed!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3665#discussion_r1024321815"}}
{"title": "Remove topics from explorer insights panel", "number": 3666, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3666", "body": "We're removing the topic pills for now.\nAlso, fix a bug where bad sourcemark data caused duplicate thread entries in the view, which broke things."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3666#pullrequestreview-1181627006", "body": ""}
{"title": "Add getRelatedTopics operation", "number": 3667, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3667", "body": "For a given file we want to show the list of related topics, which will be clickable in VS Code and bounce us to the dashboard to show all insights for a topic.\nThis is not the final implementation. Next PR will take TopicRelatedInsightsRequest and filter the returned topics by the thread IDs and commit hashes provided by the client."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3667#pullrequestreview-1181635336", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3667#pullrequestreview-1181643867", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3667#pullrequestreview-1181648322", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3667#pullrequestreview-1181649719", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3667#pullrequestreview-1181650576", "body": ""}
{"title": "Add messages page", "number": 3668, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3668"}
{"title": "Fix for window observability issues resulting in missed slurps", "number": 3669, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3669"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3669#pullrequestreview-1181663987", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3669#pullrequestreview-1181665678", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3669#pullrequestreview-1181671499", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3669#pullrequestreview-1181672049", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3669#pullrequestreview-1181686757", "body": ""}
{"comment": {"body": "The `retry` pattern I built for tasks is a little too heavy weight for this sort of thing (requires errors etc). But if this pops up in any more places I will abstract this away", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3669#discussion_r1023358217"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3669#pullrequestreview-1181691175", "body": ""}
{"title": "Make If-Modified-Since header optional for getThreads", "number": 367, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/367"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/367#pullrequestreview-885147373", "body": ""}
{"title": "Add models to map threads and pull requests to topics", "number": 3670, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3670"}
{"title": "Fix inheritance bug in ThrottlingTransport", "number": 3671, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3671", "body": "The options from the Transport were not applied to winston when extended by ThrottlingTransport.\nImpact was that we were always sending debug logs from VSCode to the LogzIO transport,\neven though the LogzIO transport options said otherwise (level: 'info')."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3671#pullrequestreview-1183119775", "body": ""}
{"comment": {"body": "Ah the ambiguity here goes both ways -- `opts` allows providing these values, and my assumption is that you would pass these into the ThrottlingTransport.  With this change there is still ambiguity (ie, `opts.format`, `opts.level` and so on will be ignored).\r\n\r\nWe could make this explicit by removing the `TransportStreamOptions &` from the `ThrottlingTransportOptions` definition above.  That way it's unambiguous that the transport settings go on the wrapped transport, and the wrapping transport respects those settings.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3671#discussion_r1024359343"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3671#pullrequestreview-1183124630", "body": "Thank you Matt!!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3671#pullrequestreview-1183134889", "body": ""}
{"comment": {"body": "The core problem is that the inheritance model is wrong here.\r\n\r\nWe either need the concept of a \"mixin\" to override the log method for all transports; or we need to override each of the winston transports individually (eg: ThrottlingFileTransport, ThrottlingConsoleTransport, etc).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3671#discussion_r1024371877"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3671#pullrequestreview-1183144478", "body": ""}
{"comment": {"body": "For now, going with your suggestion: https://github.com/NextChapterSoftware/unblocked/pull/3671/commits/86220e0c18edef0254f17df53ffbafe69d161d4a", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3671#discussion_r1024379957"}}
{"title": "Headless source agent: integrate with store to access cloud sourcemarks", "number": 3672, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3672", "body": ""}
{"title": "Revert stop word dictionary and only ignore stop words for unigrams", "number": 3673, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3673"}
{"title": "[BREAKS API ON MAIN] Add additional bools for video metadata", "number": 3674, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3674"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3674#pullrequestreview-1183179453", "body": ""}
{"title": "Add audio and camera metadata to walkthrough GRPC session", "number": 3675, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3675"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3675#pullrequestreview-1183172494", "body": ""}
{"title": "Helm docs", "number": 3676, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3676"}
{"title": "more helm doc changes", "number": 3677, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3677"}
{"title": "Add transcription is processing", "number": 3678, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3678"}
{"title": "Combine unigram topic with its bigram version", "number": 3679, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3679"}
{"title": "Add indentation logic to treeView", "number": 368, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/368", "body": "This pr adds the basic ability to indent treeItems.\nThe approach is a dynamic generator for paddings based off a maximum number of columns allowed.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/368#pullrequestreview-886346371", "body": ""}
{"title": "Escalate accessibility logging to error level", "number": 3680, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3680"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3680#pullrequestreview-1183352703", "body": ""}
{"title": "Allow mic and camera selection even when disabled", "number": 3681, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3681", "body": "Resolves UNB-751\nTested that this works. A previous change I made allows for this. Previously it would boot the mic/camera stream prematurely and crash."}
{"title": "Add custom sentry transporter fro browser", "number": 3682, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3682", "body": "As my compatriot Matt Adam has suggested, were migration sentry transporter to use @sentry/browser.\nConfirmed it works for both web and vscode\nThe old winston sentry transporters were node-specific and did not work in non-node environments without a lot of pain."}
{"comment": {"body": "> Put it in the base logger, then the web extension and SM agent will get this by default too?\r\n\r\nGood point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3682#issuecomment-1317776571"}}
{"comment": {"body": "Will address Matt's points in another pr.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3682#issuecomment-1317778632"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3682#pullrequestreview-1183424257", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3682#pullrequestreview-1183432932", "body": "Put it in the base logger, then the web extension and SM agent will get this by default too?"}
{"title": "Increase Slurp observability retries", "number": 3683, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3683"}
{"title": "Move getTopics logic to getRelevantTopics", "number": 3684, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3684", "body": "This makes it reusable"}
{"title": "Add per-file topic pills in explorer insight UI", "number": 3685, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3685", "body": "Display per-file topic pills at the bottom of the explorer insights UI.  This queries the getRelatedTopics API for each file and displays the resulting topics in the UI.  Note: these are not actually hooked up yet -- once Kay gets the topic pages done we can hook these up.\nNext PR will add the fade-out and scrolling buttons on the edges of the topic pill windows, when the topic pills overflow the content space.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3685#pullrequestreview-1184589484", "body": ""}
{"title": "[BREAKS API ON MAIN] Add transcription error cause", "number": 3686, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3686", "body": "They didn't document this, but found an error cause field in their apis."}
{"title": "Source agent will fully recalculate a repo then die", "number": 3687, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3687"}
{"title": "Update english_top_words.txt", "number": 3688, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3688"}
{"title": "Improve slurping reliability and use window title instead of web element title", "number": 3689, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689", "body": "There are some timing issues with accessibility that can't be resolved with the usual main queue dance. This PR adds some additional timing tricks, and pulls the \"title\" field from the root element (window) instead of the web element. Safari is an example where titles are not \"slurpable\" from the web element."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#pullrequestreview-1183492387", "body": ""}
{"comment": {"body": "Bordering on the ridiculous, but it appears as if the event gets ahead of the accessibility element tree and requires a small wait to make sure things \"align\"", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#discussion_r1024620216"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#pullrequestreview-1183492905", "body": ""}
{"comment": {"body": "This is necessary when switching apps across spaces or stage manager. Accessibility completely breaks in these cases", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#discussion_r1024620625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#pullrequestreview-1183494648", "body": ""}
{"comment": {"body": "Use the window instead of the element to get the title. I'm not sure where the title is derived from but it always bubbles up to the window unless overridden by the app", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#discussion_r1024622055"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#pullrequestreview-1183496204", "body": ""}
{"comment": {"body": "Tested with all the things:\r\n- Linear\r\n- Notion\r\n- Slack\r\n- Docker\r\n- Safari\r\n- Chrome\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#discussion_r1024623206"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#pullrequestreview-1183497868", "body": ""}
{"comment": {"body": "Feels like another place where we should try with increasing delays so that if the timing on a particular app or OS changes this doesn't break?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#discussion_r1024624413"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#pullrequestreview-1183498907", "body": ""}
{"comment": {"body": "Yeah it might be best to do the same \"polling\" trick here up to 1 second", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3689#discussion_r1024625199"}}
{"title": "[RFC] Remove POSTs and do creates with PUTs", "number": 369, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/369", "body": "Matt pointed out that we could remove the POST operations for creating threads and messages, and instead do them through PUTs (which would also be used for updates). \nThe service would create or update based the presence of certain properties in the request body. For example, if the PutThreadRequest has a message, then we'd attempt to create, otherwise we'd just do an update.\nThoughts?"}
{"comment": {"body": "I'm just going to say this right now.\r\n@matthewjamesadam is a lovely dude.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/369#issuecomment-1042394733"}}
{"comment": {"body": "I'm thinking of possibly adding a zally rule for  this.\r\nEnforcing puts.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/369#issuecomment-1042395168"}}
{"comment": {"body": "Prefer to keep two separate operations (POST=create, PUT=update), where each operation is strongly typed (all required fields). Both operations would still be idempotent as long as ID is a required param.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/369#issuecomment-1042419385"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/369#pullrequestreview-885248922", "body": ""}
{"title": "Codify retry pattern for slurping", "number": 3690, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3690"}
{"title": "Not returning error cause correctly", "number": 3691, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3691"}
{"title": "Revert", "number": 3692, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3692"}
{"title": "contact support", "number": 3693, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3693"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3693#pullrequestreview-1184878596", "body": ""}
{"title": "Use stores to get TLCs and reviews for a pull request", "number": 3694, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3694"}
{"title": "Unify sentry across react and node", "number": 3695, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3695"}
{"title": "Prevent observation retries clobbering the next observation set", "number": 3696, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3696", "body": "Adding retries to observation surfaced a race condition where the user flips to another app while the previous app retries are still in flight, then the previous app retries clobber the new observation. \nThe PR also adds url sanitization for Notion specifically"}
{"title": "Fix SearchInxingJob logging", "number": 3697, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3697"}
{"title": "add more cpu/mem to search service.", "number": 3698, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3698", "body": "CPU pressure was causing throttling and that was making health probes fail. This should hopefully address the pod crash loop."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3698#pullrequestreview-**********", "body": ""}
{"title": "Add screen capture error state, prompting user to restart their Mac", "number": 3699, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3699", "body": "\nResolves UNB-750"}
{"title": "[RFC] github app auth flow proposal", "number": 37, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/37", "body": "Stateless GitHub App Based Auth\nInstall PlantUML IntelliJ plugin to view. \nThis auth flow assumes the GitHub App is already installed. Auth while installing is a separate flow that needs its own sequence diagram.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/37#pullrequestreview-853837819", "body": "looks great"}
{"comment": {"body": "So `api` has the public part of the auth-service keypair? Or does `api` make a server-2-server request to `auth` to verify?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/37#discussion_r785611324"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/37#pullrequestreview-853859898", "body": ""}
{"comment": {"body": "api should have the pub key and self-verify. There's no benefit to calling the auth service for verification (in fact it adds risk). If we want to really be pedantic about this, the auth service shouldn't even mint tokens because it's public facing. We might want to introduce a secrets service downstream of the auth service. Even in that scenario the pub key should still sit on the api service. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/37#discussion_r785628231"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/37#pullrequestreview-853860953", "body": ""}
{"comment": {"body": "I would introduce the secret service at step 11. There's an open question here about steps 7-10, because I'm not sure if having the auth service make calls to Github for user info is the right move here. We could introduce an identity proxy to handle that little dance and decorate the userInfo. Thoughts?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/37#discussion_r785629032"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/37#pullrequestreview-853896798", "body": ""}
{"comment": {"body": "> api should have the pub key and self-verify. There's no benefit to calling the auth service for verification\r\n\r\n@pwerry I think that\u2019s fine. Just calling out one risk of the approach, which is that the auth and api services are deployment coupled due to their shared keypair; rotating the keys needs to be coordinated.\r\n\r\nSolvable with testing: an end-to-end login test run on deployment of either service in a full CD setup will catch fuckups.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/37#discussion_r785647991"}}
{"title": "Redis credentials rollout", "number": 370, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/370", "body": "Created a new IAM mapped service account for API service. We can only associated one service account with each deployment so this one now has both redis and postgres permissions.\nAdded template for creating Secret Provider Class (needed for mounting AWS Secret Manager objects)\nModified deployment template to mount the secret dir if a secret provider has been created\nUpdated all values.yaml files to reflect our latest changes\nAdded environment variables for Redis Username, Password and Endpoint\nMinor updates to README and Make file\nVerified the helm chart locally. The new service account is also ready."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/370#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/370#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/370#pullrequestreview-*********", "body": "Looks good to me!\nMinor comments."}
{"title": "Revert \"Use stores to get TLCs and reviews for a pull request (#3694)\"", "number": 3700, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3700", "body": "This reverts commit 83865fdb5caffac4be652ea6aa3f4e47a08f5212."}
{"title": "add deadletter queue for search indexig", "number": 3701, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3701"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3701#pullrequestreview-**********", "body": ""}
{"title": "Make concurrency more coarse. Concurrency is now at an environment level deployment.", "number": 3702, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3702", "body": "We've been having problems with granular level concurrency.\nTo that end, we are now making sure that concurrency for services is at the environment level.\nOnly one lock, rather than a lock per service(which is problematic as we can weird interleaved deployments).\nGithub only allows concurrency at workflow or job level."}
{"title": "Revert \"Revert \"Use stores to get TLCs and reviews for a pull request", "number": 3703, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3703", "body": " (#3694)\" (#3700)\"\nThis reverts commit 85fedf315be29052a4f49d1473c1cc6258722c03."}
{"title": "Add fancy edge decorations to topic pill list", "number": 3704, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3704", "body": "Add chevron buttons on each edge, if it's possible to scroll in that direction\nFade out content on each edge, if it's possible to scroll in that direction\n\n"}
{"title": "Pull queries out to shrink transaction size", "number": 3705, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3705", "body": "Might be behind some of the timeouts "}
{"title": "Create topic mappings", "number": 3706, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3706", "body": "This will allow us to map topics to pull requests and threads."}
{"title": "Show a single repo in the topic recommendation UI", "number": 3707, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3707", "body": "Fixes UNB-730\nJust show a single repo in this UI, and prioritize root repos."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3707#pullrequestreview-1186668481", "body": "Test comment"}
{"title": "Add topics UI/routes to dashboard", "number": 3708, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708", "body": "build out base Topic UI:\n\nbuild out historical PR route and view:\n"}
{"comment": {"body": "> Nice!! \ud83c\udf89\r\n\r\nI completely concur.\r\nSoneone's winning brownie points here....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#issuecomment-1320662012"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186637686", "body": ""}
{"comment": {"body": "Should this be in its own file?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026785178"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186810262", "body": ""}
{"comment": {"body": "Just so I understand -- the point of having the block template as a property is so that we can use the platform-specific syntax highlighter?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026897511"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186818725", "body": ""}
{"comment": {"body": "nit: the naming of this is a bit confusing -- it doesn't necessarily specify that the title is long, it is effectively specifying that we should allow dual-line title display.  Maybe rename this and the properties to `dualLineTitleDisplay` or `displayLongTitles` or something?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026902603"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186906483", "body": ""}
{"comment": {"body": "Correct. There's no generic shared `CodeBlock` component because the syntax highlighting logic is different enough in each client that I don't think one would make sense. So we need to be able to pass in the CodeBlock component for each client ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026956150"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186909854", "body": ""}
{"comment": {"body": "I'm wondering if this should be a different variant for CodeBlock so the dependency here is explicit -- with this approach (where the parent is overriding styling on the child) there is some risk that any changes made to CodeBlock (renaming styles, etc) would break this.  Not a huge deal for this PR but we generally try to avoid overriding inter-component styles like this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026957637"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186911691", "body": ""}
{"comment": {"body": "Hmm I guess we're doing that a lot in this file, with other components...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026958212"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186917980", "body": ""}
{"comment": {"body": "Went back and forth on this. The text in this component as it stands right now doesn't really make sense outside of the context of the PullRequestState (i.e. what the `PullRequestTag` represents) \r\n\r\nIf anything it's probably better to write a wrapper component that includes both the tag and the text", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026961930"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186920556", "body": ""}
{"comment": {"body": "There's a bunch of stuff here that's duplicated in the VSCode implementation -- is there any value in trying to consolidate these two implementations, or are they different enough that it isn't reasonable to do so?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026963758"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186929178", "body": ""}
{"comment": {"body": "import from @shared-stores or whatever ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026968552"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186929671", "body": ""}
{"comment": {"body": "nit, this can just be `const currentTeamMember = person?.memberships[0];` I think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026968884"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186930947", "body": ""}
{"comment": {"body": "You can use `TopicStreams.get(teamId)` -- this will give you the set of topics for the team, without having to re-fetch every time this component is mounted", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026969480"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186934532", "body": "Nice!! "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186945953", "body": ""}
{"comment": {"body": "would this fail if `person.memberships.length === 0` ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026979254"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186958948", "body": ""}
{"comment": {"body": "Nope -- `someArray[invalidIndex]` returns `undefined`, it doesn't throw an exception.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026985963"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1186960028", "body": ""}
{"comment": {"body": "I've definitely run into a bunch of `Cannot read property '0' of undefined` errors in my lifetime (maybe it compiles in swift??) But I think `array?.[index]` should work ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1026986797"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1391169146", "body": ""}
{"comment": {"body": "testing\n\n\n\ntest @benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1170735133"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#pullrequestreview-1392848755", "body": ""}
{"comment": {"body": "test @benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3708#discussion_r1171832971"}}
{"title": "Recover when search results references a non-existent thread", "number": 3709, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3709"}
{"title": "Refactor mocked models to shared directory", "number": 371, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/371", "body": "Add shared/mocks/ folder for mocked models for tests\nAdd @shared-mocks alias\n\n\nRefactor areas where mock data is being used for more legibility/reusability\nThis way when the models change, there should be only a few places that need updating instead of every test file where we statically define the mocked models\n\n\nNOTE: vscode node.js can't recognize btoa so I changed to the recommended Buffer.from(string).toString('base64')"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/371#pullrequestreview-886231503", "body": ""}
{"title": "Fade camera on mouse hover", "number": 3710, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3710"}
{"title": "Sourcemark agent clones repo then runs recalculation then dies", "number": 3711, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3711"}
{"title": "getRelatedTopics returns topics related to the file", "number": 3712, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3712"}
{"title": "Fix paths in SM agent", "number": 3713, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3713"}
{"title": "update slack jsons.", "number": 3714, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3714"}
{"title": "Add source agent ecr", "number": 3715, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3715"}
{"title": "Try again", "number": 3716, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3716"}
{"title": "StandardizeDockerPublishing", "number": 3717, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3717", "body": "Standardize docker publish\ntry again"}
{"title": "Relax accuracy of point resolution for FileMarks", "number": 3718, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3718", "body": "When a user creates a mark on a commit or change that has not yet been pushed to the remote,\nthen the SM engine cannot track the point through revision history for other users.\nThe proper fix for this is to allow creating insight bubbles from a locally edited file,\nby running the SM engine \"in reverse\" from the current local commit to the merge-base for the\ntracked branch (or the default remote HEAD).\n\nHowever, in the meantime, we can relax the lookup for FileMarks (since it does not need to\nexactly match lines). This fixes the problem for FileMarks for most practical cases."}
{"comment": {"body": "@pwerry @dennispi fixes the problem you guys found for this video:\r\nhttps://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/f511a9db-f29c-4fef-8201-3febc37efb27", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3718#issuecomment-1320508026"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3718#pullrequestreview-1186805405", "body": ""}
{"title": "Use AppKit text for 'All Insights read' text", "number": 3719, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3719"}
{"title": "[WIP] [DO NOT MERGE] Generate PR ingestion report", "number": 372, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/372", "body": "Enables running PR ingestion from the command line for the purposes of generating a report.\nDEFINITELY DO NOT MERGE"}
{"comment": {"body": "admin console would be good for this sort of thing in future. meh", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/372#issuecomment-1042595510"}}
{"comment": {"body": "It probably wouldn't be too much work to adapt this for Kotlin HTML so we can integrate into admin web. What you think @davidkwlam?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/372#issuecomment-1055598503"}}
{"title": "Index transcriptions", "number": 3720, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720", "body": "Next PR: fire a index event when transcription is done and saved to the database"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#pullrequestreview-1186923530", "body": ""}
