{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1062753998", "body": ""}
{"comment": {"body": "Didn't style the colour properly but it should be solid based on designs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#discussion_r938375552"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1062754541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1063711003", "body": ""}
{"comment": {"body": "hello", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#discussion_r939059060"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1063925163", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1065554482", "body": ""}
{"comment": {"body": "@kaych Hi\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/8c0a09be-ee2b-4a37-8b42-7448a69ec82b?message=ba9e182d-086f-449d-b136-a2e4bc8ad97c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#discussion_r940499791"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1124278170", "body": ""}
{"comment": {"body": "@pwerry\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/8c0a09be-ee2b-4a37-8b42-7448a69ec82b?message=eb53eafb-ebea-4be0-99a5-f75271ec427c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#discussion_r982805871"}}
{"title": "Fix bug where thread participants were not being isolated to a thread", "number": 2476, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2476"}
{"title": "Adds additional response logging", "number": 2477, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2477"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2477#pullrequestreview-1059458305", "body": ""}
{"comment": {"body": "In general, I could see this useful for unexpected API errors.\r\nCould refactor this out to the API layer later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2477#discussion_r936048828"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2477#pullrequestreview-1059458328", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2477#pullrequestreview-1059465846", "body": ""}
{"comment": {"body": "I will do that as soon as we hit another situation that calls for it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2477#discussion_r936054631"}}
{"title": "Read-only SourceMark resolution view (part 1)", "number": 2478, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478", "body": "Implement much of the read-only sourcemark resolution view.  This PR implements:\n\nA read-only view with the source code from the original commit, syntax-hilighted using Shiki to match the VSCode theme.  A banner indicates the reason for the read-only view.  This is shown for most of the failure conditions.\nA separate view, offering to take the user to GitHub.  This is used for cases where we we don't have the commit at all, or where there has been a fault.\n\nThis PR does not implement:\n* Git resolution actions (fetch/pull/checkout) or buttons (will be in a followup PR)\n* Many small bits that will make the read-only source code view nicer -- we will need to polish this somewhat to make it closer to VSCode, for instance, we should probably word-wrap if the user has enabled this.\n\n"}
{"comment": {"body": "We may want to render source points *on* the read only code view", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#issuecomment-1203250313"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#pullrequestreview-1059456248", "body": ""}
{"comment": {"body": "These two styles (this one, and the `code_block__shiki_container` below) are used in both the snippet and full-screen renderer.  I'm not sure if/how these should be extracted somewhere common...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#discussion_r936047292"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#pullrequestreview-1059457958", "body": ""}
{"comment": {"body": "This diff is a mess, but all I did was make a separate interface for each of the resolution types (they are joined into one type below).  This allows us to separate each of these types out into ones that we expect to have source code available for rendering, and ones we do not.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#discussion_r936048584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#pullrequestreview-1059458473", "body": ""}
{"comment": {"body": "This git stuff is preliminary -- not used or implemented yet", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#discussion_r936048947"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#pullrequestreview-1059459532", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#pullrequestreview-1059467684", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#pullrequestreview-1059467983", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2478#pullrequestreview-1059506891", "body": ""}
{"title": "Add API for recommended team members", "number": 2479, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2479", "body": "Setup API spec for recommended team members to be used in "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2479#pullrequestreview-1059554685", "body": ""}
{"title": "Fix frontend documentation", "number": 248, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/248", "body": "Add documentation\nupdate"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/248#pullrequestreview-872422701", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/248#pullrequestreview-872439268", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/248#pullrequestreview-872479985", "body": ""}
{"title": "Enable slow query analysis across tests", "number": 2480, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2480", "body": "Before, slow query analysis was toggled false by default.\nWe've now enabled it as true for all tests, and you can disable it if you so choose.\nAlso add a few indices that test exposed as necessary.\nDelete cascade tests are all disabled because 95% is just custom test DAO queries that are inefficient."}
{"title": "Clients log numbers as numeric types", "number": 2481, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2481"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2481#pullrequestreview-1059522686", "body": ""}
{"comment": {"body": "handsome", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2481#discussion_r936099197"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2481#pullrequestreview-1059522740", "body": ""}
{"title": "Add clock drift test for refresh token", "number": 2482, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2482"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2482#pullrequestreview-1059546444", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2482#pullrequestreview-1059552332", "body": ""}
{"title": "Exclude open PRs from threads mine view", "number": 2483, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2483", "body": "Exclude open PRs from threads mine view\nAdd failing test to prevent regression.\nBreaking change was #2468.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2483#pullrequestreview-1059552065", "body": ""}
{"title": "Admin: Fix wrapping in Sourcepoints table", "number": 2484, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2484"}
{"title": "Sanitize UUIDs from Apple", "number": 2485, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2485", "body": "This was getting annoying in Honeycomb, where it's not possible to aggregate\nbased on UUIDs because there are two UUIDs (one uppercase UUID for Apple and\none for every other clients)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2485#pullrequestreview-**********", "body": ""}
{"title": "New log field to force numeric datatype in logzio", "number": 2486, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2486"}
{"title": "Fix logzio mapping conflict", "number": 2487, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2487", "body": "json\n{\n  \"_index\": \"logzioCustomerIndex220802_v8\",\n  \"_type\": \"doc\",\n  \"_id\": \"pB5eYoIB_p8kqhRxY3KM.account-411850\",\n  \"_version\": 1,\n  \"_score\": null,\n  \"_source\": {\n    \"@timestamp\": \"2022-08-03T06:22:12.762+0000\",\n    \"index-failed-reason\": \"{\\\"type\\\":\\\"mapper_parsing_exception\\\",\\\"reason\\\":\\\"object mapping for [platform] tried to parse field [platform] as object, but found a concrete value\\\"}\",\n    \"type\": \"logzio-index-failure\",\n    \"message\": \"{\\\"platform.version\\\":\\\"98d887d18c56a441bb8722e3b3528db53e4cf5ac\\\",\\\"level\\\":\\\"DEBUG\\\",\\\"hostInfo\\\":{\\\"user\\\":\\\"root\\\",\\\"hostname\\\":\\\"adminwebservice-57fc56dbd5-45ncw\\\"},\\\"productNumber\\\":\\\"297\\\",\\\"message\\\":\\\"Failed to insert new version due to uniqueness constraints\\\",\\\"type\\\":\\\"java\\\",\\\"platform\\\":\\\"macos\\\",\\\"tags\\\":[\\\"_logz_http_bulk_json_8070\\\"],\\\"environment\\\":\\\"prod\\\",\\\"productVersion\\\":\\\"1.0.297\\\",\\\"@timestamp\\\":\\\"2022-08-03T06:22:09.291+0000\\\",\\\"service\\\":\\\"adminwebservice\\\",\\\"thread_name\\\":\\\"DefaultDispatcher-worker-2\\\",\\\"productAgent\\\":\\\"Hub\\\",\\\"logger_name\\\":\\\"com.nextchaptersoftware.version.VersionService\\\"}\",\n    \"tags\": [\n      \"_logzio_reindex\"\n    ]\n  },\n  \"fields\": {\n    \"@timestamp\": [\n      \"2022-08-03T06:22:12.762Z\"\n    ]\n  },\n  \"highlight\": {\n    \"index-failed-reason\": [\n      \"@kibana-highlighted-field@{\\\"type\\\":\\\"mapper_parsing_exception\\\",\\\"reason\\\":\\\"object mapping for [platform] tried to parse field [platform] as object, but found a concrete value\\\"}@/kibana-highlighted-field@\"\n    ]\n  },\n  \"sort\": [\n    1659507732762\n  ]\n}"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2487#pullrequestreview-1059761170", "body": ""}
{"title": "Remove getTeamMembersDeprecated API", "number": 2488, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2488", "body": "Not used:\n"}
{"title": "Fix local stack", "number": 2489, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2489", "body": "It was throwing a method not found exception. Not sure how this compiled..."}
{"comment": {"body": "You sure this is necessary?\nCi and my local stack was compiling fine?\n\nDid you do a clean build?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2489#issuecomment-1203942365"}}
{"comment": {"body": "@rasharab ah works now after `make clean` and `make full-build-local-stack`. Closing...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2489#issuecomment-1204225235"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2489#pullrequestreview-1059828201", "body": ""}
{"title": "Reconfigure web directory into semantic structure", "number": 249, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/249", "body": "Chat/Home felt like features so I left them capitalized but can change if needed\nleft a top level app/ directory but not super sure how necessary this is\nadded aliases for the new top level directories but can also remove these if needed\nwasn't sure whether ThreadView and MessageView should live under Chat/ or components/"}
{"comment": {"body": "My votes on these FWIW:\r\n\r\n> * Chat/Home felt like features so I left them capitalized but can change if needed\r\n\r\nI understand why you did this, but I think it's probably best to just be consistent and have it all lowercased.  Jeff gets the final vote I guess?\r\n\r\n> * left a top level `app/` directory but not super sure how necessary this is\r\n> * added aliases for the new top level directories but can also remove these if needed\r\n\r\nMakes sense to me\r\n\r\n> * wasn't sure whether ThreadView and MessageView should live under Chat/ or components/\r\n\r\nI think they should probably be under /chat", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/249#issuecomment-1029480522"}}
{"comment": {"body": "Updated \r\n![image](https://user-images.githubusercontent.com/13431372/152446318-5950fd19-2aad-4c21-915c-15d9f5416383.png)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/249#issuecomment-1029497184"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/249#pullrequestreview-872511590", "body": "Jeff gets final say but looks good to me"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/249#pullrequestreview-872515730", "body": "Thanks for doing this. Looks good to me.\nOnly suggestion is to lowercase as Matt suggested."}
{"title": "More duck types", "number": 2490, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2490"}
{"comment": {"body": "\ud83e\udd86", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2490#issuecomment-1204138994"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2490#pullrequestreview-1060483510", "body": ""}
{"title": "Update recommeneded team member", "number": 2491, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2491", "body": "Updated recommended team member spec to include reason.\nCan be used to communicate with user why a team member was recommended. (e.g. because you review Johns PRs)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2491#pullrequestreview-1060731919", "body": ""}
{"title": "Kill the apiservice if keys weren't loaded correctly", "number": 2492, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2492", "body": "Eliminate one possible cause of spurious Hub logouts. Theory: an apiservice instance comes up with corrupted secrets and services some refresh requests before hitting a non-recoverable error and shuts down before logs can be flushed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2492#pullrequestreview-1060869205", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2492#pullrequestreview-1060884689", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2492#pullrequestreview-1061024412", "body": ""}
{"title": "Get pull request comments from GitHub API", "number": 2493, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2493"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2493#pullrequestreview-1061110856", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2493#pullrequestreview-1062174998", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2493#pullrequestreview-1062190016", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2493#pullrequestreview-1062208266", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2493#pullrequestreview-1062215543", "body": ""}
{"title": "Delete pinpoint stack", "number": 2494, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2494", "body": "Already deleted manually via cdk via cdk destroy"}
{"title": "Fix open current file explorer thread view column", "number": 2495, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2495", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2495#pullrequestreview-1062383096", "body": ""}
{"title": "Log ktor authentication class errors", "number": 2496, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2496", "body": "Why the hell is this trace severity? Anyway this change exposes the following message:\nkt\nlogger.trace(\"Responding unauthorized because of error ${error.message}\")"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2496#pullrequestreview-1060877047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2496#pullrequestreview-1060883176", "body": "Bonkers"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2496#pullrequestreview-1060890962", "body": ""}
{"comment": {"body": "From here:\r\n\r\nhttps://github.com/ktorio/ktor/blob/26028bc9e6d8bb1f2448cd4414fd371135ad5e86/ktor-server/ktor-server-plugins/ktor-server-auth/jvmAndNix/src/io/ktor/server/auth/AuthenticationInterceptors.kt#L58", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2496#discussion_r937056646"}}
{"title": "Introduce concept of trusted source point", "number": 2497, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2497", "body": "new API model field is optional for now\nnew DB model field is optional for now\nincludes migration to backfill DB model\n\nRelated to "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2497#pullrequestreview-1060942013", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2497#pullrequestreview-1060947276", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2497#pullrequestreview-1060949917", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2497#pullrequestreview-1060958708", "body": ""}
{"title": "Add actions to read-only resolution view", "number": 2498, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498", "body": "Add git fetch/pull/commit, intercom support, and view in GitHub actions to the read-only SourceMark resolution view.\nAfter this, one more PR to do, that will add:\n* Some kind of UI indication while the git operation is taking place (a spinner in the button?) -- this will be interesting, and depending on VSCode's API I'm not certain it will be possible.\n* Add a popup to enable auto-fetch\n\n\n\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060953958", "body": ""}
{"comment": {"body": "nit: I think this should say `Checkout` ? I think it's `git checkout` so I think it's one word(??)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#discussion_r937097067"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060954903", "body": ""}
{"comment": {"body": "Yes I think that's true -- I was following the designs without thinking (cc @benedict-jw )", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#discussion_r937097761"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060955472", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060957158", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060957888", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060957962", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060959986", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060966030", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060975277", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1060996289", "body": ""}
{"comment": {"body": "I remember having this discussion in Skywagon. I think it should be \"Check Out\" since it's acting as a verb here. Checkout is used as an adjective or noun. ie \"Proceed to checkout\".\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c554078f-2a92-4309-ac45-f1e30a91874c?message=76e3e35f-7240-46b6-bc69-b3b1fa7e042f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#discussion_r937128243"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1061016690", "body": ""}
{"comment": {"body": "OK, Ben's the Boss, reverted", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#discussion_r937141489"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2498#pullrequestreview-1061017033", "body": ""}
{"title": "Make SourcePointModel isTrusted field non-nullable", "number": 2499, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2499", "body": "Do not merge. Merge only when the migration has successfully run."}
{"title": "Remove unncessary !!", "number": 250, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/250"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/250#pullrequestreview-872667074", "body": ""}
{"title": "fix lint", "number": 2500, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2500"}
{"title": "Ensure we recommend emails in instances where we can for invite flow", "number": 2501, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501", "body": "We need to be able to recommend valid emails from git (when we can).\nTo that end:\n1. We add mention event handlers to DiscussionThreadCommand so we can populate gitContributor information when a team member is mentioned.\n2. We add recommendedContributor to GitContributor such that we can use it as a fallback if email field is not populated.\nFOR NEW DISCUSSION:\nKay has a no-reply email on git, so she's ignored.\n\nFOR EXISTING DISCUSSION:"}
{"comment": {"body": "Ah I guess we can only get suggested emails via git in vscode and not for the other clients? cc: @benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#issuecomment-1204526409"}}
{"comment": {"body": "> Ah I guess we can only get suggested emails via git in vscode and not for the other clients? cc: @benedict-jw\r\n\r\nCorrect.\r\n\r\nFrankly, better than nothing. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#issuecomment-1204529927"}}
{"comment": {"body": "> Ah I guess we can only get suggested emails via git in vscode and not for the other clients? cc: @benedict-jw\r\n\r\nYes \u2014\u00a0seems fine to me. A nice bonus for those using VSCode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#issuecomment-1204531614"}}
{"comment": {"body": "UI added to input rows:\r\n<img width=\"529\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/182726856-1f1dbad0-fe9e-4d85-8947-796c8de4e744.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#issuecomment-1204565030"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061043637", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061045446", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061046663", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061046924", "body": ""}
{"comment": {"body": "why do we need recommended contributors here? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r937163868"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061047334", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061048536", "body": ""}
{"comment": {"body": "I think the naming is getting a bit confusing here. Are these suggested contributors given the team member id? We already have 'recommended' contributors when creating discussions so I think differentiating the two via naming will be helpful", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r937165012"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061051322", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061058170", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061058621", "body": ""}
{"comment": {"body": "Sure thing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r937173010"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061062374", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061065355", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061079026", "body": ""}
{"comment": {"body": "is `emptyToNull` here necessary? would `undefined` work in this case (i.e. if it's undefined then it'll try to get the gitEmail and then finally '')? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r937189043"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061080748", "body": ""}
{"comment": {"body": "No guarantee it's not empty and there were instances it was empty (hence the introduction of the utility).\r\nI would say I'd relegate that change to another pr. There are a few places it's being assigned an empty value.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r937190438"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1061098008", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1062707578", "body": ""}
{"comment": {"body": "You can use `||` instead of `??` in that case:\r\n* `||` will coalesce if the item on the left is falsy -- and empty strings are falsy.\r\n* `??` only coalesces for undefined and null\r\n\r\nThe large majority of the TS code doesn't really use null, we've generally used undefined almost everywhere.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r938338777"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1062708053", "body": ""}
{"comment": {"body": "This should be imported via `import {blah} from '@git'`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r938339197"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1062709008", "body": ""}
{"comment": {"body": "Same thing here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r938339994"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1063515006", "body": ""}
{"comment": {"body": "@mahdi-torabi \n\n@benedict-jw\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/96702fa9-06a2-4d1c-94d6-c937d3518aa7?message=7aa7d80b-0908-44f9-ab24-0768f2c07b05).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r938920417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#pullrequestreview-1063647967", "body": ""}
{"comment": {"body": "@rasharab\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c01ab749-40b5-4611-a13f-563db8b3f56a?message=95f47ac8-627c-4115-9c71-39a76d876887).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2501#discussion_r939012797"}}
{"title": "Jeff/unb 219 add recommended team members to", "number": 2502, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502", "body": "Add client support for recommended team members\nRecommended team members will always appear at the bottom of list.\nDo not merge until backend done."}
{"comment": {"body": "@jeffrey-ng Did you add logic to filter out the current person from the list too? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#issuecomment-1206846248"}}
{"comment": {"body": "> @jeffrey-ng Did you add logic to filter out the current person from the list too?\r\n\r\nI did not... I wouldn't want that to be a client side thing though? The API service shouldn't be returning the user who made the request. @richiebres ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#issuecomment-1206858528"}}
{"comment": {"body": "Backend has been done for a while. What's remaining?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#issuecomment-1211567734"}}
{"comment": {"body": "> > @jeffrey-ng Did you add logic to filter out the current person from the list too?\r\n> \r\n> I did not... I wouldn't want that to be a client side thing though? The API service shouldn't be returning the user who made the request. @richiebres\r\n\r\nThe current person (the \"author\") is not a reviewer, so they will not be returned in the API. No need to handle this in the client.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#issuecomment-1212590874"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1062686116", "body": ""}
{"comment": {"body": "Do the tests here actually pass? I found that when I subscribed to a stream in a test like this, I needed to explicitly unsubscribe as well or tests further down the line would start failing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#discussion_r938321348"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1062688016", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1063574471", "body": ""}
{"comment": {"body": "They are passing. Might be because I'm destroying the stream after each test?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#discussion_r938961501"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1063579967", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1063978833", "body": ""}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#discussion_r939250167"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1069968253", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1069981441", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1069982590", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1069988064", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1069988702", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1073335188", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2502#pullrequestreview-1074420416", "body": ""}
{"title": "Modify /Application/Unblocked.app ownership permissions", "number": 2503, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503", "body": "Summary\nIntroducing some changes to set up the app for \"seamless\" updates via a non-sandboxed embedded app. This PR does the following:\n\nModify the installer to set user-level ownership on /Applications/Unblocked.app\nIntroduce a non-sandboxed embedded app (currently a stub)\n\nOwnership permissions change has been tested with the following installer scenarios:\n- Fresh install\n- Upgrade from previous install where ownership is root:wheel\n- Upgrade from previous install where ownership is already user:staff\nFailed attempts to change ownership are passive. The plan for \"seamless install\" is to fall back to the installer if the ownership preconditions are not met."}
{"comment": {"body": "`Modify the installer to set user-level ownership on /Applications/Unblocked.app`\r\n\r\nDoes this mean that if I install it on a machine with multiple accounts, and switch accounts, the hub app won't work?  I'm not sure how stuff in `/Applications` works?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#issuecomment-**********"}}
{"comment": {"body": "> `Modify the installer to set user-level ownership on /Applications/Unblocked.app`\r\n> \r\n> Does this mean that if I install it on a machine with multiple accounts, and switch accounts, the hub app won't work? I'm not sure how stuff in `/Applications` works?\r\n\r\nIf the user is in the `staff` group (which is highly likely), then we should be good to go", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is important - it converts the ownership from `root:wheel` to `user:staff`, which is necessary for the non-sandboxed helper to write new bits without requiring root auth", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#discussion_r937154340"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Placeholder for now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#discussion_r937154619"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#pullrequestreview-1061035855", "body": ""}
{"comment": {"body": "Break out of jail", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#discussion_r937155469"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#pullrequestreview-1062457964", "body": ""}
{"comment": {"body": "`launchctl asuser` is designed to put the execution environment into as close a representation of the user environment as possible. But I've often observed that it fails and I'm not super clear on the reasons. We fall back to good ol' `sudo -u`, which seems to do the job", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#discussion_r938154731"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#pullrequestreview-1062467727", "body": ""}
{"comment": {"body": "Is this intentional?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#discussion_r938161416"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#pullrequestreview-1062468098", "body": ""}
{"comment": {"body": "Ah ignore me, this is the manual one", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#discussion_r938161667"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#pullrequestreview-1062471366", "body": ""}
{"comment": {"body": "https://developer.apple.com/library/archive/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html\r\n\r\n```\r\nauth:\r\n\r\nDefines the authorization level needed to install this package. Values: none or root.\r\nDeprecated. The installation domain determines the necessary authorization level.\r\n```\r\n\r\nStill works even though its deprecated?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#discussion_r938164017"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#pullrequestreview-1062473607", "body": ""}
{"comment": {"body": "Yeah... the documentation is highly ignorable. `auth` was used in tandem with `rootVolumeOnly` (also deprecated), but using `domains` per the documentation results in an ultra janky UX that everyone practicing the dark arts seems to avoid\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/711e4906-cbb5-4d89-a3f8-40a71472b41a?message=e9dcf95f-6b79-4380-b64a-d3b0f3b35453).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#discussion_r938165619"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2503#pullrequestreview-1062478727", "body": ""}
{"title": "\"Seamless\" installs", "number": 2504, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504", "body": "Seamless installs are facilitated as follows:\n- Main app downloads and stores new version metadata and pkg (as it does now)\n- Main app launches unsandboxed helper app via launchd (NSWorkspace.open())\n- Helper app unpacks installer package using undocumented () pkgutil command\n- Helper app drops the bits to /Application/Unblocked.app and shells out to install vscode extension\n- Helper app restarts Hub and quits\n\nThe Hub will be rejected from the App Store so long as we're embedding an unsandboxed app"}
{"comment": {"body": "I'm going to merge this peter.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#issuecomment-1212433512"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1064213357", "body": ""}
{"comment": {"body": "This has to be done because a sandboxed app can't pass arguments to `OpenConfiguration`. Only other way to do this might be to write it to defaults and read it out, but I don't think that's necessary. The container location is stable.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#discussion_r939430121"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1064214516", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1072957030", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1072957452", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1072960713", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1072967216", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1073334532", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1073336678", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1073337729", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1073338619", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1073339020", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1073339351", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1073340907", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1073343135", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2504#pullrequestreview-1073348316", "body": ""}
{"title": "Last resort defence", "number": 2505, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2505", "body": "Thor hammer..."}
{"comment": {"body": "If we want a short-term patch, I think we can get this in but I'm not comfortable with keeping this...\r\n\r\nCould we add some safeguards to service to return a 500 instead of a 401 in the error cases?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2505#issuecomment-1204528794"}}
{"title": "Add auto-fetch query to read-only SM error view", "number": 2506, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2506", "body": "This is displayed in the 'Not in Local Repo' case -- we offer to git fetch, but also encourage the user to turn on auto-fetch, as this should generally resolve this.\nOne issue @benedict-jw  -- there is always a 'Cancel' button!  Should we remove the 'Don't Fetch Automatically' button?\n"}
{"comment": {"body": "If we can't remove the cancel button, then lets remove \"don't fetch automatically\". \"Cancel\" as a label felt a bit odd because we aren't cancelling anything here, just asking them an optional question. This might be a silly question, but can we relabel \"cancel\"?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2506#issuecomment-1204541328"}}
{"comment": {"body": "> If we can't remove the cancel button, then lets remove \"don't fetch automatically\". \"Cancel\" as a label felt a bit odd because we aren't cancelling anything here, just asking them an optional question. This might be a silly question, but can we relabel \"cancel\"?\r\n\r\nAFAICT there is no way to remove or relabel the Cancel button. I'll remove the 'Don't Fetch Automatically' option for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2506#issuecomment-1204543861"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2506#pullrequestreview-1061118084", "body": ""}
{"title": "Auth finalization can get interrupted and cause a logout", "number": 2507, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2507", "body": "Summary\nAuth in the Hub occurs in 3 phases:\n1. Refresh the token\n2. Call getPerson and getTeams to bring the stores into a consistent state with the new token\n3. Publish the combined data\nIf macOS goes to sleep between steps 1 and 2, it's possible for the calls in step 2 to fail with a 401. This error is propagated to the call site for refresh, and incorrectly interpreted as a 401 from /api/login/refresh, triggering a logout. \nThe fix is to wrap the \"finalization\" logic in a try/catch, so that if any of the logic during finalization fails, auth token refresh is retried again"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2507#pullrequestreview-1061095856", "body": ""}
{"title": "Show thread ranks on thread page", "number": 2508, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2508", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2508#pullrequestreview-1061095148", "body": "nice"}
{"title": "UpgradeDependencies", "number": 2509, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2509", "body": "Update deps\nUpdate a few dependencies"}
{"title": "[DO NOT MERGE] Compare createdAt", "number": 251, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/251", "body": "Just investigating why comparing the createdAt for the same model differs when the model is created locally versus when the same model retrieved from the DB after saving."}
{"comment": {"body": "All done with this for now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/251#issuecomment-1030249481"}}
{"comment": {"body": "Continuing with https://github.com/NextChapterSoftware/unblocked/pull/258", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/251#issuecomment-1030483497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/251#pullrequestreview-873373788", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/251#pullrequestreview-873414613", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/251#pullrequestreview-873432321", "body": ""}
{"comment": {"body": "Fails with:\r\n\r\n```\r\norg.opentest4j.AssertionFailedError: \r\nexpected: 0s\r\n but was: 302ns\r\n```\r\n\r\nSo the CI runner has nano second precision", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/251#discussion_r799709445"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/251#pullrequestreview-873436828", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/251#pullrequestreview-873444298", "body": ""}
{"title": "Reload SM resolution UI after trying git operations", "number": 2510, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2510", "body": "In the SourceMark resolution UI, if the user elects to fetch/pull/checkout, afterwards we will re-resolve the sourcemark location and reload the UI.  If the git operation solved the problem, this should cause the UI to display the source code.\nOne last PR coming up after: need to add spinners or grey out the action buttons while this is happening."}
{"comment": {"body": "I'm merging this so I can make a build, please still review and let me know if you have any suggestions/improvements", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2510#issuecomment-1204707838"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2510#pullrequestreview-1061127814", "body": ""}
{"comment": {"body": "The funny syntax here is to run a series of async operations within a synchronous function.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2510#discussion_r937228616"}}
{"title": "Remove deprecated fields that havent been used in a while", "number": 2511, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2511"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2511#pullrequestreview-1062318005", "body": ""}
{"title": "UNB-490 Fix top level dashboard search", "number": 2512, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2512", "body": ""}
{"comment": {"body": "This makes sense given where the code currently is but it's strange that we need to modify the route for search to work...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2512#issuecomment-1205574039"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2512#pullrequestreview-1062377505", "body": ""}
{"title": "Fix bugs in SM read-only resolution UI", "number": 2513, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513", "body": "Fix some bugs:\n\nRemove the \"no\" auto-fetch UI option, since there's a cancel button always added.  I think I merged the PR before pushing my final change in this.\nWhen resolution git operations (fetch/pull/checkout) fail, show an error UI.  Show special UIs for \"workspace is dirty\" and \"unauthed\" cases, otherwise show git output.  This follows what VSCode does internally.\nDisable the git action buttons while the git action is running"}
{"comment": {"body": "Matt: I thought the idea was to never even attempt to checkout or pull if the checkout or pull cannot be done cleanly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513#issuecomment-1205610823"}}
{"comment": {"body": "> Matt: I thought the idea was to never even attempt to checkout or pull if the checkout or pull cannot be done cleanly.\r\n\r\nThere's no good way for me to tell... what we're doing here now is analogous to how the rest of VSCode's git UI works.\r\n\r\nWe could maybe query git directly to tell beforehand if it will work or not ...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513#issuecomment-1205615232"}}
{"comment": {"body": "I am merging this in so we can make a test build.  Please review and let me know if you have any feedback.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513#issuecomment-1205709546"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513#pullrequestreview-1062395682", "body": ""}
{"comment": {"body": "This copies the VSCode error message UI for these cases.  Unfortunately, if we use their UI directly (by calling the command, instead of directly using the git API), the async command operation does *not* wait until the git operation has completed, so we end up reloading the resolution UI before git has completed, which doesn't work well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513#discussion_r938109321"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513#pullrequestreview-1062396921", "body": ""}
{"comment": {"body": "We need to pass this through so that we can re-resolve the sourcemark after the git operation runs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513#discussion_r938110246"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513#pullrequestreview-1062398347", "body": ""}
{"comment": {"body": "We're using VSCode's git API directly here, instead of using commands.  Using the commands gets us their git UI, but unfortunately the async command operation does *not* wait until the git operation has completed, so we end up reloading the resolution UI before git has completed, which doesn't work well.  The git API works as expected.\r\n\r\nWe can swap this out with our own git API easily enough.  The benefit of using this right now is that the error returns reliably tell us when the user has a dirty workspace.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2513#discussion_r938111264"}}
{"title": "enable alb access logs in dev", "number": 2514, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514", "body": "Add CDK code to create S3 buckets \nAdded config to create a bucket for external alb access logs\nAdded annotation to enable ALB access logs in Dev. If it works fine I'll make another PR for Prod"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#pullrequestreview-1062475617", "body": ""}
{"comment": {"body": "I wonder if we should do this for the siteBucket in static-site-stack.ts as well?\r\nBasically separate its creation from static site stack?\r\n\r\n            const siteBucket = new s3.Bucket(this, `SiteBucket-${siteConfig.name}`, {\r\n                bucketName: siteConfig.s3BucketName,\r\n                publicReadAccess: false,\r\n                blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,\r\n                removalPolicy: RemovalPolicy.RETAIN,\r\n                autoDeleteObjects: false,\r\n            });", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#discussion_r938167083"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#pullrequestreview-1062476272", "body": "Looks good, minor comment."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#pullrequestreview-1062477105", "body": ""}
{"comment": {"body": "Those ones create CloudFront and this isn't a static site so I decided to have it separate\r\n\r\n--\r\n_posted via [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d17f0c48-cfbb-4971-9ac4-5700b04466f4?message=0a2d2369-051f-4df6-bbd3-261e50ce7355)._", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#discussion_r938168145"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#pullrequestreview-1062477455", "body": ""}
{"comment": {"body": "These are used for creating plain old S3 buckets for whatever use we want\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d17f0c48-cfbb-4971-9ac4-5700b04466f4?message=7e92f67e-f360-49f0-a2ec-674ab1e1994a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#discussion_r938168397"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#pullrequestreview-1062480535", "body": ""}
{"comment": {"body": "Yeah, I guess my point was centralizing all bucket creation to one stack. \r\nEither way, just a thought, looks good.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#discussion_r938170620"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#pullrequestreview-1065627496", "body": ""}
{"comment": {"body": "This is a test comment\r\n\r\n--\r\n_via [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d17f0c48-cfbb-4971-9ac4-5700b04466f4?message=0a2d2369-051f-4df6-bbd3-261e50ce7355)._", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2514#discussion_r940551460"}}
{"title": "Sanitize source points in source mark engine", "number": 2515, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2515", "body": "backfills file hashes for points, and marks them as trusted.\n\n\nupstreams original points from client. this needs another change on the apiservice to allow this and upsert instead of insert. I'll do that in separate change here: https://github.com/NextChapterSoftware/unblocked/pull/2516\n\n\nreduces upstream batch size because putSourcepoints currently takes up to 5.2 seconds in Honeycomb\n  https://ui.honeycomb.io/unblocked/environments/production/result/6N8Kzm92gY1?hideMarkers&useStackedGraphs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2515#pullrequestreview-1062605227", "body": ""}
{"title": "Allow original points to be uploaded", "number": 2516, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2516", "body": "Allow original points to be uploaded\n\n\nTODO: Upsert points to avoid duplicates in a follow up change"}
{"title": "rollout access logs to prod", "number": 2517, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2517", "body": "Fixed alb access log s3 permissions ()\nAdded annotation for prod alb logging"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2517#pullrequestreview-1062619943", "body": ""}
{"title": "Change unblocked comment branding", "number": 2518, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2518", "body": "Tests pass but I am not sure what would happen to existing comments with old signature moving forward.\nDo we need to handle those just like we do legacy ones now ?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2518#pullrequestreview-1063947089", "body": "LGTM"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2518#pullrequestreview-1064222619", "body": "Not a fan of this change. Does not significantly improve the message, and introduces odd grammar.\nIf we are trying to make the message short, there are better alternatives. For example, you could put it all on one line like this:\n edited from Unblocked"}
{"comment": {"body": "- Use of \"via\" is weirdly formal; I would use simple English instead \"from\".\r\n- why is \"posted\" lowercase at the start of a sentence? appears grammatically incorrect.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2518#discussion_r939437929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2518#pullrequestreview-1065620770", "body": ""}
{"comment": {"body": "`from` can be mis-understood as `Unblocked` having posted the content instead of user through Unblocked. I kinda agree that it could become confusing.\n\n\n\nI kept posted and edited lower case because it makes them less notable. They kinda blend in.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ef320adb-d0bf-47ed-8176-6eebf81fb285?message=32d48e34-bc3c-46d4-9cdd-f560c815fd6f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2518#discussion_r940546432"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2518#pullrequestreview-1065623658", "body": ""}
{"comment": {"body": "<img width=\"850\" alt=\"Screen Shot 2022-08-08 at 11 39 46 AM\" src=\"https://user-images.githubusercontent.com/94733135/183489873-283bccee-20dd-4a7d-a543-03fa7f00f8a2.png\">\r\n\r\n\r\nFirst comment is using the new format. Second one is using the old format. \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2518#discussion_r940548686"}}
{"title": "whoops", "number": 2519, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2519", "body": "This directory should never have been submitted.."}
{"title": "Use wrapRow in Person API to simplify", "number": 252, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/252"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/252#pullrequestreview-872757216", "body": ""}
{"title": "Use compound cursor paging for getSourceMarks API", "number": 2520, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520"}
{"comment": {"body": "Once this is going in, should we stop deployments to prod and test it end-to-end in dev?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#issuecomment-1205874650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#pullrequestreview-1062693181", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#pullrequestreview-1062693671", "body": ""}
{"comment": {"body": "This means on the final call while paging through the data (when no sourcemarks are returned) we will return a null last-modified, correct?  I'm not certain about this, but I think all the other collections will always return the last-modified for the entire collection in this circumstance?  I guess we're changing how last-modified works for this collection to maybe this is OK?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#discussion_r938327548"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#pullrequestreview-1062694794", "body": ""}
{"comment": {"body": "It'll repeat the ifModifiedSince parameter passed in the request. This logic is at the api-layer, see below.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#discussion_r938328651"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#pullrequestreview-1062695176", "body": ""}
{"comment": {"body": "@matthewjamesadam see here: returns the cursor as lastModified; or, if null, then returns the input If-Modified-Since", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#discussion_r938328937"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#pullrequestreview-1062695456", "body": ""}
{"comment": {"body": "\ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2520#discussion_r938329142"}}
{"title": "POST /modifiedSince with /threads/{threadId} returns a result if thread unread is modified", "number": 2521, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1062668845", "body": "what about ThreadRank?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1062682464", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1062683266", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1062683836", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1062683904", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1062892917", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1063547116", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1063563931", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1063568913", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1063593035", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2521#pullrequestreview-1067025934", "body": ""}
{"title": "Spec changes to support Related Pull Requests", "number": 2522, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522", "body": "First draft to support.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062698972", "body": ""}
{"comment": {"body": "Should this have it's own path?\r\n\r\nOriginally had this under `/teams/{teamId}/pullRequests/file`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#discussion_r938332036"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062699791", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062701547", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062701684", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062867737", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062867822", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062868314", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062868472", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062901780", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1062903718", "body": ""}
{"comment": {"body": "Spitballing: `/teams/{teamId}/pullRequestsForFile`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#discussion_r938488024"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1064364235", "body": "LGTM, just a small suggestion."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1065463183", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2522#pullrequestreview-1065599094", "body": ""}
{"title": "Fix SourceMarksApiDelegateImplTest and add isTrusted flag to admin", "number": 2523, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2523"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2523#pullrequestreview-1062721133", "body": ""}
{"title": "Matt Adam is a lovely dude", "number": 2524, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2524", "body": "(let's see if Matt sees this :)\nHis willingness to provide wonderful input led to this pr."}
{"title": "Defensive copy point to ensure trusted points are uploaded", "number": 2525, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2525", "body": "we were comparing the newly minted trusted point against the cached point to\n  gate upload; problem was the cache was shared so we were just comparing the\n  point to itself, which means we would never upload."}
{"title": "Fix sourcemark pusher channel", "number": 2526, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2526"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2526#pullrequestreview-1062868421", "body": ""}
{"comment": {"body": "`modifiedSince` is now a Cursor or an Instant", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2526#discussion_r938462092"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2526#pullrequestreview-1062868759", "body": ""}
{"comment": {"body": "reuse the same function as apiservice for consistency", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2526#discussion_r938462326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2526#pullrequestreview-1062872318", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2526#pullrequestreview-1062872775", "body": ""}
{"comment": {"body": "Really should be the rule in this class to prevent sadness down the road", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2526#discussion_r938465344"}}
{"title": "API to expose author recommendations based on Social comment network", "number": 2527, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#pullrequestreview-1062931147", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#pullrequestreview-1062943231", "body": ""}
{"comment": {"body": "@jeffrey-ng does this look right? I don't know the exact context the message is shown.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#discussion_r938516576"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#pullrequestreview-1063516581", "body": ""}
{"comment": {"body": "Were thinking something like `because you review David\u2019s PRs`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#discussion_r938921497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#pullrequestreview-1063517570", "body": ""}
{"comment": {"body": "This is going to live on the right hand side, similar to \"Most Contributions\"\r\n<img width=\"679\" alt=\"CleanShot 2022-08-05 at 08 18 21@2x\" src=\"https://user-images.githubusercontent.com/1553313/183108283-17683cd6-c1fe-457e-abb7-95faa62e4f0c.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#discussion_r938922186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#pullrequestreview-1063570406", "body": ""}
{"comment": {"body": "> Were thinking something like\u00a0because you review David\u2019s PRs\n\n\n\nOk, but it's the other way around: \"because David reviews your PRs\".\n\n\n\nHowever, it's not just PR messages that we count, it's any messages, so I would drop any mention of \"PRs\".\n\n\n\nThere is also a recency component, so we could say something like:\n\n- \"recently interacted with\"\n\n- \"recently worked with\"\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/8a727eda-1fdd-4918-8007-b8ffe039e6b2?message=2fc80394-2367-4c30-953f-5129dc649e2d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#discussion_r938958742"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#pullrequestreview-1063680422", "body": ""}
{"comment": {"body": "I think I would prefer the reason being an enum as opposed to a string.  This discussion brings up why -- we might display this in different ways, or want to change the wording in ways that only make sense in different contexts.  Unless we think there will be a *lot* of different scenarios an enum facilitates that best...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#discussion_r939037526"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#pullrequestreview-1067427176", "body": ""}
{"comment": {"body": "(Missed your comment somehow Matt.)\r\n\r\nThe obvious advantage of server-driven content is that we can change across all clients without pushing new bits out. The server could add several more types of recommendations like \"recently edited this file\", \"recently edited this file\", \"often reviews this file\", etc without a single client change.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2527#discussion_r941835398"}}
{"title": "Web has no notion of contriubtors", "number": 2528, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2528"}
{"title": "Allow rerunning just bulk pr ingestion", "number": 2529, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2529"}
{"title": "ThreadPArticipants API", "number": 253, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/253"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/253#pullrequestreview-873277734", "body": ""}
{"title": "Add filter for current team members for mentions", "number": 2530, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2530"}
{"comment": {"body": "There's a part of me that wonders whether the teamContext should only consist of team members that are current members.\r\nNot sure, but this is fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2530#issuecomment-1206730607"}}
{"comment": {"body": "> There's a part of me that wonders whether the teamContext should only consist of team members that are current members.\r\n> Not sure, but this is fine.\r\n\r\nIt's hard to tell because different UIs will need to show different kinds of data.  The message view needs to be able to show non-current members, when you're viewing older messages.\r\n\r\nWe could probably publish multiple streams with different kinds of filtering to make this more clear...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2530#issuecomment-1206741004"}}
{"comment": {"body": "> > There's a part of me that wonders whether the teamContext should only consist of team members that are current members.\r\n> > Not sure, but this is fine.\r\n> \r\n> It's hard to tell because different UIs will need to show different kinds of data. The message view needs to be able to show non-current members, when you're viewing older messages.\r\n> \r\n> We could probably publish multiple streams with different kinds of filtering to make this more clear...\r\n\r\nChatted with Rashin. I think what would make this less brittle is to have two client-side accessors that return filtered collections from the base team member store. And we should make those accessors very wordy/explicit:\r\n1. `getCurrentTeamMembersOnly`\r\n2. `getCurrentAndPastTeamMembers`\r\n\r\nThen remove all the inline filtering in different parts of the code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2530#issuecomment-1206795233"}}
{"comment": {"body": "> > > There's a part of me that wonders whether the teamContext should only consist of team members that are current members.\r\n> > > Not sure, but this is fine.\r\n> > \r\n> > \r\n> > It's hard to tell because different UIs will need to show different kinds of data. The message view needs to be able to show non-current members, when you're viewing older messages.\r\n> > We could probably publish multiple streams with different kinds of filtering to make this more clear...\r\n> \r\n> Chatted with Rashin. I think what would make this less brittle is to have two client-side accessors that return filtered collections from the base team member store. And we should make those accessors very wordy/explicit:\r\n> \r\n> 1. `getCurrentTeamMembersOnly`\r\n> 2. `getCurrentAndPastTeamMembers`\r\n> \r\n> Then remove all the inline filtering in different parts of the code.\r\n\r\nYes, that's what I was saying `We could probably publish multiple streams with different kinds of filtering to make this more clear...`\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2530#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2530#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2530#pullrequestreview-**********", "body": ""}
{"title": "Make sure we send a thread invite if user is mentioned that does not have account", "number": 2531, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2531", "body": "We also are standardizing triggers."}
{"title": "Minor logging changes", "number": 2532, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2532"}
{"comment": {"body": "rasDSA", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2532#issuecomment-**********"}}
{"comment": {"body": "ASDFSDA", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2532#issuecomment-**********"}}
{"title": "[RFC] PullRequestMessage model spec", "number": 2533, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533", "body": "Needs to support PR description and message body contents\n\n\n\nNeeds to support embedded threads:\n\n\n\nNeeds to support code-level comments (i.e. regular unblocked discussions):\n\n\n\nWill fill in the CRUD operations once the models are more stable."}
{"comment": {"body": "Spoke with Matt. I'm going to be making some changes on Monday to Howe message and its metadata are represented in APIs. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#issuecomment-1207003466"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1063940795", "body": "I have lot's of questions  maybe better to chat in person"}
{"comment": {"body": "Why does this message not have mentions, author, editedAt, createdAt, etc", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r939225881"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1064092733", "body": ""}
{"comment": {"body": "Can `PullRequestGroup` just contain a `Message` instead of duplicating most of Message's properties?  Is the only problem that Message has a threadId ?\r\n\r\nIt looks like none of the clients actually use `Message.threadId`, so if that's the only difference, we can probably remove it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r939339429"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1064210849", "body": ""}
{"comment": {"body": "This one also has an optional messageContent I think.\r\n\r\nIdeally we could extract the shared properties in a shared model and inherit them in both this one and Message but \ud83d\ude14", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r939428083"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1064222418", "body": ""}
{"comment": {"body": "^^\r\nDoing that on Monday.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r939437780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1067030454", "body": ""}
{"comment": {"body": "Remind me again: will these participants be used for the left side \"Relevant Pull Requests pane\" and not the right side discussion view? Does this overlap with @jeffrey-ng spec?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r941552312"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1067033632", "body": ""}
{"comment": {"body": "Right side discussion view in an expanded (wide-screen) view:\r\n\r\n<img width=\"475\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/183706745-ee091891-70a7-4c02-be67-a884d8ab34db.png\">\r\n\r\n--\r\n\r\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e2d1aa64-6442-4de4-a952-e4b899357e8e?message=1fcf0d16-b906-4db4-9326-23dc49dab2ab).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r941554659"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1067055174", "body": ""}
{"comment": {"body": "@rasharab @matthewjamesadam I don't think this is necessarily blocking is it? We could always refactor these shared properties out after?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r941570132"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1067112507", "body": ""}
{"comment": {"body": "Unclear to me whether this needs to be required or not? I guess a pr with 'no description' can still be edited to add a description? Which may imply that this is a required field but that the message content is just empty in that case? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r941611204"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1067149436", "body": ""}
{"comment": {"body": "most PRs have descriptions so for that reason, I think it's fine to keep required with an empty body \"\" in some cases", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r941637277"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1067155629", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1067233720", "body": ""}
{"comment": {"body": "FYI this will be the same as `PullRequest.createdAt`, is that ok?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r941696804"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1067235796", "body": ""}
{"comment": {"body": "I think that makes sense. I think I'm still leaning towards the field existing in both PullRequest and PullRequestDescription for consistency (i.e. description has an editedAt so it stands to reason that it would have a createdAt)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r941698223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1068959746", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1068970580", "body": ""}
{"comment": {"body": "We have no need for participants in the PR sidebar.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r942933240"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1068971575", "body": ""}
{"comment": {"body": "Is this a TLC?\r\n\r\nCould we add a description for these models.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r942933941"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1068973125", "body": ""}
{"comment": {"body": "...Will we need to support mentions? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r942935061"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#pullrequestreview-1068980680", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/2533/files#diff-ba77b4591a44b56bb559a713e693c37a41de141b33a66893a0cd87b81ed75802R2512 ? Or are you asking whether it's necessary to include them or not?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2533#discussion_r942940968"}}
{"title": "Gate auth expiry before requests", "number": 2534, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534"}
{"comment": {"body": "top-level comment ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#issuecomment-1206895848"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#pullrequestreview-1063980501", "body": ""}
{"comment": {"body": "file comment, as review", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#discussion_r939251438"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#pullrequestreview-1063980973", "body": ""}
{"comment": {"body": "file comment, as single comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#discussion_r939251792"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#pullrequestreview-1063982062", "body": ""}
{"comment": {"body": "File comment as approval", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#discussion_r939252611"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#pullrequestreview-1063985570", "body": "Top level comment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#pullrequestreview-1063988939", "body": "Approval message"}
{"comment": {"body": "Approval with file comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#discussion_r939258001"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2534#pullrequestreview-1063999436", "body": "Request changes message"}
{"title": "Toggle bulk ingestion flag", "number": 2535, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2535", "body": "Allows for temporarily pausing bulk ingestion"}
{"title": "Show bulk ingestion API responses in admin cons", "number": 2536, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2536"}
{"title": "Dont sort comments for repo", "number": 2537, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2537", "body": "Probably the reason for 502s"}
{"title": "Add thread count to admin", "number": 2538, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2538"}
{"title": "UNB-524 Fix search navigation", "number": 2539, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2539"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2539#pullrequestreview-1065574447", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2539#pullrequestreview-1065619315", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2539#pullrequestreview-1067053332", "body": ""}
{"title": "[DO NOT MERGE] Make MessageDAO internal and expose Message as a data class", "number": 254, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/254", "body": "Playing around with this to see if this is something we want to do to address . Maybe there's a way we can define the data class without all the boilerplate?"}
{"comment": {"body": "Closing this and will try an implementation in another PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/254#issuecomment-1034282562"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/254#pullrequestreview-873497422", "body": ""}
{"comment": {"body": "It might be useful to make this function name consistent for every DAO.\r\n```suggestion\r\n    fun asDataClass() = Message(\r\n```\r\n\r\nAnd we can make the DAOs implement an interface to enforce:\r\n```kotlin\r\ninterface AsDataClass<T> {\r\n    fun asDataClass(): T   \r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/254#discussion_r799756439"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/254#pullrequestreview-874210663", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/254#pullrequestreview-874213492", "body": ""}
{"comment": {"body": "(please also ignore this comment)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/254#discussion_r800335075"}}
{"title": "Permanently delete team", "number": 2540, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2540"}
{"comment": {"body": "> Looks good. Is this the right moment to introduce \"are you sure\" dialogs?\r\n\r\nyup, next ...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2540#issuecomment-1207124343"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2540#pullrequestreview-1064250162", "body": "Looks good. Is this the right moment to introduce \"are you sure\" dialogs?"}
{"title": "Admin confirmation modal", "number": 2541, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2541", "body": "fixes: \n"}
{"title": "Cleanup unused action", "number": 2542, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2542"}
{"title": "Cleanup admin rendering", "number": 2543, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2543", "body": "fix styling for release description\nfix styling for toggle list"}
{"title": "Add ability to trigger bulk ingestion for all repos in a team", "number": 2544, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2544"}
{"title": "Add description to pull request model", "number": 2545, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2545"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2545#pullrequestreview-1065656120", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2545#pullrequestreview-1065656608", "body": "Minor comment."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2545#pullrequestreview-1065881204", "body": ""}
{"title": "Basic TLC PR Sidebar with local data", "number": 2546, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546", "body": "Setup TLC PR Sidebar with local data.\nCurrently showing current commit (should hopefully be a PR commit?) and corresponding diff stats.\nHidden behind feature flag\n"}
{"comment": {"body": "Git stuff looks good to me now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#issuecomment-1208642471"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065557086", "body": ""}
{"comment": {"body": "File logic is partially copied from TextEditorSourceMarkManager.\r\n\r\n@matthewjamesadam has a upcoming PR that should help DRY the code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940501633"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065557720", "body": ""}
{"comment": {"body": "Logic will most likely change here when we integrate API to fetch PRs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940502115"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065563485", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065826739", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065852550", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065862848", "body": ""}
{"comment": {"body": "I think this entire file will probably be removed -- you will be able to subscribe to the \"focused file\" stream I'm about to push in a PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940713948"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065865748", "body": ""}
{"comment": {"body": "Yeah this will need some work, the way this is set up now you have two streams with an async promise operation in-between -- the end effect is that you will show promises for the wrong file (or no file) at times.  Let's talk once our code is merged on how to make this one stream, or otherwise rationalize this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940716446"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065867088", "body": ""}
{"comment": {"body": "We will need to completely re-do the resizing bar stuff for this UI to work as expected.  Right now you're stacking the two \"below\" bars vertically, with one resizing bar above them, whereas we want to be able to resize in-between the bars.  Tricky stuff.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940717423"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065867568", "body": ""}
{"comment": {"body": "`PullRequestInfo` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940717823"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065868766", "body": ""}
{"comment": {"body": "It probably makes more sense for the PR stream to drop repeats internally -- that's what the other streams are doing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940718819"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065869853", "body": ""}
{"comment": {"body": "This isn't directly related to your PR, but joining all the streams into one set of properties to render seems to be getting a bit out of hand -- I think I might look into improving this, there's no reason we couldn't send each separate stream to the webview so only its items are re-rendered when that stream changes...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940719648"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065869938", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065876020", "body": ""}
{"comment": {"body": "Yup. Our sidebar does not work like the explorer sidebar. From basic inspection, they use some JS to dynamically set the splits height and top position.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940724223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#pullrequestreview-1065876893", "body": ""}
{"comment": {"body": "The behaviour of the top of our bar is deliberately different, but the bar in-between the current file and PR views will need to be more similar to the VSCode bar.  We can probably adapt what we have now to work in both contexts.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a117425a-81b3-4c86-ba92-023c60a367d9?message=39009d0b-451b-4d51-9df7-42be15234217).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2546#discussion_r940724990"}}
{"title": "Move all related message data under one body", "number": 2547, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2547", "body": "MessageContent + Metadata should be under one MessageData object.\nThis is reducing duplication and ensuring easier rollout of changes when we plan to add reactions etc. to messages.\nChose to follow the optional model for versioning (I'm not going to solve versioning in this pr :))\nWe will eventually deprecate/remove the old fields (messageContent) once clients have all been updated to a recent version.\nAnnoyingly, half of this is updating code generated swift files, which sucks."}
{"comment": {"body": "So just to be clear, all message requests and responses will duplicate the content and metadata, so we keep backwards compatibility?  Do you think that will cause any problems (all message and thread data will be 2x as large...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2547#issuecomment-1208695946"}}
{"comment": {"body": "> So just to be clear, all message requests and responses will duplicate the content and metadata, so we keep backwards compatibility? Do you think that will cause any problems (all message and thread data will be 2x as large...)\r\n\r\nCorrect.\r\nI don't think it'll cause a problem for the near term. These fields will be removed in about 2-3 weeks once I've validated clients are updated.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2547#issuecomment-1208697327"}}
{"comment": {"body": "Let me know if this is okay.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2547#issuecomment-1209649060"}}
{"comment": {"body": "Suspending pr until we figure out versioning.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2547#issuecomment-1209695412"}}
{"title": "Fix deleting messages on vscode", "number": 2548, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2548", "body": "For some reason was removed in this commit "}
{"comment": {"body": "Accidental delete. Ooopsie.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2548#issuecomment-1208597073"}}
{"comment": {"body": "We need tests for these sort of regressions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2548#issuecomment-1208597776"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2548#pullrequestreview-1065774799", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2548#pullrequestreview-1065796642", "body": ""}
{"title": "Add github reactions apis", "number": 2549, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2549", "body": "Add apis to create/delete reactions."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2549#pullrequestreview-1065852308", "body": ""}
{"title": "Reconfigure vscode directory into semantic structure", "number": 255, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/255", "body": "NOTES\n* General rule of thumb is that webview code cannot have any dependencies or subdependencies to the vscode package \n* Needed to separate our the api models from the api/index.ts because of the above issue (the generated models go into their own exported module since they need to reference in the webview code)\n    * Should we follow the same pattern in web/? (the subdependency thing won't be an issue for web but just for consistency?)\n* Not sure about the messageEditor/ being its own top level directory but I figured if it's temporary then we can just remove it all in one go when it's ready to be removed. I can also see a case where maybe we have a playground/ top level directory with sandbox code in it(?)"}
{"comment": {"body": "> Not sure about the messageEditor/ being its own top level directory but I figured if it's temporary then we can just remove it all in one go when it's ready to be removed. I can also see a case where maybe we have a playground/ top level directory with sandbox code in it(?)\r\n\r\nI think the MessageEditor itself can potentially live in /components as it's reused in a bunch of webviews...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/255#issuecomment-1030270634"}}
{"comment": {"body": "@matthewjamesadam to be clear it is, just all the webview/command code is in the top level:\r\n![image](https://user-images.githubusercontent.com/13431372/152588464-25f4d482-4bf2-4bd0-a568-11a75efaef48.png)\r\n\r\nUnless your suggestion is to move all of that into `components/MessageEditor/` too?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/255#issuecomment-1030272282"}}
{"comment": {"body": "> @matthewjamesadam to be clear it is, just all the webview/command code is in the top level: ![image](https://user-images.githubusercontent.com/13431372/152588464-25f4d482-4bf2-4bd0-a568-11a75efaef48.png)\r\n> Unless your suggestion is to move all of that into `components/MessageEditor/` too?\r\n\r\nOh I see.  Sure that's fine -- that stuff will be removed anyways, it's just a temporary place for me to test the message editor UI", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/255#issuecomment-1030273407"}}
{"comment": {"body": "> > @matthewjamesadam to be clear it is, just all the webview/command code is in the top level: ![image](https://user-images.githubusercontent.com/13431372/152588464-25f4d482-4bf2-4bd0-a568-11a75efaef48.png)\r\n> > Unless your suggestion is to move all of that into `components/MessageEditor/` too?\r\n> \r\n> Oh I see. Sure that's fine -- that stuff will be removed anyways, it's just a temporary place for me to test the message editor UI\r\n\r\nYeah so I guess we could also call the folder `playground/` that just houses all of the temporary code if we want to be more organized about it.. I guess that will depend on how often this will come up and whether there will be different folders living simultaneously (i.e. I wouldn't want multiple top level temporary folders as we're building features concurrently) (Edit: we can refine this as we go, shouldn't block this PR from going in I don't think)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/255#issuecomment-1030283411"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/255#pullrequestreview-873582754", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/255#pullrequestreview-873583540", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/255#pullrequestreview-873583975", "body": "Looks good to me."}
{"title": "Add PullRequestCommentModel and PullRequestReviewModel", "number": 2550, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2550", "body": "This is where we'll store top-level comments and reviews"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2550#pullrequestreview-1065918440", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2550#pullrequestreview-1067033063", "body": ""}
{"title": "Rename variable", "number": 2551, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2551"}
{"title": "Reduce pod rollover percentage to 20%", "number": 2552, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2552", "body": "reduce pod rollover percentage. We are hitting CPU limits on our kube cluster due to concurrent deploys"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2552#pullrequestreview-1065905959", "body": ""}
{"title": "Do not trust points where the file content hash has changed", "number": 2553, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2553", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2553#pullrequestreview-1066137384", "body": ""}
{"title": "upgrade exposed", "number": 2554, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2554", "body": "Includes one of my changes that I needed."}
{"title": "Implement getPullRequestsForCommits", "number": 2555, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2555"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2555#pullrequestreview-1067036137", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2555#pullrequestreview-1067078085", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2555#pullrequestreview-1067148097", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2555#pullrequestreview-1067181063", "body": ""}
{"title": "Update PR Spec", "number": 2556, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2556", "body": "Include MergedAt & mergeCommitSha for TLC"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2556#pullrequestreview-1067154382", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2556#pullrequestreview-1067169781", "body": ""}
{"title": "Fix colours in git clipboard view", "number": 2557, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2557", "body": "Fixes UNB-526\nUse input colours instead of dropdown colours.  Looks good on lots of themes.\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2557#pullrequestreview-1067238481", "body": ""}
{"title": "Clear calculated source points from team", "number": 2558, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2558"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2558#pullrequestreview-1067172702", "body": "One q"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2558#pullrequestreview-1067175287", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2558#pullrequestreview-1067179662", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2558#pullrequestreview-1067190408", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2558#pullrequestreview-1067213982", "body": ""}
{"title": "Encode and decode to escape", "number": 2559, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2559", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2559#pullrequestreview-1067183711", "body": ""}
{"title": "Matt/message editor tweaks", "number": 256, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/256", "body": "A few tweaks to MessageEditor to make it behave more like other editors:\n\nBlock-level toolbar buttons (quote and code, right now) now hilight properly when the cursor is within a quote/code block, and in this state clicking on the button reverts the block to a plain paragraph (ie, acts as a toggle)\nDeleting the beginning of a custom block reverts the block to a plain paragraph\nHitting enter a second time at the end of a custom block closes the block and creates a new plain paragraph (how you can 'exit' out of a block).\nEnsure there is an empty editable paragraph at the end of documents, so you can always add stuff at the end."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/256#pullrequestreview-875157737", "body": ""}
{"comment": {"body": "Just a styling thing. \r\nIt's strange to me that a function within an if statement has a mutation side effect.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/256#discussion_r800992866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/256#pullrequestreview-875158013", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/256#pullrequestreview-875158099", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/256#pullrequestreview-875259142", "body": ""}
{"comment": {"body": "Good question -- the thinking is that if it handles the event it returns true, if not it returns false.  The handling and the return value are kind of tied together...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/256#discussion_r801067211"}}
{"title": "DiffStatParser: Reduce spammy errors", "number": 2560, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2560"}
{"title": "Fix truncation on git action UI", "number": 2561, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2561", "body": "Remove the branch name from the action button\nTruncate the branch name in the commandline\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2561#pullrequestreview-1069190269", "body": ""}
{"title": "Add message reaction models", "number": 2562, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2562"}
{"title": "Refactor MessageView properties", "number": 2563, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2563", "body": "Rename and restructure some props to anticipate the TLC work (i.e. generalize the MessageView to handle TLC UI)\nUI changes should be net zero"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2563#pullrequestreview-1067491314", "body": ""}
{"title": "Fix SourceMark thread caching", "number": 2564, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2564", "body": "Fixes UNB-507\nReproduction steps:\n\nOpen a file with source marks\nCut code such that you cut a sourcemark -> verify that the sourcemark does not end up repositioning elsewhere\nPaste the code in the file somewhere else\n\nPreviously, the pasted code would not end up having a sourcemark.  With this fix, it does.  Effectively we cache the loaded thread data so that even when the sourcemark disappears, we can reuse the thread data after."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2564#pullrequestreview-1067406505", "body": ""}
{"title": "Basic PR sidebar styling", "number": 2565, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1068594557", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1068596519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1068636644", "body": ""}
{"comment": {"body": "I'm guessing we should still show 'Related Pull Requests' for this state?\r\n\r\nAnd should Requests be capitalized?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r942695847"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1068965793", "body": ""}
{"comment": {"body": "Makes esense.\n\nCapitalization doesn't really matter. We have a text transform on this to be all caps.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r942929710"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1068967710", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071358888", "body": ""}
{"comment": {"body": "I think this is the equivalent to `FileParser.getShortFileName` ? (fn could prob be renamed..)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r944644861"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071359261", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071359469", "body": ""}
{"comment": {"body": "What is this? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r944645260"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071360683", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071381684", "body": ""}
{"comment": {"body": "We'll need to hookup onClick soon.\r\nI can remove the commented code soon but it'll look something like this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r944660353"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071389293", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071467749", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071470025", "body": ""}
{"comment": {"body": "As a side note we probably need to catch exceptions here -- the git and API operations could fail...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r944693109"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071634902", "body": ""}
{"comment": {"body": "You can use `nameA.localeCompare(nameB)` instead I think?  This should handle case and other language things?\r\n\r\nAlso, if there are two PRs with the same title (unlikely, I know) then the sorting will be unstable -- if the strings are the same we might want to sort by PR ID.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r944761644"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071715551", "body": ""}
{"comment": {"body": "Would this be simpler if you assign a fixed value to the items that have no diffs?\r\n```\r\nconst aChanges = (aDiffState.insertions ?? -1) + (aDiffStats.deletions ?? -1);\r\nconst bChanges = (bDiffState.insertions ?? -1) + (bDiffStats.deletions ?? -1);\r\nif (aChanges !== bChanges) {\r\n  return aChanges - bChanges;\r\n}\r\nreturn titleComapreFn(a, b);\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r944797200"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071717251", "body": ""}
{"comment": {"body": "Similarly, might be simpler to assign a very low (or very high?) date...\r\n```\r\nconst aTime = a.pullRequest.mergedAt?.getTime() ?? 0;\r\ncomst bTime = b.pullRequest.mergedAt?.getTime() ?? 0;\r\nif (aTime !== bTime) {\r\n  return aTime - bTime;\r\n}\r\n\r\nreturn titleComapreFn(a, b); // probably need a failover?\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r944798698"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071717484", "body": "A bunch of tiny things, take them or leave them as you see fit..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071726938", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071735218", "body": ""}
{"comment": {"body": "IMO we should add a `title` property here so that users hovering over the icons can understand what they represent in case the icons aren't fully clear\r\n\r\nI think the ButtonProps already take a title prop so we'd just need to pass one in at this level", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r944815841"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071746179", "body": ""}
{"comment": {"body": "Makes sense. I'll get this in first and revisit in future PR. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r944826496"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1071763630", "body": "Sidebar height is off right now.\nWill get it fixed in next Pr with resizing"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1073327158", "body": ""}
{"comment": {"body": "\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=93126459-67e5-4e43-b2d6-61cc3e30494c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r946208910"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1073337087", "body": ""}
{"comment": {"body": "testing\n\n--\n\nComment edited using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=a9f8fbff-a1ef-4c11-9d65-fa74408d175c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r946216170"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1074586561", "body": ""}
{"comment": {"body": "@jeffrey-ng test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=fd427f67-2abb-44f4-ab9e-d80fb1e1f714).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r947114385"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1077848999", "body": ""}
{"comment": {"body": "this is a test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=9f932c61-8c50-4391-ab34-ea1d3145fa97).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r949493444"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1077854777", "body": ""}
{"comment": {"body": "TEsting\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=e7d4b98b-45e0-4a40-864f-ca431b37dbe6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r949497616"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1077855830", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=814bd8bc-4c1b-4241-8299-5dd81b8ca861).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r949498364"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1077858792", "body": ""}
{"comment": {"body": "testtt\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=e17d65ca-ed31-4766-a328-a3c09368744a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r949500373"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1077860721", "body": ""}
{"comment": {"body": "abcg\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/51e2b7b3-2d56-4639-a068-c70ac1ce7ee8?message=d254e77a-7a4c-4be2-916d-ac2de3d3d2ae).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r949501746"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1077861369", "body": ""}
{"comment": {"body": "More testing\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/5fb516bc-a7df-496d-bba0-dc85f0428504?message=3c223363-c56d-4887-b6fc-a8ad53f25ce9).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r949502239"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1077861698", "body": ""}
{"comment": {"body": "Testing\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/5fb516bc-a7df-496d-bba0-dc85f0428504?message=000b5899-5d2c-4bb6-9775-192748c2a3f3).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r949502483"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1077997799", "body": ""}
{"comment": {"body": "testing\n\ngs\n\n--\n\nComment edited using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=d025062e-2396-415f-84f1-587d399f3828).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r949594498"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1078088160", "body": ""}
{"comment": {"body": "asdfasdfgggssss\n\n--\n\nComment edited using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=b5a824f6-43f4-44bc-99ea-26d45b59612e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r949659007"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1079368712", "body": ""}
{"comment": {"body": "gs\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=5b7fcab0-8e89-4a0f-aec6-a415739a418f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r950567594"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1079371181", "body": ""}
{"comment": {"body": "asdf\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=8b739a50-69ee-4c37-9577-0f5aa13aa2f0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r950569377"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1079415030", "body": ""}
{"comment": {"body": "testing\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=fbca863d-8d8e-469c-8e66-1f9cc9e903fa).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r950602343"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1080921776", "body": ""}
{"comment": {"body": "@jeffrey-ng ??\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/51e2b7b3-2d56-4639-a068-c70ac1ce7ee8?message=b77b78a0-598d-4a5f-8b2d-4ffb24a34217).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r951729565"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1080973763", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=0b64fe68-aeaf-4f9d-8904-b9961858760e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r951776184"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1080973861", "body": ""}
{"comment": {"body": "testtt\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=00a470ca-476e-4379-af1e-869d1e78b9e7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r951776278"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1080974893", "body": ""}
{"comment": {"body": "more test push\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=76b5745b-9c45-49c7-8330-c2a307f9c8cc).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r951777238"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1080977732", "body": ""}
{"comment": {"body": "asdf\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=fb51f379-c24b-492d-bfe9-a15f101a1ed1).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r951779988"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#pullrequestreview-1098409950", "body": ""}
{"comment": {"body": "This was Kelly's feedback fwiw\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/b65baa14-fe18-49f8-a3ef-70e40b78f1df?message=702b1d92-4987-4423-b7d5-a5704ae98256).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2565#discussion_r964296265"}}
{"title": "Add issue comment webhook handler stub", "number": 2566, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2566"}
{"title": "update github queues", "number": 2567, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2567"}
{"title": "Try github reaction handler", "number": 2568, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2568"}
{"title": "Add ActiveFileManager", "number": 2569, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2569", "body": "Fixes UNB-511\nThis got pretty involved.  The goal for the sidebar views is to track the file that currently has focus, meaning:\n* If the focused tab is a text editor, display sourcemarks for the file open in the editor\n* If the focused tab is a webview, and that webview is associated with a file in some way (say, a discussion view on a thread, anchored via a sourcemark to a file), then display sourcemarks for that file.\nThere are multiple problems here:\n* VSCode does not have a single unified API that allows you to track the focused tab, across both TextEditors and webviews.  The official guidance is to use window.activeTextEditor to track the focused tab when it is a text editor, and WebviewPanel.active to track the active webview\n* Webviews have no notion of an associated file.\nThis PR does the following:\n* Adds a property WebviewContentController.associatedFilePath -- this allows any webview to declare that it has an associated file.  Internally the WebviewContentController tracks its own focus state, plus this new filePath property, to understand if the focused webview is associated with a file.\n* Adds a new class ActiveFileManager, which muxes both the window.activeTextEditor state, plus all WebviewContentController.associatedFilePath states, to resolve one single active file path.\n* Update TextEditorSourceMarkManager to use this.\nPhew."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2569#pullrequestreview-1068765850", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2569#pullrequestreview-1068766218", "body": "Outside of the one question, everything looks good to me."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2569#pullrequestreview-1068767922", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2569#pullrequestreview-1068908345", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2569#pullrequestreview-1068920775", "body": ""}
{"title": "GraphQL client with auth", "number": 257, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/257", "body": "Changes\n\nmoved the submodule in order to restructure all GitHub-related graphql information under one directory:\n\napiservice/src/main/resources/\n github-graphql\n   .graphqlconfig\n   queries\n   schema\n\nIntroduce V3 (REST) and V4 (GraphQL) sub-clients in GitHubAppClient\n\nAction needed\nDevelopers need to force update once this change is merged due to the submodule move:\nsh\nrm -rf .git/modules/\nrm -rf apiservice/src/main/resources/github-graphql-schema/\ngit submodule update --init --force"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/257#pullrequestreview-873714701", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/257#pullrequestreview-873733328", "body": ""}
{"comment": {"body": "good idea, this was annoying", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/257#discussion_r799915148"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/257#pullrequestreview-873735117", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/257#pullrequestreview-873736327", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/257#pullrequestreview-873738568", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/257#pullrequestreview-873739834", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/257#pullrequestreview-873820859", "body": ""}
{"title": "Add reactions api", "number": 2570, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2570"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2570#pullrequestreview-1068695290", "body": "nice"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2570#pullrequestreview-1068701473", "body": ""}
{"comment": {"body": "If we're doing our own thing with this feature, should we limit this to the GitHub set of emojis?  Or should we just allow any of the unicode emojis?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2570#discussion_r942741282"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2570#pullrequestreview-1068740651", "body": ""}
{"comment": {"body": "GitHub set is intentional. Once they add webhook support we will transition to synchronizing with GitHub.\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2570#discussion_r942768931"}}
{"title": "Fix sourcemark move between files detection", "number": 2571, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2571", "body": "A regression was introduced where we were getting the diff between commits, but only for the from-point file. This worked fine if the point does not move between files.\nThis change gets the diff between commits, but this time including all the files in the diff. This in turn allows the RangeAdjust class to track the point across multiple files.\n\nNote\nWill need to clear out all old generated sourcepoints from all teams via admin web after this lands, forcing the VSCode extensions to recalculate all points again."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2571#pullrequestreview-1068714511", "body": ""}
{"comment": {"body": "using compact now because files can be empty", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2571#discussion_r942750493"}}
{"title": "GitHub app validation includes issues:read", "number": 2572, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2572", "body": "Impact of this change:\n1. Warnings will be logged: \"Installation permissions are invalid\",\n   which is useful for knowing which Org have not yet accepted the new permissions.\n2. Orgs that do not accept the permissions will no longer be \"maintained\" by the\n   GitHubInstallationMaintenanceJob background job."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2572#pullrequestreview-1068825393", "body": ""}
{"title": "Add universal download page", "number": 2573, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2573", "body": "Current route is getunblocked.com/getunblocked\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2573#pullrequestreview-1068874493", "body": ""}
{"comment": {"body": "Is this route temporary? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2573#discussion_r942865715"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2573#pullrequestreview-1068874848", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2573#pullrequestreview-1068895795", "body": ""}
{"comment": {"body": "No, it was what was suggested. I'll double check w Ben and Dennis before merging", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2573#discussion_r942880866"}}
{"title": "Update service permissions", "number": 2574, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2574"}
{"title": "Remove turbo", "number": 2575, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2575", "body": "Turbo is a collection of libraries that handle server side rendering and other \"magic\".\nIt optimizes load times by doing a bunch of caching and partial Dom changes. \nBasically SPA without being a SPA.\nThis PR removes a hack where we disabled turbo for a few pages (caused slow load times in PR review) and potentially fixes some other navigation issues.\n"}
{"comment": {"body": "CAVEAT: Sidebar state will now refresh on navigation.\r\naka for a split second, it shows the loading spinner before thread data loads.\r\n\r\nWill continue digging into this but I think that tradeoff is okay if it means fixing actual navigation issues (e.g. sometimes links wouldn't work, browser back may break sidebar, etc...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2575#issuecomment-1211222913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2575#pullrequestreview-1068876611", "body": ""}
{"comment": {"body": "Instead of cleaning out state, keep existing state which includes turbo. restorationIdentifier", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2575#discussion_r942867115"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2575#pullrequestreview-1068879106", "body": ""}
{"comment": {"body": "Fires after Turbo renders the page. When turbo renders a page from cache, the sidebar DOM may be rendered but the react / virtual Dom is not active.\r\n\r\nWe need to manually trigger a ReactDOM.render to kick off the entire process.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2575#discussion_r942868883"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2575#pullrequestreview-1068911974", "body": "Awesome "}
{"title": "Fix flaky test", "number": 2576, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2576", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2576#pullrequestreview-1068912391", "body": "Thanks!"}
{"title": "Expose exception to understand read-only faults", "number": 2577, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2577", "body": "\nThe DiscussionHelpers class is trying to fetch content for a commit that does not exist in the repo, which should never happen.\nThe resolution is expected to be notInRepo. We'll see..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2577#pullrequestreview-1068904677", "body": ""}
{"title": "Use upsert instead of insertIgnore", "number": 2578, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2578", "body": "Slower but needed for re-ingestion of pull requests to back fill mergedAt and mergeCommitSha"}
{"title": "Fix for read-only faults", "number": 2579, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2579", "body": "In the case where there are multiple original points (occurring when Unblocked\ningests pre-merge and post-merge points), then we were incorrectly choosing\nthe first point which may not even exist in the repo. As a result, there was\nan asymmetry between the resolution returned from the CommitLocation and the\noriginal point returned alongside it.\n broken on the left \n fix on the right\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2579#pullrequestreview-1068980443", "body": ""}
{"title": "Ensure createdAt has microsecond precision", "number": 258, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/258"}
{"title": "Revert \"GitHub app validation includes issues:read\"", "number": 2580, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2580", "body": "Reverts NextChapterSoftware/unblocked#2572"}
{"title": "Upsert pull request comments from webhooks", "number": 2581, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2581"}
{"title": "Add serial name", "number": 2582, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2582"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2582#pullrequestreview-1069039531", "body": ""}
{"title": "Enable Intercom", "number": 2583, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2583", "body": "Intercom was not enabled in Installation Sidebar."}
{"comment": {"body": "Default enableIntercom = false...\r\nShould it be enabled true by default?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2583#issuecomment-1211396667"}}
{"comment": {"body": "It's unfortunate there's no good way to make this work automatically:\r\n- We can't enable this for all webviews, because we don't want to show responses in an unexpected/strange view.  Also it would hammer intercom, though I don't know if that's really a problem or not.\r\n- If we don't enable it, but we *do* use the intercom context in that webview, the 'Contact Support' button won't work.\r\n\r\nOne thing we could do is show an error of some kind in when this occurs, in dev (ie if the Intercom context is used without intercom being enabled)-- that way at least we get some visibility that the webview is misconfigured.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2583#issuecomment-1211400133"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2583#pullrequestreview-1069046452", "body": "We should ship this today imo."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2583#pullrequestreview-1069046596", "body": ""}
{"title": "Get pull request number from the issue comment", "number": 2584, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2584"}
{"title": "Bulk ingest top level comments", "number": 2585, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2585"}
{"title": "Clicking install should go to the GitHub org install page, not the org selection page", "number": 2586, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2586", "body": "Problem is that the App client fails with 401 on every call that is made outside of an installed app. It cannot even be used for public endpoints.\nFor now, use a no-auth client, which sounds a bit crazy but is actually totally fine at our scale.\nLonger term fix is to use a GitHub OAuth App, called \"Unblocked Helper\", that will perform these authenticated requests in place of the no-auth client. (This is what we did in Skywagon.)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2586#pullrequestreview-1069138708", "body": ""}
{"title": "Set MessageModel.prReviewId during ingestion", "number": 2587, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2587"}
{"title": "Change min date for metrics charts", "number": 2588, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2588"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2588#pullrequestreview-1069247363", "body": ""}
{"title": "Obsolete Thread.prComment field", "number": 2589, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2589", "body": ""}
{"comment": {"body": "It's an optional field, and no clients seem to be using it right now -- are we sure no clients exist that use it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2589#issuecomment-1212140037"}}
{"comment": {"body": "> It's an optional field, and no clients seem to be using it right now -- are we sure no clients exist that use it?\r\n\r\nIt was always an optional field, so even legacy clients should not break right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2589#issuecomment-1212158427"}}
{"comment": {"body": "> It was always an optional field, so even legacy clients should not break right?\r\n\r\nThey won't crash, but they might not work properly, ie might not display PR information as expected.  I have no evidence of this though, it's pretty hard to bisect and figure out when this property stopped being used.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2589#issuecomment-1212180497"}}
{"comment": {"body": "> > It was always an optional field, so even legacy clients should not break right?\r\n> \r\n> They won't crash, but they might not work properly, ie might not display PR information as expected. I have no evidence of this though, it's pretty hard to bisect and figure out when this property stopped being used.\r\n\r\nThese stable releases have the client fix: 287+\r\n\r\nThere are two people using older versions; Harry churned out 20 days ago.\r\n\r\n<img width=\"854\" alt=\"Screen Shot 2022-08-11 at 12 32 25\" src=\"https://user-images.githubusercontent.com/1798345/184223974-c26ddd76-457b-4004-9bf5-6a53ed72ff72.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2589#issuecomment-1212404349"}}
{"comment": {"body": "Huh, weird, I thought I updated my client recently. That\u2019s fine though, if anything I can be the guinea pig and see how the experience is affected if at all.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2589#issuecomment-1212415981"}}
{"title": "Fixes token leeway incorrect units", "number": 259, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/259"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/259#pullrequestreview-873751046", "body": ""}
{"title": "Client linting", "number": 2590, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2590"}
{"comment": {"body": "> Is this just from running `npm run fix-lint` on the command line?\r\n\r\nNo, that didn't fix it. It generated warnings, and I manually fixed them.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2590#issuecomment-1212154702"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2590#pullrequestreview-1069913147", "body": "Is this just from running npm run fix-lint on the command line?"}
{"title": "Strip newlines", "number": 2591, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2591", "body": ""}
{"comment": {"body": "Client change not necessary. Closing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2591#issuecomment-1212279913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2591#pullrequestreview-1070081420", "body": ""}
{"comment": {"body": "Is this\r\n\r\nreally necessary? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2591#discussion_r943738740"}}
{"title": "Work around TS optional-chaining bug", "number": 2592, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2592", "body": "This TS code:\nconst activeFile = activeWebviewFile ?? window.activeTextEditor?.document?.fileName;\nresults in this JS code:\nconst activeFile = activeWebviewFile ?? vscode__WEBPACK_IMPORTED_MODULE_1__.window.activeTextEditor.document.fileName;\nie, the optional chaining is simply being removed by TypeScript.  The resulting code is not valid or safe, in that whenever document is falsy JS will stop executing.  The upshot of this is that on startup, if you have no editor focused, you will see this error:\n\nThis PR does the optional-chain checking manually.  I am going to see if there's a better solution."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2592#pullrequestreview-1070116546", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2592#pullrequestreview-1070116758", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2592#pullrequestreview-1070117562", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2592#pullrequestreview-1070119752", "body": ""}
{"title": "Flush last set of changes for reactions", "number": 2593, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2593"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2593#pullrequestreview-1070176205", "body": ""}
{"comment": {"body": "why? seems idempotent", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2593#discussion_r943804928"}}
{"title": "Ability to set hasSeenTutorial on person in admin web", "number": 2594, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2594"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2594#pullrequestreview-1070211074", "body": ""}
{"title": "Fix SourceMark display when TextEditor is loaded after Webview", "number": 2595, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2595", "body": "Fixes UNB-535\nThis happened under one circumstance: when two UIs referenced the same file (and thus the same TextEditorSourceMarks), the first was a webview, and the second a text editor.  This happens when you display a discussion view."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2595#pullrequestreview-1070242077", "body": ""}
{"comment": {"body": "This was the key bug here -- if the first UI to reference a file was a non-editor UI (ie a webview), we would attempt to add SM information at an index that didn't exist.  Ultimately the data would get overwritten.  This ensures it works as expected.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2595#discussion_r943851828"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2595#pullrequestreview-1070261640", "body": ""}
{"title": "Implement getPullRequestInfo endpoint", "number": 2596, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2596", "body": "Missing a couple tests but I'll add these later. I want to get this out so that Kay can build against real data in dev."}
{"title": "Fix bug that caused some lines in the editor to not render gutter", "number": 2597, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2597", "body": "This would occasionally cause some lines in the editor to not have the space reserved for the gutter icons (ie, the indentation would look wrong)."}
{"title": "Add slack extractor", "number": 2598, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2598", "body": "A harness we're going to be using for training data for some experiments we're planning on running."}
{"comment": {"body": "Alternative: you can dump the entire message history for the workspace as a json file", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2598#issuecomment-1212581673"}}
{"comment": {"body": "The slack export is annoyingly not channel bound (also, I don't have access to it).\r\nPlanning to one-off generate a custom json that I'm going to be using as training data.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2598#issuecomment-1212585361"}}
{"title": "New person slack message shows their team memberships too", "number": 2599, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2599"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2599#pullrequestreview-1070467221", "body": ""}
{"title": "Update File Names", "number": 26, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/26", "body": "Update Component and other subdirectories to use PascalCase.\nAlso removed @auth and moved to store.\nUPDATE\nNeeded to set git config core.ignorecase false for git to handle the change to Pascalcase."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/26#pullrequestreview-848225652", "body": ""}
{"comment": {"body": "Is there a reason we have another `index.ts` at this level?  Should we just include whatever we're exporting at `/store/index.ts` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/26#discussion_r781469616"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/26#pullrequestreview-848225911", "body": ""}
{"title": "Create Instant.nowWithMicrosecondPrecision", "number": 260, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/260", "body": "Different systems have different timestamp precisions. Postgres supports microsecond precision, so let's make sure we use that everywhere.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/260#pullrequestreview-873907625", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/260#pullrequestreview-875039436", "body": ""}
{"comment": {"body": "I'm going to probably add custom lint rules for this sort of shit as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/260#discussion_r800907498"}}
{"title": "Add test", "number": 2600, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2600", "body": "Follow up from https://github.com/NextChapterSoftware/unblocked/pull/2596"}
{"title": "Add training data", "number": 2601, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2601"}
{"title": "Poor mans login-as-user: Show mine and recommended threads in admin web", "number": 2602, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2602", "body": "problem\n\n\n\nchanges\n\nteam member page shows these links\n\n\n\nmine\n\n\n\nrecommended\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2602#pullrequestreview-1070510518", "body": "Love it"}
{"title": "Fix thread titles", "number": 2603, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2603", "body": "Fixes UNB-534"}
{"title": "Fix triggerRepoBulkPrIngestion", "number": 2604, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2604"}
{"title": "Disable unblocked signatures for comments posted from Unblocked for Expo", "number": 2605, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2605", "body": "Fixes UNB-539\nTemp change to disable signatures for expo. \nWe'll remove this later today when I do it right: "}
{"title": "Add handler stub for GitHub review events", "number": 2606, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2606"}
{"title": "Rename PullRequestGroup to PullRequestComment", "number": 2607, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607"}
{"comment": {"body": "issue comment(edit)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#issuecomment-1213374026"}}
{"comment": {"body": "Closing in favour of https://github.com/NextChapterSoftware/unblocked/pull/2608.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#issuecomment-1213441432"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#pullrequestreview-1071455872", "body": ""}
{"comment": {"body": "I think this needs disambiguation: comment is quite overloaded:\r\n- issue comment\r\n- review comment\r\n- file comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#discussion_r944688783"}}
{"comment": {"body": "Same comment as below, where \"comment\" is too ambiguous.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#discussion_r944689096"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#pullrequestreview-1071460478", "body": ""}
{"comment": {"body": "They are all comments though no? I guess that's kind of my point. This one model could encompass all of those types?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#discussion_r944690212"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#pullrequestreview-1071463200", "body": ""}
{"comment": {"body": "So this includes file comments?\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/06c7c10d-7e07-42ec-aa08-eb8634eae9e6?message=5798ca0f-6013-4235-b716-0cd396ea3a57).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#discussion_r944691018"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#pullrequestreview-1071466476", "body": "review comment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#pullrequestreview-1071480718", "body": ""}
{"comment": {"body": "Happy with the structure, just not the name \"comment\" because it's overloaded.\r\n\r\nMaybe try:\r\n- container, block, section, review, ~group~", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#discussion_r944696387"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#pullrequestreview-1071506431", "body": ""}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#discussion_r944704468"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#pullrequestreview-1071516797", "body": "top-level comment on review (edit)"}
{"comment": {"body": "ignore", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2607#discussion_r944707768"}}
{"title": "[TLC] Refactor models to leverage Message model", "number": 2608, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1071731649", "body": ""}
{"comment": {"body": "Would `null` and `[]` be treated differently here; and if they are equivalent, then maybe mark this field required?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#discussion_r944814516"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1071745338", "body": ""}
{"comment": {"body": "Not right now, but I tend to want to err on the side of optionality since it's harder to go from required to optional (i.e. if future designs do end up representing these two states differently). The difference on the clients is negligible ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#discussion_r944825780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1072922778", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1073119499", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1073139655", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1073141507", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1073145488", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1073146456", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1074411626", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1074430636", "body": "(sorry kay, please ignore just testing)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1074431282", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#pullrequestreview-1074903320", "body": ""}
{"comment": {"body": "yo kay\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/88362071-d445-4e48-9bba-9d8e43dfe0c1?message=1d274ad6-73c9-4f57-90b7-d6c5602fb12f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2608#discussion_r947346640"}}
{"title": "Make Message API models reusable for TLCs and CLCs", "number": 2609, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2609"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2609#pullrequestreview-1071791057", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2609#pullrequestreview-1071832878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2609#pullrequestreview-1071839047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2609#pullrequestreview-1071908820", "body": ""}
{"title": "Update secret", "number": 261, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/261"}
{"title": "lucene index of source code", "number": 2610, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2610", "body": "This allows us to do some complicated distance queries to validate the hypothesis of slack -> source code."}
{"title": "Add TeamSettingsModel and the ability to edit from Admin Console", "number": 2611, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2611", "body": "Settings not used yet (follow up PR)"}
{"comment": {"body": "I think in discussion with @richiebres we were discussion making settings granular based off subjects.\r\nWhat we wanted to avoid is having a massive global settings object (a la skywagon).\r\n@richiebres can provide more input.\r\n\r\nFor example, this would fall under TeamScmPreferences settings.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2611#issuecomment-1213541526"}}
{"comment": {"body": "> I think in discussion with @richiebres we were discussion making settings granular based off subjects. What we wanted to avoid is having a massive global settings object (a la skywagon). @richiebres can provide more input.\r\n> \r\n> For example, this would fall under TeamScmPreferences settings.\r\n\r\nAgree, but we can refactor/migrate at a later point. All good stuff to keep in mind.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2611#issuecomment-1213553985"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2611#pullrequestreview-1071806076", "body": "Looks good. One high level thought for the future. I think at some point we will have hierarchical settings that inherit and override settings from above. For example:\n- global settings\n- team settings\n- repo settings\nYour change is not incompatible with that."}
{"title": "Use team settings to decide whether to add a comment signature", "number": 2612, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2612", "body": "Fixes UNB-541"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2612#pullrequestreview-1071831625", "body": ""}
{"title": "update", "number": 2613, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2613"}
{"title": "Api service should be following package naming similar to other services", "number": 2614, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2614"}
{"title": "Index source code properly", "number": 2615, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2615", "body": "Lucene, for whatever reason, does not do proper segementation of periods in source code.\nSo fuck that, we'll do our own."}
{"title": "Confetti for everyone", "number": 2616, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2616"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2616#pullrequestreview-1074413363", "body": ""}
{"title": "Revert \"Make Message API models reusable for TLCs and CLCs\"", "number": 2617, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2617", "body": "Reverts NextChapterSoftware/unblocked#2609"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2617#pullrequestreview-1072997673", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2617#pullrequestreview-1073006213", "body": ""}
{"title": "Make Message API models reusable for TLCs and CLCs - V2", "number": 2618, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2618", "body": "Changes\n\nReapply original change: Revert \"Revert \"Make Message API models reusable for TLCs and CLCs (#2609)\" (#2617)\"\nMake Message.threadId optional.\nContinue to provide threadId value on the wire in order to not break legacy clients.\n\nFollow-up plan\n\nIn a week or so, remove the deprecated Message.threadId field entirely."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2618#pullrequestreview-1073070756", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2618#pullrequestreview-1073101057", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2618#pullrequestreview-1073101445", "body": ""}
{"title": "Obsolete Message.threadId [BREAKS API ON MAIN]", "number": 2619, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2619"}
{"comment": {"body": "This looks fine to me -- I kinda want to hold off on \u2705 ing it so we don't accidentally merge it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2619#issuecomment-1215712360"}}
{"comment": {"body": "> This looks fine to me -- I kinda want to hold off on \u2705 ing it so we don't accidentally merge it?\r\n\r\nI changed to draft to prevent accidental merges (it would take several clicks now), so you can approve now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2619#issuecomment-1215744976"}}
{"comment": {"body": "Yes yes yes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2619#issuecomment-1248735913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2619#pullrequestreview-1073155452", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2619#pullrequestreview-1109932925", "body": ""}
{"title": "Add client side logging", "number": 262, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262", "body": "This is probably going to be a contentious PR but I don't care as this project is still in huge flux.\nIf it's too contentious, I'm going to drop it and move on with my life. :)\n\nAdd shared client logger using winston (which allows for toggling/chaining transports and created child loggers).\nAdd api endpoints for SAAS logging which will be authenticated in due time.\nAdd custom winston transport that can send to a backing api endpoint to log to logz.io (this is entirely configurable and the client can control whether they want that..)\nUpdate webpack/tsconfig to allow for winston transports (console and file (for vscode) and maybe logz.io)\n\nTesting:\n1. Validated VSCode\n2. Validated Web\nWeb Logging:\nFrom Browser\n\nFrom Logz.io\n\nVSCode Logging\nFrom IDE\n\nFrom Logz.io\n\nFrom File\n"}
{"comment": {"body": "K.\r\n\r\n1. Moved stuff to shared-web-utils.\r\n2. For web, added logger to existing @utils module.\r\n3. For vscode, added distinct loggers to @extension-log/@web-log modules so you can leverage certain winston transports if you're logging from webview end (for webview, can't use file based or api based logger due to CORS)\r\n4. Cleaned up webpack files a bit.\r\n\r\nI think this is good enough for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#issuecomment-1031928940"}}
{"comment": {"body": "Let me know if this good enough to merge. :)\r\nGoing to merge this soon unless there's a huge disagreement with this pr as merge conflicts are getting painful.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#issuecomment-1032889223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#pullrequestreview-874127133", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#pullrequestreview-874127654", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#pullrequestreview-874129273", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#pullrequestreview-874130211", "body": ""}
{"comment": {"body": "Interesting -- is Winston trying to pull all these modules in automatically?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#discussion_r800273360"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#pullrequestreview-874136106", "body": ""}
{"comment": {"body": "Yeah. It has a few transports it's  bundled with by default that they're planning on moving out to a plug-in packaging system (there's a task for this)  Thankfully we're not using those transports so we can disable webpack needing polyfills.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#discussion_r800277567"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/262#pullrequestreview-874136224", "body": ""}
{"title": "Add Honeycomb to all other web services", "number": 2620, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2620", "body": "Fair bit of functionlity happening in admin-web that I'd like to take a look at.\nMight as well do it for all services?"}
{"title": "Add slack message analyzer", "number": 2621, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2621"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2621#pullrequestreview-1073193653", "body": ""}
{"title": "Updated Sidebar Resizer", "number": 2622, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2622", "body": "First pass at updated sidebar resizer.\nThere are some QOL items that should still be added but not blocking.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2622#pullrequestreview-1076058516", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2622#pullrequestreview-1076058644", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2622#pullrequestreview-1076061819", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2622#pullrequestreview-1077711563", "body": ""}
{"comment": {"body": "It looks like we're passing the file path through the webview so that we can make use of it again once the row is clicked on -- can't we just use the value we have here (in the TextEditorPullRequestManager) when we handle the click event?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2622#discussion_r949393039"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2622#pullrequestreview-1077713598", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2622#pullrequestreview-1077725554", "body": ""}
{"comment": {"body": "Will look into this in a separate PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2622#discussion_r949402863"}}
{"title": "Fix some onboarding state bugs", "number": 2623, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2623", "body": "Fixes UNB-546"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2623#pullrequestreview-1073275331", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2623#pullrequestreview-1073275913", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2623#pullrequestreview-1073277306", "body": ""}
{"comment": {"body": "The logic for this function used to be coupled with a pseudo-onboarded state that only the Hub understood. We've already seen a bug where a customer installed fresh on a new machine and got the \"vscode-less\" onboarding flow. \r\n\r\nOnboarding status should be captured entirely within the `Person` object. We're using `hasSeenTutorial` as the hint for this state", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2623#discussion_r946171187"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2623#pullrequestreview-1073277944", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2623#pullrequestreview-1073282357", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2623#pullrequestreview-1079700608", "body": ""}
{"comment": {"body": "[BUG] - when the network is offline and the app starts then the Person object is not available and the user will see the auth screen briefly before the network comes back online\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2d4824df-4ed6-408f-8cd1-23fc21bd9fa9?message=cf3a2c8d-1903-49d1-a725-24235e51fb11).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2623#discussion_r950878595"}}
{"title": "Upsert PullRequestReviewModel on webhook", "number": 2624, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2624"}
{"title": "Bump main", "number": 2625, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2625"}
{"title": "Sourcemark snippet finder", "number": 2626, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2626", "body": "Part of \nIntroduces a snippet matching algorithm for cases where the untrusted and trusted\npoints have different file content hashes.\n- Match first on the snippet, then match using only the selected lines.\n- Prefer matches closest to where the original snippet is expected to\n  be when there are multiple matches.\nFile              | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s \n------------------|---------|----------|---------|---------|-------------------\nAll files         |     100 |      100 |     100 |     100 |                 \n SnippetFinder.ts |     100 |      100 |     100 |     100 |"}
{"comment": {"body": "@davidkwlam ran after the memory fix from https://github.com/NextChapterSoftware/unblocked/pull/2639 was merged. Stats looks good in DEV. Works fine afaict.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2626#issuecomment-1217504313"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2626#pullrequestreview-1074396905", "body": ""}
{"comment": {"body": "We should probably also point out that this will only work as expected for `string[]`, `number[]` and `boolean[]` -- any other array content will use reference equality which probably won't do what is expected (ie, `equals<UUID>` will use reference equality)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2626#discussion_r946976522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2626#pullrequestreview-1074482655", "body": ""}
{"comment": {"body": "done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2626#discussion_r947039000"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2626#pullrequestreview-1074505653", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2626#pullrequestreview-1074658784", "body": ""}
{"title": "David/rename review status", "number": 2627, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2627", "body": "Not sure why I named it reviewed before since commented is what the GitHub API calls it.\nShould be a safe change since no clients are using this.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2627#pullrequestreview-1074458343", "body": ""}
{"title": "Add analyzer ast filter", "number": 2628, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2628"}
{"title": "Upgrade progress and confirmation", "number": 2629, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2629", "body": "Please ignore the part where I'm manually restarting the hub in this gif (have to do that because the /Applications version is launched instead of my debug build)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2629#pullrequestreview-**********", "body": ""}
{"title": "Add token auth provider for GQL client", "number": 263, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/263", "body": "Problem\nPrevious implementation required adding the token for each new API frontend. \nChanges\nThis PR implements a TokenAuthProvider which gives us ktor client DSL powers to load tokens at the client level. \nNext\nThrow a cache in front of the V3().installationAccessToken(installationId).token call"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/263#pullrequestreview-875061212", "body": ""}
{"title": "Revert \"[TLC] Refactor models to leverage Message model\"", "number": 2630, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2630", "body": "Reverts NextChapterSoftware/unblocked#2608"}
{"title": "Ask for badge permissions so that the sound settings show up in system prefs :facepalm:", "number": 2631, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2631", "body": "macOS Bug. Sound prefs do not show up in this panel unless the badge permission was also requested. \n\ne"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2631#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2631#pullrequestreview-1074772556", "body": ""}
{"comment": {"body": "The real change is just adding `.badge`. There's no action for the user to take here either - it just gets automagically added to permissions by macOS (another bug probably)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2631#discussion_r947246727"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2631#pullrequestreview-1074807391", "body": ""}
{"title": "[TLC] Try Message model refactor again", "number": 2632, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074774611", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074821298", "body": ""}
{"comment": {"body": "We're dealing with a json serializer, so I think naming matters. In other words I don't think you can change either the name or the type of a previously required field. This change will likely cause a deserialization failure", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947282774"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074823212", "body": ""}
{"comment": {"body": "This one is definitely a new unused model though. As in, it's not attached to any existing models so would the serializer still fail? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947284229"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074824628", "body": ""}
{"comment": {"body": "To my understanding, the description field on the PullRequest caused the issue because PullRequest is an existing model that current clients leverage. This change has nothing to do with the PullRequest model or any model that clients currently call for ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947285300"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074845919", "body": ""}
{"comment": {"body": "Yes I think you're right\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a3e1e68f-c913-4e04-9bdd-430d2f7c62c9?message=5ea69c29-6d7d-4853-a542-fbfaef0dc2cf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947301872"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074846202", "body": ""}
{"comment": {"body": "BTW how did this go from a purple to green insight bubble?\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a3e1e68f-c913-4e04-9bdd-430d2f7c62c9?message=99e558a1-cc78-4949-a6f9-c955ebb9db3b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947302106"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074848951", "body": ""}
{"comment": {"body": "ignore me - github issue", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947304262"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074849596", "body": ""}
{"comment": {"body": "\ud83d\ude2d", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947304735"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074855260", "body": ""}
{"comment": {"body": "We should put a comment here that `description` should always be populated even though it's optional until we remove the field", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947309385"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074859890", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/2632/files#diff-0b73cefc225d0349163a02c1f8b467bb7f4e76761b3376943e9728e51df4c6baR42-R43", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947312939"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074869669", "body": ""}
{"comment": {"body": "Can we actually mark this deprecated with `deprecated: true` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947320375"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074876225", "body": ""}
{"comment": {"body": "I had that earlier but apparently there were compiler issues? This follows the same pattern as the `Message.threadId` ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#discussion_r947325416"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2632#pullrequestreview-1074888498", "body": "Voluntold"}
{"title": "Add skip VSCode setup option for 2nd-nth users", "number": 2633, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2633", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2633#pullrequestreview-1074810003", "body": ""}
{"title": "Add skip link to install extensions onboarding view", "number": 2634, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2634", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2634#pullrequestreview-1074874575", "body": ""}
{"title": "Hacky Test DO NOT MERGE: Gzip request bodies in VSCode", "number": 2635, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2635", "body": "Does not work end-to-end due to lack of Ktor support for compressed requests.\nThis is needed to get it working:\n"}
{"comment": {"body": "updated plan:\r\nhttps://linear.app/unblocked/issue/UNB-551#comment-8bf62259", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2635#issuecomment-1226851278"}}
{"title": "pull_request_review_id can be null ", "number": 2636, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2636", "body": ""}
{"title": "GitHub review webhooks have state in lowercase while GitHub API returns it in all caps", "number": 2637, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2637", "body": "Un. Real."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2637#pullrequestreview-1075014403", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2637#pullrequestreview-1075014675", "body": ""}
{"title": "Ingest pull request reviews", "number": 2638, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2638"}
{"title": "Source mark engine using more than 10 MB memory buffer for diffs resulting in failures [ERR_CHILD_PROCESS_STDIO_MAXBUFFER]", "number": 2639, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2639", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2639#pullrequestreview-1075019715", "body": ""}
{"title": "Web Auth Updates", "number": 264, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264", "body": "Logging out on a single tab should trigger all tabs to logout.\nAdded a window event to make sure all tabs are listening to sign out event.\nAlso fixed refresh logic so that new tabs will use the refresh token on init to get its own session storage base token.\nRefactor API layer into Shared."}
{"comment": {"body": "Once approved, will make similar PR for vscode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#issuecomment-1031829828"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875145535", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875145731", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875152541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875377287", "body": ""}
{"comment": {"body": "We can get rid of these two lines here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#discussion_r801156305"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875377515", "body": ""}
{"comment": {"body": "We can get rid of the openApiGenerateVscode and openApiGenerateWeb tasks above?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#discussion_r801156479"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875381729", "body": ""}
{"comment": {"body": "This feels like something that should be in /shared ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#discussion_r801159843"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875385566", "body": ""}
{"comment": {"body": "It will be. I'll probably do this when I implement the VSCode side of things.\n\nThe only thing to figure out is the reference to AuthStore which is currently client specific.\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#discussion_r801162612"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875391583", "body": ""}
{"comment": {"body": "We will. I haven't fully moved vscode over yet.\r\nWill do that in a subsequent PR once the web refactor is approved so that we don't need to churn multiple clients at once.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#discussion_r801167297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875391667", "body": ""}
{"comment": {"body": "> We will. I haven't fully moved vscode over yet.\r\n> Will do that in a subsequent PR once the web refactor is approved so that we don't need to churn multiple clients at once.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#discussion_r801167357"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875401380", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875404474", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875406296", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875408605", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/264#pullrequestreview-875410329", "body": ""}
{"title": "GitHub app validation should include issues:read", "number": 2640, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2640", "body": "Warnings will be logged: \"Installation permissions are invalid\",\nwhich is useful for knowing which Orgs have not yet accepted the new permissions.\nUnlike #2572, the installation will not be uninstalled."}
{"title": "Require that installations have GitHub app issues:read permission", "number": 2641, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2641", "body": "Review, but don't merge until all PROD teams have accepted the latest set of permissions.\nValidate by checking for this message I Log:\nInstallation permissions are invalid\nLast remaining org, Clio:\njson\n{\n  \"id\": ********,\n  \"app_id\": 166219,\n  \"target_type\": \"Organization\",\n  \"target_id\": 30769,\n  \"account\": {\n    \"login\": \"clio\",\n    \"id\": 30769,\n    \"type\": \"Organization\"\n  },\n  \"permissions\": {\n    \"members\": \"read\",\n    \"metadata\": \"read\",\n    \"pull_requests\": \"write\"\n  },\n  \"created_at\": \"2022-07-26T22:02:29Z\",\n  \"updated_at\": \"2022-07-26T22:02:29Z\"\n}"}
{"comment": {"body": "Clio have still not accepted new permission.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2641#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2641#pullrequestreview-**********", "body": ""}
{"title": "Clearing generated point should also clear trusted points", "number": 2642, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2642", "body": "Right now the admin web button clears points that are not original;\nhowever this does not capture all of the possible generated points,\nbecause the trusted-original points are also generated.\nMy plan is to release recent sourcemark algorithm changes making sure\nthat most VSCode clients have upgraded, then clear the generated points."}
{"title": "Add clearUnreads operation", "number": 2643, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643", "body": "Just the API implementation here. Client code in subsequent PR"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076058903", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076091656", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076169128", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076172348", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076183958", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076187444", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076206658", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076261028", "body": ""}
{"comment": {"body": "Makes sense. There's an open question about how this'll perform for many, many unreads, but I think that's something we can optimize later if it's really an issue.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#discussion_r948316411"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076262337", "body": "Naming suggestion. Otherwise good!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2643#pullrequestreview-1076262823", "body": "Overall approach makes sense, thanks Pete"}
{"title": "Add content length to honeycomb", "number": 2644, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2644", "body": "[]=c_name&fields[]=c_service.name&span=525d31e7b48a389c"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2644#pullrequestreview-1076152178", "body": "thank you!"}
{"title": "Check if review comment body is null or blank", "number": 2645, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2645"}
{"title": "Limit pusher logs", "number": 2646, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2646"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2646#pullrequestreview-1076295697", "body": ""}
{"title": "Mark individual message as read", "number": 2647, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2647", "body": ""}
{"comment": {"body": "> What type of error handling do we have in the hub?\r\n> When the async fails, silent fail atm?\r\n\r\nYes not ideal. @benedict-jw should there be a failure mode for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2647#issuecomment-1218456149"}}
{"comment": {"body": "> Yes not ideal. @benedict-jw should there be a failure mode for this?\r\n\r\nRecapping in person chat \u2014\u00a0we fail silently in VSCode. It hasn't come up as an issue yet. \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2647#issuecomment-1218459618"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2647#pullrequestreview-1076384747", "body": "What type of error handling do we have in the hub?\nWhen the async fails, silent fail atm?"}
{"title": "Mark all as read", "number": 2648, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648", "body": "There's a pretty savage hack in this one. What we'd ideally like is a ContextMenu instance to pop up when the hamburger menu is left-clicked, but SwiftUI has no affordance for triggering a context menu programatically (it only works with a right-click). \nI explored a few options:\n1. Emulate a right-click on the view - turns out to be pretty tricky because SwiftUI views are not really views, so you have to dispatch the event to the global event system. I couldn't figure out how to make this work (tried NSEvent based APIs and NSApp.postEvent with no luck)\n2. Attaching an NSPopover or NSPopUpButtonCell to an AppKit bridge view contained within the LazyVStack is fraught with peril. SwiftUI seems to have a race-condition bug where NSViewRepresentable objects sometimes don't actually get attached to the window. \n3. The race-condition bug was solved by deferring the view capture until it gets attached to the window, but it can be dealloc'd at any point in the future so attaching to this view is still perilous. Instead, we attach the popover to the window view and then offset the popover to the correct location. \nThere's one downside to this, which is that an NSPopover on top of another NSPopover allows events to flow down to the NSPopover underneath. This means that when a user clicks on the section header menu, they can continue to scroll the content underneath, but the menu remains in place. Normal context menu behaviour is that the window underneath stops receiving events until the menu is dismissed. This can be achieved using the popUpContextMenu api, but the frame isn't adjustable. Additionally, context menus don't support custom layouts. \nIf this behaviour is a problem then we can explore some hacky window solutions for dropping scroll events or something"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#pullrequestreview-1078056501", "body": ""}
{"comment": {"body": "I still find it strange that these popovers are owned by the app delegate...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#discussion_r949635636"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#pullrequestreview-1078056755", "body": ""}
{"comment": {"body": "What else would own them?\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/bf4f38da-bf9d-4f99-8f99-6946f28b3763?message=ea942c95-e750-4381-8cce-bb4754d1fd4b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#discussion_r949635832"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#pullrequestreview-1078057276", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#pullrequestreview-1078057367", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#pullrequestreview-1078057737", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#pullrequestreview-1078060293", "body": ""}
{"comment": {"body": "So this is triggered when the window appears?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#discussion_r949638258"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#pullrequestreview-1078062207", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#pullrequestreview-1078063760", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#pullrequestreview-1078064093", "body": ""}
{"comment": {"body": "Not quite - at some point SwiftUI will do something to set the window property on the view. But sometimes it hits a layout race condition and fails to set the window correctly. Very odd", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2648#discussion_r949640802"}}
{"title": "Add PullRequestInfo UI", "number": 2649, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649", "body": "Hook up to sidebar click\nPullRequestInfoView houses a PullRequestBlocks list which renders a list of PullRequestBlockViews\nEntry to this UI hides behind the same prSidebar feature flag\n\nOutstanding work (to be handled in ensuing PRs):\n- [x] Rows in the sidebar need to have a selected state\n- [x] Calendar/most recent pull requests filter should be sorted first (date | discussions | changes)\n- [ ] Update open file listener logic to handle focusing into PR Info panel\n- [ ] Add overlay to PR Info CRUD operations to immediate client operations\n- [ ] Add pusher channel for pullRequestInfo/:pullRequestInfoId to get updates for PR Info events\n- [ ] PR links in discussions should link to the PrInfoView \nCan ship without:\n- [ ] Collapsing thread blocks \n- [ ] Sticky title/number on scroll\nDeferring:\n- [ ] Outdated/Resolved UI"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076417940", "body": ""}
{"comment": {"body": "Rename to `threadAggregates`? Was a bit confused why we were omitting threads and then re-adding it back.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#discussion_r948409231"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076419761", "body": ""}
{"comment": {"body": "It's the same pattern we use for the ThreadInfoAggregate, i.e. the messages are MessageAggregates but we still call them messages. I don't have a strong preference but I would prefer we are consistent on whichever we choose\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c8633a34-8bc9-468d-93f3-c1b04351722a?message=cbc601db-6495-42a7-b85d-4d51ca11f409).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#discussion_r948411010"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076421743", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076422534", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076435364", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076439316", "body": ""}
{"comment": {"body": "Is this the same as faCodeMerge?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#discussion_r948429985"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076441136", "body": ""}
{"comment": {"body": "Yes. AFAIK we can't inject fa icons as tab icons so we save them separately (@matthewjamesadam ??). We do the same thing with the Images.lock icon", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#discussion_r948431805"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076442879", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076449520", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076450043", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076450788", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076452222", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076456554", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076458989", "body": ""}
{"comment": {"body": "ThreadMessageCrudOperations?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#discussion_r948449980"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076461153", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2649#pullrequestreview-1076480569", "body": ""}
{"title": "Add install token cache", "number": 265, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265", "body": "Problem\nCalls to app install GQL APIs are making calls to the install access token API every time. We should cache those tokens and blow them when they're about to expire.\nWhat's changed\nIntroduced InstallationTokenCache to manage install tokens. This cache is not distributed so every service instance will have to load its own tokens. GitHub does not implement token families so this is ok.\nWhat's next\nUse Redis"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265#pullrequestreview-875163400", "body": ""}
{"comment": {"body": "Two things.\r\n1. How this grows over time and what sort of eviction policy we have to prevent memory issues.\r\n\r\nHave you looked into something like this?\r\nhttps://github.com/ReactiveCircus/cache4k\r\n\r\nAnyways, this is fine for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265#discussion_r800997058"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265#pullrequestreview-*********", "body": "With comments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265#pullrequestreview-*********", "body": ""}
{"comment": {"body": "I've looked at cache4k. It will definitely do the job but it's pretty heavy-weight for what this is. The token cache is _per-install_, meaning this will only grow to the number of orgs we have. Each one of these things is 2k max size, which accounts for the token and expiry timestamp. In the short term this is not a worry - 10k app installs means 20 megs of memory. When we own the world with 1M installs this will have to be modified.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265#discussion_r801017375"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265#pullrequestreview-*********", "body": ""}
{"comment": {"body": "At the very least, if we start creating a pattern of creating this little utility cache classes, I think we will have to consider a shared utility or using a 3rd party alternative cach4k or whatever.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265#discussion_r801019204"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265#pullrequestreview-*********", "body": ""}
{"comment": {"body": "\u2795 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/265#discussion_r801080682"}}
{"title": "Slack analysis improvements", "number": 2650, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2650", "body": "Register if message is part of a thrad."}
{"title": "Set PullRequestReviewModel.createdAt to review.submittedAt", "number": 2651, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2651"}
{"title": "Fix read-only faults when original source point is not in repo", "number": 2652, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2652", "body": "This was simply a mischaracterization, where we incorrectly treated a mark\nas fault instead of notInRepo. This specific case occurs when none of\nthe original points exist in the repo.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2652#pullrequestreview-1076494761", "body": ""}
{"title": "update ktor version", "number": 2653, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2653"}
{"title": "Return comment count for pull requests", "number": 2654, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2654", "body": "First pass at returning the comment counts with the getPullRequestsForCommits response."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2654#pullrequestreview-1076492617", "body": ""}
{"comment": {"body": "We can probably call these concurrently", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2654#discussion_r948488416"}}
{"title": "Move to native embedded postgres rather than x86 which was using emulation", "number": 2655, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2655", "body": "ation\nAs of 2.0.0 embedded postgres, they're providing apple silicon binaries for embedded postgres."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2655#pullrequestreview-1076504611", "body": ""}
{"title": "[TLC] Style selected PR row", "number": 2656, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2656", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2656#pullrequestreview-1076522594", "body": ""}
{"title": "Get log data for all", "number": 2657, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2657", "body": "If one's repo was on an older commit, we would not show PR data from that commit to repo's tip of main.\nUpdated git log to include --all to fetch all commits (that are available to local git)\nUpdated getFolder to handle missing filePath by recursively traversing filePath string\nIn screenshot, I'm on a very old commit where RelatedPullRequestPane does not exist.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2657#pullrequestreview-1076524546", "body": ""}
{"comment": {"body": "I suspect there might be clients using this method that expect this to find the immediate folder for the file.  I think I'd rather this go into a separate function that clearly states that this will find the closest parent folder that exists.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2657#discussion_r948521911"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2657#pullrequestreview-1076525837", "body": ""}
{"comment": {"body": "As of right now, only used here. I'll rename it to be clear", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2657#discussion_r948522877"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2657#pullrequestreview-1076532931", "body": ""}
{"comment": {"body": "Ah I didn't see that this was in a Git-specific file.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2657#discussion_r948528512"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2657#pullrequestreview-1076532970", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2657#pullrequestreview-1076645647", "body": "nice"}
{"title": "Fix pr view asset uploads", "number": 2658, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2658", "body": "Assets were not being uploaded at all for pr info view.\nAlso reducing code duplication"}
{"comment": {"body": "This is standardized across all the components that use message editors. \r\n\r\n1.  Webview Drag & Drop / Copy & Paste event triggers an asset upload request to the extension and a UUID is paired to the request.\r\n3. Extension invokes an authorized request to create an asset model via the AssetService and gets a s3 presigned url.\r\n4. Extension passes the presigned url and the corresponding asset upload UUID back to the webview.\r\n5. Webview now has enough information to know that the request with the UUID is complete, and it can now invoke a PUT request to s3 using the s3 presigned url via fetch.\r\n\r\nTESTING:\r\n<img width=\"666\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/185341184-542f3a70-31d1-4a6b-9431-3d727f899e45.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2658#issuecomment-1219106271"}}
{"comment": {"body": "FYI, there's also some weird bugs I've noticing with refreshing messages in general with that PrInfoView.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2658#issuecomment-1219121230"}}
{"comment": {"body": "> FYI, there's also some weird bugs I've noticing with refreshing messages in general with that PrInfoView.\r\n\r\nYeah it\u2019s not live updating new messages yet. They are being sent to the API though so new messages should be there once you refresh. The feature is still a work in progress/is still behind a feature flag \ud83d\ude42", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2658#issuecomment-1219678670"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2658#pullrequestreview-1076694805", "body": "This looks OK by me but @kaych wrote most of this, she is more familiar with it then I am"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2658#pullrequestreview-1076732207", "body": "Had some questions how this works  how does the code know which assets match to which message editor??"}
{"title": "Show user client versions in admin web", "number": 2659, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2659", "body": "remaining work in this PR:\n- [ ] client version store tests\n- [ ] add to the team members page\n- [x] refactor plugin\n- [x] test for write-through redis cache, and maybe refactor for reuse"}
{"title": "Improve code structure to support CI/CD (Part 1 - Static Site)", "number": 266, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/266", "body": "Created a new stack for handling cert issue requests\nAdded support for email cert validation (used in prod to create xyz.getunblocked.com certs when needed)\nCreated a separate stack for WAF (allows using a signle WAF for all static sites)\nRefactored the StaticSite stack to support creation of multiple sites\nChanged build config data structures as well as environment configs files to support above changes\nRenamed/recreated our existing static site to `dashboard.{ENV}.getunblocked.com\nAdded 'dashboard.getunblocked.com` to prod static site SSL cert and CloudFront (no DNS record for it yet)\n\nI will be removing cert creation code in DNS Stack in favor of the new ACM stack at a later time.\nAll changes have been deployed to both Dev and Prod."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/266#pullrequestreview-875311822", "body": ""}
{"title": "Create PullRequestInfo pusher channel stub", "number": 2660, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2660", "body": "This channel can be used to check whether /pullRequests/{pullRequestId}/info has updates. Can be used by the PR view to allow refreshing if any of the following are modified:\n\nPull request (i.e. title, description, etc)\nPull request top-level comments & reviews\nPull request threads"}
{"comment": {"body": "Merging this now. I'll fix up the logic in `pullRequestInfo` in a separate PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2660#issuecomment-1219818263"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2660#pullrequestreview-1077731999", "body": ""}
{"title": "Refactor admin web badge", "number": 2661, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2661"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2661#pullrequestreview-1077841974", "body": ""}
{"title": "[TLC] Adjust column widths", "number": 2662, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2662", "body": "Fixes the min-width of the deletion column to align the rows\n\n\n\nThis does mean that for diffs with smaller digit diffs that the spacing would seem more wider than necessary but I think this is good enough for the majority of cases -- we'll need to revisit the logic here soon\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2662#pullrequestreview-1078154963", "body": ""}
{"title": "Support creating message in PR View with Overlay", "number": 2663, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663", "body": "Add overlay support for creating message."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#pullrequestreview-1078001497", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#pullrequestreview-1078011303", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#pullrequestreview-1078013383", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#pullrequestreview-1078094942", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#pullrequestreview-1078129371", "body": ""}
{"comment": {"body": "This should be imported via an @-reference instead of relative paths...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#discussion_r949690990"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#pullrequestreview-1078130413", "body": ""}
{"comment": {"body": "Won't this double-send invites?  We're also calling this within createLocalMessage, which is called below?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#discussion_r949691856"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#pullrequestreview-1079062691", "body": ""}
{"comment": {"body": "Test @matthewjamesadam\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/fb26e44b-1a39-437d-a877-2fba31dca647?message=7887d3c0-5cf8-43c6-a5c2-68eca95e6165).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2663#discussion_r950362358"}}
{"title": "Showing the upgrade window shouldn't depend on successful download", "number": 2664, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2664", "body": "The download will be retried again when the user clicks on the upgrade button. This first download attempt is more of a convenience feature so that upgrading feels snappy when the dialog is presented - but if it fails we have a fallback (show spinner on upgrade button to signal work is being done)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2664#pullrequestreview-1077941348", "body": ""}
{"title": "Fix post-install app re-launch delay", "number": 2665, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2665", "body": "There may be more to this issue. Strongly suspect a failure to write the hub app is at fault, which might cause the existing application to crash. We'll try to repro and follow up in another PR"}
{"title": "Address serious issues with Honeycomb overages", "number": 2666, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2666", "body": "We are several million events over our quota.\nOne more month of this and they will suspend our account.\nThe idiomatic way of doing this for opentelemetry is via samplers.\nSince all of the existing opentelemetry samplers do not meet our needs, wrote my own that also uses baggage information to determine if something should be sampled.\nWe are using baggages because they are the paradigm for inheriting attributes between nested spans.\nThe flow in a nutshell:\n1. At top level we provide a rule based sampler that does not sample for deepcheck, shallowcheck apis.\n2. On top level ktor span creation, we create a baggage context that holds all http attributes."}
{"title": "WIP: Test jar diff", "number": 2667, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2667"}
{"title": "Introduce redis-backed write-through cache", "number": 2668, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2668"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2668#pullrequestreview-**********", "body": "handsome"}
{"title": "Show user client versions in admin web", "number": 2669, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2669", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2669#pullrequestreview-1078127624", "body": ""}
{"comment": {"body": "If a customer has multiple clients or multiple machines then this will clobber", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2669#discussion_r949689693"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2669#pullrequestreview-1078128758", "body": ""}
{"comment": {"body": "yup, I know. unavoidable unless we have some client machine identifier.\r\n\r\nbut also, I'm not sure how much we care?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2669#discussion_r949690583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2669#pullrequestreview-1078234746", "body": ""}
{"comment": {"body": "@pwerry\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c0e901fe-4536-44d7-95b4-f71fba6bdfcc?message=d1d0b61d-af79-4718-8302-311cbccb7c49).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2669#discussion_r949770195"}}
{"title": "Add PullRequest graphql query", "number": 267, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/267", "body": "Adds the ability to query the GitHub GraphQL api for a single pull request's review comment threads.  \nThe plan is to implement a batched versioned of this query so that we can grab review comment threads for multiple PRs more efficiently (to come in a separate PR). \nHowever, the API has page size limitations so this query will be called if individual PRs in the batch results have too many review threads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/267#pullrequestreview-875336850", "body": ""}
{"comment": {"body": "fyi, this is why I hate graphql. :)\r\nHaving to explicitly specify this shit sucks.\r\nGlad we're not using it for our stuff.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/267#discussion_r801124950"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/267#pullrequestreview-875337020", "body": ""}
{"title": "Fix Branch Issue", "number": 2670, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2670", "body": ".splice(-1) was only taking the last element...\nI had this fixed in my original branch. Not sure how this got in as is.\nAlso added extra check for empty strings."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2670#pullrequestreview-1078058385", "body": ""}
{"title": "PullRequestInfo pusher channel also checks comments, reviews, and threads modifiedAt", "number": 2671, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2671"}
{"title": "Adds unread only filter to hub", "number": 2672, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078102356", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078103951", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078107264", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078107762", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078111772", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078113560", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078117114", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078124888", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078139421", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078139848", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078163049", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2672#pullrequestreview-1078219799", "body": ""}
{"title": "Add PullRequest api model property to signal when a PR is awaiting ingestion", "number": 2673, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2673", "body": "If a pull request is not yet fully ingested, then this flag will be true. This will allow clients to show a loading state for a PR while awaiting ingestion.\nAt the same time, if the backend returns a PR where this flag is true, it will trigger  prioritized ingestion for this PR so that it can be ingested immediately. Once done, the PR's push channel will fire and clients can refresh to dismiss the loading state."}
{"comment": {"body": "@benedict-jw Need to think about a loading states in both the sidebar & TLC PR View for when a PullRequest has been ingested but not all its comments.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2673#issuecomment-1222941866"}}
{"comment": {"body": "> @benedict-jw Need to think about a loading states in both the sidebar & TLC PR View for when a PullRequest has been ingested but not all its comments.\r\n\r\nI added this for the sidebar: https://chapter2global.slack.com/archives/C03T4CR3HBJ/p1660842357988709\r\n\r\nFor the conversation view, what we talked about was showing the title, description, and then underneath a loading indicator. I'll draw it up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2673#issuecomment-1222975925"}}
{"comment": {"body": "Here it is for the conversation view. Below the description, lets just add a spinner with some supplementary text.\r\n<img width=\"489\" alt=\"CleanShot 2022-08-22 at 16 37 13@2x\" src=\"https://user-images.githubusercontent.com/13353189/186038105-32f28e74-a3b9-4e36-a9a0-20871839091c.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2673#issuecomment-1223331007"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2673#pullrequestreview-1079367364", "body": ""}
{"comment": {"body": "Not required, so `null` here is the same as `false` (i.e. PR is ingested)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2673#discussion_r950566598"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2673#pullrequestreview-1081098643", "body": ""}
{"title": "Sidebar resizer bar updates", "number": 2674, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674", "body": "Resizer bars now push adjacent bars when resizing, up to the minimum panel size\nReorganize the code a bit -- extract the calculating bits into a separate file\nTests\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674#pullrequestreview-1078126523", "body": ""}
{"comment": {"body": "I figured a single fixed minimum section height was fine for now...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674#discussion_r949688789"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674#pullrequestreview-1078274255", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674#pullrequestreview-1079032290", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674#pullrequestreview-1079062321", "body": ""}
{"comment": {"body": "SectionAboveTop is all the sections above the current index & sectionBelowBottom is all the sections below the current index?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674#discussion_r950362034"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674#pullrequestreview-1079067389", "body": ""}
{"comment": {"body": "`sectionAboveTop` is the y-coordinate of the top of the section above the bar, `sectionBelowBottom` is the y-coordinate of the bottom of the section below", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674#discussion_r950365707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2674#pullrequestreview-1079406049", "body": ""}
{"title": "Additional commands for PR Overlay", "number": 2675, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2675", "body": "Setup Delete and Update"}
{"comment": {"body": "Delete works as expected.\r\n\r\nUpdate is working on the data side. UI side needs some work to update.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2675#issuecomment-1220090594"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2675#pullrequestreview-1078133697", "body": ""}
{"title": "Use redis write-through cache for ClientVersionMetrics", "number": 2676, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2676", "body": "This is not working right and I have no idea why. All we get is a trace with:\nERR syntax error\nFull trace:\nunblocked-apiservice-1           | 00:04:27 | ERROR | c.n.a.p.ClientVersionMetrics: Failed to record client version metric\nunblocked-apiservice-1           | { http.req.route=/api/teams/:id/threads/recommended, http.req.query=repoIds=aa57c947-b8a9-4078-9131-a5bff86e2918repoIds=fb06ca32-cdf0-4bcc-9e08-c8c675768235limit=11, requestId=17729abad3d43167, http.req.path=/api/teams/c9c47779-235f-4ca9-aefb-96758fa96650/threads/recommended, http.req.method=GET, http.req.header.User-Agent=node-fetch/1.0 (+) }\nunblocked-apiservice-1           | io.lettuce.core.RedisCommandExecutionException: ERR syntax error\nunblocked-apiservice-1           |  at io.lettuce.core.internal.ExceptionFactory.createExecutionException(ExceptionFactory.java:147)\nunblocked-apiservice-1           |  at io.lettuce.core.internal.ExceptionFactory.createExecutionException(ExceptionFactory.java:116)\nunblocked-apiservice-1           |  at io.lettuce.core.RedisPublisher$SubscriptionCommand.doOnComplete(RedisPublisher.java:757)\nunblocked-apiservice-1           |  at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:65)\nunblocked-apiservice-1           |  at io.lettuce.core.protocol.CommandHandler.complete(CommandHandler.java:747)\nunblocked-apiservice-1           |  at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:682)\nunblocked-apiservice-1           |  at io.lettuce.core.protocol.CommandHandler.channelRead(CommandHandler.java:599)\nunblocked-apiservice-1           |  at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)\nunblocked-apiservice-1           |  at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)\nunblocked-apiservice-1           |  at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)\nunblocked-apiservice-1           |  at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\nunblocked-apiservice-1           |  at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)\nunblocked-apiservice-1           |  at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)\nunblocked-apiservice-1           |  at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\nunblocked-apiservice-1           |  at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\nunblocked-apiservice-1           |  at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:722)\nunblocked-apiservice-1           |  at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:658)\nunblocked-apiservice-1           |  at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:584)\nunblocked-apiservice-1           |  at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:496)\nunblocked-apiservice-1           |  at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\nunblocked-apiservice-1           |  at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\nunblocked-apiservice-1           |  at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\nunblocked-apiservice-1           |  at java.base/java.lang.Thread.run(Thread.java:833)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2676#pullrequestreview-1079398529", "body": ""}
{"title": "Add sticky header on scroll", "number": 2677, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677", "body": ""}
{"comment": {"body": "It looks like the font styling changes on the sticky header, which looks kind of odd. Is that intentional?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#issuecomment-1220125100"}}
{"comment": {"body": "![CleanShot 2022-08-19 at 10 04 05](https://user-images.githubusercontent.com/13431372/185672143-bd38fd60-056f-4494-9582-8c442b47a818.gif)\r\nupdated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#issuecomment-1220906311"}}
{"comment": {"body": "![CleanShot 2022-08-19 at 11 45 45](https://user-images.githubusercontent.com/13431372/185686632-a162536c-122c-462f-b398-1711e47d827a.gif)\r\n\r\nupdated ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#issuecomment-1220995213"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#pullrequestreview-1079319428", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#pullrequestreview-1079320389", "body": ""}
{"comment": {"body": "No longer need `setShowEditor(false)`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#discussion_r950533965"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#pullrequestreview-1079321234", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#pullrequestreview-1079334924", "body": ""}
{"comment": {"body": "this flag is for the post comment editor. the editing editor is handled internally within the MessageView and that's what the resolve fn is ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#discussion_r950543968"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#pullrequestreview-1079335187", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#pullrequestreview-1079336093", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#pullrequestreview-1079336261", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2677#pullrequestreview-1079340536", "body": ""}
{"title": "Test for ClientVersionStore and better pill rendering", "number": 2678, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2678"}
{"title": "Add client version metrics to team members page", "number": 2679, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2679"}
{"title": "Address comments from previous PR", "number": 2680, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2680", "body": "Addresses comments from https://github.com/NextChapterSoftware/unblocked/pull/2663"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2680#pullrequestreview-1079031191", "body": ""}
{"title": "Add mention indicators", "number": 2681, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2681", "body": "\nAlso fixes the hamburger menu rendering issue"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2681#pullrequestreview-1079058928", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2681#pullrequestreview-1079213439", "body": ""}
{"title": "Return immediately from decorateThreads if threadIds is empty", "number": 2682, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2682", "body": "Small optimization to avoid a bunch of unnecessary DB hits if threadIds passed is empty"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2682#pullrequestreview-1079127136", "body": ""}
{"title": "Fix for ClientVersionModel failing to update to newer version", "number": 2683, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2683", "body": "Add failing test\nFix for ClientVersionModel failing to update to newer version"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2683#pullrequestreview-1079162744", "body": ""}
{"title": "Open VSCode to foregroudn", "number": 2684, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2684", "body": "When opening discussion from hub, VSCode will now foreground workspace instance\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2684#pullrequestreview-1079312679", "body": ""}
{"title": "Add unread disclosures to team header in hub", "number": 2685, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2685", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2685#pullrequestreview-1079318108", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2685#pullrequestreview-1079330717", "body": ""}
{"title": "Only send click event to a single vscode instance", "number": 2686, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2686"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2686#pullrequestreview-1079311027", "body": ""}
{"title": "PullRequestInfo should include reviews without threads", "number": 2687, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2687"}
{"title": "Allow API and admin service to send priority and standard PR ingestion events", "number": 2688, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2688", "body": "I'm not sure if anything else needs to be done to enable this?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2688#pullrequestreview-1079359147", "body": ""}
{"title": "Update pr info loading state", "number": 2689, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2689", "body": "When clicking on the same PR in the sidebar, PR View would show loading state and never update.\nFix was to add onUpdate() to be called on OpenPullRequestInfoCommand"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2689#pullrequestreview-1079368819", "body": ""}
{"comment": {"body": "Main change.\r\nAdds onUpdate to trigger loading state or loaded state if already loaded", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2689#discussion_r950567661"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2689#pullrequestreview-1080861934", "body": ""}
{"title": "App install API", "number": 269, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/269", "body": "Preamble\nSee: \nProblem\nAfter the user auth flow, the client must understand whether it needs to push the user to install the GitHub App on an org. 4 states are possible:\n\nThe user does not belong to any orgs with the app installed\nThe user belongs to an org with the app installed, but not for the specific org they're interested in\nThe app is installed for a particular org the user is interested in, but it lacks permissions for a repo they're interested in\nThe app is installed for a particular org and has permission to the repo the user is interested in\n\nProposal\nStates (2)-(4) rely on the client passing a repoUrl to derive the org and repo. Unfortunately, we don't have control over the repos that the user gives permission to, so we will have to perform an extra bit of work to validate whether the app has access to the repo they're interested in after the installation completes, and possibly hint to the user that they may have to correct the error with a link to the app configuration page. \nTo facilitate these flows we need a single API /install/status/{provider} that accepts an optional repoUrl and returns an InstallState object letting the client know where the user is in the install flow, either generally or for a particular org/repo. \nInstall states are:\n[notInstalled, installed, configured]\nBehaviour if the repoUrl parameter is included:\n- if not installed for org, return notInstalled with org install url\n- if installed for org but not configured for repo, return installed with app config url\n- if installed for org and configured for repo, return configured with app config url\nBehaviour if repoUrl parameter is omitted:\n- if user has no installed orgs, return notInstalled with personalized install url\n- if user has installed org but no configured repos (not sure if possible?), return installed with personalized install url\n- if user has installed org with configured repo(s), return configured with personalized install url"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/269#pullrequestreview-875401311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/269#pullrequestreview-875402646", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/269#pullrequestreview-875407790", "body": ""}
{"title": "Persist only the biggest product number when there are multiple concurrent clients", "number": 2690, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2690", "body": "Changes\n- Persist only the biggest product number when there are multiple concurrent clients\n- Write through cache is updated with stored value\nProblem\n- A user has two VSCode extension clients, likely two VSCode workspaces, open at the same time.\n  The requests from each client are clobbering each other constantly.\n  This change reduces the DB thrashing.\n  "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2690#pullrequestreview-1079372731", "body": ""}
{"title": "Revert \"PullRequestInfo should include reviews without threads (#2687)\"", "number": 2691, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2691", "body": "This reverts commit dfc4626e40d565522a37ebf14a392efbf03b2708."}
{"title": "Sync PR Sidebar sort across sidebars", "number": 2692, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2692", "body": "Sort wasn't syncing properly across explorer sidebar and unblocked sidebar.\nRefactored sort into its own stream & removed sort state within react component."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2692#pullrequestreview-1081431761", "body": ""}
{"title": "Fix issue where logs were taking down api service", "number": 2693, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693", "body": "Best-effort truncation of log json blobs to 5k."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079403878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079404183", "body": "impressive"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079407272", "body": ""}
{"comment": {"body": "So anything that results in us going over the maximum is removed, not (itself) truncated?  And once we hit the limit, anything after that will be removed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#discussion_r950596397"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079408076", "body": ""}
{"comment": {"body": "The main culprit here will be strings (ie, at the end of the day, string property values will be the things that make up most of the large JSON blobs).  I wonder if we should clip JSON property strings to a maximum size, in cases with large strings this would allow us to see some data from more parts of the tree, I think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#discussion_r950597050"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079419421", "body": ""}
{"comment": {"body": "Yeah, that's actually a good hybrid approach. Clipping strings is a good idea. I'll do that in a follow up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#discussion_r950606045"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079419477", "body": ""}
{"comment": {"body": "Correct.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#discussion_r950606076"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079419500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079427520", "body": ""}
{"comment": {"body": "fwiw: the case that I saw a massive payload was not long strings, but a massive array of objects that contained short strings. Not saying long string aren't a problem\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#discussion_r950612721"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079430605", "body": ""}
{"comment": {"body": "Huh that's somewhat unexpected -- what was the array data?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#discussion_r950615581"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#pullrequestreview-1079447431", "body": ""}
{"comment": {"body": "source points, on a file with massive history\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b2d5bf33-e679-45ec-91a4-e58b5323f727?message=e6f2c076-1262-4278-9f31-e1839e1ad590).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2693#discussion_r950627481"}}
{"title": "[TLC] Discussion pr links should link to UB PR View", "number": 2694, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2694", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2694#pullrequestreview-1079405467", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2694#pullrequestreview-1079405654", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2694#pullrequestreview-1079416870", "body": ""}
{"title": "API Backwards Compatibility checks", "number": 2695, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2695", "body": "In openapitools we trust"}
{"comment": {"body": "What does this ultimately do?  Fail a test if the spec is incompatible, and the expectation is that you'll merge with admin privileges if you know what you're doing?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2695#issuecomment-1221178451"}}
{"comment": {"body": "> What does this ultimately do?  Fail a test if the spec is incompatible, and the expectation is that you'll merge with admin privileges if you know what you're doing?\n\nCorrect", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2695#issuecomment-1221178849"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2695#pullrequestreview-1079427691", "body": ""}
{"title": "Add thread level pr link to discussion UI", "number": 2696, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2696", "body": "\n\n"}
{"comment": {"body": "Update per Dennis' feedback:\r\nTop level PR # link goes to the PR on the web and add a menu item to go to the PR Info view: \r\n<img width=\"573\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/185997083-68408d33-5d51-4bb3-84a1-a8b816960369.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2696#issuecomment-1222782943"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2696#pullrequestreview-1079719794", "body": ""}
{"comment": {"body": "Curious why we need to pass in both the Thread and ThreadInfoAggregate?  `threadInfo.thread` gives us the thread, doesn't it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2696#discussion_r950903356"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2696#pullrequestreview-1079720063", "body": ""}
{"comment": {"body": "Just a nit, up to you if you want to change it or not, but this brings up something Jeff and I were discussing last week -- memoizing trivial things like this is actually more expensive then just using the values.  There is quite a bit of fixed overhead in memoization, plus some code complexity.  We only really need to memoize something if the calculation is very expensive, or if it's expensive and we're expecting to render a lot.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2696#discussion_r950903724"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2696#pullrequestreview-1079720102", "body": ""}
{"title": "PullRequestInfo should include reviews without threads", "number": 2697, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2697", "body": "Lets try this again"}
{"title": "Show warning in VSCode when update available", "number": 2698, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698", "body": "In this video I mimic the update path:\n- Start with an older build (********)\n- Install a newer build (********) on the command line, the same way the hub app does it\n- Reboot the hub app (as happens after an upgrade)\n- VSCode notes the new build, shows a popup offering to update\n- Click on Reload to reload the workspace\n\nHow this works:\n- Every CI build will now add the product number into the package.json that we ship with the extension\n- Whenever VSCode notices that the hub app has rebooted (ie when we reconnect to the hub app), we will check to see if an update is required:\n- Look through all installed (and non-obsoleted) extensions.  Find ours.  Parse they product number out of package.json.\n- If the product number is different then the running app, that means an update is pending.\nThis basically mimics how VSCode works internally.  The downside is that if VSCode changes how it manages extensions, we might break, but the code defaults to not warning, so in the worst case we will just miss popups."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1079436913", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1079437258", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1079439085", "body": ""}
{"comment": {"body": "Embed the product number into the package.json for distribution", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r950621599"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080760481", "body": ""}
{"comment": {"body": "Not necessary for this PR but it may be useful in future for this be raised for both connect & disconnect.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951620318"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080762364", "body": ""}
{"comment": {"body": "logging errors may be useful for debugging. Could help catch/explain failures if VScode changes how .obsolete works.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951621338"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080762799", "body": ""}
{"comment": {"body": "Same here. Log errors.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951621574"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080766499", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080769818", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080781133", "body": ""}
{"comment": {"body": "Doesn't look like there's a way to reload *all* windows.\r\nUser would need to reload for each open window :(", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951633112"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080781351", "body": ""}
{"comment": {"body": "https://github.com/microsoft/vscode/issues/97175", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951633237"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080782663", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080786756", "body": ""}
{"comment": {"body": "So after hub/installer installs new extension, this extensionPath === new extension's path, even before window refersh?\r\nAnd previously installed version is immediately sent to `.obsolete`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951636602"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080802559", "body": ""}
{"comment": {"body": "We're logging all of the parent code failures now, so this should be handled.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951645311"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080803453", "body": ""}
{"comment": {"body": "Yes this is unique to each workspace.  That is expected.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951645753"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080849614", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080920595", "body": ""}
{"comment": {"body": "@kaych testing\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2873eafb-3ad8-4a05-a6d5-73a24a75ddff?message=4c1be402-e6e0-449d-8c7f-fb02eeec70e2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951728523"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080922381", "body": ""}
{"comment": {"body": "@kaych testing\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/5773ca13-edb6-46b4-90e5-00c5e0ce47e6?message=c9a90b86-fee6-478b-a1f1-b6806bb01426).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951730103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080928490", "body": ""}
{"comment": {"body": "No, `currentProductNumber` will always be the currently-running build number.  extensionPath is the currently-running extension's path.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951735613"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080950328", "body": ""}
{"comment": {"body": "Done.  We still eat failures on `fsPromises.read` as the file is expected to often not exist (and this case is equivalent to there being no obsoleted versions).  Failures on parse (which are unexpected) will float to the top and be logged.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951755287"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1080973436", "body": ""}
{"comment": {"body": "I added a few comments that will hopefully clarify what this is doing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#discussion_r951775851"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2698#pullrequestreview-1081007645", "body": ""}
{"title": "[BREAKS API ON MAIN] Support DISMISSED PR State", "number": 2699, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2699", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2699#pullrequestreview-1079702602", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2699#pullrequestreview-1079782770", "body": ""}
{"comment": {"body": "@davidkwlam @pwerry this is failing the new API compatibility test. Technically, the test is doing the right thing, since a legacy client could fail on an unexpected enum type like this.\r\n\r\nHowever, in this case, the enum is not used by any client, so we can safely break the API spec. If you agree, then how do we actually achieve this? I could force merge the change, but it's going to fail in _main_ CI post-merge.\r\n\r\nI think we did this in the past by adding some predefined string to the commit message like:\r\n```\r\n[This breaks API compatibility on main]\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2699#discussion_r950953216"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2699#pullrequestreview-1079811866", "body": ""}
{"comment": {"body": "Fixed, see https://github.com/NextChapterSoftware/unblocked/pull/2699/commits/a46ef3f88da8c40b246650dd79c6a4851b14b735.\r\n\r\nPlease review...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2699#discussion_r950970488"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2699#pullrequestreview-1079814562", "body": ""}
{"comment": {"body": "Yup that looks good to me!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2699#discussion_r950975010"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2699#pullrequestreview-1079814640", "body": ""}
{"title": "User Icon and icon stack with theme-ui", "number": 27, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27", "body": ""}
{"comment": {"body": "Proof of concept. Closing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27#issuecomment-1013384868"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27#pullrequestreview-848360502", "body": ""}
{"comment": {"body": "It looks like we have constants for other items. Can we add them for sizes potentially?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27#discussion_r781553962"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27#pullrequestreview-848361014", "body": ""}
{"comment": {"body": "Same for border things.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27#discussion_r781554090"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27#pullrequestreview-848367641", "body": ""}
{"comment": {"body": "It seems like for certain variables we reference the whole object path such as `layout.flex.alignCenterJustifyCenter`\r\n\r\nShould this be `colors.spaceCadet-70`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27#discussion_r781555640"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27#pullrequestreview-848370987", "body": ""}
