import logging
from typing import List

from langchain_community.embeddings import OpenAIEmbeddings, HuggingFaceInstructEmbeddings
from tqdm import tqdm
from sklearn.feature_extraction.text import TfidfVectorizer

from integration_utils.message import MessageInterface

from cluster_processor_image.cluster_types import EmbeddedDoc
from cluster_processor_image.openai_constants import OPENAI_API_KEY
from cluster_processor_image.embeddings_constants import EMBEDDINGS_MIN_MESSAGE_LEN, EMBEDDINGS_MAX_MESSAGE_LEN


class EmbeddingsProcessor:
    __min_size: int
    __max_size: int

    def __init__(self, min_size: int = EMBEDDINGS_MIN_MESSAGE_LEN, max_size: int = EMBEDDINGS_MAX_MESSAGE_LEN):
        self.__min_size = min_size
        self.__max_size = max_size

    def is_embed_message(self, message: MessageInterface) -> bool:
        return self.__min_size < len(message.get_text()) < self.__max_size

    def get_embeddings(self, messages: [MessageInterface]) -> List[EmbeddedDoc]:
        pass


class TfidfEmbeddingsProcessor(EmbeddingsProcessor):
    def __init__(self, min_size: int = EMBEDDINGS_MIN_MESSAGE_LEN, max_size: int = EMBEDDINGS_MAX_MESSAGE_LEN):
        super().__init__(min_size=min_size, max_size=max_size)

    def get_embeddings(self, messages: [MessageInterface]) -> List[EmbeddedDoc]:
        vectorizer = TfidfVectorizer(analyzer="word", stop_words="english")
        messages = [message for message in messages if self.is_embed_message(message)]
        docs = [message.get_text() for message in messages]
        # matrix of per doc embeddings
        # use matrix.shape to show the full matrix size
        matrix = vectorizer.fit_transform(raw_documents=docs)
        embeddings = []
        for idx, message in enumerate(messages):
            embeddings.append(EmbeddedDoc(message=message, embedding=matrix[idx,].toarray().tolist()[0]))
        return embeddings


class OpenAIEmbeddingsProcessor(EmbeddingsProcessor):
    def __init__(self, min_size: int = EMBEDDINGS_MIN_MESSAGE_LEN, max_size: int = EMBEDDINGS_MAX_MESSAGE_LEN):
        super().__init__(min_size=min_size, max_size=max_size)

    def get_embeddings(self, messages: [MessageInterface]) -> List[EmbeddedDoc]:
        messages = [message for message in messages if self.is_embed_message(message)]

        embedder = OpenAIEmbeddings(chunk_size=5000, openai_api_key=OPENAI_API_KEY)
        embeddings: List[EmbeddedDoc] = []
        for message in tqdm(messages, desc="Generating OpenAI embeddings..."):
            try:
                embedding = embedder.embed_query(message.get_text())
                embeddings.append(EmbeddedDoc(message=message, embedding=embedding))
            except Exception as e:
                logging.exception(e)

        return embeddings


class InstructorEmbeddingsProcessor(EmbeddingsProcessor):
    def get_embeddings(self, messages: [MessageInterface]) -> List[EmbeddedDoc]:
        messages = [message for message in messages if self.is_embed_message(message)]

        instruct_embeddings = HuggingFaceInstructEmbeddings(
            query_instruction="Represent the stackoverflow question for retrieval: ",
            embed_instruction="Represent the stackoverflow document for retrieval: ",
        )
        embeddings: List[EmbeddedDoc] = []
        for message in tqdm(messages, desc="Generating Instructor embeddings..."):
            try:
                embedding = instruct_embeddings.embed_documents([message.get_text()])[0]
                embeddings.append(EmbeddedDoc(message=message, embedding=embedding))
            except Exception as e:
                logging.exception(e)

        return embeddings


class InstructorSourceCodeEmbeddingsProcessor(EmbeddingsProcessor):
    def __init__(self, min_size: int = EMBEDDINGS_MIN_MESSAGE_LEN, max_size: int = EMBEDDINGS_MAX_MESSAGE_LEN):
        super().__init__(min_size=min_size, max_size=max_size)

    def get_embeddings(self, messages: [MessageInterface]) -> List[EmbeddedDoc]:
        messages = [message for message in messages if self.is_embed_message(message)]

        instruct_embeddings = HuggingFaceInstructEmbeddings(
            query_instruction="Represent the source code question for retrieval: ",
            embed_instruction="Represent the source code document for retrieval: ",
        )
        embeddings: List[EmbeddedDoc] = []
        for message in tqdm(messages, desc="Generating Instructor embeddings..."):
            try:
                embedding = instruct_embeddings.embed_documents([message.get_text()])[0]
                embeddings.append(EmbeddedDoc(message=message, embedding=embedding))
            except Exception as e:
                logging.exception(e)

        return embeddings
