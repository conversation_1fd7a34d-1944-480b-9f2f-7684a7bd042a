{"comment": {"body": "How are these primitives different from a div?\r\nIt exposes css directly `ml` which can be nice at times but if we're still adding styles within the sx such as cursor, would it make sense to have it all in the same place?\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/27#discussion_r781556978"}}
{"title": "Setup mock date for vscode", "number": 270, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/270", "body": "Add date mocking to resolve CI for stories that include relative dates."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/270#pullrequestreview-875391581", "body": ""}
{"title": "Removes over-eager wait-for-auth state", "number": 2700, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2700", "body": "One liner. The issue was that auth state restoration wasn't happening when the token was expired, which is pretty much guaranteed every time the app starts after being asleep for a while. \nThere's no need to do this expiry check here because we're already looking for refresh token expiry. Token refresh will happen as soon as the auth store is initialized."}
{"title": "Fallback plan", "number": 2701, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2701", "body": "Not ideal, but can be merged temporarily while we figure out the correct solution so local envs don't get wrecked."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2701#pullrequestreview-1080752894", "body": ""}
{"title": "Add exception handler to pusher service", "number": 2702, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2702", "body": "Generalize plugins across services and ensure PusherService has status pages plugin."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2702#pullrequestreview-1080967359", "body": "nice cleanup "}
{"title": "Open Unblocked explorer panels on extension installation", "number": 2703, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2703", "body": "On Extension installation, expand unblocked sidebars in the explorer sidebar."}
{"comment": {"body": "Two questions:\r\n1) The value is only relevant on startup, correct?  ie, we only pop open the panels on startup, when we have never done so before?  I'm a bit confused why this is structured as a stream if it's a value we only read once on startup, update once, and never touch or read again?  Shouldn't this just be a one-shot operation?\r\n2) Is there a problem with storing this in local storage?  I don't know if VSCode is likely to evict the data at any point.  If we uninstall or disable the extension is the local storage data destroyed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2703#issuecomment-1223505783"}}
{"comment": {"body": "> * The value is only relevant on startup, correct?  ie, we only pop open the panels on startup, when we have never done so before?  I'm a bit confused why this is structured as a stream if it's a value we only read once on startup, update once, and never touch or read again?  Shouldn't this just be a one-shot operation?\r\n> * Is there a problem with storing this in local storage?  I don't know if VSCode is likely to evict the data at any point.  If we uninstall or disable the extension is the local storage data destroyed?\r\n\r\n1. Made it too complicated initially... Will make it simpler.\r\n2. From what I can tell no... But this was basic testing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2703#issuecomment-1224333010"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2703#pullrequestreview-1081009368", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2703#pullrequestreview-1081056601", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2703#pullrequestreview-1081058601", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2703#pullrequestreview-1081117185", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2703#pullrequestreview-1081167641", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2703#pullrequestreview-1082942046", "body": ""}
{"title": "Add support for pusher channel", "number": 2704, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2704", "body": "Update to proper push channel"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2704#pullrequestreview-1080988690", "body": ""}
{"title": "Cleanup gradle files", "number": 2705, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2705"}
{"title": "Update spacing in discussion count", "number": 2706, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2706", "body": "\n\nAdd padding to of icon for alignment"}
{"comment": {"body": "@jeffrey-ng is the top image old, and bottom new?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2706#issuecomment-1222841630"}}
{"comment": {"body": "Feels like we need to add an extra top margin to the icon to offset the tail and align the 'box' with the numbers..?\r\n\r\nEdit: Oh that's what this is doing. Does it need more padding?? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2706#issuecomment-1222844603"}}
{"comment": {"body": "@benedict-jw Both new.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2706#issuecomment-1222854881"}}
{"comment": {"body": "Here's old:\r\n<img width=\"252\" alt=\"CleanShot 2022-08-22 at 12 48 10@2x\" src=\"https://user-images.githubusercontent.com/1553313/186006146-0cbd11e6-06c6-46b0-9e5b-cee46de1dbda.png\">\r\n<img width=\"221\" alt=\"CleanShot 2022-08-22 at 12 48 13@2x\" src=\"https://user-images.githubusercontent.com/1553313/186006154-1b64e3af-11fd-4a11-a0a8-4e52cfea29d1.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2706#issuecomment-1222855761"}}
{"comment": {"body": "I think a point of differentiation between our view and GitHub is their discussion icon is larger than the number, which makes it easier to align:\r\n<img width=\"73\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/186006252-ea0d0136-1fc8-4ba3-837e-b32b80db82c2.png\">\r\n\r\ncc: @benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2706#issuecomment-1222857831"}}
{"comment": {"body": "Here's the icon at 14x14 and number font size at 12px:\r\n<img width=\"363\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/186007196-248cdee7-f484-497a-813d-ecfdaf18a0c3.png\">\r\n\r\n11px:\r\n<img width=\"363\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/186007468-d069bbe0-fea1-48ea-9a78-23ca0f8e4518.png\">\r\n\r\nicon 12x12 font size 11px:\r\n<img width=\"357\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/186008897-e85c9eb8-0851-434b-9388-bf0a838d1a57.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2706#issuecomment-1222871540"}}
{"comment": {"body": "Icon at 14 I think is too big in comparison to the rest of the row. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2706#issuecomment-1222889819"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2706#pullrequestreview-1081053369", "body": ""}
{"title": "Only show notifications for messages not previously seen", "number": 2707, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2707", "body": "This changes the business logic for notification display.\nOld Logic\n\nThread was previously 'read' OR thread is new OR last message createdAt timestamp has changed\n\nNew Logic\n\nLast message in thread hasn't been seen before\n\nThe old logic allows for a scenario where a previously read thread that is marked as unread by the user will result in a notification."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2707#pullrequestreview-1081106649", "body": ""}
{"title": "Setting minimum TTL for CloudFront assets endpoint", "number": 2708, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2708"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2708#pullrequestreview-1081048736", "body": ""}
{"title": "Drop all pusher subscriptions on logout", "number": 2709, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2709", "body": "Should fix the pusher issue Dennis was seeing"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2709#pullrequestreview-1081075664", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2709#pullrequestreview-1081094099", "body": "cool"}
{"title": "Add batched PR review threads GraphQL query", "number": 271, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/271", "body": "This is a follow up to https://github.com/NextChapterSoftware/unblocked/pull/267. This query grabs the same data as the other query, except in batches of 100 PRs instead of just 1. \nThis batched query is more cost effective from a rate-limit perspective. However, if a PR has >20 review threads or has a review thread with >100 comments (both unlikely but possible), then we'll want to fall back to the single PR query to get the remaining review threads and/or comments.\nSee https://docs.github.com/en/graphql/overview/resource-limitations for an explanation of how much this query costs. In a nutshell, this query costs the equivalent of 21 api calls (compared to 100 if we were to run the single PR query for each of the 100 PRs)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/271#pullrequestreview-875400205", "body": ""}
{"comment": {"body": "Just get the first 20 review threads for each PR and hope that most PRs fall under this limit (we can adjust if it turns out to not be the case). If a PR has >20 review threads, then it is more efficient to query the PR individually to page the review threads.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/271#discussion_r801174089"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/271#pullrequestreview-875408329", "body": ""}
{"title": "Intercom update", "number": 2710, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2710", "body": "Fixing an issue where null teams cause a poor intercom experience"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2710#pullrequestreview-1081090184", "body": ""}
{"comment": {"body": "Should we do any additional verification for this teamID before doing this check?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2710#discussion_r951872777"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2710#pullrequestreview-1081090882", "body": ""}
{"title": "increase ec2 instance size limit", "number": 2711, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2711"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2711#pullrequestreview-1081093097", "body": ""}
{"title": "Create put viewPullRequest operation", "number": 2712, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#pullrequestreview-1081102925", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#pullrequestreview-1081107229", "body": ""}
{"comment": {"body": "I assume this was copied over from the ViewDiscussions API.\r\nWas wondering *why* this is necessary when we should be getting this info from headers?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#discussion_r951883970"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#pullrequestreview-1081107388", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#pullrequestreview-1081109392", "body": ""}
{"comment": {"body": "It is a header?\r\n\r\n```\r\n    productAgent:\r\n      in: header\r\n      name: X-Unblocked-Product-Agent\r\n      required: false\r\n      description: |\r\n        The client agent type. See: '#/components/schemas/AgentType'\r\n      schema:\r\n        type: string\r\n        minLength: 1\r\n        maxLength: 20\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#discussion_r951885408"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#pullrequestreview-1081110617", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#pullrequestreview-1081126653", "body": ""}
{"comment": {"body": "Yeah that one.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#discussion_r951897627"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2712#pullrequestreview-1081133798", "body": ""}
{"title": "Revert \"increase ec2 instance size limit\"", "number": 2713, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2713", "body": "Reverts NextChapterSoftware/unblocked#2711"}
{"title": "[TLC] Current file explorer fixes", "number": 2714, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2714", "body": "Fix opening the proper view column for the Current File Explorer pane \nFix selected state of the thread row in the Current File Explorer pane"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2714#pullrequestreview-1081169799", "body": ""}
{"title": "Fix banner padding issues", "number": 2715, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2715"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2715#pullrequestreview-1081158651", "body": ""}
{"title": "Record pull request view event", "number": 2716, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2716"}
{"title": "Add approvers to PrInfo model", "number": 2717, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717", "body": "Per revamped PR view design:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#pullrequestreview-1081173656", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#pullrequestreview-1081175936", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#pullrequestreview-1081193697", "body": ""}
{"comment": {"body": "1. make required\r\n2. why do we have both _participants_ and _approvers_? Aren't they both participants?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#discussion_r951944639"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#pullrequestreview-1082571643", "body": ""}
{"comment": {"body": "1. We can make it required \r\n2. Approvers is a subset of participants but we need to know who the approvers are for this UI.\r\n<img width=\"987\" alt=\"CleanShot 2022-08-23 at 10 20 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/186223993-517dadad-ae03-4597-8573-fd82468604ea.png\">\r\n.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#discussion_r952921255"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#pullrequestreview-1082581378", "body": ""}
{"comment": {"body": "So if a PR has no approvers, then it's an empty list returned? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#discussion_r952927944"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#pullrequestreview-1082583665", "body": ""}
{"comment": {"body": "> So if a PR has no approvers, then it's an empty list returned?\n\nyeah, makes sense\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/5d053b07-9315-4944-bca5-df14bb8a356b?message=72c826b4-9d96-4642-927e-8fbd67c8b047).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2717#discussion_r952929476"}}
{"title": "[BREAKS API ON MAIN but not really at least I think it doesnt] Use pullRequestId path parameter definition", "number": 2718, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718", "body": "pullRequestId is currently specified as a query parameter, but it's actually a path parameter for these operations:\n/teams/{teamId}/pullRequests/{pullRequestId}/threads\n/teams/{teamId}/pullRequests/{pullRequestId}\n/teams/{teamId}/pullRequests/{pullRequestId}/info\nThis change updates it to use the new pullRequestId path parameter:\npullRequestId:\n      in: path\n      name: pullRequestId\n      required: true\n      schema:\n        $ref: '#/components/schemas/ApiResourceId'\nBUT this breaks on main without the [This breaks API compatibility on main] commit message"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#pullrequestreview-1081196034", "body": ""}
{"comment": {"body": "This is not equivalent. Old is _query_, new is _path_.\r\n```yaml\r\n    pullRequestId:\r\n      in: path\r\n      name: pullRequestId\r\n      required: true\r\n      schema:\r\n        $ref: '#/components/schemas/ApiResourceId'\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#discussion_r951946422"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#pullrequestreview-1081197245", "body": ""}
{"comment": {"body": "Understood, but `query` is inaccurate since it's in the path no? I guess I'm not sure how code gen worked here.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/f6d87593-ab70-4d77-94d4-f10fb7b02e0a?message=0e96785e-ac30-437f-acd9-49cef1f53310).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#discussion_r951947319"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#pullrequestreview-1081198278", "body": ""}
{"comment": {"body": "These are the operation paths:\r\n\r\n`/teams/{teamId}/pullRequests/{pullRequestId}/threads`\r\n`/teams/{teamId}/pullRequests/{pullRequestId}`\r\n`/teams/{teamId}/pullRequests/{pullRequestId}/info`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#discussion_r951948124"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#pullrequestreview-1081502902", "body": ""}
{"comment": {"body": "The first one (`/teams/{teamId}/pullRequests/{pullRequestId}/threads`) has been released to the `Stable` channel for a long time. Assuming this is a breaking change for a second: that it breaks _main_ is minor compared with breaking all stable versions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#discussion_r952175654"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#pullrequestreview-1081505804", "body": ""}
{"comment": {"body": "So we might have gotten away with this because the Swift/Kotlin/TS generated code is identical either way. I guess we need to verify that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2718#discussion_r952177757"}}
{"title": "Include PullRequestBlocks that are approvals even if they don't have a message or threads", "number": 2719, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2719", "body": "The PR view will show approvals now, so we need to make sure we're returning blocks with an approval status even if they have no message or threads.\nThese blocks exist but the API currently does not return blocks if it has no message or threads."}
{"title": "Add custom ktlint rules for next chapter", "number": 272, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/272", "body": "This pr adds the ability for us to specify custom rules for ktlint.\nTo that end, I've added a rule forbidding the usage of Clock.System.now().\nWill add functionality to scope it to packages, but for now, this is fine.\nAlso cleaning up some plugin usage in our package, in particular plugin versioning."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/272#pullrequestreview-875403265", "body": "  "}
{"title": "Ensure we cache keys based off headers and cors request headers", "number": 2720, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2720", "body": "We have been noticing cors failures complaining about \"No 'Access-Control-Allow-Origin\".\nThis is only when there are cache hits.\nI have a nuclear option fix for this if this doesn't work!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2720#pullrequestreview-1081244281", "body": ""}
{"title": "lint ses stack", "number": 2721, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2721"}
{"title": "Add missing file states for active file manager", "number": 2722, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722", "body": "Empty file states for sidebar panels\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#pullrequestreview-1081302565", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#pullrequestreview-1081303010", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#pullrequestreview-1081304611", "body": ""}
{"comment": {"body": "I don't see the change for \"Unblocked: Related Pull Requests\" ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#discussion_r952025356"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#pullrequestreview-1081304669", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#pullrequestreview-1081305387", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#pullrequestreview-1081305769", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#pullrequestreview-1081309269", "body": ""}
{"comment": {"body": "That panel isn't in our codebase yet.\r\nWill add that right before pushing as we can't feature flag the explorer version of related pull requests panel.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#discussion_r952028932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#pullrequestreview-1081309340", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2722#pullrequestreview-1081309570", "body": ""}
{"title": "Show VSCode update popup up to 3 times on focus", "number": 2723, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2723", "body": "As it turns out, the informational message popup disappears after a short while.  So, what we'll do is:\n\nWhenever the workspace window gains focus, if there is a pending installation, show the popup.  Do this up to three times.\nWhenever a new installation is detected, reset the popup count.  If the window has focus, show the popup\nTweak the wording a little bit \n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2723#pullrequestreview-1081418110", "body": ""}
{"title": "[TLC] Update PR header/description", "number": 2724, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724", "body": "\n\nTODO: This is still missing the list of approvers - waiting on https://github.com/NextChapterSoftware/unblocked/pull/2717 (but shouldn't prevent this work from going in)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#pullrequestreview-1081419082", "body": ""}
{"comment": {"body": "If props is used here, no benefits of useMemo", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#discussion_r952117639"}}
{"comment": {"body": "Don't need to memoize this. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#discussion_r952117981"}}
{"comment": {"body": "Same here. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#discussion_r952118024"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#pullrequestreview-1082456125", "body": ""}
{"comment": {"body": "If props is used here, no need for memoization", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#discussion_r952825294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#pullrequestreview-1082458451", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#pullrequestreview-1082461243", "body": ""}
{"comment": {"body": "Padding seems a bit tight.\r\n\r\nCurrent:\r\n<img width=\"252\" alt=\"CleanShot 2022-08-23 at 09 07 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/186207740-05ebf243-4b76-4381-bd41-132bbf986067.png\">\r\n\r\nDesigns:\r\n<img width=\"422\" alt=\"CleanShot 2022-08-23 at 09 08 32@2x\" src=\"https://user-images.githubusercontent.com/1553313/186207803-a6f1b846-d922-404d-9d28-a9b477945eff.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#discussion_r952829533"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#pullrequestreview-1082571274", "body": ""}
{"comment": {"body": "This is a destructured props definition so it's not all the props.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#discussion_r952920999"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#pullrequestreview-1082571447", "body": ""}
{"comment": {"body": "^", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#discussion_r952921117"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#pullrequestreview-1082572590", "body": ""}
{"comment": {"body": "Updated with [5cafe99](https://github.com/NextChapterSoftware/unblocked/pull/2724/commits/5cafe99f694d404b4787a5372c739b5601d175ad)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#discussion_r952921913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#pullrequestreview-1085831008", "body": ""}
{"comment": {"body": "Testing ?\n\n--\n\nComment edited using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/ad3f32be-f8d2-4657-833b-342598a50521?message=e28b3d97-a578-4b11-97e1-f8b4facf408a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2724#discussion_r955232626"}}
{"title": "Add ability to trigger PR review ingestion for all repos in a team", "number": 2725, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2725", "body": "Adds logic to ingest just reviews for pull requests. Needed for the TLC work.\nWe could also just run the regular ingestion job for all PRs, but this is more efficient because we don't need to make requests to get comments or files (we've already ingested those)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2725#pullrequestreview-1082492952", "body": ""}
{"comment": {"body": "A little hacky but gets the job done for now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2725#discussion_r952856446"}}
{"title": "Introduce fetchThreads to get threads in bulk", "number": 2726, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2726", "body": "Affects both web extension when loading threads for many sourcemarks\n(eg: ),\nand also the vscode extension when loading threads for many sourcemarks.\nWeb extension make 130 getThread requests. Latency for the final request is 1.1 seconds.\n\nRelated:\n"}
{"comment": {"body": "How will this work with the thread push channels?  Will this return a last-modified that we can feed into each thread's push channel call?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2726#issuecomment-1224300146"}}
{"comment": {"body": "> How will this work with the thread push channels? Will this return a last-modified that we can feed into each thread's push channel call?\r\n\r\nYeah, I can do that simply.\r\n\r\nHowever, I wonder should we even need to do that @matthewjamesadam? Is it really necessary to subscribe in real-time to each of those threads, on the off chance that a user opens the threads AND something changed for those threads? If a user opens one of those threads, then I think it makes sense to subscribe to changes at that point. What do you think?\r\n\r\nThink of the `api/private.yml` case with 130 threads. A user will likely only click on a couple of those threads.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2726#issuecomment-1224366328"}}
{"comment": {"body": "> Yeah, I can do that simply.\r\n> \r\n> However, I wonder should we even need to do that @matthewjamesadam? Is it really necessary to subscribe in real-time to each of those threads, on the off chance that a user opens the threads AND something changed for those threads? If a user opens one of those threads, then I think it makes sense to subscribe to changes at that point. What do you think?\r\n> \r\n> Think of the `api/private.yml` case with 130 threads. A user will likely only click on a couple of those threads.\r\n\r\nI think the main case where it matters is thread archive/unarchive/deletion... I'm not sure how much we care about it right now to be honest.  This will require some refactoring either way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2726#issuecomment-1224390939"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2726#pullrequestreview-1082831882", "body": ""}
{"title": "Setup PR Metrics", "number": 2727, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2727", "body": "Send PR event when opening PR view."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2727#pullrequestreview-1082590849", "body": ""}
{"title": "Quick fix for poller-api looping for getThread API", "number": 2728, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2728", "body": "Will follow up with tests, and next steps.\nWant to get this in quick to stop the clients hammering the service.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2728#pullrequestreview-1082704120", "body": ""}
{"title": "Get video app building again", "number": 2729, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2729", "body": "Many of the classes were built as singletons, and the Hub app isn't using proper dependency injection. This refactor  arguably adds more tech debt because it allows for incomplete service singleton initialization. \nI'm taking this path now until we have a clearer view of the shared components."}
{"comment": {"body": "> Yeah I'd say some of these changes are definitely adding more tech debt.  It looks like we're trying to share/reuse code between the two apps in a way that I'm not sure makes sense -- how similar are the two apps?\n\nInitially I assumed we'd be sharing many components, but that's definitely not the case. The only \"shared\" components will end up being a few of the UI components that don't (or at least should not) be app specific. \n\nI'll spend some time untangling those dependencies once we get the app into people's hands to start dogfooding", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2729#issuecomment-1230690221"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2729#pullrequestreview-1082700484", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2729#pullrequestreview-1082703779", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2729#pullrequestreview-1089142230", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2729#pullrequestreview-1089146106", "body": "Yeah I'd say some of these changes are definitely adding more tech debt.  It looks like we're trying to share/reuse code between the two apps in a way that I'm not sure makes sense -- how similar are the two apps?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2729#pullrequestreview-1089189247", "body": ""}
{"title": "Typo", "number": 273, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/273"}
{"title": "Fix unauthed asset in emails", "number": 2730, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2730", "body": "Image widths need to be capped.\n"}
{"title": "Reuse text editor in discussion view", "number": 2731, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2731", "body": "This means when you iterate through items in the TLC view, we will scroll and hilight the code correctly."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2731#pullrequestreview-1082740981", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2731#pullrequestreview-1082742065", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2731#pullrequestreview-1082742275", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2731#pullrequestreview-1082747153", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2731#pullrequestreview-1082749210", "body": ""}
{"title": "Standardize monitoring for services", "number": 2732, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2732"}
{"title": "Deserialize pull request assignees and reviewers", "number": 2733, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2733", "body": "We want to eventually return these in PullRequestInfo.participantIds"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2733#pullrequestreview-1084381071", "body": ""}
{"title": "Update Agora frameworks to 4.0.0 beta 2", "number": 2734, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2734"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2734#pullrequestreview-1089147031", "body": ""}
{"title": "Fix ActiveFileManager bugs", "number": 2735, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2735", "body": "ActiveFileManager now publishes a simple string | undefined for the active file, as opposed to an object containing a bunch of other junk. I think the way I had it before was a mistake that made some code more complicated then it needed to be.\nActiveFileManager.activeFile is now updated with a longer (500ms) debounce.  This lets the system naturally handle rapid file changes, like can happen while you are switching between discussions.  Fixes bugs in the \"Insights\" views."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2735#pullrequestreview-1082890151", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2735#pullrequestreview-1082891151", "body": ""}
{"comment": {"body": "This is the main bug fix.  Everything else in this PR is largely helping mitigate the problems with this -- allowing other updates to the visible files to take effect quicker, instead of debouncing 500ms.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2735#discussion_r953145077"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2735#pullrequestreview-1082916083", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2735#pullrequestreview-1082916350", "body": ""}
{"title": "Remove code", "number": 2736, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2736", "body": "Going to do this in the bulk ingestion job instead"}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/2740", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2736#issuecomment-1224964593"}}
{"title": "Fix bug where 'Insights' panel would clear", "number": 2737, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2737", "body": "This fixes a bug where the 'Insights' panel in the explorer tab would empty whenever you clicked on an insight. The problem is that we never stored the last state, so whenever the selected thread changed, we would re-render with an uninitialized state."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2737#pullrequestreview-1082911941", "body": ""}
{"title": "Handle milestoned event", "number": 2738, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2738", "body": ""}
{"title": "[Regression] Deleted threads are resurfaced when editing a file", "number": 2739, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2739", "body": "[regression]-deleted-threads-are-resurfaced-when-editing-a-file"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2739#pullrequestreview-1082936511", "body": ""}
{"title": "Fixes GQL client incompatibility and serialization issues", "number": 274, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/274", "body": "Client Interop\nKtor 2.0.0 is fundamentally backwards incompatible with its previous variants because of breaking DSL changes. I added a custom kotlin GQL client to make up for that.\nSerialization\nThe gradle builder uses jackson by default, but the runtime loader loads the kotlinx serializer. So we end up with incompatible serialization. A quick classpath exclusion fixes that. Now the default class found by the ServiceLoader will be the jackson serializer. We might need to dump a custom object mapper to that thing at some point, but that's a problem for another day."}
{"comment": {"body": "Just an FYI,\r\nI'm going to investigate the work to moving to using kotlinx serialization...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/274#issuecomment-1032866495"}}
{"comment": {"body": "Fixes https://github.com/NextChapterSoftware/unblocked/issues/268", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/274#issuecomment-1032866813"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/274#pullrequestreview-876382524", "body": ""}
{"comment": {"body": "Do we need to remove line 58?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/274#discussion_r801877188"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/274#pullrequestreview-876400110", "body": ""}
{"comment": {"body": "Maybe also a comment here why we're doing this (this is where `unblocked` would be super useful :) ).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/274#discussion_r801889897"}}
{"title": "Trigger review ingestion in the bulk ingestion job", "number": 2740, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2740", "body": "Will be reverted once ingestion is complete. This is only needed for the currently onboarded teams."}
{"title": "Get rid of extra padding", "number": 2741, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2741"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2741#pullrequestreview-1082934711", "body": ""}
{"title": "Remove secondary text VSCode theming", "number": 2742, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2742", "body": "Fixes the secondary text (the Timestamp labels here: )\nThe base theming already applied an opacity to the foreground text, which looks nice in every theme.  Using the placeholder colour is unnecessary."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2742#pullrequestreview-1082933952", "body": "It looks like this opacity is at 0.6 and the subtext in the header section is at 0.7. Should we align on those? cc: @benedict-jw"}
{"title": "Enable TLC", "number": 2743, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2743", "body": "Enable TLC to world."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2743#pullrequestreview-1082939118", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2743#pullrequestreview-1082940399", "body": "Will there be a second PR to remove the flag altogether?"}
{"title": "Revert \"Trigger review ingestion in the bulk ingestion job (#2740)\"", "number": 2744, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2744", "body": "This reverts commit bb76cf73c586ea820d0401e7fffed1282143ada9."}
{"title": "Add openapi style validator", "number": 2745, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2745", "body": "This style validator will catch things that zally doesn't during lint phase.\ni.e.\n1. Missing required fields in properties\ni.e.\n\nSECTION COUNT\n\nModels      1\nModels\nERROR in Model 'LogContext', property 'message' -> This property should be present or removed from the list of required\nFAILURE: Build failed with an exception."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2745#pullrequestreview-1082975759", "body": "cool"}
{"title": "Skip APICompatTest on main CI", "number": 2746, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2746", "body": "Makes no sense to compare main to main, or main to \"previous main\".\nInstead we need to run the test against publicly released versions only:\n"}
{"title": "Set initial active editor", "number": 2747, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2747", "body": "ActiveTextEditor was not set until window.onDidChangeActiveTextEditor is called.\nThis meant our sidebars did not have an activeFile on load even if there was an active text editor."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2747#pullrequestreview-1084279883", "body": ""}
{"title": "Test java 18 upgrade", "number": 2748, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2748"}
{"title": "Log number of commit hashes", "number": 2749, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2749"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2749#pullrequestreview-1084332351", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2749#pullrequestreview-1084333666", "body": ""}
{"title": "Update API references to shared", "number": 275, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/275", "body": "Small PR to prep for setting up VSCode to utilize shared BaseAPI\nRemoves vscode API codegen and points references to shared instance.\nSeparate Web API models into same file to match vscode."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/275#pullrequestreview-876489461", "body": "lgtm"}
{"title": "[TLC] Update related pull request panel styles", "number": 2750, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2750", "body": "Adjust font size, letter spacing, and word spacing of the Related PRs panel\n\n\n\n\n\nFix missing selected state from panel\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2750#pullrequestreview-1084453332", "body": ""}
{"title": "[PR Sidebar]: Render comment & merge date tabs while diff stat is loading", "number": 2751, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751", "body": "Rendering PR Sidebar is currently blocked on loading all diff stats. \nIf a file has a lot of PRs (100s-1000s), loading diff stats can be quite slow ( > 10s).\nTo improve overall PR sidebar UX, we will show the first two tabs (comment count, merge date) without waiting on the diff stat tab. Diff stat tab will show a loader while git operations are running.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084530049", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084534961", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084538894", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084543145", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084550357", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084591718", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084593796", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084596753", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084597021", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084597995", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084617876", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2751#pullrequestreview-1084618068", "body": ""}
{"title": "Cleanup code", "number": 2752, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2752"}
{"title": "Fix the video window toolbar layout", "number": 2753, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#pullrequestreview-1095409551", "body": ""}
{"comment": {"body": "Ignore - it's added back in a subsequent PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#discussion_r962054745"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#pullrequestreview-1095412898", "body": ""}
{"comment": {"body": "Is this the standard approach? Having a custom modifier for this feels a bit heavy when we could have a \"VideoToolbar\" View that's passed into the existing Toolbar modifier.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#discussion_r962057462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#pullrequestreview-1095413388", "body": ""}
{"comment": {"body": "This comment still relevant? Not seeing an ID or image", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#discussion_r962057846"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#pullrequestreview-1095413457", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#pullrequestreview-1095413789", "body": ""}
{"comment": {"body": "I think you're right. The result is not exactly a view though - it's `ToolbarContent`. Easy to change I think", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#discussion_r962058164"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#pullrequestreview-1095414022", "body": ""}
{"comment": {"body": "Good catch - will remove", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#discussion_r962058392"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#pullrequestreview-1099482627", "body": ""}
{"comment": {"body": "Fixed in subsequent PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#discussion_r965052180"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#pullrequestreview-1099483003", "body": ""}
{"comment": {"body": "Removed in subsequent PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#discussion_r965052427"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#pullrequestreview-1104787242", "body": ""}
{"comment": {"body": "Reverted back to use modifier. Without the modifier state preservation is lost. Seems like a SwiftUI bug...\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6542d00-cc85-4e0a-a18f-88aa27fb9077?message=e401f54c-724c-4bb9-84c7-2df2ecd1f6fd).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2753#discussion_r968897687"}}
{"title": "Remove metrics cache", "number": 2754, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2754", "body": "No longer rate limiting metrics request on clients.\nRemoves dead unnecessary code."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2754#pullrequestreview-1084509948", "body": ""}
{"title": "[TLC] Add list of approvers to pr view", "number": 2755, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2755", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2755#pullrequestreview-1084531596", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2755#pullrequestreview-1084531945", "body": ""}
{"title": "Migrate UnblockedPRCommentMessagePayload.messageId to id", "number": 2756, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2756", "body": "Idea here is to re-use our existing logic for editing/creating/deleting top level comments in GitHub (PullRequestCommentModel). \nStep one is to migrate UnblockedPRCommentMessagePayload.messageId to  UnblockedPRCommentMessagePayload.id and add a type to this message, so that the message handler can handle those events differently."}
{"title": "Lots of spurious honeycomb traces killing us", "number": 2757, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2757", "body": "We need to better handle when we spit this trace out, especially when the job is doing nothing because there are no messages etc.\nWill have to do that later, right now, our quota is being killed by this fucker."}
{"title": "Add create, update, and delete issue comment operations", "number": 2758, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2758", "body": "Needed for TLC CRUD operations"}
{"title": "Log jvm metrics", "number": 2759, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2759", "body": "Going to eventually move to honeycomb metrics (requires a bit of work, but for now, just recording shit via spans)\nA bit of other crap related to network hostname."}
{"title": "Use identity as auth identity rather than person", "number": 276, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/276", "body": "Problem\nIn some cases (app install flow for example), we need to pivot to a specific TeamMember. This is not possible if the root identity for a user is the Person object, because a person object can have many Identities/Teammembers.\nThe auth install flow needs to derive a specific TeamMember in order to determine whether the app is installed for a particular org. \nWhat's in this PR\nChange the auth identity into the service to use the Identity instead of Person"}
{"comment": {"body": "So I guess this assumes the client will chose the correct identity for the desired team?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/276#issuecomment-1032931043"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/276#pullrequestreview-876491774", "body": "Looks fine to me"}
{"title": "Minor cleanup", "number": 2760, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2760"}
{"title": "Fix snippet wrapping in admin when lines are long", "number": 2761, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2761", "body": "Fixed lack of wrapping on this page, in the Sourcepoints section:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2761#pullrequestreview-1084861507", "body": ""}
{"title": "More ergonomic logger extension functions", "number": 2762, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2762", "body": "use vararg instead of having to create maps\nremove unused Marker functionality\nno need to check if log level is enabled"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2762#pullrequestreview-1085633910", "body": "Love it, thanks."}
{"title": "Adds logs to getSourceMarks API operation to count objects returned", "number": 2763, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2763", "body": "In local testing discovered that while mark count is limited to 500 objects,\nthe point count goes as high as 12,000 objects."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2763#pullrequestreview-1084846947", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2763#pullrequestreview-1085555702", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2763#pullrequestreview-1085636345", "body": ""}
{"title": "Source mark debug tooling", "number": 2764, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764", "body": "Ability to focus the SM engine on a single sourcemark for debugging. This pretends\n  that there is only a single sourcemark in the repo. Invoke from comand line as:\n  launchctl setenv SOURCEMARK_ID fdd2e9b9-fe73-4f00-9708-76d488140a60\nGenerate a Git command report when performing source marks activity. This is\n  configured to generate a report for the SourceMarkProvider entry points.\n  ```\n  Command frequency for getSourceMarksForFile:\n\ncount    sum    max    avg cmd\n     12     83      8    6.9 git ls-tree 2a0c8ed362c56692c321eb630e9b2c1833629d14 -- api/build.gradle.kts\n     12     91     10    7.6 git rev-list -1 --objects 2a0c8ed362c56692c321eb630e9b2c1833629d14 -- api/build.gradle.kts\n      6    165     30   27.5 git diff --diff-filter=DR --find-renames=50% --name-status be69d36a13922a834baee54f306de030b1a939fb --\n  ```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#pullrequestreview-**********", "body": ""}
{"comment": {"body": "FYI there is a Math.max for this...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#discussion_r955613939"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This should probably be imported from @utils instead of a relative path...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#discussion_r955614161"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#pullrequestreview-1086350013", "body": ""}
{"comment": {"body": "ah, thanks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#discussion_r955614463"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#pullrequestreview-1086350249", "body": ""}
{"comment": {"body": "ok, what's the advantage?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#discussion_r955614641"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#pullrequestreview-1086365229", "body": ""}
{"comment": {"body": "At this point, primarily, consistency -- it's what we do everywhere else.  Grouping the code into modules in that way makes it a bit easier to rationalize the organization of the system, but there isn't much of a pragmatic difference.  TBH I've always felt a bit ambivalent about this pattern, but I'd rather keep things consistent at this point then not.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2764#discussion_r955625819"}}
{"title": "Optimize Git command usage during getSourceMarksForFile", "number": 2765, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2765", "body": "Before (5,219 ms)\ncount    sum    max    avg cmd\n   166   1539    280    9.3 git ls-tree 2a0c8ed362c56692c321eb630e9b2c1833629d14 -- api/private.yml\n   166   1328     12    8.0 git rev-list -1 --objects 2a0c8ed362c56692c321eb630e9b2c1833629d14 -- api/private.yml\n    83   2352     37   28.3 git diff --diff-filter=DR --find-renames=50% --name-status a150c3e4c7a008abb2212cec0f4eeb35570edf62 --\nAfter (2,890 ms)\ncount    sum    max    avg cmd\n   166   1537    293    9.3 git ls-tree 01cb5b3f6e9b79c0bd00efb0d685031cec1bd431 -- api/private.yml\n   166   1325     14    8.0 git rev-list -1 --objects 01cb5b3f6e9b79c0bd00efb0d685031cec1bd431 -- api/private.yml\n     1     28     28   28.0 git diff --diff-filter=DR --find-renames=50% --name-status 01cb5b3f6e9b79c0bd00efb0d685031cec1bd431 --"}
{"comment": {"body": "more to do here, but in follow up PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2765#issuecomment-1227922329"}}
{"title": "Reduce getSourceMark limit to 100 (was 500)", "number": 2766, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2766", "body": "also remove client side limit parameter\n\nThis will now require Clio to perform 1,129 sequential API calls to fetch all sourcemark for their repo, which will take ~4mins to download.\n"}
{"comment": {"body": "@richiebres I think we may want to keep the client limit here as well?  We want to protect both parts of the system, removing the client limit means client builds may get more results then they can process, if we change the service value...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2766#issuecomment-1227475544"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2766#pullrequestreview-1085706917", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2766#pullrequestreview-1085721308", "body": ""}
{"title": "increase api memory", "number": 2767, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2767"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2767#pullrequestreview-1085720718", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2767#pullrequestreview-1085722230", "body": ""}
{"comment": {"body": "Actual usage will be limited to 1.33 GiB", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2767#discussion_r955151602"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2767#pullrequestreview-1085723616", "body": ""}
{"title": "UpContainerMemoryUsage", "number": 2768, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2768", "body": "Update memory ratio\nIncease container memory ratio"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2768#pullrequestreview-1085728539", "body": ""}
{"title": "increase API memory request to 1.5", "number": 2769, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2769"}
{"title": "Add rule to forbid double bang dereferencing", "number": 277, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/277", "body": "No more !! in our code.\nit sucks and it's dangerous. \nAlso clean up tests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/277#pullrequestreview-876479009", "body": "Good plan"}
{"title": "Handle PR Ingestion states", "number": 2770, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770", "body": "When any PR is ingesting and we have less PRs than commits, we may be missing PRs so show loading state.\n\nOn PR view, show loading state if ingestion is still in progress.\n"}
{"comment": {"body": "@jeffrey-ng the text in the sidebar component looks a bit tight up against the last row. In the designs that text was placed in a container that was the same height as all the other rows. Can we try that?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#issuecomment-1227508406"}}
{"comment": {"body": ">\r\n@benedict-jw \r\nHow does this look?\r\n \r\n<img width=\"413\" alt=\"CleanShot 2022-08-25 at 09 57 31@2x\" src=\"https://user-images.githubusercontent.com/1553313/186725404-10d620be-80f1-4d36-850b-dbc90134163b.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#issuecomment-1227532218"}}
{"comment": {"body": "> @benedict-jw How does this look?\r\n\r\nMuch better!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#issuecomment-1227540391"}}
{"comment": {"body": "<img width=\"667\" alt=\"CleanShot 2022-08-25 at 15 23 07@2x\" src=\"https://user-images.githubusercontent.com/1553313/186779366-685a6304-383b-4879-baf8-c6515dd7de84.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#issuecomment-1227818820"}}
{"comment": {"body": "@jeffrey-ng can you add an equal amount of padding underneath the loading text? It's bumping right up against the header of the section beneath it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#issuecomment-1227827004"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1085761783", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1085792801", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1085834500", "body": ""}
{"comment": {"body": "@jeffrey-ng test test helloo", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#discussion_r955235067"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1085836844", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1085838248", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1085856456", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1085869793", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1086057043", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1086058048", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1086174862", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1086181121", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2770#pullrequestreview-1086187484", "body": ""}
{"title": "[TLC] Collapse thread views", "number": 2771, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2771", "body": "\nAlso:\n* Fix bug with capitalization of text\n* Fix build warning for flex property"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2771#pullrequestreview-1085874107", "body": ""}
{"comment": {"body": "Curious, any reason we changed to doing this in code instead of in CSS?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2771#discussion_r955262133"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2771#pullrequestreview-1085875501", "body": ""}
{"comment": {"body": "`text-transform: capitalize` capitalizes the first letter of every word in the string which isn't the intention here. i.e. we want the string to say `An hour ago` and not `An Hour Ago` \r\n\r\nThis helper only capitalizes the first letter of the entire string", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2771#discussion_r955263171"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2771#pullrequestreview-1086129857", "body": ""}
{"title": "Clean up synchronous logging", "number": 2772, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2772", "body": "The Bresninator inspired me to do this.\nI thank the Bresninator."}
{"title": "Wire up updating reviews", "number": 2773, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2773"}
{"title": "Add toolbar menus", "number": 2774, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2774", "body": "This PR works on its own, but falls over after screen sharing due to lost window status. I'll fix that issue in a followup"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2774#pullrequestreview-1095420864", "body": ""}
{"title": "Add debugging logs", "number": 2775, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2775", "body": "Add debugging logs for open discussion bugs"}
{"title": "Fixes permissions dialog darkmode colours", "number": 2776, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2776"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2776#pullrequestreview-1099891175", "body": ""}
{"title": "Endpoints for TLCs", "number": 2777, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1086118168", "body": ""}
{"comment": {"body": "Not sure about naming... maybe `CreateMessageContentRequest` ? \r\n\r\nThis should basically be the generalized request body for messages (the other CreateMessageRequest is tied to sourcemarks) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#discussion_r955426854"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1086121861", "body": ""}
{"comment": {"body": "Just a heads up: we can't delete review pull request blocks since reviews can only be updated. We should not show the \"Delete message\" option for these blocks (i.e. any block where `reviewState` != `null`)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#discussion_r955429446"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1086122630", "body": ""}
{"comment": {"body": "Ah okay. Client will check this -- I'm assuming backend will also guard for this too? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#discussion_r955430021"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1086123450", "body": ""}
{"comment": {"body": "Yeah service will check and throw an error if this is a review", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#discussion_r955430623"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1086125793", "body": ""}
{"comment": {"body": "Also reviews without comments (like many approvals) can't be updated, so we should hide the option here. \r\n\r\nI wonder if its worth adding some properties to the api models to signal whether a block is update-able or delete-able to help the clients out?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#discussion_r955432306"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1086128980", "body": ""}
{"comment": {"body": "Not sure if it needs to be on the API model or whether the client can just write some general helpers to facilitate. It's unclear to me whether these limitations are GH-specific (what's the behaviour on BB/GitLab?) but if they are, then I'm definitely more inclined to leave them out of the API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#discussion_r955434554"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087195833", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087199926", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087204401", "body": ""}
{"comment": {"body": "Totally up to you, but one option is to split this into its own PR so that we can ship creating/updating/deleting TLCs first. Ignore me if it's easier to implement the client changes all in one go.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#discussion_r956209156"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087357840", "body": ""}
{"comment": {"body": "We can stub this one out first and focus on supporting the TLC ones but I don't see why we wouldn't just get the API change in now? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#discussion_r956303377"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087358774", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087385134", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087431588", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087432428", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087478951", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1087589886", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2777#pullrequestreview-1092556929", "body": ""}
{"title": "Add ability to record precompressed response paypload size in honeycomb", "number": 2778, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2778", "body": "This pr allows us to intercept the ktor payload before the compression plugin fucks around with headers.\nWe can ascertain what the payload size is pre-compression.\nPost-compression, no go.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2778#pullrequestreview-1086173122", "body": ""}
{"comment": {"body": "I use runCatching all the time now, because ktlint complains about catching Throwable", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2778#discussion_r955467405"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2778#pullrequestreview-1094128782", "body": ""}
{"comment": {"body": "I like @pwerry\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/9ac46917-536f-4905-99a2-889e929f947f?message=07ce02e0-aba8-4f2d-a041-05d304be7923).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2778#discussion_r961150057"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2778#pullrequestreview-1094129047", "body": ""}
{"comment": {"body": "i like @rasharab\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/9ac46917-536f-4905-99a2-889e929f947f?message=c7144956-3e71-4ed2-aa12-678f65e112cf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2778#discussion_r961150227"}}
{"title": "Wire up creating, editing, and deleting issue comments", "number": 2779, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2779"}
{"title": "Refresh Auth VSCode", "number": 278, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/278", "body": "Add refresh logic for auth token on vscode.\nWhen a request 401s, attempt to use refreshtoken to get a new base token."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/278#pullrequestreview-876750905", "body": ""}
{"title": "Add metrics for honeycomb", "number": 2780, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2780", "body": "\nThis pr lets us now use various honeycomb meter types.\nCool thing is, you can combine metric board graphs with query graphs. (as in the above example)\nInterestingly, Honeycomb metrics are a good way of cost saving, as Honeycomb aggregates data.\n."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2780#pullrequestreview-1086288612", "body": "exciting..."}
{"title": "add auth service as a replica of api service", "number": 2781, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781", "body": "Add a new service called Auth service \nAdd ALB paths to redirect all auth related calls to the new auth service\nAdded helm charts with updated value to suite the functions which will be handled by this service \n\nAuth service is a replica of API service. It uses the same artifact but handles a subset of API paths related to auth only. This way we can limit the impact of service crashes. \nMaking a whole new grade project and moving the code to it seemed like an overkill. This PR requires disabling prod deploys before merge so we could safely test it in Dev."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#pullrequestreview-1086398522", "body": ""}
{"comment": {"body": "This would be for a service that handles `logs` and `metrics` ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#discussion_r955650954"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#pullrequestreview-1086398688", "body": ""}
{"comment": {"body": "This would be for a service that handles all heavy source mark operations", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#discussion_r955651070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#pullrequestreview-1086399270", "body": ""}
{"comment": {"body": "The current API service has a value of 100 for this field. This means for the paths mentioned in each per environment values.yaml file this service will be taking precedence over the regular api service", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#discussion_r955651492"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#pullrequestreview-1086400833", "body": ""}
{"comment": {"body": "Name here is kept as `apiservice-*-all.jar` because we are using the binary built for API service to deploy this new service. Basically a specialized instance of the API service", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#discussion_r955652607"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#pullrequestreview-1092302086", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#pullrequestreview-1279741057", "body": ""}
{"comment": {"body": "Thank you", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2781#discussion_r1093647695"}}
{"title": "Optimize SM file lookup and full recalculation", "number": 2782, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782", "body": "Changes:\n\nfound a more efficient way to get a file content hash\nadds Git cache, and introduces LRU cache dependency\n\nResults:\n\nfull recalculation runtime on unblocked repo went from 3m 31s to 1m 42s\nget source marks for file is almost instant even for large files with lots of marks;\n  however rendering (or joining on threadinfo) still takes a very long time.\n\nFixes: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087189712", "body": ""}
{"comment": {"body": "Just FYI this library won't work in non-node environments, so it won't work in the dashboard, web extension, or on VSCode in the web.  I don't know if we care about that, probably not at this point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#discussion_r956199245"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087191938", "body": ""}
{"comment": {"body": "100MB cache?  Seems like a lot of memory, but maybe that's fine...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#discussion_r956200776"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087194831", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087207210", "body": ""}
{"comment": {"body": "Why? Btw, we already have this dependency transitively, so many of our _existing_ direct dependencies are using this already today.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4b5e30ad-91af-452b-91f2-114b02f548cf?message=bff6b63d-a7f3-4ca9-a740-df2794c293b8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#discussion_r956211143"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087208508", "body": ""}
{"comment": {"body": "We can tune, but it's just 5% of 2GB which is extension limit.\n\n\n\nAlso it's the max, not pre-allocated.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/fc06be54-087e-41c6-9047-ddfa62a215ad?message=b7b623b3-6136-406a-9edc-b777fa6dffbf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#discussion_r956212007"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087217985", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087360695", "body": ""}
{"comment": {"body": "I'm not sure why, I'd imagine they're using Node APIs.  It looks like the existing transitive dependencies are build dependencies (ie, webpack and our other build tools use it), as opposed to runtime dependencies.\r\n\r\n(As a side note, this brings up a point that we probably should mark our runtime dependencies as non-dev dependencies, even though we technically don't depend on installing them at runtime.  That would make analysis like this easier)\r\n\r\nAnyways, this probably doesn't matter much at this point.  It will work in VSCode as we use it now, we can change this later if needed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#discussion_r956306224"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087498897", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087552923", "body": ""}
{"comment": {"body": "@jeffrey-ng this will help eliminate the cost of repeat fetches when populating the +/- diffs in the PR view", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#discussion_r956470371"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2782#pullrequestreview-1087573558", "body": "I guess the TTL eviction might hypothetically lead to hard-to-track-down issues, as opposed to a cache that understands when it needs to evict deterministically based on the system events (ie, \"evict data  when I have changed my current commit\").\nBut it's probably fine for now."}
{"title": "Sequential promises", "number": 2783, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783", "body": "Add support for sequential promises that can be chunked.\nUsed primarily for calculating diff stats.\nStill seems to work?\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1087372877", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1087375346", "body": ""}
{"comment": {"body": "Add a test for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#discussion_r956319727"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1087377660", "body": ""}
{"comment": {"body": "I'm confused how this passes?  If we advance the timer 1000ms, shouldn't both first and second have run (each waits for 100ms) ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#discussion_r956321895"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1087379150", "body": ""}
{"comment": {"body": "(Also I'm consued about all the `await Promise.resolve()` everywhere... )", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#discussion_r956323358"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1087381298", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1087384555", "body": ""}
{"comment": {"body": "It seems like when one uses timeouts with promises, promises can be stuck on a promise queue which needs to be resolved/flushed.\n\n\n\nIn this specific scenario, the timer wasn't stopping mockFn2 from being called. It was the promise queue which was blocking.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/72def452-53ed-4e9b-8144-3c7e6ce95527?message=c8e5d01f-4574-40fc-b00e-d85b31b1db8f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#discussion_r956328279"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1087384989", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1092135670", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1092142562", "body": ""}
{"comment": {"body": "Is this correct?  The other two (then / finally) call promise.then and promise.finally, should this be calling promise.catch ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#discussion_r959765999"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1092145239", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#pullrequestreview-1092248395", "body": ""}
{"comment": {"body": "Similar but different.\r\n\r\nWe use the. `then` as it will eventually call the reject but also cleans up the onCancelTrigger callback.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2783#discussion_r959839390"}}
{"title": "Fix vscode storybook tests", "number": 2784, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2784"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2784#pullrequestreview-1087416141", "body": ""}
{"title": "Add per service jvm metrics", "number": 2785, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2785"}
{"title": "Update", "number": 2786, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2786"}
{"title": "Add mentioned to web extension", "number": 2787, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2787", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2787#pullrequestreview-1087512824", "body": "You are beautiful."}
{"title": "Screen Share Picker", "number": 2788, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2788", "body": "The main goal of this PR is to implement the screen picker. There's some crufty stuff that I will be cleaning up as the implementation evolves."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2788#pullrequestreview-1089118790", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2788#pullrequestreview-1089121402", "body": ""}
{"comment": {"body": "Oddly the content won't hug properly if this isn't a `LazyVStack`. I'm concerned this might break in subsequent SwiftUI updates but it will be really obvious if it does", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2788#discussion_r957632753"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2788#pullrequestreview-1089122234", "body": ""}
{"comment": {"body": "Not used yet because I couldn't get focus working with the arrow keys. Can remove ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2788#discussion_r957633313"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2788#pullrequestreview-1089122629", "body": ""}
{"comment": {"body": "Ditto here - can remove the focus scope modifier", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2788#discussion_r957633569"}}
{"title": "Suppress notifications for deletions", "number": 2789, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2789", "body": "When the last message in a thread is deleted, we want to suppress any notifications. Doing this on the client solves this in all deletion scenarios, but importantly it is the only way to solve the following scenario:\n\nLastReadMessage == LastMessage - N\nDelete LastMessage\n\nThe service will mark this thread as unread in this scenario and can't understand the client notification state."}
{"comment": {"body": "> The service will mark this thread as unread in this scenario and can't understand the client notification state.\r\n\r\nTo clarify, in this scenario the thread was already set to unread before the deletion. After deletion it's still unread except the modifiedAt for the thread unread is updated, which is probably why the push channel fired.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2789#issuecomment-1229039016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2789#pullrequestreview-1087564537", "body": ""}
{"comment": {"body": "This logic should:\r\n\r\n1. Search for the new unread thread in the previous list of threads\r\n2. If found then\r\n3. if the previous latest message is the same as the new latest message, or the previous latest message createdAt timestamp is greater than the new latest message createdAt timestamp\r\n4. Then filter this unread from notifications\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2789#discussion_r956478939"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2789#pullrequestreview-1087587686", "body": ""}
{"title": "Allow setting page sizes and cursors on PR review thread queries", "number": 279, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/279", "body": "Updating the GraphQL queries to make it possible to page results."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/279#pullrequestreview-876617328", "body": "LGTM"}
{"title": "UNB-561 Add missing unread mention indicators to dashboard/extension", "number": 2790, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2790", "body": "\n\n\nAlso refactor teamMembers --> userIconProtocol logic into one place"}
{"comment": {"body": "Lol https://github.com/NextChapterSoftware/unblocked/pull/2787\r\n\r\nI'll close mine. seems like your PR has much more done.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2790#issuecomment-1229015073"}}
{"comment": {"body": "To see the eagerness on both of your parts to get his @mention icon in brings happiness to my heard.\r\nPlease, make it so.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2790#issuecomment-1229019652"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2790#pullrequestreview-1087570710", "body": ""}
{"title": "Fix bug where threads are being marked as read when the last message in a thread is deleted", "number": 2791, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791#pullrequestreview-1087584291", "body": ""}
{"comment": {"body": "The bug fixes where this was being set to `secondMessage`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791#discussion_r956494036"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791#pullrequestreview-1087723346", "body": ""}
{"comment": {"body": "Nice! I worked through the scenarios to triple check", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791#discussion_r956627755"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791#pullrequestreview-1087728703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791#pullrequestreview-1089005029", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791#pullrequestreview-1089013249", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791#pullrequestreview-1089013501", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2791#pullrequestreview-1089398731", "body": ""}
{"title": "add slack utilities", "number": 2792, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2792"}
{"title": "Add empty view for no discussions/no search", "number": 2793, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2793", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2793#pullrequestreview-1087597426", "body": ""}
{"title": "Bulk fetch threads", "number": 2794, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2794", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2794#pullrequestreview-1092292566", "body": ""}
{"comment": {"body": "refactoring. moved without modification ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2794#discussion_r959870977"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2794#pullrequestreview-1092566416", "body": ""}
{"title": "API for web extension PR unread", "number": 2795, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2795", "body": "API to support unread status in GitHub PR View.\n\nAPI to get PullRequest given teamID, repoID, PR number\nAPI + Channel to get list of unreadMessages. UnreadMessages match with IDs in GH's PR view."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2795#pullrequestreview-1087604777", "body": "Looks good. Just some naming suggestions...."}
{"comment": {"body": "```suggestion\r\n        prCommentId:\r\n          description: |\r\n            External SCM PR message ID.\r\n          type: string\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2795#discussion_r956511226"}}
{"title": "Support for diff hunk modifications", "number": 2796, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2796", "body": "Problem:\n\nA simple edit confused the source mark engine in ChannelPoller.ts\n  "}
{"title": "SourceMark is associated with an unexpected line. Heavily modified, should be removed.", "number": 2797, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2797", "body": ""}
{"title": "Attempt to track down error in fileContentHash", "number": 2798, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2798", "body": "Changes\nI cannot reproduce a problem with fileContentHash in my environment.\nHoping that more verbose logging will uncover the problem for people who can repro.\nProblem\n{\n  context: 'DiscussionThreadCommand',\n  service: 'vscode',\n  environment: 'local',\n  type: 'nodejs',\n  process: 'extension',\n  level: 'error',\n  message: 'Issue fetching anchor source point File does not exist at new commit; possibly renamed or moved.',\n  stack: 'Error: File does not exist at new commit; possibly renamed or moved.\\n' +\n    '\\tat nl.hunkReduce (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1162424)\\n' +\n    '\\tat async nl.lookupOrRecalculateForChanges (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1162159)\\n' +\n    '\\tat async nl.getPointForSourceMarkInternal (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1164865)\\n' +\n    '\\tat async nl.getPointForSourceMark (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1162923)\\n' +\n    '\\tat async dl.getSourceMarkLatestPoint (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1168991)\\n' +\n    '\\tat async gs.withChangedFiles (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1070282)\\n' +\n    '\\tat async wd (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1126107)\\n' +\n    '\\tat async Dd (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1141467)\\n' +\n    '\\tat async _ (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1127963)',\n  timestamp: '2022-08-29T16:26:41.518Z'\n}"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2798#pullrequestreview-1089115124", "body": ""}
{"title": "Add snippet", "number": 2799, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2799"}
{"title": "User Icon and icon stack with Tailwind", "number": 28, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/28", "body": ""}
{"comment": {"body": "Proof of concept. Closing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/28#issuecomment-1013385003"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/28#pullrequestreview-848317158", "body": ""}
{"comment": {"body": "I thought we'd moved this to the `/sharedCfg` tsconfig?  I think this should definitely apply to other TS projects too", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/28#discussion_r781532379"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/28#pullrequestreview-848319908", "body": ""}
{"comment": {"body": "Need to pull main, one sec", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/28#discussion_r781534271"}}
{"title": "Shared Auth Store", "number": 280, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280", "body": "Initially had authstore exist for both web and vscode client.\nThis was necessary as there was client specific code within the AuthStore which made sharing the entire AuthStore difficult.\nBased on this document:\nhttps://github.com/pmndrs/zustand/wiki/Practice-with-no-store-actions\nWe have split up the Authstores state and actions. This then allowed me to refactor out the common aspects of the store for the two clients while allowing each client define their own specific actions.\nBuild off https://github.com/NextChapterSoftware/unblocked/pull/278"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280#pullrequestreview-876695837", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280#pullrequestreview-876736610", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280#pullrequestreview-878150530", "body": ""}
{"comment": {"body": "I'm not 100% sure but should this be a property on the store itself rather then a synchronous getter?  Feels like something you'd want to monitor as opposed to fetching?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280#discussion_r803160112"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280#pullrequestreview-878154514", "body": ""}
{"comment": {"body": "Ah never mind, this is fine as a synchronous accessor too, though if some day we move this to a state value I'd imagine anyone using this could just do `store.getState().isAuthenticated`...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280#discussion_r803163331"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280#pullrequestreview-878158676", "body": ""}
{"comment": {"body": "Looks like we might be able to factor this (to the bottom of the fn ) into the base as well?  Not a big deal either way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280#discussion_r803166569"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/280#pullrequestreview-878160171", "body": "Something to think of, not for this PR: I'm wondering if the naming is getting a bit confusing, similar to 'MessageEditor' living in a bunch of places.  Should we move to a model where base functionality in shared code should be labelled with a 'Base' suffix to make it clear?"}
{"title": "Deployments for snippet", "number": 2800, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2800"}
{"title": "Fix for fileContentHash", "number": 2801, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2801", "body": "Problem is that we introduced a new way to calculate the file content hash using\nan ls-tree option called --object-only which is not available in older Git versions.\nSee also:\n- breaking change in #2782\n- debugging to find problem in #2798"}
{"comment": {"body": "Found a better way, closing...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2801#issuecomment-1230719120"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2801#pullrequestreview-1089195410", "body": ""}
{"comment": {"body": "This is just reviving the old method for generating file content hashes that was proven to work on all 2.x git versions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2801#discussion_r957682757"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2801#pullrequestreview-1089198953", "body": ""}
{"title": "Build with java 18", "number": 2802, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2802"}
{"title": "Fix for ls-tree based fileContentHash function", "number": 2803, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2803", "body": "Problem is that we introduced a new way to calculate the file content hash using\nan ls-tree option called --object-only which is not available in older Git versions.\nSee also:\n\nbreaking change in Optimize SM file lookup and full recalculation #2782\ndebugging to find problem in Attempt to track down error in fileContentHash #2798\nalternative fix in #2801"}
{"title": "Wrong ecr repo (fuck)", "number": 2804, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2804"}
{"title": "Prevent VSCode flickering on dock when opening a thread from the hub", "number": 2805, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805", "body": "If you click on a thread in the hub, and it opens in VSCode, a second VSCode instance temporarily flickers open in the system dock.  This prevents it.  This thread has some more background: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#pullrequestreview-1089368509", "body": ""}
{"comment": {"body": "Note: this will only work on Mac, but the previous code was Mac-specific as well, and we only have a hub app (from which this is called) on the Mac, so....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#discussion_r957804979"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#pullrequestreview-1089428734", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#pullrequestreview-1089429948", "body": ""}
{"comment": {"body": "Why is `open` better than invoking `code`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#discussion_r957850566"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#pullrequestreview-1089430524", "body": ""}
{"comment": {"body": "'code' for whatever reason causes a new VSCode app instance to pop open in the \"recently used\" section on the right side of the dock for a split second.  'open' does not.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64da19a2-b2a1-4578-8173-ae67d7c35b06?message=4a458a92-6a76-430a-ae7a-46d9a5f0b8c6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#discussion_r957851082"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#pullrequestreview-1089431260", "body": ""}
{"comment": {"body": "Ok read the thread on the vscode repo - bonkers.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64da19a2-b2a1-4578-8173-ae67d7c35b06?message=f5527869-9bd6-41bb-aca9-6a205303d8df).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#discussion_r957851679"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2805#pullrequestreview-1089432472", "body": ""}
{"title": "Fix fileContentHash function again", "number": 2806, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2806", "body": "Revert \"Fix for ls-tree based fileContentHash function (#2803)\"\nThis reverts commit 30cd967358e413ce3493b7f224e667ac7053aed7.\nFix for fileContentHash\nProblem is that we introduced a new way to calculate the file content hash using\nan ls-tree option called --object-only which is not available in older Git versions.\nSee also:\n- breaking change in #2782\n- debugging to find problem in #2798"}
{"title": "Fix DiffStatParser exceptions resulting in missing changed lines", "number": 2807, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2807", "body": "For older Git versions specifying an empty pretty format implies a default output;\nwhereas in more recent Git versions the behavious has changed (to what we want) to show no content.\nFix by putting in a newline (%n) which is already trimmed out anyway.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2807#pullrequestreview-1089409327", "body": ""}
{"comment": {"body": "For older Git versions specifying an empty pretty format implies a default output; whereas in more recent Git versions the behavious has changed (to what we want) to show no content. Fix by putting in a newline (%n) which is already trimmed out anyway.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2807#discussion_r957834678"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2807#pullrequestreview-1089415995", "body": ""}
{"title": "Add invite page to dashboard", "number": 2808, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1089439955", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1089579957", "body": ""}
{"comment": {"body": "Isn't this kind of thing better done by setting a margin or padding?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#discussion_r957967854"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1089582680", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1089585814", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1089586658", "body": ""}
{"comment": {"body": "We should probably trim the emails too", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#discussion_r957973070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1089586897", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1089587737", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1089588147", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1089588361", "body": "Some feedback but looks good!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1092301280", "body": ""}
{"comment": {"body": "This should be what this fn is doing, unless I'm misunderstanding your comment?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#discussion_r959877011"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#pullrequestreview-1092305607", "body": ""}
{"comment": {"body": "Never mind, misread this!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2808#discussion_r959880034"}}
{"title": "Add basic functionality for snippet shit", "number": 2809, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2809"}
{"title": "Add vscode codeblock component", "number": 281, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281", "body": "Also refactored StartDiscussion form to include MessageEditor\n\n\n\nRefactored StartDiscussionCommand to take a set of action commands \n\n\nNOTE: Is missing editor code theming, issues with the shiki library, will revisit (but shouldn't block this PR from going in)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-876722184", "body": ""}
{"comment": {"body": "follows a similar pattern as the demo code -- open to feedback if we wanted to go with a different approach", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#discussion_r802111846"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-876757374", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-876759362", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-876762678", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-876763237", "body": ""}
{"comment": {"body": "No this is probably the right way to do things...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#discussion_r802143105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-876763426", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-876777640", "body": ""}
{"comment": {"body": "`This is some code`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#discussion_r802154937"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-876778606", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/blob/699c838c5eaa6f26a6a0967d533a8988b9e95789/shared/webComponents/UserIcon/UserIcon.tsx#L8", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#discussion_r802155712"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-876783006", "body": ""}
{"comment": {"body": "klkhkjhkj", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#discussion_r802159265"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#pullrequestreview-877910996", "body": ""}
{"comment": {"body": "Not for this PR but I think we may revisit what the actual contents coming into this code block will be.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/281#discussion_r802984269"}}
{"title": "Split team member page into current/past so that it can render large teams", "number": 2810, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2810"}
{"title": "[BREAKS API ON MAIN] Try loading snippets from index", "number": 2811, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2811"}
{"title": "Prune low-value threads", "number": 2812, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812", "body": "A first pass at pruning PR-ingested threads that are likely to not contain valuable information. \nThe approach here is to use heuristics to determine whether a thread contains valuable information, and to archive it if not.\nTo do this, I'm adding a new enum field on ThreadModel called archivedReason which will let us specify why a thread was archived, which will help track why a thread was archived.\nFor now we're using a single heuristic to identify low-value threads: threads with one message where the message contains only one word (examples: LGTM!, cool, neat, emojis). The intention is to follow-up this PR with others that add additional heuritics, but to start I'd like get this logic in with something simple and straightforward.\nTODO\n\n[x] fill out stubbed tests\n[x] add migration logic to Admin Console to archive low-value threads for already ingested repos\n[x] only archive threads for closed/merged PRs \n[x] backfill ThreadModel.archivedReason for user-archived threads"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1091061797", "body": ""}
{"comment": {"body": "Add `User` for when users manually delete?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#discussion_r958999920"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1091072018", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1091072169", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1091072348", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1091073020", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1091073882", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1091074071", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1091074214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1091086271", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1093751997", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2812#pullrequestreview-1093930853", "body": ""}
{"title": "Stats page shoud link to current team members", "number": 2813, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2813", "body": "follow up from #2810"}
{"title": "Install Sentry", "number": 2814, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2814", "body": ""}
{"title": "Snippet Webview Skeleton", "number": 2815, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2815", "body": "Prototype and hidden behind feature flag.\nThis is the experimental UI I call Snippets n' Shit.\nNot meant for production.\nNot meant for review.\nNot meant for anything else but giggles and prototyping.\n-Rashin"}
{"title": "Retreive snippets from api", "number": 2816, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2816"}
{"title": "Update", "number": 2817, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2817"}
{"title": "Trace for pollingBackgroundJobs", "number": 2818, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2818"}
{"title": "Ease up on database queries", "number": 2819, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2819", "body": "Existing tests still work"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2819#pullrequestreview-1092271209", "body": ""}
{"comment": {"body": "convert to data model here, so we don't leak DAOs?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2819#discussion_r959855679"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2819#pullrequestreview-1092275218", "body": ""}
{"comment": {"body": "That'll require some refactoring in `PullRequestIngestionService`, let me do that in a follow up PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2819#discussion_r959858656"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2819#pullrequestreview-1092275797", "body": ""}
{"comment": {"body": "ah, no big deal\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/e2e1ccb9-c9d9-4799-8bb2-c4226ecee5bd?message=e0c799c6-cda0-495b-8da2-676410e13ebd).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2819#discussion_r959859022"}}
{"title": "Web deploy", "number": 282, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/282", "body": "Setup CI deployments for our web service to \nIntroduces very simple env variable system for web.\nTODO: Introduce something similar for vscode.\nUpdates some CORS settings to allow requests from domain to ''"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/282#pullrequestreview-878179126", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/282#pullrequestreview-878180057", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/282#pullrequestreview-878188259", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/282#pullrequestreview-878195598", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/282#pullrequestreview-878196168", "body": "Minor comments."}
{"title": "add timeout to kube probes", "number": 2820, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2820", "body": "Adding a 4 second timeout to deep check probes. Default value was 1 second. That causes frequent service restarts under heavy workload"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2820#pullrequestreview-1092280843", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2820#pullrequestreview-1092299355", "body": ""}
{"title": "Clear the invite form after submitting", "number": 2821, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2821"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2821#pullrequestreview-1092353632", "body": ""}
{"title": "Dont count messages in archived threads", "number": 2822, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2822"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2822#pullrequestreview-1092418472", "body": ""}
{"title": "[BREAKS API ON MAIN] Minor POC updates", "number": 2823, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2823"}
{"title": "Retry on 401 from getPerson", "number": 2824, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824", "body": "Running into situation where RefreshAuth successfully 200s but getPerson 401s within the same try catch block.\nThis could happen if getPerson is called after the authToken from refreshAuth expires (Mac goes to sleep?)\nFixes:\n* catch errors on getPerson\n* check for refreshAuth 401 specifically"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092463338", "body": ""}
{"comment": {"body": "hacky \ud83d\ude2c ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r959990943"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092466956", "body": ""}
{"comment": {"body": "would it be better to wrap the `updateAuthState` call with a try-catch instead?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r959993480"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092530048", "body": ""}
{"comment": {"body": "updateAuthState is already wrapped by a try catch within RefreshAuth.\r\nThe issue is that two API requests are technically made within this try fetch. the refresh API & getPerson API", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r960038154"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092530656", "body": ""}
{"comment": {"body": "> \r\n\r\nYeah :( No better way to verify *what* request was made from the response model. This is all handled within fetch + codegen.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r960038583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092540336", "body": ""}
{"comment": {"body": "My point is that the getPerson failure **must** be swallowed in all cases when called from within refreshAuth. So if we just try-catch the updateAuthState and never propagate the exception up from that block, then the user cannot be logged out.\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/dd0306df-6e65-490e-85b5-59abe0907017?message=3314a1e5-3ece-4066-a468-89465540a47e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r960045623"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092545341", "body": ""}
{"comment": {"body": "I don't think we want to swallow the getPerson failure within refreshAuth.\n\nIf the refreshAuth API is successful & the getPerson fails (current bug), we want to retry the refreshAuth process.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/dd0306df-6e65-490e-85b5-59abe0907017?message=3b358f2a-de2a-4d90-9d81-56b745f587e7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r960049283"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092550724", "body": "lgtm. @matthewjamesadam might want to take a look?"}
{"comment": {"body": "Ok, I get it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r960053527"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092560024", "body": ""}
{"comment": {"body": "Use log instead of console?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r960060278"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092562850", "body": ""}
{"comment": {"body": "We should add a comment here explaining what's happening (ie the scenario this happens in, and where this will get caught and how that refreshes the token).  IT's pretty challenging to follow the flow of this code otherwise.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r960062459"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092563558", "body": ""}
{"comment": {"body": "Using logger within shared AuthStore is somewhat problematic as Logger may not be initialized at this point...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r960062951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#pullrequestreview-1092565094", "body": ""}
{"comment": {"body": "This feels like we should be having separate try/catch blocks: one for the refresh operation, and for updateAuthState.  With the way the code is structured here, we're trying to deduce which part of the process we're in after catching, which feels wrong.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2824#discussion_r960064045"}}
{"title": "[BREAKS API ON MAIN] Update PRMessageUnread with PUT operation", "number": 2825, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825", "body": "Follow up to https://github.com/NextChapterSoftware/unblocked/pull/2795\nAfter speaking with Ben, we do not want to have any UI for unread status in GH at this time.\nWe have the following options:\n~~1. Keep the planned APIs and add an updatePullRequestMessageUnread which acts similar to updateThreadUnread. This is what's in the current PR.~~\n\nRemove getPullRequestThreadUnread. updatePullRequestMessageUnread behaviour changes and is only used to mark as read."}
{"comment": {"body": "Vote for 2, we can add back later if needed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#issuecomment-1233405250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-1092521804", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-1092524688", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-1092526110", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-1092526975", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-1092531000", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-1092531094", "body": ""}
{"comment": {"body": "Is this going to be used by the web extension, or VSCode?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#discussion_r960038898"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-1092536802", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-1092536994", "body": ""}
{"comment": {"body": "Is this when we scroll through PRs in GitHub.com?  Or some other user action?  I'm not sure \"I viewed this in GitHub.com\" is a good indication of product engagement?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#discussion_r960043166"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-1092538003", "body": ""}
{"comment": {"body": "\u2191 the GitHub diff is messed up. This API is unchanged.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a7a185eb-755e-4cad-ac25-d305b634cc00?message=6ae7d9fa-5496-4f8d-919e-fbc0c10a63e2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#discussion_r960043935"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2825#pullrequestreview-**********", "body": ""}
{"title": "Our current openjdk vendor had deprecated their images", "number": 2826, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2826", "body": "\nRedhat has recommended to move away from their images to those provided by other providers.\nSince on github we're using Temurin jdks for building our packages, we should be using temurin for our runtime environments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2826#pullrequestreview-**********", "body": ""}
{"title": "Refactor channel poller", "number": 2827, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2827", "body": "This is a refactoring that should not change client behaviour at all, hopefully.\nThe point of this refactoring is to change ChannelPoller so that each channel can return a series of last-modified values, one for each channel ID, not just a single last-modified value.  We will need this for bulk thread fetching.  This also makes sense structurally: the set of last-modified values (ie the channel IDs) is really determined by the values returned in the GET calls.\nMy next PR will refactor the thread stores to better use this, with bulk thread loading."}
{"comment": {"body": "> So we no longer want to get the incremental changes based on modifiedSince? But instead, refetch the entire data set?\r\n\r\nFor the two stores that changed (team members and repos) I've changed them back to do incremental updates... so should behave the exact same as before.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2827#issuecomment-1234620414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2827#pullrequestreview-1092573457", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2827#pullrequestreview-1092594715", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2827#pullrequestreview-1092601029", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2827#pullrequestreview-1093845561", "body": "So we no longer want to get the incremental changes based on modifiedSince? But instead, refetch the entire data set?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2827#pullrequestreview-1093848979", "body": ""}
{"title": "Handle getSourceMarks paging when there are zero sourcemarks in the repo", "number": 2828, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2828", "body": "problem\nProblem was that we were returning a collection with no X-Unblocked-Last-Modified header.\nchange\nNow we return a header with a cursor at the start of any possible collection.\nresult\nvscode client is now making poller requests every second for this channel with opaque cursor\njson\n{\n  \"channel\": \"/sourceMarks?repoIds=7556968d-cfa6-4c01-bf24-d4b230947384\",\n  \"ifModifiedSince\": \"eyJtb2RpZmllZEF0IjoiMTk3MC0wMS0wMVQwMDowMDowMFoiLCJtYXJrSWQiOm51bGx9\"\n}\n... which base64 decodes as:\njson\n{\n  \"modifiedAt\": \"1970-01-01T00:00:00Z\",\n  \"markId\": null\n}"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2828#pullrequestreview-1092583382", "body": ""}
{"comment": {"body": "It can be null when there are zero sourcemarks in the collection.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2828#discussion_r960077880"}}
{"title": "Add unread filter for dashboard", "number": 2829, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2829", "body": "\n"}
{"comment": {"body": "Added  context menu for each thread summary item:\r\n\r\n<img width=\"1503\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/187977816-de72842b-460c-48e0-9d85-5bbb07a0026c.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2829#issuecomment-1234584616"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2829#pullrequestreview-1093830904", "body": ""}
{"comment": {"body": "Is this never necessary?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2829#discussion_r960949043"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2829#pullrequestreview-1093833278", "body": ""}
{"comment": {"body": "Yeah the prop definition is basically the type discriminator between the two prop types. The `never` just makes it so that the external use case doesn't have to explicitly pass it in when it's not necessary", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2829#discussion_r960950597"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2829#pullrequestreview-1093838106", "body": ""}
{"comment": {"body": "If we have a bool getter, add a setter as well? That way we can move away from strings/ toString()", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2829#discussion_r960953847"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2829#pullrequestreview-1093841756", "body": ""}
{"title": "Fix Lint", "number": 283, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/283", "body": "Not sure how these got past CI"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/283#pullrequestreview-876745138", "body": ""}
{"title": "Add slack threads", "number": 2830, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2830"}
{"title": "Improve lucene slack search", "number": 2831, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2831"}
{"title": "fix helm chart path", "number": 2832, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2832", "body": "Copy paste mistake in helm chart path."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2832#pullrequestreview-1092733477", "body": ""}
{"title": "Fix alb paths", "number": 2833, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2833"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2833#pullrequestreview-1092796495", "body": ""}
{"title": "Client bits for KaySync", "number": 2834, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834", "body": "Client bits with API request commented out.\nUses intersection observer to determine which discussions are visible on screen.\nAfter n (currently 2) seconds, we trigger API request to trigger read status.\nClient is somewhat smart in that it will try to send an event for the \"latest\" message in a discussion.\nFixes UNB-583"}
{"comment": {"body": "\r\nhttps://user-images.githubusercontent.com/1553313/188029715-26185abd-796b-441c-ae5b-2cb0811b7dcd.mp4\r\n\r\nIf you focus on the third discussion in the sidebar (Is this going to be used by...),\r\n you will notice that it disappears once we view it on the main page.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#issuecomment-1234909154"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-1094166372", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-1094304378", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-1094313581", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-1094314422", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-1094314767", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-1094315825", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-1098056858", "body": ""}
{"comment": {"body": "The overall way this code is put together isn't obvious at first glance, it took me a bit of work to figure out how observer, callback, and the timer work together.  Might be worth adding some comments?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#discussion_r964033326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2834#pullrequestreview-**********", "body": ""}
{"title": "adding telemetry service to handle /logs", "number": 2835, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2835", "body": "Added service account for telemetry service\nAdded telemetry service as a replica of API service which handles /logs endpoint only \nSince logs are not mission critical this change does not require disabling prod deploys."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2835#pullrequestreview-**********", "body": ""}
{"title": "add a read replica to dev rds", "number": 2836, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2836"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2836#pullrequestreview-**********", "body": ""}
{"title": "Add ability to override environment configuration", "number": 2837, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2837"}
{"title": "Fix source mark store lookup interaction", "number": 2838, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2838", "body": "Regression introduced in #2764."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2838#pullrequestreview-1093891207", "body": ""}
{"comment": {"body": "genuinely do not understand how these generate different results.\r\n\r\nIt works, so feel free to merge @matthewjamesadam, but I'd really like to understand this better.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2838#discussion_r960989938"}}
{"title": "Server bits for KaySync", "number": 2839, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839", "body": "This also fixes UNB-542"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1093912886", "body": ""}
{"comment": {"body": "This is the change that fixes UNB-542. Basically, we check the existing `ThreadUnread.lastReadMessage` and see if its `createdAt` is less than the `createdAt` for `latestReadMessage` (provided by the client). We only update the `ThreadUnread` if the `latestReadMessage` is newer than `ThreadUnread.latestReadMessage`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961003833"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1093930802", "body": ""}
{"comment": {"body": "Totally optional. One alternative is to define a `PrCommentId` _value class_, then use `@Serializable` to magically convert from int to string-back value class during deserialization from webhook and api payloads. Eliminates a bunch of casting code.\r\nhttps://kotlinlang.org/docs/inline-classes.html", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961015956"}}
{"comment": {"body": "Need to also test cases where:\r\n1. member does not have an unread entry for the thread; expect that no unread is created\r\n2. member has an unread entry for the thread already at a later message; expect no change", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961022344"}}
{"comment": {"body": "assert that modifiedAt has not changed -- saves us some pusher cycles", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961050540"}}
{"comment": {"body": "Add a test before this line to assert that `setLatestReadMessage` has no effect (regardless of the parameters passed to it) when there is no thread unread for this member.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961051622"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1093994018", "body": ""}
{"comment": {"body": "Actually, this is handled at store layer. All good", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961058134"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1094013442", "body": ""}
{"comment": {"body": "Fixed with https://github.com/NextChapterSoftware/unblocked/pull/2839/commits/f9a152600c42bff0499482c7de253ff9cbbebe98", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961070971"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1094013599", "body": ""}
{"comment": {"body": "Fixed with https://github.com/NextChapterSoftware/unblocked/pull/2839/commits/6c00cc242574137ac9873827c81d2a15898d1cb0", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961071091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1094013803", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1094054323", "body": ""}
{"comment": {"body": "I'm going to need to take a closer look at this. I tried converting all `id` fields to \r\n\r\n```\r\n@JvmInline @kotlinx.serialization.Serializable\r\nvalue class GitHubId(val value: String)\r\n```\r\n\r\nbut am getting deserialization issues\r\n\r\n```\r\n    kotlinx.serialization.json.internal.JsonDecodingException: Unexpected JSON token at offset 101: Expected quotation mark '\"', but had '1' instead at path: $.id\r\n    JSON input: .....pulls/2673\",\r\n      \"id\": 1030573631,\r\n      \"node_id\": \"PR_kwDOGgjkzs.....\r\n```\r\n\r\nSeems like it doesn't like converting from int to string, but I might need to add that deserializer myself.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961098435"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1094054852", "body": ""}
{"comment": {"body": "I'm inclined to do this in a separate PR, its touching a lot of files.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961098799"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1094118827", "body": ""}
{"comment": {"body": "yeah, you need custom deserializer function\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6d8147a6-bed5-4a55-b50f-8bbf221e5dd1?message=fa47f0d0-1c61-45d2-9baf-3deafbcd2926).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#discussion_r961142655"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2839#pullrequestreview-1094120071", "body": ""}
{"title": "Ckd refactor ECR stacks", "number": 284, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284", "body": "Refactored cross environment stacks (mainly ECR)\n- Changed build configs to create standard ECR config\n- Added a new stack for ECR replication config\n- Moved existing replication policy from compute stack to the new stack for replicated repos\n- Changed existing ECR registry stack to use new config class for ECR\n- Added lambda funtion to cleanup old images in replicated ECR repos. (AWS does not reflect lifecycle policies in replicated repos)\n- Deleted ComputeStack since we no longer need it\n\nAll changes have been deployed to Dev, Prod and SecOps account"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#pullrequestreview-*********", "body": ""}
{"comment": {"body": "This si cool :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#discussion_r802177317"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#pullrequestreview-*********", "body": ""}
{"comment": {"body": "But I have a question.\r\nInstead of python can we stick to nodejs. :)\r\nI'd prefer our language stack is largely the same.\r\n\r\nBut this is fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#discussion_r802177964"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#pullrequestreview-*********", "body": ""}
{"comment": {"body": "is value an any type or an array/string?\r\nThere is the possibility it might be something else which will cause this length call to crash.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#discussion_r802179066"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#pullrequestreview-876808344", "body": "With comments..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#pullrequestreview-876921938", "body": ""}
{"comment": {"body": "I literally took the AWS provided sample code https://aws.amazon.com/blogs/compute/automated-cleanup-of-unused-images-in-amazon-ecr/\r\n\r\nand just refactored it to do what we need. I mean we can switch it to python but honestly this file shouldn't change for years to come", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#discussion_r802267850"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#pullrequestreview-876922474", "body": ""}
{"comment": {"body": "Type for value will always be `Array<EcrReplicationTarget>` since it's being called as validation on it. We also are setting a default `[]` as the value so that length function should hold up and not cause any issues. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#discussion_r802268282"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#pullrequestreview-876922920", "body": ""}
{"comment": {"body": "Here's my promise...if we end up adding any other infra management lambda I promise to convert this to Nodejs ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/284#discussion_r802268605"}}
{"title": "Do not update schemas for readonly replicas", "number": 2840, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2840"}
{"title": "SuppressMoreHoneycomb", "number": 2841, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2841", "body": "suppress honeycomb\nupdate"}
{"title": "Dont use window location", "number": 2842, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2842", "body": "bug reported "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2842#pullrequestreview-1093995559", "body": ""}
{"title": "Pusher now uses readonly rds instance in prod", "number": 2843, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2843", "body": "Tested in dev:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2843#pullrequestreview-1093982140", "body": ""}
{"title": "Revert \"Pusher now uses readonly rds instance in prod (#2843)\"", "number": 2844, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2844", "body": "This reverts commit 9e26060260d34a914c5db4afc515de5153b56740."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2844#pullrequestreview-1093989677", "body": ""}
{"title": "Add friendly signup names to sender email", "number": 2845, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2845"}
{"title": "update welcome email", "number": 2846, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2846"}
{"title": "Address other welcome email", "number": 2847, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2847"}
{"title": "Unblocked Code lens", "number": 2848, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848", "body": "Basic code lens support\n\n"}
{"comment": {"body": "Some nuances with the data loading that aren't 100% correct.\r\nThis is currently hidden behind a feature flag (enabled in dev) while we figure that part out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#issuecomment-1235803125"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1094150703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1094151210", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1094152368", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1094158946", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1094159501", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095073811", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095381871", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095382140", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095382265", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095383342", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095384302", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095385453", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095385755", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095386659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095387587", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095388645", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095388968", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095389490", "body": ""}
{"comment": {"body": "Yeah I can see that we'll need to make a better API for this.  I'll think about how to separate the SM/thread stuff out from where it is now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#discussion_r962040109"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095389500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095391002", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095394554", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095395342", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095401236", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2848#pullrequestreview-1095413411", "body": ""}
{"title": "update", "number": 2849, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2849"}
{"title": "Move to kotlinx serialization", "number": 285, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/285", "body": "This pr does the following:\n1. Update generators to using kotlinx serialization.\n2. Add custom serializers for all the data types we need.\n3. Update code and tests to use kotlinx serialization."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/285#pullrequestreview-876794811", "body": "ooooooweeeeee"}
{"title": "Reduce dev honeycomb sampling", "number": 2850, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2850"}
{"title": "more honeycomb reductions", "number": 2851, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2851"}
{"title": "more sampling", "number": 2852, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2852"}
{"title": "Update honeycomb sample rates per environment.", "number": 2853, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2853"}
{"title": "Sourcemark engine startup ordering changes", "number": 2854, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2854", "body": "changes\n1. wait for sourcemark store to download all marks before looking up marks for file.\n2. wait for partial-recalculation to complete before run full-recalculation. previously\n   they both ran concurrently and the event loop was filled with full-recalculation jobs first.\n3. free the onSourceMarksInitialized array which was misused because it was misleading."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2854#pullrequestreview-1095161620", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2854#pullrequestreview-1095162773", "body": ""}
{"comment": {"body": "wait for partial-recalculation to complete before run full-recalculation. previously they both ran concurrently and the event loop was filled with full-recalculation jobs first. this meant that partial-recalculation was not actually fast.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2854#discussion_r961881087"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2854#pullrequestreview-1095163491", "body": ""}
{"comment": {"body": "This is the main bug fix. Cheers for catching this @matthewjamesadam !", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2854#discussion_r961881560"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2854#pullrequestreview-1095363588", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2854#pullrequestreview-1095363654", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2854#pullrequestreview-1095365627", "body": ""}
{"title": "Move out telemetry code from apiservice", "number": 2855, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2855", "body": "Confirmed deployed:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2855#pullrequestreview-1095174091", "body": ""}
{"title": "Proxyman can track local service stack requests", "number": 2856, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2856", "body": "Background\n\n\n\n\nResult\nRequests appear to target localhost.proxyman.io:\n\nThis works because localhost.proxyman.io is an alias for loopback address:\nsh\n$ host localhost.proxyman.io\nlocalhost.proxyman.io has address 127.0.0.1"}
{"comment": {"body": "@richiebres i love you, but please update frontend docs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2856#issuecomment-1235753326"}}
{"comment": {"body": "> @richiebres i love you, but please update frontend docs.\r\n\r\nno longer needed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2856#issuecomment-1235766468"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2856#pullrequestreview-1095194251", "body": ""}
{"title": "Introduce more efficient way to get files changed since a commit", "number": 2857, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2857"}
{"title": "SourceMark full recalculation running in VSCode is kinder to the local system", "number": 2858, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858", "body": "Changes\n\nThrottles itself to limit concurrent IO to filesystem and Git\nOptimize data-locality by processing according to file path.\n\nResult\n\nCPU never goes above 30%\nlooks to be using 2 out of 8 performance cores on my Mac"}
{"comment": {"body": "I just want to say that I'm glad to see you're taking the local system's feelings into account.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#pullrequestreview-**********", "body": ""}
{"comment": {"body": "There's a `PromiseUtils.wait` for this I think", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#discussion_r961969805"}}
{"comment": {"body": "I'm confused about this... you have a 2-dimensioned array (SourceMark[][]), so effectively an array-of-arrays.  You're chunking on the topmost array, for the size of each item in the array, but I think this means you end up chunking on the wrong set?  Did you mean to flatten the chunking array first?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#discussion_r962025494"}}
{"comment": {"body": "(Sorry this is maybe confusing, I can show on a whiteboard if it's not clear)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#discussion_r962027715"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#pullrequestreview-1095409325", "body": ""}
{"comment": {"body": "ah, thanks, will fix in follow up. couldn't find it earlier - knew we must have had a utility somewhere\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/fc20939c-2e55-4600-9920-b71d7412850a?message=7d2386a7-d9a8-481b-b79a-a6e33489e612).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#discussion_r962054583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#pullrequestreview-1095409981", "body": ""}
{"comment": {"body": "Yeah it's messed up, but correct, I promise :)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a308969e-60af-46d8-b06a-19e95d6d8618?message=b7d8fcc4-645d-496e-b6aa-48950e98cd55).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#discussion_r962055107"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#pullrequestreview-1095412834", "body": ""}
{"comment": {"body": "fixed here\n\nhttps://github.com/NextChapterSoftware/unblocked/pull/2861\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/fc20939c-2e55-4600-9920-b71d7412850a?message=116547d3-a20e-4975-aad4-3df79f51306c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2858#discussion_r962057418"}}
{"title": "Fix telemetryservier crashing", "number": 2859, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2859"}
{"title": "Move to kotlinx serialization for graphql", "number": 286, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/286", "body": "Jackson is dead.\nI repeat, Jackson is dead."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/286#pullrequestreview-876800796", "body": "That easy huh"}
{"title": "Add unreads filter to vscode", "number": 2860, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1097948414", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1097971634", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1098000360", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1098023772", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1098026739", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1098028481", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1098028641", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1098033511", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1098068655", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1098076798", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2860#pullrequestreview-1098235647", "body": ""}
{"title": "Use PromiseUtils wait", "number": 2861, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2861"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2861#pullrequestreview-1095413006", "body": ""}
{"title": "Add bulk-loading thread store", "number": 2862, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2862", "body": "Fixes UNB-590\nAdd a bulk-loading thread store (for now called ThreadStore).\nRight now this is only used by the TextEditorSourceMarks code (ie, the editor gutters and the \"insights in current file\" panels).  The idea is that all loading of groups of individual threads (in discussion view and sidebar views) will eventually use this, so that there is only one place that is polling and fetching individual threads.\nThe goal is that this store will be a singleton responsible for fetching, caching, and distributing thread data everywhere.\nThere are a few moving parts here:\n\nThreadStore is the main store.  It has a single public method: getStream, which returns an object (ThreadListStream) containing a stream of threads, plus some properties/methods for controlling the stream.  Right now the only thing you can control is the set of threads being streamed, but eventually this could have paging controls and such.\nThreadStore contains a series of TeamThreadStores, one per team.  TeamThreadStore does all of the actual fetching and channel polling for thread data for a single team.  It tracks which threads have been requested by any stream, and fetches data as needed.\nThreadStore also contains a series of ThreadListStream objects, so that all fetched thread data can be delivered to the streams that need them.\n\nThis is only the first bit.  Upcoming PRs will add:\n* Unit tests -- this should be relatively easy to unit-test, given a mock channel poller and bulk-thread loading API\n* Caching -- right now, when a stream is shut down, all data loaded from that stream will be dropped.  Adding a cache to this should be relatively easy.\n* Using this in other contexts: Discussion view and sidebar."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2862#pullrequestreview-1097879950", "body": "Overall makes sense."}
{"comment": {"body": "Is it ever expected that sourceMarkId === undefined?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2862#discussion_r963932917"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2862#pullrequestreview-1098011224", "body": ""}
{"comment": {"body": "It's unlikely, but it might happen if the set of sourcemarks and threads changes rapidly (ie you are editing a file) -- by the time we fetch the thread data, we may not longer care about that thread.  So in that case it's fine to just drop it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2862#discussion_r964000964"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2862#pullrequestreview-1098472596", "body": ""}
{"title": "DROP SourceMarkGroup and SourceMarkAccess models", "number": 2863, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863", "body": "Motivation\nThese models are no longer needed.\nMinor improvement impact:\n- saves some space\n- saves a few DB queries and creates\nRelated to this:\n\nPlan\nPrev change: (#2879)\n1. change the DELETE cascade to SET_NULL for SourceMarkGroup references\n2. make references to SourceMarkGroup nullable, needed for above\n3. remove code that creates groups or queries groups\n4. add migration code to DROP SourceMarkGroup columns\n5. add migration code to DROP SourceMarkGroup table\nThis change:\n1. remove SourceMarkGroup columns schema\n1. remove SourceMarkGroup table schema\nThen, in this order:\n\n[x] Deploy & Run migration to drop columns and tables in LOCAL\n[x] Backup PROD\n\"Mahdi: I just kicked off a manual snapshot which took a couple of mins to create\"\n[x] pause prod deploy\n[x] Deploy to DEV\n[x] confirm that schema and data look ok in DEV\n[x] Run migration to drop columns and tables in DEV\n[x] confirm that schema and data look ok in DEV\n[x] unpause prod deploy\n[x] Deploy to PROD\n[x] Run migration to drop columns and tables in PROD"}
{"comment": {"body": "@davidkwlam would like to chat about how to stage this so that we can actually cleanly delete the old tables and columns via migration, as opposed to just leaking the legacy tables and columns.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#issuecomment-1236002950"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#pullrequestreview-1098031055", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#pullrequestreview-1098462585", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#pullrequestreview-1098463405", "body": ""}
{"comment": {"body": "Egads ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#discussion_r964337713"}}
{"comment": {"body": "What's the pattern for future migrations?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#discussion_r964338240"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#pullrequestreview-1099600808", "body": ""}
{"comment": {"body": "Tried to factor out some reusable utilities (see the changes to `SchemaManager.kt`), but since each migration can be different there's no general pattern.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#discussion_r965134464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#pullrequestreview-1104895739", "body": ""}
{"comment": {"body": "yo pete\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/dd234641-161d-4784-8383-4bc4fe0e6cb5?message=0e6a5de1-7d8f-4e3e-b0f0-e75e194cddbf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2863#discussion_r968977708"}}
{"title": "Add last message created at to admin thread view", "number": 2864, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2864", "body": "(From the beach  )"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2864#pullrequestreview-1096848006", "body": ""}
{"title": "Cache Sidebar expanded states", "number": 2865, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2865", "body": "This reverts commit 5d505cb07bdb47f8909273549a20c120cffa94c7.\nAccidentally pushed to main...\nPR to cache isExpanded state in VSCode sidebar.\nThis state does not sync up with the explorer tab.\n"}
{"comment": {"body": "This only does expansion, right?  Do we need to store the panel size too, otherwise that will be reset whenever you navigate away from our sidebar?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2865#issuecomment-1238363776"}}
{"comment": {"body": "Updated to handle panel size as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2865#issuecomment-1238581205"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2865#pullrequestreview-1097889048", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2865#pullrequestreview-1097890021", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2865#pullrequestreview-1098199063", "body": ""}
{"comment": {"body": "extract into var?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2865#discussion_r964133976"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2865#pullrequestreview-1098469767", "body": ""}
{"title": "WIP: move auth code out of api service", "number": 2866, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866", "body": "Moving auth service code out of API service and into a separate Gradle project"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866#pullrequestreview-1096929943", "body": ""}
{"comment": {"body": "remove", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866#discussion_r963249273"}}
{"comment": {"body": "let's avoid copy-paste please", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866#discussion_r963251601"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866#pullrequestreview-1097938047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866#pullrequestreview-1097938143", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866#pullrequestreview-1097938546", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866#pullrequestreview-1097938771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866#pullrequestreview-1142795362", "body": ""}
{"comment": {"body": "\n\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/74305bd5-48b5-450e-bdb0-6d25db27f016)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/cc470691-c220-43ed-aa32-f82275ef6f2f?message=d457b3e4-04d7-442c-b0c2-535a9bbeb803).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2866#discussion_r996004152"}}
{"title": "Thread queries return different content for same thread", "number": 2867, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2867", "body": ""}
{"comment": {"body": "going to merge this @matthewjamesadam, but review together tomorrow.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2867#issuecomment-1237679607"}}
{"comment": {"body": "Some thoughts:\r\n\r\n* mine/recommended does not use `getThread`, so at the moment this isn't an issue.  We likely will not use getThread at all pretty soon, as the idea is that we will generally use `fetchThreads` everywhere\r\n* The result for `fetchThreads`, at the moment, is sorted differently (by line occurrence in a file), so at the moment this isn't an issue, but will be pretty soon", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2867#issuecomment-1238356886"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2867#pullrequestreview-1096937031", "body": ""}
{"comment": {"body": "@matthewjamesadam here's a problem. for fetchThreads, what should we return? with rank? with unread?\r\n\r\nYou can see why I wanted to return the same thing in all cases.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2867#discussion_r963254307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2867#pullrequestreview-1096952750", "body": ""}
{"comment": {"body": "Same as fetchThreads here in getThread. Since getThread is called by both Mine and Recommended, it seems like we'd have no choice but to return both unread and rank.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2867#discussion_r963265443"}}
{"title": "Revert \"Thread queries return different content for same thread\"", "number": 2868, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2868", "body": "Reverts NextChapterSoftware/unblocked#2867\nBackground\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1662432593844369?thread_ts=1662402012.678769&cid=C02HEVCCJA3\nProblem this addresses\nClients cannot share caches if the content returned from different thread API endpoints is different for the same key (thread ID).\nClient change needed\nhttps://github.com/NextChapterSoftware/unblocked/blob/21c8c5935b78423abd4c029056f364b5fef7c2a1/shared/stores/ThreadListStore.ts#L35-L66\nTimeline\nWe should merge this, but first, we need to adjust the client sorting and wait for that change to propagate sufficiently to the population. Wait for this change to be adopted: https://github.com/NextChapterSoftware/unblocked/pull/2880."}
{"comment": {"body": "This is the first version that has the client change, so need to wait for [majority of] clients to adopt at least this version or later.\r\n```sh\r\ngit tag --contains 4fe58e7 | sort | head -1\r\nv-389\r\n```\r\n\r\nLatest versions:\r\nhttps://admin.prod.getunblocked.com/versions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2868#issuecomment-1244956037"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2868#pullrequestreview-1097738941", "body": "let me know when to merge this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2868#pullrequestreview-1127394309", "body": "Manthis makes me sad."}
{"title": "Wire up TLC create, update, and delete operations", "number": 2869, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2869"}
{"title": "Implement install state API", "number": 287, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/287", "body": "Summary\nThis PR implements the /install/state api. Following the flow laid out in . \nThe main business logic can be inferred from Install.kt\nThis should be enough to plumb the install flow through the current client demo.\nImplemented\n\nOrg specific install url based on repo url\nUser specific install url based on authenticated identity\nOrg installation status based on TeamMember association\n\nRemaining\n\nWebhook callback for new install\nPolling fallback for new installs <-- let's do this first\nTeamInstallation model\nTeamMember and Team creation after installation\nPossibly a client install completion API\nUtilize the commitSha parameter to detect forks"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/287#pullrequestreview-876816634", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/287#pullrequestreview-876907088", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/287#pullrequestreview-876921762", "body": ""}
{"comment": {"body": "Removed provider because it can now be inferred from the authenticated identity", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/287#discussion_r802267717"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/287#pullrequestreview-876922264", "body": ""}
{"comment": {"body": "I'm really not a fan of this but again bit by a bunch of opaque framework exceptions (serialization etc)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/287#discussion_r802268103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/287#pullrequestreview-877810084", "body": ""}
{"title": "Installer job tags repo with build number", "number": 2870, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2870", "body": "Part of the work to validate spec changes against previous non-obsolete Stable releases.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2870#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This should probably be a string instead of a number in the JSON, but I have no idea where this is consumed downstream, so not going to mess with it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2870#discussion_r963999370"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2870#pullrequestreview-1098122630", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2870#pullrequestreview-1098123915", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2870#pullrequestreview-1098126566", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2870#pullrequestreview-1098172402", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2870#pullrequestreview-1098187179", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2870#pullrequestreview-1098208251", "body": ""}
{"title": "Additional logging for auth issue", "number": 2871, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2871", "body": "Adding additional logs to narrow down auth errors.\n```\nERR [Extension Host] Refresh current person failed. Reverting previous auth changes ResponseError: Response returned an error code\n    at qa.request (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:900942)\n    at process.processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async qa.getPersonRaw (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:965875)\n    at async qa.getPerson (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:966000)\n    at async jn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1020227)\n    at async An (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1018956)\n    at async Mn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1019759)\nconsole.ts:137 [Extension Host] Refresh current person failed. Reverting previous auth changes ResponseError: Response returned an error code\n    at qa.request (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:900942)\n    at process.processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async qa.getPersonRaw (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:965875)\n    at async qa.getPerson (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:966000)\n    at async jn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1020227)\n    at async An (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1018956)\n    at async Mn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1019759)\ny @ console.ts:137\nlog.ts:313   ERR [Extension Host] Refresh Auth failed. 0 attempt. ResponseError: Response returned an error code\n    at qa.request (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:900942)\n    at process.processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async qa.getPersonRaw (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:965875)\n    at async qa.getPerson (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:966000)\n    at async jn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1020227)\n    at async An (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1018956)\n    at async Mn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1019759)\nconsole.ts:137 [Extension Host] Refresh Auth failed. 0 attempt. ResponseError: Response returned an error code\n    at qa.request (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:900942)\n    at process.processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at async qa.getPersonRaw (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:965875)\n    at async qa.getPerson (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:966000)\n    at async jn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1020227)\n    at async An (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1018956)\n    at async Mn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1019759)\ny @ console.ts:137\nnotificationsAlerts.ts:42 Fetching teams before auth: ResponseError: Response returned an error code\nn\n```"}
{"comment": {"body": "> semi-unrelated to this PR but could you maybe add a note or two explaining why it's necessary to revertAuthChanges ?\r\n\r\nHad a chat with Matt after the initial PR went in. Original thought was \"if we fail to get person, are we actually authenticated?\" Most likely not necessary . Will remove.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2871#issuecomment-1238642732"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2871#pullrequestreview-1098211454", "body": "semi-unrelated to this PR but could you maybe add a note or two explaining why it's necessary to revertAuthChanges ?"}
{"title": "Remove unnecessary attributes", "number": 2872, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2872"}
{"title": "Fix CI installer script", "number": 2873, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2873", "body": "problem\nsh\n$ git tag -l\nv-hub-undefined\nv-vscode-undefined\nchanges\nUse the context.runNumber instead, which comes from here:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2873#pullrequestreview-1098290669", "body": ""}
{"title": "Min OS version check in installer", "number": 2874, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2874"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2874#pullrequestreview-1098338174", "body": ""}
{"title": "Add logging parameters for rds", "number": 2875, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2875", "body": "Tested against dev/prod."}
{"title": "Add logging around database locks", "number": 2876, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2876"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2876#pullrequestreview-1098443994", "body": ""}
{"comment": {"body": "```suggestion\r\n        logger.debug(\"Database lock could not be acquired. Cannot execute synchronized database operation.\")\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2876#discussion_r964322865"}}
{"comment": {"body": "```suggestion\r\n        logger.debug(\"Database lock has been acquired. Executing synchronized database operation.\")\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2876#discussion_r964322982"}}
{"title": "TryAgain", "number": 2877, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2877", "body": "update\nUpdate"}
{"title": "Open the hub regardless of installer state", "number": 2878, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2878", "body": "This PR modifies the post-install hub popover behaviour as follows:\n1. When Hub launches, foregrounds installer if it is running. If not, pops open hub\n2. After foregrounding installer, waits for either the installer to terminate or go into the background, then pops open hub\nIn this demo please ignore the fact that the installer hasn't run, this is just to show how the new hub behaviour works assuming the installer has done its thing."}
{"comment": {"body": "> When Hub launches, foregrounds installer if it is running. If not, pops open hub\n\nDo you mean \"foregrounds the installer when it's complete\"?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2878#issuecomment-1239577742"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2878#pullrequestreview-1099555295", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2878#pullrequestreview-1099746537", "body": ""}
{"comment": {"body": "This will potentially pop up *any* random installer app that is running, won't it?  Not sure if it matters.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2878#discussion_r965236230"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2878#pullrequestreview-1099746821", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2878#pullrequestreview-1099748969", "body": ""}
{"comment": {"body": "More accurately, it will pop up ALL the running installers. Unfortunately there's no reasonable way to identify which one is ours. \n\n\n\nThe thinking here is that at least one will be ours, and if they click on another app it will resign all installer processes from active state, triggering the popup.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/092d600c-545f-4335-8a01-3ed75843f3fc?message=8b1b0bb5-7805-4dd7-9608-24415a55d632).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2878#discussion_r965237879"}}
{"title": "Remove SourceMarkGroup usage and prepare for drop", "number": 2879, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2879", "body": "Plan\nThis change:\n1. change the DELETE cascade to SET_NULL for SourceMarkGroup references\n2. make references to SourceMarkGroup nullable, needed for above\n3. remove code that creates groups or queries groups\n4. add migration code to DROP SourceMarkGroup columns\n5. add migration code to DROP SourceMarkGroup table\nNext change: (https://github.com/NextChapterSoftware/unblocked/pull/2863)\n1. remove SourceMarkGroup columns schema\n1. remove SourceMarkGroup table schema\nThen:\n1. Run migration to drop columns and tables"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2879#pullrequestreview-1098464930", "body": "Love it"}
{"title": "Add logic to page review threads and comments for a pull request", "number": 288, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/288", "body": "When we get all comment threads for a pull request, GitHub limits us to 100 of each per pull request. This PR adds logic to page when a PR exceeds those limits, producing all threads and comments for a PR given a PR number.\nNot the nicest code in the world...but there are tests!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/288#pullrequestreview-876838081", "body": ""}
{"title": "Unique sort for each thread listing", "number": 2880, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2880", "body": "See https://github.com/NextChapterSoftware/unblocked/pull/2868 for context.\nUp until now, all of the thread listings (mine, recommended, PR, archived) have shared a single sorting algorithm.  This worked fine up until recently, as we returned ThreadInfos with differing shapes depending on the API being called . We want to make all of the thread APIs return consistent data, so we need to sort differently for each list:\n\nMine/PR sorts by unread state, then by last-modified, then by ID\nRecommended sorts by rank, then by last-modified, then by ID\nArchived sorts by archival date, then by last-modified, then by ID\n\nOnce this ships out and all users have upgraded we can change our APIs to return consistent threads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2880#pullrequestreview-1099447026", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2880#pullrequestreview-1099447546", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2880#pullrequestreview-1099448688", "body": ""}
{"title": "Confetti blassssst", "number": 2881, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2881", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2881#pullrequestreview-1098507155", "body": ""}
{"title": "Update code lens to use new threads", "number": 2882, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2882", "body": "Update usage of threadsAPI and enable code lens"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2882#pullrequestreview-1098545915", "body": ""}
{"comment": {"body": "Pretty sure there's room for improvement here... Grabbed from `updateSourceMarks`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2882#discussion_r964399558"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2882#pullrequestreview-1114586796", "body": "lgtm but Matt should look over the text editor sourcemark code before merging"}
{"title": "Add anti-affinity to api service", "number": 2883, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2883", "body": "Adding anti-affinity to API service to help spread them over the maximum number of Kube nodes possible. I would like to first test this with one service and if all goes well do the same for rest."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2883#pullrequestreview-1099443410", "body": "I have an affinity for you."}
{"title": "Sharing video buttons", "number": 2884, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2884", "body": "\n\n\n"}
{"title": "adding anti-affinity for the rest of our services", "number": 2885, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2885", "body": "Same anti-affinity change but for all other services."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2885#pullrequestreview-1099539209", "body": ""}
{"title": "Revert \"Confetti blassssst\"", "number": 2886, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2886", "body": "Reverts NextChapterSoftware/unblocked#2881"}
{"comment": {"body": "Now i'm sad...\r\nWhy @pwerry WHY!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2886#issuecomment-1239677815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2886#pullrequestreview-1099558900", "body": ":("}
{"title": "VSCode button and message editor rounding", "number": 2887, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2887", "body": "Update button and editor styles in VSCode.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2887#pullrequestreview-1099607918", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2887#pullrequestreview-1099608472", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2887#pullrequestreview-1099608894", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2887#pullrequestreview-1099609462", "body": ""}
{"title": "Set ThreadUnreadModel.latestReadMessage to latestMessage if latestReadMessage is deleted and after latestMessage", "number": 2888, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2888", "body": "This was preventing Kim from Expo from marking a thread as being read.\nWhat happened was that the last message of a thread was deleted in GitHub. When we got the webhook, we correctly updated all ThreadUnreadModel.lastestMessage but we didn't check ThreadUnreadModel.latestReadMessage to see if that was still pointing to the deleted message.\nThis change should address that. Essentially, if latestReadMessage.createdAt > latestMessage.createdAt, that must mean latestReadMessage was deleted and we can safely assume latestReadMessage should be latestMessage (since if you've read a message that is more recent than latestMessage then you must've read latestMessage).\nWith this change, the updateThreadUnread operation should start working for the above edge case."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2888#pullrequestreview-1099639159", "body": ""}
{"comment": {"body": "This does not handle when `latestReadMessage` is deleted but comes before `latestMessage`, though", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2888#discussion_r965159345"}}
{"title": "Filter unreads on web extension", "number": 2889, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889", "body": "Revamp sidebar per new designs:\n\nAdd context menu to each row:\n\nAdd unreads dropdown:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099728509", "body": ""}
{"comment": {"body": "Make this a shared component?\r\nI think I've seen similar designs elsewhere for counters.\r\nSomething like this?\r\nhttps://primer.style/css/components/labels#counters", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r965223547"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099730492", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099730746", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099739795", "body": ""}
{"comment": {"body": "Could we move this into `SidebarUtils`? Just a consistent place where all the logic is handled. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r965231419"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099744636", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099746427", "body": ""}
{"comment": {"body": "Should this be a useRef instead? We don't want to re render when updated.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r965236157"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099760595", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099823552", "body": ""}
{"comment": {"body": "I think we do? The context clicked thread has a state:\r\n<img width=\"268\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/188981350-82045878-b253-45b2-b5ac-39fc94ff4f2e.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r965303154"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099825045", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099829891", "body": ""}
{"comment": {"body": "<img width=\"978\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/188982406-cf3c16d8-52ad-428b-82de-cabf11e45e29.png\">\r\n??\r\n\r\nBut either way I'm not convinced that this is necessary right now -- the only shared logic bit is show null if count === 0, otherwise the styling is different and this only appears in the sidebar for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r965307574"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099861478", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099912749", "body": ""}
{"comment": {"body": "Less about the logic. More about the style. Not necessary for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r965363846"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1099913843", "body": ""}
{"comment": {"body": "Missed that. \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r965364418"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1101278932", "body": ""}
{"comment": {"body": "Is this supposed to be in this PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r966332149"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1101282606", "body": ""}
{"comment": {"body": "In general, we should be catching errors from these async operations and either logging / propagating up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r966334363"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1101284867", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1101296426", "body": ""}
{"comment": {"body": "I wonder how this interacts with deleting a message.\r\nThis data will need to be updated locally (due to overlays) as well as on the backend.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r966339013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1101298711", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1101341601", "body": ""}
{"comment": {"body": "Any reason we don't just use the `showUnreads` property above this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r966369207"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1101342937", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1101538501", "body": ""}
{"comment": {"body": "Hmm we do this in a multiple places across clients so we'll need to fix it everywhere", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r966509361"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1101616867", "body": ""}
{"comment": {"body": "that's the dashboard flag -- I'm not sure we want to persist them across clients like this, and if we do we should do it across all the clients and not just web dashboard/web extension??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r966561137"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#pullrequestreview-1102760242", "body": ""}
{"comment": {"body": "Reusing the key should be fine since they're stored in different contexts in different apps.  I don't feel very strongly about it either way.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4a8a1ee9-cbde-4597-96a2-66940b13b61e?message=7ff6518d-1251-4889-92da-23b34b49fa1b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2889#discussion_r967371954"}}
{"title": "Add logic to page batch requests of pull request", "number": 289, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/289", "body": "Part 2 of https://github.com/NextChapterSoftware/unblocked/pull/288. Allows for paging the batch query."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/289#pullrequestreview-877001613", "body": ""}
{"comment": {"body": "This is an intermediate data class due to that fact that the batch query and the single PR query produce different classes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/289#discussion_r802326008"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/289#pullrequestreview-877810887", "body": ""}
{"comment": {"body": "(the generator produces separate classes, that is)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/289#discussion_r802911664"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/289#pullrequestreview-877816218", "body": ""}
{"title": "Additional Explorer States", "number": 2890, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2890", "body": "When user was either in non-auth state or non-installed state, explorers sidebars were broken.\nHooking into existing sidebar logic to render additional sidebar states.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2890#pullrequestreview-1099735593", "body": ""}
{"comment": {"body": "Do we know at this point if our initial login attempt succeeded?  ie do we `await` on a login initialization above?  Or do we need to sign up for an event or stream to know when to do this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2890#discussion_r965228531"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2890#pullrequestreview-1099737863", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2890#pullrequestreview-1099757656", "body": ""}
{"comment": {"body": "Awaiting `initializeAuth` should do the trick. There's a cascade of async operations that eventually updates the AuthStore's state (which is used by isAuthenticated)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0ad26a39-3e8f-4691-8c91-7fc684ed4d77?message=8e46ecf0-89be-4a11-9a97-481bdad7aaa6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2890#discussion_r965244236"}}
{"title": "All discussions are read", "number": 2891, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2891", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2891#pullrequestreview-1099686231", "body": ""}
{"title": "First stab at better schema updates", "number": 2892, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892", "body": "Add schema management based off platformVersion.\nFor local/ci development, we use the old methodology."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#pullrequestreview-1099848789", "body": "Would be nice to prevent a schema migration when the instance platform version is less than the latest deployed schema version. This prevents out of order deploys or restarts of legacy instances from rolling back newer schema changes."}
{"comment": {"body": "what if the `body()` failed within the locked region.\r\n\r\nI think we need to record that it `finished_ok` rather than just that it `finished`, so we need to:\r\n```kotlin\r\nrunCatching {\r\n  // ... migrate\r\n}.onSuccess {\r\n  completedOk = true\r\n}", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#discussion_r965322776"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#pullrequestreview-1099859938", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#pullrequestreview-1099861963", "body": ""}
{"comment": {"body": "Ah, maybe this doesn't matter as much, because this is only used for the old code path?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#discussion_r965329672"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#pullrequestreview-1099862915", "body": ""}
{"comment": {"body": "For PlatformVersionSchemaLockService, finished is only ever set if the body completes, otherwise it will be considered busy.\r\nAnd since we now have busy timeouts, it will eventually be retried.\r\n\r\nNo need for any additional state really.\r\nI've modified this function to only set isBusy to false after body().", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#discussion_r965330333"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#pullrequestreview-1099864201", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#pullrequestreview-1099864582", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#pullrequestreview-1099864929", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2892#pullrequestreview-1099944055", "body": ""}
{"title": "Add LowRelevance trait to ThreadPage", "number": 2893, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2893", "body": "Will let us see which threads were archived due to low relevance in the admin console"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2893#pullrequestreview-1099863137", "body": ""}
{"title": "Add ThreadStore tests", "number": 2894, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2894", "body": "Add some basic unit tests for ThreadStore.  This tests loading, channel push, and updating the thread listing.  I'll add more tests later as we integrate this with other parts of the client apps.\nBuilding mocks for this was a bit of a challenge.  I took the approach that the ThreadStore takes in a config object, representing the store's dependencies (channel poller, thread API, team member stream).  This lets us inject simple mocks for the tests easily, and the resulting tests work without needing odd timing hacks or anything.  The default config uses the dependencies you'd expect (the actual thread API, ChannelPoller, etc)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2894#pullrequestreview-1099911036", "body": ""}
{"comment": {"body": "fwiw, I think this is better than relying on jest.mock whenever possible.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2894#discussion_r965363428"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2894#pullrequestreview-1099911518", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2894#pullrequestreview-1099912953", "body": ""}
{"comment": {"body": "I do think this is a better pattern.  It can take a little longer to set up though.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/42170836-2bb4-4486-af58-caeab8f4a92e?message=af9e1aed-ca0a-4855-b9f4-d52263ae3ea7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2894#discussion_r965363897"}}
{"title": "Remove schema migration which has already completed", "number": 2895, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2895", "body": "2863 is done "}
{"title": "Fix team member page link in adminweb", "number": 2896, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2896", "body": "Some links were incorrectly showing the \"past members\"."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2896#pullrequestreview-1099878067", "body": ""}
{"title": "Add titles to pr filter button group", "number": 2897, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2897"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2897#pullrequestreview-1099903852", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2897#pullrequestreview-1099909734", "body": ""}
{"title": "Add LowRelevance trait to ThreadsPage", "number": 2898, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2898"}
{"title": "Use github build number and pass it to services.", "number": 2899, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2899", "body": "Going to be using build number for schmea update."}
{"comment": {"body": "Sorry, I keep creating work for you so feel free to tell me to piss off. But.. would be amazing got get these release numbers into Honeycomb?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2899#issuecomment-1240035356"}}
{"comment": {"body": "> Sorry, I keep creating work for you so feel free to tell me to piss off. But.. would be amazing got get these release numbers into Honeycomb?\r\n\r\nVery easy to do!!! :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2899#issuecomment-1240052362"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2899#pullrequestreview-1099947261", "body": ""}
{"title": "Setup unfinished stitches demo", "number": 29, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/29"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/29#pullrequestreview-849503126", "body": ""}
{"comment": {"body": "Just takes a generic string.\r\nI could have easily set theme.colors['white-100'] here as well and it would not have broken.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/29#discussion_r782366540"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/29#pullrequestreview-849503951", "body": ""}
{"comment": {"body": "No real type safety.\r\n\r\nIt has auto complete but will also allow for arbitrary strings.\r\n\r\nTyped as the following:\r\n\r\n```\r\n  export type Display =\r\n    | DataType.DisplayOutside\r\n    | DataType.DisplayInside\r\n    | DataType.DisplayInternal\r\n    | DataType.DisplayLegacy\r\n    | \"contents\"\r\n    | \"list-item\"\r\n    | \"none\"\r\n    | OnlyString;\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/29#discussion_r782367101"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/29#pullrequestreview-849517736", "body": ""}
{"comment": {"body": "<img width=\"388\" alt=\"CleanShot 2022-01-11 at 09 29 38@2x\" src=\"https://user-images.githubusercontent.com/1553313/148992163-50c63992-e361-4589-a450-6c24d6eba123.png\">\r\n\r\n<img width=\"485\" alt=\"CleanShot 2022-01-11 at 09 30 06@2x\" src=\"https://user-images.githubusercontent.com/1553313/148992212-3b4db9b2-0481-4b7c-b1a0-9be57b062150.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/29#discussion_r782376735"}}
{"title": "Move dev and prod certs to  AcmStack and refactor DnsStack", "number": 290, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/290", "body": "CDK code cleanup part 3 (DNS and Certs)\n- Added config to use AcmStack for new certs\n- New certs include subject alts for user-friendly domains and subdomains\n- Refactored DnsStack to create A and CName records from config. This is mainly intended for managing  CName user-friendly names\n- Cleaned up build config to move all DNS related configs under a single class. This touched most stacks.\nCerts have been generated and all necessary user-friendly DNS records have been created. My next PR will switch APIService from existing cert to new certs.\nRelated PR: https://github.com/NextChapterSoftware/unblocked/pull/291"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/290#pullrequestreview-877801838", "body": ""}
{"comment": {"body": "planning on getting rid of this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/290#discussion_r802905083"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/290#pullrequestreview-877802474", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/290#pullrequestreview-877803842", "body": ""}
{"comment": {"body": "Yep. Need to merge this first https://github.com/NextChapterSoftware/unblocked/pull/291/files\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/290#discussion_r802906519"}}
{"title": "Create MessageMentionModels for each @mentioned team member in GitHub ingested messages", "number": 2900, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2900", "body": "We are currently not creating these for @mentions in GitHub messages. This PR fixes that so that unread notifications get the @ treatment."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2900#pullrequestreview-1099929626", "body": ""}
{"title": "Handle collapsed sections for Notification Sync", "number": 2901, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2901", "body": "When a section is collapsed in a PR, our intersection observer will never observe it.\nAdd a mutation observer which restarts the intersection observer whenever there is a change to '.js_discussions' (scoped area where discussions are contained)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2901#pullrequestreview-1102980999", "body": ""}
{"title": "Remove no longer used property", "number": 2902, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2902"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2902#pullrequestreview-1099945142", "body": ""}
{"title": "Add more repo stats", "number": 2903, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2903", "body": "The motivation is to understand the dataset better for certain repos.\n"}
{"title": "Add service platform information to honeycomb", "number": 2904, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2904", "body": "Also fix auth service for local stack.\n"}
{"title": "Archive themis-continuous-integration-updater threads", "number": 2905, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2905", "body": "Maybe?"}
{"comment": {"body": "Going to do this instead https://chapter2global.slack.com/archives/C02VCS8L4R5/p1662657157423509?thread_ts=1662591126.673819&cid=C02VCS8L4R5", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2905#issuecomment-1247149759"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2905#pullrequestreview-1101079142", "body": ""}
{"comment": {"body": "don't think we need this clause:\r\n```\r\nThreadModel.archivedBy neq null\r\n```\r\n\r\n\r\nAlso, we need an is not deleted clause:\r\n```\r\n                            ThreadModel.isDeleted eq false,\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2905#discussion_r966197097"}}
{"title": "wrong logging context", "number": 2906, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2906"}
{"title": "Fix prod build number", "number": 2907, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2907"}
{"title": "Ensure we log build number", "number": 2908, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2908"}
{"title": "Centralize schema updates and ensure theyre all wrapped in same lock", "number": 2909, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2909", "body": "As our esteemed friend, @richiebres brought up, we're still doing some database locks (and in some cases, very inefficiently).\nIt does not make sense to do these operations outside of the schema manager seeing as they're all inter-related.\nFor example, triggers are dependent on stored procedures and tables being created.\nWe are now centralizing schema updates so that they're all contained in a singular lock.\nTESTING:\n1. Deleted local database and started all services concurrently via docker and confirmed that they all started correctly, databases were populated correctly etc."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2909#pullrequestreview-1100796540", "body": "Nice"}
{"title": "move API service to new SSL cert.", "number": 291, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/291", "body": "This is needed to fix SSL errors thrown in browser when using second level subdomains  e.g *.us-west-2.dev.getunblocked.com. \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/291#pullrequestreview-877805497", "body": ""}
{"comment": {"body": "Can you modify the scaffolding as well? \r\nWe're now using our own helm custom scaffolds to generate these things when creating a new service.\r\n\r\ni.e. \r\nhelm/scaffold/values-dev.yaml\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/291#discussion_r802907794"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/291#pullrequestreview-877809138", "body": ""}
{"comment": {"body": "Done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/291#discussion_r802910409"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/291#pullrequestreview-877810645", "body": ""}
{"title": "Show number of threads created by author", "number": 2910, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2910"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2910#pullrequestreview-1101018371", "body": ""}
{"title": "Fix team member page", "number": 2911, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2911", "body": "Inverted the logic by mistake."}
{"title": "Cleanup schema managaer", "number": 2912, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2912"}
{"title": "Admin console schema lock cleanup", "number": 2913, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2913"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2913#pullrequestreview-1101130615", "body": ""}
{"title": "Provide an API to get the top-N files with source marks.", "number": 2914, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2914", "body": "Can be used to point users to files with lot's of threads.\nReturns this:\njs\n[\n    { file: FilePath { value: 'src/game/actions.ts' }, count: 4 },\n    { file: FilePath { value: 'src/game/game2048.ts' }, count: 4 },\n    { file: FilePath { value: 'src/helpers/syntax.ts' }, count: 3 },\n    { file: FilePath { value: 'src/helpers/random.ts' }, count: 2 },\n    { file: FilePath { value: 'src/helpers/eventCache.ts' }, count: 2 },\n    { file: FilePath { value: 'README.md' }, count: 1 },\n    { file: FilePath { value: 'src/game/enums.ts' }, count: 1 },\n]"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2914#pullrequestreview-1101238251", "body": "Love it"}
{"title": "Convert mentions in ingested comments to inline elements", "number": 2915, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2915", "body": "This addresses the issue where @mentions in comments created from GitHub don't get the same treatment as @mentions in comments created from Unblocked."}
{"title": "Add ability to update pull request description", "number": 2916, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2916", "body": "Implements the updatePullRequest operation"}
{"title": "Update Error messages on git operations", "number": 2917, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2917", "body": "Show confirmation or error messages after git operations\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2917#pullrequestreview-1101410678", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2917#pullrequestreview-1101425388", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2917#pullrequestreview-1101444834", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2917#pullrequestreview-1102982183", "body": ""}
{"comment": {"body": "\"This change may no longer exist\" -- `Perhaps` is a bit of unusual wording for this?  @benedict-jw ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2917#discussion_r967527428"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2917#pullrequestreview-1102982243", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2917#pullrequestreview-1102984146", "body": ""}
{"comment": {"body": "We could spell out what \"change may no longer exist\" means?\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/80a1ed14-653b-46c5-8c87-73124b5fc1db?message=f81f6424-a6d7-49f0-86ff-f1528df38b20).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2917#discussion_r967528764"}}
{"title": "Do not update schema if later buildnumber operation has been registered", "number": 2918, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2918"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2918#pullrequestreview-**********", "body": ""}
{"title": "upgrade detekt", "number": 2919, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2919"}
{"title": "following standards in kube world, I have renamed health probe endpoints", "number": 292, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/292", "body": "Rename health probe endpoints to include __ at the beginning of their endpoint path. This is an unwritten standard in Kube world. I'll updated the APM probes once this has been deployed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/292#pullrequestreview-877845767", "body": ""}
{"title": "Grid and Stage layouts for main window", "number": 2920, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2920"}
{"title": "Fix rendering issues", "number": 2921, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2921"}
{"title": "Improve generate-api call", "number": 2922, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2922", "body": "1 gradle invocation rather than 2.\nOLD:\n```\nrasharab@Rashins-MacBook-Pro api % time make generate-api\n/Users/<USER>/chapter2/unblocked/api//../gradlew openApiGenerateShared\nBUILD SUCCESSFUL in 1s\n9 actionable tasks: 9 up-to-date\n/Applications/Xcode.app/Contents/Developer/usr/bin/make -C /Users/<USER>/chapter2/unblocked/api/../common generate-common-protos\n/Users/<USER>/chapter2/unblocked/common//../gradlew generateProto\nBUILD SUCCESSFUL in 1s\n11 actionable tasks: 11 up-to-date\nmake generate-api  1.33s user 0.17s system 47% cpu 3.152 total\n```\nNEW:\n```\nrasharab@Rashins-MacBook-Pro api % time make generate-api\ncd /Users/<USER>/chapter2/unblocked/api/.. && ./gradlew openApiGenerateShared generateProto\nBUILD SUCCESSFUL in 1s\n26 actionable tasks: 26 up-to-date\nmake generate-api  0.63s user 0.08s system 46% cpu 1.515 total\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2922#pullrequestreview-1101533143", "body": ""}
{"title": "Remove anchor sourcemark concept", "number": 2923, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2923", "body": "Never used.\nCleans up server, client and API spec.\nNon-breaking."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2923#pullrequestreview-1101748169", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2923#pullrequestreview-1102541750", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2923#pullrequestreview-1103475776", "body": ""}
{"title": "Drop SourceMarkModel isAnchor field", "number": 2924, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2924"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2924#pullrequestreview-1101541949", "body": ""}
{"title": "Ensure we do not zip and tar", "number": 2925, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2925", "body": "When we generate fatjars, the plugin will generate zips and tars that we do not need."}
{"title": "Test remote cache", "number": 2926, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2926"}
{"title": "Add team members to invite dropdown", "number": 2927, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2927", "body": "\n\nRefactored out the list of team members into its own component from the current MentionDropdown\nRefactored out the keyDown listener to the TeamMemberDropdown component \nImplemented using the existing zustand mention store but we should probably refactor this out (can be done separately)"}
{"comment": {"body": "Build was failing by not updating the web extension code, so added to the web extension form as well:\r\n<img width=\"470\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/189449924-6662647c-cd98-4d98-9203-7f9f2129e0c2.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2927#issuecomment-1242504094"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2927#pullrequestreview-1103008743", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2927#pullrequestreview-1103010147", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2927#pullrequestreview-1103010501", "body": ""}
{"title": "Fix SM engine lookup for file when any SM region is edited in an uncommitted file", "number": 2928, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2928", "body": "fixes: "}
{"title": "Highlight VSCode code blocks", "number": 2929, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929", "body": "Highlights VSCode code blocks\nWorks well for single lines\n\nFor multi lines, snippets currently do not include buffer so it highlights the entire range... \nEither we add more buffer or we remove highlighting.\n\nRelated to shiki changes here:\nhttps://github.com/matthewjamesadam/shiki/pull/1\nShikiBundle changes need to be merged in first as we need to update package.json link\nhttps://github.com/NextChapterSoftware/ShikiBundle/pull/2"}
{"comment": {"body": "cc: @davidkwlam In regards to snippets, I think there was discussion about adding padding above and below for multi line snippets?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#issuecomment-1242343869"}}
{"comment": {"body": "@jeffrey-ng for ingested comments, we can't add padding below since GitHub truncates the diff hunk at the last user-selected line. I can make it so that we include N-number of lines above, assuming they're provided by GitHub. Just say the word.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#issuecomment-1242347568"}}
{"comment": {"body": ">  I can make it so that we include N-number of lines above, assuming they're provided by GitHub. Just say the word. \r\n\r\n@davidkwlam \u2014 yes lets always add lines above if possible to reduce the scenarios where the entire snippet is highlighted.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#issuecomment-1242361516"}}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/2930\r\n\r\nNot retroactive though unless we reingest", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#issuecomment-1242367532"}}
{"comment": {"body": "I doubt many GItHub comments have multiple lines selected FWIW", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#issuecomment-1242368410"}}
{"comment": {"body": "@jeffrey-ng @benedict-jw Merged: https://github.com/NextChapterSoftware/unblocked/pull/2930 Add padding to multi-line snippets for comments ingested from GitHub", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#issuecomment-1243953241"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1102986435", "body": ""}
{"comment": {"body": "Is the idea to revert this back to the `main` bundle when the PR on the bundle repo is merged?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#discussion_r967530650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1102987366", "body": ""}
{"comment": {"body": "This fixes copy/paste from the snippet UI?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#discussion_r967531469"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1102990780", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1102990958", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1102992879", "body": ""}
{"comment": {"body": "I'm a bit confused about the behaviour of initialization with this change -- I think we may want to separate out the once-only initialization (shiki/oni plus the language parsing) from the every-time intialization (updateTheme).\r\n\r\nThe result would look something like:\r\n\r\n* `initialize` would always call `await this.updateTheme()` so that we always keep it up to date\r\n* `initializingPromise` would resolve with the other items (shiki, languages, etc) but would never be set to `undefined`, as its state should never change.\r\n\r\n?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#discussion_r967535645"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1102993085", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1104410926", "body": ""}
{"comment": {"body": "Yup. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#discussion_r968602394"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1104411885", "body": ""}
{"comment": {"body": "Adds the counter without additional html.\r\nIMO better approach when we have to do full row highlighting.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#discussion_r968603070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1104422667", "body": ""}
{"comment": {"body": "In the PRView, we now have multiple instances of CodeBlocks which means `renderCodeToHtml` is called multiple times.\r\n\r\nThis triggers multiple initializations which was causing some race conditions where themes weren't 100% fully loaded and some CodeBlocks failed to syntax highlight.\r\n\r\nThis change to the initialization promise is to block everything on a single promise. InitializingPromise is cleared/set undefined which means that, updateTheme will still be called somewhat frequently.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#discussion_r968610513"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1106107061", "body": ""}
{"comment": {"body": "Should this be on a particular commit so builds are deterministic?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#discussion_r969840573"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2929#pullrequestreview-1106108457", "body": ""}
{"title": "Remove old unused ssl cert", "number": 293, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/293", "body": "Removed the old cert. All services have been switched to use the new one. \nThis change has been deployed to all environments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/293#pullrequestreview-877849310", "body": ""}
{"title": "Add padding to multi-line snippets for comments ingested from GitHub", "number": 2930, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2930"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2930#pullrequestreview-1102967964", "body": "We could do this from SM engine too, including post-selection lines as well as pre-selection lines."}
{"title": "AddGradleBuildCacheNodeStack", "number": 2931, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2931", "body": "Adds the cdk stack to create an ec2 instance with a gradle build cache node.\n"}
{"title": "Fix highlight tracking issues", "number": 2932, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2932"}
{"title": "Factor out dependencies on VSCode git extension", "number": 2933, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933", "body": "This factors out all of the VSCode git extension usage into a helper class, so that we can swap it out with a custom variant when the git extension is disabled:\n\nIGitProvider is an interface to whoever is providing workspace git functionality.  Right now the only things this provides is the path to the git binary, and the set of git repos in the workspace.\nVSCodeGitProvider implements this interface using VSCode's git extension\nI am working on a second implementation that finds repos using git directly.\n\nSome notes:\n* I changed the resolved repo model (ClientRepoAggregate) a little bit -- this now contains both the published workspace repo, plus a GitRunner for that repo.  We were recreating GitRunners all over the place, which would reduce the value of caching and such.  The idea is that everyone can use this one GitRunner for each repo."}
{"comment": {"body": "Still getting this once in a while...\r\nWill do some digging on my local machine.\r\n<img width=\"1174\" alt=\"CleanShot 2022-09-12 at 09 29 48@2x\" src=\"https://user-images.githubusercontent.com/1553313/189707327-c96eceb9-0180-4a13-a163-75ed600d3f27.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#issuecomment-**********"}}
{"comment": {"body": "> Still getting this once in a while...\r\n> Will do some digging on my local machine.\r\n\r\nWell I can no longer reproduce this... \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#issuecomment-1244039688"}}
{"comment": {"body": "This will need some dogfooding by team before we release. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#issuecomment-1244104222"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102939236", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102939606", "body": ""}
{"comment": {"body": "This stuff was moved out from `GitPluginExt.ts` -- it was only used here, and it's unchanged.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967496368"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102940724", "body": ""}
{"comment": {"body": "@richiebres dunno if this matters.  Fixing this would require having a hashing function on GitRunner that directly accepts the content to hash, instead of taking in a file reference.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967497105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102940957", "body": ""}
{"comment": {"body": "Also FYI @richiebres this is what we talked about yesterday -- this is expected to throw if it actually fails I think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967497305"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102941960", "body": ""}
{"comment": {"body": "These will be moved into GitRunner.  I didn't do that in this PR as it got involved, as we have to do error parsing and the like.  That will come in the next PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967498029"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102947524", "body": ""}
{"comment": {"body": "Yeah, it matters. This is guaranteed to generate a completely messed-up point.\r\n\r\nBut I suspect the old behaviour is no better.\r\n\r\nSee also:\r\nhttps://linear.app/unblocked/issue/UNB-178/allow-creating-insight-bubbles-from-a-locally-edited-file", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967501353"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102949044", "body": ""}
{"comment": {"body": "This would happen if the file has not yet been added to a Git commit.\r\n\r\nSee also:\r\nhttps://linear.app/unblocked/issue/UNB-133/starting-a-discussion-on-a-file-that-isnt-in-git-fails", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967502449"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102952571", "body": ""}
{"comment": {"body": "```suggestion\r\n/**\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967509828"}}
{"comment": {"body": "Outside the scope of this change: we need to bubble up to the user that Unblocked does not work with shallow repos rather than silently swallowing.\r\n\r\nAction is to deepen using:\r\n```sh\r\ngit fetch --unshallow\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967512287"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102964307", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102966326", "body": ""}
{"comment": {"body": "OK so just to be clear, for now these changes won't regress anything?\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/f082d866-1d30-4478-a200-a8e008e1456c?message=f9387a57-7f07-4c00-a48d-bc8d582e1c76).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967515611"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102966853", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102967305", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102968236", "body": ""}
{"comment": {"body": "?  Not sure this matters, and we haven't tended to use this format when doing multi-line comments?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967517099"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102968799", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102969431", "body": ""}
{"comment": {"body": "It's not about multi-line. It's about JSDOC integration in the IDE. The JS-DOC comments appear in IDE tooltips when referencing the type.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r967518019"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1102971024", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-**********", "body": ""}
{"comment": {"body": "It looks like there is still a race here.\r\n\r\nIf we try to call this StreamAsyncFetcher *before* `VSCodeGitProvider.publishRepos` is called (which updates the stream), this can return an empty array when it should not be.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r968709974"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Fixed issue.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r968740619"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Explicitly do not want a default value for this stream.\r\n\r\nWe only want to start sending stream events once it's been populated by publishRepos.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r968741318"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-**********", "body": ""}
{"comment": {"body": "\ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r968741422"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1104602455", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#pullrequestreview-1107948541", "body": ""}
{"comment": {"body": "@matthewjamesadam Dennis just ran into this scenario in a demo.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/20b32ca7-e658-4bb8-8fb0-6a8326cf380a?message=657c2022-4c2c-460e-924c-0618bc126ce0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2933#discussion_r971118007"}}
{"title": "Remove digest email setting for now", "number": 2934, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2934", "body": "reported in slack channel \nwe dont send daily digest emails right now so we should remove the setting from the UI until implemented:\n\nNOTE: should revert this commit when the emails are implemented."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2934#pullrequestreview-1102978218", "body": ""}
{"title": "Fix uploads", "number": 2935, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2935"}
{"title": "Fix for SM commit path stitching to address issue with file renames", "number": 2936, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2936", "body": "SM engine was tracking file renames in an incomplete way. The impact was that it would track the point\nup until it was renamed only. This resulted in failures to display the point in files after the rename.\nRelated to #2928."}
{"title": "preint environment", "number": 2937, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2937"}
{"title": "Compile search service", "number": 2938, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2938"}
{"title": "Do not use upload action for saving and restoring artifacts as it is slow", "number": 2939, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2939", "body": "We are now using the cache task.\nIt's a bit of a hack, but it's the only good one until we come up with another technique."}
{"title": "Standardize error handling across api", "number": 294, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/294", "body": "This is canonical way of doing errors via openapi specs.\nIt is recommended to provide a default error response.\nAlso adding a ktor Status Page which handles exceptions that bubble up from api calls."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/294#pullrequestreview-877936503", "body": ""}
{"title": "Change server configuration for cache node", "number": 2940, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2940"}
{"title": "Do not cache apps", "number": 2941, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2941"}
{"title": "Increase cache size", "number": 2942, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2942"}
{"title": "Enable local build cache", "number": 2943, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2943", "body": "Why the hell we didn't use this before is beyond me."}
{"title": "Use m6a instance type", "number": 2944, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2944"}
{"title": "expose deepcheck for all public facing services", "number": 2945, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2945", "body": "I just discovered that we were not exposing __deepcheck endpoints to grafana for any other public service other than apiserivce. \nThis PR adds necessary ALB routing rules to expose those endpoints. These endpoints are gated by WAF and require a special header. That's to avoid having someone DDoS our service considering these checks are expensive."}
{"title": "Update shell script for buld cache node", "number": 2946, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2946"}
{"title": "Update cdk dependencies", "number": 2947, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2947", "body": "Addresses two warnings:\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n!!                                                                            !!\n!!  Node 17 has reached end-of-life on 2022-06-01 and is not supported.       !!\n!!  Please upgrade to a supported node version as soon as possible.           !!\n!!                                                                            !!\n!!  This software is currently running on node v17.4.0.                       !!\n!!  As of the current release of this software, supported node releases are:  !!\n!!  - ^18.0.0 (Planned end-of-life: 2025-04-30)                               !!\n!!  - ^16.3.0 (Planned end-of-life: 2023-09-11)                               !!\n!!  - ^14.6.0 (Planned end-of-life: 2023-04-30)                               !!\n!!                                                                            !!\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\nAnd addresses warning during cdk runtime of later version of cdk available.\nSimply update node by using any of the following:\nnvm install 18\nbrew upgrade node"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2947#pullrequestreview-1103328852", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2947#pullrequestreview-1103344009", "body": ""}
{"title": "Hide Insights button when missing repo", "number": 2948, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2948", "body": "Hide Insights button when missing repo.\nOccurs when unauthenticated / org not installed / repo not authenticated."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2948#pullrequestreview-1103358389", "body": ""}
{"title": "Add fetch/checkout/pull git ops to VSCode", "number": 2949, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2949", "body": "This is a follow-on from the previous PR (https://github.com/NextChapterSoftware/unblocked/pull/2933) -- it replaces usage of VSCode's fetch/checkout/pull git operations with our own.\nThe ops themselves are trivial, almost all the code here is to deal with error handling.\nI added a couple parsers, one that returns an error code for various failure conditions, one that returns an error message.  TBH I think the error message one may end up being an antipattern, as the message you want to display often depends on the context that the message is generated in, but for now it's fine."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2949#pullrequestreview-1103471877", "body": ""}
{"comment": {"body": "The regexes and such here are snagged from VSCode", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2949#discussion_r967979847"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2949#pullrequestreview-1104615796", "body": ""}
{"comment": {"body": "Add a permalink here to VSCode GH for reference?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2949#discussion_r968756735"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2949#pullrequestreview-1104617142", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2949#pullrequestreview-1104904024", "body": ""}
{"comment": {"body": "Done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2949#discussion_r968983800"}}
{"title": "Clean up PullRequestReviewThreadService", "number": 295, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/295", "body": "No need to have so many files, we can consolidate"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/295#pullrequestreview-878007453", "body": ""}
{"title": "Specify build cache directory", "number": 2950, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2950"}
{"title": "Video Chat Layouts & Screen Sharing UX", "number": 2951, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2951"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2951#pullrequestreview-1104500940", "body": ""}
{"comment": {"body": "Returning Self allows for a builder interface", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2951#discussion_r968665500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2951#pullrequestreview-1104523478", "body": ""}
{"comment": {"body": "This class is for the calling notification. I might re-write it now that I have better understanding of the windowing system", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2951#discussion_r968674971"}}
{"title": "Cleanup gradle home cache", "number": 2952, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2952"}
{"title": "Remove the unused message parameter from SourceMarkModel", "number": 2953, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2953"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2953#pullrequestreview-1104697175", "body": ""}
{"title": "Revert \"Cleanup gradle home cache (#2952)\"", "number": 2954, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2954", "body": "This reverts commit 829acdfe4b1fc8c920da88782679d04e23ebdf2c."}
{"title": "Video App Layout & Screen Share", "number": 2955, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1104708054", "body": ""}
{"comment": {"body": "16:9 ratio", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r968836518"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1104776058", "body": ""}
{"comment": {"body": "Side note - I suspect this is super brittle. We'll test on Ventura to understand the implications of this implementation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r968890130"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1104802322", "body": ""}
{"comment": {"body": "I tried to DRY this up, but refactoring it into a separate modifier it causes the camera stream to stop rending to the backing view. I think this is because SwiftUI is dumping the view behind the scenes without an associated AppKit window event. I haven't figured out how to deal with this outside of trying different view configurations until it works.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r968908004"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1104807847", "body": ""}
{"comment": {"body": "This timing dependent stuff is not great. There is a way to \"listen\" for animation complete events by implementing the `AnimatableModifier` protocol, but it's awkward to use", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r968911929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1104859307", "body": ""}
{"comment": {"body": "This is aggressive... Are there window events we could take advantage of instead of polling?\r\n\r\nStuff like `NSWindowDidResignKeyNotification`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r970178001"}}
{"comment": {"body": "Do we still need `panel.orderFrontRegardless` if this is used?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976811176"}}
{"comment": {"body": "If these ratios/values are important, we should pull these out into general constants.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976812043"}}
{"comment": {"body": "With this, we could have started = true even if initVideo fails (e.g. missing backingView). That expected?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976813768"}}
{"comment": {"body": "Same as before. Think we should have this as CGSize.VideoSize / or something.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976825253"}}
{"comment": {"body": "Why lazy and not just a regularVStack? Doesn't look like there's a dynamic/long list or scrollview", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976834965"}}
