{"comment": {"body": "Whoops that\u2019s a merge thingie", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160355711"}}
{"comment": {"body": "`self.mac_to_certificate_cache = {}`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160360250"}}
{"comment": {"body": "This cache maps MAC addresses to certificate serial numbers.  \nThis is a must because we capture the serial number before the association.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160360682"}}
{"comment": {"body": "I meant that the deletion can be simplified to `self.mac_... = {}`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160361297"}}
{"comment": {"body": "\"There's no such thing as user\"\r\n- G. Naaman", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160361478"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} But this dict can contain more than one entry.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160361530"}}
{"comment": {"body": "Yeah, we still need to de-userify a lot of our code base", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160361740"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} AHHH. I read the `if` as `for`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160361788"}}
{"comment": {"body": "Thank you for \u201cclassing up\u201d the cache, things were \u201cridictulous\u201d", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/360/_/diff#comment-160372473"}}
{"title": "Feature/operation replay improvements", "number": 361, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/361", "body": "Ignore timeout\nRemove classifiers not present in training\nVDIM has non-portable serialization. On my PC, int is int32 and on the server, it's int64\n\n"}
{"title": "Add Cypress A", "number": 362, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/362", "body": ""}
{"comment": {"body": "rrrrrrrrrrrrrrrrrrr", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/362/_/diff#comment-160353173"}}
{"title": "Unique IDs per SVG to avoid collision", "number": 363, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/363", "body": ""}
{"title": "add unhandled exception handling in handle_connections", "number": 364, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/364", "body": ""}
{"title": "MITM detection was disabled by default + levl ID changes fixes", "number": 365, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/365", "body": ""}
{"title": "Do not use streamer agent recovery with CFR", "number": 366, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/366", "body": "Also removes 2 unused functions"}
{"title": "Feature/disassociate upon wrong eap or unquarantine", "number": 367, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/367", "body": "Sending disassociations upon unquarantine and when we miss eap packets and device is stuck at JUST_CONNECTED. disassociation commands are in a separate monitor (management_processor + ap_agent). JUST_CONNECTED state now has a timeout after which station is disassociated. Also Passing encryption configuration per interface so we know when to check JUST_CONNECTED timeout (only on encrypted interfaces)"}
{"title": "Automatic MAC config on eth0 (WAN)", "number": 368, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/368", "body": "Connectivity issues caused by rb and r1 having the same MAC on WAN iface. So setup now configures it."}
{"title": "Move timeout detector to Session", "number": 369, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/369", "body": ""}
{"title": "Motion detect", "number": 37, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/37", "body": "Please disregard all workspace folders, only review the following files:\ncfr_motion_detect/__init__.py\nipq807x_cfr/cfr_processing.py\nipq807x_manual/cfr_plot.py"}
{"title": "Tiny ui change to make device name location fixed", "number": 370, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/370", "body": ""}
{"title": "Fix device statistics and last-seen field", "number": 371, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/371", "body": ""}
{"title": "Yair wifi sim 2G", "number": 372, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/372", "body": "Output a single class from multiple packet input\n"}
{"comment": {"body": "You can ignore anything outside infra", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/372/_/diff#comment-161872851"}}
{"title": "fix two way snr", "number": 373, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/373", "body": "Small fix dealing with None Type"}
{"title": "Improve SNR indication algorithm", "number": 374, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/374", "body": "Aggregating SNR values of packets into a 1-second long window and returning median of last windows to flask.\n"}
{"title": "Rogue ap detection threshold", "number": 375, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/375", "body": "Only if 3 external appearances of our BSSIDs were captured in the last 5 seconds, a rogue IP alert will be raised. To not flood logging and the alerts screen, reports are only being raised every 5 minutes, as before."}
{"comment": {"body": "Why not use the timestamp inside of the packets for this detection?  \n  \nThis will allow us to detect also on replayed records..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/375/_/diff#comment-162275597"}}
{"comment": {"body": "The radiotap MAC timestamp? it is written by the router on receive so it wouldn\u2019t add any information in regards to replayed records, right? ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/375/_/diff#comment-162293972"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Yes. The MAC timestamp.  \n  \nWe should strive to make sure the system operates only on the input and not using other server signals as much as possible.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/375/_/diff#comment-162299341"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Generally agree. In this case it doesn\u2019t make a lot of difference but the router timestamp **will** reflect real events timings more accurately, so depending on the definition of where the threshold should be measured, the MAC TS could be a better choice. \n\nBut we might also miss some events or have to handle edge cases because monotonicity\u00a0isn\u2019t guaranteed \\(BSS sync\\) and wraparounds will happen \\(32 bit by default with us resolution\\).\n\nIf you prefer MAC TS anyway I\u2019ll make the switch.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/375/_/diff#comment-162305610"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} I see.  \nIn that case we can keep it for later.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/375/_/diff#comment-162308308"}}
{"title": "Improve detection", "number": 376, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/376", "body": ""}
{"title": "Improve motion detection", "number": 377, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/377", "body": "Notice that frontend polling interval was reduced to 127ms\n"}
{"title": "Various stability fixes", "number": 378, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/378", "body": "Frontend: Made SVGs style IDs unique to avoid clashes (they share an ID namespace when in the same page)\nAgent: Clear the stashed client list whenever we clear the CFRControl client list\n\nServer:\n\nStop feeding trainer with packets when we already have a model\nDont crash on old splashpage token (from previous server runs, probably) - instead redirect to go.levl.tech\n\n\n\n"}
{"comment": {"body": "Agent: also clear client list when the server goes down.  \nSome of these agent fixes are trying to clear a problem where the agent will try to solve a non-existent streaming problem and restart all streamer.\n\nThis was very loud and evident by huge \u201cConnection was reset by peer\u201d tracebacks on the server.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/378/_/diff#comment-162540279"}}
{"title": "Don't ask for CFRs for background processes if we're training another device", "number": 379, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/379", "body": ""}
{"title": "Motion detect workspace files", "number": 38, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/38", "body": "Just workspace files, no need to review"}
{"comment": {"body": "who needs dis?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/38/_/diff#comment-145831062"}}
{"title": "Gilad/fix alerts", "number": 380, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/380", "body": "Alerts were a bit of a mess because weve inherited the BLE structure which doesnt really make sense in WiFi.\nNamely, ip address everywhere:\n\n"}
{"comment": {"body": "`device_id` field in the DB is `NOT NULL` but this session might not have a permanent ID.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/380/_/diff#comment-162614970"}}
{"comment": {"body": "It is not NOT NULL any longer", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/380/_/diff#comment-162615269"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} Oh sorry looked at removed code", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/380/_/diff#comment-162615618"}}
{"title": "Block unauthorized devices", "number": 381, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/381", "body": "Block device on router when it's in an unauthorized state. Unauthorized state includes JUST_CONNECTED when the encryption is disabled (training, user should access online)\n"}
{"title": "Fixed unquarantine", "number": 382, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/382", "body": "Previously the button would only work if the session was still live."}
{"title": "Shadow mode for features", "number": 383, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/383", "body": "add shadow features. Put CFR slopes as one of them\ncolor for NOT_MATCH_SHADOW\n\n\n"}
{"title": "Add last_seen column if it doesn't exist", "number": 384, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/384", "body": ""}
{"title": "Comcast release backport unq fix", "number": 385, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/385", "body": "Import unq fix\nFix training\n\n"}
{"title": "Fix crash on get_status", "number": 386, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/386", "body": ""}
{"title": "Fixes 12/07", "number": 387, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/387", "body": "Some QoL changes involving logs.\nAdded trace files for motion detection\nUI would poll the server too frequently and most requests would fail.\nFixed bug where multiple certificates would be generated for the same device. (this should probably moved eventually to the training process?)"}
{"comment": {"body": "it makes sense to send this event only after certificate creation, even if it already exists, to increase the chances that the user downloads the certificate", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/387/_/diff#comment-163512450"}}
{"comment": {"body": "How does this increase the chance the user downloads the certificate.\n\nAFAICT this only increase the chance that we generate 2 certificates", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/387/_/diff#comment-163512891"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} you\u2019re right. Since the `create new certificate if it doesn\u2019t exist and update that it\u2019s created` operation is not atomic.\n\nHow about changing it to be atomic?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/387/_/diff#comment-163624021"}}
{"comment": {"body": "Can you share what this is for?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/387/_/diff#comment-163624467"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} I thought about that actually.\n\nI think the ideal is for the certificate be generated at the end of \\(or during\\) the training process.  \nThe user shouldn\u2019t have an effect on this process.\n\nThis will probably happen sometime soon but since we\u2019re moving to a cert-less configuration for a while I thought I will write the quick and dirty solution that happens to work in our tests.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/387/_/diff#comment-163624894"}}
{"comment": {"body": "This is a script that patches the firmware image with our code.   \nYou run this inside the build container after running `setup.sh` with the firmware configuration, and it copies the resulting directory into the image.\n\nIt should be possible to automate this further but I haven\u2019t got around to it yet.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/387/_/diff#comment-163625490"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} Your will to un-'quick and dirty' is appreciated", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/387/_/diff#comment-163628319"}}
{"comment": {"body": "Would be useful to have a script that replays the trace", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/387/_/diff#comment-163862627"}}
{"title": "CFR slope movement correction", "number": 388, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/388", "body": "Measures difference between abs(cfr) of consecutive frame pairs, and disregards the pair if the pairs Pearson correlation is less than a value. Then moves on to the next frame, every frame overlapping both the previous and the current pair.\nCollects (WIFI_SLOPES_PARAMS_SUBWINDOW - 1) valid pair differences and extracts the features from them.\nSeems to yield better results than before, at the cost of longer collection times for moving or noisy records.\nWIFI_SLOPES_PARAMS_CLASSIFICATION_TIMEOUT_PACKETS and WIFI_SLOPES_PARAMS_SUBWINDOW_STEP may be changed pending tests."}
{"comment": {"body": "didn\u2019t you also update `cfr_subscriber_grisha.py`? It would be very helpful to have the same change there", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/388/_/diff#comment-163626987"}}
{"comment": {"body": "Some PEP rule does not like the lack of 2 blank lines between imports and variables", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/388/_/diff#comment-163627393"}}
{"title": "Separate splashpage address", "number": 389, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/389", "body": ""}
{"comment": {"body": "This is needed if we want to use VPN for the data streams.\n\nThe redirection chain from \u2018go.levl.tech\u2019 cannot go through the VPN for reasons, so we need to specify both addresses to the agent.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/389/_/diff#comment-163663096"}}
{"title": "Clear CFR counters when restarting recording", "number": 39, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/39", "body": "These should clear everytime recording is stopped and re-started.\nThey have no impact on the recording itself, and only serve as a visual indicator.\n\n"}
{"title": "Last commit", "number": 390, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/390", "body": "Mainly so you guys have latest code to generate confusion plots in continuous time, which will probably be useful in the future."}
{"title": "Dockerize ./generate_static.sh", "number": 391, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/391", "body": "Its very annoying having to install n/npm/node on every new server, this is nicer"}
{"title": "Fix openvpn setup issues", "number": 392, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/392", "body": ""}
{"title": "Scene movement icon", "number": 393, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/393", "body": ""}
{"title": "Feature/recsys live radiotaps", "number": 394, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/394", "body": "Recsys now has a more generic publisher that supports multiple clients\nRecsys publishes radiotaps\nSubscriber is now more generic. Example usage is in cfr_radiotap_subscriber.py\n\n"}
{"title": "Feature/recsys live radiotaps", "number": 395, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/395", "body": "Recsys now has a more generic publisher that supports multiple clients\nRecsys publishes radiotaps\nSubscriber is now more generic. Example usage is in cfr_radiotap_subscriber.py"}
{"title": "Zero touch ui fixes 20200719", "number": 396, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396", "body": "Sort table by device ID\n\nOnboarding devices (without device ID) show up last\n\n\n\nNow the history events hold also device attributes\n\nLogs show these attributes: mac, ip, hostname and device model\nAutomatic transition\n\n\n\n\n"}
{"comment": {"body": "Approved, remember not to merge before @{557058:f7a3b23e-03ba-40b3-9ce6-b139e8b20556} approves ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-165269013"}}
{"comment": {"body": "A Couple of questions:\n\n1. Can you have the software run the altering of the table automatically if it detects the fields are missing? \\(Gilad did something like that with the last seen field\\)\n2. Was there any issues with the ordering of items? I think we had some issues with the UI being updated in the wrong places when new devices are add.\n3. Did you get to test it with a system going through an upgrade?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-165269498"}}
{"comment": {"body": "1. I\u2019ll add automatic update.\n2. You said that there was an issue with inconsistent placement in the table so I added order to the table\n3. Yes. I\u2019ll rerun the test after fixing #1\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-165269801"}}
{"comment": {"body": "Yeah, I\u2019ve added this to `db_accessor::create_tables_if_required`:\n\n```\n            _execute_sql_command_no_reply(\"\"\"ALTER TABLE devices ADD COLUMN IF NOT EXISTS last_seen TIMESTAMP\"\"\")\n```\n\nBasically just add `IF NOT EXISTS`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-165269811"}}
{"comment": {"body": "What I\u2019ve meant is that did you verify that the UI behaviour was not interrupted due to the new order of devices.  \n  \nI think we had a problem with that in the past the JS that is responsible to update the UI live had some assumption that the ordering of devices is not changing if a new device is added.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-165270498"}}
{"comment": {"body": "The onboarding devices show up first and then change their location happily after getting a device ID. I don\u2019t see that as an issue", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-165270854"}}
{"comment": {"body": "That\u2019s not an issue. \n\nThe question whether the JS of the UI will not break \\(as update the wrong device information\\) because of a new device added to the middle of the table. Omer might have an input of that. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-165271859"}}
{"comment": {"body": "Not sure how it works, sorry", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-165273273"}}
{"comment": {"body": "Updated fix #1 and transition is smooth.\n\nRetested and nothing broke that I can see.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-*********"}}
{"comment": {"body": "I saw that. Great code.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/396/_/diff#comment-*********"}}
{"title": "Fix display of some smartphones", "number": 397, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/397", "body": "Added more special-cases for the computer identifier\nExpanded heuristic to detect android manufacturer\nCopypasted lg->lge because fingerbank identify some devices as LGE Android\n\n"}
{"comment": {"body": "the zero touch branch is `zero_touch_prodction`\u2026.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/397/_/diff#comment-*********"}}
{"title": "Gilad/zero touch dhcpv6", "number": 398, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/398", "body": "Send router-advertisements\n\nMerged in gilad/fingerbank-fix (pull request #397)\nFix display of some smartphones\nApproved-by: Tamir Raz\n\n\nAlso show computer on rivet networks\n\n\n"}
{"comment": {"body": "Seen to also pull DHCPv6 solicitations from iOS<14 and linux", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/398/_/diff#comment-*********"}}
{"title": "Added VPN to WAN/LAN FW zones", "number": 399, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/399", "body": "This allows us to SSH/communicate with the router using the OpenVPN interface"}
{"title": "add scope recording scripts for wifi packets", "number": 4, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/4", "body": ""}
{"comment": {"body": "Please don\u2019t place floating code inside `if __name__==\"__main__\":`, use:\n\n```python\ndef main():\n  ...\n  \nif __name__==\"__main__\":\n  main()\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/4/_/diff#comment-*********"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/4/_/diff#comment-141897898"}}
{"comment": {"body": "You forgot \n\n```python\nif __name__==\"__main__\":\n  main()\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/4/_/diff#comment-141907692"}}
{"comment": {"body": "Done. Thanks", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/4/_/diff#comment-141910442"}}
{"title": "Create motion detector", "number": 40, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/40", "body": ""}
{"title": "Added VPN to WAN/LAN FW zones (cherry picked for ztouch)", "number": 400, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/400", "body": ""}
{"title": "Reassociation packets", "number": 401, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/401", "body": "Enable them in cache.\nThey were removed from cache due to parsing issue, but parsing was fixed by @{5a4500fe0cacf235de82a9d4} . Now we can use them"}
{"title": "Gilad/zero touch dhcpv6", "number": 402, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/402", "body": "dhcpv6 for non-android devices\nFix DUID size\n\n"}
{"title": "Fix memory leak in affix stream", "number": 403, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/403", "body": ""}
{"comment": {"body": "ACKCHYUALLY this is not a memory leak", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/403/_/diff#comment-165693374"}}
{"comment": {"body": "Ah, technically correct, the best kind", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/403/_/diff#comment-165693567"}}
{"comment": {"body": "design-wise, digest should `while data: do throw-packets` and then we wouldn\u2019t have `digest_all`. There\u2019s no actual need for both APIs", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/403/_/diff#comment-165694132"}}
{"comment": {"body": "I agree 100%, but we don\u2019t have any tests or CI and I don\u2019t want to potentially break code that uses this. \\(this fix is only relevant to BLE in practice\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/403/_/diff#comment-165694433"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} go ahead and break stuff. it\u2019s fine", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/403/_/diff#comment-165695034"}}
{"title": "Clean up API", "number": 404, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/404", "body": ""}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Made sure to do as much damage as possible", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/404/_/diff#comment-165696881"}}
{"comment": {"body": "Ah, the code removal. Most beautiful thing in the world", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/404/_/diff#comment-165697299"}}
{"comment": {"body": "Pull request not found", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/404/_/diff#comment-165697404"}}
{"comment": {"body": "And you know I wrote extra code just to be able to delete it later.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/404/_/diff#comment-165697549"}}
{"comment": {"body": "What an honour", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/404/_/diff#comment-165697636"}}
{"title": "cache server requests", "number": 405, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/405", "body": "for better responsiveness with multiple open tabs"}
{"comment": {"body": "Un-approving since this can be done by nginx apparently without changing the server.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/405/_/diff#comment-165709702"}}
{"comment": {"body": "how is it done then?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/405/_/diff#comment-165771996"}}
{"comment": {"body": "[https://www.nginx.com/blog/nginx-caching-guide/](https://www.nginx.com/blog/nginx-caching-guide/)\n\nAnd specifically in flask:\n\n[https://stackoverflow.com/questions/23112316/using-flask-how-do-i-modify-the-cache-control-header-for-all-output](https://stackoverflow.com/questions/23112316/using-flask-how-do-i-modify-the-cache-control-header-for-all-output)\n\nI\u2019ll try to run it on some server with nginx to see that it works", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/405/_/diff#comment-165790139"}}
{"title": "manufacturer logo updated on change", "number": 406, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/406", "body": ""}
{"title": "Larger logos", "number": 407, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/407", "body": "Also returned ordering of devices now that the manufacturer logos update"}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/3039751976-image.png)\nAlso made sure that all devices are iPhones in order to appease to the older audience which does not know the difference.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/407/_/diff#comment-165754793"}}
{"comment": {"body": "unbreaker of code", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/407/_/diff#comment-165772490"}}
{"comment": {"body": "@{5b02c344cd95416ee040ad9c} would like that", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/407/_/diff#comment-165772573"}}
{"title": "Gzip nginx", "number": 408, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/408", "body": "Added gzip compression to nginx JSON responses\n\n"}
{"title": "Fix connectiviy issues with PSK2", "number": 409, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/409", "body": ""}
{"title": "Fix CCP router agent pingers", "number": 41, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/41", "body": ""}
{"title": "Show devices in the discovery stage", "number": 410, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/410", "body": ""}
{"title": "Add nginx-level caching", "number": 411, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/411", "body": "This lessens the load on our code, but we cannot go under 1 second."}
{"comment": {"body": "Tested by using this diff:\n\n![](https://bitbucket.org/repo/8X5z9dk/images/4051818839-image.png)\nAnd running `curl https://cbleqa.levl.tech:8080/state` repeatedly ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/411/_/diff#comment-165829694"}}
{"comment": {"body": "My PR can have lower reload rate. Just saying", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/411/_/diff#comment-166004731"}}
{"title": "Gilad/de odhcpd", "number": 412, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/412", "body": "Remove odhcpd in favour of dnsmasq config\nFilter DUIDs from DHCP leases\nWhitespace\n\n"}
{"comment": {"body": "Add comment explaining what does it mean for a line in `/tmp/dhcp.leases` to start with `duid` and why is it ignored?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/412/_/diff#comment-166044658"}}
{"title": "See branch name", "number": 413, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/413", "body": "capsfp: Support 802.11n by default and don't disable feature if there's no HT caps"}
{"comment": {"body": "But what does that do if there\u2019s no power save mode?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/413/_/diff#comment-166063290"}}
{"comment": {"body": "who\u2019s doing what?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/413/_/diff#comment-166063637"}}
{"comment": {"body": "HTCaps were required so that we can determine the power save mode \\(see line 107\\).\n\nWhat happens now if we continue without them?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/413/_/diff#comment-166064027"}}
{"comment": {"body": "I don\u2019t see that we use them anywhere. @{5a4500fe0cacf235de82a9d4} could elaborate", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/413/_/diff#comment-166067374"}}
{"title": "Added OpenVPN to r1", "number": 414, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/414", "body": ""}
{"title": "Follow Andriod 10 assocication sequence numbers", "number": 415, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/415", "body": ""}
{"comment": {"body": "remove if not used", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/415/_/diff#comment-166077582"}}
{"comment": {"body": "What is the meaning of the substraction?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/415/_/diff#comment-166078931"}}
{"comment": {"body": "Is this number persistent across connections? What is its meaning? Why is it relevant only for Android 10", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/415/_/diff#comment-166079192"}}
{"comment": {"body": "In android 10, each connection the sequence number increases by 4. So we are looking up with an older connection of the device to try to link it with the devices model", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/415/_/diff#comment-166081906"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} So why 8? In case they connected to another network?\n\nAre the sequences distinct enough to avoid collisions?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/415/_/diff#comment-166082479"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} Yeah.  \nThere are not distinct enough, so it\u2019s just a heuristic..  \\(We don\u2019t have anything else to link the two mac address right now..\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/415/_/diff#comment-166082952"}}
{"comment": {"body": "Is this PR still relevant?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/415/_/diff#comment-167953894"}}
{"comment": {"body": "?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/415/_/diff#comment-174878433"}}
{"title": "See branch name", "number": 416, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/416", "body": ""}
{"comment": {"body": "Just to make sure, the restart of the daemon does not remove the dhcp leases?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/416/_/diff#comment-166160513"}}
{"comment": {"body": "It does not", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/416/_/diff#comment-166171555"}}
{"title": "Restart dnsmasq", "number": 417, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/417", "body": ""}
{"title": "Fix training and classification when there are no HT caps", "number": 418, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/418", "body": "Previously windows caps wont be saved during training because HT Caps were missing.\nClassification threw an exception, but it was caught and the feature was marked as MATCH.\n\nThis PR adds a new item in the list. I could have turned the list into a dict with the keys 0,1,2,3,None, but I didnt want to break old comcast models.\nThe classification checks if the model is too short and ignores it if thats the case."}
{"comment": {"body": "Look OK. Wait for @{5a4500fe0cacf235de82a9d4} 's feedback before merging", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/418/_/diff#comment-*********"}}
{"comment": {"body": "Whoops, yeah, forgot that was a thing.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/418/_/diff#comment-*********"}}
{"comment": {"body": "Anyway this is not yet tested, didn\u2019t want to break the current cycle.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/418/_/diff#comment-*********"}}
{"title": "Device type classification improvement", "number": 419, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/419", "body": "Fingerbank: also request data when there's DHCPv6 fingerprint\n\nDevice type classifier: if the DHCPv6 DUID contains public mac, use this mac instead\n\nThis happens in iOS 14 devices that use private mac addresses. They send their public mac address in the DUID. Because there are no flaws in Apple devices.\n\n\n\nThe events log mac address source is now transient id since device_hw_addr is often modified and is not persistent within the code\n\nextract_info_from_caches now returns a forced mac address, which is currently sourced from DHCPv6 DUID (when its persistent)\nPEP and typos fixes\n\n"}
{"comment": {"body": "Can we use the extracted mac from DUID to also improve our identification algorithm?\n\nMeaning using it as the hw\\_mac identifier and also updating the device information with the hw mac?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/419/_/diff#comment-*********"}}
{"comment": {"body": "I\u2019ll look into overriding the hw\\_mac. I hope it won\u2019t affect the flow", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/419/_/diff#comment-167649235"}}
{"comment": {"body": "added forced mac address flow", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/419/_/diff#comment-167685399"}}
{"comment": {"body": "I\u2019ve made some changes to the PR. I\u2019d appreciate a re-review", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/419/_/diff#comment-167685450"}}
{"title": "Fix CCP router agent pingers", "number": 42, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/42", "body": ""}
{"comment": {"body": "Please explain what you\u2019re fixing", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/42/_/diff#comment-145901602"}}
{"comment": {"body": "The CCP router agent pingers \\(the router agent creates a pinger for every connected client \\(to keep the station from going into wifi powersave\\)\\).\n\nTurns out the pingers were never actually started, I fixed it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/42/_/diff#comment-145913075"}}
{"comment": {"body": "Good fix!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/42/_/diff#comment-145913406"}}
{"comment": {"body": "This thread\u2019s handle is lost after starting. Don\u2019t you want to want to hold on to its handle in order to `.join()` on it later?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/42/_/diff#comment-145914214"}}
{"comment": {"body": "Don\u2019t wanna join it. I just stop all of them with the stop\\_event. I mean it would be \u201cbest practice\u201d but to test it I\u2019d have to schedule time with the router and it\u2019s just not worth it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/42/_/diff#comment-145915365"}}
{"title": "Zero touch x master", "number": 420, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/420", "body": "hi hello there\nWere merging source codes of Comcast Enterprise Edition and Comcast Home Edition so that we have more ifs in our code.\nTo setup the router into Enterprise mode, run ./setup.sh -z.\nTo setup the server into Enterprise mode, run ./run_server.sh -z\nBy default (without the cli arguments), the modes are Zero Touch. Interoperability between modes is not expected.\nAll comments are welcome.\n"}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} do we need these env files to be tracked by git? aren\u2019t they generated every `setup.sh` call?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/420/_/diff#comment-167837759"}}
{"comment": {"body": "Can we make the zero touch the default installation and the enterprise one to require a parameter?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/420/_/diff#comment-167934281"}}
{"comment": {"body": "Sure", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/420/_/diff#comment-167934696"}}
{"comment": {"body": "Changed it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/420/_/diff#comment-167938642"}}
{"comment": {"body": "Yes and yes, I want them to be automatically updated when you pull, even before you run anything. For example `ssh.config` \\(which is also automatically generated along those env files\\), which can be included like `Include ~/repos/comcast/ipq_config/ssh.config` in your personal ssh config file. Also you might want to source those env files BEFORE you run setup, and it\u2019s a chore to remember you have to regenerate them\u2026.   \n  \nMaybe there is a better solution around these problems, but for now this is why I did this", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/420/_/diff#comment-167965986"}}
{"title": "Zero touch 2.4", "number": 421, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/421", "body": "Re-enable 2.4GHz band\nFix chain\n\n"}
{"title": "Add 3rd all.sh param to select wifi interface. 0 for 5ghz and 1 for 2.4ghz", "number": 422, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/422", "body": "This is ugly, I know.\nIf you have another preference, do tell"}
{"comment": {"body": "So the firmware always uses 5Ghz for now?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/422/_/diff#comment-167966652"}}
{"comment": {"body": "Will this flag be removed once we support both 5Ghz and 2Ghz?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/422/_/diff#comment-167966748"}}
{"comment": {"body": " I guess, for now", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/422/_/diff#comment-167969938"}}
{"comment": {"body": "I hope", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/422/_/diff#comment-167970113"}}
{"title": "Motion fixes for 2.4 GHz", "number": 423, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/423", "body": "Fix segment select\nDifferent motion threshold for different bands\n\n"}
{"comment": {"body": "Not that this is wrong, but why not use the session\u2019s `network_band` attribute?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/423/_/diff#comment-168015779"}}
{"comment": {"body": "Less accessible. Need to propagate the network band from data\\_processor to motion\\_detector\\_class1 \\(separate thread\\) then to motion\\_detector\\_class2 \\(separate process\\) then to motion\\_detector\\_class3 \\(separate class\\).", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/423/_/diff#comment-168016425"}}
{"title": "Zero touch odhcpd strikes back", "number": 424, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/424", "body": "Replace dnsmasq with odhcpd completely.\nAdd dhcpdv6 server than runs on lableft. When connecting the routers WAN port to LL, it will acquire an IPv6 address and prefix and lease them to stations.\n\n"}
{"comment": {"body": "What\u2019s the reason for adding the dns here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/424/_/diff#comment-168472950"}}
{"comment": {"body": "DHCP also advertises the address of the DNS server. By default this is the AP.  \ndnsmasq used to be the DNS server, but I couldn\u2019t figure out how to run it without providing DHCP, so I made the advertised DNS server something external we can trust.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/424/_/diff#comment-168473021"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} Can we really trust google?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/424/_/diff#comment-168473217"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} I trust them to work", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/424/_/diff#comment-168473396"}}
{"comment": {"body": "So nobody\u2019s using the `timestamp` or `expiry_seconds` field in the DHCP leases?  \nDo you remember whether we ever used them?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/424/_/diff#comment-168474144"}}
{"comment": {"body": "AFAIK we never do.\n\nShould I remove it?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/424/_/diff#comment-168474544"}}
{"comment": {"body": "I don\u2019t see the need to remove it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/424/_/diff#comment-168476481"}}
{"title": "Feature/dualband zero touch interoperability", "number": 425, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/425", "body": "Add 3rd all.sh param to select wifi interface. 0 for 5ghz and 1 for 2.4ghz, 2 for both\nCoarse ID now ignore 802.11ac and vht_caps\n\nCap feature is network band aware\n\nDiscard VHT caps, power smps, supported channels and 802.11ac\n\n\n\n2.4ghz is password protected for zero touch\n\n\nNote that this breaks compatibility with older systems because the capabilities feature has a different model and the coarse ID is different"}
{"comment": {"body": "Ouch. We should try and divide to interface-specific and non-interface-specific streamers upon refactor.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/425/_/diff#comment-168798181"}}
{"comment": {"body": "Agreed", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/425/_/diff#comment-168798392"}}
{"comment": {"body": "Should we keep that?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/425/_/diff#comment-168857051"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} asked to keep it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/425/_/diff#comment-168862631"}}
{"title": "Fix DNS lookup on the router", "number": 426, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/426", "body": "Remove unused dnsmasq config\nConfigure the router to resolve DNS using 8.8.8.8 (used to use dnsmasq, but it is no longer running)\n\n"}
{"comment": {"body": "I fail to see how this is relevant to DNS", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/426/_/diff#comment-168638781"}}
{"comment": {"body": "Solely a QOL improvement. This is used only for prints and Gal complained about it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/426/_/diff#comment-168650046"}}
{"title": "Force non-zero RA lifetime even when there's no upstream router", "number": 427, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/427", "body": "Router Advertisements (RAs) have a field called Router Lifetime.\nQuoting Aruba:\n\nRA lifetime: The lifetime associated with the default router in seconds. A value of 0 indicates that the router is not a default router and will not appear on the default router list. The router lifetime applies only to the router's usefulness as a default router; it does not apply to information contained in other message fields or options.\n\nThis value is usually inherited from the upstream router / DHCPv6 server (e.g. lableft when running in this configuration), but is set to 0 by odhcpd when theres no such router.\nIt seems like most devices ignore this field, but iOS 14 started ignoring RAs where it was set to 0.\nAdding to the confusion is the fact that dnsmasq seems to send 12 when it had no upstream router, which incidentally made working with iOS14 possible.\nNewer versions of odhcpd have a setting to control the value of this field, but our version does not.\nra_default is an alternate field that sets the value of the field if theres no upstream router."}
{"comment": {"body": "That\u2019s a great catch! Well done!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/427/_/diff#comment-168802181"}}
{"comment": {"body": "HOW THE FSCK IS THE LIFETIME FIELD AFFECTED BY **DEFAULT** CONFIGURATION???\n\nThis blows my mind. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/427/_/diff#comment-168802909"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/427/_/diff#comment-168805219"}}
{"title": "CFR slopes pilot version", "number": 428, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/428", "body": "Includes movement compensation\n2dFFT is 4096x256 instead of 1024^2, for performance considerations\nSignificantly improved FN situation\nDoes not consider packets that are too noisy (instead of returning False) - e.g. moving iPhone 6s are simply disregarded if frequency domain is too messy or unclear.\n\n"}
{"comment": {"body": "nice to see that you went for Cartesian coords and not polar", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/428/_/diff#comment-168841789"}}
{"comment": {"body": "Looks great! Good job!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/428/_/diff#comment-168842472"}}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/4048942478-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/428/_/diff#comment-168847345"}}
{"comment": {"body": "only @{5a4500fe0cacf235de82a9d4} can approve", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/428/_/diff#comment-168847630"}}
{"comment": {"body": "How is the runtime now?\n\nPlease ask the QA to examine if a major delay is felt when running the system\n\n@{5b7bbc6cc5e21441d0929b7e}", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/428/_/diff#comment-169269976"}}
{"title": "fix HE capabilities mismatch (CCP-278, CCP-279, CCP-280)", "number": 429, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/429", "body": ""}
{"title": "Feature/wifi slopes continued", "number": 43, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/43", "body": "Add class SDK_FFT_Motion for more stable wifi slopes feature extraction.\nFeature extract may now return None if it detects unstable feature (for example, during motion).\nRequired sample count is now 800 samples and the feature extract duration (on my PC) is around 1.7 seconds, so its around 10.5 seconds in total.\nRelevant research page:\n\n\n\nRelevant files:\n\nccpilot/processes/models/classification/wifi_classifier.py\nccpilot/processes/models/training/wifi_trainer.py\nworkspace/wifi_slopes/ccpilot_wifi_slopes.py\n\n\n"}
{"title": "Odhcpd and setupsh", "number": 430, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/430", "body": "Don't fail setup.sh when dnsmasq is already stopped\n(cherry picked from commit 1b2016a60456bfb16eca202dc44ffa1dda965b6b)\n\n\nAlso don't \"/etc/init.d/odhcpd enable\" 'cause it seems to be unsupported\n(cherry picked from commit adf316f5f3dbca0df0273df16de0e9957be93459)\n\n\n"}
{"title": "DHCPv6 is mandatory for some devices", "number": 431, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/431", "body": "DHCPv6 is mandatory for applicable devices\n\nIncrease timeout to allow capturing missing SOLICIT message\n\nSOLICIT is a response to Router Advertisement message, which comes every 3 seconds, so if 1 message is missed, the SOLICIT message would arrive after 6+epsilon seconds.\n  Itll be better to wait 7 seconds if theres 1 RA or SOLICIT message missing.\n\n\n\n"}
{"comment": {"body": "As we discussed, to make dhcpv6 mandatory we need to change the dhcpv6 wait criteria to include platforms that supports DHCPv6 \\(Apple, Linux, Windows\\) and not exclude platforms that don\u2019t support DHCPv6 \\(Android\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/431/_/diff#comment-169311471"}}
{"title": "correct parameter window length comparison for training part", "number": 432, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/432", "body": "I accidentally accumulated a wrong number of packets after discarding corrupted (moving) packets, so that the Training didnt actually finish (but reported as having finished).\nIts corrected."}
{"title": "DHCPv6 whitelist", "number": 433, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/433", "body": "Instead of blacklist Android, whitelist Apple, Linux, Windows"}
{"title": "Fix stress in app", "number": 434, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/434", "body": "Added missing URL scheme for CFR Companion app\nBumped app version\n\n"}
{"comment": {"body": "Version available on fdroid repo?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/434/_/diff#comment-169729088"}}
{"comment": {"body": "Not yet, uploading now", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/434/_/diff#comment-169729100"}}
{"title": "Bugfix/CCP-290 unqurantine doesnt work", "number": 435, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/435", "body": "Releasing from quarantine wasnt supposed to work for a while now (since zero touch), it seems.\nAfter releasing from quarantine, retrieved \"authorization resolve\" is DEVICE_WAITING_CLASSIFICATION, which did not cause the device to actually be unblocked."}
{"title": "CCP-302/handle broadcast dhcp ack", "number": 436, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/436", "body": "Rename dhcpv4 processor\nAdd some unittests for dhcpv4\nin the case of boot reply, use the \"client_mac_address field in the DHCP packet\n\n"}
{"comment": {"body": "Unreasonable beauty standards", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/436/_/diff#comment-169826881"}}
{"comment": {"body": "MAC ad**dresses** are defined by social media", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/436/_/diff#comment-169828952"}}
{"title": "Feature/dualband zero touch interoperability real", "number": 437, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/437", "body": "Add 3rd all.sh param to select wifi interface. 0 for 5ghz and 1 for 2.4ghz, 2 for both\nCoarse ID now ignore 802.11ac and vht_caps\n\nCap feature is network band aware\n\nDiscard VHT caps, power smps, supported channels and 802.11ac\n\n\n\n2.4ghz is password protected for zero touch\n\n\nNote that this breaks compatibility with older systems because the capabilities feature has a different model and the coarse ID is different\n\n\nThis is a copy branch of the original one since it broke from some reason\n"}
{"title": "Update server with device idle status", "number": 438, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/438", "body": "The list of connected device is derived from the output of the wlanconfig command:\nADDR               AID CHAN TXRATE RXRATE RSSI MINRSSI MAXRSSI IDLE  TXSEQ  RXSEQ  CAPS XCAPS ACAPS     ERP    STATE MAXRATE(DOT11) HTCAPS   VHTCAPS ASSOCTIME    IEs   MODE RXNSS TXNSS                   PSMODE\n04:d6:aa:a1:bb:45    1   40 433M    526M  -58     -69     -53    0      0   65535    EP   BOI NULL    0          b        1083332            AWPS            gGTR 00:08:14     RSN WME IEEE80211_MODE_11AC_VHT80  2 2   0\nUntil now we only extracted the first field, now were also extracting the IDLE column, which is (probably) the amount of time in seconds since the last transmission.\nThis data is used to handle scenarios where iPhones move from one band to the other without sending a disassociation. \nIn this case, the old connection should have a non-zero idle."}
{"comment": {"body": "Would this work for wpa2-less networks?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/438/_/diff#comment-169874963"}}
{"comment": {"body": "Maybe it'll be better if we drop this script altogether and just do the parsing with Python?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/438/_/diff#comment-169874999"}}
{"comment": {"body": "No, that\u2019s why this is only checked in the else branch \\(this check was here before\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/438/_/diff#comment-169875157"}}
{"comment": {"body": "Nah", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/438/_/diff#comment-169875196"}}
{"comment": {"body": "@{5dc7c317ea86a50c6c4c7244} Missed that. Thanks", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/438/_/diff#comment-*********"}}
{"comment": {"body": "That\u2019s why he\u2019s called Gilad Nahaman", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/438/_/diff#comment-*********"}}
{"title": "Feature/scp over serial port", "number": 439, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/439", "body": "I made a script to download/upload files to the comcast routers, with similar syntax to scp.\nUsage: python3 serial_scp.py [serial port:]filepath [serial port:]filepath\nWhere the first argument is the source and the second is the destination.\nExample: python3 serial_scp.py /dev/ttyUSB7:/lib/file.so file.so\nTheres progress bar and MD5 check in the end.\nPlease share your thoughts about this."}
{"comment": {"body": "The S in SCP can already stand for serial", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/439/_/diff#comment-*********"}}
{"comment": {"body": "I can\u2019t really call this project `scp`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/439/_/diff#comment-*********"}}
{"comment": {"body": "why not, it\u2019s not trademarked", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/439/_/diff#comment-*********"}}
{"comment": {"body": "might confuse people to think that it's _secure_", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/439/_/diff#comment-*********"}}
{"comment": {"body": "Sounds like a \"them\" problem", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/439/_/diff#comment-169917401"}}
{"title": "Patch to make the cursor bug appear less often", "number": 44, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/44", "body": "Omer is working on a complete fix.\nThis is just a patch so Dor wont have the server crash so often."}
{"title": "Feature/serial scp v2", "number": 440, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/440", "body": "More refactor\nscp also copies file attributes\nBetter comcast AP error cleaning\n\n"}
{"title": "Wifi band indication in frontend", "number": 441, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/441", "body": "Added Icons for 2.4GHz and 5GHz. API reports accordingly."}
{"title": "Update frontend's polling behavior", "number": 442, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/442", "body": "New polling behavior: requests sent every 150 ms; If server responds within 150 ms, next request will be sent when the full 150 ms interval passes; If server responds within 1 sec, next request will be sent immediately; If server takes more than 1 sec to reply, timeout is logged to console and the process back-offs for 1 sec before sending another req"}
{"comment": {"body": "Approved if you made sure the server indicator at the top doesn't go crazy because of this", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/442/_/diff#comment-170574102"}}
{"comment": {"body": "Looks OK, when server is online it is continuously green, otherwise red.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/442/_/diff#comment-170574401"}}
{"comment": {"body": "On a general note, I think that these type of changes should be pointed to master and then merged to dualband\\_\\* branch after merging to master ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/442/_/diff#comment-170594985"}}
{"title": "Avoiding potential MacPinger infinite loop", "number": 443, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/443", "body": "Connections monitoring could be stuck forever adding a pinger/arper, waiting for a device to get an IP. Added a 15 seconds timeout"}
{"title": "Trial", "number": 444, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/444", "body": "If feature is undefined (points are too scattered) - meta should be None rather than having a meta with None entries. This way a model will not be build when in fact the model is undefined.\n\n"}
{"title": "return meta = none if feature should be disregarded", "number": 445, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/445", "body": "Does not create a model if the points are too scattered, instead of returning a model of Nones that generally messes up the flow.\nFor training - if 2 or more of the windows return None - drop the entire model. Since None occurs only if the data is definitely corrupted (so that wed want it to be disregarded anyway), this should not introduce falses.\n\n"}
{"comment": {"body": "Ah, beautiful code! I\u2019m approving, but wait for @{5a4500fe0cacf235de82a9d4}  to approve as well", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/445/_/diff#comment-171028926"}}
{"title": "Now ignoring capabilities when calculating coarse_id", "number": 446, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/446", "body": "Not waiting for capabilities events\nIgnoring capabilities when calculating coarse_id\n\n"}
{"comment": {"body": "Let\u2019s open a separate branch for the CC integration work.  \n  \nI don\u2019t want it to be pushed to master yet..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/446/_/diff#comment-171033414"}}
{"comment": {"body": "Sure, on it\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/446/_/diff#comment-171033437"}}
{"comment": {"body": "Retargeted branch onto `comcast_integration`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/446/_/diff#comment-171033523"}}
{"title": "serial scp: split send stream to multiple sends", "number": 447, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/447", "body": ""}
{"title": "Added script to connect the router as a client", "number": 448, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/448", "body": ""}
{"title": "Demo/only tcpdump and simple ap agent", "number": 449, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/449", "body": "create simple xb_agent which runs:\n\na tcpdump stream\n\na connections stream against port 6000\n\nmac addresses sources come from dmcli parsing\n\n\n\n"}
{"title": "kill sneks", "number": 45, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/45", "body": "Closes CCP-35"}
{"title": "Master", "number": 450, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/450", "body": "Periodic updates from master"}
{"title": "Gilad/linkhalwifi", "number": 451, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/451", "body": "Added test executable that queries libhal_wifi.so for some details.\nWe could extract associated clients from xb6, but not on xb7 (probably because the network is down).\nThe associated client list is a string. No idea what is the separator between addresses because we could only get one device connected.\nAn ARM-BE target can be added when get the toolchain\n"}
{"title": "Fix motion code bug", "number": 452, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/452", "body": "fix bugs\nChange threshold\n\n"}
{"title": "User motion commands api", "number": 453, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/453", "body": "2 simple get routes to allow QA to write logs for actual movements. For later analysis of algo latency."}
{"title": "force hydras 3.1.2 since 3.1.1 is broken", "number": 454, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/454", "body": ""}
{"title": "Make motion detection algorithm nicer by handling errors", "number": 455, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/455", "body": "Capture exceptions from motion detection and hide them\nCFR processing also handles 40mhz\n\nReason for these changes is the following exception:\n\u001b[36mclassifier_1  |\u001b[0m Process MotionDetectorProcess-9:\n\u001b[36mclassifier_1  |\u001b[0m Traceback (most recent call last):\n\u001b[36mclassifier_1  |\u001b[0m   File \"/usr/local/lib/python3.8/multiprocessing/process.py\", line 315, in _bootstrap\n\u001b[36mclassifier_1  |\u001b[0m     self.run()\n\u001b[36mclassifier_1  |\u001b[0m   File \"/root/src/ccpilot/processes/models/common/motion_detect.py\", line 85, in run\n\u001b[36mclassifier_1  |\u001b[0m     res = self._handle_cfr(packet)\n\u001b[36mclassifier_1  |\u001b[0m   File \"/root/src/ccpilot/processes/models/common/motion_detect.py\", line 60, in _handle_cfr\n\u001b[36mclassifier_1  |\u001b[0m     motion_indication = self._detector.handle_cfr(packet)\n\u001b[36mclassifier_1  |\u001b[0m   File \"/root/src/cfr_motion_detect/__init__.py\", line 128, in handle_cfr\n\u001b[36mclassifier_1  |\u001b[0m     for chain_id, chain in enumerate(_preprocess_wrapper(packet)):\n\u001b[36mclassifier_1  |\u001b[0m   File \"/root/src/ipq_cfr/cfr_processing.py\", line 74, in preprocess_packet\n\u001b[36mclassifier_1  |\u001b[0m     raise NotImplementedError(\n\u001b[36mclassifier_1  |\u001b[0m NotImplementedError: CaptureBandwidth.BW40MHZ preprocess not implemented\n"}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} waiting for approval", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/455/_/diff#comment-172303437"}}
{"comment": {"body": "Does this actually work?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/455/_/diff#comment-172303524"}}
{"comment": {"body": "should work", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/455/_/diff#comment-172303585"}}
{"comment": {"body": "Did we verified it\u2019s working?  \nOtherwise, why we are including this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/455/_/diff#comment-172303743"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} it works. it\u2019s included to not throw exceptions in this capture mode since it did throw an error.\n\nI can remove this if you want", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/455/_/diff#comment-172308798"}}
{"title": "Enable usage of VHTCaps with only 4 bytes", "number": 456, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/456", "body": ""}
{"title": "added bandwidth consideration and different boundaries", "number": 457, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/457", "body": "changed k_x and k_y decision boundaries\nadded a quick&dirty patch to see whether a packet is at 20MHz, 40MHz or 80MHz, and multiply the frequencies if it is indeed in 20MHz (as the data indicates should be done).\n\n"}
{"comment": {"body": "any chance to not-magic-number here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/457/_/diff#comment-172412223"}}
{"comment": {"body": "Yes, but I need to extract the bandwidth from one of wifiutils functions and it\u2019s gonna be pretty ugly. So for testing purposes I prefer magic-numbering, if it\u2019s working w/o any problems I\u2019ll correct that", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/457/_/diff#comment-172412660"}}
{"comment": {"body": "@{5b7bbc6cc5e21441d0929b7e} I\u2019ll remember this promise. No tricks and no shticks, right?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/457/_/diff#comment-172413227"}}
{"title": "Ap agent from libhal wifi", "number": 458, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/458", "body": "Create CMake project for station agent\n\nSingle project for XB3 and XB6/7\nCompile for XB3 using docker image located on levlcompute\n\n\n\n\nThis is still WIP as:\n\nXB3 doesnt have the required implemented functions\n\nbut if you have comments in the meantime, do share.\n"}
{"comment": {"body": "Is there anything left to do to have a full operational agent?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/458/_/diff#comment-172635499"}}
{"comment": {"body": "XB3 doesn't have a the appropriate symbols, but it works for xb6/xb7", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/458/_/diff#comment-172636061"}}
{"comment": {"body": "Isn\u2019t the code at **station\\_agent\\_dmcli.sh** supposed to the be the alternative?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/458/_/diff#comment-172636529"}}
{"comment": {"body": "oh, yes. **station\\_agent\\_dmcli.sh** still works", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/458/_/diff#comment-172684381"}}
{"comment": {"body": "To have the same management data sent from the AP as in current QCOMM APs, we should also complete the ap\\_info generation. Right now only connection reports are being sent", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/458/_/diff#comment-172729361"}}
{"title": "cfr ver for pull", "number": 459, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459", "body": "added bandwidth consideration and different boundaries\nfactors frequency for 20MHz bandwidths and drops consecutive packets where bandwidth changes\n\n"}
{"comment": {"body": "so you\u2019re basically dumping packets that have different bws. how about you perform this check earlier \\(maybe right after defining `packet_bws `\\) instead of doing the whole preprocessing and then checking this? This would make this more readable.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172442329"}}
{"comment": {"body": "These packets also need to fulfill valid x and y vectors. So I need to process it anyway", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172442836"}}
{"comment": {"body": "@{5b7bbc6cc5e21441d0929b7e} ok", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172446561"}}
{"comment": {"body": "Can you explain in a few words what does the bandwidths factors do?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172693224"}}
{"comment": {"body": "I found out that for 20MHz bandwidths \\(both at 2.4GHz and 5GHz bands\\), the frequencies are halved, in comparison to 40 and 80Mz. Therefore I need to add a marker to each packet, and multiply the frequency wherever the packet has a 20MHz bandwidth", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172693786"}}
{"comment": {"body": "When I tested it over the data that I have, it improved the results and phones that returned FN when trained at 20MHz and classified at 80MHz \\(and vice-versa\\) were classified perfectly", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172694101"}}
{"comment": {"body": "Interesting.  \nIs it just someone you saw experimentally on the data or does it documented anywhere?  \n  \nIt can also effect the presence/motion detection algorithm. Is @{5bdac2ad92e2727e0939d08b}  aware of this behaviour?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172694598"}}
{"comment": {"body": "I\u2019m not sure what \u201cthe frequencies are halved\u201d means. @{5b7bbc6cc5e21441d0929b7e} , do you mean that there are less bins \\(x-axis\\) or that the amplitudes are two times smaller \\(y\\_axis\\)? If it\u2019s non-trivial, please send us an example figure ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172697121"}}
{"comment": {"body": "The density of stripes in the dphi/dt space is doubled for 40 and 80MHz bandwidths. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172697856"}}
{"comment": {"body": "Ok cool. That doesn\u2019t mean that the packet rate is doubled or anything, just that you observe different residual CFOs, right?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172698410"}}
{"comment": {"body": "Yes, it\u2019s a different feature \u201cfrequency\u201d. Not actual physical frequency", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/459/_/diff#comment-172699474"}}
{"title": "Feature/server cont integration infra", "number": 46, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/46", "body": "This adds CI tests on each push/PR. Basically added a pipeline that sets up an isolated environment, builds the server and runs tests. Dont pay too much attention to the test that is being run now itself, its just for sanity and will not necessarily live for long. Im adding a bunch of other tests soon."}
{"comment": {"body": "Add coverage sometime?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/46/_/diff#comment-146354403"}}
{"comment": {"body": "For now it seems like all these tests will have to run one by one. Each of them taking a while.\n\nMaybe we can create like 50 server containers, with names test\\_env1, test\\_env2, etc. then all these servers will act as a pool the tests can use in parallel?\n\nA test simply asks for a `server` fixture, and automatically gets assigned a server that\u2019s both available and clean. Once the server is used up, it gets re-cleaned for the next test to use during teardown.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/46/_/diff#comment-146355277"}}
{"comment": {"body": "That\u2019s a really good idea. I will add it as a separate task. Only caveat is that each container takes some storage, and in addition we have multiple executors - so we just need to select a good pool size. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/46/_/diff#comment-146358083"}}
{"comment": {"body": "Should be done later, not in scope. Will place in separate task.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/46/_/diff#comment-146358312"}}
{"comment": {"body": "Alright. I\u2019ve done some research regarding how you can refer to each of these servers from the host.\n\nFirst you need to run each container with a `--hostname` parameter, like this for example:\n\n`docker run -d --hostname thisisahostname ubuntu:16.04 sleep 500`\n\n\\(In our case, each server container will get a different hostname\\)\n\nAfter that, the container will be accessible **from other containers** via the hostname `thisisahostname`. For example, another container could ping the above machine with `ping thisisahostname`.\n\nOur problem is that we want the hostname to be accessible from **the host running the container**.\n\nThere are two options here:\n\n1. Have the tests themselves run from inside a container parallel to the server containers, then it could refer to them by hostname\n2. Keep running the tests on the host, but then you\u2019d have to fire up this special container \\(only one instance required\\):\n\n```\ndocker run --hostname dns.mageddo --restart=unless-stopped -p 5380:5380 \\\n-v /var/run/docker.sock:/var/run/docker.sock \\\n-v /etc/resolv.conf:/etc/resolv.conf \\\ndefreitas/dns-proxy-server\n```\n\nThis container automatically turns itself into a DNS server on your machine \\(it can do that because you mounted `/etc/resolv.conf` to it, thus giving it access to the host DNS configuration\\) and resolves all Docker hostnames on the host as if the host was part of the containers network.\n\n\u200c\n\nNow the tests can pick a server just by changing `localhost` to `somehostname1`, `somehostname2`, etc\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/46/_/diff#comment-146360049"}}
{"comment": {"body": "I would also suggest to try to deploy a cluster of hosts using Kubernetes, just to learn and get familiar with the technology.   \nWe could automatically deploy a test agent containers and server container and define the connectivity between them in a programable way.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/46/_/diff#comment-*********"}}
{"comment": {"body": "From what I\u2019ve read k8s has a very steep learning curve and a lot of configuration complexity overhead, so we should take that into account before deciding to try it out", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/46/_/diff#comment-*********"}}
{"comment": {"body": "I know.\n\nWe will probably have to learn it anyway if we would need to deploy a backend that actually serves different costumers, so it might be a good chance to start :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/46/_/diff#comment-*********"}}
{"title": "Fixed wrong access to HTCaps._asel_caps", "number": 460, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/460", "body": "This var is a single byte long (i.e. int), should not index"}
{"title": "Added is_fcs_valid property to radiotap", "number": 461, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/461", "body": ""}
{"title": "Restore session state", "number": 462, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/462", "body": "Saving session states on classification state change to restore it on the server's next startup. Linking the restored session to an existing device based on the permanent_id that session had in the previous run. Some changes are required in the management endpoint as well: When it starts getting connection reports from the AP it syncs the connection map with the restored sessions.\nPlease note that this is only intended to be used for a very brief downtime, and it is assumed that during this downtime the AP is in steady state. Some edge cases caused by not following this assumption might be handled gracefully, but many surely arent."}
{"comment": {"body": "Can you add an option to _run\\_server.sh_ to delete the session file. Also that -r will delete it as well?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/462/_/diff#comment-172694251"}}
{"comment": {"body": "Will do. Current state is that if the DB is deleted, the sessions aren\u2019t restored but the file persists.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/462/_/diff#comment-172696404"}}
{"comment": {"body": "Done.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/462/_/diff#comment-172728158"}}
{"title": "Comcast integration easy install", "number": 463, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/463", "body": "Add a script in ccpilot/ap_agents/xb_agent/deploy.py to deploy agent to XB* target\n\n\nTo deploy to xb3 over scp, call\npython3 ./deploy.py scp 2022 xb3 \n\n\nTo deploy to xb3 over serial, call\npython3 ./deploy.py serial /dev/ttyUSB4 xb3\n\n\n\n\nDeployment with scp tested from levlcompute\n\nDont forget to setup ssh from <NAME_EMAIL> if you want to deploy from there\n\n\n\nAdded a script to start ssh server on the routers\n\n\nFor XB7, call with\npython3 ./install_xb_ssh.py /dev/ttyUSB2 2024 levl@**********\n\n\n\n\n"}
{"title": "Comcast integration fix execute", "number": 464, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/464", "body": "Add execute after scp\nAdd SSH session reuse for faster script execution\n\nMaybe well switch to rsync at some point\n"}
{"title": "XB3 libhal_wifi version 2.19.0", "number": 465, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/465", "body": "Just a version bump, apparently"}
{"title": "Wired stations support", "number": 466, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/466", "body": "This branch was initiated by @{5dbeb866c424110de52552cc} and @{5b72a213e72afd064c8a4ebd} and had the purpose of supporting wired stations. The main logic changes here are in connections.py and ap_agent.py, regarding to the separation between wired and wlan macs, and the source of new macs detection. Disconnections are detected by ARPing, and the new arp_disconnection_detection.py is responsible for it.\nAfter creating this branch, some changes had to be made to match the way the macs related structs were stored and represented in master."}
{"comment": {"body": "The mac address tuple was on purpose for discovering changes between bands. \\(moving from 5ghz to 2.4, for example\\). This breaks this behavior", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/466/_/diff#comment-173594918"}}
{"title": "session restore bugfix - correct type when comapring connections", "number": 467, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/467", "body": "Use only the network band's value when restoring conns/map, for a valid comparison with the parsed connection reports"}
{"title": "Comcast integration xb3 atom libhal wifi", "number": 468, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/468", "body": "Add appropriate library for XB3 Atom \nAdd toolchain based on docker for XB targets\nxb_agent.sh to detect target and run appropriate station agent\nremove scripts for armv6b\nConsolidate CMakeLists.txt targets\nMinor change to serial_scp.py since the console of xb3 is different from xb6/7\n\n"}
{"title": "Comcast integration tunnels recovery", "number": 469, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/469", "body": "Move xb_agent workspace to seperate comcast_xb workspace\n\nChange xb_agent.sh:\n\nRecover network connections if they die by monitoring the processes\n\nReplace SSH with nc\n\n3rd party nc since busybox nc doesnt work\n\n\n\n\n\nXB3 has br0 instead of brlan0 so make that check before calling tcpdump\n\nSome more small workspace magic\n\n"}
{"title": "Fix tee connection refused error", "number": 47, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/47", "body": "an unexpected socat message tripped up tee"}
{"title": "Phase out coarse levl ID", "number": 470, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/470", "body": ""}
{"comment": {"body": "what about going all the way and remove this file/leave appropriate functions only?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/470/_/diff#comment-173950092"}}
{"comment": {"body": "Step by step..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/470/_/diff#comment-173971918"}}
{"title": "C++ support", "number": 471, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/471", "body": "Change project to use gcc 4.8.5 (from ubuntu 16.04) and its available features (C99 & C++11), which is the latest toolchain ABI-compatible with all 3 platforms.\nQuestions about using a 5 year old toolchain?"}
{"title": "Xb agent adding ap info", "number": 472, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/472", "body": "This PR contains cherry-picks from xb_agent_add_ap_info branch regarding to adding the ap info reports data. The structure of the returned data is not yet finished, since returning an iface map is also require. For now lets merge it to avoid more conflicts (The directory was changed, file names has been changed, etc)."}
{"comment": {"body": "what\u2019s the reason for these changes?\n\ntoolchain is the same for all architectures", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/472/_/diff#comment-174147563"}}
{"comment": {"body": "Old changed were reverted :\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/472/_/diff#comment-174876527"}}
{"title": "Comcast integration mgmt frames cloud endpoint", "number": 473, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/473", "body": "Add MgmtPacket\n\nBased on JSON format\nWith some similarities to radiotap\ncan convert to scapy easily\nugly hack to convert to dpkt\n\n\n\nAdd mgmt stream parser and endpoint\n\nData_processor munches on mgmt stream and pushes it to preassociation cache \nCapfingerprinting is aware of MgmtPacket \nWifi trainer and classifier are also aware of MgmtPacket\n\n"}
{"title": "Comcast integration mgmt frames xb3 side", "number": 474, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/474", "body": "Edited mgmt_frames_pipe to send the packets to xb3 server\nAdded json.hpp library to handle our packet sending\nAdded mgmt_frames_pipe_XB3 for now, might be deleted later when we decide on a better way to handle it. Updated xb_agent with some changes regarding to listening to the arm processor. Updated the deployment to xb3 to copy the mgmt_frames as well\n\n"}
{"comment": {"body": "we need to remember to fix this later since this is XB3-specific :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/474/_/diff#comment-174872479"}}
{"title": "Support static ip", "number": 475, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/475", "body": "Support for static IPs. Turned out to be quite a big PR. Main changes:\n\nThe coarse_id device linking supports states where data is missing: On wired connections, we should move on without caps; On wireless static connections, we should move on without DHCP.\nThere is also the unique but not necessarily rare case of devices with randomized MACs and no DHCPv6 and static IPs. We now allow them to be linked only based on caps. If they are classified in the future with additional paramaters (HW MAC, DHCPv6 DUID etc.), we update them and require that further classifications will use these parameters (unless they are static).\nstatic wired devices are currently not supported since we don't have neither caps or dhcp.\nAs the support for wired started moving in the direction of the agent self discovering the stations' interfaces and IPs (with no help from DHCP), this branch does another step in that direction. On the server side, the IP is no longer taken from the dhcp leases given in ap_info updates. On the agent side, the Connections class has been slightly refactoted and handles connection/disconnection events in a more constructed way. It was added a significant logic to be able to detect whether a connection is static/dynamic and its IP. Those details are now added to the report_connections data.\nMAC pinger now relies on Connections, that used to rely on MacPinger completing the connection/disconnection callback. We untie this new circular dependency by running a MacPinger thread that receives pinging requests from Connections.\n\nComments:\n\nWe still have the DHCP log tracking to detect wired connections, but since we minimized our reliance on DHCP, we can easily use the new arping functions to detect such connections.\nIn addition, we would like to monitor changes in connection properties even when they don't disconnect/connect.\n\n"}
{"comment": {"body": "care to explain logic based on hardcoded value `br-demo`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/475/_/diff#comment-174953735"}}
{"comment": {"body": "Looking at the AP data hierarchy, we have 3 entities that are currenly not handled well. More specifically, the relation between them is undefined, but should be. These are the physical interface, the virtual interface and the network. Given a MAC, we would like to be able to tell to which phy iface, vap and network it belongs. Today a lot of this is hardcoded, like the relation between `athX<->wifiX`. In the ARP table case, the interface column lists the MAC\u2019s associated network. We only care for stations in br-demo. br-demo is not deduced in an orderly manner as an \u201cAP entity\u201d anywhere today, it is assumed to be known. Unfortunately it is true here as well.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/475/_/diff#comment-174956353"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Updated the use of `ConnectionInfo`. Now no external entity accesses the nested classes `ConnectionKey` and `ConnectionDetails`. Added the option for `ConnectionInfo` itself to be used as key of dict or item in set. Whenever we need to iterate over stations we return a **set** of `ConnectionInfo` objects. In the `Connections` class we hold one \u201cDB\u201d that is actually a `Dict[ConnectionInfo, ConnectionInfo]` - `self.detailed_connections`. We use a dict to be able to search connections efficiently, and we also take advantage of the fact that `self.detailed_connections.keys()` returns a `KeysView` that behaves like a set of `ConnectionInfo` objects \\(we get clean and efficient set operations between sets of connections and the \u201cDB\u201d\\).", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/475/_/diff#comment-175206675"}}
{"title": "Comcast integration mgmt frames", "number": 476, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/476", "body": "New wifi hal\n\nMainly for XB3\n\n\n\nAdd mgmt packet reader for XB3 side\n\nAdd mgmt packet streamer and handling on server side\n\nUpdate XB3 agent:\n\narm processor sends ethernet packets to x86\nx86 sends connection, ethernet, management steams to server\n\n\n\n"}
{"title": "Xb agent add ap info", "number": 477, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/477", "body": "Merge changes from master from Sept. 1st\nAdd ap_info report\n\nThis replaces the other PR\n"}
{"title": "Comcast integration upstream", "number": 478, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/478", "body": "More upstream changes\nconnections_report to send iface instead of band\n\n"}
{"title": "Allow slim coarse id matching when no additional parameters available", "number": 479, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/479", "body": "When we find only one matching device based on coarse ID, we allow it to be chosen even when there are no additional intersecting parameters, but only in the case where at least one valid value in the session parameters was never set in the device record.\nThis solves the issue raised in ."}
{"title": "Nicer looking log table", "number": 48, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/48", "body": ""}
{"comment": {"body": "if you say so", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/48/_/diff#comment-146389228"}}
{"title": "Recsys new", "number": 480, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/480", "body": "We need to merge this work into master so that we dont have thousands of operational branches.\nIm not familiar with the content, so do your best to review this code and see if we have bugs.\n"}
{"comment": {"body": "Undo this change", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/480/_/diff#comment-176147958"}}
{"comment": {"body": "lol wtf who touched my pr? @{5dc7c317ea86a50c6c4c7244} ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/480/_/diff#comment-178566392"}}
{"comment": {"body": "[https://www.youtube.com/watch?v=T\\_x6QmuJdms](https://www.youtube.com/watch?v=T_x6QmuJdms)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/480/_/diff#comment-178599922"}}
{"title": "More data on ethernet", "number": 481, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/481", "body": ""}
{"title": "Auto build x86 docker container", "number": 482, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/482", "body": "\n"}
{"title": "Comcast integration xb agent add authentication and ignore pods", "number": 483, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/483", "body": "Connections report: Ignore pods in pods SSID\nConnection report to ignore non-authenticated devices when in authenticated network\nSome code refactor\n\n"}
{"comment": {"body": "Did the additional HAL API calls increase the run time?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/483/_/diff#comment-176791620"}}
{"comment": {"body": "Apparently, yes. from 130ms to 320ms for 2 devices", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/483/_/diff#comment-176792448"}}
{"title": "Xb6 integration", "number": 484, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/484", "body": "New deployment scripts for xb6 and xb3\nMove files around\n\n"}
{"title": "Comcast integration less ethernet", "number": 485, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/485", "body": "Revert tcpdump filter\n\nI saw that many Ethernet packets arrive and cause ever increasing latency for packets since the server cant process all of them. This means that DHCP packets may arrive after just_connected timeout.\n\n\n\nSame timeout for both bands in 1 fishy place\n\n\n"}
{"title": "Comcast integration unreachable router ui", "number": 486, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486", "body": "Now using ap_info as a keepalive mechanism to detect unreachable router situations.\n"}
{"comment": {"body": "So now there are 2 indicators, the new one being beneath the original one?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176956398"}}
{"comment": {"body": "Yes, both server and router reachable / unreachable", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176956731"}}
{"comment": {"body": "There is an issue with relying on ap\\_info reports:\n\n```\nhave_changes = sum(len(value) for value in ap_info_updates.values()) > 0\n\nif not have_changes:\n    continue\n```\n\nIt doesn\u2019t send reports if there is no new information :pensive: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176957620"}}
{"comment": {"body": "Feels like this segment can be simplified but nothing crucial.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176957821"}}
{"comment": {"body": "If you think it\u2019s better the way it is right now from a readability aspect or any other reason I won\u2019t argue.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176958106"}}
{"comment": {"body": "The xb\\_agent does send them continuously.\n\n@{5ed4f1de9a64eb0c1e78f73b} , can you make sure it\u2019s also work on the Cypress system?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176958645"}}
{"comment": {"body": "I hoped the expiry seconds field in the dhcp\\_leases would do the job :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176959370"}}
{"comment": {"body": "It would 99% of the time, but the dhcp leases list can be empty\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176962345"}}
{"comment": {"body": "In case of empty dhcp\\_leases, it doesn\u2019t work on the Cypress routers", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176982709"}}
{"comment": {"body": "Maybe the indicator can be radio taps?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176984218"}}
{"comment": {"body": "Why not the incoming connections report?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/486/_/diff#comment-176984413"}}
{"title": "Prev mac addr per ssid", "number": 487, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/487", "body": "Saving last mac address for each ssid the device has used (instead of saving last mac address per network type). This is because devices randomize macs based on ssids, that can obviously be altered on the same band.\nNow we send the ssid the device has connected to on each connection report from the AP. Instead of saving the last mac address per band in the device record (cache and DB), we keep a variable-sized dict that contains all of the previous ssids the device has connected to and the last mac used for each. There is currently no mechanism to limit the number of ssids saved per device.\nBranch management: I want to push these changes to master. There is another branch with the needed update to the XB agent, which I would like to push to comcast_integration, in addition to cherry picking changes from this PR to comcast_integration (unless we are okay with merging master to comcast_integration)."}
{"comment": {"body": "Can you verify that for the first time a device is onboarded \\(without any classification\\), the SSID and mac address are saved in the DB?  \n I try to go over this path in the code and I\u2019m not 100% sure it\u2019s saved", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/487/_/diff#comment-177578765"}}
{"comment": {"body": "It\u2019s the same as it was with `prev_mac_addr_2ghz`/5ghz/wired - if no matching device is found we go to training, but before we do so we update the device record:\n\n```\nfor device_field_str, val in extracted_parameters_dict.items():\n    setattr(session.device_record, device_field_str, val)\n    logging.info(f\"Setting device record of {device_field_str} to {val}\")\n```\n\nAnd when training is done the record should be saved to DB.\n\nSo it should be OK and it also seemed fine in my little test, I shut down the server and when I turned it back on it was able to load this attribute from the device record.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/487/_/diff#comment-177584791"}}
{"comment": {"body": "I agree with the master->comcast\\_integration merger", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/487/_/diff#comment-177585949"}}
{"title": "Xb agent add device ssid", "number": 488, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/488", "body": "Following , adding the ssid information per device in the connection report from the XBs.\n\nChanged the way the json is passed to curl. Instead of printing to cout, effectively the shell, which is limited in buffer length on the APs, we are now writing the report json to a file in /tmp/ and using curl --data @filename to POST the contents of the file.\nWhile working on that last transition, I realized that there is an unexpected character being written to the json right after the mac address, for some reason because of the uint8_t*  std::string transformation. I didnt investigate it through, instead I casted the uint8_t* into char*, and from that point the compiler handles the cast to std::string. Its a quick fix, not necessarily the best one, if you have any thoughts please share.\n\nThis also includes a merge from master. The unique changes for this PR are only in files under comcast_xb and ap_agents/xb_agent."}
{"title": "Comcast integration mips toolchain", "number": 489, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/489", "body": "Improve multiple toolchain support with usage of cmake kits\n\nPut toolchain data in *-toolchain.cmake files\nIn VSCode, open the comcast folder and just select the appropriate cmake kit\nIn CLI, call cmake with cmake -DCMAKE_TOOLCHAIN_FILE=toolchain/i586-linux-toolchain.cmake .. (with i586 kit for example)\n\n\n\nMIPS: \n\nAdd dockerfile for mips and gcc/g++ wrappers\nAdd toolchain for pod target\n\n\n\nCMakeLists.txt has targets based on kit/environmental variable.\n\nWell improve this as we go if we find issues\n\n\n\n"}
{"title": "handle_segments better name", "number": 49, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/49", "body": ""}
{"title": "Comcast integration", "number": 490, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/490", "body": "xb_agent sends tcpdump to 6103 and result of dmcli to 6000 (this needs to be parsed)\nSimple dockerfile\nNow ignoring capabilities when calculating coarse_id\ngitignore levlcompute\nDockerfile and docker script\nAdded script to connect the router as a client\nap agent parses dmcli and sends it over http\nFix bash\nComments\nip\nfix\nport\nHotfix: Server crash & and wrong connect.sh interface\nxb agent now works with ssh instead of nc\nkill\ncleanup docker attempt at xb3 toolchain\nAdded tester app for libhal_wifi on intel based comcast router\nRe-added nonworking association callback\nserver is expecting lower case mac\nhandle non-atomic access to mac address source\nAdded xb3 files\nNo SNR and no band data is collected, so connectivity indication will just be online/offline\nmove files and create cmake project\ncmake and proxy gcc scripts\nFix volume paths and compiler falgs\nmore correct cmakelists.txt\ncpp support is not complete\nMore prints\nstart adding functions\noutput json\nxb3 support and refactor\nxb3 doesn't work right now\nCommies\nxb7\nFix working with XB7/XB6\nBetter error message\nPR comments\ndon't replace ssh key if it already exists\nSmall build retarget\nStart deploy scripts\nFile copying to target\nSome path fixes\ninstall xb ssh\nfix ssh agent installer\nInstaller works\nxb agent should really killall ssh\nStore PIDs of sshs and tcpdumps\nrun in background\nAdd some sleep because reasons\nfix execute after scp\nAdd execute after scp\nMuch faster deployment with SSH session reuse\nlist is redundant\nAdded 2nd application to gather ap_info reports data.\nAdd appropriate library for XB3 Atom Add toolchain based on docker for XB targets Make xb3 target work\nxb agent to detect target and run appropriate station agent\nremove scripts for armv6b\nConsolidate CMakeLists.txt targets\nit's not big endian. sorry for confusion\nAdd execute to bash files\nFix macos\ncmake fix\nnew wifi hal\nStart work on mgmt agent Not working ATM\nSome syntax fixing XB3 has br0 instead of brlan0 so make that check before calling tcpdump\nif processes die, revive them seems to work\nMigrate changes from xb3 server\nFixed issue to build properly + commented out encryption mode since the function is undefined(?)\nreplace ssh with nc\nReorganized xb workspace\n\nLast changes:\n\nKill tcpdump\nexecutable nc32\nadd cmake-vscode magic\n\n\n\nStupid lfs\n\nLFS fix\nChange project to use gcc 4.8.5 (from ubuntu 16.04) and its available features (C99 & C++11)\nSet architecture to be i586 as i686 support i586 instructions\nNew HAL, new happiness\nNew wifi_hal.h mgmt_frames_pipe cpp\nStub of MgmtPacket Add mgmt stream parser\nAdd Mgmt stream Dataprocess eats mgmt stream and pushes it to preassociation Mgmt packet has some adapters to radiotap\nCapfingerprinting is aware of MgmtPacket Wifi trainer and classifier are also aware of MgmtPacket\nMgmt packet json parser\nadd to_dpkt hack\nAdd mgmt to print stats\nparsing\nFix parsing\nUpdate unittests\nMac address for mgmt packet\ncleanup\nEdited mgmt_frames_pipe to send the packets to xb3 server\nAdded mgmt_frames_pipe_XB3 for now, might be deleted later when we decide on a better way to handle it. Updated xb_agent with some changes regarding to listening to the arm processor. Updated the deployment to xb3 to copy the mgmt_frames as well\nMinor deploy.yml change\nAdd another build of netcat for armv6b\nexe\nfix netcat build\nwrong file\nsmart xb_agent.sh\nupdate deploy for xb3 arm\ncommon x86\nfix if\nfix if\nfix whatami\nzeropad\nupdate station agent\nMerge fixes\nc++sify station agent update connections json to new format with interface\nFix scripts and deployment\nCorrect ap info\nCleanup\nAdd artifacts\nRevert changes from master\n.gitatrributes\ngitignore update\nEdited xb_agent.sh so it works on arm as well\nap_info to send iface_map as well\nmgmt frames uses smart queue for passing messages between threads and can print to stdout Compile with O1\nap agent oops fix error in capfp\nmgmt frames pipe detect server disconnection and reconnects to it automatically\nxb_agent cleanup upon ctrl-c\nstate chace has dummy value upon init\nSnr for XB*\nrecompile XB3 artifacts\nfix save last_network_type to display 2.4/5.0 properly in the UI\ndisrecard empty mac address\nFix ip getting problem, not getting it from the device_info\nRemove recover_ssh_tunnel\nRemove private key in xb_agent.sh\nnew xb tunnel machine\nFix IPs\nno localhost\nFix session loading\nremove iface_name\nnew key\nEmptyEvent in training/classification path\nXB3 arm: send arps every second to all connected devices\nUndo remove mac + interface tuple\nless sleep\nenable_ssh.sh script\nSave progress on reading from dmcli\nChanged static IP detection to be based on server insights: if no DHCP was captured after  seconds, assume static IP\nUse server indication in decision whether to update the IP\nIn accordance with last commit\nAdd ArpTable which monitors arp messages\nFix access\nAdd valid static indication to frontend\nARP ignore probe requests\nReverse arp table keys: take last IP address of every mac Update ip addresses from arp table changes\ncheck before make\ncheck for None\nOnly display IP if it is not None\nRemove updating the IP to None\n--output\nMore script fix\nshorten connections json length by removing the badge request\ndon't use same process id for ssh session\nmore addresses\ndeploy script\nAdd connections resolver which kicks stations that the server requests to kick\nDeploy with random path\ndeploy.sh: replace rand with unique\nFix execute\nquick ugly hack for moving between bands\nfix\nUpdate SSH config\nSparse json to report connections\nshort JSON format\nno message\nAuto build docker container\ndo not filter tcpdump 2. parallel arping\npreparation for XB6\nremove double decleration\nfirst step for auto cloud deployment\nserver IP as command line input\nwhoops\nwhoops2\nwhoops3\nwhoops 4\nenable ssh for xb6\nmove files\nremove libhals from git-lfS\nremove 2\nre-add files to git\nlibhal for XB6\nfix build scripts\nMove under version tree\ndeploy for XB6\ndeploy mgmt framses agent for XB6\nip by mac address\ndefault args + confirm\nAdd argparser from \nAdd mgmt_frames_pipe_XB6 artifact\n.gitatrributes are not required for non-gitlfs files\nIgnore pods in pods SSID\nSome code refactor connection report to ignore non-authenticated devices when in authenticated network\nCorrectly use the API\nNew artifacts\nSending is router reachable to frontend\nuse host instead of IP\nRemove comment in deploy Add reference to dev bucket\nUpdated binaries\ndeploy deploy.sh as well\nAdd new xb*.levl.tech servers\nRevert tcpdump filter\nSame timeout for both bands\nNow updating history db table with ap_info events to see if the router is alive\nRemoved unused debug print\nnew machines\nfix URL\nReplacing db saving with a simpler solution\nChange UI message\nChanged the keepalive indication from ap_info messages to connections report messages\nsmaller keepalive\nfingerbank timeout\njust connected changes\nInitial commit. Added ssid per device in connection report\nconnection report written/read from file + lengthen json\nFix (is using casting considered a fix?) to . appearing in json after each mac address\nUpdated binaries\nMore use of cpp types\nAdd dockerfile for mips and gcc/g++ wrappers\nremove detect_workspace.sh. doesn't work\nSome rearrengements\nUpdate cmake kits for vscode\nFile name usage\nUsing toolchain files to make life easier\nDon't cache everything\nPut linker flags in double quotes\nForgot to add compiler optimization\n\n"}
{"comment": {"body": "The holy grail of PRs", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/490/_/diff#comment-*********"}}
{"comment": {"body": "how the hell do we test this software now\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/490/_/diff#comment-*********"}}
{"comment": {"body": "Before merging, why don\u2019t we let QA run a \u201ccomcast\\_integration\u201d server with a \u201ccomcast\\_integration\u201d qualcomm AP for a couple of hours and see that everything\u2019s ok?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/490/_/diff#comment-*********"}}
{"title": "Async fingerbank queries", "number": 491, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/491", "body": "Each device_type_classify() call dispatches a thread and returns immediately. If that thread obtains a result from fingerbank, it pushes a UserDeviceTypeClassificationEvent event to data_processor directly.\nNo tracking of child threads state, it is assumed that they are short-lived and errors are handled inside them."}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/**********-image.png)\nI think I missed that when the comment was marked outdated.\n\nAnyway, it isn\u2019t an issue as there are currently no synchronous consumers to fingerbank results. The situation was already such that every fingerbank result was pushed to the event queue, and finally ended up in `add_display_info`, where the only device state info queried is unrelated \\(stuff such does the device have a model and whether it already has a vendor name\\).\n\nRegarding `just_connected`, it actually never uses fingerbank, it just uses Grisha\u2019s `DHCPFingerprintng` \\(vendor name extracted directly from packets\\).", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/491/_/diff#comment-*********"}}
{"comment": {"body": "Got it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/491/_/diff#comment-*********"}}
{"title": "Add cablelabs ap", "number": 492, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/492", "body": "Update configuration and certs for cablelabs AP.\n"}
{"title": "Feature/multiple ap sources", "number": 493, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/493", "body": "This work is for allowing mesh configuration, meaning that multiple ap infos and connection reports may come from different sources.\n\n\nEach connection source is identified by the triplet (interface name, bssid, ssid)\n\nFor ethernet, bssid is the interface mac address and ssid is empty\n\n\n\nThis should be enough to uniquely ID network interfaces, allowing to detach the network interface from the router\n\nThis also allows multiple APs on the same router\n\n\n\nDetect changes between interfaces from different router, not just from same router\n\nRogue AP agent is now broken\n\n"}
{"title": "Qca fw img build scripts", "number": 494, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/494", "body": "Updated binaries and scripts, created ipq_setup/fw for simpler 2-step fw build process\n"}
{"title": "Pod integration connection resolve", "number": 495, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/495", "body": "update mips toolchain to be based on musl\nConnection resolv executable for pod for kicking devices\n\n"}
{"comment": {"body": "Like for adding argparse", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/495/_/diff#comment-179291524"}}
{"comment": {"body": "a slow but persistent path to normal software", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/495/_/diff#comment-179291701"}}
{"title": "FIx kicking on pod", "number": 496, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/496", "body": "Update AP names\nSome argparser fixes\n\n"}
{"comment": {"body": "Can you check the runtime for that?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/496/_/diff#comment-179467047"}}
{"comment": {"body": "About 200ms for 1 device:\n\n\u200c\n\n```\nroot@xe1v2:/var/volatile/tmp# time ./connections_resolve_POD '{\"association_reso\r\nlve_0\": {\"24:18:1d:ee:d3:60\": true}, \"association_resolve_1\": {}}'\r\nKicked 24:18:1d:ee:d3:60\r\nConnections resolve completed\r\nreal    0m 0.18s\r\nuser    0m 0.03s\r\nsys     0m 0.03s\r\nroot@xe1v2:/var/volatile/tmp# time ./connections_resolve_POD '{\"association_reso\r\nlve_0\": {\"24:18:1d:ee:d3:60\": true}, \"association_resolve_1\": {}}'\r\nKicked 24:18:1d:ee:d3:60\r\nConnections resolve completed\r\nreal    0m 0.21s\r\nuser    0m 0.03s\r\nsys     0m 0.03s\r\nroot@xe1v2:/var/volatile/tmp# time ./connections_resolve_POD '{\"association_reso\r\nlve_0\": {\"24:18:1d:ee:d3:60\": true}, \"association_resolve_1\": {}}'\r\nKicked 24:18:1d:ee:d3:60\r\nConnections resolve completed\r\nreal    0m 0.23s\r\nuser    0m 0.02s\r\nsys     0m 0.01s\r\nroot@xe1v2:/var/volatile/tmp# time ./connections_resolve_POD '{\"association_reso\r\nlve_0\": {\"24:18:1d:ee:d3:60\": true}, \"association_resolve_1\": {}}'\r\nKicked 24:18:1d:ee:d3:60\r\nConnections resolve completed\r\nreal    0m 0.15s\r\nuser    0m 0.02s\r\nsys     0m 0.02s\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/496/_/diff#comment-179470327"}}
{"title": "Pod integration persistent agent", "number": 497, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/497", "body": "Use init.d to control the levl pod agent\nInstall pod agent to /usr/levl\nSmarter deploy script (with better selection of prod/dev env)\nStarts after boot thanks to /etc/init.d/manager which is plumes process manager\nThe agent waits until the appropriate interfaces are up\n\n"}
{"title": "Pod integration frontend indication", "number": 498, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/498", "body": "Added an indication of whether the pod is alive or not to the UI.\nThis PR also puts most of the implementation regarding to adding an indication of the connection source - if its a router or a pod. This part is not fully complete and will be merged as soon.\n"}
{"comment": {"body": "The offline pic should be without the pod symbol \\(as the device is offline and not connected to the pod :slight_smile: \\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/498/_/diff#comment-179557912"}}
{"title": "Block device removal in frontend when device is online", "number": 499, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/499", "body": "Oh, what a wonderful journey Ive had in the land called web!\n\nWe dont allow forgetting online devices and return bad request from the flask server.\nCurrently if the user clicks the forget button for an online device, she receives no feedback.\n\nThe feedback I wanted to provide was to disable the forget button while the user is online + add a tooltip with an explanation when hovering over the disabled button.\n  This is the result:\n\n4. On the way there, I had to deal with another minor issue: data was not propagated dynamically into the dialog box. Thats why we didnt display the wifi icon in the dialog box up till now (we were supposed to, according to the code). Also, if the device MAC (or other properties for that matter) would have been updated while the box is open, we wouldnt have seen that.\n\n\n"}
{"comment": {"body": "you know, PRs can\u2019t be approved until they pass all builds :stuck_out_tongue: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/499/_/diff#comment-179666195"}}
{"comment": {"body": "What\u2019s behind the choice of greying out the 2nd forget button \\(with the \u201cForget\u201d text\\) and not the 1st forget button \\(with the \u201cForget Device\u201d text\\)?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/499/_/diff#comment-179666632"}}
{"comment": {"body": "Right, doing both is probably best.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/499/_/diff#comment-179683540"}}
{"comment": {"body": "Ok, canceled tests. So passing all builds now :hugging:.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/499/_/diff#comment-179689222"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Updated PR.\n\n![](https://bitbucket.org/repo/8X5z9dk/images/2420317892-ezgif.com-video-to-gif%20%25281%2529.gif)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/499/_/diff#comment-179967785"}}
{"title": "Integrate radiotap classification", "number": 5, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/5", "body": "Separated trainers to ble and wifi\nImplemented wifi trainer\nImplemented wifi and ble classifier and trainer.\ncapfp: Added serialization functionallity\nFixed usage of WlanParametersClassifier\nFixed bugs in the training process\nFixed training and classification of the radiotap messages. Commented out periodic training since it does nothing ATM.\nFixed after merege\n\n"}
{"title": "recsys: Fixed UI bug when backend is reset", "number": 50, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/50", "body": ""}
{"title": "Pod integration frontend patch fix", "number": 500, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/500", "body": "Removed the patch that was used last night when our system was tested by Comcast. Now, the right connection image is displayed according to the connection type, band and connection source (pod / router)."}
{"title": "Nightly qca fw builds", "number": 501, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/501", "body": "A jenkins job that clones the current master, generates setup objects for each ipq6018 we have, connects to the ipq6018 build env on levlcompute and packs an image for each router. images are saved as Jenkins artifacts.\nMerged PR-494 into this one, there's an interdependence between them.\nTo trigger the build, go to ."}
{"comment": {"body": "what if somebody\u2019s working on levlcompute? I\u2019d let levlcompute turn itself off automatically", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/501/_/diff#comment-180141186"}}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/501/_/diff#comment-180141358"}}
{"comment": {"body": "This script is only intended to run as a part of this pipeline. In `start_remote.sh` we create a temp file flagging whether the server was running or not when we started. Only if it was down we shut it back down.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/501/_/diff#comment-180142251"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Thanks. Fixed argument brevity, use of private key files. Also, mich created a dedicated user on azure.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/501/_/diff#comment-180260781"}}
{"comment": {"body": "A suggestion: add git commit hash/git tag to artifacts? I see that there\u2019s no easy way in blue ocean to see which git commit is being built", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/501/_/diff#comment-180271828"}}
{"comment": {"body": "I was also thinking that might be a good idea. Added it now.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/501/_/diff#comment-180285358"}}
{"comment": {"body": "Can we approve this? Regardless of having Jenkins up and automatic builds, this contains some useful files that can be used manually. And it doesn\u2019t affect any existing functionality.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/501/_/diff#comment-189778827"}}
{"title": "Pod integration merge ap agent", "number": 502, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/502", "body": "Due to introduction of multiple AP scheme, each AP needs to send its AP tree with the connection report, instead of just a list of connected devices.\nAlready implemented on the xb3/pod, the connections report sends a list of interfaces with the devices attached to them.\nThis PR is to backport this behavior to the ap_agent, so the ap_agent would send similar report to the cloud.\nI wish to do this is several steps, as the changes are everywhere, and also wish to consolidate ap_info report since its redundant at this time. And remove some dead code while at it.\n\nThis was somewhat tested by me. This can be merged into pod integration, as it doesnt affect the pod behavior at this time."}
{"comment": {"body": "Master is merged to pod\\_integration so reviewing this PR should be easier now", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/502/_/diff#comment-180494854"}}
{"title": "Accidentally added the static IP to be taken as a weighted parameter", "number": 503, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/503", "body": "when comparing session to devices (just_connected). This allows a user dependant manipulation of the data. We added this trying to solve IP display issues, and overlooked the security implications. Now that Grisha added the continuous ARP-based IP resolve and in any case we are updating the device record in DB if it doesn't match the session's IP, there's actually even no need to update the device_ipv4 parameter with static IPs."}
{"title": "re-enable fignerprinting", "number": 504, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/504", "body": ""}
{"title": "Master", "number": 505, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/505", "body": "Regular maintenance: pull master changes\n\n\nWhen we find only one matching device based on coarse ID, we allow it to be chosen even when there are no additional intersecting parameters, but only in the case where at least one valid value in the session parameters was never set in the device record.\nBetter naming\nRedundant condition\nUpdate configuration and certs for cablelabs AP\nEach device_type_classify() call dispatches a thread and returns immediately. If that thread obtains a result from fingerbank, it pushes a UserDeviceTypeClassificationEvent event to data_processor.\nHandling device type classifier in session caching now that it dispatches threads\nHandling the case of non-initialized device type classifier\nFixed udp4\nUpdated POST request timeout + avoided knowing what are the incoming events_queue and an event in FingerbankClassifier\nFixed use of code from FingerbankRunner id DataProcessor\nFix typing\nremote configuration\nMerged from master, updated eth1 as br-demo port on pilot routers, added config artifacts\nproxyjump\nrevert proxyjump\nInitial commit. Block user from forgetting device when it is offline\nFixed data flow, disabled button and added tooltip\nFixed error\nBlocking the forget button under device-info also\nFix unquarantine box size\nFix device_prev_mac_addr_wireless_store being written None instead of dict()\nfix mix-up between rb and rb_remote ssh aliases\nCritical fix for session caching, accidentally saving fingerbank's resolve_callback\nFixed use of session_attr_dict\nHandle priority comparison for device with empty matchin attrs list (pardon for direct push)\nAccidentally added the static IP to be taken as a weighted parameter when comparing session to devices (just_connected). This allows a user dependant manipulation of the data. We added this trying to solve IP display issues, and overlooked the security implications. Now that Grisha added the continuous ARP-based IP resolve and in any case we are updating the device record in DB if it doesn't match the session's IP, there's actually even no need to update the device_ipv4 parameter with static IPs.\n\n"}
{"title": "Remove pod indication from UI if there isn't a pod", "number": 506, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/506", "body": "This PR purpose is to unify the UI before merging back tom the master. \nWhen working with the pod, the UI presents a useful indication whether the pod is alive or not. We dont need this indication if we are working on our office routers (or any other router), so it should not appear in such cases."}
{"title": "Pod integration", "number": 507, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/507", "body": "Ho, ho, ho\nIts big merge time"}
{"comment": {"body": "Christmas came early this year", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/507/_/diff#comment-180707562"}}
{"comment": {"body": "David have tested it for two hours and gave the thumbs up :thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/507/_/diff#comment-180801270"}}
{"comment": {"body": "Awesomo", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/507/_/diff#comment-180801539"}}
{"title": "No more ap info + Reenable RogueApDetector", "number": 508, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/508", "body": "Ap Tree now comes fully from the connections endpoint. No need for ap_info endpoint\n\nWe store a list of APs, each with a list of network interfaces, each with a list of devices\nThis means that there are slow queries to check, for example, which device is in which AP. Instead of O(1), Its O(nk), but its fast enough\n\n\n\nRemove APInfo http API\n\n\n~~Reenable RogueAp module~~\n\n~~but disable RogueAP module if pod is detected (because there's no support for mesh right now)~~\n\n\n\nFix API calls, but still disable MITM and rogue AP detection\n\nWill be fixed in a different PR\n\n\n\nUpdate XB3 & pod code for this.\n\n\nI have yet to test this enough. You can still review the changes"}
{"comment": {"body": "Like", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/508/_/diff#comment-180931422"}}
{"comment": {"body": "Maybe not couple the mac-ip pair with the phrase \u201cdhcp report\u201d \\(also in `class DhcpReport` above\\)? This could potenitally be a dataclass instance we get from, say, an ARP table\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/508/_/diff#comment-181398487"}}
{"comment": {"body": "I get what you\u2019re saying. However, this factory method does depend on knowing that it\u2019s DHCP since it\u2019s setting `dynamic_ip=True`.\n\nWe could always add more factory method if we need them", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/508/_/diff#comment-181398698"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} got it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/508/_/diff#comment-181398773"}}
{"comment": {"body": "Some of the methods in this file are uncalled \\(such as `report_ap_info_update`\\), Do want to keep them or delete?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/508/_/diff#comment-181488678"}}
{"comment": {"body": "I missed that. I\u2019ll delete those functions", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/508/_/diff#comment-181494423"}}
{"comment": {"body": "Thanks. Removed", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/508/_/diff#comment-181500945"}}
{"title": "major algorithmic changes in the motion and presence detect", "number": 509, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/509", "body": ""}
{"comment": {"body": "Do not review \u201c[workspace/motion\\_detection/presence\\_detect\\_debug.py](https://bitbucket.org/levl/comcast/pull-requests/509/major-algorithmic-changes-in-the-motion/diff#chg-workspace/motion_detection/presence_detect_debug.py)\u201d; this file is merely intended for offline debugging.  \nI kept some comments for quick future modifications, if necessary. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/509/_/diff#comment-180969909"}}
{"comment": {"body": "Any effect on run time now that you added kmeans?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/509/_/diff#comment-181004514"}}
{"comment": {"body": "Not in this form; the settings were carefully set to minimum", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/509/_/diff#comment-181007701"}}
{"title": "Fixed UI in the recsys to show disconnected clients correctly", "number": 51, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/51", "body": "\n"}
{"comment": {"body": "Most code was just moved to another function", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/51/_/diff#comment-146489258"}}
{"title": "Revert local IP configuration for remote APs", "number": 510, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/510", "body": "We should keep the \"ip address\" field in the 10.0.0.x subnet to allow future local setup. This field translates to the br-lan IP address, and in the current state this causes br-lan and tun0 to have the same IP. Only the \"domain\" field should be altered for remote APs - the address listed in \"domain\" is used to ssh the AP."}
{"title": "Fix bug CCP-370", "number": 511, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/511", "body": "Parameters missing in update_device_identifier_params causing collected information on device to not be updated.\n"}
{"title": "Comast integration with dual pre cache sterile", "number": 512, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/512", "body": "Add preassociation cache swapping mechanism per device\n\nThanks @{5dbeb866c424110de52552cc}\n\n\n\nAdd stale cache deletion\n\nBecause I noticed that there are A LOT of fake mac address with 1 flipped bit from real mac address so there are ~47 (there are 48 bits in the mac address) additional fake caches for every connected device\nIf you disagree with this, let me know\n\n\n\nSolves issues:\n\n\n\n\n\nMany more\n\n"}
{"comment": {"body": "Maybe someone else should also voice his opinion on the stale caches mechanism, I think it\u2019s a-ok.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/512/_/diff#comment-182745363"}}
{"comment": {"body": "This branch was tested yesterday quite thoroughly by QA and no new open bugs reproducing the solved issues.\n\nI need another approval before I merge.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/512/_/diff#comment-182976170"}}
{"title": "Allow concurrent operations", "number": 513, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/513", "body": "Allow limitless concurrent onboarding/classification operations in zero touch mode (\"home\"). These processes are only secondary to the primary session-device linking process in just_connected anyway, so were fine with potentially sacrificing some CFR data.\nShorten onboarding/authorizing times for better UX. We still want to collect reasonable amount of data, so only shortened by 25% or 50% (training and classification accordingly) .\n\n"}
{"comment": {"body": "~~max\\_num\\_devices\\_to\\_prioritize can\u2019t have None value..~~\n\nSorry, didn\u2019t see that `None` makes slicing return all values", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/513/_/diff#comment-182859150"}}
{"title": "Restore session restore", "number": 514, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/514", "body": "Returned session restore, complies with new connection map stored in management_endpoint.\nThis change should solve this."}
{"title": "Alerts - send email on router/server down", "number": 515, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/515", "body": "Added infrastructure for monitoring using TICK stack (telegraf, influxdb and kapacitor) + Grafana\nAdded 3 alerts for server, ap status and disk full monitoring\n\n"}
{"comment": {"body": "what about containerizing these service? e.g. `docker-compose` them", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/515/_/diff#comment-182975375"}}
{"comment": {"body": "Good point:\n1. telegraf is monitoring the system host, thus preferred to run on the host\n2. Monitoring server is an external server that collects the data and\npresents, thus at this stage not containerized, but it can be an option to\ndeploy on other servers as well for future.\n\n", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/515/_/diff#comment-182976425"}}
{"comment": {"body": "@{5f82bf320756940075db755e} I see that telegraph can monitor the host from the container: [https://community.influxdata.com/t/how-can-we-collect-host-machine-metrics-while-telegraf-is-running-in-docker-container/12005](https://community.influxdata.com/t/how-can-we-collect-host-machine-metrics-while-telegraf-is-running-in-docker-container/12005)\n\nSomething to think about", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/515/_/diff#comment-183050814"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} i mentioned that it is preferred to monitor directly from the host ;\\):\n\n1. notice how the configuration becomes difficult for the container \n2. Not all plugins will work\n3. Think of microservices environment with multiple containers on the machine\\)..\\\\\n\nLet\u2019s brainstorm together tomorrow on the pros and cons..\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/515/_/diff#comment-183135784"}}
{"title": "Fix quarantine path - do not go through AWAITING_REASSOCICATION for offline devices", "number": 516, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/516", "body": ""}
{"title": "Disable matching of devices without intersecting attributes for non-static IPs", "number": 517, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/517", "body": "This is the root cause of most duplicate model and capabilities mismatch that we have experience in the past two weeks.\nThe slim coarse ID concept was introduced here:\n"}
{"title": "bugfix/error on log writing when operation done", "number": 518, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/518", "body": "Updated db event writing on classification ending to use cached session data and thus avoid an error in writing session info in case the operation finished while the device was disconnected. \n\n"}
{"comment": {"body": "are you sure you want to break the single-connection-to-DB optimization?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/518/_/diff#comment-183038688"}}
{"comment": {"body": "you should deepcopy this since the references can be invalidated later", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/518/_/diff#comment-183038806"}}
{"comment": {"body": "No, I thought we spend too much time in this lock and it might be inefficient. If that\u2019s not the case I\u2019ll return to one connection.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/518/_/diff#comment-183043803"}}
{"comment": {"body": "Sure? Where can they be invalidated?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/518/_/diff#comment-183043905"}}
{"comment": {"body": "Just to clarify, some fields in these objects can be invalidated but we don\u2019t need those fields. For the `Session` and `DeviceRecord` objects themselves and their required fields, a reference should suffice...", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/518/_/diff#comment-183044512"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Fair enough", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/518/_/diff#comment-183045988"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} It happens once per training, so the optimization is not critical", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/518/_/diff#comment-183046064"}}
{"title": "Add main loop statistics about handling events", "number": 519, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519", "body": "This will give us some insights about the load in the main loop.\nWith 4 devices connected, I see that the load is ~13%-18% of CPU time"}
{"comment": {"body": "good idea to wrap in a function", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183071597"}}
{"comment": {"body": "not preferred to add the time stats on the event itself?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183071873"}}
{"comment": {"body": "How is the load calculated?  \ntotal\\_time / actual\\_time?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183072200"}}
{"comment": {"body": "total\\_time / 10 \\(as the statistics are checked every ~10 seconds\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183072639"}}
{"comment": {"body": "What would be the benefit? This `start_time` is just main loop arrival time", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183072995"}}
{"comment": {"body": "1. can we add also the count of the events in Q versus total events handled at this period of stats\n2. do we see here stats of the events inside the Q and how much they are waiting?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183073040"}}
{"comment": {"body": "1. All events are handled, otherwise we would have a `backlog` message or `Total miliseconds in the last 10 seconds` close to 10000 \\(10 seconds\\). Or I don\u2019t understand what you\u2019re asking.\n2. No, I didn\u2019t add the waiting time in the queue\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183074140"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} \n\n1\\. When the stats are printed there are events in the Q, so we can measure in progress of time events arriving and total events handled in last 10 sec \\(this can show the pattern of Q growing, peaks and etc..\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183094937"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} you can measure per event for the whole flow stats..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183095853"}}
{"comment": {"body": "@{5f82bf320756940075db755e} That\u2019s something I raised that would be difficult because the main loop forwards most events to external threads/processes. So it\u2019s very difficult measuring the lifetime of some packet.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183098822"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Sure, I can do this", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183100369"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Added in 6f5f3b9afb33c6f125c010d4694a2a7e9e4457de", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/519/_/diff#comment-183107669"}}
{"title": "run_server.sh was accidentally always detached, added flag so tests can choose", "number": 52, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/52", "body": ""}
{"comment": {"body": "Reasonable, but why not always run detached?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/52/_/diff#comment-146542973"}}
{"comment": {"body": "When you\u2019re testing the server locally, you run it with `./run_server.sh` and you want to see the logs and kill it with CTRL\\+C. When it\u2019s detached it\u2019s like \u201cStarting containers\u2026 started\u201d and you\u2019re back into your shell without any logs", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/52/_/diff#comment-146568147"}}
{"title": "Flag disable to static IPs support", "number": 520, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/520", "body": ""}
{"title": "Feature/more events spam", "number": 521, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/521", "body": "Changelog:\n\nSeparate DataEvent statistics (to CFR, radiotap, ethernet)\nDon't push if you're disabled, silly\n\nWith 10 devices, the loop is utilizing 50-60% runtime at idle (all are online).\nAfter the 2nd change, it reduced to 42% runtime\nBut I see spikes reaching 90% (which means queue is not emptying) when a new device connects  with 5 devices already connected.\nStill, the per-event-runtime is increasing with the number of devices in the system (not necessarily online). Meaning that we tend to iterate on the sessions dictionary"}
{"title": "Separate unambiguous unique identifiers from looser identifiers", "number": 522, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/522", "body": "Added dhcpv6duid as top unique device identifier - if it matches then this must be the device. And a little more ordered code in parts of get_device_from_db_by_coarse_identifiers\nShorten discovery time for wired connections that will never get caps\n\nThe first bullet aims at reducing the probability of an issue like VHT caps mismatch occurring, resulting in duplicate device. We should still exhaust the root cause of the VHT caps mismtach issues, so this will be tracked here."}
{"comment": {"body": "Great refactor. I see the same pattern here in the `filter_*` functions. They can be distinguished by `next` or `list` when operating on the same generator/filter. Mind joining these logics?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/522/_/diff#comment-183295227"}}
{"comment": {"body": "mind deleting the `device_dhcpv6_duid` priority param here? It shouldn't be used anymore.\nThat is, if you `return` once you find using the unique identifier", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/522/_/diff#comment-183296270"}}
{"comment": {"body": "Shouldn\u2019t delete it. There\u2019s a delicate scenario where we are in the domain of android 10 hacks: We don\u2019t want to rely on it as a unique identifier, but we do want it to give a little edge \\(as an \u201cadditional parameter\u201d\\) to devices having a matching attribute.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/522/_/diff#comment-183297746"}}
{"comment": {"body": "OH GOD NO NONO\nNO MORE HACKS", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/522/_/diff#comment-183304045"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Only conserving existing ones", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/522/_/diff#comment-183308070"}}
{"comment": {"body": "Joined some logic but kept the outer loop.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/522/_/diff#comment-183483957"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Makes sense. Great work :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/522/_/diff#comment-183484132"}}
{"title": "Mon 808673795 : Remove blocking events from the main loop - stop processes should never be called from the main loop", "number": 523, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/523", "body": ""}
{"title": "Feature/profiling results", "number": 524, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/524", "body": "Turn off fingerprinting feature:\n\nCFR slopes since its very heavy\nVDim because it uses scapy\nTwoWaySNR because it uses scapy\n\n\n\nLess DB connections in flask (same process as the main loop)\n\n\nMotion process is now non-blocking\n\nPrints rate of sending packets to motion processes\n\n\n\nFix bad indexes in motion processing (its expecting 2 antennas but hawkeye has 4 antennas)\n\nAdd scapy/dpkt caching to Radiotap/Ethernet packet\n\n"}
{"comment": {"body": "Is this relevant to routers with more than 2 antennas, American Hawk?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/524/_/diff#comment-183537797"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/524/_/diff#comment-183538262"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Great, let\u2019s open task to support more that 2 antennas \\(not for cableslabs\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/524/_/diff#comment-183544137"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Sure. Opened https://levltech.monday.com/boards/745146510/pulses/811461386\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/524/_/diff#comment-183545357"}}
{"title": "Mon 808673795 : Remove blocking events from the main loop - stop processes should never be called from the main loop", "number": 525, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/525", "body": "Support tracing to offload thread"}
{"comment": {"body": "Regarding the `tracer_queue` being global, consider maybe initializing a sort of a `TraceBroker`class that inherits from `threading.Thread` and creates this queue or receives it from outside \\(instead if just having `handle_trace_writes()` as target function, it would become the `run()` function\\).", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/525/_/diff#comment-183976124"}}
{"comment": {"body": "Any thoughts on this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/525/_/diff#comment-183990073"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Do you want to update motion detector traces as well, for completeness?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/525/_/diff#comment-183990084"}}
{"comment": {"body": "You can use this opportunity to use python dataclass [https://docs.python.org/3/library/dataclasses.html](https://docs.python.org/3/library/dataclasses.html)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/525/_/diff#comment-183990495"}}
{"title": "Feature/downloadble history", "number": 526, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/526", "body": "Added download history button to dashboard:\n\n\nAdd /reports/history_file GET method that sends CSV\nExample downloaded history is attached\n\n\n\nAdded new field to history: network_type\n\n\n"}
{"comment": {"body": "So just a few changes I would like to make in the format:\n\n1. I want it to be a list of events - connection and disconnection events - for each row it will specify wether it is a connection event or a disconnection event. Only disconnection events will have a session length information.\n2. The device name/label, as well as the device type, should be part of the data we return for each device.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/526/_/diff#comment-184052084"}}
{"comment": {"body": "Let\u2019s create folder statistics and store the file there as well \\(similar to traces and reports directory for pickle and log files\\), we already writing the file..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/526/_/diff#comment-184052411"}}
{"comment": {"body": "It\u2019s currently written in memory for the reason of releasing this memory after downloading it. What\u2019s the reason for saving the downloaded history? We have the data in the DB", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/526/_/diff#comment-184055709"}}
{"comment": {"body": "Modified in 451ec3280207ae81fc5346e302b906a72ee548da", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/526/_/diff#comment-184078686"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Added saving files in 1f5fb91683a3f64b723247c2ab9902754d64f47e", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/526/_/diff#comment-184300835"}}
{"title": "Admin assigned device name", "number": 527, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/527", "body": "Added option to update device name from dashboard. The device name we learn independently is still the only one used under the hood, the new one is used for display (devices table and history events) if it is not empty."}
{"comment": {"body": "Maybe also log these `500` and `400` errors?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/527/_/diff#comment-184106111"}}
{"comment": {"body": "Done in `8ea31f4325a029828f5b2e539e18daaf43aa3321`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/527/_/diff#comment-184107191"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} More data would be nice in the logs.. Like `name` and `identifier` variables", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/527/_/diff#comment-184304509"}}
{"title": "Download history minor changes", "number": 528, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/528", "body": "Event Timestamp->Timestamp\nDownload history->Export log\n\n"}
{"title": "Mon 793868661: Update device details on onboarding - LEVL ID and hostname", "number": 529, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/529", "body": "While other device properties appear immediately as they are resolved, the Levl ID and Name (based on hostname) fields wait for CLASSIFICATION_SUCCESS state, each for its own reason. But these are not good reasons, so this was fixed. Set the device id sooner (actual use of it is the same as before), and overriding None \"nonrandom_device_name\" that is being set in the device if hostname is available."}
{"title": "Fix TIMEOUT_SECS bug", "number": 53, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/53", "body": ""}
{"title": "Recsys new merge", "number": 530, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/530", "body": "All of the recsys_new, now with master compatibility\n\nThe recsys_new was supposed to bring RCC CFR capabilities\n\nI dont really know what this is, but radiotaps cant be captured at the same time.\n\n\n\nAdd higher BF rate that works behind monitor_control (former cfr_control) in parallel with the cfr control\n\n\nsetup.sh:\n\nHawkeye: now copying python as well since the firmware doesnt contain it\nAdded persistent SSH session so that setup.sh works MUCH FASTER\n\n\n\nAdd beamforming SU (single user) and MU (multiuser) rate display:\n\n\n\n\n"}
{"title": "Bugfix/CCP-387 duplicate models created for galaxy s20", "number": 531, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/531", "body": "Simpler device filtering also solves the issue of ignoring all devices if any solid parameter is None. \n"}
{"title": "Add API for querying all devices / device details/ device history / remove device", "number": 532, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/532", "body": "Monday 808670783 : Add API for querying all devices / device details/ device history / remove device\nMonday 808670783 : Add API for querying all devices / device details/ device history / remove device\n\n"}
{"comment": {"body": "ignore this file for now ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/532/_/diff#comment-186011298"}}
{"title": "Mon 835196281: Dropped association requests under traffic stress tests", "number": 533, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/533", "body": "To solve radiotap monitoring drops between AP and server when AP is under traffic stress tests (IxChariot), filtering only association packets. This hurts features like rogue AP detection and SNR tracking (\"signal quality indication\"). For now, signal quality is being done instead by reporting the device's RSSI according to the AP software. The more durable solution should be to keep 2 tcpdump radiotap streams: A prioritized stream that filters only association requests, and a less prioritized stream with the rest of the radiotap packets (some might drop, but that's tolerable). We also need some kind of stable traffic stress test in-house to further investigate the causes and allow for steady stress tests."}
{"comment": {"body": "The SNR display is kinda dead right now \\(see `get_frontend_signal_strength` in `management_flask.py`\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/533/_/diff#comment-185877055"}}
{"title": "Feature/add connection mode to dashboard", "number": 534, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/534", "body": "Get station 802.11 standard and bandwidth from the router\nParse it and display a text on the dashboard\n\nExample display:\n\n"}
{"comment": {"body": "Looks good, yet I do have a request - can you make the font a little bit smaller? \\(like 0.75-0.8 of the current\\) - it draws too much attention. Plus, please make it lowercase \\(802.11ac, 802.11n, etc.\\) as this is the standard writing :slight_smile:\n\nThanks!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/534/_/diff#comment-185937332"}}
{"comment": {"body": "\\+1", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/534/_/diff#comment-185951201"}}
{"comment": {"body": "what about now?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/534/_/diff#comment-185957585"}}
{"comment": {"body": "Looks great, just add the 802.3 as discussed :smiley: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/534/_/diff#comment-185983088"}}
{"comment": {"body": "It\u2019s there \n\n\u200c\n\n![](https://bitbucket.org/repo/8X5z9dk/images/2822903724-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/534/_/diff#comment-185983305"}}
{"comment": {"body": "Awesome, thanks!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/534/_/diff#comment-185983451"}}
{"title": "triggering api", "number": 535, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/535", "body": "Add executable to periodic cron file\nEmail with attachment\n\n"}
{"comment": {"body": "Should we log all that?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/535/_/diff#comment-186258494"}}
{"title": "CCP-392 : API - Triggering - wrong message for invalid input", "number": 536, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/536", "body": ""}
{"title": "Restrict packets polling from dhcp and dhcpv6 cache to post association packets", "number": 537, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/537", "body": "Labeling capture timestamps for Ethernet and Radiotap packets. This is used to later filter only post-association-request packets when querying the DHCP and DHCPv6 caches.\nThis aims to solve issues of cache misses (using packets of wrong device) when multiple devices with the same MAC are alternatively connected to the system.\n\n\nAnd also Tims dual boot PC that got quarantined.\n\n"}
{"comment": {"body": "How are we handling the wired use case when we don\u2019t have association packets?   \nIsn\u2019t it going to break it?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/537/_/diff#comment-186595854"}}
{"comment": {"body": "It will break, I will change it to use latest DHCP packets if there is no association packet.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/537/_/diff#comment-186641887"}}
{"comment": {"body": "can you assume that the last association packet is the most recent one? This will save the `for` loop complexity", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/537/_/diff#comment-186647470"}}
{"comment": {"body": "I doesn\u2019t loop over aggregated packets, only over packet types \\(reassociation, association\\). The cache holds only 1 packet \\(at most\\) per type. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/537/_/diff#comment-186651192"}}
{"comment": {"body": "Done in `f25c56a6e80bfa677e56e76273c92d6089bd3d6e`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/537/_/diff#comment-186654075"}}
{"comment": {"body": "Many like for dataclass usage", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/537/_/diff#comment-186655066"}}
{"comment": {"body": "Oh lordy, the spam on the console since we get ~9 packets per second :slight_smile:", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/537/_/diff#comment-186656828"}}
{"title": "Mich api cosmetic changes", "number": 538, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/538", "body": "Rename api endpoint from triggering to alerts.\nOnboarding event has a unique name\nRemove devices from session that are not alive (cannot remove device bug)\nincrease LEVL-ID to 40 bits\nDisplay also removed devices in history\nEdited a bit the wording around sent emails\n\n"}
{"comment": {"body": "no need to pass the duration, we have that information here after reading the config file", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/538/_/diff#comment-186637415"}}
{"comment": {"body": "Have you updated the api doc accordingly?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/538/_/diff#comment-186637537"}}
{"comment": {"body": "Fixed.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/538/_/diff#comment-186643518"}}
{"title": "Feature/CCP-393 logfile and timestamps", "number": 539, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/539", "body": "Format timestamps in LA time zone in history file\n\nExample log is attached"}
{"comment": {"body": "Great catch. How far fetched is it to hold this in a single file that both `state.service.ts` and `management_flask.py` read from?\n\nOr possibly, for the timezone to be sent as part of the alarm/log file request?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/539/_/diff#comment-186720754"}}
{"comment": {"body": "I don\u2019t think that the latter would be useful since it makes sense to be bound with `state.service.ts`.\n\nI\u2019ll work on the first request.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/539/_/diff#comment-186738734"}}
{"title": "Fix annoying sync exception", "number": 54, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/54", "body": ""}
{"title": "Faster SSH in setup.sh and a UI tweak", "number": 540, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/540", "body": "Initializing and reusing a control socket for SSH to speed up on consecutive commands. Very useful now that we work almost exclusively over the cwqa VPN. Updated the levl ID column width to match the 10 characters length."}
{"title": "motion graph commit for integration - NOT FOR REVIEW", "number": 541, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/541", "body": ""}
{"title": "Highlight changes in UI", "number": 542, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/542", "body": "Added a 3 sec cell background highlight for changes in levl ID, device name, IP, MAC and status columns."}
{"comment": {"body": "maybe merge the master into this branch before adding you code? I can\u2019t identify your changes.\n\nAlso, how about a demonstration of the new feature?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/542/_/diff#comment-188039447"}}
{"title": "Highlight changes (cablelabs branch)", "number": 543, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/543", "body": "Add a 3 sec text glow highlight for changes in levl ID, device name and MAC columns.\n\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/3175290824-image.png)\n@{5b41d9de10d57114135eca66} , moved to new PR to make changes readable.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/543/_/diff#comment-188044001"}}
{"comment": {"body": "Can you make it also highlight the status and the IP column?  \nCan you make it 10 seconds instead of 3?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/543/_/diff#comment-188062901"}}
{"comment": {"body": "Done. 5 seconds.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/543/_/diff#comment-188072603"}}
{"title": "Enlarge default DHCP pool", "number": 544, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/544", "body": "Changed br-demo subnet and dhcp leases quota to avoid MAC-changing script troubles when reaching lease limit."}
{"title": "Feature/cfr test app update for fw11.2", "number": 545, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/545", "body": "Check both paths for backward compatibility\nExecute perms\n\n"}
{"comment": {"body": "Yeah it\u2019ll do the trick for now", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/545/_/diff#comment-188052478"}}
{"title": "bugfix/CCP-395-s8-quarantined", "number": 546, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/546", "body": "Iterated on reference to manipulated list, not on copy. List was probably corrupted in some cases, and this caused problems. Added a log to get a full view of the filtering process, shouldnt be too redundant.\n"}
{"title": "Merge cablelabs fixes and features to master", "number": 547, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/547", "body": "Periodic merge to master of changes we wanted to include in cablelabs releases, and are well-tested by now. \n** All of these changes have been previously approved, this PR is just for the merge to master to go through you guys.\nChangelog:\n\nSkipping on devices when linking session with devices \nNot removing devices with different unique identifiers \nIncrease DHCP pool size\nFrontend: Highlight columns on changes.\nStats update - remove users, rogue.\nFix for zeroed onboarding stats - \nLine up with cablelabs state and enable static IPs.\nMore patient arping on wired discovery.\n\n"}
{"title": "Devices with actual unique identifiers values should be removed from filtering list if not matching.", "number": 548, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/548", "body": "This caused the original issue in ."}
{"comment": {"body": "Do we verify that both values are not None?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/548/_/diff#comment-188129838"}}
{"comment": {"body": "Yes, inside `iterate_devices_params`. We enter the loop in the first place only if the session value is not None and we iterate over a device if its value is not None.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/548/_/diff#comment-188130596"}}
{"title": "Feature/improve subscription to recording system", "number": 549, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549", "body": "Ive refactored the subscriber to automagically open processing processes for every connected device and have 1 GUI process in total. In this manner, I hope I made the code easier to use.\n\nFor that, I added a socket to the recording system to output connected devices list. This is starting to get awfully similar to the production system.\nAnd improved subscriber connection/disconnection error handling\n\n\n\nTheres a basic usage example is derived on the CFR motion sensing\n\nYet, the CFR motion sensing is not capable of handling all of the data on my 4 core CPU. Nitzans 8 core CPU is capable.\n\n\n\n\n"}
{"comment": {"body": "Why not a a higher rate? Should it be rate-limited at all?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189547407"}}
{"comment": {"body": "Should this exist?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189547886"}}
{"comment": {"body": "it\u2019s a good example for subscriber usage.\n\nI don\u2019t think that it\u2019s worth deleting it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189621168"}}
{"comment": {"body": "it\u2019s up to the user \\(researcher\\) to decide on rate", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189621324"}}
{"comment": {"body": "BUMP", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189886182"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} We seem to bind together 2 unrelated things here. That print every 100 packets is a runtime indication that we probably want to see whether pilot or recsys. Separately, it makes sense that researcher\u2019s logic \\(including rate limiting or cherry picking\\) will be contained in the subscriber callback and that the callback will get all of the data. What do you think?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189911537"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} It\u2019s really up to the user whether he/she wants to see the logs and/or display values in the GUI.\n\nIMO, it\u2019s better to not create 2 places that limit rate. Then the logs and the GUI would go out of sync.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189912769"}}
{"comment": {"body": "Can you explain that paradigm you used here a couple of times, of setting the `__start_event.wait()` and then `__start_event.set()` on `run`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189938294"}}
{"comment": {"body": "It takes time \\(even several seconds\\) to start a process, so it\u2019s a syncing pattern - waiting until the process is actually started", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189938878"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} How is it used?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189939623"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} In this case, whoever is creating a `GuiProcess` instance, is blocking until the gui process is live. We don\u2019t want to feed it with data until it\u2019s ready.\n\nIn other case, you\u2019ll see a similar pattern:\n\n1. start\n2. wait for signal\n3. <<meanwhile in the new process>> signal that the process is running\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189940375"}}
{"comment": {"body": "Why are gui processes separated from data handler processes? Isn\u2019t a gui process supposed to be just another type of a data handler process? Or from a different point of view, why should a \u201csubscriber manager\u201d be aware of \u201cgui\u201d?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189945925"}}
{"comment": {"body": "you\u2019re right that GUI process has a similar pattern to data handler, but there is an inherit difference that there\u2019s only one GUI process and a dynamic number of data handlers. So it enforces this pattern. \n\nThere\u2019s no need for another layer of abstraction, since we\u2019ll never finish abstracting.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189947168"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} I don\u2019t see that `__start_event` is being checked anywhere, what am I missing?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189965919"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} line 22\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189966088"}}
{"comment": {"body": "Great work! Sorry for the discontinuous review.  \nI may still not agree on [https://bitbucket.org/levl/comcast/pull-requests/549#comment-189912769](https://bitbucket.org/levl/comcast/pull-requests/549#comment-189912769){: data-inline-card='' }  and [https://bitbucket.org/levl/comcast/pull-requests/549#comment-189947168](https://bitbucket.org/levl/comcast/pull-requests/549#comment-189947168){: data-inline-card='' }, but these are minuscule compared to the entire PR and the fact that it should be merged.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/549/_/diff#comment-189973952"}}
{"title": "Added script to fetch recording IDs from the specifed date", "number": 55, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/55", "body": ""}
{"comment": {"body": "What\u2019s the incentive behind this?\n\nDatastudio might be more suitable for your use case since it\u2019s script-independent.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/55/_/diff#comment-146683435"}}
{"comment": {"body": "David asked me too many times to fetch this for him.\n\nDon\u2019t know much about data studio. Doesn\u2019t it re-process the DB once a day?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/55/_/diff#comment-146893255"}}
{"comment": {"body": "Something like that. @{5cbc1fb4fdcd39100fda97de} knows lots more about it.  \nIt\u2019s a great tool to view information without running scripts.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/55/_/diff#comment-146986292"}}
{"comment": {"body": "I think it\u2019s nice to have, but it\u2019s always \u201cI just finished a recording, what its ID?\u201d\n\nI can\u2019t tell him \u201cwait for midnight\u201d", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/55/_/diff#comment-146986586"}}
{"comment": {"body": "There\u2019s not much difference between running this script and running the query you\u2019re running here through BigQuery and entering the date. Unless David has a problem with the latter, I think it\u2019s simply easier to pass him this query to run whenever he needs it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/55/_/diff#comment-146986853"}}
{"comment": {"body": "Sure, but then it has to go through me", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/55/_/diff#comment-146987036"}}
{"comment": {"body": "I don\u2019t see why, I\u2019ll pass this query to David and explain him how to do it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/55/_/diff#comment-146987452"}}
{"title": "Never-satisfied-dummy-model for fixed training/classification durations", "number": 550, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/550", "body": "Added back the dummy model. Now it always returns NEED_MORE_DATA such that if it's on, we will always get unified training and classification times, according to timeouts (now they are 30 and 5 seconds, accordingly). Turn feature on and off using the USE_MOCK_MODEL flag.\n"}
{"title": "Feature/CCP-400 fix ignoring online devices", "number": 551, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/551", "body": "Change comparing between apples and apples instead of apples and device records\n\nFixes this bug: \n"}
{"title": "Merge cablelabs fixes and features to master v2", "number": 552, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/552", "body": "Periodic merge to master of changes we wanted to include in cablelabs releases, and are well-tested by now. \n** All of these changes have been previously approved, this PR is just for the merge to master to go through you guys.\nChangelog:\n\n Fix not discarding online device when associating newly connected device\n\n"}
{"title": "More efficient device filtering", "number": 553, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/553", "body": "Following last fix, basing all of the device filtering process on hash tables rather than on lists. It was too unsettling to leave those wasteful processes, although this isnt really meaningful perf-wise.\n"}
{"comment": {"body": "while you\u2019re at it, you could make the `DeviceRecord`'s hash based on the `device_id` and then this `dict` would be redundant as you could search in `devices_set` easily. That is assuming that there are not 2 `DeviceRecord`s with the same `device_id`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/553/_/diff#comment-188402029"}}
{"comment": {"body": "Correct, will do.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/553/_/diff#comment-188404262"}}
{"comment": {"body": "On second thought, the device record is initialized with `device_id = None`, and only later an ID is set. So we could either have a hash that changes midway through the object\u2019s life \\(probably a very bad practice\\), or change the way we set device records IDs in general \\(destabilizing the system\\). I\u2019d rather stick with the current impl or create the dict in the first place instead of a set \\(bidi?\\).", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/553/_/diff#comment-188411530"}}
{"title": "Windows 10 hostnames identifcation", "number": 554, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/554", "body": "Using hostnames as device identifiers and extracting hostnames from MDNS packets if it's not available in DHCP"}
{"comment": {"body": "can you retarget to `release/cableslabs_poc`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/554/_/diff#comment-188459662"}}
{"comment": {"body": "New PR with retarget - [https://bitbucket.org/levl/comcast/pull-requests/556/mdns-hostnames-from-cablelabs](https://bitbucket.org/levl/comcast/pull-requests/556/mdns-hostnames-from-cablelabs){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/554/_/diff#comment-188465050"}}
{"title": "Windows 10 mac randomization", "number": 555, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/555", "body": "windows 10 mac randomization support\n24 hour logic\n\n"}
{"title": "Windows 10 hostnames identifcation", "number": 556, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/556", "body": "** Replaced  with retarget to release/cablelabs_poc.\nUsing hostnames as device identifiers and extracting hostnames from MDNS packets if it's not available in DHCP."}
{"comment": {"body": "this line is not doing anything", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/556/_/diff#comment-188473139"}}
{"comment": {"body": "explain this magic string?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/556/_/diff#comment-188473371"}}
{"title": "Cablelabs win10 combined", "number": 557, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/557", "body": "* Replaced  with combinde branch that ended up being just mdns.\nUsing hostnames as device identifiers and extracting hostnames from MDNS packets if it's not available in DHCP.\n"}
{"comment": {"body": "how can this happen that an android 10 device connects with wires?\n\nbetter yet - what do we do with android 10 devices connected with wires?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/557/_/diff#comment-188499228"}}
{"title": "REST API - Device lookup by MAC address", "number": 558, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/558", "body": "Support devices endpoint query by MAC address"}
{"comment": {"body": "I understood that the requirement of the feature is to return all levl ids that ever had the requested mac address, and for that we need to lookup in the history table.\n\nIs this not the case?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/558/_/diff#comment-188659810"}}
{"comment": {"body": "We need only the current device :\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/558/_/diff#comment-188681702"}}
{"comment": {"body": "retarget this to `release/cablelabs_poc`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/558/_/diff#comment-188700768"}}
{"comment": {"body": "This change not targeted for cablelabs", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/558/_/diff#comment-189532358"}}
{"title": "Various fixes for cableslabs", "number": 559, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/559", "body": "Refactor is_windows_10\nFix 802.11g display\nUse cfr_test_app from master for rb usage\nFix DHCP fingerprinting by ignoring DHCP DISCOVER messages\n\n"}
{"title": "Implement two way snr feature", "number": 56, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/56", "body": ""}
{"comment": {"body": "Like you suggested when we talked about it, I think lines 86 to 93 should go into the segmented regression function so it would output b1,b2 and bp \\(since m1 and m2 are built such that they are equal to 1 and 0 respectively.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/56/_/diff#comment-147288228"}}
{"comment": {"body": "Yes, you\u2019re right", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/56/_/diff#comment-147298548"}}
{"comment": {"body": "So currently, it\u2019s not enabled by default?  \nI didn\u2019t see any definition of this value in the docker files", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/56/_/diff#comment-147812770"}}
{"comment": {"body": "When it\u2019s not defined, the default value is '1', so it is enabled", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/56/_/diff#comment-147818245"}}
{"comment": {"body": "Yes, enabled. Also just added it to docker file.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/56/_/diff#comment-147885401"}}
{"title": "Cablelabs various fixes no mdns", "number": 560, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/560", "body": "Fix 802.11g display\nUse cfr_test_app from master for rb usage\nFix DHCP fingerprinting by ignoring DHCP DISCOVER messages\n\n"}
{"title": "Winodws 10 further fixes", "number": 561, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561", "body": "Add hostname extraction from NetBIOS protocol\nCase insensitive comparison of hostname (due to NetBIOS)\nFine tune logic to run andriod 10 code only on Winodws 10 systems with random mac address\n\n"}
{"comment": {"body": "this is awfully similar to mDNS. are they that similar?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-188996762"}}
{"comment": {"body": "Yeah.  \nThey are using the same packet format \\(the DNS packet format\\). Once we have a bit of a time, we can join them in a single parsing code.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-188996997"}}
{"comment": {"body": "Can you review and approvde?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189514690"}}
{"comment": {"body": "This thing needs deep refactor", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189519182"}}
{"comment": {"body": "when do we have wired android 10 or wired windows 10 with random mac?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/561/_/diff#comment-189519247"}}
