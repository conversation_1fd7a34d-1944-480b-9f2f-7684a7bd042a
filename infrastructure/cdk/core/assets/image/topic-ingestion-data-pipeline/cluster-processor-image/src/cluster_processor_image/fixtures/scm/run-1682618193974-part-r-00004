{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997306975", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997307420", "body": ""}
{"comment": {"body": "Yeah will come back to this. This is imperfect right now/needs iterating i.e. https://linear.app/unblocked/issue/UNB-199/onboarding-pull-request-discussion-page-arrow-points-in-the-wrong", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#discussion_r890612951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997320725", "body": ""}
{"comment": {"body": "Hmmm, I can see an argument for either, as in, this isn't technically a different component, it's a variation of the TextInput with certain fixed props\r\n<img width=\"626\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172260871-7094e2c7-b16d-4bbf-b11a-584d2a884c2c.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#discussion_r890622889"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997328173", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997334430", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997336648", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997338085", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997339249", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997340220", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997344428", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997348090", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997348504", "body": "Lots of little bits of nitpicky feedback, take them or leave them as you like..."}
{"title": "Add email icon support", "number": 1653, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1653", "body": "\n"}
{"title": "cleanup", "number": 1654, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1654"}
{"title": "update", "number": 1655, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1655"}
{"title": "test noficiation", "number": 1656, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1656"}
{"title": "Redirect from Dashboard to GH using web extension", "number": 1657, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1657", "body": "Redirects user from Dashboard to GH if web extension exists.\nChrome uses the DelcarativeNetRequest to instantly redirect.\nOf course, Safari doesn't support redirect using DelcarativeNetRequest yet.\nTherefore, using a content script on load to redirect user. This is slightly slower but still acceptable imo (as we have no other way atm...)\nChrome:\n\nSafari:\n\nRequires "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1657#pullrequestreview-998648486", "body": ""}
{"comment": {"body": "I'm not 100% clear when this will run, is it running only once when the new tab is created, or every time we try to visit a `getunblocked.com` URL?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1657#discussion_r891572493"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1657#pullrequestreview-998648557", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1657#pullrequestreview-998931641", "body": ""}
{"comment": {"body": "Once per new tab of getUnblocked.com", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1657#discussion_r891773758"}}
{"title": "Remove background transition of scrollbar for now", "number": 1658, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1658", "body": "The background clipping and background color styles were causing some weird weird bugs with the UI rendering (see below):\n\nRemove them for now. NOTE: this removes the transition fadeout of the scrollbar when hovering away."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1658#pullrequestreview-997367912", "body": ""}
{"title": "Pull Requests API changes to support Open PR tree-view", "number": 1659, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659", "body": "Fixes \nNew APIs:\n- getPullRequest\n- getPullRequests\n- getPullRequestThreads\n- getThreadsForPullRequests\nModified APIs:\n- getThreadsForMe"}
{"comment": {"body": "All makes sense to me (just the one question)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#issuecomment-1148222099"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-997402158", "body": ""}
{"comment": {"body": "For each of these deprecated fields, we'll support for as long as is needed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r890687744"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-997403523", "body": ""}
{"comment": {"body": "@matthewjamesadam @pwerry will this query param cause problems for symmetric channel polling? An alternative would be to create a separate API operation instead, like `getThreadsForMeWithOpenPRs`, if that makes channel polling easier to define.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r890688838"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-997507283", "body": ""}
{"comment": {"body": "Is it correct that we currently return 25 threads by default to the client? If that's true and we now have this parameter, what should be the behaviour when a client passes `true` here and there are >25 open PR threads? I'm wondering if its confusing if an open PR group in the tree view lists only a subset of the threads.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r890770523"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-997507692", "body": ""}
{"comment": {"body": "(ignore me if this API is only used by the hub)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r890770820"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-997509565", "body": ""}
{"comment": {"body": "Ah maybe this is used by both the hub and the extension, but the extension will always pass `false` and instead get the open PR threads via `/teams/{teamId}/threads/byPullRequests`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r890772221"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-998334409", "body": ""}
{"comment": {"body": "I don't think it necessarily makes it easier to have a separate API, but it might make it easier to understand. Certainly the push channel implementation would be clearer. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r891353372"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-998444372", "body": ""}
{"comment": {"body": "The `includeThreadsForMyOpenPrs=true` param will only be set for the Hub client. Other clients will never set this.\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/762bb94e-af68-4541-8ddc-16c057cd1f3c?message=c9dd74f5-8666-4467-a76b-67d82bcfde74).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r891431043"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-998447827", "body": ""}
{"comment": {"body": "I think it's pretty clear either way, personally.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r891433205"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-998449266", "body": ""}
{"comment": {"body": "+1\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9e69fe02-f798-4117-a768-45e7562ae13e?message=5ebf9b6c-1d7b-4883-8d79-2c7ca09efcd0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r891433831"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-998450797", "body": ""}
{"comment": {"body": "Ok, I'll leave it as is. We can change if necessary in future.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9e69fe02-f798-4117-a768-45e7562ae13e?message=6bd7db90-7d19-413b-9e0a-1c72920b408f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#discussion_r891434318"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1659#pullrequestreview-998909656", "body": ""}
{"title": "Add ability to push secrets to kubernetes", "number": 166, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/166", "body": "Kubernetes secrets will be loaded into pods via environment variables\n\nWe add a playbook to push kubernetes secrets that are encrypted by ansible over.\nWe modify helm charts to load secrets dynamically"}
{"title": "[BUG FIX] Selected contributor emails were not being sent through to createThread Api.", "number": 1660, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1660", "body": "ContributorRow ids are not guaranteed to be contributor.id. Apparently, they can be set to contributor.teamMemberId or contributor.email and finally contributor.id\nThe problem is that this amorphous id is what is passed to CreateKnowledgeCommand.toggleContributor.\nContributorsList.tsx\n```\n        \n            {addingNewContributor && addContributorRow}\n            {contributors.map((contributor) => {\n                const id = contributor.teamMemberId ?? contributor.email ?? contributor.id;\n                const isSelected = selectedContributorIds.some((selectedContributorId) => selectedContributorId === id);\n            return (\n                ContributorRow\n                    key={id}\n                    id={id}\n                    contributor={contributor}\n                    isSelected={isSelected}\n                    onClick={onSelectContributor}\n                /\n            );\n        })}\n    /div\n);\n\n```\nThat leads to a bug whereby it only searches for participants via contributor.id rather than the triple (contributor.teamMemberId, contributor.email, contributor.id).\nCreateKnowledgeCommand.createDiscussion\nconst _participants = ArrayUtils.compact(\n                [...currentlySelectedContributorIds].map((id) =\n                    currentContributors.find(\n                        (contributor) = contributor.id === id\n                    )\n                )\n            );\nQUESTION:\n1. Why are we representing ContributorRow ids by anything else but the contributor.id field?\n2. If we want to maintain this behaviour, we should have some sort of centralized encapsulation of this triple of potential ids. This is very likely going to lead to future bugs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1660#pullrequestreview-997411438", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1660#pullrequestreview-997414157", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1660#pullrequestreview-997440244", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1660#pullrequestreview-997440865", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1660#pullrequestreview-997441328", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1660#pullrequestreview-997463655", "body": ""}
{"title": "Build in xcodecloud", "number": 1661, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1661", "body": "Xcode Builds for the Hub\nWhen reviewing this PR, ignore all the code in the generatedApi and GRPC directories\nThis PR has a few different components:\n1. Necessary project settings changes to support building in Xcode Cloud (security policy, app category)\n2. ci_scripts directory rename to work with Xcode Cloud\n3. Introduction of Swift Codegen Diff action, which runs on a linux worker and compiles protoc-swift-gen from source (and subsequently our GRPC codegen for Hub IPC) followed by a diff of checked in source code\nWe were able to get all the sourcecode gen working in Xcode Cloud, but decided the economics made no sense for frequent builds like PR builds and merge builds. To generate the API and GRPC code in Xcode Cloud, we first needed to run a full brew update and then download and install openjdk. This added an additional 8 minutes of build time (to a base of 4 minutes), and ripped down over 1GB of data from the network. Yikes! Add another 2 minutes to actually generate the source and we were getting close to github actions for overall build times.\nInstead, we have source code checked in, which makes build times blazing fast in Xcode Cloud (4 minutes on a bad day). To deal with API consistency issues, we introduced a new GitHub action called Verify Swift Codegen Changes, which will fail unless the generated source in CI matches the checked-in source. Solving this is a simple matter of running make setup from /video-app/macos, then submit. \nThe installer builds still run in GitHub until we can figure out the coupling problem with VSCode, but given our success installing java etc, it might be possible to run and package the whole thing on an Xcode Cloud VM, which would be an order of magnitude cheaper.\nSpeaking of economics, here's how much we will save:\nGithub Actions\n\nCurrent github actions usage: ~12,000 minutes per month (yes, for real. download the usage report :))\nnumber of builds ~250\nAverage github actions build time: 45 minutes. This is nuts - if we imagine a more reasonable average build time of 30 minutes then we were using ~8000 minutes per month\nPrice per minute: $0.08\nTotal cost per month: $640\n\nXcode Cloud\n\nAverage build time: 4 minutes\nTotal build minutes = 250 * 4 = 1000\nTotal build hours per month = 16\nCost = Free\n\nSummary\nNot just the cost of builds, but the turnaround time of builds makes this a complete no-brainer. We should absolutely move the installer builds to Xcode Cloud as well"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1661#pullrequestreview-999028671", "body": ""}
{"comment": {"body": "file seems useless. remove?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1661#discussion_r891851532"}}
{"comment": {"body": "You probably don't need this. Meh.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1661#discussion_r891853375"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1661#pullrequestreview-999036789", "body": ""}
{"comment": {"body": "Going to need to keep this around for the installer build :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1661#discussion_r891857638"}}
{"title": "InstallationsAPI not setting displayName correctly", "number": 1662, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1662", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1662#pullrequestreview-998784175", "body": ""}
{"comment": {"body": "breaking API change. I updated clients, but there will be a transition period where some clients will break.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1662#discussion_r891666301"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1662#pullrequestreview-999098873", "body": ""}
{"comment": {"body": "this was the fix", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1662#discussion_r891906425"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1662#pullrequestreview-999905422", "body": ""}
{"title": "add scm only secret for private key", "number": 1663, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663", "body": "Added a new SCM only secret \nDeployed the secret as a placeholder to Kubernetes. I'll need to update it with the real value once this change lands. \nModified SCM service helm chart to include the new secret and set env vars for it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998552798", "body": ""}
{"comment": {"body": "Are we moving in a direction where we are splitting up secrets per service?\r\nCAn you rename it to scmservice (without hyphen). We're not using hyphens for services.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#discussion_r891506911"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998553548", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998556871", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998557498", "body": ""}
{"comment": {"body": "We should split service specific secrets. I am starting with this one and will work my way through the rest soon. \n\n\n\nMakes me uncomfortable to have say notification service accessing our private key used for Auth token generation! \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/db6fd457-60ba-4005-8916-8b9c0cd7d5ec?message=32920b5f-d782-4ca4-b796-dd8003889061).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#discussion_r891510248"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998560241", "body": ""}
{"comment": {"body": "@rasharab related to this: is it possible to replicate this per-service-secret behaviour in the local env?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#discussion_r891511984"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998563868", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998570801", "body": ""}
{"comment": {"body": "Is there a reason why we'd need this for local testing? Right now, the answer to your question is it's not possible, but we'd have to invest a bit of time figuring it out.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#discussion_r891518298"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998576938", "body": ""}
{"comment": {"body": "I thought for local testing we just put everything into an encrypted config file and don't bother with separation. \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/db6fd457-60ba-4005-8916-8b9c0cd7d5ec?message=6c74a20e-e863-4494-bdd2-8d85cf246504).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#discussion_r891523046"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998577893", "body": ""}
{"comment": {"body": "Yah thats fine\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/db6fd457-60ba-4005-8916-8b9c0cd7d5ec?message=b9f4413d-5be5-4e50-b5fb-59ad0ccefe5e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#discussion_r891523670"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998580790", "body": ""}
{"comment": {"body": "He was referring to per-service secrets. :) I did it such that everything is in one global file for local testing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#discussion_r891525589"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#pullrequestreview-998585570", "body": ""}
{"comment": {"body": "Okie dokes, for local I'll just put the private key along side the public key in `secrets.conf`. You're right, it doesn't matter for local stack. \r\n\r\nI was just worried about environmental differences but if the instances in dev/prod start up fine then we know getting the private key succeeded.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1663#discussion_r891529260"}}
{"title": "Fix snippet rendering in web extension", "number": 1664, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1664", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1664#pullrequestreview-998610392", "body": ""}
{"title": "fixed a typo in scm secret name", "number": 1665, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1665", "body": "Deployment was stuck because of a typo in the secret name. Deployed this secret and now pods are successfully rolling over."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1665#pullrequestreview-998656543", "body": ""}
{"title": "Services should allow for configurations tied to them", "number": 1666, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1666", "body": "We are now allowing for service-level secrets.\nTo do that, we need to segregate global configuration from service specific configuration."}
{"comment": {"body": "This is awesome @rasharab!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1666#issuecomment-1149029151"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1666#pullrequestreview-998655382", "body": "I love you Rashin"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1666#pullrequestreview-998665132", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1666#pullrequestreview-998666584", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1666#pullrequestreview-998674091", "body": ""}
{"title": "Fix up team urls", "number": 1667, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1667"}
{"title": "Update ktlint", "number": 1668, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1668", "body": "Also, enable auto newlines (was driving me up the fucking wall)"}
{"title": "Repo Resolution is case sensitive", "number": 1669, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1669"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1669#pullrequestreview-999906684", "body": "With comments"}
{"comment": {"body": "Why is post filtering required?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1669#discussion_r892483449"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1669#pullrequestreview-999954836", "body": ""}
{"comment": {"body": "Because we have to downcase the URL to match. You're right that it should not be necessary, and was already planning to address this in a follow-up PR.\r\n\r\nSame issue in this PR:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1672#discussion_r891772432", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1669#discussion_r892514306"}}
{"title": "update", "number": 167, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/167", "body": "Update secret to use stringData rather than data so we do not have to base64 encode value."}
{"title": "Update landing pages, add new routes", "number": 1670, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1670", "body": "/\n\n/download\n\n/extensions\n\n/team/:teamID/invite/:inviteID\n"}
{"comment": {"body": "One thing to consider: this means the doepicshit landing page bundle will contain all the content in the internal landing page.  Someone could easily crack it open and look at all its content.  Is this OK?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1670#issuecomment-1149195748"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1670#pullrequestreview-1002275703", "body": ""}
{"comment": {"body": "<3\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/e2770c1d-13cc-4a39-8ebd-9e6a813f0e2b?message=0a6c513d-da61-4c02-b88b-ebaff02bc7a6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1670#discussion_r894174317"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1670#pullrequestreview-998872532", "body": ""}
{"comment": {"body": "LOVE IT :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1670#discussion_r891729202"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1670#pullrequestreview-998873317", "body": "Thank you!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1670#pullrequestreview-998875049", "body": ""}
{"comment": {"body": "Should we keep this asset alongside this UI (since it isn't used anywhere else) like we do for the mobile icon assets?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1670#discussion_r891730966"}}
{"title": "Admin Web: Show tutorial state", "number": 1671, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1671"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1671#pullrequestreview-998959209", "body": ""}
{"title": "Case insensitive findRepo API matching", "number": 1672, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1672"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1672#pullrequestreview-998894943", "body": ""}
{"comment": {"body": "Exposed has `.lowerCase()` if you want to push the filtering to the database, but I prefer this since `RepoDAO.find { teamClause and providerClause }` won't return that many and this is way more debuggable.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1672#discussion_r891745022"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1672#pullrequestreview-998930020", "body": ""}
{"comment": {"body": "I'm not a fan of this at all. Seems weird to lookup both by owner/repo and by URL and lowercase everything.\r\n\r\nI think I'll refactor this in follow up by persisting only the lowercase url", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1672#discussion_r891772432"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1672#pullrequestreview-999913018", "body": ""}
{"comment": {"body": "Curious - what is the cause of all this case malarkey?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1672#discussion_r892485318"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1672#pullrequestreview-999960617", "body": ""}
{"comment": {"body": "I originally thought we could case-sensitive match on the URL that we get from GitHub, but turns out that's not practical, so we have to case-insensitive match.\r\n\r\nYou can do this with PG, but more efficient and less error-prone to persist as lowercase and pass lower params to the PG queries.\r\n\r\nDon't worry, I'll sort this shit out. Just want to get the fixes in first, refactor later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1672#discussion_r892518986"}}
{"title": "Update cdk dependencies", "number": 1673, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1673", "body": "Recommended way is to use npm-check-updates to update dependnecies.\nDid a ckd diff. There's some cdk fields that are going to be deprecated that I will have to fix up, and some s3 permissions that are added, but this looks to be fine!\nApparently, adds some s3 permissions by default.\n``\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretStringValueBeta1#fromToken is deprecated.\n  Usecdk.SecretValueinstead.\n  This API will be removed in the next major release.\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretProps#secretStringBeta1 is deprecated.\n  UsesecretStringValueinstead.\n  This API will be removed in the next major release.\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretStringValueBeta1#secretValue is deprecated.\n  Usecdk.SecretValueinstead.\n  This API will be removed in the next major release.\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretStringValueBeta1#fromToken is deprecated.\n  Usecdk.SecretValueinstead.\n  This API will be removed in the next major release.\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretProps#secretStringBeta1 is deprecated.\n  UsesecretStringValueinstead.\n  This API will be removed in the next major release.\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretStringValueBeta1#secretValue is deprecated.\n  Usecdk.SecretValue` instead.\n  This API will be removed in the next major release.\nStack AcmStack\nThere were no differences\nStack CustomerAssetsStack\nThere were no differences\nStack DnsStack\nThere were no differences\nStack EcrMirrorStack\nThere were no differences\nStack EksVPNConfigStack\nThere were no differences\nStack IamStack\nThere were no differences\nStack NetworkStack\nThere were no differences\nStack SqsStack\nThere were no differences\nStack StreamingAssetsStack\nIAM Statement Changes\n\n    Resource                             Effect  Action                                                                                                        Principal                      Condition \n\n -  ${AgoraStreamingAssetsBucket.Arn}/  Allow   s3:Abort                                                                                                     AWS:${AgoraStreamingUploader}            \n                                                 s3:PutObject                                                                                                                                           \n\n +  ${AgoraStreamingAssetsBucket.Arn}/  Allow   s3:Abort                                                                                                     AWS:${AgoraStreamingUploader}            \n                                                 s3:PutObject                                                                                                                                           \n                                                 s3:PutObjectLegalHold                                                                                                                                  \n                                                 s3:PutObjectRetention                                                                                                                                  \n                                                 s3:PutObjectTagging                                                                                                                                    \n                                                 s3:PutObjectVersionTagging                                                                                                                             \n\n(NOTE: There may be security-related changes not in this list. See )\nResources\n[~] AWS::IAM::Policy AgoraStreamingUploader/DefaultPolicy AgoraStreamingUploaderDefaultPolicyA4C7845B \n  [~] PolicyDocument\n      [~] .Statement:\n          @@ -2,6 +2,10 @@\n            [ ] {\n            [ ]   \"Action\": [\n            [ ]     \"s3:PutObject\",\n            [+]     \"s3:PutObjectLegalHold\",\n            [+]     \"s3:PutObjectRetention\",\n            [+]     \"s3:PutObjectTagging\",\n            [+]     \"s3:PutObjectVersionTagging\",\n            [ ]     \"s3:Abort*\"\n            [ ]   ],\n            [ ]   \"Effect\": \"Allow\",\nStack DatabaseStack\nThere were no differences\nStack RedisStack\nThere were no differences\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1673#pullrequestreview-998902231", "body": ""}
{"title": "Remove unnecessary Keep Alive", "number": 1674, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1674", "body": "Removes unused keep alive logic.\nAllows us to clean up permissions to simplify submission process."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1674#pullrequestreview-998934097", "body": ""}
{"title": "Move away from deprecated apis for storing secrets in ASM", "number": 1675, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1675", "body": "Tested it via a local deploy and confirmed secret names/values were correct."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1675#pullrequestreview-1000121845", "body": ""}
{"title": "[Onboarding] Fill in last step", "number": 1676, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1676", "body": "\n\nMissing assets / will add them once Ben has re-exported them all\nAdded API call to highlight the hub app -- will fail silently if it doesn't resolve (we don't want to block the onboarding flow if the arrow doesnt show up)\nAdded a top level checkbox to invite page that will select/deselect all emails"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1676#pullrequestreview-999971782", "body": ""}
{"title": "Replace ThreadModel.pr* fields with PullRequestModel", "number": 1677, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1677", "body": "Triggering pull request ingestion in the admin console will backfill these models/fields"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1677#pullrequestreview-998964926", "body": "just minor comments - nice work"}
{"comment": {"body": "There's also a pull request external ID (`id`), which is unique per team I believe, so this could be used instead of this compound lookup. Up to you, don't feel strongly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1677#discussion_r891856608"}}
{"title": "Simplify VSCode tree views", "number": 1678, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678", "body": "Fixes UNB-212 .  This work is needed to make implementing the Open PR Tree View UI easier.\nSimplify TreeViews, their data stores, etc, as per the conversation here: :\n* TreeView is now a collapsible list of TreeItems with a header\n* TreeView items are now generic: they take in a label, icon, prefix and suffix.  No more thread-specific stuff.\n* TreeView simply takes in the list of its children. This lets us get rid of all the stores and providers.\n* Cleaned up the sidebar provider -- there is now much less data processing happening everywhere.\n* Removed much of the Notes UI, since we're not really using it anywhere.  There's still some stuff left I think.\nLet me know if you think we shouldn't be removing any of this, I am happy to resurrect things if we think they're useful."}
{"comment": {"body": "Helps with removing the boilerplate. Had a lot of duplication going around for the threads sidebar.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-**********", "body": ""}
{"comment": {"body": "> serializes between runs!\r\n\r\nAka, if I close VScode and reopen it, it uses this cache??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r892588266"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1000058379", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1000059268", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1000061267", "body": ""}
{"comment": {"body": "Yes!  Very surprising.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/843a4481-1168-4e83-91fe-dcc8b1549508?message=da5cba3c-a084-45f6-81be-93483e24176d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r892591045"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1000074430", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1000568425", "body": ""}
{"comment": {"body": "interesting\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/843a4481-1168-4e83-91fe-dcc8b1549508?message=db3ab74e-d596-443e-897c-3f1cb67509d5).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r892953277"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1000568894", "body": ""}
{"comment": {"body": "interesting\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/843a4481-1168-4e83-91fe-dcc8b1549508?message=db3ab74e-d596-443e-897c-3f1cb67509d5).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r892953675"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1000577671", "body": ""}
{"comment": {"body": "interesting\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/843a4481-1168-4e83-91fe-dcc8b1549508?message=db3ab74e-d596-443e-897c-3f1cb67509d5).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r892961834"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1000577906", "body": ""}
{"comment": {"body": "interesting\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/843a4481-1168-4e83-91fe-dcc8b1549508?message=db3ab74e-d596-443e-897c-3f1cb67509d5).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r892962022"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1000580432", "body": ""}
{"comment": {"body": "Interesting \ud83e\udd14\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/843a4481-1168-4e83-91fe-dcc8b1549508?message=f0b9219a-2fd3-47a4-ac52-1db606d7811e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r892963973"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1001826527", "body": ""}
{"comment": {"body": "Huh I didn't mean for that to post multiple times. I wonder how that happened(??)\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/843a4481-1168-4e83-91fe-dcc8b1549508?message=e77e478c-1885-4e6b-a10e-4983d091381b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r893846640"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-1294322149", "body": ""}
{"comment": {"body": "Since we generally use `retainContextWhenHidden` for our stateful views, we probably could remove this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r1103437221"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#pullrequestreview-999048993", "body": ""}
{"comment": {"body": "This was causing a bug: we're storing the webview state in VSCode's webview state object, and this actually serializes between runs!  In this PR the webview state has changed shape (with all the treeview work), and so the deserialized data was unexpected, and the view failed.\r\n\r\nSo we'll do the simple thing: We will not deserialize if the commit SHA has changed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1678#discussion_r891867301"}}
{"title": "Set createdAt for PullRequestModel", "number": 1679, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1679"}
{"title": "Now anyone can generate secrets like the pros", "number": 168, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/168"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/168#pullrequestreview-866771657", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/168#pullrequestreview-866771878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/168#pullrequestreview-866773484", "body": ""}
{"title": "SourceMark engine should handle force pushes", "number": 1680, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1680", "body": "Definitely improved.\nBefore | After\n--|--\n | "}
{"comment": {"body": "Having trouble visualizing the effect of this bug. Any ideas?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1680#issuecomment-1150025667"}}
{"comment": {"body": "Need to do more research on this one. There are some negative consequences. Back to draft.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1680#issuecomment-1150071098"}}
{"title": "Was calling showTutorialOverlay from non-main actor", "number": 1681, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1681"}
{"comment": {"body": "It blows my mind that in 2022 this is still a problem we have to fix manually \ud83d\ude2d", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1681#issuecomment-1150166218"}}
{"comment": {"body": "> It blows my mind that in 2022 this is still a problem we have to fix manually \ud83d\ude2d\r\n\r\nTotally agree - this should be handled automatically when anything touches the app delegate or anything display related", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1681#issuecomment-1150170640"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1681#pullrequestreview-1000123186", "body": ""}
{"title": "Fix admin button -- sorry Pete", "number": 1682, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1682"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1682#pullrequestreview-1000127925", "body": ""}
{"title": "Remove swift gen dir from incremental cleanup target", "number": 1683, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1683", "body": "Since gen code is now checked in for Swift, if the swift gen dir is cleaned, it will cause major problems for anyone making changes or running npm commands"}
{"comment": {"body": "I do wonder if we should be asserting on builds if there's a mismatch with the private.yml file and the submitted generated code.\r\nOne way to do this is create another task (similar to incremetnal), and that task actually saves the current spec used to generate the code somewhere (that will be submitted with the code), and somehow abort the build if there's a mismatch.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1683#issuecomment-1150192254"}}
{"comment": {"body": "> I do wonder if we should be asserting on builds if there's a mismatch with the private.yml file and the submitted generated code.\r\n> One way to do this is create another task (similar to incremetnal), and that task actually saves the current spec used to generate the code somewhere (that will be submitted with the code), and somehow abort the build if there's a mismatch.\r\n\r\nI added something like this already. We have a github action that generates the swift code and does a diff against the checked in source. Any differences will result in a build failure", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1683#issuecomment-1150193832"}}
{"comment": {"body": "> > I do wonder if we should be asserting on builds if there's a mismatch with the private.yml file and the submitted generated code.\r\n> > One way to do this is create another task (similar to incremetnal), and that task actually saves the current spec used to generate the code somewhere (that will be submitted with the code), and somehow abort the build if there's a mismatch.\r\n> \r\n> I added something like this already. We have a github action that generates the swift code and does a diff against the checked in source. Any differences will result in a build failure\r\n\r\nThat does create a dependency on CI to determine this, but it's fine for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1683#issuecomment-1150197020"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1683#pullrequestreview-1000159973", "body": ""}
{"title": "Style nits for landing pages", "number": 1684, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1684", "body": "To match the designs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1684#pullrequestreview-1000173406", "body": ""}
{"title": "Cleanup", "number": 1685, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1685"}
{"title": "Check the first message for archiving threads", "number": 1686, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1686"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1686#pullrequestreview-1000201564", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1686#pullrequestreview-1000202045", "body": ""}
{"title": "Temp fix for storybook typing build error", "number": 1687, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1687", "body": "This storybook is broken right now anyways due to babel bugs.  This at least prevents build errors on the main bundle."}
{"title": "Add pull request API endpoints", "number": 1688, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1688"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1688#pullrequestreview-1000286533", "body": "looks good, apart from \"mine\""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1688#pullrequestreview-1000307382", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1688#pullrequestreview-1000447470", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1688#pullrequestreview-1000641732", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1688#pullrequestreview-1001637140", "body": ""}
{"title": "Add ability to define dead letter queues and othe rqueue properties", "number": 1689, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1689", "body": "This pr adds a few things:\n1. Dead Letter Queue Support\n2. Visibility Timeout Support\n3. Max Retention Period support\nAlready tested against dev"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1689#pullrequestreview-1000256949", "body": ""}
{"title": "Add GitHub App Secret and Key", "number": 169, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/169"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/169#pullrequestreview-866782031", "body": ""}
{"title": "Setup loading state in vscode", "number": 1690, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1690", "body": "Remove text based loading state for now...\n"}
{"comment": {"body": "@benedict-jw Don't think we have time to do skeleton UI right now. This is a slight upgrade from the \"loading\" text...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1690#issuecomment-1150264879"}}
{"comment": {"body": "That's fine \u2014 I wasn't thinking we'd do it here anyway. Was mostly thinking about the dashboard. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1690#issuecomment-1150339219"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1690#pullrequestreview-1000266927", "body": ""}
{"title": "Only open the sidebar in the first step", "number": 1691, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1691", "body": "Per Dennis' feedback, only have the UB sidebar open for the Import Knowledge step, close it for ensuing steps to make the viewable canvas less busy"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1691#pullrequestreview-1000356070", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1691#pullrequestreview-1000356874", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1691#pullrequestreview-1000360591", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1691#pullrequestreview-**********", "body": ""}
{"title": "Cleanup dead field", "number": 1692, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1692"}
{"title": "fix field thype", "number": 1693, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1693"}
{"title": "Support Authed Images", "number": 1694, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1694", "body": "Added AuthContext.Provider to Web extension.\nAlso added max-width within image_block to scale images to fit.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1694#pullrequestreview-**********", "body": ""}
{"title": "Only the first thread message should have an anchor sourcemark", "number": 1695, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1695"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1695#pullrequestreview-**********", "body": ""}
{"title": "Include pullrequest in api ThreadInfo model", "number": 1696, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1696", "body": "I was wrong, we weren't including it. This fixes that"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1696#pullrequestreview-**********", "body": ""}
{"title": "Cleanup", "number": 1697, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1697"}
{"title": "Remove cache", "number": 1698, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1698"}
{"title": "DO NOT MERGE: Test PR for Screenshots", "number": 1699, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1699"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1699#pullrequestreview-1000502253", "body": ""}
{"comment": {"body": "Can you add a description for what this block is doing? I'm finding it a little difficult to parse at first glance.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1699#discussion_r892900017"}}
{"title": "Icon interface", "number": 17, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/17", "body": "Update Icon Interface"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/17#pullrequestreview-845146784", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/17#pullrequestreview-845148419", "body": ""}
{"title": "Ensure we redeploy on descrets changes", "number": 170, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/170"}
{"title": "Ensure honeycomb api key is resilient", "number": 1700, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1700", "body": "Update secrets files to ensure honeycomb key is not followed by newline as well."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1700#pullrequestreview-1000546183", "body": ""}
{"title": "Log new DB connections", "number": 1701, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1701"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1701#pullrequestreview-1000558592", "body": ""}
{"title": "Fix bug when generating install url for personal org", "number": 1702, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1702"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1702#pullrequestreview-1000568147", "body": ""}
{"title": "Clip thread title to one line in web dashboard", "number": 1703, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1703", "body": "We're now sending back the full thread title string so it needs to be clipped in the UI if the string is very long\nThere's a known issue with text-overflow ellipses in child containers of flex parents -- workaround is to use grid in the parent and the ellipsing will work as expected."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1703#pullrequestreview-1000579157", "body": ""}
{"title": "Refactor icon usage a little bit", "number": 1704, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1704", "body": "We were duplicating logic on icon usage in a few places, specifically the logic on what icon should be used for a specific thread.  This simplifies it down to two places:\n* Shared Icon and ThreadIcon components, which render an icon\n* A secondary usage in VSCode, which needs direct access to icon URIs.\nI'd like to figure out how to merge these into one, but that will wait for later.\n(also, I added the green \"Open PR\" icon)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1704#pullrequestreview-1000581571", "body": ""}
{"comment": {"body": "Gets an IconSrc for a thread", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1704#discussion_r892964874"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1704#pullrequestreview-1000623274", "body": ""}
{"title": "Theory: DB busy lock contention during rollover is maxing out DB connections", "number": 1705, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1705", "body": "Adjust retry config to reduce contention.\nDownside is that deployments may rollover slightly slower (on the order of low number of seconds).\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1705#pullrequestreview-1000621317", "body": ""}
{"title": "Allow for split thread invites dependent on whether authed or not user", "number": 1706, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1706", "body": "As requested, we now send different types of thread invites\n1. user is authed and a team member\n2. user is not authed.\nWe create a new api model to represent this (and are deprecating emails and participants fields)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1706#pullrequestreview-1001544511", "body": ""}
{"comment": {"body": "FYI feel free to add an `ArrayUtils.compactMap` helper -- this is a pattern we could use pretty often and the name is used in Swift so it's somewhat familiar to some of us", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1706#discussion_r893644112"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1706#pullrequestreview-1001713667", "body": ""}
{"comment": {"body": "Excellent point. Thanks matt", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1706#discussion_r893762530"}}
{"title": "DB Connection pooling with HikariCP", "number": 1707, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1707", "body": "The library provides a ton of configuration options, but it's also built in an opinionated way with sensible defaults. \nDisabled in Prod. I have no idea how to test this so I think we just need to deploy it in dev and observe"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1707#pullrequestreview-1000682031", "body": ""}
{"title": "Add getThreadsForPullRequests and getPullRequestThreads operations", "number": 1708, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1708"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1708#pullrequestreview-1001643104", "body": ""}
{"comment": {"body": "could do with more tests, eg:\r\n- modifiedSince\r\n- ordering\r\n- empty prID input list", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1708#discussion_r893712965"}}
{"title": "Client Versions API Proposal", "number": 1709, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709", "body": "Comments inline"}
{"comment": {"body": "Any thoughts on what the version(s) will actually be?  Right now we're using commit shas which isn't right for this. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#issuecomment-1150682978"}}
{"comment": {"body": "> Any thoughts on what the version(s) will actually be? Right now we're using commit shas which isn't right for this.\r\n\r\n@matthewjamesadam agree. I think the only requirement is that the version numbers are increasing. I was hoping we could use GitHub Releases, backed by Git tags for this purpose. CI would take care of creating the releases/tags.\r\n\r\nhttps://docs.github.com/en/repositories/releasing-projects-on-github/about-releases\r\n\r\nAlso, please avoid semver -- I hate it more than YAML.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#issuecomment-1150696540"}}
{"comment": {"body": "> Any thoughts on what the version(s) will actually be? Right now we're using commit shas which isn't right for this.\r\n\r\nAgreed. The intent here was server controlled behaviour, but I think it might make more sense if the client made self-determinations about updates based on very simple rules. The service could just return a configuration object that the client uses to reason about upgrades. \"Latest version\" could be part of the configuration object", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#issuecomment-1151415391"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1000728516", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1000740093", "body": "added a bunch of suggestions / alternatives to think about"}
{"comment": {"body": "this is one potential option:\r\n```suggestion\r\n  - name: RemoteConfig\r\n    tags:\r\n      - Config\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#discussion_r893090872"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1001555855", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1001558207", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1001710608", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1001743977", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1001825248", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1001978243", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1001978698", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1001988039", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002002548", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002011379", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002012324", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002017632", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002019029", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002025954", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002052473", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002056115", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002061684", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1002064904", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1003387602", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1003777618", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1004733556", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1004734712", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1709#pullrequestreview-1004855684", "body": ""}
{"title": "Tests for Chat models", "number": 171, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/171"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/171#pullrequestreview-866795912", "body": ""}
{"comment": {"body": "next PR...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/171#discussion_r794953763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/171#pullrequestreview-866799540", "body": ""}
{"title": "Update", "number": 1710, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1710"}
{"title": "Turn on db connection pools in prod", "number": 1711, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1711", "body": "Dev verified"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1711#pullrequestreview-1000768672", "body": ""}
{"title": "Drop max db connection pool size to 5", "number": 1712, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1712"}
{"title": "Remove hikari's db connection pool", "number": 1713, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1713"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1713#pullrequestreview-1001611875", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1713#pullrequestreview-1001618911", "body": ""}
{"title": "Use correct extension urls from the hub", "number": 1714, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1714", "body": "Use extension URLs for vscode fallback and extension preference"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1714#pullrequestreview-1001656102", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1714#pullrequestreview-1001656181", "body": ""}
{"title": "Add compact map utility", "number": 1715, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1715", "body": "A brilliant man by the name of @matthewjamesadam suggested I add this utility.\nHe was right...AGAIN."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1715#pullrequestreview-1001791035", "body": ""}
{"title": "Fix lint", "number": 1716, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1716"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1716#pullrequestreview-1001792091", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1716#pullrequestreview-1001792384", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1716#pullrequestreview-1001796979", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1716#pullrequestreview-1003333618", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1716#pullrequestreview-1003345046", "body": ""}
{"title": "Fix sidebar continue when tutorial has occurred", "number": 1717, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717", "body": "Some state from the main \"onboarding\" installation leaked into the sidebar when doing the refactor.\nRemoved unnecessary logic and added a command to trigger sidebar state."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717#pullrequestreview-1001811639", "body": ""}
{"comment": {"body": "if we do this here, do we still need the same line in the `extension.ts` file?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717#discussion_r893836209"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717#pullrequestreview-1001812577", "body": ""}
{"comment": {"body": "I guess my question is more what are the use cases for calling the command in both places ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717#discussion_r893836870"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717#pullrequestreview-1001819879", "body": ""}
{"comment": {"body": "This is just registering the command into the command pallets which will allow us to manually trigger the state after initialization.\r\n\r\nWe will eventually need to integrate this into the UI somewhere to allow people to onboard *additional* repos in a workspace.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717#discussion_r893841977"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717#pullrequestreview-1001821102", "body": ""}
{"comment": {"body": "This doesn't actually call the command like in the extension.ts file.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717#discussion_r893842894"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1717#pullrequestreview-1001831055", "body": ""}
{"title": "Update getThreadsForMe", "number": 1718, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718", "body": "Updates the getThreadsForMe operation to add a new includeThreadsForMyOpenPrs parameter. This will be parameter is optional and true by default so as to not break old clients. New clients will need to pass in true for this parameter in order to exclude threads from the user's open PRs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718#pullrequestreview-1001852694", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718#pullrequestreview-1001883925", "body": ""}
{"comment": {"body": "I would fork the test to test both cases", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718#discussion_r893891656"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718#pullrequestreview-1001982593", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718#pullrequestreview-1002064908", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718#pullrequestreview-1002068743", "body": ""}
{"comment": {"body": "Done https://github.com/NextChapterSoftware/unblocked/pull/1718/commits/21fd4f75520e618f30eb8d2e9db37ea92b9c2edf", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718#discussion_r894010050"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718#pullrequestreview-1002073934", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1718#pullrequestreview-1003074032", "body": "thanks for the tests!"}
{"title": "remove dead field", "number": 1719, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1719"}
{"title": "Add createdAt accessors", "number": 172, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/172", "body": "Follow on from https://github.com/Chapter2Inc/codeswell/pull/171#discussion_r794953763"}
{"title": "Add open PR query param to hub request", "number": 1720, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1720", "body": "Completes UNB-210"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1720#pullrequestreview-1001963081", "body": ""}
{"title": "Cleanup listener before triggering complete", "number": 1721, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1721", "body": "Listener needs to be cleaned up before onComplete callback is made and sidebar is updated."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1721#pullrequestreview-1001893231", "body": ""}
{"title": "Move to apache jdbc driver", "number": 1722, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1722", "body": "This jdbc driver is far more comprehensive to HIkari.\nIt allows for much more granular control over connections and termination of connections.\n"}
{"comment": {"body": "Let's give it a whirl, easy to swap out for Hikari later. Apache commons is a \"safer\" bet", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1722#issuecomment-1151562546"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1722#pullrequestreview-1001909270", "body": ""}
{"title": "Dummy change, ignore", "number": 1723, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1723"}
{"title": "Utilize repoStore polling in installation store", "number": 1724, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1724", "body": "We are currently polling the Installations API every two seconds. This is causing us to hit GH rate limits and breaking the system.\nIdeal situation would be to continue polling the installations API with modified-since or have a push model.\nIn the meantime, we will poll the repos instead of the installations and only fetch installations when the repo list changes. \nThis works since the list of repos updating is the product of a successful installation."}
{"comment": {"body": "> thanks jeff. does this work in a timely way: how long after installing on a new repo does the UI take to update in practice?\r\n\r\nIt should reflect whatever is returned by the `findRepo` call pretty quickly -- so if `findRepo` returns updated values, it should map the repos shortly after.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1724#issuecomment-1151624750"}}
{"comment": {"body": "> > thanks jeff. does this work in a timely way: how long after installing on a new repo does the UI take to update in practice?\r\n> \r\n> It should reflect whatever is returned by the `findRepo` call pretty quickly -- so if `findRepo` returns updated values, it should map the repos shortly after.\r\n\r\nyeah, I know. just wondering how long this takes in your tests? if slow might motivate webhook-driven installation updates on server.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1724#issuecomment-1151664206"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1724#pullrequestreview-1001968045", "body": "thanks jeff. does this work in a timely way: how long after installing on a new repo does the UI take to update in practice?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1724#pullrequestreview-1001993916", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1724#pullrequestreview-1003264297", "body": ""}
{"comment": {"body": "@matthewjamesadam \ud83d\ude22 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1724#discussion_r894858027"}}
{"title": "[Onboarding] Enable sourcemark tooltip click within onboarding flow", "number": 1725, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725", "body": "Add intercepting layer command that redirects the text editor sourcemark tooltip click to the tutorial command when onboarding.\nAlso, add logic to try to set the interaction thread to the first thread with a resolved sourceMark."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#pullrequestreview-1002117479", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#pullrequestreview-1002118086", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#pullrequestreview-1002122473", "body": ""}
{"comment": {"body": "Can we add some comments here explaining the difference?  I understand it right now, but if I come back to this code in a month I'll be totally lost", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#discussion_r894054161"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#pullrequestreview-1002126131", "body": ""}
{"comment": {"body": "Any sense for how much this impacts startup time?  I feel like it could maybe blow out the loading time by quite a bit as resolving the threads may take awhile...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#discussion_r894058056"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#pullrequestreview-1002126666", "body": ""}
{"comment": {"body": "I personally haven't noticed that much of a difference -- we could get this in and see if anyone else notices any significant loading time??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#discussion_r894058501"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#pullrequestreview-1002126674", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#pullrequestreview-1002131544", "body": ""}
{"comment": {"body": "Wrote a docs comment describing the command and its purpose.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1725#discussion_r894062368"}}
{"title": "For Kay", "number": 1726, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1726", "body": "Ensure we do not send welcome email more than once."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1726#pullrequestreview-1002014866", "body": ""}
{"title": "More work for Rashin :)", "number": 1727, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1727"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1727#pullrequestreview-1002038471", "body": ""}
{"title": "Added a rule to disallow traffic to __deepcheck from public web exc", "number": 1728, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1728", "body": "Added a rule to disallow traffic to __deepcheck from public web except if the magic header is set\nThis rule will also enable traffic sampling to reduce the volume of our access logs\nTested in Dev and it works fine."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1728#pullrequestreview-1002048269", "body": ""}
{"title": "IMprove logging", "number": 1729, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1729"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1729#pullrequestreview-1002091527", "body": ""}
{"title": "Cleanup linter code so only one rules definition", "number": 173, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/173"}
{"title": "Ensure pooled passwords are updated on new connections", "number": 1730, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1730"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1730#pullrequestreview-1002108299", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1730#pullrequestreview-1002162192", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1730#pullrequestreview-1002162193", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1730#pullrequestreview-1002162859", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1730#pullrequestreview-1002223428", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1730#pullrequestreview-1002225882", "body": "I dug into this as well and came to the same conclusion - only way is to extend DataSource. This is super clean, nice work!"}
{"title": "Add client headers to hub", "number": 1731, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1731", "body": "PlistBuddy ninja hacks ftw\nResolves UNB-232"}
{"comment": {"body": "> This won't use the right value for developer builds right?\r\n\r\nWhat do we want to use for developer builds?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1731#issuecomment-1151716449"}}
{"comment": {"body": "> > This won't use the right value for developer builds right?\r\n> \r\n> What do we want to use for developer builds?\r\n\r\nCurrent SHA, I'd imagine, at least for now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1731#issuecomment-1151717259"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1731#pullrequestreview-1002106485", "body": "This won't use the right value for developer builds right?"}
{"title": "Show Open PR threads in VSCode sidebar", "number": 1732, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1732", "body": "Fixes UNB-211 -- \n\nAlmost all the work here is in PullRequestThreadStore, which manages fetching pull requests, and joining the threads for each pull request."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1732#pullrequestreview-1002117953", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1732#pullrequestreview-1002123981", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1732#pullrequestreview-1002176118", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1732#pullrequestreview-1002177932", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1732#pullrequestreview-1002214822", "body": ""}
{"title": "Add pull request pusher channels", "number": 1733, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1733"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1733#pullrequestreview-1002117470", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1733#pullrequestreview-1003130504", "body": ""}
{"comment": {"body": "minor: maybe rename this function `findById`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1733#discussion_r894764590"}}
{"title": "Log request callIds to honeycomb", "number": 1734, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1734", "body": "Matt Adam brought up that we should be associating traces with requestIds for api calls (these ids are sent to client so we can backtrace them to honeycomb)\nHes right as usual!\n\n[]=name&fields[]=service.name&span=70eb9cb882563d10"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1734#pullrequestreview-1002125404", "body": ""}
{"title": "Retry more requests", "number": 1735, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735", "body": "Using fetch retry package that should hopefully catch more errors.\nHopefully catches unexpected errors such as this which sometimes occurs randomly (e.g. 500) or with network issues\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#pullrequestreview-1002173566", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#pullrequestreview-1002173928", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#pullrequestreview-1003007450", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#pullrequestreview-1003267051", "body": ""}
{"comment": {"body": "Do we need to check `error` here as well?  Or if we don't return true/false does the default fetch-retry behaviour run?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#discussion_r894859830"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#pullrequestreview-1003267406", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#pullrequestreview-1346703875", "body": ""}
{"comment": {"body": "Tes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#discussion_r1140683033"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#pullrequestreview-1346704585", "body": ""}
{"comment": {"body": "ASFD", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1735#discussion_r1140683309"}}
{"title": "Remove invalid assertion for single SourcePoint per mark", "number": 1736, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1736", "body": "This assertion is no longer valid since we create multiple original source points;\nanother is created on PR merge, unless the merge is a trivial fast-forward rebase.\nThis assertion was missed from this PR #1084.\nResponsible for 1000s of client error messages:\nFailed to push sourcepoints into local cache Expected single item in list, but got 2\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1736#pullrequestreview-1002125192", "body": ""}
{"title": "Add pusher channel for getThreadsForPullRequests", "number": 1737, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1737"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1737#pullrequestreview-1002129046", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1737#pullrequestreview-1003045462", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1737#pullrequestreview-1003095479", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1737#pullrequestreview-1003111241", "body": ""}
{"title": "SourceMark stats to assess where to invest", "number": 1738, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1738", "body": "Quick and dirty stats. Might formalize down the line as an API, but for now this is useful enough as is.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1738#pullrequestreview-1002140010", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1738#pullrequestreview-1002158085", "body": ""}
{"title": "Move away from deprecated code", "number": 1739, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1739"}
{"title": "Fix worklfows", "number": 174, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/174"}
{"title": "Update SourceMarkCalculator.getPointForSourceMark() to accept unsavedFiles", "number": 1740, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1740"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1740#pullrequestreview-1003029208", "body": ""}
{"title": "Try to unblock queue", "number": 1741, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1741", "body": "Don't re-throw otherwise the remaining messages won't be processed."}
{"title": "Handle exceptions better", "number": 1742, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1742", "body": "Throwing an exception here will prevent other messages from being processed\nAlso if posting a message update to GitHub fails due to 404, we should not keep retrying"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1742#pullrequestreview-1002268604", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1742#pullrequestreview-1002931458", "body": ""}
{"comment": {"body": "David, this looks to be. acommon  pattern across the file?\r\nShould we make it a generalized private handler?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1742#discussion_r894627036"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1742#pullrequestreview-1003182461", "body": ""}
{"comment": {"body": "\ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1742#discussion_r894800797"}}
{"title": "Make new message indicator blue", "number": 1743, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1743"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1743#pullrequestreview-1002237489", "body": ""}
{"title": "Fix repos page when no PullRequestIngestionModel exists", "number": 1744, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1744", "body": "Fixes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1744#pullrequestreview-1002309826", "body": "The insert is a bit weird but harmless I guess"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1744#pullrequestreview-1003180206", "body": ""}
{"comment": {"body": "Test comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1744#discussion_r894799224"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1744#pullrequestreview-1003181706", "body": ""}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1744#discussion_r894800211"}}
{"title": "Fix initial configuration for dynamicDataSource", "number": 1745, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1745"}
{"title": "Use PR titles and message preview", "number": 1746, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746", "body": "Fixes UNB-164"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#pullrequestreview-1003037270", "body": ""}
{"comment": {"body": "This is necessary to prevent namespace collisions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#discussion_r894697680"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#pullrequestreview-1003080718", "body": "excited to see this"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#pullrequestreview-1003084038", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#pullrequestreview-1003084090", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#pullrequestreview-1003084841", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#pullrequestreview-1003119190", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#pullrequestreview-1003136691", "body": ""}
{"comment": {"body": "This will eventually need to be broadened to handle various oddball situations -- an empty first paragraph/block, first blocks that are not paragraphs (lists, etc)... fine for now though", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#discussion_r894768961"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#pullrequestreview-1003143837", "body": ""}
{"comment": {"body": "test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4ff2eef9-9078-4a4c-9a63-0c4370451bff?message=5cd0564c-9efb-4ce5-9ca2-4bfe25f638b6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#discussion_r894773960"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#pullrequestreview-1003153123", "body": ""}
{"comment": {"body": "I basically duplicated what the web code is doing for thread previews, but yeah there needs to be some better handling here for sure\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4ff2eef9-9078-4a4c-9a63-0c4370451bff?message=60c6d6fd-d2a2-4e3c-abeb-c6fab62379a0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1746#discussion_r894780410"}}
{"title": "Do not send welcome emails after first time", "number": 1747, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1747", "body": "Fucking boolean man."}
{"title": "Brings vscode foreward when minimized, hopefully fixes other focus bugs too", "number": 1748, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1748"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1748#pullrequestreview-1003147599", "body": ""}
{"title": "[Onboarding] Add assets", "number": 1749, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1749", "body": "\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1749#pullrequestreview-1003347271", "body": ""}
{"title": "update", "number": 175, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/175"}
{"title": "Adding annotations to Grafana dashboards for deployments", "number": 1750, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1750", "body": "Added a new step to service deployments to annotate all Grafana dashboards with the commit sha and deployment status.\nFor now I have the condition which limits annotations to only prod commented out. If it works as expected I'll make a separate PR to remove the comment.\nWe will also need to add the same step to landing page and dashboard deployments later.\nMore info about this action: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1750#pullrequestreview-1003195214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1750#pullrequestreview-1003195392", "body": "This is fucking aswsome"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1750#pullrequestreview-1003196748", "body": ""}
{"title": "Don't tab into the message editor buttons UNB-194", "number": 1751, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1751", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1751#pullrequestreview-1003221863", "body": ""}
{"title": "Testing: Don't merge", "number": 1752, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1752"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1752#pullrequestreview-1003228269", "body": ""}
{"comment": {"body": "zzzz", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1752#discussion_r894832941"}}
{"title": "[DRAFT] [IGNORE] Test commit", "number": 1753, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1753"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1753#pullrequestreview-1003233633", "body": ""}
{"comment": {"body": "Wrong ordering", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1753#discussion_r894836573"}}
{"title": "Rename archive -> delete UNB-149", "number": 1754, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1754", "body": "\n\n\n\n\n"}
{"comment": {"body": "> Planning to do this bit in the deleted view:\r\n> \r\n> * \"Deleted by Pete 3d ago\"\r\n> * \"Deleted automatically 3h ago\"\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1754/files#diff-2a4210e97a8f066ae67106e788c03863faacabf569f1a626f27c9e9a9a630b9aR52", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1754#issuecomment-1152751744"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1754#pullrequestreview-1003241771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1754#pullrequestreview-1003298651", "body": "Planning to do this bit in the deleted view?\n- \"Deleted by Pete 3d ago\"\n- \"Deleted automatically 3h ago\""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1754#pullrequestreview-1003332373", "body": ""}
{"title": "forgot to add the token to Dev deploy job", "number": 1755, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1755"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1755#pullrequestreview-1003243706", "body": ""}
{"title": "Fix Open PR UI bugs", "number": 1756, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1756", "body": "Dashboard and web extension display open PR threads in the mine section\nDon't duplicate comments if you have multiple open PRs\nWhen you open a new PR, add the new PR to the list (don't list-replace)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1756#pullrequestreview-1003245991", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1756#pullrequestreview-1003246232", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1756#pullrequestreview-1003259828", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1756#pullrequestreview-1003398589", "body": ""}
{"comment": {"body": "FYI @jeffrey-ng this is the helper method we discussed earlier -- given an input stream, and a function that maps it to another stream, it handles the stream swapping and loading state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1756#discussion_r894949020"}}
{"title": "Fix unexpected install sidebar", "number": 1757, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1757", "body": "Were displaying the \"installation\" ui in the sidebar unnecessarily.\nWe should skip if there's at least one installation already installed with valid repos."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1757#pullrequestreview-1003260367", "body": ""}
{"title": "Fix width of imgs in message editor", "number": 1758, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1758", "body": "bug:\n\nafter:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1758#pullrequestreview-1003300346", "body": ""}
{"title": "limit deployment annotation to prod only", "number": 1759, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1759"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1759#pullrequestreview-1003301791", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1759#pullrequestreview-1003311225", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1759#pullrequestreview-1003317038", "body": ""}
{"title": "Rebrand", "number": 176, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/176", "body": "Recommended steps\ngit remote remove origin\ngit remote add origin \n./gradlew clean"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/176#pullrequestreview-866811264", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/176#pullrequestreview-866811641", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/176#pullrequestreview-866813758", "body": ""}
{"title": "Try something", "number": 1760, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1760"}
{"title": "Filter our current user in Create discussion", "number": 1761, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1761", "body": "Filters out the current user in create discussion\n\n\nNext steps: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1761#pullrequestreview-1006152487", "body": ""}
{"title": "Wait for repo status", "number": 1762, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1762", "body": "Running into issues where the repo objects from Vscode Git were not fully initialized. This sometimes made it look as if a repo had no remotes and triggered the project selector state.\nBased on this:  we need to explicitly wait on repo.status() to finish loading"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1762#pullrequestreview-1003369220", "body": ""}
{"comment": {"body": "If a single repo fails for whatever reason, would prefer if it didn't kill the entire array of promises.\r\nTherefore am catching each promise individually and logging errors.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1762#discussion_r894923898"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1762#pullrequestreview-1003389762", "body": ""}
{"title": "do not do unnecessary sts token queries", "number": 1763, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1763"}
{"title": "Revert \"Try something (#1760)\"", "number": 1764, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1764", "body": "This reverts commit fa566ab7f94a2b5abbd275b717384c796cc6c5ff.\nConflicts:\ngradle.properties\nprojects/models/src/main/kotlin/com/nextchaptersoftware/db/common/tomcat/ConnectionJdbcInterceptor.kt"}
{"title": "Update messaging", "number": 1765, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1765"}
{"title": "Fix bug in SourcePoint stopPropagation persistence", "number": 1766, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1766", "body": "fix bug in SourcePoint stopPropagation persistence\nalways show snippets in admin web, for debug\nimprove sourcemark stats"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1766#pullrequestreview-1003517088", "body": ""}
{"comment": {"body": "bug fix here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1766#discussion_r895094017"}}
{"title": "Ability to clear repo sourcepoints from admin web", "number": 1767, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1767", "body": "Useful when we improve the SM engine and want to clear and forcibly recalculate everything in a repo."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1767#pullrequestreview-1003517970", "body": ""}
{"comment": {"body": "just refactor", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1767#discussion_r895095459"}}
{"title": "Installations being returned without installURL", "number": 1768, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1768"}
{"comment": {"body": "What's this for?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1768#issuecomment-1153071502"}}
{"comment": {"body": "> What's this for?\r\n\r\nFor when we fail to find the orgId, use this as a fallback. In that case, just send them to the select org page. Like this:\r\n<img width=\"682\" alt=\"Screen Shot 2022-06-11 at 22 42 25\" src=\"https://user-images.githubusercontent.com/1798345/173217419-09ff26ca-91d2-42fa-aca8-cfdcdc8eb76f.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1768#issuecomment-1153078325"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1768#pullrequestreview-1003623802", "body": ""}
{"title": "Add AtMentionFramework to editor", "number": 1769, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769", "body": "Add AtMention support to MessageEditor.\nLike the ListEditor, we are using the canonical slatejs plugin pattern as described here:\n\nThis pr adds the following:\n1. A React zustand store (as there are a few state-based items we need to manage)\n2. Keyboard handlers for the div that we plan to use for managing @mention options in dropdown.\n3. OnChange handlers for the div that we are using to determine what user is trying to search for.\n\nTODO:\n1. Proper dropdown\n2. Plugging in team members\n3. Calling appropriates apis based off @mention selection."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1004658845", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005037402", "body": ""}
{"comment": {"body": "Is the idea that we will somehow bind to the team store data here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r896226323"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005037842", "body": ""}
{"comment": {"body": "Yes :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r896226625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005070884", "body": ""}
{"comment": {"body": "We've been moving away from Zustand for awhile... we can use this for now but I'd prefer to move to something else (a React context might make the most sense here actually).\r\n\r\nIf we move the dropdown to live alongside the element, some of this stuff might disappear as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r896252214"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005072771", "body": ""}
{"comment": {"body": "I'm not 100% sure what this is doing -- is it updating the dropdown location whenever the doc is edited?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r896253652"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005073677", "body": ""}
{"comment": {"body": "I would have imagined the existing element-dispatch functionality would handle this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r896254413"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005073928", "body": ""}
{"comment": {"body": "I always assumed that react contexts are more for global store sort of things.\r\nI can understand the aversion for its use in a non-react environment, but I don't see a huge problem with it in this context.\r\n\r\nPersonally, I like that it compartmentalizes related functionality and state.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r896254583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005074701", "body": ""}
{"comment": {"body": "A react context can be set at any part of the React tree, and only affects that part of the tree (ie, it's not global).  It might be a decent fit for this, as we want to share state in a single branch of the tree (the message editor)\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/e1cce9a2-53f6-45ba-9f48-90bb8ec3e6e7?message=750bc2d4-17c9-4671-ab5b-0bd15550b841).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r896255134"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005074759", "body": ""}
{"comment": {"body": "Combination of things:\r\n1. Analyzes input, and sets the target location in the editor when '@' is triggered.\r\n2. Sets the search body that we use to filter mention dropdown contents.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r896255173"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005074817", "body": ""}
{"comment": {"body": "Fair enough, I'll address this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r896255217"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1005076385", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#pullrequestreview-1022303121", "body": ""}
{"comment": {"body": "@dennispi Does this sound right?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/e1cce9a2-53f6-45ba-9f48-90bb8ec3e6e7?message=d159b748-be40-494b-86dc-29bb5f469933).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1769#discussion_r908873712"}}
{"title": "Update spec documentation", "number": 177, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/177", "body": "introduce tag groups for related resources. for example, the \"Conversations\" group\n   would contain the Chat, ChatMessage and ChatParticipants tags.\nrename tags\nadd overview, with conventions."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/177#pullrequestreview-868108537", "body": ""}
{"comment": {"body": "FYI. zally allows for custom rules. I.e. enforcing title case on tag names", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/177#discussion_r795847427"}}
{"title": "SourceMark log supression and TODO assignments", "number": 1770, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1770"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1770#pullrequestreview-1003771927", "body": ""}
{"title": "SourceMarks log debug/warn in assertion mode only", "number": 1771, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1771", "body": "Slight amendment to #1770, where we still log all the things, but only when assertion mode is enabled."}
{"title": "add refresh logging issue", "number": 1772, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772", "body": "There's a current theory that the logout issue may be caused by unexpected 401s.\nAdd additional logging and safeguards to debug situation."}
{"comment": {"body": "You just got double approved by Matt. this is fucking stellar!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#issuecomment-1154370152"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004608603", "body": ""}
{"comment": {"body": "Why console And not our logger?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#discussion_r895906103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004614019", "body": ""}
{"comment": {"body": "Since this store is shared across clients & the logger is *not* shared across clients, we can end up in a situation where the logger may not be initialized. \n\nBased on a comment above.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#discussion_r895909884"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004669753", "body": ""}
{"comment": {"body": "I think logging the token itself is probably trouble long-term, so we should remember to remove this eventually...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#discussion_r895950103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004669823", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004804976", "body": ""}
{"comment": {"body": "asfasfss\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/748e7011-e0d7-488a-99e9-afdb97196579?message=5a346ca0-322e-475a-b190-a92d66ba1aee).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#discussion_r896046474"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004805105", "body": ""}
{"comment": {"body": "asfdafsd\n\n\n\nasfasdafds\n\n\n\n- afsdsadfs\n- asdffas\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/748e7011-e0d7-488a-99e9-afdb97196579?message=18a99c05-803d-4cd4-8029-72ab3a801807).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#discussion_r896046581"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004811562", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/eed1d181-8872-4629-bb16-09284fb93528)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/748e7011-e0d7-488a-99e9-afdb97196579?message=4c353a8f-4ee8-4e78-a7bc-37ab8afb1b06).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#discussion_r896050139"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004811808", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/b6df525f-d992-4788-a84a-81c4de82d164)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/748e7011-e0d7-488a-99e9-afdb97196579?message=4c465470-e2a2-4b6d-af5f-14754a6f3fca).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#discussion_r896050281"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004859138", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004860873", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1772#pullrequestreview-1004861207", "body": "I just love this PR, can'tstopwon'tstop"}
{"title": "Don't show misleading \"discussion references a source code location that doesn't exist at your checked-out commit\" UI", "number": 1773, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1773", "body": "Fixes UNB-245 \nThe key problem is that we were potentially triggering the async operation to look up the sourcemark, and display the resulting text editor (or error UI) multiple times.  While the operation should have been idempotent, and does seem to be idempotent when run in the VSCode extension host, when run in non-extension-host VSCode, it throws an error, which results in the wrong behaviour.\nThe solution here is to hold onto the sourcemark resolution promise, so that we only run it once."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1773#pullrequestreview-1004703535", "body": ""}
{"comment": {"body": "If I'm reading this right, then it seems as though once this is constructed we would never re-construct it.\r\n\r\nHowever, we need a way to invalidate this as the file changes. For example, the file may change due to editor changes, filesystem changes, and Git commit changes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1773#discussion_r895974518"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1773#pullrequestreview-1004706543", "body": ""}
{"comment": {"body": "This is only used to launch the editor -- the in-editor stuff is handled by the TextEditorSourceMark code, so we only want to do this once.  I don't think invalidation matters.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1773#discussion_r895976723"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1773#pullrequestreview-1004720474", "body": ""}
{"comment": {"body": "Discussed IRL. The scenario where a developer changes branches while the Discussion View is open will need to be addressed in the future, likely via a subscription to a sourcemark event stream (in this case we have the sourcemark ID so we would likely subscribe to events that affect just that sourcemark).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1773#discussion_r895986735"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1773#pullrequestreview-1004720697", "body": "thanks!"}
{"title": "includeThreadsForMyOpenPrs for push channel should default to true if param not included", "number": 1774, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1774", "body": "The API endpoint defaults to true, so should this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1774#pullrequestreview-1004723010", "body": ""}
{"title": "Update slate", "number": 1775, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1775", "body": "Found some bugs with selections/transforms that were resolved when I updated to latest slatejs."}
{"title": "Login GH UI for VSCode", "number": 1776, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776", "body": "\nCurrently only supporting GH."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Only supporting ghProvider for short term.\r\nWhen we decide to support Bitbucket/etc... we can do the UI work then. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#discussion_r896093003"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#pullrequestreview-**********", "body": ""}
{"comment": {"body": "We should maybe put a comment here explaining what this is for (ie, an equivalent for `fill=\"currentColor\"` when you can't use that)...\r\n\r\nDoes this belong in shared components?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#discussion_r898242262"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#pullrequestreview-**********", "body": ""}
{"comment": {"body": "We should maybe put a comment here explaining what this is for (ie, an equivalent for `fill=\"currentColor\"` when you can't use that)...\r\n\r\nDoes this belong in shared components?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#discussion_r898242261"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#pullrequestreview-1007884905", "body": ""}
{"comment": {"body": "It does feel unfortunate that we have to pipe these images through the webview this way.  Ideally we'd just reference them directly in the webview?  I'll maybe think about how we can do that, it'd make this kind of thing a lot easier...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#discussion_r898245429"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#pullrequestreview-1007884907", "body": ""}
{"comment": {"body": "It does feel unfortunate that we have to pipe these images through the webview this way.  Ideally we'd just reference them directly in the webview?  I'll maybe think about how we can do that, it'd make this kind of thing a lot easier...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#discussion_r898245430"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#pullrequestreview-1007885126", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#pullrequestreview-1007990465", "body": ""}
{"comment": {"body": "I don't think this is used elsewhere? In other clients, we don't have a \"mask\" like icon.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1776#discussion_r898319550"}}
{"title": "Swap user allow list for org allow list", "number": 1777, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1777", "body": "All this does is prevent an org from being installed that is not in the allow list. If an org is subsequently removed from the allow list, it will have no impact. If we want to drop orgs and have their requests be rejected, we'll have to do a little extra during authorization.\nFixes UNB-251"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1777#pullrequestreview-1004880400", "body": "future enhancement would be to control via admin web, which avoids having to deploy."}
{"title": "Rename client headers to match versionInfo naming scheme", "number": 1778, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1778", "body": "This PR just renames the existing headers. New headers will be introduced in another PR:\nX-Unblocked-Product-Version\nX-Unblocked-Product-Number"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1778#pullrequestreview-1004918845", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1778#pullrequestreview-1004920185", "body": ""}
{"title": "add download assets bucket", "number": 1779, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1779", "body": "Adding a new bucket for download assets. This will also create a CloudFront endpoint for it"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1779#pullrequestreview-1004929596", "body": ""}
{"title": "update\\\\", "number": 178, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/178"}
{"title": "exclude the temp assets folder while I wire up a dedicated bucket", "number": 1780, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1780", "body": "This is to prevent dashboard deploys from deleting the installer file in the temp location under landing-page bucket."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1780#pullrequestreview-1004937930", "body": ""}
{"title": "Touch thread when message is deleted", "number": 1781, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781", "body": "Fixes Thread pusher channel not raised when a message is deleted\nAlso:\n- grooms ThreadUnreads on message deletion\n- checks that team member is the author of a thread before deleting it\n- does not allow deleting the first in a thread\nTODO \n"}
{"comment": {"body": "> TODO groom ThreadUnreads when a message is deleted during PR ingestion/sync\r\n\r\nAre you doing this in this PR, or follow-up?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#issuecomment-1157178100"}}
{"comment": {"body": "> > TODO groom ThreadUnreads when a message is deleted during PR ingestion/sync\r\n> \r\n> Are you doing this in this PR, or follow-up?\r\n\r\nThis PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#issuecomment-1157860247"}}
{"comment": {"body": "> > > TODO groom ThreadUnreads when a message is deleted during PR ingestion/sync\r\n> > \r\n> > \r\n> > Are you doing this in this PR, or follow-up?\r\n> \r\n> This PR\r\n\r\nthreadUnreadService.setLatestMessageAllThreadUnreads(threadId)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#issuecomment-1157943503"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#pullrequestreview-1008308205", "body": ""}
{"comment": {"body": "nice tests", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#discussion_r898543206"}}
{"comment": {"body": "Just want to clear I understand this. If a user deletes a message in GitHub, that happens to be the original message in the thread, then that message will not be deleted from Unblocked. Is my understanding correct?\r\n\r\nMight be unexpected. One alternative is to delete the entire thread, but that would be surprising if there are multiple messages in the thread.\r\n\r\nOr maybe delete the entire thread if the user deletes the _only_ message in the thread?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#discussion_r898546214"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#pullrequestreview-1008424353", "body": ""}
{"comment": {"body": "> Just want to clear I understand this. If a user deletes a message in GitHub, that happens to be the original message in the thread, then that message will not be deleted from Unblocked. Is my understanding correct?\r\n\r\nYes that's correct, and you're right it's inconsistent and probably unexpected. \r\n\r\nIt's a little tricky to handle because GitHub provides the ability to delete the first message in a thread. When that happens, the second message becomes the \"anchor\" message, whereas we don't allow deleting the first message in a thread in Unblocked.\r\n\r\nAs I write this, I'm thinking maybe we can do what you suggest and delete the entire thread, then recreate the thread but with the remaining messages.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#discussion_r898641915"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#pullrequestreview-1008424966", "body": ""}
{"comment": {"body": "Of course we want to preserve the thread unreads and thread participants...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#discussion_r898642366"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#pullrequestreview-1008425724", "body": ""}
{"comment": {"body": "Hmm. Maybe this is fine for now. This will happen at some point, but it's super rare.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9b6b02bf-9b0e-4a8f-9450-5beb6ecf3a5f?message=dd4c850a-ea5d-4fb7-91eb-0dcdddd36e3c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#discussion_r898642990"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#pullrequestreview-1009458203", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1781#pullrequestreview-1009485449", "body": ""}
{"title": "Ability to parse file statuses needed for rename detection", "number": 1782, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1782", "body": "Will be used in follow on PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1782#pullrequestreview-1004975053", "body": ""}
{"title": "Add X-Unblocked-Product-Number and X-Unblocked-Product-Version headers", "number": 1783, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1783"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1783#pullrequestreview-1005008765", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1783#pullrequestreview-1005009296", "body": ""}
{"title": "Setup product number for TS clients", "number": 1784, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1784", "body": "Sets up product numbers and versions for TS Clients.\nProduct Version not populated yet as we figure out semver strategy. Infrastructure for version should be ready to go so it just needs to be populated\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1784#pullrequestreview-1006141985", "body": ""}
{"comment": {"body": "is `npm_config_*` documented anywhere?  I can't find how this works", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1784#discussion_r897016302"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1784#pullrequestreview-1006143505", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1784#pullrequestreview-1006148465", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1784#pullrequestreview-1006148803", "body": ""}
{"comment": {"body": "This was very much a random stack overflow leading to https://www.cs.hmc.edu/~jrosenbloom/FrontEndDevelopmentProject/NodeJS/node_modules/npm/html/doc/misc/npm-config.html and some testing.\r\n\r\nDidn't find any reference on the official npm docs as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1784#discussion_r897020759"}}
{"title": "Add version info api stub", "number": 1785, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1785"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1785#pullrequestreview-1005053192", "body": ""}
{"title": "Loading states for web extension", "number": 1786, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1786", "body": "Added simple loading states to web extension.\nMinimum height for dialog.\n"}
{"comment": {"body": "Using GH accent color for spinner. let me know if we want to change.\r\n\r\n<img width=\"951\" alt=\"CleanShot 2022-06-13 at 15 44 57@2x\" src=\"https://user-images.githubusercontent.com/1553313/173460329-0e9a2776-77ff-48ee-aeac-bbdd27eaf0f0.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1786#issuecomment-1154525916"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1786#pullrequestreview-1006175658", "body": ""}
{"comment": {"body": "Is this not the same as the VSCode loading UI?  Should we move this into /shared ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1786#discussion_r897039085"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1786#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1786#pullrequestreview-**********", "body": ""}
{"comment": {"body": "It is. The tricky bit is that the loading component pulls in spinners that are client specific.\r\n\r\nUp to refactoring this post release.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1786#discussion_r897269343"}}
{"title": "Prefix token providers", "number": 1787, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1787", "body": "Prefix token providers in web and extension (already done in vscode) just in case this is the cause for invalid refresh auth bug."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1787#pullrequestreview-**********", "body": ""}
{"title": "Use environment hostname for token issuer", "number": 1788, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1788", "body": "Issuer is the same in all environments, which makes it hard to know when we've crossed wires."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1788#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1788#pullrequestreview-**********", "body": ""}
{"title": "SourceMarks should persist after file rename or move", "number": 1789, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1789", "body": "Before | After\n--|--\n | "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1789#pullrequestreview-1005202252", "body": ""}
{"title": "suspendingTest block should not return anything", "number": 179, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/179", "body": "My bad."}
{"comment": {"body": "Hear me now DAvid, it's not your bad...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/179#issuecomment-1025234501"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/179#pullrequestreview-867209353", "body": ""}
{"title": "Add context menu to vscode sidebar", "number": 1790, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1790", "body": ""}
{"comment": {"body": "Looks good!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1790#issuecomment-1155799173"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1790#pullrequestreview-1006137187", "body": ""}
{"comment": {"body": "Haven't look carefully at this yet but was wondering if you had looked into https://headlessui.dev/react/menu ?\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1790#discussion_r897013037"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1790#pullrequestreview-1006179809", "body": ""}
{"comment": {"body": "This component is only related to the right click interactions -- it looks like the headless Menu is the actual UI component wrapper with the items. I think they're separate (as in, the ContextMenu takes in a component to display, which could be the headless menu)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1790#discussion_r897042016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1790#pullrequestreview-1006679086", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1790#pullrequestreview-1006680249", "body": ""}
{"title": "Allow personal orgs", "number": 1791, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1791", "body": "Follow on from #1777"}
{"comment": {"body": "w00t. Ingest all the things!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1791#issuecomment-1154689138"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1791#pullrequestreview-1005202407", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1791#pullrequestreview-1005203726", "body": ""}
{"title": "Add VersionInfoStore", "number": 1792, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1792"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1792#pullrequestreview-1005224841", "body": ""}
{"comment": {"body": "Stub. Adding implementation in next PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1792#discussion_r896370764"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1792#pullrequestreview-1005259347", "body": ""}
{"title": "Fix asset auth after issuer changes", "number": 1793, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1793"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1793#pullrequestreview-1006177500", "body": ""}
{"title": "Add publish step for macos installer", "number": 1794, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1794", "body": "Added a release step to do the following:\n- Executes if branch is main and event name starts with release-\n- Gets the version string from tag name \n- Downloads and renames asset to the final marketing name \n- Generates md5sum for installer asset \n- Generates a metadata file \n- Uploads installer to both Dev and Prod download-assets bucket along with metadata file \nSample metadata json\n{\n\"build_number\": 144,\n\"version\": \"0.1.0\",\n\"commit_sha\": \"0e9d48b634fc681cc483da7568b41e0c4013f236\",\n\"checksum\": \"f27dd0181a646df87815b69de1a653ab\",\n\"file_name\": \"unblocked-installer-0.1.0-144.pkg\",\n\"platform\": \"macos\",\n\"release_date\": \"2022-06-14 18:22:17\"\n}\n"}
{"comment": {"body": "Suggestion: leverage installer filename as base for metadata filename:\r\n`unblocked-installer-0.1.0-144-metadata.json`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1794#issuecomment-1155586153"}}
{"comment": {"body": "Chatted with Peter about the metadata filename. For now we are keeping it as is to make it easier for build ingestion via Admin console. We might change in the future once we implement some sort of polling for discovering new releases. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1794#issuecomment-**********"}}
{"comment": {"body": "Also keeping full sha in the metadata file. Client's should be passing full SHA to `X-Unblocked-Product-SHA` but are currently not doing so. `/versionInfo` endpoint will throw an error if the header value is not a full sha", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1794#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1794#pullrequestreview-**********", "body": ""}
{"title": "Add Test Accounts", "number": 1795, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1795"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1795#pullrequestreview-**********", "body": ""}
{"comment": {"body": "this one doesn't seem to exist\r\nhttps://api.github.com/organizations/*********", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1795#discussion_r897077119"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1795#pullrequestreview-**********", "body": ""}
{"comment": {"body": "https://api.github.com/users/jeffUBTest", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1795#discussion_r897077510"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1795#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This one does exist:\r\nhttps://api.github.com/organizations/*********", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1795#discussion_r897077788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1795#pullrequestreview-1006230697", "body": ""}
{"comment": {"body": "oh, nvm, I see\r\nhttps://api.github.com/user/*********", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1795#discussion_r897078936"}}
{"title": "Debug org installation", "number": 1796, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1796", "body": "Should revert this after we've figure it out."}
{"title": "Hack to display sourcemark gutter icon during onboarding", "number": 1797, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1797"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1797#pullrequestreview-1006300140", "body": ""}
{"title": "Add identityId back to mdc as we need it for diagnosing stuff", "number": 1798, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1798", "body": "Ktor 2.0.2 has the fix to the bug I submitted, so we should be able to do identityId queries now in mdc. :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1798#pullrequestreview-1006303216", "body": "sweet"}
{"title": "Add PR Threads to web extension", "number": 1799, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1799", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1799#pullrequestreview-1006325031", "body": ""}
{"title": "Zustand Implementation", "number": 18, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18", "body": "Testing out different state management libraries.\nUpdated AuthStore to use Zustand."}
{"comment": {"body": "How we would approach splitting up the store into different features. This would allow for different feature to interact with each other. \r\n\r\nFor example, if getting messages was dependent on product.\r\n\r\nhttps://github.com/pmndrs/zustand/wiki/Splitting-the-store-into-separate-slices\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#issuecomment-1005083210"}}
{"comment": {"body": "https://immerjs.github.io/immer/\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#issuecomment-1005125465"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-845863682", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-845914779", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-845980385", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-845981555", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-845984712", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-845987927", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846004469", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846005541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846009576", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846013001", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846013404", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846016514", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846055697", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846056530", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846070562", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/18#pullrequestreview-846195395", "body": ""}
{"title": "Add putChat and putMessage API stubs", "number": 180, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180", "body": "Just creating the stubs here. Will wire up the database calls in the next PR."}
{"comment": {"body": "Redoing this in another PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#issuecomment-1026243999"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#pullrequestreview-867422788", "body": ""}
{"comment": {"body": "This requires a chat object, so need to figure out how to express that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#discussion_r795369072"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#pullrequestreview-867424268", "body": ""}
{"comment": {"body": "Chat ID is not enough?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#discussion_r795371308"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#pullrequestreview-867424421", "body": ""}
{"comment": {"body": "(of course, that assumes chat already exists)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#discussion_r795371567"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#pullrequestreview-868187168", "body": ""}
{"comment": {"body": "I'm guessing you're thinking about the first message creation flow, before a chat exists? Off the top of my head, some options:\r\n\r\n1) Have a `Chat` object instead of just its ID as a property of the `Message`. Means we need to grab the chat object everytime we return messages (seems like overkill, kind of gross) but this way clients can pass along a chat object when creating a message for the first time.\r\n2) Have a `MessageCreationRequest` object for request body for the put operation that has a Chat object, so that every time a client creates or updates a message it includes the `Chat` object. If that `Chat` doesn't exist, we'll create it before creating the `Message`. But then we do we go down the rabbit hole of including the `SourceMark` in the `Chat` object?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#discussion_r795899851"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#pullrequestreview-868192168", "body": ""}
{"comment": {"body": "I also don't think it's a huge problem for clients to first create the chat, then create the first message for the chat.  You'd only be doing that for the first message, and depending on how the UI flow works, you may not even make them at the same time (ie, by the time you submit a first message, the chat will already be made)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#discussion_r795903519"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#pullrequestreview-868221958", "body": ""}
{"comment": {"body": "There are a bunch of objects needed:\r\n- message\r\n- Chat\r\n- Sourcemark\r\n- Sourcepoint\r\n\r\nCan certainly do this by making multiple requests (and maybe that\u2019s fine for now, optimizing later) but there a lot of back-and-forth requests.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#discussion_r795925358"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#pullrequestreview-868224412", "body": ""}
{"comment": {"body": "Also, for the period of time that a chat exists with no messages, what would other clients reading that chat show? Would appear to be a glitch.\r\n\r\nAlternatively, we can transactionally all models on service, making glitches impossible.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#discussion_r795926830"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#pullrequestreview-868230185", "body": ""}
{"comment": {"body": "We would be committing the entire annotation as a single object, then?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#discussion_r795930900"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#pullrequestreview-868231608", "body": ""}
{"comment": {"body": "I think a createChat, with { message, sourcePoint } as payload would work.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/180#discussion_r795931896"}}
{"title": "Supply correct before value", "number": 1800, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1800"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1800#pullrequestreview-1006495937", "body": ""}
{"title": "clean up stuff", "number": 1801, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1801"}
{"title": "Use full commit SHA in TS projects", "number": 1802, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1802", "body": "This is what we use in the API request headers."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1802#pullrequestreview-1006497060", "body": ""}
{"title": "Add download assets cf endpoint", "number": 1803, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1803", "body": "Adding a new behaviour to CloudFront for download-assets site bucket. This is the path we would use for Client installer downloads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1803#pullrequestreview-1006456032", "body": ""}
{"title": "Fix for personal org installation", "number": 1804, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804#pullrequestreview-1006496321", "body": ""}
{"comment": {"body": "Fix: Personal orgs don't have members.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804#discussion_r897267940"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804#pullrequestreview-1006507080", "body": ""}
{"comment": {"body": "fascinating. I would have assumed it would be a list of size 1", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804#discussion_r897275261"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804#pullrequestreview-1006507932", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804#pullrequestreview-1006540228", "body": ""}
{"comment": {"body": "hmm, I think this is going to fail when we attempt to fetch team members...\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/16b9faa3-205e-4c34-84e2-eff0fe1813ff?message=83fcc301-47bc-41b6-bdd9-fd85bb832767).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804#discussion_r897296917"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804#pullrequestreview-1006540369", "body": ""}
{"comment": {"body": "will need another fix\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/16b9faa3-205e-4c34-84e2-eff0fe1813ff?message=647cde70-0be0-4904-88f5-6a9471f1b249).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1804#discussion_r897297018"}}
{"title": "Upsert ThreadParticipant and ThreadUnread for @mentions in GitHub comment bodies", "number": 1805, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805", "body": "When someone @mentions a person in a pull request comment in GitHub, Unblocked should check to see if that person exists in Unblocked and upsert a ThreadParticipant and ThreadUnread model."}
{"comment": {"body": "> TODO Create ThreadUnreads for new thread participants\r\n\r\nIs there still more to come for this PR, or is it done?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#issuecomment-1157177583"}}
{"comment": {"body": "@richiebres \r\n\r\n> > TODO Create ThreadUnreads for new thread participants\r\n> \r\n> Is there still more to come for this PR, or is it done?\r\n\r\nDone here https://github.com/NextChapterSoftware/unblocked/pull/1805/commits/d9ee46253cb975696e16d3cc30ad330606a0b5e8", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#issuecomment-1167955858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#pullrequestreview-1008257241", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#pullrequestreview-1020831281", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#pullrequestreview-1022133529", "body": ""}
{"comment": {"body": "FYI, we already have a ThreadParticipantService in \"lib-api\" that does this logic.\r\n\r\nLook at ThreadParticipantService.createParticipantsForThread.\r\n\r\nIt does the unread logic as well implicitly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#discussion_r908754343"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#pullrequestreview-1022536684", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#pullrequestreview-1022553202", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#pullrequestreview-1022553207", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#pullrequestreview-1024059120", "body": ""}
{"comment": {"body": "```suggestion\r\n        this is a multi line suggestion\r\n        asdfsdfa asdfasdf\r\n```\r\n\r\nIgnore", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#discussion_r910447832"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#pullrequestreview-1027778129", "body": ""}
{"comment": {"body": "Going to spend some time thinking about how to refactor this https://linear.app/unblocked/issue/UNB-379/refactor-threadparticipantservice-to-share-between-api-service-and-pr", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1805#discussion_r913161675"}}
{"title": "Version service class implementation", "number": 1806, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1806", "body": "Also in this PR:\n- change productSha db column type from text to sha1\n- pass Hash values around instead of strings for type verification"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1806#pullrequestreview-1008432676", "body": ""}
{"title": "changed the condition to only execute on tags", "number": 1807, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1807"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1807#pullrequestreview-1006595182", "body": ""}
{"title": "Rename ClientType to AgentType and add new types", "number": 1808, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808", "body": "Summary\n\nrename ClientType to AgentType\nadd X-Unblocked-Product-Sha and X-Unblocked-Product-Agent header definitions to spec\nsend back full git sha from clients\nchanged versions endpoint to be unauthed to support getunblocked.com/download\nchanged versionInfo to versionInfo/latest to support other types of queries (which will be authed)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1006631226", "body": ""}
{"comment": {"body": "This unfortunately can't directly be of type `AgentType` because the generator doesn't create `String -> Enum Class` conversion code for headers", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#discussion_r897358930"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1006631955", "body": ""}
{"comment": {"body": "I refactored the `VersionInfo` api response to contain the agent, and then the `GetVersionInfoResponse` object can just be an array of `VersionInfo` objects", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#discussion_r897359414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1006632420", "body": ""}
{"comment": {"body": "This field needed to be top level. Doesn't make sense as part of a `VersionInfo` object, which describes a particular version instance", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#discussion_r897359745"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1006682023", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1006684645", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1006706219", "body": ""}
{"comment": {"body": "This will apply for both chrome + safari atm.\r\nI'll look into differentiating between the two...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#discussion_r897415621"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1006706291", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1006836787", "body": ""}
{"comment": {"body": "Yup that's the idea. Let's do that in a followup PR. Also - unblocked editor needs emojis :) \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/7be391c1-9546-41e4-997d-600caae3ba8a?message=f58431a6-10bb-46ae-96a7-c109f28ef085).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#discussion_r897512574"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1006885674", "body": ""}
{"comment": {"body": "Unblocked editor has emojis... kind of! \ud83d\ude1d\ud83d\ude1d\ud83d\ude1d\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/7be391c1-9546-41e4-997d-600caae3ba8a?message=55b1d542-d518-4e3e-8682-fa0ac49ecac2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#discussion_r897550103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1008329312", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1808#pullrequestreview-1008352024", "body": ""}
{"title": "Add Pulumi to allow list in prod.conf", "number": 1809, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1809"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1809#pullrequestreview-1006634449", "body": ""}
{"comment": {"body": "Can someone confirm this is the right ID?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1809#discussion_r897361285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1809#pullrequestreview-1006644647", "body": ""}
{"comment": {"body": "ID is correct, see:\r\nhttps://api.github.com/user/21992475", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1809#discussion_r897368618"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1809#pullrequestreview-1006645705", "body": "We already have a pattern for overriding by env; why don't we just overwrite the allowedOrgs array in prod?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1809#pullrequestreview-1006647816", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1809#pullrequestreview-**********", "body": ""}
{"title": "Deploy prod environment infra", "number": 181, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/181", "body": "Added prod EKS cluster config\nAdded prod CDK config\nChanged network route creation in network related stack to address a bug in CDK. The bug was causing public subnet list to include all subnets not just public ones.\nBoth EKS and CDK changes have been deployed\nAll VPN routes have been setup\nConfigured and deployed Postgres service account\nUpdated default deny rule in k8s to allow all egress by default (except the VPN range)\nAll changes have been deployed and prod environment is operational"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/181#pullrequestreview-*********", "body": ""}
{"title": "Update unresolved sourcemark UI", "number": 1810, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810", "body": "Fixes UNB-213 \n\nWhen we can't resolve a sourcemark location in VSCode, show an updated UI\nOffer to jump to GitHub\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-**********", "body": ""}
{"comment": {"body": "No idea if this is reasonable or not, but it works for now.  I'm open to other ideas.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897383921"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-**********", "body": ""}
{"comment": {"body": "We could probably consolidate this into a new mixin (i.e. `flex-column-center`)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897406047"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006696706", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006699294", "body": ""}
{"comment": {"body": "The base url part should be constructed by the service as `httpUrl`, and we can expose that on the Repo API model. I can do this in follow up if you like.\r\n\r\nIt would be `https://github.com/${repo.ownerName}/${repo.repoName}` in this case.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897410001"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006700019", "body": ""}
{"comment": {"body": "hardcoded GitHub is not ideal.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897410624"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006700139", "body": ""}
{"comment": {"body": "The httpUrl is already in the repo model...\n\n\n\n```\n\n```\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b22d9ae5-b2f6-4506-8e24-b5fb8b94bea4?message=a56863ab-5944-4dee-bb82-f3a3029c459f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897410771"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006700581", "body": ""}
{"comment": {"body": "Well that didn't work.\r\n\r\nThere is already an httpUrl on the repo model:\r\n\r\n```\r\n            Canonical HTTP url of the repo in the SCM, like `https://github.com/org/repo.git`.\r\n```\r\n\r\nWould this work?  The '.git' at the end made me think it might not...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897411112"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This whole UI will need to be redone when we support other providers.  The URL construction is also very GH-specific, for example.  There is a lot of this kind of thing around the UI...\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/56f9f0e8-6cf7-4576-a140-ed48c8243cc7?message=2b9fc11f-d81d-4748-a084-e86335984b01).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897412761"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Ugh, sorry I meant `htmlUrl`.\n\n\n\nYes, for now, you could take the `httpUrl` and drop the last `.git` part, but I'd rather provide an explicit `htmlUrl`.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b22d9ae5-b2f6-4506-8e24-b5fb8b94bea4?message=fad2569c-88ca-4954-befe-bc6a6e654a21).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897421061"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006713408", "body": ""}
{"comment": {"body": "OK.  I'll get this in for now and we'll do that as a followup.  I believe we could probably use that in other places too.\n\n\n\nIt might be worth thinking about whether we want to vend things like this through other mechanisms as well.  Piecing together URLs client-side never feels great.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b22d9ae5-b2f6-4506-8e24-b5fb8b94bea4?message=7bb2bd14-1984-494f-aa34-23eab14f98d2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897421458"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006716530", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006718324", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006720267", "body": ""}
{"comment": {"body": "Agree\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b22d9ae5-b2f6-4506-8e24-b5fb8b94bea4?message=1b6d4f90-05a0-4744-8aa2-a2e6008f36de).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897426754"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006894014", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/1817", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1810#discussion_r897556379"}}
{"title": "Plug-in team members", "number": 1811, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811", "body": "This pr plugs in team member information into message editor.\nIn order to do this: \n1. We modify vscode webview contexts such that they include TeamMembers.\n2. Add a TeamMember context store that we can reference (we will need this for web && web extension)"}
{"comment": {"body": "Dearest @matthewjamesadam let me know if this looks okay to you.\r\nMy goal is to make you happy.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811#issuecomment-1156724016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811#pullrequestreview-1007957184", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811#pullrequestreview-1007958579", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811#pullrequestreview-1007963237", "body": ""}
{"comment": {"body": "Ahh it's unfortunate we're putting domain-specific (team) stuff into this class here.  I don't have any better suggestion on how to deal with this right now though...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811#discussion_r898300505"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811#pullrequestreview-1007965245", "body": "A couple small suggestions, but looks good."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811#pullrequestreview-1007972656", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811#pullrequestreview-1007973026", "body": ""}
{"comment": {"body": "Agreed as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1811#discussion_r898307102"}}
{"title": "Word smithing onboarding", "number": 1812, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812", "body": "This is mostly wording changes, but @kaych & I also eliminated one of the steps in the onboarding workflow (by combining similar messaging)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006684832", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006685363", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006686871", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006688521", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006689113", "body": ""}
{"comment": {"body": "@kaych asked me about this earlier -- we'll eventually need a better way of determining what is an editable file, but this is fine for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#discussion_r897402004"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006695323", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006698303", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006698770", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006705082", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006750874", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006752372", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006764055", "body": ""}
{"comment": {"body": "nit to use `&mdash;` for all the places we have dashes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#discussion_r897460653"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006764535", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006765214", "body": ""}
{"comment": {"body": "`Now, ...` to match https://github.com/NextChapterSoftware/unblocked/pull/1812/files#diff-808fa3ea85439f3b501b0cefd3af0cfb125b07d8e6c9a7c57a6a76b26be3c50dR157 ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#discussion_r897461494"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006831899", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006834393", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006836500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006837300", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1812#pullrequestreview-1006874280", "body": ""}
{"title": "Fix issue with source mark rendering", "number": 1813, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813", "body": "Concurrently fetches source marks for both latest & current commit hash.\nAlso fixes some rendering issues with the popup when in the first 5 lines.\nTest case: \n"}
{"comment": {"body": "Currently *not* using file hash. We can potentially add that if necessary but the current setup with hashes seems to work fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#issuecomment-1155830817"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1006712207", "body": ""}
{"comment": {"body": "What do you mean by 'hides under GH's UI'? Can you post a screenshot? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#discussion_r897420417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1006712594", "body": ""}
{"comment": {"body": "And does the z-index not do anything? Seems to me that it's either a z-indexing issue or not, so remove line 54 if not? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#discussion_r897420728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1006713990", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1006719580", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1006855262", "body": ""}
{"comment": {"body": "Everything here is necessary.\r\n\r\nWithout Z index.\r\n<img width=\"766\" alt=\"CleanShot 2022-06-14 at 21 38 12@2x\" src=\"https://user-images.githubusercontent.com/1553313/173737852-aa7fb867-90bf-4f1e-8993-7249d73c175a.png\">\r\n\r\nAnd without moving to bottom-end, it would appear under \r\n<img width=\"366\" alt=\"CleanShot 2022-06-14 at 21 38 54@2x\" src=\"https://user-images.githubusercontent.com/1553313/173737892-dc97982a-ca6e-4226-98e2-25a43c2ded1f.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#discussion_r897526563"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1006855444", "body": ""}
{"comment": {"body": "It's both.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#discussion_r897526702"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1006865862", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1006872906", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1006889685", "body": ""}
{"comment": {"body": "Guess I'm confused as to why the upped z-index still renders the component under the header(?) Does the z-index need to be higher? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#discussion_r897553234"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1007751458", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1007766196", "body": ""}
{"comment": {"body": "Doesn't need to be higher. It's due to how the rest of GH is rendering their UI. They have a bunch of relative positioning that makes the layering non-standard.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#discussion_r898163166"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1007769095", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1007870333", "body": ""}
{"comment": {"body": "I think all of the upstream streams `remember`, so this may not be necessary?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#discussion_r898234481"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1007873188", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#pullrequestreview-1007894490", "body": ""}
{"comment": {"body": "That was my original thought but it didn't quite workout. \r\nSubsequent listeners to the stream after initiation didn't receive events until I added this remember()", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1813#discussion_r898252130"}}
{"title": "Style mentions dropdown component", "number": 1814, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814", "body": "\n"}
{"comment": {"body": "Added user styling \r\n<img width=\"359\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173942831-cdd04520-d931-4533-9641-4997deeff3ed.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#issuecomment-1157013267"}}
{"comment": {"body": "Getting this change in, we can work on it as we go.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#issuecomment-1157108157"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#pullrequestreview-1008323541", "body": ""}
{"comment": {"body": "From what I'm reading, it looks like we now essentially have two components:\r\n* A dropdown popover, containing `DropdownItem`s -- these are buttons/links/divs, with no keyboard selection/handling\r\n* A dropdown menu, containing `MenuItem`s -- these are divs with arbitrary content, with some automatic keyboard/selection functionality\r\n\r\nI wonder if these should just be two separate components?  Or, alternatively, join `DropdownItem` and `MenuItem` into one class?\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#discussion_r898560903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#pullrequestreview-1008327421", "body": ""}
{"comment": {"body": "I think what we're doing here with the keybaord/ref handling (passing the editor dom element to the dropdown, and letting it bind directly to its keyboard events) is not what Slate is really expecting.\r\n\r\nWould this work?\r\n* Use the same ref-forwarding trick we use in MessageEditor (to publish its clear/focus API), so that `MentionDropdown` can publish a \"handle keyboard\" API\r\n* The `HandleKeyDown` in MessageEditor can call this, to forward keyboard events\r\n?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#discussion_r898564165"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#pullrequestreview-1008328554", "body": ""}
{"comment": {"body": "Is there a reason we don't handle Enter here as well, so all the keyboard input is in one place?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#discussion_r898565054"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#pullrequestreview-1008357903", "body": ""}
{"comment": {"body": "Temporary stub until we define the proto for mentions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#discussion_r898588272"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#pullrequestreview-1009313368", "body": ""}
{"comment": {"body": "this line should be temporary \u2014 ie we should always be trying to insert a white space after a mention; we should revisit here once the inline element logic is figured out", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1814#discussion_r899265126"}}
{"title": "Fix for repo url with dot character", "number": 1815, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815", "body": "Will figure out what all the valid special characters are in follow up..."}
{"comment": {"body": "We need a comment in here that describes how these rules are derived. I'm lost", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#issuecomment-1155954680"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#pullrequestreview-1006808726", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#pullrequestreview-1006835491", "body": ""}
{"comment": {"body": "I did some research...\r\n\r\n# Repo Name Part\r\nThe list of valid repo name characters is:\r\n```\r\nA-Za-z0-9-_.\r\n```\r\nWhich is equivalent to:\r\n```\r\n\\w-_.\r\n```\r\n\r\nAlso:\r\n- repo names cannot end in `.git`.\r\n- repo names cannot be `.`\r\n- repo names cannot be `..`\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#discussion_r897511610"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#pullrequestreview-1006835841", "body": ""}
{"comment": {"body": "`~` was previously in our allowed set, but that's not allowed by GitHub so I removed it. It's still valid for the Org Name part of the url.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#discussion_r897511882"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#pullrequestreview-1006868965", "body": ""}
{"comment": {"body": "<img width=\"910\" alt=\"CleanShot 2022-06-14 at 22 02 57@2x\" src=\"https://user-images.githubusercontent.com/1553313/173740429-829024bd-3c16-46b3-ae07-87c5ef1d9933.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#discussion_r897537420"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#pullrequestreview-1007806384", "body": ""}
{"comment": {"body": "Another special repo is your username\r\n\r\n<img width=\"806\" alt=\"Screen Shot 2022-06-15 at 09 31 04\" src=\"https://user-images.githubusercontent.com/1798345/173878881-a31b8e74-cde3-4dfa-b9b9-84496c0252d7.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1815#discussion_r898191129"}}
{"title": "Adds resiliency to team ingestion", "number": 1816, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1816"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1816#pullrequestreview-1006863969", "body": ""}
{"comment": {"body": "It's really only repo url parsing that can fail, so pre-parsing here to catch. The duplication is not ideal. One alternative is to create a new data-type that contains the parsed url and pass to batchInsert block. \r\n\r\nAny alternative ideas? Maybe a `mapNotNull` + `runCatching` + `repo.copy()`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1816#discussion_r897533351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1816#pullrequestreview-1006864491", "body": ""}
{"comment": {"body": "ok to do parsing antics here because we're not in a batch statement", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1816#discussion_r897533766"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1816#pullrequestreview-1008443404", "body": ""}
{"comment": {"body": "nice test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1816#discussion_r898657029"}}
{"title": "Expose and use Repo web url", "number": 1817, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1817", "body": "Follow on from https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-**********"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1817#pullrequestreview-1006894332", "body": ""}
{"title": "Remove CodeBlock max-height", "number": 1818, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1818", "body": "Fixes UNB-115 \nRemove the minimum height on VSCode code blocks.  Code blocks render at full content height.\nTo be honest, I am not sure this is the right thing to do.  I will hold off on merging this until I've demoed it a bit.  The 'Start Discussion' UI in particular is pretty confusing with this."}
{"comment": {"body": "We're not doing this.  I'll update the linear issue with the new plan.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1818#issuecomment-1163597524"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1818#pullrequestreview-1011550876", "body": ""}
{"title": "Add logging to try to understand why eTag is constantly changing", "number": 1819, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1819", "body": "We're burning through rate limits syncing PRs from repos when no PRs have updated because the eTag keeps changing and I don't know why"}
{"title": "Add encoded github key to config", "number": 182, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/182"}
{"comment": {"body": "What are the steps to add this? Which app is it for?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/182#issuecomment-1026007367"}}
{"comment": {"body": "> What are the steps to add this?\r\n\r\nLook at secrets folder.\r\nReadme is there.\r\n\r\nPlanning on adding documentation for uploading to kube as well (which is fucking easy...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/182#issuecomment-1026008467"}}
{"comment": {"body": "> What are the steps to add this? Which app is it for? \r\n\r\nGitHub App\r\n\r\nSee README.md\r\n\r\n1. unpack secrets `make setup-local-env`\r\n2. edit secrets from `~/.secrets/unblocked/secrets.conf`\r\n3. `make encrypt-secrets`\r\n4. commit", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/182#issuecomment-1026009117"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/182#pullrequestreview-868158138", "body": ""}
{"title": "Fix border radius of unread marker on dashboard", "number": 1820, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1820", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1820#pullrequestreview-1007837630", "body": ""}
{"title": "Whitelist Apple UB", "number": 1821, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1821"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1821#pullrequestreview-1007845432", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1821#pullrequestreview-1007849258", "body": ""}
{"title": "trying to stop installer builds from running on every Pr", "number": 1822, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1822", "body": "I will be force merging this one and won't wait for a whole ton of builds to finish"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1822#pullrequestreview-1007846495", "body": "I was about to ask about this "}
{"title": "Allow list TheUnblockedDemo org and dennispiUB demo user", "number": 1823, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1823"}
{"title": "Add in-memory eTag cache", "number": 1824, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824", "body": "Please forgive me for my sins."}
{"comment": {"body": "Test?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#issuecomment-1156757886"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1007898036", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1008347872", "body": ""}
{"comment": {"body": "FYI, we are now using cache4k for a few cache related stuff.\r\n\r\nLook at CachedPropertyDelegate for one usage of it. :)\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#discussion_r898581048"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1008380774", "body": ""}
{"comment": {"body": " \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/84eaf2d9-9bae-46a4-a8c4-689d590fcfaa?message=619770cd-cdae-467a-8f64-6c81f2bd9c4e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#discussion_r898606864"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1009444279", "body": ""}
{"comment": {"body": " \n\nWhat is this about???\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/84eaf2d9-9bae-46a4-a8c4-689d590fcfaa?message=2e5f7628-2dd5-44bd-bd4f-9b937644633a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#discussion_r899356281"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1009448153", "body": ""}
{"comment": {"body": "safasd  asdfadsasdf\n\n\n\n\n\nWhat about \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/84eaf2d9-9bae-46a4-a8c4-689d590fcfaa?message=390418b4-62eb-4eda-8725-528ae6b3ac18).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#discussion_r899358938"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1012688581", "body": ""}
{"comment": {"body": "@matthewjamesadam@mahdi-torabi@pwerry@kaych\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/84eaf2d9-9bae-46a4-a8c4-689d590fcfaa?message=81605e4e-97cd-44d1-b54c-c4b7c3a88de8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#discussion_r901957586"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1012688647", "body": ""}
{"comment": {"body": "asdffdsdas\n\n\n\n**ASDFsdafafsd**@mahdi-torabi@pwerry@rasharab\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/84eaf2d9-9bae-46a4-a8c4-689d590fcfaa?message=07db7809-8d50-4733-81dd-5ca54c9048e9).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#discussion_r901957655"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1012689244", "body": ""}
{"comment": {"body": "@matthewjamesadam is Lovely @matthewjamesadam\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/84eaf2d9-9bae-46a4-a8c4-689d590fcfaa?message=fa7187d2-a819-4d56-ae0f-9b5d45406f68).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#discussion_r901958070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1012689246", "body": ""}
{"comment": {"body": "@mahdi-torabi@matthewjamesadam\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/84eaf2d9-9bae-46a4-a8c4-689d590fcfaa?message=a719a224-7923-441c-86da-2354266204ca).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#discussion_r901958071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#pullrequestreview-1015820419", "body": ""}
{"comment": {"body": "help me!\n\n\n\nypadt eme\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/616c7865-dc1b-4990-a449-6a8a07c86ef4?message=e6d36968-8c71-48d6-bab4-075d86874f5a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1824#discussion_r904189204"}}
{"title": "Revert \"Whitelist Apple UB\"", "number": 1825, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1825", "body": "Reverts NextChapterSoftware/unblocked#1821\nNo need for this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1825#pullrequestreview-1007903694", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1825#pullrequestreview-1007920818", "body": ""}
{"title": "Recommendations hack must include TheUnblockedDemo org", "number": 1826, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1826"}
{"title": "Login UI for web extension", "number": 1827, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-1007993798", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-1008310658", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-1008311287", "body": ""}
{"comment": {"body": "Do we want our other web extension UIs (the thread modal, notably) to use Sofia too?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#discussion_r898546926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-1009645004", "body": ""}
{"comment": {"body": "Updated to other containers to use sofia pro", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#discussion_r899489652"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-1009649377", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-1009649589", "body": ""}
{"comment": {"body": "<img width=\"1594\" alt=\"CleanShot 2022-06-16 at 13 15 46@2x\" src=\"https://user-images.githubusercontent.com/1553313/174157431-7e420e27-94bb-4c44-bccb-b1866f436d1f.png\">\r\n<img width=\"547\" alt=\"CleanShot 2022-06-16 at 13 17 24@2x\" src=\"https://user-images.githubusercontent.com/1553313/174157436-f0117857-0500-4b69-96fd-0dbe936ba69e.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#discussion_r899493055"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Not necessarily for this PR but can we also add this button text to the web login UI?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#discussion_r899502714"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I suspect we'll need to do some string massaging so that `Provider.github` --> `GitHub` etc\r\n\r\nI think in BB or SW we had a displayName property on the api model but for now we could just do a client-side switch to output a nice string ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#discussion_r899503761"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Uhh we should double check with @benedict-jw to see whether or not this is what we actually want to do. Specifically for the modals I had thought we wanted to make it look like built-in GH UI, not necessarily UB-branded UI ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#discussion_r899510414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Until we get an explicit go from Ben or Dennis I'd err on the side of caution and not add Sofia Pro to all the views, only the Login UI for now (or just everything in our sidebar) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#discussion_r899519110"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-1009951063", "body": ""}
{"comment": {"body": "I think I agree with this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#discussion_r899726193"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-1010054178", "body": ""}
{"comment": {"body": "Removed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#discussion_r899803615"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1827#pullrequestreview-1010748495", "body": ""}
{"title": "Convert inline html image to inline element", "number": 1828, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1828"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1828#pullrequestreview-1008029952", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1828#pullrequestreview-1008031138", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1828#pullrequestreview-1008032822", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1828#pullrequestreview-1008033146", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1828#pullrequestreview-1008120871", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1828#pullrequestreview-1008199790", "body": ""}
{"title": "Extra safety", "number": 1829, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1829", "body": "Error case should be caught first.\nCheck if response model exists for safety. (e.g. could be undefined)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1829#pullrequestreview-1008004296", "body": ""}
{"title": "Fix ErrorResponse typo", "number": 183, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/183"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/183#pullrequestreview-868192723", "body": ""}
{"title": "Cleanup Org Allowlist Config", "number": 1830, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1830", "body": "Tried to get array inheritance working, but couldn't.\nRemoving the personal orgs for now, because it makes no sense to ingest personal orgs. There is only one user, they don't work anyway."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1830#pullrequestreview-1008025025", "body": ""}
{"title": "Capture thread view events", "number": 1831, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831", "body": "Goal here is to capture events from the clients so that we can answer from :\n- What is an \"active user\"?\n    - Participated in a thread per day, week, month, for any of our clients\n        - Participation: Created, Replied to, or Viewed a thread"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1008098303", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1008184105", "body": ""}
{"comment": {"body": "I think this is fine as is. We may even re-purpose this for implementing read receipts (in which case this would be under the `Threads` tag).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r898452587"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1008190710", "body": ""}
{"comment": {"body": "Yeah I wondered if this is basically the same thing as the \"mark as read\" call, and whether that was worth joining together...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r898456929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1008192358", "body": ""}
{"comment": {"body": "IIRC \"mark as read\" operation can also be used to mark a thread as unread", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r898457952"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1008192524", "body": ""}
{"comment": {"body": "It's not the same, because markAsRead is only called if unread.\n\n\n\nThis API is called either way, but only if not called on any thread in the last N hours.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6672769f-4bf2-4451-957b-d700653f1202?message=57f1aa14-8e78-4f75-85b4-59f40919e566).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r898458076"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1008193336", "body": ""}
{"comment": {"body": "I'm going to rename this tag to `Events` if no objections?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r898458650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1008193839", "body": ""}
{"comment": {"body": "\ud83d\udc4d\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6672769f-4bf2-4451-957b-d700653f1202?message=66d8adf9-c05d-4930-835b-8683ac5ec8cc).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r898459019"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1008194180", "body": ""}
{"comment": {"body": "Right, I know they're different APIs today, it just feels odd that we have two APIs for things that are very similar\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6672769f-4bf2-4451-957b-d700653f1202?message=96c1cdb7-3d5b-414e-97aa-9edae929d6e2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r898459284"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1008254961", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1009553089", "body": ""}
{"comment": {"body": "If we're not going to have dedicated \"metrics\" APIs quite yet, would it make sense to update this API to include the capabilities of `updateThreadUnread` and remove that API entirely?\r\n\r\nI'm okay with either approach for what its worth.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r899427014"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1009557642", "body": ""}
{"comment": {"body": "The calling semantics are different:\n\n- client MUST call `updateThreadUnread` whenever a thread is read/unread by a user\n\n- client SHOULD call `viewThread` whenever a thread is \"opened\" by a user, but at most once per day (or hour or whatever)\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6672769f-4bf2-4451-957b-d700653f1202?message=03b71b07-553d-441a-84e4-b9efb6c3cd3a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r899430278"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1009746407", "body": ""}
{"comment": {"body": "> client SHOULD call viewThread whenever a thread is \"opened\" by a user, but at most once per day (or hour or whatever)\r\n\r\nIs the only reason we're not calling this every time a use opens a thread to reduce number of requests?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r899564316"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#pullrequestreview-1009845275", "body": ""}
{"comment": {"body": "yes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1831#discussion_r899641888"}}
{"title": "Implements Versions API", "number": 1832, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1832"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1832#pullrequestreview-1008434117", "body": ""}
{"comment": {"body": "Shame that Exposed does not support this on the DB server-side:\r\nhttps://github.com/JetBrains/Exposed/issues/500", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1832#discussion_r898652542"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1832#pullrequestreview-1009148699", "body": ""}
{"comment": {"body": "Dave and I had this exact thought yesterday. Probably functionality we can add ourselves if it becomes a performance issue? This table will be tiny so didn't see the need this time around.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/712fec59-fb2f-4ddc-ab17-e31b57533002?message=5bb4d42d-53e3-4719-b7eb-f9619b6b8fff).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1832#discussion_r899156013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1832#pullrequestreview-1009294246", "body": ""}
{"comment": {"body": "not worth it for now\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/712fec59-fb2f-4ddc-ab17-e31b57533002?message=128db857-75d6-43e3-b35b-21c52b5c9ef8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1832#discussion_r899254128"}}
{"title": "Adding Redis lock to coordinate notification service startup and template updates", "number": 1833, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1833", "body": "Each notification service instance will try to update email templates on startup. Following changes are to make sure they don't step on each other and also to allow us handle AWS rate limits better. \n\nAdded a redis lock to avoid having notification service instances stepping on each other during startup\nAdded rate limit to SES client with default backoff. Some SES API calls (e.g template CRUD) have 1 req per second limit\nUpdated configuration to provide a max timeout for startup template update lock (currently set to 30 seconds)\nUpdated tests\n\nException that was being thrown by notification service causing crashes on startup due to rate limit:\nException in thread \"main\" software.amazon.awssdk.services.ses.model.SesException: Rate exceeded (Service: Ses, Status Code: 400, Request ID: 61e54777-1e2b-4095-94b1-dab799a367f4, Extended Request ID: null)\n    at software.amazon.awssdk.core.internal.http.CombinedResponseHandler.handleErrorResponse(CombinedResponseHandler.java:125)\n    at software.amazon.awssdk.core.internal.http.CombinedResponseHandler.handleResponse(CombinedResponseHandler.java:82)\n    at software.amazon.awssdk.core.internal.http.CombinedResponseHandler.handle(CombinedResponseHandler.java:60)\n    at software.amazon.awssdk.core.internal.http.CombinedResponseHandler.handle(CombinedResponseHandler.java:41)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.HandleResponseStage.execute(HandleResponseStage.java:40)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.HandleResponseStage.execute(HandleResponseStage.java:30)\n    at software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:73)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:42)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:78)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:40)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:50)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:36)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:81)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:36)\n    at software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n    at software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:56)\n    at software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:36)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.executeWithTimer(ApiCallTimeoutTrackingStage.java:80)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:60)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:42)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:48)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:31)\n    at software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n    at software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:37)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:26)\n    at software.amazon.awssdk.core.internal.http.AmazonSyncHttpClient$RequestExecutionBuilderImpl.execute(AmazonSyncHttpClient.java:193)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.invoke(BaseSyncClientHandler.java:103)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.doExecute(BaseSyncClientHandler.java:167)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:82)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:175)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:76)\n    at software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)\n    at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:56)\n    at software.amazon.awssdk.services.ses.DefaultSesClient.updateTemplate(DefaultSesClient.java:4895)\n    at com.nextchaptersoftware.aws.ses.StandardSesProvider.updateTemplate(SesProvider.kt:150)\n    at com.nextchaptersoftware.notification.email.templates.loader.EmailTemplatesLoader$load$1.invoke(EmailTemplatesLoader.kt:35)\n    at com.nextchaptersoftware.notification.email.templates.loader.EmailTemplatesLoader$load$1.invoke(EmailTemplatesLoader.kt:22)\n    at com.nextchaptersoftware.utils.ResourceWalker$walk$1$1.visitFile(ResourceWalker.kt:38)\n    at com.nextchaptersoftware.utils.ResourceWalker$walk$1$1.visitFile(ResourceWalker.kt:29)\n    at java.base/java.nio.file.Files.walkFileTree(Files.java:2811)\n    at java.base/java.nio.file.Files.walkFileTree(Files.java:2882)\n    at com.nextchaptersoftware.utils.ResourceWalker.walk(ResourceWalker.kt:27)\n    at com.nextchaptersoftware.notification.email.templates.loader.EmailTemplatesLoader.load(EmailTemplatesLoader.kt:22)\n    at com.nextchaptersoftware.notificationservice.plugins.EmailTemplatesKt.configureEmailTemplates(EmailTemplates.kt:9)\n    at com.nextchaptersoftware.notificationservice.ModuleKt.module(Module.kt:77)\n    at com.nextchaptersoftware.notificationservice.ModuleKt.module$default(Module.kt:38)\n    at com.nextchaptersoftware.notificationservice.ServerKt$startServer$1.invoke(Server.kt:13)\n    at com.nextchaptersoftware.notificationservice.ServerKt$startServer$1.invoke(Server.kt:8)\n    at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:318)\n    at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:307)\n    at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartup(ApplicationEngineEnvironmentReloading.kt:335)\n    at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.instantiateAndConfigureApplication(ApplicationEngineEnvironmentReloading.kt:307)\n    at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.createApplication(ApplicationEngineEnvironmentReloading.kt:144)\n    at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.start(ApplicationEngineEnvironmentReloading.kt:274)\n    at io.ktor.server.netty.NettyApplicationEngine.start(NettyApplicationEngine.kt:185)\n    at com.nextchaptersoftware.notificationservice.ServerKt.startServer(Server.kt:14)\n    at com.nextchaptersoftware.notificationservice.ApplicationKt.main(Application.kt:13)\n    at com.nextchaptersoftware.notificationservice.ApplicationKt.main(Application.kt)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1833#pullrequestreview-1008094613", "body": ""}
{"title": "Additional states for Installation QOL", "number": 1834, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1834", "body": "Two things that came up.\n\nThe sidebar may be an \"installations\" state. We want to force it to installed if the user reaches the tutorial.\nIf we're in a state where we have no unknownRepos but installation is not installed, we still want to show the install link (This only occurs during testing / deleting GH app)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1834#pullrequestreview-1008313005", "body": ""}
{"title": "Catch git blame in create knowledge", "number": 1835, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1835", "body": "Catch errors from git blame to prevent create knowledge UI from crashing."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1835#pullrequestreview-1008228163", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1835#pullrequestreview-1008228248", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1835#pullrequestreview-1008231106", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1835#pullrequestreview-1008257136", "body": ""}
{"comment": {"body": "FYI it's a bit nicer to do a try/catch on this one function, rather then the entire body above...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1835#discussion_r898507426"}}
{"title": "Add logging to GitHubPullRequestReviewComment.sourceSnippet", "number": 1836, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1836", "body": "Need to understand why were getting\nj.l.IllegalArgumentException: Requested element count -5 is less than zero.\n    at k.c.CollectionsKt___CollectionsKt.takeLast(_Collections.kt:912)\n    at c.n.p.GitHubPullRequestReviewCommentExtensionsKt.sourceSnippet(GitHubPullRequestReviewCommentExtensions.kt:35)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1836#pullrequestreview-1008227761", "body": ""}
{"title": "Open Intercom from Hub app", "number": 1837, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837", "body": "Fixes UNB-172 [hub]-wire-up-intercom-links-from-hub\n\nClicking on 'Customer Support' in the Hub app now launches the dashboard with a special query flag ()\nIn the dashboard, if we are authed, show the default route (My discussions) and pop up intercom\nIf we are not authed, offer to log in, and once they do, pop up intercom:\n\n\nDashboard details:\n* I added a root route to the dashboard, that checks for this flag, and if it's set, stores a 5-minute window timestamp in session storage (this is the window where we will auto-popup intercom)\n* For all authed routes, if the flag is set in session storage (ie the user wanted customer support), pop up intercom and clear session storage\n* For the login route, if the flag is set, display a special message instead.  We want to encourage people to log in for support."}
{"comment": {"body": "Header text: \"Sign in so we can better help you\"\r\nBottom text: \"Difficulty signing in? Click here to continue\"", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#issuecomment-1157031061"}}
{"comment": {"body": "To wire this up in all the various places in the hub, search for `intercomAction`. Some of those places will have accessible auth information, others will not.\r\n\r\nIntercom action slots are found in `ContentView.swift`. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#issuecomment-1158069227"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#pullrequestreview-1008253533", "body": ""}
{"comment": {"body": "@jeffrey-ng @kaych I'm open to different ways of achieving this, if you can think of anything better...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#discussion_r898504331"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#pullrequestreview-1009743145", "body": ""}
{"comment": {"body": "Honestly nothing much top of mind... \r\n\r\nOne thing is to think about how often the above `setLaunchIntercomOnStartup` gets called?\r\nAlso, should we remove `showIntercom` from the query parameter after setting the session variable?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#discussion_r899561811"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#pullrequestreview-1009745403", "body": ""}
{"comment": {"body": "This could be refactored out next to the intercom codebase. This could be useful one day on something like the landing page? Not necessary to do this now though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#discussion_r899563471"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#pullrequestreview-1009745557", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#pullrequestreview-1009822231", "body": ""}
{"comment": {"body": "Yeah I thought about this, the problem is tha this is dashboard-specific while the other code is shared...\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ae8ce490-8a07-497d-a0de-bc71c05f6cf0?message=799204da-39e1-4f5b-8391-e248034dc04f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1837#discussion_r899623426"}}
{"title": "Ability to remove added invitee", "number": 1838, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1838", "body": ""}
{"comment": {"body": "@benedict-jw Styling tips please...\r\n\r\nI'd likely change the remove button's colour and maybe make it a bit smaller?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1838#issuecomment-1157072224"}}
{"comment": {"body": "@jeffrey-ng should we close this with the latest work? Chatted with Ben about this a while ago and we both thought the checkbox is sufficient enough -- the rows are also editable now if necessary (and if they empty the text row, the row will disappear effectively deleting the row) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1838#issuecomment-1175531933"}}
{"title": "Add mention to protos", "number": 1839, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839", "body": "We need to translate mention to protos and back again. :)\nI also want to give a big shout out to @dennispi as I know he looks at all these prs. :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008323457", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008324484", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008325893", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008326046", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008326601", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008327625", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008335861", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008337698", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008346684", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008350023", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008383237", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1008429772", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1009329633", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1009331069", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1009332795", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1009342403", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1009347261", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1009348921", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1009402980", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1839#pullrequestreview-1009502474", "body": ""}
{"title": "Enforce title case on tags for zally", "number": 184, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/184", "body": "Two things:\n1. Zally has a preference for configuring ignores via configuration file.\n2. We are now enforcing title case excluslively ont ag names."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/184#pullrequestreview-868232810", "body": "Thanks man!!"}
{"comment": {"body": "Oh, thanks, was looking for this but couldn\u2019t find.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/184#discussion_r795932662"}}
{"title": "Shorten signature", "number": 1840, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1840"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1840#pullrequestreview-1008331884", "body": "The downside is that the subject is lost now, which might lead to confusion for users who rarely encounter this.\n\"Sent from Unblocked\"\n -- what was sent? the PR? the subject of the discussion?\n\"Comment sent from Unblocked\" \n -- unambiguous"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1840#pullrequestreview-1008340118", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1840#pullrequestreview-1008341628", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1840#pullrequestreview-1008348204", "body": ""}
{"title": "TeamMembers added during PR ingestion should not be active by default", "number": 1841, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1841"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1841#pullrequestreview-1008481156", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1841#pullrequestreview-1008483987", "body": ""}
{"title": "HTTP Response Pagination addresses Pulumi onboarding", "number": 1842, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1842", "body": "general HttpResponse pagination extension\napplied to all list-based GitHub client APIs,\n  except for pull request APIs that currently depend on eTags."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1842#pullrequestreview-1009323068", "body": ""}
{"comment": {"body": "The \"next\" relation is hardcoded here, but it's trivial to replace with a predicate in the future when we use pagination for clients other than the GitHub API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1842#discussion_r899272414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1842#pullrequestreview-1009346308", "body": ""}
{"title": "Add version ingestion to admin console", "number": 1843, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1843"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1843#pullrequestreview-1009860472", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1843#pullrequestreview-1009866336", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1843#pullrequestreview-1009869138", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1843#pullrequestreview-1009869294", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1843#pullrequestreview-1009869974", "body": ""}
{"title": "Fallback to defaultContextLines if start line >= line", "number": 1844, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1844", "body": "Addresses this error "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1844#pullrequestreview-1009553750", "body": "So in future, we should:\n1. Run consistency check: \n2. Get the SM engine to generate the snippet if validation fails"}
{"title": "Remove declarative Net Request", "number": 1845, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1845", "body": "Was known that Safari does not support declarative_net_request.redirect.\nLittle was known that it would crash at start initially... \nFrom testing and from chats with Safari Engineer during WWDC, declarative_net_request.redirect will be supported in the near future (he mentioned it's in safari tech preview but it doesn't work for me there...)"}
{"comment": {"body": "Redirect will still work in both browsers using the fallback content script.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1845#issuecomment-1157944430"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1845#pullrequestreview-1009441330", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1845#pullrequestreview-1009441400", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1845#pullrequestreview-1009442758", "body": ""}
{"title": "Add app version from release tag to mac app and vscode", "number": 1846, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1846", "body": "Added a step to extract semver value from release tag if this is a build triggered by a tag push. It will default to 1.0.0 otherwise. \nThe only way I can properly test this end to end is via a tag push once we merge this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1846#pullrequestreview-1009602262", "body": ""}
{"comment": {"body": "sweet hack", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1846#discussion_r899461074"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1846#pullrequestreview-1009602332", "body": ""}
{"title": "Dont page pullRequestFiles beyond three", "number": 1847, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1847", "body": "Fixes "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1847#pullrequestreview-1009545358", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1847#pullrequestreview-1009584009", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1847#pullrequestreview-1009594185", "body": ""}
{"title": "Basic login success page", "number": 1848, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1848", "body": "\nAfter login exchange (part of auth process for clients), we have been redirecting users directly to home.\nThis led to a subpar UX so swapping it out for a basic login success page. (Needs better design  @benedict-jw )\nAdded a /login/success route which should be closed by the clients. Currently VSCode + Extension will automatically close page. Hub should as well?"}
{"comment": {"body": "@pwerry For Hub auth, I think we should trigger auth in a separate window and close this window once auth is done?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1848#issuecomment-1158018232"}}
{"comment": {"body": "Navigate Home is a backup link/button which will bring them to dashboard home. \r\n\r\nExists just in case clients do *not* close the window. (error case)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1848#issuecomment-1158019009"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1848#pullrequestreview-1009645041", "body": "Copy probably needs work once Ben/Dennis get a chance to take a look"}
{"title": "update", "number": 1849, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1849"}
{"title": "Add prod environment deployment", "number": 185, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/185", "body": "This pr does the following:\n1. Add prod environment helm values\n2. Add prod environment apiservice config\n3. Add github actions for prod deployment\n"}
{"title": "Add a test to validate source snippet parsing", "number": 1850, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1850", "body": "Validate  is no longer an issue"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1850#pullrequestreview-1009593374", "body": " awesome Dave!"}
{"title": "use step output instead of env var", "number": 1851, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1851", "body": "Change how we get version values. Instead of env var we will be using the output of the version job \nChanged services workflow to run only for changes to ci-services* workflow changes. It's annoying to wait for a whole service build when we are not making any changes to it. \nChanged the service workflow to use latest version of EC2 runner. I forgot to update the branch name it was still using the old version with runner name conflict issue."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1851#pullrequestreview-1009661852", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1851#pullrequestreview-1009662289", "body": ""}
{"comment": {"body": "Even though I had the fix pushed 2 weeks ago, it was still referencing the old version ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1851#discussion_r899502040"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1851#pullrequestreview-1009671917", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1851#pullrequestreview-1027890209", "body": ""}
{"comment": {"body": "@rasharab what do you think?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2420a5b1-956d-49b9-b1c3-d04bd24817ae?message=63cf4867-9ca0-4fea-81eb-d2c68267d328).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1851#discussion_r913259392"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1851#pullrequestreview-1027891090", "body": ""}
{"comment": {"body": "@benedict-jw testing :)\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2420a5b1-956d-49b9-b1c3-d04bd24817ae?message=a6bd0d45-7f05-42d0-9311-99f023b6bc26).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1851#discussion_r913260136"}}
{"title": "Fix center alignment of dashboard unreads", "number": 1852, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1852", "body": "looks like Sofia Pro numbers have a different line height alignment than what we used before, needed some adjusting to the styling \nbefore\n\nafter\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1852#pullrequestreview-1009696870", "body": ""}
{"title": "revert vscode version", "number": 1853, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1853", "body": "Reverting VScode Extension version for now. We need to figure out how we want to version those releases on their own."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1853#pullrequestreview-1009723366", "body": ""}
{"title": "Jeff/unb 285 broken recent workspaces in vscode", "number": 1854, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1854"}
{"title": "More generic submenu item fetcher", "number": 1855, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1855", "body": "Fixes UNB-285\nVSCode changed their recent workspace key.\nMake the JSON parser more generic to hopefully not break next time they rev versions."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1855#pullrequestreview-1009952358", "body": ""}
{"title": "All message block images need set width", "number": 1856, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1856", "body": "before:\n\n\nafter:\n\n\nNote: This looked fine in vscode because img elements were automatically given width: 100% within the vscode built-in styles but it ends up looking very broken in all our other clients"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1856#pullrequestreview-1009729239", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1856#pullrequestreview-1009754911", "body": ""}
{"comment": {"body": "Should this be max-width?  Can images not be narrower then 100% width?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1856#discussion_r899570861"}}
{"title": "fixing a bunch of stupid copy paste mistakes", "number": 1857, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1857"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1857#pullrequestreview-1009871940", "body": ""}
{"title": "Jeff/unb 284 escape valve for auth in sidebar", "number": 1858, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858", "body": "Timeouts for Auth Process.\nAfter 20 seconds, we will end polling and prompt user to try again.\nRequired a refactor of how the polling system works. VSCode & Web extension now share polling code in shared authstore.\nThis was necessary to add a \"timeout\" state\n\n"}
{"comment": {"body": "Like prior PRs, this is just a first draft of the UI/text. We should revisit these with @benedict-jw / @dennispi before release.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#issuecomment-1158239382"}}
{"comment": {"body": "Updated sidebar\r\n<img width=\"295\" alt=\"CleanShot 2022-06-28 at 15 11 59@2x\" src=\"https://user-images.githubusercontent.com/1553313/176310210-60fd71ff-f397-4509-b721-539d0414d6b2.png\">", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#issuecomment-1169334883"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#pullrequestreview-1022342331", "body": ""}
{"comment": {"body": "We really do need a reusable `Poller` helper.  Not a big deal for now but I'll make an issue to do this at some point, because we end up rebuilding this same thing over and over again, with subtle bugs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#discussion_r908896991"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#pullrequestreview-1022342464", "body": ""}
{"comment": {"body": "In this case I think we can get rid of `shouldContinuePolling`, since we're returning in almost all cases.  If we return after the success case, then we don't need to track state manually.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#discussion_r908897092"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#pullrequestreview-1022361583", "body": ""}
{"comment": {"body": "(Oh, I see we use that to cancel polling from outside the poller -- we could probably use `isPolling` for that...  or maybe I'm wrong and we need both variables)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#discussion_r908915225"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#pullrequestreview-1022364382", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#pullrequestreview-1022381743", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#pullrequestreview-1022382882", "body": ""}
{"comment": {"body": "We can't just send `state.$case` here and avoid the switch statement altogether?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#discussion_r908930191"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#pullrequestreview-1022385328", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#pullrequestreview-1022407760", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#pullrequestreview-1022500889", "body": ""}
{"comment": {"body": "Types don't quite match up unfortunately.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/eb572344-ff2d-4e7d-bc00-b36cf402ad92?message=6a7f5bbc-2ab0-4f1c-90a1-c9103ae25ed2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1858#discussion_r909016242"}}
{"title": "Singleton refresh auth promise", "number": 1859, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1859", "body": "We want there to only be a single refreshAuth request occurring at a time.\nIf we end up in a situation where multiple requests require refreshing auth, they should all be blocked by a single refresh instance.\nFor context: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1859#pullrequestreview-1009954552", "body": ""}
{"comment": {"body": "I think there might be a bug here -- I think if anything throws here, refresh auth will get stuck forever, because the promise is never cleared?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1859#discussion_r899728942"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1859#pullrequestreview-1010763163", "body": ""}
{"comment": {"body": "So I guess the plan would be to set this within the catch as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1859#discussion_r900304815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1859#pullrequestreview-1010908351", "body": ""}
{"comment": {"body": "I think so.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1859#discussion_r900425838"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1859#pullrequestreview-1011093372", "body": ""}
{"title": "Rename [1 of 4]: SourceMark -> SourceMarkGroup", "number": 186, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/186", "body": "As discussed, some renaming:\n\nSourceMark  SourceMarkGroup (this change)\nSourceMark  SourceMarkGroup in API\nSourceVector  SourceMark\nRelate SourceMark  Message"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/186#pullrequestreview-868414662", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/186#pullrequestreview-868416431", "body": ""}
{"title": "Add honeycomb traces for servcies", "number": 1860, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1860", "body": "Move some of the existing scm jobs to PollingBackgroundJobs so we can do a standardized honeycomb trace."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1860#pullrequestreview-1009864237", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1860#pullrequestreview-1009941356", "body": "Thanks!"}
{"title": "Fix timing tets", "number": 1861, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1861"}
{"title": "FixTimingTests2", "number": 1862, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1862", "body": "Fix timing tets\nupdtae\nupdate"}
{"title": "update", "number": 1863, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1863"}
{"title": "trying to fix a potential interpolation issue", "number": 1864, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1864"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1864#pullrequestreview-1009908592", "body": ""}
{"title": "Ugly version hack", "number": 1865, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1865"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1865#pullrequestreview-1009946001", "body": ""}
{"title": "Render gutter icons and tooltips for multi-thread text rows", "number": 1866, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866", "body": "Fixes UNB-191 \nSome notes:\n* Implementing the designs exactly is not possible with the restrictions in VSCode's Markdown/HTML.  In the designs the titles are bigger/bolder, and the spacing is slightly different.  This was as close as I could get it.\n* The \"multi\" thread icon (when there are multiple kinds of threads in a single row) is the Pink PR and Blue Discussion icon.  Open PRs don't appear as green.  I'm not sure how to handle all these mixed cases, I'll ask Ben next week.\n* The changes to the TextEditorSourceMark code was pretty involved, but I think the end result makes more sense, and it's more efficient FWIW.\n* I factored the tooltip creation code into another file, as it got pretty complicated as well\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009949476", "body": ""}
{"comment": {"body": "Return an icon representing a mixed set of threads", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#discussion_r899724925"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009959666", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009960270", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009961055", "body": ""}
{"comment": {"body": "Hmmm is it possible to add a `style=\u201cborder-radius: 50%\u201d` onto this html? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#discussion_r899734148"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009961527", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009961621", "body": "Nice"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009962167", "body": ""}
{"comment": {"body": "No, unfortunately.  VSCode blocks almost all tags and styles, what we can do is very limited.  I made a VSCode issue for this that got some votes but nothing concrete so far: https://github.com/microsoft/vscode/issues/140313", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#discussion_r899734961"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009963304", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009973693", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1009973981", "body": ""}
{"comment": {"body": "^", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#discussion_r899744132"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1010802669", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1010890959", "body": ""}
{"comment": {"body": "Fixed thanks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#discussion_r900413194"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1012689431", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1866#pullrequestreview-1012689698", "body": ""}
{"title": "Concurrently process repos for PR sync job", "number": 1867, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1867", "body": "We've 400 repos now so syncing them serially is causing delays in grabbing messages posted to GitHub.\nAnother optimization is to have two sync jobs, one for repos that have been active recently, and another for repos without any activity.\nBut really, we need a webhook service :)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1867#pullrequestreview-1009955554", "body": ""}
{"title": "Don't code on an empty brain", "number": 1868, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1868"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1868#pullrequestreview-1009975194", "body": ""}
{"title": "Drop concurrency to 10", "number": 1869, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1869", "body": "Too many concurrent requests, we're getting throttled by GitHub"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1869#pullrequestreview-**********", "body": ""}
{"comment": {"body": "@davidkwlam how did you discover that?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1869#discussion_r899755395"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1869#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Logs :) https://app.logz.io/#/goto/aade3473527e1d988cbbfe1c12e16492?switchToAccountId=411850\r\n\r\nStill happening, but it's dropped now. Might need to drop the concurrency 5 or try some optimizations like calling less frequently for repos with no PRs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1869#discussion_r899756396"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1869#pullrequestreview-**********", "body": ""}
{"comment": {"body": "@davidkwlam Some suggestions from GitHub:\r\n1. pass User-Agent header (should be the name of the GitHub App `unblocked-app`)\r\n2. respect Retry-After response header\r\n3. serialize requests :(\r\n\r\ndocs:\r\n- https://docs.github.com/en/rest/guides/best-practices-for-integrators#dealing-with-secondary-rate-limits\r\n- https://docs.github.com/en/rest/overview/resources-in-the-rest-api#secondary-rate-limits\r\n\r\nAlso, GitHub can freely suspend our App and our Org for breaking terms of use. They don't fuck around.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1869#discussion_r899768504"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1869#pullrequestreview-1010007027", "body": ""}
{"comment": {"body": "> Also, GitHub can freely suspend our App and our Org for breaking terms of use. They don't fuck around.\r\n\r\nYup this is the nightmare scenario.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1869#discussion_r899769178"}}
{"title": "Test: ignore this", "number": 187, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/187"}
{"title": "Fix showing at mentions", "number": 1870, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870", "body": "The logic for parsing @mention ranges was a cluster fuck.\nWe've somewhat improved it as the prior logic was a pain to discern what was going on.\nWe are now showing @mention list when user types '@' symbol.\nAlso fixed bug with hasContent logic."}
{"comment": {"body": "Seeing that gif, we probably need to add a max-height to the mentions dropdown to mitigate the jumping above the target to below when there are a large number of team members. I can do this in a follow up PR ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#issuecomment-1158513328"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#pullrequestreview-1010009533", "body": ""}
{"comment": {"body": "TIL!\r\n\r\nCan we add a white space after the mention is inserted? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#discussion_r899771041"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#pullrequestreview-1010010302", "body": ""}
{"comment": {"body": "Next pr. This was already a pain in the ass to get working. Working with slate is like getting a root canal.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#discussion_r899771603"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#pullrequestreview-1010010785", "body": ""}
{"comment": {"body": "nit: `mentionMatch[1] ?? ''`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#discussion_r899771951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#pullrequestreview-1010011220", "body": ""}
{"comment": {"body": "and could you add a comment explaining why it\u2019s the element at index 1 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#discussion_r899772232"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#pullrequestreview-1010011423", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#pullrequestreview-1010084773", "body": ""}
{"comment": {"body": "Roger", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#discussion_r899825631"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#pullrequestreview-1010907203", "body": ""}
{"comment": {"body": "Thanks for fixing this and commenting it.  Slate stuff makes sense once you see what it's doing, adding comments helps", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1870#discussion_r900424991"}}
{"title": "Drop concurrency to 5", "number": 1871, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1871"}
{"title": "Set user agent for github client", "number": 1872, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1872", "body": "This is set in the common http client, so all requests from the app client and the org client will have this set."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1872#pullrequestreview-1010020573", "body": ""}
{"title": "Just make the calls serially", "number": 1873, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1873"}
{"title": "Setup LoginLocation redirect for web", "number": 1874, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1874", "body": "Setup LoginLocation state which dictates where users land after successful login/exchange."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1874#pullrequestreview-1010901016", "body": ""}
{"title": "Handle when file sha is null", "number": 1875, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875", "body": "Addresses these exceptions "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#pullrequestreview-1011551939", "body": ""}
{"comment": {"body": "Any idea why GitHub does this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#discussion_r901137545"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#pullrequestreview-1012840877", "body": ""}
{"comment": {"body": "Empty file!\r\n\r\nhttps://github.com/pulumi/pulumi/pull/5812/files#diff-46a4f501069302dae071b3368ebd693f4848dfc7a4a17747e31f88c2afa01dc5\r\n\r\n\ud83e\udd26 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#discussion_r902070694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#pullrequestreview-1012841638", "body": ""}
{"comment": {"body": "I don't see a thread anchored on this file, but when we call the GitHub `files` api, we get every file for the PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#discussion_r902071294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#pullrequestreview-1012862095", "body": ""}
{"comment": {"body": "makes sense\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/777e9cb1-e6c0-4e96-b991-88271fd4ffab?message=1332a5be-1cef-4d3c-9d07-ce124d82661f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#discussion_r902086391"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#pullrequestreview-1012863280", "body": ""}
{"comment": {"body": "Even empty files have a file sha:\r\n\r\n```\r\ntouch a b\r\ngit add -A\r\ngit diff --cached --full-index -- a b\r\n```\r\n\r\n\r\n\r\nWill produce this SHA every time:\r\n\r\n```\r\ne69de29bb2d1d6434b8b29ae775ad8c2e48c5391\r\n```\r\n\r\nSo it's still a GitHub bug.\r\n\r\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/777e9cb1-e6c0-4e96-b991-88271fd4ffab?message=bd5e28ab-d869-42c5-9523-165270abcfcd).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1875#discussion_r902087315"}}
{"title": "[Onboarding] Show README / a file if there are no committed files", "number": 1876, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1010767691", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1010893154", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1010910049", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1010911800", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1010917558", "body": ""}
{"comment": {"body": "With this we're now running git in 3 different ways (Runner.run, Runner.runExec, runCommand).  I am concerned about subtle bugs here, can we standardize this through one mechanism?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#discussion_r900432613"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1010917799", "body": ""}
{"comment": {"body": "(Can be done as a follow-on task of course)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#discussion_r900432836"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1010918970", "body": ""}
{"comment": {"body": "runCommand just runs Runner.runExec so it's just two. But same point. We could probably consolidate run and runExec but IMO can be done in a separate PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#discussion_r900433687"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1011010948", "body": ""}
{"comment": {"body": "Run and RunExec have different use cases so I wouldn't consolidate them.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#discussion_r900499727"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1011011656", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1011011703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1876#pullrequestreview-1011045566", "body": ""}
{"title": "Kay Fixes :)", "number": 1877, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1877"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1877#pullrequestreview-1010788196", "body": ""}
{"comment": {"body": "This was the fix. The other lwas lint.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1877#discussion_r900315220"}}
{"title": "Update header with title and ellipsis", "number": 1878, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1878", "body": ""}
{"comment": {"body": "fwiw https://chapter2global.slack.com/archives/C02US6PHTHR/p1655492722505329?thread_ts=1655491914.138309&cid=C02US6PHTHR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1878#issuecomment-1159158397"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1878#pullrequestreview-1010926170", "body": ""}
{"comment": {"body": "We should definitely refactor this to a misc mixin haha", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1878#discussion_r900438770"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1878#pullrequestreview-1010926227", "body": ""}
{"title": "Add service", "number": 1879, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010943042", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010943847", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010944100", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010944317", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010944450", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010945266", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010945296", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010945353", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010945423", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010945497", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010945710", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010945718", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010945909", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010946027", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010946252", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010948790", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010949010", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010949058", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010949140", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-1010951936", "body": ""}
{"comment": {"body": "I wonder what the deep check would do, since webhook service does not have access to PG or Redis. Maybe the implementation is identical to shallow check?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#discussion_r900457221"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This service does not need to wait for DB to be actualized.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1879#discussion_r900458219"}}
{"title": "test -- ignore", "number": 188, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/188"}
{"title": "WebhookEcr", "number": 1880, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1880"}
{"title": "add service accounts and new queues", "number": 1881, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1881", "body": "Added new service account for Webhook service \nService accounts have been deployed to dev and prod \nAdded deadletter queues to existing PR ingestion queues \nAdded deadletter and standard queue for scm_hooks"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1881#pullrequestreview-**********", "body": ""}
{"title": "Hub updater", "number": 1882, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-**********", "body": ""}
{"comment": {"body": "unfortunately the async/await versions of `URLSession.downloadTask` are macOS 12 and up only :( ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#discussion_r900504507"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Update file is always the same so we don't grow the disk", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#discussion_r900504884"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1011101386", "body": ""}
{"comment": {"body": "Would this automatically replace existing file?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#discussion_r900522756"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1011101901", "body": ""}
{"comment": {"body": "Nevermind... Below.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#discussion_r900522861"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1011186858", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1011190155", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1011192442", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1011192748", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1011193242", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1011193423", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1011194647", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#pullrequestreview-1017568533", "body": ""}
{"comment": {"body": "Testing post from dev\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/7d439577-da34-4120-a4ae-1adda8e900f5?message=a7fd87ee-9e87-47dd-9f77-0cedb9476ecd).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1882#discussion_r905405835"}}
{"title": "Deploy webhook", "number": 1883, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1883"}
{"title": "Update onboarding request for PR ingestion", "number": 1884, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1884", "body": "Send an event to prioritize certain repos for PR ingestion during onboarding."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1884#pullrequestreview-1011095723", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1884#pullrequestreview-1011113342", "body": "Thanks Jeff."}
{"title": "Fix dashboard message editor", "number": 1885, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1885", "body": "Fixes UNB-289 \nI messed this up.  I put the IntercomAutoLaunch component definition in the middle of another component -- I mis-read the spacing and thought it was at the top level.  Since the function is defined in another component, it effectively is a new component every time the parent renders, which causes the react tree to be wiped out, and our state lost.\nAs part of fixing this, I moved all the intercom auto launching stuff into one file so it's easier to follow.\nWill also look at enabling this lint rule, which should catch this kind of thing: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1885#pullrequestreview-1011080365", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1885#pullrequestreview-1011144950", "body": ""}
{"title": "New POST /hooks/github API endpoint", "number": 1886, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1886", "body": "Stubs in next PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1886#pullrequestreview-1011090095", "body": ""}
{"title": "Refresh source marks on create discussion", "number": 1887, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1887", "body": "Web extension only loads source marks on load. \nTherefore, when a new discussion was created, its source mark was never added to the UI.\nWe now refresh the sourcemarkstore on discussion creation.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1887#pullrequestreview-1011110432", "body": ""}
{"title": "Add webhook models and ability to look up repo by external owner repo ID", "number": 1888, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1888", "body": "Will be used by webhook handlers"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1888#pullrequestreview-1011091656", "body": ""}
{"comment": {"body": "Will be called from the handlers using the IDs from the webhook payload", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1888#discussion_r900520885"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1888#pullrequestreview-1011104459", "body": ""}
{"title": "Increase parallelization", "number": 1889, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1889"}
{"title": "Rename [2 of 4]: SourceMark -> SourceMarkGroup API", "number": 189, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/189", "body": "As discussed, some renaming:\n\nSourceMark  SourceMarkGroup (#186)\nSourceMark  SourceMarkGroup in API (this change)\nSourceVector  SourceMark\nRelate SourceMark  Message"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/189#pullrequestreview-868445733", "body": ""}
{"title": "Pass unsaved files to getSourceMarkLatestPoint", "number": 1890, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1890", "body": "Fixes UNB-237\n\nMoved UnsavedChangeTracker to a singleton that can be used anywhere, in the VSCode utils module\nUse its wrapper behaviour in the two places calling getSourceMarkLatestPoint"}
{"comment": {"body": "> nice\r\n\r\nI added using a proper dependency injection system that is well supported *Spring\" at the onset in java land, but I was told to remove it as dependency injection has its own pains.\r\n\r\nIf we want to revisit that, then I firmly believe it should be wholesale across the product or not at all..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1890#issuecomment-1159804332"}}
{"comment": {"body": "> I added using a proper dependency injection system that is well supported *Spring\" at the onset in java land, but I was told to remove it as dependency injection has its own pains.\r\n> \r\n> If we want to revisit that, then I firmly believe it should be wholesale across the product or not at all..\r\n\r\nYeah I think I agree with that -- we should think about it a bit before adopting it, and shouldn't just adopt it piecewise.  Also, our dependency graph in the TS code is quite shallow, so DI may be overkill -- Richie's point about testability is true, but so far we've been able to mock out the minimal dependencies between modules fairly easily.\r\n\r\nThat being said, if we do go down this route, tsyringe looks like a good option.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1890#issuecomment-1160615627"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1890#pullrequestreview-1011199035", "body": ""}
{"comment": {"body": "We have enough singletons like this that we need to come up with a pattern for it -- I'll start thinking about this, but this is simple enough to work for now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1890#discussion_r900555745"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1890#pullrequestreview-1011551181", "body": "nice"}
{"comment": {"body": "Might be worth investing in a dependency injection system, something like https://github.com/microsoft/tsyringe.\r\n\r\nThis solves two issues:\r\n1. built-in singleton pattern (eg https://github.com/microsoft/tsyringe#singleton)\r\n2. testability\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1890#discussion_r901136543"}}
{"title": "update deps", "number": 1891, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1891"}
{"title": "Add docker webhook", "number": 1892, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1892", "body": "More bullshit"}
{"title": "GitHub Webhook API implementation with tests", "number": 1893, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893", "body": "Ready to hook up to queue."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#pullrequestreview-1011237379", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#pullrequestreview-1011237496", "body": ""}
{"comment": {"body": "Should compress the body when sending, because they can be quite big (100s of kB) sometimes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#discussion_r900647667"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#pullrequestreview-1011237705", "body": ""}
{"comment": {"body": "For other sqs payloads, we use kotlinx serialization to dump them into a final json payload.\r\nAre you planning on doing that?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#discussion_r900649034"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#pullrequestreview-1011237734", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#pullrequestreview-1011237751", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#pullrequestreview-1011240943", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#pullrequestreview-1011249364", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#pullrequestreview-1011249496", "body": ""}
{"comment": {"body": "yes, but only at the top level:\r\n```\r\n{\r\n   \"body\": \"string\",\r\n   \"headers\": <array of string pairs>\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1893#discussion_r900692616"}}
{"title": "update", "number": 1894, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1894"}
{"title": "Renaming classes", "number": 1895, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1895", "body": "No logic changes, just renaming as part of the webhook handler work."}
{"title": "Add Honeycomb to webhook-service", "number": 1896, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1896"}
