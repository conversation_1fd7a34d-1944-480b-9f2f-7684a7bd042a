{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882245433", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882246119", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882248774", "body": ""}
{"comment": {"body": "Yup. There wasn't much thought in the actual UI for this PR.\r\nI think we'll be revisiting the entire experience when we think more about onboarding.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#discussion_r806282191"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882250139", "body": ""}
{"comment": {"body": "I don't think it's guaranteed, which is why I have it potentially returning undefined. FWIW, I haven't seen it actually fail once I had this setup.\r\n\r\nI'll make it throw instead of return undefined.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#discussion_r806283125"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882256675", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882259314", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882259804", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882274695", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882305747", "body": ""}
{"title": "SM Engine reports throughput and cumulative usage", "number": 3200, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3200"}
{"title": "Sort SourceMarks by line", "number": 3201, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3201", "body": "Fixes UNB-680\nI will add a test for this in my other PR (https://github.com/NextChapterSoftware/unblocked/pull/3188)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3201#pullrequestreview-1131969614", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3201#pullrequestreview-1131970970", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3201#pullrequestreview-1131998798", "body": ""}
{"title": "Source mark tracking works when moved by commits but does not work when moved by untracked changes", "number": 3202, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3202", "body": "\nanother regression. needs system-level tests badly."}
{"title": "Create pull request summary page for admin console", "number": 3203, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3203", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3203#pullrequestreview-1132092300", "body": ""}
{"title": "Onboarding revamp", "number": 3204, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204", "body": "Write new onboarding flow with slides instead of the tutorial\n\n\n\nThis is hooked up to the previous command so it should flow through seamlessly\nPlanning to remove the old onboarding code in a separate PR to cut down on the diffs"}
{"comment": {"body": "on principle, I want this change in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#issuecomment-1271883427"}}
{"comment": {"body": "\ud83d\udc4d Looks good!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#issuecomment-1272113320"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1132099537", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1132103027", "body": ""}
{"comment": {"body": "This is just `divide` followed by a `map` on both of the output arrays?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r988394223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1132116349", "body": ""}
{"comment": {"body": "Should clicking on this add the email?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r988404327"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1133602332", "body": ""}
{"comment": {"body": "Same outcome but more efficient? Since it's done in one reduce fn vs two loops\r\n\r\nI can remove this if preferred -- I didn't end up using this function in this PR but IMO this would be helpful for future use anyway", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r989457260"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135042631", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135071847", "body": ""}
{"comment": {"body": "We're doing this elsewhere too, right?  Can we factor this logic out into a common method?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r990497451"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135090602", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135091779", "body": ""}
{"comment": {"body": "Is this needed?  It sounds like the code that already exists should be pretty much ensuring that the Installed state is set correctly?\r\n\r\nThe reason I'm bringing this up is that we have already run into difficulties tracking down how the Installed/Auth states were updated, since there used to be a number of places that (sometimes conflictingly) changed the states, and I'd like to keep this as simple as possible.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r990512092"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135092637", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135097832", "body": ""}
{"comment": {"body": "A bunch of the stuff here is duplicated in TutorialWizard -- is the idea that TutorialWizard will eventually be removed, so this doesn't need to be factored out?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r990516705"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135100091", "body": ""}
{"comment": {"body": "Does this work take awhile?  Should we move this to the top of the file, so it runs on startup, and the data is available immediately once we hit this step?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r990518320"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135100598", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135101415", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135110782", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135112029", "body": ""}
{"comment": {"body": "yeah I had an iteration of this on a previous commit -- the thing is there's no guarantee that it would finish loading by the time the user gets to this page; in that instance, we would be calling this twice which seems inefficient..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r990527375"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1135112453", "body": ""}
{"comment": {"body": "Yeah the plan is to remove that directory altogether once this is in", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r990527698"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1137966149", "body": ""}
{"comment": {"body": "Update: gonna merge this PR in under a feature flag (this new onboarding flow will be enabled on local and dev) and then an ensuing PR will remove the feature flag and old directory altogether", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r992647345"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#pullrequestreview-1137977458", "body": ""}
{"comment": {"body": "this is just copied over from the previous iteration of this UI (ie the TutorialWizardCommand) \r\n\r\ncc: @jeffrey-ng for more context?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3204#discussion_r992655064"}}
{"title": "Create walkthrough settings window", "number": 3205, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3205", "body": "Live preview isn't there yet. This is just to get the layout and controls working properly. The one thing that is actually working is sniffing out the default screen using the window metadata APIs. The rest is mocked\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3205#pullrequestreview-1134948790", "body": ""}
{"comment": {"body": "I'm not sure I love calling this an UnblockedButton, as that has no real meaning.  Dunno if we can call it something more particular, or just a StyledButton, or something...  maybe it's fine as-is \ud83d\ude06 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3205#discussion_r990411778"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3205#pullrequestreview-1135011762", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3205#pullrequestreview-1135083350", "body": ""}
{"comment": {"body": "We can call it StyledButton. I'll push that change up to the top level of the stack or it will create merge hell", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3205#discussion_r990505849"}}
{"title": "Add Slack PR icons", "number": 3206, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3206", "body": "Add PR-with-slack-threads icon, and use it in the sidebar UIs and the PR view tab icons.\nAs part of this, I cleaned up the way that the \"Open PR\" command worked a bit, to reduce boilerplate.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3206#pullrequestreview-1132168680", "body": ""}
{"comment": {"body": "Note: I moved this command into the common list of Sidebar commands, because its usage and implementation were the same everywhere.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3206#discussion_r988444385"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3206#pullrequestreview-1135084269", "body": ""}
{"title": "Identify slack threads during PR ingestion", "number": 3207, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3207"}
{"title": "getPullRequestInfo returns hasSlackThreads", "number": 3208, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3208"}
{"title": "Add some logs", "number": 3209, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3209"}
{"title": "Ci for cdk deploy", "number": 321, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/321", "body": "Sorry for the excessive noise on the PR. Getting CI/CD to work is a bit of a pain. \n\nAdded cdk config json for mgt environment. I missed it in one of my previous PRs \nChanged IAM stack to create an Admin Read-only role. This will be used to scope some of our automation and audit permissions later on. \nAdded flag for IAM readonly role to all env \nAdded CI/CD workflows for infra \n\nThis is all working now and we have full CI/CD for our core infra. Management account is disabled for now because it's our root account and we can't just grant Admin on it. I am working on scoped permissions but we don't run any resources in management account so it should be fine."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/321#pullrequestreview-*********", "body": ""}
{"comment": {"body": "lol with the naming", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/321#discussion_r805060037"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/321#pullrequestreview-*********", "body": ""}
{"title": "Dont count reviews with no content", "number": 3210, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3210"}
{"title": "Update default PR tab to discussions", "number": 3211, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3211", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3211#pullrequestreview-**********", "body": ""}
{"title": "Introduce SlackThreadRelevance service", "number": 3212, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3212", "body": "Will expand the heuristics in a follow up PR"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3212#pullrequestreview-1133376151", "body": "I love the naming"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3212#pullrequestreview-1133376297", "body": "I love the naming"}
{"title": "Expand SlackThreadRelevance heuristics", "number": 3213, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3213"}
{"title": "VSCode Video suppport", "number": 3214, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214", "body": "Add basic mock UI for Walkthrough.\nMain work was figuring out how to render video from outside extension. \n\nWill be filling out logic and styling in future PR after integration with Video App."}
{"comment": {"body": "Merging first as this is blocking work and doesn't affect current builds.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#issuecomment-1270949264"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#pullrequestreview-1135020966", "body": ""}
{"comment": {"body": "Yeah we have a `@knowledge` alias for this, we should use it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#discussion_r990462692"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#pullrequestreview-1135024358", "body": ""}
{"comment": {"body": ".... huh.  How was `asWebviewUri` not working?\r\n\r\nI've been thinking we shouldn't be using that anyways, as it means we have to reference images in the extension and pass them down to the view...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#discussion_r990465008"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#pullrequestreview-1135024619", "body": ""}
{"comment": {"body": "I don't think this is used?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#discussion_r990465197"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#pullrequestreview-1135072446", "body": ""}
{"comment": {"body": "Will be in my currentPR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#discussion_r990497860"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#pullrequestreview-1135072826", "body": ""}
{"comment": {"body": "I was getting some strange cyclical references... ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#discussion_r990498181"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#pullrequestreview-1135074156", "body": ""}
{"comment": {"body": "We should probably figure that out -- we shouldn't have cyclical references between modules like that, and referring to files directly like this kind of masks the problem...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3214#discussion_r990499033"}}
{"title": "Fix indentation", "number": 3215, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3215"}
{"title": "Wire up data sources for settings", "number": 3216, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3216"}
{"title": "AddSlackAuth", "number": 3217, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3217", "body": "Slack installations\nMore auth"}
{"title": "Log found slack urls in TLCs", "number": 3218, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3218"}
{"title": "Fix install bugs", "number": 3219, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3219"}
{"title": "Add modifiedAt queries to ThreadModel", "number": 322, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/322", "body": "This is an example of adding DAO support for modifiedAt status queries. Unfortunately the UUIDEntityClass structure doesn't expose the typing information for the underlying table, so this can't be implemented as extensions unless reflection is used."}
{"comment": {"body": "@richiebres what do you think about using a BRIN index for modifiedAt?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/322#issuecomment-1036550468"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/322#pullrequestreview-880597565", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/322#pullrequestreview-880672531", "body": ""}
{"title": "Ingest linked slack thread", "number": 3220, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3220"}
{"title": "Leverage Git object database to combine saved and unsaved changes for uncommitted SM move detection", "number": 3221, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3221", "body": "Problem\nThe VSCode Auto Save setting causes deterministic bugs in source mark rendering when moving source\nmarks between files in unsaved or uncommitted editors. Bugs are present regardless of the setting, but\nthe bugs are different (or at least present differently) depending on the setting.\nExplanation\nThe problem arises when one half of the move is saved and the other part of the move is unsaved. These\ntwo sets of hunks are treated like sequential commits, where the saved hunks are processed first, then\nthe unsaved hunks are processed.\nIf the saved hunks remove the mark then it is treated as deleted or heavily modified, and propagates\nno further. If the unsaved hunks are saved, then all hunks are processed together and we can detect\nthe move. This fully explains the Auto Save onFocusChange scenario.\nA similar but opposite set of events occurs for the Auto Save off scenario. In this case the move is\ntracked correctly while all files are unsaved. As soon as one file is saved the move is split across\ntwo sets of sequentially processed hunks.\nChanges\nSolution is to stop processing both sets of saved/unsaved hunks separately. Right now we generate two\nsets of diff hunks from two separate diff commands: HEAD..saved, and saved..unsaved.\nInstead we need to generate a unified set of diff hunks from a single diff: HEAD..unsaved. The Git\nnative way to do this is to add the unsaved files as blobs to the Git object database. That allows us\nto diff the blobs: HEAD{blob}..unsaved{blob}. This also allows us to take advantage of the Git CAS\nfor caching.\nGit gc will eventually prune the object IDs that we add as blobs after 2 weeks, by default.\n"}
{"comment": {"body": "Going to push, please review after @matthewjamesadam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3221#issuecomment-1270848461"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3221#pullrequestreview-1133741229", "body": ""}
{"comment": {"body": "risky", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3221#discussion_r989558133"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3221#pullrequestreview-1133741445", "body": ""}
{"comment": {"body": "will fix in follow up", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3221#discussion_r989558286"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3221#pullrequestreview-1133741494", "body": ""}
{"comment": {"body": "will fix in follow up", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3221#discussion_r989558329"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3221#pullrequestreview-1133741673", "body": ""}
{"title": "Revert \"Ingest linked slack thread (#3220)\"", "number": 3222, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3222", "body": "This reverts commit 616fffbf55f4a7595dcf2831bdd5cd191d8578be."}
{"title": "Guard against get top-N files returning few than expected results", "number": 3223, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3223", "body": "Caller asks for N results. Function calcualtes N+2 results in case some\nfiles fail to generate any sourcemarks, then returns top N of those."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3223#pullrequestreview-1133754261", "body": ""}
{"title": "Add slack sercrets", "number": 3224, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3224"}
{"title": "Address move issues for getPointForSourceMark", "number": 3225, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3225", "body": "Follow up from #3221\nfixes "}
{"comment": {"body": "Dude is on a roll. Go richie go.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3225#issuecomment-1271989319"}}
{"title": "Add admin console", "number": 3226, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3226"}
{"title": "Fix up transactions", "number": 3227, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3227"}
{"title": "Capture Preview", "number": 3228, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#pullrequestreview-1135098073", "body": ""}
{"comment": {"body": "Normally I don't touch Apple sample code but this was actually pretty well done!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#discussion_r990516868"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#pullrequestreview-1135105289", "body": ""}
{"comment": {"body": "I'm guessing this is to make the list live -- do we need to mark the same change to the Camera-monitoring class?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#discussion_r990522318"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#pullrequestreview-1135106248", "body": ""}
{"comment": {"body": "The name is kind of ambiguous -- maybe all these classes should have a common suffix?  `MicListing` / `ScreenListing` / `CameraListing` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#discussion_r990523031"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#pullrequestreview-1135109756", "body": ""}
{"comment": {"body": "`MicSelection` / `ScreenSelection` / `CameraSelection` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#discussion_r990525639"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#pullrequestreview-1135110621", "body": ""}
{"comment": {"body": "Looks like this is never called -- and capturing looks like it might be expensive.  We might need to figure out some way to deal with this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#discussion_r990526199"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#pullrequestreview-1135112990", "body": ""}
{"comment": {"body": "Oh.... I guess we would continue capturing after the window/screen selection view closes, so the expectation is that this would only get shut down when we complete the walkthrough capture?  Probably doesn't matter then.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#discussion_r990528063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#pullrequestreview-1135116221", "body": ""}
{"comment": {"body": "Rename the queue? \ud83d\ude04 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#discussion_r990530453"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#pullrequestreview-1135118818", "body": "Superb "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#pullrequestreview-1135146275", "body": ""}
{"comment": {"body": "\ud83d\ude02", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3228#discussion_r990555378"}}
{"title": "Parse slack links from PR descriptions", "number": 3229, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3229"}
{"title": "Fix logging crap", "number": 323, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/323", "body": "logz.io in debug mode logs a lot of crap"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/323#pullrequestreview-880689388", "body": "Thank you"}
{"title": "Fix SM engine regression caused by Git diff parameter change", "number": 3230, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3230", "body": "I removed --no-prefix in favour of --dst-prefix=|||, which broken everyone except for me\nbecause I had this setting in my ~/.gitconfig that masked the issue:\n[diff]\n        noprefix = true\nProper solution here is to run system level tests in CI in a reproducibile environment\nor environments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3230#pullrequestreview-1134915934", "body": ""}
{"title": "Parse slack links from PR reviews and code-level comments", "number": 3231, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3231"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3231#pullrequestreview-1134932496", "body": ""}
{"title": "Parse slack links during bulk ingestion", "number": 3232, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3232"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3232#pullrequestreview-1134960338", "body": ""}
{"title": "MakeEventQueueMoreGeneric", "number": 3233, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3233", "body": "Reorg stuff and add antoher queue\nUpdate"}
{"title": "Move to a new module", "number": 3234, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234"}
{"comment": {"body": "Try again", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272056213"}}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272056998"}}
{"comment": {"body": "test3", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272058735"}}
{"comment": {"body": "test4", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272059179"}}
{"comment": {"body": "test6", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272060340"}}
{"comment": {"body": "test7", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272060808"}}
{"comment": {"body": "test8", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272061156"}}
{"comment": {"body": "test9", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272061262"}}
{"comment": {"body": "test110", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272061654"}}
{"comment": {"body": "test12", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272064140"}}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272066535"}}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272066965"}}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272067079"}}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272070691"}}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272070861"}}
{"comment": {"body": "test2", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3234#issuecomment-1272071389"}}
{"title": "Remove 'Current File Threads' panel from sidebar", "number": 3235, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3235", "body": "We temporarily added this to dev builds to show Slack threads, but we're not really using it.  It's confusing, so remove it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3235#pullrequestreview-1135040828", "body": ""}
{"title": "Move to event queue", "number": 3236, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3236", "body": "Testing webhooksa nd stuff"}
{"comment": {"body": "Test webbhooks part 2 you know!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3236#issuecomment-1272074263"}}
{"comment": {"body": "Try agasdfsadfs asdf\r\ndsaf\r\naf\r\nASDFDSAFDSFADFSADFSFADSFDSasdf", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3236#issuecomment-1272141321"}}
{"comment": {"body": "Try agasdfsadfs asdf\r\ndsaf\r\naf\r\nASDFDSAFDSFADFSADFSFADSFDSasdfTry agasdfsadfs asdf\r\ndsaf\r\naf\r\nASDFDSAFDSFADFSADFSFADSFDSasdfTry agasdfsadfs asdf\r\ndsaf\r\naf\r\nASDFDSAFDSFADFSADFSFADSFDSasdfTry agasdfsadfs asdf\r\ndsaf\r\naf\r\nASDFDSAFDSFADFSADFSFADSFDSasdf", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3236#issuecomment-1272142951"}}
{"title": "Add event handler for ingestion", "number": 3237, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3237"}
{"title": "Fix discussion title editing", "number": 3238, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3238"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3238#pullrequestreview-1135141446", "body": ""}
{"title": "Slack ingestion", "number": 3239, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3239"}
{"title": "Update redirect urls", "number": 324, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/324", "body": "Update redirect urls for oauth dance."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/324#pullrequestreview-880718658", "body": ""}
{"title": "Upgrade openapi", "number": 3240, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3240"}
{"title": "Add slack ingestion hooks", "number": 3241, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3241"}
{"title": "Rename classes", "number": 3242, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3242"}
{"title": "Upper bound the Levenshtein distance to handle very large strings", "number": 3243, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3243", "body": "We can shortcut the expensive part of the string comparison when one string is much larger than another."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3243#pullrequestreview-1137930043", "body": ""}
{"comment": {"body": "this takes about 300 ms before my change, and <1 ms after.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3243#discussion_r992621641"}}
{"title": "Revert \"Upgrade openapi (#3240)\"", "number": 3244, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3244", "body": "This reverts commit 1bf232fe38906a805799544773bd34a40bee23dd."}
{"title": "Update CI filters to rebuild everything when gradle settings change", "number": 3245, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3245", "body": "When we change gradle it could impact almost all projects.  For instance, changing the OpenAPI version or build scripts will impact Swift and TS clients."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3245#pullrequestreview-1137958049", "body": ""}
{"comment": {"body": "I stole these from the services CI file.  I don't know if we need all of these or not, my understanding of gradle is pretty weak.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3245#discussion_r992641650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3245#pullrequestreview-1137962406", "body": ""}
{"title": "SM engine caches Git diffs", "number": 3246, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3246", "body": "We tried this before, but the diff size blew out the cache killing memory and performance.\nWe restrict the size of the diffs, so might be better now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3246#pullrequestreview-1137969668", "body": ""}
{"title": "Move SlackThreadIngestionService to lib-slack-ingestion", "number": 3247, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3247"}
{"title": "Further optimize by dropping character transposition correction and streamlining", "number": 3248, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3248", "body": "drop character transposition correction which is really more useful for typos\nabout 2X faster with other minor optimizations"}
{"comment": {"body": "You have me at transposition", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3248#issuecomment-1275186773"}}
{"title": "Add PullRequestIngestionModel.slackIngestionComplete property", "number": 3249, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3249", "body": "To track when we've extracted Slack thread references from all pull requests in a repo"}
{"title": "Fixes missing auth header for V3 client", "number": 325, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/325"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/325#pullrequestreview-880702035", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/325#pullrequestreview-880703327", "body": ""}
{"title": "Basic ipc implemnetation", "number": 3250, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250", "body": "Setup basic IPC communication between VSCode and video app.\nmany features and polish still need to be done but this sets up the models and communication between the apps.\nUploading CleanShot 2022-10-11 at 12.06.25.mp4"}
{"comment": {"body": "@matthewjamesadam Going to stop adding to this PR and branch from here.\r\n`CreateWalkthroughCommand` is still a WIP and will be the next phase. Majority of the logic there is making sure that the data we need from git & video app are available.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#issuecomment-1276559421"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1138252006", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1138253196", "body": ""}
{"comment": {"body": "We've got a lot of the same boilerplate (for port negotiation, connection/disconnection, etc) in 3 places... can we factor that out so it's reusable?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r992848495"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1138282162", "body": ""}
{"comment": {"body": "Standard grpc client setup (including port negotiation) can be refactored out.\r\n\r\nSome aspects about connection/disconnection is a bit more difficult as those are actually API/logic dependant.\r\n\r\nDifferent types of streams are setup for each client with different logic based on the responses.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r992870915"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1139394281", "body": ""}
{"comment": {"body": "I think if we make the APIs for connection/disconnection consistent (which they should be?) we could probably factor that out as well.  We can enforce that the API implement particular interfaces that we can work against...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r993671437"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1139528920", "body": ""}
{"comment": {"body": "This file is still very much a wip. Will most likely be updating when I start implementing the functionality for this page.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r993765880"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1139539454", "body": ""}
{"comment": {"body": "I'll need to think more about this.\r\nThe issue is we different streams to determine connected/disconnected states.\r\n\r\nSome may be unidirectional streams, some are duplex streams, etc...\r\nWe could potentially make all these consistent but requires a larger refactor which would mess with the hub as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r993773044"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141183023", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141187128", "body": ""}
{"comment": {"body": "For sure.  We can make this consistent in a future PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r994906816"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141194837", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141195471", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141198552", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141199938", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141201903", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141202426", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141202737", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141202933", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141219237", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141221357", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141224631", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141228774", "body": ""}
{"comment": {"body": "I'm not sure why this is named as a Command -- we invoke it directly as a function.  Just rename it to CreateWalkthrough?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r994934113"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141229980", "body": ""}
{"comment": {"body": "Can StartWalkthrough pass this in so we don't need to do this again?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r994935009"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141231144", "body": ""}
{"comment": {"body": "What are `cachedReferences` for?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r994935806"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141237165", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141238006", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141244956", "body": ""}
{"comment": {"body": "These are all the code references stored *before* one starts recording on the video app.\r\nThe last item in this cached references will be the *first* of the running references.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r994945615"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141245344", "body": ""}
{"comment": {"body": "I am not sure that a stream is the right model for the recommended/teammember stuff.  This feels like a one-off async action?   Is the only reason we're doing this because the TeamMemberStore is a stream, and we want to join the team member data?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r994945858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141246516", "body": ""}
{"comment": {"body": "Will do when we're wrapping up the feature. This was done for testing purposes. aka manually creating a walkthrough from command palette.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r994946597"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141246834", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141289812", "body": ""}
{"comment": {"body": "I see. Good idea!\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/5c3d1adb-597d-4581-a7e2-39949228d944?message=ae01d692-56aa-4f5c-81b4-2953a2e4086e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r994976042"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141416167", "body": ""}
{"comment": {"body": "Will refactor this in another PR. For now, would like to refetch repo for testing purposes (separate commands)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r995061337"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141416839", "body": ""}
{"comment": {"body": "Yup. This could technically be a one-off. Similar situation in create discussion.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#discussion_r995061801"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141470878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141477742", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141552666", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3250#pullrequestreview-1141553631", "body": ""}
{"title": "Add new infra slack queues", "number": 3251, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3251"}
{"title": "Fix open api code gen", "number": 3252, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3252"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3252#pullrequestreview-1138121724", "body": ""}
{"title": "Update RepoStore Instance", "number": 3253, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3253", "body": "Update RepoStore instance method to follow other patterns.\naka change RepoStore.instance().... to RepoStore.instance...."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3253#pullrequestreview-1138155889", "body": ""}
{"title": "Fix action buttons", "number": 3254, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3254"}
{"title": "Add slack queue processing", "number": 3255, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3255", "body": "Need to be able to trigger slack and team ingestion after app install."}
{"title": "Capture camera and add to preview", "number": 3256, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#pullrequestreview-1138212433", "body": ""}
{"comment": {"body": "The whole file was renamed, but a lot was re-written too so best to review the whole thing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#discussion_r992818943"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#pullrequestreview-1138213424", "body": ""}
{"comment": {"body": "Keeping here as a followup. I need to figure out how to handle these types of errors on macOS where the dictionary keys apparently don't exist", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#discussion_r992819355"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#pullrequestreview-1138214540", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#pullrequestreview-1138214859", "body": ""}
{"comment": {"body": "Couldn't figure out any other way to do this outside of KVO", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#discussion_r992820376"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#pullrequestreview-1138215460", "body": ""}
{"comment": {"body": "Saucy \ud83e\udd23", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#discussion_r992820772"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#pullrequestreview-1138231156", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#pullrequestreview-1141181955", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3256#pullrequestreview-1141223741", "body": ""}
{"title": "open temporary access to prod db", "number": 3257, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3257", "body": "Doug want's to do some data mining magic on prod data. \nWe should use the RO instance databasestack-mainapp32bc6243-1s2nwgqjjscqp.cluster-ro-czdnrjl0hm44.us-west-2.rds.amazonaws.com"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3257#pullrequestreview-1138249964", "body": "You complete me."}
{"title": "Include PullRequestIngestions where slackIngestionComplete is null", "number": 3258, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3258"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3258#pullrequestreview-1138248369", "body": ""}
{"title": "Implement ingestLinkedSlackThreads", "number": 3259, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3259"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3259#pullrequestreview-1138263848", "body": ""}
{"title": "Drop id from graphql queries", "number": 326, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/326", "body": "Addresses https://github.com/NextChapterSoftware/unblocked/pull/313#discussion_r804344071"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/326#pullrequestreview-880737477", "body": ""}
{"title": "Include PullRequestIngestions where slackIngestionComplete is null", "number": 3260, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3260", "body": "For real this time"}
{"title": "Generalize some slack ingestion code", "number": 3261, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3261"}
{"title": "Filter out insignificant Git commands from command log report in VSCode", "number": 3262, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3262"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3262#pullrequestreview-1139653812", "body": ""}
{"title": "Add walkthrough mode", "number": 3263, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#pullrequestreview-1141227057", "body": ""}
{"comment": {"body": "This class will ultimately be responsible for pulling all the media streams together and dumping them out to a file", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#discussion_r994932870"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#pullrequestreview-1141228108", "body": ""}
{"comment": {"body": "When the tracked window is closed we consider the session done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#discussion_r994933642"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#pullrequestreview-1141228850", "body": ""}
{"comment": {"body": "This is the menubar pill ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#discussion_r994934161"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#pullrequestreview-1141241206", "body": ""}
{"comment": {"body": "This top stack necessary?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#discussion_r994943052"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#pullrequestreview-1141244064", "body": ""}
{"comment": {"body": "It might not be, but I couldn't figure out how to get the alignment working properly without it. The MenuBar does super weird things with constraints so it's pretty touchy\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a214b633-0bdc-4c4a-8d2e-9adc517fa24f?message=530311ab-3734-42e8-81ad-d54a2234dff1).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#discussion_r994944984"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3263#pullrequestreview-1141250111", "body": ""}
{"title": "VSCode hangs downloading source marks for Sentry", "number": 3264, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3264", "body": "\nThis change from 10 MB to 50 MB is a workaround solution. Even with this change it take ~40 seconds\non getsentry/sentry to download all the source mark/point data.\nBetter solution is to prune the response content to make much leaner."}
{"title": "[SENSITIVE CHANGE]: Always run installer when file download completes", "number": 3265, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3265", "body": "Summary\nThe previous update logic did two things that prevented a successful upgrade in the scenario where the download fails:\n1. Always save the latest info regardless of download status\n2. Bypass download if the previously received update version is >= the most recent version\nThis combination caused the download and upgrade process to short-circuit. \nThe new logic resolves this issue:\n1. Still always save the latest info regardless of download status so there's a fallback in the settings menu\n2. Always download the installer if we don't have it for the most recent version"}
{"comment": {"body": "VersionStore is my friend.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3265#issuecomment-1276534548"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3265#pullrequestreview-1139499086", "body": ""}
{"comment": {"body": "I'm finding the logic here a bit hard to understand, but it seems something like, \"If we've already downloaded the correct build, use it, otherwise down it now\" ?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3265#discussion_r993744378"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3265#pullrequestreview-1139499659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3265#pullrequestreview-1139678759", "body": ""}
{"comment": {"body": "just so I make sure that I understand this code (I mostly removed the VersionStore from the iOS prototype because I assumed that we would use TestFlight, which I think @pwerry agreed we should use).\n\n\n\nThe VersionStore is polling the service for newer releases. If it finds a newer one than current, it kicks off the download, once done, opens the Install dialog?\n\n\n\nThis specific change fixes a potential race if the download had failed?\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/70414bfb-84fd-4160-bfba-c8ad7dd8e716?message=7db710a5-a293-47fd-b340-5a5ace841d86).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3265#discussion_r993868404"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3265#pullrequestreview-1141097406", "body": ""}
{"comment": {"body": "@matthewjamesadam That's correct.\r\n\r\n@cancelself Your interpretation is correct, with one minor caveat that deserves some explanation: installs are \"automatic\", in that we launch an installer process that tries to run the install itself. If that fails, we fall back to open the installer pkg.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3265#discussion_r994845610"}}
{"title": "[BREAKS API ON MAIN] Update API to include additional code reference data", "number": 3266, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266", "body": "Added additional metadata as backup for when a source mark is not generated.\nThis will still allow the client to render the name of the file & a link to GH."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1139654625", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1139658591", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141179518", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141383945", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141384329", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141570692", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141589078", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141589915", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141591339", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141610714", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141617409", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141631798", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141633948", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141635559", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141635694", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3266#pullrequestreview-1141645930", "body": ""}
{"title": "Threads can be associated with multiple pull requests", "number": 3267, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267", "body": "This pr is a first pass at thread -> n pull requests.\nThe join table is the first pass at this.\nWhat needs to be done:\n1. Migration\n2. Api updates\n3. Removing setting pullRequest in thread model.\n4. Back filling pull requests upon pr ingestion."}
{"comment": {"body": "You had me at 'multiple pull requests'", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267#issuecomment-1276653842"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267#pullrequestreview-1139636858", "body": ""}
{"comment": {"body": "Should we re-order these indexes so that thread comes last? That way the indexes support queries that ask \"Give me all threads associated with this given PR\" which I think will be more common than queries that ask \"Give me all PRs associated with this given thread\".\r\n\r\nAlso we might only need one of these since they're so similar. I think a query that uses one could easily use the other to answer the same question.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267#discussion_r993841401"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267#pullrequestreview-1141492729", "body": ""}
{"comment": {"body": "Totally missed this. You're right.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267#discussion_r995112660"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267#pullrequestreview-1141506077", "body": ""}
{"comment": {"body": "Actually we'll def need an index to answer  \"Give me the PR(s) associated with this given thread\" since that's something we do right now, but we can probably get away with a non-unique index on thread only.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d78c58b1-91ba-46a3-a6e9-4d03e511443a?message=a24d7f3e-fdce-44ed-83e5-df4aabedd68e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267#discussion_r995120689"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267#pullrequestreview-1142795027", "body": ""}
{"comment": {"body": "\n\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/89c7bf91-9f88-4cc9-b07a-4acd8ab3dac0)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d78c58b1-91ba-46a3-a6e9-4d03e511443a?message=6ff343a0-a57b-42e1-a8e5-dc602ef71bd3).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3267#discussion_r996003909"}}
{"title": "Add slack message urls", "number": 3268, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3268", "body": "Validated with ingestion locally."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3268#pullrequestreview-1139801734", "body": ""}
{"comment": {"body": "nit: since this is under a Message model already I would just shorten this to `slackUrl`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3268#discussion_r993956346"}}
{"title": "Add floating camera view to screen sharing mode", "number": 3269, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3269"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3269#pullrequestreview-1141256107", "body": ""}
{"comment": {"body": "I have no idea why Xcode did this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3269#discussion_r994953158"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3269#pullrequestreview-1143064318", "body": ""}
{"title": "Generate relaxed json serializer from templates", "number": 327, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/327", "body": "Small cleanup thing"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/327#pullrequestreview-880754422", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/327#pullrequestreview-880754496", "body": "With comments"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/327#pullrequestreview-880754973", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/327#pullrequestreview-880755966", "body": ""}
{"title": "Add laminator migrator", "number": 3270, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3270", "body": "A good friend once told me that a thread should not precluded from having multiple pull requests.\nIt has an inviolable right to that, and you know what, he's right."}
{"comment": {"body": "You had me at 'laminator'", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3270#issuecomment-1277874084"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3270#pullrequestreview-1139979755", "body": ""}
{"title": "Add SlackChannelPreferencesModel", "number": 3271, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3271", "body": "Backend db models to support saving which slack channels we should ingest\n\nIf we ultimately end up having an \"all public models\" option, I'm thinking we can create a SlackTeamModelPreferencesModel to save that."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3271#pullrequestreview-1141086828", "body": ""}
{"comment": {"body": "This is dependent on us somehow ingesting all channels before hand. I suppose your plan is maybe during initial retrieval for client display?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3271#discussion_r994838601"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3271#pullrequestreview-1141121728", "body": ""}
{"comment": {"body": "Yeah I'm thinking we'll create the db records on the fly as they come back from the API before we pass it back to the front end", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3271#discussion_r994861624"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3271#pullrequestreview-1141180073", "body": ""}
{"comment": {"body": "Chatted IRL with Rashin. Slack service will quickly ingest channels during onboarding, so the API service should pull the channels from the DB.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3271#discussion_r994901902"}}
{"title": "Upgrad admin settings", "number": 3272, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3272"}
{"title": "optimize backfill", "number": 3273, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3273"}
{"title": "Add SlackTeamPreferencesModel", "number": 3274, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3274"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3274#pullrequestreview-1141509389", "body": ""}
{"title": "Add SlackChannelStore.findBySlackTeam", "number": 3275, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3275", "body": "We'll use this to list the slack channels during onboarding"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3275#pullrequestreview-1141508961", "body": ""}
{"title": "ADd infra queue", "number": 3276, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3276"}
{"title": "Initial channel singestion", "number": 3277, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3277"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3277#pullrequestreview-1141451406", "body": ""}
{"comment": {"body": "Here is where we trigger channels ingestion.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3277#discussion_r995084886"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3277#pullrequestreview-1141458092", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3277#pullrequestreview-1141459172", "body": ""}
{"comment": {"body": "You can key off of this if ingestion is complete.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3277#discussion_r995090603"}}
{"title": "Improve backfill", "number": 3278, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3278"}
{"title": "API compat test must use current spec, not HEAD spec", "number": 3279, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3279", "body": "The original intention was to test the current spec against origin/main, not against HEAD which is often behind your current changes locally."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3279#pullrequestreview-1141550139", "body": "I am incredibly moved by this pr."}
{"title": "Add fields to Message model", "number": 328, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/328", "body": "Add more required fields to the Message model needed to build client UI"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/328#pullrequestreview-880758011", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/328#pullrequestreview-880765090", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/328#pullrequestreview-880799920", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/328#pullrequestreview-881990695", "body": ""}
{"title": "update", "number": 3280, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3280"}
{"title": "Change to DESC_NULLS_FIRST", "number": 3281, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3281", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3281#pullrequestreview-1141577446", "body": ""}
{"title": "Slack threads in the PR view", "number": 3282, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282", "body": "\n\n\n\nRefactor some PR view components to reuse for the slack view\nRewrite the thread header section to include additional data (participants and # comments)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1143094130", "body": ""}
{"comment": {"body": "We can't combine this with the one below, just pass `readOnly={readOnly}` in either way to determine if this is a read-only view or not?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#discussion_r996210689"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1143098196", "body": ""}
{"comment": {"body": "OK, I see how you've structured the props for the chain of views into mutable and immutable variants... it's a bit unfortunate that we can't just pass the props along the chain and have to have two calls at every level...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#discussion_r996213781"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1144522606", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1144525424", "body": ""}
{"comment": {"body": "Surprised this worked in the past... Guess stream.combine may mess with the types.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#discussion_r997310640"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1144527528", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1144534031", "body": ""}
{"comment": {"body": "It looks like the MessageView already takes in this readOnly prop? What other benefits do we get with this if clause?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#discussion_r997316285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1144568084", "body": ""}
{"comment": {"body": "If we're inspecting `threadInfo` to determine if the thread is a slack thread, I think maybe passing `isSlackThread` down the props chain isn't necessary...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#discussion_r997340096"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1144573046", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1144589152", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1144600332", "body": ""}
{"comment": {"body": "The MessageView component doesn't accept certain props if readOnly is true", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#discussion_r997362751"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#pullrequestreview-1144603226", "body": ""}
{"comment": {"body": "Yeah I do think it's odd that there are two places where we indicate a slack thread, the threadType and the `threadInfo.slack` config. It was unclear to me which one should be the source of truth -- I ended up looking at the threadType but in this block specifically since we reference a value in the `threadInfo.slack` I added the null check here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3282#discussion_r997364857"}}
{"title": "Backfill null ThreadPullRequestModel.pullRequest when pull request is ingested", "number": 3283, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3283", "body": "This field is null when we've ingested a slack thread before the associated pull request has been ingested"}
{"comment": {"body": "Think we need to update SlackThreadModelService to create the ThreadPullRequestModel even if the pull request doesn't exist", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3283#issuecomment-1278282199"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3283#pullrequestreview-1142694014", "body": ""}
{"title": "Alter sourcemark model to represent the concept of a \"FileMark\"", "number": 3284, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284", "body": "Changes\n\nlabels a bunch of fields optional to support FileMark concept, but does not actually make optional\nlabels other fields optional for bandwidth and storage optimization, but does not actually make optional\nintroduce new getRepoSourceMarks operation to replace getSourceMarks, again for bandwidth and storage optimization"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#pullrequestreview-1142688588", "body": ""}
{"comment": {"body": "So we'd be making `n` paged requests (one for each repo) instead of a single paged request?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#discussion_r995931996"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#pullrequestreview-1143227102", "body": ""}
{"comment": {"body": "Yes. Where N=1 typically.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#discussion_r996353863"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#pullrequestreview-1144469665", "body": ""}
{"comment": {"body": "We'll have to subscribe to `n` repo channels as well.  Just FYI the client side for this will take a bit of work (a few hours probably).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#discussion_r997271510"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#pullrequestreview-1144945660", "body": ""}
{"comment": {"body": "Hmm, seems we were already passing a single `repoId` to the `getSourceMarks` API in all cases anyway. So the API call site in VSCode is trivial.\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/fb2a002b-e0b4-4ff0-8617-f1d207894f2c?message=65c64e1e-da5c-4c26-aad8-b95ee695868b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#discussion_r997609793"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#pullrequestreview-1146162910", "body": ""}
{"comment": {"body": "You're right!  I think I misread the existing API definition", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#discussion_r998465722"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#pullrequestreview-1146164094", "body": ""}
{"comment": {"body": "Working on a PR, I'll get you to review later this morning.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/fb2a002b-e0b4-4ff0-8617-f1d207894f2c?message=8c332ebc-334c-4d90-862b-0abcb7be564b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3284#discussion_r998466544"}}
{"title": "Create ThreadPullRequestModels even when the pull request has yet-to-be ingested", "number": 3285, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3285", "body": "Right now the logic will skip ingesting a slack thread if the referenced pull request doesn't exist. This isn't ideal since it's possible slack ingestion happens before pull request ingestion.\nWe should create the ThreadPullRequestModels even if the pull request doesn't exist, as long as we have the repo and number. When PR ingestion eventually creates that pull request, we'll backfill the ThreadPullRequestModel.pullRequest field here https://github.com/NextChapterSoftware/unblocked/pull/3283"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3285#pullrequestreview-1141793494", "body": ""}
{"title": "We should not be conflating slack threads with pr threads", "number": 3286, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3286"}
{"title": "[BREAKS API ON MAIN] Fix typo in API name", "number": 3287, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3287"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3287#pullrequestreview-1141650669", "body": ""}
{"title": "IMprove admin console for multiple pull requets", "number": 3288, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3288"}
{"title": "Screen recording with mic but no camera", "number": 3289, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#pullrequestreview-1143069984", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#pullrequestreview-1143071729", "body": ""}
{"comment": {"body": "I don't fully understand what this is doing.  If there's no writer, we hold onto a description for the sample, and then quit?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#discussion_r996194129"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#pullrequestreview-1143072238", "body": ""}
{"comment": {"body": "Should this code be identical to writeVideoSample?  Or do we assume that we'll always get a video sample before any audio samples?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#discussion_r996194839"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#pullrequestreview-1143072505", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#pullrequestreview-1143073251", "body": ""}
{"comment": {"body": "Correct. The sample buffer description is used when the recording starts to provide hints to the writer about the source configuration so it picks the optimal intermediate and destination transcoding formats", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#discussion_r996195122"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#pullrequestreview-1143074278", "body": ""}
{"comment": {"body": "To clarify, frames from the screen/camera/mic are flowing into the recorder before the recording actually starts (writer is instantiated). We're saving the latest description as a primer for the transcoder", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#discussion_r996195894"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#pullrequestreview-1143080656", "body": ""}
{"comment": {"body": "The thinking here is that we need video sample data before we start writing audio samples. If it were reversed, it would be possible to have blank frames at the start of the \"video\" with audio playing in the background", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3289#discussion_r996200592"}}
{"title": "Update deployment config settings per env", "number": 329, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/329", "body": "Setup deployment configs per environment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/329#pullrequestreview-880760872", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/329#pullrequestreview-880761764", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/329#pullrequestreview-880762079", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/329#pullrequestreview-880762997", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/329#pullrequestreview-880763080", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/329#pullrequestreview-880763565", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/329#pullrequestreview-880766066", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/329#pullrequestreview-880767163", "body": ""}
{"title": "Upserts shold be in stores", "number": 3290, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3290"}
{"title": "Slack thread were not showing pu", "number": 3291, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3291", "body": "A pull request query for slack threads should now be using ThreadPullRequestStore to query thread to pull request association.\n"}
{"title": "Fix PullRequestStore.hasSlackThreads", "number": 3292, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3292"}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/3291", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3292#issuecomment-1279321302"}}
{"title": "Camera track is being written out but is not rendered", "number": 3293, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3293"}
{"comment": {"body": "Deprecated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3293#issuecomment-1301284338"}}
{"title": "Embed walkthrough app in the hub", "number": 3294, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3294", "body": "I will centralize assets in a followup PR.\nThis one just gets the app embedded so we can start working with permissions effectively"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3294#pullrequestreview-1143075330", "body": ""}
{"comment": {"body": "This will be your regular scheduled request to DRY out our icons, putting them in a single asset folder in git, and possibly building them into a single asset bundle.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3294#discussion_r996196631"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3294#pullrequestreview-1143075855", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3294#pullrequestreview-1143085697", "body": ""}
{"comment": {"body": "I'm going to merge everything into a single asset bundle as my next trick", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3294#discussion_r996204345"}}
{"title": "Update slack installations api", "number": 3295, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3295"}
{"title": "Basic Video Upload", "number": 3296, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296", "body": "Basic end to end flow for video uploading.\nThere's definitely overlap with createKnowledge but also a bunch of custom logic here.\nNext few PRs will be doing some refactoring to share code between create walkthrough and knowledge... (collaborators temporarily commented out due to this)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1144578674", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1144588973", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1144667261", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146192516", "body": ""}
{"comment": {"body": "This is great!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r998486909"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146193561", "body": ""}
{"comment": {"body": "Also set cancellable to `nil`. Not strictly necessary because of scope, but good habit to get into", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r998487651"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146432473", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146475782", "body": ""}
{"comment": {"body": "Feels like we should trigger this off of the source point data itself -- should this not display if the anchorSourcePoint has no range (I think that's the important change with the file-based points?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r998678916"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146476067", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146477389", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146478106", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146488177", "body": ""}
{"comment": {"body": "Is pretty much all of this going to be moved out into a reusable class?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r998690630"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146495433", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146612338", "body": ""}
{"comment": {"body": "Yeah. Working on this right now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r998779466"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146612477", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146612682", "body": ""}
{"comment": {"body": "That will be coming in the future. We don't quite have this capability atm.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r998779736"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146630941", "body": ""}
{"comment": {"body": "This is going to be in a separate PR though. Don't want to add to this especially since it would touch CreateKnowledge. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r998793953"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1146643965", "body": ""}
{"comment": {"body": "This will soon be moving to another location that will be shared across multiple components.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r998803889"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148094578", "body": ""}
{"comment": {"body": "@pwerry StatusBar was not being hidden so there could be a slight difference in the displayed time here vs actually recorded time.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999810403"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148203760", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148205353", "body": ""}
{"comment": {"body": "Just use path.basename?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999886869"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148218358", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148219063", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148221764", "body": ""}
{"comment": {"body": "Not quite following...\r\nWe need to create the FilePath before we can call base name.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999897928"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148225015", "body": ""}
{"comment": {"body": "Won't this result in multiple team members for a single repo (if there are multiple video references for a single repo)?  Should we first make a deduped list of the team/repo tuples, and then get the team members from that?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999900095"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148228707", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148229440", "body": ""}
{"comment": {"body": "Yeah. I have this addressed in a separate PR which refactors the `KnowledgeFormCollaborators` and the data that powers it.\r\n\r\nIn the situation where there are multiple references for a single repo, this will generate multiple streams but that seems necessary as recommended teams are generated per file.\r\n\r\nThe approach I took was to take all these streams and dedupe the resulting recommended team members..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999903099"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148232435", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148233189", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148234638", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148235427", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148238452", "body": ""}
{"comment": {"body": "If we can't determine a file type shouldn't it just be `application/octet-stream` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999909482"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148240608", "body": ""}
{"comment": {"body": "Yikes, we're uploading a video in base64?  That will be super inefficient -- do you know why we're doing this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999910851"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148243520", "body": ""}
{"comment": {"body": "From what I could tell, this isn't *actually* being uploaded.\r\nIf you dig into `AssetUploader` you'll notice that we actually use fetch to upload and the payload is the buffer.\r\n\r\nNot sure *what* the url is used for but this was done for the image assets. Maybe @rasharab has context?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999912802"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148244632", "body": ""}
{"comment": {"body": "Technically yes but that file-type wouldn't be useful for us.\r\n\r\nI think it might be safer to throw an exception here if we fail to parse the video buffer that we created.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999913524"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148254294", "body": ""}
{"comment": {"body": "\ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999920033"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148257953", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148309088", "body": ""}
{"comment": {"body": "You mean the status bar counter stays up after you click stop?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999958721"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1148310134", "body": ""}
{"comment": {"body": "Yup. We were depending on the app terminating to \"hide\" the status bar.\n\n\n\nWith the small change to how termination occurs, status bar stays up for a fraction of a second after you click stop.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/3b9c63e6-2927-417d-a9d1-8c493b1956bd?message=fe5c8c37-8b31-4627-8685-e61d1c881ce9).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r999959491"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#pullrequestreview-1149619810", "body": ""}
{"comment": {"body": "Ok maybe it makes sense to hide it as soon as stop is clicked then. I will make that change\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/3b9c63e6-2927-417d-a9d1-8c493b1956bd?message=0226c871-1995-4acb-af57-3c3943de71c6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3296#discussion_r1000880266"}}
{"title": "[API] Define Slack configuration endpoints", "number": 3297, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297", "body": "To support: \n"}
{"comment": {"body": "Can you outline how this is supposed to work?\r\n\r\nMy guess is that the setup UI is supposed to call `getSlackChannels` to get the current config, then call `postSlackConfiguration` to update it if changes are made.\r\n\r\n* For `getSlackChannels`, how do we know what `slackId` to query for?  Should this just return all the Slack config information available for this team?\r\n* I am not sure why the GET for this returns a series of channels, while the POST updates a config object.  Shouldn't the GET call return a config object instead?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297#issuecomment-1279603538"}}
{"comment": {"body": "> For getSlackChannels, how do we know what slackId to query for? Should this just return all the Slack config information available for this team?\r\n\r\nTBD, probably through a `getSlackTeams` operation\r\n\r\n> I am not sure why the GET for this returns a series of channels, while the POST updates a config object. Shouldn't the GET call return a config object instead?\r\n\r\nThis endpoint is to provide the list of all available channels for the slack team. Sounds like you're thinking of a `getSlackConfiguration` operation, which we need", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297#issuecomment-1281128241"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297#pullrequestreview-1144570687", "body": "Thank you."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297#pullrequestreview-1144636051", "body": ""}
{"comment": {"body": "Is a slack team === slack workspace?\r\n\r\nIf so, maybe rename?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297#discussion_r997388308"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297#pullrequestreview-1144652202", "body": ""}
{"comment": {"body": "Yes.\r\nThat's how slack's api refers to them. \r\nNot sure why, but I'd rather not do renames.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297#discussion_r997399789"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297#pullrequestreview-1144667303", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3297#pullrequestreview-1144810377", "body": ""}
{"title": "Unblocked Mobile", "number": 3298, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3298", "body": "One of three initial checkins for the Unblocked iPhone experience.\n1: Add UnblockedMobile target with native login + web feed.\n2: Single-sign (in-app web browser + native use same AuthToken).\n3: Native (Pull) notifications (APNS is later)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3298#pullrequestreview-1143230954", "body": ""}
{"comment": {"body": "The ifdefs are going to make stuff a bit hard to read eventually. Creating interop proxies for all of this stuff is probably a bit over the top too. \n\nI notice a lot of this code centers around the appdelegate. Did that thing become a UIApplicationDelegate?\n\nWondering if we can centralize as much of the interop code as possible to one place", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3298#discussion_r996358990"}}
{"comment": {"body": "Check the AppDelegate class. There's that funny token factory dance that has to be done to set the auth tokens. It's spaghetti right now so bring your hard hat but I hope to refactor that with Combine eventually", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3298#discussion_r996359357"}}
{"comment": {"body": "Yeah just whack it all up front. I think we handle all this in the UnblockedNotifications class. I'll explain the exact flow on Monday but it's super platform dependent", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3298#discussion_r996359501"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3298#pullrequestreview-1143233109", "body": ""}
{"comment": {"body": "so cool to be replying in the mobile app while overlooking the city at cypress outlook. \ud83d\udc3c\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/3d40e85d-66a9-42db-b74b-fca8c0923f89?message=32e9c221-680d-4b67-b911-eafa5dffb61f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3298#discussion_r996361659"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3298#pullrequestreview-1143240222", "body": ""}
{"comment": {"body": "NS* **-> UI*** is most of it. There are some install differences too. Hope that we can just have very lightweight shims or extensions.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ad292143-11da-46b4-ab23-d92e02b31f6a?message=3b05e745-4459-4ef9-89ec-315a3e4b1ea0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3298#discussion_r996371392"}}
{"title": "Why does Apple do this to us", "number": 3299, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3299", "body": "I can't really see this working to fix the issue. If it does it will be surprising"}
{"title": "Refactor SCSS Base", "number": 33, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/33", "body": "Introduce SCSS Base which takes the variables and sets defaults.\nAlso moved normalization code into SCSS Base"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/33#pullrequestreview-852345261", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/33#pullrequestreview-853412935", "body": ""}
{"comment": {"body": "Just curious, what is this for?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/33#discussion_r785192353"}}
{"title": "Add author to message and thread", "number": 330, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330", "body": "Addresses https://github.com/NextChapterSoftware/unblocked/pull/315#discussion_r804949221"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-880777413", "body": ""}
{"comment": {"body": "Will fix later", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#discussion_r805076345"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-880777812", "body": ""}
{"comment": {"body": "FWIW don't like this rule, though I get why its a thing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#discussion_r805076643"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-881967866", "body": ""}
{"comment": {"body": "I like what the rule is trying to accomplish, but I think the trigger length is too small. \r\n\r\nAlso if you use a data class to encapsulate the parameters there's no limit to the length", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#discussion_r806080368"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-881982090", "body": ""}
{"comment": {"body": "Yeah I thought about that. Not opposed to it, but at that point I'm just adding a level of indirection just to get around this rule.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#discussion_r806090601"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-881983928", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-881984465", "body": ""}
{"comment": {"body": "I think the real solution here is to implement separation of model data classes and the database layer (so that creating a `Message` object doesn't trigger an immediate save) which I've been meaning to get to.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#discussion_r806092216"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-881984620", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-882002645", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-882007469", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-882227867", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-882258684", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-882262600", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-882279130", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/330#pullrequestreview-882281982", "body": ""}
{"title": "Skip install", "number": 3300, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3300"}
{"title": "Move to bulk Git commands to reduce process exec overhead", "number": 3301, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301", "body": "No-op incremental recalculation should be extremely fast\n\nResults\n\nNo improvement on full recalculation, as expected.\nIncremental wallclock runtime recalculation improvement:\nexpo/expo: 40%\nunblocked: 30%"}
{"comment": {"body": "I want to thank you Richie. Not because of this change, but because of the litany of changes you've been making. Keep on hauling dude.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#issuecomment-1280998402"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#pullrequestreview-1144320743", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#pullrequestreview-1144321794", "body": ""}
{"comment": {"body": "This is where the infamous Richie todo bubble would be nice", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#discussion_r997170147"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#pullrequestreview-1144476023", "body": ""}
{"comment": {"body": "Is there a reason we don't just fix this where we're constructing the commands?  This feels like it could introduce hard-to-figure-out bugs because the command you supply won't be the one that actually executes...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#discussion_r997274734"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#pullrequestreview-1144530796", "body": ""}
{"comment": {"body": "This function has no behavioural impact.\r\n\r\nThis is just used for the CommandLog, which generates a report in verbose mode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#discussion_r997314127"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#pullrequestreview-1144532181", "body": ""}
{"comment": {"body": "for example, it generates a report like this with the real SHAs deduplicated\r\n\r\n```\r\n   '  count     sum     max     avg cmd\\n' +\r\n    '  15794  168996      37    10.7 git rev-parse SHA:FILE\\n' +\r\n    '   1879  105543     211    56.2 git rev-list --dense --ancestry-path --first-parent SHA ^SHA -- FILE\\n' +\r\n    '   1273   15541      26    12.2 git show SHA:FILE\\n' +\r\n    '    698   10640     500    15.2 git diff --diff-filter=DR --find-renames=50% --name-status SHA^ SHA --\\n' +\r\n    '    118    1451      19    12.3 git branch --contains SHA\\n' +\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#discussion_r997315022"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#pullrequestreview-1144557863", "body": ""}
{"comment": {"body": "Ah got it, sorry I misread where this was used \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3301#discussion_r997332911"}}
{"title": "Use correct mobile bundle id", "number": 3302, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3302"}
{"title": "ingest more team info", "number": 3303, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3303", "body": "Some team attributes, icons etc. were not being ingested.\nLet's do it now..."}
{"title": "Remove alpha channel from icons", "number": 3304, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3304"}
{"title": "Declare app encryption use", "number": 3305, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3305"}
{"title": "Add link to slack team", "number": 3306, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3306"}
{"title": "Add permissions pre-requisites for walkthrough app", "number": 3307, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3307", "body": "Uses the same permissions manager as previous iterations.\nThere are some notable stateful things to be aware of:\n1. The user could complete permissions in any order. \n2. Screen recording requires an app restart, but Mic and Camera do not. If the user grants screen recording first, it is therefore possible for the app to continue running before and after all permissions are granted\n3. Cancelling the permissions process terminates the app\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3307#pullrequestreview-1144784667", "body": ""}
{"title": "Standardize user secrets", "number": 3308, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3308"}
{"title": "Avoid re-validating untrusted points over and over on each client", "number": 3309, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3309", "body": "Changes\nIf at least one point is trusted and exists in the tree/repo then ignore all the other point.\nThis is expected to significantly reduce the frequency of these Git commands:\n- git rev-parse SHA:FILE\n- git show SHA:FILE\n\nResults for getsentry/sentry\n\n\nApprox 7.3% faster.\n\n\nBefore (1624.1 seconds)\n```\n  count     sum     max     avg cmd\n\n\n\n13037  139753      55    10.7 git rev-parse SHA:FILE\n   9926  102085      35    10.3 git show SHA:FILE\n```\n\nAfter (1504.5 seconds)\n```\n  count     sum     max     avg cmd\n\n\n2284   19474      23     8.5 git rev-parse SHA:FILE\n     34     337      14     9.9 git show SHA:FILE\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3309#pullrequestreview-1144816003", "body": ""}
{"comment": {"body": "This is now 96% of SM runtime.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3309#discussion_r997513649"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3309#pullrequestreview-1144816894", "body": ""}
{"comment": {"body": "This is 1.28% of SM runtime.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3309#discussion_r997514296"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3309#pullrequestreview-1144906885", "body": ""}
{"comment": {"body": "Really cool findings....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3309#discussion_r997580897"}}
{"title": "Add ability to get comments and files for a pull request from the rest api", "number": 331, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/331", "body": "Turns out the graphql query wont return commit id or sha for files for our github app. Lets use the rest api to get comments and files for a pull request, because we can get that info through the github rest api. "}
{"comment": {"body": "Just going to merge this since I'm blocked, will address any comments in another PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/331#issuecomment-1039402606"}}
{"title": "Fix screen capture filter", "number": 3310, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3310", "body": "Make screen capture more betterer"}
{"title": "Async load all sourcemarks", "number": 3311, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3311", "body": "For Sentry -- this ensures that the sidebar loads much quicker on startup.  Make the initial SM load in the background, so that it doesn't affect the loading of any other data.  We could potentially do this for all data stores, but it seemed more complicated to integrate this with the ChannelPoller, the risk was higher (larger blast radius), and SourceMark loading is the only place where we're loading significant data.\nThis supersedes https://github.com/NextChapterSoftware/unblocked/pull/2444"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3311#pullrequestreview-1144860296", "body": "Nice. Works!"}
{"title": "Fix rendering slack icons in large viewports", "number": 3312, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3312", "body": "before:\n\nafter:\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3312#pullrequestreview-1144885821", "body": ""}
{"title": "Only ingest slack threads containing replies", "number": 3313, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3313", "body": "Still needs work since threads with replies may not have valuable information, but let's start with this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3313#pullrequestreview-1144872481", "body": ""}
{"title": "Show number of slack threads for repo in admin console", "number": 3314, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3314"}
{"title": "EncryptSlackSecrets", "number": 3315, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3315", "body": "Encrypt slack secrets\nNeed 4096-bit rsa key to encrypt slack secrets, we were using 512, but slack tokens will not work with that as the maximum bytes size for a 512-bit rsa key is 64 bytes."}
{"title": "SM engine does not reprocess stop points", "number": 3316, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3316", "body": "\nChanges\n\ndo not recalculate previous stopped points\n\nResults for getsentry/sentry\n\n\n7.3X incremental runtime improvement  \n\n\nbefore (1,504.6 sec)\n```\n  count     sum     max     avg cmd\n\n\n\n6341 1379272     654   217.5 git rev-list --dense --ancestry-path --first-parent SHA ^SHA -- FILE\n   1625   18717      31    11.5 git diff --diff-filter=DR --find-renames=50% --name-status SHA^ SHA --\n```\n\nafter (207.3 sec)\n```\n  count     sum     max     avg cmd\n\n\n 68   17735     571   260.8 git rev-list --dense --ancestry-path --first-parent SHA ^SHA -- FILE\n 37     390      22    10.5 git diff --diff-filter=DR --find-renames=50% --name-status SHA^ SHA --\n\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3316#pullrequestreview-1144910481", "body": ""}
{"title": "[Slack] Add dashboard routes", "number": 3317, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317", "body": "Update dashboard routes to handle settings sections\nAdd placeholder for Slack UI \nAdd feature flag for Slack UI (flagged off for prod)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146187632", "body": ""}
{"comment": {"body": "This is the existing `ProfileNavigator` file but renamed. Added logic to handle fetching teams for the dropdown", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#discussion_r998483452"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146188430", "body": ""}
{"comment": {"body": "Commented out for now to suppress warnings. This page is just a shell/needs further implementation", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#discussion_r998484002"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146227518", "body": ""}
{"comment": {"body": "Do we have any of these routes hard-coded?\r\nEmails?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#discussion_r998507734"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146228148", "body": ""}
{"comment": {"body": "If so, maybe add a redirect for now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#discussion_r998508155"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146239334", "body": ""}
{"comment": {"body": "Seems a little strange that we're extracting teamID from a SettingsContext.\r\n\r\nShould we nest our team routes and have a TeamContext instead? It could provide both the teamID & possibly resolved team model as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#discussion_r998515897"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146241625", "body": ""}
{"comment": {"body": "it kind of is specific to settings though \r\n<img width=\"828\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/196501623-ce17847e-b050-44dd-acf0-9b09e324e252.png\">\r\n\r\nyou're configuring settings for specific teams. it has nothing to do with any other part of the dashboard ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#discussion_r998517478"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146243644", "body": ""}
{"comment": {"body": "Instead of filteringReady, I think we should handle the loading state & potentially error state if this fails.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#discussion_r998518893"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146243822", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146245518", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146251418", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#pullrequestreview-1146258085", "body": ""}
{"comment": {"body": "No I don't think we reference these routes anywhere outside of the dashboard ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3317#discussion_r998529161"}}
{"title": "Fix slack team admin console", "number": 3318, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3318"}
{"title": "Only ingest slack channels configured for ingestion", "number": 3319, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3319", "body": "Should only be merged once we have support for configuring the slack integration."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3319#pullrequestreview-1146356506", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3319#pullrequestreview-1146406676", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3319#pullrequestreview-1146407188", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3319#pullrequestreview-1146408127", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3319#pullrequestreview-1146480615", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3319#pullrequestreview-1146499048", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3319#pullrequestreview-1146525394", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3319#pullrequestreview-1150146489", "body": ""}
{"title": "Fix port", "number": 332, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/332", "body": "The target port has changed since we moved to port 443 for dev/prod"}
{"title": "[BREAKS API ON MAIN] Video metadata model", "number": 3320, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320"}
{"comment": {"body": "LGTM", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#issuecomment-1284636046"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146301499", "body": ""}
{"comment": {"body": "This model name is identical to the API model's name.\r\nIs that a problem?\r\n\r\nWhen updating `MessageService` to take in the API `VideoMetadata`, there seems to be a namespace collision as it auto imports *this* DB version of VideoMetadata instead of the API videoMetadata.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#discussion_r998561482"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146302318", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146308625", "body": ""}
{"comment": {"body": "The way to do this is to \"import as\" \u2014 many examples in the API layer. For example:\r\n```\r\nimport com.nextchaptersoftware.api.models.PullRequestInfo as ApiPullRequestInfo\r\nimport com.nextchaptersoftware.db.stores.PullRequestInfo\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#discussion_r998566440"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146313348", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146313829", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146507959", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146508724", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146509126", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146516353", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146701725", "body": ""}
{"comment": {"body": "@jeffrey-ng I decided to ditch the sealed class. It created unnecessary serialization complexity for what are essentially simple POJOs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#discussion_r998843499"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1146848140", "body": ""}
{"comment": {"body": "The end isn't necessary for today's UI but we can keep it in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#discussion_r998942695"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1147811135", "body": ""}
{"comment": {"body": "The API compat checker is complaining that removing `repoFileReferences`, `webFileReferences`, and `fileReferences` from `VideoMetadata` breaks backwards compatibility. Does anyone understand why? They weren't required fields... @richiebres @rasharab @matthewjamesadam @jeffrey-ng ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#discussion_r999617094"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1147949438", "body": ""}
{"comment": {"body": "Removing optional fields from a response is fine.\n\nRemoving optional fields from a request is not.\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/8e0dad8f-df6b-4ebc-97b2-0af2277a9f07?message=708ac424-9a71-454f-8293-26478b130709).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#discussion_r999708737"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1147972408", "body": ""}
{"comment": {"body": "Richie reminded me that this will break request compatibility, which is fine in this case because no clients are using it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#discussion_r999724739"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1148193545", "body": ""}
{"comment": {"body": "@richiebres I don't see a way around this goofiness. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#discussion_r999879211"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3320#pullrequestreview-1149557492", "body": ""}
{"title": "Use new getRepoSourceMarks API [BREAKS API ON MAIN]", "number": 3321, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3321", "body": "Use in Unblocked API client\nReplace VSCode usage\nReplace pusher support"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3321#pullrequestreview-1146366468", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3321#pullrequestreview-1146634961", "body": ""}
{"title": "MoveToTokenBasedAuth", "number": 3322, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3322", "body": "Slack Token Service\nFix test\nFix"}
{"title": "[BREAKS API ON MAIN] make installations for slack tied to team", "number": 3323, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3323"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3323#pullrequestreview-1146441697", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3323#pullrequestreview-1146477369", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3323#pullrequestreview-1146485944", "body": ""}
{"title": "Support ls-tree for older versions of Git", "number": 3324, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3324", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3324#pullrequestreview-1146396620", "body": ""}
{"title": "Weird rebase issues", "number": 3325, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3325"}
{"title": "Fix videoapp permissions", "number": 3326, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3326"}
{"title": "Fix issue with missing dependency", "number": 3327, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3327", "body": "When adding @mention support, onSendMessage was not included in dependency array.\nTherefore, whenever title was updated, this callback was never updated with the new title value."}
{"comment": {"body": "Think this is a pretty old bug now. \r\nhttps://github.com/NextChapterSoftware/unblocked/pull/2473", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3327#issuecomment-1283014486"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3327#pullrequestreview-1146531028", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3327#pullrequestreview-1146532737", "body": ""}
{"comment": {"body": "Need to move away from this. Explicitly removing helpful linter.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3327#discussion_r998721439"}}
{"title": "Fix at mentions", "number": 3328, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3328"}
{"title": "Show slack configuration in the admin console", "number": 3329, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3329"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3329#pullrequestreview-1146643120", "body": "handsome"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3329#pullrequestreview-1148060773", "body": ""}
{"title": "Add Hash data type for parsing commit and file SHAs", "number": 333, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/333", "body": "https://github.com/NextChapterSoftware/unblocked/pull/315#discussion_r805055630"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/333#pullrequestreview-880806616", "body": ""}
{"title": "fix tests", "number": 3330, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3330"}
{"title": "getPullRequestInfo returns last modified header", "number": 3331, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3331", "body": "So that the client can poll the existing /pullRequests/{pullRequestId}/info push channel"}
{"title": "Update breakpoints and tighten up message layout", "number": 3332, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332", "body": "The previous discussion thread layout in VSCode was more spacious, which looked fine for GitHub messages where descriptions are typically longer. However, the abundance of padding made Slack messages hard to read, as they typically have shorter descriptions. \nThis PR attempts to address that in the following ways:\nAdd a narrow breakpoint where the layout with the avatar aligned-left can be applied sooner\n\nTighten up the line-height, margins, and padding for message lists and containers."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1148100061", "body": "Looks good to me, Kay should probably take a quick look"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1148524350", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149537453", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149539035", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149544454", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149545431", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149605100", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149617670", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149620814", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149628486", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149647743", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149651216", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1149669760", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3332#pullrequestreview-1150015301", "body": ""}
{"title": "Topic Extractor (Unblocked.TopicService)", "number": 3333, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3333", "body": "Initial endpoint/schemas of the service that will receive the output of client generated topics+ratings (built by @matthewjamesadam) and then grow up to serve as the basis for server generated recommended topics (using all the PR data + Slack conversations, etc.).\nAsking for a review and also seeing if these new definitions break anything I don't know about in the checkin pipeline. :-)\nFor our initial exploration, we are scoping topics to repos rather than teams, thus the endpoints hang off of /repo."}
{"title": "Topic Extractor Service", "number": 3334, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334", "body": "Initial endpoint/schemas of the service that will receive the output of client generated topics+ratings (built by @matthewjamesadam) and then grow up to serve as the basis for server generated recommended topics (using all the PR data + Slack conversations, etc.).\nAsking for a review and also seeing if these new definitions break anything I don't know about in the checkin pipeline. :-)\nFor our initial exploration, we are scoping topics to repos rather than teams, thus the endpoints hang off of /repo."}
{"comment": {"body": "@davidkwlam + @matthewjamesadam  : I am going to fix this up based on our conversations with the hope that:\n\nPUT Topic(id,name,score,relevant,comment)[]\n\nIs done this week for our demo Friday.\n\nAnd GET /recommended is next week with PR/Slack data and DLam help/excellence.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#issuecomment-1284524321"}}
{"comment": {"body": "Going to start working on the implementation + the topicmodel table while you folks review this. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#issuecomment-1284649036"}}
{"comment": {"body": "@davidkwlam : implemented your feedback and added what I think will work on the db layer. going to try to add some tests now. sorry for the play by play, but it is easier to fix this before I get too far.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#issuecomment-1286214767"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1146954110", "body": ""}
{"comment": {"body": "Not sure if hanging topics off repos will work well. Unlike us, teams that have been onboarded to date have many repos; some of which have lots of content, but most have very little. Example:\r\n- Clio: https://admin.prod.getunblocked.com/teams/90dab8a5-e88a-4490-b8ec-a201a555b351/repos\r\n- Expo: https://admin.prod.getunblocked.com/teams/5bf29d2c-23ae-4af8-8ccc-fe767cd2df79/repos\r\n- Pulumi: https://admin.prod.getunblocked.com/teams/b3b49c13-3243-4494-a6b0-72bbfdc15383/repos\r\n\r\nThe consequence is that the repos with little content will not have any meaningful topics. Maybe we have to rely on team-level topics for the majority of low-content repos, but prefer repo-specific content if more available?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#discussion_r999025867"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1147849629", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1147882331", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1147908068", "body": ""}
{"comment": {"body": "spent a little time talking to @matthewjamesadam and @davidkwlam about this yesterday, as team vs. repo was one of the questions i had prior.\n\n\n\nthink we all agreed that /team/topics absolutely makes sense in the limit, as there should be a large number of folks with team content/context that spans repos. in fact, that is where we initially planning on homing the endpoint.\n\n\n\nbut after a quick discussion, we agreed that scoping this feature/endpoint to /repo while we are still experimenting with this feature likely made the most sense as we are just computing the topics from repo scope and once we add in team-wise context/context (N repos, Slack, Notion, etc.), we will create /teamId/topics.\n\n\n\nHappy to chat about this again.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/dcbd268c-f997-40a6-baba-b74d933fa5bc?message=8915ccd2-d424-4506-931d-33868a8c1ead).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#discussion_r999679625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1147909714", "body": ""}
{"comment": {"body": "DLam, Doug and I discussed this yesterday -- for now (ie, the next couple weeks) we're analyzing and parsing individual repos, so our short term plan is to build this as repo-scoped data.  We know that this data will likely have to cross project boundaries but we're not 100% sure how that will work right now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#discussion_r999680739"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149575618", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149621737", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149631048", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149789445", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149791752", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149796748", "body": ""}
{"comment": {"body": "Need to add score and the other properties to this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#discussion_r1000997848"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149810311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149821003", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149848152", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149858895", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1149881894", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1150128540", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1150146693", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1150149173", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1150161215", "body": "@davidkwlam is good with this. will write a test tomorrow. @matthewjamesadam : maybe you can give it a spin from the client when you can?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1151277899", "body": ""}
{"comment": {"body": "hi Doug\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/dcbd268c-f997-40a6-baba-b74d933fa5bc?message=a9db4794-469c-4e0a-a52f-f36a31699a55).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#discussion_r1002005500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1151354786", "body": ""}
{"comment": {"body": "Hi @richiebres\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/dcbd268c-f997-40a6-baba-b74d933fa5bc?message=277dea35-ffb1-4319-a0a2-36f64d0a1142).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#discussion_r1002055054"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#pullrequestreview-1221477941", "body": ""}
{"comment": {"body": "Pretty sure that all of this work is up at the team level now based on @kaych + @davidkwlam demos? Prescient @richiebres!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3334#discussion_r1051170060"}}
{"title": "Slack auth test", "number": 3335, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3335", "body": "afdssadf\n"}
{"comment": {"body": "testasfdadsds \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3335#issuecomment-1284319393"}}
{"comment": {"body": "> testasfdadsds \ud83d\udc4d\r\n\r\nafsdsd", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3335#issuecomment-1284319546"}}
{"title": "[BREAKS API ON MAIN] Fix getSlackConfiguration response definition", "number": 3336, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3336", "body": "API isn't being used yet. The implementation is doing the correct thing, returning an object instead of an array of objects, but the definition was wrong."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3336#pullrequestreview-1147991375", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3336#pullrequestreview-1147998750", "body": ""}
{"title": "getSlackConfiguration returns 404 if not found", "number": 3337, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3337"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3337#pullrequestreview-1148010789", "body": ""}
{"title": "Add emoji pre processor", "number": 3338, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3338", "body": "Fixes UNB-690"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3338#pullrequestreview-1148036859", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3338#pullrequestreview-1148037890", "body": ""}
{"title": "Updating most of the onboarding wording", "number": 3339, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3339"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3339#pullrequestreview-1148131152", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3339#pullrequestreview-1148131836", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3339#pullrequestreview-1148385912", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3339#pullrequestreview-1148388259", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3339#pullrequestreview-1150182525", "body": ""}
{"comment": {"body": "I think this should say \u201cInsights support markdown and images, and let you\u2026\u201d\r\n\r\nie the verb supports applies to markdown and images \u2014 insights also let you tag teammates etc", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3339#discussion_r1001256764"}}
{"title": "Adds pushChannelService for /threads", "number": 334, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/334", "body": "Pulls all the pieces together to give us modifiedSince queries for a particular channel. The current implementation maps a channel directly to a DB model, which might need refactoring for API models that span multiple DB models. Would be fairly trivial to modify to accept a list of predicates and queries. \nNext up I'm going to create several fixtures that we can load locally and in dev, and then introduce a hidden auth endpoint to skip the auth flow so we can start integrating with the web client."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/334#pullrequestreview-882109986", "body": "nice work!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/334#pullrequestreview-882156917", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/334#pullrequestreview-882158362", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/334#pullrequestreview-882341444", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/334#pullrequestreview-883272330", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/334#pullrequestreview-883450197", "body": "Fix error handling "}
{"comment": {"body": "ideally catch this at a higher level api exception handler", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/334#discussion_r807169410"}}
{"comment": {"body": "Is this caught at a higher level and converted into 404?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/334#discussion_r807173303"}}
{"title": "Add slack firendly unicode", "number": 3340, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3340"}
{"title": "Uninstall slack team", "number": 3341, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3341"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3341#pullrequestreview-1148271769", "body": ""}
{"title": "Include slack messages in PR message count", "number": 3342, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3342"}
{"title": "New sourcepoint API that is more efficient and supports video FileMarks", "number": 3343, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3343", "body": "Created new APIs and deprecated old ones:\n\ngetSourceMarks  getRepoSourceMarks\nfindSourceMarks  findRepoSourceMarks\nputSourcePoints  putRepoSourcePoints\ncreateThread  createThreadV2\ncreateMessage  createMessageV2\n\nFollow-up changes will:\n- implement new APIs\n- adopt new APIs in web\n- adopt new APIs in web-ext\n- adopt new APIs in VSCode and support FileMarks\n- optimize storage of points on the server (jsonSnippet -> jsonSnippetBlob, migrate originalFilePath to SourceMark maybe)\nLater performance improvements:\n- asynchronously process upstreamed points"}
{"title": "Refactored knowledge form", "number": 3344, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3344", "body": "Refactor common aspects of KnowledgeFormCollaborators into a separate file to be reused.\n"}
{"comment": {"body": "Ignore the long commit list... Git shenanigans since I branched off an existing branch.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3344#issuecomment-1284614508"}}
{"title": "Embed walkthrough app in customers builds", "number": 3345, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3345"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3345#pullrequestreview-1149570837", "body": ""}
{"title": "minor refactor", "number": 3346, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3346"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3346#pullrequestreview-1148346713", "body": ""}
{"title": "Add more logging", "number": 3347, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3347"}
{"title": "Update slack scope", "number": 3348, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3348"}
{"title": "Fix Extension Spinners", "number": 3349, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3349", "body": "GH Spinner style was being overwritten by our css.\nAdd a wrapper class which allows us to inject classnames.\n\n\n"}
{"comment": {"body": ":)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3349#issuecomment-1285005616"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3349#pullrequestreview-1149629345", "body": ""}
{"title": "Adds ability to pre-populate database with unblocked team members", "number": 335, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/335", "body": "So we can skip installation and auth etc after we drop a db.\nNext - I'm going to introduce an endpoint that allows certain environments to vend auth tokens for identities skipping OAuth"}
{"comment": {"body": "> any reason we don't also create Person objects?\r\n\r\nWe do - see `makeIdentity` in `RuntimeFixtures`. It creates a person for the identity ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/335#issuecomment-1039475176"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/335#pullrequestreview-882087052", "body": "any reason we don't also create Person objects?"}
{"title": "Versions can be added to a channel but cannot be removed", "number": 3350, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3350", "body": "The correct way to remove a version from a channel is to mark the version as obsolete.\nThis change prevents us from inadvertently breaking clients.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3350#pullrequestreview-1149798096", "body": ""}
{"title": "Check that API spec is compatible with released versions", "number": 3351, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351", "body": "\nResults\nTurns out we have broken every Stable released version!\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#pullrequestreview-1149535938", "body": ""}
{"comment": {"body": "Pulls in the selected tags without pulling in huge amounts of history. Keeps CI fast.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#discussion_r1000822569"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#pullrequestreview-1149798993", "body": ""}
{"comment": {"body": "how do you know that 487 is the magic number? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#discussion_r1000999338"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#pullrequestreview-1149800441", "body": ""}
{"comment": {"body": "_All_ versions are broken.\n\n\n\nI built 487 today from tip of main, just so that I could at least get the test to run.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/af2704dc-4c9c-4ec0-851a-23f834b36ed8?message=5dc8b2ee-4ed4-4406-9ace-e2f149127e40).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#discussion_r1001000298"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#pullrequestreview-1149801537", "body": "So rad"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#pullrequestreview-1149802293", "body": ""}
{"comment": {"body": "I'll replace this later with the \"GET /versions\" code in a follow up. But it has to start with the next stable released version.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/af2704dc-4c9c-4ec0-851a-23f834b36ed8?message=1163e9ae-19ec-4132-a4c5-d6d7c7b76f7a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#discussion_r1001001600"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#pullrequestreview-1149803084", "body": ""}
{"comment": {"body": "In the meantime, this 487 version just guards against us further breaking.\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/af2704dc-4c9c-4ec0-851a-23f834b36ed8?message=a8dbfb99-b00a-4600-99fe-9ffd86eee285).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#discussion_r1001002143"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#pullrequestreview-1149803897", "body": ""}
{"comment": {"body": "\ud83d\udc4d\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/af2704dc-4c9c-4ec0-851a-23f834b36ed8?message=03ad7769-9840-41f0-8345-10cfc4830b91).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3351#discussion_r1001002709"}}
{"title": "Adopt new SourceMark APIs in VSCode", "number": 3352, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3352", "body": "VSCode is now aware of SourceMarks/SourcePoints and FileMarks/FilePoints for Video Walkthrough\nDeals with sparse file-path and sparse snippets\nUnzips compressed and encoded snippets for SourceMarks on-demand\nUses more efficient upload API with better error handling"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3352#pullrequestreview-1159125483", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3352#pullrequestreview-1159125630", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3352#pullrequestreview-1159128395", "body": ""}
{"title": "Up specs of prod", "number": 3353, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3353"}
{"title": "Actually create videometadata in new threads", "number": 3354, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3354", "body": "ThreadsApiDelegateImpl was missing the actual metadata creation step. Got missed due to confusion over MessageService's many createMessage functions"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3354#pullrequestreview-1149793131", "body": ""}
{"comment": {"body": "This test was previously failing until metadata creation was properly implemented", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3354#discussion_r1000995400"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3354#pullrequestreview-1149808194", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3354#pullrequestreview-1149810062", "body": ""}
{"title": "Imprvoe tests and logging", "number": 3355, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3355", "body": "Add auth install tests for slack."}
{"title": "Use Flac because VSCode has licensing restrictions with AAC", "number": 3356, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3356"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3356#pullrequestreview-1149870636", "body": ""}
{"title": "API service validates output encoding", "number": 3357, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3357", "body": "The service is now guaranteed to respond with the expected model type and status code as defined by the open API spec,\neliminating tedious code and a class of bugs.\n"}
{"title": "Delete configuration when slack team is uninstalled", "number": 3358, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3358"}
{"title": "Update API implementation to conform to generated interface", "number": 3359, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3359"}
{"title": "Add admin auth endpoint", "number": 336, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336", "body": "Problem\nIt is currently not possible to authenticate with the service without stepping through the SCM auth flow. This impacts client development velocity and prevents us from writing FVTs. \nProposal\nGive clients a faster path to assume an identity for development purposes. Even more useful when used in tandem with https://github.com/NextChapterSoftware/unblocked/pull/335"}
{"comment": {"body": "> Is the main usecase for this FVTS?\r\n> \r\n> I don't see how this improves development velocity? Wouldn't this \"god\" token still have a limited expiration time? 10 minute?\r\n\r\nI can set expiry to infinite. I thought this was going to solve a problem for client development but if not we should probably close this PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#issuecomment-1039478574"}}
{"comment": {"body": "htfhyt", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#issuecomment-1041933296"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-*********", "body": "Is the main usecase for this FVTS?\nI don't see how this improves development velocity? Wouldn't this \"god\" token still have a limited expiration time? 10 minute?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-*********", "body": "not opposed to this hack for now.\nfor FVTs would prefer we actually test the login with GitHub end-2-end where we go through the GitHub login. it'd need to be a non-2FA GitHub bot account."}
{"comment": {"body": "so this is an IdentityID, not PersonID, not TeamMemberID?\r\n\r\nWhere does the client get the `id` from?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#discussion_r806169830"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-*********", "body": ""}
{"comment": {"body": "ID comes from `RuntimeFixtures`. You have to know the identity id ahead of time", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#discussion_r806177168"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-882102035", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-882103449", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/336#pullrequestreview-882104278", "body": ""}
{"title": "VideoMetadata - CodeReferences UI", "number": 3360, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360", "body": "Basic UI for CodeReferences.\nTODO: \n* FileIcons\n* Navigation to SourcePoints\n\n"}
{"comment": {"body": "<img width=\"216\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197585184-c778cd62-ded8-4fd6-8e6d-43642cfab2cb.png\">\r\n\r\nnoticed that the child corners here aren't rounded, might need to add an overflow hidden or something on the parent where the border radius is being set ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#issuecomment-1289340661"}}
{"comment": {"body": "<img width=\"708\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197585470-686d0105-6fb8-4fbb-9c0c-3f5a7cf66ebe.png\">\r\n\r\nwhat is the case where the timestamp wouldn't be rendered as a link? if the sourcemark isn't resolved?? (or was this just test UI?) if it's a sourcemark issue, is it worth adding a tooltip of some sort to explain? \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#issuecomment-1289343004"}}
{"comment": {"body": "> <img alt=\"image\" width=\"708\" src=\"https://user-images.githubusercontent.com/13431372/197585470-686d0105-6fb8-4fbb-9c0c-3f5a7cf66ebe.png\">\r\n> \r\n> what is the case where the timestamp wouldn't be rendered as a link? if the sourcemark isn't resolved?? (or was this just test UI?) if it's a sourcemark issue, is it worth adding a tooltip of some sort to explain?\r\n\r\nThe left source marks show up as links when there's a source mark backing it.\r\n\r\nIf there is no source mark (aka not actionable), will not be rendered as a link.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#issuecomment-1289379544"}}
{"comment": {"body": "Fixed rounded corners\r\n<img width=\"617\" alt=\"CleanShot 2022-10-24 at 11 06 11@2x\" src=\"https://user-images.githubusercontent.com/1553313/197595201-117cd65e-db8d-4d94-845e-120292b99ac8.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#issuecomment-1289400645"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1150138684", "body": ""}
{"comment": {"body": "This is duped in the three places. There must be a generic way to handle this with interfaces but haven't wrapped up head around it in Typescript.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#discussion_r1001225459"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151575111", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151575950", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151576669", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151577280", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151577750", "body": ""}
{"comment": {"body": "Why does this component need to support undefined/null children? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#discussion_r1002202438"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151577807", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151578476", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151578612", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151582455", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151583801", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151590888", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151592567", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151593153", "body": ""}
{"comment": {"body": "If tabs are conditionally rendered (which they are in the VideoMetadata), it would be a null / undefined child.\r\nThis is similar to how ReactNode is typed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#discussion_r1002213515"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151598505", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151599302", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151599866", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151600336", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151606792", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151609570", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1151611192", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1153485369", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1153487978", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1153490256", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1153491567", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1153491936", "body": ""}
{"comment": {"body": "does stylelint not complain about this? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#discussion_r1003556470"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1153500426", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1153542162", "body": ""}
{"comment": {"body": "Nope... Is it supposed to be at the top?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#discussion_r1003592051"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#pullrequestreview-1153544303", "body": ""}
{"comment": {"body": "supposed to be a whitespace between the mixin and the next line; ie\r\n```\r\n@include flex-center-between;\r\n<required whitespace>\r\n.the-next-style {\r\n...\r\n}", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3360#discussion_r1003593540"}}
{"title": "Only configure audio when source is available", "number": 3361, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3361"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3361#pullrequestreview-1150032105", "body": ""}
{"title": "Do not return slack threads for uninstalled teams", "number": 3362, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3362"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3362#pullrequestreview-1150083117", "body": ""}
{"title": "AddSlackIngestionSTatus", "number": 3363, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3363", "body": "Add status information\nAdd granular information"}
{"title": "[Slack] Auth/configure in dashboard", "number": 3364, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3364", "body": "\n\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3364#pullrequestreview-1150112250", "body": ""}
{"comment": {"body": "I think there's a redirect component for this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3364#discussion_r1001212582"}}
{"comment": {"body": "Is this fixing an existing bug?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3364#discussion_r1001213180"}}
{"comment": {"body": "Not sure if it's a problem here but in the past, I've had situations where multiple modals can be triggered due to useEffect running multiple times.\r\n\r\nNot saying it'll happen here but worth keeping in mind.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3364#discussion_r1001223364"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3364#pullrequestreview-1150328319", "body": ""}
{"comment": {"body": "No Redirect component in react router 6 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3364#discussion_r1001361866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3364#pullrequestreview-1150328388", "body": ""}
{"comment": {"body": "yes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3364#discussion_r1001361905"}}
{"title": "Support 3xx redirect output in mustache", "number": 3365, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3365"}
{"title": "Re-add networking entitlements to video app", "number": 3366, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3366"}
{"title": "Temp enalbe video for prod", "number": 3367, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3367", "body": "Temporarily enable video for prod. \nAllow us to get a test build."}
{"title": "Jeff/revert video prod", "number": 3368, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3368"}
{"title": "add one more link", "number": 3369, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3369"}
{"title": "Install XForwardedHeaderSupport", "number": 337, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/337", "body": " currently returning 500 with {\"status\":500,\"detail\":\"You should set secure cookie only via secure transport (HTTPS)\"}\nAssuming our API service is behind a load balancer that could be stripping away HTTPS, add XForwardedHeaderSupport to support secure cookies.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/337#pullrequestreview-881944361", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/337#pullrequestreview-881944582", "body": ""}
{"title": "Create stub TopicExtractionJob", "number": 3370, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3370", "body": "Logic that goes in the TopicExtractorService to come later in a separate PR"}
{"comment": {"body": "> Awesome. Going to check out your other branch with the db export tonight. My guess it that it should be straightforward to bring that code into this service to generate the topics off of -- and then add another mechanism to associate those topics with documents?\r\n\r\nYep should be straightforward", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3370#issuecomment-1287260975"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3370#pullrequestreview-1150145742", "body": "Awesome. Going to check out your other branch with the db export tonight. My guess it that it should be straightforward to bring that code into this service to generate the topics off of -- and then add another mechanism to associate those topics with documents?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3370#pullrequestreview-1151334363", "body": ""}
{"title": "Re-enable gradle cache", "number": 3371, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3371"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3371#pullrequestreview-1150180225", "body": ""}
{"title": "Fix Topic build breaks", "number": 3372, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3372", "body": "From #3334"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3372#pullrequestreview-1150173086", "body": ""}
{"title": "Fix bug", "number": 3373, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3373"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3373#pullrequestreview-1151235805", "body": ""}
{"comment": {"body": "Could replace these with `insertIgnores` if we had a unique index on team/slackteam", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3373#discussion_r1001976482"}}
{"title": "FixBugs", "number": 3374, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3374", "body": "Update\nFix bug"}
{"title": "Fix test", "number": 3375, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3375"}
{"title": "Remove api config settings", "number": 3376, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3376"}
{"title": "Fix build", "number": 3377, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3377"}
{"title": "Adds SSO between native client and webview", "number": 3378, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378", "body": "Shares the authToken between the native and web clients in the mobile app."}
{"comment": {"body": "@pwerry : this unifies the token between the native and web clients within the mobile app. cleans up the sign in, but we still have a little work to do to add a clean redirect_url for mobile native/web hybrid clients.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378#issuecomment-1287163774"}}
{"comment": {"body": "@pwerry : nothing that a new enum member doesn't solve. :-)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378#issuecomment-1287277445"}}
{"comment": {"body": "@pwerry : could you approve? avoiding the red in my life after yesterday. :-)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378#issuecomment-1287491680"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378#pullrequestreview-1151244342", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378#pullrequestreview-1151534618", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378#pullrequestreview-1151577119", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378#pullrequestreview-1151595516", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378#pullrequestreview-1151602441", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3378#pullrequestreview-1151655875", "body": ""}
{"title": "Add async event dequeue", "number": 3379, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3379"}
{"title": "fix web deployment", "number": 338, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/338", "body": "Last web deploy failed. This is to fix the CI job for it. \n- Distro ID was changed since our last deploy. We were forced to recreate them in order to use a more up to date CDK resource\n- Corrected some copy paste mistakes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/338#pullrequestreview-882002489", "body": ""}
{"title": "Remove camera view if capture is disabled", "number": 3380, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3380"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3380#pullrequestreview-1151230506", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3380#pullrequestreview-1151230680", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3380#pullrequestreview-1151231356", "body": ""}
{"title": "Implement new SourcePoint APIs", "number": 3381, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#pullrequestreview-1154093559", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#pullrequestreview-1154095211", "body": ""}
{"comment": {"body": "Nice", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#discussion_r1003975429"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#pullrequestreview-1154097012", "body": ""}
{"comment": {"body": "Not only is it nice, it's richie...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#discussion_r1003976621"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#pullrequestreview-1154097618", "body": ""}
{"comment": {"body": "Follow up here or in future?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#discussion_r1003977158"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#pullrequestreview-1154100493", "body": ""}
{"comment": {"body": "Nah future. It reduces storage in PG, but not by much. Planning to do eventually but definitely not in this PR and it's already a beast.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/658813c1-2325-41a4-9ef1-8d7c4349a6ef?message=27749db0-d40e-48c7-8639-7f6326a4bb1a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#discussion_r1003979322"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3381#pullrequestreview-1154100529", "body": "Another Bresnan mindbender. "}
{"title": "[Slack] Dashboard bug fixes", "number": 3382, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3382", "body": "On initial auth, preselect All public channels and prompt user to save \nFix navigation route blocking to omit dashboardBaseName from transition\nFix save button rendering as enabled on load\nClear state banner when the team changes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3382#pullrequestreview-1151496480", "body": ""}
{"comment": {"body": "`from @config` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3382#discussion_r1002149956"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3382#pullrequestreview-1151533971", "body": ""}
{"comment": {"body": "is a shared/ component so no aliases ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3382#discussion_r1002172301"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3382#pullrequestreview-1151655793", "body": ""}
{"title": "Fix slack ingestion", "number": 3383, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3383"}
{"title": "Remove slack ingestion fixtures", "number": 3384, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3384"}
{"title": "Add local slack auth", "number": 3385, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3385"}
{"title": "FixIdenityUniqueIndex", "number": 3386, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3386", "body": "FixIdentityUniqueIndex\nupdate"}
{"title": "Fix auth tests", "number": 3387, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3387"}
{"title": "[Slack] Feedback in thread views", "number": 3388, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3388", "body": "\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3388#pullrequestreview-1151570675", "body": ""}
{"title": "reenable", "number": 3389, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3389"}
{"title": "Revert code default", "number": 339, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/339"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/339#pullrequestreview-882117441", "body": ""}
{"title": "Updating insight bubble and hub onboarding videos", "number": 3390, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3390", "body": "The blue bubble video still needs to be done, but I'm waiting for the change in the product before I recapture that.\n\n"}
{"comment": {"body": "I don't think there's an immediate rush to get this in, as we're not onboarding any new customers. The contextual file widget is also changing which means all these videos need to be redone if we want to capture those updates. @matthewjamesadam  can you unapprove for the time being?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3390#issuecomment-1316359137"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3390#pullrequestreview-1181751787", "body": "Should this still go in?"}
{"title": "Rebrand discussions to context/insights", "number": 3391, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3391", "body": "per new designs/blue bubble rebranding"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3391#pullrequestreview-1151604722", "body": ""}
{"comment": {"body": "Did my best with the wording here but not sure if it requires the copy to be adjusted as a whole ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3391#discussion_r1002221848"}}
{"title": "Ensure we rebuild things when template files change", "number": 3392, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3392"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3392#pullrequestreview-1151647676", "body": "nice"}
{"title": "Remove preallocated buffer in Runner", "number": 3393, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3393", "body": "This allows us to handle very large stdout/stderr results (the exceptional case), while not unnecessarily allocating large buffers for the general case, where little-to-no data is output."}
{"comment": {"body": "> Thanks for doing this. Any idea of the impact on runtime?\r\n\r\nJust from using it I didn't notice any difference.  Any suggestion on how far we should test this?  We could build some tests that run this a thousand times and compare against `execFile`...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3393#issuecomment-1289581435"}}
{"comment": {"body": "> > Thanks for doing this. Any idea of the impact on runtime?\r\n> \r\n> Just from using it I didn't notice any difference. Any suggestion on how far we should test this? We could build some tests that run this a thousand times and compare against `execFile`...\r\n\r\nOk, tomorrow I'll run SM engine on Sentry with this change vs _main_, and see what happens. We shell out 1,000s of times for that repo, so if there's a noticeable difference we should be able to observe.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3393#issuecomment-1289957627"}}
{"comment": {"body": "## Results: main\r\n\r\n- maxRSS: 363.39 MB\r\n- wallClock: 734.69 s\r\n\r\n## Results: this change\r\nNo significant difference.\r\n\r\n- maxRSS: 355.98 MB\r\n- wallClock: 741.20 s\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3393#issuecomment-1290936132"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3393#pullrequestreview-1151663314", "body": ""}
{"comment": {"body": "I maintained the slicing here even though it felt a bit odd, we might drop important data here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3393#discussion_r1002260074"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3393#pullrequestreview-1152636695", "body": "Thanks for doing this. Any idea of the impact on runtime?"}
{"comment": {"body": "The reason for limiting here is that some error output is MBs and when called in a loop over many items (eg: for all marks, or for all commits) it pulverizes VSCode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3393#discussion_r1002954935"}}
{"title": "Unblocked-Mobile-3: Notifications", "number": 3394, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3394", "body": "Initial backgroundtask for notifications without using apns.\nonce we confirm that these notifications are actually going to fire in a meaningful way, we can wire up the existing notification infra from the hub app.\nif they don't work from background fetch, we will just move forward with apns server. I am likely to just use "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3394#pullrequestreview-1152504441", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3394#pullrequestreview-1152513849", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3394#pullrequestreview-1153969653", "body": ""}
{"title": "API Versioning: Expose release versions", "number": 3395, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3395"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3395#pullrequestreview-1152501333", "body": ""}
{"comment": {"body": "Can this be merged with the previous select?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3395#discussion_r1002855203"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3395#pullrequestreview-1152502570", "body": ""}
{"comment": {"body": "Yes. For inner join, it's 100% equivalent.\n\n\n\nI slightly prefer it like this because it reads as \"join on stable released version\" as opposed to \"join on released version then select stable versions\".\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/8528bec4-5a5e-4b4c-a6cb-60c59b8276b7?message=cd30d394-08cb-45db-a874-f5086cd4faec).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3395#discussion_r1002856173"}}
{"title": "API Versioning: Replace hardcoded live versions with real versions", "number": 3396, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3396", "body": "Use real live non-obsolete stable versions in API compatibility test.\nTests start at a minimum version number because earlier versions are technically broken but not marked as obsolete yet.\n\nNote: The API test is expected to fail until the parent PR in this stack is merged to PROD."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3396#pullrequestreview-1152504069", "body": ""}
{"title": "[BREAKS API ON MAIN] Change response object on getRepoSourceMarks", "number": 3397, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3397", "body": "The getRepoSourceMarks API was never released, so that's why it's safe to break on main."}
{"comment": {"body": "Thank you Richie. I just believe in this pr.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3397#issuecomment-1289486419"}}
{"title": "Fix deployments number", "number": 3398, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3398", "body": "Last night's deployment issue was caused by the build_attempt potion of the docker image tag being updated on a re-run. This is a new bug and I don't recall seeing it before. It could be caused by Github deprecating set::output method. I'd like to try the new approach for setting job outputs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3398#pullrequestreview-1153484624", "body": "Welcome back @mahdi-torabi !!!1"}
{"title": "HardcodeCreds", "number": 3399, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3399", "body": "Revert \"Remove slack ingestion fixtures (#3384)\"\nSlack prod ingestion"}
{"title": "Add gradle skeleton", "number": 34, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/34", "body": "Basic skeleton for Gradle.\n1. We can change the module names and whatever, don't care."}
{"comment": {"body": "No, i'll delete one.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/34#issuecomment-1012622578"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/34#pullrequestreview-852360435", "body": "do we need two sets of Gradle wrappers? surprised it does not soft link them together"}
{"title": "Message Models", "number": 340, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340", "body": "First draft of message models.\nBased on discussions with Matt and Rashin.\nMimics SlateJS data models as mentioned here: \nEDIT:\nBased on IRL discussions, we are moving the message model into a separate API spec.\nAll interactions with messages from the base API will treat messageContent as a raw json string."}
{"comment": {"body": "Once we think this is in a good place, will add comments and descriptions for models.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1039491042"}}
{"comment": {"body": "This looks right to me", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1039499324"}}
{"comment": {"body": "> Concerned about the wire overhead.\r\n> \r\n> To send:\r\n> \r\n> ```\r\n> Hi!\r\n> ```\r\n> \r\n> We need to send:\r\n> \r\n> ```json\r\n> {\r\n>   \"blockType\": \"paragraph\",\r\n>   \"paragraph\": {\r\n>     \"children\": [\r\n>       {\r\n>         \"inlineType\": \"text\",\r\n>         \"text\": \"Hi!\",\r\n>         \"link\": null\r\n>       }\r\n>     ]\r\n>   },\r\n>   \"image\": null\r\n> }\r\n> ```\r\n\r\nAgree in this scenario, it's a bit overkill but I believe the verbosity keeps the implementation simpler and easier to understand for complex messages.\r\n\r\nFor example, let's say we wanted to introduce the following:\r\n```\r\nHello, @richiebres, here's the error message [code reference]\r\n\r\n[Image]\r\n```\r\n```\r\n[\r\n{\r\n    \"blockType\": \"paragraph\",\r\n    \"paragraph\": {\r\n        \"children\": [\r\n            {\r\n                \"inlineType\": \"text\",\r\n                \"text\": \"Hello, \"\r\n            },\r\n            { \r\n                \"inlineType\": \"userRef\",\r\n                \"id\": \"userId\"\r\n            },\r\n            {\r\n                \"inlineType\": \"text\",\r\n                \"text\": \", here's the error message \"\r\n            },\r\n            {\r\n                \"inlineType\": \"codeRef\",\r\n                \"id\": \"codeRefId\" \r\n            }\r\n        ]\r\n    }\r\n},\r\n{\r\n    \"blockType\": \"img\",\r\n    \"img\": {\r\n        \"url\": \"url\"\r\n    }\r\n}\r\n]\r\n```\r\n\r\nAn alternative that comes to mind would be to encode this as markdown but we would need to either introduce custom tags or custom url parsers.\r\n\r\nWould require all clients and servers to actually *understand* markdown and be able to parse out custom elements.\r\n\r\n```\r\nHello, <user: UserID>, here's the error message <code: codeId>\r\n\r\n[https://....](altText)\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1039670402"}}
{"comment": {"body": "I'm leaning towards the proposed approach, at least for now.\r\n\r\nSlack and Notion take similar approaches for their APIs fwiw.\r\n\r\nSlack does have a basic \"text\" string payload at the top message model but if \"blocks\" are utilized (which are modelled similarly to this), it would use that instead.\r\n\r\nhttps://api.slack.com/reference/messaging/payload\r\n\r\n\r\n> The usage of this field changes depending on whether you're using blocks or not. If you are, this is used as a fallback string to display in notifications. If you aren't, this is the main body text of the message. It can be formatted as plain text, or with [mrkdwn](https://api.slack.com/reference/surfaces/formatting#basics). This field is not enforced as required when using blocks, however it is highly recommended that you include it as the aforementioned fallback.\r\n\r\n\r\nWe could do something similar to handle situations where *only* basic text or standard markdown is sent instead of blocks to save some overhead? (These are optimizations I think we can handle later.)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1039674295"}}
{"comment": {"body": "Some thoughts on wire length:\r\n\r\n* We could make the type names shorter (`p` instead of `paragraph`, etc) -- but I suspect this ultimately doesn't buy us much because gzip compression should compress this down pretty effectively already\r\n* Wouldn't the `null` optional properties not end up in the JSON object going over the wire?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1039694408"}}
{"comment": {"body": "Does the message version need to live separately from the message content?  I think you need to know what version of message you're dealing with in order to know how to interpret the string?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1040822854"}}
{"comment": {"body": "> Does the message version need to live separately from the message content? I think you need to know what version of message you're dealing with in order to know how to interpret the string?\r\n\r\nThat's a good point...\r\nThat means that the version would live within the BaseAPI spec and which would most likely have its own `MessageContent` model.\r\n```\r\n{\r\n   content: string,\r\n   version: string\r\n}\r\n```\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1040827562"}}
{"comment": {"body": "> That's a good point... That means that the version would live within the BaseAPI spec and which would most likely have its own `MessageContent` model.\r\n> \r\n> ```\r\n> {\r\n>    content: string,\r\n>    version: string\r\n> }\r\n> ```\r\n\r\nYeah something like this would do the trick.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1040835357"}}
{"comment": {"body": "I think protobuf is better suited, see:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/360", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1041004846"}}
{"comment": {"body": "Going to merge this first to unblock some current work but will be looking into https://github.com/NextChapterSoftware/unblocked/pull/360\r\n\r\nWill time box exploration for another few hours.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#issuecomment-1042411476"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-882116082", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-882117041", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-882125397", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-882125692", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-882250775", "body": "Concerned about the wire overhead.\nTo send:\nHi!\nWe need to send:\njson\n{\n  \"blockType\": \"paragraph\",\n  \"paragraph\": {\n    \"children\": [\n      {\n        \"inlineType\": \"text\",\n        \"text\": \"Hi!\",\n        \"link\": null\n      }\n    ]\n  },\n  \"image\": null\n}"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-883668014", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-883680283", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-883681020", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-883822566", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-885224068", "body": ""}
{"comment": {"body": "@richiebres ok to store like this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#discussion_r808509903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#pullrequestreview-885268971", "body": ""}
{"comment": {"body": "We should remove this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/340#discussion_r808544810"}}
{"title": "[BREAKS API ON MAIN] Add channels ingestion api", "number": 3400, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3400"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3400#pullrequestreview-1153634774", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3400#pullrequestreview-1153659417", "body": ""}
{"title": "Attempt to fix helm. (We need to lock down on helm version)", "number": 3401, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3401"}
{"title": "Fix hardcoded slack", "number": 3402, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3402"}
{"title": "Update slack thread rendering on dashboard", "number": 3403, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3403", "body": "per designs:\n\nAlso:\n* Fix small bug in user icon stack rendering\n* Make slack config labels clickable"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3403#pullrequestreview-1153954982", "body": ""}
{"comment": {"body": "Should this be structured as a switch statement to allow a different subheader for each message type?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3403#discussion_r1003872715"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3403#pullrequestreview-1153955361", "body": ""}
{"title": "[BREAKS API ON MAIN] Slack installations api cleanup", "number": 3404, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404", "body": "A slack installation url is always the same installurl for an SCM team.\nJust return all slack teams and their installation status, so client can handle it on its end."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#pullrequestreview-1153806577", "body": ""}
{"comment": {"body": "Does this mean we don't need to call `getSlackTeams` anymore on the client? Do we still need that endpoint? \r\n\r\ncc @davidkwlam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#discussion_r1003771676"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#pullrequestreview-1153810136", "body": ""}
{"comment": {"body": "Not really, depends on what you want. This one just returns the current state of slack teams installations when you make that call.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#discussion_r1003774207"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#pullrequestreview-1153811104", "body": ""}
{"comment": {"body": "If you don't think you need that information, i can just remove the field.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#discussion_r1003774831"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#pullrequestreview-1153874617", "body": ""}
{"comment": {"body": "No need to remove -- I think the client refetches teams (and channels) everytime it refetches the installations so this might save us a call. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#discussion_r1003817770"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#pullrequestreview-1153879471", "body": ""}
{"comment": {"body": "Bless your heart kay. Bless your heart:\r\n\u2764\ufe0f ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#discussion_r1003820493"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3404#pullrequestreview-1153913957", "body": ""}
{"title": "Add TopicHistogramModel", "number": 3405, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3405"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3405#pullrequestreview-1153838399", "body": "David, what we have here is truly a histogram for the ages."}
{"title": "Update installations status on auth updates", "number": 3406, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3406", "body": "Issue occurred when installations state & auth state were out of sync.\nNow, whenever auth state is updated, installations should try to refresh if there's a change."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3406#pullrequestreview-1262319619", "body": ""}
{"title": "WIP: Add CoreNLP", "number": 3407, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3407"}
{"comment": {"body": "Add this logic in https://github.com/NextChapterSoftware/unblocked/pull/3413", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3407#issuecomment-**********"}}
{"title": "Unify product terms in clients", "number": 3408, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3408", "body": "Per slack thread: \n```\nInsight (umbrella term for all content) \nNote (blue bubbles)\nVideo (video icon)\nPull Request conversation (purple bubbles)\nHistorical Pull Requests\nSlack thread (/conversation  when topics come online)\n...\n```\nOutstanding work:\n- [x] Hub text\n- ~~[ ] Email copy~~\n- [ ] Dashboard routing"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3408#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3408#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3408#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3408#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3408#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3408#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3408#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3408#pullrequestreview-**********", "body": ""}
{"title": "Add an index on ThreadSearchModel.thread", "number": 3409, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3409"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3409#pullrequestreview-1153960165", "body": ""}
{"title": "Add experiemnal ktlint rules", "number": 341, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/341", "body": "This pr adds some experimental ktlint rules, one of which ensures trailing commas and fixes code that doesn't have it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/341#pullrequestreview-882147900", "body": ""}
{"comment": {"body": "Required for trailing comma rule", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/341#discussion_r806210861"}}
{"title": "Add 'getRecommendedTopics' API", "number": 3410, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3410", "body": "Adding this call for this week's sprint.  The idea is that the client will call this, providing the set of folders in the repo.  The service will do the work to generate the top n topics and return them.\nI'm keeping this as simple as possible for now.  Maybe the request/response should be objects?"}
{"comment": {"body": "How are the topics generated? Hard to review if this is the right approach without more detail.\r\n\r\nRelated:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/3334#discussion_r999040802\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3410#issuecomment-1289800810"}}
{"comment": {"body": "Just to set context here, we are trying to power an experience that looks roughly like: \r\n\r\nhttps://www.figma.com/proto/AqtqF0tJq5f7yp2D7wMzhk/Unblocked---VSCode?node-id=8538%3A291031&scaling=min-zoom&page-id=6518%3A308049&starting-point-node-id=8471%3A281549\r\n\r\n@benedict-jw is still working on that.\r\n\r\nMy understanding of how this composes with what we checked in on Friday is that the client will configure the topics (via PUT /repo/topics) that we will drive/improve this server-side GET /recommended endpoint and the future GET /related which will return the insights for given topic.\r\n\r\nThe client has the ability to send the topics of interest the moment we get permission/done parsing the folders/commit, which we can further refine from the Git PR, Slack, et al data async via the server side TopicExtractor.\r\n\r\nFor me the question is what is the query key for the /recommended endpoint. Should it look like the existing APIs that do this? Specifically https://github.com/NextChapterSoftware/unblocked/blob/f757af1d4d0537e11d049265fe6a74b06fedf936/api/private.yml#L1380?\r\n\r\nMy original proposal for the the recommend API to follow that pattern, since I think the feature is adding topic + people pivots over specific files?\r\n\r\nYou can see that here: https://github.com/NextChapterSoftware/unblocked/pull/3334/files/00406fab6cca0faa8fdcb62f9fd2aeaf7867edc6#diff-ba77b4591a44b56bb559a713e693c37a41de141b33a66893a0cd87b81ed75802R2741\r\n\r\nRecommendedTopicRequest:\r\n      description: |\r\n       Get recommended topics from client context (file, folders, source).\r\n       type: object\r\n       properties:\r\n         filePath:\r\n           type: string\r\n           minLength:1\r\n           maxLength: 4096\r\n         required:\r\n         - filePath\r\n         \r\nAfter we decided to not implement this API last week, don't think we got to close on if the client should should the whole folder/file/commit histogram to the server either to update the \"configuration\" (topicmodel) and fetch the server-influenced topics or just separate those calls completely?\r\n\r\nThat all aside, @matthewjamesadam, do you want to consider returning Topic (below) from this API? This way you can update your scores for topics you already know about, merge in new topics from the server into the same client views, and maybe offer a training/editing experience on top (not for today ;-)).\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/blob/f757af1d4d0537e11d049265fe6a74b06fedf936/api/private.yml#L4645\r\n\r\n Topic:\r\n       type: object\r\n       properties:\r\n         id:\r\n           $ref: '#/components/schemas/ApiResourceId'\r\n         name:\r\n           type: string\r\n           minLength: 1\r\n           maxLength: 100\r\n         description:\r\n           type: string\r\n           minLength: 1\r\n           maxLength: 280\r\n         score:\r\n           description: The ranking score for this topic generated by Unblocked. Higher is better.\r\n           type: integer\r\n           format: int32\r\n           minimum: 0\r\n           maximum: 1000000000\r\n         relevant:\r\n           description: A number from -1 (no) to +1 (yes), where +1 is most relevant to the person\r\n           type: number\r\n           format: float\r\n           minimum: -1.0\r\n           maximum: +1.0\r\n         reason:\r\n           description: Most pertinent reason for the relevant number from the person.\r\n           type: string\r\n           minLength: 1\r\n           maxLength: 1000\r\n       required:\r\n         - id\r\n         - name\r\n         - score", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3410#issuecomment-1289842862"}}
{"comment": {"body": "> How are the topics generated? Hard to review if this is the right approach without more detail.\r\n> \r\n> Related: [#3334 (comment)](https://github.com/NextChapterSoftware/unblocked/pull/3334#discussion_r999040802)\r\n\r\nThis is only meant to be used for the \"topic extraction\" demos in the next week or two, which is explicitly run as a separate command in VSCode to verify that our topic extraction logic is sound on other repos and teams.  It's not going to be used by any productized part of any client UI, and isn't really meant to be a long-lived API.  We can remove this pretty much at any point as the features evolve.\r\n\r\nThis API specifically facilitates the topic extraction: the client provides the list of folder names in the repo (the request body) and the service takes that, plus the slack/PR histograms it has calculated, to provide a set of recommended topics (the response body).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3410#issuecomment-1290819619"}}
{"comment": {"body": "I've updated this as we discussed this morning: the request takes in the folders plus an enum list specifying which sources to process, the response returns a list of `Topic`s.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3410#issuecomment-1291229164"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3410#pullrequestreview-1155707738", "body": ""}
{"title": "Fix indices", "number": 3411, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#pullrequestreview-1153973961", "body": ""}
{"comment": {"body": "why?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#discussion_r1003886886"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#pullrequestreview-1153974014", "body": ""}
{"comment": {"body": "why?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#discussion_r1003886915"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#pullrequestreview-1153975246", "body": ""}
{"comment": {"body": "Did a db analysis, we were doing seq scans for statistics page.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#discussion_r1003887807"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#pullrequestreview-1153975309", "body": ""}
{"comment": {"body": "Did a db analysis, we were doing seq scans for statistics page.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#discussion_r1003887843"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#pullrequestreview-1153975387", "body": ""}
{"comment": {"body": "It's basically unusable at the moment at times against prod.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#discussion_r1003887907"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#pullrequestreview-1153978529", "body": ""}
{"comment": {"body": "Who cares about stats page? Not worth creating an index for that.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1f065cdc-d7c3-4495-baa1-aaea3abfdb2d?message=fda94253-da48-4cbb-ac14-0fb0ee9cb5a8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#discussion_r1003890388"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#pullrequestreview-1153987560", "body": ""}
{"comment": {"body": "I do as I use it and so do others.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#discussion_r1003897583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#pullrequestreview-1153987953", "body": ""}
{"comment": {"body": "Personally, if you don't care, feel free to remove the page. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3411#discussion_r1003897934"}}
{"title": "Typescript version of takeWhile", "number": 3412, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3412", "body": "TS version of Kotlin's takeWhile, which is pretty useful\n  \n\n\ngoing to be used in follow up PR. factoring out here to make review easier."}
{"comment": {"body": "There's something so bewilderingly beautiful about adding kotlin functionality to typescript.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3412#issuecomment-1289973204"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3412#pullrequestreview-1155106204", "body": ""}
{"title": "Update SlackChannelIngestionService to save topic histogram", "number": 3413, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#pullrequestreview-1155149926", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#pullrequestreview-1155153073", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#pullrequestreview-1155193253", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#pullrequestreview-1155301698", "body": ""}
{"comment": {"body": "Good stuff, thanks!\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b8f7f6be-5001-4a92-a4a7-c5041a160013?message=9a344023-15ca-4372-b753-6eafeb198a52).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#discussion_r1004828880"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#pullrequestreview-1155317697", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#pullrequestreview-1155639329", "body": ""}
{"comment": {"body": "Be careful with doing this in the constructor. This should probably be done lazily.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#discussion_r1005020522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#pullrequestreview-1155647941", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/3423", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3413#discussion_r1005029240"}}
{"title": "VSCode topic extraction commands", "number": 3414, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414", "body": "Add a series of commands that display a topic extraction and review UI.  There is a separate command for each different king of source data:\n\nLocal git (git commit messages and bodies)\nPRs\nSlack\nPRs and Slack\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159155007", "body": ""}
{"comment": {"body": "NB: we will probably be able to drop these at some point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007461570"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159155414", "body": ""}
{"comment": {"body": "Used to drop all common English words.  We can probably get rid of this eventually too", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007461868"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159161791", "body": ""}
{"comment": {"body": "Do these commands need to be exposed in the command pallete?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007467035"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159165138", "body": ""}
{"comment": {"body": "Huh... they do already appear when I hit Shift+Cmd+P...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007469717"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159166633", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159167204", "body": ""}
{"comment": {"body": "Also, these commands should only be allowed to run *after* auth.\r\n`\"when\": \"unblocked-vscode:authenticated && unblocked-vscode:installed\"`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007471293"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159167924", "body": ""}
{"comment": {"body": "Not urgent but gut feeling is this can be slowish?\r\nShould this be async so it's not completely blocking?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007471885"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159168783", "body": ""}
{"comment": {"body": "This is processing one folder name at a time, so each individual run is very fast, and the overall work is async so nothing will be blocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007472516"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159168945", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159170333", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159170539", "body": ""}
{"comment": {"body": "Do we want to log these errors for debugging purposes? This seems like an unexpected error.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007473760"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159170576", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159170611", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159171161", "body": ""}
{"comment": {"body": "Yes they shouldn't happen.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007474306"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159172763", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159172823", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159173217", "body": ""}
{"comment": {"body": "I added palette entries for these and required auth/installed (except for the local git option, which doesn't require anything, as it works on a local checked out repo)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007475992"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159173752", "body": ""}
{"comment": {"body": "I didn't see it in this PR... Am I missing something?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007476464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#pullrequestreview-1159174086", "body": ""}
{"comment": {"body": "Sorry, I meant I just fixed this in a new commit", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3414#discussion_r1007476699"}}
{"title": "Record when ingestion was last finished", "number": 3415, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3415"}
{"title": "prevent deployment cancelation before rollback", "number": 3416, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3416", "body": "This should address the problems of deployments getting into a bad state requiring explicit rollbacks"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3416#pullrequestreview-1155451165", "body": ""}
{"title": "Revert \"Clearing generated point should also clear trusted points (#2642)\"", "number": 3417, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3417", "body": "This reverts commit ec87cd057463fc2d2e002fba6692a1c3d05d8b23.\nNot sure what I was thinking. This was just a super dumb bug.\n So I fucked up and used this on the unblocked repo on DEV, which nuked all sourcepoints for blue bubble threads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3417#pullrequestreview-1155503440", "body": ""}
{"title": "Remove subicons for slack users", "number": 3418, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3418", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3418#pullrequestreview-1155471259", "body": ""}
{"title": "Discussion count in stats page should not include slack threads", "number": 3419, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3419"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3419#pullrequestreview-1155499064", "body": ""}
{"title": "Allow hyphenated header names for responses", "number": 342, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/342", "body": "The inimitable Peter Werry once told me, a \"Last-Modified\" response header is better than no header.\nZally allows high customization for cases for various types, including header types."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/342#pullrequestreview-882209904", "body": "LOL. Rashin is the king of simplicity"}
{"title": "API falls back on any sourcepoint if none exist", "number": 3420, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3420", "body": "This should never happen, but add resilience by falling back on any point if there are no original points."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3420#pullrequestreview-1155658250", "body": ""}
{"title": "Add URL slupring", "number": 3421, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421", "body": "There's a bunch of things going on in this PR:\n\nSandbox is removed from Video App\nThis allowed us to ditch all the goofy hacks needed to embed sandboxed apps, so the extra schemes, targets, and configurations are gone.\nNew Accessibility permission added to permissions drop down and is a requirement to run the app\nNew accessibility observer class to walk the browser view tree, looking for an (undocumented) AXWebArea element, which has a magical (also undocumented) AXURL attribute. This works for both Chrome and Safari"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155524715", "body": ""}
{"comment": {"body": "We're all about polling at this fine establishment!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1004982932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155526429", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155542001", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155545457", "body": ""}
{"comment": {"body": "This goofy closure is necessary because the compiler complains about type inference when we write this:\r\n\r\n```swift\r\nguard let role: String = try? root.attribute(.role) else { return nil }\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1004992313"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155545825", "body": ""}
{"comment": {"body": "I'd guess this function is the most fragile (in that it is querying within the target app) -- is there value in catching any errors that happen here (so that at every level of walking through the view hierarchy we can catch any problems)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1004992420"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155546060", "body": ""}
{"comment": {"body": "Feels like a compiler bug ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1004992506"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155556897", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155561765", "body": ""}
{"comment": {"body": "What would we do with those errors? I can certainly add some logging", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1004997647"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155564964", "body": ""}
{"comment": {"body": "It's less about handling errors (though logging is all good), more about avoiding problem areas.  My understanding of the AX framework is that this stuff is fragile, my assumption is that this may fail in parts of view trees for a variety of reasons we can't anticipate, but we can try to at least skip the bits of the tree that fail and navigate the rest.   Maybe it doesn't matter since we are only really dealing with two apps that we believe should be well-behaved.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1004998757"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155568856", "body": ""}
{"comment": {"body": "I think that's what will happen in this case. The view walk is recursive, and it will bail on an entire subtree if there is an issue at the current node. It's best-effort but not guaranteed (which is ok I think?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1004999907"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155571039", "body": ""}
{"comment": {"body": "This was moved to `start()` so that we don't end up inadvertently requesting recording permissions before the permissions dialog is presented", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1005000614"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155572066", "body": ""}
{"comment": {"body": "@jeffrey-ng This is the IPC service integration for url slurping", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1005000935"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155597442", "body": ""}
{"comment": {"body": "OK \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1005008738"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#pullrequestreview-1155655446", "body": "Overall looks okay. Will need to test this out."}
{"comment": {"body": "There are developer builds which we may want to include.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1005038415"}}
{"comment": {"body": "What's going on here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3421#discussion_r1005039722"}}
