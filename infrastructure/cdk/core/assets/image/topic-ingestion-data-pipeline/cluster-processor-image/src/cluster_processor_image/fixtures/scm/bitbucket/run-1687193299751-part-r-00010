{"comment": {"body": "Why save the indices of the last packet in each channel and not references to the packets themselves?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/602/_/diff#comment-119325367"}}
{"comment": {"body": "Well, no, since each packet belongs to a specific anchor and has a dict channel\\_to\\_last\\_idx, but without the anchor.  \nIf I create a dict with tuple as keys in the packet storer, then I can\u2019t just copy the relevant dict to the packet, I need to go over each key and create a new dict.  \nI know in python this can probably be done in a single line :slight_smile:, still I like the way it is better.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/602/_/diff#comment-119332535"}}
{"comment": {"body": "I tried it at the beginning. Somehow the packets were not updated when more events arrived to the packet. I didn\u2019t investigate it just modified it to be indexes.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/602/_/diff#comment-119332889"}}
{"title": "Feature/BIS-6806 hill   fix outside window test", "number": 603, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/603", "body": "Fix outside window test - outside window should be lower than hill height.\nAlso, make the code clearer.\n\n"}
{"comment": {"body": "I neutralized this check by using fast\\_abs\\(\\), so regression tests won\u2019t fail. This neutralization was part of Michael and Nuriel\u2019s changes to expand thresholds for now. Now it will work the same as before only with clearer code.  \nOnce the algorithms matures and I add more modifications, we can de-neutralize it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/603/_/diff#comment-119339472"}}
{"comment": {"body": "You might want to simplify these conditions since you already checked for `!statistics_found` in line 105. MISRA would probably say something here\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/603/_/diff#comment-119615050"}}
{"title": "Feature/BIS-6809 fix large scale tests", "number": 604, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/604", "body": "fix large scale tests - note this fix is only relevant for one cfo cluster, not two\n\n"}
{"title": "V3 merge", "number": 605, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605", "body": "Including enablement of feature encryption       \n\n"}
{"comment": {"body": "What's behind adding feature encryption to our non-encrypted library?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-120515674"}}
{"comment": {"body": "LEVL_ENABLE_FEATURE_ENCRYPTION", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-120515720"}}
{"comment": {"body": "shouldn't the progress also be under LEVL_ENABLE_FEATURE_ENCRYPTION?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-120515725"}}
{"comment": {"body": "No. The progress is a huge structure and we want to encrypt just the features which I quite small and they are sent over the CAN bus.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-120516246"}}
{"comment": {"body": "See my prev comment. Features progress is not encrypted..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-120516251"}}
{"comment": {"body": "Yes. You are correct. We should have 2 modes:\n\n1. Not encryption at all\n2. Only feature encryption\n\nAll other modes should be dropped. We will fix that later.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-120516266"}}
{"comment": {"body": "how about simplifying LEVL_ENABLE_FEATURE_ENCRYPTION handling since LEVL_ENABLE_FEATURE_ENCRYPTION and LEVL_ENABLE_FULL_ENCRYPTION are mutually exclusive? Handling LEVL_ENABLE_FEATURE_ENCRYPTION could be done once.\r\n\r\n```\r\n#ifdef LEVL_ENABLE_FULL_ENCRYPTION\r\n...\r\n#else\r\n    #ifdef LEVL_ENABLE_FEATURE_ENCRYPTION\r\n    ...\r\n    #endif\r\n    ...\r\n#endif\r\n```\r\n\r\nSame comment for Levl_Train", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-120516369"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} what about this PR? should we merge it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-121665350"}}
{"comment": {"body": "Yes. We should.  \nThe develop has progress a bit. I\u2019ll do a rebase to see that everything is still working and will resubmit again.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-121674865"}}
{"comment": {"body": "You don\u2019t want to rebase because you\u2019ll \u201close\u201d the tag. It\u2019ll also be much more complicated than simply merging.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/605/_/diff#comment-121675282"}}
{"title": "FIN-867 add spinner and adv mode", "number": 606, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/606", "body": "Changed filter radio buttons to spinner.\nAdded option to change advertisement latency for battery performance testing."}
{"comment": {"body": "It\u2019s more like low/regular latency than low/high power. We already have low/high power for tx\\_power", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/606/_/diff#comment-119619761"}}
{"title": "Feature/BIS-6815 implement large scale wish list", "number": 607, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/607", "body": "Add preprocess-once flag for a much quicker run (feature extract can be done only once)\nAdd filter by boards and models\nAdd diffrent-imei-only flag (to test FPR only)\nAdd control num of packets during train and classify (for py only)\nAdd models to detailed scenarios when printing results\nNicer code: better handling of specific scenario - use the general flow to filter this scenario\n\n"}
{"title": "Hill regression test to actually check separation of devices", "number": 608, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/608", "body": ""}
{"title": "Added support for Android 10", "number": 609, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/609", "body": "Added support for android 10 where we are unable to query for IMEI using android's APIs.\nWill now load phone number automatically on start.\nFixed bug.\nAndroid app version 15.\n\n"}
{"comment": {"body": "Bless you!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/609/_/diff#comment-120685226"}}
{"comment": {"body": "Maybe change \u201cPhoneNumber\u201d to \u201cInventoryIndex\u201d or \u201cInventoryNumber\u201d or \u201cPhoneIndex\u201d or \u201cCatalogNumber\u201d or \u201cCatalogIndex\u201d\u2026 Phone number is very confusing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/609/_/diff#comment-120912372"}}
{"title": "ADB Jenkins uninstall bug fixed (actually fixed this time)", "number": 61, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/61", "body": "ADB Jenkins uninstall bug fixed (actually fixed this time)"}
{"title": "Feature/BIS-6816 investigate suspected leakage i", "number": 610, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/610", "body": "Regression tests dont consume too much memory (around 100MB), but when used with junit, catch produces a lot of data, more than 7GB.\nSolution - remove usage of junit for regression tests.\n"}
{"title": "No junit, should have no xml", "number": 611, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/611", "body": ""}
{"comment": {"body": "Also enable test\\_duration to run in the quick tests.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/611/_/diff#comment-119707626"}}
{"title": "Feature/BIS-6844 large scale tests to support hill", "number": 612, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/612", "body": "Feature extract again in classify when Hill feature used - it cant use the features that were pre-computed because the feature extraction progress they used aggregated all features (which is not the case in classify, that should aggregate only its features).\n  The pre-computed will be used for training only (many scenarios have the same training so reuse is important).\nEnable Hill in python tests\nFix bug: when using -scen with same pickle in train and classify, we got 4 scenarios instead of one\n\n"}
{"title": "Large scale time improvements", "number": 613, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/613", "body": "Re-use feature extract for classify as well; \nc classify by 35 packets each time; \nRun large scale in Jenkins on c only so it would run much faster\n\n"}
{"title": "BIS-6383 wifi and board temp regression tests", "number": 614, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/614", "body": "Board temperature online training:\n\nSplit to test with relaxed map for some tests and less-relaxed map for other tests\n\n\n\n\n"}
{"title": "Fail preamble feature if we don't have enough samples", "number": 615, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/615", "body": ""}
{"title": "Add hill diagram to large scale tests to help investigate hill issues", "number": 616, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/616", "body": "Added -hill-diagram flag that draws the hill features (last feature for each channel) vs the hill model."}
{"title": "FIN-879 app upload rates", "number": 617, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/617", "body": "Implemented different WiFi upload rates\nSlow - around 8 Kb/s\nNormal - around 60 Kb/s\nFast - around 300 Kb/s\nFastest - around 2500 Kb/s"}
{"comment": {"body": "there's actually a very low limit on the total number of Downloaders/uploaders, about 5, depends on the phone ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/617/_/diff#comment-120807681"}}
{"comment": {"body": "Would that limit throw an exception or just not add any more uploades? If there\u2019s no exception than it\u2019s ok with me, 5 would work too.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/617/_/diff#comment-120891898"}}
{"title": "Fix hill_draw - small bug and a few edge cases", "number": 618, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/618", "body": "Fixed hill_draw when theres no model (during training)\nFixed second if should be indented inside first if\nFixed trying to call hill_draw with failing feature\nChanged mechanism for finding hill_feature per channel - when packet is chosen on hill column, we go backwards until we find a successful feature event for each channel (we no longer keep this info inside the packet)\n\n"}
{"title": "Fix import", "number": 619, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/619", "body": ""}
{"title": "Changed demo protocol baudrate to 921600", "number": 62, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/62", "body": ""}
{"title": "Feature/BIS-6712 timing causes fn", "number": 620, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/620", "body": "Change Timing min packets for classify from 6 to 8\nchange p-value in in ks test from 0.011 to 0.003 to get TPR 99.9%\nImprove monitor for Timing - same ks test as in c, draw graph until row selected\nFix const 78 -> 73 ((180000-90000)/1250 + 1)\n\nMinor large scale script fixes riding on this PR just cause its easier:\n\nChange copy-paste issue\nAdd PyQt5 to requirements - for large scale script\n\n"}
{"comment": {"body": "Is there a reason for implementing this ourselves rather than using [scipy.stats.kstest](https://docs.scipy.org/doc/scipy-0.14.0/reference/generated/scipy.stats.kstest.html)?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/620/_/diff#comment-121081244"}}
{"comment": {"body": "Would be nicer to call the C function instead of code duplication", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/620/_/diff#comment-121081364"}}
{"comment": {"body": "Two different implementations that provide different numbers and thresholds - was difficult to compare.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/620/_/diff#comment-121084799"}}
{"comment": {"body": "You\u2019re right. Would require some work to convert to c structures, I\u2019ll add this option for another bug I opened on Timing: [https://jira.levltech.com:8443/jira/browse/BIS-6976](https://jira.levltech.com:8443/jira/browse/BIS-6976)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/620/_/diff#comment-*********"}}
{"title": "Feature/BIS-6980 large scale tests to run simple", "number": 621, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/621", "body": "Add basic test for large scale mechanism to verify its not broken\nWould run in bosch_integration, syntax/import failure would fail the build (problem can be seen in console log)\nFix problematic import for Jenkins\n\n"}
{"comment": {"body": "I\u2019m not sure making our builds depend on LEVL repo is a good idea.\n\nThe levl repo is very unstable, this can \\(and did\\) lead to builds failing for no apparent reason.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/621/_/diff#comment-*********"}}
{"title": "Fix bug - throwing when trying to draw timing in classification when there is an empty model", "number": 622, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/622", "body": "Draw model only when exists and its not empty\nCombine two functions (classify draw and train draw) to one\n\n"}
{"title": "Feature/BT-6 update monitor to have local master", "number": 623, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/623", "body": "Add demo_fingerprinter_x64_library library and python wrapper to it\n\nDLLed main_loop_9x/projects/ble_base_library/include/private/fingerprinting.h\n\n\n\nEvent deserializer to accept a generic st_port which is not a string\n\n\nAdd master standalone device which performs STs behavior\n\nPython port of STs fingerprinting_manual\nWorks without encryption\nUse it in monitor with Ctrl+Shift+T instead of Ctrl+T\nAdd option to load model file before starting the standalone device with Ctrl+Shift+O\nRestart master via Ctrl+Shift+R\nPersistent flash loaded and saved every model update in addition to user logs\n\n\n\nAdd model progress to ctypes\n\n\nNo validation and deepcopies in hydra\n\nThis is a hack for now since these take a lot of time when converting ctypes object to Hydra. This should be converted to a runtime option.\n\n\n\nAdding model storing/loading\n\n\n"}
{"comment": {"body": "Great job, we can finally throw all the ST boards to the trash, where they belong", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/623/_/diff#comment-121411238"}}
{"comment": {"body": ":laughing: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/623/_/diff#comment-121622143"}}
{"title": "Feature/hydra autogeneration", "number": 624, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624", "body": "Removed hydra's source code in favor of pip's version of hydra, added alpha version of elf to hydra script\nAdded app_start_stop.py script, useful for some of the tests\nAdded struct generation.\nSwiched events.py to use the structs from fingerprinting_structs.py\nNew version for d2h script, Converted more structs to be autogenerated\nHydra -> Hydras\nevents.py -> fingerprinting_structs.py\nFixed struct integrity tests.\nFixed struct integrity tests.\nFixed struct integrity tests.\nFix for ctypes enums\nFixed struct integrity tests.\nAdded requirements for hydras at a specific version\n\n"}
{"comment": {"body": "Maybe add \u201cThis struct is automatically generated using the `blablabla` script, do not modify it manually\" before each class definition?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121467306"}}
{"comment": {"body": "Yes, thought of this too, will be added to the d2h script.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121569164"}}
{"comment": {"body": "what\u2019s `bitsize ` doing here?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121617284"}}
{"comment": {"body": "I\u2019m not seeing the code that\u2019s generating those 2 empty lines", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121617561"}}
{"comment": {"body": "please keep `ANCHORS_TO_REPORT ` here since it\u2019s not autogenerated", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121619706"}}
{"comment": {"body": "it\u2019s not 6. it\u2019s `ANCHORS_TO_REPORT`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121619773"}}
{"comment": {"body": "* We\u2019re now introducing name conflicts between hydra and ctypes. Should be noted/verified that we don\u2019t use `from events import *` or similar anymore\n* How about artifacting autogeneration with every build in Jenkins?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121619881"}}
{"comment": {"body": "\u200c\n\n* Maybe add some prefix / suffix to all of them\n* What's the point? The generated file is tracked by git, the script has to manually be ran by developers making modifications and then staged/committed manually \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121620229"}}
{"comment": {"body": "generate\\_hydra\\_structs.sh is hardcoded to docker\u2019s elf output. It\u2019s not comfortable to generate it locally. Would be much easier to download from jenkins", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121620854"}}
{"comment": {"body": "non hardware docker works seamlessly on Windows/Mac, much essisr/faster than pushing and having to wait for Jenkins to make it for you. Even more so on Windows with the introduction of Docker-supporting WSL2", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121621245"}}
{"comment": {"body": "Let\u2019s add another way to generate those files.\n\nWe don\u2019t want to wait until:\n\n> WSL 2 is part of the development builds for Windows 10 20H1, which is expected for release around April 2020\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121622263"}}
{"comment": {"body": "I agree with omer", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121622958"}}
{"comment": {"body": "Great catch.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121623058"}}
{"comment": {"body": "Fair enough, but for now even running them through regular Docker is easier than manually editing them / waiting for Jenkins to do it. It can be done with a click of a button. If you disagree, maybe you\u2019re using it in a way that makes it somehow awkward - it shouldn\u2019t be\n\nAlso, you can change your Windows settings to get Insider updates, which will give you WSL2 right now", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/624/_/diff#comment-121625830"}}
{"title": "PR comments fixes", "number": 625, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/625", "body": ""}
{"comment": {"body": "I wish someday @{5b02c344cd95416ee040ad9c} would make a pull-request that doesn\u2019t include random unrelated improvements", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/625/_/diff#comment-121652181"}}
{"comment": {"body": "what about:\n\n* [https://bitbucket.org/levl/bosch\\_integration/pull-requests/624/feature-hydra-autogeneration/diff#comment-121619881](https://bitbucket.org/levl/bosch_integration/pull-requests/624/feature-hydra-autogeneration/diff#comment-121619881)\n* [https://bitbucket.org/levl/bosch\\_integration/pull-requests/624/feature-hydra-autogeneration/diff#comment-121569164](https://bitbucket.org/levl/bosch_integration/pull-requests/624/feature-hydra-autogeneration/diff#comment-121569164)\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/625/_/diff#comment-121654028"}}
{"title": "Feature/BIS-6977 hill outliers", "number": 626, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626", "body": "Add Hill outliers handling\nFix min Hill length to previously implemented reduced sampling (should be 40/2, not 40)\nDisable Hill for two WIFI tests since Hill fails them after min length change\n\n"}
{"comment": {"body": "MISRA issue: unsigned is compared with signed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121667521"}}
{"comment": {"body": "MISRA issue: memset return value is not used", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121667563"}}
{"comment": {"body": "would be nice to alias`PyFingerprintingFeaturesDisabled(False, False, True, False, False, True, False, False)` ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121667814"}}
{"comment": {"body": "MISRA issue: unsigned is compared with signed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121667823"}}
{"comment": {"body": "missing parentheses here", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121668296"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121675389"}}
{"comment": {"body": "Right", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121675457"}}
{"comment": {"body": "I don\u2019t think it\u2019s required here..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121675504"}}
{"comment": {"body": "Correct", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121675753"}}
{"comment": {"body": "It\u2019s a code in TODO that should be fixed at some point.. Rather not", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/626/_/diff#comment-121675949"}}
{"title": "Feature/misra is back", "number": 627, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/627", "body": "Bring back static analysis steps\nFix MISRA\n\n\n"}
{"title": "fix MISRA issues", "number": 628, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/628", "body": "Several issues raised just before klocwork reactivation"}
{"title": "Feature/BIS-3540 enable classify aging", "number": 629, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/629", "body": "Enable aging in classification for voting table features\nFix two bugs in aging mechanism:\n  (1) Current feature should get age 0\n  (2) The buffer is cyclic - so age ALL the feature sets in the buffer, even those after the current position (in case buffer already did a cycle)\nAdd a test for aging with several buffer cycles and two packet rates\n\n"}
{"comment": {"body": "Looks good!\n\nI suggest to add a test that specifically tests aging", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/629/_/diff#comment-121882285"}}
{"comment": {"body": "Right - will do, thanks", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/629/_/diff#comment-121886091"}}
{"title": "fix bug: heating should be done only in training mode", "number": 63, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/63", "body": ""}
{"title": "Removed multi-dimentional arrays from hydra", "number": 630, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/630", "body": ""}
{"comment": {"body": "Would this work for any number of dimensions in a multi-dimensional array?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/630/_/diff#comment-121998747"}}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/630/_/diff#comment-121998806"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/630/_/diff#comment-122008345"}}
{"title": "Large scale tests modifications", "number": 631, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/631", "body": "Disable Relay for large scale tests\nLarge scale tests on c to run until result or until max packets (instead of always until max packets)\nAdd total statistics for errors and no decision made, to alert us of cases of biased results (since errors and no decision made (need more data at last packet) are ignored in the result statistics)\nAdd failed features to detailed scenarios table as well (previously was only on groups table)\n\n"}
{"title": "Add another default test to large scale tests (for py) to make sure it does not break", "number": 632, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/632", "body": ""}
{"title": "Add total statistics per feature for large scale tests", "number": 633, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/633", "body": ""}
{"title": "Add online training option for left training packets (not used in training)", "number": 634, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/634", "body": "When running large scale tests with -o-train-left-train-packets, classify-finalize cycles will run using the the training packets that were not used during train (since in c training stops when system replies that it completed training).\nSo If training pickle has 5000 packets and 1500 were used for train, 3500 packets will be used in online training (divided to classifications with 35 packets each).\nStatistics regarding the number of successful online training cycles, num of failures / errors etc is provided in the detailed scenarios table as part of the large scale tests output."}
{"comment": {"body": "Please see why jenkins failed\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/634/_/diff#comment-122346169"}}
{"comment": {"body": "Of course", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/634/_/diff#comment-122346189"}}
{"title": "Add online training to large scale tests!", "number": 635, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/635", "body": "By using -o-train, after each train, large scale tests will find all similar pickles (with same imei and board) other than train and classify pickles, and will online train using all their packets.\nAdded another large scale tests run for online train after the basic run, results should be created in  different files. Note that this will more than double our current large scale tests runtime."}
{"title": "Limit number of online training cycles by using packets from as many pickles as possible", "number": 636, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/636", "body": "Limit number of online training cycles to 200 (in each cycle, 35 packets).\nLimit number of pickles for online training to 100 (so min cycles from each chosen pickle is 2).\nThat means max online training on 7000 packets (35*200).\nIf there are less that 100 similar pickles, we divide 200 by number of similar pickles (and round downwards) to get number of cycles per pickle."}
{"title": "Large scale test research", "number": 637, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/637", "body": "\n\nadd selective download from db\n\n"}
{"comment": {"body": "This changes the default \\(previously was True\\). So please add this flag to run\\_large\\_scale\\_tests.sh for:  \n`python3.6 ./pickles_creator.py`  \nto:  \n`python3.6 ./pickles_creator.py` -only-new-records  \n  \nAlso, if no one uses the PICKLE\\_ONLY\\_NEW\\_RECORDS anymore you can remove it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/637/_/diff#comment-123222303"}}
{"comment": {"body": "Nuriel I\u2019d rather the wifi change will not be pushed to develop. This is the pickles creator of the large scale tests.. In the near future I\u2019ll probably remove the wifi condition so we get both wifi an no-wifi. Anyway the WIFI\\_OFF\\_QUERY is missing `and (wifi_enabled=false or wifi_enabled is null)`.  \nBut I think adding all those conditions to the pickler make it over-abstract..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/637/_/diff#comment-123653119"}}
{"title": "Feature/BIS-7077 instfreq low pass filter", "number": 638, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/638", "body": "Add low pass filter for InstFreq\n\n"}
{"title": "Sigal - Feature/BIS-7077 instfreq low pass filter", "number": 639, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/639", "body": "Add low fass filter for InstFreq\n\n"}
{"comment": {"body": "It causes 100% false negatives, I\u2019ll have to investigate", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/639/_/diff#comment-122680744"}}
{"title": "Trained models will now be stored on flash & used on next boot unless a train command is sent again (QueryTask in protocol.py now has an option to disable train commands and go straight to Q)", "number": 64, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/64", "body": ""}
{"title": "Feature/BIS-7083 add max packets for reply", "number": 640, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/640", "body": "Limit number of packets received recently for response - if any anchor reached 31 packets recently (last 4 seconds) and theres still no decision, returns match.\nRemove specific hill mechanism that checked packets num.\nEnable Relay in large scale tests\n\n\n"}
{"comment": {"body": "Cool!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/640/_/diff#comment-122932476"}}
{"comment": {"body": "The time threshold should be 3 seconds.\n\nThis threshold is not related to the size of the aging that we have.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/640/_/diff#comment-122936778"}}
{"comment": {"body": "The aging time determines which features enter the decisions phase.. It would be less consistent if we distinguish the two.  \nWhat\u2019s the point of having two thresholds? It would create different behaviors at the beginning and afterwards:  \nIn the first 3 seconds, if you don\u2019t have enough packets the features would reply Need More Data and we would reply match.  \nThen, a second after, the features can find enough data in the last 4 seconds and reply No Match.\n\nDo we want to make this distinction?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/640/_/diff#comment-122958859"}}
{"title": "Not so horrible Instant Frequency", "number": 641, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641", "body": "Instant frequency\nMISRA\n\n"}
{"comment": {"body": "Do we still need it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122931867"}}
{"comment": {"body": "I find the lack of using ```statistics_get_variance_unbiased``` disturbing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122932396"}}
{"comment": {"body": "Very good!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122932887"}}
{"comment": {"body": "What should be done with the error?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122933090"}}
{"comment": {"body": "Yes, it\u2019s very hard to know what\u2019s going on with the instant frequency \\(there are A LOT of numbers to follow\\) and this debug code is essential for it. I have Python code that knows how to parse this debug info and display it in a useful manner. I\u2019d like to keep this debug code there, forever.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122933145"}}
{"comment": {"body": "Great name!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122933490"}}
{"comment": {"body": ":worried: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122933540"}}
{"comment": {"body": "Nothing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122935946"}}
{"comment": {"body": "I find your `statistics_t` interface unrelated and disturbing.   \nSeriously though, I don\u2019t use `statistics_t` and I don\u2019t feel like initializing one with M1 and M2 and n \\(which I need to calculate\\) just to do `* (n / n - 1)`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122953197"}}
{"comment": {"body": "That\u2019s unfortunate.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122958196"}}
{"comment": {"body": "Starting with avg = 0 + j0, n = 0 and `i = data_start`; would make the code more readable and simpler", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122959023"}}
{"comment": {"body": "Please reuse the code for calculating average", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122969824"}}
{"comment": {"body": "Will do", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122979098"}}
{"comment": {"body": "Another reason is to avoid large numbers in floats.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122980496"}}
{"comment": {"body": "Already gave reasons why not [here](https://bitbucket.org/levl/bosch_integration/pull-requests/549/bis-6184-add-instant-frequency-feature/activity#comment-117557937)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/641/_/diff#comment-122981133"}}
{"title": "Timeout for response should be 3 seconds", "number": 642, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/642", "body": "Aging is currently 4 seconds. The limit of max 31 packets is for timeout of 3 seconds.\n@{5a4500fe0cacf235de82a9d4} I didnt add disable mechanism since its not needed - large scale is not using the timestamps (rtc_now=0), and it should rarely happen that we get 31 packets to an anchor in 3 seconds AND reply need more data.\nIf we reach 3 seconds and dont reach these 31 packets for any of the anchors, we still reply need more data."}
{"title": "Feature/pc feature extract no relay corr", "number": 643, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643", "body": "Fixed bug in the relay feature extraction filtering algorithm.\nAdded anchor simulator and relay detect live plot, will be refactored, not final!\n\nall files in io_infra were copied from levl, no need to review.\n"}
{"comment": {"body": "Don\u2019t we want to fill this field?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643/_/diff#comment-123220101"}}
{"comment": {"body": "~~paths should be relative to file\u2019s path, since relative path could change according to calling module~~\n\nDisregard. I see that it\u2019s not used anywhere", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643/_/diff#comment-123220125"}}
{"comment": {"body": "These comments don\u2019t happen in levl repo\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643/_/diff#comment-123220185"}}
{"comment": {"body": "Do you think overriding the original value is better? Or even needed?  \nMaybe give another member called \u2018named\\_source\\_port\u2019?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643/_/diff#comment-123220222"}}
{"comment": {"body": "I also thought it\u2019s a bit weird, but @{5b72a213e72afd064c8a4ebd} wanted to do it like that, and he was the one who wrote this code.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643/_/diff#comment-123223140"}}
{"comment": {"body": "Well this whole file is redundant here anyway since we are not using it anyway.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643/_/diff#comment-123223152"}}
{"comment": {"body": "We should, will be added after the refactor.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643/_/diff#comment-123223170"}}
{"comment": {"body": "Please review and approve changes in relay\\_detect\\_feature.c as they are mandatory for V3.1, the rest are experimental scripts not intended to be merged in the first place and are being massively refactored right now to fit into the develop branch.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643/_/diff#comment-123223192"}}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} is currently unavailable. This decision is up to you now", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/643/_/diff#comment-123223379"}}
{"title": "Fix AR is_valid", "number": 644, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/644", "body": ""}
{"title": "Remove useless iq_cancel_cfo", "number": 645, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/645", "body": ""}
{"title": "Feature/replay on standalone master", "number": 646, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/646", "body": "Add FingerprintingManual replayer Ctrl+Alt+R to load pickle and re run them\nMoneky patch hydras to make it work faster\n\n"}
{"title": "Row selection to fix diff", "number": 647, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/647", "body": ""}
{"comment": {"body": "Y do we need mac support?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/647/_/diff#comment-123298198"}}
{"comment": {"body": "Accessibility for the technologically impaired is essential for maintaining equality ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/647/_/diff#comment-123298445"}}
{"comment": {"body": "That\u2019s very considerate of you. Good job.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/647/_/diff#comment-123298839"}}
{"title": "Feature/BT-5 cfo classification when no model", "number": 648, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/648", "body": "ST libfingerprinting with feature encryption\nDon't draw model when no model\n\n"}
{"title": "Feature/event deserialiser performance", "number": 649, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/649", "body": "Separated ThreadedSerial to a process\nThis helps prevent events from failing on CRC checks."}
{"comment": {"body": "Please rebase on develop", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/649/_/diff#comment-123377866"}}
{"title": "Training from app", "number": 65, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/65", "body": "Add the option to initiate training from the app by sending a unique packet"}
{"comment": {"body": "Maybe a small comment here explaining that you simulate a Bosch-UART demo command with the same TID as the one found in the packet", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/65/_/diff#comment-76986100"}}
{"title": "Fix recorder main bug", "number": 650, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/650", "body": ""}
{"title": "ThreadedSerial's background process is now a daemon, which should make it die when its parent is dead.", "number": 651, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/651", "body": ""}
{"title": "Feature/monitor enhancements", "number": 652, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/652", "body": "Replay with multi devices\nFix save as dataframe\nSeparate CFO draw by channel (different colors per channel)\nLocal master support button\nBetter start train sync with recordings\n\n"}
{"comment": {"body": "Why is this a thread?\n\nIs it supposed to replace ThreadedSerial?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/652/_/diff#comment-123784671"}}
{"comment": {"body": "There are not real time constraints to the replay feature. It\u2019s in another thread to not block the UI thread", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/652/_/diff#comment-123815660"}}
{"title": "Concurrent connection & advertisements IQ capture.", "number": 653, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/653", "body": "Added project\nWell, something works\nExtraction of connection and advertisement IQ is now working :)\nRestored watchdog\n\n"}
{"comment": {"body": "Can you keep the previous builder arguments?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/653/_/diff#comment-123884295"}}
{"comment": {"body": "It sort of destroys my VM since it has less than 8 cores.\n\nThis way it just uses the optimal number of jobs for your machine.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/653/_/diff#comment-123896143"}}
{"comment": {"body": "Do you need the comments?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/653/_/diff#comment-124162652"}}
{"comment": {"body": "Do you need the comment?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/653/_/diff#comment-124162660"}}
{"comment": {"body": "but `parallelizationNumber` is now 2\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/653/_/diff#comment-124163451"}}
{"title": "Feature/instfreq monitor", "number": 654, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/654", "body": "\n\ninstfreq monitor structs\nD2H PEP friendly & type hints for arrays\nRegenerate hydra\nevents.py refactor\nInstfreq monitor\n\n"}
{"comment": {"body": "Maybe we\u2019d use a better abbreviation other than ars\u2026 doesn\u2019t sound appropriate ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/654/_/diff#comment-124159625"}}
{"comment": {"body": "Sounds great to me, comment ignored", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/654/_/diff#comment-124162118"}}
{"title": "Instfreq threshold should be -4 in log10 not log e (-9.2 == -4*ln(10))", "number": 655, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/655", "body": ""}
{"title": "Feature/BIS-7139 less FN with CFO", "number": 656, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/656", "body": "Increase min num of packets for learnt board temperatures for CFO with wifi \nCreate 2nd lobe based only recent packets\nxfail tests (Yair is working on new data)\n\n"}
{"comment": {"body": "What about `evaluate_online_training_cfo_update`\\(\\)?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/656/_/diff#comment-124162763"}}
{"comment": {"body": "To be handled in another PR. This one is busy enough", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/656/_/diff#comment-124163130"}}
{"title": "Only update instfreq if all other voting features match", "number": 657, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/657", "body": ""}
{"title": "ST builds shouldn't run together", "number": 658, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/658", "body": ""}
{"comment": {"body": ":thumbsup: , Thank you!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/658/_/diff#comment-124163891"}}
{"title": "Upgrade gradle version so it hopefully stops crashing randomly", "number": 659, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/659", "body": ""}
{"title": "Xioami send cpu temperature on andriod app", "number": 66, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/66", "body": ""}
{"title": "BIS-7149 implement cfo channel slope fea", "number": 660, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660", "body": "Implement CFO Channel Slope feature\nAdd CFO channel slope to monitor\nLarge Scale Classify to get max 31 valid packets\n\n"}
{"comment": {"body": "Maybe only push if the CFO votes match?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124213447"}}
{"comment": {"body": "Why did you cast this to uint16? why not just make it 32 bit?\n\nanyway, each of the flags should be casted to 16 bit aswell.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124214401"}}
{"comment": {"body": "Not really needed. They can be tested separately", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124214402"}}
{"comment": {"body": "But it can be a nice indication that the CFO is an outlier", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124214819"}}
{"comment": {"body": "Is this for the implementation of aging?\n\nWe wanted to implement ageing in a different way to the non-voting features, this will also save a lot of space.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124215105"}}
{"comment": {"body": "Could be uint8 to save some more space.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124215304"}}
{"comment": {"body": "This only takes up space in the read-only code flash \\(Which is huge\\), so it doesn\u2019t really matter", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124215566"}}
{"comment": {"body": "Create a preset for this too", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124216367"}}
{"comment": {"body": "CFO model might be mistaken. That\u2019s why the passing threshold for CFO model is 40% of the packets.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124216969"}}
{"comment": {"body": "`FEATURE_MASK_CFO_CHANNEL_SLOPE  ` does it really need casting? It\u2019s not different than the other macros", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124217071"}}
{"comment": {"body": "Great PR! Quality code and tests!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124217447"}}
{"comment": {"body": "Apparently it is.. the complier \\(or MISRA, I don\u2019t remember\\) interprets it as uint8\\_t and cries that the shift is too large. The uint16\\_t fixes it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124220811"}}
{"comment": {"body": "The compiler interprets the the first 8 as uint8\\_t. The last one needs to be casted otherwise the compiler \\(or MISRA\\) cries that it\u2019s not large enough.\n\nNow, I can\u2019t change all of them because the voting features mask is 8 bits. That would require changing everywhere and increasing the voting features mask even though it only needs 8 bits.\n\nThe issue is that these flags are used both for disabling features \\(a uint32\\_t field\\) and for voting features results and validity.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124221558"}}
{"comment": {"body": "This is for the feature - it requires the history of cfo and channels that arrive. The aging for this feature actually uses the voting table mechanism, since the history of this feature is completely parallel to the history of the voting table - it just needs different fields for this feature.  \nI can\u2019t put these extra fields inside the voting table because this is not a voting feature, so there\u2019s a different structure for it, and it uses the voting table to control the aging.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124222770"}}
{"comment": {"body": "Naa.. that\u2019s too much. How do you want to call such a preset?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124224448"}}
{"comment": {"body": "We should ask ourselves what the hell even `False, False, True, False, False, True, False, True` means, why did we pick those, and then we can give it a name. But the way it is. With no name, it is very confusing.\n\nMy general goal is that the \u2026. True, False \u2026 lists only appear in a single place together along with all the other presets, each with a name that indicates where it might be useful.\n\nThe reason for that goal is so we don\u2019t have to look everywhere for those lists every time we add a new feature, it\u2019s all in the same place.\n\n\u200c\n\nIt might not be relevant to this pull request, I think I\u2019ll take care of this in a separate one", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124225037"}}
{"comment": {"body": "I think the presets should be simple and common..  \nThis one probably needs a comment \\(that would be a guidance for future features as to whether they are relevant and should be enabled\\).  \nIn these cases I just tried to understand what the test checks and if the new feature is appropriate for the test.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124226209"}}
{"comment": {"body": "Yay :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124226399"}}
{"comment": {"body": "Wrote this and than deleted:\n\nMaybe this should be seperated to another file.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124235688"}}
{"comment": {"body": "Oh. ok, got it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124235852"}}
{"comment": {"body": "Ok, If I'm not mistaken, the same thing happens in the timing feature, correct?\n\nIf so, I think it should be modified to use the same mechanism as well.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124236186"}}
{"comment": {"body": "How is it different from all the other features..? all of them have such functions in the libfingerprinting..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124251328"}}
{"comment": {"body": "It works similarly but the data is not parallel to the voting table, due to two reasons:  \n\\(1\\) It contains intervals between features - not features;  \n\\(2\\) Even more of a problem - it doesn\u2019t contain invalid intervals \\(voting table can contain data for invalid cfo and preable, for example\\).\n\nSo the data in both structures is not parallel and I can\u2019t use the voting table ages for timing.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124252439"}}
{"comment": {"body": "We should generally dump bad habits :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124265024"}}
{"comment": {"body": "Right :slight_smile: So after you dispatch the classify function of each feature to the classification folder, I\u2019ll do the same.  \nBTW I think that uniformity is more important in terms of code order and knowing where to look. I think I initially wrote this function inside the classifier and then put it in the same place as all the features.  \nAnyway we should refactor the fingerprinting.c at some point, but I don\u2019t think some features should be located differently than others.  \nAnd these were way too many words on this issue..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/660/_/diff#comment-124279112"}}
{"title": "Added support for programming using eclipse on macs", "number": 661, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/661", "body": ""}
{"title": "Feature/BIS-6730 wifi doesnt return MINOR UPDATE", "number": 662, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/662", "body": "Update online train regression tests to perform multiple online trains per data set (using additional flag thats true only for online train wifi)\nOnline train checks for MAJOR/MINOR update based on some information of candidate model (evaluate_online_training_cfo_update)\nWifi Online train performs finalize after 50 and not 52 packets (since its never reached if MATCH is returned after 25 packets and model can store only 26 packets = 51 packets in total < 52 packets criterion)\n\n"}
{"title": "Feature/disable relay detect", "number": 663, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/663", "body": "Disabled analog relay detection feature for v3.1\nRestored optimal number of jobs\n\n"}
{"comment": {"body": "Wow!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/663/_/diff#comment-124227370"}}
{"title": "Added check for number CLI arguments in relevant scripts", "number": 664, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/664", "body": ""}
{"title": "Monitor stuff", "number": 665, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/665", "body": "Only reuse USB dialog on Windows\nInstfreq shorter text on graphs\nInstfreq triggered CFO better representation\n\n"}
{"title": "Instfreq minor/major logic", "number": 666, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666", "body": ""}
{"comment": {"body": "please simplify the control flow here. nest the `if`s so that only 1 `get_multi_anchor_classify_finalize_result` call happens", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124289720"}}
{"comment": {"body": "I don't see how this can be simplified. The calls are different, one minor one major ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124291261"}}
{"comment": {"body": "they also have a conditional continue between them", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124291324"}}
{"comment": {"body": "Do you check this on the new model or the old?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124295427"}}
{"comment": {"body": "`get_multi_anchor_classify_finalize_result` is called twice when `added_packets >= INSTFREQ_FINALIZE_MINIMUM_FOR_MAJOR_UPDATE`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124296544"}}
{"comment": {"body": "Old model. If the old model is not triggered yet we would like to major update so it comes closer to getting triggered", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124302012"}}
{"comment": {"body": "I\u2019m aware of that, but it\u2019s a very cheap function and I don\u2019t see how you could rewrite the code to make it more simple with just 1 call to that function", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124302189"}}
{"comment": {"body": "Waiting for approval @{5b02c344cd95416ee040ad9c} @{5b41d9de10d57114135eca66} @{5a49d431ef77662a7583f8f0} @{5a4500fe0cacf235de82a9d4} @{5c98864d67af62601c20d976} ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124305445"}}
{"comment": {"body": "```\nif(!instfreq_is_cfo_correction_triggered && added_packets >= INSTFREQ_FINALIZE_MINIMUM_FOR_MINOR_UPDATE) {\n  MAJOR\nelse\n  if added_packets >= INSTFREQ_FINALIZE_MINIMUM_FOR_MINOR_UPDATE\n    MINOR\n  \n```\n\nWill behave the same. Refactor if coverage is an issue", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124369621"}}
{"comment": {"body": "But then I have to introduce another variable that will hold the MAJOR and MINOR and then I\u2019ll have to call `get_multi_anchor_classify_finalize_result`  on that variable and so it becomes just as complex or even more. I don\u2019t see the point in changing it, thank you for the suggestion", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/666/_/diff#comment-124372762"}}
{"title": "Feature/BIS-7220 large scale tests to support wi", "number": 667, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/667", "body": "Allow scenarios with wifi on only in large scale tests\nAdd large scale run for wifi on\n\n"}
{"comment": {"body": "What about the calculations you do when you test for error from model? Wifi model has 1 or 2 intercepts - error should be calculation from the closer intercept \\(if 2 lobes exist; there\u2019s a has\\_2\\_lobes flag in the wifi model\\)\n\nMore specifically: in `collect_feature_info_c`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/667/_/diff#comment-124371464"}}
{"comment": {"body": "Yes - adjusted the code so that the lobe with the smallest diff from cfo is now used.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/667/_/diff#comment-124417920"}}
{"title": "Create monitor dataframe in a tidy format", "number": 668, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/668", "body": "Short function to organize the monitor data into one dataframe in a tidy format. I tested it a bit, it works but might crash in some cases (e.g. no data in one event)."}
{"title": "Added stack usage statistics", "number": 669, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669", "body": "Added a script that analyzes GCC-generated metadata and produces the total stack usage of a function (including further function calls).\nAlso removed usage of function pointers in transient_extraction.c that prevented analysis."}
{"comment": {"body": "Nice,   \nCan you please paste here a link or a few lines of how the output looks like?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124412095"}}
{"comment": {"body": "\u200c\n\n```\n[\n  {\n      \"name\": \"Levl_TrainReset\",\n      \"local_stack_usage\": 32,\n      \"total_stack_usage\": 72,\n      \"source\": \"libfingerprinting/Debug9x/src/LEVL_fingerprinting.o\",\n      \"unresolved_calls\": [\n          \"memset\",\n          \"memcpy\"\n      ]\n  },\n  {\n      \"name\": \"feature_set_unpack_inplace\",\n      \"local_stack_usage\": 16,\n      \"total_stack_usage\": 72,\n      \"source\": \"libfingerprinting/Debug9x/src/feature_extraction/feature_set_unpacker.o\",\n      \"unresolved_calls\": [\n          \"memmove\",\n          \"memcpy\"\n      ]\n  },\n# ...\n]\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124412754"}}
{"comment": {"body": "@{5b72a213e72afd064c8a4ebd} asked that it would fail when a function goes above some threshold, but I\u2019ve yet to implement this.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124412887"}}
{"comment": {"body": "It should also fail when the stack-usage is unbound \\(happens with fn-pointers and recursion\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124413111"}}
{"comment": {"body": "Why not parse the `stack_analysis.json` in `/home/<USER>/repos/bosch_integration/libfingerprinting/tests/code_quality_tests/test_code_quality.py` in a new `test_*` function \\(it gets automatically ran if it\u2019s called like that, see [pytest](https://docs.pytest.org/en/latest/)\\) and check against some arbitrary threshold \\(for now set it to whatever allows it to pass \\+ some extra\\)? Should be a one-liner, unless I\u2019m missing something", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124414291"}}
{"comment": {"body": "How is recursion detected? Does it detect indirect recursion \\(a calls b calls \u2026 calls a\\)?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124414601"}}
{"comment": {"body": "We should also have the analysis done for our PowerPC env \\(AKA ST\\). Is this also in the scope?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124415309"}}
{"comment": {"body": "With the green hills compiler this is not going to be the same. But it costs so much money that I bet they have built-in utilities for stack usage analysis. BRING OUT THE COMPILER BOOKS", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124415753"}}
{"comment": {"body": "In the end, we need to check the stack usage of each of our public API functions, and make sure the maximum of those does not pass the threshold", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124415874"}}
{"comment": {"body": "Have mercy", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124416010"}}
{"comment": {"body": "We\u2019re not allowed recursions \\(thank MISRA\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124416229"}}
{"comment": {"body": "praise MISRA", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124416305"}}
{"comment": {"body": "What can be done with unresolved calls such as to `memcpy`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124416320"}}
{"comment": {"body": "Not much, we have no way to get stack-usage data for precompiled objects.  \nWe can compile libc once for ARM and extract the data and enter it manually \\(the script supports that\\).\n\nOtherwise we have to assume that all libc calls we use are bounded and have a reasonable stack usage.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124420545"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} @{5a49d431ef77662a7583f8f0} is this enough? Can Bosch be provided with the stack size we\u2019re using without the standard functions?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124421152"}}
{"comment": {"body": "Good point, we need to discuss it\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/669/_/diff#comment-124437403"}}
{"title": "Collection extra data (cfo + temps) each x packets", "number": 67, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/67", "body": ""}
{"comment": {"body": "Great coding style!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/67/_/diff#comment-77036717"}}
{"title": "Update acceptance range for phone temperature for unlearnt temperatures", "number": 670, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/670", "body": "Can stop replay"}
{"title": "Smaller feature struct (256)", "number": 671, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671", "body": ""}
{"comment": {"body": "What\u2019s the reason for this change? Padding?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-124747863"}}
{"comment": {"body": "Since it\u2019s surrounded by 32-bit fields, it\u2019s taking up uint32\\_t anyway, so I thought might as well make it less confusing/limited", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-124755144"}}
{"comment": {"body": "I tried moving it somewhere else with more 16-bit fields so it doesn\u2019t take up the extra 2 bytes but it required too much changes in other code so I gave up", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-124755199"}}
{"comment": {"body": "Good riddance. Thanks!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-124758997"}}
{"comment": {"body": "`AssertionError: Hydra (11636) vs board (11644) struct <class 'events.ModelProgressEvent'> size mismatch`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-125099264"}}
{"comment": {"body": "```\nAssertionError: Hydra (11636) vs board (11644) struct <class 'events.ModelProgressEvent'> size mismatch\n```\n\n!!!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-125101539"}}
{"comment": {"body": "!!! I\u2019m trying to fix it but the !!! d2h.py !!! script is !!! giving !!! me  bad !!! structs !!!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-125138742"}}
{"comment": {"body": "What does it do? Omit trailing padding?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-125139425"}}
{"comment": {"body": "Don\u2019t feel like investigating but when I run it on the cloud machine I get different results than my local machine :man_shrugging:  \nWe\u2019ll see if that makes the difference that causes the error once the build is done", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-125139752"}}
{"comment": {"body": "Please approve", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-125192337"}}
{"comment": {"body": "It turned out to be a problem with the manual structs in `events.py` that are not generated by `d2h.py`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-125192861"}}
{"comment": {"body": "Use slack\u2019s nudge", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/671/_/diff#comment-125196568"}}
{"title": "Added optional custom cache directory for S3 download", "number": 672, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/672", "body": ""}
{"title": "Struct size analysis utility", "number": 673, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/673", "body": ""}
{"comment": {"body": "Cool!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/673/_/diff#comment-125083787"}}
{"title": "Feature/hydras3", "number": 674, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674", "body": "This update brings us Hydars version 3, with major performance improvements.\nAnd Major performance improvements for the monitor."}
{"comment": {"body": "We should probably lock onto a commit rather than a branch.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125049295"}}
{"comment": {"body": "we should probably decide if we\u2019re using u8 or uint8\\_t and stick with that", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125051898"}}
{"comment": {"body": "Should be \\`isinstance\\(hydra\\_member, hydras.array.Array\\)\\`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125053625"}}
{"comment": {"body": "I\u2019d rather just lock on to a version from pip when it\u2019ll be uploaded", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125055018"}}
{"comment": {"body": "this was auto generated.\n\nthe cause for this is the way d2h interpreters our code.\n\na change for this should come from hydra.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125055371"}}
{"comment": {"body": "This does pretty much the same thing anyway\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125064819"}}
{"comment": {"body": "You can declare arrays with `[]`????? That\u2019s amazing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125098174"}}
{"comment": {"body": "please use a more standard name instead of i8", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125099105"}}
{"comment": {"body": "Very informative", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125099419"}}
{"comment": {"body": "I often encounter [https://stackoverflow.com/questions/11461356/issubclass-returns-false-on-the-same-class-imported-from-different-paths](https://stackoverflow.com/questions/11461356/issubclass-returns-false-on-the-same-class-imported-from-different-paths) so I advise against usage of `issubclass`. Is there an alternative?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125099427"}}
{"comment": {"body": "see comment above [https://bitbucket.org/levl/bosch\\_integration/pull-requests/674/feature-hydras3/diff#comment-125099427](https://bitbucket.org/levl/bosch_integration/pull-requests/674/feature-hydras3/diff#comment-125099427)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125099440"}}
{"comment": {"body": "Much informative", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125099441"}}
{"comment": {"body": "The old autogenerated comment was way better", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125236033"}}
{"comment": {"body": "I agree but the file is now generated by a script that is part of the hydras GitHub so it makes less sense to put the complex comment there. If you want we can append it via an extra local script", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/674/_/diff#comment-125237670"}}
{"title": "Feature/fix channel mhz bug", "number": 675, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/675", "body": "Fixed a bug in \"Online training after calibration\", coverage broken as a result\nAdd to make up for lost coverage\n\n"}
{"title": "PC Anchor simulator", "number": 676, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/676", "body": "Added anchors simulator.\nIts time for it to meet the develop branch.\n\nPlease note that there are some unfinished changes regarding the integration of the anchor simulator with the monitor.\nThey will be continued in a different PR."}
{"title": "V3 1 RC3 branch", "number": 677, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/677", "body": "Merge RC3 branch back into develop\n\nInstfreq minor/major logic\nAdd sklearn package to docker\n\n"}
{"title": "Import ios app", "number": 678, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/678", "body": "Initial Commit\nAdded temperature readings, works only when running out of sandbox\nChanged data format to base64\nFixed mistake in TID length\nFixed the way we send the IMEI\nMinor changes\nAdded support for non-jailbroken iPhones in the iPhone app.\nChanged LEVL_TEMPERATURE_UNKNOWN to be 2^16 to match fields from app.\n\n"}
{"comment": {"body": "Does the app work when running not out of sandbox \\(without sending temperature ofcourse\\)?\n\nWe want to test iphones\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/678/_/diff#comment-125322635"}}
{"comment": {"body": "Yes it does.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/678/_/diff#comment-125429246"}}
{"comment": {"body": "Please approve.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/678/_/diff#comment-125837274"}}
{"title": "Fixed extract_bosch_manfucaturer_spec returning a pointer to a local", "number": 679, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/679", "body": "This bug would result in corrupted reads when receiving adv-packets from iphones."}
{"comment": {"body": "\u05d1\u05e8\u05d0\u05e1\u05d8 \u05d6\u05d4 \u05dc\u05d0 \u05d4\u05d9\u05d4 \u05e7\u05d5\u05e8\u05d4", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/679/_/diff#comment-125137883"}}
{"comment": {"body": "But there\u2019s no MISRA Rust", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/679/_/diff#comment-125139304"}}
{"title": "Will now only use CPU temperature if it's available, will use battery temperature otherwise.", "number": 68, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/68", "body": ""}
{"title": "Feature/BIS-7256 large scale to support running", "number": 680, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/680", "body": "Add framework to run a specific folder datasets\nFix bug with -o-train\nFix small edge case\n\n"}
{"comment": {"body": "So it\u2019s both passed around as parameter and set globally? Or am I just misunderstanding?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/680/_/diff#comment-125198043"}}
{"comment": {"body": "PICKLES\\_PATH is actually a get\\_pickles\\_path\\(\\) function.. So it\u2019s global to all modules. I used the method in the link you sent me.  \nI only pass it to functions when creating new processes - I have to pass it or the default will be used in the new process.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/680/_/diff#comment-125206077"}}
{"comment": {"body": "Environment variables are inherited by child processes, just saying...", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/680/_/diff#comment-125210060"}}
{"comment": {"body": ":slight_smile: Yea, I heard you silently in my head saying that..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/680/_/diff#comment-125211749"}}
{"comment": {"body": "What\u2019s behind this break?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/680/_/diff#comment-125235567"}}
{"comment": {"body": "I don\u2019t want the search to be recursive. After we get the higher level files, that\u2019s it. I\u2019ll add a comment.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/680/_/diff#comment-125258935"}}
{"title": "Fixed iPhone handling", "number": 681, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/681", "body": "Fixed BOF when packet_len > 33\nFixed monitor's parsing of TID packets\n\n"}
{"comment": {"body": "bless you", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/681/_/diff#comment-125319285"}}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/681/_/diff#comment-125320186"}}
{"comment": {"body": "am blessed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/681/_/diff#comment-125320951"}}
{"title": "Force cfo wifi online training 2 steps", "number": 682, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/682", "body": ""}
{"title": "Fixed monitor-crashing typo", "number": 683, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/683", "body": ""}
{"comment": {"body": "Now that I think of it it\u2019s not actually a typo but @{5b41d9de10d57114135eca66} originally meant it to be packed\\_id \\(as in **pack**ing\\) but I recently modified it thinking it\u2019s a typo and seems I missed some \\(monitor\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/683/_/diff#comment-125381199"}}
{"comment": {"body": "it contained 2 ids \\(timestamp and anchor\\) hence packed, but it\u2019s not critical", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/683/_/diff#comment-125381574"}}
{"title": "Instfreq - Final fisher will not use ignore mechanism needed by samples fisher", "number": 684, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/684", "body": ""}
{"title": "Feature/BIS-7154 when training with wifi online", "number": 685, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685", "body": "Original issue: BIS-7154\nTL;DR: When training on messages with one connectivity type (wifi/no-wifi), classification session with the other connectivity type will not trigger a model update.\nThe issue was that we had some circular dependency our logic: Marking the model as updated was dependent on the connectivity status saved in the model state, but the status wasnt updated until we marked the model as updated.\nSub-Issue #2: We decided to rearrange the online training stages - now we always learn an updated model, only then do we evaluate the update flag (None/Minor/Major) based on the temporary model. This makes the code a little bit cleaner and allows us to avoid the aforementioned circular dependencies more easily.\nSub-Issue #3: We decided to specify a threshold to minor updates. When too few packets are accepted for online learning in a classification session, we report no update, revert model changes and dont bother Bosch with saving the model. The threshold is now set to be 0.35 * (the size of the stored packets container = CFO_MODEL_WIFI_MODEL_STORED_AGGREGATED_PACKETS_COUNT = 26).\nAdditionaly: Complied with Grishas BIS-7274; Added missing test for this case."}
{"comment": {"body": "Please remove temporary / irrelevant messages from PR description \\(such as `Complying with MISRA tests`, or `Temp fix, test not included. Solves original issue.`\\) so we can have a nice summary of what you actually meant to do without the intermediate steps on the way", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125414352"}}
{"comment": {"body": "A general overview of the issue being resolved and how it\u2019s being resolved would also be nice, since most of us are not familiar with the CFO code, especially the wifi/no wifi part", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125414735"}}
{"comment": {"body": "Updated description.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125420165"}}
{"comment": {"body": "Is it safe enough to assume that `wifi_cfo_model_updated` is initialized with `false`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125446209"}}
{"comment": {"body": "No need to duplicate `init_training`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125446425"}}
{"comment": {"body": "Will remove.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125450279"}}
{"comment": {"body": "Would add check.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125450857"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Shouldn\u2019t we test accepted packets only?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125554649"}}
{"comment": {"body": "No, since we can have online training with some packets being \u201cnot accepted\u201d in case where we learnt 1 lobe and have packets from 2nd lobe in online training,", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125562922"}}
{"comment": {"body": "As discussed need to add more tests to make sure we really generate a valid model finally", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125596196"}}
{"comment": {"body": "[https://jira.levltech.com:8443/jira/browse/BIS-7291](https://jira.levltech.com:8443/jira/browse/BIS-7291)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/685/_/diff#comment-125734600"}}
{"title": "Feature/BIS-7277 add bosch boards tests to large", "number": 686, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/686", "body": "Fix large scale tests execution\nAdd bosch_boards tests\nImprove pickles_creator - filter non required records earlier (=> much less queries when possible)\nAdd more infra to use a different datasets folder (to uploader and pickles_creator)\n\n"}
{"title": "Feature/BIS-7290 add iphones to large scale test", "number": 687, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/687", "body": "Allow running with tx_model in at least one of the pickles; do not create relay pickles\n"}
{"title": "V3 1 RC2 branch", "number": 688, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/688", "body": "Update acceptance range for phone temperature for unlearnt temperatures Can stop replay\nXfail\nAllow false positive between 2 S9s\nMore relaxed regression tests\n\n"}
{"title": "Fixed bug in hydra_object_to_ctypes_object", "number": 689, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/689", "body": ""}
{"title": "Only Mi devices are now supported by app", "number": 69, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/69", "body": ""}
{"title": "Fixed wrong hydra enum access", "number": 690, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/690", "body": ""}
{"title": "Added connection functionality to the android app.", "number": 691, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/691", "body": ""}
{"title": "Feature/cfo smaller model", "number": 692, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/692", "body": "WiFi model symmetric matrices\nFixed struct_treesize\n\nWe need to reduce further 800 bytes from each submodel. If we lose instfreq precision we get 720. The no-wifi CFO model symmetry is another 60 bytes. The rest of the 20 can be done by eliminating the padding (yellow parts, each 4 bytes):\n\nThere are plans to add another bucket of IF. that means adding another 3 huge squares to the 3x3 grid on the left. Im not entirely sure how were gonna fit that in our 12kb model goal."}
{"comment": {"body": "Great work!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/692/_/diff#comment-125730497"}}
{"comment": {"body": "Don\u2019t forget to update python\u2019s `LEVL_MODEL_ST_SIZE`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/692/_/diff#comment-125756494"}}
{"comment": {"body": ":thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/692/_/diff#comment-125757617"}}
{"comment": {"body": "Right", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/692/_/diff#comment-125779700"}}
{"title": "Feature/BIS-7295 support relay large scale tests", "number": 693, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/693", "body": "Adjust large scale tests to relay: \n\nWhen using -check-relay, run only scenarios where train is non relay, classify is relay and their imeis match.\nIn each such scenario, expect Not Match, and in post processing statistics treat these scenarios as if these are different imeis, so that match is False Positive and not match is True Negative.\n\n"}
{"title": "Add test to validate C and python constants", "number": 694, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/694", "body": "I thought itd be helpful\n"}
{"comment": {"body": "Good thinking :thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/694/_/diff#comment-125818056"}}
{"title": "Fix py bug in large scale", "number": 695, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/695", "body": "When running large scale tests on python, there was a bug when printing results (tried to take results from c run)"}
{"title": "Feature/BIS-7285 cfo model being updated after n", "number": 696, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/696", "body": "Instant frequency finalization now avoids model update if last classification was a no-match.\nThere was no test that ended classification without match and enabled inst freq. Added for coverage.\n\n"}
{"comment": {"body": "`get_multi_anchor_classify_finalize_result` needs to be called with `LEVL_CLASSIFYFINALIZE_NO_MODEL_UPDATE` instead \\(`LEVL_CLASSIFYFINALIZE_ERROR` reasons\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/696/_/diff#comment-125785333"}}
{"title": "Lower instfreq precision", "number": 697, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/697", "body": ""}
{"title": "Feature/BIS-7318 large scale tests add car recor", "number": 698, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/698", "body": "Add automatic creation of more pickles to sonata_records (Sonata/Tuscon records)\n"}
{"title": "increase phone temperature penalty to support phones with temp slope of 900", "number": 699, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/699", "body": ""}
{"title": "Feature/FIN-263 Transient extraction", "number": 7, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/7", "body": "removing extra_macros.h and moving min/max to fast_math\n\nsome refactoring to mitigate code duplication in weighted diff\nadding comments and making code clearer\n\n\n\nadding documentation to some modules\n\nadding more compliance with MISRA\n\n\n\nremoving mock file\n\nadded unittest for transient extraction with real packet"}
{"comment": {"body": "didn\u2019t you change the makefile to include all c files without specifically mentioning them?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/7/_/diff#comment-70279570"}}
{"comment": {"body": "You still need to make sure all is according to coding conventions, right?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/7/_/diff#comment-70280369"}}
{"comment": {"body": "Please tell everyone to approve the pull request once they reviewed it and all comments fixed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/7/_/diff#comment-70280442"}}
{"comment": {"body": "parameter names are meaningless in this function\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/7/_/diff#comment-70280694"}}
{"comment": {"body": "WIP. If you spot something I missed, let me know", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/7/_/diff#comment-70281132"}}
{"comment": {"body": "Will be fixed in the next PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/7/_/diff#comment-70281211"}}
{"title": "Use cpu temp as model", "number": 70, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/70", "body": ""}
{"comment": {"body": "Are you printing the phone temperature and not CPU temperature here on purpose?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/70/_/diff#comment-77075337"}}
{"title": "Fixed rendering of grisha diffs", "number": 700, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/700", "body": ""}
{"title": "Add SVW (volkswagen) configuration", "number": 701, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/701", "body": "Can be flashed with serial. IQ capture tested to be working"}
{"comment": {"body": "What\u2019s the verdict on manual\\_fingerprinting?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/701/_/diff#comment-126062883"}}
{"comment": {"body": "Not working", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/701/_/diff#comment-126062890"}}
{"title": "Raised android-app version to 17", "number": 702, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/702", "body": ""}
{"title": "Remove channel_mhz in python since it causes crashes", "number": 703, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/703", "body": ""}
{"title": "Get is_relay field from db as well in large scale pickler", "number": 704, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/704", "body": ""}
{"title": "Added option to not send temperatures even when using the jailbroken app", "number": 705, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/705", "body": ""}
{"comment": {"body": "I sure do understand all that Swift", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/705/_/diff#comment-126067091"}}
{"title": "Added conn agent to build", "number": 706, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/706", "body": ""}
{"title": "Raised hydra version", "number": 707, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/707", "body": ""}
{"title": "Feature/BIS-7260 add support for 0 slope 2 seperate models merge", "number": 708, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/708", "body": "Introducing 0 phone slope model for the 0 phone slope phones (MATE10 lite, MATE20 lite, J3) that will run in parallel with the regular model.\n\n\nCFO Wifi: add separate 0 phone slope model which will train independently of regular model. At online train, either 1 or both will perform online training based on own MATCH result.\n\n0 phone slope model doesnt have phone temperature histogram since it doesnt train the phone slope\n0 phone slope has hard coded 0 phone slope.\n\n\n\nCFO no wifi: future PR will have the same behavior as no wifi (i.e. separate models) but currently it calculates 0 phone slope model from regular model at first classification (it takes about 0.5ms)\n\n\nMonitor is updated to show the 4 possible acceptance ranges (1 or 2 lobes per regular or 0 phone slope model) per anchor, but no textual hints yet.\nLike said, another PR will follow for the no-wifi.\nStructures' exact size is not checked yet."}
{"title": "Added the connection RSSI feature", "number": 709, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/709", "body": ""}
{"comment": {"body": "MISRA requires parenthesis around each condition to prevent mis interpretation of the code ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/709/_/diff#comment-126932081"}}
{"comment": {"body": "I think you have some logical problem here.  \nIf one of the timestamp is not refreshed but you still don\u2019t have enough packets, you still need to returr MORER\\_DATA\\_NEEDED.  \nIn this case it will return NO\\_PARTICIPATION.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/709/_/diff#comment-126932115"}}
{"comment": {"body": "Really impressive work!\n\nWe just have to make the build and tests pass :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/709/_/diff#comment-126932130"}}
{"title": "correction of 960000 instead of 1e6", "number": 71, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/71", "body": "Fixed the correction with 25 len period, the running time is 76-80ms."}
{"comment": {"body": "what\u2019s the reason for this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/71/_/diff#comment-77086769"}}
{"comment": {"body": "To work with dialog 83", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/71/_/diff#comment-77090026"}}
{"title": "Internal classification enum", "number": 710, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/710", "body": "It differentiates between a match and a not-participated for each feature"}
{"comment": {"body": "How did this change?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/710/_/diff#comment-126350420"}}
{"comment": {"body": "No idea, but these are the correct values.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/710/_/diff#comment-126353694"}}
{"comment": {"body": "You need to add a test that some are `LEVL_CLASSIFY_INTERNAL_NO_PARTICIPATION` and some are not", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/710/_/diff#comment-126388076"}}
{"comment": {"body": "Good catch", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/710/_/diff#comment-126389251"}}
{"title": "Large scale tests modifications", "number": 711, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/711", "body": "Fix online train edge case\nEnable Timing\nAdd more overnight tests\n\n"}
{"title": "Added vivo (54) MEID to the list of IMEI's", "number": 712, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/712", "body": ""}
{"title": "Better ITD WiFi Tests", "number": 713, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/713", "body": "New data (not included) to pass refactored tests.  \nLocally, all tests pass except 148_2, which will never pass (learn lobe A, train online B, test online B ), which is xfailed."}
{"title": "Following Omer's recent PR, we need more tests", "number": 714, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/714", "body": ""}
{"comment": {"body": "When does it causes problems that this struct is missing from hydra?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/714/_/diff#comment-126393481"}}
{"comment": {"body": "We can\u2019t validate it since we don\u2019t have hydra format.\n\nI don\u2019t see that we use this struct ATM", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/714/_/diff#comment-126400598"}}
{"title": "Some didn't participate unit test", "number": 715, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/715", "body": ""}
{"comment": {"body": "Merge this already", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/715/_/diff#comment-126522522"}}
{"title": "Feature/BIS-7260 split to 2 models 0 slope and not", "number": 716, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/716", "body": "Consolidate CFO structures in various train/model/classify structs\nMove functions out of LEVL_fingerprinting (mainly CFO finalize logic)\nIntroduce CFO multi model (with multi being currently single)\nOnly move stuff - no new features introduced\n\nThis will allow next PR to introduce second CFO model with as few changes as possible\n"}
{"title": "Fixed bug where the app would advertise forever when turning", "number": 717, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/717", "body": "phone-screen off"}
{"title": "BIS-7382 relay regression test", "number": 718, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/718", "body": "Relay regression test - takes 3 phones with 2 anchors, trains on recordings with just the phone transmitting, classifies each phone against its own recordings with relay active.\nThe test runs and fails."}
{"comment": {"body": "How come all tests pass? Realy should fail the tests, no?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/718/_/diff#comment-126558052"}}
{"comment": {"body": "They failed when I ran them", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/718/_/diff#comment-126558951"}}
{"comment": {"body": "If you add a new regression test, you also need to add its name to `libfingerprinting\\regressions_main.cpp` file, circa line 139 \\(in not-online-training|without-encryption tests section\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/718/_/diff#comment-126612442"}}
{"comment": {"body": "Will do", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/718/_/diff#comment-126680236"}}
{"comment": {"body": "Please see why jenkins fails and then you can merge\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/718/_/diff#comment-126709551"}}
{"title": "Fix xfail test_QT_WIFI_004_Wifi_Off_then_On_Training_Classification_Same_Phone", "number": 719, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/719", "body": ""}
{"title": "China demo/bosch requested fixes", "number": 72, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/72", "body": "AES-b64 dump will use curly braces instead of brackets\nAdded some demo debug prints as requested by Bosch\nAdded even more demo prints as requested by mich"}
{"comment": {"body": "Thanks!\nLooks great.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/72/_/diff#comment-77702204"}}
