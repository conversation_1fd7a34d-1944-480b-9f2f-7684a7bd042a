{"title": "Bugfix/AvroS3ArchiverSink parameter name", "number": 598, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/598", "body": "We had a problem with our AvroS3ArchiverSink, where one of the parameters' name was changed from type to type_:\ndef create_pipeline(type_=None,                    \n                    topic=None,                    \n                    bucket_name=_bucket_name,                    \n                    **kwargs):\n    schema_name = _get_schema_name_from_type(type_)\nThe problem was that while that name change happened, no update was done in all the places that uses that logic (that kept using the wrong name). Therefore, that parameter was passed as one of the kwargs parameters, instead of the actual type_ that was left empty:\nsource, pipeline = create_pipeline(type=DeviceIntelligenceLog)\nInstead of\nsource, pipeline = create_pipeline(type_=DeviceIntelligenceLog)"}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} good catch!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/598/_/diff#comment-318559305"}}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18504)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/598/_/diff#comment-318561463"}}
{"title": "Merge relevant code to dev", "number": 599, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/599", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18986](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18986){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/599/_/diff#comment-318842498"}}
{"title": "Data archiver -phase 1", "number": 6, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/6", "body": "this is the first code, only for the integration tests.\n"}
{"title": "fix function", "number": 60, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/60", "body": ""}
{"title": "Fix levl unique id & corrupted WS Discovery return value", "number": 600, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/600", "body": "This PR fixes 2 unrelated issues: \n1. Change the unique_levl_id in the MatchResult to be a property instead of a field. This is due the fact that the id is always derivated from the device which is already a field in the MatchResult. It saves the need to update it explicitly. \n2. Change the return value from a corrupted ws discovery packet and add a unit test for a partial ws packet."}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/600/_/diff#comment-318581343"}}
{"title": "MEROSP-1277 Now writing the hostname group to the decision log as well", "number": 601, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/601", "body": "Just a little leftover - the hostname group was written only to the DB, but we can also write it to the decision log and make our testing life a bit easier. In addition, its a good indication for debug problems that might occur in the future.\nSome tests were updated as well to make sure we wont break it.\nA PR was also opened in eros-infrastructure to make sure we update the schema properly."}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} @{5dbeb866c424110de52552cc} the release cujo branch is in code freeze, let\u2019s discuss tomorrow if this is crucial fix to release", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/601/_/diff#comment-318650476"}}
{"title": "Align packer parser package version with latest version", "number": 602, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/602", "body": "The current version in the code is 0.8.0 but the one saved in the code artifact is 0.9.0, as can be seen when running the following command:\naws codeartifact list-package-versions --package eros-packet-parser --repository levl-pypi --domain levl --domain-owner 844647645876 --region us-east-1 --format pypi | jq -r '.defaultDisplayVersion'\nThis change will align the versions and allow the CI to bump the following version upgrades automatically."}
{"title": "refactor device_model_return_result_logic function", "number": 603, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/603", "body": "refactor device_model_return_result_logic function, trying to reduce runtime duration and achieve more readability.\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/149071251-Figure_3.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/603/_/diff#comment-318661183"}}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19567](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19567){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/603/_/diff#comment-318936847"}}
{"title": "Revert Hostname Uniquness from release, Will Be Moved to dev", "number": 604, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/604", "body": "This reverts commit ba070a0a4dae4283e3e81e6f50ed19a8e35f8094."}
{"comment": {"body": "We\u2019ll add it to our dev branch instead", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/604/_/diff#comment-318806123"}}
{"title": "updated ios 16 fp", "number": 605, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/605", "body": ""}
{"title": "Bugfix/MEROSP-1276 mac address validation", "number": 606, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/606", "body": "This PR comes as a fix to our release branch to overcome an issue that occurred  in the cujo integration - some of the sessions we parsed contained packets from different devices.\nIn order to improve our mechanism so we can handle such cases, we can add some validations while we parse the packets from the session. Each Eth and RT packet contain the  mac address theyre associated with them, so we can just compare that address to the mac address of the session were now parsing.\nFor most cases, we need to compare it with the source mac address of the Eth/RT packet. For DHCP ACK / DHCPv6 Reply messages, we need to compare it with the destination mac address.\nNote that were exposed to the type of the packet only after stepping one step inside our parsing mechanism. For example, we cant start with that condition check right in the beginning of parse_ethernet_packet because we dont know yet if its indeed a DHCP ACK / DHCPv6 Reply packet. For that reason, that check has to be done for every type of the packet, even in the price of a duplicate code line."}
{"title": "Merge release cujo to dev daily", "number": 607, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/607", "body": ""}
{"title": "Dhcp model update os 16", "number": 608, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/608", "body": "updated ios 16 fp\ndeleted changes\n\nrestoring before changes"}
{"title": "Flake8 commas", "number": 609, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/609", "body": ""}
{"comment": {"body": "passed regression:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19899](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/19899){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/609/_/diff#comment-319181346"}}
{"title": "Feature/metrics latency", "number": 61, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/61", "body": "Now calculating the decision time and the time the device spent in the pipeline\nRemoved unnecessery generate_latest()\ntemporary remove column\ntemp remove column\n\n"}
{"title": "Bugfix/MEROSP-1287 parralell post tenant faild", "number": 610, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/610", "body": "There is no real reason to download a helm chart, we can install it directly from its repo,\nin addition, I added the namespace prefix (folder) for the values file location to avoid overriding it when using it from the configuration API.\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} please redirect to dev. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/610/_/diff#comment-319109322"}}
{"title": "MEROSP-811, MEROSP-1127 / Hostname identification feature", "number": 611, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/611", "body": "After merging it to release/cujo, we reverted it due to freezing the branch.\nThis PR adds the logic that was reviewed and adds it above dev branch.\nRefer to those previous PRs: 1 and 2.\nThe matching PR in eros-infrastructure still exists :slight_smile:"}
{"comment": {"body": "what does `or \"\"` do?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/611/_/diff#comment-318945480"}}
{"comment": {"body": "nice unit testing", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/611/_/diff#comment-318946402"}}
{"comment": {"body": "It\u2019s for the case where we load a None hostname from the DB.\n\nNone or \u201c\u201c = \u201c\u201c\n\nSo the object will deserialize from the DB will be `NonrandomDeviceName(\u201d\u201d, HostnameGroup.N_A)`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/611/_/diff#comment-319078602"}}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/20353](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/20353){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/611/_/diff#comment-319466644"}}
{"title": "Infra for custom made ua regex", "number": 612, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/612", "body": ""}
{"title": "Infra for custom made ua regex", "number": 613, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/613", "body": "Our ua_parser package is working with preconfigured regex list to find indentifiers and os version  \nIn order to add our regex to the list I wrote this infra which loads the yaml file, parse the written regex and adds them to the package list of regex."}
{"title": "Typing merge optimization", "number": 614, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/614", "body": ""}
{"title": "Use hostname if exists to refine typing reuslt", "number": 615, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/615", "body": "Use either iPhone or iPad in the hostname can be useful to filter further refine the typing results. This feature is added with a low confidence score.\nRegression passed: \n"}
{"title": "Use formatted MACs in connected devices list", "number": 616, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/616", "body": "We saved connected devices with keys being MACs formatted as ints as received in the event. Internally we use pretty MACs strings to represent MAC addresses. This causes troubles when looking up the devices MAC address in the connected devices list (used to extract the interface details for the station)."}
{"title": "Add iphone5 rows on glinet to l2 model", "number": 617, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/617", "body": ""}
{"comment": {"body": "better to let @{621df03094f7e20069fd6ab2} handle this files", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/617/_/diff#comment-319164853"}}
{"title": "L2 model dry run", "number": 618, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/618", "body": "dry run - - start parsing eth packets\neth extractor\ntests fix\nbug fix\nadded canonical and manual labeling\nflake8 fix\nremoved import time\ndry run data\nfixed surface\niphone 5 data from cujo in glinet\nsorted columns and rows\nfixed labels\n\n\n\n"}
{"title": "Increase DHCPv4 fingerprint confidence", "number": 619, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/619", "body": "The bug was caused by the fact that we have in the device DB only Intel devices that are tagged as Android devices and hence the system thinks its an android device. Although the OS that was detected was Windows.\nCombined with the fact that mac_vendor feature has a higher confidence than DHCPv4 fingerprint, the former is preferred over the later.\n\nRegression passed: "}
{"title": "Feature label metrics", "number": 62, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/62", "body": "Now calculating the decision time and the time the device spent in the pipeline\nRemoved unnecessery generate_latest()\ntemporary remove column\ntemp remove column\nAdding labels to metrics\n\n"}
{"title": "all late changes", "number": 620, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/620", "body": "added canonical and manual labeling + fixed labels\ndry run data\nfixed surface\niphone 5 data from cujo in glinet\nsorted columns and rows\n\n\n\n"}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} Is there any difference in the set of expected results?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/620/_/diff#comment-319421845"}}
{"comment": {"body": "Please also include link to regression tests passing", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/620/_/diff#comment-319421978"}}
{"comment": {"body": "New devices has been added.  \nno difference with known devices", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/620/_/diff#comment-319425080"}}
{"comment": {"body": "It doesn\u2019t mean that those new devices cannot added collisions with the older devices", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/620/_/diff#comment-319425428"}}
{"comment": {"body": "There is no difference with the known collisions not new ones.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/620/_/diff#comment-319426797"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20247](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20247){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/620/_/diff#comment-319428327"}}
{"comment": {"body": "Can someone approve my PR???", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/620/_/diff#comment-319459869"}}
{"title": "Fix WS uuid collection in parser & change the WS data type to list of strings", "number": 621, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/621", "body": "Add the missing part in the packet parser main that collects the ws discovery data that was detected. Without it the data is not written to the db.\nChange the type of the ws discovery data to hold a list of uuids. The decision was made due to the nature of the ws discovery protocol that assign a small number of uuids to each device. This change causes the comparison of two devices that sends ws discovery packets to match devices that have at least one uuid in common. \n  In order to prevent a memory leak, the size of the list was limited to 5 elements, and for each update of the list, the oldest values are thrown away in case of an overflow.\nThe schema was changed to reflect the new type of the ws discovery data\nUnit tests for the list update were added and the existing one were refactored due to the change in type.\n\n"}
{"comment": {"body": "see the head of the file. this is a generated file. you need to change `features.proto`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/621/_/diff#comment-319450841"}}
{"comment": {"body": "[https://levltech.atlassian.net/wiki/spaces/TS/pages/8227389557/WS+Discovery+Feature](https://levltech.atlassian.net/wiki/spaces/TS/pages/8227389557/WS+Discovery+Feature){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/621/_/diff#comment-319451242"}}
{"comment": {"body": "Use `make build_proto_schemas`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/621/_/diff#comment-319451506"}}
{"comment": {"body": "Passed Regression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20319](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20319){: data-inline-card='' }   \n", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/621/_/diff#comment-319454564"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} OK", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/621/_/diff#comment-319460013"}}
{"title": "Bugfix/MEROSP-1203 classifier crashed when conn", "number": 622, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/622", "body": ""}
{"comment": {"body": "good catch", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/622/_/diff#comment-319452079"}}
{"comment": {"body": "what is the description?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/622/_/diff#comment-319452354"}}
{"comment": {"body": "[https://levltech.atlassian.net/browse/MEROSP-1203](https://levltech.atlassian.net/browse/MEROSP-1203){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/622/_/diff#comment-319455409"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20519](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20519){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/622/_/diff#comment-319482774"}}
{"title": "changing classifier profile to work with remote environment", "number": 623, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/623", "body": ""}
{"title": "fixed bug of no association matches", "number": 624, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/624", "body": "When there are no association matches (the association is not inside l2 model) the logic was to return a valid result. \nInstead we return a non-valid result."}
{"comment": {"body": "here I changed the value from 273 to 274 on purpose, so the data will not be in the l2\\_model", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/624/_/diff#comment-319506385"}}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20630](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20630){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/624/_/diff#comment-319528970"}}
{"title": "new l2 model", "number": 625, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/625", "body": "cujo dry run\npixel 3 data - MEROSP-1289\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20687](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20687)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/625/_/diff#comment-319629182"}}
{"title": "small refactor of device os", "number": 626, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/626", "body": "dont use string as version. use Version as version"}
{"title": "Enum refactor", "number": 627, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/627", "body": ""}
{"comment": {"body": "regression passed\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20771](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20771){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/627/_/diff#comment-320050296"}}
{"comment": {"body": "Why here value is still needed?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/627/_/diff#comment-320052690"}}
{"comment": {"body": "because `context_os_resolution` is being serialized as just `int` and not Enum", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/627/_/diff#comment-320053087"}}
{"title": "fixed bug of no association matches", "number": 628, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/628", "body": "Dont proceed if association matches is empty, even if probe is not empty."}
{"comment": {"body": "Give the PR a better title, please :\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/628/_/diff#comment-320057002"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/628/_/diff#comment-320058185"}}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20893](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20893)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/628/_/diff#comment-320060951"}}
{"title": "MEROSP-1312 apple watch duplicate model despite matching dhcp transaction id", "number": 629, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/629", "body": "Updated dhcpv4 fp model with watchOS fps\nTemporarily set dhcp transaction id as a strong feature. It will prioritize it over the type filtering.\n\n"}
{"comment": {"body": "Regression passed [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20939](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20939){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/629/_/diff#comment-320066733"}}
{"comment": {"body": "The DHCP fp update doesn\u2019t do anything, as with the current code only the first match is returned, so it can be reverted.\n\n![](https://bitbucket.org/repo/o5KReBa/images/3071367224-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/629/_/diff#comment-320066974"}}
{"comment": {"body": "Nice :ok_hand: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/629/_/diff#comment-320067043"}}
{"comment": {"body": "We should return all the results, the issue is in the code not the dhcp model.  \nWhen we update the code the update for the dhcp model will take effect.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/629/_/diff#comment-320069860"}}
{"comment": {"body": "I agree. But let\u2019s do it in a separate PR.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/629/_/diff#comment-320069937"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Can revert the change in the csv?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/629/_/diff#comment-320070761"}}
{"comment": {"body": "will do", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/629/_/diff#comment-320070987"}}
{"comment": {"body": "Done. Will update when tests pass, but give me all your approvals", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/629/_/diff#comment-320071261"}}
{"title": "Step metrics label", "number": 63, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/63", "body": "\n\nsupport tenant name label in metrics\n\n"}
{"title": "2022-07-31 l2 cujo model", "number": 630, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/630", "body": "new data from cujo\nadded data from cujo of apple watch - assigned to apple watch series 7\nadded iphone6s data from cujo\nfixed apple watch new fp - fe:7d:34:a7:18:64, ee:26:c3:a4:0b:ee, 18:7e:b9:26:d0:9f\n\n"}
{"comment": {"body": "Please attach link to regression passing", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/630/_/diff#comment-320069677"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21001](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21001){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/630/_/diff#comment-320070393"}}
{"title": "l2 lookup by fingerprint", "number": 631, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/631", "body": "embedded @{5a4500fe0cacf235de82a9d4} code from Comcast for concatenate features to one string column. l2_fingerprint is the index of the dataframe. \nknown issue: need to see about noisy values of l2_fingerprint"}
{"comment": {"body": "pipeline passed:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22141](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22141){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/631/_/diff#comment-320581505"}}
{"title": "Flake8 multiline container", "number": 632, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/632", "body": "adding flake8-multiline-container\n"}
{"comment": {"body": "That\u2019s a very good one, I love it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/632/_/diff#comment-320164136"}}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22256](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22256){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/632/_/diff#comment-320598036"}}
{"title": "Bugfix/MEROSP-1318 fix general device type description", "number": 633, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/633", "body": "The DHCPv4 FP module would return general_os information only when both vendor and os information was available. This caused to Windows and Android devices (where vendor information is rarely available) to be identified at level 0 and return empty model description.\nAfter the fix, devices are identified to level 2 and return a model description such as Android device or Windows device.\nFixing this issue also uncovered some issue with filtering devices that was also fixed.\n"}
{"comment": {"body": "PR for update regression tests automation repo: [https://bitbucket.org/levl/eros-automation/pull-requests/220/bugfix-merosp-1318-fix-general-device-type](https://bitbucket.org/levl/eros-automation/pull-requests/220/bugfix-merosp-1318-fix-general-device-type){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/633/_/diff#comment-320083830"}}
{"comment": {"body": "Regression pass: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21151](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21151){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/633/_/diff#comment-320084234"}}
{"title": "MLRv1/MLRv2 feature comeback for Apple Watches", "number": 634, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/634", "body": "Add MLRv1 parsing\nMaking also use of global MLR address (prefix with ff02::2:ff)\nFor now limiting the scope of the feature to be used only on devices that have been identified by our system as Apple Watches, to prevent unwanted FP detections\n\n"}
{"comment": {"body": "Regression pass: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21238](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21238)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/634/_/diff#comment-320185622"}}
{"comment": {"body": "nice formatting", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/634/_/diff#comment-320191757"}}
{"comment": {"body": "we really can\u2019t maintain two main branches anymore. dev is getting really far from release/cujo and better than release/cujo. consider merging to dev and start releasing to cujo from dev or a new branch named ml2 from dev", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/634/_/diff#comment-320215115"}}
{"comment": {"body": "Latest regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21354](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21354){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/634/_/diff#comment-320223585"}}
{"comment": {"body": "We just need to maintain this branch until the demos to Comcast/Charter next week. After that we can start maintaining a single branch and using dev again, also for CUJO relesaes.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/634/_/diff#comment-320223779"}}
{"comment": {"body": "General note: We got to have some strong link / cross validation of common families, consumer categories, OSes \\(broad categories that can be conditions in the code\\). The state today is that if, for example, `watchOS` changes to `WatchOS` in the models, there are places in the code that are affected and we will only find out in tests \\(or not at all\\).", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/634/_/diff#comment-320446735"}}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22034?launchesParams=page.page%3D1)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/634/_/diff#comment-320564563"}}
{"title": "the daily merge", "number": 635, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/635", "body": ""}
{"comment": {"body": "Regression\u2019s good?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/635/_/diff#comment-320428902"}}
{"comment": {"body": "no. 4 tests are failing", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/635/_/diff#comment-320443448"}}
{"comment": {"body": "woooooooohoooooooo:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22003](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22003){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/635/_/diff#comment-320557677"}}
{"title": "Merged in bugfix/MEROSP-1203-classifier-crashed-when-conn (pull request #622)", "number": 636, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/636", "body": "MEROSP-1203-classifier-crashed cherry pick from dev"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21442](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21442)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/636/_/diff#comment-320252053"}}
{"title": "breaking flake8-multiline-containers to multiple PRS - part 1", "number": 637, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/637", "body": ""}
{"title": "Temporarily disable dhcpv4 handshake (requested ip) from being a strong identifier", "number": 638, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/638", "body": "View bug description for background for decision."}
{"comment": {"body": "Passed UT and regression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21499](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21499){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/638/_/diff#comment-320274923"}}
{"title": "Cujo l2 model update 2022 08 01 + FP fixes", "number": 639, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/639", "body": "update L2 model with CUJO labeled devices \n\nBeside the model, after re-running the cujo data through our system we were still getting FP identity identification on the apple devices. This is solved by adding the following:\n\nMissed ICMP NS filter that should have ignored all NS packets that are sent with a source address (and are used to query other devices on the network) - this filtering was done on the pilot system implementation but for some reason was not transferred properly to the eros implementation\nLimiting ICMP NS feature to only run on Android/Linux based systems (this feature does not work on Apple / Windows operating system but those are querying addresses with ICMP NS that were assigned with DHPCv6 - which causes a collision if two devices were assigned the same DHCPv6 address in different times) \n\n"}
{"comment": {"body": "Regression pass: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21670](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21670){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/639/_/diff#comment-320404108"}}
{"comment": {"body": "no need to have two ifs. one is enough", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/639/_/diff#comment-320490862"}}
{"comment": {"body": "changing code in code freeze will cause troubles. we can add an issue to the known issues. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/639/_/diff#comment-320491239"}}
{"comment": {"body": "What do you mean?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/639/_/diff#comment-320492089"}}
{"title": "matching fixes", "number": 64, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/64", "body": "vault id is not a matching filter but a general filter\nchange how matching features looked in decision log (fixes  )\n\n"}
{"title": "add ui and client api deployment to pipelines", "number": 640, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/640", "body": ""}
{"comment": {"body": "find a way to get the variables from file. not this file.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/640/_/diff#comment-320520220"}}
{"comment": {"body": "Only \u201cSET\u201d section is helm values which are configured as deployments variables inside the repository deployment settings so we can change them statically before any deployment trigger,  as for the rest, they are the variables for the pipe himself \n\n[https://github.com/yves-vogl/aws-eks-helm-deploy](https://github.com/yves-vogl/aws-eks-helm-deploy){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/640/_/diff#comment-320522816"}}
{"title": "calling the function only when needed", "number": 641, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/641", "body": ""}
{"title": "new model 2022-08-02", "number": 642, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/642", "body": "new model 2022-08-02"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21854](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21854){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/642/_/diff#comment-320521755"}}
{"comment": {"body": "Let\u2019s not update the model in the release cujo unless we need due to critical issues / bugs / new devices.\n\nYou can merge those changes to the dev branch.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/642/_/diff#comment-320523978"}}
{"comment": {"body": "There is no new data.  \nIt is after splitting the pcap files", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/642/_/diff#comment-320524500"}}
{"comment": {"body": "I understand but we want to freeze the cujo\\_integraiton\\_ml1 branch and include more changes in it unless it\u2019s required.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/642/_/diff#comment-320524677"}}
{"title": "Bugfix/MEROSP-1319 - Data Health", "number": 643, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/643", "body": "This PR solves two problems to improve our data health measurement:\n\nAdding a new type of indication, in case we did get L2 data but didnt find a model for it. This is very important for our deployment, and will help us solve problems in real time.\nAdding the right case for the times we dont get L2 association or L2 probe at all. Up until now we missed this case in the data health checks and returned that nothing is wrong.\n\n"}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} let\u2019s merge this to dev only, I would like to freeze the branch as we are too close to the POC.\n\nThe only remainder is the multicast report v1 to release branch.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/643/_/diff#comment-320533227"}}
{"title": "Flake8 spellcheck", "number": 644, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/644", "body": "use whitelist.txt to add words"}
{"title": "Merge cujo release ml1 to dev 220803", "number": 645, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/645", "body": "Merged in feature/MLR_feature_comeback (pull request #634)\nMLRv1/MLRv2 feature comeback for Apple Watches\n\n\nMerged in feature/MEROSP-1367-enable-multiple-typing-results-dhcpfp (pull request #646)\n\nMerged in wispr_ios_15_6 (pull request #653) added iOS 15.6 wispr agent\n\n"}
{"comment": {"body": "Regression passed besides iphone12 test that should be resolved separately [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26101](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26101){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/645/_/diff#comment-323437306"}}
{"title": "Support Multiple fingerprints in DHCP FP", "number": 646, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/646", "body": "We are using DHCP FP to identify devices OS type, version.. The DHCP FP might have similar FP for different types of family devices, for example iOS and watchOS, thus the DHCP FP db can return now multiple options for same dhcp fp and the upper logic of typing algorithm should remove according other layers irrelevant options. For example layer 2 will identify the device as watch and thus we can remove the iOS results from OS options returned in DHCP.\nThanks to @{5ed4f1de9a64eb0c1e78f73b} for implementation, helping to progress while his absence."}
{"title": "Di information devices db", "number": 647, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647", "body": "DI information for cujo\nadded support_dfs\nuknown 5ghz band devices are left empty\nfix numerical columns\nfixed canonical name of galaxy s20 fe\nadded chipset column\nchange to \"Qualcomm\" and \"MediaTek\" from \"Qualcomm Inc\" and \"MediaTek Inc\"\nfixed int/inf columns\nfixed apple chipset\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22897](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22897){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647/_/diff#comment-321046377"}}
{"comment": {"body": "don\u2019t forget to merge to dev too", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647/_/diff#comment-321078185"}}
{"comment": {"body": "New report  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23399](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23399){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647/_/diff#comment-321701837"}}
{"comment": {"body": "The 0/1 fields in the device DB in some cases appears as \u201c1\u201d and in some cases as \u201c1.0\u201d.\n\nPlease fix this to all be integers. This can cause us a lot of trouble.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647/_/diff#comment-321703429"}}
{"comment": {"body": "This issue still open as far as I can see.\n\nAlso, don\u2019t forget to also update L2 model with the canonical name change.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647/_/diff#comment-321716154"}}
{"comment": {"body": "Can the chip vendor be \u201cQualcomm\u201d and \u201cMediaTek\u201d instead of \u201cQualcomm Inc\u201d and \u201cMediaTek Inc\u201d?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647/_/diff#comment-321717349"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647/_/diff#comment-321717442"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23652](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23652){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647/_/diff#comment-321718743"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23768](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23768){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/647/_/diff#comment-*********"}}
{"title": "remove client api chart from eros-chart", "number": 648, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/648", "body": ""}
{"title": "added iPadOS to dhcp model", "number": 649, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/649", "body": ""}
{"title": "changed DataArchiver bucket_name to one in a new S3 account", "number": 65, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/65", "body": "changed DataArchiver bucket_name to one in a new S3 account"}
{"title": "add flake8-tidy-imports", "number": 650, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/650", "body": ""}
{"title": "update devices db with more information", "number": 651, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/651", "body": "more information:\n\n6ghz\nwifi encryption\ndfs\n\n"}
{"comment": {"body": "If we\u2019re gonna use this in the demo, it should point to relase/cujo", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/651/_/diff#comment-*********"}}
{"comment": {"body": "Yes. \n\n@{621df03094f7e20069fd6ab2} Please update the target branch.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/651/_/diff#comment-*********"}}
{"comment": {"body": "There is a PR to release/cujo - [https://bitbucket.org/levl/eros-classifier/pull-requests/647](https://bitbucket.org/levl/eros-classifier/pull-requests/647){: data-inline-card='' }   \nThis PR is to keep dev up-to-date", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/651/_/diff#comment-321105896"}}
{"title": "new l2 model after splitting the pcap files", "number": 652, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/652", "body": "new l2 model after splitting the pcap files"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23124](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23124){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/652/_/diff#comment-321108282"}}
{"title": "added iOS 15.6 wispr agent", "number": 653, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/653", "body": "added iOS 15.6 wispr agent"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23183](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23183)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/653/_/diff#comment-321128675"}}
{"comment": {"body": "after fixing tests\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23225](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23225){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/653/_/diff#comment-321141650"}}
{"title": "Bugfix/MEROSP-1319 - Data Health", "number": 654, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/654", "body": "Re-opening this PR to dev since we freezed release/cujo.\nThis PR solves two problems to improve our data health measurement:\n\nAdding a new type of indication, in case we did get L2 data but didnt find a model for it. This is very important for our deployment, and will help us solve problems in real time.\nAdding the right case for the times we dont get L2 association or L2 probe at all. Up until now we missed this case in the data health checks and returned that nothing is wrong.\n\n"}
{"comment": {"body": "It\u2019s very good addition, but requires different metric of models KPIs, as it doesn\u2019t mean the data arrived is missing, it means models missing data..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/654/_/diff#comment-321377315"}}
{"comment": {"body": "very nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/654/_/diff#comment-321829776"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} what this PR is waitinig?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/654/_/diff#comment-324149363"}}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27966/2115819)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/654/_/diff#comment-324968633"}}
{"title": "added new wispr agent, 438, to ios 16", "number": 655, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/655", "body": "added 438 new wispr agent to ios 16"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23366](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23366){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/655/_/diff#comment-321699931"}}
{"title": "update eros-cujo cert", "number": 656, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/656", "body": "updates the arn of the cert to fit eros-cujo dns"}
{"title": "MEROSP-1383 Added the rt band and mode", "number": 657, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/657", "body": "Inner PR, added the last_radio_band and last_radio_mode"}
{"comment": {"body": ":thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/657/_/diff#comment-321722719"}}
{"title": "fixed galaxy s20 fe", "number": 658, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/658", "body": ""}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23718](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23718){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/658/_/diff#comment-321721465"}}
{"title": "MEROSP-1383 extract device radio details for disaply", "number": 659, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/659", "body": "Update device schema in dynamo db to support new fields.\nFill device capabilities based on canonical name lookup and new columns in the devices db. Last connection data and VPN is still filled with mock data at this point.\n\nSee {: data-inline-card='' } for more details."}
{"title": "Feature/performance tests", "number": 66, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/66", "body": "Add metrics for session and logging\n"}
{"title": "fix ssl arn", "number": 660, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/660", "body": ""}
{"title": "802.11a support only on 5GHz", "number": 661, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/661", "body": ""}
{"comment": {"body": "Regression pass:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23954](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23954)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/661/_/diff#comment-321733704"}}
{"title": "Get radio caps also on level 5 models, bad comparison", "number": 662, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/662", "body": ""}
{"comment": {"body": "Regression pass:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24006](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24006)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/662/_/diff#comment-321785384"}}
{"title": "Apple Relay Detection", "number": 663, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/663", "body": "Fill the vpn_type field in the device record with the detected VPN details of the last connection. Currently only 2 states are supported: Apple Private Relay and Unknown.\nAdded a degenerated identification feature that is only used for display. It reduces all domain names in DNS queries to ones matching 2 known DNS addresses used by Apple Private Relay. If there is any match Apple Private Relay is returned, else Unknown."}
{"title": "Bugfix/Missing RT mode", "number": 664, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/664", "body": "Instead of trying to calculate the RT mode from the first parsed RT packet, we need to make sure we look at the association packet"}
{"title": "Improvment/DHCPv6 dictionary to dataclass", "number": 665, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/665", "body": "Just small improvement, removed the usage of the dictionary used in dhcpv6_parser and replaced it with a dataclass"}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} what this PR is waitinig?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/665/_/diff#comment-324149506"}}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27993/2117541)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/665/_/diff#comment-324971663"}}
{"title": "Perf without db calls and DHCP fp refactor", "number": 666, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/666", "body": "subtracting db function runtime from runtime tests\nfixing dhcpfp to be bytes\nfixing DHCP lookup table to have unique index\nremoving hack from dhcpfp (filtering value of 114)"}
{"comment": {"body": "The DHCP FP should be a string field and not bytes, to be compatible with the CUJO format as this is the format we shall get the data when we start tapping into the CUJO stream.\n\nMeaning to have the fingerprint `[1, 3, 6, 15, 31, 33, 43, 44, 46, 47, 119, 121, 249, 252`\\] as `\"1,3,6,15,31,33,43,44,46,47,119,121,249,252\u201d`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/666/_/diff#comment-322090152"}}
{"comment": {"body": "so we can convert it to bytes when getting it from cujo", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/666/_/diff#comment-322116253"}}
{"comment": {"body": "We should always work and store data in human readable formats when possible.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/666/_/diff#comment-322119161"}}
{"comment": {"body": "what is the human meaning of 249? I am trying to reduce runtime duration. String manipulations means dynamic allocations which are expensive. The db will still use strings. We can add `__str__` and `__repr__` for human readable prints. currently the data was not in string inside the system but in list of ints.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/666/_/diff#comment-322122219"}}
{"comment": {"body": "rolled back bytesfield and bytes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/666/_/diff#comment-322181342"}}
{"title": "added Pep8 naming - flake8 plugin", "number": 667, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/667", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27141](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27141)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/667/_/diff#comment-324044883"}}
{"title": "Add the MDNS RPVR typing feature", "number": 668, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/668", "body": "This feature is extracted from a field that is present in MDNS packets and can help to detect the OS of a given device. The extracted value is a float that correspond to one or more operating systems that are mapped in a csv file.\n  \n\nAdd the relevent class that resembles the WisprAgent feature in its characteristics\nAdd the rpvr field in the MDNS parser\nAdd the csv file that maps the floats to OS versions and the loading action\nAdd new feature to schema\nAdd new feature to configuration of TypeClassifier, env file and helm deployement files\nAdd the relevant unit tests\nRemove unused version environment variable\nRemove calls to loading of models, that are already called from the TypeClassifier init function\n\n"}
{"title": "added classifier features to eth packets", "number": 669, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/669", "body": ""}
{"title": "Feature/prev_rand_mac", "number": 67, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/67", "body": "Now we look at the hw_mac and using the previous rand MAC address at a matching feature"}
{"comment": {"body": "let\u2019s add in devices table column if the mac is random or not as we had in the pilot 'R' ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/67/_/diff#comment-233866649"}}
{"comment": {"body": "what if the mac address already exists in the list? Then the list would grow forever", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/67/_/diff#comment-234822972"}}
{"title": "added lfs pull to automation in local regression make command", "number": 670, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/670", "body": "I set up this command before we decided to move all the tests data to lfs (pulled from S3 at the time instead)\n\nTo keep the command useful, added git lfs pull to the command."}
{"comment": {"body": "no need to add this to release/cujo, redirect to dev please", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/670/_/diff#comment-322243618"}}
{"comment": {"body": "Ok, handling that", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/670/_/diff#comment-322254941"}}
{"comment": {"body": "don\u2019t just redirect, create a branch from dev and add your small change", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/670/_/diff#comment-322256630"}}
{"title": "added lfs pull to automation in local regression make command", "number": 671, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/671", "body": "Updated the local_regression make command to work with lfs."}
{"title": "new l2 mode 2022-08-09 with new devices from cujo", "number": 672, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/672", "body": ""}
{"comment": {"body": "No need to merge it with the cujo relesae. We can redirect it to dev.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/672/_/diff#comment-322263287"}}
{"comment": {"body": "I will do it when I update dev with the new models", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/672/_/diff#comment-322264072"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24667](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24667){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/672/_/diff#comment-322265148"}}
{"title": "typo", "number": 673, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/673", "body": ""}
{"title": "rt packet types", "number": 674, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/674", "body": ""}
{"title": "refactor to helm utils", "number": 675, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/675", "body": "There's no need to download helm chart locally, return an informative exception to configuration API\n"}
{"title": "MEROSP-1247 pipeline", "number": 676, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/676", "body": "setup processing pipelines\nsetup processing pipelines\nWIP\nsupport suffix filtering\nworking version\ntypi fix'\nalign the item types in the list\nextract firehose list to seperate py file\ncatch parser wxceptions\nsmudge job_id across all entries\nWIP\nd pcap\nremove sticky namespace\nmerge\nkafka auth fix\nflake\ncatch exceptions\npipeline fix\nadding metrics\nmetrics\n\nMerged in feature/data_catalog_classifier_features (pull request #669)\nadded classifier features to eth packets\n\nadded classifier features to eth packets\n\nApproved-by: Ariel Tohar Approved-by: Ophir Carmi\n\n\nMerged in feature/data_catalog_l2_packets (pull request #674)\nrt packet types\n\nrt packet types\n\nApproved-by: Ariel Tohar Approved-by: Ophir Carmi\n\n\nlocal run\n\nWIP\nlog fix\n\n"}
{"title": "MEROSP-1247- data catalog", "number": 677, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/677", "body": "The main purpose of this PR is to get the packet parser changes into dev.\ndata catalog design: \nthis is the first phase where we handle pcap files only.\n"}
{"title": "Bugfix/MEROSP-1287 parralell post tenant failed", "number": 678, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/678", "body": "According to levl-pypi the latest version is 0.18.0 :man_shrugging:"}
{"title": "Additional radio capabilities", "number": 679, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/679", "body": "Add new device radio capabilities extracted from the devices db to be saved to device for display."}
{"title": "Feature/fix matching", "number": 68, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/68", "body": "DecisionLog changes in PR 64  changed the wrong flow. This PR fixes that.\nThe rest of the changes are permission changes (from 755 to 644) because git insisted\n\n"}
{"title": "external control of raw/features extraction", "number": 680, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/680", "body": "In order to control and priorities the amount of data digested\nwe decided to separate the  and the control it externally"}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/680/_/diff#comment-322503764"}}
{"title": "Bugfix/MEROSP-1287 parralell post tenant failed", "number": 681, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/681", "body": "\n\n[skip ci] bumped helm_deployment_api package version, build 6917, new version 0.19.0\nupdate helm deployment version\n\n"}
{"title": "Feature/MEROSP-1383 additional radio properties", "number": 682, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/682", "body": "Add new device radio capabilities extracted from the devices db to be saved to device for display\nfix requests\nadd updated csv\nfix nan\nfix mu-mimo supproted flag\n\n"}
{"title": "fix mu-mimo", "number": 683, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/683", "body": ""}
{"comment": {"body": "assign `df[\"mu_beamformee_capable\"].values[0]` to a variable, you can do it:\n\n\u200c\n\n```\nif \"max_mimo_configuration\" in df:\n    val = df[\"mu_beamformee_capable\"].values[0]\n    if not math.isnan(val):\n        device_radio_caps.mu_beamformee_capable = \"Yes\" if val == 1 else \"No\"\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/683/_/diff#comment-322623475"}}
{"comment": {"body": "Totally agree and all this code shouldn\u2019t be even merged to dev.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/683/_/diff#comment-322623744"}}
{"title": "remove word exception from log print", "number": 684, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/684", "body": ""}
{"title": "reducing latency thresholds", "number": 685, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/685", "body": "Till this PR we only enforce throughput (i.e. number of sessions per seconds) thresholds. \nThis PR enforce also latency (i.e. duration of session) thresholds. \nThe thresholds are not final. \nWe need to see that the test passes for some number of pipelines. \nWe also added prints of the actual duration in so we could refine the thresholds in the coming future."}
{"comment": {"body": "@{6265307b185ac200692f9bd9} why we have different thresholds for the scenarios? 300msec, 400msec, 600msec", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/685/_/diff#comment-323292329"}}
{"comment": {"body": "because empty vault is faster than full vault", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/685/_/diff#comment-323365639"}}
{"comment": {"body": "sure, do we have test with most classification use case? can we reduce the thresholds?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/685/_/diff#comment-323454473"}}
{"comment": {"body": "didn\u2019t understand", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/685/_/diff#comment-323466649"}}
{"comment": {"body": "Could you please add a short description, especially concerning the \u2018whys\u2019 and not the \u2018whats\u2019?  \nI realize this is not a lot of changes but it is still hard for me to deduce the reasoning behind those.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/685/_/diff#comment-323505350"}}
{"comment": {"body": "what does the `remove_db_functions` function do?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/685/_/diff#comment-323505578"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/685/_/diff#comment-323688235"}}
{"title": "MEROSP-1247 pipeline with csv", "number": 686, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/686", "body": "\n\nbuffered list implementation\n\n"}
{"title": "removing obsolete code", "number": 687, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/687", "body": ""}
{"title": "MEROSP-1247 externa control raw features", "number": 688, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/688", "body": "external control of raw/features extraction\nremove empty file\nextract num partition to env var\ndefault suffix to None\nlog suffix\n\n"}
{"title": "Devices db optim", "number": 689, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/689", "body": "devices db is now indexed. the index is a dict {column: {value: [row_index, row_index, ], value: [row_index, row_index, ]\n{\n  column_name1: {\n      value1: [row_index, row_index, ], \n      value2: [row_index, row_index, ],\n      ...\n  },\n  column_name2: {\n      value3: [row_index, row_index, ], \n      value4: [row_index, row_index, ],\n      ...\n  }\n  ...\n}\nquery is a combination of getting row_indices:\n(d[col1][val1]  d[col2][val2]) || d[col3][val3]\nwhere && is the intersection between the lists of row_indices, and || is their union\n\nin addition, now the release_date column is translated to epoch time (seconds since 1970) on init.\n\n"}
{"comment": {"body": "@{6265307b185ac200692f9bd9} can you add more info on the PR? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/689/_/diff#comment-323291760"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/689/_/diff#comment-323369818"}}
{"comment": {"body": "From looking again at the PR, I think it has the potential to hurt the runtime. Specifically in cases where you would have huge lists in the index and the operations on those list would be very heavy.\n\nIt might be that we are seeing the runtime improvement only because of a biased measuring set \\(only iPhones I assume\\) - if you would run this code on Android, I would think the rumtime would not be good.\n\nWhat I think we should do, if are trying to tackle here the high-res DB, is to index only the relevant fields for lookup and index them to their values \\(those always return a small results list\\) and any further queries should be done on the values themselves. Let\u2019s discuss this when you are availble.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/689/_/diff#comment-324288812"}}
{"title": "Feature/fix matching", "number": 69, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/69", "body": "session.decision is a list of list of matches features, not a list of matched features.\n  Now  would be fixed.\n\n"}
{"title": "remove tensroflow - align with dev", "number": 690, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/690", "body": ""}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/690/_/diff#comment-323427707"}}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26912](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26912){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/690/_/diff#comment-323816202"}}
{"title": "pytest: revive code coverage", "number": 691, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/691", "body": ""}
{"title": "MEROSP-778 remove connectors", "number": 692, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/692", "body": "There's no need to use our monitoring connectors anymore as we moving to Kinesis and Firehose instead"}
{"comment": {"body": "there is nothing better than PR full of code delete", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/692/_/diff#comment-323687228"}}
{"title": "remove ignore of flake8 of two files", "number": 693, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/693", "body": "change line length to be the right one"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26559](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26559){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/693/_/diff#comment-323708737"}}
{"title": "Events to data catalog", "number": 694, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/694", "body": "This PR adds the code which will produce the datacatalog rows from classifier event"}
{"title": "optimizing release date query", "number": 695, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/695", "body": "Now release date is converted on init to epoch time (seconds since 1970) and compared as int on query."}
{"comment": {"body": "@{6265307b185ac200692f9bd9} Find not convert in device db when it\u2019s created? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/695/_/diff#comment-323748321"}}
{"comment": {"body": "we want `release_date` to be human readable in the csv file", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/695/_/diff#comment-323767185"}}
{"comment": {"body": "local regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26854](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26854)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/695/_/diff#comment-323781934"}}
{"title": "[Snyk] Security upgrade python from 3.10-slim to 3.11-rc-slim", "number": 696, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/696", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.11-rc-slim, as this image has only 49 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity | Priority Score / 1000 | Issue | Exploit Maturity |\n| --- | --- | --- | --- |\n|  | 471 | Double Free SNYK-DEBIAN11-GNUTLS28-2964220 | No Known Exploit |\n|  | 400 | Allocation of Resources Without Limits or Throttling SNYK-DEBIAN11-LIBTIRPC-2959390 | No Known Exploit |\n|  | 400 | Allocation of Resources Without Limits or Throttling SNYK-DEBIAN11-LIBTIRPC-2959390 | No Known Exploit |\n|  | 150 | Information Exposure SNYK-DEBIAN11-UTILLINUX-2401081 | No Known Exploit |\n|  | 571 | Out-of-bounds Write SNYK-DEBIAN11-ZLIB-2976151 | No Known Exploit |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "added test for get typing scoring", "number": 697, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/697", "body": ""}
{"comment": {"body": "Is this a test template? what is the purpose of this?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/697/_/diff#comment-324063657"}}
{"comment": {"body": "no. It\u2019s assuring the function raise exception.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/697/_/diff#comment-324065360"}}
{"title": "infer datetime format devices db", "number": 698, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/698", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27143](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27143)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/698/_/diff#comment-324065845"}}
{"title": "remove flake8-spellcheck", "number": 699, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/699", "body": ""}
{"title": "Feature/snapshot session integration", "number": 7, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/7", "body": "initial commit\nremoved local folder\nstructure fix\nfix open path\ndont use zlib for now, folder structure update\nfixing pipeline\ns3 pipline fix\nminor\nremove unused\nremove unused\nremove unused\nintegrate kafka with archiver layer\n\n"}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} no code in \\_\\_init files.. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/7/_/diff#comment-225210442"}}
{"comment": {"body": "We should find better way to distribute our package to workers.. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/7/_/diff#comment-225211108"}}
{"title": "Feature/session id link to devices", "number": 70, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/70", "body": "Link session id and devices\n"}
{"title": "Packet parser reduce exception logging", "number": 700, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/700", "body": ""}
{"comment": {"body": "when you see `None, SystemErrors...` it means that outside it will print warning to log again. that why I removed this one. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/700/_/diff#comment-324080887"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} does this error helps us? or we should suppress/fix them?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/700/_/diff#comment-324084831"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} why not using bug branch convention?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/700/_/diff#comment-324149153"}}
{"comment": {"body": "started to work on it before I knew there is a Jira ticket.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/700/_/diff#comment-324166059"}}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27370](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27370)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/700/_/diff#comment-324166453"}}
{"title": "hotfix aws client call fix", "number": 701, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/701", "body": "When calling aws api, the function is getting args and kwargs and looking for specific params.\nUsually they are using CamelCase convention , probably code they converted from java.\nanyways, it got into dev by mistake need to fix ASAP.\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\nMissing required parameter in input: \"DeliveryStreamName\"\nMissing required parameter in input: \"Records\"\nUnknown parameter in input: \"records\", must be one of: DeliveryStreamName, Records\n"}
{"comment": {"body": "@{6085103f5797db006947d59a} Good catch, but do we need the sparrow FH? Won\u2019t it be replaced with new FH for all the data points? raw, results, decision, error, features?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/701/_/diff#comment-324148645"}}
{"comment": {"body": "Not sure, but for now lets fix dev\n\nand later we are going to review sparrow again anyways", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/701/_/diff#comment-324149563"}}
{"title": "Packet parser changes for datacatalog", "number": 702, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/702", "body": "I will split the MEROSP-127 PR\nthis one will push the packet parser changes to support data catalog use case.\nAdded a class that represents the parser return.\nchanged parsed_packet_to_parsed_fields that will get parsed data and not connectionResult.\n\n"}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/702/_/diff#comment-324338581"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27724](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27724)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/702/_/diff#comment-324380934"}}
{"title": "remove obsolete code and added unit tests", "number": 703, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/703", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27656](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27656){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/703/_/diff#comment-324369675"}}
{"title": "added galaxy tests to runtime tests", "number": 704, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/704", "body": "till now we had only iPhone 13 runtime tests."}
{"comment": {"body": "Is there a significant difference in runtime?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/704/_/diff#comment-324393676"}}
{"comment": {"body": "don\u2019t know yet. currently it passes with the same thresholds, we\u2019ll see.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/704/_/diff#comment-324448628"}}
{"title": "No coverage runtime tests", "number": 705, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/705", "body": "disable pytest coverage for runtime test to avoid its runtime"}
{"title": "use different pytest ini for runtime tests", "number": 706, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/706", "body": "disable pytest coverage for runtime tests"}
{"title": "Flake8 import order", "number": 707, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/707", "body": "forces import order"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29011](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29011)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/707/_/diff#comment-325740499"}}
{"title": "The data catalog part of MEROSP-1247", "number": 708, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/708", "body": "after merging the packet parser, this PR contains data catalog code and some minor other changes."}
{"title": "new l2 model after fixing canonical, M1 devices and power caps", "number": 709, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/709", "body": "\n\nadded MacBook with M1 data\nfixed some canonical naming\nremoved google data\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28534](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28534){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/709/_/diff#comment-325070234"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2}   \nI think we need to do something so we can see the diff between the files", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/709/_/diff#comment-325075771"}}
{"comment": {"body": "There was a big change with the data source, so there are many changes.  \nThey are trackable.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/709/_/diff#comment-325171600"}}
{"title": "added session id to db", "number": 71, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/71", "body": ""}
{"title": "removed sleep", "number": 710, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/710", "body": ""}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28300](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28300)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/710/_/diff#comment-325083943"}}
{"title": "remove sleep", "number": 711, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/711", "body": ""}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28246](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28246){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/711/_/diff#comment-325076765"}}
{"title": "remove kafkaheaders defaults", "number": 712, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/712", "body": ""}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28420](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28420)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/712/_/diff#comment-325129023"}}
{"title": "Fix WS Discovery single device false positive", "number": 713, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/713", "body": "Error was due to the naive parsing of all WS DIscovery packets aa if they were equal, when in fact, the same EndPointReference can appear in a 2 different types of packets and have a different meaning. The fix was to filter all the types of WS Discovery packets except for the Hello packets. A unit test was added. An existing unit test was fixed\n{: data-inline-card='' } \nThe update whitelist was changed to write new words in lower case.\nNew words were added to the whitelist\n\n"}
{"comment": {"body": "Passed local regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/713/_/diff#comment-325234094"}}
{"title": "Change the format to save sessions", "number": 714, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/714", "body": "The current format to save sessions is to group them in directories by event types and inside each directory sort by timestamp.\nThis PR suggests a different format, that might be a bit more intuitive to a developers eyes.\nIn the new format the sessions files are saved with the timestamp as the prefix of the directory name and the type of event as the suffix. In this fashion, the order of events and thus the scenario is clearer.  \n"}
{"comment": {"body": "@{6252eba45d1e700069ad0104} Good suggestion! Have you validated the proposal with other teams, automation? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/714/_/diff#comment-325427717"}}
{"comment": {"body": "@{5f82bf320756940075db755e}   \nIt was coordinated with me, and this is exaclty what we agreed on. No objections on my end for this one. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/714/_/diff#comment-325459830"}}
{"title": "Refactor the manual test tool", "number": 715, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/715", "body": "In order to make the manual test tool useful, the coupling to pytest was removed, and the tool activation is now throught the 'test_utils.py' script, with arguments as before but with the additional short option --dir or -d, --name or -n, --res or -r.\nThe arguments are passed when calling the script in the following fashion: python test_utils.py -d  -r "}
{"comment": {"body": "Passed local regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/715/_/diff#comment-325482537"}}
{"title": "MEROSP-1450 - new canonical names for MacBooks", "number": 716, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/716", "body": "new canonical names for MacBooks\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28959](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28959){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/716/_/diff#comment-325551087"}}
{"title": "MEROSP-1510 - adding new features to data catalog", "number": 717, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/717", "body": ""}
{"title": "Feature/MEROSP-835 Removed android repo", "number": 718, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/718", "body": "This PR removes the android hostnames repo. It was a component that was used by the nonrandom_device_name feature, and is no longer relevant after the refactor and improvements we did in this feature. Its no longer used so we can delete it :slight_smile:"}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29235/2200281)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/718/_/diff#comment-325989786"}}
{"title": "Data catalog config", "number": 719, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/719", "body": ""}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29363](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29363){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/719/_/diff#comment-326050566"}}
{"title": "Now writing the decision time to the DB", "number": 72, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/72", "body": "After manually updating the database with the new column, we can enable the code that saves the decision duration time"}
{"title": "force no broad exceptions", "number": 720, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/720", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29558](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29558){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/720/_/diff#comment-326516583"}}
{"title": "DeviceOS comparison fix and tests", "number": 721, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/721", "body": "This PR solves a bug that was reported in MEROSP-1525. We got two user agents that suggested two different OS versions. When we compared between the two, we saw that the lower one was chosen instead of the higher one. It turns out that the comparison mechanism was wrong.\nNote that a matching branch exists in eros-automation, to fix the OS versions of the relevant tests in the regression as well."}
{"title": "added unit tests for strong match icmp6 and is feature exists", "number": 722, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/722", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29584](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29584){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/722/_/diff#comment-326517269"}}
{"title": "Unit tests 2", "number": 723, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/723", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29669](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29669)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/723/_/diff#comment-326523861"}}
{"title": "flake8-pytest-style", "number": 724, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/724", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29715](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29715)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/724/_/diff#comment-326528300"}}
{"title": "more unit tests and converting datahealth to int enum", "number": 725, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/725", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29908](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29908){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/725/_/diff#comment-326705231"}}
{"title": "added pixel 2 L2 data for glinet", "number": 726, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/726", "body": "added pixel 2 L2 data for glinet\n{: data-inline-card='' }"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29891](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29891){: data-inline-card='' }  \nfails on 4 tests.  \nIt\u2019s improve these tests", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/726/_/diff#comment-326658250"}}
{"comment": {"body": "Please wait for my go @{621df03094f7e20069fd6ab2} , thx", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/726/_/diff#comment-326705077"}}
{"title": "minor", "number": 727, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/727", "body": ""}
{"title": "more unit tests", "number": 728, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/728", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29988](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29988){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/728/_/diff#comment-326730335"}}
{"title": "added agent serial and pid to packed messages", "number": 729, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/729", "body": "{: data-inline-card='' }"}
{"comment": {"body": "why do you need this?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/729/_/diff#comment-327306585"}}
{"comment": {"body": "  \nbecause mocks should be like the real api  \n\n![](https://bitbucket.org/repo/o5KReBa/images/3182919652-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/729/_/diff#comment-327314364"}}
{"title": "new proto", "number": 73, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/73", "body": ""}
{"title": "added pixel 2 L2 data for glinet", "number": 730, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/730", "body": "Just renamed @{621df03094f7e20069fd6ab2} 's branch for enabling custom regression run"}
{"comment": {"body": "MEROSP-1516", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/730/_/diff#comment-*********"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30099](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30099){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/730/_/diff#comment-*********"}}
{"title": "adding  flake8 plugin for pandas syntax - pandas-vet", "number": 731, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/731", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30039](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30039){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/731/_/diff#comment-*********"}}
{"title": "Unit test 5", "number": 732, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/732", "body": "more unit-tests\nconverting device status to int enum\n\n"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30103](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30103){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/732/_/diff#comment-326758616"}}
{"title": "more unit tests", "number": 733, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/733", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30253](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30253){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/733/_/diff#comment-326974960"}}
{"title": "fix spelling", "number": 734, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/734", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30372](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30372)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/734/_/diff#comment-326992733"}}
{"title": "Unit test 7", "number": 735, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/735", "body": "minor refactor\nmore unit tests\n\n"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30575](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30575){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/735/_/diff#comment-327046261"}}
{"title": "Bugfix/MEROSP-1472 device first mac address", "number": 736, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/736", "body": "Up until now, we used the prev_rand_mac_addresses feature as our first_mac_address indication for each device. \nThe problem we had with this mechanism is that sometimes the first mac address a device is connected with is a HW mac address. Even if we do rely on both HW mac value and the prev_rand_mac_addresses list value, we cant determine which one of them was the first one if we dont expand the current way of saving the data - for example, we can save each element in the prev_rand_mac_addresses list with the timestamp of the connection, but thats just over-design and more complex. The simplest way to solve this problem is just to save the mac address the device is connected with in its first connection and just keep it. That value will be set only once, and will allow us to get the actual value when we need to.\n"}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30434)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/736/_/diff#comment-327005875"}}
{"title": "Bugfix/MEROSP-1565 ipad typing", "number": 737, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/737", "body": "After calculating the typing features, were building a query based on their results to get the right model from the devices db csv. The problem that was discovered in this issue showed us that we might build a query with invalid values. In this case, it turned out that the http user agent returned a iOS-Device model string, which was searched later in the csv under the identifier column. Since its an invalid value, we got no result and therefore we didnt get the best typing result we could get:\n'((identifier == \\'iOS-Device\\'))  (vendor == \\'Apple\\'  (internal_name == \\'J171AP\\' | identifier == \\'J171AP\\'))'\nIn this PR, we make sure to check that the identifier were getting from the user agent is indeed valid. We can do it by loading at the startup the list of valid identifiers and search it when we build the query we use later on."}
{"comment": {"body": "Great work!\n\nCan you also added an analogous handling of mDNS info model IDs, so only handle model IDs that valid?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/737/_/diff#comment-327244974"}}
{"comment": {"body": "Sure", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/737/_/diff#comment-327245160"}}
{"comment": {"body": "Note that there is a matching branch in eros-automation since this change improved the resolution in 4 tests :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/737/_/diff#comment-327280496"}}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30889) \\(With the matching eros-automation branch that improves resolution\\) \n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/737/_/diff#comment-327286707"}}
{"title": "more unit tests and removing redundant code", "number": 738, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/738", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30870](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30870){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/738/_/diff#comment-327301880"}}
{"title": "fix function call", "number": 739, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/739", "body": "missed this one in the last aws api calls fix"}
{"title": "Traceback fix - None prev mac addressees", "number": 74, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/74", "body": ""}
{"title": "Tcp timestamp parser", "number": 740, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740", "body": "added tcp_parser that parse tcp timestamp out of all tcp packets.\nthere was already http_parser. HTTP is a subset of TCP protocol. so I needed to support multiple parsers for the same packet.\nThis is preparation for an algorithm that will identify devices by their tcp timestamp slope.\n\n"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31117](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31117){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-327334641"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4}   \nhow do we want to handle multiple tcp protocols in the data catalog?  \nbecause the code is taking the first one now  \nIm not sure this is what we want", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-327341054"}}
{"comment": {"body": "For the TCP timestamp we want the tcp timestamp of the SYN packets \\(first packet in each connection\\).  \nThere shouldn\u2019t be that many of those.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-327343031"}}
{"comment": {"body": "Why only SYN packets?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-327548600"}}
{"comment": {"body": "That is what we are using currently for our identity feature.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-327600900"}}
{"comment": {"body": "research said it is ok to have multiple data for the same packet. I added unit test for data catalog.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-327623238"}}
{"comment": {"body": "regression passed again:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31665](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31665)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-327660714"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} where are parsing.py adding tcp\\_fields?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-328268511"}}
{"comment": {"body": "implemented filter all packets but SYN packets", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-328271494"}}
{"comment": {"body": "in phase 2. this is phase 1", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-328478555"}}
{"comment": {"body": "regression passed again:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32804](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32804){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/740/_/diff#comment-328487443"}}
{"title": "Unit test 9", "number": 741, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/741", "body": "removing obsolete code and adding unit tests"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31133](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31133){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/741/_/diff#comment-327339375"}}
{"title": "removing obsolete code", "number": 742, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/742", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31303](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31303){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/742/_/diff#comment-327547361"}}
{"title": "Unit test 11", "number": 743, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/743", "body": "more removing obsolete code\n\n"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31402](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31402){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/743/_/diff#comment-327583302"}}
{"title": "To kinesis logs", "number": 744, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/744", "body": "after integration tests\nfound some problems \nfixed them, added some tests.\nbufferedList is a list which get full to max_buffer_list and than uses its writer to clean the buffer."}
{"title": "Feature/Remove overlapping pkts between events", "number": 745, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/745", "body": "This PR adds a mechanism that removes overlapping packets between events so we wont process them more than one time.\nThe overall flow is as follows: For each connection event, were saving the last timestamp that was received in the raw_data within it. Later on (in the ongoing events from that device), well parse only packets that were received after the last timestamp we saw in the connection event.\nThe requirement for that feature was established after the latest call with Cujo and the way well handle ongoing events in our future integrations. We might get overlapping data in the future and we can add all the mechanism that we need from now."}
{"comment": {"body": "add unit tests for this function", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/745/_/diff#comment-327678389"}}
{"comment": {"body": "Let\u2019s discuss that before you merge it in - I don\u2019t think we have considered all the implications around adding such feature.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/745/_/diff#comment-328222270"}}
{"comment": {"body": "No problem, leaving this open until we discuss it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/745/_/diff#comment-328233346"}}
{"title": "MEROSP-1444 ut pipelines", "number": 746, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/746", "body": "ut pipeline\nrename test\nlint\nfix lint\nglobal variable\nflatten local file structure\n\n"}
{"title": "Unit test 12", "number": 747, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/747", "body": "more unit tests\n\n"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31911](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31911){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/747/_/diff#comment-328081808"}}
{"title": "Feature/MEROSP-1150 DB reduction + Feature aggregation", "number": 748, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/748", "body": "This PR comes as an additional improvement to our typing procedure following the latest attempts to bring it to the next level.\nSome DB changes:\n\nEach typing feature will have its own column in the DB (Just like the identification features). The typing_features dictionary shall not be used anymore.\nEach typing feature will be saved in the DB only with its feature result. We do not need to save any additional data. For example: We will save the wispr agent result, without saving the actual wispr agent strings that we got from the AP.\n\nSome other changes:\n\nFilling up the fields in the decision logs that were empty until now\nAdded dataclass usages where possible\n\nFurther documentation can be found here. Please read the it :) \n"}
{"comment": {"body": "remove. python3 negates the eq function by default.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/748/_/diff#comment-328080523"}}
{"comment": {"body": "nice work done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/748/_/diff#comment-328080543"}}
{"title": "Unit test 13", "number": 749, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/749", "body": "removing obsolete code\nmore unit tests and removing obsolete code\n\n"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31944](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31944)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/749/_/diff#comment-328085077"}}
{"title": "Bugfix/EROS-69 wrong os filter", "number": 75, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/75", "body": "Now filtreing by the device os only if exists\n"}
{"title": "fixing crash when not found in dhcp lookup", "number": 750, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/750", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32116](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32116){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/750/_/diff#comment-328094787"}}
{"title": "start applying black style linter/fixer - part 1", "number": 751, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/751", "body": ""}
{"comment": {"body": "regression passed:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33081](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33081){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/751/_/diff#comment-328521391"}}
{"title": "Unit test 14", "number": 752, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/752", "body": "more unit tests\nremoving the word KEY from protocol keys\n\n"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32507](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32507)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/752/_/diff#comment-328253025"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} why no description for the changes? What Jira issue, task this implements?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/752/_/diff#comment-328268982"}}
{"title": "MEROSP-1630 OnePlus 10 devices for new lab device 147", "number": 753, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753", "body": "Added OnePlus 10 devices to devices_db for the new lab device 147 - OnePlus 10 Pro 5G\nNew PR after updating Android devices\n\nMEROSP-1630"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32333](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32333){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-328107223"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} This is not really a fix. \n\nThe devices db became stale and doesn\u2019t have the newer Android devices, so let\u2019s bring  all the new devices and not just what is required to pass the test on our devices.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-328107502"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} please review MIch\u2019s comment", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-328107773"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} we will do it of course :\\)\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-328436978"}}
{"comment": {"body": "After adding all new android devices  \n[https://bitbucket.org/levl/eros-classifier/pipelines/results/7720](https://bitbucket.org/levl/eros-classifier/pipelines/results/7720){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-328846679"}}
{"comment": {"body": "\u201c`Galaxy Z Fold4\u201d` seems to have an OS name of \u201cAndroid L\u201d. Can you check that? \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-328892820"}}
{"comment": {"body": "Yes, this is correct.\n\n> _Android 12L is a tablet-first OS with better multitasking support, redesigned widgets, and other optimizations that capitalize on extra screen real estate. In short, Android 12L is designed for tablets and foldables like the_ [_Samsung Galaxy Z Fold3_](https://www.androidpolice.com/2021/09/04/samsung-galaxy-z-fold3-review/)_, while Android 12 is optimized for smartphones._\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-329040364"}}
{"comment": {"body": "This is going to break the detection of this device. Please fix it to Android until we understand it better. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-329072773"}}
{"comment": {"body": "No problem", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-329074397"}}
{"comment": {"body": "after fixing regression  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33959](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33959){: data-inline-card='' }   \n  \nneed to fix bug [https://levltech.atlassian.net/browse/MEROSP-1613](https://levltech.atlassian.net/browse/MEROSP-1613){: data-inline-card='' }  before approving and merging.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-329113661"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} we are ready, notify when u\u2019r ready", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-329127416"}}
{"comment": {"body": "@{5f82bf320756940075db755e} What changes do you want?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-329700759"}}
{"comment": {"body": "[https://levltech.atlassian.net/browse/MEROSP-1630](https://levltech.atlassian.net/browse/MEROSP-1630){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-329729607"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35714](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35714){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/753/_/diff#comment-330211348"}}
{"title": "MEROSP-1556 new devices and data from cujo", "number": 754, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/754", "body": "MEROSP-1556"}
{"comment": {"body": "It looks like iPad 7th / 8th / 9th gen has the same L2 signature \\(at least for the 2.4Ghz band\\). Should we combined them under the same canonical name?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/754/_/diff#comment-328110739"}}
{"comment": {"body": "Also, do we have any new L2 collisions?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/754/_/diff#comment-328222821"}}
{"comment": {"body": "Yes, we now have a collision between apple watch series 3 and apple watch series 7", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/754/_/diff#comment-329059909"}}
{"comment": {"body": "Is it the problem with the contradicting labels we have received from them?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/754/_/diff#comment-329071712"}}
{"comment": {"body": "@{5f82bf320756940075db755e}  What changes do you want?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/754/_/diff#comment-329700771"}}
{"comment": {"body": "[https://levltech.atlassian.net/browse/MEROSP-1556](https://levltech.atlassian.net/browse/MEROSP-1556){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/754/_/diff#comment-329729188"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35615](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35615){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/754/_/diff#comment-330177823"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36503](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36503){: data-inline-card='' } all passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/754/_/diff#comment-331370599"}}
{"title": "MEROSP-1446 move to ecs", "number": 755, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/755", "body": "As part of the transition to ECS, I wrote a pipeline to deploy all Terragrunt files (eventually terraform files) to AWS to make it intuitive and straightforward.\nanother change is to add a consumer group id variable so well be able to consume parallelly from EKS and ECS instances without colliding.\n"}
{"title": "add terragrunt pipeline + consumer group id env var", "number": 756, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/756", "body": "As part of the transition to ECS, I wrote a pipeline to deploy all Terragrunt files (eventually terraform files) to AWS to make it intuitive and straightforward.\nanother change is to add a consumer group id variable so well be able to consume parallelly from EKS and ECS instances without colliding."}
{"comment": {"body": "please run regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/756/_/diff#comment-331373107"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36535](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36535){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/756/_/diff#comment-331374252"}}
{"title": "Protocol keys small refactor", "number": 757, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/757", "body": "splitting protocol keys to protocol keys and protocol attributes\n"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32715](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32715){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/757/_/diff#comment-328283988"}}
{"title": "fixing data catalog to take only interesting packets", "number": 758, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/758", "body": "for example: DHCP packets that we dont want, returns exit_code None and the data catalog was saving them"}
{"title": "MEROSP-1150/Features aggregation", "number": 759, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/759", "body": "As for the features aggregation component, this PR adds our ability to keep track of our L2 data - whether the union or intersection. Up until now, we sometimes overrode the data in those dictionaries if we got new information of the same key (aka same platform and connection type). This fix comes to keep the data if we got the same key again, and perform union or intersection accordingly. \nFor all the other features, the features aggregation is already implemented by default, since the merge_feature_data method always chooses the new data over the old data, which ensures us that we keep the latest results."}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32751)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/759/_/diff#comment-328315544"}}
{"comment": {"body": "maybe use a function as the code appears again in L2 intersection", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/759/_/diff#comment-332985032"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} please link to Jira story of aggregation [https://levltech.atlassian.net/browse/MEROSP-1150](https://levltech.atlassian.net/browse/MEROSP-1150){: data-inline-card='' }  as this commits are not linked", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/759/_/diff#comment-332989582"}}
{"title": "Prev rand Mac - Not adding the address to the previous addresses list if it already exists", "number": 76, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/76", "body": ""}
{"title": "update iOS models - rpvr and wispr agent from cujo data", "number": 760, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/760", "body": "update iOS models - rpvr and wispr agent from cujo data\nsorted rpvr csv"}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33072](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33072){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/760/_/diff#comment-328517721"}}
{"title": "Revive apple mdns rpvr", "number": 761, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/761", "body": "\nfixing apple mdns rpvr\nwispr and rpvr to high resolution\nremove obsolete code\nwispr and rpvr only for apple"}
{"comment": {"body": "Does it mean that the rpvr feature was not really working?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/761/_/diff#comment-328532982"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} @{621df03094f7e20069fd6ab2} @{6265307b185ac200692f9bd9} Can you please validate that the filtering devices by OS version is not impacted by those changes?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/761/_/diff#comment-328533188"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/761/_/diff#comment-328538402"}}
{"comment": {"body": "I will engage a new PR with removing low resolution and moving filtering to the lookup. And then I will decline this PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/761/_/diff#comment-328539104"}}
{"comment": {"body": "I see.\n\nThat was causes a lot of the regression failures.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/761/_/diff#comment-328540943"}}
{"title": "more black style guide", "number": 762, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/762", "body": ""}
{"title": "apple mdns rpvr was not working till now", "number": 763, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/763", "body": ""}
{"comment": {"body": "regression passing with the modifications in autoamtion:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33338](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33338){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/763/_/diff#comment-328595874"}}
{"comment": {"body": "passed regression:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33338](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33338){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/763/_/diff#comment-328595920"}}
{"title": "kinesis exclusion list", "number": 764, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764", "body": "in order to support both regression tests on every namespace and producing msgs to kinesis we are adding a black list which will contain automation pid."}
{"comment": {"body": "revert please", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328783521"}}
{"comment": {"body": "```\nI101 Imported names are in the wrong order. Should be BufferedList, KinesisWriter\n```\n\nwhy do we need it?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328802998"}}
{"comment": {"body": "@{6085103f5797db006947d59a} so when you are changing the imports we will know what changed. if it is sorted, we will know in the PR what changed. if not sorted, we won\u2019t", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328804484"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} \n\n![](https://bitbucket.org/repo/o5KReBa/images/3570471340-image.png)\nchanged the order.. looks pretty visible in the pr", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328805827"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33558](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33558){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328806597"}}
{"comment": {"body": "@{6085103f5797db006947d59a} if you will insert one more it will be different.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328806794"}}
{"comment": {"body": "if you want\n\nmaybe we can find a script that does all those changes automatically because it is a waste of time to change it manually\n\nthe imports are being added automatiicly", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328806960"}}
{"comment": {"body": "@{6085103f5797db006947d59a} we will imply black in the next few days. it will make some of the changes automatically. but you will need to run it manually.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328807469"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} ok\n\nso I suggest that when we will have the automatic linter we will remove all ignores.\n\nuntil than\n\nit is a waste of time\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328818217"}}
{"comment": {"body": "@{6085103f5797db006947d59a} I disagree.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/764/_/diff#comment-328822252"}}
{"title": "more black style guide", "number": 765, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/765", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33481](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33481)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/765/_/diff#comment-328776197"}}
{"title": "runtime tests: fixing agent serial to be random", "number": 766, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/766", "body": ""}
{"title": "more black style - part 3", "number": 767, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/767", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33768](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33768){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/767/_/diff#comment-329087416"}}
{"title": "fix pipeline steps", "number": 768, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/768", "body": "we had a miss usage of the get_latest_pip_version script"}
{"title": "Black 4", "number": 769, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/769", "body": "more black style guide"}
{"comment": {"body": "@{6265307b185ac200692f9bd9} same as [https://bitbucket.org/levl/eros-classifier/pull-requests/774](https://bitbucket.org/levl/eros-classifier/pull-requests/774){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/769/_/diff#comment-329670487"}}
{"title": "set _result_topic to SESSION_BATCH_SNAPSHOT_RESULT", "number": 77, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/77", "body": ""}
{"title": "more removing obsolete code", "number": 770, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/770", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34123](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34123){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/770/_/diff#comment-329156042"}}
{"comment": {"body": "what is the purpose of the pr?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/770/_/diff#comment-329160595"}}
{"comment": {"body": "cleaning the code, breaking big functions to smaller ones.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/770/_/diff#comment-329171845"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} please make sure to work with defined Jira story and subtasks which should be traced to commits, release later on..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/770/_/diff#comment-329208914"}}
{"title": "Add rpvr field to device class", "number": 771, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/771", "body": ""}
{"title": "some debug logs", "number": 772, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/772", "body": "added some debug logs\n{: data-inline-card='' }"}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/772/_/diff#comment-329157826"}}
{"comment": {"body": "@{6085103f5797db006947d59a} please open task and add it\u2019s issue in the PR branch or commit comment.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/772/_/diff#comment-329670721"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/772/_/diff#comment-329705144"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34330](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34330){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/772/_/diff#comment-329736707"}}
{"title": "Remove low resolution devices db", "number": 773, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/773", "body": "type classifier: devices db - removing low resolution and combining filtering features into regular typing features\nregression passed:\n\n\nwithout indexing: galaxy runtime performance unit tests improves a lot."}
{"comment": {"body": "@{6265307b185ac200692f9bd9}\n\n1. Let\u2019s align tomorrow with @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} @{5d74d49897d8980d8eacd7f8} @{5a4500fe0cacf235de82a9d4} @{5dbeb866c424110de52552cc} @{6085103f5797db006947d59a} the proposed algorithm changes as it\u2019s not that straightforward as it might look.\n2. [https://levltech.atlassian.net/browse/MEROSP-1166](https://levltech.atlassian.net/browse/MEROSP-1166){: data-inline-card='' }  Add the following Jira issue subtask as comment in the commit or the branch name. \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/773/_/diff#comment-329670645"}}
{"comment": {"body": "So, just to make sure I understand, by removing the low-resolution DB \\(that one of its initial purposes was to optimize the runtime\\) we get a better performance?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/773/_/diff#comment-329703595"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/773/_/diff#comment-329711574"}}
{"title": "more code clean", "number": 774, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/774", "body": ""}
{"comment": {"body": "@{6265307b185ac200692f9bd9} I have created the following story for such code cleanups, please work with subtasks and align with the commits with clear description of what fixed and why those improvements are important.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/774/_/diff#comment-329670466"}}
{"title": "MEROSP-1620: applying black linter on packet parser files", "number": 775, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/775", "body": ""}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34444](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34444){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/775/_/diff#comment-329712082"}}
{"title": "MEROSP-1621: changing device vault_id from str to UUID", "number": 776, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/776", "body": "str type for everything is not a best practice.\nregression passed:\n{: data-inline-card='' }"}
{"comment": {"body": "Why not do the same for tenant id?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/776/_/diff#comment-329824129"}}
{"comment": {"body": "we use the same tenant\\_id, it\u2019s a fixture", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/776/_/diff#comment-329828279"}}
{"title": "MEROSP-1622: index wispr agent data frame", "number": 777, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/777", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "MEROSP-1623: removing obsolete code", "number": 778, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/778", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "MEROSP-1627: devices db : removing low resolution", "number": 779, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/779", "body": "type classifier: devices db - removing low resolution and combining filtering features into regular typing features\nwithout indexing: galaxy runtime performance unit tests improves a lot.\n\n"}
{"title": "Update icmp ns addresses when matching a device", "number": 78, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/78", "body": ""}
{"title": "MEROSP-1628: devices db lookup optimization - indexing", "number": 780, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780", "body": ""}
{"comment": {"body": "Can you share which sessions were used for the benchmark?\n\nI want to make sure we are covering all the scenarios and we have a good run-time in all of them\n\n1. Android/iPhone identified with a strong identifier \\(user-agent / mdns\\)\n2. Android/iPhone identified with L2 typing \\(canonical name\\)\n3. Android/iPhone not identified to exact model but just returns general type \\(for example \u201ciOS device\u201d / \u201cAndroid device\u201d\\)\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-329875584"}}
{"comment": {"body": "in iPhone test:  \n`eros_pipelines/tests/resources/iphone13_connected_ingestion_event.bin`\n\nin galaxy test:  \n`eros_pipelines/tests/resources/galaxy_s21_connected_ingestion_event.bin`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-329892525"}}
{"comment": {"body": "You have a very biased sampling as those sessions contains strong identifier with high confidence \\(user-agent / mdns info\\)\n\nCan you check with the following sessions that there isn\u2019t an impact of the performance?\n\n1. Android device that is identified by L2\n2. Android device that is not identified\n3. Apple device that is identified by L2\n\nI\u2019ve sent you the ingestion events over slack.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-330149535"}}
{"comment": {"body": "regression passed:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36200](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36200){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-330819615"}}
{"comment": {"body": "What is the meaning of this optimization?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-330824619"}}
{"comment": {"body": "when a query is `\"general_os\" == \"Android\"` we get 36000 rows out of 38000. then in the next score the 36000 are indexed online, which takes a lot of time.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-330828716"}}
{"comment": {"body": "But who says that this is the only case where we more than `MAX_NUM_OF_ROW_INDICES` results?\n\nI really don\u2019t like this hack.   \nDo we get degradation of the runtime without this?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-331380728"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} The assumption of the structure of the devices db table is that at least the first queries should reduce the number of rows to a small amount.   \n\nSure we get a big degradation in some very specific cases \\(\u201cAndroid device\u201d cases\\).  \n\nI don\u2019t like to argue. I think this \u201cAndroid device\u201d thing is a hack in the first place. It should not say in the context model what OS is installed. \n\nThere is context os for that. \n\nor there can be a third attribute for that.\n\nAnd yes. this is the only case where a column has more than 1500 same values.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-*********"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} \n\nThe reduction of the number of rows to a very small number can happen in a few cases:\n\n1\\. With tho string identifiers \\(i.e. user-agent/mDNS\\) and then it happens first.  \n2\\. Doing it with the L2 data - which happens last because it\u2019s with the lowest confidence. And this is where would see the degradation.\n\nThe L2 data is not a \u201cvery specific\u201d case, I would I expect it in production to be accounted to about 90% of the cases.\n\np.s. This is not the only case. When we have mac\\_vendor == \u2018Samsung\u2019 we have ~2700 results.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-*********"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} so do you think Samsung invented 2700 types of devices since 2006?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-*********"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} There are 2700\\+ different models in the database that belong to Samsung..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-*********"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} It means they produced on average more than 100 models per year. maybe we need to reduce the number of rows.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-*********"}}
{"comment": {"body": "It\u2019s not that the there are 100 different models per year. Is that that each model has so many different configurations, and each configuration is a different model number.\n\nAll in all, there are 2700 model numbers and this is the data we have and we need to work with.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-331385498"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} I will try another approach, where there will no need to index the remaining data frame on the fly.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/780/_/diff#comment-331402581"}}
{"title": "MEROSP-1578 fixing mac vendor values", "number": 781, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/781", "body": "fixing mac vendor values according MEROSP-1578"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35051](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35051){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/781/_/diff#comment-329872983"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35188](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35188){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/781/_/diff#comment-329908403"}}
{"title": "fix python in Data Archiver", "number": 782, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/782", "body": "fix python in Data Archiver\nERROR: Package 'eros-data-archiver' requires a different Python: 3.7.14 not in '=3.8'\n@{606d973d3e6ea000685ed65f}"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34713](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34713){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/782/_/diff#comment-329733147"}}
{"title": "filter in portion of the data and filter out black listed vaults and partners", "number": 783, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/783", "body": ""}
{"comment": {"body": "dwh stands for what?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/783/_/diff#comment-351952068"}}
{"comment": {"body": "dwh = datawarehouse", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/783/_/diff#comment-352876404"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6875](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6875)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/783/_/diff#comment-353028072"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6874](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6874){: data-inline-card='' } api\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6873](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6873){: data-inline-card='' } smoke\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/783/_/diff#comment-353058519"}}
{"title": "MEROSP-1613: removing Spider crawler user agent parser", "number": 784, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/784", "body": "fixing bug when user agent parser (3rd party lib) acknowledged a windows laptop as Spider crawler.\nregression passed:\n{: data-inline-card='' }"}
{"title": "Feature/MEROSP-1635 performance improvements   m", "number": 785, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/785", "body": "reading mac vendor csv to dict instead of going through data frame\nregression passed:\n{: data-inline-card='' }"}
{"title": "MEROSP-1636: more unit tests for device os range", "number": 786, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/786", "body": ""}
{"title": "black linter - sparrow_pipelines", "number": 787, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/787", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "more cases to runtime tests", "number": 788, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/788", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "MEROSP-1644 Fixed the wrong return value in Bonjour_UID::f_match()", "number": 789, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/789", "body": "This PR fixes a problem we have in the filtering method of the Bonjour UID - In case we dont have a Bonjour value in our current session and we do have it on a candidate from the DB, we filter our this device and wont try to match to it. And same thing vise-versa:\nIf f_match returns True  we keep the device in the candidates' list\nIf f_match returns False  we remove the device from the candidates list\nThere is no reason to remove a device as a candidate if we didnt get it. Its worth trying to match them anyway by other features instead.\n"}
{"comment": {"body": "please add unit test and make sure it is checking the return True", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/789/_/diff#comment-330548657"}}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35947)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/789/_/diff#comment-330550194"}}
{"comment": {"body": "I\u2019m working on a unitest now even though I\u2019m quite struggling with getting to this case. If we got to this stage, it means we know it\u2019s also an Apple device. On the other hand I\u2019m checking the case where we didn\u2019t get a Bonjour from it \\(which we do get from Apple devices\\). Maybe I\u2019ll try to edit an existing session", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/789/_/diff#comment-330550661"}}
{"comment": {"body": "no need to inject a session. just check this function", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/789/_/diff#comment-330551001"}}
{"title": "Hotfix/db connections issue", "number": 79, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/79", "body": "Move DB engine and sessionmaker to global\n"}
{"comment": {"body": "`sqlalchemy_engine_factory` appears in many tests\u2026should be fixed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/79/_/diff#comment-234925971"}}
{"title": "MEROSP-1645 Not using the EffectiveWifiMode as a query feature", "number": 790, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/790", "body": "The get_devices_db_query is a common method for typing features that defines the way we need to search for rows in the devices db csv, according to each feature. For example, the mac_vendor feature tells us the vendor of the session, so that method builds a query that searches vendors in the csv:\n@staticmethoddef get_devices_db_query(data):\n    cond = f\"{DevicesDBSchema.vendor} == '{data}'\"\n    return cond\nEach typing feature defines its own implementation according to the data collected by it.\nThe problem that is solved in this PR relates to the EffectiveWifiMode feature, which actually has nothing to do with any of our csv(s), and its only goal is to extract L2 data that we use later on (and especially the connection mode).\nSince it inherits from the L2DeviceTyping class, it inherits its get_devices_db_query implementation as well, which puts us in a funny situation where we just build queries that has nothing to do with the devices db csv and just causes us to search for nonsense. \nUp until now, it didnt cause any bug since the query made no sense and just returned nothing. But even just building it and running it is a waste of time and logically wrong.\n"}
{"comment": {"body": "add unit test please", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/790/_/diff#comment-330790789"}}
{"comment": {"body": "I\u2019m not sure there is really something to test here. It\u2019s just an improvement so we don\u2019t call a query that by-definition returns nothing. The code keeps working before and after this change. It\u2019s just avoiding un-needed calls", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/790/_/diff#comment-331410315"}}
{"comment": {"body": "you worked hard to find this improvement, don\u2019t let anyone erase your hard work.\n\nthe unit test is very simple, something like:\n\n```\ndef test_EffectiveWifiMode_get_devices_db_query():\n    assert EffectiveWifiMode().get_devices_db_query() is None\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/790/_/diff#comment-331512124"}}
{"comment": {"body": "np, I added a simple test", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/790/_/diff#comment-331749599"}}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/37647) \n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/790/_/diff#comment-331771407"}}
{"title": "Bugfix/MEROSP-1518 vivo v5 57 58 false positive (Hostname UNDETERMINED)", "number": 791, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/791", "body": "This PR solves the MEROSP-1518 issue but in fact changes a bit the way we match devices by their hostname. After the last improvements we made in that identification feature (subjecting each hostname to a fixed HostnameGroup), it was decided that if a hostname is classified to the UNDETERMINED group, we shall not consider it as a matching feature. What happened is that some devices had their factory default hostname as the hostname we got in the connections (even if we tried to change it), and therefore different devices had the same hostname and we had FP problems. For all those cases, the hostname was in the UNDETERMINED group, so this change will solve those problems for now until other decisions will be made regarding this component."}
{"comment": {"body": "unit test please", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/791/_/diff#comment-331775187"}}
{"comment": {"body": "Update: it looks like some tests are failing in the regression. I guess those are tests that relied on the hostname feature as the solo identifier feature. I\u2019m checking it and will update", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/791/_/diff#comment-331841687"}}
{"title": "MEROSP-1620: more black linter", "number": 792, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/792", "body": "regression passed:\n"}
{"title": "MEROSP-1649: don't use enum .value propetry", "number": 793, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/793", "body": "regression passed:\n{: data-inline-card='' }"}
{"comment": {"body": "This solved the non-deterministic unit tests?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/793/_/diff#comment-333674136"}}
{"comment": {"body": "I think so.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/793/_/diff#comment-333689907"}}
{"title": "added doc string as of my understanding, please fix me if u I miss something or if its unclear", "number": 794, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/794", "body": ""}
{"comment": {"body": "[https://levltech.atlassian.net/browse/MEROSP-1650](https://levltech.atlassian.net/browse/MEROSP-1650){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/794/_/diff#comment-330880447"}}
{"comment": {"body": "Can I suggest an updated wording?\n\n```\n\"\"\"\nOne-to-one conversion of a non-uuid agent serial (or vault id; used externally) to a uuid (used internally).\n\nParameters\n----------\nseed: str\n    A string representing the vault id.\n\nReturns\n-------\n    The vault id in uuid representation.\n\"\"\"\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/794/_/diff#comment-330946587"}}
{"comment": {"body": "Love it! @{5dbeb866c424110de52552cc}   \nDo you mind if we add the following clarification on top of yours?\n\n```\nif one streams an event to vault \"my_vault\", he should expect to get results \nfor a vault which will be whatever comes out of: \n\n>>> agent_serial_to_uuid(\"my_vault\")\n```\n\nThis is important for outside testers who are not fully aware of the classifier\u2019s internals.  \nWithout this piece of knowledge, no test will ever work\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/794/_/diff#comment-330956299"}}
{"comment": {"body": "@{5e2ee80dad92310e881b7a22} \n\nSuggesting:  \n`An ingestion event from vault_id = \u201dx\u201d will correspond to results with vault_id = agent_serial_to_uuid(\"x\")`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/794/_/diff#comment-331367039"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Mixed both suggestions :slight_smile: \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/794/_/diff#comment-331369071"}}
{"title": "MEROSP-1647-platform-type-missing", "number": 795, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/795", "body": ""}
{"comment": {"body": "very nice that you added a message. please change the message to be more specific ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/795/_/diff#comment-331374311"}}
{"comment": {"body": "this is the fastest way to find the row which is not as expected", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/795/_/diff#comment-331374554"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36687](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36687)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/795/_/diff#comment-331377979"}}
{"comment": {"body": "@{6085103f5797db006947d59a} yes. but what is wrong with the row? maybe you need to same the platform\\_type is wrong?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/795/_/diff#comment-331381480"}}
{"title": "more black linter", "number": 796, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/796", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "Feature/MEROSP-1657 create demo namespace on ero", "number": 797, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/797", "body": "For demo purposes I've created a new custom pipeline to deploy our entire stack to eros-prod EKS environment."}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} can you create deployment for job dogfood as well?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/797/_/diff#comment-331388469"}}
{"comment": {"body": "yes ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/797/_/diff#comment-331389003"}}
{"comment": {"body": "@{5f82bf320756940075db755e} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/797/_/diff#comment-331390005"}}
{"comment": {"body": "very nice!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/797/_/diff#comment-331554730"}}
{"title": "Insert configuration logic to TypeClassifier", "number": 798, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/798", "body": "This PR closes the gap that exist between the features configuration module and the type classification service.\nThe issue was that the type classification service doesnt take the features mode of operation into consideration when executing its logic on a given session.\nThe main change was to follow and enforce the definition of a \"disabled\" feature when iterating over existing features in a given session. \nThis means that all the methods of TypeClassifier will iterate over \"considered\" features, i.e. features that are not disabled(can be enabled or in shadow mode) instead of all the available features. The effect of this is that a feature that is disabled, will not be extracted from a session even if relevant data for it exist in the session, so its validity will be unknown. This change was reflected by changing the unit tests where the validity of the features is asserted.\nImportant note: The same gap exists in the match making service but was left unchanged due to its dispersed logic that defines and uses features in an inconsistent fashion that doesn't allow to give complete solution. For strong features the configuration will work in the match making service but a general solution for all the features is currently not feasible."}
{"comment": {"body": "Regression passed [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36927](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36927){: data-inline-card='' }   \n", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/798/_/diff#comment-331392366"}}
{"comment": {"body": "Tested locally:  \nRan a local classifier with router, disabled all the typing features except for _rpvr_ and connected an IPhoneX\\(lab device number 74\\).  \nReceived the following:  \n1\\. In the decision log:\n\n```\n\"context_process_info_features_mode\": { # 0-> feature is disabled, 1-> feature is enabled\n\"http_user_agent\": 0,\n\"quic_user_agent\": 0,\n\"mdns_info\": 0,\n\"low_res_user_agent\": 0,\n\"mac_vendor\": 0,\n\"ssdp_user_agent\": 0,\n\"wispr_agent\": 0,\n\"l2_union\": 0,\n\"l2_intersection\": 0,\n\"dhcpv4_lookup\": 0,\n\"release_date\": 0,\n\"effective_wifi_mode\": 0,\n\"mdns_rpvr\": 1\n},\n\"context_process_info_features_validity\": { # 4-> validity is unknown, 0-> validity is OK\n\"http_user_agent\": 4,\n\"quic_user_agent\": 4,\n\"mdns_info\": 4,\n\"low_res_user_agent\": 4,\n\"mac_vendor\": 4,\n\"ssdp_user_agent\": 4,\n\"wispr_agent\": 4,\n\"l2_union\": 4,\n\"l2_intersection\": 4,\n\"dhcpv4_lookup\": 4,\n\"release_date\": 4,\n\"effective_wifi_mode\": 4,\n\"mdns_rpvr\": 0\n},\n```\n\n2\\. In the result log:\n\n```\n    \"context_os_description\": \"iOS 15.6\",\n    \"context_os_name\": \"iOS\",\n    \"context_os_ver_major\": 15,\n    \"context_os_ver_minor_lower\": 6,\n    \"context_os_ver_minor_upper\": 6,\n    \"context_os_resolution\": 4\n```\n\nwhich is the correct OS for the given phone.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/798/_/diff#comment-331394119"}}
{"title": "MEROSP-1550 added amazon fire data for glinet", "number": 799, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/799", "body": "added amazone fire data for glinet"}
{"comment": {"body": "MEROSP-1550", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/799/_/diff#comment-331461706"}}
{"comment": {"body": "The amazon fire is not in the devices DB, so this won\u2019t do anything..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/799/_/diff#comment-331499836"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/37181](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/37181){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/799/_/diff#comment-331512355"}}
{"title": "Feature/device type identification 101 packages issue", "number": 8, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/8", "body": "Add feature extraction and device type identification content\nSome extra files around trying to make packages and paths work.\nRight now its in one package format"}
{"comment": {"body": "it was a placeholder .can be removed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/8/_/diff#comment-224588967"}}
{"title": "Now updating the device name when matching a device", "number": 80, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/80", "body": ""}
{"title": "MEROSP-1677 fixing eros-api to have no dependencies other than 3rd party packages", "number": 800, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/800", "body": "regression passed:\n{: data-inline-card='' }\npassed bump"}
{"comment": {"body": "why have you moved data archiver to the api? api should supply interfaces and common utils, not specific modules/packages..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/800/_/diff#comment-331620059"}}
{"comment": {"body": "It\u2019s really a very small part of the archiver. If you want the profiler to be able to write files to s3, I need it. profiler is being used in the api and there is no simple way to separate it from api.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/800/_/diff#comment-331760775"}}
{"title": "MEROSP-1628 index devices db offline", "number": 801, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/801", "body": ""}
{"comment": {"body": "I think we are at a good point where we can merge.\n\nWe would need to solve the Android hack as soon as possible.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/801/_/diff#comment-331711938"}}
{"comment": {"body": "I want to make sure we don\u2019t enter hacks", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/801/_/diff#comment-331714483"}}
{"comment": {"body": "The essence of the hack will still exist with a low-res DB: If we have only 1 \u201cOS-only\u201d feature, many rows can be returned from the DB. And the problem that is circumvented here \\(calling `_return_result_logic` with a large DB\\) will still occur. So the data structures should support these cases in a more fundemental way, and research are aware of that.\n\n@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d}  @{5d74d49897d8980d8eacd7f8}  \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/801/_/diff#comment-331758979"}}
{"comment": {"body": "regression passed \\(almost\\):\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/37747](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/37747){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/801/_/diff#comment-331794130"}}
{"title": "MEROSP-1649: small refactor", "number": 802, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/802", "body": "dont return data when there is not enough data\nregression passed:\n{: data-inline-card='' }"}
{"title": "MEROSP-1700 Don't enforce api version on producer", "number": 803, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/803", "body": "removed if clause for var _default_kafka so we'll never run with an api-version that doesn't support KafaHeaders, however keep the var as it is used as default fallback for many other vars\nresolves:\n"}
{"comment": {"body": "Keep in mind this api version does not support Kafka Headers, so regardless when you thought this \u201cif\u201d is sutisfied: you should never use it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/803/_/diff#comment-331574291"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/37357](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/37357)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/803/_/diff#comment-331760430"}}
{"title": "new apple devices", "number": 804, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/804", "body": "new apple devices sep 2022"}
{"comment": {"body": "Why is the diff so large?\n\nWe are just adding 4 devices..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/804/_/diff#comment-331711155"}}
{"comment": {"body": "Something went wrong.  \nI\u2019m remaking it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/804/_/diff#comment-331752735"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/38008](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/38008){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/804/_/diff#comment-331817235"}}
{"title": "remove push to cujo custom pipeline", "number": 805, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/805", "body": "Rollout of a new tag should be pushed during build time and not at any time."}
{"comment": {"body": "Link to Jira ussue", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/805/_/diff#comment-331834986"}}
{"title": "Some small improvements in mdns_parser.py", "number": 806, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/806", "body": "Minor changes\n"}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/37990)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/806/_/diff#comment-331816616"}}
{"comment": {"body": "please link with Jira issue", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/806/_/diff#comment-331834729"}}
{"title": "MEROSP-1626: reduced latency thresholds and enlarged throughput thresholds", "number": 807, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/807", "body": ""}
{"title": "black linter", "number": 808, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/808", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "trying to revive devices db lookup based on minimal/maximal os", "number": 809, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/809", "body": ""}
{"title": "Now updating the bonjour uid(s) when matching a device", "number": 81, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/81", "body": ""}
{"title": "fixing relative path of profiler files", "number": 810, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/810", "body": "no need of regression"}
{"title": "black in pre commit", "number": 811, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/811", "body": ""}
{"title": "install curl on classifier container for ecs healthcheck", "number": 812, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/812", "body": "since we've changed our docker image to python, curl is no longer pre-installed.\nfor ECS health check purposes we need to install it.\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} add subtask in the ecs story and link to this PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/812/_/diff#comment-332989542"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/812/_/diff#comment-332989591"}}
{"title": "MEROSP-1692 apple devices sep 22", "number": 813, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/813", "body": "added apple watch 8 and fixed max os\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/39058](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/39058)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/813/_/diff#comment-333763052"}}
{"title": "Add query by MAC  support in DB / Change primary sort key to MAC", "number": 814, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/814", "body": "\n\nIn order to improve performances when searching for a device in the database, the primary sort key was changed to MAC address instead of Levl Id.  \n\n1. Change the DB query logic inside the dynamo db API.\n2. Add unit tests.\n3. Change the docker compose files to create the dynamo db table with the mac address as primary sort key\n4. Change is the default value of the mac address fields in the device class which was changed to\nthe string value \"None\". This is due to the error that is raised if these values are actual None value or an empty string\n5. There is an existing dependency between the classification unit test that stem from the state of the database.\nIn order to solve this, the following changes were made:\n\nAdd an 'empty table' functionality to the db module\nAdd a 'get number of devices in table' to the db module\nAdd the relevant unit tests for these functions\nAdd a 'setup' fixture in the classification test module that will reset the db before any test is running.\n\n6. An additional change is to remove the parameter 'transient_id' from the ConnectionResult method that gives the network type, since it makes no sense to give a parameter that already belong to the class or can be retrieved from its own API.\n7. Add the logic inside the match maker to check if a device that was matched\nto the current session has a different mac address which means the record\nin the database should be updated, i.e. the existing record should be deleted\nand the device from the session should be inserted instead.\nAll the data from the existing record is inserted through the current session\nprocessing. A unit test was added.\n8. Fix the matching unit tests by adding a setup function that will clean the database\nbefore each test start.\n9. Add an option in the test utils to generate a mac address of 2 types: random or hardware.\nEach type is processed differently inside the classifier.\n10. Fix a bug in the de-duplication of devices.\nThe bug symptom was that the matched_features held duplicate strings that corresponded\nto the same feature that matched a device. This was in a scenario were 2 devices were\ncandidates to be matched, one had 2 features and the other only only one but the same as\nas the first candidate. It seemed as if the underlying assumption of the de-duplication\ndid not take such a case into account which is the reason it was defined as a list.\nThe solution was to turn the matched features list into a set, which will prevent this\nduplicity and will convey the fact that matched features should not hold duplicate records.\n11.Remove buggy lines from unit test that assigned the same mac address to the same session without any effect or necessity.\n12. Satisfy PR comments:\n-> remove mac_addr by band fields and insertion logic\n-> separate the random mac address generation in the test utils\n-> change the search in db function name\n13. Change the empty db logic in order to be independent of the minimum batch size the db can digest and to simplify the code.\n14. Fix the prepare vault functionality in test pipeline runtime.\nThe test gave all the devices the same mac address which conflicts with the new db table format, so a unique address was given\nto each device instead."}
{"title": "Feature/MEROSP-1594 support multiple threads cla - NOT FOR MERGE", "number": 815, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/815", "body": "Support Classifier scale as multiple threads for optimization of the ECS scheduling to reduce I/O wait and support scale with high throughput, low latency and minimum cost."}
{"title": "adding unit tests to monitoring", "number": 816, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/816", "body": "regression passed (almost): \n{: data-inline-card='' }"}
{"title": "local pcap extraction runner with readme", "number": 817, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/817", "body": ""}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/194](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/194){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/817/_/diff#comment-336865343"}}
{"title": "Bugfix/MEROSP-1725 rpvr iphone 8 plus connection", "number": 818, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/818", "body": "We had a problem in the DHCPv4 Lookup typing feature, which assumed that we always have a valid FeatureResult to investigate in parse_feature_result_from_device(). This statement was in fact true, until we added the features configuration component, which led some features to never be calculated and stay in their None value. So, in order to avoid the error we saw in the issue, we just need to make sure that the feature_result itself is not None before accessing to its data."}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/39455)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/818/_/diff#comment-334696174"}}
{"title": "MEROSP-1785 fixing profiler to disable before enable if it didn't happen already", "number": 819, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/819", "body": ""}
{"title": "Feature/metrics lag time", "number": 82, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/82", "body": "fix the lag time metric\n"}
{"comment": {"body": "it\u2019s better to calculate it before `raw_session = RawSession().parse(raw_bytes)` not include this processing time", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/82/_/diff#comment-235091279"}}
{"title": "black in pre commit", "number": 820, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/820", "body": "regression passed:\n{: data-inline-card='' }"}
{"comment": {"body": "@{6265307b185ac200692f9bd9} we have agreed to discuss before adding black as part of pre commit hooks\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/820/_/diff#comment-334713288"}}
{"comment": {"body": "this tool will ease the work of all developers. @{6085103f5797db006947d59a} agreed. we can always remove it if we\u2019ll see it\u2019s a pain.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/820/_/diff#comment-334814063"}}
{"comment": {"body": "also as you see this is the **Sixth** PR of this black insertion issue.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/820/_/diff#comment-334851755"}}
{"title": "adding unit tests for kafka utils", "number": 821, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/821", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "MEROSP-1150/Features aggregation", "number": 822, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/822", "body": "As for the features aggregation component, this PR adds our ability to keep track of our L2 data - whether the union or intersection. Up until now, we sometimes overrode the data in those dictionaries if we got new information of the same key (aka same platform and connection type). This fix comes to keep the data if we got the same key again, and perform union or intersection accordingly.\nFor all the other features, the features aggregation is already implemented by default, since the merge_feature_data method always chooses the new data over the old data, which ensures us that we keep the latest results.\nNote: This is a new PR that was opened to clear some git-jira issues"}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/39619)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/822/_/diff#comment-334704930"}}
{"title": "move non operational code out of logs", "number": 823, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/823", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "periodic spell check", "number": 824, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/824", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "updated model for iOS 15.7, 16 and 16.1 beta ; added L2 data for iPhone 14 and Apple watch series 8", "number": 825, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/825", "body": "updated model for iOS 15.7, 16 and 16.1 beta\nadded L2 data for iPhone 14 and Apple watch series 8\nadded missing blank line\nfixed apple watch test\nwithout dhcp fp model\n\n"}
{"comment": {"body": "Due to L2 fp of apple watch series 8, the resolution decreased", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/825/_/diff#comment-334714425"}}
{"comment": {"body": "Due to L2 fp of apple watch series 8, the resolution decreased", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/825/_/diff#comment-334714427"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/39817?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/39817?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/825/_/diff#comment-334769011"}}
{"comment": {"body": "For what I saw, The tests failed for two reasons:\n\n1. OS resolution - don\u2019t separate between iOS 15.6 and 15.7.\n2. Apple Watch - now we can\u2019t separate between Apple watch 6, 7 and 8.\n3. iPhone 13 family/iPhone 14 - in some cases, can\u2019t be distinguished by L2\n\n\u200c\n\nNeed to fix regression @{626ed79bd7fd480068d706a5}\n\nAnd I\u2019m waiting for @{5dbeb866c424110de52552cc} regarding dhcp fp for multiple OS versions", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/825/_/diff#comment-334769794"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} Why is it Arthur's responsibility to update the tests?\n\nIt's your PR and your changes so your should know best what has to be to done to  fix those.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/825/_/diff#comment-334775479"}}
{"comment": {"body": "Got it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/825/_/diff#comment-335103783"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/40304](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/40304){: data-inline-card='' } \n\npassed all tests after fixing", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/825/_/diff#comment-335149004"}}
{"title": "Bugfix/MEROSP-1646 Don't filter by random bonjour uids", "number": 826, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/826", "body": "The Bonjour UID is an important feature in the system which uses us in the identification process - both for matching devices and with filtering out devices. In fact, at the moment its the only filtering feature we have in the system. \nThis PR is an improvement of our Bonjour UIDs handling - In the cases of which we inspect a Bonjour UID from a random mac, we will not filter out candidate devices by it. Note that it does not mean that those devices might be matched or not in the future - we just wont remove them from the candidates list before that.\nThis component was in our comcast repo code, but forgotten when we moved to our new eros code."}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/39718)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/826/_/diff#comment-334798837"}}
{"title": "Feature/MEROSP-1604 user agent research   improv", "number": 827, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827", "body": "New user agent analyzer using aho-corasick string matching\n\n"}
{"comment": {"body": "why is this better? the vendor is already LGE", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827/_/diff#comment-334830474"}}
{"comment": {"body": "In this case the specific\\_name in the devices db is \u201cLG G6\u201d, while \u201cG6\u201d is the canonical\\_name. The specific\\_name is generally better to give if we can. I agree that in this particular case we don\u2019t really gain new information, but the behavior of the proposed code is correct given the devices db.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827/_/diff#comment-334833018"}}
{"comment": {"body": "why?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827/_/diff#comment-334838184"}}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827/_/diff#comment-334838385"}}
{"comment": {"body": "All results are checked against the devices db before they are accepted. This is essentially a way to skip the test for the OS _version_ \\(since we won\u2019t have all of them in the db\\). The answer for if the extracted feature in the allowed object will always be _yes_.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827/_/diff#comment-334839482"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} please add a comment", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827/_/diff#comment-334843630"}}
{"comment": {"body": "@{5f82bf320756940075db755e} added", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827/_/diff#comment-334881412"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} please go over all comments, old ones too. You can see them in the activity in the upper right corner of the webpage. It\u2019s a good practice to answer them all.  Even a \u201cI don\u2019t agree\u201d is ok.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827/_/diff#comment-334902799"}}
{"comment": {"body": "thanks :\\)\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/827/_/diff#comment-334922024"}}
{"title": "Add a test for the QUIC too long DCID field", "number": 828, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/828", "body": "One field in a packet that follows the QUIC protocol is the DCID, Destination Connection ID, and is defined to be of max length of 20 bytes. In this commit, a test with a session that contains QUIC packets with DCID longer than 20 bytes was added. The purpose of this test is to log such an occurrence for future reference, and is not meant to test a certain functionality beside the fact that the session can still be digested properly by the system. In addition, the error raised when such a packet is parsed was changed to a warning in order to prevent the impression that a critical event occurred.\nFrom the QUIC protocol definition:\n\n```\nDCID Len: \n    The byte following the version contains the length in bytes \n    of the Destination Connection ID field that follows it.\n    This length is encoded as an 8-bit unsigned integer.  In QUIC\n    version 1, this value MUST NOT exceed 20.  Endpoints that receive\n    a version 1 long header with a value larger than 20 MUST drop the\n    packet.  Servers SHOULD be able to read longer connection IDs from\n    other QUIC versions in order to properly form a version\n    negotiation packet.\nDestination Connection ID: \n    The Destination Connection ID field follows \n    the DCID Len and is between 0 and 20 bytes in length. \n    Section 7.2 describes the use of this field in more detail.\n```\n"}
{"comment": {"body": "Great description :thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/828/_/diff#comment-334840811"}}
{"comment": {"body": "Thanks :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/828/_/diff#comment-334841031"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/40018](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/40018){: data-inline-card='' }  passed regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/828/_/diff#comment-334870412"}}
{"title": "add unit tests for mac vendor", "number": 829, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/829", "body": "regression passed:\n{: data-inline-card='' }"}
{"comment": {"body": "Didn\u2019t add a valid scenario on purpose?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/829/_/diff#comment-335638794"}}
{"comment": {"body": "the valid scenario is tested according to the pytest coverage", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/829/_/diff#comment-335648208"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/829/_/diff#comment-335674239"}}
{"title": "s3 bucket name as env var", "number": 83, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/83", "body": ""}
{"comment": {"body": "@{6085103f5797db006947d59a} test\\_archiver.py", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/83/_/diff#comment-236179847"}}
{"comment": {"body": "please check the KAFKA\\_ADDRESS in all repos", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/83/_/diff#comment-236180879"}}
{"title": "created typing conflict resolver", "number": 830, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/830", "body": "moved code from type classifier to a new class\nregression passed:\n{: data-inline-card='' }"}
{"title": "read mac vendor as set and not to pandas for bonjour uid", "number": 831, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/831", "body": ""}
{"comment": {"body": "Regression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/40235)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/831/_/diff#comment-335054721"}}
{"title": "MEROSP-1855: update pytest.ini files", "number": 832, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/832", "body": ""}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34){: data-inline-card='' }  regression passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/832/_/diff#comment-336376894"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35){: data-inline-card='' }  smoke test passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/832/_/diff#comment-336376899"}}
{"title": "MEROSP-1856: support message type header for ingestion events", "number": 833, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/833", "body": ""}
{"title": "Feature/MEROSP-1833 DB Reduction - Don't save none fields", "number": 834, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/834", "body": "This PR is one of the multiple PRs well have in the upcoming few days in order to fulfil the DB reduction requirements (listed here).\nThis specific PR makes sure that we wont write useless data. All fields with None data will not be written. When we deserialize the data from the DB, well know that we need to fill them with None values if we dont see them in the serialized string we extracted.\nFor example, this data will be written to the DB:\n{\"id\": \"327ee43c-105c-4957-8355-dd6b60e9c912\", \"tenant_id\": \"0e67c260-d3df-4340-9a7a-50c75da3b621\", \"vault_id\": \"1aac6bc8-8041-4156-8d32-69bdc77466ac\", \n\"creation_session_id\": \"4e5540c1-29cf-4b21-81fb-00accdc6496c\", \"last_update_session_id\": \"cd538598-3025-495d-aa9c-8e425042bca8\", \"creation_time\": 1665476672.265329, \n\"last_update_time\": 1665476672.265339, \"status\": 0, \"mac_addr\": \"b2:80:03:f1:31:57\", \"first_assigned_mac_addr\": \"b2:89:03:f1:31:54\", \n\"type_vendor\": \"Apple\", \"hostname\": \"iPhone\", \"netbios_transaction_id\": 897459873}\nInstead of this data:\n{\"id\": \"3b94e616-572b-44ea-b92d-678eab745a45\", \"tenant_id\": \"97dd1b0b-8c25-4fdb-ab5e-f3f94b567369\", \"vault_id\": \"64db6588-076f-43c9-9219-4450c22f2d82\", \n\"creation_session_id\": \"96698d39-09f9-49fb-b0b2-3a5ccce61d71\", \"last_update_session_id\": \"bb228196-bb6a-45cb-8ab8-c3a708fdcdfb\", \"creation_time\": 1665476874.823837, \n\"last_update_time\": 1665476874.823842, \"status\": 0, \"mac_addr\": \"b3:82:03:f1:31:55\", \"first_assigned_mac_addr\": \"b6:84:03:f1:31:50\", \n\"type_vendor\": \"Apple\", \"type_os\": null, \"type_model\": null, \"typing_result\": null, \"hw_mac_addr\": null, \"hostname\": \"iPhone\", \n\"hostname_name_group\": null, \"ipv4\": null, \"ipv6\": null, \"dhcpv4_transaction_id\": null, \"dhcpv6_duid\": null, \"dhcpv6_duid_type\": null, \n\"dhcpv6_duid_hw_mac\": null, \"dhcpv6_duid_timestamp\": null, \"dhcpv6_ipv6\": null, \"cfo_estimated\": null, \"cfo_actual\": null, \"l2_caps\": null, \n\"wps_uid\": null, \"netbios_transaction_id\": 897459873, \"bonjour_uids\": null, \"icmp_ns_random_addresses\": null, \"icmp_mclr\": null, \n\"l1_slopes_identification\": null, \"prev_rand_mac_addresses\": null, \"ipv4_leases_store\": {}, \"typing_features\": {}, \"identification_level\": null, \n\"device_data_health\": null, \"arp_ipv4\": null, \"is_ipv4_static\": null, \"ws_discovery\": null, \"device_radio_caps\": null, \"last_evt_radio_details\": null, \"vpn_type\": null}\nIn addition, note that this feature actually makes us schemaless. No matter which fields well choose to add or remove from our Schema, we will not break the serialization / deserialization. Any field that will be added or removed wont affect us - well just write a None value/\n"}
{"comment": {"body": "this will bind the tests together as the uuid will be. the same for all of them ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/834/_/diff#comment-338526768"}}
{"comment": {"body": "The emphasis of those tests is to make sure that some fields are written / not written to the json format of the device, so I\u2019m not sure if that matters here. It\u2019s not a matching scenario or anything ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/834/_/diff#comment-338527303"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1665](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1665)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/834/_/diff#comment-338580547"}}
{"title": "Feature/rod in the barrel fix", "number": 835, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/835", "body": "added t header\nfix headers\njob yaml fix\n\n"}
{"title": "MEROSP-773 Metrics should be pushed via Telegraf to Prometheus", "number": 836, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/836", "body": "\n\nMEROSP-773: Support local and docker telegraf client with telegraf, prometheus and grafana compose stack\nMEROSP-773: fixed and tested telegraf client for classifier metrics - cloud watch dimensions should be fixed\nMEROSP-773: add env params to support relegraf host and port\n\n"}
{"title": "MEROSP-1882 levl di   deviceintelligence - update result avro schema for ML2", "number": 837, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/837", "body": "update result avro schema for ML2\nMEROSP-1882: fix import order\n\n"}
{"comment": {"body": "Do we have a PR in eros-infrastructure as well to match those changes in the schema?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/837/_/diff#comment-337522867"}}
{"comment": {"body": "[https://bitbucket.org/levl/eros-infrastructure/pull-requests/98](https://bitbucket.org/levl/eros-infrastructure/pull-requests/98){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/837/_/diff#comment-338700183"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2497](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2497){: data-inline-card='' } \n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2498](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2498){: data-inline-card='' } \n\nPassed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/837/_/diff#comment-338847014"}}
{"title": "adding IS_TAG flag to regression pipeline - identify if the trigger is for a tag", "number": 838, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/838", "body": "Adding a variable that indicates if the pipeline was triggered with a tag"}
{"comment": {"body": "\u200c\n\n![](https://bitbucket.org/repo/o5KReBa/images/3073103277-image.png)\nregression passed\n\n@{5f82bf320756940075db755e} Please approve the change so i can merge it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/838/_/diff#comment-337649282"}}
{"title": "MEROSP-1848 Decision Log - pipeline_process_info_total_latency", "number": 839, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/839", "body": "Now setting the pipeline_process_info_total_latency field in the Decision Log, which will be used in our regression tests to make sure our pipeline times wont exceed reasonable times."}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1033)\n\n:slight_smile:", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/839/_/diff#comment-337668376"}}
{"title": "use aws_region to get ecr creds", "number": 84, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/84", "body": "use aws_region to get ecr creds"}
{"title": "MEROSP-1896 Removed query_builder.py", "number": 840, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/840", "body": "Before the last big refactor we had in the classifier, the query_builder.py file was responsible for matching the identification features between devices and helping us get to the complete decision.\nSince then, the classifier was changed so each feature will have its own match function within each class, which makes the query_builder file unnecessary anymore. Remove it. If well ever need we can track it in our git history."}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1263)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/840/_/diff#comment-338117296"}}
{"title": "Feature/MEROSP-1835 DB Reduction - shorten fields names", "number": 841, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/841", "body": "This PR is another phase in our db reduction task, and this one specifically handles the long names of some of the fields in our schema.\nNote that some fields will be removed anyway (like l1_slopes_identification) in the next PR so theres no point with editing their name."}
{"comment": {"body": "Looks good, can you add please add on each PR of db reductions how much it reduced.. as there are more shorten options here, for example remove all '\\_', shorten hw\\_mac \u2192 hmac\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/841/_/diff#comment-338128775"}}
{"comment": {"body": "This PR reduces about 150-200 bytes. The next phase of reducing the unnecessary fields, as explained above \\(removing typing\\_result field\\), reduces more \\(1200 bytes\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/841/_/diff#comment-338129905"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1491](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1491)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/841/_/diff#comment-338551602"}}
{"title": "Feature/MEROSP-1837 DB Reduction - limit iterable fields sizes", "number": 842, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/842", "body": "This PR is another phase in our db reduction task, and this one specifically is more like a caution task - making sure that any field that can potentially take a lot of storage (for example - lists) will be bounded in size so well know for sure it wont harm us later on.\nThis PR handles the ipv4_leases field, the bonjour_uids and prev_rand_mac_addresses."}
{"comment": {"body": "Do we want to limit the number here or later? If we get a connection that contains both known and unknown IDs, we don\u2019t want to ignore either at this point\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/842/_/diff#comment-338510761"}}
{"comment": {"body": "I think so. If it\u2019s a new device, it\u2019s the last point in the code where we actually handle those bonjours \\(If I\u2019m not mistaking\\) ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/842/_/diff#comment-338511668"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} I was referring to the case of an existing device. We should probably separate the list used internally and the one saved externally. The one used internally shouldn\u2019t be limited as it might deteriorate the efficacy. And the one saved to DB should be chosen intelligently the way you implemented it below.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/842/_/diff#comment-338522074"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1829](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1829)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/842/_/diff#comment-338607187"}}
{"title": "Feature/MEROSP-1844 DB Reduction - remove unnecessary fields - Part 1", "number": 843, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/843", "body": "This PR is another phase in our db reduction task, and this one specifically handles some of our unused fields - the cfo fields, l1_slopes, radiocaps fields and vpn_type field.\nAnother PR is on the way with more fields that needs to be removed (such as the typing_result and is_ipv4_static). This is only one part of this phase."}
{"comment": {"body": "Can be removed from log too.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/843/_/diff#comment-338526437"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1792](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1792)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/843/_/diff#comment-338602544"}}
{"title": "re-enable import order forcing by flake8", "number": 844, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/844", "body": "regression passed (almost):\n{: data-inline-card='' }"}
{"title": "MEROSP-1899 Don't use str enums", "number": 845, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/845", "body": "don't use str enums\n\nregression passed (almost):\n{: data-inline-card='' }"}
{"comment": {"body": "Please wait for regression, I\u2019m concerened about `PlatformType`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/845/_/diff#comment-338562745"}}
{"title": "get devices table name from environment", "number": 846, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/846", "body": ""}
{"comment": {"body": "configmap? helm chart values?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/846/_/diff#comment-338593781"}}
{"title": "adding unit tests", "number": 847, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/847", "body": "regression passed (almost):\n{: data-inline-card='' }"}
{"title": "MEROSP-1884 Fixed the assignment of pipeline_process_info_total_latency", "number": 848, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/848", "body": "The pipeline_latency is calculated after we finish filling up the decision log, so we need to specifically fill this field after we finish our pipeline."}
{"comment": {"body": "I don\u2019t think it is recommended to lean on the fact that python uses copy by reference here. consider using return value", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/848/_/diff#comment-338612185"}}
{"comment": {"body": "No problem, I\u2019ll change it ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/848/_/diff#comment-338612925"}}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2007](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2007)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/848/_/diff#comment-338630669"}}
{"title": "Hotfix/dynamodb table name", "number": 849, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/849", "body": "ERROR: Package 'eros-helm-deployment' requires a different Python: 3.7.15 not in '>=3.8'\n\nwhen Bitbucket trie to create new deployment API version"}
{"comment": {"body": "didn\u2019t we had such param already? cluster-name or somerthing?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/849/_/diff#comment-*********"}}
{"comment": {"body": "cluster name != aws account region, cluster name is above this one", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/849/_/diff#comment-*********"}}
{"title": "DatArchiver add ability to pull exact namber off files from s3", "number": 85, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/85", "body": "DatArchiver: added ability to pull exact number off files from s3 in def pull_data\nAdded some docs to def pull_data"}
{"title": "MEROSP-1844 Removed l2_caps field from device_class", "number": 850, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/850", "body": "Forgotten in the last PR, this field is never set and never used, we get the l2 data from other sources."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2172](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2172)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/850/_/diff#comment-338670879"}}
{"title": "ClassifierConfiguration from parameter store", "number": 851, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/851", "body": "\n\nflake8 I101 added to ignore list. Import statements are in the wrong order. we need a script for this one\n{: data-inline-card='' }"}
{"title": "MEROSP-1910 added csv validator and fixed csv files", "number": 852, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/852", "body": "regression passed:\n{: data-inline-card='' }"}
{"comment": {"body": "We\u2019ll see these duplicate diffs forever as long as the sparrow side isn\u2019t aware of this script. @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} @{621df03094f7e20069fd6ab2} Can you consider incorporating it into the CSVs creation process?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/852/_/diff#comment-338955523"}}
{"title": "Feature/MEROSP-1834 Remove features_data_summary field", "number": 853, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/853", "body": "This PR removes the gigantic features_data_summary struct from the typing_result that we save in the DB. It takes a LOT of space and we dont really use it. Example of how it looks:\n\"features_data_summary\": {\"features_validity\": {\"http_user_agent\": 0, \"quic_user_agent\": 1, \"mdns_info\": 0, \"low_res_user_agent\": 0, \"mac_vendor\": 1, \"ssdp_user_agent\": 1, \"wispr_agent\": 0, \"l2_union\": 0, \"l2_intersection\": 0, \"dhcpv4_lookup\": 0, \"release_date\": 1, \"effective_wifi_mode\": 0, \"mdns_rpvr\": 0}, \"features_configs\": {\"http_user_agent\": 1, \"quic_user_agent\": 1, \"mdns_info\": 1, \"low_res_user_agent\": 1, \"mac_vendor\": 1, \"ssdp_user_agent\": 1, \"wispr_agent\": 1, \"l2_union\": 1, \"l2_intersection\": 1, \"dhcpv4_lookup\": 1, \"release_date\": 1, \"effective_wifi_mode\": 1, \"mdns_rpvr\": 1}, \"features_default_mode\": {\"http_user_agent\": true, \"quic_user_agent\": true, \"mdns_info\": true, \"low_res_user_agent\": true, \"mac_vendor\": true, \"ssdp_user_agent\": true, \"wispr_agent\": true, \"l2_union\": true, \"l2_intersection\": true, \"dhcpv4_lookup\": true, \"release_date\": true, \"effective_wifi_mode\": true, \"mdns_rpvr\": true}}\nInstead of saving it in the DB, I moved it to be saved under the Decision part of the ConnectionResult (we do need this data for the export adapter, we certainly do not need it for the DB)\nIn addition, two more improvements are made here:\n\nWell not save the vendor under the Mdns Rpvr feature (Its just always Apple, by definition).\nChanged the field name of device_os to just os, in the wispr agent and mdns rpvr fields.\n\n"}
{"comment": {"body": "Regression passed!  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2721](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2721){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/853/_/diff#comment-338934045"}}
{"title": "MEROSP-1834 Moved device_radio_caps and device_radio_mode outside of the DB storage", "number": 854, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/854", "body": "The device_radio_caps and device_radio_mode are two large fields that we store in the DB. We use them only for UI purposes. The data looks like this:\n\"device_radio_caps\": {\"supported_radio_bands\": \"2.4GHz, 5GHz\", \"supported_radio_modes\": \"802.11 a/b/g/n/ac/ax\", \"supported_enc_modes\": \"WEP, WPA, WPA2, WPA3\", \"supported_roaming_modes\": \"\", \"max_supported_data_rate\": \"\", \"supported_tx_power_range\": \"\", \"max_mimo_configuration\": \"\", \"dfs_support\": \"No\", \"mu_beamformee_capable\": \"\", \"wifi_chipset\": \"Apple\", \"wifi_sw_version\": \"\"}, \"device_radio_mode\": \"802.11n\"\nThis PR moves them outside of the DB to the Decision struct (which we dont save to the DB). If well need to bring back our UI anytime soon we can use the data from there, but there isnt a reason for us to keep it in the DB anyway."}
{"comment": {"body": "Regression passed!  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3306](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3306){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/854/_/diff#comment-339162503"}}
{"title": "revert promethues http endpoint", "number": 855, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/855", "body": "We need this http endpoint for ECS health check."}
{"title": "MEROSP-1884 with decision log validation", "number": 856, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/856", "body": "Check that decision log exists before accessing it. Supports cases where the pipeline ends with no result/decision logs (only error logs)."}
{"comment": {"body": "Regression passed!  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2961](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2961){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/856/_/diff#comment-338969451"}}
{"title": "MEROSP-1913 Separate Built-in Profiler From save_debug", "number": 857, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/857", "body": "Under save_debug, allow to disable built-in profiler."}
{"comment": {"body": "Regression passed!  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2977](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/2977){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/857/_/diff#comment-338953690"}}
{"title": "removing obsolete code", "number": 858, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/858", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "Change the util that generate a random mac address", "number": 859, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/859", "body": "generate only valid hexadecimal values from the corresponding characters\nadd another dimension of randomness to reduces the possibility of repeated mac values\n\n"}
{"comment": {"body": "Jira issues and links comments and branch please", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/859/_/diff#comment-338972678"}}
{"title": "Master merges from dev", "number": 86, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/86", "body": "Add more DB fields\nNew fields\nAdd more filters to bypass\nand now with dhcp_requested_ip\nadd bonjour and icmp6 ns features\nAdd matching and integration test with android device\nFix types\nflake8\nadapt parser to device session interface\nraise to 50msec\nadd bonjour and ICMP ns feature extraction unittests\nadded snapshot deployment\nfix typo\nMerge with new interface\nFix tests and formatting\nArchiver in fixture\nSSDP is Linux fix\nSSDP is Linux fix\nfixed topic\nerror handling + logs\nNow parsing DHCP ACK messages as well, mistakenly forgotten\nfixed logs\nadd logs and enable logging info\nadd logs and enable logging info\nImproved the choosing procedure of the parsing method\nForgot NB port\nlogging not used\nRemoved unnecessery lambda\nRemoved unnecessery print\nWrong key fix\nCreate db package\nKeep moving files around\nMove around\nMatching query will return the matching features\nAdd os filtering\nExtract mac address from session\nlocal changes\nFix test with DB cleanup setup Fix OS usage\nFix flake8 and icmpns\nAnother format\nStandartize Android OS Levl ID is 128-bit UUID\nDB: add last update DHCPv6: add windows flow\nAdd windows matching unittest\nFix tests\nWIP\nhistory devices\nLoad android device repository to DB and add query\nremove that file uploads until it's tested\nadd metrics decorator\nAdd non-random device name for windos and android with tests Store hostname in DB\nfix ci errors\nClear comments for flakey flake\nextract metrics to utils py\nmetrics decoration on all pipelines\nbug fix\nFix DHCPv4 IP usage Use DHCPv4 requested IP feature\nCleanup files\nCollect metrics in matching\nFix DHCPv4 requested IP usage\nadd history devices\nadd history devices - fix flake8\nadd history devices - fix cr\nFix test\nimpl\nFix DB create\ndon't use metrics\nRemove metrics\nimpl\nCreate DeviceHistory if not exist\nFormat file\nfix lint issues\nautomation ennvironment\nexecute pipeline as dask distributed\nadded deploy rollout\nnotifications\nMerged dev into dev_tools/distributed\nextract decision to json file\nrename notification\nremove bad tests\nrefator the serialization of device session as member function\nfix flake8\nfix tests with dask\nadding more functionality for pull and put data archiver by path &adding a test in test archaive using path\nsqs integration\nadd logging\nadd integration env\nyaml fix\nfix integration pipeline\nformat decisions list\nhack notification metrics\nmark out tests until local kafka dependency is fixed\n30 seconds timeout per integration test\nsolving some comments for the previous pull request\nRevert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\"\nskip integration test\nchange error logging to info\nadd kafka callback\nparse_session() does not longer belong to a class, it's stateless\nfix radio tap set() serialization\nfix radio tap set() serialization\n\nMerged in dev_tools/local_kafka (pull request #44)\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/decision_log (pull request #46)\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\n\nfix deployment decision snapshot\n\n\nMerged in amir_archiver (pull request #39)\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\n\n\nMerged in feature/gitignore_vscode (pull request #49)\nIgnore vscode helper file\n\nignore vscode helkper file\n\nApproved-by: Tamir Raz\n\n\nMerged in dev_tools/metrics (pull request #50)\nDev tools/metrics\n\nfix dask prom\nfix metrics instanciation\nfix flake8\ntimeout + input as byte like\nlist of sessions\nflake8\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev_tools/metrics\nrelocate pytest.ini\ndisable kafka integrated tests\nmetric fixes\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in dev_tools/per_developer_environment (pull request #51)\nDev tools/per_developer_environment\n\nMerge branch 'dev_tools/metrics' of bitbucket.org:levl/eros-classifier into dev_tools/per_developer_environment\ndeploy parallely\nuse summary instead of Histogram to measure duration\nflatten analytics\nuse histogram again\ntest\nrename\nrename2\nsummary histogram test\nrename\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in vault_id (pull request #48)\nupdate schemas to contain tenant and vault id\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into vault_id\nupdate schemas to contain tenant and vault id\nadded tenant and vault from session\nfix some tests\nchanges\nMerge branch 'dev' into vault_id\ntests fix\ntests fix\nadded test helper\nremoved insert to vaults\n\nApproved-by: Shimon Goulkarov\n\n\nhotfix db host\n\nchange DASK default value in code to false\nhot fix uuid to str\n\nMerged in hotfix_get_pipeline_configuration (pull request #53)\nchange pipeline name init\n\nchange pipeline name init\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bug/feature_none_exception (pull request #55)\nBug/feature none exception\n\nignore vscode helkper file\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nfix exception on features filter in case None\nfix code review comemnts\n\nApproved-by: Gregory kovelman\n\n\nfix flake\n\nchange to updated db hostname default\n\nMerged in match_against_tenant_vault (pull request #58)\nMatch against tenant vault\n\nadded Device = get_device_model()\nget tenant_name from env\nfix flake\ninit Device schema from env\nfix typo\nflake fix\nfix filter\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in migration_aws (pull request #59)\nMigration aws\n\nMerged in dev (pull request #19)\n\nDev\n\nfix radio tap set() serialization\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerged in hotfix/serialize_rt_sets (pull request #40)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar Approved-by: Itai Zolberg\n\nMerged in simplify_packet_parser (pull request #41)\n\nparse_session() does not longer belong to a class, it should be stateless\nApproved-by: Shimon Goulkarov\n\nfix radio tap set() serialization\nMerged in hotfix/serialize_rt_sets (pull request #42)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar\n\nMerged in dev_tools/local_kafka (pull request #44)\n\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\nMerged in feature/decision_log (pull request #46)\n\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\nfix deployment decision snapshot\nMerged in amir_archiver (pull request #39)\n\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\nApproved-by: Nadav Livni Approved-by: Itai Zolberg\n\nMerge branch 'master' of bitbucket.org:levl/eros-classifier into dev\nfix flake\nchange to updated db hostname default\n\nApproved-by: Nadav Livni\n\n\ndeploy yo ca-central-1\n\nremove canada region\n\nMerged in hotfix/get_mac_address (pull request #60)\nfix function\n\nfix function\nMerged dev into hotfix/get_mac_address\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/metrics_latency (pull request #61)\nFeature/metrics latency\n\nNow calculating the decision time and the time the device spent in the pipeline\nRemoved unnecessery generate_latest()\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\ntemporary remove column\ntemp remove column\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\n\nApproved-by: Ariel Tohar\n\n\nMerged in feature_label_metrics (pull request #62)\nFeature label metrics\n\nNow calculating the decision time and the time the device spent in the pipeline\nRemoved unnecessery generate_latest()\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\ntemporary remove column\ntemp remove column\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\nAdding labels to metrics\nAdding labels to metrics\nFix labeling\nFix UT\n\nApproved-by: Ariel Tohar Approved-by: Gregory kovelman\n\n\nsupport tenant name label in metrics\n\n\nMerged in step_metrics_label (pull request #63)\nStep metrics label\n\nMerged in dev (pull request #19)\n\nDev\n\nfix radio tap set() serialization\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerged in hotfix/serialize_rt_sets (pull request #40)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar Approved-by: Itai Zolberg\n\nMerged in simplify_packet_parser (pull request #41)\n\nparse_session() does not longer belong to a class, it should be stateless\nApproved-by: Shimon Goulkarov\n\nfix radio tap set() serialization\nMerged in hotfix/serialize_rt_sets (pull request #42)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar\n\nMerged in dev_tools/local_kafka (pull request #44)\n\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\nMerged in feature/decision_log (pull request #46)\n\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\nfix deployment decision snapshot\nMerged in amir_archiver (pull request #39)\n\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\nApproved-by: Nadav Livni Approved-by: Itai Zolberg\n\nMerge branch 'master' of bitbucket.org:levl/eros-classifier into dev\nfix flake\nchange to updated db hostname default\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nsupport tenant name label in metrics\n\nApproved-by: Gregory kovelman\n\n\nMerged in feature/fix_matching (pull request #64)\nmatching fixes\n\nvault id is not a matching filter but a general filter change how matching features looked in decision log\nfix tests\nfix tests\n\nApproved-by: Ariel Tohar Approved-by: Nadav Livni\n\n\nMerged in DataArchiver_bucket_name-fix (pull request #65)\nchanged DataArchiver bucket_name to one in a new S3 account\n\nchanged DataArchiver bucket_name to one in a new S3 account\nFix UT with updated bucket name\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nAdd metrics for session and logging\n\nFix flake\nconvert metrics to summary\nconvert metrics to summary - fix flake\nconvert metrics to summary - fix lenght got raw_session\nadd scheduler to config of deployment\ndask deployment scheduler\ndefault dask scheduler\n\nMerged in feature/performance_tests (pull request #66)\nFeature/performance tests\n\nsupport tenant name label in metrics\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nAdd metrics for session and logging\nFix flake\nconvert metrics to summary\nconvert metrics to summary - fix flake\nconvert metrics to summary - fix lenght got raw_session\nadd scheduler to config of deployment\ndask deployment scheduler\ndefault dask scheduler\n\nApproved-by: Nadav Livni\n\n\nMerged in feature/fix_matching (pull request #68)\nFeature/fix matching\n\nvault id is not a matching filter but a general filter change how matching features looked in decision log\nfix tests\nfix tests\nFixed the wrong thing :(\nperms\nconflic\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov\n\n\nLink session id and devices\n\n\nMerged in feature/fix_matching (pull request #69)\nFeature/fix matching\n\nvault id is not a matching filter but a general filter change how matching features looked in decision log\nfix tests\nfix tests\nFixed the wrong thing :(\nperms\nconflic\nFix source of decision\nmerge\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/session_id_link_to_devices (pull request #70)\nFeature/session id link to devices\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nLink session id and devices\nfix session id\nupdate UT with session_id\nfix tests\nMerge branch 'feature/session_id_link_to_devices' of bitbucket.org:levl/eros-classifier into feature/session_id_link_to_devices\n\nConflicts: matching/tests/test_matching.py\n\nfix tests\nrevert last commit\nfix devie_type NoneType exception\nMerge branch 'feature/session_id_link_to_devices' of bitbucket.org:levl/eros-classifier into feature/session_id_link_to_devices\n\nApproved-by: Nadav Livni\n\n\nhot fix deploy\n\nadded sed -i s|{{scheduler}}||g to all deployments\n\nMerged in feature/device_decision_time (pull request #72)\nNow writing the decision time to the DB\n\nNow writing the decision time to the DB\ninteger -> float\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/prev_rand_mac (pull request #67)\nFeature/prev_rand_mac\n\nUpdating instead of overriding the list of rand macs\nUpdating to a list in device history log table\nAttemp to add a column to Device table\nParameter fix\nQuery fix\nFlake fix\nMerge branch 'dev' of  into feature/prev_rand_mac\nRemoved update_table_if_required() method, we'll do iot manually for now\nFixed session.decision usage\nMerge branch 'dev' of  into feature/prev_rand_mac\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bugfix/none_list_prev_rand_mac (pull request #74)\nTraceback fix - None prev mac addressees\n\nTraceback fix with none prev rand mac address\n\n\n\nMerged in proto_update (pull request #73)\nnew proto\n\nnew proto\nMerged dev into proto_update\n\nApproved-by: Shimon Goulkarov Approved-by: Itai Zolberg\n\n\nMerged in bugfix/EROS_69_wrong_os_filter (pull request #75)\nBugfix/EROS-69 wrong os filter\n\nNow filtreing by the device os only if exists\nHandling None type as well\nstr compare fix\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/prev_rand_mac_list_fix (pull request #76)\nPrev rand Mac - Not adding the address to the previous addresses list if it already exists\n\nNot adding the rand mac address to the previous addresses list if it already exists\n\nApproved-by: Gregory kovelman\n\n\nMerged in bugfix/resualt_topic_issue (pull request #77)\nset _result_topic to SESSION_BATCH_SNAPSHOT_RESULT\n\nset _result_topic to SESSION_BATCH_SNAPSHOT_RESULT\nMerged dev into bugfix/resualt_topic_issue\n\nApproved-by: Shimon Goulkarov\n\n\nMove DB engine and sessionmaker to global\n\n\nMerged in feature/update_icmp_ns_when_matching (pull request #78)\nUpdate icmp ns addresses when matching a device\n\nNow updating icmp ns addresses when matching a device\nUsing set.union instead of +=\n\nApproved-by: Shimon Goulkarov\n\n\nflake fix\n\nfix ut\n\nMerged in hotfix/db_connections_issue (pull request #79)\nHotfix/db connections issue\n\ndefault dask scheduler\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nLink session id and devices\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMove DB engine and sessionmaker to global\nflake fix\nfix ut\nMerge branch 'hotfix/db_connections_issue' of bitbucket.org:levl/eros-classifier into dev\n\nApproved-by: Nadav Livni Approved-by: Gregory kovelman\n\n\nMerged in bugfix/EROS_65_update_device_hostname (pull request #80)\nNow updating the device name when matching a device\n\nNow updating the device name when matching a device\nMerge branch 'dev' of  into bugfix/EROS_65_update_device_hostname\n\nApproved-by: Gregory kovelman\n\n\nfix the lag time metric\n\n\nMerged in feature/metrics_lag_time (pull request #82)\nFeature/metrics lag time\n\nLink session id and devices\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMove DB engine and sessionmaker to global\nflake fix\nfix ut\nMerge branch 'hotfix/db_connections_issue' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nfix the lag time metric\n\nApproved-by: Gregory kovelman\n\n\naws_region as variable\n\n\nMerged in new_datalake (pull request #83)\ns3 bucket name as env var\n\ns3 bucket name as env var\nfix test\nfix flake and test\n\nApproved-by: Shimon Goulkarov\n\n\ntypo fix\n\nsed fix\n\n"}
{"title": "MEROSP-1789 Fix access to non-existant result log", "number": 860, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/860", "body": "Check result log exists before accessing it.\n@{5f82bf320756940075db755e} Why is the data being accessed through the result log? It should be available regardless. Please advise."}
{"title": "Feature/MEROSP-1077 pull eros configuration from", "number": 861, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/861", "body": "MEROSP-1077: fix exception on configs\n"}
{"title": "MEROSP-1457 Fix DB record sizes metrics", "number": 862, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/862", "body": "Fix db size metrics to reflect actual record sizes. We were looking at the number of elements in the item dict."}
{"title": "Feature/MEROSP-1831 Change Typing Features to Keep Only Results", "number": 863, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/863", "body": "Saving typing features data in an explicit manner.\nSaving feature value, without wrapping TypingResult struct and single feature typing result.\n\n"}
{"comment": {"body": "Regression passed!  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3439](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3439){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/863/_/diff#comment-339199815"}}
{"title": "MEROSP-1844 Removed typing_features field, it's unused", "number": 864, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/864", "body": "That field was forgotten between the merges, we dont use anymore (at all), it can be removed :)"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3535](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3535){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/864/_/diff#comment-339319350"}}
{"title": "MEROSP-1910 added script that adds columns to devices_db", "number": 865, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/865", "body": "columns:\nfamily\nclass\nline\ntype\nbreaking specific_name to its parts"}
{"comment": {"body": "Have you update the confluence page with the schema? Was it updated in dror\u2019s side?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/865/_/diff#comment-339405551"}}
{"comment": {"body": "can we make this script run as part of Dror\u2019s script.. so we won\u2019t miss changes in device\\_db and this one not aligned..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/865/_/diff#comment-339406322"}}
{"title": "fixing devices db path in env file", "number": 866, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/866", "body": ""}
{"title": "Feature/MEROSP-1844 Remove features_data_summary field from serialization", "number": 867, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/867", "body": "This PR adds an additional minor improvement to our latest DB reduction efforts, by not including the features_data_summary and was_prior_logic_used fields in our serialized device record."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3708](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3708)\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/867/_/diff#comment-339392211"}}
{"title": "MEROSP-1844 Removed cpative_portal and supported_protocols fields", "number": 868, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/868", "body": "The captive_portal and supported_protocols fields are never set and we never use them. We can simply remove them :)"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3806](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/3806)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/868/_/diff#comment-339430453"}}
{"title": "MEROSP-1949", "number": 869, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/869", "body": "adding isort to pre-commit, sorts python imports to groups\nsorts lexicographically\n\n"}
{"comment": {"body": "Nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/869/_/diff#comment-339446998"}}
{"title": "dev merge to master", "number": 87, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/87", "body": "\n"}
{"title": "MEROSP-1958 - unit tests and removing obsolete code", "number": 870, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/870", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "add cluster suffix to ssm parameter, rename cluster env var", "number": 871, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/871", "body": "We should be able to create the same environments in different clusters,\nRename cluster environment variable for simplicity."}
{"title": "added prints to file validators", "number": 872, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/872", "body": ""}
{"title": "Add search by mac logic to device connection pipe", "number": 873, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/873", "body": "\n\nAdd the logical part that skips the matching process for a given session. in case the event is a connection event, and the mac address of the connecting device exist already in the database. This is based on the assumption that 2 devices that share the same mac address are actually the same device.\nThis change required an additional change in 2 unit tests.\n  -> test_mate_10_24ghz_with_ua; the result log ceased to be updated once the matching was not performed so these assertions were removed.\n  -> test_identity_dedup_correction_a21; the mac address in the event was changed to be different for the last session in order to enable a deduplication based on the features. If the mac address in the last session is not different from the 2 other sessions, only 2 devices will be created and the test will fail.\nRemove the db calls from inside the match maker and place them inside the pipeline. The definition of match maker is an algorithm/service that gives a decision if a device is equal or not to another device from a given set of devices, and is not required to know the implementation details of where those devices come from nor has the necessity to have an API that fetch these devices.\nRemove the feature extraction from inside the match maker and place them inside the pipeline. As a part of reducing and refining the API for the modules in the classifier, the extraction part was placed outside the match maker module. The same should and will be done for the type classifier. The motivation is that the feature extraction is closer logically to the parsing and organization of data than the core logic of matching algorithm itself.\nRemove parameters from functions inside the match maker that are not used and reduce internal function calls.\nRemove the empty device table functionality from the database module as it is an unsafe function. The corresponding unit test was removed and the unit tests that had dependencies and used the empty table functionality were refactored via the 'generate_session' util function. The vault id in the generate_session was constant for all the test and was replaced with a randomly generated one along with the option to request a certain vault id in order to maintain the same vault id for multiple sessions inside a unit test.\nFix calls to functions that had their API changed inside unit tests.\n\nAdd device attribute extraction\nAfter changing the connection event pipeline to bypass the matching service if the device is already found in the database, a logical hole was detected.\nThe matching service performed the action of extracting and updating in the decision some attributes of the device, such as its IP address and hostname.\nThese attributes are not necessarily used to identify the device but are\nused as additional data that is of interest and should be presented to the client.\nThis commit add this part in a fashion that is not optimal globaly but make the\nbest out of the existing architecture. Whenever it will be possible, an additional,\nsepparated service will be defined that will take care of this part in the system\nin a much more effective way.\nThe current solution define a new module that extract the ip and hostname,\nadd an additional method to the match maker that perform the extraction,\nand call it in the pipeline directly or internally via the match method inside the\nmatch maker.\n\n\n"}
{"comment": {"body": "regression passed [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/latest/5517](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/latest/5517){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/873/_/diff#comment-341184645"}}
{"title": "adding flake8 cognitive complexity", "number": 874, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/874", "body": "alerts on too complex functions\nregression passed:\n{: data-inline-card='' }"}
{"title": "MEROSP-1994 adding parse coverage script to pipeline", "number": 875, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/875", "body": "adding script to pipeline that merges the coverage of the unit tests to one place and gives the stats about it"}
{"title": "MEROSP-782/cluster separation vars", "number": 876, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/876", "body": ""}
{"title": "MEROSP-2009-multiple-uneeded-pip-install", "number": 877, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/877", "body": ""}
{"title": "removing unused functions", "number": 878, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/878", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "fixing bug where tests are failing but pipeline doesn't", "number": 879, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/879", "body": ""}
{"title": "Dev", "number": 88, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/88", "body": "\n\n\nMerged in new_datalake (pull request #83)\ns3 bucket name as env var\n\ns3 bucket name as env var\nfix test\nfix flake and test\n\nApproved-by: Shimon Goulkarov\n\n\ntypo fix\n\nsed fix\n\nMerged in DatArchiver-add-ability-to-pull-exact-namber-off-files-from-s3 (pull request #85)\nDatArchiver add ability to pull exact namber off files from s3\n\nDatArchiver: added ability to pull exact number off files from s3\nfix conflict\nMerge branch 'dev' into DatArchiver-add-ability-to-pull-exact-namber-off-files-from-s3\n\nApproved-by: Shimon Goulkarov\n\n\n"}
{"title": "fixing spelling", "number": 880, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/880", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "Remove verbose logs", "number": 881, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/881", "body": "These prints were spamming us with no real need. Thanks @{5ed4f1de9a64eb0c1e78f73b}"}
{"title": "MEROSP-2033 reducing cognitive complexity th", "number": 882, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/882", "body": "Complex code is not good.  flake8 can help alerting on complex code. this PR is about complex functions. reducing the threshold means setting the max complexity to a lower bound.\nregression passed:\n{: data-inline-card='' }\n"}
{"title": "change os environ to classifier config at telemetry modules", "number": 883, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/883", "body": ""}
{"title": "MEROSP-2045 adding flake8-expression-complexity", "number": 884, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/884", "body": "alerts on expressions that are too complex\nregression passed (almost):\n{: data-inline-card='' }"}
{"title": "set api version to latest", "number": 885, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/885", "body": "there was a bug with the pipeline that bump its version, should be working after this one."}
{"title": "MEROSP-2064 Fixed UAResult import", "number": 886, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/886", "body": "UAResult was defined outside of the api package, which broke the system a bit when it could not be imported from there."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5431](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/5431)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/886/_/diff#comment-341127391"}}
{"title": "Bugfix/fix push api", "number": 887, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/887", "body": "\n\nuse python 3.9 as base image\n\n"}
{"title": "Support Cujo LENS integration", "number": 888, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/888", "body": "Support Cujo LENS integration\nIn order to extract data to our DWH from production environments, we need to make kinesis json schema (data models) integration with Cujo LENS.\nThe story support requires changes according the defined schemas for kinesis streams, so cujo will be able to consume, obfuscate and forward to LEVL DWH.\n\nAligned Cujo deployments pipelines\nAdded schema registry API with UT\nSupport dynamic schema registry and refactor api to support the schema registry with UT\nBonus: reduced CI/CD times from ~22min  ~17min\n\n\n"}
{"title": "MEROSP-1996-regression-gli-net---ios-16", "number": 889, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889", "body": "update models for iOS 16.1"}
{"comment": {"body": "please reconsider this. the data is the same as iOS/iPadOS 15 and watchOS 8. I hope the regression will fail. no need to add data that is not differentiating. @{621df03094f7e20069fd6ab2} already encountered this problem ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-341656809"}}
{"comment": {"body": "The classifier should support multiple results for same feature", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-341657635"}}
{"comment": {"body": "we maybe should find a more compact way to write the model", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-341657682"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} this will reduce the resolution.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-341657928"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} We must reduce resolution, because we don\u2019t know which major it is. we can discuss this with Tamir today", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-341658518"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} Indeed some tests fail. Two issues here:\n\n1. When there are multiple os version options for a dhcp fp, the system takes the newer one only \\(not a range\\)\n2. Since dhcp fp is higher confidence than wispr, the wispr agent is ignored and the higher version from dhcp fp trumps it\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-341659786"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2}\n\nIt looks like \\(2\\) is directly caused by \\(1\\). It\u2019s not a problem that the wispr is with a lower confidence \\(as it should be\\) but the system should know how to handle it with multiple results from DHCP.\n\nIs issue [MEROSP-2102](https://levltech.atlassian.net/browse/MEROSP-2102) is the one opened to fix issue \\(1\\)? @{5dbeb866c424110de52552cc}", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-341897010"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} and this issue  \n[https://levltech.atlassian.net/browse/MEROSP-1851](https://levltech.atlassian.net/browse/MEROSP-1851){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-342347374"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-343371462"}}
{"comment": {"body": "I merged another PR with these rpvr and wispr changes  \n[https://bitbucket.org/levl/eros-classifier/pull-requests/893](https://bitbucket.org/levl/eros-classifier/pull-requests/893){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-343371730"}}
{"comment": {"body": "Regression passed locally \\(can\u2019t run regression with custom branch vs. automation branch with the same name\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-348982262"}}
{"comment": {"body": "Tried again but had other issues with NoBrokersAvailable. Still passed locally.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-349948576"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} @{62e7c839e50f2f2a395430c2} \n\nWhy are we not merging this? What we are waiting for?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-350180349"}}
{"comment": {"body": "it changes 112 results in the regression:  \n[https://bitbucket.org/levl/eros-automation/pull-requests/389](https://bitbucket.org/levl/eros-automation/pull-requests/389){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/889/_/diff#comment-350198605"}}
{"title": "Dev merges to master", "number": 89, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/89", "body": "Add more DB fields\nNew fields\nAdd more filters to bypass\nand now with dhcp_requested_ip\nadd bonjour and icmp6 ns features\nAdd matching and integration test with android device\nFix types\nflake8\nadapt parser to device session interface\nraise to 50msec\nadd bonjour and ICMP ns feature extraction unittests\nadded snapshot deployment\nfix typo\nMerge with new interface\nFix tests and formatting\nArchiver in fixture\nSSDP is Linux fix\nSSDP is Linux fix\nfixed topic\nerror handling + logs\nNow parsing DHCP ACK messages as well, mistakenly forgotten\nfixed logs\nadd logs and enable logging info\nadd logs and enable logging info\nImproved the choosing procedure of the parsing method\nForgot NB port\nlogging not used\nRemoved unnecessery lambda\nRemoved unnecessery print\nWrong key fix\nCreate db package\nKeep moving files around\nMove around\nMatching query will return the matching features\nAdd os filtering\nExtract mac address from session\nlocal changes\nFix test with DB cleanup setup Fix OS usage\nFix flake8 and icmpns\nAnother format\nStandartize Android OS Levl ID is 128-bit UUID\nDB: add last update DHCPv6: add windows flow\nAdd windows matching unittest\nFix tests\nWIP\nhistory devices\nLoad android device repository to DB and add query\nremove that file uploads until it's tested\nadd metrics decorator\nAdd non-random device name for windos and android with tests Store hostname in DB\nfix ci errors\nClear comments for flakey flake\nextract metrics to utils py\nmetrics decoration on all pipelines\nbug fix\nFix DHCPv4 IP usage Use DHCPv4 requested IP feature\nCleanup files\nCollect metrics in matching\nFix DHCPv4 requested IP usage\nadd history devices\nadd history devices - fix flake8\nadd history devices - fix cr\nFix test\nimpl\nFix DB create\ndon't use metrics\nRemove metrics\nimpl\nCreate DeviceHistory if not exist\nFormat file\nfix lint issues\nautomation ennvironment\nexecute pipeline as dask distributed\nadded deploy rollout\nnotifications\nMerged dev into dev_tools/distributed\nextract decision to json file\nrename notification\nremove bad tests\nrefator the serialization of device session as member function\nfix flake8\nfix tests with dask\nadding more functionality for pull and put data archiver by path &adding a test in test archaive using path\nsqs integration\nadd logging\nadd integration env\nyaml fix\nfix integration pipeline\nformat decisions list\nhack notification metrics\nmark out tests until local kafka dependency is fixed\n30 seconds timeout per integration test\nsolving some comments for the previous pull request\nRevert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\"\nskip integration test\nchange error logging to info\nadd kafka callback\nparse_session() does not longer belong to a class, it's stateless\nfix radio tap set() serialization\nfix radio tap set() serialization\n\nMerged in dev_tools/local_kafka (pull request #44)\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/decision_log (pull request #46)\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\n\nfix deployment decision snapshot\n\n\nMerged in amir_archiver (pull request #39)\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\n\n\nMerged in feature/gitignore_vscode (pull request #49)\nIgnore vscode helper file\n\nignore vscode helkper file\n\nApproved-by: Tamir Raz\n\n\nMerged in dev_tools/metrics (pull request #50)\nDev tools/metrics\n\nfix dask prom\nfix metrics instanciation\nfix flake8\ntimeout + input as byte like\nlist of sessions\nflake8\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev_tools/metrics\nrelocate pytest.ini\ndisable kafka integrated tests\nmetric fixes\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in dev_tools/per_developer_environment (pull request #51)\nDev tools/per_developer_environment\n\nMerge branch 'dev_tools/metrics' of bitbucket.org:levl/eros-classifier into dev_tools/per_developer_environment\ndeploy parallely\nuse summary instead of Histogram to measure duration\nflatten analytics\nuse histogram again\ntest\nrename\nrename2\nsummary histogram test\nrename\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in vault_id (pull request #48)\nupdate schemas to contain tenant and vault id\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into vault_id\nupdate schemas to contain tenant and vault id\nadded tenant and vault from session\nfix some tests\nchanges\nMerge branch 'dev' into vault_id\ntests fix\ntests fix\nadded test helper\nremoved insert to vaults\n\nApproved-by: Shimon Goulkarov\n\n\nhotfix db host\n\nchange DASK default value in code to false\nhot fix uuid to str\n\nMerged in hotfix_get_pipeline_configuration (pull request #53)\nchange pipeline name init\n\nchange pipeline name init\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bug/feature_none_exception (pull request #55)\nBug/feature none exception\n\nignore vscode helkper file\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nfix exception on features filter in case None\nfix code review comemnts\n\nApproved-by: Gregory kovelman\n\n\nfix flake\n\nchange to updated db hostname default\n\nMerged in match_against_tenant_vault (pull request #58)\nMatch against tenant vault\n\nadded Device = get_device_model()\nget tenant_name from env\nfix flake\ninit Device schema from env\nfix typo\nflake fix\nfix filter\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in migration_aws (pull request #59)\nMigration aws\n\nMerged in dev (pull request #19)\n\nDev\n\nfix radio tap set() serialization\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerged in hotfix/serialize_rt_sets (pull request #40)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar Approved-by: Itai Zolberg\n\nMerged in simplify_packet_parser (pull request #41)\n\nparse_session() does not longer belong to a class, it should be stateless\nApproved-by: Shimon Goulkarov\n\nfix radio tap set() serialization\nMerged in hotfix/serialize_rt_sets (pull request #42)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar\n\nMerged in dev_tools/local_kafka (pull request #44)\n\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\nMerged in feature/decision_log (pull request #46)\n\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\nfix deployment decision snapshot\nMerged in amir_archiver (pull request #39)\n\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\nApproved-by: Nadav Livni Approved-by: Itai Zolberg\n\nMerge branch 'master' of bitbucket.org:levl/eros-classifier into dev\nfix flake\nchange to updated db hostname default\n\nApproved-by: Nadav Livni\n\n\ndeploy yo ca-central-1\n\nremove canada region\n\nMerged in hotfix/get_mac_address (pull request #60)\nfix function\n\nfix function\nMerged dev into hotfix/get_mac_address\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/metrics_latency (pull request #61)\nFeature/metrics latency\n\nNow calculating the decision time and the time the device spent in the pipeline\nRemoved unnecessery generate_latest()\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\ntemporary remove column\ntemp remove column\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\n\nApproved-by: Ariel Tohar\n\n\nMerged in feature_label_metrics (pull request #62)\nFeature label metrics\n\nNow calculating the decision time and the time the device spent in the pipeline\nRemoved unnecessery generate_latest()\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\ntemporary remove column\ntemp remove column\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/metrics_after_rebase\nAdding labels to metrics\nAdding labels to metrics\nFix labeling\nFix UT\n\nApproved-by: Ariel Tohar Approved-by: Gregory kovelman\n\n\nsupport tenant name label in metrics\n\n\nMerged in step_metrics_label (pull request #63)\nStep metrics label\n\nMerged in dev (pull request #19)\n\nDev\n\nfix radio tap set() serialization\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerged in hotfix/serialize_rt_sets (pull request #40)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar Approved-by: Itai Zolberg\n\nMerged in simplify_packet_parser (pull request #41)\n\nparse_session() does not longer belong to a class, it should be stateless\nApproved-by: Shimon Goulkarov\n\nfix radio tap set() serialization\nMerged in hotfix/serialize_rt_sets (pull request #42)\n\nfix radio tap set() serialization\nApproved-by: Ariel Tohar\n\nMerged in dev_tools/local_kafka (pull request #44)\n\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\nMerged in feature/decision_log (pull request #46)\n\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\nfix deployment decision snapshot\nMerged in amir_archiver (pull request #39)\n\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\nApproved-by: Nadav Livni Approved-by: Itai Zolberg\n\nMerge branch 'master' of bitbucket.org:levl/eros-classifier into dev\nfix flake\nchange to updated db hostname default\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nsupport tenant name label in metrics\n\nApproved-by: Gregory kovelman\n\n\nMerged in feature/fix_matching (pull request #64)\nmatching fixes\n\nvault id is not a matching filter but a general filter change how matching features looked in decision log\nfix tests\nfix tests\n\nApproved-by: Ariel Tohar Approved-by: Nadav Livni\n\n\nMerged in DataArchiver_bucket_name-fix (pull request #65)\nchanged DataArchiver bucket_name to one in a new S3 account\n\nchanged DataArchiver bucket_name to one in a new S3 account\nFix UT with updated bucket name\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nAdd metrics for session and logging\n\nFix flake\nconvert metrics to summary\nconvert metrics to summary - fix flake\nconvert metrics to summary - fix lenght got raw_session\nadd scheduler to config of deployment\ndask deployment scheduler\ndefault dask scheduler\n\nMerged in feature/performance_tests (pull request #66)\nFeature/performance tests\n\nsupport tenant name label in metrics\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nAdd metrics for session and logging\nFix flake\nconvert metrics to summary\nconvert metrics to summary - fix flake\nconvert metrics to summary - fix lenght got raw_session\nadd scheduler to config of deployment\ndask deployment scheduler\ndefault dask scheduler\n\nApproved-by: Nadav Livni\n\n\nMerged in feature/fix_matching (pull request #68)\nFeature/fix matching\n\nvault id is not a matching filter but a general filter change how matching features looked in decision log\nfix tests\nfix tests\nFixed the wrong thing :(\nperms\nconflic\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov\n\n\nLink session id and devices\n\n\nMerged in feature/fix_matching (pull request #69)\nFeature/fix matching\n\nvault id is not a matching filter but a general filter change how matching features looked in decision log\nfix tests\nfix tests\nFixed the wrong thing :(\nperms\nconflic\nFix source of decision\nmerge\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/session_id_link_to_devices (pull request #70)\nFeature/session id link to devices\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nLink session id and devices\nfix session id\nupdate UT with session_id\nfix tests\nMerge branch 'feature/session_id_link_to_devices' of bitbucket.org:levl/eros-classifier into feature/session_id_link_to_devices\n\nConflicts: matching/tests/test_matching.py\n\nfix tests\nrevert last commit\nfix devie_type NoneType exception\nMerge branch 'feature/session_id_link_to_devices' of bitbucket.org:levl/eros-classifier into feature/session_id_link_to_devices\n\nApproved-by: Nadav Livni\n\n\nhot fix deploy\n\nadded sed -i s|{{scheduler}}||g to all deployments\n\nMerged in feature/device_decision_time (pull request #72)\nNow writing the decision time to the DB\n\nNow writing the decision time to the DB\ninteger -> float\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/prev_rand_mac (pull request #67)\nFeature/prev_rand_mac\n\nUpdating instead of overriding the list of rand macs\nUpdating to a list in device history log table\nAttemp to add a column to Device table\nParameter fix\nQuery fix\nFlake fix\nMerge branch 'dev' of  into feature/prev_rand_mac\nRemoved update_table_if_required() method, we'll do iot manually for now\nFixed session.decision usage\nMerge branch 'dev' of  into feature/prev_rand_mac\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bugfix/none_list_prev_rand_mac (pull request #74)\nTraceback fix - None prev mac addressees\n\nTraceback fix with none prev rand mac address\n\n\n\nMerged in proto_update (pull request #73)\nnew proto\n\nnew proto\nMerged dev into proto_update\n\nApproved-by: Shimon Goulkarov Approved-by: Itai Zolberg\n\n\nMerged in bugfix/EROS_69_wrong_os_filter (pull request #75)\nBugfix/EROS-69 wrong os filter\n\nNow filtreing by the device os only if exists\nHandling None type as well\nstr compare fix\n\nApproved-by: Shimon Goulkarov Approved-by: Gregory kovelman\n\n\nMerged in feature/prev_rand_mac_list_fix (pull request #76)\nPrev rand Mac - Not adding the address to the previous addresses list if it already exists\n\nNot adding the rand mac address to the previous addresses list if it already exists\n\nApproved-by: Gregory kovelman\n\n\nMerged in bugfix/resualt_topic_issue (pull request #77)\nset _result_topic to SESSION_BATCH_SNAPSHOT_RESULT\n\nset _result_topic to SESSION_BATCH_SNAPSHOT_RESULT\nMerged dev into bugfix/resualt_topic_issue\n\nApproved-by: Shimon Goulkarov\n\n\nMove DB engine and sessionmaker to global\n\n\nMerged in feature/update_icmp_ns_when_matching (pull request #78)\nUpdate icmp ns addresses when matching a device\n\nNow updating icmp ns addresses when matching a device\nUsing set.union instead of +=\n\nApproved-by: Shimon Goulkarov\n\n\nflake fix\n\nfix ut\n\nMerged in hotfix/db_connections_issue (pull request #79)\nHotfix/db connections issue\n\ndefault dask scheduler\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nLink session id and devices\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMove DB engine and sessionmaker to global\nflake fix\nfix ut\nMerge branch 'hotfix/db_connections_issue' of bitbucket.org:levl/eros-classifier into dev\n\nApproved-by: Nadav Livni Approved-by: Gregory kovelman\n\n\nMerged in bugfix/EROS_65_update_device_hostname (pull request #80)\nNow updating the device name when matching a device\n\nNow updating the device name when matching a device\nMerge branch 'dev' of  into bugfix/EROS_65_update_device_hostname\n\nApproved-by: Gregory kovelman\n\n\nfix the lag time metric\n\n\nMerged in feature/metrics_lag_time (pull request #82)\nFeature/metrics lag time\n\nLink session id and devices\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nMove DB engine and sessionmaker to global\nflake fix\nfix ut\nMerge branch 'hotfix/db_connections_issue' of bitbucket.org:levl/eros-classifier into dev\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nfix the lag time metric\n\nApproved-by: Gregory kovelman\n\n\naws_region as variable\n\n\nMerged in new_datalake (pull request #83)\ns3 bucket name as env var\n\ns3 bucket name as env var\nfix test\nfix flake and test\n\nApproved-by: Shimon Goulkarov\n\n\ntypo fix\n\nsed fix\nfix import api\nfix session api merges\n\nMerged in DatArchiver-add-ability-to-pull-exact-namber-off-files-from-s3 (pull request #85)\nDatArchiver add ability to pull exact namber off files from s3\n\nDatArchiver: added ability to pull exact number off files from s3\nfix conflict\nMerge branch 'dev' into DatArchiver-add-ability-to-pull-exact-namber-off-files-from-s3\n\nApproved-by: Shimon Goulkarov\n\n\n"}
{"title": "MEROSP-2103 bug fix", "number": 890, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/890", "body": ""}
{"title": "remove healthcheck", "number": 891, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/891", "body": "Delete unnecessary code"}
{"comment": {"body": "please write a description", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/891/_/diff#comment-341784474"}}
{"comment": {"body": "I will try to quickly help with that based on Liat and Ron\u2019s correspondence:\n\n**Liat Ackerman**\u00a0\n\nI have a question regarding the code in classifier, inside the `infrastructure_healthcheck `directory.There is a lot of code duplication inside this directory, e.g. a redefinition of the class KafkaMsg.  \nIt seems this code is used in bitbucket pipeline for a procedure called 'healthcheck'.  \nThe question is what is the purpose of this procedure, and what is the API required from the classifier  \nin order to perform this procedure.  \nThe requested API should be separated from and blind to the implementation of said API.\u00a0We would like to supply this API in order to allow this code to be rewritten and keep its functionality.\n\n**Ron Cohen**\u00a0Hey, this code was used in initial integration steps with Cujo, to be able to test our pipelines \\+ infra without revealing some of our core code \\(back then\\) we wanted to create a dummy version of our classifier that uses the same infrastructure tools as the original classifier, So to be able to deliver Cujo this container we had to separate some code to \u201cclean\u201d env so we can containerize it \\(code duplication\\). the steps that BB pipelines uses is to build to push this container to Cujo\u2019s ECR. I think we can deprecate it..\n\nFrom back then no one maintained it as this service became irrelevant..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/891/_/diff#comment-341794448"}}
{"title": "Bugfix/MEROSP-2111 metric api inc not adding to", "number": 892, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/892", "body": "The inc() api should add to previous value the added value.\nThe current implementation just sets the value in inc() and thus all the metrics with inc() not working as expected."}
{"title": "Hotfix/new MEROSP-1996 ios 16 models", "number": 893, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/893", "body": "update models for ios 16\nignored dhcp new data fix\n\n\noriginal PR\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6180](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/6180){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/893/_/diff#comment-342360196"}}
{"title": "MEROSP-1740 support rpvr for data catalog", "number": 894, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/894", "body": ""}
{"title": "MEROSP-2001: align error log schema with ML2 data models definition", "number": 895, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/895", "body": "As part of Cujo LENS integration, we are required to align all the schema that are exported to the datalake (result_log, error_log, feature_log, decision_log).\nThis PR aligns the error_log according:\n{: data-inline-card='' } \nand the feature_log (aka datacatalog) according:\n{: data-inline-card='' }"}
{"title": "MEROSP-2083 telegraf client not able to", "number": 896, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/896", "body": "As part of the Cujo integration, we had to align to their metrics mechanism,\nthey use HTTP requests to deliver metrics. (their metrics endpoint is HTTPS proxy server)"}
{"title": "MEROSP-2076 cujo tag standard", "number": 897, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/897", "body": ""}
{"comment": {"body": "replace `source` with `./` everywhere", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/897/_/diff#comment-343378040"}}
{"comment": {"body": "nice work. told you it can be done. no need to put one-liners in Bitbucket yaml", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/897/_/diff#comment-343378132"}}
{"comment": {"body": "who is @{634e54561db4d2ebcf611e5a} ? the author of this PR?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/897/_/diff#comment-343378146"}}
{"comment": {"body": "Is there any advantage that we\u2019d get by using `./` ? The reason I used `source` because we want to include all exported variables to the current shell as next shell commands require them. AFAIK, `./` would execute the export command but the environment vars may not be available in the current shell for next commands to consume.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/897/_/diff#comment-343560605"}}
{"comment": {"body": "nice to e-meet you @{634e54561db4d2ebcf611e5a} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/897/_/diff#comment-343668863"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} so maybe use `export` before declaring the vars?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/897/_/diff#comment-343669269"}}
{"comment": {"body": "nice to meet you too @{6265307b185ac200692f9bd9} !", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/897/_/diff#comment-344107846"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} export is used in the file however, when we execute that, it runs in a child shell and the variable is accessible only in that shell. `source` pulls all exported variable to current shell thus making it available in the next commands", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/897/_/diff#comment-344108051"}}
{"title": "fixing pipeline - two steps are the same", "number": 898, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/898", "body": "in Makefile : docker_test_modules_ci and docker_test_classifier_ci were the same"}
{"title": "MEROSP-2147 connection event and result should not be dataclass", "number": 899, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/899", "body": "regression passed:\n{: data-inline-card='' }"}
{"title": "Feature/basic matching", "number": 9, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/9", "body": "Add matching module\n\nScheme is sliced from pilot scheme\nUsing ORM with SQLAlchemy\n\nIntegrate feature extraction, device type identification and matching into pipeline"}
{"comment": {"body": "this code intention was for flask/service health\\_check status, please remove\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/9/_/diff#comment-225208574"}}
{"comment": {"body": "we should add the features results to the session as well.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/9/_/diff#comment-225208897"}}
{"comment": {"body": "iKeja DHCP bypass code also required for DHCP device type", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/9/_/diff#comment-225209115"}}
{"comment": {"body": "@itai delete this one", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/9/_/diff#comment-225587412"}}
{"title": "Adding pipeline hit counter metric", "number": 90, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/90", "body": "Adding pipeline hit counter metric\n"}
{"title": "MEROSP-2167 convert string enums to int enums", "number": 900, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/900", "body": "\npassed regression:\n"}
{"title": "Bugfix - reduce expression complexity in user_agent_analyzer", "number": 901, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/901", "body": ""}
{"title": "MEROSP-2168 gather unit test coverage to one place", "number": 902, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/902", "body": "added another step to pipeline that gets the coverage from all unit test steps\n"}
{"comment": {"body": "can we fail this step if below 80%?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/902/_/diff#comment-343709297"}}
