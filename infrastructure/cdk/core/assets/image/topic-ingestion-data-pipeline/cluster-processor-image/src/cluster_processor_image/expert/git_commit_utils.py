import logging
from typing import List, Optional
import pandas as pd
from git import Repo, Commit

from dataclasses import dataclass, asdict


@dataclass
class CommitInfo:
    hash: str
    author: str
    email: str
    date: pd.Timestamp
    message: str
    insertions: Optional[int] = None
    deletions: Optional[int] = None
    file_path: Optional[str] = None  # Add a field to store the file path


class GitCommitUtils:
    def __init__(self, repo_dir: str):
        self.repo = Repo(repo_dir)

    def get_all_commits(self) -> pd.DataFrame:
        commits = list(self.repo.iter_commits())
        commit_info_list = self._extract_commit_info(commits)
        return pd.DataFrame([asdict(commit) for commit in commit_info_list])

    def get_file_commits(self, file_paths: List[str]) -> pd.DataFrame:
        commit_data = []
        for file_path in file_paths:
            commits = list(self.repo.iter_commits(paths=file_path))
            commit_data.extend(self._extract_commit_info(commits=commits, file_path=file_path))

        return pd.DataFrame([asdict(commit) for commit in commit_data])

    def _extract_commit_info(self, commits: List[Commit], file_path: Optional[str] = None) -> List[CommitInfo]:
        commit_data = []
        for commit in commits:
            for changed_file, stats in commit.stats.files.items():
                if not file_path or file_path in changed_file:
                    try:
                        commit_info = CommitInfo(
                            hash=commit.hexsha,
                            author=commit.author.name,
                            email=commit.author.email,
                            date=pd.Timestamp(commit.authored_datetime),
                            message=commit.message,
                            file_path=changed_file,
                            insertions=stats["insertions"],
                            deletions=stats["deletions"],
                        )
                        commit_data.append(commit_info)
                    except Exception as e:
                        # Log the error and continue with the next iteration
                        logging.error(f"Error processing commit {commit.hexsha} for file {changed_file}: {e}")

        return commit_data
