{"title": "fix typing scores", "number": 198, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/198", "body": ""}
{"title": "names refactor", "number": 199, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/199", "body": ""}
{"title": "Packet parser impl", "number": 2, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/2", "body": "Facing our first integration, this PR brings the code for our packets parsing.\nSome notes:\n\nThe file collector_interface.pydoes not belong to this PR. Its here until we figure out how to solve the importing problem in the best way possible. It will be removed from here once we do it.\nThere are three unitests files: test_ethernet_parser.py (To test specific protocols parsing), test_radiotap_parser.py (Self-explanatory) and test_session_parser.py (To test full sessions, in the structure described in collector_interface.py). We have both positive and negative unitests, and our fixes from PR-905, 985, 875, 943 and 729 are reproduced there. The packets are from real pcaps.\nMost of the files contain code from our current code base, with some adjustments and formatting. The brand new code can be found in packet_parser.py and ethernet_parser.py.\nCFR packets are not handled yet.\n\n\nWhats left?\n\nI really want to split some of the logic in radiotap_parser.py to separate files. Feel free to make some suggestions regarding it.\nRight now the main files are ethernet_parser.py, radiotap_parser.py, packet_parser.py and the empty cfr_parser.py. Maybe we need to separate them from the specific-protocols-parsing files (such as dhcp_parser.py, etc). Any thoughts?\nFigure out what to do with the flake8 plugin. Its mostly good but some of its requirements are quite edgy.\nAs can be seen in the third test in test_session_parser.py, parsing a real session takes us about 23 miliseconds, and thats without parsing the CFR packets. It needs to be much faster.\n\n"}
{"title": "Hotfix/ssdp user agent linux fix", "number": 20, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/20", "body": "extensions.json edited online with Bitbucket\nSSDP is Linux fix\n\n"}
{"title": "Bug fix/empty typing", "number": 200, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/200", "body": "logs\nchanges\ngix None bug and dhcp model score\ntyping improvements\nfix parsing existing device features\n\n"}
{"title": "typing_closings", "number": 201, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/201", "body": ""}
{"title": "Release/alpha l1 l7", "number": 202, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/202", "body": "Some types hints in radiotap_parser.py\nAdded cfr data to parsing.py file\nNow appending mocked data to CfrFields from parsing.py\nAdded cfo to features.py\nChanged cfr.type to UintField in the protobuf\nAdded cfo to the db_schema, might change the type later on\nAdded test placeholder\nAdded cfo_structs.py file, with all the needed classes and definitions for building the CFO model\nAdded a simple usage of CFO_OneClassClassifier_v3_1 just to see if we integrate with it well\nFlake fixes\nAdded scipy to requirements.txt\nAdded sklearn to requirements.txt\nCreated CFO directory under features_implementation\nNow calling CFO_OneClassClassifier_v3_1.train() in cfo.py\nNow returning the serialized cfo model\nStarted integrating the CFO component in the matchmaker\nChanged parameter name: session -> current_session\nCalculating cfo model if got 1 matching device from the bypass\nWIP incorporating the highest score mechanism with the cfo\nSimplified matchmaker decision making with the CFO\nflake fixes\nNone check\nDocumentation fixes\nFlake fixes\nChanged naming in CFO: EXACT -> ACTUAL\nAdded bonjour_uid parameter to filter_out_by_strong_identifiers() method, still need to complete the implementation\nNow unpacking the data in parse_cfr_packet()\nAdded documentation in cfo feature implementation\nSplit to two cfo models\nMissing feature name fix\nFlake fix\nFreezing sklearn version in requirements.txt\nNow classifying only against the estimated cfo feature\nNow classifying only with the cfo_estimated model\nUpdated the cfo classification method to return specific result values\nUpdated changes from research_master branch\nNow substracting 500 points from the score if got a rejection result from the cfo classifier\nFlake fixes\nNow filtering by os version\nAdded debug log to help the integration\nAdded bonjour uid as a strong negative filter\ncfo edit: perform_cfo_func() -> _perform_cfo_func()\nChanged cfo_val type in the pb to double. Added SLOPES value to L1DataTypes as another possible value\nUpdated cfo_structs.py with the new filtering classes and methods\nOutlierCFO typo fix. Fixed the packet_parser to treat the cfo_val as double instead of uint\nPerform online training only if the matched device is for sure from the true labeling\nAdded rssi_mean value. Added filter() calls\nNow calling deserialize_model() before online_training\nUpdated struct unpack types according to the real sent data\nfix flake8 logger import\nfix UT\nremove not relevant file\n\n"}
{"title": "Feature/l1 typing slopes", "number": 203, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/203", "body": "Copy slopes model and helper files from other repo \n\nAdd L1SlopesDeviceTyping module for typing extraction\n\nMay return multiple models per identification or none\n\n\n\nTODO: integration with agent, as in deserialization of data\n\n\n"}
{"comment": {"body": "add `L1SlopesDeviceTyping.load_model()` to make sure model is loaded\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/203/_/diff#comment-262696253"}}
{"comment": {"body": "Added", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/203/_/diff#comment-262698131"}}
{"title": "Feature/code cleanup", "number": 204, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/204", "body": "code refactor and cleanup\ncode refactor and cleanup\ncorrect skleran lib\ncleanup pytest\nupdate python versions\n\n"}
{"title": "Feature/slopes parsing", "number": 205, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/205", "body": "Parse\nCheck for none in slopes\n\n"}
{"comment": {"body": "@{5b41d9de10d57114135eca66} why do you want to commit one by one?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/205/_/diff#comment-263692943"}}
{"comment": {"body": "i didn\u2019t write this function. @{6085103f5797db006947d59a} ?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/205/_/diff#comment-263746887"}}
{"title": "levl streamz", "number": 206, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/206", "body": ""}
{"title": "Hotfix/sparrow timestamps", "number": 207, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/207", "body": "add event_id to the forwarder\nhandle event_id\nfix timestamps\nfix stream names\nfix timestamp bug\nignore cfr\n\n"}
{"title": "ua android match logic", "number": 208, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/208", "body": ""}
{"title": "Some CFO fixes", "number": 209, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/209", "body": "While we didnt finish integrating with the CFO component, there are some fixes and logs we need to add to our main branch."}
{"title": "Hotfix/data archiver logs", "number": 21, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/21", "body": "fixed topic\nerror handling + logs\n\n"}
{"title": "remove betterproto - features simple dataclass", "number": 210, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/210", "body": "Initial commit for removing betterproto:\n\nfeatures and parsed classes to simple dataclass\ncompile protobuf as regular build - need to verify how tools treat p2b imports\n\n"}
{"title": "CFO fixes", "number": 211, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/211", "body": "This PR adds some important fixes to our CFO flow.\n\nOn ongoing events, we mistakenly replaced the CFO models in the DB with None models\nThe output of the CFO filter() method was not used properly, and now it is\nThe CFO models threshold is now reduced to a lower bound\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim-buster", "number": 212, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/212", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.9.7-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 614  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "CFO additional fixes", "number": 213, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/213", "body": "Now, well filter out devices by strong identifiers only if those identifiers exist. In addition, we will not filter out devices by their vendor if their confidence level is not 100."}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.8.12-slim-buster", "number": 214, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/214", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.8.12-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 400  | Information Exposure SNYK-DEBIAN10-LIBGCRYPT20-1297893   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Feature/split sparrow build", "number": 215, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/215", "body": ""}
{"title": "Bugfix/MEROSP-29 duplicate model for linux", "number": 216, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/216", "body": "We had two main problems:\n\nWindows / Linux mismatch - Some of our Linux connections are identified as Windows OS devices. We filter them out and ignore them when we try to find a matching device, so we got a duplicate model. After reproducing it, it was discovered that we got netbios hostnames from the Linux device, and therefore identified it as a Windows device. This PR removes this strong identification for now.\nFiltering out Unknown' OS devices - for some of our Linux connections, we cant decide whats the OS and we leave it as Unknown. We later compare string-to-string with all the candidates and their OS name, and no matter what OS they have, it wont be equal to Unknown. This is now fixed in our matchmaker.\n\n"}
{"title": "CFO/Fixed vendor filtering", "number": 217, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/217", "body": "We wish to compare the vendor and filter out devices by it only if we are sure with confidence 100 that both session and candidate have the same vendor."}
{"title": "Now handling the case of a negative device score in select_device_with_highest_score()", "number": 218, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/218", "body": "If all the candidates have a negative score, none of them can win as the most matching device. In that case, select_device_with_highest_score returned None which caused us some trouble later on. \nWe need to make sure that if all candidates returned with a negative combined score, we need to treat it as a new Levl ID scenario.\nThis case was not handled and caused the bug described in the issue."}
{"title": "Improved some logs", "number": 219, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/219", "body": "Just small adjustments to make our debugging easier"}
{"title": "fixed logs", "number": 22, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/22", "body": "fixed logs\n\n"}
{"title": "Bugfix/MEROSP-172 CFO candiate with a reject result", "number": 220, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/220", "body": "As a continuation to a previous PR from today, David found another bug - the case where the true labeling returned 0 candidates, and the CFO evaluator returned exactly one candidate, but this candidate has a negative CFO result (outlier). In that case, we didnt calculate the entire score and just assumed we found a match.\nIn this PR, were ignoring candidates from the CFO evaluator if they got back with a negative result AND if the true labeling didnt score them as well."}
{"title": "Increased CFO model decision threshold to 200", "number": 221, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/221", "body": ""}
{"title": "Update slopes code", "number": 222, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/222", "body": "update sdk_fft.py\nreduce packets to 350\n\n"}
{"title": "remove debug message", "number": 223, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/223", "body": "remove debug message\n\n"}
{"title": "[Snyk] Fix for 2 vulnerabilities", "number": 224, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/224", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\nhvplot 0.7.2 requires holoviews, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  539/1000  Why? Has a fix available, CVSS 6.5  | Incorrect Access Control  SNYK-PYTHON-DASK-1767103 |  dask: 2021.6.0 - 2021.10.0  |  No  | No Known Exploit \n  |  654/1000  Why?* Has a fix available, CVSS 8.8  | Deserialization of Untrusted Data  SNYK-PYTHON-NETWORKX-1062709 |  networkx: 2.5.1 - 2.6  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the effected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Pytest mocking + CFO matching tests", "number": 225, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/225", "body": "This PR adds usage examples using mocks in pytest, which well also use as future reference. Those tests check the CFO matching procedure flow, according to the bugs that were opened in the last few days."}
{"title": "More unitests + Removing unused code", "number": 226, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/226", "body": "This PR adds tests to the following components:\n\nWPS_UID feature\nDevice ongoing / disconnected calls\nCFR packets parsing\n\nIn addition, some unused code that wasnt used is removed (it will also improve our coverage).\n"}
{"title": "Changed dhcpv6_duid_timestamp DB type from Integer to BigInteger", "number": 227, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/227", "body": "We got the following errors in the notification side: \npsycopg2.errors.NumericValueOutOfRange: integer out of range\nThe dhcpv6_duid_timestamp type in the DB was Integer, even though it needs to be BigInteger. This PR fixes it."}
{"title": "remove requests and HTTP notification usage", "number": 228, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/228", "body": ""}
{"title": "Features scores to csv", "number": 229, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/229", "body": "This PR removes the numeric scores we had in our code, regarding to our matching and identification features. From now on, well load the scores from a csv file in S3 and use them as regular afterwards.\nThe main change in the identification features can be seen in scoring_constants.py. After we load the file, we parse the scores and update each feature in the dataclass.\nThe main change in the matching features can be seen in matchmaker.py. After we load the file, we create a dictionary that maps each feature with its score and use it along the code. Its also an improvement from the old way, where we re-created that dictionary for each device connection (in select_device_with_highest_score).\n"}
{"title": "add logs and enable logging info", "number": 23, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/23", "body": "SSDP is Linux fix\nadd logs and enable logging info\n\n"}
{"comment": {"body": "are we sure about the format?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/23/_/diff#comment-226601022"}}
{"comment": {"body": "You can suggest another", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/23/_/diff#comment-226621416"}}
{"title": "Moved all ethernet parsers under ethernet directory", "number": 230, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/230", "body": ""}
{"title": "Created a multiclass_decision() method to handle the case of multiple candidates", "number": 231, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/231", "body": ""}
{"title": "Feature/Always calculate cfo model", "number": 232, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/232", "body": ""}
{"title": "Outsourcing devices scores - NN", "number": 233, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/233", "body": "This PR changes the way we calculate the score of each device when doing the matchmaking procedure. Instead of calculating the score in our code, we outsource it to a neural network, and that way we hide the true logic.\nThe main things are:\n\nLoading the keras model from S3\nLoading the features-to-indexes json file from S3\nBuilding the features array and sending it to the NN model\nGetting the scoring according to the NN decision\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.9.7-slim-buster", "number": 234, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/234", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.9.7-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-DEBIAN10-LIBGCRYPT20-1582894   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Feature/slopes indentification", "number": 235, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/235", "body": "Add l1 slopes identification feature\nupdate score for tests\nupdate proto schema\n\n"}
{"title": "Device typing/tests docs", "number": 236, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/236", "body": "captive portal tests\ndocs\n\n"}
{"title": "Renamed devices_find_bypass() to devices_query()", "number": 237, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/237", "body": ""}
{"title": "Add l2 caps matching", "number": 238, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/238", "body": "add l2 caps feature implementation\nadd feature usage in matchmaker, db\n\nno tests yet"}
{"title": "Now always using multiclass decision, since there are two features with a score of zero", "number": 239, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/239", "body": "Good job @{6085103f5797db006947d59a} with investigating this bug!\nBug scenario:\nYesterday, we added two features with a score of zero. \nIn the case of one of them matched, we return a device as a matched device from the query_builder. Later on, we dont check the score of that device, because we assume that if the query_builder returned exactly one device, its a true label decision and we can use it.\nIt caused us bugs with matching devices that should NOT be matched.\n\nSolution:\nFrom now on, well always use the multiclass decision logic. Since the devices score is always calculated there, itll catch this case and well create a new device for the session."}
{"title": "DHCP ACK parsing", "number": 24, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/24", "body": "Now parsing DHCP ACK messages as well, mistakenly forgotten.\nIn addition, now looking at the src port when choosing the suitable parsing method."}
{"title": "l2_caps fixes", "number": 240, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/240", "body": "This PR fixes the case where we counted empty l2_caps as a matched feature.\nIn addition, it fixes a copy-paste thing in the matchmaker."}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to rc-slim-buster", "number": 241, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/241", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:rc-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-DEBIAN10-LIBGCRYPT20-1582894   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Minor logs changes", "number": 242, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/242", "body": ""}
{"title": "Cfo slopes unitest", "number": 243, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/243", "body": ""}
{"title": "Bugfix/CFO with empty filtered data", "number": 244, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/244", "body": "From now on, well not attempt to calculate or classify against CFO model if the filtered data is empty.\nThis fixes a bug @{6085103f5797db006947d59a} saw on Thursday, where we had the following exception:\nFile \"/code/matching/src/matchmaker.py\", line 573, in match\n    cfo_model_evalautor.train_cfo()\n  File \"/code/matching/src/cfo/cfo.py\", line 105, in train_cfo\n    self._perform_cfo_func(self.one_class_classifier.train)\n  File \"/code/matching/src/cfo/cfo.py\", line 130, in _perform_cfo_func\n    cfo_func(filtered_temp_and_channels_and_rssi, filtered_cfos)\n  File \"/code/matching/src/cfo/cfo_structs.py\", line 566, in train\n    assert len(X)  0\nIn addition of checking if we got any CFO data, we need to see that we got left with any data after calling the estimater filter() method.\nThis fix was done after talking to research team, that confirmed that the filter() method might return empty data in some cases."}
{"title": "changed log level", "number": 245, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/245", "body": ""}
{"title": "CFO model processing log", "number": 246, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/246", "body": "We have a mysterious error in the pipeline. It is somewhere in CFO, in some specific cases which we cant put our finger on. Added 2 logs in filtering and classification to help us pinpoint the error."}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0rc1-slim-buster", "number": 247, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/247", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.10.0rc1-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-DEBIAN10-LIBGCRYPT20-1582894   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Feature/CFO scoring to zero", "number": 248, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/248", "body": "Updated the S3 paths of the keras model and features indexes to the new configuration of zero score to the CFO feature. Until further changes, the CFO will not be considered as a factor when attempting to match between devices. We will continue to learn the results and its behaviour from the logs, but thats it for now.\n"}
{"title": "Release/alpha l1 l7 to dev", "number": 249, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/249", "body": ""}
{"title": "Features/much more matching", "number": 25, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/25", "body": "Snapshoting progress.\nFollow up on \n\nAdd DB schema module with utils for device table\nMore complete matching algorithm with matching log\nDHCPv6 Windows matching flow\n\n"}
{"title": "Added logs with the specific time we spend in matching", "number": 250, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/250", "body": "Those logs might help us in the future when well have more devices connected simultaneity. And its more consistent with the same logs that we have in the parsing procedure."}
{"title": "Bugfix/cfo is now zero scored", "number": 251, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/251", "body": "Updated the keras model with the correct version, where the CFO score is really zero. In addition, updated the unitests with the new logic\n"}
{"title": "L2 new model code", "number": 252, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/252", "body": "added white list for matching\nmodel_os not relevant\n\n"}
{"comment": {"body": "why not in utils?why not in utils?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/252/_/diff#comment-268353556"}}
{"comment": {"body": "Let\u2019s add UT performance measurements for models and pipelines..\n\nWe can set a baseline and make sure not to make regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/252/_/diff#comment-268353630"}}
{"comment": {"body": "Can we use a dataclass instead of dictionaries with raw strings as the keys? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/252/_/diff#comment-268490592"}}
{"title": "Bugfix/MEROSP-237 traceback query builder", "number": 253, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/253", "body": "Fixing the specific OS search in the os field from the db. Since we save a tuple (and not a string), we need to access the os in the right place\n"}
{"title": "Feature/performance matchmaker", "number": 254, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/254", "body": "After our hand-off, we can go back and replace some code elements that affect on our performance badly.\nSome first steps that will help us for sure:\n\nEvaluating the CFO values only if needed\nNot using the NN model when calculating the score of each candidate device\n\n"}
{"title": "[Snyk] Security upgrade python from 3.8.8-slim-buster to 3.10.0-slim-buster", "number": 255, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/255", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to python:3.10.0-slim-buster, as this image has only 72 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | Use of a Broken or Risky Cryptographic Algorithm SNYK-DEBIAN10-LIBGCRYPT20-1582894   | No Known Exploit   |\n|    | 500  | Out-of-bounds Write SNYK-DEBIAN10-LZ4-1277601   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 714  | Buffer Overflow SNYK-DEBIAN10-OPENSSL-1569403   | No Known Exploit   |\n|    | 614  | Out-of-bounds Read SNYK-DEBIAN10-OPENSSL-1569406   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "sparrow pipelines", "number": 256, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/256", "body": ""}
{"title": "[Snyk] Security upgrade numpy from 1.20.3 to 1.22.0rc1", "number": 257, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/257", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n | Buffer Overflow  SNYK-PYTHON-NUMPY-2321969 |  numpy: 1.20.3 - 1.22.0rc1  |  No  | Proof of Concept \n | Denial of Service (DoS)  SNYK-PYTHON-NUMPY-2321970 |  numpy: 1.20.3 - 1.22.0rc1  |  No  | Proof of Concept \nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the effected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "[Snyk] Security upgrade numpy from 1.20.3 to 1.22.0rc1", "number": 258, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/258", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\npanel 0.9.3 has requirement bokeh>=2.0.0, but you have bokeh 1.4.0.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  578/1000  Why? Proof of Concept exploit, Recently disclosed, Has a fix available, CVSS 3.7  | Buffer Overflow  SNYK-PYTHON-NUMPY-2321969 |  numpy: 1.20.3 - 1.22.0rc1  |  No  | Proof of Concept \n  |  578/1000  Why?* Proof of Concept exploit, Recently disclosed, Has a fix available, CVSS 3.7  | Denial of Service (DoS)  SNYK-PYTHON-NUMPY-2321970 |  numpy: 1.20.3 - 1.22.0rc1  |  No  | Proof of Concept \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the effected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Feature/master dev merges prep", "number": 259, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/259", "body": "try to align master with dev to resolve the conflicts before merging dev to master..\nseems there are mistakes here"}
{"title": "Features/very much more matching", "number": 26, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/26", "body": "Add more matching features with tests:\n\n\nHostname\n\nAndroid\nWindows\n\n\n\nDHCPv4 requested IP\n\n\n\n\nAdded metrics to matching (but full integration is still WIP)\n\n\n"}
{"comment": {"body": "let\u2019s move to another location, as also sub modules can use the same even can be under metrics folder in root..\n\nwhy using print?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/26/_/diff#comment-227015828"}}
{"title": "graceful exit bug fix", "number": 260, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/260", "body": "graceful exit bug fix"}
{"title": "Dev", "number": 261, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/261", "body": ""}
{"title": "MEROSP-235 logs, changed history log build, added session id to matchresult", "number": 262, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/262", "body": ""}
{"title": "packaging api", "number": 263, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/263", "body": ""}
{"title": "MEROSP-265 Fixed query_builder to NOT consider empty dhcp requested IPs as a match", "number": 264, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/264", "body": "We had a FP caused by comparing empty strings and consider it as a match.\ndevice_dhcpv4_handshake Dhcpv4Handshake(given_ip='', requested_ip='')\n...\nMatched features: [['match_dhcpv4_handshake_requested_ip']]\nThis PR fixes that bug, to make sure we compare only real data."}
{"title": "Merged in api_package (pull request #263)", "number": 265, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/265", "body": "packaging api\n\npackaging api\npack parser package, api package\nMerge branch 'dev' into api_package\nfix package\ntests\ntests fix\nfix tests\ndebug tests\ndebug tests\ntests\n\nApproved-by: Shimon Goulkarov"}
{"title": "fix l2 not returning os", "number": 266, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/266", "body": ""}
{"title": "Msk", "number": 267, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/267", "body": "!! NOT FOR MERGE YET !!"}
{"title": "Bugfix/MEROSP-271 missing hostname and ip", "number": 268, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/268", "body": "In MEROSP-271, we had an interesting problem of some connections that didnt had any ip or hostname. That problem was reproduced from time to time when the mac randomization script was used on one of our Linux lab laptops.\nWhen looking more closely, it turned out a more serious problem occurred - every time we had this bug, we sent two connections - one with all the correct data (including ip and hostname), and one connection without them.\nWhen looking at the radiotap packets, we can see that we did had an interesting case of association  disassociation  re-association every time this bug reproduced:\n\nSo, technically we did had two connection attempts, but we should consider only one of them as a valid connection. The first connection didnt contain any data (and any dhcp packets in general) since this connection was never actually complete. Only the second connection was the valid one.\nIn order to solve it, we can (and we might will) add a mechanism in the agent side that would enforce that each (dynamic IP) connection must contain DHCP packets  in order it to be sent. For now (since this bug rarely reproduces) we can add a fix in the classifier side. In fact, having this fix in the classifier side is not a bad idea even if we will add a fix in the agent side as well in the future.\nThis PR adds this fix, and makes sure we have at least one DHCP packet in the session before we attempt to create a connection from it."}
{"title": "Feature/update l2 typing model default", "number": 269, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/269", "body": "Adding latest L2 typing model to UT\nmark latest model\n\n"}
{"title": "Feature/history devices", "number": 27, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/27", "body": "raise to 50msec\nSSDP is Linux fix\nlocal changes\nhistory devices\nadd history devices\n\n"}
{"comment": {"body": "are these comments required?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/27/_/diff#comment-227134863"}}
{"comment": {"body": "This schema is not going to last for long. Why not dynamically load from `Device` schema?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/27/_/diff#comment-227135093"}}
{"comment": {"body": "This print is redundant and will happen for all tests", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/27/_/diff#comment-227135249"}}
{"comment": {"body": "Use dynamic copying of fields", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/27/_/diff#comment-227135386"}}
{"title": "control charts version", "number": 270, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/270", "body": ""}
{"title": "Bugfix/Checking that we have the minimum DHCP data ONLY if we are in classification pipeline", "number": 271, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/271", "body": "(In order to not drop the ongoing and disconnection events from the main pipeline)"}
{"title": "[Snyk] Security upgrade tensorflow/tensorflow from 2.7.0 to 2.8.0rc0", "number": 272, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/272", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to tensorflow/tensorflow:2.8.0rc0, as this image has only 32 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 321  | Improper Check for Dropped Privileges SNYK-UBUNTU2004-BASH-581100   | Mature   |\n|    | 514  | CVE-2021-3737 SNYK-UBUNTU2004-PYTHON38-1932959   | No Known Exploit   |\n|    | 514  | CVE-2021-3737 SNYK-UBUNTU2004-PYTHON38-1932959   | No Known Exploit   |\n|    | 514  | CVE-2021-3737 SNYK-UBUNTU2004-PYTHON38-1932959   | No Known Exploit   |\n|    | 300  | Out-of-bounds Read SNYK-UBUNTU2004-SQLITE3-581593   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Removed any usage of our zero-scored features for now", "number": 273, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/273", "body": "After the last performance checks that @{5e2ee80dad92310e881b7a22} did, and the vast improvement of session parsing to about ~122msec, we can merge those changes to dev and not waste time on zero-scored features for now. We will return them when theyll have an impact on our decision making, for now they are taking crucial time."}
{"title": "[Snyk] Security upgrade dnslib from 0.9.16 to 0.9.19", "number": 274, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/274", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  661/1000  Why?* Recently disclosed, Has a fix available, CVSS 7.5  | Improper Authentication  SNYK-PYTHON-DNSLIB-2331913 |  dnslib: 0.9.16 - 0.9.19  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the effected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Dynamodb", "number": 275, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/275", "body": "dynamodb init\nkafka address\nfix test\nadded some memory\nmemory games\nmem\nmem2\n1\n[skip ci] syntax\nflake\nremoving redis\nengine updates\nrp id change\ndynamo tests\nfix test ttl\nchanges\nchanges\n\n"}
{"title": "log levels reduction", "number": 276, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/276", "body": ""}
{"title": "[Snyk] Security upgrade dnslib from 0.9.16 to 0.9.19", "number": 277, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/277", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n | Improper Authentication  SNYK-PYTHON-DNSLIB-2331913 |  dnslib: 0.9.16 - 0.9.19  |  No  | No Known Exploit \nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the effected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Hotfix/snyk alert dnslib", "number": 278, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/278", "body": "log levels reduction\nupgrade dnslib - fix in security issues\n\n"}
{"title": "DYNAMO DB", "number": 279, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/279", "body": "test msk over ssl\nchange kafka address\nadd kafka config flag\nadd kafka config flag\nchange to test env kafka address\nchanged certs location\nchanged bootstarp server path\nchanged bootstarp server path\nchanged kafka settings\nfix certs path\nfix brokers path\nchange msk brokers path\nmake tls connection to kafka as default\nuse msk flag\nfix imports\nadd new charts\nremove s3 prefix from data archiver\nfix import path\nfix sparrow chart\ngetting kafka from one place\nadd kafka logs\nfix string\nremove kafka logs\nreport portal\ndns kafka\nfix script\nhelm new value, added api version to kafka\nadded kafka logs\nremove db_exporter and sparrow_fwd\n\nMerged in dynamodb (pull request #275)\nDynamodb\n\nremoving redis\nengine updates\nrp id change\ndynamo tests\nfix test ttl\nchanges\nchanges\nMerge branch 'msk' into dynamodb\npr changes\nfix env\n\nApproved-by: Itai Zolberg\n\n\n"}
{"title": "Fix DB create", "number": 28, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/28", "body": ""}
{"title": "Feature/MEROSP-386 adding kpis", "number": 280, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/280", "body": "This PR includes the additonal KPIs well add to our system as we grow towards the v1.0 requirements.\nThere are three types of KPIs we wish to add:\n\nSystem operational KPIs, which mostly are already implemented\nData Health KPIs, which will continue to grow as their design & final definitions are completed here.\nErrors handling KPIs, which will subject our errors to three types and send them, labeled, to our metrics center.\n\nThe current metrics are designed to work as they are in Grafana. Once we move to CloudWatch, minor changes will be done, like replacing the unsupported Histogram type.\n"}
{"comment": {"body": "We should add the string reason for the data health as well in the decision log..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/280/_/diff#comment-276104481"}}
{"title": "Feature/telegraf to cloudwatch", "number": 281, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/281", "body": "WIP\nworking version\nclass\n\n"}
{"title": "Stabilization/sparrow 0 8", "number": 282, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/282", "body": "installation\nFROM tensorflow/tensorflow:2.7.0\ninstall local packages\n\n"}
{"title": "Hotfix/sparrow footprint", "number": 283, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/283", "body": "\n\ncleanup /tmp and reduce memory usage\nreduce logging\n\n"}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} please make sure this passes the CI and merge. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/283/_/diff#comment-276673082"}}
{"title": "Feature/KPIs - Added another scenario of getting a 'C' grade", "number": 284, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/284", "body": "Added another scenario of getting a 'C' grade in the data health measurement.\nAdded some documentation\n\n"}
{"title": "[Snyk] Security upgrade tensorflow/tensorflow from 2.7.0 to 2.8.0rc1", "number": 285, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/285", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to tensorflow/tensorflow:2.8.0rc1, as this image has only 46 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 371  | Buffer Overflow SNYK-UBUNTU2004-GLIBC-2356852   | No Known Exploit   |\n|    | 371  | CVE-2021-3999 SNYK-UBUNTU2004-GLIBC-2359259   | No Known Exploit   |\n|    | 514  | CVE-2021-3737 SNYK-UBUNTU2004-PYTHON38-1932959   | No Known Exploit   |\n|    | 371  | CVE-2021-3997 SNYK-UBUNTU2004-SYSTEMD-2332040   | No Known Exploit   |\n|    | 371  | CVE-2021-3997 SNYK-UBUNTU2004-SYSTEMD-2332040   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Export api", "number": 286, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/286", "body": "Add base schemas\nClassifier->egestion adapter fills messages with data.\nSend on pipelines sink to legacy result topic + 2 new topics: result + log\n\n"}
{"comment": {"body": "@{5dbeb866c424110de52552cc} let\u2019s finalize this PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/286/_/diff#comment-276672976"}}
{"title": "[Snyk] Security upgrade tensorflow/tensorflow from 2.7.0 to 2.8.0", "number": 287, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/287", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to tensorflow/tensorflow:2.8.0, as this image has only 48 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 514  | CVE-2021-3737 SNYK-UBUNTU2004-PYTHON38-1932959   | No Known Exploit   |\n|    | 371  | CVE-2021-3996 SNYK-UBUNTU2004-UTILLINUX-2387723   | No Known Exploit   |\n|    | 371  | CVE-2021-3996 SNYK-UBUNTU2004-UTILLINUX-2387723   | No Known Exploit   |\n|    | 371  | CVE-2021-3995 SNYK-UBUNTU2004-UTILLINUX-2387728   | No Known Exploit   |\n|    | 371  | CVE-2021-3995 SNYK-UBUNTU2004-UTILLINUX-2387728   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "[Snyk] Security upgrade tensorflow/tensorflow from 2.7.0 to 2.7.1", "number": 288, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/288", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to tensorflow/tensorflow:2.7.1, as this image has only 49 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 467  | Integer Overflow or Wraparound SNYK-UBUNTU2004-EXPAT-2387546   | No Known Exploit   |\n|    | 467  | Integer Overflow or Wraparound SNYK-UBUNTU2004-EXPAT-2387546   | No Known Exploit   |\n|    | 371  | Buffer Overflow SNYK-UBUNTU2004-GLIBC-2356852   | No Known Exploit   |\n|    | 371  | CVE-2021-3999 SNYK-UBUNTU2004-GLIBC-2359259   | No Known Exploit   |\n|    | 514  | CVE-2021-3737 SNYK-UBUNTU2004-PYTHON38-1932959   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Bugfix/MEROSP-453 traceback in notification", "number": 289, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/289", "body": "This PR fixes a types problem in the db_schema that caused an exception for data_health and data_health_criteria fields"}
{"title": "Features/very much more matching", "number": 29, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/29", "body": "don't use metrics\nRemove metrics\n\n"}
{"title": "no more pg", "number": 290, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/290", "body": ""}
{"title": "Feature/MEROSP-449 quic user agent typing usage", "number": 291, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/291", "body": "The QUIC network protocol packets contain user agent data, which acts the same as HTTP user agent data. By capturing those packets and processing them in our device type identification, well be able to have a more accurate decision making.\nNote that processing the user agent data is the same as processing the HTTP user agent data. In the end, we might even get the same result from both of them.\nIn addition, the QUIC user-agents are now processed in Sparrow as well, in the same columns as the HTTP user-agents are stored and parsed."}
{"title": "Fixed handling of None messages in format_decision", "number": 292, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/292", "body": ""}
{"title": "Send results logs on disconnections", "number": 293, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/293", "body": "sending result and log messages on disconnection\n\n"}
{"title": "support disconnection source event in schema", "number": 294, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/294", "body": ""}
{"title": "Feature/continous testing back", "number": 295, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/295", "body": "log levels reduction\nupgrade dnslib - fix in security issues\nrun smoke tests on each PR by default\n\n"}
{"comment": {"body": "I\u2019m a big advocate for the move but as demonstrated by @{5dbeb866c424110de52552cc} , unsurprisingly, this unmaintained step doesn\u2019t work as expected now. Would love to sit with @{5dbeb866c424110de52552cc} / @{6085103f5797db006947d59a}  / @{5fd5d5149edf2800759cc96d} to make it work as intended, cause there are some small dilemmas here and there to consider.  \nI recommend not merging it for now as it will only confuse your devs to make them think they run automation suite when they\u2019re actually not.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/295/_/diff#comment-277924751"}}
{"comment": {"body": "Sanity for sure not covers many interesting test cases, that will be added. But it\u2019s stable and for now it will make full integration with automation and verify we don\u2019t missed any dead on arrival bugs, for example, changing api and not covering it in automation and etc.. as soon as we have more interesting test cases they will be added automatically under sanity\u2026\n\n![](https://bitbucket.org/repo/o5KReBa/images/3554220673-Screen%20Shot%202022-02-07%20at%2023.45.26.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/295/_/diff#comment-277931249"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Do you know why this is the display in the pipeline?  \n\n![](https://bitbucket.org/repo/o5KReBa/images/137714593-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/295/_/diff#comment-278033873"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Honestly I don\u2019t. This is what we wonderred about yesterday right?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/295/_/diff#comment-278034722"}}
{"comment": {"body": "Yeah. Tagged Shimon, maybe he knows..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/295/_/diff#comment-278035045"}}
{"comment": {"body": "It shows the UT passed as well.. will check why later on.. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/295/_/diff#comment-278037749"}}
{"title": "Add export dir to API package", "number": 296, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/296", "body": "__init__.py was missing"}
{"title": "Add source event id to result message", "number": 297, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/297", "body": ""}
{"title": "Feature/improve radiotap parsing", "number": 298, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/298", "body": "This PR changes the way we store our parsed RadioTap packets. Instead of storing all of them together under one general RadioTap structure, we store them separately by their type (kinda like we do with storing the DHCP request & DHCP ACK separately).\nBy doing so, we can iterate them more quickly.\nIn addition, I tried to remove our looping iterations as much as possible, even after doing this separation. For example, instead of using the built-in Python filter to get the first element that fulfils a condition, I preferred to changed it to the good old for loop that stops when it finds the desired element.\nNote that while we parse all RadioTap packets, we now store only the assoc & probe requests. If in the future well want to process more types of RadioTap packets in our feature extraction, well just need to update our inner parsing.py file with more structs.\nThis branch was not yet checked in terms of performance improvement, but Im opening this PR anyway to keep it in our mind."}
{"comment": {"body": "Is it possible to make them inherit from the same general Fields class?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/298/_/diff#comment-278780504"}}
{"comment": {"body": "I\u2019ll check :\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/298/_/diff#comment-278780780"}}
{"comment": {"body": "Great initiative, another small step towards better code\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/298/_/diff#comment-278842291"}}
{"comment": {"body": "why not a specific model?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/298/_/diff#comment-281452738"}}
{"title": "[Snyk] Security upgrade tensorflow/tensorflow from 2.7.0 to latest", "number": 299, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/299", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to tensorflow/tensorflow:latest, as this image has only 48 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 467  | Integer Overflow or Wraparound SNYK-UBUNTU2004-EXPAT-2387546   | No Known Exploit   |\n|    | 467  | Integer Overflow or Wraparound SNYK-UBUNTU2004-EXPAT-2387546   | No Known Exploit   |\n|    | 514  | CVE-2021-3737 SNYK-UBUNTU2004-PYTHON38-1932959   | No Known Exploit   |\n|    | 371  | CVE-2021-3996 SNYK-UBUNTU2004-UTILLINUX-2387723   | No Known Exploit   |\n|    | 371  | CVE-2021-3995 SNYK-UBUNTU2004-UTILLINUX-2387728   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Feature/feature extraction HDL", "number": 3, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3", "body": "Interface definition for feature_extraction and device_type_identification modules\n\nWith example for feature implementation with DHCPv6 with dummy example\n\n\n\n"}
{"comment": {"body": "Relevant here as well: [https://bitbucket.org/levl/eros-classifier/pull-requests/4?atlOrigin=eyJpIjoiMWRiZjlmZjhkYmE3NDg0Mzk3NWI3ODZhZjczNGQyODQiLCJwIjoiYmItY2hhdHMtaW50ZWdyYXRpb24ifQ#comment-224039365](https://bitbucket.org/levl/eros-classifier/pull-requests/4?atlOrigin=eyJpIjoiMWRiZjlmZjhkYmE3NDg0Mzk3NWI3ODZhZjczNGQyODQiLCJwIjoiYmItY2hhdHMtaW50ZWdyYXRpb24ifQ#comment-224039365){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224056061"}}
{"comment": {"body": "Renamed from `src` to `features`. I hope it\u2019ll help", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224090634"}}
{"comment": {"body": "Eventually fixed with `conftest.py` in `features` directory", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224273619"}}
{"comment": {"body": "this is required?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224283787"}}
{"comment": {"body": "yes, for vscode autocompletion", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224284260"}}
{"comment": {"body": "why we have features/features/\u2026 folders?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224284309"}}
{"comment": {"body": "What do you mean? that was the plan. `features` contains the package metadata and `features/features` is the package itself.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224285035"}}
{"comment": {"body": "the features not supposed to be explicit in the feature\\_extraction.. it should get he list of features to extract..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224285413"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} the `DEVICE_FINGERPRINT_URL` `CONFIGURATION_API_URL` not the pythonpath", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224285556"}}
{"comment": {"body": "Then who\u2019s calling `FeatureExtraction` with the list of features? It should definitely be embedded inside the package", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224285781"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} so the device\\_type\\_identification shouldn\u2019t be part of features and will use the package\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224285966"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Oh. I don\u2019t know. Maybe @{5fd5d5149edf2800759cc96d} can elaborate", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224286091"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} device\\_type\\_identification creates features\\_extraction and sets the features it wants to extract..\n\nBypass will do the same.. and so on..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224286868"}}
{"comment": {"body": "@{5f82bf320756940075db755e} What do you propose at this point? What folder and package structure?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/3/_/diff#comment-224289297"}}
{"title": "Create DeviceHistory if not exist", "number": 30, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/30", "body": ""}
{"title": "Feature/my msk connect", "number": 300, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/300", "body": "add base schemas\nupdate schemas and fill with data\nfix tests and flake\nfix imports\n4 characters flake error\negestion -> export\nWIP\n\n"}
{"title": "Feature/identification level", "number": 301, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/301", "body": "This PR adds an indicator of our decision making in the matchmaker, as an effort of improving our ways of documenting, labeling and investigating our system better when we reach larger scales.\nThe indicator itself is quite simple, and relies on the amount and type of features we used to take our decision. In case of a new device, we based our decision by all of the features we extracted. In case of a matching device, we based our decision by all the features that actually matched. In both cases, we wish to store the reason for our decision.\n\n**\nNote - I still need to add a unitest, this PR is open for now since Im OOO in Sunday"}
{"comment": {"body": "Where are the values definitions? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/301/_/diff#comment-279474140"}}
{"comment": {"body": "why we need to iterate twice? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/301/_/diff#comment-279474237"}}
{"comment": {"body": "Fixed ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/301/_/diff#comment-279746759"}}
{"comment": {"body": "They were pre-added by Tamir in `classification_details.py` :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/301/_/diff#comment-279746918"}}
{"title": "Handlers multi platform features", "number": 302, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/302", "body": "Multi-platform features in handlers. Extract platform info and add as part of the features.\n"}
{"comment": {"body": "@{5f82bf320756940075db755e} @{6085103f5797db006947d59a} This can be deleted, right?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/302/_/diff#comment-294831060"}}
{"comment": {"body": "What\u2019s the status of this PR?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/302/_/diff#comment-295179440"}}
{"comment": {"body": "That\u2019s why I asked @{5f82bf320756940075db755e} and @{6085103f5797db006947d59a} - this is no longer needed, right? Nadav replaced all of that?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/302/_/diff#comment-295934720"}}
{"title": "L2 typing use new models", "number": 303, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/303", "body": "Read both default and multiplatform models. The currently used models are specified ones, manually created based on current v0 model. The multiplatform part in the multiplatform model is the presence of the platform+config columns, and some added rows with a 2nd platform, used for testing for now.\nPartition model by platform upon reading\nUse platform info in model lookup\n\n"}
{"comment": {"body": "Do we need separate default model file? or just default model platform type?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/303/_/diff#comment-280230592"}}
{"comment": {"body": "We don\u2019t have to separate. But the default model will be created in a different manner than the multiplatform model, it will have different versions etc. So it is better to have this separation.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/303/_/diff#comment-280543961"}}
{"title": "hot fix", "number": 304, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/304", "body": ""}
{"title": "Hotfix/Removed db package installation from the sparrow dockerfile", "number": 305, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/305", "body": "This package was removed since its no longer exists. We need this fix to make sure our pipelines will pass without problems :)"}
{"title": "Hotfix/fix sparrow build image", "number": 306, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/306", "body": "Removed the deployment to eros-dev cluster, since its no longer exists"}
{"title": "Removed unnecessery prints of the device intelligence log and result", "number": 307, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/307", "body": ""}
{"title": "refactor helm charts", "number": 308, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/308", "body": ""}
{"title": "MEROSP-464 Changed the ssid name to be optional, since it's not yet implemented in the agent-side", "number": 309, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/309", "body": "This PR solves the bug that occurs when the avro schema finds a None ssid in the ipv4_leases dictionary.  This component is not yet implemented in the agent-side (we send an empty list from the agent side), but until now it didnt bother us since we didnt enforce the ssid to actually be valid. The avro schema does look at the ssid string values and required them to exist. Until we finish this implementation in the agent-side, we can change this value to be optional and avoid this crash."}
{"comment": {"body": "you will need to fix this file a bit to fit the data structure after flatten,\n\nmerge dev in and you will see the changes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/309/_/diff#comment-280491911"}}
{"comment": {"body": "\u2018n/a\u2019 is better", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/309/_/diff#comment-280535235"}}
{"title": "Feature/simple metric e2e", "number": 31, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/31", "body": "implement simple metric (packet_parser) end 2 end\n\n"}
{"title": "MEROSP-471 Fixing handling of a None user agent in quic_user_agent.py", "number": 310, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/310", "body": ""}
{"title": "Fearure/directed global probe feature", "number": 311, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/311", "body": "initial commit\ninitial commit\nchanges\n\n"}
{"title": "MEROSP-463 Replacing NaN values in the csv to empty strings", "number": 312, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/312", "body": "In MEROSP-463, we noticed a bug where we failed to parse our device typing result in the following case:\nmodel_name        model_vendor model_os\nChromecast Ultra       NaN      NaN\nChromecast v2          NaN      NaN\nNaN values cannot be parsed in the same way we parse the rest of our strings, and the most simple solution would be to replace those values with empty strings in the csv. Note that this action happens only once, when we start of system and load the csv model, so it wont affect our perofrmance."}
{"title": "Feature/pilot improvments", "number": 313, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/313", "body": "Merged in fearure/directed_global_probe_feature (pull request #311)\nFearure/directed global probe feature\n\ninitial commit\ninitial commit\nchanges\n\n\n\nadded tag list\n\nchanges\n\n"}
{"title": "Bugfix/MEROSP-472 quic traceback", "number": 314, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/314", "body": "Added some safety enforcement, in which we wont access areas outside of our packet"}
{"title": "logs changes", "number": 315, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/315", "body": ""}
{"comment": {"body": "the downside that if we have exception, we won\u2019t have this counted, we should add in both", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/315/_/diff#comment-281338645"}}
{"comment": {"body": "we will miss only the `step_execution_counter` ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/315/_/diff#comment-281338714"}}
{"comment": {"body": "@{6085103f5797db006947d59a} yes, let\u2019s add it explicitly in exception block as well", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/315/_/diff#comment-281338726"}}
{"comment": {"body": "@{5f82bf320756940075db755e} ok", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/315/_/diff#comment-281338743"}}
{"title": "Leftover old models paths", "number": 316, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/316", "body": "update leftover models' paths\n\n"}
{"title": "nex mac vendor csv", "number": 317, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/317", "body": ""}
{"comment": {"body": "@{6085103f5797db006947d59a} Do we know if it has better performance \\(algo\\) than FB?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/317/_/diff#comment-281543248"}}
{"title": "Fix local setting for VSCode users", "number": 318, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/318", "body": "fix local configuration and added env-classifier\nLOCAL_CLASSIFIER confiugration\nfix local_classifier config\n\n"}
{"title": "Align files and add error to decision log", "number": 319, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319", "body": "This PR adds the erros to decision log and adding another separate error log with separate topic for catching errors in the system\nWhile working on the changes, aligned tree structure and enhanced the data health counters"}
{"comment": {"body": "Why \u2018or\u2019 was changed to \u2018and\u2019?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282128139"}}
{"comment": {"body": "This is a good advice and might consume non-negligible runtime, we should consider really fixing that\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282172349"}}
{"comment": {"body": "I agree, but as long as we are working in all the logs in such manner, no need to flush it on the screen.. Please open task for logs refactor\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282172679"}}
{"comment": {"body": "@{5f82bf320756940075db755e} in backlog", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282174873"}}
{"comment": {"body": "the requirement is to raise data health N in case of all of the handshakes not arrived. if one of them is missing can be C", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282186659"}}
{"comment": {"body": "Move to `error_log.py`? That way the entire interface will be in one file \\(the consumer will need to import only that file\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282467707"}}
{"comment": {"body": "Is this just used in the case where an error occurs before a connection\\_result exists?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282472883"}}
{"comment": {"body": "Is this log left on purpose?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282475773"}}
{"comment": {"body": "Is this log left on purpose?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282479569"}}
{"comment": {"body": "Why was this added? When will this happen?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/319/_/diff#comment-282480930"}}
{"title": "automation ennvironment", "number": 32, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/32", "body": ""}
{"title": "Handle unexpected QUIC packets", "number": 320, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/320", "body": "Patch for .\nFurther tests to be done.\n"}
{"title": "Sparrow pipeline (golden test)", "number": 321, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/321", "body": "this PR contains the pipelines to be scheduled on k8s in order to create validated l2 typing model:\n\nbackfill job that extracts features and labels\nhourly cron that creates and validates(golden test) l2 model\nhourly cron that creates dhcp fingerprint model\nhourly cron that extracts features and labels of the additional hour of raw data\n\nartifacts(repo: ):\n\ns3crawler chart\nprocessor chart\nl2model chart\ndhcp fingerprint chart\n\nThe architectural design:\n\n\n\n"}
{"title": "Sparrow ut[NOT FOR MERGE YET]", "number": 322, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/322", "body": "utest\nflake\n\n"}
{"title": "MEROSP-480: Fix missing label on data health metric in disconnection event flow", "number": 323, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/323", "body": ""}
{"title": "Support error log in BI and add UT", "number": 324, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/324", "body": ""}
{"title": "MEROSP-484 Decision log cosmetic improvements", "number": 325, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/325", "body": "Changed the decision time we present in ongoing and disconnection events to be N/A instead of 0.0.\nChanged the number of digits we show after the decimal point for the decision duration value if its valid\n\n"}
{"title": "MEROSP-485 Formatted the duid hw mac we present in the log", "number": 326, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/326", "body": ""}
{"title": "mersop 479 478 fix", "number": 327, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/327", "body": ""}
{"title": "Fixed the short connection metric", "number": 328, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/328", "body": "As I was looking in this file, I thought that this fix is needed. Feel free to suggest any other solution for this case.\nThe goal of this metric is to subject any connections that were too short. By taking the time of the disconnection, and subtracting the last time that the device was connected, we can measure this amount of time correctly. The old calculation was wrong in my opinion."}
{"title": "Hotfix/pipeline latency kpi", "number": 329, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/329", "body": "Support error log in BI and add UT\nfix create pipelines imports indent\nadd missing stream for error log\nmove ErrorLevel to error_log and add more error log flows\nfix lag time metric and UT for avro schema\nAlign schema in UT\nvalidate header of arrived event\nadd CFR parser lengght validation and handling\nfix date log\nfix datetime import\nfix flake unsued varaibles\nfix pipeline latency metric to include the deserialize step as well\n\n"}
{"title": "execute pipeline as dask distributed", "number": 33, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/33", "body": "\n\nexecute pipeline as dask distributed\n\n"}
{"comment": {"body": "great name", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/33/_/diff#comment-227757916"}}
{"comment": {"body": "what if we zip everything? `.dockerignore` should enforce not copying bad files to the image in the previous line", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/33/_/diff#comment-227758253"}}
{"title": "Feature/error log bi", "number": 330, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/330", "body": "\n\nfill error log with source info\ninitial support for multiple error logs per event\n\n"}
{"title": "Bugfix/MEROSP-486 no l2 data presented in the decision log", "number": 331, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/331", "body": "This bug was opened since we didnt present the association & probe caps in the decision log (we showed empty values). After investigating the problem, it turned out that we actually had two bugs within it:\n\nThe L2 data indeed wasnt extracted correctly in the export_adapter. This was fixed, and the two methods that were responsible for it were merged to one method.\n\nAfter getting the L2 data correctly, we failed in the avro serialization. The association caps somehow turned to be a tuple instead of a dictionary along the flow, which was luckily discovered by one of our tests. The cause of that bug lies in a comma that was left in the code (matches2_data['association_features']):  \n\n\n\n"}
{"title": "Hotfix/L2 data on disconnection events", "number": 332, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/332", "body": "Fixed the retrieving of association and probe requests in a disconnection events. In this case, we get a json from the db and need to handle it this way, in oppose to connection & ongoing events\n"}
{"title": "Helm deployment tool - local", "number": 333, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/333", "body": "\n\nadd local helm deployment tool\n\n"}
{"title": "Bugfix/MEROSP-489 unlimited entries in typing f", "number": 334, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/334", "body": "fix bug of saving null data and too much data\nflake\n\n"}
{"title": "Minor change in the logs, just to make them more clear", "number": 335, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/335", "body": ""}
{"title": "Helm deployment tool", "number": 336, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/336", "body": "\n\nadd local image deployment\n\n"}
{"comment": {"body": "what is different here than docker\\_build?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/336/_/diff#comment-285662825"}}
{"comment": {"body": "docker\\_build = builds the tools image\n\nclassifier\\_docker\\_build = builds the classifier image ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/336/_/diff#comment-285801914"}}
{"title": "rename 'topic' param with (S3)'bucket_sub_dir'", "number": 337, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/337", "body": "rename 'topic' param with (S3)'bucket_sub_dir'. more descriptive name and avoid name collision with the real Kafka topics."}
{"comment": {"body": "@{5e2ee80dad92310e881b7a22} can you elaborate where it\u2019s an issue? we have partitioning structure schema used in monitor connectors. it\u2019s probably correct to move the schema outside infrastructure archiver to the monitors, but that means all the schema..  \\(aka topic/date\u2026 and remain relative and absolute path in infrastructure\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/337/_/diff#comment-285662632"}}
{"comment": {"body": "I think this is just confusing, the keyword \u201ctopic\u201d is all over the place \\(at least in automation where we use archiver the most I guess\\)   \nand it\u2019s so hard to make it clear if you mean the topic you subscribe-to/ producer-to/ or is it means a location in S3.  \n  \nI got my head spinning from that.   \nIf it drags u towards major changes we\u2019ll back down and think of another solution.. but it just a keyword change any IDE can handle easily.. I guess I miss the complexity of such changes in infra.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/337/_/diff#comment-285675883"}}
{"title": "Fixed the sessions data accessing in sparrow pipeline", "number": 338, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/338", "body": "While looking at the logs, the following traceback occurred:\n13:43:42.150 Traceback (most recent call last):\n13:43:42.150 File \"/code/eros_pipelines/src/__init__.py\", line 174, in start\n13:43:42.150 self.stream.emit(records)\n13:43:42.150 File \"/code/sparrow_pipelines/src/piplines_core.py\", line 34, in emit\n13:43:42.150 result = self._batch(msg)\n13:43:42.150 File \"/code/sparrow_pipelines/src/piplines_core.py\", line 40, in _batch\n13:43:42.150 return list(map(lambda x: self._single(x), messages))\n13:43:42.150 File \"/code/sparrow_pipelines/src/piplines_core.py\", line 40, in lambda\n13:43:42.150 return list(map(lambda x: self._single(x), messages))\n13:43:42.150 File \"/code/sparrow_pipelines/src/piplines_core.py\", line 46, in _single\n13:43:42.150 result = func(result)\n13:43:42.150 File \"/code/eros_pipelines/src/sparrow_forwarder.py\", line 51, in transform\n13:43:42.150 res = connection_event.raw_data.to_dict()\n13:43:42.150 AttributeError: 'ConnectionResult' object has no attribute 'raw_data'\nThe transform() method is called right after the deserialize() method. Since deserialize() returns a ConnectionResult instance (and not a ConnectionEvent instance), this method had to be fixed in order it to work properly."}
{"title": "Helm deployment tool", "number": 339, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/339", "body": "NOT FOR MERGE YET"}
{"title": "added deploy rollout", "number": 34, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/34", "body": "added deploy rollout\n\n"}
{"title": "fix absolute path pull logic in archiver, test cover added", "number": 340, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/340", "body": "before:\nin order to use the pulling method with an absolute path, u still had to provide params: topic and datetime because they were not optional, which defeats the goal of it. \nafter:\nthe arguments are optional. now pulling for an absolute path makes much more sense\ntest cov added\n"}
{"comment": {"body": "remove comments  \ncode should explain itself", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/340/_/diff#comment-286165818"}}
{"title": "Fix archiver", "number": 341, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/341", "body": "before archiver override the argument to_path in pull_data\n\nfixed archiver logic pull remote data to a local path\nadded relevant test\n\n"}
{"title": "Helm deployment tool", "number": 342, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/342", "body": "\n\nconvert k8s utils to a class , add parent dir as package name\nfix conflicts\n\n"}
{"title": "Fix archiver", "number": 343, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/343", "body": "\n\nadded archiver utils funcs: determine_event_type[con or raw], convert_raw_to_con. added archiver method: convert_batch_to_conn_event, load_con_events_from_s3/local\n\n"}
{"title": "Fix archiver", "number": 344, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/344", "body": "\n\nadded archiver utils funcs: determine_event_type[con or raw], convert_raw_to_con. added archiver method: convert_batch_to_conn_event, load_con_events_from_s3/local\n\n"}
{"title": "load typing models when creating the pipeline", "number": 345, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/345", "body": ""}
{"title": "Fix import archiver", "number": 346, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/346", "body": "fix relative import problem with archiver after being packaged\n\n"}
{"comment": {"body": "shouldn\u2019t it be relative \\(without the `data_archiver.src`\\)\n\n\u200c\n\n`data_archiver.event_deserialize_utils import convert_batch_files_to_connection_events`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/346/_/diff#comment-286540321"}}
{"comment": {"body": "`data_archiver.event_deserialize_utils import convert_batch_files_to_connection_events`  \nis the problem..  \nafter it packaged, it is no longer aware of anything outside \u201csrc\u201d directory. hence the user in any other repo will get the import error", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/346/_/diff#comment-286540870"}}
{"comment": {"body": "when importing relatively like this you can enjoy both worlds of working with unpackaged code in classifier repo and outside of it with packakged code and have zero problems", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/346/_/diff#comment-286541156"}}
{"title": "Feature/typing v1", "number": 347, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/347", "body": "This PR just moves typing features under features dir.\nand added Hostname and RandomMac to typing features\n\n"}
{"comment": {"body": "do we need only os and model here? all other will get from the device/os database", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/347/_/diff#comment-287184773"}}
{"title": "update dynamo at the end of event pipeline, fixed tests", "number": 348, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/348", "body": ""}
{"title": "Helm deployment tool", "number": 349, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/349", "body": "\n\nupdate readme file and fix typo\n\n"}
{"title": "notifications", "number": 35, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/35", "body": ""}
{"title": "fix the make command docker_run_local_classifier", "number": 350, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/350", "body": "classifier from docker needs slightly different env params, file created. \nadded creds for S3 in docker command by attaching the needed volume\n"}
{"comment": {"body": "What\u2019s different here compared to .env?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/350/_/diff#comment-287647142"}}
{"comment": {"body": "  \n`DYNAMODB_HOST=http://dynamodb-local:8000`   \nand   \n`KAFKA_ADDRESS=kafka:9092`  \n  \nVS   \n  \n`KAFKA_ADDRESS=localhost:29092`  \nand  \n`DYNAMODB_HOST=http://local_host:8000`   \n", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/350/_/diff#comment-287647570"}}
{"comment": {"body": "it come down to docker networking... when running inside a container the attaches to a bridge network of another compose, any service get a dns by the service name", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/350/_/diff#comment-287647894"}}
{"title": "Align classifier with 0.8.1 data collector interface", "number": 351, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/351", "body": "Align classifier and UT with 0.8.1 data collector api\ndd pcap utils to dump from session\nadd UT\nmove classifier to use data archiver as local package\nremove hardcoded packet parser api copy of phy and etc\n\nnotice: schema changed the mode from list/str to enum as should\n"}
{"comment": {"body": "Where is this being used?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/351/_/diff#comment-288116744"}}
{"comment": {"body": "it\u2019s tools to parse some pcaps with management packets, used for sparrow goldenset, for example", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/351/_/diff#comment-288119884"}}
{"title": "Feature/typing v1 [Not For Merge Yet]", "number": 352, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/352", "body": "load typing models when creating the pipeline\nminor fix\nmoving code, using hostname and randomMac as ytping features\nload scores\nchanged result comperator\ntypo\nchanges\ntyping models\nflake\nrefactoring typing\n\n"}
{"title": "Various Updates to Metrics", "number": 353, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/353", "body": "Added DB size\nincoming/outgoing data rates\nscored devices ratio\nmatch/no match labels\nupdated buckets\n\n"}
{"title": "Golden test", "number": 354, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/354", "body": "WIP\nWIP\nanalyze probe request without association\nupdate validation to include contains in matches\n\n"}
{"title": "load model fix", "number": 355, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/355", "body": "splitting the typing into small PRs"}
{"title": "Massive typing change", "number": 356, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/356", "body": "each typing model will return his feature (answer, model/os/vendor etc)\ndevice_type_identification.py changed a lot! plz look at it.\nfixed the way we are handling dataclasses\n"}
{"title": "Update metrics' buckets", "number": 357, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/357", "body": "Adapt buckets to data seen in office dogfood"}
{"title": "Remove metrics dependency from archiver", "number": 358, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/358", "body": "Report metrics in archiver caller instead inside archiver"}
{"title": "Hotfix/local deploy helm", "number": 359, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/359", "body": "add archiver option to dump to pcaps the connection events with UT and internal util to parse pcaps to radio tap packet pcaps with labels\nfix ut and flake8\nfix code review comments\nfix return value hint\nalign to latest interface 0.8.1\nalign to 0.8.1 and use data archiver as package\nfix import ater merges with dev\nfix topic creation, deletion\nfix topic replace utils\n\n"}
{"title": "adding more functionality for pull and put data archiver by path &adding a test in test archaive using path", "number": 36, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/36", "body": "\n\nadding more functionality for pull and put data archiver by path &adding a test in test archaive using path\n\n"}
{"title": "Sparrow forwarder", "number": 360, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/360", "body": "rename spelling error\nfix sparrow_forwarder"}
{"title": "Feature/error logs cont", "number": 361, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/361", "body": "add archiver option to dump to pcaps the connection events with UT and internal util to parse pcaps to radio tap packet pcaps with labels\nfix ut and flake8\nfix code review comments\nfix return value hint\nalign to latest interface 0.8.1\nalign to 0.8.1 and use data archiver as package\nfix import ater merges with dev\nfix topic creation, deletion\nfix topic replace utils\nadd more error logs\n\n"}
{"title": "Feature/improve radiotap parsing [NOT FOR MERGE YET]", "number": 362, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/362", "body": "This PR changes the way we store our parsed RadioTap packets. Instead of storing all of them together under one general RadioTap structure, we store them separately by their type (kinda like we do with storing the DHCP request & DHCP ACK separately).\nBy doing so, we can iterate them more quickly.\nIn addition, I tried to remove our looping iterations as much as possible, even after doing this separation. For example, instead of using the built-in Python filter to get the first element that fulfills a condition, I preferred to changed it to the good old for loop that stops when it finds the desired element.\nNote that while we parse all RadioTap packets, we now store only the assoc & probe requests. If in the future well want to process more types of RadioTap packets in our feature extraction, well just need to update our inner parsing.py file with more structs.\nThis branch was not yet checked in terms of performance improvement, but Im opening this PR anyway to keep it in our mind."}
{"title": "add scoring file to helm", "number": 363, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/363", "body": ""}
{"title": "bug fix", "number": 364, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/364", "body": ""}
{"title": "logs and wispr fix", "number": 365, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/365", "body": ""}
{"title": "Hotfix/l2 parse time", "number": 366, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/366", "body": "First of all there is a small change the makes L2 data parsing much better.\nLast logs PR wasn't good, because if you set root log level to debug, all other loggers (kafka, s3fs, etc..) will be in debug as well wich is not good.\nSo I added a logger called typing (we can add another one for identification..) and it has is own log level.\n\n"}
{"title": "just loggers", "number": 367, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/367", "body": ""}
{"title": "Matchmaker Refactor and Device Deduplication Based on Strong Identifiers", "number": 368, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368", "body": "Matchmaker Refactor\n\nSeparate feature extraction and matching more clearly\nLink feature objects with extracted values from the get-go and dont use extracted_features protobuf throughout. Now matchmaker and its derivatives are not aware of specific features, they just iterate over features and group by grade (strong, weak).\n\n\n\nAlign Identification Flow\n\nTo match the flow described here.\n\nIncorporate external matching / filtering classes in the flow.\n\nStrongMatcher and WeakMatcher iterate strong/weak features, accordingly, mainly differ by the method to get the best match (out of all matches).\n\nTypeFilter, FeatureFilter and ActivityFilter return lists of filtered devices.\n\nActivity filtering disabled by default, for now\n\n\n\n\n\n\n\nDevice Deduplication\n\nThe StrongMatcher also introduces a method to get a list of duplicated device. Given a list of matching devices and the earliest created device (which is the selected device), all matching devices other than the selected one should be removed.\nThe selected devices DB record will be updated with all of the updated strong features from the current session. Features that should be saved to DB based on combination of the last device record values and the current extracted features will be combined based on the to-be-deleted device record that contributed that feature.\n\n\n\nIdentification Levels\n\n\nThere is a difference between the feature grade for matchmakers use and the feature grade for statistics collection use:\n\nFrom matchmakers perspective, features are composable using mixins. That is, for example, a feature can participate both in the strong matching stage and in the feature filtering stage, using different matching functions based on multiple inheritence\nBut each identification feature can only count in one statistical feature grade: strong/weak/statistical. So from that aspect, it only has one value that is defined on the IdentificationFeature level.\n\n\n\n\n\n"}
{"comment": {"body": "Great description :smiley: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368/_/diff#comment-293732574"}}
{"comment": {"body": "why call the match explicitly w\\_match, f\\_match and etc?\n\nthe class already \u201cown\u201d the logic on how to match..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368/_/diff#comment-293761287"}}
{"comment": {"body": "about all the NotImplementedErros\n\nnot sure its a good idea..\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368/_/diff#comment-293867042"}}
{"comment": {"body": "add \n\n```\n@dataclass_json\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368/_/diff#comment-293867836"}}
{"comment": {"body": "If any of those data is going to the DB, it better be with dataclass\\_json as well\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368/_/diff#comment-*********"}}
{"comment": {"body": "Had some minor comments\n\nI think we must have a meeting about the matching flow it's a whole new code over there.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368/_/diff#comment-*********"}}
{"comment": {"body": "Only after adding this methods it does.\n\nWe need to have one match methode abastractly defined under IdentificationFeature. Each feature should account for one matching feature. Bonjour is both strong and filtering, and the methods are slightly different. I didn\u2019t want to add more noise by adding another feature. This should be dealt with as a later step when removing the redundancy of having extracted\\_features saved to protobuf \\(should be treated the same way typing features are now treated\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368/_/diff#comment-*********"}}
{"comment": {"body": "Why? The \u201cmodel implelmentation\u201d of any comparison function for a class first check that the type of the `other` is identical\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368/_/diff#comment-*********"}}
{"comment": {"body": "For now none of them are, but as I replied to Shimon and as we talked - the identification features should behave similarly to the typing features, including having separate `feature` and `feature_data`, having a nested json in the device record etc. This is for another refactor session.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/368/_/diff#comment-293879348"}}
{"title": "Feature/Decision log fields for metric usages - Phase 1", "number": 369, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/369", "body": "This PR adds two additional fields to the decision log, in order them to be examined by our Athena queries and create our required metrics later on.\nThe fields are:\n\nis_single_mac_to_multi_macs_connection: Did the event changed the number of macs associated to the device, from one mac to multiple (two) macs?\nis_weak_feature_to_strong_feature_connection: Did the event changed the identifying indicator in a a way that we now have a strong identifier associated to the device, and before that we only had a weak identifier associated with it?\n\nIn addition to the fields added to the decision log, another field was added to the Device schema in the DB - the identifying indicator. In order for us to compare the identifying indicator when matching a session to an existing device and understanding if we need to set the is_weak_feature_to_strong_feature_connection flag to '1', we need to keep the identifying indicator of each device in the DB.\nFurther examples and explanations can be found in the bottom of this page. \nThis is the first phase of fields additions, more to come later on, I prefer well have small and fast PRs to approve and not a big one that will look bigger than it really is.\n"}
{"title": "Feature/notification integration", "number": 37, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/37", "body": "sqs integration\nadd logging\nadd integration env\nfix integration pipeline\nformat decisions list\nhack notification metrics\n\n"}
{"comment": {"body": "these tests takes too long since it tries to connect to local kafka and timed out \\(30 minutes\\)\n\ni disabled it until it is fixed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/37/_/diff#comment-228922243"}}
{"comment": {"body": "we should fix those", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/37/_/diff#comment-228933041"}}
{"title": "paritition name", "number": 370, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/370", "body": ""}
{"title": "add regression suite to default and dev pipeline", "number": 371, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/371", "body": ""}
{"title": "poll interval", "number": 372, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/372", "body": ""}
{"title": "Hotfix/poll interval", "number": 373, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/373", "body": "add archiver option to dump to pcaps the connection events with UT and internal util to parse pcaps to radio tap packet pcaps with labels\nfix ut and flake8\nfix code review comments\nfix return value hint\nalign to latest interface 0.8.1\nalign to 0.8.1 and use data archiver as package\nfix import ater merges with dev\nfix topic creation, deletion\nfix topic replace utils\nadd more error logs\nparse band from radiotap header and fix error flows in parser\nfix flake8 warning\nfix back the poll interval and max batch size\n\n"}
{"title": "Feature/Decision log fields for metric usages - Phase 2", "number": 374, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/374", "body": "This PR adds another field to the decision log, in order to be examined by our Athena queries and create our required metrics later on.\nThe new field, is_deduped_event, determines if this event is associated to a device that we already know, that now connected again with a new random mac address. It will help us building the queries that refer to de-duped events more easily. Further examples and explanations can be found in the bottom of this page.\nThis is the second phase of fields additions, more to come later on, I prefer well have small and fast PRs to approve and not a big one that will look bigger than it really is. The first phase was done in this PR."}
{"title": "few changes", "number": 375, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/375", "body": ""}
{"title": "fix ipv4 assignment to device", "number": 376, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/376", "body": "TODO: add relevant unittest"}
{"title": "Improve param list log brevity", "number": 377, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/377", "body": ""}
{"title": "Remove deduplicated devices from db - WIP", "number": 378, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/378", "body": "Remove deduplicated devices from db\nUpdate new vault size accordingly\n\n"}
{"comment": {"body": "Can we get an indication of whether it succeeded or not?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/378/_/diff#comment-295180275"}}
{"comment": {"body": "You\u2019re right, we should. `delete_items` returns the response, but no one today parses it. I will check it. Although, if it\u2019s an error other than \u201ckey not found\u201d, I don\u2019t know what we should do. Should we keep trying to delete it? Probably not. So some way to keep a list of pending devices for removal is required. Btw, this is also true for writing devices - we don\u2019t handle failures.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/378/_/diff#comment-295180613"}}
{"comment": {"body": "It\u2019s not a small task, I think we should add it to backlog and merge. @{5f82bf320756940075db755e} @{5ed4f1de9a64eb0c1e78f73b} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/378/_/diff#comment-296752381"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} :thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/378/_/diff#comment-296752442"}}
{"title": "Feature/sparrow pipelines", "number": 379, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/379", "body": "Few changes in the way we load sparrow pipelines.\nModel validator will be generic and get his typing_logic_function from an Enum that will map pre configured pipeline name to the relevant function.\nL2 typing model - flag that will help sparrow to force using default l2 model.\n(removed all DEFAULT strings from the default model and fixed the tests. we need it for sparrow as well)\n"}
{"title": "Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\"", "number": 38, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/38", "body": ""}
{"title": "Feature/Decision log minor change - extracted features", "number": 380, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/380", "body": "As for now, we present data regarding our features in the decision log. We show the entire FeatureResult struct anyway, no matter if the features extraction code is valid or not:\n\nFollowing this PR, well present the details for each feature in the decision log only if it was indeed extracted. Theres no reason to show the data of features that were not extracted, or didnt have enough data, or any other reason. Its easier to filter by eye and by queries - we know that the feature was extracted properly if it exists, and if the field in empty - we know it wasnt.\n"}
{"title": "adding params to helm", "number": 381, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/381", "body": ""}
{"title": "add telegraf exporter to prometheus", "number": 382, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/382", "body": "This tool contains Telegraf server that export to local Prometheus server metrics that fetched and parsed from athena (pyhton code)\n"}
{"comment": {"body": "you can use here:\n\n\u200c\n\n```\nf\"{k=}\" and you will get a string like \"key=value\"\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/382/_/diff#comment-296086943"}}
{"comment": {"body": "we have the same code in sparrow.. maybe we should reuse", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/382/_/diff#comment-296087857"}}
{"comment": {"body": "is it exported to some package ? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/382/_/diff#comment-296088779"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} I don't think so but you are more than welcome to export it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/382/_/diff#comment-296089768"}}
{"comment": {"body": "Q: How is the metric\u2019s prometheus-type set? Counter, Gauge, etc.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/382/_/diff#comment-296753672"}}
{"title": "Feature/device data health", "number": 383, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/383", "body": "This PR adds the device data health property, in addition to the existing session data health / event data health.\nAs part of our efforts to extract useful data for our metrics and analytics, two additional data health indicators were defined  the device data health and the household data health. In this phase well add the device data health, which gives us a good indication about the quality of data we get from each device.\nNote that the device data health addresses the data quality in general, and not just from the last session. According to the definition, well accumulate all the data health scores and update this field if it was improved. In other words, when a device gets a high data health score, it will be kept and will not be reduced.\nIn order for us to extract the data later on in our metrics, the device data health was added to the decision log. In order for us to compare our latest data healths and decide if we need to update the device data health, it was also added to our db schema."}
{"title": "Reduce some spamming", "number": 384, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/384", "body": "Changed the severity of two logs under the nb_parser.py file from info to debug, for two reasons:\n\nThe netbios hostname is anyway disabled for now :slight_smile: \nThose two logs are not so important anyway, even if that feature was enabled\n\n"}
{"comment": {"body": "I  added an example of  a logger per module/service\n\nthere is a typing logger now in the code and you can configure his log level with env param.\n\nmaybe we can use the same concept  here as well.\n\nfrom eros\\_pipelines.src.\\_\\_init\\_\\_.py:\n\n```\n_root_log_level = os.environ.get(\"ROOT_LOG_LEVEL\", 'INFO')\n_typing_log_level = os.environ.get(\"TYPING_LOG_LEVEL\", 'INFO')\nlogging.basicConfig(format=\"%(asctime)s-%(name)s-%(levelname)s:%(message)s\", level=_root_log_level)\nlogging.getLogger('typing').setLevel(_typing_log_level)\n```\n\nthe logging.getLogger is creating a new logger when the key doesn't exists.\n\nand in the module you want to use it you need to add a line like this:\n\n```\nlogger = logging.getLogger('features')\n```\n\nand change all the log lines to [logger.info/debug](http://logger.info/debug)/etc..\n\nthis is just an easy way to control loggers per context..\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/384/_/diff#comment-296003166"}}
{"comment": {"body": "Good idea! Also looking forward we will need ability to enable/disable logs per vault", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/384/_/diff#comment-296004035"}}
{"title": "sparrow bugs", "number": 385, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/385", "body": "we had a bug in l2 pick model function, mp model can be none or not having platform column.\nthe second thing is that I think we should raise the exception when having one in sparrow pipeline..\n"}
{"title": "added placeholders and restricted boolean fields", "number": 386, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/386", "body": "user should can use SED to replace placeholders or use pythonic function for that purpose (I can supply it from automation if u want me to)\nplaceholder FULLSTACK if what we called so far custom stack  \nso to avoid confusion from this point and on(this will be documented on confluence as well):  \nif FULL_STACK == True:\n      deploy classifier with all connector and sparrow \nif FULL_STACK == False:\n      deploy classifier only"}
{"comment": {"body": "please add to readme as well\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/386/_/diff#comment-298329221"}}
{"comment": {"body": "added!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/386/_/diff#comment-298331713"}}
{"title": "Identification-Level for ongoing / disconnection events", "number": 387, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/387", "body": "Now setting the identification level in the decision log only for connection events. For ongoing / disconnection events, we will keep it as N_A, since this field has no meaning for them.\nThis change comes to make sure that we dont keep disinformation that might confuse us when we wish to analyze the data, or when we just look at the decision log and might get false information about the amount of such events."}
{"title": "added info level vault id logging", "number": 388, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/388", "body": "currently, log the vault id only on debug level. such critical info should be info, considering the classifier uses info level debugging by default.\nI used the vault id var which is already extracted in the modified function, and this is the only place where the function gets called anyways, so no need to panic about function changes."}
{"comment": {"body": "Good catch", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/388/_/diff#comment-296953287"}}
{"title": "Typing/dhcp android", "number": 389, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/389", "body": "WOP\nadded filtering features, rmoved unused models and code\nprevent captive from query on android devices, fixed some minor issues\nfix test\n\n"}
{"title": "Retrying Amir' PR", "number": 39, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/39", "body": ""}
{"title": "missing field in Device dataclass", "number": 390, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/390", "body": "API cant retrieve the devices list because identification_level field is missing\n"}
{"title": "Bug/api device dataclass", "number": 391, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/391", "body": "\n\nfixed dc-avro version\n\n"}
{"title": "MEROSP-504 Fix QUIC parsing exception from CID unexpected length", "number": 392, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/392", "body": "The source & destination connection identification length is defined by the QUIC protocol to be at maximum length of 64 bits(8 bytes). The aioquic library allow 20 bytes for each. A unit test that declares a length of 80 bytes for the destination CI was added. The expected behavior is to ignore this packet and return None when parsing."}
{"title": "Bug/get device", "number": 393, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/393", "body": "\n\nfix return device obj\n\n"}
{"title": "sparrow_minors", "number": 394, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/394", "body": "1.fixed logs level init\n2. there is an issue with exceptions, I think we need to log them and continue \n3. trying to save model with name and queryid"}
{"title": "added sparrow_bucket to config", "number": 395, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/395", "body": "configuration addons"}
{"title": "Archiver local utils - This will help me getting you the sessions that triggered exceptions/bugs during manual tests", "number": 396, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/396", "body": "\n\nlocal loading and filtering for archiver\n\n"}
{"comment": {"body": "replace the prints", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/396/_/diff#comment-298320467"}}
{"comment": {"body": "@{5e2ee80dad92310e881b7a22} what with this PR?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/396/_/diff#comment-309557642"}}
{"title": "indicator to return value and not name", "number": 397, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/397", "body": ""}
{"title": "Remove deuplicated devices from db", "number": 398, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/398", "body": "Remove deduplicated devices from db\nUpdate new vault size accordingly\n\n"}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/2574339191-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/398/_/diff#comment-298320697"}}
{"title": "refactoring directories, l2 model path structure to fit s3", "number": 399, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/399", "body": "the structure:\ndevice_typing  contains the typing service model\nfeatures  contains a directory called typing_features with all the models in it."}
{"title": "First working integration", "number": 4, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4", "body": "try sleep\nDon't exit on source start\ncouldn't work with dask, just sleep\nremove obsolete packages\npromote to single package multi-modules\nfix setup installations\nbuild packet_parser and python package\ninstall locally\nlocal dask cluster\n\n"}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} Grisha\u2019s comment is still valid - git lfs should hold binary data, separated from the script.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4/_/diff#comment-224038174"}}
{"comment": {"body": "Our current package structure is such that we effectively work with relative paths. To import the pip-installed package, we currently need to import \u201csrc\u201d. @{6085103f5797db006947d59a} has already updated the package structure in `data_archiver` and this should also be applied here.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4/_/diff#comment-224039365"}}
{"comment": {"body": "We should wait on the source instead of sleeping forever. I have a code for that that should be tested, will update.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4/_/diff#comment-224041340"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} , please note that the dependency in `betterproto` is required because of the deserialization step", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4/_/diff#comment-224044374"}}
{"comment": {"body": "Let\u2019s have this import as relative for now.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4/_/diff#comment-224044803"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} Can you reference fields from the dataclasses in `collector_interface` instead of using raw strings \\(.name\\)?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4/_/diff#comment-224048030"}}
{"comment": {"body": "And on the same note are the `getattr`s necessary?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4/_/diff#comment-224049373"}}
{"comment": {"body": "Not in the scope of this PR, but we should decide on a logging scheme. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4/_/diff#comment-224049804"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} In cases we transform raw byte fields to other types, we might benefit from using some of the suggested methods here: [https://stackoverflow.com/questions/36797088/speed-up-pythons-struct-unpack](https://stackoverflow.com/questions/36797088/speed-up-pythons-struct-unpack){: data-inline-card='' }", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/4/_/diff#comment-224051023"}}
{"title": "fix radio tap set() serialization", "number": 40, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/40", "body": "\n\nfix radio tap set() serialization\n\n"}
{"comment": {"body": "which member is a set? I\u2019m not aware that we have a `set` member. It\u2019s probably a bug that we shouldn\u2019t hide\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/40/_/diff#comment-229235631"}}
{"title": "Update wispr agent model", "number": 400, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/400", "body": ""}
{"title": "Test infra", "number": 401, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/401", "body": "better unittests infra:\nusing CsvTestCase from sparrow as an infrastructure for labeled data csv files."}
{"title": "format replace values script and git ignore tmp helm config", "number": 402, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/402", "body": ""}
{"title": "Removed upper case usages from the avro schema", "number": 403, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/403", "body": "Changed all upper case letters from our decision log schema and changed them to lower case (for example, processInfo  process_info) due to a bug that prevents us to read the columns properly in Athena.\nThe fields were already updated in the glue shcema, and this PR comes to make it identical and prevent further errors."}
{"title": "Fix erroneous db size calculation", "number": 404, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/404", "body": ""}
{"title": "fixed values in values.yaml", "number": 405, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/405", "body": ""}
{"title": "adding missing packages to make install", "number": 406, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/406", "body": ""}
{"title": "Athena metrics", "number": 407, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/407", "body": "\n\nfix bb pipeline\n\n"}
{"title": "all defaults env params should be in values.yaml file", "number": 408, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/408", "body": "when one adds an env param, he should:\n\nadd its template to the relevent configmap.yaml file\nadd the key to helm_deployment/values.yaml\n\n"}
{"title": "Bugfix/MEROSP-755 decision log   duration metrics", "number": 409, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/409", "body": "In order to test our systems processing speed, the following timing fields are used inside the MatchResult class, in the context of decisions made regarding a certain device while it is being processed by the system:\n\nsystem_process_duration:\n  From the detection of an event in the agent until it is fully processed in the relevant\n  pipeline inside the classifier.\npipeline_process_duration:\n  The time spent on processing an event inside the classifier's pipeline.  \n\nChanges made:\n\nAdd & Refactor the times names to reflect their meaning. \n  decision_duration -> system_process_duration\n  pipeline_process_duration  new field.\nChange the type of decision_duration(system_process_duration) in the Avro schema, from dictionary to float, due to unjustified overhead.\nAdd the necessary calculations to update both of the times measurements inside the disconnection and ongoing events pipelines and recording it inside the decision log. The pipeline processing time calculation is identical for all types of events so it was placed in the outer scope.\nRefactor the export adapter API, remove redundant arguments in 'get_messages_for_export' method. Redundancy was due to a historic gap in the ConnectionResult fields update along the pipelines.\nAdd the times measurements to the decision formatting with a protection from unexpected None value.\nAdd protection from None value before pipeline process time calculation since the processing of an event might fail and return a session with a None value assigned to it. \n\n"}
{"comment": {"body": "Excellent PR description. Good job ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/409/_/diff#comment-298940052"}}
{"comment": {"body": ":blush: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/409/_/diff#comment-298969939"}}
{"title": "parse_session() does not longer belong to a class, it should be stateless", "number": 41, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/41", "body": ""}
{"title": "Athena metrics", "number": 410, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/410", "body": "\n\nincrease exporter timeout\n\n"}
{"title": "fixed exit_gracefully and added metrics", "number": 411, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/411", "body": "we have a function that catches signal.SIGINT signal.SIGTERM in order to close kafka consumer properly.\nit was missing some params and the process had to exit after it so I added a flag and fixed the params"}
{"title": "relaunch msk cluster", "number": 412, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/412", "body": "relaunch msk cluster\nbootstrap servers' address changed"}
{"title": "Helm deployment config", "number": 413, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/413", "body": "added local files dir\nupdated how to tenant template\n\n"}
{"title": "helm action return error messages", "number": 414, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/414", "body": ""}
{"title": "loading android repo on create pipeline", "number": 415, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/415", "body": ""}
{"title": "Feature/ecs support", "number": 416, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/416", "body": "\n\nMove all sinks (result, error and decision logs, sparrow connector) to Firehose stack\n\nimpl kinesis stream\nchange the group id\nsink\ndevice classification pipeline\ndisable sinks for tests\nflake8\nserialize by sink\nfix tests\njson serialize\nWIP\nrelaunch msk cluster\nsparrow-forwarder from the classifier pipeline\nstream naming convention\n\n"}
{"comment": {"body": "good name:joy: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/416/_/diff#comment-300294602"}}
{"title": "Feature/ecs support telegraf metrics[NOT FOR MERGE]", "number": 417, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/417", "body": "\n\nMetrics should be pushed via Telegraf to Prometheus\n\n\nabstract the cw metrics client to open telemetry client\nopen telemetry impl\nimplement telegraf open telemetry\n\n"}
{"title": "Feature/helm errors", "number": 418, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/418", "body": "helm action return errors messages\nfix stderr condition\nfix conflicts\n\n"}
{"title": "run local services and debug classifier with pycharm", "number": 419, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/419", "body": "see instructions:\n"}
{"comment": {"body": "Nice!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/419/_/diff#comment-300898613"}}
{"title": "fix radio tap set() serialization", "number": 42, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/42", "body": ""}
{"title": "update kafka address on tmeplates and charts", "number": 420, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/420", "body": ""}
{"title": "MEROSP-836 parse arp packet", "number": 421, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/421", "body": "ARP ipv4 parsing\nroll back requirements\nrun local services and debug classifier with pycharm\nrename score csv and classes to separate identification and typing scores, added ARP feature and unit tests, added logic for IP resolution in matchmaker\nfixing static IP resolution\nadded colored logs, fixing unit tests scoring csv paths\nfixing flake8 and unit tests\nflake8\n\n"}
{"title": "Fixed a copy-paste issue in the decision log", "number": 422, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/422", "body": "Thank you @{6085103f5797db006947d59a}"}
{"title": "bumped boto3 15 minor versions", "number": 423, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/423", "body": "IDK wh it happened to me specifically (could be just and timing)\nI tried to run the classifier locally and encountered this exception:\n\nthis deprecated function is used some 1234567 layers deep in our dependency tree.\nafter digging more I got here:\n\nfinally, by using the pip dependency resolver I found that the only change needed for everything to work is to bump boto3 1.17.91   >>>  1.17.106"}
{"title": "MEROSP-777 DeviceIntelligenceResult", "number": 424, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/424", "body": "changes according to integration requirements"}
{"comment": {"body": "nice!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/424/_/diff#comment-300490377"}}
{"title": "Feature/packet aprser artifactory", "number": 425, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/425", "body": "add archiver option to dump to pcaps the connection events with UT and internal util to parse pcaps to radio tap packet pcaps with labels\nfix ut and flake8\nfix code review comments\nfix return value hint\nalign to latest interface 0.8.1\nalign to 0.8.1 and use data archiver as package\nfix import ater merges with dev\nfix topic creation, deletion\nfix topic replace utils\nadd more error logs\nparse band from radiotap header and fix error flows in parser\nfix flake8 warning\nfix back the poll interval and max batch size\nadd packet parser to artifactory for common usage\nfix merges typo\nfix library name\n\n"}
{"title": "MEROSP-861 exporting identifier in model_number", "number": 426, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/426", "body": ""}
{"title": "Bugfix/None device_data_health value", "number": 427, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/427", "body": "We had a bug in the dogfood regarding the device data health, a field that was recently added to our schema (as part of the work on the KPIs):\nTypeError: must be string on field device_data_health\n2022-05-12 05:35:52,051-root-ERROR:must be string on field device_data_health\nThis PR changes this field to have an optional value of None, for cases of old devices that didnt have this field in the first place.\n"}
{"title": "Beautify schemas", "number": 428, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/428", "body": "beautify avro schemas\nadded deepdiff package for tests of avro schemas\n\n"}
{"comment": {"body": "have you checked if it works in Athena?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/428/_/diff#comment-301353965"}}
{"comment": {"body": "see:  \n[https://bitbucket.org/levl/eros-infrastructure/pull-requests/49/beautify-schemas?link_source=email](https://bitbucket.org/levl/eros-infrastructure/pull-requests/49/beautify-schemas?link_source=email){: data-inline-card='' }   \nThere wasn\u2019t a real change, so it is not blocking this PR.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/428/_/diff#comment-301368424"}}
{"comment": {"body": "also The infra PR was merged already", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/428/_/diff#comment-301368497"}}
{"title": "Custom stack", "number": 429, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/429", "body": "Added a new Custom stack flag to indicate if any logs should be produced to Kafka.\n"}
{"title": "Added a test that reproduce the parsing problem that was solved in PR-40", "number": 43, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/43", "body": ""}
{"title": "Athena metrics", "number": 430, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/430", "body": "\n\nAthena query timeout increased\n\n"}
{"title": "fixing case of no arp and no ipv4 resolution - is static will be None instead of True", "number": 431, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/431", "body": ""}
{"title": "Feature/MEROSP-809: ICMPv6 MCLRv1", "number": 432, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/432", "body": "This PR adds support in MCLRv1 as a matching feature, along with its parsing. This comes as an addition to our existing usage in MCLRv2 as a matching feature.\nThe overall description and motivation for this task can be found here."}
{"title": "Bugfix/consumer category in low high res merge", "number": 433, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/433", "body": "added consumer category to high low merge\nfix typing to align result log\n\n"}
{"title": "support ipv6 resolution", "number": 434, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/434", "body": "choose between icmp6 ns ipv6 addresses and dhcpv6 address in matchmaker.\nadded unit tests for parsing and matching"}
{"title": "Feature/MEROSP-778 move all sinks error and decision logs sparrow connector to firehose stack", "number": 435, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/435", "body": "impl kinesis stream\nchange the group id\nsink\ndevice classification pipeline\ndisable sinks for tests\nflake8\nserialize by sink\nfix tests\njson serialize\nWIP\nrelaunch msk cluster\nsparrow-forwarder from the classifier pipeline\nstream naming convention\nsend results even when produce_to_kafka is False\nWIP\nproduce to separate kafka clusters\n\n"}
{"title": "MEROSP-839 capfp changes", "number": 436, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/436", "body": "added features of router configuration\n\nsee:\n\n\nthese features will be used for a new platform (router). these features are platform-invariant."}
{"comment": {"body": "Is this the first introduction of ctypes into our code? We have some issues with radiotap parsing performance. Can you find a comparison between \u201cunpacking\u201d with ctypes and struct.unpack?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/436/_/diff#comment-304072928"}}
{"comment": {"body": "[https://stackoverflow.com/questions/52004279/python-similar-functionality-in-struct-and-array-vs-ctypes](https://stackoverflow.com/questions/52004279/python-similar-functionality-in-struct-and-array-vs-ctypes){: data-inline-card='' }  says struct.unpack is better. will change", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/436/_/diff#comment-304079526"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} i\u2019m not seeing any reference to performance in the link. Also, notice that there are bitfields involved, so extra bit manipulations are required", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/436/_/diff#comment-304102770"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} What was decided?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/436/_/diff#comment-304643526"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} to leave it as struct.unpack does not do bit manipulation", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/436/_/diff#comment-304643754"}}
{"title": "Revert mclr v1 processing", "number": 437, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/437", "body": "This PR reverts the icmpv6 mclr v1 packets processing, since its been decided that more research needs to be done before fully integrating this component."}
{"title": "Feature/MEROSP-775 levl di ingestion", "number": 438, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/438", "body": "removed unused classes\nwop convertin\n\n"}
{"title": "MEROSP-840 radiotap changes", "number": 439, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/439", "body": ""}
{"comment": {"body": "Can you give some background?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/439/_/diff#comment-303823013"}}
{"comment": {"body": "this is a subtask of [https://levltech.atlassian.net/browse/MEROSP-832](https://levltech.atlassian.net/browse/MEROSP-832){: data-inline-card='' }  \nGrisha\u2019s changes to radio tap features ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/439/_/diff#comment-303837025"}}
{"comment": {"body": "Can this be merged?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/439/_/diff#comment-306671457"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} let\u2019s make sure this is tested once sparrow is stable", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/439/_/diff#comment-306671600"}}
{"comment": {"body": "passed local regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/439/_/diff#comment-315843975"}}
{"title": "Dev tools/local kafka", "number": 44, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/44", "body": "local kafka\nlocal kafka docker-compose\nadded kafka + cached docker layers + makefile\nimprove build order + clean up\nreturning network flag\nminio changes\nlint + default network\ncached layer fix\nreverting to postgres host\nset host network\nremoved rm line\nchange to default network\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\n\n"}
{"title": "fix kafka headers", "number": 440, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/440", "body": "Kafka headers are not supported in v1 API , we shouldent state that.\nKafka api version determined automatically."}
{"comment": {"body": "Will the SSL true still work?\n\nWe might need to adjust the version to \\(0,11,0\\) if it won\u2019t, but prefered to remove", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/440/_/diff#comment-303498637"}}
{"comment": {"body": "Yes its working without stating it, tested it locally and waiting for Automation to verify.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/440/_/diff#comment-303499099"}}
{"title": "Fix parsed fields initialization issue", "number": 441, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/441", "body": "Giving the dataclasses' members default values consisting of class instances was wrong. Caused data to not be zeroed. Now Initializing the class with default factory."}
{"title": "Hotfix/kafka headers", "number": 442, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/442", "body": "fix kafka headers\nfix setup.py\n\n"}
{"title": "fix arp ipv4 0.0.0.0 address and add unit tests", "number": 443, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/443", "body": ""}
{"title": "Fix duplicate devices presentation issues", "number": 444, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/444", "body": "Duplicate devices are aggregated into list based on matched features, so make sure the levl-ids that are eventually there appear only once.\nThe LEVL-IDs are saved as UUID types but should be strings.\nFix some matching tests.\n\n"}
{"title": "Revert dedup list type to UUID", "number": 445, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/445", "body": "Comply with schema that says that the dedup field should be a list of UUIDs. The issue we had earlier was with json dumping and this will be fixed with a json default encoder ."}
{"title": "tool to deserliaze and validate local events", "number": 446, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/446", "body": ""}
{"comment": {"body": "probably should be removed\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/446/_/diff#comment-305815620"}}
{"title": "removing captive", "number": 447, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/447", "body": "Removing captive portal feature\nit should also be removed from the decision log but I didnt want to break the schema again"}
{"title": "dhcp db", "number": 448, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/448", "body": ""}
{"title": "Health check container for Cujo integration", "number": 449, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/449", "body": "cujo testup WIP\nWIP\ndockerize\nflake8\nci\nci\nbug fix\nadd readme\nci\ndynamo db region\nadd logs\ndeploy via helm\nformat message\nadd logs\nwroking version\n\n"}
{"title": "metrics tools -> metrics and time measurements", "number": 45, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/45", "body": ""}
{"title": "MEROSP-839 capfp changes 2", "number": 450, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/450", "body": "\n"}
{"title": "Feature/Prior Logic", "number": 451, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/451", "body": "Added the Prior Logic to the device type identification procedure.\nTLDR - the prior logic determines the reasonable device type in the cases when we cant make the decision based on our algorithm alone. For example, if we cant decide between an iPad and an iPhone, well just decide its an iPhone since more people have iPhones in comparison to iPads.\nThis logic is actually quite a hack for now, until well see real data from our deployed vaults that will teach us the exact measurement and percentage of each device in the market.\nThe cases in which we handle are:\n\niPad / iPhone - both are in the csv column of device_type. In this case well go with the iPhone as our decision.\nPixel / not Pixel - well go with the Pixel as our decision. In this case as well, the relevant column is also device_type.\nApple Desktop / Apple Laptop - there are three options according to the csv: Apple Desktop iMac, Apple Desktop Mac, Apple Laptop MacBook. In all three cases, the vendor is Apple and the consumer_category is either Desktop or Laptop. In this case well go with Apple Laptop as our decision.\nGalaxy S* / Galaxy Note* - that case is a bit more complex because there are many options for each of them (for example: S20, S10, etc). Either way, well go with the Galaxy S* as our decision.\n\n"}
{"title": "Prior logic to decision log", "number": 452, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/452", "body": "Added an indication of the prior logic usage to the decision log, for future investigation and to make our QA life easier"}
{"title": "all ipv6 addresses are now exposed in result log", "number": 453, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/453", "body": ""}
{"title": "updated multi platform model", "number": 454, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/454", "body": ""}
{"comment": {"body": "I did not notice that the folder structure was flattened\u2026 Does sparrow use the same convention? Is the S3 directory build accordingly?  \nI was thinking it should be:\n\n```\n- l2_models\n  - default\n    - default_do_not_delete\n      - whatever.csv\n    - date1.csv\n    - date2.csv\n  - mp\n    - default_do_not_delete\n      - whatever.csv\n    - date3.csv\n    - date4.csv\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/454/_/diff#comment-305304821"}}
{"comment": {"body": "ask @{5fd5d5149edf2800759cc96d} or @{6085103f5797db006947d59a} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/454/_/diff#comment-305321323"}}
{"title": "limit number of ipv6 addresses", "number": 455, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/455", "body": ""}
{"comment": {"body": "If both ICMPv6 and DHCPv6 contribute not identical addresses \\(might be overlapping\\), don\u2019t we want a union of both?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/455/_/diff#comment-305302616"}}
{"comment": {"body": "DHCPv6 has only one address. I think it\u2019s a bug if it doesn\u2019t match one of the ICMPv6 addresses. Same as in ipv4. Even though it is a bug, we take the ICMPv6 in this case.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/455/_/diff#comment-305321897"}}
{"comment": {"body": "don\u2019t forget to update schemas", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/455/_/diff#comment-315858848"}}
{"title": "Pre commit hooks", "number": 456, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/456", "body": "added pre-commit hook for flake8\nadded pre-commit install to make install\n\n"}
{"comment": {"body": "@{6265307b185ac200692f9bd9} does this changes the code or verifies?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/456/_/diff#comment-306499539"}}
{"comment": {"body": "Trim Trailing Whitespace.................................................Passed  \nFix End of Files.........................................................Passed  \nCheck Yaml...........................................\\(no files to check\\)Skipped  \nCheck for added large files..............................................Passed  \nflake8...................................................................Passed  \n  \nthe first two changes the files. only for the files you changed already.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/456/_/diff#comment-306503892"}}
{"title": "Bugfix/MEROSP-958 macbook pro m1 hw dhcp wrong", "number": 457, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/457", "body": "change the way we choose os\nfix the way we build test\ntyping improvments\n\n"}
{"title": "Feature/MEROSP-968 align timestamp definition in", "number": 458, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/458", "body": "removing too broad exception\nchanging timestamps type in result log\n\n"}
{"comment": {"body": "@{5dbeb866c424110de52552cc} what was agreed with Cujo? Does they expecting this change in ML1? if so, we need to merge only those changes to release/cujo.. brancn", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/458/_/diff#comment-306674477"}}
{"comment": {"body": "Yes, they already merged this change and expect this in ML1.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/458/_/diff#comment-306736528"}}
{"title": "MEROSP-959 Fixed typing_merge() to update the model_ui_description as well", "number": 459, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/459", "body": "Up until now, when we performed our typing_merge(), we updated our DeviceTypingResult fields if we found a better model / os resolution.\nThe problem that was described in MEROSP-959 referred to a scenario where we indeed improved our resolution but not all fields were updated (and among them, the model_ui_description field that is quite important).\nFor example, if in our first connection we identified the device as an iPhone 12 / iPhone 13 device, and in the second connection we identified it as an iPhone 13 device, the model resolution was not updated and we kept the less-good iPhone 12 / iPhone 13 description.\nAfter this fix, all the fields will be updated if we indeed improved our typing resolution."}
{"title": "add decision log table", "number": 46, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/46", "body": ""}
{"comment": {"body": "Best is to have try except individually for every table, since if the first table doesn\u2019t exist, the rest wouldn\u2019t be deleted", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/46/_/diff#comment-229765136"}}
{"comment": {"body": "would `sqlalchemy.Boolean` be more appropriate for this 1-hot encoding? as the values can be either 0 or 1 \\(or True or False\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/46/_/diff#comment-229767793"}}
{"comment": {"body": "`int(DHCPv6_DUID.name in session.decision)` is a nice trick to turn return 0 or 1", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/46/_/diff#comment-229768130"}}
{"title": "better ua handle", "number": 460, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/460", "body": ""}
{"comment": {"body": "@{6085103f5797db006947d59a} Why not putting the csv in code already now? others will join.\n\nmake sense that ll manual csv files should be in code.. in this PR for example I can\u2019t see the diff", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/460/_/diff#comment-305730407"}}
{"comment": {"body": "Do we want it be merged to dev or to _release/cujo\\_integration\\_ml1_?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/460/_/diff#comment-305741621"}}
{"comment": {"body": "dev I think", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/460/_/diff#comment-305808813"}}
{"comment": {"body": "@{6085103f5797db006947d59a} Cujo release, then to dev", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/460/_/diff#comment-305809206"}}
{"title": "Feature/local model files", "number": 461, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/461", "body": "comment out dt ttls\ninitial direction of fixing packet parser cancer\nmore conditional entry of features to parsed data\nfix tests because of apple model_info\nfix test\nfinally fix device\nadd local model files\nforgotten file\niPadOS -> iOS, watchOS -> iOS\ndhcp local db\nnew dhcp fingerprints\nresolve another name problem\nprint typing features to log\nfix tests\nadditional macOS 12 fingerprint\nrevert naming\nmodel from manual built\nrevert naming\nremove device_db.csv\nrestored device_db.csv from LFS\nstop track device db\nrestore devices_db.csv from LFS\nfixed ipad naming\nupdated model manually\nadd missing imports\nfixed tests\ntest fix\nfix tests\nskip ttl test for now\nfixed test around s3_client\nfixed test around s3_client\nfix skip\nfinally fix tests\nSome surface fixes\ncode cleanup\nmore code cleanup\nmodel version -> v2\n\nRevert \"more code cleanup\"\nThis reverts commit 0f5908c90563b5d97881fea1a1c4914c13ddbb8f.\n\n\nRevert \"model version -> v2\"\nThis reverts commit 72005e389b457d1986c57ac7aa9b82952f597db5.\n\n\nRevert \"Revert \"more code cleanup\"\"\nThis reverts commit 0cdf8503789ae1947091f703b19eb597806cc544.\n\n\nmove model to v2\n\nmore code cleanup\nnew dhcp db version\nmerge devices db with devices_db_2022_06_02.csv\nfix ident\nfix ident\nmodel names in configuration\nlintt\nto LFS\nupdate l2 models from latest, convered also MP model\nremoved redundant if\nfix lint this time?\nmaybe this time?\nupdate L2 model with new devices: iPads, iPhone SE3, apple watch 7\n\n"}
{"title": "MEROSP-999 Increased significantly the dynamo db ttl value", "number": 462, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/462", "body": "From now on, well use the default value for DYNAMODB_TTL_SEC_FROM_NOW, which is 2592000 (seconds in month), instead of 6. This change is made to avoid the problems we experience from time to time when running in local environment configuration\n"}
{"title": "MEROSP-1003 Updated api version from 0.58.7 to 0.58.8", "number": 463, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/463", "body": "Due to the latest changes we made last week"}
{"title": "Fix yaml parsing", "number": 464, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/464", "body": "fixed replace_values.py yaml problem, added ruamel package to reqs.txt file (MIT license)\nremoved ruamel package from reqs.txt to test-reqs.txt. according to Shimon's request\n\n"}
{"title": "Feature/MEROSP-911 get ipv6 from ack message and", "number": 465, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465", "body": "dhcpv6 ipv6 : parse reply message instead of request message\nA separate feature was added\nregression passed:\n{: data-inline-card='' }"}
{"comment": {"body": "Thanks", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465/_/diff#comment-308287358"}}
{"comment": {"body": "No need to include comments like \u201cflake8\u201d, \u201cbug fixes\u201d etc. in PR description.\n\nI would also mention the fact that a feature was added.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465/_/diff#comment-308288142"}}
{"comment": {"body": "Great!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465/_/diff#comment-308289670"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465/_/diff#comment-308297386"}}
{"comment": {"body": "thanks!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465/_/diff#comment-308297926"}}
{"comment": {"body": ":joy: Thanks", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465/_/diff#comment-308320891"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} please retarget to dev", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465/_/diff#comment-308715100"}}
{"comment": {"body": "why?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465/_/diff#comment-308719411"}}
{"comment": {"body": "this is out of scope for cujo ml1", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/465/_/diff#comment-308858083"}}
{"title": "querying only on identifier with UA", "number": 466, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/466", "body": ""}
{"title": "Release/cujo integration ml1", "number": 467, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/467", "body": "Merged in bugfix/MEROSP-959-wrong-model-ui-description (pull request #459)\nMEROSP-959 Fixed typing_merge() to update the model_ui_description as well\n\nMEROSP-959 Fixed typing_merge() to update the model_ui_description as well\n\nApproved-by: Shimon Goulkarov Approved-by: Ophir Carmi\n\n\nRevert multiple IPv6 resolution\n\nupdated eros-api version 0.58.7\nadapt to automation release branch\n\nMerged in prior_logic_to_decision_log (pull request #452)\nPrior logic to decision log\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into feature/MEROSP-775-levl-di-ingestion\nfix test data and l2 strict matches\nl2 mp file\nAdded an indication of the prior logic usage to the decision log\nFixed the MacBook scenario to enter the right code flow along with the updated general_device_type_set\nUpdated L2 S3 model path\nMerge branch 'dev' into prior_logic_to_decision_log\nMerge branch 'dev' into prior_logic_to_decision_log\nMerge branch 'dev' into prior_logic_to_decision_log\nMerge branch 'release/cujo_integration_ml1' into prior_logic_to_decision_log\n\nApproved-by: Ophir Carmi\n\n\nMerged in bugfix/MEROSP-999-fix-dynamo-db-ttl (pull request #462)\nMEROSP-999 Increased significantly the dynamo db ttl value\n\nMEROSP-999 Increased significantly the dynamo db ttl value\nRemoved DYNAMODB_TTL_SEC_FROM_NOW from the local_env_file so we'll use the default value - 30 days\nRevered the change in test_env_file\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bugfix/ua_mac_adaptor (pull request #460)\nbetter ua handle\n\nscoring csv\nscoring csv\nfixed logger and ua logic\nMerged release/cujo_integration_ml1 into bugfix/ua_mac_adaptor\nremove unused, added function for low res query\nfix tests\nMerged in feature/local_model_files (pull request #461)\n\nFeature/local model files\n\nfix ident\nmodel names in configuration\nlintt\nto LFS\nupdate l2 models from latest, convered also MP model\nremoved redundant if\nfix lint this time?\nmay\nl2 fixes\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into bugfix/ua_mac_adaptor\nfixed dynamo ttl\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov\n\n\nMerged in bugfix/MEROSP-1003-update-the-schema-version-in (pull request #463)\nMEROSP-1003 Updated api version from 0.58.7 to 0.58.8\n\nMEROSP-1003 Updated api version from 0.58.7 to 0.58.8\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\n\n\nMerged in fix-yaml-parsing (pull request #464)\nFix yaml parsing\n\nfixed replace_values.py yaml problem, added ruamel package to reqs.txt file (MIT license)\nremoved ruamel package from reqs.txt to test-reqs.txt. according to Shimon's request\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov Approved-by: Ophir Carmi\n\n\nMerged in bugfix/MEROSP-1004-iphone-11-wrong-model-name-b (pull request #466)\nquerying only on identifier with UA\n\nquerying only on identifier\n\nApproved-by: Ariel Tohar\n\n\n"}
{"comment": {"body": "revert", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/467/_/diff#comment-306674224"}}
{"title": "new models of devices db and layer2", "number": 468, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/468", "body": "new devices db with fix of poco and redmi\nnew l2 model"}
{"title": "Fixed the AvroS3ArchiverSink to write the bytes-part of the KafkaMsg", "number": 469, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/469", "body": "After getting tracebacks in our monitoring components, hopefully it will solve the lack of writing S3"}
{"title": "Dev -> Metrics branch (to be updated with the decision log)", "number": 47, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/47", "body": ""}
{"title": "Feature/MEROSP-967 save all data relating to eve", "number": 470, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/470", "body": "now we support saving all data needed for debug: ingestion event, decision log, result log, error log, caps.\nphase two will be to save it to the cloud s3\n\nsee:\n"}
{"title": "MEROSP-985 move typing models and device", "number": 471, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/471", "body": "new models of devices db and layer2\ncorrect l2 model file names\nloading fix\nadded using_local_files to env\nadded models\nlogs improvment, tests, os logic\nfix tests:\n\n"}
{"comment": {"body": "@{6085103f5797db006947d59a} might help [https://github.com/git-ftp/git-ftp/issues/579](https://github.com/git-ftp/git-ftp/issues/579){: data-inline-card='' }\n\n\u200c\n\n```\nclone:\n  lfs: true\n  depth: full\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/471/_/diff#comment-307280673"}}
{"title": "Bugfix/MEROSP-1016 l2 in decision log", "number": 472, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/472", "body": "This PR fixes two issues which are related to each other:\n\nMEROSP-1016: No L2 data was presented in the Decision Log\nMEROSP-1025: The data health checks for L2 data were wrong\n\nThe problem that started those two issues is the same - we had an inner change in the devices typing_features and not all of our code was updated with this change. We used to write the assoc and probe explicitly after processing the L2DeviceTyping feature, but it was changed to writing other fields instead - L2_Union and L2_Intersection.\nThis PR changed the code for the following reasons:\n\nThe problem with L2_Union was that it simply wrote the assoc and probe we used in the typing procedure without specifying which is which. Its an important indication for us, so the first thing this PR is adding is an explicit saying of which one is the assoc dictionary and which one is the probe dictionary (Refer to l2_device_typing:151, 154). We later use those fields to fill the decision log and calculate the data health.\nThe AvroSchema was not updated with the correct type. We specified both assoc and probe data in the decision log as Dict[str, str] but that type is wrong and caused inner crashes in the serialization process, so it had to be changed to Dict[str, Union[str, int, None]].\nThe data health check was wrong, since we no longer kept the L2DeviceTyping in our features dictionary. We need to look at the L2_Union dictionary instead to check those conditions we defined in the data health.\nWhen writing the decision log, the actual extraction of the assoc and probe data was wrong and thats why we simply wrote None every time instead of real data.\n\n"}
{"title": "add bump charts manual option", "number": 473, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/473", "body": ""}
{"title": "Hotfix/cujo values", "number": 474, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/474", "body": "add bump charts manual option\nupdate models and fix local flag\n\n"}
{"comment": {"body": "@{5e2ee80dad92310e881b7a22} you will need this fixes to be able to work with latest release cujo branch..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/474/_/diff#comment-307370388"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Thanks for the heads up!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/474/_/diff#comment-307370650"}}
{"title": "Fix file pathes", "number": 475, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/475", "body": "local models\nflake\nlocal files fix\n\n"}
{"title": "Feature/golden set tests", "number": 476, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/476", "body": "This is a poc for adding golden set devices to classifier\n\n"}
{"comment": {"body": "remove?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/476/_/diff#comment-307544003"}}
{"comment": {"body": "1. please add binary files with Git LFS\n2. please beautify json files, you can use `python -m json.tool <filename>`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/476/_/diff#comment-307544202"}}
{"comment": {"body": "remove?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/476/_/diff#comment-307544668"}}
{"comment": {"body": "its here until Shimon will decide if or not to test decisions", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/476/_/diff#comment-307546276"}}
{"comment": {"body": "same", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/476/_/diff#comment-307546284"}}
{"comment": {"body": "its only poc. who that will add tests over here will take care of the beautify ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/476/_/diff#comment-307546732"}}
{"comment": {"body": "I don\u2019t agree.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/476/_/diff#comment-307548018"}}
{"title": "edge case for os options", "number": 477, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/477", "body": ""}
{"title": "add cujo namespace to result schema", "number": 478, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/478", "body": ""}
{"comment": {"body": "how it reflects in Glue and in Athena?\n\nalso same PR in eros-infra :wink:", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/478/_/diff#comment-307620573"}}
{"comment": {"body": "yeah already opened one :smiley: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/478/_/diff#comment-307624472"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} Get latest eros-classes-api version from release/cujo\\_integration\\_ml1, so you\u2019ll be able to bump & upload new version to codeartifact", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/478/_/diff#comment-308154778"}}
{"comment": {"body": "And updated the corresponding version in eros-automation \\(coordinate with David if needed\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/478/_/diff#comment-308154800"}}
{"title": "Bugfix/MEROSP-1031 is new mac address", "number": 479, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/479", "body": "This PR fixes the identity_is_new_mac_address_assigned field in the Intelligence Result, which was always set to True.\nA new method was added to identify only the right cases for this flag, and all the unitests in test_pipeline_device_classification.py were updated as well."}
{"title": "update schemas to contain tenant and vault id", "number": 48, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/48", "body": "update schemas to contain tenant and vault id\n"}
{"comment": {"body": "what device\\_id stands for?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/48/_/diff#comment-230491768"}}
{"title": "Feature/MEROSP-967 save all data relating to event", "number": 480, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/480", "body": "added remote save\n\nsee:\n{: data-inline-card='' }\n\nJira issue\n{: data-inline-card='' }"}
{"title": "L2 typing model adjustments", "number": 481, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/481", "body": "The following have changed:\n\n~~Do not use probe request as the sole feature to identify a device (must have also an association feature vector match) - this was the historical behavior of the system from day 1 and it should fix MEROSP-1033 and MEROSP-1034~~\nActually merge L2 typing results between bands (using L2Union and L2Intersection) , this was not implemented properly and only used the L2 data from the last connection was used.\nDo not store the raw L2 feature vectors in the DB anymore, but store the results (list of models). This should decrease the DB size entry by a huge amount. The raw data in the DB was not used in any way.\nFix typing_merge logic. The logic was not updating the properties of the new incoming data/features in many cases.\n~~Update DHCP fingerprinting model with regards to iOS 15 / iOS 16 detection~~\n\n"}
{"title": "Bugfix/MEROSP-1024 Fixed device_first_mac_address field", "number": 482, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/482", "body": "This PR fixes the device_first_mac_address field value assignment in our Intelligence Result, and in general performs a fix in our prev_rand_mac_addresses list we keep.\nWhen we detect a device with a randomized mac that we already know, were adding that mac to the list of prev_rand_mac_addresses (as we should). The problem is the way we used to add this mac address to the list of mac addresses - that list didnt track the order of additions, and later on we relied on that order in the Intelligence Result:\ndevice_first_mac_address=device.prev_rand_mac_addresses[0]\nThis is fixed in this PR, by changing mac_features::get_updated_fields() to use a list instead of set.\nFor example: \nGiven\nx = {\"1\", \"2\", \"3\"}\ny = {\"4\"}\nz = x.union(y)\nWell get {'3', '4', '1', '2'}, {'1', '4', '3', '2'}, {'3', '4', '2', '1'} in different runs, and anyway the order is not guaranteed to be kept in the old way the code was implemented.\nOn the other hand, changing it to a list of elements that we always append the new mac address as the last element will keep track on the order of additions.\nThis PR also adds a unitest to validate this logic."}
{"comment": {"body": "nice!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/482/_/diff#comment-308149883"}}
{"comment": {"body": "1. Doesn\u2019t the set keep items in alphanumerical order? That\u2019s obviously still a problem, but just pointing out that your example in the description might be inaccurate.\n2. What is the size limit of this list of previous MACs? Over a certain size, I would suggest using an ordered dict to get the benefits of both maintaining order and both ~O\\(1\\) search\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/482/_/diff#comment-308153744"}}
{"comment": {"body": "The unordered behavior stems from using union, I stand corrected.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/482/_/diff#comment-308155950"}}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9286)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/482/_/diff#comment-309486545"}}
{"title": "Reopening Bugfix/MEROSP-1016 l2 in decision log", "number": 483, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/483", "body": "This PR re-opens  that was reverted due to automation changes"}
{"comment": {"body": "We need to make sure the automation tests don\u2019t check those fields of the result log, to avoid such future problems. Once those assertions are removed, we can re-merge this PR.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/483/_/diff#comment-308155965"}}
{"title": "Feature/avro namespace", "number": 484, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/484", "body": "add cujo namespace to result schema\nadd namespace to result meta class\nadd namespace to result meta class\nfix api version\n\n"}
{"title": "Feature/MEROSP-823 New services configuration tool", "number": 485, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/485", "body": "Introduce a new feature configuration tool\nThe changes in this PR allow the user to run the match making & type finding services while using a subset of the available features and not all of them. The features that are being used, are referred to as enabled, and those that aren't - disabled. The action of injecting the system with the desired subset, is referred to as 'configurating'.\n\nChange the MatchMaker class to be static with an 'init' function that allows a configuration injection. This way, the 2 Services class are aligned to be both static.\nAdd Configuration & Configurator classes for each service.\nChange the Feature class to hold the knowledge about its mode of operation.\nAdd data collection about features data validity and mode of operation inside the services logic, to the Result modules. This is then transferred to the Intelligence Log module, in order to allow a transparency about the way the system ran its services in retrospective.\nAdd unit tests for the configuration and data collection\n\n\n\nFix rebase from dev\n\nChange session creation in tests to match new api\nRemove captive portal leftovers\nRemove aws param when attempting a connection to dynamodb\nChange .env file to accept all changes from dev\n\n\n\nFix failure in running tests\n\nAdd configuration parameters to .env file\nRevert to previous parameters in connecting dynamodb\nAdd missing parameter in values.yaml\nRemove some unnecessary imports\n\n\n\nRefactor the Feature class to accept a default configuration denoted by its omission\n\nChange the string values for enable/disable to integer 1/0 respectively\nAdd custom exception & unit tests to the id/type matching services\nChange the case sensitivity of the configuration service. It can now accept any case and thus the environment\n  variables in the env files can be given in the upper case convention but any case can be safely processed.\nRevert and align the .env, Makefile & helm deployment values.yaml to 'dev' branch\n\n"}
{"title": "added logic for ingestion events in archiver. added test and docstring where it's not clear how archiver works", "number": 486, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/486", "body": ""}
{"title": "add schema registry serialization/deserialization", "number": 487, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/487", "body": "WIP\nCujo uses Schema Registry to produce and consume messages from Kafka.\nAdded:\n\nability to serialize Kafka messages using schema predefined at the SR\nSR container to docker-compose (ci/local work usage)\nBasic unit test to verify SR functionality \nNew env vars to indicate if to serialize message using SR or not\nNew keys (as mentioned above) to eros-chart values (and to classifier configmap)\n\n"}
{"title": "GLinet Support on Classifier's Side", "number": 488, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/488", "body": "Add glinet to enum of possible platform types\n\n\nAdd logging for use of mp model\n\n\n"}
{"title": "align the schema with cujo - order of values is important", "number": 489, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/489", "body": ""}
{"title": "Ignore vscode helper file", "number": 49, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/49", "body": ""}
{"title": "added new l2 model with GLiNet data", "number": 490, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/490", "body": ""}
{"title": "Feature/MEROSP-967 save all data relating to eve", "number": 491, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/491", "body": "add environment variables of debug save as values to eros-chart and helm deployment\n"}
{"comment": {"body": "@{6265307b185ac200692f9bd9} do you need help to test the values via the REST API?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/491/_/diff#comment-308782308"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/491/_/diff#comment-309279934"}}
{"comment": {"body": "link to passed regression:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9299](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9299){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/491/_/diff#comment-309507410"}}
{"title": "Manual Trigger for eros-api Package Publishing", "number": 492, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/492", "body": "We had trouble with versions being published from almost every push. Make it the dev's responsibility to push a new package version for eros-api, pushing a button in the CI pipeline."}
{"title": "Bugfix/MEROSP-1032 iphone 11 117 context model", "number": 493, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/493", "body": "fixing prior logic and adding unit test\nfixing two similar bugs:\n{: data-inline-card='' } \n{: data-inline-card='' }"}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/9114) :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/493/_/diff#comment-309242680"}}
{"title": "reduce memory of docker image", "number": 494, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/494", "body": ""}
{"title": "Feature/memory linits bitbucket", "number": 495, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/495", "body": "The PR tries to solve the memory issues we have with our UT:\nWhen running pytest it agrgegates all the tests under single run to summarize the report at the end of execution. We also using codecov that is adding graphs of execution for code coverage the test were executed, thus at the end of tests run the memory raises significatly (more then 1G).\nInitial fix is to split the tests to multiple runners (notice pytest-mp wont help as the issue at the summary reports)\nIf this wont fix, we might disable codecov and check further its usage on every UT run.\n"}
{"title": "Removed spamming log that didn't add much from the release_date feature", "number": 496, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/496", "body": "Removed spamming log that didn't add much from the release_date feature. That feature is not that important and anyway this log doesnt add information at all: if we didnt get dhcpv6_timestamp we can just see it in the decision log or in any other way."}
{"title": "new layer2 model data", "number": 497, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/497", "body": "new model from comcast repository\ninclude GLiNet data\n\n"}
{"title": "Feature/align msk branch", "number": 498, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/498", "body": "Merged in feature/memory_linits_bitbucket (pull request #495)\nFeature/memory linits bitbucket\n\nreduce memory of docker image\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into release/cujo_integration_ml1\ntry to change memory limits\ntest memory limits\nrevert total memory\nsplit tests to multiple runners\n\nApproved-by: Ariel Tohar Approved-by: Liat Ackerman\n\n\nMerged in bugfix/MEROSP-1032-iphone-11-117-context_model- (pull request #493)\nBugfix/MEROSP-1032 iphone 11 117 context model\n\nMerge branch 'release/cujo_integration_ml1' into bugfix/MEROSP-1032-iphone-11-117-context_model-\nfixing prior logic and adding unit test\nchanging prior logic to proceed with all iphones\nfixed prior logic to be first\nariel's changes\nMEROSP-1032 MEROSP-1081 Fixing prior logic for iPhone / iPad case, adding canonical_name_set reference\nMEROSP-132 Fixing canonical_name_set for Pixel as well\nMerge branch 'release/cujo_integration_ml1' into bugfix/MEROSP-1032-iphone-11-117-context_model-\nMerge branch 'release/cujo_integration_ml1' into bugfix/MEROSP-1032-iphone-11-117-context_model-\nMEROSP-1032 Typo\n\nApproved-by: Ariel Tohar\n\n\nMerged in feature/MEROSP-1055-classifier-sr (pull request #487)\nadd schema registry serialization/deserialization\n\nrevert split test bitbucket\nremove pytest cov in CI and remain in local\ntry more deep split - test_pipeline_device_classification.py most memory consuming\nsplit e2e tests\ncomment sparrow and merge later with fixed branch\nremove compose down in CI\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into feature/MEROSP-1055-classifier-sr\nfixe merges and comments\nrevert defaults\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into feature/MEROSP-1055-classifier-sr\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov Approved-by: Itai Zolberg\n\n\nMerged in layer2_model_devices_db (pull request #497)\nnew layer2 model data\n\nnew layer2 model data\n\nApproved-by: Ariel Tohar Approved-by: Shimon Goulkarov\n\n\n"}
{"title": "ethernet parser unit test - moved packets to bin files", "number": 499, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/499", "body": "{: data-inline-card='' } \npassed regression"}
{"title": "Integration merges", "number": 5, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/5", "body": "Started setting up the initial code for our packet parsing, with some nice initial unitests\nFixed icmp_ns parameter passing. Added dpkt to the requirements file\nSome modifications to pass flake8 rules\nAdded dhcpv6 parser and tests\nAdded dhcp parser\n67 -> 68\nflake8 fixes\nAdded a first negative test\nAdded a test that will reproduce the bug from PR_729\nSimplified netbios parsing\nAdded a negative test that validates PR 905 fix\nRenamed packet_parser.py -> ethernet_parser.py\nAdded radiotap_parser.py\nSeperated the tests file to test_ethernet_parser.py and test_radiotap_parser.py\nAdded a test to reproduce the bug that was fixed in PR 985. Added more parsing methods to the radiotap parser\nTests name change\nAdded more negative tests regarding to the packet structure\nFixed a bug in icmp parsing and added more unitests\nAdded icmp mclr tests\nDocumentation fix\nAdded SSDP parsing and unitests\ninitial commit\nAdded the main pakcet parser. Updated the relevant tests with the new structure\nChanged to the new tuples format\nImport fixes\nUpdated packet_parser.py with the Session structure\nDocumentation fixes\nTemporarly adding collector_interface.py so the tests will pass\ntest_packet_parser.py -> test_session_parser.py\nFixed test_radiotap_parser.py\nNaming in test_radiotap_parser\nDocumentation\ntyping\nMinor changes and names changing\nMoved the large session example to a specific file usin ggit lfs\nWIP\nadd deserialization + prints\nupdate topic\nget rid of collector dependency for now\nUse namespace to reach kafka service\nClean flask service of deployment\nFix some more namespaces\ntemp prints\ntry sleep\nDon't exit on source start\ncouldn't work with dask, just sleep\nremove obsolete packages\npromote to single package multi-modules\nfix setup installations\nbuild packet_parser and python package\ninstall locally\nlocal dask cluster\nremove obsolete test\nenable local dask cluster\nlint\nlint\nlint\nSimplified packet_parser. Added some include fixes and changed USER_AGENT -> SSDP_USER_AGENT\nNow using get_netbios_transaction_id() instead of copy-pasting the inner implementation\nUpdated to the newer collector_interface.py with betterproto\nCreated a file with a serialized session and added it with git lfs\nRemoved the old session_example file from git-lfs\nRemoved the eros-data-collector linked dependency to see if it solves the docker building problem\nfix lint issues and ignore complexity warning\nignore lfs lint for now\nexclude eggs\nfix flake8 comma for eggs ignore\nfix flake8 path for eggs ignore\n\n"}
{"title": "Dev tools/metrics", "number": 50, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/50", "body": "local kafka\nlocal kafka docker-compose\nadded kafka + cached docker layers + makefile\nimprove build order + clean up\nreturning network flag\nminio changes\nlint + default network\ncached layer fix\nreverting to postgres host\nset host network\nremoved rm line\nchange to default network\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nmetrics decorator WIP\nfix dask prom\nfix metrics instanciation\nfix flake8\ntimeout + input as byte like\nlist of sessions\nflake8\nrelocate pytest.ini\ndisable kafka integrated tests\nmetric fixes\n\n"}
{"title": "Local classifier docker compose", "number": 500, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/500", "body": "fixing run local services\n"}
{"title": "Feature/MEROSP-1110 validate cujos simulated ingestion event", "number": 501, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/501", "body": "This PR adds a test that validates that we deserialize, parse and process the ingestion event we got from Cujo successfully.\nThe ingestion event they sent us provided the following result:\n{\n  \"result_id\": \"9fac1dad-0210-4fde-ae56-360438083508\",\n  \"result_timestamp\": 1655632680.862,\n  \"source_event_id\": \"4fc84bdf-c9f3-467b-bb17-8c35a4c40daf\",\n  \"agent_serial\": \"07cf828d-4075-4dea-8930-aeb3673ca5c5\",\n  \"partner_id\": \"a2c58a72-9ff6-4605-bf0c-8ba293685ab1\",\n  \"vault_id\": \"07cf828d-4075-4dea-8930-aeb3673ca5c5\",\n  \"platform_type\": \"ENG_IPQ6018\",\n  \"has_identification_result\": true,\n  \"has_context_result\": true,\n  \"device_mac_address\": \"b4:1a:1d:d7:a3:12\",\n  \"device_is_attended\": true,\n  \"device_first_mac_address\": \"b4:1a:1d:d7:a3:12\",\n  \"device_ipv4\": \"************\",\n  \"device_ipv6\": \"fd00:db80:0:1234:12f:6ed7:4fc8:ada7\",\n  \"device_hostname\": \"E\",\n  \"device_connectivity_status\": \"connected\",\n  \"device_connectivity_update_ts\": 1655632680.862,\n  \"connection_type\": \"WIRELESS_2_4\",\n  \"connection_bssid\": \"00:03:7f:97:64:6e\",\n  \"connection_essid\": \"24g dedup_reproduce\",\n  \"connection_mode\": \"MODE_11AXG_HE20\",\n  \"connection_channel\": 5,\n  \"identity_levl_id\": \"aa21c188-3653-4ece-a070-11b39b0c4cc1\",\n  \"identity_is_new\": true,\n  \"identity_is_new_mac_address_assigned\": true,\n  \"identity_is_levl_id_dedup\": false,\n  \"identity_deduped_levl_ids\": [],\n  \"context_model_is_details_updated\": true,\n  \"context_model_description\": \"Galaxy A21\",\n  \"context_model_vendor\": \"Samsung\",\n  \"context_model_device_type\": \"Mobile phone\",\n  \"context_model_device_family\": \"Galaxy\",\n  \"context_model_name\": [\n    \"Galaxy A21\"\n  ],\n  \"context_model_resolution\": 5,\n  \"context_os_is_details_updated\": true,\n  \"context_os_description\": \"Android 10\",\n  \"context_os_name\": \"Android\",\n  \"context_os_ver_major\": 10,\n  \"context_os_ver_minor_lower\": 0,\n  \"context_os_ver_minor_upper\": 0,\n  \"context_os_resolution\": 2\n}\nNote that there was an extra \\n in the event they sent us that was probably added by the editor they used to save the binary data in. I removed that extra \\n (which broke the deserialization)."}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10264)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/501/_/diff#comment-311995686"}}
{"title": "erasing the tmp dir before each run, logging to warning", "number": 502, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/502", "body": ""}
{"title": "removing error log for no dhcpv4", "number": 503, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/503", "body": "no DHCP ack is not an error, for example in static ip connection."}
{"comment": {"body": "why removed the errorlog? it should go 1:1 with counter, with same severity \\(level\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/503/_/diff#comment-310730284"}}
{"comment": {"body": "we talked about it. it will always error on static ip", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/503/_/diff#comment-310776413"}}
{"title": "Bugfix/MEROSP-1101 context os / context model fields", "number": 504, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/504", "body": "This PR fixes a problem we currently have in our result log - we use the same indication flag for different fields, which causes us to not fill accurate data.\nThe problem is with the context_model_is_details_updated and context_os_is_details_updated fields - they both relied on the same session.decision.match_result.context_details_updated value.\nBy creating separate flags in the MatchResult, we can fill the right data when performing the typing merge.\nThis PR is expected to break some regression tests, Im now working on the relevant fixes on a dedicated branch in eros-automation."}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10990),\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/504/_/diff#comment-312742017"}}
{"title": "Refactor error reporting", "number": 505, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/505", "body": "This PR introduces a new module - \"ErrorReporter\" that encapsulates the calls to the interfaces that are used to collect and externalize the error events that occur in the classifier. The API of ErrorReporter which includes three methods that reflects the level of error being reported, allow to choose to update the metrics, and log the error with 3 levels of severity: WARNING/ERROR/FATAL to the logging module and the DeviceIntelligenceErrorLog. It can also accept an exception if one occurred and log its trace to the DeviceIntelligenceErrorLog.\nThe main changes are the removal of explicit calls to the reporting interfaces and their replacement by the ErrorReporter API.\nThe value of this encapsulation is a greater control over the error report and a better modularity for this process."}
{"comment": {"body": "@{6252eba45d1e700069ad0104} @{5dbeb866c424110de52552cc} I like the approach. Did you considered changing all our logging usage with this wrapper?this will help to advance to more formatted structure, option to disable log levels, even per some key \\(for example, vault\\_id\\)..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/505/_/diff#comment-312049259"}}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/4186113892-carl-chef-kiss.gif)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/505/_/diff#comment-312089891"}}
{"comment": {"body": "Did you want to have a comment here to explain this? Or is this clear enough?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/505/_/diff#comment-312090233"}}
{"comment": {"body": "Great job leveraging a bug report to an overall improvement in the system", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/505/_/diff#comment-312143584"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} I think that because this is a very typical usage and a short one of the \u2018partialmethod\u2019 pythonic tool, plus the region comment that define these lines as the public API, there is no need for further explanations. If you can think of a comment that will make this clearer, I don't mind adding one.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/505/_/diff#comment-312603095"}}
{"comment": {"body": ":smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/505/_/diff#comment-312603176"}}
{"title": "add support for msk vpc peering env", "number": 506, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/506", "body": ""}
{"comment": {"body": "sure we want hard coded value here?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/506/_/diff#comment-311894534"}}
{"title": "Fixing vendor_ouis_20722_8", "number": 507, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/507", "body": "fixed vendor_ouis_20722_8\nnew data\n\n@{5dbeb866c424110de52552cc} need to fix feature extractor  \nfixing the following issue:\n{: data-inline-card='' }"}
{"title": "Feature/msk split redesign", "number": 508, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/508", "body": "add support for msk vpc peering env\nadd option to run regression on cujo env\n[skip ci] bumped eros-chart new version 0.39.0\nadd result topic to config ,ap\nadd result topic to config ,ap\n[skip ci] bumped eros-chart new version 0.40.0\nadd ssl flag to default producer\nredesign msk split\n\n"}
{"comment": {"body": "Can you explain?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/508/_/diff#comment-311946316"}}
{"comment": {"body": "[https://levltech.atlassian.net/browse/MEROSP-1135](https://levltech.atlassian.net/browse/MEROSP-1135){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/508/_/diff#comment-312596606"}}
{"title": "removing error log when ip is static", "number": 509, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/509", "body": "no need to error over each static ip connection. we get the ipv4 from arp on static connection. if there is no arp or DHCP we alert. also we warn when there is no DHCP"}
{"comment": {"body": "Just making sure - is the `NO_IP_INDICATION` error used correctly to cover the cases when no DHCP or ARP data was available to retrieve IPv4 addresses?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/509/_/diff#comment-312009386"}}
{"comment": {"body": "error log also should be raised here with warning level", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/509/_/diff#comment-312050881"}}
{"comment": {"body": "there are two occurrences of `NO_IP_INDICATION`. one is checking that there is at least one of DHCP or ARP, the other one I removed.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/509/_/diff#comment-312298301"}}
{"comment": {"body": "why? it is not an error, I would remove the warning as well.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/509/_/diff#comment-312327586"}}
{"title": "Dev tools/per_developer_environment", "number": 51, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/51", "body": "This PR need to be reviewed after dev_tools/metrics is merged to dev\nthe delta (compared to dev_tools/metrics)  is only per_developer_environment\n\nlocal kafka\nlocal kafka docker-compose\nadded kafka + cached docker layers + makefile\nimprove build order + clean up\nreturning network flag\nminio changes\nlint + default network\ncached layer fix\nreverting to postgres host\nset host network\nremoved rm line\nchange to default network\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nmetrics decorator WIP\nfix dask prom\nfix metrics instanciation\nfix flake8\ntimeout + input as byte like\nlist of sessions\nflake8\nrelocate pytest.ini\ndisable kafka integrated tests\nmetric fixes\n\n"}
{"title": "Health check", "number": 510, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/510", "body": "adding health_check\nbuild fix\nfix vault_id, added schema registry to health check\nfix test\n\n"}
{"title": "change external dependency of eros-api to a local one (pip install -e api)", "number": 511, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/511", "body": "change external dependency of eros-api to a local one (pip install -e api)"}
{"comment": {"body": "@{5fd5d5149edf2800759cc96d} Is this PR still relevant?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/511/_/diff#comment-312577872"}}
{"title": "MEROSP-1034 Added a test that validates that the bug won't reproduce", "number": 512, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/512", "body": "This PR adds two things:\n\nMEROSP-1034: Adding a test regarding our POCO device in the lab, which was wrongly identified as a Note device. The test should enforce that this wont happen again (we used to have many problems with the POCO device specifically, lets add this test as a life guard).\nFixing another test that used wrong syntax and will cause pipelines failures\n\n"}
{"title": "S3 Sessions saving fix", "number": 513, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/513", "body": "Now saving the relevant part from the msg to s3, the API was not updated since we started using the KafkaMsg object"}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/10672)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/513/_/diff#comment-*********"}}
{"comment": {"body": "Good catch!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/513/_/diff#comment-*********"}}
{"title": "release/cujo_integration_ml1 merge into dev", "number": 514, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/514", "body": "Just the merge with conflict resolving.\nAutomation has a parallel PR.\nUnit tests + regression passed."}
{"title": "fixing ipv6 length and adding unit tests", "number": 515, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/515", "body": ""}
{"title": "Fixed the path to which we write the decision logs in S3", "number": 516, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/516", "body": "We had a problem in the path which we write the decision logs to in S3. The problem was that we took the same part of the string for both the topic and the schema name (both used index 0). It caused a wrong S3 path to be created.\nFor example, the path was:\neros-prod.levl.datalake/dogfood/v1/topic=dogfood\nInstead of:\neros-prod.levl.datalake/decision_logs/v1/topic=dogfood\nIn addition, the version of the path needs to be updated to a newer version, due to the latest types changes in the decision log schema - V1 is no longer valid for us to parse."}
{"title": "hotfix/fix-mdns", "number": 517, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/517", "body": "hotfix/fix-mdns"}
{"comment": {"body": "Hey, should this be merged or declined?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/517/_/diff#comment-317513223"}}
{"title": "Dont shadow python builtins", "number": 518, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/518", "body": ""}
{"comment": {"body": "Thanks for that!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/518/_/diff#comment-312423226"}}
{"comment": {"body": "you\u2019re welcome", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/518/_/diff#comment-312424715"}}
{"title": "S3 fixes", "number": 519, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/519", "body": "Those changes were already approved and merged here and here :slight_smile: They were brought back to this PR after the revert that had to be done to fix the git issues"}
{"title": "Dev", "number": 52, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/52", "body": "Add more DB fields\nNew fields\nAdd more filters to bypass\nand now with dhcp_requested_ip\nadd bonjour and icmp6 ns features\nAdd matching and integration test with android device\nFix types\nflake8\nadapt parser to device session interface\nraise to 50msec\nadd bonjour and ICMP ns feature extraction unittests\nadded snapshot deployment\nfix typo\nMerge with new interface\nFix tests and formatting\nArchiver in fixture\nSSDP is Linux fix\nSSDP is Linux fix\nfixed topic\nerror handling + logs\nNow parsing DHCP ACK messages as well, mistakenly forgotten\nfixed logs\nadd logs and enable logging info\nadd logs and enable logging info\nImproved the choosing procedure of the parsing method\nForgot NB port\nlogging not used\nRemoved unnecessery lambda\nRemoved unnecessery print\nWrong key fix\nCreate db package\nKeep moving files around\nMove around\nMatching query will return the matching features\nAdd os filtering\nExtract mac address from session\nlocal changes\nFix test with DB cleanup setup Fix OS usage\nFix flake8 and icmpns\nAnother format\nStandartize Android OS Levl ID is 128-bit UUID\nDB: add last update DHCPv6: add windows flow\nAdd windows matching unittest\nFix tests\nWIP\nhistory devices\nLoad android device repository to DB and add query\nremove that file uploads until it's tested\nadd metrics decorator\nAdd non-random device name for windos and android with tests Store hostname in DB\nfix ci errors\nClear comments for flakey flake\nextract metrics to utils py\nmetrics decoration on all pipelines\nbug fix\nFix DHCPv4 IP usage Use DHCPv4 requested IP feature\nCleanup files\nCollect metrics in matching\nFix DHCPv4 requested IP usage\nadd history devices\nadd history devices - fix flake8\nadd history devices - fix cr\nFix test\nimpl\nFix DB create\ndon't use metrics\nRemove metrics\nimpl\nCreate DeviceHistory if not exist\nFormat file\nfix lint issues\nautomation ennvironment\nexecute pipeline as dask distributed\nadded deploy rollout\nnotifications\nMerged dev into dev_tools/distributed\nextract decision to json file\nrename notification\nremove bad tests\nrefator the serialization of device session as member function\nfix flake8\nfix tests with dask\nadding more functionality for pull and put data archiver by path &adding a test in test archaive using path\nsqs integration\nadd logging\nadd integration env\nyaml fix\nfix integration pipeline\nformat decisions list\nhack notification metrics\nmark out tests until local kafka dependency is fixed\n30 seconds timeout per integration test\nsolving some comments for the previous pull request\nRevert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\"\nskip integration test\nchange error logging to info\nadd kafka callback\nparse_session() does not longer belong to a class, it's stateless\nfix radio tap set() serialization\nfix radio tap set() serialization\n\nMerged in dev_tools/local_kafka (pull request #44)\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/decision_log (pull request #46)\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\n\nfix deployment decision snapshot\n\n\nMerged in amir_archiver (pull request #39)\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\n\n\nMerged in feature/gitignore_vscode (pull request #49)\nIgnore vscode helper file\n\nignore vscode helkper file\n\nApproved-by: Tamir Raz\n\n\nMerged in dev_tools/metrics (pull request #50)\nDev tools/metrics\n\nfix dask prom\nfix metrics instanciation\nfix flake8\ntimeout + input as byte like\nlist of sessions\nflake8\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev_tools/metrics\nrelocate pytest.ini\ndisable kafka integrated tests\nmetric fixes\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in dev_tools/per_developer_environment (pull request #51)\nDev tools/per_developer_environment\n\nMerge branch 'dev_tools/metrics' of bitbucket.org:levl/eros-classifier into dev_tools/per_developer_environment\ndeploy parallely\nuse summary instead of Histogram to measure duration\nflatten analytics\nuse histogram again\ntest\nrename\nrename2\nsummary histogram test\nrename\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in vault_id (pull request #48)\nupdate schemas to contain tenant and vault id\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into vault_id\nupdate schemas to contain tenant and vault id\nadded tenant and vault from session\nfix some tests\nchanges\nMerge branch 'dev' into vault_id\ntests fix\ntests fix\nadded test helper\nremoved insert to vaults\n\nApproved-by: Shimon Goulkarov\n\n\nhotfix db host\n\n\n"}
{"title": "fix radiotap iter_information_elements and added radiotap flags", "number": 520, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/520", "body": "The iter_information_elements function of the radiotaps parsed the FCS at the end of the packet as if it was IE. This created extra l2 model signatures with false information element.\nsame as "}
{"comment": {"body": "@{5dbeb866c424110de52552cc} @{5d74d49897d8980d8eacd7f8} we should retarget to release/cujo ml1 branch? does the pilot L2 model builder also fixed this?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/520/_/diff#comment-312800719"}}
{"comment": {"body": "yes,  but first we need to merge to comcast repo and then it will effect to l2 model builder, but i don\u2019t have the \u201cmerge\u201d button on comcast repo so i need someone to do it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/520/_/diff#comment-313005308"}}
{"title": "MEROSP-1133: vendor ouis fix", "number": 521, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/521", "body": "{: data-inline-card='' }\n{: data-inline-card='' }"}
{"title": "eros-data-collector version to 0.10.2", "number": 522, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/522", "body": "passed regression:\n{: data-inline-card='' }"}
{"comment": {"body": "`__hash__` of enum isn't already implemented?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/522/_/diff#comment-312979127"}}
{"comment": {"body": "it solved an error. looked at stackoveflow:  \n[https://stackoverflow.com/questions/1608842/types-that-define-eq-are-unhashable](https://stackoverflow.com/questions/1608842/types-that-define-eq-are-unhashable){: data-inline-card='' }   \napparently if you define `__eq__` you need to define `__hash__` as well", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/522/_/diff#comment-312992342"}}
{"comment": {"body": "it a huge refactor..\n\nbit hard to review it\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/522/_/diff#comment-313479252"}}
{"comment": {"body": "sorry. it\u2019s mainly removing stuff and adapting to the new structs.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/522/_/diff#comment-313481174"}}
{"title": "Feature/create auto deployment MEROSP-1125", "number": 523, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/523", "body": "This PR support multiple MSK configurations for CUJO deployment environments.\nThis is enhancement of the feature/msk_split_redesign PR branch.\nWe have multiple kafka_addr for production topics and separated for logs, kinesis firehose stream sinks and separate configuration for kafka authentication and topics."}
{"comment": {"body": "why did you change the tests files like \u201ciphone12\\_#11\u201d?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/523/_/diff#comment-313014643"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} i wasnt the scope of the PR \n\nwhats change is required here?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/523/_/diff#comment-313464616"}}
{"comment": {"body": "revert all binary files", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/523/_/diff#comment-313465262"}}
{"comment": {"body": "we didn\u2019t, all test files were updated to lfs, and some were missing..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/523/_/diff#comment-313466686"}}
{"comment": {"body": "wdym?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/523/_/diff#comment-313466701"}}
{"comment": {"body": "`tests_utils/resources/iphone12_#111/connection.batch.gz` for example should not be changed in this PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/523/_/diff#comment-313481258"}}
{"title": "[Snyk] Security upgrade tensorflow/tensorflow from 2.7.0 to 2.8.2", "number": 524, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/524", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nDockerfile\n\nWe recommend upgrading to tensorflow/tensorflow:2.8.2, as this image has only 40 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 586  | CVE-2022-32208 SNYK-UBUNTU2004-CURL-2936016   | No Known Exploit   |\n|    | 586  | CVE-2022-32206 SNYK-UBUNTU2004-CURL-2936028   | No Known Exploit   |\n|    | 514  | CVE-2022-1271 SNYK-UBUNTU2004-GZIP-2442549   | No Known Exploit   |\n|    | 678  | Loop with Unreachable Exit Condition (Infinite Loop) SNYK-UBUNTU2004-OPENSSL-2426343   | No Known Exploit   |\n|    | 586  | CVE-2022-2068 SNYK-UBUNTU2004-OPENSSL-2933120   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Flake8 return", "number": 525, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/525", "body": "applying flake8 return values check\nadded colors to flake8\nadded return values everywhere\n\npassed regression:\n{: data-inline-card='' }"}
{"comment": {"body": "Isn\u2019t it that just the `return None` should be outside of the loop?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/525/_/diff#comment-313471138"}}
{"comment": {"body": "Does R504 happen a lot? Or in parts we don\u2019t have control over?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/525/_/diff#comment-313471288"}}
{"comment": {"body": "87 times. it\u2019s not really a bug in my humble opnion.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/525/_/diff#comment-313471465"}}
{"comment": {"body": "inside the for there was always a return on the first iteration.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/525/_/diff#comment-313471513"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} Exactly, I think that\u2019s a bug. I think that it was supposed to be: return the fingerprint if data exists in any of the packets \\(not necessarily the first one\\), else return None.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/525/_/diff#comment-313473382"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} fixed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/525/_/diff#comment-313474575"}}
{"title": "periodically merge new stuff from release/cujo to dev", "number": 526, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/526", "body": ""}
{"title": "MEROSP-1029 Bugfix/LG6 Typing", "number": 527, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/527", "body": "This PR solves MEROSP-1029, by updating the model with the missing data. In addition, a test that reproduces the problem is added"}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/11755)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/527/_/diff#comment-313478126"}}
{"title": "Strict matches performance", "number": 528, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/528", "body": "breaking l2 device typing to smaller functions\nadded profiling to save debug data\n\n"}
{"comment": {"body": "local regression passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/528/_/diff#comment-315540982"}}
{"title": "added flake8-comprehensions plugin", "number": 529, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/529", "body": ""}
{"title": "change pipeline name init", "number": 53, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/53", "body": "\n\nchange pipeline name init\n\nthis will fix the issue with deploying tenant.\npipeline name was determined from an ENV property called PIPELINE\nsince we are using configmaps for all tenants, we cant have multiple PIPELINE properties with name for each classifier pipeline.  \nthe solution I used it to determine the pipeline name from the container HOSTNAME.\n"}
{"title": "dont ignore flake8 on unit tests", "number": 530, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/530", "body": ""}
{"title": "Feature/MEROSP-829 features configuration tool", "number": 531, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/531", "body": "This PR Introduces a new feature configuration tool\nThe changes in this PR allow the user to run the match making & type classifiyng services while using a subset of the available features and not all of them. The features that are being used, are referred to as enabled, and those that aren't - disabled. The action of injecting the system with the desired subset, is referred to as giving a configuration.\n\nChange the MatchMaker class to be static with an 'init' function.\n  This way, the 2 Services class are aligned to be both static.\nAdd Configuration & Configurator classes for each service.\nChange the Feature class to hold the knowledge about its mode of operation.\nAdd data collection about features data validity and mode of operation inside the services logic, to the Result modules. This is then transferred to the Intelligence Log module, in order to allow a transparency about the way the system ran its services in retrospective.\n\nThis configuration tool architecture was designed with the intention to better support configurations of other modules inside the classifier in the future and the ability to change these configurations at runtime, while collecting and exposing these configurations through the Logs that our system produces.\nFor the moment, a configuration for a given module is treated as having 2 main sections:\n1. Features configuration: an indication for each feature to be (or not to be) considered inside the internal logic of the module.\n2. General configuration: settings that are custom to the module.\nIn the future, this definition is likely to change but at the moment satisfies the requirements.  \nA default configuration is produced for each module and each feature(default mode of operation), which allows the user to run the classifier services without explicitly declaring the desired configuration if the default one is satisfactory.  \n\n\nChanges:\n\nRename the 'DeviceTypeIdentification' module to 'TypeClassifier' along with all the name appearances that pertains to this module in the code.\nChange the configuration architecture of MatchMaker & TypeClassifier: - Considering the possibility of configuration for other parts in the classifier in the future, an abstract class, 'ConfiguarbleModule' was added, alongside a general 'ModuleConfigurator' and 'ModuleConfiguration'. When a module inherits the ConfiguarbleModule, it defines a configurator class that allow to create and update its configuration. The configurations for the aformentioned modules where added as children classes to the ModuleConfiguration & ModuleConfigurator classes.\nThe source for a configuration for all the modules can be given as a dictionary or read from environment variables. The module responsible to accept this source, parse it and distribute it to all the modules was added, AKA 'ConfigurationDistributor'.\nThe init function for the MatchMaker & TypeClassifier were separated from the creation of configuration, to allow better control in the future, when attempting to change the configuration dynamicaly at runtime.\nAdd the metadata about the configuration: - In order to know if a configuration was explicitly given or the default one was used, a corresponding field was added inside the configuartion class which relates to the general section of the configuration (everything that is not related to the features in the module). For each feature, a field that denotes whether the mode of opration was chosen by default or explicitly was added as well. The relevant fields were added to the avro schema in a fashion that allows to know how the features were configured after the classification is performed.\nAdditional \"FeatureTag\" classes, 'MainFeature' & 'FilterTypeFeature' were added, in order to identify the main features and filtering features inside the TypeClassifier. In the future, as part of a bigger refactoring, the feature classes might have fields that denote these characteristics instead of multiple inheritance.\nAll the relevant unit tests were added or refactored to accept the new configuration API.\n\n\n\n"}
{"title": "Hotfix/Fixing enum values comparison", "number": 532, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/532", "body": "All the sessions got N_A identification level, which was caused by the way we compared values from the EventType enum. This PR fixes it in order to get correct identification levels and fix some weird metrics behaviour we got from it."}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12374)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/532/_/diff#comment-313795937"}}
{"title": "Release/cujo integration ml1", "number": 533, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/533", "body": "Merged in lg6_typing (pull request #527)\nMEROSP-1029 Bugfix/LG6 Typing\n\nMEROSP-1029 Updated the model and added a test that reproduces that problem\n\nApproved-by: Tamir Raz Approved-by: Ophir Carmi\n\n\nMerged in bugfix/identification_levl_na (pull request #532)\nHotfix/Fixing enum values comparison\n\nFixing enum values comparison\n\nApproved-by: Tamir Raz Approved-by: Liat Ackerman\n\n\nMerged in feature/create-auto-deployment-MEROSP-1125 (pull request #523)\nFeature/create auto deployment MEROSP-1125\n\nfix kafka addr from prod kafka addr\nfix topic split for conenctors\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into feature/create-auto-deployment-MEROSP-1125\nparse_connection_event try/except block\nMerge branch 'feature/create-auto-deployment-MEROSP-1125' of bitbucket.org:levl/eros-classifier into feat ure/create-auto-deployment-MEROSP-1125\nset cluster name without suffix\n[skip ci] readme file\nparse result log\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into feature/create-auto-deployment-MEROSP-1125\nMerge branch 'feature/create-auto-deployment-MEROSP-1125' of bitbucket.org:levl/eros-classifier into feature/create-auto-deployment-MEROSP-1125\n\nApproved-by: Nadav Livni Approved-by: Itai Zolberg\n\n\n[skip ci] added kafka topics flag\n\n\nMerged in bugfix/agent_serial_export (pull request #544)\nbug fix\n\nbug fix\nadded agent serial and pid to tests\nfixed DeviceIntelligenceLog tenant id type to str\n\nApproved-by: Ariel Tohar Approved-by: Itai Zolberg\n\n\nMerged in MEROSP-1154 (pull request #539)\nMEROSP-1154\n\nmoving files\nmore\nadding radiotap unit test files\nfixing band selection in radiotap\nremoving lfs track from main file and moving to other file\nfixing models\nPR comment about logging format\nmore PR comments\nMerged release/cujo_integration_ml1 into MEROSP-1154\n\nApproved-by: Tamir Raz Approved-by: Dror Bendet\n\n\nMerged in bugfix/MEROSP-1174-fix-identification-level-cal (pull request #538)\nBugfix/MEROSP-1174 fix identification level calculation\n\nMEROSP-1174 Fixed the identification level calculation, and made sure it's calculated even if it's a new device (as it should)\nMEROSP-1174 Added some documentation\nMEROSP-1174 Documentation fix\nMEROSP-1174 Updated arp_ipv4 feature's statistical grade so the identification level calculation won't break\nMEROSP-1174 Updated Dhcpv6Ipv6 class as well with the statistical grade\nMerge branch 'release/cujo_integration_ml1' into bugfix/MEROSP-1174-fix-identification-level-cal\nMEROSP-1174 tests fix - logs_topics initialization\n\nApproved-by: Tamir Raz Approved-by: Ophir Carmi\n\n\n[skip ci] rod_in_the_barrel now works for all envs\n\n\nMerged in local-regression (pull request #548)\nadded make command for running regression locally\n\nadded make command for running regression locally\nMerged release/cujo_integration_ml1 into local-regression\nremove debuging lines from make command\nMerge branch 'local-regression' of bitbucket.org:levl/eros-classifier into local-regression\n\nApproved-by: Tamir Raz Approved-by: Artur Aharonovich Approved-by: Ophir Carmi\n\n\n"}
{"title": "Test radiotap parser refactor", "number": 534, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/534", "body": "moving ethernet bin files\nrefactor of test radiotap parser\n\n"}
{"comment": {"body": "Thinking about it, why do we name specific files instead of wildcard?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/534/_/diff#comment-314453295"}}
{"comment": {"body": "I don\u2019t think it works", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/534/_/diff#comment-314453912"}}
{"title": "fixed band parsing", "number": 535, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/535", "body": ""}
{"comment": {"body": "Need to check in comcat repository before merging", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/535/_/diff#comment-314437995"}}
{"title": "adding flake8-commas", "number": 536, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/536", "body": ""}
{"title": "Feature/MEROSP-1145 2", "number": 537, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/537", "body": "runtime profiler as conditional decorator added, decorate only when setting the environment variable EROS_PROFILE to true.\nadded one test to show the usage.\nstarting to break matching to functions.\nrefactor metrics function\n"}
{"title": "Bugfix/MEROSP-1174 fix identification level calculation", "number": 538, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/538", "body": "We had two problems in the way we calculated the identification level:\n\nThe identification level was calculated only for existing devices. If a new device connected, we left the identification level as N_A, since the parameter that was sent to the calculating method was the list of matching feature. In the case of a new device that list is empty. So the first fix was to send that method the list of extracted features of that device, which should be the indication for our identification level in case of a new device.\nFor existing devices, the for loop caused some trouble because the arp_ipv4 and dhcpv6 features didnt have the statistical_grade member.\n\nIn addition, some tests were edited to assert that this wont break.\n"}
{"comment": {"body": "Can\u2019t there be features in `evt_features` that are invalid?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/538/_/diff#comment-314978161"}}
{"comment": {"body": "Maybe assign to `identification_level` once outside the `if..else`? I think a previous version of the code did that", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/538/_/diff#comment-314978264"}}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13634)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/538/_/diff#comment-315291263"}}
{"comment": {"body": "They are all valid :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/538/_/diff#comment-315470712"}}
{"comment": {"body": "I preferred to leave it that way since the calculation is different for each case :slight_smile: \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/538/_/diff#comment-315470857"}}
{"title": "MEROSP-1154", "number": 539, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/539", "body": "{: data-inline-card='' } \n\n"}
{"comment": {"body": "This untyped tuple returned here is jarring to me. Do you think it makes sense to have a dataclass for that?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/539/_/diff#comment-314992617"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/539/_/diff#comment-315163380"}}
{"title": "Dev", "number": 54, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/54", "body": "Add more DB fields\nNew fields\nAdd more filters to bypass\nand now with dhcp_requested_ip\nadd bonjour and icmp6 ns features\nAdd matching and integration test with android device\nFix types\nflake8\nadapt parser to device session interface\nraise to 50msec\nadd bonjour and ICMP ns feature extraction unittests\nadded snapshot deployment\nfix typo\nMerge with new interface\nFix tests and formatting\nArchiver in fixture\nSSDP is Linux fix\nSSDP is Linux fix\nfixed topic\nerror handling + logs\nNow parsing DHCP ACK messages as well, mistakenly forgotten\nfixed logs\nadd logs and enable logging info\nadd logs and enable logging info\nImproved the choosing procedure of the parsing method\nForgot NB port\nlogging not used\nRemoved unnecessery lambda\nRemoved unnecessery print\nWrong key fix\nCreate db package\nKeep moving files around\nMove around\nMatching query will return the matching features\nAdd os filtering\nExtract mac address from session\nlocal changes\nFix test with DB cleanup setup Fix OS usage\nFix flake8 and icmpns\nAnother format\nStandartize Android OS Levl ID is 128-bit UUID\nDB: add last update DHCPv6: add windows flow\nAdd windows matching unittest\nFix tests\nWIP\nhistory devices\nLoad android device repository to DB and add query\nremove that file uploads until it's tested\nadd metrics decorator\nAdd non-random device name for windos and android with tests Store hostname in DB\nfix ci errors\nClear comments for flakey flake\nextract metrics to utils py\nmetrics decoration on all pipelines\nbug fix\nFix DHCPv4 IP usage Use DHCPv4 requested IP feature\nCleanup files\nCollect metrics in matching\nFix DHCPv4 requested IP usage\nadd history devices\nadd history devices - fix flake8\nadd history devices - fix cr\nFix test\nimpl\nFix DB create\ndon't use metrics\nRemove metrics\nimpl\nCreate DeviceHistory if not exist\nFormat file\nfix lint issues\nautomation ennvironment\nexecute pipeline as dask distributed\nadded deploy rollout\nnotifications\nMerged dev into dev_tools/distributed\nextract decision to json file\nrename notification\nremove bad tests\nrefator the serialization of device session as member function\nfix flake8\nfix tests with dask\nadding more functionality for pull and put data archiver by path &adding a test in test archaive using path\nsqs integration\nadd logging\nadd integration env\nyaml fix\nfix integration pipeline\nformat decisions list\nhack notification metrics\nmark out tests until local kafka dependency is fixed\n30 seconds timeout per integration test\nsolving some comments for the previous pull request\nRevert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\"\nskip integration test\nchange error logging to info\nadd kafka callback\nparse_session() does not longer belong to a class, it's stateless\nfix radio tap set() serialization\nfix radio tap set() serialization\n\nMerged in dev_tools/local_kafka (pull request #44)\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/decision_log (pull request #46)\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\n\nfix deployment decision snapshot\n\n\nMerged in amir_archiver (pull request #39)\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\n\n\nMerged in feature/gitignore_vscode (pull request #49)\nIgnore vscode helper file\n\nignore vscode helkper file\n\nApproved-by: Tamir Raz\n\n\nMerged in dev_tools/metrics (pull request #50)\nDev tools/metrics\n\nfix dask prom\nfix metrics instanciation\nfix flake8\ntimeout + input as byte like\nlist of sessions\nflake8\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev_tools/metrics\nrelocate pytest.ini\ndisable kafka integrated tests\nmetric fixes\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in dev_tools/per_developer_environment (pull request #51)\nDev tools/per_developer_environment\n\nMerge branch 'dev_tools/metrics' of bitbucket.org:levl/eros-classifier into dev_tools/per_developer_environment\ndeploy parallely\nuse summary instead of Histogram to measure duration\nflatten analytics\nuse histogram again\ntest\nrename\nrename2\nsummary histogram test\nrename\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in vault_id (pull request #48)\nupdate schemas to contain tenant and vault id\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into vault_id\nupdate schemas to contain tenant and vault id\nadded tenant and vault from session\nfix some tests\nchanges\nMerge branch 'dev' into vault_id\ntests fix\ntests fix\nadded test helper\nremoved insert to vaults\n\nApproved-by: Shimon Goulkarov\n\n\nhotfix db host\n\nchange DASK default value in code to false\nhot fix uuid to str\n\nMerged in hotfix_get_pipeline_configuration (pull request #53)\nchange pipeline name init\n\nchange pipeline name init\n\nApproved-by: Shimon Goulkarov\n\n\n"}
{"title": "added statistics to runtime perfromance", "number": 540, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/540", "body": ""}
{"title": "Test bug merosp-1031 and add a manual test tool", "number": 541, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/541", "body": "Add a test to check the identity_is_new_mac_address_assigned attribute for the case of a Galaxy A21 connection and disconnection events in a sequence. Since the test passes, and several changes were made to the code since the bug was opened, it is considered as resolved.\nAdd a script to update the whitelist.txt file when the flake8 complains about misspelled words that did not choose to be born this way and cannot be changed. After calling the script ./update_white_list.sh word1 word2 word3 the warnings will be supressed.\nAdd a manual test tool to run session files and check the result.\n  ` Inject session files through the DEVICE_CLASSIFICATION pipeline and check the result. The file(s) can be injected through the IDE interface rather than being written explicitly inside the code. A result JSON file can be given as well to compare the actual results with the expected ones.\n\nThe available options are inserted in the same line where the 'additional arguments' reside (in Pycharm), and they are:\n\n\n--dir=\"my directory path\"\n\nuse the --dir option to select a directory that all the session files will be taken from. In order to force a certain order of injection, the files should be named in a lexicographical order. e.g. 1_iphone_connection_event.bin 2_iphone_ongoing_event.bin 3_iphone_disconnection_event.bin. This can also be used as a way to document the order of events that caused the bug.\nThe expected sessions files types are '.gz' and '.bin' and thus the names of the files are expected to end with either one or the other.\nexample: --dir=\"tests_utils/resources/merosp-1031\"\n\n\n\n--name=\"file name or path\"\n\nuse the --name option to inject a single file explicitly.\nexample: --name=\"tests_utils/resources/merosp-1031/connection_event.gz\"\n\n\n\n--res=\"path to result.json file\"\n\nuse the --res option to inject a result json file and edit the compare_attrs list to select which parameters will be compared from the json file.\nexample: --res=\"tests_utils/resources/merosp-1031/result_log_connect.json\"\n\n\n\n** Can also be used as a template to new unit tests for new bugs.\n4. Remove or replace case sensitive words from the white words list, due to flake8 policy of case insensitivity regarding the words in the white list.\n5. Change Makefile test local regression option to test against the dev branch.\n6. Fix flake8 styling -> Multi-line container\n7. Divide the test_pipeline_device_classification file due to length exceeding the allowed length.\n    The utils methods were placed in a more appropriate file, test_utils.py.\n8. Attempt to satisfy the flake8 F811 complaints regarding the file_list fixture, ending up supressing it to maintain sanity.\n"}
{"title": "Feature/MEROSP-1177 preprocess   create dynamo d", "number": 542, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/542", "body": "starting populate vaults for tests\nadded three unit test with preprocess of populating vaults\n\n"}
{"comment": {"body": "regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16150](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16150){: data-inline-card='' } \n\n1 known test failed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/542/_/diff#comment-317278327"}}
{"title": "Periodic release/cujo_integration_ml1 to dev", "number": 543, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/543", "body": "Merged in lg6_typing (pull request #527)\nMEROSP-1029 Bugfix/LG6 Typing\n\nMEROSP-1029 Updated the model and added a test that reproduces that problem\n\nApproved-by: Tamir Raz Approved-by: Ophir Carmi\n\n\nMerged in bugfix/identification_levl_na (pull request #532)\nHotfix/Fixing enum values comparison\n\nFixing enum values comparison\n\nApproved-by: Tamir Raz Approved-by: Liat Ackerman\n\n\nMerged in feature/create-auto-deployment-MEROSP-1125 (pull request #523)\nFeature/create auto deployment MEROSP-1125\n\nfix kafka addr from prod kafka addr\nfix topic split for conenctors\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into feature/create-auto-deployment-MEROSP-1125\nparse_connection_event try/except block\nMerge branch 'feature/create-auto-deployment-MEROSP-1125' of bitbucket.org:levl/eros-classifier into feat ure/create-auto-deployment-MEROSP-1125\nset cluster name without suffix\n[skip ci] readme file\nparse result log\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into feature/create-auto-deployment-MEROSP-1125\nMerge branch 'feature/create-auto-deployment-MEROSP-1125' of bitbucket.org:levl/eros-classifier into feature/create-auto-deployment-MEROSP-1125\n\nApproved-by: Nadav Livni Approved-by: Itai Zolberg\n\n\n"}
{"title": "bug fix", "number": 544, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/544", "body": "probably lost in one of the merges.. we are sending vault id instead of agent serial"}
{"title": "adding flake8-pie", "number": 545, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/545", "body": ""}
{"comment": {"body": "regression passed locally", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/545/_/diff#comment-315551358"}}
{"title": "Release cujo ml1 to dev 20220712", "number": 546, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/546", "body": ""}
{"title": "Release cujo ml1 to dev", "number": 547, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/547", "body": ""}
{"comment": {"body": "local regression passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/547/_/diff#comment-315547893"}}
{"title": "added make command for running regression locally", "number": 548, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/548", "body": "This Make command will enable devs to run regression locally.\n\nWhen executed, the logs will be available from both cli and in report portal with the following format for the run:\nregression_{HOSTNAME}_classifier_branch:{CLASSIFIER_BRANCH}\nSo everyone can identify the regression run"}
{"title": "Fix wrong Kafka & Zookeeper addresses", "number": 549, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/549", "body": "Swagger API failed to work due to wrong addresses in the values.yaml file that is being used as template.\nreplace_values.py should work after this fix."}
{"title": "Bug/feature none exception", "number": 55, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/55", "body": "ignore vscode helkper file\nfix exception on features filter in case None\n\n"}
{"title": "Feature/MEROSP-811 hostname uniqueness feature", "number": 550, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/550", "body": "This PR adds more logic and improves our current feature of NonrandomDeviceName.\nUp until now, after extracting the hostname from the session, we didnt use a smart check of whether it matches another hostname, and just used the startswith to get a slight indication.\nFrom now on, each hostname will have its group level, which allows us to compare hostnames by their uniqueness and make smarter decisions.\nFor example, if an Android user upgrades its OS version, the hostname might change from Franks-MBP to Frank-s-MBP. We can know classify them as the same device by knowing that this change was not made by a human-being.\nOn the other hand, other hostnames were set by the user itself, even after an upgrade. In that case, well know to determine about whether they match or not.\nFurther documentation regarding this feature can be found here.\nMore unitests to be added later on, Im opening this PR anyway for feedback and review for now.\nNote that our current implementation uses regexes to determine the classification level of each hostname. We might modify it if we get to bad performance in run-time."}
{"title": "added flake8-simplify again", "number": 551, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/551", "body": ""}
{"comment": {"body": "maybe should be `rt_count += 2` as that what the code did before", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/551/_/diff#comment-315926977"}}
{"comment": {"body": "local regression passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/551/_/diff#comment-316092303"}}
{"comment": {"body": "Where can I find the version where it\u2019s `+=2`?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/551/_/diff#comment-316749744"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} you can see here in the diff or:  \n[https://bitbucket.org/levl/eros-classifier/src/dev/packet_parser/tools/extract_session.py](https://bitbucket.org/levl/eros-classifier/src/dev/packet_parser/tools/extract_session.py){: data-inline-card='' } lines 29 and 46", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/551/_/diff#comment-316757926"}}
{"comment": {"body": "talked to @{5f82bf320756940075db755e} and he said the counter is just for debug and this is just a tool and not operational code.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/551/_/diff#comment-316954776"}}
{"title": "fixing git merge", "number": 552, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/552", "body": "roll back git merge that revived this bug:\n"}
{"comment": {"body": "local regression passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/552/_/diff#comment-316759915"}}
{"title": "separate ip resolution to a module and added more unit tests and flake noqa", "number": 553, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/553", "body": ""}
{"title": "bug fix", "number": 554, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/554", "body": ""}
{"title": "Update replace values : replace_values script - replace slash ( / )  with underscore ( _ )", "number": 555, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/555", "body": "Handle a slash ( / ) in the tag image, inside the replace_value script, by replacing it with underscore ( _ ) using the replace method\nNote : i used os.sep to account for multiple OS (not os.path.sep) since MAC uses colon ( : ) instead of slash in path)"}
{"comment": {"body": "@{62c1676dce5a604dbfb32cba} what is the default of sep? we shouldn\u2019t replace \u2018-', only '/\u2019", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/555/_/diff#comment-*********"}}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/**********-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/555/_/diff#comment-*********"}}
{"comment": {"body": "\\\\\\\\ on windows and / on linux / mac", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/555/_/diff#comment-*********"}}
{"title": "Fix MERSOP-1220 - wrong OS context in Macbook", "number": 556, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/556", "body": "The bug was that a WISPr agent string that exists in the db with iOS value (428.*******) and not in MacOS values got priority over the DHCP MacOS fingerprint.\nThe WISPr agent string should be taken in a confidence this lower than the DHCP fingerprint which returns the general os type."}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} if i recall we had this order previously. @{6085103f5797db006947d59a} am i missing something here?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/556/_/diff#comment-316788154"}}
{"comment": {"body": "not sure..  \nneed to check regression with this value", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/556/_/diff#comment-316838145"}}
{"comment": {"body": "@{6085103f5797db006947d59a} @{5f82bf320756940075db755e} \n\nI thought we already fixed that in the past \\(making the wispr lower confidence than dhcp\\), but it seems like we didn\u2019t.\n\nAll the regression tests are passing.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/556/_/diff#comment-316839030"}}
{"title": "new model", "number": 557, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/557", "body": "new model from "}
{"title": "upgrade to latest user agent parser package - ua-parser 0.15.0", "number": 558, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/558", "body": "The User Agent parser package we are using have released latest version after long time, lets give it a try. It should support latest user agent regex and types.\nDiff between last version we have used: "}
{"title": "remove auto deploy from dev and master to cujo ci", "number": 559, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/559", "body": "remove auto deploy from dev and master to cujo ci\nadd manual trigger to cujo deployments"}
{"title": "Dev master merges", "number": 56, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/56", "body": "Add more DB fields\nNew fields\nAdd more filters to bypass\nand now with dhcp_requested_ip\nadd bonjour and icmp6 ns features\nAdd matching and integration test with android device\nFix types\nflake8\nadapt parser to device session interface\nraise to 50msec\nadd bonjour and ICMP ns feature extraction unittests\nadded snapshot deployment\nfix typo\nMerge with new interface\nFix tests and formatting\nArchiver in fixture\nSSDP is Linux fix\nSSDP is Linux fix\nfixed topic\nerror handling + logs\nNow parsing DHCP ACK messages as well, mistakenly forgotten\nfixed logs\nadd logs and enable logging info\nadd logs and enable logging info\nImproved the choosing procedure of the parsing method\nForgot NB port\nlogging not used\nRemoved unnecessery lambda\nRemoved unnecessery print\nWrong key fix\nCreate db package\nKeep moving files around\nMove around\nMatching query will return the matching features\nAdd os filtering\nExtract mac address from session\nlocal changes\nFix test with DB cleanup setup Fix OS usage\nFix flake8 and icmpns\nAnother format\nStandartize Android OS Levl ID is 128-bit UUID\nDB: add last update DHCPv6: add windows flow\nAdd windows matching unittest\nFix tests\nWIP\nhistory devices\nLoad android device repository to DB and add query\nremove that file uploads until it's tested\nadd metrics decorator\nAdd non-random device name for windos and android with tests Store hostname in DB\nfix ci errors\nClear comments for flakey flake\nextract metrics to utils py\nmetrics decoration on all pipelines\nbug fix\nFix DHCPv4 IP usage Use DHCPv4 requested IP feature\nCleanup files\nCollect metrics in matching\nFix DHCPv4 requested IP usage\nadd history devices\nadd history devices - fix flake8\nadd history devices - fix cr\nFix test\nimpl\nFix DB create\ndon't use metrics\nRemove metrics\nimpl\nCreate DeviceHistory if not exist\nFormat file\nfix lint issues\nautomation ennvironment\nexecute pipeline as dask distributed\nadded deploy rollout\nnotifications\nMerged dev into dev_tools/distributed\nextract decision to json file\nrename notification\nremove bad tests\nrefator the serialization of device session as member function\nfix flake8\nfix tests with dask\nadding more functionality for pull and put data archiver by path &adding a test in test archaive using path\nsqs integration\nadd logging\nadd integration env\nyaml fix\nfix integration pipeline\nformat decisions list\nhack notification metrics\nmark out tests until local kafka dependency is fixed\n30 seconds timeout per integration test\nsolving some comments for the previous pull request\nRevert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\"\nskip integration test\nchange error logging to info\nadd kafka callback\nparse_session() does not longer belong to a class, it's stateless\nfix radio tap set() serialization\nfix radio tap set() serialization\n\nMerged in dev_tools/local_kafka (pull request #44)\nDev tools/local kafka\n\nanother layer fix\nnetwork fix\nfind network name\nanother cache try\nsetting network\nsetting network\nchanged to copy\nsupport local docker_test cmd\nlimit integration test with 30 seconds timeout\nMerged dev into dev_tools/local_kafka\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in feature/decision_log (pull request #46)\nadd decision log table\n\nadd decision log table\nadd naive snapshot for decision session\nFix UT for decision log\nfix code review for event type\n\nApproved-by: Gregory kovelman\n\n\nfix deployment decision snapshot\n\n\nMerged in amir_archiver (pull request #39)\nRetrying Amir' PR\n\nRevert \"Revert \"adding more functionality for pull and put data archiver by path &adding a test in test archaive using path (pull request #36)\" (pull request #38)\"\nadding file_filter to pull data base on specific file filter && test to test archiver to check file filter\nchange minio to default parameters\nadded helper function: _prep__path()  from_path, to_path and update test_archiver for more than one csv grep\nchange parmeter in test_archiver.py\nchange test_put_data_pull_data_by_filefilter_and_path to check the filter without hardcode path\nfix flake8 complain\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into amir_archiver\n\nApproved-by: Shimon Goulkarov Approved-by: Nadav Livni\n\n\nMerged in feature/gitignore_vscode (pull request #49)\nIgnore vscode helper file\n\nignore vscode helkper file\n\nApproved-by: Tamir Raz\n\n\nMerged in dev_tools/metrics (pull request #50)\nDev tools/metrics\n\nfix dask prom\nfix metrics instanciation\nfix flake8\ntimeout + input as byte like\nlist of sessions\nflake8\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev_tools/metrics\nrelocate pytest.ini\ndisable kafka integrated tests\nmetric fixes\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in dev_tools/per_developer_environment (pull request #51)\nDev tools/per_developer_environment\n\nMerge branch 'dev_tools/metrics' of bitbucket.org:levl/eros-classifier into dev_tools/per_developer_environment\ndeploy parallely\nuse summary instead of Histogram to measure duration\nflatten analytics\nuse histogram again\ntest\nrename\nrename2\nsummary histogram test\nrename\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in vault_id (pull request #48)\nupdate schemas to contain tenant and vault id\n\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into vault_id\nupdate schemas to contain tenant and vault id\nadded tenant and vault from session\nfix some tests\nchanges\nMerge branch 'dev' into vault_id\ntests fix\ntests fix\nadded test helper\nremoved insert to vaults\n\nApproved-by: Shimon Goulkarov\n\n\nhotfix db host\n\nchange DASK default value in code to false\nhot fix uuid to str\n\nMerged in hotfix_get_pipeline_configuration (pull request #53)\nchange pipeline name init\n\nchange pipeline name init\n\nApproved-by: Shimon Goulkarov\n\n\nMerged in bug/feature_none_exception (pull request #55)\nBug/feature none exception\n\nignore vscode helkper file\nMerge branch 'dev' of bitbucket.org:levl/eros-classifier into dev\nfix exception on features filter in case None\nfix code review comemnts\n\nApproved-by: Gregory kovelman\n\n\n"}
{"title": "bitbucket-pipelines.yml edited online with Bitbucket", "number": 560, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/560", "body": "bitbucket-pipelines.yml edited online with Bitbucket"}
{"title": "Change timestamp to float - fix de-serializing", "number": 561, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/561", "body": ""}
{"title": "Wispr 15 5", "number": 562, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/562", "body": "wispr model with iOS 15.5 agent\n\n"}
{"comment": {"body": "The entire file looks modified. Do you have some spacing issues or something like that?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/562/_/diff#comment-316886394"}}
{"title": "new wispr model with iOS 15.5 agent", "number": 563, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/563", "body": ""}
{"comment": {"body": "FYI, I run regression tests and all passed. You can run them also yourself by going in the pipeline for this PR.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/563/_/diff#comment-316920588"}}
{"title": "Merge release cujo to dev", "number": 564, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/564", "body": "Merged in lg6_typing (pull request #527)\nMEROSP-1029 Bugfix/LG6 Typing\n\nMEROSP-1029 Updated the model and added a test that reproduces that problem\n\nApproved-by: Tamir Raz Approved-by: Ophir Carmi\n\n\nMerged in bugfix/identification_levl_na (pull request #532)\nHotfix/Fixing enum values comparison\n\nFixing enum values comparison\n\nApproved-by: Tamir Raz Approved-by: Liat Ackerman\n\n\nMerged in feature/create-auto-deployment-MEROSP-1125 (pull request #523)\nFeature/create auto deployment MEROSP-1125\n\nfix kafka addr from prod kafka addr\nfix topic split for conenctors\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into feature/create-auto-deployment-MEROSP-1125\nparse_connection_event try/except block\nMerge branch 'feature/create-auto-deployment-MEROSP-1125' of bitbucket.org:levl/eros-classifier into feat ure/create-auto-deployment-MEROSP-1125\nset cluster name without suffix\n[skip ci] readme file\nparse result log\nMerge branch 'release/cujo_integration_ml1' of bitbucket.org:levl/eros-classifier into feature/create-auto-deployment-MEROSP-1125\nMerge branch 'feature/create-auto-deployment-MEROSP-1125' of bitbucket.org:levl/eros-classifier into feature/create-auto-deployment-MEROSP-1125\n\nApproved-by: Nadav Livni Approved-by: Itai Zolberg\n\n\n[skip ci] added kafka topics flag\n\n\nMerged in bugfix/agent_serial_export (pull request #544)\nbug fix\n\nbug fix\nadded agent serial and pid to tests\nfixed DeviceIntelligenceLog tenant id type to str\n\nApproved-by: Ariel Tohar Approved-by: Itai Zolberg\n\n\nMerged in MEROSP-1154 (pull request #539)\nMEROSP-1154\n\nmoving files\nmore\nadding radiotap unit test files\nfixing band selection in radiotap\nremoving lfs track from main file and moving to other file\nfixing models\nPR comment about logging format\nmore PR comments\nMerged release/cujo_integration_ml1 into MEROSP-1154\n\nApproved-by: Tamir Raz Approved-by: Dror Bendet\n\n\nMerged in bugfix/MEROSP-1174-fix-identification-level-cal (pull request #538)\nBugfix/MEROSP-1174 fix identification level calculation\n\nMEROSP-1174 Fixed the identification level calculation, and made sure it's calculated even if it's a new device (as it should)\nMEROSP-1174 Added some documentation\nMEROSP-1174 Documentation fix\nMEROSP-1174 Updated arp_ipv4 feature's statistical grade so the identification level calculation won't break\nMEROSP-1174 Updated Dhcpv6Ipv6 class as well with the statistical grade\nMerge branch 'release/cujo_integration_ml1' into bugfix/MEROSP-1174-fix-identification-level-cal\nMEROSP-1174 tests fix - logs_topics initialization\n\nApproved-by: Tamir Raz Approved-by: Ophir Carmi\n\n\n[skip ci] rod_in_the_barrel now works for all envs\n\n\nMerged in local-regression (pull request #548)\nadded make command for running regression locally\n\nadded make command for running regression locally\nMerged release/cujo_integration_ml1 into local-regression\nremove debuging lines from make command\nMerge branch 'local-regression' of bitbucket.org:levl/eros-classifier into local-regression\n\nApproved-by: Tamir Raz Approved-by: Artur Aharonovich Approved-by: Ophir Carmi\n\n\nMerged in MEROSP-840-radiotap-changes (pull request #439)\nMEROSP-840 radiotap changes\n\nremove too broad execption\nMerge branch 'MEROSP-840-radiotap-changes' of bitbucket.org:levl/eros-classifier into MEROSP-840-radiotap-changes\nMerge branch 'release/cujo_integration_ml1' into MEROSP-840-radiotap-changes\nMerged release/cujo_integration_ml1 into MEROSP-840-radiotap-changes\nMerge branch 'release/cujo_integration_ml1' into MEROSP-840-radiotap-changes\nMerged release/cujo_integration_ml1 into MEROSP-840-radiotap-changes\nupdating models\nMerged release/cujo_integration_ml1 into MEROSP-840-radiotap-changes\npackets to bin files\nfixing ut\n\nApproved-by: Tamir Raz Approved-by: Gregory kovelman\n\n\n[skip ci] fix default values and script\n\n\nMerged in bugfix/MEROSP-1206-model_loader---not-closing-o (pull request #554)\nbug fix\n\nbug fix\n\nApproved-by: Tamir Raz Approved-by: Ariel Tohar\n\n\nMerged in bugfix/MERSOP-wrong-contex-on-macbook-pro (pull request #556)\nFix MERSOP-1220 - wrong OS context in Macbook\n\nwispr should be in lesser confiedence than a dhcp os determination\n\nApproved-by: Shimon Goulkarov Approved-by: Dror Bendet\n\n\nMerged in fix-no-ip-indication (pull request #552)\nfixing git merge\n\ndon't error on ip indication when there is no DHCP ack\n\nApproved-by: Tamir Raz Approved-by: Shimon Goulkarov Approved-by: Liat Ackerman\n\n\n"}
{"title": "add environment image tag", "number": 565, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/565", "body": "Cujo docker tag added as repository deployment variable, this code will look for Cujo's docker tag before assigning branch name as docker tag. \n"}
{"title": "More extensive DHCPv4 transaction ID extraction", "number": 566, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/566", "body": "Extracting dhcp transaction id feature from req messages and not just ack.\n\nMEROSP-1208"}
{"comment": {"body": "@{5dbeb866c424110de52552cc} this will eliminate the dependency on DHCP ACK. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/566/_/diff#comment-317031851"}}
{"title": "Add ipv6 tests", "number": 567, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/567", "body": ""}
{"title": "move generate functions to utils", "number": 568, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/568", "body": ""}
{"title": "changed log level", "number": 569, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/569", "body": "change the log level to stop spam warnings"}
{"title": "Feature/metrics after rebase", "number": 57, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/57", "body": "Now calculating the decision time and the time the device spent in the pipeline\nRemoved unnecessery generate_latest()\ntemporary remove column\ntemp remove column\n\n"}
{"title": "throughput check", "number": 570, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/570", "body": ""}
{"comment": {"body": "regression passed:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17462](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17462){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/570/_/diff#comment-318378892"}}
{"title": "Temp test helm charts", "number": 571, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/571", "body": "Replace 'enabled' value by 1 - int over string\n[skip ci] bumped eros-chart new version 0.47.0\n\n"}
{"title": "Merge release cujo to dev", "number": 572, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/572", "body": "\n\ncompare os minor as well\n\nfix for regression test\nmerge from release cujo ml1 to dev"}
{"comment": {"body": "don\u2019t use f-string in logging", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/572/_/diff#comment-317247826"}}
{"comment": {"body": "@{6085103f5797db006947d59a} please redirect the PR to release/cujo\\_integration\\_ml1 branch and then to dev", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/572/_/diff#comment-317544245"}}
{"comment": {"body": "it should be merged to dev  \nand cherry pick the os fix to cujo", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/572/_/diff#comment-317582341"}}
{"title": "Ip resolution module 2", "number": 573, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/573", "body": "separate ip resolution from matchmaker.\nunit tests for ipv6 and ipv4"}
{"comment": {"body": "passed regression : [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16079](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/16079){: data-inline-card='' } \n\nexcept one known issue/test", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/573/_/diff#comment-317255787"}}
{"title": "Fix connector bug resolving sink type", "number": 574, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/574", "body": "We changed type to type_ due to flake, but missed one use of the variable."}
{"title": "File length validator", "number": 575, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/575", "body": "added file length validator\nchanged threshold of file length validator to 2000\n\n"}
{"title": "Bugfix/MEROSP-1244 Wrong data health", "number": 576, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/576", "body": "This PR fixes a bug in our Data Health calculation. \nWhen we tried to see if we got all the L2 data (or some of it), we looked at the feature_data part of the L2 extracted feature.\nThe problem is that in l2_device_typing we no longer had the relevant fields within the feature_data field and we kept only the final result. It somehow got lost along all the changes we had in our L2 typing procedure.\nBy returning them (the association_features and probe_features keys) we can now access them again and get the right indication and without re-iterating the L2 data.\nTo make sure it wont break again, a test was added."}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17489)\n\n:slight_smile:", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/576/_/diff#comment-318373686"}}
{"comment": {"body": "@{5ed4f1de9a64eb0c1e78f73b} do we have a more efficient way to mark existence of assoc, probe packets, without iterating them again?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/576/_/diff#comment-318408001"}}
{"comment": {"body": "We are not iterating them again :slight_smile: They were already calculated and stored. At this point, it\u2019s just going through a dictionary with one key and value, that looks like this:\n\n```\n{\u201c2.4G\u201d: {model_matches, association_vector, probe_vector}}\n```\n\nThe \u2018for\u2019 loop is for future implementation when we\u2019ll keep the information both for 2.4G and 5G, and even in that case that loop will run only **twice**.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/576/_/diff#comment-318497161"}}
{"title": "Devices db optimization", "number": 577, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/577", "body": "typing refactor\nmore refactor to type classifier, scores to json\n\n"}
{"title": "Schema registry on connectors helm chart", "number": 578, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/578", "body": "add sr configs to all connectors\nfix pre commit\nremoved lock\n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} we need only on results", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/578/_/diff#comment-317353162"}}
{"title": "Bugfix/desrialize kafka message Schema registry", "number": 579, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/579", "body": "add sr configs to all connectors\nfix pre commit\nremoved lock\nremove wrong placeholder\ndeserialize bytes insted of obj\n\n"}
{"title": "Match against tenant vault", "number": 58, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/58", "body": "added Device = get_device_model()\nget tenant_name from env\n\n"}
{"title": "fix file not existing", "number": 580, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/580", "body": ""}
{"title": "fixing bug of wrong resolution of release_date feature and the criteria", "number": 581, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/581", "body": ""}
{"comment": {"body": "regression passed except to 3 known tests that will be fixed in Nadal\u2019s PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/581/_/diff#comment-317542556"}}
{"title": "Partial merge from dev to cujo release", "number": 582, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/582", "body": "Partial merge from dev\nincluding the os merge fix"}
{"title": "Feature/remove tensorflow", "number": 583, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/583", "body": "remove tensorflow\nalign latest version for docker\n\n"}
{"comment": {"body": "why is this for release cujo?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/583/_/diff#comment-319210434"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} it\u2019s not, redirected to dev.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/583/_/diff#comment-320634567"}}
{"title": "bugfix 1227+1236 new l2 model with new lab devices", "number": 584, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/584", "body": "{: data-inline-card='' } \n{: data-inline-card='' }"}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} Can you look at the conflict?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/584/_/diff#comment-318374396"}}
{"comment": {"body": "there should be a unit test that checks the effect of these changes in models. we can\u2019t just change models without changing code or unit tests", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/584/_/diff#comment-318520835"}}
{"comment": {"body": "the test has been changed before approving this PR.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/584/_/diff#comment-318559158"}}
{"comment": {"body": "Declining the PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/584/_/diff#comment-318559201"}}
{"title": "Support de-facto CUJO data formatting", "number": 585, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/585", "body": "Strip null terminator from platform string.\nRead BSSID from raw bytes (not MAC-formatted)\n\n"}
{"title": "Add WS Discovery identification feature", "number": 586, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/586", "body": "The WS Discovery feature is based on a protocol that windows devices use in order to probe for and discover existing services on a giving network.\nThe protocol defines many behavioral requirements but the relevant property that will be used in order to identify a device is the 'Address', extracted from the following format:\na:EndpointReference\na:Addressxs:anyURI/a:Address\n[a:ReferenceProperties ... /a:ReferenceProperties]?\n...\n/a:EndpointReference\nAn example from the WS Discovery specification documentation \n(24) a:EndpointReference\n(25) a:Address\n(26) uuid:98190dc2-0890-4ef8-ac9a-5940995e6119\n(27) /a:Address\n(28) /a:EndpointReference\nLines (24-28) contain the stable, unique identifier for the Target Service that is\nconstant across network interfaces, transport addresses, and IPv4/v6.\nChanges:\n\nAdd a WS Discovery Feature class and a corresponding packet parser.\nAdd the feature name to the configurations modules and files.\nAdd unitests for parsing and matching.\nRefactor the Feature class property struct's name to avoid shadowing the python package with the same name.\nRemove unused Dhcpv6Ipv6 class which also shadowed the name of the Feature class with same name\nAdd the relevant fields to the device model and intelligence log\n\n"}
{"comment": {"body": "nice", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/586/_/diff#comment-317839690"}}
{"comment": {"body": "please wait until the following PR is merged:  \n[https://bitbucket.org/levl/eros-classifier/pull-requests/577](https://bitbucket.org/levl/eros-classifier/pull-requests/577){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/586/_/diff#comment-317842136"}}
{"comment": {"body": "You got it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/586/_/diff#comment-317843398"}}
{"comment": {"body": "Great description, I suggest to use it as the basis for the feature page on Confluence.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/586/_/diff#comment-318368385"}}
{"comment": {"body": "Thank you, it was my intention when I wrote it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/586/_/diff#comment-318376925"}}
{"comment": {"body": "merged", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/586/_/diff#comment-318377553"}}
{"comment": {"body": "Passed regression  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/1761](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17615)\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/586/_/diff#comment-318378862"}}
{"title": "Runtime profiler reportportal attachment", "number": 587, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/587", "body": "runtime profiler: added attachment reportportal\nsmall fixes like narrower exception for dns unapck error."}
{"comment": {"body": "How did this come about? Is there a test that results in this?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/587/_/diff#comment-317842181"}}
{"comment": {"body": "trying to reduce logs", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/587/_/diff#comment-317848608"}}
{"comment": {"body": "Cool", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/587/_/diff#comment-317849595"}}
{"comment": {"body": "known issue: only one test out of three produce attachment to report portal", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/587/_/diff#comment-317884695"}}
{"title": "added flake8-bugbear", "number": 588, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/588", "body": ""}
{"comment": {"body": "regression passed:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17393](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17393)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/588/_/diff#comment-318373778"}}
{"title": "Fix mislabeled devices", "number": 589, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/589", "body": ""}
{"title": "Migration aws", "number": 59, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/59", "body": "change to updated db hostname default\n"}
{"title": "Better names returned in ui_description for device that are unknown", "number": 590, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/590", "body": ""}
{"comment": {"body": "Regression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17310) :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/590/_/diff#comment-318371625"}}
{"title": "MEROSP-1271 Now deserializing the kafka msgs only if they are not None", "number": 591, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/591", "body": "Another small issue, happened after the transition to saving KafkaMsg objects instead of raw data"}
{"comment": {"body": "Pipeline passed,\n\nRegression [passed](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17790)\n\n:slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/591/_/diff#comment-318383901"}}
{"title": "Client api deployment", "number": 592, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/592", "body": "added client api helm chart to eros-chart stack."}
{"comment": {"body": "why isn\u2019t there a placeholder here?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/592/_/diff#comment-318388028"}}
{"comment": {"body": "thats the helm chart values not helm deployment values", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/592/_/diff#comment-318388802"}}
{"title": "Update typing with relation to usage prior", "number": 593, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/593", "body": "In some cases, we might get a better typing result (in typing_merge) without increasing the resolution such that a prior is not needed anymore but in the current implementation those results are not updated as the result returned to the user (as they dont increase the typing resolution).\nThe change is making sure we prioritize result that were derived without the need to use a prior when updating the typing result."}
{"comment": {"body": "Please let us know with a comment here if regression has passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/593/_/diff#comment-318383688"}}
{"comment": {"body": "Regression has passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17624](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17624)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/593/_/diff#comment-318383976"}}
{"title": "Use canonical names families", "number": 594, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/594", "body": "replace iPhone 11, iPhone 12 and iPhone 13 with iPhone 11 family, iPhone 12 family and iPhone 13 family"}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} can you share the automation PR for fixing the expected results?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/594/_/diff#comment-318389767"}}
{"comment": {"body": "Here it is:\n\n[https://bitbucket.org/levl/eros-automation/pull-requests/214/fixed-expected-results-according-to-family](https://bitbucket.org/levl/eros-automation/pull-requests/214/fixed-expected-results-according-to-family){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/594/_/diff#comment-318389947"}}
{"comment": {"body": "Thanks", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/594/_/diff#comment-318394120"}}
{"comment": {"body": "Regression tests passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18152](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18152){: data-inline-card='' } \n\nAutomation PR: [https://bitbucket.org/levl/eros-automation/pull-requests/215](https://bitbucket.org/levl/eros-automation/pull-requests/215){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/594/_/diff#comment-318398558"}}
{"title": "splitting devices db to low res and high res", "number": 595, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/595", "body": "currently the split occurs in every query, which takes maybe a lot of time. we will still have only one csv file but at the initialization we split them to two data frames."}
{"comment": {"body": "passed regression\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18142](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18142){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/595/_/diff#comment-318397988"}}
{"title": "MEROSP-1242 user agent parser wrong parsing", "number": 596, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/596", "body": "We have a problem with some UA regex.\nthis fix removes some regex that returnes Generic or Generic Smartphone which we have nothing to do with.\nand adds one the solves 'Chrome/96.0.4664.104 Android 11; SM-A217F' and we can add as many as we want.\n@{5d74d49897d8980d8eacd7f8} plz let me know if you have some more strings that we should add."}
{"title": "add schema registry values to connectors", "number": 597, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/597", "body": "Add Schema Registry values to connectors values (Helm deployment tool values file)"}
