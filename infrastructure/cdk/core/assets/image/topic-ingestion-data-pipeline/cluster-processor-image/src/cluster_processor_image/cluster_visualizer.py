import time

from pandas import DataFrame
from wordcloud import WordCloud
import matplotlib.pyplot as plt
import altair as alt
import seaborn as sns

from dataframe_constants import DOC_COLUMN, CLUSTER_COLUMN, CLUSTER_TOPICS_COLUMN, X_COLUMN, Y_COLUMN


class ClusterVisualizer:
    def visualize_clusters(self, df: DataFrame):
        pass


class WordCloudClusterVisualizer(ClusterVisualizer):
    def visualize_clusters(self, df: DataFrame):
        for c in df[CLUSTER_COLUMN].unique():
            dfc = df.query(f"{CLUSTER_COLUMN} == {c}")
            # concatenates all texts from all documents in that cluster
            docs = dfc[DOC_COLUMN].str.cat(sep=" ")
            docs = docs.lower()
            docs = " ".join([word for word in docs.split()])
            wordcloud = WordCloud(max_font_size=50, max_words=100, background_color="white").generate(docs)
            plt.figure()
            plt.imshow(wordcloud, interpolation="bilinear")
            plt.axis("off")
            plt.show()
            time.sleep(0.1)


class ScatterPlotVisualizer(ClusterVisualizer):
    def visualize_clusters(self, df: DataFrame):
        color_palette = sns.color_palette("Paired", 1000)
        cluster_colors = [color_palette[x] for x in df[CLUSTER_COLUMN]]
        plt.figure()
        plt.scatter(df[X_COLUMN], df[Y_COLUMN], c=cluster_colors, alpha=0.25)
        plt.show()
        time.sleep(0.1)


class ChartVisualizer(ClusterVisualizer):
    def visualize_clusters(self, df: DataFrame):
        selection = alt.selection_multi(fields=[CLUSTER_TOPICS_COLUMN], bind="legend")

        brush = alt.selection_interval()
        chart = (
            alt.Chart(df)
            .mark_circle(size=60, stroke="#666", strokeWidth=1, opacity=0.3)
            .encode(
                x=alt.X(
                    f"{X_COLUMN}", scale=alt.Scale(zero=False), axis=alt.Axis(labels=False, ticks=False, domain=False)
                ),
                y=alt.Y(
                    f"{Y_COLUMN}", scale=alt.Scale(zero=False), axis=alt.Axis(labels=False, ticks=False, domain=False)
                ),
                color=alt.Color(
                    f"{CLUSTER_TOPICS_COLUMN}:N",
                    legend=alt.Legend(columns=1, labelLimit=100, symbolLimit=0, labelFontSize=14),
                ),
                opacity=alt.condition(selection, alt.value(1), alt.value(0.2)),
                tooltip=[DOC_COLUMN, CLUSTER_TOPICS_COLUMN, CLUSTER_COLUMN],
            )
            .properties(
                width=1200,
                height=500,
            )
            .add_selection(selection)
            .add_params(brush)
        )

        bars = (
            alt.Chart(df)
            .mark_bar()
            .encode(
                x="count()",
                y=alt.Y(f"{CLUSTER_COLUMN}:N", axis=alt.Axis(domain=False, ticks=False, title=None, labelLimit=200)),
                color=f"{CLUSTER_TOPICS_COLUMN}:N",
                tooltip=[CLUSTER_TOPICS_COLUMN, CLUSTER_COLUMN],
            )
            .transform_filter(brush)
            .add_selection(selection)
        )

        concatenated_chart = chart & bars

        concatenated_chart.configure(background="#FDF7F0").properties(title="Topics Clustering").save("filename.html")
