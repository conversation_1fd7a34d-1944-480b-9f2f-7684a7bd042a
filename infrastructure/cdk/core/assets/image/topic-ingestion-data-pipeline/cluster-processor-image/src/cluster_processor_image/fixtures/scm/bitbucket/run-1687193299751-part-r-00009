{"comment": {"body": "<PERSON><PERSON><PERSON> can you explain a bit why this struct is needed or its name.. all cfo model struct\u2019s fields are \\(or should be\\) variables. IMO this struct\u2019s variables should be integrated in cfo\\_model\\_st, or at least its name should be modified to cfo\\_model\\_slope", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108535210"}}
{"comment": {"body": "There\u2019s no failure here - we continue classify until we don\u2019t get MORE\\_DATA\\_NEEDED anymore \\(hence, break\\). Then we check what we got, in this case we check for match.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108537040"}}
{"comment": {"body": "Deserves a proper Goodbyyyyyyyyyye", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108537717"}}
{"comment": {"body": "What kind of ceremony?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108548740"}}
{"comment": {"body": "Well.. that was it.. my farewell cry :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108552471"}}
{"comment": {"body": "Because MISRA is out bestest friend and likes us to keep functions with 7 parameters at most", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108559147"}}
{"comment": {"body": "I\u2019ll think about it. I didn\u2019t want to refactor too much, but it\u2019s true that currently they have the same meaning as library configuration because they\u2019re set only once during TrainReset.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108559430"}}
{"comment": {"body": "Default is a much better name", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108559507"}}
{"comment": {"body": "It\u2019s a variable in the statistical meaning; more like a random variable. Our linear regression is trying to solve the relationship between CFO and the triplet channel, board temperature and device temperature.  \nMaybe renaming it to `cfo_model_random_variables_t` would be better? Maybe the context should come from the name of the struct instance itself. Then if I clear the \\_slope suffix, it would be clearer.\n\nIt\u2019s already part of the `cfo_model_t` under the `slopes` variable. It\u2019s just distinct since this triplet appears everywhere.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108561386"}}
{"comment": {"body": "Right. I\u2019ll remove it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108561438"}}
{"comment": {"body": "Fixed in acb0b594d2bc978602693cde9582906ee1e398d9", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108574398"}}
{"comment": {"body": "Fixed in acb0b594d2bc978602693cde9582906ee1e398d9", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108574424"}}
{"comment": {"body": "Removed in a887cd197f48def8cc0d474e287ee57f413b8470", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108574594"}}
{"title": "Feature/BIS-4516 CFO code cleanup validate structs", "number": 503, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/503", "body": "Refactor data_structure_descriptor\nAdd structures comparison between Hydra and C\nNew step for struct integrity test\nFix padding in hydra events\nFixed regression in monitor_v3"}
{"comment": {"body": "Why shorten the word \u201creal\u201d? rel can mean both real or relative :worried: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/503/_/diff#comment-108542114"}}
{"comment": {"body": "it\u2019s short for relative", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/503/_/diff#comment-108558676"}}
{"comment": {"body": ":thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/503/_/diff#comment-108610053"}}
{"comment": {"body": "Much clearer", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/503/_/diff#comment-108610323"}}
{"title": "Will now archive the android app", "number": 504, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/504", "body": ""}
{"title": "Patch UART", "number": 505, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/505", "body": "This patch allows anchors be recordable in high temperatures. Tested up to 125C degrees."}
{"title": "WIFI will now be turned back on when the phone is not advertising to enable the phone to receive updates and etc.", "number": 506, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/506", "body": ""}
{"title": "Feature/wifi stress", "number": 507, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/507", "body": "Wi-Fi stress information and GUI added to application / advertisements\nSee Version 7 at: \nAnd also:\n"}
{"comment": {"body": "Nice refactoring work!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/507/_/diff#comment-109257359"}}
{"comment": {"body": "I accidentally told Android Studio to reformat the code without noticing\u2026 Didn\u2019t do actual reformatting myself lol", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/507/_/diff#comment-109257556"}}
{"comment": {"body": "You still deserve the credits", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/507/_/diff#comment-109257827"}}
{"comment": {"body": "About the stress directory, I think that it would lose its meaning quickly. Would be better to bury it under some `wifi-stress-server`. Even `levl` repo would be appropriate.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/507/_/diff#comment-109258199"}}
{"comment": {"body": "Renamed to stress-server in a3c2b1d7183c22418ce88fde50e9677532b71bc2", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/507/_/diff#comment-109258578"}}
{"comment": {"body": "Technically it has nothing to do with Wi-Fi so I\u2019ll keep that word out", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/507/_/diff#comment-109258984"}}
{"title": "Refactor script", "number": 508, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/508", "body": ""}
{"title": "Added numerical Wi-Fi usage to adv (log of bps), GUI for accelerating download (raise=...)", "number": 509, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/509", "body": ""}
{"comment": {"body": "Please note I changed the beacon restart period to be shorter so the WiFi usage is reflected more precisely", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/509/_/diff#comment-109456588"}}
{"comment": {"body": "This is the download speed that matches each value of exp:\n\n```\n>>> print(os.linesep.join(f'{str(i).zfill(3)}: {1.1**i / 1024:.5f} kB/s' for i in range(255)))\r\n000: 0.00098 kB/s\r\n001: 0.00107 kB/s\r\n002: 0.00118 kB/s\r\n003: 0.00130 kB/s\r\n004: 0.00143 kB/s\r\n005: 0.00157 kB/s\r\n006: 0.00173 kB/s\r\n007: 0.00190 kB/s\r\n008: 0.00209 kB/s\r\n009: 0.00230 kB/s\r\n010: 0.00253 kB/s\r\n011: 0.00279 kB/s\r\n012: 0.00306 kB/s\r\n013: 0.00337 kB/s\r\n014: 0.00371 kB/s\r\n015: 0.00408 kB/s\r\n016: 0.00449 kB/s\r\n017: 0.00494 kB/s\r\n018: 0.00543 kB/s\r\n019: 0.00597 kB/s\r\n020: 0.00657 kB/s\r\n021: 0.00723 kB/s\r\n022: 0.00795 kB/s\r\n023: 0.00874 kB/s\r\n024: 0.00962 kB/s\r\n025: 0.01058 kB/s\r\n026: 0.01164 kB/s\r\n027: 0.01280 kB/s\r\n028: 0.01408 kB/s\r\n029: 0.01549 kB/s\r\n030: 0.01704 kB/s\r\n031: 0.01874 kB/s\r\n032: 0.02062 kB/s\r\n033: 0.02268 kB/s\r\n034: 0.02495 kB/s\r\n035: 0.02744 kB/s\r\n036: 0.03019 kB/s\r\n037: 0.03321 kB/s\r\n038: 0.03653 kB/s\r\n039: 0.04018 kB/s\r\n040: 0.04420 kB/s\r\n041: 0.04862 kB/s\r\n042: 0.05348 kB/s\r\n043: 0.05883 kB/s\r\n044: 0.06471 kB/s\r\n045: 0.07118 kB/s\r\n046: 0.07830 kB/s\r\n047: 0.08613 kB/s\r\n048: 0.09474 kB/s\r\n049: 0.10422 kB/s\r\n050: 0.11464 kB/s\r\n051: 0.12610 kB/s\r\n052: 0.13871 kB/s\r\n053: 0.15259 kB/s\r\n054: 0.16784 kB/s\r\n055: 0.18463 kB/s\r\n056: 0.20309 kB/s\r\n057: 0.22340 kB/s\r\n058: 0.24574 kB/s\r\n059: 0.27031 kB/s\r\n060: 0.29735 kB/s\r\n061: 0.32708 kB/s\r\n062: 0.35979 kB/s\r\n063: 0.39577 kB/s\r\n064: 0.43534 kB/s\r\n065: 0.47888 kB/s\r\n066: 0.52677 kB/s\r\n067: 0.57944 kB/s\r\n068: 0.63739 kB/s\r\n069: 0.70112 kB/s\r\n070: 0.77124 kB/s\r\n071: 0.84836 kB/s\r\n072: 0.93320 kB/s\r\n073: 1.02652 kB/s\r\n074: 1.12917 kB/s\r\n075: 1.24209 kB/s\r\n076: 1.36629 kB/s\r\n077: 1.50292 kB/s\r\n078: 1.65322 kB/s\r\n079: 1.81854 kB/s\r\n080: 2.00039 kB/s\r\n081: 2.20043 kB/s\r\n082: 2.42047 kB/s\r\n083: 2.66252 kB/s\r\n084: 2.92877 kB/s\r\n085: 3.22165 kB/s\r\n086: 3.54381 kB/s\r\n087: 3.89820 kB/s\r\n088: 4.28802 kB/s\r\n089: 4.71682 kB/s\r\n090: 5.18850 kB/s\r\n091: 5.70735 kB/s\r\n092: 6.27808 kB/s\r\n093: 6.90589 kB/s\r\n094: 7.59648 kB/s\r\n095: 8.35613 kB/s\r\n096: 9.19174 kB/s\r\n097: 10.11092 kB/s\r\n098: 11.12201 kB/s\r\n099: 12.23421 kB/s\r\n100: 13.45763 kB/s\r\n101: 14.80339 kB/s\r\n102: 16.28373 kB/s\r\n103: 17.91210 kB/s\r\n104: 19.70331 kB/s\r\n105: 21.67365 kB/s\r\n106: 23.84101 kB/s\r\n107: 26.22511 kB/s\r\n108: 28.84762 kB/s\r\n109: 31.73239 kB/s\r\n110: 34.90562 kB/s\r\n111: 38.39619 kB/s\r\n112: 42.23581 kB/s\r\n113: 46.45939 kB/s\r\n114: 51.10532 kB/s\r\n115: 56.21586 kB/s\r\n116: 61.83744 kB/s\r\n117: 68.02119 kB/s\r\n118: 74.82331 kB/s\r\n119: 82.30564 kB/s\r\n120: 90.53620 kB/s\r\n121: 99.58982 kB/s\r\n122: 109.54880 kB/s\r\n123: 120.50368 kB/s\r\n124: 132.55405 kB/s\r\n125: 145.80946 kB/s\r\n126: 160.39040 kB/s\r\n127: 176.42944 kB/s\r\n128: 194.07239 kB/s\r\n129: 213.47962 kB/s\r\n130: 234.82759 kB/s\r\n131: 258.31034 kB/s\r\n132: 284.14138 kB/s\r\n133: 312.55552 kB/s\r\n134: 343.81107 kB/s\r\n135: 378.19218 kB/s\r\n136: 416.01139 kB/s\r\n137: 457.61253 kB/s\r\n138: 503.37379 kB/s\r\n139: 553.71116 kB/s\r\n140: 609.08228 kB/s\r\n141: 669.99051 kB/s\r\n142: 736.98956 kB/s\r\n143: 810.68852 kB/s\r\n144: 891.75737 kB/s\r\n145: 980.93310 kB/s\r\n146: 1079.02641 kB/s\r\n147: 1186.92906 kB/s\r\n148: 1305.62196 kB/s\r\n149: 1436.18416 kB/s\r\n150: 1579.80257 kB/s\r\n151: 1737.78283 kB/s\r\n152: 1911.56111 kB/s\r\n153: 2102.71723 kB/s\r\n154: 2312.98895 kB/s\r\n155: 2544.28784 kB/s\r\n156: 2798.71663 kB/s\r\n157: 3078.58829 kB/s\r\n158: 3386.44712 kB/s\r\n159: 3725.09183 kB/s\r\n160: 4097.60101 kB/s\r\n161: 4507.36112 kB/s\r\n162: 4958.09723 kB/s\r\n163: 5453.90695 kB/s\r\n164: 5999.29765 kB/s\r\n165: 6599.22741 kB/s\r\n166: 7259.15015 kB/s\r\n167: 7985.06517 kB/s\r\n168: 8783.57168 kB/s\r\n169: 9661.92885 kB/s\r\n170: 10628.12174 kB/s\r\n171: 11690.93391 kB/s\r\n172: 12860.02730 kB/s\r\n173: 14146.03003 kB/s\r\n174: 15560.63303 kB/s\r\n175: 17116.69634 kB/s\r\n176: 18828.36597 kB/s\r\n177: 20711.20257 kB/s\r\n178: 22782.32283 kB/s\r\n179: 25060.55511 kB/s\r\n180: 27566.61062 kB/s\r\n181: 30323.27168 kB/s\r\n182: 33355.59885 kB/s\r\n183: 36691.15873 kB/s\r\n184: 40360.27461 kB/s\r\n185: 44396.30207 kB/s\r\n186: 48835.93228 kB/s\r\n187: 53719.52550 kB/s\r\n188: 59091.47805 kB/s\r\n189: 65000.62586 kB/s\r\n190: 71500.68844 kB/s\r\n191: 78650.75729 kB/s\r\n192: 86515.83302 kB/s\r\n193: 95167.41632 kB/s\r\n194: 104684.15795 kB/s\r\n195: 115152.57375 kB/s\r\n196: 126667.83112 kB/s\r\n197: 139334.61423 kB/s\r\n198: 153268.07566 kB/s\r\n199: 168594.88322 kB/s\r\n200: 185454.37154 kB/s\r\n201: 203999.80870 kB/s\r\n202: 224399.78957 kB/s\r\n203: 246839.76852 kB/s\r\n204: 271523.74538 kB/s\r\n205: 298676.11991 kB/s\r\n206: 328543.73191 kB/s\r\n207: 361398.10510 kB/s\r\n208: 397537.91561 kB/s\r\n209: 437291.70717 kB/s\r\n210: 481020.87788 kB/s\r\n211: 529122.96567 kB/s\r\n212: 582035.26224 kB/s\r\n213: 640238.78846 kB/s\r\n214: 704262.66731 kB/s\r\n215: 774688.93404 kB/s\r\n216: 852157.82744 kB/s\r\n217: 937373.61019 kB/s\r\n218: 1031110.97121 kB/s\r\n219: 1134222.06833 kB/s\r\n220: 1247644.27516 kB/s\r\n221: 1372408.70268 kB/s\r\n222: 1509649.57294 kB/s\r\n223: 1660614.53024 kB/s\r\n224: 1826675.98326 kB/s\r\n225: 2009343.58159 kB/s\r\n226: 2210277.93975 kB/s\r\n227: 2431305.73372 kB/s\r\n228: 2674436.30709 kB/s\r\n229: 2941879.93780 kB/s\r\n230: 3236067.93158 kB/s\r\n231: 3559674.72474 kB/s\r\n232: 3915642.19722 kB/s\r\n233: 4307206.41694 kB/s\r\n234: 4737927.05863 kB/s\r\n235: 5211719.76450 kB/s\r\n236: 5732891.74095 kB/s\r\n237: 6306180.91504 kB/s\r\n238: 6936799.00654 kB/s\r\n239: 7630478.90720 kB/s\r\n240: 8393526.79792 kB/s\r\n241: 9232879.47771 kB/s\r\n242: 10156167.42548 kB/s\r\n243: 11171784.16803 kB/s\r\n244: 12288962.58483 kB/s\r\n245: 13517858.84332 kB/s\r\n246: 14869644.72765 kB/s\r\n247: 16356609.20041 kB/s\r\n248: 17992270.12045 kB/s\r\n249: 19791497.13250 kB/s\r\n250: 21770646.84575 kB/s\r\n251: 23947711.53033 kB/s\r\n252: 26342482.68336 kB/s\r\n253: 28976730.95169 kB/s\r\n254: 31874404.04686 kB/s\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/509/_/diff#comment-109457245"}}
{"comment": {"body": "no salary raise for you :stuck_out_tongue: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/509/_/diff#comment-109457934"}}
{"comment": {"body": "Approved?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/509/_/diff#comment-109591521"}}
{"title": "Feature/slack integration", "number": 51, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/51", "body": "adding slack\ncommenting out slack\nreverting\nslack details in jenkinsfile\njenkinsfile\njenkinsfile"}
{"title": "add GazillionDatasets test with 100 datasets", "number": 510, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/510", "body": "Some refactor to online_training regression tests\nFurther work is needed since classification starts to fail when using more than 100 datasets."}
{"comment": {"body": "Please review", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/510/_/diff#comment-110048886"}}
{"title": "Fixed shelter filtering", "number": 511, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/511", "body": ""}
{"comment": {"body": "Is this fix related to the open filter bug \\(that sheltering currently still gets other phones' data\\), or its just a simple correction that you spotted?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/511/_/diff#comment-109687538"}}
{"comment": {"body": "This diff changes nothing it just makes more sense  \n  \nThe diff below it makes shelter leakage 255 times less likely", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/511/_/diff#comment-109752194"}}
{"comment": {"body": "Merge this", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/511/_/diff#comment-109755699"}}
{"comment": {"body": "Will merge after build passes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/511/_/diff#comment-109761072"}}
{"title": "Fixed WiFi turns on bug", "number": 512, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/512", "body": ""}
{"title": "Removed pinger service because it is no longer needed with WiFi stress testing", "number": 513, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/513", "body": ""}
{"title": "Feature/BIS-4517 more cfo tests", "number": 514, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/514", "body": "Refactor online training regression tests to reuse allocated memory (shared pointers, references)\nAdd variable scaling API for CFO model. Currently only update the channel variable, but this requires more work. This increases the number of succeeding online training iterations to 350 from 100. \nRefactor logging for online training\nGazillion datasets only runs on 64-bit env"}
{"comment": {"body": "Just for my understanding is this really a shared\\_ptr or it can be a unique\\_ptr?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/514/_/diff#comment-110976974"}}
{"comment": {"body": "it can\u2019t be a unique\\_ptr since the packets are reused and ownership transfer \\(in the case of unique\\_ptr\\) would be very difficult", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/514/_/diff#comment-110982416"}}
{"comment": {"body": "OK", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/514/_/diff#comment-110987297"}}
{"title": "Feature/BIS-4552 extented regression testing mec", "number": 515, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515", "body": "Add mechanism for large scale testing:\n  Create new pickles is required (Google now supported in docker);\n  Upload them to storage;\n  Run large scale tests\nRun large scale tests in Jenkins every night at 1:00\nAdd smaller bigquery utils to bosch_integration\nUse highest record id to decide if to add record (so records with very few pickles won't be tested again and again)\nSet env variables outside python script to avoid order issues;\n  Also add these env vars in python to make life easier for local users\nOptions to run the tool: c only, py only, print all scenarios, run specific scenario, run on specific imei combination only\n\nNote the Jenkins_large_scale currently uses specific branches, until I commit and change the branches to develop and master (for levl)."}
{"comment": {"body": "Looking great.\n\nSince it would take time to follow the logic in this PR, I suggest to do a frontal explanation and review. WDYT?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-110932108"}}
{"comment": {"body": "You\u2019re cloning levl anyway, is there still need for this file?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111135512"}}
{"comment": {"body": "Pandas methods can make this simpler:\n\n```python\nfirst_packet_time, last_packet_time = df.packet_time.iloc[0], df.packet_time.iloc[-1]\nmin_agc, max_agc = df.agc_locked.min(), df.agc_locked.max()\n...\n```\n\nNo need to loop / create auxiliary functions like `get_min_max_agc`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111136949"}}
{"comment": {"body": "Maybe pickle this triplet: `(valid_scenarios, suspicious_scenarios, print_all_scenarios)` and artifact it so it can be analyzed, processed and worked on later with `post_process_scenarios_results` or other code? \n\nThis is because all the code before takes a long time to run, so it\u2019s nice to have it\u2019s output saved persistently", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111137689"}}
{"comment": {"body": "An alternative to the `debug` flag is replacing the print call with a `logging.info(...)` call.\n\nThen all `info` logging coming from a specific module \\(such as `pyfingerprinting.py`\\) can be disabled in a manner similar to this:\n\n```python\nimport logging\r\n\r\nlogging.getLogger(\"pyfingerprinting\").setLevel(logging.ERROR)\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111140571"}}
{"comment": {"body": "Can be simplified:\n\n```python\nfeatures = (feature for success, feature, err_code in (feature_extract_c(lib, packet) for packet in packets) if success)\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111141526"}}
{"comment": {"body": "Py3 trick: `packets, *_ = load_all_packets(a_pickle)`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111141803"}}
{"comment": {"body": "Great work", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111142284"}}
{"comment": {"body": "Python3 already has a lib function for that:\n\n```python\nfrom pathlib import Path\nfor file in Path(directory).glob('**/*.pickle'):\n  ...\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111144305"}}
{"comment": {"body": "The debug flag is actually not used for printing, it\u2019s used to tell the script to run in the same process, so we can debug the scenarios \\(when running new processes sometimes the breakpoints don\u2019t work\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111179137"}}
{"comment": {"body": "The large\\_scale\\_tests\\_runner can optionally run for c only \\(if you use -run c\\) - in such a case levl repo is not required. It\u2019s a small util and we may need to use it for other stuff in the future.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111334227"}}
{"comment": {"body": "Good to know. For now leaving as is since code is short and IMO a bit more readable as is", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111334801"}}
{"comment": {"body": "All the relevant data in the scenario object is printed out. If further processing is required, it should be added to the script so it would always be done.  \nIf further investigation is required for specific scenarios, this data won\u2019t be useful.\n\nI think this tool provides A LOT of required info that we should start working on - and we should concentrate on the meaning of this report.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111335749"}}
{"comment": {"body": "Nice, less clear", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111335838"}}
{"comment": {"body": "Ahhh who would understand such a code?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111335976"}}
{"comment": {"body": "Yes but even when working on this code, when I want to change the format of printing or modify anything in the report - I have to wait for it to run again. If there was a short call to .pickle I could simple debug it by always reading from the pickles instead of re-generating everything.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111336108"}}
{"comment": {"body": "Right - will change", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111336651"}}
{"comment": {"body": "I personally think it\u2019s more readable than 7 lines of for loops, if\u2019s and else\u2019s. \n\nIt can be broken up further to be a bit more readable:  \n\n```python\nextractions = (feature_extract_c(lib, packet) for packet in packets)\nfeatures = (feature for success, feature, err_code in extractions if success)\n```\n\nIt also creates a lazy generator instead of an intermediate list so it\u2019s more efficient \\(I know that efficiency is not critical in this context but still\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111336893"}}
{"comment": {"body": "Strongly disagree.. I think these 2 lines require much more effort to read.  \nAnyway I\u2019ll remove the pass, no idea why it\u2019s still there :\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111339547"}}
{"comment": {"body": "IMO this is not significant and not required", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111351782"}}
{"comment": {"body": "Ok, I think you\u2019re suggestion can be useful for the following:\n\n1. If I can access previous results from current run in Jenkins, I can more easily find and present differences between results \\(new scenarios that are failing for example\\)\n2. When we want to investigate the results offline \\(I mean automate the investigation\\), I can use such a pickle.\n\nThis introduces a problem of backwards compatibility though, since the scenario struct changes as I add more fields to it. But, we can ignore it and just use it when possible. Anyway this may prove useful, I\u2019ll consider it - thanks Omer.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/515/_/diff#comment-111663324"}}
{"title": "Add CFO diagrams upon request", "number": 516, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/516", "body": "When running the large_scale_tests with -cfo-diagram, two cfo plots are popped for each scenario: \n\nCFO by phone and board temp (3D diagram)\nA plot with 4 diagrams: \n  Classification CFO predicted vs. actual (+training CFO), by order\n  CFO by phone temperature (train and classify)\n  CFO by board temperature (train and classify)\n  CFO by channel (train and classify)"}
{"comment": {"body": "The two diagrams appear as follows:\n\n![](https://bitbucket.org/repo/x8eLRbo/images/1816623951-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/516/_/diff#comment-111906998"}}
{"comment": {"body": "Does the 3d plot makes sense according to the scenario? It shows that there\u2019s a lot of phone temperature data when the board temperature is 16c and then the phone temperature becomes stationary at ~26 degrees with board temperature varying between 16c and 21c.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/516/_/diff#comment-112009262"}}
{"comment": {"body": "We could benefit from finding out the correlation between the phone temperature and the board temperature. \\(This is not a graph\\)\n\nThe calculation is with `np.corrcoef` on the phone/board temperatures **ordered by time.** This returns a 2x2 symmetric matrix and we should look at the \\[0, 1\\] or \\[1, 0\\] cells \\(are the same\\). Values above ~0.9 should show a warning", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/516/_/diff#comment-112017105"}}
{"comment": {"body": "Will do", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/516/_/diff#comment-112103708"}}
{"comment": {"body": "Yes it makes sense. For this specific pickle \\(train and classify pickles here are the same I think\\), phone temp range is \\(19.7, 28.4\\) and board temp range is \\(1434.0, 2090\\).  \nIf you wish to continue with this detective work, record id is 000390 and it\u2019s name is: `temp_matrix_att10db_phones_9_10_22_23_30_33_37_9999`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/516/_/diff#comment-112180252"}}
{"title": "Set branch name to develop; run scale tests using py as well", "number": 517, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/517", "body": ""}
{"title": "Feature/sine recording AB", "number": 518, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/518", "body": "FW for recording sine waves from signal generator\n\nAdd utility to record IQ without actual packets\nAdded sine_recording_agent to build scripts"}
{"title": "Treat packet capture as a co-routine which doesn't block", "number": 519, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/519", "body": ""}
{"comment": {"body": "Might as-well make this change in all agents", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/519/_/diff#comment-113693023"}}
{"comment": {"body": "You\u2019re right. I missed that", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/519/_/diff#comment-113764259"}}
{"comment": {"body": "HELLO", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/519/_/diff#comment-113914378"}}
{"title": "BIS-313 pyfingerprinting example into api", "number": 52, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/52", "body": "Renamed pyfingerprinting cmake SWIG to swigpyf, pyfingerprinting will be the name reserved for the final wrapper\nComplete re-organization of Pyfingerprinting with new folder structure, new API and documentation"}
{"comment": {"body": "If there isn\u2019t a matching number of bytes in the data \\(not a multiple of uint32\\_t\\) we probably want to throw this packet away", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/52/_/diff#comment-75083837"}}
{"comment": {"body": "I check on line 74 that it divides 2. The IQ is in uint16\\_t\u2019s so it just needs to divide 2 and not 4. We pass it padded to uint32\\_t\u2019s to the library but we receive it in uint16\\_t\u2019s.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/52/_/diff#comment-75097192"}}
{"comment": {"body": "So why you need to truncate it here?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/52/_/diff#comment-75100913"}}
{"comment": {"body": "the library is expecting uint32\\_t; from that it extracts the 2 \\* int8\\_t fields that it needs", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/52/_/diff#comment-75101003"}}
{"comment": {"body": "The library can only handle 3000 IQ samples. Our recordings have a variable number of samples \\(mostly 4000\\). I just remove the first 1000 on the line we\u2019re commenting on \\(which are the least likely to have packet IQ and are probably just noise\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/52/_/diff#comment-75101329"}}
{"comment": {"body": "The library is expecting a uint32\\_t per sample with the 16 most significant bits set to zero and the 16 least significant bits containing both the I and Q data \\(8 bit each\\).\n\nThe IQ I receive in this function comes as a buffer of uint16\\_t\u2019s per sample, I pad it on line 78 with two bytes of zeroes per sample to have a uint32\\_t per sample.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/52/_/diff#comment-75101553"}}
{"comment": {"body": "Nice!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/52/_/diff#comment-75135755"}}
{"comment": {"body": "Got it.  \nThanks!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/52/_/diff#comment-75137325"}}
{"title": "Feature/BIS-4552 add more statistics", "number": 520, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/520", "body": "Add CFO statistics\nAdd draw_pickles_diagrams script - to draw CFOs of several pickles on the same graph\nAdd statistics for similar pickles\nSort results by similar temperatures\nFix critical bug when running on py fingerprint\nFix path issue for easier local use"}
{"comment": {"body": "Great work!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/520/_/diff#comment-113465807"}}
{"title": "Indicate if failed to change WiFi state", "number": 521, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/521", "body": ""}
{"title": "Feature/seperate debug app", "number": 522, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/522", "body": "Seperated debug and release apps to allow us to install the debug app without messing with the phone's currently installed app\nWill now build and archive release builds"}
{"title": "Added signing", "number": 523, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/523", "body": ""}
{"title": "Fix broken stand-alone demos", "number": 524, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/524", "body": "The demos were broken from miss-handling of the beacon_id field from the new app"}
{"title": "App version 1.9.0, Fixed bug where app crashed if WIFI is off", "number": 525, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/525", "body": ""}
{"title": "Feature/cfloat cfo", "number": 526, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/526", "body": "Phase is now in radians\nFix CFO stuff and tests\n\nTODO:\n\ncoarse CFO still used int units"}
{"title": "Float instead of Q31", "number": 527, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/527", "body": "Use float instead of Q31\nFix get_prev set_prev in monitor\nChange test_ITD_005 packet to one that is invalid\nMore flexible std in ITD_training_classification __init_falling_trans_model\natan2 with LUT\ntest_falling_transient_mean rewrite and xfail, test_falling_transient_exact xfail\nChange test_ITD_021 packet to one that is actually invalid\nStill at int64 for linear regression\nrelax thresholds for tests\nDelete unused code to pass coverage\nUse old atan2 impl for coarse CFO, might fix later\ncarg_cfloat unit tests"}
{"comment": {"body": "What is the run-time of feature extraction comparing before and after the change?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/527/_/diff#comment-114736678"}}
{"comment": {"body": "Float:\n\n```\n23.31275ms feature extraction\r\n24.14953125ms feature extraction\r\n23.37665625ms feature extraction\r\n23.5169375ms feature extraction\r\n24.11959375ms feature extraction\r\n24.2484375ms feature extraction\r\n23.1049375ms feature extraction\r\n23.40734375ms feature extraction\r\n22.9411875ms feature extraction\r\n23.332375ms feature extraction\r\n22.63246875ms feature extraction\r\n23.08203125ms feature extraction\r\n23.1355ms feature extraction\r\n23.00825ms feature extraction\r\n23.02178125ms feature extraction\r\n23.4008125ms feature extraction\r\n22.94203125ms feature extraction\r\n22.38428125ms feature extraction\r\n23.20928125ms feature extraction\r\n22.38746875ms feature extraction\r\n22.61096875ms feature extraction\r\n23.04978125ms feature extraction\r\n23.06696875ms feature extraction\r\n23.10128125ms feature extraction\r\n22.48646875ms feature extraction\r\n22.56515625ms feature extraction\r\n23.22734375ms feature extraction\r\n22.27696875ms feature extraction\r\n23.98546875ms feature extraction\r\n23.1839375ms feature extraction\r\n24.248375ms feature extraction\r\n23.1208125ms feature extraction\r\n23.35646875ms feature extraction\r\n23.26596875ms feature extraction\r\n24.40871875ms feature extraction\r\n23.11ms feature extraction\r\n23.00346875ms feature extraction\r\n22.646ms feature extraction\r\n23.65128125ms feature extraction\r\n23.0213125ms feature extraction\n```\n\nQ31:\n\n```\n30.51659375ms feature extraction\r\n30.0124375ms feature extraction\r\n30.41875ms feature extraction\r\n30.3053125ms feature extraction\r\n30.7194375ms feature extraction\r\n30.55215625ms feature extraction\r\n30.1870625ms feature extraction\r\n30.10890625ms feature extraction\r\n30.2480625ms feature extraction\r\n30.43234375ms feature extraction\r\n30.660375ms feature extraction\r\n30.056125ms feature extraction\r\n30.4461875ms feature extraction\r\n30.2260625ms feature extraction\r\n30.18928125ms feature extraction\r\n30.90184375ms feature extraction\r\n31.0709375ms feature extraction\r\n30.2493125ms feature extraction\r\n30.65834375ms feature extraction\r\n30.39275ms feature extraction\r\n30.1611875ms feature extraction\r\n30.83234375ms feature extraction\r\n30.41921875ms feature extraction\r\n30.26671875ms feature extraction\r\n30.04946875ms feature extraction\r\n30.77096875ms feature extraction\r\n30.37659375ms feature extraction\r\n30.24884375ms feature extraction\r\n30.0310625ms feature extraction\r\n30.70215625ms feature extraction\r\n30.35784375ms feature extraction\r\n31.1276875ms feature extraction\r\n31.4935625ms feature extraction\r\n30.544375ms feature extraction\r\n30.57228125ms feature extraction\r\n30.87046875ms feature extraction\r\n30.546ms feature extraction\r\n30.31059375ms feature extraction\r\n30.70778125ms feature extraction\r\n29.97596875ms feature extraction\r\n30.596ms feature extraction\r\n30.76603125ms feature extraction\r\n30.828625ms feature extraction\r\n30.53496875ms feature extraction\r\n30.75515625ms feature extraction\r\n30.78278125ms feature extraction\r\n30.4515ms feature extraction\r\n30.37821875ms feature extraction\r\n30.37209375ms feature extraction\r\n30.22234375ms feature extraction\r\n31.5705ms feature extraction\r\n31.28209375ms feature extraction\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/527/_/diff#comment-114750317"}}
{"comment": {"body": "What handles now that one of the numbers is 0 while the other is not?  \nI followed the code and it seems like it would crash..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/527/_/diff#comment-114751444"}}
{"comment": {"body": "The test is when both of the parts are zero. If one of them is not, it would enter the carg function.\n\nIf both of them are zero, it\u2019s an unanswered question; also described in `//TODO: handle case when amplitude is 0`.  \nMaybe we should leave this comment", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/527/_/diff#comment-114752024"}}
{"comment": {"body": "Fixed in 98e63e95f6c2e315339af1914e9f98354b55804c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/527/_/diff#comment-114759125"}}
{"comment": {"body": "23\\.5~ after, 30.3~ before. So a 22.5% reduction in featex time", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/527/_/diff#comment-114759945"}}
{"comment": {"body": "Nice!! :ok_hand: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/527/_/diff#comment-114814373"}}
{"title": "Feature/more stable imei android", "number": 528, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/528", "body": "Hopefully cause Android to return a more stable IMEI"}
{"comment": {"body": "Maybe we should request the permission instead of UIOFASVFUOIAVVBUI", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/528/_/diff#comment-114800058"}}
{"comment": {"body": "This function is only called after the user explicitly accepted/declined\u2026 Not sure what else to do? Show error and then crash? ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/528/_/diff#comment-114803858"}}
{"comment": {"body": ":thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/528/_/diff#comment-114809126"}}
{"comment": {"body": "version 10 and not 1.10.0 or so?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/528/_/diff#comment-114809186"}}
{"comment": {"body": "Never was a point in using 1.x.x, so I use the same version to display as the one that is used in advertisements.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/528/_/diff#comment-114884183"}}
{"comment": {"body": "Approve?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/528/_/diff#comment-114884244"}}
{"comment": {"body": "Did @{5b02c344cd95416ee040ad9c} approve this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/528/_/diff#comment-114885874"}}
{"comment": {"body": "Yes. He even said I should just use the versionCode directly in the text displayed but I told him to do it in a separate pull request", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/528/_/diff#comment-114885982"}}
{"title": "Feature separation graph demo for Tim", "number": 529, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/529", "body": ""}
{"title": "BIS-315 fix timing features to work with accurate timing reading", "number": 53, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/53", "body": "Moved to use accurate timestamp with timer0\nReduce allowed jitter of packet timing now that we are using the accurate timer\nEnable timing feature extraction\nUsing interval values of timing instead of the absolute timing in ms"}
{"comment": {"body": "Just a note: For some unclear reason, i\u2019ve experienced more persice timer readings while using the arm-2017-q4 toolchain\u2026 and so should you.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/53/_/diff#comment-75097761"}}
{"comment": {"body": "Noted.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/53/_/diff#comment-75102166"}}
{"comment": {"body": "Is it the value we allow the timing to deviate from 0 mod 625? If so, 5 is too small. For s8 for example 5-6 is very common. Maybe we should change it to 10ish?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/53/_/diff#comment-75138388"}}
{"comment": {"body": "Mich, did you consider changing it? I don\u2019t think that the code we pushed now to the develop branch is suitable for S8\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/53/_/diff#comment-75709282"}}
{"title": "Feature separation graph demo for Tim", "number": 530, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/530", "body": ""}
{"comment": {"body": "we want pictures!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/530/_/diff#comment-114777564"}}
{"comment": {"body": "Red - S10\n\nBlue - My S10\n\nGreen - S9\n\nPurple - LG G7\n\n![](https://bitbucket.org/repo/x8eLRbo/images/835591869-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/530/_/diff#comment-114783652"}}
{"title": "Add iq normalization algorithms; add fast sin/cos", "number": 531, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531", "body": "Add iq normalization algorithms, including fast sin and fast cos.\nFor now, LUT values for fast_sin/cos are one per degree (0-90). Well modify table according to required resolution later on when we test real usages of fast_sin/cos."}
{"comment": {"body": "Please benchmark the performance of these algorithms on the embedded platform - it\u2019s interesting to see how long they take in their current form", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531/_/diff#comment-114910670"}}
{"comment": {"body": "I don\u2019t think it\u2019s necessary. All these algorithms will be used in the hill feature and will be checked by the featureExtract benchmark.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531/_/diff#comment-114944616"}}
{"comment": {"body": "I didn\u2019t mean you write tests that do this - I meant it\u2019s interesting to just physically check how long they take to run and what will be their performance impact.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531/_/diff#comment-114944981"}}
{"comment": {"body": "Add linear interpolation?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531/_/diff#comment-114949101"}}
{"comment": {"body": "I'm not sure whether its significant in this case \\(needs to be investigated\\), but `amp_squared_sum` may grow rather large. Floating point values lose accuracy as they get larger.  \n  \nThis summation algorithm exists to compensate for that, we might need it in the future if we realize this summation is problematic:  \n[https://en.wikipedia.org/wiki/Kahan\\_summation\\_algorithm](https://en.wikipedia.org/wiki/Kahan_summation_algorithm)\n\n\u200c\n\nThe same thing applies for the `compute_mean_amplitude` function, which has the same problem, but less significant because it deals with regular amplitudes rather than squares of amplitudes.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531/_/diff#comment-114955060"}}
{"comment": {"body": "Was this added to this pull request by mistake?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531/_/diff#comment-114958130"}}
{"comment": {"body": "Yeaaaaa, thanks for that! \\(:", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531/_/diff#comment-115016066"}}
{"comment": {"body": "Thanks, you\u2019re right, I wanted to test it first to see if it\u2019s enough for real packets. If it\u2019ll be a problem I\u2019ll probably do the same as in the CFO \\(using int64\\) or using your algo \\(don\u2019t know maybe they\u2019re the same\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531/_/diff#comment-115018281"}}
{"comment": {"body": "No, just wanted to add this fix quickly", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/531/_/diff#comment-115025458"}}
{"title": "Updated jenkins to use the release app", "number": 532, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/532", "body": ""}
{"comment": {"body": "Nice PR!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/532/_/diff#comment-115573430"}}
{"comment": {"body": "IKR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/532/_/diff#comment-115575943"}}
{"title": "Feature/BIS-6079 wifi flag", "number": 533, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/533", "body": "Utilize connectivity_status for wifi flag\nSeep wifi flag to cfo model training and testing\nWifi CFO model is currently always accepting\nAll tests have wifi off"}
{"title": "Feature/BIS-6078 implement instant frequency feature extraction", "number": 534, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/534", "body": "Instant frequency"}
{"title": "Empty-handling of instant frequency feature", "number": 535, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/535", "body": ""}
{"comment": {"body": "what\u2019s the reason for putting the includes before the \\_\\_cplusplus guards?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/535/_/diff#comment-115389168"}}
{"comment": {"body": "There' sno reason to put them after\u2026it makes less sense", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/535/_/diff#comment-115389331"}}
{"comment": {"body": "consistency.. \n\n```\n# guard1\n# guard2\n...code be here...\n# end of guard2\n# end of guard1\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/535/_/diff#comment-115390066"}}
{"comment": {"body": "But it\u2019s not actually a guard. The purpose of extern \u201cC\u201d is modifying the function declarations in the **current file** to not have name mangling \\(because their definitions are compiled in C files and not C\\+\\+ files\\).\n\nBy putting the extern \u201cC\u201d before the include, you essentially put everything inside the include inside extern \u201cC\u201d. It might not do much harm but it doesn\u2019t make any sense - you indirectly put all the declarations of the included file into your extern \u201cC\u201d\u2026. Potentially creating nested extern \u201cC\u201d's inside extern \u201cC\u201d'. Again it doesn\u2019t do anything, but it\u2019s just weird.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/535/_/diff#comment-115391858"}}
{"comment": {"body": "is it necessary to hide this static assert from klocwork?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/535/_/diff#comment-115566881"}}
{"comment": {"body": "I created `NUM_VOTING_FEATURES` as a duplicate of `LEN_VOTING_INDEX` because MISRA doesn\u2019t allow to compare/add/subtract enums and regular values. So I bet that I try to compare them in the static assert MISRA will complain as well\u2026 No harm in hiding static asserts from MISRA, they\u2019re static", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/535/_/diff#comment-115571189"}}
{"comment": {"body": "I believe that every deviation as such requires bureaucracy, so that\u2019s why I prefer not to have them, even though they\u2019re harmless. @{5a49d431ef77662a7583f8f0} would know better", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/535/_/diff#comment-115573283"}}
{"title": "Feature/BIS-6084 em iteration", "number": 536, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/536", "body": "Add EM_step components and tests\nFix 32-bit tests (in gcc flags)\nStart to add placeholders for cfo wifi model"}
{"comment": {"body": "This pull request is open for reviews", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/536/_/diff#comment-115722078"}}
{"comment": {"body": "Reference python can be found here: [https://bitbucket.org/levl/levl/src/master/python/workspace/cfo\\_align/em.py](https://bitbucket.org/levl/levl/src/master/python/workspace/cfo_align/em.py)  \nFunction is `EM_step` and it calls `get_two_betas`, `calc_sigSqr` and `get_w`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/536/_/diff#comment-115778741"}}
{"comment": {"body": "POKE :knife: :knife: :knife: :knife: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/536/_/diff#comment-116025754"}}
{"title": "Naive low pass filter implementation for the whole packet", "number": 537, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/537", "body": ""}
{"comment": {"body": "is it necessary to keep it in float64 instead of float32?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/537/_/diff#comment-115802015"}}
{"comment": {"body": "this convolution is not really correct.. values from the edges are not part of the convolution. is this by design?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/537/_/diff#comment-115802553"}}
{"comment": {"body": "These are the numbers Nuriel gave me.\n\nThis first implementation is only needed to test if it helps us to get better results for instant frequency.\n\nOnce we decide to use it we will do a round of performance tuning", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/537/_/diff#comment-115810868"}}
{"comment": {"body": "Yes we dont want to allocate another buffer and we dont use the edges in feature extraction", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/537/_/diff#comment-115811021"}}
{"comment": {"body": "I suggest to add a unittest to this. And even move convolution to commons/signal\\_processing/.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/537/_/diff#comment-116023788"}}
{"comment": {"body": "I will add a unit test.\n\nThe problem with moving to commons is that it is not the general correct implementation of convolution like you yourself noticed\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/537/_/diff#comment-116028028"}}
{"title": "meta analysis (fisher) and t-test", "number": 538, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538", "body": "Fisher and t-test"}
{"comment": {"body": "all you had to do is follow the damn train, ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116101427"}}
{"comment": {"body": "ICE THOSE FOOLS CJ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116101614"}}
{"comment": {"body": "use float64\\_t instead of double", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116103482"}}
{"comment": {"body": "done in 15f8b15d4900013e6ebbfea12a2f7bd497039662", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116103792"}}
{"comment": {"body": "do we need this code in the PR?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116122350"}}
{"comment": {"body": "Oops, didn't mean to commit it. Will remove ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116122537"}}
{"comment": {"body": "Fixed in a15cb7e7d48df769780c76b238422dc977efe21f", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116140815"}}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/1895778804-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116317380"}}
{"comment": {"body": "now you know how I feel?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116333329"}}
{"comment": {"body": "\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a\ud83d\udd2a", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/538/_/diff#comment-116438974"}}
{"title": "Remove 32 bit compilation for MacOS", "number": 539, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/539", "body": "The i386 platform is deprecated starting from XCode 10."}
{"title": "BIS-37 code coverage in CI", "number": 54, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/54", "body": "Add gcov flags to compilation\nPublish code coverage in Jenkins"}
{"comment": {"body": "these are GNU-specific and will cause MSVC based builds to fail", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/54/_/diff#comment-75143172"}}
{"comment": {"body": "Disabled it for now in MSVC systems. 68db20c7b685c6136f8a676a354b06371ef7d5da", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/54/_/diff#comment-75152569"}}
{"title": "Fix bug BIS-6150: Small variance in channel slope causes CFO model to fail", "number": 540, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/540", "body": ""}
{"title": "Feature/BIS-6153 training classification", "number": 541, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/541", "body": "Add wifi training and classification based on EM algorithm\nFix android app\nUpdate monitor"}
{"comment": {"body": "Do you need this commented line?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/541/_/diff#comment-116923082"}}
{"comment": {"body": "For future reference", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/541/_/diff#comment-116923096"}}
{"comment": {"body": "Do you need these commented lines?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/541/_/diff#comment-116923392"}}
{"comment": {"body": "Yes, for future reference. They\u2019ll be used soon", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/541/_/diff#comment-116923433"}}
{"title": "Feature/BIS-6157 board extreme temperature", "number": 542, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/542", "body": "first draft of extreme temperature handling\n\n"}
{"comment": {"body": "Maybe you should add the script that generates those priors to the repository? \\(Maybe under the python\\_tools\\)  \nSo if we want to change them, it will be easy to do. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/542/_/diff#comment-117321097"}}
{"comment": {"body": "This is a research task. @{5b41d9de10d57114135eca66} or @{5b6154f66366b42ca0def662} please do that after you finish your tasks for V3\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/542/_/diff#comment-117323558"}}
{"comment": {"body": "You can remove this code", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/542/_/diff#comment-117719500"}}
{"comment": {"body": "We don\u2019t scale the variables in the end?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/542/_/diff#comment-117762941"}}
{"comment": {"body": "Looks good to me.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/542/_/diff#comment-117763029"}}
{"comment": {"body": "Right now we dont\u2026\n\nits problematic with x^2 variables and we decided it should work good enough for now", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/542/_/diff#comment-117817053"}}
{"title": "Analog relay detection feature", "number": 543, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543", "body": "Design document\n\nModel specifications document\n\n\nStarted implementing relay detection model.\nFixed compilation bug.\nAdded handling for channel seperation for the relay detection feature.\nAdded rolling sum calculation for the relay detection feature, also added p-value estimation for the classification phase.\nImplemented combined p-value calculation across all channels.\nMinor compilation error fix.\nFixed errors for compilation on mac.\nAdded tests for the relay detection feature extraction and training.\nAdded relay detection feature classification tests.\nAnalog relay detection classification fixes to match accuracy of floating points.\nAnalog relay detection bug fixes.\nFixed analog relay detection tests.\nFixed minor bug with progress struct initialization.\nAdded missing calculation of ampl_buff. Regression now works!\nAdded more tests for coverage.\nAdded relay detection structs to the python event description file.\nFixed python structs\nFixed python struct sizes.\nFixed hydra events\nAdded ctypes structs for relay detection model.\nFixed ctypes structs, struct tests are now passing\nFixed compilation error.\nMisra fixes.\nMisra fixes.\nMisra fixes.\npyfingerprinting struct size update.\nAdded relay detect feature mask to the pyfingerprinting project.\nFixed integration tests.\nAdded coverage.\nMisra fixes.\n\n"}
{"comment": {"body": "Why not start with 0?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-116941915"}}
{"comment": {"body": "Talk to Sigal. She might be already calculating this value for her algorithm and it can be re-used.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-116941956"}}
{"comment": {"body": "You forgot to check that the agc is 0 also in the start of the noise area.\n\nThis function will return true if the there is agc>0 in the beginning of the packet and changes to 0 during the noise section..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-116942471"}}
{"comment": {"body": "Is it even legal to do this cast by MISRA? \\(As the float value might be larger than MAX\\_INT32\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-116944317"}}
{"comment": {"body": "It is MISRA compliant, but you are right about the fact it can be larger than MAX\\_INT32, this will cause the index to result with 0, which is incorrect.\n\nWill fix.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-116953400"}}
{"comment": {"body": "1. According to Dialog, the start of the packet is not always guaranteed to be present, it depends on many factors but sometimes it can be just leftovers from the last packet.\n2. @{5bdac2ad92e2727e0939d08b} Has been using the same index for his research and have seen good results.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-116956006"}}
{"comment": {"body": "If i'm not mistaken i\u2019ve been told that if something like this happens, it means we should ignore the packet anyway as it might not be noise in that section.\n\n@{5bdac2ad92e2727e0939d08b} Did you see something related to this issue during your research?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-116956710"}}
{"comment": {"body": "We should ignore the packets. But to ignore the packet this function should return false :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-116957766"}}
{"comment": {"body": "Exactly as Mich said..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-116959547"}}
{"comment": {"body": "I\u2019m pretty sure it will\u2026\n\nCan you give an example of input that will not return a correct result?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117017224"}}
{"comment": {"body": "Except for `(packet->agc_transition_count == 0U)` which is redundant \\(it\u2019s always > 0 as first sample is always counted as a \u201ctransition from nothing\u201d\\), Guy\u2019s algorithm seems correct to me.\n\nLet\u2019s define the noise section to be \\[0, `NOISE_SECTION_END_INDEX`\\]\n\nAt the end of the day, the goal is to not have any non-zero-agc sections before the noise section ends.\n\nGuy\u2019s `for` loop finds the first non-zero-agc section.\n\nIf that section starts anywhere before the end of the noise section \\(`packet->agc_transitions[i].transition_idx <= NOISE_SECTION_END_INDEX`\\), then some part of that section is going to overlap with the noise section.\n\nIf the first non-zero-agc section starts only after the noise part ends, then the noise part is guaranteed to be entirely 0-agc.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117018089"}}
{"comment": {"body": "What\u2019s with the moving around c\\+\\+ guards? Any good reason not to keep them right after the header guards?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117321595"}}
{"comment": {"body": "Isn\u2019t this test `if (progress->channel_progress[i].num_packets > RELAY_DETECT_MIN_NUM_OF_PACKETS_FOR_CLASSIFY_PER_CHANNEL)` redundant when testing for `model->channel_valid` ?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117324602"}}
{"comment": {"body": "first use of `statistics_get_std_unbiased`. I\u2019m shocked", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117325376"}}
{"comment": {"body": "I had issues with that as well, it\u2019s due to a different include behaviour of Clang vs gcc that causes compilation errors in include from C vs includes from C\\+\\+", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117326028"}}
{"comment": {"body": "Yep.\n\nEach header file should take care for itself, we should not \u201cforce\u201d headers we do not own to use cpp.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117339769"}}
{"comment": {"body": "No, channel valid means that the model for this channel exists.\n\n`if (progress->channel_progress[i].num_packets > RELAY_DETECT_MIN_NUM_OF_PACKETS_FOR_CLASSIFY_PER_CHANNEL)` checks that we have received enough packets for this channel during this classification session.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117340181"}}
{"comment": {"body": ":crown: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117341507"}}
{"comment": {"body": "But they have decided to not start the noise section at 0 but in some other position so we need to take care of that as well..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117424042"}}
{"comment": {"body": "That\u2019s exactly what I\u2019ve tried to explain in my first comment here.\n\nWe expect the AGC vector to be zeroed out since the beginning of the packet, even though we only start using it at index 250.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/543/_/diff#comment-117435479"}}
{"title": "Feature/BIS-6080 implement hill feature", "number": 544, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544", "body": "Implement hill feature (its enabled)\nChange API so that feature extraction now has progress, and reset is required at beginning of training/classification\n\n"}
{"comment": {"body": "We already have `ampl_buff` inside `internal_packet_info_t` which is being using in both transient extraction and relay detection, we should all use this instead of calculating it again and again.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-116995627"}}
{"comment": {"body": "It is possible for the feature to fail extraction causing this value to retain at `LEVL_CLASSIFY_MORE_DATA_NEEDED`, making the whole classification process stuck.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-116996401"}}
{"comment": {"body": "This will make you send a valid feature \\(duplicate of the previous\\) as a new one even though you didn\u2019t use the packet at all.\n\nYou should mark the feature as invalid inside the feature\u2019s strcut.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-116998720"}}
{"comment": {"body": "That was the intent - the feature contains the result of the aggregated data. If the specific packet has invalid data, the feature should still contain the aggregated data.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-117049208"}}
{"comment": {"body": "I\u2019m calculating amplitudes of aggregated, averaged iqs.. it\u2019s not the same amplitudes as for this specific packet", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-117050125"}}
{"comment": {"body": "It\u2019s possible for all features currently, but I talked to Nuriel and Hill receiving noisy and invalid packets is a real possibility - I\u2019ll add a safety mechanism so we\u2019ll return a match after enough packets, let\u2019s say 25", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-117052430"}}
{"comment": {"body": "Right, i\u2019ve missed that.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-117061022"}}
{"comment": {"body": "Ok so I see we currently don\u2019t keep track of num packets in classification. I want to consult Nuriel again to understand if when we get those noisy packets it should affect other features as well. Leaving as-is for now, it\u2019s a general issue for classification for all features, we\u2019ll discuss in the office", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-117113077"}}
{"comment": {"body": "Do we really need the double precision here? Can\u2019t we store the SNR values in a logartimic dB scale?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-117323427"}}
{"comment": {"body": "Just a small note, the types are usually called complex64 \\(for single precision\\) and complex128 \\(for double precision\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-117323572"}}
{"comment": {"body": "Ok, thanks, I just want to be consistent with cfloat32\\_t. We can consider changing both later on..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-117397767"}}
{"comment": {"body": "It\u2019s an open question that I\u2019m leaving as possible improvement, but we need to check it, since a trivial solution requires more runtime - log for every sample. I\u2019ll talk to Nuriel about a possible solution, if there\u2019s a better one will be entered at a different PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/544/_/diff#comment-117400042"}}
{"title": "Added missing initialization of array in cfo wifi tests which caused failures.", "number": 545, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/545", "body": ""}
{"comment": {"body": "Great catch :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/545/_/diff#comment-116988234"}}
{"title": "Feature/cfo wifi reduce number of packets", "number": 546, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/546", "body": "Reduce number of wifi packets iteration to 252\n\n"}
{"comment": {"body": "It\u2019ll take too much time to create new datasets for the unittests, so I disabled them.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/546/_/diff#comment-117334040"}}
{"title": "Danielle's QTD test", "number": 547, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/547", "body": "Tests:\nFeature 016\nClassify 030, 048, 045, 046\nWiFi 002, 003, 004, 006, 008\nAll tests work (not necessarily pass) - except for LEVL_TEMPERATURE_UNKNOWN problem in Classify 045, 046. (@{5a49d431ef77662a7583f8f0} )\n"}
{"comment": {"body": "`get_features_from_packet_df` and `get_wifi_features_from_packet_df` are too tests-specific.  \nI suggest you put them with the tests using them, or in some helper file", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/547/_/diff#comment-117423289"}}
{"comment": {"body": "You should add assert or at least prints for feature extraction that fails - or maybe you test is doing it with printing the exception? did you try it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/547/_/diff#comment-117505967"}}
{"comment": {"body": "Also here, you should add assert that checks that training does not return error", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/547/_/diff#comment-117506228"}}
{"comment": {"body": "We discussed it, your code is OK", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/547/_/diff#comment-117508167"}}
{"comment": {"body": "We discussed it, your code is OK", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/547/_/diff#comment-117508188"}}
{"title": "Hotfix no progress in manual agent", "number": 548, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/548", "body": ""}
{"title": "BIS-6184 add instant frequency feature", "number": 549, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/549", "body": "Instant frequency implementation, fisher and t now 32-bit\nLowered t-test unit test allowed error threshold\nDisabled encryption, reduced memory consumption of various agents\nCompact location averages\nCompact CFO averages\nAlways modify output model in Levl_ClassifyFinalize Instfreq\nOnline training model backup so it can be restored after NO_UPDATE\nFixing merge stuff\nt-test variance edge cases\nRemoved useless ampl_buffer\n\n"}
{"comment": {"body": "We have statistics module. Wouldn\u2019t you like to reuse it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/549/_/diff#comment-117531321"}}
{"comment": {"body": "The count is kept separately to save memory space, so it doesn\u2019t really fit in this case.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/549/_/diff#comment-117557937"}}
{"comment": {"body": "`+ + sizeof(ret)` ? one plus is not enough?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/549/_/diff#comment-117825041"}}
{"title": "Add regression tests to the system", "number": 55, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55", "body": "Add a pure C++ implementation of the regression tests for a good testing performance (now runs under 3s).\nThe datasets are generate using a python script and written to file using capnp serialization."}
{"comment": {"body": "Nice!\n\nWhat is the dataset you attached? Does it cover our temperature range? Maybe each recording should have some information about itself that it will print together with the IMEI so we know what we are testing\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-76597666"}}
{"comment": {"body": "Build is failing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-76825420"}}
{"comment": {"body": "The Bitbucket plugin doesn\u2019t seem to support the LFS files checkout.\n\nTrying to think of a way to overcome this..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-76825469"}}
{"comment": {"body": "why am I not part of the party? :disappointed: \n\nOmer was working on the pythonic interface to the lib. What\u2019s the advantage of calling the lib via cpp and not via python? Then we wouldn\u2019t need the dependency on `capnp`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77032373"}}
{"comment": {"body": "Creating CapturedPacket structs in the Levl format \\(from Python Pickle format\\) takes too long", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77032899"}}
{"comment": {"body": "Right. I\u2019m still not fancy with the dependency on `capnp`  ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77035398"}}
{"comment": {"body": "The run-time mostly. In pure c\\+\\+ the run takes 3s vs 200s\\+ with the python wrapper.\n\nI\u2019m also not a big fan of the dependency as well. We could actually write a c\\+\\+ code to parse the json objects from the cloud..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77036073"}}
{"comment": {"body": ":astonished: \n\nI suggest we give python another try later on. Numpy and friends are wrapping C\\+\\+ code for speed reason, so we can probably achieve this as well.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77037407"}}
{"comment": {"body": "I added a README file to the directory to describe the dataset.\n\nIt\u2019s currently 8 phones from 4 different models \\(S8,S9,Mate 10, Pixel2\\) that we should be able to separate. Temperature range 25-30C.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77901406"}}
{"comment": {"body": "Please review. I want to merge it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77901456"}}
{"comment": {"body": "Are these files protected in some way or are they available to public?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77902270"}}
{"comment": {"body": "For not they are public but I\u2019ll add a key to pull them out of AWS in the future.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77902765"}}
{"comment": {"body": "who\u2019s calling this file?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77903333"}}
{"comment": {"body": "Currently no-one.\n\nIt\u2019s was called once to generate the datasets from the DB and will be called in the future when we are going to generate more datasets.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77907301"}}
{"comment": {"body": "Training can return an error -  we should assert on that", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77910058"}}
{"comment": {"body": "Here we should assert if training was not completed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77910350"}}
{"comment": {"body": "Fixed in [22db914](https://bitbucket.org/levl/bosch_integration/commits/22db914a465fa84d01ba5b7f7594fb067465277f)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77918643"}}
{"comment": {"body": "We are currently don\u2019t have an interface to handle failures in the training process.\n\nEach packet that fails is just ignored. So if we failure on all packets, the training will never be complete.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77918816"}}
{"comment": {"body": "the error handling is missing but it\u2019s a very good test so I think you can merge", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77920269"}}
{"comment": {"body": "error handling is missing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/55/_/diff#comment-77920329"}}
{"title": "Cached downloader will use symlinks to cache instead of copyfile", "number": 550, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/550", "body": ""}
{"comment": {"body": "Beautiful code", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/550/_/diff#comment-117509329"}}
{"title": "St will only be built on a proper builder to make sure that it has a license.", "number": 551, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/551", "body": ""}
{"title": "FIN-872 Danielle QTD", "number": 552, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/552", "body": "Fixed import loop\n______________________________\nTests:\nFeature 016\nClassify 030, 048, 045, 046\nWiFi 002, 003, 004, 006, 008\nAll tests work (not necessarily pass) - except for LEVL_TEMPERATURE_UNKNOWN problem in Classify 045, 046. (@Igal Lerer )\n"}
{"title": "Feature/BIS-6223 cfo x wifi online training", "number": 553, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/553", "body": "Online training logic for CFO x Wifi\nDifferent packets containers for regular training and online training\nAdd test for CFO wifi online training regression\nReduce model size\nUpdate dialog NVM memory so that model storage wouldnt crash\nCalibration tests: update file paths\nMonitor: fix colors\nMonitor: show more data needed and failure of new features as well (non-voting table features)\nFix develop issues on dialog\n\nHappy PRing :slight_smile:\n"}
{"comment": {"body": "Please simplify this function to have a more clear logic", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/553/_/diff#comment-117686237"}}
{"comment": {"body": "Updated logic in a17720646e5f473eada245906d95c210282e2bb3", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/553/_/diff#comment-117718954"}}
{"title": "Support phones without phone temperature sensors", "number": 554, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/554", "body": "In case of no temperature reading on training/classification the CFO model is going to be ignored."}
{"comment": {"body": "What\u2019s the plan to integrate this with the monitor? How do we test this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/554/_/diff#comment-117823998"}}
{"title": "Feature/disable timing until fixed", "number": 555, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/555", "body": "disable timing until it is investigated\ndisable timing\n\n"}
{"comment": {"body": "What about Levl\\_ModelLibSetCfg?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/555/_/diff#comment-117719188"}}
{"comment": {"body": "fixed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/555/_/diff#comment-117723171"}}
{"title": "Feature/relay detect feature QTD", "number": 556, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/556", "body": "Added QTD tests for relay detection model. Some minor fixes and adjustments to the relay detection model.\nFixes.\nIncreased number of packets for relay detect classification to 50. Fixed tests.\nCoverage fix.\nImplemented all ITD tests.\nAdded missing ITD test.\nFixed comment.\n\n"}
{"title": "Refactored feature extraction duration test to make it run as a quick system test.", "number": 557, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/557", "body": ""}
{"title": "Monitor bug fixes", "number": 558, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/558", "body": ""}
{"title": "Fix app for timing, enable timing", "number": 559, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/559", "body": "Fixing bug: timing with two beacons doesnt work.\n\nFixed the app so that with and without tid, data is set in the same order, so it can be retrieved in the same way in our c code.\nEnabled timing\nAlso fixed the wifi enabled flag position in the monitor.\n\n"}
{"title": "BIS-309 port the uart demo api to dialog", "number": 56, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56", "body": "Implemented the Bosch UART protocol on Dialog\n\nCreated bosch_uart_protocol that starts a UART listening task that intercepts demo commands then parses and calls the registered callback\nbosch_uart_protocol also supports sending commands\nAdded hexlify helper module with functions needed for protocol\nAdded LEVL_CONFIG_DEMO_PROTOCOL flag to enable/disable the protocol\nble_iq_advertiser_task.c will now init and run bosch_uart_protocol\nModified custom_config_qspi.h with new protocol config\n\n\n\nChanged the demo protocol 8x implementation to use square brackets\n\nRefactored fingerprinting logic out of ble_iq_advertiser_task.c into fingerprinting.h and fingerprinting.c, integrated Bosch UART demo protocol into ble_iq_advertiser_task.c"}
{"comment": {"body": "Also here I think we want error message so if they send incorrect command we will quickly know what is the problem", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76612374"}}
{"comment": {"body": "Dont forget to add TID handling", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76613366"}}
{"comment": {"body": "The idea is that we may receive data that is not commands - the commands parser doesn\u2019t even consider something a command at this point. To consider something a command, it has to have all the properties of a command - \u2018\\[\u2019 then valid command type then valid BT Address then \u2018=\u2019 then valid TID then \u2018\\]\u2019. Anything else is just considered regular different input. It\u2019s not an error at this point, just a \u201cthis is not a command so I should go on\u201d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76613837"}}
{"comment": {"body": "This is not relevant for the demo - since we don\u2019t support more than one TID at a time, the TID field in a query command is meaningless.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76614507"}}
{"comment": {"body": "This should probably be an error message", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76614745"}}
{"comment": {"body": "This happens when they classification-query us before or during training. It\u2019s not an error on our end, it\u2019s an error on their end - the protocol gives us no means to notify them of an error. What do you propose we do?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76615154"}}
{"comment": {"body": "But if they do send a command and it is formatted incorrectly - will we get an error message?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76615301"}}
{"comment": {"body": "OK", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76615642"}}
{"comment": {"body": "No. We have to define what counts as a command. We can\u2019t print an error message for every non-command sequence. Maybe \u2018\\[\u2019 and \u2018\\]\u2019 COMMAND\\_LEN-bytes-apart is considered a command and any format errors after that we print? But then if they send commands too short we won\u2019t send an error message at all\u2026 it\u2019s problematic", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76616952"}}
{"comment": {"body": "Are you still reviewing this branch? Can you approve it? I want to merge", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76825344"}}
{"comment": {"body": "Got ahead and merge :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76825371"}}
{"comment": {"body": "using `char`s is not really MISRA compatible. we shouldn't use these in bosch\\_integration", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76987691"}}
{"comment": {"body": "don\u2019t mind me. this is not part of the lib, but it\u2019s still good practice", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76988336"}}
{"comment": {"body": "good code and refactoring process! love the UART task thing!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76988427"}}
{"comment": {"body": "I was afraid we were going to have to be MISRA compliant even in none-library code that is in bosch\\_integration", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/56/_/diff#comment-76988592"}}
{"title": "Update NVM driver params", "number": 560, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/560", "body": ""}
{"title": "Feature/bad merge fix", "number": 561, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/561", "body": "fixed bad merge\n\n\n"}
{"title": "Feature/BIS-6467 cfo x wifi 2 lobes", "number": 562, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/562", "body": "Add 2 lobes decision logic, with tests\nUpdate monitor to handle 1 lobe\n\nHappy PRing"}
{"title": "Added relay detect to the monitor. Fixed monitor feature indexing.", "number": 563, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/563", "body": ""}
{"comment": {"body": "do we need these prints?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/563/_/diff#comment-117831129"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/563/_/diff#comment-117936988"}}
{"title": "Feature/BIS-6561 add signalnoiseratio to feature", "number": 564, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/564", "body": "Refactored feature extraction duration test to make it run as a quick system test.\nAdd snr to featue, no tests yet\nSimpler code for now; snr calculated if hill enabled only\nadd low snr test\n\n"}
{"title": "Parallel execution of python tests for fast unit tests", "number": 565, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/565", "body": ""}
{"title": "Feature/cfo wifi missing code", "number": 566, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/566", "body": "Add collapse parameters to cfo wifi model with unittest\nFix some monitor issue\n\n"}
{"comment": {"body": "Wouldn\u2019t it better to have the condition outside the function? \n\nThe function should contain just the collapse logic.. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/566/_/diff#comment-117919096"}}
{"comment": {"body": "Yes, but coverage\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/566/_/diff#comment-117923036"}}
{"comment": {"body": "There isn\u2019t a test you can test this flow?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/566/_/diff#comment-117925503"}}
{"comment": {"body": "Working on it as part of the ITD, but I\u2019d prefer to push these changes before", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/566/_/diff#comment-117927070"}}
{"comment": {"body": "Ok.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/566/_/diff#comment-117927488"}}
{"title": "Qtd tests dor", "number": 567, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/567", "body": "Merged Danielle branch\nFixed SNR naming (qt 15)\n\nAdded SNR test (qt_feature_15)"}
{"comment": {"body": "these tests are empty. Is it on purpose?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/567/_/diff#comment-117881678"}}
{"comment": {"body": "you need to test the error variable that it\u2019s what you\u2019re expecting", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/567/_/diff#comment-117881826"}}
{"comment": {"body": "you need to populate the other fields \\(such as phone temperature\\) for feature extraction/train/classify as well. Take a look at `QT_WIFI_008` for example, something else that @{5cbc1fb4fdcd39100fda97de} wrote", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/567/_/diff#comment-117882175"}}
{"comment": {"body": "Comments in python should be written using `#`\n\nUse `'''` only for function documentation.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/567/_/diff#comment-118115763"}}
{"comment": {"body": "Try using names more descriptive than `q`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/567/_/diff#comment-118116733"}}
{"title": "Removal of encryption in test to speed-up", "number": 568, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/568", "body": ""}
{"title": "Feature/fix st debugging", "number": 569, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/569", "body": "Fix V3 ST support\n\n"}
{"title": "Added Base64 encoded, AES-encrypted dumping ability to 8x, will now dump encrypted model when training is done", "number": 57, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57", "body": ""}
{"comment": {"body": "Technically when sizeof\\(fingerprint\\_model\\) % AES\\_BLOCK\\_SIZE == 0 you added a padding when it\u2019s not needed.\n\nHowever it\u2019s not very important..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-76641564"}}
{"comment": {"body": "cool usage", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-76989538"}}
{"comment": {"body": "recurring calls to `b64_aes_mem_dump` would reinitialize them aes peripheral. we might want to avoid that", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-76991233"}}
{"comment": {"body": "You have to re-initialize for every \u201ctransaction\u201d. Initialization is simply telling it what to do where to copy from what\u2019s the key etc.  \n for every transaction -  \n init\\(setup\\) \u2192 start\\(\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-76991363"}}
{"comment": {"body": "you\u2019re right, but init is also configuring interrupts and clocks. not that critical, along with your comment on the thread-safety", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-76991902"}}
{"comment": {"body": "Yep. Nothing we can do about it anyway, unless we write our own driver for the AES peripheral", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-76992054"}}
{"comment": {"body": "mmm... no way to use `hw_aes_hash_restart`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-76993182"}}
{"comment": {"body": "It just runs the same transaction again \\(assumes everything is the same \\(length, location, etc\\) except for the data\\). We can\u2019t make that assumption \\(we can check if it\u2019s the same and then choose between init\\+start and restart but I don\u2019t think the benefits are good enough\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-76998717"}}
{"comment": {"body": "The moment we start to print data other than the model \\(features or something\\) it will just keep init\\(\\)-ing anyway", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-76998846"}}
{"comment": {"body": "I would prefer you at least pad this buffer with random bytes, using ecb will not reveal our model since its already less than 16 bytes, but it will uncover the size of our model..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/57/_/diff#comment-81436940"}}
{"title": "Remove failure of building model if timing and hill features are not built", "number": 570, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/570", "body": ""}
{"title": "QT45 and Q46 now passing", "number": 571, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/571", "body": ""}
{"title": "Feature/6 anchors", "number": 572, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/572", "body": "6 Anchors\n\n"}
{"title": "add board bower 2,3 to CFO wifi model", "number": 573, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/573", "body": ""}
{"title": "BIS-6542 bug fix - removal of cfo symbol in obfuscated lib", "number": 574, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/574", "body": ""}
{"comment": {"body": "The 5 steps of MISRA:\n\n![](https://bitbucket.org/repo/x8eLRbo/images/2979403264-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/574/_/diff#comment-117967681"}}
{"title": "Added QT_TRAIN_10 test as regression test", "number": 575, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/575", "body": ""}
{"title": "Feature/BIS-6443 hill feature add tests and fixe", "number": 576, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/576", "body": "Add tests, fix feature extraction progress usage in regression tests\nSome hill config changes; Add no hill logic\nAlgorithmic changes according to Nuriel's work\nHill still disabled for some regression tests\n\n"}
{"title": "fix CFO confidence interval for extreme board temperature", "number": 577, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/577", "body": ""}
{"title": "Cancel width element of hill algorithm + Adjust threshold", "number": 578, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/578", "body": ""}
{"title": "Wifi itd", "number": 579, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/579", "body": "Add ITD 145, 148, 162\nUpdate 2 lobes decision\nUpdate CFO packet count acceptance; Accept for wifi online training also packets that are not in range\n\n\n"}
{"title": "BIS-310 simulate the bosch side of the uart demo api", "number": 58, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/58", "body": "Added bosch_demo_simulation/protocol.py that allows to easily simulate the bosch side of the UART demo API"}
{"comment": {"body": "Please fix the baudrate according to what Michael sent", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/58/_/diff#comment-76608279"}}
{"comment": {"body": "do we need further error handling? like resetting `rec_dat`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/58/_/diff#comment-76990286"}}
{"comment": {"body": "good idea", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/58/_/diff#comment-76990400"}}
{"comment": {"body": "Just a trick, you can write 1e6 :stuck_out_tongue_winking_eye: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/58/_/diff#comment-81435383"}}
{"title": "fix calibration after extreme board temperature changes", "number": 580, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/580", "body": ""}
{"title": "Writing model to flash now works", "number": 581, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/581", "body": ""}
{"title": "Feature/BIS-6623 hill and snr fixes", "number": 582, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/582", "body": "snr fixes, hill algorithm changes\nenable regressions for hill (not online training)\n\n"}
{"title": "Lowered number of required packets for relay detect feature to 30.", "number": 583, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/583", "body": "Will now disable relay detection model if learned in a noisy environment."}
{"comment": {"body": "Can we put the condition to be per channel? i.e need at least 10 packets for one of the channel..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/583/_/diff#comment-118132944"}}
{"comment": {"body": "No\u2026 thats exactly what we\u2019ve changed in c4dee177ef993245be1e23911866bfc52c38f057\n\nThis matches Gal\u2019s python code.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/583/_/diff#comment-118136461"}}
{"title": "Feature/android beacon slider fix", "number": 584, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/584", "body": "Lowered number of required packets for relay detect feature to 30. Will now disable relay detection model if learned in a noisy environment.\nFixed android beacon slider.\n\n"}
{"title": "Replace isnanf with c++ equivalent", "number": 585, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/585", "body": ""}
{"title": "Humane disable of instant frequency feature", "number": 586, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/586", "body": ""}
{"title": "Feature/commit hash in boot event", "number": 587, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/587", "body": "Added commit hash to the boot events\nAdded check for commit hash when receiving boot event\n\n"}
{"comment": {"body": "what about dirty commits?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/587/_/diff#comment-118208923"}}
{"comment": {"body": "Do you have any suggestions for what to do with them?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/587/_/diff#comment-118219906"}}
{"comment": {"body": "Just notify that they are dirty", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/587/_/diff#comment-118220333"}}
{"title": "Made feature struct a bit smaller by moving around fields, preventing unneeded padding", "number": 588, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/588", "body": "s"}
{"title": "Feature/store online training packets in model", "number": 589, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/589", "body": "Add model stored packets container; which contains packets for next time we finalize (if we dont have enough packets for finalize)\nUpdate logic for counting and performing online training\nFix one unassigned variable\nAdd unittests\n\n\n"}
{"comment": {"body": "no", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/589/_/diff#comment-118245307"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/589/_/diff#comment-118248554"}}
{"title": "Created a stripped down version of libfingerprinting with CFO only", "number": 59, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/59", "body": ""}
{"title": "Reduce model stuct size", "number": 590, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/590", "body": ""}
{"title": "Anchors will timeout featex", "number": 591, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/591", "body": ""}
{"title": "Export to pandas", "number": 592, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/592", "body": ""}
{"comment": {"body": "is this a duplication of the monitor columns? Maybe we should join them. We wouldn\u2019t remember to update both of these tables", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/592/_/diff#comment-118288259"}}
{"comment": {"body": "unnecessary new line", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/592/_/diff#comment-118288380"}}
{"comment": {"body": "Beautiful code! Well done!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/592/_/diff#comment-118288421"}}
{"comment": {"body": "It\u2019s PyCharm autoformat protecting us from long lines", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/592/_/diff#comment-118315418"}}
{"comment": {"body": "It\u2019s slightly different, and not necessarily every column you want to display you also want to put in the dataframe and vice-versa", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/592/_/diff#comment-118315872"}}
{"title": "ST fix flash bug", "number": 593, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/593", "body": ""}
{"title": "Feature/BIS-6669 hill downsample", "number": 594, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/594", "body": "Downsample for hill only\nDisable returning error for low snr (it breaks tests and would take time to make them work)\n\n"}
{"comment": {"body": "FeatureExtract with the downsampled hill now takes 46-47ms", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/594/_/diff#comment-118342229"}}
{"title": "Feature/BIS-6157 board extreme temperature", "number": 595, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/595", "body": "fix xfailed calibration tests\n\n\n"}
{"title": "Hill final model", "number": 596, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/596", "body": ""}
{"comment": {"body": "Why is this disabled?.. It\u2019s the main test that checks not match for hill", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/596/_/diff#comment-118412282"}}
{"title": "Feature/BIS-6757 timing should not create model if there are too many bins", "number": 597, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/597", "body": "Very simple fix for the timing problem - In some phones we see there are too many bins filled, consistently. Usually there are 5 or 9 and thats it.\nSolution: When building timing model, even before cleaning less loaded bins, check if there are at least 20 filled bins, each filled with 2 or more. If so - set n_bins to 0 in the model.\nDuring classification, if ANY of the anchors have a timing model with 0 bins, timing returns match.\n"}
{"comment": {"body": "Why set n\\_bins to 0 rather than keep it how it was and just later check \\(`n_bins >= (uint8_t)(TIMING_MODEL_MAX_BINS * 2U`\\) as a mark for whether the model \u201cexists\u201d or not? We\u2019re just losing information here and it may be interesting for debugging", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/597/_/diff#comment-118414757"}}
{"comment": {"body": "The model contains max 10 bins, so not possible.  \nAnyway I rather keep it simple: bad data in training \u2192 no model.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/597/_/diff#comment-118420296"}}
{"title": "Fixed test test_QT_TRAIN_010_number_of_packets_for_training", "number": 598, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/598", "body": ""}
{"title": "Feature/BIS-6772 fix hill not match test", "number": 599, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/599", "body": "Enable not match test - Use a different pickle for it\n"}
{"title": "Feature/FIN-263 Transient extraction", "number": 6, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/6", "body": "*creating stub of falling transient\n*starting to implement helper functions for falling transient\n*fixing feature extraction cmakelist\nadding fast math library with fast sqrt added q31 statistics library with mean, var and std *starting to add falling transient\nfix warning\nadding more fast math and q31 functions updating statistics API *adding \"extra macros\" file, until a better name is found\n*naive transient detection implementation finished. requires testing\n\nbugfix in transient extraction\n\nadded input valiation in transient extraction\nadded unittest to transient extraction\n\n\n\naccidentally implement rising transient extraction so refactored to support both rising and falling based on API input added simple tests\n\n*renaming to transient extraction\nCherry picked ee2d915\n*some comments\n*another compilation issue\n\n"}
{"comment": {"body": "n is a counter, so it probably need to be uint32\\_t or size\\_t", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/6/_/diff#comment-70067329"}}
{"comment": {"body": "I think the CFO code also uses the amplitude values in it\u2019s calculation so it probably best to calculate the amplitude outside", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/6/_/diff#comment-70067510"}}
{"comment": {"body": "Looks like iterator is a better name", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/6/_/diff#comment-70126645"}}
{"comment": {"body": "Makes sense, but on the other hand, there\u2019s conversion to Q31 format for Q31 math on every sample and I wanted to spare the conversion so I stored in advance.\n\nWhat do you think?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/6/_/diff#comment-70131585"}}
{"title": "Fixed a bug where Jenkins would fail if the app is not installed on the test device", "number": 60, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/60", "body": "Please ignore this pull request if it fails"}
{"title": "Fix ctypes cfo_model_predict API mismatch", "number": 600, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/600", "body": ""}
{"title": "BIS-6383 wifi and board temp regression tests", "number": 601, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601", "body": "Update regression datasets creation logic to support wifi and board temperatures\nCreate new wifi regression test with S8/S9/Mate10Lite dataset\nCreate new board temperature online training regression test\nCreate new board temperature + wifi online training regression test\nRefactor dataset_loader to support rev4 format and to be backward compatible with rev3 files\nFix no confidence interval in wifi\nAdd weights to stored packets in wifi\nLower accepted board temperature range to 85 degrees\n\n\n"}
{"comment": {"body": "Why are these models in comment?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601/_/diff#comment-119181049"}}
{"comment": {"body": "Wifi online training regression is still WIP.  \nThis will change in the future", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601/_/diff#comment-119181500"}}
{"comment": {"body": "BUMP", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601/_/diff#comment-119619907"}}
{"comment": {"body": "Is this supposed to be removed?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601/_/diff#comment-119696761"}}
{"comment": {"body": "No. I left it for easier debugging. It doesn\u2019t affect runtime much", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601/_/diff#comment-119700771"}}
{"comment": {"body": "Please add some print here so we know we skipped training with this phone", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601/_/diff#comment-119708125"}}
{"comment": {"body": "Nice!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601/_/diff#comment-119708649"}}
{"comment": {"body": "There\u2019s already a print later checking `if (train_res == LEVL_TRAINING_COMPLETE_PARTIAL) {`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601/_/diff#comment-119825450"}}
{"comment": {"body": "When the future will arrive? :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/601/_/diff#comment-120278850"}}
{"title": "Feature/BIS-6587 hill monitor", "number": 602, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/602", "body": "Add Hill to monitor. \nWhen Hill column is chosen, we draw the data of the specific row chosen, along with the last feature of the other channels.\nThis represents best the collected data up to the row chosen."}
{"comment": {"body": "Choosing a specific row in the Hill column in the monitor presents the following graph. I will explain the data presented in a meeting.\n\n![](https://bitbucket.org/repo/x8eLRbo/images/3829497777-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/602/_/diff#comment-119198879"}}
{"comment": {"body": "Would\u2019ve been simpler as a dictionary of \\(anchor, channel\\) tuples rather than nested dicts", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/602/_/diff#comment-119324909"}}
