import json
import logging
import os
import tempfile
from typing import List

import pandas
import umap
from embedding_utils.instructor.instructor_bm25 import Instructor<PERSON>25EmbeddingGenerator
from pandas import DataFrame
from tqdm import tqdm

import git_utils.git_utils as git

from embedding_utils.embedding_generator import Embedding<PERSON>enerator
from integration_utils.integrations_provider import IntegrationsProvider
from integration_utils.message import MessageInterface

# --- NEW imports ---
from pinecone_utils.pinecone_index_config import IndexConfig, Metric
from pinecone_utils.pinecone_vector_store import PineconeVectorStore

from cluster_processor_image.cluster_processor import HDBScanProcessor, KMeansProcessor
from cluster_processor_image.cluster_topics_processor import (
    OpenAIClusterTopicsProcessor,
    TfidfClusterTopicsProcessor,
    CountClusterTopicsProcessor,
)
from cluster_processor_image.cluster_types import EmbeddedDoc, TopicClusterResultItem
from cluster_processor_image.config_loader import Config<PERSON>oader
from cluster_processor_image.embeddings_processor import (
    TfidfEmbeddingsProcessor,
    InstructorEmbeddingsProcessor,
    InstructorSourceCodeEmbeddingsProcessor,
)
from cluster_processor_image.expert.topics_experts_writer import FileTopicsExpertsWriter
from cluster_processor_image.path_constants import PROCESS_INPUT_GLOB, PROCESS_OUTPUT_DIRECTORY, PROCESS_OUTPUT_TOPICS_BASE_NAME
from cluster_processor_image.process_constants import (
    PROCESS_MIN_DOCUMENTS,
    PROCESS_MAX_DOCUMENTS,
    PROCESS_DOCUMENT_MIN_LENGTH,
    PROCESS_DOCUMENT_MAX_LENGTH,
)
from cluster_processor_image.source_classifier import get_source_classifiers
from cluster_processor_image.source_code_provider import SourceCodeProvider, SourceCodeMessage
from cluster_processor_image.cluster_topics_writer import FileClusterTopicsWriter, PineconeClusterTopicsWriter
from cluster_processor_image.dataframe_constants import (
    X_COLUMN,
    Y_COLUMN,
    CLUSTER_COLUMN,
    EMBEDDING_COLUMN,
    FILE_PATH_COLUMN,
    DOC_ID_COLUMN,
    CLUSTER_TOPICS_COLUMN,
    DOC_TOPIC_COLUMN,
)
from cluster_processor_image.topics_metadata import TopicsMetadata
from cluster_processor_image.topics_metadata_writer import FileTopicsMetadataWriter
from cluster_processor_image.expert.topics_experts_extractor import TopicsExpertsExtractor

# constants
from cluster_processor_image.pinecone_constants import PINECONE_API_KEY

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)


def load_config() -> ConfigLoader:
    return ConfigLoader()


def load_dataframe(name: str = "dataframe") -> DataFrame:
    base = os.path.dirname(os.path.realpath(__file__))
    return pandas.read_pickle(os.path.join(base, name))


def persist_dataframe(df: DataFrame, name: str = "dataframe"):
    base = os.path.dirname(os.path.realpath(__file__))
    df.to_pickle(os.path.join(base, name))


def create_vector_store(config: ConfigLoader) -> PineconeVectorStore:
    """
    Build and initialize our PineconeVectorStore.
    Expects ConfigLoader to provide:
      - pinecone_namespace: str
      - pinecone_hybrid_index: str
      - pinecone_dimension: int
      - pinecone_metric: Metric
    """
    idx_cfg = IndexConfig(
        name=config.pinecone_hybrid_index,
        dimension=768,
        metric=Metric.DOTPRODUCT,
    )
    store = PineconeVectorStore(api_key=PINECONE_API_KEY, index_config=idx_cfg)
    return store


def create_embedder() -> EmbeddingGenerator:
    return InstructorBM25EmbeddingGenerator()


def clone_repository(config: ConfigLoader) -> str:
    local_dir = tempfile.mkdtemp()
    opts = ["--no-tags", "--single-branch"]
    if config.repo_clone_auth:
        opts.append(f"--config http.extraHeader='Authorization: {config.repo_clone_auth}'")
    git.clone_with_config(
        repo_http_clone_url=config.repo_http_clone_url,
        local_dir=local_dir,
        multi_options=opts,
    )
    return local_dir


def get_topics_metadata(config: ConfigLoader, repo_dir: str) -> TopicsMetadata:
    if git.git_is_repo_empty(repo_dir):
        logging.info("Repository is empty, exiting …")
        return TopicsMetadata(commit_sha="0"*40, commit_timestamp="0")
    sha, ts = git.git_head(repo_dir=repo_dir)
    return TopicsMetadata(commit_sha=sha, commit_timestamp=ts)


def write_topics_metadata_to_file(meta: TopicsMetadata):
    FileTopicsMetadataWriter().writer_topics_metadata(topics_metadata=meta)


def get_source_code_messages(config: ConfigLoader, repo_dir: str) -> List[SourceCodeMessage]:
    provider = SourceCodeProvider(
        repo_id=config.repo_id,
        repo_dir=repo_dir,
        source_classifiers=get_source_classifiers(llm_endpoint_url=config.llm_endpoint_url),
    )
    msgs = provider.load()
    logging.info(f"Loaded {len(msgs)} source docs")
    msgs = [m for m in msgs if PROCESS_DOCUMENT_MIN_LENGTH <= len(m.get_text()) <= PROCESS_DOCUMENT_MAX_LENGTH]
    if len(msgs) < PROCESS_MIN_DOCUMENTS:
        logging.error(f"Only {len(msgs)} docs, need ≥{PROCESS_MIN_DOCUMENTS} → exiting")
        exit(1)
    return msgs[:PROCESS_MAX_DOCUMENTS]


def get_embeddings(messages: List[MessageInterface]) -> List[EmbeddedDoc]:
    embs = InstructorSourceCodeEmbeddingsProcessor().get_embeddings(messages=messages)
    if not embs:
        logging.error("Failed to generate any embeddings → exiting")
        exit(1)
    return embs


def get_clusters(messages: List[MessageInterface], embeddings: List[EmbeddedDoc]) -> DataFrame:
    df = HDBScanProcessor().get_clusters(messages=messages, embeddings=embeddings)
    if df.empty:
        logging.error("No clusters produced → exiting")
        exit(1)
    return df


def merge_file_paths(df: DataFrame, source_code_messages: List[SourceCodeMessage]) -> DataFrame:
    mapping = {
        DOC_ID_COLUMN: [m.get_id() for m in source_code_messages],
        FILE_PATH_COLUMN: [m.file_path for m in source_code_messages],
    }
    return pandas.merge(df, DataFrame(mapping), on=DOC_ID_COLUMN, how="inner")


def get_cluster_topics(df: DataFrame) -> DataFrame:
    return OpenAIClusterTopicsProcessor().get_cluster_topics(df=df)


def reduce_embeddings(df: DataFrame) -> DataFrame:
    reducer = umap.UMAP(n_neighbors=50, min_dist=0.2, metric="cosine")
    coords = reducer.fit_transform(df[EMBEDDING_COLUMN].tolist())
    df[X_COLUMN], df[Y_COLUMN] = coords[:, 0], coords[:, 1]
    return df


def write_topics_to_pinecone(
        config: ConfigLoader,
        vector_store: PineconeVectorStore,
        embedder: EmbeddingGenerator,
        df: DataFrame,
):
    writer = PineconeClusterTopicsWriter(
        repo_id=config.repo_id,
        pinecone_store=vector_store,
        embedder=embedder,
    )
    writer.write_cluster_topics(df=df)


def write_topics_to_file(df: DataFrame):
    FileClusterTopicsWriter().write_cluster_topics(df=df)


def get_topics_experts(repo_dir: str, df: DataFrame) -> DataFrame:
    rows = []
    for c in tqdm(df[CLUSTER_COLUMN].unique(), desc="Extracting topic experts…"):
        cluster_json = df.query(f"{CLUSTER_COLUMN} == {c}")[CLUSTER_TOPICS_COLUMN].iloc[0]
        items = TopicClusterResultItem.from_json(cluster_json)
        for item in items:
            docs = df[df[DOC_ID_COLUMN].isin(item.doc_ids)]
            for _, r in docs.iterrows():
                rows.append({
                    CLUSTER_COLUMN:    r[CLUSTER_COLUMN],
                    FILE_PATH_COLUMN:  r[FILE_PATH_COLUMN],
                    DOC_ID_COLUMN:     r[DOC_ID_COLUMN],
                    DOC_TOPIC_COLUMN:  item.name,
                })
    edf = pandas.DataFrame(rows)
    return TopicsExpertsExtractor(repo_dir=repo_dir).extract_top_authors_for_topics(topic_df=edf)


def write_topics_experts(df: DataFrame):
    FileTopicsExpertsWriter().write_topics_experts(df=df)


def main():
    logging.info("Loading task config …")
    config = load_config()

    logging.info("Initializing PineconeVectorStore …")
    vector_store = create_vector_store(config=config)

    logging.info("Creating embedder …")
    embedder = create_embedder()

    logging.info(f"Cloning {config.repo_id} …")
    repo_dir = clone_repository(config=config)

    logging.info("Writing topics metadata …")
    meta = get_topics_metadata(config=config, repo_dir=repo_dir)
    write_topics_metadata_to_file(meta)

    logging.info("Loading source code messages …")
    source_msgs = get_source_code_messages(config=config, repo_dir=repo_dir)

    logging.info("Generating embeddings …")
    embeddings = get_embeddings(messages=source_msgs)

    logging.info("Clustering embeddings …")
    df = get_clusters(messages=source_msgs, embeddings=embeddings)

    logging.info("Merging file paths …")
    df = merge_file_paths(df=df, source_code_messages=source_msgs)

    logging.info("Annotating clusters with topics …")
    df = get_cluster_topics(df=df)

    logging.info("Upserting cluster topics into Pinecone …")
    write_topics_to_pinecone(config=config, vector_store=vector_store, embedder=embedder, df=df)

    logging.info("Dumping topics to disk …")
    write_topics_to_file(df=df)

    logging.info("Extracting topic experts …")
    experts_df = get_topics_experts(repo_dir=repo_dir, df=df)

    logging.info("Writing topic experts …")
    write_topics_experts(df=experts_df)


if __name__ == "__main__":
    try:
        main()
    except Exception:
        logging.exception("Unhandled exception in main()")
