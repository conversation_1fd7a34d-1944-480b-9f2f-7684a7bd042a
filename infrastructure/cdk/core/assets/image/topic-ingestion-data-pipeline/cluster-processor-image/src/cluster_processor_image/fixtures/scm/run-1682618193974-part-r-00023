{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-927983483", "body": ""}
{"comment": {"body": "no, the backend sets the flag on the Message model. to my knowledge, we don't filter on the backend because then we wouldn't be able to detect the event change on the client. @matthewjamesadam can explain better", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r839883347"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-928126677", "body": ""}
{"comment": {"body": "@jeffrey-ng when fetching updates, our API requires that the updated objects are models (because that is what the GET calls return).  The only way to say \"this message has been updated, and it is now deleted\" is to return a Message with an isDeleted flag.\r\n\r\nWe could definitely add a filter to only return undeleted messages, but we would *only* be able to use that on the initial fetch for the store, not on the subsequent queries.  For messages the total volume of models per thread is so small that I don't think this is worthwhile, but it might be worth looking into for other larger datasets (threads?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r839982912"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-928138443", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-928139130", "body": ""}
{"comment": {"body": "Good catch -- we should probably add a pattern to make this happen more automatically, since I think we'll run into this a lot.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r839991779"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-928140809", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-928272769", "body": ""}
{"comment": {"body": " It's unexpected to return deleted messages from a correctness standpoint. If the user deletes a message, I don't think it should be returned in API requests. Sometimes the deleted message could contain sensitive data which shouldn't be sent to clients.\r\n \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840091919"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-928275896", "body": ""}
{"comment": {"body": "What is the use case of having this global modal context provider?\r\n\r\nHaving the modal provider is useful to remove prop passing for state & helpers such as closeDialog but we can keep that localized to where modals are actually used.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840094245"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-928279709", "body": ""}
{"comment": {"body": "(discussed in person)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840097086"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-928298404", "body": ""}
{"comment": {"body": "The provider handles the actual rendering of the modals as well, ie the hook passes the dialog components into the provider which then handles the rendering/stacking internally. With the basic headlessui component, I think the Dialog components are just manually rendered in each of the views? \r\n\r\nI'm open to suggestions for alternative patterns, but this made sense to me. There's one (globalish) place that handles the rendering of the modals instead of multiple(?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840112858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-929234204", "body": ""}
{"comment": {"body": "I think since a dialog's `id` is generated internally in the provider, any outer view code using this context won't actually be able to close a modal using this method (it won't have the id).  We could just remove this for now if we don't think it's going to be used.  Another alternative would be to have `openModal` return a method that would close the modal.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840789098"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-929240710", "body": ""}
{"comment": {"body": "I like the modal pattern, nice! \ud83c\udf89 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840793695"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-929246878", "body": ""}
{"comment": {"body": "Can we do buttons in shared components like this?  I thought you had to use a platform-specific Button to get properly themed buttons?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840797944"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-929248010", "body": ""}
{"comment": {"body": "I think I like the latter -- I'm curious though, what would be a use case for needing to close a modal that's outside of the currently opened modal? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840798766"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-929251737", "body": ""}
{"comment": {"body": "TBH I can't think of a good case for that :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840801418"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-929252194", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-929258130", "body": ""}
{"comment": {"body": "I think what's happening is that all of the the client specific scss is being compiled despite the actual component itself not being rendered, so this element ends up getting styled by the client scss. That's probably... unintended behaviour??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840805864"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-929260539", "body": ""}
{"comment": {"body": "That's how the MessageEditor is designed to work, and it's fine as long as it's intentional.  Would this mean we can just have a single shared Button component that everyone uses, instead of also having separate application-specific Button  components?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840807452"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#pullrequestreview-929363686", "body": ""}
{"comment": {"body": "This may not work well for the web extension where we want to inject a classname into the button component.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r840880214"}}
{"title": "Deregister SM background tasks cleanly on shutdown", "number": 725, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/725", "body": "orderly shutdown of SM server and SM scheduler background tasks\nmoves JVM process to application level"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/725#pullrequestreview-926714481", "body": ""}
{"title": "Update office ip", "number": 726, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/726"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/726#pullrequestreview-926713016", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/726#pullrequestreview-926713212", "body": "Sure would be nice to have comments in JSON. Consider using HOCON."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/726#pullrequestreview-926713871", "body": ""}
{"title": "Address bugs in SM shutdown", "number": 727, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/727"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/727#pullrequestreview-926787092", "body": ""}
{"comment": {"body": "using `task` as the key was totally wrong. using a property on the class instead.\r\n\r\nThe general fault here is that you cannot use a `data class` like `task` as a key, because the key is effectively the serialized content of the entire class, which in this case had some property modifications; which means the key was constantly changing. I incorrectly assumed that data classes had references (pointers) like regular classes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/727#discussion_r839011966"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/727#pullrequestreview-928261120", "body": ""}
{"title": "Debug logging for schema update fail", "number": 728, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/728"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/728#pullrequestreview-926811141", "body": ""}
{"title": "Make pusher team bound", "number": 729, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729"}
{"comment": {"body": "Looks fine to me, will let Jeff and Matt give the final thumbs up", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#issuecomment-1083842401"}}
{"comment": {"body": "> I'm going to assume these changes were vlaidated with client?\r\n\r\nHave not validated with the client yet", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#issuecomment-1086762094"}}
{"comment": {"body": "Validated - working", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#issuecomment-1086767469"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-1004695094", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-1193556761", "body": ""}
{"comment": {"body": "testing testing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#discussion_r1031787909"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-926827832", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-926828050", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-926842840", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-926843243", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-926975209", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-929612240", "body": ""}
{"comment": {"body": "Since the push API is team bound, you can only have a single team member here @jeffrey-ng ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#discussion_r841130651"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-929612280", "body": ""}
{"comment": {"body": "Adding team here is just poor man's access control", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#discussion_r841130694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-929619643", "body": "I'm going to assume these changes were vlaidated with client?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-930641430", "body": ""}
{"comment": {"body": "I'm not sure if this is what you're getting at, but the client already makes separate pusher calls for each team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#discussion_r841921667"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-930768418", "body": ""}
{"comment": {"body": "Exactly ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#discussion_r842011199"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/729#pullrequestreview-959642034", "body": ""}
{"title": "Webview rendering skeleton", "number": 73, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73", "body": "Shows a new Webview-rendering skeleton."}
{"comment": {"body": "Anyone care to review this?  It's ready to go in I think.  I'm adding tests and storyboards but that can go in the next PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#issuecomment-1016945268"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-856136282", "body": ""}
{"comment": {"body": "This is just a demo command for now.  It sets up a timer that sends a new set of props to the webview every second, and handles values sent back from the webview.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r787267503"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-856138492", "body": ""}
{"comment": {"body": "This is the new extension-side engine for rendering webview content. It has two generic properties:\r\n- The props sent to the view (ViewPropsT)\r\n- The props send back to the extension from the view (ExtensionPropsT)\r\n\r\nI treated this a bit differently then the WebviewContainer / WebviewController in OctoberDemo.  Those were wrappers, and so we had to wrap or re-export all of the webview / webpanel properties that we needed, which I think added a lot of unneeded code and made for some confusing lifetime management.  This class is *only* concerned with rendering the webview, and passing properties back and forth.  For any other property (title, etc) you just use the basic VSCode API, instead of a wrapped API.\r\n\r\n@rasharab I'm interested in your feedback on this...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r787269198"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-856138828", "body": ""}
{"comment": {"body": "These types are referenced by both the webview and the extension / command code", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r787269462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-856143641", "body": ""}
{"comment": {"body": "This is the webview side renderer.\r\n\r\nIt hooks up the webview, monitors incoming messages for prop updates, and on each prop update re-renders.  It also passes along helpers for sending messages back to the extension and getting/setting client-side state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r787273063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-856143748", "body": ""}
{"comment": {"body": "(The state stuff is TBD, I have some ideas on how to make this nicer from a react standpoint)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r787273144"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857161307", "body": ""}
{"comment": {"body": "I'm fine with a composition model.\r\nI actually agree with your points of the cruft from the prior implementation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r788006926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857161924", "body": ""}
{"comment": {"body": "Wut wut?\r\nYour figured that out?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r788007346"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857167356", "body": ""}
{"comment": {"body": "Yep, this enforces using the same types on both the extension and webview end of things.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r788011205"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857189084", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857200580", "body": "Re: file structure..\nI suppose the alternative would be to organize by functional groupings.. i.e. StartDiscussion/ and the related files (webview, tsx file, extension command) would live in this folder. This approach may be more straight forward semantically in terms of knowing every view and every file related to this view being in the one folder instead of knowing to jump around to find the required parts\nI think I probably still have a slight preference for this current structure since it's more organized -- but I do think the parity with web/ argument is a slightly uneven one since sorting is pretty straight forward on the web, all of the related view logic lives within the tsx files"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857219250", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857223579", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857238706", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857450572", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857473297", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857474428", "body": ""}
{"comment": {"body": "@matthewjamesadam could you remove the extra resolve on line 59", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r788229291"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#pullrequestreview-857476822", "body": ""}
{"comment": {"body": "\ud83d\udc4d done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/73#discussion_r788231128"}}
{"title": "Memoize data cache streaming hook", "number": 730, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/730", "body": "unbreaks the MessageEditor"}
{"comment": {"body": "comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/730#issuecomment-1083768168"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/730#pullrequestreview-926829032", "body": ""}
{"title": "Add video channel push", "number": 731, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/731", "body": "Summary\nMuch like threads before we realized messages carry an implicit participant update, video push requires that the subscriber is notified of both channel and participant updates"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/731#pullrequestreview-926855224", "body": ""}
{"title": "Source mark agent monitor", "number": 732, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/732", "body": "On startup, if a sourcemark agent (SMA) is not running, VSCode will launch it.  The launch code right now runs the java app which is pretty hokey, this will improve once SMA is a native binary\nOn startup, if a SMA is running, VSCode will attempt to connect to it\nEvery second, VSCode will send a heartbeat to the SMA\nIf the SMA is dead (pid no longer exists, or heartbeat fails), VSCode will re-launch the SMA\n\nThere is some trickiness around launching the SMA.  When VSCode starts up, it could open a bunch of workspaces, each of which has a separate extension instance.  We don't want each extension instance to launch a separate SMA, so we use a lockfile.  The lockfile contains a json object like so:\n{\n  pid: SMA pid\n  port: SMA port\n}\nExtensions lock the file to get exclusive read/write access, so only one extension instance will attempt to launch the SMA at a time.\nThis thing is going to be a nightmare to write tests for -- I'm thinking through how it can be unit tested."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/732#pullrequestreview-928115024", "body": ""}
{"title": "clean ecr repo every hour", "number": 733, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/733"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/733#pullrequestreview-926886028", "body": ""}
{"title": "Extension Discussions", "number": 734, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734", "body": "Lot of plumbing to setup web extension discussions.\n\nWill be subsequent PRs to cleanup styling.\nRefactor components."}
{"comment": {"body": "There's a `package-lock.json` update here without an associated `package.json` change -- I don't think that's expected?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#issuecomment-1085112055"}}
{"comment": {"body": "Still a lot of TBD for the CodeBlock including highlighting. Had originally attempted to do so in this PR but was growing too large so I stopped and branched off. These must have gotten in before I branched. Will remove the excess for now.\r\n\r\nSorry for the confusion. Should have added this to the top level description.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#issuecomment-1086221109"}}
{"comment": {"body": "> Still a lot of TBD for the CodeBlock including highlighting. Had originally attempted to do so in this PR but was growing too large so I stopped and branched off. These must have gotten in before I branched. Will remove the excess for now.\r\n> \r\n> Sorry for the confusion. Should have added this to the top level description.\r\n\r\nNo problem, it looks good!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#issuecomment-1086222816"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-928192957", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-928197423", "body": ""}
{"comment": {"body": "Clean up the commented-out stuff?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840032462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-928244871", "body": ""}
{"comment": {"body": "Will do. This was copied over from the Web's implementation but will need to do this from scratch. Design is different enough from the other clients as we want to match GH?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840060655"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-928249601", "body": ""}
{"comment": {"body": "Yeah I would imagine so", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840065217"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-928276682", "body": ""}
{"comment": {"body": "??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840094788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Stopped on the modal state management when I found out you were working on a context :)\r\n\r\nWill update this once we have things merged and figured out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840749651"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-*********", "body": ""}
{"comment": {"body": "I'm confused, is syntax hilighting basically disabled for now?  I'm guessing eventually this will work by doing the hilighting in the background JS by passing messages here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840818984"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Do we need this for now?  Do we know if we're going to launch a separate service worker, or use the background JS for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840819365"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-*********", "body": ""}
{"comment": {"body": "It looks like we've split the thread and message loading states here -- I'm not sure we need to do that?  Would it make things simpler if we just treated the UI as 'loading' until all its data has loaded?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840826386"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-929292135", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-929292650", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-929293466", "body": ""}
{"comment": {"body": "We should probably still check that there is at least one item so we don't crash when rendering on a mistake...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840831053"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-929296395", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-929383719", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#pullrequestreview-929388349", "body": ""}
{"comment": {"body": "Should be checked on line 9. Will update legibility .", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/734#discussion_r840897632"}}
{"title": "Update IP", "number": 735, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/735", "body": "Office wifi outage. New IP. Update."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/735#pullrequestreview-927833319", "body": ""}
{"title": "Video background service scaffolding", "number": 736, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#pullrequestreview-927958384", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#pullrequestreview-927960335", "body": ""}
{"comment": {"body": "Cleanup?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#discussion_r839866558"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#pullrequestreview-927961675", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#pullrequestreview-927962220", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#pullrequestreview-927964825", "body": ""}
{"comment": {"body": "This is going right back in again when I start adding background jobs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#discussion_r839869734"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#pullrequestreview-927965175", "body": ""}
{"comment": {"body": "Compiler gets annoyed with unused fields", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#discussion_r839869983"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/736#pullrequestreview-928024818", "body": ""}
{"title": "Cleanup cdk", "number": 737, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/737", "body": "Validated synth worked"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/737#pullrequestreview-927981065", "body": ""}
{"title": "Revert", "number": 738, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/738", "body": "Cleanup cdk\nRevert \"Cleanup cdk\""}
{"title": "Fix again", "number": 739, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/739", "body": "Revert \"Cleanup cdk (#737)\"\ntry again"}
{"title": "First crack at auth API", "number": 74, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/74", "body": "Preamble\nI got too deep into the implementation and decided to chunk it up, starting with this API definition. I also ripped out the authentication openapi generator templating because it's broken. I'll fix that in a separate PR.\nThe tests are also pretty janky, and will be fixed in subsequent PRs when I wire everything up. There's some refactoring we will need to do soon to wire up configurations and build out some testing utilities for mocks.\nSummary\nFirstly, this part of the API only deals with the client -> apiservice interactions. The two endpoints are:\n/login\nWill immediately bounce the client with a redirect to the auth provider\n/login/exchange?state=nonce;code=exchange_code\nAuth provider will bounce the client with a redirect back to us, containing the exchange code"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/74#pullrequestreview-856222607", "body": ""}
{"comment": {"body": "where are these used? looks like dead code. we're using 400/ErrorResponse400 above.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/74#discussion_r787333630"}}
{"comment": {"body": "Not part of your change, but can you change `version: 1.0.0` to `1`. We're not using semver.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/74#discussion_r787336297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/74#pullrequestreview-856244725", "body": ""}
{"comment": {"body": "UnauthorizedError is meant to be used. Pushed change. \r\n\r\nForbiddenError isn't used yet but will be for all team scoped endpoints", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/74#discussion_r787349091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/74#pullrequestreview-856245030", "body": ""}
{"comment": {"body": "Pushed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/74#discussion_r787349319"}}
{"title": "Return all threads if no repos", "number": 740, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/740", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/740#pullrequestreview-928004960", "body": ""}
{"title": "Update", "number": 741, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/741"}
{"title": "Fix test config", "number": 742, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/742"}
{"title": "Setup Repostore for web dashboard", "number": 743, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743", "body": "Initial implementation of RepoStore and the respective thread requests for web dashboard"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-928217899", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-930957783", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-930988871", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-930990207", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-931067972", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-931068944", "body": ""}
{"comment": {"body": "yeah another general renaming nit that's not necessarily blocking this PR but I really want to rename the ThreadAndParticipants (and MessageAndAuthor, etc) into ThreadAggregate / MessageAggregate - I know the idea is to not tack on a bunch of additional properties but at least this way it's more indicative of what it actually is imo \r\n\r\n(if no objections I can make this change in the next few days)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#discussion_r842222340"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-932205424", "body": ""}
{"comment": {"body": "I'll do this immediately after this PR is in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#discussion_r843044773"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-932206037", "body": ""}
{"comment": {"body": "Agree since we may want to add on additional IDs / references to the models.\r\nFor example, I think it may be necessary to tag on a repoID to the threadModel.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#discussion_r843045168"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-932208212", "body": ""}
{"comment": {"body": "This concept may be going away soon due to VSCode.\r\n\r\nWe will need to resolve a list of teamIDs and repoIds instead of just a single teamID", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#discussion_r843046738"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-932218073", "body": "lgtm pending matt's thumbs up"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/743#pullrequestreview-932549668", "body": ""}
{"title": "Add ability to get the auth token from one of the extensions", "number": 744, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744", "body": "Adds a new bidirectional streaming rpm endpoint called token. The approach:\n1) Client will open a stream to token by sending an initial Token message to the server\n2) SourceMarkServer receives the request and then \"saves\" the stream by registering it as a token provider in TokenManager\n3) When the source mark API client needs a token, it will request one from the TokenManager and the TokenManager will iterate through each provider and return the first one that gives back a token.\nNot the nicest code, suggestions welcome."}
{"comment": {"body": " Needs tests", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#issuecomment-**********"}}
{"comment": {"body": "+1 for first use of Flows \ud83d\ude42", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#pullrequestreview-928279980", "body": ""}
{"comment": {"body": "We iterate through each provider (one for each extension that has hit the `tokens` rpc operation) and get the first one that returns a token. If one does not, that probably means the extension is closed and we can remove it from the list.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#discussion_r840097303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#pullrequestreview-928288420", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#pullrequestreview-928288929", "body": "cool"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#pullrequestreview-928290892", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#pullrequestreview-928292095", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#pullrequestreview-928295152", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/744#pullrequestreview-928307662", "body": ""}
{"title": "Add ability to generate auth token for post requests", "number": 745, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/745"}
{"title": "Add basic unread pusher support", "number": 746, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/746", "body": "The current unread implementation works well for the my discussions mode, and it was largely not a huge problem to implement it for pusher.\nTESTING:\n1. Local stack testing with Kays stuff. Works well for my discussions. Used a series of curl commands to add messages as Mahdi. :)"}
{"comment": {"body": "I think @davidkwlam might be the right person to review this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/746#issuecomment-1086193416"}}
{"comment": {"body": "@kaych @matthewjamesadam So this will push an event for unreads for threads across _all teams_ for a user. Makes sense for the dashboard since it displays threads from all teams, but will extensions use this and if so will they'll handle appropriately?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/746#issuecomment-1086206101"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/746#pullrequestreview-929272140", "body": ""}
{"title": "Generate native macOS SM agent binaries", "number": 747, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/747", "body": "not working yet.\nremaining work:\n- [ ] gRPC\n- [x] ProcessBuilder"}
{"comment": {"body": "Abandoned.\r\n\r\n**SourceMark Kotlin Native Postmortem**\r\nhttps://www.notion.so/nextchaptersoftware/SourceMark-Kotlin-Native-Postmortem-1b414df203a94033a87dfa19f727f580", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/747#issuecomment-1088075503"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/747#pullrequestreview-929446118", "body": ""}
{"comment": {"body": "Third party library. Seirously lol\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/747#discussion_r840938955"}}
{"title": "Clean up yaml specs", "number": 748, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/748", "body": "Remove invalid references to id.\nCleanup threadId usage."}
{"title": "Fix up dao naming and lint rules", "number": 749, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/749", "body": "This pr does two things:\n1. Makes sure all DAO classes are properly named.\n2. Add abiltity to ktlint rules to ignore files/packages (i.e. test)\nPretty much all boilerplate outside of ktlint custom rules changes..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/749#pullrequestreview-929430440", "body": ""}
{"comment": {"body": "With the ktlint augmentation to allow for ignoring tests, we no longer need these disable pragmas in test files :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/749#discussion_r840926595"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/749#pullrequestreview-929433157", "body": ""}
{"title": "Add custom type, function, and op for full text search", "number": 75, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/75", "body": "These don't come with the Exposed framework, probably because they're Postgres-specific. Adding these now so that we can drop them in later when we create the models.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/75#pullrequestreview-856249964", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/75#pullrequestreview-857021093", "body": "Fair play for figuring this out!\nOne general comment about the code structure. Seem overly nested:\ndb/common/functions/TSVectorFunction.kt\ndb/common/ops/SearchOp.kt\ndb/common/types/TSVectorType.kt\nMaybe this is sufficient?\ndb/common/SearchOp.kt\ndb/common/TSVectorFunction.kt\ndb/common/TSVectorType.kt\nWe're likely to have more extensions in the future, so we may end up putting all of these in files like this, with a view to upstreaming:\ndb/common/OperatorExtensions.kt (SearchOp)\ndb/common/FunctionExtensions.kt (TSVectorFunction)\ndb/common/TypeExtensions.kt (TSVectorType)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/75#pullrequestreview-857155458", "body": ""}
{"title": "Fix scheduler bug and remove unnecessary fields", "number": 750, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/750"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/750#pullrequestreview-929432233", "body": ""}
{"title": "Fix Extension build", "number": 751, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/751", "body": "Merge issue. Missed required delete handler"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/751#pullrequestreview-929432432", "body": ""}
{"title": "Fix creating knowledge form", "number": 752, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/752", "body": "My recent changes slightly broke the discussion creation flow (as in the form webview panel wasn't closing after creation)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/752#pullrequestreview-929444382", "body": ""}
{"title": "Add editing messages on client", "number": 753, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/753", "body": "NOTES:\n* Needed to add a MockMessage body that doesn't include an image because we can't parse those right now and our stories were breaking because of this\n* ~Right now everyone can edit and delete every message -- this will need to be gated by comparing the currentPerson.id to the message author id (and can be layered on in another PR) -- but I'm unsure of how exactly to grab this data properly in the two clients~ Editing and deleting is now gated by a boolean calculated onto the MessageAndAuthor (needs renaming) model -- will refactor this logic out of the store once we have the currentPerson available in the webviews"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/753#pullrequestreview-930906431", "body": ""}
{"comment": {"body": "FWIW, we will likely be moving to a stream based approach for AuthStore where we will not be able to fetch properties in sync like this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/753#discussion_r842106866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/753#pullrequestreview-930907267", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/753#pullrequestreview-930910200", "body": "In the long run, we probably don't want to scope editing/deleting/... to isAuthor. Having proper roles and permissions is the long term goal (post April)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/753#pullrequestreview-931018131", "body": ""}
{"comment": {"body": "should be ok. we can grab it from the AuthStream and combine it with this stream, and then pass it down to this fn. additionally, I think Matt said we're gonna move towards getting the currentPerson to be available to the webviews at which point we can move this logic down to the view level ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/753#discussion_r842188858"}}
{"title": "Fix bug where SourceMarkScheduler.startTask was not returning", "number": 754, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/754", "body": "\nSeems like the use of coroutineScope was not returning so any call to registerRepoPath would never never return."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/754#pullrequestreview-929466638", "body": ""}
{"title": "Video channel maintenance job", "number": 755, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755", "body": "Summary\nThis is a background job that \"cleans\" video channels in the following way:\n\nIf a channel's max time is reached (30 minutes for example), the participants are kicked from the channel\nIf a participant has stopped heart-beating the channel beyond a max interval, the participant is kicked\nIf a participant has left the channel for some period of time, the participant object is deleted. We have to keep the participant object around for a while in order for push updates to work (clients need to know that a participant has left)\nAdditionally if the participant that left is the host, there's some additional cleanup that happens for active video recordings, and host status is handed off to the first available participant."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-929481164", "body": ""}
{"comment": {"body": "Moved this file so it's accessible by other services", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#discussion_r840966989"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-929481504", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-929622931", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-931086916", "body": "great work!"}
{"comment": {"body": "can you explain? seems weirdly high", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#discussion_r842235164"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-932181077", "body": ""}
{"comment": {"body": "Probably a naming fail. When a participant's heartbeats expire, they are flagged as `exited`, but the model is not deleted for a short period of time to allow for push events to fire and everyone to catch up. After the participant model has been set to `exited`, then after `staleParticipantExpiry` is reached the model is nuked.\r\n\r\nThis can probably safely be set to the same value as the channelExpiry since that's the maximum possible time the channel will be alive. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#discussion_r843020251"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-932187790", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-932201625", "body": ""}
{"comment": {"body": "Does this break our updating model?  Our push event system assumes that models are never actually deleted.  I guess the failure scenario here is when someone closes their laptop for a day in the middle of a video call, in which case the video states might be messed up?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#discussion_r843042139"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-932229847", "body": ""}
{"comment": {"body": "This is a general problem that we have to solve:\r\n1. Privacy: Doesn't seems right to _mark_ content as deleted, but not actually delete it. See also\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/776#discussion_r842206307.\r\n2. Complexity: Not being able to cleanup server objects just because of poller leads to scale issues eventually, but also the complexity is error prone.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#discussion_r843061974"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-932241981", "body": ""}
{"comment": {"body": "I think privacy concerns are handled by redacting data from archived/deleted models when they're fetched (that's our plan for deleted messages, anyways) -- this honours privacy concerns for deleted objects, but also allows archiving and unarchiving objects when a model requires it.\r\n\r\nAs for complexity, what you're saying is true, but it feels like a hard limitation on our pusher scheme.  If we think we need to delete objects, we might need to revisit the scheme (post-April), as it will be a source of a lot of subtle bugs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#discussion_r843071079"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-932456325", "body": ""}
{"comment": {"body": "I think this is why I set a very long expiry for \"cleanup\". The existence of these objects is only important as long as a client needs to retrieve them through push. In the scenario where a laptop is closed for a while I think it's probably better if the client does a full `get` on the channel data when it recovers. This is practical as a strategy for video content since it's bound to a single channel, but doesn't scale for other things like threads and messages. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#discussion_r843215166"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/755#pullrequestreview-932592831", "body": ""}
{"title": "Use proto2", "number": 756, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/756", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/756#pullrequestreview-929493503", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/756#pullrequestreview-929493577", "body": ""}
{"title": "Add video channel push", "number": 757, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/757", "body": "This was already approved but I'm resurrecting it to make reviews of the base branch a little easier"}
{"title": "Add auth allow list", "number": 758, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758", "body": "Summary\nTo create an allow list we need to use a stable identifier. The only such ID in our system is the external SCM identifier. \nTo facilitate this I had to add the externalId (GitHub identity) as a claim in the token. No harm to this, except that anyone with this token will be able to identity exactly who owns it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#pullrequestreview-929611687", "body": ""}
{"comment": {"body": "Are these println statements intentional ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#discussion_r841129720"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#pullrequestreview-929611705", "body": ""}
{"comment": {"body": "Yes, it's a token generation script", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#discussion_r841129749"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#pullrequestreview-929611754", "body": ""}
{"comment": {"body": "One more database hit to load the identity on refresh (we were previously only loading teams for the identity). This is needed to get the SCM ID", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#discussion_r841129866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#pullrequestreview-929611774", "body": "Thank you!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#pullrequestreview-929611783", "body": ""}
{"comment": {"body": "TODO: `IdentityStore`/`IdentityService`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#discussion_r841129882"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#pullrequestreview-929611804", "body": ""}
{"comment": {"body": "This is a \"special\" ID in the allow list", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#discussion_r841129908"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/758#pullrequestreview-929611820", "body": "Other than my question on the println statements the rest look fine."}
{"title": "AddSupportForPerRepoThreadChannels", "number": 759, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/759", "body": "There was.a request to get per-repo thread push notification.\nThis pr adds support for that.\nThe channel uri would be in the format:\n\"/threads?repoIds={repo1}&repoIds={repo2}...\""}
{"comment": {"body": "Not to throw a spanner in the works but there's a stale PR of mine that I really need to go in soon or we're going to keep clobbering each other: https://github.com/NextChapterSoftware/unblocked/pull/729", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/759#issuecomment-1086761016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/759#pullrequestreview-929619470", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/759#pullrequestreview-929619489", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/759#pullrequestreview-929619534", "body": ""}
{"title": "Wire up ktor auth dsl", "number": 76, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/76", "body": "Summary\nThis is the start of what we need to do to wire up token based authorization. I've added the Authentication installer, although at the moment it doesn't do anything because there are no endpoints that use it. \nTesting\nThe validator is missing a test - will add \nExtras\nI slightly refactored the base to a \"module\" layout. In Server.kt the idea is that we can pass multiple modules and chain them like the good old days. Not sure if anyone has looked at EngineMain layout? Also - why Netty over CIO?"}
{"comment": {"body": "Can we add a mock test endpoint that requires the JWT token for client testing purposes?\r\nWorking on the client side of this hopefully today.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/76#issuecomment-1016701134"}}
{"comment": {"body": "> Can we add a mock test endpoint that requires the JWT token for client testing purposes?\r\n> Working on the client side of this hopefully today.\r\n\r\nNext PR. I need to fix the openapi templating to generate the auth wrappers for endpoints", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/76#issuecomment-1016719860"}}
{"comment": {"body": "One thing worth noting is that the verifier and token generator are operating within the apiservice. The API allows for a remote verifier, which we can wire up once we split this to its own service", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/76#issuecomment-1016850906"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/76#pullrequestreview-856417387", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/76#pullrequestreview-857167371", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/76#pullrequestreview-857315523", "body": ""}
{"comment": {"body": "Issuer and expiry validation is done as part of the verifier", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/76#discussion_r788114800"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/76#pullrequestreview-858451304", "body": ""}
{"title": "AddSupportForPerRepoThreadChannels", "number": 760, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/760", "body": "There was.a request to get per-repo thread push notification.\nThis pr adds support for that.\nThe channel uri would be in the format:\n\"/threads?repoIds={repo1}&repoIds={repo2}...\"\nOther Changes:\n1. Clean up some parsing of channel crap."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/760#pullrequestreview-929625015", "body": "Looks semantically correct to me!"}
{"title": "Update makefile and readme", "number": 761, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/761"}
{"title": "Move to using Yaml Serialization for Kotlin and Typescript", "number": 762, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/762", "body": "Apparently, performance of yaml parsing is superior to json."}
{"title": "Video recording background maintenance job", "number": 763, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763", "body": "Summary\nWill stop and process video recordings that exceed the maximum recording duration (currently 60 seconds)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#pullrequestreview-931066377", "body": ""}
{"comment": {"body": "move to top-level in file, rather than on class instance", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#discussion_r842220299"}}
{"comment": {"body": "these are usually empty afaict.\r\n\r\nif they are expected to be empty normally, then maybe we can have default (non-abstract) no-op implementations in the base BackgroundJob interface class, so that we don't have these stubs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#discussion_r842222033"}}
{"comment": {"body": "optional: this class looks _very_ similar to other lock providers.\r\n\r\nWe could have a GlobalLockProvider interface, from which the VideoRecordingLifecycleLockProvider and GitHubAppInstallationMaintenanceLockProvider; and an ObjectLockProvider that is used for instance locks like PullRequestIngestionLockProvider. Something like that make sense?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#discussion_r842921140"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#pullrequestreview-932119618", "body": ""}
{"comment": {"body": "I observed the same thing and agree. I'll do this in a separate PR though so I'm not doing stealth refactors on that thing for PR ingestion etc", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#discussion_r842969055"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#pullrequestreview-932126045", "body": ""}
{"comment": {"body": "Hahah I did this originally and the consensus was that subclasses should be forced to implement: https://github.com/NextChapterSoftware/unblocked/pull/662#discussion_r836056466\r\n\r\nI'm not opinionated about this myself, although I tilt more towards getting rid of stuff that isn't commonly used", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#discussion_r842976309"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#pullrequestreview-932201408", "body": ""}
{"comment": {"body": "I see. ok, that makes sense.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/763#discussion_r843041981"}}
{"title": "Value Cache Stream", "number": 764, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/764", "body": "Very basic stream for local values."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/764#pullrequestreview-930960764", "body": ""}
{"comment": {"body": "Do you prefer terriers or poodles @jeffrey-ng ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/764#discussion_r842145960"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/764#pullrequestreview-930966624", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/764#pullrequestreview-930967056", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/764#pullrequestreview-931007047", "body": ""}
{"comment": {"body": "I'm an equal opportunity dog fan.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/764#discussion_r842180476"}}
{"title": "Fix context menu display bug", "number": 765, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/765", "body": "only in safari:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/765#pullrequestreview-930705122", "body": ""}
{"title": "AddTeamMembersPushChannels", "number": 766, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/766", "body": "This pr adds channels for determining team member updates.\nAlso augments team member logic for modified at."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/766#pullrequestreview-930775922", "body": ""}
{"comment": {"body": "There's a slight deviation from the query in the API operation that the clients will use get the modified team members (`getTeamMembers`) but not a big deal in practice, and also not unique to this (the other push queries are not exactly the same as the query used to retrieve the objects). No action necessary now, just wanted to raise that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/766#discussion_r842016632"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/766#pullrequestreview-930782581", "body": ""}
{"comment": {"body": "You're right. valid point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/766#discussion_r842021423"}}
{"title": "Add asset service skeleton", "number": 767, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/767"}
{"title": "Added S3 bucket and CF behavior for static assets", "number": 768, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/768", "body": "Added new stack to create customer assets' buckets\nAdded placeholder lambda edge funtions\nAdded CF behavior for /assets/*\nAdded public site bucket to hold error images and assets\nCleaned up some old unused dependencies \nExported bucket object in Agora streaming stack"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/768#pullrequestreview-930862867", "body": ""}
{"title": "Switch TS proto lib", "number": 769, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/769", "body": "When generating sourcemark TS code, use the same TS generator we use for messages.  It generates more idiomatic typescript and handles optionals.\nAs part of this, I switched the token response message to use the google wrapper type, as we do in messages.  This allows the optional scalar value to work correctly.\nNote that all required properties that refer to other messages are generated as optionals!  I'm not sure why."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/769#pullrequestreview-930877213", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/769#pullrequestreview-930885711", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/769#pullrequestreview-930893162", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/769#pullrequestreview-930893415", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/769#pullrequestreview-930894307", "body": ""}
{"title": "Fix warning", "number": 77, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/77", "body": "It was complaining:\nw: /Users/<USER>/Code/codeswell/apiservice/src/main/kotlin/com/codeswell/db/common/OperatorExtensions.kt: (19, 20): 'TypeExtensions.TSVectorType' is a final type, and thus a value of the type parameter is predetermined"}
{"comment": {"body": "We should treat warnings as errors", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/77#issuecomment-**********"}}
{"comment": {"body": "^ Yup: https://github.com/Chapter2Inc/codeswell/pull/78", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/77#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/77#pullrequestreview-*********", "body": ""}
{"title": "add service account for assetservice", "number": 770, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/770", "body": "Service account has been created. EKS deploys are currently manual."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/770#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Should we add listobjects?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/770#discussion_r842102557"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/770#pullrequestreview-*********", "body": ""}
{"title": "Add helm charts ofr assetservice", "number": 771, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/771"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/771#pullrequestreview-*********", "body": ""}
{"title": "Deploy asset service", "number": 772, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/772"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/772#pullrequestreview-*********", "body": ""}
{"title": "Implement badges", "number": 773, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/773", "body": "\nfor reference:  for packaging this extension with this proposed api\nNOTES:\n- Will need to use an Insiders build of vscode to be able to see the badges\n- there is a bug where badge doesn't show up if the user hasn't clicked the Unblocked icon to initialize the extension (and create the webview)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/773#pullrequestreview-931008460", "body": ""}
{"title": "Oops", "number": 774, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/774"}
{"title": "Web extension code highlighting", "number": 775, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/775", "body": "Setting up code highlighting for web extension\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/775#pullrequestreview-931012812", "body": ""}
{"comment": {"body": "Maybe add a dependency array to useAsyncOperation so we can use it in situations like this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/775#discussion_r842184851"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/775#pullrequestreview-931013590", "body": ""}
{"title": "Ability to archive and delete threads", "number": 776, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-931044723", "body": "I think delete should actually delete on server."}
{"comment": {"body": "if it's deleted, then it should not be possible to return in API, so it'd remove this bit", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#discussion_r842206307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-931053909", "body": ""}
{"comment": {"body": "I think the flag is there for the same reason why it's a flag on the Message model -- i.e. the change in the model to generate the push event (sort of described [here](https://github.com/NextChapterSoftware/unblocked/pull/724#discussion_r839982912))", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#discussion_r842211563"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-931089712", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-931917344", "body": ""}
{"comment": {"body": "What is `title` set to in this case?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#discussion_r842825335"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-932185108", "body": ""}
{"comment": {"body": "Hmm, probably the same thing? We don't ever clear the title right now do we? \r\n\r\nI guess it doesn't necessarily matter all that much because we won't ever be showing 'deleted' threads on the client, we just need the flag there to let the client know the Thread has changed so that the client hides it. cc: @davidkwlam for more thoughts? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#discussion_r843024555"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-932206322", "body": ""}
{"comment": {"body": "For archived threads, `title` definitely won't be clear since we'll probably want to show the thread even if its in an `Archived` folder (assuming that's what we end up having)\r\n\r\nFor deleted threads, I suppose it doesn't matter but the API service can just return an empty string in case we want to be conservative and not show any details. So in `ThreadDAO.asApiModel` we could do:\r\n\r\n```\r\n    title = when (isDeleted) {\r\n        true -> \"\"\r\n        else -> title\r\n    }\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#discussion_r843045370"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-932249704", "body": ""}
{"comment": {"body": "Ok, but `title` was just one example to illustrate the privacy point. _All_ of the fields should but nulled if we're taking this approach, it's not just enough to null the title.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#discussion_r843076854"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-932471715", "body": ""}
{"comment": {"body": "@kaych should follow the pattern of messages where we nuke the content. See `MessageStore.delete()`\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#discussion_r843230166"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-932472642", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#pullrequestreview-932574162", "body": ""}
{"comment": {"body": "@richiebres nullified the title and all the pr ingested data, `participants` are a list of uuids we tack onto the API model so I kept the logic there to send back an empty list if the thread is deleted ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/776#discussion_r843304684"}}
{"title": "Agora Client", "number": 777, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/777", "body": "Summary\nImplements Agora API calls for channel and recording management"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/777#pullrequestreview-931063762", "body": ""}
{"comment": {"body": "Could make all of these configurable in config... later :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/777#discussion_r842218290"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/777#pullrequestreview-932459241", "body": "partial review...\nEach client (agora, scm, redis) should be a separate Gradle subproject under the projects/clients/ directory. I realize you didn't put the existing pattern in place, but would be great to not continue this pattern."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/777#pullrequestreview-932516801", "body": "Looks fine to me, though if you could implement Richie's suggestion that would be "}
{"comment": {"body": "Detekt might nail you for `MagicNumber` :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/777#discussion_r843262737"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/777#pullrequestreview-932590882", "body": ""}
{"comment": {"body": "> partial review...\r\n> \r\n> Each client (`agora`, `scm`, `redis`) should be a separate Gradle subproject under the `projects/clients/` directory. I realize you didn't put the existing pattern in place, but would be great to not continue this pattern.\r\n\r\nSeparate PR for this for sure. Coming up next", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/777#discussion_r843317336"}}
{"title": "Fix up readmes for secrets deployment", "number": 778, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/778"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/778#pullrequestreview-931084976", "body": "Thanks!"}
{"title": "Add java runner", "number": 779, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/779", "body": "Standardize the java set of arguments used to run our services.\nFuture plan is to be able to configure memory etc. per service via arguments if we have to, but I want to control stuff like gc etc. via a global jar runner.\nFor example, I\"m planning on moving us to ZGC.\n\nTesting:\nConfirmed successful deployment:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/779#pullrequestreview-931241317", "body": ""}
{"comment": {"body": "To be clear, Gradle will copy copy the run script to libs as part of the build process ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/779#discussion_r842349328"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/779#pullrequestreview-931241568", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/779#pullrequestreview-931760238", "body": ""}
{"comment": {"body": "yes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/779#discussion_r842715774"}}
{"title": "Treat warnings as errors", "number": 78, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/78", "body": "https://github.com/Chapter2Inc/codeswell/pull/77#issuecomment-**********"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/78#pullrequestreview-857192825", "body": ""}
{"title": "Get correct source snippet for a PR ingested comment", "number": 780, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/780", "body": "PR ingested comments currently show the diff hunk, but really want it to show the lines from the current version of the file. We can get this from the diff hunk if we do some processing.\nIf we did this right, then the source snippet lines will match exactly the lines in that file at that commit."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/780#pullrequestreview-931242001", "body": "Sweet!"}
{"comment": {"body": "maybe split onto many lines using triple `\u201c\u201d\u201d\u2026\u201d\u201d\u201d.trimIndent()` for readability?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/780#discussion_r842350191"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/780#pullrequestreview-931259167", "body": ""}
{"comment": {"body": "Yeah super unreadable but they\u0012're pasted from actual comments so I want to keep them to be 100% sure parsing works", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/780#discussion_r842363108"}}
{"title": "Add asset service ecr", "number": 781, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/781"}
{"title": "update", "number": 782, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/782"}
{"title": "Fix incorrect resource arn for postgres in prod for assetservice", "number": 783, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/783"}
{"comment": {"body": "Also, just updated the iamserviceaccount in prod via eksctl as well. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/783#issuecomment-**********"}}
{"title": "Consolidate TestUtils class to a single module", "number": 784, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/784", "body": "This class was floating around everywhere"}
{"comment": {"body": "thank you!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/784#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/784#pullrequestreview-*********", "body": ""}
{"title": "Improve Deployment Times", "number": 785, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/785", "body": "This pr does the following:\n1. Add a new custom gradle task (buildMatrix) that derives all the build tasks that we can run in project.\n2. We output that result as a json, and we consume that json to create a matrix of tasks to run. These matrix of tasks effectively parallelize across ALL our build targets, and auto-adds service jars to an artifact that we deploy.\ni.e.\nNEW: (7M)\n\nOLD: (12M) \n\nMore work has to be done to share stuff between jobs, but this is a start."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/785#pullrequestreview-932411816", "body": ""}
{"title": "Add slack notification for GitHub actions (WIP)", "number": 786, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/786", "body": "Added Slack alarms for main branch builds and deploys of service and infra workflows\nBuild failure alarms are sent to infra-alarms-dev\nService/infra  deploy failure alarms are sent to each corresponding env"}
{"comment": {"body": "can we fire only if:\r\n- [ ] action fails\r\n- [ ] action occurs on _main_", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/786#issuecomment-1089126733"}}
{"comment": {"body": "Yep I commented that out for testing. It should only fire for main build/deploy failures", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/786#issuecomment-1089131155"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/786#pullrequestreview-932316191", "body": "sweet"}
{"title": "Remove node dependencies", "number": 787, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/787", "body": "No longer using these"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/787#pullrequestreview-932344339", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/787#pullrequestreview-932349892", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/787#pullrequestreview-932357710", "body": ""}
{"title": "Fix code block line range", "number": 788, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/788", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/788#pullrequestreview-932480736", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/788#pullrequestreview-932515703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/788#pullrequestreview-932580983", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/788#pullrequestreview-932581832", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/788#pullrequestreview-932821269", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/788#pullrequestreview-933742462", "body": ""}
{"comment": {"body": "Worth adding a jest test for this ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/788#discussion_r844127307"}}
{"title": "Test ZGC", "number": 789, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/789"}
{"title": "Web JWT Doc", "number": 79, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79", "body": "Writing down some research and assumptions I'm going to be taking before jumping into the auth code."}
{"comment": {"body": "Notion doc: \r\nhttps://www.notion.so/nextchaptersoftware/JWT-Doc-ab0b2bbbec3f44c0a1a771337c3eaecb", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#issuecomment-1016798457"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#pullrequestreview-857377846", "body": ""}
{"comment": {"body": "I don't think this is an issue for us. The tokens are created and validated by the service, and only the service has the validation key. Forgery is not possible here, but stolen tokens are. \r\n\r\nFor that we will implement request signature validation", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#discussion_r788149015"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#pullrequestreview-857381320", "body": ""}
{"comment": {"body": "Additionally, we're using the Authorization header, which set by the application and not accessible to other web apps", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#discussion_r788152896"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#pullrequestreview-857384308", "body": ""}
{"comment": {"body": "If we use a cookie like 'x-authentication-jwt' or something then it's a different story, but the service is wired to check the Authorization header", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#discussion_r788156187"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#pullrequestreview-857408920", "body": ""}
{"comment": {"body": "Fingerprinting via custom cookie seems like an interesting idea and relatively trivial to implement: https://cheatsheetseries.owasp.org/cheatsheets/JSON_Web_Token_for_Java_Cheat_Sheet.html#token-sidejacking", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#discussion_r788181745"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#pullrequestreview-857411342", "body": ""}
{"comment": {"body": "Is there anything that needs to be done to pull the fingerprint cookie?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#discussion_r788183564"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#pullrequestreview-857413991", "body": ""}
{"comment": {"body": "FYI: I am planning to do this next", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#discussion_r788185381"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#pullrequestreview-857426487", "body": ""}
{"comment": {"body": "https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/JSON_Web_Token_for_Java_Cheat_Sheet.md#token-sidejacking\r\n\r\nAccording to this, it's a hardened cookie, aka unaccessible from the browser.\r\n\r\nI'm not 100% sure how this will translate to our other clients though such as vscode. Maybe not necessary since we don't need to worry about XSS?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#discussion_r788194702"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#pullrequestreview-857587514", "body": ""}
{"comment": {"body": "It won't hurt them. Most http clients will just ignore cookie response headers and discard them by default. I won't implement this for now but it will be trivial to wire it up later", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/79#discussion_r788310516"}}
{"title": "Filter threads by team when repo IDs are not supplied", "number": 790, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/790", "body": "This has the side effect of preventing requests for repos in other teams. There is still a wider effort that needs to happen to to lock down resources to team () but at least this covers threads for now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/790#pullrequestreview-932522166", "body": ""}
{"title": "Fixworkflow", "number": 791, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/791"}
{"title": "faster kube deployments", "number": 792, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/792", "body": "Scaled out both dev and prod clusters\nChanged Kubernetes deployments to bring up 100% of new pods during deployment (kinda blue/green -ish)\nRight sized a few of the services (cpu/mem)\nRegenerated helm charts"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/792#pullrequestreview-932513971", "body": ""}
{"title": "scaling pusher resources back up", "number": 793, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/793", "body": "Pusher service is crashlooping \nIt's due to memory pressure. Increased its memory allocation back to the original value."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/793#pullrequestreview-932580150", "body": ""}
{"title": "Attempt faster build", "number": 794, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/794"}
{"title": "Change addListener() to subscribe() to unsubscribe", "number": 795, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/795"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/795#pullrequestreview-934168682", "body": ""}
{"title": "Integrate agora client", "number": 796, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/796", "body": "This is almost guaranteed to break when real clients are hitting it, but we'll have to just go through that pain..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/796#pullrequestreview-932592590", "body": ""}
{"comment": {"body": "I want to call out that it's super weird this passed the linter @rasharab @richiebres @matthewjamesadam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/796#discussion_r843318700"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/796#pullrequestreview-932649548", "body": ""}
{"title": "Add basic asset models and apis", "number": 797, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797", "body": "This is a work in progress, so these models and apis will ikely change.\nThis should hopefully unblock Mahdi a bit as the apis are now sparsely implemented."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-933926957", "body": ""}
{"comment": {"body": "This doesn't seem to follow the pattern of other APIs. Example:\r\n`/teams/{teamId}/assets/{assetId}`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#discussion_r844260235"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-933927555", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-933930115", "body": ""}
{"comment": {"body": "I think the implication of this is that we're no longer using edge to authenticate a download? If that's not the intention then we may want to consider creating a new auth type for this endpoint that only the edge lambda can auth against, along with the user token", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#discussion_r844263462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Yeah. We are following the pusher model so that we can use prefixes for alb routing. I had assets in there but Mahdi said To remove it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#discussion_r844266712"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-*********", "body": ""}
{"comment": {"body": "No. Cloud front will have an endpoint that will use this to query. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#discussion_r844267582"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-*********", "body": ""}
{"comment": {"body": "The content that is sync'd with GitHub PR comments will be an actual URL, so that we can display that \"unauthed\" download image. In turn, the web extension will have to set the user token before the browser fetches the resource. \r\n\r\nAlternatively, the web extension can do the media dance and get a _different_ url before rendering, but I think it's better to leave the browser to this task?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#discussion_r844267892"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-933937302", "body": ""}
{"comment": {"body": "Do we want this encoded in the model or should it be a runtime thing based on content type, environment, etc?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#discussion_r844268594"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-933940442", "body": ""}
{"comment": {"body": "\ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#discussion_r844269605"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#pullrequestreview-933940794", "body": ""}
{"comment": {"body": "Getting auth going is secondary to me getting this up. That's a story I can solve After the fact", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/797#discussion_r844269671"}}
{"title": "Refactor clients projects", "number": 798, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#pullrequestreview-932654541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#pullrequestreview-932742176", "body": ""}
{"comment": {"body": "@richiebres This module is problematic because it has a dependency on `models`. The source of that dependency is `Provider`. Untangling that may require quite a bit of surgery (although probably worth the effort, but maybe not now)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#discussion_r843428512"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#pullrequestreview-932742566", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#pullrequestreview-932750193", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#pullrequestreview-932858167", "body": ""}
{"comment": {"body": "I see. The `Provider` type should moved, along with other simple common types, to a new `:projects:datatypes` sub-project", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#discussion_r843491719"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#pullrequestreview-933769585", "body": ""}
{"comment": {"body": "Yup - something for another PR though. Let's get this basic structure in place and move on for now. If it affects build times then we can do this as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#discussion_r844146512"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/798#pullrequestreview-933793104", "body": ""}
{"title": "Skeleton for source mark integration in VSCode", "number": 799, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/799"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/799#pullrequestreview-932699206", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/799#pullrequestreview-933742417", "body": ""}
{"comment": {"body": "I have a PR I'll be putting up today that removes this FYI...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/799#discussion_r844127282"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/799#pullrequestreview-933750641", "body": ""}
{"comment": {"body": "Ah never mind, misread", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/799#discussion_r844133191"}}
{"title": "Update baselines of main", "number": 8, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/8"}
{"title": "Move to aws postgres jdbc driver", "number": 80, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/80", "body": "This driver has some addiitonal layers to deal with Aurora clusters (we decided to use aurora clusters for other reasons Mahdi can talk about)\nIt is in preview mode, but the mysql driver for aurora is also the recommended jdbc driver for users using mysql aurora.\nWe will eventually move to this once this matures if we don't take this now.\n\nAnd then we have this: :)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/80#pullrequestreview-857240378", "body": ""}
{"title": "update", "number": 800, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/800"}
{"title": "Remove Unused  Dependencies", "number": 801, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/801"}
{"comment": {"body": "> did you find an auto-magic way to find unused deps, or was this manual?\r\n\r\nSomewhat magic.\r\n\r\nhttps://github.com/gradle-dependency-analyze/gradle-dependency-analyze\r\n\r\nHas some edge cases, so you have to rely on ci to ferret them out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/801#issuecomment-1089809061"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/801#pullrequestreview-932774219", "body": "did you find an auto-magic way to find unused deps, or was this manual?"}
{"title": "Update gradle wrapper", "number": 802, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/802"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/802#pullrequestreview-932859419", "body": ""}
{"title": "Fix logger", "number": 803, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/803"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/803#pullrequestreview-932930092", "body": ""}
{"title": "Render sourcemarks in the gutter", "number": 804, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/804", "body": "Render SourceMarks in the gutter.  In this PR the SourceMarks for an editor are pulled when the editor opens.  A follow-on PR will pull updated values once file content changes.\nThe testing story for this stuff is not simple, as it's mostly tying different VSCode APIs together.  I'll do some research on how we can start writing tests for things like this, but I'm not sure how easy it will be.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/804#pullrequestreview-933800427", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/804#pullrequestreview-933800809", "body": ""}
{"comment": {"body": "Same here -- this is not really being used any more.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/804#discussion_r844169418"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/804#pullrequestreview-933801425", "body": ""}
{"comment": {"body": "FYI @richiebres @davidkwlam this is the interface any object providing source marks for gutter rendering should implement.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/804#discussion_r844169889"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/804#pullrequestreview-933803018", "body": ""}
{"comment": {"body": "And FYI @richiebres @davidkwlam you can replace this property to change where the gutter renderer pulls sourcemarks from", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/804#discussion_r844170669"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/804#pullrequestreview-933817606", "body": "lgtm"}
{"title": "Update RepoStore setup for VSCode", "number": 805, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805", "body": "Setup Repo resolution for VSCode.\nRefactors general setup of RepoStore and streams.\nRemoves all mock repo IDs from VSCode + Web.\nRemoved all mock teamIDs from VSCode. Web extension next. Web will require some product decisions."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934177490", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934182417", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934186001", "body": ""}
{"comment": {"body": "In this kind of case, where RepoStore is clearly dependant on TeamStore, I think it might make sense to internalize this within RepoStore itself?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#discussion_r844451155"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934191149", "body": ""}
{"comment": {"body": "Fail over to the first remote if `origin` doesn't exist?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#discussion_r844454972"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934201736", "body": ""}
{"comment": {"body": "Since this is a promise, we can actually go through all the remotes, if we wanted, until we find a match...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#discussion_r844462778"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934202619", "body": ""}
{"comment": {"body": "This isn't used anywhere? Get rid of it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#discussion_r844463505"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934205358", "body": ""}
{"comment": {"body": "teamIds will have duplicate teams -- we should make sure this is a unique set", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#discussion_r844465593"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934208656", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934209739", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934216711", "body": ""}
{"comment": {"body": "This is being used in CreateKnowledgeCommand.\n\nWhen a user makes a discussion, we want to figure out *what* repo they are making a command for based on the current document.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#discussion_r844474294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-934219472", "body": ""}
{"comment": {"body": "I had intentionally kept this outside the TeamStore so the lifecycle is well defined.\nThis was also done so that we didn't have any window.showErrorMessage logic live within the store.\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#discussion_r844476492"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Might have a way to do this while keeping those two points into account... Giving it a shot.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#discussion_r844477072"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/805#pullrequestreview-*********", "body": ""}
{"title": "Thread achived marker should be a date", "number": 806, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/806", "body": "In order to properly sort (and eventually auto-delete archived threads), there needs to be a date property indicating when a thread has been archived. Instead of just adding another property onto the model, the proposed change is to change the isArchived boolean property into an archivedAt Date property (and its presence would double as a boolean for the client to understand whether a thread has been archived or not). \nAdded a new endpoint to update this property \nWe should allow users to both archive threads and also restore archived threads, hence the request endpoint having a boolean property to indicate which direction to go"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/806#pullrequestreview-933982884", "body": "makes sense, good call"}
{"comment": {"body": "suggestion: I think it's simpler to add two API operations to eliminate some logic:\r\n\r\n```\r\nPUT  /teams/{teamId}/threads/{threadId}/archive:\r\nPUT  /teams/{teamId}/threads/{threadId}/restore:\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/806#discussion_r844297167"}}
{"title": "Setup CI & Chrome store Publishing", "number": 807, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/807", "body": "Setup some CI for web extension.\nModify manifest to support chrome store publishing.\nFixed some regressions in build due to missing CI"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/807#pullrequestreview-934294463", "body": ""}
{"title": "Split dev and prod secret configs", "number": 808, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/808", "body": "Created a separate ansible vault for prod secrets\nUpdated Make targets  and ansible playbooks to handle two environments\nAdded a new RSA keypair to Prod vault (also stored in 1p)\nUpdated Agora appID and Cert in both Dev and Prod"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/808#pullrequestreview-934094889", "body": ""}
{"comment": {"body": "We should probably down the line consider using roles for this sort of per-environment thing.\r\nThis is fine for now.\r\n\r\nhttps://docs.ansible.com/ansible/latest/user_guide/playbooks_reuse_roles.html", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/808#discussion_r844382030"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/808#pullrequestreview-934095112", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/808#pullrequestreview-934098983", "body": ""}
{"comment": {"body": "I have a bunch more work to do per service secret objects but this should do for now. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/808#discussion_r844385524"}}
{"title": "Reorganize logger initialization", "number": 809, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809", "body": "This PR changes the logger system to always use a single logger (logger from @shared-utils).\n\nEach app initializes the logger with its own logging properties and targets on startup\nAnyone logging should now always import { logger } from '@shared-utils' -- it makes logging more consistent across all projects\nFixes circular dependencies where logger initialization and API initialization depended on each other"}
{"comment": {"body": "Thanks @matthewjamesadam !", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#issuecomment-1090820001"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#pullrequestreview-934124545", "body": ""}
{"comment": {"body": "@richiebres I think this is equivalent to what you had before -- setting the context as a particular property on a child logger.  I think this is still worth having as a helper function (vs requiring every logger to do this) -- maybe this should be the `logger` call so everyone is forced to provide a context?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#discussion_r844406390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#pullrequestreview-934128111", "body": ""}
{"comment": {"body": "if no objections, then my preference would be better to force all callers to provide a context, making this the default `logger`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#discussion_r844408728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#pullrequestreview-934129250", "body": ""}
{"comment": {"body": "\ud83d\udc4d sounds good, will do", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#discussion_r844409600"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#pullrequestreview-934132098", "body": "looks good, aside from making contextLogger the default logger function.\nthanks for this matt."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#pullrequestreview-934138661", "body": ""}
{"comment": {"body": "Done -- the only downside is that the local `logger` variables now have to be called something else (`log`) so they don't conflict with the imported symbol, but I think it's probably fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#discussion_r844416556"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#pullrequestreview-934159390", "body": ""}
{"comment": {"body": "LOL. I guess this is for jest possibly. :)\r\nWeird that it behaves differenttly than TS.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#discussion_r844431697"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#pullrequestreview-934168017", "body": ""}
{"comment": {"body": "Yeah, each tool does aliasing/paths differently, which is annoying.  Jest uses regexes for this, which is what was causing the import confusions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/809#discussion_r844438034"}}
{"title": "Move to second release of postgres", "number": 81, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/81", "body": "They're behind in releasing this to maven, but I need IAM authentication and it's provided ehre."}
{"title": "Fix default", "number": 810, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/810"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/810#pullrequestreview-934142022", "body": ""}
{"title": "Move assets buckets to us west", "number": 811, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/811", "body": "Moved the static error assets bucket to StaticSites stack (still in us-east-1 because it needs integration with CF)\nMoved all client assets buckets to us-west-2\nEnabled transfer acceleration on all assets buckets \nDeployed it to Dev from my local machine and works fine."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/811#pullrequestreview-934191630", "body": ""}
{"title": "Video app belts and braces", "number": 812, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812", "body": "No code, just the project structure and the Agora, GRPC, OpenAPI, and codegen dependencies.\nAgora frameworks are linked directly, and the openAPI + GRPC dependencies are managed via XPM"}
{"comment": {"body": "Should we be concerned about dumping 100+MB of binaries into the repo?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812#issuecomment-1093108628"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812#pullrequestreview-934195441", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812#pullrequestreview-934216319", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812#pullrequestreview-935449732", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812#pullrequestreview-936953605", "body": "Nice! "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812#pullrequestreview-936962600", "body": ""}
{"comment": {"body": "@matthewjamesadam I'm going to explore other options for openapi generation. It's super heavy to bootstrap the macOS vm with java 11 just to run this gradle task. We can probably do better", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812#discussion_r846479242"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812#pullrequestreview-936969061", "body": ""}
{"comment": {"body": "\ud83d\udc4d sounds good", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/812#discussion_r846483550"}}
{"title": "Add utils for sourcemarks", "number": 813, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/813"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/813#pullrequestreview-934275836", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/813#pullrequestreview-934293539", "body": ""}
{"comment": {"body": "I'm curious, is there a reason to wrap the uuid library in this way?  Are git UUIDs different?  Should we be verifying that the UUID is v4 (or whatever git uses?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/813#discussion_r844527634"}}
{"title": "[BRESNINATOR ALERT] Move away from utils to libs", "number": 814, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/814", "body": "Move utils to libs (delete utils)\nLibs folder has a hierarchy now:\nSegment libs into more granular modules (more work to be done).\nFix all dependencies across projects.\nEventually, most of libs:common will be in more granular dependencies.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/814#pullrequestreview-934342398", "body": ""}
{"title": "Fix up naming of dependent modules in clients and libs folders", "number": 815, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/815", "body": "To avoid naming conflicts in the future with gradle modules.\nJust hit it today when we were splitting up libs...\nAs discussed with Richie..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/815#pullrequestreview-934358224", "body": "didn't really review, but I like the structure:\n```\nlibs/\n lib-common\n  build.gradle.kts\n  src\n      main\n      test\n lib-pringestion\n  build.gradle.kts\n  src\n      main\n lib-scm\n  build.gradle.kts\n  src\n      main\n      test\n lib-service\n  build.gradle.kts\n  src\n      main\n      test\n lib-video\n     build.gradle.kts\n     src\n         main\n         test\nclients/\n client-redis\n  build.gradle.kts\n  src\n      main\n client-scm\n  build.gradle.kts\n  src\n      main\n client-video\n     build.gradle.kts\n     src\n         main\n         test\n```\nthank you!"}
{"title": "CleanupLogging", "number": 816, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/816", "body": "Split out logging\nupdate"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/816#pullrequestreview-934454621", "body": ""}
{"comment": {"body": "agree \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/816#discussion_r844622974"}}
{"comment": {"body": "do we need this dep?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/816#discussion_r844625858"}}
{"title": "Add text utils to vscode SM", "number": 817, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/817", "body": "ported from KT"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/817#pullrequestreview-935312655", "body": ""}
{"comment": {"body": "FYI we've tended to use `undefined` for optional return values.  The difference between null and undefined is pretty much historical and not particularly useful, it's becoming more common for teams to move to just use undefined everywhere so there's no ambiguity...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/817#discussion_r845307478"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/817#pullrequestreview-935313500", "body": ""}
{"comment": {"body": "(I'm wondering if we should add a lint rule for this, but I'm not sure if it would cause havoc with our usage of third party libraries that do use null, or not...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/817#discussion_r845308086"}}
{"title": "Revert build fanning changes as it is way too expensive for now", "number": 818, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/818"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/818#pullrequestreview-935392411", "body": ""}
{"title": "Split up more modules", "number": 819, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/819"}
{"title": "[WIP] Introduce SourceMark models", "number": 82, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82", "body": "Builds on https://github.com/Chapter2Inc/codeswell/pull/71\nImplements https://www.notion.so/nextchaptersoftware/RFC-Model-User-Identities-51ca26f525774b33ae3a29ef94c96752\n\nTODO\n\n[ ] some rework to match Notion doc\n[ ] complete testing\n[ ] diagram"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-857438982", "body": ""}
{"comment": {"body": "An idle thought -- I'm assuming this represents a single \"annotation\" or bit of knowledge, and will logically including chats, media, etc?  If so, we may want to name this something more generic, like Annotation or Discussion?  There may be discussions that aren't strongly associated with a particular code point, they may be related to a file, or to no location in code at all -- this is even shown a bit that the SourceMark <-> SourceVector relationship is 1:0-Many...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r788203918"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-857546915", "body": ""}
{"comment": {"body": "Yes any knowledge \u2014 a Chat/Video/Audio/Walkthrough/Note/etc \u2014 can be associated with a SourceMark.\r\n\r\nIf we have less-granular annotations \u2014 anchored on file/module/repo instead of file+lineNum \u2014 then I believe the existing models can be extended to fit. For example, abstract FileAnnotations, ModuleAnnotations, and RepoAnnotations could be siblings of SourceVector.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r788279271"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-857547136", "body": ""}
{"comment": {"body": "I'll explore this a bit, and write up a proposal...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r788279450"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-857579131", "body": ""}
{"comment": {"body": "Can we call this class `SourceMarkState`? Or else include as inner class to `SourceMark` so we can clearly see what it refers to in code?\r\n\r\nex. `SourceMark.State`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r788304143"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-857580069", "body": ""}
{"comment": {"body": "Ah wait these are in a different package - now I'm confused because we have DbOrdinal references here. Is this class shared between types?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r788304882"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-857582288", "body": ""}
{"comment": {"body": "I just noticed that none of these database related calls are suspending. Is that an issue?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r788306564"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-857817195", "body": ""}
{"comment": {"body": "Ha. Good catch. These must all be synchronous right now.\r\n\r\nExposed has `newSuspendedTransaction { }` and `suspendedTransactionAsync { }` which we should be using at least in production code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r788390985"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-857819162", "body": ""}
{"comment": {"body": "https://github.com/JetBrains/Exposed/wiki/Transactions#working-with-coroutines\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r788394113"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-858346799", "body": ""}
{"comment": {"body": "Interesting. I'll review the implementation. The sample in that doc has at least one thing wrong (not using Dispatchers.IO). Bridging thread local management code to coroutines is hard and consumed 5x Golbeck brains to do right in Skywagon", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r788833443"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#pullrequestreview-858664753", "body": ""}
{"comment": {"body": "Made issue https://github.com/Chapter2Inc/codeswell/issues/89", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/82#discussion_r789053273"}}
{"title": "WIP: Add primary email property to Person API model", "number": 820, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/820", "body": "Add primaryEmail property onto Person in our API.  Person is only used in getPerson, which returns the currently-logged-in user.\nI added a PersonStore as part of this, following the patterns in the other stores"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/820#pullrequestreview-935430554", "body": ""}
{"comment": {"body": "FYI I'm passing the outer transaction in here -- I'm guessing this is the right thing to do but I'm not sure", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/820#discussion_r845393153"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/820#pullrequestreview-935450461", "body": ""}
{"title": "Add ability to archive threads", "number": 821, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821", "body": "\n\n\n\nNOTES:\n* Archived discussions are only listed on the web dashboard (i.e. users can archive threads on vscode but can only restore them from the dashboard)\n* Copy is placeholder/can be polished\n* Client-side filtering for now for archived threads, we should update the GET threads API to filter by archived / not archived but this should be okay for April\n* Archiving/restoring access is limited to the author of the thread/anchor message right now (similar to editing/deleting threads) -- all of this should eventually be replaced by some sort of role-based access control"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#pullrequestreview-935451754", "body": ""}
{"comment": {"body": "Are we expected to have this `/repos` channel? Right now it's breaking pusher ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#discussion_r845408241"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#pullrequestreview-935452855", "body": ""}
{"comment": {"body": "There's a deleteThread endpoint (that flips the isDeleted flag to true) but we don't expose the ability to do this on the client right now per discussions with Ben", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#discussion_r845409125"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#pullrequestreview-935454299", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#pullrequestreview-935454717", "body": ""}
{"comment": {"body": "hide message editor if thread is archived", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#discussion_r845410424"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#pullrequestreview-935455570", "body": ""}
{"comment": {"body": "need to backfill support for these features in the web extension ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#discussion_r845411063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#pullrequestreview-936703865", "body": ""}
{"comment": {"body": "Good question, maybe this didn't ever get done?  @davidkwlam @rasharab should the `/repos` pusher channel work?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#discussion_r846296947"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#pullrequestreview-936743849", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#pullrequestreview-936744573", "body": ""}
{"comment": {"body": "Again, might be worth factoring this out above?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#discussion_r846325799"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/821#pullrequestreview-936744903", "body": ""}
{"title": "Update web extension build for publish", "number": 822, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/822", "body": "Updates web extension build for publishing.\nFix bug from merging multiple conflicting PRs.\nRename and fix CI."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/822#pullrequestreview-935456318", "body": ""}
{"title": "I think job orders matter", "number": 823, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/823", "body": "Moving jobs to the end of list so they would catch failures? I am not sure if order matters but right now we are not getting alerts on failures. It's still worth a try."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/823#pullrequestreview-935505872", "body": ""}
{"title": "Use teamId from path for putSourcemarks", "number": 824, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/824"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/824#pullrequestreview-935516215", "body": ""}
{"title": "Add lib log", "number": 825, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/825", "body": "This includes the logback.xml that we need."}
{"title": "Try again", "number": 826, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/826"}
{"title": "First pass at implementing SourceMarkStore", "number": 827, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827", "body": "TODO in future PRs\n\n[ ] support storing SourcePoints for uncommitted file changes\n[ ] storing cache on disk so that we don't need to fetch all sourcemarks on start\n[x] support push updates so that we can fetch new sourcemarks (https://github.com/NextChapterSoftware/unblocked/pull/827#discussion_r845524947)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935584100", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935584904", "body": ""}
{"comment": {"body": "@richiebres note `repoId` here, not sure if that'll be a problem for the calculator", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#discussion_r845498738"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935599682", "body": ""}
{"comment": {"body": "All good. Repo id is correct", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#discussion_r845508336"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935603285", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935603877", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935605359", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935624357", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935624447", "body": ""}
{"comment": {"body": "Is the intention for this store that it will load all sourcemarks for a repo?  If so I think you might want to use a DataCacheStream, which will load the sourcemarks, keep them up to date, and will tell you when they've changed.  Take a look at UnreadStreamStore as an example.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#discussion_r845524947"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935631884", "body": ""}
{"comment": {"body": "Yep, we want to have all sourcemarks for repo so that the calculator can generate new source points as commits happen. Might be able to make that more efficient but that's the approach for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#discussion_r845529976"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935634441", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935635276", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935637453", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935641571", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935643703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935652946", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935654996", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935655813", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935658152", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/827#pullrequestreview-935745982", "body": ""}
{"title": "Rename action to services-build", "number": 828, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/828"}
{"title": "Refactor some utils", "number": 829, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/829"}
{"comment": {"body": "Are you thinking these are general-purpose enough to be reused elsewhere?  I was kind of wondering about what these would evolve into, some of them (like the FilePath / Hash) have third-party libraries we're already widely using?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/829#issuecomment-1092202673"}}
{"comment": {"body": "> Are you thinking these are general-purpose enough to be reused elsewhere? I was kind of wondering about what these would evolve into, some of them (like the FilePath / Hash) have third-party libraries we're already widely using?\r\n\r\nMy motivation for defining these is mainly type-safety, as it's unsafe to pass everything around as a string. So these will probably be fairly light classes. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/829#issuecomment-1092244480"}}
{"comment": {"body": "> My motivation for defining these is mainly type-safety, as it's unsafe to pass everything around as a string. So these will probably be fairly light classes.\r\n\r\nNo, I'm wondering about the file placement.  You moved these files out of `sourcemarks` into `utils`, which is more generic -- if these are only going to be used by the sourcemark diffing engine then they should probably belong there?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/829#issuecomment-1092253121"}}
{"comment": {"body": "> > My motivation for defining these is mainly type-safety, as it's unsafe to pass everything around as a string. So these will probably be fairly light classes.\r\n> \r\n> No, I'm wondering about the file placement. You moved these files out of `sourcemarks` into `utils`, which is more generic -- if these are only going to be used by the sourcemark diffing engine then they should probably belong there?\r\n\r\nok, moved back. I'm easy", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/829#issuecomment-1092267238"}}
{"comment": {"body": "Just to provide some more context -- if you want to wrap the strings in objects for the sourcemark engine that's fine, but using it more broadly carries some tradeoffs.  Objects always have reference semantics, which means you couldn't use a UUID or FilePath object as a Map or Set key (it will not work as you'd expect).  It means the standard `===` operator won't work, which means a lot of the standard algorithms won't do what you'd expect as they're largely equality-based.\r\n\r\nI do wish JS had value objects.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/829#issuecomment-1092268761"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/829#pullrequestreview-935674420", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/829#pullrequestreview-935679843", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/829#pullrequestreview-935741216", "body": ""}
{"title": "Cdk add iam eks roles and groups", "number": 83, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/83", "body": "Created a new CDK stack to manage IAM resources. Also added two IAM roles for managing access to EKS \nAdded RBAC role/rolebinding definitions for Kubernetes. These roles are used by IODC when mapping IAM users (via IAM roles) to access levels on Kube\nUpdated EKS cluster setup instructions \nAdded a lot of documentation around EKS-IAM auth management \n\nThese changes have been deployed to Dev. I have tested Kube access with limited deploy role."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/83#pullrequestreview-857453280", "body": ""}
{"title": "Fix issue with initial VCS update", "number": 830, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/830", "body": "Due to .startWith({ initialized: false, value: undefined }), the initial event for the stream may be incorrect if the stream's value was set before it's subscribed to.\nAlso fixed issue with unsubscribing in sync. Must be done async."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/830#pullrequestreview-935744188", "body": ""}
{"title": "Filter SourcePoints by commit in addition to filePath", "number": 831, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/831", "body": "Filter for SourcePoints for the current commit. \nAssumes each SourceMark has a source point for each commit since the source mark's creation...but maybe this is a bad assumption?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/831#pullrequestreview-936659138", "body": ""}
{"title": "Port Git utils from KT to TS", "number": 832, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832"}
{"comment": {"body": "> It feels a bit odd to have two Git APIs in our extension, but that's something we can resolve over time I think\r\n\r\nAgree. I considered merging at first, but easier conceptually to keep isolated for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#issuecomment-1092336187"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#pullrequestreview-935790780", "body": ""}
{"comment": {"body": "need to figure out how to test this, in follow up...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#discussion_r845648479"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#pullrequestreview-935797338", "body": ""}
{"comment": {"body": "Just FYI we will probably be removing this pretty soon (@jeffrey-ng ?) -- we now has a RepoStore that maps the workspace repositories that VSCode has to Unblocked repos (through our API).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#discussion_r845653851"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#pullrequestreview-935800581", "body": ""}
{"comment": {"body": "Would you mock the git behaviour, or try to build an environment where you could actually run git?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#discussion_r845656528"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#pullrequestreview-935800770", "body": "It feels a bit odd to have two Git APIs in our extension, but that's something we can resolve over time I think"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#pullrequestreview-935804438", "body": ""}
{"comment": {"body": "Good to know.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#discussion_r845659767"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#pullrequestreview-935805529", "body": ""}
{"comment": {"body": "Both. I think i\u2019ll inject a pluggable Git, which can be mocked for unit tests, swapped out with containerized Git versions for integration tests, and defaults to the existing Git version from VSCode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/832#discussion_r845660641"}}
{"title": "Add Assets Service Implementations", "number": 833, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/833", "body": "This pr does two things:\n1. Adds an ednpoint for creating an asset (and returning the asset metadata with its upload url)\n2. Adds an endpoint for getting an asset (and return the asset metadata with its download url)\nTesting:\n1. Validated via unit tests.\n2. Validated via some integration tests I have yet to check in."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/833#pullrequestreview-935846181", "body": "LGTM, ping me when ready for the green tick"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/833#pullrequestreview-936745724", "body": ""}
{"comment": {"body": "Just had a chat with Peter. This part needs a bit more love. \r\n1- Generating thumbnails for videos (we might have to tag those assets with video tag)\r\n2- Agora does multi-file uploads so we might have to bring up a special endpoint on cloudFront for streaming. That would also require assets service to be aware of Agora generated files that are part of one video session (generate multiple signed urls) \r\n\r\nFor Agora assets, if we create a special Cloudfront endpoint `e.g /stream` then we can take advantage of S3 -> Cloudfront native auth. The Lambda function would take care of token validation ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/833#discussion_r846326584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/833#pullrequestreview-936751302", "body": ""}
{"comment": {"body": "Agreed. Let's get this in.\r\nIt's a start.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/833#discussion_r846330370"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/833#pullrequestreview-936753299", "body": "Looks good to me."}
{"title": "Add CloudFront@Edge auth for customer assets", "number": 834, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834", "body": "Create auth-config.json to store pub keys for each environment. Lambda@edge does not support env-vars so this is recommended way for passing config.\nModified lambda function to load config, verify jwt signature, issuer and audience. It will then cross check requested teamID against claims\nAdded unit tests to help with local dev\n\nI know the auth function is not \"up to code\" with regards to its structure. The highest priority when writing this code was to ensure we don't accidentally allow access due to config failures. Hencethe nested structure code with authorization deeply nested in checks. We will iterate over this a few more times but for now it should do the job"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834#pullrequestreview-935880476", "body": ""}
{"comment": {"body": "Wish there was a way to share config with the Jwt implementation in kotlin ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834#discussion_r845719082"}}
{"comment": {"body": "I won't tell Dennis if you don't. Oh and hi Dennis \ud83e\udd23", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834#discussion_r845720221"}}
{"comment": {"body": "Nice work on these!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834#discussion_r845720853"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834#pullrequestreview-935886993", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834#pullrequestreview-935887254", "body": ""}
{"comment": {"body": "Unfortunately that would require a build script and a bunch more moving parts to work with CDK. CDK is kinda dumb and only knows how to zip the directory as an asset. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834#discussion_r845724400"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834#pullrequestreview-965586163", "body": ""}
{"comment": {"body": "test unblocked post\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/834#discussion_r867626227"}}
{"title": "Add SourceMarks push channel", "number": 835, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/835", "body": "Needed to allow the SourceMark service to grab only modified sourcemarks and sourcepoints"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/835#pullrequestreview-936805211", "body": "These push endpoints are starting to get a little heavy . I predict our upper bound on customers before we need to introduce real push is around 10"}
{"title": "TextEditor SourceMarks get repos from RepoManager", "number": 836, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/836"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/836#pullrequestreview-936699538", "body": ""}
{"comment": {"body": "I had written a test for something else and needed to debug the VSCode tests -- just including it alongside this PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/836#discussion_r846293760"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/836#pullrequestreview-938346612", "body": ""}
{"title": "Handler Bearer string in tokens", "number": 837, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/837", "body": "Changed the code to handle Bearer prefix in tokens\nCleanup comments and unused code\nUpdated test for positive case\nAdded test command to package.json"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/837#pullrequestreview-937052427", "body": ""}
{"title": "Handle repo resolution in web extension", "number": 838, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838", "body": "Adds support for proper repo resolution in web extension.\nGiven the current URL and teams from TeamStore, tries to resolve repos.\nEach url within the browser will have its own RepoStore instance.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#pullrequestreview-936789119", "body": ""}
{"comment": {"body": "Do we want to consider these kinds of things a stream error, or should it be a stream state ?  I'm not sure which model is better?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#discussion_r846357070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#pullrequestreview-936789673", "body": ""}
{"comment": {"body": "I do think the code using this will need to understand these scenarios though, because likely we'll want to display a nicer UI when a repo hasn't been onboarded yet.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#discussion_r846357430"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#pullrequestreview-936794144", "body": ""}
{"comment": {"body": "Yeah. In general I don't think I have a clear sense of the error vs state.\r\n\r\nThe biggest thing to keep in mind is the moment we send an error to the stream, it's done. Having an error state keeps the subscription stream open. Not sure if that is necessary in error cases though?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#discussion_r846360594"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#pullrequestreview-936797204", "body": ""}
{"comment": {"body": "Ah good point, maybe that's one of the deciding factors (whether the case should terminate the stream or not).  For this, it does terminate the stream, so it maybe makes sense.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#discussion_r846362723"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#pullrequestreview-936798696", "body": ""}
{"comment": {"body": "The downside is that the errors aren't typed, which means we can't express much about the scenario that happened, and render it in any interesting way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#discussion_r846363787"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#pullrequestreview-936815739", "body": ""}
{"comment": {"body": "Yup. I really don't like errors in JS for that reason... Was much nicer to handle in Swift.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#discussion_r846375630"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#pullrequestreview-938402629", "body": ""}
{"comment": {"body": "Does this cause a bunch of churn in the repo store?  ie, as we navigate around files are we re-requesting all the repos?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#discussion_r847602957"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#pullrequestreview-938403293", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#pullrequestreview-938429503", "body": ""}
{"comment": {"body": "This would cause churn if a tab navigated *out* of a repo context and back in.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/838#discussion_r847621678"}}
{"title": "SourceMarkStore uses a stream", "number": 839, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/839", "body": "Updates the SourceMarkStore to be created per repo, and makes the SourceMarkStore listen and fetch updated/new sourcemarks"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/839#pullrequestreview-936926143", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/839#pullrequestreview-936928152", "body": ""}
{"comment": {"body": "The idea is this would be called on shutdown", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/839#discussion_r846453262"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/839#pullrequestreview-936934883", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/839#pullrequestreview-936936265", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/839#pullrequestreview-936937535", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/839#pullrequestreview-936940517", "body": ""}
{"title": "Move to using Hocon for configuration", "number": 84, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84", "body": "The hocon model is by far the best because it's using lightbend, which allows for explicit loading of environment variables via config file.\nWe are moving away from implicitly using environment variables through hoplite mechanism.\nHopllite will handle reflection (which is nice and auto-populating fields)\nHoplite will invoke lightbend to load config files, which can reference environment variables explicilty."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-857450996", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-857451669", "body": ""}
{"comment": {"body": "lightbend is awesome.\r\nthis allows for explicit loading from env.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#discussion_r788213439"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-857466977", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-857550210", "body": "very happy about this. nice work Rashin!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-857553437", "body": ""}
{"comment": {"body": "awesome", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#discussion_r788284316"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-857816979", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-857817047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-858465693", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-858465821", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-858465966", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-858466075", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-858524536", "body": ""}
{"comment": {"body": "How does the hierarchy work?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#discussion_r788953154"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#pullrequestreview-858571389", "body": ""}
{"comment": {"body": "Definitions are specified first in.\r\nSo the environment definitions always override global definitions (if they exist).\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/84#discussion_r788986314"}}
{"title": "Add ability to configure token expirys", "number": 840, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/840", "body": "Want to be able to configure token expirys easily, especially when testing local environment."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/840#pullrequestreview-936981911", "body": ""}
{"title": "fix alb health probes", "number": 841, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/841", "body": "from here:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/841#pullrequestreview-936983888", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/841#pullrequestreview-936986477", "body": "sweet, thanks"}
{"title": "RevertGcChange", "number": 842, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/842", "body": "Revert \"Test ZGC (#789)\"\nRevert memory changes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/842#pullrequestreview-936983899", "body": ""}
{"title": "Soft delete sourcemarks", "number": 843, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/843", "body": "Since the extension is getting polling for updates, it needs to get back deleted sourcemarks too so that it can clear them out of the local cache."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/843#pullrequestreview-937019285", "body": "the beauty of making multi-stack changes in one PR. monorepo ftw."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/843#pullrequestreview-937021501", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/843#pullrequestreview-937060277", "body": ""}
{"title": "Basic Intercom integration", "number": 844, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844", "body": "Add 'Get Help' button on the bottom-right of the web dashboard, and the bottom-right of the VSCode discussion thread webview.\nAdded a bunch of Intercom infrastructure to make this happen, hidden behind an IntercomProvider (which boots up Intercom) and a useIntercom hook, which allows calling into the intercom API.\nNext PR will set the logged-in user information.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#pullrequestreview-937010540", "body": ""}
{"comment": {"body": "This diff is busted.  All I did was wrap the existing `BrowserRouter` in an `IntercomProvider`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#discussion_r846514192"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#pullrequestreview-937030945", "body": ""}
{"comment": {"body": "I get why we would want this but I feel like having the intercom bubble in the IDE is just gonna be super in the way ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#discussion_r846530005"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#pullrequestreview-937031698", "body": ""}
{"comment": {"body": "ah I guess it's just a link. still, I feel like people can be pretty sensitive with the surface area in the IDE. I wonder if we could (or should) collapse this into a menu item, i.e. under the Unblocked logo ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#discussion_r846530646"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#pullrequestreview-937033938", "body": ""}
{"comment": {"body": "Yes this is true -- we're going to investigate this next week, it might end up living up there.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#discussion_r846532415"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#pullrequestreview-937047020", "body": ""}
{"comment": {"body": "note: we should probably using a button styled as a link for these instances (not blocking for this PR, just an observation)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#discussion_r846543771"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#pullrequestreview-937047104", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#pullrequestreview-938410271", "body": ""}
{"comment": {"body": "\ud83d\udc4d yes -- once we figure out what the UI should actually be we'll do this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/844#discussion_r847608045"}}
{"title": "Flow control for sm calc", "number": 845, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/845", "body": "changes:\n1. moved 3 unused files to unused directory\n2. calculator\n3. array utils"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/845#pullrequestreview-937037854", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/845#pullrequestreview-937038784", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/845#pullrequestreview-937039290", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/845#pullrequestreview-937044720", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/845#pullrequestreview-937048283", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/845#pullrequestreview-937049615", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/845#pullrequestreview-937050821", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/845#pullrequestreview-937052749", "body": "Feel free to totally remove the unused files, we can resurrect them from git if we need to in the future."}
{"title": "AssetService should not have redis access", "number": 846, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/846"}
{"title": "Style extension discussion thread dialog", "number": 847, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/847", "body": "\nModal styling: \nButton styling: \nColors: "}
{"comment": {"body": "@matthewjamesadam For context, GH / Primer styles its buttons using the `btn` className.\r\n\r\nBecause our shared components use the shared button, we have no opportunity to inject the className into the component.\r\nOne option was to add a prop and pass this in but that could be pretty tedious / error prone.\r\n\r\nWe decided on pulling in the primer `btn` css instead and style the shared button the same way we do for other clients.\r\n\r\n(This pattern could apply to other components as well)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/847#issuecomment-1093435564"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/847#pullrequestreview-938406350", "body": ""}
{"comment": {"body": "So we're recreating the basic Primer styling here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/847#discussion_r847605222"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/847#pullrequestreview-938409259", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/847#pullrequestreview-938428740", "body": ""}
{"comment": {"body": "Yes - the main use case is the one Jeff described, re: in shared view components where we reference the shared button component -- these are missing the primer styling so we manually style them with the css taken directly from primer\r\n\r\n(in other words, using the web extension Button in the web extension code works fine without all of this code given the mapping code in the web-extension/Button.tsx file, but at worse we're just duplicating the code)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/847#discussion_r847621109"}}
{"title": "Upstream points from calculator", "number": 848, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/848", "body": "upstream points (see SourceMarkCalculator)\nsome data typing changes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/848#pullrequestreview-937038439", "body": ""}
{"comment": {"body": "We should probably remove this and `stores` if the calculator has a `SourceMarkStore`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/848#discussion_r846536142"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/848#pullrequestreview-937038709", "body": ""}
{"comment": {"body": "This should get the store from the calculator", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/848#discussion_r846536380"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/848#pullrequestreview-937061859", "body": ""}
{"title": "Multiple ingresses in same group CANNOT share group order", "number": 849, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/849", "body": "We were using the same group order as pusherservice which is not allowed.\nTEsted via manual helm deployment.\n\n"}
{"title": "Setup Auth on API requests", "number": 85, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/85", "body": "Whenever an API in the spec requires bearerAuth, codegen APIs will now be provided a token from the TokenProvider\nTokenProvider currently stores JWT within session storage based on OWASP recommendation: \nAdditional work:\n- Refresh tokens\nNOTE: Zustand can be used without React: \nCould potentially lead to stores for both web & vscode"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/85#pullrequestreview-858571468", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/85#pullrequestreview-858573221", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/85#pullrequestreview-858598360", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/85#pullrequestreview-858678211", "body": ""}
{"comment": {"body": "I'm guessing this creates two different stores -- that's maybe a bit unexpected?  Not sure if it matters though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/85#discussion_r789062882"}}
{"title": "See if we can eliminate the kms bullshit", "number": 850, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/850"}
{"title": "Try again", "number": 851, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/851"}
{"title": "Asset Service Role requires KMS access", "number": 852, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/852", "body": "dont ask me why it needs it, but it does.\nSue me\nTesting:\n1. Validated against dev (upload and downloads)\n2. Updated both environments as well using eksctl"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/852#pullrequestreview-937064797", "body": "Thanks for digging into this."}
{"title": "update", "number": 853, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/853"}
{"title": "Land action buttons in the toolbar", "number": 854, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854", "body": "There's a particular magical incantation to get SwiftUI to layout buttons in the toolbar in the way that we need it to... and this is it. \nIncluded some state stubs as placeholders for real data and state bindings. Also - on macOS 12 the non-prominent border button style has a bug where you have to hover over the button to drop the it into the correct unfocused state, so I had to write a style wrapper "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854#pullrequestreview-938271761", "body": ""}
{"comment": {"body": "We're defining two sizing frames, once here and once in the ContentView itself -- it's probably best to define our overall app sizing in one place?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854#discussion_r847513060"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854#pullrequestreview-938274982", "body": ""}
{"comment": {"body": "Do we know for sure that all these SF symbols are available on the OSes we'll need to support?  It's never been clear to me how we can ensure that we don't accidentally ship a breaking dependency with this kind of thing...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854#discussion_r847515376"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854#pullrequestreview-938275093", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854#pullrequestreview-938897814", "body": ""}
{"comment": {"body": "Will fix in the next one. That was me playing around and originally had that second frame further down in the hierarchy ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854#discussion_r847968232"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854#pullrequestreview-938898693", "body": ""}
{"comment": {"body": "I think the answer to this is to ship with these pulled out in icon assets. We're going to have to export some of the stuff Ben created anyways because not even FontAwesome has the right iconography ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/854#discussion_r847968831"}}
{"title": "Added logic for CloudFront  asset lookup", "number": 855, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855", "body": "Added necessary logic to lookup asset metadata and create a request to S3 for the asset using signed url provided by assets service\nAdded tests for asset lookup (using mocha)\nConverted CloudFront Auth tests to use mocha\nMinor touch ups and bug fixes here and there\n\nThis works locally (meaning tests pass). Next I'll work on ironing out the kinks when functions have been deployed"}
{"comment": {"body": "Pushed a build script to handle running lambda tests as well as installing prod (removing dev) dependencies so CDK could package each lambda under 1MB size limit", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#issuecomment-1094492707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#pullrequestreview-937307594", "body": "LGTM, minor nits"}
{"comment": {"body": "Makes me wonder if we should be slipping our own trace header info in here...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#discussion_r846813924"}}
{"comment": {"body": "Why not 1.3?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#discussion_r846814106"}}
{"comment": {"body": "Richie was suggesting we use `RegEx` where possible to get an up front eval on the expression for validity", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#discussion_r846814819"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#pullrequestreview-937322205", "body": ""}
{"comment": {"body": "What do you mean ? like a request trace ID ? I think CloudFront adds a request ID which can be used for tracing. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#discussion_r846832591"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#pullrequestreview-937323154", "body": ""}
{"comment": {"body": "(https://docs.aws.amazon.com/cloudfront/latest/APIReference/API_OriginSslProtocols.html)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#discussion_r846834181"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/855#pullrequestreview-937415883", "body": ""}
{"title": "Add local camera view", "number": 856, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856", "body": "Ugly hacks galore. This is just laying down some basics to figure out what we can do with SwiftUI"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-937470825", "body": ""}
{"comment": {"body": "Eventually we'll provide a help menu, but for now we won't. I'm sure there's a much better way to do this with AppKit...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r846950854"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-937471456", "body": ""}
{"comment": {"body": "The ID magically makes SwiftUI understand it's dealing with a different view and will animate the image change", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r846951323"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-937473849", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-937476332", "body": ""}
{"comment": {"body": "This feels like it goes against the grain of what SwiftUI wants to do, which is to potentially create new view instances when there are property changes. \r\n\r\nThis would mess with Agora though because it has no concept of view lifecycle, so the only alternative would be to \"rebind\" Agora to the new view every time it's created, which would create artifacts. Going with this for now unless anyone has a better idea...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r846954605"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-937476583", "body": ""}
{"comment": {"body": "Not a secret, but I will pull it out to a config abstraction later", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r846954764"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-937476870", "body": ""}
{"comment": {"body": "Abstracted this so we can mock it for testing purposes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r846954993"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938266309", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938267058", "body": ""}
{"comment": {"body": "Thank you!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847509875"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938267204", "body": ""}
{"comment": {"body": "Thank you!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847509975"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938268209", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938269259", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938269711", "body": ""}
{"comment": {"body": "How do you determine the uid? Is it just a random number? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847511696"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938270586", "body": ""}
{"comment": {"body": "Shoudl be commented. The benefit of annotations unblocked annotations here. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847512267"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938271302", "body": "I don't see a problem here."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938273261", "body": ""}
{"comment": {"body": "The UID is not random. It maps to a particular video stream (from another user for example), and so must match the uid of the incoming stream.\r\n\r\nBut in the case of the local display, we can just set it to 0. I can extrapolate that to a constant if it helps", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847514146"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938276253", "body": ""}
{"comment": {"body": "Should we set up the GitHub ignore files so these don't show up in the diffs?  They're basically noise, they're un-reviewable anyways...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847516297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938277718", "body": ""}
{"comment": {"body": "Yup can do", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847517343"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938280805", "body": ""}
{"comment": {"body": "I think the way we're doing this here is antithetical to how the SwiftUI app lifecycle works.  We can definitely go down this route (basically means using AppKit for top-level window/menu stuff, which would let this work in OS 10.15+).  The more modern way of doing this is with UIMenuBuilder, something like:\r\n\r\n```\r\n// Inside AppDelegate.\r\noverride func buildMenu(with builder: UIMenuBuilder) {\r\nsuper.buildMenu(with: builder)\r\n\u00a0\r\nbuilder.replaceChildren(ofMenu: .help) { _ in\r\n[]\r\n}\r\n}\r\n\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847519550"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938286819", "body": ""}
{"comment": {"body": "Is this supposed to replace the canvas view set up in `start` ?  I don't see how this ever gets associated with `self.backingView` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847522817"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938288089", "body": ""}
{"comment": {"body": "We should maybe (eventually) build all of this camera view logic into its own view so that it doesn't bleed into the top-level view like it does here...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847523751"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938301298", "body": ""}
{"comment": {"body": "Agora renders directly to the backingView's metal layer. It's totally opaque. Agora's API is super awkward this way: very C like in that it accepts structs of data that imply a resulting state", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847533138"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938302120", "body": ""}
{"comment": {"body": "I am _definitely_ going to do this :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847533666"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938353942", "body": ""}
{"comment": {"body": "I get that, it didn't look to me like we were ever detaching from `backingView`.  But I think I see how it works now: we are attaching `engine` to `backingStore` for the duration of this view, and providing the `engine` with the correct `AgoraRtcVideoCanvas`, which it renders into that view.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847569849"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938357707", "body": ""}
{"comment": {"body": "No I think what Agora wants and how NSViewRepresentable work are in alignment here:\r\n\r\n```\r\nThe system calls this method only once, when it creates your view for the first time. For all subsequent updates, the system calls the [updateNSView(_:context:)](https://developer.apple.com/documentation/swiftui/nsviewrepresentable/updatensview(_:context:)) method.\r\n```\r\n\r\nNSViews are not meant to be created and destroyed quickly, so this is fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847572577"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938357899", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938358680", "body": ""}
{"comment": {"body": "That's correct - when passing a new canvas Agora handles attach/detach. One thing I noticed is that it doesn't flush the metal layer after detach, so if you enable the camera again you will briefly see the last rendered camera frame while  the camera is booting up.\n\nAt any rate I will dig a little deeper into Agora to see if there's a way to give us a little more control", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847573219"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938432998", "body": ""}
{"comment": {"body": "UIKit only API though? Is there an equivalent for macOS? SwiftUI tries to deal with this via the `CommandBuilder` DSL but the best you can do is remove items from a menu. You can't remove the top level items that the system drops in unfortunately, thus this hack", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847624187"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#pullrequestreview-938461911", "body": ""}
{"comment": {"body": "Ahh you're right I thought I had used this before but it's only for catalyst apps.\r\n\r\nIn the SwiftUI hobby app I wrote I ended up using AppKit and building the menu by hand, because mixing-and-matching bits of predefined menus that the framework gives you, and your own bits, seemed less then ideal.\r\n\r\nhttps://github.com/matthewjamesadam/Scane/blob/403814a70060339470fc11e1a4373a5ad7e66866/Scane/ScaneApp.swift#L16\r\nhttps://github.com/matthewjamesadam/Scane/blob/main/Scane/MainMenu.swift", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/856#discussion_r847644253"}}
{"title": "Client logger keys should be distinguishable", "number": 857, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/857", "body": "Ensure keys like 'environment' do not conflict."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/857#pullrequestreview-938412992", "body": ""}
{"title": "Add ability to delete asset", "number": 858, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/858", "body": "For cleanup etc.."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/858#pullrequestreview-938470302", "body": ""}
{"title": "Some more utils", "number": 859, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/859", "body": "Splitting these changes out of a larger PR to make easier to review.\nThese will be used in a follow on PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/859#pullrequestreview-938506416", "body": ""}
{"comment": {"body": "bug fix", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/859#discussion_r847676639"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/859#pullrequestreview-938506559", "body": ""}
{"comment": {"body": "bug fix", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/859#discussion_r847676723"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/859#pullrequestreview-938517402", "body": ""}
{"title": "Add authenticated testing endpoint", "number": 86, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86", "body": "This change re-introduces auth templating for endpoints, and makes all endpoints opt-out."}
{"comment": {"body": "More of a meta question:\r\n\r\nDo we expect to continue going down this path of a single OpenAPI spec or towards multiple specs for specific domains?\r\naka Auth, Pusher, API, ... (similar to SW)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#issuecomment-1017705582"}}
{"comment": {"body": "> More of a meta question:\n> \n> \n> \n> Do we expect to continue going down this path of a single OpenAPI spec or towards multiple specs for specific domains?\n> \n> aka Auth, Pusher, API, ... (similar to SW)\n\nGood question. I don't see this as a particularly difficult problem to solve down the line though. As with everything else I think we need to go fast and see how it evolves before factoring", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#issuecomment-1017712611"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#pullrequestreview-857491113", "body": ""}
{"comment": {"body": "This means every path is authenticated by default", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#discussion_r788241453"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#pullrequestreview-857491178", "body": ""}
{"comment": {"body": "And this is how you opt-out", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#discussion_r788241517"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#pullrequestreview-858470340", "body": ""}
{"comment": {"body": "Should we comment how we opt out?\r\nOr let the user figure that out :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#discussion_r788915727"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#pullrequestreview-858479192", "body": ""}
{"comment": {"body": "Add this to top level group of tags at top of spec file.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#discussion_r788922042"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#pullrequestreview-858480457", "body": "Looks good."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#pullrequestreview-858508992", "body": ""}
{"comment": {"body": "You're right, I'll add a comment above paths", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#discussion_r788942058"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/86#pullrequestreview-858528490", "body": ""}
{"title": "Support for uncommitted changes and plumbing", "number": 860, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860", "body": "Plumbing the TextEditorSourceMarkManager to the SourceMarkProvider\nIntroduce RepoResolver which wraps RepoStore and providing a way to lookup repo information quickly\nIntroduce getLocationForSourceMark\nSupport for uncommitted repo changes"}
{"comment": {"body": "> SourceMarkCalculator probably needs tests at some point, but happy to have these changes merged in so that we can play with it before then (assuming this doesn't crash the extension :) )\r\n\r\nIs probably _will_ crash or slow down the extension, so before I merge I'll use the `NullSourceMarkProvider`. It'll take a day or two for me to make stable.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938534334", "body": "SourceMarkCalculator probably needs tests at some point, but happy to have these changes merged in so that we can play with it before then (assuming this doesn't crash the extension :) )"}
{"comment": {"body": "Where is this going to be called?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847696232"}}
{"comment": {"body": "There's a SourceMarkStore.destroy function that we can call when this `SourceMarkCalculator` is no longer needed. I guess its not necessary if we only ever throw out a `SourceMarkCalculator` on VS Code shutdown.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847703987"}}
{"comment": {"body": "But if we do call SourceMarkManager.removeCalculator while the extension is still up, we should ultimately SourceMarkStore.destroy to stop getting updates.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847704799"}}
{"comment": {"body": "We're ok if this hammers the service on first load for a large repo? Probably fine for now, we can optimise as we iterate.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847708411"}}
{"comment": {"body": "(This'll only be an issue for large repos with many PR comments).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847709626"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938547022", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938609209", "body": ""}
{"comment": {"body": "Agree.\r\n\r\nRight now this will make _N_ PUT requests, where _N_ is number of source marks. For our Unblocked repo right now there are ~600 of these, but most older repos would have 10X that.\r\n\r\nI think we can better leverage your batch PUT API by grouping these into batches (of say 50?) source marks to reduce the network overhead.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847749765"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938612955", "body": ""}
{"comment": {"body": "Ok, I'll add a `SourceMarkCalculator.destroy` method that calls `SourceMarkStore.destroy`. Not 100% clear to me what would call `SourceMarkCalculator.destroy` though; I guess onFolderClose or onWorkspaceClose. @matthewjamesadam you have thoughts on when best to tear down?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847752439"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938615431", "body": ""}
{"comment": {"body": "Actually, I was intending this top-level `recalculate()` method to be called at workspace/folder open, just like you were mentioning in scrum.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847754066"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938617883", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938658554", "body": ""}
{"comment": {"body": "Are the extra braces from a copy and paste?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847781538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938700951", "body": ""}
{"comment": {"body": "Ah gotcha, I was confused by the `private`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847812693"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938733476", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#pullrequestreview-938734103", "body": ""}
{"comment": {"body": "When a workspace shuts down, the entire extension is unloaded (extensions are per-workspace) -- we could probably hook into that ad shut down just to be nice, but I'm not sure it would make much of a difference.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/860#discussion_r847839183"}}
{"title": "Update fontawesome lib with thumbtack fix", "number": 861, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/861", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/861#pullrequestreview-938550715", "body": ""}
{"title": "Stupid Rashin should not be using customer names for s3 keys", "number": 862, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/862", "body": "WE already correctly name it  for presign url when we specify the contentDisposition. Not sure why I ever did this. :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/862#pullrequestreview-938621460", "body": ""}
{"title": "Send current user information to Intercom", "number": 863, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863", "body": "Send current user's name, email, ID, and avatar to Intercom.\nI implemented this by adding a person prop to the IntercomProvider.  In the web we populate this directly from the AuthStore, in VSCode we pipe this through the webview properties, add it to the webview context, and then pipe it into the IntercomProvider from there.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#pullrequestreview-938704596", "body": ""}
{"comment": {"body": "This is a replacement for `WebviewStateHandlers` below)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#discussion_r847815661"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#pullrequestreview-940260072", "body": ""}
{"comment": {"body": "Do we need to log if intercom is undefined?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#discussion_r848955194"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#pullrequestreview-940260747", "body": ""}
{"comment": {"body": "Not a huge fan of accessing authstore here in a generic helper but don't have a better suggestion atm. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#discussion_r848955745"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#pullrequestreview-940260817", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#pullrequestreview-940266473", "body": ""}
{"comment": {"body": "Hmm good question.  I actually think that it's not possible for this to ever happen if LoadIntercom has been called, but there's no harm in logging.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#discussion_r848960807"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#pullrequestreview-940267453", "body": ""}
{"comment": {"body": "The only other option I could think of was making this a regular value prop, so the calling code would have to add this, but I thought it was useful to add for anyone using a webview renderer.  Maybe I was wrong?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/863#discussion_r848961749"}}
{"title": "Save PR comment ID on MessageModel", "number": 864, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/864", "body": "We'll need this ID to keep messages synced with the GH comment\nCreate \nUpdate \nDelete "}
{"comment": {"body": "weird that we already have this on the Thread as `commentId`. Should we consolidate?\r\n```kotlin\r\n    /**\r\n     * Fields for comment threads ingested from pull requests\r\n     * */\r\n    val prNumber = integer(\"prNumber\").nullable()\r\n    val prTitle = text(\"prTitle\").nullable()\r\n    val prHtmlUrl = text(\"prHtmlUrl\").nullable()\r\n    val commentId = text(\"commentId\").nullable()\r\n    val commentHtmlUrl = text(\"commentHtmlUrl\").nullable()\r\n    val archivedAt = timestamp(\"archivedAt\").nullable()\r\n    val isDeleted = bool(\"isDeleted\").clientDefault { false }.nullable()\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/864#issuecomment-1095700527"}}
{"comment": {"body": "@richiebres yeah `ThreadModel.commentId` is the ID for the first message so there is data duplication. I think we can get rid of that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/864#issuecomment-1095702693"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/864#pullrequestreview-940000867", "body": ""}
{"comment": {"body": "ah, we don't have this yet", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/864#discussion_r848765028"}}
{"title": "Persist web extension web worker", "number": 865, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865", "body": "Web extension MV3 has moved background scripts to service workers. Chromium has a built in 5 minute lifespan for a service worker causing our data, polling, streams, etc... to go away after 5 minutes.\nControversial issue that has not been addressed in a couple years: \nUsing the following workaround to keep service worker alive. Requires some additional permissions in manifest.json.\n"}
{"comment": {"body": "Have had a tab open for the last 10 minutes and it seems to be okay?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#issuecomment-1095706347"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#pullrequestreview-938728270", "body": ""}
{"comment": {"body": "Any idea what the end effect of this is?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#discussion_r847834627"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#pullrequestreview-938730601", "body": ""}
{"comment": {"body": "woo! \ud83e\udd2a ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#discussion_r847836417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#pullrequestreview-938731264", "body": "Ship it before anyone looks too close "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#pullrequestreview-939789456", "body": ""}
{"comment": {"body": "host permissions grant the extension extra privileges for certain hosts. Earlier, we were asking for only github & getUnblocked since we wanted to make fetch requests without CORS restrictions. \r\n\r\nWe now need all_urls so that we can run `executeScript` to keep service worker alive.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#discussion_r848602718"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#pullrequestreview-939790008", "body": ""}
{"comment": {"body": "https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions#host_permissions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/865#discussion_r848602949"}}
{"title": "Run PR ingestion on all repos", "number": 866, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/866", "body": "We no longer need to hardcode the repo in our config files."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/866#pullrequestreview-938794556", "body": ""}
{"title": "Add ability to debug storybook", "number": 867, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/867"}
{"title": "Use logger instead of console", "number": 868, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/868", "body": "Replace almost all console usages with logger.\nI think the logger usage should be something like:\nlog.debug('message') -- simply log a single string\nlog.error('message', e) -- log an exception e, we should generally include a message too indicating more about the message.  The logs will include the exception details and call stack, if whoever threw the exception used new Error().\nlog.warn('message', { collection, of, things, to log}) -- log with a collection of associated objects, useful if you have a simple object that you want to log everything for\nlog.error('message', e, { collection, of, things, to, log}) -- log an exception e with additional data -- the additional collection will be merged into the exception data for logging purposes.\nFuture PR will clean up the last couple instances and (hopefully) add a linter rule."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/868#pullrequestreview-940125444", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/868#pullrequestreview-940151867", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/868#pullrequestreview-940160145", "body": "Besides Richie's requests, this makes me happy!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/868#pullrequestreview-940160216", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/868#pullrequestreview-*********", "body": ""}
{"title": "Add identity to client logs", "number": 869, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/869", "body": "/logs endpoint is now authenticated."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/869#pullrequestreview-*********", "body": ""}
{"title": "added a role to grant deploybot ECR power user access", "number": 87, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/87", "body": "Added new root account Id config param to secOps config class\nCreated IAM role for cross account access to ECR\nUpdated sec-ops account json to include root account ID\nAll changes have been deployed to dev and I have validated deploybot user's access to ECR.\n\nDeploybot arn: arn:aws:iam::************:role/EcrDeployerRole"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/87#pullrequestreview-*********", "body": ""}
{"title": "Add basic Image Drag & Drop", "number": 870, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/870", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/870#pullrequestreview-*********", "body": ""}
{"comment": {"body": "All blocks need to have a text node, was leading to render errors by slate.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/870#discussion_r848931106"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/870#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/870#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/870#pullrequestreview-940260037", "body": ""}
{"title": "Re-resolve SourceMarks when resolved repos changes", "number": 871, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/871", "body": "Whenever the resolved workspace repos change, re-fetch and render sourcemarks.\nThis still doesn't work as expected.  On startup, even with a resolved repo, the sourcemark engine returns an empty set of sourcemarks (attn @richiebres ) but with this PR it should at least be easier to test."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/871#pullrequestreview-940233281", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/871#pullrequestreview-940254720", "body": ""}
{"title": "Update overlay stream logic to support partials", "number": 872, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/872", "body": "Edits and Deletes should not need to require the entire model to trigger overlay logic, tweak the overlay logic to support partials to update \nFix some styling issues"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/872#pullrequestreview-940255117", "body": ""}
{"comment": {"body": "Can we add a test or condition here for partial overlay update?  You could maybe even just change one of these two overlay test conditions to use a partial update instead of a complete one.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/872#discussion_r848951002"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/872#pullrequestreview-940257192", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/872#pullrequestreview-940260487", "body": ""}
{"comment": {"body": "Added to line 116.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/872#discussion_r848955543"}}
{"title": "Add ability to add overrides for s3 presigned urls", "number": 873, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/873", "body": "This pr adds the ability to provide a cloudfront id as an additional parameters when generated presigned urls.\ni.e. \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/873#pullrequestreview-940271115", "body": ""}
{"title": "[Extension] Add message deleting, thread archiving/restoring", "number": 874, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/874", "body": "Feature parity with web/vscode\nAdded logic to support modal stacking and click events (i.e. click outside should only close the top-most stacked modal), add helper to close all modals"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/874#pullrequestreview-941669998", "body": ""}
{"title": "Install VSCode commands to toggle SM engine and clear sourcepoints", "number": 875, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/875", "body": "You can turn source marks on and off now in VSCode using commands, rather then editing the code."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/875#pullrequestreview-940352849", "body": ""}
{"title": "changing header name", "number": 876, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/876"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/876#pullrequestreview-940294818", "body": ""}
{"title": "Add amazon headers", "number": 877, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/877"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/877#pullrequestreview-940333904", "body": ""}
{"title": "removing the extra header", "number": 878, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/878"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/878#pullrequestreview-940365120", "body": ""}
{"title": "Bug fixes", "number": 879, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/879", "body": "fix git commit ordering bugs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/879#pullrequestreview-940497130", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/879#pullrequestreview-941233157", "body": ""}
{"comment": {"body": "You probably know this, but this means the processing for these items will be done serially, not in parallel -- I'm not sure if that matters or not, or if it will negatively affect performance.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/879#discussion_r849664876"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/879#pullrequestreview-941237030", "body": ""}
{"comment": {"body": "intentional. running full recalculation is melting the IDE, causing the \"inferior\" IDE to hang. I'm doing this to try to workaround for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/879#discussion_r849667741"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/879#pullrequestreview-941245062", "body": ""}
{"comment": {"body": "At some point we could look into whether running this as a web worker (https://developer.mozilla.org/en-US/docs/Web/API/Web_Workers_API/Using_web_workers) would make sense.  Web workers can run on a separate OS thread (or process), which means that it could run on a separate CPU from the rest of the extension.  The downside is that all communication with the web worker is done through a message passing API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/879#discussion_r849673605"}}
{"title": "Add support for fuzzy search", "number": 88, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88", "body": "This PR adds a couple of additions to support fuzzy searching to enable returning results even when a query is misspelled. \nThe next steps are to implement the models. As we build those, we can drop in this logic and enable search.\nStart here to review\n\nAnother application of fuzzy search is providing search suggestions as the user types into the search bar. The client would hit the backend on every keystroke (perhaps debouncing) and get back a list of fuzzy-matched suggested search terms that we know will return results. This would require maintaining a table of suggested terms, but that table wouldn't need to always be 100% up to date. This is described in more detail in section F.31.5 of the pg_trgm docs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#pullrequestreview-857737849", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#pullrequestreview-857742600", "body": ""}
{"comment": {"body": "Once `pg_trgm` is installed, we can create a trigram GIN index to support fast fuzzy string search. Creating a GIN index with `gin_trgm_ops` is also not supported by Exposed, hence the raw SQL again.\r\n\r\nNext: [Run a fuzzy search](https://github.com/Chapter2Inc/codeswell/pull/88/files#r788362152)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#discussion_r788361165"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#pullrequestreview-857746204", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#pullrequestreview-857748726", "body": ""}
{"comment": {"body": "Finally, we can combine both full text search and fuzzy search. Full text search adds support for quotes and/or boolean operators, and fuzzy gives us matches for misspelled queries.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#discussion_r788362946"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#pullrequestreview-857775342", "body": ""}
{"comment": {"body": "(Note: because this is being created manually, we'll need to manually call this at some point after the tables have been created, maybe in the `Database.init()` function. Gross, I know.)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#discussion_r788371223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#pullrequestreview-858469352", "body": ""}
{"comment": {"body": "cool", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#discussion_r788986824"}}
{"comment": {"body": "You may need to quote the column. PG does not accept names with uppercase letters without quotes. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#discussion_r788988659"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#pullrequestreview-858578050", "body": ""}
{"comment": {"body": "Just for more context here: ideally the `org.jetbrains.exposed.sql.Index` class would support the part where we pass `gin_trgm_ops` to create the `USING GIN ($columnName gin_trgm_ops)` part of this index. Unfortunately, `Index` is a data class and can't be extended (at least from what I can tell).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#discussion_r788991143"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#pullrequestreview-858599269", "body": ""}
{"comment": {"body": "`QueryBuilder.append` actually does the escaping/quoting (and converting to the proper case, etc.)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/88#discussion_r789005938"}}
{"title": "adding header param back.", "number": 880, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/880", "body": "Seems CloudFront still needs the value to be signed. I am getting unsigned payload errors so if this doesn't fix it I'll abandon this approach and fallback to generating signed urls in Lambda."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/880#pullrequestreview-941220133", "body": ""}
{"title": "Add exception errors for apiservice", "number": 881, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/881"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/881#pullrequestreview-941371056", "body": ""}
{"title": "[RFC] Run recalculator when source marks store has been initialized", "number": 882, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/882", "body": "Subscribes to store initialization\nOn store initialization the calculator runs a full recalculation,\n  and also cancels the subscription. We only want to run this once\n  as all subsequent calculations should be incremental.\nAll public calculator APIs block on initialization."}
{"comment": {"body": "GitHub is confused. This was not actually merged, see #884 instead.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/882#issuecomment-1098352467"}}
{"title": "TextEditorSourceMarkManager indexes by file path", "number": 883, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/883", "body": "Index the TextEditorSourceMark instances by file path, not by document.  I thought the documents were stable, but they can be removed, so the file path is a better index.  It also matches how the rest of our SourceMark code works anyways."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/883#pullrequestreview-941376162", "body": "cool. So this should fix duplicate editors not showing the insight bubbles."}
{"comment": {"body": "`.first` can be dangerous. might be worth sanity checking that each editor is operating on the same file?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/883#discussion_r849768440"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/883#pullrequestreview-941385691", "body": ""}
{"comment": {"body": "> cool. So this should fix duplicate editors not showing the insight bubbles.\r\n\r\nIt should, but I haven't tested it yet because I can't get the engine to return sourcemarks right now...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/883#discussion_r849775599"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/883#pullrequestreview-941397618", "body": ""}
{"comment": {"body": "> `.first` can be dangerous. might be worth sanity checking that each editor is operating on the same file?\r\n\r\nHmm right now that shouldn't happen (the parent class will always ensure this) -- I could add a check in the `addEditor` method that could enforce this.  Alternatively, we could just generate the decorators separately on each editor.  This will only be slightly less efficient in the case where you have a split window editing the same file in multiple editors, and even then I doubt it will make a substantial difference.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/883#discussion_r849783787"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/883#pullrequestreview-941401688", "body": ""}
{"comment": {"body": "> I could add a check in the addTextEditor method that could enforce this\r\n\r\nyup, that works for me", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/883#discussion_r849786692"}}
{"title": "Run recalculator when source marks store has been initialized", "number": 884, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/884", "body": "Subscribes to store initialization.\nOn store initialization the calculator runs a full recalculation,\n  and also cancels the subscription. We only want to run this once\n  as all subsequent calculations should be incremental.\nAll public calculator APIs block on initialization.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/884#pullrequestreview-941497286", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/884#pullrequestreview-941546842", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/884#pullrequestreview-941579664", "body": ""}
{"title": "Temporarily disable PR ingestion", "number": 885, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/885", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/885#pullrequestreview-941386084", "body": ""}
{"title": "Use setting for enabling SM engine", "number": 886, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/886", "body": "Shows up in Code -> Settings\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/886#pullrequestreview-941389310", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/886#pullrequestreview-941389820", "body": ""}
{"comment": {"body": "This monitors settings changes, so it will pick up when you check the option on/off", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/886#discussion_r849778469"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/886#pullrequestreview-941393235", "body": "thanks"}
{"title": "[Extension] Sidebar styling", "number": 887, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887", "body": "\n\n\n\nAdd hover to open and pinning ability, save to local storage on change to persist state\nIs missing repo name (Jeff to add later) \nUsing temporary logo file \nNOTE: Some views/components on GH look like they're styled inline which will possibly break our layout logic"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#pullrequestreview-942474839", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#pullrequestreview-942477375", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#pullrequestreview-942503032", "body": ""}
{"comment": {"body": "Is this directly using the localStorage API?  Should we abstract this behind a class that manages storing settings?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#discussion_r850597461"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#pullrequestreview-942505392", "body": ""}
{"comment": {"body": "This doesn't feel too great, modifying the parent document from a child react component.  I'm not sure that I have a better concrete idea, do we need to modify the document iself, or can we just modify the sidebar here by setting a padding in the component styling?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#discussion_r850599186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#pullrequestreview-942507000", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#pullrequestreview-942639916", "body": ""}
{"comment": {"body": "<img width=\"542\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/163451713-1b11e052-c8f5-4729-860f-10a9bdd46b74.png\">\r\n<img width=\"848\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/163451664-b7a18d1a-ede1-4fce-8106-480f3cf51002.png\">\r\n\r\nIt looks like we want the sidebar to overlay the document when it's opened on hover but for the rest of the DOM to move over when the sidebar is pinned open, hence needing to modify the DOM given a sidebar user event\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#discussion_r850690539"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#pullrequestreview-942651671", "body": ""}
{"comment": {"body": "We could? I guess I'm not sure how much more that gives us vs using the localStorage API directly. like as of now the functionality is exactly the same..?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#discussion_r850698923"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#pullrequestreview-942655158", "body": ""}
{"comment": {"body": "It's more to abstract and centralize the settings keys then the localstore API itself.  I don't feel strongly about this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/887#discussion_r850701501"}}
{"title": "Source mark actions on file save and edit", "number": 888, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/888", "body": "Handle file save event, and re-request sourcemarks on the relevant file.  Handle debounced file editing events, but for now take no action."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/888#pullrequestreview-941574942", "body": ""}
{"comment": {"body": "I could've used a third-party thing for this but it seemed like overkill...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/888#discussion_r849912886"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/888#pullrequestreview-941597890", "body": "nice"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/888#pullrequestreview-941605662", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/888#pullrequestreview-941606722", "body": ""}
{"title": "fix asset lookup and retrieval from s3", "number": 889, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889", "body": "Fixed the logic in lookup lambda function to lookup assets via assets service\nChanged how we handle paths and cache keys in Lookup lambda. This caused us a bit of a pain but turns out\n  Cloudfront does some weird stuff with request.uri and request.origin.custom.path fields\nChanged Auth lambda to remove /assets from uris before forwarding requests to lookup (so we get cache hits)\nHad to change a lot of unit tests for both Auth and Lookup lambdas\nAdded a very simple Upload/Download integration test (not hooked up into CI/CD yet) so we can make sure everything is working in dev before pushing changes to prod.\nEnabled caching on /assets\nAdded an origin request policy to CloudFront so we can pass query strings to Lookup lambda\nHad to modify auth lambda to use query strings to pass a couple of pieces of info to Lookup lambda. The reason for this is that querystrings are not used as part of cache key so we won't get a miss everytime we forward an auth token to backend\nA whole bunch of hacks (cdk exports) to break some CDK stack dependencies.\n\nThere's still some cleanup work to be done but this is fully functional in Dev. I'll be doing those cleanups as well as enabling integration tests (I'll add one for each asset type) in my next PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#pullrequestreview-941621935", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#pullrequestreview-941623156", "body": ""}
{"comment": {"body": "Should we do any validation here against a schema or something? This will break if the responseBody does not have a downloadUrl.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#discussion_r849949349"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#pullrequestreview-941633290", "body": ""}
{"comment": {"body": "I hope this token is not useful outside of this test should it be stolen :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#discussion_r849957467"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#pullrequestreview-941661267", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#pullrequestreview-941772762", "body": ""}
{"comment": {"body": "That's a dummy test token. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#discussion_r850070401"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#pullrequestreview-941773035", "body": ""}
{"comment": {"body": "It's in a try-catch block so if the url is missing we should get a 500 error.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#discussion_r850070607"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/889#pullrequestreview-941777899", "body": ""}
{"title": "Web extension - Basic discussion creation", "number": 890, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890", "body": "Creates a thread discussion given highlighted code in GH.\nAlso setting up some work for source mark resolution (Partially used in this PR but lot of setup for next PR) \n"}
{"comment": {"body": "There's a wee bit of crossover with https://github.com/NextChapterSoftware/unblocked/pull/874 with some of the modal work, would like to get 874 in first if possible", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#issuecomment-1098572801"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-941626362", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-941627396", "body": ""}
{"comment": {"body": "Class currently *not* used but is setting up for next PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r849952706"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-941673890", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-941674412", "body": ""}
{"comment": {"body": "Would this still be necessary given the ^above suggestion? Slight concern that changing this will break some existing styles", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r849991275"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-941675256", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942485008", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942499805", "body": ""}
{"comment": {"body": "I guess the point is we *don't* actually want this 'button' class for web extension.\r\n\r\nIt doesn't look like buttonAs is actually used in places outside of Web extension as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r850595131"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942502203", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942536051", "body": ""}
{"comment": {"body": "don't we? it looks like most of the styling is done on the variant class and not this one? maybe I'm missing something though?\r\n\r\nand the prop isn't used in a ton of places yet because I just added it but I've already found several places where it could be used (i.e. Matt's intercom link)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r850621869"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942546595", "body": ""}
{"comment": {"body": "Updated. There's an ignoreStyling option that removes all these classes if true. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r850629705"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942560854", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942562086", "body": ""}
{"comment": {"body": "ah yup makes sense, just made a similar comment below\r\n\r\nEdit: still think that we can keep the `.button` class then ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r850641035"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942574555", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942577819", "body": ""}
{"comment": {"body": "could probably abstract this to a `DialogFooter` reusable component with the styling (i.e. so that the footer doesn't scroll with the content) \r\n\r\nhttps://primer.style/css/components/box-overlay\r\n(see the div with the `Box--footer` class)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r850651816"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942580067", "body": ""}
{"comment": {"body": "I'd rather keep it off if we're un styling everything. The classname itself doesn't provide much value.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r850653469"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942589522", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942601546", "body": ""}
