{"comment": {"body": "not in this commit but I see this magic numbers:\n\n`const q31_t min_data_power = q31_mul(float_to_q31(0.6f), abs_data_mean);const q31_t max_noise_power = q31_mul(float_to_q31(0.4f), abs_data_mean);`\n\nlets talk tomorrow I want to understand what are those and if there are assumptions about the input data for those magic numbers to be correct", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72388718"}}
{"comment": {"body": "I think the algorithm should also work with candidate length even as low as 100.  \nThe falling transients are pretty short in most of the phones we studied.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72401981"}}
{"comment": {"body": "What does `(void)amp_len` actually do?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72402206"}}
{"comment": {"body": "ARM M0\\+ does not have hardware implementation of CTZ and CLZ, so I\u2019m guessing the compiler insert some software implementation here.\n\nI think it would be better if we write and use an explicit code.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72403007"}}
{"comment": {"body": "Overall very good and important feature implementation ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72439027"}}
{"comment": {"body": "Taken from python implementation.\n\nSure, we can overview this together", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72440807"}}
{"comment": {"body": "yea, you\u2019re right. CLZ is only from M3.\n\nIt\u2019s still faster than the while loop previously implemented. Is there still any particular reason to implement this explicitly?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72442881"}}
{"comment": {"body": "that\u2019s the search window length after the packet end. Dima and I are still not sure about it, but 400 works for now.\n\nI think that we need to further investigate packets in order to conclude the optimal value.\n\nAny thoughts?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72443333"}}
{"comment": {"body": "I don\u2019t know. We need to perform this test at some point. fast\\_carg can\u2019t work on \\(0, 0\\). Maybe return an error or some magic number?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72443651"}}
{"comment": {"body": "removes unused variable warning", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72443685"}}
{"comment": {"body": "we definitely need to increase the number of packets test. probably though python interface would be best", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72444051"}}
{"comment": {"body": "Was waiting for @dimabl for a new packet", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72444083"}}
{"comment": {"body": "Thanks", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72444119"}}
{"comment": {"body": "I would like to have the same computational code running across platforms/compilers", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72448135"}}
{"comment": {"body": "But it\u2019s not critical at this point.  \nMaybe just added a TODO comment to the code.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72448195"}}
{"comment": {"body": "We\u2019ll discuss about it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72454464"}}
{"title": "Created a stripped down copy of the Android app for physec", "number": 390, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/390", "body": ""}
{"title": "add finalize to regression tests", "number": 391, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/391", "body": ""}
{"title": "Jenkins slave will now automatically restart", "number": 392, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/392", "body": ""}
{"comment": {"body": "Why not just create a node\\_info file for the hardware slave and one for the builder slave?\n\nThen we won\u2019t have to check and change the file each time.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/392/_/diff#comment-95533551"}}
{"comment": {"body": "Great idea do that", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/392/_/diff#comment-95533668"}}
{"title": "Added support to print only some of the IQ samples instead of all of them", "number": 393, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/393", "body": ""}
{"title": "Add version to FW", "number": 394, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/394", "body": ""}
{"title": "Add filter for AGC to the capturing FW", "number": 395, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/395", "body": ""}
{"title": "Feature/BIS-3316 flash st with cyclone", "number": 396, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396", "body": "First step of ST CI: flashing with cyclone via docker\n\nRemove unsused stuff\nUpdate hardware builder docker to debian-based\nAdd PEMicro GDB server installation to hardware docker\nAdd flashing script for ST"}
{"comment": {"body": "Please `./trigger_build.sh` on this branch so we see that the full automatic hardware tests still work after the migration from Alpine", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396/_/diff#comment-97002827"}}
{"comment": {"body": "We're no longer using the C pre-processor, so just change this to a regular Dockerfile. Use `#` comments instead of `//` and change the makefile so it doesn't call cpp and remove the `.pp` intermediates", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396/_/diff#comment-97002861"}}
{"comment": {"body": "Approved except for `docker/Dockerfile.hardware` comments, good job", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396/_/diff#comment-97003120"}}
{"comment": {"body": "Fixed in e3048e915a1b377b0c4c233db3c88f3c5c6c910a", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396/_/diff#comment-97192129"}}
{"comment": {"body": "I\u2019ll appreciate an approval", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396/_/diff#comment-97596839"}}
{"comment": {"body": "Approved but do we really want to merge before running the hardware tests?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396/_/diff#comment-97600393"}}
{"comment": {"body": "We\u2019ll have @guyklevl handle this :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396/_/diff#comment-97643510"}}
{"comment": {"body": ":worried: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396/_/diff#comment-97851661"}}
{"comment": {"body": "It\u2019s passing now", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/396/_/diff#comment-97853957"}}
{"title": "Improve monitor_v2.py a bit - create main; get pickle file from user; fix path to be relative", "number": 397, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/397", "body": "Improve monitor_v2.py a bit for ease of use:\ncreate main; \nget pickle file from user; \nfix path to be relative;\nadd printouts so user can find the output png file"}
{"title": "Physec agent will now hide IQ crc in noise before packet so modifications can be detected", "number": 398, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/398", "body": ""}
{"title": "Q always even fix", "number": 399, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/399", "body": ""}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/1968797861-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/399/_/diff#comment-97115839"}}
{"comment": {"body": "Beautiful work! I\u2019m impressed!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/399/_/diff#comment-97117694"}}
{"title": "Features skeleton", "number": 4, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4", "body": "update math.c from NXP project\nfix compilation\nfix warning\n\n"}
{"comment": {"body": "1. project is not compiling: [https://bitbucket.org/levl/bosch\\_integration/addon/pipelines/home#!/results/55](https://bitbucket.org/levl/bosch_integration/addon/pipelines/home#!/results/55)\n2. no tests?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70009620"}}
{"comment": {"body": "Few things:\n\n1. Convert all fixed point floating function types to q31/q15. All integer calculation probably should be in a seperate file\n2. Match code to coding convention\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70015265"}}
{"comment": {"body": "1. We decided not to use q31 for now in cfo extraction\n2. Will be done in future commit\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70445908"}}
{"comment": {"body": "yep :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70445948"}}
{"comment": {"body": "not compliant with q31 multiplication", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70466985"}}
{"comment": {"body": "not compliant with q31 multiplication", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70467046"}}
{"comment": {"body": "Right, this should be int32, will be fixed in next commit", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70468920"}}
{"comment": {"body": "maybe slop & intercept could be in temp variable so that there wouldn\u2019t be so many pointer dereferences", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70471420"}}
{"comment": {"body": "couldn\u2019t x\\_mean and y\\_mean overflow here?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70471577"}}
{"comment": {"body": "this `for` looks redundant", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70471817"}}
{"comment": {"body": "what\u2019s the point in the index wrapping \\(modulus operator\\)? the indexes at the end of the buffer are not related to the beginning and vice versa.\n\ncould lead to false positives?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70472256"}}
{"comment": {"body": "I did not use this function yet, but I need to slop and intercept to be output variables", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70474719"}}
{"comment": {"body": "only theoretically\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70475115"}}
{"comment": {"body": "It is initializing global static that has values from previous call\u2026 It\u2019s not redundant", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70475414"}}
{"comment": {"body": "The algorithm following is using extrema counter to know how many and which fields were fields", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70476221"}}
{"comment": {"body": "agree", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/4/_/diff#comment-70478144"}}
{"title": "bad commit", "number": 40, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/40", "body": ""}
{"comment": {"body": "Looks good to me.\n\nThe CPU really need some rest.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/40/_/diff#comment-72107904"}}
{"title": "change agc filter to be from 3 instead of 4", "number": 400, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/400", "body": ""}
{"title": "Add ping and ping reply to ST; add system test", "number": 401, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/401", "body": "Add ping and ping reply to demo protocol, to be used in ST. \nAdded to projects testing_agent and fingerprinting_manual.\nAdded system test to see if ST is alive and responding using ping."}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/401/_/diff#comment-97648571"}}
{"comment": {"body": "Don't forget to `join` the thread", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/401/_/diff#comment-97648787"}}
{"comment": {"body": "Thanks :slight_smile:  \nRead and agree with all comments, will fix. 10x for the quick review!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/401/_/diff#comment-97664635"}}
{"title": "Feature/FIN-624 dockerize st", "number": 402, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/402", "body": "Update configurations to the 1.1.0.201903181104 plugins of the SDK\nFix dependency on obfuscated library - will now look for it during compile time\nDocker script for powerpc compiler\nCompilcation and obfuscation for ST Add step to jenkins\nFlash ST image in HW tests"}
{"comment": {"body": "Mind that most of the new files \\(Makefile, \\*.ld, components/\\*\\) are snapshots of the SPC5 Studio SDK", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/402/_/diff#comment-97857165"}}
{"comment": {"body": "Please move all `main_loop_st`-specific `.gitignore` entries to a `main_loop_st/.gitignore` file. Nested `.gitignore` files only apply to the directory they're in and it's much more cleaner than polluting a single `.gitignore` file with all the directory-specific stuff", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/402/_/diff#comment-97857230"}}
{"comment": {"body": "File can be simplified to:\n\n```shell\n#!/bin/bash\n\nset -e\n\n# Force the script to run inside the builder container\n. require_docker_powerpc_builder.sh\n\nfunction build_st_project {\n  pushd $1\n  mkdir -p build/obj\n  make clean\n  make all\n  popd\n}\n\nbuild_st_project main_loop_st/libfingerprinting\n./obfuscate_static_lib_powerpc.sh\nbuild_st_project main_loop_st/libbase_application\nbuild_st_project main_loop_st/testing_agent\nbuild_st_project main_loop_st/fingerprinting_manual\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/402/_/diff#comment-97857315"}}
{"comment": {"body": "Approved except for petty comments, good job", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/402/_/diff#comment-97857367"}}
{"comment": {"body": "Fixed in f24ef5f3e3d8d540bca0914430ac4b76ef6ba410", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/402/_/diff#comment-97857541"}}
{"comment": {"body": "Great idea. Fixed in f24ef5f3e3d8d540bca0914430ac4b76ef6ba410", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/402/_/diff#comment-97857547"}}
{"title": "Feature/BIS-3445 ibeacon for 9x corrected", "number": 403, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403", "body": "Copy ibeacon_filter from 8x\nRefactor packet capture to accept any kind of filter func (mainly to hide non-bosch behavior from files going into the bosch drop)"}
{"comment": {"body": "Is there a reason why we wouldn\u2019t want to enable capturing of ibeacons and regular beacons together?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403/_/diff#comment-97910668"}}
{"comment": {"body": "Good job :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403/_/diff#comment-97910707"}}
{"comment": {"body": "Recording iPhones and Androids together at the same time with the same firmware", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403/_/diff#comment-97911052"}}
{"comment": {"body": "That\u2019s exactly what I\u2019m wondering\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403/_/diff#comment-97911294"}}
{"comment": {"body": "No particular reason for the 9x. We could have that controlled by the host.\n\nTo be done in a future PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403/_/diff#comment-97911537"}}
{"comment": {"body": "We should avoid #ifdef configurations like `IBEACON_CAPTURER`. They don\u2019t get built by Jenkins so their code usually stops working and drifts off from what usually gets built.\n\nMaybe have a special agent for iBeacon capture? Or an agent that does both iBeacon and regular? We need to think about the right way to go about this, a simple `#ifdef` is not ideal", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403/_/diff#comment-97911765"}}
{"comment": {"body": "There is no need to for it to be controlled by the host, it can simply filter both packet types, can\u2019t it?\n\nIs there a big difference in the packet sizes?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403/_/diff#comment-97912107"}}
{"comment": {"body": "It can, but I haven\u2019t added filtering \\(\u201cshelters\u201d\\) for iphones, so we we\u2019d capture both ibeacons and bosch packets when we just want to record ibeacons", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403/_/diff#comment-97912845"}}
{"comment": {"body": "Good catch. Sounds like an agent doing both and host-controlled behavior would be best, but I\u2019ll add it in another PR ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/403/_/diff#comment-97913115"}}
{"title": "Add ST to CI", "number": 404, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/404", "body": "At last something worked.\n\nFlashing (as usual) using Cyclone\nReset using Arduino Due (which receives a 0 to pull down - perform reset, 1 to disconnect - stop reset)\nUART communication with UART2USB (using FTDI chip) instead of onboard serial communication - due to lack of linux drivers\nAdd flashing LED to ST testing agent to see that its alive\nRun ST ping test as part of CI"}
{"comment": {"body": "Is this setup documented anywhere? How to flash arduino, how to connect everything, etc.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/404/_/diff#comment-99078441"}}
{"comment": {"body": "In progress. Will be updated here: [https://jira.levltech.com:8090/display/BII/SPC58ECxx](https://jira.levltech.com:8090/display/BII/SPC58ECxx)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/404/_/diff#comment-99078992"}}
{"comment": {"body": "Is it possible to flash the Arduino automatically? Who resets the resetter?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/404/_/diff#comment-99080001"}}
{"comment": {"body": "* Possible to flash during tests - not worth the effort. \n* [dialog\\_hard\\_reset.py](https://bitbucket.org/levl/bosch_integration/pull-requests/404/add-st-to-ci/diff#chg-docker/hardware/dialog_hard_reset.py) should reset it. I\u2019m not expecting problems since software is simple and Arduino is reliable enough.\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/404/_/diff#comment-99080985"}}
{"comment": {"body": "Nice work", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/404/_/diff#comment-99090656"}}
{"comment": {"body": "This is redundant now when we have the Arduino to reset for us.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/404/_/diff#comment-99250238"}}
{"comment": {"body": "Arduino is resetting the ST only", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/404/_/diff#comment-99250369"}}
{"comment": {"body": "Why? There is not reason to re-flash it, the Arduino basically does nothing.\n\nThere is also no reason to reset it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/404/_/diff#comment-99250787"}}
{"title": "add is_moving bit (set according to our interpretation of gyro sensor) and phone_app_version", "number": 405, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/405", "body": ""}
{"comment": {"body": "version number should be a constant", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/405/_/diff#comment-98243328"}}
{"comment": {"body": "Probably better to write a separate function for the gyro code", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/405/_/diff#comment-98243966"}}
{"comment": {"body": "We should also have notification for the user if the phone is moving or not", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/405/_/diff#comment-98244662"}}
{"title": "Feature/BIS-3450 bosch integration add fields to packet", "number": 406, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/406", "body": "add is_moving bit (set according to our interpretation of gyro sensor) and phone_app_version\nlisten to gyro only while transmitting\nadd printout of isMoving that's being transmitted"}
{"comment": {"body": "I think it's better that we have the version byte be the first byte, it's odd to have version-specific bytes like `L` be before the version number which is \u201ccross-versions\u201d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/406/_/diff#comment-98395584"}}
{"comment": {"body": "It would be nice if you moved all this sensor code to it's own `.java` file, the `MainActivity.java` file is cluttered enough as it is .", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/406/_/diff#comment-98395697"}}
{"comment": {"body": "She did", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/406/_/diff#comment-99251851"}}
{"comment": {"body": "Correct - done", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/406/_/diff#comment-99302879"}}
{"title": "fix IQ CRC calculation on the board", "number": 407, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/407", "body": ""}
{"title": "Add ble app to suppor BLE/WiFi parallel transmission", "number": 408, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/408", "body": ""}
{"comment": {"body": "Is sporadic transmission relevant anymore?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/408/_/diff#comment-98704183"}}
{"comment": {"body": "Yes. We still need an option to transmit sporadically time-wise. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/408/_/diff#comment-98919138"}}
{"title": "Feature/dialog adv", "number": 409, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/409", "body": "Added ble advertiser\nAdded tid setting from python"}
{"comment": {"body": "Is this call redundant? `levl_adv_start` is already called within `levl_adv_data_set`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/409/_/diff#comment-98825223"}}
{"comment": {"body": "The commit looks great!\n\nCan you please check why the quick system tests stopped working though?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/409/_/diff#comment-98825560"}}
{"comment": {"body": "It seems like they did run for this PR. :no_mouth: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/409/_/diff#comment-98892727"}}
{"comment": {"body": "Add this project as part of the projects built in jenkins in build\\_eclipse\\_projects.sh to make sure that they don\u2019t break", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/409/_/diff#comment-98898032"}}
{"comment": {"body": "Most disregarded comment ever", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/409/_/diff#comment-99252854"}}
{"title": "Revert \"bad commit (pull request #40)\"", "number": 41, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/41", "body": "This reverts pull request #40.\n\nbad commit"}
{"title": "Feature/BIS-3482 8 anchors support", "number": 410, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/410", "body": "Modify voting_table to accommodate all features, in order to reduce memory consumption\nadjust code and tests to use 8 anchors\nModify python according to changes in classification structures\nadd UT for better coverage\nrename disable feature defines to feature mask defines"}
{"title": "Added advertiser to build_eclipse_projects.sh", "number": 411, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/411", "body": ""}
{"comment": {"body": "Merge?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/411/_/diff#comment-102546883"}}
{"title": "enable aging", "number": 412, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/412", "body": ""}
{"title": "Added \"apt-get update\" before starting to install from apt", "number": 413, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/413", "body": "This caused some issues with creating the images after adding support for the ST libraries."}
{"comment": {"body": "That's odd since hardware docker is built from x86 docker and x86 docker is doing `apt-get update`. Maybe there\u2019s another issue?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/413/_/diff#comment-99492098"}}
{"comment": {"body": "This happens when x86 is cached while hardware slave is not.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/413/_/diff#comment-99492550"}}
{"title": "timing feature", "number": 414, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/414", "body": "Timing feature:\n\nAdd required structures to save histogram during training progress and anchors' packets time intervals during classification progress.\nAdd ks statistic test\nIntervals extraction is used from both training and classification\nAdd the timing training and classification to fingerprinting flow\nAdd and modify UTs\nNote: will probably be pushed as disabled feature, still needs research for different phones"}
{"comment": {"body": "how does `kolmogorov_smirnov_two_sample_test` behave with histograms with different bin widths?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/414/_/diff#comment-100224476"}}
{"comment": {"body": "It doesn\u2019t care about it. I will add tests that make sure of that - 10x for the input", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/414/_/diff#comment-100227386"}}
{"comment": {"body": "I think you can remove the comment", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/414/_/diff#comment-100227461"}}
{"comment": {"body": "As we discussed - leaving just for code clarity", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/414/_/diff#comment-100227810"}}
{"comment": {"body": "These lines should be done in both scenarios, when timing extraction succeeds and when not\u2026\n\nI think it\u2019s better just to do them once outside of `timing_model_push_interval`\\(\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/414/_/diff#comment-100228940"}}
{"comment": {"body": "The reason I put it in both places is that in `timing_model_push_interval`\\(\\), if it reaches 100 packets it sets 0 in both fields.. so it shouldn\u2019t be overridden. I Can put these lines before the call to push, but then I need to save them in other vars to use them in the push, and the logic is a bit strange. As discussed, leaving as is", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/414/_/diff#comment-100229046"}}
{"comment": {"body": "Maybe we need to have also a python test for timing classification\u2026\n\nWhat do you think?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/414/_/diff#comment-100275791"}}
{"title": "Add some info to better explain code", "number": 415, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/415", "body": "Let me know if there are other places that I should add a little explanations"}
{"comment": {"body": "Thanks \\(:", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/415/_/diff#comment-100789179"}}
{"title": "Hardware docker container will now use the host's ADB server instead of it's own, hopefully this will cause less disconnections", "number": 416, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/416", "body": ""}
{"title": "Feature/BIS-3562 coarse cfo estimation", "number": 417, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/417", "body": "This is a partial PR for your reviews. Theres some work left.\nChange log:\n\nAdd coarse CFO estimator (not integrated into preprocessing)\nSupporting modules: circular buffer, zero crossing, uphase\nUpdated Catch to version 2.7.2 (for using Data Generators)\nCouple of fixes\n\nWork left:\n\nBuild LUT for CFOs other than 1Mhz and 960KHz (probably for 900KHz, 950KHz, 1.05MHz, 1.10MHz)\nIntegrate into preprocessing flow\nDocuments"}
{"title": "Changes to the libfingerprinting API to support calibration", "number": 418, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/418", "body": ""}
{"comment": {"body": "Shouldn\u2019t we also have Levl\\_StartCalbration in our api?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/418/_/diff#comment-100315091"}}
{"comment": {"body": "Train is used for calibration, when initialized with `LEVL_TRAINTARGET_DIALOG_MASTER`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/418/_/diff#comment-100315457"}}
{"comment": {"body": "ok, would we have an option to change training params here if needed?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/418/_/diff#comment-100316016"}}
{"comment": {"body": "We can choose to behave differently during training when `LEVL_TRAINTARGET_DIALOG_MASTER` is configured", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/418/_/diff#comment-100316451"}}
{"title": "Feature/BIS-3562 coarse cfo estimation part 2", "number": 419, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419", "body": "The feature is finished. The work remaining is described later.\nChange log:\n\nAdd coarse CFO estimator\n\nSupporting modules: circular buffer, zero crossing, uphase\n\nDocumentation: \n\n\n\nAdd tables for baseband correction for 850KHz, 900KHz, 960KHz, 1040KHz, 1100KHz, 1152KHz; code generator provided\n\nMinor fixes\n\nWork left:\n\n~~Failing integration tests are due to us expecting to fail at >100KHz CFO but now we can handle them. Waiting for @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} to provide new packets.~~\nDocuments\n~~Regular CFO estimation error is now larger comparing to python with to baseband correction of not 1MHz (probably) due to quantization error.~~"}
{"comment": {"body": "Last point in \u201cwork left\u201d  section sounds problematic. Do you plan to fix it before the merge?\n\nAlso, do you plan to run serious tests for the new code to make sure we don\u2019t break the cfo feature \\(maybe compare cfo estimation of ~1K packets with coarse cfo estimation in the pipeline and without, we probably need to pick the packets with interesting base cfos - maybe around 50K or -50K, here the coarse cfo estimation will probably affect the cfo estimation\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100570039"}}
{"comment": {"body": "There\u2019s no easy way to fix this, but I should elaborate that the error rose from 480Hz to 580Hz.  \nThere\u2019s a suggestion by @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} to work with linear phase correction instead of complex exponent multiplication, but it\u2019s not a short task.\n\nI\u2019ve added 4 out of 130 failed packets from Bosch with CFOs <-100KHz. I could add the rest of them if you think it\u2019ll benefit. Do you have a suggestion for other packets?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100571061"}}
{"comment": {"body": "I suggest querying packets from our db - few packets for each cfo in \\[-75K,-50K,-25K,25K,50K,75K\\]. \n\nFor every packet running the cfo calculation from develop and from your branch and compare the results.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100571288"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} , you\u2019re already fetching new packets for the tests. Could you also prepare such packets that @{5a1d1745007eb21a79e5f2b7} requested?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100588518"}}
{"comment": {"body": "I think we must do it. And even do a full evaluation of the CFO estimation total estimation, but before that, we should verify that the down conversion doesn\u2019t have rounding errors.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100611599"}}
{"comment": {"body": "Do we have tests for this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100704705"}}
{"comment": {"body": "Do we have tests with the different tables?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100707392"}}
{"comment": {"body": "No. I\u2019ll add some tests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100708040"}}
{"comment": {"body": "yes. I will prepare the testing packets", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100916294"}}
{"comment": {"body": "Fixed in f6fae25c42218ce7317fd08f272806ecab10b14c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100916330"}}
{"comment": {"body": "Fixed in daf78b237265d74ef0b59f93895fbbf21aa9c438", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100916332"}}
{"comment": {"body": "What buffer? When should this be called? What is this buffer used for? Parameter docs don\u2019t match actual parameters", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100924752"}}
{"comment": {"body": "something seems off about this function. I\u2019ll explain tomorrow ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100924828"}}
{"comment": {"body": "dont we have some array\\_length macro? ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100924969"}}
{"comment": {"body": "Yup, need to update", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100935299"}}
{"comment": {"body": "apparently not. COUNT\\_OF would have been nice, but macros aren\u2019t allowed according to MISRA", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100936987"}}
{"comment": {"body": "Fixed in 8b0e01ca01720526c6fef6326ce612b27e56d68d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100937399"}}
{"comment": {"body": "Did you consider merging the ifs to one or statement since the action in both ifs is the same? something like bool need\\_to\\_add = \\(peak\\_type\\_maximum && \u2026\\) || \\(!peak\\_type\\_maximum && \u2026.\\) \u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100951129"}}
{"comment": {"body": "Did you consider combining all those m\\_\\* variables to one struct?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100951835"}}
{"comment": {"body": "Yes, but coverage for this killed me so I split it to two cases", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100954818"}}
{"comment": {"body": ":disappointed: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100964542"}}
{"comment": {"body": "at work left, bullet 1 -  I finished adding new packets", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100970428"}}
{"comment": {"body": "Would it be more convenient?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-100978111"}}
{"comment": {"body": "Please approve", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/419/_/diff#comment-101697784"}}
{"title": "very bad commit", "number": 42, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/42", "body": ""}
{"title": "Feature/BIS-3562 coarse cfo estimation part 3", "number": 420, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/420", "body": "Coverage\nKlocwork false positive\nOther fixes"}
{"title": "Feature/BIS-3562 coarse cfo estimation part 4", "number": 421, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/421", "body": "Merge develop\nAdd range test to estimation\nAdd CFO range when checking for specs"}
{"title": "Enable timing; add fixes after research", "number": 422, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/422", "body": "Research on the timing feature provided the following insights:\n\nBLE interval timeslot should be 1250, since the bins are always even. This also allows to discard illegals and improves true positive rate.\nNumber of bins in histogram should be 40. 32 is not enough and important data seems to be lost (we get lower true positive rate).\nWhen using the KS test, should not move the histograms so they would align. The KS test is very sensitive to it and provides bad results if we attempt alignment.\nP-value that provides True Positive Rate of over 99.5% for 6-30 intervals is 0.011.  \n\nFixed according to research insights; also modified the timing model itself to take less space."}
{"comment": {"body": "maybe split this loop into 2 for loops? 1st from 0 to `TIMING_MODEL_MAX_BINS` then 2nd from `TIMING_MODEL_MAX_BINS` to n\\_bins?\n\ncould help readiness and compiler optimization \\(and less branches\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/422/_/diff#comment-101195914"}}
{"comment": {"body": "did you check that `timing_model_st` doesn\u2019t have some padding?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/422/_/diff#comment-101196238"}}
{"comment": {"body": "worth checking if the compiler would optimize this and not dereference the pointers until the very end.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/422/_/diff#comment-101196819"}}
{"comment": {"body": "Yes, I wrote it so it would be divided by 8 bytes. Also the events work well with the board.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/422/_/diff#comment-101256932"}}
{"comment": {"body": "yes, you\u2019re right, will use local var and dereference only at the end.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/422/_/diff#comment-101257323"}}
{"comment": {"body": "Yes, good idea, will do. 10x", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/422/_/diff#comment-101257512"}}
{"title": "Feature/monitor v3", "number": 423, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/423", "body": "Monitor V3"}
{"comment": {"body": "Somebody\u2019s missing from the reviewers list..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/423/_/diff#comment-100916358"}}
{"comment": {"body": "Yep, almost ~7-8 billion people", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/423/_/diff#comment-100916681"}}
{"comment": {"body": "Why don't you take this from the packet struct itself?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/423/_/diff#comment-101692091"}}
{"comment": {"body": "For now each packet holds 4 different events and each of them contains a lot of irrelevant information. These columns are the most important and interesting ones. \n\n\u200c\n\nI'll make packets clickable in the future so you can see the rest of the info ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/423/_/diff#comment-101692100"}}
{"title": "Update catch to v2.7.2", "number": 424, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/424", "body": ""}
{"title": "Feature/BIS-3562 coarse cfo estimation part 5", "number": 425, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/425", "body": "Consective bits count as sections\nEarly stop when enough sections detected\nRemove unused code and fix tests"}
{"title": "reduce packet buffer size", "number": 426, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/426", "body": ""}
{"title": "fix according to CR comments", "number": 427, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/427", "body": ""}
{"title": "allow filtering of imei in iq recording agent", "number": 428, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/428", "body": ""}
{"comment": {"body": "This function is exposed to Bosch as part of the package we create when we release. Can you put this somewhere else?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/428/_/diff#comment-101891338"}}
{"comment": {"body": "you could use `memcmp` next time", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/428/_/diff#comment-101891442"}}
{"comment": {"body": "Will do, thanks!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/428/_/diff#comment-101898041"}}
{"comment": {"body": "Right, nicer code, changing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/428/_/diff#comment-101898202"}}
{"title": "fix according to CR comments", "number": 429, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/429", "body": ""}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/429/_/diff#comment-101908651"}}
{"title": "Revert \"very bad commit (pull request #42)\"", "number": 43, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/43", "body": "This reverts pull request #42.\n\nvery bad commit"}
{"title": "Feature/BIS-3410 h feature", "number": 430, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430", "body": "Add H feature\nFeature extraction now disqualifies packet if CFO is out of specs"}
{"comment": {"body": "lol", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-101974866"}}
{"comment": {"body": "Can you give a more verbose reason?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-101975031"}}
{"comment": {"body": "Approved other than comments", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-101975323"}}
{"comment": {"body": "Hmm do we usually store bool in our structures and not uint8\\_t? I though need to specify the exact bit size according to MISRA", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-101992600"}}
{"comment": {"body": "Other structures also use bool", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-102063399"}}
{"comment": {"body": "Not following", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-102063435"}}
{"comment": {"body": "These tests actually need to be removed altogether. We now don\u2019t have an option for feature extraction to succeed and have CFO invalid at the same time.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-102063718"}}
{"comment": {"body": "I think we should allow some more space here, we have seen some dongles with slightly larger than 0.53 average h, so this limit might be problematic.\n\n@{557058:f7a3b23e-03ba-40b3-9ce6-b139e8b20556} @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} what do you think the limit should be? 0.45-0.55 sounds reasonable?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-102067849"}}
{"comment": {"body": "In our regression tests there are device types that should be separated using only the h feature.\n\nCan you add this check to the regression tests?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-102070849"}}
{"comment": {"body": "0\\.45 - 0.55 sounds better to me.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-102071619"}}
{"comment": {"body": "I\u2019ll replace it in a follow-up PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-102542021"}}
{"comment": {"body": "I see. Just add this text to the x-fail reason then ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/430/_/diff#comment-102542145"}}
{"title": "add option to reset imei filters", "number": 431, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/431", "body": ""}
{"title": "Feature/compile with ghs", "number": 432, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/432", "body": "Add support for GHS compiler the the fingerprinting project.\nRe-generated files for ghs compiler\nFixed docker for ghs licensing\nChanged compilation flags for ghs\nFixes for license\nAdded new license\nFixed obfuscation script for GHS compiler\nFixed obfucation"}
{"comment": {"body": "What\u2019s your proposed way of running this docker locally? With SSH tunnel to local port 2009?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/432/_/diff#comment-102059414"}}
{"comment": {"body": "It can be a solution, but it won\u2019t work all the time since the license server only runs when we are doing builds, but this can be fixed if needed.\n\nThis is a problem since we do only have license for the Jenkins server..\n\nI do have another solution I won\u2019t specify here, remind me to tell you about it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/432/_/diff#comment-102071989"}}
{"comment": {"body": "Check that the compiler flags match [Bosch\u2019s expectations](https://jira.levltech.com:8090/display/BII/DA1469x?preview=/317653053/430244206/OPL8_compiler_20181106.docx)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/432/_/diff#comment-102546309"}}
{"comment": {"body": "Finally passed build, please approve.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/432/_/diff#comment-105853177"}}
{"title": "Add timing to monitor, with graphics", "number": 433, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/433", "body": "Add timing to monitor, column will show the bin index of each packet (retrieved from time elapsed from last packet of same anchor).  \nClicking on the Timing column should produce a proper histogram per anchor:\n\nFor training, histogram will be created from training packets\nFor classification, the model histogram and the classification packets histogram will be presented in the same diagram, and results of ks test will be presented"}
{"comment": {"body": "Cool!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/433/_/diff#comment-102269285"}}
{"title": "Jenkins slaves will now connect via an SSH tunnel", "number": 434, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/434", "body": ""}
{"title": "Implement calibration", "number": 435, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435", "body": "Beside the code, we should also discuss the API (names are different than those in the external API doc)"}
{"comment": {"body": "Do we need to check the num\\_packets here? Each features checks separately if got enough packets to build it. \n\nBasically what\u2019s I\u2019m asking is, what is the desired behavior if we call the Levl\\_Train\\_Build\\_Submodel with less than max\\_packets packets but enough packets to build each feature? \n\nShould we wait for max\\_packets or return the model?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-103439561"}}
{"comment": {"body": "I think we should add a GLOBAL\\_STATIC\\_ASSERT to make sure that TRAINING\\_NUM\\_PACKETS\\_FULL is smaller than TRAINING\\_NUM\\_PACKETS\\_MAX. actually we need it to be much smaller and not by 1-2 packets, because otherwise we will always stop training while we have only one model \\(one of the anchors reached 2000 and the rest are 1900\\+\\) I suggest something like TRAINING\\_NUM\\_PACKETS\\_FULL \\+ 200 < TRAINING\\_NUM\\_PACKETS\\_MAX", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-103461173"}}
{"comment": {"body": "Seems like a code duplication for all our \u201cnormal features\u201d . Would it be nicer to do something like normal\\_feature\\_apply\\_calibration\\(normal\\_model\\_t\\* incoplete\\_normal\\_model, normal\\_model\\_t\\* normal\\_model\\_of\\_modeled\\_anchors, n\\_modeled\\_anchors,\u2026.\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-103467188"}}
{"comment": {"body": "When we get to this function, it means the flow \\(previous to this func\\) was true to one of two options:\n\n1. All anchors reached 1500 packets\n2. One of the anchors reached 2000 packets, and this is not the calibration process \\(for which we require ALL anchors to get to 1500 packets\\).\n\nThis function needs to distinguish between the case of:   \na. reached 1500, but not enough data for one of the features \\(i.e., training fails\\)  \nb. didn\u2019t get to 1500 because another anchor reached 2000 already, and this is valid, and calibration will be required.\n\nChecking if we reached 1500 allows us to distinguish between these cases.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-103831958"}}
{"comment": {"body": "Yea, so I struggled with it mentally while writing the code. The problem is that we use a pointer to a structure \\(Model\\) that\u2019s divided by submodel, and not by feature.  \nSo, if I want to avoid code duplication, I need to collect the pointers of the each relevant feature and put them in an array of pointers \\(which should probably be held statically\\).  \nThe code of collecting the pointers of the features would also be a duplication, since it\u2019s the same thing for a different feature..  \nThen I would need to get the result and fill the relevant feature in the model, which is again, a duplication.\n\nFinally I decided that to avoid duplication here I need to write about the same amount of code, which would also look like a duplication.\n\nSo, I passed :\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-103834443"}}
{"comment": {"body": "Python Enums don\u2019t require you to specify a value:  \n\n![](https://bitbucket.org/repo/x8eLRbo/images/4092287035-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-103920531"}}
{"comment": {"body": ":slight_smile: I think the manual enumeration is ok here and more conventional. Nice to know this option exists though, thanks", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-104186753"}}
{"comment": {"body": "Right - adding", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-104186981"}}
{"comment": {"body": "IIRC Dima said we only need to not learn the board temperature, but I'm not sure", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-104191573"}}
{"comment": {"body": "Yes that was my understanding as well but he found it was wrong and we fixed it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-104191701"}}
{"comment": {"body": "Ok, but if for example we reached this function with 2000 packets from one anchor and 450 packets from another, now we don\u2019t try to build a model for a 500 packets anchor. \n\nIn some cases we can build a model from less than max\\_packets packets \\(if cfo model is disabled for example\\) but we don\u2019t even try. Is it the desired behavior? ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-104212364"}}
{"comment": {"body": "Yes, we proposed this to Michael and he decided we should use a simple rule, since it will most likely be discussed with Bosch laster on anyway.  \nSo even if the second anchor has even just below what\u2019s required, we still dismiss it for now.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-104215723"}}
{"comment": {"body": "Sigal, we need a good explanation like this about the max\\_packets in our design  document, can you please write a few sentences  about it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-104215932"}}
{"comment": {"body": "Sure, once we finish CR I\u2019ll update the design doc, and also include this explanation", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/435/_/diff#comment-104219227"}}
{"title": "Recording agent to not rotate output packet", "number": 436, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/436", "body": ""}
{"comment": {"body": "You forgot to sign the IQ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/436/_/diff#comment-102867134"}}
{"comment": {"body": "I don\u2019t really need signed IQ on non rotated buffers. Maybe I\u2019ll add an assert to prevent choosing this option.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/436/_/diff#comment-102867396"}}
{"title": "Feature/board temperature slop train", "number": 437, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/437", "body": "update conf interval when the tested packets temperature is far from the train temperatures\nadd unitests for board conf interval\nreduce histogram size from word to byte, add board histogram to cfo progress struct\ncheck also board temperature histogram for major update\nmove board slop to train from constant\nprotect histogram from overflow\ntemperature histogram to store temperature range per bin"}
{"comment": {"body": "Looks good lets do some tests with the HW as we discussed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/437/_/diff#comment-103177843"}}
{"comment": {"body": "Beautiful code. Short and elegant", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/437/_/diff#comment-104592318"}}
{"comment": {"body": "Please update monitor to also perform this calculation (code can be found at system_tests/monitor/single_packet.py)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/437/_/diff#comment-104596283"}}
{"comment": {"body": "I\u2019m only ~80% sure that you\u2019re  sarcastic, so I\u2019m not sure how to reply.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/437/_/diff#comment-104597303"}}
{"comment": {"body": "Is this logic implemented in the monitor now? I couldn\u2019t find it in the code ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/437/_/diff#comment-104598705"}}
{"comment": {"body": "I think that when you feel that Grisha is sarcastic this means he is very sincere.  I would bet its 90% a real compliment :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/437/_/diff#comment-104604902"}}
{"title": "Added support for recording from the iphone app", "number": 438, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/438", "body": ""}
{"comment": {"body": "* what data is available on the iphone advertisements?\n* what about the PC scripts? Could they handle iphone advertisements?\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/438/_/diff#comment-103398249"}}
{"comment": {"body": "PC scripts should be able to handle them, except for actually doing the fingerprinting itself.\n\nWe can only modify the Local\\_Name on iOS, so i\u2019ve decided to encode the data into the name using base64 encoding.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/438/_/diff#comment-103432472"}}
{"title": "fix app on phones without Gyro", "number": 439, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/439", "body": ""}
{"comment": {"body": "Could you add an explanation of why you left the comment or remove it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/439/_/diff#comment-103426357"}}
{"comment": {"body": "On second thought, it seems finish\\(\\) is proper here \\(as we do in other cases\\). It seems the finish\\(\\) doesn\u2019t close immediately though. Maybe the correct fix here is keep the finish\\(\\) and just add return, in case the finish doesn\u2019t happen immediately.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/439/_/diff#comment-103451900"}}
{"comment": {"body": "fixed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/439/_/diff#comment-103603081"}}
{"title": "corrected RF usage in main loop", "number": 44, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/44", "body": ""}
{"title": "Feature/shelter filtering", "number": 440, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/440", "body": "Added shelter filtering on the board side.\nAdded option for shelter filtering on iq_recording_agent\nAdded android app support for shelter filtering"}
{"comment": {"body": "I think that we need to support more than 16 values\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/440/_/diff#comment-103793126"}}
{"comment": {"body": "It's just for separating different ovens basically\u2026 I don't think we'll record using 16 different boards in parallel. It's not for personal use, just for recording use. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/440/_/diff#comment-103794435"}}
{"title": "Feature/dual beacon", "number": 441, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/441", "body": "Added views for dual beacon.\nPassing the beacon count parameter to the advertiser service.\nAdded dual beacons to the app.\nFixed crash when stopping the advertisement."}
{"title": "Feature/BIS-3947 SDK Update - Moving from AA to AB, support for flashing Bosch hardware", "number": 442, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/442", "body": "From now on, we only support AB boards by default. If you want to compile for AA boards, you need to uncomment the AA config line and use the special AA scripts for flashing.\n\nUpdated SDK to Bosch's variant of 10.430.2.4_AB\nFixed timing feature monitor failure display\nFixed SDK bug (IQ buffer start offset invalid)\nChanged IQ Agent version string to indicate 99AB\nAdded scripts for flashing AA and Anchor B3\nAll agents now support new IQ capture"}
{"comment": {"body": "maybe prefix this comment with LEVL? so that we\u2019d know where to look for SDK patches", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/442/_/diff#comment-103951747"}}
{"comment": {"body": "It\u2019s not an SDK patch, it\u2019s part of our code to overcome the SDK bug", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/442/_/diff#comment-103952164"}}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/71628897-goto.png)\n[[https://xkcd.com/292/](https://xkcd.com/292/)](https://xkcd.com/292)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/442/_/diff#comment-103952171"}}
{"comment": {"body": "I used 11 goto\u2019s, how bad can THAT be?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/442/_/diff#comment-103952639"}}
{"comment": {"body": "What would be the workflow now for flashing dev boards, anchors and old AA boards?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/442/_/diff#comment-103952886"}}
{"comment": {"body": "Get attacked by 11 velociraptors?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/442/_/diff#comment-103953020"}}
{"comment": {"body": "I\u2019m sorry for the attack, but here\u2019s another quote:\n\n![](https://bitbucket.org/repo/x8eLRbo/images/417963658-image.png)\n\u200c\n\n[https://www.i-programmer.info/programming/theory/1332-goto-spaghetti-and-velociraptor.html](https://www.i-programmer.info/programming/theory/1332-goto-spaghetti-and-velociraptor.html)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/442/_/diff#comment-103953695"}}
{"comment": {"body": "**AB Dev boards** - seamless. Just do everything as we usual did for AA.\n\n**B3 Anchors** - Use the `program_qspi_serial_b3` flashing script. Compile for AB \\(no need to change anything\\)\n\n**AA Boards** - Uncomment `// #define dg_configENABLE_DA1469x_AA_SUPPORT (1)` in `custom_config_qspi.h`, then for flashing use either of `program_qspi_jtag_aa` and `program_qspi_serial_aa` for jtag/serial flashing respectively. The BLE stack library has an AB suffix but it seems to work fine on AA so no need to change that.\n\n\\`\\`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/442/_/diff#comment-103957363"}}
{"title": "Feature/dual beacon", "number": 443, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/443", "body": "Added views for dual beacon.\nPassing the beacon count parameter to the advertiser service.\nAdded dual beacons to the app.\nFixed crash when stopping the advertisement.\nFixed beacon index."}
{"title": "fix wifi issue", "number": 444, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/444", "body": ""}
{"title": "Feature/BIS-3410 h feature regression tests", "number": 445, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/445", "body": "Start to add data structures for h feature\ncompile already\nContinue implementation\nFix unused\nMore missing h feature settings\nH feature shouldn't be dependent on CFO feature mask for feature calculation\nFix tests\nFix tests\nUpdate structures sizes\nAlways filter according to H & CFO ranges, not just when features are enabled\nFix tests\nUpdate tests and hydra\nTry to handle coverage\nSimplify boolean logic\nUpdate flow and tests\nMark xfail\nRemove hind_est redundant variable Remove some prints\nGive significant names to PyFingerprintingFeaturesDisabled patterns\nFix some h feature related Add H feature regression tests\nAdd exact H test to python\nLess strict exact test\nCoverage\nAdd test for not enough H packets\nFix test\nFix struct\nRemove test for invalid H (since does't make sense)\nFix structure size\nHydra\nFix some regression builder types\nMaking use of regression tests generator ver2\nUpdate builder script\nAdd dongles to regression tests Print logs of regression stages\nDownload new dongle data set\nFinalize classes Cleanup warnings\nFix name of regression test running\nUpdate regression tests output name\nMake it print everything (for debug)\nPrints cause it to overflow\nSeparate regression tests\nfix paths\nPrint regression dataset init progress Give up on GENERATE_REF Update argparsing\nNo parallel\nTry to understand which test is causing the crash\nNot prints of success for 32-bit regression tests\nSee if removing capture helps\nNo new in tests\nNo reports for 32-bit"}
{"title": "disable timing", "number": 446, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/446", "body": "Following Omers findings that the timing histogram jumps between at least two places when  user activates other apps on the phone, the timing feature should be disabled to prevent it causing many false negatives (false NOT_MATCHes). \nWe should resume feature investigation later on."}
{"title": "add validation for invalid submodels in classify", "number": 447, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/447", "body": "If classify gets a module that has one or more invalid submodules, it should return failure: LEVL_CLASSIFY_ERR_INCOMPLETE_MODEL"}
{"title": "Feature/BIS-3746 coarse cfo in monitor", "number": 448, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/448", "body": "Add coarse CFO estimation to monitor\nAdd H feature columns"}
{"title": "Feature/BIS-4057 add all submodules valid valida", "number": 449, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/449", "body": "Add validation to Levl_Classify() - go over all submodels and check theyre valid. Otherwise, return LEVL_CLASSIFY_ERR_INCOMPLETE_MODEL."}
{"comment": {"body": "\"submodules\"", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/449/_/diff#comment-104609488"}}
{"comment": {"body": "Nice catch \\(:", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/449/_/diff#comment-104613006"}}
{"title": "Feature/BIS-231 runtime optimization", "number": 45, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/45", "body": "lib is now compiled with optimization for speed\nfix python issue\nadding inits to python\ntransient extraction: replacing start of transient search with memory\ntransient extraction: error handling when we don't have enough samples\nremoving redundant iterator template for transient extraction due to optimization"}
{"title": "Feature/program old board script", "number": 450, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/450", "body": "AA support for build/program scripts"}
{"title": "Feature/dual beacon 2", "number": 451, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/451", "body": "Please review packet_captures new API.\nThe rest of the changes are just migrations for all projects to support the new api.\n\n\nRefactored packet_capture interface to enable capturing packets and handling them on different tasks.\nAdded new packet_capture interface support to the iq_capture project.\nMigrated all projects to the new packet_capture api.\nRaised priority of the rfmon task to enable faster filtering of unneeded packets."}
{"comment": {"body": "Bless you", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/451/_/diff#comment-104817514"}}
{"title": "BIS-4105 monitor to support calibration", "number": 452, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/452", "body": "Adjustment of monitor to ST (branched from Omers branch):\n\ninitial adjustments of Monitor to ST\nVarious changes for monitor to support more than one anchor\nEven better master support ST\nMonitor now supports master but it's slow\nFixed monitor bug\n\nAdd support for calibration:\n\nadd monitor support for calibration; add two-button-presses to st manual fingerprinting to request calibration"}
{"title": "Remove test_ITD_001 and test_ITD_017", "number": 453, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/453", "body": "Tests need to be removed since they are meaningless"}
{"comment": {"body": "Why are they meaningless?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/453/_/diff#comment-105855379"}}
{"comment": {"body": "Because we changed the meaning of not valid CFO - now it means the whole Feature extraction should fail.\n\nBut actually the input packets are good to verify the new behavior so we just need to make sure feature extraction fails with the bad CFO packets", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/453/_/diff#comment-105855541"}}
{"comment": {"body": "`test_ITD_003_cfo_range_validation` is doing that", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/453/_/diff#comment-105876888"}}
{"comment": {"body": "approve much?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/453/_/diff#comment-105906630"}}
{"title": "BIS-4105 monitor to support calibration", "number": 454, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/454", "body": "initial adjustments of Monitor to ST\nVarious changes for monitor to support more than one anchor\nEven better master support ST\nMonitor now supports master but it's slow\nFixed monitor bug\nadd monitor support for calibration; add two-button-presses to st manual fingerprinting to request calibration"}
{"title": "Add 6 regression tests to cfo calibration online training", "number": 455, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/455", "body": "Tests with temperature changes - some tests are currently set to ignore until Dimas algorithm fix."}
{"comment": {"body": "Much nicer :thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/455/_/diff#comment-105418058"}}
{"comment": {"body": "\\(\\(:", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/455/_/diff#comment-105418682"}}
{"title": "Feature/FIN-773 dialog UART gets stuck", "number": 456, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/456", "body": "Add monitor to UART writes that resets the UART FIFO if SW doesnt respond"}
{"comment": {"body": "Please mark this as an SDK patch", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/456/_/diff#comment-105397356"}}
{"comment": {"body": "Nice", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/456/_/diff#comment-105417725"}}
{"title": "Feature/cfo conf interval use histogram", "number": 457, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457", "body": "get rid of different number of packets for full and const\ntemperature histogram add distance from given temperature calculation\ncalculate cfo conf interval from histogram\nremove max,min temperatures mechanism from cfo model\npython structs fix\nadd temperature histogram unitests\nclibration tests are passing now"}
{"comment": {"body": "Do you want to update it to something lower \\(like 1250 or 1300\\) like we agreed or do you want to do it in a different PR?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105855305"}}
{"comment": {"body": "I actually tried, but had Misra problems. In our current flow if the cfo is invalid for some reason so seems that the whole packet is invalid, so I can\u2019t reach a case when total packets is larger than 1500 but packets for cfo is smaller than 1400 for example. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105855326"}}
{"comment": {"body": "So why it\u2019s a Misra problem? Do we have some unreachable conditions?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105855478"}}
{"comment": {"body": "It looks like we bound the CFO into a 20KHz wide band for the entire range of temperatures.  \nIs it valid for all the phones and board we have recorded?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105855538"}}
{"comment": {"body": "Sorry, code coverage problem. In train\\_build\\_model\\_cfo We can\u2019t reach this code with cfo-progress->num\\_packets < 1500 because we call this func only when received more than max\\_packets=1500 \\(from train\\_build\\_submodel func\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105855632"}}
{"comment": {"body": "Not sure what do you mean. the 40 bounds our conf interval to 4000 \\(40 \\* 50 \\+ 2000\\) We can increase it if we need, but 4000 looks reasonable.\n\nMaybe instead of bounding the temperature it\u2019s better to bound the interval to 4000?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105855783"}}
{"comment": {"body": "Oh ok. Got it.  \nI didn\u2019t understood the code correctly..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105855845"}}
{"comment": {"body": "Ok.   \nSo we will do it in a different PR.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105855916"}}
{"comment": {"body": "There are more skipped calibration tests in  **test\\_calibration\\_combinations.py**\n\nCan you check that your code fixed them as well?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105856027"}}
{"comment": {"body": "Checked those, some pass, but few doesn\u2019t. Seems that there is an issue with the packets we use for those tests. there are few \u201ccfo lobes\u201d in those packets - similar to the wifi on behavior. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105856560"}}
{"comment": {"body": "I see.\n\nWe are now doing new records. So we can replace them.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/457/_/diff#comment-105856591"}}
{"title": "extend feature extration duration test to check training and classify durations as well", "number": 458, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/458", "body": ""}
{"comment": {"body": "Please leave `QT_FEATURE_001` as part of the test name", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/458/_/diff#comment-105615902"}}
{"title": "Feature/BIS-4152 2 beacons tests", "number": 459, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/459", "body": "add tests to check 2 beacons; add command for controlling beacons count to android app\nname for doc reference"}
{"title": "merging recording to main application", "number": 46, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/46", "body": ""}
{"comment": {"body": "worth refactoring at this point, isn\u2019t it? splitting the python script into multiple files", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/46/_/diff#comment-72510549"}}
{"comment": {"body": "Agree, will fix on Sunday", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/46/_/diff#comment-72543614"}}
{"comment": {"body": "This will be fixed in next PR when we integrate into Grisha\u2019s framework", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/46/_/diff#comment-72925397"}}
{"title": "Feature/FIN-755 monitor to support more than one", "number": 460, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/460", "body": "Various changes for monitor to support more than one anchor\nadd monitor support for calibration; add two-button-presses to st manual fingerprinting to request calibration\nProgress reports will now only have 2 anchors, removing voting and timing tables from classification progress\nAdded training/calibration progress indication\nExtensive monitor improvements\nAdded feature plotting to monitor\nAdded plots to many other columns\nAllow user to choose which anchors are shown and which are not in graphs\nIgnore pickle files in monitor directory\nST Testing agent will report the correct identifier\nReplace struct assignment with memcpy because it causes CPU to crash with -O2"}
{"title": "adv agent to operate with monitor", "number": 461, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/461", "body": "(1) Added an option to burn the adv agent with specific tid and then use a button to start advertising - so it can be used with monitor;\n(2) Monitor adjusted so the adv agent can be connected and not considered an anchor."}
{"comment": {"body": "What changed in this file? Could you remove the extra empty lines?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/461/_/diff#comment-105913120"}}
{"comment": {"body": "The prev settings was wrong and compilation failed, it tried to link with ble\\_stack\\_da1469x. Once we moved to AB boards, it should be ble\\_stack\\_da1469x\\_AB \\(this was the change\\).  \nWhat empty lines?.. I did not edit this file manually", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/461/_/diff#comment-105920272"}}
{"comment": {"body": "mmm I see Guy made the same change manually in a different PR, I\u2019ll probably merge with his code once it\u2019s pushed to develop and remove this auto-generated change.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/461/_/diff#comment-105921170"}}
{"title": "Fixed temperature formatting and compilation of the adv_agent project", "number": 462, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/462", "body": ""}
{"title": "Add doc links to test names", "number": 463, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/463", "body": ""}
{"title": "Feature/monitor  requests", "number": 464, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/464", "body": "Events now contain vote information\nFixed model reporting after classification timeout\nShow which features are matching and which not\nAdded handy console\nMonitor will now show failed features if classification fails"}
{"title": "Feature/BIS-4217 QT classify 014 015 023 024", "number": 465, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/465", "body": "Add QT-CLASSIFY-023\nAdd test for online training after calibration"}
{"comment": {"body": "cool, do you plan to add a test for QT-CLASSIFY-\\{15,23\\} or the `\"Online training after calibration\u201d`\n\nwith the minor update case is enough?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/465/_/diff#comment-106046635"}}
{"comment": {"body": "QT-CLASSIFY-015 was already implemented. QT-CLASSIFY-024 is a duplication of 015, like we discussed.   \nDo you feel there would be benefit for implementing 023 or do you mean something else?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/465/_/diff#comment-106047488"}}
{"comment": {"body": "sounds good, missed the QT-015", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/465/_/diff#comment-106048062"}}
{"comment": {"body": "Do you plan to change the struct variable declarations across all levl code?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/465/_/diff#comment-106048394"}}
{"comment": {"body": "No :slight_smile:   \nJust where CLion points me to..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/465/_/diff#comment-106052353"}}
{"title": "Feature/self hosted files", "number": 466, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/466", "body": "Self host powerpc packages"}
{"title": "Feature/change default board temp slop behavior", "number": 467, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/467", "body": "\n\nallow different param for board and phone conf interval penalties\nchange board temperature default slop to 600\nincrease the default values - CHANGED IT TO 100 FOR PHONE, 150 FOR BOARD, MAINMY BECAUSE INCREASING IT MORE FOR THE PHONE FAILED OUR CURRENT REGRESSION TESTS\nadd histograms initialization"}
{"comment": {"body": "Unrelated question, is there any reason why those are part of the model?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/467/_/diff#comment-106046026"}}
{"comment": {"body": "good question, same about predict\\_conf\\_interval\\_base param I guess?\n\nwe need those for the classification and we probably\\(?\\) need an option to pass those to classify funcs so  just consts in cfo\\_classification file is not great. \n\nSo what do you suggest? params to the classify function?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/467/_/diff#comment-106047313"}}
{"title": "Feature/BIS-4249 update test h exact", "number": 468, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/468", "body": "Fix code quality test (piggybacking on this PR)\nUpdate h_exact test"}
{"title": "Interface change: Add connectivity status data to PacketMetadata for future use", "number": 469, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/469", "body": ""}
{"comment": {"body": "Might have been a bit better having a function create that default metadata so this doesn\u2019t have to be repeated in the future the next time a new field is added", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/469/_/diff#comment-106065597"}}
{"comment": {"body": "what\u2019s the use case of connectivity status? what does it represent? Can it be reduced to uint8?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/469/_/diff#comment-106069684"}}
{"comment": {"body": "It will be used to pass the information about the wifi/other connectivities of the phone. 8 bits might be enough but I don\u2019t want to limit myself.  \nIs the additional 4 bytes are an issue?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/469/_/diff#comment-106093188"}}
{"comment": {"body": "I see.\n\nNot right now.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/469/_/diff#comment-106094806"}}
{"title": "fix fields order in ble beacons", "number": 47, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/47", "body": ""}
{"title": "Feature/monitor fixes", "number": 470, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/470", "body": "Fixed monitor bugs\nFixed more monitor bugs"}
{"comment": {"body": "Funny :slight_smile: , did it affect only on the Monitor?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/470/_/diff#comment-106065865"}}
{"comment": {"body": "`return len(events)`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/470/_/diff#comment-106070374"}}
{"comment": {"body": "I added these constants for monitor use, they weren\u2019t there initially so it doesn\u2019t affect anything", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/470/_/diff#comment-106081579"}}
{"comment": {"body": "events is a generator", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/470/_/diff#comment-106082602"}}
{"title": "Raised version number, added app upload script to fdroid server", "number": 471, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/471", "body": ""}
{"title": "Added ghs compiler test", "number": 472, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/472", "body": ""}
{"comment": {"body": "maybe add compiler version test as well?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/472/_/diff#comment-106120095"}}
{"comment": {"body": "Done.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/472/_/diff#comment-106242844"}}
{"title": "Monitor now draws CFO graph", "number": 473, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/473", "body": ""}
{"title": "fixed padding", "number": 474, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/474", "body": ""}
{"title": "Pretty print diffs between models, when selecting two sequences", "number": 475, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/475", "body": ""}
{"title": "Fix hw tests", "number": 476, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/476", "body": "Fixed trigger build script to work via ssh tunnel.\nFixed hw tests.\nFixed typo which caused adv_agent not to be compiled.\nFixed system tests hang after exception occurred on the event deserialiser thread."}
{"title": "Feature/BIS-4180 fix calibration tests to have better records with temperatures", "number": 477, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477", "body": "Modify calibration tests to use better records; add some tests; remove initial more general tests"}
{"comment": {"body": "Python allows multiplying most iterables `data = raw_pack[:idx] + noise_to_duplicate * 4 + raw_pack[idx:]`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106241464"}}
{"comment": {"body": "packet['receiver_temperature'] is in celcius, right? Then you need to multiply it by 100 before inserting it into the feature extraction, since the units are decicelcious", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106241898"}}
{"comment": {"body": "Isn\u2019t that centicelsius?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106242090"}}
{"comment": {"body": "Already multiplied in db \\(pickle is created from db\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106242118"}}
{"comment": {"body": "```\n>>> bytes([1, 2, 3]) * 3\r\nb'\\x01\\x02\\x03\\x01\\x02\\x03\\x01\\x02\\x03'\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106242373"}}
{"comment": {"body": "Yes, it's centicelcius. My bad.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106242591"}}
{"comment": {"body": "`import functools`\n\nMark with `@functools.lru_cache(maxsize=None)` so this doesn\u2019t re-load the file each time \\(memoization\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106243159"}}
{"comment": {"body": "Will do in different PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106243584"}}
{"comment": {"body": "Try...except could miss potential bugs in this flow. Should not be part of a test workflow", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106243847"}}
{"comment": {"body": "Sure, missed it when pushing - removing in different PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106244059"}}
{"comment": {"body": "What's the reason this is hardcoded with index 1?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106244323"}}
{"comment": {"body": "Only used for calibrated model. I\u2019ll change to it to be a parameter.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106244627"}}
{"comment": {"body": "doesn't this always evaluate to True? `cfo_model` is not a pointer and `base_temperature_board` is a local variable.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106244774"}}
{"comment": {"body": "yes, forgot `cfo_model = get_cfo_model(lib)`, 10x", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/477/_/diff#comment-106245073"}}
{"title": "Feature/fix caching issue", "number": 478, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/478", "body": "Fixed monitor caching issue\nFixed CFO drawing when there's no model available (training)"}
{"title": "Fix the lovely PR comments", "number": 479, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/479", "body": ""}
{"title": "Feature/BIS-181 DA14683 bringup", "number": 48, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/48", "body": "updating dialog SDK to 1.0.14.1081\nremoving unused files from SDK fixing main loop project with updated files from template project in new SDK\nadded missing hook to catch BLE RX\nfixing paths in cmake generation\nfixing paths in project\nupdating prebuild step\nupdating .gitignore\nupdating .gitignore\nmore SDK residue cleanup\nreplacing prebuild step with eclipse origin\nupdated .gitignore updated project configuration to also have 83 and 81\nalso building 83 project in jenkins removing static analysis\nproject cleanup\nmodifying RAM usage flashing on target now requires user interaction cleanup\nfixing main loop compilation script\nwrong directory in previous commit\nimproving jenkins to not store unncessarry artifacts\nupdating .gitignore"}
{"comment": {"body": "* project is updated to support DA14681 & DA14683 configurations \\(debug & release for each\\)\n* when programming target, user input is needed to select the target \\(0 for DA14681 or 1 for DA14683\\)\n* currently jenkins expects DA14681 to be connected for system tests until we change it\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/48/_/diff#comment-73454606"}}
{"title": "make sure we have more than one bin diff in board temperature", "number": 480, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/480", "body": ""}
{"title": "fix test and enable it", "number": 481, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/481", "body": "This test checks several stages. In this second stage, not enough temperatures were received so the const should not change. (The next check in the test makes sure that once enough temperatures were received the const changes)."}
{"title": "Unxfail tests", "number": 482, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/482", "body": ""}
{"comment": {"body": "Please approve", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/482/_/diff#comment-106287127"}}
{"title": "add test to check minor and major updates; refactor a bit to reuse code", "number": 483, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/483", "body": ""}
{"title": "Feature/BIS-4337 fix training progress", "number": 484, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/484", "body": "Replace progress indication"}
{"title": "Add classification state and training progress indication to monitor", "number": 485, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/485", "body": ""}
{"title": "Feature/finalize column", "number": 486, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/486", "body": "Added a finalize column to the monitor\nDPI"}
{"title": "Feature/BIS-4352 regression tests seg fault", "number": 487, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/487", "body": "Fix rev3 files reading in regression tests\nConsolidate dataset reading for regression tests for easier maintenance\nAdd optional-lite v3.2.0\nRemove 64-bit structs redundancy \nAssert rev3 dataset bug in runtime\nWait individual tests to catch segfaults in unittests"}
{"comment": {"body": "I\u2019m getting lost because a lot of code has moved around. What was the bug exactly?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/487/_/diff#comment-106451361"}}
{"comment": {"body": "Basically, the following fix was introduced:\n\n```\n+struct Levl_PacketMetadata_st_32_t {\n+    uint64_t rtc_timestamp;\n+    uint64_t rtc_now;\n+    int32_t phone_temperature;\n+    uint32_t connectivity_status;\n+    uint8_t anchor_id;\n+    // Padding for rev3\n+    uint8_t __padding0[3]; // Bug in rev3 datasets introduced structure mismatch. To read rev3 files, last padding needs to be removed\n```\n\n```\n+    // Padding for non-rev3\n+    // uint8_t __padding0[7];\n+} __attribute__((packed));\n```", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/487/_/diff#comment-106451857"}}
{"title": "v2.1", "number": 488, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/488", "body": ""}
{"title": "Feature/missing st artifacts", "number": 489, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/489", "body": "Add powerpc library to drop"}
{"title": "Feature/BIS-289 falling transient regression", "number": 49, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/49", "body": "falling transient: fixed assumptions about packet length\nalso building 83 project in jenkins removing static analysis\npartial revert of previous cherry pick"}
{"title": "Improved Bosch demo for v2.1", "number": 490, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/490", "body": "The Bosch demo has a few hiccups in packet processing during reception of protocol UART commands. This caused frequent timeouts and prevented it from finishing training.  \nTo overcome this Ive:\n\nIncreased the Bosch Demo timeout duration\nDecreased the query period"}
{"title": "Release/V2 1", "number": 491, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/491", "body": "Merge v2.1 into develop"}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/3492797948-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/491/_/diff#comment-106487993"}}
{"title": "Release/V2 1", "number": 492, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/492", "body": "Merge v2.1 into mater"}
{"title": "Fix timing in monitor (timing in lib still disabled)", "number": 493, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/493", "body": ""}
{"title": "Feature/debug hw tests crash", "number": 494, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/494", "body": "Unxfail tests\nFix temperature extraction test\nFix temperature\nadd test to check minor and major updates; refactor a bit to reuse code\nSkip extract phone temperature\nAdded a finalize column to the monitor\nDPI\nAdd classification state and training progress indication to monitor\nDisplay relative timestamp\nConsoldate class/train state column Fix timestmap presentation bug\nv2.1\nAdd powerpc library to drop\nSpaces\nImproved Bosch demo for v2.1\nWill now save the result of the pickles when the tests fail because of an exception\nFixed testing agent causing hw_tests to hang.\nAdded wake lock to the app.\nClassification tests will now run only with ST.\nAdded classification tests with ST\nFixed android app crashes.\nChanged hw test for preamble to require only 92% of them to be valid."}
{"comment": {"body": "Were is the change of increasing expected execution time from 60 to 65 ms?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/494/_/diff#comment-107100794"}}
{"title": "Feature/wifi stress", "number": 495, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/495", "body": "App supports running downloads, WiFi enabled by default\nAdded stress server\nImproved Android app downloading\nImproved stress scripts\nAdded WiFi stress indication to advertisment packets"}
{"comment": {"body": "I accidentally ran the Android Studio code formatter on `MainActivity.java` so there\u2019s a lot of whitespace diff. Try to ignore it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/495/_/diff#comment-106981652"}}
{"title": "Feature/BIS-4415 enable timing and adjust to 2 b", "number": 496, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496", "body": "Shorten TID to 5 bytes\nAdd beacon stream id to tid app\nEnable Timing feature\nModify Timing to handle 2 beacon streams\nFix Timing aging bug to use rtc_now only for aging"}
{"comment": {"body": "Is the index now the whole byte?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107611027"}}
{"comment": {"body": "TID was shortened to 5 bytes?\n\nDid we discuss Bosch about this?\n\nAnyway, this kind of change is worth notifying the whole team about.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107619778"}}
{"comment": {"body": "This was decided by Michael, discussed with him, Omer and Igal. When I push the code I will send an email to all of course, but the code is not yet done.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107621161"}}
{"comment": {"body": "Maybe turn this into a `FEATURE_MASK_NONE` so it's more clear what `0` means?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107912171"}}
{"comment": {"body": "Would be really interesting to reuse existing variable instead of creating a new one since it uses just 1 bit, for example, `levl_packet_id_t`'s anchor\\_id, which has many unused bits.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107939854"}}
{"comment": {"body": "Could you elaborate on this selection? When should beacon\\_id taken from feature extraction instead of from captured packet?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107943339"}}
{"comment": {"body": "I think it would make the code less clear. I agree that it would save memory but as long as we don\u2019t have to I think we should avoid it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107945955"}}
{"comment": {"body": "I understand where this is coming from, the disable\\_feature\\_mask field is not entirely intuitive to understand, but I don\u2019t like this added constant.  \nI\u2019ll add a comment about this field where it\u2019s defined explaining its meaning.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107982437"}}
{"comment": {"body": "For usages with TID - yes. For recordings \\(no TID\\), left the code as it was \\(gets a nibble\\).  \nWe now have another 5 bytes \\(beside the beacon index\\) that are available for further data setting if we need, so I didn\u2019t bother using a mask etc'.  \nIn the future if we\u2019re short in space we can add a mask for it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107985359"}}
{"comment": {"body": "Is all this really necessary just to test this? Wouldn\u2019t it better to call classify\\_validate directly? This test seems to be dealing with a lot of irrelevant stuff", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107986902"}}
{"comment": {"body": "Ok so the beacon\\_id arrives in the captured packet, and then the fingerprinting lib puts it into the internal feature structure for use in the training and classification.\n\nTheoretically, the monitor could just use the beacon\\_id in captured packet, since it always has it. That was what I did initially.  \nBUT - I had a bug where the feature did not get filled properly with this value, and the C code results showed a bad model because of it, while the python code showed the proper beacon\\_id since it didn\u2019t look at the feature.\n\nSo, I modified the python to use the feature structure which is more indicative. Sometimes however we get Captured Packet and we don\u2019t have a feature yet - in such cases I took the data from the Captured Packet.\n\nmmm maybe I can set the beacon to N/A in such a case for clarity. I\u2019ll see if it\u2019s not too complicated to modify the code to support it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107988212"}}
{"comment": {"body": "Well, I just did the same as the rest of tests in this file, which is to use the external API to test this case. IMO it\u2019s better than a unittest because it tests the external API as it\u2019s used by the user. This ERROR value is part of the external API.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107990043"}}
{"comment": {"body": "I agree with @{5b41d9de10d57114135eca66} , simply take it from `self.captured`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-107999993"}}
{"comment": {"body": "I agree that it\u2019s always better to test the entire flow but unit-tests should focus on small things to keep them simple. In any-case, if the rest of the file looks like this anyway I guess it\u2019s fine\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-108000618"}}
{"comment": {"body": "This is what I did initially, and it missed a bug in the embedded code. I added a comment in the code to clarify the use.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-108001093"}}
{"comment": {"body": "I see it more as a test to the API than a functional Unittest anyway.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-108001380"}}
{"comment": {"body": "I think it\u2019s also worth providing regression tests with 2 beacons.  \nNothing is actually testing timing feature with 2 beacons.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-108083995"}}
{"comment": {"body": "There\u2019s a system test with 2 beacons - test\\_beacon\\_streams.py", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-108106619"}}
{"comment": {"body": "Actually, you\u2019re right. Beside this system test \\(that only checks Training\\) we have nothing to test the whole system with 2 beacons. I\u2019ll add a test.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-108109351"}}
{"comment": {"body": "Looks great! Awesome work!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/496/_/diff#comment-108155836"}}
{"title": "Feature/BIS-4292 unstable matrix inversion", "number": 497, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/497", "body": "Reason to make this change to have the variables of the CFO model have about the same scale of values; we had bias (constant 1*10^0), board temperature (~2*10^1), device temperature (~2*10^1) and channel (~2*10^3).\nThe channel is much larger than the other values, so when calculating the inverse of the linear regress matrix, the values are not stable - small change in original matrix might cause large changes in the inverse.\nChanging the channel to be ~2*10^1 would make more stable inversions.\nAnother reason to do so is to decrease the change of overflows, since in every train (including online training) sequence, we add a squared variable to ever increasing sum, which would reach its overflow much faster with large channel values.\nChange log:\n\nReplace channel_mhz with channel_ghz\nFix tests to pass new variable"}
{"comment": {"body": "Is the sign change on purpose? If my understanding is correct, changing the channels from mhz to ghz should affect the intercept, but why negate it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/497/_/diff#comment-107545120"}}
{"comment": {"body": "That\u2019s to make the tests pass. The logic is valid anyway.  \nWith single precision, it\u2019s too susceptible to small changes in values.. With intercept at 10k, the error was 1.5% which is higher than 0.5% allowed.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/497/_/diff#comment-107546037"}}
{"title": "Changed tests thresholds", "number": 498, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/498", "body": "test_QT_FEATURE_002_preamble_length\nLowered the percent required from 95% to 85%.\ntest_QT_FEATURE_003_falling_transient_std\nSTD now has to be lower than 5 instead of 4."}
{"title": "IQ Recording agent with watchdog to prevent freezes during recording", "number": 499, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/499", "body": "@{5b41d9de10d57114135eca66} has better memory than me"}
{"comment": {"body": "A common scenario is to have the dialog-capture setting up the board \\(IMEI filter, what not\\) before using the phone app to advertise. Then the watchdog would be triggered for no reason, then the set up would be deleted.\n\nCan you propose a better handling to this situation?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/499/_/diff#comment-108124119"}}
{"comment": {"body": "It raises a more difficult question: The filters are destroyed when the watchdog is triggered. How do we keep them around after a reset?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/499/_/diff#comment-108124717"}}
{"comment": {"body": "BootEvent\u2122?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/499/_/diff#comment-108124990"}}
{"comment": {"body": "Events? In iq\\_recording\\_agent? Have you lost your mind?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/499/_/diff#comment-108125307"}}
{"comment": {"body": "Beautiful solutions to our problems! Great work!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/499/_/diff#comment-108155598"}}
{"title": "Feature/FIN-263 Transient extraction", "number": 5, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/5", "body": "*creating stub of falling transient\n*starting to implement helper functions for falling transient\n*fixing feature extraction cmakelist\nadding fast math library with fast sqrt added q31 statistics library with mean, var and std *starting to add falling transient\nfix warning\nadding more fast math and q31 functions updating statistics API *adding \"extra macros\" file, until a better name is found\n*naive transient detection implementation finished. requires testing\n\nbugfix in transient extraction\n\nadded input valiation in transient extraction\nadded unittest to transient extraction\n\n\n\naccidentally implement rising transient extraction so refactored to support both rising and falling based on API input added simple tests\n\n*renaming to transient extraction\n\n"}
{"title": "BIS-295 python bindings libfingerprinting", "number": 50, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/50", "body": "Added Python SWIG module (pyfingerprinting) target to CMake\n\nChanged x64 target to be position independent with -fPIC to allow dynamic linkage, may harm performance\n\n\n\nAdded example code that uses pyfingerprinting for training / classification\n\nLimited pyfingerprinting to UNIX based systems only\nAdded a convenient toggle for LEVL_fingerprinting.c printf calls\nExample script now works, added example phone recordings, train-classify correct only on Pixel2 ATM"}
{"comment": {"body": "* How does this integrate into the CI flow?\n* Putting the tests under lib\\_build seems out of place. Could you move it to a better location, such as /python\\_tools?\n* Great code and documentation!\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/50/_/diff#comment-74520178"}}
{"comment": {"body": "no way to make this platform independent?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/50/_/diff#comment-74520273"}}
{"comment": {"body": "what should this turn into if this is an example?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/50/_/diff#comment-74520423"}}
{"comment": {"body": "It\u2019s possible but it\u2019s pretty difficult - Python on Windows comes with Python dll files which must be linked with your extension module, and MinGW can\u2019t really link with DLL\u2019s directly, you have to manipulate them first\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/50/_/diff#comment-74520634"}}
{"comment": {"body": "It\u2019s an example of how to use the pyfingerprinting bindings.\n\nThere are 3 layers to Pyfingerprinting -\n\n1. \\_pyfingerprinting.so \u2190 A shared library which is a Python extension module generated and compiled by SWIG and CMAKE from our .h files  \n   It contains the actual c functions\n2.  pyfingerprinting.py \u2190 A SWIG generated thin Python wrapper around \\_pyfingerprinting.so. Very awkward and difficult to use.\n3.  to\\_be\\_determined.py \u2190 A proper, easy-to-use wrapper with helper functions that will be very similar to this example code that will allow easier access for the feature extraction/training/classification functionality provided by Pyfingerprinting\n\nto\\_be\\_determined.py still needs to be created, it will be used by the regression tests to test the libfingerprinting library", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/50/_/diff#comment-74522105"}}
{"comment": {"body": "These are not tests, just generic Python bindings for libfingerprinting. Their main use will be tests in the future.\n\nSince it\u2019s not tests, there is no reason to integrate them into the CI flow.\n\nHowever, if the generation of the bindings fails for some reason it is detected by the CI flow which tries to generate and compile the bindings.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/50/_/diff#comment-74522640"}}
{"comment": {"body": "FindPython3 was added to CMake only version 3.12.0.\n\nPlease update the required CMake version in our files.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/50/_/diff#comment-74721778"}}
{"title": "extend test", "number": 500, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/500", "body": "Extend regression test to include another phone and see classify not match."}
{"title": "Feature/BIS-4516 CFO code cleanup", "number": 501, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/501", "body": "Most of the work is prettifying and cleaning up stuff.\nPlease review and understand the new scopes of variables.\nChange log:\n\nRemove monitor_v2\nRemove min/max unused variables\nTake conf. interval params out of submodel scope into model scope\nTake meta parameters out of submodel scope into model scope"}
{"title": "Feature/BIS-4516 CFO code cleanup", "number": 502, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502", "body": "Most of the work is prettifying and cleaning up stuff.\nPlease review and understand the new scopes of variables.\nChange log:\n\nRemove monitor_v2\nRemove min/max unused variables\nTake conf. interval params out of submodel scope into model scope\nTake meta parameters out of submodel scope into model scope\nEach linear regression variable has a slope name context"}
{"comment": {"body": "Do we really want it to be part of the whole model and not part of the CFO model?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108400153"}}
{"comment": {"body": "That\u2019s the idea. These are the same values for all of the submodels.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108404795"}}
{"comment": {"body": "If these two vars are constants throughout the training and classification, maybe better to set them at configuration stage", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108531429"}}
{"comment": {"body": "Why do we need this?.. I think the `calc_specific_feature_vote` should be left with its params, creating a struct just for its params seems to give this function a special emphasis", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108532292"}}
{"comment": {"body": "Maybe a better name for const is default", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/502/_/diff#comment-108533798"}}
