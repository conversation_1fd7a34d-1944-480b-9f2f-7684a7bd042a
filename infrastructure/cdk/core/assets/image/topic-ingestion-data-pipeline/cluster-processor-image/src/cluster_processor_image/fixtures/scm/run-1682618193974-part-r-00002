{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1209#pullrequestreview-*********", "body": "Thanks"}
{"title": "Cdk update routes existing peering (VPN config setup)", "number": 121, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/121", "body": "Added a new stack to manage route setup between EKS and VPN\nExtended EKS config object to include new params needed for route setup\nModified existing NetworkStack to support both route creation with and without peering connection creation. It now can\ncreate routes for an existing peering connection (e.g a cross account one that can't be automated)\nModified dev cdk config to include new info needed for above changes\nFinally modified EKS cluster to take down public endpoint. Now EKS cluster can only be access using VPN\n\nAll above changes have been applied and work as expected.\nIMPORTANT NOTE: Continuous deployment to Kubernetes is broken until we setup OpenVPN for GitHub Actions"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/121#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/121#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/121#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/121#pullrequestreview-*********", "body": "Minor comments"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/121#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/121#pullrequestreview-862857292", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/121#pullrequestreview-862857392", "body": ""}
{"title": "Fix incorrect push channel in repo store", "number": 1210, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1210", "body": "@jeffrey-ng @kaych either of you know what's up with this?  I'm guessing this was temporary while the repo channel didn't work?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1210#pullrequestreview-966725756", "body": "Most likely an artifact from when /repos didn't exist..."}
{"title": "Thread recommendation model", "number": 1211, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1211", "body": "Based on "}
{"title": "[RFC] Separate APIs for my/recommended threads", "number": 1212, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1212"}
{"comment": {"body": "As discussed earlier, we'll need to add a \"get fat threads by ID\" endpoint.\r\n\r\nAlso, I think we will need a separate \"get archived fat threads\" endpoint for this UI:\r\n\r\n<img width=\"1138\" alt=\"Screen Shot 2022-05-11 at 1 59 36 PM\" src=\"https://user-images.githubusercontent.com/2133518/167946650-5f07ba24-261a-4198-94d8-12d9988eb46d.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1212#issuecomment-1124290097"}}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/1271", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1212#issuecomment-1125476984"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1212#pullrequestreview-966611713", "body": ""}
{"comment": {"body": "Response content is missing recommendation rank and reason. Will add later if this direction makes sense.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1212#discussion_r868263640"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1212#pullrequestreview-966820406", "body": ""}
{"comment": {"body": "What other parameters do we need to add here?  `limit`?  Do we want to make `sort` explicit?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1212#discussion_r868450376"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1212#pullrequestreview-966827730", "body": ""}
{"comment": {"body": "Add `limit` and `cursor`. Ordering will be hard coded.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1212#discussion_r868457720"}}
{"title": "Move away from aws postgres library", "number": 1213, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1213", "body": "Sadly, AWS is not keeping this library up to date and its against older versions of postgres.\nIt has interesting failover mechanics that deal well with Aurora, but we need a library that is not stagnant atm.\nWhen AWS does decide to fully support their jdbc, then we'll move to it.\nAlso upping kotlin versions."}
{"title": "Fix join used for re-indexing", "number": 1214, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1214", "body": "The old query was joining sourcemark with message in addition to thread. We just want it to join on thread only\nOld:\nSELECT threadmodel.id, messagemodel.id \nFROM threadmodel \nINNER JOIN messagemodel ON threadmodel.id = messagemodel.thread \nINNER JOIN sourcemarkmodel ON threadmodel.id = sourcemarkmodel.thread AND messagemodel.id = sourcemarkmodel.message \nWHERE (sourcemarkmodel.repo = 'fb06ca32-cdf0-4bcc-9e08-c8c675768235') AND (sourcemarkmodel.\"isAnchor\" = TRUE)\nNew:\nSELECT threadmodel.id, messagemodel.id \nFROM threadmodel \nINNER JOIN messagemodel ON threadmodel.id = messagemodel.thread \nINNER JOIN sourcemarkmodel ON threadmodel.id = sourcemarkmodel.thread \nWHERE (sourcemarkmodel.repo = 'fb06ca32-cdf0-4bcc-9e08-c8c675768235') AND (sourcemarkmodel.\"isAnchor\" = TRUE)"}
{"title": "Gradle files referencing stale versions", "number": 1215, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1215", "body": "Clean up gradle files."}
{"title": "Updated Input", "number": 1216, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216", "body": "Added some functionality to Input component to handle icons and actions. Primarily doing this as setup for search input.\nStyling is most likely a WIP with padding and multiple action/icon states.\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966918149", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966918898", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966919541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966920461", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966921377", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966922038", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966923970", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966927687", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966931471", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-966934878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-967020538", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-968034104", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-968034485", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-968035666", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-968044708", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-968057116", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969817860", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969818460", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969819033", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969820152", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969823051", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969829465", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969837047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969837954", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969934908", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969941298", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969942644", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969943079", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969945277", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969947396", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969947874", "body": "Looking good once the remaining feedback is looked at..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-969964476", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-970008529", "body": ""}
{"comment": {"body": "We're not going to auto-search with debouncing anymore?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#discussion_r870759586"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#pullrequestreview-970023181", "body": ""}
{"comment": {"body": "Nope. Based on chat with Ben and David.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1216#discussion_r870770571"}}
{"title": "Make sure we format before we lint in parallel", "number": 1217, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1217"}
{"title": "Move to coroutine friendly client", "number": 1218, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1218"}
{"title": "Add author to search vector", "number": 1219, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1219", "body": "First pass at adding author to search. This is kind of hacky but works. It will have to change when we implement ranking search results which I will be tackling next."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1219#pullrequestreview-966846394", "body": "cool"}
{"comment": {"body": "curious how are emails tokenized?\r\n\r\nif it separates an email such as `username@domain` into `[\"username\", \"domain\"]`, then I'd be concerned about the domain part spamming results. For example if you type \"gmail\" you get every message in the database. Might need to apply some stop words at some point in the future.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1219#discussion_r868476813"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1219#pullrequestreview-966849424", "body": ""}
{"comment": {"body": "it leaves `username@domain` unseparated, so searching for `<EMAIL>` gets results but searching `getunblocked.com` or `getunblocked` returns nothing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1219#discussion_r868479987"}}
{"title": "Add chat and message endpoint stubs", "number": 122, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122", "body": "Building off of https://github.com/Chapter2Inc/codeswell/pull/120.\nDefinitely not final, but just want to put something up to start wiring things together. This can/will change based on what clients need."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862606458", "body": "some of my comments could be done in follow up"}
{"comment": {"body": "add title", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r791938298"}}
{"comment": {"body": "add body", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r791938511"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862611311", "body": ""}
{"comment": {"body": "going to do this in a follow up", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r791941895"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862611403", "body": ""}
{"comment": {"body": "going to do this in a follow up", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r791941927"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862630590", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862632663", "body": ""}
{"comment": {"body": "I'm not sure I understand this method, wouldn't this return all chats for all annotations?  Should there be a query parameter on here to ask for chats associated with an annotation?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r791957175"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862677363", "body": ""}
{"comment": {"body": "Just to be clear: `annotation` == source mark?\r\n\r\nYeah right now it returns all chats but it doesn't have to, we can scope it down to require a source mark id. Although this unstopped version could be used populate the list of chats in the plugin? \r\n\r\n<img width=\"369\" alt=\"CleanShot 2022-01-25 at 10 01 32@2x\" src=\"https://user-images.githubusercontent.com/1924615/151033298-35b65cad-6a2a-449c-8750-e9c2556b64e9.png\">\r\n\r\nThough maybe for this use case we should have other query params.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r791989650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862679755", "body": ""}
{"comment": {"body": "But for sure there should be a `sourceMarkId` query param, I just haven't implemented it yet :).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r791991276"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862686375", "body": ""}
{"comment": {"body": "> Just to be clear: `annotation` == source mark?\r\n> \r\n> Yeah right now it returns all chats but it doesn't have to, we can scope it down to require a source mark id. Although this unstopped version could be used populate the list of chats in the plugin?\r\n> \r\n> <img alt=\"CleanShot 2022-01-25 at 10 01 32@2x\" width=\"369\" src=\"https://user-images.githubusercontent.com/1924615/151033298-35b65cad-6a2a-449c-8750-e9c2556b64e9.png\">\r\n> \r\n> Though maybe for this use case we should have other query params.\r\n\r\nThis is exactly the point Matt was making about the naming confusion with @richiebres in another thread.\r\nSoureMark == Annotaiton.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r791994803"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862702207", "body": ""}
{"comment": {"body": "Ah sorry missed that last comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r792006876"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862764860", "body": ""}
{"comment": {"body": "> Just to be clear: `annotation` == source mark?\r\n\r\nYeah\r\n\r\n> Yeah right now it returns all chats but it doesn't have to, we can scope it down to require a source mark id. Although this unstopped version could be used populate the list of chats in the plugin?\r\n\r\nAh OK -- I think in that case the thing we're displaying here is the SourceMarks/Annotations, not the Chats -- I believe we only fetch the chats on a per-sourcemark-basis, when we're viewing that individual sourcemark.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r792049619"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862771151", "body": ""}
{"comment": {"body": "Interesting, then is the chat title a property of the sourcemark/annotation? Or is title a property of the chat and the client fetches all chats for all source marks in that list?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r792052872"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862775292", "body": ""}
{"comment": {"body": "I think the former, sourcemark/annotation has a title, and that's what we display here...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r792054801"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862783315", "body": ""}
{"comment": {"body": "~https://github.com/Chapter2Inc/codeswell/pull/120 implies there there can be many chats for a source mark/annotation. @richiebres thoughts?~ EDIT: Just chatted with Richie IRL, there is a one-to-one relationship between sourcemark/annotation and chat\r\n\r\n~If title is on source mark/annotation, then the chat model is not very useful (messages should just refer directly to the annotation).~ EDIT: Just chatted with Richie IRL, we want to keep separate in case we ever have non-chat things associated with source marks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r792060333"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862829047", "body": ""}
{"comment": {"body": "> I think the former, sourcemark/annotation has a title, and that's what we display here...\r\n\r\n@matthewjamesadam SourceMarks should not have any content. It's purely for tracking the \"annotation\" through Git revision history. This isolation is intentional.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r792093074"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-862837138", "body": ""}
{"comment": {"body": "> @matthewjamesadam SourceMarks should not have any content. It's purely for tracking the \"annotation\" through Git revision history. This isolation is intentional.\r\n\r\nI agree with this, that's largely where my comment here came from: https://github.com/Chapter2Inc/codeswell/pull/120#issuecomment-1021436088\r\n\r\nI think the top-level object should be a container that logically contains chat(s), source marks, and whatever else might be associated with a particular bit of knowledge or discussion.  I would imagine that top-level thing does need a title or topic to describe what it's for.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r792098606"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-864358513", "body": ""}
{"comment": {"body": "https://github.com/Chapter2Inc/codeswell/pull/145", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r793239348"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#pullrequestreview-864362271", "body": ""}
{"comment": {"body": "https://github.com/Chapter2Inc/codeswell/pull/147", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/122#discussion_r793242329"}}
{"title": "More visual fixes", "number": 1220, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1003026096", "body": ""}
{"comment": {"body": "ping\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=64db71b2-1ff5-42f4-ba72-3741dec5a808).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r894689788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1003029484", "body": ""}
{"comment": {"body": "Testing PR title\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=b0b31ad4-969d-449f-aa9c-d62c0b6c1a40).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r894692160"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1003109703", "body": ""}
{"comment": {"body": "test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=41a83b07-6877-46c4-b71a-3052b75a7745).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r894749665"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1016305968", "body": ""}
{"comment": {"body": "hi\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=3b1b6f48-5e90-4302-975b-c006a41e21bb).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r904498079"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1017568318", "body": ""}
{"comment": {"body": "post\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/5a0c3a4d-51ca-40de-8860-a29761f0d682?message=60fe6d23-b5cd-4a16-a889-ad42260e4563).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r905405703"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1017608978", "body": ""}
{"comment": {"body": "thwack\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=76251905-f1fa-4ccd-be4d-d64d2b3f797a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r905434381"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1022337386", "body": ""}
{"comment": {"body": "whabam!\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=f8c891eb-2602-4034-a737-8658a39255fd).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r908894554"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1022340266", "body": ""}
{"comment": {"body": "pow!\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=c1c489d6-c1dc-4036-be75-94734e2aa607).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r908895642"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1023981507", "body": ""}
{"comment": {"body": "will this work\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=8dd5a291-c608-4e6c-8109-122bc31368db).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910391546"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1023983413", "body": ""}
{"comment": {"body": "woops\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=18158091-5e15-43a3-bc49-ab9095453ca2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910392950"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1023987737", "body": ""}
{"comment": {"body": "liuhliuh\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=a512b99a-6b02-482e-a1c4-75a179452e76).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910396055"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1023988143", "body": ""}
{"comment": {"body": "my turn\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=b3fe6cce-6d5e-4955-9e1f-9909b45ff999).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910396317"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024061628", "body": ""}
{"comment": {"body": "ping\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=f0fc6e61-ca8d-409d-940b-f56b750d674b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910449774"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024061911", "body": ""}
{"comment": {"body": "pong\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=e3aac91c-a51d-42bc-9d8b-7514f464f15a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910450004"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024063676", "body": ""}
{"comment": {"body": "biff\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=abdfb5d7-6e55-48c7-8ba0-9940aeb5d765).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910451303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024067324", "body": ""}
{"comment": {"body": "baff\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=6d8cdfbc-42cc-4fe0-8826-af132cc250c5).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910453979"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024067640", "body": ""}
{"comment": {"body": "asdfasdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=6a825bbb-9e9d-4f46-a6b0-1ef40894cd07).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910454201"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024068616", "body": ""}
{"comment": {"body": "asdfasdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=bf2036b6-5c86-4823-8937-737dbeb0cff8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910454944"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024068987", "body": ""}
{"comment": {"body": "asdfasdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=0d604dec-889a-470d-bce5-0b6dff82d177).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910455256"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024070417", "body": ""}
{"comment": {"body": "asdfaf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=24258358-0e23-40f6-8cd6-31c879aa59ee).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910456355"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024070603", "body": ""}
{"comment": {"body": "asdfas\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=80b9abcc-3b9a-496e-936c-11f0b9c28eaa).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910456489"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024071096", "body": ""}
{"comment": {"body": "asdfa\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=beb1b21f-026d-4f6d-af22-3ae05c2902e3).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910456863"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1024071363", "body": ""}
{"comment": {"body": "asdfas\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=065c2b20-b421-4108-861a-7f85d6808121).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r910457069"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1029452005", "body": ""}
{"comment": {"body": "fasdfasd\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=b3a243d1-5741-4b03-8538-a17e7c782192).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r914383824"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-1029521889", "body": ""}
{"comment": {"body": "hi\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1cf2eb87-09ab-4447-8c6a-cc0cce6cb773?message=21b0be4b-7c2d-4194-9358-c41aed5ea3f7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r914435209"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-966880373", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-966880804", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-967043183", "body": ""}
{"comment": {"body": "This diff is a mess. I don't know what git did here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r868689648"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968008058", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869391345"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968009148", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869392510"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968009691", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869392964"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968068662", "body": ""}
{"comment": {"body": "boom\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869440728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968068989", "body": ""}
{"comment": {"body": "hi\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869440997"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968156904", "body": ""}
{"comment": {"body": "hi\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869501563"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968164098", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869505344"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968169901", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869511401"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968187094", "body": ""}
{"comment": {"body": "sup\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869523638"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968193087", "body": ""}
{"comment": {"body": "weird\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869528031"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968194888", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869529304"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968195697", "body": ""}
{"comment": {"body": "hi\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869529880"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968199150", "body": ""}
{"comment": {"body": "poke\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869532431"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968201160", "body": ""}
{"comment": {"body": "kuhl\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869533803"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968201326", "body": ""}
{"comment": {"body": "jh\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869533945"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968204324", "body": ""}
{"comment": {"body": "asdfasdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869536038"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968205618", "body": ""}
{"comment": {"body": "fetch\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869536895"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968215106", "body": ""}
{"comment": {"body": "sdfsd\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869543520"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968217753", "body": ""}
{"comment": {"body": "asdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869545484"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968218599", "body": ""}
{"comment": {"body": "critical alert\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869546131"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968218778", "body": ""}
{"comment": {"body": "asdfs\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869546247"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968223319", "body": ""}
{"comment": {"body": "asdb\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869549450"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968235259", "body": ""}
{"comment": {"body": "asdfa\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869557543"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968251519", "body": ""}
{"comment": {"body": "sfds\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869567118"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968256541", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869572281"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-968302657", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r869605712"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-970124273", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r870848585"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-971435036", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r871792904"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-971435658", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r871793343"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-971435724", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r871793394"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-971435821", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r871793462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-972499167", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r872570356"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-972500525", "body": ""}
{"comment": {"body": "hi\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r872571305"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-972500946", "body": ""}
{"comment": {"body": "mnmn\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r872571543"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-978992880", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877351434"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-978994245", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877352661"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-978997234", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877354148"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-978999935", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877355581"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979005237", "body": ""}
{"comment": {"body": "asdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877360147"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979016487", "body": ""}
{"comment": {"body": "asdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877368932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979023415", "body": ""}
{"comment": {"body": "gasdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877374019"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979024983", "body": ""}
{"comment": {"body": "asdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877375107"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979025292", "body": ""}
{"comment": {"body": "fdsa\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877375305"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979158449", "body": ""}
{"comment": {"body": "asdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877466931"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979158833", "body": ""}
{"comment": {"body": "asdfa\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877467111"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979163090", "body": ""}
{"comment": {"body": "afsdfa\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877470123"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979168383", "body": ""}
{"comment": {"body": "laisuhdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877474049"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979168575", "body": ""}
{"comment": {"body": "iaushf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877474176"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979169779", "body": ""}
{"comment": {"body": "joijoij\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877474829"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979171293", "body": ""}
{"comment": {"body": "hihihi\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877475856"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979172672", "body": ""}
{"comment": {"body": "asdgf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877476778"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979178148", "body": ""}
{"comment": {"body": "go!\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877480548"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-979180680", "body": ""}
{"comment": {"body": "again!\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r877482328"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-986525254", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r882887115"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-986553041", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r882905568"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-986553232", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r882905708"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-986554003", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r882906273"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-986558638", "body": ""}
{"comment": {"body": "hi\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r882909932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-986709246", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r883018597"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#pullrequestreview-986730113", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1220#discussion_r883034255"}}
{"title": "Touch thread when message is edited to force thread refresh", "number": 1221, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1221", "body": "Item #4 from this list:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1221#pullrequestreview-966858665", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1221#pullrequestreview-966860076", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1221#pullrequestreview-966860437", "body": ""}
{"title": "VScode search", "number": 1223, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223", "body": "Adds search capabilities to VSCode.\nUsing existing TreeView architecture which requires a root node. Will look into cleaning this up in subsequent PR as it may require refactoring the TreeViews.\nSearch command currently works by opening the Unblocked sidebar and is dependant on the input's autofocus to gain focus. \nTODO: \n* Explicitly add logic to force focus onto input if autofocus fails (e.g. if unblocked sidebar is already open, command will not focus search bar) https://github.com/NextChapterSoftware/unblocked/issues/1224\n* When opening a thread from the search results, it may re-open the non-search sidebar due to how focus states are generated right now. Needs investigation. https://github.com/NextChapterSoftware/unblocked/issues/1225\nhttps://user-images.githubusercontent.com/1553313/167506609-79261172-545b-4da5-a22f-facdbc00d4af.mp4\nStacked on https://github.com/NextChapterSoftware/unblocked/pull/1192.\nInput UI will be updated once https://github.com/NextChapterSoftware/unblocked/pull/1216 is in."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#pullrequestreview-969672297", "body": ""}
{"comment": {"body": "If this is always enabled should we even bother with the context ceremony?  Do we think we *won't* enable it any point?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#discussion_r870521614"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#pullrequestreview-969715079", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#pullrequestreview-969718056", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#pullrequestreview-969718848", "body": ""}
{"comment": {"body": "We don't want it enabled when the user is *not* logged in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#discussion_r870554826"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#pullrequestreview-969722073", "body": ""}
{"comment": {"body": "Another question is, is this the right place to manage the Repo muxing?  We have the same issue with Threads, but we made a separate ThreadStore that managed muxing the Repos for each client.  Not sure which path is better TBH.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#discussion_r870557067"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#pullrequestreview-969725530", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#pullrequestreview-969789457", "body": ""}
{"comment": {"body": "That's a good point. I think it's a general thing where we could abstract repo *out* of all these VCs and into the store.\r\n\r\nWe'll most likely be refactoring this with fat thread models so will do so then for all clients.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1223#discussion_r870602338"}}
{"title": "Leverage AWS template API for emails", "number": 1226, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966889359", "body": ""}
{"comment": {"body": "did you try Kotlin HTML templates? seems a lot safer as is deals with closing tags, whitespace correctness, and enforces allowed tags (eg: prevents putting `ul` inside a `tr`)\r\n\r\neg:\r\n```kt\r\ndiv {\r\n  table {\r\n    tr {\r\n      td { ... }\r\n    }\r\n  }\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#discussion_r868529294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966890988", "body": ""}
{"comment": {"body": "I did look at it and it's a possible solution but my difficulty was mostly with inline css and conditionals in the html etc. It felt like the AWS template approach is an easier and cleaner way to handle it. \r\n\r\nI do see the downside of it however, maintaining a theme and making centralized changes. \r\n\r\nMaybe down the road we could generate these templates from Kotlin DSL ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#discussion_r868531602"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966891994", "body": ""}
{"comment": {"body": "Wouldn't this break the existing send() function ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#discussion_r868532926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966893745", "body": ""}
{"comment": {"body": "Ah nevermind. I see you have modified the tests. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#discussion_r868535527"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966893904", "body": "Looks good to me."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966907107", "body": ""}
{"comment": {"body": "My main concern was whether the kotlin templating supports all the weird email-specific attributes that we might need for client support. Maybe that's not a huge issue(?) but I also figured we could always iterate and layer on the kotlin templating if we wanted to in the future, it was easier to start off with something familiar like plain HTML ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#discussion_r868554214"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966908588", "body": ""}
{"comment": {"body": "Ideally, we go from a templated email framework, (mjml, parcel, ...) and have it compile into raw HTML.\r\n\r\nThe tooling for those should help quite a bit.\r\n\r\nhttps://parcel.io\r\nhttps://documentation.mjml.io", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#discussion_r868556613"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966918474", "body": ""}
{"comment": {"body": "> My main concern was whether the kotlin templating supports all the weird email-specific attributes that we might need for client support\r\n\r\nit supports any attributes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#discussion_r868572830"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966919129", "body": ""}
{"comment": {"body": "> conditionals in the html \r\n\r\nconditionals, or programmatic templated text is probably the main reason for using KT since it's a full modern programming language", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#discussion_r868574036"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#pullrequestreview-966999930", "body": ""}
{"comment": {"body": "> Ideally, we go from a templated email framework, (mjml, parcel, ...) and have it compile into raw HTML.\r\n> \r\n> The tooling for those should help quite a bit.\r\n> \r\n> https://parcel.io https://documentation.mjml.io\r\n\r\nparcel looks really cool, going to look into this a bit more. would be great to use their interface if it helps take away from the pain of building and testing cross-client compatible emails ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1226#discussion_r868622185"}}
{"title": "[Team Isolation] Scope thread, message, and repo find operations to a specific team", "number": 1227, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1227", "body": "For the team isolation effort, I'm going to start by adding simple team filters, so that we have something working even if it's not the best. I'll then start investigating row-level isolation.\nThis PR adds team filters for the find operations called by the API service."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1227#pullrequestreview-967197414", "body": "fun"}
{"title": "Fix VSCode rerender issue", "number": 1228, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1228", "body": "PROBLEM:\n\nWebview controller is sending the rerender event before the RenderWebview has registered its event listener.\nProposal is to try rerendering on load. Should be no downside as the code is guarding for props.\nSidebar still works since the Auth event comes in periodically but depending on this causes for a sluggish UI."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1228#pullrequestreview-966915361", "body": ""}
{"title": "Fix prod asset links", "number": 1229, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1229", "body": "Trailing slash broke parsing on cloudfront lambda edge."}
{"title": "Idempotent API Resource Creation", "number": 123, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/123", "body": ""}
{"comment": {"body": "Whenever we add API spec validation tests, we should also add tests that post/patch are disallowed in the specs...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/123#issuecomment-1021533366"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/123#pullrequestreview-862753398", "body": ""}
{"title": "FixGithubActions", "number": 1230, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1230", "body": "Deploy on sharedConfigs changes\nUpdate"}
{"title": "Update cloudfront tests and make more fault tolerant:", "number": 1231, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1231", "body": "This pr does several things:\n1. Cleans up empty array elements from splitting path components.\n2. Makes url parsing a little more fault tolerant such that it handles double slashes.\n3. Fixes up tests (godmode api etc.)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1231#pullrequestreview-967060053", "body": ""}
{"title": "Fix pretty", "number": 1232, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1232"}
{"title": "Allow service to use more memory", "number": 1233, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1233"}
{"title": "Prevent duplicate notifications", "number": 1235, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1235", "body": "Collects all the push handlers in a set and only fires them once per channel notification. Previous implementation was firing fetch twice because were listening on both threads and unreads, but only a single fetch is necessary"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1235#pullrequestreview-968257543", "body": ""}
{"title": "Optimize initial sourcemark calculation", "number": 1236, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236", "body": "Improvements\n- initial full recalculation runs in background.\n- introduce initial partial recalculation that calculates point for sourcemarks of tree-view\n  threads only. block getting source marks for file on the partial recalculation only.  partial\n  recalculation triggered on initial thread store load.\n- avoid inadvertently rerunning initial full recalculation\nFuture improvements\n\n\n[ ] get original source point from FatThreads\n[ ] create sourcemark calculator much earlier in the flow, basically as soon as we have resolved the repo\n[ ] triggers an update of open source editors on full recalculation completion."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236#pullrequestreview-968502854", "body": ""}
{"comment": {"body": "fixes race here. we were creating two SourceMarkCalculators (and rerunning the entire calculation in duplicate and duplicating storage) in some cases.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236#discussion_r869746259"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236#pullrequestreview-968503831", "body": ""}
{"comment": {"body": "This is the main change. Instead of running (and blocking on) recalculation for 1000 sourcemarks, now we do so for just 50 of them. So 20x faster here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236#discussion_r869747022"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236#pullrequestreview-968541130", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236#pullrequestreview-969668651", "body": ""}
{"comment": {"body": "Looks good -- I am working on removing ThreadStore, and this brings up an associated question: to what extent does the SourceMark engine use the ThreadStore?\n\n- (in this PR) to understand the \"high priority\" threads\n- ? Any other reason?\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236#discussion_r870518983"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236#pullrequestreview-969731505", "body": ""}
{"comment": {"body": "@matthewjamesadam the only reason I can think of this use case, where we want to trigger partial recalculation on tree view item as soon as the treeview data has been fetched.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1236#discussion_r870563433"}}
{"title": "Add support for template CRUD", "number": 1237, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237", "body": "Extended SES provider to support template List,Create,Update and Delete\nAdded another application plugin to create/update templates on service startup. It will also take care of removing orphaned templates\nAdded tests for the new functionality\nModified existing email tests to use a dynamically created template\nAdded two basic template files\nRemoved DB init from notification service startup (we don't care about db here)\n\nNote on the EmailTemplate class: \nThe built-in Template class provided by AWS SDK was not serializable by Kotlin. Instead of adding more serializers I just created a wrapper class to make our life easier. This class can later be extended to generate/ingest templates as well."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237#pullrequestreview-**********", "body": ""}
{"comment": {"body": "@rasharab does this sound right?\n\n\n\n@kaych\n\n\n\n@mahdi-torabi\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/056da2a8-c91d-4434-87ab-043cbe573bd8?message=5d774cc6-9dd9-4a19-aea2-03107504bf25).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237#discussion_r908873913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237#pullrequestreview-968450629", "body": ""}
{"comment": {"body": "This is because the AWS SDK provided Template class is not Kotlin serializable. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237#discussion_r869709043"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237#pullrequestreview-968458434", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237#pullrequestreview-968459148", "body": ""}
{"comment": {"body": "This is junit4 code.\r\nUse the below..\r\n\r\nimport org.junit.jupiter.api.MethodOrderer;\r\nimport org.junit.jupiter.api.TestMethodOrder;\r\n        \r\n@TestMethodOrder(MethodOrderer.MethodName.class)\r\npublic class TestClass{\r\n   //..\r\n}", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237#discussion_r869715335"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237#pullrequestreview-968460819", "body": ""}
{"comment": {"body": "I tried but I keep getting \r\n`An annotation argument must be a compile-time constant\r\nDeclarations are not allowed in this position`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1237#discussion_r869716637"}}
{"title": "Move away from junit4 annotaiton", "number": 1238, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1238"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1238#pullrequestreview-968475077", "body": ""}
{"title": "Add ranking and sorting of search results", "number": 1239, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1239", "body": "Orders thread search results based what matches the query. Results are sorted in order of:\n\nThread author\nThread reply authors\nThread title\nThread message bodies\n\nSo search results that match the thread author are ranked higher than those that match on authors of replies, and matches on the thread title are ranked higher than matches on thread message bodies."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1239#pullrequestreview-969697381", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1239#pullrequestreview-969714847", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1239#pullrequestreview-969715223", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1239#pullrequestreview-969715371", "body": ""}
{"title": "Move Deployments to using OpenVPN", "number": 124, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/124", "body": "Make sure we use openvpn to allow for kube helm chart deployment."}
{"title": "grant notification service access to manage SES templates", "number": 1240, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1240"}
{"comment": {"body": "I merged the config in a different PR. Closing this. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1240#issuecomment-1156081796"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1240#pullrequestreview-968490408", "body": ""}
{"title": "Basic web extension search", "number": 1241, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1241", "body": "Setup functional web extension search.\nStyling is a bit off as we currently do not have designs. Will revisit once designed. https://github.com/NextChapterSoftware/unblocked/issues/1242\nInput box styling waiting on https://github.com/NextChapterSoftware/unblocked/pull/1216\nTODO: useBeforeUnload with other contentPortManagers  https://github.com/NextChapterSoftware/unblocked/issues/1243\nWill revert to auto filter once https://github.com/NextChapterSoftware/unblocked/pull/1264 is in as there are some changes to the base store."}
{"comment": {"body": "\r\nhttps://user-images.githubusercontent.com/1553313/167739271-99018096-c7d1-4a7e-8a7c-06d1b18fc3fa.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1241#issuecomment-1123002194"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1241#pullrequestreview-972537643", "body": ""}
{"comment": {"body": "It might be best to treat `deps` the same as it is in useEffect and other hooks -- with useEffect, null/undefined means \"run on each render\".  For this hook, undefined means \"run once\", I could see this being confusing...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1241#discussion_r872597869"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1241#pullrequestreview-972545096", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1241#pullrequestreview-972824196", "body": ""}
{"comment": {"body": "Hmm any suggestions on how to get that behaviour?\r\n\r\nThe only thing I can think of is something like this?\r\n\r\n```\r\n    const effectDependencies = deps ? [onBeforeUnload, ...deps] : undefined;\r\n    useEffect(() => {\r\n        window.addEventListener('beforeunload', onBeforeUnload);\r\n        return () => {\r\n            window.removeEventListener('beforeunload', onBeforeUnload);\r\n        };\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, effectDependencies);\r\n    ```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1241#discussion_r872806386"}}
{"title": "Add swift grpc definition for client IPC", "number": 1244, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244", "body": "Adding swift GRPC back to the build!\nAdds CI dependency: brew install swift-protobuf grpc-swift\nThe client (VSCode) and service (Hub) interaction is as follows:\n\nVSCode initiates connection to Hub (1 for thread actions, 1 for auth). Resulting streams are bidirectional\nHub receives VSCode thread handling capabilities on thread action stream, stores for action handling\nHub pushes token updates to VSCode via the token stream, and issues action requests via the thread action stream"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-969698629", "body": ""}
{"comment": {"body": "The risk with this is that I don't think there's a way to isolate the response to a single client, so multiple clients could handle a thread click...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r870540348"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-969713566", "body": ""}
{"comment": {"body": "I'm digging on this a bit now - I assumed that each service call would map to a specific client, and so we could hold the stream connection open and use it to dispatch messages back over the stream to the right client", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r870551116"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-969742093", "body": ""}
{"comment": {"body": "I'm not sure what the Swift GRPC generated code lets you do, but for grpc itself this should in theory be possible.  You'd basically only issue the response (the TheadActionRequest) out to a single client.  The question becomes: which one?\r\n\r\nThis gets into a product decision: when you have n clients open, how do you prioritize who should open the request?  I think right now we only have the client types to switch on (vscode vs web extension, assuming we add a parameter where a client announces its type).  I think reasonable behaviour might be:\r\n\r\n* First go through all of the VSCode clients, give them a chance to handle the response.  If one of them does, we're done.\r\n* Then go through all of the web extension clients, give them a chance to handle the response.  If one of them does, we're done.\r\n* Then open in the dashboard\r\n\r\nVSCode would probably only \"handle\" the request if a workspace with the given repo is already open, for instance.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r870570202"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-969748606", "body": ""}
{"comment": {"body": "I think the idea is that the web extension never connects to the Hub, so we're only selecting from a list of VSCode clients, else send to the Dashboard. The Dashboard would then be responsible for redirecting to GitHub based on the availability of the extension. \r\n\r\nVSCode selection is done based on the first available client capable of handling the request.\r\n\r\nDo I have this right @jeffrey-ng?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r870574042"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-969753201", "body": ""}
{"comment": {"body": "I don't see how the dashboard can tell if the web extension is installed...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r870576737"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-969756285", "body": ""}
{"comment": {"body": "Sorry I mean the dashboard domain rather than the app itself, let's just call that the \"unblocked.com\" domain - I think the idea is that the web extension runs over the unblocked domain and runs interception on those urls, otherwise the dashboard needs to be capable of dealing with it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r870578779"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-970245466", "body": ""}
{"comment": {"body": "As Peter said, we could have the web extension communicate with the dashboard & redirect to GH when certain paths are visited.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r870943308"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-970248958", "body": ""}
{"comment": {"body": "VSCode will \"register\" with the hub app on load. This registration will pass the set of repos that it has currently open and can open discussions for.\r\n\r\nThe hub app should use this information and only enable \"open in VSCode\" if there's a client that can open the thread. If there are multiple clients that can open the same thread, we can just choose one?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r870946015"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-970251391", "body": ""}
{"comment": {"body": "I'm proposing selecting the first for now, and over time we can add some additional \"scoring\" data (perhaps sourcepoint data) that might help narrow down the ideal open workspace.\r\n\r\nIt's worth noting that the behaviour essentially boils down to: \"is there a workspace currently open that can handle this\", and **not** \"does there exist a workspace that vscode can open to handle this\", which might be a necessary secondary before giving up and opening it in the browser. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r870947877"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-971075174", "body": ""}
{"comment": {"body": "Some questions:\r\n* Does VSCode need to send the list of repos to the hub app?  Can't the hub app signal VSCode that it should *potentially* try to open a given repo, and VSCode can indicate if it opened it or not?\r\n* Can the web extension directly interact with the hub instead of us hopping through our domain?  I'm assuming not, since the web extension can't find the hub port, and probably can't communicate over localhost...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r871537208"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-971097425", "body": ""}
{"comment": {"body": "I'll answer the first one and leave the second to Jeff: it's important that the hub understand vscode's capabilities up front without having to ask at the point of action (although vscode will still send a success response when asked to open). This is because the hub may change its options depending on vscode's ability to serve a particular file. Or at least that's the current intent.\r\n\r\nIf we go the other way, where the hub always allows an \"open in vscode\" option regardless of handling capabilities (or whether it's even installed for that matter), then the experience potentially has some lag after item click (like if vscode needs to perform a network operation, or its process is busy), and we need intelligent fallbacks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r871553091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-971138567", "body": ""}
{"comment": {"body": "On the second point, we may be able to have the extension communicate with the app. There's native app communication but the way it works is slightly different for Chrome & Safari.\r\n\r\nChrome requires hard coding native app path and using a registry. https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/Native_messaging\r\n\r\nSafari requires the web extension to be bundled with Native App.\r\n\r\nIt's something we can look into in the future but I don't think it'll be feasible in a realistic timeframe... ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#discussion_r871580982"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1244#pullrequestreview-971152393", "body": ""}
{"title": "Create missing thread unreads when creating a message", "number": 1245, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1245", "body": "When we ingest PR threads, we don't create ThreadUnreadModels so that they don't appear unread in unblocked. \nWe want to start creating them when someone adds a reply in unblocked, so that thread participants see the thread is unread when someone adds a new message."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1245#pullrequestreview-968532743", "body": ""}
{"title": "The kotlin-test framework is heavily reliant on junit4, move away", "number": 1246, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1246", "body": "Was getting tired of handling naming collisions between annotations between junit4 and junit5, the latter being what we're using."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1246#pullrequestreview-968566047", "body": ""}
{"title": "Ensure we load resources correctly when bundled in final jar", "number": 1247, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1247", "body": "Resources while debugging/test have different paths compared to when running a jar from command line.\nDuring debug/tests, it uses the absolute path on the filesystem.\nWhen using bundled resource in jar, the scheme is unique:\ni.e.\njar:/templates/blah\nYou have to handle those cases distinctly.\nTESTING:\n```\nrasharab@Rashins-MacBook-Pro libs % bash ./runServiceJAR.sh -j notificationservice-1.0.0-all.jar\n21:11:23,181 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-local.xml] at [jar:file:/Users/<USER>/chapter2/unblocked/build/libs/notificationservice-1.0.0-all.jar!/logback-local.xml]\n21:11:23,185 |-INFO in ch.qos.logback.core.joran.spi.ConfigurationWatchList@40f33492 - URL [jar:file:/Users/<USER>/chapter2/unblocked/build/libs/notificationservice-1.0.0-all.jar!/logback-local.xml] is not of type file\n21:11:23,208 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - debug attribute not set\n21:11:23,208 |-INFO in ch.qos.logback.core.joran.action.ShutdownHookAction - About to instantiate shutdown hook of type [ch.qos.logback.core.hook.DelayingShutdownHook]\n21:11:23,210 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]\n21:11:23,212 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [STDOUT]\n21:11:23,215 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property\n21:11:23,238 |-INFO in ch.qos.logback.classic.joran.action.RootLoggerAction - Setting level of ROOT logger to WARN\n21:11:23,238 |-ERROR in ch.qos.logback.core.joran.action.AppenderRefAction - Could not find an appender named [LogzioLogbackAppender]. Did you define it below instead of above in the configuration file?\n21:11:23,238 |-ERROR in ch.qos.logback.core.joran.action.AppenderRefAction - See  for more details.\n21:11:23,238 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [STDOUT] to Logger[ROOT]\n21:11:23,238 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [io.ktor.auth.jwt] to TRACE\n21:11:23,238 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.nextchaptersoftware] to DEBUG\n21:11:23,238 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.nextchaptersoftware.plugins.Monitoring] to DEBUG\n21:11:23,239 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.nextchaptersoftware.sourcemarks.git.GitRunner] to INFO\n21:11:23,239 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - End of configuration.\n21:11:23,239 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@4fbdc0f0 - Registering current configuration as safe fallback point\n21:11:23 | INFO  | c.n.n.p.EmailTemplates: Initializing email template: ThreadInviteTemplate.json \n{  } \n21:11:23 | INFO  | c.n.n.p.EmailTemplates: 'ThreadInviteTemplate' already exists, updating it \n{  } \n21:11:23 | INFO  | c.n.n.p.EmailTemplates: done updating :'ThreadInviteTemplate' \n{  } \n21:11:23 | INFO  | c.n.n.p.EmailTemplates: Initializing email template: RawTemplate.json \n{  } \n21:11:23 | INFO  | c.n.n.p.EmailTemplates: 'RawTemplate' already exists, updating it \n{  } \n21:11:23 | INFO  | c.n.n.p.EmailTemplates: done updating :'RawTemplate' \n{  } \n21:11:23 | INFO  | c.n.n.p.EmailTemplates: Cleaning orphaned templates \n```"}
{"comment": {"body": "Rashin is freakin awesoooome! ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1247#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1247#pullrequestreview-968667509", "body": ""}
{"title": "this is to fix the health probes which I think still depend on DB", "number": 1248, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1248", "body": "This fixes the: \nException in thread \"DefaultDispatcher-worker-1\" kotlinx.coroutines.CoroutinesInternalError: Fatal exception in coroutines machinery for DispatchedContinuation[Dispatchers.IO, Continuation at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt)@7e45291]. Please read KDoc to 'handleFatalException' method and report this incident to maintainers\n    at kotlinx.coroutines.DispatchedTask.handleFatalException(DispatchedTask.kt:144)\n    at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:115)\n    at kotlinx.coroutines.internal.LimitedDispatcher.run(LimitedDispatcher.kt:42)\n    at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:95)\n    at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:570)\n    at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:749)\n    at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:677)\n    at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)\n    Suppressed: kotlinx.coroutines.DiagnosticCoroutineContextException: [org.jetbrains.exposed.sql.transactions.experimental.TransactionCoroutineElement@2b0b3414, org.jetbrains.exposed.sql.transactions.experimental.TransactionScope@2cb8a5b7, DeferredCoroutine{Active}@46c6b9e1, Dispatchers.IO]\nCaused by: java.lang.IllegalStateException: Please call Database.connect() before using this code\n    at org.jetbrains.exposed.sql.transactions.NotInitializedManager.currentOrNull(TransactionApi.kt:39)\n    at org.jetbrains.exposed.sql.transactions.TransactionManager$Companion.currentOrNull(TransactionApi.kt:124)\n    at org.jetbrains.exposed.sql.transactions.experimental.TransactionCoroutineElement.updateThreadContext(Suspended.kt:35)\n    at org.jetbrains.exposed.sql.transactions.experimental.TransactionCoroutineElement.updateThreadContext(Suspended.kt:31)\n    at kotlinx.coroutines.internal.ThreadContextKt.updateThreadContext(ThreadContext.kt:78)\n    at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:224)\n    ... 6 more"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1248#pullrequestreview-968690313", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1248#pullrequestreview-968690521", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1248#pullrequestreview-968691200", "body": ""}
{"title": "Make health checkers explicit", "number": 1249, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1249", "body": "Multiple people @davidkwlam @mahdi-torabi have been burrned by implicit health checkers.\nI have decided that their pain needs to be addressed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1249#pullrequestreview-969676304", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1249#pullrequestreview-969692007", "body": ""}
{"comment": {"body": "Shouldn't Admin web do a DB check ?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1249#discussion_r870535779"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1249#pullrequestreview-969693843", "body": ""}
{"comment": {"body": "or maybe I am looking at the wrong place. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1249#discussion_r870536996"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1249#pullrequestreview-969695753", "body": ""}
{"comment": {"body": "Yeah, those are just generic defaults for testing purposes.\r\nThe actual stuff used by server is in Application.kt.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1249#discussion_r870538309"}}
{"title": "Identity related APIs", "number": 125, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125", "body": "just stubs for now\nimplementation comes in follow up"}
{"comment": {"body": "@pwerry @matthewjamesadam @kaych @rasharab anything else on this one?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-862903040", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-862911292", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-862953172", "body": ""}
{"comment": {"body": "There's a problem with either the templates or the generator that causes this to break.\r\n\r\nThe generator adds a `Provider` type to the parameter list, but never generates the actual `Provider` class", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792183242"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-862953662", "body": ""}
{"comment": {"body": "I think it has to do with Enum types specifically", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792183391"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-862970080", "body": ""}
{"comment": {"body": "I take this back. The generator appears to be non-deterministic", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792199615"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-862975110", "body": ""}
{"comment": {"body": "You mean locally? Not surprising, try clean:\r\n`./gradlew clean build`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792203584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-862977217", "body": ""}
{"comment": {"body": "This is not a generator issue.  :)\r\nI was helping Peter diagnose this.\r\nIf it was a generator issue, we would be noticing this error across the many CI builds we do that invoke said generator.\r\n\r\nThis is likely a result of incremental builds which should **_not_** be conflated with determinism.\r\nIncremental gradle builds in particular don't work that great with the types of changes that go on with code generation....\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792205254"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863004807", "body": ""}
{"comment": {"body": "is this the provider identity? ie not the Codeswell identity? if so I think displayName and avatarUrl are optional(?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792227297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863016208", "body": ""}
{"comment": {"body": "It is the SCM provider identity.\r\nWhy do you think displayName and avatarUrl are optional?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792236396"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863017905", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863032629", "body": ""}
{"comment": {"body": "Avatar is not optional, it just defaults to a random pic. (eg: https://api.github.com/users/akyrtzi)\r\nThe displayName will fallback to the username \u2014 I\u2019ll update this in the api spec.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792249181"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863035934", "body": ""}
{"comment": {"body": "updated in https://github.com/Chapter2Inc/codeswell/pull/125/commits/752289565fd58bb870550099010672957f38d473", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792251885"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863048228", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863063619", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863068364", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863073214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863074579", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863813190", "body": ""}
{"comment": {"body": "Fine for now, may have to be paged in the future?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792835207"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863825189", "body": ""}
{"comment": {"body": "Same is true for all list APIs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792844239"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863825382", "body": ""}
{"comment": {"body": "Yeah defer", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792844380"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863864052", "body": ""}
{"comment": {"body": "This feels a bit under-optimized for what a client will want to do: render avatars and names (2 additional API calls to get to that information for each participant)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792872417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-863914440", "body": ""}
{"comment": {"body": "@matthewjamesadam preferred this. the idea is to cache the teamMembers client-side, then lookup. It's super cheap to expose the full model here, so I'm happy to expose it; then the client can either consume the full model or just the ID-part.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792905905"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-864010241", "body": ""}
{"comment": {"body": "done here https://github.com/Chapter2Inc/codeswell/pull/125/commits/345eb0d25a65d04b11524085178c11c4f42717e3", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r792976815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-864086886", "body": ""}
{"comment": {"body": "Yeah I don't have a strong preference either way, this is fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#discussion_r793034492"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/125#pullrequestreview-864094788", "body": ""}
{"title": "Web extension source point bugs", "number": 1251, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1251", "body": "Add Development prod script for web extension\nFixes two bugs:\n1. When trying to render source points, we tried attaching to DOM too early. Add timeout to wait for GH rendering first.\n2. Fetch filePath from DOM instead of URL due to issues with branch names."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1251#pullrequestreview-969802812", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1251#pullrequestreview-969807424", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1251#pullrequestreview-969808245", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1251#pullrequestreview-969934936", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1251#pullrequestreview-969967513", "body": ""}
{"title": "Clean up dependencies", "number": 1252, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1252"}
{"title": "Make ThreadSearchModel.modelId and ThreadSearchModel.modelType nullable", "number": 1253, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1253", "body": "We don't need these columns anymore. Let's first make them nullable then once deployed we can drop them."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1253#pullrequestreview-969805879", "body": ""}
{"title": "Drop ThreadSearchModel.modelId and ThreadSearchModel.modelType columns", "number": 1254, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1254", "body": "Now that the columns are nullable and https://github.com/NextChapterSoftware/unblocked/pull/1253 has deployed to all environments, it's safe to remove these columns from our models. \nNote that this will not drop the columns from the database table because exposed's data migration feature doesn't drop columns. That needs to happen manually."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1254#pullrequestreview-969845267", "body": ""}
{"title": "Testing Spot EC2 action runner (WIP) Do NOT merge", "number": 1255, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1255", "body": "This PR switches our workflow to the new in-house GitHub action for spot instances. \nIt's currently using the MaxPerformance scheduling which means we try to get the largest available spot instance for the cost of an on-demand instance"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1255#pullrequestreview-1003346380", "body": ""}
{"comment": {"body": "fsdsfda\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/631ab908-f325-4e26-8015-da9f1009e412?message=3b4f6d10-da67-4edf-9ff3-330415454479).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1255#discussion_r894906071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1255#pullrequestreview-970215846", "body": ""}
{"comment": {"body": "This is because we haven't yet made our repo public. We will remove it once the repo is public", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1255#discussion_r870920639"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1255#pullrequestreview-971023299", "body": ""}
{"title": "Add pricing permissions for deploybot", "number": 1256, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1256"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1256#pullrequestreview-969927736", "body": ""}
{"title": "Set up a new job to read from pr_ingestion queue", "number": 1258, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1258", "body": "This job will eventually ingest comments from pull requests. Adding this now to test that we can consume from the queue before adding our logic."}
{"title": "Use insertIgnore in GitHubModelTransformers.createIdentities", "number": 1259, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1259", "body": "To simplify the logic"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1259#pullrequestreview-970010558", "body": "Nice!"}
{"title": "Run CI on api/ changes", "number": 126, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/126", "body": "Should we also run VSCode and Web workflows on api/ changes @matthewjamesadam ?"}
{"comment": {"body": "@richiebres yeah I think we should -- the API spec is an input into the build for those projects, if we don't rebuild them then we risk leaving broken builds for the next engineer that makes a change in either of those projects...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/126#issuecomment-1021623229"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/126#pullrequestreview-862914653", "body": ""}
{"title": "Get more data for invite emails", "number": 1260, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#pullrequestreview-970089609", "body": ""}
{"comment": {"body": "I'd consider creating a `MessageBodyToHtmlConverter` to wrap this logic.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#discussion_r870821190"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#pullrequestreview-970108917", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#pullrequestreview-971450979", "body": "Looks good to me"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#pullrequestreview-971457557", "body": ""}
{"comment": {"body": "I mentioned this yesterday, but will put it here too: I think we need to handle when client's retry this operation. If they do, I think we'll send multiple emails here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#discussion_r871809447"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#pullrequestreview-971461907", "body": ""}
{"comment": {"body": "@mahdi-torabi is there something we can do to gate spamming users with multiple emails? does amazon SES have some way to handle this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#discussion_r871812736"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#pullrequestreview-971464742", "body": ""}
{"comment": {"body": "We use Fifo queues for emails. As long as the contents are the same the queue does a great job deduplicating them \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#discussion_r871814706"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#pullrequestreview-971465140", "body": ""}
{"comment": {"body": "It somehow even remembers if you recently had sent an item and won't queue it if it's a dupe. Even if it no longer exists in the queue. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1260#discussion_r871814965"}}
{"title": "Show GitHub rate limit on admin team page", "number": 1261, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1261", "body": "We'll need this for backing off pull request ingestion when we get close to the limit"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1261#pullrequestreview-970094394", "body": "cool"}
{"title": "Implement hub grpc service", "number": 1262, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262", "body": "Verified using Evan grpc CLI"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971487225", "body": ""}
{"comment": {"body": "This the port that's supposed to be written to user defaults?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871831355"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971488466", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971492226", "body": ""}
{"comment": {"body": "Or does grpc provide a new address/port on line 45?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871835092"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971492756", "body": ""}
{"comment": {"body": "Doesn't look like this factory is used?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871835480"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971493777", "body": ""}
{"comment": {"body": "Not sure if I'm understanding the spec correctly but shouldn't the client (aka vscode) be generating this UUID and sending it as part of the TokenRequest to the Hub?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871836286"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971496645", "body": "@matthewjamesadam May be good for you to also take a look. I have a rough understanding of what's going on but I'm learning as I go with grpc."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971499659", "body": ""}
{"comment": {"body": "holy smokes good catch! It's meant to be used by the `HubIPCServiceProvider`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871840700"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971509407", "body": ""}
{"comment": {"body": "port 0 generally means \"find and use a free public port\", so yes this will be returned below I'd imagine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871848217"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971510894", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971511250", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971511576", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971511717", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971513245", "body": ""}
{"comment": {"body": "You could probably DRY these two interceptors out into one generic on request/response that filters out based on IP address...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871850904"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971513455", "body": ""}
{"comment": {"body": "Yes I'm starting to think we need to remove this field after all. After playing with it I realized we can just assign a tracking identifier for each client here, so it's not actually necessary for the client to send one after all. I'll need to play around with that though because of that weird bug I found with empty request objects", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871851204"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971513852", "body": ""}
{"comment": {"body": "Yup in the process of doing that now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871851524"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971515712", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971522256", "body": ""}
{"comment": {"body": "Matt is correct - port 0 gets us a new port which is read out and stored", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#discussion_r871858157"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971522795", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971531832", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971543532", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971545775", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1262#pullrequestreview-971550217", "body": ""}
{"title": "Add getAnchorSourceMarkOriginalSourcePointForThread", "number": 1263, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1263", "body": "Needed for https://github.com/NextChapterSoftware/unblocked/pull/1260"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1263#pullrequestreview-970121254", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1263#pullrequestreview-970121367", "body": ""}
{"comment": {"body": "do we still need anchor sourcemark?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1263#discussion_r870846255"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1263#pullrequestreview-970122514", "body": ""}
{"comment": {"body": "For this query? I think so, to guard against multiple sourcemarks for a thread (even though we don't support it)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1263#discussion_r870847223"}}
{"title": "Update search behaviour", "number": 1264, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1264", "body": "Revert search behaviour to auto result. The original reason to move away from auto result was a jarring loading screen in-between searching instances. Removed search loading state for better flow UX.\nMove debouncer logic from VC to search store."}
{"comment": {"body": "\r\nhttps://user-images.githubusercontent.com/1553313/167968073-8343bd46-b6ad-429d-be1a-ee742b18b4b9.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1264#issuecomment-1124404696"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1264#pullrequestreview-971564268", "body": ""}
{"comment": {"body": "Feel free to modify Debouncer to take a value as well.  Any current invocations could just be a Debouncer<void>.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1264#discussion_r871885669"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1264#pullrequestreview-971565190", "body": ""}
{"title": "Load thread in dashboard on click", "number": 1265, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1265", "body": "Has some temporary code in here as a hack until we move to fat thread models. Just so people can play with it\nThis PR also moves the Hub to use Prod by default"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1265#pullrequestreview-971442659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1265#pullrequestreview-972560546", "body": ""}
{"comment": {"body": "worth adding local?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1265#discussion_r872613562"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1265#pullrequestreview-972593804", "body": ""}
{"comment": {"body": "Update\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1265#discussion_r872637548"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1265#pullrequestreview-972614138", "body": ""}
{"comment": {"body": "I'm not using the local stack for dev, but it's trivial to add when we do!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1265#discussion_r872651880"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1265#pullrequestreview-972760892", "body": ""}
{"title": "Add basic trace library", "number": 1266, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266", "body": "Got basic configuration library up for connecting to honeycomb and adding proper shutdown handlers to flush data."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#pullrequestreview-970164555", "body": ""}
{"comment": {"body": "be nice to make these required, even for environments including local.\r\ncould have an additional `isEnabled: boolean` to toggle it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#discussion_r870881053"}}
{"comment": {"body": "1. randomly? we may want to preserve 100% of 500s?\r\n1. does this drop the _spans_ or the entire _trace_. not clear to me from docs. it is drops spans, then we end up with weird holes in the trace", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#discussion_r870885960"}}
{"comment": {"body": "could do more with tests here:\r\n- within the child context, we could assert that `(ANIMAL, \"cat\")` is set\r\n- set an attribute within the child context, and assert that the child attribute is not in current context once we leave the child context.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#discussion_r870887603"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#pullrequestreview-970182074", "body": ""}
{"comment": {"body": "This is just a skeleton. None of this stuff is really to be used ATM.\r\nthis test will be augmented.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#discussion_r870894695"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#pullrequestreview-970182299", "body": ""}
{"comment": {"body": "Yeah, going to write our own sampler probably.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#discussion_r870894888"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#pullrequestreview-970237624", "body": ""}
{"comment": {"body": "Agreed!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1266#discussion_r870937339"}}
{"title": "Emit pull request ingestion events", "number": 1267, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1267", "body": "Part of the work to move towards event-based PR ingestion"}
{"title": "Query to support My Threads in tree view", "number": 1268, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268", "body": "changes\n\norder by unread then last message created time -- so that clients do not have to fetch all threads\nlimit and cursor pagination -- supports paging from any thread in the thread collection\nfat thread results -- thread, unread, participant IDs, repo ID, messages, sourcemark, and original source points\nfilters out archived\nfilters by repos\n\nnext\n\nnew apis in https://github.com/NextChapterSoftware/unblocked/pull/1271"}
{"comment": {"body": "> It looks good to me based on the tests but hard to see all the possible behaviours until we're using it\r\n\r\nI spent 90% of my time on the tests, which are solid. The only thing I can\u2019t get a sense for yet is runtime performance. Won\u2019t be able to assess that until this is exposed in api and I can test on prod dataset. That\u2019s next.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#issuecomment-1125193405"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-970986029", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-970989036", "body": ""}
{"comment": {"body": "Should we moving to the paradigm of adding assertions in our code?\r\nAlso, if in fact true, null are the only appropriate values, should unread state be an enum?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#discussion_r871477178"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-970991810", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-970995188", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-970998634", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971024821", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971028570", "body": ""}
{"comment": {"body": "The idea is to avoid table density, so the trick is null == false", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#discussion_r871503819"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971034852", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971046950", "body": "It looks good to me based on the tests but hard to see all the possible behaviours until we're using it"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971128745", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971137026", "body": ""}
{"comment": {"body": "We already have UnreadStatus, but that won\u2019t help here.\r\n\r\nThe problem here is subtle and a little hard to explain clearly: the thread query is a left join on ThreadUnread, meaning that the result set has sparse ThreadUnread rows. Our business logic needs to treat a missing row the same as `isUnread == false`. Coercing false as null makes the query quite efficient.\r\n\r\nI want this to fail in dev if we ever get a false, since that\u2019s a programmer error.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#discussion_r871579849"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971138063", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971141107", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971141802", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971200947", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971204755", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971211173", "body": ""}
{"comment": {"body": ">where no table entry for a {thread, member} pair means the thread has been read by the member\r\n\r\nDo we need to update `ThreadUnreadStore.createMissingThreadUnreads` to reflect this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#discussion_r871633506"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971211859", "body": ""}
{"comment": {"body": "(this is the backfill function for PR ingested threads we chatted about a couple days ago)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#discussion_r871634023"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971212531", "body": ""}
{"comment": {"body": "Ah maybe ignore me, I think it's fine", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#discussion_r871634502"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971242766", "body": ""}
{"comment": {"body": "This new field will be null for all existing ThreadUnreads, so ideally we would backfill. But I don't think it's worth doing tbh. After this is deployed, anyone replying to a thread will set isUnread=true.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#discussion_r871656251"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971440447", "body": ""}
{"comment": {"body": "So basically we're leveraging SQL behaviour on left joins to make the query efficient. \r\nGuaranteed this will not be obvious (it wasn't for me as I wasn't in the loop).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#discussion_r871797426"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971441967", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971693669", "body": ""}
{"comment": {"body": "It's well tested; so even without the runtime development assertion regressions are not possible.\r\n\r\nOne alternative is to make ThreadUnread a dense model; but it seems wasteful since 99.5% of threads are not unread.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#discussion_r871995020"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971707388", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971707643", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971713643", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971720010", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1268#pullrequestreview-971723461", "body": ""}
{"title": "update", "number": 1269, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1269"}
{"title": "MessageEditor basics", "number": 127, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/127", "body": "Adds a skeleton for a WYSIWYG editor using Slate.js.  This is a totally unstyled UI and only supports the following:\n\nBold/italic text, through either a button, a hotkey (Cmd+B), or entering in markdown-style *something*.\nQuote blocks\nCode blocks\nImage blocks (though this is just a proof-of-concept of how it could work)\n\nThere are many things left to do:\n* Add more features in the editor UI doc\n* Figure out how to test this thing\n* Add styling support so VSCode / web can be styled correctly\n* Find a way to delegate platform-supplied behaviour (for example, syntax hilighting)"}
{"comment": {"body": "Chromatic support is broken right now, so here's a screenshot of what the predefined content looks like:\r\n\r\n<img width=\"1552\" alt=\"Screen Shot 2022-01-25 at 1 38 42 PM\" src=\"https://user-images.githubusercontent.com/2133518/*********-eabe7fd2-e2b7-45fa-a5d2-fbbd57cf8f2e.png\">\r\n\r\nStorybook works really well for testing this UI out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/127#issuecomment-1021634218"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/127#pullrequestreview-*********", "body": "Looks really good as a first draft!\nI can really imagine how we go about building out the rest of the editor.\nFor styling, I don't think there's going to be a quick solution. I can imagine having a documented set of classnames that each client implements in css. There should still be some base styling in regards to spacing & etc.\nFor testing,  ?"}
{"comment": {"body": "It looks like this is everything *except* paragraph?\r\n\r\nI'm thinking that maybe we introduce a `ParagraphTrait` which also conforms to `ElementTrait` and has its own render function.\r\n\r\nEverything, including ParagraphTrait *must* have a render function to appear in the editor.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/127#discussion_r792204967"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/127#pullrequestreview-862997268", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/127#pullrequestreview-863003321", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/127#pullrequestreview-864108489", "body": ""}
{"title": "Add per channel poll rate to ChannelPoller", "number": 1270, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1270", "body": "The implementation is simple: Continue to run the poller once a second, but only actually query for the channels that we need to on each poll.  This could be made more precise but is probably fine for now.\nThe unit tests are pretty messy.  I'd like to upgrade us to Jest 28 which would let us use fake timers to write these tests synchronously, so they would be 100% precise and deterministic.  That will come later too."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1270#pullrequestreview-971528506", "body": "Nice! Pretty simple way to add the different intervals"}
{"title": "New thread listing APIs", "number": 1271, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271", "body": "Thread listing API return ThreadInfo model that contains Thread, Message, Participants, SourceMarks, etc.\nChanged getThread API to return ThreadInfo too. Technically a breaking change, but no client is using currently so fine.\nIntroduce cursor-based paging on new thread listing APIs, and limits.\nDeprecated getThreads, which we will remove once all client have transitioned to new thread listing APIs.\nDeprecated participants on Thread as it is duplicative and not needed where Thread object is now used (archiving, restoring, updating)."}
{"comment": {"body": "Are you adding the get archived ThreadInfos call later?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#issuecomment-1125432743"}}
{"comment": {"body": "\ud83c\udf89 Looks great!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#issuecomment-1125436144"}}
{"comment": {"body": "Indeed, YOLO \ud83d\udea2 ship it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#issuecomment-1125436264"}}
{"comment": {"body": "> Are you adding the get archived ThreadInfos call later?\r\n\r\nI'll do in another PR. Just going to wait to see if there is any additional feedback on this:\r\nhttps://chapter2global.slack.com/archives/C02GEN8LFGT/p1652393321894119", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#issuecomment-1125467053"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971456157", "body": ""}
{"comment": {"body": "This sorting behaviour is true if `X-Unblocked-If-Modified-Since` is not provided -- if it is, I think its ordered by recency of DB update?  The idea being that clients will need to sort the updated items into their caches?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871808425"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971458021", "body": ""}
{"comment": {"body": "Do we want to return a wrapper object for this instead of the ThreadInfo itself, so we can allow adding meta-information (\"why is this relevant?\") for each thread in the future without breaking backwards compatibility?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871809792"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971459107", "body": ""}
{"comment": {"body": "I've found that code generation against these sort of collective type based operators is hit and miss.\r\nJust a heads up about that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871810578"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971459432", "body": ""}
{"comment": {"body": "Changing this to FatThreads will come later?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871810811"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971460063", "body": ""}
{"comment": {"body": "I think it's ordered with the same scheme regardless of whether `X-Unblocked-If-Modified-Since` is provided, except only with the items from last modified if it is - so yes I think we need to insert/sort/limit when these items are received", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871811273"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971460578", "body": ""}
{"comment": {"body": "Ordering is identical either way right now in #1268. Pretty sure clients don't care about ordering of collection in the X-Modified-Since case.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871811657"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971461045", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971461200", "body": ""}
{"comment": {"body": "This is the recommended way to override description on an object. We've been using it for months.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871812156"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971461406", "body": ""}
{"comment": {"body": "getPersonalThreads?\r\n\r\nI don't know, the whole pronoun in api thing is weird to me.\r\nJust me probably.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871812308"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971462100", "body": ""}
{"comment": {"body": "Ah I see the relevancy is on the ThreadInfo, never mind...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871812863"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971467436", "body": ""}
{"comment": {"body": "Maybe we just need to add `getThreadsForRashin`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871816644"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971468456", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971469953", "body": ""}
{"comment": {"body": "So the reason for deprecating this is because `Thread` will ultimately only be used for mutating operations, not for GET operations at all?  Is that correct?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871818501"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971470640", "body": ""}
{"comment": {"body": "yup that's the idea. didn't want to break existing stuff now, as clients are already using this.\r\n\r\n@davidkwlam can decide when he's ready to switch.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871819042"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971471626", "body": "As the inimitable @matthewjamesadam once told me, YOLO.\nShip it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#pullrequestreview-971477398", "body": ""}
{"comment": {"body": "Yup, I had a note in the PR description explaining why. If you update, archive or restore a thread, then you really don't need to re-fetch participants or other objects; you just need the thread, as you only changed the thread properties.\r\n\r\n> Deprecated participants on Thread as it is duplicative and not needed where Thread object is now used (archiving, restoring, updating).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1271#discussion_r871824067"}}
{"title": "Setup GRPC Client and AUth", "number": 1272, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272", "body": "Setup GRPC for HubClient.\nInfluenced by "}
{"comment": {"body": "WIP: Still needs to be tested against hub client...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#issuecomment-1125485214"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971529467", "body": ""}
{"comment": {"body": "I don't think the /generated path within shared was being built anymore... Not sure how things were working.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#discussion_r871863925"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971531304", "body": ""}
{"comment": {"body": "Yikes.\r\n\r\nI'm not sure I love how we're generating code into a central (`/common`) place?  The swift code is generating into its own project, should we do that too?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#discussion_r871865383"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971535627", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971536000", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971540916", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971544373", "body": ""}
{"comment": {"body": "Yeah. This is the same code/path that's used for the message proto as well. There's a comment within shared/api/models where @richiebres mentions he wants to consolidate `shared` `common` and `api` to potentially help this out?\r\n\r\nRegardless, not something I'd like to tackle in this PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#discussion_r871873683"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971547900", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971560355", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971562191", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-971592522", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-972516555", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-972862905", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-973310486", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-973310771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-973310975", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-974674404", "body": ""}
{"comment": {"body": "We'll need to re-start this polling loop whenever we *lose* the connection to the hub app as well, right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#discussion_r874239040"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-974674773", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-974797304", "body": ""}
{"comment": {"body": "I guess this depends on how tied-together we want the auth state to be?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#discussion_r874334211"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1272#pullrequestreview-974798750", "body": ""}
{"title": "Clean up duplicated code", "number": 1273, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1273"}
{"title": "Pull out reused code and add tests", "number": 1274, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1274"}
{"title": "Persist GitHub Org role", "number": 1275, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1275", "body": "For now just debugging in admin web, but will be leveraged for directing users to owner-specific tasks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1275#pullrequestreview-971619574", "body": ""}
{"title": "Progress bar component - use for rate limit", "number": 1276, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1276", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1276#pullrequestreview-971637864", "body": "Lol"}
{"title": "Archived threads API", "number": 1277, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1277", "body": "Plan:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1277#pullrequestreview-971748895", "body": ""}
{"title": "Remove testAuthentication api operation", "number": 1278, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1278"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1278#pullrequestreview-971766148", "body": ""}
{"title": "Revert \"Persist GitHub Org role\"", "number": 1279, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1279", "body": "Reverts NextChapterSoftware/unblocked#1275\nBUG: Removes owners from team, breaking auth for pretty much everyone."}
{"title": "Add Calico to EKS and network policy for API service", "number": 128, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/128", "body": "Added Calico installation instructions (Also installed it on Dev cluster)\nAdded default deny policy yaml (not applied yet)\nCreated helm template for network policy\n\nThis is the yaml generated by helm template command. Some values are missing because template command does not have release names but the rest look to be in order. \n```yaml\n\nSource: service/templates/network-policy.yaml\nkind: NetworkPolicy\napiVersion: networking.k8s.io/v1\nmetadata:\n  name: RELEASE-NAME-service\n  labels:\n    helm.sh/chart: service-0.1.0\n    app.kubernetes.io/name: service\n    app.kubernetes.io/instance: RELEASE-NAME\n    app.kubernetes.io/version: \"1.16.0\"\n    app.kubernetes.io/managed-by: Helm\n  annotations:\n    external-dns.alpha.kubernetes.io/hostname: RELEASE-NAME.us-west-2.dev.usecodeswell.com\n    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http\n    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: arn:aws:acm:us-west-2:129540529571:certificate/9f9742d5-3454-43ce-a7e2-c7bae0c36bc5\n    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: https\nspec:\n  podSelector:\n    matchLabels:\n      app.kubernetes.io/name: service\n      app.kubernetes.io/instance: RELEASE-NAME\n  ingress:\n  - from:\n    - podSelector:\n        matchLabels:\n          app.kubernetes.io/name: service\n          app.kubernetes.io/instance: RELEASE-NAME\n    ports:\n    - port: 8080\n  egress:\n  - to:\n    - podSelector:\n        matchLabels:\n          app.kubernetes.io/name: service\n          app.kubernetes.io/instance: RELEASE-NAME\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/128#pullrequestreview-862929368", "body": ""}
{"title": "[Performance] Pull request ingestion is event-based", "number": 1280, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1280", "body": "Just a refactor of the PR ingestion logic to speed ingestion up.\n(sorry for the big PR)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1280#pullrequestreview-972938003", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1280#pullrequestreview-972938383", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1280#pullrequestreview-972938777", "body": ""}
{"title": "Update the readme with project setup steps", "number": 1281, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1281", "body": "Unblocked Video App\n## Setup]\nRun make setup, then open Unblocked Video App.xcodeproj"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1281#pullrequestreview-972608156", "body": ""}
{"title": "Persist org role -- with bug fix", "number": 1282, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1282", "body": "Re-apply the previously reverted PR #1279, which failed because it marked all of the Org owners as deactivated.\nThe fix https://github.com/NextChapterSoftware/unblocked/pull/1282/commits/e009b31ba47bec50cdef03fdda0f76bb636bd169 is to process both the members and owners in one shot."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1282#pullrequestreview-972585534", "body": ""}
{"title": "Remove sourceMarkGroupId from thread API response", "number": 1283, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1283", "body": "Never used or needed to be exposed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1283#pullrequestreview-972617836", "body": ""}
{"title": "Add basic honeycomb framework and etsts", "number": 1284, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1284"}
{"title": "Add thread ID to ThreadInfo", "number": 1285, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1285", "body": "Client-side machinery assumes models have an id property:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1285#pullrequestreview-972628302", "body": "Thanks"}
{"title": "Disable flaky test", "number": 1286, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1286"}
{"title": "update thread invites email template", "number": 1287, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1287"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1287#pullrequestreview-972663777", "body": ""}
{"title": "update", "number": 1288, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1288"}
{"title": "Make ThreadInfo.id non-optional", "number": 1289, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1289", "body": "In the generated TS code, ThreadInfo.id was optional.  I suspect this is an outcome of using the allOf trick here....?\nNot sure if there's a better way to deal with this, I'm open to suggestions."}
{"comment": {"body": "> fucking code generators (fwiw Kotlin generated code was fine)\r\n\r\nRemember the comment I said yesterday about polymorphic allOf, oneOf etc.\r\nI remember us hitting this a while back with other shit.\r\nThe code generators are all wonky across language boundaries.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1289#issuecomment-1126505022"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1289#pullrequestreview-972691484", "body": "fucking code generators (fwiw Kotlin generated code was fine)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1289#pullrequestreview-972692113", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1289#pullrequestreview-972723807", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1289#pullrequestreview-972732612", "body": ""}
{"title": "Spinner", "number": 129, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/129", "body": "Prep for loading page. Not in any designs."}
{"title": "change escape characters", "number": 1290, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1290"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1290#pullrequestreview-972744915", "body": ""}
{"title": "Fix Create Discussion in Web Extension", "number": 1291, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1291", "body": "Github removed the data-permalink-href property on the selector...\nMove to basic href."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1291#pullrequestreview-972838225", "body": ""}
{"title": "Update escaping", "number": 1292, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1292"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1292#pullrequestreview-972841274", "body": ""}
{"title": "trying single slash escapes", "number": 1293, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1293"}
{"comment": {"body": "Dupe of https://github.com/NextChapterSoftware/unblocked/pull/1292", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1293#issuecomment-1126491865"}}
{"title": "Add nullibility to tokens", "number": 1294, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1294", "body": "And a logout action to the token request"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1294#pullrequestreview-972886131", "body": ""}
{"title": "Send emails to recipients", "number": 1295, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1295"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1295#pullrequestreview-972903058", "body": ""}
{"title": "Add grpc dependency", "number": 1296, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1296"}
{"title": "Plumbing new thread APIs to thread info store", "number": 1297, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1297", "body": "Hooked up these:\n - getThread\n - getThreadsForMe\nThese will come in follow up PR:\n - getThreadsRecommended\n - getThreadsArchived"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1297#pullrequestreview-972930182", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1297#pullrequestreview-972930644", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1297#pullrequestreview-972932474", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1297#pullrequestreview-972932649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1297#pullrequestreview-972933272", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1297#pullrequestreview-972944918", "body": ""}
{"title": "Update web extension version", "number": 1298, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1298", "body": "Updating to match App Store connect."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1298#pullrequestreview-974670417", "body": ""}
{"title": "Add image caching for avatars", "number": 1299, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1299"}
{"title": "Web primitives", "number": 13, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/13"}
{"title": "Spinner", "number": 130, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130", "body": "Setup spinner for loading screen. Currently not in designs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#pullrequestreview-862973090", "body": ""}
{"comment": {"body": "These can be *anything* @benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#discussion_r792202007"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#pullrequestreview-865482664", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#pullrequestreview-865482922", "body": ""}
{"comment": {"body": "should maybe think about having an animations.scss ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#discussion_r794037186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#pullrequestreview-865483552", "body": ""}
{"comment": {"body": "yeah I'm curious if we plan to always have these in the same color or do we plan to have them be different at different places for any reason (?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#discussion_r794037579"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#pullrequestreview-865483910", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#pullrequestreview-866452335", "body": ""}
{"comment": {"body": "This will be an interesting thing to have a storybook UI test for -- because the animation always runs the snapshot point will be inconsistent?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#discussion_r794707772"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#pullrequestreview-868139864", "body": ""}
{"comment": {"body": "Let's revisit this if we notice more need for animations? I don't forsee too many usecases, at least from our current designs...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#discussion_r795870059"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#pullrequestreview-868152733", "body": ""}
{"comment": {"body": "There currently is *not* a chromatic test for this but they handle it.\r\nhttps://www.chromatic.com/docs/animations", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#discussion_r795879306"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#pullrequestreview-868152979", "body": ""}
{"comment": {"body": "Chromatic will pause CSS animations and reset them to their beginning state.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/130#discussion_r795879480"}}
{"title": "Support if-modified-since on getThread operation", "number": 1300, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1300"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1300#pullrequestreview-972956823", "body": ""}
{"title": "Onboarding Views", "number": 1301, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1301", "body": "Login action is wired up, all other actions are not. We had a brief discussion about intercom on Friday - probably needs to go to the dashboard.\nLight\n\n\n\n\nDark\n\n\n\n"}
{"comment": {"body": "@pwerry @jeffrey-ng do we want to handle the new fonts in a separate PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1301#issuecomment-1126778080"}}
{"comment": {"body": "> @pwerry @jeffrey-ng do we want to handle the new fonts in a separate PR?\r\n\r\nYes - I'll bring those all in at once. \r\n\r\nOnce I figure out how to install the font and get it to place nice with the system colours then it's pretty trivial to apply it across the board", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1301#issuecomment-1126789277"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1301#pullrequestreview-973087183", "body": ""}
{"comment": {"body": "This `LoginView` declaration is duplicated because `@ViewBuilder` doesn't support switch `fallthrough`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1301#discussion_r873055868"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1301#pullrequestreview-974568252", "body": ""}
{"title": "Populate PR comment url on thread", "number": 1302, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1302", "body": "Clients should use the PR comment url instead of the PR url when\n  the discussion is sourced from a PR comment.\nAlso fix bug where we were returning deleted messages in API."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1302#pullrequestreview-973120104", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1302#pullrequestreview-973124109", "body": ""}
{"comment": {"body": "Oh I've been using Op.FALSE but I guess this works too?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1302#discussion_r873107158"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1302#pullrequestreview-973126679", "body": ""}
{"comment": {"body": "Lol. Shit I hope so :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1302#discussion_r873111171"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1302#pullrequestreview-973127043", "body": "LGTM. I'll follow up Monday re: emails"}
{"title": "Obsolete getThreads and Threads.participants", "number": 1303, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1303", "body": "Note: Can review, but merge only when clients are ready to move on.\nTODO\n- [x] shared/stores/ThreadStore.ts needs to be rewritten / removed @matthewjamesadam"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1303#pullrequestreview-980736917", "body": ""}
{"title": "Starts the hub on login", "number": 1304, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304", "body": "Summary\nThis turns out to be quite the ceremony:\n\nCreate launcher app target\nSet launcher target to run in background only\nSet \"Skip Install\" build step in launcher target\nEmbed launcher app in main app at Contents/Library/LoginItems using copy files build script phase in main app target\nMake sure app is signed with Developer ID or notarized (launcher will fail if not)\nWhen main app launches, call SMLoginItemSetEnabled(launcher app bundle id)\nIn launcher app, do some detection work to see if main app is running, if not:\nGrab launch app bundle path and pop up 3 levels to Contents, then down to main app binary\nCall NSWorkspace.openApplication()\nKill launcher"}
{"comment": {"body": "w00t - solved", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#issuecomment-1128413796"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-973289316", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974648307", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974801405", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974802534", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974803025", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974803264", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974803670", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974803886", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974804042", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974804459", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974804571", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974804796", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974805298", "body": ""}
{"comment": {"body": "Wait I'm confused... do we need both an UnblockedHubLoginItem and an UnblockedHubLauncher?  The code looks pretty much the same?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#discussion_r874344116"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974805611", "body": "I'm guessing we have a duplicate launcher app here, but aside from fixing that this looks good.  Nice job figuring out this obscure, bizarre stuff."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974806136", "body": ""}
{"comment": {"body": "It's the same. I have both here while I'm experimenting", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#discussion_r874344774"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1304#pullrequestreview-974806440", "body": ""}
{"title": "UI to guide people to enable badge UI", "number": 1305, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1305", "body": "Detect when badges aren't enabled (ie, user has not enabled proposed APIs for our extension) -- this can be detected when setting the badge fails\nWhen this happens, show a UI that tells them what to do\n\n@benedict-jw this is just a temporary UI while our badge API is waiting for final approval.  Wording suggestions welcome."}
{"comment": {"body": "Do we have any control over the tab label?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1305#issuecomment-1127834088"}}
{"comment": {"body": "> Do we have any control over the tab label?\r\n\r\nYes the tab label and icon can be whatever we want", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1305#issuecomment-1127881762"}}
{"comment": {"body": "I've removed the UI for this -- we'll just log and avoid crashing now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1305#issuecomment-1128231651"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1305#pullrequestreview-973409094", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1305#pullrequestreview-974617864", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1305#pullrequestreview-974668906", "body": ""}
{"title": "Delay PR ingestion until rate limit resets", "number": 1306, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1306", "body": "I need to add a test for the PullRequestIngestionJob but the basic idea here is to check whether we're close to our rate limit and delay ingesting PRs for the team until it resets. We'll do that by setting the timeout on the message to ~ the reset time so that it'll be released to be consumed once the rate limit resets.\nFor now I've set the threshold for delay at 1000 remaining requests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1306#pullrequestreview-974439174", "body": ""}
{"title": "Add ApiDataStream", "number": 1307, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1307", "body": "Part 1 of adopting ThreadInfos.  This is a replacement for DataCacheStream, most of the code is a slightly modified copy of the DataCacheStream code.  The differences are:\n* ApiDataStream makes no assumption about the data it contains (ie, doesn't hold an array, and doesn't insist that its elements have an id property)\n* ApiDataStream only returns individual returned API elements, not a fully-composed list.  I will add a helper later that makes composing an array of final elements easy, for data where this makes sense.\nSome notes:\n* This isn't being used anywhere.  Next PR will use this in a few places.\n* I had to disable some tests to get this in.  I will enable them as the features they're tied to are enabled."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1307#pullrequestreview-974322706", "body": ""}
{"title": "[WiP] Add onboarding status to Person model", "number": 1308, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308"}
{"comment": {"body": "Closing - we'll handle this differently with a list of onboarding status flags. `Person` API model may still have a single `onboarded` flag", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#issuecomment-1128314168"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#pullrequestreview-974252992", "body": ""}
{"comment": {"body": "The onboarding status should probably be a structure of some kind -- I'm going to guess we will need to store more then just a boolean...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#discussion_r873933569"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#pullrequestreview-974253393", "body": ""}
{"comment": {"body": "@jeffrey-ng any thoughts on this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#discussion_r873933840"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#pullrequestreview-974275921", "body": ""}
{"comment": {"body": "What is the definition of \u201conboarded\u201d? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#discussion_r873950461"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#pullrequestreview-974343673", "body": ""}
{"comment": {"body": "That's probably right, and the structure needs to accommodate at least the following concerns:\r\n- Re-entrancy\r\n- Possibility of non-linear flow or branching?\r\n- A definition of \"fully onboarded\" given the above concerns", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#discussion_r873998299"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#pullrequestreview-974346820", "body": ""}
{"comment": {"body": "Yup that's missing here. Being \"fully onboarded\" affects the branching logic in the client. One thing to note is that the client doesn't need to understand what the definition of \"fully onboarded\" is until it's actually in the onboarding flow. A top level boolean is needed for this branching logic to work correctly. I think we should avoid baking any complex logic into the client to determine onboarding status at the top level.\r\n\r\nInternally the logic has the potential to get pretty complicated depending on support for re-entrancy etc. What we've arrived on so far is \"after the org onboarding and team invite step\". This means a user could quit half way through VSCode's \"onboarding\" flow, and still be in an \"onboarded\" state, and so would not see onboarding again when they restart VSCode. \r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#discussion_r874000683"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#pullrequestreview-974443748", "body": ""}
{"comment": {"body": "I don't follow. Why is this a server flag? Also, why is this not on TeamMember?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1308#discussion_r874067481"}}
{"title": "Use ThreadInfo API for dashboard", "number": 1309, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1309", "body": "Caveats:\n- 'Archived' API is not yet implemented, so that UI is removed\n- Search API is not yet implemented, so that UI is disabled"}
{"comment": {"body": "Waiting on implementation of push channels to merge this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1309#issuecomment-1128409401"}}
{"comment": {"body": "Blocked by recommended API in https://github.com/NextChapterSoftware/unblocked/pull/1328 (which needs review / approval)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1309#issuecomment-1130487965"}}
{"comment": {"body": "> Blocked by recommended API in #1328 (which needs review / approval)\r\n\r\nI've disabled the recommended UI in the dashboard in this PR -- it's very easy to re-enable it once the API is working.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1309#issuecomment-1130495407"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1309#pullrequestreview-974581090", "body": ""}
{"comment": {"body": "Temporarily disable 'Team' tab until the recommended API is working.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1309#discussion_r874168073"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1309#pullrequestreview-974668639", "body": ""}
{"title": "Revert \"Add Calico to EKS and network policy for API service\"", "number": 131, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/131", "body": "Reverts Chapter2Inc/codeswell#128"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/131#pullrequestreview-862973378", "body": ""}
{"title": "Add ktor server tracing", "number": 1311, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1311", "body": "We add the ability to trace ALL ktor server calls through plugin interception."}
{"title": "VSCode recent projects", "number": 1313, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313", "body": "Fetch recent projects from VSCode.\nHacky method of grabbing recent workspaces & folders from an internal storage.json file which represents the menu bar state.\nOriginal intention was to grab data from internal DB but ran into issues running sqlite3.\nNo UI at the moment, just informational popup from command.\n"}
{"comment": {"body": "Added sample picker to demonstrate opening new workspace in same window is possible.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/168704500-0f49adbf-4ba2-4abe-962f-812edcfd8010.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#issuecomment-1128280201"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974630818", "body": ""}
{"comment": {"body": "Should we prefix all of the other commands with `Unblocked: ` too for discovery?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r874205916"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974669472", "body": ""}
{"comment": {"body": "I think it's good practice.\r\n\r\nWe'll most likely need to audit all the commands before we send this out to customers.\r\nI don't expect this specific command to exist in the public release. This is more for debugging.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r874235159"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974672654", "body": ""}
{"comment": {"body": "Yeah we should be prefixing all our commands with 'Unblocked:', and we should remove the commands that we don't actually want end-users to use through the command palette.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r874237696"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974807604", "body": ""}
{"comment": {"body": "I'm not sure but we should maybe drop these changes if there's not an associated package.json change?  I'm guessing these are some fallout from trying to get sqlite working?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r874345873"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974809233", "body": ""}
{"comment": {"body": "I think `type-fest` has a `Get` and `GetPath` for this already?  Might be worth using? https://github.com/sindresorhus/type-fest/blob/main/source/get.d.ts", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r874347046"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974810217", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974814959", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974817554", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974818865", "body": ""}
{"comment": {"body": "hahah \ud83e\udd2a wonder how stable this is between VSCode releases...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r874353909"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-974819106", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-975719653", "body": ""}
{"comment": {"body": "\ud83d\ude22 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r874987217"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-975720006", "body": ""}
{"comment": {"body": "Yeah.. Let me clean this up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r874987542"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-975729187", "body": ""}
{"comment": {"body": "Nice. Was looking for a _.get alternative. Exactly what I want. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r874995965"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-975749232", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-975761508", "body": ""}
{"comment": {"body": "Maybe I'm not fully understanding but there are no examples of *how* to use this.\r\nDoesn't seem to actually help without using something like _.get?\r\n\r\nThe GET in type-fest seems useful to help add types to utilities that actually handle the logic.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r875018564"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#pullrequestreview-975779559", "body": ""}
{"comment": {"body": "Yeah I think it's really only valuable for typing?  Maybe someone out there has written a good type-safe Typescript-happy version of get?  Or just use the types from type-fest and keep your implementation?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1313#discussion_r875031442"}}
{"title": "Tiebreak recommended threads on lastMessageCreatedAt", "number": 1314, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1314", "body": "Sort by {rank, lastMessageCreatedAt}"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1314#pullrequestreview-974708294", "body": ""}
{"title": "Peter remove team view", "number": 1315, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1315", "body": "Small PR to dump the team members view. Placeholder text for now"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1315#pullrequestreview-974652081", "body": ""}
{"title": "Fix expiration logic", "number": 1316, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1316"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1316#pullrequestreview-974674105", "body": ""}
{"comment": {"body": "yesssssss", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1316#discussion_r874238763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1316#pullrequestreview-974674238", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1316#pullrequestreview-974674697", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1316#pullrequestreview-974717345", "body": ""}
{"title": "Anchor links in web extension", "number": 1317, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1317", "body": "Add links to file from anchor source point in web extension.\nCurrently navigates existing tab to new URL and highlights relevant code.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1317#pullrequestreview-974800968", "body": ""}
{"title": "Implement getThreadsArchived API", "number": 1318, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318", "body": "TODO in next PR...\n - [ ] refactor store, needed for search and pusher, by splitting in phases:\n      1. input validation (move to API layer)\n      1. get thread IDs only, using bespoke predicate\n      1. decorate\n      1. re-sort"}
{"comment": {"body": "got some refactoring and tests to do, but other than that is ready for review", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#issuecomment-1128275967"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#pullrequestreview-975762374", "body": ""}
{"comment": {"body": "woah how did this pass CI in the first place", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#discussion_r875019178"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#pullrequestreview-975767460", "body": ""}
{"comment": {"body": "I just ran `make lint` and it fixed this\r\nI think maybe because it's in `/test/`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#discussion_r875022873"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#pullrequestreview-975773159", "body": ""}
{"comment": {"body": "Do we need a test for the empty result case? I discovered that some APIs are not returning a lastModified when the result is empty and ifModifiedSince is not specified", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#discussion_r875026978"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#pullrequestreview-975778313", "body": ""}
{"comment": {"body": "will do", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#discussion_r875030597"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#pullrequestreview-975778599", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#pullrequestreview-975780937", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#pullrequestreview-975797208", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#pullrequestreview-975850079", "body": ""}
{"comment": {"body": "good review: turns out there were lots of bugs: https://github.com/NextChapterSoftware/unblocked/pull/1318/commits/5cab1dd3129cf0aeb941604d7f017594a8e181d7", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1318#discussion_r875082656"}}
{"title": "AddTelemetryForJdbcCalls", "number": 1319, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1319"}
{"title": "update", "number": 132, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/132"}
{"title": "Fix participant thread count in Hub", "number": 1320, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1320", "body": "fixed ...\n"}
{"comment": {"body": "Awesome thx! I had interpreted this as (2+) as in \"more than 2\" but this makes more sense. \n\nOne more change needed though: when the participant count is larger than 11 there won't be enough space to accommodate the extra character. \n\nOptions:\n\n(9+)\n\nMake circle larger (grow to fit)\n\nMake text smaller (shrink to fit)\n\n@benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1320#issuecomment-1128991036"}}
{"comment": {"body": "I'll have a think about it. My sense is if we can make double digits work, we're covered for 99.9% of cases.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1320#issuecomment-1129051492"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1320#pullrequestreview-974806269", "body": ""}
{"title": "Send notification emails to participants on thread creation", "number": 1321, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1321"}
{"comment": {"body": "> I don't get how this works though. why would we send invites to participants that are already in the team?\r\n\r\nIt's more like an email notification that they were added as a participant to the thread. \r\n<img width=\"381\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/168879625-c010521c-f821-4fa4-ae6b-e206915dab72.png\">", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1321#issuecomment-1129160555"}}
{"comment": {"body": "> > I don't get how this works though. why would we send invites to participants that are already in the team?\r\n> \r\n> It's more like an email notification that they were added as a participant to the thread.\r\n\r\nOn every thread?!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1321#issuecomment-1129166504"}}
{"comment": {"body": "> > I don't get how this works though. why would we send invites to participants that are already in the team?\r\n> \r\n> It's more like an email notification that they were added as a participant to the thread. <img alt=\"image\" width=\"381\" src=\"https://user-images.githubusercontent.com/13431372/168879625-c010521c-f821-4fa4-ae6b-e206915dab72.png\">\r\n\r\nI don't think we need this one actually. We need the email where they are not a member of Unblocked, which looks similar to this. Essentially invite by way of @mentioning. If you're already a member, we now have other ways of notifying (Hub App, digest emails). Originally these were all not in scope when this state was conceived. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1321#issuecomment-1129174075"}}
{"comment": {"body": "Hmm okay so this email is not needed? Closing this PR then. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1321#issuecomment-1129184193"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1321#pullrequestreview-975854116", "body": "minor feedback inline.\nI don't get how this works though. why would we send invites to participants that are already in the team?"}
{"comment": {"body": "Here's the problem: the only people that we have emails for are current team members. And there's no point in sending invite emails to people who are already in the team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1321#discussion_r875085444"}}
{"comment": {"body": "better to filter out the author from the participant TeamMembers, rather than filter out emails", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1321#discussion_r875087330"}}
{"comment": {"body": "at this point we have already hit the DB to construct ThreadInviteEmailData. let's move this check much earlier in the flow.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1321#discussion_r875089001"}}
{"title": "Make auth re-entrant", "number": 1322, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1322", "body": "Summary\nCurrent behaviour is not re-entrant, so if you back out of the auth flow you get stuck. The only way to fix this with the current UX (login page remains until you log in) is to make it re-entrant."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1322#pullrequestreview-975858741", "body": ""}
{"title": "Onboarding Installations Spec Change", "number": 1323, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323", "body": "Spec change to support GH App installation for an org.\nClients will provide a list of cloneURLs and the API service will return all the relevant organizations and their install status.\nClients will continue polling request until all the clone urls within the request match with a corresponding repo in the org's response.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-975945629", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-975954149", "body": "Partial review. Main changes I'd propose:\n1. rename as \"installations\"\n2. combine with existing installState api, which is doing exactly the same thing, just for a single repoUrl right now"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-975972549", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-976065155", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-976334940", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977674034", "body": ""}
{"comment": {"body": "why is this plural?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#discussion_r876399230"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977678030", "body": ""}
{"comment": {"body": "Replace all of these {displayName, teamId, provider, avatarUrl} fields with the `Team` object instead.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#discussion_r876402310"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977688497", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977689017", "body": ""}
{"comment": {"body": "Add second \"unknownRepos\" array.\r\n\r\nName, owner, url.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#discussion_r876410375"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977690768", "body": ""}
{"comment": {"body": "nvm, makes sense ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#discussion_r876411790"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977696613", "body": ""}
{"comment": {"body": "Can not do this as Team would be need to optional.\r\nThis doesn't work for our UX as we need displayName, even if Team isn't in Unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#discussion_r876416059"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977726249", "body": ""}
{"comment": {"body": "choosing to comment out the old code in this case, because it'll be pretty useful when implementing the replacement API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#discussion_r876439169"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977726383", "body": ""}
{"comment": {"body": "choosing to comment out the old code in this case, because it'll be pretty useful when implementing the replacement API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#discussion_r876439272"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977727497", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977729102", "body": "LGTM"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1323#pullrequestreview-977729826", "body": "Look good to me"}
{"title": "Update template with new font", "number": 1324, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1324", "body": "re: new font for branding"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1324#pullrequestreview-975990786", "body": ""}
{"title": "Add typed baggages", "number": 1325, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1325", "body": "This pr allows us to pass baggages down to spans while still maintaining type.\nTo maintain cross-platform data passing, the designers of opentelemetry prohibited baggage value types other than string.\nTo get around that, baggages now have type metadata.\nWhen a new span is created, it looks at any baggages in the context, and propagages the correctly typed baggage contents down."}
{"title": "Rename ThreadUnread fields", "number": 1326, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1326"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1326#pullrequestreview-976122430", "body": ""}
{"title": "Refactor ThreadInfo store", "number": 1327, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327", "body": "total rewrite, should be easier to consume and more efficient now"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#pullrequestreview-976122713", "body": ""}
{"comment": {"body": "@davidkwlam for search you just need to call this function on a list of `threadIds`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#discussion_r875283572"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#pullrequestreview-976127403", "body": ""}
{"comment": {"body": "Thanks, we ok to make this public?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#discussion_r875286910"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#pullrequestreview-976136179", "body": ""}
{"comment": {"body": "ya\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#discussion_r875293481"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#pullrequestreview-976233452", "body": ""}
{"comment": {"body": "I imagine channel poller will want to hit this function directly right? Is there a point in going past this to decorate things once we know if there are any \"Mine\" threads since last modified?\r\n\r\nIs there any further optimization we can do for the channel poller specifically? Count?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#discussion_r875372326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#pullrequestreview-976247670", "body": ""}
{"comment": {"body": "yeah exactly, the point of the refactor is that the poller should call _this_ function.\r\n\r\npass `requestedLimit=1` and a `modifiedSince`. can't do better than that performance-wise. the count won't do any better.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#discussion_r875384069"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1327#pullrequestreview-976295907", "body": ""}
{"title": "Add thread recommended store implementation", "number": 1328, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977208619", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977217494", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977220922", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977224182", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977225247", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977230553", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977254822", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977255207", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977433020", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1328#pullrequestreview-977460114", "body": ""}
{"title": "Add ability to store our service specific fields in api queries", "number": 1329, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1329", "body": "We want to store teamId etc. in api queries for honeycomb."}
{"title": "Auth implementation against stub provider", "number": 133, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133", "body": "Apologies for this doozy..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-863163078", "body": ""}
{"comment": {"body": "@richiebres -> main entry point for GitHub access_code and identity resolution", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r792356475"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-863164045", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-863835923", "body": "Overall lgtm but I'm no domain expert here... Going to depend a lot on @richiebres  for this one."}
{"comment": {"body": "For these 400s, I wonder if it would be useful to actually return a \"GeneralError\" model that includes the status code & more importantly, a client side error description?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r792852047"}}
{"comment": {"body": "Can we add a todo or comment here? \r\nWe should \"try\" to be a bit smarter and infer based on user agent.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r792853746"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-864421919", "body": ""}
{"comment": {"body": "For sure!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793287751"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865234559", "body": ""}
{"comment": {"body": "I'd rather not reinvent the wheel. Use HTTP status codes and pass a custom `message: String`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793860527"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865239255", "body": ""}
{"comment": {"body": "Would also remove all the custom error response objects like `UnauthorizedError` that can conflict with the status code. For example, custom error response objects make it possible to say this, which is gibberish:\r\n```yaml\r\n        '201':\r\n          $ref: '#/components/responses/NotFoundError'\r\n        '409':\r\n          $ref: '#/components/responses/UnauthorizedError'\r\n```\r\n\r\nBetter to just have an ErrorResponse and use http status codes, where ErrorResponse has a message.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793863969"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865244005", "body": "cool  \nfew comments, but mostly minor. Don't understand the repoUrl thing (https://github.com/Chapter2Inc/codeswell/pull/133#discussion_r793919901)."}
{"comment": {"body": "Let's create a separate app for each env, meaning move to dev.conf.\r\n(Totally fine to do in follow up.)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793867387"}}
{"comment": {"body": "General comment on `AuthenticationStateModel`. These records do not expire and are not cleaned up right now. I think once we have a Redis instance we should probably move to that store instead with ttl. Is that the idea?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793879176"}}
{"comment": {"body": "one thing in particular worth checking here is that the email is non null. this demonstrates that we're not just fetching public Information (most of the user profile is public), but we're also fetching PII that requires auth (email).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793890218"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865254488", "body": ""}
{"comment": {"body": "Agreed, I'll dump these", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793874590"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865291971", "body": ""}
{"comment": {"body": "Added", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793900689"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865292126", "body": ""}
{"comment": {"body": "Gone", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793900786"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865297848", "body": ""}
{"comment": {"body": "Is this used to determine what URL to jump to at the end of the flow?  I don't know that the service should be defining this, it feels like something the client should define, since it understands its own URL schemes?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793904961"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865318700", "body": ""}
{"comment": {"body": "Imagine this flow:\r\nvscode -> Browser -> Api Service -> GitHub OAuth -> Web Service -> Load Web App -> Api Service (js)\r\n\r\nThe experience I'm imagining is that the web app (dashboard?) will provide some optimal landing UX depending on the origin client type, in this case vscode. What is the best way to shuttle that information around? Currently it's being set as a server cookie, but it could be a client cookie or in the url state parameter", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793920148"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865320786", "body": ""}
{"comment": {"body": "In other words the oauth redirect url to the web-service does not currently indicate the client origin", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793921694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865323757", "body": ""}
{"comment": {"body": "\ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793923915"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865325419", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865331074", "body": ""}
{"comment": {"body": "Absolutely", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793929056"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865331286", "body": ""}
{"comment": {"body": "I'll add a comment at the top of the class definition", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793929205"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865333883", "body": ""}
{"comment": {"body": "I understand what you're saying here.  One thing I'd suggest (and doesn't block this PR or anything) is that the final jump URL should be specified by the originating client (`vscode` in your chain above), and passed to the browser.  The individual clients are responsible for their own URL schemes.  I think this reduces the dependency logic between each part of the system.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793931141"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865338113", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865350119", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865355626", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865393152", "body": ""}
{"comment": {"body": "Do you mean the oauth redirect url? Because after that the web app is back in control, but we're also limited in what can be carried through the redirect (only `state` and `code` parameters). In Skywagon we often used the `state` parameter to reference a state object on the backend, which can be populated by both the service and the client through the api. In this implementation we have essentially the same thing, so if we want the ability for the client to set some origin data up front and have it passed back through the exchange API, we can certainly do that. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r793973599"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865396759", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865398050", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865431322", "body": ""}
{"comment": {"body": "No I mean the final step in the chain after 'Api Service (js)' -- ie, do we jump to a particular dashboard URL, go to a particular vscode:// URL, etc.  We could keep that in the `state` param, or alternatively host it in the browser local storage (ie, the browser would store it in step 2 above, and restore it after the last step)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r794001047"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865439909", "body": ""}
{"comment": {"body": "Yup I think we're saying the same thing - once the web app has control after oauth we have lots of things we can do. I would suggest that since we're in js land we could codify this in an API as a response object and keep it as part of the flow. Referencing local storage feels brittle to me for some reason", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r794007038"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865442090", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865468910", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865470396", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865475681", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865476762", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#pullrequestreview-865480398", "body": ""}
{"comment": {"body": "added", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/133#discussion_r794035454"}}
{"title": "Add sofia pro fonts", "number": 1330, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1330", "body": "Dark Mode\n\n\n\n\n\n\n\nLight Mode\n\n\n\n\n\n\n"}
{"comment": {"body": "Do we really need to explicitly set the font everywhere?  We can't set the font family at the top level of the views or something?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1330#issuecomment-1129403267"}}
{"comment": {"body": "I guess I'm thinking of something like this... though hopefully without breaking parts of Xcode :) https://stackoverflow.com/a/61546428", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1330#issuecomment-1129404750"}}
{"comment": {"body": "> Do we really need to explicitly set the font everywhere? We can't set the font family at the top level of the views or something?\r\n\r\nI cleaned it up. There are some places that have to explicitly reference the \"special\" font cases, but maybe we can figure something out for that as well...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1330#issuecomment-1129446920"}}
{"comment": {"body": "Nice! \ud83c\udf89 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1330#issuecomment-1129568679"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1330#pullrequestreview-976218122", "body": ""}
{"title": "Add operationId", "number": 1331, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1331"}
{"title": "Remove useless transaction", "number": 1332, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1332", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1332#pullrequestreview-976245539", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1332#pullrequestreview-976245606", "body": ""}
{"title": "Persist PR Open related fields on thread", "number": 1334, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1334"}
{"title": "Fix multiple background listeners", "number": 1335, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1335", "body": "Running into issue where multiple background listeners were being registered.\nThis meant that whenever the client send a message to the background, events were being doubled/tripled/etc...\nThis was causing multiple auth windows to appear and rate limiting our auth: Fixes \nWe should only ever have a single background listener per port type."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1335#pullrequestreview-977614856", "body": ""}
{"title": "Setup outline for installation wizard", "number": 1336, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1336", "body": "Setup basic outline for installation wizard.\nHave not spent enough time into thinking about wizard architecture to make this generic. My hope was to keep these wizards simple for now and then refactor out a generic wizard once we have a better understanding of functionality we need."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1336#pullrequestreview-983976986", "body": ""}
{"title": "Add new office IP to allow list", "number": 1337, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1337"}
{"title": "DatabaseTests should catch and rerun only PSQLException", "number": 1338, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1338", "body": "Not working..."}
{"comment": {"body": "Richie is handsome", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1338#issuecomment-1133346317"}}
{"comment": {"body": ":( ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1338#issuecomment-1149209447"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1338#pullrequestreview-977401266", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1338#pullrequestreview-980834617", "body": ""}
{"title": "Cleanup code", "number": 1339, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1339"}
{"title": "Create sourcemarks/annotations api stubs", "number": 134, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134", "body": "Adds\n- ability to put/get a source mark\n- ability to get chats for a source mark\nRemoves\n- ability to list all chats (sourcemarkId query param is required)\n- ability to list all messages (chatId query param is now required)\nStill TBD:\n- Sourcemark or Annotation? Let's discuss in person"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-863858939", "body": "Don't worry about the naming, as that's independent of this change. I'll take care of naming later, once I figure out what to do."}
{"comment": {"body": "not a list", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#discussion_r792897899"}}
{"comment": {"body": "not a list", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#discussion_r792897998"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-863860433", "body": ""}
{"comment": {"body": "We can relax this later if we want to allow other query params, but I think we'll want to enforce at least one query is provided.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#discussion_r792869720"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-863860772", "body": ""}
{"comment": {"body": "Ditto https://github.com/Chapter2Inc/codeswell/pull/134/files#r792869720", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#discussion_r792869996"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-863911881", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-863999511", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-863999650", "body": ""}
{"comment": {"body": "See: https://github.com/Chapter2Inc/codeswell/pull/134#discussion_r792966027", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#discussion_r792966197"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-863999740", "body": ""}
{"comment": {"body": "See: https://github.com/Chapter2Inc/codeswell/pull/134#discussion_r792966027", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#discussion_r792966297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-864157465", "body": ""}
{"comment": {"body": "Should there be a requestBody for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#discussion_r793087047"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-864160813", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-864163528", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-864170961", "body": ""}
{"comment": {"body": "erm yes haha, thanks.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#discussion_r793095946"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#pullrequestreview-864178536", "body": ""}
{"comment": {"body": "Fixed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/134#discussion_r793101477"}}
{"title": "Update secrets", "number": 1340, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1340"}
{"title": "update", "number": 1341, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1341"}
{"title": "Fix up secrets config", "number": 1342, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1342"}
{"title": "Fix build", "number": 1343, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1343", "body": "How did this pass CI?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1343#pullrequestreview-977561507", "body": ""}
{"title": "Lock to version", "number": 1344, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1344"}
{"title": "update", "number": 1345, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1345"}
{"title": "Persist PR creator on thread", "number": 1346, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346", "body": "Saves the PR author.\nWill be needed for:\n1. recommendation engine\n2. making the PR author a participant()"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#pullrequestreview-979351073", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#pullrequestreview-979353027", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#pullrequestreview-979364120", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#pullrequestreview-979364658", "body": ""}
{"comment": {"body": "Probably makes sense to add this to the `updateThread` function to set the PR author if null so that there is no need to drop threads to re-ingest", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#discussion_r877613028"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#pullrequestreview-979367613", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#pullrequestreview-979368764", "body": "LGTM, but updating updateThread will make it so we don't need to drop threads to re-import"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#pullrequestreview-979372877", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/1346/commits/f67ae6b6df56d2d9da19844f967da90669483402", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#discussion_r877619798"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#pullrequestreview-979373198", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1346#pullrequestreview-979375735", "body": ""}
{"title": "Use ThreadInfo API for VSCode", "number": 1347, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347", "body": "(This builds on top of https://github.com/NextChapterSoftware/unblocked/pull/1309)\nUse ThreadInfo APIs in VSCode.\nSome caveats:\n* Similar to the dashboard, this breaks search.\n* For the editor/gutter integration, the behaviour isn't ideal: when we open the file, we fetch all threads for sourcemarks in the file, and then we begin monitoring those threads.  I think we can do this, but should maybe crank the polling frequency in this case way down (once every 20/30 seconds or something) -- the only data we pull from this is the title and participants, which change very rarely."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-977611751", "body": ""}
{"comment": {"body": "I made a separate store for publishing the set of threads relevant to the VSCode workspace (ie, for all repos in the workspace).  Uses the same lower-level stores, but means we don't duplicate this logic everywhere.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r876351796"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-977734805", "body": ""}
{"comment": {"body": "is there a reason why we changed from initialized to ready?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r876446414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978878487", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978878659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978879348", "body": ""}
{"comment": {"body": "I thought it made it more clear, especially when other \"initialized\" states were added.  We can rename it if we want to?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r877269707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978888097", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r877276093"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978912262", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r877293817"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978924882", "body": ""}
{"comment": {"body": "is this something that needs to be addressed before merging?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r877302730"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978925356", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978925695", "body": "lgtm pending the two question marks"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978934774", "body": ""}
{"comment": {"body": "No -- search will be broken for a little while, until the API is available.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r877309970"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978934965", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978952403", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r877320062"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978953719", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-978992655", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r877351126"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#pullrequestreview-984160794", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1347#discussion_r881176359"}}
{"title": "WIP testing docker based workers. do NOT merge", "number": 1348, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1348"}
{"title": "Add traces for toher services", "number": 1349, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1349"}
{"title": "Eks install calico", "number": 135, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/135", "body": "Try adding network policy for API service again\nChanged EKS cluster node type because t2 instances are not available in some regions like us-west-2d\nAdded DNS allow policy \nModified deny all policy to allow cluster egress traffic \nAll of these changes except the apiservice/.helm/service/templates/network-policy.yaml have been verified,"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/135#pullrequestreview-864045308", "body": ""}
{"title": "Implements fat thread push channels", "number": 1350, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977693297", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977693855", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977694517", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977696284", "body": ""}
{"comment": {"body": "regex be gone!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#discussion_r876415872"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977701251", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977703730", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977710167", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977711165", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977768485", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977833413", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977929400", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977929513", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977932118", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977932812", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1350#pullrequestreview-977937093", "body": ""}
{"title": "increase helm deployment timeout", "number": 1351, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1351", "body": "Deployments are timing out in prod. This should give a little more time to helm so the atomic rollover could finish."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1351#pullrequestreview-977695235", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1351#pullrequestreview-977695466", "body": ""}
{"title": "Add onboarding templates and assets folder", "number": 1352, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1352"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1352#pullrequestreview-977708111", "body": ""}
{"title": "Open discussion in VSCode from Hub", "number": 1353, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1353", "body": "Waiting on hub app to implement pushing to fully test this out.\nSets up new grpc stream with Hub app whenever repos are updated in VSCode."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1353#pullrequestreview-980735858", "body": ""}
{"title": "Update font to sofia-pro and update reply count", "number": 1354, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1354"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1354#pullrequestreview-977745148", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1354#pullrequestreview-977745178", "body": ""}
{"title": "Order participants by createdAt in API", "number": 1355, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1355"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1355#pullrequestreview-977747030", "body": ""}
{"comment": {"body": "this is the only changed line\r\n\r\n(everything else is whitespace changes)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1355#discussion_r876455009"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1355#pullrequestreview-977767722", "body": ""}
{"title": "VSCode Installation Store", "number": 1356, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1356", "body": "Basic Installation Store.\nShould be used to populate \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1356#pullrequestreview-979124303", "body": ""}
{"comment": {"body": "This is fine for now, but ultimately I don't think the structure of this is correct -- it is joining stream data in a way that's unusual.  I think once we want to make the installation stream automatically refreshing, we should do something like: Make the InstallationStore stream an xstream Producer.  On the `start` signal (ie, someone cares about installation status), we sign up for AuthStore updates, and also set up a refresh timer.  On `stop` signal, we stop.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1356#discussion_r877439550"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1356#pullrequestreview-979124376", "body": ""}
{"title": "Add identity id to traces", "number": 1357, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1357"}
{"title": "Test for /mine lastModified", "number": 1358, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1358", "body": "Seems solid afaict..."}
{"title": "Fix sampling", "number": 1359, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1359"}
{"title": "configured dev cluster to use KMS for secret encryption", "number": 136, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/136", "body": "Added docs for KMS key generation\nAdded KMS key to Dev cluster for Kube secrets encryption \nEnabled KMS backed secret encryption in Dev"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/136#pullrequestreview-864182696", "body": ""}
{"title": "Revert main panel to use macOS default fonts", "number": 1360, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1360"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1360#pullrequestreview-977879041", "body": ""}
{"title": "Integrates with fat threads", "number": 1361, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361", "body": "Currently the \"dumb\" version that works like this:\n\nFetch /threads/mine with limit 25\nSubscribe to /threads/mine with latest modified at\nSubscribe to every /thread/:id with latest modified at\nIf any of those fire, repeat from step 1\n\nThis works fine and takes care of the deleted/archivedAt case, but obviously does not allow for any scrolling past 25 items, and is not as efficient as if we called /threads/mine with a latest modified value. \nIt's still an improvement over what we had previously though.\nNext iteration will add infinite scrolling and do things the \"right\" way:\n- GET /threads/minelimit=N\n- subscribe to each of the N threads by id, using modifiedSince\n- subscribe to /threads/mine (no limit) using modifiedSince\n- fetch more from threads.last().cursor when the end of the list is visible to user"}
{"comment": {"body": "> subscribe to each of the N threads by id, using modifiedSince\r\n\r\nDo we need to do this?  I would have thought subscribing to `/threads/mine` would be sufficient?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#issuecomment-1131908906"}}
{"comment": {"body": "> > subscribe to each of the N threads by id, using modifiedSince\r\n> \r\n> Do we need to do this? I would have thought subscribing to `/threads/mine` would be sufficient?\r\n\r\nApparently deleted and archived threads are filtered from the `/threads/mine` response, so to capture those we need to subscribe on all the visible threads", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#issuecomment-1132026996"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#pullrequestreview-977941877", "body": ""}
{"comment": {"body": "Doing this because again the generated code lets us down by base64 encoding all the query params...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#discussion_r876599929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#pullrequestreview-978001524", "body": ""}
{"comment": {"body": "oh man, totally defeats the point of code-gen", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#discussion_r876643421"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#pullrequestreview-978834914", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#pullrequestreview-978837726", "body": ""}
{"comment": {"body": "Sort of.. it still does all the networking and body serialization. I'll eventually re-write the templates...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#discussion_r877241539"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#pullrequestreview-978849523", "body": "Logic looks good. Dont know enough swift to comment on language aspects"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#pullrequestreview-980434106", "body": ""}
{"comment": {"body": "hi Pete\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#discussion_r878358758"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#pullrequestreview-980434365", "body": ""}
{"comment": {"body": "sdgsdfg \n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#discussion_r878358969"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#pullrequestreview-980434411", "body": ""}
{"comment": {"body": "sdfsd\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#discussion_r878359019"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#pullrequestreview-980434414", "body": ""}
{"comment": {"body": "sdfsd\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1361#discussion_r878359023"}}
{"title": "Recommend thread on creation", "number": 1362, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1362", "body": "Changes here are just plumbing really:\n- Annotate threads, when created from API and PRInjestion, with ThreadRank.\n  The ThreadRank is random gibberish for now.\n- Moved event notifications outside the DB transaction to prevent sending\n  retries and prevent sending events if transaction rolled-back.\nNext:\n- use ingested PR data to generate recommendation model\n- calculate ThreadRank from recommendation model\nFuture:\n- decouple libraries, creating separately deployed recommendation service\n- decouple ThreadRank calculation from thread creation runtime by sending async event to SQS instead of recommending inline"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1362#pullrequestreview-978092679", "body": ""}
{"title": "Unblocked installer", "number": 1363, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1363"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1363#pullrequestreview-978885166", "body": ""}
{"comment": {"body": "Do you know if failures here will bubble up and be reported in the pkg installer UI?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1363#discussion_r877273963"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1363#pullrequestreview-978888483", "body": ""}
{"comment": {"body": "test test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1363#discussion_r877276383"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1363#pullrequestreview-978897068", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1363#pullrequestreview-979032606", "body": ""}
{"comment": {"body": "We can have the installer fail by exiting the postinstall script with non-zero code, but I don't think it's possible to surface a custom message (will need to dig more to confirm)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1363#discussion_r877380274"}}
{"title": "Quick fix for thread pusher issue", "number": 1364, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1364", "body": "TODO in follow up:\n- [ ] API tests\n- [ ] move modifiedAt concatenation logic down to the ThreadInfoStore"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1364#pullrequestreview-978981788", "body": ""}
{"comment": {"body": "does this need unread as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1364#discussion_r877341487"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1364#pullrequestreview-978984661", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1364#pullrequestreview-978985647", "body": ""}
{"comment": {"body": "no. recommended threads don't have an unread concept because you are not participating in the thread", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1364#discussion_r877344061"}}
{"title": "temp fix while I address a bug", "number": 1365, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1365", "body": "This change causes Github action to request a spot instance with the same size as the current on-demand instance type specified in our config. This is just a temporary fix while I address a bug"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1365#pullrequestreview-978995607", "body": ""}
{"title": "Fix unread sorting bug", "number": 1366, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1366", "body": "Probably needs a test..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1366#pullrequestreview-978993574", "body": ""}
{"title": "Fix up transaction contexts", "number": 1367, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1367"}
{"title": "Reduce pusher events", "number": 1368, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1368"}
{"title": "Only recommended threads for non-thread participants", "number": 1369, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1369", "body": "I had logic completely backwards."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1369#pullrequestreview-979109980", "body": ""}
{"title": "Auto accept baselines in main", "number": 137, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/137", "body": "Removing GH integration led to issues transferring accepted baselines from branches to main.\nNow, auto accept changes as new baselines for the main branch.\nWe are making this decision with the assumption that reviewers have already approved baselines in the PRs before merging to main.\nMore info: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/137#pullrequestreview-864192975", "body": ""}
{"title": "Dont allow thread creation without title", "number": 1370, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1370", "body": "We could probably have a nicer UI (i.e. show an error state on submit instead of just disabling) but this should do for now"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1370#pullrequestreview-979130701", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1370#pullrequestreview-979134018", "body": ""}
{"comment": {"body": "so we require both title and description.\r\ncan we make the description optional instead?\r\nor default the title to the description content", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1370#discussion_r877448285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1370#pullrequestreview-979142315", "body": ""}
{"comment": {"body": "I'd probably prefer the latter (default the optional title to the description) but that would need a bit more work translating the message content into text just for the title. Submit an issue that we can address later? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1370#discussion_r877455853"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1370#pullrequestreview-979156159", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/issues/1371", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1370#discussion_r877465393"}}
{"title": "Only indent the list if there is at least one unread", "number": 1372, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1372", "body": "Small visual change.\nHas Unreads\n\nNo Unreads\n"}
{"title": "Accidentally inverted merge logic for notifications", "number": 1373, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1373", "body": "One liner"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1373#pullrequestreview-979185101", "body": ""}
{"title": "Fix hub anchor point", "number": 1374, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1374", "body": "Hub popover anchor point is now centered\n"}
{"title": "Cleanup notificaiton classes", "number": 1375, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1375"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1375#pullrequestreview-979222934", "body": ""}
