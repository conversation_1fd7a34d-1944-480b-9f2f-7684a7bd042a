import dataclasses
from typing import List, Set

import pandas as pd

from cluster_processor_image.expert.git_contributors_extractor import GitContributorsExtractor
from cluster_processor_image.dataframe_constants import DOC_TOPIC_COLUMN, FILE_PATH_COLUMN, DOC_TOPIC_EXPERTS_COLUMN
from cluster_processor_image.expert.git_dataframe_constants import GIT_EMAIL_COLUMN, GIT_AUTHOR_COLUMN
from cluster_processor_image.expert.topic_expert_types import TopicExpert, TopicExperts


class TopicsExpertsExtractor:
    def __init__(self, repo_dir: str):
        self.repo_dir = repo_dir

    def extract_top_authors_for_topics(self, topic_df: pd.DataFrame, top_n: int = 5) -> pd.DataFrame:
        all_topic_experts = {}

        for topic, file_paths in topic_df.groupby(DOC_TOPIC_COLUMN)[FILE_PATH_COLUMN]:
            git_contributors_extractor = GitContributorsExtractor(self.repo_dir)
            top_authors_df = git_contributors_extractor.get_top_authors_for_files(*file_paths.to_list(), top_n=top_n)

            topic_experts: Set[TopicExpert] = set()
            # Iterate over the rows of the DataFrame
            for index, row in top_authors_df.iterrows():
                # Create a TopicExpert instance for each row
                topic_expert = TopicExpert(
                    email=row[GIT_EMAIL_COLUMN],
                    name=row[GIT_AUTHOR_COLUMN],
                )
                topic_experts.add(topic_expert)

            all_topic_experts[topic] = dataclasses.asdict(TopicExperts(name=topic, experts=list(topic_experts)))

        topic_df[DOC_TOPIC_EXPERTS_COLUMN] = topic_df[DOC_TOPIC_COLUMN].map(all_topic_experts)
        return topic_df
