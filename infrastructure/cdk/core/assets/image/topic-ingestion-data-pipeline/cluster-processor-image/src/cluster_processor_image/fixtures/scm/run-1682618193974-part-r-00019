{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904781620", "body": ""}
{"comment": {"body": "TBD: Needs a larger conversation about background jobs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822942699"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904782353", "body": ""}
{"comment": {"body": "Don't care whether they're the host or not, joining a channel that you're already in should be a no-op", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822943207"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-904782956", "body": ""}
{"comment": {"body": "Channel already created, no-op", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#discussion_r822943625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/510#pullrequestreview-912602399", "body": ""}
{"title": "Improve powerml utilities", "number": 5100, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5100"}
{"title": "Add PersonsApiDelegateImpl integration tests", "number": 5101, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5101", "body": ""}
{"title": "nFix index accessor", "number": 5102, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5102"}
{"comment": {"body": "Closing for this https://github.com/NextChapterSoftware/unblocked/pull/5103#pullrequestreview-**********", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5102#issuecomment-1452438470"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5102#pullrequestreview-1322473019", "body": ""}
{"title": "Fix first message lookups", "number": 5103, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5103"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5103#pullrequestreview-**********", "body": ""}
{"title": "Remove identityName from logs", "number": 5104, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5104"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5104#pullrequestreview-**********", "body": "good call"}
{"title": "Add Cluster Auto-Scaler to our Kubernetes infra", "number": 5105, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5105", "body": "Created service accounts for cluster auto-scaler\nDeployed service accounts to Dev and Prod\nDeployed autoscaler to both Dev and prod. They are working as expected.\n\nI'll keep monitoring them to make sure we don't over-provision"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5105#pullrequestreview-**********", "body": ""}
{"title": "Installations V2 Spec", "number": 5106, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5106", "body": "Proposed Installations V2 Spec\n\nText is now provided by backend\nInstall URL is optional. Not necessary for BB or GL\nIntroduce new API to confirm BB & GL installations\n\nSee also\n"}
{"comment": {"body": "![CleanShot 2023-03-02 at 13 47 03@2x](https://user-images.githubusercontent.com/1553313/*********-01b3eacd-a9be-43fe-8354-48a5277eaecd.png)\r\n\r\nHave not planned for this text yet... Any thoughts on where this should live? I don't think it belongs on the installation model as this is one layer up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5106#issuecomment-1452593260"}}
{"comment": {"body": "API compatibility check is failing:\r\n```\r\n./gradlew :api:test\r\n```\r\n\r\nbut I don't understand how this is possible, because the installationId field never existed before:\r\n```\r\n    --------------------------------------------------------------------------\r\n    --                            What's Changed                            --\r\n    --------------------------------------------------------------------------\r\n    - POST   /installations\r\n      Return Type:\r\n        - Changed 200 OK\r\n          Media types:\r\n            - Changed application/json\r\n              Schema: Broken compatibility\r\n              Missing property: [n].installationId (string)\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5106#issuecomment-1455552929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5106#pullrequestreview-1324683568", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5106#pullrequestreview-1326981326", "body": ""}
{"title": "Integrate APIs into new onboarding command", "number": 5107, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107", "body": "Note that we've run a migration so that all existing users will have hasDismissedToast set to true, so no existing users should see this UI, only newly onboarded users. \nIf you'd like to test this (i.e. on yourself), there is a Reset onboarding states button on the person page in the admin console. If you do this, make sure to toggle on the Unblocked: Tutorial Wizard so that the hub knows that you've properly onboarded."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1322804061", "body": ""}
{"comment": {"body": "polling frequency in the sidebar", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#discussion_r1123816199"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1324790783", "body": ""}
{"comment": {"body": "Might want to join this with the `useEffect` block above, as they're both basically responsible for updating the state as the onboarding state changes and as user actions occur", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#discussion_r1125170718"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1324792761", "body": ""}
{"comment": {"body": "There is a chance we might double-subscribe here -- we should probably check to see if we are already subscribed/listening, and if so take no action.  For instance, I'm not sure if signing up for visibility events publishes an event to the callback immediately or not.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#discussion_r1125172853"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1324802062", "body": ""}
{"comment": {"body": "I think this can be dropped quite a bit lower here?  Once every 20 seconds or something?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#discussion_r1125176903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1324804382", "body": ""}
{"comment": {"body": "Or alternatively -- we only need to fetch these values until we've verified that the user has completed onboarding... if we determine all steps are complete we can drop this stream...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#discussion_r1125177985"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1326912214", "body": ""}
{"comment": {"body": "I remembered why these are split the way they are. \r\n\r\nWe don't want the first useEffect block to run every time, just in cases when there is no expanded item (aka activeItem) -- most of the time this is on load. Thus the first block has a `if (!activeItem && ...` block before any state setting code runs. This is because when the user starts clicking around, we don't want to override their selections which is what the block would do given the dependencies. \r\n\r\nThe second block is for when there is an expanded item but given a user action, the item has been completed and therefore we need to manually collapse that item and open a new one. The reason this block is only concerned with collapsing and why we set it back to undefined instead of another enum is because we don't know at this point which actions they've already completed. So instead of assuming some order of actions that the user has taken, we just set the activeItem back to undefined and let the first useEffect block worry about the ordering.\r\n\r\nIt's all a little nebulous so I'm happy to chat in person about this to explain it a bit more. There's probably a world where we do collapse all of this logic into one very big useEffect block but I'd argue that this way is at least a bit more legible and digestible, given the context of why they are split the way that it is.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#discussion_r1126826648"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1326958603", "body": ""}
{"comment": {"body": "I added a null check above this line to unsubscribe if it already exists.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#discussion_r1126847728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1326971758", "body": ""}
{"comment": {"body": "I thought about this too; I didn't know if we could drop a stream depending on its returned body, and if it's a part of a combined stream? Do we have an example of that in the code anywhere? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#discussion_r1126858321"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1327239293", "body": ""}
{"comment": {"body": "I can think of a few ways of building a nice utility for this, but it will take a little effort.  Let's get this in and I can give you some ideas", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#discussion_r1127065787"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5107#pullrequestreview-1327242149", "body": ""}
{"title": "Remove no longer needed code", "number": 5108, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5108", "body": "I've run this and dropped the columns in both dev and prod."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5108#pullrequestreview-1322962994", "body": ""}
{"title": "Add service region config", "number": 5109, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5109"}
{"title": "Wire up snippets", "number": 511, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/511"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/511#pullrequestreview-904803300", "body": ""}
{"title": "Fix disabled button after editing", "number": 5110, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5110"}
{"comment": {"body": "possible to add automated test?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5110#issuecomment-1452727521"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5110#pullrequestreview-1322884605", "body": ""}
{"comment": {"body": "This is maybe fine to fix this short term, but we almost certainly should be looking into simplifying all of this:\r\n\r\n* We call `checkForInvites` in several other places -- are we sure that all the code paths end with us resetting this state?\r\n* Can we move the state transitions (to \"disabled\" and \"enabled\") into the same code block so it's easier to follow?  Having state transitions in separate parts of code like this is going to end up with bugs\r\n* I don't really understand how this invite state works, but should we be tracking this as a different state variable then \"sending message\" ?  We're not really sending a message at this point?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5110#discussion_r1123872147"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5110#pullrequestreview-1322899698", "body": ""}
{"comment": {"body": "This is OK but would it make sense to just move this into `onSendMessage` so we don't set this state in two places, and so both state transitions (to true, and then to false) are in the same code block?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5110#discussion_r1123881991"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5110#pullrequestreview-1322899890", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5110#pullrequestreview-1322904376", "body": ""}
{"comment": {"body": "it doesn't hit onSendMessage until after the invite modal is resolved. there's a slight subtlety here, but I think the ideal behaviour is that the Comment button is disabled when the invite modal pops up (which wouldn't be the case if we moved the state setting to inside the function)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5110#discussion_r1123884684"}}
{"title": "Disable auto triggering of topic generation during bulk ingestion", "number": 5111, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5111", "body": "When a new team is onboarded, we trigger this for each repo during bulk ingestion. Ideally we only want to do this once after all pull requests have been ingested. \nLet's disable this for now while I come up with a better approach."}
{"title": "chore(deps): update definitelytyped", "number": 5112, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5112", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/chrome (source) | ^0.0.218 -> ^0.0.219 |  |  |  |  |\n| @types/node (source) | 18.14.2 -> 18.14.4 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Immortal: This PR will be recreated if closed unmerged. Get config help if that's undesired.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Introduce local stack GitHub Cloud App", "number": 5113, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5113"}
{"title": "Deal with relative future times", "number": 5114, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5114"}
{"title": "Add pusher region", "number": 5115, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5115"}
{"title": "getting sick of crazy amount of service initialization code", "number": 5116, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5116"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5116#pullrequestreview-1324465270", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5116#pullrequestreview-1324470209", "body": "impressive Rashin! thank you"}
{"title": "Debug logs for token refresh", "number": 5117, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5117"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5117#pullrequestreview-1324679715", "body": ""}
{"title": "Move AuthSidebar to Shared", "number": 5118, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5118", "body": "Moves AuthSidebar into shared/ide\nMajority of this PR is moving code around."}
{"title": "SCM no-auth APIs for Bitbucket and GitLab to support installation flow", "number": 5119, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5119"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5119#pullrequestreview-1324679394", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5119#pullrequestreview-1324679474", "body": ""}
{"comment": {"body": "Broadcast messages endpoint is public, even for self-hosted servers:\r\nhttps://docs.gitlab.com/ee/api/broadcast_messages.html\r\n\r\nExamples:\r\n```sh\r\ncurl 'https://gitlab.ethz.ch/api/v4/broadcast_messages?per_page=1'\r\ncurl 'https://gitlab.inria.fr/api/v4/broadcast_messages?per_page=1'\r\ncurl 'https://gitlab.freedesktop.org/api/v4/broadcast_messages?per_page=1'\r\ncurl 'https://gitlab.gnome.org/api/v4/broadcast_messages?per_page=1'\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5119#discussion_r1125077716"}}
{"title": "Split out SourceMarkProvider", "number": 512, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/512", "body": "next:\n- (Dave) make API client\n- (Dave) plugin the API -- unlocks the ability for the SourceMark application to run against DEV / PROD\n- (Dave) use modifiedSince -- more efficient\n- (Richie) cache the content in memory (on the task)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/512#pullrequestreview-904794954", "body": ""}
{"comment": {"body": "Your stuff goes here Dave", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/512#discussion_r822952307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/512#pullrequestreview-904805310", "body": ""}
{"title": "Trigger topic generation when pull request ingestion is complete for newly onboarded teams", "number": 5120, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120", "body": "Adds a new job to the topic service that finds triggers topic generation for teams where the following are true:\n\ncreated in the last 7 days\nhave completed* pull request ingestion for all repos\nhave not previously had topic generation triggered\n\n*The ingestion flags may be set to true but we might still be processing ingestion events off the queue. However, for the purposes of topic ingestion this is good enough since we should have enough data for topic generation."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120#pullrequestreview-1324783508", "body": ""}
{"comment": {"body": "My understanding is that PR ingestion has taken days in the past. Is that still the case for the largest team?\r\n\r\nAssuming that topic generation is idempotent and reentrant, then we could run topic generation many times for a team at progress intervals until ingestion has completed. One of:\r\n- a time based interval of 10m, 30m, 1h, 4h, 8h, ...\r\n- a thread count interval of 10, 100, 500, 1000, 1500, ...\r\n\r\nSame for recommendation trigger.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120#discussion_r1125163258"}}
{"comment": {"body": "Maybe I'm misreading, but it seems this will re-trigger topic generation every 30 seconds for every team that has completed ingestion for all its repos. I think we need a stopping condition other than \"it's been 7 days\".", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120#discussion_r1125165242"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120#pullrequestreview-1324786151", "body": ""}
{"comment": {"body": "The other stopping condition is whether we've triggered an execution. If there is at least one, then we skip. I can reduce that to teams created in the las 3 or 1 days if that's better.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120#discussion_r1125166310"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120#pullrequestreview-1324786852", "body": ""}
{"comment": {"body": "> Assuming that topic generation is idempotent and reentrant, then we could run topic generation many times for a team at progress intervals until ingestion has completed.\n\nI'm hoping to avoid calling out to PowerML multiple times here since that could get expensive but @rasharab might have an opinion here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120#discussion_r1125167126"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is a file comment.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5120#discussion_r1135904532"}}
{"title": "Fix Jetbrains token Provider", "number": 5121, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5121"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5121#pullrequestreview-**********", "body": ""}
{"title": "Rev OnboardingStatus models", "number": 5122, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5122", "body": "Rm unnecessary person id from OnboardingStatus models\nAdd hasDismissedToast to new models"}
{"comment": {"body": "Are we likely to add more onboarding states? If so consider using this PR instead which uses PATCH to avoid re-breaking the api:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/5172", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5122#issuecomment-**********"}}
{"comment": {"body": "@richiebres @kaych we should definitely consider going in that direction. Seems crazy to have to rev the API each time an optional flag is added.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5122#issuecomment-1460379711"}}
{"comment": {"body": "Closing (addressed in https://github.com/NextChapterSoftware/unblocked/pull/5172).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5122#issuecomment-1460771089"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5122#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5122#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5122#pullrequestreview-**********", "body": ""}
{"comment": {"body": "is the diff just changing this to a PATCH ? @richiebres ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5122#discussion_r1129699557"}}
{"title": "Repo URL parsing must take into account special characters in the user-info part", "number": 5123, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5123", "body": "Encountered this when dealing with Bitbucket HTTP clone urls:\nhttps://<EMAIL>/getunblocked/sample.git"}
{"title": "SCMs like GitLab and Bitbucket don't have install urls", "number": 5124, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5124"}
{"title": "change listener port of services", "number": 5125, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125", "body": "Dev outage was caused by changes to security policy in Kubernetes. We run our services in unprivileged pods. That means ports lower than or equal 1024 are not allowed for a service. All of our services run on 443.\nThis change introduces a listener port which is used to bring up the service while keeping the old service.port parameter as is. This is because the service.port configuration param is used in many places to construct urls referring to our system and the outside world would have to access us via port 443 (Loadbalancer and CloudFront)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#pullrequestreview-1325266785", "body": ""}
{"comment": {"body": "Typo? Listed twice ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#discussion_r1125721388"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#pullrequestreview-1325273070", "body": ""}
{"comment": {"body": "I copied the line above it (treated it same as port because I don't understand it well). I think it's a fallback mechanism to take the value from env var (e.g when we run tests) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#discussion_r1125728131"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#pullrequestreview-1325274723", "body": ""}
{"comment": {"body": "This is horrible I know but I don't know how else to deal with this nullable crap. At least tests pass now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#discussion_r1125729938"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#pullrequestreview-1325284250", "body": ""}
{"comment": {"body": "Correct.\r\n\r\nThe second value overrides the first value if provided. It defaults to 8080, but env var exists, it'll consume env var.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#discussion_r1125739578"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#pullrequestreview-1325285077", "body": "Get this in !!!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5125#pullrequestreview-1325286460", "body": "Not 100% sure but I think this is going to muck with CORS and possibly anything else that does port 443 -> https mapping"}
{"title": "Move to kube naming conventions for ports", "number": 5126, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5126", "body": "\nAlso, moving away from defaults for config."}
{"title": "Fix service egress", "number": 5127, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5127", "body": "Fixed the egress traffic. It was being blackholed for some reason\nI also changed the deployment percentages because right now helm doesn't wait for backend service rollovers. It always shows a success. This is to test if changing it to anything other that 100% would force it to wait."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5127#pullrequestreview-1325293924", "body": ""}
{"title": "fix for broken loadbalancer config in Dev", "number": 5128, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5128", "body": "Documentation reference: \nSince we are now using a cluster with networking being done fully via an overlay, we need to change how our load balancers target services.\nManually tested in Dev and works"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5128#pullrequestreview-1325506535", "body": ""}
{"comment": {"body": "This is the new cluster version", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5128#discussion_r1125896175"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5128#pullrequestreview-1325517874", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5128#pullrequestreview-1325518188", "body": "Would like to chat about alb.ingress.kubernetes.io/backend-protocol. LGTM"}
{"title": "Auth Sidebar coordinator", "number": 5129, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129", "body": "Setup AuthSidebar in IntelliJ with full auth flow.\nCurrently not possible to logout. Will add that shortly :)\n"}
{"comment": {"body": "General Flow:\r\n1. IntelliJ launches Agent.\r\n2. IntelliJ pushes existing tokens to Agent\r\n3. Agent triggers setupAuth. If there is no refresh token, update auth stream from loading -> unauth\r\n4. When authstream updates to unauthenticated, a listener calls `AuthSidebarCommand`. Another subscriber also fetches login providers.\r\n5. AuthSidebarCommand tells the Agent to load AuthSidebar window & webview.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I wonder if this should ultimately be done by the auth store.  I would expect this behaviour to be consistent (if we can't log in, initialize providers)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#discussion_r1128436981"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Related to our conversations yesterday :)\r\n\r\nThis is done here as InitializeLoginProviders depends on a TokenProvider.\r\n\r\n```\r\nconst initializeLoginProviders = () => {\r\n    return sharedInitializeLoginProviders(JetbrainsTokenProvider, AgentType.Intellij, []);\r\n};\r\n```\r\n\r\nI'll look into refactoring this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#discussion_r1128475951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Ah yeah -- there's no rush, what's here in this PR is fine, something to consider for later I think", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#discussion_r1128476741"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#pullrequestreview-**********", "body": ""}
{"comment": {"body": "There's no protection here against calling this multiple times.... is it safe if we AuthSidebarCommand() multiple times?\r\n\r\nShould we also `dropRepeats` and `debounce` this stream so that quick state changes and duplicates are ignored?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#discussion_r1128554539"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#pullrequestreview-1329507958", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#pullrequestreview-1329514127", "body": ""}
{"comment": {"body": "FWIW I find the chaining of subscriptions, streams, and data here a bit awkward and probably error-prone.\r\n\r\nI wonder if it would be more straightforward if we build the authPollingStream as single stream that always exists, but has a property that starts/stops the auth process.  That way the subscription can be a stream that we subscribe and unsubscribe like the others here, it just won't be \"active\" until the right time...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5129#discussion_r1128575355"}}
{"title": "Update Persons API", "number": 513, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/513", "body": "Current getPerson API returns an array of Person when it should only return a single Person"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/513#pullrequestreview-904806827", "body": ""}
{"title": "Fix up service template", "number": 5130, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5130", "body": "Service template needs to move to bootstrapper"}
{"title": "Update EKS readme with new Calico installation instructions", "number": 5131, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5131", "body": "Added the overlay CIDR ranges\nUpdated installation instructions for Calico CNI"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5131#pullrequestreview-1326992344", "body": ""}
{"title": "UNB-1024 Manually align radio center", "number": 5132, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5132"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5132#pullrequestreview-1327044812", "body": ""}
{"title": "Set up explorer insight stream in Jetbrains", "number": 5133, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133", "body": "Pipe workspace repo and selected file state through to the agent\nAdd a heartbeat monitor so agent dies when parent shuts down\nWrite \"current file\" insights out to stdout so we can verify it works"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#pullrequestreview-1327121605", "body": ""}
{"comment": {"body": "These two FIXMEs will be fixed in an upcoming PR.  I think the ChannelPoller and TeamSTore should be streaming directly from their dependencies instead of having the app level plug the states together.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#discussion_r1126983502"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#pullrequestreview-1327122242", "body": ""}
{"comment": {"body": "This block is temporary, subscribes to the insight stream and writes out streamed insights.  This verifies that everything is working end to end", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#discussion_r1126984073"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#pullrequestreview-1327123289", "body": ""}
{"comment": {"body": "Dunno how to handle this right now, we maybe need two separate tasks for dev and prod but that seems wrong.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#discussion_r1126985021"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#pullrequestreview-1327155409", "body": ""}
{"comment": {"body": "What's this for? Does this provide the git interface?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#discussion_r1127008328"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#pullrequestreview-1327197754", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#pullrequestreview-1327220781", "body": ""}
{"comment": {"body": "Yeah.  The build in IntelliJ SCM APIs are very weak, so (for instane) we can't determine git remotes.  This gets us a proper git interface.  It is basically a standardized, but optional, API for plugins to use.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#discussion_r1127053016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Where is this used?\r\nFrom what I can tell, we only have the `AgentGitProvider` which shouldn't be using this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5133#discussion_r1127125604"}}
{"title": "Add ability to rename the topic from the topic page", "number": 5134, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134", "body": "Just from the topic page though. Needs another PR to allow editing the topic name from the topics overview page."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think we should consider having an alias or displayname for topics seeing as it's going to be much harder to correlate backend to frontend topics if the user decides to willy nilly change topic names.\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#discussion_r1129640411"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#pullrequestreview-**********", "body": ""}
{"comment": {"body": "What do you think @davidkwlam ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#discussion_r1129641470"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#pullrequestreview-1331001829", "body": ""}
{"comment": {"body": "For sure let's chat in the office. Curious to figure out how it would work with merging and understanding the relationship with keywords", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#discussion_r1129701094"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#pullrequestreview-1331004443", "body": ""}
{"comment": {"body": "This change was for us to allow fixing casing issues, but it would definitely need more thought before being exposed to customers", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#discussion_r1129703280"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#pullrequestreview-1331128268", "body": ""}
{"comment": {"body": "I was primarily considered about customer stuff. This is fine as long as it's not abused by us. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5134#discussion_r1129810496"}}
{"title": "Installations client Implementation", "number": 5135, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5135", "body": "Updated VSCode Installations to use the new API. \nMajority of text is provided by API.\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5135#pullrequestreview-1335481255", "body": "ship it "}
{"title": "move to 0.0.11 llama", "number": 5136, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5136"}
{"title": "Add ability to get trending topics with the getTopics operation", "number": 5137, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5137", "body": "For the new sidebar updates:\n\nOne option is to use the existing getTopics operation to populate the Trending Topics section. To do that, we can add a TopicType enum and ask the client to pass this when it wants a list of trending topics.\nThe logic to decide what topics are trending is still TBD but that shouldn't matter to clients. They should just be able to call this operation and get back a list of N topics that are trending.\nOpen to other suggestions if this is not the ideal approach."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5137#pullrequestreview-1331642192", "body": ""}
{"title": "Root VSCode settings", "number": 5138, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5138", "body": "When you have VSCode open on the root folder, run linting and prettier by default on save."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5138#pullrequestreview-1327377180", "body": ""}
{"title": "Add GRPC bindings for ktor", "number": 5139, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5139"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5139#pullrequestreview-1327423114", "body": "minor: maybe it's the code gen or the grpc framework, but there are so many nulls in this code, which is odd"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5139#pullrequestreview-1327428009", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5139#pullrequestreview-1327462364", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5139#pullrequestreview-1327462770", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5139#pullrequestreview-1327462992", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5139#pullrequestreview-1327466270", "body": ""}
{"title": "Fix sidebar styling to match mocks", "number": 514, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514", "body": "Icons need to be replaced, will update once https://github.com/NextChapterSoftware/unblocked/pull/502 goes in (imports all the custom type icons)"}
{"comment": {"body": "I'm just working on updating the Teammember stuff and ripping out the global teamMember fetcher.\r\n\r\nCould we temporarily revert those? I'll get to populating this data later today. Just want it to be consistent with elsewhere.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#issuecomment-1063275984"}}
{"comment": {"body": "@jeffrey-ng done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#issuecomment-1063378532"}}
{"comment": {"body": "I think this PR makes it pretty clear to me that we should be using webpack bundling for all our resources -- that way it's easy to tell where resources are being used, and we know for sure at build time that the correct resources are bundled into the correct bundles...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#issuecomment-1065627786"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#pullrequestreview-904820103", "body": ""}
{"comment": {"body": "will update with var name that's defined in https://github.com/NextChapterSoftware/unblocked/pull/496", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#discussion_r822970618"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#pullrequestreview-904824698", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#pullrequestreview-905020402", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#pullrequestreview-905020499", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#pullrequestreview-906639104", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#pullrequestreview-906642900", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#pullrequestreview-906643025", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#pullrequestreview-906650712", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/514#pullrequestreview-907881093", "body": ""}
{"title": "Fix incremental builds for new components directory for api generation", "number": 5140, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5140", "body": "There were a few problems:\n1. We were not correctly outputting content to outputDirectory\n2. We were not correctly specifying inputs for generation tasks."}
{"title": "Fix margin spacing and remove duped code", "number": 5141, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5141"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5141#pullrequestreview-1327612584", "body": ""}
{"title": "Remove close other editors fn", "number": 5142, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5142"}
{"comment": {"body": "Closing in favour of https://github.com/NextChapterSoftware/unblocked/pull/5179", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5142#issuecomment-1460737517"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5142#pullrequestreview-1327577881", "body": ""}
{"title": "Update makefile", "number": 5143, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5143"}
{"title": "Add more ViewController lifecycle methods to stop GIF animations", "number": 5144, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5144", "body": "Ignore everything except GIF.swift. The rest is SwiftFormat changes after the update"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5144#pullrequestreview-1327707021", "body": ""}
{"title": "Various fixes for Bitbucket and GitLab install flow from VSCode", "number": 5145, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5145", "body": "Bitbucket workspaces should be deserialized as BitbucketOrg\nGitLab groups should be deserialized as GitLabOrg\nFix user-agent\nFix development assertion"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5145#pullrequestreview-1327725027", "body": ""}
{"title": "Revert some CI script changes that break deployment", "number": 5146, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5146"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5146#pullrequestreview-1327708168", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5146#pullrequestreview-1327708232", "body": ""}
{"title": "Remove submodule clone and PAT where possible", "number": 5147, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5147", "body": "Only need recursive clone for build step of IDE workflows for assets,\n  and build step of services workflow for GraphQL.\nOnly need PAT when cloning a private submodule,\n  or when make write changes to the repo."}
{"title": "Fix VSCode installer build in CI", "number": 5148, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5148"}
{"comment": {"body": "Doesn't work:\r\nhttps://github.com/NextChapterSoftware/unblocked/actions/runs/4356100046/jobs/7613638312", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5148#issuecomment-1458428306"}}
{"comment": {"body": "@matthewjamesadam closing as this approach did not work", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5148#issuecomment-1458554784"}}
{"title": "CleanUpEmojiParser", "number": 5149, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5149", "body": "Clean up emoji parser\nRename"}
{"title": "Increase DEV log level to DEBUG", "number": 515, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/515"}
{"title": "Fix Installer CI build... maybe", "number": 5150, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5150"}
{"comment": {"body": "hmm, this is exactly what I had here, modulo the quotes:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/5148", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5150#issuecomment-1458646139"}}
{"comment": {"body": "> hmm, this is exactly what I had here, modulo the quotes:\r\n> #5148\r\n\r\nI don't know why your build didn't succeed -- mine did? https://github.com/NextChapterSoftware/unblocked/actions/runs/**********", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5150#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5150#pullrequestreview-**********", "body": ""}
{"title": "Updated Login buttons for VSCode", "number": 5151, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5151", "body": "Updated styling of buttons for VSCode Login Buttons.\nOrder of LoginProviders is also now sorted.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5151#pullrequestreview-**********", "body": ""}
{"title": "Add ability to toggle individual onboarding states from the person page", "number": 5152, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5152", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5152#pullrequestreview-**********", "body": ""}
{"title": "add environment variables for certs", "number": 5153, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5153", "body": "I don't like hardcoded paths in core app. This should help us move those paths out and make them an infra controlled settings"}
{"comment": {"body": "> I don't like hardcoded paths in core app. This should help us move those paths out and make them an infra controlled settings\r\n\r\nAgree. They should end up as hocon env resolved config like this:\r\n```hocon\r\ntls {\r\n    caPath: \"/localstack/ca/path\"\r\n    caPath: ${?CERTS_PATH_CA_CRT}\r\n    certPath: \"/localstack/crt/path\"\r\n    certPath: ${?CERTS_PATH_TLS_CRT}\r\n    keyPath: \"/localstack/key/path\"\r\n    keyPath: ${?CERTS_PATH_TLS_KEY}\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5153#issuecomment-1458700619"}}
{"comment": {"body": "Great PR, thank you!!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5153#issuecomment-1458704164"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5153#pullrequestreview-1329357976", "body": ""}
{"title": "Add Import Alias", "number": 5154, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5154", "body": "Add import alias based on conversations here:\n"}
{"comment": {"body": "This is just the first step towards unifying the imports. All future code should use the new aliases. \r\n\r\nNext Steps: (Either week of March 13 / 20)\r\n1. Consolidate imports into `sharedConfigs/tsconfig.json`\r\n2. Update client tsconfig.json baseUrl\r\n3. Within other config files (webpack, jest), parse tsconfig.json and inject alias.\r\n4. Replace all existing imports", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5154#issuecomment-1458929464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5154#pullrequestreview-1329622094", "body": ""}
{"title": "Bump jpeg-js from 0.4.2 to 0.4.4", "number": 5155, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5155", "body": "Bumps jpeg-js from 0.4.2 to 0.4.4.\n\nRelease notes\nSourced from jpeg-js's releases.\n\nv0.4.4\n\nv0.4.4 (2022-06-07)\n\nfeat: add comment tag encoding (#87) (13e1ffa), closes #87\nfix: validate sampling factors (#106) (9ccd35f), closes #106\nfix(decoder): rethrow a more helpful error if Buffer is undefined (#93) (b58cc11), closes #93\nchore(ci): migrate to github actions (#86) (417e8e2), closes #86\nchore(deps): bump y18n from 4.0.0 to 4.0.3 (#98) (2c90858), closes #98\nchore(deps): bump ws from 7.2.3 to 7.4.6 (#91) (fd73289), closes #91\nchore(deps): bump hosted-git-info from 2.8.8 to 2.8.9 (#90) (9449a8b), closes #90\nchore(deps): bump lodash from 4.17.15 to 4.17.21 (#89) (ffdc4a4), closes #89\n\nv0.4.3\n\nv0.4.3 (2021-01-11)\n\nfix: handle 0x00E1 / 0x00E0 segments from Pixel phones (#84) (a2d7ed9), closes #84\n\n\n\n\nCommits\n\n9ccd35f fix: validate sampling factors (#106)\nb58cc11 fix(decoder): rethrow a more helpful error if Buffer is undefined (#93)\n2c90858 chore(deps): bump y18n from 4.0.0 to 4.0.3 (#98)\nfd73289 chore(deps): bump ws from 7.2.3 to 7.4.6 (#91)\n9449a8b chore(deps): bump hosted-git-info from 2.8.8 to 2.8.9 (#90)\nffdc4a4 chore(deps): bump lodash from 4.17.15 to 4.17.21 (#89)\n13e1ffa feat: add comment tag encoding (#87)\n417e8e2 chore(ci): migrate to github actions (#86)\na2d7ed9 fix: handle 0x00E1 / 0x00E0 segments from Pixel phones (#84)\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "chore(deps): update dependency gradle to v8.0.2", "number": 5156, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5156", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| gradle (source) | patch | 8.0.1 -> 8.0.2 |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ngradle/gradle\n\n### [`v8.0.2`](): 8.0.2\n\nThe Gradle team is excited to announce Gradle 8.0.2.\n\nThis is the second patch release for Gradle 8.0. We recommend using 8.0.2 instead of 8.0.\n\nIt fixes the following issues:\n\n-   [#23698]() Gradle 8 RC2 runs out of metaspace\n-   [#23962]() Java/Scala build with no explicit toolchain: build fails with Gradle 8.0.1 / Scala 2.13\n-   [#23990]() Gradle 8.0.+ silently dropped support for custom compilers in `JavaCompile`\n-   [#24031]() InstrumentingTransformer generates different class files in Gradle 8 and 7.6 which leads to Remote Build-Cache misses\n-   [#24109]() Extending an already resolved configuration no longer works correctly\n-   [#24122]() Update configuration cache state for some plugins\n-   [#24129]() includeBuild in PluginManagementSpec deincubated in Gradle 8, docs still say it's incubating\n\nIssues fixed in the first patch release:\n\n-   [#21551]() Document integration of Scala plugin with toolchains and problems with `target` flag\n-   [#23888]() `--no-rebuild` suddenly gone without new deprecation cycle and without the reason for its undeprecation being void\n-   [#23905]() Gradle 8.0 fails Scala build with isBlank not found in String class error\n\n[Read the Release Notes]()\n\n#### Upgrade Instructions\n\nSwitch your build to use Gradle 8.0.2 by updating your wrapper:\n\n    ./gradlew wrapper --gradle-version=8.0.2\n\nSee the [Gradle 7.x upgrade guide](\\_7.html#changes\\_8.0) to learn about deprecations, breaking changes and other considerations when upgrading to Gradle 8.0.2.\n\n#### Reporting Problems\n\nIf you find a problem with this release, please file a bug on [GitHub Issues]() adhering to our issue guidelines.\nIf you're not sure you're encountering a bug, please use the [forum]().\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Add assets to onboarding checklist", "number": 5157, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5157", "body": "\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5157#pullrequestreview-1329711988", "body": ""}
{"comment": {"body": "Are we using the previous videos at all?  Should we just replace them?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5157#discussion_r1128730920"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5157#pullrequestreview-1329713081", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5157#pullrequestreview-1329718176", "body": ""}
{"comment": {"body": "Yes we use them for the slideshow component of onboarding.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5157#discussion_r1128736316"}}
{"title": "chore(deps): update dependency nicklockwood/swiftformat to from: \"0.51.2\"", "number": 5158, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5158", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| nicklockwood/SwiftFormat | patch | from: \"0.51.0\" -> from: \"0.51.2\" |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnicklockwood/SwiftFormat\n\n### [`v0.51.2`]()\n\n[Compare Source]()\n\n-   Fixed `hoistTry` rule breaking multiline function chains\n-   Added `--asynccapturing` and `--throwcapturing` options for `hoistTry` and `hoistAwait` rules\n-   Fixed changes in last line of file not being correctly tracked\n\n### [`v0.51.1`]()\n\n[Compare Source]()\n\n-   Fixed `redundantNilInit` removing required `nil` inside Result Builders\n-   Fixed `redundantLet` removing required `let` inside Result Builder `switch/case` statements\n-   Fixed `hoistTry` rule removing second `try` inside XCTAssert statements\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "[BREAKS API ON MAIN] Add listInstallations", "number": 5159, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5159", "body": "Adds listInstallations which returns ListInstallation\nSome API refactoring to pull repos out of the old Installation object"}
{"title": "Remove global Team member cache", "number": 516, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/516", "body": "Remove caching logic that mapped team members to a team.\nNo longer necessary as we've introduced teamID to a thread.\nThings can be cleaned up quite a bit if we move to a stream / publisher model. Joining data is a bit cumbersome atm. Being able to join streams of data should help."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/516#pullrequestreview-905148773", "body": ""}
{"comment": {"body": "Have a bunch of these reducers / map generators in this PR\r\n\r\nHave a subsequent PR that makes these a bit nicer but will require streams to really make this better", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/516#discussion_r823201577"}}
{"title": "Add clientAssets .gitignore and dashboard nits", "number": 5160, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5160", "body": "Add .gitignore to the clientAssets submodule\nFixes:\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5160#pullrequestreview-1329740478", "body": ""}
{"title": "Refactor TeamStore clauses", "number": 5161, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5161", "body": "No behaviour changes."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5161#pullrequestreview-1329748464", "body": ""}
{"title": "chore(deps): update plugin org.jetbrains.intellij to v1.13.1", "number": 5162, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5162", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.jetbrains.intellij | 1.13.0 -> 1.13.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Bootstrap GRPC secretservice", "number": 5163, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5163", "body": "There's a lot going on here \n\nSecret service is now booting a GRPC service with a healthcheck endpoint. \nFor health checks, Docker is using the OSS tool grpc_health_probe, which k8s has out-of-the-box support for as well. The health probe executes the same checks as the HTTP __deepcheck and __shallowcheck requests. @mahdi-torabi we need to add a health probe. See docker-compose-local.yml for an example.\nI added a refresh token endpoint that accepts an authed request. Requests to authed endpoints require an AuthBundle in the message to perform token validation. See Secret.proto for details.\nThere are some additional and required env vars added to config (see global.conf and local.conf). These are:\n    a. SECRET_SERVICE_HOSTNAME  // Part of grpcEndpoints config, should be the hostname other services use to call the secretservice\n    b. SECRET_SERVICE_PORT // Part of grpcEndpoints config\n    c. AUTH_SERVICE_IDENTITY // The SAN for the authservice, part of servicesMetadata.identities configuration\n    d. SECRET_SERVICE_IDENTITY // The SAN for the secretservice, part of servicesMetadata.identities configuration\nIdentity validation is now performed using Subject Alternative Name, as opposed to Common Name. The SAN used for identity validation must be of type DNS, and be the first entry in the SAN list. \nI've added a root CA cert and openssl cnf in a directory called self-signed-certs. We can use these to generate additional certs for local development purposes. See the src/main/resources/certs directories in the auth service and secret service for an example.\nA small change to the CORS plugin code was needed because of a change to nullability of the schemes field in HostConfig"}
{"title": "fix(deps): update hopliteversion to v2.7.2", "number": 5164, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5164", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.sksamuel.hoplite:hoplite-hocon | 2.7.1 -> 2.7.2 |  |  |  |  |\n| com.sksamuel.hoplite:hoplite-core | 2.7.1 -> 2.7.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Remove duplicate ExplorerInsights code", "number": 5165, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5165", "body": "Move the shared implementation from /shared/webComponents into /shared/ide.\nVSCode now uses the shared implementation."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5165#pullrequestreview-1329799214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5165#pullrequestreview-1329800069", "body": "There are a few places where the imports were changed from @shared... to ../../.. when moving into the shared directory."}
{"title": "fix(deps): update protobufversion to v3.22.1", "number": 5166, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5166", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.google.protobuf:protoc (source) | 3.22.0 -> 3.22.1 |  |  |  |  |\n| com.google.protobuf:protobuf-kotlin (source) | 3.22.0 -> 3.22.1 |  |  |  |  |\n| com.google.protobuf:protobuf-java-util (source) | 3.22.0 -> 3.22.1 |  |  |  |  |\n| com.google.protobuf:protobuf-java (source) | 3.22.0 -> 3.22.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Fix Startup Loading", "number": 5167, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5167", "body": "Turns out IntelliJ Startup is very slow.\npostStartupActivity is triggered double digit seconds after the editor launches.\nThis causes a race with the timeout within JetbrainsTokenProvider and potentially triggers an unnecessary \"logout\" operation due to a failed setupAuth.\nRemove this timeout and just wait for the TokenProvider to send the first copy of its tokens before running setupAuth.\nTokenService was also having an issue with null credentials."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5167#pullrequestreview-**********", "body": ""}
{"title": "Use Git worktree for API compatibility test", "number": 5168, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5168", "body": "Problem\nCaused by the move to components. The private.yml file used to be self-contained, so we could get a content stream from Git for that file at any revision. After the transition to components, the revision-specific private.yml would be accurate, but the individual components would be resolved from HEAD in all cases. Thats why the checker could not catch changes in components changes.\nSolution\nSince the spec is now 100s of files, we just ditto the entire repo into /tmp, using a git worktree to do it efficiently. The relative references to components are consistent once again."}
{"comment": {"body": "Failing as expected @kaych :\r\n```\r\n    --------------------------------------------------------------------------\r\n    --                            What's Changed                            --\r\n    --------------------------------------------------------------------------\r\n    - PUT    /person/onboardingStatus\r\n      Parameter:\r\n        - Add X-Unblocked-Product-Agent in header\r\n      Request:\r\n            - Changed application/json\r\n              Schema: Broken compatibility\r\n      Return Type:\r\n        - Changed 200 OK\r\n          Media types:\r\n            - Changed application/json\r\n              Schema: Backward compatible\r\n    - PUT    /person/hasSeenTutorial\r\n    - POST   /teams/{teamId}/threadsV3/{threadId}\r\n      Parameter:\r\n        - Add X-Unblocked-Product-Agent in header\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5168#issuecomment-1459135958"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5168#pullrequestreview-1329840662", "body": "Nice!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5168#pullrequestreview-1329845310", "body": ""}
{"title": "Fix concurrent slack threads", "number": 5169, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5169", "body": "Noticed that all cases where there were dup threads, the thread created timestamp was the same.\nI have no logs to indicate otherwise, but there is some logic causing dupe calls.\nIn the meanwhile, moving to race-condition safe upsert stuff I added a while back and added tests that reproduced this."}
{"title": "Add /api subpath", "number": 517, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/517", "body": "It's working\nbefore:\ncurl -X GET 'localhost:8080/api/preauth' -i\nHTTP/1.1 404 Not Found\nafter:\ncurl -X GET 'localhost:8080/api/preauth' -i\nHTTP/1.1 200 OK"}
{"comment": {"body": "Health works too @mahdi-torabi \r\n\r\n```\r\ncurl -X GET 'localhost:8080/api/__health' -i\r\nHTTP/1.1 200 OK\r\nVary: Origin\r\nContent-Length: 15\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\nHi there Mahdi!\u23ce\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/517#issuecomment-**********"}}
{"comment": {"body": "Will refactor to merge `TestAuthService` and `UnblockedApiClient` later", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/517#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/517#pullrequestreview-904906783", "body": ""}
{"title": "Revert hasDismissedToast to unbreak API", "number": 5170, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5170", "body": "We need another way to achieve the same functionality.\nSome options are:\n1. version the old operation API\n2. separate endpoint for hasDismissedToast flag"}
{"title": "add more mem to search service prod", "number": 5171, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5171"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5171#pullrequestreview-1330055282", "body": ""}
{"title": "Use patch for OnboardingStatusUpdate", "number": 5172, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172", "body": "I think PATCH method might be a good pattern for requests like this. Should be non-breaking when we add another requests param, because PATCH semantics expect every input parameter to be optional."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#pullrequestreview-1331008368", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#pullrequestreview-1331009736", "body": ""}
{"comment": {"body": "please remove the currentPerson check above this call (382-385)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#discussion_r1129707814"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#pullrequestreview-1331010443", "body": ""}
{"comment": {"body": "rm currentPerson check above", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#discussion_r1129708420"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#pullrequestreview-1331010849", "body": ""}
{"comment": {"body": "same here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#discussion_r1129708739"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#pullrequestreview-1331163321", "body": ""}
{"comment": {"body": "done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#discussion_r1129835250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#pullrequestreview-1331163622", "body": ""}
{"comment": {"body": "done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5172#discussion_r1129835477"}}
{"title": "Revert \"[BREAKS API ON MAIN] Add listInstallations\"", "number": 5173, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5173", "body": "Reverts NextChapterSoftware/unblocked#5159"}
{"title": "[BREAKS API ON MAIN] Add listInstallations", "number": 5174, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5174", "body": "Adds listInstallations which returns ListInstallations\nSome API refactoring to pull repos out of the old Installation object"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5174#pullrequestreview-1331156285", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5174#pullrequestreview-1331156431", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5174#pullrequestreview-1331157623", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5174#pullrequestreview-1331219790", "body": ""}
{"title": "UpdateLogging for initial error", "number": 5175, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5175", "body": "Add additional logging to logger which should include more error information."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5175#pullrequestreview-1331148456", "body": ""}
{"title": "Render discussion threads in IntelliJ", "number": 5176, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176", "body": "Render Discussion Threads in sidebar (real data)\n\nCodeblock rendering will be a separate PR with source mark resolution.\nMove DiscussionThread types into shared.\nDiscussionThread.tsx was not moved to shared in this PR.\nReason being there is still CodeBlock implementation being different. Majority of the code is still the same. This will be resolved in a separate PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#pullrequestreview-1331463447", "body": ""}
{"comment": {"body": "In general, I think we should be able to remove the jet brains-specific UI here completely, excepting the styling?  As long as you send a `postMessage` method (which has an identical signature in VSCode and Jetbrains) then you should be able to reuse the same UX in both  That's what I did for the explorer insights anyways.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#discussion_r1130032095"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#pullrequestreview-1331466340", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#pullrequestreview-1331468116", "body": ""}
{"comment": {"body": "What does this do?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#discussion_r1130034088"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#pullrequestreview-1331472502", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#pullrequestreview-1331594302", "body": ""}
{"comment": {"body": "That's true. The main issue is that \"CodeBlock\" is actually implemented per client. Will need to consolidate that to IDE before this can be moved to shared.\r\n\r\nWill be doing that in tandem to source mark resolution.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#discussion_r1130133971"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#pullrequestreview-1331595981", "body": ""}
{"comment": {"body": "This adds the webview as a content. Aka will allow us to actually \"close\" the discussion (which closes the webview due to toolWindow.setToHideOnEmptyContent)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5176#discussion_r1130135838"}}
{"title": "add S3 role to be used for DB backup imports", "number": 5177, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5177", "body": "This will create an IAM role giving RDS access to backups S3 buckets to import backups. \nAttaching the role to RDS would be done manually as part of the DB restore procedure in our DR playbook"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5177#pullrequestreview-1331227003", "body": ""}
{"title": "Set Person hasDismissedToast for PATCH /person/onboardingStatus", "number": 5178, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5178"}
{"comment": {"body": "damn, how did I miss that", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5178#issuecomment-1460726148"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5178#pullrequestreview-1331293366", "body": ""}
{"title": "Make onboarding UIs responsive", "number": 5179, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5179", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5179#pullrequestreview-1331339695", "body": ""}
{"title": "minor api cleanup", "number": 518, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/518", "body": "just sort the tags"}
{"title": "Fix GRPC tests to use first available port", "number": 5180, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5180"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5180#pullrequestreview-1331334270", "body": ""}
{"title": "Attempt moving dev to new poewrml", "number": 5181, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5181"}
{"title": "Fix typo", "number": 5182, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5182"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5182#pullrequestreview-1331425460", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5182#pullrequestreview-1331429563", "body": ""}
{"title": "Fix cdk", "number": 5183, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5183"}
{"title": "Fix skip link alignment", "number": 5184, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5184", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5184#pullrequestreview-1331477815", "body": ""}
{"title": "Fix powerml", "number": 5185, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5185"}
{"title": "Fix typo", "number": 5186, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5186", "body": "Fix typo"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5186#pullrequestreview-1331505385", "body": ""}
{"title": "Implement listInstallations API", "number": 5187, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187", "body": "Also adds GitLab and Bitbucket specific pagination."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#pullrequestreview-1331816887", "body": ""}
{"comment": {"body": "@pwerry had to add user secrets and SCM secrets to API\r\n\r\nYou'll see all of the TODOs in the Helm charts. The goal of the secret service is to remove all of these TODOs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#discussion_r1130235923"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#pullrequestreview-1331822267", "body": ""}
{"comment": {"body": "@benedict-jw I need a (public) default placeholder image for SCM avatars.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#discussion_r1130237654"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#pullrequestreview-1331828484", "body": ""}
{"comment": {"body": "For convenience, tracking in this issue. I can help.\r\nhttps://linear.app/unblocked/issue/UNB-1040/remove-user-and-scm-secrets-from-most-services", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#discussion_r1130241795"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#pullrequestreview-1331839240", "body": ""}
{"comment": {"body": "@richiebres, here's a link to the default bitbucket workspace image.\r\nhttps://d301sr5gafysq2.cloudfront.net/b1fb8f353982/img/default_workspace_avatar/workspace_code.svg", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#discussion_r1130253939"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#pullrequestreview-1331898584", "body": ""}
{"comment": {"body": "Most appreciated!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5187#discussion_r1130323141"}}
{"title": "Enable powerml for prod", "number": 5188, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5188"}
{"title": "Update onboarding copy", "number": 5189, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5189", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5189#pullrequestreview-1334165313", "body": "Proposed content changes that might make this a bit cleaner?"}
{"comment": {"body": "Is this a list of recently changed files by the person viewing the tutorial? \r\n\r\nIf so, perhaps something like this?\r\n\r\n\"Open a file you've recently modified and describe your change with an Unblocked Note. Sharing your note with your team members helps them (and future developers) understand the motivation for your improvement\"\r\n\r\nIf not, how about:\r\n\r\n\"Open a file recently modified and describe the change with an Unblocked Note. Sharing this note with your team members helps them (and future developers) understand the motivation for the improvement\"\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5189#discussion_r1131947521"}}
{"comment": {"body": "\"Creating a video to walkthrough a proposed change or overview of a system has never been easier. The generated video includes direct links to any files, apps and websites you reference. Your audience can get to those assets as simply as clicking a link\"", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5189#discussion_r1132976898"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5189#pullrequestreview-1335747299", "body": "Don't know if you're waiting for approval for this, or if I'm the right person to approve or not"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5189#pullrequestreview-1338075023", "body": ""}
{"comment": {"body": "Test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5189#discussion_r1134697071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5189#pullrequestreview-1402752258", "body": ""}
{"comment": {"body": "Interesting.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5189#discussion_r1178357089"}}
{"title": "Add anchor sourcemark id to the Message model", "number": 519, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/519", "body": "Anchor/header sourcemark\nUpdated design:"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/519#pullrequestreview-905166951", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/519#pullrequestreview-905167059", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/519#pullrequestreview-905167105", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/519#pullrequestreview-905339149", "body": "Note that for the createThread request, the anchorSourcemarkId is required. This isn't possible to express right now, because the CreateThreadRequest contains a CreateMessageRequest, which correctly has an optional anchorSourcemarkId.\nNot a big deal; the server will have to fail any createThread request that is missing an anchorSourcemarkId at runtime."}
{"title": "Jetbrains explorer insights", "number": 5190, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5190", "body": "Enable the explorer insights UI in Jetbrains"}
{"comment": {"body": "> `jetbrains/src/insights/ExplorerInsightsContainer.scss` is empty\r\n\r\nYes -- that was an empty file added before, this PR removes it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5190#issuecomment-1461016716"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5190#pullrequestreview-1331655009", "body": "jetbrains/src/insights/ExplorerInsightsContainer.scss is empty"}
{"title": "reduce powerml load", "number": 5191, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5191"}
{"title": "Fix powerml utils", "number": 5192, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5192"}
{"title": "Add powerml tests", "number": 5193, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5193"}
{"title": "A few tiny jetbrains tweaks", "number": 5194, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5194", "body": "Fix pill and topic tag styling\nHook up thread display"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5194#pullrequestreview-1332050776", "body": ""}
{"title": "Render source mark", "number": 5195, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5195", "body": "Refactor our VSCode SourceMark helpers into shared.\nUpdate DiscussionThreadStream to handle basic SM resolution.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5195#pullrequestreview-1334042454", "body": ""}
{"comment": {"body": "nit, don't particularly like the initial capitals for variable names --  makes them look like classes...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5195#discussion_r1131839932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5195#pullrequestreview-1334043858", "body": ""}
{"title": "Increase sample size", "number": 5196, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5196"}
{"title": "Increase sample size", "number": 5197, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5197"}
{"title": "Add powerml retry semantics", "number": 5198, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5198"}
{"title": "install tenacity", "number": 5199, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5199"}
{"title": "Add Detekt static analysis", "number": 52, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52", "body": "Detekt IntelliJ Setup\n\nInstall Detekt IntelliJ plugin\nConfigure Detekt\nOpen IntelliJ Preferences\nToggle only these checkboxes on:\n[x] Enable Detekt\n[x] Enable rules upon the default configuration\n[x] Enable rules upon the default configuration\n\n\nSet Configuration Files to PATH_TO_REPO/apiservice/detekt.yml"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52#pullrequestreview-854950724", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52#pullrequestreview-854955728", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52#pullrequestreview-854956149", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52#pullrequestreview-854956938", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52#pullrequestreview-854956983", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52#pullrequestreview-854969484", "body": ""}
{"comment": {"body": "Crap missed this during review. Dupe line here. Should it say `Enable formatting (ktlint) rules`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52#discussion_r786433546"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52#pullrequestreview-855020611", "body": ""}
{"comment": {"body": "Supposed to be: _Treat detekt findings as errors_\r\n\r\nFixing in follow up...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/52#discussion_r786470092"}}
{"title": "Introduce Repos API endpoint", "number": 520, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520", "body": "next:\n- server implementation\n- possibly consolidate with installState operation"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520#pullrequestreview-905162029", "body": ""}
{"comment": {"body": "Should this be team-scoped?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520#discussion_r823220470"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520#pullrequestreview-905163897", "body": ""}
{"comment": {"body": "We're not going to provide the clone URL(s) on here?  So I'm guessing the preferred workflow will be for clients to use `findOrCreateRepo` to resolve repos?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520#discussion_r823221958"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520#pullrequestreview-905166320", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520#pullrequestreview-905251404", "body": ""}
{"comment": {"body": "yes, same comment applies equally to almost all endpoints, and plan we agreed on is here:\r\nhttps://www.notion.so/nextchaptersoftware/Resource-Authorization-0126454fb1c74d9ab2df9ff12935e243\r\n\r\nso not in scope for this change.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520#discussion_r823293707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520#pullrequestreview-905252774", "body": ""}
{"comment": {"body": "Client-side flow should be:\r\n\r\n 1. `getRepos`\r\n 2. get `rootCommitSha` from the system using `git rev-list --max-parents=0 HEAD`\r\n 3. match the `rootCommitSha` against the list of repos returned from 1\r\n 4. if no match, call `findOrCreateRepo`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/520#discussion_r823294838"}}
{"title": "Add retry logging", "number": 5200, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5200"}
{"title": "reduce sampling", "number": 5201, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5201"}
{"title": "Fix issues with pagination and role access", "number": 5202, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5202"}
{"title": "Change ml descriptions", "number": 5203, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5203"}
{"title": "IncreaseTopicGeneration2", "number": 5204, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5204", "body": "Increase number of topics generated\nincrease topics generated"}
{"title": "RemoveHardcodedREgions", "number": 5205, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5205", "body": "Increase number of topics generated\nincrease topics generated\nremove hardcoded regions"}
{"title": "Web team installation", "number": 5206, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5206", "body": "Setup generic web team installation which only loads when user has no teams.\nEnumerates all the installations for the user (based on identity in JWT).\nTODO: Setup unique GH flow that doesn't enumerate teams. Dependant on new API.\n\n"}
{"comment": {"body": "merge @jeffrey-ng ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5206#issuecomment-1462998348"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5206#pullrequestreview-1333670004", "body": ""}
{"comment": {"body": "@jeffrey-ng what's the line-height for this text?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5206#discussion_r1131521687"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5206#pullrequestreview-1333681423", "body": ""}
{"comment": {"body": "Just pushed a change for 22. The original screenshot was the default which I think is 20?\r\n\r\n<img width=\"921\" alt=\"CleanShot 2023-03-09 at 11 56 37@2x\" src=\"https://user-images.githubusercontent.com/1553313/224139645-404c24aa-7f39-43d7-94cd-5e208d46a0ce.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5206#discussion_r1131526737"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5206#pullrequestreview-1333690648", "body": "good from my pov. We need to iterate with new api coming soon..."}
{"comment": {"body": "Will come from listInstallations response.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5206#discussion_r1131530631"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5206#pullrequestreview-1333849118", "body": ""}
{"title": "move away from deprecated apis", "number": 5207, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5207"}
{"title": "Up rev cdk deps", "number": 5208, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5208"}
{"title": "Fix service bootstrap template", "number": 5209, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5209"}
{"title": "Fix issues with api prefix", "number": 521, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/521", "body": "Update localhost endpoints to include api prefix. (already done for dev & prod)\nCertain places in auth manually generate redirect paths to the api service. Needed to include /api/ prefix here as well. cc @pwerry @richiebres \nhttps://github.com/NextChapterSoftware/unblocked/pull/517"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/521#pullrequestreview-905147481", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/521#pullrequestreview-905150379", "body": ""}
{"title": "New listInstallations API for web-based team installation", "number": 5210, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210", "body": "Now returns an object:\n{\n  provider: Provider,\n  installations: [InstallationV2]\n}\n\n\nIncludes HTML url of each installation\n\n\nSlight behaviour change, returning installations matching the specified optional provider query parameter.\n  If the provider argument is not specified, then it infers the provider from the currently logged-in identity."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#pullrequestreview-**********", "body": ""}
{"comment": {"body": "@jeffrey-ng note this is an svg data url. That ok?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#discussion_r1131549706"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I haven't personally tried this but looks like it's okay?\r\nhttps://css-tricks.com/lodge/svg/09-svg-data-uris/", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#discussion_r1131555435"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Is this a breaking change?\r\n\r\nWe use InstallationV2 in multiple APIs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#discussion_r1131556423"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#pullrequestreview-1333780987", "body": ""}
{"comment": {"body": "Nope. Only used in response. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#discussion_r1131576609"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#pullrequestreview-1333938236", "body": ""}
{"comment": {"body": "fixed here\n\n[https://github.com/NextChapterSoftware/unblocked/pull/5212](https://github.com/NextChapterSoftware/unblocked/pull/5212)https://github.com/NextChapterSoftware/unblocked/pull/5212https://github.com/NextChapterSoftware/unblocked/pull/5212", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#discussion_r1131719859"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#pullrequestreview-1333938900", "body": ""}
{"comment": {"body": "Btw Rashin is a great dancer. Best dancing form in all the world in fact.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5210#discussion_r1131720328"}}
{"title": "Split vscode sidebar into tabs", "number": 5211, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5211", "body": "Kept this split to vscode for now.\nNote that this is just rearranging existing data (i.e. haven't added the Recommended Topics & Expoerts/Trending Topics sections yet)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5211#pullrequestreview-1334019469", "body": ""}
{"title": "Fix GitLab empty group SVG data-uri placeholder", "number": 5212, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5212", "body": "Data URI is not a URL. Ktor was encoding it as such, which broke the image.\n"}
{"title": "Replace dangerous Url.toString usage", "number": 5213, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213", "body": "We have many instances where we do this:\n```kotlin\nval url: Url? = null\n//  silently transforms to \"null\" using the Any?.toString() function, instead of the intended Url.toString().\n//   This \"null\" result is often exposed in the API.\nurl.toString()\n```\nInstead move to Url.asString extension property:\n```kotlin\nval url: Url? = null\nurl.asString //  fails to not compile\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333946628", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333946804", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333959337", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333960372", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333970332", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333971858", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333972519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333973220", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333973494", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333975823", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5213#pullrequestreview-1333976269", "body": ""}
{"title": "Expose fully qualified GitLab subgroup name", "number": 5214, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214", "body": "Changes\n\nadded fullPath\n\nExample\njson\n    \"installations\": [\n        {\n            \"avatarUrl\": \"\",\n            \"displayName\": \"Unblocked\",\n            \"fullPath\": \"getunblocked\",\n            \"htmlUrl\": \"\",\n            \"installationId\": \"63439188\",\n            \"isInstalled\": false,\n            \"provider\": \"gitlab\"\n        },\n        {\n            \"avatarUrl\": \"data:image/svg...\",\n            \"displayName\": \"aa\",\n            \"fullPath\": \"getunblocked/aa\",\n            \"htmlUrl\": \"\",\n            \"installationId\": \"64306716\",\n            \"isInstalled\": false,\n            \"provider\": \"gitlab\"\n        },\n]\nIntention\nUse fullPath + htmlUrl to render secondary text.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Based on the PR description, we're using fullPath for the secondary text. What's this for?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#discussion_r1131823595"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#pullrequestreview-**********", "body": ""}
{"comment": {"body": "- Title text: **displayName**\n- Secondary text: **fullPath**\n- Secondary link: **htmlUrl**\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#discussion_r1131826945"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#pullrequestreview-**********", "body": ""}
{"comment": {"body": "<img width=\"526\" alt=\"CleanShot 2023-03-09 at 15 45 18@2x\" src=\"https://user-images.githubusercontent.com/1798345/224197327-1daa3bcf-dea5-426f-a68b-099fb0c6b560.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#discussion_r1131828020"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#pullrequestreview-1334044653", "body": ""}
{"comment": {"body": "So this should only be populated for GitHub & GHE? Is it guaranteed for these? aka error state if missing?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#discussion_r1131843000"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#pullrequestreview-1334044728", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#pullrequestreview-1334045296", "body": ""}
{"comment": {"body": "Always hardcoded and always exists.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#discussion_r1131843693"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#pullrequestreview-1334045442", "body": ""}
{"comment": {"body": "Lol, except I forgot to actually add it :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#discussion_r1131843885"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#pullrequestreview-1334045547", "body": ""}
{"comment": {"body": "I'll add it... ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5214#discussion_r1131844011"}}
{"title": "View-based explorer insight stream works in Jetbrains", "number": 5215, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5215", "body": "This passes the current editor's view range through to the active file stream."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5215#pullrequestreview-1334041027", "body": ""}
{"title": "Fix more scrolling instances", "number": 5216, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5216", "body": "Fix scrolling for:\n* onboarding UIs (installation, slideshow, checklist)\n* sidebar when there is a toast"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5216#pullrequestreview-1334066396", "body": ""}
{"title": "Remove Tool window", "number": 5217, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5217"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5217#pullrequestreview-1335750084", "body": ""}
{"title": "Enable search in hub for customers", "number": 5218, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5218", "body": "Is it time? Cmd+Shift+K \n"}
{"comment": {"body": "@pwerry lets sync on this tomorrow. It's been a while since I looked at this and am noticing a few things.\r\n- I think the empty state could do with some text at a minimum\r\n- There can be a delay in returning results. Should we add a spinner?\r\n- I'm also noticing some results that are pruned from dashboard search. For example, if you search \"fix\" in the hub, I'm seeing a bunch of \"dependabot\" results that don't appear on dashboard.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5218#issuecomment-1463289315"}}
{"comment": {"body": "Good feedback! Yes to placeholder text and spinner. Not sure why search results differ. I'll take a look with Kay to see how that API is used", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5218#issuecomment-1463311138"}}
{"title": "Fix Jetbrains lint", "number": 5219, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5219", "body": "Fixes https://github.com/NextChapterSoftware/unblocked/pull/5215"}
{"title": "DEBUG level for Exposed", "number": 522, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/522"}
{"title": "Install copy should be SCM specific", "number": 5220, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5220", "body": "GitHub -> Org\nBitbucket -> Workspace\nGitLab -> Group"}
{"comment": {"body": "Apologies I didn't see this last night. Should we say \"Organization\" for GitHub?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5220#issuecomment-**********"}}
{"comment": {"body": "> Apologies I didn't see this last night. Should we say \"Organization\" for GitHub?\r\n\r\n@benedict-jw yeah, sorry that's better, changed here:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/5235", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5220#issuecomment-**********"}}
{"title": "Connect installation API and enterprise provider plumbing", "number": 5221, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5221", "body": "Installations show when they are connected\n\n\n\nconnect installation succeeds\n\n\n\n\n\n\n\n\n\n\n"}
{"title": "Add support for grpc checks and override flag", "number": 5222, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5222", "body": "Added grpc type health probes\nAdded flag to chose between http, grpc or none\nModified secret service to disable all health probes. This is to prevent breaking our CI/CD pipeline while we test the new service"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5222#pullrequestreview-**********", "body": ""}
{"title": "Fix display name in SCM installation response", "number": 5223, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5223", "body": ""}
{"title": "Add External link to ConnectTeams", "number": 5224, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5224", "body": ""}
{"comment": {"body": "Full titles are in DEV now\r\n<img width=\"753\" alt=\"Screenshot 2023-03-10 at 12 57 48\" src=\"https://user-images.githubusercontent.com/1798345/224426877-0d004fc8-1ca0-4833-acbc-6b7540f841a0.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5224#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5224#pullrequestreview-**********", "body": ""}
{"title": "fix the port", "number": 5225, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5225", "body": "Forgot to quote the number."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5225#pullrequestreview-1335572008", "body": ""}
{"title": "Move to kotlinter to run ktlint via gradle", "number": 5226, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5226", "body": "\nKotliner is using the new Gradler Worker apis to speed up execution and to hopefully deal with incremental builds more appropriately."}
{"title": "Add SCM repo web url to ScmRepo", "number": 5227, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5227"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5227#pullrequestreview-1335656523", "body": ""}
{"title": "Upstream sourcemark points incrementally, instead of all at once", "number": 5228, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5228", "body": "The intention was to upload points in batches as the full recalculation takes place.\nHowever, due to a logic error we actually only uploaded them in batches once\nfull recalculation fully completed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5228#pullrequestreview-1335675059", "body": ""}
{"title": "Do not build custom-ktlint-rules automatically", "number": 5229, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5229"}
{"title": "Fix property loading", "number": 523, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/523", "body": "Test order is non-deterministic. If system props haven't loaded when a test runs that doesn't call ServiceInitializer.init(), tests will fail. I can't see a good reason why we would not want to call this on startup..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/523#pullrequestreview-905180961", "body": ""}
{"title": "Add link to repo", "number": 5230, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5230"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5230#pullrequestreview-**********", "body": ""}
{"title": "Reliable IntelliJ agent shutdown", "number": 5231, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5231", "body": "Heartbeat API is now a bidi stream\nAdd deterministic shutdown when IDE is shut down in a healthy state -- agent closes immediately\nAdd connection tracking to handle when IDE dies unexpected.  The agent tracks connections and disconnections on the stream, and whenever there have been no connections for > 1 minute, it shuts down"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5231#pullrequestreview-**********", "body": ""}
{"title": "Connect installation API upserts teams", "number": 5232, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5232", "body": "Now this UI actually works\n\nTeams are created in admin web\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5232#pullrequestreview-**********", "body": ""}
{"title": "Add VSCode git provider flag", "number": 5233, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5233", "body": "\"unset\" in this case means use whatever the default VSCode behaviour is -- the next PR will change the default to use CustomGitProvider.  This way we can force behaviour in either direction if we want."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5233#pullrequestreview-**********", "body": ""}
{"title": "Shared Discussion Thread", "number": 5234, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234", "body": "Refactor Code Block & Discussion Thread into shared/ide\n\n\nNext step is to update DiscussionThreadCommand in VSCode to use the shared stream."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This looks like a totally generic CodeBlock component?  It doesn't depend on any other IDE functionality, should this belong in /shared/webComponents?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#discussion_r1134557105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#pullrequestreview-1337866195", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#pullrequestreview-1337879233", "body": ""}
{"comment": {"body": "It's not dependant on IDE functionality but it's styled quite differently from the Web codeblock. Therefore, I have it in the IDE components for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#discussion_r1134567627"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#pullrequestreview-**********", "body": ""}
{"comment": {"body": "OK sounds good", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5234#discussion_r1134567953"}}
{"title": "Use GitHub Organization not GitHub Org", "number": 5235, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5235", "body": "From https://github.com/NextChapterSoftware/unblocked/pull/5220#issuecomment-**********."}
{"title": "VSCode uses UseVSCodeGitProvider flag", "number": 5236, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5236", "body": "This effectively blocks repo resolution and git services until login is completed.  We only resolve this value once, so subsequent logout/login has no effect.\nNote that this also changes the default git provider to CustomGitProvider"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5236#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5236#pullrequestreview-**********", "body": ""}
{"title": "Update onboarding button text responsiveness", "number": 5237, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5237", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5237#pullrequestreview-**********", "body": ""}
{"title": "Add TopicMetricsJob and service skeleton", "number": 5238, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5238"}
{"title": "Updated Connect flow for GH", "number": 5239, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5239", "body": "Updated Connect flow for installations to have special UI based on installURL\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5239#pullrequestreview-**********", "body": ""}
{"title": "Setup \"Me\" and message overlay", "number": 524, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524", "body": "Setup the currentPerson into AuthStore.\nUse this to make Messages locally for overlay."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524#pullrequestreview-907866667", "body": ""}
{"comment": {"body": "This means we're re-fetching the current user every minute?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524#discussion_r825175293"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524#pullrequestreview-907866944", "body": ""}
{"comment": {"body": "Typo", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524#discussion_r825175505"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524#pullrequestreview-907867877", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524#pullrequestreview-907912643", "body": ""}
{"comment": {"body": "Yeah... This was done before the recent change JWT change.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524#discussion_r825211401"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524#pullrequestreview-907912718", "body": ""}
{"comment": {"body": "I'm going to keep this here for now but am up for suggestions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/524#discussion_r825211469"}}
{"title": "changing how we export RDS", "number": 5240, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5240", "body": "Our old approach for exporting RDS was not working out. The export format had to be processed before being imported back into an RDS cluster. \nThis new approach uses our nightly snapshots and exports them to S3. S3 replication then takes care of copying them to us-east-2.\nEach time this lambda runs we export only one snapshot. This is to ensure we stay under the 5 concurrent job limit. The lambda function now runs once every hour. \nTested it in Dev and works as expected. Lambda's are an absolute source of pain"}
{"title": "Add ability to count messages and pull requests", "number": 5241, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5241", "body": "To be used for trending topics"}
{"title": "Fix PR Header", "number": 5242, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5242", "body": "Fixes issue with oversized Insight header."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5242#pullrequestreview-1338145649", "body": ""}
{"comment": {"body": "Test test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5242#discussion_r1134726544"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5242#pullrequestreview-1338146299", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5242#pullrequestreview-1340308840", "body": ""}
{"comment": {"body": "this is a file-level thread reply", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5242#discussion_r1136231882"}}
{"title": "scm service sample rate increae for honeycomb", "number": 5243, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5243"}
{"title": "Restart agent process if it dies", "number": 5244, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5244", "body": "Note that this doesn't really work yet -- this doesn't re-connect all the relevant grpc flows.  I'll figure that out some other time."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5244#pullrequestreview-1339835669", "body": ""}
{"comment": {"body": "This looks like a big change but I really didn't change much: IDEAgentProcess is now effectively a simple data object, with a static suspend factory function to create it.  This allows the object to be fully created, or not, instead of potentially being half initialized and in an odd state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5244#discussion_r1135927452"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5244#pullrequestreview-1339837698", "body": ""}
{"comment": {"body": "the tears you bring to my eyes...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5244#discussion_r1135928420"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5244#pullrequestreview-1339838378", "body": ""}
{"comment": {"body": "FYI this is a rough equivalent to `new Promise((resolve) => doStuff(cb => resolve(cb)))`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5244#discussion_r1135928740"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5244#pullrequestreview-1339840711", "body": ""}
{"title": "IntelliJ PR", "number": 5245, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5245", "body": "Refactored PR Views out of VSCode into shared.\nSTYLING STILL TBD. Lots of work needs to be done to figure out the themes...\nUpdated common stream to populate PR in IntelliJ\nNext PR will be to setup commands so we can open up the sections within IntelliJ.\nINTELLIJ:\n\nVSCODE:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5245#pullrequestreview-1340273808", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5245#pullrequestreview-1340275841", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5245#pullrequestreview-1340277957", "body": ""}
{"title": "Add view title menu to unblocked sidebar", "number": 5246, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5246", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5246#pullrequestreview-1346713761", "body": ""}
{"comment": {"body": "Do we need this debug logging?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5246#discussion_r1140686726"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5246#pullrequestreview-1346719473", "body": ""}
{"comment": {"body": "I don't know if this will work in this scenario (maybe not all of the streams have a loadable state?) but there is a `filterAllReady` stream operator that blocks until all inputs are ready...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5246#discussion_r1140688772"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5246#pullrequestreview-1346727182", "body": ""}
{"title": "Add support for snapshot copy", "number": 5247, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5247", "body": "Modified the python copy to do both snapshot copy and S3 exports\nChanged IAM roles to allow more RDS actions\n\nI will make a spearate PR with a lambda function which cleans up snapshots beyond the retension age.\nPS: I know a whole bunch of print statements are littered all over the python code...I simply don't care! Sorry!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5247#pullrequestreview-1340165815", "body": ""}
{"title": "Create queue and skeleton handlers for calculating topic metrics", "number": 5248, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5248"}
{"title": "Encode Unicode", "number": 5249, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5249", "body": "Before:\n\nAfter:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5249#pullrequestreview-1340278444", "body": ""}
{"comment": {"body": "Not sure where we should organize helper extensions like this?\r\n\r\nWould write tests wherever we move this...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5249#discussion_r1136202848"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5249#pullrequestreview-1340278867", "body": ""}
{"comment": {"body": "@rasharab ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5249#discussion_r1136203069"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5249#pullrequestreview-1341922453", "body": ""}
{"comment": {"body": "We have a StringExtensions in lilb-common.\r\nNot sure if that's the best place t put this, but there you go.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5249#discussion_r1137403734"}}
{"title": "Setup dashboard basename", "number": 525, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/525", "body": "Hopefully fix react router in deployed environment due to dashboard prefix."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/525#pullrequestreview-905196547", "body": ""}
{"title": "Use correct jetbrains build cfg", "number": 5250, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5250", "body": "Sets logging context and headers for API requests\nAlso, remove an unused file (we don't have a generic sidebar tool window)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5250#pullrequestreview-1342688835", "body": ""}
{"title": "Refactor ScmPullRequestComment data classes to support GitHub file-level comments", "number": 5251, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5251", "body": "Next PR will update ingestion to create threads with file points instead of source points for file-level comments."}
{"title": "Update pull request ingestion to support GitHub file-level comments", "number": 5252, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5252", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5252#pullrequestreview-1342426393", "body": ""}
{"comment": {"body": "Test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5252#discussion_r1137771858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5252#pullrequestreview-1342427997", "body": ""}
{"comment": {"body": "Herrow", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5252#discussion_r1137772676"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5252#pullrequestreview-1344859659", "body": ""}
{"comment": {"body": "Another test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5252#discussion_r1139425457"}}
{"comment": {"body": "Test comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5252#discussion_r1139425533"}}
{"comment": {"body": "Test file comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5252#discussion_r1139425625"}}
{"title": "update us-east-2 config for DR", "number": 5253, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5253", "body": "I need this change so that services in us-east-2 could start"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5253#pullrequestreview-1340650263", "body": ""}
{"title": "Bump webpack from 5.75.0 to 5.76.0", "number": 5254, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5254", "body": "Bumps webpack from 5.75.0 to 5.76.0.\n\nRelease notes\nSourced from webpack's releases.\n\nv5.76.0\nBugfixes\n\nAvoid cross-realm object access by @Jack-Works in webpack/webpack#16500\nImprove hash performance via conditional initialization by @lvivski in webpack/webpack#16491\nSerialize generatedCode info to fix bug in asset module cache restoration by @ryanwilsonperkin in webpack/webpack#16703\nImprove performance of hashRegExp lookup by @ryanwilsonperkin in webpack/webpack#16759\n\nFeatures\n\nadd target to LoaderContext type by @askoufis in webpack/webpack#16781\n\nSecurity\n\nCVE-2022-37603 fixed by @akhilgkrishnan in webpack/webpack#16446\n\nRepo Changes\n\nFix HTML5 logo in README by @jakebailey in webpack/webpack#16614\nReplace TypeScript logo in README by @jakebailey in webpack/webpack#16613\nUpdate actions/cache dependencies by @piwysocki in webpack/webpack#16493\n\nNew Contributors\n\n@Jack-Works made their first contribution in webpack/webpack#16500\n@lvivski made their first contribution in webpack/webpack#16491\n@jakebailey made their first contribution in webpack/webpack#16614\n@akhilgkrishnan made their first contribution in webpack/webpack#16446\n@ryanwilsonperkin made their first contribution in webpack/webpack#16703\n@piwysocki made their first contribution in webpack/webpack#16493\n@askoufis made their first contribution in webpack/webpack#16781\n\nFull Changelog: \n\n\n\nCommits\n\n97b1718 Merge pull request #16781 from askoufis/loader-context-target-type\nb84efe6 Merge pull request #16759 from ryanwilsonperkin/real-content-hash-regex-perf\nc98e9e0 Merge pull request #16493 from piwysocki/patch-1\n5f34acf feat: Add target to LoaderContext type\nb7fc4d8 Merge pull request #16703 from ryanwilsonperkin/ryanwilsonperkin/fix-16160\n63ea82d Merge branch 'webpack:main' into patch-1\n4ba2252 Merge pull request #16446 from akhilgkrishnan/patch-1\n1acd635 Merge pull request #16613 from jakebailey/ts-logo\n302eb37 Merge pull request #16614 from jakebailey/html5-logo\ncfdb1df Improve performance of hashRegExp lookup\nAdditional commits viewable in compare view\n\n\n\nMaintainer changes\nThis version was pushed to npm by evilebottnawi, a new releaser for webpack since your current version.\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "chore(deps): update dependency webpack to v5.76.0 [security]", "number": 5255, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5255", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| webpack | 5.75.0 -> 5.76.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\nGitHub Vulnerability Alerts\nCVE-2023-28154\nWebpack 5 before 5.76.0 does not avoid cross-realm object access. ImportParserPlugin.js mishandles the magic comment feature. An attacker who controls a property of an untrusted object can obtain access to the real global object.\n\nRelease Notes\n\nwebpack/webpack\n\n### [`v5.76.0`](https://togithub.com/webpack/webpack/releases/tag/v5.76.0)\n\n[Compare Source](https://togithub.com/webpack/webpack/compare/v5.75.0...v5.76.0)\n\n#### Bugfixes\n\n-   Avoid cross-realm object access by [@Jack-Works](https://togithub.com/Jack-Works) in [https://github.com/webpack/webpack/pull/16500](https://togithub.com/webpack/webpack/pull/16500)\n-   Improve hash performance via conditional initialization by [@lvivski](https://togithub.com/lvivski) in [https://github.com/webpack/webpack/pull/16491](https://togithub.com/webpack/webpack/pull/16491)\n-   Serialize `generatedCode` info to fix bug in asset module cache restoration by [@ryanwilsonperkin](https://togithub.com/ryanwilsonperkin) in [https://github.com/webpack/webpack/pull/16703](https://togithub.com/webpack/webpack/pull/16703)\n-   Improve performance of `hashRegExp` lookup by [@ryanwilsonperkin](https://togithub.com/ryanwilsonperkin) in [https://github.com/webpack/webpack/pull/16759](https://togithub.com/webpack/webpack/pull/16759)\n\n#### Features\n\n-   add `target` to `LoaderContext` type by [@askoufis](https://togithub.com/askoufis) in [https://github.com/webpack/webpack/pull/16781](https://togithub.com/webpack/webpack/pull/16781)\n\n#### Security\n\n-   [CVE-2022-37603](https://togithub.com/advisories/GHSA-3rfm-jhwj-7488) fixed by [@akhilgkrishnan](https://togithub.com/akhilgkrishnan) in [https://github.com/webpack/webpack/pull/16446](https://togithub.com/webpack/webpack/pull/16446)\n\n#### Repo Changes\n\n-   Fix HTML5 logo in README by [@jakebailey](https://togithub.com/jakebailey) in [https://github.com/webpack/webpack/pull/16614](https://togithub.com/webpack/webpack/pull/16614)\n-   Replace TypeScript logo in README by [@jakebailey](https://togithub.com/jakebailey) in [https://github.com/webpack/webpack/pull/16613](https://togithub.com/webpack/webpack/pull/16613)\n-   Update actions/cache dependencies by [@piwysocki](https://togithub.com/piwysocki) in [https://github.com/webpack/webpack/pull/16493](https://togithub.com/webpack/webpack/pull/16493)\n\n#### New Contributors\n\n-   [@Jack-Works](https://togithub.com/Jack-Works) made their first contribution in [https://github.com/webpack/webpack/pull/16500](https://togithub.com/webpack/webpack/pull/16500)\n-   [@lvivski](https://togithub.com/lvivski) made their first contribution in [https://github.com/webpack/webpack/pull/16491](https://togithub.com/webpack/webpack/pull/16491)\n-   [@jakebailey](https://togithub.com/jakebailey) made their first contribution in [https://github.com/webpack/webpack/pull/16614](https://togithub.com/webpack/webpack/pull/16614)\n-   [@akhilgkrishnan](https://togithub.com/akhilgkrishnan) made their first contribution in [https://github.com/webpack/webpack/pull/16446](https://togithub.com/webpack/webpack/pull/16446)\n-   [@ryanwilsonperkin](https://togithub.com/ryanwilsonperkin) made their first contribution in [https://github.com/webpack/webpack/pull/16703](https://togithub.com/webpack/webpack/pull/16703)\n-   [@piwysocki](https://togithub.com/piwysocki) made their first contribution in [https://github.com/webpack/webpack/pull/16493](https://togithub.com/webpack/webpack/pull/16493)\n-   [@askoufis](https://togithub.com/askoufis) made their first contribution in [https://github.com/webpack/webpack/pull/16781](https://togithub.com/webpack/webpack/pull/16781)\n\n**Full Changelog**: https://github.com/webpack/webpack/compare/v5.75.0...v5.76.0\n\n\n\n\nConfiguration\n Schedule: Branch creation - \"\" (UTC), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Add more region specific config", "number": 5256, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5256"}
{"title": "Rename MockScmObjects function", "number": 5257, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5257"}
{"title": "SourcePointStore.createSourcePoints should take a list of Points", "number": 5258, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5258", "body": "This will allow creating FilePoints (in addition to SourcePoints). Fixes a bug where we're creating duplicate file points."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5258#pullrequestreview-1342114716", "body": ""}
{"comment": {"body": "This is the bug. The default for `SourcePointModel.isTrusted` is `false`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5258#discussion_r1137561865"}}
{"title": "Database specific region for sts auth", "number": 5259, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5259"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5259#pullrequestreview-1342135144", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5259#pullrequestreview-1342136173", "body": "Looks good to me."}
{"title": "Use asymmetric key for auth", "number": 526, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526", "body": "This is a step towards removing possibly removing token minting from the api-service. The correct architecture for this would be a non-public facing IdentityService that handles the final step of the OAuth dance to GitHub to mint the refresh token. \nThat would prevent a confused deputy attack, because refreshing is trusted. Also prevents token theft if the api-service is popped."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#pullrequestreview-905236011", "body": ""}
{"comment": {"body": "This is super lazy of me, but it gets the job done because of the algo mismatch", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#discussion_r823281502"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#pullrequestreview-905236259", "body": ""}
{"comment": {"body": "real keys now!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#discussion_r823281701"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#pullrequestreview-905236580", "body": ""}
{"comment": {"body": "This fixes an annoying bug I left behind a month ago...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#discussion_r823281932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#pullrequestreview-905371760", "body": "Don't really follow the changes in Jwt, but everything else looks good."}
{"comment": {"body": "- Am I reading this right: all of these 5 lines (and the associated call sites) are for the local env _only_? If so, seems like a fair bit of logic divergence in the code for the local env; the risk is that we are testing/running something that is not representative of the deployed product.\r\n\r\n- Minor point: userHome, secretsDir, secretPropertiesFile could be `private` here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#discussion_r823387400"}}
{"comment": {"body": "bad smell: this Jwt object can freely read every config value in the system. the specific configs should be injected. not for today.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#discussion_r823391384"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#pullrequestreview-905386925", "body": ""}
{"comment": {"body": "Yes agreed. I've been meaning to refactor this. Ultimately Jwt should be a class, not an object, and it should be injected where needed with the right injected config values", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#discussion_r823398650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#pullrequestreview-905390385", "body": ""}
{"comment": {"body": "You're not wrong \ud83d\ude14. I'm going to toss this potato over to the machine when he's back. I think he had some plans for this. The reason this smells bad is because of divergence in k8s secrets management to local, where k8s is doing env injection. We shouold try to align that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#discussion_r823401045"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#pullrequestreview-905392268", "body": ""}
{"comment": {"body": "The token generators and verifiers should also be injected so that access is controlled not only through failed key load, but also through service config", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#discussion_r823402410"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#pullrequestreview-905395420", "body": ""}
{"comment": {"body": "worth looking into\r\nhttps://github.com/Kodein-Framework/Kodein-DI#readme", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/526#discussion_r823404653"}}
{"title": "Set setHasSeenTutorial for VSCode if updatePersonTutorial sent from hub", "number": 5260, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5260", "body": "This is a hack but this operation is deprecated"}
{"title": "UNB-1008 Add page titles for all dashboard routes", "number": 5261, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5261", "body": "Add contextual title and team name to page titles on the dashboard.\n\n\n\nInsights\n* My Insights\n* Recommended Insights\n* Recently Deleted Insights\n* [Thread title]\n* [PR title] #[PR number]\nTopics\n* Topics\n* [Topic Name]\n* Add Topic\nExperts\n* [Team Member Name]\nSearch\n* Search Results for [search queries]\nSettings\n* Email Preferences\n* Slack (unconnected)\n* [Slack workspace] (connected)\n* Invite Team Members"}
{"comment": {"body": "https://linear.app/unblocked/issue/UNB-1008/more-descriptive-dashboard-page-head", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5261#issuecomment-1470779879"}}
{"comment": {"body": "@kaych are we tail truncating?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5261#issuecomment-1470996872"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5261#pullrequestreview-1342356233", "body": ""}
{"comment": {"body": "Reset on unmount. Acts as a fallback (in case a route forgets to set its title) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5261#discussion_r1137724467"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5261#pullrequestreview-1342609261", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5261#pullrequestreview-1346461097", "body": ""}
{"comment": {"body": "With this hook returning a function, there is no control over when the title is updated or changed.  I wonder if this hook should work where instead it takes in a function defining the title, plus the dependencies, and sets the title as required, instead of returning a function?\r\n\r\n`useDocumentTitle(() => 'New Title With ${value}', [value])`\r\n\r\nThis is the pattern we seem to be manually replicating in all the usages of this hook anyways, plus we guard against incorrect usage of the function...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5261#discussion_r1140562205"}}
{"title": "Increase llogging for services temporarily", "number": 5262, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5262"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5262#pullrequestreview-1342609586", "body": ""}
{"title": "Move Instant.startOfDayPST to lib-common", "number": 5263, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5263", "body": "To allow reuse for trending topics"}
{"title": "Create TopicMetricsModel and store", "number": 5264, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5264"}
{"title": "Glob SourcePoint uploading requests together", "number": 5265, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265", "body": "This globs SourcePoint upload requests together, so that only one upload request is pending at a time.  Might help with this issue:  -- if IDE operations run while source points are uploading, we can run multiple uploads at the same time.\nWrote a GlobRunningPromise helper for this.  This globs promise executions together while one is outstanding."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#pullrequestreview-1344488635", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#pullrequestreview-1347344819", "body": "Not sure I get this. We need to buffer requests, not drop them. Could be reading the test wrong though."}
{"comment": {"body": "I don't think this is what we want.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#discussion_r1141238144"}}
{"comment": {"body": "We do not want to cancel promises, we want to _buffer_ promises to enforce that they run serially.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#discussion_r1141238190"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#pullrequestreview-1348955341", "body": ""}
{"comment": {"body": "This function intentionally doesn't queue requests.  The assumption is that the promise being executed isn't a discrete unit of work, it is a promise that executes *all* the work, like in the `upstreamSourcePoints` function.  `upstreamSourcePoints` does all the work, so we want to make sure there is only one outstanding request for this function running at a time.  If a request occurs while a promise is already executing it is \"globbed\" onto the existing one...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#discussion_r1142386827"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#pullrequestreview-1348963146", "body": ""}
{"comment": {"body": "ah, I get it now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#discussion_r1142390288"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#pullrequestreview-1348964229", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#pullrequestreview-1348971157", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#pullrequestreview-1349146968", "body": ""}
{"comment": {"body": "For context, the reason that we have two globbed promises is due to the presence of the `forceUpstream` boolean arg. Consider this timeline:\r\n\r\n1. upstream called with force=false. this promise suspends.\r\n2. upstream called with force=true. this promise is globbed onto 1.\r\n3. upstream runs with force=false (from promise 1)\r\n   - \u26a0\ufe0f Problem occurs when there are up to 1999 points in the buffer they will sit in the buffer unexpectedly because force=false. Without globbing the call with force=true would have uploaded those points immediately.\r\n\r\n\r\nDownside of this approach is that we run up to two upstream requests concurrently; but at least we cap the overall outstanding network requests to just 2, whereas before this change it would have been unbounded.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5265#discussion_r1142512678"}}
{"title": "Changes needed to deploy cold site", "number": 5266, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5266", "body": "Renamed IAM roles created by lambda construct. IAM roles are global and we were getting conflicts\nAdded a new parameter to Redis stack to support deploying new versions while also avoiding recreating the existing clusters. The old version is no longer supported as an active config for new clusters.\nUpdated CDK us-easst-2 config to mark it as an active DR site coldSite=False\nUpdated subnet information for cold site\nAdded EKS cluster config yaml\nAdded config file for us-east-2 dev KMS issuer\nUpdated database stack to support creating DB clusters from snapshots"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5266#pullrequestreview-1344565289", "body": ""}
{"title": "Use current node version in CI", "number": 5267, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5267", "body": "Node 17 is EOL"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5267#pullrequestreview-1344521144", "body": ""}
{"title": "Create kube config for us-east-2 and deployment script", "number": 5268, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5268", "body": "Create kube config for us-east-2 and deployment script\nNote: Deployment script provided here is a very basic boilerplate for DR procedures. I will be reworking this script and move some of the logic in GH Actions to here. The reconfigure GH action to use this script for regular deployments. This way we won't need to maintain separate scripts"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5268#pullrequestreview-1344499593", "body": ""}
{"comment": {"body": "I will be refactoring this script so we could use one script for both manual and CI deploys. I just need this added for now so I can finish up the DR procedure", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5268#discussion_r1139215610"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5268#pullrequestreview-1344714460", "body": ""}
{"title": "Implement metrics calculation for trending topics", "number": 5269, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5269"}
{"title": "Schema fix", "number": 527, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/527", "body": "We figured out why the BUSY table in DEV was permanently locked in Exposed:\ntrx {\n    1. acquire lock\n    2. upgrade schema\n    3. delete lock\n}\nIf step 2 fails (in this case failing because of invalid SourcePointModel schema), then the can never be deleted and can never be re-acquired. This is an Exposed fail safe, and is actually desired behaviour to prevent data loss.\nNext:\n- we need to ensure that the application fails if the schema cannot be upgraded"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/527#pullrequestreview-905237315", "body": ""}
{"title": "Update CSI secrets driver installation procedure", "number": 5270, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5270", "body": "This is just a small readme update. I will no wait for a review."}
{"title": "Enable triggering topic metrics calculation from the admin console", "number": 5271, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5271", "body": "From the topic specific page\n"}
{"title": "Clean up MacOS folder", "number": 5272, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5272"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5272#pullrequestreview-1355150778", "body": ""}
{"title": "Enable triggering topic metrics calculation for the last 90 days from the admin console", "number": 5273, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5273", "body": "For now this is safe to trigger multiple times since we don't recalculate if it was previously done (though I may update it to do recalculations)."}
{"title": "Support file threads on the clients", "number": 5274, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274", "body": "web:\n\nvscode:\n\nintellij:\n\n\nAdded FileHeader component (separate component for web vs ide clients) -- this is rendered in both the CodeBlock component and also can be rendered individually (i.e. when there is no code block)\nAdded MarkReference component which renders either a CodeBlock (if there are sourcepoints) or a FileHeader (if there are no sourcepoints but there are filepoints)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346473479", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346479022", "body": ""}
{"comment": {"body": "Note: clip from the left-side of file paths by default", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#discussion_r1140578827"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346484178", "body": ""}
{"comment": {"body": "I don't think we need separate cases for Source and File point resolutions.  `ResolvedSourcePointData.sourcePointData` already handles both source and file points (despite its name) -- `SourcePointData.point` is of type `Point`, which is either a `SourcePoint` or a `FilePoint`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#discussion_r1140582893"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346485771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346490946", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346492738", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346496051", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346685978", "body": ""}
{"comment": {"body": "@jeffrey-ng suggested casing the resolved data. I don't really care either way but I suppose casing allows for better future proofing in case we end up adding case specific properties in the future", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#discussion_r1140676187"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346787631", "body": ""}
{"comment": {"body": "OK... fine for now.  My problem with having separate casing here is that these are not really fully mutually exclusive values.  A lot of code runs against *either* a resolved file or resolved source, and now that code has to handle both cases and code paths.  You can see how this affects a lot of downstream code.  Let's leave this for now but if this starts getting more complicated we can change its structure.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#discussion_r1140761290"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5274#pullrequestreview-1346787758", "body": ""}
{"title": "Add filter button to Jetbrains explorer insights tool window", "number": 5275, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275", "body": "Add a button to the explorer insights title bar that toggles the filter bar\nDisplay the current file in the top-right zone of the explorer insights panel, where the filter toggle is in VSCode\n\n\nSome notes:\n* I had to add a way for the plugin to communicate with the agent directly, back and forth.  I added a flag to the tunnel API so that commands can be sent to and from the agent to and from either the view or the plugin."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#pullrequestreview-1345121824", "body": ""}
{"comment": {"body": "This was an interesting unit test to write, might be worth a second look -- testing an async hook within a rendered mock component, triggering behaviour using a button in the component.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#discussion_r1139497412"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#pullrequestreview-1345122555", "body": ""}
{"comment": {"body": "I got rid of the tunnel client code -- it didn't seem to do much other then convert to/from JSON.  Going through this made it much harder to encode/decode to the types we needed, so I moved this into the webview controller... hope this is OK @jeffrey-ng ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#discussion_r1139498573"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#pullrequestreview-1346707377", "body": ""}
{"comment": {"body": "Does this work?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#discussion_r1140684341"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#pullrequestreview-1346708609", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#pullrequestreview-1346709146", "body": ""}
{"comment": {"body": "It sets the icon in the stripe, but not the tool window title", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#discussion_r1140685050"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#pullrequestreview-1346710129", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#pullrequestreview-1346715642", "body": ""}
{"comment": {"body": "By Removing the TunnelClient and moving all the logic to the webview controller, doesn't that constrain *how* we communicate with the extension?\r\n\r\nFor example, when we want to send data about source marks for the gutter icons, we don't want to use a webview controller.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#discussion_r1140687382"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#pullrequestreview-1346720760", "body": ""}
{"comment": {"body": "Right... I'd assume that will just be GRPC data though?  And if it's JSON-encoded data within the GRPC data we will JSON encode and decode as needed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#discussion_r1140690010"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5275#pullrequestreview-1346723649", "body": ""}
{"title": "Consolidate thread and pr commands in IntelliJ", "number": 5276, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5276", "body": "Went down a huge (almost entire day...) rabbit hole refactoring the architect to handle tool windows & webview controllers that are created and destroyed.\nThis was a ultimately much more complicated and UX wasn't great as there is a lot of overhead in tearing down and creating webviews.\nTook another approach and decided to consolidate Threads & PRs into a single webview. With this, tool windows are never unregistered and webviews never destroyed. \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5276#pullrequestreview-1346285938", "body": ""}
{"comment": {"body": "This inception-style nesting will work for auth/install states as well...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5276#discussion_r1140452129"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5276#pullrequestreview-1346436571", "body": ""}
{"comment": {"body": "nit: is there a reason why this alias is singular but the file plural", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5276#discussion_r1140541332"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5276#pullrequestreview-1346452936", "body": ""}
{"title": "Added lambda function to cleanup manual snapshots", "number": 5277, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5277", "body": "When copying snapshots to a secondary region, they show up as manual snapshots. Even if the original was an automated snapshot! This new function helps us avoid having snapshots staying around beyond our data rentention policy.\nWe are still able to manually exclude a particular snapshot from this cleanup procedure by tagging it (tag name: 'retain', tag value: 'true')"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5277#pullrequestreview-1346507855", "body": ""}
{"title": "More cleanup of EKS install instructions", "number": 5278, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5278", "body": "More cleanup of EKS install instructions. This is purely documentation change so I am not going to request a review."}
{"title": "Drop batch size to 1 for metrics calculation", "number": 5279, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5279"}
{"title": "refactor openAPI resource IDs", "number": 528, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/528", "body": "Less error-prone UUID typing."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/528#pullrequestreview-905332583", "body": ""}
{"comment": {"body": "@davidkwlam I found this pattern can be used to override the description on the sibling `$ref`\r\nhttps://github.com/swagger-api/swagger-ui/issues/4732#issuecomment-405233540", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/528#discussion_r823358958"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/528#pullrequestreview-905357922", "body": ""}
{"title": "Refactor useDocumentTitle hook", "number": 5280, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5280"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5280#pullrequestreview-1346723509", "body": ""}
{"title": "JeffML v1", "number": 5281, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5281", "body": "JeffML is the future!!!"}
{"title": "Add graph to view topic metrics", "number": 5282, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5282"}
{"title": "Fix PR Message", "number": 5283, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5283", "body": "Fix issue where messages created in PR view had duplicate values due to overlay."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5283#pullrequestreview-1346773474", "body": ""}
{"comment": {"body": "Bug was that the \"new\" message we created did not have the same ID as the localMessage.id\r\n\r\nTherefore, this overlay would never be cleaned up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5283#discussion_r1140745323"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5283#pullrequestreview-1346778863", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5283#pullrequestreview-1346779035", "body": ""}
{"title": "Setup PR Actions in IntelliJ", "number": 5284, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5284", "body": "Connect PR Actions in IntelliJ"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5284#pullrequestreview-1346789246", "body": ""}
{"title": "Add trending score to topics", "number": 5285, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285", "body": "We need to add a trending score property to the Topic API model but unfortunately its being used by an existing PUT endpoint. This PR revs those endpoints, renaming the existing Topic to LegacyTopic and creating a new Topic that has the trendingScore property."}
{"comment": {"body": "@kaych bad news: `Topic` is used by a put operation", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#issuecomment-1476566981"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#pullrequestreview-1346795045", "body": ""}
{"comment": {"body": "What do we think about `trendingLevel`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#discussion_r1140769918"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#pullrequestreview-1349372040", "body": ""}
{"comment": {"body": "@kaych I made `Topic.trendingScore` required now since `Topic` is effectively a new model with this PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#discussion_r1142660302"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#pullrequestreview-1349458185", "body": ""}
{"comment": {"body": "Implies that \"RecommendedTopics\" is not getting an update to include new fields?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#discussion_r1142716690"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#pullrequestreview-1349466398", "body": ""}
{"comment": {"body": "Ah yeah, the endpoint that returns this model is deprecated so no need to rev.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#discussion_r1142722491"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#pullrequestreview-1355716945", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#pullrequestreview-1355727401", "body": ""}
{"comment": {"body": "do we need an updated `putTopicsV2` that accepts an array of `UpdateTopicRequest` objects?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#discussion_r1146920954"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#pullrequestreview-1355728291", "body": "LGTM"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#pullrequestreview-1355728937", "body": ""}
{"comment": {"body": "No, it's not being used by any clients.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5285#discussion_r1146922714"}}
{"title": "Allow generating topic activity metrics for the previous 365 days", "number": 5286, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5286"}
{"title": "Setup DiscussionThread Commands in IntelliJ", "number": 5287, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5287", "body": "Setup majority of commands for DiscussionThreads.\nStill some TODOs, mainly in regards to navigation & dialogs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5287#pullrequestreview-1346822191", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5287#pullrequestreview-1349180063", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5287#pullrequestreview-1349544489", "body": ""}
{"comment": {"body": "We may need to factor some of this out so we can use it in the different UIs -- specifically the thread maintenance functions are shared between the insight and explorer UIs (so we can delete threads, etc).  Something to consider...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5287#discussion_r1142779792"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5287#pullrequestreview-1349545747", "body": ""}
{"title": "this is a paiful reminder right here..", "number": 5288, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5288", "body": "This right here is a really painful reminder that ruined my Saturday......Do NOT let your environment configs drift apart!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5288#pullrequestreview-1347344266", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5288#pullrequestreview-1347344279", "body": ""}
{"title": "Address SM issue where we flushed the point buffer too frequently", "number": 5289, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5289", "body": "Background\nSM engine generates 100,000s of points. The intention is that those point are buffered\nfor upload in chunks of 2000 points in order to reduce the impact of the network\nrequest overhead.\nProblem\nProblem was that we were flushing the buffer whenever the user navigated to a file in\nthe editor.\nChange\nThis change will flush the buffer as intended:\n1. when the buffer reaches 2000 points, or\n2.  when the buffer is explicitly flushed at the end of full recalculation.\nResults\nWith this change there are approximately 4X fewer upload requests, which means that the buffering is working as expected.\nResults | Before | After\n--|--|--\nPoints uploaded | 48,279 | 49,603\nNumber of upload requests | 211 | 55"}
{"title": "Agora channel auth", "number": 529, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/529", "body": "Quick little token generator for Agora. Suffers the same injection issue as Jwt. I will refactor both in a separate PR\nStacked on: https://github.com/NextChapterSoftware/unblocked/pull/510"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/529#pullrequestreview-905400459", "body": ""}
{"title": "Update eks doc calico subnets", "number": 5290, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5290", "body": "This is just a minor documentation change. I won't ask for reviews and just force merge it."}
{"title": "Annotate more internal teams and users", "number": 5291, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5291"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5291#pullrequestreview-1347581719", "body": ""}
{"title": "I messed up dev. This should fix it", "number": 5292, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5292", "body": "I accidentally deleted Redis and ActiveMQ in us-west-2 while cleaning DR stuff. AWS console decided to all of a sudden change the region because another tab in another browser was still pointing at west region"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5292#pullrequestreview-1347594190", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5292#pullrequestreview-1347607919", "body": "Secret names for activemq password haven't changed I assume?"}
{"title": "fixing ebs persistent volume driver which was needed for grafana agent", "number": 5293, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5293", "body": "Changes have been deployed to both Dev and Prod. This was required to get Grafana agent working again."}
{"title": "Fix activemq host", "number": 5294, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5294"}
{"title": "Fix prompt counting", "number": 5295, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5295"}
{"title": "Fix openai error handling", "number": 5296, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5296"}
{"title": "Use Flow for GitHubInstallationMaintenanceJob", "number": 5297, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5297", "body": "Slightly more efficient"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5297#pullrequestreview-1349214587", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5297#pullrequestreview-1349220838", "body": ""}
{"title": "add more cpu to public services that are being throttled", "number": 5298, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5298", "body": "We see a large increase in 4xx errors in prod ALB metrics followed by very frequent failures in HTTP probes. Looking at ALB logs I see a bunch of 460 errors which could be due to target services not responding in time. Looking at Grafana I do see some CPU throttling."}
{"comment": {"body": "Suggest we revert this.\r\n\r\nContext: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1679342930604199?thread_ts=1679337459.271719&cid=C02HEVCCJA3", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5298#issuecomment-1476870003"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5298#pullrequestreview-1349280609", "body": ""}
{"title": "Fix shutdown semantics", "number": 5299, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5299"}
{"title": "Upload jacoco result to CodeCov", "number": 53, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/53", "body": "CODECOV_TOKEN secret added here: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/53#pullrequestreview-854967970", "body": ""}
{"title": "Recalculate SourceMarks for each successive commit pair", "number": 530, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/530", "body": "Processing pairwise is better than processing commits in aggregate because:\n\nwe believe the SourceMark tracking accuracy is improved since the solution space\n   for the number of possible destinations for any tracked code segment is much\n   smaller when restricted to the diffs from a single commit; and\nwe need to track SourcePoints for all possible commits in order to render source\n   points with high fidelity in the web client."}
{"comment": {"body": "Lets merge this in if its ready, I have some conflicting changes that I'd like to get in", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/530#issuecomment-1064337849"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/530#pullrequestreview-906180723", "body": ""}
{"comment": {"body": "my next PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/530#discussion_r823963848"}}
{"comment": {"body": "@davidkwlam another API for you", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/530#discussion_r823964344"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/530#pullrequestreview-906180726", "body": ""}
{"comment": {"body": "@davidkwlam @matthewjamesadam `findSourceMarks` needs to take a fileHash. I'll rev the api.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/530#discussion_r823963620"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/530#pullrequestreview-906228386", "body": ""}
{"title": "Basic PR File Opening", "number": 5300, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5300", "body": "Add basic File Opening functionality to PR Command\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5300#pullrequestreview-1349553103", "body": ""}
{"comment": {"body": "As discussed, we should probably refactor this so that we only have a single command and service implementation for each command type -- next time we have to add a command in this direction we'll refactor it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5300#discussion_r1142786078"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5300#pullrequestreview-1349553784", "body": ""}
{"comment": {"body": "We probably have to scroll to the right point in the file too?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5300#discussion_r1142786616"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5300#pullrequestreview-1349553930", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5300#pullrequestreview-1351543615", "body": ""}
{"comment": {"body": "Updated to navigate to line.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5300#discussion_r1144101726"}}
{"title": "Refactor teardown for background job", "number": 5301, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5301", "body": "Just removes unnecessary boilerplate."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5301#pullrequestreview-1349437651", "body": "Killing it.\nthis guy has no bounds!!!!"}
{"title": "Add logging to debug chart", "number": 5302, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5302"}
{"title": "Refactor general sidebar from VSCode to Shared", "number": 5303, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5303", "body": "Moving general sidebar from VSCode to Shared in preparation to adding it to IntelliJ"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5303#pullrequestreview-1349556096", "body": ""}
{"comment": {"body": "It looks like this was moved from vscode -- can we remove the original vscode file?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5303#discussion_r1142788243"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5303#pullrequestreview-1349556263", "body": ""}
{"comment": {"body": "Same here, can the originating vscode file for this be removed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5303#discussion_r1142788394"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5303#pullrequestreview-1349556408", "body": ""}
{"title": "Revert \"add more cpu to public services that are being throttled\"", "number": 5304, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5304", "body": "Reverts NextChapterSoftware/unblocked#5298.\nNot the cause of the issue."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5304#pullrequestreview-1349503791", "body": ""}
{"title": "Sort topic metrics before charting", "number": 5305, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5305"}
{"title": "Refactor Create Insight", "number": 5306, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5306", "body": "Refactor CreateKnowledge code into CreateInsight in shared.\nPrep for CreateInsight flow in IntelliJ\n"}
{"comment": {"body": "@jeffrey-ng Did you also test that the mention flow (i.e. mentioning team members updates the list of collaborators) still works the same?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5306#issuecomment-1481601912"}}
{"comment": {"body": "> @jeffrey-ng Did you also test that the mention flow (i.e. mentioning team members updates the list of collaborators) still works the same?\r\n\r\nI believe so. Just created this right now https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/796faa3e-6c18-4d8b-84bb-a09f524b63e0\r\n\r\nDid a @mention within the editor. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5306#issuecomment-1481630805"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5306#pullrequestreview-1355147334", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5306#pullrequestreview-1355149811", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5306#pullrequestreview-1355152073", "body": ""}
{"title": "Add jetbrains build to installer job", "number": 5307, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5307"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5307#pullrequestreview-1350908380", "body": ""}
{"comment": {"body": "Don't know if it matters, but this file shouldn't change on its own (without an associated package.json change)...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5307#discussion_r1143671027"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5307#pullrequestreview-1350919957", "body": " Nice"}
{"title": "Very odd...", "number": 5308, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5308", "body": "Somehow this package-lock file got modified in my PR: a32c43e5ea9dbd7502f9e1a7f03b5db66017f211\nThis reverts..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5308#pullrequestreview-**********", "body": ""}
{"title": "Fix team member page crashing due to sort order stability violation", "number": 5309, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5309", "body": "Reproduced by sucking down DEV DB\n"}
{"title": "Update deployment configs", "number": 531, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/531", "body": "Current backend APIS are generating incorrect redirect urls.\nFrom dev, getLoginOptions:\n{\"providers\":[{\"provider\":\"github\",\"oauthUrl\":\"\"}]}"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/531#pullrequestreview-906172860", "body": ""}
{"title": "Upload jetbrains bundle to s3", "number": 5310, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5310"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5310#pullrequestreview-**********", "body": ""}
{"title": "Add TopicModel.trendingScore property", "number": 5311, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5311", "body": "Oh, it's getting lit "}
{"title": "Flesh out openai implementation", "number": 5312, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5312"}
{"title": "Flesh out openai implemetnation", "number": 5313, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5313"}
{"title": "Jetbrains gutter icons pt1", "number": 5314, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314", "body": "Add gutter icons to the jetbrains editors.  This gets the general mechanism going, the actual gutter content is very basic right now (just display the title).  Also note that if there are multiple SMs on a single line, there will be multiple icons.  I will fix these issues in the next PR.\n\nAdd tracking of which files are being edited, and which visible editors are editing each file\nAdd SM stream from agent -> plugin\nWhen an editor is open for a file, begin streaming SMs.  Create gutter icons for the resulting SMs\nAdd an agent API for \"agent commands\" -- commands called by the plugin and executed on the agent.  Add a \"view thread\" command.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1351531932", "body": ""}
{"comment": {"body": "Idea would be to place things like \"openBrowser\" and \"logout\" here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1144093154"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1351532522", "body": ""}
{"comment": {"body": "Yes -- though I'd argue 'openBrowser' is unnecessary as the plugin can just do that itself", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1144093598"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1351532764", "body": ""}
{"comment": {"body": "What's the thought behind this change?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1144093781"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1351533195", "body": ""}
{"comment": {"body": "I was running into similar issues with coroutines... @pwerry ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1144094109"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1351533496", "body": ""}
{"comment": {"body": "Ah that's on Ben's recommendation.  Because we have the icon displayed in the stripe, displaying 'Unblocked' along it is unnecessary and wastes space...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1144094326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1351534062", "body": ""}
{"comment": {"body": "I suspect the solution here is to add the coroutine scope to each class (as Peter suggested earlier) so that the context includes the class `this` context.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1144094750"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1351538507", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1351539231", "body": ""}
{"comment": {"body": "Nevermind. \r\n\r\nOpenBrowser happens the other way.\r\nWill add the equivalent to this for Agent -> Extension commands.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1144098566"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1353122652", "body": ""}
{"comment": {"body": "```kotlin\r\ninterface IDECoroutineScope : CoroutineScope {\r\n    val agentService: AgentService\r\n    override val coroutineContext: CoroutineContext\r\n        get() = agentService.coroutineScope.coroutineContext\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1145168236"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1353123841", "body": ""}
{"comment": {"body": "@jeffrey-ng I'll take care of this.  Thanks @pwerry ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1145169016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#pullrequestreview-1353124136", "body": ""}
{"comment": {"body": "Then all you have to do is call `launch { }` instead of `launchWithIDEAgent`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5314#discussion_r1145169204"}}
{"title": "Pin SwiftChat iOS dependency", "number": 5315, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5315"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5315#pullrequestreview-1351418813", "body": ""}
{"title": "cleanup after dr exercise", "number": 5316, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5316", "body": "Added explicit handling of coldSite flag to each stack. This way the stack could exist as an empty stack while all resourced get deleted. \nset us-east-2 back to a cold site \nI have deployed these changes locally."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5316#pullrequestreview-1351441451", "body": ""}
{"title": "Add extract topics engine", "number": 5317, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5317"}
{"title": "Sidebar Container", "number": 5318, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318", "body": "Refactors the \"General Sidebar\" logic into a shared stream.\nCreate a SidebarContainer which renders AuthSidebar & GeneralSidebar.\nAdd Logout Functionality\n\nTODO: \n* Styling\n* Some actions in GeneralSidebar"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1352975035", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1352984035", "body": ""}
{"comment": {"body": "Will be setting up a generic \"storage\" API in the proto which will store data on the extension side.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145065570"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1352995140", "body": ""}
{"comment": {"body": "Todo: Setup generic action to generate messages.\r\n\r\n` Messages.showMessageDialog(e.project, \"Title\", \"Description\", Messages.getInformationIcon())` on extension side.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145075090"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1352998007", "body": ""}
{"comment": {"body": "Confirm commands require message dialogs.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145077639"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353085602", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353087451", "body": ""}
{"comment": {"body": "Does this add a main menu item for our actions?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145145299"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353104312", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353140568", "body": ""}
{"comment": {"body": "I think generally using `getCurrentValue()` is an antipattern -- it might cause race conditions here when streams update before the input value's results get through the stream.\r\n\r\nCan we pass the input value through the GeneralSidebarStream, so that we get the current input in the sidebarState?  That way the input value always comes through with the stream data that generated it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145180635"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353143702", "body": ""}
{"comment": {"body": "Actually I'm confused about this code -- we probably shouldn't be doing this here?  Triggering a search shouldn't be done based on the output of a stream like this?\r\n\r\nMaybe something to deal with in a future PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145182739"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353146953", "body": ""}
{"comment": {"body": "The boolean streams below don't need to be `| undefined` -- they never publish an undefined value.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145184890"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353159676", "body": ""}
{"comment": {"body": "Don't all these cases basically break down to:\r\n\r\n```\r\nthis.controller.setProps({\r\n  $case: 'auth',\r\n  props: ...authPollingState\r\n})\r\n```\r\n\r\nDo we need to duplicate all these?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145193428"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353162510", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353163867", "body": ""}
{"comment": {"body": "I am not sure we need this here TBH.  I don't think we need to store / restore the last search term?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145196433"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353164185", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353179072", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353179896", "body": ""}
{"comment": {"body": "This was for parity in VSCode. Not sure how important this is? @kaych ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145207395"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353180201", "body": ""}
{"comment": {"body": "It does not.\r\n<img width=\"813\" alt=\"CleanShot 2023-03-22 at 10 25 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/226994065-40a026d9-56e7-4172-ada6-d00ad2d98746.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145207611"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353180480", "body": ""}
{"comment": {"body": "We *can* add our own menu if we wanted though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145207851"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353182129", "body": ""}
{"comment": {"body": "IMO it's expected behaviour. If you have a search term in the main VSCode search and you navigate away and back, the term persists. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145208997"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353182251", "body": ""}
{"comment": {"body": "Didn't want to rock the boat too much with this PR.\r\n\r\nIf we're okay with a larger refactor, I have a few ideas on streamlining the search here...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145209086"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353183932", "body": ""}
{"comment": {"body": "I guess whether or not it's necessary for jetbrains depends on what the expected behaviour is for the IDE. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145210281"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#pullrequestreview-1353186834", "body": ""}
{"comment": {"body": "I had tried multiple ways to do this but could never get the typing to 100% work. \r\n\r\naka AuthPollingState !== `AuthSidebarViewProps` which is expected", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5318#discussion_r1145212418"}}
{"title": "Optimize api usage for openai", "number": 5319, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5319"}
{"title": "Update deployment configs", "number": 532, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/532", "body": "OAuth redirect was sending to incorrect URL\nRemove extra /api path segment."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/532#pullrequestreview-906256952", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/532#pullrequestreview-906257448", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/532#pullrequestreview-906258184", "body": ""}
{"title": "Fix issue where we were looping through GitHub Org owners twice", "number": 5320, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5320", "body": "Two problems:\n1. we were looping through GitHub Org owners in the GitHub API twice\n2. we were misrecording those \"owners\" as \"admins\""}
{"title": "Add util classes to calculate 30-day rolling count", "number": 5321, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5321"}
{"title": "Update Topic views", "number": 5322, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322", "body": "Reorganize TopicView; moves description to the sidebar\n\n\n\n\nAdd affordance to invite expert that isn't on Unblocked\n\n\n\n\nAdd hover state with description to Topics view table\n\n\n\nAdd Slack banner to Topics view\n"}
{"comment": {"body": "@kaych We should only show this for _current_ team members (`isCurrentMember: true`) who do not have an account (`hasAccount: false`).\r\n\r\nIn this case Padraig is not a current member (`isCurrentMember: false`) so sending an invite is pointless.\r\n\r\n![](https://user-images.githubusercontent.com/********/*********-34c04e8b-bdcc-4ed2-b0ba-679282d5b860.png)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-**********", "body": ""}
{"comment": {"body": "```suggestion\r\n                        if (!expert.hasAccount && expert.isCurrentMember && expert.identity.provider !== Provider.Slack) {\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1144108876"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-**********", "body": ""}
{"comment": {"body": "```suggestion\r\n        if (teamMember.hasAccount || !teamMember.isCurrentMember) {\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1144109259"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is unnecessary. The correct solution is to filter out non-current members from the list (see: https://github.com/NextChapterSoftware/unblocked/pull/5322/commits/67cded3fcee6bc20bc406d58a0b3214d1a6f2d37).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1145151912"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Resolving for https://github.com/NextChapterSoftware/unblocked/pull/5322/commits/67cded3fcee6bc20bc406d58a0b3214d1a6f2d37", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1145153065"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1353515672", "body": ""}
{"comment": {"body": "Remove empty file", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1145435872"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1354895036", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1354899259", "body": ""}
{"comment": {"body": "Within shared, I still think we can start moving towards using aliases instead of these relative imports. `@shared/api/models`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146376229"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1354926486", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1354971231", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1354977658", "body": ""}
{"comment": {"body": "Wonder if we can leverage this for the animations?\r\nhttps://headlessui.com/react/transition#showing-and-hiding-content\r\n\r\nPersonally haven't tried it...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146429319"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think we discussed not using index accessors like this anymore? I think `noUncheckedIndexedAccess` will complain about this.\r\n\r\nIn this case, since we are only switching on 1, \r\n\r\n```\r\nconst teamMemberWithoutAccount = membersWithoutAccount[0];\r\n\r\nif (!teamMemberWithoutAccount) {\r\n   ... default stuff\r\n   }\r\n   \r\n   return ...\r\n   ```\r\n\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146433864"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Why are we manually truncating based on word length?\r\nCould we handle this in css?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146436816"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I started out with that but the build was failing? https://github.com/NextChapterSoftware/unblocked/actions/runs/**********/jobs/**********", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146469987"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355152849", "body": ""}
{"comment": {"body": "What is this `source-agent` client? Why is it building client code...\r\n\r\nThe reason it's failing is you need to setup the base necessary aliases in the webpack.base.js\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146546289"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355174100", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355174970", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355185873", "body": ""}
{"comment": {"body": "Wouldn't that be the most unsafe? i.e. trying to access [0] when the list may be empty? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146569186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355251255", "body": ""}
{"comment": {"body": "It's truncating by sentence -- I don't think this can be done with CSS? i.e. if length > MAX, show the first n sentences instead of the first n characters.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146613656"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355333112", "body": ""}
{"comment": {"body": "It seems to be failing on `@shared/api/models` too? Which we use elsewhere", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146668038"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355336998", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355408270", "body": ""}
{"comment": {"body": "If you add ` '@shared': path.resolve(__dirname, '../shared'),` to webpack, tsconfig, etc... this should just work.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146717416"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355409759", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355411779", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355413463", "body": ""}
{"comment": {"body": "I guess my question was we already use this import in shared code (see shared/DiscussionThread.tsx) so why would that work there and not here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146721091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355420097", "body": ""}
{"comment": {"body": "Nope. As long as the array isn't optional, accessing with array index technically returns an optional value. That's why the null check afterwards is necessary.\r\n\r\nIt's not technically typed like this in Typescript until we add the `noUncheckedIndexedAccess` compile flag which I'd like to do after the big push...\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146725229"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355424360", "body": ""}
{"comment": {"body": "DiscussionThread probably wasn't being imported in by source agent.\r\n\r\nUpdated this to update the package.json & etc.. since I just ran into the same issue.\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/5342", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146728094"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355428140", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355440748", "body": ""}
{"comment": {"body": "I don't think your suggested block will work here since the default case needs to handle both 0 and >1. But I can add a check on the `teamMember` for safety", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1146738780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355448582", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355455597", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355456802", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355457337", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1355486864", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1378111164"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#pullrequestreview-1379993396", "body": ""}
{"comment": {"body": "@invitetest1 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5322#discussion_r1163273251"}}
{"title": "Adds GitLab and Bitbucket member models", "number": 5323, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5323", "body": "classes will be used in follow up PRs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5323#pullrequestreview-1351771418", "body": ""}
{"title": "Fixes S3 upload issue for installer", "number": 5324, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5324"}
{"title": "Add Bishop Fox team and users to insiders to omit from stats", "number": 5325, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5325", "body": "And announcements"}
{"comment": {"body": "need to update tests", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5325#issuecomment-1479921059"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5325#pullrequestreview-1353034426", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5325#pullrequestreview-1353041968", "body": ""}
{"title": "Fix build ingestion", "number": 5326, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5326"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5326#pullrequestreview-1353084017", "body": ""}
{"comment": {"body": "Need to be optional because the build scanner may pull in older builds. Can phase out eventually and make these required.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5326#discussion_r1145143063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5326#pullrequestreview-1353094744", "body": ""}
{"title": "Add model images", "number": 5327, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5327"}
{"title": "Jetbrains each service implements coroutine scope", "number": 5328, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5328", "body": "As discussed.  Any class can implement AgentCoroutineScope, which requires a project property (which all our services already do).  Such classes will now get a launchWithIDEAgent method which will maintain the class context when running."}
{"comment": {"body": "Slick!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5328#issuecomment-1480034574"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5328#pullrequestreview-1353208140", "body": ""}
{"title": "Lambda function to disable src/dst check on k8s nodes", "number": 5329, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5329", "body": "Added a new stack to contain all of our housekeeping ec2 related lambda functions\nAdded a lambda function to run every 5 minutes and disable src/dsk check on any k8s nodes that have it enabled\nAdded the coldSite check to S3 stack. I forgot this one in my previous cleanup PR\n\nThis is temporary while we work to move away from Calico."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5329#pullrequestreview-1353446113", "body": ""}
{"title": "Use fileHash to retrieve a SourcePoint that's tree-same to a calculated SourcePoint", "number": 533, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/533"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/533#pullrequestreview-906318199", "body": ""}
{"comment": {"body": "Should we drop the commit?  I don't think we need it if we're providing the file hash?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/533#discussion_r824055762"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/533#pullrequestreview-906318298", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/533#pullrequestreview-906329105", "body": ""}
{"comment": {"body": "not sure...\r\n\r\nAssuming that the SourceMark DB is up to date, then we can find the correct point using either of these two combinations:\r\n- A: {filePath + commitHash}\r\n- B: {filePath + fileHash}\r\n\r\nJust wondering if there are situations where for some reason a client would prefer one combination over the other. Perhaps a web client will have difficulty providing a Git file hash (not impossible, but tricky). It's not clear to me yet.\r\n\r\nSo really the client should provide the filePath and oneOf(commitHash, fileHash). We could make the commitHash, fileHash optional -- does that make sense?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/533#discussion_r824065701"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/533#pullrequestreview-906330869", "body": ""}
{"comment": {"body": "I'll make the optional change for now. we can revisit later...\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/533#discussion_r824067004"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/533#pullrequestreview-906366209", "body": ""}
{"title": "Add webview launch", "number": 5330, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5330", "body": "Re-add request webview"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5330#pullrequestreview-1353502157", "body": ""}
{"title": "add openai topics", "number": 5331, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5331"}
{"title": "Add request timeouts", "number": 5332, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5332"}
{"title": "Increase powerml topic count", "number": 5333, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5333"}
{"title": "Make sure recommended topics return with relevant value of 0", "number": 5334, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5334"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5334#pullrequestreview-**********", "body": ""}
{"title": "Introduce ScmTeamMaintenanceJob for team refresh", "number": 5335, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5335", "body": "Responsible for refreshing existing teams from SCM source of truth,\nand  for each of those teams  refreshing team members and repos.\nRuns periodically.\nPhases\n- refresh team (this change)\n- refresh members (next)\n- refresh repos (next)"}
{"title": "Fix \"manage your {getProviderDisplayName(provider)} Repositories\"", "number": 5336, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5336", "body": ""}
{"title": "Updated jetbrains gutter icons", "number": 5337, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5337", "body": "Add proper icons for different thread types"}
{"comment": {"body": "@matthewjamesadam I've pixel aligned the gutter icons and aligned the colours with the IntelliJ noun palette. \r\n<img width=\"140\" alt=\"CleanShot 2023-03-23 at 11 14 43@2x\" src=\"https://user-images.githubusercontent.com/13353189/227310555-db3608b6-568f-4c0e-ac8f-8ebcce59b64b.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5337#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5337#pullrequestreview-**********", "body": ""}
{"comment": {"body": "`cancelled` is the correct event for when a bidirectional grpc stream is cancelled by the client.  We weren't closing down the sourcemark streams ever...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5337#discussion_r1150877478"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5337#pullrequestreview-1361496735", "body": ""}
{"comment": {"body": "Ignore this -- tooltip UIs are not great for our purposes here, next commit I will be removing the tooltip and replacing with a click handler and popup", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5337#discussion_r1150878789"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5337#pullrequestreview-1361824491", "body": ""}
{"title": "Implements Hub installer for jetbrains", "number": 5338, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5338"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5338#pullrequestreview-1355708884", "body": ""}
{"title": "Adds client config to hub auth state", "number": 5339, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5339", "body": "Tested the upgrade and it doesn't trash the existing auth state because the new associated value is optional. Test shows the new config is successfully downloaded and stored (and restored). Not currently used but will be in subsequent PRs."}
{"title": "Deploy env secrets as part of CI/CD", "number": 534, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/534", "body": "This should publish secrets for each environment as part of the same job deploying service(s)\nLimitation: Currently this deploys on each service deploy. It's not a big deal and doesn't cost us anything extra"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/534#pullrequestreview-906319718", "body": ""}
{"title": "Hookup Connect Button", "number": 5340, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5340"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5340#pullrequestreview-1354945284", "body": ""}
{"title": "Move topic to approved list when relevance is set to 1.0", "number": 5341, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5341", "body": "Fixes a bug where a user couldn't add a topic from the recommended list."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5341#pullrequestreview-1355093209", "body": ""}
{"title": "Fix issues with dropdown operations in IntelliJ", "number": 5342, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5342", "body": "Integrates majority of Dropdown functionality.\nOpening URLs was failing as we need to add event handlers for the CefBrowser to intercept url requests.\nCopy URL was not working in IntelliJ as clipboard API is not supported within CefBrowser. (). Implemented backup mechanism when clipboard fails."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5342#pullrequestreview-1355143360", "body": ""}
{"comment": {"body": "Currently no confirmation dialogs. Will be added subsequently once we setup a dialog interface with IntelliJ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5342#discussion_r1146540256"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5342#pullrequestreview-1355146530", "body": ""}
{"comment": {"body": "File requests occur when extension fetches bundled webview files (e.g. sidebar.ts, insight.ts, ...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5342#discussion_r1146542246"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5342#pullrequestreview-1355378260", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5342#pullrequestreview-1355403508", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5342#pullrequestreview-1355430852", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5342#pullrequestreview-1355431141", "body": ""}
{"title": "Add falco rule for new calico docker image", "number": 5343, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5343", "body": "This should suppress the excessive noise in Security Alarms channel. \nI have already deployed this change to Dev and Prod"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5343#pullrequestreview-1355169551", "body": ""}
{"title": "AdminWeb: Profile page crashes due to missing identities", "number": 5344, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5344", "body": "Assumption was that every person would have at least one identity."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5344#pullrequestreview-1355251690", "body": ""}
{"title": "Add more jeffml tests", "number": 5345, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5345"}
{"title": "enable jeffml for prod", "number": 5346, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5346"}
{"title": "GitLab connect page has missing avatars", "number": 5347, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5347", "body": "Hack fix, until Ktor address this:\n"}
{"title": "super confusing moving topics over", "number": 5348, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5348", "body": "Topics should not be moved over as it affects correctness and some relevancy testing we're adding to console pages.\nIn any event, it's negated whenever we do a new topic ingetstionl"}
{"title": "Improve task logging", "number": 5349, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5349"}
{"title": "adding matt's ip to waf", "number": 535, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/535"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/535#pullrequestreview-906322829", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/535#pullrequestreview-906323510", "body": ""}
{"title": "Encryption system must be specified in SCM-service so that it can refresh tokens", "number": 5350, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5350", "body": "SCM service correctly decrypted a refresh token, correctly refreshed it over the GitLab API,\nbut failed to persist it because it did not have an encryption system loaded.\nThis caused downstream chaos in API service during installation."}
{"title": "Add install jetbrains client capability", "number": 5351, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5351"}
{"title": "Open File on discussion open in IntelliJ", "number": 5352, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5352", "body": "On discussion thread open, open file if resolved."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5352#pullrequestreview-**********", "body": ""}
{"title": "JetBrains lint", "number": 5353, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5353", "body": "How does this not fail CI builds?"}
{"title": "Admin web provider brand icons", "number": 5354, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5354"}
{"title": "Cleanup admin web person page", "number": 5355, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5355", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5355#pullrequestreview-**********", "body": ""}
{"title": "Introduce TeamMemberMaintenance to manage team members", "number": 5356, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5356", "body": "Runs periodically."}
{"title": "increase timeouts", "number": 5357, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5357"}
{"title": "Add token", "number": 5358, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5358", "body": "Fix installer build"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5358#pullrequestreview-**********", "body": ""}
{"title": "Fix installer", "number": 5359, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5359", "body": "Add missing token setting"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5359#pullrequestreview-**********", "body": ""}
{"title": "Use API models", "number": 536, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/536", "body": "SourceMarksProvider is going to end up hitting the API service so we should just use those models instead"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/536#pullrequestreview-906388957", "body": "awesome Dave!"}
{"title": "Add missing token", "number": 5360, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5360"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5360#pullrequestreview-**********", "body": ""}
{"title": "Fix task id generator", "number": 5361, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5361"}
{"title": "Optimize prompts token usage", "number": 5362, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5362"}
{"title": "Add debiggom", "number": 5363, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5363"}
{"title": "update model files", "number": 5364, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5364"}
{"title": "SCM api for repo operations", "number": 5365, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5365", "body": "Sets up auth for the ScmRepoApi, which will be used for the majority of SCM operations on repo (eg: PR ingest)"}
{"title": "Fix installer", "number": 5366, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5366"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5366#pullrequestreview-1357046067", "body": ""}
{"title": "Fix sending emails", "number": 5367, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5367"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5367#pullrequestreview-1357155254", "body": ""}
{"title": "Add helper functions to calculate trending score", "number": 5368, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5368", "body": "Next PR will wire these up"}
{"title": "Remove plugin directory before update", "number": 5369, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5369", "body": "The Copy operation will leave the old plugin jar in the plugin directory alongside the new one. Which plugin gets loaded is non-deterministic. To be safe, we need to nuke the plugin directory before the copy operation."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5369#pullrequestreview-1357263631", "body": ""}
{"title": "Setup environment specific tokens", "number": 537, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/537", "body": "Setup environment specific tokens for vscode.\nWas running into issues where dev was trying to use local credentials in VSCode as they share common workspace data.\nUnnecessary for web as localstorage is per domain/subdomain."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/537#pullrequestreview-906351959", "body": ""}
{"title": "Add stop words", "number": 5370, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5370"}
{"title": "[UNB-1078] Add backup fonts when UI font is unavailable in system", "number": 5371, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5371", "body": "We were dependant on font family from IntelliJ Settings.\nThis font may not exist in system so we need some sensible fallbacks.\nBefore:\n\nAfter\n"}
{"comment": {"body": "Fixes https://linear.app/unblocked/issue/UNB-1078/jetbrains-default-ide-font-inter-is-not-available-in-web-views", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5371#issuecomment-1483344612"}}
{"comment": {"body": "> This is fine in an of itself, but we'll be using the wrong font...\r\n\r\nYeah. There's a bigger question of how we can access the fonts *from* IntelliJ instead of depending on system fonts...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5371#issuecomment-1487555394"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5371#pullrequestreview-1361846491", "body": "This is fine in an of itself, but we'll be using the wrong font..."}
{"title": "Scope Insight commands to their respective model", "number": 5372, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5372", "body": "Controller action stream is publishing \"cached\" events when new subscription is setup.\nSubscription now filters out mismatch insight ids."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5372#pullrequestreview-1357298774", "body": ""}
{"title": "Calculate trending score every night", "number": 5373, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5373"}
{"title": "Setup environment specific tokens", "number": 5374, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5374", "body": "Currently sharing the same tokens for all environments.\nThis is an issue if one uses unblocked on their main IntelliJ & builds IntelliJ locally in Dev"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5374#pullrequestreview-1359434928", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5374#pullrequestreview-1359968872", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5374#pullrequestreview-1359969784", "body": "I'm not sure how the system property is ultimately set -- from build scripts or something?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5374#pullrequestreview-1361675889", "body": ""}
{"comment": {"body": "updated env from a property value to a environment variable.\r\n@pwerry ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5374#discussion_r1150998237"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5374#pullrequestreview-1361684275", "body": ""}
{"comment": {"body": "Do we want to use env vars for `product_number` and `product_version` as well? Just to keep things consistent?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5374#discussion_r1151003807"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5374#pullrequestreview-1361684706", "body": ""}
{"title": "Allow updating trending score from admin console", "number": 5375, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5375"}
{"title": "Update tab styling", "number": 5376, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5376", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5376#pullrequestreview-1361845576", "body": ""}
{"title": "Make calculateTrendingScore is less generous", "number": 5377, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5377"}
{"title": "Add topics intro dialog", "number": 5378, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5378", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5378#pullrequestreview-1373482364", "body": ""}
{"comment": {"body": "I'm wondering if this styling should be built into the dialog itself -- if I add a `bannerImg` in another dialog now, I'll have to duplicate these styles to get it to render as expected, right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5378#discussion_r1158883583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5378#pullrequestreview-1373483607", "body": ""}
{"comment": {"body": "How stable is this?  Local storage can get cleared or evicted... this is probably fine for now but do we need this to be stored on the service?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5378#discussion_r1158884436"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5378#pullrequestreview-1373483664", "body": ""}
{"title": "Add trending property to topics", "number": 5379, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379", "body": "\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359658940", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359694993", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359804434", "body": ""}
{"comment": {"body": "This shouldn't be needed?  Shouldn't tabs render with the tab header taking up fixed space, and the body taking up the rest?\r\n\r\nGenerally, calculating these things by hand is not a great option, you will often run into sub-pixel calculation bugs, and styling and layout becomes more fragile.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#discussion_r1149744917"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359806808", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359811280", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359813395", "body": ""}
{"comment": {"body": "Any reason we restructure and then re-structure the topics here?  Can't we just pass in `topics`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#discussion_r1149750931"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359813770", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359813973", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359815257", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359815831", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359817391", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359839334", "body": ""}
{"comment": {"body": "Same here (`topics` instead of `[...topics]`)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#discussion_r1149768498"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359844836", "body": ""}
{"comment": {"body": "As a side question -- do we need the `sortBy` state?  It looks like `topicSort` is a superset of `sortBy` already, it contains both the field to sort by plus the direction?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#discussion_r1149772513"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359845949", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359846749", "body": "I had a lot of suggestions but feel free to change what you feel is worth changing now, merge this, and then we can talk over any subsequent changes..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359864526", "body": ""}
{"comment": {"body": "It definitely doesn't work without it. This may be related to how these child components are siblings instead of single section units. Will keep this note here for further investigation ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#discussion_r1149785972"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359868519", "body": ""}
{"comment": {"body": "I think the intention was as a fallback to `[]` in case `topics` is empty? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#discussion_r1149788912"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359872966", "body": ""}
{"comment": {"body": "`topics` isn't optional, so if it is empty then it _will_ be `[]`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#discussion_r1149791990"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#pullrequestreview-1359897309", "body": ""}
{"comment": {"body": "Ah I think I remember. I think `.sort()` is mutative so I think I passed in a copy here to preemptively guard against that in case we needed the original sort order for some other application", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5379#discussion_r1149808993"}}
{"title": "Point cache must be write-through backfilled during recalculation", "number": 538, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/538"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/538#pullrequestreview-906360577", "body": ""}
{"comment": {"body": "write-through cache", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/538#discussion_r824087637"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/538#pullrequestreview-906368496", "body": ""}
{"title": "getTopicsForMetrics returns approved topics", "number": 5380, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5380", "body": "This will enable the nightly job that calculates trending score for topics."}
{"title": "Trigger topic metrics calculation for all approved topics", "number": 5381, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5381"}
{"title": "Detect plugin updates and restart", "number": 5382, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382", "body": "I tried to do this without restarting and had it working under some conditions, but it ultimately proved impossible to guarantee an effective reload without a restart. Internal APIs also had to be used, and that yielded a runtime compatibility issue while experimenting, so ultimately that approach had to be abandoned.\nSince \"installation\" is already handled by dropping bits manually, the \"reload\" operation is all that's left. Plugin \"reload\" in IntelliJ requires the creation of a plugin \"descriptor\" for the updated plugin, which is an internal API. \nI also tried coercing IntelliJ's public plugin related classes to execute reload related code-paths to no avail. They really went out of their way to prevent manual plugin manipulation .\nAt any rate, this works.\nRight after the bits are dropped, user will see this:\n\nAnd then when they click \"Reload\", they will see this:\n"}
{"comment": {"body": "Just to confirm, if the user has IntelliJ open, they'll have to smash two buttons in subsequent alerts? \r\nFor first-time installation, they'll only be asked to restart right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#issuecomment-1485889614"}}
{"comment": {"body": "> Just to confirm, if the user has IntelliJ open, they'll have to smash two buttons in subsequent alerts?\r\n> For first-time installation, they'll only be asked to restart right?\r\n\r\nThat's correct. The restart dialog for first time users won't be native to Intellij though. We'll have to create one via the Hub", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#issuecomment-1485890839"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#pullrequestreview-1357897514", "body": ""}
{"comment": {"body": "Please let me know if we have a string properties file or general place where we store constants. Couldn't find one!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#discussion_r1148430405"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#pullrequestreview-1359439285", "body": ""}
{"comment": {"body": "We haven't had much of a need for one (not any strings that are used broadly) -- feel free to make a Strings.kt or whatever.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#discussion_r1149501458"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#pullrequestreview-1359488389", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#pullrequestreview-1359526552", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#pullrequestreview-1359543255", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#pullrequestreview-1359909872", "body": ""}
{"comment": {"body": "I think a lot of these changes will conflict with the changes I made here? https://github.com/NextChapterSoftware/unblocked/pull/5328 -- we don't need to manually track coroutine scopes or agent services much anymore?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#discussion_r1149817442"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#pullrequestreview-1359913071", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#pullrequestreview-1359913507", "body": ""}
{"comment": {"body": "Hmm I am confused as to how these changes aren't conflicting or causing CI errors", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5382#discussion_r1149819647"}}
{"title": "Trigger topic trending score recalculation from the admin console", "number": 5383, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5383"}
{"title": "Increase ratio for the first trending level", "number": 5384, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5384"}
{"title": "Refactor HTTP SCM clients as ScmHttpClientFactory", "number": 5385, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5385"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5385#pullrequestreview-1359584279", "body": ""}
{"title": "Add generic SCM Repo API Factory", "number": 5386, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5386", "body": "Vends generic ScmRepoApi interface, mainly for use in PR ingest."}
{"title": "Clean ununsed SCM classes", "number": 5387, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5387"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5387#pullrequestreview-1359584843", "body": ""}
{"title": "Add ability to update topic description from admin console", "number": 5388, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5388", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5388#pullrequestreview-1359717154", "body": ""}
{"title": "Downgrade \"Not processing personal installation\" warning to info", "number": 5389, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5389", "body": "This is an expected scenario should a user install on their personal org."}
{"title": "forgot to add vault password to dev deploy job", "number": 539, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/539"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/539#pullrequestreview-906368494", "body": ""}
{"title": "Add missing GitHubPullRequestEvent types", "number": 5390, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5390", "body": "json\n{\n  \"level\": \"ERROR\",\n  \"messageId\": \"ID:webhookservice-58b96fbdb8-mmv5j-42025-1679867683776-1:3:1:1:302\",\n  \"message\": \"Failed to process event message\",\n  \"platform\": {\n    \"version\": \"7595085fe8d1e1363dd48a4ae77b9f1b4f1ef5ff\",\n    \"buildNumber\": \"19750\"\n  },\n  \"environment\": \"prod\",\n  \"@timestamp\": \"2023-03-27T10:15:32.434+0000\",\n  \"LogSize\": 2812,\n  \"service\": \"scmservice\",\n  \"thread_name\": \"DefaultDispatcher-worker-339\",\n  \"teamId\": \"29cbd453-4a25-4792-9424-6f172abfab70\",\n  \"logger_name\": \"com.nextchaptersoftware.event.queue.dequeue.EventDequeueService\",\n  \"stack_trace\": \"k.s.SerializationException: com.nextchaptersoftware.scm.github.models.GitHubPullRequestEvent.Action does not contain element with name 'dequeued' at path $.action\n    at k.s.j.i.JsonNamesMapKt.getJsonNameIndexOrThrow(JsonNamesMap.kt:63)\n    at k.s.j.i.StreamingJsonDecoder.decodeEnum(StreamingJsonDecoder.kt:351)\n    at c.n.s.g.m.GitHubPullRequestEvent$Action$$serializer.deserialize(GitHubPullRequestEvent.kt:15)\n    at c.n.s.g.m.GitHubPullRequestEvent$Action$$serializer.deserialize(GitHubPullRequestEvent.kt:15)\n    at k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\n    at k.s.e.AbstractDecoder.decodeSerializableValue(AbstractDecoder.kt:43)\n    at k.s.e.AbstractDecoder.decodeSerializableElement(AbstractDecoder.kt:70)\n    at k.s.j.i.StreamingJsonDecoder.decodeSerializableElement(StreamingJsonDecoder.kt:162)\n    at c.n.s.g.m.GitHubPullRequestEvent$$serializer.deserialize(GitHubPullRequestEvent.kt:9)\n    at c.n.s.g.m.GitHubPullRequestEvent$$serializer.deserialize(GitHubPullRequestEvent.kt:9)\n    at k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\n    at k.s.json.Json.decodeFromString(Json.kt:95)\n    at c.n.s.h.g.GitHubPullRequestHandler.handle(GitHubPullRequestHandler.kt:19)\n    at c.n.s.h.ScmEventWebhookHandler.handleGitHubWebhook(ScmEventWebhookHandler.kt:127)\n    at c.n.s.h.ScmEventWebhookHandler.access$handleGitHubWebhook(ScmEventWebhookHandler.kt:38)\n    at c.n.s.h.ScmEventWebhookHandler$process$2.invokeSuspend(ScmEventWebhookHandler.kt:87)\n    at c.n.s.h.ScmEventWebhookHandler$process$2.invoke(ScmEventWebhookHandler.kt)\n    at c.n.s.h.ScmEventWebhookHandler$process$2.invoke(ScmEventWebhookHandler.kt)\n    at k.c.i.UndispatchedKt.startUndispatchedOrReturnIgnoreTimeout(Undispatched.kt:100)\n    at k.c.TimeoutKt.setupTimeout(Timeout.kt:146)\n    at k.c.TimeoutKt.withTimeout(Timeout.kt:44)\n    at k.c.TimeoutKt.withTimeout-KLykuaI(Timeout.kt:71)\n    at c.n.s.h.ScmEventWebhookHandler.process(ScmEventWebhookHandler.kt:82)\n    at c.n.s.h.ScmEventWebhookHandler.access$process(ScmEventWebhookHandler.kt:38)\"\n}"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5390#pullrequestreview-1359823941", "body": ""}
{"title": "Admin: searching for identities shows team memberships more clearly", "number": 5391, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5391", "body": "Before\n\nAfter\n"}
{"title": "Admin: People page shows smaller badge icons for SCMs", "number": 5392, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5392", "body": "For Dennis.\nBefore\n\nAfter\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5392#pullrequestreview-1359948360", "body": ""}
{"title": "Jetbrains fix confusing coroutine setup", "number": 5393, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5393", "body": "Rename AgentCoroutineScope to ProjectCoroutineScope, as the scope is tied to the project, not the agent\nProjectCoroutineScope uses ProjectCoroutineService to get a singleton coroutine scope for each project\nThis is done similarly everywhere now"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5393#pullrequestreview-1359971258", "body": ""}
{"title": "Visualize average of daily message counts for a topic", "number": 5394, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5394", "body": "Just adding a couple more admin console visualizations to see if we can improve on the definition of topic trendiness."}
{"title": "Upload VSCode bundle to S3 on installer builds", "number": 5395, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5395", "body": "As part of the new install and update process we need to lift the vscode extension out of the installer. This PR doesn't remove the extension from the installer though - that will come in a follow up after we test the alternative installation path internally for a few builds."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5395#pullrequestreview-**********", "body": ""}
{"title": "[BREAKS API ON MAIN] Validate slack webhook secrets", "number": 5396, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5396", "body": "Slack webhook validation requires taking into account request timestamp and signature.\nSlacks sdk is fully formed and has a signature validator for this...\nTested against local stack with local webhooks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5396#pullrequestreview-**********", "body": "did all of these really change?\nsecrets/k8s/assets/dev/admin.secrets.env.yaml\nsecrets/k8s/assets/dev/scm.secrets.env.yaml\nsecrets/k8s/assets/dev/secrets.env.yaml\nsecrets/k8s/assets/dev/transcription.secrets.env.yaml\nsecrets/k8s/assets/dev/user.secrets.env.yaml\nsecrets/k8s/assets/prod/admin.secrets.env.yaml\nsecrets/k8s/assets/prod/scm.secrets.env.yaml\nsecrets/k8s/assets/prod/secrets.env.yaml\nsecrets/k8s/assets/prod/transcription.secrets.env.yaml\nsecrets/k8s/assets/prod/user.secrets.env.yaml\nsecrets/local/assets/secrets.conf\nsecrets/local/assets/secrets.properties"}
{"title": "Revert \"[BREAKS API ON MAIN] Validate slack webhook secrets (#5396)\"", "number": 5397, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5397", "body": "This reverts commit 65423b7053521793cbd5f5e662da4cbf3817f010."}
{"title": "Add ability to override operation earliest releases so that server side operations are not used in compat checks", "number": 5398, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5398"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5398#pullrequestreview-1360068671", "body": ""}
{"comment": {"body": "This can be 823\r\n```suggestion\r\n            earliestSupportedReleaseTag = ReleaseTag(tagNumber = 823),\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5398#discussion_r1149929587"}}
{"title": "Admin: refactoring", "number": 5399, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5399"}
{"title": "Add ktlint check", "number": 54, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/54", "body": "ktlint IntelliJ Setup\n\nInstall ktlint IntelliJ plugin\nConfigure ktlint\nOpen IntelliJ Preferences\nGo to Tools  ktlint \nToggle only these checkboxes on:\n[x] Enable ktlint\n[x] Lint after Reformat\n\n\nSet Annotate ktlint errors as to Error"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/54#pullrequestreview-854985145", "body": ""}
{"title": "Disable prod deploys for now", "number": 540, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/540", "body": "also make ansible deploys more verbose so I can troubleshoot things"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/540#pullrequestreview-906409269", "body": ""}
{"title": "Revert \"Revert \"[BREAKS API ON MAIN] Validate slack webhook secrets (#5396)\" (#5397)\"", "number": 5400, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5400", "body": "This reverts commit 66f32e9bf6a99a7f4e18487237f28a83fb7cd2eb."}
{"title": "fix bugs related to insight topic mapping", "number": 5401, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5401"}
{"title": "UpdateOpenAICode", "number": 5402, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5402", "body": "update code\nFix prompt"}
{"title": "Fix banner flashing", "number": 5403, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5403", "body": "On refresh of the Topics view, the red banner to prompt users to connect to Slack is consistently flash on render:\n\nTo mitigate this behaviour, we should default on setting the show boolean to false, and opt in to showing the banner only if the conditions apply (i.e. they don't have slack installed)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5403#pullrequestreview-1361581692", "body": ""}
{"title": "Only seek spotonly instances for jetbrains", "number": 5404, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5404"}
{"title": "Deprecate the getMessages API", "number": 5405, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5405"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5405#pullrequestreview-1361589184", "body": "Good, ,kill that test"}
{"title": "Add scrollToTop hook for manual scrolling", "number": 5406, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5406", "body": "re: \nThe regular ScrollToTop top level component is only triggered on route change. In the case of the New Topic creation flow, the route doesn't actually change, but the views do. Add a hook to manually scroll to the top given a list of dependencies."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5406#pullrequestreview-1365726479", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5406#pullrequestreview-1365829523", "body": ""}
{"title": "IncreaseRenovateParallelCount", "number": 5407, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5407", "body": "Only seek spotonly instances for jetbrains\nincrease renovate parallel count"}
{"title": "chore(deps): update dependency @headlessui/react to v1.7.13", "number": 5408, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5408", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @headlessui/react | 1.7.11 -> 1.7.13 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ntailwindlabs/headlessui\n\n### [`v1.7.13`](https://togithub.com/tailwindlabs/headlessui/blob/HEAD/packages/@headlessui-react/CHANGELOG.md#1713---2023-03-03)\n\n[Compare Source](https://togithub.com/tailwindlabs/headlessui/compare/@headlessui/react@v1.7.12...@headlessui/react@v1.7.13)\n\n##### Fixed\n\n-   Ensure `Transition` component completes if nothing is transitioning ([#2318](https://togithub.com/tailwindlabs/headlessui/pull/2318))\n-   Enable native label behavior for `` where possible ([#2265](https://togithub.com/tailwindlabs/headlessui/pull/2265))\n-   Allow root containers from the `Dialog` component in the `FocusTrap` component ([#2322](https://togithub.com/tailwindlabs/headlessui/pull/2322))\n-   Fix `XYZPropsWeControl` and cleanup internal TypeScript types ([#2329](https://togithub.com/tailwindlabs/headlessui/pull/2329))\n-   Fix invalid warning when using multiple `Popover.Button` components inside a `Popover.Panel` ([#2333](https://togithub.com/tailwindlabs/headlessui/pull/2333))\n-   Fix restore focus to buttons in Safari, when `Dialog` component closes ([#2326](https://togithub.com/tailwindlabs/headlessui/pull/2326))\n\n### [`v1.7.12`](https://togithub.com/tailwindlabs/headlessui/blob/HEAD/packages/@headlessui-react/CHANGELOG.md#1712---2023-02-24)\n\n[Compare Source](https://togithub.com/tailwindlabs/headlessui/compare/@headlessui/react@v1.7.11...@headlessui/react@v1.7.12)\n\n##### Added\n\n-   Add explicit props types for every component ([#2282](https://togithub.com/tailwindlabs/headlessui/pull/2282))\n\n##### Fixed\n\n-   Ensure the main tree and parent `Dialog` components are marked as `inert` ([#2290](https://togithub.com/tailwindlabs/headlessui/pull/2290))\n-   Fix nested `Popover` components not opening ([#2293](https://togithub.com/tailwindlabs/headlessui/pull/2293))\n-   Make React types more compatible with other libraries ([#2282](https://togithub.com/tailwindlabs/headlessui/pull/2282))\n-   Fix `Dialog` cleanup when the `Dialog` becomes hidden ([#2303](https://togithub.com/tailwindlabs/headlessui/pull/2303))\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency autoprefixer to v10.4.14", "number": 5409, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5409", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| autoprefixer | 10.4.13 -> 10.4.14 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\npostcss/autoprefixer\n\n### [`v10.4.14`]()\n\n[Compare Source]()\n\n-   Improved startup time and reduced JS bundle size (by Krlis Gais).\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Add putSourcePoint operation", "number": 541, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/541", "body": "Allows the sourcemark agent to add a SourcePoint to an existing SourceMark"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/541#pullrequestreview-906427420", "body": ""}
{"title": "chore(deps): update dependency classnames to v2.3.2", "number": 5410, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5410", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| classnames | 2.3.1 -> 2.3.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nJedWatson/classnames\n\n### [`v2.3.2`](https://togithub.com/JedWatson/classnames/blob/HEAD/HISTORY.md#v232--2022-09-13)\n\n[Compare Source](https://togithub.com/JedWatson/classnames/compare/v2.3.1...v2.3.2)\n\n-   Fix TypeScript types when using require, thanks [Mark Dalgleish](https://togithub.com/markdalgleish) ([#276](https://togithub.com/JedWatson/classnames/pull/276))\n-   Fix toString as `[Object object]` in a vm, thanks [Remco Haszing](https://togithub.com/remcohaszing) ([#281](https://togithub.com/JedWatson/classnames/pull/281))\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency fetch-retry to v5.0.4", "number": 5411, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5411", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| fetch-retry | 5.0.2 -> 5.0.4 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\njonbern/fetch-retry\n\n### [`v5.0.4`](): Adds example on how to use with Node.js fetch and fixes failing tests\n\n[Compare Source]()\n\n-   Adds an example on how to use with Node.js fetch API which became available in version 18.\n-   Fixes failing tests when running Node.js version 18, which failed due to using an older version of node-fetch, resulting in having multiple versions of the `Request` object.\n\n### [`v5.0.3`](): Security updates: minimist\n\n[Compare Source]()\n\nUpdated dependencies and fixed security issues reported in dependencies, most notably minimist.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency immer to v9.0.21", "number": 5412, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5412", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| immer | 9.0.12 -> 9.0.21 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nimmerjs/immer\n\n### [`v9.0.21`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   ensure type exports is first in package.json export declaration ([#1018]()) ([b6ccd0f]())\n\n### [`v9.0.20`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   patching maps failed when using number keys ([#1025]()) ([dd83e2e]())\n\n### [`v9.0.19`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   don't freeze drafts returned from produce if they were passed in as draft ([#917]()) ([46867f8]())\n-   produce results should never be frozen when returned from nested produces, to prevent 'hiding' drafts. Fixes [#935]() ([a810960]())\n-   release and publish from 'main' rather than 'master' branch ([82acc40]())\n-   revert earlier fix ([#990]()) for recursive types ([#1014]()) ([3eeb331]())\n-   Upgrade Github actions to Node 16 attempt 1 ([9d4ea93]())\n-   Upgrade Github actions to Node 16 attempt 2 ([082eecd]())\n\n### [`v9.0.18`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   Preserve insertion order of Sets, fixes [#819]() ([#976]()) ([b3eeb69]())\n-   unnecessarily recursive Draft type ([#990]()) ([b9eae1d]())\n\n### [`v9.0.17`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   special case NaN in setter ([#912]()) ([5721bb7]())\n\n### [`v9.0.16`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   protect isDraftable against undefined constructor ([#969]()) ([ced4563]())\n\n### [`v9.0.15`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   Add \"types\" to exports for TypeScript 4.7 ([#946]()) ([85ce6b7]())\n\n### [`v9.0.14`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   Use .esm.js module for backwards compatibility with old build tools ([#939]()) ([d30d219]())\n\n### [`v9.0.13`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   consistent recipe return type in produceWithPatches ([#934]()) ([220d61d]())\n-   incorrect return type for async produceWithPatches ([#933]()) ([9f7623d]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
