{"comment": {"body": "Don\u2019t we want to save the last packet per submodel?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92498915"}}
{"comment": {"body": "Maybe we could remove the voting table for the timing feature \\(which is not use right now\\) and pack 3 anchors into the object? :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92499900"}}
{"comment": {"body": "arguments should be const pointers", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92567223"}}
{"comment": {"body": "argument should be const pointers", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92567246"}}
{"comment": {"body": "These are output parameters", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92567441"}}
{"comment": {"body": "This API is inconsistent with the others - they return enum or nothing and this one int32. It\u2019s preferable that we\u2019re consistent with it.\n\nAlso, const pointers", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92567601"}}
{"comment": {"body": "Using const in function declarations doesn\u2019t do anything, it just makes the function declaration harder to understand and read. The caller doesn\u2019t care whether you\u2019re allowed to modify the parameters in your function scope or not.\n\nThe `const * const` still exists in the function definition, there it actually does something", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92567800"}}
{"comment": {"body": "This one was written by @igal_leveltech so I'm not entirely sure but I believe the reasoning is that it's the same as `int32_t* ret` parmeters. This function had no other return value so it can return its error code directly via return value instead of via output parameters. It can\u2019t be enum because it needs to be consistent with the format of other error codes in the error codes file", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92568186"}}
{"comment": {"body": "Might be better to do it in a separate pull request", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92568336"}}
{"comment": {"body": "Wasn\u2019t really sure what was the purpose of this field but it seemed like a debug field so I preferred not to mess around with it in this pull request", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92568432"}}
{"comment": {"body": "Yeah, it\u2019s returning an error code. But there are only 2 possible return values: no error and input is null.\n\nI\u2019d prefer it to be consistent with the other functions, but not critical", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92569323"}}
{"comment": {"body": "How about validating the external config? For example, that number of anchors is not more than the maximum allowed?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92574628"}}
{"comment": {"body": "Please note that this function is not part of the external API and is not static for code coverage purposes.\n\nAlso, could this be renamed to something prettier? Looks like a CamelCase abuse", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92575416"}}
{"comment": {"body": "@igal_leveltech Didn\u2019t you do this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92575954"}}
{"comment": {"body": "decision field is either 1 or 0. Any reason not make this variable bool?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92577388"}}
{"comment": {"body": "This struct is duplicated A LOT. I had to make it as small as possible.\n\ntimestamp was reduced from 64bit to 32bit by converting it from ms to seconds\n\ndecision and anchor\\_id only need `uint8_t` each but if I set them to `uint8_t` the compiler pads the entire struct with `uint16_t` anyway so to avoid padding I set them both to `uint16_t` and now the struct is neatly packed\n\nThere's still room for improvement here, like moving all the timestamps to a separate array and having the decision and anchor\\_id be `uint8_t` each but there\u2019s no time for this now", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92578907"}}
{"comment": {"body": "I see", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92579505"}}
{"comment": {"body": "Are all features in the dataset actually valid?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92583197"}}
{"comment": {"body": "I wonder how long it took you to catch this buffer overflow\u2026 Good job", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92584338"}}
{"comment": {"body": "Who\u2019s this \u201c12345\u201d?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92584849"}}
{"comment": {"body": "not yet, I am waiting for the code to be in develop", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92584930"}}
{"comment": {"body": "It's just event\\_deserialiser.py so it doesn't matter... But thanks for reminding I need to fix this proper for `` `event_pickler.py``", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92585260"}}
{"comment": {"body": "Only like 2 hours :disappointed: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92585409"}}
{"title": "BIS-2298 when levlclassify exits on erro", "number": 316, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/316", "body": "Fixed bug where progress was not encrypted properly\nFixed minor bug in the regression tests using an uninitialized value."}
{"title": "Will now publish cppcheck results as junit", "number": 317, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/317", "body": ""}
{"comment": {"body": "Please merge this", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/317/_/diff#comment-92572841"}}
{"title": "Fixed bug where multiple training started events were sent", "number": 318, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/318", "body": ""}
{"title": "Fixed ST compilations", "number": 319, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/319", "body": ""}
{"title": "Feature/FIN-340 Jenkins & system tests c", "number": 32, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/32", "body": "adding python script to reboot jlink-attached device\nminor reordering\nstarting to add foundations for system tests\nadding more wrappers\nmoving files around\n*fixing paths\nfix paths\nfixing paths\nwrapping up system tests flow and utils adding example on how to run system test\ntrying to run system tests\ncommenting out stuff so that system tests would\nforcing python 3.6\nfixing search scripts\nfix jlink path\nfix paths\nfix serial port access\nuninstalling previous android app before installing\ntrying another way to enumerate ports\nanother try to fix enumeration\ncausing test to fail\n\nRevert \"causing test to fail\"\nThis reverts commit ae152fc39d2dedc69eac95725cb8a89b4fa42a3a.\n\n\nprettifying and cleaning up\n\noutputing test reports"}
{"comment": {"body": "It\u2019s great to see it working. Still a lot to do to make the code reusable for different tests but we can do it later.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/32/_/diff#comment-71698699"}}
{"title": "Fixed fingerprinting_manual project", "number": 320, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/320", "body": ""}
{"title": "Temporary Revert of \"BIS-2298 when levlclassify exits on erro (pull request #316)\"", "number": 321, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/321", "body": "This reverts pull request #316.\n\nFixed bug where progress was not encrypted properly Fixed minor bug in the regression tests using an uninitialized value."}
{"title": "update for monitor_v2", "number": 322, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/322", "body": ""}
{"comment": {"body": "you are taking now one vote - before you took the whole array of votes - didnt you>", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/322/_/diff#comment-92710398"}}
{"comment": {"body": "you are taking now one vote - before you took the whole array of votes - didnt you>", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/322/_/diff#comment-92710405"}}
{"comment": {"body": "Yup. Right now there seems to be no use to \u201cnext voting\u201d, I\u2019m keeping it anyway in case we\u2019ll use it later.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/322/_/diff#comment-92710427"}}
{"title": "Regression tests for multi anchor", "number": 323, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323", "body": "New regression data set with testbus3\n\nFixing to make test pass\n\nChange treshold in CFO classification\nUse rtc timestamp from metadata struct for all timemout logic"}
{"comment": {"body": "Did you try increasing it further? How far until regression breaks?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710395"}}
{"comment": {"body": "The regression test run-time increased again :disappointed: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710409"}}
{"comment": {"body": "Wouldn\u2019t it cry about redefinition in win 64bit?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710449"}}
{"comment": {"body": "No.\n\nThis is a quick fix to a systematic issue with our CFO model when taking 2 devices with completely different CFO models and if by random one of their channels \\(37,38,39\\) overlap than we always return \u201cMATCH\u201d. Raising it to 1/2 make the model to match more than 1 channel.\n\nWe will solve in a better way in the next version.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710452"}}
{"comment": {"body": "Probably would.\n\nFixed in a4bba0d990288d4ca025f135107f1792c44e66a2", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710547"}}
{"comment": {"body": "What\u2019s the reason for splitting logic between 32 bit and 64 bit? It worked before", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710597"}}
{"comment": {"body": "It broke with the new dataset and I didn\u2019t manage to figure out why, so I just started from scratch.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710634"}}
{"comment": {"body": "So it\u2019s not working under 64 bit?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710682"}}
{"comment": {"body": "It\u2019s now working :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710692"}}
{"comment": {"body": "Update the list of tests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710694"}}
{"comment": {"body": "Update the list of tests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710695"}}
{"comment": {"body": "Updated 8b78935e4c0ef47b6aa4e1ad1dbac718019547e3", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710768"}}
{"comment": {"body": "Updated 8b78935e4c0ef47b6aa4e1ad1dbac718019547e3", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/323/_/diff#comment-92710771"}}
{"title": "remove not needed configuration", "number": 324, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/324", "body": ""}
{"comment": {"body": "Do we want to delete the baseband\\_correction\\_960k.h file and fix baseband\\_correction.c to work only with 1M? ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/324/_/diff#comment-92711011"}}
{"comment": {"body": "I just wanted to not have not relevant options in the configuration struct\u2026 I dont care to leave this file\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/324/_/diff#comment-92711090"}}
{"comment": {"body": "IMHO we should delete it given that we don\u2019t call it in the current configuration and we even don\u2019t have an easy way to call it after we delete the relevant configuration.\n\nGuess it\u2019s not urgent though. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/324/_/diff#comment-92711623"}}
{"comment": {"body": "I see that actually we can call it via `baseband_corr_param`. Not sure we should delete it in this case :\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/324/_/diff#comment-92711789"}}
{"comment": {"body": "What is the reason for moving from 32 to 64 bits here?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/324/_/diff#comment-92711835"}}
{"comment": {"body": "Currently we put the time in this variable which is 64 bit. I am not sure why it was 32 bits in the first place\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/324/_/diff#comment-92711858"}}
{"comment": {"body": "There are dialog/ST projects that are settings `Levl_FeatureCfg_internal_t`. Make sure to update them as well", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/324/_/diff#comment-92739074"}}
{"comment": {"body": "right, will do that in a separate PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/324/_/diff#comment-92748002"}}
{"title": "monitor update - with correct voting table size", "number": 325, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/325", "body": ""}
{"title": "Nuriel develop added ITD Test", "number": 326, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/326", "body": "update ITD tests\nremoved prints\nno more comments\nmerge with develop"}
{"comment": {"body": "Do I merge?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/326/_/diff#comment-92715250"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/326/_/diff#comment-92715384"}}
{"comment": {"body": "After you fix the failing tests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/326/_/diff#comment-92752318"}}
{"title": "Manual-fingerprinting TID fingerprinting", "number": 327, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/327", "body": "Added slider to Android app with TID channel\nevent_pickler.py now tells manual_fingerprinting the TID to filter according to"}
{"title": "add packet quality", "number": 328, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/328", "body": ""}
{"comment": {"body": "Are you testing that there\u2019s no missing padding?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/328/_/diff#comment-92713763"}}
{"comment": {"body": "Any tests for this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/328/_/diff#comment-92713767"}}
{"comment": {"body": "Nuriel is writing the test ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/328/_/diff#comment-92713788"}}
{"comment": {"body": "What do you mean?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/328/_/diff#comment-92713797"}}
{"comment": {"body": "If you put `packet_quality` before `timestamp` it will eliminate compiler padding so the `__padding` field can be removed from the Hydra version of this struct", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/328/_/diff#comment-92714083"}}
{"comment": {"body": "What @omer_levltech said", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/328/_/diff#comment-92714257"}}
{"comment": {"body": "fixed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/328/_/diff#comment-92736207"}}
{"comment": {"body": "fixed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/328/_/diff#comment-92736228"}}
{"title": "Feature/BIS-2153 start implementing hw tests wit", "number": 329, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329", "body": "Change log:\n\n\nRevert \"Feature/BIS-2153 start implementing hw tests wit (pull request #302)\"\nThis reverts pull request #302.\nBring back support for ST NVM and tests\n\n\nFix some makefile rules to fix dependencies\n\nUpdate ST fingerprinting manual and testing agent with the latest finalize changes\nUpdate ST testing agent Fix dependecies of projects built\nFixed prepare_phones bug\nUpdate board instance to more gracefully handle events\nBetter threading handling\nAdd debug message support in ST with debug descriptors Fix Levl_Classify compilation in testing agent\nReduce support to 1 max anchor for ST since thats what the ST can support for now\n\nST requires further work for 2 anchors support due to large data structures"}
{"comment": {"body": "We have more places where board instances are created in the tests themselves.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92748687"}}
{"comment": {"body": "Couple still means 2", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92750724"}}
{"comment": {"body": "If you're missing events this is not a good solution. Itll just means that when events go missing you'll have an even harder time realizing what went wrong ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92751576"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92752183"}}
{"comment": {"body": "TODO: fix later\n\nWe can still find those missing packets using the event counter", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92752229"}}
{"comment": {"body": "We are actually using the data from the metadata structure \\(timestamps and phone temperature\\). We need to supply it here.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92754292"}}
{"comment": {"body": "No other project right now fills these fields..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92754744"}}
{"comment": {"body": "But if this happens so often that you had to comment it out, it means it often makes other stuff not work and we won\u2019t understand why while debugging. At the very least raise a warning, and even that should be temporary. Why are you losing events? With ThreadedSerial this should no longer happen. This is very weird", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92755264"}}
{"comment": {"body": "ST happens to rarely lose events", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92761777"}}
{"comment": {"body": "Fixed in 3eed8c271342afe164977fb2ddd9ed3d732a84e8", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92771431"}}
{"comment": {"body": "Fixed in aca73944b14b21e6f9d2b32a5c4ab60650e1cd5a.\n\nOnly manual tests and `test_QT_FEATURE_006_Serializing_and_deserializing_a_feature_set` are not yet fixed. in future PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/329/_/diff#comment-92771655"}}
{"title": "FIN-335 cfo model reduce memory usage", "number": 33, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/33", "body": "move the possible online stage of the linear regression to separate funcion\nuse make_matrix, expand init internal state\nadded linear regression finalize funcyion\nremove the force const board temperature\nbreak calc_cfo_model to sub funcs\ncfo_model internal state\nadded test for online cfo model build + memset linear regression result to 0\nintegrate online cfo model creation to LEVL_fingerprinting.c\nlinkage fix\nmore code cleanup and documentation\nmore code cleanup\nmore cleanup"}
{"comment": {"body": "Looking great!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/33/_/diff#comment-72011005"}}
{"comment": {"body": "Did you test it with real dialog and phones?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/33/_/diff#comment-72012177"}}
{"comment": {"body": "No, I didn\u2019t", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/33/_/diff#comment-72012193"}}
{"comment": {"body": "Ran some tests, the overall model looks OK. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/33/_/diff#comment-72012880"}}
{"comment": {"body": ":thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/33/_/diff#comment-72013259"}}
{"title": "Fix IQ recording agent not sending IQ", "number": 330, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/330", "body": ""}
{"title": "Feature/BIS-2941 fix issues with iq on system tests", "number": 331, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/331", "body": "Added handling of IQ packets in the event deserializer.\n\nRevert \"Added handling of IQ packets in the event deserializer.\"\nThis reverts commit a6935166\n\n\nMade IQ as an event\n\nRefactored event deserializer's digestion system to improve speed and enable IQ packets to be transferred along side to regular events.\nRestored iq handling functions, not working yet.\nFixed IQ handling functions."}
{"comment": {"body": "Please also remove the mark.xfail from the IQ tests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/331/_/diff#comment-92796983"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/331/_/diff#comment-92799210"}}
{"comment": {"body": "Make the build pass", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/331/_/diff#comment-92802139"}}
{"title": "Restored temporary reverted pull request.", "number": 332, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/332", "body": "These fixes were reverted temporally, now being re-applied."}
{"comment": {"body": "Awesome code!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/332/_/diff#comment-92791518"}}
{"comment": {"body": ":heart_eyes: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/332/_/diff#comment-92791646"}}
{"title": "Feature/BIS-2153 start implementing hw tests wit", "number": 333, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/333", "body": "Fix struct description generated from hydra not handling arrays of structs correctly\nFix event descriptors in C\nRemove remaining anchor id/session id usage"}
{"title": "update", "number": 334, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/334", "body": "major update\nmulti anchor basic infra and event ordering amendment"}
{"title": "fix events enum", "number": 335, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/335", "body": ""}
{"title": "Remove time limit in voting table", "number": 336, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/336", "body": "The alternative is to have the maximal number of votes defined by the number of anchor configuration."}
{"comment": {"body": "Ran into this annoying bug as well", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/336/_/diff#comment-92842754"}}
{"comment": {"body": "Please write a comment of why this test is xfailed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/336/_/diff#comment-92844181"}}
{"title": "Feature/fix struct descriptions again", "number": 337, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/337", "body": "Issues with C descriptors\nFix hidden padding in packet ID and relevant structures\n\nWe need to automate these things"}
{"title": "Fix missing event data bytes", "number": 338, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/338", "body": "Make it work with ST again"}
{"title": "Feature/BIS-2938 create a system test demo with 2 anchors", "number": 339, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/339", "body": "Added an optional (--multi-demo flag to system tests) test to system tests that runs featex on 2 anchors and models in Python"}
{"title": "Publish jenkins to jira", "number": 34, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/34", "body": "Added build step to public issues to jira using zephyr\nFix jenkinsfile\nFixed systemtest results directory"}
{"comment": {"body": "publishing unittests is missing, no?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/34/_/diff#comment-71815697"}}
{"comment": {"body": "Nope, this is pretty ugly but I just copy the results to another folder and trigger another Jenkins job which publishes the test results to JIRA.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/34/_/diff#comment-71826647"}}
{"title": "Feature/BIS-2992 packet metadata dialog standalone", "number": 340, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/340", "body": ""}
{"comment": {"body": "Looks like this define is not needed anymore", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/340/_/diff#comment-92856610"}}
{"title": "Fix ST", "number": 341, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/341", "body": ""}
{"title": "update - last training model is always first model - deletes all online models before last training model", "number": 342, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/342", "body": ""}
{"title": "change phone_temperature in metadata to int from uint", "number": 343, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/343", "body": ""}
{"title": "Fixed issue where system tests hangs on timeout.", "number": 344, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/344", "body": ""}
{"title": "ESPECIALLY FOR OMER AND KAPLAN - a long and excessively verbose comment for the commit without any shorts", "number": 345, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/345", "body": "Fixed a bug wherein the models reorganizing would cause the function to crash."}
{"comment": {"body": "Why are you being so verbose? This is just monitor code, it\u2019s not critical, why bother?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/345/_/diff#comment-92951455"}}
{"comment": {"body": "I\u2019ve read your commit message and still cannot understand what you did there from it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/345/_/diff#comment-92953911"}}
{"title": "ESPECIALLY FOR OMER AND KAPLAN - a long and excessively verbose comment for the commit without any shorts", "number": 346, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/346", "body": "Organizes inializations"}
{"title": "Always update training and classification progress packet ID, not just when successful", "number": 347, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/347", "body": "last_feature_extraction_packed_id in both training and classification progress will now be updated as soon as possible, even in-case of errors"}
{"title": "Removed encryption from the regression tests to speed up the testing process", "number": 348, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/348", "body": ""}
{"comment": {"body": "PR tests fail because of bad IQ reception, will be fixed in the next PR.\n\nMerging this one for now.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/348/_/diff#comment-92985447"}}
{"title": "Feature/mutli anchor metadata", "number": 349, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/349", "body": "Event deserializer can now serialize events\nCreate new event feature extraction with metadata that the ST will receive\nCreate feature_with_metadata from encrypted features and captured packet\nST receives metadata successfully\nFix ST timers"}
{"comment": {"body": "Can also use `TypedArray(3, UInt8())`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/349/_/diff#comment-93010807"}}
{"comment": {"body": "Why remove this? it\u2019s very helpful when trying to understand which fields don\u2019t match", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/349/_/diff#comment-93011127"}}
{"comment": {"body": "I\u2019m not the one to put it here", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/349/_/diff#comment-93013142"}}
{"comment": {"body": "There are no \\*\\_name variables, so the code doesn\u2019t run. Still, there\u2019s also enough information with the prints, IMHO", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/349/_/diff#comment-93013309"}}
{"title": "Feature/BIS-43 Falling transient feature integration", "number": 35, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/35", "body": "adding .gitignore\nrefactoring falling transient model to work sample by sample integrating falling transient model to Levl flow\nfixing tests\ntelling cmake to output more warnings"}
{"title": "BIS-2961 returning training complete whe", "number": 350, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/350", "body": "Removed redundant passing of external structs for internal functions.\nFixed test_no_complete_after_build_model_fail"}
{"title": "python will now filter out packets with low AGC when testing the IQ.", "number": 351, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/351", "body": ""}
{"title": "Feature/fixes for multianchor", "number": 352, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/352", "body": "Fix same anchor reported when multiple anchors Fix filtering for multiple anchors\nST supports 2 anchors, dialog only 1"}
{"title": "missing tests and missing lib init check in finalize", "number": 353, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/353", "body": ""}
{"title": "All agents (including ST) will now use the default library configuration", "number": 354, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/354", "body": ""}
{"title": "Feature/phone temp only in metadata", "number": 355, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/355", "body": "Removed some of the phone_temperature instances\nMore removing of phone_temperature\nMore removal of phone_temperature\nMore removing of phone_temperature\nRemoved every more phone temperature from many places\nRemove usage of phone temperaute in captured packet\nRemove even more phone temperature\nFixed libdialogboilerplate phone_temperature issues\nFix dynamic analysis\nCapturedPacket alignment makes more sense now\nNew regression tests\nTest invalid phone temperature passed to training/classification"}
{"comment": {"body": "Are anchor id and phone temperature fields set in the serialized data?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/355/_/diff#comment-93110075"}}
{"comment": {"body": "How come we have invalid temperatures in our dataset?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/355/_/diff#comment-93110968"}}
{"comment": {"body": "@michlevltech ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/355/_/diff#comment-93111193"}}
{"comment": {"body": "Nuriel randomly generated phone temperatures centered 25 with some std, some of them went past 0 and 40, so I just clipped it to 0 and 40", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/355/_/diff#comment-93111579"}}
{"comment": {"body": "Any plans for this test? If no, then it\u2019s best to remove it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/355/_/diff#comment-93111705"}}
{"comment": {"body": "It needs to be replaced with a test that somehow checks that the board knows how to extract the temperature from the packet", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/355/_/diff#comment-93112785"}}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/3600770977-image.png)\n@gregory-levl ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/355/_/diff#comment-93119106"}}
{"title": "Online training adaptive training of device temperature slope", "number": 356, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356", "body": "We can train the temperature of a device only if we have enough temperatures from the same device (At least 3C variation), if we dont have the required variation - we fallback to use the constant slope for the model (by making the linear regression ignore the values and use a constant slope).\n\nAlso add regression tests (and regression data) specific for online training."}
{"comment": {"body": "What about MINOR update? should we have tests for that?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93087157"}}
{"comment": {"body": "We have other system/integration tests that are checking that.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93089816"}}
{"comment": {"body": "why 600?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93089900"}}
{"comment": {"body": "It represents better the phones in our dataset.\n\nS8 and MATE10 has slope of 400-450 while S9 and Pixel has slopes of 750-900.\n\nLets talk about it in person", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93090546"}}
{"comment": {"body": "Checked on all our records, the median slop over 40 phones is -465, the average is -480. \n\nThat said it\u2019s probably not that important, over 3C the diff between -600 and -450 is not that big and after we got enough temperatures we learn the slop from the data.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93098281"}}
{"comment": {"body": "Aren\u2019t those float32\\_t anyway? defined as 0.0f and 40.0f", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93099586"}}
{"comment": {"body": "same here, maybe the cast is redundant? ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93099652"}}
{"comment": {"body": "ok, noticed you changed the definition to  0U and 40U. So it makes sense. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93099794"}}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/2553237017-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93113213"}}
{"comment": {"body": "`variables_to_ignore_mask` should be const.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93114732"}}
{"comment": {"body": "This change should also be included in `system_tests/consts.py`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93115866"}}
{"comment": {"body": "Does it matter for non pointers? ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93116155"}}
{"comment": {"body": "This will always be true due to alignment, what is this test for?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93116583"}}
{"comment": {"body": "Don\u2019t we have another file already declaring this struct?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93116698"}}
{"comment": {"body": "We\u2019re not testing `LEVL_FEATURE_USE_FULL`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93116972"}}
{"comment": {"body": "We don\u2019t have it implemented it yet..\n\nWe assume constant board temperature slope.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93118480"}}
{"comment": {"body": "Yes. We have some code duplication at the moment. We will fix it after the release.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93118592"}}
{"comment": {"body": "Fixed in 649c69db", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/356/_/diff#comment-93120311"}}
{"title": "M2 wasn't pulled up...", "number": 357, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/357", "body": "Fixed GPIO usage on ST"}
{"title": "Fixed issue where wrong flash configuration was chosen after moving to the new sdk", "number": 358, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/358", "body": "Ive also cleaned up the custom_config_qspi.h file so eclipse will know to recognise it better."}
{"comment": {"body": "Did you test it works on the new chip? If you didn\u2019t, please create a Jira task to do so", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/358/_/diff#comment-93113063"}}
{"title": "Fix anchor ID not correctly set", "number": 359, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/359", "body": ""}
{"title": "Added doxygen support", "number": 36, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/36", "body": ""}
{"title": "Add temperature history histogram to the CFO model", "number": 360, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360", "body": "The histogram is used for 2 main use cases:\n\nDecide when to move the CFO between a constant slope to trained slope.\nDecide when we have new temperatures in online training and when to return MINOR/MAJOR model update."}
{"comment": {"body": "Include can be simplified", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93134700"}}
{"comment": {"body": "What\u2019s the purpose of counter? There\u2019s no usage of it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93135703"}}
{"comment": {"body": "It cannot.\n\nOtherwise it fails eclipse build.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93136138"}}
{"comment": {"body": "It\u2019s in the same directory\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93136443"}}
{"comment": {"body": "I know. It stills fails..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93136564"}}
{"comment": {"body": ":hushed: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93136691"}}
{"comment": {"body": "The purpose was to have rules that are proportional to the number of samples \\(percentiles etc\\).  It seems it won\u2019t be used in V2.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93136800"}}
{"comment": {"body": "A comment describing an overview of the algorithm would be nice here:\n\n`// We have enough packets when the distance between the lowest and highest temperatures that have the minimum amount of packets is more than the minimum allowed distance`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93137037"}}
{"comment": {"body": "It's because we have `-I-` \\(disables `.` includes\\) so we can override SDK includes\n\n`#include \"training/temperature_histogram.h\u201d` should work", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93137946"}}
{"comment": {"body": "Maybe call it \u201cDISTANCE\u201d to be consistent with the histogram naming", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93140585"}}
{"comment": {"body": "Not critical ATM but this init doesn\u2019t make sense since the sum of the histogram elements is not equal to the counter", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/360/_/diff#comment-93142173"}}
{"title": "Feature/include metadata in progress", "number": 361, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/361", "body": "Add event 16: FingerprintingPacketMetadata \nFix temperature extraction from packet"}
{"title": "Agents will no longer pass 0 metadata to classification", "number": 362, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/362", "body": ""}
{"comment": {"body": "Beautiful code!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/362/_/diff#comment-93166953"}}
{"title": "Save finalized model to flash", "number": 363, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/363", "body": ""}
{"comment": {"body": "What is done with `flash_tid`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/363/_/diff#comment-93169440"}}
{"comment": {"body": "It\u2019s used for untethered filtering during classification\u2026 For now filtering is a bit broken when it comes to the Bosch demo so it doesn\u2019t really matter, we\u2019ll have to figure out later", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/363/_/diff#comment-93170089"}}
{"title": "Feature/fix hw tests", "number": 364, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/364", "body": "Will now save packets metadata to the flash for the serialization test\nFixed bugs in the handle_classify_from_flash function\nAdded pickle registry to pickle board agents which are being created not using the fixture. Some more fixes to the hw tests.\nFixed falling transient test being too strict."}
{"comment": {"body": "Magnificent code!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/364/_/diff#comment-93193943"}}
{"title": "Bosch demo will now DEMO_PRINTF training progress, demo protocol script will now ask for shelter", "number": 365, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/365", "body": ""}
{"title": "Fixed wrong Android slider starting value indication", "number": 366, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/366", "body": ""}
{"title": "Feature/fix hardware tests", "number": 367, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/367", "body": "Remove redundant code \nAlso send TID filter to anchor\nFix hangs\nPrint anchors count\nAttach source port to agent event\nSome bugfix in board_pair\nUpdate ST with classify_from_flash"}
{"title": "major update - multianchor works and fun stuff", "number": 368, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/368", "body": ""}
{"title": "Make IQ more flexible", "number": 369, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/369", "body": "Due to some weird bug in IQ printing, sometimes the IQ comes at an offset. We try to look at the IQ in all possible offsets until we find the access address. The root problem should be investigated in the future"}
{"title": "Feature/BIS-11 Integrate Klocwork into pipelines", "number": 37, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/37", "body": "adding .gitignore\ntrying klocwork in pipeline\nfixing jenkinsfile\nfixing jenkins\nanother go at jenkinsfile\nanother go at jenkinsfile\nwrong arguments format\nupdating cmakelist - might help to build\ntrying new format of jenkinsfile\nbad path at jenkinsfile\ncorrecting paths\ntrying to fix licensing problem\nanother go at jenkinsfile\nupdated paths\nupdate paths\nfailing builds on critical errors\ntrying to fail build\ntrying to fail locally\nanother path\nanotehr take at klocwork\nanother take\nadding paths\nCI\ntrying different jenkis steps\njenkins\nadding python script to parse static analysis\ncollecting reports"}
{"comment": {"body": "need your comments on expected behavior", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/37/_/diff#comment-72014519"}}
{"comment": {"body": "conflict to resolve", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/37/_/diff#comment-72015099"}}
{"comment": {"body": "It seems like this test will always fail\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/37/_/diff#comment-72015139"}}
{"comment": {"body": "test is not really running; mode like an example usage; but i\u2019ll fix it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/37/_/diff#comment-72029405"}}
{"comment": {"body": "merge issues appear only after PR submittion", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/37/_/diff#comment-72029446"}}
{"comment": {"body": "fixed in 988220324c2f300310693fd8d92f8b7c9b2f8c2d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/37/_/diff#comment-72040223"}}
{"comment": {"body": "fixed in 1d3a30af59184914a25f3db653d4011c4a31591e", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/37/_/diff#comment-72040285"}}
{"title": "Advance release version", "number": 370, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/370", "body": ""}
{"title": "Removed symbols used by feature extraction from the training module", "number": 371, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/371", "body": ""}
{"title": "omer please tell me your are still happy with my naming", "number": 372, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/372", "body": "monitor update, expects one training sequence at the beginning of the record"}
{"title": "Fix timings", "number": 373, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/373", "body": "Dialog doesn't report the correct rtc_timestamp when creating metadata\nUpdate event_deserializer & pickler sleeps"}
{"title": "Run regression with encryption only on tests that require regression with encryption", "number": 374, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/374", "body": ""}
{"title": "In final training call will now report training progress before reporting model", "number": 375, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/375", "body": ""}
{"title": "Levl_Train will now check if the model has been initialized", "number": 376, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/376", "body": ""}
{"title": "Feature/test with obfuscated lib", "number": 377, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/377", "body": "Will now use the obfuscated lib for testing.\nFixed building script so it will now be linking with the obfuscated library\nAdded LEVL_PREINCLUDE_FILE option\nFixed compilation of private symbols after obfuscation\nFixed compilation error"}
{"title": "Verify configuration input on number of models.", "number": 378, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/378", "body": ""}
{"title": "Merge develop into release V2", "number": 379, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/379", "body": "Will now use the obfuscated lib for testing.\nFixed building script so it will now be linking with the obfuscated library\nAdded LEVL_PREINCLUDE_FILE option\nFixed compilation of private symbols after obfuscation\nFixed compilation error"}
{"title": "Feature/BIS-11 Integrate Klocwork into pipelines", "number": 38, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/38", "body": "adding .gitignore\ntrying klocwork in pipeline\nfixing jenkinsfile\nfixing jenkins\nanother go at jenkinsfile\nanother go at jenkinsfile\nwrong arguments format\nupdating cmakelist - might help to build\ntrying new format of jenkinsfile\nbad path at jenkinsfile\ncorrecting paths\ntrying to fix licensing problem\nanother go at jenkinsfile\nupdated paths\nupdate paths\nfailing builds on critical errors\ntrying to fail build\ntrying to fail locally\nanother path\nanotehr take at klocwork\nanother take\nadding paths\nCI\ntrying different jenkis steps\njenkins\nadding python script to parse static analysis\ncollecting reports\nmoving static analysis stage in parallel with build to speed up execution\nfixing jenkinsfile\nprettifying jenkinsfile\npython test to make some sense\nprettifying results\n\nRevert \"prettifying results\"\nThis reverts commit 68df4c5ae6f91198000d65004ae77aa6a2d88655.\n\n\nfixing static analysis issues\n\nfixing another static analysis issue\nfixing (C99)%22\nhandling overflows: (C99)%22\noutputting at least one testcase in static analysis"}
{"comment": {"body": "We should fail the cfo estimation if we don\u2019t have enough samples. But that\u2019s probably for another commit..  \n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/38/_/diff#comment-72039727"}}
{"comment": {"body": "nice catch! What was the reason for the second correction \\(line 106\\) after we add the first one \\(line 99\\)?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/38/_/diff#comment-72040558"}}
{"comment": {"body": "Great question! Buffer overflow error from the static analysis tool, but it\u2019s probably a false positive due to the first fix", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/38/_/diff#comment-72041035"}}
{"comment": {"body": "it\u2019s not testing for minimum number of samples here; more for too many samples.\n\nstill - for another day", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/38/_/diff#comment-72047239"}}
{"title": "Feature/test with obfuscated lib (REOPENED -> Merge into Release instead of develop)", "number": 380, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/380", "body": "Will now use the obfuscated lib for testing.\nFixed building script so it will now be linking with the obfuscated library\nAdded LEVL_PREINCLUDE_FILE option\nFixed compilation of private symbols after obfuscation\nFixed compilation error"}
{"comment": {"body": "Amazing code.\n\nWay to go!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/380/_/diff#comment-93322329"}}
{"comment": {"body": "You should learn something from @omer_levltech ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/380/_/diff#comment-93323015"}}
{"title": "change board slop to 800", "number": 381, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/381", "body": "change the board temp slop"}
{"title": "Small fix to configuration validity - non zero number of anchors", "number": 382, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/382", "body": ""}
{"title": "Feature/data sections function sections", "number": 383, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/383", "body": "Add Dialog & ST compiler flags\nObfuscated sections as well\nLevl_GetMultiAnchorClassifyFinalizeResult should be obfuscated\nST projects will link with obfuscated library before the regular one"}
{"title": "histogram works now as well as some model sequences issues that are corrected", "number": 384, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/384", "body": ""}
{"title": "V2 release", "number": 385, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/385", "body": ""}
{"title": "Release/V2 RC", "number": 386, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/386", "body": "Merge V2 into develop"}
{"title": "Wait for more packets", "number": 387, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/387", "body": "Verify number of packets in tests"}
{"title": "Feature/merge v2 rc", "number": 388, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/388", "body": "Insert V2_RC into develop\nFix merge issues"}
{"title": "Added physec_iq_agent", "number": 389, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/389", "body": ""}
{"comment": {"body": "What are the difference between the physec agent and our regular recording agent?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/389/_/diff#comment-93862584"}}
{"comment": {"body": "TID filtering via UART protocol and some LED\u2019s. Didn\u2019t want to make any changes to the iq\\_recording\\_agent because some systems may rely on it having no filters", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/389/_/diff#comment-93869158"}}
{"comment": {"body": "Got it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/389/_/diff#comment-93870375"}}
{"title": "Feature/BIS-43 Falling transient feature integration", "number": 39, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39", "body": "refactoring falling transient model to work sample by sample integrating falling transient model to Levl flow\nfixing tests\ntelling cmake to output more warnings\nWIP\nfinished refactoring falling trans extraction is looking only after the packet data end\nfixing typo\nminor warning removal minor refactoring\nanother pass at optimizing falling transient\nfixing test\nfor now, we don't need building android app when not running system tests\noptimizing sqrt routine"}
{"comment": {"body": "PR is open to public", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72215778"}}
{"comment": {"body": "In this for loop we compute the start of the transient given that we found the end of the transient \\(best\\_i\\).\n\nWe can avoid the loop by storing the start in the previous for loop \\(the one that finds the best end\\).\n\nDo you want to discuss/fix it now or do you prefer to do it after this pull request?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72227440"}}
{"comment": {"body": "Sounds great, but after this PR since the current implementation achieves the timing requirements", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72228828"}}
{"comment": {"body": "cool", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72231646"}}
{"comment": {"body": "What about this TODO? can it be removed?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72384617"}}
{"comment": {"body": "I don\u2019t mind having another PR for this but let\u2019s do it right after so we will not forget", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72386647"}}
{"comment": {"body": "Your test is good but we need to think how we test features to cover all scenarios", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72387516"}}
{"comment": {"body": "Can you add a replacement test if this one is not good?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/39/_/diff#comment-72387734"}}
