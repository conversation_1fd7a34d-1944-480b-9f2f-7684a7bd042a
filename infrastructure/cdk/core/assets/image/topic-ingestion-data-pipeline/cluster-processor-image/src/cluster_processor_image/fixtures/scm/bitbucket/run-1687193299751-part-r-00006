{"comment": {"body": "worth thinking about, but definitely not for embedded environment", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/16/_/diff#comment-70651699"}}
{"title": "Tried fixing issue where <PERSON><PERSON><PERSON> does not delete the workspace.", "number": 160, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/160", "body": ""}
{"title": "add model trainded flag", "number": 161, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/161", "body": ""}
{"title": "Fix test case of input phone temperature (missing multiplier by 100)", "number": 162, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/162", "body": ""}
{"comment": {"body": "* \\* 100 is missing\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/162/_/diff#comment-85227067"}}
{"comment": {"body": "Yes. Sorry. My mistake.\n\nFixed in 12a5869b2a9fcfdf4a9e16c2cb11fca5a24d44b2", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/162/_/diff#comment-85227186"}}
{"title": "fix classify init tests", "number": 163, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/163", "body": ""}
{"comment": {"body": "What about the classification init test?\n\nWould it be in a separate PR?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/163/_/diff#comment-85236365"}}
{"comment": {"body": "are we ok with this error code \\(note trained instead of not initialized?\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/163/_/diff#comment-85238107"}}
{"comment": {"body": "Yes, its OK", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/163/_/diff#comment-85238405"}}
{"comment": {"body": "fixed in commit [88df665](https://bitbucket.org/levl/bosch_integration/commits/88df665627142774c8c264b6b842723f58ab5279?at=develop)  ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/163/_/diff#comment-85238460"}}
{"title": "Hotfix/errors for uninitialized objects", "number": 164, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/164", "body": "validate feature and progress before accessing number of packets in progress\nremove hide from uninitialized training progress object test"}
{"title": "Feature/errors for uninitialized objects", "number": 165, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/165", "body": "validate feature and progress before accessing number of packets in progress\nremove hide from uninitialized training progress object test"}
{"comment": {"body": "Awesome code!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/165/_/diff#comment-85240292"}}
{"title": "Feature/missing hw tests", "number": 166, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/166", "body": "Added missing save model instruction\nFixed hw testing code with new API.\nAdded classify from flash feature to the embedded side\nAdded missing feature serialization hw test."}
{"title": "Enabled system tests through Jenkins", "number": 167, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/167", "body": ""}
{"comment": {"body": "Pleaese also add a line to publish the junit report to Jira like in other tests \\(`XrayImportBuilder` step\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/167/_/diff#comment-85798397"}}
{"comment": {"body": "Maybe also do Develop at 3 AM or something?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/167/_/diff#comment-85983079"}}
{"comment": {"body": "I think that for now we will leave it this way, when we will continue working on develop we will add it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/167/_/diff#comment-85998267"}}
{"comment": {"body": "What\u2019s the effect of this? Does it mean that PRs can be approved/merged only after a nightly run?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/167/_/diff#comment-86015112"}}
{"title": "Refactor Levl_FeatureExtract", "number": 168, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/168", "body": ""}
{"comment": {"body": "What is the meaning of \u201cSpecs\u201d?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/168/_/diff#comment-85796018"}}
{"comment": {"body": "Specifications that are covered under OUT\\_OF\\_BLE\\_SPECS detection", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/168/_/diff#comment-85796060"}}
{"title": "Release/V1 0", "number": 169, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/169", "body": "Update develop with on-going changes happening in the release candidate"}
{"comment": {"body": "All changes in the release candidate didn\u2019t go through a code review process?\n\nI think that in the future, any changes in a release candidate should also be done with pull requests into the release candidate so we can resolve issues one by one instead of having to create a \u201cgiant\u201d pull request of the release candidate into develop with many unrelated changes.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/169/_/diff#comment-85798769"}}
{"comment": {"body": "I did a couple of changes not through PRs. Won\u2019t happen again.\n\nAll RC changes are relevant to develop and should be merged into develop. I think that if we do incremental PRs, we should be fine and will not have giant PRs.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/169/_/diff#comment-85798838"}}
{"title": "Falling transient model and classification", "number": 17, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/17", "body": ""}
{"comment": {"body": "no tests for this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/17/_/diff#comment-70651945"}}
{"comment": {"body": "we need to think about this contamination of internal lib includes in external API", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/17/_/diff#comment-70651984"}}
{"comment": {"body": "Added tests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/17/_/diff#comment-70652006"}}
{"comment": {"body": "We really need to think about it.  \nAll the structure here will contain our internal structure for features/models.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/17/_/diff#comment-70652045"}}
{"comment": {"body": "missing include to normal model. redundant include to statistics", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/17/_/diff#comment-70652111"}}
{"comment": {"body": "good for debugging, bad for production.\n\nnormally internal structures are hidden behind `void *`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/17/_/diff#comment-70652133"}}
{"title": "Feature/main loop (Manual fingerprinting) (updated to merge into release instead of develop)", "number": 170, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/170", "body": "Deleted old irrelevant mainloop\nParallel build changed from optimal to unlimited in boilerplate 9x project\nMoved CRC to its own file\nFixed Eclipse indexing bug\nAdded flash_model.h and flash_model.c to boilerplate to allow easy verified storing and loading of the fingerprinting model from flash.\nInitial version of manual fingerprinting project\nevent_deserialiser.py more informative\nModel event now has a trained field to indicate whether it was trained or loaded from flash\nRGB test\nRGB LED now shows the last result of feature extraction\nRGB handling moved to its own file\nFixed model flash CRC bug\nFingerprinting will now be shown on RGB\nFlash model saving now actually works\nButton will now reset the fingerprinting process"}
{"comment": {"body": "What is Ethernet, ZIP?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/170/_/diff#comment-85888784"}}
{"comment": {"body": "It\u2019s the CRC-32 polynomial used by Ethernet, ZIP and many others. It\u2019s the most popular polynomial, used by the Python 3 binascii.crc32 function", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/170/_/diff#comment-85891554"}}
{"comment": {"body": "This PR should be merged to the release branch not develop. Every few days we will do a merge back from relaese to develop.\n\nWe want all the code for the release to be on release branch and without manual merges  with develop later\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/170/_/diff#comment-85903804"}}
{"comment": {"body": "But is it for the release branch?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/170/_/diff#comment-85923013"}}
{"comment": {"body": "After discussing it, we agreed that it needs to be in release for manual testing purposes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/170/_/diff#comment-85987406"}}
{"title": "Hotfix/BIS-1221 manual tests support", "number": 171, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171", "body": "Change infra for manual oven tests: QT_MANUAL_001 & 002 (fix hydra and more informative test agent)\nQTD_MANUAL_001 can be called with pytest --QT_MANUAL_001. likewise for 002\nManual tests dont run automatically"}
{"comment": {"body": "Shouldnt we have also training\\_failed?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86024583"}}
{"comment": {"body": "We have `EVENT_TYPE_MODEL_PROGRESS` with field `result` to tell us that", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86025495"}}
{"comment": {"body": "Got it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86027034"}}
{"comment": {"body": "small comment - branch should start with \u201cfeature/\u201d not \u201chotfix/\u201d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86028354"}}
{"comment": {"body": "What about: [https://jira.levltech.com:8090/display/BII/CMP\\+-\\+Configuration\\+Management\\+Plan#CMP-ConfigurationManagementPlan-BranchingManagementStrategy](https://jira.levltech.com:8090/display/BII/CMP+-+Configuration+Management+Plan#CMP-ConfigurationManagementPlan-BranchingManagementStrategy)?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86029774"}}
{"comment": {"body": "It says to only use hotfix branches for the production branch \\(aka master\\), not release branches", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86032873"}}
{"comment": {"body": "Right. My bad", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86033392"}}
{"comment": {"body": "This makes me happy to see a reference to the document :thumbsup: :smiley: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86034064"}}
{"comment": {"body": "Approval, anyone?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86267942"}}
{"comment": {"body": "Good work!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/171/_/diff#comment-86277212"}}
{"title": "Hotfix/BIS-1214 falling transient is incorrect", "number": 172, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/172", "body": "remove python-c transient extraction comparisson\ncalculate data and noise stats from small packet areas\ntransient extraction unit tests fix\nnegative weight param update\nupdate transient tests files\non the road to 100 code coverage\nmore very important changes to reach 100% branch coverage\nprevious important changes messed with misra. take 2\ntake 3\ntake 4"}
{"comment": {"body": "Branch type should not be \u201cHotfix/\u201d, it should be \u201cFeature/\u201d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/172/_/diff#comment-86032351"}}
{"comment": {"body": "Fixing bugs on the release version should actually be hotfix/\\* by what we have defined.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/172/_/diff#comment-86039186"}}
{"comment": {"body": "There\u2019s another discussion here: [https://bitbucket.org/levl/bosch\\_integration/pull-requests/171/hotfix-bis-1221-manual-tests-support/diff#comment-86034064](https://bitbucket.org/levl/bosch_integration/pull-requests/171/hotfix-bis-1221-manual-tests-support/diff#comment-86034064)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/172/_/diff#comment-86061743"}}
{"comment": {"body": "Ok, Mich/Igal/Grisha should it be hotfix or Feature? Is it important?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/172/_/diff#comment-86128994"}}
{"comment": {"body": "You can leave it hotfix for this commit, but please use 'Feature/' in the future", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/172/_/diff#comment-86502713"}}
{"comment": {"body": "We should go over the tests before releasing the version and make sure they are according to QTD and ITD - not necessary fixing the tests very likely we need to fix documents in some places, but it\u2019s important that the document and the test that implements it will do the same.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/172/_/diff#comment-86502732"}}
{"title": "Feature/create dockerfile for jenkins slave", "number": 173, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/173", "body": "SEE THE README FILE BEFORE REVIEWING TO BETTER UNDERSTAND WHATS GOING ON\nA joint effort by @gregory-levl @guyklevl and @omer_levltech -\nTwo Dockerfiles, one for hardware-bound testing and one for generic building and testing.\nThe purpose of the Dockerfiles is\n\nTo allow us to easily deploy slaves\nMake our building process and dependencies be reproducible\nAct as documentation for our build process & requirements\nHave it all be tracked by Git\n\nSome of the tools required by the Dockerfile are downloaded from S3, well have to move it to Git LFS eventually, so its too tracked by Git"}
{"comment": {"body": "what\u2019s the idea behind `*android*` branches?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/173/_/diff#comment-86121663"}}
{"comment": {"body": "rm -rf /\\*", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/173/_/diff#comment-86121697"}}
{"comment": {"body": "Any branch that has android in its name probably modifies the app code so I do want this step to run in the pipeline in this case \\(this is the Android App step\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/173/_/diff#comment-86132488"}}
{"comment": {"body": "`--no-preserve-root`  ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/173/_/diff#comment-86132899"}}
{"comment": {"body": "Just to make sure that the app builds in the docker environment? Weird trick - might be actually safer to allow all commits to build the app.\n\nDoesn\u2019t really bother me; we just need to document it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/173/_/diff#comment-86133785"}}
{"comment": {"body": "It has nothing to do with Docker, it\u2019s just something I thought would be a good idea. It shouldn\u2019t really belong to this branch.\n\nI\u2019m guessing this step was limited to develop/master/release because the Android App takes a long time to build and it\u2019s not really important to 99% of the work we do. So I thought that branches that modify it will probably have android in their name, so it would be nice if it is built in those cases\u2026\n\nI\u2019ll add a comment explaining this reasoning", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/173/_/diff#comment-86134161"}}
{"comment": {"body": "Does anyone plan to further review this or should I merge?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/173/_/diff#comment-86159137"}}
{"comment": {"body": "I am reviewing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/173/_/diff#comment-86276281"}}
{"title": "BIS-1093 preamble calculation is wrong", "number": 174, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/174", "body": "Modified preamble algorithm so that we seek only interchanging bit sequences that end at the beginning of the access code. Changed the distance where we compare phase to sample_rate-1, instead of demod_rate which equals sample_rate / 2, in accordance with the new methods used in the python code. \nIn the future, we might want to correct the phase for CFO when calculating preamble length, as this is expected to give superior results."}
{"comment": {"body": "The `LEVL_SAMPLE_RATE_MHZ_INTERNAL-1U` expression can be `#define`d to be more self-explanatory", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/174/_/diff#comment-86126327"}}
{"title": "Fixed fingerprinting_manual merge errors", "number": 175, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/175", "body": ""}
{"title": "Feature/event serializer pickle dump", "number": 176, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/176", "body": "The event deserializer main function will now dump pickles once in a while\nFeature extraction failure event will now have a ret parameter"}
{"comment": {"body": "Do we need the same fix for training/classification errors?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/176/_/diff#comment-86314885"}}
{"comment": {"body": "It already exists for training/classification", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/176/_/diff#comment-86316035"}}
{"title": "Systest and C struct mismatch fix", "number": 177, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/177", "body": "Will now raise an error if the size of the C structs is bigger than the size of the Hydra Python structs, also synced some mismatching structs"}
{"title": "Add Pyro4 packet to system tests", "number": 178, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/178", "body": ""}
{"title": "Bugfix/manual fingerprinting updates", "number": 179, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/179", "body": "Event FeatureExtractionSuccess will now have channel information\nManual fingerprinting will now use the LEVL_FEATURE_TRAIN mode of the fingerprinting library"}
{"title": "embedded integration", "number": 18, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/18", "body": "*running code optimization on preprocessing stages\n*fixing arm CMake file\nfixing embedded project workspace (forcing it to work with printing feature extraction duration in ms\n*fixing tests\nAdded android app\nAdded android app\nfix advertising to work as bosch app\nfix array not big enough in alignment_correction"}
{"comment": {"body": "I changed the name from \u2018nxp trainer\u2019 to \u2018Bosch trainer\u2019", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/18/_/diff#comment-70720521"}}
{"title": "Events pickler import constraints bugfix", "number": 180, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/180", "body": "Took pickling logic out of event_deserialiser.py and put it in events_pickler.py because of Pickle import constraints"}
{"comment": {"body": "pickle pickle!\n\n![](https://bitbucket.org/repo/x8eLRbo/images/999420885-download.jpg)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/180/_/diff#comment-86506493"}}
{"comment": {"body": "Huh?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/180/_/diff#comment-86506635"}}
{"comment": {"body": "One could trigger Ctrl\\+C during run and then the file \\(whole session\\) would be corrupt. What about catching sigint/sigterm to allow the pickler to dump the file safely?\n\nYou could use `GracefulInterruptHandler` from levl\\\\python\\\\recording\\\\inst\\_freq\\_online\\\\main.py", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/180/_/diff#comment-86506733"}}
{"comment": {"body": "Not related to this pull request \\( this code was simply moved from a different file\\). But thank you, I\u2019ll fix that in the future", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/180/_/diff#comment-86507513"}}
{"comment": {"body": "It turns out you can do pickle.Pickler to append a pickle file in a streaming manner, so this is not needed anymore, I just add every event to the file", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/180/_/diff#comment-86508342"}}
{"title": "fingerprinting.c now allows you to configure the training model for phone/board/channel individually", "number": 181, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/181", "body": " Device-Board-Channel\n\nManual fingerprinting will now use Train-Constant-Train\nTesting agent will keep using Constant-Constant-Constant"}
{"title": "Feature/BIS-1221 manual tests support part 2", "number": 182, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/182", "body": "Add remaining manual tests (003-009)\nRun manual tests with --QT_MANUAL XXX (less hard coded code)\nManual tests in directory parallel to system tests\nUpdate board agent with AGC/RSSI filtering\nForce embedded to stop on important events, according to parameters"}
{"comment": {"body": "Shouldn\u2019t this be in fingerprinting\\_init?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/182/_/diff#comment-86508869"}}
{"comment": {"body": "I appreciate you compiling projects other than the ones you work on! ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/182/_/diff#comment-86508882"}}
{"comment": {"body": "Can you actually do this test with 750 RSSI filter? maybe you should lower it to 650\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/182/_/diff#comment-86509248"}}
{"comment": {"body": "Could be", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/182/_/diff#comment-86516215"}}
{"comment": {"body": "It\u2019s actually using AGC filter in this since I get better filtering with it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/182/_/diff#comment-86516233"}}
{"comment": {"body": "On second thought, it should be reset on any skip\\_train results, since it reloads the trained model and the previous classification result has no meaning.  \nInit is also called only once, but skip\\_train can be called during runtime.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/182/_/diff#comment-86521906"}}
{"title": "Will now always relink projects so changes in libraries will be re-linked without having the libraries as a project-reference that slows down build", "number": 183, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/183", "body": ""}
{"comment": {"body": "I\u2019m not seeing how we don\u2019t use project-reference. But we\u2019ll see as we go\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/183/_/diff#comment-86520290"}}
{"comment": {"body": "What do you mean?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/183/_/diff#comment-86557414"}}
{"comment": {"body": "Remembering to compile all 3 projects with appropriate configurations is not the solution. But we\u2019ll figure it out later", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/183/_/diff#comment-86558097"}}
{"title": "Added option to trigger build from commandline", "number": 184, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/184", "body": ""}
{"comment": {"body": "Can the script run the current git branch if not branch is specified?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/184/_/diff#comment-86523506"}}
{"comment": {"body": "Yes, added in [8d2beca](https://bitbucket.org/levl/bosch_integration/commits/8d2beca4b84302da17785bb690fb0aa42533256b)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/184/_/diff#comment-86526770"}}
{"title": "Add formatting capabilities to debug events", "number": 185, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/185", "body": ""}
{"title": "Updated libfingerprinting to require 2500 training packets in the constant model, manual fingerprinting will now use constant model", "number": 186, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/186", "body": ""}
{"comment": {"body": "there\u2019s `TRAINING_NUM_PACKETS_FULL` for that. What\u2019s the reason for modifying `TRAINING_NUM_PACKETS_CONSTANT ` to be the same?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/186/_/diff#comment-86525986"}}
{"comment": {"body": "That was the requirement. The CONSTANT model now requires 2500 training packets, not 500. Just because the two #define\u2019s have the same value doesn\u2019t mean they should be merged, they have a different meaning.\n\nAt-least that\u2019s how I understand it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/186/_/diff#comment-86526052"}}
{"title": "CapturedPacket event now has RSSI and AGC information", "number": 187, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/187", "body": ""}
{"title": "Feature/BIS-1302 add compiler alignment to interface", "number": 188, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/188", "body": "Use alignas() for interface sturctures to explicitly specify 4 bytes alignment"}
{"comment": {"body": "`alignas` is part of C11, not C99. Doesn\u2019t look like we can use it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/188/_/diff#comment-86531447"}}
{"comment": {"body": "As we discussed, Bosch required '-std=gnu11'  flag. This means we work with C11", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/188/_/diff#comment-86531727"}}
{"title": "Will now append to pickle file instead of recreating it", "number": 189, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/189", "body": ""}
{"title": "integration", "number": 19, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/19", "body": ""}
{"title": "Will now report that training has started when starting training via button press", "number": 190, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/190", "body": ""}
{"title": "Updated Hydra to 87315f5a409cbe74171a42a69c49630db37a1460", "number": 191, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/191", "body": "Hydra now supports converting structs to dict, useful for pandas"}
{"comment": {"body": "Please approve", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/191/_/diff#comment-86557967"}}
{"title": "present_data update", "number": 192, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/192", "body": "Added confidence intervals ( = decision boundaries) for all tests\nAdded graphics in case a test result is invalid\nChanged internal functions name to Present_Data\nFound and fixed CFO confidence interval graphics bug\nFixed paths"}
{"comment": {"body": "There\u2019s also dependency on full PC paths:\n\n    os.chdir(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\bosch_integration\")\n    \n    sys.path.append(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\bosch_integration\")\n\nPlease change it relative paths", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/192/_/diff#comment-86558176"}}
{"title": "Android app will no longer heat up the CPU on purpose during training", "number": 193, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/193", "body": ""}
{"title": "Update all *_NUM_PACKETS_THRESHOLD_CONSTANT from 200 to 1500 because CONSTANT now requires 2500 packets", "number": 194, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/194", "body": ""}
{"comment": {"body": "Sorry, I probably misunderstood your question.\n\nFalling transient and preamble threshold should be the same as with not constant model. This is not 1500 - this is 200 right?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/194/_/diff#comment-86558166"}}
{"comment": {"body": "It seems I misunderstood the code. You\u2019re right", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/194/_/diff#comment-86558717"}}
{"title": "Feature/FIN-603 static analysis breaks for PR with 1 commit", "number": 195, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/195", "body": "Jenkinsfile generates build name for Klocwork based on whether it's a PR or not"}
{"title": "Partially revert \"Update all *_NUM_PACKETS_THRESHOLD_CONSTANT from 200 to 1500 because CONSTANT now requires 2500 packets (pull request #194)\"", "number": 196, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/196", "body": "This reverts pull request #194, leaving some constants unchanged.\n\nUpdate all *_NUM_PACKETS_THRESHOLD_CONSTANT from 200 to 1500 because CONSTANT now requires 2500 packets"}
{"title": "The huge difference between release and develop is slowing BitBucket and Jenkins down", "number": 197, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/197", "body": ""}
{"title": "Make the extraction of features to be atomic", "number": 198, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/198", "body": "What happens today is that in failure scenarios, we partially write data to the out feature struct (and not encrypt btw) during the processing of the data.\nAfter the change, the data is written to the features object only on a successful run of feature extraction."}
{"comment": {"body": "In order to save memory and improve performance, maybe it would be better to just `memset` the struct before returning if the return value is an error.\n\nThis way we don\u2019t have to hold an extra local `Levl_Feature_internal_st` and we don't have to perform the copy at the end of the feature extraction. \n\nThe `memset` overhead will then only exist in exceptional error circumstances", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/198/_/diff#comment-86560622"}}
{"comment": {"body": "I would prefer actually not to make any changes to the output struct at all if we are not returning any values there. I think this is a more consistent behavior of the api.\n\nI don't think there is any performance hit right now as the struct is very small `sizeof(Levl_Feature_internal_st) < 100`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/198/_/diff#comment-86561001"}}
{"comment": {"body": "Fair enough", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/198/_/diff#comment-86561363"}}
{"title": "Changed CFO classification threshold to 25 packets instead of 31 to lower classification time for slow-advertising phones", "number": 199, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/199", "body": ""}
{"title": "*adding skeleton for feature extraction module", "number": 2, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/2", "body": ""}
{"title": "Feature/FIN-324 Reduce embedded code runtime", "number": 20, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/20", "body": "*optimizing CFO extraction convolution window\n*minor optimization and warning fix"}
{"title": "Feature/BIS-1430 qt manual 008 shouldnt stop whe", "number": 200, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/200", "body": "Don't fail QT_MANUAL_008 on first mismatch - just log it and move on\nSimplify 008 to just classify all the time until CTRL+C"}
{"title": "Hotfix/ITD", "number": 201, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/201", "body": "fix ITD tests\nfix jenkins error"}
{"comment": {"body": "is this the final version of the code?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/201/_/diff#comment-86568475"}}
{"comment": {"body": "There\u2019s a lot of code commented out. If a piece of code\u2019s not relevant anymore, it\u2019s better to delete it. If it\u2019s still important for some reason, it should be explained why it\u2019s commented out by another comment / the code should be made available via other means \\(if statements, configuration variables, etc\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/201/_/diff#comment-86570407"}}
{"comment": {"body": "Ok", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/201/_/diff#comment-86583047"}}
{"comment": {"body": "There is an issue with  `test_ITD_003_cfo_range_validation` , \n\nthat Igal is investigating in the c code. After his fix, I should fix this function.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/201/_/diff#comment-86583130"}}
{"title": "Robust BLE packet filtering", "number": 202, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/202", "body": "We use the manufacturer data from the BLE packet to parse the Bosch headers.\nIn real life scenarios, we might have additional AD sections (for example AD flags section which is mandatory in all connectable ADV packets).\nIn this change, we iterate over all AD sections in the packet and parse only the manufacturer data AD."}
{"comment": {"body": "I\u2019m not sure about this but I think this is the first instance of us making the library Bosch specific.\n\nMaybe it would be a better idea to have the library be provided with some optional predicate that receives the demodulated data as input and outputs a valid/invalid answer\n\nThis way this logic turns into Bosch\u2019s responsibility \\(we\u2019ll move the code in this pull request outside the library\\). Any changes to the advertisement structure can be fixed by Bosch without going through us first \\(Maybe they will want to add more manufacturer ID\u2019s? Maybe change the manufacturer specific data itself?\\). We shouldn\u2019t care about those details in our library.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/202/_/diff#comment-86570199"}}
{"comment": {"body": "I completely agree that it shouldn\u2019t be a part of our library and it should be under Bosch\u2019s responsibility.\n\nAs we don\u2019t have any other packet filtering options \\(due to the lack of SDK\\) and we cannot change our interface in the last minute, we don\u2019t have any other option. I would suggest to add it as part of the system configuration in V2.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/202/_/diff#comment-86571044"}}
{"comment": {"body": "When \u2018ret == true\u2019 the break should be inside the if `BLE_GAP_DATA_TYPE_MANUFACTURER_SPEC` just for the beauty of the code. functionally it\u2019s the same right now.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/202/_/diff#comment-86572195"}}
{"comment": {"body": "By MISRA rules you can have only up to one break per loop, so that\u2019s the reason its together", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/202/_/diff#comment-86572301"}}
{"comment": {"body": "Why it changed from 14 to 12?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/202/_/diff#comment-86572325"}}
{"comment": {"body": "0x12 is the actually the correct value ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/202/_/diff#comment-86572405"}}
{"comment": {"body": "Do we have a way to test that with their app?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/202/_/diff#comment-86572632"}}
{"comment": {"body": "We don\u2019t have their app.  \nBut the test vectors in the unit tests are packet taken from captured logs from their app.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/202/_/diff#comment-86572849"}}
{"title": "Update to work with test", "number": 203, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/203", "body": "Does not have to expect model after boot, might take several boots.\nLots of code duplication and bad documentation - but this is a temporary patch.\nAlso fixed a bug where if there is an invalid test result that is not the last error - the graphics get a bit messed up\nAdded markers when new classification begins\nAdded different markers for different channels"}
{"title": "Feature/BIS-1210 memory overrun in encryption", "number": 204, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/204", "body": "Tried fixing issue where jenkins does not delete the workspace.\nFixed bug in Levl_Train when using encryption.\nAdded encryption for the model structs as-well. Does add a significant amount of added latency, at least for the tests.\nImproved efficiency of encryption on Levl_Train().\nNow also encrypting the model progress.\nNow also encrypting the classification progress.\nFixed some of the tests\nFixed all tests to also work with encryption.\nFixed misra issues.\nNow jenkins will run code in both encryption and no encryption configurations.\nRemoved redundant way of posting the code coverage results\nFixed include\nAdded function to serialize board instance so it could be saved as a pickle.\nAdded serialization of failed tests after test session is over.\nSome fixes for the HW tests.\nAdded encryption to the embedded project"}
{"comment": {"body": "not unittests, per se", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/204/_/diff#comment-86569938"}}
{"comment": {"body": "There are a few issues still:\n\n1. It break 100% code coverage\n2. Conflict in merge to the release branch\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/204/_/diff#comment-86571109"}}
{"title": "Feature/BIS-1452 manual tests workspace not gene", "number": 205, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/205", "body": "Fix paths Better reboot\nAll stages sould update the filter after agent reset\nFix testing agent not learning the channel\nQTM 001 and 002 also save logs"}
{"comment": {"body": "`as gih` can omitted if not referenced inside the `with` scope", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/205/_/diff#comment-86572658"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/205/_/diff#comment-86583812"}}
{"title": "Testing agent will now report a model event before starting classification, after loading a model from flash.", "number": 206, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/206", "body": ""}
{"comment": {"body": "Please approve", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/206/_/diff#comment-86590272"}}
{"comment": {"body": "Builds not reported as successful", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/206/_/diff#comment-86592347"}}
{"comment": {"body": "Ugh. It was successful when I wrote the comment. Will investigate", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/206/_/diff#comment-86594950"}}
{"title": "Extracting receiver temperature from packet data", "number": 207, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/207", "body": ""}
{"title": "Remove library symbols strings, except symbols exported in LEVL_fingerprinting.h", "number": 208, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/208", "body": ""}
{"comment": {"body": "How about words like \u201cenc\u201d, \u201cdec\u201d, \u201cobf\\*\u201d \\(obfsucation\\) ?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/208/_/diff#comment-86587459"}}
{"comment": {"body": "We still have the names of all the object files:\n\n![](https://bitbucket.org/repo/x8eLRbo/images/2826772727-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/208/_/diff#comment-86587493"}}
{"comment": {"body": "The object files are not archived into the .a file", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/208/_/diff#comment-86587630"}}
{"comment": {"body": "What do you mean? The image shows the .a file opened with 7z \\(.a files are are like .tar files for .o files\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/208/_/diff#comment-86587682"}}
{"comment": {"body": "Build the cmake in lib\\_build/arm", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/208/_/diff#comment-86587836"}}
{"comment": {"body": "I read the cmakelists.txt more carefully now, so I understand now", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/208/_/diff#comment-86588110"}}
{"title": "Fix compilation with encryption", "number": 209, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/209", "body": "Fixed issue with the way the project was compiled with/without encryption\nEnabled skipped tests for corrupted features.\nRunning features corrupted tests will only occur when using encryption."}
{"title": "CFO feature training and classification", "number": 21, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/21", "body": ""}
{"title": "Feature/BIS-1362 fix IDT QTD tests", "number": 210, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/210", "body": "add error_code check for feature extraction tests\n\nI added the error code defines and return value. \nFor some tests I also added the verification - for others I will ask Dima and Nuriel to add the verification to their tests."}
{"comment": {"body": "Thanks for inserting the error codes. Could you also replace the magic number in test\\_training and test\\_classification?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/210/_/diff#comment-86592529"}}
{"comment": {"body": "Don\u2019t we check for `LEVL_FEATURE_ERR_OUT_OF_BLE_SPECS ` anywhere?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/210/_/diff#comment-86593090"}}
{"comment": {"body": "We do in `ITD_062_fixture`.\n\nI just added the define - before it was hardcoded -4.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/210/_/diff#comment-86649997"}}
{"comment": {"body": "Will do that in next commit", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/210/_/diff#comment-86650041"}}
{"title": "Feature/BIS-1347 compile and burn image on 9x", "number": 211, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/211", "body": "Updated eclipse to cmake converter to support 9x project\nChanged name of libdialogboilerplate to ble_base_library\nAdded cmake compilation for 9x as well.\nChange jenkins to use the 9x SDK to burn the system testing image\nFixed paths for 9x\nUpdated burn script to work with 9x\nFixed issue with the way the project was compiled with/without encryption\nAdded execute permissions to the burning scripts"}
{"comment": {"body": "It still has an issue with burning the image from the docker container, but I want to include this in release so we would actually build for 9x.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/211/_/diff#comment-86593752"}}
{"comment": {"body": "We shouldn\u2019t compile the ARM Library with Eclipse .cproject, we should just re-use the compiled, stripped final ARM library that is generated beforehand by the ARM library build scripts. This way the tests run on the actual official .a file instead of some odd Eclipse configuration", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/211/_/diff#comment-86593961"}}
{"comment": {"body": "Added in [ac11f6f](https://bitbucket.org/levl/bosch_integration/commits/ac11f6fe8838aaad2b84ca174ee89fc1fa38d4af?at=release/V1_0)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/211/_/diff#comment-86598671"}}
{"comment": {"body": "We should build the Release version for testing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/211/_/diff#comment-86640869"}}
{"comment": {"body": "remove the -I- if we\u2019re not using it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/211/_/diff#comment-86640935"}}
{"title": "BIS-1407 missing reset between classification", "number": 212, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/212", "body": "Removed fixed phone temprature from the testing agent\nFixed Classification and packet capture tests."}
{"title": "Feature/fix skip fingerprinting encryption mismatch", "number": 213, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/213", "body": "fingerprinting_skip_train will now receive a decrypted model and encrypt it itself\n(cherry picked from commit 0fa7629a290e2cf4c8959dff1a1bfb530c97427a)"}
{"title": "fingerprinting_skip_train will now receive a decrypted model and encrypt it itself", "number": 214, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/214", "body": "(cherry picked from commit 0fa7629a290e2cf4c8959dff1a1bfb530c97427a)"}
{"comment": {"body": "Please approve \\(this is urgent\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/214/_/diff#comment-86614864"}}
{"title": "Feature/BIS-1656 model statistic q31 overflow", "number": 215, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/215", "body": "move from statistic q31 to statistic in normal models\nstatistic_q31 to statistic in progress\nWill now link libmath (-lm) in both Release and Debug configurations in the 8x Eclipse project"}
{"comment": {"body": "You may want to delete the q31 statistics code altogether and not just run away from it, considering it\u2019s buggy", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/215/_/diff#comment-86613965"}}
{"title": "Will now report a timeout event when the idle timer is triggered", "number": 216, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/216", "body": ""}
{"title": "Feature/BIS-1335 create a bosch fingerprinting demo", "number": 217, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217", "body": "The Bosch UART protocol will now not be aware of the different types of incoming commands\n\nThe testing agent abuses the Bosch protocol in order to be commanded, so it adds more commands that are not needed in the Bosch protocol, so the protocol simply calls the callback with the command character itself and doesn't verify if the command is a valid command or not.\n\n\n\nFlash model now supports saving extra data alongside the model\n\nFixed manual and testing_agent to support new boilerplate\nInitial version of fingerprinting_bosch\n\nBosch demo now works\n\nFixed bosch_uart_protocol bugs\nbosch demo timeout lowered from 3s to 1s\nWill now respond to train command\nButton works as expected\nprotocol.py demo protocol baudrate fixed"}
{"comment": {"body": "please don't use char", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86640685"}}
{"comment": {"body": "We already agreed about this during the China demo, it's non-library code and we're comparing this value to \u2018c' char literals which are signed. This pull request didn't introduce the char usage, it's just code to fit the already existing code which uses char. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86642943"}}
{"comment": {"body": "Weird decision\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86643189"}}
{"comment": {"body": "Omer, is there a reason not to use int8\\_t?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86644752"}}
{"comment": {"body": "No reason, it\u2019s just not relevant to this pull request, the surrounding code was already using char. I\u2019ll create another pull request fixing this if needed.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86644955"}}
{"comment": {"body": "OK", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86649135"}}
{"comment": {"body": "Dont forget to fix RSSI to 200 in next commits\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86649285"}}
{"comment": {"body": "All looks OK besides the removal of accessing our internal structures", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86649565"}}
{"comment": {"body": "This will be taken care of in [BIS-1668](https://jira.levltech.com:8443/jira/browse/BIS-1668)  ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86650063"}}
{"comment": {"body": "I can\u2019t understand why you added this tid if all places its being used in it is passed as \u201cfaketid\u201d.\n\nwhat is it even used for?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86721535"}}
{"comment": {"body": "Because training is for a specific TID. RIght now there\u2019s no TID filtering so it\u2019s all a bit fake but eventually when we have TID filtering it makes sense for this function to receive one as a parameter", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/217/_/diff#comment-86721653"}}
{"title": "Decrease transient search interval to 64 samples after end of packet", "number": 218, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/218", "body": ""}
{"comment": {"body": "Tests are failing with this PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/218/_/diff#comment-86665455"}}
{"title": "Improved board instance to work with new events", "number": 219, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/219", "body": ""}
{"title": "Feature/FIN-326 Integrate preamble feature", "number": 22, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/22", "body": "*starting to add preamble size API\nadding preamble length model training and classification based on gaussian model modules added small test to classification *fixed compilation issues from previous commit\nchanging preamble length model to be running during training integrated preamble length model to be in classification stage adding Q31 API for normal model adding test for training"}
{"title": "Fixed bug which caused training to fail if used for 10 seconds after training is completed", "number": 220, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/220", "body": ""}
{"title": "BIS-1662 combine tests that require two", "number": 221, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/221", "body": "Removed fixed phone temprature from the testing agent\nFixed Classification and packet capture tests.\nFixed bug in training from flash.\nCreated a fixture for classification tests. Updated QT_FEATURE_010 + QT_FEATURE_011.\nFix for board agent serialization.\nCFO test will now check std for each channel."}
{"comment": {"body": "Please fix the conflicts", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/221/_/diff#comment-86691267"}}
{"comment": {"body": "On it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/221/_/diff#comment-86697843"}}
{"comment": {"body": "We need make -j for the compilation trick", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/221/_/diff#comment-86746698"}}
{"title": "Falling transient discard false detection of transient start", "number": 222, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/222", "body": ""}
{"title": "Fix board temperature and number packet for training constants", "number": 223, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/223", "body": "Following EIS when board temperature is also multipile of 100.\nBoard slope value for 9x boards\nDecrease number of packets for training"}
{"comment": {"body": "This should also be updated in the dialogboilerplate project as it is now using a constant temperature of 25", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/223/_/diff#comment-86720767"}}
{"comment": {"body": "Done in [78173af](https://bitbucket.org/levl/bosch_integration/commits/78173afe4257d9c5fcfe4120608fb949b9917a7a)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/223/_/diff#comment-86724193"}}
{"title": "Compilation with make -j", "number": 224, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/224", "body": "Compilation with make -j"}
{"comment": {"body": "Now when i think about it im pretty sure we could just tell make to compile the specific target.\n\nI will try and update.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/224/_/diff#comment-86758255"}}
{"comment": {"body": "Yep, it works.\n\nAdded in [04d2067](https://bitbucket.org/levl/bosch_integration/commits/04d20676649fb0c1133f5fd0b8b86637c1e1d7f5?at=release/V1_0)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/224/_/diff#comment-86758671"}}
{"title": "Feature/BIS-1668 prepare the bosch demo source c", "number": 225, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225", "body": "Prepared Bosch demo for delivery (also changed libfingerprinting)\n\nLevl_CapturedPacket_st now as in EIS (reordering of fields, RSSI changed to 16-bit), updated testing structs accordingly\nLevl_FeatureExtract now receives const Levl_CapturedPacket_t\nLibrary now performs both rotation and downsampling (both at the same loop), packet_capture no longer supports buffer rotation\nPassing NULL to Levl_*Init functions will now cause usagee of default values\nMoved code not relevant to Bosch in boilerplate to a \"private\" folder that will be removed with a script in the future during deployment of demo code\nb64aes_dump now simply b64_dump because library is responsible for encrypting the model\nAdded a lot of documentation to boilerplate code to aid Bosch in understanding the code\nble_filter_beacon_bosch now returns simple yes/no answer (no longer supports training packets), supports const data\nfingerprinting.c and fingerprinting.h now private, fingerprinting_wrapper.c and fingerprinting_wrapper.h simpler that are a more Bosch-friendly version of them have been added\nflash_model now stores a Levl_Model_St and not Levl_Model_Internal_st\nRemoved led.h and led.c and all usages of it, it's not relevant to 9x, we have rgb.h and rgb.c instead\nFlash model will now use an IV for the CRC\nfingerprinting_bosch ble_iq_advertiser_task.c now much more Bosch friendly\n\n\n\nImproved Bosch demo LED indication\n\nBosch demo button will now force the board into training mode with dummy TID to allow using the demo without the UART protocol\nRemoved obsolete release configuration from Bosch demo\nFixed MISRA\nFixed more MISRA\nfingerprinting_bosch will now look for the library and include directories using environment variables instead of Eclipse workspace macros\nFixed some typos\ndialogboilerplate will now include LEVL_fingerprinting.h from an environment variable if the libfingerprinting project doesn't exist in the workspace\nLevl_*GetCfg functions now DLL_PUBLIC (still internal though), ctypes Levl_CapturedPacket_t now has raw_IQ_start_idx with the correct type\nMISRA fun (Klocwork is wrong)\nRefactor Levl_*Init because Klocwork is wrong\nAdded test for LEVL_*Init functions with NULL ptr for code coverage\nAdded script to automatically generate a drop (zip containing the example demo) for Bosch\nFixed Jenkinsfile typo\nUpdated regression datasets to match new EIS\nImproved Drop script, will now take everything required to build the project"}
{"comment": {"body": "Where did the \\*2 go?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86759767"}}
{"comment": {"body": "Line 47", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86760105"}}
{"comment": {"body": "Moving this here will break the hw tests because the packet Capture event begins printing the packet from the timestamp field.\n\nplease update.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86760242"}}
{"comment": {"body": "That\u2019s what I get for writing horrible code", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86760332"}}
{"comment": {"body": "I think there are some new files here without copyright\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86761493"}}
{"comment": {"body": "We can\u2019t expose internal interfaces. And it\u2019s not on the EIS", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86766372"}}
{"comment": {"body": "you might also reuse the libfingerprinting/tests/code\\_quality\\_tests/library\\_string\\_dictionary.txt", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86766449"}}
{"comment": {"body": "They're in `LEVL_fingerprinting_internal.h`\n\nIt is needed because some of the tests call those functions via the DLL. Pretty sure DLL\\_PUBLIC only affects dynamic linkage, not static-linkage.\n\nWe\u2019ll just have to make sure it doesn\u2019t make it to the stripped archive", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86772775"}}
{"comment": {"body": "Was looking all over for it and couldn\u2019t find it. Thanks", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86773787"}}
{"comment": {"body": "Which ones? And where can I get a copy \\(no pun intended\\) of the copyright text?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86773862"}}
{"comment": {"body": "I guess all of the files in this PR.\n\nYou can use this template:\n\n    /**\n    \n     * Copyright (C) 2018, LEVL Technologies\n    \n     */\n\nAlthough the date might be 2018-2019 since we\u2019re delivering in Jan 1st, 2019.\n\nTemplate taken from libfingerprinting/include/LEVL\\_fingerprinting.h", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86774101"}}
{"comment": {"body": "You\u2019re right. The getters are not in the EIS.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86774170"}}
{"comment": {"body": "Thanks, fixed", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/225/_/diff#comment-86780128"}}
{"title": "Cover the cases where the current timestamp of a timer is smaller than the current timestamp", "number": 226, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/226", "body": ""}
{"comment": {"body": "What\u2019s the reason behind this? Overflow was handled but now the system time can\u2019t overflow", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/226/_/diff#comment-86776779"}}
{"comment": {"body": "We saw in our tests that sometimes the hardware \u201cblips\u201d with the timestamps and we get timestamp that are a bit older. In this case we don\u2019t want to crash the training process.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/226/_/diff#comment-86776852"}}
{"comment": {"body": "Maybe we should check for valid `timediff` within some threshold instead. I don't think that stopping overflow is a good tradeoff.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/226/_/diff#comment-86776861"}}
{"comment": {"body": "The time is a 64 bits timer, we don\u2019t have overflow..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/226/_/diff#comment-86779536"}}
{"title": "Reading the HW temperature sensor", "number": 227, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/227", "body": ""}
{"comment": {"body": "Generally we\u2019d want higher than LOWEST priority since idle task is with LOWEST priority.\n\nIt\u2019d only affect us if ever we want low power modes, but whatever\u2026.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/227/_/diff#comment-86781166"}}
{"title": "Added return value verification", "number": 228, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/228", "body": ""}
{"title": "Added return value verification", "number": 229, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/229", "body": ""}
{"comment": {"body": "Both jenkins runs for this PR passed. For some reason also a run for the previous PR is shown as not passing\u2026 it is irrelevant to this PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/229/_/diff#comment-86780923"}}
{"title": "integration", "number": 23, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/23", "body": ""}
{"title": "check that permissions in Bitbucket work", "number": 230, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/230", "body": "check that permissions in Bitbucket work"}
{"title": "Feature/feature extraction tests use error codes", "number": 231, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/231", "body": "run all tests with all the features\ncheck error code and preamble_length_valid in test_preamble_exact\ncheck error_code and valid_res for all transient,preamble,cfo tests\nadd test that for bad rssi all error codes are -9\nchange -9 to the error_code"}
{"comment": {"body": "This test is testing the CFO. What\u2019s the reason for enabling the other features as well?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/231/_/diff#comment-86781821"}}
{"comment": {"body": "Discussed it with Igal. He claims that it\u2019s better to run all the tests with as close as possible settings to the real system ones. So we probably prefer to run all features extraction even when we are interested only in one feature. WDUT?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/231/_/diff#comment-86781836"}}
{"title": "Feature/hw test preamble transient fix", "number": 232, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/232", "body": "force the feature_extraction test to run with a specific phone model (s8)\nchange default params of HW tests for preamble and transient"}
{"title": "Feature/better obfuscation", "number": 233, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/233", "body": "Revert \"Modified libfingerprinting ununsed symbols to hackishly force obfuscation script to remove some symbols\"\nThis reverts commit 38c7a45060eb81dc7406f62127a1e8e59bb2d9e9.\nConflicts: libfingerprinting/lib_build/arm/objfuscate.sh\n\n\nObfuscation now perfect (I think)\n\nobjfuscate.sh supports paths with spaces"}
{"title": "Changed RSSI filtering to 200, encryption code copied to boilerplate for pre-release manual testing to allow linkage with stripped library", "number": 234, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/234", "body": ""}
{"title": "Feature/manual fingerprinting fixes", "number": 235, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/235", "body": "Fixed loading from flash bug in manual fingerprinting\nEnabled encryption in Jenkins ARM build"}
{"title": "Feature/BIS-1731 capture iq project broken", "number": 236, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/236", "body": "Fix capture IQ project\nFix rssi not being used at all Fix printing of board temperature Fix packet len being unknown"}
{"title": "Fix classification paths", "number": 237, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/237", "body": ""}
{"title": "Feature/fix iq", "number": 238, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/238", "body": "Fixed issues with IQ acquisition\nFixed packets that require IQ"}
{"title": "present data update", "number": 239, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/239", "body": ""}
{"title": "Cfo model reduce memory", "number": 24, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/24", "body": "disable board temperature slop learning option to save memory\nstore 1/cfo_var instead of var to save the w vector"}
{"title": "Feature/final transient fix", "number": 240, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/240", "body": "Final transient fix\nMISRA fixes\ndecrease window for stats size\ntest update\ntest update\nMISRA fixes\nMISRA fix\ncode coverage fix\nfix the fix"}
{"title": "Prepare .h file for release", "number": 241, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/241", "body": ""}
{"title": "Feature/fix some hwtests", "number": 242, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/242", "body": "Added prints to system tests and fixed some tests.\nUnhide encryption tests."}
{"title": "Release/V1 0", "number": 243, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/243", "body": "Tried fixing issue where jenkins does not delete the workspace.\nFixed bug in Levl_Train when using encryption.\nAdded encryption for the model structs as-well. Does add a significant amount of added latency, at least for the tests.\nImproved efficiency of encryption on Levl_Train().\nNow also encrypting the model progress.\nNow also encrypting the classification progress.\nFixed some of the tests\nFixed all tests to also work with encryption.\nFixed misra issues.\n\nThe Bosch UART protocol will now not be aware of the different types of incoming commands\nThe testing agent abuses the Bosch protocol in order to be commanded, so it adds more commands that are not needed in the Bosch protocol, so the protocol simply calls the callback with the command character itself and doesn't verify if the command is a valid command or not.\n\n\nFlash model now supports saving extra data alongside the model\n\nFixed manual and testing_agent to support new boilerplate\nInitial version of fingerprinting_bosch\nNow jenkins will run code in both encryption and no encryption configurations.\nRemoved redundant way of posting the code coverage results\nFixed include\nAdded function to serialize board instance so it could be saved as a pickle.\nAdded serialization of failed tests after test session is over.\nSome fixes for the HW tests.\nAdded encryption to the embedded project\npresent_data update\nMake feature extracte not write to the output struct in any fail scenario\nDon't fail QT_MANUAL_008 on first mismatch - just log it and move on\nSimplify 008 to just classify all the time until CTRL+C\nChanged CFO classification threshold to 21 packets instead of 31 to lower classification time for slow-advertising phones\nCFO 21 threshold was a bit too low, broke regression tests, testing 25\nupdate present_data\nfix\nParse all AD structures of the BLE beacons\nFixed indices of positions in packets\nMISRA fixes\nmore MISRA\nSimplify code\nMISRA fixes. Maybe work now?\nfix ITD tests\nFix paths Better reboot\nfix jenkins error\nAdd test cases for code coverage\nFixed typs\nAll stages sould update the filter after agent reset\nFix testing agent not learning the channel\npost-igal update\nQTM 001 and 002 also save logs\nFix QTM 008\nFix dump names\nTesting agent will now report a model event before starting classification after loading a model from flash.\nFixed bug that caused training to fail after calling it multiple times with the testing agent\nFixed tests and coverage\nFixed coverage.\nRemoved unused code\nIncorporation of extracting receiver temperature\nFix MISRA signed/unsigned issues\nKeep only exported symbols in stripped binary\nMultipile jobs failure on library build\nMultipile jobs failure on library build\nRemove sections flags from compilation flags\nFix flags location in Cmake library\nFix typo\nfix pull request comments\nQT Manual also save logs for 003, 004 and 009\nFixed issue with the way the project was compiled with/without encryption\nupdate present_data - new classification markers\nEnable shound_not_contain_strings test\nExtended word dictionary for string search\nEnabled skipped tests for corrupted features.\nMake string search code to be case agnostic\nRunning features corrupted tests will only occur when using encryption.\nITD_test.py\nadd error_code check\nreturn conf to original value\nupdate present_data - new classification markers\nupdate present_data - new classification markers\nupdate present_data - new classification markers\nupdate present_data - new classification markers\nRemoved fixed phone temprature from the testing agent\nFixed Classification and packet capture tests.\nFixed bug in training from flash.\nmove from statistic q31 to statistic in normal models\nstatistic_q31 to statistic in progress\nfingerprinting_skip_train will now receive a decrypted model and encrypt it itself\nWill now link libmath (-lm) in both Release and Debug configurations in the 8x Eclipse project\n\nAccidental direct commit to Release - Revert \"fingerprinting_skip_train will now receive a decrypted model and encrypt it itself\"\nThis reverts commit 0fa7629a290e2cf4c8959dff1a1bfb530c97427a.\n\n\nfingerprinting_skip_train will now receive a decrypted model and encrypt it itself\n(cherry picked from commit 0fa7629a290e2cf4c8959dff1a1bfb530c97427a)\n\n\nWill now report a timeout event when the idle timer is triggered\n\nupdate itd cfo test\n\nBosch demo now works\n\nFixed bosch_uart_protocol bugs\nbosch demo timeout lowered from 3s to 1s\nWill now respond to train command\nButton works as expected\nprotocol.py demo protocol baudrate fixed\n\n\n\nBosch fingerinting demo - Removed fingerprinting_training_callback and fingerprinting_features_callback\n\nFixed sizeof bug\nCreated a fixture for classification tests. Updated QT_FEATURE_010 + QT_FEATURE_011.\nDecrease transient search interval to 64 samples\nFix for board agent serialization.\nCFO test will now check std for each channel.\nImproved board instance to work with new events\nFixed bug which caused training to fail if used for 10 seconds after training is completed\n\nPrepared Bosch demo for delivery (also changed libfingerprinting)\n\nLevl_CapturedPacket_st now as in EIS (reordering of fields, RSSI changed to 16-bit), updated testing structs accordingly\nLevl_FeatureExtract now receives const Levl_CapturedPacket_t\nLibrary now performs both rotation and downsampling (both at the same loop), packet_capture no longer supports buffer rotation\nPassing NULL to Levl_*Init functions will now cause usagee of default values\nMoved code not relevant to Bosch in boilerplate to a \"private\" folder that will be removed with a script in the future during deployment of demo code\nb64aes_dump now simply b64_dump because library is responsible for encrypting the model\nAdded a lot of documentation to boilerplate code to aid Bosch in understanding the code\nble_filter_beacon_bosch now returns simple yes/no answer (no longer supports training packets), supports const data\nfingerprinting.c and fingerprinting.h now private, fingerprinting_wrapper.c and fingerprinting_wrapper.h simpler that are a more Bosch-friendly version of them have been added\nflash_model now stores a Levl_Model_St and not Levl_Model_Internal_st\nRemoved led.h and led.c and all usages of it, it's not relevant to 9x, we have rgb.h and rgb.c instead\nFlash model will now use an IV for the CRC\nfingerprinting_bosch ble_iq_advertiser_task.c now much more Bosch friendly\n\n\n\nImproved Bosch demo LED indication\n\nBosch demo button will now force the board into training mode with dummy TID to allow using the demo without the UART protocol\nRemoved obsolete release configuration from Bosch demo\nFixed MISRA\nFixed more MISRA\nDifferent strategy - ignore packets where the start of transient is found too far from the endof the data\neliminate branches\nfingerprinting_bosch will now look for the library and include directories using environment variables instead of Eclipse workspace macros\nFixed some typos\nConsts change: 1. slope of borad freq offset 2. Number packet for training in conatnt mode\ndialogboilerplate will now include LEVL_fingerprinting.h from an environment variable if the libfingerprinting project doesn't exist in the workspace\nBoard tempeature should be received as mults of 100 in the EIS\nWorkaround cmkae dependency issue\nFix float const\nKlocwork exclude not needed anymore?\nFix main loop code to use board temp of x100\nLevl_*GetCfg functions now DLL_PUBLIC (still internal though), ctypes Levl_CapturedPacket_t now has raw_IQ_start_idx with the correct type\nMISRA fun (Klocwork is wrong)\nRefactor Levl_*Init because Klocwork is wrong\nAdded test for LEVL_*Init functions with NULL ptr for code coverage\nAdded script to automatically generate a drop (zip containing the example demo) for Bosch\nFixed Jenkinsfile typo\nFix borad_temperature default val\ntemporary removed transient validation so code coverage won't crash\nThis is the fix that was needed for code coverage\nOvercome MISRA\nMaybe MISRA now?\nUpdated regression datasets to match new EIS\nCompilation with make -j\nImproved Drop script, will now take everything required to build the project\nFixed make script\nFix code review comments\nfingerprinting_wrapper.c edited online with Bitbucket\nlevl_ble_observer.c edited online with Bitbucket\nCover the cases where new_time is not larger than the current timestamp\nAdded a copright notice to all Bosch demo drop files\nBoilerplate will now also look for LEVL include files in LEVL_FINGERPRINTING_INCLUDE env var\nFixed compilation error after review\nfingerprinting_bosch will now also look for LEVL include files inside LEVL_FINGERPRINTING_INCLUDE\nfix cfo range test, and add error codes testing\nRead board temperature from HW sensor\nfix pull request comments\nfix success condition in cfo_h test\nrevert .cproject\nadd test for True success error code equal 0\nrevert change of rssi filter\nImproved CMake archive stripping\nAdded return value verification\nrun all tests with all the features\ncheck error code and preamble_length_valid in test_preamble_exact\ncheck error_code and valid_res for all transient,preamble,cfo tests\nadd test that for bad rssi all error codes are -9\nchange -9 to the error_code\nFix code review comments\nObjfuscation now almost works (some symbols leftover)\nforce the feature_extraction test to run with a specific phone model (s8)\nImproved objfuscate.sh\nModified libfingerprinting ununsed symbols to hackishly force obfuscation script to remove some symbols\nRemoved test for deleted code\nud\n__read_library_stripped fixed\nJenkinsfile will now use correct stripped\nFixed issues with IQ acquisition\nRemoved GetCfg from stripped library\nchange default params of HW tests for preamble and transient\n\nRevert \"Modified libfingerprinting ununsed symbols to hackishly force obfuscation script to remove some symbols\"\nThis reverts commit 38c7a45060eb81dc7406f62127a1e8e59bb2d9e9.\nConflicts: libfingerprinting/lib_build/arm/objfuscate.sh\n\n\nAdd delay after capture\n\nObfuscation now perfect (I think)\nobjfuscate.sh supports paths with spaces\n\nRevert \"Removed test for deleted code\"\nThis reverts commit 27ba9c6ae550a5d924ec01a23855ec919440bb92.\n\n\nPy -> Python3.6\n\nChanged RSSI filtering to 200, encryption code copied to boilerplate for pre-release manual testing to allow linkage with stripped library\nud\nFix capture IQ project\nAdded checking validity of features\nFixed correct location of validation of features\nFixed loading from flash bug in manual fingerprinting\nFixed packets that require IQ\nFix rssi not being used at all Fix printing of board temperature Fix packet len being unknown\nEnabled encryption in Jenkins ARM build\nFixed now for real\nFixed code coverage of integration tests\nud\nFinal transient fix\nMISRA fixes\ndecrease window for stats size\ntest update\ntest update\nMISRA fixes\nMISRA fix\ncode coverage fix\nfix the fix\nAdded prints to system tests and fixed some tests.\nPreparing .h files for release\nRemove build for 8x\nRemove build for 8x\nUnhide encryption tests."}
{"title": "Feature/bosch main loop beautify", "number": 244, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/244", "body": "Demo fixes - beatify code and remove LED blinking\nBosch demo UART now at 921600\nRemoved personal names from Bosch drop\nRenamed Bosch demo build configuration\nAdded base64 dumps to Bosch demo"}
{"title": "Feature/bosch heating app", "number": 245, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/245", "body": "Only look at MainActivity.java and HeatService.java\n\n\nAdded bosch heater app\nNow app won't stop when in background."}
{"title": "Added IQ printing debug level to Bosch demo", "number": 246, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/246", "body": ""}
{"title": "Made Bosch demo default RSSI more sensitive (650 vs 600)", "number": 247, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/247", "body": ""}
{"title": "Release/V1 0", "number": 248, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/248", "body": "Demo fixes - beatify code and remove LED blinking\nFixed LEDs\nBosch demo UART now at 921600\nRemoved personal names from Bosch drop\nRenamed Bosch demo build configuration\nAdded base64 dumps to Bosch demo\nAdded Dima to evil_words.txtl\nRFMON less messy\nAdded explanations for Bosch demo RSSI values\nAdded bosch heater app\nNow app won't stop when in background.\nAdded IQ printing debug level to Bosch demo\nMade Bosch demo default RSSI more sensitive (650 vs 600)\nFixed some bugs in Bosch demo IQ printing"}
{"title": "Master", "number": 249, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/249", "body": "Demo fixes - beatify code and remove LED blinking\nFixed LEDs\nBosch demo UART now at 921600\nRemoved personal names from Bosch drop\nRenamed Bosch demo build configuration\nAdded base64 dumps to Bosch demo\nAdded Dima to evil_words.txtl\nRFMON less messy\nAdded explanations for Bosch demo RSSI values\nAdded bosch heater app\nNow app won't stop when in background.\nAdded IQ printing debug level to Bosch demo\nMade Bosch demo default RSSI more sensitive (650 vs 600)\nFixed some bugs in Bosch demo IQ printing"}
{"title": "Feature/FIN-326 Integrate preamble feature", "number": 25, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/25", "body": "*starting to add preamble size API\nadding preamble length model training and classification based on gaussian model modules added small test to classification *fixed compilation issues from previous commit\nchanging preamble length model to be running during training integrated preamble length model to be in classification stage adding Q31 API for normal model adding test for training\n*training progress is now classifier-independent\n*fixing first MIN_NUM_PACKETS_CLASSIFY_CFO should be MAX_VOTING_SIZE\n*adding missing flow call\nembedded project: retargerting malloc and printf's _malloc_r to use FreeRTOS's heap (using pvPortMalloc) embedded project: reducing up some FreeRTOS heap and moving that free RAM to general application (added 6000 bytes) increasing sampling window size to 3000\nfixing previous commit not compiling fixing some warnings related to floats\nfixing some warnings fixed missing flow in preamble training"}
{"comment": {"body": "Indentation", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/25/_/diff#comment-70984359"}}
{"comment": {"body": "OK", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/25/_/diff#comment-70987693"}}
{"comment": {"body": "All features have such nice names, and suddenly `levl_cfo_st`, Names should be consistent,", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/25/_/diff#comment-71008731"}}
{"comment": {"body": "Issue will be refactored later", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/25/_/diff#comment-71009054"}}
{"title": "One of the manual tests had a different format from the rest", "number": 250, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/250", "body": ""}
{"title": "Feature/BIS-1409 fix itd tests with feature extract", "number": 251, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/251", "body": "Start moving ITD tests from C++ to python. That requires a python wrapper for C++ helper function.\n\nAdd intergration test C++ helper and python wrapper for it\nBuild x64 lib with the helper lib\nFix pyfingerprinting to handle new outputs\n\nWork is not complete on transition to python, so this is an RFC"}
{"comment": {"body": "Did anything in this file change? Hard to see because it moved", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/251/_/diff#comment-86942369"}}
{"comment": {"body": "just project name, target name and src/include paths \\(added ..\\)\n\nand C standard is C11", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/251/_/diff#comment-86942694"}}
{"comment": {"body": "There's a lot of code here copied from the other file, to make maintenance easier, can you maybe try to see if you can somehow share code between them? Maybe have a Common.CMake file under `x64/` with common function definitions like `function(setup_compiler_specific target_name)`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/251/_/diff#comment-86942890"}}
{"comment": {"body": "I\u2019ll work on it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/251/_/diff#comment-86942977"}}
{"title": "Move ITD 001, 002, 005, 006, 009, 010 to python", "number": 252, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/252", "body": "Move ITD 001, 002, 005, 006, 009, 010 to python  \n\n001 fails due to lack of valid data\n\nAdd dataset fixture to not reread pickles (saves little bit of time)"}
{"title": "Feature/BIS-1791 bugfix automate manual tests", "number": 253, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/253", "body": "Reuse IMEIs of phone and store them as part of the event dump\nAdd some settling\nAdd more sleep Add missing distance filter\nFix individual filtering issue for manual tests\nFix devices used for tests"}
{"title": "Feature/static analysis label fix", "number": 254, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/254", "body": "Static anlysis will now require label analysis instead of builder\nAll Jenkinsfile stages now have an agent field"}
{"comment": {"body": "What\u2019s the actual difference? Just moving the static analysis step to `analysis` agent?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/254/_/diff#comment-87178502"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/254/_/diff#comment-87178941"}}
{"comment": {"body": "OK. Anyway, can\u2019t approve due to build not passing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/254/_/diff#comment-87181173"}}
{"comment": {"body": "You can remove this PR..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/254/_/diff#comment-87353170"}}
{"title": "present_data function latest version", "number": 255, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/255", "body": "A reference for future releases"}
{"title": "Feature/BIS-1228 retarget klocwork client", "number": 256, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/256", "body": "Move analysis to Klocwork server\nAdd quick static analysis (which takes 20s) in feature branches. Full static analysis will be made in PRs, develop, release/* and master branches\nMerge some changes from PR #254\nRefactor static analysis scripts to support quick static analysis reports"}
{"title": "Feature/BIS 1228 retarget klocwork client part 2", "number": 257, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/257", "body": "Change IP to locahost\nFull integration analysis for PRs will be done on new klocwork project"}
{"title": "Feature/BIS-1409 fix itd tests with feature extract part 3", "number": 258, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/258", "body": "Move tests ITD 017, 018, 021, 022, 025, 026, 033, 041, 042, 043, 044 and 045 to python\nCorrect assertion collection from C code (C doesn't propagate exceptions from python callbacks)\nFix warnings and compilation issue\nAdd textual progress bar when downloading dataset\nAdd misisng channel_mhz to some cfo test"}
{"comment": {"body": "Does `PyFingerprintingFeaturesDisabledNone` takes our default configuration? \\(only timing feature disabled\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/258/_/diff#comment-87599199"}}
{"comment": {"body": "no, but it\u2019s equivalent since we don\u2019t have implementation of timing feature \\(not calling the functions\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/258/_/diff#comment-87599361"}}
{"comment": {"body": "Got it.  \nCan you please add default configuration of `PyFingerprintingFeaturesDisabledNone`? so we can just write our tests without specifying configuration\u2026  \nCan be another PR.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/258/_/diff#comment-87604626"}}
{"title": "Feature/BIS-1805 Cleanup some duplication", "number": 259, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259", "body": "Merge capturedpacket, between system_tests and regression_tests_builder\nAdd assertion of compatibility between hydra and ctypes for some types \nReplace feature extraction result\nRename feature extraction field names in present_data.py"}
{"comment": {"body": "It doesn\u2019t seem like a good idea to include this here.. Why is that?\n\nShouldn\u2019t it be better to just remove this from ctypes too?\n\nor maybe just ditch ctypes.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87488581"}}
{"comment": {"body": "I would say its better to just ditch ctypes instead of adding assertions for both types.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87488667"}}
{"comment": {"body": "Can\u2019t ditch ctypes since we need to it access c functions. Much more useful for PC environment than Hydra since Hydra requires padding, word-size and endianness information.\n\nWe can generate structures from one form to another, but that was too much work ATM.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87595615"}}
{"comment": {"body": "Wanted to do it myself, thanks", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87602000"}}
{"comment": {"body": "I think it\u2019s just to simplify matters. The fields are pointless but they do no harm and removing them just adds complexity", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87602511"}}
{"comment": {"body": "This is horrible, good work! I wish we didn\u2019t need this", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87603040"}}
{"comment": {"body": "Great job, this will make our work easier", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87603636"}}
{"comment": {"body": "Since everything is little-endian and we keep all our internal structs aligned anyway \\(it\u2019s good for low memory consumption\\), it means we have no padding or alignment issues \\(just in a single API struct\\), it shouldn\u2019t be hard to use ctypes just for calling functions while using Hydra to generate the structs that are used for calling those functions.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87604173"}}
{"comment": {"body": "And I forgot pointer size.\n\nWe\u2019re not really hand picking the structures, especially the internal ones.   \nIt\u2019s bad practice to start manually handling the struct fields - too much reliance on people.  \nAn automated process would be more appropriate.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87605549"}}
{"comment": {"body": "When I come to think of it, your recursive verification function can be slightly modified to actually generate the ctypes structs instead\u2026 I\u2019ll give it a try later", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87607635"}}
{"comment": {"body": "Yes, it\u2019s a way to go.\n\nJust need to handle pointers, padding, etc\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/259/_/diff#comment-87609533"}}
{"title": "Feature/FIN-276 CI build ARM image", "number": 26, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/26", "body": "Added Jenkinsfile\nupdated script\nAdded Jenkinsfile\nAdded Jenkinsfile\n*updating cmakelist for ARM\ntest commit\nanother test commit\nanother jenkins test\nanother jenkins test"}
{"title": "Docker complete refactor, removed lib_build/arm, Jenkinsfile responsible for much less, automated Eclipse builds", "number": 260, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/260", "body": ""}
{"title": "fix jenkins not deleting @tmp workspaces at the end of run", "number": 261, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/261", "body": "deleting @tmp workspaces is an open bug at Jenkins:  . This commit will do the trick for now..."}
{"title": "Fixed FeatureExtractionSuccessEvent structure", "number": 262, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/262", "body": ""}
{"comment": {"body": "Can you change the valid fields to uint32\\_t instead of uint8\\_t so we don\u2019t need padding?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/262/_/diff#comment-88065861"}}
{"comment": {"body": "We\u2019ll need to update the EIS for that. @michlevltech will think about it", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/262/_/diff#comment-88067269"}}
{"comment": {"body": "Why? These are internal structures", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/262/_/diff#comment-88069185"}}
{"comment": {"body": "Right. Missed that. I\u2019ll update it in a future PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/262/_/diff#comment-88069338"}}
{"title": "Converted mich temperature sensor SDK changes to patches (BIS-1793)", "number": 263, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/263", "body": ""}
{"title": "Feature/BIS-1223 st mcu bringup", "number": 264, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264", "body": "Bring-up of SPC58EC-DISP with fingerprinting_manual project (similar to dialog ones)\n\n\nHardware:\n\nLEDs behavior - we have 3 green LEDs; Ill call them Rory, Gulliver and Boris\nButton works (SW 3, leftmost)\nUART over USB cable\n\n\n\nSoftware:\n\nlibfingerprinting static library project without encryption\n\nfingerprinting_manual\n\nReceives System Tests events from UART (FeatureExtractionSuccessEvent for now)\nExpects unencrypted features\n\n\n\n\n\nWith a bridge between the dialog and the ST (such as PC), the ST is capable of training and classification"}
{"comment": {"body": "\ud83d\udc4f", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88117042"}}
{"comment": {"body": "Are all these voids related to the ST somehow? Or is it just in the PR by accident?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88117230"}}
{"comment": {"body": "Great file", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88117704"}}
{"comment": {"body": "Something that the ST compiler raised as warnings. C functions without void parameter may except any number of arguments \\(unlike CPP\\) while with void excepts only 0 number of parameters", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88117766"}}
{"comment": {"body": "But this is main\\_loop\\_9x code?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88117889"}}
{"comment": {"body": "Great to have you find it to be useful", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88118046"}}
{"comment": {"body": "Document?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88118065"}}
{"comment": {"body": "Reusing them in the ST project", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88118230"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88118396"}}
{"comment": {"body": "Sorry you had to write this code in C", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88119002"}}
{"comment": {"body": "Great stuff, next step is CI?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88119742"}}
{"comment": {"body": "Actually it saved time", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88120165"}}
{"comment": {"body": "Probably the step after the next step", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/264/_/diff#comment-88120609"}}
{"title": "Feature/BIS-1223 st mcu bringup part 2", "number": 265, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/265", "body": "Embedded:\n\nCreate boilerplate (libbase_application) project with some workarounds and update accordingly sub-project fingerprinting_manual\nPrinting ST events works\nConsolidate dialog & ST events handling/printing, sharing routines, except for BSP utilities (LEDs, RTOS, UART, timestamp)\n\nPython:\n\nEvent deserialization works with ST\nFix bug in ModelProgress\nFunction to autogenerate serialization/deserialization hint for embedded\n\nPlease go over this thoroughly - Im trying to make this as similar as possible to current Dialog projects and setup"}
{"comment": {"body": "Our task will probably be in low priority comparing to other Bosch tasks on the chip\u2026\n\nMaybe we should set it to low priority also here\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/265/_/diff#comment-88709696"}}
{"comment": {"body": "This priority is for the system start task - it\u2019s standard to have it the highest priority to that it initializes other tasks without interruption. You can see the same behavior in Dialog SDKs \\(system\\_init task\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/265/_/diff#comment-88711262"}}
{"comment": {"body": "Can this be defined somewhere else? Seems unrelated to events.c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/265/_/diff#comment-88711466"}}
{"comment": {"body": "I\u2019ll try to make it more self-contained in the next PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/265/_/diff#comment-88714078"}}
{"comment": {"body": "How difficult would be it to change the format parser to support 100B instead of BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB\u2026.?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/265/_/diff#comment-88747890"}}
{"comment": {"body": "It\u2019s already part of the next PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/265/_/diff#comment-88754926"}}
{"title": "Feature/BIS-1347 compile and burn image on 9x", "number": 266, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266", "body": "Updated eclipse to cmake converter to support 9x project\nChanged name of libdialogboilerplate to ble_base_library\nAdded cmake compilation for 9x as well.\nChange jenkins to use the 9x SDK to burn the system testing image\nFixed paths for 9x\nUpdated burn script to work with 9x\nFixed issue with the way the project was compiled with/without encryption\nAdded execute permissions to the burning scripts\nSwitched to using the stripped version of libfingerprinting when burning the test agent\nFixed issues caused by merge\nNow building the main loop project for arm\nFixed hardware containter to be able to connect to the usb\nFixed jenkinsfile to work with multiple phones attached to the running hardware runner\nAdded install app on multiple devices script\nFix for docker to enable devices to connect to it\nFixed for docker with jlink\nAdded usbreset file\nFixes for docker slave\nnow the board will be hard-resetted after firmware is burned into.\nFixed board reset\nUpdated reset scripts\nAdded python hard reset script\nFixed bugs related to moving to 9x\nAdded option to not use cfg file for the cli programmer\nFixed file change mistake\nFixed jenkinsfile to burn the image for 9x\nStatic anlysis will now require label analysis instead of builder\nRemoved redundant port exposing for the docker image\nSmall fix to hardware runner Dockerfile\nAll Jenkinsfile stages now have an agent field\nMaybe agents everywhere is not such a good idea\nBuild and run unitests with the same agent\nFixed QT_FEATURE_002_preamble_length which was too strict and sometimes failed even when everything was ok\nFixed merge errors\nFixed jenkinsfile error for system tests\nFixed running hardware tests through the new docker container (heard you like dockers so i put a docker in your docker so you could docker while you docker)\nlaunch_hardware_slave.sh\nMoved Jenkinsfile flash programming to program_to_flash.sh\nTemporary remove of unittest\nFixed jenkins build\nFixed program_to_flash.sh\nchmod +x\nFixed Jenkinsfile\nUpdated System tests to new Grisha make_phone interface\nDocker improvements for Mac\nDocker improvements for Mac\nImprove docker mac support\nImprove docker mac support\nImprove docker mac support\nFixed issue with system tests caued by adding a command when creating a board instance\nFix for jenkins\nDocker improvement for Windows\nDocker improved Windows support\nDocker improved Windows support\nDocker Improved OS support\nDocker fixed mac support\nOnly kill ADB if needed\nendif fi\nFixed Makefile\nRemove TTY\nOnly mount /dev/ Docker\nRemoved lsusb\nFixed hw tests with the new phone api.\nDocker and mac are now best friends\nFixed vbox_disable_usb.sh for mac\nChanged timeout to check if it will now work with jenkins\nSome fixes to docker with system tests\n\nRevert \"Temporary remove of unittest\"\nThis reverts commit ff3073ea67b2ce748d6fd899ca0b5a8208c4f9c8.\n\n\nRestored temp changes"}
{"comment": {"body": "Could this be changed to strictly C compiler? Since `gcc` is a C compiler and we wouldn\u2019t want regressions with the Mac build since we\u2019re no testing Mac release often.  \n Or replace gcc with g\\+\\+", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89016123"}}
{"comment": {"body": "There are many new bash files causing a clutter - could they be put in folder hierarchy? We probably need to run only one of them to get the system tests we need", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89016927"}}
{"comment": {"body": "@gregory-levl , it\u2019s beyond obvious that cpp doesn\u2019t stand for C plus plus it stands for C pre-processor. Everybody knows that. \\(it\u2019s true\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89188971"}}
{"comment": {"body": "Irrelevant to this pull request. Please open a Jira ticket", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89191496"}}
{"comment": {"body": "Cool info on gcc:\n\n           The C preprocessor is intended to be used only with C, C++, and\n           Objective-C source code.  In the past, it has been abused as a\n           general text processor.\n\nHistory is bound to repeat itself", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89191562"}}
{"comment": {"body": "great idea", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89191640"}}
{"comment": {"body": "It already has", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89191722"}}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/1843978224-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89191941"}}
{"comment": {"body": "Irrelevance is irrelevant.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89192185"}}
{"comment": {"body": "Not enough arrows:  \n\n![](https://bitbucket.org/repo/x8eLRbo/images/1175523662-image.png)\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/266/_/diff#comment-89192634"}}
{"title": "Feature/BIS-1223 st mcu bringup part 3", "number": 267, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/267", "body": "Dialog:\n\nAdded Dialog anchor project which does feature extraction and outputs it\nMoved unpacking to libfingerprinting; other projects compile with that\n\nST:\n\nReceives encrypted features\nlibfingerprinting unpacks the feature set\nAdded 32MHz timer driver\nUART baudrate is 3000000"}
{"comment": {"body": "Many things have changed since original PR due to endianess becoming a configuration - you\u2019re welcome to review the PR again", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/267/_/diff#comment-89423011"}}
{"title": "Regression will now run with optimizations", "number": 268, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/268", "body": "Regression took too long because it was with -O0 and --coverage.\nNow its -O3 without --coverage, so it runs much faster.\nWill also run regression with enc and without enc in parallel, both in parallel to integration tests as well. This shortens the running time for tests significantly.\nDue to the removal of --coverage from the regression tests, we had to fill up the missing coverage by writing new tests and integrating encryption into integration tests (thank you @gregory-levl  for the help).\nDue to integration encryption into the integration tests, we accidentally made MISRA run on the with-enc version of the library, unearthing many MISRA issues that have been resolved.\nAlso some overall improvements to all the scripts and .gitignore"}
{"comment": {"body": "Now that I think of it, once we have enough packets, shouldn\u2019t we rebuild the model instead of decrypting the input and returning it?\n\n@michlevltech @mrmich ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/268/_/diff#comment-89420066"}}
{"comment": {"body": "I believe this is irrelevant to this pull request, could you open a Jira ticket for this?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/268/_/diff#comment-89420831"}}
{"title": "Fixed timed trigger for develop branch", "number": 269, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/269", "body": ""}
{"title": "Integration of timing and preamble feature feature", "number": 27, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/27", "body": ""}
{"comment": {"body": "don\u2019t forget to revert this file", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/27/_/diff#comment-71329114"}}
{"title": "Feature/BIS-1815 integrate new dialog sdk iq capture", "number": 270, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/270", "body": "New IQ capture SDK from Dialog. Note that the AGC value reported is invalid (RSSI is fine though)\n\nUpdated SDK to the IQ capture variant of *********\nAdded new IQ example projects (from Dialog)\nNew IQ capture works in iq_recording_agent\nTesting agent now uses new IQ capture\nfingerprinting_manual now also supports new IQ\nBosch demo now uses new IQ capture\nMade iq_print.c more maintainable\nbuild_eclipse_projects.sh & vbox*.sh improved\nFixed board_instance reset to support new SDK with Python monkey patching\nXML escaping in static analysis\nIQ transformation during preprocessing now testable (added tests too)\nLibrary will now use RFMON_SOURCE_3 by default (default values are the values used by Bosch)"}
{"comment": {"body": "Use the Side-by-side diff if you want to be able to read this diff", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/270/_/diff#comment-89420921"}}
{"comment": {"body": "Bless you", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/270/_/diff#comment-89421001"}}
{"comment": {"body": "main\\_loop\\_9x/projects/dk\\_apps/iq\\_data/ble\\_iq\\_scanner/DA1469x-00-Release\\_QSPI is part of the PR. Please remove those files", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/270/_/diff#comment-89421018"}}
{"comment": {"body": "Why not not use the entire 9 bits of the sample?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/270/_/diff#comment-89421287"}}
{"comment": {"body": "Didn\u2019t want to complicate this PR more than needed. BIS-1818 is for 9-bit support", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/270/_/diff#comment-89421312"}}
{"comment": {"body": "What are these magic numbers?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/270/_/diff#comment-89422667"}}
{"comment": {"body": "It\u2019s based off of Dialog\u2019s IQ demo. I\u2019m not entirely sure why these numbers are used.\n\n0x20000000 is the RAM\u2019s base address\n\nNo idea what\u2019s 0x800000", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/270/_/diff#comment-89422683"}}
{"comment": {"body": "Merge?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/270/_/diff#comment-89423195"}}
{"title": "Android build docker", "number": 271, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/271", "body": "Added a docker used to build the android app\nAdded android build script to the jenkinsfile\nAdded build-tools;28.0.3 to the docker image to speed up the build process"}
{"comment": {"body": "I commented on the commits directly, please see my comments.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/271/_/diff#comment-89422716"}}
{"comment": {"body": ":rage: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/271/_/diff#comment-89422721"}}
{"title": "BIS-1818 move to 9bit in iq", "number": 272, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272", "body": "Added 9-bit IQ support"}
{"comment": {"body": "Any reason to use `IqSampleDepth `if it\u2019s only used for 1 source out of 2?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425328"}}
{"comment": {"body": "Maybe this could be reduced to a uint16\\_t-only operation with `(uint16_t)0` instead of this monstrosity\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425350"}}
{"comment": {"body": "Not sure what you mean but you\u2019re free to edit it to whatever you want \\(you can use the Bitbucket editor\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425368"}}
{"comment": {"body": "It\u2019s more like \u201cMaxIqSampleDepth\u201d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425374"}}
{"comment": {"body": "`const uint16_t i9 = (uint16_t)((((uint16_t)0 - ((uint16_t)i_sign)) & 0xff00U) | i_lsb8);`\n\n`const uint16_t q9 = (uint16_t)((((uint16_t)0 - ((uint16_t)q_sign)) & 0xff00U) | q_lsb8);`\n\nIs this how you use Bitbucket editor?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425389"}}
{"comment": {"body": "I wish I could block people on BitBucket", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425421"}}
{"comment": {"body": "You\u2019re now banned from my pull requests", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425427"}}
{"comment": {"body": "Might be a more \u201cprettier\u201d way to do 2\u2019s complement is:  \n`int16_t i = (int16_t)((sample & 0x007fc000U) >> 14u);`  \n` int16_t q = (int16_t)((sample & 0xff700000U) >> 14u);`  \n`if (i > (1<<8)) {`  \n`   i -= (1<<9);`  \n`}`  \n`if (q > (1<<8)) {`  \n`   q -= (1<<9);`  \n`}`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425487"}}
{"comment": {"body": "_This comment was banned by the Pull Request master_", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425626"}}
{"comment": {"body": "I'm afraid branching may hurt the performance ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425737"}}
{"comment": {"body": ":joy: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/272/_/diff#comment-89425976"}}
{"title": "BIS-2072 extract per sample agc along with new iq capture", "number": 273, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/273", "body": "Library will now record AGC transitions in IQ for future use\nFixed flash model bug / bad enum value\nFixed test iq transform unittest"}
{"comment": {"body": "What\u2019s the reason behind 20?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/273/_/diff#comment-89506248"}}
{"comment": {"body": "The AGC doesn\u2019t change too much throughout the packet. We only need to record its transitions \\(on which indices they occur and their values\\). 20 is more than enough to hold a typical packet. On rare cases when there are more transitions - for now we simply pretend there are only 20. Maybe in the future we can make it an error \\(we wouldn\u2019t want to fingerprinting a packet with more than 20 transitions - it means something went awfully wrong with its reception\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/273/_/diff#comment-89506839"}}
{"title": "Feature/BIS-1223 st mcu bringup part 4", "number": 274, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274", "body": "Add packet ID with anchor name and session ID instead of feature timestamp via feature configuration\nImprove batch reading from UART\nAdd MultiBoardDigest to handle communication between dialog and ST and capturing events in a serial manner\nAdd EventsDecryptor; add conversion between ctypes and hydra\nMake Present data decrypt the encrypted features\nFix present_data usage (cfo struct mismatch and not plotting) \nFix Levl_FeatureCfg_internal_t mismatch in ctypes\nFix libfingerprinting helpers"}
{"comment": {"body": "great name", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89528738"}}
{"comment": {"body": "Does this still work if I only want to record manual\\_fingerpinting on Dialog without ST?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89528968"}}
{"comment": {"body": "You betcha", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89529253"}}
{"comment": {"body": "I think it should be the job of the event deserializer to decrypt encrypted events - this way any application that needs to consume events \\(such as present\\_data\\) wouldn\u2019t have to worry about encryption\n\nOr maybe add def post\\_process\\(\\) function to each event, that for some events, would cause decryption, and for some, nothing.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89529717"}}
{"comment": {"body": "Does this mean we can now delete all ctypes struct and keep only the Hydra ones? Or I\u2019m understanding it wrong?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89530050"}}
{"comment": {"body": "No, not yet. ctypes structs need to be present, but they can be generated during run-time from hydra with some extra effort.\n\nAnd there\u2019s the question of pointers. For now, only in the capturedpacket struct", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89532244"}}
{"comment": {"body": "With the current design \\(great one\\), it\u2019s not up to the event deserializer to monitor and handle events. It\u2019s up to the higher layer to do so.\n\nI could tell the event pickler/deserializer/testing\\_agent to do, or create a middleman to handle it. Your call.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89533063"}}
{"comment": {"body": "Right before `evt_obj = struct_type.deserialize(event_struct_serialized)` we can call `struct_type.preprocess(event_struct_serialized)` \\(if preprocess doesn\u2019t exist for that specific type then it should skip this step\\). This function can modify the buffer before being deserialized \\(such as decrypting it\\) and only then it will be passed to Hydra with the decrypted contents.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89533828"}}
{"comment": {"body": "I\u2019ll leave this design implementation to a later stage since only user of the decryption is present\\_data", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89631271"}}
{"comment": {"body": "Added test to verify structs integrity.\n\nLast chance to comment", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/274/_/diff#comment-89632794"}}
{"title": "Regression will now run Catch tests in parallel using fork - Jenkins build time now only 2m", "number": 275, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/275", "body": ""}
{"comment": {"body": "Why did you decline the PR?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/275/_/diff#comment-89612626"}}
{"title": "Regression will now run Catch tests in parallel using fork - Jenkins build time now only 2m", "number": 276, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/276", "body": ""}
{"comment": {"body": "there\u2019s a catchy way to add arguments: [https://github.com/catchorg/Catch2/issues/793](https://github.com/catchorg/Catch2/issues/793)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/276/_/diff#comment-89603774"}}
{"comment": {"body": "Is there a way to generate this list automatically? Maybe using catch listing [https://github.com/catchorg/Catch2/blob/master/docs/command-line.md#listing-available-tests-tags-or-reporters](https://github.com/catchorg/Catch2/blob/master/docs/command-line.md#listing-available-tests-tags-or-reporters)?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/276/_/diff#comment-89604057"}}
{"comment": {"body": "I want to remove arguments, not add them", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/276/_/diff#comment-89604176"}}
{"comment": {"body": "It\u2019s a command-line interface, I need an API. I\u2019d have to look at the Catch source code to figure out how to extract this list. It wasn\u2019t so simple so I just gave up.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/276/_/diff#comment-89604259"}}
{"comment": {"body": "I meant argument parsing - instead of manually doing so.\n\nThere\u2019s tons of catch-y ways: [https://github.com/catchorg/Catch2/issues/890](https://github.com/catchorg/Catch2/issues/890)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/276/_/diff#comment-89604461"}}
{"comment": {"body": "That\u2019s for parsing CLI inside test cases, here we\u2019re parsing them before Catch::Session\\(\\).run", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/276/_/diff#comment-89605575"}}
{"title": "Fix system tests", "number": 277, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/277", "body": "Fixed error in IQ capture caused by moving to the new SDK.\nFixed system tests CFO struct name after change.\nFixed failing system tests."}
{"comment": {"body": "Maybe a 3rd approve would help @guyklevl merge the PR", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/277/_/diff#comment-90261678"}}
{"comment": {"body": "@michlevltech we need your help", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/277/_/diff#comment-90261774"}}
{"title": "Only run enc regression tests when we have encryption", "number": 278, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/278", "body": ""}
{"title": "Feature/integration tests broken on pc", "number": 279, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/279", "body": "Fix export of validation helper\nHelper lib can't run in a different configuration from the fingerprinting lib (encrypted/not)"}
{"comment": {"body": "Please approve", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/279/_/diff#comment-90235615"}}
{"comment": {"body": "it would be nice if you did: `lib, iiv_helper_lib = lib_with_iiv_helper` at the beginning of each function and kept the rest of the code as it was. It\u2019s more readable like this.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/279/_/diff#comment-90236097"}}
{"comment": {"body": "Other than this I approve", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/279/_/diff#comment-90236107"}}
{"comment": {"body": "Fixed in f034fe74d2a3963c8f0910232074c31e27b51d1d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/279/_/diff#comment-90259732"}}
{"title": "Feature/FIN-276 CI build ARM image", "number": 28, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/28", "body": "Added Jenkinsfile\nupdated script\nAdded Jenkinsfile\nAdded Jenkinsfile\n*updating cmakelist for ARM\ntest commit\nanother test commit\nanother jenkins test\nanother jenkins test\nAdded Jenkinsfile\ncorrected script\nanother test at eclipse build\n*eclipse build\ntesting if jenkins needs a file\ndecided not to add previous commit file\nstarting to add eclipse project to cmake\ndiscarding eclipse build\ncmakelist generation WIP"}
{"title": "Will now perform cached S3 downloads and without relying on buckets being public", "number": 280, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/280", "body": ""}
{"comment": {"body": "Any chance to get a progress bar here? Same as the one you removed `ProgressPercentage`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/280/_/diff#comment-90228660"}}
{"comment": {"body": "Your progress bar was glitching up in the Jenkins console especially when running in parallel with other stuff. I figured it was rather obsolete now that we have caching ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/280/_/diff#comment-90228698"}}
{"comment": {"body": "It\u2019s useful when testing locally", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/280/_/diff#comment-90228721"}}
{"comment": {"body": "Alright I'll add it back ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/280/_/diff#comment-90228728"}}
{"title": "Feature/BIS-2114 integrate 10.0.2.60 sdk version", "number": 281, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/281", "body": "NOTE -> This pull request is not into develop, it's into an intermediate branch I created called intermidiate/add_10.0.2.60_sdk just so the new SDK diff doesnt drown out the relevant diff. After this pull request is approved Ill merge intermidiate into develop directly.\n\n\nUpdate UART SDK patch file to new version\nRemoved platform_devices.h patch because the file no longer exists in the new SDK\nUpdated sdk_defs.h patch file to new version\nUpdated hw_gpadc.c/.h patches to new SDK version\nRemoved patch for ble_config.h because it's no longer needed in new SDK\nRemoved obsolete definitions from .cproject's to match new SDK\nChanged CONFIG_RETARGET_UART to match new SDK\nFixed tempsens drivers\nAdded missing include paths\nPatched ad_gpadc.c to ignore NULL io\nMore new SDK changes\nCompletely removed old RFMON, updated everything to use new SDK RFMON\ncli_programmer.exe will now have an AA-supporting internal uartboot.bin .exe resource\nFixed events_deserializer.py main print\nAll Eclipse projects now build with the new SDK\nFixed temperature reading threading bug\nAdded option to not use cfg file for the cli programmer\nuartboot.bin updated to AA supporting version\nFixed dialog image burning to work with the new SDK."}
{"comment": {"body": "Note that this pull request is really boring, it\u2019s just changes to adapt to the new SDK, not much business code here.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/281/_/diff#comment-90234628"}}
{"comment": {"body": "Also note that the red LED stopped working, it has been decided that this pull request should be merged anyway and the red LED will be fixed later", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/281/_/diff#comment-90234636"}}
{"title": "Fixed compilation of unit tests for MacOS", "number": 282, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/282", "body": ""}
{"title": "Android app sometimes randomly fails build on Jenkins, this is an attempt to maybe fix it", "number": 283, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/283", "body": "Add missing directory to Android docker\napt utils before everything\nMore fixed to Android Dockerfile to maybe fix download issue"}
{"comment": {"body": "Why do you think this should fix it?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/283/_/diff#comment-90268349"}}
{"comment": {"body": "It\u2019s just random warnings that appeared during the container build, it\u2019s good to fix them anyway, maybe it will also fix the download issue", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/283/_/diff#comment-90268483"}}
{"comment": {"body": ":man_shrugging: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/283/_/diff#comment-90268593"}}
{"title": "Feature/BIS-1323 add logs to tests", "number": 284, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/284", "body": "Added printing assert values for each assert - only in Catch\nregression tests should check if the os supports fork before they run\nAdded prints of tested values to log\n\nPlease review also the jenkins tests log - this is how we will write tests from now on and we want this to be usable to understand what the test actually did"}
{"comment": {"body": "I would say we should look more into [OpenMP](https://bisqwit.iki.fi/story/howto/openmp/) for paralleling our tests, it has a really nice syntax and can be disabled without any modifications to the code just by removing the `-fopenmp` flag from the `gcc` command.\n\nI have been using it a few times in the past if any help is needed.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/284/_/diff#comment-90426050"}}
{"comment": {"body": "It won\u2019t work, we must use fork to share the giant in-memory dataset between processes and it\u2019s super fast because it\u2019s read-only \\(no COW\u2019s\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/284/_/diff#comment-90426301"}}
{"comment": {"body": "OpenMP is multithreaded by default, Hence, shares its memory.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/284/_/diff#comment-90466449"}}
{"comment": {"body": "We can\u2019t do multi-threading because the library is not thread safe. We can\u2019t do simple multi-processing because we have to share the datasets between processes. That leaves us with fork as the only simple option I can think of", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/284/_/diff#comment-90468311"}}
{"comment": {"body": "Please add a line number / file position to the asserts. Something like:\n\n    import inspect\n    inspect.currentframe().f_back", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/284/_/diff#comment-90634239"}}
{"title": "Feature/lower num packets training", "number": 285, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/285", "body": "Fixed docker python installation to make sure that we always install python3.6\nDecreased num of packets for training to 500 to make training faster"}
{"title": "Feature/BIS-1223 st mcu bringup part 5", "number": 286, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/286", "body": "~~Minor changes ahead~~\n\nDecouple bosch demo protocol from dialog\nAdd ST testing agent project that receives demo protocol from 2nd UART channel\n\nUpdate board_instance and friends:\n\nAdd MultiBoard digester (in event_deserializer.py), with option for multiprocessing (event_deserializer_offload.py)\nAdd serialization of asynchronous dialog+ST messages\nIQ deserialization decouples from board_instance\nIQ capturing broken due to performance issues with pyserial readline\n\n\n\nCurrently not using multiprocessed option and serialization of asynchronous messages due to performance issues - will be handled later since theyll be needed for monitor/multiple dialogs\n\n\nAdded test test_ITD_084_Classification_of_a_phone_time_limit_Matching_classification with board_agent_multi fixture\n\nFixture requires Dialog with fingerprinting_anchor_agent and ST with testing_agent\n\n\n\nReduce encrypted feature extraction size to 128 bytes due to performance issues"}
{"comment": {"body": "I think this should be changed before the V2 release, we don\u2019t want Bosch getting used to that size, it\u2019s too limiting", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/286/_/diff#comment-90579761"}}
{"comment": {"body": "![](https://bitbucket.org/repo/x8eLRbo/images/2579701990-image.png)\n`evil_words.txt` saves the day?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/286/_/diff#comment-90580526"}}
{"comment": {"body": "Agree, but if that causes problems to start running tests - it\u2019s good enough for now.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/286/_/diff#comment-90580568"}}
{"comment": {"body": "If the patch fails to apply, it's either due to conflicts or some other errors. Why would we want to ignore that? `|| true` ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/286/_/diff#comment-90581085"}}
{"comment": {"body": "@michlevltech 's idea", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/286/_/diff#comment-90584477"}}
{"comment": {"body": "Almost\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/286/_/diff#comment-90584611"}}
{"comment": {"body": "Not saying it should be changed now, just that it would be a good idea to do so before we release", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/286/_/diff#comment-90584760"}}
{"comment": {"body": "It fails when it\u2019s reapplying patches on a patched file. \n\n> error: patch failed: main\\_loop\\_st/testing\\_agent/components/components.c:23  \n> error: main\\_loop\\_st/testing\\_agent/components/components.c: patch does not apply\n\nI don\u2019t have a way to test whether a patch was already applied, so I `|| true`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/286/_/diff#comment-90585253"}}
{"title": "Pytest will now also print positive asserts", "number": 287, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/287", "body": ""}
{"title": "Feature/BIS-1223 st mcu bringup part 6", "number": 288, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/288", "body": "PowerPC lib objfuscation\nDone locally on PC"}
{"title": "Enabled TID filtering for system tests", "number": 289, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289", "body": ""}
{"comment": {"body": "Gilfyole would create a randomDataBytesFactory.java", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91031991"}}
{"comment": {"body": "What\u2019s the idea behind making it static?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91032016"}}
{"comment": {"body": "Since it doesn't use `self` there\u2019s no reason to make it non-static, makes it more flexible and useable from outside the class without creating an instance", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91034171"}}
{"comment": {"body": "I didn\u2019t watch Silicon Valley so I don\u2019t get the joke", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91034440"}}
{"comment": {"body": "I have and I still don\u2019t get it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91038178"}}
{"comment": {"body": "You should then", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91044585"}}
{"comment": {"body": "bottom line is that it would be cool to have a packet factory since everybody likes factory design pattern is common in java.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91047303"}}
{"comment": {"body": "But we need it for the ST..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91047796"}}
{"comment": {"body": "Nice :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91069070"}}
{"comment": {"body": "Comment is not aligned with implementation\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91200950"}}
{"comment": {"body": "You need to \n\n1. close the serial port\n2. wait for queue termination\n3. join this thread\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91404511"}}
{"comment": {"body": "Actually my problems are with the events queue.join, not this new queue, but thanks I\u2019ll modify this", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91404860"}}
{"comment": {"body": "Well then it should be fixed in a separate pull request\u2026 I did a lot of work to make st\\_pt5 play nice with the regular system tests without fully reverting it, this one I just skipped because you\u2019re not here and we need to talk about it \\(why does it send all commands to both dialog and st for example? didn\u2019t want to change that but I\u2019m required to do so in order to fix this\u2026\\)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91423248"}}
{"comment": {"body": "Just writing down remaining tasks:\n\n* Port TID API to ST\n* Fix reset command for ST\n* Rename `start_classification_with_features_from_flash`  \n* Maybe fix thread termination stuff\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91433477"}}
{"comment": {"body": "I gave it some thought and it seems that closing the serial port is problematic:\n\n1. The thread calling `.close` is not the one reading from it, meaning it can cause an error in the reading thread\n2. I don't care about the data in the queue here so no need to call `join` on the queue \\(which is what I assume you meant in #2\\) - once `close()` is called `run()` finishes running and the queue is discarded\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/289/_/diff#comment-91436124"}}
{"title": "Resubmit: Integration of timing and preamble feature feature", "number": 29, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/29", "body": "Integration of timing and preamble feature.\nLast PR says its merged but it didnt actually merged through."}
{"title": "Feature/BIS-1323 add logs to tests", "number": 290, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/290", "body": "Run the Catch unitests again - second time with console reporter\nregression tests should check if the os supports fork before they run\nAdded prints of tested values to log\nfix some prints\nAdded comment to explain why running each test twice\nAdd more logs\nAdded more logs to tests\njenkins fix\nJenkins fix\nindentation fix"}
{"title": "fixing file name and line not prining", "number": 291, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/291", "body": ""}
{"title": "Now jenkins will only run some of the hardware tests on each pull request", "number": 292, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/292", "body": ""}
{"comment": {"body": "Please close this I\u2019ve already merged it into my filtering branch and it works well", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/292/_/diff#comment-91421375"}}
{"title": "Rename: DLL_PUBLIC -> LEVL_DLL_PUBLIC", "number": 293, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/293", "body": ""}
{"comment": {"body": "Why? ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/293/_/diff#comment-91055583"}}
{"comment": {"body": "Requested by bosch so we don\u2019t have name collisions", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/293/_/diff#comment-91055647"}}
{"comment": {"body": "As far as my understanding goes this doesn't change anything. Can you maybe explain more specifically what is this trying to achieve?\n\n\u200c\n\nOh maybe you mean DLL\\_PUBLIC and DLL\\_LOCAL themselves are colliding\u2026 alright", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/293/_/diff#comment-91055808"}}
{"comment": {"body": "Yes. The macro names as we define them in files they are including in their project..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/293/_/diff#comment-91055943"}}
{"title": "Feature/cfo const feature fix", "number": 294, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294", "body": "\nSupport constant features (i.e only one device temperature or only one channel in the training process) - I process the relevant column and row in A^T *A matrix of the linear regression to make sure the matrix is invertible.\n\nadd matirx to store the fixed invertible matrix\nimplement the constant features removal\nsome code simplification + misra fixes\nmove default slop to defines in cfo_model.h\noverride the regression default bad value to default values in cfo model\nallow numerical error in constant feature check + add unitests\nadd test for 2 constant features\nadd unitest for all trained features are constant\nmove feature average calculation to function\nmove feature update from linear regression result to function"}
{"comment": {"body": "In the pull request **Description** please remove irrelevant commit messages like \u201cmisra\u201d and \u201cmore comments\u201d and leave the interesting stuff so we can see an itemized list of what\u2019s changed before we read the confusing diff\n\n  \n  ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91309550"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91309943"}}
{"comment": {"body": "I am not sure but do we want to send `cfo_model_tmp_res` to PC together with the progress for monitoring?\n\nMaybe it will help us to debug ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91324714"}}
{"comment": {"body": "Could there be division by zero issue here? data\\[0\\] is for intercept, right?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91368922"}}
{"comment": {"body": "Maybe it\u2019s starting to take too much space on the stack as there\u2019s already `x_t_w_x_invert` on the stack.\n\nCould you move these variables to global space?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91369531"}}
{"comment": {"body": "data\\[0\\] can\u2019t be zero. It's <intercept\\_vec, intercept\\_vec> where intercept\\_vec is a vector of ones.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91397869"}}
{"comment": {"body": "Just reduced the size of those by factor of 4 \\(we allow up to 10 variables while our regression has max 4 variables\\)\n\ncommit c1b09ff6d90e8fd8fad850477bfb28b30373a514", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91398415"}}
{"comment": {"body": "Not sure, I think it\u2019s pretty straightforward to get it from the progress object.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91399064"}}
{"comment": {"body": "Just set `x_t_w_x_fixed` and `x_t_w_x_invert` to static, our library is not thread-safe anyway", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91448832"}}
{"comment": {"body": "Ok, after a discussion with Omer I understand what you mean. The x\\_t\\_w\\_x\\_invert is only 2 floats and a pointer \\(it doesn\u2019t hold the data\\). I can make it static inside the function as Omer suggested but it doesn\u2019t seem too important\\(to save 12 bytes\\). WDUT?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91451727"}}
{"comment": {"body": "If it\u2019s only 2 floats and a pointer, then leave it as it is.\n\nI generally hate statics inside functions since they\u2019re more difficult to view in debug mode. Whatever is clearer to you.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91452955"}}
{"comment": {"body": "Do not merge. I want to run system tests first to see that nothing\u2019s broken", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91454568"}}
{"comment": {"body": "Good luck", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91454662"}}
{"comment": {"body": "[Passed](https://jenkins.levltech.com/blue/organizations/jenkins/bosch_integration/detail/feature%2Fcfo_const_feature_fix/28/pipeline/214)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91463534"}}
{"comment": {"body": "cool!", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91481815"}}
{"comment": {"body": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05d0\u05d5\u05ea\u05d9 \u05d1\u05d8\u05e2\u05d5\u05ea \u05d0\u05d5 \u05d1\u05db\u05d5\u05d5\u05e0\u05d4? \u05d0\u05e0\u05d9 \u05de\u05d1\u05d9\u05df \u05d1\u05d2\u05d3\u05d5\u05dc \u05d0\u05ea \u05d4\u05d1\u05e2\u05d9\u05d4 (\u05e1\u05d9\u05e4\u05e8\u05ea \u05dc\u05d9 \u05d1\u05d6\u05de\u05e0\u05d5 \u05de\u05d4 \u05d4\u05d9\u05d0)\n\u05d5\u05d0\u05ea \u05d4\u05e4\u05ea\u05e8\u05d5\u05df (\u05d1\u05d2\u05d3\u05d5\u05dc, \u05dc\u05d0 \u05dc\u05d4\u05e9\u05ea\u05de\u05e9 \u05d1\u05d8\u05d5\u05e8 \u05d4\u05e7\u05d1\u05d5\u05e2 \u05d1\u05e8\u05d2\u05e8\u05e1\u05d9\u05d4) \u05d0\u05d1\u05dc \u05dc\u05d4\u05ea\u05e2\u05de\u05e7 \u05d1\u05e7\u05d5\u05d3 \u05d9\u05d9\u05e7\u05d7 \u05dc\u05d9 \u05dc\u05d0\n\u05de\u05e2\u05d8 \u05d6\u05de\u05df...", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/294/_/diff#comment-91872230"}}
{"title": "Feature/BIS-2154 regression support in 64bit", "number": 295, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295", "body": "\n\nAdd fastdwarf project\nAdd generate_data_structures_report\nCreate a fastdwarf parser and print Levl_CapturedPacket_st\nUse output from Levl_CapturedPacket to run regression tests\nRun 32 and 64 bit tests configurations\nFix some project warnings"}
{"comment": {"body": "Can you please provide an overview of what\u2019s going on?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295/_/diff#comment-91439199"}}
{"comment": {"body": "Isn't `(void)` sufficient for MISRA?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295/_/diff#comment-91440630"}}
{"comment": {"body": "MISRA doesn\u2019t like casting bool to void. Different types\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295/_/diff#comment-91446138"}}
{"comment": {"body": "The reason regression tests didn\u2019t work on 64-bit was due to packing method of the captured packets, as they were packed according to memory representation on 32-bit system. Due to the pointer in the structure, they were of different sizes and member offsets on 64-bit environments.\n\nI added a tool called fastdwarf which analyzes an elf file and prints the stored data structures with the members offsets \\(and types\\), allowing detection of paddings and actual location. I used this tool to analyze Levl\\_CapturedPacket\\_st, created a python script to generate a generic compatible C struct without pointers and with paddings, so that the final product would have the same representation as the 32-bit one.  \nThus I was able to modify the regression tests to run in 64-bit env as well.\n\nI then added duplicated configurations for 32-bit tests and 64-bit tests so that both of these configuration would run during CI, like we discussed last week.  \nThis makes us run in 4 configurations so far, 32-bit and 64-bit, with encryption and without.\n\nAs a side note, we can scale the fastdwarf tool to validate/generate ctypes/hydra structures..\n\nOther than that, I fixed a couple of compiler warnings that annoyed me.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295/_/diff#comment-91448032"}}
{"comment": {"body": "1. It seems like the generated struct is tracked by Git. Do we want that? It\u2019s generated, not sure it\u2019s a good idea to track it\n2. It\u2019s actually pretty easy to predict the compiler padding, the only differences \\(that I know of\\) between 32-bit and 64-bit is that 64-bit forces uint64\\_t types to be 64-bit aligned while 32-bit doesn\u2019t force them. The pointer size is also different. I think performing the analysis ourselves using Python on the existing ctypes representation of the CapturedPacket struct without relying on fastdwarf might be a better, more simpler way to achieve this.  \n   Even better, stop relying on this struct during regression and create a specialized struct for regression. We have to do a conversion anyway between pickles and some intermediate binary format we upload to S3, might as well use an intermediate architecture-agnostic format and upload that to S3\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295/_/diff#comment-91458205"}}
{"comment": {"body": "1. I hard coded the solution \\(structure definition\\) since I didn\u2019t want to store and load autogenerated code at this time.\n2. It sounds pretty easy but we didn\u2019t do it so far and shouldn\u2019t guess the solution when we can use tools for that. Best way is elf files analysis since you don\u2019t know the compiler\u2019s considerations.  \n  Packing in a different format might be an overkill, and we tried this path before with CapnProto.\n3. Fastdwarf \\(or any other similar tool to extract structure information\\) can be used to verify the ctypes and Hydra structures, so we should proceed with this path of analysis our elf products.\n\n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295/_/diff#comment-91544838"}}
{"comment": {"body": "\u201cPacking in a different format might be an overkill, and we tried this path before with CapnProto.\u201d\n\nNot talking about doing anything complicated. No need for CapnProto. Just store all the fields, one by one, one after another, with a constant size for each field, independent of the platform, and then deserialize that simple raw representation with simple platform-agnostic C code. The CapturedPacket struct is very simple, I don\u2019t see the reason to use external tools.\n\n\u201c\u2026 since you don\u2019t know the compiler\u2019s considerations.\u201d:\n\nWe do know the compiler\u2019s considerations because it needs to adhere to the ABI. uint8\\_t no restrictions, uint16\\_t must be 2-byte aligned, uint32\\_t 4-byte aligned, uint64\\_t 8-byte aligned unless on x32 then it\u2019s 4-byte aligned.\n\nAnyway, we don\u2019t need to care about that if we just use a simple custom binary representation in the S3 file as I mentioned above.\n\n\u201cFastdwarf \\(or any other similar tool to extract structure information\\) can be used to verify the ctypes and Hydra structures, so we should proceed with this path of analysis our elf products.\u201d -\n\nThat\u2019s a great idea, it will be good for verification but I still don\u2019t think we should use it to auto-generate code that is later used by the regression tests, it over-complicates the regression tests.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295/_/diff#comment-91576540"}}
{"comment": {"body": "I think I need to clarify that no code is being generated during CI for regressions tests; I made some initial infrastructure from extracting structure information automatically from source code/executable.  \nThis can be used for further verification of the representations of those data structures across our environments.\n\nHowever, at this moment, using a simple script \\(infrastructure for the future\\), this allowed me to easily unpack regression datasets for both 32-bit and 64-bit environments without creating new serialization methods and having other unnecessary overheads.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295/_/diff#comment-91602264"}}
{"comment": {"body": "I\u2019m gonna merge it even if nobody approves in 30 minutes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/295/_/diff#comment-91903196"}}
{"title": "Fail library functions when init was not called", "number": 296, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/296", "body": "Using new return errors for LIB_NOT_INITED"}
{"comment": {"body": "Please add these new definitions to ctypes", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/296/_/diff#comment-91870390"}}
{"comment": {"body": "Fixed in 4827f07a7772a728c2573520a7bae7a7b809c5b3", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/296/_/diff#comment-91875262"}}
{"comment": {"body": "What about `Levl_ClearCfg`? Don\u2019t we want to access it in python?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/296/_/diff#comment-91876577"}}
{"comment": {"body": "Probably should.\n\nPlease open a task on me, I\u2019ll do it in a follow up pull request.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/296/_/diff#comment-91876712"}}
{"title": "Feature/BIS-2201 online training of cfo", "number": 297, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297", "body": "The feature is done except small code coverage issue\n\nAdd Levl_ClassifyFinalize and return values Refactor CFO model building in Levl_fingerprinting.c Collect new model information when classifying\nUpdate API changes in python\nfix criteria of when to add a packet to online training\ndisable progress when calling finalize - must call ClassifyReset to reanable it\nImplement ClassificationProgress in ctypes and complete verification against hydra\nFix C event type\nFix C descriptor\nadd cfo check to decide if a packet is suitable for online training\nfix test that started failing after adding copy cfo_progress to classify_progress struct at first classify after ClassifyReset()\navoid code duplication in test packet and test packet for online training\ntest if cfo_math_for_online_training only if cfo feature is not disabled\nadd evaluation for model after online training and decide the update score\nAdded first tests for online training\nAdded decrypt function for ClassifyFinalize()\nAdded check in ClassifyFinalize that last classify() returned MATCH\nadd test for classification_finalize on one packet with minor update\nadd test for major update\nfix online training tests and last classification result update\nadd test for finalize with not finished classification"}
{"comment": {"body": "Most of our integration tests are in python. Any reason to use Cpp?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91869925"}}
{"comment": {"body": "`last_classify_result` field is missing", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91869950"}}
{"comment": {"body": "What is the plan with the missing code coverage?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91869970"}}
{"comment": {"body": "what\u2019s the purpose of `does_feature_match`? It looks like a duplication of `feature_votes`", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91869989"}}
{"comment": {"body": "Please also fix merge issue :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91875581"}}
{"comment": {"body": "How come the interval for online training is larger?\n\nIf it passed here with the larger interval, it\u2019s still going to be reject as the classification result will be \u201cNOT MATCH\u201d.  So it looks like some excessive work done here.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91876172"}}
{"comment": {"body": "I think if you call Classify\\_Finalize before the classification is being completed you should return an error.\n\nWe need to add an error to the EIS for that.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91876358"}}
{"comment": {"body": "I have a fundamental issue with this condition.\n\nYou just cannot apply a looser criteria for the online training vs regular classification.  \n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91876428"}}
{"comment": {"body": "Will there be a follow-up pull request to handle transition between constant slope to trained slope in the CFO model?\n\nBecause right now if we start with a constant slope, the model is actually never updated..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91876611"}}
{"comment": {"body": "The intercept is updated anyway. I suggest not using the constant slop mode at all. \n\nIf we had only one temperature, in pull request feature/cfo\\_const\\_feature\\_fix we make sure the slop is \u201cconstant\u201d, and if we got more than one temperature we theoretically can learn the slop, anyway, if we got an unreasonable slop, we go back to the \u201cconstant\u201d one. I agree that we can implement a more complicated logic, but I\u2019m not sure it\u2019s a top priority.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91879105"}}
{"comment": {"body": "I\u2019m not sure I follow the comment. \n\nWe use the packet in online training iff all the features but cfo are good and the cfo is not way off. Does it make sense to you?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91879157"}}
{"comment": {"body": "This interval defines when we accept the packet for online training. It may be \u201cNOT MATCH\u201d in classification but still ok for online training if the cfo is under 5\\*conf\\_interval. ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91879244"}}
{"comment": {"body": "solved", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91911276"}}
{"comment": {"body": "Ok.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91976528"}}
{"comment": {"body": "As we discussed previously, it probably should be something smaller like 3-4", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91976646"}}
{"comment": {"body": "I have already updated the EIS for that :wink:  \n", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91976799"}}
{"comment": {"body": "I\u2019ve approved the PR.\n\nPlease solve all MISRA issues before merging.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91977280"}}
{"comment": {"body": "feature\\_votes is the result of the whole voting table.\n\ndoes\\_feature\\_match is only for last packet.\n\nlet\u2019s leave it as agreed with Michael", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/297/_/diff#comment-91982148"}}
{"title": "Adjust model and feature set sizes", "number": 298, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/298", "body": ""}
{"comment": {"body": "Need to update `bosch_integration\\libfingerprinting\\tests\\integration_tests\\pyfingerprinting_reborn\\structs\\pyfingerprinting_structs.py` as well", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/298/_/diff#comment-91879136"}}
{"comment": {"body": "Added also that.\n\n811835bbb65919eb879201e28f0356a1f002c98a", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/298/_/diff#comment-91879966"}}
{"title": "Added training progress indication to Levl_Train", "number": 299, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299", "body": "dd"}
{"comment": {"body": "Are Bosch okay with an indication just for a single anchor? Wouldn\u2019t they maybe like to know the progress for each anchor so they can maybe ask the user to move the phone around the car or something?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91878377"}}
{"comment": {"body": "I think we will start first with a single progress indicator, and maybe do something more complex in the next releases.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91878687"}}
{"comment": {"body": "Sadly due to Catch limitations all tests added to this file also need to be added to a list in regresion\\_main.cpp, otherwise they will not run in Jenkins", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91879387"}}
{"comment": {"body": "Right now it's `(curr*ind_done) * max`, what would you want me to change it to for multiple anchors? Maybe `((anch1 + anch2 + ... + anch_n) * ind_done) / (max*n)`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91879454"}}
{"comment": {"body": "Sounds good to me.\n\nJust need to make sure anch\\_x does not exceed max. Otherwise, their sum might overflow.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91879990"}}
{"comment": {"body": "Good point. It's actually allowed to exceed max so in this calculation I'll clip it down to max if it's higher", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91880039"}}
{"comment": {"body": "This is indeed a very sad story.\n\nFixed it in ffb1b68a58927100c906e4e077a9d5f5d771ed3d", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91880052"}}
{"comment": {"body": "Maybe add at the end of this test a TrainReset\\(\\) call and verify that progress becomes zero after that\u2026", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91898903"}}
{"comment": {"body": "maybe also check that `progress_ind != LEVL_TRAINING_PROGRESS_IND_DONE`?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91903475"}}
{"comment": {"body": "Yeap.\n\nAdd in 23bf66e503e270a4d607ad4d47b419a62cf52e38", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91903833"}}
{"comment": {"body": "How come Klocwork is not crying about this line? :thinking: \n\nShouldn\u2019t allow missing parentheses and casting of complex expression", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91929403"}}
{"comment": {"body": "Cool description.\n\ndd stands for descriptive description?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91929575"}}
{"comment": {"body": "It\u2019s only a problem when the operation have a different priority \\(for example \\* and \\+\\) and it might be ambiguous. Once the operations have the same priority it\u2019s ok.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91999306"}}
{"comment": {"body": "You got it :point_right: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-91999378"}}
{"comment": {"body": "It still had problems with the same precedence operators such as \\+.\n\nAnyway, mind adding a parentheses there?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/299/_/diff#comment-92053485"}}
{"title": "Feature/FIN-266 fix code review items from pr1", "number": 3, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/3", "body": "*splitting types_helper to q31_math and q15_math to mitigate #undef\n*optimizing rolling mean routine\nrenaming rolling mean to dc removal\n*fixing regression of dc removal\n\n"}
{"title": "Feature/FIN-276 CI build ARM image", "number": 30, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/30", "body": "Added Jenkinsfile\nupdated script\nAdded Jenkinsfile\nAdded Jenkinsfile\n*updating cmakelist for ARM\ntest commit\nanother test commit\nanother jenkins test\nanother jenkins test\nAdded Jenkinsfile\ncorrected script\nanother test at eclipse build\n*eclipse build\ntesting if jenkins needs a file\ndecided not to add previous commit file\nstarting to add eclipse project to cmake\ndiscarding eclipse build\ncmakelist generation WIP\ncleaning up repo\n\nRevert \"cleaning up repo\"\nThis reverts commit 5885499ec3f40cf161fd082e6a2b9061a3d1cd9e.\n\n\n*adding .project parsing\n\ncontinuing parsing project\nminor fix to eclipse project files\nsaving WIP\nparsing WIP\nalmost done\nlinking issues\ntarget is building at last\nfinal touches\nmaking progress with building libfingerprinting\ntarget built at last\ntelling jenkins to build the project\nmaybe fix jenkins\nforcing python 3.6\n*removing special CMake paths\ntrying building lib again\nreverting previous change\n*fixing paths\ncase is important\nchanging output dir\njekingsfile: splitting to a new test\nfix jenkinsfile\ntrying to output .elf file\ntrying archiving\nmore artifacts\ntesting mail sent after failure\nfix bad jenkisfile\nexpanding mail sent\nfix Jenkinsfile\nfix jenkinsfiles\ntrying jenkisfile again\nreverting failed test\npretiffying and cleaning up PR"}
{"title": "Dynamic memory analysis of library code", "number": 300, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/300", "body": ""}
{"comment": {"body": "Irrelevant question: Why is endianness part of the \u201ctraining configuration\u201d? Maybe we should create a global configuration for generic stuff like anchor count, endianness and other future configurations that don\u2019t necessarily relate to featext/train/classify ?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/300/_/diff#comment-91879296"}}
{"comment": {"body": "Well it\u2019s more \u201cTraining library configuration\u201d rather than \u201ctraining configuration\u201d.\n\nI want the init functions to follow how we supposed to deploy our libraries. In two parts: Init function for the feature extraction library \\(running on the dialog\\) and for the models library that will should include both training and classification \\(running on the ST master\\).", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/300/_/diff#comment-91879937"}}
{"comment": {"body": "Cool!\n\nDid it find errors?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/300/_/diff#comment-91899214"}}
{"comment": {"body": "Nope. Looks good so far.\n\nHowever, valgrind is know to work better with dynamic memory access vs static memory access. But nevertheless, its still to run some verifications.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/300/_/diff#comment-91903544"}}
{"comment": {"body": "There\u2019s a lot of code duplication here with regression tests. This will break easily in the near future.\n\nSee what you can reuse", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/300/_/diff#comment-91904032"}}
{"comment": {"body": "Not very straight forward, there isn\u2019t a way to do it without putting a lot of effort in.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/300/_/diff#comment-91909541"}}
{"title": "Feature/BIS-2259 add NVM and model storage", "number": 301, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/301", "body": "Add NVM driver to ST with patch to driver Start on adding model storage in NVM in ST\nAdd testing agent NVM access with stored features"}
{"title": "Feature/BIS-2153 start implementing hw tests wit", "number": 302, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/302", "body": "test_classification.py tests pass with the current progress.\nChange log:\n\nAdd dialog's software reset and enable software reset in the board instance\nAdd CLI option to run tests with ST configuration ( --with-st )\nAdd NVM driver to ST with patch to driver\nRemove duplicated ITD-084\nRemove redundant board_agent_multi and reuse board_agent (which accepts True parameters if used with ST)"}
{"comment": {"body": "For some reason it passed:\n\n![](https://bitbucket.org/repo/x8eLRbo/images/352647293-image.png)\n  \n\nYet when I try to run system tests on the last commit of this branch \\( `f0dd932d` \\) and on any other child commits on `develop` they fail \\(it seems Dialog ignores the start training event, but I didn't investigate too much the exact cause\\)\n\nI think I will revert this pull request until this issue is fixed, what do you say?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/302/_/diff#comment-92136867"}}
{"title": "CLI utility to run functionality of feature extraction from the command line", "number": 303, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/303", "body": ""}
{"comment": {"body": "purpose?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/303/_/diff#comment-91960097"}}
{"comment": {"body": "To be able to run feature extraction on packet straight from the command line.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/303/_/diff#comment-91960328"}}
{"comment": {"body": "Who\u2019s the producer of those packets in this format?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/303/_/diff#comment-91961268"}}
{"comment": {"body": "Mostly packets that are directly read from dialog boards.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/303/_/diff#comment-91965813"}}
{"comment": {"body": "I\u2019m not really following the purpose and design of this util, but I\u2019ll approve.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/303/_/diff#comment-91982117"}}
{"title": "Feature/BIS-2288 bosch drop doesnt compile easil", "number": 304, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/304", "body": "Update evil words to catch future private folders issue\nRemove private links and references in drop\nAttach make 4.2.1 for Windows to make it easier for Bosch to use the new make (solves hanging -j issue)\nDrop contains the requires files for libfingerprinting"}
{"comment": {"body": "What private things are in .cproject?\n\nOther than `<listOptionValue builtIn=\"false\" value=\"\"${workspace_loc:/${ProjName}/include/private}\"\"/>` I didn\u2019t see anything and there\u2019s no reason to remove it, it doesn\u2019t do anything if the directory doesn\u2019t exist", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/304/_/diff#comment-91989198"}}
{"comment": {"body": "I know, but while I was at it, I preferred to remove references that Bosch didn\u2019t need to know about", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/304/_/diff#comment-91990671"}}
{"title": "Feature/BIS-2114 integrate 10.0.2.60 sdk version", "number": 305, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/305", "body": "Update of SDK to 10.0.2.60\n\nTheres patch for GPIO\nTheres patch for UART baudrates\nTheres hack in fingerprinting_bosch for 921600 baudrate\n\n\n\nNo bosch packet verification"}
{"comment": {"body": "My comment got buried under mountains of diff, here\u2019s a direct link [https://bitbucket.org/levl/bosch\\_integration/pull-requests/305/feature-bis-2114-integrate-100260-sdk/activity#comment-91993394](https://bitbucket.org/levl/bosch_integration/pull-requests/305/feature-bis-2114-integrate-100260-sdk/activity#comment-91993394)", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/305/_/diff#comment-91995083"}}
{"comment": {"body": "Fixed in dacb789c73c3c73f7d1c6e66042039190ec2b7d1", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/305/_/diff#comment-92057069"}}
{"comment": {"body": "I cant follow all the code - is there something special here? something risky?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/305/_/diff#comment-92063122"}}
{"comment": {"body": "SDK update, always risky.\n\nThere\u2019s no bosch packet verification anymore", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/305/_/diff#comment-92065360"}}
{"title": "Update android app", "number": 306, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/306", "body": "Update packets to new Bosch format\nRemove all heating code from app\nNew transmission modes (continuous and sporadic)"}
{"comment": {"body": "This is failing the quick system tests because the testing agent doesn\u2019t expect this new format and filtering fails.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/306/_/diff#comment-92116471"}}
{"comment": {"body": "Fixed it :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/306/_/diff#comment-92421278"}}
{"title": "Fixed Android build issues", "number": 307, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/307", "body": ""}
{"comment": {"body": "I\u2019m honored.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/307/_/diff#comment-92222634"}}
{"title": "Revert \"Feature/BIS-2153 start implementing hw tests wit (pull request #302)\"", "number": 308, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/308", "body": "This reverts pull request #302, it broke system tests"}
{"title": "Fixed android builds failing sometimes", "number": 309, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/309", "body": "They are failing because of failures while downloading gradle packages from google, ive enabled gradle caching to prevent this.\n\nAlso fixed a bug in the SDK to enable it to run on mac OS."}
{"comment": {"body": "Do not patch SDK directly", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/309/_/diff#comment-92266218"}}
{"comment": {"body": "We already do this for S3 cache in `run_in.sh` , copy the S3 code and do something similar ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/309/_/diff#comment-92266726"}}
{"comment": {"body": "\ud83d\udc4c", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/309/_/diff#comment-92272298"}}
{"comment": {"body": "Our hero! :v: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/309/_/diff#comment-92272970"}}
{"title": "FIN-345 run system tests automatically o", "number": 31, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/31", "body": "\n\nModified jenkins file to work with multiple slaves\nFixes to jenkinsfile\nAdded binary artifacts to jenkins stash\nAdded arm binary to jenkins stash\nFixed jenkins file.\nFixes\nFixes to jenkins file\nFixed for jenkins file\nAdded stage to burn the output to the dialog board\nEnabled parallel builds, and tried to fix on board testing stage\nTried to fix burn on board\nTried to fix burn on board\nTried to fix burn to board stage\nHopefully last fix for burining on board\nEnabled parallel test running\nStarted adding compilation and installation of android app to the jenkins pipeline.\nFix\nFix\nFixes for android app pipeline integration\nNow system tests will only run on pullrequests to / from develop"}
{"title": "Update recording agent to output data in testbus 3 format", "number": 310, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/310", "body": "Output the entire data of the testbus.\nDo not rotate packet in place on HW\nFixed bug for trimming demodulated data by 2 bytes"}
{"comment": {"body": "How did the code work until now? Does this change not break anything else?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/310/_/diff#comment-92271526"}}
{"comment": {"body": "It didn\u2019t work actually. It was trimming our packets by 2 bytes.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/310/_/diff#comment-92271705"}}
{"comment": {"body": "Yeah I got that. Doesn\u2019t the code verify that the demodulated data is the same as the provided data? How did it work if it verifies?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/310/_/diff#comment-92271965"}}
{"comment": {"body": "It\u2019s actually doesn\u2019t.\n\nThe library ignores the input completely and is working only with the data it demodulates from the iq..", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/310/_/diff#comment-92272288"}}
{"comment": {"body": "Alright", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/310/_/diff#comment-92272388"}}
{"title": "Feature/online training tests", "number": 311, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/311", "body": "Implemented the QTD for online training\n\nimplement QT-016\nimplement QT-017\nQT-014, QT-015\nimplemented qt-018,qt-019, added error codes for finalize, removed old test for finalize with uninitialized model\nQT-021\nfix old tests with new error codes. zero init seq even if classify_finalize called on bad inputs\ninit_model func\nfix old test for code coverage"}
{"comment": {"body": "Make sure to also add this in the ctypes wrapper", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/311/_/diff#comment-92300029"}}
{"comment": {"body": "We have other qualification and integration tests implemented in python. Is it more comfortable in C\\+\\+?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/311/_/diff#comment-92300243"}}
{"comment": {"body": "We also have qualification tests in c\\+\\+. It was more comfortable for the online training since I didn\u2019t need real packets for those tests.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/311/_/diff#comment-92314957"}}
{"comment": {"body": "fixed, 66947e88c405274f191917b6628e6eb774bce220", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/311/_/diff#comment-92318985"}}
{"title": "Feature/fix system tests empty pickles", "number": 312, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/312", "body": "Fixed system tests producing empty pickles."}
{"comment": {"body": ":alien: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/312/_/diff#comment-92489711"}}
{"title": "Monitor v2", "number": 313, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/313", "body": "Event types taken from events calsses (instead of numbers)\n\nFor now - supposedly handles online training, infrastructure is laid of multi-anchor (but awaits actual records to finalize)"}
{"title": "Feature/BIS-2855 add cppcheck to the jenkins pip", "number": 314, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/314", "body": "Added cppcheck to the jenkins pipeline"}
{"comment": {"body": ":thumbsup: ", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/314/_/diff#comment-92490006"}}
{"title": "Feature/BIS-2283 implement multi sensor fingerprinting", "number": 315, "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315", "body": "Library now supports multiple-anchors\nboard_instance now allows adding user-defined event handlers\nImported io_infra from Levl repository 5d392b59f8663344d142d65aaacb3d71f7f73b8f\nModified system tests to support multiple dialogs\nLibrary aligned to new EIS\nMoved init_Seq and model_ready_seq to the end of the model struct to more frequently detect old flash model issues\nUpdated pyf to support multianchor, fixed regression and dynamic analysis tests, fixed integration tests, catch tests, Eclipse projects\nAdded qtd tests\nRemoved endiannes from configuration\nFixed default board temperatures in python"}
{"comment": {"body": "Are we only using only rtc\\_now \\(and ignoring rtc\\_timestamp\\)?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92495241"}}
{"comment": {"body": "Maybe a better way would be to put all of this configuration in the init of the voting table?", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92495441"}}
{"comment": {"body": "Yes, the only use we currently have for time is to detect whether a vote is old or not. We can\u2019t use anchor times because they\u2019re out of sync, so we\u2019re forced to use the ST time which is rtc\\_now", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92495799"}}
{"comment": {"body": "I want the voting table to only be a circular buffer that stores the votes, nothing else. So I don\u2019t want it to care about minimums/thresholds and such.\n\nThe classifier is a pure function that receives a voting table as parameter and all the parameters needed to classify it and gives a result. I don\u2019t see a reason to turn it into an entity with a state that needs to be initialized", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92496814"}}
{"comment": {"body": "Ok. Got it.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92497354"}}
{"comment": {"body": "Maybe we can remove the scope code?\n\nI don\u2019t think we will need it anywhere here.", "htmlUrl": "https://bitbucket.org/levl/bosch_integration/pull-requests/315/_/diff#comment-92498530"}}
