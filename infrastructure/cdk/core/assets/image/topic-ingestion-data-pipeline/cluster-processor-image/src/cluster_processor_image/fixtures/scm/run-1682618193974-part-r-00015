{"comment": {"body": "the properties of the object are all optional, so I wasn't sure if it was necessary to be required? I guess we can pass through `{}` if there are no filters? Is that a pattern we follow everywhere else?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#discussion_r1067370266"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244478028", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244480642", "body": ""}
{"comment": {"body": "I'm thinking the client always needs to provide some sort of filter, not sure when we'd call this without one?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#discussion_r1067372063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244484805", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244485979", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244486854", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244489442", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244503700", "body": ""}
{"comment": {"body": "Hmm I think that's a good point. Updated.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#discussion_r1067387369"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244532434", "body": ""}
{"title": "TryNewBert", "number": 4228, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4228", "body": "Try new bert\nUpdate\nTry again"}
{"title": "Fix folder filter", "number": 4229, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4229"}
{"title": "Fix vpn concurrency issue for now", "number": 423, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/423"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/423#pullrequestreview-894289210", "body": ""}
{"title": "Fix post processing", "number": 4230, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4230"}
{"title": "Fixes upload dealloc crash", "number": 4231, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4231", "body": "Confirmed that dealloc occurs immediately after uploaders is set to empty"}
{"comment": {"body": "It's glorious \ud83e\udd29 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4231#issuecomment-1379562564"}}
{"comment": {"body": "\r\n> It's glorious \ud83e\udd29\r\n\r\nAs are you both!!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4231#issuecomment-1379563138"}}
{"title": "Use hevc and aac", "number": 4232, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4232"}
{"title": "trying to set a static version while I work on a fix to add retries", "number": 4233, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4233"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4233#pullrequestreview-1244661741", "body": ""}
{"title": "Cleanup post process lambda", "number": 4234, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4234"}
{"title": "Add logging to getRelatedTopics", "number": 4235, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4235"}
{"title": "Overlay file sourcemark addition/deletion in VSCode", "number": 4236, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236", "body": "Whenever threads are added or archived locally, use the StreamOverlay to overlay a new/deleted thread into the stream.\nFixes UNB-815\nThis does a few things:\n\nWe had a very hacky overlay for this before, which only worked when creating threads (not deleting).  This PR switches this over to use the StreamOverlay, which is what we use elsewhere for overlaying data into streams.\nMove the code that triggers the overlay from the discussion thread UI into ThreadStore, so it will trigger when any thread is added or removed anywhere.\nThis handles deletion correctly by overlaying a thread removal.\n\nThere is still some hackyness: because there is no way to have a push channel for this data, we instead temporarily poll for new sourcemarks.  This is just a local operation though, so it's OK to do."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#pullrequestreview-1262858756", "body": ""}
{"comment": {"body": "Thinking we should throw an error after 20 attempts to unwind the overlay.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#discussion_r1082025799"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#pullrequestreview-1262858940", "body": ""}
{"comment": {"body": "Same. Should unwind after 20 attempts.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#discussion_r1082025932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#pullrequestreview-1262859085", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#pullrequestreview-1264052623", "body": ""}
{"comment": {"body": "The overlay will unwind itself if there's a failure, that's an improvement in this PR.  This code here is to manually refresh the sourcemark listing for this file (which isn't an API call) -- normally overlays rely on push notifications to know when to update, but in this case there is no push overlay so we can't, so we do this manually.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#discussion_r1082861993"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#pullrequestreview-1264084641", "body": ""}
{"comment": {"body": "I noticed that it will unwind if there's an error thrown.\r\n\r\nWhat happens if this runs 20 times and no errors are thrown but the new thread also doesn't show up?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#discussion_r1082884097"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#pullrequestreview-1264085819", "body": ""}
{"comment": {"body": "It will unwind as expected.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4236#discussion_r1082884851"}}
{"title": "Polish video loading states on dashboard", "number": 4237, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4237", "body": "Reduces size of preview image for smaller transfers\n\nUpdate Video Draft viewport based on this: \n\n\nUpdate VideoPreview images to have a fixed size with skeleton UI.\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4237#pullrequestreview-1244719270", "body": ""}
{"title": "reduce cpu request to help with deployments", "number": 4238, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4238", "body": "Trying to help with deployment speed and consistency. We are wasting over 60% of our resources"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4238#pullrequestreview-1244742851", "body": ""}
{"title": "Fix GHE Registration POST", "number": 4239, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4239", "body": "Instead of using fetch POST, we want to use form post so that the browser follows."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4239#pullrequestreview-1244754118", "body": "sweet"}
{"title": "update readme", "number": 424, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/424"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/424#pullrequestreview-894289405", "body": ""}
{"title": "Only return approved topics", "number": 4240, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4240"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4240#pullrequestreview-1244760313", "body": ""}
{"title": "score powerml", "number": 4241, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4241"}
{"title": "Get topics by source", "number": 4242, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4242", "body": "First attempt at fixing returning topics for a file"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4242#pullrequestreview-1244791364", "body": ""}
{"title": "Integrate search API with TopicView", "number": 4243, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243", "body": "No significant UI changes, just replace the API providing the data. The dropdown filtering is now done via the API (instead of clientside filtering):\n\n\n\nRefactored logic from the ThreadSearchStore into its own base store for reuse (kept the old code in the Mine view, can refactor that out separately since it's unrelate to topics work) \nAdded ~InsightSearchStore~ InsightSearchStream to query the new search API"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1244803663", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1246110967", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1246262840", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1246277080", "body": ""}
{"comment": {"body": "Add a test for this case?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#discussion_r1068489379"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1246665695", "body": ""}
{"comment": {"body": "done!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#discussion_r1068727685"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1246691468", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1246721538", "body": ""}
{"comment": {"body": "This could possibly be simplified -- if I'm reading this correctly, this class takes in a stream of queries, and produces a stream of debounced queries.  If so, this could be modelled as a stream operator instead of a class, you can take a look at `StreamOperators` and `ApiStreamOperators` for similar composable stream operators.  Then in the view using this, you'd ultimately do something like:\r\n\r\n```\r\nconst queryStream = createValueStream<SearchInsightsRequest>(defaultQuery);\r\nconst insightSearchStore = useMemo(\r\n  () => new InsightSearchStore({teamId, topicId}, searchQueryStream.stream.compose(SearchDebounceOp__\r\n)\r\n\r\n/// below -- call queryStream.updateValue(...) to set new query value\r\n```\r\n\r\n\r\nYou could _almost_ replace this with the `debounce` operator that ships with xstream except we have special behaviour for the \"no search\" case, which takes effect immediately without debouncing.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#discussion_r1068770712"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1246722448", "body": ""}
{"comment": {"body": "I think this has a race condition, if a new query is specified, and then immediately cleared, the debounced value will \"win\" once the debounce period expires.  I don't have a great solution for this off the top of my head.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#discussion_r1068771583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1246728748", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1246730716", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1250607774", "body": "One comment but otherwise this looks good"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4243#pullrequestreview-1250622569", "body": ""}
{"title": "Responsive 'Add Topic' button in mobile dashboard", "number": 4244, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4244", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4244#pullrequestreview-1244830414", "body": ""}
{"comment": {"body": "Also getting this in -- if there are no recommended topics, don't display the header label", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4244#discussion_r1067607852"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4244#pullrequestreview-1246215493", "body": ""}
{"title": "Make breakpoints consistent", "number": 4245, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4245", "body": "There was a discrepancy between 'large' breakpoint definitions causing some display bugs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4245#pullrequestreview-1244842236", "body": ""}
{"title": "Fix discussion thread header post message", "number": 4246, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4246", "body": "Actions were referencing the wrong postMessage :(\nUnfortunately there's a \"standard\" postMessage API in typescript so we didn't catch the type issue."}
{"comment": {"body": "Maybe we should rename our postMessage so there's not a conflict?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4246#issuecomment-1380752766"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4246#pullrequestreview-1244851121", "body": ""}
{"title": "Fix sort ordering", "number": 4247, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4247"}
{"title": "Add maxHeight to video", "number": 4248, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4248", "body": "Missing max height on video"}
{"comment": {"body": "> Do we need to set a max-width here too?\r\n\r\nI don't think so? The issue with the height was because we defined it in vh, not a percentage. That pushed it outside its parent container.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4248#issuecomment-1380762903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4248#pullrequestreview-1246101301", "body": "Do we need to set a max-width here too?"}
{"title": "Update Browser Icon", "number": 4249, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4249", "body": "Update browser icon in URL Icon"}
{"comment": {"body": "<img width=\"543\" alt=\"CleanShot 2023-01-11 at 22 37 24@2x\" src=\"https://user-images.githubusercontent.com/1553313/211995272-23c66302-d43c-42af-b74b-e8af4df7da4e.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4249#issuecomment-1379877812"}}
{"comment": {"body": "Could maybe use tests mapping the paths/urls to icons?  The values are pretty cryptic (some older ones won't have schemes, etc)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4249#issuecomment-1380756249"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4249#pullrequestreview-1246103873", "body": ""}
{"title": "update", "number": 425, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/425"}
{"title": "Fix Reference Delete", "number": 4250, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4250", "body": "Deleting references were problematic for webFileReferences as filePaths can be identical.\nThis typically occurs when there are empty paths / if we slurped websites at their root path."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4250#pullrequestreview-1246406437", "body": ""}
{"comment": {"body": "nit naming: `areCodeReferencesEqual` ?? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4250#discussion_r1068601182"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4250#pullrequestreview-1246406671", "body": ""}
{"title": "increase timeout", "number": 4251, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4251"}
{"title": "Clean up TopicService", "number": 4252, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4252"}
{"title": "Improve PowerML spatial ordering", "number": 4253, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4253", "body": "Cluster and order scm and slack messages."}
{"title": "Returned topics need to be sorted", "number": 4254, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4254", "body": "gettopics returned from service were not respecting topic score."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4254#pullrequestreview-1246450688", "body": ""}
{"comment": {"body": "There is a larger question whether the store should be returned topics orderd by score.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4254#discussion_r1068636947"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4254#pullrequestreview-1246450903", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4254#pullrequestreview-1246451634", "body": ""}
{"comment": {"body": "Clients can always reorder as necessary", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4254#discussion_r1068637521"}}
{"title": "Remove stanford lib from API service", "number": 4255, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4255"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4255#pullrequestreview-1246586864", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4255#pullrequestreview-1246712228", "body": ""}
{"title": "Allow topic deletion in dashboard topic list UI", "number": 4256, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256", "body": "There were some tricky bits here, specifically because the dropdown header is only displayed when the user is hovering over the associated row, and the button \"background\" only displays when the mouse is over the button.\nI couldn't find a UX for the confirmation dialog -- @benedict-jw is this OK?\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#pullrequestreview-1246650598", "body": ""}
{"comment": {"body": "FYI we can probably remove the locally-generated `displayName` pretty soon, as the PowerML-generated topics are already capitalized.\r\n\r\nAlso.... this sort is caps-sensitive!  I might need to do something smarter for Sorter.sortByProperty to use locale sorting by default.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#discussion_r1068720717"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#pullrequestreview-1254108944", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#pullrequestreview-1254111564", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#pullrequestreview-1254113363", "body": "Lgtm aside from copy question. Side note that this menu/deleting kind of makes it feel like we should be able to edit topics too "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#pullrequestreview-1254154689", "body": ""}
{"comment": {"body": "So there's no delete? Just update to -1?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#discussion_r1073973586"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#pullrequestreview-1254155191", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#pullrequestreview-1254168941", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Correct.  Updating to -1 is effectively a deletion.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4256#discussion_r1073980448"}}
{"title": "Fix sampling", "number": 4257, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4257", "body": "Opt in to 1:1 sample rate."}
{"title": "Up polling interval", "number": 4258, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4258"}
{"title": "Auth provider vends enterprise API clients", "number": 4259, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4259"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4259#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Fix this before checking in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4259#discussion_r1068789318"}}
{"title": "Disable ingesting PRs in dev", "number": 426, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/426", "body": "We've two API services now so we'd be creating dupes. Need to think of a better approach."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/426#pullrequestreview-894317570", "body": ""}
{"title": "Add diagnostics", "number": 4260, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4260"}
{"title": "Remove old topic extraction UI", "number": 4261, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4261", "body": "Since we have a real topic UI now, we don't need this anymore.  Removing this also lets us remove the API it uses."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4261#pullrequestreview-1246746680", "body": ""}
{"title": "Index file path components", "number": 4262, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4262", "body": "Fixes an issue where partial matches on strings containing /s don't return search results. \nThe fix is to split such strings and append the result to the string to be indexed."}
{"title": "Revert \"Drop ThreadTopicModel and PullRequestTopicModel tables (#4163)\"", "number": 4263, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4263", "body": "This reverts commit 2a589dddc4d39c8a7eb7a4c58b6a1dc8e5a4888a."}
{"title": "[BREAKS API ON MAIN] Remove getRecommendedTopics operation", "number": 4264, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4264", "body": "Deprecated operation that's no longer used by the client."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4264#pullrequestreview-1246796499", "body": ""}
{"title": "Vend enterprise version of GitHub User client", "number": 4265, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4265"}
{"title": "V0.1 Manifest flow style", "number": 4266, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266", "body": "First parts of manifest flow styled\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#pullrequestreview-1246827242", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#pullrequestreview-1246886899", "body": ""}
{"comment": {"body": "@richiebres What's this change about?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#discussion_r1068913514"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#pullrequestreview-1246887364", "body": ""}
{"comment": {"body": "You were showing the **inputUrl**, which has not been sanitized.\n\nInstead show the sanitized url returned from the server.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#discussion_r1068913995"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#pullrequestreview-1246887819", "body": ""}
{"comment": {"body": "Ahh okay. Makes sense. The displayName variable is a little confusing as I expected that to be an actual name (e.g. Nike), not a url.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#discussion_r1068914436"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#pullrequestreview-**********", "body": ""}
{"comment": {"body": "```json\r\n[{\r\n    \"inputUrl\": \"https://ghe.secops.getunblocked.com/avatars/u/4?s=200\",\r\n    \"status\": \"ok\",\r\n    \"unregisteredProvider\": {\r\n        \"displayName\": \"ghe.secops.getunblocked.com\",\r\n        \"provider\": \"githubEnterprise\",\r\n        \"manifestUrl\": \"https://ghe.secops.getunblocked.com/organizations/:ORG_NAME/settings/apps/new?state=6bdab716-52de-480a-9128-945e8e88eedd\",\r\n        \"manifestBody\": \"{\\\"name\\\":\\\"Unblocked DEV\\\",\\\"url\\\":\\\"https://dev.getunblocked.com\\\",\\\"description\\\":\\\"Unblocked helps development teams surface and create institutional knowledge in their codebase.\\\",\\\"redirect_url\\\":\\\"https://1a1a-216-232-204-144.ngrok.io/integrations/appCreate\\\",\\\"setup_url\\\":\\\"https://dev.getunblocked.com/dashboard/install/redirect\\\",\\\"callback_urls\\\":[\\\"https://dev.getunblocked.com/dashboard/login/exchange\\\"],\\\"hook_attributes\\\":{\\\"url\\\":\\\"https://dev.getunblocked.com/api/hooks/githubEnterprise\\\",\\\"active\\\":true},\\\"public\\\":false,\\\"default_permissions\\\":{\\\"emails\\\":\\\"read\\\",\\\"issues\\\":\\\"read\\\",\\\"members\\\":\\\"read\\\",\\\"metadata\\\":\\\"read\\\",\\\"pull_requests\\\":\\\"write\\\"},\\\"default_events\\\":[\\\"issue_comment\\\",\\\"membership\\\",\\\"meta\\\",\\\"organization\\\",\\\"pull_request\\\",\\\"pull_request_review\\\",\\\"pull_request_review_comment\\\",\\\"pull_request_review_thread\\\",\\\"repository\\\"]}\"\r\n    }\r\n}]\r\n```\r\n\r\nThis is an example response.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#discussion_r1068914561"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#pullrequestreview-1248101635", "body": ""}
{"comment": {"body": "FWIW we do need to properly fix this -- the warnings are telling us about actual bugs that we could very well hit at some point.  Not sure how urgent that is.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4266#discussion_r1069737574"}}
{"title": "Update spinner", "number": 4267, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4267", "body": "Adding spinner to manifest page"}
{"title": "Update Github Enteprise error text to match designs", "number": 4268, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4268", "body": "Design\n\nExample\n"}
{"title": "GitHub Enterprise Apps are public now", "number": 4269, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4269", "body": "Manifest Apps created in GHE instances are now eligible to be installed on any GitHub Org on the private-cloud instance."}
{"title": "Add timeout for all github jobs", "number": 427, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/427", "body": "Github actions do not have a global timeout.\nSo we set it per job"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/427#pullrequestreview-894314945", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/427#pullrequestreview-894315105", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/427#pullrequestreview-894315208", "body": ""}
{"title": "Show/hide topic tags under feature flag", "number": 4270, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4270"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4270#pullrequestreview-1247936445", "body": ""}
{"title": "Slack webhooks need to handle workspaces shared across teams", "number": 4271, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4271"}
{"title": "SearchV2 filtering by author should be an OR operation (not AND)", "number": 4272, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4272"}
{"title": "Fix video upload crash take 2", "number": 4273, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4273", "body": "The underlying cause for the crash was asynchronous access on an array. Should have spotted this earlier. Moved those shared properties to an actor.\nThere was another crash that happened due to early deallocations, which I think was due to a swift bug with incorrectly tagged mutable arrays. \nAnd finally I spotted early deallocations when the uploader instances weren't captured in properties and instead relied on @escaping closure capture. There seems to be some weird swift behaviour there too"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4273#pullrequestreview-1248427154", "body": "Man I hate swift :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4273#pullrequestreview-1248669651", "body": ""}
{"comment": {"body": "I also saw early deallocations, so I'm keeping this hack here. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4273#discussion_r1070167735"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4273#pullrequestreview-1248670951", "body": ""}
{"comment": {"body": "`lazy` evidently fixes incorrectly tagged mutable arrays", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4273#discussion_r1070168801"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4273#pullrequestreview-1248671459", "body": ""}
{"title": "Hack to filter duplicate users out of the tooltips", "number": 4274, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4274", "body": "Right now this just filters out anyone with any of the same names.\nprivate.yml:\n\nSourceMarkEngine.ts\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4274#pullrequestreview-1248480803", "body": ""}
{"title": "Updated Login routes to include HomeHeader", "number": 4275, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275", "body": "Refactored parts of HomeHeader to HomeHeaderContainer.\nApplied HomeHeaderContainer to login pages\n\n"}
{"comment": {"body": "Double primary button @benedict-jw ! ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#issuecomment-**********"}}
{"comment": {"body": "@jeffrey-ng can we offset the vertical alignment of the sign-in page? I think it needs to move up since. I know it's probably centered, but optically it feels like it's sitting low.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#issuecomment-**********"}}
{"comment": {"body": "> Double primary button @benedict-jw !\r\n\r\nI think it's okay with two buttons. They stand out as options. When we start supporting additional SCM providers we can reevaluate. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#issuecomment-**********"}}
{"comment": {"body": "@benedict-jw Updated from 20vh to 10vh of padding.\r\n\r\nHere it is again on 15inch MBP\r\n![CleanShot 2023-01-17 at 15 32 11@2x](https://user-images.githubusercontent.com/1553313/213035229-cb2a8452-dff3-4c37-8b6c-f0f68ee17684.png)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#issuecomment-**********"}}
{"comment": {"body": "15vh\r\n![CleanShot 2023-01-17 at 15 33 00@2x](https://user-images.githubusercontent.com/1553313/213035321-99696f56-1e9c-4e12-ad68-4abf73102343.png)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1260964912", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1260966483", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262279016", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262279210", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262284954", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262300214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262317824", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262318600", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262319653", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262321644", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262327376", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262329428", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4275#pullrequestreview-1262330749", "body": ""}
{"title": "Add Contributor to walkthrough", "number": 4276, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4276", "body": "We were not adding contributors to the actual thread...\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4276#pullrequestreview-1248494794", "body": ""}
{"title": "Search", "number": 4277, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4277", "body": "This introduces the search notebook with some embedding, search, summarization, prompting examples for topics/systems/people/experts."}
{"comment": {"body": "@rasharab : can you unblock these notebooks into main? They are mostly for your benefit. Going to see if I can answer your questions for PowerML in another branch/diff/or.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4277#issuecomment-1396378684"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4277#pullrequestreview-1261057437", "body": ""}
{"title": "Fix message layout breakpoint", "number": 4278, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4278"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4278#pullrequestreview-1248684848", "body": "PREWEEKEND HIONOR"}
{"title": "Better hack for deducing users", "number": 4279, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4279", "body": "As per @kaych 's great ideas"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4279#pullrequestreview-1248616037", "body": ""}
{"title": "update", "number": 428, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/428"}
{"title": "Scroll icon stack and fix maxIcons logic", "number": 4280, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4280", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4280#pullrequestreview-1250595566", "body": ""}
{"title": "Add Custom Icons", "number": 4281, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281", "body": "Add Linear, Notion & Unblocked icons for code references\nAlso added yml to list of file icons.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#pullrequestreview-1252125678", "body": ""}
{"comment": {"body": "We decided not to ship these in our FA bundle?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#discussion_r1072563150"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#pullrequestreview-1252128699", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#pullrequestreview-1252130742", "body": ""}
{"comment": {"body": "This may pick up some unexpected icons -- slack? notes?  We may want to extract the icons we actually intend to use in branding contexts out from the general set of custom icons...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#discussion_r1072569652"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#pullrequestreview-1252130978", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#pullrequestreview-1252274525", "body": ""}
{"comment": {"body": "Unable to. Custom kits / icons in FA only support web clients.\r\n\r\nhttps://chapter2global.slack.com/archives/C030RHZ0YV8/p1673649835043989?thread_ts=1673637286.139169&cid=C030RHZ0YV8", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#discussion_r1072695594"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#pullrequestreview-1252288819", "body": ""}
{"comment": {"body": "Updated so that `CustomIcons` only includes a subset.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4281#discussion_r1072700923"}}
{"title": "Transform slack bullet points to preserve formatting", "number": 4282, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4282", "body": "Slack has its own markup language. Converting it to proper markdown is too heavy of a lift right now, but we can do something simple to preserve bullet point formatting in ingested slack threads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4282#pullrequestreview-**********", "body": ""}
{"title": "Determine enterprise provider endpoint from state", "number": 4283, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4283"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4283#pullrequestreview-**********", "body": ""}
{"title": "Use SearchInsightService for getTopicRelatedInsights", "number": 4284, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4284", "body": "This will allow returning insights by recency in descending order so that the newest insights appear at the top."}
{"title": "Revert \"Use SearchInsightService for getTopicRelatedInsights (#4284)\"", "number": 4285, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4285", "body": "This reverts commit ab316e8bd33c7ee183346100358c799726c0572d."}
{"title": "Dont index archived threads", "number": 4286, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4286", "body": "Just for now so that they don't appear in the Topics overview page"}
{"title": "VScode enterprise auth", "number": 4287, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287", "body": "Support VSCode GHE.\nUpdated LoginManifest with Basic ComboBox implementation to allow multiple repos.\n"}
{"comment": {"body": "Current Flow:\r\n\r\nVSCode calls PreAuth + GetLoginOptions. This generates all available providers and for non-registered Enterprise providers, will encode repoURLs & preauth token as query parameters into a auth URL which launches browser manifest flow.\r\n\r\nManifest flow will call findEnterpriseUrls with these repoURLs and preauth token which returns a list of enterprise providers which the user may select from (or ignore and enter a custom value). From this point, standard Enterprise flow (create app + user auth)\r\n\r\nWhile this is all happening, VSCode is polling exchange auth with preauthToken which should return a token + refresh token once user auth is complete.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-1250699968", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-1250707569", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-1250737168", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-1260939782", "body": ""}
{"comment": {"body": "So this is the 'unmapped' set of raw vscode workspaces, presumably so we can use them to determine which git servers we want to connect to?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#discussion_r1080675857"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-1260961190", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-1260961944", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#pullrequestreview-1261008539", "body": ""}
{"comment": {"body": "Yup. This data is sent off to manifest flow to prefill the inputs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4287#discussion_r1080725236"}}
{"title": "Include archived threads when triggering reindexing", "number": 4288, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4288"}
{"title": "Map insight to topics after creating or editing", "number": 4289, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4289"}
{"title": "Utility to get test resource", "number": 429, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/429", "body": "refactor"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/429#pullrequestreview-894579401", "body": ""}
{"title": "Send search index event on thread archive or restoration", "number": 4290, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4290"}
{"title": "findEnterpriseProvider now returns error message and org name if possible", "number": 4291, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#pullrequestreview-**********", "body": ""}
{"comment": {"body": "So no more hard-coded error based on status?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#discussion_r1071517923"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#pullrequestreview-**********", "body": ""}
{"comment": {"body": "yup, I changed in client", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#discussion_r1071529683"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#pullrequestreview-1250594525", "body": ""}
{"comment": {"body": "@jeffrey-ng fyi: pre-filling org name on the second page if available", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#discussion_r1071529926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#pullrequestreview-1250605607", "body": ""}
{"comment": {"body": "\ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4291#discussion_r1071537989"}}
{"title": "Send topic insight mapping event after insight indexing", "number": 4292, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4292", "body": "The handler just logs for now. Once I've confirmed its all wired up correctly, I'll move the logic from the search service to the topic service."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4292#pullrequestreview-1250578002", "body": ""}
{"title": "Allowlist GitHub Enterprise callback URLs", "number": 4293, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4293", "body": "To support local client development against DEV backend stack.\nThis change only applies to DEV."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4293#pullrequestreview-1250668422", "body": ""}
{"comment": {"body": "Cannot use a localhost URL as the webhook URL.\n\nJust sending the traffic to DEV instead. Webhook pipeline in DEV should be smart enough to reject hook events sent from GitHub Apps that DEV does not recognize. So, this will created a little bit of noise, but does not harm DEV stack.\n\nOverride with an ngrok endpoint in your personal stack to route to your personal webhook service.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4293#discussion_r1071582676"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4293#pullrequestreview-1250674660", "body": ""}
{"comment": {"body": "This is beauitful", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4293#discussion_r1071587402"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4293#pullrequestreview-1250674717", "body": ""}
{"title": "Move topic insight mapping logic to topic service", "number": 4294, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4294"}
{"comment": {"body": "Dude, are you trying to make yourself even more amazing than you already are. Seriously...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4294#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4294#pullrequestreview-**********", "body": ""}
{"title": "Make TopicInsightMappingEventPayload serializable", "number": 4295, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4295"}
{"title": "Login Options V3", "number": 4296, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4296", "body": "Update LoginOptions to include AvailableEnterpriseProvider\nAdds dashboard URL to manifest creation page to prevent hard-coded links in VSCode + hub."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4296#pullrequestreview-**********", "body": ""}
{"comment": {"body": "What's an example of this URL?\r\n```\r\nhttps://dev.getunblocked.com/dashboard/login/manifest?provider=githubEnterprise\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4296#discussion_r1071615370"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4296#pullrequestreview-**********", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/4287/files#diff-45e84db70af3edb644d7366bdce20e978044184421e573cb44f835adb72b9456R25", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4296#discussion_r1071615910"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4296#pullrequestreview-**********", "body": ""}
{"title": "Extend the auth exchange window from 10m to 1h to allow for GHE", "number": 4297, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4297", "body": "GitHub Enterprise App creation will take more time than a simple OAuth dance,\nso we're extending the expiry to accomodate.\nRisk is that an potential attacker who used another exploit to steal an exchange\ntoken now has 50 more minutes to pull off the attack.\nFuture alternatives\n\nSetup client-request-signing using asymmetric key-pair exchange before pre-auth,\n  so that the server can trust the origin of the request.\nMake the exchange token single-use. This would allow us to detect (but not prevent)\n  token stealing. Downside is that this is not resilient to dropped network responses.\n  So this is not a great option."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4297#pullrequestreview-1250711624", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4297#pullrequestreview-1250712084", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4297#pullrequestreview-1250716524", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4297#pullrequestreview-1250716916", "body": ""}
{"title": "Refactor search query with debouncer", "number": 4298, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4298"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4298#pullrequestreview-1250746122", "body": ""}
{"title": "Support for GitHub Enterprise hooks", "number": 4299, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4299", "body": "GitHub Enterprise hooks are exactly the same shape as GitHub Cloud hooks.\nWe route the GHE hooks to the exact same webhook-service endpoint as GitHub Cloud (/api/hooks/github).\nThe only difference is that we now validate the source of the hook using the\nGitHub App ID (from the X-GitHub-Hook-Installation-Target-ID request header)\nagainst known cloud (configured) and server (persisted) GitHub Apps.\nIf the source of the hook is unknown, then we throw it away."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4299#pullrequestreview-1250764337", "body": ""}
{"comment": {"body": "TODO needs new test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4299#discussion_r1071654150"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4299#pullrequestreview-1250895944", "body": ""}
{"comment": {"body": "Testing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4299#discussion_r1071750858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4299#pullrequestreview-1250981150", "body": ""}
{"comment": {"body": "Test 2", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4299#discussion_r1071807665"}}
{"title": "Make api a gradle project", "number": 43, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/43", "body": "We are now moving api package to being a gradle project.\nWe are defining the private spec api generator as an openapi task.\nWe also fix up makefile to simplify code gen."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/43#pullrequestreview-854738189", "body": ""}
{"title": "User-specific local config overrides", "number": 430, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/430", "body": "To use, create a new file with your $USER here:\ntouch apiservice/src/main/resources/config/local/$USER.conf"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/430#pullrequestreview-894538524", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/430#pullrequestreview-894538685", "body": "Minor comment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/430#pullrequestreview-894564874", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/430#pullrequestreview-894565395", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/430#pullrequestreview-894568775", "body": ""}
{"title": "Hub Enterprise Auth", "number": 4300, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300", "body": "Supports Enterprise Auth from Hub.\nUpdated so that login items reflect provided login options. Still partially hardcoded due to UI (temporary as there will be a large revamp with new hub)\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-1250755057", "body": ""}
{"comment": {"body": "Should we have loading states here? Kept loading states simple for now as this should be temporary but should think about error states.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1071646847"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-1252082086", "body": ""}
{"comment": {"body": "Was done in a similar pattern as AuthStore.\r\n\r\nIMO, when v2 of hub app is a priority, auth logic should be revisited. Error states aren't handled gracefully atm with the LoginView.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072512382"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-1252083675", "body": ""}
{"comment": {"body": "Can be refactored to include repoURLs in future when/if the hub has access to local repo URLs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072513347"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-1252149381", "body": ""}
{"comment": {"body": "\"Handled gracefully\" is being a bit charitable. I don't think error states are really handled at all - we just bounce back to the login view", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072591538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-1252159245", "body": ""}
{"comment": {"body": "We definitely need to show the spinner when login is in progress, but a failed login attempt should result in an error state that allows the user to retry. If we allow for a UX dead end where the only recourse is an app restart, then we need to refactor this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072603136"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is UX dependent though?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072604176"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I'd just ditch this preview provider unless it was helpful during development", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072605511"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Updated to always retry loading.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072675393"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Yup. We'll scale this up when we add real support for the other providers.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072676045"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Yeah. Status quo with what we have.\r\nIt's graceful since it's not a dead end \ud83d\ude2c ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072676623"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Not sure this is the right construct. This will literally run it over and over again I think? I wonder if `infiniteRetry` would be better?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072819709"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Spoke in person. Forever is still preferable.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#discussion_r1072835916"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-1264056659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-1264098840", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4300#pullrequestreview-1264099274", "body": ""}
{"title": "Add commit/thread IDs to insight search", "number": 4301, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4301", "body": "This will be used in the explorer insights panel, to allow searching for insights within a particular file.  The \"file\" is represented by the set of commits (PRs) and threads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4301#pullrequestreview-1250760088", "body": ""}
{"title": "Fix webhook validation typo", "number": 4302, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4302"}
{"title": "add lambda function for video asset transcoding", "number": 4303, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4303", "body": "Elastic Transcoder does not have any CloudFormation resources. This means we can't automate the creation of pipelines and presets so they need to be created via AWS console as described here \n\nAdded build config to support multiple jobs and pipelines if needed \nAdded stack to create IAM role, Lambda and S3 Lambda trigger for transcoding jobs \nModified Lambda construct to support pre-made roles \nUpdated Dev and prod configs with IDs and configuration for each environment's respective pipeline\n\nI have tested it in Dev and it works fine. We need to decide whether to remove input_suffix or make a change to asset service ?\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4303#pullrequestreview-1251437947", "body": ""}
{"comment": {"body": "Right now we transcode files ending with this suffix. I can set it to an empty string and that would target any video files that have not been generated by our pipeline. \r\n\r\nMy preferred approach would be to modify asset service and set the correct suffix for the raw video. Makes our future lives easier. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4303#discussion_r1072130151"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4303#pullrequestreview-1252425103", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4303#pullrequestreview-1252440572", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4303#pullrequestreview-1252443120", "body": ""}
{"comment": {"body": "Agreed. Lambdas are impossible to debug.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4303#discussion_r1072806808"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4303#pullrequestreview-1252443194", "body": ""}
{"title": "Implement insight search with ID filter", "number": 4304, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4304"}
{"title": "[BREAKS API ON MAIN] Separate endpoint for GHE hooks", "number": 4305, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4305", "body": "Hook events sourced from GitHub Cloud are allowed only if their IP source matches\nGitHub's published hook IPs.\nAs a result we need to separate the GHE hooks, since they can come from anywhere."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4305#pullrequestreview-1252214945", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4305#pullrequestreview-1252216742", "body": ""}
{"title": "Fix null filemark line range in emails", "number": 4306, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4306", "body": ""}
{"title": "[Dashboard] Add team member view", "number": 4307, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307", "body": "First cut of a team member view in the dashboard:\n\n\nRefactored out a bit of the topic view components for reuse here and in the search views\nMissing topics and related team members in this user's network (API work needed for this)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252492093", "body": ""}
{"comment": {"body": "Was the intent that this is generic? It looks like this is only used for topicId?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#discussion_r1072851346"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252493717", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252493722", "body": ""}
{"comment": {"body": "Yeah it's also used in the TeamMemberView (i.e. the uuid is a teamMemberId)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#discussion_r1072853462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252495386", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252498873", "body": ""}
{"comment": {"body": "Nevermind. Just noticed the teamMember version.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#discussion_r1072857730"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252500359", "body": ""}
{"comment": {"body": "nit: try catch ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#discussion_r1072858786"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252501849", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252503074", "body": ""}
{"comment": {"body": "there is a try/catch inside the `getInsightCountsByFilterType` fn ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#discussion_r1072860587"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252503494", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1252506054", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1254157094", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1254158374", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1254159251", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1254162400", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1254181550", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1254191314", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4307#pullrequestreview-1254249635", "body": ""}
{"title": "use s3 construct", "number": 4308, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4308"}
{"title": "Rename TopicInsightMappingEventService function", "number": 4309, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4309"}
{"title": "Use KotlinLogging", "number": 431, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431", "body": "Changes\n\n\nMove toward a more \"event-based\" logger, where varying properies are confined to MDC\n  and the message are simply events. This allows us to create nice property based filters\n  in Logz.io, rather than parsing strings.\n\n\nDropping the Ktor HttpClient loggers, which are totally useless in a distributed\n   system. For example, look at the data from this Logz.io query:\n   \n\n\nDrop Exposed logger for everything except tests. Can be enabled locally with the\n   enableSqlLogging config instead.\n\n\nColoured logging.\n\n\nPatterns\n\n\nLogger construction; KotlinLogging has a very simple one-liner:\nprivate val logger: KLogger = KotlinLogging.logger {}\n\n\nRegular usage:\nlogger.debug(\"message\")\n   logger.info(\"message\")\n   logger.error(\"message\")\n\n\nLazy eval; most useful for debug messages that may be disabled in PROD, or\n  for message payloads that are expensive to compute:\nlogger.debug { \"lazy evaluated $hello message\" }\n\n\nExceptions; pass to logger:\nlogger.error(exception) { \"something went wrong\" }\n\n\nMore info:\n"}
{"comment": {"body": "Coloured output locally:\r\n\r\n<img width=\"943\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/156065271-5d372935-a141-4544-8501-09080a280864.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#issuecomment-1054703056"}}
{"comment": {"body": "> Coloured output locally:\r\n> \r\n> <img alt=\"image\" width=\"943\" src=\"https://user-images.githubusercontent.com/1798345/156065271-5d372935-a141-4544-8501-09080a280864.png\">\r\n\r\nIf those are the defaults, they're gross. Makes me squint hard. Maybe have to see it in terminal...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#issuecomment-1054711866"}}
{"comment": {"body": "> > Coloured output locally:\r\n> \r\n> If those are the defaults, they're gross. Makes me squint hard. Maybe have to see it in terminal...\r\n\r\nYeah, agree, I looked into changing. It's possible but it's a shit tonne of work because you have to overwrite the classes responsible for `%highlight`. Really hard to justify the investment right now. Here's the link for future reference: https://logback.qos.ch/manual/layouts.html#customConversionSpecifier\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#issuecomment-1054718448"}}
{"comment": {"body": "I will say this. I never knew that Richie had such a love got coloured output. It's given me a new perspective as to how His minds works", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#issuecomment-1054797183"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#pullrequestreview-895639750", "body": ""}
{"comment": {"body": "Would be nice if it was:\r\n`http.req.headers.if-modified-since` or something similar", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#discussion_r816283470"}}
{"comment": {"body": "agreed ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#discussion_r816287472"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#pullrequestreview-895642287", "body": ""}
{"comment": {"body": "- no need for `hostname` or `user` locally.\r\n- `thread` is usually not useful in Kotlin - can add back in if you think otherwise.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#discussion_r816285206"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#pullrequestreview-895658729", "body": ""}
{"comment": {"body": "fixed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/431#discussion_r816297365"}}
{"title": "Asset bucket changes", "number": 4310, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4310"}
{"title": "update iam permissions for asset service", "number": 4311, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4311"}
{"title": "Update highlighted styling for transcription", "number": 4312, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4312", "body": "Fix transcription segment highlighting in web\n"}
{"comment": {"body": "cc @benedict-jw @pwerry ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4312#issuecomment-1386049502"}}
{"comment": {"body": "@jeffrey-ng hard to tell from the screenshot but does the mouse cursor change as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4312#issuecomment-1386078647"}}
{"comment": {"body": "> @jeffrey-ng hard to tell from the screenshot but does the mouse cursor change as well?\r\n\r\nUpdated cursor to pointer.\r\nAlso highlights on hover to the same colour.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4312#issuecomment-1386105911"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4312#pullrequestreview-1262306092", "body": ""}
{"title": "Add search bar to hub", "number": 4313, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313", "body": "Hidden behind a default: EnableSearch\nSets up search bar component and system hotkey. \nDoesn't actually do any searching yet."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#pullrequestreview-1252504760", "body": ""}
{"comment": {"body": "This is where the search results go", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#discussion_r1072861763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#pullrequestreview-1252569663", "body": ""}
{"comment": {"body": "Wouldn't this need to live within the `if`? ObservableObject comes from Combine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#discussion_r1072907482"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#pullrequestreview-1252571913", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#pullrequestreview-1252581386", "body": ""}
{"comment": {"body": "Not following on why this is necessary.\r\n\r\nIn what situations would we want this UI to be in search mode on appear? Keyboard events should still work as this view appears and immediately, the onChange would trigger searchMode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#discussion_r1072916046"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#pullrequestreview-1252581894", "body": ""}
{"comment": {"body": "oops yes you're right", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#discussion_r1072916413"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#pullrequestreview-1252585408", "body": ""}
{"comment": {"body": "The bug is that `onChange` will not receive changes to `searchMode` until `keyboardShortcutViewModel` is accessed at least once. The cause is unclear to me but the workaround is to just interrogate the published value `onAppear`, and then `onChange` works forever after that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#discussion_r1072918943"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4313#pullrequestreview-1252605076", "body": ""}
{"title": "Trigger topic insight mapping after topic is updated or created", "number": 4314, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4314"}
{"title": "Assset videos should have extensions", "number": 4315, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4315", "body": "update video\nUpdate"}
{"title": "Keep reference to enterprise ID", "number": 4316, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4316", "body": "If a user has created / connected to an enterprise provider, populate getLoginProviders with enterpriseID on subsequent logins."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4316#pullrequestreview-**********", "body": ""}
{"title": "update templates", "number": 4317, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4317"}
{"title": "Display search results", "number": 4318, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4318", "body": "Focusing down to the result list isn't implemented yet. The full search implementation will use autocomplete tokens, which will necessitate tabbing between views as well. This may be pretty complicated to implement in SwiftUI...\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4318#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This list will *not* be sorted correctly if there's > 1 team.\r\n\r\nEither we have some sort of relevancy score that we can sort against or we need some UX that groups the results by the team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4318#discussion_r1081645504"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4318#pullrequestreview-1262403524", "body": ""}
{"comment": {"body": "Search results will still be relevancy sorted within their team \"block\". For now I'm ignoring this issue because it's not super relevant for 99.9% of users. @benedict-jw does this feel right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4318#discussion_r1081680878"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4318#pullrequestreview-1262406846", "body": ""}
{"comment": {"body": "There's a race possible here that I'm going to fix. We need to interrupt previous searches from completing when a new one is triggered.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4318#discussion_r1081682866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4318#pullrequestreview-1279741297", "body": ""}
{"comment": {"body": "Yes grouping in their team block seems reasonable to me. Eventually I think we'll head in a direction where we'll have a team picker, so you'll only see results by team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4318#discussion_r1093647832"}}
{"title": "Fix IP rule for webhooks", "number": 4319, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4319", "body": "We only want to restrict traffic to the /github endpoint.\nTraffic to the /githubEnterprise endpoint cannot be restricted."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4319#pullrequestreview-1252585965", "body": ""}
{"title": "Add channel token endpoint for video chats. Implementation to follow", "number": 432, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#pullrequestreview-895689598", "body": "naming changes. feel free to push back"}
{"comment": {"body": "what's a channel? is that a DB resource?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#discussion_r816321275"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#pullrequestreview-895700147", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#pullrequestreview-895701092", "body": ""}
{"comment": {"body": "The concept of a channel is abstract - it represents a \"room\" that participants connect to. Its semantics are provider dependent. In this case the API is simply generating tokens that the client can use to connect to Agora channels", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#discussion_r816329379"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#pullrequestreview-895703065", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#pullrequestreview-895703987", "body": ""}
{"comment": {"body": "I see, so a channel ID is opaque, but could be a thread ID under the hood if started from a thread.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#discussion_r816331653"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#pullrequestreview-895705247", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#pullrequestreview-895705840", "body": ""}
{"comment": {"body": "absolutely - but more likely a `messageId`, or `prId` since videos are bound to messages and pull requests", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#discussion_r816333235"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/432#pullrequestreview-895714333", "body": ""}
{"title": "update buckets", "number": 4320, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4320"}
{"title": "Fix HMAC valdiation of GHE webhooks", "number": 4321, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4321", "body": "Forgot to pass the prefix."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4321#pullrequestreview-1252630205", "body": ""}
{"title": "Take counts out of the topic store", "number": 4322, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4322"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4322#pullrequestreview-1252668199", "body": ""}
{"title": "Fix build", "number": 4323, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4323"}
{"title": "FixExtensions", "number": 4324, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4324", "body": "Fix extensions\nTry again\nTry again"}
{"title": "Transition to old buckets", "number": 4325, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4325"}
{"title": "Add filter input to VSCode Explorer Insights panel", "number": 4326, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326", "body": "This is implemented using a server API, so that we get the same stemming and search capabilities as we have in the service.\nThe way this is implemented is that I create four different streams:\n1) File-scoped, no filter\n2) View-scoped, no filter\n3) File-scoped, with filter\n4) View-scoped, with filter\nand we switch between the streams as the filter state changes.  This seems to work OK and let us compose the streams.\nThe layout here is a little bit funny, the item alignment is now more-obviously off because the new show/hide button makes the right-alignment more obvious.  I'll look into this, but since the scrollbar can throw the alignment off there's not a ton we can do here.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-1260854852", "body": ""}
{"comment": {"body": "I refactored this to behave like `useState`, where the returned value is a `[value, setFn]` tuple.  It seems like a more idiomatic hook API, and allowed more flexibility.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#discussion_r1080614439"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is the `UnfilteredStream` extracted from ExplorerInsightsWebviewProvider above, unchanged.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#discussion_r1080615797"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is the guts of the new filtering", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#discussion_r1080616210"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-1260915560", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4326#pullrequestreview-1260935077", "body": ""}
{"title": "fix the suffix and remove s3.head_object call", "number": 4327, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4327", "body": "Removed S3 head object calls. We don't need them anymore. I'll add filters for events later on.\nModified video-transcoding config \n\nThe issue for not having jobs processed was caused by the manually created pipeline pointing at the wrong bucket (blob assets). I had to edit it through AWS console. We are already paying the price for one manual config. \nAlso there was a mistake in the IAM policy for prod where it was referencing Dev account ID. fixed that and I can now see my test assets being processed."}
{"comment": {"body": "I have already deployed these changes to both Dev and Prod and validated the functionality in both environments", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4327#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4327#pullrequestreview-**********", "body": ""}
{"title": "add default runtime seccompProfile", "number": 4328, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4328", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4328#pullrequestreview-**********", "body": ""}
{"title": "Pod security non root user", "number": 4329, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4329", "body": "This could be a breaking change. I will watch things closely and revert if needed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4329#pullrequestreview-1260850861", "body": ""}
{"title": "Initial SourceMark Calculator", "number": 433, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433", "body": "This has miles to go, so don't dig too deep into the algorithm.\nNext step is testing, which will tease out issues."}
{"comment": {"body": "How are we testing this stuff?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#issuecomment-1055661922"}}
{"comment": {"body": "> How are we testing this stuff?\r\n\r\nThat's what I'm working on right now, but I want to merge that separately otherwise this is going to be a mess. There are going to be two types of tests:\r\n1. first, hopefully today, unit tests based on fixtures setup with raw Git output\r\n2. then, integration tests based on real fixtures running on real repo from docker. porting work from here https://github.com/NextChapterSoftware/OctoberDemo/pull/299", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#issuecomment-1055666464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#pullrequestreview-895679121", "body": ""}
{"comment": {"body": "This was a nightmare to figure out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#discussion_r816312595"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#pullrequestreview-895692974", "body": ""}
{"comment": {"body": "This pattern sucks. If `SourcePoint` was a data class, then I could say:\r\n```kotlin\r\nnearestPoint.copy(\r\n  commitHash = targetCommit.value,\r\n  filePath = fileDiff.newPath,\r\n  isOriginal = false,\r\n)\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#discussion_r816323120"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#pullrequestreview-896582750", "body": ""}
{"comment": {"body": "I won't even pretend to understand what this is doing. Needs a comment :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#discussion_r816967867"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#pullrequestreview-896584729", "body": ""}
{"comment": {"body": "ah nvm github was obscuring your comment \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#discussion_r816968734"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/433#pullrequestreview-896761804", "body": ""}
{"title": "Update manifest url for exchange", "number": 4330, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4330", "body": "Client needs to send two redirectURLs.\n\nRedirectURL for oauth exchange (exists)\nRedirectURL for manifest exchange (NEW)\n\nWeb client is currently currently setting the redirectURL as login/manifest/exchange instead of login/exchange in findEnterpriseRepositories which is causing issues with the oauth flow."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4330#pullrequestreview-1254100567", "body": ""}
{"title": "Respect limit passed by client", "number": 4331, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4331"}
{"title": "route to specific video asset for vscode", "number": 4332, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4332", "body": "We are using product agent to determine which video asset we should be using.\nFor vscode we need to use h264 with mp3 due to licensing issues.\nThis is horrible, but it is what it is."}
{"title": "Drive repo indexing with an event", "number": 4333, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4333", "body": "Triggering reindexing for a repo from the admin console was hitting timeouts. Instead lets send a single event for a repo, and have the search service be responsible for creating the indexing events for each insight."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4333#pullrequestreview-1260771516", "body": ""}
{"title": "increase slack service and slack data service cpu limits", "number": 4334, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4334"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4334#pullrequestreview-1260799648", "body": ""}
{"title": "Upgrade exposed/aws", "number": 4335, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4335"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4335#pullrequestreview-1260849332", "body": ""}
{"title": "Open PR from Hub", "number": 4336, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336", "body": "Update spec to allow hub to open PR in VSCode"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1260859403", "body": ""}
{"comment": {"body": "Haven't updated any of the inputs into PR view yet.\r\n\r\nThis will need to be updated to make pr title, file path & slackThreads optional.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1080617673"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1260942653", "body": ""}
{"comment": {"body": "Not sure that we need to make this change. We already have `OpenThreadAction` and `OpenPRAction`.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1080677982"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1260965360", "body": ""}
{"comment": {"body": "It's just renaming thread to insight to be more broad.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1080694110"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1260967439", "body": ""}
{"comment": {"body": "Concern is not the renaming, it's the addition of a new enum case. The intended semantics of this thing is like:\r\n```\r\nenum InsightAction {\r\n    OPEN = 0;\r\n    CLOSE = 1;\r\n}\r\n```\r\n\r\nSince you added the new `Open[PR/Thread]Action` types, it feels redundant to also add an `OPEN_PR` and `OPEN_THREAD` enum case I think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1080695600"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1260994868", "body": ""}
{"comment": {"body": "This is primarily used for the capabilities `supportedActions`.\r\nIf we don't need that, I think we can get rid of this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1080715189"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1261120757", "body": ""}
{"comment": {"body": "\ud83d\udc4d", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1080809413"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1262334902", "body": ""}
{"comment": {"body": "We could just unify this and have a single `id` property, being the `threadId` if it's a thread request, or a `prId` if it's a PR ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1081636409"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1262336297", "body": ""}
{"comment": {"body": "Those should all be doable without too much work.  The title and slackThreads should be loaded off of the fetched PR information.  The currentFilePath can just be made optional with no ill effect I think.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1081637333"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1262379136", "body": ""}
{"comment": {"body": "Yeah I'm with Matt - could refactor this as follows:\r\n```proto\r\nenum InsightType {\r\n  PR = 0;\r\n  THREAD = 1;\r\n}\r\n\r\nmessage InsightActionRequest {\r\n  string requestId = 1;\r\n  string insightId = 2;\r\n  string teamId = 3;\r\n  string repoId = 4;\r\n  InsightType type = 5;\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1081665219"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1262384450", "body": ""}
{"comment": {"body": "Or if you prefer message typing:\r\n```proto\r\nmessage InsightActionRequest {\r\n  oneof content {\r\n    OpenThreadAction openThread = 1;\r\n    OpenPRAction openPR = 2;\r\n  }\r\n}\r\n\r\nmessage OpenInsightAction {\r\n  string requestId = 1;\r\n  string insightId = 2;\r\n  string teamId = 3;\r\n  string repoId = 4;\r\n}\r\n\r\nmessage OpenThreadAction {\r\n  OpenInsightAction action = 1;\r\n}\r\n\r\nmessage OpenPRAction {\r\n  OpenInsightAction action = 2;\r\n}\r\n```\r\n\r\nProbably easier to do the first one though so the receiving client doesn't have to toss around its own type info", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1081668637"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1262395859", "body": ""}
{"comment": {"body": "The search PR has a TODO to slot in similar behaviour for opening PRs. Let's do that in a separate PR once this is in though", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1081676229"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-1262708269", "body": ""}
{"comment": {"body": "Updated spec.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1081923262"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Nice - I like it!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#discussion_r1081926309"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4336#pullrequestreview-**********", "body": "LGTM"}
{"title": "Identity provider was wrong for GHE", "number": 4337, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4337"}
{"title": "Refactor menu properties", "number": 4338, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4338", "body": "Also cleans up some styling for breakpoints"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4338#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This should fix https://linear.app/unblocked/issue/UNB-849/slack-threads-should-not-have-view-pull-request-conversation-menu", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4338#discussion_r1080701748"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4338#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Do we really need to switch on the thread type here?  Wouldn't we just always navigate to the PR if `threadInfo.pullRequest` is not null (and maybe if anchorSourcePointDAta and threadInfo.mark exist?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4338#discussion_r1081644357"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4338#pullrequestreview-1262372283", "body": ""}
{"comment": {"body": "I was just going off by the discussion in this thread https://chapter2global.slack.com/archives/C02US6PHTHR/p1674083189427299\r\n\r\nas in, if we can guarantee that those properties won't exist on a slack thread then sure? But I guess this seemed to me like the most robust way to ensure that this menu item hides/shows only for the types we want ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4338#discussion_r1081662107"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4338#pullrequestreview-1262376027", "body": ""}
{"title": "Rename GitHub API classes", "number": 4339, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4339", "body": "No behaviour change."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4339#pullrequestreview-**********", "body": ""}
{"title": "Logging bug fix", "number": 434, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/434", "body": "I mixed up the key/values"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/434#pullrequestreview-*********", "body": ""}
{"title": "Fix topic routing", "number": 4340, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4340"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4340#pullrequestreview-**********", "body": ""}
{"title": "Set ThreadInfo.pullRequests", "number": 4341, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4341"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4341#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4341#pullrequestreview-**********", "body": ""}
{"title": "Support offsets for search result pagination", "number": 4342, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4342"}
{"title": "Refactor GitHub classes", "number": 4343, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4343", "body": "No behaviour change, just renames."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4343#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4343#pullrequestreview-1261012602", "body": ""}
{"title": "Revert VSCode", "number": 4344, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4344", "body": "Reverting https://github.com/NextChapterSoftware/unblocked/pull/4287 for deployment purposes"}
{"title": "Fix icon size vs code", "number": 4345, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4345", "body": "Issue with shared styles between web + VSCode.\nTemporary fix for VSCode. Does make web icons smaller but will address that independently once this is in.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4345#pullrequestreview-1262400149", "body": ""}
{"comment": {"body": "Changes here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4345#discussion_r1081678955"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4345#pullrequestreview-1262400464", "body": ""}
{"comment": {"body": "And here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4345#discussion_r1081679129"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4345#pullrequestreview-1262410057", "body": ""}
{"title": "Add missing slack urls", "number": 4346, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4346", "body": "Add slack urls to insight summary views\n\n\nAdd missing context menu to insight explorer pr rows:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4346#pullrequestreview-1262476584", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4346#pullrequestreview-1262860072", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4346#pullrequestreview-1262860402", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4346#pullrequestreview-1262860879", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4346#pullrequestreview-1262868208", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4346#pullrequestreview-1264150231", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4346#pullrequestreview-1264158423", "body": ""}
{"title": "Add transcoder access to video service", "number": 4347, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4347"}
{"title": "Add links to PullRequest", "number": 4348, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4348"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4348#pullrequestreview-1262563237", "body": ""}
{"title": "Web References Icon Override", "number": 4349, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349", "body": "\nBack to proper size"}
{"comment": {"body": "Main painpoint here is that it completely ignores the size set within the tsx for the icon.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#issuecomment-1397514563"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1262815620", "body": ""}
{"comment": {"body": "We have a `@icon-sizing` mixin -- I see they do slightly different things but maybe we should at least put them in the same place? And name them more clearly? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#discussion_r1081997596"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1262817256", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1262830258", "body": ""}
{"comment": {"body": "If anything, we would move icon-sizing to layout-mixing.\r\n\r\nIcon-sizing lives within icon.scss right now which isn't / shouldn't be referenced by other sass files.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#discussion_r1082007232"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1262834603", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1262842740", "body": ""}
{"comment": {"body": "Moved and renamed icon-sizing to layout-mixin.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#discussion_r1082013837"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1262847214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264136237", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264136580", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264143367", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264152562", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264155033", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264155992", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264157402", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264158770", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264160635", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264166907", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264336675", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4349#pullrequestreview-1264392957", "body": ""}
{"title": "Plumb identity ID into API call logger", "number": 435, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/435", "body": "Second log entry below has identityId\n```\n16:06:15 | DEBUG | c.n.p.Monitoring: 200 OK: GET - /godmode/60933ef2-1a65-41e9-9265-190fc6f37d87\n{ http.req.query=, http.req.path=/godmode/60933ef2-1a65-41e9-9265-190fc6f37d87, http.req.method=GET, http.req.header.User-Agent=curl/7.77.0, http.res.status=200 }\n16:06:48 | DEBUG | c.n.p.Monitoring: 200 OK: GET - /teams\n{ http.req.query=, identityId=60933ef2-1a65-41e9-9265-190fc6f37d87, http.req.path=/teams, http.req.method=GET, http.req.header.User-Agent=curl/7.77.0, http.res.status=200 }\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/435#pullrequestreview-895728324", "body": ""}
{"comment": {"body": "intentionally not prefixing this one with \"http.req.\" because these IDs could be added at multiple points in the stack, and we don't want a bunch of duplicates like: \"http.req.identityId\" and \"db.identityId\" and \"identityId\" etc", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/435#discussion_r816350899"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/435#pullrequestreview-895728630", "body": ""}
{"comment": {"body": "teamId will go here two, once we figure this out:\r\nhttps://www.notion.so/Token-Resource-Authorization-e8fdb9db549f4e0788d2c6d923be70e8", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/435#discussion_r816351145"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/435#pullrequestreview-896582268", "body": ""}
{"title": "break it or make it. Remove all capabilities from service pods", "number": 4350, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4350"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4350#pullrequestreview-1262598186", "body": ""}
{"title": "Add transcoder functionality", "number": 4351, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4351"}
{"title": "Reintroduce GHE in VSCode", "number": 4352, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4352", "body": "Revert the VSCodeGHE revert\nAdded fix to RepoStore for 'Could not find repo from service'"}
{"comment": {"body": "Merging in as revert was already approved in the past.\r\n\r\nOnly change is as state above.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4352#issuecomment-1397797883"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4352#pullrequestreview-1262651335", "body": ""}
{"comment": {"body": "Slight update to how currentValue is stored and accessed.\r\n\r\nBefore this, whenever updateValue changed, we would be assigning by value or updating the reference.\r\n\r\nThis didn't propagate to the accessors of currentValue. From their perspective, nothing was updated.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4352#discussion_r1081881888"}}
{"title": "add a temp writable volume and fsGroup security context", "number": 4353, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4353", "body": "Our logging library is throwing exceptions because it cannot write to /tmp directory after recent permission changes. This adds a temporary empty dir volume with writable permissions to /tmp to get around that without adding any security risks\nailed to instantiate [ch.qos.logback.classic.LoggerContext]\nReported exception:\nch.qos.logback.core.LogbackException: Failed to initialize Configurator: ch.qos.logback.classic.util.DefaultJoranConfigurator using ServiceLoader\n    at ch.qos.logback.classic.util.ContextInitializer.autoConfig(ContextInitializer.java:99)\n    at ch.qos.logback.classic.util.ContextInitializer.autoConfig(ContextInitializer.java:77)\n    at ch.qos.logback.classic.spi.LogbackServiceProvider.initializeLoggerContext(LogbackServiceProvider.java:50)\n    at ch.qos.logback.classic.spi.LogbackServiceProvider.initialize(LogbackServiceProvider.java:41)\n    at org.slf4j.LoggerFactory.bind(LoggerFactory.java:152)\n    at org.slf4j.LoggerFactory.performInitialization(LoggerFactory.java:139)\n    at org.slf4j.LoggerFactory.getProvider(LoggerFactory.java:422)\n    at org.slf4j.LoggerFactory.getILoggerFactory(LoggerFactory.java:408)\n    at org.slf4j.LoggerFactory.getLogger(LoggerFactory.java:357)\n    at org.slf4j.LoggerFactory.getLogger(LoggerFactory.java:383)\n    at software.amazon.awssdk.utils.Logger.loggerFor(Logger.java:221)\n    at software.amazon.awssdk.auth.signer.internal.AbstractAws4Signer.clinit(AbstractAws4Signer.java:65)\n    at software.amazon.awssdk.services.rds.DefaultRdsUtilities.init(DefaultRdsUtilities.java:40)\n    at software.amazon.awssdk.services.rds.DefaultRdsUtilities.init(DefaultRdsUtilities.java:46)\n    at software.amazon.awssdk.services.rds.DefaultRdsUtilities$DefaultBuilder.build(DefaultRdsUtilities.java:153)\n    at com.nextchaptersoftware.db.common.config.AwsStsPasswordDelegate.getValue(AwsStsPasswordDelegate.kt:20)\n    at com.nextchaptersoftware.db.common.config.AwsDbConfigProvider.getPassword(AwsDbConfigProvider.kt:16)\n    at com.nextchaptersoftware.db.common.config.AwsDbConfigProvider.init(AwsDbConfigProvider.kt:31)\n    at com.nextchaptersoftware.db.common.config.DbConfigProviderDelegate.getValue(DbConfigProviderDelegate.kt:30)\n    at com.nextchaptersoftware.db.common.Database.getDbConfigProvider(Database.kt:34)\n    at com.nextchaptersoftware.db.common.Database.connect(Database.kt:78)\n    at com.nextchaptersoftware.db.common.Database.init(Database.kt:52)\n    at com.nextchaptersoftware.apiservice.ApplicationKt.main(Application.kt:32)\n    at com.nextchaptersoftware.apiservice.ApplicationKt.main(Application.kt)\nCaused by: io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigQueueException: java.io.FileNotFoundException: /tmp/logzio-logback-queue/java/logzio-logback-appender/meta_data/page-0.dat (No such file or directory)\n    at io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.MappedPageFactory.acquirePage(MappedPageFactory.java:94)\n    at io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigArray.initArrayIndex(BigArray.java:241)\n    at io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigArray.commonInit(BigArray.java:157)\n    at io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigArray.init(BigArray.java:141)\n    at io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigQueue.init(BigQueue.java:83)\n    at io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigQueue.init(BigQueue.java:67)\n    at io.logz.logback-appender.sender.DiskQueue.init(DiskQueue.java:32)\n    at io.logz.logback-appender.sender.DiskQueue.init(DiskQueue.java:10)\n    at io.logz.logback-appender.sender.DiskQueue$Builder.build(DiskQueue.java:154)\n    at io.logz.logback-appender.sender.LogzioSender$Builder.getLogsQueue(LogzioSender.java:272)\n    at io.logz.logback-appender.sender.LogzioSender$Builder.build(LogzioSender.java:264)\n    at io.logz.logback.LogzioLogbackAppender.start(LogzioLogbackAppender.java:252)\n    at ch.qos.logback.core.model.processor.AppenderModelHandler.postHandle(AppenderModelHandler.java:84)\n    at ch.qos.logback.core.model.processor.DefaultProcessor.secondPhaseTraverse(DefaultProcessor.java:248)\n    at ch.qos.logback.core.model.processor.DefaultProcessor.secondPhaseTraverse(DefaultProcessor.java:244)\n    at ch.qos.logback.core.model.processor.DefaultProcessor.traversalLoop(DefaultProcessor.java:90)\n    at ch.qos.logback.core.model.processor.DefaultProcessor.process(DefaultProcessor.java:106)\n    at ch.qos.logback.core.joran.GenericXMLConfigurator.processModel(GenericXMLConfigurator.java:199)\n    at ch.qos.logback.core.joran.GenericXMLConfigurator.doConfigure(GenericXMLConfigurator.java:165)\n    at ch.qos.logback.core.joran.GenericXMLConfigurator.doConfigure(GenericXMLConfigurator.java:122)\n    at ch.qos.logback.core.joran.GenericXMLConfigurator.doConfigure(GenericXMLConfigurator.java:65)\n    at ch.qos.logback.classic.util.DefaultJoranConfigurator.configureByResource(DefaultJoranConfigurator.java:53)\n    at ch.qos.logback.classic.util.DefaultJoranConfigurator.configure(DefaultJoranConfigurator.java:34)\n    at ch.qos.logback.classic.util.ContextInitializer.autoConfig(ContextInitializer.java:93)\n    ... 23 more\nCaused by: java.io.FileNotFoundException: /tmp/logzio-logback-queue/java/logzio-logback-appender/meta_data/page-0.dat (No such file or directory)\n    at java.base/java.io.RandomAccessFile.open0(Native Method)\n    at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:344)\n    at java.base/java.io.RandomAccessFile.init(RandomAccessFile.java:259)\n    at java.base/java.io.RandomAccessFile.init(RandomAccessFile.java:213)\n    at java.base/java.io.RandomAccessFile.init(RandomAccessFile.java:127)\n    at io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.MappedPageFactory.acquirePage(MappedPageFactory.java:85)\n    ... 46 more"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4353#pullrequestreview-1262719503", "body": ""}
{"title": "Cleanup VSCode explorer insight filter UX", "number": 4354, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4354", "body": "Reduce padding to match designs\nAnimate the filter input bar open/close.  I added an animation that collapses/expands the UI."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4354#pullrequestreview-1264129695", "body": ""}
{"comment": {"body": "Fwiw we have a SearchInput component that basically has some of these components built in (i.e. the close icon) -- could probably add some icon overrides to handle the magnifying glass vs filter leading icon", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4354#discussion_r1082912658"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4354#pullrequestreview-1264130029", "body": ""}
{"title": "Move transcoding to event based system", "number": 4355, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4355"}
{"title": "Search suggestions API", "number": 4356, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356", "body": "To handle the autocomplete UI in the search experience:\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262755611", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262756269", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262757989", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262759773", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262760343", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262760898", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262761728", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262762489", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262764159", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262767277", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262768848", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1262769667", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264066228", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264089695", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264091402", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264102038", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264108163", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264109653", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264114283", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264126711", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264148981", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264149971", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264153158", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264160804", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4356#pullrequestreview-1264253551", "body": "LGTM. The response length will be between 0 and 10 (inclusive) so it would make sense to only reveal the suggestion drop down when a non-empty array is returned."}
{"title": "Mobile chat", "number": 4357, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357", "body": "This calls a GCP service that I am still figuring out where to check into the tree. There are 3 todos in the client, but think this includes your feedback @pwerry."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#pullrequestreview-1264104061", "body": ""}
{"comment": {"body": "No DNS name?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#discussion_r1082896892"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#pullrequestreview-1264105416", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#pullrequestreview-1264106824", "body": ""}
{"comment": {"body": "I would make the `send` function `async` and then do:\r\n```swift\r\nawait MainActor.run {\r\n    self.messages.append(messageItem())\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#discussion_r1082898677"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#pullrequestreview-1264107898", "body": ""}
{"comment": {"body": "Also then you can use the `async` version of `URLSession.dataTask`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#discussion_r1082899306"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#pullrequestreview-1264108599", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#pullrequestreview-1264127478", "body": ""}
{"comment": {"body": "Trying to see what it takes to run on GCP and use their native ML tools, need to figure out how to get QDN over there.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#discussion_r1082911309"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#pullrequestreview-1269917210", "body": ""}
{"comment": {"body": "This is on GCP, not AWS, so it doesn't have a hostname yet.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4357#discussion_r1087040023"}}
{"title": "Fix potential recording crash", "number": 4358, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4358", "body": "Thread 0 Crashed::  Dispatch queue: com.apple.main-thread\n0   libswiftCore.dylib                     0x1b6c45fa0 closure #1 in closure #1 in closure #1 in _assertionFailure(_:_:file:line:flags:) + 380\n1   libswiftCore.dylib                     0x1b6c45fa0 closure #1 in closure #1 in closure #1 in _assertionFailure(_:_:file:line:flags:) + 380\n2   libswiftCore.dylib                     0x1b6c45ce4 closure #1 in closure #1 in _assertionFailure(_:_:file:line:flags:) + 200\n3   libswiftCore.dylib                     0x1b6c45adc closure #1 in _assertionFailure(_:_:file:line:flags:) + 212\n4   libswiftCore.dylib                     0x1b6c4563c _assertionFailure(_:_:file:line:flags:) + 236\n5   Unblocked Video                        0x1041e5104 internalInfoCallback(_:axElement:notification:cfInfo:userData:) + 732\n6   Unblocked Video                        0x1041e4dfc @objc internalInfoCallback(_:axElement:notification:cfInfo:userData:) + 84\nAXSwift is at fault here. The crash only seems to happen when using the more advanced observer callback method, so I removed it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4358#pullrequestreview-1262763767", "body": ""}
{"title": "Fix bug where shortcut key needs to be pressed twice to open hub", "number": 4359, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4359"}
{"title": "Video chat recording API", "number": 436, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436", "body": "API completely re-written.\nThe key idea behind this is that the service is now maintaining channel state, including the list of participants and their roles. To create a channel, the client calls /videoChannels/{channelID}/join, where channelID is a UUID the client generates. The response contains the Agora identity and token that the client should use to join the channel. The next participant that comes along will already have the channelID, and when they join they are added to that VideoChannel's participant list.\nWhen updates to the video channel state occur, either by adding participants or by putting it in \"recording\" mode, the push channel will receive an update.\nRecordings are a top level entity (rather than a child of VideoChannel) because they are referenced in other parts of the system, like Messages"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076559849", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/b258a6a3-931b-452a-9283-b49469169087)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=5b4a1ab4-409e-4b72-9a22-e44c05fcd331).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948550529"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076559943", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/0aa4d918-c851-4c66-9134-b5dc08df50c7)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=5508fb12-a6d9-4313-adac-8068dec69ef3).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948550624"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076560216", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=efcf9637-72cf-4714-9c9a-23a822cfdae2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948550864"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076560745", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=f4a4d6f3-58b1-45a5-b8d6-9986046a8e02).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948551346"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076565382", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/e8a71392-99f6-4299-bbb1-272cb9023d0f)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=f1bfdd90-2f92-4718-bb16-e35a14025e6d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948555175"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076565511", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=ac12d785-672c-4193-bc8c-946d7a40a4ae).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948555285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076809352", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/82b15133-d516-45a2-9231-f56a71068bd9)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e38d9684-1634-4a59-a5f2-296b590f42df?message=c1e67c40-463d-4597-a69f-34485fc63637).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948734788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076809901", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/1d64e295-ca53-4aeb-93b0-cdc9764f843e?message=bb17dbe5-8e61-4442-9a28-04ad3a517778).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948735167"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076811564", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/6738fc1e-739b-4515-9a68-5e240b84ddc2)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/1d64e295-ca53-4aeb-93b0-cdc9764f843e?message=6c0ca19b-37b1-4fa1-b43a-7ac82a161601).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948736335"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076812967", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/18b3e93f-709b-40af-86e2-fb5ce822ad32)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=dc9afef2-911b-45ad-b707-9232aaa4c41e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948737326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076817230", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/56994bf5-a5f6-4793-8413-ae32432ef751)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=5e002f1b-f121-4af5-9d34-ef958d158397).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948740512"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076817363", "body": ""}
{"comment": {"body": "asdfsda\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=2db6f034-6a62-44b2-8beb-4537d655c142).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948740592"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076841749", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/d426bc7e-bdfb-473a-9150-1387cbdf4c59)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/32c79c4e-409e-4fd3-b19b-17158c8540af?message=12dd7ce2-1307-4cad-a958-dbdd94dc6c3f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948758683"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076842228", "body": ""}
{"comment": {"body": "![](data:image/jpeg;base64,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)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/32c79c4e-409e-4fd3-b19b-17158c8540af?message=11e2325f-ddaa-4032-ba1f-f8d2830550fb).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948759123"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076842359", "body": ""}
{"comment": {"body": "![](data:image/jpeg;base64,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)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/0a49c47a-efc1-44ad-ad33-3496a255198d?message=1b3f3ad3-1382-46b2-8b7c-46be3cc99a08).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948759222"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-**********", "body": ""}
{"comment": {"body": "![](data:image/jpeg;base64,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)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/0a49c47a-efc1-44ad-ad33-3496a255198d?message=ff150c53-9a76-4eaa-a65d-0a28c2b85c2b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948763710"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-**********", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/6073b8e9-f3cd-43af-9bf0-3d8b609ab724)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/0a49c47a-efc1-44ad-ad33-3496a255198d?message=7339c199-acd9-4a9a-a2ba-5c5ef25dd89e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948770174"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076857344", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/c03ef4af-935a-4f3e-bc1c-bd6572f97863)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e38d9684-1634-4a59-a5f2-296b590f42df?message=ec405128-f806-4af3-a2d8-78a2f479343b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948770333"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076857723", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/fdb24baf-b7bc-4d93-910a-0cf69e7c685c)\n\n![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/c1424993-d594-4960-a483-a62d25b03bd5)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=7bf84c49-d9cb-4f13-ae97-b044b07d96a8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948770595"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076858232", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/ef74b67a-554c-4d53-ad93-a4cf62768b93)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/1d64e295-ca53-4aeb-93b0-cdc9764f843e?message=f8edf310-e77e-41ec-8a54-cf10bf53a63a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948770928"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076858486", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/4467aa6e-881b-4d2b-b86b-c6262a71766f)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/32c79c4e-409e-4fd3-b19b-17158c8540af?message=fd43870e-ea98-4a8f-b186-157f2d9aee64).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948771122"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076858809", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/eba7203a-4ad5-467b-8e78-cf8a81a5ba1e)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e38d9684-1634-4a59-a5f2-296b590f42df?message=d694e8d7-b985-4505-9481-b0fd0615271c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948771372"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-1076859090", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/7697e822-3453-4a20-abca-fa6bbee70c80)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a92a5122-1f2c-4809-8627-ade0a7734f35?message=9d6fdc23-1474-4c92-bfaa-563e96a4fd22).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r948771589"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-895765353", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-895827221", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-895888832", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-896652116", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-896668348", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-896673541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-896694671", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-896995628", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-896998687", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-897000285", "body": ""}
{"comment": {"body": "This is very Agora-centric -- I assume we need the agora items to pass through to the video app.  I'm wondering if we should also include data here that maps this to a TeamMember (maybe just the teamMemberID) so that we can display Unblocked user info for who is streaming?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r817273323"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-897003120", "body": ""}
{"comment": {"body": "I'm guessing clients will poll on this channel `/videoChannels/{channelID}` so they know when this resource changes, so we can keep the video banner UI up to date?\r\n\r\n<img width=\"422\" alt=\"Screen Shot 2022-03-01 at 5 28 46 PM\" src=\"https://user-images.githubusercontent.com/2133518/156277357-5d4e042c-f41b-44c1-9fcf-b32a07547928.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r817275577"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-897077444", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-897080347", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-897086917", "body": ""}
{"comment": {"body": "I'm not sure what's Agora centric about what you've highlighted here, but there are definitely some Agora centric things in the API (like AgoraParticipantID). And this is a bit of an unfortunate consequence of Agora using uints for their participant ids instead of UUIDs. I can modify the channel participant object so it uses uints for its own ID, but that's not very provider agnostic. Open to suggestions - maybe we need a `VideoProvider` object that carries this info. Seems overkill for now though", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r817340653"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-897087363", "body": ""}
{"comment": {"body": "Including `teamMemberID` is definitely the right thing to do. I'm going to add that to the `VideoChannelParticipant` and remove it everywhere else", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r817340929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-897097160", "body": ""}
{"comment": {"body": "Exactly!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r817348307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-897097574", "body": ""}
{"comment": {"body": "To that point, I haven't yet created an association between `Thread` and `VideoChannel`, but I have some thoughts on how to model this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r817348575"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-897995265", "body": ""}
{"comment": {"body": "I would split this out to be more explicit:\r\n\r\n`PUT  /videoChannels/{channelID}`\r\n* createVideoChannel (this create AND joins the creator)\r\n\r\n`PUT  /videoChannels/{channelID}/join`\r\n* joinVideoChannel (only participants call this)\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r817993855"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-898001110", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-898002514", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-898021015", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-898024601", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-898030252", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-898065758", "body": "remaining nits"}
{"comment": {"body": "make status required\r\n```suggestion\r\n        status:\r\n          type: string\r\n          enum: [ pending, joined ]\r\n      required:\r\n        - id\r\n        - agoraId\r\n        - teamMemberId\r\n        - videoChannelId\r\n        - role\r\n        - status\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r818033408"}}
{"comment": {"body": "We need this here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r818033559"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-898069986", "body": ""}
{"comment": {"body": "I think the client needs this for its own db. We have a similar value for `ThreadParticipant` (`threadId`)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r818036395"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-898070707", "body": ""}
{"comment": {"body": "good catch", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r818036963"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#pullrequestreview-898105891", "body": ""}
{"comment": {"body": "aside: we're removing ThreadParticipant :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/436#discussion_r818057191"}}
{"title": "Remove auto-focus of VSCode tree items", "number": 4360, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4360", "body": "We currently re-focus a tree view whenever a selected item is re-rendered in that view.  This was added here: https://github.com/NextChapterSoftware/unblocked/pull/1036.\nThis seems wrong, and is causing bugs.  Scrolling around on views can cause the view focus to change.  I can't see why we need this feature, I think we should remove it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4360#pullrequestreview-1262824325", "body": ""}
{"title": "Resize insight summary for unread dot spacing", "number": 4361, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4361", "body": "before:\n\nafter:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4361#pullrequestreview-1262845350", "body": ""}
{"title": "Introduce Enterprise GitHub App factory", "number": 4362, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4362", "body": "fixes \nfixes \nfixes "}
{"title": "Enable GHE in PROD", "number": 4363, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4363"}
{"title": "Fix multipart upload", "number": 4364, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4364"}
{"title": "Cleanup cdk code", "number": 4365, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4365"}
{"title": "Mobile chat", "number": 4366, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4366"}
{"comment": {"body": "@pwerry : you already reviewed this, this is just a change to stop an apple connect warning about the mobile build.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4366#issuecomment-1398792976"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4366#pullrequestreview-1264160686", "body": ""}
{"title": "VSCode support for opening PR from Hub", "number": 4367, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4367", "body": "Follow up to https://github.com/NextChapterSoftware/unblocked/pull/4336\nUpdates OpenPullRequestInfo to allow for panel metadata to be updated after PR is loaded."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4367#pullrequestreview-1264252412", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4367#pullrequestreview-1264422212", "body": ""}
{"title": "Add links to Topic and TeamMember", "number": 4368, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4368", "body": "For navigation amongst clients. Not sure if there are other links that are relevant for these models(?)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4368#pullrequestreview-1264402313", "body": ""}
{"title": "Add vscode and dashboard capabilities to PR insights", "number": 4369, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369"}
{"comment": {"body": "Will need this to work end to end https://github.com/NextChapterSoftware/unblocked/pull/4367", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#issuecomment-1399015821"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#pullrequestreview-1264401736", "body": ""}
{"comment": {"body": "What's the purpose of having dashboard home as a backup?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#discussion_r1083088575"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#pullrequestreview-1264410287", "body": ""}
{"comment": {"body": "Should we try making this button conditional? VSCode publishes its capabilities to the Hub so we could have this responsive.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#discussion_r1083093604"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#pullrequestreview-1264411694", "body": ""}
{"comment": {"body": "Good question. Originally we thought if we somehow failed to resolve the url we might as well plonk them on the dashboard and let them navigate to it, but this is pretty lazy. We need proper error handling here. I'll discuss with Ben next week. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#discussion_r1083094537"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#pullrequestreview-1264412070", "body": ""}
{"comment": {"body": "If VSCode fails to open right now, we open it in Safari. I don't think that's appropriate for a button that says \"Open in VSCode\"", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#discussion_r1083094800"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#pullrequestreview-1264412265", "body": "Just some thoughts on behaviour."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#pullrequestreview-1264413008", "body": ""}
{"comment": {"body": "Yes - to do this we need an observer that the HubIPCService code publishes to for particular repoIds. It's complex, and not something I want to do in this PR, but you're totally right about this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#discussion_r1083095522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#pullrequestreview-1264413745", "body": ""}
{"comment": {"body": "Actually I reimplemented this. If the user explicitly clicks on \"Open in VSCode\" and it fails, it now fails silently. We need to do a better job of handling the error case.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4369#discussion_r1083096012"}}
{"title": "Implement locks for PR ingestion and enable ingestion in DEV", "number": 437, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437", "body": "Implements a locking mechanism for PR ingestion so that we're not ingesting dupes when multiple API service instances are running."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895739256", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895739627", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895750694", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895752804", "body": "nice work figuring this out!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895756683", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895759877", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895767026", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895767267", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895942197", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895943684", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/437#pullrequestreview-895969647", "body": ""}
{"title": "Add cloudfront support for product agent", "number": 4370, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4370"}
{"title": "Asset url requests should include product agent", "number": 4371, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4371"}
{"title": "Refactor PullRequestContextMenu for reuse", "number": 4372, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4372", "body": "Refactor PullRequestContextMenu for reuse across clients\nPass through PullRequest.links.dashboardUrl into clients\nRename @chat to @threads (large diff but no file changes except deleting ChatSummaryView files)\nCleanup spacing between unread/mention indicator and the title in the dashboard"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4372#pullrequestreview-1264474657", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4372#pullrequestreview-1264475005", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4372#pullrequestreview-1264475430", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4372#pullrequestreview-1264475905", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4372#pullrequestreview-1264476473", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4372#pullrequestreview-1264479863", "body": ""}
{"title": "Remove SearchService, SearchStore, and SearchModel", "number": 4373, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4373", "body": "We're replacing it with the SearchInsight versions."}
{"title": "Delete SearchModel table", "number": 4374, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4374", "body": "Requires https://github.com/NextChapterSoftware/unblocked/pull/4373 to be merged first"}
{"title": "Include PullRequestModel.updatedAt when indexing recency", "number": 4375, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4375"}
{"title": "GitHubInstallationMaintenanceJob runs every 12h instead of every 10s", "number": 4376, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4376", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4376#pullrequestreview-1266183441", "body": ""}
{"title": "Fix image max-width in pr layout", "number": 4377, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4377"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4377#pullrequestreview-1266077310", "body": ""}
{"title": "update powerml code", "number": 4378, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4378"}
{"title": "update docs", "number": 4379, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4379"}
{"title": "Move ThreadParticipants to TeamMembers within Thread", "number": 438, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438", "body": "Removing concept of ThreadParticipants. \nClients will always request ThreadParticipants / TeamMembers with the context of a thread.\nAdd list of TeamMember IDs to Thread."}
{"comment": {"body": "```\r\n/Code/unblocked/apiservice/build/generated-api/src/main/kotlin/com/nextchaptersoftware/api/models/Thread.kt: (27, 93): Serializer has not been found for type 'UUID'. To use context serializer as fallback, explicitly annotate type or property with @Contextual\r\n```\r\n\r\nAnyone have experience with this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#issuecomment-1054860761"}}
{"comment": {"body": "So one (major-ish) downside of this change is that pusher needs to pull `lastModifiedAt` from two DB models: `ThreadModel` and `ThreadParticipantModel`.\r\n\r\nI know this makes it nicer for the client to consume, but it's slower to join on two models, and costly at the scale that pusher currently polls the API.\r\n\r\n@pwerry @matthewjamesadam @jeffrey-ng you guys sure you want to go this direction? what's the strong motivation?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#issuecomment-1054864226"}}
{"comment": {"body": "> So one (major-ish) downside of this change is that pusher needs to pull `lastModifiedAt` from two DB models: `ThreadModel` and `ThreadParticipantModel`.\r\n> \r\n> I know this makes it nicer for the client to consume, but it's slower to join on two models, and costly at the scale that pusher currently polls the API.\r\n> \r\n> @pwerry @matthewjamesadam @jeffrey-ng you guys sure you want to go this direction? what's the strong motivation?\r\n\r\nThe motivation is that, from the client point of view, these are effectively the same dataset -- everywhere we render a Thread, we render its ThreadParticipants.  Having the datasets separate means that the client will have to download every single Thread (because it needs all the Threads to render the tree), plus every single ThreadParticipant (to render avatars on the tree), and subscribe to the channels for both, to render the sidebar UI.  As a side note, both models already are quite small, and will have a low rate of churn, so it didn't feel like joining them was wrong (as opposed to, say the unread table, which has very different updating characteristics).\r\n\r\nI might be misunderstanding, but I think even if the models are kept separate, Pusher will have to pull lastModifiedAt from two DBs anyways, because the client will have to subscribe to both Threads and ThreadParticipants to render live UIs?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#issuecomment-1054978198"}}
{"comment": {"body": "\r\n> I know this makes it nicer for the client to consume, but it's slower to join on two models, and costly at the scale that pusher currently polls the API.\r\n\r\nThe cost is sunk, because the consequence of _not_ doing this is that the client would have to subscribe to every single thread for participant updates (think tree view)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#issuecomment-1055919997"}}
{"comment": {"body": "\r\n> @pwerry @matthewjamesadam @jeffrey-ng you guys sure you want to go this direction? what's the strong motivation?\r\n\r\nMotivation: threads and participants are tightly coupled in the UX. Separating these out leads to N queries/channels instead of just 1. That's about all I can think of - data inconsistencies don't appear to be an issue here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#issuecomment-1055923695"}}
{"comment": {"body": "Will need some help with this:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/438#issuecomment-1054860761\r\n\r\nLooks like some type / serialization issues for an array of UUIDs?\r\n\r\n@pwerry @richiebres ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#issuecomment-1057196333"}}
{"comment": {"body": "> Will need some help with this: [#438 (comment)](https://github.com/NextChapterSoftware/unblocked/pull/438#issuecomment-1054860761)\r\n> \r\n> Looks like some type / serialization issues for an array of UUIDs?\r\n> \r\n> @pwerry @richiebres ?\r\n\r\nI found the fix, and currently fighting mustache...\r\n```kotlin\r\n-    @SerialName(value = \"participants\") @Required val participants: kotlin.collections.List<java.util.UUID>\r\n+    @SerialName(value = \"participants\") @Required val participants: kotlin.collections.List<@Contextual java.util.UUID>\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#issuecomment-1057411876"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#pullrequestreview-895751311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#pullrequestreview-897813956", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/438#pullrequestreview-897823916", "body": ""}
{"title": "Restrict pod network access dev", "number": 4380, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4380", "body": "Fix a tiny bug in CI (variable was being set for the wrong action)\nAdded a new Global Network policy to enforce stronger deny rules. (Deny traffic between pods, pod->kubeAPI)\nModified pod policy to use calico and only allow traffic from the service created as part of the same chart\n\nThis would probably break dev. I have tested as much as I could but the rest has to be done live."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4380#pullrequestreview-1266243885", "body": "Man, some of this stuff is not well documented. \nCheerios"}
{"title": "AttemptRetries", "number": 4381, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4381", "body": "retry\nTry retries"}
{"title": "Add support for esbuild loader", "number": 4382, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382", "body": "Webpack ts-loader is really slow.\nSpeed up build times by moving ts-build to esbuild-loader.\nThis required updating our exports to explicitly export types. \nVSCode for DEV builds:\nBefore - 35659 ms for extension. 42207ms for webviews\nAfter - 5884ms for extension. 7862ms for webviews\nWeb for DEV builds:\nBefore - 27258 ms \nAfter - 8156 ms"}
{"comment": {"body": "GH Actions caught type issues:\r\n<img width=\"1291\" alt=\"CleanShot 2023-01-23 at 13 48 28@2x\" src=\"https://user-images.githubusercontent.com/1553313/214157487-4d38521c-9971-483b-8e1f-09a8fabde393.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#issuecomment-1401039274"}}
{"comment": {"body": "In GH Actions, this is currently translating to the following:\r\n<img width=\"368\" alt=\"CleanShot 2023-01-23 at 15 22 34@2x\" src=\"https://user-images.githubusercontent.com/1553313/214174635-777584fc-16b0-4016-b615-4d550d142bd5.png\">\r\n\r\nto\r\n\r\n<img width=\"298\" alt=\"CleanShot 2023-01-23 at 15 22 53@2x\" src=\"https://user-images.githubusercontent.com/1553313/214174644-6f0419e2-5bfc-4fab-99fa-d77af48c11e3.png\">", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#issuecomment-1401144105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#pullrequestreview-1268019072", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#pullrequestreview-1268025217", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#pullrequestreview-1268098707", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#pullrequestreview-1268110710", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#pullrequestreview-1268319918", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#pullrequestreview-1268463670", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#pullrequestreview-1270131086", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#pullrequestreview-1270132815", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4382#pullrequestreview-1270155552", "body": ""}
{"title": "grant access to calico network policies to deployer user", "number": 4383, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4383", "body": "Our deployer user was missing the permission to create calico network resources. This has been applied to both Dev and prod. \nThe procedure to apply it is documented in the cluster setup README."}
{"title": "Update slack avatar", "number": 4384, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4384"}
{"title": "Increase the avatar cache size to accommodate larger images", "number": 4385, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4385"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4385#pullrequestreview-1266416534", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4385#pullrequestreview-1266417942", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4385#pullrequestreview-1266427915", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4385#pullrequestreview-1266435198", "body": ""}
{"title": "Update create topic flow", "number": 4386, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4386", "body": "Per new designs:\n\nAdd ability to clear topics:\n\n\nAdd ability to invite non-UB experts:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4386#pullrequestreview-1266548095", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4386#pullrequestreview-1266558776", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4386#pullrequestreview-1266583161", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4386#pullrequestreview-1266590760", "body": ""}
{"title": "increase pusher dev cpu resources", "number": 4387, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4387"}
{"title": "Creates a unblocked-bot GCP App Engine app", "number": 4388, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4388", "body": "see app.py. didn't check in the embedding as it is big and we don't have lfs turned on. no changes to production."}
{"title": "Move reindexing event to restoreThreadIfNeeded", "number": 4389, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4389", "body": "Should fix a bug where we're not re-indexing when an archived thread is restored."}
{"title": "Sourcemarks API is authed", "number": 439, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/439", "body": "My bad."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/439#pullrequestreview-895750324", "body": "thanks"}
{"title": "Fix dashboard redirects after deleting threads", "number": 4390, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4390", "body": "Under Mine, the view will retrigger via pusher channel (existing behaviour, no-op)\nIn the Insights view, manually refetch insights\nIn the Thread view, don't redirect after deleting/restoring"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4390#pullrequestreview-1268114715", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4390#pullrequestreview-1268116783", "body": ""}
{"title": "Goodnight sweet badges", "number": 4391, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4391", "body": ""}
{"comment": {"body": "\"Don't cry because it's over. Smile because it happened.\" :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4391#issuecomment-1402448983"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4391#pullrequestreview-1268117565", "body": "I think I might cry"}
{"title": "Don't return team members unmarked as experts", "number": 4392, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4392"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4392#pullrequestreview-1268145986", "body": ""}
{"title": "UNB-863 Search query parameter needs size limit", "number": 4393, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4393", "body": "Fixes UNB-863"}
{"title": "Encrypt s3 buckets", "number": 4394, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4394"}
{"title": "More topic management polish", "number": 4395, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395", "body": "Add experts to topic:\n\nRemove experts from topic:\n\n\nUpdate UI for new designs:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1269981724", "body": ""}
{"comment": {"body": "nit, use this type below in the ContextRow usage so it's clear that's where it's actually used?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#discussion_r1087087927"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1270171870", "body": ""}
{"comment": {"body": "Did we finalize what we're doing for display vs token names yet?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#discussion_r1087214868"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1270181302", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1270181539", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1270183212", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1271792582", "body": ""}
{"comment": {"body": "No but this dialog isn't used anywhere yet so we have time to revisit. For the layout/matching design, I think it's worth merging. \r\n\r\nFor transparency, we still need to figure out what renaming a topic actually does. If the actual topic.name is being renamed, what is the ripple effect on how topics are indexed and matched to insights? It's possible that we need to add a `displayName` property to the Topic model, and have users rename this property instead.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#discussion_r1088348190"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1271793795", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1271802306", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1271822619", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1271833566", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1271838100", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4395#pullrequestreview-1271888014", "body": ""}
{"title": "First pass at creating a search suggestion cache", "number": 4396, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4396", "body": "To enable quick look ups for search suggestions"}
{"title": "Network security for ALB and EC2 Kube nodes", "number": 4397, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4397", "body": "Added a deny policy to block pods from accessing EC2 metadata endpoint (Deployed to dev and prod)\nAdded a new stack which creates a security group to allow traffic from CloudFront origin facing IPs using AWS managed prefix list \n\nOnce this has been deployed we need to set the annotation to use the new security group in helm charts. I will be making a separate PR for that."}
{"title": "Add new security group annotation to all external facing services.", "number": 4398, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4398", "body": "Related PR: https://github.com/NextChapterSoftware/unblocked/pull/4397\n\nAdded the security group created as part of the PR above to pod alb annotations. This should modify the ALB to only accept traffic coming from CloudFront origin facing IP addresses\nAdded pod security context to complement the container security context configuration that we added previously."}
{"title": "Bump protobufjs from 6.11.2 to 6.11.3 in /common", "number": 4399, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4399", "body": "Bumps protobufjs from 6.11.2 to 6.11.3.\n\nChangelog\nSourced from protobufjs's changelog.\n\n6.11.3 (2022-05-20)\nBug Fixes\n\ndeps: use eslint 8.x (#1728) (a8681ce)\ndo not let setProperty change the prototype (#1731) (b5f1391)\n\n\n\n\nCommits\n\nb130dfd chore(6.x): release 6.11.3 (#1737)\nc2c17ae build: publish to main\nb2c6a5c build: run tests if ci label added (#1734)\na8681ce fix(deps): use eslint 8.x (#1728)\nb5f1391 fix: do not let setProperty change the prototype (#1731)\n7afd0a3 build: configure 6.x as default branch\n37285d0 build: configure backports\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "AWS CDK project to deploy core infra components", "number": 44, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/44", "body": "This is a PR to create our core CDK app which is used to deploy basic infrastructure components like VPC networks, databases etc to AWS. \n\nAdded class definition for environment JSON config files (lib/build-config.ts)\nCreated JSON config file for Dev in us-west-2 (config/dev-us-west-2.json)\nAdded Network Stack to create all required VPC/network configuration\nAdded Database Stack to deploy an RDS instance and allow access to it from EKS cluster\nUpdated README to include environment setup instructions and some useful resources\n\nAll components included in this PR have been deployed to dev. \nNext:\n- PR to add ECR Repo and IAM roles \n- Setup GitHub actions to CI/CD CDK changes to Dev"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/44#pullrequestreview-854768361", "body": ""}
{"comment": {"body": "We might, at the beginning, require this sucker to be accessible for debuggin purposes.\r\nI would consider either making this public accessible OR create a VPN that we can use to access this guy.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/44#discussion_r786278831"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/44#pullrequestreview-854768441", "body": "One comment."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/44#pullrequestreview-854769874", "body": ""}
{"comment": {"body": "I am working on setting up the VPN. This database cannot be made public because it's launched in a private isolated subnet. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/44#discussion_r786279976"}}
{"title": "Introduce ThreadUnread DB model", "number": 440, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/440", "body": "Next\n\nintegrate Unread inserts/mutations with message creates/deletes\nintegrate Unread inserts/mutations with message reads/unreads\nintegrate Unread inserts/mutations with thread deletes\nAPI model for fetching unreads  https://github.com/NextChapterSoftware/unblocked/pull/442\nAPI model for marking threads as read/unread  https://github.com/NextChapterSoftware/unblocked/pull/442\n\nMotivation\nhttps://www.notion.so/nextchaptersoftware/Unread-Message-Synchronization-50af28a5fa734f59a670e0f04e15e938"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/440#pullrequestreview-896589906", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/440#pullrequestreview-896591564", "body": ""}
{"comment": {"body": "Should this be ThreadParticipant since we're always in the context of a thread?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/440#discussion_r816974144"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/440#pullrequestreview-896602648", "body": ""}
{"comment": {"body": "@pwerry no, and actually @kaych brought the same point up last week as well.\r\n\r\nGiven this a bit of thought and the reason is clear to me now: the set of participants does not overlap completely with the set unread members, meaning that:\r\n- you can be a thread participant, but unfollow a thread\r\n- you can follow a thread, without being a thread participant", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/440#discussion_r816982242"}}
{"title": "Bump jsonwebtoken from 8.5.1 to 9.0.0 in /infrastructure/cdk/core/assets/lambda/cloudfront-edge-assets-auth", "number": 4400, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4400", "body": "Bumps jsonwebtoken from 8.5.1 to 9.0.0.\n\nChangelog\nSourced from jsonwebtoken's changelog.\n\n9.0.0 - 2022-12-21\nBreaking changes: See Migration from v8 to v9\nBreaking changes\n\nRemoved support for Node versions 11 and below.\nThe verify() function no longer accepts unsigned tokens by default. ([834503079514b72264fd13023a3b8d648afd6a16])\nRSA key size must be 2048 bits or greater. ([ecdf6cc6073ea13a7e71df5fad043550f08d0fa6])\nKey types must be valid for the signing / verification algorithm\n\nSecurity fixes\n\nsecurity: fixes Arbitrary File Write via verify function - CVE-2022-23529\nsecurity: fixes Insecure default algorithm in jwt.verify() could lead to signature validation bypass - CVE-2022-23540\nsecurity: fixes Insecure implementation of key retrieval function could lead to Forgeable Public/Private Tokens from RSA to HMAC - CVE-2022-23541\nsecurity: fixes Unrestricted key type could lead to legacy keys usage - CVE-2022-23539\n\n\n\n\nCommits\n\ne1fa9dc Merge pull request from GHSA-8cf7-32gw-wr33\n5eaedbf chore(ci): remove github test actions job (#861)\ncd4163e chore(ci): configure Github Actions jobs for Tests  Security Scanning (#856)\necdf6cc fix!: Prevent accidental use of insecure key sizes  misconfiguration of secr...\n8345030 fix(signverify)!: Remove default none support from sign and verify met...\n7e6a86b Upload OpsLevel YAML (#849)\n74d5719 docs: update references vercel/ms references (#770)\nd71e383 docs: document invalid token error\n3765003 docs: fix spelling in README.md: Peak - Peek (#754)\na46097e docs: make decode impossible to discover before verify\nAdditional commits viewable in compare view\n\n\n\nMaintainer changes\nThis version was pushed to npm by julien.wollscheid, a new releaser for jsonwebtoken since your current version.\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Bump json5 from 1.0.1 to 1.0.2 in /infrastructure/cdk/core", "number": 4401, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4401", "body": "Bumps json5 from 1.0.1 to 1.0.2.\n\nRelease notes\nSourced from json5's releases.\n\nv1.0.2\n\nFix: Properties with the name __proto__ are added to objects and arrays. (#199) This also fixes a prototype pollution vulnerability reported by Jonathan Gregson! (#295). This has been backported to v1. (#298)\n\n\n\n\nChangelog\nSourced from json5's changelog.\n\nUnreleased [code, diff]\nv2.2.3 [code, diff]\n\nFix: json5@2.2.3 is now the 'latest' release according to npm instead of\nv1.0.2. (#299)\n\nv2.2.2 [code, diff]\n\nFix: Properties with the name __proto__ are added to objects and arrays.\n(#199) This also fixes a prototype pollution vulnerability reported by\nJonathan Gregson! (#295).\n\nv2.2.1 [code, diff]\n\nFix: Removed dependence on minimist to patch CVE-2021-44906. (#266)\n\nv2.2.0 [code, diff]\n\nNew: Accurate and documented TypeScript declarations are now included. There\nis no need to install @types/json5. (#236, #244)\n\nv2.1.3 [code, diff]\n\nFix: An out of memory bug when parsing numbers has been fixed. (#228,\n#229)\n\nv2.1.2 [code, diff]\n\n\n... (truncated)\n\n\nCommits\n\na62db1e 1.0.2\ne0c23fe docs: update CHANGELOG for v1.0.2\n62a6540 fix: add proto to objects and arrays\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Bump json5 from 1.0.1 to 1.0.2", "number": 4402, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4402", "body": "Bumps json5 from 1.0.1 to 1.0.2.\n\nRelease notes\nSourced from json5's releases.\n\nv1.0.2\n\nFix: Properties with the name __proto__ are added to objects and arrays. (#199) This also fixes a prototype pollution vulnerability reported by Jonathan Gregson! (#295). This has been backported to v1. (#298)\n\n\n\n\nChangelog\nSourced from json5's changelog.\n\nUnreleased [code, diff]\nv2.2.3 [code, diff]\n\nFix: json5@2.2.3 is now the 'latest' release according to npm instead of\nv1.0.2. (#299)\n\nv2.2.2 [code, diff]\n\nFix: Properties with the name __proto__ are added to objects and arrays.\n(#199) This also fixes a prototype pollution vulnerability reported by\nJonathan Gregson! (#295).\n\nv2.2.1 [code, diff]\n\nFix: Removed dependence on minimist to patch CVE-2021-44906. (#266)\n\nv2.2.0 [code, diff]\n\nNew: Accurate and documented TypeScript declarations are now included. There\nis no need to install @types/json5. (#236, #244)\n\nv2.1.3 [code, diff]\n\nFix: An out of memory bug when parsing numbers has been fixed. (#228,\n#229)\n\nv2.1.2 [code, diff]\n\n\n... (truncated)\n\n\nCommits\n\na62db1e 1.0.2\ne0c23fe docs: update CHANGELOG for v1.0.2\n62a6540 fix: add proto to objects and arrays\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Bump qs and express", "number": 4403, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4403", "body": "Bumps qs and express. These dependencies needed to be updated together.\nUpdates qs from 6.9.6 to 6.11.0\n\nChangelog\nSourced from qs's changelog.\n\n6.11.0\n\n[New] [Fix] stringify: revert 0e903c0; add commaRoundTrip option (#442)\n[readme] fix version badge\n\n6.10.5\n\n[Fix] stringify: with arrayFormat: comma, properly include an explicit [] on a single-item array (#434)\n\n6.10.4\n\n[Fix] stringify: with arrayFormat: comma, include an explicit [] on a single-item array (#441)\n[meta] use npmignore to autogenerate an npmignore file\n[Dev Deps] update eslint, @ljharb/eslint-config, aud, has-symbol, object-inspect, tape\n\n6.10.3\n\n[Fix] parse: ignore __proto__ keys (#428)\n[Robustness] stringify: avoid relying on a global undefined (#427)\n[actions] reuse common workflows\n[Dev Deps] update eslint, @ljharb/eslint-config, object-inspect, tape\n\n6.10.2\n\n[Fix] stringify: actually fix cyclic references (#426)\n[Fix] stringify: avoid encoding arrayformat comma when encodeValuesOnly = true (#424)\n[readme] remove travis badge; add github actions/codecov badges; update URLs\n[Docs] add note and links for coercing primitive values (#408)\n[actions] update codecov uploader\n[actions] update workflows\n[Tests] clean up stringify tests slightly\n[Dev Deps] update eslint, @ljharb/eslint-config, aud, object-inspect, safe-publish-latest, tape\n\n6.10.1\n\n[Fix] stringify: avoid exception on repeated object values (#402)\n\n6.10.0\n\n[New] stringify: throw on cycles, instead of an infinite loop (#395, #394, #393)\n[New] parse: add allowSparse option for collapsing arrays with missing indices (#312)\n[meta] fix README.md (#399)\n[meta] only run npm run dist in publish, not install\n[Dev Deps] update eslint, @ljharb/eslint-config, aud, has-symbols, tape\n[Tests] fix tests on node v0.6\n[Tests] use ljharb/actions/node/install instead of ljharb/actions/node/run\n[Tests] Revert [meta] ignore eclint transitive audit warning\n\n6.9.7\n\n[Fix] parse: ignore __proto__ keys (#428)\n[Fix] stringify: avoid encoding arrayformat comma when encodeValuesOnly = true (#424)\n[Robustness] stringify: avoid relying on a global undefined (#427)\n[readme] remove travis badge; add github actions/codecov badges; update URLs\n[Docs] add note and links for coercing primitive values (#408)\n[Tests] clean up stringify tests slightly\n[meta] fix README.md (#399)\nRevert [meta] ignore eclint transitive audit warning\n\n\n\n... (truncated)\n\n\nCommits\n\n56763c1 v6.11.0\nddd3e29 [readme] fix version badge\nc313472 [New] [Fix] stringify: revert 0e903c0; add commaRoundTrip option\n95bc018 v6.10.5\n0e903c0 [Fix] stringify: with arrayFormat: comma, properly include an explicit `[...\nba9703c v6.10.4\n4e44019 [Fix] stringify: with arrayFormat: comma, include an explicit [] on a s...\n113b990 [Dev Deps] update object-inspect\nc77f38f [Dev Deps] update eslint, @ljharb/eslint-config, aud, has-symbol, tape\n2cf45b2 [meta] use npmignore to autogenerate an npmignore file\nAdditional commits viewable in compare view\n\n\n\nUpdates express from 4.17.2 to 4.18.2\n\nRelease notes\nSourced from express's releases.\n\n4.18.2\n\nFix regression routing a large stack in a single route\ndeps: body-parser@1.20.1\n\ndeps: qs@6.11.0\nperf: remove unnecessary object clone\n\n\ndeps: qs@6.11.0\n\n4.18.1\n\nFix hanging on large stack of sync routes\n\n4.18.0\n\nAdd root option to res.download\nAllow options without filename in res.download\nDeprecate string and non-integer arguments to res.status\nFix behavior of null/undefined as maxAge in res.cookie\nFix handling very large stacks of sync middleware\nIgnore Object.prototype values in settings through app.set/app.get\nInvoke default with same arguments as types in res.format\nSupport proper 205 responses using res.send\nUse http-errors for res.format error\ndeps: body-parser@1.20.0\n\nFix error message for json parse whitespace in strict\nFix internal error when inflated body exceeds limit\nPrevent loss of async hooks context\nPrevent hanging when request already read\ndeps: depd@2.0.0\ndeps: http-errors@2.0.0\ndeps: on-finished@2.4.1\ndeps: qs@6.10.3\ndeps: raw-body@2.5.1\n\n\ndeps: cookie@0.5.0\n\nAdd priority option\nFix expires option to reject invalid dates\n\n\ndeps: depd@2.0.0\n\nReplace internal eval usage with Function constructor\nUse instance methods on process to check for listeners\n\n\ndeps: finalhandler@1.2.0\n\nRemove set content headers that break response\ndeps: on-finished@2.4.1\ndeps: statuses@2.0.1\n\n\ndeps: on-finished@2.4.1\n\nPrevent loss of async hooks context\n\n\ndeps: qs@6.10.3\ndeps: send@0.18.0\n\nFix emitted 416 error missing headers property\nLimit the headers removed for 304 response\ndeps: depd@2.0.0\ndeps: destroy@1.2.0\ndeps: http-errors@2.0.0\ndeps: on-finished@2.4.1\n\n\n\n\n\n... (truncated)\n\n\nChangelog\nSourced from express's changelog.\n\n4.18.2 / 2022-10-08\n\nFix regression routing a large stack in a single route\ndeps: body-parser@1.20.1\n\ndeps: qs@6.11.0\nperf: remove unnecessary object clone\n\n\ndeps: qs@6.11.0\n\n4.18.1 / 2022-04-29\n\nFix hanging on large stack of sync routes\n\n4.18.0 / 2022-04-25\n\nAdd root option to res.download\nAllow options without filename in res.download\nDeprecate string and non-integer arguments to res.status\nFix behavior of null/undefined as maxAge in res.cookie\nFix handling very large stacks of sync middleware\nIgnore Object.prototype values in settings through app.set/app.get\nInvoke default with same arguments as types in res.format\nSupport proper 205 responses using res.send\nUse http-errors for res.format error\ndeps: body-parser@1.20.0\n\nFix error message for json parse whitespace in strict\nFix internal error when inflated body exceeds limit\nPrevent loss of async hooks context\nPrevent hanging when request already read\ndeps: depd@2.0.0\ndeps: http-errors@2.0.0\ndeps: on-finished@2.4.1\ndeps: qs@6.10.3\ndeps: raw-body@2.5.1\n\n\ndeps: cookie@0.5.0\n\nAdd priority option\nFix expires option to reject invalid dates\n\n\ndeps: depd@2.0.0\n\nReplace internal eval usage with Function constructor\nUse instance methods on process to check for listeners\n\n\ndeps: finalhandler@1.2.0\n\nRemove set content headers that break response\ndeps: on-finished@2.4.1\ndeps: statuses@2.0.1\n\n\ndeps: on-finished@2.4.1\n\nPrevent loss of async hooks context\n\n\ndeps: qs@6.10.3\ndeps: send@0.18.0\n\n\n\n... (truncated)\n\n\nCommits\n\n8368dc1 4.18.2\n61f4049 docs: replace Freenode with Libera Chat\nbb7907b build: Node.js@18.10\nf56ce73 build: supertest@6.3.0\n24b3dc5 deps: qs@6.11.0\n689d175 deps: body-parser@1.20.1\n340be0f build: eslint@8.24.0\n33e8dc3 docs: use Node.js name style\n644f646 build: supertest@6.2.4\necd7572 build: Node.js@14.20\nAdditional commits viewable in compare view\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "limit common", "number": 4404, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4404"}
{"title": "trying to fix alb security group in Dev.", "number": 4405, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4405"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4405#pullrequestreview-1269942187", "body": ""}
{"comment": {"body": "Double dupe? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4405#discussion_r1087060766"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4405#pullrequestreview-1269942644", "body": ""}
{"comment": {"body": "No. AWS tags are case sensitive. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4405#discussion_r1087061107"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4405#pullrequestreview-1269942924", "body": ""}
{"comment": {"body": "Just trying to avoid making multiple PRs ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4405#discussion_r1087061292"}}
{"title": "Backfill company", "number": 4406, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406", "body": "Basic model for company.\nAdded migration to backfill companies but no entry point from admin console yet.\nCompany creation still needs to occur on Team creation. Requires refactoring TeamDAO.new call sites which may conflict with @richiebres changes?"}
{"comment": {"body": "@jeffrey-ng take a look at where teams are created here:\r\n- `GitHubInstallationHandler.upsertTeam()` -- webhook install\r\n- `GitHubInstallationMaintenanceJob.handleInstall()` -- background sync\r\n\r\nYou need to upsert a company in both cases, and add the company reference to team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#issuecomment-1404318932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#pullrequestreview-1269962898", "body": ""}
{"comment": {"body": "needs test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#discussion_r1087077030"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#pullrequestreview-1270001739", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#pullrequestreview-1270002094", "body": ""}
{"comment": {"body": "Added", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#discussion_r1087101931"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#pullrequestreview-1270082077", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#pullrequestreview-1270082863", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#pullrequestreview-1270195934", "body": ""}
{"comment": {"body": "Remember to make this a required reference in follow up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4406#discussion_r1087227594"}}
{"title": "Bump decode-uri-component from 0.2.0 to 0.2.2", "number": 4407, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4407", "body": "Bumps decode-uri-component from 0.2.0 to 0.2.2.\n\nRelease notes\nSourced from decode-uri-component's releases.\n\nv0.2.2\n\nPrevent overwriting previously decoded tokens  980e0bf\n\n\nv0.2.1\n\nSwitch to GitHub workflows  76abc93\nFix issue where decode throws - fixes #6  746ca5d\nUpdate license (#1)  486d7e2\nTidelift tasks  a650457\nMeta tweaks  66e1c28\n\n\n\n\n\nCommits\n\na0eea46 0.2.2\n980e0bf Prevent overwriting previously decoded tokens\n3c8a373 0.2.1\n76abc93 Switch to GitHub workflows\n746ca5d Fix issue where decode throws - fixes #6\n486d7e2 Update license (#1)\na650457 Tidelift tasks\n66e1c28 Meta tweaks\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Bump loader-utils from 1.4.0 to 1.4.2", "number": 4408, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4408", "body": "Bumps loader-utils from 1.4.0 to 1.4.2.\n\nRelease notes\nSourced from loader-utils's releases.\n\nv1.4.2\n1.4.2 (2022-11-11)\nBug Fixes\n\nReDoS problem (#226) (17cbf8f)\n\nv1.4.1\n1.4.1 (2022-11-07)\nBug Fixes\n\nsecurity problem (#220) (4504e34)\n\n\n\n\nChangelog\nSourced from loader-utils's changelog.\n\n1.4.2 (2022-11-11)\nBug Fixes\n\nReDoS problem (#226) (17cbf8f)\n\n1.4.1 (2022-11-07)\nBug Fixes\n\nsecurity problem (#220) (4504e34)\n\n\n\n\n\nCommits\n\n331ad50 chore(release): 1.4.2\n17cbf8f fix: ReDoS problem (#226)\n8f082b3 chore(release): 1.4.1\n4504e34 fix: security problem (#220)\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Bump minimatch from 3.0.4 to 3.1.2", "number": 4409, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4409", "body": "Bumps minimatch from 3.0.4 to 3.1.2.\n\nCommits\n\n699c459 3.1.2\n2f2b5ff fix: trim pattern\n25d7c0d 3.1.1\n55dda29 fix: treat nocase:true as always having magic\n5e1fb8d 3.1.0\nf8145c5 Add 'allowWindowsEscape' option\n570e8b1 add publishConfig for v3 publishes\n5b7cd33 3.0.6\n20b4b56 [fix] revert all breaking syntax changes\n2ff0388 document, expose, and test 'partial:true' option\nAdditional commits viewable in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Allow local changes to DataCacheStore", "number": 441, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/441", "body": "This allows making local changes to DataCacheStore safely.  The local data is treated as an overlay on the remove data.  There are two APIs added to DataCacheStore:\nstore.overlayValues(values) = unwindFn -- this adds values to the local overlay values.  This returns a function that can be called to safely \"unwind\" the overlay.  The intention is that the unwind function can be called when a related API function fails, as the overlay values will no longer be relevant:\nconst unwindFn = dogStore.overlayValues([newDog]);\ntry {\n   API.dogs.createDog(newDog);\n}\ncatch (error) {\n  unwindFn();\n}\nstore.withOverlayValues(values, asyncFn) -- this adds 'values' to the local overlay values while the asyncFn is operating.  If asyncFn throws an error, the values are unwound.  This is a terser form of the above:\ndogStore.withOverlayValues([newDog], API.dogs.createDog(newDog))"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/441#pullrequestreview-895865344", "body": ""}
{"comment": {"body": "FYI Jeff I made the data immutable, it always return a new merged list instead of merging inline.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/441#discussion_r816455599"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/441#pullrequestreview-896905915", "body": ""}
{"title": "Bump deep-object-diff from 1.1.7 to 1.1.9", "number": 4410, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4410", "body": "Bumps deep-object-diff from 1.1.7 to 1.1.9.\n\nRelease notes\nSourced from deep-object-diff's releases.\n\nv1.1.9\nVulnerability patch\nDetails outlined here: #85. TLDR: The prototype of the returned diff object could be polluted but not globally on all objects.\nFix: mattphillips/deep-object-diff#87\nThanks @Retr02332 for highlighting the issue and validating the fix.\nThis vulnerability was introduced in \nv1.1.8\nPatch\n\nFix typings resolution when using TypeScript 4.7+ with ESM #83\nimprove return type for detailedDiff #72\n\nCredits\nThanks @Nitive and @icholy for your PRs\n\n\n\nCommits\n\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Bump terser from 4.8.0 to 4.8.1", "number": 4411, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4411", "body": "Bumps terser from 4.8.0 to 4.8.1.\n\nChangelog\nSourced from terser's changelog.\n\nv4.8.1 (backport)\n\nSecurity fix for RegExps that should not be evaluated (regexp DDOS)\n\n\n\n\nCommits\n\n40674a4 update changelog, version\nd8cc569 backport fix to potential regexp DDOS\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Give search service access to redis", "number": 4412, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4412"}
{"comment": {"body": "Several things need to be done:\r\n1. You will need to modify the helm charts as well.\r\n2. Kubernetes changes need to be done on your computer (the ekstcl files...). It cannot be automated. I'll take care of that when you push this in. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4412#issuecomment-1404117592"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4412#pullrequestreview-1269983093", "body": "Looks good, submit"}
{"title": "Bump async from 2.6.3 to 2.6.4", "number": 4413, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4413", "body": "Bumps async from 2.6.3 to 2.6.4.\n\nChangelog\nSourced from async's changelog.\n\nv2.6.4\n\nFix potential prototype pollution exploit (#1828)\n\n\n\n\nCommits\n\nc6bdaca Version 2.6.4\n8870da9 Update built files\n4df6754 update changelog\n8f7f903 Fix prototype pollution vulnerability (#1828)\nSee full diff in compare view\n\n\n\nMaintainer changes\nThis version was pushed to npm by hargasinski, a new releaser for async since your current version.\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Bump protobufjs from 6.11.2 to 6.11.3", "number": 4414, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4414", "body": "Bumps protobufjs from 6.11.2 to 6.11.3.\n\nChangelog\nSourced from protobufjs's changelog.\n\n6.11.3 (2022-05-20)\nBug Fixes\n\ndeps: use eslint 8.x (#1728) (a8681ce)\ndo not let setProperty change the prototype (#1731) (b5f1391)\n\n\n\n\nCommits\n\nb130dfd chore(6.x): release 6.11.3 (#1737)\nc2c17ae build: publish to main\nb2c6a5c build: run tests if ci label added (#1734)\na8681ce fix(deps): use eslint 8.x (#1728)\nb5f1391 fix: do not let setProperty change the prototype (#1731)\n7afd0a3 build: configure 6.x as default branch\n37285d0 build: configure backports\nSee full diff in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Bump minimist from 1.2.5 to 1.2.7", "number": 4415, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4415", "body": "Bumps minimist from 1.2.5 to 1.2.7.\n\nChangelog\nSourced from minimist's changelog.\n\nv1.2.7 - 2022-10-10\nCommits\n\n[meta] add auto-changelog 0ebf4eb\n[actions] add reusable workflows e115b63\n[eslint] add eslint; rules to enable later are warnings f58745b\n[Dev Deps] switch from covert to nyc ab03356\n[readme] rename and add badges 236f4a0\n[meta] create FUNDING.yml; add funding in package.json 783a49b\n[meta] use npmignore to autogenerate an npmignore file f81ece6\nOnly apps should have lockfiles 56cad44\n[Dev Deps] update covert, tape; remove unnecessary tap 49c5f9f\n[Tests] add aud in posttest 228ae93\n[meta] add safe-publish-latest 01fc23f\n[meta] update repo URLs 6b164c7\n\nv1.2.6 - 2022-03-21\nCommits\n\ntest from prototype pollution PR bc8ecee\nisConstructorOrProto adapted from PR c2b9819\nsecurity notice for additional prototype pollution issue ef88b93\n\n\n\n\nCommits\n\nc590d75 v1.2.7\n0ebf4eb [meta] add auto-changelog\ne115b63 [actions] add reusable workflows\n01fc23f [meta] add safe-publish-latest\nf58745b [eslint] add eslint; rules to enable later are warnings\n228ae93 [Tests] add aud in posttest\n236f4a0 [readme] rename and add badges\nab03356 [Dev Deps] switch from covert to nyc\n49c5f9f [Dev Deps] update covert, tape; remove unnecessary tap\n783a49b [meta] create FUNDING.yml; add funding in package.json\nAdditional commits viewable in compare view\n\n\n\nMaintainer changes\nThis version was pushed to npm by ljharb, a new releaser for minimist since your current version.\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Bump jpeg-js and @jimp/jpeg", "number": 4416, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4416", "body": "Bumps jpeg-js and @jimp/jpeg. These dependencies needed to be updated together.\nUpdates jpeg-js from 0.4.2 to 0.4.4\n\nRelease notes\nSourced from jpeg-js's releases.\n\nv0.4.4\n\nv0.4.4 (2022-06-07)\n\nfeat: add comment tag encoding (#87) (13e1ffa), closes #87\nfix: validate sampling factors (#106) (9ccd35f), closes #106\nfix(decoder): rethrow a more helpful error if Buffer is undefined (#93) (b58cc11), closes #93\nchore(ci): migrate to github actions (#86) (417e8e2), closes #86\nchore(deps): bump y18n from 4.0.0 to 4.0.3 (#98) (2c90858), closes #98\nchore(deps): bump ws from 7.2.3 to 7.4.6 (#91) (fd73289), closes #91\nchore(deps): bump hosted-git-info from 2.8.8 to 2.8.9 (#90) (9449a8b), closes #90\nchore(deps): bump lodash from 4.17.15 to 4.17.21 (#89) (ffdc4a4), closes #89\n\nv0.4.3\n\nv0.4.3 (2021-01-11)\n\nfix: handle 0x00E1 / 0x00E0 segments from Pixel phones (#84) (a2d7ed9), closes #84\n\n\n\n\nCommits\n\n9ccd35f fix: validate sampling factors (#106)\nb58cc11 fix(decoder): rethrow a more helpful error if Buffer is undefined (#93)\n2c90858 chore(deps): bump y18n from 4.0.0 to 4.0.3 (#98)\nfd73289 chore(deps): bump ws from 7.2.3 to 7.4.6 (#91)\n9449a8b chore(deps): bump hosted-git-info from 2.8.8 to 2.8.9 (#90)\nffdc4a4 chore(deps): bump lodash from 4.17.15 to 4.17.21 (#89)\n13e1ffa feat: add comment tag encoding (#87)\n417e8e2 chore(ci): migrate to github actions (#86)\na2d7ed9 fix: handle 0x00E1 / 0x00E0 segments from Pixel phones (#84)\nSee full diff in compare view\n\n\n\nUpdates @jimp/jpeg from 0.16.1 to 0.16.2\n\nRelease notes\nSourced from @jimp/jpeg's releases.\n\nv0.16.2\n Bug Fix\n\n@jimp/jpeg\n\nBump jpeg-js over 0.4.4 to avoid cve-2022-25851 #1093 (@melhadad)\n\n\n\n Documentation\n\ndocs: toc added for easier reading #984 (@j-d-carmichael)\nfeat: add handwritten.js project #946 (@alias-rahil)\n@jimp/plugin-fisheye\n\nadded the e back to @jimp/plugin-fisheye #947 (@mynameismax)\n\n\n\nAuthors: <AUTHORS>
{"title": "Bump prismjs and refractor", "number": 4417, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4417", "body": "Bumps prismjs and refractor. These dependencies needed to be updated together.\nUpdates prismjs from 1.26.0 to 1.29.0\n\nRelease notes\nSourced from prismjs's releases.\n\nv1.29.0\nRelease 1.29.0\nv1.28.0\nRelease 1.28.0\nv1.27.0\nRelease 1.27.0\n\n\n\nChangelog\nSourced from prismjs's changelog.\n\n1.29.0 (2022-08-23)\nNew components\n\nBBj (#3511) 1134bdfc\nBQN (#3515) 859f99a0\nCilk/C  Cilk/C++ (#3522) c8462a29\nGradle (#3443) ********\nMETAFONT (#3465) 2815f699\nWGSL (#3455) 4c87d418\n\nUpdated components\n\nAsciiDoc\n\nSome regexes are too greedy (#3481) c4cbeeaa\n\n\nBash\n\nAdded sh alias (#3509) 6b824d47\nAdded support for parameters and the java and sysctl commands. (#3505) b9512b22\nAdded cargo command (#3488) 3e937137\n\n\nBBj\n\nImprove regexes (#3512) 0cad9ae5\n\n\nCSS\n\nFixed @-rules not accounting for strings (#3438) 0d4b6cb6\n\n\nCSS Extras\n\nAdded support for RebeccaPurple color (#3448) 646b2e0a\n\n\nHoon\n\nFixed escaped strings (#3473) ********\n\n\nJava\n\nAdded support for constants (#3507) 342a0039\n\n\nMarkup\n\nFixed quotes in HTML attribute values (#3442) ca8eaeee\n\n\nNSIS\n\nAdded missing commands (#3504) b0c2a9b4\n\n\nScala\n\nUpdated keywords to support Scala 3 (#3506) a090d063\n\n\nSCSS\n\nFix casing in title of the scss lang (#3501) 2aed9ce7\n\n\n\nUpdated plugins\n\nLine Highlight\n\nAccount for offset when clamping ranges (#3518) 098e3000\nIgnore ranges outside of actual lines (#3475) 9a4e725b\n\n\nNormalize Whitespace\n\nAdd configuration via attributes (#3467) 91dea0c8\n\n\n\nOther\n\nAdded security policy (#3070) 05ee042a\nAdded list of maintainers (#3410) 866b302e\n\n\n\n... (truncated)\n\n\nCommits\n\n59e5a34 1.29.0\ncd080f2 Updated npmignore to include new MD files (#3534)\n751664b Added PR stop notice (#3532)\n248f6ab Added changelog for v1.29.0 (#3533)\n098e300 Line Highlight: Account for offset when clamping ranges (#3518)\n6b824d4 Bash: Added sh alias (#3509)\n15272f7 Website: Added third-party tutorial for Pug template (#3459)\nc8462a2 Cilk: Add support for Cilk (with C/C++) (#3522)\n859f99a Added bqn language support (#3515)\n0cad9ae BBj: Improve regexes (#3512)\nAdditional commits viewable in compare view\n\n\n\nUpdates refractor from 3.5.0 to 3.6.0\n\nRelease notes\nSourced from refractor's releases.\n\n3.6.0\n\nfbc9422 v3: Update Prism to 1.27.0\n\nFull Changelog: \n\n\n\nCommits\n\nfbb7605 3.6.0\nfbc9422 v3: Update Prism to 1.27.0\nSee full diff in compare view\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "test gradle dependnecies", "number": 4418, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4418"}
{"title": "Bump follow-redirects from 1.14.7 to 1.15.2", "number": 4419, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4419", "body": "Bumps follow-redirects from 1.14.7 to 1.15.2.\n\nCommits\n\n9655237 Release version 1.15.2 of the npm package.\n6e2b86d Default to localhost if no host given.\n449e895 Throw invalid URL error on relative URLs.\ne30137c Use type functions.\n76ea31f ternary operator syntax fix\n84c00b0 HTTP header lines are separated by CRLF.\nd28bcbf Create SECURITY.md (#202)\n62a551c Release version 1.15.1 of the npm package.\n7fe0779 Use for ... of.\n948c30c Fix redirecting to relative URL when using proxy\nAdditional commits viewable in compare view\n\n\n\n\nDependabot will resolve any conflicts with this PR as long as you don't alter it yourself. You can also trigger a rebase manually by commenting @dependabot rebase.\n\n\nDependabot commands and options\n\n\nYou can trigger Dependabot actions by commenting on this PR:\n- `@dependabot rebase` will rebase this PR\n- `@dependabot recreate` will recreate this PR, overwriting any edits that have been made to it\n- `@dependabot merge` will merge this PR after your CI passes on it\n- `@dependabot squash and merge` will squash and merge this PR after your CI passes on it\n- `@dependabot cancel merge` will cancel a previously requested merge and block automerging\n- `@dependabot reopen` will reopen this PR if it is closed\n- `@dependabot close` will close this PR and stop Dependabot recreating it. You can achieve the same result by closing it manually\n- `@dependabot ignore this major version` will close this PR and stop Dependabot creating any more for this major version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this minor version` will close this PR and stop Dependabot creating any more for this minor version (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot ignore this dependency` will close this PR and stop Dependabot creating any more for this dependency (unless you reopen the PR or upgrade to it yourself)\n- `@dependabot use these labels` will set the current labels as the default for future PRs for this repo and language\n- `@dependabot use these reviewers` will set the current reviewers as the default for future PRs for this repo and language\n- `@dependabot use these assignees` will set the current assignees as the default for future PRs for this repo and language\n- `@dependabot use this milestone` will set the current milestone as the default for future PRs for this repo and language\n\nYou can disable automated security fix PRs for this repo from the [Security Alerts page]().\n\n"}
{"title": "Introduce Unread API", "number": 442, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442", "body": "New APIs\n- Get the unread status for all threads.\n- Get the unread status of one thread.\n- Update the unread status of one thread."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#pullrequestreview-896588828", "body": "Not sure how it's the get all unreads operation is going to work but I'm assuming you'll filter on the person making the api call?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#pullrequestreview-896609699", "body": ""}
{"comment": {"body": "Might be an overly eager optimization but I wonder if the Unread object should contain the last message content so that the client doesn't have to do a bunch of queries to render the preview text", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#discussion_r816987300"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#pullrequestreview-896609815", "body": ""}
{"comment": {"body": "@matthewjamesadam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#discussion_r816987410"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#pullrequestreview-896611192", "body": ""}
{"comment": {"body": "In practice it's highly unlikely that more than 1 thread will have its unread status updated at a time...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#discussion_r816988379"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#pullrequestreview-896619706", "body": ""}
{"comment": {"body": "The message may have already been loaded by the message subscriber.\r\n\r\nI think it might be tricky for the client-side message store to subscribe to two streams at the same time right?\r\n- `/threads/:id/messages` \u2192 `[Message]`\r\n- `/threads/:id/unread` \u2192 `Unread { Message }`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#discussion_r816994788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#pullrequestreview-896674474", "body": ""}
{"comment": {"body": "Yeah merging data between two sources in this way will make things more complicated and feels like an over-optimization at this point.  For now I'd vote to keep things simple -- message API gives you messages, unread API gives you unread pointers.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/442#discussion_r817034348"}}
{"title": "Cache team members for search autocomplete", "number": 4420, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4420"}
{"title": "Configure Renovate", "number": 4421, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4421", "body": "Welcome to Renovate! This is an onboarding PR to help you understand and configure settings before regular Pull Requests begin.\n To activate Renovate, merge this Pull Request. To disable Renovate, simply close this Pull Request unmerged.\n\nDetected Package Files\n\ndocker-compose-ci.yml (docker-compose)\ndocker-compose-local-aws.yml (docker-compose)\ndocker-compose-local-intellij.yml (docker-compose)\ndocker-compose-local.yml (docker-compose)\nactivemq/Dockerfile (dockerfile)\ninfrastructure/cdk/core/assets/image/topics-data-pipeline/bert-trainer-image/Dockerfile (dockerfile)\ninfrastructure/cdk/core/assets/image/topics-data-pipeline/decompress-processor-image/Dockerfile (dockerfile)\ninfrastructure/cdk/core/assets/image/topics-data-pipeline/folder-processor-image/Dockerfile (dockerfile)\ninfrastructure/cdk/core/assets/image/topics-data-pipeline/histogram-trainer-image/Dockerfile (dockerfile)\ninfrastructure/cdk/core/assets/image/topics-data-pipeline/powerml-trainer-image/Dockerfile (dockerfile)\ninfrastructure/cdk/core/assets/image/topics-data-pipeline/sanitizer-processor-image/Dockerfile (dockerfile)\ninfrastructure/cdk/core/assets/lambda/s3-video-transcoding-trigger/Dockerfile (dockerfile)\ninfrastructure/cdk/core/assets/lambda/topics-data-pipeline/trigger-topics-ingestion/Dockerfile (dockerfile)\nkotlin/template/service/Dockerfile.j2 (dockerfile)\nprojects/services/adminwebservice/Dockerfile (dockerfile)\nprojects/services/apiservice/Dockerfile (dockerfile)\nprojects/services/assetservice/Dockerfile (dockerfile)\nprojects/services/authservice/Dockerfile (dockerfile)\nprojects/services/folderdataservice/Dockerfile (dockerfile)\nprojects/services/monkeyservice/Dockerfile (dockerfile)\nprojects/services/notificationservice/Dockerfile (dockerfile)\nprojects/services/pusherservice/Dockerfile (dockerfile)\nprojects/services/scmdataservice/Dockerfile (dockerfile)\nprojects/services/scmservice/Dockerfile (dockerfile)\nprojects/services/searchservice/Dockerfile (dockerfile)\nprojects/services/slackdataservice/Dockerfile (dockerfile)\nprojects/services/slackservice/Dockerfile (dockerfile)\nprojects/services/telemetryservice/Dockerfile (dockerfile)\nprojects/services/topicservice/Dockerfile (dockerfile)\nprojects/services/transcriptionservice/Dockerfile (dockerfile)\nprojects/services/videoservice/Dockerfile (dockerfile)\nprojects/services/webhookservice/Dockerfile (dockerfile)\nsource-agent/Dockerfile (dockerfile)\n.github/actions/ci-docker-ecr-publish/action.yaml (github-actions)\n.github/actions/ci-service-k8s-deploy/action.yaml (github-actions)\n.github/actions/ci-services-smoke-test/action.yaml (github-actions)\n.github/actions/publish-notify-slack/action.yaml (github-actions)\n.github/workflows/ci-build-macos.yml (github-actions)\n.github/workflows/ci-build-vscode.yml (github-actions)\n.github/workflows/ci-build-web-extension.yml (github-actions)\n.github/workflows/ci-diff-swift-codegen.yml (github-actions)\n.github/workflows/ci-infra-deploy.yml (github-actions)\n.github/workflows/ci-infra.yml (github-actions)\n.github/workflows/ci-installer.yml (github-actions)\n.github/workflows/ci-java-toolcache.yml (github-actions)\n.github/workflows/ci-macos-pr.yml (github-actions)\n.github/workflows/ci-macos-with-notarization.yml (github-actions)\n.github/workflows/ci-service-deploy.yml (github-actions)\n.github/workflows/ci-service-package.yml (github-actions)\n.github/workflows/ci-services.yml (github-actions)\n.github/workflows/ci-sharedWeb.yml (github-actions)\n.github/workflows/ci-source-agent.yml (github-actions)\n.github/workflows/ci-vscode.yml (github-actions)\n.github/workflows/ci-web-extension.yml (github-actions)\n.github/workflows/ci-web.yml (github-actions)\ngradle.properties (gradle)\nsettings.gradle.kts (gradle)\nbuild.gradle.kts (gradle)\napi/build.gradle.kts (gradle)\nbuildSrc/gradle.properties (gradle)\nbuildSrc/settings.gradle.kts (gradle)\nbuildSrc/build.gradle.kts (gradle)\ncommon/build.gradle.kts (gradle)\ncustom-ktlint-rules/build.gradle.kts (gradle)\ngradle/libs.versions.toml (gradle)\ngradle/test.libs.versions.toml (gradle)\nprojects/apps/slackanalysis/build.gradle.kts (gradle)\nprojects/apps/slackextractor/build.gradle.kts (gradle)\nprojects/apps/slackindexer/build.gradle.kts (gradle)\nprojects/apps/slackquery/build.gradle.kts (gradle)\nprojects/apps/sourceindexer/build.gradle.kts (gradle)\nprojects/apps/sourcequery/build.gradle.kts (gradle)\nprojects/clients/client-activemq/build.gradle.kts (gradle)\nprojects/clients/client-assemblyai/build.gradle.kts (gradle)\nprojects/clients/client-intercom/build.gradle.kts (gradle)\nprojects/clients/client-redis/build.gradle.kts (gradle)\nprojects/clients/client-scm/build.gradle.kts (gradle)\nprojects/clients/client-sendinblue/build.gradle.kts (gradle)\nprojects/clients/client-slack/build.gradle.kts (gradle)\nprojects/clients/client-video/build.gradle.kts (gradle)\nprojects/libs/lib-api/build.gradle.kts (gradle)\nprojects/libs/lib-api-generated/build.gradle.kts (gradle)\nprojects/libs/lib-api-model/build.gradle.kts (gradle)\nprojects/libs/lib-asset/build.gradle.kts (gradle)\nprojects/libs/lib-auth/build.gradle.kts (gradle)\nprojects/libs/lib-aws/build.gradle.kts (gradle)\nprojects/libs/lib-cache/build.gradle.kts (gradle)\nprojects/libs/lib-client-config/build.gradle.kts (gradle)\nprojects/libs/lib-common/build.gradle.kts (gradle)\nprojects/libs/lib-compress/build.gradle.kts (gradle)\nprojects/libs/lib-config/build.gradle.kts (gradle)\nprojects/libs/lib-emoji/build.gradle.kts (gradle)\nprojects/libs/lib-environment/build.gradle.kts (gradle)\nprojects/libs/lib-event-queue/build.gradle.kts (gradle)\nprojects/libs/lib-experts/build.gradle.kts (gradle)\nprojects/libs/lib-folder-data/build.gradle.kts (gradle)\nprojects/libs/lib-git/build.gradle.kts (gradle)\nprojects/libs/lib-ingestion/build.gradle.kts (gradle)\nprojects/libs/lib-insider/build.gradle.kts (gradle)\nprojects/libs/lib-intercom/build.gradle.kts (gradle)\nprojects/libs/lib-intercom-service/build.gradle.kts (gradle)\nprojects/libs/lib-job/build.gradle.kts (gradle)\nprojects/libs/lib-ktor/build.gradle.kts (gradle)\nprojects/libs/lib-log/build.gradle.kts (gradle)\nprojects/libs/lib-log-kotlin/build.gradle.kts (gradle)\nprojects/libs/lib-lucene/build.gradle.kts (gradle)\nprojects/libs/lib-markdown/build.gradle.kts (gradle)\nprojects/libs/lib-network/build.gradle.kts (gradle)\nprojects/libs/lib-notification/build.gradle.kts (gradle)\nprojects/libs/lib-pringestion/build.gradle.kts (gradle)\nprojects/libs/lib-proto-generated/build.gradle.kts (gradle)\nprojects/libs/lib-recommendation/build.gradle.kts (gradle)\nprojects/libs/lib-scm/build.gradle.kts (gradle)\nprojects/libs/lib-scm-data/build.gradle.kts (gradle)\nprojects/libs/lib-search/build.gradle.kts (gradle)\nprojects/libs/lib-security/build.gradle.kts (gradle)\nprojects/libs/lib-sendinblue/build.gradle.kts (gradle)\nprojects/libs/lib-service/build.gradle.kts (gradle)\nprojects/libs/lib-slack/build.gradle.kts (gradle)\nprojects/libs/lib-slack-data/build.gradle.kts (gradle)\nprojects/libs/lib-slack-extractor/build.gradle.kts (gradle)\nprojects/libs/lib-slack-ingestion/build.gradle.kts (gradle)\nprojects/libs/lib-slack-webhook/build.gradle.kts (gradle)\nprojects/libs/lib-sourcemark/build.gradle.kts (gradle)\nprojects/libs/lib-topic/build.gradle.kts (gradle)\nprojects/libs/lib-topic-extractor/build.gradle.kts (gradle)\nprojects/libs/lib-trace/build.gradle.kts (gradle)\nprojects/libs/lib-trace-jdbc/build.gradle.kts (gradle)\nprojects/libs/lib-trace-ktor/build.gradle.kts (gradle)\nprojects/libs/lib-trace-redis/build.gradle.kts (gradle)\nprojects/libs/lib-trace-service/build.gradle.kts (gradle)\nprojects/libs/lib-transcription/build.gradle.kts (gradle)\nprojects/libs/lib-user-engagement/build.gradle.kts (gradle)\nprojects/libs/lib-user-secret/build.gradle.kts (gradle)\nprojects/libs/lib-versions/build.gradle.kts (gradle)\nprojects/libs/lib-video/build.gradle.kts (gradle)\nprojects/libs/lib-video-transcoder/build.gradle.kts (gradle)\nprojects/models/build.gradle.kts (gradle)\nprojects/scripts/build.gradle.kts (gradle)\nprojects/services/adminwebservice/build.gradle.kts (gradle)\nprojects/services/apiservice/build.gradle.kts (gradle)\nprojects/services/assetservice/build.gradle.kts (gradle)\nprojects/services/authservice/build.gradle.kts (gradle)\nprojects/services/folderdataservice/build.gradle.kts (gradle)\nprojects/services/monkeyservice/build.gradle.kts (gradle)\nprojects/services/notificationservice/build.gradle.kts (gradle)\nprojects/services/pusherservice/build.gradle.kts (gradle)\nprojects/services/scmdataservice/build.gradle.kts (gradle)\nprojects/services/scmservice/build.gradle.kts (gradle)\nprojects/services/searchservice/build.gradle.kts (gradle)\nprojects/services/slackdataservice/build.gradle.kts (gradle)\nprojects/services/slackservice/build.gradle.kts (gradle)\nprojects/services/telemetryservice/build.gradle.kts (gradle)\nprojects/services/topicservice/build.gradle.kts (gradle)\nprojects/services/transcriptionservice/build.gradle.kts (gradle)\nprojects/services/videoservice/build.gradle.kts (gradle)\nprojects/services/webhookservice/build.gradle.kts (gradle)\ngradle/wrapper/gradle-wrapper.properties (gradle-wrapper)\nhelm/baseservice/values.yaml (helm-values)\nhelm/scaffold/Chart.yaml (helmv3)\nprojects/services/adminwebservice/.helm/adminwebservice/Chart.yaml (helmv3)\nprojects/services/apiservice/.helm/apiservice/Chart.yaml (helmv3)\nprojects/services/assetservice/.helm/assetservice/Chart.yaml (helmv3)\nprojects/services/authservice/.helm/authservice/Chart.yaml (helmv3)\nprojects/services/folderdataservice/.helm/folderdataservice/Chart.yaml (helmv3)\nprojects/services/monkeyservice/.helm/monkeyservice/Chart.yaml (helmv3)\nprojects/services/notificationservice/.helm/notificationservice/Chart.yaml (helmv3)\nprojects/services/pusherservice/.helm/pusherservice/Chart.yaml (helmv3)\nprojects/services/scmdataservice/.helm/scmdataservice/Chart.yaml (helmv3)\nprojects/services/scmservice/.helm/scmservice/Chart.yaml (helmv3)\nprojects/services/searchservice/.helm/searchservice/Chart.yaml (helmv3)\nprojects/services/slackdataservice/.helm/slackdataservice/Chart.yaml (helmv3)\nprojects/services/slackservice/.helm/slackservice/Chart.yaml (helmv3)\nprojects/services/telemetryservice/.helm/telemetryservice/Chart.yaml (helmv3)\nprojects/services/topicservice/.helm/topicservice/Chart.yaml (helmv3)\nprojects/services/transcriptionservice/.helm/transcriptionservice/Chart.yaml (helmv3)\nprojects/services/videoservice/.helm/videoservice/Chart.yaml (helmv3)\nprojects/services/webhookservice/.helm/webhookservice/Chart.yaml (helmv3)\nemails/sandbox/sandbox.html (html)\ncommon/package.json (npm)\nemails/sandbox/libs/text/package.json (npm)\ninfrastructure/cdk/core/assets/lambda/cloudfront-edge-assets-auth/package.json (npm)\ninfrastructure/cdk/core/assets/lambda/cloudfront-edge-assets-origin-lookup/package.json (npm)\ninfrastructure/cdk/core/assets/lambda/cloudfront-edge-custom-error/package.json (npm)\ninfrastructure/cdk/core/assets/lambda/cloudfront-edge-path-rewrite/package.json (npm)\ninfrastructure/cdk/core/assets/lambda/cloudfront-edge-www-redirect/package.json (npm)\ninfrastructure/cdk/core/assets/lambda/integration-tests-cloud-front-edge-assets/package.json (npm)\ninfrastructure/cdk/core/package.json (npm)\npackage.json (npm)\nshared/package.json (npm)\nsource-agent/package.json (npm)\nvscode/package.json (npm)\nweb-extension/package.json (npm)\nweb/package.json (npm)\npython/pip/secrets-requirements.txt (pip_requirements)\nvideo-app/macos/BuildTools/Package.swift (swift)\nvideo-app/macos/generated/api/Package.swift (swift)\n\nConfiguration\n Renovate has detected a custom config for this PR. Feel free to ask for help if you have any doubts and would like it reviewed.\nImportant: Now that this branch is edited, Renovate can't rebase it from the base branch any more. If you make changes to the base branch that could impact this onboarding PR, please merge them manually.\nWhat to Expect\nWith your current configuration, Renovate will create 88 Pull Requests:\n\nchore(deps): update definitelytyped\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/definitelytyped`\n  - Merge into: `main`\n  - Upgrade [@types/chrome]() to `^0.0.210`\n  - Upgrade [@types/isomorphic-fetch]() to `^0.0.36`\n  - Upgrade [@types/webextension-polyfill]() to `^0.10.0`\n\n\n\n\n\nchore(deps): update dependency @storybook/testing-library to ^0.0.13\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/storybook-testing-library-0.x`\n  - Merge into: `main`\n  - Upgrade [@storybook/testing-library]() to `^0.0.13`\n\n\n\n\n\nchore(deps): update dependency com.github.johnrengelman.shadow to v7.1.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/shadowjarpluginversion`\n  - Merge into: `main`\n  - Upgrade com.github.johnrengelman.shadow to `7.1.2`\n\n\n\n\n\nchore(deps): update dependency flight-school/anycodable to from: \"0.6.7\"\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/flight-school-anycodable-0.x`\n  - Merge into: `main`\n  - Upgrade [Flight-School/AnyCodable]() to `1312a5667aafc818a51549161aa78af4e6767396`\n\n\n\n\n\nchore(deps): update frankie567/grafana-annotation-action action to v1.0.3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/frankie567-grafana-annotation-action-1.x`\n  - Merge into: `main`\n  - Upgrade [frankie567/grafana-annotation-action]() to `v1.0.3`\n\n\n\n\n\nchore(deps): update plugin com.github.johnrengelman.shadow to v7.1.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/com.github.johnrengelman.shadow-7.x`\n  - Merge into: `main`\n  - Upgrade com.github.johnrengelman.shadow to `7.1.2`\n\n\n\n\n\nfix(deps): update activemqversion to v5.17.3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/activemqversion`\n  - Merge into: `main`\n  - Upgrade [org.apache.activemq:activemq-pool]() to `5.17.3`\n  - Upgrade [org.apache.activemq:activemq-client]() to `5.17.3`\n\n\n\n\n\nfix(deps): update aws-java-sdk-v2 monorepo to v2.19.24\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/aws-java-sdk-v2-monorepo`\n  - Merge into: `main`\n  - Upgrade [software.amazon.awssdk:bom]() to `2.19.24`\n  - Upgrade [software.amazon.awssdk:sts]() to `2.19.24`\n  - Upgrade [software.amazon.awssdk:sfn]() to `2.19.24`\n  - Upgrade [software.amazon.awssdk:sqs]() to `2.19.24`\n  - Upgrade [software.amazon.awssdk:ses]() to `2.19.24`\n  - Upgrade [software.amazon.awssdk:s3]() to `2.19.24`\n  - Upgrade [software.amazon.awssdk:rds]() to `2.19.24`\n  - Upgrade [software.amazon.awssdk:elastictranscoder]() to `2.19.24`\n\n\n\n\n\nfix(deps): update dependency edu.stanford.nlp:stanford-corenlp to v4.5.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/corenlpversion`\n  - Merge into: `main`\n  - Upgrade [edu.stanford.nlp:stanford-corenlp]() to `4.5.2`\n\n\n\n\n\nfix(deps): update dependency io.lettuce:lettuce-core to v6.2.2.release\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/lettuceversion`\n  - Merge into: `main`\n  - Upgrade [io.lettuce:lettuce-core]() to `6.2.2.RELEASE`\n\n\n\n\n\nfix(deps): update dependency io.logz.logback:logzio-logback-appender to v1.0.28\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/logziologbackappenderversion`\n  - Merge into: `main`\n  - Upgrade [io.logz.logback:logzio-logback-appender]() to `1.0.28`\n\n\n\n\n\nfix(deps): update dependency org.jsoup:jsoup to v1.15.3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/jsoupversion`\n  - Merge into: `main`\n  - Upgrade [org.jsoup:jsoup]() to `1.15.3`\n\n\n\n\n\nfix(deps): update dependency org.junit.jupiter:junit-jupiter to v5.9.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/junit5-monorepo`\n  - Merge into: `main`\n  - Upgrade [org.junit.jupiter:junit-jupiter]() to `5.9.2`\n\n\n\n\n\nfix(deps): update logbackversion to v1.4.5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/logbackversion`\n  - Merge into: `main`\n  - Upgrade [ch.qos.logback:logback-core]() to `1.4.5`\n  - Upgrade [ch.qos.logback:logback-classic]() to `1.4.5`\n\n\n\n\n\nfix(deps): update protobufversion to v3.21.12\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/protobufversion`\n  - Merge into: `main`\n  - Upgrade [com.google.protobuf:protoc]() to `3.21.12`\n  - Upgrade [com.google.protobuf:protobuf-kotlin]() to `3.21.12`\n  - Upgrade [com.google.protobuf:protobuf-java-util]() to `3.21.12`\n  - Upgrade [com.google.protobuf:protobuf-java]() to `3.21.12`\n\n\n\n\n\nfix(deps): update slackversion to v1.27.3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/slackversion`\n  - Merge into: `main`\n  - Upgrade [com.slack.api:slack-app-backend]() to `1.27.3`\n  - Upgrade [com.slack.api:slack-api-model-kotlin-extension]() to `1.27.3`\n  - Upgrade [com.slack.api:slack-api-client-kotlin-extension]() to `1.27.3`\n  - Upgrade [com.slack.api:slack-api-client]() to `1.27.3`\n\n\n\n\n\nfix(deps): update zallyversion to v2.1.1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/zallyversion`\n  - Merge into: `main`\n  - Upgrade [org.zalando:zally-ruleset-zally]() to `2.1.1`\n  - Upgrade [org.zalando:zally-ruleset-zalando]() to `2.1.1`\n  - Upgrade [org.zalando:zally-core]() to `2.1.1`\n\n\n\n\n\nchore(deps): update actions/setup-java action to v3.9.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/actions-setup-java-3.x`\n  - Merge into: `main`\n  - Upgrade [actions/setup-java]() to `v3.9.0`\n\n\n\n\n\nchore(deps): update aws-cdk monorepo to v2.62.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/aws-cdk-monorepo`\n  - Merge into: `main`\n  - Upgrade [aws-cdk]() to `2.62.0`\n  - Upgrade [aws-cdk-lib]() to `2.62.0`\n\n\n\n\n\nchore(deps): update dependency @floating-ui/react-dom to ^0.7.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/floating-ui-react-dom-0.x`\n  - Merge into: `main`\n  - Upgrade [@floating-ui/react-dom]() to `^0.7.0`\n\n\n\n\n\nchore(deps): update dependency @prezly/slate-commons to ^0.75.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/prezly-slate-commons-0.x`\n  - Merge into: `main`\n  - Upgrade [@prezly/slate-commons]() to `^0.75.0`\n\n\n\n\n\nchore(deps): update dependency @prezly/slate-lists to ^0.75.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/prezly-slate-lists-0.x`\n  - Merge into: `main`\n  - Upgrade [@prezly/slate-lists]() to `^0.75.0`\n\n\n\n\n\nchore(deps): update dependency com.expediagroup.graphql to v6.3.5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/graphqlpluginversion`\n  - Merge into: `main`\n  - Upgrade [com.expediagroup.graphql]() to `6.3.5`\n\n\n\n\n\nchore(deps): update dependency com.github.node-gradle.node to v3.5.1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/gradlenodepluginversion`\n  - Merge into: `main`\n  - Upgrade com.github.node-gradle.node to `3.5.1`\n\n\n\n\n\nchore(deps): update dependency com.google.protobuf to v0.9.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/protobufpluginversion`\n  - Merge into: `main`\n  - Upgrade com.google.protobuf to `0.9.2`\n\n\n\n\n\nchore(deps): update dependency io.gitlab.arturbosch.detekt to v1.22.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/detektpluginversion`\n  - Merge into: `main`\n  - Upgrade [io.gitlab.arturbosch.detekt]() to `1.22.0`\n\n\n\n\n\nchore(deps): update dependency nicklockwood/swiftformat to from: \"0.50.7\"\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/nicklockwood-swiftformat-0.x`\n  - Merge into: `main`\n  - Upgrade [nicklockwood/SwiftFormat]() to `34cd9dd87b78048ce0d623a9153f9bf260ad6590`\n\n\n\n\n\nchore(deps): update dependency slate to ^0.88.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/slate-0.x`\n  - Merge into: `main`\n  - Upgrade [slate]() to `^0.88.0`\n\n\n\n\n\nchore(deps): update dependency slate-react to ^0.88.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/slate-react-0.x`\n  - Merge into: `main`\n  - Upgrade [slate-react]() to `^0.88.0`\n\n\n\n\n\nchore(deps): update dependency webextension-polyfill to ^0.10.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/webextension-polyfill-0.x`\n  - Merge into: `main`\n  - Upgrade [webextension-polyfill]() to `^0.10.0`\n\n\n\n\n\nchore(deps): update kotlin monorepo to v1.8.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/kotlin-monorepo`\n  - Merge into: `main`\n  - Upgrade [org.jetbrains.kotlin.plugin.jpa]() to `1.8.0`\n  - Upgrade [org.jetbrains.kotlin.plugin.serialization]() to `1.8.0`\n  - Upgrade [org.jetbrains.kotlin.jvm]() to `1.8.0`\n\n\n\n\n\nchore(deps): update node.js to v19.5.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/node-19.x`\n  - Merge into: `main`\n  - Upgrade [node]() to `19.5.0-slim`\n\n\n\n\n\nchore(deps): update plugin com.expediagroup.graphql to v6.3.5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/com.expediagroup.graphql-6.x`\n  - Merge into: `main`\n  - Upgrade [com.expediagroup.graphql]() to `6.3.5`\n\n\n\n\n\nchore(deps): update plugin com.github.node-gradle.node to v3.5.1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/com.github.node-gradle.node-3.x`\n  - Merge into: `main`\n  - Upgrade com.github.node-gradle.node to `3.5.1`\n\n\n\n\n\nchore(deps): update plugin com.google.protobuf to v0.9.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/com.google.protobuf-0.x`\n  - Merge into: `main`\n  - Upgrade com.google.protobuf to `0.9.2`\n\n\n\n\n\nchore(deps): update plugin io.gitlab.arturbosch.detekt to v1.22.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/io.gitlab.arturbosch.detekt-1.x`\n  - Merge into: `main`\n  - Upgrade [io.gitlab.arturbosch.detekt]() to `1.22.0`\n\n\n\n\n\nchore(deps): update public.ecr.aws/lambda/python docker tag to v3.9\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/public.ecr.aws-lambda-python-3.x`\n  - Merge into: `main`\n  - Upgrade public.ecr.aws/lambda/python to `3.9`\n\n\n\n\n\nchore(deps): update python docker tag to v3.11\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/python-3.x`\n  - Merge into: `main`\n  - Upgrade python to `3.11-slim-bullseye`\n\n\n\n\n\nchore(deps): update slackapi/slack-github-action action to v1.23.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/slackapi-slack-github-action-1.x`\n  - Merge into: `main`\n  - Upgrade [slackapi/slack-github-action]() to `v1.23.0`\n\n\n\n\n\nfix(deps): update commonmarkversion to v0.21.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/commonmarkversion`\n  - Merge into: `main`\n  - Upgrade [org.commonmark:commonmark-ext-gfm-strikethrough]() to `0.21.0`\n  - Upgrade [org.commonmark:commonmark-ext-autolink]() to `0.21.0`\n  - Upgrade [org.commonmark:commonmark]() to `0.21.0`\n\n\n\n\n\nfix(deps): update dependency @floating-ui/react-dom-interactions to ^0.13.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/floating-ui-react-dom-interactions-0.x`\n  - Merge into: `main`\n  - Upgrade [@floating-ui/react-dom-interactions]() to `^0.13.0`\n\n\n\n\n\nfix(deps): update dependency com.expediagroup:graphql-kotlin-client-serialization to v6.3.5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/graphqlktorversion`\n  - Merge into: `main`\n  - Upgrade [com.expediagroup:graphql-kotlin-client-serialization]() to `6.3.5`\n\n\n\n\n\nfix(deps): update dependency com.fasterxml.jackson.module:jackson-module-kotlin to v2.14.1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/jacksonversion`\n  - Merge into: `main`\n  - Upgrade [com.fasterxml.jackson.module:jackson-module-kotlin]() to `2.14.1`\n\n\n\n\n\nfix(deps): update dependency com.github.doyaaaaaken:kotlin-csv-jvm to v1.7.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/kotlincsvversion`\n  - Merge into: `main`\n  - Upgrade [com.github.doyaaaaaken:kotlin-csv-jvm]() to `1.7.0`\n\n\n\n\n\nfix(deps): update dependency io.github.reactivecircus.cache4k:cache4k-jvm to v0.9.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/cache4kversion`\n  - Merge into: `main`\n  - Upgrade [io.github.reactivecircus.cache4k:cache4k-jvm]() to `0.9.0`\n\n\n\n\n\nfix(deps): update dependency io.honeycomb:honeycomb-opentelemetry-sdk to v1.4.1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/honeycombopentelemetryversion`\n  - Merge into: `main`\n  - Upgrade [io.honeycomb:honeycomb-opentelemetry-sdk]() to `1.4.1`\n\n\n\n\n\nfix(deps): update dependency org.assertj:assertj-core to v3.24.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/assertjversion`\n  - Merge into: `main`\n  - Upgrade [org.assertj:assertj-core]() to `3.24.2`\n\n\n\n\n\nfix(deps): update dependency org.bouncycastle:bcpkix-jdk15to18 to v1.72\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/bouncycastleversion`\n  - Merge into: `main`\n  - Upgrade [org.bouncycastle:bcpkix-jdk15to18]() to `1.72`\n\n\n\n\n\nfix(deps): update dependency org.eclipse.microprofile.openapi:microprofile-openapi-api to v3.1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/eclipseopenapimicroprofileversion`\n  - Merge into: `main`\n  - Upgrade [org.eclipse.microprofile.openapi:microprofile-openapi-api]() to `3.1`\n\n\n\n\n\nfix(deps): update dependency org.mockito.kotlin:mockito-kotlin to v4.1.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/mockitokotlinversion`\n  - Merge into: `main`\n  - Upgrade [org.mockito.kotlin:mockito-kotlin]() to `4.1.0`\n\n\n\n\n\nfix(deps): update dependency org.mockito:mockito-inline to v4.11.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/mockito-monorepo`\n  - Merge into: `main`\n  - Upgrade [org.mockito:mockito-inline]() to `4.11.0`\n\n\n\n\n\nfix(deps): update dependency org.openapitools.empoa:empoa-swagger-core to v2.1.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/openapiswaggerversion`\n  - Merge into: `main`\n  - Upgrade [org.openapitools.empoa:empoa-swagger-core]() to `2.1.0`\n\n\n\n\n\nfix(deps): update grpcversion to v1.52.1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/grpcversion`\n  - Merge into: `main`\n  - Upgrade [io.grpc:protoc-gen-grpc-java]() to `1.52.1`\n  - Upgrade [io.grpc:grpc-stub]() to `1.52.1`\n  - Upgrade [io.grpc:grpc-protobuf]() to `1.52.1`\n  - Upgrade [io.grpc:grpc-netty-shaded]() to `1.52.1`\n\n\n\n\n\nfix(deps): update hopliteversion to v2.7.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/hopliteversion`\n  - Merge into: `main`\n  - Upgrade [com.sksamuel.hoplite:hoplite-hocon]() to `2.7.0`\n  - Upgrade [com.sksamuel.hoplite:hoplite-core]() to `2.7.0`\n\n\n\n\n\nfix(deps): update ktlintversion to v0.48.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/ktlintversion`\n  - Merge into: `main`\n  - Upgrade [com.pinterest.ktlint:ktlint-test]() to `0.48.2`\n  - Upgrade [com.pinterest.ktlint:ktlint-core]() to `0.48.2`\n\n\n\n\n\nfix(deps): update luceneversion to v9.4.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/luceneversion`\n  - Merge into: `main`\n  - Upgrade [org.apache.lucene:lucene-queryparser]() to `9.4.2`\n  - Upgrade [org.apache.lucene:lucene-highlighter]() to `9.4.2`\n  - Upgrade [org.apache.lucene:lucene-core]() to `9.4.2`\n\n\n\n\n\nfix(deps): update opentelemetryversion\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/opentelemetryversion`\n  - Merge into: `main`\n  - Upgrade [io.opentelemetry:opentelemetry-sdk-extension-resources]() to `1.19.0`\n  - Upgrade [io.opentelemetry:opentelemetry-sdk]() to `1.22.0`\n  - Upgrade [io.opentelemetry:opentelemetry-extension-kotlin]() to `1.22.0`\n  - Upgrade [io.opentelemetry:opentelemetry-exporter-otlp]() to `1.22.0`\n  - Upgrade [io.opentelemetry:opentelemetry-exporter-logging]() to `1.22.0`\n  - Upgrade [io.opentelemetry:opentelemetry-sdk-testing]() to `1.22.0`\n\n\n\n\n\nfix(deps): update sentryversion to v6.12.1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/sentryversion`\n  - Merge into: `main`\n  - Upgrade [io.sentry:sentry-logback]() to `6.12.1`\n  - Upgrade [io.sentry:sentry-log4j2]() to `6.12.1`\n  - Upgrade [io.sentry:sentry]() to `6.12.1`\n\n\n\n\n\nchore(deps): update actions/checkout action to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/actions-checkout-3.x`\n  - Merge into: `main`\n  - Upgrade [actions/checkout]() to `v3`\n\n\n\n\n\nchore(deps): update actions/download-artifact action to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/actions-download-artifact-3.x`\n  - Merge into: `main`\n  - Upgrade [actions/download-artifact]() to `v3`\n\n\n\n\n\nchore(deps): update actions/upload-artifact action to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/actions-upload-artifact-3.x`\n  - Merge into: `main`\n  - Upgrade [actions/upload-artifact]() to `v3`\n\n\n\n\n\nchore(deps): update definitelytyped (major)\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-definitelytyped`\n  - Merge into: `main`\n  - Upgrade [@types/react]() to `^18.0.0`\n  - Upgrade [@types/react-dom]() to `^18.0.0`\n  - Upgrade [@types/uuid]() to `^9.0.0`\n\n\n\n\n\nchore(deps): update dependency @floating-ui/react-dom to v1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/floating-ui-react-dom-1.x`\n  - Merge into: `main`\n  - Upgrade [@floating-ui/react-dom]() to `^1.0.0`\n\n\n\n\n\nchore(deps): update dependency babel-loader to v9\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/babel-loader-9.x`\n  - Merge into: `main`\n  - Upgrade [babel-loader]() to `^9.0.0`\n\n\n\n\n\nchore(deps): update dependency copy-webpack-plugin to v11\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/copy-webpack-plugin-11.x`\n  - Merge into: `main`\n  - Upgrade [copy-webpack-plugin]() to `^11.0.0`\n\n\n\n\n\nchore(deps): update dependency org.jlleitschuh.gradle.ktlint to v11\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-ktlintpluginversion`\n  - Merge into: `main`\n  - Upgrade org.jlleitschuh.gradle.ktlint to `11.0.0`\n\n\n\n\n\nchore(deps): update dependency postcss-cli to v10\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/postcss-cli-10.x`\n  - Merge into: `main`\n  - Upgrade [postcss-cli]() to `^10.0.0`\n\n\n\n\n\nchore(deps): update dependency postcss-import to v15\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/postcss-import-15.x`\n  - Merge into: `main`\n  - Upgrade [postcss-import]() to `^15.0.0`\n\n\n\n\n\nchore(deps): update dependency postcss-loader to v7\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/postcss-loader-7.x`\n  - Merge into: `main`\n  - Upgrade [postcss-loader]() to `^7.0.0`\n\n\n\n\n\nchore(deps): update dependency postcss-preset-env to v8\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/postcss-preset-env-8.x`\n  - Merge into: `main`\n  - Upgrade [postcss-preset-env]() to `^8.0.0`\n\n\n\n\n\nchore(deps): update dependency sass-loader to v13\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/sass-loader-13.x`\n  - Merge into: `main`\n  - Upgrade [sass-loader]() to `^13.0.0`\n\n\n\n\n\nchore(deps): update dependency source-map-loader to v4\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/source-map-loader-4.x`\n  - Merge into: `main`\n  - Upgrade [source-map-loader]() to `^4.0.0`\n\n\n\n\n\nchore(deps): update dependency stylelint-config-standard to v29\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/stylelint-config-standard-29.x`\n  - Merge into: `main`\n  - Upgrade [stylelint-config-standard]() to `^29.0.0`\n\n\n\n\n\nchore(deps): update dependency stylelint-config-standard-scss to v6\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/stylelint-config-standard-scss-6.x`\n  - Merge into: `main`\n  - Upgrade [stylelint-config-standard-scss]() to `^6.0.0`\n\n\n\n\n\nchore(deps): update dependency svg-url-loader to v8\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/svg-url-loader-8.x`\n  - Merge into: `main`\n  - Upgrade [svg-url-loader]() to `^8.0.0`\n\n\n\n\n\nchore(deps): update dependency type-fest to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/type-fest-3.x`\n  - Merge into: `main`\n  - Upgrade [type-fest]() to `^3.0.0`\n\n\n\n\n\nchore(deps): update dependency uuid to v9\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/uuid-9.x`\n  - Merge into: `main`\n  - Upgrade [uuid]() to `^9.0.0`\n\n\n\n\n\nchore(deps): update dependency webpack-cli to v5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/webpack-cli-5.x`\n  - Merge into: `main`\n  - Upgrade [webpack-cli]() to `^5.0.0`\n\n\n\n\n\nchore(deps): update dependency webpack-manifest-plugin to v5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/webpack-manifest-plugin-5.x`\n  - Merge into: `main`\n  - Upgrade [webpack-manifest-plugin]() to `^5.0.0`\n\n\n\n\n\nchore(deps): update dependency zustand to v4\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/zustand-4.x`\n  - Merge into: `main`\n  - Upgrade [zustand]() to `^4.0.0`\n\n\n\n\n\nchore(deps): update react monorepo to v18 (major)\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-react-monorepo`\n  - Merge into: `main`\n  - Upgrade [react]() to `^18.0.0`\n  - Upgrade [react-dom]() to `^18.0.0`\n\n\n\n\n\nchore(deps): update react-dnd monorepo to v16 (major)\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-react-dnd-monorepo`\n  - Merge into: `main`\n  - Upgrade [react-dnd]() to `^16.0.0`\n  - Upgrade [react-dnd-html5-backend]() to `^16.0.0`\n\n\n\n\n\nchore(deps): update test packages (major)\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-test-packages`\n  - Merge into: `main`\n  - Upgrade [@testing-library/react]() to `^13.0.0`\n  - Upgrade [@testing-library/react-hooks]() to `^8.0.0`\n  - Upgrade [@types/jest]() to `^29.0.0`\n  - Upgrade [jest]() to `^29.0.0`\n  - Upgrade [mocha]() to `^10.0.0`\n  - Upgrade [ts-jest]() to `^29.0.0`\n\n\n\n\n\nfix(deps): update dependency com.google.api-client:google-api-client to v2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-googleoauthclientversion`\n  - Merge into: `main`\n  - Upgrade [com.google.api-client:google-api-client]() to `2.1.3`\n\n\n\n\n\nfix(deps): update dependency io.agora:authentication to v2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-agoraauthversion`\n  - Merge into: `main`\n  - Upgrade [io.agora:authentication]() to `2.0.0.2-ci-test`\n\n\n\n\n\nfix(deps): update dependency io.github.microutils:kotlin-logging to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-kotlinloggingversion`\n  - Merge into: `main`\n  - Upgrade [io.github.microutils:kotlin-logging]() to `3.0.4`\n\n\n\n\n\nfix(deps): update dependency org.mockito:mockito-inline to v5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-mockito-monorepo`\n  - Merge into: `main`\n  - Upgrade [org.mockito:mockito-inline]() to `5.0.0`\n\n\n\n\n\nfix(deps): update dependency react-merge-refs to v2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/react-merge-refs-2.x`\n  - Merge into: `main`\n  - Upgrade [react-merge-refs]() to `^2.0.0`\n\n\n\n\n\n Dependency Lookup Warnings \nPlease correct - or verify that you can safely ignore - these lookup failures before you merge this PR.\n\nFailed to look up maven dependency projects:apps\nFailed to look up maven dependency projects:clients\nFailed to look up maven dependency projects:libs\nFailed to look up maven dependency projects:services\nFailed to look up maven dependency com.nextchaptersoftware.gradle.ktlint\nFailed to look up npm dependency @fortawesome/fontawesome-common-types\nFailed to look up npm dependency @fortawesome/fontawesome-pro\nFailed to look up npm dependency @fortawesome/fontawesome-svg-core\nFailed to look up npm dependency @fortawesome/free-brands-svg-icons\nFailed to look up npm dependency @fortawesome/free-solid-svg-icons\nFailed to look up npm dependency @fortawesome/pro-duotone-svg-icons\nFailed to look up npm dependency @fortawesome/pro-light-svg-icons\nFailed to look up npm dependency @fortawesome/pro-regular-svg-icons\nFailed to look up npm dependency @fortawesome/pro-solid-svg-icons\nFailed to look up npm dependency @fortawesome/pro-thin-svg-icons\nFailed to look up npm dependency @fortawesome/react-fontawesome\n\nFiles affected: settings.gradle.kts, package.json\n\n Got questions? Check out Renovate's Docs, particularly the Getting Started section.\nIf you need any further assistance then you can also request help here.\n\nThis PR has been generated by Mend Renovate. View repository job log here."}
{"title": "Add aliases for all S3 keys", "number": 4422, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4422", "body": "I want to use these aliases to limit permissions granted to service accounts. Right now any service account with KSM access can read any key.\nAlready deployed to Dev and works as expected."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4422#pullrequestreview-**********", "body": ""}
{"title": "Update GitHub App description", "number": 4423, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4423", "body": "Matches "}
{"title": "Skip slack users for search autocomplete", "number": 4424, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4424"}
{"title": "Skip bot users for search autocomplete", "number": 4425, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4425"}
{"title": "Add logs on fetch failures", "number": 4426, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4426", "body": "Dennis was running into issues where VSCode was completely unresponsive.\nThere were no logs so difficult to reproduce.\nAdding logs at the API layer to narrow things down."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4426#pullrequestreview-1270208255", "body": ""}
{"title": "chore(deps): update definitelytyped", "number": 4427, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4427", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @types/chrome (source) | ^0.0.180 -> ^0.0.210 |  |  |  |  |\n| @types/isomorphic-fetch (source) | ^0.0.35 -> ^0.0.36 |  |  |  |  |\n| @types/webextension-polyfill (source) | ^0.8.3 -> ^0.10.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Immortal: This PR will be recreated if closed unmerged. Get config help if that's undesired.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency @storybook/testing-library to ^0.0.13", "number": 4428, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4428", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @storybook/testing-library | ^0.0.7 -> ^0.0.13 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nstorybookjs/testing-library\n\n### [`v0.0.13`](https://togithub.com/storybookjs/testing-library/blob/HEAD/CHANGELOG.md#v0013-Mon-Jun-13-2022)\n\n[Compare Source](https://togithub.com/storybookjs/testing-library/compare/v0.0.12...v0.0.13)\n\n#####  Bug Fix\n\n-   Fix dependency of [@storybook/instrumenter](https://togithub.com/storybook/instrumenter) [#22](https://togithub.com/storybookjs/testing-library/pull/22) ([@yannbf](https://togithub.com/yannbf))\n\n##### Authors: <AUTHORS>
{"title": "chore(deps): update dependency com.github.johnrengelman.shadow to v7.1.2", "number": 4429, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4429", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.johnrengelman.shadow | 7.1.1 -> 7.1.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "API spec refactor and fixes", "number": 443, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/443", "body": "cleanup\n- fix all Zally API lint rules\n- refactor ifModifiedSince params\n- refactor default responses\n- rename Annotation to Content"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/443#pullrequestreview-896580654", "body": ""}
{"title": "chore(deps): update dependency flight-school/anycodable to from: \"0.6.7\"", "number": 4430, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4430", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| Flight-School/AnyCodable | patch | from: \"0.6.1\" -> from: \"0.6.7\" |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nFlight-School/AnyCodable\n\n### [`v0.6.7`]()\n\n[Compare Source]()\n\n### [`v0.6.6`]()\n\n[Compare Source]()\n\n### [`v0.6.5`]()\n\n[Compare Source]()\n\n### [`v0.6.4`]()\n\n[Compare Source]()\n\n### [`v0.6.3`]()\n\n[Compare Source]()\n\n### [`v0.6.2`]()\n\n[Compare Source]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update frankie567/grafana-annotation-action action to v1.0.3", "number": 4431, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4431", "body": "This PR contains the following updates:\n| Package | Type | Update | Change |\n|---|---|---|---|\n| frankie567/grafana-annotation-action | action | patch | v1.0.2 -> v1.0.3 |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nfrankie567/grafana-annotation-action\n\n### [`v1.0.3`]()\n\n[Compare Source]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update plugin com.github.johnrengelman.shadow to v7.1.2", "number": 4432, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4432", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.johnrengelman.shadow | 7.1.1 -> 7.1.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update activemqversion to v5.17.3", "number": 4433, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4433", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.apache.activemq:activemq-pool (source) | 5.17.2 -> 5.17.3 |  |  |  |  |\n| org.apache.activemq:activemq-client (source) | 5.17.2 -> 5.17.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update aws-java-sdk-v2 monorepo to v2.19.24", "number": 4434, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4434", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| software.amazon.awssdk:bom (source) | 2.19.17 -> 2.19.24 |  |  |  |  |\n| software.amazon.awssdk:sts (source) | 2.19.17 -> 2.19.24 |  |  |  |  |\n| software.amazon.awssdk:sfn (source) | 2.19.17 -> 2.19.24 |  |  |  |  |\n| software.amazon.awssdk:sqs (source) | 2.19.17 -> 2.19.24 |  |  |  |  |\n| software.amazon.awssdk:ses (source) | 2.19.17 -> 2.19.24 |  |  |  |  |\n| software.amazon.awssdk:s3 (source) | 2.19.17 -> 2.19.24 |  |  |  |  |\n| software.amazon.awssdk:rds (source) | 2.19.17 -> 2.19.24 |  |  |  |  |\n| software.amazon.awssdk:elastictranscoder (source) | 2.19.17 -> 2.19.24 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/aws-sdk-java-v2\n\n### [`v2.19.24`]()\n\n[Compare Source]()\n\n#### **AWS CRT HTTP Client**\n\n-   ### Features\n    -   Remove CRT connection timeout default\n\n#### **AWS CloudFormation**\n\n-   ### Features\n    -   Enabled FIPS aws-us-gov endpoints in SDK.\n\n#### **AWS S3 Control**\n\n-   ### Features\n    -   Add additional endpoint tests for S3 Control. Fix missing endpoint parameters for PutBucketVersioning and GetBucketVersioning. Prior to this fix, those operations may have resulted in an invalid endpoint being resolved.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **AWS Security Token Service**\n\n-   ### Features\n    -   Doc only change to update wording in a key topic\n\n#### **AWSMainframeModernization**\n\n-   ### Features\n    -   Add returnCode, batchJobIdentifier in GetBatchJobExecution response, for user to view the batch job execution result & unique identifier from engine. Also removed unused headers from REST APIs\n\n#### **Amazon Elastic Compute Cloud**\n\n-   ### Features\n    -   This release adds new functionality that allows customers to provision IPv6 CIDR blocks through Amazon VPC IP Address Manager (IPAM) as well as allowing customers to utilize IPAM Resource Discovery APIs.\n\n#### **Amazon Polly**\n\n-   ### Features\n    -   Add 5 new neural voices - Sergio (es-ES), Andres (es-MX), Remi (fr-FR), Adriano (it-IT) and Thiago (pt-BR).\n\n#### **Amazon SageMaker Service**\n\n-   ### Features\n    -   SageMaker Inference Recommender now decouples from Model Registry and could accept Model Name to invoke inference recommendations job; Inference Recommender now provides CPU/Memory Utilization metrics data in recommendation output.\n\n#### **Redshift Serverless**\n\n-   ### Features\n    -   Added query monitoring rules as possible parameters for create and update workgroup operations.\n\n### [`v2.19.23`]()\n\n[Compare Source]()\n\n#### **AWS CRT HTTP Client**\n\n-   ### Features\n    -   Renamed `readBufferSize` -> `readBufferSizeInBytes`.\n    -   Renamed: `ConnectionHealthChecksConfiguration` -> `ConnectionHealthConfiguration`\n        Renamed: `allowableThroughputFailureInterval` -> `minimumThroughputTimeout`\n        Renamed: `minThroughputInBytesPerSecond` -> `minimumThroughputInBps`\n        Renamed: `AwsCrtAsyncHttpClient.builder().connectionHealthChecksConfiguration` -> `AwsCrtAsyncHttpClient.builder().connectionHealthConfiguration`\n\n-   ### Removals\n    -   Removed `tlsCipherPreference`.\n\n#### **AWS Glue DataBrew**\n\n-   ### Features\n    -   Enabled FIPS us-gov-west-1 endpoints in SDK.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **AWS Systems Manager for SAP**\n\n-   ### Features\n    -   This release provides updates to documentation and support for listing operations performed by AWS Systems Manager for SAP.\n\n#### **Amazon Route 53**\n\n-   ### Features\n    -   Amazon Route 53 now supports the Asia Pacific (Melbourne) Region (ap-southeast-4) for latency records, geoproximity records, and private DNS for Amazon VPCs in that region.\n\n### [`v2.19.22`]()\n\n[Compare Source]()\n\n#### **AWS Lambda**\n\n-   ### Features\n    -   Release Lambda RuntimeManagementConfig, enabling customers to better manage runtime updates to their Lambda functions. This release adds two new APIs, GetRuntimeManagementConfig and PutRuntimeManagementConfig, as well as support on existing Create/Get/Update function APIs.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon SageMaker Service**\n\n-   ### Features\n    -   Amazon SageMaker Inference now supports P4de instance types.\n\n### [`v2.19.21`]()\n\n[Compare Source]()\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon Elastic Compute Cloud**\n\n-   ### Features\n    -   C6in, M6in, M6idn, R6in and R6idn instances are powered by 3rd Generation Intel Xeon Scalable processors (code named Ice Lake) with an all-core turbo frequency of 3.5 GHz.\n\n#### **Amazon Interactive Video Service**\n\n-   ### Features\n    -   API and Doc update. Update to arns field in BatchGetStreamKey. Also updates to operations and structures.\n\n#### **Amazon QuickSight**\n\n-   ### Features\n    -   This release adds support for data bars in QuickSight table and increases pivot table field well limit.\n\n### [`v2.19.20`]()\n\n[Compare Source]()\n\n#### **AWS Elemental MediaLive**\n\n-   ### Features\n    -   AWS Elemental MediaLive adds support for SCTE 35 preRollMilliSeconds.\n\n#### **AWS Glue**\n\n-   ### Features\n    -   Release Glue Studio Hudi Data Lake Format for SDK/CLI\n\n#### **AWS Ground Station**\n\n-   ### Features\n    -   Add configurable prepass and postpass times for DataflowEndpointGroup. Add Waiter to allow customers to wait for a contact that was reserved through ReserveContact\n\n#### **AWS Panorama**\n\n-   ### Features\n    -   Added AllowMajorVersionUpdate option to OTAJobConfig to make appliance software major version updates opt-in.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **Amazon Appflow**\n\n-   ### Features\n    -   Adding support for Salesforce Pardot connector in Amazon AppFlow.\n\n#### **Amazon CloudWatch Logs**\n\n-   ### Features\n    -   Bug fix - Removed the regex pattern validation from CoralModel to avoid potential security issue.\n\n#### **Amazon Connect Participant Service**\n\n-   ### Features\n    -   This release updates Amazon Connect Participant's GetTranscript api to provide transcripts of past chats on a persistent chat session.\n\n#### **Amazon Connect Service**\n\n-   ### Features\n    -   Amazon Connect Chat introduces Persistent Chat, allowing customers to resume previous conversations with context and transcripts carried over from previous chats, eliminating the need to repeat themselves and allowing agents to provide personalized service with access to entire conversation history.\n\n#### **Amazon Elastic Compute Cloud**\n\n-   ### Features\n    -   Adds SSM Parameter Resource Aliasing support to EC2 Launch Templates. Launch Templates can now store parameter aliases in place of AMI Resource IDs. CreateLaunchTemplateVersion and DescribeLaunchTemplateVersions now support a convenience flag, ResolveAlias, to return the resolved parameter value.\n\n#### **Amazon OpenSearch Service**\n\n-   ### Features\n    -   This release adds the enhanced dry run option, that checks for validation errors that might occur when deploying configuration changes and provides a summary of these errors, if any. The feature will also indicate whether a blue/green deployment will be required to apply a change.\n\n#### **Amazon SageMaker Service**\n\n-   ### Features\n    -   HyperParameterTuningJobs now allow passing environment variables into the corresponding TrainingJobs\n\n#### **CodeArtifact**\n\n-   ### Features\n    -   Documentation updates for CodeArtifact\n\n### [`v2.19.19`]()\n\n[Compare Source]()\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **AWS WAFV2**\n\n-   ### Features\n    -   Improved the visibility of the guidance for updating AWS WAF resources, such as web ACLs and rule groups.\n\n#### **Amazon CloudWatch**\n\n-   ### Features\n    -   Enable cross-account streams in CloudWatch Metric Streams via Observability Access Manager.\n\n#### **Amazon Elastic File System**\n\n-   ### Features\n    -   Documentation updates for EFS access points limit increase\n\n#### **Amazon Interactive Video Service Chat**\n\n-   ### Features\n    -   Updates the range for a Chat Room's maximumMessageRatePerSecond field.\n\n### [`v2.19.18`]()\n\n[Compare Source]()\n\n#### **AWS Cloud9**\n\n-   ### Features\n    -   Added minimum value to AutomaticStopTimeMinutes parameter.\n\n#### **AWS Network Firewall**\n\n-   ### Features\n    -   Network Firewall now allows creation of dual stack endpoints, enabling inspection of IPv6 traffic.\n\n#### **AWS SDK for Java v2**\n\n-   ### Features\n    -   Updated endpoint and partition metadata.\n\n#### **AWSBillingConductor**\n\n-   ### Features\n    -   This release adds support for SKU Scope for pricing plans.\n\n#### **EC2 Image Builder**\n\n-   ### Features\n    -   Add support for AWS Marketplace product IDs as input during CreateImageRecipe for the parent-image parameter. Add support for listing third-party components.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency edu.stanford.nlp:stanford-corenlp to v4.5.2", "number": 4435, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4435", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| edu.stanford.nlp:stanford-corenlp (source) | 4.5.1 -> 4.5.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency io.lettuce:lettuce-core to v6.2.2.release", "number": 4436, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4436", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.lettuce:lettuce-core | 6.2.0.RELEASE -> 6.2.2.RELEASE |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nlettuce-io/lettuce-core\n\n### [`v6.2.2.RELEASE`]()\n\n[Compare Source]()\n\n#### :green_book: Links\n\n-   Reference documentation: \n-   Javadoc: \n\n#### :lady_beetle: Bug Fixes\n\n-   The hostname and password cannot parse even if escaping with RedisURI redis-sentinel the password include '@' and '#' [#2254]()\n-   Fix password parsing error when redis-sentinel URI contains @ [#2255]()\n-   XTrimArgs Should Allow Limit = 0 [#2250]()\n-   NullPointerException if INFO command on redis cluster fails [#2243]()\n-   Own `RedisCredentialsProvider` causes issue with protocol handshake on Redis 5 [#2234]()\n-   Proper creation of `AttributeKey` [#2111]()\n\n#### :bulb: Other\n\n-   Improve Document on dynamicRefreshSources [#2139]()\n-   Improve Document on pingBeforeActivateConnection [#2138]()\n-   Shutdown issue [#2111]()\n\n#### :heart: Contributors\n\nWe'd like to thank all the contributors who worked on this release!\n\n-   [@DanielYWoo]()\n-   [@Emibergo02]()\n-   [@Leoncanva]()\n-   [@PedroMPagani]()\n-   [@coolbeevip]()\n-   [@jiantosca]()\n-   [@m-ibot]()\n\n### [`v6.2.1.RELEASE`]()\n\n[Compare Source]()\n\nThe Lettuce team is pleased to announce the Lettuce 6.2.1 service release!\nThis release ships with bugfixes and dependency upgrades.\n\nFind the full changelog at the end of this document.\n\nThanks to all contributors who made Lettuce 6.2.1.RELEASE possible. Lettuce 6 supports\nRedis 2.6+ up to Redis 7.x. In terms of Java runtime, Lettuce requires at least Java 8 and\nworks with Java 19. It is tested continuously against the latest Redis source-build.\n\n#### :green_book: Links\n\n-   Reference documentation: \n-   Javadoc: \n\n#### :star: New Features\n\n-   Make SlotHash utility methods public [#2199]()\n\n#### :lady_beetle: Bug Fixes\n\n-   INFO response parsing throws on encountering '\\n' on NodeTopologyView [#2161]()\n-   `PartitionSelectorException` during refresh of `Partitions` [#2178]()\n-   RedisURI.Builder#withSsl(RedisURI) not working with SslVerifyMode#CA [#2182]()\n-   SMISMEMBER is not marked a readonly command [#2197]()\n-   Eval lua script expects return integer but null [#2200]()\n-   `ZRANGESTORE` does not support by Rank comparison [#2202]()\n-   zrevrangestorebylex/zrevrangestorebyscore range arguments flipped [#2203]()\n\n#### :bulb: Other\n\n-   Fixes typo in ReadFrom [#2213]()\n\n#### :heart: Contributors\n\nWe'd like to thank all the contributors who worked on this release!\n\n-   [@CodePlayer]()\n-   [@h-marvin]()\n-   [@henry701]()\n-   [@kotovdv]()\n-   [@lorenzhawkes]()\n-   [@oridool]()\n-   [@tadashiya]()\n-   [@ze]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency io.logz.logback:logzio-logback-appender to v1.0.28", "number": 4437, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4437", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.logz.logback:logzio-logback-appender | 1.0.27 -> 1.0.28 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nlogzio/logzio-logback-appender\n\n### [`v1.0.28`]()\n\n[Compare Source]()\n\n-   Added exceedMaxSizeAction parameter to drop/cut oversized logs.\n-   Bumped logzio sender version - fixing Index Out of Bounds error in bigqueue\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency org.jsoup:jsoup to v1.15.3", "number": 4438, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4438", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.jsoup:jsoup (source) | 1.15.1 -> 1.15.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency org.junit.jupiter:junit-jupiter to v5.9.2", "number": 4439, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4439", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.junit.jupiter:junit-jupiter (source) | 5.9.1 -> 5.9.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Fix getMessages, introduce getThreadMessages", "number": 444, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/444", "body": "Introduce getThreadMessages, to get messages for a single thread.\nFix bug in getMessages operation wasn't getting message for all threads.\n\nSee also:\nhttps://github.com/NextChapterSoftware/unblocked/pull/420#discussion_r816909600"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/444#pullrequestreview-896611370", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/444#pullrequestreview-896612052", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/444#pullrequestreview-896613861", "body": "Just the one comment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/444#pullrequestreview-896647098", "body": ""}
{"title": "Green checks for approved topics", "number": 4440, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4440"}
{"title": "ignore video app", "number": 4441, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4441"}
{"title": "Remove old explorer insights / PR VSCode panels", "number": 4442, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4442", "body": "This removes the code for the old, separated Insights and Pull Request explorer panels in VSCode.  This was dead code -- we were no longer using these webviews or providers, but we still built and shipped them."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4442#pullrequestreview-**********", "body": ""}
{"title": "fix(deps): update logbackversion to v1.4.5", "number": 4443, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4443", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| ch.qos.logback:logback-core (source) | 1.4.1 -> 1.4.5 |  |  |  |  |\n| ch.qos.logback:logback-classic (source) | 1.4.1 -> 1.4.5 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update protobufversion to v3.21.12", "number": 4444, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4444", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.google.protobuf:protoc (source) | 3.21.4 -> 3.21.12 |  |  |  |  |\n| com.google.protobuf:protobuf-kotlin (source) | 3.21.4 -> 3.21.12 |  |  |  |  |\n| com.google.protobuf:protobuf-java-util (source) | 3.21.4 -> 3.21.12 |  |  |  |  |\n| com.google.protobuf:protobuf-java (source) | 3.21.4 -> 3.21.12 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nprotocolbuffers/protobuf\n\n### [`v3.21.12`]()\n\n### [`v3.21.11`]()\n\n### [`v3.21.10`]()\n\n### [`v3.21.9`]()\n\n### [`v3.21.8`]()\n\n### [`v3.21.7`]()\n\n### [`v3.21.6`]()\n\n### [`v3.21.5`]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update slackversion to v1.27.3", "number": 4445, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4445", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.slack.api:slack-app-backend | 1.27.1 -> 1.27.3 |  |  |  |  |\n| com.slack.api:slack-api-model-kotlin-extension | 1.27.1 -> 1.27.3 |  |  |  |  |\n| com.slack.api:slack-api-client-kotlin-extension | 1.27.1 -> 1.27.3 |  |  |  |  |\n| com.slack.api:slack-api-client | 1.27.1 -> 1.27.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nslackapi/java-slack-sdk\n\n### [`v1.27.3`](): version 1.27.3\n\n#### Changes\n\n-   \\[slack-api-client] [#1106]() Fix [#821]() emoji.list missing include_categories option - Thanks [@slushpupie]() [@seratch]()\n-   \\[all] [#1107]() Upgrade gson, kotlin, and so on - Thanks [@seratch]()\n-   \\[slack-api-model] Add `media_progress` property in `file` objects - Thanks [@seratch]()\n-   \\[slack-api-client] Add `team_id` to admin.analytics.getFile API response data - Thanks [@seratch]()\n\n***\n\n-   All issues/pull requests: \n-   All changes: \n\n### [`v1.27.2`](): version 1.27.2\n\n##### Changes\n\n-   \\[slack-api-client] [#1089]() Fix [#1088]() Update MethodsClient to be consistent on the text field warnings with Node / Python SDKs - Thanks [@seratch]()\n-   \\[slack-api-client] [#1090]() Fix [#1087]() Add admin.conversations.bulk{Archive|Delete|Move} API method support - Thanks [@seratch]()\n-   \\[bolt] [#1097]() Fix [#1092]() by reducing info level logging in AmazonS3InstallationService/AmazonS3OAuthStateService - Thanks [@seratch]()\n-   \\[document] [#1096]() Update Spring Boot documents and examples to support v3 - Thanks [@seratch]()\n\n***\n\n-   All issues/pull requests: \n-   All changes: \n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Whenever PR is behind base branch, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
