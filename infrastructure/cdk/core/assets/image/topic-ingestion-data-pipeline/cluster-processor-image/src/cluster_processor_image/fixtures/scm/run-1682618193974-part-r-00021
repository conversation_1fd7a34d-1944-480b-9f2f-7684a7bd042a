{"title": "Trigger SCM Team Maintenance job after SCM installation connection", "number": 5614, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5614", "body": "Ensures that the connected team's resources are refreshed immediately."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5614#pullrequestreview-1380465100", "body": ""}
{"comment": {"body": "No longer need to run maintenance so frequently.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5614#discussion_r1163592448"}}
{"title": "Fix bug in home profile when user has multiple SCM memberships", "number": 5615, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5615", "body": "Noticed because when I logged in as <PERSON><PERSON><PERSON><PERSON> we displayed my Bitbucket profile.\nWe were choosing the profile information for the user at random.\nThis is an example of a terrible general pattern that we should develop a\nlint rule to avoid use:\njs\nperson?.memberships[0]\nIn general, do not use:\njs\narray[0]"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5615#pullrequestreview-1381665479", "body": ""}
{"title": "Address cases where we choose a random member for a person", "number": 5616, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1381841019", "body": ""}
{"comment": {"body": "@kaych we should maybe publish the \"current member\" in this hook, so we don't have to recalculate it everywhere?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164473062"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1382079930", "body": ""}
{"comment": {"body": "there's also a `currentTeamId` that you can grab from the same context", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164627673"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1382080714", "body": ""}
{"comment": {"body": "Which context? `TeamContext` ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164627951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1382083087", "body": ""}
{"comment": {"body": "Ah sorry, I meant we could maybe add currentMember as a return value on this hook -- it could calculate the currentTeamMember as below", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164629268"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1382083150", "body": ""}
{"comment": {"body": "Are all memberships guaranteed to be from different teams? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164629325"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1382092040", "body": ""}
{"comment": {"body": "I assume your other comment was meant for this thread @matthewjamesadam -- I don't feel strongly one way or another about adding team member information to a team context but right now that context only exists on web/ code anyway so we would still need to pull this logic for the ide. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164636149"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1382093411", "body": ""}
{"comment": {"body": "Yes, a person can only be a member of a team once.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164637090"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1382094194", "body": ""}
{"comment": {"body": "Ah... got it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164637655"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1382095567", "body": ""}
{"comment": {"body": "> there's also a `currentTeamId` that you can grab from the same context\r\n\r\n@kaych yup, I'll change", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164638621"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-1382106449", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#pullrequestreview-**********", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/5616/commits/05dba005b215df1bc1c1515129c6b6ccd7c36874", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5616#discussion_r1164655938"}}
{"title": "Update message links", "number": 5617, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5617", "body": "Update some links to include label. Primarily used for SCM links that differ based on provider."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5617#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Currently have WebLink scoped to the PR links but can be used for all links...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5617#discussion_r1164388926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5617#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This breaks compatibility both with old and new clients -- maybe we should have the provider as a separate property instead?\r\n\r\n(hmm... it could be a property on the thread/PR?  The provider will be the same for all the messages on a thread)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5617#discussion_r1164396961"}}
{"title": "Add topic summary descriptions", "number": 5618, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5618"}
{"title": "[unb 1145] Dynamic name by injecting provider", "number": 5619, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5619", "body": "Update hard-coded context menus to be dynamic based on provider.\nRequired injecting provider into aggregate models."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5619#pullrequestreview-**********", "body": "Nice  thank you\nAny concern about the length of the string in the enterprise cases? The longest string \"GitLab Self-hosted\" will still fit in the menus?\nts\n        case Provider.Bitbucket:\n            return 'Bitbucket.com';\n        case Provider.Github:\n            return 'GitHub.com';\n        case Provider.GithubEnterprise:\n            return 'GitHub Enterprise';\n        case Provider.Gitlab:\n            return 'GitLab.com';\n        case Provider.GitlabSelfHosted:\n            return 'GitLab Self-hosted';"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5619#pullrequestreview-**********", "body": ""}
{"title": "Refresh the refresh token", "number": 562, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/562", "body": "The way we were doing it before meant that we weren't correctly updating the refresh token"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/562#pullrequestreview-907880959", "body": ""}
{"title": "Minor rename", "number": 5620, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5620"}
{"title": "Rename GitLab Enterprise", "number": 5621, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5621", "body": "Make consistent with server.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5621#pullrequestreview-**********", "body": ""}
{"title": "Abstract search phase 1", "number": 5622, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5622"}
{"title": "[WIP] Add lib-jira", "number": 5623, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623"}
{"comment": {"body": "Is there a client for this?\r\nTrying to determine how you're planning to segment this stuff.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#issuecomment-1507351319"}}
{"comment": {"body": "Testing notifications for David", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#issuecomment-1513633743"}}
{"comment": {"body": "boop", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#issuecomment-1513635936"}}
{"comment": {"body": "hello", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#issuecomment-1515723897"}}
{"comment": {"body": "hello friends", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#issuecomment-1515732917"}}
{"comment": {"body": "hi Petey", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#issuecomment-1516710218"}}
{"comment": {"body": "hi Richie", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#issuecomment-1516712765"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#pullrequestreview-1382148266", "body": ""}
{"comment": {"body": "You can just declare as `Instant`, serializer will do the right thing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#discussion_r1164673212"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#pullrequestreview-1382150051", "body": ""}
{"comment": {"body": "That would work...if Jira used a standard date string format :|", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#discussion_r1164674472"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#pullrequestreview-1382150903", "body": ""}
{"comment": {"body": "`2023-04-06T10:41:24.099-0700` where is my colon", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5623#discussion_r1164675057"}}
{"title": "Expose installation ID allowing API to talk about unique installations", "number": 5624, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5624", "body": "Not used yet, but will be in subsequent PRs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5624#pullrequestreview-**********", "body": ""}
{"title": "JetBrains search", "number": 5625, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625", "body": "Add Search Everywhere provider for JetBrains IDEs.  Results show up under an 'Unblocked' tab, and under 'All'.  Fixes \n\nImplement SearchEverywhereContributor\nAdd agent API for searching.  This returns a stream of values, right now only a single value is returned, but if we wanted to do additional searching we could return additional streamed results\nAdd PR icons to jetbrains project\n\n"}
{"comment": {"body": "@benedict-jw this shows our results in the 'All' tab -- it's worth considering what we might want to display on the right hand side here, as it's pretty common for search providers to populate that side\r\n\r\n<img width=\"795\" alt=\"Screenshot 2023-04-12 at 3 16 53 PM\" src=\"https://user-images.githubusercontent.com/2133518/231598308-a2bc94fc-49db-4659-9123-635ea68ecffb.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#issuecomment-**********"}}
{"comment": {"body": "> @benedict-jw this shows our results in the 'All' tab -- it's worth considering what we might want to display on the right hand side here, as it's pretty common for search providers to populate that side\r\n\r\nCan we put our avatars in there?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#issuecomment-**********"}}
{"comment": {"body": "> > @benedict-jw this shows our results in the 'All' tab -- it's worth considering what we might want to display on the right hand side here, as it's pretty common for search providers to populate that side\r\n> \r\n> Can we put our avatars in there?\r\n\r\nPossibly.  I'm going to get this in as-is and we can put this on the todo list.  Adding custom rendered items here might be a bit tricky...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#issuecomment-**********"}}
{"comment": {"body": "\r\n> @benedict-jw this shows our results in the 'All' tab -- it's worth considering what we might want to display on the right hand side here, as it's pretty common for search providers to populate that side\r\n> \r\n> <img alt=\"Screenshot 2023-04-12 at 3 16 53 PM\" width=\"795\" src=\"https://user-images.githubusercontent.com/2133518/231598308-a2bc94fc-49db-4659-9123-635ea68ecffb.png\">\r\n\r\nHow's the overall performance? Does it freeze the UI?\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#issuecomment-**********"}}
{"comment": {"body": "> How's the overall performance? Does it freeze the UI?\r\n\r\nPerformance is fine.  AFAICT they allocate a ton of threads, and the expectation is that you block the thread you're given while your search contributor is processing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#issuecomment-**********"}}
{"comment": {"body": "> > > @benedict-jw this shows our results in the 'All' tab -- it's worth considering what we might want to display on the right hand side here, as it's pretty common for search providers to populate that side\r\n> > \r\n> > \r\n> > Can we put our avatars in there?\r\n> \r\n> Possibly. I'm going to get this in as-is and we can put this on the todo list. Adding custom rendered items here might be a bit tricky...\r\n\r\nAlternatively I would put one of our other metadata items we use (date or comment count)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This was extracted from the general sidebar -- it does a search across all IDE repos.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#discussion_r1164692831"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#pullrequestreview-**********", "body": ""}
{"comment": {"body": "The `ColoredListCellRenderer` is the closest thing JB has to a \"standard\" list item.  Every search provider seems to use a different renderer for whatever reason, but this one seems to look right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#discussion_r1164693804"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Fixed to 'relevance' ordering for now -- not sure if this is right?  VSCode seems to use recency for this, but I'm not sure that's right either?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#discussion_r1164695055"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#pullrequestreview-**********", "body": ""}
{"comment": {"body": "What was the reason to move away from the ProjectCoroutineScope?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#discussion_r1165758973"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#pullrequestreview-1383786354", "body": ""}
{"comment": {"body": "Ah I should have commented on this -- I removed it from this class only, because this class is the *target* for most of what ProjectCoroutineScope does.\r\n\r\nI wanted to add `withIDEAgent` to the ProjectCoroutineScope, but then that meant this class would recursively call into itself (or override the actual implementation).\r\n\r\nThis way the same API works fine in all services.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5625#discussion_r1165763356"}}
{"title": "Bitbucket Repo Selection: API to list available repos and connect", "number": 5626, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5626", "body": "Flow still works with new APIs, we're just passing a null list of repos to connectInstallationV3.\nFixes: UNB-1155\nFixes: UNB-1156"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5626#pullrequestreview-1382726008", "body": ""}
{"comment": {"body": "@jeffrey-ng for you", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5626#discussion_r1165062788"}}
{"title": "Fixes Update Markdown layout when the width of the Markdown is small", "number": 5627, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5627", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5627#pullrequestreview-**********", "body": ""}
{"title": "Add serach indexer for openai", "number": 5628, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5628"}
{"title": "Update ProviderIcon for broad use", "number": 5629, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629", "body": "Have ProviderIcon handle all scm icons."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#pullrequestreview-**********", "body": ""}
{"comment": {"body": "At this point, ProviderIcon should just handle slack as well...\r\n\r\nIf we ever move Slack out of Provider, than we can redo this Icon.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#discussion_r1164751059"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Remove Icon above and this conditional.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#discussion_r1164751484"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#pullrequestreview-**********", "body": ""}
{"comment": {"body": "What are the differences between this and the shared ProviderIcon?  It looks like it' maybe just the color option?  Can we move this into shared and just have this be consistent across all clients?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#discussion_r1164769627"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Not with how assets work in the IDEs right now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#discussion_r1164769800"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think it's worth revisiting if we can figure out how to do direct imports but since vscode (not sure about jetbrains) has to import its assets in its roundabout way and I don't think vscode ever renders provider icons in color, I don't think it's worth expanding this specific component to handle those.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#discussion_r1164770652"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I don't think I understand this?  We now have two `ProviderIcon` components, one in shared, one in web, and they do almost the same thing, the web version is a superset of the shared version.  It looks to me like we could replace the `shared` version with this `web` version, and remove the `web` version completely?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5629#discussion_r1165816617"}}
{"title": "Merge server sourcepoints with local sourcepoints on each round", "number": 563, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/563"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/563#pullrequestreview-907530787", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/563#pullrequestreview-907558849", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/563#pullrequestreview-932369284", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/563#pullrequestreview-932369633", "body": "Just one comment"}
{"title": "Generalize openai prompts", "number": 5630, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5630"}
{"title": "Add ability to toggle search embedding", "number": 5631, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5631"}
{"title": "Semantic search part 2", "number": 5632, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5632"}
{"title": "[BREAKS API ON MAIN] Ability to install/uninstall repos and support stateless connect install flow", "number": 5633, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5633", "body": "Operation changes:\n- modifies listInstallationRepos to return InstallationAndRepos object to drive stateless install flow\n- adds patchInstallation to install additional repos for an installation\n- adds deleteRepo operation to uninstall a repo"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5633#pullrequestreview-1384048026", "body": ""}
{"title": "Move Hub API into shared", "number": 5634, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5634", "body": "Preliminary PR for integrating hub with JetBrains IDEs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5634#pullrequestreview-1384056520", "body": ""}
{"title": "Update client assets to main", "number": 5635, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5635", "body": "Was changed in #5631"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5635#pullrequestreview-1384093926", "body": ""}
{"title": "Remove refresh team error popup", "number": 5636, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5636"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5636#pullrequestreview-1384124189", "body": ""}
{"title": "Update login UI", "number": 5637, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637", "body": "\n\nUpdate fontawesome libs to latest version\nUpdate login UI to new designs\nIf only a single public button is made available, we show the horizontal button style:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#pullrequestreview-**********"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Any reason we don't use a button here? We use it in the other provider buttons, and logically it probably should be one?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#discussion_r1166051853"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#pullrequestreview-**********", "body": ""}
{"comment": {"body": "If we ever unable `noUncheckedIndexedAccess`, which I think we should, this won't get type checked properly and will be undefined.\r\n\r\nSomething like this should work here?\r\n```\r\nconst [firstProvider, ...remainingProviders] = providers;\r\n    const providerCount = providers.length;\r\n\r\n    if (providerCount === 0) {\r\n        return null;\r\n    }\r\n\r\n    if (providerCount === 1) {\r\n        return ...\r\n\r\n    return ...\r\n    ```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#discussion_r1166147793"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Or ArrayUtils.firstOrUndefined.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#discussion_r1166147965"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I tried enabling that flag and it was going to be a huge job to fix the damage.  I'll get to it some day.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#discussion_r1166148685"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Can I make it a switch statement instead? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5637#discussion_r1166149234"}}
{"title": "JetBrains  Hub Integration", "number": 5638, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638", "body": "Fixes UNB-1161\n\nContinue attempt to connect to hub\nOn startup, pull login token from hub\nAs hub logs in/out, reflect that in IntelliJ\nHandle thread and PR actions sent from hub\n\nSlight change to the hub prefs to generalize the first 'Other Insights in' option:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#pullrequestreview-1384397562", "body": ""}
{"comment": {"body": "FYI @jeffrey-ng I added APIs here so we can have generic IDE code that can open these UIs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#discussion_r1166136786"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#pullrequestreview-1384400659", "body": ""}
{"comment": {"body": "FYI @pwerry the hub no longer tries to foreground VSCode, VSCOde (and IntelliJ) do it themselves when a thread view is requested", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#discussion_r1166138131"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#pullrequestreview-1384401189", "body": ""}
{"comment": {"body": "FYI @pwerry dunno if it's worth changing the user prefs to match the label/intent", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#discussion_r1166138346"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#pullrequestreview-1388911782", "body": ""}
{"comment": {"body": "Worth try catching and logging any errors. Just helps with debugging in the future...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#discussion_r1169247161"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#pullrequestreview-1388913727", "body": ""}
{"comment": {"body": "I would only focus the project window if `shouldShowOnLaunch` is set.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#discussion_r1169248486"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#pullrequestreview-1388915261", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#pullrequestreview-1388954290", "body": ""}
{"comment": {"body": "This is not after launching IntelliJ -- this is from a user action.  IntelliJ is already launched and the right project is open.  The user clicks on a thread in the hub, and we want to display that thread in the project.  The project window may be minimized or hidden behind another window, so we have to bring it to the front.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#discussion_r1169273984"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#pullrequestreview-1388979722", "body": ""}
{"comment": {"body": "Yup. That's the case where launchView is called with `InsightToolWindowFactory.ToolWindowId.viewId ` where `shouldShowOnLaunch` is false.\r\n\r\nI'm trying to avoid the situation where the Agent launches the sidebar in the background and that triggers the project to focus.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5638#discussion_r1169288621"}}
{"title": "Implement install APIs", "number": 5639, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5639", "body": "Changes\n- implement patchInstallation\n- implement listInstallationRepos properly\n- remove implementation for deprecated legacyListInstallations\n- remove implementation for deprecated connectInstallation\nExample of listInstallationRepos\njson\n{\n  \"installation\": {\n    \"avatarUrl\": \"\",\n    \"displayName\": \"Unblocked\",\n    \"fullPath\": \"getunblocked\",\n    \"htmlUrl\": \"\",\n    \"installationId\": \"{79f8f678-37d1-4d28-8ce1-e33d0f063c81}\",\n    \"isInstalled\": false,\n    \"provider\": \"bitbucket\"\n  },\n  \"reposInstalled\": [],\n  \"reposNotInstalled\": [\n    {\n      \"fullName\": \"getunblocked/sample\",\n      \"ownerName\": \"getunblocked\",\n      \"repoName\": \"sample\",\n      \"webUrl\": \"\",\n      \"httpUrl\": \"\",\n      \"sshUrl\": \"\",\n      \"externalId\": \"{70032227-068a-4683-bd23-cc0783d9f3df}\",\n      \"scpUrl\": \"*****************:getunblocked/sample.git\"\n    },\n    {\n      \"fullName\": \"getunblocked/chatgpt-retrieval-plug\",\n      \"ownerName\": \"getunblocked\",\n      \"repoName\": \"chatgpt-retrieval-plug\",\n      \"webUrl\": \"\",\n      \"httpUrl\": \"\",\n      \"sshUrl\": \"\",\n      \"externalId\": \"{942f7e3e-4d98-4c5a-897f-3c77cadb1d17}\",\n      \"scpUrl\": \"*****************:getunblocked/chatgpt-retrieval-plug.git\"\n    }\n  ]\n}"}
{"title": "Add team-based APIs", "number": 564, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/564"}
{"comment": {"body": "This looks good to me from a service standpoint. When the team-scope PR lands this is going to explode the amount of test refactoring just FYI", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/564#issuecomment-1067523720"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/564#pullrequestreview-910557068", "body": ""}
{"comment": {"body": "@jeffrey-ng the \"key\" for this is now any object (the template `K` argument) -- this allows the key to be an object giving a series of properties, for instance, `{ teamId, threadId}` for the message store.  The client has to provide a `keyGenerator` to map the key object to a string.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/564#discussion_r827222829"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/564#pullrequestreview-910558200", "body": ""}
{"comment": {"body": "We'll need to think through how we deal with dashboard permalinks.  For now I added team ID to the URL.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/564#discussion_r827223715"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/564#pullrequestreview-910611608", "body": ""}
{"title": "REduce summary temprerature", "number": 5640, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5640"}
{"title": "Install flow lists GitLab user repos", "number": 5641, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5641"}
{"title": "Reduce prompt input", "number": 5642, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5642"}
{"title": "Fix listInstallationRepos installation ID", "number": 5643, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5643"}
{"title": "Hub: Top-level PR comment sub-heading says \"Unknown code reference\"", "number": 5644, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5644", "body": "Now we construct the sub-heading according to this schema, where the fileName is optional:\n\n\nTop-level comment PR thread:\nPR #{prNumber}\n\n\nCode-level comment PR thread:\nPR #{prNumber}  {fileName}\n\n\nBefore\n\nAfter\n"}
{"comment": {"body": "> If I'm reading this correctly, we will now only show the subheading if it is a PR thread or a slack thread? What about notes and video walkthroughs?\r\n\r\nAh, good catch. I'll fix.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5644#issuecomment-1507887403"}}
{"comment": {"body": "@richiebres - Bit of post-merge feedback from Dennis. Remove the PR number for the code-level comment to differentiate the sub-text further. The sub-texts would look like this:\r\n\r\nTop-level comment PR thread:\r\n`PR #{prNumber}`\r\n\r\nCode-level comment PR thread:\r\n`{fileName}`\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5644#issuecomment-1508884091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5644#pullrequestreview-1384463292", "body": "If I'm reading this correctly, we will now only show the subheading if it is a PR thread or a slack thread? What about notes and video walkthroughs?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5644#pullrequestreview-1384653780", "body": ""}
{"title": "Try again", "number": 5645, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5645"}
{"title": "Fix again", "number": 5646, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5646"}
{"title": "New onboarding flow", "number": 5647, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647", "body": "Login\n\nAllow Notifications\n\nNotifications Enabled\n\nIDE Selection (VSCode)\n\nIDE Selection (IntelliJ)\n\nIDE Selection (Single VSCode and Single Jetbrains)\n\nIDE Selection (Multiple VSCodes)\n\nIDE Selection (Multiple Jetbrains)\n\nIDE Selection (Multiple IDEs)\n\nTour\n"}
{"comment": {"body": "This looks awesome @pwerry! Couple notes:\r\n- GitLab Enterprise should probably say \"GitLab Self-Hosted\r\n- I owe you a dark image for the \"notify when complete\" state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#issuecomment-1513455361"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1389147842", "body": ""}
{"comment": {"body": "Ignore all this, it's hard-coded to test things out. I'll be moving this to a dynamic restore model in the next PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1169405354"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390469409", "body": ""}
{"comment": {"body": "Keeping this here because it's the right place for it. It will eventually key off of the restored auth state and onboarding completion default. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170265437"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390921561", "body": ""}
{"comment": {"body": "Worth adding codium", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170559409"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390930668", "body": ""}
{"comment": {"body": "Removing this means that the view itself would keep the intrinsic height?\r\nTherefore, we're not \"collapsing\", just hiding.\r\n\r\nNoticed this in the Login video.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170566080"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390935939", "body": ""}
{"comment": {"body": "Instead of passing this around everywhere, would it make sense to have this inside an environment?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170569391"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390938167", "body": ""}
{"comment": {"body": "Instead of unwrapping the optional like this.\r\n\r\n```\r\nlet ides = IDE.installedVSCodeIDEs() + IDE.installedJetbrainsIDEs()\r\n\r\nif let firstIDE = ides.first {\r\n    return firstIDE\r\n} else {\r\n    return IDE.Variant.VSCode.vscode\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170571522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390939174", "body": ""}
{"comment": {"body": "If we're confident that this works, I would remove these debugging logs as they don't provide value in the long run.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170572057"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390941465", "body": ""}
{"comment": {"body": "If the backup is \"Press Me\", I don't think finishButtonLabel should ever be optional...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170573723"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390943229", "body": ""}
{"comment": {"body": "Doesn't seem like contentView should be optional either.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170574904"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390944155", "body": ""}
{"comment": {"body": "Do we ever expect a situation where both actions are missing?\r\nIf so, should hide this entire HStack.\r\n\r\nPropose extracting this out into its own component to handle those different states.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170575552"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390950631", "body": ""}
{"comment": {"body": "Group the Action and Label into a struct. With this, there's no chance for a mismatch where the label is defined by the action isn't and vice versa.\r\n\r\n```\r\nstruct ButtonAction {\r\n   var action...\r\n   var label...\r\n}\r\n```\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170580055"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390958224", "body": ""}
{"comment": {"body": "Provide default values for all these optional values either here or in an extension on OnboardingStep. Will help reduce the boilerplate.\r\n\r\ne.g.\r\n```\r\n    let skipAction: (() -> Void)? = nil\r\n\r\n    var finishAction: (() -> Void)? = nil\r\n\r\n    var secondaryAction: (() -> Void)? = nil\r\n\r\n    let finishButtonLabel: String? = nil\r\n    ```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170585038"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390961175", "body": ""}
{"comment": {"body": "Why return an empty HStack instead of null?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170587087"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390961615", "body": ""}
{"comment": {"body": "Empty Hstack?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170587375"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390961976", "body": ""}
{"comment": {"body": "Remove", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170587630"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1390967451", "body": ""}
{"comment": {"body": "Nested ifs are hard to read. Suggest using switch statements. Not a fan of the force unwrap here but I think it's pretty safe and nicer than the alternative.\r\n\r\n```\r\nprivate var installedIDEState: InstalledIDEState {\r\n    switch (installedVSCodeIDEs.count, installedJetbrainsIDEs.count) {\r\n    case (0, 0):\r\n        return .none\r\n        \r\n    case (1, 0):\r\n        return .singleVSCode(ide: installedVSCodeIDEs.first!)\r\n        \r\n    case (0, 1):\r\n        return .singleJetbrains(ide: installedJetbrainsIDEs.first!)\r\n        \r\n    case (1, 1):\r\n        return .singleVSCodeAndSingleJetbrains(vscode: installedVSCodeIDEs.first!, jetbrains: installedJetbrainsIDEs.first!)\r\n        \r\n    case (_, 0):\r\n        return .multipleVSCode(vscodes: installedVSCodeIDEs)\r\n        \r\n    case (0, _):\r\n        return .multipleJetbrains(jetbrains: installedJetbrainsIDEs)\r\n        \r\n    case (_, _):\r\n        return .multiple(vscodes: installedVSCodeIDEs, jetbrains: installedJetbrainsIDEs)\r\n    }\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1170591395"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392446161", "body": ""}
{"comment": {"body": "Yup this is much better - will add", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171578451"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392450019", "body": ""}
{"comment": {"body": "My brain went on vacation here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171581058"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392455017", "body": ""}
{"comment": {"body": "@benedict-jw we need VSCodium iconography please", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171584490"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392459122", "body": ""}
{"comment": {"body": "This was not my observation. I tested this with the hub NSPopover and it correctly resized when collapsed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171587419"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392460503", "body": ""}
{"comment": {"body": "Good idea", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171588389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392669286", "body": ""}
{"comment": {"body": "There is always at least 1 button according to the designs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171720955"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392671985", "body": ""}
{"comment": {"body": "ViewBuilder complains about type inference issues and won't accept nil", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171722632"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392672159", "body": ""}
{"comment": {"body": "ditto", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171722725"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392822728", "body": ""}
{"comment": {"body": "Could use EmptyView() instead then.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171817111"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392834284", "body": ""}
{"comment": {"body": "Ahhh I forgot about that thing - thx!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171824285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392920204", "body": ""}
{"comment": {"body": "Never understood why this wasn't the default behaviour. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171878983"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392922567", "body": ""}
{"comment": {"body": "nit: I wonder if we should have explicit sort order here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171880694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392930116", "body": ""}
{"comment": {"body": "Instead of duplicating this, Add a custom modifier to LoopingVideoView to apply a \"Onboarding\" style.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171885846"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392930493", "body": ""}
{"comment": {"body": "https://developer.apple.com/documentation/swiftui/viewmodifier", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171886106"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392930939", "body": ""}
{"comment": {"body": "Same here. Either a separate View or ViewModifier to apply the styles & padding. Will help with consistency.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171886412"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392931619", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392977709", "body": ""}
{"comment": {"body": "You mean favour specific flavour of IDE? Or apply lexicographical sorting?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171919005"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1392983012", "body": ""}
{"comment": {"body": "I'll dry this up in a subsequent PR. Originally some of the values were different but converged and I forgot to do this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1171920546"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#pullrequestreview-1394343422", "body": ""}
{"comment": {"body": "I've added these to the Figma Library.\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5647#discussion_r1172816933"}}
{"title": "Fix embedding generation", "number": 5648, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5648"}
{"title": "Resolve Bitbucket @-mentions to the user display name in PR comments", "number": 5649, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5649", "body": "This adds support for rendering Bitbucket @-mentions in markdown.\nHowever, there is still follow on work to render @-mentions in titles and previews."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5649#pullrequestreview-**********", "body": ""}
{"title": "Convert service probes to HTTP", "number": 565, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/565", "body": "Converted existing TCP probes to HTTP on __health\nAdded a startup probe\nAdded SERVICE_GIT_SHA env var\nRegenerated base charts for pusher and api services to include changes mentioned above."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/565#pullrequestreview-907830641", "body": "amazing - thanks"}
{"title": "Revert \"Update login UI\"", "number": 5650, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5650", "body": "Reverts NextChapterSoftware/unblocked#5637"}
{"title": "Fix login button", "number": 5651, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5651", "body": "Single login button was missing click handler."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5651#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Change here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5651#discussion_r1166983220"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5651#pullrequestreview-**********", "body": ""}
{"title": "Fix create insight intelli j", "number": 5652, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5652", "body": "Fix Create Insight issues from demo Fridays.\n\nMerge issue where typescript types were invalid... Not sure how builds did not catch this\nCreate Insight gutter icon renderer was not being disposed properly."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5652#pullrequestreview-1386010470", "body": ""}
{"comment": {"body": "forEach makes more sense then map -- we're not mapping to a result here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5652#discussion_r1167164666"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5652#pullrequestreview-1386014477", "body": ""}
{"comment": {"body": "FWIW if you track allEditors in EditorService, then we wouldn't need this interstitial class that maps from files to editors.\r\n\r\nThe only reason we needed this structure for sourcemarks is because we don't want to duplicate SM mapping and resolution, we want a single resolution event per file, that then is demuxed out to each editor.  For this selection listener, we just want to listen to every editor, so it can be simple.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5652#discussion_r1167166771"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5652#pullrequestreview-1386014692", "body": ""}
{"title": "[unb 1153] bitbucket repo selection UI to selects", "number": 5653, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653", "body": "Add Repo Selector during web onboarding..\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#pullrequestreview-1388871348", "body": ""}
{"comment": {"body": "Use the same verb as the action button `Add`?\r\n```suggestion\r\n        return <div className=\"repo_connector__empty\">Add repositories.</div>;\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#discussion_r1169224569"}}
{"comment": {"body": "> Unblocked will request access?\r\n\r\nWe need to reword this, because it sounds like we actively request access, which we do not. Perhaps we can mention that the user can change this later in settings?\r\n\r\n```suggestion\r\n                description=\"Add specific repositories. You can add more repositories from settings later.\"\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#discussion_r1169228084"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#pullrequestreview-1388871797", "body": ""}
{"comment": {"body": "Might make more sense to have the mapped variant as a separate `removeMapped`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#discussion_r1169220724"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#pullrequestreview-1388887935", "body": ""}
{"comment": {"body": "```suggestion\r\n            <h1>Which repositories would you like to connect?</h1>\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#discussion_r1169231211"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#pullrequestreview-1388895716", "body": ""}
{"comment": {"body": "I suspect this may need to be displayed somewhere?   Dunno if this operation is ever likely to fail or not?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#discussion_r1169236201"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#pullrequestreview-1388902475", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#pullrequestreview-1388927080", "body": ""}
{"comment": {"body": "I don't think so? As most APIs, we don't expect it to fail...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5653#discussion_r1169256732"}}
{"title": "Drop pulsing Upgrade indicator on menubar icon", "number": 5654, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5654", "body": "It's very sad, but this has to go. Just witnessed the pulsing icon responsible for pinning app CPU to 50%"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5654#pullrequestreview-1386040373", "body": ""}
{"title": "Dont include PullRequestComments and PullRequestReviews in count", "number": 5655, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5655", "body": "So that we don't double count as we migrate GitHub TLCs to threaded TLCs. Only to be merged once migration is done."}
{"comment": {"body": "Hello TLC", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5655#issuecomment-1512423826"}}
{"comment": {"body": "hello again", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5655#issuecomment-1512436251"}}
{"comment": {"body": "and again", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5655#issuecomment-1512452978"}}
{"comment": {"body": "hello", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5655#issuecomment-1512466965"}}
{"comment": {"body": "hello", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5655#issuecomment-1512467530"}}
{"comment": {"body": "hello", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5655#issuecomment-1512468169"}}
{"comment": {"body": "hero", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5655#issuecomment-1512468242"}}
{"comment": {"body": "hello edit", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5655#issuecomment-1512471268"}}
{"title": "Dont query db for PullRequestComments and PullRequestReviews if theyre threaded", "number": 5656, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5656", "body": "Just some prep work in anticipation of migrating GitHub TLCs (PullRequestCommentModel and PullRequestReviewModel) to threaded TLCs. \nThis change skips going out to the database to retrieve PullRequestCommentModels and PullRequestReviewModels if the presence of threaded TLCs is detected. This is so that we don't return duplicate TLC comments during the migration."}
{"title": "Fix dashboard URLs in local environment", "number": 5657, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5657", "body": "As demoed this morning."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5657#pullrequestreview-1386109069", "body": ""}
{"title": "Set a timeout for \"Set up SDK\" step in the services workflow", "number": 5658, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5658", "body": "Usually this takes less than 2 min.\nWe should fast fail, rather than waiting for the entire job to timeout.\nSee also:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5658#pullrequestreview-1386172387", "body": ""}
{"title": "Route localhost traffic through an external address that proxyman can intercept", "number": 5659, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5659", "body": "This hostname is a DNS alias for localhost:\nlocalhost.proxyman.io\nSee also\n"}
{"comment": {"body": "didn't work", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5659#issuecomment-1515749148"}}
{"title": "Icon color fixes", "number": 566, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/566", "body": ""}
{"comment": {"body": "I'm actually not sure how to get around this for the webpanel icons. AFAIK those need to be absolute paths (`webPanel.iconPath`) which means we can't adjust the colors of the asset for the theme. This isn't a super immediate issue since this is only really an issue with the Notes yellow color and we won't be supporting notes for the first launch at least.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/566#issuecomment-1065583875"}}
{"comment": {"body": "Dear @kaych , I want you to know that we have not forgotten this pr!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/566#issuecomment-1110046386"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/566#pullrequestreview-910571296", "body": ""}
{"comment": {"body": "This feels like it should be a part of the Icons stylesheet, or maybe a separate root stylesheet we import in the webviews, instead of being inline here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/566#discussion_r827233201"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/566#pullrequestreview-910571860", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/566#pullrequestreview-911898987", "body": ""}
{"title": "Remove /getunblocked landing page", "number": 5660, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5660", "body": "We'll just use /download.\nThis saves us a huge amount on the landing page bundle size, as we were bundling the unblocked-download.svg as an inline image in the bundle, which is silly."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5660#pullrequestreview-1388574413", "body": ""}
{"title": "REmove model endpointws", "number": 5661, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5661"}
{"title": "Deploy privacy policy and ToS", "number": 5662, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5662", "body": "Ugly static HTML files.  This will test that they actually end up at the right place, and that our web server is configured to serve them correctly."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5662#pullrequestreview-1386650299", "body": ""}
{"title": "Login UI link to ToS and privacy pages", "number": 5663, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5663", "body": "Will wait until this is in before merging: https://github.com/NextChapterSoftware/unblocked/pull/5662\nAdd links to ToS and privacy policy in login page.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5663#pullrequestreview-1388495082", "body": ""}
{"title": "Hide threaded TLCs for GitHub and GitHubEnterprise", "number": 5664, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5664", "body": "Temporary change to prevent showing duplicate top-level comments in GitHub pull requests in the UI while we migrate them to threads."}
{"title": "Revert \"Hide threaded TLCs for GitHub and GitHubEnterprise (#5664)\"", "number": 5665, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5665", "body": "This reverts commit 2491abb1b677566f65b0e1777f9f1521055e4fba."}
{"title": "Restrict access to DEV", "number": 5666, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5666", "body": "This change declares a restricted access service to gate access to DEV based on provider host, org, and user.\nAnother PR will follow to consume the restricted access service in places where we create team and user accounts."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5666#pullrequestreview-**********", "body": "Nice Part 1 "}
{"title": "Create threads from top-level comments", "number": 5667, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5667", "body": "Creates threads from top-level comments during bulk ingestion and from webhooks received."}
{"title": "fix the path rewrite function", "number": 5668, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668", "body": "Fixed the path rewrites to handle RAW HTML (for landing page)\nAdded more tests to make sure we don't break the SPA routing (for dashboard)\nTested these changes in Dev and worked as expected. I know the solution is hacky but I couldn't find a better one."}
{"comment": {"body": "Let's go!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Is this going to transform all dashboard urls to end with `/index.html`? I'm not sure what impact that will have on ktor routing...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#discussion_r1168024851"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#pullrequestreview-**********", "body": ""}
{"comment": {"body": "No this is only executed on CloudFront Viewer requests before retrieving values from S3. Dashboard is an SPA and handles its own routing.\r\n\r\nThis logic applies only when we try to retrieve a file from S3", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#discussion_r1168038382"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This change has been deployed to Dev so you can see it in action. I didn't notice anything broken in Dev dashboard. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#discussion_r1168038460"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#pullrequestreview-**********", "body": ""}
{"comment": {"body": "`dev` looks correct to me, thanks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#discussion_r1168043383"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5668#pullrequestreview-1387096123", "body": ""}
{"title": "Bitbucket external IDs come in different formats", "number": 5669, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5669", "body": "Bitbucket is really odd. They have two different types of IDs, so we need\nto support both in the @-mention code:\n\n63ea8d19c5061c632c0c8c31\n557058:3a0bac81-3b25-46c3-af58-b56159e4d511"}
{"title": "Add readiness probe and update descriptions", "number": 567, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/567"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/567#pullrequestreview-907864662", "body": ""}
{"title": "Update UnblockedPRCommentService to support write-back for threaded TLCs", "number": 5670, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5670", "body": "Adding this so that once TLCs are migrated to threads, replying to the thread or updating a TLC writes back to GitHub and GitHub enterprise."}
{"title": "Better ToS", "number": 5671, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5671", "body": "Remove goofy bits at the top and bottom of the ToS"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5671#pullrequestreview-1388493371", "body": ""}
{"title": "Fix login spacing", "number": 5672, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5672", "body": "If the AvailableEnterpriseSection section is empty, right now the spacing is a bit off.  This fixes it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5672#pullrequestreview-1388641779", "body": ""}
{"title": "Use restricted access service to deny access to DEV", "number": 5673, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673", "body": "These entry points are restricted:\n- on-premise host creation\n- team creation\n- user creation"}
{"comment": {"body": "Added user auth tests here:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/5673/commits/1585b9c8b7cd864c8efd52b95f5fe90e1138fd0c", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673#issuecomment-1511917439"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673#pullrequestreview-1388686012", "body": ""}
{"comment": {"body": "`allowUser()?`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673#discussion_r1169100115"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673#pullrequestreview-1388686789", "body": ""}
{"comment": {"body": "lol, thanks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673#discussion_r1169100644"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673#pullrequestreview-1388690514", "body": ""}
{"comment": {"body": "fixed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673#discussion_r1169102855"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673#pullrequestreview-1388699393", "body": "Small nit, plus do we need a user auth test here too?"}
{"comment": {"body": "DRY this and declare up top?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5673#discussion_r1169109820"}}
{"title": "Add support for providing a transaction to PullRequestIngestionService methods", "number": 5674, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5674", "body": "No logic change"}
{"title": "UNB-1170: ML: Improve topic summary prompt", "number": 5675, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5675", "body": ""}
{"comment": {"body": "To be clear, we are not adding date or anything to the content we're passing to the prompt.\r\nIt's just raw text.\r\nI believe you added an <AUTHOR> @date to them...\r\nI've got to finaggle that into the prompt input. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5675#issuecomment-1512234148"}}
{"comment": {"body": "@rasharab: counting your amazing semantic SearchInsightQueryService to push the right documents, but you are right that `SummarizeTopicPromptDocumentInput`? or something likely needs to push the right metadata into the context. Just figuring out how to make changes in here. Sorry for the mess.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5675#issuecomment-1512240406"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5675#pullrequestreview-1389072027", "body": ""}
{"title": "Additional logging", "number": 5676, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5676", "body": "Adds additional logging to debug potential situations where errors are thrown in streams unexpectedly, freezing up the UI."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5676#pullrequestreview-1388869361", "body": ""}
{"title": "Consolidate IntelliJ sidebar", "number": 5677, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677", "body": "Merges explorer and insights sidebar into single toolwindow\n"}
{"comment": {"body": "What impact does this have on metrics?\r\n- IdeSidebarViewed\r\n- IdeInsightsViewed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#issuecomment-1515491150"}}
{"comment": {"body": "> What impact does this have on metrics?\r\n> \r\n> * IdeSidebarViewed\r\n> * IdeInsightsViewed\r\n\r\nGood question.  \r\nView metrics in general haven't been implemented yet.\r\nAdded task to track https://linear.app/unblocked/issue/UNB-1182/handle-viewed-metrics", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#issuecomment-1516918919"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#pullrequestreview-1388893665", "body": ""}
{"comment": {"body": "Preparing API so that agent can close / remove \"content\" from the sidebar.\r\n\r\nThis will primarily be used to show / hide sidebars based on auth state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#discussion_r1169234673"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#pullrequestreview-1388894928", "body": ""}
{"comment": {"body": "Hides the ID label in the header. This took far too long to figure out...\r\n\r\ne.g. \"Run\" in this image\r\n<img width=\"312\" alt=\"CleanShot 2023-04-17 at 13 26 58@2x\" src=\"https://user-images.githubusercontent.com/1553313/232602457-f6fb699a-4983-4f7d-8905-9737d2ed8f8b.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#discussion_r1169235667"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#pullrequestreview-1394734797", "body": "Moving away from SidebarContainer so that each sidebar type lives in its own content within IntelliJ"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#pullrequestreview-1394820127"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#pullrequestreview-1394821298", "body": ""}
{"comment": {"body": "If this class implements `ProjectCoroutineScope` then you get this for free.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#discussion_r1173131272"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#pullrequestreview-1394824671", "body": ""}
{"comment": {"body": "Ugh it's a little bit ugly to have the per-toolbar content wedged inside this (more generic) code.  Maybe not a big deal, but we maybe should factor out the per-view logic into a separate set of classes/interfaces that this class will call...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#discussion_r1173133493"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5677#pullrequestreview-1394832208", "body": ""}
{"title": "Install Bitbucket webhook during installation", "number": 5678, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5678"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5678#pullrequestreview-1389062788", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5678#pullrequestreview-1389077859", "body": ""}
{"comment": {"body": "Local stack users need to override these in their user-specific config override for webhooks locally.\r\n\r\nSee for example:\r\n```\r\nprojects/clients/client-scm/src/main/resources/config/scm-local-richie.conf\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5678#discussion_r1169356240"}}
{"title": "Allow direct asset import in VSCode webviews", "number": 5679, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5679"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5679#pullrequestreview-1389037024", "body": ""}
{"title": "Use correct path for readines checks", "number": 568, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/568", "body": "To be merged after https://github.com/NextChapterSoftware/unblocked/pull/567"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/568#pullrequestreview-907875532", "body": ""}
{"title": "Add ThreadModel.isTopLevelCommentThread property", "number": 5680, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5680", "body": "In theory we could do a left join and check that there is no sourcemark associated with the thread, but slack threads don't have an associated sourcemark. I could add an additional clause to ignore slack threads but if in the future we add another kind of thread that doesn't have a sourcemark (like issues) then we'd have to remember to update the clause here.\nIt's just less fragile to add a ThreadModel.isTopLevelCommentThread property."}
{"title": "Filter out pull request comments if theyve been threaded", "number": 5681, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5681"}
{"title": "UNB-1112 Update dashboard message layout", "number": 5682, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682", "body": "\n\n\n"}
{"comment": {"body": "> I'm guessing you tested out the message/thread view layouts in IDEs as well?\r\n\r\nYes. All the changes here are in the client specific scss files so there shouldn't be cross-contamination.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682#issuecomment-1512265451"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682#pullrequestreview-1389093384", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682#pullrequestreview-1389093589", "body": ""}
{"comment": {"body": "Kind of a gross hack to get around the how Sofia Pro sits low on the line height. This helps vertically center the subline to the icon. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682#discussion_r1169367211"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682#pullrequestreview-1389093593", "body": ""}
{"comment": {"body": "I don't really understand what this is doing, but the arbitrary margins and measurements don't feel great... don't know if there are any alternatives though", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682#discussion_r1169367214"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682#pullrequestreview-1389094784", "body": "I'm guessing you tested out the message/thread view layouts in IDEs as well?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682#pullrequestreview-1389099558", "body": ""}
{"comment": {"body": "Yeah I've tried to add comments to describe how I've calculated these numbers but it's still generally not great. The good news is I'll probably be revisiting this soon if we end up refactoring the PR view components to look like the message layout components so hopefully a lot of these values will go away.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5682#discussion_r1169370392"}}
{"title": "Add Bitbucket webhook route", "number": 5683, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5683"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5683#pullrequestreview-1389273554", "body": ""}
{"title": "Unb 1170 ml improve topic summary prompt", "number": 5684, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5684"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5684#pullrequestreview-1389180480", "body": ""}
{"title": "Add testbed for embeddings", "number": 5685, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5685"}
{"title": "Add team setting for threaded top-level comments", "number": 5686, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5686"}
{"title": "Fix PullRequestIngestionService to allow null files for top-level threads", "number": 5687, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5687", "body": "The check for a null file only applies for code-level comments."}
{"title": "All repositories state in Repo selection", "number": 5688, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5688", "body": "Add state to enable installation for all current and future repos in an installation.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5688#pullrequestreview-1389301191", "body": ""}
{"comment": {"body": "Is this the correct place to update all?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5688#discussion_r1169497998"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5688#pullrequestreview-1390099929", "body": ""}
{"comment": {"body": "No. See above comments. Just set `installRepositoriesIds` to null.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5688#discussion_r1170024193"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5688#pullrequestreview-1390107730", "body": ""}
{"comment": {"body": "Let\u2019s put this in the `installation` object instead. Needs to be an optional Boolean, optional means unset.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5688#discussion_r1170029296"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5688#pullrequestreview-1390977124", "body": ""}
{"title": "Enable TLC notifications for insider teams", "number": 5689, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5689"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5689#pullrequestreview-1390649763", "body": ""}
{"title": "Add the platform version to the application", "number": 569, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/569"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/569#pullrequestreview-907894888", "body": "This might cause a deployment failure until my PR is merged https://github.com/NextChapterSoftware/unblocked/pull/570"}
{"title": "deal with another noisy rule", "number": 5690, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5690", "body": "This rule was noising and firing a ton of alarms in the middle of night. Added the exception for Calico containers. \nDeployed to Dev and Prod."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5690#pullrequestreview-1390660206", "body": ""}
{"title": "Remove unnecessary render cycle", "number": 5691, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5691", "body": "There is a useEffect within CreateInsight that triggers InsightCommand's Update.\nThis caused the postMessage to update (without a useCallback) causing a recursive rendering cycle."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5691#pullrequestreview-1390674174", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5691#pullrequestreview-1390691204", "body": ""}
{"title": "[DO NOT MERGE] Enable TLC notifications for all teams", "number": 5692, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5692"}
{"title": "Introduce new defaults for OnboardingV2", "number": 5693, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5693", "body": "It's a bit janky, but really the only way to get the experience we need for existing customers (which is not to show the onboarding flow).\nThe logic is as follows:\n- Existing customers will already be \"authed\", and their \"hasSeenTutorial\" flag will be true\n- This will load into memory on startup, so there is no race condition possible\n- We will set this flag to true at the tail end of the onboarding flow in the Hub, both in the service and on the client\n- The reason for having a client-side flag for this is to deal with what happens when users log out. We want to show the sign-in UX back in the hub instead of in the onboarding window. \n- One additional challenge remains, which is to deal with the case where they sign in with a different user that doesn't belong to an onboarded team"}
{"comment": {"body": "The onboarding flow should *always* be dictated by the server's onboarding flag.\r\n\r\nStoring this state locally is fragile. Also doesn't allow us to dynamically reset onboarding states.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5693#issuecomment-1515440653"}}
{"comment": {"body": "> The onboarding flow should _always_ be dictated by the server's onboarding flag.\r\n> \r\n> Storing this state locally is fragile. Also doesn't allow us to dynamically reset onboarding states.\r\n\r\nThere's a chicken and egg issue here, which is a bit subtle:\r\n- The first time the user opens the app, we will have no understanding of whether they have previously onboarded, so we have no choice but to show the Onboarding login window\r\n- If the user has previously onboarded and then logs out, their `Person` object is gone, so we have no backend insights. But we want to \"save\" the fact that they have gone through onboarding previously, and show the Popover style login view instead of the Onboarding window\r\n- If the user is still halfway through onboarding and logs out, then we want to show the Onboarding login window and not the popover\r\n\r\nIt's fundamentally impossible to determine what to show without logging in first, so at least for the initial login window we have no choice but to track this on the client.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5693#issuecomment-1515487532"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5693#pullrequestreview-1392942620", "body": ""}
{"comment": {"body": "IMO, I would change this from \"hasOnboarded\" to \"UsePopoverAuth\".\r\nWhen the user opens the hub for the first time, this will be false.\r\nTherefore, the \"window\" auth will appear.\r\nIf true, show the \"popover\" auth.\r\n\r\nAfter auth, we grab the person and then determine whether or not the tutorial window should appear. \r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5693#discussion_r1171894436"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5693#pullrequestreview-1393013360", "body": ""}
{"title": "WIP: JetBrains custom editor", "number": 5694, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5694"}
{"title": "Include pull request reviews in the TLC thread", "number": 5695, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5695"}
{"title": "Telling the LLM more about the usage seems to improve results", "number": 5696, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5696", "body": "Tests:\n- Topics\n- Machine Learning\n- Sourcemarks.\nDefinitely the best results so far.\nThere are a bunch of tools (prompt builders + dynamic loaders) that we may want to look at adopting too.\nWe can discuss those and may use one when we need to add expert descriptions?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5696#pullrequestreview-1390974653", "body": ""}
{"title": "Record invites sent and invites dismissed", "number": 5697, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5697"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5697#pullrequestreview-1391075583", "body": ""}
{"title": "GitHub TLC thread comes first", "number": 5698, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5698", "body": "We want the TLC thread to appear immediately under the PR description. This only applies to GitHub PRs since BitBucket and GitLab support threaded TLCs, so those will appear in order of creation date."}
{"title": "Add onboarding state logic", "number": 5699, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#pullrequestreview-1393014207", "body": ""}
{"comment": {"body": "Is there a reason we only want to do this for the hub auth & not the window auth?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#discussion_r1171944037"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#pullrequestreview-1394136935", "body": ""}
{"comment": {"body": "The problem is that this fires when the app launches in the hub closed state, which causes the login flow to stop dead in its tracks. \n\nWe probably need to be even more sophisticated and look for window close events to replace what this is doing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#discussion_r1172680392"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#pullrequestreview-1394494279", "body": ""}
{"comment": {"body": "@jeffrey-ng This logic basically does the same thing as your hook for the popover. The idea here is that the same behaviour should apply for when the window is up and when it's closed, just like the popOver. But it's an XOR situation. We don't want one clobbering the other", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#discussion_r1172915927"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#pullrequestreview-1396243519", "body": ""}
{"comment": {"body": "This seems aggressive... I'm assuming `permissionsGrantedAction` will cleanup this view?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#discussion_r1174064742"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#pullrequestreview-1396244937", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#pullrequestreview-1396270668", "body": ""}
{"comment": {"body": "Correct. Also the CPU impact is negligible - we do this in the old flow and previously measured the impact. 200 milliseconds was the polling frequency threshold that allowed it to feel \"responsive\"", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5699#discussion_r1174080074"}}
{"title": "App connects to DB", "number": 57, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/57", "body": "Not really functional. Just ensures schema is up to date."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/57#pullrequestreview-855776420", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/57#pullrequestreview-855777173", "body": ""}
{"comment": {"body": "I'm going to have to futz with this shortly.\r\nJust be aware of that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/57#discussion_r787010061"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/57#pullrequestreview-855777515", "body": "Wiht comments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/57#pullrequestreview-855795694", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/57#pullrequestreview-855802785", "body": ""}
{"title": "rename probe paths and regen charts", "number": 570, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/570", "body": "This can be merged once you have renamed paths in API spec \nRelated to #571"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/570#pullrequestreview-907915357", "body": ""}
{"title": "Repo selection UI in Settings", "number": 5700, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700", "body": "Add SCM settings to dashboard. Feature-flagged off in dashboard.\nCurrently shows Org Selector -> Repo Selector. Will be removing Org Selector soon.\n\n\nUpdates ConnectTeam to handle empty installation state (aka non org owner)\n\n"}
{"comment": {"body": "> <img alt=\"CleanShot 2023-04-18 at 16 42 15@2x\" width=\"1378\" src=\"https://user-images.githubusercontent.com/1553313/232927976-2ad4262f-36ca-4c08-9951-d96d5f444a1b.png\"> <img alt=\"CleanShot 2023-04-18 at 16 42 22@2x\" width=\"1523\" src=\"https://user-images.githubusercontent.com/1553313/232927979-fba1a94f-2329-43f9-9638-b53173c87e56.png\">\r\n\r\nIs there meant to be so much spacing above the content?? I'm assuming it's like 50% of the vh but I feel like it should probably cap off at some point instead of always sitting at 50", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#issuecomment-1515510450"}}
{"comment": {"body": "> > <img alt=\"CleanShot 2023-04-18 at 16 42 15@2x\" width=\"1378\" src=\"https://user-images.githubusercontent.com/1553313/232927976-2ad4262f-36ca-4c08-9951-d96d5f444a1b.png\"> <img alt=\"CleanShot 2023-04-18 at 16 42 22@2x\" width=\"1523\" src=\"https://user-images.githubusercontent.com/1553313/232927979-fba1a94f-2329-43f9-9638-b53173c87e56.png\">\r\n> \r\n> Is there meant to be so much spacing above the content?? I'm assuming it's like 50% of the vh but I feel like it should probably cap off at some point instead of always sitting at 50\r\n\r\nSupposed to be the same as Slack. I believe it's a hard coded margin.\r\nSpecifically this:\r\nhttps://github.com/NextChapterSoftware/unblocked/blob/3e141f6e9a2536d7b0ae349996870d1d9dfbfe9a/web/src/settings/Slack/Slack.scss#L28", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#issuecomment-1515516179"}}
{"comment": {"body": "@jeffrey-ng We should probably knock that down to a fixed value. 100px? How does that look on your screen", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#issuecomment-1515523267"}}
{"comment": {"body": "> You can add more repositories from settings later.\r\n\r\nThis part of the text doesn't make sense when you're already in settings.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#issuecomment-1515694061"}}
{"comment": {"body": "<img width=\"1368\" alt=\"CleanShot 2023-04-20 at 10 06 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/233438277-0f9402f6-00b2-4e8a-bc1d-309feff5a02d.png\">\r\n<img width=\"1394\" alt=\"CleanShot 2023-04-20 at 10 04 32@2x\" src=\"https://user-images.githubusercontent.com/1553313/233438282-f1a2410a-d94d-4d9c-a4e8-69f7925aecdb.png\">\r\n\r\nUpdated padding to 100px in settings", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Now that Matt fixed the asset imports, we can probably get rid of this file and add the `color` prop to the shared/ProviderIcon component. It would leverage this same iconsrc helper ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#discussion_r1171941099"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#pullrequestreview-**********", "body": ""}
{"comment": {"body": "What is this route? Does it not need a `team/{teamId}` prefix ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#discussion_r1171942419"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#pullrequestreview-**********", "body": ""}
{"comment": {"body": "nit: Looked into this interface -- feel like it could have a better descriptive name. `RepoData` or `RepoNameInterface` \r\n\r\nRename or not, I think it'd be super helpful to add a comment to the interface definition describing its properties.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#discussion_r1171943834"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#pullrequestreview-1393014185", "body": ""}
{"comment": {"body": "Same q, are these routes not gated by team?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#discussion_r1171944016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#pullrequestreview-1393014570", "body": ""}
{"comment": {"body": "For context, almost every route on the dashboard is prefixed w teamId, so I just want to understand whether it should be required here, and if not, why not", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#discussion_r1171944311"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#pullrequestreview-1393019444", "body": ""}
{"comment": {"body": "Yup. None of these need teamID as the teams don't exist yet.\r\n\r\nThe teams are actually created by a request on `/connect/installationId/repo`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#discussion_r1171947820"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#pullrequestreview-1393019807", "body": ""}
{"comment": {"body": "No need for team. \r\nThis success page is most likely going away very soon.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#discussion_r1171948137"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5700#pullrequestreview-1394478410", "body": ""}
{"title": "5 sentences is just too long, going the other way:", "number": 5701, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5701", "body": "Also, make sure that we don't emit 'Answer' in the response."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5701#pullrequestreview-1391075959", "body": ""}
{"title": "Add ScmPullRequestReview.asScmPrCommentTopLevel extension", "number": 5702, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5702", "body": "To be used when adding these to the TLC thread."}
{"title": "Update to java 17", "number": 5703, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5703", "body": "Required to run against newer versions of IntelliJ"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5703#pullrequestreview-1392485820", "body": ""}
{"title": "List all TLC threads under the pull request description", "number": 5704, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5704", "body": "Not just for GitHub."}
{"title": "Set a timeout for \"Set up SDK\" step in all workflows", "number": 5705, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5705", "body": "Prevents the job from running for 45 mins and then dying.\nSimilar to #5658."}
{"title": "UNB-1174: Add button to refresh approved topic descriptions", "number": 5706, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5706", "body": "\nAdded triggerTopicSummaryAllTopics to TopicsPage + Routing"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5706#pullrequestreview-**********", "body": ""}
{"title": "Calculate engagement metrics", "number": 5707, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5707", "body": "7 day\n30 day\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5707#pullrequestreview-**********", "body": ""}
{"title": "Use user engagement to gate invite flows", "number": 5708, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5708"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5708#pullrequestreview-**********", "body": ""}
{"title": "Ensure updating a TLC message sets the correct title on the thread", "number": 5709, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5709", "body": "Also make sure we trigger the required events when creating a TLC thread."}
{"title": "Refactor health checks", "number": 571, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/571", "body": "We provide two health checks:\n\ndeep check  to be used for startup and liveness probes\nshallow check  to be used for readiness probes\n\nRelated to https://github.com/NextChapterSoftware/unblocked/pull/570"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/571#pullrequestreview-907927380", "body": "Looks good to me."}
{"title": "FIx missing Provider", "number": 5710, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5710", "body": "Fixes missing provider which resulted in incorrect label in menu dropdown.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5710#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5710#pullrequestreview-**********", "body": ""}
{"comment": {"body": "why optional?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5710#discussion_r1171847296"}}
{"title": "Reformat PR views in clients", "number": 5711, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711", "body": "This is done in conjunction with the new TLC threads.\nvscode:\n\n\njetbrains:\n\n\ndashboard:\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-**********", "body": ""}
{"comment": {"body": "A bit strange that this is hard-coded to \"secondary\" but \"CodeBlock\" isn't.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171847834"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392879607", "body": ""}
{"comment": {"body": "ArrayUtils.firstOrUndefined to get the firstSourcePoint.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171853238"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392879962", "body": ""}
{"comment": {"body": "Same as above. FIrstOrUndefined", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171853443"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392884755", "body": ""}
{"comment": {"body": "Why is the CodeBlock at the message level?\r\nI still see it as a thread level item. The data to populate the CodeBlock is coming from the thread.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171856683"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392889651", "body": ""}
{"comment": {"body": "Remove", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171859832"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392894383", "body": ""}
{"comment": {"body": "Empty title?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171862998"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392900019", "body": ""}
{"comment": {"body": "Empty file", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171866629"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392903158", "body": ""}
{"comment": {"body": "Same here. Could end up with a mismatch on CodeBlock and FileHeader. Is that expected?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171868018"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392911052", "body": ""}
{"comment": {"body": "Is this PullRequestBlock supposed to represent a TLC?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171872633"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392913866", "body": ""}
{"comment": {"body": "It looks like title is optional on MessageView.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171874618"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392915713", "body": ""}
{"comment": {"body": "Yeah it's just an optional node that gets passed in to render -- right now this only gets passed in for the first/anchor message, per the new designs:\r\n\r\n<img width=\"512\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/233204614-b5c63725-2fba-4548-aedc-eb6ce39f9279.png\">\r\n\r\nThe reason being the MessageView has very specific grid styling pattern that requires a lot of ugly hacking around to replicate. It's much cleaner to pass in something to render within the body of the MessageView layout.\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171875850"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392916356", "body": ""}
{"comment": {"body": "Remove?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171876187"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392917112", "body": ""}
{"comment": {"body": "It's intended. I'll update this to make it clearer", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171876782"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392963603", "body": ""}
{"comment": {"body": "This MessageView specifically, yes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#discussion_r1171909160"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5711#pullrequestreview-1392968267", "body": ""}
{"title": "Bulk ingestion of GitHub PRs triggers review-only ingestion", "number": 5712, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5712", "body": "Part of the work to roll up review comments into the TLC thread."}
{"title": "Don't return review blocks without threads", "number": 5713, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5713", "body": "Removes blocks like"}
{"title": "Add pendingTeams API", "number": 5714, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5714", "body": "Everyone is approved for now until we add the implementation..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5714#pullrequestreview-**********", "body": "Don't need.\nWe're going to add this endpoint which is safer:\nGET /pendingTeams"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5714#pullrequestreview-**********", "body": ""}
{"title": "Fix health check", "number": 5715, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5715", "body": "Not clear why this affects Kube health checks.\nThis causes Kube health check request to respond with 406:\nkotlin\ncall.respond(status = HttpStatusCode.fromValue(200), message = response)\nThis does not:\nkotlin\ncontext.call.respond(HttpStatusCode.OK, \"Ok!\\n\")"}
{"title": "Unb 1170 topic summary prompt 3", "number": 5716, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5716", "body": "The previous prompt tended to list all the PR titles and make up users."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5716#pullrequestreview-**********", "body": ""}
{"title": "Initial fetch in Poller", "number": 5717, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5717", "body": "Instead of waiting for doPoll to run before fetching, trigger an initial fetch when all channels have not run yet."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5717#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I don't think the logic here is right.  It is a single per-team flag, which means only the first first poll will be relevant, instead of every channel being able to do their first fetch independently.\r\n\r\nI think the right way to do this is to do the initial channel fetch for every channel before polling, but this is tricky considering multiple clients might request polling on a channel...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5717#discussion_r1171927619"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5717#pullrequestreview-1392994341", "body": ""}
{"comment": {"body": "Ah yikes.  Should we be removing the other `shouldRetryRequest` invocation?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5717#discussion_r1171928945"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5717#pullrequestreview-1392996828", "body": ""}
{"comment": {"body": "Yeah. Updating that right now...\r\n\r\nI think we want poller events to trigger an auth refresh?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5717#discussion_r1171930747"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5717#pullrequestreview-1392998046", "body": ""}
{"comment": {"body": "Yes poller should result in an auth refresh", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5717#discussion_r1171931705"}}
{"title": "Ignore low relevance TLCs", "number": 5718, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5718"}
{"title": "Admin: rename activity events to make more human readable", "number": 5719, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5719"}
{"title": "Fix exception", "number": 572, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/572"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/572#pullrequestreview-907921748", "body": ""}
{"title": "Remove build cache lock", "number": 5720, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5720"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5720#pullrequestreview-1393014872", "body": ""}
{"title": "Admin: Declare more internal teams", "number": 5721, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5721", "body": "Declare more intenral teams\n\n\n\n\n\nTeams are only marked as internal in PROD. This is intended to best simulate\n   production conditions in non-PROD environments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5721#pullrequestreview-1393168399", "body": ""}
{"comment": {"body": "Only PROD declares inside teams, so that non-prod environments treat _all_ teams as external teams to best simulate production conditions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5721#discussion_r1172054728"}}
{"title": "Fix Retry logic", "number": 5722, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5722"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5722#pullrequestreview-1393022844", "body": ""}
{"title": "Respect the event createThreadUnread property", "number": 5723, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5723"}
{"title": "Add ability to invoke hugging face embeddings", "number": 5724, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5724", "body": "We need to be able to invoke hugging face instructor embedding from sagemaker.\nHugging face does not provide an inference endpoint for this model."}
{"title": "Update hugging face endpoints", "number": 5725, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5725"}
{"title": "Add a catch block for top-level comment ingestion", "number": 5726, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5726"}
{"title": "Admin: avatar style tweak", "number": 5727, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5727"}
{"title": "Persist all repo selection preference in DB", "number": 5728, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5728"}
{"title": "Minimum GitLab level to install Unblocked is Maintainer", "number": 5729, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5729"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5729#pullrequestreview-1394465834", "body": ""}
{"comment": {"body": "Hi Richie, I can't type", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5729#discussion_r1172897136"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5729#pullrequestreview-1394466608", "body": ""}
{"comment": {"body": "Again! and again!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5729#discussion_r1172897806"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5729#pullrequestreview-1394476050", "body": ""}
{"comment": {"body": "comment from UB\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/deb8a78a-d43a-46d9-a47e-ffd49a571017?message=eee72749-4ed9-45db-88d5-d935ac91a4aa).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5729#discussion_r1172904085"}}
{"title": "API service exits if schema migration fails", "number": 573, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/573", "body": "It's possible for someone to check in a model change that will fail schema migration; for example, adding a non-nullable column to an existing model where there already exists rows in the database.\nCurrently when this happens, SchemaManager.updateSchema will return and the application will proceed with starting up. This is not what we want, because the application will expect the database to be in a particular state. \nIt would be better for the application to hard fail and exit. This way deployment halts and we can be alerted so that we can revert the change.\nThis change updates updateSchema to check to see if schema migration succeeded and returns true if so. If it returns false, the application will exit.\nNote: when schema migration fails, the busy table in the database will have a row. In addition to reverting the offending change, we'll need to clear that table so that later schema changes can be attempted."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/573#pullrequestreview-910889935", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/573#pullrequestreview-910892608", "body": ""}
{"title": "Revert \"Ignore low relevance TLCs (#5718)\"", "number": 5730, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5730", "body": "This reverts commit ee3d6e4199342504292c04688334d1618ef4e459.\nA TLC thread with a single message that doesn't have valuable content will be archived, so this change is unnecessary."}
{"title": "Bitbucket PR hook handler", "number": 5731, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5731"}
{"title": "Add cache segment download timeout", "number": 5732, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5732", "body": "By default, the cache download will continue on for 10 minutes doing nothing.\nTo get around that, it is recommended to set a smaller itmeout and force a cache miss when it does timeout.\nThe timeout does not cause the build to fail, it just makes it a cache miss build."}
{"title": "Dont clear the next batch urls until bulk ingestion is complete", "number": 5733, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733", "body": "If bulk ingestion gets interrupted, clearing the URLs means it will restart from the beginning."}
{"comment": {"body": "Thanks Richie", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#issuecomment-1516685696"}}
{"comment": {"body": "Hi Richie, from GitHub", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#issuecomment-1516687839"}}
{"comment": {"body": "Hi Richie from GitHub again!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#issuecomment-1516689346"}}
{"comment": {"body": "why is this slow", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#issuecomment-1516691127"}}
{"comment": {"body": "What about now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#issuecomment-1516696200"}}
{"comment": {"body": "5", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#issuecomment-1516696530"}}
{"comment": {"body": "6", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#issuecomment-1516697120"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#pullrequestreview-1394330825", "body": "If you look at the change that introduce this code (#5497), the intention was only to \"clean things up\". So I think this is totally safe."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#pullrequestreview-1394534267", "body": ""}
{"comment": {"body": "Hi Richie this is David", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#discussion_r1172940296"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#pullrequestreview-1394534995", "body": ""}
{"comment": {"body": "@richiebres ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#discussion_r1172940754"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#pullrequestreview-1394536235", "body": ""}
{"comment": {"body": "foo", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5733#discussion_r1172941585"}}
{"title": "Admin: Show repo selection preference, and cleanup code", "number": 5734, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5734"}
{"title": "Standard upload timeout is too low", "number": 5735, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5735"}
{"title": "IncreaseTimeout2", "number": 5736, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5736", "body": "Standard upload timeout is too low\nIncrease timeout v2"}
{"title": "Accidentally reduced a timeout I should not have", "number": 5737, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5737"}
{"title": "chore(deps): update actions/checkout action to v3", "number": 5738, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5738", "body": "This PR contains the following updates:\n| Package | Type | Update | Change |\n|---|---|---|---|\n| actions/checkout | action | major | v2 -> v3 |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nactions/checkout\n\n### [`v3`](https://togithub.com/actions/checkout/blob/HEAD/CHANGELOG.md#v352)\n\n[Compare Source](https://togithub.com/actions/checkout/compare/v2...v3)\n\n-   [Fix api endpoint for GHES](https://togithub.com/actions/checkout/pull/1289)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency io.lettuce:lettuce-core to v6.2.4.release", "number": 5739, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5739", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.lettuce:lettuce-core | 6.2.3.RELEASE -> 6.2.4.RELEASE |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nlettuce-io/lettuce-core\n\n### [`v6.2.4.RELEASE`]()\n\n[Compare Source]()\n\n#### :green_book: Links\n\n-   Reference documentation: \n-   Javadoc: \n\n#### :star: New Features\n\n-   Add capability of FailOver with takeOver option [#2358]()\n-   Improve `AdaptiveRefreshTriggeredEvent` to provide the cause and contextual details [#2338]()\n-   Refine `RedisException` instantiation to avoid exception instances if they are not used [#2353]()\n\n#### :lady_beetle: Bug Fixes\n\n-   Fix long overflow in `RedisSubscription#potentiallyReadMore` [#2383]()\n-   Consistently implement CompositeArgument in arg types [#2387]()\n\n#### :bulb: Other\n\n-   README.md demo has an error [#2377]()\n-   Fix Set unit test sscanMultiple fail in redis7 [#2349]()\n\n#### :heart: Contributors\n\nWe'd like to thank all the contributors who worked on this release!\n\n-   [@SreedharReddyKallu]()\n-   [@jacob-pro]()\n-   [@manzhizhen]()\n-   [@mindas]()\n-   [@yangbodong22011]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Properly handle statuses for custom exceptions", "number": 574, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/574", "body": "The StatusPages handler was effectively intercepting all exceptions and setting them to 500s. There are a class of exceptions, including some of Ktor's default exceptions, which need to be handled upstream so they can adopt the correct error responses. \nAdditionally, since Ktor 2.0.0, exceptions are logged and dealt with internally ahead of StatusPages handling, meaning we should not re-throw exceptions, and in fact they've removed re-throws from their sample code for 2.0.0. This is a much better model IMO. It makes no sense to have plugins get in the middle of the exception handling pipeline unless they are explicitly for that purpose. \nAdditionally, throwing exceptions in the status pages plugin creates problems for test applications. I've filed a bug report against Ktor for that issue here:  \nDefault exception handling in Ktor is given by this function:\nkotlin\npublic fun defaultExceptionStatusCode(cause: Throwable): HttpStatusCode? {\n    return when (cause) {\n        is BadRequestException - HttpStatusCode.BadRequest\n        is NotFoundException - HttpStatusCode.NotFound\n        is UnsupportedMediaTypeException - HttpStatusCode.UnsupportedMediaType\n        is TimeoutException, is TimeoutCancellationException - HttpStatusCode.GatewayTimeout\n        else - null\n    }\n}\nThis list is incomplete though, and even Ktor's samples demonstrate potential custom status handling of some other common cases. I've added UnauthorizedException and ForbiddenException, which are helpful in the pipeline to generalize common error cases. \nI'm looking for feedback on one specific question: should top level API delegates capture and handle all possible error states explicitly? Or should we allow exceptions to bubble up to status handlers?\nThe former feels more in line with a tight API definition, but it creates tight coupling with the underlying implementation..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/574#pullrequestreview-908258332", "body": ""}
{"comment": {"body": "The logic behind this is to check to see if Ktor's default exception handling will provide a status code for this exception, and if so, skip the status handling logic here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/574#discussion_r825565404"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/574#pullrequestreview-908258522", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/574#pullrequestreview-909523856", "body": ""}
{"title": "fix(deps): update dependency io.milvus:milvus-sdk-java to v2.2.5", "number": 5740, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5740", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.milvus:milvus-sdk-java | 2.2.2 -> 2.2.5 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nmilvus-io/milvus-sdk-java\n\n### [`v2.2.5`](): milvus-sdk-java-2.2.5\n\nRelease date2023-04-04\n\nCompatible with Milvus v2.2.x\n\nImprovement\n\n-   Implement flushAll() interface\n-   Add ignoreGrowing flag for query/search\n\n### [`v2.2.4`](): milvus-sdk-java-2.2.4\n\nRelease date2023-03-26\n\nCompatible with Milvus v2.2.x\n\nImprovement\n\n-   Implement alterCollection() interface\n-   Use the same grpc version v1.46.0 as milvus-proto repo\n\n### [`v2.2.3`](): milvus-sdk-java-2.2.3\n\nRelease date2023-02-11\n\nCompatible with Milvus v2.2.x\n\nImprovement\n\n-   Implement getLoadState() interface\n-   Add refresh parameter to load() interface\n-   Add getProcess() for bulkinsert task state\n-   Fix example error\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency io.pinecone:pinecone-client to v0.2.3", "number": 5741, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5741", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.pinecone:pinecone-client | 0.2.2 -> 0.2.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\npinecone-io/pinecone-java-client\n\n### [`v0.2.3`]()\n\n-   Update to use latest protos\n    -   Ability to use SparseValues\n    -   Filters on describe index stats calls\n-   Remove vulnerable dependencies\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update actions/setup-java action to v3.11.0", "number": 5742, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5742", "body": "This PR contains the following updates:\n| Package | Type | Update | Change |\n|---|---|---|---|\n| actions/setup-java | action | minor | v3.10.0 -> v3.11.0 |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nactions/setup-java\n\n### [`v3.11.0`](https://togithub.com/actions/setup-java/releases/tag/v3.11.0)\n\n[Compare Source](https://togithub.com/actions/setup-java/compare/v3.10.0...v3.11.0)\n\nIn scope of this release we added support for IBM Semeru (OpenJ9) JDK ([https://github.com/actions/setup-java/pull/289](https://togithub.com/actions/setup-java/pull/289)).\n\n```yaml\nsteps:\n - name: Checkout\n   uses: actions/checkout@v3\n - name: Setup-java\n   uses: actions/setup-java@v3\n   with:\n     distribution: semeru\n     java-version: 17\n```\n\n##### Supported distributions\n\nCurrently, the following distributions are supported:\n\n| Keyword | Distribution | Official site | License\n|-|-|-|-|\n| `temurin` | Eclipse Temurin | [Link](https://adoptium.net/) | [Link](https://adoptium.net/about.html)\n| `zulu` | Azul Zulu OpenJDK | [Link](https://www.azul.com/downloads/zulu-community/?package=jdk) | [Link](https://www.azul.com/products/zulu-and-zulu-enterprise/zulu-terms-of-use/) |\n| `adopt` or `adopt-hotspot` | AdoptOpenJDK Hotspot | [Link](https://adoptopenjdk.net/) | [Link](https://adoptopenjdk.net/about.html) |\n| `adopt-openj9` | AdoptOpenJDK OpenJ9 | [Link](https://adoptopenjdk.net/) | [Link](https://adoptopenjdk.net/about.html) |\n| `liberica` | Liberica JDK | [Link](https://bell-sw.com/) | [Link](https://bell-sw.com/liberica_eula/) |\n| `microsoft` | Microsoft Build of OpenJDK | [Link](https://www.microsoft.com/openjdk) | [Link](https://docs.microsoft.com/java/openjdk/faq)\n| `corretto` | Amazon Corretto Build of OpenJDK | [Link](https://aws.amazon.com/corretto/) | [Link](https://aws.amazon.com/corretto/faqs/)\n| `semeru` | IBM Semeru Runtime Open Edition | [Link](https://developer.ibm.com/languages/java/semeru-runtimes/downloads/) | [Link](https://openjdk.java.net/legal/gplv2+ce.html) |\n| `oracle` | Oracle JDK | [Link](https://www.oracle.com/java/technologies/downloads/) | [Link](https://java.com/freeuselicense)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Add submodules to macOS installer build", "number": 5743, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5743"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5743#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5743#pullrequestreview-**********", "body": ""}
{"title": "Byepass gradle cache for now", "number": 5744, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5744"}
{"title": "Temporarily disable bulk ingestion paths", "number": 5745, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5745", "body": "We need to re-run bulk ingestion to generate TLC threads but we only need to ingest issue comments, not PR or review comments. I'll revert this later today."}
{"title": "Push the GH PAT to the macOS builder", "number": 5746, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5746"}
{"title": "Add GH PAT secret to macos build definition", "number": 5747, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5747"}
{"title": "Remove temporary bulk ingestion changes used for TLC thread generation", "number": 5748, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5748", "body": "Only to be merged after we've run it for existing teams."}
{"title": "DisableGradleCache", "number": 5749, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5749", "body": "Byepass gradle cache for now\nGoodbye gradle cache"}
{"title": "force helm charts to update", "number": 575, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/575", "body": "We recently renamed our health endpoints. My last PR modified the path in Deployment specs but it seems helm charts were not properly regenerated. \nMy last PR with the actual path change: https://github.com/NextChapterSoftware/unblocked/pull/570"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/575#pullrequestreview-909076382", "body": ""}
{"title": "Fix notification title in Hub for TLCs", "number": 5750, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5750"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5750#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5750#pullrequestreview-**********", "body": ""}
{"title": "Fix message header icons", "number": 5751, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5751"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5751#pullrequestreview-**********", "body": ""}
{"title": "Revert \"Fix notification title in Hub for TLCs\"", "number": 5752, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5752", "body": "Reverts NextChapterSoftware/unblocked#5750\nProblem with the change: Hub notifications for the first message in a thread (code-level or top-level) will show duplicate content in the 1st and 3rd line."}
{"comment": {"body": "Follow on Hub changes\r\nhttps://www.notion.so/nextchaptersoftware/Hub-Messaging-Improvements-5288f91683414c3aa37c751214381f6c?pvs=4", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5752#issuecomment-1522382723"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5752#pullrequestreview-1394564012", "body": ""}
{"comment": {"body": "Revert this revert ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5752#discussion_r1172959461"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5752#pullrequestreview-1394565188", "body": ""}
{"comment": {"body": "sfsdfdsf", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5752#discussion_r1172960251"}}
{"title": "chore(deps): update dependency winston to v3.8.2", "number": 5753, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5753", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| winston | 3.5.1 -> 3.8.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nwinstonjs/winston\n\n### [`v3.8.2`](https://togithub.com/winstonjs/winston/blob/HEAD/CHANGELOG.md#v382-httpsgithubcomwinstonjswinstoncomparev381v382)\n\n[Compare Source](https://togithub.com/winstonjs/winston/compare/v3.8.1...v3.8.2)\n\n##### Patch-level changes\n\n-   Add `.js` to main entry point in package.json in [https://github.com/winstonjs/winston/pull/2177](https://togithub.com/winstonjs/winston/pull/2177); thanks to new contributor [@rumanbsl](https://togithub.com/rumanbsl)\n-   Small grammatical fixes in README.md in [https://github.com/winstonjs/winston/pull/2183](https://togithub.com/winstonjs/winston/pull/2183); thanks to new contributor [@mikebarr24](https://togithub.com/mikebarr24)\n-   Move colors to non-dev dependencies by [@wbt](https://togithub.com/wbt) in [https://github.com/winstonjs/winston/pull/2190](https://togithub.com/winstonjs/winston/pull/2190)\n\n##### Dependency updates by [@dependabot](https://togithub.com/dependabot) + CI autotesting\n\n-   Bump [@babel/preset-env](https://togithub.com/babel/preset-env) from 7.18.2 to 7.19.0 in [https://github.com/winstonjs/winston/pull/2189](https://togithub.com/winstonjs/winston/pull/2189)\n-   Bump [@babel/cli](https://togithub.com/babel/cli) from 7.17.10 to 7.18.10 in [https://github.com/winstonjs/winston/pull/2173](https://togithub.com/winstonjs/winston/pull/2173)\n-   Bump eslint from 8.18.0 to 8.23.0 in [https://github.com/winstonjs/winston/pull/2184](https://togithub.com/winstonjs/winston/pull/2184)\n-   Bump [@babel/core](https://togithub.com/babel/core) from 7.18.5 to 7.19.0 in [https://github.com/winstonjs/winston/pull/2192](https://togithub.com/winstonjs/winston/pull/2192)\n-   Bump logform from 2.4.1 to 2.4.2 in [https://github.com/winstonjs/winston/pull/2191](https://togithub.com/winstonjs/winston/pull/2191)\n\n### [`v3.8.1`](https://togithub.com/winstonjs/winston/blob/HEAD/CHANGELOG.md#v381-httpsgithubcomwinstonjswinstoncomparev380v381)\n\n[Compare Source](https://togithub.com/winstonjs/winston/compare/v3.8.0...v3.8.1)\n\n##### Patch-level changes\n\n-   Update types to match in-code definitions in [https://github.com/winstonjs/winston/pull/2157](https://togithub.com/winstonjs/winston/pull/2157); thanks to new contributor [@flappyBug](https://togithub.com/flappyBug)\n\n##### Dependency updates by [@dependabot](https://togithub.com/dependabot) + CI autotesting\n\n-   Bump logform from 2.4.0 to 2.4.1 in [https://github.com/winstonjs/winston/pull/2156](https://togithub.com/winstonjs/winston/pull/2156)\n-   Bump async from 3.2.3 to 3.2.4 in [https://github.com/winstonjs/winston/pull/2147](https://togithub.com/winstonjs/winston/pull/2147)\n\n### [`v3.8.0`](https://togithub.com/winstonjs/winston/blob/HEAD/CHANGELOG.md#v380-httpsgithubcomwinstonjswinstoncomparev372v380--2022-06-23)\n\n[Compare Source](https://togithub.com/winstonjs/winston/compare/v3.7.2...v3.8.0)\n\n##### Added functionality\n\n-   Add the stringify replacer option to the HTTP transport by [@domiins](https://togithub.com/domiins) in [https://github.com/winstonjs/winston/pull/2155](https://togithub.com/winstonjs/winston/pull/2155)\n\n##### Dependency updates by [@dependabot](https://togithub.com/dependabot) + CI autotesting\n\n-   Bump [@babel/core](https://togithub.com/babel/core) from 7.17.8 to 7.18.5\n-   Bump eslint from 8.12.0 to 8.18.0\n-   Bump [@types/node](https://togithub.com/types/node) from 17.0.23 to 18.0.0\n-   Bump [@babel/preset-env](https://togithub.com/babel/preset-env) from 7.16.11 to 7.18.2\n-   Bump [@babel/cli](https://togithub.com/babel/cli) from 7.17.6 to 7.17.10\n\n##### Updates facilitating repo maintenance & enhancing documentation\n\n-   Explicitly note that the Contributing.md file is out of date\n-   Add instructions for publishing updated version by [@wbt](https://togithub.com/wbt) (docs/publishing.md)\n-   Prettier Config File by [@jeanpierrecarvalho](https://togithub.com/jeanpierrecarvalho) in [https://github.com/winstonjs/winston/pull/2092](https://togithub.com/winstonjs/winston/pull/2092)\n-   Readme update to explain origin of errors for handling ([#2120](https://togithub.com/winstonjs/winston/issues/2120))\n-   update documentation for [#2114](https://togithub.com/winstonjs/winston/issues/2114) by [@zizifn](https://togithub.com/zizifn) in [https://github.com/winstonjs/winston/pull/2138](https://togithub.com/winstonjs/winston/pull/2138)\n-   enhance message for logs with no transports [#2114](https://togithub.com/winstonjs/winston/issues/2114) by [@zizifn](https://togithub.com/zizifn) in [https://github.com/winstonjs/winston/pull/2139](https://togithub.com/winstonjs/winston/pull/2139)\n-   Added a new Community Transport option to the list: Worker Thread based async Console Transport by [@arpad1337](https://togithub.com/arpad1337) in [https://github.com/winstonjs/winston/pull/2140](https://togithub.com/winstonjs/winston/pull/2140)\n\nThanks especially to new contributors [@zizifn](https://togithub.com/zizifn), [@arpad1337](https://togithub.com/arpad1337), [@domiins](https://togithub.com/domiins), & [@jeanpierrecarvalho](https://togithub.com/jeanpierrecarvalho)!\n\n### [`v3.7.2`](https://togithub.com/winstonjs/winston/blob/HEAD/CHANGELOG.md#v372--2022-04-04)\n\n[Compare Source](https://togithub.com/winstonjs/winston/compare/v3.7.1...v3.7.2)\n\nThis change reverts what should have been the feature-level update in 3.7.0 due to issue [#2103](https://togithub.com/winstonjs/winston/issues/2103) showing this to be breaking, unintentionally.\n\n### [`v3.7.1`](https://togithub.com/winstonjs/winston/blob/HEAD/CHANGELOG.md#v371--2022-04-04)\n\n[Compare Source](https://togithub.com/winstonjs/winston/compare/v3.6.0...v3.7.1)\n\nThis change includes some minor updates to package-lock.json resolving npm audit failures: one in [ansi-regex](https://togithub.com/advisories/GHSA-93q8-gq69-wqmw) and another in [minimist](https://togithub.com/advisories/GHSA-xvch-5gv4-984h).\n\n### [`v3.6.0`](https://togithub.com/winstonjs/winston/blob/HEAD/CHANGELOG.md#v360--2022-02-12)\n\n[Compare Source](https://togithub.com/winstonjs/winston/compare/v3.5.1...v3.6.0)\n\n-   \\[[#2057](https://togithub.com/winstonjs/winston/issues/2057)] Fix potential memory leak by not waiting for `process.nextTick` before clearing pending callbacks (thanks [@smashah](https://togithub.com/smashah)!)\n-   \\[[#2071](https://togithub.com/winstonjs/winston/issues/2071)] Update to `logform` 2.4.0, which includes changes such as new options for `JsonOptions` and some typo fixes regarding levels\n-   Various other dependencies are updated, tests are reorganized and cleaned up, etc. (thanks [@wbt](https://togithub.com/wbt), [@Maverick1872](https://togithub.com/Maverick1872), [@fearphage](https://togithub.com/fearphage)!)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update plugin org.jmailen.kotlinter to v3.14.0", "number": 5754, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5754", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.jmailen.kotlinter | 3.13.0 -> 3.14.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update activemqversion to v5.18.1", "number": 5755, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5755", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.apache.activemq:activemq-pool (source) | 5.17.4 -> 5.18.1 |  |  |  |  |\n| org.apache.activemq:activemq-client (source) | 5.17.4 -> 5.18.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Ensure that ThreadModel.lastMessageCreatedAt is correctly set for TLCs", "number": 5756, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5756", "body": "The transactions here are unnecessary since we do the same checks in the ingestion logic."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5756#pullrequestreview-1394677618", "body": "So, there's no logical change here?\nIs the idea that leaving the transaction open for a long time is causing DB contention?"}
{"comment": {"body": "This changes the return type. Is that intentional?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5756#discussion_r1173034322"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5756#pullrequestreview-1394680974", "body": ""}
{"comment": {"body": "Yup, nothing calling it uses the return value (otherwise it would fail to compile).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5756#discussion_r1173036481"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5756#pullrequestreview-1394681047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5756#pullrequestreview-1399110951"}
{"title": "chore(deps): update dependency gradle to v8.1", "number": 5757, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5757", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| gradle (source) | minor | 8.0.2 -> 8.1 |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Improve performance of embedding", "number": 5758, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5758", "body": "Move to ml instances.\nMove away from lambda to direct sagemaker invocations"}
{"title": "Add team access state", "number": 5759, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759", "body": "When this is deployed I will manually move all existing teams to Granted state. Currently unused, but teams have to be set to the right state before we deploy the filters for JWT and getTeams"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394815945", "body": ""}
{"comment": {"body": "This is a setting, so it should use `TeamSettingsStore` rather than directly on `TeamModel`.\r\n\r\nWe already have patterns for reading and writing these settings.\r\n<img width=\"841\" alt=\"Screenshot 2023-04-20 at 14 56 23\" src=\"https://user-images.githubusercontent.com/1798345/233496040-922b8617-6b35-47b8-8e4a-c787d7f0a784.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173127522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394816378", "body": ""}
{"comment": {"body": "Cool thx - wasn't aware of this. I'll move it over", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173127820"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394817493", "body": ""}
{"comment": {"body": "Thanks. Over time the settings will pile up, which is why we established this pattern.\n\n\n\nI know you have multiple states, but this could be accomplished equivalently using two settings:\n\n- Pending\n\n- Blocked", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173128593"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394818807", "body": ""}
{"comment": {"body": "What about `Allowed/Granted` though? Or is the absence of a setting an indication that they are allowed? What about default (should be Pending I think?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173129465"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394820020", "body": ""}
{"comment": {"body": "I think it will become important to distinguish between `Pending` and `Denied`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173130355"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394820688", "body": ""}
{"comment": {"body": "Did you see the blue bubble note?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173130854"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394821048", "body": ""}
{"comment": {"body": "You can set client defaults.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173131105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394821460", "body": ""}
{"comment": {"body": "So two settings:\n\n- Allowed (default false)\n\n- Blocked (default false)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173131377"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394821685", "body": ""}
{"comment": {"body": "Or just drop blocked for now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173131535"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394822158", "body": ""}
{"comment": {"body": "I see. If we're going to introduce a different binary for each value then I'll drop \"blocked\" for now and we can add later", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173131820"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394822486", "body": ""}
{"comment": {"body": "\ud83d\udc4d", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173132024"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394851753", "body": ""}
{"comment": {"body": "Updated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173154002"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394853881", "body": ""}
{"comment": {"body": "The idea behind this was that we could add different styling per list option. I started down that path but quickly backed out to focus on core functionality. Thought we'd keep this here in case we decide to do that in the future (some settings might be dangerous for example)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173155676"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394858303", "body": ""}
{"comment": {"body": "Add comment or make variable more descriptive.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173158963"}}
{"comment": {"body": "Will need a migration to backfill for current teams.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173159307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394871442", "body": ""}
{"comment": {"body": "Added migration and renamed to `userAccessAllowed`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173168771"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#pullrequestreview-1394885232", "body": ""}
{"comment": {"body": "cool", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5759#discussion_r1173178769"}}
{"title": "Video Channel API", "number": 576, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576", "body": "Implements the Video Channel API Delegate. See inline for walkthrough"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#pullrequestreview-909215412", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#pullrequestreview-909216061", "body": ""}
{"comment": {"body": "The client will subscribe to a push channel for this video channel, so we need to send back last-modified", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#discussion_r826249426"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#pullrequestreview-909528073", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#pullrequestreview-909627794", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#pullrequestreview-909628240", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#pullrequestreview-909628551", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#pullrequestreview-909629967", "body": ""}
{"comment": {"body": "Removed `modifiedAt` (and `If-Modified-Since`) because it carries no meaning for this API. Only the push service cares about this, which is yet to be implemented", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#discussion_r826557360"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/576#pullrequestreview-912602671", "body": ""}
{"title": "chore(deps): update node.js to v19.9.0", "number": 5760, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5760", "body": "This PR contains the following updates:\n| Package | Type | Update | Change |\n|---|---|---|---|\n| node | final | minor | 19.7.0-slim -> 19.9.0-slim |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnodejs/node\n\n### [`v19.9.0`](https://togithub.com/nodejs/node/releases/tag/v19.9.0): 2023-04-10, Version 19.9.0 (Current), @RafaelGSS\n\n[Compare Source](https://togithub.com/nodejs/node/compare/v19.8.1...v19.9.0)\n\n##### Notable Changes\n\n##### Tracing Channel in diagnostic_channel\n\n`TracingChannel` adds a new, high-performance channel to publish tracing data about the timing and purpose of function executions.\n\nContributed by Stephen Belanger in [#44943](https://togithub.com/nodejs/node/pull/44943)\n\n##### New URL.canParse API\n\nA new API was added to the URL. `URL.canParse` checks if an `input` with an optional base value can be parsed correctly\naccording to WHATWG URL specification.\n\n```js\nconst isValid = URL.canParse('/foo', 'https://example.org/'); // true\nconst isNotValid = URL.canParse('/foo'); // false\n```\n\nContributed by Khafra in [#47179](https://togithub.com/nodejs/node/pull/47179)\n\n##### Other notable changes\n\nevents:\n\n-   (SEMVER-MINOR) add getMaxListeners method (Khafra) [#47039](https://togithub.com/nodejs/node/pull/47039)\n    msi:\n-   (SEMVER-MINOR) migrate to WiX4 (Stefan Stojanovic) [#45943](https://togithub.com/nodejs/node/pull/45943)\n    node-api:\n-   (SEMVER-MINOR) deprecate napi_module_register (Vladimir Morozov) [#46319](https://togithub.com/nodejs/node/pull/46319)\n    stream:\n-   (SEMVER-MINOR) add setter & getter for default highWaterMark (Robert Nagy) [#46929](https://togithub.com/nodejs/node/pull/46929)\n    test_runner:\n-   (SEMVER-MINOR) expose reporter for use in run api (Chemi Atlow) [#47238](https://togithub.com/nodejs/node/pull/47238)\n\n##### Commits\n\n-   \\[[`2cea7d8141`](https://togithub.com/nodejs/node/commit/2cea7d8141)] - **benchmark**: fix invalid requirementsURL (Deokjin Kim) [#47378](https://togithub.com/nodejs/node/pull/47378)\n-   \\[[`6a4076a188`](https://togithub.com/nodejs/node/commit/6a4076a188)] - **benchmark**: lower URL.canParse runs (Khafra) [#47351](https://togithub.com/nodejs/node/pull/47351)\n-   \\[[`23a69d9279`](https://togithub.com/nodejs/node/commit/23a69d9279)] - **buffer**: fix blob range error with many chunks (Khafra) [#47320](https://togithub.com/nodejs/node/pull/47320)\n-   \\[[`e3d98c3e7a`](https://togithub.com/nodejs/node/commit/e3d98c3e7a)] - **buffer**: use private properties for brand checks in File (Khafra) [#47154](https://togithub.com/nodejs/node/pull/47154)\n-   \\[[`9dc6aef98d`](https://togithub.com/nodejs/node/commit/9dc6aef98d)] - **build**: bump github/codeql-action from 2.2.6 to 2.2.9 (dependabot\\[bot]) [#47366](https://togithub.com/nodejs/node/pull/47366)\n-   \\[[`910d2967f1`](https://togithub.com/nodejs/node/commit/910d2967f1)] - **build**: update stale action from v7 to v8 (Rich Trott) [#47357](https://togithub.com/nodejs/node/pull/47357)\n-   \\[[`666df20ad9`](https://togithub.com/nodejs/node/commit/666df20ad9)] - **build**: remove Python pip `--no-user` option (Christian Clauss) [#47372](https://togithub.com/nodejs/node/pull/47372)\n-   \\[[`3970537bb4`](https://togithub.com/nodejs/node/commit/3970537bb4)] - **build**: avoid usage of pipes library (Mohammed Keyvanzadeh) [#47271](https://togithub.com/nodejs/node/pull/47271)\n-   \\[[`254a03b2eb`](https://togithub.com/nodejs/node/commit/254a03b2eb)] - **crypto**: unify validation of checkPrime checks (Tobias Nieen) [#47165](https://togithub.com/nodejs/node/pull/47165)\n-   \\[[`8e1e9edc57`](https://togithub.com/nodejs/node/commit/8e1e9edc57)] - **deps**: update timezone to 2023c (Node.js GitHub Bot) [#47302](https://togithub.com/nodejs/node/pull/47302)\n-   \\[[`30c043c2b9`](https://togithub.com/nodejs/node/commit/30c043c2b9)] - **deps**: update timezone to 2023b (Node.js GitHub Bot) [#47256](https://togithub.com/nodejs/node/pull/47256)\n-   \\[[`40be01bc9c`](https://togithub.com/nodejs/node/commit/40be01bc9c)] - **deps**: update simdutf to 3.2.3 (Node.js GitHub Bot) [#47331](https://togithub.com/nodejs/node/pull/47331)\n-   \\[[`4b09222569`](https://togithub.com/nodejs/node/commit/4b09222569)] - **deps**: upgrade npm to 9.6.3 (npm team) [#47325](https://togithub.com/nodejs/node/pull/47325)\n-   \\[[`2a6c23ea5e`](https://togithub.com/nodejs/node/commit/2a6c23ea5e)] - **deps**: update corepack to 0.17.1 (Node.js GitHub Bot) [#47156](https://togithub.com/nodejs/node/pull/47156)\n-   \\[[`06b718363d`](https://togithub.com/nodejs/node/commit/06b718363d)] - **deps**: V8: cherry-pick [`3e4952c`](https://togithub.com/nodejs/node/commit/3e4952cb2a59) (Richard Lau) [#47236](https://togithub.com/nodejs/node/pull/47236)\n-   \\[[`7e24498d81`](https://togithub.com/nodejs/node/commit/7e24498d81)] - **deps**: upgrade npm to 9.6.2 (npm team) [#47108](https://togithub.com/nodejs/node/pull/47108)\n-   \\[[`7a4beaa182`](https://togithub.com/nodejs/node/commit/7a4beaa182)] - **deps**: V8: cherry-pick [`215ccd5`](https://togithub.com/nodejs/node/commit/215ccd593edb) (Joyee Cheung) [#47212](https://togithub.com/nodejs/node/pull/47212)\n-   \\[[`8a69929f23`](https://togithub.com/nodejs/node/commit/8a69929f23)] - **deps**: V8: cherry-pick [`975ff4d`](https://togithub.com/nodejs/node/commit/975ff4dbfd1b) (Debadree Chatterjee) [#47209](https://togithub.com/nodejs/node/pull/47209)\n-   \\[[`10569de53f`](https://togithub.com/nodejs/node/commit/10569de53f)] - **deps**: cherry-pick win/arm64/clang fixes (Cheng Zhao) [#47011](https://togithub.com/nodejs/node/pull/47011)\n-   \\[[`ff6070eb1d`](https://togithub.com/nodejs/node/commit/ff6070eb1d)] - **deps**: V8: cherry-pick [`cb30b8e`](https://togithub.com/nodejs/node/commit/cb30b8e17429) (Darshan Sen) [#47307](https://togithub.com/nodejs/node/pull/47307)\n-   \\[[`0bbce034f9`](https://togithub.com/nodejs/node/commit/0bbce034f9)] - **doc**: add a note about os.cpus() returning an empty list (codedokode) [#47363](https://togithub.com/nodejs/node/pull/47363)\n-   \\[[`f8511e0b27`](https://togithub.com/nodejs/node/commit/f8511e0b27)] - **doc**: clarify reports are only evaluated on active versions (Rafael Gonzaga) [#47341](https://togithub.com/nodejs/node/pull/47341)\n-   \\[[`863b4d9c5b`](https://togithub.com/nodejs/node/commit/863b4d9c5b)] - **doc**: remove Vladimir de Turckheim from Security release stewards (Vladimir de Turckheim) [#47318](https://togithub.com/nodejs/node/pull/47318)\n-   \\[[`2192b5b163`](https://togithub.com/nodejs/node/commit/2192b5b163)] - **doc**: add importing util to example of \\`process.report.getReport' (Deokjin Kim) [#47298](https://togithub.com/nodejs/node/pull/47298)\n-   \\[[`1c21fbfa9a`](https://togithub.com/nodejs/node/commit/1c21fbfa9a)] - **doc**: vm.SourceTextModule() without context option (Axel Kittenberger) [#47295](https://togithub.com/nodejs/node/pull/47295)\n-   \\[[`89445fbea9`](https://togithub.com/nodejs/node/commit/89445fbea9)] - **doc**: make win arm64 tier 2 platform (Stefan Stojanovic) [#47233](https://togithub.com/nodejs/node/pull/47233)\n-   \\[[`296577a549`](https://togithub.com/nodejs/node/commit/296577a549)] - **doc**: document process for sharing project news (Michael Dawson) [#47189](https://togithub.com/nodejs/node/pull/47189)\n-   \\[[`e29a1462c7`](https://togithub.com/nodejs/node/commit/e29a1462c7)] - **doc**: revise example of assert.CallTracker (Deokjin Kim) [#47252](https://togithub.com/nodejs/node/pull/47252)\n-   \\[[`bac893adbe`](https://togithub.com/nodejs/node/commit/bac893adbe)] - **doc**: fix typo in SECURITY.md (Rich Trott) [#47282](https://togithub.com/nodejs/node/pull/47282)\n-   \\[[`0949f238d1`](https://togithub.com/nodejs/node/commit/0949f238d1)] - **doc**: use serial comma in cli docs (Tobias Nieen) [#47262](https://togithub.com/nodejs/node/pull/47262)\n-   \\[[`71246247a9`](https://togithub.com/nodejs/node/commit/71246247a9)] - **doc**: improve example for Error.captureStackTrace() (Julian Dax) [#46886](https://togithub.com/nodejs/node/pull/46886)\n-   \\[[`0b2ba441b2`](https://togithub.com/nodejs/node/commit/0b2ba441b2)] - **doc**: clarify http error events after calling destroy() (Zach Bjornson) [#46903](https://togithub.com/nodejs/node/pull/46903)\n-   \\[[`a21459e0d5`](https://togithub.com/nodejs/node/commit/a21459e0d5)] - **doc**: update output of example in AbortController (Deokjin Kim) [#47227](https://togithub.com/nodejs/node/pull/47227)\n-   \\[[`7a2090c14c`](https://togithub.com/nodejs/node/commit/7a2090c14c)] - **doc**: drop one-week branch sync on major releases (Rafael Gonzaga) [#47149](https://togithub.com/nodejs/node/pull/47149)\n-   \\[[`eb4de0043d`](https://togithub.com/nodejs/node/commit/eb4de0043d)] - **doc**: fix grammar in the collaborator guide (Mohammed Keyvanzadeh) [#47245](https://togithub.com/nodejs/node/pull/47245)\n-   \\[[`908798ae19`](https://togithub.com/nodejs/node/commit/908798ae19)] - **doc**: update stream.reduce concurrency note (Raz Luvaton) [#47166](https://togithub.com/nodejs/node/pull/47166)\n-   \\[[`36c118bc92`](https://togithub.com/nodejs/node/commit/36c118bc92)] - **doc**: remove use of DEFAULT_ENCODING in PBKDF2 docs (Tobias Nieen) [#47181](https://togithub.com/nodejs/node/pull/47181)\n-   \\[[`7ec87fd5ce`](https://togithub.com/nodejs/node/commit/7ec87fd5ce)] - **doc**: fix typos in async_context.md (Shubham Sharma) [#47155](https://togithub.com/nodejs/node/pull/47155)\n-   \\[[`a03aaba996`](https://togithub.com/nodejs/node/commit/a03aaba996)] - **doc**: update collaborator guide to reflect TSC changes (Rich Trott) [#47126](https://togithub.com/nodejs/node/pull/47126)\n-   \\[[`c45a6977ec`](https://togithub.com/nodejs/node/commit/c45a6977ec)] - **doc**: clarify that `fs.create{Read,Write}Stream` support `AbortSignal` (Antoine du Hamel) [#47122](https://togithub.com/nodejs/node/pull/47122)\n-   \\[[`82c7757177`](https://togithub.com/nodejs/node/commit/82c7757177)] - **doc**: improve documentation for util.types.isNativeError() (Julian Dax) [#46840](https://togithub.com/nodejs/node/pull/46840)\n-   \\[[`8f9b9c17d5`](https://togithub.com/nodejs/node/commit/8f9b9c17d5)] - **doc**: rename the startup performance initiative to startup snapshot ([#47111](https://togithub.com/nodejs/node/issues/47111)) (Joyee Cheung)\n-   \\[[`c08995e897`](https://togithub.com/nodejs/node/commit/c08995e897)] - **doc**: indicate that `name` is no longer an optional argument (Daniel Roe) [#47102](https://togithub.com/nodejs/node/pull/47102)\n-   \\[[`316d626e61`](https://togithub.com/nodejs/node/commit/316d626e61)] - **doc**: fix \"maintaining dependencies\" heading typos (Keyhan Vakil) [#47082](https://togithub.com/nodejs/node/pull/47082)\n-   \\[[`a4b1a7761f`](https://togithub.com/nodejs/node/commit/a4b1a7761f)] - **esm**: skip file: URL conversion to path when possible (Antoine du Hamel) [#46305](https://togithub.com/nodejs/node/pull/46305)\n-   \\[[`c5cd6b7f3b`](https://togithub.com/nodejs/node/commit/c5cd6b7f3b)] - **(SEMVER-MINOR)** **events**: add getMaxListeners method (Khafra) [#47039](https://togithub.com/nodejs/node/pull/47039)\n-   \\[[`2c2b07ce5f`](https://togithub.com/nodejs/node/commit/2c2b07ce5f)] - **fs**: invalidate blob created from empty file when written to (Debadree Chatterjee) [#47199](https://togithub.com/nodejs/node/pull/47199)\n-   \\[[`e33dfce401`](https://togithub.com/nodejs/node/commit/e33dfce401)] - **inspector**: log response and requests in the inspector for debugging (Joyee Cheung) [#46941](https://togithub.com/nodejs/node/pull/46941)\n-   \\[[`f6ec81dc05`](https://togithub.com/nodejs/node/commit/f6ec81dc05)] - **inspector**: fix session.disconnect crash (theanarkh) [#46942](https://togithub.com/nodejs/node/pull/46942)\n-   \\[[`a738164fed`](https://togithub.com/nodejs/node/commit/a738164fed)] - **lib**: define Event.isTrusted in the prototype (Santiago Gimeno) [#46974](https://togithub.com/nodejs/node/pull/46974)\n-   \\[[`7d37dcdd9a`](https://togithub.com/nodejs/node/commit/7d37dcdd9a)] - **(SEMVER-MINOR)** **lib**: add tracing channel to diagnostics_channel (Stephen Belanger) [#44943](https://togithub.com/nodejs/node/pull/44943)\n-   \\[[`16d3dfa0aa`](https://togithub.com/nodejs/node/commit/16d3dfa0aa)] - **meta**: fix notable-change comment label url (Filip Skokan) [#47300](https://togithub.com/nodejs/node/pull/47300)\n-   \\[[`2c95f6e18b`](https://togithub.com/nodejs/node/commit/2c95f6e18b)] - **meta**: clarify the threat model to explain the JSON.parse case (Matteo Collina) [#47276](https://togithub.com/nodejs/node/pull/47276)\n-   \\[[`22b9acdbf8`](https://togithub.com/nodejs/node/commit/22b9acdbf8)] - **meta**: update link to collaborators discussion page (Michal Zasso) [#47211](https://togithub.com/nodejs/node/pull/47211)\n-   \\[[`dc024d930a`](https://togithub.com/nodejs/node/commit/dc024d930a)] - **meta**: automate description requests when notable change label is added (Danielle Adams) [#47078](https://togithub.com/nodejs/node/pull/47078)\n-   \\[[`54195357f3`](https://togithub.com/nodejs/node/commit/54195357f3)] - **meta**: move TSC voting member(s) to regular member(s) (Node.js GitHub Bot) [#47180](https://togithub.com/nodejs/node/pull/47180)\n-   \\[[`a3bffbaa11`](https://togithub.com/nodejs/node/commit/a3bffbaa11)] - **meta**: move TSC voting member to regular membership (Node.js GitHub Bot) [#46985](https://togithub.com/nodejs/node/pull/46985)\n-   \\[[`d2a6aa6ecd`](https://togithub.com/nodejs/node/commit/d2a6aa6ecd)] - **meta**: update GOVERNANCE.md to reflect TSC charter changes (Rich Trott) [#47126](https://togithub.com/nodejs/node/pull/47126)\n-   \\[[`b0aad345bf`](https://togithub.com/nodejs/node/commit/b0aad345bf)] - **meta**: ask expected behavior reason in bug template (Ben Noordhuis) [#47049](https://togithub.com/nodejs/node/pull/47049)\n-   \\[[`c03e79b141`](https://togithub.com/nodejs/node/commit/c03e79b141)] - **(SEMVER-MINOR)** **msi**: migrate to WiX4 (Stefan Stojanovic) [#45943](https://togithub.com/nodejs/node/pull/45943)\n-   \\[[`ca981be2b9`](https://togithub.com/nodejs/node/commit/ca981be2b9)] - **(SEMVER-MINOR)** **node-api**: deprecate napi_module_register (Vladimir Morozov) [#46319](https://togithub.com/nodejs/node/pull/46319)\n-   \\[[`77f7200cce`](https://togithub.com/nodejs/node/commit/77f7200cce)] - **node-api**: extend type-tagging to externals (Gabriel Schulhof) [#47141](https://togithub.com/nodejs/node/pull/47141)\n-   \\[[`55f3d215b8`](https://togithub.com/nodejs/node/commit/55f3d215b8)] - **node-api**: document node-api shutdown finalization (Chengzhong Wu) [#45903](https://togithub.com/nodejs/node/pull/45903)\n-   \\[[`b3fe2ba59b`](https://togithub.com/nodejs/node/commit/b3fe2ba59b)] - **node-api**: verify cleanup hooks order (Chengzhong Wu) [#46692](https://togithub.com/nodejs/node/pull/46692)\n-   \\[[`d6a12328a6`](https://togithub.com/nodejs/node/commit/d6a12328a6)] - **repl**: preserve preview on ESCAPE key press (Xuguang Mei) [#46878](https://togithub.com/nodejs/node/pull/46878)\n-   \\[[`33b0906640`](https://togithub.com/nodejs/node/commit/33b0906640)] - **sea**: fix memory leak detected by asan (Darshan Sen) [#47309](https://togithub.com/nodejs/node/pull/47309)\n-   \\[[`069515153f`](https://togithub.com/nodejs/node/commit/069515153f)] - **src**: remove usage of `std::shared_ptr::unique()` (Darshan Sen) [#47315](https://togithub.com/nodejs/node/pull/47315)\n-   \\[[`4405fc879a`](https://togithub.com/nodejs/node/commit/4405fc879a)] - **src**: use stricter compile-time guidance (Tobias Nieen) [#46509](https://togithub.com/nodejs/node/pull/46509)\n-   \\[[`bbde68e5de`](https://togithub.com/nodejs/node/commit/bbde68e5de)] - **src**: remove unused variable in crypto_x509.cc (Michal Zasso) [#47344](https://togithub.com/nodejs/node/pull/47344)\n-   \\[[`7a80312e19`](https://togithub.com/nodejs/node/commit/7a80312e19)] - **src**: don't reset embeder signal handlers (Dmitry Vyukov) [#47188](https://togithub.com/nodejs/node/pull/47188)\n-   \\[[`d0a5e7e342`](https://togithub.com/nodejs/node/commit/d0a5e7e342)] - **src**: fix some recently introduced coverity issues (Michael Dawson) [#47240](https://togithub.com/nodejs/node/pull/47240)\n-   \\[[`0a4ff2f9a0`](https://togithub.com/nodejs/node/commit/0a4ff2f9a0)] - **src**: replace impossible THROW with CHECK (Tobias Nieen) [#47168](https://togithub.com/nodejs/node/pull/47168)\n-   \\[[`2fd0f79963`](https://togithub.com/nodejs/node/commit/2fd0f79963)] - **src**: fix duplication of externalized builtin code (Keyhan Vakil) [#47079](https://togithub.com/nodejs/node/pull/47079)\n-   \\[[`36a026bf44`](https://togithub.com/nodejs/node/commit/36a026bf44)] - **src**: remove dead comments about return_code_cache (Keyhan Vakil) [#47083](https://togithub.com/nodejs/node/pull/47083)\n-   \\[[`aefe26692c`](https://togithub.com/nodejs/node/commit/aefe26692c)] - **src**: remove SSL_CTX_get_tlsext_ticket_keys guards (Tobias Nieen) [#47068](https://togithub.com/nodejs/node/pull/47068)\n-   \\[[`90f4e16350`](https://togithub.com/nodejs/node/commit/90f4e16350)] - **src**: fix clang 14 linker error (Keyhan Vakil) [#47057](https://togithub.com/nodejs/node/pull/47057)\n-   \\[[`b0809a73da`](https://togithub.com/nodejs/node/commit/b0809a73da)] - **src,http2**: ensure cleanup if a frame is not sent (ywave620) [#47244](https://togithub.com/nodejs/node/pull/47244)\n-   \\[[`1fc62c7b35`](https://togithub.com/nodejs/node/commit/1fc62c7b35)] - **(SEMVER-MINOR)** **stream**: add setter & getter for default highWaterMark ([#46929](https://togithub.com/nodejs/node/issues/46929)) (Robert Nagy) [#46929](https://togithub.com/nodejs/node/pull/46929)\n-   \\[[`b8c6ceddd5`](https://togithub.com/nodejs/node/commit/b8c6ceddd5)] - **stream**: expose stream symbols (Robert Nagy) [#45671](https://togithub.com/nodejs/node/pull/45671)\n-   \\[[`f37825660c`](https://togithub.com/nodejs/node/commit/f37825660c)] - **stream**: dont wait for next item in take when finished (Raz Luvaton) [#47132](https://togithub.com/nodejs/node/pull/47132)\n-   \\[[`8eceaaeb4d`](https://togithub.com/nodejs/node/commit/8eceaaeb4d)] - **test**: fix flaky test-watch-mode-inspect (Moshe Atlow) [#47403](https://togithub.com/nodejs/node/pull/47403)\n-   \\[[`db95ed0b1b`](https://togithub.com/nodejs/node/commit/db95ed0b1b)] - **test**: move debugger tests with --port=0 to parallel (Joyee Cheung) [#47274](https://togithub.com/nodejs/node/pull/47274)\n-   \\[[`041885ebd0`](https://togithub.com/nodejs/node/commit/041885ebd0)] - **test**: use --port=0 in debugger tests that do not have to work on 9229 (Joyee Cheung) [#47274](https://togithub.com/nodejs/node/pull/47274)\n-   \\[[`130420b9e1`](https://togithub.com/nodejs/node/commit/130420b9e1)] - **test**: run doctool tests in parallel (Joyee Cheung) [#47273](https://togithub.com/nodejs/node/pull/47273)\n-   \\[[`4b4336c34e`](https://togithub.com/nodejs/node/commit/4b4336c34e)] - **test**: verify tracePromise does not do runStores (Stephen Belanger) [#47349](https://togithub.com/nodejs/node/pull/47349)\n-   \\[[`54261f3294`](https://togithub.com/nodejs/node/commit/54261f3294)] - **test**: run WPT files in parallel again (Filip Skokan) [#47283](https://togithub.com/nodejs/node/pull/47283)\n-   \\[[`e2eb0543be`](https://togithub.com/nodejs/node/commit/e2eb0543be)] - **test**: update wasm/jsapi WPT (Michal Zasso) [#47210](https://togithub.com/nodejs/node/pull/47210)\n-   \\[[`d341d0389f`](https://togithub.com/nodejs/node/commit/d341d0389f)] - **test**: skip test-wasm-web-api on ARM (Michal Zasso) [#47299](https://togithub.com/nodejs/node/pull/47299)\n-   \\[[`567573b16a`](https://togithub.com/nodejs/node/commit/567573b16a)] - **test**: skip instantiateStreaming-bad-imports WPT (Michal Zasso) [#47292](https://togithub.com/nodejs/node/pull/47292)\n-   \\[[`45e7b10287`](https://togithub.com/nodejs/node/commit/45e7b10287)] - **test**: fix 'checks' validation test for checkPrime (Tobias Nieen) [#47139](https://togithub.com/nodejs/node/pull/47139)\n-   \\[[`5749dfae70`](https://togithub.com/nodejs/node/commit/5749dfae70)] - **test**: update URL web-platform-tests (Yagiz Nizipli) [#47135](https://togithub.com/nodejs/node/pull/47135)\n-   \\[[`49981b93d2`](https://togithub.com/nodejs/node/commit/49981b93d2)] - **test**: reduce flakiness of test-http-remove-header-stays-removed.js (Debadree Chatterjee) [#46855](https://togithub.com/nodejs/node/pull/46855)\n-   \\[[`6772aa652a`](https://togithub.com/nodejs/node/commit/6772aa652a)] - **test**: fix test-child-process-exec-cwd (Stefan Stojanovic) [#47235](https://togithub.com/nodejs/node/pull/47235)\n-   \\[[`41a69e772b`](https://togithub.com/nodejs/node/commit/41a69e772b)] - **test**: skip broken tests win arm64 (Stefan Stojanovic) [#47020](https://togithub.com/nodejs/node/pull/47020)\n-   \\[[`7bcfd18f2c`](https://togithub.com/nodejs/node/commit/7bcfd18f2c)] - **test**: mark test-http-max-sockets as flaky on win32 (Tobias Nieen) [#47134](https://togithub.com/nodejs/node/pull/47134)\n-   \\[[`b96808b3e2`](https://togithub.com/nodejs/node/commit/b96808b3e2)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#47222](https://togithub.com/nodejs/node/pull/47222)\n-   \\[[`65955f1e46`](https://togithub.com/nodejs/node/commit/65955f1e46)] - **test,crypto**: update WebCryptoAPI WPT (Filip Skokan) [#47131](https://togithub.com/nodejs/node/pull/47131)\n-   \\[[`bc6511a243`](https://togithub.com/nodejs/node/commit/bc6511a243)] - **test_runner**: color errors only when colors are available (Moshe Atlow) [#47394](https://togithub.com/nodejs/node/pull/47394)\n-   \\[[`463361e625`](https://togithub.com/nodejs/node/commit/463361e625)] - **test_runner**: hide failing tests title when all tests pass (Moshe Atlow) [#47370](https://togithub.com/nodejs/node/pull/47370)\n-   \\[[`eb837ce80d`](https://togithub.com/nodejs/node/commit/eb837ce80d)] - **test_runner**: stringify AssertError expected and actual (Moshe Atlow) [#47088](https://togithub.com/nodejs/node/pull/47088)\n-   \\[[`6b87f29000`](https://togithub.com/nodejs/node/commit/6b87f29000)] - **test_runner**: add code coverage support to spec reporter (Pulkit Gupta) [#46674](https://togithub.com/nodejs/node/pull/46674)\n-   \\[[`bd4697a2a3`](https://togithub.com/nodejs/node/commit/bd4697a2a3)] - **test_runner**: expose reporter for use in run api (Chemi Atlow) [#47238](https://togithub.com/nodejs/node/pull/47238)\n-   \\[[`3e7f8e8482`](https://togithub.com/nodejs/node/commit/3e7f8e8482)] - **test_runner**: report failing tests after summary (HinataKah0) [#47164](https://togithub.com/nodejs/node/pull/47164)\n-   \\[[`4530582767`](https://togithub.com/nodejs/node/commit/4530582767)] - **test_runner**: count nested tests (Moshe Atlow) [#47094](https://togithub.com/nodejs/node/pull/47094)\n-   \\[[`5a43586554`](https://togithub.com/nodejs/node/commit/5a43586554)] - **test_runner**: accept \\x1b as a escape symbol (Debadree Chatterjee) [#47050](https://togithub.com/nodejs/node/pull/47050)\n-   \\[[`a5ebc896f1`](https://togithub.com/nodejs/node/commit/a5ebc896f1)] - **test_runner**: support defining test reporter in NODE_OPTIONS (Steve Herzog) [#46688](https://togithub.com/nodejs/node/pull/46688)\n-   \\[[`a65fe5c29a`](https://togithub.com/nodejs/node/commit/a65fe5c29a)] - **tools**: fix update-openssl.yml compare version (Marco Ippolito) [#47384](https://togithub.com/nodejs/node/pull/47384)\n-   \\[[`760e13c58d`](https://togithub.com/nodejs/node/commit/760e13c58d)] - **tools**: ensure failed daily wpt run still generates a report (Filip Skokan) [#47376](https://togithub.com/nodejs/node/pull/47376)\n-   \\[[`9c975f79f0`](https://togithub.com/nodejs/node/commit/9c975f79f0)] - **tools**: use ref_name to get branch pushed on (Debadree Chatterjee) [#47358](https://togithub.com/nodejs/node/pull/47358)\n-   \\[[`b1d6a15028`](https://togithub.com/nodejs/node/commit/b1d6a15028)] - **tools**: add a at here tag for slack messages (Debadree Chatterjee) [#47358](https://togithub.com/nodejs/node/pull/47358)\n-   \\[[`c340de6d51`](https://togithub.com/nodejs/node/commit/c340de6d51)] - **tools**: disable Codecov commit statuses (Michal Zasso) [#47306](https://togithub.com/nodejs/node/pull/47306)\n-   \\[[`034082f0e5`](https://togithub.com/nodejs/node/commit/034082f0e5)] - **tools**: update eslint to 8.37.0 (Node.js GitHub Bot) [#47333](https://togithub.com/nodejs/node/pull/47333)\n-   \\[[`03b6650c81`](https://togithub.com/nodejs/node/commit/03b6650c81)] - **tools**: fix duration_ms to be milliseconds (Moshe Atlow) [#44490](https://togithub.com/nodejs/node/pull/44490)\n-   \\[[`30c667ec3a`](https://togithub.com/nodejs/node/commit/30c667ec3a)] - **tools**: automate brotli update (Marco Ippolito) [#47205](https://togithub.com/nodejs/node/pull/47205)\n-   \\[[`83791e5459`](https://togithub.com/nodejs/node/commit/83791e5459)] - **tools**: fix typo in nghttp2 path (Marco Ippolito) [#47330](https://togithub.com/nodejs/node/pull/47330)\n-   \\[[`53e8dad64a`](https://togithub.com/nodejs/node/commit/53e8dad64a)] - **tools**: add scorecard workflow (Mateo Nunez) [#47254](https://togithub.com/nodejs/node/pull/47254)\n-   \\[[`2499677d0b`](https://togithub.com/nodejs/node/commit/2499677d0b)] - **tools**: pin actions by hash for auto-start-ci.yml (Gabriela Gutierrez) [#46820](https://togithub.com/nodejs/node/pull/46820)\n-   \\[[`98f64ee724`](https://togithub.com/nodejs/node/commit/98f64ee724)] - **tools**: standardize base64 update (Marco Ippolito) [#47201](https://togithub.com/nodejs/node/pull/47201)\n-   \\[[`c1ef1fde8f`](https://togithub.com/nodejs/node/commit/c1ef1fde8f)] - **tools**: update codecov branch (Rich Trott) [#47285](https://togithub.com/nodejs/node/pull/47285)\n-   \\[[`9ecf2a4144`](https://togithub.com/nodejs/node/commit/9ecf2a4144)] - **tools**: update lint-md-dependencies to rollup@3.20.2 (Node.js GitHub Bot) [#47255](https://togithub.com/nodejs/node/pull/47255)\n-   \\[[`def7e3d908`](https://togithub.com/nodejs/node/commit/def7e3d908)] - **tools**: upgrade Windows digital signature to SHA256 (Tobias Nieen) [#47206](https://togithub.com/nodejs/node/pull/47206)\n-   \\[[`0b78ac53ad`](https://togithub.com/nodejs/node/commit/0b78ac53ad)] - **tools**: standardize update-llhttp.sh (Marco Ippolito) [#47198](https://togithub.com/nodejs/node/pull/47198)\n-   \\[[`deb80b1c46`](https://togithub.com/nodejs/node/commit/deb80b1c46)] - **tools**: add button to copy code example to clipboard (jakecastelli) [#46928](https://togithub.com/nodejs/node/pull/46928)\n-   \\[[`6dca79f1ce`](https://togithub.com/nodejs/node/commit/6dca79f1ce)] - **tools**: standardize update-nghttp2.sh (Marco Ippolito) [#47197](https://togithub.com/nodejs/node/pull/47197)\n-   \\[[`0c613c9347`](https://togithub.com/nodejs/node/commit/0c613c9347)] - **tools**: fix Slack notification action (Antoine du Hamel) [#47237](https://togithub.com/nodejs/node/pull/47237)\n-   \\[[`3f49da5113`](https://togithub.com/nodejs/node/commit/3f49da5113)] - **tools**: notify on Slack when invalid commit lands (Antoine du Hamel) [#47178](https://togithub.com/nodejs/node/pull/47178)\n-   \\[[`337123d657`](https://togithub.com/nodejs/node/commit/337123d657)] - **tools**: update daily wpt actions summary (Filip Skokan) [#47138](https://togithub.com/nodejs/node/pull/47138)\n-   \\[[`78ce8d3469`](https://togithub.com/nodejs/node/commit/78ce8d3469)] - **tools**: allow test tap output to include unicode characters (Moshe Atlow) [#47175](https://togithub.com/nodejs/node/pull/47175)\n-   \\[[`8850dacc88`](https://togithub.com/nodejs/node/commit/8850dacc88)] - **tools**: update lint-md-dependencies to rollup@3.19.1 (Node.js GitHub Bot) [#47045](https://togithub.com/nodejs/node/pull/47045)\n-   \\[[`d1ca5b6d47`](https://togithub.com/nodejs/node/commit/d1ca5b6d47)] - **tools**: align update-ada.sh with other scripts (Tony Gorez) [#47044](https://togithub.com/nodejs/node/pull/47044)\n-   \\[[`b58d52301e`](https://togithub.com/nodejs/node/commit/b58d52301e)] - **tools**: update eslint to 8.36.0 (Node.js GitHub Bot) [#47046](https://togithub.com/nodejs/node/pull/47046)\n-   \\[[`d78bef8a1f`](https://togithub.com/nodejs/node/commit/d78bef8a1f)] - **tools,meta**: update README and tools to reflect changes in TSC charter (Rich Trott) [#47126](https://togithub.com/nodejs/node/pull/47126)\n-   \\[[`d243115f41`](https://togithub.com/nodejs/node/commit/d243115f41)] - **url**: improve URLSearchParams creation performance (Yagiz Nizipli) [#47190](https://togithub.com/nodejs/node/pull/47190)\n-   \\[[`461ef04f87`](https://togithub.com/nodejs/node/commit/461ef04f87)] - **url**: add pending-deprecation to `url.parse()` (Yagiz Nizipli) [#47203](https://togithub.com/nodejs/node/pull/47203)\n-   \\[[`ef62e5a59e`](https://togithub.com/nodejs/node/commit/ef62e5a59e)] - **(SEMVER-MINOR)** **url**: implement URL.canParse (Khafra) [#47179](https://togithub.com/nodejs/node/pull/47179)\n-   \\[[`0b565e8f62`](https://togithub.com/nodejs/node/commit/0b565e8f62)] - **url**: allow extension of user provided URL objects (Antoine du Hamel) [#46989](https://togithub.com/nodejs/node/pull/46989)\n-   \\[[`cbb362736b`](https://togithub.com/nodejs/node/commit/cbb362736b)] - **util**: fix inspecting error with a throwing getter for `cause` (Antoine du Hamel) [#47163](https://togithub.com/nodejs/node/pull/47163)\n-   \\[[`9537672511`](https://togithub.com/nodejs/node/commit/9537672511)] - **vm**: properly handle defining props on any value (Nicolas DUBIEN) [#46615](https://togithub.com/nodejs/node/pull/46615)\n-   \\[[`75669e98bf`](https://togithub.com/nodejs/node/commit/75669e98bf)] - **watch**: fix watch path with equals (Moshe Atlow) [#47369](https://togithub.com/nodejs/node/pull/47369)\n\n### [`v19.8.1`](https://togithub.com/nodejs/node/releases/tag/v19.8.1): 2023-03-15, Version 19.8.1 (Current), @targos\n\n[Compare Source](https://togithub.com/nodejs/node/compare/v19.7.0...v19.8.1)\n\n##### Notable Changes\n\nThis release contains a single revert of a change that was introduced in v19.8.0\nand introduced application crashes.\n\nFixes: [#47096](https://togithub.com/nodejs/node/issues/47096)\n\n##### Commits\n\n-   \\[[`f7c8aa4cf1`](https://togithub.com/nodejs/node/commit/f7c8aa4cf1)] - ***Revert*** \"**vm**: fix leak in vm.compileFunction when importModuleDynamically is used\" (Michal Zasso) [#47101](https://togithub.com/nodejs/node/pull/47101)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Enable notifications for TLC threads for all teams", "number": 5761, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5761", "body": "I'll be creating a follow up PR to remove the team setting since its no longer needed."}
{"title": "Clean up PullRequestInfo", "number": 5762, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5762", "body": "We no longer need to return the individual comments, as they've been rolled up into a thread."}
{"title": "Reduce indexing concurrency from 5 to 1", "number": 5763, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5763"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5763#pullrequestreview-1394845019", "body": ""}
{"title": "add one more search service instance", "number": 5764, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5764", "body": "This is temp until I roll out auto-scalers."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5764#pullrequestreview-1394846697", "body": ""}
{"title": "Clean up embedding endpoitns", "number": 5765, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5765"}
{"title": "Better copying of JS content in JetBrains plugin", "number": 5766, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5766", "body": "Copy all JS bundle content in the jar, instead of doing it manually\nRebuild bundle content when webpack config changes\nFixes missing Unblocked image in search tab"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5766#pullrequestreview-1394865777", "body": ""}
{"title": "Catch error instead of throwing", "number": 5767, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5767", "body": "Issue where creating an insight on \"strange\" file can cause empty tool windows in IntelliJ\nGit operations could occasionally fail. These types of failures should not prevent the user from creating insights."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5767#pullrequestreview-1394885601", "body": ""}
{"comment": {"body": "Creating an insight in VSCode for a similar file will succeed as it is catching errors like this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5767#discussion_r1173179056"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5767#pullrequestreview-1394886392", "body": "nice! good idea"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5767#pullrequestreview-1394893696", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5767#pullrequestreview-1394894286", "body": ""}
{"comment": {"body": "I don't remember the surrounding context, but this may be an area where the streams/code should be shared between both IDEs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5767#discussion_r1173185457"}}
{"title": "Implement deleteRepo and hide uninstalled repos", "number": 5768, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5768"}
{"title": "Show default settings for teams with no settings", "number": 5769, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5769"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5769#pullrequestreview-1394906545", "body": ""}
{"comment": {"body": "Lazy and inefficient but it's a one-off migration", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5769#discussion_r1173194120"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5769#pullrequestreview-1394907306", "body": ""}
{"title": "Style anchor sourcemark code block", "number": 577, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577", "body": "Funnel through sourcemark data to the createThread request \nAdd MockSourceMark code and update story\nThere's additional UI work to move the title into the first message body but I'll do that in another PR\nThere are a couple of TODOs that will be addressed once the anchorSourceMarkId property is supported on the backend\nNOTE: Not added to the web dashboard yet from lack of designs (chatted with Ben about this). We may need to rethink which parts of the thread UI we want to reuse between clients when need to implement this"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#pullrequestreview-909372267", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#pullrequestreview-909374351", "body": ""}
{"comment": {"body": "If you change this to `fb06ca32-cdf0-4bcc-9e08-c8c675768235`, we will create source points on the Unblocked repo in local and dev environments.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#discussion_r826363184"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#pullrequestreview-909375535", "body": ""}
{"comment": {"body": "Oh never mind, this is only used in stories, so that doesn't make sense.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#discussion_r826364055"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#pullrequestreview-909398898", "body": ""}
{"comment": {"body": "I think we're maybe at a place where we should wrap all of the `loaded` properties (messages, thread, teamMembersMap, anchorSourceMark) in a single optional object, that is only set when we're in the `loaded` state.  Using mock source marks here feels like the wrong approach?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#discussion_r826380983"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#pullrequestreview-909399294", "body": ""}
{"comment": {"body": "Ugh never mind, I misread which file I was in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#discussion_r826381250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#pullrequestreview-909400351", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#pullrequestreview-909401582", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#pullrequestreview-909401923", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/577#pullrequestreview-909405113", "body": ""}
{"title": "In DEV treat all users as non-internal users", "number": 5770, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5770", "body": "Motivation is to ensure that the DEV experience is as similar to product as possible. Engagement metrics are not recorded for internal users, which prevent us from testing functionality in DEV that depends on users being engaged.\nPart of this change cleanly separates admin web users from internal users. they are no longer the same thing."}
{"title": "Organize JetBrains into folders", "number": 5771, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5771"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5771#pullrequestreview-1394933606", "body": ""}
{"title": "Enable Hugging Face Embeddings", "number": 5772, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5772", "body": "Enable hugging face instructor embeddings\nUpdate"}
{"title": "Add TeamMember.description + TeamMember.userDefinedDescription", "number": 5773, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773"}
{"comment": {"body": "@pwerry : this is the failure I was referencing. pretty sure I have seen this before when I have changed private.yml?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#issuecomment-1517299993"}}
{"comment": {"body": "@cancelself just cd into `macos` and run make setup, it should regenerate the swift files. If CI still fails after than, then you might be on an older version of the generator.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#issuecomment-1518100784"}}
{"comment": {"body": "> Where are the descriptions intended to be shown in the client UI?\r\n> \r\n> Not clear to me if this should be be bundled with the TeamMember responses, or if descriptions should be exposed in a separate API. If we render descriptions ubiquitously in most of the clients then we should do the former; but if the descriptions are only shown in one page in one client then we should do the latter.\r\n> \r\n> cc @matthewjamesadam @rasharab thoughts?\r\n\r\nIn general I agree -- from @cancelself's comment it sounds like the plan is to show this fairly broadly, so I think it probably does belong on the TeamMember.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#issuecomment-1518201267"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#pullrequestreview-1396121463", "body": "LGTM. Some folks might have thoughts on the TeamMember.yml changes, maybe back that out and put it in a separate PR to get feedback so that the rest go in? And then update the TeamMemberPage.kt admin console page to show these fields so that you can iterate and see the results."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#pullrequestreview-1396159392", "body": "Where are the descriptions intended to be shown in the client UI?\nNot clear to me if this should be be bundled with the TeamMember responses, or if descriptions should be exposed in a separate API. If we render descriptions ubiquitously in most of the clients then we should do the former; but if the descriptions are only shown in one page in one client then we should do the latter.\ncc @matthewjamesadam @rasharab thoughts?"}
{"comment": {"body": "Since these are nullable the upsert on the GitHub installation path will obliterate the stored description, which is probably not what you want. Only overwrite if specified:\r\n```suggestion\r\n            description?.also { this.description = description }\r\n            userDefinedDescription?.also { this.userDefinedDescription = userDefinedDescription }\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#discussion_r1174013124"}}
{"comment": {"body": "Add a test that upserting a TeamMember with a `null` description does not overwrite a previously written description.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#discussion_r1174014779"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#pullrequestreview-1396184842", "body": ""}
{"comment": {"body": "Kotlin is magical.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#discussion_r1174029803"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5773#pullrequestreview-1396316000", "body": ""}
{"title": "Update Max Heap Size", "number": 5774, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5774", "body": "It looks like the flag changed when going from java 11 - 17"}
{"comment": {"body": "lol maybe 4g is the magic number \ud83d\ude06 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5774#issuecomment-1518067815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5774#pullrequestreview-1395382955", "body": ""}
{"title": "chore(deps): update dependency html-webpack-plugin to v5.5.1", "number": 5775, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5775", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| html-webpack-plugin | 5.5.0 -> 5.5.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\njantimon/html-webpack-plugin\n\n### [`v5.5.1`]()\n\n[Compare Source]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency com.aallam.openai:openai-client to v3.2.1", "number": 5776, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5776", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.aallam.openai:openai-client | 3.2.0 -> 3.2.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nAallam/openai-kotlin\n\n### [`v3.2.1`]()\n\n[Compare Source]()\n\n> Published 9 Apr 2023\n\n##### Added\n\n-   Proguard / R8 rules for jvm ([#149]())\n-   `OpenAI` implements `AutoCloseable` ([#151]())\n\n##### Dependencies\n\n-   Kotlin to `1.8.20` ([#146]())\n-   Kotlin serialization to `1.5.0` ([#146]())\n-   Ktor to `2.2.4` ([#146]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency io.github.reactivecircus.cache4k:cache4k-jvm to v0.10.0", "number": 5777, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5777", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.github.reactivecircus.cache4k:cache4k-jvm | 0.9.0 -> 0.10.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nreactivecircus/cache4k\n\n### [`v0.10.0`](https://togithub.com/reactivecircus/cache4k/blob/HEAD/CHANGELOG.md#0100)\n\n[Compare Source](https://togithub.com/reactivecircus/cache4k/compare/0.9.0...0.10.0)\n\n##### Added\n\nAdded new targets:\n\n-   `watchosArm64`\n-   `watchosSimulatorArm64`\n-   `watchosX64`\n\n`FakeTimeSource` is now public - [#30](https://togithub.com/ReactiveCircus/cache4k/pull/30)\n\n##### Changed\n\n-   Kotlin 1.8.20.\n-   AtomicFU 0.20.2.\n-   Stately 2.0.0-rc1.\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Update pull request indexing logic", "number": 5778, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5778", "body": "Since pull request issue comments and review comments are in threads, those will be indexed as a thread. Thus we don't need to include them when indexing the pull request itself."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5778#pullrequestreview-1394985148", "body": ""}
{"comment": {"body": "We should probably be placing this logic in the index content service such that the functionality is shared across the various indexing/embedding services.\r\n\r\nThat was the original intention behind creating that so that code duplication is reduced in these cases.  ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5778#discussion_r1173254508"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5778#pullrequestreview-1394985580", "body": ""}
{"comment": {"body": "If that's not feasible for you (time-wise). I'm going to be moving this logic in here out in a follow-up pr.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5778#discussion_r1173254869"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5778#pullrequestreview-1394986069", "body": "I'll be making requisite changes once this is submitted."}
{"title": "Increase search sevice", "number": 5779, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5779"}
{"title": "chore(deps): update dependency npm-check-updates to v16.10.9", "number": 5780, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5780", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| npm-check-updates | 16.10.8 -> 16.10.9 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nraineorshine/npm-check-updates\n\n### [`v16.10.9`]()\n\n[Compare Source]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "GEneralize insight indexing content", "number": 5781, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5781"}
{"title": "Revert \"Reduce indexing concurrency from 5 to 1 (#5763)\"", "number": 5782, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5782", "body": "This reverts commit a979d443770df4e18dab7e8a31c53cb016043251."}
{"title": "Implement pendingTeams endpoint", "number": 5783, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783", "body": "Logic to filter pending teams from the JWT and the getTeams response was reverted. Current plan is to have the Hub make a call to pendingTeams to determine which teams should be allowed through the onboarding Gate. Trivial to add filtering back in for future clients."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#pullrequestreview-1395046755", "body": ""}
{"comment": {"body": "Wow did we ever mess these tests up....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#discussion_r1173298252"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#pullrequestreview-1395048947", "body": ""}
{"comment": {"body": "This is a dangerous change", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#discussion_r1173299816"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#pullrequestreview-1395049104", "body": ""}
{"comment": {"body": "So is this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#discussion_r1173299932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#pullrequestreview-1395049151", "body": ""}
{"comment": {"body": "And this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#discussion_r1173299968"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#pullrequestreview-1396084909", "body": ""}
{"comment": {"body": "Naming \ud83e\udddf\u200d\u2642\ufe0f", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#discussion_r1173973133"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#pullrequestreview-1396185193", "body": ""}
{"comment": {"body": "I knew I'd get nailed for this - sorry :( ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5783#discussion_r1174030067"}}
{"title": "Add missing feature flag", "number": 5784, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5784"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5784#pullrequestreview-1395062755", "body": ""}
{"title": "Write-back for updating reviews", "number": 5785, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5785", "body": "TODO tests"}
{"title": "Add activemq queue prefetch limit", "number": 5786, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5786", "body": "Default queue prefect limit is 1000. That's per consumer.\nWe need to reduce this..."}
{"title": "chore(deps): update postcss packages", "number": 5787, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5787", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| postcss (source) | 8.4.21 -> 8.4.23 |  |  |  |  |\n| postcss-loader | 7.0.2 -> 7.2.4 |  |  |  |  |\n| postcss-preset-env (source) | 8.0.0 -> 8.3.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\npostcss/postcss\n\n### [`v8.4.23`]()\n\n[Compare Source]()\n\n-   Fixed warnings in TypeDoc.\n\n### [`v8.4.22`]()\n\n[Compare Source]()\n\n-   Fixed TypeScript support with `node16` (by Remco Haszing).\n\n\n\n\nwebpack-contrib/postcss-loader\n\n### [`v7.2.4`]()\n\n[Compare Source]()\n\n### [`v7.2.3`]()\n\n[Compare Source]()\n\n### [`v7.2.2`]()\n\n[Compare Source]()\n\n### [`v7.2.1`]()\n\n[Compare Source]()\n\n### [`v7.2.0`]()\n\n[Compare Source]()\n\n##### Features\n\n-   add support for TypeScript based configs ([#632]()) ([c6b5def]())\n\n### [`v7.1.0`]()\n\n[Compare Source]()\n\n##### Features\n\n-   **deps:** update `cosmiconfig` ([#628]()) ([8114be4]())\n\n##### [7.0.2]() (2022-11-29)\n\n##### Bug Fixes\n\n-   support ESM version of `postcss.config.js` and `postcss.config.mjs` ([#614]()) ([955085f]())\n\n##### [7.0.1]() (2022-07-11)\n\n##### Bug Fixes\n\n-   unexpected failing on CSS syntax error ([#593]()) ([888d72e]())\n\n\n\n\ncsstools/postcss-plugins\n\n### [`v8.3.2`]()\n\n[Compare Source]()\n\n-   Updated `@csstools/postcss-gradients-interpolation-method` to `3.0.4` (patch)\n\n### [`v8.3.1`]()\n\n[Compare Source]()\n\n-   Updated `@csstools/postcss-logical-viewport-units` to `1.0.3` (patch)\n-   Updated `@csstools/postcss-media-minmax` to `1.0.2` (patch)\n-   Updated `@csstools/postcss-media-queries-aspect-ratio-number-values` to `1.0.2` (patch)\n-   Updated `@csstools/postcss-stepped-value-functions` to `2.1.1` (patch)\n-   Updated `@csstools/postcss-trigonometric-functions` to `2.1.1` (patch)\n-   Updated `postcss-custom-media` to `9.1.3` (patch)\n-   Updated `postcss-custom-properties` to `13.1.5` (patch)\n-   Updated `postcss-custom-selectors` to `7.1.3` (patch)\n-   Updated `@csstools/postcss-color-function` to `2.2.1` (patch)\n-   Updated `@csstools/postcss-color-mix-function` to `1.0.1` (patch)\n-   Updated `@csstools/postcss-gradients-interpolation-method` to `3.0.3` (patch)\n-   Updated `@csstools/postcss-hwb-function` to `2.2.1` (patch)\n-   Updated `@csstools/postcss-oklab-function` to `2.2.1` (patch)\n-   Updated `postcss-lab-function` to `5.2.1` (patch)\n\n### [`v8.3.0`]()\n\n[Compare Source]()\n\n-   Replace `postcss-media-minmax` with `@csstools/postcss-media-minmax` [Check the plugin README]() for usage details. *We hope to revert this in the future when maintenance is resumed upstream.*\n-   -   Updated `cssdb` to `7.5.3` (patch)\n\n### [`v8.2.0`]()\n\n[Compare Source]()\n\n-   Added `@csstools/postcss-gradients-interpolation-method` [Check the plugin README]() for usage details.\n-   Updated `cssdb` to `7.5.2` (patch)\n\n### [`v8.1.0`]()\n\n[Compare Source]()\n\n-   Added `@csstools/postcss-color-mix-function` [Check the plugin README]() for usage details.\n-   Improve `debug` message format\n-   Updated `@csstools/postcss-cascade-layers` to `3.0.1` (patch)\n-   Updated `@csstools/postcss-color-function` to `2.2.0` (minor)\n-   Updated `@csstools/postcss-font-format-keywords` to `2.0.2` (patch)\n-   Updated `@csstools/postcss-hwb-function` to `2.2.0` (minor)\n-   Updated `@csstools/postcss-ic-unit` to `2.0.2` (patch)\n-   Updated `@csstools/postcss-is-pseudo-class` to `3.1.1` (minor)\n-   Updated `@csstools/postcss-logical-float-and-clear` to `1.0.1` (patch)\n-   Updated `@csstools/postcss-logical-resize` to `1.0.1` (patch)\n-   Updated `@csstools/postcss-logical-viewport-units` to `1.0.2` (patch)\n-   Updated `@csstools/postcss-media-queries-aspect-ratio-number-values` to `1.0.1` (patch)\n-   Updated `@csstools/postcss-nested-calc` to `2.0.2` (patch)\n-   Updated `@csstools/postcss-normalize-display-values` to `2.0.1` (patch)\n-   Updated `@csstools/postcss-oklab-function` to `2.1.0` (minor)\n-   Updated `@csstools/postcss-progressive-custom-properties` to `2.1.0` (minor)\n-   Updated `@csstools/postcss-scope-pseudo-class` to `2.0.2` (patch)\n-   Updated `@csstools/postcss-stepped-value-functions` to `2.1.0` (minor)\n-   Updated `@csstools/postcss-text-decoration-shorthand` to `2.2.1` (minor)\n-   Updated `@csstools/postcss-trigonometric-functions` to `2.1.0` (minor)\n-   Updated `@csstools/postcss-unset-value` to `2.0.1` (patch)\n-   Updated `autoprefixer` to `10.4.14` (patch)\n-   Updated `browserslist` to `4.21.5` (patch)\n-   Updated `css-blank-pseudo` to `5.0.2` (patch)\n-   Updated `css-has-pseudo` to `5.0.2` (patch)\n-   Updated `css-prefers-color-scheme` to `8.0.2` (patch)\n-   Updated `cssdb` to `7.5.1` (minor)\n-   Updated `postcss-attribute-case-insensitive` to `6.0.2` (patch)\n-   Updated `postcss-color-functional-notation` to `5.0.2` (patch)\n-   Updated `postcss-color-hex-alpha` to `9.0.2` (patch)\n-   Updated `postcss-color-rebeccapurple` to `8.0.2` (patch)\n-   Updated `postcss-custom-media` to `9.1.2` (patch)\n-   Updated `postcss-custom-properties` to `13.1.4` (patch)\n-   Updated `postcss-custom-selectors` to `7.1.2` (patch)\n-   Updated `postcss-dir-pseudo-class` to `7.0.2` (patch)\n-   Updated `postcss-double-position-gradients` to `4.0.2` (patch)\n-   Updated `postcss-focus-visible` to `8.0.2` (patch)\n-   Updated `postcss-focus-within` to `7.0.2` (patch)\n-   Updated `postcss-gap-properties` to `4.0.1` (patch)\n-   Updated `postcss-image-set-function` to `5.0.2` (patch)\n-   Updated `postcss-lab-function` to `5.2.0` (minor)\n-   Updated `postcss-logical` to `6.1.0` (minor)\n-   Updated `postcss-nesting` to `11.2.1` (minor)\n-   Updated `postcss-opacity-percentage` to `2.0.0` (major)\n-   Updated `postcss-overflow-shorthand` to `4.0.1` (patch)\n-   Updated `postcss-page-break` to `3.0.4` (patch)\n-   Updated `postcss-place` to `8.0.1` (patch)\n-   Updated `postcss-pseudo-class-any-link` to `8.0.2` (patch)\n-   Updated `postcss-selector-not` to `7.0.1` (patch)\n\n### [`v8.0.1`]()\n\n[Compare Source]()\n\n-   Improve `types` declaration in `package.json`\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Immortal: This PR will be recreated if closed unmerged. Get config help if that's undesired.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Fix admin console toggle", "number": 5788, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5788"}
{"title": "Remove deprecated scm queues", "number": 5789, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5789"}
{"title": "Adds config DI to Jwt", "number": 579, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/579", "body": "Precursor to module split"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/579#pullrequestreview-909369900", "body": ""}
{"comment": {"body": "Not amazing - we're going to have to figure out how to load per-module config eventually. Not for this PR though", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/579#discussion_r826359974"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/579#pullrequestreview-909370397", "body": ""}
{"comment": {"body": "This is where the real change takes place. Everything else is just dependency breakage", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/579#discussion_r826360326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/579#pullrequestreview-909406092", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/579#pullrequestreview-923722796", "body": ""}
{"comment": {"body": "I think `Jwt` should be a library of util functions that has zero config dependencies; then move the non-generic functionality into `GitHubAuth` and `ApplicationAuth`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/579#discussion_r836829649"}}
{"title": "Set userAccessAllowed to true by default", "number": 5790, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5790", "body": "Give teams access to the platform by default (until we don't)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5790#pullrequestreview-1396262653", "body": ""}
{"title": "chore(deps): update dependency gradle to v8.1.1", "number": 5791, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5791", "body": "This PR contains the following updates:\n| Package | Update | Change |\n|---|---|---|\n| gradle (source) | patch | 8.1 -> 8.1.1 |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ngradle/gradle\n\n### [`v8.1.1`](): 8.1.1\n\nThis is a patch release for Gradle 8.1. We recommend using 8.1.1 instead of 8.1.\n\nIt fixes the following issues:\n\n-   [#24748]() MethodTooLargeException when instrumenting a class with thousand of lambdas for configuration cache\n-   [#24754]() Kotlin DSL precompiled script plugins built with Gradle 8.1 cannot be used with other versions of Gradle\n-   [#24788]() Gradle 8.1 configure freeCompilerArgs for Kotlin in buildSrc breaks build with unhelpful errors\n\n[Read the Release Notes]()\n\n#### Upgrade Instructions\n\nSwitch your build to use Gradle 8.1.1 by updating your wrapper:\n\n    ./gradlew wrapper --gradle-version=8.1.1\n\nSee the [Gradle 8.x upgrade guide](\\_8.html#changes\\_8.1) to learn about deprecations, breaking changes and other considerations when upgrading to Gradle 8.1.1.\n\n#### Reporting Problems\n\nIf you find a problem with this release, please file a bug on [GitHub Issues]() adhering to our issue guidelines.\nIf you're not sure you're encountering a bug, please use the [forum]().\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency constructs to v10.2.2", "number": 5792, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5792", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| constructs | 10.2.1 -> 10.2.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/constructs\n\n### [`v10.2.2`]()\n\n[Compare Source]()\n\n##### [10.2.2]() (2023-04-21)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update logbackversion to v1.4.7", "number": 5793, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5793", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| ch.qos.logback:logback-core (source) | 1.4.5 -> 1.4.7 |  |  |  |  |\n| ch.qos.logback:logback-classic (source) | 1.4.5 -> 1.4.7 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency com.github.johnrengelman.shadow to v8.1.1", "number": 5794, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5794", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.johnrengelman.shadow | 8.0.0 -> 8.1.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency com.github.node-gradle.node to v3.6.0", "number": 5795, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5795", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.node-gradle.node | 3.5.1 -> 3.6.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Fix extension", "number": 5796, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5796"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5796#pullrequestreview-1396210309", "body": ""}
{"title": "chore(deps): update dependency webpack-cli to v5.0.2", "number": 5797, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5797", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| webpack-cli (source) | 5.0.1 -> 5.0.2 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nwebpack/webpack-cli\n\n### [`v5.0.2`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   error message for missing default export in configuration ([#3685]()) ([e0a4a09]())\n-   **perf:** reduced startup time ([3b79059]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "JetBrains New UI icons", "number": 5798, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5798"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5798#pullrequestreview-1396227585", "body": ""}
{"title": "Revert \"chore(deps): update dependency gradle to v8.1.1 (#5791)\"", "number": 5799, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5799", "body": "This reverts commit 713c4d4742a79929d3fd28781db634def422e2b3."}
{"title": "Add team models", "number": 58, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/58"}
{"title": "Fix teamID in RuntimeFixtures", "number": 580, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/580"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/580#pullrequestreview-909455338", "body": ""}
{"title": "Pull in pending teams", "number": 5800, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1398397986", "body": ""}
{"comment": {"body": "These could probably just be the same, but kept them separate because they're technically two different things", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1175524216"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1398403910", "body": ""}
{"comment": {"body": "Upstream nil checks will prevent a nil equality check at this stage", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1175528475"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1398407865", "body": ""}
{"comment": {"body": "We're probably at the point where this can be removed. Will do in another PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1175531178"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400836043", "body": ""}
{"comment": {"body": "So the only difference between getLatestTeam & refresh is the return value?\r\nIn this case, we don't use the return value so refresh??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177116823"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400836302", "body": ""}
{"comment": {"body": "Do we need this? Or just copied over", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177116987"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400837219", "body": ""}
{"comment": {"body": "How is this different from `pendingTeams()`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177117615"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400839038", "body": ""}
{"comment": {"body": "If that's thee case, why are these typed as optionals?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177118886"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400852052", "body": ""}
{"comment": {"body": "Nit: Given we have stores for everything else, I think moving this to its own store would be cleaner. Abstracts the APIs out of the views.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177125934"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400852481", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400884357", "body": ""}
{"comment": {"body": "Yeah this is copy paste nonsense. Will remove", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177150175"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400885570", "body": ""}
{"comment": {"body": "`pendingTeams()` will short-circuit on a cached value. This function will always fetch from the network", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177151037"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400885953", "body": ""}
{"comment": {"body": "They're not?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177151374"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400886384", "body": ""}
{"comment": {"body": "Probably just ignore this comment - I just meant that we're filtering at the combine callback level (see above)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177151707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400887250", "body": ""}
{"comment": {"body": "Mostly agree, except that we never \"fetch\" this value so there's no reason to create a Store for it. I have a feeling we'll be doing that soon though...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177152377"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#pullrequestreview-1400889262", "body": ""}
{"comment": {"body": "Ha! That's a good point. I think I might have created a few redundant functions here!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5800#discussion_r1177153992"}}
{"title": "fix(deps): update protobufversion to v3.22.3", "number": 5801, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5801", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.google.protobuf:protoc (source) | 3.22.2 -> 3.22.3 |  |  |  |  |\n| com.google.protobuf:protobuf-kotlin (source) | 3.22.2 -> 3.22.3 |  |  |  |  |\n| com.google.protobuf:protobuf-java-util (source) | 3.22.2 -> 3.22.3 |  |  |  |  |\n| com.google.protobuf:protobuf-java (source) | 3.22.2 -> 3.22.3 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nprotocolbuffers/protobuf\n\n### [`v3.22.3`]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about these updates again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Fix embedded postgres stale cleanup", "number": 5802, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5802"}
{"title": "Ensure that we record the invite sent before sending the invite", "number": 5803, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5803", "body": "It is impossible to atomically send an email and also record that the email was sent.\nWe can choose to send the email first, or record the invite first; both have drawbacks.\n\nIf we record the invite first then there is a risk that the email will never be sent.\nIf we send the email first then there is a risk that the invite will not be recorded\n  leading to duplicate emails being sent.\n\nThis change inverts the order so that we record the invite first. Motivation is because\nKay sent a bad team member ID which failed TeamInviteeModel referencial integrity,\nwhich resulted in a 500, which was retried automatically by the client, and it sent 7\nemails."}
{"title": "JetBrains: only poll when window has focus", "number": 5804, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5804", "body": "This funnels the window focus events through to the agent.\nWhile I was in this code I also changed the ChannelPoller to get its focus state from FocusStream, instead of having its own focus setting API."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5804#pullrequestreview-1398476284", "body": ""}
{"title": "UpdateRepos GRPC from request to stream", "number": 5805, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805", "body": "We relied on a standard gRPC request from IntelliJ to the Agent to transmit a list of repositories.\nThe projectService typically issued two requests:\n\nUpon loading, it sent an empty repository list.\nFollowing an update, it sent a list of repositories.\n\nUnfortunately, the sequence of these requests is not guaranteed, leading to instances where the Agent first received the second request and then the first, resulting in an empty repository state for the Agent.\nPR updates the communication method to utilize a stream that guarantees the correct order of requests."}
{"comment": {"body": "Thread about order of grpc requests. https://github.com/grpc/grpc/issues/10853#issuecomment-297478862\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#issuecomment-1518427667"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#pullrequestreview-1396487713", "body": ""}
{"comment": {"body": "Does the response have to be a stream?  We aren't actually returning anything...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#discussion_r1174228031"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#pullrequestreview-1396488328", "body": ""}
{"comment": {"body": "I can't remember, does this need extraBufferCpaacity so we don't lose values?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#discussion_r1174228453"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#pullrequestreview-1396488347", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#pullrequestreview-1396489340", "body": ""}
{"comment": {"body": "In this case no... The only time we are concerned about buffers is if \"every\" event matters.\r\nHere, we only really care about the latest results.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#discussion_r1174229009"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#pullrequestreview-1396489852", "body": ""}
{"comment": {"body": "Is that how this works?  I thought if it can't store the value it drops it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5805#discussion_r1174229350"}}
{"title": "Gradle cache propblems", "number": 5806, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5806"}
{"title": "Make sure step doesn't cause build to fail", "number": 5807, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5807"}
{"title": "Expert Summary Service", "number": 5808, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808", "body": "Introduces ExpertSummaryService which generates TeamMember.description with an associated eventqueue + adminweb action."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#pullrequestreview-1400435829", "body": ""}
{"comment": {"body": "Accidental check in?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#discussion_r1176850250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#pullrequestreview-1400441573", "body": ""}
{"comment": {"body": "Actually, I'd just update the relevant fields (description) and not set the other fields just to be safe.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#discussion_r1176854258"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#pullrequestreview-1400442718", "body": ""}
{"comment": {"body": "Depending on how long `summarizeExpertCompletionService.summarize` takes the fields may have changed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#discussion_r1176855087"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#pullrequestreview-1400443883", "body": ""}
{"comment": {"body": "Also I'd call `summarizeExpertCompletionService.summarize` outside of a transaction since that might take a while to return", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#discussion_r1176855869"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#pullrequestreview-1400445540", "body": ""}
{"comment": {"body": "Maybe just break out the various database calls into separate transactions, I don't think it's important that these be all in the same transaction (especially if you only update the TeamMember.description field)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#discussion_r1176856937"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#pullrequestreview-1400462485", "body": ""}
{"comment": {"body": "Yes! thank you. I was trying to figure out why I wasn't getting code completion in IDE and didn't realize that this got written. Fix!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#discussion_r1176867709"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#pullrequestreview-1400482974", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/ad8fed8a-9d0a-483e-9cb6-e26c61f5fcf6)\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5808#discussion_r1176878780"}}
{"title": "This action is consuming a lot of cache space", "number": 5809, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5809"}
{"title": "Implement backend support for anchorSourceMark", "number": 581, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581", "body": "An anchor SourceMark is the one that appears at the top of a thread. Versus a regular SourceMark which can appear in a message body, an anchor SourceMark can never be removed/deleted from a thread and there can ever only be one per thread. \nMain changes in this PR:\n\nadding an isAnchor property to SourceMarkModel\nensuring that the isAnchor field is updated if the thread creation request has a message with an anchorSourceMarkId\nreturning the correct value for anchorSourceMarkId when returning Messages\n\nBecause of what I mentioned above, isAnchor is only ever set for one SourceMark per thread and only at thread creation time."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581#pullrequestreview-909711946", "body": ""}
{"comment": {"body": "Add this filter here so that we never delete an anchor source mark", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581#discussion_r826619504"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581#pullrequestreview-909719334", "body": ""}
{"comment": {"body": "This is not the most efficient way of getting anchor SourceMarks for messages but is probably fine for now.\r\n\r\nIf this becomes an issue, we can update the query in `messageService.getMessages(threadIds)` to join on `SourceMarkModel` and have it return in one query.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581#discussion_r826624876"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581#pullrequestreview-910538444", "body": ""}
{"comment": {"body": "I wonder if we should do more here, like create a PG rule to throw when attempting to delete a row where this value is set to true?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581#discussion_r827223877"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581#pullrequestreview-910613914", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581#pullrequestreview-910635274", "body": ""}
{"comment": {"body": "I think we can consider that if it becomes a problem. I don't think deleting SourceMarks will ever happen though, I think the likelier case is that someone accidentally tries to create a second anchor SourceMark for a thread or attempts to set this field to false but those are easy things to solve.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/581#discussion_r827278838"}}
{"title": "comment out unused junit test config", "number": 5810, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5810"}
{"title": "Update gradle build cache node release version", "number": 5811, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5811"}
{"title": "MoreGradleActionStepFixes", "number": 5812, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5812", "body": "Revert \"Revert \"chore(deps): update dependency gradle to v8.1.1 (#5791)\" (#5799)\"\nAttempt to move to a version of build action with better caching?"}
{"title": "Remove setup java cache", "number": 5813, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5813"}
{"title": "chore(deps): update dependency org.openapi.generator to v6.5.0", "number": 5814, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5814", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.openapi.generator | 6.4.0 -> 6.5.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Attempt to fix gradle dying", "number": 5815, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5815"}
{"title": "fix(deps): update dependency constructs to v10.2.4", "number": 5816, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5816", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| constructs | 10.2.2 -> 10.2.4 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\naws/constructs\n\n### [`v10.2.4`]()\n\n[Compare Source]()\n\n##### [10.2.4]() (2023-04-23)\n\n### [`v10.2.3`]()\n\n[Compare Source]()\n\n##### [10.2.3]() (2023-04-22)\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Admin: View and clear team member email invites", "number": 5817, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5817"}
{"title": "Refactor admin action routes", "number": 5818, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5818"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5818#pullrequestreview-1396956175", "body": "Can this guy get a medal for loveliness??"}
{"title": "chore(deps): update babel monorepo", "number": 5819, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5819", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @babel/core (source) | 7.16.12 -> 7.21.4 |  |  |  |  |\n| @babel/plugin-transform-react-jsx (source) | 7.16.7 -> 7.21.0 |  |  |  |  |\n| @babel/preset-env (source) | 7.16.11 -> 7.21.4 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nbabel/babel\n\n### [`v7.21.4`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7214-2023-03-31)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.21.3...v7.21.4)\n\n##### :bug: Bug Fix\n\n-   `babel-core`, `babel-helper-module-imports`, `babel-preset-typescript`\n    -   [#15478](https://togithub.com/babel/babel/pull/15478) Fix support for `import/export` in `.cts` files ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-generator`\n    -   [#15496](https://togithub.com/babel/babel/pull/15496) Fix compact printing of non-null assertion operators ([@rtsao](https://togithub.com/rtsao))\n\n##### :nail_care: Polish\n\n-   `babel-helper-create-class-features-plugin`, `babel-plugin-proposal-class-properties`, `babel-plugin-transform-typescript`, `babel-traverse`\n    -   [#15427](https://togithub.com/babel/babel/pull/15427) Fix moving comments of removed nodes ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :house: Internal\n\n-   Other\n    -   [#15519](https://togithub.com/babel/babel/pull/15519) Update Prettier integration test ([@fisker](https://togithub.com/fisker))\n-   `babel-parser`\n    -   [#15510](https://togithub.com/babel/babel/pull/15510) refactor: introduce `lookaheadInLineCharCode` ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-code-frame`, `babel-highlight`\n    -   [#15499](https://togithub.com/babel/babel/pull/15499) Polish babel-code-frame highlight test ([@JLHwung](https://togithub.com/JLHwung))\n\n### [`v7.21.3`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7213-2023-03-14)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.21.0...v7.21.3)\n\n##### :eyeglasses: Spec Compliance\n\n-   `babel-parser`\n    -   [#15479](https://togithub.com/babel/babel/pull/15479) disallow mixins/implements in flow interface ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :bug: Bug Fix\n\n-   `babel-parser`\n    -   [#15423](https://togithub.com/babel/babel/pull/15423) \\[ts] Allow keywords in tuple labels ([@Harpica](https://togithub.com/Harpica))\n-   `babel-plugin-transform-typescript`\n    -   [#15489](https://togithub.com/babel/babel/pull/15489) Register `var` decls generated by `import ... =` TS transform ([@amoeller](https://togithub.com/amoeller))\n    -   [#15494](https://togithub.com/babel/babel/pull/15494) fix: Consider `export { type foo }` as type-only usage ([@magic-akari](https://togithub.com/magic-akari))\n\n##### :nail_care: Polish\n\n-   `babel-traverse`, `babel-types`\n    -   [#15484](https://togithub.com/babel/babel/pull/15484) Skip node deprecation warnings when used by an old `@babel` package ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-generator`\n    -   [#15480](https://togithub.com/babel/babel/pull/15480) chore: Improve `jsonCompatibleStrings` deprecation ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :house: Internal\n\n-   [#15465](https://togithub.com/babel/babel/pull/15465) Add ESLint-readable package name ([@nzakas](https://togithub.com/nzakas))\n\n##### :microscope: Output optimization\n\n-   `babel-plugin-transform-typescript`, `babel-preset-typescript`\n    -   [#15467](https://togithub.com/babel/babel/pull/15467) Optimize TS enums output ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n### [`v7.21.0`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7210-2023-02-20)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.20.12...v7.21.0)\n\n##### :rocket: New Feature\n\n-   `babel-core`, `babel-helper-create-class-features-plugin`, `babel-plugin-proposal-class-properties`, `babel-plugin-proposal-private-methods`, `babel-plugin-proposal-private-property-in-object`\n    -   [#15435](https://togithub.com/babel/babel/pull/15435) feat: Implement `privateFieldsAsSymbols` assumption for classes ([@fwienber](https://togithub.com/fwienber))\n-   `babel-helper-create-regexp-features-plugin`, `babel-plugin-proposal-regexp-modifiers`, `babel-standalone`\n    -   [#15226](https://togithub.com/babel/babel/pull/15226) feat: Support regexp modifiers proposal ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-cli`, `babel-core`, `babel-generator`, `babel-plugin-transform-destructuring`, `babel-plugin-transform-modules-commonjs`, `babel-plugin-transform-react-jsx`, `babel-traverse`\n    -   [#15022](https://togithub.com/babel/babel/pull/15022) feat: Generate sourcemaps of friendly call frames ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-parser`, `babel-types`\n    -   [#15384](https://togithub.com/babel/babel/pull/15384) \\[ts] Support `const` modifier in type parameters ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-generator`, `babel-helpers`, `babel-parser`, `babel-plugin-proposal-decorators`, `babel-plugin-syntax-decorators`, `babel-runtime-corejs2`, `babel-runtime-corejs3`, `babel-runtime`\n    -   [#15405](https://togithub.com/babel/babel/pull/15405) Implement decorators as presented at `2023-01` TC39 meeting ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-parser`\n    -   [#15114](https://togithub.com/babel/babel/pull/15114) Parser option to allow `new.target` outside functions ([@overlookmotel](https://togithub.com/overlookmotel))\n    -   [#15320](https://togithub.com/babel/babel/pull/15320) Add `annexb: false` parser option to disable Annex B ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-core`\n    -   [#15283](https://togithub.com/babel/babel/pull/15283) feat: Support `.cts` as configuration file ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-generator`, `babel-parser`, `babel-plugin-transform-typescript`\n    -   [#15381](https://togithub.com/babel/babel/pull/15381) \\[ts] Support `export type * from` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :bug: Bug Fix\n\n-   `babel-plugin-transform-typescript`\n    -   [#15379](https://togithub.com/babel/babel/pull/15379) \\[ts5.0] Better inlining of constants in enums ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-core`\n    -   [#15366](https://togithub.com/babel/babel/pull/15366) handling circular/shared structures in deep-clone ([@azizghuloum](https://togithub.com/azizghuloum))\n-   `babel-helper-create-class-features-plugin`, `babel-plugin-proposal-class-properties`, `babel-plugin-proposal-class-static-block`, `babel-plugin-proposal-private-methods`, `babel-plugin-transform-classes`, `babel-plugin-transform-new-target`\n    -   [#15406](https://togithub.com/babel/babel/pull/15406) Preserve class elements comments in class transform ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-parser`, `babel-plugin-transform-flow-comments`, `babel-plugin-transform-flow-strip-types`, `babel-types`\n    -   [#15414](https://togithub.com/babel/babel/pull/15414) \\[ts] Fix restrictions for optional parameters ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :nail_care: Polish\n\n-   `babel-parser`\n    -   [#15400](https://togithub.com/babel/babel/pull/15400) polish: improve \"`await` as identifier\" error in modules ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :house: Internal\n\n-   `babel-core`\n    -   [#15137](https://togithub.com/babel/babel/pull/15137) Improve CJS compat with ESM-based `@babel/core` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :microscope: Output optimization\n\n-   `babel-plugin-transform-typescript`\n    -   [#15418](https://togithub.com/babel/babel/pull/15418) \\[ts] Handle exponentiation operator in constant folding ([@ehoogeveen-medweb](https://togithub.com/ehoogeveen-medweb))\n\n### [`v7.20.12`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v72012-2023-01-04)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.20.7...v7.20.12)\n\n##### :bug: Bug Fix\n\n-   `babel-traverse`\n    -   [#15224](https://togithub.com/babel/babel/pull/15224) Fix `TaggedTemplateLiteral` evaluation ([@nmn](https://togithub.com/nmn))\n-   `babel-helper-create-class-features-plugin`, `babel-plugin-proposal-class-properties`\n    -   [#15312](https://togithub.com/babel/babel/pull/15312) fix: `delete this` in static class properties initialization ([@SuperSodaSea](https://togithub.com/SuperSodaSea))\n\n##### :nail_care: Polish\n\n-   `babel-traverse`\n    -   [#15313](https://togithub.com/babel/babel/pull/15313) Implement support for evaluating computed properties. ([@JBYoshi](https://togithub.com/JBYoshi))\n\n### [`v7.20.7`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7207-2022-12-22)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.20.5...v7.20.7)\n\n##### :eyeglasses: Spec Compliance\n\n-   `babel-helper-member-expression-to-functions`, `babel-helper-replace-supers`, `babel-plugin-proposal-class-properties`, `babel-plugin-transform-classes`\n    -   [#15223](https://togithub.com/babel/babel/pull/15223) fix: Deleting super property should throw ([@SuperSodaSea](https://togithub.com/SuperSodaSea))\n-   `babel-helpers`, `babel-plugin-proposal-class-properties`, `babel-plugin-transform-classes`, `babel-plugin-transform-object-super`\n    -   [#15241](https://togithub.com/babel/babel/pull/15241) fix: Throw correct error types from sed ant class TDZ helpers ([@SuperSodaSea](https://togithub.com/SuperSodaSea))\n\n##### :bug: Bug Fix\n\n-   `babel-parser`, `babel-plugin-transform-typescript`\n    -   [#15209](https://togithub.com/babel/babel/pull/15209) fix: Support auto accessors with TypeScript annotations ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-traverse`\n    -   [#15287](https://togithub.com/babel/babel/pull/15287) Fix `.parentPath` after rename in `SwitchCase` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-plugin-transform-typescript`, `babel-traverse`\n    -   [#15284](https://togithub.com/babel/babel/pull/15284) fix: Ts import type and func with duplicate name ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-plugin-transform-block-scoping`\n    -   [#15278](https://togithub.com/babel/babel/pull/15278) Fix tdz analysis for reassigned captured for bindings ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-plugin-proposal-async-generator-functions`, `babel-preset-env`\n    -   [#15235](https://togithub.com/babel/babel/pull/15235) fix: Transform `for await` with shadowed variables ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-generator`, `babel-plugin-proposal-optional-chaining`\n    -   [#15258](https://togithub.com/babel/babel/pull/15258) fix: Correctly generate `(a ?? b) as T` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-plugin-transform-react-jsx`, `babel-types`\n    -   [#15233](https://togithub.com/babel/babel/pull/15233) fix: Emit correct sourcemap ranges for `JSXText` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-core`, `babel-helpers`, `babel-plugin-transform-computed-properties`, `babel-runtime-corejs2`, `babel-runtime-corejs3`, `babel-runtime`\n    -   [#15232](https://togithub.com/babel/babel/pull/15232) fix: Computed properties should keep original definition order ([@SuperSodaSea](https://togithub.com/SuperSodaSea))\n-   `babel-helper-member-expression-to-functions`, `babel-helper-replace-supers`, `babel-plugin-proposal-class-properties`, `babel-plugin-transform-classes`\n    -   [#15223](https://togithub.com/babel/babel/pull/15223) fix: Deleting super property should throw ([@SuperSodaSea](https://togithub.com/SuperSodaSea))\n-   `babel-generator`\n    -   [#15216](https://togithub.com/babel/babel/pull/15216) fix: Print newlines for leading Comments of `TSEnumMember` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :nail_care: Polish\n\n-   `babel-plugin-transform-block-scoping`, `babel-traverse`\n    -   [#15275](https://togithub.com/babel/babel/pull/15275) Improve relative execution tracking in fn exprs ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :house: Internal\n\n-   `babel-helper-define-map`, `babel-plugin-transform-property-mutators`\n    -   [#15274](https://togithub.com/babel/babel/pull/15274) Inline & simplify `@babel/helper-define-map` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-core`, `babel-plugin-proposal-class-properties`, `babel-plugin-transform-block-scoping`, `babel-plugin-transform-classes`, `babel-plugin-transform-destructuring`, `babel-plugin-transform-parameters`, `babel-plugin-transform-regenerator`, `babel-plugin-transform-runtime`, `babel-preset-env`, `babel-traverse`\n    -   [#15200](https://togithub.com/babel/babel/pull/15200) Rewrite `transform-block-scoping` plugin ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :running_woman: Performance\n\n-   `babel-helper-compilation-targets`\n    -   [#15228](https://togithub.com/babel/babel/pull/15228) perf: Speed up `getTargets` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n### [`v7.20.5`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7205-2022-11-28)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.20.2...v7.20.5)\n\n##### :eyeglasses: Spec Compliance\n\n-   `babel-helpers`, `babel-plugin-transform-destructuring`, `babel-plugin-transform-modules-commonjs`, `babel-preset-env`, `babel-runtime-corejs2`, `babel-runtime-corejs3`, `babel-runtime`, `babel-traverse`\n    -   [#15183](https://togithub.com/babel/babel/pull/15183) Improve array destructuring spec compliance ([@SuperSodaSea](https://togithub.com/SuperSodaSea))\n-   `babel-cli`, `babel-helpers`, `babel-plugin-proposal-class-properties`, `babel-plugin-proposal-class-static-block`, `babel-plugin-transform-classes`, `babel-plugin-transform-runtime`, `babel-preset-env`\n    -   [#15182](https://togithub.com/babel/babel/pull/15182) fix: apply toPropertyKey when defining class members ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-helper-create-class-features-plugin`, `babel-helpers`, `babel-plugin-proposal-decorators`, `babel-plugin-proposal-private-property-in-object`, `babel-preset-env`, `babel-runtime-corejs2`, `babel-runtime-corejs3`, `babel-runtime`\n    -   [#15133](https://togithub.com/babel/babel/pull/15133) fix: validate rhs of `in` when transpiling `#p in C` ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :bug: Bug Fix\n\n-   `babel-parser`\n    -   [#15225](https://togithub.com/babel/babel/pull/15225) Parse `using[foo]` as computed member expression ([@JLHwung](https://togithub.com/JLHwung))\n    -   [#15207](https://togithub.com/babel/babel/pull/15207) Export `ParseResult` type ([@davydof](https://togithub.com/davydof))\n    -   [#15198](https://togithub.com/babel/babel/pull/15198) fix: parse `import module, ...` ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-helper-wrap-function`, `babel-preset-env`, `babel-traverse`\n    -   [#15181](https://togithub.com/babel/babel/pull/15181) fix: Edge cases for async functions and `noNewArrow` assumption ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-plugin-transform-arrow-functions`, `babel-plugin-transform-parameters`, `babel-traverse`\n    -   [#15163](https://togithub.com/babel/babel/pull/15163) fix: Throw error when compiling `super()` in arrow functions with default / rest parameters ([@SuperSodaSea](https://togithub.com/SuperSodaSea))\n-   `babel-helpers`, `babel-node`, `babel-plugin-proposal-async-generator-functions`, `babel-plugin-transform-regenerator`, `babel-preset-env`, `babel-runtime-corejs2`, `babel-runtime-corejs3`, `babel-runtime`\n    -   [#15194](https://togithub.com/babel/babel/pull/15194) fix: Bump `regenerator` and add tests ([@SuperSodaSea](https://togithub.com/SuperSodaSea))\n-   `babel-helper-create-regexp-features-plugin`\n    -   [#15192](https://togithub.com/babel/babel/pull/15192) fix: Update `regjsparser` for `@babel/standalone` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-parser`, `babel-types`\n    -   [#15109](https://togithub.com/babel/babel/pull/15109) fix: Babel 8 types ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-generator`\n    -   [#15143](https://togithub.com/babel/babel/pull/15143) Don't print inner comments as leading when wrapping in `(``)` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-plugin-transform-block-scoping`, `babel-traverse`\n    -   [#15167](https://togithub.com/babel/babel/pull/15167) Register `switch`'s `discriminant` in the outer scope ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :nail_care: Polish\n\n-   `babel-generator`\n    -   [#15173](https://togithub.com/babel/babel/pull/15173) Improve generator behavior when `comments:false` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-plugin-transform-block-scoping`\n    -   [#15164](https://togithub.com/babel/babel/pull/15164) Only extract IDs for TDZ checks in assign when necessary ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :house: Internal\n\n-   `babel-core`, `babel-parser`\n    -   [#15202](https://togithub.com/babel/babel/pull/15202) Bump typescript to 4.9.3 ([@JLHwung](https://togithub.com/JLHwung))\n\n### [`v7.20.2`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7202-2022-11-04)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.19.6...v7.20.2)\n\n##### :bug: Bug Fix\n\n-   `babel-core`, `babel-helper-create-class-features-plugin`, `babel-helper-module-transforms`, `babel-helper-plugin-utils`, `babel-helper-simple-access`, `babel-node`, `babel-plugin-transform-block-scoping`, `babel-plugin-transform-classes`, `babel-plugin-transform-react-constant-elements`, `babel-preset-env`, `babel-standalone`, `babel-types`\n    -   [#15124](https://togithub.com/babel/babel/pull/15124) fix: `@babel/node` repl and enable `no-use-before-define` rule ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-plugin-transform-typescript`\n    -   [#15121](https://togithub.com/babel/babel/pull/15121) fix: `tsSatisfiesExpression` check with different duplicated `@babel/types` versions ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-parser`\n    -   [#15094](https://togithub.com/babel/babel/pull/15094) fix: `parser` typings for plugins ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-generator`\n    -   [#15118](https://togithub.com/babel/babel/pull/15118) Improve printing of \\[no LineTerminator here] with comments ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-plugin-proposal-decorators`, `babel-plugin-proposal-object-rest-spread`, `babel-plugin-transform-jscript`\n    -   [#15113](https://togithub.com/babel/babel/pull/15113) fix: wrap anonymous class expression within statement ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-plugin-transform-destructuring`\n    -   [#15104](https://togithub.com/babel/babel/pull/15104) fix: Destructuring exceptions ` for ( let { } = 0 ; 0 ; )  ` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n### [`v7.19.6`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7196-2022-10-20)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.19.3...v7.19.6)\n\n##### :eyeglasses: Spec Compliance\n\n-   `babel-plugin-proposal-decorators`\n    -   [#15059](https://togithub.com/babel/babel/pull/15059) Ensure non-static decorators are applied when a class is instantiated. ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :bug: Bug Fix\n\n-   `babel-parser`\n    -   [#15062](https://togithub.com/babel/babel/pull/15062) Fix parsing of block comments nested in flow comments ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n    -   [#15052](https://togithub.com/babel/babel/pull/15052) fix: improve module block program location tracking ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-plugin-transform-runtime`, `babel-runtime-corejs2`, `babel-runtime-corejs3`\n    -   [#15060](https://togithub.com/babel/babel/pull/15060) Ensure `@babel/runtime-corejs3/core-js/*.js` can be imported on Node.js 17+ ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-preset-env`, `babel-traverse`\n    -   [#15043](https://togithub.com/babel/babel/pull/15043) fix: preserve this for `super.*` template tags ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :nail_care: Polish\n\n-   `babel-generator`, `babel-plugin-transform-flow-comments`\n    -   [#15037](https://togithub.com/babel/babel/pull/15037) Improve generation of comments without location ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :memo: Documentation\n\n-   `babel-standalone`\n    -   [#15055](https://togithub.com/babel/babel/pull/15055) Fix missing `transformSync` function name ([@lomirus](https://togithub.com/lomirus))\n\n##### :house: Internal\n\n-   `babel-parser`\n    -   [#15056](https://togithub.com/babel/babel/pull/15056) Use `startLoc.index` instead of carrying around `start` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   Other\n    -   [#15035](https://togithub.com/babel/babel/pull/15035) chore: Update yarn 3.2.4 ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :running_woman: Performance\n\n-   `babel-core`, `babel-standalone`\n    -   [#15023](https://togithub.com/babel/babel/pull/15023) Don't bundle unnecessary plugins in `@babel/standalone` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n### [`v7.19.3`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7193-2022-09-27)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.19.1...v7.19.3)\n\n##### :bug: Bug Fix\n\n-   `babel-plugin-proposal-decorators`\n    -   [#8566](https://togithub.com/babel/babel/pull/8566) Correctly update bindings of decorated class declarations ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-parser`\n    -   [#14974](https://togithub.com/babel/babel/pull/14974) fix: Normal parsing of `JSXText` following `JSXSpreadChild` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n    -   [#14941](https://togithub.com/babel/babel/pull/14941) fix: Support local exports in TS `declare module`s ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n    -   [#14940](https://togithub.com/babel/babel/pull/14940) fix: allow ts redeclaration with `import =` and `var` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-generator`\n    -   [#14962](https://togithub.com/babel/babel/pull/14962) Fix printing of Flow internal slot functions ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-cli`\n    -   [#14950](https://togithub.com/babel/babel/pull/14950) Emit `@babel/cli` source maps based on configuration files ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-plugin-transform-typescript`\n    -   [#14946](https://togithub.com/babel/babel/pull/14946) fix: ts exported vars are shadowed by `declare` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :nail_care: Polish\n\n-   `babel-core`\n    -   [#14954](https://togithub.com/babel/babel/pull/14954) Optional filename when preset uses fn test/include/exclude ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :house: Internal\n\n-   `babel-helper-compilation-targets`, `babel-helper-transform-fixture-test-runner`, `babel-parser`, `babel-preset-env`, `babel-traverse`\n    -   [#14961](https://togithub.com/babel/babel/pull/14961) chore: use `c8` for coverage testing ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :microscope: Output optimization\n\n-   `babel-plugin-transform-typescript`\n    -   [#14952](https://togithub.com/babel/babel/pull/14952) \\[ts] remove nested `declare namespace` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n### [`v7.19.1`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7191-2022-09-14)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.19.0...v7.19.1)\n\n##### :bug: Bug Fix\n\n-   `babel-core`\n    -   [#14930](https://togithub.com/babel/babel/pull/14930) Avoid fancy stack traces size computation ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-traverse`\n    -   [#14932](https://togithub.com/babel/babel/pull/14932) fix: isForAwaitStatement is broken ([@JLHwung](https://togithub.com/JLHwung))\n-   Other\n    -   [#14872](https://togithub.com/babel/babel/pull/14872) Use the built-in class fields and private methods rules in ESLint 8 ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-parser`\n    -   [#14920](https://togithub.com/babel/babel/pull/14920) \\[estree] attach comments after directives at the end of file ([@hegemonic](https://togithub.com/hegemonic))\n    -   [#14900](https://togithub.com/babel/babel/pull/14900) \\[ts] allow redeclaring a var/type with the same name as import ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-plugin-transform-typescript`\n    -   [#14913](https://togithub.com/babel/babel/pull/14913) fix: do not remove type import used in TS import= ([@JLHwung](https://togithub.com/JLHwung))\n\n### [`v7.19.0`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7190-2022-09-05)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.18.13...v7.19.0)\n\n##### :eyeglasses: Spec Compliance\n\n-   `babel-parser`\n    -   [#14666](https://togithub.com/babel/babel/pull/14666) Support private name in decorator member expression ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-helpers`, `babel-plugin-proposal-async-generator-functions`, `babel-preset-env`, `babel-runtime-corejs2`, `babel-runtime-corejs3`, `babel-runtime`\n    -   [#14877](https://togithub.com/babel/babel/pull/14877)  Remove one promise tick in yield\\* ([tc39/ecma262#2819](https://togithub.com/tc39/ecma262/issues/2819))  ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :rocket: New Feature\n\n-   `babel-generator`, `babel-helpers`, `babel-parser`, `babel-plugin-proposal-decorators`, `babel-plugin-syntax-decorators`, `babel-runtime-corejs2`, `babel-runtime-corejs3`, `babel-runtime`\n    -   [#14836](https://togithub.com/babel/babel/pull/14836) Add 2022-03 decorators version (stage 3) ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-parser`\n    -   [#14695](https://togithub.com/babel/babel/pull/14695) \\[parser] Make `decoratorsBeforeExport` default to `false` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-generator`, `babel-parser`\n    -   [#14744](https://togithub.com/babel/babel/pull/14744) Default to hash syntax for Record\\&Tuple ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-standalone`\n    -   [#14867](https://togithub.com/babel/babel/pull/14867) feat: add proposal-record-and-tuple to standalone ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-helper-create-regexp-features-plugin`, `babel-helpers`, `babel-plugin-proposal-duplicate-named-capturing-groups-regex`, `babel-plugin-transform-named-capturing-groups-regex`, `babel-standalone`\n    -   [#14805](https://togithub.com/babel/babel/pull/14805) Add support for the duplicate named capturing groups proposal ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :bug: Bug Fix\n\n-   `babel-helper-function-name`, `babel-helper-wrap-function`, `babel-plugin-transform-classes`\n    -   [#14897](https://togithub.com/babel/babel/pull/14897) Fix: class transform should not drop method definition when key contains non-BMP characters ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-plugin-transform-typescript`\n    -   [#14890](https://togithub.com/babel/babel/pull/14890) fix: TS plugin shouldn't remove `#privateField!` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-parser`\n    -   [#14819](https://togithub.com/babel/babel/pull/14819) fix: parse a\\>>c as a>>c) ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-helper-builder-react-jsx`\n    -   [#14886](https://togithub.com/babel/babel/pull/14886) Fix helper-builder-react-jsx compat with Babel 7.9 ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :nail_care: Polish\n\n-   `babel-core`\n    -   [#11612](https://togithub.com/babel/babel/pull/11612) Make error message prefixes more descriptive ([@eps1lon](https://togithub.com/eps1lon))\n    -   [#11554](https://togithub.com/babel/babel/pull/11554) Hide internal `@babel/core` functions in config errors ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :memo: Documentation\n\n-   [#14895](https://togithub.com/babel/babel/pull/14895) docs: remove david-dm from README ([@SukkaW](https://togithub.com/SukkaW))\n\n##### :house: Internal\n\n-   `babel-standalone`\n    -   [#14863](https://togithub.com/babel/babel/pull/14863) ship [@babel/standalone](https://togithub.com/babel/standalone) source maps ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-core`, `babel-parser`, `babel-traverse`\n    -   [#14880](https://togithub.com/babel/babel/pull/14880) Update typescript to 4.8 ([@JLHwung](https://togithub.com/JLHwung))\n\n### [`v7.18.13`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v71813-2022-08-22)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.18.10...v7.18.13)\n\n##### :bug: Bug Fix\n\n-   `babel-generator`\n    -   [#14869](https://togithub.com/babel/babel/pull/14869) fix: jsx with `retainLines` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-core`\n    -   [#14843](https://togithub.com/babel/babel/pull/14843) Fix a race condition in `@babel/core` ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-plugin-transform-destructuring`\n    -   [#14841](https://togithub.com/babel/babel/pull/14841) fix: Destructuring exceptions in nested `for` expressions ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :nail_care: Polish\n\n-   `babel-traverse`\n    -   [#14833](https://togithub.com/babel/babel/pull/14833) Let `path.remove()` remove `IfStatement.alternate` ([@djpohly](https://togithub.com/djpohly))\n    -   [#14837](https://togithub.com/babel/babel/pull/14837) Add support for static evaluation of ?? operator ([@djpohly](https://togithub.com/djpohly))\n\n##### :house: Internal\n\n-   [#14846](https://togithub.com/babel/babel/pull/14846) fix: Print build logs correctly ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n### [`v7.18.10`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v71810-2022-08-01)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.18.9...v7.18.10)\n\n##### :rocket: New Feature\n\n-   `babel-helper-string-parser`, `babel-types`\n    -   [#14757](https://togithub.com/babel/babel/pull/14757) feat: Automatically generate `cooked` for `templateElement` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :bug: Bug Fix\n\n-   `babel-parser`\n    -   [#14817](https://togithub.com/babel/babel/pull/14817) fix(parser): allow TS declare readonly fields with initializers ([@Josh-Cena](https://togithub.com/Josh-Cena))\n-   `babel-helper-string-parser`, `babel-parser`\n    -   [#14798](https://togithub.com/babel/babel/pull/14798) Fix position of errors in template literals after newlines ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-plugin-transform-typescript`\n    -   [#14774](https://togithub.com/babel/babel/pull/14774) fix: TS `declare class` in namespace should be removed ([@yimingjfe](https://togithub.com/yimingjfe))\n-   `babel-plugin-transform-react-jsx`\n    -   [#14759](https://togithub.com/babel/babel/pull/14759) fix: skip flattening spread object with **proto** ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-generator`\n    -   [#14762](https://togithub.com/babel/babel/pull/14762) fix: Types containing comments generate invalid code ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-helper-wrap-function`, `babel-plugin-transform-async-to-generator`, `babel-traverse`\n    -   [#14752](https://togithub.com/babel/babel/pull/14752) Fix compiling async arrows in uncompiled class fields ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :house: Internal\n\n-   Other\n    -   [#14800](https://togithub.com/babel/babel/pull/14800) chore: Remove `.yarnrc` file ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n    -   [#14802](https://togithub.com/babel/babel/pull/14802) chore: Fix coverage test ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n    -   [#14671](https://togithub.com/babel/babel/pull/14671) feat: Make most `make` commands cross-platform ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n    -   [#14790](https://togithub.com/babel/babel/pull/14790) enable typescript incremental builds ([@zxbodya](https://togithub.com/zxbodya))\n-   `babel-traverse`\n    -   [#14799](https://togithub.com/babel/babel/pull/14799) Restructure virtual types validator ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-cli`\n    -   [#14779](https://togithub.com/babel/babel/pull/14779) chore: expand prettier-e2e test and update typings/deps ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-parser`\n    -   [#14796](https://togithub.com/babel/babel/pull/14796) Make ParseError Much Simpler now that we can use TypeScript ([@tolmasky](https://togithub.com/tolmasky))\n-   `babel-core`, `babel-parser`\n    -   [#14785](https://togithub.com/babel/babel/pull/14785) chore: remove flow check scripts ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-cli`, `babel-core`, `babel-parser`, `babel-plugin-transform-unicode-escapes`, `babel-preset-env`, `babel-template`, `babel-traverse`\n    -   [#14783](https://togithub.com/babel/babel/pull/14783) Convert `@babel/parser` to TypeScript ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-helper-string-parser`, `babel-parser`\n    -   [#14772](https://togithub.com/babel/babel/pull/14772) Extract string parsing to a separate package ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-cli`, `babel-node`\n    -   [#14765](https://togithub.com/babel/babel/pull/14765) Enforce type checking on `babel-{cli,node}` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :microscope: Output optimization\n\n-   `babel-plugin-proposal-export-default-from`\n    -   [#14768](https://togithub.com/babel/babel/pull/14768) optimize: Simplify the `export-default-from` transform ([@magic-akari](https://togithub.com/magic-akari))\n\n### [`v7.18.9`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7189-2022-07-18)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.18.6...v7.18.9)\n\n##### :bug: Bug Fix\n\n-   `babel-plugin-transform-modules-systemjs`, `babel-types`\n    -   [#14763](https://togithub.com/babel/babel/pull/14763) fix: allow exporting `TSDeclareFunction` as default ([@zxbodya](https://togithub.com/zxbodya))\n-   `babel-generator`\n    -   [#14758](https://togithub.com/babel/babel/pull/14758) fix: `returnType` with comments generates incorrect code ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :nail_care: Polish\n\n-   `babel-cli`\n    -   [#14748](https://togithub.com/babel/babel/pull/14748) Print a message when the watcher of `babel-cli` is ready. ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :house: Internal\n\n-   `babel-core`, `babel-helper-remap-async-to-generator`, `babel-helpers`, `babel-parser`, `babel-plugin-transform-block-scoping`, `babel-preset-env`\n    -   [#13414](https://togithub.com/babel/babel/pull/13414) Prepare for compiling Babel to native ESM ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-helper-create-class-features-plugin`, `babel-helper-member-expression-to-functions`, `babel-helper-remap-async-to-generator`, `babel-helper-replace-supers`, `babel-helper-wrap-function`, `babel-helpers`, `babel-plugin-bugfix-v8-spread-parameters-in-optional-chaining`, `babel-plugin-proposal-decorators`, `babel-plugin-proposal-object-rest-spread`, `babel-plugin-proposal-optional-chaining`, `babel-plugin-transform-block-scoping`, `babel-plugin-transform-classes`, `babel-traverse`, `babel-types`\n    -   [#14739](https://togithub.com/babel/babel/pull/14739) Provide better parentPath typings ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :running_woman: Performance\n\n-   `babel-generator`\n    -   [#14701](https://togithub.com/babel/babel/pull/14701) perf: Improve generator perf ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n### [`v7.18.6`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7186-2022-06-27)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.18.5...v7.18.6)\n\n##### :eyeglasses: Spec Compliance\n\n-   `babel-parser`\n    -   [#14650](https://togithub.com/babel/babel/pull/14650) \\[ts] Disallow property access after instantiation expression ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n    -   [#14636](https://togithub.com/babel/babel/pull/14636) \\[ts] Allow `...` followed by newline or binary operator ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-generator`, `babel-parser`, `babel-preset-env`, `babel-template`\n    -   [#14668](https://togithub.com/babel/babel/pull/14668) JSON modules should be imported with default ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :bug: Bug Fix\n\n-   `babel-helper-remap-async-to-generator`, `babel-plugin-proposal-async-generator-functions`\n    -   [#14391](https://togithub.com/babel/babel/pull/14391) Transform `await` in computed class keys ([@Yokubjon-J](https://togithub.com/Yokubjon-J))\n-   `babel-plugin-transform-parameters`\n    -   [#14694](https://togithub.com/babel/babel/pull/14694) fix: preserve function params type if possible ([@magic-akari](https://togithub.com/magic-akari))\n-   `babel-core`\n    -   [#14583](https://togithub.com/babel/babel/pull/14583) fix: Memory leak when deep cloning in `babel-core` ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-core`, `babel-helper-check-duplicate-nodes`, `babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression`, `babel-plugin-bugfix-v8-spread-parameters-in-optional-chaining`, `babel-plugin-proposal-destructuring-private`, `babel-plugin-proposal-optional-chaining`, `babel-plugin-transform-runtime`\n    -   [#14663](https://togithub.com/babel/babel/pull/14663) Fix `import { types } from \"@babel/core\"` with native ESM ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :house: Internal\n\n-   `babel-standalone`\n    -   [#14697](https://togithub.com/babel/babel/pull/14697) Add `proposal-unicode-sets-regex` to `@babel/standalone` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   Other\n    -   [#14687](https://togithub.com/babel/babel/pull/14687) chore: Update bench baselines ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-generator`, `babel-types`\n    -   [#14685](https://togithub.com/babel/babel/pull/14685) enable TS compiler option: strictBindCallApply ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-code-frame`, `babel-core`, `babel-generator`, `babel-helper-annotate-as-pure`, `babel-helper-builder-binary-assignment-operator-visitor`, `babel-helper-builder-react-jsx`, `babel-helper-check-duplicate-nodes`, `babel-helper-compilation-targets`, `babel-helper-create-class-features-plugin`, `babel-helper-create-regexp-features-plugin`, `babel-helper-define-map`, `babel-helper-explode-assignable-expression`, `babel-helper-fixtures`, `babel-helper-function-name`, `babel-helper-hoist-variables`, `babel-helper-member-expression-to-functions`, `babel-helper-module-imports`, `babel-helper-module-transforms`, `babel-helper-optimise-call-expression`, `babel-helper-plugin-test-runner`, `babel-helper-plugin-utils`, `babel-helper-remap-async-to-generator`, `babel-helper-replace-supers`, `babel-helper-simple-access`, `babel-helper-split-export-declaration`, `babel-helper-transform-fixture-test-runner`, `babel-helper-validator-option`, `babel-helper-wrap-function`, `babel-helpers`, `babel-highlight`, `babel-plugin-bugfix-v8-spread-parameters-in-optional-chaining`, `babel-plugin-external-helpers`, `babel-plugin-proposal-async-generator-functions`, `babel-plugin-proposal-class-static-block`, `babel-plugin-proposal-decorators`, `babel-plugin-proposal-destructuring-private`, `babel-plugin-proposal-function-bind`, `babel-plugin-proposal-function-sent`, `babel-plugin-proposal-json-strings`, `babel-plugin-proposal-object-rest-spread`, `babel-plugin-proposal-optional-chaining`, `babel-plugin-proposal-partial-application`, `babel-plugin-proposal-pipeline-operator`, `babel-plugin-proposal-private-property-in-object`, `babel-plugin-proposal-record-and-tuple`, `babel-plugin-syntax-typescript`, `babel-plugin-transform-block-scoped-functions`, `babel-plugin-transform-block-scoping`, `babel-plugin-transform-classes`, `babel-plugin-transform-computed-properties`, `babel-plugin-transform-destructuring`, `babel-plugin-transform-duplicate-keys`, `babel-plugin-transform-exponentiation-operator`, `babel-plugin-transform-flow-comments`, `babel-plugin-transform-flow-strip-types`, `babel-plugin-transform-for-of`, `babel-plugin-transform-function-name`, `babel-plugin-transform-modules-amd`, `babel-plugin-transform-modules-commonjs`, `babel-plugin-transform-modules-systemjs`, `babel-plugin-transform-modules-umd`, `babel-plugin-transform-object-super`, `babel-plugin-transform-parameters`, `babel-plugin-transform-property-mutators`, `babel-plugin-transform-proto-to-assign`, `babel-plugin-transform-react-constant-elements`, `babel-plugin-transform-react-display-name`, `babel-plugin-transform-react-inline-elements`, `babel-plugin-transform-react-jsx-compat`, `babel-plugin-transform-react-jsx-source`, `babel-plugin-transform-react-jsx`, `babel-plugin-transform-runtime`, `babel-plugin-transform-typescript`, `babel-plugin-transform-unicode-escapes`, `babel-preset-env`, `babel-preset-typescript`, `babel-standalone`, `babel-template`, `babel-traverse`, `babel-types`\n    -   [#14601](https://togithub.com/babel/babel/pull/14601) enable noImplicitAny ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-core`, `babel-helper-transform-fixture-test-runner`, `babel-plugin-transform-destructuring`\n    -   [#14659](https://togithub.com/babel/babel/pull/14659) Run Babel asynchronously in fixtures ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n### [`v7.18.5`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7185-2022-06-13)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.18.2...v7.18.5)\n\n##### :bug: Bug Fix\n\n-   `babel-plugin-transform-new-target`\n    -   [#14611](https://togithub.com/babel/babel/pull/14611) fix: `new.target` with shadowed class name ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-plugin-transform-modules-systemjs`\n    -   [#14655](https://togithub.com/babel/babel/pull/14655) Fix named destructuring exports ([@underfin](https://togithub.com/underfin))\n\n##### :memo: Documentation\n\n-   [#14332](https://togithub.com/babel/babel/pull/14332) docs: eslint-parser requireConfigFile behaviour ([@JLHwung](https://togithub.com/JLHwung))\n-   [#14619](https://togithub.com/babel/babel/pull/14619) Move v7 prereleases changelog to a separate file ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :house: Internal\n\n-   `babel-traverse`\n    -   [#14649](https://togithub.com/babel/babel/pull/14649) Rely on the call stack to clean up cache in `_guessExecutionStatusRelativeTo` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-core`\n    -   [#14641](https://togithub.com/babel/babel/pull/14641) Change limit of source map 3MB ([@vasicvuk](https://togithub.com/vasicvuk))\n-   Other\n    -   [#14627](https://togithub.com/babel/babel/pull/14627) Speedup e2e test on github ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n    -   [#14248](https://togithub.com/babel/babel/pull/14248) chore: automate compat-data update ([@tony-go](https://togithub.com/tony-go))\n-   `babel-parser`\n    -   [#14592](https://togithub.com/babel/babel/pull/14592) feat: Automatically generate test results that do not exist ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n##### :running_woman: Performance\n\n-   `babel-traverse`\n    -   [#14617](https://togithub.com/babel/babel/pull/14617) Fix `_guessExecutionStatusRelativeToDifferentFunctions` perf ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n### [`v7.18.2`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7182-2022-05-25)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.18.0...v7.18.2)\n\n##### :bug: Bug Fix\n\n-   `babel-plugin-transform-template-literals`\n    -   [#14582](https://togithub.com/babel/babel/pull/14582) fix: skip template literal transform for TSLiteralType ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-helpers`\n    -   [#14537](https://togithub.com/babel/babel/pull/14537) Support frozen built-ins in `@babel/runtime` ([@Jack-Works](https://togithub.com/Jack-Works))\n-   `babel-runtime-corejs2`, `babel-runtime-corejs3`, `babel-runtime`\n    -   [#14581](https://togithub.com/babel/babel/pull/14581) Define the global `regeneratorRuntime` in `@babel/runtime/regenerator` ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-helper-environment-visitor`, `babel-helper-replace-supers`, `babel-plugin-proposal-class-properties`, `babel-plugin-proposal-decorators`, `babel-traverse`, `babel-types`\n    -   [#14371](https://togithub.com/babel/babel/pull/14371) environmentVisitor should skip decorator expressions ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :memo: Documentation\n\n-   `babel-types`\n    -   [#14571](https://togithub.com/babel/babel/pull/14571) add Accessor alias description ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :house: Internal\n\n-   [#14541](https://togithub.com/babel/babel/pull/14541) Fix synchronization between main thread and worker ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n\n### [`v7.18.0`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v7180-2022-05-19)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.17.12...v7.18.0)\n\n##### :rocket: New Feature\n\n-   `babel-preset-env`\n    -   [#14556](https://togithub.com/babel/babel/pull/14556) feat: add import-assertions to shippedProposals ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-helper-create-class-features-plugin`, `babel-helper-define-map`, `babel-plugin-proposal-class-static-block`, `babel-plugin-proposal-destructuring-private`, `babel-plugin-proposal-object-rest-spread`, `babel-plugin-syntax-destructuring-private`, `babel-plugin-transform-destructuring`, `babel-plugin-transform-proto-to-assign`, `babel-plugin-transform-typescript`, `babel-standalone`, `babel-traverse`, `babel-types`\n    -   [#14304](https://togithub.com/babel/babel/pull/14304) Transform destructuring private ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-generator`, `babel-parser`, `babel-types`\n    -   [#14359](https://togithub.com/babel/babel/pull/14359) \\[ts 4.7] Support optional variance annotations ([@magic-akari](https://togithub.com/magic-akari))\n-   `babel-generator`, `babel-parser`\n    -   [#14476](https://togithub.com/babel/babel/pull/14476) \\[ts 4.7] Support `extends` constraints for `infer` ([@sosukesuzuki](https://togithub.com/sosukesuzuki))\n-   `babel-generator`, `babel-parser`, `babel-plugin-transform-typescript`, `babel-traverse`, `babel-types`\n    -   [#14457](https://togithub.com/babel/babel/pull/14457) \\[ts] Add support for instantiation expressions ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-helper-module-transforms`, `babel-plugin-transform-modules-amd`, `babel-plugin-transform-modules-commonjs`, `babel-plugin-transform-modules-umd`\n    -   [#14456](https://togithub.com/babel/babel/pull/14456) Pass filename to `importInterop` method ([@NickHeiner](https://togithub.com/NickHeiner))\n\n##### :bug: Bug Fix\n\n-   `babel-types`\n    -   [#14551](https://togithub.com/babel/babel/pull/14551) Do not create multiple copies of comments when cloning nodes ([@liuxingbaoyu](https://togithub.com/liuxingbaoyu))\n-   `babel-parser`\n    -   [#14557](https://togithub.com/babel/babel/pull/14557) Fix parsing of `<` after object literals with the `jsx` plugin ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-plugin-transform-react-pure-annotations`\n    -   [#14528](https://togithub.com/babel/babel/pull/14528) fix: do not mark computed `React[...]` methods as pure ([@JLHwung](https://togithub.com/JLHwung))\n\n##### :nail_care: Polish\n\n-   `babel-core`, `babel-helper-transform-fixture-test-runner`, `babel-helpers`, `babel-plugin-proposal-async-generator-functions`, `babel-plugin-transform-async-to-generator`, `babel-plugin-transform-block-scoping`, `babel-plugin-transform-classes`, `babel-plugin-transform-regenerator`, `babel-plugin-transform-runtime`, `babel-preset-env`, `babel-runtime-corejs2`, `babel-runtime-corejs3`, `babel-runtime`, `babel-standalone`\n    -   [#14538](https://togithub.com/babel/babel/pull/14538) Inline `regeneratorRuntime` as a normal helper ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :house: Internal\n\n-   `babel-core`, `babel-helper-create-class-features-plugin`, `babel-plugin-proposal-decorators`, `babel-plugin-transform-modules-systemjs`\n    -   [#14530](https://togithub.com/babel/babel/pull/14530) improve helper-create-class-features typings ([@JLHwung](https://togithub.com/JLHwung))\n\n### [`v7.17.12`](https://togithub.com/babel/babel/blob/HEAD/CHANGELOG.md#v71712-2022-05-16)\n\n[Compare Source](https://togithub.com/babel/babel/compare/v7.17.10...v7.17.12)\n\n##### :bug: Bug Fix\n\n-   `babel-plugin-transform-react-constant-elements`\n    -   [#14536](https://togithub.com/babel/babel/pull/14536) Never hoist JSX elts referencing vars from the current scope ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n-   `babel-generator`\n    -   [#14524](https://togithub.com/babel/babel/pull/14524) fix: perserve parentheses of lhs id with rhs unamed fn ([@JLHwung](https://togithub.com/JLHwung))\n    -   [#14532](https://togithub.com/babel/babel/pull/14532) Print necessary parentheses for functions in postfix expressions ([@xiawenqi](https://togithub.com/xiawenqi))\n-   `babel-plugin-transform-destructuring`\n    -   [#14494](https://togithub.com/babel/babel/pull/14494) Update scope info after destructuring transform ([@peey](https://togithub.com/peey))\n-   `babel-parser`\n    -   [#14522](https://togithub.com/babel/babel/pull/14522) fix: allow liberal named type-as imports ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-parser`, `babel-plugin-transform-destructuring`, `babel-types`\n    -   [#14500](https://togithub.com/babel/babel/pull/14500) Fix parsing ts type casts and nested patterns in destructuring ([@nicolo-ribaudo](https://togithub.com/nicolo-ribaudo))\n\n##### :house: Internal\n\n-   `babel-plugin-proposal-decorators`, `babel-types`\n    -   [#14519](https://togithub.com/babel/babel/pull/14519) [@babel/types](https://togithub.com/babel/types) builder improvements ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-core`\n    -   [#14490](https://togithub.com/babel/babel/pull/14490) Update to Jest 28 ([@JLHwung](https://togithub.com/JLHwung))\n-   `babel-core`, `babel-generator`, `babel-helper-create-class-features-plugin`, `babel-helper-create-regexp-features-plugin`, `babel-helper-module-transforms`, `babel-helper-plugin-utils`, `babel-parser`, `babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression`, `babel-plugin-bugfix-v8-spread-parameters-in-optional-chaining`, `babel-plugin-external-helpers`, `babel-plugin-proposal-async-do-expressions`, `babel-plugin-proposal-async-generator-functions`, `babel-plugin-proposal-class-properties`, `babel-plugin-proposal-class-static-block`, `babel-plugin-proposal-decorators`, `babel-plugin-proposal-export-default-from`, `babel-plugin-proposal-export-namespace-from`, `babel-plugin-proposal-function-sent`, `babel-plugin-proposal-json-strings`, `babel-plugin-proposal-logical-assignment-operators`, `babel-plugin-proposal-nullish-coalescing-operator`, `babel-plugin-proposal-object-rest-spread`, `babel-plugin-proposal-optional-chaining`, `babel-plugin-proposal-partial-application`, `babel-plugin-proposal-pipeline-operator`, `babel-plugin-proposal-private-methods`, `babel-plugin-proposal-private-property-in-object`, `babel-plugin-proposal-record-and-tuple`, `babel-plugin-proposal-unicode-property-regex`, `babel-plugin-syntax-decorators`, `babel-plugin-syntax-destructuring-private`, `babel-plugin-syntax-flow`, `babel-plugin-syntax-import-assertions`, `babel-plugin-syntax-pipeline-operator`, `babel-plugin-syntax-record-and-tuple`, `babel-plugin-syntax-typescript`, `babel-plugin-transform-arrow-functions`, `babel-plugin-transform-async-to-generator`, `babel-plugin-transform-block-scoping`, `babel-plugin-transform-classes`, `babel-plugin-transform-computed-properties`, `babel-plugin-transform-destructuring`, `babel-plugin-transform-duplicate-keys`, `babel-plugin-transform-flow-comments`, `babel-plugin-transform-flow-strip-types`, `babel-plugin-transform-for-of`, `babel-plugin-transform-instanceof`, `babel-plugin-transform-jscript`, `babel-plugin-transform-literals`, `babel-plugin-transform-modules-amd`, `babel-plugin-transform-modules-commonjs`, `babel-plugin-transform-modules-systemjs`, `babel-plugin-transform-modules-umd`, `babel-plugin-transform-named-capturing-groups-regex`, `babel-plugin-transform-new-target`, `babel-plugin-transform-parameters`, `babel-plugin-transform-property-mutators`, `babel-plugin-transform-proto-to-assign`, `babel-plugin-transform-react-constant-elements`, `babel-plugin-transform-react-jsx`, `babel-plugin-transform-reserved-words`, `babel-plugin-transform-runtime`, `babel-plugin-transform-spread`, `babel-plugin-transform-template-literals`, `babel-plugin-transform-typeof-symbol`, `babel-plugin-transform-typescript`, `babel-preset-env`, `babel-preset-flow`, `babel-preset-react`, `babel-preset-typescript`, `babel-traverse`, `babel-types`\n    -   [#14499](https:\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Immortal: This PR will be recreated if closed unmerged. Get config help if that's undesired.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Add threadId to SourceMark", "number": 582, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/582", "body": ""}
{"comment": {"body": "Looks good - needs a db test to validate sourcemark is deleted when the thread goes away", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/582#issuecomment-1067526725"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/582#pullrequestreview-909622727", "body": ""}
{"title": "Improve gradle documentation for jvm args", "number": 5820, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5820"}
{"title": "Limit cache directories", "number": 5821, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5821"}
{"title": "Reduce concurrency", "number": 5822, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5822"}
{"title": "DisableGradleFlag", "number": 5823, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5823", "body": "Reduce concurrency\ndisable gradle flag"}
{"title": "chore(deps): update dependency @emotion/eslint-plugin to v11.10.0", "number": 5824, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5824", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @emotion/eslint-plugin (source) | 11.7.0 -> 11.10.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nemotion-js/emotion\n\n### [`v11.10.0`](https://togithub.com/emotion-js/emotion/releases/tag/%40emotion/react%4011.10.0)\n\n[Compare Source](https://togithub.com/emotion-js/emotion/compare/@emotion/eslint-plugin@11.7.0...@emotion/eslint-plugin@11.10.0)\n\n##### Minor Changes\n\n-   [#2819](https://togithub.com/emotion-js/emotion/pull/2819) [`bbad8c79`](https://togithub.com/emotion-js/emotion/commit/bbad8c79937f8dfd5d93bf485c1e9ec44124d228) Thanks [@nicksrandall](https://togithub.com/nicksrandall)! - `exports` field has been added to the `package.json` manifest. It limits what files can be imported from a package but we've tried our best to allow importing all the files that were considered to be a part of the public API.\n\n-   [#2819](https://togithub.com/emotion-js/emotion/pull/2819) [`bbad8c79`](https://togithub.com/emotion-js/emotion/commit/bbad8c79937f8dfd5d93bf485c1e9ec44124d228) Thanks [@nicksrandall](https://togithub.com/nicksrandall)! - Thanks to the added `exports` field, the package now includes a `worker` condition that can be utilized by properly configured bundlers when targeting worker-like environments. It fixes the issue with browser-specific files being prioritized by some bundlers when targeting workers.\n\n##### Patch Changes\n\n-   Updated dependencies \\[[`bbad8c79`](https://togithub.com/emotion-js/emotion/commit/bbad8c79937f8dfd5d93bf485c1e9ec44124d228), [`bbad8c79`](https://togithub.com/emotion-js/emotion/commit/bbad8c79937f8dfd5d93bf485c1e9ec44124d228)]:\n    -   [@emotion/babel-plugin](https://togithub.com/emotion/babel-plugin)[@11](https://togithub.com/11).10.0\n    -   [@emotion/cache](https://togithub.com/emotion/cache)[@11](https://togithub.com/11).10.0\n    -   [@emotion/serialize](https://togithub.com/emotion/serialize)[@1](https://togithub.com/1).1.0\n    -   [@emotion/utils](https://togithub.com/emotion/utils)[@1](https://togithub.com/1).2.0\n    -   [@emotion/weak-memoize](https://togithub.com/emotion/weak-memoize)[@0](https://togithub.com/0).3.0\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Do gradle caching manually", "number": 5825, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5825"}
{"title": "Make sure we hash gradle wrapepr propties file", "number": 5826, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5826"}
{"title": "chore(deps): update dependency @grpc/grpc-js to v1.8.14", "number": 5827, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5827", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @grpc/grpc-js (source) | 1.6.7 -> 1.8.14 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\ngrpc/grpc-node\n\n### [`v1.8.14`](): @grpc/grpc-js 1.8.14\n\n[Compare Source]()\n\n-   Fix sequencing of some events related to connectivity state changes ([#2421]())\n\n### [`v1.8.13`](): @grpc/grpc-js 1.8.13\n\n[Compare Source]()\n\n-   Fix memory leak in channelz socket tracking ([#2394]())\n\n### [`v1.8.12`]()\n\n[Compare Source]()\n\n-   Fix an occasional type error when receiving DNS updates ([#2380]())\n-   Fix ordering of events when handing requests on the server ([#2376]() contributed by [@phoenix741]())\n\n### [`v1.8.11`](): @grpc/grpc-js 1.8.11\n\n[Compare Source]()\n\n-   Avoid accumulating placeholder objects when sending many messages on a long-running stream ([#2372]())\n\n### [`v1.8.10`](): @grpc/grpc-js 1.8.10\n\n[Compare Source]()\n\n-   Fix bugs in \"pick first\" load balancing policy that caused incorrect reconnection behavior ([#2369]())\n\n### [`v1.8.9`](): @grpc/grpc-js 1.8.9\n\n[Compare Source]()\n\n-   Fix a bug where clients would continue to send pings at the original configured rate after receiving a backoff request from the server ([#2363]())\n\n### [`v1.8.8`](): @grpc/grpc-js 1.8.8\n\n[Compare Source]()\n\n-   Remove `progress` field in returned status object ([#2350]())\n-   Export `InterceptingListener` and `NextCall` types ([#2351]())\n-   Fix a bug that could cause a crash when sending messages that exceed the outgoing message buffer size while a retry is in progress ([#2349]())\n\n### [`v1.8.7`](): @grpc/grpc-js 1.8.7\n\n[Compare Source]()\n\n-   Make handling of HTTP2 session references work independent of keepalive settings ([#2337]())\n\n### [`v1.8.6`](): @grpc/grpc-js 1.8.6\n\n[Compare Source]()\n\n-   Hold a reference to transport from call to avoid premature garbage collection ([#2336]())\n\n### [`v1.8.5`](): @grpc/grpc-js 1.8.5\n\n[Compare Source]()\n\n-   Cancel deadline timer when the call ends ([#2335]())\n\n### [`v1.8.4`]()\n\n[Compare Source]()\n\n-   Fix a bug that would sometimes allow the Node process to exit even though a gRPC request is active ([#2322]())\n\n### [`v1.8.3`](): @grpc/grpc-js 1.8.3\n\n[Compare Source]()\n\n-   Fix bug that caused streams to fail early when receiving a GOAWAY ([#2319]())\n\n### [`v1.8.2`]()\n\n[Compare Source]()\n\n-   Continue keepalive pings after receiving a GOAWAY on the client ([#2308]())\n-   Fix handling of keepalive timers when the timeout is longer than the interval ([#2304]() contributed by [@nicknotfun](), included in [#2308]())\n-   Ensure the last received message is fully handled before outputting status ([#2316]())\n\n### [`v1.8.1`]()\n\n[Compare Source]()\n\n-   Implement support for the `grpc.service_config_disable_resolution` channel option ([#2277]() contributed by [@kleinsch]())\n-   Include standard headers in trailers-only responses ([#2305]())\n-   Fix a memory leak in the retry implementation ([#2306]())\n\n### [`v1.8.0`](): @grpc/grpc-js 1.8.0\n\n[Compare Source]()\n\n-   Implement retries ([specified in gRFC A6]()) ([#2243](), [#2278]())\n-   Enable servers to send trailers-only responses ([#2278]())\n-   Add server connection management options ([#2272]())\n\n### [`v1.7.3`](): @grpc/grpc-js 1.7.3\n\n[Compare Source]()\n\n-   Server performance improvements ([#2249]() contributed by [@AVVS]())\n\n### [`v1.7.2`](): @grpc/grpc-js 1.7.2\n\n[Compare Source]()\n\n-   Make the default value of the `grpc-node.max_session_memory` option `Number.MAX_SAFE_INTEGER` on the server ([#2245]())\n\n### [`v1.7.1`](): Node gRPC v1.7.1\n\n[Compare Source]()\n\n#### Changes\n\n-   Publish prebuilt binaries for Node 9\n-   Fix file permissions issue with Linux prebuilt binaries (reported in [#76]()).\n\n### [`v1.7.0`](): @grpc/grpc-js 1.7.0\n\n[Compare Source]()\n\n-   Enable outlier detection support by default ([#2221]())\n-   Expose `path` and `callEnd` event in `ServerSurfaceCall` ([#2132]() contributed by [@ajmath]())\n-   Make graceful switch happen more quickly in some cases when service config is updated ([#2199]())\n\n### [`v1.6.12`](): @grpc/grpc-js 1.6.12\n\n[Compare Source]()\n\n-   Fix typo in the error handling fix released in 1.6.11 ([#2216](), contributed by [@clww]() in [#2213]())\n\n### [`v1.6.11`]()\n\n[Compare Source]()\n\n-   Fix handling of malformed status messages ([#2210]())\n\n### [`v1.6.10`](): @grpc/grpc-js 1.6.10\n\n[Compare Source]()\n\n-   Fix a memory leak of Node http2 stream objects when cancelling streaming requests ([#2193]())\n\n### [`v1.6.9`](): @grpc/grpc-js 1.6.9\n\n-   Fix bugs in the Outlier Detection implementation ([#2173](), [#2181]())\n-   Handle errors when sending keepalive pings ([#2188]())\n-   Fix Typescript `reference` tag generation ([#2126]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "Clean pull request cache that cant be used on main", "number": 5828, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5828", "body": "All non-default branch cache is essentially useless and should be wiped."}
{"title": "FixWrongActionsCache", "number": 5829, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5829", "body": "Clean pull request cache that cant be used on main\nWrong actions cache"}
{"title": "Add shallow feature flag config", "number": 583, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/583", "body": "~Should eventually implement a config file per environment~ (done)\nAlso simplified sidebar to hide the mocked sections for now:"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/583#pullrequestreview-910496737", "body": ""}
{"comment": {"body": "Can't import config into the json file so just hide these for now\r\n\r\nWould have been a good use case here for an Unblocked annotation since we can't add comments to json files either", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/583#discussion_r827179306"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/583#pullrequestreview-911900302", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/583#pullrequestreview-911903207", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/583#pullrequestreview-911933933", "body": ""}
{"title": "Admin: cleanup profiles", "number": 5830, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5830"}
{"title": "Try dyanmic parallelization", "number": 5831, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5831"}
{"title": "chore(deps): update dependency @fortawesome/react-fontawesome to ^0.2.0", "number": 5832, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5832", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| @fortawesome/react-fontawesome | ^0.1.18 -> ^0.2.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nFortAwesome/react-fontawesome\n\n### [`v0.2.0`]()\n\n[Compare Source]()\n\n##### Added\n\n-   Support for React forwardRef if using React >= 16.3\n\n**Previous [0.1.x change log available here]()**\n\n### [`v0.1.19`]()\n\n[Compare Source]()\n\n##### Fixed\n\n-   Added missing beatFade, spinPulse, and spinReverse animations\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update dependency node-fetch to v3.3.1", "number": 5833, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5833", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| node-fetch | 3.2.10 -> 3.3.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nnode-fetch/node-fetch\n\n### [`v3.3.1`]()\n\n[Compare Source]()\n\n##### Bug Fixes\n\n-   release \"Allow URL class object as an argument for fetch()\" [#1696]() ([#1716]()) ([7b86e94]())\n\n### [`v3.3.0`]()\n\n[Compare Source]()\n\n##### Features\n\n-   add static Response.json ([#1670]()) ([55a4870]())\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "fix(deps): update dependency org.openapitools.openapistylevalidator:openapi-style-validator-lib to v1.9", "number": 5834, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5834", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| org.openapitools.openapistylevalidator:openapi-style-validator-lib (source) | 1.8 -> 1.9 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nopenapitools/openapi-style-validator\n\n### [`v1.9`]()\n\n[Compare Source]()\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update plugin com.github.node-gradle.node to v4", "number": 5835, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5835", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| com.github.node-gradle.node | 3.5.1 -> 4.0.0 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
{"title": "chore(deps): update plugin io.freefair.github.dependency-submission to v8", "number": 5836, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/5836", "body": "This PR contains the following updates:\n| Package | Change | Age | Adoption | Passing | Confidence |\n|---|---|---|---|---|---|\n| io.freefair.github.dependency-submission | 6.6.3 -> 8.0.1 |  |  |  |  |\n\n Dependency Lookup Warnings \nWarnings were logged while processing this repo. Please check the Dependency Dashboard for more information.\n\nRelease Notes\n\nfreefair/gradle-plugins\n\n### [`v8.0.1`](https://togithub.com/freefair/gradle-plugins/releases/tag/8.0.1)\n\n[Compare Source](https://togithub.com/freefair/gradle-plugins/compare/8.0.0...8.0.1)\n\n**Full Changelog**: https://github.com/freefair/gradle-plugins/compare/8.0.0...8.0.1\n\n### [`v8.0.0`](https://togithub.com/freefair/gradle-plugins/releases/tag/8.0.0)\n\n[Compare Source](https://togithub.com/freefair/gradle-plugins/compare/6.6.3...8.0.0)\n\n#### What's Changed\n\n-   Bump org.sonarsource.scanner.gradle:sonarqube-gradle-plugin from 3.5.0.2730 to 4.0.0.2929 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/714](https://togithub.com/freefair/gradle-plugins/pull/714)\n-   Bump org.sonarqube from 3.5.0.2730 to 4.0.0.2929 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/715](https://togithub.com/freefair/gradle-plugins/pull/715)\n-   Bump org.apache.maven:maven-core from 3.8.7 to 3.9.0 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/711](https://togithub.com/freefair/gradle-plugins/pull/711)\n-   Bump de.larsgrefer.sass:sass-embedded-host from 1.12.0 to 1.12.1 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/719](https://togithub.com/freefair/gradle-plugins/pull/719)\n-   Bump io.github.gradle-nexus.publish-plugin from 1.1.0 to 1.2.0 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/718](https://togithub.com/freefair/gradle-plugins/pull/718)\n-   Bump org.springframework.boot:spring-boot-dependencies from 2.7.8 to 2.7.9 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/720](https://togithub.com/freefair/gradle-plugins/pull/720)\n-   Bump org.mockito:mockito-core from 4.11.0 to 5.1.1 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/700](https://togithub.com/freefair/gradle-plugins/pull/700)\n-   Bump io.github.classgraph:classgraph from 4.8.154 to 4.8.155 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/722](https://togithub.com/freefair/gradle-plugins/pull/722)\n-   Bump mavenPluginToolsVersion from 3.7.1 to 3.8.1 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/721](https://togithub.com/freefair/gradle-plugins/pull/721)\n-   Bump org.apache.maven.plugin-tools:maven-plugin-annotations from 3.7.1 to 3.8.1 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/725](https://togithub.com/freefair/gradle-plugins/pull/725)\n-   Bump io.github.classgraph:classgraph from 4.8.155 to 4.8.156 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/730](https://togithub.com/freefair/gradle-plugins/pull/730)\n-   Bump io.github.gradle-nexus.publish-plugin from 1.2.0 to 1.3.0 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/734](https://togithub.com/freefair/gradle-plugins/pull/734)\n-   Bump io.github.classgraph:classgraph from 4.8.156 to 4.8.157 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/738](https://togithub.com/freefair/gradle-plugins/pull/738)\n-   Bump org.mockito:mockito-core from 5.1.1 to 5.2.0 by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/739](https://togithub.com/freefair/gradle-plugins/pull/739)\n-   Bump org.apache.maven:maven-core from 3.9.0 to 3.9.1 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/745](https://togithub.com/freefair/gradle-plugins/pull/745)\n-   Bump org.apache.maven:maven-plugin-api from 3.9.0 to 3.9.1 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/744](https://togithub.com/freefair/gradle-plugins/pull/744)\n-   Bump org.apache.maven:maven-model from 3.9.0 to 3.9.1 in /examples by [@dependabot](https://togithub.com/dependabot) in [https://github.com/freefair/gradle-plugins/pull/743](https://togithub.com/freefair/gradle-plugins/pull/743)\n\n**Full Changelog**: https://github.com/freefair/gradle-plugins/compare/6.6.3...8.0.0\n\n\n\n\nConfiguration\n Schedule: Branch creation - At any time (no schedule defined), Automerge - At any time (no schedule defined).\n Automerge: Disabled by config. Please merge this manually once you are satisfied.\n Rebasing: Never, or you tick the rebase/retry checkbox.\n Ignore: Close this PR and you won't be reminded about this update again.\n\n\n[ ] If you want to rebase/retry this PR, check this box\n\n\nThis PR has been generated by Mend Renovate. View repository job log here.\n"}
