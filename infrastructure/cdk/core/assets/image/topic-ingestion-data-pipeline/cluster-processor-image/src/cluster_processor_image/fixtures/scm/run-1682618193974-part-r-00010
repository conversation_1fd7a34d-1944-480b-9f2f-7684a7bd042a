{"comment": {"body": "Backup image for when this is missing?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976835438"}}
{"comment": {"body": "(Not sure if that's even possible though)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976835608"}}
{"comment": {"body": "Remove comment?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976837186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115787848", "body": ""}
{"comment": {"body": "Do we want to rename this to camelCase (callOutline) to match callRed.colorset ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976774860"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115836073", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115847755", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115855345", "body": ""}
{"comment": {"body": "\ud83e\udd2a ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976822257"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115900811", "body": ""}
{"comment": {"body": "Switching based on titles and names (this code, and 'Desktop Picture' below) seems fragile -- will this work on non-English machines?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976853710"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115910031", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115922240", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115925866", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115928916", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115930888", "body": ""}
{"comment": {"body": "Can we put some comments or something in to indicate why we're doing this? It's not clear to me", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r976873553"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1115956691", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116310956", "body": ""}
{"comment": {"body": "Agreed. I'll codify this in another PR that will generalize agora video rendering for all the other participant streams", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977148514"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116318332", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116318548", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116327961", "body": ""}
{"comment": {"body": "I tried this - as it turns out, you only receive notification events for windows your app owns. Polling the CGWIndow framework is really the only way to get information about foreign window objects", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977160903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116332878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116333194", "body": ""}
{"comment": {"body": "\ud83d\ude2d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977164508"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116335694", "body": ""}
{"comment": {"body": "Evidently not because the window level is above literally everything (maximum). I might re-write this whole thing to be a vanilla window instead of a panel though. Panels are basically unnecessary overhead and ultimately less flexible than plain NSWindows", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977166064"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116336672", "body": ""}
{"comment": {"body": "You're right that's kinda bad", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977166822"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116338185", "body": ""}
{"comment": {"body": "Argh I have to address this up the PR stack because of a refactor", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977167466"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116338550", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116338739", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116339129", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116340612", "body": ""}
{"comment": {"body": "I think this is a fair point. We could show a broken image icon or something, or maybe the window should be omitted if the image can't be captured", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977169238"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116340668", "body": ""}
{"comment": {"body": "Yup this is a weird one. For reasons I can't explain, using a regular stack causes strange alignment and sizing issues with the text descriptions. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977169280"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116341258", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1116345036", "body": ""}
{"comment": {"body": "I believe the owner is the process name, so I think so, but I'll double check", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977172450"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#pullrequestreview-1117459312", "body": ""}
{"comment": {"body": "Using `VStack`, notice that all items in the row are the same height, which results in letterboxing: \r\n<img width=\"780\" alt=\"CleanShot 2022-09-22 at 10 44 57@2x\" src=\"https://user-images.githubusercontent.com/858772/191816044-bc96c74c-14f8-4edb-9425-2a34022cca0d.png\">\r\n\r\nUsing `LazyVStack`, which evidently allows for correct vertical layout hugging:\r\n<img width=\"807\" alt=\"CleanShot 2022-09-22 at 10 47 05@2x\" src=\"https://user-images.githubusercontent.com/858772/191816448-8365fe1a-d714-43f0-8717-542e300d240c.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2955#discussion_r977933536"}}
{"title": "Add efs csi driver", "number": 2956, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2956", "body": "Updated construct module to get latest bug fixes for CDK \nCreated a new EFS stack to provision an EFS storage in each EKS VPC. This EFS storage is intended for holding copies of git checkouts \nEFS storage is configured with intelligent tiering. It moves untouched files to lower storage tier after 14 days and returns them to primary after the first access \nAdded configuration for creating EFS controller service account to eksctl config \nAdded yaml for storage class definitions \n\nAll changes above have been deployed to both Dev and prod."}
{"comment": {"body": "I have tested the EFS stuff in Dev and works as expected", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2956#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2956#pullrequestreview-**********", "body": ""}
{"title": "DROP SourceMarkModel message column", "number": 2957, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2957"}
{"comment": {"body": "Can also drop the message index, but might be easier to do that manually", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2957#issuecomment-**********"}}
{"comment": {"body": "> Can also drop the message index, but might be easier to do that manually\r\n\r\nindexes are automatically removed when you remove it's column\r\n\r\n```\r\nDROP COLUMN\r\nThis form drops a column from a table. Indexes and table constraints involving the column will be automatically dropped as well. You will need to say CASCADE if anything outside the table depends on the column, for example, foreign key references or views.\r\n```\r\n\u2014 https://www.postgresql.org/docs/7.4/sql-altertable.html", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2957#issuecomment-1244227381"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2957#pullrequestreview-1104696127", "body": ""}
{"title": "[BUG FIX] Ensure correct database context is used for tests", "number": 2958, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2958", "body": "The problem was ultimately due to incorrect transaction manager being used, seeing as we're using unique databases per test.\nI tracked it down to this line in Suspended.kt in Exposed.\nsuspend fun newScope(_tx: Transaction?): T {\n        val manager = (_tx?.db ?: db ?: TransactionManager.currentDefaultDatabase.get())?.transactionManager ?: TransactionManager.manager\n        ...\n        return TransactionScope(tx, newContext + element).body()\n    }\nBy default, the transaction db was null, the TransactionManger.currentDefaultDabase was null, so it would use whatever was set as the global TransactionManger.manager, which is based off a threadLocal value which does not work for coroutines!\nTo get around this, we are now doing several things.\n1. All database tests now set a database coroutine context, and that is used, when available, for suspendedTransaction.\n2. All client/server tests need to ensure that any ApplicationTestEngine calls are injected with the correct Database context. That is guaranteed now by a DatabaseContextPlugin.\nAll tests on my local machine now run under 3 minutes 40 seconds."}
{"comment": {"body": "> genius level unlocked \u2728\r\n> \r\n> does this mean we can get rid of the 20 transaction retries in tests?\r\n\r\nThere might be subtle bugs in a few remaining tests, but yes, that's the goal.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2958#issuecomment-1244465072"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2958#pullrequestreview-1104825537", "body": "genius level unlocked  \ndoes this mean we can get rid of the 20 transaction retries in tests?"}
{"title": "Status bar insights and PR", "number": 2959, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2959", "body": "Add Insights and PR to status bar.\nTODO: Add unblocked icons using woff\n\n"}
{"comment": {"body": "Do we pluralize \"Insight\" if there is more than one?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2959#issuecomment-1244445815"}}
{"comment": {"body": "> Do we pluralize \"Insight\" if there is more than one\r\n\r\nYes\r\n\r\n<img width=\"671\" alt=\"CleanShot 2022-09-12 at 14 23 07@2x\" src=\"https://user-images.githubusercontent.com/1553313/189761216-8f0bf03e-165e-40f1-ba06-0e08db367b42.png\">\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2959#issuecomment-1244500717"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2959#pullrequestreview-1104852856", "body": ""}
{"comment": {"body": "+ 1 so Insights is always before PR if displayed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2959#discussion_r968945233"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2959#pullrequestreview-1104955385", "body": ""}
{"title": "Minor cleanup", "number": 296, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/296", "body": "Update\nupdate"}
{"title": "Fix nginx config", "number": 2960, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2960"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2960#pullrequestreview-1104826711", "body": "thanks"}
{"title": "Fix VSCode create discussion", "number": 2961, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2961"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2961#pullrequestreview-1104903538", "body": ""}
{"title": "Try to eliminate jdk download", "number": 2962, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2962"}
{"title": "Restore archived thread when creating a reply", "number": 2963, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2963", "body": "Fixes UNB-636\nThreadUnreads are already handled correctly for archived threads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2963#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2963#pullrequestreview-**********", "body": ""}
{"title": "Add custom git provider to VSCode", "number": 2964, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2964", "body": "Fixes UNB-455\nAdds CustomGitProvider, which is a GitProvider with custom code for finding the git binary and finding git repos in the workspace.  The code is pretty heavily commented so I won't outline its behaviour much here.\nI'm considering how to add some amount of automated testing to this -- it should be possible since this doesn't really depend on VSCode at all.  Maybe we could add canned zip files with sample folder structures that we can run off of..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2964#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2964#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2964#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2964#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2964#pullrequestreview-**********", "body": ""}
{"title": "Strip signatures from thread titles when updating the first message", "number": 2965, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2965"}
{"title": "Upgrade silently in the background then show notification", "number": 2966, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966", "body": "Summary\nUpgrades now happen automatically as soon as the download is finished. The \"post-upgrade\" launch generates a system notification that an upgrade has occurred. Click on that notification displays the upgrade dialog with markdown text. If the user ignores the notification, the hub will display the upgrade content until the continue button is clicked. When the Hub is in the \"Just Upgraded\" state, the menu bar icon pulses gently to let the user know something happened.\nPulsing upgrade icon\n\nIgnore notification flow\n\nClick notification flow\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1106347711", "body": ""}
{"comment": {"body": "I originally tried using a single view with an `ObservableObject` containing a published enum value to switch between the different states. It kind of worked, except that the animation state value went directly to `1.0` and didn't animate. Couldn't figure out why but I suspect SwiftUI has some kind of state optimization path when conditionals are used.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r970010534"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1106447605", "body": ""}
{"comment": {"body": "It's not urgent, manually swapping host views feels like the wrong approach -- if we're using a hosting view, and thus using SwiftUI for the content (which is great), I think we should deal with state transitions inside the View itself, since that's the kind of thing SwiftUI is particularly good at.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r970080057"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1106450069", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1106450200", "body": ""}
{"comment": {"body": "Yes I did try this, but for some reason the animated state always shot to 1.0 and stopped animating. I'm not clear yet about why this happens but I think it has to do with conditionals in the view builder. The only way I could get SwiftUI to deal with state was to remove the conditional, but then this required two different types. At that point it was easier and more consistent to swap the NSView(s)\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/be7a1bd5-2a45-44ac-8593-d38c9c4166c7?message=ac587f7a-cd11-4fd7-8fa1-6a3527f6b7b7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r970081919"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1106450756", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1106455422", "body": ""}
{"comment": {"body": "Instead of manually adding/removing hosting views, would it make sense to have this logic live *within* a SwiftUI view?\r\n\r\naka a single UnblockedMenuBar that takes in a state/props for hasUpgrade, hasUnreads, etc...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r970085688"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1106459355", "body": ""}
{"comment": {"body": "Haha basically what Matt said here https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r970080057", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r970088444"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1106469955", "body": ""}
{"comment": {"body": "Yeah we're all thinking the same thing. Here's what I was doing previously that didn't work. The animation value just stuck at 1.0 whenever the status was set to `upgrade`:\r\n\r\n```swift\r\nenum MenuBarStatus {\r\n    case read\r\n    case unread\r\n    case upgrade\r\n}\r\n\r\nclass MenuStatusObservable: ObservableObject {\r\n    @Published var status: MenuBarStatus = .read\r\n}\r\n\r\nstruct UnblockedMenuBarItem: View {\r\n    @ObservedObject var menuStatusObservable: MenuStatusObservable\r\n    @State var scale: CGFloat = 0.7\r\n    \r\n    var body: some View {\r\n        ZStack {\r\n            if menuStatusObservable.status == .upgrade {\r\n                Image(imageName)\r\n                    .resizable()\r\n                    .frame(width: 14, height: 16)\r\n                    .reverseMask {\r\n                        Circle()\r\n                            .frame(width: outerClipSize * scale, height: outerClipSize * scale)\r\n                            .position(x: 12, y: 4.5)\r\n                    }\r\n                    .overlay(\r\n                        ZStack {\r\n                            Circle()\r\n                                .frame(width: innerDotSize * scale, height: innerDotSize * scale)\r\n                                .position(x: 12, y: 4.5)\r\n                                .foregroundColor(pulseColor)\r\n                                .blur(radius: 3)\r\n                            Circle()\r\n                                .frame(width: innerDotSize * scale, height: innerDotSize * scale)\r\n                                .position(x: 12, y: 4.5)\r\n                                .foregroundColor(pulseColor)\r\n                        }\r\n                    )\r\n            } else {\r\n                Image(imageName)\r\n                    .resizable()\r\n                    .frame(width: 14, height: 16)\r\n            }\r\n        }\r\n        .frame(width: 25, height: 20)\r\n        .onAppear {\r\n            withAnimation(.easeInOut(duration: 1.2).repeatForever(autoreverses: true)) {\r\n                scale = 1.0\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Then in the init method do this:\r\nlet menuStatusObservable = MenuStatusObservable()\r\n\r\nprivate init() {\r\n    if let button = statusItem?.button {\r\n            button.setFrameSize(CGSize(width: 25, height: 25))\r\n            button.addSubview(NSHostingView(rootView: UnblockedMenuBarItem(menuStatusObservable: menuStatusObservable)))\r\n    }\r\n    ... etc\r\n}\r\n\r\n\r\n// Then to switch states do this:\r\nfunc updateIcon() {\r\n        Task {\r\n            hasUpgrade = UserDefaults.standard.bool(forKey: \"JustUpgraded\")\r\n            await MainActor.run {\r\n                if hasUpgrade {\r\n                   menuStatusObservable.status = .upgrade\r\n                } else if hasUnreads {\r\n                   menuStatusObservable.status = .unread\r\n                } else {\r\n                   menuStatusObservable.status = .read\r\n                }\r\n            }\r\n        }\r\n    }\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r970096159"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1106480688", "body": ""}
{"comment": {"body": "The annoying complexity to this is that it's not purely SwiftUI, but rather a SwiftUI view hosted in an AppKit view (`NSStatusBarButton`). For this reason we can't deal purely in state, because in this case the \"state\" is owned by the service `class` and not the `SwiftUI` view. So it has to be an `@ObservedObject`. The combination of observed object and conditional borks the animation for unknown reasons", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r970103792"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1108356432", "body": ""}
{"comment": {"body": "Test test test This is a test http://google.com/ something else http://blah.com/ something", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r971403105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#pullrequestreview-1109831713", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/1ebbfdad-9c04-4739-8858-3e4ab6413e99)\n\nTest test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/378dfbec-678d-4919-8eab-afa35de3806c?message=dffe3e1e-60f7-42d8-b41f-fc65a08e33e9).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2966#discussion_r972417546"}}
{"title": "Add 'Start Discussion' command to editor context menu", "number": 2967, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2967", "body": "Fixes UNB-626\n@benedict-jw :\n* Where in the menu should this appear?  I put it at the bottom, but we can put it in a different location if we want to\n* The label is a default from our existing command -- do we want Unblocked: at the beginning?\n"}
{"comment": {"body": "I'd say yes, Unblocked: even in the lightbulb menu now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2967#issuecomment-1244932039"}}
{"comment": {"body": "> I'd say yes, Unblocked: even in the lightbulb menu now\r\n\r\nOK I'll add it to this PR.  This is what it looks like now.\r\n\r\n<img width=\"365\" alt=\"Screen Shot 2022-09-14 at 2 38 13 PM\" src=\"https://user-images.githubusercontent.com/2133518/190267380-db78281f-8e2a-427a-89c7-4394abe1a475.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2967#issuecomment-1247328211"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2967#pullrequestreview-1108255337", "body": ""}
{"title": "update", "number": 2968, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2968"}
{"title": "Try setup java cache", "number": 2969, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2969"}
{"title": "Get additional fields required to create Identity models", "number": 297, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/297", "body": "We're going to create Identity models for each comment author that doesn't already exist in our system, and then a TeamMember which will be used for the author field of the message object. We need some additional fields from the GitHub API for the Identity model, this PR grabs those fields."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/297#pullrequestreview-878105126", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/297#pullrequestreview-878105448", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/297#pullrequestreview-878106674", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/297#pullrequestreview-878107336", "body": ""}
{"title": "Render Client Version Breakdown", "number": 2970, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2970", "body": ""}
{"title": "Version adoption metrics cleanup", "number": 2971, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2971"}
{"title": "Remove completed migrations", "number": 2972, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2972"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2972#pullrequestreview-1105160330", "body": ""}
{"title": "Update VSCode tooltips", "number": 2973, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2973", "body": "Fixes UNB-606\n\nRemove the Unblocked logo\nAdd a 'Powered by Unblocked' label to the end of the stacked label (for single threads), or below all other content (for multi-threads).\n\nSingle-thread:\n\nMulti-thread:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2973#pullrequestreview-1106276734", "body": ""}
{"title": "Ensure eips ahve names", "number": 2974, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2974"}
{"title": "Restore discussion on dashboard", "number": 2975, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975", "body": "Updated CSS for detailView to support banners.\nUpdated CSS for Icon and general padding to discussion thread.\nAdded restoration banner to discussion thread.\nAdded restoration button to summary discussion threads. \n\n\n\n\n\n"}
{"comment": {"body": "Did you add an info icon? I didn't have one in the designs because I didn't think it really needed it. What do you think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#issuecomment-1245891101"}}
{"comment": {"body": "> Did you add an info icon? I didn't have one in the designs because I didn't think it really needed it. What do you think?\r\n\r\nDidn't even notice. Is part of our default banner component. Will remove.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#issuecomment-1245928064"}}
{"comment": {"body": "Updated:\r\n<img width=\"1385\" alt=\"CleanShot 2022-09-13 at 13 36 56@2x\" src=\"https://user-images.githubusercontent.com/1553313/190004339-b8468d04-310a-40d6-ad83-2e7e7fe6656e.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#issuecomment-1245930186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1106781230", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1106782102", "body": ""}
{"comment": {"body": "what\u2019s the reason for this? is this change okay with all the other views? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#discussion_r970329669"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1106783599", "body": "I think you need to update the icon in the CreateKnowledge view in vscode (ie it references the DiscussionDraft icon which was renamed  we should also be using the regular blue icon there now)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1107915854", "body": ""}
{"comment": {"body": "Location of this has changed to within `detail_layout_view__header_content` so the value needs to change to keep the same overall header height.\r\n\r\nI'd like to actually remove this entirely but keeping it around for Settings. This isn't actually used in the DiscussionThread view as the headers are always larger than 46px.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#discussion_r971095218"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1107916632", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1111120192", "body": ""}
{"comment": {"body": "What do these look like?  I don't see them in the screenshots anywhere?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#discussion_r973321138"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1111120794", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1111269221", "body": ""}
{"comment": {"body": "Just greyed out versions of the icons.\r\n\r\n<img width=\"526\" alt=\"CleanShot 2022-09-16 at 14 45 15@2x\" src=\"https://user-images.githubusercontent.com/1553313/190798074-bae91fff-46b1-40fe-88c3-508a6716f0ae.png\">\r\n<img width=\"617\" alt=\"CleanShot 2022-09-16 at 14 45 07@2x\" src=\"https://user-images.githubusercontent.com/1553313/190798097-220000eb-6643-4417-bdbd-e92c3091ef43.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#discussion_r973428419"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1111287746", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2975#pullrequestreview-1111313084", "body": ""}
{"title": "Update tool cache", "number": 2976, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2976"}
{"title": "Remove migration", "number": 2977, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2977"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2977#pullrequestreview-1106522360", "body": ""}
{"title": "Fix scrolling", "number": 2978, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2978", "body": "Fix scrolling issue in unresolved code block"}
{"comment": {"body": "\r\nhttps://user-images.githubusercontent.com/1553313/190022254-49780f93-5e7c-43ae-acc2-1d945756d290.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2978#issuecomment-1246022167"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2978#pullrequestreview-1106533442", "body": ""}
{"title": "Fix lint", "number": 2979, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2979"}
{"title": "Move MessageView container to shared and add to vscode UI", "number": 298, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298", "body": "Move the MessageView to the shared/ directory and create a vscode styled version\n*Note that the CodeBlock components are too different client per client, so I moved that to be passed into the component as children (so the MessageView component doesn't need to understand the CodeBlock properties)\nshared (barebones):\n\nweb:\n\nvscode:"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-878167683", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-878172364", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-878172787", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-878173165", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-878173907", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-878177063", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-878217993", "body": ""}
{"comment": {"body": "We can remove this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#discussion_r803213583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-878218057", "body": ""}
{"comment": {"body": "Oh... this is to make jest happy?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#discussion_r803213640"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-878222652", "body": ""}
{"comment": {"body": "I suspect this class will eventually be taking in the same model as MessageEditor -- we should probably figure that out soon", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#discussion_r803217289"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-879173233", "body": ""}
{"comment": {"body": "Yep", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#discussion_r803911607"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/298#pullrequestreview-879212237", "body": ""}
{"title": "Move to primed docker image", "number": 2980, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2980"}
{"title": "Remove ls", "number": 2981, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2981"}
{"title": "Disable specific refresh retry", "number": 2982, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2982", "body": "Running into interesting bug where API requests are blocked indefinitely by a refresh auth promise.\nThis occurs since refresh auth process had it's own retry logic which could loop forever.\nRemove refresh auth's retry logic and depend on generic API retry logic."}
{"comment": {"body": "This is a risky change. Merge after next release to allow for dogfooding.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2982#issuecomment-1246059383"}}
{"comment": {"body": "Release deployed. Let's get this in to dogwood", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2982#issuecomment-1248309169"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2982#pullrequestreview-1109717108", "body": ""}
{"title": "Fix updated state bug", "number": 2983, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2983", "body": "Bug\nPrevious published version was racing against update to latest version info causing the update text to be prematurely dismissed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2983#pullrequestreview-1107816372", "body": ""}
{"title": "Backfill mentions for ingested comments", "number": 2984, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2984", "body": "Need to migrate existing comments to create mentions blocks"}
{"title": "Use s3 pagination apis", "number": 2985, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2985"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2985#pullrequestreview-1106599584", "body": ""}
{"title": "Engagement score in admin web", "number": 2986, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2986", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2986#pullrequestreview-1106708598", "body": "Rad"}
{"title": "Optimize source point snippets and file paths", "number": 2987, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2987", "body": ""}
{"title": "Revert \"Engagement score in admin web\"", "number": 2988, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2988", "body": "Reverts NextChapterSoftware/unblocked#2986\nNeeds work."}
{"title": "Cache ThreadInfos", "number": 2989, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2989", "body": "Fixes UNB-591\nCache ThreadInfos so that we aren't constantly thrashing the fetchThreads API whenever people change files."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2989#pullrequestreview-**********", "body": "Nice tests!"}
{"title": "Managing root (Management) account components (DNS and IAM) using CDK", "number": 299, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/299", "body": "Added api.getunblocked.com user friendly alias records for apiservice.us-west-2.prod.getunblocked.com\nDeleted manually created dashboard.getunblocked.com alias and re-created it in CDK code\nAdded ability to create IAM groups, attach policies for groups and add users to each group\nRe-created  all existing manually created IAM groups and their respective policies relating to automation work (admin groups are still manually managed)\nDeployed all these changes to all envs\n\nBesides some cosmetic code cleanup, I think we are ready to start implementing CI/CD for infra...yaaay!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/299#pullrequestreview-*********", "body": ""}
{"comment": {"body": "We should probably add a linter for these files.\r\n else if should be after bracket.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/299#discussion_r804095037"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/299#pullrequestreview-879482938", "body": ""}
{"comment": {"body": "Hmm the code formatter didn't pick it up. I haven't run the linter on it for weeks. :P \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/299#discussion_r804114146"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/299#pullrequestreview-*********", "body": ""}
{"title": "Update spin keyframe", "number": 2990, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2990", "body": "Slight update to spinner keyframe?\nCSS animations for spinners are already the \"best\" technique possible as it takes advantage of GPU acceleration. Using SVGs, animations, should be more taxing...\nOf course, if you're GPU bound and CPU bound, everything is going to be slow so the only alternative would be to not have a spinner.\n\nTested this with GPU acceleration disabled. \n\nBefore:\n\nAfter:\n\nBut not sure if that's due to the changes or just random variance..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2990#pullrequestreview-1106776429", "body": ""}
{"title": "Update readme for java tool caching", "number": 2991, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2991"}
{"title": "SourceMark Engine Performance Optimization", "number": 2992, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2992", "body": "Motivation\nGiven a repo that is fully up to date wrt the source marks  meaning the latest\npoint has been calculated, upstreamed, and downloaded for all marks  then why does\nfull recalculation take so long? Something is wrong here because the only work needed\nis to rebuild a point cache per file from the downloaded dataset.\nChanges\nTurns out that we were not leveraging the pre-calculated points very much. Now we\ncheck to see if the HEAD commit has been previously calculated for a point based on\nthe point file-content hash.\nResults\nHard to estimate from the debugger environment, but looks like ~2X faster."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2992#pullrequestreview-1106948017", "body": ""}
{"comment": {"body": "dropping because my CPU is barely used", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2992#discussion_r970436625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2992#pullrequestreview-1106948808", "body": ""}
{"comment": {"body": "this was dumb \u2014 we can skip lots of work here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2992#discussion_r970437181"}}
{"title": "Remove no longer needed migrator", "number": 2993, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2993", "body": "This reverts commit 27e85bdc52b767b7f3551752918667249fc97a2e."}
{"title": "[BREAKS API ON MAIN] Delete snippet libs", "number": 2994, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2994", "body": "Bye bye snippets prototype."}
{"title": "Puts video app and hub into same app group to share container", "number": 2995, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2995"}
{"title": "Bandaid for handling very large diffs in SM engine", "number": 2996, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996", "body": "explicitly add --no-pager\nremove spammy log\n\nBetter solutions here:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#pullrequestreview-1108122464", "body": ""}
{"comment": {"body": "This seems to behave OK?  I'd have imagined allocating 100MB for every git execution (which we call a *lot*) would cause a lot of thrashing, but I guess somehow it works out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#discussion_r971237659"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#pullrequestreview-1108122596", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#pullrequestreview-1108122944", "body": ""}
{"comment": {"body": "I do wonder if we run stuff in parallel (Promise.all etc) we could easily allocate GBs...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#discussion_r971238023"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#pullrequestreview-1108127358", "body": ""}
{"comment": {"body": "Fair.\n\n\n\nMost Git commands take less than 25 ms, but can take up to 500 ms. So there would be transient spikes, but perhaps not large peaks caused by concurrency since the window for overlap is low.\n\n\n\nI'll see what happens locally, and will abandon if memory jumps.\n\n--\n\nComment edited using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a1f68503-62d5-4754-8887-029c7fd5edf9?message=a9828672-1e27-4058-8939-dcd931c96118).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#discussion_r971240979"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#pullrequestreview-1108268510", "body": ""}
{"comment": {"body": "Tests are ok:\n\n- no difference in peak memory before/after this change afaict\n\n- only ok, because the baseline memory used by the extension is actually fairly significant at about 1.7 GB. this almost certainly OOMs for larger repos.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a1f68503-62d5-4754-8887-029c7fd5edf9?message=ecb223e9-de68-44e1-a9b2-97ad25d6b7f0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#discussion_r971338210"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#pullrequestreview-1108268790", "body": ""}
{"comment": {"body": "Sounds good!\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a1f68503-62d5-4754-8887-029c7fd5edf9?message=8fa963ac-9a6a-485e-b7b8-85391bdb28ca).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2996#discussion_r971338413"}}
{"title": "Add slack service", "number": 2997, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2997"}
{"title": "Archive threads started by team member where ignoreThreads is true", "number": 2998, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2998", "body": "Main changes:\n\nAdds a TeamMember.ignoreThreads property and the ability to toggle this field from the admin console\nAdds a ArchivedReason.IgnoredAuthor enum to be used when archiving a thread created by a team member where ignoreThreads is true\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2998#pullrequestreview-1108248264", "body": "cool"}
{"comment": {"body": "inconsistent label. call both \"Threads Ignored\" or both \"Ignore Threads\" please", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2998#discussion_r971333679"}}
{"title": "Additional context for blue bubble snippets", "number": 2999, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999", "body": "When creating an insight bubble from VSCode, inject additional context below and after.\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1108307635", "body": ""}
{"comment": {"body": "@richiebres Could be causing bug that shifts source points.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r971367249"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1108311220", "body": ""}
{"comment": {"body": "Check out this:\n\n\n\nhttps://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1c381df0-7d23-4548-bba6-be544c97f015?message=65da2556-2247-4add-8d48-572972373725\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=3df7d107-dbd8-4b94-9492-aee8d71683b6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r971370172"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1108324569", "body": ""}
{"comment": {"body": "Pushed a fix to your branch. Lmk if it works?\n\nhttps://github.com/NextChapterSoftware/unblocked/pull/2999/commits/30b72a916fed2cf899e34a1e010074253f565f55\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=3e1e7b7c-f64f-407a-9a03-c8dabe4ecf16).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r971380246"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1108355721", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1108360321", "body": ""}
{"comment": {"body": "Did not work :( ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r971405862"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1108360964", "body": ""}
{"comment": {"body": "What! You have an example thread id/link from DEV?\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=f919c3d7-4887-40a8-802d-68211d06feac).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r971406312"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1108530794", "body": ""}
{"comment": {"body": "needs server fix\n\nhttps://github.com/NextChapterSoftware/unblocked/pull/3006\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=8dec72dd-2dbf-486f-9ea2-29c7050df37b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r971529604"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1108544705", "body": ""}
{"comment": {"body": "Should be fixed now. try again.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r971539717"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109414030", "body": ""}
{"comment": {"body": "Just tried this and no luck.\r\nhttps://admin.dev.getunblocked.com/teams/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/threads/f0dc2c60-6fc8-4b88-8c23-dac09265281d", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r972137660"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109416039", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109500459", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109501512", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109515311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109519445", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109522166", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/a3a07b26-964e-4412-9061-66c8e2cf88ca)\n\nThe problem is this first point is not marked as trusted. Can you confirm that the flag is set on the wire, with Proxyman?\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=83f64a21-7ca7-4327-b440-6f65fb2ba14f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r972204539"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109555751", "body": ""}
{"comment": {"body": "```\r\n{\r\n  \"title\": \"test2\",\r\n  \"message\": {\r\n    \"id\": \"cbd3a308-decb-4d64-9138-56b99b54016a\",\r\n    \"threadId\": \"0e6924f0-01c1-41be-9cc2-6d50f2d6ad55\",\r\n    \"messageContent\": {\r\n      \"content\": \"CgwqCgoICgYKBGFzZGYSATE=\",\r\n      \"version\": \"1\"\r\n    },\r\n    \"mentions\": [],\r\n    \"sourcemarks\": [\r\n      {\r\n        \"id\": \"08786b9d-1a7e-47b4-aeb7-139b144f57b5\",\r\n        \"repoId\": \"7fce6ebb-5989-48e7-9af0-fff594769a8a\",\r\n        \"sourcePoint\": {\r\n          \"isOriginal\": true,\r\n          \"commitHash\": \"71123a50c4a6ed11358dd1662d75e88d40d014ca\",\r\n          \"filePath\": \"web-extension/src/auth/AuthStore.ts\",\r\n          \"fileHash\": \"bebb752baa0c4778a30691b4e8358778c2526b97\",\r\n          \"lines\": {\r\n            \"start\": 22,\r\n            \"end\": 22\r\n          },\r\n          \"columnStart\": 7,\r\n          \"columnEnd\": 7,\r\n          \"snippet\": {\r\n            \"start\": 19,\r\n            \"lines\": [\r\n              \"} from '@shared-stores';\",\r\n              \"import { PromiseProxyClientFn, PromiseProxyTraits } from '@shared-proxy';\",\r\n              \"import { getWebServiceTransportBackground, getWebServiceTransportClient } from '@transport';\",\r\n              \"import { API } from '@api';\",\r\n              \"import { logger } from '@shared-web-utils';\"\r\n            ]\r\n          }\r\n        }\r\n      }\r\n    ]\r\n  },\r\n  \"threadParticipants\": []\r\n}\r\n```\r\n\r\nBody for one of the create POST requests.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r972227946"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109556367", "body": ""}
{"comment": {"body": "https://admin.dev.getunblocked.com/teams/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/threads/0e6924f0-01c1-41be-9cc2-6d50f2d6ad55", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r972228384"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109587830", "body": ""}
{"comment": {"body": "ok, so the body is still missing the isTrusted flag. so somewhere in TS API caller\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=9a0e4861-87c3-4c76-bf41-9e5e26113756).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r972250356"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109595690", "body": ""}
{"comment": {"body": "So that should be set true on threadCreation?\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=d0cc77af-f851-4217-aab2-b5be7abb0ebf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r972255698"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109595938", "body": ""}
{"comment": {"body": "ya\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=25525917-7e63-44d4-a966-8b21330274b8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r972255876"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109596404", "body": ""}
{"comment": {"body": "I thought that is what this did\n\nhttps://github.com/NextChapterSoftware/unblocked/pull/2999/commits/30b72a916fed2cf899e34a1e010074253f565f55\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=50f9e525-1073-4c62-83d8-e74e7fe90731).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r972256220"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1109612888", "body": ""}
{"comment": {"body": "That did the trick!\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/38f6a844-6343-498a-b2ff-e782d97c70bb?message=9e3c1bc9-4cc3-41e1-8f30-e3c6c771918c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#discussion_r972267421"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1112902099", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2999#pullrequestreview-1112903429", "body": ""}
{"title": "Add Jest test", "number": 3, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3"}
{"title": "Sass", "number": 30, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30", "body": "Updating web codebase to use Sass.\nUsing GH Primer () to influence variables."}
{"comment": {"body": "> lgtm, couple of elements/styles can likely be refactored out into their own components\r\n\r\nFeel free to refactor them out as you see fit.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#issuecomment-1012447529"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-1124604355", "body": ""}
{"comment": {"body": "What a Sassy PR this is", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r983048106"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851042504", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851099318", "body": ""}
{"comment": {"body": "Should this be shared between the webpack cfg and storybook cfg in some way?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r783512806"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851102316", "body": ""}
{"comment": {"body": "This is an interesting case -- we're referencing class definitions across components here and that feels like it might be risky.  If the size is an option on `Icon` should it be a prop on it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r783515100"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851104675", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851108562", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851109192", "body": ""}
{"comment": {"body": "In this case, I'm not *actually* referencing the class definition.\r\n\r\nThe Icon component takes in a classname and I've passed in the classname `icon` which is referenced in this scss.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r783520409"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851109323", "body": ""}
{"comment": {"body": "But I agree on the point. Not 100% sure what should be done here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r783520515"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851115117", "body": ""}
{"comment": {"body": "Any ideas on a clean way to do this?\n\nIt seems like this is only necessary for certain rules from the base webpack config.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r783524998"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851127161", "body": ""}
{"comment": {"body": "wouldnt the view just give the Icon component a prop that specifies its size? then the component itself will know how to size itself?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r783534227"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851143693", "body": ""}
{"comment": {"body": "So something like this?\r\n\r\n```\r\nexport const Icon = (props) => { \r\n    return (\r\n        <img style={{width: `${props.width}`}}/>\r\n    )\r\n}\r\n```\r\n\r\nWouldn't this allow for arbitrary values which we're trying to avoid with our sass constants?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r783546964"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851148664", "body": ""}
{"comment": {"body": "another way would be via classnames, esp if we're sticking to a set of icon sizes \r\n\r\nso \r\n\r\n```\r\ninterface Props {\r\n   size: small | medium | large;\r\n   // ... other stuff\r\n}\r\n\r\nexport const Icon = ({ size = 'small' }) => { \r\n    const classes = ClassNames({\r\n        icon: true,\r\n        [`icon__${size}`]: !!props.size,\r\n        // ... other stuff\r\n    })\r\n    return <img className={classes} />;\r\n}", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r783550877"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-851150072", "body": ""}
{"comment": {"body": "where ClassNames is something like \r\n```\r\nexport const ClassNames = (args: { [key: string]: boolean }) => {\r\n    let result = '';\r\n    for (const key in args) {\r\n        if (!!args[key]) {\r\n            result = result.concat(` ${key}`);\r\n        }\r\n    }\r\n    return result;\r\n};", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r783551910"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-852073585", "body": ""}
{"comment": {"body": "We'll need some predefined sizes from @benedict-jw for this.\r\nLet's get this in as a separate PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r784207018"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-852085467", "body": ""}
{"comment": {"body": "fine with me as long as it doesnt get dropped. there are several places where we can refactor out parts into their own components but I understand we don't want to keep blowing up this PR\r\n\r\nbtw is this gonna be the same CodeBlock component we use for vscode? Or will parts of it be reused? I'm not sure what we decided to do re: shared components, would the vscode view just reference the web components file? \r\n\r\nEdit: If we won't be sharing the components themselves, how about the styles? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r784215423"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-852094922", "body": ""}
{"comment": {"body": "I spoke to Ben about this a bit. \r\nIt may be difficult to share components between the two clients since styles are heavily influenced by VSCode. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#discussion_r784222083"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/30#pullrequestreview-852153225", "body": "lgtm, couple of elements/styles can likely be refactored out into their own components"}
{"title": "Fix zally", "number": 300, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/300"}
{"title": "Add \"Ignored Author\" trait to threads page", "number": 3000, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3000"}
{"title": "Remove migration", "number": 3001, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3001"}
{"title": "Set PullRequest.ingestionInProgress", "number": 3002, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3002"}
{"title": "Add slack client apis", "number": 3003, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3003", "body": "This pr copies over some utilities I created for slack api usage using Slack java sdk.\nIt also cleans up how we're doing webhook posts such that we are now using Slack's models and apis.\nWe should be using the canonical Payload model that slack provides for webhook posts which has a dsl! for block generation.\nShould make richie happy there...\nConfirmed new webhook apis are working as I sent myself one. :)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3003#pullrequestreview-1108350250", "body": ""}
{"comment": {"body": "dsl baby!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3003#discussion_r971398290"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3003#pullrequestreview-1108354416", "body": ""}
{"comment": {"body": "The deprecation is because slacks want us to use incoming webhooks via apps rather than integrations (we're using the latter).\r\nChannel, user, iconUrl are not available via app based incoming webhooks, so they are force-functioning this transition.\r\n\r\nFor now, we'll just use the deprecated channel, username, iconUrl of PayloadBuilder.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3003#discussion_r971401470"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3003#pullrequestreview-1108358128", "body": "cool"}
{"title": "update", "number": 3004, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3004"}
{"title": "Fix message editor buttons", "number": 3005, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3005", "body": "The editor toolbar buttons don't work.  This regressed here: https://github.com/NextChapterSoftware/unblocked/pull/2927\nThe issue is that the @-mention dropdown captures mouse input regardless of whether it is open or not.  I don't know if this solution is optimal but it seems to work."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3005#pullrequestreview-1109684825", "body": ""}
{"title": "Persist isTrusted field for new blue insight points", "number": 3006, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3006"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3006#pullrequestreview-1108530736", "body": ""}
{"title": "Source marks are archived when threads are archived", "number": 3007, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3007", "body": "fixes \nFollow-up: migration to backfill isArchived and isDeleted on SM. can happen in the follow-up - we can ship with this and everything will still work fine.\n\nImpact\nExperience when you attempt to view a thread that has been archived from Search.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3007#pullrequestreview-1109680989", "body": ""}
{"comment": {"body": "Not really used by the client right now, but good for debugging.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3007#discussion_r972314060"}}
{"title": "Video Chat IPC Spec", "number": 3008, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008", "body": "This spec defines 3 RPC interfaces:\n1. Join an existing channel\n2. Start a new channel\n3. Listen for video events\nClients should launch the video app and then connect to the event stream before issuing join/new channel requests."}
{"comment": {"body": "Do we need a \"get current state\" style API?  I'm thinking of the case where a VSCode workspace starts up, and notices that the video app is already running...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008#issuecomment-1248527653"}}
{"comment": {"body": "> Do we need a \"get current state\" style API? I'm thinking of the case where a VSCode workspace starts up, and notices that the video app is already running...\r\n\r\nYup I considered that, but couldn't think of what the clients would get from that information that they couldn't get from the Join/NewChannel APIs. But maybe it would make sense to immediately send back `CurrentConnectedChannel` or `NoChannelConnected` events through the stream API?\r\n\r\nOr as you suggest maybe we make it a separate call so it's explicit and doesn't pollute the real-time event stream?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008#issuecomment-1248535795"}}
{"comment": {"body": "> > Do we need a \"get current state\" style API? I'm thinking of the case where a VSCode workspace starts up, and notices that the video app is already running...\r\n> \r\n> Yup I considered that, but couldn't think of what the clients would get from that information that they couldn't get from the Join/NewChannel APIs. But maybe it would make sense to immediately send back `CurrentConnectedChannel` or `NoChannelConnected` events through the stream API?\r\n\r\nOne alternative is that we could create fully descriptive state object. So instead of sending events down the wire, events are inferred through changes in state. It means state is entirely contained within the video app and not reconstructed in the clients based on events. But I'm not a huge fan of this because each client might have different stateful needs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008#issuecomment-1248539341"}}
{"comment": {"body": "> One alternative is that we could create fully descriptive state object. So instead of sending events down the wire, events are inferred through changes in state. It means state is entirely contained within the video app and not reconstructed in the clients based on events. But I'm not a huge fan of this because each client might have different stateful needs.\r\n\r\nI think this is a better model.  It deals with duplicate/missing states and events better.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008#issuecomment-1248550956"}}
{"comment": {"body": "\r\n\r\n\r\n> > One alternative is that we could create fully descriptive state object. So instead of sending events down the wire, events are inferred through changes in state. It means state is entirely contained within the video app and not reconstructed in the clients based on events. But I'm not a huge fan of this because each client might have different stateful needs.\r\n> \r\n> I think this is a better model. It deals with duplicate/missing states and events better.\r\n\r\nThat's a good point. Missing real-time events like channel shutdown could be problematic. I'll refactor the stream to be a state change stream instead of an event stream. That way clients can assume that if they're connected to the stream they will reliably receive state changes, and if the connection drops they can reconnect and reliably get an up-to-date view of state. \r\n\r\nThe other two APIs (Join/New) should still be separate APIs. We should avoid bidirectional streams or we'll have to encode messageIds etc.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008#issuecomment-1248556651"}}
{"comment": {"body": "> I think this is a better model. It deals with duplicate/missing states and events better.\r\n\r\nUpdated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008#issuecomment-1248565901"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008#pullrequestreview-1109686840", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008#pullrequestreview-1109689270", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3008#pullrequestreview-1109947982", "body": ""}
{"title": "Share app group between Hub and Video App", "number": 3009, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3009", "body": "This is necessary so that the Hub can sniff the IPC channel for the video chat app"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3009#pullrequestreview-1109521354", "body": ""}
{"title": "Fix logging", "number": 301, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/301", "body": "Was not initializaing logs api correctly."}
{"title": "[BREAKS API ON MAIN] Remove 'allof' in API", "number": 3010, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3010"}
{"comment": {"body": "Huggable offence!!!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3010#issuecomment-1248655395"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3010#pullrequestreview-1109688209", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3010#pullrequestreview-1109695149", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3010#pullrequestreview-1109700683", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3010#pullrequestreview-1109859600", "body": ""}
{"title": "Ensure we validate that openapi spec is not using nasty ops", "number": 3011, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3011", "body": "We've hit constant problems with code generation related to combine operations (allOf, oneOf, anyOf).\nThis pr stops that insanity via adding rules to the openapi linter.\nWe have two linters:\n1. Zally (this guy does general styling checks (i.e. do we have tags/descriptions/etc) and is casing correct. It is not extensible.\n2. OpenApi Linter (this guy is extensible and has some additional checks that zally doesn't do).\n```\n\nSECTION COUNT\n\nOperations   36\nModels     11\n\nOperations\nERROR in Operation GET /teams/{teamId}/threads/mine 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation GET /teams/{teamId}/threads/mine 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation GET /teams/{teamId}/threads/mine 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation GET /teams/{teamId}/threads/byPullRequests 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation GET /teams/{teamId}/threads/byPullRequests 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation GET /teams/{teamId}/threads/byPullRequests 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation POST /teams/{teamId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation POST /teams/{teamId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation POST /teams/{teamId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation GET /teams/{teamId}/threads/recommended 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation GET /teams/{teamId}/threads/recommended 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation GET /teams/{teamId}/threads/recommended 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation GET /teams/{teamId}/threads/archived 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation GET /teams/{teamId}/threads/archived 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation GET /teams/{teamId}/threads/archived 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation GET /teams/{teamId}/threads/search 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation GET /teams/{teamId}/threads/search 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation GET /teams/{teamId}/threads/search 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation GET /teams/{teamId}/threads/{threadId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation GET /teams/{teamId}/threads/{threadId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation GET /teams/{teamId}/threads/{threadId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/info 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/info 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/info 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation PUT /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation PUT /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation PUT /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation POST /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Operation POST /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Operation POST /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Operation GET /teams/{teamId}/videoRecordings/{recordingId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/videoChannelParticipantId)\nERROR in Operation PUT /teams/{teamId}/videoRecordings/{recordingId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/videoChannelParticipantId)\nERROR in Operation PUT /teams/{teamId}/videoRecordings/{recordingId}/stop 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/videoChannelParticipantId)\n\nModels\nERROR in Model 'ThreadInfo', property 'property/sourceMark', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Model 'ThreadInfo', property 'property/rank', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Model 'ThreadInfo', property 'property/unread', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Model 'PullRequestInfo', property 'property/sourceMark', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Model 'PullRequestInfo', property 'property/rank', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Model 'PullRequestInfo', property 'property/unread', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Model 'PullRequestBlock', property 'property/sourceMark', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\nERROR in Model 'PullRequestBlock', property 'property/rank', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\nERROR in Model 'PullRequestBlock', property 'property/unread', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\nERROR in Model 'UpdateThreadUnreadRequest', property 'property/latestReadMessage', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/latestReadMessage)\nERROR in Model 'VideoRecording', property 'property/videoChannelParticipantId', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/videoChannelParticipantId)\nFAILURE: Build failed with an exception.\n```"}
{"title": "Handle pasted links in message editor", "number": 3012, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3012", "body": "Fixes UNB-355\nWhen text is pasted or dropped, parse out links.  This can be extended to parse out other kinds of text in the future.  For now this is pretty simple."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3012#pullrequestreview-1109834573", "body": ""}
{"comment": {"body": "Github botched this -- I renamed the file (from Image.ts) because this isn't image-specific any more.  I'll try to highlight the differences.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3012#discussion_r972419517"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3012#pullrequestreview-1109835803", "body": ""}
{"comment": {"body": "This section is new -- give Slate a chance to paste content (so copy/paste within slate works), then try the copied text if it exists.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3012#discussion_r972420411"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3012#pullrequestreview-1109836084", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3012#pullrequestreview-1116046055", "body": ""}
{"title": "[BREAKS API ON MAIN] Slack threads API proposal", "number": 3013, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3013", "body": "Add slack ThreadType\nAdd slack property onto ThreadInfo, which provides slack metadata when a thread originates from Slack.  I just put the channel name onto this for now.\nAdd getThreadsForCommit API call.  This returns a series of ThreadInfos for a given commit.\n\nPending questions:\n* What kind of pagination/continuation do we need for getThreadsForCommit?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3013#pullrequestreview-1109878037", "body": ""}
{"comment": {"body": "I took this from `getThreadsForMe`, but we should probably just comment out or remove limit/cursor/before/after/IfModifiedSince for now?  We'll need to figure out how much pagination and pusher support we need here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3013#discussion_r972451150"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3013#pullrequestreview-1109878932", "body": ""}
{"comment": {"body": "Also note unlike `getThreadsForMe`, but like `getPullRequestsForCommits`, the request is in the body, and this is a `POST` call.  Needed for cases where there are a lot of commits relevant to a file.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3013#discussion_r972451791"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3013#pullrequestreview-1109916767", "body": "Personally, this falls in line with what we discussed, so make it so."}
{"title": "Jeff/unb 647 restore discussion on vscode", "number": 3014, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3014", "body": "Adds restore discussion banner to VSCode threads.\n\nDependant on https://github.com/NextChapterSoftware/unblocked/pull/2975 for banner content"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3014#pullrequestreview-1111301672", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3014#pullrequestreview-1111302364", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3014#pullrequestreview-1111303837", "body": "Good to go once the ThreadArchivedText issue is fixed"}
{"title": "Matt should be made aware that his changes are nice", "number": 3015, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3015"}
{"title": "Fix test", "number": 3016, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3016"}
{"comment": {"body": "Force merging.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3016#issuecomment-1248691342"}}
{"title": "APICompatTest can include breaking change text anywhere in the message", "number": 3017, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3017"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3017#pullrequestreview-1109892796", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3017#pullrequestreview-1109893296", "body": ""}
{"comment": {"body": "I would like for the expectedBreakingChange message to change to something more appropraite with a bit of Richie flair...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3017#discussion_r972462603"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3017#pullrequestreview-1109893341", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3017#pullrequestreview-1109894016", "body": ""}
{"comment": {"body": "\ud83c\udf88\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6131b734-cb1e-4d34-8705-d23bc04dcaec?message=27fd9e05-817d-4c1a-a02f-4d26843e2099).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3017#discussion_r972463151"}}
{"title": "Correct API spec thread API response codes [BREAKS API ON MAIN]", "number": 3018, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3018", "body": "Server is already doing the right thing (200). Spec was wrong.\nNo client impact."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3018#pullrequestreview-1109917793", "body": ""}
{"title": "Add libs", "number": 3019, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3019", "body": "Just add skeleton for slack libs. No code."}
{"title": "Bug with our config loader means that strict mode will incorrectly assert on nullable fields", "number": 302, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/302", "body": "sert on nullable fields"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/302#pullrequestreview-878189551", "body": ""}
{"comment": {"body": "we need the port for CORS when debugging localhost", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/302#discussion_r803190575"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/302#pullrequestreview-878189754", "body": ""}
{"title": "Backfill to mark sourcemarks as archived or deleted", "number": 3020, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3020", "body": "Mark sourcemarks as archived or deleted as appropriate if their related thread is also archived or deleted.\nTODO\n- [x] refactor query to store\n- [x] tests for above"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3020#pullrequestreview-1111120472", "body": ""}
{"title": "Environment support for Kube deployments", "number": 3021, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3021", "body": "Refactored workflows to reduce repetition in workflow definition \nTook the image build step out of deploy workflow \nAdded support for environments and concurrency models to address concurrent Kube deployments \nThis might break CI but unfortunately there's no way for me to test the deployment portion without a merge to main."}
{"comment": {"body": "Once I get these changes working I'll start separating Dev and Prod secrets to ensure environment isolation ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3021#issuecomment-1251572815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3021#pullrequestreview-1112898788", "body": "Love this change!!!"}
{"title": "Toggle web extension sidebar", "number": 3022, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022", "body": "Extension button now toggles sidebar.\nCurrently somewhat hidden to test out feature. By default, sidebar is still rendered.\nAlso includes unread count within badge. As part of this work, refactored out common sidebar streams for both VSCode + Web Extension.\n\n"}
{"comment": {"body": "If the sidebar has been hidden, should we grey out the icon? If going from hidden to visible, I think we should always go into pinned state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#issuecomment-1249832729"}}
{"comment": {"body": "> \r\n\r\nMakes sense. Do we have a greyed out version of our icon I could use?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#issuecomment-1249936130"}}
{"comment": {"body": "Summarizing our conversation IRL \u2014 rather than greying out, let's badge the extension icon as a reminder that there is new content for users to look at. Extension icons grey out automatically when the extension does not apply to the site you're viewing, so we don't want to confuse the pattern with that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#issuecomment-1251286013"}}
{"comment": {"body": "\r\nhttps://user-images.githubusercontent.com/1553313/191386704-c1e50e18-2040-46d0-926a-0fda89d4f4db.mp4\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#issuecomment-1253041172"}}
{"comment": {"body": "This looks good.\r\n\r\na couple thoughts that aren't necessarily a part of this PR:\r\n* I do wonder how valuable the number is.  It is basically not a true number anyways, as we load at most 10 threads from each thread list, so any listing with >10 unreads will end up broken.\r\n* Architecturally, we've done something a bit odd: for the streams that are loaded based on \"relevant repos\" (repos, threads, I'm sure there are others), we are cobbling together a list of relevant repos, then re-creating all streams separately for each client.  I wonder if it would make more sense for each client to publish a \"relevant repos\" list, and each store would build one set of streams based on that.  It would mean a consistent set of stores for all clients.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#issuecomment-1253050166"}}
{"comment": {"body": "> I do wonder how valuable the number is. It is basically not a true number anyways, as we load at most 10 threads from each thread list, so any listing with >10 unreads will end up broken.\r\n\r\nAgreed and I get it. IMO, I think this is still okay as the main purpose of this is to re-engage the user. It's not necessary for it to be 100% accurate.\r\n\r\n> Architecturally, we've done something a bit odd: for the streams that are loaded based on \"relevant repos\" (repos, threads, I'm sure there are others), we are cobbling together a list of relevant repos, then re-creating all streams separately for each client. I wonder if it would make more sense for each client to publish a \"relevant repos\" list, and each store would build one set of streams based on that. It would mean a consistent set of stores for all clients.\r\n\r\nOverall, I agree. I think we should have stores / interfaces not based on the client type but how a client fetches local data (e.g. git, urls, etc...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#issuecomment-1253939116"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#pullrequestreview-1114576875", "body": ""}
{"comment": {"body": "do we want to write in the same 10+ logic here? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#discussion_r975890666"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#pullrequestreview-1114577676", "body": ""}
{"comment": {"body": "actually, I wonder if it's worth consolidating that logic to the shared stream if we're gonna show the same result in all the clients\r\n\r\nmaybe not, just a thought", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#discussion_r975891232"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#pullrequestreview-1114579322", "body": ""}
{"comment": {"body": "oh I guess the vscode badge API only types to a number ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#discussion_r975892660"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#pullrequestreview-1114580055", "body": ""}
{"comment": {"body": "The logic to generate this is already shared across clients with the SidebarThreadStream. Don't think we need to take the extra step to going from number -> string since rendering is slightly different (as you've mentioned)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#discussion_r975893207"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#pullrequestreview-1114591955", "body": "IMO clicking the icon to toggle the sidebar is a little unexpected - I would expect this to be an item in the submenu instead:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#pullrequestreview-1114596698", "body": ""}
{"comment": {"body": "Maybe factor this type out so that you aren't repeating it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#discussion_r975906537"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#pullrequestreview-1114596918", "body": ""}
{"comment": {"body": "(The array-tuple type containing all these states, I mean)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#discussion_r975906697"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3022#pullrequestreview-1114597779", "body": ""}
{"title": "Clients should tolerate unknown API enum values", "number": 3023, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3023", "body": "Only a couple places that needed fixing."}
{"comment": {"body": "Can anyone say @matthewjamesadam is lovely?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3023#issuecomment-1249868627"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3023#pullrequestreview-1111230048", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3023#pullrequestreview-1111290774", "body": ""}
{"title": "Update safari build scripts", "number": 3024, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3024", "body": "Safari web extension now sends proper product agent.\nDownside, requires two webpack builds during CI now but I think that's unavoidable?\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3024#pullrequestreview-1111261010", "body": ""}
{"title": "Remove Package step from VSCode CI builds", "number": 3025, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3025", "body": "As discussed.  We don't need to build packages for VSCode builds, as they're built as part of the Installer build.  This will speed up VSCode PR builds and main builds.\nThe existing Build step already builds and tests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3025#pullrequestreview-1111364130", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3025#pullrequestreview-1111364142", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3025#pullrequestreview-1111370890", "body": ""}
{"title": "Compress sourcemark LRU cache keys", "number": 3026, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026", "body": "Problem\nThe cache keys for the SourceMark Git LRU cache are not small, because the key is literally the git command string. For a large repo, this adds up.\nSolution\nHash the command strings, and encode as binary string to pack efficiently.\nRisks\n\nInsignificant risk that a collision will occur. Weird shit would happen in this case.\nCPU time taken by hash function might negatively impact performance.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1111385361", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1112545676", "body": ""}
{"comment": {"body": "I think this should be removed. 'crypto' is built into node and the NPM package is just a placeholder", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#discussion_r974452196"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1112547533", "body": ""}
{"comment": {"body": "Won't this result in key collisions?  Multiple items will hash to the same keys, and I don't think there is any code that disambiguates the result?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#discussion_r974453496"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1112548710", "body": ""}
{"comment": {"body": "bunch of existing uses\r\n\r\n<img width=\"606\" alt=\"Screen Shot 2022-09-19 at 09 30 56\" src=\"https://user-images.githubusercontent.com/1798345/191067152-dd15c396-2a7e-4dfe-9509-3d3bb40ebd60.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#discussion_r974454336"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1112566610", "body": ""}
{"comment": {"body": "I called this out on the PR description:\n\n> Insignificant risk that a collision will occur. Weird shit would happen in this case.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/56c6de7e-114d-4210-860f-783ad3bc34f2?message=0687220c-2133-416a-850e-fc6489ed37dd).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#discussion_r974466800"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1112566851", "body": ""}
{"comment": {"body": "The chance of a collision is very low.\r\n\r\n--\r\n\r\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/56c6de7e-114d-4210-860f-783ad3bc34f2?message=d28c27ed-1507-49f4-946a-43a323b6181d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#discussion_r974466958"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1112569580", "body": ""}
{"comment": {"body": "Those ship with node so you don't need to install the package for it.  https://nodejs.org/api/crypto.html", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#discussion_r974469021"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1112578921", "body": ""}
{"comment": {"body": "ok, I'll switch all the uses \ud83d\udc4d \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#discussion_r974475533"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1112579695", "body": ""}
{"comment": {"body": "You shouldn't need to change the imports, you can simply remove the entry from the package.json (and changes from package-lock)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/e5f1237f-8d29-43c3-80d9-3e3af0384129?message=bb48ec0e-048c-4adf-ad10-6234a9956b56).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#discussion_r974476078"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#pullrequestreview-1112580546", "body": ""}
{"comment": {"body": "ah, ok\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/e5f1237f-8d29-43c3-80d9-3e3af0384129?message=5084d0d0-2fea-45da-8f6b-3207d73fe7fa).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3026#discussion_r974476558"}}
{"title": "Move scm file", "number": 3027, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3027"}
{"title": "Revert \"Move scm file (#3027)\"", "number": 3028, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3028", "body": "This reverts commit 1e265c57fc43b25fb25f39b36477dda9d1bbadee."}
{"title": "Fix naming for our internal slack stuff", "number": 3029, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3029"}
{"title": "Add DataCacheStore and tests", "number": 303, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/303", "body": "This adds two store wrappers to facilitate polling / fetching / caching data stores:\ncreateDataCacheStore -- this is the equivalent of createVanillaStore, this creates a non-react store based on a set of traits.  The store begins polling immediately when created.  The returned object has the same API as createVanillaStore, but when you call store.destroy() polling stops.\nuseDataCacheStore -- this is the equivalent of create and useStore in a single hook.  This creates a store based on a set of traits.  The store begins polling immediately when created, and is stopped when the view is unmounted.  Both a sliced and unsliced variant of the function are available."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/303#pullrequestreview-878208658", "body": ""}
{"comment": {"body": "To be clear, the component that calls this hook *owns* the reference to the cache.\r\n\r\nOnce the components is cleaned up, the cache will be destroyed. Correct?\r\n\r\nAka child components that want to reference the data need to be passed it by Prop and cannot access the data \"globally\" through a provider-like interface.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/303#discussion_r803205924"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/303#pullrequestreview-878213178", "body": ""}
{"comment": {"body": "Correct, yes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/303#discussion_r803209650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/303#pullrequestreview-883679624", "body": ""}
{"title": "Video app IPC service implementation", "number": 3030, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030", "body": "The IPC service is implemented but currently leverages a dummy publisher to yield responses. All requests to the service will yield a dummy response and have no practical effect. \nThe next PR will implement the Hub-side.\nIPC setup is as follows:\n\nService boots up\nService drops the IPC port to ~/Library/Group\\ Containers/2FNXZ6K9M4.com.nextchaptersoftware.shared/Library/Preferences/2FNXZ6K9M4.com.nextchaptersoftware.shared.plist\nClient must sniff this port directly with defaults read ~/Library/Group\\ Containers/2FNXZ6K9M4.com.nextchaptersoftware.shared/Library/Preferences/2FNXZ6K9M4.com.nextchaptersoftware.shared or similar"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1112709546", "body": ""}
{"comment": {"body": "Oops - this should move to the model file", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#discussion_r974561940"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119071517", "body": ""}
{"comment": {"body": "What is this ID?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#discussion_r979085562"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119076031", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119076676", "body": ""}
{"comment": {"body": "Is this File supposed to be here? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#discussion_r979089244"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119076751", "body": ""}
{"comment": {"body": "Same here? Doesn't seem too related to the PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#discussion_r979089303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119078367", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119108123", "body": ""}
{"comment": {"body": "Project settings", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#discussion_r979114026"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119108442", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119108805", "body": ""}
{"comment": {"body": "It was supposed to be in the last PR. Accidentally committed to this one and it was hard to unwind \ud83d\ude2d", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#discussion_r979114536"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119120402", "body": ""}
{"comment": {"body": "Please ignore this. Mean to be in the last PR but I had to move it forward", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#discussion_r979124044"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#pullrequestreview-1119120499", "body": ""}
{"comment": {"body": "It's just a slight refactor where I pulled out the Camera implementation into its own file", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3030#discussion_r979124115"}}
{"title": "Compress sourcemark LRU cache values", "number": 3031, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3031", "body": "Also, follow up from #3026, no need to import crypto."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3031#pullrequestreview-1112729871", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3031#pullrequestreview-1112734536", "body": ""}
{"title": "Implement VideoIPCClient", "number": 3032, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3032"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3032#pullrequestreview-1114258981", "body": ""}
{"comment": {"body": "It's a little bit unfortunate that we have to do this kind of status polling, but unfortunately the GRPC library doesn't offer an API to check on connection status. A guaranteed way to establish connection status is to open a stream and wait for a response.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3032#discussion_r975663934"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3032#pullrequestreview-1119094619", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3032#pullrequestreview-1119095244", "body": ""}
{"comment": {"body": "This is something. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3032#discussion_r979103495"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3032#pullrequestreview-1119107638", "body": ""}
{"comment": {"body": "Hideous. It's amazing to me that openApplication, which has an async variant, returns before the app is actually running", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3032#discussion_r979113649"}}
{"title": "Move common ingestion utilities", "number": 3033, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3033"}
{"title": "Streamify the VSCode commit/PR pipeline", "number": 3034, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3034", "body": "First bit of work for displaying Slack threads, this extracts the stream of current commits and file data out from the PR stream and makes it reusable."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3034#pullrequestreview-1113146844", "body": ""}
{"title": "Right-click options for threads", "number": 3035, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3035", "body": "Behaviour: try to open in requested destination but still fall back if destination not available\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3035#pullrequestreview-1112890956", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3035#pullrequestreview-1112894364", "body": ""}
{"title": "fix the region", "number": 3036, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3036"}
{"title": "remove concurrency", "number": 3037, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3037"}
{"title": "force a rerun", "number": 3038, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3038"}
{"title": "renable concurrency and disable secret deployments", "number": 3039, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3039"}
{"title": "Update fontawesome packages to 6.0.0", "number": 304, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/304", "body": "Released on Monday: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/304#pullrequestreview-878216966", "body": ""}
{"title": "passing kube api hosts directly", "number": 3040, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3040"}
{"title": "fixing my mistake in docker image creation", "number": 3041, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3041"}
{"title": "Basic slack importer", "number": 3042, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3042"}
{"title": "Update files for matt", "number": 3043, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3043"}
{"title": "Add resource usage for debugging and make sourcepoint upload more efficient", "number": 3044, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3044", "body": "Trying to track down the memory consumption problem, 1GB is evaporating somewhere."}
{"title": "Splitting deploy step", "number": 3045, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3045", "body": "Broke the deploy step apart. It was really hard to see deployment steps for different environments on GH action UI\nRe-enabled secret deployments"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3045#pullrequestreview-1113079301", "body": ""}
{"title": "Fixing deployment", "number": 3046, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3046"}
{"title": "Test another deployment change", "number": 3047, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3047"}
{"title": "cheap fix for secrets deployment", "number": 3048, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3048", "body": "I think the issue is caused by Kubectl not supporting concurrency checks like helm."}
{"title": "fix docker image tag", "number": 3049, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3049"}
{"title": "Move to jdk 17", "number": 305, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/305", "body": "Was using jdk 8 and we're building jdk 17."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/305#pullrequestreview-878217607", "body": ""}
{"title": "Fix secret deployment", "number": 3050, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3050", "body": "Revert the condition for secrets deployments\nMove the prod deployment suspension flag one level up"}
{"title": "fix kube probe paths", "number": 3051, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3051", "body": "Made changes to add service name to health probe path.\nOld path: /api/__deepcheck\nNew path: /api/health/apiservice/__deepcheck\nFixed the secret name used to control prod deployments in main workflow"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3051#pullrequestreview-**********", "body": ""}
{"title": "Add some debugging logs", "number": 3052, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3052", "body": "Add debugging logs to track down unexpected fault UI instances\nRemove spammy badge logs, we don't need them"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3052#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3052#pullrequestreview-**********", "body": "add more metadata to logs as we discussed"}
{"title": "remove secret from global env vars", "number": 3053, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3053"}
{"title": "Archive sourcemarks when PR archives threads", "number": 3054, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3054", "body": "Missed this from previous #3007"}
{"title": "Add logging for sourcemark lookup failure", "number": 3055, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3055"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3055#pullrequestreview-1114348867", "body": ""}
{"title": "Highlighting for Web", "number": 3056, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3056", "body": "Setup highlighting for web.\nRequired some refactoring of code block to support full line highlighting and the jumpiness of transitioning from unformatted -> formatted.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3056#pullrequestreview-1114611200", "body": ""}
{"title": "Bring back teammember view", "number": 3057, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3057", "body": "To activate teammember view: defaults write com.nextchaptersoftware.UnblockedHub EnableTeamMemberView YES\n"}
{"comment": {"body": "@pwerry I can see from that screenshot that you aren't filtering out inactive users, ie Padraig appears in the user list.  We should probably filter those users out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3057#issuecomment-1252928102"}}
{"comment": {"body": "Also, the list doesn't look sorted in any way -- sort by name maybe?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3057#issuecomment-1252928615"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3057#pullrequestreview-1114469886", "body": ""}
{"title": "Tweak VSCode build settings", "number": 3058, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3058", "body": "Ignore files we don't care about in the file explorer\nEnable default bundle splitting in the webpack build -- this makes debug bootup faster, and creates smaller output bundles, as the duplicated data in each bundle is factored out.  Unfortunately the odd breakpoint triggering on startup is still there."}
{"comment": {"body": "This seems to break the webviews.  Not sure why, there are no failures or errors.  Closing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3058#issuecomment-1252949771"}}
{"title": "The most insane build hack I've ever done", "number": 3059, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3059", "body": "This build hack is necessary because of an Xcode/Swift compiler bug that Ankit was tracking back in 2019, where an embedded app causes module map resolution to fail (still not fixed): \nThis PR introduces two new build configurations and a new scheme as a way to omit the embedded video app for our main customers builds (for now).\nClose your eyes "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3059#pullrequestreview-1114531015", "body": ""}
{"title": "Update fontawesome svg core package", "number": 306, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/306", "body": "Update to 6.0.0 needs this package updated as well for the typing."}
{"comment": {"body": "Hello there something", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/306#issuecomment-1036445288"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/306#pullrequestreview-879154461", "body": ""}
{"title": "Upsert source points to prevent duplicates", "number": 3060, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3060"}
{"title": "Fix duplicate sourcepoint upload", "number": 3061, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3061", "body": "This partially reverts #3044"}
{"title": "Boot app and create new channel on person click", "number": 3062, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062", "body": "Curiously when you embed one sandboxed app inside another the permissions requests need to exist at the top level."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#pullrequestreview-1115634081", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#pullrequestreview-1120482057", "body": ""}
{"comment": {"body": "I _really_ don't like this, but couldn't think of a better way to achieve it. The problem this tries to solve is that the `mainView`'s `@State` wrappers get completely unloaded when we switch tabs, so things like collapsed sections won't be preserved. I believe this is a unique issue with conditionals within `ForEach`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#discussion_r980212170"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#pullrequestreview-1120488919", "body": ""}
{"comment": {"body": "This is a wonky permissions quirk where the embedded app seems to require that the parent app adopt these entitlements. We need to solve this in another PR. It's super annoying from an experience standpoint to have to accept permissions twice for two different apps.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#discussion_r980219322"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#pullrequestreview-1120490176", "body": ""}
{"comment": {"body": "This code is just proving the point that dispatching the channel request is possible. It's not what the real product code will do", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#discussion_r980220659"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#pullrequestreview-1120947667", "body": ""}
{"comment": {"body": "Should we hoist this state out into a separate object that will manage it more reliably?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#discussion_r980530627"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#pullrequestreview-1121019857", "body": ""}
{"comment": {"body": "I think the idea of managing _view_ _state_ outside of the view runs counter to the intended design pattern for SwiftUI, so if we needed to reach for another solution I think I'd prefer if it allowed us to preserve usage of `@State` within the view. This is in contrast with _app state_, which I agree is a better candidate for an `ObservableObject` or more specifically a `Publisher` of some kind\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/00ed6618-5fbd-4717-80a4-52ce69a61da6?message=f834907e-ea79-4f40-9576-4b1cb6611da2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#discussion_r980583042"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#pullrequestreview-1122303454", "body": ""}
{"comment": {"body": "I agree.  It might be worth figuring out why the state is not being kept -- is SwiftUI unable to correlate the before-and-after views so that state is maintained?  In React if you want this to work correctly, you have to add a `key` property (an identifier) onto every child view.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/00ed6618-5fbd-4717-80a4-52ce69a61da6?message=82f5d3f7-1c53-4107-985c-450f8914cd01).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3062#discussion_r981454694"}}
{"title": "Fix naming", "number": 3063, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3063"}
{"title": "Login as VSCode user for open source repo", "number": 3064, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064", "body": "Changes\n\nRead only bearer token\nUpdate mustache template\nInstall read-only authentication\nImpersonation in auth service\n\nSee also\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115649349", "body": ""}
{"comment": {"body": "Intentionally allowing read-only user to also upstream source points.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r976661720"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115650828", "body": ""}
{"comment": {"body": "copy-paste of `AuthTokenConfigurator`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r976662753"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115658788", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115682742", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115686002", "body": ""}
{"comment": {"body": "I hate templates", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r976678266"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115692136", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115692437", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115692557", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115693569", "body": ""}
{"comment": {"body": "This helps:\r\nhttps://www.notion.so/nextchaptersoftware/Mustache-Development-8379170f7e7a4dd1a96a66dead1b7548", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r976682485"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115694294", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115696508", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115698953", "body": ""}
{"comment": {"body": "Lastly, this function will need to be modified to return the auth token with the read-only bearer type", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r976687841"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115733219", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115863757", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1115866704", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1117791734", "body": ""}
{"comment": {"body": "Done in an appallingly hacky way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978171194"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1117792430", "body": ""}
{"comment": {"body": "\ud83d\ude05", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978171745"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1117795715", "body": ""}
{"comment": {"body": "yeah, this is awful\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c13934d5-74d8-40fe-93a0-60af101daef1?message=fec41cbc-f51a-4cda-9700-3c56969795fd).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978174361"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1117795819", "body": ""}
{"comment": {"body": "I'll see if I can make it clearer\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c13934d5-74d8-40fe-93a0-60af101daef1?message=d7693dcd-511f-437d-b71c-8d3843c34ea9).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978174487"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1117804320", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1117804572", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1117804649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1117805061", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1117874346", "body": ""}
{"comment": {"body": "@matthewjamesadam need a better way to plumb this into the TS apps. doing this is clunky for VSCode, and doesn't even work for the web apps.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978233250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1118733902", "body": ""}
{"comment": {"body": "I think we won't actually use these `readOnlyBearerAuth` -- are you sure we need these here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978849691"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1118737337", "body": ""}
{"comment": {"body": "yes we do unfortunately. Take a look at the generated code where every operationID that has _both_ `readOnlyBearerToken` and `bearerToken` actually performs two lookups from the [same] token store. It's functional, but clearly quite silly in what it's doing.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4068846c-b7ca-4a76-a50f-005fa4bc6583?message=d1010909-3401-45a6-b397-ac13b57e970a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978852053"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1118853387", "body": ""}
{"comment": {"body": "cc @jeffrey-ng\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/012fe321-abdd-4c37-912c-37c3df745572?message=efb62bbe-6eaf-4a78-b9b3-b628b9a7365f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978933181"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1118907800", "body": ""}
{"comment": {"body": "We could add this to the UI behind a feature flag?\r\n\r\nAka in the login UI, we can add an input box where you post this identity.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978970164"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1118908729", "body": ""}
{"comment": {"body": "I'm removing this. We came up with a better idea here:\n\n[https://chapter2global.slack.com/archives/C02HEVCCJA3/p1663958206601779?thread_ts=1663956152.552739&cid=C02HEVCCJA3](https://chapter2global.slack.com/archives/C02HEVCCJA3/p1663958206601779?thread_ts=1663956152.552739&cid=C02HEVCCJA3)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/012fe321-abdd-4c37-912c-37c3df745572?message=e145415b-801f-43ed-bfc2-c4f8ce6414ba).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r978970852"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#pullrequestreview-1119445085", "body": ""}
{"comment": {"body": "Fixed here:\n\n[https://github.com/NextChapterSoftware/unblocked/pull/3102/files#diff-2d98a89aeba25c02e51021353255857341c6845d592abe46f71795063f625fdfR57](https://github.com/NextChapterSoftware/unblocked/pull/3102/files#diff-2d98a89aeba25c02e51021353255857341c6845d592abe46f71795063f625fdfR57)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c13934d5-74d8-40fe-93a0-60af101daef1?message=5611d5a2-ce3a-4a7c-9c05-af56acad5683).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3064#discussion_r979450725"}}
{"title": "Revert \"Basic slack importer (#3042)\"", "number": 3065, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3065", "body": "This reverts commit 1894366acd4e1979e32b6c319bbdb5c6c378a4fd."}
{"title": "Add logging", "number": 3066, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3066"}
{"title": "Rip out SM engine settings", "number": 3067, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3067", "body": "Motivation\n- Customers have found these and are messing with them.\n- See \nAlso lint cleanup."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3067#pullrequestreview-1115819504", "body": ""}
{"comment": {"body": "It's always off now. Which is fine as I can toggle this on myself in a debug session.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3067#discussion_r976797139"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3067#pullrequestreview-1115833433", "body": ""}
{"title": "Apparently we need pr comments queue access for creating messages", "number": 3068, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3068", "body": "Not good forcing every service to be dependent on a queue.\nWill be changing the code up."}
{"title": "Minor DB perf improvement when upserting source points", "number": 3069, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3069", "body": "Avoids a write SQL request sometimes."}
{"title": "Optimize docker base image", "number": 307, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/307", "body": "Saving 400MB per image creation by change base image to a more streamline variant.\nYeah, I think that's worth it..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/307#pullrequestreview-878232601", "body": ""}
{"title": "Cache git diffs", "number": 3070, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3070", "body": "Diff between the same pair of commits is always identical, so eligible for immutable caching."}
{"title": "API spec lint", "number": 3071, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3071"}
{"title": "Add team parsing logign", "number": 3072, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3072"}
{"title": "Updating logging", "number": 3073, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3073"}
{"title": "[BREAKS API ON MAIN] Message pinning", "number": 3074, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074", "body": "Add optional isPinned property to the Message model as well as the UpdateMessageRequest body\nImplement backend -- needs review on whether this is the best approach"}
{"comment": {"body": "> Concerns about the intent of this feature at a high level. It's odd in that the message is pinned for _all_ users in a team, which would not scale with a large number of team members (eg: Pulumi, Ada, Clio, etc).\r\n\r\n@richiebres I'm not sure I follow why it wouldn't scale? Idea here is that starred or pinned messages would benefit large teams by highlighting the key message or messages in a thread. I'm not opposed to adding who pinned and when.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#issuecomment-1254203528"}}
{"comment": {"body": "Per [slack thread](https://chapter2global.slack.com/archives/C02GEN8LFGT/p1663794375466609), putting a pause on this until we can invest time in thinking through this feature.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#issuecomment-1254283131"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#pullrequestreview-**********", "body": "@benedict-jw @dennispi Concerns about the intent of this feature at a high level. It's odd in that the message is pinned for all users in a team, which would not scale with a large number of team members (eg: Pulumi, Ada, Clio, etc).\nThe equivalent feature in Slack does also pin for all users, but at least it's scoped to a channel.\nAlso slack provides information like who pinned and when. Without that, how do I know why it was pinned, and if it is safe to unpin?"}
{"comment": {"body": "Can't add non-nullable field to existing table as the SQL migration will fail, so make nullable.\r\n\r\nAlso, since this is _sparse_ do not set a client default.\r\n\r\n```suggestion\r\n    val isPinned = bool(\"isPinned\").nullable()\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#discussion_r976950304"}}
{"comment": {"body": "remove the if-else and simplify\r\n```kt\r\nisPinned?.also { message.isPinned = it }\r\n```\r\n\r\nlogic is wrong here when content and isPinned changes, so should be something like:\r\n\r\n```kt\r\nif (content changed) {\r\nsave new content\r\nwas changed = true\r\n}\r\n\r\nif (pinning changed) {\r\nsave new pinning\r\nwas changed = true\r\n}\r\n\r\nreturn was changed\r\n```\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#discussion_r976954143"}}
{"comment": {"body": "Add tests for:\r\n1. content and pin changed\r\n2. neither content nor pin changed, and expect no DB write", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#discussion_r976954902"}}
{"comment": {"body": "nit: add description", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#discussion_r976955445"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#pullrequestreview-1116062424", "body": ""}
{"comment": {"body": "I wonder if we should explicitly guard against this? I don't see a way where a user could update both content and isPinned in one action.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#discussion_r976963902"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#pullrequestreview-1116063630", "body": ""}
{"comment": {"body": "since it's not invalid we should allow it\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/e89d7854-5547-4780-85fd-bd52d5bd1d8f?message=b2c723d2-a78a-4a73-94cb-3d6d3bf211a1).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3074#discussion_r976964746"}}
{"title": "Make transacitons more granular", "number": 3075, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3075"}
{"title": "WIP: Saving a few more seconds (DO NOT MERGE)", "number": 3076, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3076"}
{"title": "FixUserAttribution", "number": 3077, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3077", "body": "Fix user attribution\nFi xuser attibution"}
{"title": "Move padding to header", "number": 3078, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3078", "body": "This is a regression from here -- not sure why the style was moved though.\nbefore:\n\nafter:\n"}
{"comment": {"body": "Most likely regression when adding banner.\r\nhttps://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/14e37932-dba5-4ae8-a6c6-7dcc319114e3", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3078#issuecomment-1254207763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3078#pullrequestreview-1116144462", "body": ""}
{"title": "Jeff/unb 666 allow users to hide insight bubbles in", "number": 3079, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3079", "body": "Allow users to hide source mark gutter in VSCode.\n\nThe package.json is really bloated due to limitations on VSCode command titles/icons.\n\nGitLens runs into the same issue which blows up their extension.package.json due to all the state permutations."}
{"comment": {"body": "<img width=\"549\" alt=\"CleanShot 2022-09-21 at 15 50 06@2x\" src=\"https://user-images.githubusercontent.com/1553313/191624569-7153903a-5677-4838-bcfe-dd526a0b14e0.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3079#issuecomment-1254316609"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3079#pullrequestreview-1117333800", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3079#pullrequestreview-1117345272", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3079#pullrequestreview-1117434009", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3079#pullrequestreview-1117436285", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3079#pullrequestreview-1117438036", "body": ""}
{"comment": {"body": "Yowza \ud83e\udd2a ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3079#discussion_r977919162"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3079#pullrequestreview-1117439524", "body": ""}
{"title": "Adds configurable SQL logging to transactions", "number": 308, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/308", "body": "Just a tool to debug stuff"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/308#pullrequestreview-879127031", "body": ""}
{"title": "Show Slack threads in TS clients", "number": 3080, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080", "body": "Show Slack threads in typescript clients:\n\nShow slack icon for slack-related threads (as they can be shown in the mine listings now)\nIn VSCode dev builds, add a new Unblocked: Current File Threads panel, that displays all threads relevant to the current file. Right now this includes Slack threads and PR comments.  This is a temporary panel, I suspect we will be merging this data into one of the existing panels before enabling this in prod.\n\n\n"}
{"comment": {"body": "Bit confused. In the screenshot, this replaces the insights panel?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#issuecomment-1255799980"}}
{"comment": {"body": "> Bit confused. In the screenshot, this replaces the insights panel?\r\n\r\nNo it's an additional panel, only visible in dev.  I think that screenshot was from an older build where I had accidentally disabled the Insights panel.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#issuecomment-1256552271"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#pullrequestreview-1116182699", "body": ""}
{"comment": {"body": "Only attempt to resolve the SM if the thread actually has one. This prevents the read-only view from displaying (with an error) for threads that are not associated with a SM", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#discussion_r977052454"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#pullrequestreview-**********", "body": ""}
{"comment": {"body": "FYI @jeffrey-ng this lets us easily enable/disable VSCode UI definitions (in the package.json with the `where` clause) based on feature flags", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#discussion_r977052761"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is basically a clone of the `ExplorerCurrentFileWebviewProvider`, but pulls from the `CurrentFileThreadsStream`.  I suspect we will be merging all these providers into one thing at some point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#discussion_r977053061"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This allows a given source UI to provide a default file association for a discussion view. This is needed when launching a slack discussion, as the slack thread itself has no SM, as thus has no file path associated with it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#discussion_r977053385"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Nice!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#discussion_r978264223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3080#pullrequestreview-**********", "body": ""}
{"title": "Jeff/unify build versions", "number": 3081, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3081", "body": ""}
{"comment": {"body": "Sample of working build.\r\nhttps://github.com/NextChapterSoftware/unblocked/actions/runs/3107972426", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3081#issuecomment-1255517649"}}
{"comment": {"body": "> Sample of working build.\r\n> https://github.com/NextChapterSoftware/unblocked/actions/runs/3107972426\r\n\r\nFor that build:\r\n* I don't see any Safari/Chrome artifacts?  There is the single `web-extension-artifact` output, but that is just the web extension JS/CSS.\r\n* (maybe this is related to the above?) the `web-extension-build` job failed, in the `Run Build` task: https://github.com/NextChapterSoftware/unblocked/actions/runs/3107972426/jobs/5036669254\r\n```\r\nUnable to parse manifest.json at file:///Users/<USER>/work/unblocked/unblocked/web-extension/build/safari/\r\n[98](https://github.com/NextChapterSoftware/unblocked/actions/runs/3107972426/jobs/5036669254#step:10:99)\r\nxcodebuild: error: './build/Unblocked/Unblocked.xcodeproj' does not exist.\r\n[99](https://github.com/NextChapterSoftware/unblocked/actions/runs/3107972426/jobs/5036669254#step:10:100)\r\nerror: archive not found at path '/Users/<USER>/work/unblocked/unblocked/web-extension/build/Unblocked/build/archive/Unblocked.xcarchive'\r\n[100](https://github.com/NextChapterSoftware/unblocked/actions/runs/3107972426/jobs/5036669254#step:10:101)\r\n** EXPORT FAILED **\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3081#issuecomment-1255523042"}}
{"comment": {"body": "My bad. Forgot to update the OS versions back to os 12\r\nhttps://github.com/NextChapterSoftware/unblocked/actions/runs/3108688372", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3081#issuecomment-1255557866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3081#pullrequestreview-1117878120", "body": "pretty!"}
{"title": "Fix channel paging", "number": 3082, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3082"}
{"title": "Ensure we consume threads with nested prs", "number": 3083, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3083"}
{"title": "Admin console slack stuff", "number": 3084, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3084"}
{"title": "Separate Auth and Refresh Person", "number": 3085, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3085", "body": "Temporary patch to disassociate refreshing auth token and refreshing person. This will no longer block requests when auth refresh is successful but getPersons fails.\nIssue was that getPersons was throwing a 401 (https://github.com/NextChapterSoftware/unblocked/pull/2824) which we are no longer manually retrying (https://github.com/NextChapterSoftware/unblocked/pull/2982).\nThere needs to be some refactoring done to authstore in general to lock this down some more."}
{"comment": {"body": "Theory is that these issue come from general network availability issues.\r\n\r\naka pending request on the network stack has a valid auth token when put on the stack but by execution time, token no longer valid.\r\n\r\nPotential fix is to add a post response middleware that refreshes auth and retries on 401", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3085#issuecomment-1255364592"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3085#pullrequestreview-1117485186", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3085#pullrequestreview-1117491704", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3085#pullrequestreview-1117493664", "body": ""}
{"title": "Introduce Insiders Service to centralize internal user/team behaviour", "number": 3086, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3086", "body": "We have special lists of:\n- internal users\n- internal teams\n- demo users\n- impersonators (NEW)\nWhich are used to provide special handling for:\n- user engagement metrics\n- client version tracking\n- intercom attribution\n- fake recommendation model generation\n- login as user (NEW)"}
{"comment": {"body": "Also used for intercom attribution...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3086#issuecomment-1255628385"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3086#pullrequestreview-1117757211", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3086#pullrequestreview-1117757597", "body": ""}
{"title": "Move away from deprecated way of managing gradle versions", "number": 3087, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3087", "body": "Version catalogs are the recommended approach these days..."}
{"title": "Add 'Unblocked' prefix to Start Discussion option", "number": 3088, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3088", "body": "In both code action and context editor.\nNote that we can't use the category setting for this, as that is only displayed in the command palette, not in context menus or elsewhere.  I think we should probably just remove the category elsewhere, and explicitly have then prefix everywhere?\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3088#pullrequestreview-1117796043", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3088#pullrequestreview-1117915299", "body": ""}
{"title": "Cleanup gradl efiles", "number": 3089, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3089"}
{"title": "Run CI on package.json / package-lock.json change", "number": 309, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/309", "body": "PRs that only change dependencies should run CI too."}
{"comment": {"body": "Kay is fixing the remaining failure on a different branch.  I'm going to force merge this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/309#issuecomment-1035272119"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/309#pullrequestreview-879164326", "body": ""}
{"title": "Impersonator should not affect client version metric", "number": 3090, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3090", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3090#pullrequestreview-1117881189", "body": "HANEDSOME!!!!"}
{"title": "Whoopsie, fixing prod", "number": 3091, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3091"}
{"title": "Revert \"Cache git diffs (#3070)\"", "number": 3092, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3092", "body": "This reverts commit 092742104392bf9e65805663f10557ffc9734c69.\nTurns out that massive branch merges occur in repos, like expo/expo\nwhere 26,075 files and 3,864,730 lines of diffs were cached."}
{"title": "Sort lines", "number": 3093, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3093"}
{"title": "Upgrade some of our dependencies", "number": 3094, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3094"}
{"title": "Turn off full recalculation", "number": 3095, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3095", "body": "Motivation is that full recalculation is unusably slow, maxing out mem and cpu.\nUntil we tune the algorithm, full recalculation is going to stay off.\nRely only on incremental recalculation so that we only recalculate marks necessary\nfor \"open\" files. In this approach, we still bulk fetch all mark data but we only\nrecalculate part of the entire dataset and typically deferred this until the file\nis opened.\nDownside risk is that points are not created for web extension consumption; so this\nbreaks the effectiveness of the web extension."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3095#pullrequestreview-1117980982", "body": ""}
{"title": "Update agora sdk to release version", "number": 3096, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3096"}
{"title": "Adds identity override to hub", "number": 3097, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3097"}
{"comment": {"body": "Closing now that we're going to do this server side", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3097#issuecomment-1256545839"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3097#pullrequestreview-1118878722", "body": ""}
{"comment": {"body": "Just ignore these it's Xcode being dumb", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3097#discussion_r978950304"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3097#pullrequestreview-1118880734", "body": "Where does the key come from?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3097#pullrequestreview-1118882555", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3097#pullrequestreview-1118882856", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3097#pullrequestreview-1118889192", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3097#pullrequestreview-1118902928", "body": ""}
{"title": "Admin web shows impersonators", "number": 3098, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3098", "body": "Just adds a badge to admin web."}
{"title": "Basic slider carousel component", "number": 3099, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3099", "body": "(To be a part of the upcoming onboarding  revamp work)\nbasic implementation:\n\nwith a custom template:\n\nNote: Had to update a lot of the css imports in shared component css files in order for storybook to load properly"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3099#pullrequestreview-1119133129", "body": ""}
{"title": "Added Kubernetes deployment config", "number": 31, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31", "body": "Due to limitations in eksctl cli, EKS deployments are manual. Implementing CI/CD for EKS cluster deployments requires additional components to properly handle configuration changes in order to call appropriate sub-commands. Considering the low frequency of changes to core EKS configurations, full automation of this component is better left for future. \n\nAdded eksctl yaml config file for Dev Kubernetes cluster \nAdded README with deployment instructions\nDeployed EKS cluster to Dev with latest configurations"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#pullrequestreview-850996634", "body": ""}
{"comment": {"body": "Is this a once off? If we're intending to reuse this, then this should be extracted to a script and parametized for (env, zoneID, etc)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#discussion_r783437640"}}
{"comment": {"body": "Same as above. Seems you could generate this yaml from a here doc and check in separately.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#discussion_r783438281"}}
{"comment": {"body": "What's the command to generate this output?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#discussion_r783439218"}}
{"comment": {"body": "How often will we be doing this? Once per region standup, everytime we edit config, or other? Trying to get a sense for how automated this should be.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#discussion_r783440626"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#pullrequestreview-851002630", "body": ""}
{"comment": {"body": "It's once per cluster setup (first time we create a new cluster) so these procedures will run max 3-4 more times. Hence why I didn't bother with automating it at this point ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#discussion_r783442002"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#pullrequestreview-851004011", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#pullrequestreview-851005222", "body": ""}
{"comment": {"body": "Deployments are done per region standup. The most frequent operations would probably be IAM permission changes and network policies that can be automated at a later time. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#discussion_r783443766"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#pullrequestreview-851007087", "body": ""}
{"comment": {"body": "This is not a generated output. This is inventory of what we have. Those CIDRs are chosen by us as part of our infrastructure network. pre-prod environments use 172.16-31.x.x/16 private IP range, prod uses 10.x.x.x/16 and 192.168.x.x/16 is reserved for VPN. I have a notion Architecture Doc WIP that I will share later. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#discussion_r783445040"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#pullrequestreview-851016937", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#pullrequestreview-851017680", "body": ""}
{"comment": {"body": "Same as my previous comment. These procedures will run 3-5 times during initial region setup so I couldn't justify automating or scripting it. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#discussion_r783452533"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/31#pullrequestreview-851082384", "body": "I'm okay with this."}
{"title": "Relax json serialization for kotlinx", "number": 310, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/310", "body": "Serializer was bombing on GitHub requests because it was strict"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/310#pullrequestreview-879295779", "body": ""}
{"comment": {"body": "rationale for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/310#discussion_r804002808"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/310#pullrequestreview-879296360", "body": "Approved with a question."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/310#pullrequestreview-879297105", "body": ""}
{"comment": {"body": "Sorry GitHub went nuts as I was typing the summary and created the PR. Added rationale to description", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/310#discussion_r804003778"}}
{"title": "Fix email formatting", "number": 3100, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3100", "body": "before:\n\nafter:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3100#pullrequestreview-1119084511", "body": "Wow, we should validate this output too. It turns out you can put whatever you want into the email field in Git:\ngit config user.email 'literally, anything'\nExample:\n"}
{"title": "Minor slack webhook challenge handling", "number": 3101, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3101"}
{"title": "Login-As-User is controlled by server [BREAKS API ON MAIN]", "number": 3102, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3102", "body": "Changes\n\nWorks consistently on Hub, WebExt, VSCode, Dashboard clients\nNo client changes needed to activate\nDoes not break API at all; optional headers were never used and now removed.\n\nHow to Login-as-User\n\n"}
{"title": "Defer computing Git diffs unless necessary", "number": 3103, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3103", "body": "Problem\nWe compute diffs for all files in a commit to track sourcemarks that move across files\nin a commit. However, mostly sourcemarks are not moved between files.\nChanges\n- Compute diff for only the current file in the commit that the sourcemark lives in.\n- Only computing the diffs for the other files in the commit when we know that\n  the sourcemark may have been moved.\nImpact\n- expected to bring down memory usage, since we will cache less Git content\n- expected to improve runtime perf because we do not parse diffs as frequently\nResults\n- full recalculation on nextchaptersoftware/unblocked:\nmemory (MB) | before | after\n--|--|--\nrss | 1,968 | 131\nmaxRSS | 1,968 | 131\nheapUsed | 1,711 | 81\nheapTotal | 1,856 | 91\n\nfull recalculation on expo/expo:\n\nmemory (MB) | before | after\n--|--|--\nrss | fail | 2,003\nmaxRSS | fail | 2,085\nheapUsed | fail | 646\nheapTotal | fail | 661"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3103#pullrequestreview-1120927022", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3103#pullrequestreview-1120927274", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3103#pullrequestreview-1120928333", "body": ""}
{"title": "Remove spammy webview logs", "number": 3104, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3104", "body": "No props to render elements is logged a lot when no failure has actually occurred, so move it to a point where we do think a failure has happened.  Setting authentication token is not a useful log AFAICT."}
{"comment": {"body": "You're lovely.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3104#issuecomment-1258533832"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3104#pullrequestreview-1120802670", "body": ""}
{"title": "Can't spell Dennis", "number": 3105, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3105", "body": "Auth lookup fails for Dennis as a result."}
{"title": "Flip all macOS builders to macOS 12 using Xcode 14.0.1", "number": 3106, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3106"}
{"title": "Add slack webhook to infra", "number": 3107, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3107"}
{"title": "Fix LUIElement break and remove unncecessary entitlements from base app", "number": 3108, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3108"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3108#pullrequestreview-1120874018", "body": ""}
{"title": "Ignore case when sorting team members", "number": 3109, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3109"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3109#pullrequestreview-1120874335", "body": ""}
{"title": "Fix incorrect github user id type", "number": 311, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/311"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/311#pullrequestreview-879345435", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/311#pullrequestreview-879346433", "body": ""}
{"comment": {"body": "any reason for string conversion?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/311#discussion_r804038835"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/311#pullrequestreview-879346695", "body": ""}
{"comment": {"body": "We store it as text in the DB", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/311#discussion_r804039023"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/311#pullrequestreview-879346842", "body": ""}
{"comment": {"body": "Or this is one case where it's an integer and you generally wa nt ids to be typed as string.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/311#discussion_r804039145"}}
{"title": "Fix teammember spacing", "number": 3110, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3110", "body": "To align teammember view with threads:\nTo this\n\n\n\n"}
{"comment": {"body": "@pwerry can we align the font-weight for the discussion title and the name too?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3110#issuecomment-1258630575"}}
{"comment": {"body": "> @pwerry can we align the font-weight for the discussion title and the name too?\r\n\r\nYup. General question: do we move the threads view to look like the team member view (more leading and trailing padding) or vice versa?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3110#issuecomment-1258635708"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3110#pullrequestreview-1120910311", "body": ""}
{"title": "Refactored Auth Store", "number": 3111, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111", "body": "AuthStore was fragile due to architecture and lack of tests.\nEverything is now more stream based when possible.\nRefactored to move away from Zustand and make it entirely stream based.\nRefactored AuthState into multiple streams."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#pullrequestreview-1120971676", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#pullrequestreview-1120972358", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#pullrequestreview-1120973159", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#pullrequestreview-1121030422", "body": ""}
{"comment": {"body": "As per our discussion, we should export some kind of scoping for these functions and streams... ie for the reset() function, AuthStore.reset or AuthReset or ResetAuth or something.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#discussion_r980591318"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#pullrequestreview-1121030712", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#pullrequestreview-1121031065", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#pullrequestreview-1121035457", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#pullrequestreview-1121040834", "body": "Some feedback but looks good!"}
{"comment": {"body": "This doesn't result in the Person being sent to the webview -- this might matter if the Person is loaded *after* the webview is displayed.  I think calling setAuth again would fix this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#discussion_r981697167"}}
{"comment": {"body": "We should also set ChannelPoller.updateAuthState() directly when setupChannelPollerSubscription is called, on the current auth state, to set the initial state correctly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3111#discussion_r981698831"}}
{"title": "Delete LEFT side threads during bulk ingestion", "number": 3112, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3112", "body": "Fixes UNB-671\nTo be reverted once run for the unblocked repo, since its the only one with LEFT side threads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3112#pullrequestreview-1121009837", "body": ""}
{"comment": {"body": "Permanently deleting here since we shouldn't have ingested in the first place. This is cleaner than archiving, since we'd need to create an `ArchivedReason` for this scenario that only exists for the Unblocked repo.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3112#discussion_r980575667"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3112#pullrequestreview-1121017723", "body": ""}
{"title": "Consolidate generated code to a single directory", "number": 3113, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3113", "body": "This change moves all codegen to a single location. Specifically:\ngeneratedApi and GRPC to:\n- generated/api\n- generated/grpc"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3113#pullrequestreview-1121025505", "body": ""}
{"title": "Revert \"Delete LEFT side threads during bulk ingestion (#3112)\"", "number": 3114, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3114", "body": "This reverts commit 9efca1cb544fbc7283359a23a124368238a0d668."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3114#pullrequestreview-1121130896", "body": ""}
{"title": "Remove unused import", "number": 3115, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3115"}
{"title": "[DO NOT MERGE] Add unused import", "number": 3116, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3116", "body": "Ideally this should fail PR build..."}
{"title": "Add slack webhook framework", "number": 3117, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3117"}
{"title": "Fix teammember spacing (take 2)", "number": 3118, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3118"}
{"title": "Add Git debugging for source points", "number": 3119, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3119", "body": "Used for validating snippets."}
{"title": "fix cloudfront cors issue using new resource in CDK", "number": 312, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/312", "body": "It's already deployed Dev and prod and the config looks identical to the working hand configured setup in Dev. Waiting for final confirmation from Jeff but this should be good to go."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/312#pullrequestreview-879437606", "body": ""}
{"title": "Partial recalculation processes top-N files", "number": 3120, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3120", "body": "Immediately after construction the source mark calculator now processes the top-10 files ordered\nby thread count. Motivation is to ensure that there are several files with lots of content\navailable soon after Unblocked has been loaded in VSCode.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3120#pullrequestreview-1121089073", "body": ""}
{"title": "Add message event handlers", "number": 3121, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3121"}
{"title": "update more dependencies", "number": 3122, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3122"}
{"title": "Re-enable full recalculation", "number": 3123, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3123", "body": "This reverts commit f2a2dfdb6f95c9a61e114a015f9c1732efc03cce from #3095.\nMerge when memory is constrained."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3123#pullrequestreview-1121225858", "body": ""}
{"title": "Improve VSCode debug startup time", "number": 3124, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3124", "body": "Change the source map type for VSCode. This improves the debugging experience:\n\nStartup time is a lot quicker\nWhen breakpoints are set on startup, don't break execution in a random location"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3124#pullrequestreview-1122315091", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3124#pullrequestreview-1122320154", "body": ""}
{"title": "Source mark engine doesn't handle file paths with spaces", "number": 3125, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3125", "body": "\nMostly updating test fixtures as a result of changing the diff arguments. The fix is pretty small/simple."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3125#pullrequestreview-1122496170", "body": ""}
{"title": "Update Web extension HWM", "number": 3126, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3126", "body": "Because published web extensions are > than existing build numbers, add .1 minor version to update HWM.\nMainly needed for Safari which is currently at 1.0.7533 while our installer build numbers at 1.0.437"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3126#pullrequestreview-1122515382", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3126#pullrequestreview-1122530094", "body": ""}
{"title": "Use universal release tag", "number": 3127, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3127"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3127#pullrequestreview-1122557929", "body": ""}
{"title": "Fix admin web copy command which had broken shell escaping", "number": 3128, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3128"}
{"title": "Add VideoChannelParticipant request", "number": 3129, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3129", "body": "Add API to fetch VCP for a given team / user.\nEach client will have a pusher channel for each team membership a person has. This channel updates whenever a there's an update to the Video Channel Participant list for a team membership.\nWhenever this channel sends an event, the client will call getVideoChannelParticipants to get an updated list."}
{"comment": {"body": "> Add API to fetch VCP for a given team / user.\r\n> \r\n> Each client will have a pusher channel for each team membership a person has. This channel updates whenever a there's an update to the Video Channel Participant list for a team membership.\r\n> \r\n> Whenever this channel sends an event, the client will call getVideoChannelParticipants to get an updated list.\r\n\r\nFor personalized channels like this, we haven't explicitly specified the person or team memberships into the channel, they've been implied by the person (via the token) making the request.  For instance, we don't subscribe to `/threads/mine/<person>`, we subscribe to `/threads/mine`.  I suspect we should do the same here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3129#issuecomment-1260087974"}}
{"comment": {"body": "> For personalized channels like this, we haven't explicitly specified the person or team memberships into the channel, they've been implied by the person (via the token) making the request. For instance, we don't subscribe to `/threads/mine/<person>`, we subscribe to `/threads/mine`. I suspect we should do the same here.\r\n\r\nNice. Prefer that approach. Will add the mine suffix.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3129#issuecomment-1260109476"}}
{"comment": {"body": "No longer working with VCPs.\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/3132", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3129#issuecomment-1260193281"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3129#pullrequestreview-1122737832", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3129#pullrequestreview-1122738986", "body": ""}
{"title": "Use databaseId instead of id", "number": 313, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/313", "body": "Rest object ids map to databaseId in GraphQL objects, while rest object node_ids map to id in GraphQL objects:\n\n\nOur GraphQL UserQuery uses databaseId as the identity external ID, while thus far the pull request GraphQL queries have used id as the external ID. Let's make sure we use the same field."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/313#pullrequestreview-879445764", "body": ""}
{"comment": {"body": "`databaseId` is nullable in GraphQL, fall back to `id` if it is (though I can't imagine when this would ever be the case for `User`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/313#discussion_r804096690"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/313#pullrequestreview-879466047", "body": "@rasharab more type coercion here because of our models"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/313#pullrequestreview-879757017", "body": ""}
{"comment": {"body": "I don\u2019t think we should fall-back here. I think we should bubble up this error. The reason is that we depend on the externalID to lookup the user by id.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/313#discussion_r804343624"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/313#pullrequestreview-879757596", "body": ""}
{"comment": {"body": "I would drop the id on the line above. We\u2019re not going to use it, and it\u2019s just going to cause confusion. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/313#discussion_r804344071"}}
{"title": "Commit propagation changes course when the sourcemark moves to another file", "number": 3130, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3130", "body": "Problem\n\n\nCommit propagation edges are constructed based on the original point's file, but\n  also taking into account when the file is renaemd or moved.\n\n\nHowever, when the point is moved from one file to another (which is not the same\n  as the file itself being renamed or moved), then the original propagation path is\n  invalid.\n\n\nChanges\n\n\nRefactor the commit propagation so that it can start from an arbitrary commit.\n\n\nDetect when the point has moved to another file. If it has moved then we re-generate\n  the remainder of the propagation path, clearing the current path and restarting\n  from the moved point instead."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3130#pullrequestreview-**********", "body": ""}
{"title": "[BREAKS API ON MAIN] - Add \"Declined\" type to VidepChannelParticipant.Status enum", "number": 3131, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131", "body": "Summary\nThis PR does two things:\n1. Modernizes the Video tests to operate against the local PG stack instead of mock stores. This caught a whole bunch of issues\n2. Adds the \"Declined\" participant status type, and changes the \"Pending\" type to \"Invited\" to reduce confusion\nThe Status type denotes a video channel participant's channel status. Clients will listen on a push channel that will vend video channel participant changes for that user, which drives the invite and realtime chat experiences"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1122834968", "body": "just minor nits"}
{"comment": {"body": "Any possibility that this can break legacy clients?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#discussion_r981812109"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1122845988", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1122846172", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1122846519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1122846638", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1123964975", "body": ""}
{"comment": {"body": "Nope - they wouldn't ever receive these models or request them", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#discussion_r982590682"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1123965504", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1123967213", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1124126985", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3131#pullrequestreview-1124135504", "body": ""}
{"title": "[BREAKS API ON MAIN] Updated channels api", "number": 3132, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3132", "body": "Update getVideoChannels to return only for me. Setup for modifiedSince.\nAdd Invite API\nAdd date to VideoParticipants for notifications purposes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3132#pullrequestreview-1124424238", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3132#pullrequestreview-1127396896", "body": "Thank you Jeff for being you."}
{"title": "Add webhook processing for slack", "number": 3133, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3133"}
{"title": "Fix millisecond resolution", "number": 3134, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3134"}
{"title": "update cdk deps", "number": 3135, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3135"}
{"title": "Replace rawgit.com CDN as it has been EOL", "number": 3136, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3136", "body": "Before (CSS broken)\n\n()\nAfter\n"}
{"comment": {"body": "This is one of my favourite changes of the year", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3136#issuecomment-1261206926"}}
{"title": "Hub touch ups", "number": 3137, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3137", "body": "Current shipping client\n\n\n\n\nWith Tabs\n\n\n\n\n"}
{"title": "Minor rename", "number": 3138, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3138"}
{"title": "Re-resolve SMs for read-only view when focused", "number": 3139, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3139", "body": "When we display the read-only SM resolution view, re-resolve and display the view whenever VSCode's window is refocused, so if the user runs git in terminal we will update the display.\nNow that I'm playing with this, I'm not sure it 100% solves this.  The user may use the VSCode UI to change branches, or may run this command in the built-in VSCode terminal. Something to consider for the future.\n"}
{"comment": {"body": "And actually, that video shows further bugs -- the code tracking the SMs for a file (shown in the sidebar panel and the text editor we end up opening) does not update and pull the new SM...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3139#issuecomment-1261298249"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3139#pullrequestreview-1124211158", "body": ""}
{"title": "Fix cdk pretty and lint", "number": 314, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/314"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/314#pullrequestreview-879520514", "body": ""}
{"comment": {"body": "We might need to add `cdk.context.json` to ignore list as well. That file is generated and managed by CDK", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/314#discussion_r804128146"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/314#pullrequestreview-879521825", "body": ""}
{"comment": {"body": "Roger\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/314#discussion_r804128508"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/314#pullrequestreview-879523140", "body": ""}
{"title": "Inspired by Richard Bresnan", "number": 3140, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3140", "body": "This pr was inspired by a pr made long time ago in Skywagon days that linted out expressions in log statements.\nRichie once told me, why the hell are you doing variable evaluation in a log statement, why arent you indexing it?\nAnd hes right."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3140#pullrequestreview-1124279602", "body": "awesome, thank you!"}
{"comment": {"body": "```suggestion\r\n        \"logger\",\r\n        \"log\",\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3140#discussion_r982806955"}}
{"comment": {"body": "what's \"t\"?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3140#discussion_r982809797"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3140#pullrequestreview-1124290631", "body": ""}
{"comment": {"body": "exception.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3140#discussion_r982814519"}}
{"title": "Reduce unread dot indicator size by 2pt", "number": 3141, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3141", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3141#pullrequestreview-1124280470", "body": ""}
{"title": "Ability to toggle full recalculation ON/OFF via environment", "number": 3142, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3142"}
{"comment": {"body": "You had me at toggle.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3142#issuecomment-1261548462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3142#pullrequestreview-1124445167", "body": ""}
{"title": "Reduce disclosure size", "number": 3143, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3143"}
{"title": "Tweak sourcemark benchmark resource metrics", "number": 3144, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3144", "body": "Changes\n\ntime values in seconds\nmemory values in MB\nadd wallclock time\n\nAlso\n\ndefer upstream of new points till after full recalculation\nnew env vars to control SM internal debugging:\nSOURCEMARK_DEBUG\nSOURCEMARK_VERBOSE"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3144#pullrequestreview-1124395896", "body": ""}
{"comment": {"body": "fixlint", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3144#discussion_r982889063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3144#pullrequestreview-1124396907", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3144#pullrequestreview-1124434451", "body": "I'm excited by this pr.\nYou had me at benchmark."}
{"title": "Disable test", "number": 3145, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3145"}
{"title": "Fix invite email", "number": 3146, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3146", "body": "In the case the user has auth'd but the team has not auth's and the user does not have vscode, we do another invite flow such that we send an invitation to another person without team context.\nThis email will only ask to install unblocked."}
{"title": "goodbye aws pinpoint, mahdi hates you", "number": 3147, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3147"}
{"title": "cleanup db logs", "number": 3148, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3148"}
{"comment": {"body": "You had me at \"cleanup\"", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3148#issuecomment-1261717428"}}
{"title": "Add inviteToChannel() to VideoChannelService", "number": 3149, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3149"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3149#pullrequestreview-1124466850", "body": ""}
{"title": "Store ingested pull request review threads in the db", "number": 315, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315", "body": "Super rough first pass at creating the required database entries for PR ingestion."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879628953", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879629744", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879631899", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879632639", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879758377", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879759052", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879759731", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879763195", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879767690", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-879772633", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-880468649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-880469569", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-880478617", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-880481106", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-880577286", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-880598821", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-880751662", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-880800501", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-881953713", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-881953855", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-881954768", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-882268452", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-890332436", "body": ""}
{"comment": {"body": "FML. I keep on thinking Java threads.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#discussion_r812335799"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-890332841", "body": ""}
{"comment": {"body": "Haha, skywagon anyone. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#discussion_r812336085"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/315#pullrequestreview-890338151", "body": ""}
{"title": "Update context init", "number": 3150, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3150", "body": "Potentially fixes issue where VSCode contexts become out of sync with auth state. This manifests with incorrect sidebar states.\n\nAs of right now, HubAuth stream will send an event whenever its auth state changes (minute or so.) This may trigger VSCode's setupAuth which will reinitialize install & auth context to false. \nFix is to move the initialization of install & auth context outside of setupAuth."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3150#pullrequestreview-1124481077", "body": ""}
{"title": "Show client web extension versions in admin web", "number": 3151, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3151"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3151#pullrequestreview-1124491579", "body": ""}
{"title": "Update VideoChannel modifiedAt on participant updates", "number": 3152, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3152", "body": "Needed for optimized reads of channel activity for poller and videoChannels/mine"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3152#pullrequestreview-1125973864", "body": "one rare issue that might be worth addressing"}
{"comment": {"body": "Since trx is non-null:\r\n```suggestion\r\n    ) = trx.run {\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3152#discussion_r984006134"}}
{"comment": {"body": "This might sound weird/impossible, but it's possible due to the way PG works that `it[this.modifiedAt]` is greater than `updated`. In that scenario, you would be moving the `modifiedAt` back in time, which would cause issues for our poller.\r\n\r\nCan address this by adding the predicated write clause to the where statement:\r\n```\r\nVideoChannelModel.modifiedAt less updated\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3152#discussion_r984009072"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3152#pullrequestreview-1125976364", "body": ""}
{"comment": {"body": "jupiter API for `assertNotNull` doesn't provide an unwrapping interface :( ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3152#discussion_r984007808"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3152#pullrequestreview-1126019815", "body": ""}
{"comment": {"body": "Do this and we lose telemetry data", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3152#discussion_r984037071"}}
{"title": "Fix bug in DiffStat parser", "number": 3153, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3153", "body": "handle files with tabs, spaces, newlines, quotes\nhandle empty diffstat for binary files"}
{"title": "Initial VSCode + Video app setup", "number": 3154, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154", "body": "Setup basic grpc communication between vscode + video app.\nFeature flagged. \n"}
{"comment": {"body": "I think this PR makes sense, although we're going to have to make some behavioural changes in the very near future. I don't think it makes sense for the `newChannel` response to be the green light indicator for successful channel creation. Consider the following flow:\r\n\r\n1. User has never launched the video app before\r\n2. User makes a call\r\n3. Video app pops up, permissions displayed\r\n4. Permissions granted asynchronously\r\n5. Maybe the video app restarts\r\n6. Video app finally places call\r\n\r\nThere are a number of places in this flow where the GRPC response to both `newChannel` and `stateStream` will fail, but yet those failures are not a real indication of overall failure. \r\n\r\nWe'll need to think about how to deal with these situations", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#issuecomment-1262829852"}}
{"comment": {"body": "I guess the only important information that VSCode receives is that the video app has been launched.\r\n\r\nOutside of that, I don't foresee VSCode really listening to the stream? Majority of the metadata (presence) will most likely come from API service\r\n\r\nBut overall I agree. This current implementation is on the \"happy\" case. There's going to be edge cases that VSCode will need to gracefully handle.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#issuecomment-1262843898"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1125957600", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126008057", "body": ""}
{"comment": {"body": "Is it important for VSCode to be fairly reactive if the Hub launches a video conversation?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984028585"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126009250", "body": ""}
{"comment": {"body": "Have you tested whether this launches multiple instances?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984029440"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126010345", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126011325", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126030232", "body": ""}
{"comment": {"body": "When working on this, I had a bug which called this multiple times. I think it did launch multiple windows of the video player but not multiple instances of the app? (aka only a single icon in dock)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/3997fe0f-2215-454c-86ea-f0fec216c92c?message=2103839d-283b-4471-85cc-a4a0834d1264).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984044460"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126031247", "body": ""}
{"comment": {"body": "This has nothing to do with hub launching video. It's used when retrying to launch the video app.\r\n\r\nIMO, VSCode should get any metadata about that conversation from the API service & not the video app.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984045195"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126036985", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126039607", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126040366", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126040718", "body": ""}
{"comment": {"body": "Hmm this is bad news for us. I'll try to see what can be done here to ensure only a single window is possible", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984051708"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126041370", "body": ""}
{"comment": {"body": "With this implementation, we could possibly call it multiple times if launching is successful but we fail to connect with the grpc service.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/3997fe0f-2215-454c-86ea-f0fec216c92c?message=e62aed00-f31b-4034-a240-c19d47a6e14a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984052183"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126042100", "body": ""}
{"comment": {"body": "Hmm yeah we might want to separate those two actions. But maybe not since \"launching\" should be idempotent...\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/3997fe0f-2215-454c-86ea-f0fec216c92c?message=89ef8252-25ff-4eb0-b1a7-62975ca55b3f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984052707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126045182", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126051429", "body": ""}
{"comment": {"body": "Man I forgot that grpc is ancient and doesn't support promises.  Should we use a technique like https://github.com/grpc/grpc-node/issues/54#issuecomment-1057215464 and wrap the calls so that we get nicer API usage?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984059244"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126053229", "body": ""}
{"comment": {"body": "Yowza \ud83e\udd2a ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984060511"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126053803", "body": ""}
{"comment": {"body": "Should we be using some API to find this directory? Feels like we're depending on an OS implementation detail and might be fragile?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984060988"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126056401", "body": ""}
{"comment": {"body": "This seems like the tool to use?\nHandles multiple platforms and is native to NodeJS\n\nhttps://nodejs.org/api/os.html#oshomedir", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984062891"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126056929", "body": ""}
{"comment": {"body": "No, I meant the logic for determining where on macos the shared container lives.\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d239e951-9cb2-4481-a130-20ce981101c2?message=e5226de1-a98c-4d13-b8f0-67010a7e1aa2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984063289"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126060460", "body": ""}
{"comment": {"body": "Ahh right... This is a limitation with app group containers.\n\n\n\n[https://chapter2global.slack.com/archives/C02HEVCCJA3/p1664472308223539?thread_ts=1664471117.550489&cid=C02HEVCCJA3](https://chapter2global.slack.com/archives/C02HEVCCJA3/p1664472308223539?thread_ts=1664471117.550489&cid=C02HEVCCJA3)\n\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1664472308223539?thread_ts=1664471117.550489&cid=C02HEVCCJA3\n\nI've done some digging and I believe Peter has probably done way more digging but this seems like the only way right now?\n\n\n\n@pwerry Do we have to put these two apps into the same app container?\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d239e951-9cb2-4481-a130-20ce981101c2?message=f7024481-973b-4cf6-8992-7208656f32e0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984065860"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1126076982", "body": ""}
{"comment": {"body": "It's really the only way the Hub can know where to connect to unless we lift the sandbox restrictions on one of the apps\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d239e951-9cb2-4481-a130-20ce981101c2?message=eebac353-062b-4433-89b0-5eb64019e334).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r984078265"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1127408856", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-1346695341", "body": ""}
{"comment": {"body": "This is anotheer test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r1140679951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is anotheer test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r1140680039"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is anotheer test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3154#discussion_r1140680040"}}
{"title": "Account for SM usage more accurately", "number": 3155, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3155"}
{"title": "Refactor sourcemark/thread loading", "number": 3156, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156", "body": "Refactor how we load per-file sourcemarks and associated files.\nPreviously, the loading logic was built into TextEditorSourceMarks -- that class managed SM/thread loading, rendering, and editor states.  WIth his PR:\n* FileSourceMarkStreams: given a file path, vends a stream that returns resolved sourcemark and thread data.  Under the hood this resolves the sourcemarks, loads the threads, and joins the data.  The class tracks created streams, so there is only one outstanding active stream per file (ie, when multiple UIs stream data for the same file, it is reused)\n* TextEditorSourceMarks uses this stream, and is now much simpler: its purpose is now, given a stream of SMs/threads for a file, render those SMs in the editor gutters.\n* CurrentFileInsightsStream: Builds on top of FileSourceMarkStreams -- for the currently-active file, returns the threads relevant to that file.  The variety of UIs that display \"insights for current file\" now use this stream.\nI am working on unit tests for this and will commit that in a subsequent PR"}
{"comment": {"body": "> Outside of simplifying the code, what benefits are there to having individual streams for file paths instead of editors? I would assume we _only_ care about threads for files that have an active editor?\r\n\r\nThere are two scenarios we've already run into where the old structure was awkward:\r\n* Our webviews (which are not TextEditors) can publish active files, and thus break this model.  For instance, right now, you can display a read-only file UI (which is a webview), in ways that breaks the \"Insights in this file\" UI.\r\n* The codelens UI broke the old model, because the TextEditorSourceMarksManager was only tracking visible editors, whereas the codelens API we have to implement required us to track any arbitrary editor. With this change, the codelens code can just ask for the stream it wants, by file.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#issuecomment-1262817029"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1125986370", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1125992439", "body": "I think it all makes sense. Doesnt really change the functionality but seems simpler to think about.\nOutside of simplifying the code, what benefits are there to having individual streams for file paths instead of editors? I would assume we only care about threads for files that have an active editor?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1127293382", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1128628009", "body": ""}
{"comment": {"body": "Is missing threadInfo is an error case in this situation?\r\nIf so, I think adding at least some logs would help debug this issue in the future.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#discussion_r985954719"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1128633166", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1128644699", "body": ""}
{"comment": {"body": "Not specific to this PR since we do this in a few places but always felt off fetching the source point with `[0]`.\r\n\r\nWe're making the assumption that there's always at least / exactly one source point? And what does that source point represent? Latest? Original?\r\n\r\nReading / refactoring this isn't ideal.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#discussion_r985966348"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1128660492", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1128729976", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1128740165", "body": ""}
{"comment": {"body": "It is expected that we might not have ThreadInfo at this point.  This is joining the two streams (sourcemarks and threads) together.  The resulting received events looks like:\r\n* [sourceMarks: [sm1, sm2, ...], threadInfos: [] ] -- we receive the initial set of sourcemarks for this file (but we have no threads yet).  The initial SMs tell us which threads are relevant (each SM has a sm.threadId)\r\n* [sourceMarks: [sm1, sm2, ...], threadInfos: [thread1, thread2, ...] ] -- once all threads are received", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#discussion_r986032440"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1128746082", "body": ""}
{"comment": {"body": "I'll add a comment to this point", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#discussion_r986036603"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1128747693", "body": ""}
{"comment": {"body": "The source mark engine API we use guarantees that there will be exactly one source point, containing the point that is relevant to the current codebase.  I'm not sure, would a comment here help?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#discussion_r986037737"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3156#pullrequestreview-1128830759", "body": ""}
{"title": "UserIcon Popover", "number": 3157, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3157", "body": "Update UserIcon to take in popover in preparation for Video work.\n"}
{"comment": {"body": "Current iteration. Popover content be cleaned when we hook up the data.\r\n<img width=\"454\" alt=\"CleanShot 2022-09-29 at 14 31 23@2x\" src=\"https://user-images.githubusercontent.com/1553313/193145876-13ef3f76-48ba-4f53-af12-b33f774c62e8.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3157#issuecomment-1262840570"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3157#pullrequestreview-1126060308", "body": ""}
{"comment": {"body": "How generic is this?  Feels like placement that is specific to the user icon?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3157#discussion_r984065761"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3157#pullrequestreview-1126065508", "body": ""}
{"title": "Implements videochannels/mine", "number": 3158, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3158"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3158#pullrequestreview-1126077267", "body": ""}
{"title": "Added debug message to figure out spawn EBADF issues", "number": 3159, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3159"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3159#pullrequestreview-1126041674", "body": "This is so beautifl!!!1"}
{"title": "Add modifiedAt field with update triggers", "number": 316, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316", "body": "Problem\nNeeded for Push and response caching. \nSummary\nAdded a store procedure to update modifiedAt timestamps, and then we register a trigger for each table to fire at the proc whenever UPDATE is called\nTrigger operation syntax and examples here: "}
{"comment": {"body": "FYI:\r\nhttps://github.com/paulkagiri/ExposedDatesAutoFill/blob/master/src/main/kotlin/app/Models.kt\r\n\r\nTake a look at that for an alternative implementation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#issuecomment-1035647385"}}
{"comment": {"body": "> FYI: https://github.com/paulkagiri/ExposedDatesAutoFill/blob/master/src/main/kotlin/app/Models.kt\r\n> \r\n> Take a look at that for an alternative implementation.\r\n\r\nYeah this is my preferred approach, but I'm not sure if this works if updating models with DSL (i.e. does this only work with DAO updates?). Maybe for the few cases where we use the DSL to update objects we remember to set `modifiedAt`? Oof.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#issuecomment-1035653217"}}
{"comment": {"body": "> FYI: https://github.com/paulkagiri/ExposedDatesAutoFill/blob/master/src/main/kotlin/app/Models.kt\r\n> \r\n> Take a look at that for an alternative implementation.\r\n\r\nI avoided this approach for the reason that David mentioned - if you modify the DB with raw SQL or use DSL this will break. See `EntityHook.kt` - the hooks are fired based on DAO events", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#issuecomment-1035657429"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879639118", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879640753", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879640960", "body": ""}
{"comment": {"body": "What happens if someone creates a table doesn't have `modifiedAt` column? I know you added `modifiedAt` to the base class above but curious to know what happens if someone moves that then creates a new table without the column.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#discussion_r804232969"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879641380", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879643975", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879645710", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879648640", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879650697", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879658393", "body": ""}
{"comment": {"body": "@richiebres just double checking: `editedAt` is the timestamp of the last user edit (if edited) which may be (but not necessarily) equal to `modifiedAt`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#discussion_r804251051"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879679123", "body": ""}
{"comment": {"body": "Good point - I can add a null check. To answer the question - it will bomb if operating on a table with no `modifiedAt` column", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#discussion_r804267850"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879692394", "body": ""}
{"comment": {"body": "Scrap - nullability != existence. There is no way to protect short of swallowing the exception within the trigger function, which is super gross. The trigger function has access to the schema name `TG_TABLE_SCHEMA` but I don't see an obvious way to use that without incurring a cost", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#discussion_r804278621"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879694514", "body": "This is okay to me.\nDon't know how else you're going to do this other than the Entity monitoring and that has its pitfalls as well."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/316#pullrequestreview-879706767", "body": ""}
{"title": "Push for video channel changes", "number": 3160, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3160", "body": "Like it says on the tin"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3160#pullrequestreview-1127069405", "body": ""}
{"title": "move to latest ktlint", "number": 3161, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3161", "body": "The ktlint-gradle guys are way behind in ktlint support (and they are refusing to upgrade), so I just forked and fixed it up here:\n\nWe now can move to latest ktlint which has a whole bunch of new useful rules."}
{"title": "Setup Retries", "number": 3162, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162", "body": "Issue where unexpected 401s can occur if there's a large gap between a refresh and its API request within tokenRefreshMiddleware\nWe will now retry the refresh process if we get a 401 and the current status is unauthenticated.\nRequired some type hackery with node-fetch to allow for updating the stream buffer high water mark."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#pullrequestreview-1126202416", "body": ""}
{"comment": {"body": "Some type hackery since the models within node-fetch do not 100% match the standard fetch models.\r\n\r\nFunctionally should be identical, especially since it's only called within the codegen.  ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#discussion_r984174799"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#pullrequestreview-1126202880", "body": ""}
{"comment": {"body": "Updating HWM as node js default is far too small for some of our requests.\r\n\r\nDefault value is 16KB... Our thread requests and source mark requests are almost 100KB\r\nhttps://github.com/node-fetch/node-fetch/pull/671#issuecomment-530972334", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#discussion_r984175170"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#pullrequestreview-1128767218", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#pullrequestreview-1128810887", "body": ""}
{"comment": {"body": "Is there a reason we do this in middleware as opposed to updating `_refreshAuth` to retry?  Doing this in middleware seems to add complexity (we have to determine if this is a refresh request, we have to tie in a bunch of other state data into this request, etc)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#discussion_r986081855"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#pullrequestreview-1128818673", "body": ""}
{"comment": {"body": "Ahh never mind I get it -- the point here is we think there could be (very infrequent) random 401 failures, so if we get a 401, and we seem to be unauthenticated, we will try to re-auth, and if that succeeds re-request.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#discussion_r986087376"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3162#pullrequestreview-1128820268", "body": ""}
{"title": "Merge commit correctness and perf fixes", "number": 3163, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3163", "body": "Addresses correctess issue at merge commits, where changes to the file were completely\n  omitted along some incident branches. We were supposed to get the diff between the\n  non-treesame commits for that file; but we incorrectly got the diff between non-treesame\n  commits for any file which rarely worked correctly.\n\n\nShould significantly improve performance of diffs calculated for edges incident on merge\n  commits in the case where the sourcemark was refactored between files. We were supposed to\n  compute the other files diffs for changes in the same commit that the sourcemark file was\n  changed; but we incorrectly computed the other files diffs for all changes from the changed\n  commit to the merge commit, which could contain 100s of commits and 1000s of files."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3163#pullrequestreview-1126305324", "body": ""}
{"title": "New approach to getting top-N sourcemarks per file", "number": 3164, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3164", "body": "More of a brute force approach, but will always be accurate because\nit uses exactly the same API used to fetch sourcemarks for a file."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3164#pullrequestreview-1126387224", "body": ""}
{"title": "SM engine handles very large diffs better", "number": 3165, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3165", "body": "fixes: \nfixes: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3165#pullrequestreview-1126387678", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3165#pullrequestreview-1127396337", "body": "There's something about this pr that feels right."}
{"title": "Simplify single video channel push query", "number": 3166, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3166", "body": "We no longer have to check for participant updates because we're updating the channel modifiedAt whenever there are participant table changes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3166#pullrequestreview-1127082639", "body": ""}
{"title": "Delete thread if entire PR thread is deleted in GitHub", "number": 3167, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3167", "body": "Note that this is a hard delete of the thread since it will no longer exist in GitHub"}
{"title": "Fix some ThreadStore bugs", "number": 3168, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3168", "body": "Only send threads back to the stream once we have data for all requested threads.  Before we would return partial cached results.\nAlways send the full set of threads back to the stream.  Before, we would only send the updated threads\nHandle the empty ready state correctly (when some code requests no threads)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3168#pullrequestreview-1127406919", "body": ""}
{"title": "Allow only a single window for chat app, and close app when window is closed", "number": 3169, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3169"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3169#pullrequestreview-1127413076", "body": "Thankfully it was a small change..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3169#pullrequestreview-1127413925", "body": ""}
{"comment": {"body": "This cryptic code has the effect of both removing \"new window\" from the app menu, and limiting the app to only a single window. Proven to work even when calling `open -b 'com.nextchaptersoftware.UnblockedVideoChat'` multiple times", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3169#discussion_r984987485"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3169#pullrequestreview-1127414134", "body": ""}
{"comment": {"body": "This makes me both laugh and cry", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3169#discussion_r984987649"}}
{"title": "Make docker use the same postgres version as our AWS instance", "number": 317, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/317"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/317#pullrequestreview-879665679", "body": ""}
{"title": "Archive closed PR threads", "number": 3170, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3170", "body": "...and restore if the PR is reopened."}
{"title": "Revert \"SM engine handles very large diffs better\"", "number": 3171, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3171", "body": "Reverts NextChapterSoftware/unblocked#3165\nBad results in terms of mem/runtime. Back to drawing board..."}
{"comment": {"body": "This breaks my heart for a number of reasons too numerous to list here ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3171#issuecomment-1264212256"}}
{"title": "Further optimize getTopFiles", "number": 3172, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3172", "body": "prefetch as soon as sourcemark download completes\njust run on 3 file"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3172#pullrequestreview-1128807163", "body": ""}
{"title": "Add basic team information", "number": 3173, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3173"}
{"title": "SM engine handles very large diffs better", "number": 3174, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3174", "body": "Problem\nSometimes we encounter very large diffs caused by a developer merging a long lived side branch,\nrunning prettier across and entire code base, checking in generated code, or checking in large\nfiles for whatever reason.\nSolution\nApply a bunch of heuristics, almost all of which sacrifice correctness for performance.\n\nIgnore generated files\nAssume that files can only be moved to other files matching the original file extension\nExclude deleted files from the diff, since a mark cannot be moved to a deleted file.\nExclude large file diffs completely if the diff is larger than 1000 lines.\n\n"}
{"comment": {"body": "Man, seems like someone is on a roll", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3174#issuecomment-1266194036"}}
{"title": "Fix bug in getTopFiles prefetch", "number": 3175, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3175", "body": "Prefetch was doing nothing useful. Now it's doing what it's supposed to.\n{\n  context: 'SourceMarkCalculator',\n  service: 'vscode',\n  environment: 'local',\n  type: 'nodejs',\n  process: 'extension',\n  teamId: '9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361',\n  repoId: '7fce6ebb-5989-48e7-9af0-fff594769a8a',\n  repoRootPath: '/Users/<USER>/work/NextChapterSoftware/unblocked',\n  pid: 33014,\n  file: FilePath { value: 'api/private.yml' },\n  level: 'error',\n  message: 'getFileRelativeToRepo: expected file to be absolute',\n  timestamp: '2022-10-03T19:22:47.968Z'\n}"}
{"title": "Updates the Video App sandbox relationship with the Hub", "number": 3176, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3176", "body": "This doesn't get us all the way there on the experience we're after, but these changes are a necessary starting point to set up the parent/child permissions relationship with the Hub"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3176#pullrequestreview-1134929064", "body": ""}
{"comment": {"body": "Probably doesn't matter much, but with us focusing on 1-to-n walkthrough UIs, these messages aren't really meaningful anymore", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3176#discussion_r990398340"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3176#pullrequestreview-1134945197", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3176#pullrequestreview-1134945434", "body": ""}
{"comment": {"body": "Maybe put this in a constant somewhere so we're not duplicating it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3176#discussion_r990409431"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3176#pullrequestreview-1135103059", "body": ""}
{"comment": {"body": "Yeah this is going to need to change for the top level app. I'll tackle this when I bring permissions back to the walkthrough app\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/3f3020e3-ce85-4a21-8aa0-3b4753d7fb7d?message=36898678-dac0-46b1-b163-153b84901d9d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3176#discussion_r990520511"}}
{"title": "Clean up function", "number": 3177, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3177"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3177#pullrequestreview-1128964994", "body": ""}
{"comment": {"body": "This is the clean up", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3177#discussion_r986187844"}}
{"title": "Fix getTopFiles prefetch again", "number": 3178, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3178"}
{"title": "Add logging to debug why thread title isnt updating with message deletion", "number": 3179, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3179"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3179#pullrequestreview-1129230805", "body": ""}
{"title": "Propose modified since status API", "number": 318, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318", "body": "Problem\nTo facilitate our fast-polling and caching strategy, we need a simple interface for clients to ask whether a particular entity or set of entities has been modified since a particular date. \nProposal\nAfter speaking with Matt, we concluded that this would be most easily implemented as a POST request with a request body. OpenAPI does support header definitions but the support is not great for kotlin server so we would have to write our own templates etc. \nRequest:\n{\n    \"channels\": [\n        {\n            \"channel\": \"/threads\",\n            \"ifModifiedSince\": \"2022-02-03T06:45:48Z\"\n        }\n    ]\n}\nResponse:\n{\n    \"channels\":[ \"/threads\" ]\n}\nThoughts for the future\nIf we want true http response caching, we probably do need to implement support for OpenAPI header params"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-879744500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-879748463", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-879748823", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-879751103", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-879809302", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-879816102", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-880725362", "body": ""}
{"comment": {"body": "Doesn't matter much for now, but: would it make sense to treat ifModifiedSince as an opaque token from the client's point of view?  The client should always just be feeding back whatever values it gets from the service anyways..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#discussion_r805035405"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-880726606", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-880726827", "body": ""}
{"comment": {"body": "For sure. The typing is useful though, because it gives us some input validation for free", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#discussion_r805037016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-880740474", "body": ""}
{"comment": {"body": "change\r\n```\r\nPOST /channels/modifedSince\r\n```\r\n\r\nto\r\n```\r\nPOST /channels/batch -> 200 { [:channel] }\r\nHEAD /channels/:channel -> 200,3xx\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#discussion_r805047320"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-965397510", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#discussion_r867439073"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-965399102", "body": ""}
{"comment": {"body": "ping\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#discussion_r867441251"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-966491169", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#discussion_r868211847"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#pullrequestreview-966506394", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/318#discussion_r868218313"}}
{"title": "[BREAKS API ON MAIN] AddSlackTeamIngestion2", "number": 3180, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3180", "body": "Slack team ingestion\nAdd new enum"}
{"title": "Pass thread ID", "number": 3181, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3181"}
{"title": "Remove unused import", "number": 3182, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3182", "body": "So confused why this isn't getting caught in CI"}
{"title": "Sourcemark engine excludes file types by extension", "number": 3183, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3183", "body": "Sourcemark engine excludes file types by extension, fixing #3174.\nAlso skip pre-computed nodes"}
{"title": "More efficient sourcemark token searching across files", "number": 3184, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3184", "body": "Main changes\n\nLeverage git diff -G to search for tokens from the original snippet when there\n  are a large number of candidate files to search. Git is several orders of\n  magnitude faster at searching for text than node application is.\n\nOther changes\n\nWhen searching for inserts across files, skip parsing lines removed from the\n  diff since we never inspect these hunks.\nUpstream newly computed sourcepoints on the fly, but ensure that we have a\n  significant batch to amortize the network overhead.\nFull recalculation loop iterates by file, instead of by chunks-of-files. This\n  was leading to memory spikes.\nFull recalculation no longer has a CPU throttle, because it was pretty useless."}
{"comment": {"body": "You had me at efficient...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3184#issuecomment-1275401590"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3184#pullrequestreview-1138264335", "body": ""}
{"title": "Found a faster Git native way to compute content hashes", "number": 3185, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3185", "body": "hyperfine --warmup 3 'git rev-list -1 --objects HEAD -- README.md' 'git rev-parse HEAD:README.md'                                                                  5.6s\nBenchmark 1: git rev-list -1 --objects HEAD -- README.md\nTime (mean  ):       7.0 ms    0.5 ms    [User: 5.3 ms, System: 1.2 ms]\n  Range (min  max):     6.4 ms    9.7 ms    325 runs\nBenchmark 2: git rev-parse HEAD:README.md\nTime (mean  ):       2.4 ms    0.3 ms    [User: 1.4 ms, System: 0.7 ms]\n  Range (min  max):     2.0 ms    4.0 ms    688 runs\nSummary\n'git rev-parse HEAD:README.md' ran\n    2.91  0.38 times faster than 'git rev-list -1 --objects HEAD -- README.md'"}
{"comment": {"body": "> Is there any way to tell which options are supported in various versions of git? I've never known how to answer the question \"Will this work on our oldest-supported git or not?\"\r\n\r\n@matthewjamesadam CI regression testing with an old version of Git is the correct way to do this. In the meantime, I run all new commands locally using a very old version (2.0.5) and this one is fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3185#issuecomment-1267415963"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3185#pullrequestreview-1130406514", "body": "Is there any way to tell which options are supported in various versions of git?  I've never known how to answer the question \"Will this work on our oldest-supported git or not?\""}
{"title": "[BREAKS API ON MAIN] Updated spec for VSCode Walkthrough", "number": 3186, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186", "body": "Current plan is to reuse existing models as much as possible.\nA walkthrough is just another type of thread.\n\nThe main difference from a the view standpoint is a new VideoBlock within MessageContent. \nCreating a walkthrough from VSCode can use the existing CreateThreadRequest models\n\nVideo will be uploaded and attached to MessageContent, similar to existing image assets.\nRelatedFiles / Anchor points maps to CreateMessageRequest.sourcemarks. Each file will be its own sourcemark. We will have n source marks -> 1 thread.\nTeamMembers = CreateThreadRequest.threadParticipants"}
{"comment": {"body": "<img width=\"662\" alt=\"CleanShot 2022-10-04 at 16 05 56@2x\" src=\"https://user-images.githubusercontent.com/1553313/193946602-55498034-03c0-4ffd-8816-98b5f4ee3dbb.png\">\r\n\r\nDesigns just changed :)\r\n\r\nWith this, it's not as simple as just rendering a video anymore.\r\nThinking of moving this out of MessageContent unless we think there's merit to keeping it around for PR ingestion?\r\n@davidkwlam ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#issuecomment-1267701149"}}
{"comment": {"body": "> Thinking of moving this out of MessageContent unless we think there's merit to keeping it around for PR ingestion?\r\n\r\nI don't think this would impact PR ingestion since we don't have video walkthroughs in PR threads", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#issuecomment-1267703640"}}
{"comment": {"body": "> > Thinking of moving this out of MessageContent unless we think there's merit to keeping it around for PR ingestion?\r\n> \r\n> I don't think this would impact PR ingestion since we don't have video walkthroughs in PR threads\r\n\r\nNot walkthrough specifically but just generic videos. One thought of adding this was to add support for videos in descriptions, comments, etc..\r\nFor example https://github.com/NextChapterSoftware/unblocked/pull/3154", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#issuecomment-1267707414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-1130624456", "body": ""}
{"comment": {"body": "I think this is a breaking change?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#discussion_r987337899"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-1130627977", "body": ""}
{"comment": {"body": "I think all our clients support new enum values gracefully now, so this should be fine.  Clients that aren't expecting a 'walkthrough' thread, for instance, will just render it with a generic thread icon (blue bubble I think)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#discussion_r987340605"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-1130633272", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-1130707535", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-1130874394", "body": ""}
{"comment": {"body": "\ud83d\udc4d\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2584216a-11cd-4450-8d44-1df7e738dea8?message=cdeea536-17b8-4cb0-8475-cbfcedcce9dd).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#discussion_r987527897"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-1131982801", "body": ""}
{"comment": {"body": "maxLength seems to be a necessary field in the spec. Do we just set this to an arbitrary large number?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#discussion_r988307318"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-1131993321", "body": ""}
{"comment": {"body": "This brings up the question of whether the transcription should be included here, or whether it should be a separate item that is fetched on demand... I'm fine with this for now and we'll see?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#discussion_r988314680"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3186#pullrequestreview-**********", "body": ""}
{"title": "Minor provider changes", "number": 3187, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3187", "body": "No longer just scm providers..."}
{"title": "FileSourceMarkStream tests", "number": 3188, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#pullrequestreview-**********", "body": "There's something about this change that brings a smile to my face.\nI'm not sure what it is, I'm not sure how it is, but it is.\nThank you Matt."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Why does this need to be lazy?\r\nNone of this logic should be activated until it has a subscription?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#discussion_r987310895"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#pullrequestreview-1130585627", "body": ""}
{"comment": {"body": "Does this need to be lazy?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#discussion_r987311176"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#pullrequestreview-1130609256", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#pullrequestreview-1130609792", "body": ""}
{"comment": {"body": "Chatted with Matt.\r\nIf the overall stream wasn\u2019t lazy, we would still initialize ActiveFileManager & RepoStore on import and would defeat the purpose of those being lazy.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#discussion_r987327794"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think ideally all these streams would be fully lazy so that creating them was effectively free... but we're not at that point right now.  This is a bit of a downside for using `createValueStream`, as it doesn't force you to think through handling subscribe/unsubscribe states.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3188#discussion_r987357799"}}
{"title": "Rename scmproviders", "number": 3189, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3189"}
{"title": "Update VScode environments", "number": 319, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/319", "body": "Update VSCode environments to support local & deployed dev environments."}
{"comment": {"body": "This is producing different builds for different environments (ie, in the webpack conf) -- should we be doing this?  Or building all the conf information into the app and selecting the environment to use at runtime?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/319#issuecomment-**********"}}
{"comment": {"body": "> This is producing different builds for different environments (ie, in the webpack conf) -- should we be doing this? Or building all the conf information into the app and selecting the environment to use at runtime?\r\n\r\nIn this situation, the differing environment variables are so insignificant that I'm personally okay with the separate builds?\r\nThey're just string literals that are used for endpoints. It has no affect on what is actually built.\r\n\r\nI think we would want to revisit this if these configs were like feature flags that can actually alter build time such as tree shaking. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/319#issuecomment-1036495879"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/319#pullrequestreview-879798761", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/319#pullrequestreview-880692309", "body": ""}
{"title": "PullRequest API model indicates if it has slack threads or not", "number": 3190, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3190", "body": "See the 'Pull Requests Discussions' section below:\n\nWe need a way to differentiate PRs that have slack threads from PRs that do not, so we can render a different icon.  I'm not sure if this is the best way to model this or not."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3190#pullrequestreview-**********", "body": ""}
{"title": "Add slack provider", "number": 3191, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3191"}
{"title": "Return hasSlackThreads in getPullRequestsForCommits operation", "number": 3192, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3192"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3192#pullrequestreview-**********", "body": ""}
{"title": "Video Walkthrough Proto", "number": 3193, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193", "body": "Spec for communication between VSCode + Video Walkthrough App."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1131706592", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1131708955", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133455535", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133456494", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133458385", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133460491", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133462400", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133463456", "body": ""}
{"comment": {"body": "VSCode needs to deliver these to the walkthrough app somehow for collection. Then the video app will echo these back when the session is complete right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#discussion_r989361307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133466311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133468373", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133508267", "body": ""}
{"comment": {"body": "That's correct. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#discussion_r989392091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133510697", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133514054", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133603028", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133683526", "body": ""}
{"comment": {"body": "Maybe add `teamId`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#discussion_r989516639"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3193#pullrequestreview-1133745318", "body": ""}
{"title": "Create SlackURL data class to extract slack urls from PR descriptions and comments", "number": 3194, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3194", "body": "The regex probably needs some work"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3194#pullrequestreview-1130756582", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3194#pullrequestreview-1130757332", "body": "Might wnat to take a look at PullRequestUrl and RepoUrl as we use those sort of paradigms."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3194#pullrequestreview-1131778280", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3194#pullrequestreview-1131779173", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3194#pullrequestreview-1131780285", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3194#pullrequestreview-1131827102", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3194#pullrequestreview-1131829283", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3194#pullrequestreview-1131991603", "body": ""}
{"title": "Create a new walkthrough app", "number": 3195, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3195", "body": "Creates a new app that is a member of the shared container. There's some UI code for buttons I'm slipping in here too but it's pretty straightforward. \nIPC service is bootstrapped and (currently) uses the same default location and key for the IPC port as the VideoChatApp. \nFor launch purposes, the bundleId of this app is com.nextchaptersoftware.UnblockedWalkthroughApp"}
{"comment": {"body": "Are a bunch of these images shared between apps?  Should we build a single asset bundle and get images from there so we aren't duplicating everything?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3195#issuecomment-1269000858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3195#pullrequestreview-1132051456", "body": ""}
{"comment": {"body": "Remove?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3195#discussion_r988355477"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3195#pullrequestreview-1132051559", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3195#pullrequestreview-1133655201", "body": ""}
{"comment": {"body": "It's modified in a subsequent PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3195#discussion_r989494402"}}
{"title": "Linearize commit propagation", "number": 3196, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3196", "body": "Too many performance, memory and correctness issues with merge commits.\nInstead we linearize the commit traversal.\nLimitation is that only one of the paths inbound to a merge commit will be processed,\nmeaning that sourcemarks may not be visible in the web extension for a file at a\ncommit within the non-processed path. Probably not a big deal given that most file\nbrowsing occurs on the main line and at or near HEAD."}
{"title": "Split into distinct slack apps for dev and prod", "number": 3197, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3197"}
{"title": "Await auth check", "number": 3198, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3198", "body": "When a refresh token is expired, sharedSetupAuth or sharedRefreshAuth will throw an error.\nThis should be caught and trigger a logout but since we were not waiting for it, logout was never triggered..."}
{"comment": {"body": "> Explicit typing would have caught this right?\r\n\r\nSince the parent function is async, this is type-safe. Maybe I'm missign something Pete?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3198#issuecomment-1268757222"}}
{"comment": {"body": "> > Explicit typing would have caught this right?\r\n> \r\n> Since the parent function is async, this is type-safe. Maybe I'm missing something Pete?\r\n\r\nAhh sorry I see the issue. It was returning a promise when it should have been returning a concrete value since this is the top level handler. Is that right? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3198#issuecomment-1268759368"}}
{"comment": {"body": "Typing wasn't the issue.\r\nIt's that we wanted to actually catch the error at this level, not higher up in the call-stack.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3198#issuecomment-1268761945"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3198#pullrequestreview-1131826200", "body": "Explicit typing would have caught this right?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3198#pullrequestreview-1131827200", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3198#pullrequestreview-1131829088", "body": ""}
{"title": "Source mark fails to track moved file move at boundary", "number": 3199, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3199", "body": "\nAlso relax the text similarity difference within a file."}
{"title": "Capitalize util files", "number": 32, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/32"}
{"comment": {"body": "Think Jeff's recent changes mitigate the need for this PR. Closing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/32#issuecomment-1013384431"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/32#pullrequestreview-851132631", "body": ""}
{"title": "Setup GH Install", "number": 320, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320", "body": "Setup a basic version of GH Install starting from VSCode (Required to provide repo url)\nCurrently some caveats due to limited API. Will revisit when API is updated or onboarding is more defined.\nIntroduced VSCode Git interface as part of GH Install work. Will require some UI and thought around the experience to determine correct repositories and remotes.\nAlso introduced useAsyncOperation hook with result type.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-881952709", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882001631", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882002355", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882002729", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882003471", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882004656", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882006377", "body": ""}
{"comment": {"body": "is this copied from somewhere?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#discussion_r806108411"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882010488", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882011311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882011774", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882013168", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882013521", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882013800", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882026053", "body": ""}
{"comment": {"body": "Yeah. This is pulled from VSCode at their suggestion. Basically provides the typing for the VSCode Git interface.\r\nI can add a link for reference.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#discussion_r806122318"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882026730", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882028206", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882059776", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882101421", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882101722", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882214275", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882216513", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882231314", "body": ""}
{"comment": {"body": "Is this guaranteed to succeed?  Back when I wrote the equivalent to this in OctoberDemo it wasn't, there were apparently some scenarios where the built-in git was unavailable so we'd fail to command line git.  Maybe it's definitely always installed now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#discussion_r806269873"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882232311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882232519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882233458", "body": ""}
{"comment": {"body": "Just FYI this case does happen, and did happen a fair bit in OctoberDemo -- it's not really relevant to this code because we'll probably be replacing this with an equivalent UI of some kind, but at that point we should make sure we deal with this with a nice warning/error.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#discussion_r806271553"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882240412", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/320#pullrequestreview-882240515", "body": ""}
