{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1027939995", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/9b526856-bb15-4c9b-855b-97729ce560bb)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/21e3194c-c1b3-4fad-9139-c7c84bc1e3d4)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/550d9b29-d945-455b-9bdc-4202364fa06c)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/7c490dba-7384-4f73-a0b1-e9739c073612)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=0f20d241-0f03-4ca6-a01e-163217b9031e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r913301434"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1028974417", "body": ""}
{"comment": {"body": "\n\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/c7ae6d50-7fe9-46df-8edd-fe3568cdfa08)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b20bad91-718f-4829-b1bd-d1cdd888b323?message=5a9844af-e30b-4d5e-a5f5-3fc02d024d6c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914033748"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1028977600", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/32bd41d9-401a-425d-b321-9113755ceda9)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b20bad91-718f-4829-b1bd-d1cdd888b323?message=267254ff-aba1-410b-b983-be1da5ce366a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914035981"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1028984020", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/111481bf-95a8-41b1-9a88-b9e5142bdb72)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=98a8e318-98a1-4149-aabc-d033855510ca).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914040638"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1028984227", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/240bdfb7-5a84-49c5-9d80-e4afd95e59c3)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=93cd030d-557f-4168-9804-5f52b4c3dc0b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914040794"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1028999544", "body": ""}
{"comment": {"body": "Hello?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914051869"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1029000990", "body": ""}
{"comment": {"body": "Hello adding myself to this thread", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914052974"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1029001678", "body": ""}
{"comment": {"body": "Here is an image?\r\n\r\n\r\n<img src=\"https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/8689d6b2-1fa4-4042-a5d0-305e115028c6\" style=\"max-width: 200px\">\r\n\r\n\r\n\r\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=8bb6f64b-2405-4a42-bbd3-bfd9fa606f96).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914053492"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1029023723", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/8689d6b2-1fa4-4042-a5d0-305e115028c6)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=fa203c0c-9cbf-4f6f-a9ff-ee1c51b69d67).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914060520"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1029039825", "body": ""}
{"comment": {"body": "Test test\r\n![](blob:https://dev.getunblocked.com/5221c526-c167-4d0d-9817-fe28d2822f11)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914068318"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1029333129", "body": ""}
{"comment": {"body": "@pwerry. asdfsad @pwerry\n\n\n\n @mahdi-torabi asfdasd. @mahdi-torabi dfs\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=d2b5d181-c2eb-4690-89ca-60131f612aec).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914293885"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1029333513", "body": ""}
{"comment": {"body": "hi @mahdi-torabi\n\n\n\n  @mahdi-torabi hi. @mahdi-torabi @mahdi-torabi\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=2e4e158f-6ef2-44db-9f3c-81ed25d63733).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r914294195"}}
{"title": "Use import aliases", "number": 213, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/213"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/213#pullrequestreview-869946737", "body": ""}
{"title": "Only build mac for web extension", "number": 2130, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2130", "body": "Add flag to only build Mac app, not safari."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2130#pullrequestreview-1027832978", "body": ""}
{"title": "Add watch debug target for VSCode", "number": 2131, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2131", "body": "Adds a separate 'watch' debug target for VSCode, against dev/prod envs.  When you run this, it will run webpack in watch mode, and will do quick rebuilds whenever you save files.  You can hit Cmd+R in VSCode to reload and pull in changes.\nThis should be faster then the previous debug targets, which required full rebuilds.  We had tried enabling webpack caching but it causes a lot of bugs.\nThe one downside to this is that you need to manually shut down the watch session in the 'Terminal' UI, if you want to re-build from scratch (ie if you change the webpack config)."}
{"comment": {"body": "> Only for dev and prod? Can this be configured for local builds as well?\r\n\r\nDone.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2131#issuecomment-1174145760"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2131#pullrequestreview-1027846078", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2131#pullrequestreview-1027846271", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2131#pullrequestreview-1027852629", "body": "Only for dev and prod? Can this be configured for local builds as well?"}
{"title": "Sourcemark engine collects commit graph information", "number": 2132, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2132", "body": "This change collects the parents commit(s) in addition to the commit\nfor each node in the commit history.\n\nFor a repo with linear history there will always be just one parent.\n  This has been our current assumption.\nFor repos with merges, there can be between 1 and 3 parents per node.\n\nThis change has no behaviour impact. The parent information will be\nused for graph traversal in a follow up change."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2132#pullrequestreview-1027890462", "body": ""}
{"title": "Fix getSourceMarkLatestPoint returns commits not on local branch", "number": 2133, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2133", "body": "\nDo not merge until these are fixed:\n- \n- "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2133#pullrequestreview-1028967559", "body": ""}
{"title": "Add Spinner to org installation", "number": 2134, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2134", "body": "Add loading state after triggering org installation."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2134#pullrequestreview-1027913563", "body": ""}
{"title": "Polish add email flow", "number": 2135, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2135", "body": "Update design for @-mentioned contributor row \n\n\n\nThe mentioned row is not toggleable\n\n\nonce the mention is deleted, it becomes a regular row\n\n\n\nSome general refactoring"}
{"comment": {"body": "I really like this!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2135#issuecomment-1174353131"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2135#pullrequestreview-1027897824", "body": ""}
{"title": "UpdateLogging", "number": 2136, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2136"}
{"title": "update", "number": 2137, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2137"}
{"title": "fix the conditon so we only run smoke tests once", "number": 2138, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2138"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2138#pullrequestreview-1027921309", "body": ""}
{"title": "Update no threads state view", "number": 2139, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2139", "body": "Temporary patch for first issue outlined here\nThe idea is that for now we should always expect users to have threads, so show a loading state when the getThreads call returns an empty value and wait for the threads to populate naturally. If it loads for too long without resolving any threads (>1min?), have some way for the user to contact us (note: in the gif, the timeout is shortened to 3000ms for brevity)\nThe true solution is to add a flag to either the getThreads response or Repo model to signify whether the service is still ingesting PRs or not, then the client can read this flag and know to show an intermittent loading state or a true empty threads state. This will require a bit more involvement/refactoring and is less pressing of an issue for the immediate future since we've already ingested the PRs for the teams we plan to onboard in the immediate future (note: this fix should land sometime this week)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2139#pullrequestreview-1027926116", "body": ""}
{"title": "Move API models under api package", "number": 214, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/214", "body": "minor re-structure to make it more obvious which model type you're importing\nimport com.nextchaptersoftware.db.models.Thread\nimport com.nextchaptersoftware.db.models.ThreadModel\nimport com.nextchaptersoftware.api.models.CreateThreadRequest\nimport com.nextchaptersoftware.api.models.UpdateThreadRequest"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/214#pullrequestreview-869969736", "body": ""}
{"comment": {"body": "This is the only real change.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/214#discussion_r797178057"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/214#pullrequestreview-869969868", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/214#pullrequestreview-870009150", "body": "Looks good"}
{"title": "change trigger events and add checkout step", "number": 2140, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2140"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2140#pullrequestreview-1027925691", "body": ""}
{"title": "Fix rendering of the mention dropdown in the web extension", "number": 2141, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2141", "body": "before:\n\nafter:\n"}
{"comment": {"body": "@kaych can we vertically centre the avatar with the name?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2141#issuecomment-1174452357"}}
{"comment": {"body": "Thank you!!!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2141#issuecomment-1174453063"}}
{"comment": {"body": "@benedict-jw done \r\n<img width=\"217\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/177224795-ffa8bb20-3f11-4c3e-aeb4-c12e75d99959.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2141#issuecomment-1174454418"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2141#pullrequestreview-1027928779", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2141#pullrequestreview-1027929770", "body": ""}
{"title": "Do not use invalid team memberships for sending emails", "number": 2142, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2142"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2142#pullrequestreview-1027936746", "body": ""}
{"comment": {"body": "@rasharab the better fix is to update `findMembershipsForPerson()` to only join on Teams that are not deleted.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2142#discussion_r913298652"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2142#pullrequestreview-1027937637", "body": ""}
{"comment": {"body": "Considered that, but that has the potential for knock on effects in stuff that uses the above somehow expecting to get deleted teams.\r\n\r\nIf you don't see the need for that, then I can do it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2142#discussion_r913299500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2142#pullrequestreview-1027938493", "body": ""}
{"comment": {"body": "It only has one caller, and the caller is in the API layer so customer visible.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0080bb10-537a-4350-8a2c-00071c20e09c?message=50ec468f-c948-446e-a087-e3a13611044b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2142#discussion_r913300198"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2142#pullrequestreview-1027938885", "body": ""}
{"comment": {"body": "Very frustrating btw that the person model is returning memberships for deleted teams.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2142#discussion_r913300526"}}
{"title": "Manually add user in web extension", "number": 2143, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143", "body": "Added ability to manually add users in web extension\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143#pullrequestreview-1027958819", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143#pullrequestreview-1028019986", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143#pullrequestreview-1028951439", "body": ""}
{"comment": {"body": "wonder if we can refactor this out into `TeamMemberUtils` since we do similar stuff in vscode to find the contributor/team member match ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143#discussion_r914017266"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143#pullrequestreview-1028951571", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143#pullrequestreview-1028974278", "body": ""}
{"comment": {"body": "should remove this line too per Ben's feedback\r\n(I can do this in my PR too)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143#discussion_r914033629"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143#pullrequestreview-1030603438", "body": ""}
{"comment": {"body": "Yes we should definitely factor this out", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2143#discussion_r915189421"}}
{"title": "Fix how images are uploaded for message edits", "number": 2144, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2144", "body": "2 problems when editing a message:\n1. Rendering images\n2. Uploading images"}
{"title": "Richie is right again", "number": 2145, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2145", "body": "Updating findMembershipsForPerson such that it only shows memberships that are not for deleted teams."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2145#pullrequestreview-1027946631", "body": "cheers man"}
{"title": "Drop the tabs", "number": 2146, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2146", "body": "I'm playing with blending modes with the footer. There are two modes: \"within window\" and \"behind window\". \"Within Window\" allows content in the hub to blur underneath. \"Behind Window\" just makes the footer a part of the window itself, and so the content just disappears. The effect is subtle and can't be seen with a still image. I personally prefer \"Behind Window\" because the footer pops out too much using \"Within Window\". The footer should be a part of the background\nWithin Window\n\n\nBehind Window\n\n"}
{"comment": {"body": "Between these two options I agree with you. Especially in light mode, the footer pops way too much in the former. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2146#issuecomment-1174548246"}}
{"comment": {"body": "Ditto with @benedict-jw. Good call. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2146#issuecomment-1174640209"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2146#pullrequestreview-1028876180", "body": ""}
{"comment": {"body": "For legibility, would be helpful to refactor this out into a \"footer\" view.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2146#discussion_r913963215"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2146#pullrequestreview-1028877457", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2146#pullrequestreview-1044450051", "body": ""}
{"comment": {"body": "niceeeee\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/bd429684-fb65-4054-b87e-b9947824c980/thread/7438b7f8-c3fc-4830-895e-487f6ee8b06a?message=ecc2ced5-703b-4a65-9d6e-46b492d0845f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2146#discussion_r925196432"}}
{"title": "Fix url", "number": 2147, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2147"}
{"title": "Fix welcome emails", "number": 2148, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2148"}
{"title": "AddDashboardUrl", "number": 2149, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2149"}
{"title": "Implement createMessage and updateMessage operations", "number": 215, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215", "body": "This will also create the SourceMarks in CreateMessageRequest.sourcemarks. Next PR will fix the Message DB model to replace body with blocks"}
{"comment": {"body": "The API for message create/update is not right yet:\r\n- https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798127560\r\n- https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798127829\r\n\r\nbut I think you should merge your change first, then we can figure out a better APIRequest shape after", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#issuecomment-1028503970"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#pullrequestreview-869989542", "body": ""}
{"comment": {"body": "just curious, why return?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798101061"}}
{"comment": {"body": "When we do this bit, I think we should do it outside the transaction.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798122570"}}
{"comment": {"body": "in the createRequest case, existingSourceMarks should be empty right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798123265"}}
{"comment": {"body": "in the createRequest case, sourceMarksToDelete should be empty right?\r\nas a result, wondering if we should have a different groomer for create and update. What you think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798123340"}}
{"comment": {"body": "weird that we require `sourcemarks` when updating the message content. seems really unnecessary. I think this points to a problem in API design. let's chat.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798127560"}}
{"comment": {"body": "It's really `sourcepoints` that we need to create not `sourcemarks`. Weird API, let's chat.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798127829"}}
{"comment": {"body": "nice", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798135119"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#pullrequestreview-871271687", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#pullrequestreview-871390257", "body": ""}
{"comment": {"body": "no real reason other than to prevent logic from being added below", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#discussion_r798231785"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/215#pullrequestreview-871400269", "body": ""}
{"title": "Replace sourcemark linear propagation with graph propagation", "number": 2150, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2150", "body": ""}
{"title": "Filter out corrupt/invalid original points", "number": 2151, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2151", "body": "Part of the fix needed for this:\n"}
{"title": "fix a typo in the action path", "number": 2152, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2152", "body": "I am stuck in CI hell!"}
{"title": "ses stack", "number": 2153, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2153"}
{"title": "Change app name from UnblockedHub.app to Unblocked.app", "number": 2154, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2154", "body": "It's all build config and package changes. I could add some code in the installer scripts to check for and remove UnblockedHub.app if we think that's needed"}
{"comment": {"body": "Confirmed this works as expected with installer", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2154#issuecomment-1175319023"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2154#pullrequestreview-1028971880", "body": ""}
{"title": "fix smoke tests to avoid multiple runs", "number": 2155, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2155", "body": "Moved the smoke test to Dev deploy step under a conditional. This should eliminate all those new extra jobs we see in GitHub actions \nFixed the conditional to run smoke tests only on API service deploys"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2155#pullrequestreview-1028972918", "body": ""}
{"title": "Create commit sourcepoint in addition to merge sourcepoint", "number": 2156, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2156", "body": "When ingesting a thread, we should create a source point with a commit ID that is consistent with the file."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2156#pullrequestreview-1028996990", "body": "do we need to make any changes to \nPullRequestReviewThread.commitAndLineRange?"}
{"comment": {"body": "what about the original_commit_id?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2156#discussion_r914051827"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2156#pullrequestreview-1029010817", "body": ""}
{"comment": {"body": "We'll have to ignore that now if we only want to create source points where the file commit ID == comment commit ID.  original_commit_id == commit_id when a thread is first opened but as commits are pushed, commit_id will be updated (and should match the file commit).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2156#discussion_r914056460"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2156#pullrequestreview-1029011254", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2156#pullrequestreview-1029031094", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2156#pullrequestreview-1029032167", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2156#pullrequestreview-1029032975", "body": ""}
{"title": "make smoke tests less sensitive", "number": 2157, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2157", "body": "Dev API calls are slower than prod due to resource limitations. This sets a higher threshold for smoke tests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2157#pullrequestreview-1029024552", "body": ""}
{"title": "reduce ses dimensions for cost savings", "number": 2158, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2158"}
{"title": "Admin web shows thread unreads for each team member", "number": 2159, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2159", "body": "Related to this:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2159#pullrequestreview-1029055455", "body": ""}
{"title": "Add Teams endpoint", "number": 216, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/216", "body": "Just API stubs; implementation in next PR.\nAddresses https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869666679"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/216#pullrequestreview-869994482", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/216#pullrequestreview-870878668", "body": ""}
{"comment": {"body": "Does this get the teams that the current user is a member of?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/216#discussion_r797846028"}}
{"title": "Add PromiseProxy and FetchProxy", "number": 2160, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160", "body": "Basic implementation of a Promise-forwarding proxy and a Fetch proxy.  This isn't hooked up to anything right now, I'm getting this in so we can test if the mechanism works in the web extension or not.  If it does, this will get cleaned up with tests, error handling, etc."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160#pullrequestreview-1029067684", "body": ""}
{"comment": {"body": "@jeffrey-ng you'd call this in the web extension's BaseAPI, it creates the proxying fetch implementation for you", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160#discussion_r914100684"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160#pullrequestreview-1029068257", "body": ""}
{"comment": {"body": "@jeffrey-ng you'd call this in the web extension's background script to hook up the fetch handler", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160#discussion_r914101459"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160#pullrequestreview-1029069035", "body": ""}
{"comment": {"body": "@jeffrey-ng you'd create one of these in the content script, with the constructor argument hooked up to forward data to the background script, and any messages from the background script should result in a call to `dataReceived`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160#discussion_r914102439"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160#pullrequestreview-1029069741", "body": ""}
{"comment": {"body": "@jeffrey-ng create one of these in the background script, with the constructor argument being resulting in a message being sent to the content script, and any message from the content script resulting in a call to `dataReceived`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160#discussion_r914103358"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2160#pullrequestreview-1029189225", "body": ""}
{"title": "Fix content port issue", "number": 2161, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2161", "body": "Listener was refreshing unexpectedly.\nThis caused the port listener to be removed/added.\nThe initial listener reference that should have received the \"loaded\" request was removed which led to missing data.\nTemporary fix is to fix the refresh issue but this shows some fragility in the content port architecture. We will be revisiting this by moving majority of state/logic into content scripts."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2161#pullrequestreview-1029083619", "body": ""}
{"title": "cleanup kinesis", "number": 2162, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2162"}
{"title": "[Web Extension] Fix mentions in create discussion", "number": 2163, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163", "body": "Feature parity w vscode: \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#pullrequestreview-1029133053", "body": ""}
{"comment": {"body": "This would only return a single value.\r\nIs there a chance to have multiple diff contributors?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#discussion_r914150819"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#pullrequestreview-1029134242", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#pullrequestreview-1029135693", "body": ""}
{"comment": {"body": "I think given the dependencies, the useEffect would run for each instance \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/58102219-9be6-4b01-85f5-c67e8cdec742?message=ba7c237c-f56d-4031-b469-e6dd516c38ac).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#discussion_r914152632"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#pullrequestreview-1029140126", "body": ""}
{"comment": {"body": "Don't think this is something that needs to be fixed but just a thought.\r\n\r\n`selectedContributorsMap` is memoized from `selectedGitContributors`\r\nHere we're manually updating the map. We then push these changes back up to selectedGitContributors array which then updates the map again.\r\n\r\nIf one updates the map but *not* the array, map changes could potentially be wiped out the next time the array is updated.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#discussion_r914155774"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#pullrequestreview-1029141857", "body": ""}
{"comment": {"body": "If we could structure this in a way where there's only a single source that can be updated, we can avoid potential bugs.\r\n\r\nMaybe a separate data structure which provides multiple setter & getter functions but the actual data source is read only.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#discussion_r914156980"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#pullrequestreview-1029142574", "body": ""}
{"comment": {"body": "yeah I had initially only defined the map in each fn instance (so that would have been avoided) but since I was repeating the pattern so much I ended up refactoring it out. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#discussion_r914157527"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#pullrequestreview-1029147508", "body": ""}
{"comment": {"body": "Though I guess with the impending refactor, it's unclear to me how much of this code will remain anyways (i.e. will these still be state objects or will they just be data passed into the component like in vscode)? \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/f06e8dbf-479c-4243-93ad-60676d2ea37e?message=7ec6d882-9a6e-4cdf-bc27-8ab93c34b2b4).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#discussion_r914161013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2163#pullrequestreview-1029184593", "body": ""}
{"title": "Admin: Sort People table by most recently created", "number": 2164, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2164", "body": "Dennis mentioned this. Will try to get sortable tables working in a future change."}
{"title": "Create missing sourcepoints when syncing a PR thread", "number": 2165, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2165", "body": "Part 2 of https://github.com/NextChapterSoftware/unblocked/pull/2156\nThis will let us trigger re-ingestion and create any missing sourcepoints."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2165#pullrequestreview-1029202182", "body": "nice"}
{"title": "Clear unread threads in admin web", "number": 2166, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2166", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2166#pullrequestreview-1029190273", "body": ""}
{"title": "Adds section headers", "number": 2167, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2167"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2167#pullrequestreview-1029225233", "body": ""}
{"comment": {"body": "This might need some commenting to explain what's going on.  In this case I guess the size of the dropdown never really shrinks, and this is intended to always set a high water mark for the height?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2167#discussion_r914215671"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2167#pullrequestreview-1029227165", "body": ""}
{"comment": {"body": "That's correct - I spoke with @benedict-jw about this and we came to the conclusion that it's too janky to animate the content size up and down once the thing is presented. I may optimize this later to try and remove this hack and use out-of-the-box SwiftUI size constraints, but I couldn't get this to work while hacking on the section headers", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2167#discussion_r914217058"}}
{"title": "Fix line height alignment", "number": 2168, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2168", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2168#pullrequestreview-1029229157", "body": ""}
{"title": "Make builds go a minute faster.", "number": 2169, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2169"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2169#pullrequestreview-1029341628", "body": ""}
{"title": "Ensure github secrets are deployed to dev/prod", "number": 217, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/217", "body": "Dev/Prod secrets are loaded via pod specific environment variables encrypted on kubernetes.\nThey are uploaded using make setup-k8s-env per environment (dev/prod).\nIf you want to do this yourself, I would highly recommend the documentation that Mahdi has written up.\n\nTo load them, we us the wonderful lightbend optional system/env variables:       \nclientSecret: ${?GITHUB_CLIENT_SECRET}\nclientKey: ${?GITHUB_CLIENT_KEY}\nThe process of loading:\n1. Attempt to load from secrets.conf located ~/.secrets/unblocked/secrets.conf\n2. Attempt to load from environment specific conf.\n3. Attempt to load from global.conf\nValidated this works as expected..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/217#pullrequestreview-870140085", "body": "Winner winner chicken dinner"}
{"title": "Fix loading window for tutorial wizard", "number": 2170, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2170", "body": "Fix the loader to 100vh/100vw\nFlag off onboarding flag when users hit the last step pf onboarding"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2170#pullrequestreview-1029309616", "body": ""}
{"title": "Add resiliency to post-install app start script", "number": 2171, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2171", "body": "2 changes:\n- Try to fall back to opening the hub as the installer user if the local user can't be resolved. It really shouldn't matter since the installer user has higher privileges\n- Try this 3 times with a sleep pause of 1 second between each try. The thinking here is that the post-install script is somehow running before the system is ready to launch the app"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2171#pullrequestreview-**********", "body": ""}
{"title": "blob_url can be null", "number": 2172, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2172", "body": "Like for submodule changes: https://github.com/NextChapterSoftware/unblocked/pull/812/files\nhttps://app.logz.io/#/goto/9da973b62d008a12d4941c08195cfdac?switchToAccountId=411850\nSee: video-app/macOS/agora"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2172#pullrequestreview-**********", "body": ""}
{"title": "Update Redirect Styling", "number": 2173, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2173", "body": "Removed automatic redirect to dashboard.\nUpdated styling of base.\nTODO: Refactor out \"Landing\" page into base component or mixin\n"}
{"comment": {"body": "Want to handle styling refactor when we redo parts of dashboard.\r\nNeed to get this in asap.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2173#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2173#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2173#pullrequestreview-**********", "body": ""}
{"comment": {"body": "\ud83e\udd14 the parent here seems to be a grid box, not a flex box, I'm surprised this works?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2173#discussion_r914290094"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2173#pullrequestreview-1029328312", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2173#pullrequestreview-1029330678", "body": ""}
{"comment": {"body": "The sub-text font styling looks very small. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2173#discussion_r914292086"}}
{"title": "mentionOnChange should handle whitespaces better", "number": 2174, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2174", "body": "Couldn't do what I was doing here, in particular introducing @mentions before whitespaces."}
{"title": "Fix for Git file content hash lookup", "number": 2175, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2175", "body": ""}
{"title": "Set sidebar pinned on new install", "number": 2176, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2176", "body": "Hook into install callback to set initial pinned state."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2176#pullrequestreview-1029343382", "body": ""}
{"comment": {"body": "Technically can use localStorage from content script but not recommended.\r\n\r\nThis local storage refers to the current page's local storage, aka GH.\r\nIf we want this to be part of the web extension, needs to be done with browser.storage.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2176#discussion_r914301546"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2176#pullrequestreview-1029372068", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2176#pullrequestreview-1030606573", "body": ""}
{"comment": {"body": "Should we move this to use the storage proxy?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2176#discussion_r915191813"}}
{"title": "Fix regression", "number": 2177, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2177"}
{"title": "Don't create ThreadUnreads for anonymous users for PR ingest/sync/@-mention", "number": 2178, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178", "body": "We shouldn't create a thread unread for anonymous users. This is so that when they join they're not hit with a bunch of unread messages."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#pullrequestreview-1032263846", "body": ""}
{"comment": {"body": "Creating a comment here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#discussion_r916356287"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#pullrequestreview-1032355033", "body": ""}
{"comment": {"body": "@richiebres does this create a notification for you", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#discussion_r916427524"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#pullrequestreview-1032355738", "body": ""}
{"comment": {"body": "ya\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9fe24ff7-231c-4609-86b6-3f1479759e11?message=ca18d3cb-5561-4901-894d-ed516e28b9e0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#discussion_r916428092"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#pullrequestreview-1033054993", "body": ""}
{"comment": {"body": "Could add a slice to make more efficient.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#discussion_r916914474"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#pullrequestreview-1034833399", "body": ""}
{"comment": {"body": "Message from Unblocked. (going to add a reaction a few seconds later in GitHub\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6bc96738-c6b0-4518-9ed1-a1b4fceb849b?message=6126c9da-3633-4147-8fbb-6bd7c3134232).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#discussion_r918280325"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#pullrequestreview-1035026169", "body": ""}
{"comment": {"body": "Halllooooo test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6bc96738-c6b0-4518-9ed1-a1b4fceb849b?message=f4a32768-fa3e-4ec9-92a8-c53b2af7e768).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#discussion_r918405332"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#pullrequestreview-1035026954", "body": ""}
{"comment": {"body": "Howdy MATT\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6bc96738-c6b0-4518-9ed1-a1b4fceb849b?message=ddd96ffe-da10-4b12-8224-6105d4301aae).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#discussion_r918405894"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#pullrequestreview-1035028230", "body": ""}
{"comment": {"body": "zzzzz\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6bc96738-c6b0-4518-9ed1-a1b4fceb849b?message=4a7834b6-4a65-46eb-9777-e4ebb52bd305).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#discussion_r918406923"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#pullrequestreview-1035029081", "body": ""}
{"comment": {"body": "Hi MATT\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6bc96738-c6b0-4518-9ed1-a1b4fceb849b?message=108f6ba0-ed26-4cb3-b7c6-fff06b2dca13).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2178#discussion_r918407620"}}
{"title": "Update host permisisons", "number": 2179, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2179", "body": "Scope host permissions.\nThis is a scary change.... We had initially done this due to a keepAlive hack.\nWe'll need to dog food this to truly understand if it causes any issues."}
{"comment": {"body": "Going to push a build out without this change before merging this in and dog fooding.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2179#issuecomment-1176415626"}}
{"comment": {"body": "Based on testing from the last day and overnight, it seems okay but I'm not 100% confident...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2179#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2179#pullrequestreview-**********", "body": "OK -- not sure how you want to manage the risk on this.  Should we generate builds to test locally before merging this in?"}
{"title": "Change login api to return object with provider details", "number": 218, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/218", "body": "Problem\nThe current /login endpoint returns a 302 redirect to the SCM oauth authorization endpoint. This is problematic for clients because it doesn't mesh well with openapi generators, requiring custom templates. Rather than treat the request as browser originated, we should assume api requests are always from the client and return a payload.\nProposal\nChange /login to /login/options and return a LoginOptions payload:\n{\n    \"providers\": [\n        {\n            \"provider\":\"github\",\n            \"oauthUrl\":\"\n        },\n        {\n            \"provider\":\"bitbucket\",\n            \"oauthUrl\":etc\n        }\n    ]\n}\nAdditionally, we can remove the provider argument from the /login and /preAuth APIs, and instead shuttle the provider arg through the oauthUrl state parameter. This simplifies the client model and keeps the provider selection encoded by the server. \nPossible Next Steps\nFor now the clientType is still being shuttled as a cookie. We discussed embedding a final \"landing url\" within the oauth redirect uri, but I don't see a strong use case for this yet. Please comment."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/218#pullrequestreview-870875790", "body": "This looks correct to me. I still prefer removing the clientType from the query params but I'll let you and Jeff figure that out."}
{"title": "Header section touchups", "number": 2180, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think there's a `Divider` component?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#discussion_r914388521"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#pullrequestreview-1029458611", "body": ""}
{"comment": {"body": "Yes - but for some reason it's not correctly adopting the `foregroundColor` property, so we had to use a 1 pixel Rectangle\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/1e09aefe-3698-4c75-9298-d6a5d0174c6d?message=55c3e1ae-d70a-4b58-9c0e-f78aa48a59ca).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#discussion_r914388815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#pullrequestreview-1029458688", "body": ""}
{"comment": {"body": "We should look into eventually making this into a list aka tableView.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#discussion_r914388888"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#pullrequestreview-1029458731", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#pullrequestreview-1029463286", "body": ""}
{"comment": {"body": "SwiftUI List is also super bugged. It doesn't adopt vibrancy or other colour properties, and applies weird padding that you can't control (at least not in SwiftUI). We can wait for SwiftUI to catch up, or possibly fall back to AppKit and wrap our own SwiftUI view. Either way it's a sad state of affairs for now\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/245bc8fe-0d2d-45a1-be60-91bfbec68c53?message=b6d3fa1e-6a1e-4df4-9ce1-64c5789eefce).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#discussion_r914392247"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#pullrequestreview-1030562074", "body": ""}
{"comment": {"body": "@richiebres\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/245bc8fe-0d2d-45a1-be60-91bfbec68c53?message=fb823124-9e59-4c30-bb8d-206e61abed5a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2180#discussion_r915159543"}}
{"title": "Update mentions whitespacing", "number": 2181, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2181", "body": "Some slight refactoring to showing the mention dropdown and add whitespace after inserting mentions"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2181#pullrequestreview-1029409988", "body": ""}
{"title": "Allow making new threads with empty message content", "number": 2182, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182", "body": "This got more involved then I thought it would be:\n* MessageEditor can tell if a message has content, but the form that it publishes this is not super helpful when it comes to handling this state in the parent code.\n* So I ended up making a MessageUtils class that has helpers for detecting empty-content messages, and generating plain-text messages.  It's ugly.\n* I'm open to alternatives"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#pullrequestreview-1029426712", "body": ""}
{"comment": {"body": "This prevents rendering the title if the description happens to be a plain text message containing the same text as the title.  This is pretty brittle and fragile, I don't love it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#discussion_r914364657"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#pullrequestreview-1029426815", "body": ""}
{"comment": {"body": "If you guys are OK with these, I will add tests.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#discussion_r914364732"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#pullrequestreview-1029464417", "body": ""}
{"comment": {"body": "Hmmm can we rename this fn, as it's really comparing equality and not just looking for the plain text type \ud83e\udd14 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#discussion_r914393041"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#pullrequestreview-1029464967", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#pullrequestreview-1029467132", "body": ""}
{"comment": {"body": "Presumably will need to do the same for the extension CreateDiscussionPortManager ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#discussion_r914395104"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#pullrequestreview-1029506939", "body": ""}
{"comment": {"body": "Will do", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#discussion_r914424351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#pullrequestreview-1029507125", "body": ""}
{"comment": {"body": "Yep -- I thought getting VSCode in first was the highest priority, will do extension tomorrow", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2182#discussion_r914424482"}}
{"title": "Fix: originalStartLine (38) is greater than originalLine (38)", "number": 2183, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2183", "body": "Off by one error?\n"}
{"comment": {"body": "I've never seen a comment where original start line equals start line - usually it's null when they're equal", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2183#issuecomment-1175735416"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2183#pullrequestreview-1029445125", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2183#pullrequestreview-1029468863", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2183#pullrequestreview-1029469030", "body": ""}
{"comment": {"body": "@davidkwlam can you confirm this is what you'd expect?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2183#discussion_r914396568"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2183#pullrequestreview-1029502277", "body": ""}
{"comment": {"body": "With your changes, yeah. I still think its odd that the GitHub API returns a startLine that equals line (usually its just null), and I'm unable to repro it but I guess its possible. I just tried creating a multi line comment for one line and startLine ended up null. \r\n\r\nMaybe we should just take the default context lines if startLine == line? Up to you.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2183#discussion_r914421029"}}
{"title": "Support even older Git versions", "number": 2184, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2184", "body": "With a minor tweak, we now support every Git version that VSCode supports. Back to 2.0.5 in 2014.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2184#pullrequestreview-1029464799", "body": ""}
{"comment": {"body": "inconsequential. probably a few ms slower.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2184#discussion_r914393345"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2184#pullrequestreview-1029517847", "body": ""}
{"title": "Update onboarding asset", "number": 2185, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2185", "body": "\nalso small typo fix for the icons stack"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2185#pullrequestreview-1029491122", "body": ""}
{"title": "Use filled support icon", "number": 2186, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2186"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2186#pullrequestreview-1029488331", "body": "wonder if something like this could be helpful for the icons? "}
{"title": "Fix installer script loop", "number": 2187, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2187"}
{"title": "Create unread ThreadUnreads for @mentioned team members", "number": 2188, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2188", "body": "Fixes UNB-385"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2188#pullrequestreview-1030502929", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2188#pullrequestreview-1032010577", "body": "A couple of questions:\n1. should we mark the _entire_thread as unread for the @-mentioned member if they are new to the thread?\n2. what if the @-mentioned member is already a thread participant? I think in this case, we should not touch their ThreadUnread row."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2188#pullrequestreview-1032091463", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2188#pullrequestreview-1032093638", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2188#pullrequestreview-1032173731", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2188#pullrequestreview-1032174503", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2188#pullrequestreview-1032175132", "body": "Consider doing this now or later:\nhttps://github.com/NextChapterSoftware/unblocked/pull/2188#discussion_r916172263"}
{"title": "start ec2 runners only when files have changed", "number": 2189, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2189", "body": "We were starting runner instances when build jobs were not supposed to run because of a missing conditional."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2189#pullrequestreview-1030375947", "body": ""}
{"title": "Implement Team apis", "number": 219, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/219", "body": "4-way join\n\nI'm not concerned about the 4-way join for now. I think optimization of this should be driven by empirical performance issues.\nWe could change to 3-way join, denormalizing by adding PersonID to TeamMember model; but the problem is that we would need to consistently (transitionally) update both the IdentityModel and TeamMemberModel when a user dis/connects an identity.\n\nAPI-DB coupling problem\nSee #220."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/219#pullrequestreview-871175202", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/219#pullrequestreview-871176273", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/219#pullrequestreview-871188289", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/219#pullrequestreview-871214835", "body": ""}
{"title": "Add NotificationPreferences to Person model", "number": 2190, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190"}
{"comment": {"body": "Closing in favour of https://github.com/NextChapterSoftware/unblocked/pull/2210.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#issuecomment-1176836967"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1030427800", "body": ""}
{"comment": {"body": "NotificationPreferences seems too tightly scoped.\r\nProbably UserPreferences?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r915064351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1030432749", "body": ""}
{"comment": {"body": "IMO UserPreferences would probably house an object of NotificationPreferences but I thought this might be the simplest approach for what we need now \r\n\r\nBut feel free to modify this as needed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r915067807"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1030441114", "body": ""}
{"comment": {"body": "I'd say to make this somewhat future-thinking we should follow a nested hierarchy.\r\nUserPreferences\r\n* NotificationPreferences\r\n\r\n@richiebres for his thoughts?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r915073451"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1030443045", "body": ""}
{"comment": {"body": "Richie brought up an interesting point.\r\nWhat if we had endpoints for each preference type?\r\n\r\ni.e.\r\nGET/PUT/DELETE /person/email-preferences\r\nGET/PUT/DELETE /person/thread-preferences", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r915074818"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1030447149", "body": ""}
{"comment": {"body": "Or we could start out with just this for now:\r\n```\r\nGET/PUT/DELETE /person/preferences\r\n```\r\n\r\nUnlike the Person object, I don't think clients would need to poll on preferences right? At least I don't see why they'd need to poll for email preferences at least.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r915077782"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1030481053", "body": ""}
{"comment": {"body": "Yeah we don't need live updates for preferences, we can just fetch them on demand when the prefs UI opens, since it's only opened rarely.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r915101843"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1030809060", "body": ""}
{"comment": {"body": "@UnblockedDemo hi\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d756a685-0cd5-4df8-917f-cf55ca0d9534?message=6607808a-be0b-4516-9497-9e5a661f9bb5).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r915326263"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1030809106", "body": ""}
{"comment": {"body": "@UnblockedDemo hi\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d756a685-0cd5-4df8-917f-cf55ca0d9534?message=0f144bf7-2047-480c-a764-e653c7379af1).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r915326298"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1032078644", "body": ""}
{"comment": {"body": "[test](https://github.com/NextChapterSoftware/unblocked/pull/2190/files)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r916222390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1033277345", "body": ""}
{"comment": {"body": "```\ntest\n```\n\nhello\n\n\n\n> something hello\n\nhello\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/88685d0e-47d0-4aed-b08a-2728f9f1ab2e?message=92236702-a85a-4aab-a39f-edb1cf9749bc).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r917065733"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1033480915", "body": ""}
{"comment": {"body": "@UnblockedDemo \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/88685d0e-47d0-4aed-b08a-2728f9f1ab2e?message=c51b1f4a-2951-4c36-845e-3db134f000e5).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r917222707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1033480974", "body": ""}
{"comment": {"body": "@UnblockedDemo bonjourno\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/88685d0e-47d0-4aed-b08a-2728f9f1ab2e?message=6ab736aa-256e-4b0b-8391-b2ab2afd2fc2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r917222793"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1034977116", "body": ""}
{"comment": {"body": "```\nfdsfdfsdfsdf\n```\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=f14e7e5f-8d2c-424f-8601-86b5007e6acf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r918382710"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1034977616", "body": ""}
{"comment": {"body": "peeakaboo", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r918383050"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1034983649", "body": ""}
{"comment": {"body": "```\nsdfkljsdflks\n```\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/88685d0e-47d0-4aed-b08a-2728f9f1ab2e?message=78b433b1-0970-4a5c-952d-a03b40a78bc6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r918385230"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1034984052", "body": ""}
{"comment": {"body": "Something eskeljlsek \n\n```\nsdkfldsklfsdkfsdflkj\n```\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/88685d0e-47d0-4aed-b08a-2728f9f1ab2e?message=61f74447-5461-46f3-aeb2-b176f147a32b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r918385320"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1034985486", "body": ""}
{"comment": {"body": "peekaboo", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r918385726"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036207203", "body": ""}
{"comment": {"body": "@rasharab \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=311c5a90-3991-4cb6-9cd0-d9696a5aa7f8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919219777"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036207331", "body": ""}
{"comment": {"body": "@pwerry \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=921e8b06-fa65-4570-bfc2-477b5caf7caa).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919219915"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036215997", "body": ""}
{"comment": {"body": "@rasharab mentions are great. But can you please send me another message? I missed the notification for some strange reason\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=56c9a6c6-41c7-41e7-854a-a98107ef8252).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919226556"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036217594", "body": ""}
{"comment": {"body": "@pwerry ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919227713"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036218384", "body": ""}
{"comment": {"body": "@pwerry \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=84a8ce2a-4681-46c5-9924-b8c6e0e58047).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919228250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036218514", "body": ""}
{"comment": {"body": "nice\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=181e2f4f-2596-40c3-ab10-c61b1e00d486).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919228336"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036221819", "body": ""}
{"comment": {"body": "@pwerry @rasharab why am I getting emails for this ? I am not on Mentions or reviewers \n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=e1b546f5-be4e-419e-9753-0a9615239681).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919230734"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036222997", "body": ""}
{"comment": {"body": "Those emails seem to be from Github \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=2eb03873-dee1-401c-9493-b7378329ad68).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919231623"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036226065", "body": ""}
{"comment": {"body": "We figured it out. It wasn't my email, it was UnblockedDemo which is a group that I am member of. \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=ceef8cae-f945-4da3-bc50-708d3c6df71d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919233678"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1036256263", "body": ""}
{"comment": {"body": "Github emails are **spam**\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=c071856a-ba60-49d4-a5ac-88861586d69f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r919255076"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1074749641", "body": ""}
{"comment": {"body": "ping\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=e58524bd-391e-46fc-9c59-d17001ac8182).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r947230270"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1074765809", "body": ""}
{"comment": {"body": "ping\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=c82c08f1-8964-4a25-bd80-d8aed03736f2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r947241789"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1074766234", "body": ""}
{"comment": {"body": "working?\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=9653d6fc-daff-421c-bf12-86c49b38b4bc).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r947242088"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1074821325", "body": ""}
{"comment": {"body": "yes it is\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a760bc11-b2c6-48a6-a72c-f5c4867bfc1d?message=67bc127e-8c8d-4a11-8832-9d6be0ed9aa3).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r947282794"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1076381299", "body": ""}
{"comment": {"body": "asdfasdf\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=5eadcc0b-6c58-4068-b8de-1a2e73126a2c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r948376749"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1076541004", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/867ab113-ba3d-43d2-890f-3f1dab74705d)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a760bc11-b2c6-48a6-a72c-f5c4867bfc1d?message=8117f15d-00b5-4de8-9557-275e326a0cdc).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r948535072"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081011993", "body": ""}
{"comment": {"body": "@pwerry\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d756a685-0cd5-4df8-917f-cf55ca0d9534?message=7d37c141-c995-4607-b695-2a632be681e0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951811268"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081012538", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d756a685-0cd5-4df8-917f-cf55ca0d9534?message=ea5f496f-f0bf-40c5-bcaf-4c067a997db9).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951811789"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081032621", "body": ""}
{"comment": {"body": "test\n\n\n\n![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAfoAAAKqCAYAAADSc5wjAAAMPGlDQ1BJQ0MgUHJvZmlsZQAASImVVwdYU8kWnltSIbQAAlJCb4KIlABSQmihdwRRCUmAUEIMBBV7WVRw7aICNnRVRLHT7IidRbFhXyyoKOtiwa68SQFd95XvTb6Z+fPPmf+cOXduGQDUT3DF4lxUA4A8UaEkNtifMTY5hUF6AsjwpwHMAI3LKxCzoqPDASyD/d/LuxsAkfVXHWRa/xz/r0WTLyjgAYBEQ5zOL+DlQXwQALyKJ5YUAkCU8eaTC8UyDCvQlsAAIV4ow5kKXCXD6Qq8V24TH8uGuBUAsiqXK8kEQO0y5BlFvEyoodYHsZOILxQBoM6A2CcvL58PcRrENtBGDLFMn5n+g07m3zTThzS53MwhrFiLvJADhAXiXO7U/zMd/7vk5UoHfVjBqpolCYmVrRnm7WZOfpgMq0LcK0qPjIJYC+IPQr7cHmKUmiUNSVDYo4a8AjbMGdCF2InPDQiD2BDiIFFuZLiST88QBnEghjsEnSIs5MRDrAfxQkFBYJzSZpMkP1bpC63PkLBZSv4cVyL3K/N1X5qTwFLqv84ScJT6mFpxVnwSxFSILYqEiZEQq0HsWJATF6a0GVOcxY4ctJFIY2XxW0AcKxAF+yv0saIMSVCs0r40r2BwvdimLCEnUon3F2bFhyjyg7XyuPL44VqwywIRK2FQR1AwNnxwLXxBQKBi7dgzgSghTqnzQVzoH6uYi1PFudFKe9xMkBss480gdikoilPOxRML4YZU6OMZ4sLoeEWceHE2NzRaEQ++DIQDNggADCCFNR3kg2wgbO9t6IX/FCNBgAskIBMIgIOSGZyRJB8RwTYOFIM/IRKAgqF5/vJRASiC/NchVtE6gAz5aJF8Rg54AnEeCAO58L9UPks05C0RPIaM8B/eubDyYLy5sMrG/z0/yH5nWJAJVzLSQY8M9UFLYiAxgBhCDCLa4ga4D+6Fh8PWD1ZnnIl7DK7juz3hCaGD8JBwndBFuDVROFfyU5QRoAvqBylzkf5jLnArqOmK++PeUB0q47q4AXDAXaAfFu4LPbtClq2MW5YVxk/af1vBD1dDaUdxoqCUYRQ/is3PM9Xs1FyHVGS5/jE/iljTh/LNHhr52T/7h+zzYR/2syW2EDuAncVOYuexI1gDYGDHsUasDTsqw0O767F8dw16i5XHkwN1hP/wN3hlZZkscKp16nH6ohgrFEyRPaMBO188VSLMzCpksOAbQcDgiHiOIxjOTs7OAMjeL4rH15sY+XsD0W37zs37AwDv4wMDA4e/c6HHAdjnDm//pu+cDRO+OlQAONfEk0qKFBwuawjwKaEO7zR9YAzMgQ1cjzNwA17ADwSCUBAF4kEymACjz4L7XAImg+lgDigBZWAZWA0qwEawBewAu8F+0ACOgJPgDLgILoPr4A7cPd3gBegD78BnBEFICA2hI/qICWKJ2CPOCBPxQQKRcCQWSUbSkExEhEiR6cg8pAxZgVQgm5EaZB/ShJxEziMdyC3kAdKDvEY+oRiqimqjRqgVOhJloiw0DI1Hx6OZ6CS0GJ2PLkHXotXoLrQePYleRK+jXegLtB8DmAqmi5liDhgTY2NRWAqWgUmwmVgpVo5VY3VYM7zOV7EurBf7iBNxOs7AHeAODsETcB4+CZ+JL8Yr8B14Pd6KX8Uf4H34NwKNYEiwJ3gSOISxhEzCZEIJoZywjXCIcBreS92Ed0QiUZdoTXSH92IyMZs4jbiYuJ64h3iC2EF8ROwnkUj6JHuSNymKxCUVkkpI60i7SMdJV0jdpA9kFbIJ2ZkcRE4hi8hzyeXkneRj5Cvkp+TPFA2KJcWTEkXhU6ZSllK2UpoplyjdlM9UTao11ZsaT82mzqGupdZRT1PvUt+oqKiYqXioxKgIVWarrFXZq3JO5YHKR1UtVTtVtmqqqlR1iep21ROqt1Tf0Gg0K5ofLYVWSFtCq6Gdot2nfVCjqzmqcdT4arPUKtXq1a6ovVSnqFuqs9QnqBerl6sfUL+k3qtB0bDSYGtwNWZqVGo0aXRq9GvSNUdpRmnmaS7W3Kl5XvOZFknLSitQi681X2uL1imtR3SMbk5n03n0efSt9NP0bm2itrU2Rztbu0x7t3a7dp+Olo6LTqLOFJ1KnaM6XbqYrpUuRzdXd6nuft0bup+GGQ1jDRMMWzSsbtiVYe/1huv56Qn0SvX26F3X+6TP0A/Uz9Ffrt+gf88AN7AziDGYbLDB4LRB73Dt4V7DecNLh+8fftsQNbQzjDWcZrjFsM2w38jYKNhIbLTO6JRRr7GusZ9xtvEq42PGPSZ0Ex8Tockqk+Mmzxk6DBYjl7GW0croMzU0DTGVmm42bTf9bGZtlmA212yP2T1zqjnTPMN8lXmLeZ+FiUWExXSLWovblhRLpmWW5RrLs5bvraytkqwWWDVYPbPWs+ZYF1vXWt+1odn42kyyqba5Zku0Zdrm2K63vWyH2rnaZdlV2l2yR+3d7IX26+07RhBGeIwQjage0emg6sByKHKodXjgqOsY7jjXscHx5UiLkSkjl488O/Kbk6tTrtNWpzujtEaFjpo7qnnUa2c7Z55zpfO10bTRQaNnjW4c/crF3kXgssHlpivdNcJ1gWuL61c3dzeJW51bj7uFe5p7lXsnU5sZzVzMPOdB8PD3mOVxxOOjp5tnoed+z7+8HLxyvHZ6PRtjPUYwZuuYR95m3lzvzd5dPgyfNJ9NPl2+pr5c32rfh37mfny/bX5PWbasbNYu1kt/J3+J/yH/92xP9gz2iQAsIDigNKA9UCswIbAi8H6QWVBmUG1QX7Br8LTgEyGEkLCQ5SGdHCMOj1PD6Qt1D50R2hqmGhYXVhH2MNwuXBLeHIFGhEasjLgbaRkpimyIAlGcqJVR96KtoydFH44hxkTHVMY8iR0VOz32bBw9bmLczrh38f7xS+PvJNgkSBNaEtUTUxNrEt8nBSStSOoaO3LsjLEXkw2ShcmNKaSUxJRtKf3jAsetHted6ppaknpjvPX4KePPTzCYkDvh6ET1idyJB9IIaUlpO9O+cKO41dz+dE56VXofj81bw3vB9+Ov4vcIvAUrBE8zvDNWZDzL9M5cmdmT5ZtVntUrZAsrhK+yQ7I3Zr/PicrZnjOQm5S7J4+cl5bXJNIS5Yha843zp+R3iO3FJeKuSZ6TVk/qk4RJthUgBeMLGgu14Yd8m9RG+ov0QZFPUWXRh8mJkw9M0ZwimtI21W7qoqlPi4OKf5uGT+NNa5luOn3O9AczWDM2z0Rmps9smWU+a/6s7tnBs3fMoc7JmfP7XKe5K+a+nZc0r3m+0fzZ8x/9EvxLbYlaiaSkc4HXgo0L8YXChe2LRi9at+hbKb/0QplTWXnZl8W8xRd+HfXr2l8HlmQsaV/qtnTDMuIy0bIby32X71ihuaJ4xaOVESvrVzFWla56u3ri6vPlLuUb11DXSNd0rQ1f27jOYt2ydV8qsiquV/pX7qkyrFpU9X49f/2VDX4b6jYabSzb+GmTcNPNzcGb66utqsu3ELcUbXmyNXHr2d+Yv9VsM9hWtu3rdtH2rh2xO1pr3GtqdhruXFqL1kpre3al7rq8O2B3Y51D3eY9unvK9oK90r3P96Xtu7E/bH/LAeaBuoOWB6sO0Q+V1iP1U+v7GrIauhqTGzuaQptamr2aDx12PLz9iOmRyqM6R5ceox6bf2zgePHx/hPiE70nM08+apnYcufU2FPXWmNa20+HnT53JujMqbOss8fPeZ87ct7zfNMF5oWGi24X69tc2w797vr7oXa39vpL7pcaL3tcbu4Y03Hsiu+Vk1cDrp65xrl28Xrk9Y4bCTdudqZ2dt3k33x2K/fWq9tFtz/fmX2XcLf0nsa98vuG96v/sP1jT5db19EHAQ/aHsY9vPOI9+jF44LHX7rnP6E9KX9q8rTmmfOzIz1BPZefj3ve/UL84nNvyZ+af1a9tHl58C+/v9r6xvZ1v5K8Gni9+I3+m+1vXd629Ef333+X9+7z+9IP+h92fGR+PPsp6dPTz5O/kL6s/Wr7tflb2Le7A3kDA2KuhCv/FMBgRTMyAHi9HQBaMgB0eD6jjlOc/+QFUZxZ5Qj8J6w4I8qLGwB18Ps9phd+3XQCsHcrPH5BffVUAKJpAMR7AHT06KE6eFaTnytlhQjPAZuiv6bnpYN/UxRnzh/i/rkHMlUX8HP/Ly1qfFec6fdMAAAAimVYSWZNTQAqAAAACAAEARoABQAAAAEAAAA+ARsABQAAAAEAAABGASgAAwAAAAEAAgAAh2kABAAAAAEAAABOAAAAAAAAAJAAAAABAAAAkAAAAAEAA5KGAAcAAAASAAAAeKACAAQAAAABAAAB+qADAAQAAAABAAACqgAAAABBU0NJSQAAAFNjcmVlbnNob3TdpBXgAAAACXBIWXMAABYlAAAWJQFJUiTwAAAB1mlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iWE1QIENvcmUgNi4wLjAiPgogICA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPgogICAgICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIgogICAgICAgICAgICB4bWxuczpleGlmPSJodHRwOi8vbnMuYWRvYmUuY29tL2V4aWYvMS4wLyI+CiAgICAgICAgIDxleGlmOlBpeGVsWURpbWVuc2lvbj42ODI8L2V4aWY6UGl4ZWxZRGltZW5zaW9uPgogICAgICAgICA8ZXhpZjpQaXhlbFhEaW1lbnNpb24+NTA2PC9leGlmOlBpeGVsWERpbWVuc2lvbj4KICAgICAgICAgPGV4aWY6VXNlckNvbW1lbnQ+U2NyZWVuc2hvdDwvZXhpZjpVc2VyQ29tbWVudD4KICAgICAgPC9yZGY6RGVzY3JpcHRpb24+CiAgIDwvcmRmOlJERj4KPC94OnhtcG1ldGE+CqBC+VUAAAAcaURPVAAAAAIAAAAAAAABVQAAACgAAAFVAAABVQAAb48ZLAQxAABAAElEQVR4AeydBZQst9GF9czMzBQzQ8wYMzMzx8zMzMzMzHzMMTNzDC+O7ZhjZvv971Nc/ddq1UM7szu7e+uc3Sa1Wn2l0ZWqStUD9thi4kFBIgSEgBAQAkJACPRJBAaI6PtkveqlhIAQEAJCQAhEBET0aghCQAgIASEgBPowAiL6Ply5ejUhIASEgBAQAiJ6tQEhIASEgBAQAn0YARF9H65cvZoQEAJCQAgIARG92oAQEAJCQAgIgT6MgIi+D1euXk0ICAEhIASEwIBJJplE6+jVDoSAEBACQkAI9FEERPR9tGL1WkJACAgBISAEQEBEr3YgBISAEBACQqAPIyCi78OVq1cTAkJACAgBISCiVxsQAkJACAgBIdCHERDR9+HK1asJASEgBISAEBDRqw0IASEgBISAEOjDCIjo+3Dl6tWEgBAQAkJACIjo1QaEgBAQAkJACPRhBET0fbhy9WpCQAgIASEgBET0agNCQAgIASEgBPowAiL6Ply5ejUhIASEgBAQAiJ6tQEhIASEgBAQAn0YARF9H65cvZoQEAJCQAgIARG92oAQEAJCQAgIgT6MgIi+D1euXk0ICAEhIASEgIhebUAICAEhIASEQB9GQETfhytXryYEhIAQEAJCQESvNiAEhIAQEAJCoA8jIKLvw5WrVxMCQkAICAEhIKJXGxACQkAICAEh0IcRENH34crVqwkBISAEhIAQENGrDQgBISAEhIAQ6MMIiOj7cOXq1YSAEBACQkAIiOjVBoSAEBACQkAI9GEERPR9uHL1akJACAgBISAERPRqA0JACAgBISAE+jACIvo+XLl6NSEgBISAEBACInq1ASEgBISAEBACfRgBEX0frly9mhAQAkJACAgBEb3agBAQAkJACAiBPoyAiL4PV65eTQgIASEgBISAiF5tQAgIASEgBIRAH0ZARN+HK1evJgSEgBAQAkJARK82IASEgBAQAkKgDyMgou/DlatXEwJCQAgIASEgom/jNjDZZJOFEUccMQwaNCi89tprbVzSykUbYYQRwgILLFAkeumll8LHH39cHGunZxFQ/fQs/nq6EGg1AiL6ViPchfxvv/32MGDAgJjDsssu24WcevbWaaaZJpx44olFIa655ppw0UUXFcdlO8MPP3yYZ555wpxzzhnGH3/8MMooo4ShhhoqfPbZZ+HDDz8Md911V3jjjTfKbm/Jeco077zzxr8JJ5ww/POf/wxPPPFEeOqpp8Lvv//ekmf6TIcYYogwyyyzhPnmmy9MOumkYbTRRgvDDTdc+Oqrr8JHH30Uy3H//ff7W6ruN1o/VTNWAiEgBNoCARF9W1RDvhD9mejXWGONsMkmm+SBcWffeuutcOSRR4ZPPvnEnW3N7mKLLRZ23XXXYvDlnwLJ77bbbuHNN9/0p5u6P8EEE4Rzzz03+3z/oP/+97/h+OOPD88995w/Xbovoi+FRheEQJ9AQETfxtXYn4l+s802C6uttlpNtfPtt9+G9ddfP/z66681pW8k0dJLLx122GGHirf+8ccfYa+99gqvvPJKxXSNXsSUc8YZZ9R0O+ae7bbbLrz33ntV04voq0KkBEKgVyMgom/j6tt5553DsMMOGyCy008/vY1LWrlojRDJhhtuGNZee+3w6aefhieffDK8+uqr4d///nf48ccfw3TTTRfWW2+9wAzX5Nlnnw3777+/HTZ1iw372muvLWbSP/30UzQ9oLJfdNFFA2U1EwvXVl111aY+3zKbaKKJwjnnnBPbw/PPPx9n7O+//374/PPPoxqfgdGss85qyWO6tdZaqzgu22mkfsry0nkhIATaDwERffvVSZ8rUSNEMuSQQ4ZhhhkmEnsZIMxumeUirSTYzTffvCBvZso77bRTtM3HBw/+h0oftb3JoYceGh5//HE7bOoWP4VvvvmmNM8tttgirLLKKsV1BiEMBCpJI/VTKT9dEwJCoL0QGDC40xrE7KCaim+kkUYK/CF0ND/88EM8XnjhhcP0008fsJU++uijVTuV9PWZpcwxxxxh6qmnjp7Y2BVff/31gBq0TMwBietffvll+OWXXwJOSnPPPXd03sIx6YUXXggvvvhixXwsf8pPZ8cMkdnbu+++G+9/5513LEnpdrzxxovXIACzE5PfQgstFGd5zEYpS6X3scyHHnro6Gxmx37LTLZWAYsxxhijQ3LqDJx6QlpFJDik7bfffsUrrb766rFdFieatHPVVVdFR0Cyy2kODj/88DDbbLMVT8NOjzamJ4S6v/XWWwsNA5ogTECVpFX1U+mZuiYEhED3ITBgsD1xEI/DvnnEEUdENWnu8QcffHCYa6654qUHHnggdnwQdCoffPBB2HHHHSvOxLhn8cUXjzZPvKhTgRRPOumkcO+996aX4vGFF14Yxh133LhPOtSVDDhMfWo3ofalLF9//bWdKrYMFg444IAw1VRTlZIr5H3LLbeEs88+u7gv3bnjjjviKZvpHXfccQHC9sK1k08+Odx9993+dKd9y6vThcEn6vG6n3baacMJJ5zQIRuW5/lZZ4eLLT5oFZHMNNNM4eijjy5Kv+6660bv8+JEE3YY/J133nlFTtSv92rHAx5nQC/U93LLLedPdeu+9+1gdQOrHCpJq+qn0jN1TQgIge5DoCB6e+QNN9zQoWOz857oIWJmDmXy3XffBVSIOYLlHlSbuUFCmh/ECJGn4okeu+3EE0+cJimOmWXnvLeZddNp1yLM8Jmh5Zy9PDnjeY3KuUyuvvrqcPHFF5ddDj6vNFFXif7tt9+u6kyWPrNZx60iEhzf0JwgrSLXBRdcMOy9997FM1ZcccViGR2/gSuvvDKMPPLI8br/h39BJRW7T9vM/fnnnz/su+++RZZ77rlnePnll4vj3E6r6if3LJ0TAkKg+xEYMNiBaRDOQzYLpcPcaKONOqngPdFbMQcOHBhuuumm8P3334ellloqrne2a9goIfRUcKLizwTV+3XXXRdV/5NPPnlA/WqzddIwC02DxXiit3x4HqYDAszggORV17vsskun9dZG9LwvKnpUspAhAwMGDnTodIAmZQOglJwZBIEJJgjMEWuuuWZc50w+PGudddYpJYCVVlopmg7smRtssIHtdnlG35NET736ARvq5GrajeLFMzs4KP79738PSy65ZHEVEwlttNmCgxsrABBMHyuvvHLxiG233baYuRMEaOaZZy6u5dpccbFFO5QNfwIbhDPQpr1Vk2bXT7Xn6boQEALdi0B0xoNY8eY1ssdm72cFFCkl+pwdkuU8NvOE1CBtb1vG/s2s1ma92K732WefTm+M2hn1M/LFF18ET3icS4keEvHEQUfH4IFAIgimhmOPPTbu2z/8DTbeeONw6aWXlmoemMkxo0MYzLC2OxVP9Lwz66x9EJcxxxwznH/++dGxjHtR+9aqSfAqWMM1fX7uOKe670miz5WxnnOowW1wSJ1C9N5Mw+Bsm2226dDW6sm/UlpP5r4N+DXtaHogVO+Zn6r4Kz2jkWv8Zi0IEU6LBPLxmDAooS3W4mfSyPN1jxAQAr0HgcLrPiXp1MaYEj0zByJxeYHAb7755mJGcfnllwf+TLCXM/NH8JKGOHPRxCBhBgTWcTErxhxg4okejQBrqFPZY489wiKLLBJPszRr9913T5NUPR511FGjapaEkHiKCec90ecGP6TBS9tmn+mskOtlIqL/HzLe6z3FCh8KBqm1ODum99ZyjB8H0fkQ39YIXENkPMQGmjjB2SCWASRq/VYJs/CyJZe0QwbqOMxKhIAQEAIF0XtSAxZmKN7G7omeyFs2w0ohZJZhKu9UfX/qqaeGKaecMt5CGFPfERqp2xa7Omp4hNk4s3ITT/RlM2RspCwtQhiQQBaVBG0DnSdqe2ZIJltttVUx4EA1mnque6I/5phjwoMPPmi3Fttxxhknrru2E8svv3xNxNQo0bMEyy+x4rmEan3sscesCL1qiymJwR5i7cO/wDPPPBMdK/25Zu3j7IfTH2L+Hph1tt5663iO1SrM+hFMNtZ2GPBWcuKMN3ThH8sKjehzmPD72n777UvNRF14tG4VAkKglyFQED3l9sTCsiUfQtMTPepAOpGceAepVF3slynl7i07R7msUyONJ/pLLrkkkG8qSyyxRLHEyTroNA3qTuyvpDWzRZrGH6eDH655ot9yyy0Dqw5y4rHNaUOq3VOP6j6XV185xyBmxhlnjA6WNqPm3XLmpma8szffMPClvdDeWC2ClofBJOYl5Lbbbiu0WZhrrr/++mYUoWoeDC7wB8E3xTu5EmiJNtsqbUfVgimBEBACbYFAB6LH4czs2mlH5Yk+t5bY3sarWVP7uu8ILX0t20pEb2rTNB9P9Gk5SIvtnGVT2HtrFUwEqG+9eKKvNFP3al3WXeM4WE384EBE3xktP6jkKjPravEgOudS+YwPQIO5iXgR5nSXmqZ8fbHk7uGHH66ceQuuYq7CbGWSltHOaysEhED/QaCU6LF7ooo0aYTovU2TfDzZYYO/7777LPuK2//85z8dbPl+Rt8o0V9wwQXBgt3wcOyaOPSxXM9HEmPAY6rRakS/wgordCinfyn/7rWSgCcOEb1H83/7OF3eeOONhTaGQddpp53WOWEXzrASAvNNKrQRMw1xDY0Q6noTfF5YltkTAgZTTDFFfDRf+WOwIhECQqD/ItCB6D2x4MyDOtTEE32tqvs0HTZ5fAGQsuVq9rxK264S/VhjjRVQ+ZvgO3DnnXfaYbHFKdAHG6lG9LWq7iulKx4+eMfXh4jeI/P/+2hlLOY968VZN14m5ijnr+ecQf11oi0eeOCB/lRU2aM9GDhwYHGeT+kecsghxTHL8vyKk+LCnzuNlCXNo+wYs9oyyywTL1OGWj8OVJafzgsBIdC7ESiIHlU2nsImacAPT/SV1ueecsopMdoc+aTOeP4aHwTxHaM9t5ZtV4nef4kMz2SWAeaE7477D6VUI3qWBeai+aXR1Sqp+H05GiV6YggwmPCCqaAnVMm+DK3YRzU9+uijx6xRq7PCIScpEVuaav4SqZMq9+UCOXkzwm+//RbjMNgz0i3BlzAtealnNYa/L7dPm6XtIpXad+5enRMCQqDvIVAQve98ckvJPNEDA17HfDnLC+pL1P2m6k7tg34JHw5CeFI3sgSoq0SPip2AK0ilQQte0z7qXjWiTzUYhg1Bf/jwCVJPh94o0fe1dfSGY7rF9OJNKzkCtnvSQZudr0b0pPNaA0ic1Repg5t3NK0WvMcPCqwc9bQLuye3TaP1lbXJ3L06JwSEQN9EIBL9JJNMEr3aTZ349NNPd1JXpkSP2hLi9h2eX7vOYCENmIPjG0FFLL49dnFUremSNaAmLR7DeFbjvOalq0Tvv+tNOfnOOB2iF6IFQgJeqhE9ebHuGmdFE8gIfwd7Z2b8aRx6S5tu+zPRo1265557YjyFn3/+OYUmsGQR/wy+WWBCnAaWEeakK0TvB4bkDfFjejLxA1jOVbPPN0r0xx9/fPzwE2GU+Y5DKnjf8zsl/r5JK/wWLG9thYAQ6B0IDBi89nsQQWyM5CErItGl3uUp0fN6LFvDkx47IDNWwsqaPPTQQ+Goo46yw2LrQ4pyEk9m1sJD+nToLBNiRso3x9EM5Dzmu0r0PJdAK0a+DFYIs8uMELs8H8ixWACkNalG9KQDP8icqH98MIcgO7a2mmsECUq1GAxqMJWk4r8ljvOiF6KxMYv0Ay273hdm9LaaAcyIg/Dxxx/HLbEV+OLhX/7yl0JzxHtXczrrCtGnTn+UafDHoGKbgVTB2yR10rPzftso0XutAc8BE94bPwMw4fdnbZrn8dtisJwbKPnyaF8ICIG+jUCnj9qk6nZ7fU/01T7ewmdiUe2XfdQj19HZc9Jtq4ieSHVl9lwrA1oLtB109Eg1ood8K63HZyaWEjb5ps6BnKtFymaOfYnoa8GB0LQE1UkHUP7erhA9+eDFjgbBE6nPn33aOx76PtBUmobjXPuvRXXviT6Xrz/HYCQNx+yva18ICIH+g0BB9Iz+DzrooMDHOXLiiZ4odcxS+R642ePtHhyiCDeb+9KbpWHLWmTi3BMAJSfMVAm4g4oU7YAXH360LBpdtXX05IdKFvV8Ss50ksSr5z1YMmXajtRBkTz8zBPiRb1qsQi4jjAwopxlznDNJno0CTg+ekmDF/lr7bhPW0Srk/synJUXXKkflkrmNBuWji2fWKYNp1KLjd7uYdBHpDxbOWLnaS8sAcUEVGmwYem9z4adq4XoGcz87W9/izEg0t+d5UNZMB3RDqsNOOwebYWAEOjbCAwYHOlrEB1DqqpPXzslesLSQpDEAZ9hhhmi6h0npFo6Op83amtCjDJjIgwttkdm0hBttc7b59PoPgMWBh2QCvusBiA2fq3iid5i4aNGRf2PsPKgp9ZT1/oO7ZyOgSB1g68D9ngjVWzxOIN2RxtJ8SGiIp+DZUD14osvhqeeeqo0fkJ6bzOOGXgSHZDVHPgqgAFaNDDB16TaILsZZVAeQkAI9B4ECq/7akXOEX21e/rD9RzR94f31jsKASEgBIRA70BARN/FehLRdxFA3S4EhIAQEAItRUBE30V4RfRdBFC3CwEhIASEQEsRENF3EV4RfRcB1O1CQAgIASHQUgRqJnrvKVz2DfiWlrRNM7egNjiJmTNemxZVxRICQkAICIF+iEDNRN8PsdErCwEhIASEgBDo9QiI6Ht9FeoFhIAQEAJCQAiUIyCiL8dGV4SAEBACQkAI9HoERPS9vgr1AkJACAgBISAEyhEQ0ZdjoytCQAgIASEgBHo9AiL6Xl+FegEhIASEgBAQAuUIiOjLsdEVISAEhIAQEAK9HgERfa+vQr2AEBACQkAICIFyBET05djoihAQAkJACAiBXo+AiL7XV6FeQAgIASEgBIRAOQIi+nJsdEUICAEhIASEQK9HQETf66tQLyAEhIAQEAJCoBwBEX05NroiBISAEBACQqDXIyCi7/VVqBcQAkJACAgBIVCOgIi+HBtdEQJCQAgIASHQ6xEYsMwyywz+lPqg8Nprr/X4ywwxxBBhiimmCBNMMEFRlvfffz8MHDiwONaOEBACQkAICAEhUDsCA1555ZVBJF922WVrv6vJKccdd9xw4IEHhskmm6xTzpD8Ntts0+m8TggBISAEhIAQEALVEehxomcWf91114XhhhsuW1oRfRYWnRQCQkAICAEhUBMCPU70G220UVhrrbViYTEhPPbYY+GNN94Iv/zySzz3wQcfhOeff76ml1EiISAEhIAQEAJCoCMCA84444xB3377bTj99NM7Xummo2OOOSbMOOOM8Wn3339/OO6447rpyXqMEBACQkAICIG+j0CPe91fdNFFYZxxxolIn3TSSeHuu+/u+6jrDYWAEBACQkAIdBMCA6aZZprojPfjjz9WfOQII4wQRhlllJjmu+++C/wh0047bVhkkUXCUEMNFV566aXw9NNPh0p54Xg3YMCAeC//IHfL98wzz4z3FxcH73z99dcV88PGTxlmm222MOGEE4a33347qvrfe+89n03FffIYY4wxOqT55ptvCvNBhws6EAJCQAgIASHQixCo2Ua/8847hyWWWCK+2jPPPBMeeeSRsMMOOwRI0gu29X333Te8+uqr/nSxf/vtt3cg+uJCyQ7EzXNysuqqq4bNNtssm9+vv/4ajjjiiPDkk0/mbu1wjoHCCSec0OEcyw132223Dud0IASEgBAQAkKgtyHQENF/+umnYeyxx84SLAD88ccfYbXVVgs///xzJzzqJfoyr/tDDz00zDHHHJ3yT0/ccMMN4bzzzktPdzjOEX2lAUaHm3UgBISAEBACQqCNEWiI6O19IOG77rorqvEHB94JM8wwg10Kt9xySzjrrLOKY9tBKzD00EPbYZyRDz/88PH4oYceiur/4uLgnQ8//DC8+OKL/lSYa665wsEHH1ycQ81+/fXXB8oz/fTTB2b69gw8+fHs//zzz4v06Y6IPkVEx0JACAgBIdBXEGiY6LHFE+TGy4knnhgG2/zjKYh1ww039Jez+5dffnkYffTR47XDDjssLq/LJnQnvQMfNvxNN920gx0fP4BzzjmnIHuW52FOKBMRfRkyOi8EhIAQEAK9HYGGiJ5ZMmvfzSHPQJh33nnD/vvvHw9/+umnOLO2a2XbeokeEr/wwguL7LCt33vvvcWx7Wy33XZFtD/Ku9xyy9mlTlsRfSdIdEIICAEhIAT6CAINET02+o033rgTBGOOOWa49NJL4/lq5Go310v0Cy64YNh7773j7b///ntYYYUVLKsO21FHHTVceeWVxbl11lknevAXJ9wOXv+rrLKKOxPCP//5z5q0Cx1u0oEQEAJCQAgIgTZDoCGiJ3LdLrvskn2VO+64I55vFdGvt956gT8E2/zaa68d93P/vOPffvvtF5577rlcMp0TAkJACAgBIdBnEWiI6Fled8ABB2RBaTXR77XXXmGhhRaKz/7oo4/C5ptvni0HJ/G4txj6559/fnTYK02sC0JACAgBISAE+iAC/Ybocc676aab+mAV6pWEgBAQAkJACJQj0OuIvlHVPV73+jhOeUPQFSEgBISAEOibCPQ6op9//vmLpXIE5llxxRVjgJ60erxjINew5WPTzwnhb7fccssOlx599NHw8MMPdzinAyEgBISAEBACvQ2BXkf0fACHdfQmfHUPp7tUfMjeao6BWl6XoqdjISAEhIAQ6CsI9DqiB3hC2k4wwQSxDljLj0Oen61PMskk8bO7Qw45ZEyTC+4TL/z5T0Tv0dC+EBACQkAI9CUECqK/+uqrO7wXH4W56qqrCrW4nyH3pNc9heRLdYcffnhRXr6Wd+edd8ZwuVNOOWVYaqmlgpE8s/kNNtggfPnll0X6dEdEnyKiYyEgBISAEOgrCBREn3shosu9++678VI7ET0FImgOwXOqCQF5+KskIvpK6OiaEBACQkAI9GYEaiZ6H1L28ccfD3w9LiddWUdP+Nxnn302l2323NJLLx223XbbYvbuExGC96CDDur0kRyfxvanmmqqcMopp9hh3OrrdR3g0IEQEAJCQAj0UgQGDLZnD+qlZS+Kjb2eT9ZONNFEAYJmsFBJVV/cqB0hIASEgBAQAn0cgT5B9H28jvR6QkAICAEhIAQaRkBE3zB0ulEICAEhIASEQPsjIKJv/zpSCYWAEBACQkAINIyAiL5h6HSjEBACQkAICIH2R0BE3/51pBIKASEgBISAEGgYARF9w9DpRiEgBISAEBAC7Y+AiL7960glFAJCQAgIASHQMAIi+oah041CQAgIASEgBNofARF9+9eRSigEhIAQEAJCoGEERPQNQ6cbhYAQEAJCQAi0PwIi+vavI5VQCAgBISAEhEDDCIjoG4ZONwoBISAEhIAQaH8ERPTtX0cqoRAQAkJACAiBhhEQ0TcMnW4UAkJACAgBIdD+CIjo27+OVEIhIASEgBAQAg0jIKJvGDrdKASEgBAQAkKg/REQ0bd/HamEQkAICAEhIAQaRkBE3zB0ulEICAEhIASEQPsjIKJv/zpSCYWAEBACQkAINIxAWxL9uOOOG6aZZpr4Uq+99lr4/PPPG37B/n7jOOOME8Yee+wIw3vvvRd++OGHXg3JCCOMECaffPL4Dp999ln49NNPe/X7tEvhp5122jD33HPH4jzyyCPhnXfeaZeiqRxCQAh0EYG2JPpTTz01TDnllPHVLrvssnDFFVd08TX77+0XX3xxQfQnnHBCuPfee3s1GLvttltYbLHF4js8+eST4eCDD+7V79Muhd9uu+3CsssuG4tzzTXXhIsuuqhdiqZyCAEh0EUE2pLoTzvttDDFFFPEV7v00kvDlVde2cXX7L+3e6I/7rjjwv3339+tYMw///xh1VVXjc986KGHws0339yl53uif+KJJ8IhhxzSpfx08/8QENGrJQiBvouAiL7v1m18s54m+i222CKsssoqsSyYYSDqroiIvivold8roi/HRleEQG9HQETf22uwSvnXWmutwqZ91llnha+++qrKHc293Gyin3feecPCCy8cC3nnnXeGF198sbkF7qe5iej7acXrtfsFAiL6flHNPfeSzSb6nnuTvv1kEX3frl+9Xf9GoEeJfpJJJgnzzTdfmGiiicJzzz0X8Pb95ZdfQr02+tFGGy3MOeecYcYZZww//fRTnOWR388//1xz7c4+++xhqqmmimXhJjy6//Wvf4XHH388/PrrrzXnQ8KxxhorzDrrrGG66aYLQw01VHj//ffD888/H959992K+Qw//PBh1FFHjWl+/PHH8PXXX8d984gef/zxwxdffBFQgT/66KPZvIYccsgwzDDDZK+Bxx9//JG9ZifHG2+8uDto0KDwySefxP3pp58+LLTQQmHAgAEBB7gXXnihNB/Kz3uYrLPOOmGJJZaIhwMHDsza1L/55pvS1QBDDDFEGHbYYS27Dlvayu+//97hXKUD8qFe+MN7/9VXX43trtqqDtrXcMMNF7P+8ssvYxulXHipzzPPPFFLAiZoF6rha+UbY4wxAnmYsBqiJ1dEiOitJrQVAn0PgR4hepbPnXzyyWGUUUbphCjOWjPNNFNNzngsszriiCMKckwzQ7WLB38lWXLJJQOzzhFHHDGbDMJ75ZVXwp577pm97k8yaMGGbKTgr7EPSZx33nnhwQcfTC/F4/XWWy/wh0CKO+20UzjjjDPCBBNMEM/5fwwEtt122/Dxxx/70wHnxTHHHLPDOTuoxev+jjvuiMl5b56PA9/QQw9tWRTXqL+77767w3kOTjrppDD11FN3Ol/pBA6CPCcne+21Vxxk5K7V6nUPoe63336RlHP5UC+77rprMbBJ01x44YWBNovwfgwUMB8w8PHCUr8dd9yxGKD5a+n+bbfd1oHoGaCa02KatjuORfTdgbKeIQR6BoFuJ3pmp8ccc0yc6Za9MiRjnWiZ1z225w033LBIV5bXhx9+GLbaaqvsTItlWrU4hzGjX2mllcoeEc8feuihYY455qiYhotvv/122GGHHbLpUqKn8wevMoEEX3rppQ6XWYrIDDQntXjdG9FzP7NlNARlcvXVVwec/bw0QvQs+WMQkpP9998/YJfPSS1e98zczz333DD66KPnsijOMROnDhk8pOKJ/t///neYeOKJ0yTFMVqQTTbZpDgu20mJHu3EyiuvXJa85edF9C2HWA8QAj2GQLcT/QUXXBBMPcxb33PPPVFlz6x1zTXX7NQh54iepXeo900g4htuuCGqxyG5pZdeOs667DrXmEmnAlGNPPLI8TQzZEgSlT+DDNT4c801V5wFQgKViB719AYbbFBkj4r8rrvuiiT822+/hRlmmCEsssgigeA1tRK9H+ygWkZdz6BlwgknDLPNNlssd47o0SpMOumkRVnWXnvtYkZeL9GTCe9+0003RVyYqVNHprGgjLw7qncTzCeYZExQ+c8888zxEBK89tpr7VKxhbCZVefE6sGuoYGx2XUtRO+99MmDQDC33npr+P7778Nf//rXsPjiixeDxe+++y6+nz3Ltp7o7RwmHcwnaIIYdKKKN9lll13CG2+8YYfZrYg+C4tOCgEh0AIEupXoIbxjjz22eI0TTzwxEr2dwLZMp+pnX7mAOZC2qbOxY2+22Wad7JsQHDN+BEJad911O6hUsSX79fmkzdlqmdEuuOCCpep2ZowMGGzm+9FHH8UZe87eah7wRx11lL1yh62f0dsFyshgJ5XVVlstPPDAA6UEaenrXV7nZ/TghkrbkxZmgfPPP7/wA6ikdqcMzXbG88RdjeipG4K/mC2cQRwqfC8MjPy5008/Pdx+++0+SWyTNrjgAloLb7Yg/+uuu64YAFEvvp13yOzPAxF9DhWdEwJCoBUIdCvRQ3A2u2MWCBmnkqrTU6KfZZZZwpFHHlnctvXWW0dnt+KE2/H26rPPPrtDsBZmnSw3M9l4440bCqeKTXappZaK2UCMDBhwmGtEUqLHwWufffZpJKvinq4Q/Ztvvhl23nnnIi/bwXbPzBqppnLuSaL3eOa0D/Y+fuCIz8Omm25ql+LWz+jRPKy//vodrnOwxx57RK0N+zj57b777uyWioi+FBpdEAJCoMkIdCvR+w6TmaNXv/v3QrVqM+SU6DfaaKOoKiU9NmRmYCZm1+eY/WWWWaYIpYuqFRusCbMwOlsTSG3fffftpBmw62XbU045Jar5uY69HHV6o+KJiTwwBzQ6aLAydIXo8aXIOQ5igvAhUpdffvmsDwRl6Emi33vvvaM2hnJgW8dXIycE9KGcSM4pzrfbMg2G1yCh1dl8881zjyrOrbHGGtHz304w8L3xxhvtsNu3stF3O+R6oBDoNgS6leivv/76YunVmWeeGW2luTe96qqrCo/8lOiJbY7tvF7Bvm2dud3rZ3KcY9Zny9dQ8/7jH/+oukQP1botics5p9mzatl6osfObxHlarm3LE1XiH7LLbcMH3zwQTZr1Ns2sILUILec9CTRe8fAp59+Ohx44IG5IsZVHkcffXS8RhtYbrnlOqTzRH/JJZcE2mcqLCE07UetDnlpHj15LKLvSfT1bCHQWgS6leg9ORx22GHhsccey76dd9hLiR4PahzS6pUc0bN+n2Vift23z5dO/6233qo40/fah7IZsM+z0r4nevwFzMeg0j3VrnWF6CvN1P17H3744aXr+nuS6L3pppIGCUc62pkJDoc45pl4ok/t85bGEz2DRe+caWnaeSuib+faUdmEQNcQ6Fai93ZJ1Oio03PiO9aU6LG12/Im1O3HH398LotO57Ct5hzkMBFgk+XjK3zO1WapPgM8tHGkywVD8YRXi1e7zzfd90TPOvptttkmTVL3cVeIfoUVVigNSOPfG5+Jhx9+OFu2diH6SjEVcDD0Do+rr756h7bi26OIPlvNOikEhEAbI9CtRM/SKgtMkzrHeYz8sreU6A844IAi8EmzyNCejd2eCHnMzvDGNj8Brpep5S+//PJilQDvByk0Ku1G9LWq7iul60mi96r7Z555JtB2coKDqK2EqKa6F9HnENQ5ISAE2hmBbiV6bxNneRKdZiqQ7S233FIsiUqJHpUoa7cRltbZfppPV49ZmoUJwaL3vf7663GpWZovSwSnmWaaeLqrX2drN6Ivi6TH0kbq0qSSit8TfZkXv+VTy7ae5XU+ql7OdGPPwzHOgtwQT4Gli15aMaNHVT7SSCMVjyFMMnEcekqkuu8p5PVcIdB6BLqV6H30uG+//bbwnvevueyyywY6HZOU6AnIgi3cBAcrHK1aIdtvv3303CfvMqIgDK133qrkmFatjO1G9ASXAYNUPNlWW17nPdqb4aTmn11tHb33hOcdiKWQ+3qfJ/Kcx7y/3qwZvTdjUbZqOJKmlSKibyW6ylsI9CwC3Ur0xKb3y+HOOeecGHXNIEhn0ZxPiZ5z3k6P0xRkBInkhPXe2JpTwmIdPXZ3bNjEKM8JS8hYSoaUrWnnQymo7Pl4DYIjFs/KEQraCAKvlMV1bzeiR42NuvvZZ5+N78Y/ohpSb/a+lcLXkt7HPSA/lkfmAhORthaph+ipGwLZmAmGwD9ErfNC+yAugEmOyEX0ho62QkAI9EYEupXoAYggNT5EKg55kCjR8JgZW0haAzNH9HjLQ/bmOAeBEI6UQCWQNiFyCZ1KqFii7eXsrtji8fxHmK1DZnjYE7IWxzwIytT2pKmkOfCzVtKSx0MPPRTX1ePAx1fsCLeK01etIXAb8T8gVrot9aMcyIorrlisKsBOzSzdC05qfqDjI+ORDuwgc+oITKkj+zoe11B755wc7RmQLcsqMckg4MFHghiY2VcBaQN+MGH3EuM+/UAOvhPmjElwG5ZAeuErgT7+vw9oRDpm7JiNWC/Px2n4+pxJWRAnEb0hpK0QEAK9EYFuJ3pIniVtEECZQAZGDDmi5z5m6QRAsXRleXG+GtFXupdrkAfBdCqJtwdXStdKoifgSiVcc+VKvwPgiR4iTr9c5/NAG4KTYjUh6iBL1sqkLAiN938ouzc9nw6QwIPBpQ9hm97DMcGXGMwRPyEVEX2KiI6FgBDoTQh0O9EDDrNOPh/Ld9u9QMgsc1pggQWKz9QSV50ZYU6YIWP3n2yyyXKXI8EzcyT2uF8+RWI6ftTSDDxMtZtmQtAayJMgKbUI3tsMCFKthN3LbJI4ALkvpJHGq+7RLniVsuVRaesDElVK56+VET11gd2W5Yv2ERu7D1LET6JsSZ2l81tm4jjmoblh8GDaGNKUrXHnGfhk1CMp0du9BLPxH7Cx82zRDGASKPuwjo/dUBYrod519H55ImWQjR4UJEJACLQCgR4hensRCB8C4GtrTz31VFQP59aqW/qyLbN6VLx8xx5Swm6PFzNe8HhRVxNU0vyNP/748X6iwaHixqbbSHkIwANB4ZPAPuFXUVd7FXm1MvXUdZvRey0IphK+v46gZn/33Xd7qnhdeq61E0w6LPPE1INJopY20qUH94Kb5YzXCypJRRQCDSLQo0TfYJl1WwsRyBF9Cx+nrNsEARF9m1SEiiEEWoCAiL4FoPbmLEX0vbn2Gi+7iL5x7HSnEGh3BET07V5D3Vw+EX03A94mjxPRt0lFqBhCoAUIiOhbAGpvzlJE35trr/Gyi+gbx053CoF2R0BE3+411M3lsy8Meme8bi6CHtcDCIjoewB0PVIIdBMCIvpuAlqPEQLtjADfjOCrfchpp50Wl6S2c3lVNiEgBGpHQERfO1ZKKQSEgBAQAkKg1yEgou91VaYCCwEhIASEgBCoHQERfe1YKaUQEAJCQAgIgV6HgIi+11WZCiwEhIAQEAJCoHYERPS1Y6WUQkAICAEhIAR6HQIi+l5XZSqwEBACQkAICIHaERDR146VUgoBISAEhIAQ6HUIiOh7XZWpwEJACAgBISAEakdARF87VkopBISAEBACQqDXISCi73VVpgILASEgBISAEKgdARF97VgppRAQAkJACAiBXoeAiL7XVZkKLASEgBAQAkKgdgRE9LVjpZRCQAgIASEgBHodAiL6XldlKnBXERhiiCHC1FNPHYYccsjw/fffh4EDB3Y1S90vBISAEGhbBET0bVY1k002WZh//vnDLLPMEsYZZ5zwwQcfhOeffz488sgj4ZNPPmmz0vbO4sw111zh4IMPjoX/+uuvA59olQgBISAE+ioCInpXs7fcckuc5UGom266abwy77zzhv322y/u33jjjeG8885zdzR3d7fddguLLbZYaaY8+4Ybbii9rgu1ISCirw0npRICQqBvICCi/7MemUEfeeSR8ejZZ58N+++/f9zfe++9w4ILLhj3jzrqqPDQQw/9eUdzN4ceemiYY445qmYK0bdysFG1AH0ggYi+D1SiXkEICIGaERDR/wnVTjvtFJZccsl4dMopp4S77ror7l9++eVh9NFHD4MGDQorrrhi+P3332sGt9aEzOKZzZt88cUX4fzzz48q+4UWWihssskmYbjhhrPLYfPNNw8fffRRcayd+hAYb7zxwsYbbxxveu211wKaHIkQEAJCoK8iIKL/s2YvvPDCMO6448aj1VZbLfz4449hmGGGCajrBwwYECDfDTbYoCXt4IwzzgjY5hGey3N++OGHeMy/SSaZJJAGJzIErQLaBYkQEAJCQAgIgWoIiOgHIwSB3nrrrZHQv/nmm7D22mtH3PxM+4EHHgjHHntsNTzrvj7KKKOEK6+8Mj6bmy+77LJwxRVXdMrngAMOCPPMM088/8svv4SVV165SDPmmGOGoYceOh5/+eWXgeuVhAENg5fffvstfP7555WShokmmiiaFPBS//jjj8Nzzz0XXn/99fDHH39UvI+Lww8/fBh11FFjOgYwOL4h0047bZh77rnD+OOPHwdQzKofffTReI1/YDLCCCPEYwY81EklGW200QqNx6effpot27DDDlsMlHxeaGiq4eXTs8/z5pxzzjDjjDOGn376Kbz44osRl59//jlNGgePYI2AX63CioCxxx47Jv/uu+8CfxIhIASEQCMI9EuiX2aZZcL222/fCF7xHpZjbbPNNg3f729cb731An8mzObRHqSCCn+vvfYqTu+7775Rtc+JCy64IKCORvDOP+KII+J+7t9f/vKXcPLJJ8dLmCMYMPz666+dki6++OJhhx12CEMNNVSna5D8SSedFO69995O1/wJ/25ghnkEzcQEE0zgk8V9BgLbbrttJENvRmHgsv7663dK708wSIMYkZwfBasYwCsn9XjdTz755BFbG7yk+d15553h1FNP7XD65ptvLgZhW2+9dXj//fc7XC87WG655SIeXMdMg7lGIgSEgBBoBIF+SfSegBoB7cMPPwxbbLFFI7d2umf33XcPiy66aDzP7HDVVVftlIYTEBm2ZJsdnnXWWYVtGQ3EhhtuWDUPEuBkyEoCpIxAanUMvPvuuyPhx8wy/zzOED3vx2y+TBjIvPTSS3HQwuDFBB+FsqWFiyyySNhjjz1iUgYg+FGk2gbwBeec1Er0a621VsTY8M/lxTnaxlZbbVWUgXrC9IJceumlUXsTD6r88xqcaoO3KlnpshAQAv0cgX5J9LPPPntYc801i6qfaaaZCgKFaBDU+ahmEVS7b7zxRtznH6rriy++uDjuyg4z0JlnnjlmUW326meu+A6ce+658T5U5Nddd13xDnvuuWd4+eWXs8XiPtTYCA5/119/fYd0npy5QJnI+6233grMaFdfffXCl4HrOBGies+JzwvtgZEk5gLugRQnnHDCMNtss4WRRx45aiwMf0wYY4wxRsw2N1O255144olhmmmmiYevvvpqltAxBSy77LLF80n/17/+Nd5TC9FPMcUU4bTTTrNHRg0Iqx+Ib4Aaf+mllw6zzjprcd2vjEBLwewc4d28Vqa4IbNzySWXhLHGGiteOe6448L999+fSaVTQkAICIHqCPRLok9huf322yMJfPvtt4GZG+JV5a10fvOOeGUzbCsvBGLe9w8//HCxHJDrp59+eiRi9p955pnAjDAVCPXwww+PpyHeVVZZpYN9Grv41VdfXajBX3jhhbDPPvuk2YQTTjihmJlXclL0RG+Z4I/AzDYVHCDxg2BggTCLX2ONNeK+95uIJ/78530rOIUm4vHHH/dJsvv1Lq9jOaOZGxgYbLbZZh2cJXmI16qA7brrrht9EhjEmeNk2XvkCmltkmvUU87+n7tP54SAEBACKQL9nuh9R4yjmQXH8evnIUfvLJaC2JVjP3N95513KvoOXHXVVdFRjeels1dmldjUEWzuK620Utz3/w477LCANgN5++23i/SWZscddwxLLbVUPETNDtHmlhOONNJIcUBgM3S0IzlnsZToywYO9ny/ZRbO+5rk7NvM0rfbbruYJHVQtPty23qI3sdXIK9cOewZDGBwjETOPvvsgH0eMdJmAOCXaBKfAYdPhHZA/SPejwJcvfYpJtA/ISAEhEAdCPR7ovfkhnr2jjvuiPBZp512znVgW1NSZtCorRFMArvuumvpfZ7o33zzzbDzzjsXaZndYsNnixDi9cknnyyus8N1c67LqYNxJJtyyinjPZ999lkHe7KRum2ZcY844ogxLasRmI2nkhJ9maNhep8dY5pAtY/kVj14bcgTTzwRDjnkELu14rYeot9oo40KLQ+DHjQnJoYFx+zj5Gn4oVlAw4B4NfyBBx4Ynn766Xjev5/XGvln1qPuj5nqnxAQAkIgQaDfE71Xy2J/ZjmXd3yrpJpOsGzoEFu/LaOq5s1/zTXXBGbTSG52fMwxxxR+BdjosdWbeM9zCIsZf+q05gcSdl8tW2asngDtHk/0qJ5RQdcjqPNRkyN45XNsgp8BpgwjWwY9DH5qkXqIngET6esV77CJXR5TEMJAkgFlanbwvgLHH398mG666WJ6hT2OMOifEBACXUCgXxE9NugllliiA1xbbrllJAtm7uecc068NvHEE0fnLQ4gepzREGz4zXaK8rNoPMuZKZfJTTfdFIP4cJ1yMCv34gksJXNm3TPMMENMzrpvTBOp3HbbbYVGIL1W6bgWoscBz1YGVMrLXyM2AO9sZL7LLrsUTpF+EMAX6Mye7+8v2/c4eYLNpfez7tz1snOe6P3KABvMsfLBwixbHmYCufbaawttCZhVi3Vg92srBISAEMgh0K+InqVdOJI1KvXYgWt9hl/K9tVXX0UnrrJ7PRGj8s95/vvBgKnnU7U+fgj4I6TivfrJ/7777kuTZI//85//ZG35fkZvBJfNoMJJ1usTrAfx6nlPwPfcc0/A+75WqYfosbUz8EPQGDDbrkVwKrTohmgfWO2AmGbDljkyeERLw2CG2Tt1TB36tPFA/4SAEBACDSIgoq8DuFYQvQ8OQ6Q6nLVykjqn+Xj8Pr0RCOfMjk8Mf56DVFqrj0e8BYPxS8TijQ38awbR+zXwRpIQI2YMk0rr7C2N39ZD9H49e6ODFZ7tzSJ8FpcBBHXK6onJJpssDiaoL2bz5hBazTnTv5P2hYAQEAJlCPQromdmS+hVc1hjDbitKWcJFOpu1MUWgAW1rl8/jZ04NxMuA7eW895znPRltmavqiZdmff3VFNNFRgEIOZIyKzYnMTSZXkx4Z//uI/7ET97/vNy3ZtmED0P9U6EkCBxD2wZZLXYA7lC10P0OBDa9+qrqflzz7JzRCu0tfZ8V8FMNP59GOhhkrGPKxHjgFgHEiEgBIRAVxDoV0TvgfLOUH59s18//+CDDwYc3FoplINlWBbCtSzanA8M89///rdD2Ny0fN5pD78DoviZnZsALu+99156SzxmqRoDDwRHPWzGpn6OJ+v81yyiZ3kjMQAQgtQQf98cGJkBQ5z1SD1ET9Ak3wa813w9z8QR0aIp4lPAigUGliussEJckmexBVhOZw6XLJdkGaRECAgBIdAVBPot0fPtd1v+9NRTT4WDDjoo4uhV37klal0Bu+xev76dWTje8q+88kqRPJ31owZmyVaZ+CWDELZpMPyAJncv2g2I05bgoUqmLJgsUiEtM12Wv1kQnjRNs4jexzoAHxu08DwC1fBe9Ug9RE++3k4PEfOdhLKQvMzGIe/0WwpE+WOtvBevmvfLLElDvS2//PI+ufaFgBAQAg0h0G+JHg9uPtyCmNMa+3w5jrCmEAoddroEjTTNFkKyemcynsn66X/+859RTc11Izeu4WGOGaFM+DpdbpZb5h3v80lNBNj0USdD+tjIcYzDqZHlX5Sp0vLDZhE95fNRAa283rPdzqVbwvba0ja7hkaA5YYI72TOb3Yd50I0Kyakh+ytDmgbBFAiaBFfyyNELiYPtA582pjrFvbW8mDrww9zTB0xsEL46I6VieNqURJJIxECQkAI1IJAvyX6iy66KIwzzjgRI77gxqyVTto6fQLGELiku4TIdswCjUxyz0XVywy7LLa8v8cC/vhztQas8eu+/f25/e4i+lyZ8Lw3b/Zc2TiH1oH3rkdyTpEM+vhYjWlHKuVXRvTeB4L7bTkd+17DxHG9Kwm4RyIEhIAQyCHQb4nelqr5JW1+bXNunXoOwGae4/l8Zc3i2fu8cQSD5Gv9zKmPrkY+zDw33nhjn2XFfdTlxLnHMzwnaBawHzPTJqpbTvyMno/imOd/Lm21c3wBji/BmUCmZZ/YtTRsUw2Fv1a2nyN60hLeFnMPXvI5oUyo9IniZzZ3nw78LZxtzs/CL2/sLrORL5/2hYAQ6JsI9Fuib+fqhFznmWeeqBJGPUwo25ydvDveAVs8Xu6opwk4xIBh4MCBMXBNd5g1uuMd630Gs3pMGODCoAy7PQMwNC2VTCr1PkfphYAQEALNQEBE3wwUlYcQEAJCQAgIgTZFQETfphWjYgkBISAEhIAQaAYCIvpmoKg8hIAQEAJCQAi0KQIi+jatGBVLCAgBISAEhEAzEBDRNwNF5SEEhIAQEAJCoE0RENG3acWoWEJACAgBISAEmoGAiL4ZKCoPISAEhIAQEAJtioCIvk0rRsUSAkJACAgBIdAMBET0zUBReQgBISAEhIAQaFMERPRtWjEqlhAQAkJACAiBZiAgom8GispDCAgBISAEhECbIiCib9OKUbGEgBAQAkJACDQDARF9M1BUHkJACAgBISAE2hQBEX2bVoyKJQSEgBAQAkKgGQiI6JuBovIQAkJACAgBIdCmCIjo27RiVCwhIASEgBAQAs1AQETfDBSVhxAQAkJACAiBNkVARN+mFaNiCQEhIASEgBBoBgIi+magqDyEgBAQAkJACLQpAiL6Nq0YFUsICAEhIASEQDMQENE3A0XlIQSEgBAQAkKgTREQ0bdpxahYQkAICAEhIASagYCIvhkoKg8hIASEgBAQAm2KgIi+TStGxRICQkAICAEh0AwERPTNQFF5CAEhIASEgBBoUwRE9G1aMSqWEBACQkAICIFmICCibwaKykMICAEhIASEQJsiIKJv04pRsYSAEBACQkAINAMBEX0zUFQeQkAICAEhIATaFAERfZtWjIolBISAEBACQqAZCIjom4Gi8hACQkAICAEh0KYIiOjbtGJULCEgBISAEBACzUBARN8MFJWHEBACQkAICIE2RUBE36YVo2IJASEgBISAEGgGAiL6ZqCoPISAEBACQkAItCkCIvo2rRgVSwgIASEgBIRAMxAQ0TcDReUhBISAEBACQqBNERDRt2nFqFhCQAgIASEgBJqBgIi+GSgqDyEgBISAEBACbYqAiL5NK0bFEgJCQAgIASHQDARE9M1AUXkIASEgBISAEGhTBET0bVoxKpYQEAJCQAgIgWYgIKJvBorKQwgIASEgBIRAmyIgom/TilGxhIAQEAJCQAg0AwERfTNQVB5CQAgIASEgBNoUARF9m1aMiiUEhIAQEAJCoBkIiOibgaLyEAJCQAgIASHQpgiI6Nu0YlQsISAEhIAQEALNQEBE3wwUlYcQEAJCQAgIgTZFQETfphWjYgkBISAEhIAQaAYCIvpmoKg8hIAQEAJCQAi0KQIi+jatGBVLCAgBISAEhEAzEBDRNwNF5SEEhIAQEAJCoE0RENG3acWoWEJACAgBISAEmoGAiL4ZKCoPISAEhIAQEAJtioCIvk0rRsUSAkJACAgBIdAMBET0zUBReQgBISAEhIAQaFMERPRtWjEqlhAQAkJACAiBZiAgom8GispDCAgBISAEhECbIiCib9OKUbGEgBAQAkJACDQDARF9M1BUHkJACAgBISAE2hQBEX2bVoyKJQSEgBAQAkKgGQiI6LuI4nzzzReGGmqomMtDDz3Uxdx0uyEwyiijhFlnnTUe/ve//w0vv/yyXaprO8EEE4R55503ew/53n///dlr7Xpy1FFHDbPMMkss3ueffx5ee+21di1qLNe0004bxhlnnLj/6KOPht9//72ty9vXCzfCCCOEySefPL7mZ599Fj799NO+/sp6v8EI9Duip+OZY445Gq787777Ltx8883F/bfffnsYMGBAPF522WWL871xB0KcYoopYtHffffd8Pjjj2dfY/jhhw+rrrpqvPbrr7+Ga665JpuuKycZQO23334xCwh5vfXWayi71VZbLWy22WbZe3/44Yew+uqrZ6+168mNN944rLnmmrF4H330Udh8883btaixXPxWhh566LhP2buDWPh98zs3eeKJJ8I777xjh9ktbXqVVVYpfss+0aBBg8K//vWv8Morr4Svv/7aX+q0v+SSS4axxx670/myE7fddlvVPMvubeT8brvtFhZbbLF465NPPhkOPvjgRrLRPb0MgX5H9IceemiXiB5iW2mllYpq7ktEf9pppxVE//bbb4cddtiheE+/s/TSSxfX6ASXW245f7kp+80i+mWWWSZsv/322TKJ6LOwNPWkJ/oNN9wwoIVotVxxxRVhIVZWKgAAL09JREFUtNFGKx7zxhtvhF122aU4zu3MMMMM4dhjj81d6nDut99+C7feems4//zzwx9//NHhGgf+fTtdzJzYY4894gAic6klpzzRMwA65JBDWvIcZdpeCIjo66wPEX0IzFp22mmniFy7Ez2FpNMfYoghYnmZ6ZmmQEQfIWnpP0983UH01DVE7wVzwQorrOBPddqvlejtxm+++SZssMEGgf7Ai39ff75sX0RfhozONxOBfkf0qPXKbLZe9c4sAPV1KqjuLr300uJ0f5zR9zaiLypr8M70008fjjvuuHhKRO+Rac2+J77uIHqesfbaa3d6mQMOOCA888wznc7biZTor7/++oLExxhjjDDZZJOFv/zlLx1U+88++2zYf//9LYu4XWONNcK4445bnJt99tnDeOONF48ZFNxzzz3FNXYYlHz55ZcdzrXygL5v4YUXjo+48847w4svvtjKxynvNkGg3xF9Jdw9aZ999tkdbPFl9/l7/EChLH07n69VdS+i77la7M02+u4g+nPPPTdMOOGEsYIYyOF8hlRTU6dEj2/Hjz/+GO+1fxNNNFHgNzLMMMPYqbDuuuuGr776qjhOd3bfffew6KKLxtNoAXKDkPQeHQuBZiMgoneIetLuCtHTuTBqnnHGGaMTz9NPPx3ee+8996TKu6gf55xzznj/Tz/9FEfdzz33XPj5558r39jFq80ieso/3HDDxdIwW/nll1+i6nzuuecO88wzT+wYX3jhhfheOTsnN+Zs9Kjf55prrqiRwUGPzvvNN9+s6627OqOnDKj/Z5tttkgo+DI8//zzddWvLzAe0DPPPHOcLUIsaJGeeuqp8MUXX/hkxX6tRM+s0pxEwapS2+lqewMTiJK6HXbYYQPe9cwUqdvunNHj9HfTTTcV733SSScVJiawhbzLpBai514c2bBzmxxzzDHhwQcftMNO254meuqGOskJv8t6VkGMOeaY0b9p0kknDewzwPnkk08Cmo33338/94iWnfNtlrb90ksvxd8hg7tahNUrOGAiaGltUDf//PPHfnf00UcPH374YaDvRrtbJmBLv4amGP+Tf/zjHwUWDAiHHHLIiDFYlwkrg1hhxHasscaKz+V9WFGTmobSPPzv/OOPP46Xc/wjonfIdZXocdI744wzihmFyzrg4YojYBmxkZZO/4gjjgg0wpygajv11FNzl5pyrllEf+GFFxbqSzpbGjEDHyMeKywe2DvuuGPW6zgl+quuuipsvfXWnfLgB7TXXntV/DHa89h2hehZaYAHf/oe5MsPkrqjnmsRCBs1by4v7h84cGB0DksHiLUQPZ7UDIhMcBxDFZ1KM9obquB99tkndmg+f9o55cAfwrzuWz2j946XzJ6ZbeM4ZxjTfsoIqVaihzjxlDehzzj99NPtsNO2p4me38ZCCy3UqVycqNXrnqWu5MOyTsMyzRCiPPLIIyuaR9J7GjmG4DG9QYo5YRkuba4aQdIvTT311DELfhsM1hi0Gfn7vP/973+Hrbbayp+K+1tssUVcqZFeYGXWvvvuG04++eR4iYEIKzpSAVMmdKZ1Sq9zzCCDdGUDBc9ZlfhHRO/Q9aA1MqOnE5lkkklcjh13sc+deOKJHU/+ebTWWmsFOsKyH5LdxCiTRldpwGBp6922guj5kUw88cSlRWFGsMkmm3S67om+08XkBA6BRx99dKgljkGjRF/rao0bbrghnHfeeUkJ//+QjopOxtaW//+VznsMbi655JIOF6oR/VFHHRU1BHYT/iRXXnmlHRbbZrS3SksXeRD14ttzq4n+hBNOKJbVoVU4/PDDg1fl33HHHVH1XoDgdmolem7x/QRtDszLpKeJHh+CMp+kauYM3omBzcUXXxxn8GXvaOdZZnvRRRfZYdO3/HbB2uKWlD0Aov373/9eqhXjPk/0DAYXX3zxLMmTNudwfNBBB4W//vWvXM4KxGwmnjKiZ8AIvtXk+++/DzvvvHP44IMPOiX1bbES/4joHXQetEaInqwgYCrw1VdfjaRPh2oNk6U5K6+8cieSZu06JGvCaBTCQCUMMbCczYLHkKYamVg+9W5bQfRWBtbk0/mOOOKIAUxwcDJh6VOqHssRPWo5OhPU5cxYV1xxxYJIqqlm7VmNED3P8uuNmS0yC2DWTX7M9G3WSqew0UYblS4jY6A3zTTTWHHiunIGgMxEwARzD+phTB/1ED0dBjMdv378nHPOiars4mF/7jSjvaGyBAPrqGiz1113XWz3vAPxCazd2/NbTfS33HJL8UzqixkrcQYs5gPmEDzlc1Ir0YMvAwqTsoGUXe9pop9qqqk6aHfwrzFnwVqI3mtJeCfU9DfeeGNATcysmrYMSZJnq4meQYQfIDPIeuyxxyKh8l60OxP6TmbVZeKJ3g9IMZ3Rv2CW4HeCiY427pcQo9lAe2FCf8C7E2sB8xW+Wn6AW43oCVxEXfBcNHjEYVhwwQU7aEHpa7bZZht7ZLH1nMXJMv4R0ReQdRypN0L0NBhm237kxQz/rLPOKp5iHVBxYvAOM0BTRWEvQj2c2ppw4qGjRHgOaslqwTti4jr+tYro+VHdfffdRUn44UAKZsd/4IEHOq1hTokeXwVm/v6d0x8c5WfWVkkaIXrfwfD8TTfdtLDp8Sw6OUjVyL6sk0FN59ctE5XPVgD4MpMP6jr8GJhteMnN6MHzlFNOKWIg0D4w8dx1113+1mK/Ge2NGZMtWaNzQS2etnvMWDYQ4OGUvVUBc7CRonVBeH/KRrlwoKNuTPgd0TGnUgvR8y5oWPwglbZgttE0T457mujTMtW7jh51thEokxfeJydTTjllxD23UimXvt5z6WAbcwkk54VZ7xJLLBFP0QboL8ramyd6bmASRvvBJu8FVT6DVr/SCm4wLSVtieeYjZ97sfP7QUYZ0cMV9957b2kwJ0wu9AMmOcdPT/S8cxn/iOgNxcFbD1ojRP/II49EO63LMu7SOeBkgaT20pSsKtkRaWw4wSC1li8mrvFfK4geZ7z111+/UwlYP7zIIovE87kOJCX6q6++OqoQ04wYRJm5pJZIcfUSPSSOz4EJszl+nKlst912cSTPeX5wfgZgaT3B0kE04oGdEj0/bAjVOh6eTeAXbI45aVZ7Y6BmtkWcB1FlpsKSNmY4Jq0kev+s1KaKBswGlWUz8JToIQ06aIQZFu0GG6jlw/laIjb2dqL3fULudwoO3SGemBlsr7POOp0ei+Mbzp82uGRywX058flx/cwzz+w0qM7dh78CmjYTBti5AbXvq8uI3vKotPVtF1MUWlEvnrMq8Y+I3qHmQauVSP09Bx54YKcRIdl722FqJ0TNiyobwQPWO/Z49Q/7qNEYOSOowm0GE0804Z//UaNGKouMV215nXfGK5u1eg1FjqBTos8td+KVMYVsueWW8e1rUd/XS/So0Pbee++Yf6XAKzhQels4HZHXPpABHuFmt6u1fcUHu3+e6JmtQOymimX/sMMOKw1dTDbNam++3ZcFfUnV3K0keswI5kh17bXXdhicoWZlgINgx2QwnUpK9On19Bis09lTmobj3k703sbPOzOgS2e9ufdu9rnLLrus0KRgovFaUv8sfHVmmmmmeKrSwMQTPTZwHGNrEeIi8BtD0Bgtv/zy2du8yagWokeLx4SFFQ0jjTRSkSeTJDvODUb877AS/4joC0i7PqPH/pdbFsWPwxw3Uoc8VPneQ9oVp+IuTnl4fTZTWkH0aDP8CNjKi4qNWROSc8jzRF+JYPlR8+NG6IhyM+l48c9/9RI9MfYtzn61Wbj/0eH5y5JIE2YZ3lubwYlXdVu6altP9Gla1I8MoFKzj0/XjPZGx4NN0gSPYpv92jm2zLC86aFVRJ+ax1Cl0qZM0oGpqfXtOtt6iJ5BMCaXMg9+n29vJ3pvErH3woyGPZollMwiwaPV4gfJkLQ3BfpnE+6aCRGS61csrSd6luhaX2TXy7b4e9j3JSoNEHz/Vono0RIxKUBTUE2YQDGI9eL7nEr8I6J3qHnQap1x+XvKAuZ4ok+9dL1XsCtK1d1WEL13FGvWjL7sR+l/CDknKU/0EFfZx2cwiXjP9DLSMUDrJXpsZLY8Kad5sHzZejVbaqIhqpottyFtWVvhWiWpRPTchzPPtttuW5pFM9pbPYMr//toFdF7swmDHdPwGAhoUfwMMGd+SYn+rbfeKpxm8aDGDo9DFH4TbGuV3k70vKe3fefeG+IH3zLyzd1T7znfjso0SOSJdhStFVJJw+eJ3pbWxZuq/CP0NwNHBI0abTon3h8nR/QM/OEYC+6UyyM9x+oHTJhePC5lfQr8I6J3qHnQuovoeY7ZVxlZHn/88a5E5bvYvivN3MrvLL/iG38loq/2URuvum810eOFi7OcCSPkSmtou4voU493TC4+BgIqP1R/9UpK9HQi9913X4eBAyYE7zzkn9GM9uZV8tW0KP431Sqi9/ZQ/65l+3yFDrLwkhJ9manI31PLfl8get4T0wdqZNaem9Np+v7p4Da93pVj34723HPP0s9We5NgpQmC7+vqWcWE2YfVPkhugmLvWI3o/YCBexg0YNbFmZFBJQNWBGdI8+8S0UdIuv7PN6buInrvRMRMIbeEoutvVlsO3sOWBsdMKSeMHO1aTq3ebKLPPcPK5Z3LKtnMLH29RN+o6h6vW7zvTVLVfSWnS7snt/VEz0wTNSIRuXAKYikVAvnSkfzzn//slEUz2htL61hiZVJGihACzlEmrSD61DnKnlVpy0CQAaEXEb1Ho/I+qw6Y1bKszlYLcUcO18o51X6V9mYR/somD+RGAK6llloqZlyr6h7/DgYptYif5KDJsKWb6b2Yh1iZguRm9N6npFLgIp9ORJ+i3OBxTxA9dhXzIC3zJm3wdeq+jaVfjESR1HPZZ+YDpeQacbOJnmfzDXYCYaRSq83M7iManDk8QpQ481USv1SGgQSj+dxMnNUQfhbNzCJdxuU7q5y9rVI57Jonem9KwAOeD6SYsx+zGdpVqt1oVnvzvxWcFXMfR2FZFoNHk1YQvZ/BMcAhHkFOGGjZMjGup7NCEX0OternfF9A6twSsOq5VE/htTa0PfsNp3fiO8FgHslpbiy9n9HXQ/TeBEd74zeW/s55hjf5pX1kOugv69vwccHx0JyyRfRWe13c+s6ru2b0aWdY5jnZxVer6XbvKVrJvuUbMbNJW99vD2kF0UOS2JdT8TbnWjQi/kfGDxXbv18Dm+afmgZy63e5x9sxyTfnFOjLWgnftAz+uIzoSZOuNYZ8bcWA5dGs9oaDpTkQ4XRon/6157DFOxkvZZNWED1LC/myHFJpcMp170Px8MMPdwh6IqIHocbE95u54FeN5drxLr9yiQE/5JgKGiT/rYNKIcMbJfr0GWWDDj+oT4k+1YiVmfG8mYB3FdGnNd7gsW+w3UX0FNXbTWnEeI56r2H/OqjMUAuRptmSkkC6FJDn8QEWlivZKDO3zK8VRO/V1PbehPb0nwnNrTO1tH7rPXjLlv/59H79O/XDgMiP4vH6ZgDACBxh+REDtlRSfIkyRrpUQ8CsHNU/a2ZTB6dKRM/zfEAUjnMDk2a0NwZ3zKYRBjaoTL33NRHT6JytnZCu2UTPoM3Hsq8Wmc0vs0vJQkRPDXUWVvbgYZ+2Q0vJB118W89psixtV7ZEqOP3bZJbzZMuBUR7VfYJ4EaJnufzbYcFFlggFoXfLs/1Zjp/nUQp0XPOR3FEW+GX5nKd9shqItq4iYjekOjitqeInuhddL7WKdJx0smzDhQnDUIxYn+lsUMCZTPGLr5+vN3bejnBEjDUoRAbjY8/Kye2c1RXdJpeWkH05M8PhjpCZY1D2N/+9reiLJTPiMeXJbefOsKgleA9wRVBleeXxqWdDLNxZgusfMDJDpugkTx5VOpg/DpfnkW5+eIVdU24Y9SOdJ6M+usJgUteCB0DHQdf30IoD5EWffS2ZrQ33pdZi4W5pS0QOASvf9ortkzfSVGWZhN9+iW5dFkdz/Til9lx3ke1axbRo2GgHk0I+WzOa9RFGluBAV364SK7t6tbBsL24RbLi9Us5vxLm6DteYGw+HKaif2WGWjzmyBUNf47DORop7R/6w8qeaFbfl3Z+gE3+VBWgjXRBvkUMO3OpGywbde7QvTEzMBMZu9NvTIYAk9U+xbUzJ6VI3qvieJ++h8GU7QPzKfE77D8LR8RvSHRxW1PET3FZpZOAI60c8y9Eg0jpxrOpa33HI0UL3Yjrkr3l3nZWufAvfygcrOBepbX8b5I2vDjycH/IBlUxzk7saXxWwZLkOHII4/sTxf7Oa91VOD8+KrJ5ZdfHvgrE9TdRK6zzrYsHecbIXruS4mcWQ0zcK85aEZ7gziZyZfVC8/z7bnZRO8HTbWYQlK1qw+80iyi95/lpS6qSaWlYtXurXbdL5etltaup+Yv/1u2NLktdU2ArVaFwOWZTHb47ZhTXq4cnIMsUXungyqfvitETz4MmBiklbV9NJ32MaEc0RPk6oILLii9n2fg7MckSl73oNFE8V8TqlV17++ptI7RAuak6+h98SFZot2ZzdFfYx/CQ6VPbHiIqlXCiBU7vEUTS59D6E++IlXm+ORt0ThjPZgJx1qN6H2gDp7HoAL7nycOyvXtt9+GXXfdte7gMwxk+KFiiiCimv/BMtDxAWHs/ZmlskY9NwjiR8l6VT8bsvtyW5wIIT6bEfs01PPrr78eB0lpUB3vTFfJJp06SXlSs2c1o72BH0F40s6XGSD48ulem9Gi/anU+Vq5at16mzuzO55XTahb+zCKj0XhlwySR9lKgmr510v0kKM3eVTLv57rfhVNrfelRM+adNp92aezyZfyM6holWbClx2nUxzuyvpIZvg4FftBrb/f9j3RVxuc2z3pFg0CEwBI237HfAwH/w8CCVkgrzJtI4NL2qzXANkzGJwziOb3Y5OCdMkuaWvlH62jN2TbaAuZoXIjMAmxtRnVEYXrtddeq+g41uxX4EeFKhk1HdHQWK6FirnMf6DZz8/lR4eMfQx1Oz8mtt0tLCliIMLMmU4OW3uZLbBa2fiR82VC8mQAQX5oJpodI6FSOZrR3sCCFQq0V9TBEIak7yBAX0A7JcALAyUIjTpGlZ+LBtrqN0crx0SEgSaDbH4z9I/VCL5V5QITJiS2ygWNqwWuAqdKy6bp0+hnyYOJwjPPPFP6/flGyy+ibxQ53ScEhIAQEAJCIIMAmlkmA0gtnwPOZNHUUyL6psKpzISAEBACQqCvI8BHcIhkmjPVodInCqaZA3tyybTVg4jekNBWCAgBISAEhEANCJivBx+2gezfeeedaDZADU88CyP5sq8l1vCIpiYR0TcVTmUmBISAEBACfR0BI/pK74mfDUt5U4faSve06pqIvlXIKl8hIASEgBDokwiwXJWVQ8T9t9m7vSjL6Yhfz6qHnnIOtLLYVkRvSGgrBISAEBACQqBOBFimOv7448cvzrEyibge7SYi+narEZVHCAgBISAEhEATERDRNxFMZSUEhIAQEAJCoN0QENG3W42oPEJACAgBISAEmoiAiL6JYCorISAEhIAQEALthoCIvt1qROURAkJACAgBIdBEBET0TQRTWQkBISAEhIAQaDcERPTtViMqjxAQAkJACAiBJiIgom8imMpKCAgBISAEhEC7ISCib7caUXmEgBAQAkJACDQRARF9E8FUVkJACAgBISAE2g0BEX271YjKIwSEgBAQAkKgiQiI6JsIprISAkJACAgBIdBuCIjo261GVB4hIASEgBAQAk1EQETfRDCVVd9EYIYZZgh8oQp58cUXw9dff903X1RvVRWBaaedNowzzjgx3aOPPlrTl8qGGGKIMPXUU4chhxwyfP/992HgwIFVn9OXEkwyySRhsskmi6/Eu7///vsNvd4ss8wSpppqquy9fDXupZdeyl7TyRBE9INbAd8UXmCBBcLss88eG+SXX34ZO/THHnss0ID6syyzzDIRH8PgxhtvDD/88IMdZrdzzTVX7NhyF7/77rvw2muvhXfeeafit5qHHnrosNpqq8XOMZdPeu6nn34K119/fXq6KccXXnhhGHfccWNeJ510Urj77rubkq8y6X0I3HzzzYG2iWy88cbh008/rfoS/B4OPvjgmI5B4jrrrFP1nr6UYK+99goLLbRQfCUGR4cffnhDr8f33WecccbsvZA8z5HkEej3RL/44ouHnXfeOQwYMCCL0HPPPRf222+/7LW+fnL44YcP1113XQdsLrnkknDVVVdVfPVzzz03TDjhhBXTcPHDDz8MRx99dHj77bc7pZ1ooonCOeec0+l82YlBgwaF5ZZbruxyl86L6LsEX5+62RP9hhtuGD7//POq79cORH/AAQeEUUcdNWog9t5775o0EVVfrMYEzSL6Qw89NMwxxxzZp4ros7AUJ/s10TNj3GyzzQowynYgop122qniDLTs3t58fuWVVw5bbrllh1dA9bbNNtt0OJce1Er0dt/555/faTYuojd0tG0nBHor0d96662FdgyNQnean5pF9Jg+GKyY0DeZpkBEb6jkt/2W6EcYYYRw7bXXFrPVX3/9NVx00UXh/vvvD9NPP33YdNNNO8xKL7300nDllVfmUeyjZ0855ZRONjFmziussELFQY8nemY89957b0QIlScEDr4jjzxygRp5brHFFuGjjz4qzg0zzDCxDoYaaqji3GKLLRaGG264ePzFF1+EJ598srj2448/BgYMrRDN6FuBau/MsxGiH2+88aKanzfGbHXLLbd0+8v3BaJPQdt2220LLZ6IPkWn43G/JfrNN988rLrqqhENiGaPPfYIr776aoEOo0fIfbTRRovnIJYNNtiguN7Xd3AgokNii0CkqPKR4447Lg6I4kHmnyf6sh8g5pIllliiuPuVV16JdVCcyOxgNhhrrLHilWeeeSagjuwOEdF3B8q94xmNEH07vJmIvh1qoefK0G+JHjvzKKOMEpF/8803o50+rQZmkLvttltxeuutt+7kMYoqyez7X331VZHW70CW9iwI8+eff/aXi32I1FRTpDP1Gp6+c889dxh//PEDAw5mBTi1tFLmm2++wjcBT+Fnn322UJO9/PLLYc899yx9fC1Ez81XXHFFMZD673//G9Zbb73SPLnQTkRPnVIn88wzT6DeX3jhhejA+ccff1R8By5OMMEEYdZZZ41bBi74KjAgol7RLNUqrATAZjnppJPGVQGU45NPPol11ahnc63PRuOCEyuCIyTPZnAMJvyhyaGNvvvuuzVlSbu3gSTtnvaPzD///NEBa/TRR484Pf300+GNN96omCeD8znnnDPeR9lYKYGvTdnvLs2MumWlBXU77LDDxvcgD+q2VqLnPhsk+/x///338Msvv/hTNe2jgcTrnHKB+wcffBDot/hdpsJzbWWAXeM3Sf0gu+yyS6wvu8aWyQ5tp0xGGmmk8Ne//jVq5HBM/fbbbwP9ALjgYFtJcqp73ge1+0wzzRTbyCOPPFLx+bn8mzGjR8PIb4hVER9//HFsJ6+//npFjWWuLF05Rx9gmkucO2ln/L6WWmqp6BxOW/rXv/4VHnjggao+IWXv0y+JnobKLM2EfdT4qaBqvummmwoix9sar2svt99+e3F92WWX9ZeKfd/QH3rooXDUUUcV1/wORGdkhy0cv4AzzjgjEoJPxz4dIQ2dxtkK8Y4vdK50cIcddlh8FGS00korlT62VqLfd999Y0dORjTu5ZdfvjRPLrQL0UPSCy+8cFHvVmh+pDvuuGMxQLPztqUdQEB0cmUCiZGuEhkwaCQNHb8NMtP8aB9HHnlkQPPRCllyySVj+yRvSJ3VGGjJ0vJAtLvuumt47733KhaD3xWdLcLqiQcffDDgZW3k72/+97//Hbbaait/Ku5PPvnk4YgjjigGy2mCO++8M5x66qnp6Q7H8847b9hnn30KUrSLtE8853HMNa/7Mmc8Bie07ZzU63VPx83vLiVuyxt877jjjnDeeefZqbhCxPdvxYUKOxB9zpkVP6ZVVlmlGNTlsmCQx4TIm958Ot//MfhjwMEgKhV+PzvssEP45ptv0kvZ464QPU7YPMsI1j+AuqY9msnRX2vFPr8dyBzB/2nmmWeOvlHpQJE6okwnnnhip2JUe59+SfTMOA488MACrErOKZdddlnRyBm94rHqpRaiP+igg+JomPvqIXp+xMzmy4QfEDPBVsgNN9xQ2MMZmFDu2267rZilYOpA3Z6TWol+9913D4suumiRRdlAyRK0A9FDMhNPPLEVqdOWWdEmm2zS6TwnPH7ZBH+eRIOCaYNZWyr8+C+++OJiXX963R9fc8010e/En2vWvid6OqCU4P1z6DgxszCrLhNP9KiZ6bhyJM/9OVJaa621AsRbqRzci/aEQQJlSqWac276nmVET5umbeekHqJfe+21o7mw2jsxKMRx1iSdyNj5StscpqT3v+VK94Pn8ccfH2edaTpP9CmGaVr6vL///e81ze4bJXo/iUmf749zEzt/vVn7nughcSZ4ZXXOYIoBtZda3qdfEj2zUT8jqEQwZ511ViDgA8IsO/U4bxXR+x8EMybUunRSLFubbbbZojNbq4ieoBQ44iGUw5zv0C5MNtlk8XylAYvvHMps9GTC8jlmLAhqVWYOlaQdiN7K9/jjj0eV7ogjjhggGVNjcx3VaE69bET/2WefhSeeeCIuK2SmO/bYY4cFF1ywg5Yg19bIm7gG22+/PbtRUN3SUaDZwSQwzTTTRJKks+8uoreyoMK9+uqr4+8ENS8aGuuwmPWtu+66lrTT1hO9b/uo/ln1wv1TTDFFbPsMdvzsk/OnnXZakScaJwaqzz//fDQNLb300tFUYgm45mfAnGdGhSbBZlHkwdJS/HZYu7366qt3mv2VET0aF/oUe3fqBDyQWoke7cTpp58e7+EfmKDl4J2oaxxayZNtSvSofRkoeYEUrTw4rZppxNKkzq123n7LpKfNv/XWW7E+wIuZJwMMm43+9ttv8TgdRHmit3wZMIM3bYay+hl+Lf465NMI0XutKXkQM4V65r3AnHrmt2OCpoK+t5Xiid7aPhjyXOK4oAFB20U7+s9//tOB6Gt9n35J9Hh4G6kArO800go94YQTilk1jWL99dfvkKRVRG8PwdMfp8BUmH1gs6FMzRbvKOdHkDgjWrAPfqBrrrlm9tHWOXCxjOhT/wd+aIxkK0m7ED2k5IPmQA50FrYigHo59thjO70Kg0tUbwQLygk2SzpFE4gRgvPig4ZAQmUzxymnnDKSQ602cv+MWvb9jJ70BFHaeHAAGW+v9evHSYPq26+U4JyJJ3rOQRrMVDAbeWGWT2fsfxOQNoMcBCJlyWwa1InZMcSM8JsHW9KaMItkQIvQyeKP4zUqDPYZ6NpAgHS8b6sC5lxwwQUBb32EQTDmj1xdYl/eaKONoho6Ji7516gzHkROH8PAPicMcPldGi5nnnlm4FleUqKn/fvBKmlT52jqisFHJamX6DGZMRA1XwX8ajDTpOL7/O5wwvZET1nwV2KykPpMMClikmf41vM+/ZLoUb8zg0Kq2Zu9WgS1knnqx5sH/2sl0Zc1RHt2q7beSQ7Pe7QaCE4j/KhNUFGnjZFrnughcDpxBNsm2gLs28wGvFTz5CdtOxB9brBH2TBlLLLIIuzGWWAZAccEFf55kwkRxFKnS2auzGCRSkRf4RFNuZQSfdnyU09YzMyxi+YkJfocYeTuw08BXwSTnMOsXaOMFsr47LPPjn4ndo2BmvlOPPXUUwFzWyqYH/zMs1VEz2/D+/GgXbvrrrvS4tR13CjR1/IQfrtoFpCcpi8lerSiAwdrR70wUMAPyEgYvwOvpfFpbb9eosd/Bgc3hL58jTXWyAYOwvGQAYFpQJjQ+AGsPb9ZW0/0DELpV2sZQNbzPv2S6HFuIdwtkiNvX4GHHHJIdKDiXE693EqiZwZdbVTry9qMfUbo+CWYpB0nTouoqxF+mHSYqXiiT6/ljlE/77///rlLHc61A9ETZ4GOLRU/Y/RakDSdHTPoYZaIxzwdiwkaIzvOkR044TCG0ClASOms1/Jq5dYTPeVAQ5ZzIGTmbFqwSup7T/T4KNAJ1yLMZjGdIHi0e3W3ddRcYx+zB5oOBDU0g3gT/zsu8z/BX4bZnkmriN5rztBMoMHoqjSD6CFjtAyY72yZK+VCO2eOlLnJiSd6HO34reSEwY1NAKqt7OH+eokeR0yrf8xnPi6KtRXbQrbWz6GdQ0vXKvFEn/MDK3tuPe/TL4keuwuNE0FFuOKKK5ZhGb148bJGch2Q7yDKbP10xmajy4147eHe3pIbVFi6Vm59x5krgx/4MOqks0ulVqJHm8LIGQ1CLdIORE8ZciGAiQmAyQOp5JCHfwjmD1tuWem9c6tBUNV6guJ+Bqssv6GTYJlSLqRwpec0cs0TfSWtGIMSG8SltmT/XE/0ZctdfXrbxxyAiaBewd8FEx7CwAp/BhMGLbT9VJhtmtqUa7T9WmZe3oRRi43eaw5yau60XLUcd4XoURcz4K/khGplwK7slyRz3hM9M/nUz8nuRZ3PYAypZbBcL9H7JdX2zFq29PF+AFnLPfWk8URfphnL5VfP+/RLovdkxmykko3ed0A5YmsV0eOAZzbFXCW36pwnadZiM0jxgsnDe5Uz20jtoT4PnHggIRPsT+SLExpqaWZhtUo7ED3twdvnreye6HN2PWZDaD9q+QaA5Yl3PQOhVLwPRXqNY4gfc0uunLn0jZzzRF9plsYMyi9pKxsM+9+ZLa2rpVy+rdWS3tJ4omctN99cQKr1B/733iqip50YqaaaByt/vdtGiX677baLjoW1Pg8nVOzLXjzRl/nskN5rMnKTKp8n+/USvTnDpvlUO+5OomcQT53XIvW8T78k+tRzuazzAWzfkeRG1/6HX5ZPIzP6SiPfWhpBI2nSuAG15JGLU+8xq/TDriV/n6Y3Ez2OhpCjCYNG7JA4WOFFjWYJwdnO1KJlRE86bNOoxFGZ2rpuznvJ1Y2/3pV9T/QETzH1eZpnI0Sf84pP87VjT4poAljiVYvga2EDVK+Sbwei96tbWJ2BFq2r0gjRE/PBP5tB+z333BMdbJlxM6BEGPibz1NXiN5rNCu1KcOiXqL3GDCAvu+++yyrils83euZkFTMLHPRz+h5p2oxJyyLet6nXxI9qij/qURGrTmPVgD1NumcLdmPqsqI3ntx1qq67wmip/xgUY+AW3qPiL5zuGSWEtm6cDzP7bOlKdY+XSWi9/fhVwHxskzJvM+5Xkml7u9vZN8TfaXn+AiLldL5GT0Y1PrdAq/mbvQ3w/IwOlsTVrSky8+4xoAKvxSTVs3ovR8GnT6df1fFk0KluCH+OX6FB5o4NIw5wkMbglYEqUb0ZcGOuNcPhr3GhWs5qZfosclb5NF6BpO5ZzfznCf6nKNi2bPqeZ9+SfTYR72d1XuWe1AJpentx4wC6Xy9+B8QI9ucF/pFgz+WY5Gt2pno/YCE0Toe8zlhxmmEwlIkfBz82lkRfUeiR23PgNCkzIsXGzBt0RyCaiV6y5dtGvQlt0TPp2903xO9PTdHjpCDOV/V6oxXD9F7dW8t9u+y9/WaOVbl4O+QCuvpIT+TVhG9d2Dkd5iu9LHn17P1/RR1gmmwmngbMJoTP8jx93pNWzWir7Qs1w8sck59/pns+yV5lVZ02H3+I13N0pRY3l3ZNkr09bxPvyR6KsWTUZnzlP/BodLDE9jUfVaxfsZvEeTsGltGkAwWrPNuZ6L3nUHZ4Id3YtDC4MWE5U0PP/ywHXbAVqr7/wVj8TNGAsn4gZEBh8OTdwxthOjJy5NWWfAee2aj25Toc4Ng8vaD3EqxEhqd0afkS8TLRlYheFIjgh+hblPxq3W41iqiZ6maX9nRDBOM1xQRnpfAO9UEB0VbAVK2/JXY+z5mRDWi55ksCyMQjBcGw/Q5bBF+L/TRlQQNlvkDVBpEWh7e34DfHwPutD+3tN25bZTo63mffkv0qZo6DUyTBsgoGzH6dcI51SEkiD3VpF2JPrXHpcvqrPy29QMcOg0f29sPokT0/0OMTsziauc8a+kwUYFaR8ddOaLHUxznxjJHuzS8M7PpWmOHW93Wsk2JnpknMywfwCkNAFSJhBslesrq7fTMGPHezmnWSEu5CYyTBmzxmgcG9ZCRX71AVDI0XjZgJ69WET15+3fCf4PfF0vOUqG+UcVXCzbl+6mcCTLNl2PvK5BbCcEkBqdPU4dzTy1Ej1o+DUPs1fZlkyry90IAGaJrmlRzZMNEQ79lv0PeiY9z5ZaFkhZccZ71Zl57VjO3jRJ9Pe/Tb4meDhWAvSMTP2xUdqxtZkmdNQgqtSyql1cdkg6yx2GF4BvEvDYVN9eQdiV6b+vMLav7X+n//79f1pSmbwbRE9jEe2vzZDoU62jp/HwQC0bmaQzo/y9t1/ZY5mZhMSGkHMlW87r3nSYdGVHXyAd1M4MsnJns3ay0OaK3stA5MfOkY8VPAiKi08f5zfLJrRKxvLu6TYme/CB7tAnYlVkGuMgiixRlya1E8GXoCtHT4UOM9t7gy4oOAgqBAQGGCNSEbw7hYbmerrTBbEJ/YL95bNEEqeFduJ8wun4QRtlzRE8YVQY4XigfH7pB+K3woSwvOHulbSr3TtQ3g2rWgFPfrDnnvSotW7TneCLlHAMh+juLDoj/BBh68apxzjOIY/kmAw5s8jg1+/6TNLUQPenMGZXnEoTIbPxcKwtYxLVU/ADGflfeLMFEy/cTqWmLNktsDEifusG5FefM6aabLranau02LU8jx40SPc+q9X36LdEDEj9KPiLAj7+S5NYzW/q0g7DztqXxMYLlh4u0K9F71V4tsabT2ZpXxzWD6MHLj9YNz7JtrvMuS1vveSNX7muU6Bko0CkZGeXKQKdDp1TJ696XJZeHnUM1SRS6MidTS9fo1hM92Fd6L0gTu3fZR5AoQ1eInvuZpTNLTMmYa6mUtRXeiXZc9i5g6vPPET2zQAb/9UhZLI9a36kWooeQUcUzC8xJDhPe9fLLL+8wY0/v5T5MMgw8kGpEz7vaYCrNi2O0T9SjDUByafw5SBmzgq8Xf33TTTft9IVPv9zPp83ttzvRU+Za3qdfEz0g0QHTUCw0JudMGOERnSwdbdt12zJgIA/zqrbz3M95Znv1BsypZM+0/Ju1TZ0OCQ7BzKyS8MPCpm+dIuktqIT/WE2jqvt6iR4ysVjllcrdyDU/cMFhiHXeqVSb0ZMe9TwqWPBOhdkSJMNnVm0NNTimsz9iQDC79OrSNC9magxga12mk95fy7EnemZQ2OjxGLb2YHnQcTObrPY5ZU/0kAt/9Qq/YdS3RG7LCaTETJYoZ5hPcsIsGW1VSoiQKXVH/dgsNue9ns6wcs9Iz5URPel4J1TH9mGt9F4GhniQe+fiNI0dU278jtBmEPXN11WO6LmPFR1o+yzyneXFloEpM2a0N+Zbkvu9+7gPaFrQvmFf988nP9oracG6HqEPZ6UCava03liC6s1Jli/1TJz7ssBVDOr4HYEtk7NWip/R5wYmtTy72vv0e6I3EPkRoELFns4SEFRUtY4qyQPioyNnhEkD/sc//lFqJ7Rnats/EaCN4HCFUyMdI9+Mr7dzwzSEeYnOjXxwRsJsxIyKWUirJSV6bNwIznFEw0M1S6fu1aitLpPlz28RYkIdzIeGIEOCNBG1LbcywO7zWwaaqNu5n98y2Pak2DvRodNXUd/UddkHkppdVkyQ9I0MosDiscceq6t/TMuDJhRTE+/DpAYv+J5wjGNgQDvBPMNvinbL+4EtZN/bpOx9RPS9rSZVXiHQBgiUEX0bFE1FEAJCIEFARJ8AokMhIASqIyCir46RUgiBdkFARN8uNaFyCIFehICIvhdVlora7xEQ0ff7JiAAhED9CIjo68dMdwiBnkJARN9TyOu5QqAXI+AD87Cum9UAEiEgBNoTARF9e9aLSiUEhIAQEAJCoCkIiOibAqMyEQJCQAgIASHQngiI6NuzXlQqISAEhIAQEAJNQUBE3xQYlYkQEAJCQAgIgfZEQETfnvWiUgkBISAEhIAQaAoCIvqmwKhMhIAQEAJCQAi0JwIi+vasF5VKCAgBISAEhEBTEBDRNwVGZSIEhIAQEAJCoD0RENG3Z72oVEJACAgBISAEmoLA/wEAAP//Kqm4FQAAQABJREFU7J0FmCy30bVlZmZmZmaIY2Zmppjt2I5j5piZmZnZjpkhjpmZYmZm+7+vvlvz12q7B3v2zk6fep7dZrV0pNYpVZU0g0088cR/BokQEAJCQAgIASHQlQgMJqLvynpVoYSAEBACQkAIRARE9GoIQkAICAEhIAS6GAERfRdXroomBISAEBACQkBErzYgBISAEBACQqCLERDRd3HlqmhCQAgIASEgBET0/aQNDDPMMGGeeeaJuf3ggw/C66+/3k9yrmwKgdYQGGecccI000wTE3nxxRfDZ5991lqCg/jpbivPIIZTr68DARF9HSB1wi0bbbRRWGuttWJWXnnllfD3v/+9E7KlPAiBtiNw4oknhimmmCK+56KLLgqXXHJJ29/Zzhf0RXlWW221MOyww8Zi3HbbbeHzzz/PLNIYY4wRll566Xjt22+/DTfccEPcX2ONNcLQQw+d+Uw9Jx955JH4/BxzzFHP7Zn3fPfdd+H666+P14YbbriwyiqrhMEGG6zXvX/++Wd45513wvPPPx++/vrrXtd1IgQRfT9pBRtvvHFYc801Y25F9P2k0tqcTZS/mWeeOb7lzDPPDC+//HKb3zhokj/ppJPC5JNPHl9+4YUXhksvvXTQZKSgt/ZFeW655ZZKbg866KAA8WbJ5ptvHlZdddV46Zdffgkrr7xyGHzwwcNNN92UdXvd5y6//PIw5ZRThlaI/tdffw0rrbRSfOcMM8wQjjzyyJrv/+2338KNN94Yzj777PDHH3/UvL8sN4jo+0lNi+j7SUX1YTZPOeWUMOmkk8Y3nnvuueHKK6/sw7f33av6ghj7rjQh9EV56iX6zTbbLDD6R7qB6K0ev/nmm7DBBhsElAWJRvT9pg2I6PtNVfVZRkX0fQZ1oS/qdKKnsFtssUUgLigV4oQw9yOMnm+//fb0lnh85513hhFGGCHMN998mdeXXXbZynksUW+++Wbl2HYww2PBQdIR/dVXX10h8dFHHz0qvFNNNVUP0/4TTzwR9tlnH0uu1FuN6PtJ9Yvo+0lF9WE2RfR9CHaBr+oPRJ9X3J122iksueSS8fIPP/wQVl999bxbq56/+eabK6R8+umnV3zxeQ+lRI8V4scff+xx+4QTThitJT62YN111w1fffVVj/vKeCCiH1Dr008/fYzqHX/88cPwww8ftcunn346vPHGGzXbBBG0FiDy0UcfxftJY5FFFgkzzjhjDBJ5/PHHw1tvvVUzLW4YddRRw/zzzx/zxPvvueee2FD7iuhHGWWUSnnyPhB8eCOPPHIsDx/bzz//nFs2Prp55503atzjjjtu/Dg/+eST8Oqrr4annnoq97n0Au+cdtppw2yzzRaoJ2YdoLG/++676a25x4xE8BlOMskkcVRC+T7++OOG08l9QQMXJptssjDTTDOFAUtQhxFHHDF8+umn4cMPPwwPPfRQbkDRmGOOGYYccsjKWw4++OCIBSfwS1577bWVa7ZDur///rsd9tgOMcQQYe655454jDfeeIGgJqLan3nmmYhLj5uTA54da6yx4lnMoxbsxfew2GKLhQkmmCCm9dhjj1WuJUn0OgQL2j4d9pNPPhkefPDBaE6ulxhbKY/PjMeZtoqvl3a81FJLxXbMSJfgL77NajMAWi2Pz1Oj+62Y7qu9q5OJnnzT9nbddddKEY444ohw7733Vo6L3KFPGnvssWOStBHaCkL7IR/U/08//RT7/jvuuCO25XhDxr9292+lJXoIdd99940BI77z9HVAx0cUKhpnnnjNlMARRll0cqnQ4REUkxcgQid19NFHh6mnnjp9NCocKB7mS2tnMJ4vjzev+UztvvvuYeGFF46n7r///nDYYYf5y5X9bbfdNkb0UrYsgYAwwZ133nlZlyvntttuu7DMMstUFJDKhQE7RAqTn2qKFEoJ98wyyyyZaZAeCsuhhx4a/vvf//rkC9/HvLjbbrtlthF72RdffBG23HLLwIjJC0SeZU7196T7++23X0DR9DL77LOHrbbaKubBlFR/nX3Im04SxSNLUJhozwj+UDrX448/PhAd7YVviMCoa665xp/usY9ywLOmPPqLRF2jEFULxiuiPP6dHudtttkmBjxSH3TGXigbJupjjz3Wnw6tlqdHYk0elJXoqSMfSEh/dvLJJzeJYvXHqGdiYxDcGCuuuGLYY489woILLtirn6HfP+6442J7SVPti/6ttETPKP6oo45KMc88xn/EdLaswA5PjIwu0eLyBK0u7RS4l87xrLPOCqONNlreo3G0ZZ3yoCb6/fffP44EyWwe0XtlILdQAy6ALQ09S1DGIAAbOWbdwzk6XOqSEVYqfPjnn39+xa+YXvfHV1xxRU2lw9/f6D7WkgsuuCAMNdRQNR+FiFNrhSegmgkMvAFlNlVeUMCWW265upKAMBhRp+KJnlELYtO50ns5zlI4OI+VBoUiT9nmHurX2n5W1H0R5eE9Jh5nvldGsfZ+u8e2rGlB5LpJEeWxtFrZlpXowcz3yXn9UyvY2rMp0TMbhADAPPn3v/8d+zO73pf9W+mJnk4EEzlmYMzBmHInmmiiqJ3ZIh1UDCMSyDgV36i4huaGRvnCCy9E0mfuu3ViaH1MX0lH9d4cRho8a+kuv/zyYbrppuN0RTqd6BmZXXbZZZX80hlefPHF4bXXXoujNqwWWATAl5F4HtGjAXsLB0E7t956azRx4xYBWxvhooQx95fIYS9YArbffvvKKeqZjhw3Cy4A8rD44ovHUVi7iR5TO6NPhHZHZ3zXXXcF5gszYmXk+pe//CW6j7KIHsww85ust956FeXw0Ucf7UXotDdGnGl7M2LEooL7hPZG3WBBwOLANE4LuOJdO+ywQ68FmjzRW36wrjACf++996KLZIkllqgQJOf+9re/2a2V7TnnnBNw6ZigDGOyp27IR6r8ViP6Vspj72frid6UDDDErQFOWKhol7Qd3C2e6Isoj89Ls/ue6GkDeRYvTMy2RoFF3Vd7p++rOs1HT75RtI455phKEbLaS+Viizue6EnK2gqKL3P64RXaL30V7Tkl+r7s30pL9HSYGw+Ym05DyFtkATPMQgstFJvD999/H4kkbRtGyFbRdGb/+9//Krcxwj/ttNMqxwcccEDAjG/C6A4lwszbXOMeL34EzflOJ3pcDEzbQTCJQ8Ap2XCNES4dZmpa5hqECDGanHHGGeG6666zw7glFoIRMlskS3tntMiHhkBo//jHP+J++o/Ojg81K/o3vbfZY5QdIy7M2bgtsgQ/Na6a1HSf3ttsMB4uDCxaKDZ5/nsWpiGaGSEve+65Z4/Xp0RPvAPfk1e0CIRaf/3143Ngm1oR0gArRs8QvQl+cUyjhhnnsxbMKaI89k62nug5/vLLL8POO+/cK26BWAJiRoiPQIoqT0ysxX+e6OtNqr8TPdY7+gNrt5R70003jUp9vRg0cl9K9Dz77LPPxkj/1Po711xzxT6QgQbS1/1baYk+ol3jH0Rki3NkdVQ87omekcghhxzSK1UaHwEaSNrB49e3kQ7vYJTKCM8LSgkLUJj5sNOJng7fFvdBu7UFOXyZau1jPUELRhjJ09FmyQILLBD22muveClrhOEDuaoRfVbaRZ9jtGtm+7QdNPOuZom+nnehnG2yySbxVgLtUpNkSvTEaaBoeaHjhQSt3a6zzjo9lGqesQV/8POvvfba/vG4nwZXZRF9r4cyTtQqj3/EEz3fJDhYoJW/L93vy/Kk706Pu53ocaVaEDCuPRRX+lLvPkJBw+rVLkmJnn7b+r1a7+zr/k1EP7BGGBUSCY3Z3k/PgISto8Ls7kcsPOqJPs8PiSkJkxLCB+h9nt4Ulvr74gMD/zGiZQSBdDrRL7roojHgbGDW48gRZSdrVG/3pFtPiihQRGGbWH3Y1pv+V1hhhR6jVObR2lxeOm2sI1kWBEu7nVs/osfMvcsuu/Sw/jT67qKInhEQbR/FyjDFEmXBmFnWLE/0eUow5cFaZZ0vQYiYNE0YrdNZIul3YfewRVkwi1c9RN9Mefz7PNEzAwHLXj3SrvLU8+70Hk/0KLh5igoWNQse7k8j+rS86TFtMrWupve0epwSPZZbW0K4Vtp93b+VmugJgsPEjC/RRlrVKigdkXCvJ3pGPTbNyKfjTe9pQN7hhx8efbPcn2UitXSIcKZzRTqd6FGasEBY50yeMRGjyDz33HORaL37guteeM7Mof58PftEf+NLNfGEZOewMjA9ik4cJaKvfiBoxx13jFO0LB9sGcniy0ORYRpQVvvx9/v9VogevzixC7POOmuF3H3afj/LKuNxzbpuzxOrYdH0KF1muuQ6rguL0j/11FNz69ynkUf0rZbH8svWE30jPt4iy+Pz08y+J/pqS+BmrYxX7X1+YJJlQav2rL/m+81m5tH7tNJ9vmeCc9Ng1vS+Vo9Tot9www2rTre09w2K/q20RE+wEeYTC+aySqi2xd/I1CcvvsHaCMhfZ98TfepH9iac9JpPx39gnU705BuTKyNWGyH6srAP8ROIRkBKKmlATXq92nFK9NyLmQ9lLk8gKrTxvFW+8p5r9DymbDo1G0FlPQ/R014g/1rSLNFj4dh7771z6yZ9bxaRe6LPM7uTjifplHD8t0M8xsMPP5y+Oh77ALcsoi+iPP7FnujTPPv70v2iypOm28xxtxM963CYhRBLBMG1b7/9dhwsse0L8URfzaqV5mVQ9G+lJXrfeVARkCcdPdHBfhEMfKlGVu0gen6MxDr+biJ6MGUxCYJhCFgaaaSRONVLCF5hKp4Xopn9NERGnuaP8/el+0SZ26JF6TUCtqg/TJV51psi/Obpe7OOCUrDn4ipHPJPhU5j6623rjkiaYbocUthTrf30lliXcGyQRApLgUEn6ctPNIuomd2iuWjGqF6k3hK9EWVx9eBJ3pmKORFrPtn2C+iPGmazR53O9FnrYzXLFbNPueJ3ubR15PWoOjfSkn0BMbhMzbhZyOZtpUKQXBEJpu0g+gJ3sN8imSRnr3bT81q54jed1Z5Fgofc1BNObG8s8VywmqBjKwhEVOeuJZ2pnT+5MMkvW7nm93iw2UZT6bVWcAfaREpCwH3pTCtjrz89a9/jWuD27sZ0ftpgXbeb5shegIjbToYJM+CMFkmTn4SlPXOkXYRPT/Cw3roSDXzLW4gUxRToi+qPDETA/95ogefekeIRZTH56OVfRF9K+jV92yzRD8o+rdSEj2/v8zcYKSanwmToP9RhHYQPZ05c70RRqOMgLPEm/jbSfQ+8IloY9YVSIWV7Gzpx3qJ3qfBXG1M9kb2pOcVKu5lKp0FReJvu/vuu30She37qYAkOijXxvZR21nkmhbaE329vmRGzhbrUU2xJHBu0QFBlUhWXoow3fs2jTUty41Dp0iAk438U6IvqjyxoAP/NUv0RZTH56OVfRF9K+jV92yzRE/qfd2/lZLoiczGNIowh54guyxhlEEUvkk7iN4rHbwnK+DPT/PjnnYSvR+VZE2ZIi+XXHJJhaSbIXrK4DtFfJvpMpXepUHEMFP22iXet8o0vkH1u+4smENwJsJom8WSqokP5MwjyvR5b0FikZ0DDzwwvSUqWFdddVVload2Eb0naVwGTC1NBauSn1WREn1R5fHvbZboiyiPz0cr+yL6VtCr79lWiL7P+7cB02j+rK9Y3XMXv+HNaAjBH8roPg1+8iZBK3k7iJ6RCj5TG71mjbJ8h05e2kn0PnYBkyWmSy+sB4+/2ySL6DFF8yMpBGJl+dZZ+pERqEXlZ0Vce9LjXZhvWco2SywWgPzyTi+Yn4mwzwu042c3mRZpwlxugsvaIcz3R5NnulOW4BMniBGpZ06uD9Akz1gjLEApK33O+ShrCBxy9Yt70B4hT5vfzjPtInqm9HkFL10UidkbtEeL2icvKdEXVR7SNmmW6Isoj+Wh1a2IvlUEaz/fCtH3df9WyhE9VYg50JampXNkShZkgF8eXzIBE6m0g+h5B/M9vW+Y4B9M1XS6LImKYuKlnUTPFEFv4YA8mRJIp0tevE+bPGURvUW5o0S99NJLcUodUbL4xvnBB5b0NcWGe1ifwJONldWvasc5RvYEjVF+fLu4AFj1zqwuWXmxQC4ic5nCZr99Tf1C8qyIZy6EvrIcQMoEwDENCHcNvyBnv8pnZaf+a/0Wgzef8xxuKNoxkfu24h0uET+HOg0EgsRZVwDsaGe4kagnL+0iet7BbAf/+xCPPPJIjJxmNTyCFs03b/lJib6o8lj6bJslep5ttTykUYSI6ItAsXoarRA9Kfdl/1ZaomfUyYiomkBydELmH2wX0UN6WBhSEvV5I6rTFJN2Ej2jbDo6e5fPA/sQ8/vvv19ZvCeLXI3o02ezjqsFYaF0QXaeCLLSsHNZeTGit3vytih7WHbauQSudxHk5YPzKALMyU0XZ8p65oQTToi/wJh1jXNZizhxDiWnmjDf3Xz57SR66pYfLqo2zZW6sW8wJXorY6vl8Vi0QvRFlMfnpdl9EX2zyNX/XKtE35f9W2mJnurEV08EcjrdCjJj5Me66KxgZCbmLLNuPVHq1ebRW7OiI8PHx8guFRbS4cc0WMYTYXUxgqXaJZggIVhbzMTegxme80TOMwpFssiVtdoxmWNSt9GypWFb1iOA5B944AE7lbslYG6jjTbKVT7IF5hg2k8Xv+E54iCILcgTnmE6X73TqPLSqXWe+mU9dFspLr2fdsfPwkLe6TLI6b3+GHM/lpgsvNPV6HiOtsbiPcw6SOsHSwBt/r777qv80lZWwKpf173aUqPV5tFbGagbZr7YMtF2Hjxw8WAFsp+pzZoCWUR57J1sPdE3s1Z6q+XxeWl23xM9ayb4lSV9mt71Uc/KeMQ20W8iWe3Cp11t3/eb1ZR9SyOde95p0+vqwc7Kkm77on8rNdEDOKNp/JFmTiZAKc+HmlZQO47Jz5xzzhnzhLmbjj/Lz92Od/s06TzpzPnAIAM6/qwIfP9Muk8a+PMxrWOtwCqBlYRyZU3pSp9Pj/HtMxUR/z84seYBJF1PWrgeeJY1CyBEfoSFvKDQNbIaXZqnZo4ZCYAt5aBMttgHbg46z74SCIn6YW0BguFoa/4HmfoqH/Ye8oOSOMkkk4T//Oc/0YRfK+bAnmXbbeXxZdN+9yPQzv6t9ETf/c1HJRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAISAEhIAQKDMCIvoy177KLgSEgBAQAl2PgIi+66tYBRQCQkAICIEyIyCiL3Ptq+xCQAgIASHQ9QiI6Lu+ilVAIdATgSGGGCKsvPLKPU+6o2uvvTb88ccf7ox2W0Fg8MEHD1NPPXUA9++//z68/fbbrSSnZ4VAwwiI6BuGTA9kITD00EOHxRZbLNCpmdx7773hhx9+sMNe20UXXTQMP/zw8fwTTzwRPv7441736ETxCIw44ojhiiuuyE14tdVWCz/++GPu9VYvjDPOOGGOOeaoJPPTTz+Fu+++u3Kc7gwzzDDhr3/9a+X0v//97/D7779Xjjt9Z6655goHHHBAzObXX38d1llnnU7PsvLXZQiI6F2F3nDDDVHrhnA23XTTeGW++eYLe++9d9xnpHPWWWe5J3rvbrvttmHZZZeNFw4++ODwyCOPxP1zzjkn0MH9+uuvVUdT3DzzzDOHQw89ND531113hWOOOSbu77rrruEvf/lL3N95553DK6+8Evfz/hVRnry00/MzzjhjOOKII3qcvvPOOyt573FhwMEoo4wSLr300srpc889N1x55ZWV407dWWCBBcKqq64as3f//feH66+/vlOzmpsvlKurrroq93q7iX677barfCOWif322y88/vjjdthju8Yaa4RNNtmkcg6ihDD7i4jo+0tNdW8+RfQD63aWWWapkCujy3322Sde2WOPPcJCCy0U9w877LBA515NIPRxxx033rL66qvHES0mO0h3sMEGC1988UVYf/31qyURdtppp7DkkkvGe0488cRw6623xv2LLroojD766OHPP/+MygJKQ54UVZ689NPzWUTPqAvS+OWXX9Lbw6ijjhouueSSyvn+QvRbbLFFWGWVVWK+X3zxxYDy1R9l5JFHDkMOOWQl6xdeeGFsn5wYFET/wQcfhM0337ySH7+z5pprho033rhySkRfgUI7QqAuBET0A2Hy5HrCCSeE2267LV65+OKLw2ijjRbJdcUVV6xqMsRsfeONN8YO85tvvglrr712TGPhhRcOu+++e9xHUUBhqCaQHqN/xJSFoYYaKlx33XUx7c8//zxssMEG1ZLooSw0W56qL0guZhE9t6DgnHbaacndQUTfC5FBe+Kmm26quF0GBdFTepQmlKdU+jvRo/ibokL5+CYkQqAvERDRD0Tbk6t1dPidMdczEq+HXPE7HnTQQTHF//73v2HfffeN+1gHcAEgXDdzfjyR/MtTFvBn77bbbvHuRpWFZsuTZK3qYR7RY3VgBJwGd2lEXxXOPr/YCURPkNo222zTq+z9neh7FUgnhEAfIyCiHwB4HrkSXGam2XvuuScceeSRVasHv/niiy8e7znuuOPC7bffHvcxUUNsmNxXWGGFXqTnE51zzjnDgQceGE/hs8R3iaA0zDvvvHGfwJ7HHnss7mf9K6o8WWnnncsjeu7HF49p2EujRE+Zpp122jDbbLOF8ccfP7z++usBF8u7777rk+21TywAihry1Vdf9brOCdLGlI0QhPbzzz/Hff7x/HDDDVc5xmy8xBJLxGOIyeqqcsOAHaw51YIQxxxzzIrZ/JNPPontAaVyqaWWCpNOOmkg+Oydd94JtLnPPvvMJ13ZJ6Bu7rnnDhNOOGG0/nz77bfhueeeC88880z47rvvKvfVu9MJRE9eiXF56623emS7XqLHRQYmk0wySRhvvPHi98YIGkyqBXoSs2D1/+WXX4aJJ544us7AFMsedYTMPvvsAescSj+xM7gb8oQ6pF2lgjsry5Xl7+PbGHbYYeMpXH3cT1rzzDNP7ANox08//XQsV6pA+3Rsn2dnmmmmONgAowceeCA8++yz8TLHtD2knQGY8QX6N8gQKCXRL7PMMmH77bdvGnQbeUBgEEGzghKx1lprBYJ1mhXMgJBCEeVpNg88lxL9k08+GTtGrhFVbQFsHCONED3BW9SZEfb/pfB//+mMcYuk5GD33HzzzZXnLEjSrtmW5+nAkdRagsLG1KhGhAjyo446KvcRrEQQAcIIluDLLbfcshcxoBgS0HjsscdW0sI6g4WEWI08gQhoW9WIKH12UBI9ZDz99NPHLL388ssBhdlLLaKHgLfaaqswwQQTVOraP88+liWCRR966KH0Uth6662jAs4FMEOR9IIVDgVvyimnrJymbvbff//MAEICNvfaa6/KvX6nnqh7b12k/c0666xhkUUW6VU2FJAdd9yxamAilkTykiodKAgMHgg0NqUC94IpNT7P2u//CJSS6Ndbb73AX7Py/vvvB4KyfIfdTFr/+Mc/YjqNEol/1y233BIYhRRRHp9uo/sp0ROH8Pe//71CaGeeeWbEy9Kth+i55/jjjw9jjTWWPZa5pdOFWBkBp1IP0dNhMxJEiiD6arMNeIdvN5A48SFZSgz3pkFq4Aih1RI68qOPPjoTk6xnByXRQ9I+joMIez8Cr0X0WAGWW265rGL1Osf3ctJJJ/U474m+x4UaB3mkzcwYvu0syXvG3+uJ/r333gsTTTSRv9xjH5z8jAR/kbUSUCDzhDbiFYANN9ww14KUl4bO9w8ESkn0jADoPEwwa1lHayYtPgDIC8F0xkjD5KWXXgrnn39+HDVhhkUInrMAOj5mRtnIdNNNFwikQ9544424YEY8GPCPTp6RpJ9TzOgO+e233yqBSZhnbQSHKe9///tfvId/EBmLcBRRnkqiTeykRM/0wCmmmKKSL8zJPo/1EH06mqYOmIHw4YcfxrrBGmIjY0ZsTMNKzaKtEj3lwpRrQn1ZHdHJZk0JfPTRR+PsCnsm3XqiR0mh7dHpMrJ97bXX4hRPlL9pppkmltVHoxvRY2Yl1uPVV1+NbgxwIF907oYJbYhj0q4lg5LosbRAvpNPPnnM5lNPPdVjRFwv0WMW59kXXngh4oj7ZKqpportbowxxqhAsMMOO0TM7IQneuoDBYlZK7jhrF/g/CmnnBKV6j333LNCkOQtdZXgBqBM9iz1aIpko0RveaSusUaMMMII0Qpo/QHXsYD4/olzmOOvvvrq2JY4BptrrrkmmvtpW/7b4Toiov8/HLrxfymJPq1IIwPMwHwACB16I5HyPmo/b0pcraj9vClxdCwLLrhgzBdz8x9++OG4n/eviPLkpZ13PovoySekZtO4UGzuuOOOmEQtokcZo6wmZ5xxRpx1YMds8a1ecMEFlUV30tE49xgW7OeZ7quN6HnOSxHT6zzRkzYWGTprP4rlPAoeMQnM5DCBuFH2KGuWQABgYiO1U089tcfzWc9wblATPfEXtl4EpIqFymIqahE93w2mfxYBgtCyxKamcg3/Nt+UiSd6FCe+ZQQrgyl55q7jPPkkv0g932Oj8+j9iJ53+Hgfjqlb1kEwk3tW/BBWEvobBDxx7b355pvxmH8MSlAa7dvknEz3oNCdUnqiZxRk093wK9viOH7+/L/+9a9M355vEiykY749mxLn58/XE7WPv42ALIQRDmZGxDopPthaykJR5YkvbuBfFtET9OMXR/EuhlpE7/HM8tta1rw/lBEc2HvpdKKnTjG9FukbxY1hPu8s5cfjY/uDmujJh1kr2Gf0yneH1CL6eFONf37RnfRb9ESPxQhFHfEKtidTr9T77zQvC60Qfd66G8zAWXTRReMrsWCkrgIUAb/qpK0L4vPoY1M4L6L36HTXfumJPo9ciRLH3FcPudIkjFC8VcCTUD0drie3opWFRsvTaDPPI3rMyJgQbYRp0wtrET0rzpnL48EHHwwoYSZmErUtyoQJsxr8qM7qheudOKInIhylshkBU+ZoE6lvLiTSYbaIxX2ko9e893QC0fsZJ3x3kDPKW6NEj1Vjsskmi4q3tRFG5lb/uLpI28QTPa4YRtQIMSY2w4J2dPLJJ8fz3qrDd+VXeIw3JP9aIfq8wE7W6MDUjqRxHJzz7R4lAGUgFTCyMnFNRJ8i1D3HpSf6Isg1bxTtNeZ6rAL2cXplYf75569YGfpKWWimeecRPWn50YcFD1Ujeiwh3lzdSH7SRVcMU9Kwjj5Nb1Ca7ushijS/mPMxzVYL0rJn8PvbFFE7l7XtBKInX7gdTGnBzYO7px6iR+HBPE2EupF7Vjk5l84C8UTv68Ofxy1w3nnnxSQhWFsMCzcMlohq0grRg8dll13WK3kUEBQRxL4pu4kYAf8MszT8lFG7D0WRejcR0RsS3bctFdFjyjIN3aqSqFQ6BkYQ+IEROlAjBcx8ti44BIyGzcjDRz7jP59hhhnis8xlNh/6uuuuG0YaaaR4/uyzz44Bdhww/xvfGL5Fk7HHHruytCrzsG2UQASvjc6YO88IDcFnSOBWEeWxPLSyrUb0dDyUxzrgf/7zn4Fo4rwlcL2/ttE89TeiNwtHveX0rpB6nqnm9vDPdwrR+9gYLDNMy4SoICGTdAlc+z0Ka192X962GtHznWKBQgiCtGmhrJDJH8L7bWXKvJUf440D/7VC9Kl/3tL1RJ+6InysD/1atRkJXhEW0Ru63bctFdG3QiBUPRHdBEN5X2IzTQLSg/yqfYC10mXBGKKALYCp1v1Z1608WdcaPVeN6EnLj5pZ5AZrRx7RE6XMSM6EkVrWiMSu25Yo848++sgO49Z3ZKa89bhhwIHPWy2riTfb1jtaTt/ng/GyFohJ77djb9rmHJH3jHqZKYL5FgJD8Pnb7zP0N6In/4xG+T4QXDjEduQRPdHlRJOba4gZBijEuHuYnYJyjhCzYJaNbid6ZhowLdUkr91z3X8fInpDrPu2IvoG6tSIUUTfG7RaRI85limJNupisQ6/qhx+UZuqlpoUGyHDNGd+pJrX4fko6r4mehbMwTpTj7DgCzgjkB8mZB+PYGkcfvjhcSU0jvsj0aMAU+cI0yZRjM0fzTk/omfEbdMPIXnwzFotEasAShrS7URPbIv/VUUsizaDIQIw8B/xMyidJiJ6Q6L7tqUiegiEZSRN+0fDtznHRN7TafKR2JryzHn1i2swgiIojBGn+RGZg2/TWJiaY2TFqMqi8BmhMIfehJ+XxQ9tK22RHwvIIvjIRrPMvTXTPC4B/2MYjFxZDa6I8li+WtnWInrS9gTEaIvpYyae6DnHD/jY0pxEkVf7vXJLI2uLrx+sEeokncLGeXyvuE6QRoieejQ/aXy4zn9+RN8I0fuR7umnn96jM/ev9n7ueometmVTrfiJ5tQy4tNvdT91P2QpYJjPbenhtK14osf1YetQYNmwKbFpHn2cSLcTPWX37T7P/O/jf3hGRA8K3SmlInpfhZArHwMjzLxfmrv33nt7/ca6T4N9H5nrPyj/q3e11rf3P4bj17dn6Uoi95FavtyiyhNf1sS/eogeYrc4iPQVKdF7qwlTz+iEmhEULxYZQVDmIHIvLGGMC8EsDbWI3o8M0yAon261/WaJnoAw1rhH8pQfYkX8bzLUS/SMmm0557y0q5WpkWv1ED3rWWy00UaZyXqiP+SQQ2IAHjeyUJG3EtnDKIzE2ZgiUwai53uyBbxsJU/Dw7b0Vxb/wzkRvSHTfdvSEr0n1//85z/RT0v1+l+aq/XjMdzvPyiLbqVjoTOHPPhREm925JlUvLLgF5VpVllopTxp3uo9rofoSYufzDVLhk87JXosJVgATC6//PJo+rdjv2U0zigUE7iPNuaec845J05BY5/rjKC9sIKfD4qsRfRpoBNklPfDM/49fr9ZoicmY9IBU+mQLGsCRM0iL0bY3Fcv0ft6QbECz3pW1OMdjUo9RI/iyqjeLG7+HZ7oN9tss8D6/wgEjoKAud+EdFAGmBljUgai90GNlBtFjhkFJksvvXRghUAvInqPRnftl5bo/S/N+RFMI78050fRfmlLv9Z13jxY34y86dgrC5ivkU8//TR3dGPpFFEeS6uZbb1EnwYK2btSoue890lzDAERZAXJMUonLd5r08yySJroaIjBBLIngI0ZGNSTuVfselYado1tui4AZPj8889Hl4ARDMuVMrMiT5oleh8FTtospgIeuHVQjPjhH1t7wN5dL9F7xZdncVPhbrIy4bKyaHRLu9ltPURP2p7E/bs80aeBm5A4VjHqEaUITPxysaTTTqJnbjok6wVLllnmCCq179ruYUln+6VLzvnBg7cS2v1sq0Xd233eSsM5LFC4+5gxZN+M3ctWRO/R6K790hK9J1ci6Qm0YyRuH2E95OqjoL3Z0P+kLD8zS8eTJ15ZIGCGwBnE/0Ruo8pCs+XJy2M95+sletLCv5x2NFlEj5kaJcyWIa2VjyySxj8PsZrZNk2D6UeYNi1eICuN9Bk6ROZ250mt+mqW6GkrWHn8iD3NA+UhVgQCROoleu7FteFHvpwzYZZHOgK0a41u6yX6vLrzRM+7+caIVakmKF7my28n0ZM3m3pXLT/+GrNFLM6H80URPaZ7YozMdeXfyT5Biyi69m2I6FOEuue4tERv0dieXJmPa0tF1uqsaQL+V7Pwi9qvp1kwFJ1uLf+8n/LHSBBfPOKDh2opC9xfRHlIp1nx5SCNann25m97HyZnH2xo59limsVEbh2Sv8Y+oyRG1UT1Q0ipMMpCYbDgLrvOc5xndGQ/OlIP0fM8gUxEcY822mhxFG0+fq5l/UIa50080Tca+MboFEXS+1YtXQgMVwSEZsRRLUDNnvNb1phffvnl4/Q2XyZ+yGmXXXbxtza97xeiIZGsYDxLnKmVjMq92KqRdg4FiBUu/Y/Q2DUCbIlAv++++ypTztKlkr3lwP82gF8YxyuitEeeQfxCOhz7axzXIynR+/gUrFr3DogVSqWeET3PYLli+ijuMlsbH2sNVjHO0xatnldaaaWKBSd9n477NwKlJfr+XW3lzD2r6bHy2XjjjRetLyy6A7FnTadKEYIMCFRDIaFjo+PPisBPn+vUY0ZiKEyYp3FHsEgT7qMyC5YOMEEJYv486+X7X3osMzaUnbUJaPvWTrwFM1U2yo5Vt5VfRN9tNaryCAEhIATqQABLCRYTxMcY1fGobulnCIjo+1mFKbtCQAgIgXoQIACQJbgJ9EtnUGDSxx1hS3TfeeedLa2yWU9+dM+gQ0BEP+iw15uFgBAQAm1DwGbiYJZnuWbcXKyoOMkkk4RFFlmkMkOD68Q9EJAs6U4ERPTdWa8qlRAQAiVHwIi+GgyM9FkCmuBjSfciIKLv3rpVyYSAECgxAswMYdlnglcJRvXCKP6dd96Js2NYj0HS3QiI6Lu7flU6ISAEhECMuIfw8c0ztY4phpLyICCiL09dq6RCQAgIASFQQgRE9CWsdBVZCAgBISAEyoOAiL48da2SCgEhIASEQAkRENGXsNJVZCEgBISAECgPAiL68tS1SioEhIAQEAIlREBEX8JKV5GFgBAQAkKgPAiI6MtT1yqpEBACQkAIlBABEX0JK11FFgJCQAgIgfIgIKIvT12rpEJACAgBIVBCBET0Jax0FVkICAEhIATKg4CIvjx1rZIKASEgBIRACREQ0Zew0lVkISAEhIAQKA8CIvry1LVKKgSEgBAQAiVEQERfwkpXkYWAEBACQqA8CIjoy1PXKqkQEAJCQAiUEAERfQkrXUUWAkJACAiB8iAgoi9PXaukQkAICAEhUEIERPQlrHQVWQgIASEgBMqDgIi+PHWtkgoBISAEhEAJERDRl7DSVWQhIASEgBAoDwIi+vLUtUoqBISAEBACJURARF/CSleRhYAQEAJCoDwIiOjLU9cqqRAQAkJACJQQARF9CStdRRYCQkAICIHyICCiL09dq6RCQAgIASFQQgRE9CWsdBVZCAgBISAEyoOAiL48da2SCgEhIASEQAkRENGXsNJVZCEgBISAECgPAiL68tS1SioEhIAQEAIlREBEX8JKV5GFgBAQAkKgPAiI6MtT1yqpEBACQkAIlBABEX0JK11FFgJCQAgIgfIgIKIvT12rpEJACAgBIVBCBET0Jax0FVkICAEhIATKg4CIvjx1rZIKASEgBIRACREQ0Zew0lVkISAEhIAQKA8CIvry1LVKKgSEgBAQAiVEQERfwkpXkYWAEBACQqA8CIjoy1PXKqkQEAJCQAiUEAERfQkrXUUWAkJACAiB8iAgoi9PXaukQkAICAEhUEIERPQlrHQVWQgIASEgBMqDgIi+PHWtkgoBISAEhEAJERDRl7DSVWQhIASEgBAoDwIi+vLUtUoqBISAEBACJURARF/CSleRhYAQEAJCoDwIiOjLU9cqqRAQAkJACJQQARF9CStdRRYCQkAICIHyICCiL09dq6RCQAgIASFQQgRE9CWsdBVZCAgBISAEyoOAiL48da2SCgEhIASEQAkRENGXsNJVZCEgBISAECgPAiL68tS1SioEhIAQEAIlREBEX8JKV5GFgBAQAkKgPAiI6MtT1yqpEBACQkAIlBABEX0JK11FFgJCQAgIgfIgIKIvT133u5LOMsssYcopp8zM92uvvRaeffbZzGs6KQSaQWDiiScOk046aXz09ddfDx988EEzyXT0M8MPP3yYbLLJYh4//fTT8Mknn3R0fpW5YhAoHdEvsMACYZRRRqmg9/zzz4d33323cpzuzDTTTGGiiSaKp99+++3w4osvprfouE0IHHHEEWHGGWfMTB2S33333TOv6aQQaAaBk046KUw++eTx0X//+9/h+OOPbyaZjn5m1113DYsttljM42OPPRYOOOCAjs6vMlcMAqUj+ptvvjkMNthgFfTQaDfeeOPKcbpzzTXXhGGHHTaehuT5UDpdxhxzzLDHHnvEbP7vf/8Lxx57bKdnOTN/Bx10UJhjjjkyr4noM2HRyRYQ6ESiZ2Cy6qqrxlLdf//94frrr2+hhCH2X0b0jz76aDjwwANbSk8P9w8ESk/0VBOk+Mwzz2TWWH8k+immmCKceOKJsTw//fRTpaPILGAHnxxiiCF6WF+23HLLsPDCC8cci+g7uOL6adY6kei32GKLsMoqq0REixho+BG9iL6fNtQmsi2iHwDae++9F/72t79lwieiz4RlkJzcdtttw3LLLRffLaIfJFXQ1S8tA9HPN998YZFFFon1eOutt+YOcLq6oktYOBH9wErfcccdAwFeqYjoU0QG3bGIftBhX4Y3l4Hoy1CPKmNvBET0AzEhynaHHXbohVCjRD/hhBNGv/LUU08dPvroo/Dkk0+Gl156Kfzxxx+90rYTRMIOPfTQ8fDbb78Nv//+u13qsSWIkPiC3377LXz33XeVa8MNN1wPEzdRtfvss0+8/uuvv2ZaKzDpf/XVV5U00h3eRbrI119/HX788ce4j8+QALnRRhstvP/+++Hxxx8PL7/8cryW/sP0Pvfcc4dJJpkkjDfeeOHPP/+MwYy4ST7++OP09prHg4roRx111EqcxhdffBF++eWXMPjgg4d55pknzDvvvBHHp59+Oo6OqtWzLyBpzjnnnBHLn3/+Oc4geOqpp8IPP/zgb+u1P84441RiTGhfCO2HURr18s4778Q6eeutt3o9W9QJ3jfyyCPH5L788stAtPqSSy4ZaLu33XZbJZJ79tlnj66Wzz//PNx1111Vo9hHHHHE2Fb4figjaT333HMRU9/Wq5XBfwe0V9otMu2008a6og2SF0zgDz30UK+k6iF62vRYY41VeZYYn2p13mh/4L87XrLOOuuEJZZYIr6PYOAsn/o333yT225op8MMM0wlv36HdpzX19h9Rbc38sJ3Q+zNZ599Fu67775KMDR9IPiSJ/LWDim6PNNPP32YZpppwvjjjx+/wzfffDPQF7zxxhu52W/H92Mvy2tvpSb6F154IcwwwwyGUcAHTPCal3qJfvHFF4+KwpBDDukfj/t0BMcdd1y48847e13jxLnnnhs7N/a57/bbb2e3h8w///xh7733juforDbYYIPK9fXWWy/w14jUCkIkHygryNVXXx3uvffeQBS8kb9/V+r6oIPfaqutwgQTTFAhJX8/+yggpJfV4ab32vGgIvq0fmadddZIrD6okzyCKZYhIxjLt99C8EcddVTsGPx524fcqGfwyRIfTLrSSiuFU045JeKc3ktENcGM1Ugofabe46233jqssMIK8XamoNHJeeG9EJSfGomSt//++0clxN+72mqrRR/06KOP7k/32Echxbdca7qb/w4gxZ122inik+aPxFEEaE+mLHGuFtHTQZ9xxhnB8kqZtt9++0Dnnkqz/YH/7tI0847vvvvu2KayrjMzxeJa0uv1RN0X2d58vIHPC4rcXnvtVZnlgOJrcQn+viL2iygP3/C+++4b23dWf08+aRs33HBDOP3003tlu8jvxxKv1d5KTfQPPPBAJFgjNDrZf/7zn4Zd3NZD9NWiw31iEDgfciopkWQRPVr93//+9/hoXxP9jTfeGGhIWSRPhmjU5jvn2BMyx9XklltuiR1stXvsmk+3L330vn5Qamy6peXLb7FUbLLJJv5UZR/t/7DDDgt5nYPdSMdHZ0A9p+I7KqaFMprOkzvuuKMtMy58R5X37qzzKEAoAF7OPPPMTEXF38M+CsvRRx8d7rnnnvRS5TgleqxWjObzBBKkHZlUI3osGHTaNjWX/BDES5+RSiv9QTNEzwDimGOOSbMRj7Hs4ZfPknqC8Ypqbyh5WPfyhBG8WTX7iuib/X74jlHW6xGUQPptr7gX+f2Qh3raW+mJ/oorrqhEqENYG220UTQpWSXWInrfufAMpt2rrroqvPrqq3FhitVXX70yWuc6I5N0Lr4nEj70Rol+3HHHDYyiTcYee+yw5pprxkPM/KeddppdqmzJH+6KPPEdDrjY6JWGy3OMsphzPNtss0UzdhbRY4LDHI3lhPgHzNJTTTVVzNsYY4xReTUuk2p5sRs7gegtL4888ki0RowwwghhrbXWqozyuL7zzjtnujPOO++8QN2YMF3q4Ycfjh0cpm+/ZgC4McpJxXe8XINwbrrppogxpE9eTJGg7ldeeeXCR/W+o6JtQMAsboQyaO2E81gbMO3vueeesY2QX9qlN8Ub0TPCBlNrl5h4Z5555ph/Mz3XKo//Fn2bxUTMN4erCSsTbXakkUaK6zDUQ/SM4BjJ415AaNd8x6+88ko89v98HjjfaH9AG/DKG6NxcEBQIq+88sq47/9B2LwnS7CqzDXXXJVLtDPM10ijRM8zzbQ32sahhx7K41FwNdDv4mbC9bXssstW2g039BXR865mymNETxvDRP/EE0/E/ov6YRCw4oorRnM+6SNwyFlnnfV/BwP+F/n91NveSk/0NECI0D6u//znP9HEaLVSjegx5V1++eXRr8T9+Gbo1FJB27aRRToa595WiT59XxHT6zzRkz6dLJojPnkvjPJRZi688MLKaT5sPgY+5jwf4EUXXVQhxzzcKgkO3OkUok+VMfygKHe23gKjziOPPLJH9uls/eIkJ598coC0vaD5mz+WTgTLAO4AL57ouYfZIt7dRDv2ih3vxERbpPiOCmLGRI747wjT+TbbbBPP+/Z/8MEHR+UmXhjwD0UEkkLpyRLM5BdccEFFUTj11FMDFqYsSTs97rn00kt7tE17DpcB9eQJMmtEz5oUjOTNmsXIDPcM5UulqP7Ap+vN3YNiel0R7Q38zAoGydOuLeaHshL345XaviL6Zr8fFL6NB6y9Qp+X56bD2rPQQgvFqvz+++/DGmusUanWor6fRtqbiH4A0aMxY1JFqHzMizRIpBrR88EvtdRS8T7MhFRmFrHRMFAIbLSTjmr6A9FX62AjAA3+AyszcWcpP1nJdQLRQwzrr79+r+zttttuYdFFF43nsWD84x//6HGPV5yyTNjcTCASC6KgOCBZrh7f8T744IPhkEMOiff6fxAjBIWcffbZMcbCX29133dUTNGyNRtQchdccMGYvFd2UAQYSSKQKe6aRgQzKYojgkJg32qaRkr09SqQlk5K9HyzWCVMgYOAaIN5sQJF9QeWH7adRPTNtDdcHpdddlmlSCeccEIM2KycGLgDaZqVr6+IvpnypPnOO8bFg5KJwCne4lnU99NIexPRDzQpebK1wDMqqRrR08ExekZYN9oqlmMjddtCaph5EUZ73tfo352OFuMDA/5V89HbPbYtekSfaqT2nnq3jMqYCUBQlOHByBOTHVJv+p1A9HmBT2uvvXbYcMMNY3kggs033zzu2z9vwSBIx4+67R62hx9+eGDZZSRLYfBEv99++/WysPCcH0GnMRAEEZkpnHsbETpjZlj4jgpTMu0X8RYJ8onVAvFkRRr+O4k3DPyHgoMbivXmTVHhEiu5WRxNNfJOiZ6A1aw4B/9Ov++JHnMs9WB+YxR5AkxTC4t/vqj+wKfpsRvUI/pm2hsuRaw4CGby5Zdf3hevss/3YisA9hXRN1OeSobdDiNr+jesFtZeuIy1zfo7LFc2k6Co76eR9iaiH0j0BKzYlDQaJKY9Glw1okdTtWlGrt5r7vpOkJs7nejxRVogYM3CDbyBDpuIZCLUrbHnPUsnah953j2c7wSiZ7TsRyiWX6+IZQXkXXfddZVOIE+ZIy0wW2aZZWKyWel4os8jMh/4lAbkNTqatvKxpWNhBO87Kk/c/jxuG2ISEBQgFCHk2muvDfjlveAzh0TNvOuvpfvVyM4TfTNk4Yk+fS/+fUi3mhTVH/h3dBLRN9Pe+K5N6a2m0Pvvp5m685hV22/1+7G0ceVsttlmcQA21FBD2encLVZiM/P776SV76eR9iaiH0j01NDFF18c54ezbyOhakRPEJSZWXmmXulvRO8tHPWUEaWJKWK1CN7S6k9En0fSvqPKckX4DgYzPz+mlCUE0xEQiuDHROH04tMxi4i/zr4n+tTUXTTRe9eAH5XxLfGH0MnZdNDUmrHddttVLDvx5hr/sCgQ7JglnugJwDMLS9a9WeeqET33e+tF1vNF9Qc+7U4i+mbam3fbVJvSy5oStkZAXxF9M+WhbnAxEFzXiGUMd5/Fg3iib+X7aaS9iegd0fvOmuAz5nLycZuPLh1NEBSEXxXBn8eiIPXIhx9+2MOX3+kjepQdHzVarYyYrrjfFCCsIwSD4Q8jaIyFUBB8rkQuI2UjeqZwZk3LAgvvAmCWAoGOXlol+rypVv4defu0f0YlRXVUvnPnnSg2WCCIhMf9QbtAcHtZYFO9RO+DAWMidfxLiZ480F5ZEAXB17rLLrtkzqjgelH9AWmZ9Heix1JDFDqSpQBbOX1b6HSiP+ecc6KLyfKOxZN4GqbeomCaQOI22GkH0TfS3kT0juipIEyONo2GfRppHtHja7R5tY2QoTUE29oUI47zglU8AVT7YEijaB89C+bQaOsRb6qD5Im8zvoZYJQoM4WWgegxWdsIIM8qAL4+wKaW6b7ZEUk99VjtnqKI3v8MMdPwGIFnBbP6uIW+InpIngW0MMvynVsfgDKChcL8rR6novoDn2Z/J/qll166suJote+cBZhoV0gnEz2xI7jvTMydZce2hUPgD5N2EH0j7U1EnxC9JyAaHFq8feTpiB5SttW/6pmTapWebv30E2/y9PdhrmSeMtII0Tf70UBGFgDVCNH7xRuqLcqivuAAADi3SURBVGrjo9SrdQAeA28azluy2N9f1H4RFhd8cRZVnLpufD59hDnmfXDy0uqI3qfV7H5RRO99jHwDeT/B6mcR9BXR+9+j97NywCyrXjhfVH9AWiae6JuJlbF0bIsVrZGfqW21vbFuxvHHHx9fn85osjyx9Sv4Ndtn+fTy9lstj1dcsixu9l4f88W5dhB9I+1NRJ8QPZXi/fIcm6RE7/2LjF6ZNldrrXJLy2+ZIkXQGsKSsP/617/85bh/ySWXBBbuQGoRPVGgzOtG0qkd8WQd/5olel+WPOUH8z75s4Vd6iV6FB3zz7Jgz7rrrltHSVq/pQii95HwLBhjCxr53DF6JGjPzH1+6prd12pHZem0si2K6L31DAWHGQ2psES1X5NgUBA9efLfOscsoENdefH3tNIf+DT9wCPLwuPvrWe/r4k+bdO0X5uN4fPrLV6dTPTe8pA3TZZy+cEbx+0g+kbam4g+g+gJHMI8l0pK9Jhi8eEbYaFx43/NMutxL2myMldK5D5ghQU58Mv6JRNxH+DrMqlF9NznCSHPvGTpZW2bJXoiUS2ADAInuMyXBd89yoCt9sW76yV6frCBDtYE6wGrqbVbiiB6Ist9vWdF7/vlSlHQaIcWwGNl9PXa3033zFFnKh2SNVrFLcY0RHOPcd+gInreff7551d+0Ib6SRcrKqo/4F0mflU53pmu3Gn31bvta6InX359BRQg2jkrP5r465zrZKKnvdJuEeqDVT3TH7Dx7st444B/7SD6RtqbiD6D6CEjNEy0US8p0XMNUoPcTCAtRiZ0XDRYzN+sijfddNPFkVoWSfvGQzqM+DBjYh1gfWg+di9Zafjr7KcBI5i6CRaxFalYfjJvhTGeb5boCVw69thjSSIKeLCaHtHflJOpY/ajIP6eeqbXcb8vFx8aAX4+AIaVDv0Sq/aOVrZFED3vJ6DR/8AKnR0rMaIo/uUvf4lLCls+wYx5vql0E9F7VwzlRKkhaJNAReaw01bSb3BQEj1LxxKrYoGmWJXowCEvkyL6A0uLLZ05rjN7J+/CdcDo3hRolF3m/aeC+djcb3aNH8eyaYz8oA+/HueFNumXBS6ivaGoYZE0SxXfLf0P78e0by4ty0cnEz15ZOaIDe6oD3iBYDz88vyKpAVvWnnYtoPoSbfe9iaizyB6APTRohwjWUTPee9f4ria5JF0aupJ06CDq7aMbno/92IuzpNqU114plmi51kIip+irCZ0TPxUJVLviJ57KRdmXuv4OOdl00037fGLZP5as/tFET3xHJih6byrCSZB2p/Nu/X3FtHx+vSa2S/KdE8dEpPiR+xpfiAFltm1znNQEj15w4qCydQky9VWRH9g6bNludUsV4/dk7eIEwq34Wb31tq+/fb/X7qYe4tqbygYLHNrZJ/mA2XFZoR0OtGzyqMt+5yWw47BkUXBrJ9qF9HzvnraW+mI3s895Nfr/I8tWCWxxY+MJm3T5zjH76izhnGWYIrGBJW3gA6aH6Nq/P9Z63qzAAM/DsKo1wvP8bHxrC1ak6cs+OfYp6Hh02YLufiPLGv1Nv+8J/q8AEF/v9+ncRM97n/kxK4TVY21gpGEBelUC2qx5/yWkRXmP9wgKWn6D8o/08q+nxVBpPi9A36yNxU/NbNa/RA/gaKS1rOlxwif+cTUe5b49juoTPfePeOXRvYL46Ac4dZC/KgDv/x5550Xz/MP6w6r9aUjT66hAPJ9ohDaFK1qAZ5+Hr1fg5+06hEf3JTO9/fP+1kAnM9aF6HV/sC/j32IksC80UYbLVo5/Ldsa36kz/hZDem1vOOU6Itsb/wIFv0n36+NiLGK0A9jyQFXhOXHbYGlvHw2e76o8uCrxyKVWpxQTFFGWQKbfs74g/LYsupFfj+GQ632VjqiN2DatYV4MDvSqOnUGTnz8VD5eZ23zwsEwLKRNCAUC57rr8JIDbcDnTjzkRn9+B9g6a/lKiLfKJJgwwcKoVHXWIzqaSNFvL/T0sClAR60f74XftUvy6LRafmulZ9W+4Na6ffX6/yKI1Mqzf3AWvCsfIlQ//aDSJ1cPr5hvl/csuwTfMyy1YNS8tqbiH5Q1oreLQSEgBAQAj1+Uz1vto5gah4BEX3z2OlJISAEhIAQqAMBfq2SAGUf6GePYf1kZpC5I/J+bMbu17ZxBET0jWOmJ4SAEBACQqABBIjLwFzPD9tA9kxJw01FcO1cc81VIXlW0fRTiRt4hW6tgoCIvgo4uiQEhIAQEAKtI2BEXy0lgnKJZlccTzWUmrsmom8ONz0lBISAEBACdSLAjAxmpjDLwkz09ijT6fjhK2YJlDUY1bBo11ZE3y5kla4QEAJCQAj0QoAFcsYbb7zAL4S+9tprmT9k1OshnWgJARF9S/DpYSEgBISAEBACnY2AiL6z60e5EwJCQAgIASHQEgIi+pbg08NCQAgIASEgBDobARF9Z9ePcicEhIAQEAJCoCUERPQtwaeHhYAQEAJCQAh0NgIi+s6uH+VOCAgBISAEhEBLCIjoW4JPDwsBISAEhIAQ6GwERPSdXT/KnRAQAkJACAiBlhAQ0bcEnx4WAkJACAgBIdDZCIjoO7t+lDshIASEgBAQAi0hIKJvCT49LASEgBAQAkKgsxEQ0Xd2/Sh3QkAICAEhIARaQkBE3xJ8elgICAEhIASEQGcjIKLv7PpR7oSAEBACQkAItISAiL4l+Drr4ZlmmimMNtpoMVP//e9/ww8//NBZGSwgN8MPP3yYbLLJYkqffvpp+OSTTwpIVUkIASFQBgQWWGCBMMQQQ4Q///wzPPDAA4UWeZpppgnjjDNOTPPJJ58M3333XaHpt5JY6Yh++umnD5NOOmkFszfffDO8/PLLlWO/M8sss4QJJpigcuqdd94JL7zwQuW403auv/76MNRQQ8Vs7b777uHZZ5/ttCy2nJ9dd901LLbYYjGdxx57LBxwwAEtp6kEhIAQKAcCN998cxhssMFiYZdddtlCC33JJZeEUUcdNaZ50EEHhUceeaTQ9FtJrHREf84554Rxxx23gtlXX30V1l133cqx3/HEyXkUgp133tnf0lH7Pr+dQvRo0KuuumrE6f777w/ksRXxRP/oo4+GAw88sJXk9KwQEAIlQkBEX5LKTomeYm+66abho48+6oHAXHPN1Wu0KKLvAVFdB1tssUVYZZVV4r0vvvhigKhbERF9K+jpWSFQbgRE9CWp/yyi//e//x2OP/74HggceuihAdO9FxG9R6O+/aKJfr755guLLLJIfPmtt94annnmmfoyoruEgBAoPQLtJHpcAZNPPnnE+Morrwwff/xxx+BdetM9NfHNN9+Etddeu0el3HDDDWHIIYfscU5E3wOOug6KJvq6XqqbhIAQEAIZCLST6DNe1zGnSk30H3zwQRh//PFjZWy11Vbh3XffjfsLL7xwwMeN+HuqEf2II44Y5p577jDhhBPGyMtvv/02PPfcc3HEWW/05XDDDRdGGWWU+N4ff/wxfP3113F/2mmnDfPMM08Yb7zxwueffx4wgT/00EPxmv9Xj4+eqPWRRx45Pvbbb7+Fzz77zCfRa5/yzDHHHGHqqaeO7g2iSV966aXwxx9/9LqXE+Sfcpiss846YYklloiHb7/9dqZPHUUrb4bA4IMPHoYZZhhLrsf2l19+Cb///nuPc+kBUbAWfGPuGTDAKjDjjDMGAiwff/zx8NZbb6WPZh4TbLPQQgsF6uSVV14J9913X6WerNzg+uuvv2Y+3+rJIstD9DFtdpJJJolti0hk2hZWkmqjEd+GvvzyyzDxxBOHJZdcMtDmb7vttspMiNlnnz3wLdFm77rrrvgt1Sp/o+0tTW/ooYcO8847bwy4JRaH74iZGa+++mp46qmn0tvbdtwJ/YEvHN8RbXa22WaLfd7rr78ennjiiUqf5+9t9z7tYsopp4x9Je9i9gzfIcFred8N+ff9Vl6fSl9h3yH9StpPZRH9VFNNFQN8CWQmwJc+rla/wr1jjDFGJlR8R9W+n6yHiqyfMcccM8w666xhuummi4NVeK3URH/55ZeHtdZaK+J+9913h6OOOiruH3300REkKuz2228PSy21VDyfRfSrrbZa9EGPPvroWfUXzxHwh28ZpaGarLfeeoE/BFLcaaedwimnnFJRRvyzdGDbbrttj9iCWkRPg6ZsZqngY8GSkX4MvGfxxRcPO+ywQ+Ve/27uP+6448Kdd97pT8d9zqMUNCIe+/Q5FC7IIkvqibr3H/ZKK60U8fQzKSxd0iJSNgsL7oEUwS6rbJDIww8/HDbeeOOYHArA3//+97hf9L8iykNHi2ILDqYEpfmkwz3iiCMyFcqtt946rLDCCvERrwhbGuCIgkdnbsK3tP/++0elys75bbPtzafB97D00kvHuvLnbZ/O++qrrw7nnXeenSp820n9gRVuu+22C8sss0xmXaOc8Y3Vq+hams1sUQax8I0wwgiZj9NGnn/++fDPf/6z13UGG7QrJMsCaw9ceOGFFQLOinz33w+4HHPMMQHl0Av5OOuss8K1117rT/fYz4rh8jfssccedbsVi6qf+eefP/LMsMMO67MS90tN9GeeeWZYeeWVw1hjjRW+//77sMYaawQ0K8z2bOnA0XxtGkYW0ZNGFnGkSEMgEMU999yTXqocp0T/008/RS28ckOyk0bWVyP6GWaYIRx++OGxXCTDx/K3v/2tMhr1SfOB8GHVEpQgiN1LM0SPwsAHlyX77LNPwC+fJfVE3fsPG82W0Wee3HHHHeHYY4/tdZkRAvVcTZljFG8KVF8RfbPlgRCXW265XuXMOnHLLbeEk046qcclT/Q9LtQ4wEKFApBKK+3N0qqmENo9bJlOS8faLumk/gDrE7FH9G/VBGJjkFOtb6r2fD3XmBJbTyAuCiYKeSr1Ev1ll11WGfnXInr/zabv4zir7dt9WFj3228/O+y1zXp3elOR9VPrGyo90WMOslE9HcAUU0xRGY3R6bMIQj1Ezwgb05MpB5iQZp555qhImOmZhoVikTdq9ETPx2ejLczrmFTff//9qFRgfhtppJGiJu7nyucRPfcffPDBlfS++OKLsOWWW2aay30eaJjce9VVV8VysVDN6quvXlkUgut8vOTNBHO4J1NG4+CAYM4iSCUVCJv3ZAmjQrRnE0YFtihFo0RPGmB/0003xfUQyCd1bwSdVz/bb799HBFZHlD+brzxxogfbQN8vfQV0fPOZspjRM8IF3M2a0O89tprsTxYfdZcc83KqIh3YNmhzCae6GmnKLAErjIqtzbLeaxRmPb33HPPioJJ2t7s2mp7I098w3TwJlgZLr744lgmrmGFoR3yLTNy7Qui74T+IFW6GagQwPrhhx9GtxVt3/omCJaBDu6wdgjWU/osBGyYc46JnPZi3zguF9pzXxE9eaGdMrDDfUc+wMRM/1zfZJNNMs3wuCiZOuxl8803Dzaarofoi6oflOcNNtigkpWff/45utDgBvo0BnmlJ3p8h9ZJMM8bnz0VToNbccUVwzbbbFOV6CFuSIpns4RR4AUXXFDp6E499dRIEln3pp0e91x66aUBc1QqmAjRwD1BZhE9mue+++5b6YDxVzKSpzGkgu+VDxIzNfL000/HTjq9j9E3/j4E/6tvZOm9RQfjNTq9zo/o+agp+//+979KNiH70047rXLMAjyY8U3AAhOeKQNZmPg88VxfEX0z5SF/kDILR11xxRW5vsiLLrqoYsFIy+yJHsUWFxMCjqbk4Xri20F8e0HhxM2BFNXe+BY222yzmCYkAmHx/aZC5wzp06m3SzqlP8A9A9YmZ5xxRrjuuuvsMG7Bn76JLUIfdthhh8X9Iv+BO/2YyYYbbpgZG8S3RvzLvffea7dWtu0Y0fP9YAkilsqEvJ577rkVwkYZ2Xvvve1y1W0jC+YUVT/pN4SSi2KexjyVnujpxKlYRomYytFw0TLNTI/2X21EX7XmB17ELEbHilT7mFKiTzvYgcnlblKixzSEv8tGWVgE6HzzAl523HHHSjwCWNBhZgWlEGiEQmDppqM0n8FOIvoHH3wwHHLIIT57cZ/OjgAW5Oyzz45+3Hgw4B+jC5QDhI6BxZUsSDKeHPCPNnPNNddU8Ogrom+mPJbnWlvqntEMkipznugZIZ544onxPkbuCy64YNxHCT3yyCPjPooAlhgENwAmUaSo9kZsBG0Qod3aAk3xRAf+64v+AB+zBRpbX5YFBaPSvfbaK16CHLDYFS2pMk19Nbp0dTuI/o033ghY61LhezerAt98vW6uRoi+qPrx3xB5RYnie01FRD+A6Om8119//R7Y0EnRWTVC9Pj1ifSddMASu0YcJIp/yoK4qpF3SvSMlLMqrUdG3YEnejpTH4CDyZJGnTXSsSTosHFdIETCei3cSN22kIAF1RhWlo7fdhLR41PLGs35EWfqlyOozmYNoChRnizBTE29I31F9M2UJ2Yw+YfVCbcMxGD1S+dsCq7Fr9hjnuhxxaAoIx4rLCknn3xyPO/bANYpa1dFtbdFF1007LbbbvFd/MNSgfJWra1Xbm7jzqDsD3xfgELIyNTE6ti23pVBkGWWcm/PNrMFB9xlJnwfKBfpqNOuZ23bQfSYzokzSiV1BaH81JPXRoi+qPo54YQTKkGvmOqxUGSJiH4A0WP+oMOyhk9DR6Ojo6iH6PHREsU80UQTZWHc4xz+7LygFE/0mNZtRbkeCVQ58I0nvQ0/lDdRp9c59oEsWdfzzvlOPb3Hd/LVyp4+l3fszeSN+ujzFCeiwZlmhqQBeUSeE3eAVFPSfBp9RfTNlCcWZMA/FFIUP6bhWLu3a+k2HSV7ovfE7c9DthbdzijD1qnAgkbAGlJUe0vNl6TNN4wZE7Msyp13x3C9nTKo+wNM4MSQNCN8Xz7mppk0sp7xI1iuM/pkEMO7UEKYpprlTrS02kH0tH9G9VniXX5YRb15P+t+ztVL9EXWD0oz7gYEK+v5558f99N/IvoBHQ/io2WZ4mEjhFpE76+n4GYdVzOjeaInAI8OshGpRvR8WDRYypYnaN1o341KfyF6G6Gm5fMknbpW/EqK1WYHeAWkr4i+mfJQdmYx4HesRfCGUzWi964OgpHMbE4wHH+IDxbyCmeR7Q2r2S677JJbJoifeBxGce2UTugPiJ/Jm8VSq+ztInrWR2AGgA9083mhfyLeI2+k3w6iJ7aDmI4sIZ7Bpt1Vi6vyz9ZL9EXWDwodigPCoCQrvoFrIvqBRI+vyuYG00GZBuc/3JSk55xzzh4LwNBoGBFiQmE0QQeJYOYmyARJ04gnB/7zRO+Dmfw91fZTosf1QDCeBduQHzrdPM3ZNxq0QzrGeoQo3jxzXyeN6JshxtNPP71iqUmVAI8NP3ZE1DnSyURP50U8gSl0WK0Y7WLeJUiRedUIMSV0+ki7iL7o9jb22GPH361gRG0R3rEA7l8186a7randTukPmF3gp4kycs375n1BidC2RaX8+aL2ISR+V4S+lil/WYombiIi31O3SzuIvppJ3hM9LiZiUWpJvURfZP34b4jYD9YkyRIR/UCizwKHc9WI3pt1mUbECDyL8Ji/zm/FI31F9LZ+P9oj05/so6r2fm8Gggwwt7Uq/Z3o/fxUVgRk1JglBPlhBkc6megZcTPyRuhMCc60FSHjyYH/cBtRd0i7iL4d7W1g9mOAJKsfEl+B0mLtn+tML2zHAjGd0h+kPvF2ldewbmZLHok8p35Y6MVGpaSVZYJmipgFd1ZbMId+q9oUN2+SJ5CNaaVZ4u9DiaffrCX1En2R9cOgdLTRRotZ8/EyaV5F9C0QvfcxMvJjRJ0lPqq7GtEWOaL3i+l4siV/3tzq8+sDO+rxf/tn8/b9u4sgQG8iryeP/oNtZkRP7AXTLJFqHYxfkauIcubh2Wp5vOJSbXSL62rRAUFuSLuIvh3tLWY4+cfaAJjsjeyJHSCGoGjppP7Aj0irjfSKxqCZ9LA44iIjCA7JUqipQ/vhMeb6M40xS/z3kTWX3V8nWJTjVJitBGmboPTWYxGpl+hJt6j6sbVeSLNaDJSIvgWip7NgqhmS9zF5TZT7BgXR814fDIM/jMCpdCTnrReM9piyVE+0KenniR8ZsmCOTdnKu7/W+b4m+tQcm+XDxP/IPGWTTiZ6b3nIU5Qw77NIkq0d0C6ib0d7szpIt77907nbjID0vlaOO6k/8DFHTGXbeMCUtk4W3AvMEkKyZrcwhdWWpKX/ol9JF/dJV9+rRfR57lFbUIq8YKE1ly7H1aQRoi+qfnxeyRvWOtzGqYjoWyD6WlOqiIYk0t2iIgF/UBE9U6iIyDQTGaNTphV6XxgfE+Yf6+AhLAL40g+KcnAv/n6W//3Xv/7FqUxhcRZ+8hfhA91oo40yF8vIfDjjZF8TPVnwi8fgw2aeLb9fgECKkIifTtnJRM/CMgQhIRA4/lC/rgJmRZQBW83Q7rMgO459dL23DjUajFdUe2OOPj/4xIg6a+TFCA2Li7X9eoOrKGsj0kn9Aa5CXIYmWeZwu2axDRAfGBYtTNWkndH/5M2fx8pCPpC82S1+NO5nb/BMahXgXC2i5550gSzygNJugXjV4nJ43ksjRF9U/aTfEDMZUJqsf7L8iehbIHrfsQEoq9QR1EQgHxWJhsqvHHkZVERPHlKNlyAsGroXv8oY5yEDAjwgLzpR1gPA788vI2EKpWFVWxmPhsgPiUAgCIoFkf+M7o1gWDqYX9JKhehwW3/AruHPs2mMBA4xLccLS7pikjbxnUMzpnvSIcgOP50JQUtECIMHOJhP0K53MtGngUDUL9PP6NBYB4A2m67p364RPXgV0d5s/j6KJGZfvj/qh3KwgA91ZB0392D2tbZndVbEttP6Ax8zQPkgWfon2idrYGAOZ+qofU+NkFojePlV4Bit861TP3xHBOYxGDCzPenmrQ/hXT3ch0WKtMg/vv40or8eoqc9EHRMv8w6EiyOQ5+FcC1rgax4MeNfI0TP40XVj7eaki64Upf0g/S3sa8eoG39ycWyiJ8uhfnEzEF55ffmxZSkIS+CIfyIPU2HxkKjpoNF0jT8/e3y0ft30PiJYDXJcjnU+wMhpFGL6LkHs6GtXMZxKnm/Xuf9T+kzeceMSmzpVe4pguhJx6+WxbEX6hhysVFwJxM9+aYjZTZGNaEDtXbSTqInD622NyP6auWxa9ViaeyeZred1h/gVuT7ZkRdj/QF0dfKB4q6rdSX3pu6QdPrROwz+LBZRrWIHkXdSD1Ni2P6dv7qlUaJvsj6qfUNlW5E730jjRI9nTmmbC+MGlhLPh15cg8dJGZrOkwL6KoWAOWJHuXA1hD376u27wM86PwgnFQwX2Kes1Xt8EHZ4kD+XkiLJU29pu2voynyQydEudJB1BJG4gTmESGKlcMCo3guXY3O0ko1XjtfbZsSvZ+r3eyI3t7HT6BSR9Q5+YfgsUwQPY6Vw3yMkCS/utcOKaI8EBIRx/5HaCyvtAeCSrGUWPBTujSqN/97M7hfGIfV8nADIX7U7hfSsXeybaW9WdvC7OrblU8faxsk/8ADD/jThe93Un9ghQN/XGbmkrPztoXwsLJhWvc/XmTXW92yvDh9JAqHuU/SNMkDgy4Cl6sJI3f6xbSeMVVznlG/9Vm1iB4LDPebYmDv5RtgHQKmJzcijRK9pV1U/fANoSRlTS0tHdEbuEVvMftggsL8Cdnwwx3pmuhFv7Mv0kPjxQ0x+eSTxw8C8x/lwzLh/ft9kZdOegf17YNeiFOwX7Erampiu8uLJYo2i5JK7MFDDz3U4wd/2v3+rPRbaW8oMJQHUy71gwmTtorSnAaeZr27yHOd2B8Qq8AUUOIZcGW89957kdj7Eht+MIw/8oDLi7UbWJ2ukf4EEz3f2iSTTBJ/CRHTeytBw6wSiVsTJYR+O2+1vFrtAxeluQ/8rKdaz9n1ouqHPOCSYVlr9qlnEb2hrK0QaAEBgp1Mk85yh7SQtB4VAkKgwxFASWDVR7M0VFuMZ1AURUQ/KFDXO/sdAgTlEEuQtXJYughNEdMS+x1AyrAQKDECfpobls7ll1++o9AQ0XdUdSgznYqABfXhusCf+eabb0ZfIBHFmCJN8G/jC5YIASHQnQgQJ2DLXTOCxw0xxhhjVArLzIasn8Ou3DAIdkT0gwB0vbL/IWBEXy3n+IOZpVHm2IVq+OiaEOgGBHDNsaxylvB7J0w3biVmICvdVs+J6FtFUM+XAgF+4Y6P21ZCtEITec9HTcRtrama9oy2QkAI9F8E0tlA9AEQPGs4HHzwwZmLNg3q0oroB3UN6P39CgEiu4nS5Y+fEu7LiOV+BZQyKwSEQMcgIKLvmKpQRoSAEBACQkAIFI+AiL54TJWiEBACQkAICIGOQUBE3zFVoYwIASEgBISAECgeARF98ZgqRSEgBISAEBACHYOAiL5jqkIZEQJCQAgIASFQPAIi+uIxVYpCQAgIASEgBDoGARF9x1SFMiIEhIAQEAJCoHgERPTFY6oUhYAQEAJCQAh0DAIi+o6pCmVECAgBISAEhEDxCIjoi8dUKQoBISAEhIAQ6BgERPQdUxXKiBAQAkJACAiB4hEQ0RePqVIUAkJACAgBIdAxCIjoO6YqlBEhIASEgBAQAsUjIKIvHlOlKASEgBAQAkKgYxAQ0XdMVSgjQkAICAEhIASKR0BEXzymSlEICAEhIASEQMcgIKLvmKpQRoSAEBACQkAIFI+AiL54TJWiEBACQkAICIGOQUBE3zFVoYwIASEgBISAECgeARF98ZgqRSEgBISAEBACHYOAiL5jqkIZEQJCQAgIASFQPAIi+uIxVYpCQAgIASEgBDoGARF9x1SFMiIEhIAQEAJCoHgERPTFY6oUhYAQEAJCQAh0DAIi+o6pCmVECAgBISAEhEDxCIjoi8dUKQoBISAEhIAQ6BgERPQdUxXKiBAQAkJACAiB4hEQ0RePqVIUAkJACAgBIdAxCIjoO6YqlBEhIASEgBAQAsUjIKIvHlOlKASEgBAQAkKgYxAQ0XdMVSgjQkAICAEhIASKR0BEXzymSlEICAEhIASEQMcgIKLvmKpQRoSAEBACQkAIFI+AiL54TJWiEBACQkAICIGOQUBE3zFVoYwIASEgBISAECgeARF98ZgqRSEgBISAEBACHYOAiL5jqkIZEQJCQAgIASFQPAIi+uIxVYpCQAgIASEgBDoGARF9x1SFMiIEhIAQEAJCoHgERPTFY6oUhYAQEAJCQAh0DAIi+o6pCmVECAgBISAEhEDxCIjoi8dUKQoBISAEhIAQ6BgERPQdUxXKiBAQAkJACAiB4hEQ0RePqVIUAkJACAgBIdAxCIjoO6YqlBEhIASEgBAQAsUjIKIvHlOlKASEgBAQAkKgYxAQ0XdMVSgjQkAICAEhIASKR0BEXzymSlEICAEhIASEQMcgIKLvmKpQRoSAEBACQkAIFI+AiL54TJWiEBACQkAICIGOQUBE3zFVoYwIASEgBISAECgeARF98ZgqRSEgBISAEBACHYOAiL5jqkIZEQJCQAgIASFQPAIi+uIxVYpCQAgIASEgBDoGARF9x1SFMiIEhIAQEAJCoHgERPTFY6oUhYAQEAJCQAh0DAIi+o6pCmVECAgBISAEhEDxCIjoi8e01ClOO+20YY455sjE4Msvvwwvv/xyePvtt8Mff/yReY9OCoFWEJhrrrnCAgssECaeeOIw5phjhp9++il8/vnn4Z133gm33HJLePfdd1tJXs8KgX6JgIjeVdsNN9wQhhhiiPDxxx+HTTfdNF6Zb775wt577x33r7322nDWWWe5J3rvbrvttmHZZZeNFw4++ODwyCOPxP1zzjknjDPOOOHXX38NK6+8cu8H3ZmZZ545HHroofHMXXfdFY455pi4v+uuu4a//OUvcX/nnXcOr7zyinuq924R5emdavUz22+/fVhmmWWq3zTgKmR/5JFHhrfeeqvmvbpBCNRCAGLnO51wwgmr3grx77DDDuF///tf1fv23XffMMooo4Tff/897LHHHnFb9YESXNxoo40CfRNy5plnRqW9BMXuiiKK6AdW4yyzzFIh1yeeeCLss88+8Qof+UILLRT3DzvssHD//fdXrXgIfdxxx433rL766uGHH36IygOkO9hgg4UvvvgirL/++lXT2GmnncKSSy4Z7znxxBPDrbfeGvcvuuiiMProo4c///wzKgsoDXlSVHny0s87Xy/R2/OnnXZaABuJEGgWARTos88+Oww++OB1JbHbbruF559/vuq9N954Y/xuuWmdddYJX3/9ddX7y3DxlFNOCZNOOmks6rnnnhuuvPLKMhS7K8oooh9YjZ5cTzjhhHDbbbfFKxdffHEYbbTRIrmuuOKKVTV7Oho6CAj9m2++CWuvvXZMY+GFFw6777573EdRQGGoJnxEdF6IKQtDDTVUuO6662LamCI32GCDakmEIspT9QU5Fz3RY56/4oor4p1DDz10GH/88cP0008fRh555MrTKC1bbLFF+OCDDyrntCMEGkHgwgsvDGOMMUZ8hPb0n//8J6AUYy0aa6yxwkwzzRSV9TnnnDN+PyL6RtD9//eK6P8/Fv1tT0Q/sMY8ua622mrhxx9/DJAT5nqIux5yxTd90EEHxRT/+9//Bsx/CNYBXAAI182cH08k//KUhUUXXTTQQSGNKgvNlifJWl2HnuixZqCopIJ7Y7nllqucfvrpp8Oee+5ZOdaOEKgXAZRH7047+eSTw80335z5OArm5ptvHkf/tUboGtH3hlBE3xuT/nJGRD+gpvLIdbHFFgv4xZF77rkn+pTjQc4//OaLL754vHrccceF22+/Pe5fcsklYdRRR41WgRVWWKFqIBqjjgMPPDA+9/jjj4f99tsv7qM0zDvvvHH/gAMOCI899ljcz/pXVHmy0q51rh6iJw3DhP16lCjKRKDfbLPNFi0Dr7/+esDF0khwFaM+lLFJJpkkjgC/+uqrGI/RSDo+HxNMMEEgH0899VTNWAOeM0vGb7/9Fr777juK3kuGGWaYMNxww8XzWIXyghbxH9t9kBaKKUIg2owzzhitUO+//36gDREAWUuGH374gLtnhhlmiO4hfNjEgIBNPeJxgXybqZ963pPeg2UL0zoCpmuuuWZ6S81j8j722GP3uA8fNPE6CN81bcULlgNiefKklfqhLhhkIN9++22uFZF3MAip1p7S/E022WTRzz7VVFPFNvPmm29GCwjfYCoEMw455JCV08QcUbcIihCDoFQ+/fTTXvn1bfrnn3+utNX0WSs32FZTxHy+Pvnkk/iNgNdSSy0VJh3gWuB9BF/SZ3/22Wfpa3ocE9NBnzD11FOHjz76KDz55JPhpZdeyv3uejzcjw5KSfQEi0FIzQqBZNtss0249NJLY8BOs+mgRKy11lqBSOFmBf82jbqI8jSbB/9cvURPpzH77LPHRwl4QgHKk+222y4G+NGppUJHiFukWlAfBMs9EFlWGqQJURIAiSUmT1ZdddWw2WabZaZBvMQhhxySq4B5a49366Tv8mboatYfFEk6J+Tqq68O9957bzjiiCMq5O/Tfe+998Lf/vY3f6qyT0dHXaREZzcQvEa0uh812zXbtlo/lk4zW+oV1xjSLNHjJsOi14hARt4qlT7bSv1466IfMPh3zD///JUg4XoU5Y033jisscYamW2XdOnT0uBYiBzSbEQYmKBcevn73/8ellhiiXjKWzr9PexjieH7rIWtzxf9MAGCW265Za8YDdK58847w7HHHpu+Kg7ICMr0iozdhHIN7jzbLVJKol9vvfUCf80KIyX8yr7BNZPWP/7xj5iOddjNpEEnzLS1IsrTzPvTZ+olekgV4kUYkRD/kApWkOOPPz76WdNr/pgP+qijjooavD/PPqO1888/v+LDTa/7Y+IJzjvvPH+qsg/pQta15JprrskkxXqJ/rLLLquM/OslekZXWJJshJ/mMa/jJIaEEXGe8mPp/PLLL5kzRYqoH3tHs9tNNtkkEpg9j9LBKLURaTfRN1o/9RA9xAmBItWInjqCtPIUOY8Tbe+CCy6onGqmf8PymCrL9RI9fRmS114tYz5fkDjxSHltmNgf3DVe6v2WsciCXTdIKYmekaQ38RGsYw3l2WefjfUKQWACRejovPkT0w7kwYgcMxJCZ2EBdJidGGUj0003XSCQDnnjjTfC999/H/f5RyNlNOIJxKavQH4vvvhivJdRF9H2CFH7fmoQWjBpFlGe+IIW/9VL9JDqiCOOGN+WNxOBj8wrQdQBMxA+/PDDWDdYQ2zEwYiaEQt15SW13mCKpqPATIcZcppppokkSd3lET0WF9wlJozIGUUzCiK4kJG+1TGdFNOQUpNhO4med1r7heQwm2NqnnzyyaOrg7acjj4x4eLPNiENrAK4IcCGcs0999xxm0f0RdSPvb/ZbVbdHH744bEc9aaJ2ddcbvYMcSSGKRH95hqx65BrNfeZx6bR+imS6OljaOMmmLrvuOOO8Nxzz8U+hT4OF+Wwww4bUqKnb7JvlOcZTBCYjDz66KO9CJ0+i1Fw6m5qJ9EbtryT/vK1116LLhf6DcpNX+GJPh3k0fdcddVV4dVXXw18E8QUWT9OOenjrR/muL9KKYk+rSwzGWEGhjyQRiPlfZR73pS4WlH7eVPiCFRbcMEFY74wsz788MNxP+9fEeXJS7vW+VpEDyFSnnnmmaeSVNrBcAFljLKanHHGGXHWgR2zxafHCIQtkhWkiDnbFLYXXnghYEXJkimmmCKOJLJGg4zybUSEEscaC77jp2Mgf0b2kOVee+3V4zXtJHpeRCfLSCU1mzLKp/PCJeDFTwPFb7rLLrtkjoTJN4oLZk4vRdWPT7PZfZsZ45+nTBZjwMisms/XP2f7rQbjeaInzUbqpyii9/E+5OHuu++Oli/2vdBucYEQFEu586TZYLx2Ej15xaJJHEUaM8EAiZgeKxP9xOWXX16JvcgLAmbdEuKBkGrWknhDP/lXeqJnBG3T3QjEsMVx/Pz5f/3rX+Ghhx6qWqX4MC1QxabEEcxj8+fraTA77rhjDCjhRSeddFL0jbLv58/XUhaKKg/vbUY80aNtQ64IWDAaYLqTBTlx3twg7HvxeDKS50POEoLPjFSzovzBkZEtUo3os9LmHCROx2tCJ5Dlu8NkbAslUe50BN1uoj/11FMrHZrlNW/r2wj3+Omkec+k54uqnzTdZo5R0o4++uhKAFtWGrQzlMIHHngg63Kvc0UTfSP1UxTR+zqqFhfSq/A5JzqR6PnWcN9gqaglvn8l9gQLIPFBqWDFQCEwiw7W0rzg2fTZTj0uPdH7yvfkakFRNKRa5ErlZo2iPQlljTbTRuE/zKKVhUbLk+at3mNP9NWeYYSD+RsXSJZcf/31lRHygw8+GKNh7T77AG0LyZoQ1Oc/Xj+1kbrcf//9e4167dmsLYslofQh1YIGiYAmONMkXWSlnUSP64ZOq17xkepZylE96RRVP/W8q557GK1RT4zgcFXkCVNbsXzUkiKJvtH6KYroWXfDovdPP/30QJ21Ip1I9M8880zl+6xVNiytKIUIswP892p9iW1RHkYYYYR4L0GKRPD3Zyk90RdBrn6E5K0CPiK4HqtAlrLgo2v7SllopUHXS/SMMPJ8qYz4zdzWaF5Sn5onWEsLbZ4YCjoJlAh82nnifXq1RkVWf6SFZYi2YOLzUS2dZoLxMFFbYJa9r9rWT9UkboQ6a0SKrJ9G3lvPveSNQDWU7CmnnDJzVgwrunkrTVa6RRJ9o/VTBNGj7Nx0002VohGV7mN7Khca2OlEomcA4wm7WnH8t1XtvvQa37WPZ0mv94fjUhE9Wr9N87DK4QNAi2O0h58VmWiiiSpmWEzuBGsg+PDxc+H7Yg61Cf5z5h8jBLmYD33dddcNI400UjxPQA+jWISAMKZ1WNQ55/ABr7LKKuzGVfWs8bK2vQWkEfyDXwkhEIzAkyLKExMs6J8negj1n//8Z0wZTP/617/GVcpsSguYM12N4C8v+MdsfX9/vp79lOh5xvsIs9IgnyzFa+se+Hu8spYVwevvJeKeoCaE+sZiYdJOorepdfauWltGd9QHUu8I16dZdP34tIveJ4iVGAOC7Wy0RuAW31q1JaSLJPpG66cIomeePDNWTMytZMfNbDuR6KvNTEnLiOJTzdqT3m/HInpDop9sW+mgKKJFH7OYhif6RosPiTO3O/XjNpIOo1A+vGYJ0ZenkffWutcTfZZZmDgGiMb89Cx4s9VWW/VIlmhZP/eVNAmuqiUoUqnSYM+gVPEbAyhNFjRn12ybkjPnmyX6NHiwnUSfN6XPypVufYdN9LQt0JTel3fcjvrJe1dR5/10NNIkKNPiR7LeUSTRN1o/RRA9JmpM1SbLL798r2h4u1bv1rcb8ljvWvde0a42j76Z6XXMjqi2hoYvm69TfPD8YFg9QuS+dwfW80yn3VOqEb2IvmfzM8Wl59nWjmoRPakzHc1PeUnn3qZmx0Y+5npyzyiPHw1ilGcBlDzHCG+llVbqkUSzpnsCBIm+N8Hig68PqWa691aBaqMVH9WN5QAlpV7xcQt0kuDbiLS7fhrJSyP3ep81LjuwzhNPCmm8Rd4z/nwr9eMHEnmBkqyBsOGGG8ZXZgX6pnWEMt3IKpK+LLbfLNH7QFWsmfaDYZYuW1attJkhWPqqDYL8PHoWzMG6WY8wwCKWBmlU+aon/U6+p1RET+NnWhdbBDOvzcMm8h6tjdGerSnPlBwC9EyYUoXflRGNzZ9nDr4t9sJcTNNyCeYwEsE3hC/UBJ8dI1p8iAj5sYAvRsE2mmUes5nmcQn4X3lj5EonXUR5LF9FbOshet7j/WUExmBe9eI7ZRbDwWXSDuF3AHAfmOBu8cud+oBKTL7UdTpPmGd9R8UxHTGEbuJNqdUULO/nbxfRU0b7BUXcFihejUpf1U+j+ap2Py44m4pJECijujzxRA+hpusi5D1n51sheu9aYeogf6n45baziJ77PSE2MgJP32XHnugb8Y2jZPg+kqnIqfipgO0iepQm63ObsWSlee5Px6Uiel8xkCsfM347P8Ly8+fr8a15sxQft/l5bW4vjbbW+vberOvXt2dUCNEg1Tp9rhdVHtJqReol+nQhm3T9fj+qYerMxhtv3Eq2qj7ryZUO1C+OROwE8+hN8n40xbeDrI4KhZKOF+E6PmII3wsLl6B8mlSr81aIhMVwUJ5MslwWdi1v25f1k5cHzjMViujodA51+gyuMkZ05qdP6zm9HyuJrTSYWmfSe7OOW6kfllKeddZZY7JM6yWQN5V6fivC1xGDFJTaVoTgWQY2SCOrxtH/bb311vG5vFU8fYBo1vcTHx74zyswjYzovWUBZZ1pcwysyiClJXpPrvysJdOuEG/WTMkn3pD88/40Om98yUxpoTHSqTASMBNb8mjl0JMEo3lWrkKaVRZaKU8lU03u1Ev0JO9HWCmZ06HQsZgw+sqbigcZs4gNJjwsBV5YqpgIe1PA/DX2sYjYDwdxnI7EOednZjCfFreDH61PPPHEMSrX4g68ssbzJl6hoH3QEZsw0mQRG/vhG863i+hJ248aiW2AzLAapQI+mK7TUVhR9ZO+r9FjfhWSb5bfl2fElhVZjpWOeexmYeMdtXzWfkGhPHNztby2QvR+8S3cSUy19YGDjI59XEveiJ6FolgwyoRy0NZTixT9FfWPUpH3nZCGzxftH8tQmpa9y2/9QmCcT5Us1rkgnsCUsHYRPco2FlcLBsaySrBwqnCTR+6l3ROLlaVocU9/ktISvTd9edOwaco0tlojcT+KxsxPw0CIlLcV2PJWo/KNhBGjrbzmlQXMo0iWads/z34R5UnTbOa4EaJnFUJvsk9/EMOvakdeUAaYDscHyigOczidmUWQZ00/NEWMjxm3C6N1Vr/D/QKJEbRkHUyqbFj5mZvtP3ZGRyzFyyIsPM+vZhnJ026Yp87Smql40yHXMB/S+ZJ/XDQ2grTn2kn0rBoG2VvZyTf4EFdAewMfpo1i6sxzNRRRP1bWZrdG9PY8I3t+xIcZKZAQJIILzOqH+2hDjJqriSc17iNdAmBthT2IF/zypBWin3TSSWOgraWNcskceEaflMXP1uGePKLnmh+FcwxB33fffTEQkXXwse7wHUBsKMl+rXvu9+IHR5wnPywPy/stWI1lpPmOvNBP4hO3Of3UC0ovMQMsEU5/ae2Q59pF9KSduupwXdFH06cwSCNYl1gu8kWeqmFLev1FSkv0nlxXXnnl2JnREBshV+9X8j4fb4ZKySttGF5ZwDeMlox4M26jykKz5Unz1sxxI0SfdgB0psQ2mGCWRQljxFyPVCP6Ws/T+bDMa9YSuDzrV0qsllaeT5VnfEBeVhosrELnYn7kdhI970eR5VftqIdqkkf0RdRPtffWcy0l+lrPYDpGEas1EsUKAGlBgFlSi4xaIXre5y0uWe9HYYWQkGpkhIWIIFBThrPSsnO1iJ77UmXVnrVtXn/n/fR2r9/68tTCtlnTvb3Pz6Sxc3nbatjmPdOJ50tL9Dan0pOr7zTqIVeilS061K+ehFZMsB4NtpZVwM8E8HOaCQhcdNFFY5vJ+3h8gyqiPD69Zvf9B+1/OyAvPczgPhgMsk0XsEELZ+RvJrc0LTRxTLeY9tNneW7ppZeuRNumz3LMM7hMak3TIR3q3I8OLT1GBrh/7EeR7Hy6ZeTOaNGPYLiHdsh5OlIz39dL9NWUi/T96TFBhFgr8pQpRpOMxlKXiE+nlfrx6TSzj1KEcsxaFmYVy0oH9wQEQRupRfL2PGRP2nzjWJB8ndUiI0/0zdQP1h2W9WV078VGw7RZXH5IPWTEN0acS9Y3RFn4oS7ynOX68O9nn0EIyhJ4e0y4Rr/Ft5glqZWEe3g31hcskrbATy1sPdHjssubUpuVBzuHtYrf3LBvzc7bFpzBmLbPAKK/S2mJvr9XXBnzj6mRIKXxxhsvmgEx0fIx1jNtCELgWXxudFAQ69sDfPqMJOgoGxF8vZgxMX/zfszvWab6vDTpxHEHTDLJJHENbebzDuqgIEb1mC3pACE48AEbP1skrzx2vpX6sTRa2VLHuByoFzpwykHbwErDtl6CbyUPRT8L0fMDQpSFlRypk1bE6og2jMJK+yXdvmp/KJZ8O/zmBYo1i4CZ2b+VcjX7LBYb4k1w8dB+cDtYv9Af20seDiL6PGR0XggIASEgBIRAFyAgou+CSlQRhIAQEAJCQAjkISCiz0NG54WAEBACQkAIdAECIvouqEQVQQgIASEgBIRAHgIi+jxk/l97dUgEAACAQKx/axJQ4G8WxxDICRAgQIBAQMDRB0ZUgQABAgQIPAFH/2TkBAgQIEAgIODoAyOqQIAAAQIEnoCjfzJyAgQIECAQEBhVGHfBCvtWUwAAAABJRU5ErkJggg==)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=b9254770-e766-4aa1-be59-5ea50a980347).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951831126"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081032722", "body": ""}
{"comment": {"body": "test2\n\n\n\n![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/0ba8c4da-d19a-48f5-aba5-97411cf83884)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=c9ef07ac-c383-4e0d-8ea8-5bde04501c76).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951831244"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081149241", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/ee13e571-42af-47c8-ac63-3f07800a1f78)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=ca2084b5-4c06-4e12-b00c-22aeb1784907).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951913407"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081149358", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/474dd114-9180-4727-b856-caea967857d8)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=a862fbea-9abb-4a8a-ad5d-27533555ae62).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951913486"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081161242", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/343127af-e156-473c-bd5f-a867b107f69d)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=0763292f-e2a6-4f00-a41e-ee16ab672b54).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951921868"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081161336", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/973cf0db-adeb-42d5-a889-f728173927ac)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=c5805e9a-1404-4b73-acaa-e5668248eff0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951921925"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081177188", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/40ac56b3-1f21-4815-b1d1-6e0e066f51c4)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=080e408d-0d12-4f9e-b48c-024f8ecbb220).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951933099"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081177263", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/2839a01e-37c8-440a-9c61-ffbbea2f8084)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=c51d19fa-5883-40e3-b107-15439b39503d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951933156"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081187708", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d756a685-0cd5-4df8-917f-cf55ca0d9534?message=7aebc7ba-6221-4a4f-bd2b-0b5139cbb65f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951940521"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081245581", "body": ""}
{"comment": {"body": "![](data:image/png;base64,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)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=0b6eb8f9-4e1f-4aab-a1e6-d5fa326512e8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951980937"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1081245654", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/f12ee4d9-99b2-4d65-b7d1-548ddfd93795)\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/e99d12e9-c9ef-4ab2-a11b-b7dcceeafd0b?message=c8fb22c6-9b32-4675-a17f-17efb5a59a6f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r951980984"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1082607958", "body": ""}
{"comment": {"body": "ping\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d756a685-0cd5-4df8-917f-cf55ca0d9534?message=9f6c8aaf-51ae-48df-a9b7-b3a07988d020).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r952946815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1082732947", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d756a685-0cd5-4df8-917f-cf55ca0d9534?message=d42dd0b0-15b8-4810-ae39-b4d2f336a9f0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r953035674"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1087541890", "body": ""}
{"comment": {"body": "@kaych hello\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=1d9d380e-11b1-4bfa-b2a3-546ae56dd4b0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r956461834"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1087542284", "body": ""}
{"comment": {"body": "@kaych\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a760bc11-b2c6-48a6-a72c-f5c4867bfc1d?message=0f8383ea-f4a9-4c90-a1e2-842745106680).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r956462145"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1087560839", "body": ""}
{"comment": {"body": "@UnblockedDemo\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a760bc11-b2c6-48a6-a72c-f5c4867bfc1d?message=ae8881cc-cbf3-4252-bbe9-4dc43fe22836).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r956476185"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1087561885", "body": ""}
{"comment": {"body": "@kaych\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a760bc11-b2c6-48a6-a72c-f5c4867bfc1d?message=80760120-e838-4a4a-acd6-2f51c97bbbf0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r956477060"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1094018168", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=3c972d8f-cec6-42ce-a863-9844563f6e35).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r961074067"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1094018704", "body": ""}
{"comment": {"body": "test2\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=22dcd6e0-83b2-430e-81f5-f6c6cedbc019).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r961074411"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1094018797", "body": ""}
{"comment": {"body": "test3\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=3dd85f7a-b8fa-4261-938e-1dbcbcf1d872).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r961074471"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#pullrequestreview-1094022340", "body": ""}
{"comment": {"body": "test4\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c6580f64-7d4a-4ce1-9bac-bb3c6011ccfc?message=0c0355b0-2701-49ee-ab6a-3038fb84d5d6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2190#discussion_r961076818"}}
{"title": "Enable Gradle caching", "number": 2191, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2191", "body": "All PRBs will be using caches as read-only\nMain branch builds will update incremental caches\n\nTested it and on clean builds with no cache we spend about 3 more minutes. Once caches have been populated, build times drop from 9+ mins to under 3 minutes."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2191#pullrequestreview-1030569381", "body": ""}
{"title": "Fix section header padding", "number": 2192, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2192"}
{"title": "update", "number": 2193, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2193"}
{"title": "Update", "number": 2194, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2194", "body": "Bad unique index"}
{"title": "Make descriptions optional on web extension thread creation", "number": 2195, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2195", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2195#pullrequestreview-1030507064", "body": ""}
{"comment": {"body": "missing dependency", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2195#discussion_r915120128"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2195#pullrequestreview-1030533115", "body": ""}
{"title": "Provide product version/number headers in TS clients", "number": 2196, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2196", "body": "Fixes UNB-397. This looks like it regressed as people changed the CI scripts.\n\nUpdate CI scripts to pass down product number/version to build scripts\nUpdate prod build scripts to fail if product number/version are not supplied\nAdd buildCfg logs back so we get some debugging help, but make them debug logs\nDisable writing out debug logs on the console for jest tests"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2196#pullrequestreview-1030627264", "body": ""}
{"title": "Don't ingest threads on the left side", "number": 2197, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2197", "body": "I think we said we wouldn't do this, so not sure why I overlooked this.\nThis is an example of a thread created from a left side comment:\nhttps://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/85adeaa7-80b8-4084-9c75-71f02011aaa2\nThis is the comment:\nhttps://github.com/NextChapterSoftware/unblocked/pull/2188/files#r915117239"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2197#pullrequestreview-1030816217", "body": ""}
{"title": "Web Extension Proxy for storage", "number": 2198, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2198", "body": "Utilize promise forward proxy to setup storage transport layer."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2198#pullrequestreview-1030715633", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2198#pullrequestreview-1030717986", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2198#pullrequestreview-1030718080", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2198#pullrequestreview-1030718592", "body": "One important detail to fix with the register calls, other then that this looks good"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2198#pullrequestreview-1031842258", "body": ""}
{"comment": {"body": "Was running into a few build errors for web client stemming from `TS2339: Property 'entries' does not exist on type 'Headers'`\r\n\r\nAccording to the types, the headersInit should take in type Headers as well?\r\n\r\n`type HeadersInit = string[][] | Record<string, string> | Headers;`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2198#discussion_r916056867"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2198#pullrequestreview-1031849572", "body": ""}
{"comment": {"body": "I don't know if this will actually work though... \r\nI'll be testing this out in another branch where I'm actually using the fetch proxy. Can address any comments there.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2198#discussion_r916062332"}}
{"title": "Adds intercom hash to person", "number": 2199, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2199", "body": "Doesn't include web client implementation (will add in separate PR)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2199#pullrequestreview-1030632896", "body": "Wonder what benefit this really adds"}
{"comment": {"body": "Interesting. So we expose the hash in the api. An attacker would need both the id and hash to spoof a user. But could get both of these from a client attack or api attack.\r\n\r\nSo this prevents a remote attack where the attacker somehow knows the personID but not the hash. How would an attacker ever get the person ID? I think this spoofing protection is really just to defend against spoofing users using _public_ information (eg: email).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2199#discussion_r915209655"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2199#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Ah that's right! I thought we were sending back personIds for teammembers but these never get exposed. In that case it's true that personId is probably not obtainable without access to the user account or executing a MiM attack. \n\n\n\nHappy to close or defer this?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c85ec693-28f0-40ef-90d8-3b6348031a24?message=969b85a7-2f76-403a-bb7b-8770b9035238).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2199#discussion_r915214214"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2199#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Up to you. It's not harmful to do this, just very limited value as is.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c85ec693-28f0-40ef-90d8-3b6348031a24?message=231116f3-ce8a-4363-b46c-3bf744f82a1f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2199#discussion_r915216648"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2199#pullrequestreview-1030645256", "body": ""}
{"comment": {"body": "Matt just pointed out that it's possible to re-engage a conversation with just the email. In that case we want identity verification turned on so we can decide whether or not to \"trust\" the individual on the other end", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2199#discussion_r915218248"}}
{"title": "Add Tailwind listing & sorting", "number": 22, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/22", "body": "Add an tailwind eslint plugin that helps with both sorting & enforces only using tailwind classnames (can remove this rule)\nWill check eslint during CI.\nVSCode should also automatically handle the sort with eslint on file save."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/22#pullrequestreview-845118916", "body": ""}
{"comment": {"body": "Might be worth making the tool/action order in the commands consistent -- 'lint-fix' vs 'fix-pretty' vs 'pretty-fix' ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/22#discussion_r779176225"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/22#pullrequestreview-845119402", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/22#pullrequestreview-845177771", "body": ""}
{"comment": {"body": "Changed lint-fix to fix-lint which is consistent with everything else.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/22#discussion_r779219897"}}
{"title": "Fix VSCode package step", "number": 2200, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2200", "body": "Fix error in VSCode package step\nPropagate product version/number values through to dashboard build"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2200#pullrequestreview-1030701405", "body": ""}
{"title": "Fix thread recommendation stability and performance", "number": 2201, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2201", "body": "The scm-service was failing to recommended newly created threads because the\nthread did not exist yet. This occurred because the recommendation was run from\nwithin the same transaction as the thread creation.\nThis change allows the thread to be fully created first, then applies a\npotential recommendation.\nERROR: insert or update on table \"threadrankmodel\" violates foreign key constraint \"fk_threadrankmodel_thread__id\"\nDetail: Key (thread)=(7c70a1e4-ae35-467e-a9cd-fd1ab7db07bf) is not present in table \"threadmodel\".\nAlso, we run recommendation rebuild only for members who have Unblocked accounts.\nFixes:\n- \n- "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2201#pullrequestreview-**********", "body": ""}
{"title": "Update email templates", "number": 2202, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2202"}
{"title": "Revert \"Enable Gradle caching\"", "number": 2203, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2203", "body": "Reverts NextChapterSoftware/unblocked#2191"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2203#pullrequestreview-**********", "body": ""}
{"title": "Fix web extension dev builds", "number": 2204, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2204", "body": "The error checking was done at the root, which meant importing the file would immediately fail."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2204#pullrequestreview-**********", "body": ""}
{"title": "Reformat secrets", "number": 2205, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2205", "body": "Reviewing requires checking out this branch and decrypting"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2205#pullrequestreview-1030754581", "body": ""}
{"title": "Strip markdown from titles for PR ingested threads", "number": 2206, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2206", "body": "Fixes UNB-116"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2206#pullrequestreview-1030836493", "body": ""}
{"title": "update", "number": 2207, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2207"}
{"title": "Revert \"Update email templates (#2202)\"", "number": 2208, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2208", "body": "This reverts commit 0b8e81ea2323cf175c21f2e54e4672cb9c29e2dc."}
{"title": "Add identity hash to intercom client", "number": 2209, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2209", "body": "Resolves UNB-259"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2209#pullrequestreview-1030781335", "body": ""}
{"title": "Add basic api for email preferences", "number": 2210, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210", "body": "Implementation to follow"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#pullrequestreview-1030794778", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#pullrequestreview-1030796101", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#pullrequestreview-1030798014", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#pullrequestreview-1030802207", "body": ""}
{"comment": {"body": "So this implies we'd do multiple fetches/commits if we have any non-email preferences?  It's fine as long as there aren't any preferences that cross boundaries -- this is what killed us in Skywagon, workspaces had many elements that referred to or depended on each other, so we couldn't commit different bits separately in any way that made sense.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#discussion_r915320867"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#pullrequestreview-1030802615", "body": ""}
{"comment": {"body": "Correct.\r\n@richiebres brought up your exact scenario, and this is an attempt to take our learning from the skywagon model. Not having cross-boundaries and granular/contained settings control.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#discussion_r915321189"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#pullrequestreview-1030804577", "body": ""}
{"comment": {"body": "It's something you can only avoid until you can't, unfortunately.  There was no way to separate everything in workspaces because of how workspaces work, and the product experience we wanted to have.\r\nNot an issue here really, if we hit something like that we can just figure out what to do then.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#discussion_r915322780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#pullrequestreview-1030806969", "body": ""}
{"comment": {"body": "I hope you're wrong, but yeah, who knows what requirements we have down the line.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2210#discussion_r915324626"}}
{"title": "Fallback to searching files by previous_filename", "number": 2211, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2211", "body": "Handles creating threads on renamed files"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2211#pullrequestreview-**********", "body": "sweet"}
{"title": "Show VSCode badges on startup", "number": 2212, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2212", "body": "Fixes UNB-319.\nThis is a mess, details are in the Linear issue: \nThe solution I have here is the least intrusive one that seemed to work:\n* On extension startup, determine which sidebar state we should start up in (unauthed, uninstalled, installed), and go to that state directly, instead of relying on the chain of sidebar provider logic.\n* If we are starting directly in the installed state, open up that sidebar so the badge is displayed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2212#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Without this change the badge would show, then disappear, then show again.  The problem was that as VSCode progressively loaded more repos in the workspace, we would re-fetch threads, which would cause the stream to go back into the loading state.\r\n\r\nI'm not sure if this is ideal or not, it likely causes some thrashing on startup.  Ideally RepoStore wouldn't issue any values until all the repos in the workspace are loaded?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2212#discussion_r915327367"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2212#pullrequestreview-**********", "body": ""}
{"comment": {"body": "The question is *when* are all the repos loaded? I haven't quite figured out if there's a signal for that...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2212#discussion_r916019471"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2212#pullrequestreview-1031790500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2212#pullrequestreview-1031791606", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2212#pullrequestreview-1048193147", "body": ""}
{"title": "update", "number": 2213, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2213"}
{"title": "Add some unit tests to validate bug was fixed", "number": 2214, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2214", "body": "There are a couple of old sourcemarks that have empty snippets. Just adding them as unit tests to show that the bug has been fixed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2214#pullrequestreview-1030832681", "body": ""}
{"title": "Add tests for MessageUtils", "number": 2215, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2215"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2215#pullrequestreview-1031792048", "body": ""}
{"title": "Add links to show GitHub API responses", "number": 2216, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2216", "body": "To help with debugging PR ingestion"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2216#pullrequestreview-1030831942", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2216#pullrequestreview-1030835745", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2216#pullrequestreview-1030850589", "body": ""}
{"title": "Remove legacy sourcemark kotlin native app", "number": 2217, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2217"}
{"comment": {"body": "wtf. I didn't do this\r\n\r\n<img width=\"564\" alt=\"Screen Shot 2022-07-06 at 18 25 26\" src=\"https://user-images.githubusercontent.com/1798345/177670425-7966e19c-e38c-4796-a92e-7c93c203bac7.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2217#issuecomment-1176939596"}}
{"comment": {"body": "> wtf. I didn't do this\n> \n> \n> \n> <img width=\"564\" alt=\"Screen Shot 2022-07-06 at 18 25 26\" src=\"https://user-images.githubusercontent.com/1798345/177670425-7966e19c-e38c-4796-a92e-7c93c203bac7.png\">\n> \n> \n\nActivity audit time", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2217#issuecomment-1176997897"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2217#pullrequestreview-1030864437", "body": ""}
{"title": "[EXPERIMENT] Experiment with Rust for sourcemarkapp git queries", "number": 2218, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2218", "body": "highly performant and it has direct bindings into git c libraries!\nNot necessary to do process calls for quite a few things\n\nAlso, good support for grpc.\nOtherwise, consider golang as it has similar support for direct bindings to git libraries. (not sure how well this will work on windows...)"}
{"comment": {"body": "Thanks, Rashin!\r\n\r\nWe use low-level Git sub-commands that are not in `libgit`, and unlikely to be in any other library since there wouldn't be much general utility for this stuff.\r\n\r\nMade a list here of what we depend on:\r\nhttps://www.notion.so/nextchaptersoftware/Git-Command-Set-068a25ad21c24698915bdfca4788f321\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2218#issuecomment-1176988439"}}
{"title": "Paginate getSourceMarks API operation", "number": 2219, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219", "body": "API service component of this change.\n  \n\n\nClient changes (vscode) will follow."}
{"comment": {"body": "Slack discussion:\r\nhttps://chapter2global.slack.com/archives/C036YH3QF7T/p1657166129770189", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#issuecomment-1177078628"}}
{"comment": {"body": "Re-worked to use only the `limit`. There is no \"cursor\", and no \"after\" parameter. Instead, pass the lastModified from a previous page to the next call as if-modified-since.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#issuecomment-1180012866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#pullrequestreview-1031911350", "body": ""}
{"comment": {"body": "Nice. I guess order doesn't matter since clients will want to suck down all sourcemarks.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#discussion_r916105329"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#pullrequestreview-1031969064", "body": ""}
{"comment": {"body": "Correct, the order is irrelevant.\r\n\r\nChatted with Matt, and we're going to change it to modifiedAt instead so that the order is consistent with pusher.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#discussion_r916145857"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#pullrequestreview-1031973631", "body": ""}
{"comment": {"body": "Cool. Yeah I guess Sourcemark models aren't edited at all so we don't need to worry about ordering issues as a result of edits occuring while requests are happening \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4b938e6a-3470-4db3-a49f-65fa30d863fd?message=24b79a2e-79ef-430f-9b17-c2726cebbea4).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#discussion_r916149037"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#pullrequestreview-1033842121", "body": ""}
{"comment": {"body": "This is effectively to avoid API migration. Setting the limit to a massive value only until the clients have caught up and have the ability to handle paginated responses. New clients can lower this to reasonable values.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2219#discussion_r917574866"}}
{"title": "Static Site and RDS access over VPN", "number": 222, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/222", "body": "Added support for VPN access to RDS from VPN per env. Currently we only allow it for DEV\nA whole bunch of code cleanup and pretty lint + indent stuff\nAdded a new stack to deploy an S3 hosted static site with CloudFront and WAF\nCreated a new config object for things that need to be deployed in AWS Standard region (us-east-1) life WAF and CF\nAdded config json for standard region in Dev\n\nA few more notes around Static Site:\n- deploybot has access  S3 static site bucket for get, write, delete and list object operations\n- deploybot has access  CloudFront  to list all distributions (needed to get Distro ID) and also to create, list invalidations for file paths on S3 bucket\n- IAM role pattern for static site actions arn:aws:iam::AWS_ACCOUNT_ID_HERE:role/S3StaticSiteDeployerRole\n- We will add the prod configuration json in the next PR. \n- To simplify CI/CD story we can provide CloudFront ID and bucket names as config parameters instead of lookup (they won't change for the lifetime of an env)\n- WAF is currently allowing access only from our office network IP address.\nAll these changes have been deployed to Dev"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/222#pullrequestreview-*********", "body": ""}
{"title": "Enforce getSourceMarks API limit", "number": 2220, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2220", "body": "Do not merge until all (vscode) clients are using the limit and cursor."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2220#pullrequestreview-**********", "body": ""}
{"title": "Consolidate original/latest point lookup", "number": 2221, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2221", "body": "Single call returns latest and original points for a sourcemark\nAlso returns a reason when the latest point could not be found -- but this bit is rubbish right now. Can be improved in follow up."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2221#pullrequestreview-1031004808", "body": ""}
{"comment": {"body": "`bundle.reason` could be used to generate different error flows. Right now a string, but will be a more helpful structure.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2221#discussion_r915472384"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2221#pullrequestreview-1031933363", "body": ""}
{"comment": {"body": "Sounds good -- for now a string is fine, an enum will definitely be useful in the future if we want to display different types of UIs with different actions (check out branch, etc)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2221#discussion_r916120587"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2221#pullrequestreview-1031937709", "body": ""}
{"title": "fix the difference between CDK and Cloudformation", "number": 2222, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2222", "body": "Dev DB is upgraded to 14.3\nProd DB is kept back on 13.4\nAdded configs so we could specify versions which are not yet added to built-in enums provided by CDK \nDeployed to Dev \nDid a diff with prod and there was no difference."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2222#pullrequestreview-1031935302", "body": ""}
{"title": "update", "number": 2223, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2223"}
{"title": "update postgres", "number": 2224, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2224"}
{"title": "Move tests around", "number": 2225, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2225"}
{"title": "Web Extension Refactor", "number": 2226, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226", "body": "Moves all logic & statefulness out of background scripts into content scripts.\nLogic should not have changed much. Tried to keep a similar interface for now but there's room to simplify things in the future.\nAlso fixes unb-130"}
{"comment": {"body": "\ud83c\udf89 nice work!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#issuecomment-1183633598"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1034873599", "body": ""}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r918308685"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1037733335", "body": ""}
{"comment": {"body": "Can we make sure we make a follow-on task to handle this properly?  I don't know exactly what we'll do, but right now this means any network failures will result in *no* response back to the web extension, ie the fetch request will hang indefinitely.  THat's not great.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r920338757"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1037737908", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1037747179", "body": ""}
{"comment": {"body": "https://linear.app/unblocked/issue/UNB-429/fetchproxyservice-error-handling", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r920348588"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1037753491", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1037768957", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1037849012", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1037904487", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1037912588", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1037920441", "body": ""}
{"comment": {"body": "This is a test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/2fd082c6-8a30-4257-95ec-c8805258f193/thread/8c59ed6a-f06c-4e96-91b6-201d8bf65bca?message=11ce0b7d-3509-4643-bf08-cc8606a8530c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r920469023"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346747451", "body": ""}
{"comment": {"body": "Test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140716675"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346748907", "body": ""}
{"comment": {"body": "abc", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140718238"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346757368", "body": ""}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140727327"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346758304", "body": ""}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140728400"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346760464", "body": ""}
{"comment": {"body": "test123", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140730688"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346766278", "body": ""}
{"comment": {"body": "abcdef", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140737005"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346766440", "body": ""}
{"comment": {"body": "gaga\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140737147"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346770590", "body": ""}
{"comment": {"body": "bbaa", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140741740"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346772195", "body": ""}
{"comment": {"body": "ssff", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140743700"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346772263", "body": ""}
{"comment": {"body": "ffsfs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140743787"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346817827", "body": ""}
{"comment": {"body": "testtest", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140798546"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346817945", "body": ""}
{"comment": {"body": "@jeff wow", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140798740"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346818052", "body": ""}
{"comment": {"body": "@jeffrey-ng 123", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140798934"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#pullrequestreview-1346821899", "body": ""}
{"comment": {"body": "@jeffrey-ng 123", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2226#discussion_r1140805799"}}
{"title": "Workaround LazyVStack height bug", "number": 2227, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2227", "body": "Definitely a least (most?) proud moment.\nBug is that LazyVStack incorrectly sizes itself for 1 frame during a multi-layout pass. This throws the contentHeight hack for a loop because it has to ignore the incorrect size. Only way to do this without introducing a ton more complexity is to take the latest value within some minimum timing threshold."}
{"comment": {"body": "> We should get this in for now but I think we're really starting to fight against how SwiftUI works or its limitations.\r\n> \r\n> An option is to rewrite this with appkit if SwiftUI truly doesn't support this use case well enough.\r\n\r\nThere's a pain threshold that we're getting close to, but not quite reached. I'm willing to live with a couple small hacks like this for the other benefits SwiftUI is giving us (implementation speed mainly), so long as the time spent working around limitations doesn't exceed the time to implement this in AppKit. But we're getting close to that...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2227#issuecomment-1178243403"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2227#pullrequestreview-1032134928", "body": "We should get this in for now but I think we're really starting to fight against how SwiftUI works or its limitations.\nAn option is to rewrite this with appkit if SwiftUI truly doesn't support this use case well enough."}
{"title": "AddPersonEmails", "number": 2228, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2228"}
{"title": "Don't track metrics for internal teams", "number": 2229, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229", "body": "I know we said we wouldn't do this but the numbers are wonky:\n\nSome days have total users less than MAU and/or WAU because Rashin recreated his person object.\nSome days have total teams less than MAT and WAT because of the Unblocked demo team that is recreated."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032219186", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032222839", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032223519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032224419", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032225966", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032226427", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032226890", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032226964", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032227061", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032227708", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032228239", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032246411", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2229#pullrequestreview-1032248180", "body": "thanks dave!"}
{"title": "Get team through referenced objects in ModelBuilders", "number": 223, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/223", "body": "For a lot of the models we create in ModelBuilders, we can grab the team through the referenced objects. For example, if a message object needs the team object, it can grab it through the thread it references. This way there is no chance that the thread and message can refer to different teams when setting up tests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/223#pullrequestreview-871039912", "body": "Not sure if this is worth it TBH\n1. doesn't completely address the issue you want to solve (see inline comments)\n2. prevents flexibility in specifying a team"}
{"comment": {"body": "Still has same problem, if client passes `message` and `repo` with different teams.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/223#discussion_r797967460"}}
{"comment": {"body": "Still has same problem, if client passes `thread` and `member` with different teams.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/223#discussion_r797967707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/223#pullrequestreview-871040989", "body": ""}
{"title": "Characterize source point resolution scenarios", "number": 2230, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2230", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2230#pullrequestreview-1032231855", "body": ""}
{"comment": {"body": "will do in follow-up. for now, just assuming all these 4 cases are the same `notInRepo` ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2230#discussion_r916331517"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2230#pullrequestreview-1032238677", "body": ""}
{"title": "Add custom template to fix storybook", "number": 2231, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2231", "body": "Spent hours digging into what was causing the invalid JS (yet valid since it worked outside Storybook???) but wasn't able to figure it out.\nGoing around the problem and adding a custom template for runtime.\nVSCode storybook with message editor (which would definitely fail as it pulls in base api)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2231#pullrequestreview-1032247775", "body": "lol"}
{"title": "Additional logging", "number": 2232, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2232", "body": "Additional logging in places that could cause spinner issues"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2232#pullrequestreview-1032275086", "body": ""}
{"title": "SourceMark resolution failure UIs", "number": 2233, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233", "body": "The typings here are getting out of control, this was an effort to get this UI in as quickly as possible.  I will clean this up at some point later."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#pullrequestreview-1032272582", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#pullrequestreview-1032273405", "body": ""}
{"comment": {"body": "I think we can probably get rid of this wrapping type, but it was causing a lot of extra knock-on work so I'm going to do this later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#discussion_r916363663"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#pullrequestreview-1032274324", "body": ""}
{"comment": {"body": "@richiebres looking for feedback on any of these error messages.  I'm not sure how to characterize some of these \"default\" errors in a way that can help users.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#discussion_r916364442"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#pullrequestreview-1032274573", "body": ""}
{"comment": {"body": "(Note that the UIs here are very minimal, just to get something in for tonight.  We'd want to format branch names, commits, etc in a nicer way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#discussion_r916364616"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#pullrequestreview-1032275260", "body": ""}
{"comment": {"body": "@dennispi @benedict-jw any thoughts on these error messages?  I'm 100% brain-fogged and can't tell what's good or not at this point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#discussion_r916365147"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#pullrequestreview-1032277621", "body": ""}
{"comment": {"body": "I'll edit.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#discussion_r916367061"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#pullrequestreview-1032579088", "body": ""}
{"comment": {"body": "yes do please, this is hard to follow.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#discussion_r916590048"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2233#pullrequestreview-1032587398", "body": ""}
{"title": "UseEmailSettings", "number": 2234, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2234"}
{"title": "cleanup queries", "number": 2235, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2235"}
{"title": "Improved sourcemark characterization", "number": 2236, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2236", "body": "More scenarios covered.\n"}
{"title": "Update cdk to match DB versions deployed to RDS", "number": 2237, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2237", "body": "DB upgrade was done without any problems. Updated CDK version to match current DB version and deployed it locally to make sure everything is working as expected. This should be a noop change."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2237#pullrequestreview-1032478132", "body": ""}
{"title": "WIP: Adding secondary nodegroups to allow for zero downtime upgrades", "number": 2238, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2238"}
{"title": "Admin auth", "number": 2239, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239", "body": "Styling needs improvement and \"logout\" button is missing. Logout button just needs to clear the \"admin-session\" cookie.\nAlso no strict origin policy is applied to the server cookie so it might be possible to hijack it. Will add policy in a follow-up.\nIt might also be nice to yank out the user email and display it to show the user which account they're logged in as. Trivial to add"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-**********", "body": "cool. nice work"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Could probably figure out how to save the origin url before the /login redirect, but that's sugar on the cake", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#discussion_r916988033"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-**********", "body": ""}
{"comment": {"body": "@richiebres this is where we would create a permissions list for each person by either their email or google id (just using email here for display convenience). We might want to store this in the DB.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#discussion_r916989347"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-1033177836", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-1034685140", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-1034685610", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-1034751896", "body": ""}
{"comment": {"body": "@mahdi-torabi can you please let me know if this is correct or if we need more changes?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#discussion_r918222923"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-1034770784", "body": ""}
{"comment": {"body": "Is there a `name:` field in your new secret yaml file ? If yes have you set. it to `unblocked-admin-service-secrets-env` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#discussion_r918236177"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-1034773225", "body": "Looks good to me."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#pullrequestreview-1034777167", "body": ""}
{"comment": {"body": "Yup!\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ee5b86fe-3ec5-4f8a-9a34-67ebee3c1612?message=a1f59dd5-b7f3-4564-af5f-2a0e6ba4028d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2239#discussion_r918240648"}}
{"title": "Deploy static site to prod", "number": 224, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/224", "body": "Added config json for prod static site. Standard region in prod account is bootstrapped and all changes for static site have been deployed. \n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/224#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/224#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/224#pullrequestreview-*********", "body": ""}
{"title": "Email prefs in admin console", "number": 2240, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2240", "body": ""}
{"title": "Contact support styling change", "number": 2241, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2241", "body": "From:\n\nTo:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2241#pullrequestreview-**********", "body": ""}
{"title": "Update templates", "number": 2242, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2242"}
{"title": "Scroll to line with padding", "number": 2243, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243", "body": "Scroll page so that item is at 30% of vh whenever possible.\nIgnoring GH's line navigation with #L15\nGH's basic behaviour jumped directly to the line at the top of page.\nIf we utilized GH's line hash state, and scrolled subsequently, we would have the following:\n\nTherefore, wrote our own scrolling system for line numbers. \n\nURL is cleaned up and should look identical to standard GH hash URL\nNew URL: \nStandard GH URL:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#pullrequestreview-1033433962", "body": ""}
{"comment": {"body": "Want to force our highlighting style over GH's when they overlap.\r\n\r\n<img width=\"843\" alt=\"CleanShot 2022-07-08 at 15 51 23@2x\" src=\"https://user-images.githubusercontent.com/1553313/178079548-fd25990a-3529-46d9-8a2a-72cc5434b916.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#discussion_r917176717"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#pullrequestreview-1034718234", "body": ""}
{"comment": {"body": "It looks like this results in a new navigation (ie a new location pushed on the stack) -- is that what we intend?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#discussion_r918199884"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#pullrequestreview-1034736638", "body": ""}
{"comment": {"body": "\ud83d\ude2e this whole thing is wild.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#discussion_r918212170"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#pullrequestreview-1034739702", "body": ""}
{"comment": {"body": "We want to rewrite the URL without navigation. But I guess we don't necessarily need that rewrite to be on the stack.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#discussion_r918214394"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#pullrequestreview-1034742209", "body": ""}
{"comment": {"body": "Yeah exactly. It means navigating here results in multiple navigations, which means hitting back doesn't do what you expect it to do.\r\n\r\nThe history / navigation API should have a way of replacing the entry instead of adding to the stack.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#discussion_r918216140"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#pullrequestreview-1034746909", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2243#pullrequestreview-1034747659", "body": ""}
{"title": "add opentelemetry tracing for redis", "number": 2244, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2244", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2244#pullrequestreview-1033467613", "body": ""}
{"comment": {"body": "Intentional?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2244#discussion_r917206285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2244#pullrequestreview-1038063121", "body": ""}
{"comment": {"body": "Yes\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/2fd082c6-8a30-4257-95ec-c8805258f193/thread/9f52c458-bac8-40e9-943a-d0624fe2038e?message=1cf0cacd-ac78-4df5-98ce-3072cbff00c7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2244#discussion_r920574504"}}
{"title": "update", "number": 2245, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2245"}
{"title": "Move away from deprecated Locations API in Ktors", "number": 2246, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2246", "body": "Ktors team have introduced a breaking change that we have to take care of otherwise next major upgrade we're going to have problems..\nAlso, there's some neat things we can do with resources (i.e. nested paths  nested classes etc.)\nAnyways, here you go."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2246#pullrequestreview-1033564976", "body": ""}
{"title": "VScode fetch sourcemarks using paging", "number": 2247, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247", "body": "Add paged API request helper, and hook up to VSCode' SourceMark store.\n\nVSCode's SourceMark store was using the old DataAPIStream, I moved it over to the new APIStream code\nThe pager can set a limit on the number of pages to fetch at once, so that we don't starve the other stores of the ability to fetch, if we're fetching a ton of SourceMarks.  Right now the SM store is fetching 5 pages per channel poll, which means it will do 5 requests, then wait a second.  We can change this if needed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033573208", "body": ""}
{"comment": {"body": "This mock API was a bit fragile -- with this change upserted pets will always be inserted in order correctly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#discussion_r917313797"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033573372", "body": ""}
{"comment": {"body": "FYI just adding a limit to the request below, when the API supports it, should be enough to get paging to work.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#discussion_r917314063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033573414", "body": ""}
{"comment": {"body": "NB `this.stream` now filters out `loading` states, as it seemed that all usages below were filtering them out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#discussion_r917314127"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033826590", "body": ""}
{"comment": {"body": "So we could potentially make multiple requests with the same ifModifiedSince value?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#discussion_r917564071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033827346", "body": ""}
{"comment": {"body": "If the response comes back with values but response.ifModifiedSince is undefined, we will run the previous request again?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#discussion_r917564675"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033827884", "body": ""}
{"comment": {"body": "Just to be clear, if a single request of the poller fails, we will fail the entire paged request.\r\n\r\nI think this is okay though since we should have retries for unexpected failures so failing shouldn't occur unexpectedly...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#discussion_r917565063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033828594", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033833235", "body": ""}
{"comment": {"body": "Yep, that is all true. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#discussion_r917568858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033834035", "body": ""}
{"comment": {"body": "Yes, though it should never be the case. The fall through here is really to handle the case where the service returns no items (ie, the final empty page), in that case last-modified might be empty, I wasn't sure, so we make sure to store the last value we got back. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#discussion_r917569388"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#pullrequestreview-1033834975", "body": ""}
{"comment": {"body": "Yes -- that would potentially lead to cycles of re-fetching the same items, but it should never happen. If a page is returned, we should also get a last-modified value. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2247#discussion_r917570044"}}
{"title": "Attempt to address timeouts in recommendation backfill", "number": 2248, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2248", "body": "Recommendation backfill is timing out, because there is far too much work\nfor a single call.  Reduce the total number of threads to backill to just the\nmost recent 2000 and batch to kep the transations smaller."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2248#pullrequestreview-1035020169", "body": ""}
{"title": "Fix source mark pagination", "number": 2249, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2249", "body": "limit for applying to source-points instead of source-marks"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2249#pullrequestreview-1034596995", "body": ""}
{"title": "AddHelmChartUtilities", "number": 225, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225", "body": "Add ability to crate service helm charts automatically using ansible playbook.\nFix up github actions\n\nThe framework now is such:\n1. Create initial service helm skeleton via \"make create-service-helm-charts\"\n2. Any changes to base helm charts will be taking place in \"unblocked/helm/baseservice\""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#pullrequestreview-871167053", "body": ""}
{"comment": {"body": "love it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#discussion_r798056416"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#pullrequestreview-871167590", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#pullrequestreview-871167923", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#pullrequestreview-871171136", "body": ""}
{"comment": {"body": "no env var for root helm dir?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#discussion_r798059550"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#pullrequestreview-871172920", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#pullrequestreview-871173104", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#pullrequestreview-871180730", "body": ""}
{"comment": {"body": "Yeah, trying to figure that out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#discussion_r798066867"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#pullrequestreview-871181096", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#pullrequestreview-871204896", "body": ""}
{"comment": {"body": "Fixed :){", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/225#discussion_r798084994"}}
{"title": "Get pull request ingestion progress", "number": 2250, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250", "body": "The clients need to know if PR ingestion is occurring as there is a spinner shown during the tutorial while there are no threads to display. \nThere is a new property on Repo called isPullRequestIngestionComplete. The idea here is to use Redis to keep track of how many PRs are left to be ingested during the initial onboarding ingest. For repos with no PR threads, we'll know pretty quickly and can set isPullRequestIngestionComplete to true immediately so that clients can progress forward from the spinner without waiting on the threads API for results that will never come.\nFor repos that do have PR threads, we'll increment a counter in Redis every time we put a job on the queue during onboarding, and decrement when that job has been complete. When that counter hits 0, then we'll set isPullRequestIngestionComplete to true."}
{"comment": {"body": "@richiebres \r\n\r\n> * does the client need to know the percentage complete, or just that ingestion is complete? maybe we could have an `isIngestionReady: boolean` flag on `Repo` model instead\r\n\r\nThe progress percentage doesn't really matter for this purpose, I just thought I'd add it but might be a case of \"you ain't gonna need it\". Could simplify like you suggest.\r\n\r\n> * do we need to wait for ingestion to be complete, or just that it has ingested some minimal number of threads. for example, if a repo has 100K threads it'll take hours to complete, but we may have sucked in a few 100 threads in the first minute.\r\n\r\nWe don't, the clients just need to know whether it should continue to wait for some threads to appear (in which case a spinner will continue to show until threads are returned by the API) or if the repo has no threads, in which case the client can exit the spinner otherwise it'll be stuck waiting for threads that will never appear.\r\n\r\nSo to take your suggestion, we could put a `isIngestionComplete` flag on the repo that will be true if ingestion is complete. So for repos without threads, this will flip to true immediately because we'll be done ingesting (i.e. nothing to ingest).\r\n\r\n@kaych @jeffrey-ng would that work?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#issuecomment-1182602595"}}
{"comment": {"body": "@davidkwlam I think that's more what I was envisioning for what the client needs at least. ie the onboarding client would resolve the UI as soon as there's is something in the response; if `isIngestionComplete` && the response is empty, then show the error page. else show loading state ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#issuecomment-1183447209"}}
{"comment": {"body": "Thanks @richiebres @kaych, I'll rework this with your suggestions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#issuecomment-1183452665"}}
{"comment": {"body": "top level comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#issuecomment-1206715091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#pullrequestreview-1036386370", "body": "does the client need to know the percentage complete, or just that ingestion is complete? maybe we could have an isIngestionReady: boolean flag on Repo model instead\ndo we need to wait for ingestion to be complete, or just that it has ingested some minimal number of threads. for example, if a repo has 100K threads it'll take hours to complete, but we may have sucked in a few 100 threads in the first minute.\npusher behaviour is not clear to me\nbe nice to reuse this for Recommendation engine, since it also needs to be triggered when ingestion is ready"}
{"comment": {"body": "this is oddly named", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#discussion_r919349958"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#pullrequestreview-1042138496", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#pullrequestreview-1042144409", "body": ""}
{"comment": {"body": "The problem here is `setPullRequestIngestionComplete` will never be called if at least one PR ingestion job fails for whatever reason, so the `isPullRequestIngestionComplete` flag on RepoModel right now will only be set to true if all jobs were successfully completed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#discussion_r923572528"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#pullrequestreview-1059510597", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#pullrequestreview-1063692398", "body": "I am reviewing this"}
{"comment": {"body": "this is a response", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#discussion_r939045702"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#pullrequestreview-1063693368", "body": ""}
{"comment": {"body": "reply", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2250#discussion_r939046865"}}
{"title": "Poll Installations API directly", "number": 2251, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251", "body": "InstallationsStore in VSCode had to poll RepoStore instead of the Installations API directly due to some early limitations.\nUpdated to poll Installations to reduce complexity."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#pullrequestreview-1034660554", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#pullrequestreview-1034661537", "body": ""}
{"comment": {"body": "With this change, doesn't this mean we will *always* be polling for installations?  Even when we don't care about installation changes?\r\n\r\n(Hmm.... maybe that's fine?  Maybe we always do care about installations?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#discussion_r918159442"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#pullrequestreview-1034699708", "body": ""}
{"comment": {"body": "Polling state is local and is managed by `startPollingInstallations` & `stopPollingInstallations` and not dependant on subscribers.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#discussion_r918186991"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#pullrequestreview-1034732202", "body": ""}
{"comment": {"body": "Ah.\r\n\r\nI think this might be a bit of an antipattern, having the object that owns/manages a stream also subscribe to it.  It basically means the stream is always active, and I suspect the stream and parent object (this store) will never be reclaimed by the GC...  or maybe not, it's a bit hard to say.\r\n\r\nThe fact that we have a stream owner subscribing to its own stream, plus separate \"start / stop stream\" functions, makes me think we are working against xstream.  I think it would make more use to follow the way xstream is meant to work:\r\n\r\n* The store would publish a `Stream<InstallationStoreState>` as it does now, but would not export `startPolling` / `stopPolling` functions.\r\n* The store would not use ValueStream.  It would instead publish an xstream Producer, which lets the stream know when anyone is subscribed or not (via its start / stop functions)\r\n* When the producer's start method is called, the stream would start its own timer loop, polling for values.  Stop would stop this.\r\n* The logic for refreshing teams would also happen as a part of this loop", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#discussion_r918208325"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#pullrequestreview-1034732558", "body": ""}
{"comment": {"body": "This can be done later of course, and maybe I'm missing some part of the puzzle here and this doesn't make sense...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#discussion_r918208686"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#pullrequestreview-1034749240", "body": ""}
{"comment": {"body": "That makes sense. My intention was to have a pattern where a subscription was independent from the polling state.\r\n\r\nThis will require some refactoring of INstallationWizardManager which I'd like to do as a separate PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#discussion_r918221051"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Test\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ccd74d1e-02e2-4c12-8710-dbca9d2dedeb?message=72c99dd9-e86d-4d1b-bb0d-37e686c82fa1).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2251#discussion_r948276680"}}
{"title": "update adminweb service account with s3 ro perms", "number": 2252, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2252", "body": "Updated admin web service account to grant S3 list bucket and GetObject access on download-assets (public bucket). The reason for this approach is so that we won't need to provide a directory listing page for download assets. \nChanges have been deployed to both Dev and Prod. Next I'll create the S3 Kotlin client provider class."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2252#pullrequestreview-**********", "body": ""}
{"title": "Remove superfluous settings", "number": 2253, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2253"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2253#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Should this be `threadInviteEmails eq true` here too? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2253#discussion_r918173301"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2253#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Removed the necessity for specifying that as the boolean is the same for both functions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2253#discussion_r918179937"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2253#pullrequestreview-**********", "body": ""}
{"title": "Catch correct error type", "number": 2254, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2254", "body": "Should have been catching ResponseError instead of just Response."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2254#pullrequestreview-1034699925", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2254#pullrequestreview-1034706345", "body": ""}
{"comment": {"body": "As a reminder, this is done to handle an onboarding race condition.\r\n\r\n1. When new installations are fetched, that typically means a new *team* has been created.\r\n2. The new installations triggers a team fetch. This means we have a new team to fetch repos for.\r\n3. When we try making a repo request for this new team, instead of a 404, we would potentially get a 401 now instead of a 200. This occurs because the current auth token does not have have a claim to the new team that we just fetched from getTeams. Therefore, we need to refresh the token and retry the repo fetch.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2254#discussion_r918191541"}}
{"title": "add converters", "number": 2255, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2255"}
{"title": "Clean up SourceMark rendering types", "number": 2256, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2256", "body": "Cleanup from https://github.com/NextChapterSoftware/unblocked/pull/2233\nRemove the wrapping AnchorSourcePointData type, use SourceMarkResolution everywhere instead.\nDiscussionThreadCommand still stores both a SourceMarkResolution (the resolved SM/SP), plus the SourceMarkData (the renderable sourcemark data, like the HTML rendered clip).\nAs part of this, moved the HTML rendering into DiscussionThreadCommand, as that was the only place that actually used it -- I think this makes the code dependencies a little bit clearer."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2256#pullrequestreview-1035017645", "body": "lgtm"}
{"title": "stop", "number": 2257, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2257"}
{"title": "update", "number": 2258, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2258"}
{"title": "Enable admin auth in dev", "number": 2259, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2259", "body": "Enable admin auth in dev"}
{"title": "lint make target", "number": 226, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/226", "body": "for convenience. faster than ./gradlew check which runs tests"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/226#pullrequestreview-871149228", "body": ""}
{"title": "Fix admin prod config property name", "number": 2260, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2260"}
{"title": "[Dashboard] Update (first pass)", "number": 2261, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261", "body": "\n\n\nNOTES:\n* This only ports over the existing UI and functionality (i.e. will need to layer on the 'new' features like the modal navigation) \n* There are quite a lot of files changed but it's 99% CSS changes for the new designs (i.e. functionality is all the same)\n* Small viewports are still in progress but in its current form it's functional/doesn't look broken so I think it's enough to get in\n* Will need to do a separate pass at revamping all the theme variables to match Ben's style guide as well as a pass at the typography.scss file"}
{"comment": {"body": "I would love to get that white space removal done in the near future. @davidkwlam is that on your to do list?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#issuecomment-1180804944"}}
{"comment": {"body": "Should we update all clients to use the \"Icon + Text\"? \r\n\r\nThis makes pseudo styles a bit more difficult to handle if we ever to go down that path.\r\n<img width=\"1164\" alt=\"CleanShot 2022-07-11 at 15 16 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/178368045-8acb5791-d7fb-43b9-8bd9-b8140f351176.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#issuecomment-1180806548"}}
{"comment": {"body": "@benedict-jw This might need to be a client-side change after all. The sourcemark engine depends on the snippet being untouched to accurately track movement or changes between commits.\r\n\r\nConvo here: https://linear.app/unblocked/issue/UNB-412/remove-preceding-white-space-in-code-snippet", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#issuecomment-1180825335"}}
{"comment": {"body": "> Should we update all clients to use the \"Icon + Text\"?\r\n> \r\n> This makes pseudo styles a bit more difficult to handle if we ever to go down that path.\r\n\r\nI don't know if we need to, at least not right now... I think we can probably start with standardizing headers and p/div/span tags to a strict style mixin and go from there (plan is to do that in an ensuing PR)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#issuecomment-1180849803"}}
{"comment": {"body": "> Should we update all clients to use the \"Icon + Text\"?\r\n> \r\n> This makes pseudo styles a bit more difficult to handle if we ever to go down that path. <img alt=\"CleanShot 2022-07-11 at 15 16 47@2x\" width=\"1164\" src=\"https://user-images.githubusercontent.com/1553313/178368045-8acb5791-d7fb-43b9-8bd9-b8140f351176.png\">\r\n\r\nFWIW I have a preference for using actual text for text, not images, if we can avoid it... but if this is using some specially-styled text it's all good", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#issuecomment-1180951007"}}
{"comment": {"body": "> > Should we update all clients to use the \"Icon + Text\"?\r\n> > This makes pseudo styles a bit more difficult to handle if we ever to go down that path. <img alt=\"CleanShot 2022-07-11 at 15 16 47@2x\" width=\"1164\" src=\"https://user-images.githubusercontent.com/1553313/178368045-8acb5791-d7fb-43b9-8bd9-b8140f351176.png\">\r\n> \r\n> FWIW I have a preference for using actual text for text, not images, if we can avoid it... but if this is using some specially-styled text it's all good\r\n\r\nHow come ? I feel like this way we can actually make the icon sizing, letter spacing, spacing between Icon+Text consistent between clients without too much effort, it's treated as a logo entity vs image+text ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#issuecomment-1180958178"}}
{"comment": {"body": "> @benedict-jw This might need to be a client-side change after all. The sourcemark engine depends on the snippet being untouched to accurately track movement or changes between commits.\r\n> \r\n> Convo here: https://linear.app/unblocked/issue/UNB-412/remove-preceding-white-space-in-code-snippet\r\n\r\nAh, thanks for looping me in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#issuecomment-1180963606"}}
{"comment": {"body": "> How come ? I feel like this way we can actually make the icon sizing, letter spacing, spacing between Icon+Text consistent between clients without too much effort, it's treated as a logo entity vs image+text\r\n\r\nIt's not a strong preference, really.  Encoding text in images has a bunch of downsides: the text isn't searchable, it's much larger over the wire, it's less accessible.  None of these are particularly relevant in this case.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#issuecomment-1181293676"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034850700", "body": ""}
{"comment": {"body": "If we're going to be importing in shared, remove from the platform specific font files", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#discussion_r918292476"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034854674", "body": ""}
{"comment": {"body": "This makes sense but just wanted to note that this could cause some unexpected scrolling if we ever manually update the history without navigating.\r\n\r\nIt's something we're doing for web extension but not web atm...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#discussion_r918295282"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034862566", "body": ""}
{"comment": {"body": "Is this just used to style the sidebar? Or do you foresee other components that would be restyled?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#discussion_r918300880"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034899452", "body": ""}
{"comment": {"body": "it's not so much the sidebar than it is sidebar+content. ie in the `overview` layout, we want the main container to be centered vs in the `detail` layout, we want the content to be full width\r\n\r\nthere's a chance there's a way to simplify this -- this was written when the designs were still changing so it might be a bit of overkill. But this is a nice solution IMO for what we need it to do \r\n\r\nin terms of future UIs, there's a settings UI that will leverage the `detail` layout as well:\r\n<img width=\"592\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/178353133-a6dbdf58-fc47-46c2-8a1e-56beeb10cdf5.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#discussion_r918326327"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034900401", "body": ""}
{"comment": {"body": "We can revisit when/if that comes up(?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#discussion_r918327071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034947542", "body": ""}
{"comment": {"body": "Won't this load fonts for clients that don't necessarily need them?  I don't think VSCode uses any custom fonts?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#discussion_r918360063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034957068", "body": ""}
{"comment": {"body": "Can we put a comment here about why we're doing this?  I am pretty sure this will cause relatively minor bugs (like, clicking back/forward will not take you to the scroll location you were at previously), and we may need to investigate it at some point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#discussion_r918367265"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034960696", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034962734", "body": ""}
{"comment": {"body": "vscode uses custom fonts in onboarding \u2728 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#discussion_r918371655"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034969056", "body": ""}
{"comment": {"body": "I like the general mechanism for this -- one concern I have is that with this, each view is triggering the layout as an effect on first render.  The problem with this is that any view that *doesn't* explicitly set the layout will effectively inherit the last-set layout, which will lead to strange views.\r\n\r\nIdeally this would almost be a property on a route or view, with a default value being used (overview, I guess?) for whatever layout is most common.  Maybe a wrapper that we define in the route?\r\n\r\n```\r\n  <Route path=\"/my/thing\" element={<DetailLayout><MyThing /></DetailLayout>}/>\r\n```\r\n\r\n(Or `MyThing` could use `DetailLayout` internally?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#discussion_r918376538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034969399", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034970028", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1034972086", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2261#pullrequestreview-1035005864", "body": ""}
{"title": "update", "number": 2262, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2262"}
{"title": "Bump Swift GRPC version for diff job", "number": 2263, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2263", "body": "Doesn't break anything, just keeping things in parity with latest brew version"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2263#pullrequestreview-1034878101", "body": ""}
{"title": "UpgradeVersiong", "number": 2264, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2264"}
{"title": "update", "number": 2265, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2265"}
{"title": "update", "number": 2266, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2266"}
{"title": "Fix another bug in sourcemark paging", "number": 2267, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2267", "body": "Querying for both mark and point modifiedAt was error prone.\nSimplify modifiedAt queries so that only the sourcemark modifiedAt is queried.\nThis change touches the source mark modifiedAt whenever a new point is created for\nthe mark in order to achieve the simpler query."}
{"title": "Create a helper function to get a trimmed snippet", "number": 2268, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2268", "body": "Not sure if this is the right approach but throwing it out there for feedback."}
{"comment": {"body": "Merging this. I'll open another PR with the proper implementation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2268#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2268#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2268#pullrequestreview-**********", "body": " Yep looks good"}
{"title": "Add S3 client to aws-lib", "number": 2269, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2269", "body": "Added S3 client provider along with functionality needed to implement polling for release artifacts \nAdded tests for S3 client. \n\nMy next PR will wire up Version ingestion and obsolete actions to S3 and introduce polling job."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2269#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2269#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2269#pullrequestreview-**********", "body": "With comments"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2269#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2269#pullrequestreview-**********", "body": ""}
{"title": "Upload test report if build fails", "number": 227, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/227"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/227#pullrequestreview-871219516", "body": ""}
{"comment": {"body": "Please add retention period...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/227#discussion_r798096044"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/227#pullrequestreview-871220164", "body": ""}
{"comment": {"body": "Probably shoudl be doing this for all of our stuff iin this file.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/227#discussion_r798096618"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/227#pullrequestreview-871222228", "body": ""}
{"title": "Fix VSCode discussion thread text editor closing behaviour", "number": 2270, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2270", "body": "Fix VSCode discussion text editor closing bugs from user feedback:\n* If you open a discussion, and then edit the text in the editor, do not close the editor when we close the discussion, as long as the editor is in a dirty state\n* If we open a discussion and display a SM resolution UI, close that when we close the discussion"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2270#pullrequestreview-1036353944", "body": ""}
{"title": "Trim source snippet lines", "number": 2271, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2271", "body": "Fixes UNB-412\nMight be missing some test cases"}
{"comment": {"body": "> 2\\. One corner case I can think of, but I don't think we can deal with: mixed spaces and tabs.  I know that seems odd, but for teams that don't use auto formatters it does happen.  In that case unless we somehow know how many spaces a tab resolves to visually I'm not sure we can do anything useful.\r\n\r\n@matthewjamesadam This scenario is covered, since this change consistently removes the exact same number of tabs or spaces from _every_ line. We don't make any assumption that a tab is 4 x spaces for example.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2271#issuecomment-1183527042"}}
