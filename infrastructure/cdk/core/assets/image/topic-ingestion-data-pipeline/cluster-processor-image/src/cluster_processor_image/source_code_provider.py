import logging
import os
import time
from typing import List

from classifier_utils.source_classifier import SourceClassifier
from file_utils.file_traversal import FileTraversal
from partition_utils.code_partitioner import CodePartitioner
from uuid_utils.uuid_utils import UuidUtils

import git_utils.git_utils as git

from integration_utils.message_provider import MessageProvider
from integration_utils.message import MessageInterface


class SourceCodeMessage(MessageInterface):
    repo_id: str
    file_content: str
    file_path: str
    file_name: str
    start_line: int
    end_line: int

    def __init__(self, repo_id: str, file_content: str, file_path: str, start_line: int, end_line: int):
        self.repo_id = repo_id
        self.file_content = file_content
        self.file_path = file_path
        self.start_line = start_line
        self.end_line = end_line
        self.file_name = file_path.split("/")[-1]

    def get_id(self) -> str:
        return UuidUtils.get_uuid_from_file(repo_id=self.repo_id, file_path=self.file_path).__str__()

    def get_text(self) -> str:
        return (
            f"FILE_PATH: {self.file_path}\n"
            + f"FILE_NAME: {self.file_name}\n"
            + f"FILE_ID: {self.get_id()}\n"
            + f"CONTENT:\n"
            + self.file_content
        )

    def get_truncated_text(self) -> str:
        return self.get_text()


class SourceCodeProvider(MessageProvider):
    _repo_id: str
    _repo_dir: str
    _partitioner = CodePartitioner(max_chars=8000, strip_imports=True)
    _source_classifiers: dict[str, SourceClassifier]

    def __init__(
        self,
        repo_id: str,
        repo_dir: str,
        source_classifiers: dict[str, SourceClassifier],
    ):
        self._repo_id = repo_id
        self._repo_dir = repo_dir
        self._source_classifiers = source_classifiers

    def __process_file(
        self,
        root_path: str,
        rel_file: str,
    ) -> List[SourceCodeMessage]:
        """Process a single file."""
        abs_file = os.path.join(root_path, rel_file)
        messages: list[SourceCodeMessage] = []

        try:
            file_metadata = git.file_metadata(abs_file)
            # Partition the source file
            partitions = self._partitioner.split_file(file_path=rel_file, file_content=file_metadata.get("content"))

            # Generate embeddings for each partition and join on the file metadata
            for partition in partitions:
                messages.append(
                    SourceCodeMessage(
                        repo_id=self._repo_id,
                        file_content=partition.code,
                        file_path=rel_file,
                        start_line=partition.start_line,
                        end_line=partition.end_line,
                    )
                )

            if len(messages) <= 0:
                logging.warning(f"Unable to create message for file: '{rel_file}'. Skipping pinecone upsert.")

        except TimeoutError:
            logging.warning(f"TimeoutError while processing file '{abs_file}'")
        except Exception as e:
            logging.exception(f"Failed to process file '{abs_file}'. Exception: {str(e)}")

        return messages

    def __process_repo(
        self,
        root_path: str,
    ) -> List[SourceCodeMessage]:
        """Process the files in the repository."""
        rel_files: list[str] = FileTraversal(
            root_path=root_path,
            min_file_size=256,
            source_classifiers=self._source_classifiers,
        ).list_text_files()
        file_count = len(rel_files)
        messages: List[SourceCodeMessage] = []

        while rel_files:
            rel_file = rel_files.pop()
            start_time = time.time()
            file_messages = self.__process_file(
                root_path=root_path,
                rel_file=rel_file,
            )
            messages.extend(file_messages)
            elapsed = time.time() - start_time
            logging.info(
                f"Processed file in {round(elapsed, 3)}s: '{rel_file}' ({round(100 * (file_count - len(rel_files)) / file_count, 1)}%)"
            )

        return messages

    def load(self) -> [MessageInterface]:
        start_time = time.time()

        if git.git_is_repo_empty(repo_dir=self._repo_dir):
            logging.info(f"Repository is empty, exiting ...")
            return []

        logging.info("Processing repository ...")
        messages = self.__process_repo(
            root_path=self._repo_dir,
        )

        elapsed_seconds = round(time.time() - start_time)
        logging.info(f"Done, processed {len(messages)} files in {elapsed_seconds}s")

        return messages
