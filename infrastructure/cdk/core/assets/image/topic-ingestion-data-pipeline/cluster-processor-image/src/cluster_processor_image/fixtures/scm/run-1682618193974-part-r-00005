{"title": "Trying to condense...", "number": 1897, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897", "body": "Dumped images so we can focus on text in this PR"}
{"comment": {"body": "Do we feel this issue was addressed out-of-band? @dennispi can we close this PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#issuecomment-1168171263"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1011583678", "body": "Per our discussion, the spacing / whitespace feels odd even with this shortened text. \nI'll work with @kaych to incorporate these changes and find the correct way to space the text out."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1011597317", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1011598842", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1011693687", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012466411", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012555498", "body": ""}
{"comment": {"body": "I still feel like this should be present tense, ie `Unblocked imports your discussions and makes them available ...`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#discussion_r901860454"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012555949", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012556653", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012569078", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012570019", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012582370", "body": ""}
{"comment": {"body": "I think it's important to draw attention to what Unblocked has _already done_. If we use the present tense, we would need to do something like:\r\n```\r\nUnblocked imports your discussions and makes them available in the sidebar and directly in your code. \r\nUnblocked has already imported discussions you've recently participated in.\r\n\r\nSelect from the sidebar to view a discussion.\r\n```\r\n\r\nSomething like that?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#discussion_r901881218"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012626065", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012655329", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012657301", "body": ""}
{"comment": {"body": "> I think it's important to draw attention to what Unblocked has already done.\r\n\r\nNot super convinced on this because we are actually actively ingesting content, as in, it's a constant process and not a one-off action in the past. And by drawing attention to the sidebar, it's clear that something has already been done. But I don't feel super strongly so we can table this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#discussion_r901934599"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012682822", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012689753", "body": ""}
{"comment": {"body": "You make a good point about it being continuous. We also need to ensure is that the customer clearly understands we've done some work for them, and that they can interact with those newly imported PRs. Is that obvious when the tense is present (not a rhetorical question)? \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/e4feea20-e508-48ec-86c3-cd7b798f34d6?message=d47f7620-6131-43d3-b61f-be758a532d78).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#discussion_r901958440"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1897#pullrequestreview-1012824651", "body": ""}
{"title": "Refactor PullRequestReviewThread object", "number": 1898, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1898", "body": "Just pass in the whole object.\nNo logic changes here and it's all covered by tests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1898#pullrequestreview-1011556089", "body": ""}
{"title": "proper honeycomb", "number": 1899, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1899"}
{"comment": {"body": "> Haha, yeah I botched that. Thanks!\r\n\r\nNot really botched. Just added minor ktors tracing.\r\nGot to document this stuff. My bad.\r\n\r\n-Rashin", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1899#issuecomment-1159809529"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1899#pullrequestreview-1011566873", "body": "Haha, yeah I botched that. Thanks!"}
{"title": "Recoil implementation", "number": 19, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/19"}
{"title": "Remove sourcemark operations", "number": 190, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/190", "body": "Creating a source mark will happen when creating/updating a message, so the PUT operation is not needed. Similarly, we'll get source marks through messages."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/190#pullrequestreview-868455771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/190#pullrequestreview-868463282", "body": ""}
{"title": "Compress webhook payloads", "number": 1900, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900", "body": "Compress SCM body, then base64 encode, then JSON encode."}
{"comment": {"body": "> I'm a bit surprised that sqs doesn't come with compression options out of the box. We sure we have to roll our own?\r\n\r\n@pwerry good point, didn\u2019t think of that. However turns out that SQS SDK does not support compression. Nor does it support the `Accept: gzip` header for the network transfer!\r\n\r\nI think I\u2019ll rework in follow up so that our SQS message provider layer transparently handles compression (and only compresses when necessary since small messages see negative benefit). Then all messages producers and consumers can benefit without writing a line of code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Also needs Provider, but I need to extract that from DB models first. Will do later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r901169535"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-**********", "body": ""}
{"comment": {"body": "for `ScmWebhookMessage`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r901169674"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-**********", "body": "I'm a bit surprised that sqs doesn't come with compression options out of the box. We sure we have to roll our own?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Commutative. Man. You know how To make a man's day", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r901740063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-1014519838", "body": ""}
{"comment": {"body": "@mahdi-torabi\n\n@matthewjamesadam\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/faff2321-3f69-4786-b665-c7de96efd079?message=8b80a3e6-a261-44f2-98c3-bd567bcc0659).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r903275328"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-1014519896", "body": ""}
{"comment": {"body": "@pwerry\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/faff2321-3f69-4786-b665-c7de96efd079?message=3f1ef890-ed7e-4118-86d0-20454a29d4ea).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r903275367"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-1014520418", "body": ""}
{"comment": {"body": "hello hello hello\n\n@pwerry @mahdi-torabi @matthewjamesadam\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/faff2321-3f69-4786-b665-c7de96efd079?message=759c9811-d1b2-4b34-9bdd-d8a840b14f3b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r903275745"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-1015312464", "body": ""}
{"comment": {"body": "pikachu\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/faff2321-3f69-4786-b665-c7de96efd079?message=90294aaa-6d09-498d-a9cc-b1cd553c0a31).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r903836562"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-1015790945", "body": ""}
{"comment": {"body": "@PadraigK\n\n@pwerry\n\n\n\n@davidkwlam\n\n@PadraigK\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/faff2321-3f69-4786-b665-c7de96efd079?message=da896d46-b568-402d-a732-8950ec3b7607).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r904169088"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-1015791748", "body": ""}
{"comment": {"body": "@J\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/faff2321-3f69-4786-b665-c7de96efd079?message=db217404-dd9e-4d18-b244-e71a5ce82ec7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r904169588"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-1015791857", "body": ""}
{"comment": {"body": "@jeffrey-ng\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/faff2321-3f69-4786-b665-c7de96efd079?message=2ba70882-a50b-4972-b8dc-40c566e75bd7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r904169664"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#pullrequestreview-1015792078", "body": ""}
{"comment": {"body": "@kaych\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/faff2321-3f69-4786-b665-c7de96efd079?message=669937dd-88c2-428d-825b-2ec0973f67d2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1900#discussion_r904169803"}}
{"title": "Pull out TeamMemberProvider creation into a factory", "number": 1901, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1901", "body": "More refactoring fun. We're going to need to reuse this logic in the GitHubPullRequestReviewCommentHandler so let's pull it out and add some tests."}
{"title": "Add GitHubPullRequestReviewCommentEvent and GitHubPullRequestEvent handlers", "number": 1902, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902", "body": "Will enable in the next PR once we have the SCM service consuming the events from hooks_scm queue"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#pullrequestreview-**********", "body": ""}
{"comment": {"body": "do we really need this to return DAO... maybe we can return data class instead?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r901923641"}}
{"comment": {"body": "Possible to return `Repo` instead? Ignore me if this is necessary", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r901924205"}}
{"comment": {"body": "optional: you could do an UPDATE here if you include the state in the predicate. slightly more efficient.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r901930857"}}
{"comment": {"body": "do we ever mark the PR as merged on this path?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r901934105"}}
{"comment": {"body": "Do we have a test that asserts that an open PR + merged event results in a merge PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r901938080"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#pullrequestreview-1012666943", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#pullrequestreview-1012755538", "body": ""}
{"comment": {"body": "Yeah, this will trigger PR ingestion for that PR which upserts the pull request and sets the state. Reason for doing \"full\" ingestion of the PR in this case is that we need to grab the latest files for the PR and create a new merge commit sourcepoint for all threads that are not out of date.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r902004414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#pullrequestreview-1012756314", "body": ""}
{"comment": {"body": "No we don't but if we add one, that should live in `PullRequestIngestionServiceTest`. Let me add that there in a separate PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r902004966"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#pullrequestreview-1012757721", "body": ""}
{"comment": {"body": "Needed to pass to `PullRequestIngestionService` which creates models. \r\n\r\nI can refactor that service to take data classes but that should probably happen in another PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r902005912"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#pullrequestreview-1012757907", "body": ""}
{"comment": {"body": "Same reason as https://github.com/NextChapterSoftware/unblocked/pull/1902/files#r902005912", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r902006034"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#pullrequestreview-1012789215", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/1919", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r902030596"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#pullrequestreview-1012812675", "body": ""}
{"comment": {"body": "nah, all good", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#discussion_r902048469"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1902#pullrequestreview-1012813734", "body": ""}
{"title": "limit access to webhooks endpoint", "number": 1903, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903", "body": "Added IP filtering rule to limit access to /api/hooks \nAdded GitHub web hooks IPs to whitelist \nRemoved old rule which was commented out"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#pullrequestreview-1011720116", "body": "not clear to me how this works when we have inbound hooks from bitbucket, gitlab, etc.."}
{"comment": {"body": "make specific to GitHub?\r\n```suggestion\r\n        \"name\": \"waf-cloudfront-github\",\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#discussion_r901276694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#pullrequestreview-1011736464", "body": ""}
{"comment": {"body": "GitLab's IP addresses are here:\r\nhttps://docs.gitlab.com/ee/user/gitlab_com/#ip-range\r\n\r\nWhere in config would be put those?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#discussion_r901287935"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#pullrequestreview-1012383820", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#pullrequestreview-1012627768", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#pullrequestreview-1012633445", "body": ""}
{"comment": {"body": "This WAF is fronting the CloudFront distribution that serves all of our endpoints. Not specific to GitHub or any particular service.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#discussion_r901914844"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1903#pullrequestreview-**********", "body": ""}
{"title": "Webhook queue message contains SCM provider", "number": 1904, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1904", "body": "Dave: will need this to \"route\" the message to the appropriate handler."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1904#pullrequestreview-**********", "body": ""}
{"title": "Add ScmWebhookProcessingJob", "number": 1905, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905", "body": "Part 1 of  to check that consuming from the queue works.\nNext PRs will deserialize, decompress, and route the webhooks to the correct handler."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-**********", "body": ""}
{"comment": {"body": "How long does it take to process a single message roughly?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r901893892"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-10********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think we need to get [Honeycomb] metrics to observe how long it takes end-to-end to process a message that takes into account:\r\n- queue latency\r\n- processing latency", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r901918903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Yeah, hard to say at this point. For events that don't require hitting the GitHub API this should be quick (just some database calls) but I've seen single GitHub API requests take 2 seconds.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r901921013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-**********", "body": ""}
{"comment": {"body": "One of the really nice effects of Kafka was that we could partition by key. If we were to choose orgID as the key then we'd get two nice affinity effects:\n\n\n\n1. ordering of messages is consistent within a partition, so no out-of-order messages within an org.\n\n2. we can increase concurrency as much as we like (infinity) because we would never run the risk of making concurrent GitHub API calls for a GitHub identity (org in this case), so would avoid secondary rate limit throttle.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9d33b8ce-bd08-4027-86b9-aa00aae03cb2?message=20ebac63-c293-471a-937d-f84d15d7db5e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r901958197"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012689430", "body": ""}
{"comment": {"body": "Or SQS message groups:\n\nhttps://aws.amazon.com/blogs/compute/solving-complex-ordering-challenges-with-amazon-sqs-fifo-queues/\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9d33b8ce-bd08-4027-86b9-aa00aae03cb2?message=922c8675-3775-45b6-af62-444552ee43de).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r901958199"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012689490", "body": ""}
{"comment": {"body": "So in worst case, there could be a 20 second latency. Meanwhile another consumer could be idle.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9d33b8ce-bd08-4027-86b9-aa00aae03cb2?message=1055caa1-9927-4034-9c0f-f3bccc953c8a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r901958252"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012689620", "body": ""}
{"comment": {"body": "I think we can do this with SQS through a combination of FIFO queues, making lots of queues, then manually sharding the orgIDs to each of those queues, and making sure that only one consumer instance polls each queue at any time. This is really fucking complicated in failure scenarios and I wouldn't touch this with a barge poll.\n\n\n\nWe may need to go the Kafka route in the future if we run into significant ordering/scaling issues.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9d33b8ce-bd08-4027-86b9-aa00aae03cb2?message=cf5416c5-9f32-4ef5-8953-595e80cba25b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r901958335"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012689693", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012747769", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012747842", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012748053", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012748723", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012749002", "body": ""}
{"comment": {"body": "> I think we can do this with SQS through a combination of FIFO queues, making lots of queues, then manually sharding the orgIDs to each of those queues, and making sure that only one consumer instance polls each queue at any time. This is really fucking complicated in failure scenarios and I wouldn't touch this with a barge poll.\r\n\r\nYeah not a fan of this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r901999596"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012749751", "body": ""}
{"comment": {"body": "> * ordering of messages is consistent within a partition, so no out-of-order messages within an org.\r\n> *\r\n\r\nWhat you're talking about is FIFO and grouping.\r\n\r\nhttps://docs.aws.amazon.com/sns/latest/dg/fifo-message-grouping.html", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r901999804"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012750317", "body": ""}
{"comment": {"body": "This will throw if we've not handled a particular event, which is what we want (we can alert and fix then retry)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r902000390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012752272", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012752448", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012765588", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012766047", "body": ""}
{"comment": {"body": "Cool. I think this is what we want, as it'll allow us to scale up without risking the GitHub concurrency per org secondary throttle. I'll add an issue...\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9d33b8ce-bd08-4027-86b9-aa00aae03cb2?message=8a2ca8f3-72d2-442c-88d5-82ae8f5e52b4).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r902012263"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012780065", "body": ""}
{"comment": {"body": "https://linear.app/unblocked/issue/UNB-315/use-sqs-fifo-and-grouping-to-prevent-latency-and-availability-issues\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9d33b8ce-bd08-4027-86b9-aa00aae03cb2?message=a12753db-eb6d-424b-a4fc-ace20c630c0c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r902023598"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012792650", "body": ""}
{"comment": {"body": "It does mean that new event types will error until we push a change to add to the `GitHubWebhookEvent` enum, but I think that's preferable to failing silently", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r902033547"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012816548", "body": ""}
{"comment": {"body": "The downside is that the new events will clog the queue because they'll be retried a bunch of times. In the case where these events are very numerous then we'd delay the processing of well-formed messages.\r\n\r\nThis is fine for now, but I think the general solution is to never retry on the main queue (let the main queue blast through well-formed messages as fast as possible). Instead in the failure case, we should delete it from the main queue (as if it had succeeded) and re-queue by sending it to a completely independent retry queue with delay.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#discussion_r902051874"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1905#pullrequestreview-1012817222", "body": "Sweet, let's get this in "}
{"title": "Add fallback URL Logic", "number": 1906, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1906", "body": "Add fallback logic for when Hub app falls back to either Web extension or eventually dashboard.\nTODO:\nRequire Popup designs for clients."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1906#pullrequestreview-1012761833", "body": ""}
{"title": "UNB-165 Hook up install downloadUrl to web landing page", "number": 1907, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012624996", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012625617", "body": ""}
{"comment": {"body": "local landing was broken without these changes to mirror [these](https://github.com/NextChapterSoftware/unblocked/commit/3e00658a3e64ff438d57d184e1cccc30deb94227) changes ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#discussion_r901902538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012628197", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012630095", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012634183", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012651771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012658164", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012664128", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012687374", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012688217", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012689549", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012766410", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012767034", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012768840", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012769123", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012771186", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1012803155", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1907#pullrequestreview-1014201885", "body": "Looks good.  I should have made it clear, I'm 100% fine with the hook if you think that's a better way forward..."}
{"title": "Add Ben Test Org", "number": 1908, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1908", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1908#pullrequestreview-1012629835", "body": ""}
{"title": "Admin console delete repo", "number": 1909, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1909", "body": "For testing and demoing onboarding (and maybe other purposes???) we want to delete the repo from our API services.\nAdded basic deleting capabilities to admin console to delete repos. Would be nice to have an \"alert\" to block these requests but wasn't able to figure that out with kotlinx SSR and confirm dialogs...\nAn alternative to deleting the repos would be to add a \"isDeleted\" flag for soft deletes?"}
{"comment": {"body": "Why is uninstall not enough?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1909#issuecomment-1160757714"}}
{"comment": {"body": "> Why is uninstall not enough?\r\n\r\nWe end up with Installation models where `getInstalled === false` but `unknownRepos` is empty and `repos` is populated.\r\nThis isn't entirely representative of a \"new\" user/org for onboarding.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1909#issuecomment-1160763009"}}
{"comment": {"body": "Cleaner to nuke the entire team, but also do it in a way that is reversible:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1955", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1909#issuecomment-1163266231"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1909#pullrequestreview-1012667885", "body": "Concerned about the cascading effect here, because it's not tested.\nDeleting a repo will (in theory) delete first-order dependencies:\n- ThreadModel\n- PullRequestIngestionModel\n- PullRequestModel\n- SourceMarkModel\n- ThreadSearchModel\nThen this will cascade to many other models:\n- MessageModel\n- ThreadUnreadModel\n- ThreadRankModel"}
{"title": "Rename [3 of 4]: SourceVector -> SourceMark", "number": 191, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/191", "body": "Continue renaming:\n\nSourceMark  SourceMarkGroup (#186)\nSourceMark  SourceMarkGroup in API (#189)\nSourceVector  SourceMark (this change)\nRelate SourceMark  Message"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/191#pullrequestreview-868466784", "body": ""}
{"title": "Wrap in transaction", "number": 1910, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1910", "body": "Interesting that a unit test wouldn't have caught this since every suspendingDatabaseTest is a transaction block\nMaybe the problem here is allowing the caller to call it without a transaction. Without the default null value, the caller would be forced to open a transaction"}
{"comment": {"body": "> Interesting that a unit test wouldn't have caught this since every `suspendingDatabaseTest` is a transaction block\r\n> \r\n> Maybe the problem here is allowing the caller to call it without a transaction. Without the default null value, the caller would be forced to open a transaction\r\n\r\nAgree here. All of these functions at the model layer should either pass a parent transaction through to a child transaction, or open a new transaction", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1910#issuecomment-1160774004"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1910#pullrequestreview-1012668715", "body": ""}
{"title": "UNB-291 Make context menus 12px size", "number": 1911, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1911", "body": "Make it 12px per Dennis' feedback\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1911#pullrequestreview-1012762629", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1911#pullrequestreview-1012764380", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1911#pullrequestreview-1012764435", "body": ""}
{"title": "Use full-range lines when creating new discussions", "number": 1912, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912", "body": "Fixes UNB-200\nWhen starting a new discussion, use the full line range (ie, start to end of every line).  We keep putting logic for how to do range conversion and processing in different files, so I moved this into a central place and wrote some tests.  Running tests involved mocking out some VSCode types, since we don't have those at runtime."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012737883", "body": "Nice tests. Just one comment"}
{"comment": {"body": "The motivation for `getContentRange` is clear; so that we can widen the visible _context_ of the user's selection.\r\n\r\nI'm less clear about why we would extend the point itself in `getLineExtentRange`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#discussion_r901991866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012747343", "body": ""}
{"comment": {"body": "Ah... so `getLineExtentRange`'s purpose is to always extend the given range to the line extents, I guess the question here is whether we should be using this for the SourcePoint when we create a new discussion?  I'm totally open on that, we can only extend the range for the context and leave the SourcePoint with the precise characters...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#discussion_r901998477"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012747837", "body": ""}
{"comment": {"body": "FYI @richiebres is what I'm doing here with the character ranges correct?  ie, zero (\"beginning\") and MAX_LEN (\"end\") characters should be stored in SourcePoints as undefined?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#discussion_r901998799"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012750568", "body": ""}
{"comment": {"body": "> only extend the range for the context and leave the SourcePoint with the precise characters...\n\n\n\nyeah, this is what I would expect. remember that we can always _render_ the source point with an extended line range in clients (and I think we should do this at first at least), but it would be nice to have the flexibility to change this later. We don't have the ability to change behaviour later unless we accurately capture the original input exactly.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2c974b00-610d-46c3-b9d8-40f9c937fbf3?message=9512d8ea-0841-40ab-a85a-44eac1066c5a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#discussion_r902000575"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012789659", "body": ""}
{"comment": {"body": "Makes sense, done.\r\n\r\nThere's a bug that's preventing testing this stuff end to end, but this generally seems to be working on the client: https://linear.app/unblocked/issue/UNB-316/created-sourcepoints-lose-their-columnend-value", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#discussion_r902030873"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012792469", "body": ""}
{"comment": {"body": "Thanks, I'll take that bug.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2c974b00-610d-46c3-b9d8-40f9c937fbf3?message=6b86132a-00ec-4287-86a9-011c2f1101b6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#discussion_r902033400"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012795973", "body": ""}
{"comment": {"body": "yup", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#discussion_r902036005"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012796068", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012811120", "body": ""}
{"comment": {"body": "Fixed: https://github.com/NextChapterSoftware/unblocked/pull/1922", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#discussion_r902047402"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#pullrequestreview-1012818982", "body": ""}
{"comment": {"body": "Thanks!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1912#discussion_r902053685"}}
{"title": "Add at mentions to contributors list", "number": 1913, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913", "body": "We need the ability to add at mentions to contributors list in create discussion form.\nTo do that:\n1. We create new editor extensions for Mentions with function insertMentionNode. We piggy back on that call to trigger a callback onMention passed to MessageEditor.\n2. We modify KnowledgeForm functionality to add mentioned contributor.\n3. We modify updatedContributorsWithIdentity functionality to allow for using teamMemberId to find identity.\n4. We fix bug with addContributor functionality as it was using email for contributor id.\nOther Changes:\n1. Followed @matthewjamesadam recommendation to move away from weird nested callback behaviour for onKeyDown and onChange handlers. I agreed with him.\nTODO:\n1. Handle @mention deletions (pain in the ass, kind of).\n2. Annoyingly, we hash emails for teamMembers. This is annoying as I now have to make an extra call to get the email for the team member."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#pullrequestreview-1012741498", "body": ""}
{"comment": {"body": "bug", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#discussion_r901994346"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#pullrequestreview-1012741652", "body": ""}
{"comment": {"body": "if teamMemberId, why do a search by email :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#discussion_r901994453"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#pullrequestreview-1014255123", "body": ""}
{"comment": {"body": "Nice way of adding operations to the editor!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#discussion_r903075667"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#pullrequestreview-1014256574", "body": "I know you already merged this, just wanted to say: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#pullrequestreview-1014518502", "body": ""}
{"comment": {"body": "@pwerry\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/b70fe80b-c26f-4d26-971e-8dc2f7ddfbff?message=9de34a30-5b40-4dec-ad76-598b4bbd8575).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#discussion_r903274245"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#pullrequestreview-1014518556", "body": ""}
{"comment": {"body": "@mahdi-torabi\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/b70fe80b-c26f-4d26-971e-8dc2f7ddfbff?message=b2ad7aaf-19b4-48f1-9248-8f5fbb94c273).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1913#discussion_r903274283"}}
{"title": "Switch to new version scheme", "number": 1914, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1914", "body": "Making changes mentioned here \nLatest build no: 184\nFilename: unblocked-installer-1.0.184.pkg"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1914#pullrequestreview-1012828717", "body": ""}
{"comment": {"body": "ha! clever", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1914#discussion_r902061330"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1914#pullrequestreview-1012828812", "body": ""}
{"title": "Fix email alignment", "number": 1915, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1915", "body": "\nthis specific email was somehow right aligned from the extra td element. I noticed that this block was wrapped in an if conditional in the other templates but not this one -- tested on parcel and it seems to be aligned properly now? will test manually once deployed to be sure"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1915#pullrequestreview-1012752099", "body": ""}
{"comment": {"body": "^ this specific block here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1915#discussion_r902001726"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1915#pullrequestreview-1012753483", "body": "Thank you!"}
{"title": "Use commit hash as backup for source mark resolution", "number": 1916, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916", "body": "In case we are unable to generate fileHash, use commit hashes on page to find source marks"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#pullrequestreview-1012779576", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#pullrequestreview-1012780896", "body": ""}
{"comment": {"body": "By using only commit hashes as backup and not having a file hash, we could fail for the reason outlined here: https://github.com/NextChapterSoftware/unblocked/pull/1813#discussion_r897426258", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#discussion_r902024229"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#pullrequestreview-1012786426", "body": "This will partially work at least. If we can't figure out how to get the fileHash then I have a suggestion"}
{"comment": {"body": "The failure scenario occurs when a user annotates a SourceMark on any commit between the latest modified commit on the file and the commit that the user is observing in the web.\r\n\r\nIf we can't figure out a way to do this, then one option is to backtrack the original point to the latest modified commit before the annotated commit; but we'd have to do this for _every_ original SourcePoint, which incurs significant storage complexity.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#discussion_r902028366"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#pullrequestreview-1012798435", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#pullrequestreview-1012799063", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#pullrequestreview-1012799135", "body": ""}
{"comment": {"body": "We should now be able to include file hashes in find requests.\r\n\r\nI still plan on keeping the `fetchFromCommitHashes` as a backup just in case file hash generation fails.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#discussion_r902038629"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#pullrequestreview-1012799404", "body": ""}
{"comment": {"body": "That makes sense, if GitHub changes their endpoints or whatever, having a failover will be useful", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#discussion_r902038822"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1916#pullrequestreview-1012812108", "body": ""}
{"title": "Adds HMAC secret validation for github webhook events", "number": 1917, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1917"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1917#pullrequestreview-1012776858", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1917#pullrequestreview-1012781392", "body": ""}
{"comment": {"body": "fancy", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1917#discussion_r902024574"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1917#pullrequestreview-1012782603", "body": ""}
{"comment": {"body": "lols", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1917#discussion_r902025514"}}
{"title": "Nothing to see here", "number": 1918, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1918"}
{"comment": {"body": "\ud83d\udc40 \ud83d\udc40 \ud83e\udd14 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1918#issuecomment-1160906294"}}
{"title": "Add test for merging an open PR", "number": 1919, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1919"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1919#pullrequestreview-1012813543", "body": ""}
{"title": "Rename [4 of 4]: Message relates to SourceMark", "number": 192, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/192", "body": "Continue renaming:\n\nSourceMark  SourceMarkGroup (#186)\nSourceMark  SourceMarkGroup in API (#189)\nSourceVector  SourceMark (#191)\nRelate SourceMark  Message (this change)\n\n\n"}
{"comment": {"body": "Can we add cardinalities to the diagram?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/192#issuecomment-1026310830"}}
{"comment": {"body": "One more change after this to rename `Chat` to `Thread`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/192#issuecomment-1026425881"}}
{"title": "Update json", "number": 1920, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1920"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1920#pullrequestreview-1012800663", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1920#pullrequestreview-1012801363", "body": ""}
{"title": "fix link", "number": 1921, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1921"}
{"title": "Fix sourcepoint column bug", "number": 1922, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1922"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1922#pullrequestreview-1012811390", "body": ""}
{"comment": {"body": "this is the fix", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1922#discussion_r902047679"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1922#pullrequestreview-1012812696", "body": ""}
{"title": "PullRequestIngestionJob pulls from the priority queue before taking from the non-priority queue", "number": 1923, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1923", "body": "The job should continue to pick off the priority queue until its empty before taking from the non-priority queue."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1923#pullrequestreview-1012818158", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1923#pullrequestreview-1017830281", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1923#pullrequestreview-1017945969", "body": ""}
{"title": "Add team member store", "number": 1924, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1924"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1924#pullrequestreview-1012820538", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1924#pullrequestreview-1012821452", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1924#pullrequestreview-1012821772", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1924#pullrequestreview-1012821878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1924#pullrequestreview-1012822083", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1924#pullrequestreview-1012822720", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1924#pullrequestreview-1012824462", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1924#pullrequestreview-1012824493", "body": ""}
{"title": "AWS SDK retries by default so remove unnecessary override", "number": 1925, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1925", "body": "Since the SES retry config is identical without and without the config override, I'm just cleaning up to avoid confusion.\nFrom debugger, AWS SDK retries by default:"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1925#pullrequestreview-**********", "body": ""}
{"title": "scale up webhooks service", "number": 1926, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1926", "body": "Scale up web hook service in prod \nDeployed new EKS service account to grant scm service access to hooks_scm queue"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1926#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1926#pullrequestreview-**********", "body": ""}
{"title": "Hook up webhook-service to SQS queue", "number": 1927, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1927"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1927#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1927#pullrequestreview-**********", "body": ""}
{"title": "Serialize GitHub webhook headers properly", "number": 1928, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1928"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1928#pullrequestreview-**********", "body": ""}
{"title": "Webhook Handler Plumbing", "number": 1929, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1929", "body": "Just stubs"}
{"comment": {"body": "How do you feel about making these injectable to help with testability", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1929#issuecomment-1161245661"}}
{"comment": {"body": "I can do it if no objections", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1929#issuecomment-1161250584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1929#pullrequestreview-1012940061", "body": ""}
{"title": "Move components into shared directory", "number": 193, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193", "body": "Move Icon and UserIcon components to the shared/ folder\nMove common styles into the shared directory and leveraging @forward to reference the shared styles in the client directories\nAdd webUtils folder for shared consumption"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-868562984", "body": ""}
{"comment": {"body": "not sure if `styles/` should live under webComponents or directly under shared/", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#discussion_r796173378"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-868563534", "body": ""}
{"comment": {"body": "moved shared/ package.json up a level. will require everyone to delete the node_modules in `shared/webComponents/`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#discussion_r796173793"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-868564204", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-869850312", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-869853777", "body": ""}
{"comment": {"body": "This works? This path seems a bit off to me.\r\nThe include below should have already moved the reference up a level.\r\n\r\nSo I think this should be './webComponents/styles'?\r\n\r\nThis works?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#discussion_r797045879"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-869855536", "body": ""}
{"comment": {"body": "This is in the `.storybook` subfolder so I think this is right...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#discussion_r797047613"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-869857296", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-869859648", "body": ""}
{"comment": {"body": "it works -- @jeffrey-ng does it look off because it has to traverse into `webComponents/`? i.e. should `styles/` live as directly under shared instead of inside webComponents? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#discussion_r797051767"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-869860294", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-869861820", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-869864952", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/193#pullrequestreview-871022437", "body": ""}
{"title": "Wire up pull request webhook handlers", "number": 1930, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1930"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1930#pullrequestreview-1012983232", "body": ""}
{"title": "Reduce max threads from 25 to 10 in WebExt, Web, VSCode", "number": 1931, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1931", "body": "Cleaner, more focused."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1931#pullrequestreview-1013872525", "body": ""}
{"title": "Don't handle webhooks for messages created from Unblocked", "number": 1932, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1932", "body": "When we reply to a PR thread from Unblocked, that will lead to a webhook sent to us after we post to GitHub, causing us to create that message (again).\nLet's ignore the create event if the message has an Unblocked signature. \nYup, super hacky but not sure if there's a better way. Anyways, this will address in the meantime while we think of something better."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1932#pullrequestreview-1013046843", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1932#pullrequestreview-1013050080", "body": ""}
{"title": "Wire up TeamMember push", "number": 1933, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933"}
{"comment": {"body": "Let's hold onto this one until after the demo tomorrow. We haven't tested what will happen when teammember push is magically turned on in the clients", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#issuecomment-1162450521"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1013890903", "body": ""}
{"comment": {"body": "Keeping the old endpoint around for a week or so", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r902818854"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1013963965", "body": ""}
{"comment": {"body": "mark as deprecated so that we remember to remove\r\n```yaml\r\ndeprecated: true\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r902870599"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014076373", "body": ""}
{"comment": {"body": "Remove.\r\nHe's not demoing. Just remove.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r902949356"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014121560", "body": ""}
{"comment": {"body": "Hmm, this has me thinking. Maybe we can tie API versioning into the client versioning approach that you introduce Pete. We have all of the information we need to determine if an API operation is used by a non-obsolete version, so we could automate this stuff in theory. (Future thing)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r902981545"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014169444", "body": ""}
{"comment": {"body": "Yeah I was thinking about this yesterday, we maybe don't need an explicit API version, but I think we'd likely have to track or store the API spec for every build so that we can correlate which API operations are used in each build...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r903014413"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014241171", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014242104", "body": ""}
{"comment": {"body": "He's demoing tomorrow morning...\r\n\r\nAs for versioning - it would require us to maintain an \"obsoletion\" matrix, which can get a little hairy. We'll have to science this a little bit because getting it wrong is costly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r903065955"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014247347", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014340121", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014349070", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014374676", "body": ""}
{"comment": {"body": "> ... but I think we'd likely have to track or store the API spec for every build so that we can correlate which API operations are used in each build...\n\n\n\nGit does the tracking already, so we'd get this for free. We would still need a build-time validator (that runs in CI) that checks that the removed API operation is safe to remove.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/22aea6b2-2025-4d33-bd9a-958f3210552b?message=fa3460bf-249c-4eb8-b8a6-7027acf73d68).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r903166528"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014509043", "body": ""}
{"comment": {"body": "@jeffrey-ng\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/b3ddd84a-4fa0-4e3b-b67b-a1b33a246b97?message=708714fe-5530-4a5e-8ed7-717107069fbb).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r903267015"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014509355", "body": ""}
{"comment": {"body": "@pwerry\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/b3ddd84a-4fa0-4e3b-b67b-a1b33a246b97?message=f3deee14-6cfb-4463-a271-dcda41124b4e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r903267223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1014519220", "body": ""}
{"comment": {"body": "adfsads\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/b3ddd84a-4fa0-4e3b-b67b-a1b33a246b97?message=ffef8a78-b916-46e9-8468-49e5f98ad0fa).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r903274779"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#pullrequestreview-1017568637", "body": ""}
{"comment": {"body": "testing post from dev\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/b3ddd84a-4fa0-4e3b-b67b-a1b33a246b97?message=c557413e-b6dc-418b-808c-82bd97bb4eba).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1933#discussion_r905405896"}}
{"title": "Dont create the message or thread if the comment exists", "number": 1934, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1934", "body": "Follow up to https://github.com/NextChapterSoftware/unblocked/pull/1932, though think that PR is still needed since there is a possible race condition here."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1934#pullrequestreview-1017382544", "body": ""}
{"comment": {"body": "```suggestion\r\n            return@suspendedTransaction\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1934#discussion_r905276152"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1934#pullrequestreview-1017382965", "body": ""}
{"comment": {"body": "^ignore, I'm just testing something", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1934#discussion_r905276608"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1934#pullrequestreview-1022542260", "body": ""}
{"comment": {"body": "Is this ID unique across all repos in the team? i.e are you sure we don't need to add repoId?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/f2563c2c-4ac5-4343-a498-ed69b1be9acd?message=f18cfe2e-0d86-441f-bec8-b9a015e68b98).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1934#discussion_r909048413"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1934#pullrequestreview-1022695934", "body": ""}
{"comment": {"body": "Ah good call. I'm pretty sure its unique to the team but adding that to the where clause wouldn't hurt", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1934#discussion_r909168211"}}
{"title": "New Stream overlay", "number": 1935, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1935", "body": "Add a new StreamOverlay class.  This allows for temporary mutations in the middle of a Stream.  Hopefully the comments make it clear what I'm going for here, the idea is to make something generic that can be used in any stream.\nI got rid of the ApiDataStreamOverlay as it wasn't being used.  This will supersede the DataCacheStreamOverlay once it's done.\nThis PR just adds the basic framework.  I'm hooking this into stores in the next PR that stacks on this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1935#pullrequestreview-1014109871", "body": ""}
{"comment": {"body": "This is a little unfortunate but I can see why it's necessary...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1935#discussion_r902969497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1935#pullrequestreview-1014110244", "body": ""}
{"comment": {"body": "An alternative that is probably more work is to make the overlays array into a publisher itself.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1935#discussion_r902969837"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1935#pullrequestreview-1014111750", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1935#pullrequestreview-1014112482", "body": ""}
{"comment": {"body": "I actually started out with the stream publishing the overlays directly, the problem is that we actually mutate the overlay list in the middle of pipeline processing (~ line 56), so that means we can't pass it into its own stream...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1935#discussion_r902972307"}}
{"title": "Get AgentType from ViewThread operation call", "number": 1936, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#pullrequestreview-1014047622", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#pullrequestreview-1014060525", "body": ""}
{"comment": {"body": "make it required, then you don't need the null check in the application code", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#discussion_r902940272"}}
{"comment": {"body": "I think the definition of `productAgent` is wrong -- it should be required for the version use case too", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#discussion_r902940941"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#pullrequestreview-1014071075", "body": ""}
{"comment": {"body": "I think it'll impact the clients? https://chapter2global.slack.com/archives/C02HEVCCJA3/p1655835539838969?thread_ts=1655833976.841769&cid=C02HEVCCJA3\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a8c34d69-1b9d-4c24-a6cb-53b8aa26da73?message=39d945d8-11aa-4199-82ba-fcb3a2752b45).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#discussion_r902945882"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#pullrequestreview-1014072524", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#pullrequestreview-1014116181", "body": ""}
{"comment": {"body": "yeah, fuck this is really dumb.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#discussion_r902976325"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#pullrequestreview-1014117784", "body": ""}
{"comment": {"body": "cc @matthewjamesadam @pwerry be great if we could figure out a solution to this, if feasible. These product headers _are_ actually required, but the service needs to assume they are optional.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#discussion_r902977957"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#pullrequestreview-1014117920", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#pullrequestreview-1014176158", "body": ""}
{"comment": {"body": "Can we just process this at runtime for now until we figure out a better option?  And we would have to add all of these headers to every single API operation, right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#discussion_r903019005"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#pullrequestreview-**********", "body": ""}
{"comment": {"body": "> \u00a0And we would have to add all of these headers to every single API operation, right?\n\n\n\nI believe the spec language allows you to add them to every operation _by default_; so we'd just remove them for the health check and webhook operations.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a8c34d69-1b9d-4c24-a6cb-53b8aa26da73?message=6c921f24-92f6-4316-800a-c7085954726a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1936#discussion_r903171606"}}
{"title": "Adds HMAC validation for GH webhooks", "number": 1937, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-**********", "body": ""}
{"comment": {"body": "add `return` -- right now this code will continue to process the payload that has been tampered ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#discussion_r902944529"}}
{"comment": {"body": "would be caught with test ;) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#discussion_r902944728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1014070317", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1014073999", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1014075990", "body": ""}
{"comment": {"body": "This is intentional. We are going to let this slide temporarily and log, then enforce in a followup", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#discussion_r902949030"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1014114605", "body": ""}
{"comment": {"body": "ah, sorry, I forgot \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#discussion_r902974699"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1014284020", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1017568849", "body": ""}
{"comment": {"body": "testing post from dev\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/7da16d45-568b-4588-aa42-349043c068b7?message=a04a726a-90f8-4757-bcf6-d640e6d466be).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#discussion_r905406035"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1017575066", "body": ""}
{"comment": {"body": "test ingest", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#discussion_r905410235"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1022105192", "body": ""}
{"comment": {"body": "pete\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/7da16d45-568b-4588-aa42-349043c068b7?message=febdcc80-902e-4380-9349-a0d3c0043414).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#discussion_r908735058"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1022105824", "body": ""}
{"comment": {"body": "sdfsdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/7da16d45-568b-4588-aa42-349043c068b7?message=82131962-8d8f-4133-aa5d-9fcefc21f7eb).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#discussion_r908735913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#pullrequestreview-1022105856", "body": ""}
{"comment": {"body": "dfgdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/7da16d45-568b-4588-aa42-349043c068b7?message=3e341dc7-7e08-4613-ae15-197b4169f316).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1937#discussion_r908735973"}}
{"title": "Delete mentioned contributors", "number": 1938, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1938", "body": ""}
{"title": "Client triggered thread events", "number": 1939, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939", "body": "Trigger thread events once a day per user/team tuple.\nSimilar pattern to how BaseAPI was setup."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1014094806", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1014457267", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1014472973", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1014558864", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1014562070", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1014565421", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1014566521", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1015511422", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1015516041", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1015524142", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1015539683", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1015540728", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1015636936", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1016098882", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1016106891", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1017284552", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1017286593", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1017296818", "body": "I'm not sure we should be issuing this event when we create threads, but other then that looks good"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1017298635", "body": ""}
{"comment": {"body": "@davidkwlam Verifying that we want a viewThread event after thread creation?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#discussion_r905219694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1017365317", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#pullrequestreview-1017440418", "body": ""}
{"comment": {"body": "Yes, thanks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1939#discussion_r905318164"}}
{"title": "Add more logging", "number": 194, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/194"}
{"title": "[Onboarding] Spacing and bug fixes", "number": 1940, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1940", "body": "part of "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1940#pullrequestreview-1014165930", "body": ""}
{"title": "Fix button styling", "number": 1941, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1941"}
{"comment": {"body": "> If we're not consolidating the two buttons, you'll likely need to do these changes as well\r\n> \r\n> * https://github.com/NextChapterSoftware/unblocked/pull/1940/files#diff-64fb557a58021c38d649f10a9807bcf2ba869df600b12f7c385d7271925a7959R9\r\n> * https://github.com/NextChapterSoftware/unblocked/pull/1940/files#diff-7b44b728406a3d0aba4810945f931714acd8bae4a45ae738dc09478b4a51285eR34\r\n> \r\n> and note that the buttons are `large` by default\r\n> \r\n> * https://github.com/NextChapterSoftware/unblocked/pull/1940/files#diff-7b44b728406a3d0aba4810945f931714acd8bae4a45ae738dc09478b4a51285eR32\r\n\r\nThe typing for the InstallButtons are a bit constrained without a larger refactor which I am not planning on doing right now.\r\nWill make the changes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1941#issuecomment-1162350660"}}
{"comment": {"body": "<img width=\"1212\" alt=\"CleanShot 2022-06-21 at 14 02 33@2x\" src=\"https://user-images.githubusercontent.com/1553313/174896829-7c9cf299-710e-4ad9-a97a-a023427ecf4d.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1941#issuecomment-1162358151"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1941#pullrequestreview-1014192257", "body": "If we're not consolidating the two buttons, you'll likely need to do these changes as well \n\nhttps://github.com/NextChapterSoftware/unblocked/pull/1940/files#diff-64fb557a58021c38d649f10a9807bcf2ba869df600b12f7c385d7271925a7959R9\nhttps://github.com/NextChapterSoftware/unblocked/pull/1940/files#diff-7b44b728406a3d0aba4810945f931714acd8bae4a45ae738dc09478b4a51285eR34\n\nand note that the buttons are large by default \n* https://github.com/NextChapterSoftware/unblocked/pull/1940/files#diff-7b44b728406a3d0aba4810945f931714acd8bae4a45ae738dc09478b4a51285eR32"}
{"title": "Dashboard redirect route", "number": 1942, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1942", "body": "Add support for dashboard redirecting.\nWhen VSCode triggers GH Install, it will navigate users to this dashboard install route /install with two query parameters.\n1. the target location\n2. The redirect location for /install/redirect which is stored in LocalStorage\nOnce GH install is done, GH will redirect to /install/redirect. If the localStorage variable exists, will promptly redirect immediately.\nUI is TODO need designs.\nVSCode implementation is also todo."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1942#pullrequestreview-1014549585", "body": ""}
{"comment": {"body": "Doesn't matter for this PR, but I'm wondering in general if we should start centralizing all the local/session storage values into one place.  Right now it's pretty scattered, it might help to have a central class for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1942#discussion_r903297763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1942#pullrequestreview-1014549617", "body": ""}
{"title": "Move stores away from caches directory", "number": 1943, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1943"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1943#pullrequestreview-1014266241", "body": ""}
{"comment": {"body": "Ignore this warning. It's not real", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1943#discussion_r903083993"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1943#pullrequestreview-1014303215", "body": ""}
{"comment": {"body": "Hello\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0c2afe82-9958-4961-a65a-2802af1a9bf2?message=f831175e-c28a-41ee-a490-866628c6c15a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1943#discussion_r903111883"}}
{"title": "Handle SM exception", "number": 1944, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1944", "body": "Handle exceptions thrown by the sourcemark engine when querying for sourcemarks in a file.  If we don't handle exceptions here, the sourcemark gutter will not render at all."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1944#pullrequestreview-1014300571", "body": ""}
{"comment": {"body": "What errors are typically thrown? The reason I ask is that there is a specific race scenario, identifiable from the `RangeAdjustContentRace` error, which the caller must retry.\r\n\r\nSee this comment block for detailed explanation:\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/blob/041e24c53ab526c2fe7363d4558eda0ea994fe74/vscode/src/sourcemark/SourceMarkCalculator.ts#L667-L690", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1944#discussion_r903109891"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1944#pullrequestreview-1014303407", "body": ""}
{"comment": {"body": "It was this error: https://linear.app/unblocked/issue/UNB-114/vscode-exception-diff-content-is-not-based-off-the-old-point.\n\n\n\nI didn't have any uncommitted or unsaved changes of any kind -- a clean checked-out detached HEAD.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ee365625-dcc5-4a1a-9917-ee3b8d71a37b?message=7661292b-b5ee-4dd7-85c2-db0f8cdd8d34).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1944#discussion_r903112164"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1944#pullrequestreview-1014361683", "body": ""}
{"comment": {"body": "Chatted IRL. Just going to capture the errors for now. If we find occurrences of `RangeAdjustContentRace` then we can take further action, but we just need to measure for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1944#discussion_r903158168"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1944#pullrequestreview-1014361734", "body": ""}
{"title": "Update title", "number": 1945, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1945", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1945#pullrequestreview-1014274001", "body": ""}
{"title": "Fix Refresh Issue in Web Extension", "number": 1946, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1946", "body": "Were not properly cleaning out the auth promise singleton in most failure cases.\nThis led to never actually refreshing."}
{"comment": {"body": "@pwerry Should fix your issue", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1946#issuecomment-1162409215"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1946#pullrequestreview-1014298248", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1946#pullrequestreview-1014305181", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1946#pullrequestreview-1014309277", "body": ""}
{"title": "Adds success logging for HMAC auth", "number": 1947, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1947"}
{"title": "Log auth error and delete message from scm webhook queue", "number": 1948, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1948"}
{"title": "Dashboard updates", "number": 1949, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1949", "body": "Quick pass at dashboard so that things don't look too broken in smaller viewports\n\n\n\nSort user icon stack by latest message authored\n\n\n\nother small nits"}
{"comment": {"body": "The mobile view looks great!\r\n\r\nA couple of points about the icon stack:\r\n1. needs to be consistent with the Hub icon stack\r\n2. currently the icon stack is ordered chronologically from left to right, meaning that the thread author (the person who posed the question) is consistently the first person in the stack, which I find beneficial. unclear why we'd change that.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1949#issuecomment-1162480222"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1949#pullrequestreview-1014551274", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1949#pullrequestreview-1015486726", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1949#pullrequestreview-1015572084", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1949#pullrequestreview-1015578801", "body": ""}
{"title": "Update service tokens", "number": 195, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/195"}
{"title": "Message operations are now reflected in the UI immediately", "number": 1950, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1950", "body": "Message operations (create, update, remove) are reflected immediately in the main thread UI.  Updates are not reflected in the thread listing UIs in the sidebar, that will come in a future PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1950#pullrequestreview-1014368439", "body": ""}
{"comment": {"body": "Sorry the diff for this file got kind of messed up, because I removed a bunch of dead code. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1950#discussion_r903163411"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1950#pullrequestreview-1016073651", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1950#pullrequestreview-1016073697", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1950#pullrequestreview-1016108952", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1950#pullrequestreview-1016119684", "body": ""}
{"title": "update API resource service versions", "number": 1951, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1951", "body": "Several API resources have been moved out of beta so we have to update helm charts before upgrading to Kube 1.22\n- Modified helm charts to use v1 version for Ingress related APIs \n- Regenerated helm charts. \nDetails here: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1951#pullrequestreview-1014503858", "body": ""}
{"title": "message mentions", "number": 1952, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1952"}
{"title": "Calculate DAU, WAU, and MAU", "number": 1953, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953", "body": "Adds two new models\n\nUserEngagementModel <- captures user activity events\nUserEngagementMetricsModel <- stores daily, weekly, and monthly active users metrics\n\nThe admin console will run a job periodically every hour checking to see if the previous days metrics have been calculated and if not, it will do so.\nDAU is calculated as the unique active users between 00:00:00 PST and 23:59:59.999999 PST for a given day.\nWAU is calculated for a 7-day period starting at 00:00:00 PST on the first day and ending 23:59:59.999999 PST on the last day. \nMAU is calculated like WAU except for a 30-day period.\n(PST was chosen arbitrarily here)\nAdmin console is a work in progress"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1015668400", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1015670795", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1015671176", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1015675892", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1015678183", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1015722777", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1016027233", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1016282410", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1016283403", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1017398018", "body": ""}
{"comment": {"body": "don't really understand this pattern of consuming tests from other modules", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#discussion_r905289005"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#pullrequestreview-1017420179", "body": ""}
{"comment": {"body": "I think we need a `lib-test` to store shared test logic", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1953#discussion_r905304262"}}
{"title": "rename", "number": 1954, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1954"}
{"title": "Ability to soft-delete a team", "number": 1955, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1955", "body": "It's safe, because it's reversible.\nIt's fast, because it just updates a single model.\n\nFuture intent:\n1. release the ability to delete a team as functionality to end-users\n2. automatically purge soft-deleted teams 60 days after soft deletion (use modifiedAt)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1955#pullrequestreview-1015684238", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1955#pullrequestreview-1015686067", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1955#pullrequestreview-1015686239", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1955#pullrequestreview-1015690104", "body": "Rock and roll"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1955#pullrequestreview-1015691776", "body": ""}
{"title": "GitHub Webhook Event Fixtures and Derserializers", "number": 1956, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1956", "body": "New events\nNew deserialization tests for events above\nNew handlers, mostly stubs for now (because this PR is getting massive)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1956#pullrequestreview-1017392740", "body": " mostly stubs"}
{"comment": {"body": "You'll do this in another PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1956#discussion_r905285138"}}
{"comment": {"body": "You'll do this in another PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1956#discussion_r905285188"}}
{"comment": {"body": "Another PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1956#discussion_r905286389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1956#pullrequestreview-1017399325", "body": ""}
{"comment": {"body": "yup, this one is way too big already", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1956#discussion_r905289891"}}
{"title": "Rename Events to Metrics", "number": 1957, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1957"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1957#pullrequestreview-1015602042", "body": ""}
{"title": "Testing cpu related jvm args", "number": 1958, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1958", "body": "Introduced an environment variable to allow for adding extra JVM args from Kube deployment spec. \nAdded a JVM CPU arg to prefer CPU quota over CPU shares. \n\nGoing based on info provided here: \nThe goal is to better understand CPU requirements of JVM with regards to multi-threaded apps so we could standardize our API instance resources. Once done we can simply scale out the number of pods to accommodate more users rather than constantly tuning JVM params."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1958#pullrequestreview-1015671985", "body": ""}
{"title": "Fix stuff", "number": 1959, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1959"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1959#pullrequestreview-1016054227", "body": ""}
{"comment": {"body": "Doesn't this mean we will be re-rendering pretty much all the time, as we're re-setting this state on every change?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1959#discussion_r904353243"}}
{"title": "Rename Chat -> Thread", "number": 196, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/196", "body": "Follow on from #192."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/196#pullrequestreview-868668642", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/196#pullrequestreview-868669784", "body": ""}
{"title": "bump", "number": 1960, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1960"}
{"title": "Revert \"Testing cpu related jvm args\"", "number": 1961, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1961", "body": "Reverts NextChapterSoftware/unblocked#1958\n```\nOperating System Metrics:\n    Provider: cgroupv1\n    Effective CPU Count: 1\n    CPU Period: 100000us\n    CPU Quota: 40000us\n    CPU Shares: 204us\n    List of Processors, 4 total:\n    0 1 2 3\n    List of Effective Processors, 4 total:\n    0 1 2 3\n    List of Memory Nodes, 1 total:\n    0\n    List of Available Memory Nodes, 1 total:\n    0\n    Memory Limit: 512.00M\n    Memory Soft Limit: Unlimited\n    Memory & Swap Limit: 512.00M\nopenjdk version \"17.0.2\" 2022-01-18\nOpenJDK Runtime Environment (build 17.0.2+8-86)\nOpenJDK 64-Bit Server VM (build 17.0.2+8-86, mixed mode, sharing)\nError: Could not find or load main class\nCaused by: java.lang.ClassNotFoundException:\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1961#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1961#pullrequestreview-**********", "body": ""}
{"title": "Setup web ignore fallback dialog", "number": 1962, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1962", "body": "\n\nUI is a bit different from the designs. The colours, sizes & etc. seem quite custom for this UI at the moment and I'm wondering if the large styling refactor would fix a lot of those issues..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1962#pullrequestreview-1016058328", "body": ""}
{"title": "Brings in new update dialog designs, minus stars effect", "number": 1963, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1963", "body": "\n"}
{"comment": {"body": "Can we add a minimize button? Maybe I don't to update right now (eg: I'm currently presenting in a meeting).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1963#issuecomment-1163589677"}}
{"comment": {"body": "> Can we add a minimize button? Maybe I don't to update right now (eg: I'm currently presenting in a meeting).\r\n\r\nUnlike the settings window, there's no other way to bring this window to the foreground besides clicking on it in the dock. I think the idea here is that they can close it, and just open the installer from the hub settings menu. There will be indicators galore to lead them down the right path. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1963#issuecomment-1163845684"}}
{"title": "Test integration", "number": 1964, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1964"}
{"title": "Add thread-level context menu", "number": 1965, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965", "body": "dashboard\n\nextension\n\nvscode\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#pullrequestreview-1016069737", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#pullrequestreview-1016083037", "body": ""}
{"comment": {"body": "Is this basically a debouncer for a string state?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#discussion_r904372051"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#pullrequestreview-1016083586", "body": ""}
{"comment": {"body": "The previous wording was odd in the context of the dashboard where there isn't anything being removed (from the dashboard). The alternative is to specify that the sourcemarks will be removed from the code editor but unclear to me if that is necessary here ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#discussion_r904372498"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#pullrequestreview-1016090886", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#pullrequestreview-1016094639", "body": ""}
{"comment": {"body": "If so there's already a `useDebounce` that might work?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#discussion_r904377003"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#pullrequestreview-1016096835", "body": ""}
{"comment": {"body": "(`useDebounce` has some issues I'm seeing in PR feedback... you could also directly use the `Debounce.ts` code, which actually debounces correctly)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#discussion_r904378128"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#pullrequestreview-1016097491", "body": ""}
{"comment": {"body": "It's not really a debouncer, it's just changing the string temporarily before resetting it\r\n\r\nthough I suppose it could use a debouncer ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#discussion_r904378250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#pullrequestreview-1019246167", "body": ""}
{"comment": {"body": "[test](https://www.google.com/)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1965#discussion_r906616213"}}
{"title": "Change CPU allocation to quota based", "number": 1966, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1966", "body": "Changing java cpu allocation to take quota based allocation over cpu share based one. Tested locally with our current JDK version and hopefully this time it won't break the deployment. \n\nUnlike cpu.share, the quota is based on time period and not based on available CPU power. cfs_period_us is used to define the time period, its always 100000us (100ms). k8s has an option to allow to change this value but still alpha and feature gated. The scheduler uses this time period to reset the used quota. The second file, cfs_quota_us is used to denote the allowed quota in the quota period."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1966#pullrequestreview-1015891197", "body": ""}
{"comment": {"body": "Worse comes to worse we use this:\r\n -XX:ActiveProcessorCount", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1966#discussion_r904239294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1966#pullrequestreview-1015891259", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1966#pullrequestreview-1015891776", "body": ""}
{"comment": {"body": "Yep that's my next step. Trying this first to see if we can get a baseline guaranteed perf.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1966#discussion_r904239757"}}
{"title": "Jeff/unb 327 web extension interstitial dialog", "number": 1967, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1967"}
{"title": "Revert \"Change CPU allocation to quota based\"", "number": 1968, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1968", "body": "Reverts NextChapterSoftware/unblocked#1966\nI think I have figured out the issue but want to keep this on hand so I can test without it later tonight."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1968#pullrequestreview-1016060399", "body": ""}
{"title": "Align the search input styling with other search inputs in vscode", "number": 1969, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1969", "body": "placeholder\n\nwith text\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1969#pullrequestreview-1016070446", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1969#pullrequestreview-1016075534", "body": ""}
{"comment": {"body": "for some reason storybook wouldn't build at all with the alias here -- not sure what's the issue with just this one ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1969#discussion_r904367069"}}
{"title": "Workflow: separately name check-for-changes jobs", "number": 197, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/197", "body": "Allows us to reference the workflow-specific check-for-changes jobs."}
{"title": "Added dev gh app", "number": 1970, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1970", "body": "Needed for Installation Flow per env."}
{"title": "Add new dev conf", "number": 1971, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1971", "body": "Setup new GH App for dev.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1971#pullrequestreview-1016175404", "body": ""}
{"comment": {"body": "How does one add new secrets?\r\n\r\nThink this is the only thing missing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1971#discussion_r904410068"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1971#pullrequestreview-1016182261", "body": "TODO\n- [ ] add DEV secrets"}
{"comment": {"body": "DEV and local environments should use the DEV app. Only PROD should use the prod app.\r\n\r\nSo we need to move this block to `global.conf`, and move what is currently in `global.conf` to `prod.conf`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1971#discussion_r904412782"}}
{"title": "Add icons for unread at mentions", "number": 1972, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972", "body": "This pr now shows the at mention icon when a user has been at mentioned for an unread message.\nThe at mention overrides unread icon.\nTESTING:\nFor the purposes of this demo, I made all messages unread when vended from the api service for the testing purposes. :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#pullrequestreview-1016226381", "body": ""}
{"comment": {"body": "I'm deliberating whether this should be done on the backend.\r\nFor now, this is sufficient.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#discussion_r904438759"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#pullrequestreview-1017350427", "body": ""}
{"comment": {"body": "Ah yeah -- it might be best to have this tagged onto some part of the message.  The way this works now, clients will be doing this work on every fetch for every single thread, which isn't ideal.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#discussion_r905255765"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#pullrequestreview-1017361162", "body": ""}
{"comment": {"body": "minor thing, but we ultimately only care about this as a boolean (ie, do we have unread mentions, or not) -- if you return a boolean instead of a number then there's somewhat less array creation/processing here?\r\n\r\n```\r\nreturn unreadMessages.some(message => message.mentions?.find(...))\r\n```\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#discussion_r905263244"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#pullrequestreview-1017363580", "body": ""}
{"comment": {"body": "Agreed. Was wondering if we woudl be using this for some sort of badge, but yeah.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#discussion_r905264981"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#pullrequestreview-1017365110", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#pullrequestreview-1017365581", "body": ""}
{"comment": {"body": "Will add a linear task for this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#discussion_r905266315"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#pullrequestreview-1017366512", "body": ""}
{"comment": {"body": "It's probably not the most important thing in the world TBH, but maybe it fits into a bigger conversation of how we should store and process message metadata...\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/152da649-6c22-4cc2-8b40-e63a6826659e?message=4baa14c8-65d1-4b42-a9dc-4c199abdd3ee).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#discussion_r905266987"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#pullrequestreview-1020943567", "body": ""}
{"comment": {"body": "\n\n\n\n\n\n\n\n\n\n@dennispi\n\n\n\n\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/e9157c72-f963-4a1e-a504-064db8ef23d0?message=47990770-550c-4a26-9f86-f0c687b5afa4).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#discussion_r907915537"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#pullrequestreview-1020945440", "body": ""}
{"comment": {"body": "\n\n\n\n@jeffrey-ng is lovely\n\n\n\n@matthewjamesadam is lovely\n\n\n\n@kaych is lovely\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/e9157c72-f963-4a1e-a504-064db8ef23d0?message=59cff0eb-0ac6-4535-8e58-497272048ac1).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1972#discussion_r907917071"}}
{"title": "Version Obsoletion behaviour in the Hub", "number": 1973, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1973", "body": "Upgrade dialog and hub state will look like this when the upgrade is forced:\n\nAlso modified update UX to match designs:"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1973#pullrequestreview-1017635469", "body": ""}
{"comment": {"body": "Just to check, if hasUpgrade true & updateAvailable false, this will go through. That expected?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1973#discussion_r905453448"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1973#pullrequestreview-1017635501", "body": ""}
{"title": "Re-orders avatars to put latest message author on top", "number": 1974, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1974", "body": ""}
{"comment": {"body": "@pwerry I think you can close this one yeah? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1974#issuecomment-1164872743"}}
{"comment": {"body": "Yup this is all wrong. I'll open a new PR for the new plan", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1974#issuecomment-1165126464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1974#pullrequestreview-1017268744", "body": "The idea is that the API should return the participants in the correct order, so this ultimately shouldn't be necessary?"}
{"title": "GitHub Dev App secrets and config", "number": 1975, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1975", "body": "Re-do of https://github.com/NextChapterSoftware/unblocked/pull/1971\nIncludes all GitHub app secrets, including webhook secret. GitHub App has been properly configured to expect these secrets"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1975#pullrequestreview-1017263715", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1975#pullrequestreview-1017297065", "body": ""}
{"title": "Thread operations display in UI immediately", "number": 1976, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976", "body": "Creating and deleting threads now takes effect immediately in the sidebar UIs.\nFixes UNB-192."}
{"comment": {"body": "@jeffrey-ng I'm gonna merge this today, let me know if you have any problems with it...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#issuecomment-1167610245"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#pullrequestreview-1017382346", "body": ""}
{"comment": {"body": "Almost all the interesting work in this PR is here, and the diff got a bit messed up.  Basically we're just making overlays for the thread listing streams.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#discussion_r905275954"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#pullrequestreview-1017382846", "body": ""}
{"comment": {"body": "Adds the new thread to the thread listing.  Note that we only apply this to the 'mine' stream.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#discussion_r905276478"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#pullrequestreview-1017384418", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#pullrequestreview-1017626755", "body": ""}
{"comment": {"body": "test test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#discussion_r905447012"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#pullrequestreview-1020722870", "body": ""}
{"comment": {"body": "For my own understanding, when this cancel occurs, the mock threadInfo will be removed from the stream?\r\nIs there any noticeable flicker when this occurs in the UI?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#discussion_r907756763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#pullrequestreview-1020723213", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#pullrequestreview-1020735150", "body": ""}
{"comment": {"body": "Yes, as soon as the cancel function returns true, the overlay is cancelled -- the intended usage is to drop the overlay once we get the expected data from the API (in this case, the API returns a thread with the ID we just added).  There is no flicker because the new (from API) value simply replaces the overlay, and the overlay is dropped forever after that point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1976#discussion_r907764934"}}
{"title": "Increase CPU limit and request in prod", "number": 1977, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1977", "body": "CPU requests are only used when scheduling pods. It's the CPU limit which controls how CGrpups treat a service and whether or not allow it to burst and use idle cpu cycles\nI'll be publishing a doc with all the details about how these values get configured. This is just a first round of increase in resources to prepare for some performance tests.\nThere will be a few more of these before we get the final numbers."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1977#pullrequestreview-1017485261", "body": ""}
{"title": "Update dropoff URL", "number": 1978, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1978", "body": "Continuation of https://github.com/NextChapterSoftware/unblocked/pull/1942\nVSCode will drop users off at the install route which drops off a redirect location before continuing to GH."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1978#pullrequestreview-1017622879", "body": ""}
{"title": "Update client icon stacking", "number": 1979, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1979", "body": "As part of \nVscode:\n\n\nExtension:\n\nDashboard:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1979#pullrequestreview-1018741317", "body": ""}
{"comment": {"body": "I wonder if this means we should have platform-specific icon stack scss files, so this doesn't bleed into other components?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1979#discussion_r906243027"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1979#pullrequestreview-1018743681", "body": "I wholeheartedly approve of this use of negative margins"}
{"title": "API cleanup", "number": 198, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/198", "body": "separate Threads and ThreadParticipants\nplural resources"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/198#pullrequestreview-868696431", "body": ""}
{"title": "FixEmails", "number": 1980, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1980", "body": "Fix emails\nUpdate\nUpdate"}
{"title": "Make spot instances use 4xlarge machines", "number": 1981, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1981", "body": "2xlarge defines the acceptable on-demand price. This will make the system use 4xlarge instances when available and under the 2xlarge price."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1981#pullrequestreview-1017580762", "body": ""}
{"title": "Add Open PR (green) gutter icons", "number": 1982, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1982", "body": "Fixes UNB-324. Handle all the various combination of thread types."}
{"comment": {"body": "I'd assume we'll need to do the same for the web extension??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1982#issuecomment-1164867402"}}
{"comment": {"body": "> I'd assume we'll need to do the same for the web extension??\r\n\r\nYes -- right now the \"multi\" icon in the web extension is very generic.  The problem there is that we can't reuse this code because of how we use images in all the clients: https://www.notion.so/nextchaptersoftware/TS-Client-Images-96790eadd15a480e9c9023c5965ae3e2.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1982#issuecomment-1164873151"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1982#pullrequestreview-1017640596", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1982#pullrequestreview-1017641103", "body": "all the colours "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1982#pullrequestreview-1017641370", "body": ""}
{"title": "Fix responsiveness", "number": 1983, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1983", "body": "fixes:\n\nafter:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1983#pullrequestreview-1018731013", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1983#pullrequestreview-1018749586", "body": ""}
{"comment": {"body": "Is this on purpose?  We want to remove titles?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1983#discussion_r906248397"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1983#pullrequestreview-1018752100", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/fda05318-f17c-4f36-8ba7-29cff4ea70f5)\n\n\n\nNew designs remove the title and just rely on the text in the nav elements\n\n\n\nIf we add more content to the nav bar we'll probably leverage title organization again but I think the thought is for now they're unnecessary\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/236c8cae-22fe-4d01-8aae-7c5d1a174cdb?message=3403df3a-5aaf-419c-85f7-f1b061a701f8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1983#discussion_r906250135"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1983#pullrequestreview-1018766735", "body": ""}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1983#discussion_r906259766"}}
{"title": "First pass at hacking together a metrics chart", "number": 1984, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1984", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1984#pullrequestreview-1017807368", "body": ""}
{"title": "Add user specific logging", "number": 1985, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1985"}
{"comment": {"body": "\ud83c\udf89 thanks!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1985#issuecomment-1165033468"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1985#pullrequestreview-1017823628", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1985#pullrequestreview-1017823846", "body": ""}
{"comment": {"body": "was producing verbose output for honeycomb.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1985#discussion_r905592308"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1985#pullrequestreview-1017824136", "body": "nice"}
{"title": "Jeff/unb 313 redirect back to unblocked after gh", "number": 1986, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986", "body": "Styling colours and font sizes is off from designs as we do not have these newish constants in our system.\nWill revisit with larger refactor in the coming days.\nError state when navigating from VSCode -> GH:\n\nComing back from GH -> VSCode:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#pullrequestreview-1017827384", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#pullrequestreview-1017827491", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#pullrequestreview-1017868779", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#pullrequestreview-1018695922", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#pullrequestreview-1020788704", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#pullrequestreview-1020789500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#pullrequestreview-1020790475", "body": ""}
{"comment": {"body": "Should we be using `history.replace` instead of `window.location` here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#discussion_r907803780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#pullrequestreview-1020790649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#pullrequestreview-1020832374", "body": ""}
{"comment": {"body": "I think window.location is the correct one to use.\n\nFrom the mozilla docs, history doesn't actually make a http request. More used for navigation **within** a SPA. \n\n\n\nWindow.location is used more to navigate outside of ones domain.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/5b6bc9c2-b301-4f3a-9dc6-e32af50a2f20?message=74fd3718-74c0-44e7-bb1a-a9a2c4104380).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1986#discussion_r907833477"}}
{"title": "Fix dependencies on localstack", "number": 1987, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1987", "body": "All servcies were depending on postgres to ensure localstack was up for docker compose.\nThat's not right.\nShould make it service-specific."}
{"title": "Remove unncessary check", "number": 1988, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1988", "body": "It's causing flakes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1988#pullrequestreview-1017842672", "body": ""}
{"title": "Exclude deleted teams from admin statistics", "number": 1989, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1989"}
{"title": "Add create/updateThread and create/updateMessage operations", "number": 199, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-868714195", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-868714495", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-868715598", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-868715739", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-868716435", "body": ""}
{"comment": {"body": "This is just a stub for now, other properties to be added later (like file, line, etc)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#discussion_r796296060"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-868716863", "body": ""}
{"comment": {"body": "The `SourceMarkGroup` properties that should be a part of this object can be added later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#discussion_r796296391"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-868789632", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869565098", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869565545", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869566209", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869568978", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869570813", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869572464", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869572497", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869574248", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869575698", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869576979", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869578135", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869579522", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869582886", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869590686", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869592860", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869625414", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869637869", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869638649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869666481", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869708017", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869708558", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869708874", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869709193", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869726792", "body": ""}
{"comment": {"body": "@richiebres", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#discussion_r796928999"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869728653", "body": ""}
{"comment": {"body": "This comment is meant for the `CreateThreadRequest` object", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#discussion_r796929784"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869870503", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869879627", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/199#pullrequestreview-869909407", "body": ""}
{"title": "Allow Klue in PROD", "number": 1990, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1990"}
{"title": "Add thread paticipant store", "number": 1991, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1991"}
{"title": "update", "number": 1992, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1992"}
{"title": "Log number of changed files for pull request", "number": 1993, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1993"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1993#pullrequestreview-1017929051", "body": ""}
{"title": "Update Swift GRPC", "number": 1994, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1994"}
{"title": "Missed a config change", "number": 1995, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1995"}
{"comment": {"body": "Super weird this didn't show up in the file diff. Xcode must have cached my change without writing it...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1995#issuecomment-1165164219"}}
{"title": "Define new thread participant ordering", "number": 1996, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996", "body": "Ordering is now as follows:\n1. Author\n2. Participants that have not yet posted messages, ordered by join date ascending\n3. Participants that have posted messages, ordered by message recency ascending"}
{"comment": {"body": "\u2705", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#issuecomment-1165790231"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017972082", "body": "I thought that the PR author (not the message author) was in the participant list too, but now I see that they are not. I guess we should leave these out of the picture?"}
{"comment": {"body": "@rasharab @davidkwlam is the plan that Unblocked @-mentioned and SCM @-mentioned members end up in this list? That is: they are added to `ThreadParticipantModel`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905709045"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017981135", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017985640", "body": ""}
{"comment": {"body": "Even if they do, I'm not sure it affects the ordering scheme here. We're looking for message authors to define ordering, so I think the @-mentioned participants will just enter the list of participants that haven't posted yet", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905719461"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017986484", "body": ""}
{"comment": {"body": "Yup. I was just confirming that's how we'd add them to the thread (as opposed to some other model or table).\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c0198111-43a0-4f08-ac3e-4082fe08c094?message=8952606a-d3f2-4610-ac42-d7f0539ca13f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905720027"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017987165", "body": ""}
{"comment": {"body": "These unblocked notifications are amazing btw. Instant reply\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c0198111-43a0-4f08-ac3e-4082fe08c094?message=d3acae5d-83e7-4c0b-9b8d-99e6be7c7347).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905720529"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017987655", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/8308c782-8ae7-41fe-8bcb-07e20e4fa252)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c0198111-43a0-4f08-ac3e-4082fe08c094?message=92cf69f8-f529-43fb-beb5-1c98420002fb).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905720853"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017987800", "body": ""}
{"comment": {"body": "How come I didn't get the third line Pete?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c0198111-43a0-4f08-ac3e-4082fe08c094?message=f508c80a-47f6-4100-9802-f9df7e6a90cf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905720950"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017988605", "body": ""}
{"comment": {"body": "btw you can use operator overloading here.\r\n```suggestion\r\n        val nonMessageParticipants = participantsOrderedByCreatedAtAscending - messageParticipants\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905721597"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017988717", "body": ""}
{"comment": {"body": "Interestingly, this does not render in github in safari. Summoning @jeffrey-ng  ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905721672"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017989859", "body": ""}
{"comment": {"body": "Can see in the dashboard. This is weird, not sure what happened here. That message was sent from the github extension\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/c0198111-43a0-4f08-ac3e-4082fe08c094?message=2466a9ae-8d27-49f7-a521-0f98a599fe5e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905722566"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017991063", "body": ""}
{"comment": {"body": "I actually tried this, but it complains that it should be a set. This implied that it was using `minus` under the hood, which does not guarantee order preservation", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905723394"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#pullrequestreview-1017992045", "body": ""}
{"comment": {"body": "`list.toSet()` is stable.\n\nbut nvm, this is fine.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d9cb97b3-c2db-4c10-8daa-f23bce17eb4a?message=77b37c9d-fe8c-4cc0-87ce-994ccb270798).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1996#discussion_r905724132"}}
{"title": "Accordion participants", "number": 1997, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997", "body": ""}
{"comment": {"body": "It looks like the faces with +2 fan out slightly differently than if there are less than 3. Is that intentional? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#issuecomment-1165185105"}}
{"comment": {"body": "> It looks like the faces with +2 fan out slightly differently than if there are less than 3. Is that intentional?\r\n\r\nThe underlying animation principle is the same: the first and last avatars have a destination that they animate to, and everything else follows to fill their positions. In the < 3 case, the last avatar is already in its final destination, so it's just the avatars you already see fanning out to the left. In the > 3 case, the last avatar has to move to the right to assume its final position, while the first avatar moves to the left. This creates an \"accordion\" effect, but I think this is what we wanted? \r\n\r\n@kaych do you have any thoughts?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#issuecomment-1165650752"}}
{"comment": {"body": "> > It looks like the faces with +2 fan out slightly differently than if there are less than 3. Is that intentional?\r\n> \r\n> The underlying animation principle is the same: the first and last avatars have a destination that they animate to, and everything else follows to fill their positions. In the < 3 case, the last avatar is already in its final destination, so it's just the avatars you already see fanning out to the left. In the > 3 case, the last avatar has to move to the right to assume its final position, while the first avatar moves to the left. This creates an \"accordion\" effect, but I think this is what we wanted?\r\n> \r\n> @kaych do you have any thoughts?\r\n\r\nI think it looks the same on the other clients, so let's get this in and compare and go from there", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#issuecomment-1165777826"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#pullrequestreview-1017971170", "body": ""}
{"comment": {"body": "For anyone curious, we actually have to drop in all the avatars or the animation won't work. SwiftUI has some curious bugs...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#discussion_r905708324"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#pullrequestreview-1018753304", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#pullrequestreview-1018759155", "body": ""}
{"comment": {"body": "The frame size here is different (20 instead of 22) -- I'm guessing that is deliberate?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#discussion_r906254389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#pullrequestreview-1018759224", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#pullrequestreview-1018759649", "body": ""}
{"comment": {"body": "Yes - the \"+\" indicator is slightly smaller than the avatars\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b49b79c1-6477-4e39-88c7-590fd2e3c68c?message=4cbf4ddc-cedd-4836-b30c-177921f0aabf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1997#discussion_r906254757"}}
{"title": "Show default intercom UI in dashboard", "number": 1998, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1998", "body": "There is a custom UI for this coming up, but for now we're using the default UI.\n"}
{"comment": {"body": "Some of the Navigator.scss changes will conflict with https://github.com/NextChapterSoftware/unblocked/pull/1983. Can we merge that one in first?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1998#issuecomment-1165334396"}}
{"comment": {"body": "> Some of the Navigator.scss changes will conflict with #1983. Can we merge that one in first?\r\n\r\nYep will do", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1998#issuecomment-1165767963"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1998#pullrequestreview-1017977461", "body": ""}
{"comment": {"body": "Threw this in here too -- use consistent terminology in the hub", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1998#discussion_r905713039"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1998#pullrequestreview-1018964386", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1998#pullrequestreview-1018964471", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1998#pullrequestreview-1018967251", "body": ""}
{"title": "Move to more efficient gradle caching for builds", "number": 1999, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1999", "body": "Setup-java is shit at gradle caching.\nThere's a better action that seems to handle this better."}
{"title": "new testfile", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2"}
{"title": "Mobx Implementation", "number": 20, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/20"}
{"title": "Update service tokens", "number": 200, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/200"}
{"title": "Update version", "number": 2000, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2000"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2000#pullrequestreview-1018745538", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2000#pullrequestreview-1018745794", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2000#pullrequestreview-1018746099", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2000#pullrequestreview-1018765957", "body": ""}
{"title": "Logging for denied installations", "number": 2001, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2001"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2001#pullrequestreview-1018744217", "body": ""}
{"title": "Do model creation in the store", "number": 2002, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2002", "body": "Fixes these exceptions "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2002#pullrequestreview-1018812500", "body": ""}
{"title": "Move services out", "number": 2003, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2003"}
{"title": "move dependencis around", "number": 2004, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2004"}
{"title": "Fix builds", "number": 2005, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2005"}
{"title": "Get PullRequest, comments, and files through the admin console", "number": 2006, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006", "body": "Hits the GitHub API to get the pull request, comments, and files, and spits back the body. This will help with debugging \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#pullrequestreview-1018958408", "body": ""}
{"comment": {"body": "Just do it :)\r\n\r\nit's a one-line change:\r\n```\r\nclient.get() --> client.stream()\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#discussion_r906395991"}}
{"comment": {"body": "can we nuke all the page numbers?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#discussion_r906396872"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#pullrequestreview-1018960415", "body": ""}
{"comment": {"body": "what do you mean? this is here so that we can page the files API", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#discussion_r906397405"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#pullrequestreview-1018960933", "body": ""}
{"comment": {"body": "yeah, can we stream then remove the paging?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/65dcfe76-126d-4b07-b930-9de7e3bec541?message=f17ee58b-6f82-4d5c-b10d-7b795eeeb611).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#discussion_r906397759"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#pullrequestreview-1018961430", "body": ""}
{"comment": {"body": "Unless I'm missing some complexity?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/65dcfe76-126d-4b07-b930-9de7e3bec541?message=627cff80-73b0-4f02-8b7f-d67f28ce0c2a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#discussion_r906398125"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#pullrequestreview-1018974043", "body": ""}
{"comment": {"body": "Ok, maybe try something like this, which will stop fetching pages from the client once the max limit has been met:\n\n\n\n```\nvar count = 0\nclient.stream(...)\n   .onEach { count++ }\n   .takeWhile { count < max}\n```\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/65dcfe76-126d-4b07-b930-9de7e3bec541?message=4932c22f-7afb-4c8d-8600-102a7151327c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#discussion_r906402950"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2006#pullrequestreview-1018994881", "body": ""}
{"title": "Update Intercom UI in VSCode / web extension", "number": 2007, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007", "body": "Update Intercom UI in the VSCode sidebar, the VSCode discussion view, and the web extension discussion view:\n\n\n\nSome notes:\n\nVSCode displays intercom inline, while the web extension does not -- it will launch the dashboard with intercom open\nWeb extension sidebar is coming up in a future PR"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#pullrequestreview-1019018335", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#pullrequestreview-1019020274", "body": ""}
{"comment": {"body": "@jeffrey-ng any opinion on this?  Basically if this is a sidebar, we wrap the UI in a sidebar frame (which gives us the footer).  The other alternative I guess would be to generate two webview bundles, one for the sidebar installation wizard, one for the editor installation wizard...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#discussion_r906436577"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#pullrequestreview-**********", "body": ""}
{"comment": {"body": "So this will *not* render the Intercom button, just sets up the provider *for* the intercom button?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#discussion_r907554565"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Correct, this just sets up the provider on each webview, if required.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#discussion_r907601941"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#pullrequestreview-**********", "body": ""}
{"comment": {"body": "And to be clear, this can provide Intercom to either the standard built-in intercom button, or any custom UI (like the sidebar chin) that we want to use.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#discussion_r907617408"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Think this is okay for now... We may revisit this if we decide to refactor *all* the sidebars into a single sidebar.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#discussion_r907758755"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2007#pullrequestreview-**********", "body": ""}
{"title": "first pass for creating perf tests", "number": 2008, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2008", "body": "Using k6 testing frame work to create perf tests \nAt the moment only includes Read tests (no data mutation)\nStarted with most expensive read calls to impose a load on the system \nProvides localdev, smoke, load and stress test scenarios. \nlocaldev is only used when developing tests \nsmoke performs light tests simulating a load that typical user login and some light activity would do\nload and stress modes perform heavier and more aggressive reads. They are designed to pose a significant load on API service  and pusher service \n\nI am still working on this but want to have the base templates for now. \nUpdate:\n- Added GitHub actions workflows to run smoke tests \n- For now workflows don't cause deployment failures until we are confident that tests are working"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2008#pullrequestreview-1027916642", "body": ""}
{"title": "Update dashboard color theming", "number": 2009, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2009", "body": "\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2009#pullrequestreview-1019118600", "body": "   "}
{"title": "[RFC] Rename SourceMarkGroupId to SourceMarkId", "number": 201, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/201", "body": "I remember @matthewjamesadam mentioning yesterday that the clients won't really care about SourceMarkGroups but will instead query /threads with a list of SourceMark ids (see )."}
{"comment": {"body": "I think we want both. It's actually easier to jump from SMG ID to Thread, and easier to jump from SM ID to Message.\r\n\r\nSo singular:\r\n```\r\nGET /sourceMarkGroups/:smgID/thread\r\nGET /sourceMarks/:smID/thread\r\n```\r\n\r\nAnd plural:\r\n```\r\nGET /threads?sourceMarkGroupsId=1&sourceMarkGroupsId=2...\r\nGET /threads?sourceMarkId=1&sourceMarkId=2...\r\n```\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/201#issuecomment-1027119163"}}
{"comment": {"body": "Yeah the API the clients will need likely depends on exactly how the sourcemark algorithms work -- ie, if the algorithms primarily tell the client about sourcemarks, then we'll need to query in terms of sourcemarks.  If they primarily query in terms of SMG's, we'll need to query in terms of SMGs...  I'm not sure we've figured out exactly how that will work yet....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/201#issuecomment-1027129070"}}
{"comment": {"body": "Ok let's leave this and revisit when we have a better idea of how clients will get threads", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/201#issuecomment-**********"}}
{"title": "Fix issue no teams (aka new onboarding user)", "number": 2010, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2010", "body": "Fix the situation where a new user has no teams.\nSince there are no teams, there will be no resolved repos. Therefore we can short circuit the process with an empty list.\nTested with AppleUB account which is part of no non-deleted teams\n<img width=\"1471\" alt=\"CleanShot 2022-06-24 at 15 48 42@2x\" src=\"\n\\\na31e718f-097e-44bf-aff7-be237199344d.png\">\n."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2010#pullrequestreview-**********", "body": ""}
{"title": "Update hub app Intercom UIs", "number": 2011, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2011", "body": "SwiftUI code diffs are horrible.  This looks like I rewrote each of the UIs, but what I did was:\n\nRemove the ZStack, and the HStack/Button that added the question-mark intercom button\nAdd a Button (link-styled) to the parent VStack\n\n"}
{"comment": {"body": "@pwerry I'm going to merge this today one way or another, please review!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2011#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2011#pullrequestreview-**********", "body": ""}
{"comment": {"body": "@pwerry I don't know if you had a system for how you were naming these, so feel free to suggest some other naming/meaning for this...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2011#discussion_r906508664"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2011#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is the new bit, the same in each UI", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2011#discussion_r906509247"}}
{"title": "Uninstall repos via webhook", "number": 2012, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2012", "body": "Uninstall repos via webhook\nAdd test for TeamStore.uninstallProvider\nAdd test for RepoStore uninstallRepos and rename\nRefactor GitHubInstallationMaintenanceJob"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2012#pullrequestreview-**********", "body": ""}
{"title": "Update unblocked app icon", "number": 2013, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2013"}
{"title": "Jeff/unb 345 update web extension sidebar styling", "number": 2014, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2014", "body": "Update sidebar styling..\n\n."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2014#pullrequestreview-**********", "body": ""}
{"title": "Remove team member via hook", "number": 2015, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2015"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2015#pullrequestreview-**********", "body": ""}
{"title": "Update client icons", "number": 2016, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2016", "body": "\n\n(vscode change to be reflected in a new build)\n\nSome other small copy/clean up things"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2016#pullrequestreview-1019530197", "body": ""}
{"title": "Emails for everyone", "number": 2017, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2017", "body": "Mention emails if not existing participants on message creation."}
{"title": "Add logging extensions for MDC functionality in coroutines (fuck you mu)", "number": 2018, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2018"}
{"title": "Add Ascend Org to prod", "number": 2019, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2019"}
{"title": "Add support for light theme in vscode storybook", "number": 202, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202", "body": "Leverage storybook-stylesheet-toggle addon\nAdd a light-theme.css stylesheet \nToggle between themes using the paintbrush icon in the top panel:\n1.\n\n\n2.\n\n3."}
{"comment": {"body": "Updated themes to reflect standard vscode themes:\r\n![image](https://user-images.githubusercontent.com/13431372/152036342-ba913a74-1da3-431d-ab77-334aff1683ac.png)\r\n![image](https://user-images.githubusercontent.com/13431372/152036390-7288357f-28e5-4b7d-be56-c71974e57333.png)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#issuecomment-1027201732"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#pullrequestreview-869789629", "body": ""}
{"comment": {"body": "Something worth checking: I think we can remove the posts / build-storybook-css commands entirely, since the CSS here doesn't need to be processed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#discussion_r796981686"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#pullrequestreview-869790070", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#pullrequestreview-869799682", "body": ""}
{"comment": {"body": "Yup. No need for posts anymore. I think it's been removed from web.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#discussion_r796991763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#pullrequestreview-869815266", "body": ""}
{"comment": {"body": "@kaych mentioned that we still need to copy the CSS into a place where storybook can serve it from... we don't need postcss for that though", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#discussion_r797008181"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#pullrequestreview-869831911", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/202#pullrequestreview-869946385", "body": ""}
{"title": "Add test", "number": 2020, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2020"}
{"title": "move to mdc context", "number": 2021, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2021"}
{"title": "Try renaming", "number": 2022, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2022"}
{"title": "update", "number": 2023, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2023"}
{"title": "Fix refreshing org installation", "number": 2024, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2024", "body": "During org installation step, we need to refresh teams before refreshing repos.\nThese teams are generated with an app installations, so we need to either poll or have push.\nThis used to work as we \"polled\" teams once a minute but that was unreliable."}
{"title": "Remove allowedOrgs config", "number": 2025, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2025", "body": "Motivation:\n1. PITA to maintain this thing\n2. requires a deployment\n3. requires that we know the GitHub Org ID in advance\n4. somewhat responsible for this issue: \nPurpose of the allowlist is to prevent unauthorized use of our service.\nRight now that's not really a concern given that nobody knows about\nthe product.\nFuture:\n- Simpler solution is just toggle team authorization in admin web.\n- By default all teams are not authorized, meaning that users are\n  restricted from logging into unblocked at the service level.\n- Installations are still ingested regardless."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2025#pullrequestreview-1020454157", "body": ""}
{"title": "Refresh Auth when repo 401", "number": 2026, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2026", "body": "Manually refresh auth when 401 failure due to missing team credentials.\nThis will be removed once we can poll installations API"}
{"comment": {"body": "Merging to get in for onboarding trials. This will get cleaned up once we can poll installations.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2026#issuecomment-1166944143"}}
{"title": "Rename org and add member via hook", "number": 2027, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2027"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2027#pullrequestreview-1019691538", "body": ""}
{"comment": {"body": "This is the rename org implementation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2027#discussion_r907035417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2027#pullrequestreview-1019692467", "body": ""}
{"comment": {"body": "This is the add team member implementation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2027#discussion_r907036100"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2027#pullrequestreview-1019779034", "body": ""}
{"title": "Hide deleted teams in disclosure element to declutter", "number": 2028, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2028"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2028#pullrequestreview-1019707777", "body": ""}
{"title": "Only open the hub after the installer is gone", "number": 2029, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2029", "body": "The only caveat with this approach is that it may be possible the user has multiple installers alive at once, and in that case we won't know which one belongs to us. \nThere is a possible workaround for this though (if we deem it important): we have can the installer drop its PID into user defaults. \nBehaviour after this change:\n\nWhen any installer is open, hub will not pop open. Hub will pop open when all installer processes have exited.\nManually popping open the hub will cancel this behaviour. There's an open question about whether this is desirable, or if we want to pop the hub open regardless of previous user interaction.\n\nResolves UNB-346"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2029#pullrequestreview-**********", "body": ""}
{"title": "A whole bunch of cleanup and minor fixes", "number": 203, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/203", "body": "Fixed an issue in Route table rule creation. We were getting dupe entries back when querying CDK for subnets list\nFixed ECR replication for prod and added replication target to ECR config\nRe-created prod RDS database\nSet prod RDS DB to 2 instances\nUpdated RDS ServiceAccount role in EKS \nAdded backup policy and 30 day default retention to RDS\n\nAll changes have been deployed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/203#pullrequestreview-*********", "body": ""}
{"title": "Don't know upgrade notifications until onboarding is complete", "number": 2030, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2030", "body": "Resolves UNB-347"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2030#pullrequestreview-**********", "body": ""}
{"comment": {"body": "We may still want to show the upgrade popup *after* they finish onboarding.\r\nDon't have a great suggestion of *when* we trigger the popup though.\r\n\r\nWe can't just depend on \"hasOnboarded\" as that would be true before the user finishes the tutorial.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2030#discussion_r907552355"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2030#pullrequestreview-1020430613", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2030#pullrequestreview-1020437063", "body": ""}
{"comment": {"body": "Good point - filed https://linear.app/unblocked/issue/UNB-347/dont-show-upgrade-notifications-until-customer-is-onboarded", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2030#discussion_r907557290"}}
{"title": "Increase onboarding launch timeout", "number": 2031, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2031", "body": "Resolves UNB-348"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2031#pullrequestreview-1020585872", "body": ""}
{"title": "Add return statement", "number": 2032, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2032", "body": "Was missing return statement for find repo.\nTherefore never updating the repoStore state...\nOnce this initial release is out, I'll put a lot more focus on adding tests to these stores..."}
{"comment": {"body": "We might want to take a look at whether we can simplify the structure of the auth code too -- I find it hard to follow the logic of how it works sometimes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2032#issuecomment-1167638481"}}
{"comment": {"body": "> We might want to take a look at whether we can simplify the structure of the auth code too -- I find it hard to follow the logic of how it works sometimes.\r\n\r\nAgreed. I just figured out a pretty big auth issue in our systems that's related to installations.\r\n\r\nWhenever a user is added to a new team (usually during installations but could happen any time), we actually need to trigger an auth refresh as the \"stale\" token does *not* contain the new team's claims. Without refreshing auth, we would 401 for requests against the new team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2032#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2032#pullrequestreview-**********", "body": ""}
{"title": "Show unblocked user status on TeamMembersPage", "number": 2033, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2033", "body": "\nHelps answer the following (from ):\n- Which members of the team have created an account and logged in?\n    - Which members of the team have yet to log in\n@richiebres I see on the the TeamMemberPage it says \"Has Person Account\". Can/should we rename that to \"Is Unblocked User\" or \"Has Unblocked Account\"?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2033#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2033#pullrequestreview-**********", "body": ""}
{"title": "Add awaits to try catch in createKnowledge", "number": 2034, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2034", "body": "Async operation without await in a try catch skips the catch block."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2034#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2034#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2034#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2034#pullrequestreview-**********", "body": ""}
{"title": "MinorAdminChange", "number": 2035, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2035", "body": "update\nlist mentions"}
{"title": "Add missing margin and update message editor focus state", "number": 2036, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2036"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2036#pullrequestreview-1020567649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2036#pullrequestreview-1020575287", "body": ""}
{"title": "Use kotlin preconditions", "number": 2037, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2037", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2037#pullrequestreview-1020658134", "body": ""}
{"title": "Throw error if repo is not SCM connected", "number": 2038, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2038", "body": "Really should never happen"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2038#pullrequestreview-1020686960", "body": ""}
{"title": "Should retry PR comment job when team is not connected", "number": 2039, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2039", "body": "It would be very unusual for a team to uninstall their SCM Org minutes after\nonboarding. So treating this scenario as a bug and retrying. The impact will\nbe that these messages will b e retried a number of times. Ideally, we should\nqueue affinity/isolation and short circuit the main queue to reduce the\nimpact on other teams.\nFailing to treat this scenario as an retryable event lead to a massive drop\nin PR comment ingestion, which we have to fix afterwards. See impact in the\nissue below:\n"}
{"comment": {"body": "This code path is also called after onboarding when we get a webhook for a new thread. Same logic still applies though: I don't think we'd be getting webhook events if the team has uninstalled the app.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2039#issuecomment-1167819481"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2039#pullrequestreview-1020708285", "body": ""}
{"title": "Pulls identity info from GitHub after authorization", "number": 204, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204", "body": "Summary\nThis PR implements a small GitHub REST client to perform the token exchange and fetch user info, including primary email. I've also brought in Mockito to create a few mocks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869661934", "body": ""}
{"comment": {"body": "Not particularly happy with this, but there are a ton of exceptions buried within jackson, ktor, exposed, etc that all point to a failure in the identity validation process. I'm not sure if we want to expose service errors during the auth process, or just mask all errors with Unauthorized (I picked the latter)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#discussion_r796902766"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869663443", "body": ""}
{"comment": {"body": "Factory needed for dependency injection purposes in tests. Mocking enums is a code smell", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#discussion_r796903306"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869666064", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869666679", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869704466", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869718072", "body": ""}
{"comment": {"body": "Not that it matters hugely, but I'd go with `Exception` here, since Throwable include OOM that should be bubbled up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#discussion_r796931979"}}
{"comment": {"body": "maybe this is better on the IdentityModel, since that's already a singleton?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#discussion_r796946980"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869791772", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869802713", "body": ""}
{"comment": {"body": "Trying to follow the existing `EntityClass` approach that exposed uses, where find operations are exposed via the `UUIDEntityClass` companion object", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#discussion_r796994790"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869804744", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869809032", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869810264", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869810687", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869828238", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869951365", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869992583", "body": ""}
{"title": "Revert \"SourceMark engine should handle force pushes (#1680)\"", "number": 2040, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2040", "body": "This reverts commit 01c30d6b5b14564b237ea8ac193a371ebc3da25e.\nReason for revert:\n\nTested and the result of this change is an increase in the PointNotInRepo case, which is expected.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2040#pullrequestreview-1020720679", "body": ""}
{"title": "Update email templates", "number": 2041, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2041", "body": "Fixed up some template parameters and addresses weird out of bounds issues with contents.\n"}
{"title": "[sorry richiebres :(] Add log line with repoId and pr number", "number": 2042, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2042", "body": "The context values for our log lines still don't look right: \n\nAdding this here just to confirm before digging deeper."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2042#pullrequestreview-1020753235", "body": ""}
{"title": "Style dashboard dropdown", "number": 2043, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2043", "body": "per new designs:\n\nalso:\n* Added breakpoint for dashboard margin\n* Fix line spacing of user icon element in the web extension sidebar now that the font is no longer Sofia Pro\n* Pin the header of the dashboard threads overview and style the discussion header the same"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2043#pullrequestreview-1020781126", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2043#pullrequestreview-1020815198", "body": ""}
{"title": "Fixes various missing team states", "number": 2044, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2044"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2044#pullrequestreview-1020778643", "body": ""}
{"comment": {"body": "Noticed that the client was stuck in an infinite loop on 4xx responses. Should not retry any 4xx responses.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2044#discussion_r907795431"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2044#pullrequestreview-1020779578", "body": ""}
{"comment": {"body": "Noticed that some request types weren't sending unblocked headers. This is because the generated code uses two types of \"builders\", and one of those builders wasn't setting headers", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2044#discussion_r907796077"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2044#pullrequestreview-1020782578", "body": ""}
{"comment": {"body": "This is the crux of the fix, and it does a few things that you won't see in this PR because of how this code is executed. During onboarding, we already poll the Person (me) API looking for team member status. I've added a call to teams here, which also fixes the case where authorization state changes. \r\n\r\nThe hub is still not doing anything clever on general `401` responses for non-auth APIs. This should only happen when a user is removed from a team or a team is deleted. We're currently relying on a token refresh (once every 30 seconds) to resolve this state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2044#discussion_r907798184"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2044#pullrequestreview-1020814832", "body": ""}
{"title": "Update favicon with 32x32", "number": 2045, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2045", "body": "Prev size of 1024x1024 was downsized too sharply:\n\n(before and after)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2045#pullrequestreview-1020824862", "body": "This will make the icon displayed in any other context very blocky (ie an upscaled image), won't it?  Probably not a big deal for now, but we can add different-sized PNG images to handle that...\nlink rel=\"icon\" type=\"image/png\" sizes=\"196x196\" href=\"/favicon-192.png\"\nlink rel=\"icon\" type=\"image/png\" sizes=\"160x160\" href=\"/favicon-160.png\"\netc..."}
{"title": "Admin web banner for deleted team", "number": 2046, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2046", "body": "Shows a banner on every team-related page when the team is deleted."}
{"title": "Show intercom button in web extension sidebar", "number": 2047, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047", "body": ""}
{"comment": {"body": "Updated to use white-with-alpha constants", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#issuecomment-1167994915"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#pullrequestreview-1020815173", "body": ""}
{"comment": {"body": "@jeffrey-ng any thoughts on where I should put this?  It's a custom style for buttons that is only relevant for the web extension sidebar... open to ideas.\r\n\r\nAlso, not sure if I should be factoring any of the below into variables or themes or whatnot.  Let me know what I should be doing!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#discussion_r907821470"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#pullrequestreview-**********", "body": ""}
{"comment": {"body": "If it's a one off button style that's just used for web extension & just the intercom button, I think this is fine?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#discussion_r907838960"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#pullrequestreview-**********", "body": ""}
{"comment": {"body": "In terms of theme, the sidebar is a bit unique. We don't actually take the GH theme constants into account as it's *our* world.\r\n\r\nIf we have intercom buttons/styles that need to live within the main UI or discussions popup, we may need to take the theme into account.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#discussion_r907839825"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2047#pullrequestreview-**********", "body": ""}
{"title": "Sidebar styling updates", "number": 2048, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2048", "body": "Tooltip each row to see full title (probably a better experience we can add later)\nIn an unpinned state, keep tab sidebar expands.\nAdd delay when you mouse off of the sidebar. Its quite jumpy right now.\nAdd border box for the pin icon. Also quite finicky right now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2048#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think we should wrap this in a generic `[value, setValue] = useDebouncedState(initialValue, delay)` hook.  `value` would only update at a debounced rate.\r\n\r\n(There is already a `useDebounce` hook, which AFAICT doesn't actually debounce, and is not being used, so we should get rid of that...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2048#discussion_r907857860"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2048#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2048#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I refactored this into colors.scss, shouldn\u2019t need to define this here anymore ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2048#discussion_r908003456"}}
{"title": "Update hub icon", "number": 2049, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2049"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2049#pullrequestreview-1020859678", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2049#pullrequestreview-1020859952", "body": ""}
{"comment": {"body": "Xcode is hilarious", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2049#discussion_r907852773"}}
{"title": "Basic MessageEditor theming for web and vscode", "number": 205, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205", "body": "Adds some basic theming for the MessageEditor for VSCode and web.\nSome questions:\n* Is the approach OK?  I did the simple thing and defined CSS classes that each project can provide.\n* Is this basic file structure OK?  We now have a lot of 'MessageEditor' files and classes.  Not sure if this is confusing or OK or not."}
{"comment": {"body": "Can foresee some conflicts (or at the very least duped code) with this branch and https://github.com/NextChapterSoftware/unblocked/pull/193 -- I think it would be helpful to get that one merged first", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#issuecomment-1027294351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-869738309", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-869801192", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-869804574", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-869805304", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-869834938", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-869836052", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-869837340", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-869867482", "body": ""}
{"comment": {"body": "Spacing is different for web and vscode?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#discussion_r797059971"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-869868218", "body": "LGTM.\nThe only questions I have are the client specific css that modifies padding, max widths etc... Those don't seem to be theme related?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-871002053", "body": ""}
{"comment": {"body": "@jeffrey-ng I forget if there are perf issues with calling this mixin multiple times in a single file? \r\n\r\nThe alternative would be to call it once at a top level but it would probably require leveraging the universal selector or a top level element (body?) i.e.\r\n```\r\n* {\r\n    @include theme-colors {\r\n        .message-editor {\r\n            border-color: themed($message-editor-border);\r\n        }\r\n\r\n        .message_editor__toolbar {\r\n            background: themed($message-editor-toolbar-background);\r\n        }\r\n    }\r\n}", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#discussion_r797939632"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-871005081", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-871006728", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-871018823", "body": ""}
{"comment": {"body": "I'll double-check this, but they are styled different...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#discussion_r797950244"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-871019437", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-871161190", "body": ""}
{"comment": {"body": "@jeffrey-ng any feedback on this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#discussion_r798052153"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-871166713", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-871166853", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#pullrequestreview-871168410", "body": ""}
{"comment": {"body": "Both the web and vscode styles are using $spacer-8 AFAICT...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/205#discussion_r798057416"}}
{"title": "Remove unused reply/pin action buttons", "number": 2050, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2050", "body": "Fixes UNB-351\n@kaych LMK if this is a problem or not, looks like they weren't really hooked up to anything?"}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2050#issuecomment-1167994886"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2050#pullrequestreview-1020861461", "body": ""}
{"title": "Adds disabled test for quick token generation", "number": 2051, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2051", "body": "Should the occasion arise..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2051#pullrequestreview-1020863174", "body": ""}
{"title": "Fix email parsing of html", "number": 2052, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2052", "body": ""}
{"title": "Poll refresh token during onboarding", "number": 2053, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2053", "body": "Token must be refreshed during onboarding or the team claims won't update and subsequent team-based requests will fail"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2053#pullrequestreview-1020878411", "body": ""}
{"title": "Send view thread metrics when thread is opened from hub", "number": 2054, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2054"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2054#pullrequestreview-1020875579", "body": ""}
{"title": "Send thread event on every request", "number": 2055, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2055", "body": "We want to send thread events on every open event.\nRemoved guard but keeping metricsCache code to allow rate limiting."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2055#pullrequestreview-1020881521", "body": ""}
{"title": "Add team member info for @mention in web", "number": 2056, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2056", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2056#pullrequestreview-1020896876", "body": ""}
{"comment": {"body": "We can do this OR we can use streamState (SingleThreadStream) which also retrieves teamMembers and add an extra field to ThreadInfoAggregate for all teamMembers.\r\n\r\nI chose to be explicit as I didn't want to pollute ThreadInfoAggregate.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2056#discussion_r907880704"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2056#pullrequestreview-1020901054", "body": ""}
{"comment": {"body": "Yeah I think this is the right approach.  It doesn't cost anything extra, really, because the same team member stream is used under the hood (ie, they will all source from the same API calls).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2056#discussion_r907883869"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2056#pullrequestreview-1020901318", "body": ""}
{"comment": {"body": "Did not know that! Thanks Matt.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2056#discussion_r907884059"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2056#pullrequestreview-1020901519", "body": ""}
{"title": "Add repo via hook", "number": 2057, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057", "body": "richie/unb-300-handle-installation_repositories-event"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#pullrequestreview-1020914384", "body": ""}
{"comment": {"body": "So this logic is intentionally different from `GitHubModelTransformers.createRepositories`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#discussion_r907893542"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#pullrequestreview-1020919995", "body": ""}
{"comment": {"body": "Sorry, wrong place for this thread.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#discussion_r907898165"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#pullrequestreview-1020924647", "body": ""}
{"comment": {"body": "Should this logic be the same as `GitHubModelTransformers.createRepositories`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#discussion_r907901574"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#pullrequestreview-1020925159", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/2057/files#r907901574", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#discussion_r907901918"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#pullrequestreview-1020941354", "body": ""}
{"comment": {"body": "yeah, going to consolidate in the next change:\r\nhttps://linear.app/unblocked/issue/UNB-299/handle-installation-event\r\n\r\nhowever, I did miss the `PullRequestIngestion` model creation \ud83e\udd26\u200d\u2642\ufe0f . Will fix in this PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#discussion_r907914064"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#pullrequestreview-1020995142", "body": ""}
{"comment": {"body": "Added PullRequestIngestion model in https://github.com/NextChapterSoftware/unblocked/pull/2057/commits/b381aab29f63179989c827b43d615e0199c726c9.\r\n\r\nNot sure if this fully addresses your feedback @davidkwlam ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#discussion_r907953857"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#pullrequestreview-1022031530", "body": ""}
{"comment": {"body": "Yup!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#discussion_r908670939"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2057#pullrequestreview-1022031748", "body": ""}
{"title": "Log running processes on startup", "number": 2058, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2058"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2058#pullrequestreview-1020907483", "body": ""}
{"comment": {"body": "This goes to our service?  Seems a bit sketchy to send and store third-party application information in our service?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2058#discussion_r907888669"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2058#pullrequestreview-1021039504", "body": ""}
{"comment": {"body": "Does not go to our service, this is local logging to help debug an issue with app launch. I intend to remove this before launch\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/de24a003-475d-4d22-9932-18b342d9d5d3?message=f8e60f18-fd0c-437f-835f-c6e0565c6549).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2058#discussion_r907987959"}}
{"title": "Admin order teams", "number": 2059, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2059", "body": "Consistently order teams. Newer teams shown first."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2059#pullrequestreview-1020912996", "body": ""}
{"title": "Add rds information and update configuration", "number": 206, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/206"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/206#pullrequestreview-869811977", "body": "Looks good. Thanks for documenting it."}
{"title": "Calculate DAT, WAT, and MAT", "number": 2060, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2060"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2060#pullrequestreview-**********", "body": ""}
{"comment": {"body": "`teamStore.countTeams` only considers teams where `TeamModel.deletedProviderExternalId eq null` (see `TeamStore` changes below) so that we're not counting deleted teams", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2060#discussion_r908841045"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2060#pullrequestreview-**********", "body": "This is fine for now, but suggest that we create two separate graphs: one for teams and one for users. Two reasons:\n1. the Y-axis will be different\n2. the graph will be to busy"}
{"comment": {"body": "oh, cool. didn't know you could do that!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2060#discussion_r909129439"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2060#pullrequestreview-**********", "body": ""}
{"title": "Add @mention for web extension", "number": 2061, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2061", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2061#pullrequestreview-1020998927", "body": ""}
{"title": "Add installer detection logging during onboarding", "number": 2062, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2062"}
{"title": "Allow logging into dev from localhost", "number": 2063, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063", "body": "This is part 1 of allowing us to do dashboard development against dev.  Having to run the dashboard against a local stack has been a significant time-waster, generally it'd be easier to have to keep fewer parts of the system running at a time.\nThis part changes the API service in such a way that (I hope) login is allowed from a localhost dashboard, in the dev environment:\n\nUpdate CORS for dev, to allow access from localhost development ports\nUpdate /login call, adding an optional redirectUrl query parameter.  The idea here is that clients could provide the ultimate exchange URL to end up at.  If nothing is provided, the default redirect URL for that environment is used.\nUpdate the Auth config to specify which \"alternative\" redirect URLs are allowed for each environment.  Basically, dev allows redirecting back to localhost, and all other envs do not allow any other alternatives.  We don't want people jumping to random URLs in prod, so I locked this down.\n\nThe next PR would add the client bits to run against dev from a local dashboard environment.  I have to deploy this first to see if it works though.\nThe only concern I have here is that the config files are not typed at all -- if I mess things up, and (say) fail to pass in a required value for the prod config, or provide something of the wrong type, I assume that will cause some problems?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1021090785", "body": ""}
{"comment": {"body": "I'm assuming if I pass this in here, and do *not* provide this in every other configuration, that this value is inherited?  But maybe I'm wrong and I should be adding this everywhere?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#discussion_r908025445"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1021104837", "body": ""}
{"comment": {"body": "It's inherited", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#discussion_r908035976"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1021113668", "body": ""}
{"comment": {"body": "I wasn't sure if this was the right thing to do or not:\r\n1) Does throwing this automatically result in the appropriate error code being returned at the API layer?\r\n2) Is throwing this (HTTP-centric) exception here, outside of the API layer, bad form?  Should I instead throw a custom Exception here, catch it in the AuthApiDelegate and explicitly respond with the code I want?\r\n\r\nI saw other services doing things like this so I assumed it was probably OK, but I don't know for sure.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#discussion_r908042212"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1022017779", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1022188108", "body": ""}
{"comment": {"body": "> Does throwing this automatically result in the appropriate error code being returned at the API layer?\r\n\r\nYes.\r\n\r\n> Is throwing this (HTTP-centric) exception here, outside of the API layer, bad form? Should I instead throw a custom Exception here, catch it in the AuthApiDelegate and explicitly respond with the code I want?\r\n\r\nThe platform is designed so that we can throw exceptions like this at the service layer, so what you're doing is ok", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#discussion_r908793217"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1022216268", "body": ""}
{"comment": {"body": "In idiomatic Kotlin:\r\n```kotlin\r\nredirectUrl?.let {\r\n    if (!authenticationConfig.oauthRedirectAltUrls.contains(it)) {\r\n        throw BadRequestException(\"Redirect URL not allowed\")\r\n    }\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#discussion_r908812922"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1022216574", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1022217345", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1022220554", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1022236872", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2063#pullrequestreview-1022248939", "body": ""}
{"title": "Upgrade rds instance size prod", "number": 2064, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2064", "body": "Currently we are CPU bound in load tests. Other instance types turned out to be very expensive. For the preview release we will still rely on burstable instances until we can make a good case for higher end instance types. \nResized prod to use t4g.large instance size (largest burstable type available). We might want to invest in multi-writer cluster before resizing instances again."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2064#pullrequestreview-**********", "body": ""}
{"title": "Renaming organization must rename repo owner part", "number": 2065, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2065", "body": "Renaming the repo also needs to be more lenient to account for a repo rename\nracing against the org rename and winning."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2065#pullrequestreview-**********", "body": ""}
{"title": "Get timestamp of last thread view", "number": 2066, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2066"}
{"comment": {"body": "@richiebres I was hoping avoid having to query this table since it might get complicated (`get latest createAt for these team member IDs`). Postgres has some windowing that might let us do that, but I'm pretty sure I'll have to write some custom functions or raw SQL.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2066#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2066#pullrequestreview-**********", "body": "We already have this persisted in the UserEngagementModel.\nIt has everything we need right?\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2066#pullrequestreview-1024234045", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2066#pullrequestreview-1024401657", "body": ""}
{"comment": {"body": "```suggestion\r\n                store.getLatestThreadViewed(trx = this, teamMemberId = member)?.id,\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2066#discussion_r910699515"}}
{"title": "Teams with no threads shouldn't crash the hub", "number": 2067, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2067"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2067#pullrequestreview-1022136892", "body": ""}
{"title": "Handle installation hook", "number": 2068, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2068", "body": "This duplicates functionality in the GitHubInstallationMaintenanceJob, but the use cases are different:\n- webhook install will only ever create teams, members, repos\n- the maintenance job creates and destroys teams, members, repos\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2068#pullrequestreview-1022561440", "body": ""}
{"title": "Update assets error images", "number": 2069, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2069", "body": "Updated existing asset error images with the new ones for 404 and 401 codes\nAdded a new image to be used for deleted assets once we implement the functionality"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2069#pullrequestreview-1022139697", "body": ""}
{"title": "Remove unused delegate class", "number": 207, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/207"}
{"title": "Fix logging part 2", "number": 2070, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2070", "body": "The \"withLoggingContext\" that we were using from mu library will not work as it makes assumptions that we're not. using coroutines.\nJust doing simple copy and write logic as what it was doing was wrong.\nAdded stress test that reproed the problem."}
{"title": "Update styling for fallback Dialog", "number": 2071, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2071", "body": "Add support for ignoring fallback dialogs.\nAPI for passing one shot messages and receiving a response isn't great for multi browser support..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2071#pullrequestreview-1023968266", "body": ""}
{"comment": {"body": "Is there no way to type this message so we don't need to do all this checking?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2071#discussion_r910382115"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2071#pullrequestreview-1023976568", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2071#pullrequestreview-1023981587", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2071#pullrequestreview-1023982648", "body": ""}
{"comment": {"body": "No way using the browser APIs.\r\n\r\nWe can add wrappers to this in the long term similar to how the port stuff was done.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2071#discussion_r910392414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2071#pullrequestreview-1023985501", "body": ""}
{"title": "Center spinner in VSCode", "number": 2072, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2072"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2072#pullrequestreview-1022199007", "body": "It doesn't feel great to be mixing the"}
{"title": "update", "number": 2073, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2073"}
{"title": "Update email assets", "number": 2074, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2074"}
{"title": "enable performance insights", "number": 2075, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2075", "body": "I have already enabled this on one prod instance to see query times. The default retention of 7 days is free so we should have them enabled in Dev and prod. \nIt's kinda like the Mongo dashboard and shows all the slow queries, long waits and locks etc."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2075#pullrequestreview-1022205629", "body": ""}
{"title": "Sidebar breakpoints", "number": 2076, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076", "body": "Simple Breakpoints for VSCode sidebar.\n\n"}
{"comment": {"body": "I'll hop on and make a few tweaks.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076#issuecomment-1169103645"}}
{"comment": {"body": "My changes are in. I'm certain there can be more code sharing but I'm a little hesitant to make those at this point in time. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076#issuecomment-1171634331"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076#pullrequestreview-1023729528", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076#pullrequestreview-1024140607", "body": ""}
{"comment": {"body": "I worked with Kay to finesse the H1 and P styles in the tutorial wizard. The installation flow should be using the same styles, so I factored out those styles and added them here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076#discussion_r910511892"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076#pullrequestreview-1024140990", "body": ""}
{"comment": {"body": "These are the responsive styles for the installation and tutorial pages.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076#discussion_r910512141"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076#pullrequestreview-1283556377", "body": ""}
{"comment": {"body": "@benedict-jw Sounds good. I notice there are similar styles referenced by other views, should we address and refactor those?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2076#discussion_r1096164146"}}
{"title": "More favicon nits", "number": 2077, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2077"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2077#pullrequestreview-1022243749", "body": ""}
{"title": "Use IconForThread to generate source mark icons", "number": 2078, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2078", "body": "\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2078#pullrequestreview-1022246625", "body": ""}
{"title": "Add lastModified header to getTeamMembers response", "number": 2079, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2079", "body": "Needed for push\nResolves UNB-274"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2079#pullrequestreview-1022599634", "body": ""}
{"title": "VSCode Auth", "number": 208, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208", "body": "Basic VSCode auth with message UI.\nNo webview in place."}
{"comment": {"body": "Cleaned up and removed webview aspects.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#issuecomment-1027628414"}}
{"comment": {"body": "I'll need to make some changes to handle https://github.com/NextChapterSoftware/unblocked/pull/218\r\n\r\nWould like to do this in a separate PR as it affects both web and vscode.\r\n\r\n@matthewjamesadam @pwerry ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#issuecomment-1028219349"}}
{"comment": {"body": "> Looks good \ud83c\udf89 maybe we should start thinking about how to test this?\r\n\r\nYeah. I have a rough understanding of how we can test UI components but not sure about these more complicated flows that actually interact with VScode...\r\n\r\nWe'll need to also do a spike on mocking out API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#issuecomment-1028268960"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-869856386", "body": ""}
{"comment": {"body": "Not really a part of this PR, but we should rename these to `unblocked-vscode.*` ...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#discussion_r797048450"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-869857755", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-869870601", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-869949047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-869949427", "body": ""}
{"comment": {"body": "Shouldn't we be reusing the /shared/stores version of this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#discussion_r797154067"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-869950983", "body": ""}
{"comment": {"body": "This was moved to shared in this PR.\r\nGithub doesn't make that super obvious.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#discussion_r797156010"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-870785731", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-870798595", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-870872908", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-870956700", "body": ""}
{"comment": {"body": "might want to run `showLoading()` right at the very beginning so we see that while we're making the other API calls?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#discussion_r797904368"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-870961638", "body": ""}
{"comment": {"body": "Moved earlier in process.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#discussion_r797907829"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/208#pullrequestreview-870967321", "body": "Looks good   maybe we should start thinking about how to test this?"}
{"title": "Fix 'Contact Support' sidebar border in web extension", "number": 2080, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2080", "body": "In GH light mode, the border was very heavy.  This now matches how the sidebar search input border looks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2080#pullrequestreview-1022260727", "body": ""}
{"title": "Fix issue with sidebar project selector", "number": 2081, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2081", "body": "Opening a project from sidebar project selector did not re-open the sidebar to correct state.\nUpdating flow to keep plugin sidebar open on workspace open.\n"}
{"comment": {"body": "<img width=\"215\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/176269557-5fe218aa-4337-45b7-9972-2b6d0001f12c.png\">\r\nare we supposed to cap this list at a certain threshold? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2081#issuecomment-1169147985"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2081#pullrequestreview-1023730780", "body": ""}
{"title": "Revert \"Revert \"SourceMark engine should handle force pushes (#1680)\" (#2040)\"", "number": 2082, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2082", "body": "This reverts commit 8e6d8dd291da018b4f64dd7916c1280ee4037788.\nReasons for rolling back the revert is because without this bug,\nsourcemark doon't show properly when there are merge commits.\nFucked up. Both will be fixed once we handle merge commit properly."}
{"title": "update", "number": 2083, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2083"}
{"title": "Install Redirect to Download", "number": 2084, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2084", "body": "When one installs the GH App without coming from VSCode, we will now redirect user to Landing/download instead of to VSCode.\nAka instead of seeing this:\n\nThey will be redirected immediately to \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2084#pullrequestreview-1022289509", "body": ""}
{"title": "Defer asking for notification permissions until first unread", "number": 2085, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2085", "body": "Resolves UNB-160"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2085#pullrequestreview-1022567875", "body": ""}
{"title": "add configuration set name", "number": 2086, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2086"}
{"title": "Use manifest plugin for favicons", "number": 2087, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2087", "body": "Directly importing the assets in the html doesn't work in the bundled build (needs relative path), use plugin to generate a manifest to create the icons instead \nThis does generate a manifest json but I think its contents are relatively safe? \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2087#pullrequestreview-1022425630", "body": ""}
{"title": "Web login page", "number": 2088, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2088", "body": "\nMoved some styles to mixins.\nMoved some constants to shared constants.\nOverall I think we need to refactor out the landing page css into a more generic format post release."}
{"comment": {"body": "No changes to download.\r\n\r\nFont size in login & download are different.\r\n\r\n<img width=\"755\" alt=\"CleanShot 2022-06-28 at 14 30 06@2x\" src=\"https://user-images.githubusercontent.com/1553313/176301727-abadec73-0fe7-4313-93fc-9304b3ad69d9.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2088#issuecomment-1169264139"}}
{"comment": {"body": "<img width=\"2604\" alt=\"CleanShot 2022-06-28 at 16 44 32@2x\" src=\"https://user-images.githubusercontent.com/1553313/176322376-8bd07f3d-ccab-4ab0-8642-39beeb367e30.png\">\r\n\r\nLeft live. Right local.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2088#issuecomment-1169391903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2088#pullrequestreview-1022577196", "body": "I'm assuming the /extensions page also looks the same?"}
{"title": "Change \"```suggestion\" thread titles to \"Suggestion: ...\"", "number": 2089, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089", "body": "Fixes UNB-266"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1022457868", "body": ""}
{"comment": {"body": "For migrating existing threads. Will remove once its ran on all teams", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#discussion_r908983070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1022532951", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1022708016", "body": ""}
{"comment": {"body": "```\n    suspend fun setUp() {\n        team = makeTeam()\n    }\n\n```\n\nignore me \u2014 testing!\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/9caf1523-2574-4e1c-9771-b8b9d74f90d5?message=0317ea70-da9c-4695-9fa3-edebd8342643).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#discussion_r909177750"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1022712164", "body": ""}
{"comment": {"body": "Not related to this PR, but filed an issue to track a suggestion editing bug that I noticed:\r\nhttps://linear.app/unblocked/issue/UNB-372/editing-messages-with-suggestions-removes-the-suggestion", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#discussion_r909180931"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1022788688", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1022792050", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1024091355", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1024091568", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1024231382", "body": "nice"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1027829513", "body": ""}
{"comment": {"body": "Creating a thread to test @mentions...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#discussion_r913183684"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1027830825", "body": ""}
{"comment": {"body": "@richiebres can you reply to this message in unblocked if this @mention works?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#discussion_r913186531"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#pullrequestreview-1027832394", "body": ""}
{"comment": {"body": "How about now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2089#discussion_r913189919"}}
{"title": "GitHub graphql generator", "number": 209, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/209", "body": "Generates type-safe Kotlin code from GraphQL files:\nshell\n$ tree apiservice/build/generated/src/main/kotlin/\napiservice/build/generated/src/main/kotlin/\n com\n     nextchaptersoftware\n         clients\n             githubGraphql\n                 GraphQLTypeAliases.kt\n                 UserQuery.kt\n                 userquery\n                     User.kt\nThe submodule is added to get the published GitHub V4 GraphQl schema. The intention is that we uplevel this submodule occasionally as GitHub release new versions."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/209#pullrequestreview-869847758", "body": ""}
{"comment": {"body": "just a simple sample file to prove the pattern. will likely remove this in future.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/209#discussion_r797039803"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/209#pullrequestreview-869859573", "body": ""}
{"comment": {"body": "Where you thinking?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/209#discussion_r797051694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/209#pullrequestreview-869860433", "body": ""}
{"title": "Fix synchronous logging apis (mu sucks)", "number": 2090, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2090"}
{"title": "Images need to be 32x32 for safari to recognize", "number": 2091, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2091", "body": "I thought I tested this on Safari but it was probably showing a cached icon. Needed to test on a private browser.\nbefore:\n\nafter:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2091#pullrequestreview-1022563973", "body": ""}
{"title": "Add extension download links to hub", "number": 2092, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2092"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2092#pullrequestreview-1022566180", "body": ""}
{"title": "Update extension manifest icons", "number": 2093, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2093", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2093#pullrequestreview-1022576196", "body": ""}
{"title": "Disable preauth secret cookie in dev and local", "number": 2094, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2094"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2094#pullrequestreview-1022582947", "body": ""}
{"title": "Optimize getInstallations API call to facilitate polling during onboarding flow", "number": 2095, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2095", "body": "Memoize GitHub API responses in memory\nIntroduce general cached pattern.\nCan extend in future for LRU, or expiry, or distributed store backed (PG or Redis).\n\n"}
{"comment": {"body": "Question?\r\nWhy are we creating our own cache library?\r\n\r\nWe're already making use of cache4k (and have it as a dependency)\r\n\r\nhttps://github.com/ReactiveCircus/cache4k", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2095#issuecomment-1169452326"}}
{"comment": {"body": "> Question? Why are we creating our own cache library?\r\n> \r\n> We're already making use of cache4k (and have it as a dependency)\r\n> \r\n> https://github.com/ReactiveCircus/cache4k\r\n\r\nSorry, just noticed this now. Looked for previous implementations in our code before I wrote this, but for some reason couldn't find this one. And it looks like it's unused now btw. I'll take a look at cache4k - not familiar.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2095#issuecomment-1169601127"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2095#pullrequestreview-1022648765", "body": ""}
{"title": "Fix for failure to add owners to organizations", "number": 2096, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2096", "body": "Ben just hit this bug."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2096#pullrequestreview-1022621753", "body": ""}
{"title": "Too much padding after removing extensions menu items", "number": 2097, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2097"}
{"title": "Add thread icon to extension dialog", "number": 2098, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2098", "body": "per Ben's request:\n\n\nalso upped the z-index of the modal dialog to layer over the GH pinned header (they must've upped the z-index recently?)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2098#pullrequestreview-1023675481", "body": ""}
{"comment": {"body": "Is this change intended? part of web, not extension.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2098#discussion_r910177499"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2098#pullrequestreview-1023676108", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2098#pullrequestreview-1023677823", "body": ""}
{"comment": {"body": "Yeah. Having issues with the favicon in Safari on prod -- seeing if this will fix. The docs for the manifest plugin also recommends putting the HtmlWebpackPlugin before the manifest plugin so this addresses that \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a8427200-9b3c-4b5d-ba25-1126b978aa72?message=3fe122a4-732f-44cb-aeb6-fa9dd4d645a0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2098#discussion_r910179095"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2098#pullrequestreview-1023739940", "body": ""}
{"comment": {"body": "For future reference there's also a `ThreadIcon` component that basically does this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2098#discussion_r910223277"}}
{"title": "Revert \"Update email assets (#2074)\"", "number": 2099, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2099", "body": "This reverts commit 40644665a44fbe7f079b19932c0d23f1fdc9ce3b."}
{"title": "Fix TypeKit", "number": 210, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/210", "body": "Use the new TypeKit project reference."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/210#pullrequestreview-869880777", "body": ""}
{"title": "NewAssets", "number": 2100, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2100", "body": "Revert \"Revert \"Update email assets (#2074)\" (#2099)\"\nupdate"}
{"title": "Recommendation demo hack fixes", "number": 2101, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2101", "body": "Add Ben to list of demo users\nIgnore duplicate errors, which occurs only for demo users when the \"fake\"\n  network relationship failed to overwrite the \"real\" network relationship.\nAlso flip the order to that fake relationships take precedence."}
{"title": "Rename GitHub App in DEV", "number": 2102, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2102", "body": "Rename our GitHub Apps to drop the trailing \"App\" part of the name. Testing in DEV first.\nContext\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2102#pullrequestreview-1023756642", "body": ""}
{"title": "Always return empty for deprecated /teamMembers channel", "number": 2103, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2103"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2103#pullrequestreview-1023709222", "body": ""}
{"title": "Allow running local dashboard against dev", "number": 2104, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2104", "body": "Client changes that allow running local dashboard against dev.\nnpm run start:dev runs the dashboard against dev\nnpm run start:local runs the dashboard against local stack"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2104#pullrequestreview-1023710438", "body": ""}
{"comment": {"body": "This allows overriding any environment values from the webpack build configuration.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2104#discussion_r910202516"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2104#pullrequestreview-1023737066", "body": ""}
{"title": "Rename GitHub App in PROD", "number": 2105, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2105", "body": "Rename our GitHub Apps to drop the trailing \"App\" part of the name.\nContext\n\n\nNote that renaming the PROD app from \"Unblocked App\" to \"Unblocked\"\n  would change the app to .\nThe problem is that the unblocked token is taken.\nHere's the hack: we introduce a zero width character in the display\n  name, so that the display name \"Unblocked\" appears as \"Unblocked\",\n  which forces the token to be un-blocked which is available."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2105#pullrequestreview-1023757351", "body": ""}
{"title": "Use Permalink as it contains commit hash instead of branch name", "number": 2106, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2106", "body": "Thread creation is web extension was failing depending on the current url.\nCurrentURL: \nhref=\"/NextChapterSoftware/unblocked/raw/main/vscode/webpack.dev.js\"\nCurrent URL: \nhref=\"/NextChapterSoftware/unblocked/raw/9bcca7731aa854fc5bc6c5adc9897078e5cbe02e/vscode/webpack.dev.js\"\nTherefore we prefer fetching the permalink when it exists.\nWe cannot only depend on the permalink though since it doesn't exist when the Curren URL references a commit...\ndata-permalink-href=\"/NextChapterSoftware/unblocked/raw/b728bd56d37450c6cc6c680f7780107cd76f7dbe/vscode/webpack.dev.js\""}
{"comment": {"body": "This is a huge pain...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2106#issuecomment-1170306186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2106#pullrequestreview-1023798257", "body": "Thanks Jeff!\n(we need tests for this stuff though, even if it's just static fixtures for now)"}
{"title": "Store needs to be localized to editor", "number": 2107, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2107"}
{"title": "Fix issue with subsequent navigation", "number": 2108, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2108", "body": "Port creation did not work as I had expected.\nPort.sender.url is the URL of the page when the port was created, not when the newest/current message is sent.\nThis caused issues when navigating between repositories.\nAdded some cleanup to reduce churn."}
{"comment": {"body": "Potentially also fixes \"missing port sender\" sidebar bug.\r\nThere were chances that the port was created on a non-repo URL which forced the port to send an invalid URL when loading the thread sidebar.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2108#issuecomment-1170339620"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2108#pullrequestreview-1023842216", "body": ""}
{"title": "update email templtes", "number": 2109, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2109"}
{"title": "Update chromatic token", "number": 211, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/211", "body": "Update chromatic token for shared library"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/211#pullrequestreview-869881857", "body": ""}
{"title": "Fix navigation/search operations that left extra elements in the history", "number": 2110, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2110", "body": "Fixes UNB-364\nFixes two issues:\n* If you go to the dashboard root, we left an extra history entry when redirecting to /mine\n* Whenever you viewed a thread, we would add extra entries in the history as we modified the search parameters"}
{"comment": {"body": "> Is the fallback for when the hub cant open a thread in a different client and sends it to the web instead?\r\n> \r\n> I feel like it would be helpful to add comments to the file for context (general comment, not related to this PR).\r\n\r\nYep that's the case here.  I agree that some commenting would help...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2110#issuecomment-1170561298"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2110#pullrequestreview-1024008836", "body": "Is the fallback for when the hub cant open a thread in a different client and sends it to the web instead? \nI feel like it would be helpful to add comments to the file for context (general comment, not related to this PR)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2110#pullrequestreview-1024010137", "body": ""}
{"comment": {"body": "so this looks like we could probably do a catch in the beginning to avoid repetition/be more clear\r\n\r\nie\r\n\r\n```\r\nif (!isFallback) {\r\n    return;\r\n}\r\n\r\nif (!ignoreFallback) {\r\n...\r\n}\r\n\r\nsearchParams.delete( ... // etc", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2110#discussion_r910412178"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2110#pullrequestreview-1024011535", "body": ""}
{"comment": {"body": "\ud83d\udc4d\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/3c585214-3acc-4769-b14b-0d8984fd70ce?message=d8160f9a-8046-4b31-bb75-fa9e83786181).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2110#discussion_r910413104"}}
{"title": "Add Popover component", "number": 2111, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2111", "body": "Separates out the logic in the Dropdown component so that the Popover is a separate component re: UB discussion (changes to the Dropdown component in terms of UI output should be a no-op)\nRefactored some of the styling so that they are shared mixins between the Dropdown and Popover components\nFix some layout bugs"}
{"comment": {"body": "@jeffrey-ng the popover and the dropdown are styled the same ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2111#issuecomment-1174019576"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2111#pullrequestreview-1027770211", "body": "So the dropdown didn't change styles. Does the popover?"}
{"title": "Suppress notifications when marked unread", "number": 2112, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2112", "body": "The current logic error always shows a notification if a message wasn't previous marked as unread. This is not really what we want. \nThis PR exploits a particular behaviour of Set.insert(), which is that oldMember will actually be the newMember if it was able to insert the item. If the same item already exists, insert() will return the previous item. The lastMessageCreatedAt field is not counted towards item uniqueness.\nThis means that we only need to check that newItem.lastMessageCreatedAt  oldItem.lastMessageCreatedAt because if it is successfully inserted, then these values will be the same (this is the marked unread case)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2112#pullrequestreview-1024005959", "body": ""}
{"title": "Enable launch testing", "number": 2113, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2113", "body": "There's a launch test now enabled in CI. This PR comments out the UI tests because they're doing nothing and will waste build minutes"}
{"title": "New Selector", "number": 2114, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2114", "body": "Github changed the selector ID...\nThis is used to get the latest commit hash for source mark rendering & discussion creation."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2114#pullrequestreview-1024030421", "body": ""}
{"title": "AddEmailConfigurationSets", "number": 2115, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2115"}
{"title": "Revert \"Suppress notifications when marked unread\"", "number": 2116, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2116", "body": "Reverts NextChapterSoftware/unblocked#2112"}
{"title": "Try tagging more details", "number": 2117, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2117"}
{"title": "EncodeUriComponent instead of encodeUri", "number": 2118, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2118", "body": "encodeURI !== encodeURIComponent.\nWas causing some issues with installation url.\nAlso removed unnecessary encoding/decoding in web."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2118#pullrequestreview-1024107451", "body": ""}
{"title": "Don't render editor placeholder when we have an empty list", "number": 2119, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2119", "body": "Slate's placeholder logic is pretty simple, it renders a placeholder if the document does not have any leaf nodes with text in them.  This doesn't work for our document, because we can have lists that do not have text, but count as content.  This leads to goofy things like this:\n\nThis adds custom placeholder creation and rendering logic, stolen from the Slate source code."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2119#pullrequestreview-1024109668", "body": "Thank you!!"}
{"title": "Update dependencies", "number": 212, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/212"}
{"title": "update", "number": 2120, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2120"}
{"title": "update", "number": 2121, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2121"}
{"title": "Update adding email flow per designs", "number": 2122, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2122", "body": "\n\n"}
{"comment": {"body": "Thank you lord!!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2122#issuecomment-1170614012"}}
{"comment": {"body": "related but not the entire work item: https://linear.app/unblocked/issue/UNB-331/invite-specific-team-members-functionality", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2122#issuecomment-1170617724"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2122#pullrequestreview-1024132337", "body": ""}
{"title": "Fix checkbox state", "number": 2123, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2123", "body": "Disable the checkbox if the input is invalid, per Ben's request"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2123#pullrequestreview-1024137262", "body": ""}
{"title": "SourceMark file hash wrong after pairwise compare of whitespace only changes", "number": 2124, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2124", "body": ""}
{"title": "Remove no longer needed code", "number": 2125, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2125"}
{"title": "SourceMark file rename handling works when there is no file content change", "number": 2126, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2126", "body": "Addresses bugs where the engine failed to recognize a file rename\nor file move unless the content of the file had also been modified."}
{"title": "Show alert when team is not SCM connected", "number": 2127, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2127"}
{"title": "Validate file content sha of original ingested source points", "number": 2128, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2128", "body": "This is the sole cause of all these exceptions:\nDiff content is not based off the old point\n"}
{"comment": {"body": "If you have some examples of threads where this error is being thrown, I can have a closer look and see if/how ingestion is failing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2128#issuecomment-1173320752"}}
{"comment": {"body": "> If you have some examples of threads where this error is being thrown, I can have a closer look and see if/how ingestion is failing\r\n\r\nThanks @davidkwlam, was going to talk to you tomorrow about it. Took me a while to get my head around it so wrote an issue up. It has examples from unblocked in DEV:\r\nhttps://linear.app/unblocked/issue/UNB-377/pr-ingested-source-point-file-hashes-are-inconsistent", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2128#issuecomment-1173328575"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2128#pullrequestreview-1026992124", "body": ""}
{"title": "Ensure that email templates cleanup is in-band with redis lock", "number": 2129, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129", "body": "\nCreated a parallel test that validated there was a bug that needed to be fixed.\nThe cleanup should be within the redis lock."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1027831543", "body": ""}
{"comment": {"body": "Is it worth adding template names to these error messages ? Takes the guess work out of a figuring out which template is causing issues. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r913188145"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1027831858", "body": ""}
{"comment": {"body": "Should we add the template name to this log statement ? It would be useful to know which template is causing failures in case we had a faulty template that was causing failures. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r913188843"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1027831978", "body": "Approved with a small suggestion"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1027832479", "body": ""}
{"comment": {"body": "The logging context is included as part of the messaging. (look at withLoggingContext)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r913190076"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1027832844", "body": ""}
{"comment": {"body": "Great question. The logging context that I added earlier is added to all log messages within the logging context block (withLoggingContext)\r\n\r\ni.e.\r\n11:02:07 | INFO  | c.n.n.e.t.l.EmailTemplatesLoader: template not found, creating it \r\n{ emailTemplate=ThreadInviteJoinEmailTemplate.json } \r\n11:02:07 | INFO  | c.n.n.e.t.l.EmailTemplatesLoader: done creating template \r\n{ emailTemplate=ThreadInviteJoinEmailTemplate.json } \r\n11:02:07 | INFO  | c.n.n.e.t.l.EmailTemplatesLoader: Initializing email template \r\n{ emailTemplate=InviteEmailTemplate.json } \r\n11:02:07 | INFO  | c.n.n.e.t.l.EmailTemplatesLoader: template not found, creating it \r\n{ emailTemplate=InviteEmailTemplate.json } ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r913190780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1027933973", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/28fbcf28-f7b3-42d7-b2dc-ecb26e56ace6)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=544f1def-16f2-488f-9a40-ce2c313fcebe).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r913296250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1027934751", "body": ""}
{"comment": {"body": "test\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/fbd59878-57d7-4aeb-a626-3e8ff80db812)\n\n\n\nhello\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/ed337c0d-8fab-4632-a7d9-85b1cebaf40d)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/8d7494cd-816b-42a4-ad19-8efd3d954eae)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/96d1693c-2851-4c8f-92cf-14001bad5744)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=a1a4596e-4a9d-4679-8105-6301a35506ef).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r913296839"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#pullrequestreview-1027939585", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/1544f4cc-748a-4f15-82ab-6e97448b6373)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/cb87264c-8de7-4e18-b325-167839b670fe?message=eb098a8e-8120-4644-8551-947fc3bcc0ae).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2129#discussion_r913301100"}}
