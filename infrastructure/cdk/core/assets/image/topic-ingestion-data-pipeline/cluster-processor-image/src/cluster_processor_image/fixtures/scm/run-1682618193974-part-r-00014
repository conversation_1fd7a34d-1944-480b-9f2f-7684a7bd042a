{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/397#pullrequestreview-891539367", "body": ""}
{"title": "Add countdown timer before recording starts", "number": 3970, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3970"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3970#pullrequestreview-1207314432", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3970#pullrequestreview-1207316111", "body": ""}
{"comment": {"body": "Does this need to be a weak self? No concerns on retention cycles?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3970#discussion_r1041453702"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3970#pullrequestreview-1207316867", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3970#pullrequestreview-1207334561", "body": ""}
{"comment": {"body": "The retention cycle is probably not present because it's detached within a Task, but I've added weak to be sure", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3970#discussion_r1041465812"}}
{"title": "Don't use bundleId to launch walkthrough app", "number": 3971, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3971"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3971#pullrequestreview-1207386639", "body": ""}
{"comment": {"body": "All our apps should be able to do tricks like this, right?  ie, they're all packaged in one app bundle so we can find all of them relative to each other, instead of using `urlForApplication` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3971#discussion_r1041496893"}}
{"title": "Simplify VSCode sidebar", "number": 3972, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972", "body": "This is purely changing the UI, not any of the data handling -- so we are still loading and processing the mine list even though we don't really need to anymore.  Once we've verified this is the UI we want we can cut out more.\n\n"}
{"comment": {"body": "Related PR & current file insights also gone?\r\n\r\nSo Explorer is the place to display this data?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#issuecomment-1340060522"}}
{"comment": {"body": "> Related PR & current file insights also gone?\r\n> \r\n> So Explorer is the place to display this data?\r\n\r\nYes, correct.  The sidebar only displays `related` and search results.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#issuecomment-1340062846"}}
{"comment": {"body": "Ah interesting, so `My Insights` isn't something we're displaying in vscode anymore?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#issuecomment-1340066326"}}
{"comment": {"body": "> Ah interesting, so My Insights isn't something we're displaying in vscode anymore?\r\n\r\nShould still show up in the explorer sidebar. Just not in the unblocked sidebar.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#issuecomment-1340078414"}}
{"comment": {"body": "> > Ah interesting, so My Insights isn't something we're displaying in vscode anymore?\r\n> \r\n> Should still show up in the explorer sidebar. Just not in the unblocked sidebar.\r\n\r\nNo, we don't show 'My Insights' in the explorer sidebar at all.  So the end goal here is to remove it from VSCode completely, the assumption is that people will use the hub to track your personal updates", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#issuecomment-1340095326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#pullrequestreview-1207368696", "body": ""}
{"comment": {"body": "This allows carets to be optional, and if they're not displayed, ensures the children are always shown", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#discussion_r1041486437"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#pullrequestreview-1207392526", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#pullrequestreview-1207393107", "body": "Assuming removing Related PR and current insights is on purpose."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#pullrequestreview-1207394377", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3972#pullrequestreview-1207396640", "body": ""}
{"title": "Simplify topic expert recommendation algorithm", "number": 3973, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3973", "body": "Increase pull request creator and reviewer weighting, and take the top three contributors instead of just looking at outliers."}
{"comment": {"body": "I'm going to create a comparison table in the admin console so that we can compare different algorithms at a glance.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3973#issuecomment-1340068896"}}
{"title": "...", "number": 3974, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3974"}
{"title": "Fix bert", "number": 3975, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3975", "body": "Fix bert training to create nested trees"}
{"title": "Add walkthrough launcher gif", "number": 3976, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3976"}
{"title": "Generate Asset Urls", "number": 3977, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3977", "body": "Generate assets url instead of depending on asset download url"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3977#pullrequestreview-1207589486", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3977#pullrequestreview-1207597917", "body": ""}
{"title": "Fix data caching for git commit existence check", "number": 3978, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978", "body": "There's been a regression where fetching the insights for private.yml can be quite slow, up to 10 seconds.  This fixes it.\nGitRunner.commitExists is called a lot by the sourcemark engine.  There are two levels of caching that it uses:\n1. We fetch all commits in one operation and store them in a set (TreeCache)\n2. If we can't find a commit in the TreeCache, we query using git cat-file, this occurs when commits don't exist in the tree (like PR commits that get dropped on merge and whatnot).  We intend to cache the output of these as well, using the general git command cache, but this never actually worked, because git cat-file works different then most git commands, in that we don't care about (or want to cache) the output of the command, we care about (and want to cache) whether the command succeeded or not.  The git operation would throw an exception, which bypassed caching.\nI wrote a separate set of GitRunner.run* methods that run \"boolean success\" commands, ie, those that disregard output, but only return true/false based on whether the command succeeds, and cache appropriately.\nOne outcome of this is that once the cache is warmed up, there is a lot of work being done synchronously when you flip between files, which causes the UI to block up.  I added a hacky chunked wait so large mark lists would behave a little better but this is not a great long-term solution.  We need to profile what is happening in the SM engine here and optimize.\nAlso a separate issue I found is that a unique GitRunner instance is created on every invocation of the SM engine.  This means the cache is less effective, though practically it doesn't seem to make a big difference on my machine.  This is pretty easy to fix, but I wasn't sure if it was intentional or not for some reason so I've left it alone for now."}
{"comment": {"body": "I am YOLO merging this now to fix the issue, @richiebres let's talk about this when you're back -- I suspect we should unwind at least part of this and implement it differently...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#issuecomment-1340321034"}}
{"comment": {"body": "> Also a separate issue I found is that a unique GitRunner instance is created on every invocation of the SM engine. This means the cache is less effective, though practically it doesn't seem to make a big difference on my machine. This is pretty easy to fix, but I wasn't sure if it was intentional or not for some reason so I've left it alone for now.\r\n\r\n@matthewjamesadam This is managed in SourceMarkManager. We create exactly one GitRunner per repo.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#issuecomment-1347223103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#pullrequestreview-1214286216", "body": ""}
{"comment": {"body": "@matthewjamesadam the main problem with this is that the git fetch UX in the read-only view will no longer work, since fetching a commit that does exist in the repo fails to bust the `''` cache value until 900,000 seconds (10 days) pass.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#discussion_r1046354104"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#pullrequestreview-1214287830", "body": ""}
{"comment": {"body": "@matthewjamesadam  Of the two changes you made, did you find that this one was actually effective?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#discussion_r1046355174"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#pullrequestreview-1214338013", "body": ""}
{"comment": {"body": "They were both needed.  For `private.yml` the other change reduced the overall processing time from > 10 seconds to  < 2 seconds, however the entire panel UI would block for those two seconds (ie we wouldn't show the loading spinner).  This change allowed that to run.\r\n\r\nThis is definitely a hack, I didn't know a best way to fix this.  Maybe there's some way to chunk this work up better, or maybe we should look into running the SM engine as a web worker (separate thread) or separate process, but those are likely a lot of work to implement...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#discussion_r1046387260"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#pullrequestreview-1214348792", "body": ""}
{"comment": {"body": "All the SM code is already async, so not obvious to me what the 1ms wait does. It's surprising.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#discussion_r1046395294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#pullrequestreview-1214364373", "body": ""}
{"comment": {"body": "To be honest I am not 100% sure.  I didn't get time to dig into this beyond finding a quick fix, and I wasn't familiar with the surrounding code.  I think the core of the problem is that once the git operations run cached (ie, they are not calling and waiting for the external git process execution), there is no operation that is forcing an asynchronous wait anywhere, so my guess is either:\r\n\r\n* The javascript VM is effectively running all of this code synchronously\r\n* Or there is so much work being done in queuing up all of the async work that *that* is bogging down the VM\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#discussion_r1046405749"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Interesting. I'll dig, thanks.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3978#discussion_r1046407256"}}
{"title": "Add slack webhook filtering", "number": 3979, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3979"}
{"title": "Added Bastion Host", "number": 398, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/398", "body": "Added a new stack to create bastion host under sec-ops account\nAdded necessary configs for bastion host to sec-ops config object\nUpdated sec-ops json env config file to list of retool public IPs and SSK key name for bastion host\nUpdated prod RDS to allow traffic from ***********/16 (sec-ops CIDR) so bastion host can talk to RDS (needed for retool)\nAdded public keys for both our management user as well as retool user\nCreated retool user on postgres (creds in 1p) and verified it on retool\nNot directly related - Created 'admin` user on postgres Dev and Prod. Added creds to 1Password\n\nall changes have been deployed to secops account. There were no changes to Dev. The only remaining change is IP address whitelist for prod which should be added by CI/CD."}
{"comment": {"body": "Probably should document the creation of the other users if we ever dump the db.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/398#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/398#pullrequestreview-*********", "body": "With comments."}
{"title": "Web compose to use authed asset urls", "number": 3980, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3980", "body": "Web Compose needs to follow a similar path to DiscussionThread and use authed urls for video assets."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3980#pullrequestreview-**********", "body": ""}
{"title": "Upgrade trainer instance types", "number": 3981, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3981"}
{"title": "Show the pull request at the top of the list of the \"Thread\" topics page", "number": 3982, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3982"}
{"comment": {"body": "It's glorious \ud83c\udfc6 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3982#issuecomment-1341368930"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3982#pullrequestreview-1208895574", "body": ""}
{"title": "Make sure we clean up thread titles", "number": 3983, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3983", "body": "Ensure internal thread titles are kosher during demos. :)"}
{"comment": {"body": "Do we need to censor PRs as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3983#issuecomment-1341412451"}}
{"comment": {"body": "> Do we need to censor PRs as well?\r\n\r\nGosh darn it,\r\n\r\n> Do we need to censor PRs as well?\r\n\r\nGood point!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3983#issuecomment-1341414165"}}
{"comment": {"body": "> > Do we need to censor PRs as well?\r\n> \r\n> Gosh darn it,\r\n> \r\n> > Do we need to censor PRs as well?\r\n> \r\n> Good point!!\r\n\r\nPhew good thing you didn't swear here and make any more work for the censoring system", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3983#issuecomment-1341415314"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3983#pullrequestreview-1208929780", "body": ""}
{"title": "Add client download url to get asset response", "number": 3984, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3984"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3984#pullrequestreview-1208991453", "body": "Love it. thank you"}
{"title": "Apps were missing bundle display name", "number": 3985, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3985", "body": "This makes them show up blank in the Activity Monitor. This PR also bumps the minimum version for the installer to macOS 12.3"}
{"title": "Fix", "number": 3986, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3986"}
{"title": "Capture initial code reference from hub walkthroughs.", "number": 3987, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3987", "body": "If you have file A open in VSCode before walkthrough app initialization, VSCode will try sending the file reference but nothing will be captured. Once walkthrough app initializes and starts running, VSCode will not resend file A since technically, the file never changed in VSCode.\nThis is not a problem within VSCode as we resend file A when VSCode initializes the walkthrough. \nThe fix here is to always send the latest file reference event to the walkthrough app whenever vscode is focused.\nThis may send duplicate events but too much data is better than too little data. Added dedupe logic to the walkthrough app which should remove unnecessary references.\nBased on Dennis' feedback.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3987#pullrequestreview-**********", "body": ""}
{"title": "Really fix it this time", "number": 3988, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3988"}
{"title": "Remove ssl scripts for processing", "number": 3989, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3989"}
{"title": "Add a discussion thread sidebar to vscode", "number": 399, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399", "body": "This pr does the following:\n1. Creates a webview provider for thread sidebar.\n2. Add a viewContainer to activityBar on left handside of vscode that loades thread sidebar webview.\n3. Fix up webpack to add assets to dist folder for vscode. This is necessary as were running the extension environment from the dist folder:\n            \"args\": [\"--extensionDevelopmentPath=${workspaceFolder}/dist\"],\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#pullrequestreview-891760308", "body": ""}
{"comment": {"body": "VSCode has some weird naming requirements for ids. for view containers.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#discussion_r813370657"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#pullrequestreview-891762280", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#pullrequestreview-891777656", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#pullrequestreview-891781751", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#pullrequestreview-891782648", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#pullrequestreview-891804663", "body": ""}
{"comment": {"body": "Should we fold this into `WebviewContentController`?  I think we want these options to be consistent for all webviews?  We might be able to set `webviewParent.webview.options` in the WebviewContentController contractor?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#discussion_r813406844"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#pullrequestreview-891805357", "body": ""}
{"comment": {"body": "I was thinking the same thing. I'm not sure why we aren't doing that TBH (for rest of commands)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/399#discussion_r813407384"}}
{"title": "RemoveSSLFromProcessing", "number": 3990, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3990", "body": "Remove ssl scripts for processing\nComment"}
{"title": "Run main things on main", "number": 3991, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3991"}
{"title": "Hub should use clientDownloadUrl instead of constructing its own", "number": 3992, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3992"}
{"title": "Return topic experts in getRelatedTopics", "number": 3993, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3993"}
{"title": "Cache file topics to reduce API calls", "number": 3994, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3994", "body": "VSCode is currently calling the getRelatedTopics API call every time a new file is focused.  There's no reason to do this, because the file -> topic mappings change very infrequently.  Introduce a cache that maps files to topics with a 10 minute ttl."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3994#pullrequestreview-1209229447", "body": ""}
{"comment": {"body": "Won't this create a new LRU every time this function is called?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3994#discussion_r1042757578"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3994#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3994#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Yep... however this is effectively only called once.  The WebviewProvider creates the stream, which calls this function once to generate the combined sub-stream and cache as a pair.\r\n\r\nI'm trying to avoid using statics everywhere if I can... this felt like it kept things functional.  I'm open to structuring this otherwise if you'd prefer.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3994#discussion_r1042760057"}}
{"title": "Add new data processing service", "number": 3995, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3995"}
{"title": "added cert-manager and resized nodes", "number": 3996, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3996", "body": "Updated EKS node groups but I haven't pushed them. I will be pushing them as part of our upcoming EKS cluster upgrade\nAdded cert-manager installation steps \nInstalled cert-manager in dev and prod \nUpdated EKS logging to reduce cost of CloudWatch logs\n\nFYI: The following are files exposed to container via cert-manager\nca.crt the root ca\n tls.crt actual cert \n tls.key private key"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3996#pullrequestreview-1209203549", "body": ""}
{"comment": {"body": "Woohoo :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3996#discussion_r1042739206"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3996#pullrequestreview-1209204539", "body": ""}
{"title": "Pass all events through the countdown window", "number": 3997, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3997"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3997#pullrequestreview-1209216704", "body": ""}
{"title": "Show file-scoped threads in explorer insights", "number": 3998, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3998", "body": "We were filtering out any file-scoped (ie, video) threads here.  Instead we want to always show them."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3998#pullrequestreview-1209227664", "body": ""}
{"comment": {"body": "Are we using this as an escape valve as well?\r\n\r\naka if a non file-mark is missing sourcePoints (or mark entirely), we should still try to show the thread?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3998#discussion_r1042756767"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3998#pullrequestreview-1209227781", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3998#pullrequestreview-1209228439", "body": ""}
{"comment": {"body": "Yeah pretty much -- I figured this was probably fine?  Alternatively we could filter out those as we could possibly consider them bad data...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3998#discussion_r1042757251"}}
{"title": "Update contributors list style", "number": 3999, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3999", "body": "Updated backgrounds and borders.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3999#pullrequestreview-1209245252", "body": ""}
{"comment": {"body": "Nested everything within this div.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3999#discussion_r1042769686"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3999#pullrequestreview-1209289883", "body": ""}
{"title": "Break", "number": 4, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4"}
{"title": "Start private spec", "number": 40, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/40", "body": "This is a basic pr for moving private spec over.\nlet me know if this is good enough. :)\nAnd feel free to do whatever you want afterwards..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/40#pullrequestreview-*********", "body": "Will be updating this for web in future PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/40#pullrequestreview-*********", "body": ""}
{"title": "Create resources for streaming service", "number": 400, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400", "body": "Added a new stack to create the following resources\nIAM user within each env account. It's used only for uploads to an S3 bucket\nSecret AccessKey/secret for the user. We also add it to ASM for later mounting on EKS\nCreated an S3 bucket for streaming assets\nEnabled automatic tiering on S3 bucket\nCreated IAM policy to grant user access to S3 bucket\n\nOur apporach with IAM user in each app account is temporary. We would like to move towards STS tokens or signed URLs if Agora folks help us. For now this should unblock Peter."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Question, are we running  \"npm run check-lint\" in CI?\r\nWe should be as I've noticed a few problems with the infrastructure code in terms of formatting.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#discussion_r813431984"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#pullrequestreview-*********", "body": "With comments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#pullrequestreview-*********", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/402", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#discussion_r813443901"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#pullrequestreview-891910103", "body": ""}
{"comment": {"body": "I can't install it locally. It gives me this weird auth error from npm. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#discussion_r813491190"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#pullrequestreview-891910630", "body": ""}
{"comment": {"body": "I ended up doing the best I could by running the pretty plugin in vscode ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#discussion_r813491640"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#pullrequestreview-891910851", "body": ""}
{"comment": {"body": "FontAwesome\r\nWe currently use the pro package of Fontawesome for our Icons. This requires access to a private NPM registry when running npm install.\r\n\r\nYou will need the Pro Package Token from 1Password.\r\n\r\n~..zshrc\r\n\r\nexport FONTAWESOME_NPM_AUTH_TOKEN=<Auth Token from 1Password>", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/400#discussion_r813491819"}}
{"title": "update all helm charts to include the cert volume", "number": 4000, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4000", "body": "Adding /certs to all pods. \nUpdating all helm charts \n\nCerts volume will contain the following files:\nca.crt the root ca\ntls.crt actual cert\ntls.key private key\nCurrently we set the following fields in the cert:\nSAN set to {SERVICENAME}.{NAMESPACE}.{AWS_REGION}.{ENV}.getunblocked.com\nCommon name set to {AWS_REGION}.{ENV}.getunblocked.com\nWe can set additional attributes as needed. You can find a full list of attributes here: \nExample cert: \n-----BEGIN CERTIFICATE-----\nMIIDLTCCAhWgAwIBAgIRALGrGpoRnkeacQTToSZvYOMwDQYJKoZIhvcNAQELBQAw\nKjEoMCYGA1UEAxMfdXMtd2VzdC0yLnByb2QuZ2V0dW5ibG9ja2VkLmNvbTAeFw0y\nMjEyMDcyMTU1NTBaFw0yMzAzMDcyMTU1NTBaMAAwggEiMA0GCSqGSIb3DQEBAQUA\nA4IBDwAwggEKAoIBAQDRSR3sk78+tjPk11ZYNWombWAA3M6JHqM3igbEA+qQR1am\nGCw/NVQZqGRepwhMnw3DHuSESDvw28n7e4gPBU/0oj3yrIl3eUHxJfQqeLCDHwtC\nWkUxRIli27JFmOobWnO6qPdnxdwV0XKcZpVLf3BSatLGh3GNg+GXk7nFFEZgvi1Y\n3jTvtGhWXH8INetQDFDnV1YJ3FjUWLZw1zcD9HOHDYd2xMT/W0tENmCuYb5jeXgZ\nmveM/DbS6cd/zTrrDB+adzLBbuC4aaOga79TGkUifx5sKMp+Uwv/7MpaAvpgAeQc\nGuPtRlRk2e8ZJE4Ld7nQ3+DQ/r4Tu5ChFSClhKbTAgMBAAGjeDB2MA4GA1UdDwEB\n/wQEAwIFoDAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFMUluJbWuuavvB11zO43\nkl/rOGxrMDUGA1UdEQEB/wQrMCmCJ29uZS50d28udXMtd2VzdC0yLnByb2QuZ2V0\ndW5ibG9ja2VkLmNvbTANBgkqhkiG9w0BAQsFAAOCAQEAzSKjVxDMPaeufD91Zy8d\nz4tFAWUmw6oGjhwEUZr4t/wVfRgCgL0waxaB5gyiZuEHlcjKG6y5701vbKXZs/vm\nsZtLwV5qolSXW1pENQYBZgSBgqxEZlbWF+E5+a6POcqJwojEw1ntl4JOqw3KERfq\n0ZLXjRVYOgl71s/nBEIx/eHzGyWD0vSzvn5EdhdOUOEybIPzq/KlxH+q0olOcCXv\nG/+ASz+RWfDGmBF5nslmNwmwaHOPe5noKMfN5sR698woWXchip9sA4Hg22HpPAm5\nRKqcpO+L8rfjgReaitbv8jpEwk1Ma2o7I7Q56NYI5/cCSU0I1CmSDOgar3+jWhn8\npA==\n-----END CERTIFICATE-----"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4000#pullrequestreview-1209246404", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4000#pullrequestreview-1209246680", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4000#pullrequestreview-1209248622", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4000#pullrequestreview-1209249337", "body": ""}
{"title": "[BREAKS API ON MAIN] Rename expertIds to teamMemberIds", "number": 4001, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4001", "body": "My understanding is when this field is populated, we want to limit the search to insights authored by team members in that list. These team members may or may not be experts, so I think calling it teamMemberIds is more accurate. This API isn't being used (yet)."}
{"comment": {"body": "Makes sense to me", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4001#issuecomment-1341777544"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4001#pullrequestreview-1209280629", "body": ""}
{"title": "Always render the watermark", "number": 4002, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4002"}
{"title": "Wheeeeee late night coding is fun", "number": 4003, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4003"}
{"title": "Try detaching camera from preview", "number": 4004, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4004"}
{"title": "PersistData", "number": 4005, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4005", "body": "persist data\nI htink this should work :)\nUpdate"}
{"title": "Update walkthrough gif", "number": 4006, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4006"}
{"title": "Use Xcode 14.1", "number": 4007, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4007"}
{"title": "Deploy more changes for data", "number": 4008, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4008"}
{"title": "Revert detached preview", "number": 4009, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4009"}
{"title": "Rename from threadSidebar", "number": 401, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/401"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/401#pullrequestreview-891829692", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/401#pullrequestreview-891829930", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/401#pullrequestreview-891830092", "body": ""}
{"title": "Explicitly copy sample buffers", "number": 4010, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4010"}
{"comment": {"body": "testing a comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4010#issuecomment-1343262468"}}
{"title": "Fix deployments", "number": 4011, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4011"}
{"title": "Fix content type", "number": 4012, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4012"}
{"title": "Add scm buckets", "number": 4013, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4013"}
{"title": "Add scm data inputs", "number": 4014, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4014"}
{"title": "Modify web file references in web composer", "number": 4015, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4015", "body": "Ability to add and remove related links for web.\n"}
{"comment": {"body": "Updated button style\r\n<img width=\"660\" alt=\"CleanShot 2022-12-08 at 16 26 52@2x\" src=\"https://user-images.githubusercontent.com/1553313/206594832-947bfa65-7fb1-402b-9576-ea385be3c2fd.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4015#issuecomment-1343636367"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4015#pullrequestreview-1214284380", "body": ""}
{"comment": {"body": "should this be flex-center-start? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4015#discussion_r1046352865"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4015#pullrequestreview-1214286148", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4015#pullrequestreview-1214454905", "body": ""}
{"comment": {"body": "No. In the \"add button\" case, the designs actually have it as between.\r\nThis is different from what the screenshots show as those are using the icon button.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4015#discussion_r1046470041"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4015#pullrequestreview-1214456371", "body": ""}
{"comment": {"body": "But hasn't the Add button been removed? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4015#discussion_r1046470823"}}
{"title": "Adding labeling notebook", "number": 4016, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4016", "body": "This checkin cleans up the new folder that @rasharab made for the ml python work and then adds in a notebook where we are doing our labeling work that pulls from S3 and then creates a simple example -> labels map."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4016#pullrequestreview-1210942365", "body": ""}
{"title": "Use a single CIContext to avoid pixel buffer leaks", "number": 4017, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4017"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4017#pullrequestreview-1210934730", "body": ""}
{"comment": {"body": "This is the fix I kid you not", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4017#discussion_r1043937100"}}
{"title": "Immediately slurp the active foreground app", "number": 4018, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4018"}
{"title": "TakeScmDataProcessing", "number": 4019, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4019", "body": "Add scm and slcka processing\nupdate"}
{"title": "Lint cdk source code", "number": 402, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/402", "body": "We should be linting the cdk codebase.\n"}
{"title": "Fix test", "number": 4020, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4020"}
{"title": "Clean up lambda", "number": 4021, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4021"}
{"title": "Suppress topiic service honeycomb", "number": 4022, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4022"}
{"title": "search service sampling increase", "number": 4023, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4023"}
{"title": "Move image assets", "number": 4024, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4024"}
{"title": "Add sanitization jobs", "number": 4025, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4025"}
{"title": "update", "number": 4026, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4026"}
{"title": "[BREAKS API ON MAIN] VideoDraft API V2", "number": 4027, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4027"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4027#pullrequestreview-1212340726", "body": ""}
{"comment": {"body": "Note: previewAssetId is optional", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4027#discussion_r1044901741"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4027#pullrequestreview-1212340995", "body": ""}
{"comment": {"body": "Make the draft model team bound", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4027#discussion_r1044901938"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4027#pullrequestreview-1212341312", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4027#pullrequestreview-1212342222", "body": ""}
{"comment": {"body": "In seconds? Add a description.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4027#discussion_r1044902866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4027#pullrequestreview-1214245178", "body": ""}
{"title": "Fix search navigation", "number": 4028, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4028"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4028#pullrequestreview-1212349555", "body": ""}
{"title": "Add associated topics to ThreadInfo and PullRequest", "number": 4029, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4029", "body": "Also add optional list of participants to PullRequest"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4029#pullrequestreview-1214358145", "body": ""}
{"title": "Paralellize calling poll get fns", "number": 403, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/403"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/403#pullrequestreview-893059029", "body": ""}
{"comment": {"body": "Do we need to batch these into smaller chunks? 2/3 at a time?\r\n\r\nThis could fully saturate the request bandwidth.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/403#discussion_r814312816"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/403#pullrequestreview-893059666", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/403#pullrequestreview-893064697", "body": ""}
{"comment": {"body": "The browser will basically do this for us based on its own rate limiting.  I'm not sure that adding our own logic here will help necessarily?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/403#discussion_r814316876"}}
{"title": "Video Draft List", "number": 4030, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030", "body": "Added Video Draft List route and their corresponding rows.\n\nTODO: Add entry point to sidebar. This may require rejigging fetching / updating of draft lists in order to populate the sidebar inbox number."}
{"comment": {"body": "> TODO: Add entry point to sidebar. This may require rejigging fetching / updating of draft lists in order to populate the sidebar inbox number.\r\n\r\nIf we want a number that works correctly we will probably need a push channel", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#issuecomment-1350106636"}}
{"comment": {"body": "> > TODO: Add entry point to sidebar. This may require rejigging fetching / updating of draft lists in order to populate the sidebar inbox number.\r\n> \r\n> If we want a number that works correctly we will probably need a push channel\r\n\r\nCorrect...\r\nI was planning on tackling this next in a separate PR. Was considering doing a really low latency polling initially with some manual triggers. This is currently only used on the dashboard so user can always manually refresh if necessary.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#issuecomment-1351989885"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1216592322", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1216595296", "body": ""}
{"comment": {"body": "This is getting into a lot of business logic and store stuff -- maybe this should be moved into its own store stream so we aren't mixing view updates with this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#discussion_r1047875133"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1216620564", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1216620779", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1216622795", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1216636184", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1216637537", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218033268", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218050986", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218118524", "body": "lgtm save for matt's outstanding comment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218258066", "body": ""}
{"comment": {"body": "Updated with VideoDraftStore", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#discussion_r1048981048"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218302145", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218336112", "body": ""}
{"comment": {"body": "The filtering stuff is to remove items when deleted, right?  Should we be using `StreamOverlay` for this?  I could see an implementation that uses `StreamOverlay`, and after deleting calls `refresh()` again to get an official updated set of values (since we have no pusher channel)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#discussion_r1049031810"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218338601", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218347826", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218350636", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218351980", "body": "A bit more random feedback, take it or leave it as you see fit"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#pullrequestreview-1218442358", "body": ""}
{"comment": {"body": "Updated to use overlay.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4030#discussion_r1049106484"}}
{"title": "Missing ref for create walkthrough", "number": 4031, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4031", "body": "Missing ref... Most likely from bad merge.\nWithout this, blocks within message editor were not saved in walkthrough creation."}
{"title": "Remove noisy log", "number": 4032, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4032", "body": "This log is no longer necessary as failed connections to walkthrough app are expected.\nVSCode used to only connect with the walkthrough app on demand. aka whenever VSCode wanted to start a video walkthrough.\nThis was changed so that VSCode will consistently try connecting to the walkthrough app on launch, regardless of video walkthrough intent."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4032#pullrequestreview-1214211412", "body": ""}
{"title": "Avoid creating and discarding a GitRunner during lookup", "number": 4033, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4033", "body": "From here:\nhttps://github.com/NextChapterSoftware/unblocked/pull/3978#issuecomment-1347223103\nAvoid resolving a Git repo each time. (Has no effect on the cache)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4033#pullrequestreview-1214331679", "body": ""}
{"title": "Revamp insight cards", "number": 4034, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034", "body": "Per new designs:\n\nStill missing:\n* Associated topics (https://github.com/NextChapterSoftware/unblocked/pull/4029)\n* PR participants \n    * Right now the upper section only shows the author (instead of all of the participants)"}
{"comment": {"body": "The hash-tag icon here seems big?  I kind of feel it should just be part of the text instead of a separate icon?\r\n\r\n<img width=\"209\" alt=\"Screen Shot 2022-12-12 at 3 28 42 PM\" src=\"https://user-images.githubusercontent.com/2133518/207181652-33ea6299-93b3-45f1-b5de-efbe947ea8d9.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#issuecomment-1347489689"}}
{"comment": {"body": "> The hash-tag icon here seems big? I kind of feel it should just be part of the text instead of a separate icon?\r\n> \r\n> <img alt=\"Screen Shot 2022-12-12 at 3 28 42 PM\" width=\"209\" src=\"https://user-images.githubusercontent.com/2133518/207181652-33ea6299-93b3-45f1-b5de-efbe947ea8d9.png\">\r\n\r\nles designs:\r\n<img width=\"562\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/207187584-ea84625d-747c-4eb1-9855-d721b5def2fd.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#issuecomment-1347550694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#pullrequestreview-1214472021", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#pullrequestreview-1214472642", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#pullrequestreview-1214477901", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#pullrequestreview-1214479999", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#pullrequestreview-1214482536", "body": ""}
{"comment": {"body": "Can we add some tests for these?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#discussion_r1046490077"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#pullrequestreview-1214514572", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#pullrequestreview-1214567369", "body": ""}
{"comment": {"body": "done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#discussion_r1046553737"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4034#pullrequestreview-1214571557", "body": ""}
{"title": "Add parallel pipelines", "number": 4035, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4035"}
{"title": "move to bert topics ingestion", "number": 4036, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4036"}
{"title": "Update statefulness of VSCode walkthrough", "number": 4037, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037", "body": "This PR does two things:\n\n\nWe were \"completing\" _videoWalkthroughStateStream. This would mean that subsequent connections / walkthroughs would not attach file references correctly as this stream not longer received / published events.\n\n\nVSCode is currently publishing events, regardless of video walkthrough state. It will now only publish events if there is an active walkthrough based on the Video Walkthrough app's running state."}
{"comment": {"body": "cc: @pwerry @richiebres for no. 2", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#issuecomment-1347442178"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214446256", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214459649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214460977", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214461186", "body": "This looks ok to me but maybe @matthewjamesadam wants to weigh in"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214479927", "body": ""}
{"comment": {"body": "Should we add a state to `VideoWalkthroughState` ('disconnected` or something?) so that _videoWalkthroughStateStream` be of type `Stream<VideoWalkthroughState>`, not `Stream<VideoWalkthroughState | undefined>`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#discussion_r1046488107"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214480351", "body": ""}
{"comment": {"body": "(or isn't that what the `endVideo` state is?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#discussion_r1046488442"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214483502", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214490336", "body": ""}
{"comment": {"body": "Yeah. That's what endVideo is. It's basically the \"complete\" state and isn't typed in the grpc spec.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#discussion_r1046495726"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214490777", "body": ""}
{"comment": {"body": "Maybe we call this something neutral like 'inactive' or 'disconnected' or something, and use this as the initial state?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#discussion_r1046496013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#pullrequestreview-1214545215", "body": ""}
{"comment": {"body": "Yup. Had updated it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4037#discussion_r1046537258"}}
{"title": "Show topic experts in VSCode editor tooltip", "number": 4038, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038", "body": "Show the top three experts for a file, in the VSCode editor's tooltip.\nI made a new class (FileTopicStream), whose purpose is to return the set of topics relevant to a file.  This merges a lot of upstream data and fetches the topic when needed.  This is now used by the explorer insights panel and the TextEditorSourceMarks code.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#pullrequestreview-1214468137", "body": ""}
{"comment": {"body": "This is intended to be a globally-reusable class that gives the dashboard URL for a given thing (right now, for a topic).  We could use this in the dashboard itself for navigation events, if we wanted to, but I didn't quite go that far.  Thoughts?\r\n\r\n(It also occurs to me that the `Topic` model could have a dashboard URL property as well, then this wouldn't be necessary)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#discussion_r1046479373"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#pullrequestreview-1214468427", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#pullrequestreview-1214497965", "body": ""}
{"comment": {"body": "We've typically avoided generating these types of URLs locally. E.g. `ThreadLinks`\r\nIs this something we could do with topics as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#discussion_r1046500692"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#pullrequestreview-1214500299", "body": ""}
{"comment": {"body": "Yes ideally this would come from the Topic model, but it's not on there now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#discussion_r1046502240"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#pullrequestreview-1214502501", "body": ""}
{"comment": {"body": "IMO, not as important if this was just a dashboard util but if this is used in VSCode, there's a chance these hard-coded urls don't match the deployed service.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#discussion_r1046503575"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#pullrequestreview-1214533947", "body": ""}
{"comment": {"body": "This enables a topic to render of every line?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#discussion_r1046528769"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#pullrequestreview-1214535612", "body": ""}
{"comment": {"body": "Yes, just a cheap way of applying the tooltip to every character on every line.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#discussion_r1046530015"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4038#pullrequestreview-1214536505", "body": ""}
{"title": "Add histogram sub pipeline", "number": 4039, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4039", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4039#pullrequestreview-1214563543", "body": ""}
{"title": "Consolidate creating discussions/notes into one component", "number": 404, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404", "body": "NOTE: Lots of file changes but this is primarily due to me moving things around and deleting older files. Bulk of the work is in vscode/src/knowledge/ and shared/webComponents/Button/\n\nRefactored the ability to create discussions/notes into a single command/webview/form component\nEssentially the idea is the abstract them into a common 'Knowledge' type concept since the UI is largely similar \n\n\n\n\n\n\nSimplified the file structure of the top level folders in the vscode/ directory (this is open to feedback)\nvscode/src/\n    discussion/  // @discussion alias\n        discussionThread/ // all thread view related UIs and commands\n        startDiscussion/ // all start discussion specific UIs and commands\n        DiscussionCommands.ts // file that can be shared\n    notes/ // @notes alias\n        addNote/ // all add note specific UIs and commands \n    knowledge/ // @knowledge alias\n        CreateKnowledge/ // Common UI used in for startDiscussion and addNote, component is referenced in the extension.startDiscussion.ts and extension.addNote.ts \n    ... // other directories, etc\n\n\nRefactored Button component to shared/ directory - similar to the MessageEditor component, it then gets imported to each client directory and reexported with the client-specific styling\n\nDone mostly in part to support the dropdown logic which will may be required to be shared across clients"}
{"comment": {"body": "<img width=\"496\" alt=\"CleanShot 2022-02-25 at 09 11 11@2x\" src=\"https://user-images.githubusercontent.com/1553313/155757834-535e7299-8947-430c-85d5-f42d8810c2a9.png\">\r\nIs this the expected styling for web?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#issuecomment-1051037016"}}
{"comment": {"body": "There are a bunch of little issues Jeff and I are referencing here, but overall I think this looks good.  I wonder if we will see more \"creation\" forms designed that might not fit this template, but we can cross that bridge when we get to it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#issuecomment-1051044483"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-892916609", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-892961267", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-892962968", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-892965259", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-892973549", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-892976074", "body": ""}
{"comment": {"body": "TBH I'm not sure the map mixin improves legibility over simply defining the properties in the CSS below, but I'm open to either direction", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r814254094"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-892979318", "body": ""}
{"comment": {"body": "Should we remove these commands now?  I'm not sure they're useful since we have a code action provider?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r814256373"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893098076", "body": ""}
{"comment": {"body": "Is there a use case for not having discussions/notes tied to a specific piece of code? ie a note made for an entire file. I'm not sure how that would be annotated but it seems like a valid scenario that we should support (..?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r814341063"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893193554", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893199872", "body": ""}
{"comment": {"body": "I'm curious, is there a reason for building up these items using useMemo instead of just building it in JSX below?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r814421875"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893201018", "body": ""}
{"comment": {"body": "Ah I guess it's to make composing the different forms easier below", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r814422881"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893945187", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893947659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893950744", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893952501", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893957545", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-893965699", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894269101", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894275981", "body": ""}
{"comment": {"body": "Wonder if this needs to be updated whenever the viewport size changes as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815185397"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894277223", "body": ""}
{"comment": {"body": "The last time I had interactions, I had to add some delays for chromatic as it introduced flake.\r\n\r\nhttps://www.chromatic.com/docs/delay", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815186208"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894277989", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894278706", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894279349", "body": ""}
{"comment": {"body": "Do we need an onClick if there's already an onChange?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815187702"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894279873", "body": ""}
{"comment": {"body": "Maybe for a 100% complete implementation, but since in this case the dropdown is transient I'm not sure it's critical that this bit work perfectly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815188114"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894279887", "body": ""}
{"comment": {"body": "Is this the equivalent to the StartDiscussion right now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815188124"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894284131", "body": ""}
{"comment": {"body": "I think I understand the intention behind this knowledgeForm. Because we have multiple UIs that are similar, it does make sense to reuse as many components as possible.\r\n\r\nMy slight concern with this approach of having \"types\" and switching on them is that it tightly couples the all the types together and makes customizing the views more difficult.\r\n\r\nFor example, in the knowledgeType.notes situation, it doesn't actually need the contributors section but it still exists in the props and is part of the rendering.\r\n\r\nSince you've already done the work on splitting up the views into smaller reusable components, we could consider having top level components that compose these smaller components and which have their own props.\r\n\r\naka instead of switching on type for discussion, note, etc... each of these types could have their own components which are built using the `KnowledgeForm` components.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815191265"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894286747", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894287784", "body": ""}
{"comment": {"body": "Should this be a shared thing?\r\nCould see it being useful in web extension", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815194098"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894288411", "body": ""}
{"comment": {"body": "Also, we may want to refactor this into shared. I assume we're going to have similar flows and UI in web extension.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815194611"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894289958", "body": ""}
{"comment": {"body": "yeah unless the dropdown is fixed, it'll always be positioned relative to the viewport I think? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815195784"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894291750", "body": ""}
{"comment": {"body": "I think we can cross that bridge when we get there? It should be easy to move it out when it needs to be moved out ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#discussion_r815197357"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/404#pullrequestreview-894298896", "body": "Most of my comments are more for the future. In general, things look good to me."}
{"title": "Attach topics to insights", "number": 4040, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4040", "body": "per https://github.com/NextChapterSoftware/unblocked/pull/4029, topicIds are now returned with threadInfo's and pullRequest's. \nThis PR aggregates the topicIds to topics.\n\n~Note: This PR doesn't add topics to the UI yet, just handles the data aggregating to the insights.~"}
{"comment": {"body": "Updated with topic tags/chips:\r\n\r\n<img width=\"893\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/207472988-c7875455-6f20-4d97-acc5-17d477cdd9c9.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4040#issuecomment-1350140611"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4040#pullrequestreview-1216488207", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4040#pullrequestreview-1216520925", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4040#pullrequestreview-1218252445", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4040#pullrequestreview-1218256111", "body": ""}
{"comment": {"body": "I'm kind of wondering if we should have another helper that aggregates the stream states, so that we don't have to duplicate this logic everywhere.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4040#discussion_r1048979738"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4040#pullrequestreview-1218270691", "body": ""}
{"title": "Cache all commits in the repo, not just the commits in the tree", "number": 4041, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4041", "body": "Changes\n\nReverts #3978, as no longer necessary.\nFix bug in CommandLog that prevented visibility into git commands that had non-zero exit status.\nBulk cache all commits in the repo, not just commits in the tree.\nInvalidate caches when the user fetches or pulls via our \"read-only\" UX.\nOptimize cache storage a bit to offset the extra objects stored.\n\nResult\n\nWe were making 10,000s of calls to git cat-file -e SHA^{commit} that had non-zero exit status.\n  Now there are zero."}
{"comment": {"body": "Much better then my hacky solution \ud83d\ude06 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4041#issuecomment-1349457055"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4041#pullrequestreview-1216198071", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4041#pullrequestreview-1216202620", "body": ""}
{"comment": {"body": "This doesn't handle the case where fetch/pull is done through another mechanism (through the IDE, on the command line or another git app) -- I'm guessing the answers from the TreeCache will be wrong in these scenarios, but I'm not sure what the end result of this would be?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4041#discussion_r1047565857"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4041#pullrequestreview-1216204525", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4041#pullrequestreview-1216249629", "body": ""}
{"comment": {"body": "Pull outside of our extension UX is fine as long as the HEAD changes.\n\n\n\nIf the fetch (or pull without HEAD change) invoked outside of our extension UX occurs, then unblocked may think that a commit does not exist in the repo when it actually does.\n\n\n\nThe only impact [that I can think of currently] is the read-only view would show the \"commit does not exist in repo, try fetching\" UI instead of showing a read-only UI. And if they hit fetch button, the cache would be immediately flushed and we'd recognize the commit.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4041#discussion_r1047597704"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4041#pullrequestreview-1216282133", "body": ""}
{"title": "Trigger topics ingestion", "number": 4042, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4042"}
{"title": "Fix step function creds", "number": 4043, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4043"}
{"title": "Update stack names", "number": 4044, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4044"}
{"title": "Fix topic ingestion id", "number": 4045, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4045"}
{"title": "update", "number": 4046, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4046"}
{"title": "Fix IAM permission for s3 for topic service", "number": 4047, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4047"}
{"title": "update docs", "number": 4048, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4048"}
{"title": "Add retries top ipelines", "number": 4049, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4049"}
{"title": "Improve stage retries", "number": 4050, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4050", "body": "Ensure we retry only on specific subset of errors."}
{"title": "Rev API for VideoMetadata changes", "number": 4051, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051", "body": "API changes only. Web clients were modified to use the LegacyVideoMetadata model in place of the VideoMetadata model in all instances."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216438539", "body": ""}
{"comment": {"body": "Is it possible to get rid of `createThread` right now? Shouldn't be any clients using it at this point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047762081"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216440059", "body": ""}
{"comment": {"body": "It might be but I'd prefer to do any API cleanup tasks in another PR. We're going to push the latest clients out to Stable soon, which will bring the API backwards compatibility checks up to date for a follow-up ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047763502"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216440684", "body": ""}
{"comment": {"body": "I'd lean towards making this required at this point.\r\nI'm not sure what undefined would represent here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047764219"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216444029", "body": ""}
{"comment": {"body": "Updating this to `assetUrl` would break backwards compatibility?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047767146"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216444046", "body": ""}
{"comment": {"body": "Do you mean `threadType`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047767155"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216444656", "body": ""}
{"comment": {"body": "Sadly yes. Renaming is a break", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047767574"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216446209", "body": ""}
{"comment": {"body": "Should this `CreateMessageRequest` be deprecated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047768651"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216446485", "body": ""}
{"comment": {"body": "Same here. Deprecation?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047768831"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216451470", "body": ""}
{"comment": {"body": "Yes. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047772379"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216459401", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216518814", "body": ""}
{"comment": {"body": "Instead of changing all the TS code to `Legacy*` it would probably be easier and less disruptive to just adopt the new model", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047819210"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216561034", "body": ""}
{"comment": {"body": "I think Peter wanted to keep the PR lean. I'll be doing this now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047849617"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216563362", "body": ""}
{"comment": {"body": "Adopting the new model needs to wait until the backend actually supports those API calls. They're just stubs right now but I'm actively implementing those now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047851283"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216584899", "body": ""}
{"comment": {"body": "I'm maybe missing something, but I think `threadParticipants` should be removed -- the thread participants for a new thread is defined by who posts the thread?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047867515"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216645713", "body": ""}
{"comment": {"body": "We should definitely remove this. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047912111"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#pullrequestreview-1216648542", "body": ""}
{"comment": {"body": "Actually I think these might be invite slots?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4051#discussion_r1047914270"}}
{"title": "Add API for clients to provide folders for topic generation", "number": 4052, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#pullrequestreview-1217867720", "body": ""}
{"comment": {"body": "This is intentional -- I figured we might need to update third-party (open source?) project folder structures while impersonating project owners.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#discussion_r1048723712"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#pullrequestreview-1217876153", "body": ""}
{"comment": {"body": "I don't love this, we lose all typing in the data, but I think the only alternative is to get ktor working?  @rasharab @davidkwlam any thoughts?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#discussion_r1048729513"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#pullrequestreview-1217903599", "body": ""}
{"comment": {"body": "Kind of a gross suggestion: perhaps we can add a runtime check to only allow this for open source repos if the user is read-only.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#discussion_r1048746970"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#pullrequestreview-1217921940", "body": ""}
{"comment": {"body": "Yeah that's one option.  This call is analogous in this way to the SourceMark update API (`putRepoSourcePoints`), which also allow mutations with a read-only token.  The reasoning is that these APIs are not caused by human actions (posting a thread or message, etc), but are executed automatically to keep the system up-to-date with client-side data.  In this scenario there is no risk of an unblocked admin who is impersonating a user accidentally changing user-modifiable data.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#discussion_r1048756162"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#pullrequestreview-1217962113", "body": ""}
{"comment": {"body": "I'm fine with this since we're kind of doing something analagous for source points.\r\nWe have to dedicate sometime to getting this working natively in ktor. I saw a ktor issue where we can kind of get this working, but with edge cases.\r\n\r\nhttps://youtrack.jetbrains.com/issue/KTOR-1248/Support-decompression-for-incoming-request-bodies", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#discussion_r1048782827"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4052#pullrequestreview-1217962533", "body": "I'm okay with this, make it so."}
{"title": "Clean up topics page", "number": 4053, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4053"}
{"title": "Modify web references in VScode", "number": 4054, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054", "body": "Adds support to adding &\n\n removing web references in VSCode.\n\n\nAlso added logic so negative positions, which are set for manually added references, will appear at the end of the references list without a timestamp\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1216640779", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1216643127", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1217931255", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1218306332", "body": ""}
{"comment": {"body": "What does a negative movie position mean in this case?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#discussion_r1049012743"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1219643329", "body": ""}
{"comment": {"body": "Manually added references will have a negative position. This is used to sort those negative positions to the bottom.\r\n\r\nWill update the naming.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#discussion_r1049932120"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1219650358", "body": ""}
{"comment": {"body": "Instead of a negative number (which feels kind of ... unexpected? obscure?) can we make the position optional?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#discussion_r1049938553"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1219651801", "body": ""}
{"comment": {"body": "There is also a helper for this -- `ArrayUtils.divide` does what you're doing here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#discussion_r1049939605"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1219656960", "body": ""}
{"comment": {"body": "This is a symptom of the difficult to change API spec \ud83d\ude22 . Might be a bit better now with Peter's change.. Will give that a shot.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#discussion_r1049943324"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1219658123", "body": ""}
{"comment": {"body": "If it's going to cause a ton of work or heartache it's fine as-is, as long as we document it so we actually understand what it means", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#discussion_r1049944088"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1219711741", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#pullrequestreview-1219712440", "body": ""}
{"comment": {"body": "Might want to move the processing here into a util function?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4054#discussion_r1049979967"}}
{"title": "Sourcemark incorrectly treated a change as Modified Unrecognizably", "number": 4055, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4055", "body": ""}
{"title": "Add lemmatization", "number": 4056, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4056"}
{"title": "Update histogram script", "number": 4057, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4057"}
{"title": "Add error handling to walkthrough complete", "number": 4058, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4058", "body": "There is a potential bug where VSCode would throw an error before sending the disconnect event to the walkthrough app.\nIn the event there is a failure on VSCode, we should still try to send the disconnect event to the walkthrough app so that it closes."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4058#pullrequestreview-1216621850", "body": ""}
{"title": "Add createdAt to VideoMetadata", "number": 4059, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4059"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4059#pullrequestreview-1216539801", "body": ""}
{"title": "SourceMark Application Runner", "number": 406, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/406", "body": "separate SourceMark app to demonstrate concept\nuses polling strategy to monitor repos\n\nPlan: \nNext PRs:\n- [ ] Git primitives to support SourceMark calculation\n- [ ] SourceMark calculation"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/406#pullrequestreview-892836603", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/406#pullrequestreview-892838168", "body": ""}
{"title": "Write out json", "number": 4060, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4060"}
{"title": "Scoring BERT + Slack", "number": 4061, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4061", "body": "Walked @matthewjamesadam through this live earlier. No impact on data pipeline or app."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4061#pullrequestreview-1216543401", "body": ""}
{"title": "Update topics page in admin console", "number": 4062, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4062"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4062#pullrequestreview-1216561570", "body": ""}
{"title": "Move markdown processing", "number": 4063, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4063"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4063#pullrequestreview-1216558901", "body": ""}
{"comment": {"body": "Needed this as well.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4063#discussion_r1047848065"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4063#pullrequestreview-1216559547", "body": ""}
{"title": "Fix docker file", "number": 4064, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4064", "body": "Need to inclue additional phython modules in docker image\n$252Faws$252Fsagemaker$252FProcessingJobs/log-events/PreProcessTrainData-2c3c6e44-eac9-45d8-9f80-3bc9b9d15f21$252Falgo-1-1670974596\nModuleNotFoundError: No module named 'text_sanitizer'\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4064#pullrequestreview-1216573659", "body": ""}
{"title": "Wire up histogram topic event handling", "number": 4065, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4065", "body": "Sorry for the code duplication"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4065#pullrequestreview-1216600273", "body": "Jesus, you're fast."}
{"title": "Fix pre-processing", "number": 4066, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4066"}
{"title": "Kitchen sink", "number": 4067, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4067", "body": "Throw Slack + Folders + Notion at Bertopic. No pipeline or app changes."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4067#pullrequestreview-1218442392", "body": ""}
{"title": "[BREAKS API ON MAIN] Remove unnecessary message id from request model", "number": 4068, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4068"}
{"comment": {"body": "Crud - the ID is needed because the `CreateMessageRequestV3` model is used in the `CreateThreadRequestV3` model. The CreateMessage APIs should use the path id and ignore the body id", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4068#issuecomment-1350254654"}}
{"title": "[BREAKS API ON MAIN] - Add draftId to CreateUpdateVideoMetadata", "number": 4069, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4069", "body": "There is no explicit binding between a draft and video metadata. The service will check that the assets between the metadata and the draft match before executing the delete operation"}
{"title": "Introduce Git service primitives to support SourceMark calculator", "number": 407, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/407", "body": "Initial utils:\n- check if git repo is shallow (we cannot support shallow repos, this is a user error)\n- get the canonical git repo root (takes account of symlinks, relative paths, etc)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/407#pullrequestreview-*********", "body": ""}
{"comment": {"body": "fine for now. this class is an example of something that is platform dependent, so a use case for KT multi-platform ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/407#discussion_r814186777"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/407#pullrequestreview-*********", "body": ""}
{"title": "Batch inserts into smaller transactions to avoid deadlocks", "number": 4070, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4070"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4070#pullrequestreview-**********", "body": ""}
{"title": "Count words in titles and slack bodies", "number": 4071, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4071"}
{"title": "Implement video metadata changes", "number": 4072, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072", "body": "Implements the backend changes necessary for clients to adopt the MessageV3 API. Notably:\n1. Separate Create/Update and Get request/response objects\n2. Sending draftId to create requests triggers transactional delete of video drafts on creation of new threads and messages"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072#pullrequestreview-1218089759", "body": ""}
{"comment": {"body": "This could be done more efficiently but 99.999% of the time it will be a single videoMetadata object that requires draft cleanup", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072#discussion_r1048871522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072#pullrequestreview-1218255260", "body": ""}
{"comment": {"body": "You can just remove this line, and call `SchemaManager.dropColumn()` from an admin-web migration once deployed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072#discussion_r1048979109"}}
{"comment": {"body": "almost all copy paste \ud83d\ude2c ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072#discussion_r1048986790"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072#pullrequestreview-1218278017", "body": ""}
{"comment": {"body": "Poorly repeated pattern. I'll DRY it up", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072#discussion_r1048992726"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072#pullrequestreview-1218333319", "body": ""}
{"comment": {"body": "Actually I'll do this in a separate PR. Will make the migration smoother while old instances are rolling over", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4072#discussion_r1049029580"}}
{"title": "Update support for new VideoMetadata models", "number": 4073, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4073", "body": "Added support for new VideoMetadata properties.\nDuration + PreviewImage."}
{"comment": {"body": "I think this has to be merged first: https://github.com/NextChapterSoftware/unblocked/pull/4072 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4073#issuecomment-1352087881"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4073#pullrequestreview-1218027715", "body": ""}
{"comment": {"body": "These values are expected to be required...\r\nOptionality is due to how protobuf encodes dates. `google.protobuf.Timestamp`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4073#discussion_r1048828623"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4073#pullrequestreview-1218363481", "body": ""}
{"title": "Lemmatize words in sanitizer phase", "number": 4074, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4074", "body": "Also removing the stem_words function because I don't think we want that (though maybe we do?). \nStemming is useful for full text search but does not produce natural language words. We'd have to convert stems back to the word form, but lemmatization I think covers this:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4074#pullrequestreview-1218006738", "body": ""}
{"comment": {"body": "FYI, the function is only lemmatizing verbs. The \"v\" option only does verb lemmatization.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4074#discussion_r1048813768"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4074#pullrequestreview-1218007988", "body": ""}
{"comment": {"body": "Gotcha. I think we want to do it for nouns too, unless you disagree?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4074#discussion_r1048814630"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4074#pullrequestreview-1252601132", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/a81b0998-63f9-4e65-9769-06e57ad32054)\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4074#discussion_r1072930655"}}
{"title": "Use real createdAt time", "number": 4075, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4075"}
{"title": "Lint fix", "number": 4076, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4076"}
{"title": "queue based system for folder data to s3", "number": 4077, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4077", "body": "Api service should not readily have access to pushing to s3 buckets.\nWe are using event based system to flush to s3."}
{"title": "Add topics impleemntation", "number": 4078, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4078"}
{"title": "Fix slack", "number": 4079, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4079"}
{"title": "Fix Client Refresh", "number": 408, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/408", "body": "Refresh token expiration date was not properly parsed leading to refreshing with an expired refresh token.\nThis caused infinite recursion on client.\nUpdated parsing and date comparison of tokens.\nRenamed signout to logout to be consistent."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/408#pullrequestreview-892884974", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/408#pullrequestreview-892886249", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/408#pullrequestreview-893910766", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/408#pullrequestreview-893964617", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/408#pullrequestreview-893965410", "body": ""}
{"title": "VSCode sends folder updates to service every now and then", "number": 4080, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4080", "body": "This is very simplistic -- on login (or when the repo set changes), periodically we will check to see if nobody has uploaded folder lists in the last 20 hours.  If nobody has, we will generate, encode, and upload the folders."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4080#pullrequestreview-1218297918", "body": ""}
{"comment": {"body": "The changes in this file are to change the RepoStore stream from a `Stream<ValueCacheStreamState<ClientRepoAggregate[]>>` to a `Stream<LoadableDataStreamState<ClientRepoAggregate[]>>` -- the types are not terribly different but `LoadableDataStreamState` and `ValueStream` is what we're moving towards, and it has a number of useful operators, one of which I wanted to use.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4080#discussion_r1049006651"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4080#pullrequestreview-1218334818", "body": "Looks good to me."}
{"title": "More sack fixes", "number": 4081, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4081"}
{"title": "Make folder data in s3 proeprly formed", "number": 4082, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4082"}
{"title": "Add filter by experts to TopicView", "number": 4083, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083", "body": ""}
{"comment": {"body": "Add experts list to the righthand column:\r\n<img width=\"1320\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/207748907-1c562cae-5df1-4688-8efd-ae9e6492e980.png\">\r\n\r\n(is missing the (+) button from the designs; to be implemented as the topic CRUD APIs are added and built)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#issuecomment-1352430285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1218451530", "body": ""}
{"comment": {"body": "Need to handle case where allExperts is also empty?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1049113580"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1218453140", "body": ""}
{"comment": {"body": "What is this key used for? Doesn't seem to be used for styling or logic?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1049114786"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1218458101", "body": ""}
{"comment": {"body": "It's for the react rendering; the component doesn't know to rerender itself on state change otherwise", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1049118497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1218458645", "body": ""}
{"comment": {"body": "I don't think so. if it's empty then the find() will return undefined and the fn will fallback to the generic string", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1049118908"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1219727724", "body": ""}
{"comment": {"body": "You should be able to just use `key={expert.id}` as long as the expert ID is unique.  The selection state is already passed in as another parameter.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1049989788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1219729447", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1219737907", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1219738065", "body": ""}
{"comment": {"body": "It's hard to explain but without the key, the checkbox click doesn't rerender properly when the checked state changes:\r\n![CleanShot 2022-12-15 at 10 18 48](https://user-images.githubusercontent.com/********/*********-4c667dc1-7f94-4c37-ac06-bf4bf4dc508e.gif)\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1049997143"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Yep I understand the need for the key, I think the key can just be the `expert.id` though, I don't think it needs to take the selection state into account.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1049998188"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Is the idea that eventually we're going to use the new search API for this, so we won't be doing this filtering client-side?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1049999141"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-**********", "body": ""}
{"comment": {"body": "It doesn't seem to work with just the expert id :/ I see the same behaviour in the gif above", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1049999832"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Yep that's right. the insights will be sent back from the service", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1050006333"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1219753819", "body": ""}
{"comment": {"body": "That's very confusing, we shouldn't even really need the key here at all.  Something else is going on that is breaking this.  I guess we leave this here for now and try to figure it out at some point if it causes bugs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1050007430"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1219756055", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#pullrequestreview-1219758059", "body": ""}
{"comment": {"body": "Test image \n\n![](https://dev.getunblocked.com/assets/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/a17bb956-76a5-4b48-9b24-f560b1bad06d)\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4083#discussion_r1050010276"}}
{"title": "Notify Unblocked Slack when new walkthroughs are created", "number": 4084, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4084", "body": "AVADAKADABRA!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4084#pullrequestreview-1218524248", "body": ""}
{"title": "Generate Walkthrough preview images", "number": 4085, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085", "body": "Generates Preview/Poster images for walkthroughs.\nThese will be used to hopefully help with the blank initial frames in Safari.\nWill also reduce data costs on list pages where we can load just the preview image instead of the video."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#pullrequestreview-1218501950", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#pullrequestreview-1219638456", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#pullrequestreview-1219695561", "body": ""}
{"comment": {"body": "Nested try/catch like this is pretty confusing, should we move this into two separate try/catch steps instead of nesting it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#discussion_r1049969256"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#pullrequestreview-1220149089", "body": ""}
{"comment": {"body": "Curious, why does this go through the `tiffRepresentation`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#discussion_r1050277008"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#pullrequestreview-1221144136", "body": ""}
{"comment": {"body": "From my digging, this was how generating a jpeg or png is done? You start from a tiff and convert.\r\n ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#discussion_r1050951736"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#pullrequestreview-1221175657", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#pullrequestreview-1221177346", "body": ""}
{"comment": {"body": "No longer nested.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4085#discussion_r1050972116"}}
{"title": "Add folder data to pipelines", "number": 4086, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4086"}
{"title": "Search V2", "number": 4087, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4087", "body": "Creating the models to support Search V2. Next PRs will wire up indexing and implement the searchInsights API operation."}
{"title": "Enable folder filtering for bert and histogram", "number": 4088, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4088", "body": "AddHistgoramFilter\nUpdate"}
{"title": "update documentation", "number": 4089, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4089"}
{"title": "Commit hash should be merge commit sha", "number": 409, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409", "body": "Going to immediately follow up this PR to clean up this hacky approach, but wanted to get this in so that PR ingestion uses the merge commit SHA when creating SourcePoints to unblock richie"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#pullrequestreview-892891594", "body": ""}
{"comment": {"body": "This is the super hacky part, I'll clean up in post", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#discussion_r814193724"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#pullrequestreview-892897604", "body": "thanks. minor comments"}
{"comment": {"body": "presumably the merge commit sha is only available on V3? otherwise, might be more efficient to run a targeted V4 query instead since we're only fetching a few properties.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#discussion_r814200102"}}
{"comment": {"body": "typo: response\r\n```suggestion\r\ndata class GitHubPullResponse(\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#discussion_r814201203"}}
{"comment": {"body": "heads up: I suspect this is optional. we know it's optional at least for OPEN PRs. may also be optional for the \"Rebase and merge\" strategy. but happy to wait and fix only when we need to.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#discussion_r814201347"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#pullrequestreview-892908039", "body": ""}
{"comment": {"body": "Yep, V4 doesn't allow getting the merge commit for a pull request. To be more efficient, it's better that we just paginate pull requests from V3 and drop V4 altogether (as we're doing right now)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#discussion_r814205606"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#pullrequestreview-892910840", "body": ""}
{"comment": {"body": "oh, nvm \ud83e\udd26 \r\n\r\nI saw \"request\", but its a pull-request.\r\n\r\nignore me, I'm stupid", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/409#discussion_r814207388"}}
{"title": "Improve folder parsing", "number": 4090, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4090"}
{"title": "Map insights to topics from all sources (including BERT and histogram)", "number": 4091, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4091"}
{"title": "Optimize state machine executions", "number": 4092, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4092"}
{"title": "ReduceQuery", "number": 4093, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4093"}
{"title": "Increase timeouts and fix inflect code", "number": 4094, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4094"}
{"title": "TryToSpeedUpLemmatization", "number": 4095, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4095", "body": "Fix code\nUpdate\nTry to speed up lemmatization"}
{"title": "Try to speed up lemmatization", "number": 4096, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4096"}
{"title": "optimize sanitization", "number": 4097, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4097"}
{"title": "Parallelize sanitization", "number": 4098, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4098"}
{"title": "Remotely enable sourcemark debugging on customer accounts", "number": 4099, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4099"}
{"title": "Change Service Service to Identity Service", "number": 41, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/41"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/41#pullrequestreview-*********", "body": ""}
{"title": "Refactor GitHub API client logic", "number": 410, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/410", "body": "No logic changes, just cleanup from https://github.com/NextChapterSoftware/unblocked/pull/409"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/410#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/410#pullrequestreview-*********", "body": ""}
{"title": "GitHub App manifest for On-premise (GitHub Enterprise Server) [BREAKS API ON MAIN]", "number": 4100, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1224896937", "body": ""}
{"comment": {"body": "Breaking API, but not really. Dave's going to break it anyway.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053585571"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1224898033", "body": ""}
{"comment": {"body": "Will be done in follow up changes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053586299"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1224915446", "body": ""}
{"comment": {"body": "Days since last break: 0\r\nBreak interval in days: 0", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053593533"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1224916443", "body": ""}
{"comment": {"body": "\ud83e\udd23", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053593894"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1224925416", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1224929001", "body": ""}
{"comment": {"body": "Curious, why is this breaking API compatibility?  I don't see the break in the API spec", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053603596"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1224929503", "body": ""}
{"comment": {"body": "Provide enum gets another value", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053604128"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1224930169", "body": ""}
{"comment": {"body": "I thought we didn't consider that a breaking change?  All the clients should handle new enum values gracefully?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053604885"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1224936074", "body": ""}
{"comment": {"body": "I can see both arguments. It's technically a breaking change from a spec perspective (OpenAPI diff's perspective); but as long as all clients handle unknown values then it's fine in practice.\n\n\n\nThe APICompatTest is based on OpenAPI diff library, so we'd have to write our own or extend it to treat this as a non-breaking change.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053611294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I think the Swift decoder might blow up if it receives an unknown enum case. Breaks responses.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053612350"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-**********", "body": ""}
{"comment": {"body": "``` swift\r\npublic enum Provider: String, Codable, CaseIterable, CaseIterableDefaultsLast {\r\n    case github = \"github\"\r\n    case bitbucket = \"bitbucket\"\r\n    case slack = \"slack\"\r\n    case unknownDefaultOpenApi = \"unknown_default_open_api\"\r\n}\r\n```\r\n\r\nIt's going to try to resolve that using `Codable`, which will bomb", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053616095"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-**********", "body": ""}
{"comment": {"body": "No, unknown values will get mapped to `unknownDefaultOpenApi` --- we figured this out both for TS and Swift, exactly for this kind of scenario...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053632463"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-**********", "body": ""}
{"comment": {"body": "(For swift the `CaseIterableDefaultsLast` protocol does this magic)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053633016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#pullrequestreview-1225379243", "body": ""}
{"comment": {"body": "Ah missed that! I feel like this is one of the biggest downsides to protocol adoption with default implementations. Opaque behaviours that are not visible until you find that magic extension", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4100#discussion_r1053923129"}}
{"title": "Chunk inserts", "number": 4101, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4101"}
{"title": "Fix read-only view syntax hilighting", "number": 4102, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4102", "body": "I missed this UI when we switched the hilighter over to be entirely file-extension-based."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4102#pullrequestreview-1219784126", "body": ""}
{"title": "Fix crash bug", "number": 4103, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4103", "body": "Fixes a where navigating directly to a thread caused a runtime exception.\nWas due to prevRoute not existing if navigating directly to a page using a url."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4103#pullrequestreview-1219811771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4103#pullrequestreview-1219812360", "body": "Kinda wondering if as should be a linter error.  It almost always gets you into trouble."}
{"title": "Wire up Search V2 indexing", "number": 4104, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4104"}
{"title": "Optimizations to glue code with dsl updates with powerml", "number": 4105, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4105", "body": "PowerML changes.\nGlue ETL can now handle no folders for customer.\nClean up stuff"}
{"title": "Fix explorer insights bug that caused spinner", "number": 4106, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4106", "body": "Mistake on my part, we didn't handle git failures here correctly.  The end result is that if you clicked on a file that hasn't been added to the git repository, the stream would go into the error state and the insights panel would go into the 'loading' state forever."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4106#pullrequestreview-1219945314", "body": ""}
{"comment": {"body": "Also note changing the default view to 'View scoped' @benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4106#discussion_r1050134900"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4106#pullrequestreview-1219948558", "body": ""}
{"title": "Add ability to delete file references", "number": 4107, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4107", "body": "Update RelatedFilesSection to delete file references along web file references"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4107#pullrequestreview-1220135229", "body": ""}
{"title": "Add 'No insights in view area' UI to VSCode explorer insights panel", "number": 4108, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4108", "body": "Add a UI for when there are insights in the file, but not in the part of the view you are looking at, when 'Visible range' is enabled.\n"}
{"title": "document ghe instance", "number": 4109, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4109", "body": "Added readme with info about our GHE deployment\nAdded the modify CloudFormation template used to deploy this service \nAdded code used for turning it on/off using a hacky lambda function"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4109#pullrequestreview-1220003240", "body": ""}
{"title": "Git Diff and Friends", "number": 411, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/411", "body": "add git diff parser: fundamental to SourceMark tracking\nand utils like: headCommitSha, remoteCloneUrl, rootCommit which will be used in follow up PRs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/411#pullrequestreview-893163102", "body": ""}
{"title": "Add API error handler in FolderUploader", "number": 4110, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4110", "body": "Oops, I had this code sitting locally and didn't push it to the PR for this feature."}
{"title": "optimize histogram", "number": 4111, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4111"}
{"title": "Fix prod activemq", "number": 4112, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4112"}
{"title": "Increase glue worker count", "number": 4113, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4113"}
{"title": "Bold the topic name", "number": 4114, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4114", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4114#pullrequestreview-1220044138", "body": ""}
{"title": "Enable new VSCode Explorer insights panel globally", "number": 4115, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4115"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4115#pullrequestreview-1220103760", "body": ""}
{"title": "Search V2 indexing fixes", "number": 4116, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4116"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4116#pullrequestreview-1220067236", "body": ""}
{"title": "Ignore pending reviews", "number": 4117, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4117", "body": "Pending reviews don't have a submittedAt, so this PR fixes:\nuse:kotlinx.serialization.json.internal.JsonDecodingException: Unexpected JSON token at offset 1077: Expected string literal but 'null' literal was found at path: $.review.submitted_at Use 'coerceInputValues = true' in 'Json {}` builder to coerce nulls to default values. JSON input: .....032b0cad0b49e\",\"submitted_at\":null,\"state\":\"pending\",\"html_u....."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4117#pullrequestreview-1220096977", "body": ""}
{"title": "Fix text vibrancy issues in onboarding panels on Ventura", "number": 4118, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4118"}
{"title": "Implement searchInsights operation", "number": 4119, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4119"}
{"title": "Add ThreadType to Thread model to support Notes", "number": 412, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412"}
{"comment": {"body": "Data model\r\nhttps://www.notion.so/nextchaptersoftware/Data-Model-b9c282a52230418d9d2235d933954768\r\n\r\n<img width=\"1257\" alt=\"Screen Shot 2022-02-24 at 17 24 15\" src=\"https://user-images.githubusercontent.com/1798345/155635744-ac23887d-d37b-4293-9528-db55b4f3b747.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#issuecomment-1050424857"}}
{"comment": {"body": "I just talked to Ben.  Ultimately the only difference between Discussions (PR or otherwise) and Notes are:\r\n* Notes are private to one user (ie, one ThreadParticipant)\r\n* Notes, as designed now, can only have a single Message.  The idea is that editing a note is done by editing the single Message within the Note, as opposed to adding more Messages.  He did think there could be some use for multi-message Notes, but wasn't certain at this point.\r\n\r\nFor now I'd be in favour of adding a ThreadType property to Thread, and at runtime the service can prevent additional Messages from being added to a Thread with threadType==note.  If the workflows or data models start to diverge I think we can reassess at that point?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#issuecomment-1051396149"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-893178570", "body": "I don't think this is the right direction:\n1. a Message is a model shared by Notes and Threads; meaning that we don't need to reimplement most of the properties on another object\n2. a Thread is actually an abstraction that can be either a NoteThread or ChatThread\n3. SourceMarks always reference Message objects\nAttaching the model that I had in mind. We can chat tomorrow?\nSuggestions to model this is to:\n1. add a ThreadType enum {Chat, Note} to Thread model; OR\n2. create two objects that wrap Threads: {ChatThread, NoteThread} and refactor the Chat-specific and Note-specific properties into the new objects."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-895432649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-895583060", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-896918624", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-896959503", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-896984635", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-897820275", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-897820813", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-897823014", "body": ""}
{"comment": {"body": "Weird that the API could create a PR thread. I guess this will have to be prevented by a runtime check.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#discussion_r817866600"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-897931233", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-897936416", "body": ""}
{"comment": {"body": "would there be a use case here for when a user chooses to convert a Note to a PR Comment? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#discussion_r817945785"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-897940776", "body": ""}
{"comment": {"body": "I was wondering about that -- we could implement that as a series of separate PR thread creations (& note deletions) but that isn't transactional -- we could be left in an inconsistent state if some of those operations fail.  I don't know how much we should care about this for April.\r\n\r\nBen did mention that we might eventually have a workflow to add threads to an existing PR, which would probably need this as well.... nothing concrete right now though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#discussion_r817948016"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-898004966", "body": ""}
{"comment": {"body": "@richiebres I think there is another actual use case here. If the current branch has a PR opened on it, there are designs for adding a new PR comment through our UI.\r\n\r\ni.e. in the instance where a PR on the branch exists, when the user highlights a block of code, this is what they would see in the context menu:\r\n![image](https://user-images.githubusercontent.com/********/156426363-56e8574c-5f98-4b9c-85f5-04c636f1b989.png)\r\n\r\nSo we would need a way to post a PR comment in our API", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#discussion_r817991278"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-898024204", "body": ""}
{"comment": {"body": "Ah, forgot about `Add Pull Request Comment...` -- this works out well then.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#discussion_r818005037"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-898025128", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/412#pullrequestreview-898353169", "body": ""}
{"title": "Send Preview url back with the VideoMetadata API response", "number": 4120, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4120"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4120#pullrequestreview-1220150455", "body": ""}
{"title": "Include title in SearchInsightAuthor.searchVector", "number": 4121, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4121"}
{"title": "Add TopicExpertModel.sourceType to note the recommendation engine source", "number": 4122, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4122"}
{"title": "Add docs", "number": 4123, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4123"}
{"title": "Lint fix", "number": 4124, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4124"}
{"title": "Force the walkthrough app to close if the initializer is unresponsive", "number": 4125, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4125"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4125#pullrequestreview-1221413043", "body": ""}
{"comment": {"body": "Not the most elegant thing in the world but it will get the job done. We _must_ not leave this app hanging around because the user has no easy way to terminate it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4125#discussion_r1051126666"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4125#pullrequestreview-1221413560", "body": ""}
{"comment": {"body": "You have to do this because SwiftUI will crash otherwise", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4125#discussion_r1051126963"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4125#pullrequestreview-1221415529", "body": "Will test this out with VSCode..."}
{"title": "Fix topic mapping", "number": 4126, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4126"}
{"title": "Add web entry point to video drafts", "number": 4127, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4127", "body": "Adds entry point to video drafts.\nThe number is not fully responsive as there's no pusher channel. It will react to current user interaction but if one pushes a new draft while viewing the page, a refresh is necessary.\nRow will disappear if there are no drafts.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4127#pullrequestreview-1221430725", "body": ""}
{"title": "Use capability stream for DebugSourcemarkEngine", "number": 4128, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4128", "body": "Addresses race in https://github.com/NextChapterSoftware/unblocked/pull/4099. I did it wrong originally."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4128#pullrequestreview-1221477325", "body": ""}
{"comment": {"body": "This will effectively 'latch' the value when the GitRunner is constructed -- whether that's OK or not depends on whether the GitRunner is constructed before or after login.  A bit awkward...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4128#discussion_r1051169663"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4128#pullrequestreview-1221478394", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4128#pullrequestreview-1221479769", "body": ""}
{"comment": {"body": "Yeah, this one is ok, expected. I mostly care about the sourcemark engine's GitRunner, which is constructed after we have resolved an API repo to work. So should be fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4128#discussion_r1051171251"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4128#pullrequestreview-1221482724", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4128#pullrequestreview-1221494127", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4128#pullrequestreview-1221526317", "body": ""}
{"title": "Periodic pull request ingestion runs less frequently", "number": 4129, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4129", "body": "From every hour to every 12 hours.\nMotivation\n- \n- "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4129#pullrequestreview-1221517627", "body": ""}
{"title": "Run PR ingestion in DEV", "number": 413, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/413", "body": "Also adds logic to skip ingestion if the repo already has SourceMarks. This is a quick and dirty way to prevent continually ingesting PR comments every time the application loads. \nIf you wish to re-ingest, just drop your database or run\nDELETE FROM sourcemarkmodel where repo='fb06ca32-cdf0-4bcc-9e08-c8c675768235'\nUltimately we want to make ingestion idempotent and be able to pick up where it left off if it's interrupted mid-ingestion. We'll do that later."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/413#pullrequestreview-893359188", "body": ""}
{"title": "[BREAKS API ON MAIN] Update searchInsights response shape", "number": 4130, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4130", "body": "To allow the server to return the result counts"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4130#pullrequestreview-1225239857", "body": ""}
{"title": "Delete VideoDraft on thread creation", "number": 4131, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4131", "body": "Need to pass draftID when creating a thread from a Video Draft.\nAlso notifies our VideoDraftStore to refresh after thread creation."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4131#pullrequestreview-1221536272", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4131#pullrequestreview-1221536627", "body": ""}
{"title": "Limit topics returned from the API to 50", "number": 4132, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4132"}
{"title": "Dont limit the number topics used for mapping insights", "number": 4133, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4133"}
{"title": "Delete video in VSCode", "number": 4134, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4134", "body": "Ability to delete threads at the message context level.\nThis is currently necessary for VSCode as standard thread operations are done in the header which is tightly coupled with the CodeBlock. This CodeBlock is not rendered for video walkthroughs :)\n"}
{"comment": {"body": "Passing in a thread operations to the first messageView.\r\n\r\nOpen to suggestions on alternatives... I considered using a context as well but seems a bit overkill.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4134#issuecomment-1355746171"}}
{"comment": {"body": "> This is currently necessary for VSCode as standard thread operations are done in the header which is tightly coupled with the CodeBlock. \r\n\r\nAh hmm this won't be the case with this work in https://github.com/NextChapterSoftware/unblocked/pull/4140 -- so maybe hold off on merging this one?? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4134#issuecomment-1359896518"}}
{"comment": {"body": "No need for this PR anymore with #4140 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4134#issuecomment-1360235759"}}
{"title": "updated lambda and readme", "number": 4135, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4135", "body": "Updated the readme with email setup info and automatic shutdown stuff."}
{"title": "Ignore unexpected repo file event during hub video walkthrough", "number": 4136, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4136", "body": "VSCode was sending a file reference event on walkthrough starting even if it's not in focus.\nThis was causing unexpected initial repo file ref events showing when starting a video walkthrough from the hub."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4136#pullrequestreview-1221899699", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4136#pullrequestreview-1224928707", "body": ""}
{"comment": {"body": "Since the walkthrough app is an accessory app, do we need to call `hide()`?\r\n\r\nAlso I think `NSApp` is essentially an alias for `NSApplication.shared`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4136#discussion_r1053603301"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4136#pullrequestreview-1224930256", "body": ""}
{"title": "Upsert topic experts periodically", "number": 4137, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4137"}
{"title": "Limit topics per file in VSCode", "number": 4138, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4138", "body": "Limit the topics for a file to the top 3 topics that have at least 50% of the score of the top topic."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4138#pullrequestreview-1221620631", "body": ""}
{"title": "Clean up InsightsApiDelegateImpl and SearchInsightService", "number": 4139, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4139"}
{"title": "Add Sidebar Panes for VSCode Extension", "number": 414, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/414", "body": "This pr does the following:\n1. Adds styling for TreeView for VScode.\n2. Removed TreeNode and we are now using a flattened TreeViewState for webview and extension.\n3. Add specialized TreeDataProviders for each of the specific panes we have in sidebar. Somewhat mimics how vscode data providers work.\n4. Adds sidebar logic and populating with dummy data.\nTODO:\n1. Fine tune how state management works.\n2. Add event handlers for a bunch of things.\n3. Figure out refresh logic.\n4. Output live data.\n"}
{"comment": {"body": "I'll give this until Monday evening and submitting before I leave. Let me know if anything here is too gnarly Matt. :)\r\nStill a lot of work to do admittedly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/414#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/414#pullrequestreview-894340693", "body": ""}
{"comment": {"body": "Moving away from using a recursive tree structure in VSCode and a falttened tree in Webview.\r\nNow just using flattened tree across everything.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/414#discussion_r815237520"}}
{"title": "Add unified headers for vscode insights", "number": 4140, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4140", "body": "Add shared InsightHeader component for all vscode thread views. It will render a header, a subheader (includes topics and any other optional metadata) and the menu component in the same format.\nthreads:\n\n\n\nPRs:\n"}
{"comment": {"body": "Nice! I checked out your branch to give this a try. Looks great. One minor thing, but the animation of the topics expanding feels a touch off. I think it is because the text to the right doesn't animate with the topics. Would it be possible to animate that at the same time?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4140#issuecomment-1358352218"}}
{"comment": {"body": "One other thing I noticed @matthewjamesadam @jeffrey-ng is that when clicking on the video row, the source code tries to open along side the video, even though the file is already open. Here's a video of it.\r\n\r\nhttps://user-images.githubusercontent.com/13353189/208523704-0dd2a2cc-5632-4c66-8e67-5bf5052cb46a.mp4\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4140#issuecomment-1358355253"}}
{"comment": {"body": "@kaych would it be possible to add a slight delay to the hover state as well? It feels a touch jumpy and I think a delay might help smooth it out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4140#issuecomment-1358404494"}}
{"comment": {"body": "> One other thing I noticed @matthewjamesadam @jeffrey-ng is that when clicking on the video row, the source code tries to open along side the video, even though the file is already open. Here's a video of it.\r\n> \r\n>  CleanShot.2022-12-19.at.12.55.02.2.mp4\r\n\r\nTo follow up on this; this is a known/fixed bug -- the video thread itself was old and thus had corrupted data, but this should be fixed for new video threads.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4140#issuecomment-1358519552"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4140#pullrequestreview-1224981072", "body": ""}
{"title": "Conditional logout on refresh auth", "number": 4141, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4141", "body": "Some members on the team were noticing issues with random logouts on client.\nBased on Matt's logs, it looks like errors were being thrown on RefreshAuth which was causing logout operation.\nUpdated logic to only logout on 401 or other expected unauthed situations."}
{"comment": {"body": "Updated tests on the AuthStore. Validates error case that will *not* trigger logout.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4141#issuecomment-1358381417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4141#pullrequestreview-1223426519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4141#pullrequestreview-1223428102", "body": " \nTests?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4141#pullrequestreview-1223584332", "body": ""}
{"comment": {"body": "Moved CustomError to its own shared/error directory due to cyclical reference issues with putting it in WebUtils...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4141#discussion_r1052671702"}}
{"title": "reduce kube logging", "number": 4142, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4142", "body": "We were sending 2GB of logs from Kube to CloudWatch because of audit logs. This dripped it back to 100MB/day"}
{"title": "Thread video preview container", "number": 4143, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4143", "body": "Utilize Video Preview Image whenever possible in thread listing.\n"}
{"comment": {"body": "Yup.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4143#issuecomment-1360059083"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4143#pullrequestreview-1224933643", "body": ""}
{"comment": {"body": "if null, should we return `<MessageContentBody readOnly videoOnly messageContent={message.messageContent} />` as a fallback??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4143#discussion_r1053608497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4143#pullrequestreview-1224934347", "body": "Thanks!\nWhen you click on the image, does the click event just propagate and the action opens the thread?"}
{"title": "Disable topic mapping job", "number": 4144, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4144"}
{"title": "Do topic mappings during topic creation/ingestion", "number": 4145, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4145"}
{"title": "Add API methods for topic maintenance", "number": 4146, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146", "body": "Add query property to getTopics API that allows fetching only \"recommended\" and \"relevant\" topics.  \"relevant\" topics are ones that are user-approved, and are the topics we generally show throughout the UI.  \"recommended\" topics are ones that are not marked as relevant, and are only used as recommendations in this UI:\n\n\n\nAdd new updateTopic API method, used to add a new topic or update an existing topic.  This will be used to add new topics (whether those topics are \"recommended\" topics generated by the system, or whether they are brand new topics):\n\n\n\nThis doesn't handle the training UI.  I'm leaving that for now."}
{"comment": {"body": "Extracted the topic type to a separate enum and clarified that an undefined query means all topics.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#issuecomment-1358577140"}}
{"comment": {"body": "FYI also changed the TS clients so they fetch relevant topics.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#issuecomment-1358599004"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223631018", "body": ""}
{"comment": {"body": "Is it worth adding something to the `Topic` model that indicates its system recommended and has not been \"approved\" by a user, or is that inferred by the `relevant` property?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#discussion_r1052703713"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223631902", "body": ""}
{"comment": {"body": "The client will be fetching \"approved\" and \"recommended\" topics in two different calls (using the new query parameter), so it will always understand the difference...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#discussion_r1052705020"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223643157", "body": ""}
{"comment": {"body": "What happens when this param is not passed in? Does it default to all? Should we just remove that from the enum type? As in, you either pass in this query param (`relevant` | `recommended`) or you get `all` back", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#discussion_r1052713498"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223652657", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223655396", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223659529", "body": ""}
{"comment": {"body": "If we want to be backwards-compatible it should probably return `relevant`.  I'm open to alternative opinions though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#discussion_r1052724943"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223660412", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223661717", "body": ""}
{"comment": {"body": "Do we have enough users on the current topics experience that we should worry too much about backwards compatibility at this stage? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#discussion_r1052726539"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223661908", "body": ""}
{"comment": {"body": "Probably not.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#discussion_r1052726675"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223666215", "body": "lgtm"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1223668500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1224923356", "body": ""}
{"comment": {"body": "@matthewjamesadam Ah sorry, I didn't catch this: this shouldn't be an array, yeah?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#discussion_r1053598012"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#pullrequestreview-1224930460", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/4149", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4146#discussion_r1053605214"}}
{"title": "Video drafts are available in read-only mode", "number": 4147, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4147"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4147#pullrequestreview-1223668683", "body": ""}
{"title": "Replace ThreadTopicModel and PullRequestTopicModel with TopicInsightModel", "number": 4148, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4148"}
{"title": "[BREAKS API ON MAIN] updateTopic Request body should just be a single object", "number": 4149, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4149"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4149#pullrequestreview-1224929457", "body": ""}
{"title": "add more perms to iam user for agora", "number": 415, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/415", "body": "Ran the npm run fix-pretty and it made the file messy. Here are the changes I have made:\n- Added multi-part upload perms to IAM user \n- Added a couple of flags to completely disable public hosting and public objects in the bucket\nThis is already deployed and to Dev to help with Demo stuff"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/415#pullrequestreview-894139187", "body": ""}
{"title": "Add API for delete all Video Drafts", "number": 4150, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4150"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4150#pullrequestreview-1225002337", "body": ""}
{"title": "Video reliability improvements", "number": 4151, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#pullrequestreview-1225035882", "body": ""}
{"comment": {"body": "I looked into falling back to old-school delegate style management for finer grained controls, but it didn't get us anything. \r\n\r\nIf we support continuation there are basically 2 ways to achieve this:\r\n- Split requests into chunks and continue from failed multi-part requests\r\n- Figure out how to do random access uploads with S3 (don't think that's possible), and then fall back to delegate style to record successful bytes and mark continuation points.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#discussion_r1053681164"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#pullrequestreview-1225036758", "body": ""}
{"comment": {"body": "Coming in part 2", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#discussion_r1053681718"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#pullrequestreview-1225037867", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#pullrequestreview-1225038094", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#pullrequestreview-1225268184", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#pullrequestreview-1226527377", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#pullrequestreview-1226528136", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4151#pullrequestreview-1226586290", "body": ""}
{"title": "Refactor TopicStore to allow querying recommended topics", "number": 4152, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4152", "body": "This is just a pure refactor:\n\nChange the key for TopicStore to allow querying for relevant or recommended topics\nChange all existing uses of TopicStore to get the stream of relevant topics (the \"current user approved\" topics)\n\nAlso, fixed the dashboard storybook"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4152#pullrequestreview-1225291852", "body": ""}
{"title": "Persist GitHub Enterprise apps", "number": 4153, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4153"}
{"title": "Add pull request count to repo page", "number": 4154, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4154"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4154#pullrequestreview-1225154068", "body": "thanks"}
{"title": "Sort topics descending by score", "number": 4155, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4155"}
{"title": "Reduce Slack polling by 60X", "number": 4156, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4156", "body": "Motivation\n- \nFeel free to revert this change."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4156#pullrequestreview-1225202844", "body": ""}
{"title": "Delete all video drafts", "number": 4157, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4157", "body": "\n"}
{"comment": {"body": "@jeffrey-ng are you able to use the icon buttons for the individual cards?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4157#issuecomment-1360400787"}}
{"comment": {"body": "@jeffrey-ng For the confirmation dialogue, can we go with:\r\n\r\nTitle: Are you sure you want to delete all video drafts?\r\nSub-text: This action cannot be undone.\r\nButtons: Cancel / Delete All", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4157#issuecomment-1360410950"}}
{"comment": {"body": "<img width=\"1049\" alt=\"CleanShot 2022-12-20 at 15 28 57@2x\" src=\"https://user-images.githubusercontent.com/1553313/208785687-e0343506-fc69-497e-8e6c-b05251a4b1a5.png\">\r\n<img width=\"985\" alt=\"CleanShot 2022-12-20 at 15 28 54@2x\" src=\"https://user-images.githubusercontent.com/1553313/208785701-3c1fd9bf-8e3d-4352-8edf-ceb570c75479.png\">\r\n\r\nUpdated styles\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4157#issuecomment-1360463986"}}
{"comment": {"body": "@benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4157#issuecomment-1360464209"}}
{"comment": {"body": "Nice - let's vertically-aligned the icon with the text and merge.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4157#issuecomment-1360501733"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4157#pullrequestreview-1225288499", "body": ""}
{"title": "[DO NOT MERGE] Add message body to SearchInsightsResponse", "number": 4158, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4158"}
{"title": "Test Entry point for manifest", "number": 4159, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4159", "body": "Test flow to make post request to GHE instance.\nSends a post with form as tested here: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4159#pullrequestreview-1225235938", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4159#pullrequestreview-1225237204", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4159#pullrequestreview-1225239964", "body": ""}
{"comment": {"body": "Is this route where create app should redirect you to? If so, then can you update the `appCreateRedirectUrl` value in global.conf and prod.conf\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4159#discussion_r1053821330"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4159#pullrequestreview-1225240647", "body": ""}
{"comment": {"body": "- https://github.com/NextChapterSoftware/unblocked/blob/10058fd3995c644c47982e06c25f9517088f50a2/projects/libs/lib-config/src/main/resources/config/global.conf#L24\r\n\r\n- https://github.com/NextChapterSoftware/unblocked/blob/10058fd3995c644c47982e06c25f9517088f50a2/projects/libs/lib-config/src/main/resources/config/prod.conf#L27", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4159#discussion_r1053821833"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4159#pullrequestreview-1225241849", "body": "looks good, pending config changes"}
{"title": "Enable loading runtime fixtures in dev", "number": 416, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/416"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/416#pullrequestreview-894091705", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/416#pullrequestreview-894141216", "body": ""}
{"title": "Add MobileMenu component for dashboard", "number": 4160, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160", "body": "Add generic mobile menu component (has a side animation for now but we can customize when more is necessary)\n\nAdd filter menu for the mobile viewport that replaces the filter dropdowns in the larger viewports\nAdd a couple of generic header and row components for reuse"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#pullrequestreview-1241366474", "body": ""}
{"comment": {"body": "Not sure we should be adding `& HTMLAttributes<HTMLDivElement>` here, as it makes it seem like you can pass any `div` props into a `ContextRow`.  Probably best just to add className to the props explicitly?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#discussion_r1065221240"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#pullrequestreview-1241370316", "body": ""}
{"comment": {"body": "I guess I'm wondering the inverse; re: are there any div props that this component shouldn't be able to accept? It is just a div afterall", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#discussion_r1065224057"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#pullrequestreview-1241370799", "body": ""}
{"comment": {"body": "Yeah if you want to just allow all div props that's fine too, but we should actually pass them through to the rendered div so the props take effect...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#discussion_r1065224512"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#pullrequestreview-1241372263", "body": ""}
{"comment": {"body": "oops! \ud83d\ude06 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#discussion_r1065225539"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#pullrequestreview-1241375413", "body": ""}
{"comment": {"body": "Ah yes missed that. Updated.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#discussion_r1065227764"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4160#pullrequestreview-1241380055", "body": ""}
{"title": "[DO NOT MERGE] Revert \"Replace ThreadTopicModel and PullRequestTopicModel with Topic", "number": 4161, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4161", "body": "InsightModel (#4148)\"\nThis reverts commit 543e6df1beabf7dff8af99ac8601a105393e2e2c."}
{"title": "Add retry dialog for video upload", "number": 4162, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4162", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4162#pullrequestreview-1225458912", "body": ""}
{"title": "Drop ThreadTopicModel and PullRequestTopicModel tables", "number": 4163, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4163"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4163#pullrequestreview-1246385737", "body": "Thank you David for being David."}
{"title": "Unify references list", "number": 4164, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4164", "body": "Merge references list and move contributors list to the side.\n"}
{"title": "Unify References List", "number": 4165, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4165", "body": "Merge references list and move contributors list to the side.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4165#pullrequestreview-1228290092", "body": ""}
{"title": "Add ContextRow component", "number": 4166, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4166", "body": "Ben calls this a ContextRow.  It's basically a large block-display button with styling half way between a button and a link.  It's primarily used in the right-hand sidebar in the dashboard.\n\n\nContextRow can have an action (onClick) or not.  It has two variants, regular and deemphasized (looks somewhat faded out, until hovered).\nWe're currently using this in one place (the dashboard topic view, on the right side)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4166#pullrequestreview-**********", "body": ""}
{"title": "Enable GH Enterprise in dev", "number": 4167, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4167"}
{"comment": {"body": "Looks like there may be some more work... Missing Provider db models & etc...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4167#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4167#pullrequestreview-**********", "body": ""}
{"comment": {"body": "enable for local stack too.\r\n\r\n(might be cleaner to enable globally, and disable in prod.conf)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4167#discussion_r1054689390"}}
{"comment": {"body": "it's correct, but double negatives are hurting my brain \ud83e\udee0\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4167#discussion_r1054691383"}}
{"title": "Add brand icons to references", "number": 4168, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4168", "body": "Parsed through  for relevant brands.\n\nTODO: FileIcons next."}
{"comment": {"body": "Let me know if there are any others we should add.\r\n\r\nOr any custom icons that don't exist in FA. (e.g. notion)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4168#issuecomment-1361932031"}}
{"comment": {"body": "Updated:\r\n<img width=\"1083\" alt=\"CleanShot 2022-12-21 at 16 34 54@2x\" src=\"https://user-images.githubusercontent.com/1553313/209029746-a393fb0a-e8bc-418a-94d8-eaeabe14e755.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4168#issuecomment-1362255697"}}
{"comment": {"body": "Worked through some colour and spacing tweaks with Jeff.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4168#issuecomment-**********"}}
{"comment": {"body": "> Or any custom icons that don't exist in FA. (e.g. notion)\r\n\r\n@jeffrey-ng I have Linear and Notion svg icons we can use. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4168#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4168#pullrequestreview-**********", "body": ""}
{"title": "[WIP] Adds enterpise providers to login options", "number": 4169, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4169", "body": "Changes\n\nclient adds a new enterpriseHostAndPort query param\nserver adds enterprise provider \"OAuth\" Url to response\nurl is /login/{provider}/manifest, when enterprise provider app does not exist\nurl is /login/{provider}, when enterprise provider app does exist\n\n\n\nError Handing\nIf enterprise provider app does not exist, and the enterpriseHostAndPort is not network accessible then we need to encode that information in the response. There are two approaches: (i) use the default ApiError response, or (ii) add some custom errors to this API.\nUsing defaultApiError response\nThe downside is that these responses cannot be versioned; for example, the type field below cannot be an enum.\nFirewall\n{\n  \"type\": \"firewall\",\n  \"title\": \"Connection to your enterprise server timed out\",\n  \"status\": 400,\n  \"detail\": \"Unblocked cannot connect to your enterprise server. You may need to allow inbound https access.\",\n  \"instance\": \"\"\n}\nDNS\n{\n  \"type\": \"dns\",\n  \"title\": \"Your enterprise server was not found\",\n  \"status\": 400,\n  \"detail\": \"Unblocked cannot find your enterpise server. You may need to make your internal enterprise server DNS public.\",\n  \"instance\": \"\"\n}"}
{"comment": {"body": "See https://github.com/NextChapterSoftware/unblocked/pull/4185", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4169#issuecomment-1364371530"}}
{"title": "Having problems with prettier", "number": 417, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/417"}
{"title": "Send error level logs to the logging API", "number": 4170, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4170", "body": "There's no reliability here, just fire and forget. If this doesn't serve our needs then I will implement a reliable queue."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4170#pullrequestreview-1226740913", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4170#pullrequestreview-1226741318", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4170#pullrequestreview-1226749400", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4170#pullrequestreview-1226794393", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4170#pullrequestreview-1226819254", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4170#pullrequestreview-1228327747", "body": ""}
{"comment": {"body": "Choosing not to remotely log general request failures because it can result in significant pressure (and possible DDoS)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4170#discussion_r1055893345"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4170#pullrequestreview-1228339966", "body": ""}
{"title": "Disable start walkthrough if running in VSCode", "number": 4171, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4171", "body": "Starting a walkthrough should not be possible if already running.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4171#pullrequestreview-1228331316", "body": ""}
{"title": "Update getTopics operation and implement updateTopic operation", "number": 4172, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4172", "body": "Used for topic management."}
{"title": "Move pipeline from dev (unblocked only) to prod (all partners)", "number": 4173, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4173", "body": "while moving the scoring into something that we can share with powerml too. no changes to the data pipeline or the app, just the science-like notebooks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4173#pullrequestreview-1228407705", "body": "Ship it!"}
{"title": "Updated preview duration", "number": 4174, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4174", "body": "Refactored preview duration to left.\nAdded to thread listing as well.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4174#pullrequestreview-1228295780", "body": ""}
{"comment": {"body": "The vscode DurationOverlay.tsx file is empty -- it presumably needs to import this scss?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4174#discussion_r1055871303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4174#pullrequestreview-1228298144", "body": ""}
{"title": "Bring over some text processing functions from the data science pipeline", "number": 4175, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4175", "body": "1: improve the url removal.\n2: general emoji removal function.\n3: general username removal.\n4: remove tokenization. seems like Bert/PowerML can do casing, which we are going to test."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4175#pullrequestreview-1228255228", "body": ""}
{"comment": {"body": "Ah I don't think markdown processing will remove html", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4175#discussion_r1055844840"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4175#pullrequestreview-1228255297", "body": ""}
{"title": "Fixes slurp bugs", "number": 4176, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4176"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4176#pullrequestreview-1228324545", "body": ""}
{"comment": {"body": "Debugging tool that I don't want to re-write so leaving it in the code :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4176#discussion_r1055891072"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4176#pullrequestreview-1228339046", "body": ""}
{"title": "Fix deleted videos", "number": 4177, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177", "body": "Fixing a bug where Steve was missing video content.\nWas able to reproduce by editing the video's title. Editing the title === updating the message content. MessageEditor currently does not handle videos and drops them... (TO FIX)\nThis hacky PR will try inserting a video that exists within the video metadata back into the message content if missing.\nThis is technically okay right now since we don't support deleting videos without deleting entire threads..."}
{"comment": {"body": "Before:\r\n\r\n<img width=\"1232\" alt=\"CleanShot 2022-12-22 at 15 43 44@2x\" src=\"https://user-images.githubusercontent.com/1553313/209244312-bd247f27-b9e0-478a-8195-498074c9fffe.png\">\r\n\r\nAfter:\r\n<img width=\"1058\" alt=\"CleanShot 2022-12-22 at 15 43 11@2x\" src=\"https://user-images.githubusercontent.com/1553313/209244328-1c093a44-a230-4bf7-a0df-3e5ff1ecb915.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#issuecomment-1363445325"}}
{"comment": {"body": "For reference, Steve's video is edited according to admin console.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#issuecomment-1363446409"}}
{"comment": {"body": "Working in VSCode as well. (notice the edited tag)\r\n<img width=\"628\" alt=\"CleanShot 2022-12-22 at 15 53 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/209244616-8b7bcc73-d9e4-4935-98db-37644581f92e.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#issuecomment-1363446463"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#pullrequestreview-1228391685", "body": ""}
{"comment": {"body": "optimization - consider skipping this completely if videoMetadataIds is empty", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#discussion_r1055938043"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#pullrequestreview-1228393822", "body": ""}
{"comment": {"body": "Should be caught by this https://github.com/NextChapterSoftware/unblocked/blob/6fe08f9ae52c66a90c2bb90b2a6f51b156abbc88/shared/stores/ThreadInfoAggregate.ts#L23", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#discussion_r1055939766"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#pullrequestreview-1228394184", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#pullrequestreview-1228394663", "body": ""}
{"comment": {"body": "We can also skip below if `videosToInsert` is empty", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4177#discussion_r1055940434"}}
{"title": "Replace SearchService with SearchInsightService", "number": 4178, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4178", "body": "We're replacing SearchService with SearchInsightService for the search V2 experience."}
{"title": "Try to fix installer build", "number": 4179, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4179"}
{"comment": {"body": "This turned out to be an issue with jitpack.io (and a gradle bug preventing fallback to other repo hosts).\r\n\r\nClosing this and will create a new PR that removes our reliance on jitpack", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4179#issuecomment-1364146311"}}
{"title": "Add readme for databse", "number": 418, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/418"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/418#pullrequestreview-894143633", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/418#pullrequestreview-894172974", "body": ""}
{"title": "Add helper components", "number": 4180, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4180", "body": "Two changes I'm committing separately from the topic maintenance UI:\n1) Add a footer to the detail layout view, this will be used for the previous/next buttons in this design:\n\n2) Add a CardCheckbox component.  This is a stylized checkbox with a large panel area:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4180#pullrequestreview-1229241790", "body": ""}
{"comment": {"body": "@kaych I think you should double-check my work here when you're back.  I tried to keep the same layout you had before, where the header is sticky and the scroll area is the entire viewport in mobile (I'm guessing that was deliberate).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4180#discussion_r1056525888"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4180#pullrequestreview-1229273104", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4180#pullrequestreview-1229273263", "body": ""}
{"title": "Translate video element in message editor", "number": 4181, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4181", "body": "Update MessageEditor to properly handle Video Elements\nEditing a message with a video should no longer be completely destructive... \nUnable to show preview image at this time... If we wanted to do this, would require injecting video metadata into the MessageEditor which I'd like to avoid unless necessary.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4181#pullrequestreview-1242779831", "body": ""}
{"comment": {"body": "I think rendering the URL is a bit confusing -- maybe just render a FA video icon for now? https://fontawesome.com/search?q=video&o=r", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4181#discussion_r1066167625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4181#pullrequestreview-1242780878", "body": ""}
{"title": "Set correct topic source when created", "number": 4182, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4182"}
{"title": "NO", "number": 4183, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4183", "body": "Built the maven module from \nFork was required because it hasn't been touched since 2020"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4183#pullrequestreview-1229377630", "body": ""}
{"title": "Prod text sanitization changes", "number": 4184, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4184", "body": "Looks the changes that made things better in dev, made things worst for the usercloud team. This change removes the two new text processing rules we introduced yesterday and the start of a notebook to debug prod."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4184#pullrequestreview-**********", "body": ""}
{"title": "Introduce Enterpise Provider flow to drive enterprise login", "number": 4185, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4185", "body": "API Design\n\nExample errors\n\nendpointNotResolved\nDNS issue.\n\"Your enterprise server was not found\"\n\n\"Unblocked cannot find your enterpise server. You may need to make your internal enterprise server DNS public.\"\n\n\nendpointNotReachable\n\nFirewall issue.\n\"Connection to your enterprise server timed out\"\n\"Unblocked cannot connect to your enterprise server. You may need to allow inbound https access.\""}
{"title": "Improve text sanitizer and model config", "number": 4186, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4186", "body": "The intent of this was to roll back a change, but decided to just go all the way. Gets usercloud back to the previous state (which doesn't appear to be related to folders as this investigation demostrated) and improves everyone else."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4186#pullrequestreview-1230927883", "body": ""}
{"title": "Initial demo: Still refactoring this, but it works.", "number": 4187, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187", "body": "Need to remove the key before checking in, adopting whatever approach @pwerry is using for things like this.\nNeed to include the previous messages in the current prompt.\nNeed to refactor the OpenAI code into a client.swift file.\nFix whatever config option that is doubling the size of the input area.\nAdding our embeddings (the thing that is really interesting here?)"}
{"comment": {"body": "<img width=\"515\" alt=\"image\" src=\"https://user-images.githubusercontent.com/332509/210105865-b6b8e857-9713-4f62-96c2-d6ba380b70b7.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#issuecomment-1368063869"}}
{"comment": {"body": "On the API key thing, it seems like a step too far for a demo to bother creating an API proxy service. If we make use of OpenAI in any serious way we'll end up doing this anyway, so for now I would just YOLO that key into the client until we plan to put it in customer hands.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#issuecomment-1368567812"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233464250", "body": ""}
{"comment": {"body": "Is this type erasure a SwiftyChat requirement?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059804481"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233464312", "body": ""}
{"comment": {"body": "Not for now, but typically \"scroll to bottom on new message\" needs a predicate state of \"already at bottom of scrollview\". Otherwise the user might be reading messages further upstream and will get interrupted. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059804571"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233464502", "body": ""}
{"comment": {"body": "This might be better off moved to a ViewModel, where the view model class implements `ObservableObject` and has a `@Published` element for the array of messages. Then refer to the Observable with the `@Observed` keyword. This way it can be backed by a store for persistence and loaded via Combine, which will automagically interop with SwiftUI through the `@Observed` wrapper. \r\n\r\n`ThreadTabViewModel` demonstrates a slightly more sophisticated version of this pattern with a `LoadingState` wrapper to show something while the data is being loaded from disk or off the network.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059804867"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233464847", "body": ""}
{"comment": {"body": "Too late - key lives forever in GitHub now \ud83d\ude05.\r\n\r\nTo answer the question about API keys when interacting with 3rd party services - there's really only 2 feasible approaches to this:\r\n1. Unblocked backend mints tokens for OpenAI access\r\n2. Requests to OpenAI are proxied through Unblocked\r\n\r\nOddly, it seems that OpenAI uses basic API key auth only: https://beta.openai.com/docs/api-reference/authentication\r\n\r\nThis means that (2) is really our only option or we'll leak keys in the client. \r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059805324"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233464967", "body": ""}
{"comment": {"body": "Slightly more idiomatic to do this, which will also prevent a crash:\r\n```swift\r\nguard let body = try? JSONSerialization.data(withJSONObject: requestBody) else { return }\r\nrequest.httpBody = body\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059805549"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233465014", "body": ""}
{"comment": {"body": "With latest swift, you only have to write:\r\n```swift\r\nif let data {\r\n   // ... \r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059805643"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233465024", "body": ""}
{"comment": {"body": "ditto", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059805672"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233465029", "body": ""}
{"comment": {"body": "?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059805679"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233465113", "body": ""}
{"comment": {"body": "Might have to change the return signature to be optional or to throw then do this:\r\n```swift\r\nlet dictionary = (try? JSONSerialization.jsonObject(with: data, options: [])) as? [String: Any] else { return nil }\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059805780"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233465145", "body": ""}
{"comment": {"body": "Force unwrapping might case a crash if the API shape changes in a weird way that causes the decoder to barf", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059805856"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1233465252", "body": ""}
{"comment": {"body": "Is this a transient dependency or intentional for image loading?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1059806018"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239708339", "body": ""}
{"comment": {"body": "Need to look further, but I think this is pulled in from the swiftchat. \n\nIt is not clear to me at all that we should use this rather than another lib.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064050283"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239708409", "body": ""}
{"comment": {"body": "After building the embedding stuff, it is pretty clear that all of this stuff should be on the server. Going to figure that out next week.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064050370"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239708451", "body": ""}
{"comment": {"body": "clearly, I am not an engineer. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064050418"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239708462", "body": ""}
{"comment": {"body": ":-)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064050441"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239708536", "body": ""}
{"comment": {"body": "didn't know. thank you, I yell internally when I write those if optional = optional.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064050548"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239708564", "body": ""}
{"comment": {"body": "I will do that, good pattern", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064050566"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239708620", "body": ""}
{"comment": {"body": "oh, I like that pattern too.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064050642"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239708854", "body": ""}
{"comment": {"body": "jaded about keys in private/demo repos (happy for them to pollute the namespace under rotation), but this clearly isn't worthy of being in a production.\n\n\n\nour embeddings are going to be around 1GB if the current training trend continues, so I think that 2 is where we'll go if this thing has legs?\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064050953"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239714684", "body": ""}
{"comment": {"body": "Yup that's sensible. Our current key strategy is to symmetrically encrypt and commit to the repo. Infrastructure decrypts and lands keys on deployed instances. Eventually we'll shoot for the moon and use a vault storage but the cost is not worth the additional maintenance overhead atm", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064059456"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239714948", "body": ""}
{"comment": {"body": "I feel like this kind of knowledge sharing is the highest order value you can get from reviews. Instant level-up. It would be amazing to be able to surface this kind of insight from from all the usual review noise. Things like \"nits\", \"needs more tests\", or even refactoring suggestions are not particularly useful long-term", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064059866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#pullrequestreview-1239715105", "body": ""}
{"comment": {"body": "I don't think it matters too much. Kingfisher has been around for eons and is pretty stable. I'm just always a bit nervous about libraries that have a ton of their own dependencies on dubious or poorly maintained projects, just trying to make sure we remain mindful of that :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4187#discussion_r1064060136"}}
{"title": "usercloud testing", "number": 4188, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4188", "body": "Finally got a chance to run the bert data science notebook over the prod usercloud data. Below is what we get with nearly ~1k messages. Right now the prod data pipeline only generates the default topic cluster, which is 3 terms. It is possible that all the messages aren't making into the prod bert job. will look at that next.\nTopic   Count   Name\n0   -1  268 -1_authz_test_state\n1   0   85  0_pagination_server_storage\n2   1   84  1_policy_tokenizer_idp\n3   2   80  2_invite_profile_public\n4   3   47  3_debug_devbox_log\n5   4   47  4_events_log_audit\n6   5   47  5_tenants_status_auth\n7   6   33  6_config_userstore_paths\n8   7   29  7_reducers_return_users\n9   8   29  8_jsonclient_jsonapi_underlying\n10  9   28  9_staging_prod_provisioning\n11  10  27  10_golang_samples_sdk\n12  11  25  11_aws_region_userclouds\n13  12  25  12_git_dev_code\n14  13  22  13_azcli_namespace_storage\n15  14  21  14_component_ui_lib\n16  15  19  15_test__\n17  16  18  16_tests_test_cache\n18  17  18  17_policy_user_code\n19  18  17  18___\n20  19  16  19_table_tenantdb_logdb"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4188#pullrequestreview-1233378088", "body": ""}
{"title": "Daos should not be used for status updates", "number": 4189, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4189", "body": "Used a lot of network traffic to drag in daos for updates.\nWe should be using models..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4189#pullrequestreview-1241104238", "body": ""}
{"title": "Scaling up dev cluster", "number": 419, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/419", "body": "Scaling up dev cluster\nRemoving old unused node group \nDev cluster was hitting number of pod limits. T3 instances have a low pod count so I added a second node. \nWe used a lot of our pod count for Controllers and Monitoring pods.\n\nNote: Cluster auto-scaler seems to work on CPU and Memory only and not pod limits. Pod limits are due to Network drivers which Kube doesn't know how to handle when it comes to scaling\nI intentionally haven't configured CI/CD for Kubernetes because of eksctl limitations. It's not smart enough to diff a config file and only deploy changes. Most changes require explicit CLI invocation to update resources. \nCommand used to update cluster scale:\neksctl scale nodegroup --cluster=dev --name=eks-managed-t3-medium --nodes-min=2 --nodes-max=3 --nodes=2"}
{"comment": {"body": "NAME                                           STATUS   ROLES    AGE   VERSION\r\nip-172-16-112-117.us-west-2.compute.internal   Ready    <none>   28d   v1.21.5-eks-9017834\r\nip-172-16-174-212.us-west-2.compute.internal   Ready    <none>   13m   v1.21.5-eks-9017834", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/419#issuecomment-1051228743"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/419#pullrequestreview-894213879", "body": ""}
{"title": "Get and display pull request team members", "number": 4190, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4190", "body": "Per https://github.com/NextChapterSoftware/unblocked/pull/4029, atttach pr participant team members to the client model and display if they exist\nAlso added a generic component to display a string list of items with a helper title attribute"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4190#pullrequestreview-1241381008", "body": ""}
{"comment": {"body": "There's an empty ItemList.scss file. Remove?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4190#discussion_r1065231877"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4190#pullrequestreview-1241382467", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4190#pullrequestreview-1241383411", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4190#pullrequestreview-1241391423", "body": ""}
{"comment": {"body": "Done. Thanks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4190#discussion_r1065239394"}}
{"title": "Enterprise options UI", "number": 4191, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4191"}
{"title": "Base enterprise options UI", "number": 4192, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4192", "body": "Added functional support for Enterprise login / registration.\nNon web clients should not be affected and only display existing public providers (aka GH)."}
{"comment": {"body": "This PR is hypothetical... Has not been functionally tested with real APIs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4192#issuecomment-**********"}}
{"comment": {"body": "PR was originally intended to go into another branch... Since that's been merged, PR view is in a weird state.\r\nOverall this PR is pretty safe. Only changes that would affect existing product is the new usage of getLoginOptionsV2.\r\n\r\nWill merge this in first to start testing out the flow.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4192#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4192#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4192#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Fetching provider as a string purely for display purposes.\r\n\r\nIf we want to do more with it, we'll need to type it back to the Provider enum.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4192#discussion_r1065110645"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4192#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4192#pullrequestreview-**********", "body": ""}
{"title": "Multipart Asset Upload Api", "number": 4193, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193", "body": "Steps\n\nAsk the AssetService to initiate a multipart upload with S3, and generate the pre-signed urls for each part\nClient uploads the parts, and receives an eTag in each response\nClient sends completion request to AssetService with a map of { partNumber : eTag }"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This upload id comes from Amazon and is opaque", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193#discussion_r1065117176"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193#pullrequestreview-1241219364", "body": ""}
{"comment": {"body": "S3 has a 10000 part upper limit", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193#discussion_r1065117540"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193#pullrequestreview-1241239141", "body": ""}
{"comment": {"body": "Do we need to provide a hash for each part? For integrity purposes?\r\n ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193#discussion_r1065130684"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193#pullrequestreview-1241248298", "body": ""}
{"comment": {"body": "I'm not sure that's necessary for now. The urls are pre-signed and are authenticated with our service and restricted to author only.\n\nThe only attack vector would be if the presigned part url was stolen. \n\nFor client side integrity checks, it just has to check the hash in the upload part response and abort or retry if there is a mismatch.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193#discussion_r1065136709"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4193#pullrequestreview-1241286424", "body": ""}
{"title": "Multipart upload service implementation", "number": 4194, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4194"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4194#pullrequestreview-1242633782", "body": ""}
{"comment": {"body": "@rasharab I modified the behaviour to throw forbidden if the person trying to delete the asset is not the author. Contemplating just returning 404 instead.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4194#discussion_r1066032264"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4194#pullrequestreview-1242637151", "body": "Looks good to me"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4194#pullrequestreview-1242654661", "body": ""}
{"comment": {"body": "@rasharab changing this to 12h to make the client implementation a tad simpler. If we want this reduced we'll have to add an API to refresh urls for individual upload parts", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4194#discussion_r1066049729"}}
{"title": "Include pull request participants", "number": 4195, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4195"}
{"title": "Use custom emoji library", "number": 4196, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4196", "body": "Moving away from outdated library and updated to the latest emoji+unicode mappings as found here.\n\nCustom implementation for emoji parsing which should take skin tones into account."}
{"title": "'Add topic' dashboard UI", "number": 4197, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197", "body": "Add sidebar to the topics page that shows recommended new topics, and allows creating a new topic\nAdd 'new topic' wizard -- this can be used to update an existing topic too\n\nStill needs a bit of polish (will be added in a followup PR):\n* We need to refresh the topic stores after creating the new topic (since we don't have a push channel).  Still figuring this out\n* A couple things don't work 100% right in mobile view\n* A few other tiny UI bits and pieces that probably aren't urgent\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Let's introduce a slack channel that announces every time we add a new size const :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065234544"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241384755", "body": ""}
{"comment": {"body": "heh will bing every few minutes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065234719"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241385536", "body": ""}
{"comment": {"body": "Didn't see this before. May take a stab at consolidating this with the app.tsx so that routes are only typed once...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065235243"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241386313", "body": ""}
{"comment": {"body": "The tricky part would be that routes within app.tsx are nested. \r\naka\r\n```\r\n<Route path=\"/team/:teamId\">\r\n   <Route path=\"/threads...\"/>\r\n      ...\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065235807"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Yeah -- I'm not sure how this can be integrated with the routing table to be hoenst...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065236178"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Instead of undefined, may be worth having a \"non-unblocked\" member user?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065236493"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-**********", "body": ""}
{"comment": {"body": "To be clear, this is expected to never happen -- it would only happen if the topic table is referencing a team member that no longer exists.  I'm not sure we can display anything meaningful in this case, \"Unknown User\" won't help people much...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065237341"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241388893", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241389587", "body": ""}
{"comment": {"body": "This `as` safe?\r\n\r\nWe have it defined as `    const currentState = location.state as { prevRoute?: string };` in app.tsx", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065238056"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241390518", "body": ""}
{"comment": {"body": "Not necessary now but will eventually need to error handle this as I believe this is just an API request atm?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065238741"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241391520", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241391582", "body": ""}
{"comment": {"body": "Yep -- will handle this in the subsequent PR where I also handle updating the cache when the value is updated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065239531"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241399074", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241401171", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241401762", "body": ""}
{"comment": {"body": "It's not safe, no.\r\n\r\nThe state can be any data at all -- this *should* have a type narrowing function to actually tell at runtime if the object is correct.  I'll write one.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065246809"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#pullrequestreview-1241406042", "body": ""}
{"comment": {"body": "Added a type predicate to validate that the type is correct at runtime.  This removes the need for `as`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4197#discussion_r1065249861"}}
{"title": "Updates to web contributor list", "number": 4198, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4198", "body": "Fixes icon styling to have proper background colours (background color overrides existed in VSCode but not web)\nGitContributors from FileReferences were not going through team member resolution. Updated.\n\nBefore:\n\nAfter:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4198#pullrequestreview-1241394269", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4198#pullrequestreview-1241394303", "body": ""}
{"title": "Most to latest gradle build cache node", "number": 4199, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4199"}
{"title": "Add user status wrapper to icon", "number": 42, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42", "body": "*Note: will update some of this code with theme colors once I get theming set up (i.e. the background of the indicator)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854793679", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854793958", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854794611", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854796148", "body": ""}
{"comment": {"body": "I'd prefer if we *don't* reference other variable files if possible.\r\nThe current reason to do so makes sense but could start leading to circular dependencies.\r\n\r\nI think the solution may be to separate Mixins & variables? I didn't go down this path initially as I didn't think we were going to leverage mixins too much.\r\nI've seen this pattern done in a few libraries such as GH Primer.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#discussion_r786299925"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854796730", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854797538", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854797726", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854798122", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854800232", "body": ""}
{"comment": {"body": "I think we need to come to a decision on variable case.\r\nkebab case (aka icon size) vs camel case (aka user status)\r\n\r\nI actually have a slight preference towards camel case but I think icon size is kebab case due to matching sass?\r\n ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#discussion_r786303122"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854849807", "body": ""}
{"comment": {"body": "I also prefer camel casing in general ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#discussion_r786342326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854851692", "body": ""}
{"comment": {"body": "I wouldn't mind general rule of everything in camelCase but icon sizing is the exception since it has to match the css kebab\r\n\r\nbut I guess I don't feel super strongly. cc: @matthewjamesadam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#discussion_r786343900"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-854863272", "body": ""}
{"comment": {"body": "I'm open to this -- not sure if we should have one master `mixin.scss` file or maybe a `mixin/` folder with different semantic areas\r\n\r\nI've also seen some patterns where they just export everything from one `main.scss` file (in a deliberate order) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#discussion_r786353019"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-855765658", "body": ""}
{"comment": {"body": "I don't think it should be a single mixin folder. Should be scoped to certain areas similar to our variables.\r\n\r\nI'm not sure atm of the implications of exporting everything into a single main.scss file... I can look into this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#discussion_r787001745"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-855766211", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-855780026", "body": ""}
{"comment": {"body": "I like camelCase everywhere.  I'd suggest using a mapping table to map camelCase iconSizes to kebab-case icon-sizes, as there aren't very many values.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#discussion_r787012059"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#pullrequestreview-855835491", "body": ""}
{"comment": {"body": "My inclination is to leave the refactoring to another PR, when we have a clearer idea on how to proceed. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/42#discussion_r787051571"}}
{"title": "Hookup Thread & Message Creation", "number": 420, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420", "body": "For Web, setup message creation.\n* Does not include live updates as that depends on Message channel. TODO\nFor VSCode, setup thread creation.\nIntroduces new UUID library (copied from VSCode) since VSCode does not support crypto.getRandomValue which the npm uuid lib depends on."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-894224569", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-894224897", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-894226336", "body": ""}
{"comment": {"body": "With current implementation, we are updating title on every key press.\r\nShould debounce or have a \"save\" button treatment.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#discussion_r815149455"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895491784", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895493050", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895493844", "body": ""}
{"comment": {"body": "what does this do? add comment?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#discussion_r816178559"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895579132", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895580041", "body": ""}
{"comment": {"body": "We should probably also have a test to ensure that this flag is set and updated correctly.  I'm happy to do it in another PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#discussion_r816240290"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895581083", "body": ""}
{"comment": {"body": "It selects all content on focus...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#discussion_r816240999"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895587089", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895596273", "body": ""}
{"comment": {"body": "This is telling me that what we really need is an ObservableMap of some kind?  Presumably wherever we use this, we would want to also be updated if the Thread changes, so we actually want an observable/subscription API?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#discussion_r816251875"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895596481", "body": ""}
{"comment": {"body": "(Not feedback for this PR per se, just thinking out loud.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#discussion_r816252054"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895606823", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895611695", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895655585", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895657547", "body": ""}
{"comment": {"body": "Specifically, it moves the cursor to the bottom.\r\n\r\nWithout this, it was setting focus at the top of the editor, on top of initialContent.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#discussion_r816296479"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895657825", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895659191", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895659519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895664406", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895673570", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895674699", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-895726720", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896500912", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896551844", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896579921", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896657144", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896665782", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896666866", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896670022", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896678983", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896687217", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896694710", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896695242", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896697483", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/420#pullrequestreview-896698860", "body": ""}
{"title": "The /enterpriseProviders endpoint routes to auth service", "number": 4200, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4200"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4200#pullrequestreview-**********", "body": ""}
{"title": "Add application name to jdbc driver", "number": 4201, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4201", "body": "We need this for better debugging with aurora."}
{"title": "Prevent default for anchors within Link container", "number": 4202, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4202"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4202#pullrequestreview-**********", "body": ""}
{"title": "PowerML + UnblockedChat notebooks", "number": 4203, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4203", "body": "examples of the powerml topic gen and the openai summarization/qa"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4203#pullrequestreview-1241810973", "body": ""}
{"title": "Standardize db connection properties", "number": 4204, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4204", "body": "Ensure core set of properties are standarized across various connection types."}
{"title": "Multipart upload client implementation", "number": 4205, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4205", "body": "There's quite a bit of resilience built into this:\n- Every API call is now auth gated so that we're not falling over when there's no auth token\n- Every API call retries 3 times for >= 500 errors\n- Uploads are now chunked to 5MB parts. Each part is retried up to 3 times with a backoff before giving up\n- Parallelized to 3 simultaneous part uploads. This parallelization is not dynamic, meaning it doesn't currently adjust according to network conditions. We could do that if needed through. The idea is to allow parts to complete while still maintaining some semblance of network efficiency. \n- Upload attempts are also auth gated as a hint about network reachability\n- Uploads will fall back to single single-shot if the file size is <= 20MB"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4205#pullrequestreview-1243121085", "body": ""}
{"comment": {"body": "Tempted to pull this in.\r\nhttps://github.com/apple/swift-algorithms", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4205#discussion_r1066450359"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4205#pullrequestreview-1243126597", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4205#pullrequestreview-1243132318", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4205#pullrequestreview-1243135703", "body": ""}
{"comment": {"body": "I'm not sure if we've hit the threshold for this just yet.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4205#discussion_r1066461289"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4205#pullrequestreview-1243140513", "body": ""}
{"title": "Guard against multiple Hub launch attempts", "number": 4206, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4206"}
{"comment": {"body": "Don't think the app is being launched multiple times. This is purely an OS bug", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4206#issuecomment-1378154362"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4206#pullrequestreview-1242769364", "body": ""}
{"comment": {"body": "And this will skip this method if it has already been executed for this instance", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4206#discussion_r1066157736"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4206#pullrequestreview-1242776336", "body": ""}
{"comment": {"body": "Terminate if another hub instance exists", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4206#discussion_r1066164238"}}
{"title": "Demo hack", "number": 4207, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4207"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4207#pullrequestreview-1242824369", "body": ""}
{"title": "Undo demo hack", "number": 4208, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4208", "body": "Undo demo hack.  Also add text clipping to context rows so very long topic names clip properly.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4208#pullrequestreview-1242863947", "body": ""}
{"title": "Use MenuBarOwningApplication on initializtion", "number": 4209, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4209", "body": "\nIssue Dennis was running into where slurps were sometimes missing.\nThe issue is that the frontmostApplication when AppUrlMonitor launches is always the UnblockedVideo App, not the application before.\nOur original approach was the activate the previous application but the activation event isn't perfect. Current theory is if that the menubarOwningApplication === target application, activating the target application doesn't actually work...\nTo work around this, the initial foreground application will be the menuBarOwningApplication (which will never be the VideoApp since it's an accessory app). \nOn application did change, we still want to go observe the frontmostApplication"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4209#pullrequestreview-**********", "body": ""}
{"title": "Prep infra for pusherservice", "number": 421, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/421", "body": "Adding service account for a new pusher service (for now same perms as API service)\nAdding a new ECR repo for pusher service images (for now we build API service code and push it there)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/421#pullrequestreview-*********", "body": ""}
{"title": "Fix search for empty arrays", "number": 4210, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4210"}
{"title": "Add support for app types", "number": 4211, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211", "body": "Add API support for app types references\nEDIT:\nDue to issues with this being a breaking API change, decided to encode the app type into the FilePath instead for webFileReferences."}
{"comment": {"body": "<img width=\"1436\" alt=\"CleanShot 2023-01-11 at 10 54 34@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-592539ca-94a7-48c8-8a18-7564c9d44c71.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#issuecomment-1379347713"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1243168160", "body": ""}
{"comment": {"body": "curious about this change. Is this to avoid the private init pattern?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#discussion_r1066504046"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1243204821", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1243316838", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1243317284", "body": ""}
{"comment": {"body": "This was not on purpose....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#discussion_r1066594417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1243317877", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244311644", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244476530", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244483835", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244484162", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244484629", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244485709", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244486446", "body": "Need to fix bundleId for safari"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244486885", "body": "Pulling approve"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244507130", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4211#pullrequestreview-1244507619", "body": ""}
{"title": "Enable StyleLint in VSCode", "number": 4212, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4212", "body": "Enable StyleLint processing in VSCode -- this gives red error underlines for CSS/SASS style bugs, so you don't have to wait for linting to run in CI or commandline.\nAt some point the StyleLint people changed the default settings to not process SASS files.  This re-enables it.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4212#pullrequestreview-**********", "body": ""}
{"title": "Add resiliency to processing stages", "number": 4213, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4213"}
{"title": "[BREAKS API ON MAIN] Enterprise providers api implementation", "number": 4214, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4214", "body": "Breaking API change:\n- Added values to EnterpriseProviderResult.Status enum\n- Not breaking customers, since this API is not yet used in PROD.\nTODO\n\n[ ] tests\n\nDEFER\n\n[ ] encrypt secrets\n[ ] persist session and encode key in state parameter"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4214#pullrequestreview-**********", "body": ""}
{"comment": {"body": "@jeffrey-ng some changes here. can you take a look?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4214#discussion_r1066497296"}}
{"title": "Remove ses kinesis stack", "number": 4215, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4215"}
{"title": "Remove video walkthrough from VSCode", "number": 4216, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4216", "body": "Removes Walkthrough entrypoint and code from VSCode"}
{"comment": {"body": "Code references still working as that code lives independently from starting / creating a walkthrough.\r\nhttps://user-images.githubusercontent.com/1553313/211683046-bf869568-8038-4234-b217-5d918eb13a95.mp4\r\n\r\n\r\n<img width=\"1307\" alt=\"CleanShot 2023-01-10 at 15 23 01@2x\" src=\"https://user-images.githubusercontent.com/1553313/211683147-aecbfe32-76ca-4121-b6a4-e11488fb4b98.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4216#issuecomment-1378027024"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4216#pullrequestreview-1244324918", "body": ""}
{"title": "Dont index open pull requests", "number": 4217, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4217"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4217#pullrequestreview-1243107228", "body": ""}
{"title": "Use new Ventura API for login item registration", "number": 4218, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4218"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4218#pullrequestreview-1243138077", "body": ""}
{"title": "Use github auth token for protoc clone step", "number": 4219, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4219"}
{"title": "Setup helm charts and CI/CD for new pusher service", "number": 422, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/422", "body": "Created new helm chart thingi for new pusher service\nModified Github actions workflows to deploy a copy of APIservice as pusher service\nModified service deployment workflow to take ECR repo name as an input param\n\nAs we discussed, the plan is to bring up a copy of API service as pusher until we separate those services.\nInfra prep PR: https://github.com/NextChapterSoftware/unblocked/pull/421"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/422#pullrequestreview-894267087", "body": ""}
{"title": "Don't delete approved (or rejected) topics when regenerating topics", "number": 4220, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4220", "body": "Matt switched us over to Bert generated topics and approved a subset of them. We shouldn't delete these approved topics when regenerating topics."}
{"title": "powerml topics", "number": 4221, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4221"}
{"title": "Fix bugs in Add Topic dashboard UI", "number": 4222, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222", "body": "Refresh topic store listing after creating a new topic\nShow Close button in Add Topic view\nFix bug where navigating back and forth in the wizard would clear the selected set of experts"}
{"comment": {"body": "Some nits I noticed (don't need to addressed in this PR):\r\n\r\n1. I wonder if we should cap this list at the first 4-5? It feels a little heavyweight, especially if the approved topics list is shorter than this one\r\n    <img width=\"273\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/211905381-ba0b4108-939d-4a9e-80bf-7fe8a0e8ab81.png\">\r\n\r\n2. Think we need some spacing at the bottom here\r\n    <img width=\"327\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/211905540-ff7a5dbb-5135-4ebe-ab73-a4693b5d143b.png\">\r\n\r\n3. I think this is an edge case but we should probably have some placeholder image here if there happens to be no approved topics \r\n    <img width=\"1334\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/211905885-41cd655a-0cb4-49b9-9ad5-1b029f8b7115.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#issuecomment-1379411025"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#pullrequestreview-1243151652", "body": ""}
{"comment": {"body": "Had to update this because it was out of sync with the TS that ships with VSCode, and the `in` keyword wasn't working correctly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#discussion_r1066472973"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#pullrequestreview-1243151863", "body": ""}
{"comment": {"body": "FYI @jeffrey-ng here is the right-but-ugly way to dynamically narrow the type at runtime", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#discussion_r1066473146"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#pullrequestreview-1243152235", "body": ""}
{"comment": {"body": "All the changes here are solely so that we can manually trigger a re-run of topic loading, when we locally add a new topic.  A better solution is probably to add a push channel.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#discussion_r1066473494"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#pullrequestreview-1244318434", "body": ""}
{"comment": {"body": "Needed to do this to build with TS 4.9", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#discussion_r1067263066"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#pullrequestreview-1244319630", "body": ""}
{"comment": {"body": "All of the knowledge-creation forms needed to be changed in this way to build with TS 4.9.  It makes sense, what we were doing before was technically wrong (though it seemed to work at runtime).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#discussion_r1067263858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#pullrequestreview-1244323046", "body": ""}
{"comment": {"body": "Haha. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#discussion_r1067266120"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#pullrequestreview-1244324876", "body": ""}
{"comment": {"body": "Instead of keeping data in state, should we pass data with another technique?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#discussion_r1067267385"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#pullrequestreview-1244326004", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#pullrequestreview-1244363604", "body": ""}
{"comment": {"body": "It's a good question.  I guess the alternative would be:\n\n\n\n1) Store in a variable.  The nice thing about using state is that we can store the entire object easily, and it is stored in browser memory, so if you refresh the page the state is maintained.  If we used a variable then refreshing would drop the state.  I'm also not sure exactly how we would know to clear this variable, because it is not attached to a particular navigation point.\n\n\n\n2) Store in the path (as a query argument?).  This is pretty similar to storing in state but we'd need to store all the values we need separately, as you can't store an object here reasonably.\n\n\n\nI'm open to changing this if you feel strongly either way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4222#discussion_r1067293503"}}
{"title": "Remove code dupe", "number": 4223, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4223"}
{"title": "Clean up inputs to histogram pipeline", "number": 4224, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4224", "body": "Adds back some of the text cleaning logic that was removed from the preprocessing step"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4224#pullrequestreview-1243345672", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4224#pullrequestreview-1249124965", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4224#pullrequestreview-1249125001", "body": ""}
{"title": "Make progress dialog backgroundable", "number": 4225, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4225", "body": "Customer request. Motivation: under terrible network conditions the dialog gets in the way\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4225#pullrequestreview-1244530462", "body": ""}
{"title": "Add a maximum connection lifetime duration for db", "number": 4226, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4226"}
{"title": "[BREAKS API ON MAIN] Remove counts from search req and add new counts endpoint", "number": 4227, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244475608", "body": ""}
{"comment": {"body": "@kaych make this required?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#discussion_r1067368767"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/4227#pullrequestreview-1244477895", "body": ""}
