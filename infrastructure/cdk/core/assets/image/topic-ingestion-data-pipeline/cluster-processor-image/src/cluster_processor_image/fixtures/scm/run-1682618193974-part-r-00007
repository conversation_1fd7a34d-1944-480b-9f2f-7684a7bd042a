{"comment": {"body": "> 1. The algorithm has to run pretty fast because it's going to synchronously block rendering the entire UI.  What we have here is potentially O(n^2), which could get bad for giant source blocks with large spaces.  Not a problem for now, but something to keep an eye on if we notice these UIs are becoming slower.\r\n\r\nIt's O(n log n) if you sort the lines first, see https://www.geeksforgeeks.org/longest-common-prefix-using-sorting/.\r\n\r\nHowever, _n_ is going to be insignificant in all cases that I can practically think of. Typically less than 5. IMO, not worth optimizing. We could put in a limit for corner cases; so we'd abort the trimming if the snippet line count was greater than 100.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2271#issuecomment-1183529672"}}
{"comment": {"body": "> @matthewjamesadam This scenario is covered, since this change consistently removes the exact same number of tabs or spaces from _every_ line. We don't make any assumption that a tab is 4 x spaces for example.\r\n\r\nA team that consistently uses (tab=4 spaces) could have code like:\r\n\r\n```\r\n<tab>something\r\n<space><space><space><space>something else\r\n```\r\n\r\nVisually in an editor this would look aligned, but there's no good way for us to clip this such that it would appear unindented in the snippet view.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2271#issuecomment-1183532943"}}
{"comment": {"body": "> > @matthewjamesadam This scenario is covered, since this change consistently removes the exact same number of tabs or spaces from _every_ line. We don't make any assumption that a tab is 4 x spaces for example.\r\n> \r\n> A team that consistently uses (tab=4 spaces) could have code like:\r\n> \r\n> ```\r\n> <tab>something\r\n> <space><space><space><space>something else\r\n> ```\r\n> \r\n> Visually in an editor this would look aligned, but there's no good way for us to clip this such that it would appear unindented in the snippet view.\r\n\r\nI know, you are correct. However, Dave's change handles this and does _not_ currently clip in this scenario. Maybe add another test to demonstrate this @davidkwlam ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2271#issuecomment-1183534051"}}
{"comment": {"body": "@richiebres Yep, that's right: we only clip if the leading whitespace characters are of the same type, since there's no way to know with certainty a tab equals n spaces. I'll add a test to show.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2271#issuecomment-1183543128"}}
{"comment": {"body": "ship it \ud83d\ude80 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2271#issuecomment-1183554124"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2271#pullrequestreview-1036634407", "body": "Superb tests as usual"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2271#pullrequestreview-1037679579", "body": "This looks good!  A couple thoughts:\n\n\nThe algorithm has to run pretty fast because it's going to synchronously block rendering the entire UI.  What we have here is potentially O(n^2), which could get bad for giant source blocks with large spaces.  Not a problem for now, but something to keep an eye on if we notice these UIs are becoming slower.\n\n\nOne corner case I can think of, but I don't think we can deal with: mixed spaces and tabs.  I know that seems odd, but for teams that don't use auto formatters it does happen.  In that case unless we somehow know how many spaces a tab resolves to visually I'm not sure we can do anything useful."}
{"title": "update the Dev teamID in smoke tests", "number": 2272, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2272", "body": "This still needs the secret for Dev Refresh Token to be updated before we can merge it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2272#pullrequestreview-**********", "body": ""}
{"title": "Dashboard cleanup", "number": 2273, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2273", "body": "Update fonts to match styleguide \nStyle the dashboard modal \nAdd a new destructive button type \nRefactor the Layout hooks into a wrapper component for explicit use and to avoid unintentional layouts\nRefactor theming into its own provider\nAdd max-width to the sidebar \nSmall layout bug fixes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2273#pullrequestreview-**********", "body": "A couple comments, but looks good"}
{"comment": {"body": "This will still set the global layout style on first render for the component... I'm not sure about the best way to fix this, maybe undoing the layout back to a default when the component is unloaded?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2273#discussion_r919589440"}}
{"title": "Adds release channels", "number": 2274, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274", "body": "Part 1 (this PR)\nImplements the release channel concept. Current channels are:\n- Stable\n- Beta\n- Internal\nEach team is subscribed to a single release channel. The default is Stable. This PR does not include changes to the admin console - that's coming in Part 2, along with the ability to change the release channel a team is subscribed on.\nDesign:\n- A version can belong to multiple release channels. This gives us maximum flexibility to assign special builds to certain teams, or to define unique release set for a particular build\n- Some channels are supersets of others. Ex: Internal is a super set of Beta and Stable. This means that builds released to Beta will also be released to Internal. Set relationships are computed at runtime and aren't part of the model\nPart 2 (next PR)\n\nWire up release channel assignment and team subscriptions in admin console"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274#pullrequestreview-1036576667", "body": ""}
{"comment": {"body": "idempotent", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274#discussion_r919502378"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274#pullrequestreview-1036582467", "body": ""}
{"comment": {"body": "Only returns `Stable` releases when authorizedTeams is empty", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274#discussion_r919506860"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274#pullrequestreview-1037890536", "body": ""}
{"comment": {"body": "this is the bit that we chatted about simplifying?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274#discussion_r920465893"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274#pullrequestreview-1037916962", "body": ""}
{"comment": {"body": "yup", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274#discussion_r920466579"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2274#pullrequestreview-1037997054", "body": ""}
{"title": "Update", "number": 2275, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2275"}
{"title": "Set prCommentBodyHash on MessageModel", "number": 2276, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2276", "body": "Part one of \nNext PR will run a migration to set this property (we'll pull from the  to minimize the number of API calls needed) then use this new property instead of prCommentUpdatedAt."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2276#pullrequestreview-1036636822", "body": ""}
{"title": "Backfill sourcemark modifiedAt timestamps", "number": 2277, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2277", "body": "Needed for sourcemark pagination to work properly."}
{"title": "add pinpoint apis", "number": 2278, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2278"}
{"title": "Use prCommentBodyHash to determine if a message has been updated", "number": 2279, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2279"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2279#pullrequestreview-1037836554", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2279#pullrequestreview-1038085653", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2279#pullrequestreview-1038093326", "body": ""}
{"title": "Run build in CI for vscode/web", "number": 228, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/228"}
{"comment": {"body": "not sure what the intention is, but you wanna do this for shared-web too?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/228#issuecomment-1028440921"}}
{"comment": {"body": "> not sure what the intention is, but you wanna do this for shared-web too?\r\n\r\nShared-web doesn't have a build, it just has tests and a storybook, so that's intentional.  The root cause here is that the way we build code for testing and for execution is slightly different, so even if our tests pass in CI, a build may fail.  We're in this state right now -- someone committed some code where tests pass but the build doesn't work.  This is a cheap way to fix this for now.  There might be a better way to fix the root cause, I'm not certain.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/228#issuecomment-1028446135"}}
{"comment": {"body": "Closing in favour of #232 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/228#issuecomment-1029289796"}}
{"title": "Possibly address long-running admin web task completion", "number": 2280, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2280", "body": "Problem\nStruggling to get some long-running tasks in admin web to complete synchronously (within\nan admin web POST request, which is 30 seconds) because each of these tasks performs DB\noperations on the entire thread or source mark collections.\nWorkaround\nApparently, I'm committing all sorts of sins by launching GlobalScope jobs,\nso I am at least timing out these jobs after a few minutes to prevent the service from\nfalling over due to resource leaks.\nAlternatives (future)\n\nuse an SQS queue for each item in the DB\nuse DB streaming rather than fetching an entire table worth of IDs\ninvoke periodically automatically and post-ingestion (for the recommendation task) instead of triggering manually\n  \n\n"}
{"title": "Login page button style", "number": 2281, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2281", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2281#pullrequestreview-1037619060", "body": ""}
{"comment": {"body": "why this extra column?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2281#discussion_r920257440"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2281#pullrequestreview-1037620029", "body": ""}
{"comment": {"body": "ah i see, you're using divs to apply spacers on right and left", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2281#discussion_r920258139"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2281#pullrequestreview-1037620849", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2281#pullrequestreview-1037660381", "body": ""}
{"comment": {"body": "yeah, grid system\n\nhttps://getbootstrap.com/docs/4.0/layout/grid/\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/52f00371-fbed-45ea-88ae-54d60662d492?message=d1815ff6-600b-4807-8b5b-a0829bb31791).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2281#discussion_r920286471"}}
{"title": "double write installer and add new /releases CF endpoint", "number": 2282, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2282", "body": "/build will be the new destination for publishing all build when we switch to new artifact ingestion code\n/releases will hold artifacts that are downloadable by end users (public)\nAdded the new /releases* CloudFront endpoint to prepare for transition \nModified CI/CD to double write artifacts to help to but the old destination as well as the new /builds prefix"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2282#pullrequestreview-1037741281", "body": ""}
{"title": "[Admin Console] Replace \"Modified\" with \"Last Active\" on the People page", "number": 2283, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2283"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2283#pullrequestreview-1037755642", "body": ""}
{"title": "Add release channels to admin console", "number": 2284, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2284", "body": "Part 2\nTeams now have a dropdown selector to assign a release channel. \nVersions have release channel toggles. The UX is a bit funky here because some channels are supersets of others and the toggles don't currently reflect those relationships. We can create these relationships but it could cause some confusion - for example turning off \"Internal\" implies turning off everything, turning on \"Stable\" implies turning on everything, etc"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2284#pullrequestreview-1037782998", "body": ""}
{"comment": {"body": "I snuck this in here for @mahdi-torabi . He's going to be using it in some related work", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2284#discussion_r920374132"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2284#pullrequestreview-1037938708", "body": ""}
{"comment": {"body": "should use the bootstrap dropdown component instead, as it'll behave and look better. I can do this in a minor follow-up change...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2284#discussion_r920482892"}}
{"comment": {"body": "use the existing `sort` argument to prevent sorting alphabetically \r\n```suggestion\r\n            renderToggleList(\r\n                sort = false,\r\n                href = \"$path/updateReleaseChannels\",\r\n                title = \"Release Channels\",\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2284#discussion_r920498558"}}
{"title": "UpdateEmailChannel", "number": 2285, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2285", "body": "Update\nUpdate"}
{"title": "added a new bucket and separate CF behavior for releases", "number": 2286, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2286", "body": "My last attempt to create a new CF failed because of a misconfiguration. It's much cleaner to create a new bucket for our release management stuff. \n\nAdded a new releases bucket \nAdded a /releases* CloudFront behaviour. The old /download-assets will be removed once we have finished cutting over to the new release mechanism.\nAdded publish permission for the new bucket to deploybot user \n\nI'll make a separate PR to update the ci-installer.yaml workflow (which is broken right now) once these changes have been deployed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2286#pullrequestreview-1037813927", "body": ""}
{"title": "double write to new releases buckets", "number": 2287, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2287", "body": "Part 2 of fixing my mess:\nDouble writes all installer build artifacts to both buckets. \nRequires https://github.com/NextChapterSoftware/unblocked/pull/2286"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2287#pullrequestreview-1037862114", "body": ""}
{"title": "[RFC] User and Team graphs are side-by-side", "number": 2288, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2288", "body": "Easier to read side-by-side with separate tables I think.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2288#pullrequestreview-1037877386", "body": ""}
{"title": "Jeff/unb 428 fix code highlighting in web extension", "number": 2289, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2289", "body": "Fix code highlighting due to moving from background script to content script.\nUtilizes PromiseProxyClientFn"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2289#pullrequestreview-1037952375", "body": ""}
{"comment": {"body": "newInstall.ts made it into this PR too, let's remove it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2289#discussion_r920487008"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2289#pullrequestreview-1037964863", "body": ""}
{"title": "Fix annotations", "number": 229, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/229", "body": "We need to modify annotations of subchart.\n"}
{"title": "Jeff/unb 413 show bubbles in the GitHub diff view", "number": 2290, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290", "body": "\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1039363739", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1039364761", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1039393012", "body": ""}
{"comment": {"body": "Ugh, this makes me think that we'll need to handle all other cases where there's no body correctly.  I'm not sure how to solve this in general.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#discussion_r921512121"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1039439826", "body": ""}
{"comment": {"body": "I couldn't think of a generic way of handling this.\r\n\r\nI don't think it's safe to assume that when `response.text()` returns an empty string, we should set the body as null/undefined/", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#discussion_r921544543"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1039492040", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1039498186", "body": ""}
{"comment": {"body": "nit, can just be `public key: SourceMarkRendererUtilsKey`, then you don't need to define the member or assign", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#discussion_r921584100"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1039504180", "body": ""}
{"comment": {"body": "Wow, this is a lot of crazy dom inspection/manipulation.  It'll be interesting to see how often this stuff breaks as GH evolves...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#discussion_r921588320"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1039516447", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1040675219", "body": ""}
{"comment": {"body": "test\n\n\n\n\n\ntes\n\nt\n\nte\n\ns\n\nt\n\nt\n\nes\n\n\n\n\n\n\n\ntest\n\n\n\n\n\ntest\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/2fd082c6-8a30-4257-95ec-c8805258f193/thread/a7ef8374-46b8-46f9-bf34-999f28caa817?message=6f8e3ed1-921d-4122-a04d-fec61cd8399a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#discussion_r922426582"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#pullrequestreview-1040675307", "body": ""}
{"comment": {"body": "test\n\n\n\n\n\ntes\n\n\n\ntes\n\nt\n\n\n\n\n\ntest\n\n\n\n\n\ntest\n\n\n\n\n\ntest\n\n\n\n\n\nset\n\nset\n\nest\n\nest\n\ntes\n\nest\n\nse\n\nt\n\nset\n\nset\n\nset\n\nset\n\nse\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/2fd082c6-8a30-4257-95ec-c8805258f193/thread/a7ef8374-46b8-46f9-bf34-999f28caa817?message=647a5f60-3ca8-44a3-a64f-378ee2779c09).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2290#discussion_r922426653"}}
{"title": "Admin web sorts users by last viewed", "number": 2291, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2291"}
{"comment": {"body": "> MAX_PEOPLE is 100 so we'll need to solve when we hit that :).\r\n\r\nyeah, we need data tables. on my backlog\r\nhttps://datatables.net", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2291#issuecomment-1183684811"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2291#pullrequestreview-1037983018", "body": "MAX_PEOPLE is 100 so we'll need to solve when we hit that :)."}
{"title": "Follow up emails for welcome", "number": 2292, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2292", "body": "Since sendgrid is being a fucking PITA, fuck it, I'll just use amazon pinpoint.\nFuck you sendgrid."}
{"title": "Jeff/unb 433 sourcemark popover hidden under fold", "number": 2293, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2293", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2293#pullrequestreview-1038051042", "body": ""}
{"comment": {"body": "Flattening out scss to work with portal.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2293#discussion_r920565029"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2293#pullrequestreview-1038246967", "body": ""}
{"title": "Git blame on createDiscussion should filter out noreply emails", "number": 2294, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2294", "body": "per "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2294#pullrequestreview-1038052165", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2294#pullrequestreview-1038052249", "body": ""}
{"title": "Fetch all threads for web regardless of repoIDs", "number": 2295, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295", "body": "For dashboard, we do not want to pass repoIDs as part of mine/recommended/archived at requests.\nLarge orgs === lots of repos === long query parameter === 494 error \n\n"}
{"comment": {"body": "Address comments here: https://github.com/NextChapterSoftware/unblocked/pull/2299", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295#issuecomment-1184802611"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295#pullrequestreview-1038084112", "body": ""}
{"comment": {"body": "`all` is mutually exclusive with `childIds` -- maybe we should make `childIds` optional and use that as the trigger to query all?\r\n\r\nAs this is now, there could be multiple \"all\" streams, which doesn't make sense...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295#discussion_r920590859"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295#pullrequestreview-1038085514", "body": ""}
{"comment": {"body": "Not entirely exclusive. We still need the teamIDs that come from the childIds. We're ignoring *just* the repoIDs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295#discussion_r920591931"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295#pullrequestreview-1038086836", "body": ""}
{"comment": {"body": "In this key, the `teamId` already exists, above, it's not a part of the childIds at all.  The childIds *are* the repoIds.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295#discussion_r920592974"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295#pullrequestreview-1038087993", "body": ""}
{"comment": {"body": "It seems wrong that we're even depending on the repo store here (ie, repoTuples) -- I think this should be taking from the team store, and *those* should be driving this list of tuples `{ teamId: <teamId>, childIds: null/undefined }`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2295#discussion_r920593806"}}
{"title": "Add lambda tests", "number": 2296, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2296"}
{"title": "Ensure intercom only displays on one webview at a time", "number": 2297, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297", "body": "I finally cracked this thing, what a mess.\nThis code tries to restrict Intercom to only being active in a single webview at at time:\n* WebviewContentController now tracks a set of webviews that are willing to display intercom, and tracks a single \"primary\" intercom target.  This primary target is the only one that actually has Intercom enabled, so if a message is received, it only appears in one webview.\n* When webviews are created, destroyed, and go through visibility state changes, WebviewContentController recalculates who is the primary intercom target.\n* Attempting to open Intercom on a secondary intercom window forces that window to claim primary intercom target status.\n* As part of this, the individual flags where we specify which webviews use intercom or not has been moved from the root webview code (where we render each webview), into the extension, where we create the WebviewContentController.\nIntercom in web extensions and dashboard should not change at all.\nThis is probably quite fragile, and AFAICT it's pretty much impossible to write any tests for this, since it's largely based on built-in VSCode behaviour.  So, we'll have to be careful if this stuff is changed.\nLong term we may want to consider ditching Intercom, if any of the alternatives are better."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297#pullrequestreview-1039113688", "body": ""}
{"comment": {"body": "If we have a disabled state, what's the purpose of undefined?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297#discussion_r921321610"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297#pullrequestreview-**********", "body": ""}
{"comment": {"body": "It's mostly to provide a default so that every person using `IntercomProvider` doesn't have to explicitly enable it -- I'm open to removing this though if you think it's not worth having.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297#discussion_r921331379"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2297#pullrequestreview-**********", "body": ""}
{"title": "Admin web improvements", "number": 2298, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2298", "body": "Unread count nulls\nVersion breadcrumb\nRelease badge colours\nMobile responsiveness\nAdd teams to People page\nRemove sourcemark backfill migration\nMove sourcemarks to thread page\nMany style tweaks\nSearch for identities\nMore PR info (state, commit id, comment link)\n\n"}
{"comment": {"body": "The bresinator is on a tear \ud83e\udd23", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2298#issuecomment-1183973268"}}
{"comment": {"body": "I couldn't discern from the code... are there tooltip hints when you hover on the team icons?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2298#issuecomment-1183977494"}}
{"comment": {"body": "> I couldn't discern from the code... are there tooltip hints when you hover on the team icons?\r\n\r\nyup, works. (although the default tooltip hover delay is 1500ms)\r\n<img width=\"398\" alt=\"Screen Shot 2022-07-13 at 21 38 32\" src=\"https://user-images.githubusercontent.com/1798345/178899944-7ac35b74-8b68-4a17-8e80-cad9db947a31.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2298#issuecomment-1183979393"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2298#pullrequestreview-1038277610", "body": ""}
{"comment": {"body": "nice :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2298#discussion_r920740326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2298#pullrequestreview-1038280491", "body": ""}
{"title": "Refactor threads with better types", "number": 2299, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2299", "body": "Cleanup "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2299#pullrequestreview-1039143243", "body": ""}
{"comment": {"body": "Since we have separate types for these, can we rename `childIds` to be `repoIds` for mine/recomended/mineWithOpenPRs, and `prIds` for `prs`?  I think the way the typing below works, it should be possible, and would probably make all the behaviour here a lot clearer", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2299#discussion_r921341865"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2299#pullrequestreview-1039153492", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2299#pullrequestreview-1039353288", "body": ""}
{"comment": {"body": "Updated. Clearer now but adds more switch statements.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2299#discussion_r921484021"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2299#pullrequestreview-1039390840", "body": ""}
{"comment": {"body": "Looks good, now that I'm thinking about it we might want to take this all the way and have `ThreadStoreKey` be typed by request type as well, so that the API for this is entirely scoped and correct for every type of data, but this is good for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2299#discussion_r921510627"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2299#pullrequestreview-1039391131", "body": ""}
{"title": "Setup shared configs", "number": 23, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/23", "body": "Setup shared configs between VSCode & Web.\nMoved majority of config settings into a root SharedConfigs directory.\nThe equivalent Web & VSCode configs then import or reference their corresponding parent from the SharedConfigs directory.\nHope is to keep the two clients consistent in regards to styling and code conventions.\nMajority of the configs only allow you to reference file paths with the CLI. What they typically do is recursively move up the directories until it files a config file. aka we would need to place these shared config files at the root of the parent folder, not within a sharedConfigs folder. Therefore, when possible, updated the configs to JS and imported shared configs into individual config files."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/23#pullrequestreview-846197339", "body": ""}
{"comment": {"body": "I'm guessing you did this to reduce storybook spam while you're messing with things?  Or did you do this intentionally?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/23#discussion_r779961807"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/23#pullrequestreview-846199699", "body": ""}
{"comment": {"body": "Something to consider -- these are fairly central to the web UI (none of these colours and styles are used in VSCode, for instance).  I'm not sure how much this matters, but should this only be in the web project?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/23#discussion_r779963672"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/23#pullrequestreview-846199805", "body": " awesome"}
{"title": "Fixes failed displayName parse issue", "number": 230, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/230"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/230#pullrequestreview-871214055", "body": ""}
{"comment": {"body": "add test for the null case?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/230#discussion_r798091933"}}
{"title": "Keeping dlam out of prod db", "number": 2300, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2300"}
{"comment": {"body": "Today is a sad day", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2300#issuecomment-1184647823"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2300#pullrequestreview-1039150214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2300#pullrequestreview-1039154020", "body": ""}
{"title": "Only show metrics from 2022-07-07", "number": 2301, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2301"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2301#pullrequestreview-1039191772", "body": "thanks Dave!"}
{"title": "Only count external teams", "number": 2302, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2302", "body": "Currently the total count includes internal teams. This PR fixes that.\n\nTotal users still includes internal users, so I'll need to fix that in a separate PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2302#pullrequestreview-1039885519", "body": ""}
{"title": "UNB-405 [Dashboard] Add user email settings", "number": 2303, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303", "body": "Email settings:\n\n\n\nRequired a bit of refactoring from the DiscussionThread UI to share the same styles\nAdded a Toggle component that leverages the headlessui/Switch component \nA bit of refactoring to the LayoutContext for the new layout and breakpoints requested by Ben\nOther small layout nits"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303#pullrequestreview-1040583493", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303#pullrequestreview-1040606007", "body": ""}
{"comment": {"body": "So this means any UI that isn't explicitly wrapped in an overview/detail layout wrapper will display as a spinner?  Should we just pick one of them to be the default?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303#discussion_r922379680"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303#pullrequestreview-1040606578", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303#pullrequestreview-1040608841", "body": ""}
{"comment": {"body": "Until there are more UIs specced out and there's a clearer idea of what a default might be, I kind of like that you'll need to explicitly set the layout. It's a more intentional way to go about building out the UI I think", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303#discussion_r922381630"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303#pullrequestreview-1040620683", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303#pullrequestreview-1040648938", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2303#pullrequestreview-1040649167", "body": ""}
{"title": "Adds VersionInfoStore upsert", "number": 2304, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2304"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2304#pullrequestreview-1039292231", "body": ""}
{"title": "Move window creation to background", "number": 2305, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2305", "body": "Move Auth window creation back to background.\nHad been moved to Content script during refactor. Looks like creating a window from the content script triggers Safari's native Popup blocker. Not an issue in chrome.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2305#pullrequestreview-1039330486", "body": ""}
{"comment": {"body": "Added longer timeout periods to handle any jitter time in browser APIs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2305#discussion_r921467461"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2305#pullrequestreview-1039364534", "body": ""}
{"title": "UNB-439 Move title saving to input blur", "number": 2306, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2306", "body": "Issue was that the title was being updated on every key press causing the view to reload and remount, moving the cursor to the end \nChatted with Ben, can move the actual title updating to the blur event of the input so avoid any unnecessary behaviour while the user is typing"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2306#pullrequestreview-1039351141", "body": ""}
{"title": "Style the download error dialog", "number": 2307, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2307", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2307#pullrequestreview-1039464195", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2307#pullrequestreview-1039464978", "body": ""}
{"title": "Update timeout", "number": 2308, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2308", "body": "Update timeout to better support slow networks."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2308#pullrequestreview-1039566519", "body": ""}
{"title": "VSCodium Support", "number": 2309, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309", "body": "Summary\n\nAdd installer support to drop bits for both vscode and vscodium\nAdd hub support for app launch and foreground support for vscodium\n\nResolves UNB-436"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#pullrequestreview-1039507324", "body": "LGTM. Unfortunate that we can't unit test this post install"}
{"comment": {"body": "Just to be totally sure, I'd verify this works with multiple installed VS codes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#discussion_r921593500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#pullrequestreview-1039515911", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#pullrequestreview-1039521665", "body": ""}
{"comment": {"body": "Verified that it works with VSCode and Codium dropped into standard and non-standard directory. All locations on the system get the extension", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#discussion_r921600829"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#pullrequestreview-1039522341", "body": ""}
{"comment": {"body": "I meant multiple VSCodes to show that arrays get parsed correctly\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/6f193db5-0b29-4849-ae4d-5dfcc2b4abe2?message=78ffb447-20bb-47dd-a482-826a7d5b3d88).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#discussion_r921601319"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#pullrequestreview-1039526290", "body": ""}
{"comment": {"body": "Yup verified", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#discussion_r921604169"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2309#pullrequestreview-1039540471", "body": ""}
{"title": "fixbranches", "number": 231, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/231"}
{"title": "Add ability to copy filepath from dashboard", "number": 2310, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2310", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2310#pullrequestreview-1039525813", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2310#pullrequestreview-1039526006", "body": ""}
{"title": "Navigate to PR Comment instead of top", "number": 2311, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2311", "body": "Navigate directly to thread comment in PR instead of PR itself.\n\nFixes: unb-485"}
{"comment": {"body": "Nevermind.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2311#issuecomment-1184951560"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2311#pullrequestreview-1039549326", "body": ""}
{"title": "Fix: New repos added from GitHub are not getting created", "number": 2312, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2312", "body": "\nI think this is the general lesson to learn from this:\n1. for updates: it's better to use as small a transaction as possible while ensuring data integrity\n2. for inserts: it still makes sense to batch"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2312#pullrequestreview-1039563775", "body": ""}
{"comment": {"body": "This change has a surprisingly large impact on test performance due to conflicts and retries. The test runtime reduced from 28s to 18s locally.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2312#discussion_r921633105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2312#pullrequestreview-1039568467", "body": ""}
{"comment": {"body": "Makes sense, since there's a unique constraint and the tests run in parallel", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2312#discussion_r921636670"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2312#pullrequestreview-**********", "body": ""}
{"title": "Add support for release artifact polling", "number": 2313, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313", "body": "Introduced more config params for version management \nAdded function to S3 provider to help with reading manifest files from private paths e.g /builds \nModified makeObsolete and releaseToChannel method so they would take care of artifact moves before state changes \nAdded function to scan /builds directory and call ingestion on each existing artifact\nAdded pruneBuilds function to remove old artifacts based on cutoff specified in config. Currently we have this disabled in the background job until we add delete capability to versionStore\nAdded background job to run auto ingestion every 1 minute \nAdded tests to fully verify artifact release process locally using awslocalstack"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-**********", "body": ""}
{"comment": {"body": "We can enable this once we have introduced a delete method to versionStore. That way an artifact that has been ingested but never released can be removed both from DB and S3", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#discussion_r922345595"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040564084", "body": ""}
{"comment": {"body": "Added this for backwards compatibility so we could still retain our manual ingestion capabilities. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#discussion_r922346290"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040565291", "body": ""}
{"comment": {"body": "We have been double writing artifacts to both download-assets (old bucket) and the new bucket for a couple of days", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#discussion_r922346671"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040566697", "body": ""}
{"comment": {"body": "This is just a bit of future proofing for when/if we introduce other platforms", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#discussion_r922347133"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040567792", "body": ""}
{"comment": {"body": "Moves object within the existing bucket. Simplifies some of the other code. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#discussion_r922347458"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040573311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040662801", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040663577", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040667033", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040667102", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040671139", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040674280", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040674561", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040678213", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040678299", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040684068", "body": ""}
{"comment": {"body": "Use `Duration` and `Instant` APIs to compute time diffs:\r\n`val cutOffDate = Instant.now() - versionConfig.autoArchiveBuildsAfter`\r\n\r\nand\r\n\r\n`it.lastModified().toInstant() > cutOffDate`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#discussion_r922432413"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040684837", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040685730", "body": ""}
{"comment": {"body": "That was the lint problem. Instant is in Nano second so we were not able to find a way around it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#discussion_r922433623"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040687194", "body": ""}
{"comment": {"body": "Bummer - all good then", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#discussion_r922434647"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040688796", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040689744", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040690526", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040691494", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#pullrequestreview-1040718726", "body": ""}
{"comment": {"body": "I promise to remove it next week. I just don't want to be on the hook for a fire fight during onboarding :P ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2313#discussion_r922457032"}}
{"title": "Add webhook handler logging and fix org lookup bug", "number": 2314, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2314", "body": "fixes:\n  \n\n\nadds debugging to help with this:\n  "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2314#pullrequestreview-1039778849", "body": ""}
{"comment": {"body": "bug fix: passing installID instead of orgID \ud83e\udd26\u200d\u2642\ufe0f ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2314#discussion_r921800718"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2314#pullrequestreview-1039779041", "body": ""}
{"comment": {"body": "logging metadata in context", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2314#discussion_r921800870"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2314#pullrequestreview-1039780460", "body": ""}
{"title": "Basic sendInBlue implementation", "number": 2315, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2315"}
{"title": "Install repo on the right team", "number": 2316, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2316", "body": "We were installing incoming repo hook requests on the first repo found in the DB,\nregardless of the team. Turns out that the first repo happened to be a deleted team.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2316#pullrequestreview-1039822143", "body": ""}
{"comment": {"body": "We were installing incoming repo hook requests on the first repo found in the DB, regardless of the team. Turns out that the first repo happened to be a deleted team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2316#discussion_r921832459"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2316#pullrequestreview-1039823099", "body": ""}
{"comment": {"body": "Attempt to fix the logging context, because the fields are not showing up. I think it's due to the `launch`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2316#discussion_r921833163"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2316#pullrequestreview-1039863585", "body": ""}
{"comment": {"body": "yeah, this fixed it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2316#discussion_r921861830"}}
{"title": "Do not attempt to PR ingest disconnected repos", "number": 2317, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2317", "body": "Do not try to PR ingest when:\n- a repo is disconnected\n- a team is disconnected\n- a team is deleted"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2317#pullrequestreview-1040440707", "body": ""}
{"title": "Fix: Web extension shows duplicate identical source marks", "number": 2318, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2318", "body": "Fixes these duplicates:\n\n\n\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2318#pullrequestreview-1040872627", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2318#pullrequestreview-1040875035", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2318#pullrequestreview-1040881065", "body": ""}
{"comment": {"body": "I'm assuming all clients in the wild already pass this along?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2318#discussion_r922571510"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2318#pullrequestreview-1040881760", "body": ""}
{"comment": {"body": "They do, except in exceptional circumstances. but in those exceptional circumstances they are getting the wrong result anyway, so it needs to die.\n\n\n\nIf you look at the TS source for where this was called without a fileHash, you'll see that's it's wrapped in a catch block, so non-fatal.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/28f935c7-ccf9-4e17-b416-fc63fdb26659?message=edf03f62-7153-4375-931e-465a20a1c042).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2318#discussion_r922572014"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2318#pullrequestreview-1040884315", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2318#pullrequestreview-1040904971", "body": ""}
{"title": "Fix installer script", "number": 2319, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2319", "body": "Forgot about the goofy bash array expansion rules"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2319#pullrequestreview-1040509418", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2319#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This is the main change (edited)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2319#discussion_r922325034"}}
{"title": "Update Web & VSCode for LoginOptions API change", "number": 232, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232", "body": "Web was a pretty straight forward change.\nUpdated login page to render login button based on login providers.\nVSCode required larger changes as we only wanted to show relevant items within command palette.\nVSCode now has state aware commands related to auth.\nRemoved the need for custom codegen templates."}
{"comment": {"body": "@matthewjamesadam Feel free to leave feedback here. I'll address it in a subsequent PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-871380953", "body": ""}
{"comment": {"body": "This is pretty much temporary right?  I don't think we're likely going to be using actual commands for this stuff, we'll be integrating with a UI of some kind?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#discussion_r798224654"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-871382255", "body": ""}
{"comment": {"body": "Doesn't `expiresAt` change for other reasons (refreshing tokens?) -- should there be a separate isAuthed boolean that this kind of thing can run off of?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#discussion_r798225631"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-871382400", "body": ""}
{"comment": {"body": "Also, how is the lifetime of this subscription managed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#discussion_r798225750"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-871384651", "body": ""}
{"comment": {"body": "For now we could probably just ignore command visibility, and deal with this properly once the UI is defined...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#discussion_r798227466"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-871385747", "body": ""}
{"comment": {"body": "Oh this is a good candidate for a reusable react utility -- `useAsyncEffect`.  Otherwise we'll probably run into this in a bunch of places.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#discussion_r798228309"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-871387716", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-872145539", "body": ""}
{"comment": {"body": "That's fair. I wanted to integrate with how to the different states. Is useful to tell what the current state of auth is and was a good learning experience.\r\n\r\nI'm okay with leaving this in for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#discussion_r798777791"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-872149303", "body": ""}
{"comment": {"body": "> Doesn't expiresAt change for other reasons (refreshing tokens?) -- should there be a separate isAuthed boolean that this kind of thing can run off of?\r\n\r\nYeah. I was planning on revisiting this when we hook up the actual refresh logic. (Refreshing only occurs on setup right now). IMO, there should be a computedValue that's based on expiresAt existing. Will require writing/pulling in some middleware for Zustand to support this.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#discussion_r798780459"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-872154097", "body": ""}
{"comment": {"body": "> Also, how is the lifetime of this subscription managed?\r\n\r\nGood question. As of right now, this *doesn't* get cleaned up. This subscription will exist for the lifecycle of the store.\r\n\r\nIn this situation, I don't believe it needs to be cleaned up as we want this to exist for the lifespan of the authstore which is the lifespan of the application.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#discussion_r798783826"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-872156176", "body": ""}
{"comment": {"body": "Good point. I can write one up. Would be three states: loading, success, error.\r\n\r\nI wish we had enums with associated values... was much nicer in swift \ud83d\ude22 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#discussion_r798785235"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/232#pullrequestreview-872485087", "body": "Can we get this in, i'm blocked :)"}
{"title": "Expose reactions property on GitHub comments", "number": 2320, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2320", "body": "Just getting the ball rolling on syncing GitHub reactions.\nThis will help determine whether a GitHub comment has reactions. Next step is to call the reactions API for a comment with reactions, so that we can tell which user reacted (next PR)."}
{"comment": {"body": "> Next step is to call the reactions API for a comment with reactions, so that we can tell which user reacted (next PR).\r\n\r\nWorth looking into graphQL to see if we can get the comment + reactions + reactors in one shot?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2320#issuecomment-1185753490"}}
{"comment": {"body": "@richiebres we need commit ID and graphQL does not give us that so we'd still need to hit the V3 API", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2320#issuecomment-1185756958"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2320#pullrequestreview-1040606471", "body": ""}
{"title": "Get comment reactions from the GitHub API", "number": 2321, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2321", "body": "Adding logic here to suck down reactions+reactors for a comment. The intention here is to only call this for comments with reactions.\nNot used for now. We should only hit this API once we have everything in place to show reactions, since this will cost an API request."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2321#pullrequestreview-1040653096", "body": ""}
{"title": "Inline version ingest button", "number": 2322, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2322", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2322#pullrequestreview-1040738019", "body": ""}
{"title": "add more logging and s3 permissions", "number": 2323, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2323", "body": "Deployed changes to grant AdminWeb permissions on the new releases S3 bucket\nAdded two lines of log to know when ingestion jobs start and finish"}
{"comment": {"body": "Pushed a fix for S3 region. We were passing the wrong param and it was causing the following error: \r\n\r\n```\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:48)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:31)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:37)\r\n\t... 36 common frames omitted\r\nCaused by: java.net.UnknownHostException: s3.releases.dev.getunblocked.com.amazonaws.com\r\n\tat java.base/java.net.InetAddress$CachedAddresses.get(InetAddress.java:801)\r\n\tat java.base/java.net.InetAddress.getAllByName0(InetAddress.java:1509)\r\n\tat java.base/java.net.InetAddress.getAllByName(InetAddress.java:1367)\r\n\tat java.base/java.net.InetAddress.getAllByName(InetAddress.java:1301)\r\n\tat org.apache.http.impl.conn.SystemDefaultDnsResolver.resolve(SystemDefaultDnsResolver.java:45)\r\n\tat org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:112)\r\n\tat org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)\r\n\tat software.amazon.awssdk.http.apache.internal.conn.ClientConnectionManagerFactory$DelegatingHttpClientConnectionManager.connect(ClientConnectionManagerFactory.java:86)\r\n\tat org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)\r\n\tat org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)\r\n\tat org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)\r\n\tat org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)\r\n\tat org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)\r\n\tat org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)\r\n\tat software.amazon.awssdk.http.apache.internal.impl.ApacheSdkHttpClient.execute(ApacheSdkHttpClient.java:72)\r\n\tat software.amazon.awssdk.http.apache.ApacheHttpClient.execute(ApacheHttpClient.java:254)\r\n\tat software.amazon.awssdk.http.apache.ApacheHttpClient.access$500(ApacheHttpClient.java:104)\r\n\tat software.amazon.awssdk.http.apache.ApacheHttpClient$1.call(ApacheHttpClient.java:231)\r\n\tat software.amazon.awssdk.http.apache.ApacheHttpClient$1.call(ApacheHttpClient.java:228)\r\n\tat software.amazon.awssdk.core.internal.util.MetricUtils.measureDurationUnsafe(MetricUtils.java:63)\r\n...\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2323#issuecomment-1185888140"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2323#pullrequestreview-1040753823", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2323#pullrequestreview-1040769577", "body": ""}
{"title": "Email template", "number": 2324, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2324", "body": ""}
{"title": "fix bucket regions. Almost there", "number": 2325, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2325", "body": "Releases bucket is created in standard region and cli doesn't like it when we call it via us-west-2. Even though S3 is global!!\nException message:\nsoftware.amazon.awssdk.services.s3.model.S3Exception: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint. (Service: S3, Status Code: 301, Request ID: XJ8CJ5M1DFM4EKBG, Extended Request ID: igjjFyfCEyKlzDQMMA6KvDo8waU7rZZAzQK+SuFXAeOkMlmZYgGz0IT6oZHDJjaUIeVf6ZI/mHY=)\n    at software.amazon.awssdk.protocols.xml.internal.unmarshall.AwsXmlPredicatedResponseHandler.handleErrorResponse(AwsXmlPredicatedResponseHandler.java:156)\n    at software.amazon.awssdk.protocols.xml.internal.unmarshall.AwsXmlPredicatedResponseHandler.handleResponse(AwsXmlPredicatedResponseHandler.java:108)\n    at software.amazon.awssdk.protocols.xml.internal.unmarshall.AwsXmlPredicatedResponseHandler.handle(AwsXmlPredicatedResponseHandler.java:85)\n    at software.amazon.awssdk.protocols.xml.internal.unmarshall.AwsXmlPredicatedResponseHandler.handle(AwsXmlPredicatedResponseHandler.java:43)\n    at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler$Crc32ValidationResponseHandler.handle(AwsSyncClientHandler.java:95)\n    at software.amazon.awssdk.core.internal.handler.BaseClientHandler.lambda$successTransformationResponseHandler$7(BaseClientHandler.java:245)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.HandleResponseStage.execute(HandleResponseStage.java:40)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.HandleResponseStage.execute(HandleResponseStage.java:30)\n    at software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:73)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:42)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:78)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:40)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:50)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:36)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:81)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:36)\n    at software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n    at software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:56)\n    at software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:36)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.executeWithTimer(ApiCallTimeoutTrackingStage.java:80)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:60)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:42)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:48)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:31)\n    at software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n    at software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:37)\n    at software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:26)\n    at software.amazon.awssdk.core.internal.http.AmazonSyncHttpClient$RequestExecutionBuilderImpl.execute(AmazonSyncHttpClient.java:193)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.invoke(BaseSyncClientHandler.java:103)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.doExecute(BaseSyncClientHandler.java:167)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:82)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:175)\n    at software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:76)\n    at software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)\n    at software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:56)\n    at software.amazon.awssdk.services.s3.DefaultS3Client.listObjectsV2(DefaultS3Client.java:6430)\n    at com.nextchaptersoftware.aws.s3.StandardS3Provider.listObjects(S3Provider.kt:38)\n    at com.nextchaptersoftware.aws.s3.S3Provider$DefaultImpls.listObjects$default(S3Provider.kt:18)\n    at com.nextchaptersoftware.version.VersionService.scanForBuilds(VersionService.kt:167)\n    at com.nextchaptersoftware.adminwebservice.jobs.BuildIngestionJob.run(BuildIngestionJob.kt:16)\n    at com.nextchaptersoftware.service.PollingBackgroundJob$run$2$2$1$1.invokeSuspend(PollingBackgroundJob.kt:46)\n    at com.nextchaptersoftware.service.PollingBackgroundJob$run$2$2$1$1.invoke(PollingBackgroundJob.kt)\n    at com.nextchaptersoftware.service.PollingBackgroundJob$run$2$2$1$1.invoke(PollingBackgroundJob.kt)\n    at com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt$withSpan$2.invokeSuspend(SpanCoroutine.kt:27)\n    at com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt$withSpan$2.invoke(SpanCoroutine.kt)\n    at com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt$withSpan$2.invoke(SpanCoroutine.kt)\n    at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:89)\n    at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:169)\n    at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)\n    at com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt.withSpan(SpanCoroutine.kt:26)\n    at com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt.withSpan(SpanCoroutine.kt:47)\n    at com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt.withSpan(SpanCoroutine.kt:75)\n    at com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt.withSpan$default(SpanCoroutine.kt:69)\n    at com.nextchaptersoftware.service.PollingBackgroundJob$run$2.invokeSuspend(PollingBackgroundJob.kt:42)\n    at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)\n    at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)\n    at kotlinx.coroutines.internal.LimitedDispatcher.run(LimitedDispatcher.kt:42)\n    at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:95)\n    at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:570)\n    at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:749)\n    at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:677)\n    at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2325#pullrequestreview-1040797220", "body": ""}
{"title": "Fixing path prefixes for real s3", "number": 2326, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2326", "body": "Looks like path prefixes on real S3 might be different than localstack when it comes to leading slash. Right now scans run but they don't pick up on any artifacts. This might fix it"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2326#pullrequestreview-1040855772", "body": ""}
{"title": "UserId for emails", "number": 2327, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2327"}
{"title": "Add s3 bucket to local stack", "number": 2328, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2328"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2328#pullrequestreview-1040882869", "body": ""}
{"title": "Admin web data tables", "number": 2329, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2329", "body": "All tables are now sortable.\n\n\nFuture: Can do much more with DataTables, like pagination and search; but it didn't work well so I disabled for now until I can get it working properly.\n\n\n"}
{"title": "Create auth tokens for GitHub app S2S auth", "number": 233, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/233"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/233#pullrequestreview-871229321", "body": ""}
{"comment": {"body": "Next - I'm eventually going to change our own auth token generation to use asym key pairs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/233#discussion_r798103644"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/233#pullrequestreview-871240828", "body": "nice!\ncouple of suggested renames"}
{"title": "Don't include internal users in total users count", "number": 2330, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2330", "body": "Fixes this number so that it doesn't include persons that are a member of an internal team"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2330#pullrequestreview-1041161471", "body": ""}
{"title": "Tear down video service as it is not used currently", "number": 2331, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2331", "body": "Immediate motivation for doing this is to reduce Honeycomb EPM.\n"}
{"title": "update", "number": 2332, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2332"}
{"title": "UseSandboxedIntercomeEnvironment", "number": 2333, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2333", "body": "Rather than polluting the production intercom app, I'm using a sandboxed test app nested underneath our main app.\nScrewing around with intercom gives me the heepie jeepies when it's with production.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2333#pullrequestreview-1042506215", "body": ""}
{"title": "Add checkbox to select/unselect all in create thread", "number": 2334, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2334", "body": "extension:\n\nAlso fix color of the invite team member icon"}
{"comment": {"body": "The positioning of the checkbox here is maybe a bit confusing -- at first I thought it was a checkbox associated with the 'Invite specific team members' label, ie that you check the box to invite specific team members, and this somehow had to do with enabling the person checkboxes below.  I'm not sure how it could be made more obvious that this is a 'check/uncheck all' checkbox", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2334#issuecomment-1188334848"}}
{"comment": {"body": "How does this feel? The label should probably say Select all, or Unselect all depending on the state of the checkbox.  @kaych @dennispi \r\n<img width=\"361\" alt=\"CleanShot 2022-07-18 at 15 30 37@2x\" src=\"https://user-images.githubusercontent.com/13353189/179628199-1ab8d285-942a-49b4-b9c8-2f9fe6de7aee.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2334#issuecomment-1188391611"}}
{"comment": {"body": "This is what it looks like in the default light theme. We may need to run this through a few different themes to finalize the color of that row. @kaych \r\n<img width=\"357\" alt=\"CleanShot 2022-07-18 at 15 34 30@2x\" src=\"https://user-images.githubusercontent.com/13353189/179628606-254492a7-0a2d-4a91-b14c-503b97b50ef5.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2334#issuecomment-1188392204"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2334#pullrequestreview-1042565587", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2334#pullrequestreview-1042585731", "body": ""}
{"title": "Extend auth token expiry to 7 days", "number": 2335, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2335"}
{"comment": {"body": "> Makes me uncomfortable but if the boss wants it, sure\r\n\r\nI'm good with it for now. Requires a cross-site injection attack on an individual to pull the token. We can further harden with a strict domain policy on the session token", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2335#issuecomment-1188367036"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2335#pullrequestreview-1042596164", "body": "Makes me uncomfortable but if the boss wants it, sure"}
{"title": "Create PR ingestion model as part of org installation", "number": 2336, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2336", "body": "Reason we have not encountered this before is because we have always pre-installed the org,\nand org maintenance would have kicked in eventually."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2336#pullrequestreview-1042606645", "body": ""}
{"title": "Ingest prioritized repos first during onboarding", "number": 2337, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2337"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2337#pullrequestreview-1042606880", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2337#pullrequestreview-1042608863", "body": ""}
{"title": "Temporarily disable dev smoke tests", "number": 2338, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2338"}
{"title": "Skip for deleted teams", "number": 2339, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2339"}
{"comment": {"body": "hi", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2339#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2339#pullrequestreview-**********", "body": ""}
{"title": "IAM role setup for monitoring", "number": 234, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/234", "body": "Role for Grafana IAM User to consume CloudWatch metrics across accounts\nNetwork policy to allow Grafana agent running on Kubernetes to scrape Kube System Metrics pod\nMinor readme update"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/234#pullrequestreview-*********", "body": ""}
{"title": "Collect some logout debug info", "number": 2340, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340", "body": "Summary\n\nRemove ability for VSCode to log the hub out (this was not the intended behaviour, the Hub should rule the roost)\nAdds stack print when logout is invoked to help investigate unwanted logout events"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#pullrequestreview-**********", "body": ""}
{"comment": {"body": "fwiw, I don't believe VSCode is actually calling this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#discussion_r923964389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#pullrequestreview-**********", "body": ""}
{"comment": {"body": "How is the token trashed?\r\n\r\nUser manually logging out? Any other possibilities? If so, may be worth logging the actual location the token is trashed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#discussion_r923964798"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#pullrequestreview-1042693797", "body": ""}
{"comment": {"body": "We have similar logs in TSClients that log the reason for logging out including when a token was trashed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#discussion_r923964926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#pullrequestreview-1042693892", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#pullrequestreview-1042694599", "body": ""}
{"comment": {"body": "This code contains a stack logger to try and figure that out", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#discussion_r923965511"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#pullrequestreview-1042694785", "body": ""}
{"comment": {"body": "I thought not - which is good. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2340#discussion_r923965662"}}
{"title": "VSCode 'Current File' sidebar panel v1", "number": 2341, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341", "body": "First cut for the 'Current File' sidebar in VSCode:\n\nTextEditorSourceMark now publishes a stream of the threads it has loaded\nTextEditorSourceMarkManager publishes the stream of loaded threads for whichever file has focus\nSidebarWebviewProvider subscribes to this stream, pushes the data to the webview\nSidebar renders the new 'current file' data.\n\nNote:\n* This doesn't add anything to the main file explorer UI\n* The webview doesn't display 'loading' or 'no threads' state.  Will add in a follow-on PR.\n* There is no resizing in the bar above the view.  Will add in a follow-on PR.\n* Because of this, the UI is disabled for now.\n"}
{"comment": {"body": "> It might be helpful to add a `title` tag to the `Current File` text to show the file name to disambiguate (i.e. if a user has multiple editor panels open)\r\n\r\nYep, that is coming in a follow-on PR where I implement more of the content in the panel properly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#pullrequestreview-**********", "body": ""}
{"comment": {"body": "NB this centres the spinner properly, without this the spinner caused overflow to not work", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#discussion_r923961023"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#pullrequestreview-1042689231", "body": ""}
{"comment": {"body": "This is basically a custom `DiscussionPane` for the current file.  I will put the other custom UI (loading, no threads) in here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#discussion_r923961292"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#pullrequestreview-1042689640", "body": ""}
{"comment": {"body": "Use correct border/background for sidebar headers", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#discussion_r923961621"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#pullrequestreview-1044168407", "body": ""}
{"comment": {"body": "the", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#discussion_r925015097"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#pullrequestreview-1044169087", "body": ""}
{"comment": {"body": "Will stream.of send a complete event? If so, that's usually unexpected no?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#discussion_r925015609"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#pullrequestreview-1044169515", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#pullrequestreview-1044171389", "body": ""}
{"comment": {"body": "Huh. I'm not sure what happens to a `flatten`ed thread if it's sub-thread completes, I'm guessing completion is ignored when flattening, because this does seem to work fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#discussion_r925016583"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2341#pullrequestreview-1044206351", "body": "It might be helpful to add a title tag to the Current File text to show the file name to disambiguate (i.e. if a user has multiple editor panels open)"}
{"title": "Fix thread mine url from web clients and vscode", "number": 2342, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2342", "body": "This basically causes onboarding to hang because poller will fail to update threads when a new org is onboarding (pull request ingestion)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2342#pullrequestreview-1042694102", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2342#pullrequestreview-1042694946", "body": ""}
{"title": "Use search API to get priority pull requests", "number": 2343, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2343", "body": "We're currently shunting pull requests to the priority queue as we page over the pull requests for a repo. In the worst case, the onboarding user may only have pull requests on the later pages.\nWe can do better: use the search API to ask for the onboarding customer's PRs and add those to the priority queue, then page over all pull requests to put onto the regular queue.\nWe need to use the user-to-server token here because this search query requires repository access (search query containing repo:OWNER/REPONAME) and our regular token does not have that."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2343#pullrequestreview-1043802248", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2343#pullrequestreview-1043817235", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2343#pullrequestreview-1044072206", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2343#pullrequestreview-1044080528", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2343#pullrequestreview-1044083558", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2343#pullrequestreview-1044092947", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2343#pullrequestreview-1044225009", "body": ""}
{"title": "Disable auto-ingestion in admin console", "number": 2344, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2344", "body": "Ingestion is borked. Dev and Prod appear to be racing each other somehow"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2344#pullrequestreview-1043685616", "body": ""}
{"title": "Fix crash (502) in unreads section of thread page", "number": 2345, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2345", "body": "j.l.IllegalStateException: You can't change tag attribute because it was already passed to the downstream\n    at k.h.c.DelayedConsumer.onTagAttributeChange(delayed-consumer.kt:16)\n    at k.h.i.DelegatingMap.put(delegating-map.kt:27)\n    at k.h.i.DelegatingMap.put(delegating-map.kt:5)\n    at c.n.a.a.c.AvatarKt.avatar(Avatar.kt:16)\n    at c.n.a.a.p.ThreadPageKt.renderMessages(ThreadPage.kt:401)\n    at c.n.a.a.p.ThreadPageKt$renderThreadPage$2$2.invoke(ThreadPage.kt:113)\n    at c.n.a.a.p.ThreadPageKt$renderThreadPage$2$2.invoke(ThreadPage.kt:98)\n    at i.k.s.h.Placeholder.apply(Template.kt:29)\n    at i.k.s.h.TemplateKt.insert(Template.kt:82)\n    at c.n.a.a.t.ContentTemplate.apply(ContentTemplate.kt:37)\n    at c.n.a.a.t.ContentTemplate.apply(ContentTemplate.kt:18)\n    at i.k.s.h.RespondHtmlTemplateKt$respondHtmlTemplate$2.invoke(RespondHtmlTemplate.kt:21)\n    at i.k.s.h.RespondHtmlTemplateKt$respondHtmlTemplate$2.invoke(RespondHtmlTemplate.kt:21)\n    at i.k.s.h.HtmlContent.writeTo(RespondHtml.kt:60)\n    at i.k.s.p.c.CompressedWriteResponse$writeTo$2.invokeSuspend(Compression.kt:181)\n    at i.k.s.p.c.CompressedWriteResponse$writeTo$2.invoke(Compression.kt)\n    at i.k.s.p.c.CompressedWriteResponse$writeTo$2.invoke(Compression.kt)\n    at k.c.i.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:89)\n    at k.c.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:264)\n    at i.k.s.p.c.CompressedWriteResponse.writeTo(Compression.kt:179)\n    at i.k.s.e.BaseApplicationResponse$respondWriteChannelContent$2$1.invokeSuspend(BaseApplicationResponseJvm.kt:173)\n    at k.c.j.i.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)\n    at k.c.DispatchedTask.run(DispatchedTask.kt:106)\n    at k.c.i.LimitedDispatcher.run(LimitedDispatcher.kt:42)\n    at k.c.s.TaskImpl.run(Tasks.kt:95)\n    at k.c.s.CoroutineScheduler.runSafely(CoroutineScheduler.kt:570)\n    at k.c.s.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:749)\n    at k.c.s.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:677)\n    at k.c.s.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2345#pullrequestreview-1043813787", "body": ""}
{"title": "Fix build ingestion", "number": 2346, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2346", "body": "Revert VersionInfo upsert to insert"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2346#pullrequestreview-1043870484", "body": ""}
{"title": "ThreadInfo paging is cursor based only when there are more items", "number": 2347, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2347", "body": "Only use before cursor for paging through thread info collection when we know that there are more pages.\nMost of the changes are benign, just lifting the limit coersion to API layer.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2347#pullrequestreview-1043943031", "body": ""}
{"comment": {"body": "This is the fix.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2347#discussion_r924862262"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2347#pullrequestreview-1044011332", "body": ""}
{"comment": {"body": "Could put the switch statement inside the respond call to save but up to you", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2347#discussion_r924910528"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2347#pullrequestreview-1044046822", "body": ""}
{"title": "Add intercom client", "number": 2348, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2348"}
{"title": "Send slack message when new user/team signs up", "number": 2349, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349", "body": "\n\n\nIncoming webhooks are listed here:\n  \n\n\n\n\n\nNeed to deploy secrets."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1044102943", "body": ""}
{"comment": {"body": "This feels icky for some reason \ud83d\ude31", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r924972414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1044104950", "body": ""}
{"comment": {"body": "Can't think of a better way to do this, but feels weird. Meh", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r924973965"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1044106527", "body": ""}
{"comment": {"body": "\ud83d\ude02", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r924975202"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1044107703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1044126399", "body": ""}
{"comment": {"body": "yeah, it's gross. I'll have another go at it, maybe in follow up\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/06f2cac4-791c-45c5-97a4-167e209bcbd1?message=0a497e51-1565-41b2-84de-edfdcf5fe799).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r924990187"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1044132480", "body": ""}
{"comment": {"body": "agree. will try to improve\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/645bd6a6-7c14-44bb-8050-c870986c6d99?message=3142ad3e-c5a6-4bcc-8598-15576e13da6e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r924994358"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1046940696", "body": ""}
{"comment": {"body": "test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/645bd6a6-7c14-44bb-8050-c870986c6d99?message=264ba96f-3352-4d45-bdbb-114c52326405).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r926964260"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1047266004", "body": ""}
{"comment": {"body": "you man\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/645bd6a6-7c14-44bb-8050-c870986c6d99?message=fb38cd8e-383b-48aa-9a39-7252e55518b9).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r927199295"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1047266081", "body": ""}
{"comment": {"body": "fgdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/645bd6a6-7c14-44bb-8050-c870986c6d99?message=acf04c53-5629-4e73-a24e-5904f20f7825).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r927199342"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1047266157", "body": ""}
{"comment": {"body": "cab bcc\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/645bd6a6-7c14-44bb-8050-c870986c6d99?message=0b23c609-99c3-4237-b147-e28db797d66c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r927199401"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1047266320", "body": ""}
{"comment": {"body": "sdfd\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/645bd6a6-7c14-44bb-8050-c870986c6d99?message=5dbaad6f-bb01-4505-b388-fa7483bf34a3).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r927199573"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#pullrequestreview-1047266934", "body": ""}
{"comment": {"body": "gu\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/645bd6a6-7c14-44bb-8050-c870986c6d99?message=ed842508-0751-4c2e-bb4d-ae8dba3c3c0a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2349#discussion_r927200072"}}
{"title": "Add ability to update all service sub charts", "number": 235, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/235"}
{"title": "Add banner for installing extension", "number": 2350, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350", "body": "chrome:\n\nsafari:\n\n\nBanner falls back to the Chrome content as a default \nAdd Banner component\nRevamp Button styles per Ben's styleguide:\n\nAdd top level context provider for local storage -- this was necessary for the banner as we need the banner to show immediately after setting the token \nAdd helper to parse the useragent browser string (note that MDN recommends not doing this since the logic is brittle and the browsers can change its strings and break the logic pretty easily)\nFix some layout bugs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I'm guessing 'false' here means 'Don't use an icon, even if a variant is set' ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#discussion_r925835467"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#pullrequestreview-**********", "body": ""}
{"comment": {"body": "The way this works, `banner__with_icon` will still be set as a class even if the `icon` prop is false, but a `variant` is set, so an icon is not displayed.  I'm not sure if that's OK or not.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#discussion_r925836433"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#pullrequestreview-1045379602", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#pullrequestreview-1045386111", "body": ""}
{"comment": {"body": "Are we sure we want to make the behaviour different for the web client?  Would it make sense to move some of this into the base Dialog?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#discussion_r925849429"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#pullrequestreview-1045424855", "body": ""}
{"comment": {"body": "so we dont use the Modal/Dialog components in vscode, since we use the vscode system popup instead, so it'd only be the web-extension right now\r\n\r\nI think this makes sense because only the web dashboard has these customizations available, so I don't think it's that necessary to add them to the base class -- most of the other clients will leverage their own styles\r\n\r\nFWIW this only adds additional style args to the web buttons, the rest of the logic remains the same", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#discussion_r925877214"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#pullrequestreview-1045441634", "body": ""}
{"comment": {"body": "Updated so that the class is not always set, i.e. iconSrc can return false \r\n\r\n<img width=\"1675\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/180048088-0bedf327-5224-4517-8e61-a65cf875531b.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#discussion_r925888303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#pullrequestreview-1045705473", "body": ""}
{"comment": {"body": "I think this should be a `useRef`, not `useState`.  The difference is that whenever state is modified, the component is re-rendered, which we don't actually want here.\r\n\r\nIf you use `useRef`, then you'd use `.current` everywhere:\r\n\r\n```\r\nconst tokensMap = useRef<Map<string, string>>(new Map());\r\n\r\nconst getItem = (...) {\r\n  return tokensMap.current.get(key) ...\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#discussion_r926075824"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#pullrequestreview-1045707021", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2350#pullrequestreview-1045711897", "body": ""}
{"title": "Switch to copy and tagging for version releases", "number": 2351, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351", "body": "Changed the obsolete step to avoid moving files from releases to archived directory \nRemoved everything related to archived directory. Moving forward we would just keep things in release directory \nAdded support for adding and updating tags on S3 object. Mainly used to mark an artifact as obsolete \nModified the release step to copy artifacts from builds directory to releases instead of a move \nUpdated tests"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044076934", "body": ""}
{"comment": {"body": "We're still doing a \"move\" here since we're deleting the origin object?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#discussion_r924953902"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044078907", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044080564", "body": ""}
{"comment": {"body": "No the Move function is not being used. We do a copy now. Line 145 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#discussion_r924956512"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044082723", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044084758", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044090439", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044091775", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044093426", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044094134", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2351#pullrequestreview-1044098289", "body": ""}
{"title": "Disable", "number": 2352, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2352"}
{"comment": {"body": "@rasharab  Why?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2352#issuecomment-1189580281"}}
{"comment": {"body": "Timing based test.\r\nIt's problematic, we'll deal with it later.\r\nThe real crux of the issues is that somehow, these variant of tests are not passing on a regular basis.\r\nAnd I've noticed similar problems in other test packages where they do screwy stuff with lastModifiedAt to validate output.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2352#issuecomment-1189581157"}}
{"title": "Add client bits", "number": 2353, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2353"}
{"title": "Potential fix to turbo issues", "number": 2354, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354", "body": "Fix issues with source marks only rendering on initial load."}
{"comment": {"body": "This needs to be fixed: https://linear.app/unblocked/issue/UNB-451/fix-turbohack", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#issuecomment-1189605226"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#pullrequestreview-1044125634", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#pullrequestreview-1044126802", "body": ""}
{"comment": {"body": "SourceMarkRenderer has similar code, do we need to do the same thing there?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#discussion_r924990485"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#pullrequestreview-1044126919", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#pullrequestreview-1044127712", "body": ""}
{"comment": {"body": "Remove?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#discussion_r924991122"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#pullrequestreview-1044128580", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#pullrequestreview-1044130060", "body": "This stuff is getting pretty wild, I'm not sure I follow all of it anymore.  Once the dust has settled and we've figured out an approach that works we may want to document the code pretty thoroughly"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#pullrequestreview-1044138259", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#pullrequestreview-1044140793", "body": ""}
{"comment": {"body": "https://turbo.hotwired.dev/handbook/drive#disabling-turbo-drive-on-specific-links-or-forms", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2354#discussion_r925000126"}}
{"title": "Update select/unselect all checkbox styling", "number": 2355, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2355", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2355#pullrequestreview-1044134154", "body": ""}
{"title": "Reduce parallel", "number": 2356, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2356"}
{"title": "sendinblue secrets", "number": 2357, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2357"}
{"title": "Revert \"Reduce parallel (#2356)\"", "number": 2358, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2358", "body": "This reverts commit d9878cbf381c7c4474c6b74ce15a517d546552bd."}
{"title": "Fix flake", "number": 2359, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2359"}
{"title": "FixScaffoldFiles", "number": 236, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/236", "body": "Fix scaffolding\nUpdate"}
{"title": "Insight Bubble Button", "number": 2360, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360", "body": "Setup data source and injec button for insight button.\nEDIT Images and video below."}
{"comment": {"body": "@benedict-jw May want a loading state for the button.\r\n\r\nCurrently have it initialized to 0 and update when it gets data. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#issuecomment-1189862488"}}
{"comment": {"body": "TODO: Will look into animations.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#issuecomment-1189863852"}}
{"comment": {"body": "> @benedict-jw May want a loading state for the button.\n\nThere's one in the designs. \n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#issuecomment-1190366038"}}
{"comment": {"body": "Updated:\r\n\r\nhttps://user-images.githubusercontent.com/1553313/180094834-e22a4b3b-2293-49f2-8544-ef953eb51092.mp4\r\n\r\nhttps://user-images.githubusercontent.com/1553313/180095373-7efca339-d437-4e1d-bd6e-e7e981b089d8.mp4\r\n\r\n\r\n\r\n<img width=\"471\" alt=\"CleanShot 2022-07-20 at 14 28 45@2x\" src=\"https://user-images.githubusercontent.com/1553313/180094850-2928d066-b537-4ecb-bd0b-3f73f2cc3a2b.png\">\r\n<img width=\"464\" alt=\"CleanShot 2022-07-20 at 15 32 26@2x\" src=\"https://user-images.githubusercontent.com/1553313/180094849-67779636-140a-4899-a7ae-96ba12d8ab50.png\">\r\n<img width=\"554\" alt=\"CleanShot 2022-07-20 at 14 27 39@2x\" src=\"https://user-images.githubusercontent.com/1553313/180094857-55158800-b782-4884-b193-474a20eeb1f9.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#issuecomment-1190840429"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045365585", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045366000", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045366381", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045367042", "body": ""}
{"comment": {"body": "is there no stylelint for the web extension??? surprised it's not complaining about this bc I run into it all the time haha", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#discussion_r925835793"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045369230", "body": ""}
{"comment": {"body": "okay so it only does this when click navigating? should it also pin when the user starts scrolling?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#discussion_r925837377"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045370818", "body": ""}
{"comment": {"body": "Can we not use the `DialogHeader` component here?? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#discussion_r925838536"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045523477", "body": ""}
{"comment": {"body": "Nope. Before the user clicks, it should act as a standard dropdown.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#discussion_r925948352"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045693978", "body": ""}
{"comment": {"body": "This doesn't use the same mechanisms as dialog. Built from scratch to handle custom functionality and rendering.\nAlso styling is slightly different.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#discussion_r926066920"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045753404", "body": ""}
{"comment": {"body": "There is... What's wrong with this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#discussion_r926111437"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045765761", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045774446", "body": ""}
{"comment": {"body": "haha we're sloooowly filling in all the numbers.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#discussion_r926127940"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1045774921", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1046824582", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1046825358", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1046825536", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1046827162", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1047251658", "body": ""}
{"comment": {"body": "Haha yeah... This is necessary to get pixel perfect designs unfortunately.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#discussion_r927187935"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1047251914", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1048229002", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1048230168", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1048234847", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1048248806", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1048248945", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1048400124", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1048400747", "body": ""}
{"comment": {"body": "is this not the `<Loading />` component? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#discussion_r927991957"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1048401112", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2360#pullrequestreview-1048401506", "body": ""}
{"title": "WIP: trying to check ulimit values on builder machines", "number": 2361, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2361"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2361#pullrequestreview-1044263026", "body": ""}
{"title": "Limit priority jobs for each priority user", "number": 2362, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#pullrequestreview-1044404314", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#pullrequestreview-1044405008", "body": ""}
{"comment": {"body": "This line prevents enqueuing the same PR more than once.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#discussion_r925163496"}}
{"comment": {"body": "you can chain the `.take(N)` so that we don't need the `pullRequests` variable.\r\n\r\nalso make the chain a _sequence_, so that we avoid mapping / filtering over the entire collection unnecessarily ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#discussion_r925163845"}}
{"comment": {"body": "not following this bit. once we hit `enqueuedForIdentity` we should stop, but that's not the first conditional ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#discussion_r925166294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#pullrequestreview-1044405192", "body": ""}
{"comment": {"body": "What's this extra check for?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#discussion_r925163663"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#pullrequestreview-1044406190", "body": ""}
{"comment": {"body": "So that we don't go beyond the thirty, assumption being if it's not empty then we've already ingested the max", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#discussion_r925164433"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#pullrequestreview-1044409061", "body": ""}
{"comment": {"body": "crap youre right", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#discussion_r925166526"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#pullrequestreview-1044411339", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/2363", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2362#discussion_r925168121"}}
{"title": "Fix switch statement", "number": 2363, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2363"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2363#pullrequestreview-1044411307", "body": "perfect"}
{"title": "[Do not merge] Revert limiting priority jobs (just in case)", "number": 2364, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2364"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2364#pullrequestreview-1044416365", "body": ""}
{"title": "Add notification flow to onboarding", "number": 2365, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1044438369", "body": ""}
{"comment": {"body": "Literally the worst thing I've done in a long time. Making this async would require a huge amount of work because it doesn't jive well with SwiftUI's view builder. Could roll it into a publisher but then we'd get flickering artifacts. In reality this is a system call that returns at light speed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#discussion_r925187735"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1044439705", "body": ""}
{"comment": {"body": "The filth is glorious", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#discussion_r925188722"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045317471", "body": ""}
{"comment": {"body": "Might be worth a comment here explaining what you just told me in person, because otherwise it's not obvious why we're doing this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#discussion_r925802205"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045319416", "body": ""}
{"comment": {"body": "Eh this is fine.  It's contained in a small function that's easy to understand, not a big deal.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#discussion_r925803610"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045322003", "body": ""}
{"comment": {"body": "I don't understand what this is for?  Might be worth a comment?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#discussion_r925805524"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045329737", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045330112", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045334001", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045334168", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045339221", "body": ""}
{"comment": {"body": "I'll add a comment to the call-site to this function in `ContentView.swift`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#discussion_r925818058"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045343203", "body": ""}
{"comment": {"body": "Actually I added it to `openNotificationPreferences()` because we can get into the same situation from the preferences pane", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#discussion_r925820381"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2365#pullrequestreview-1045369627", "body": ""}
{"title": "Show onboardings on admin console", "number": 2366, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2366", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2366#pullrequestreview-1045037418", "body": ""}
{"title": "update intercom apis", "number": 2367, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2367"}
{"title": "Prioritize Initial Onboarding", "number": 2368, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2368", "body": "Currently call API.Persons.putOnboarding whenever the \"org installation\" step is shown.\nThis occurs in main \"onboarding\" page & the sidebar onboarding page.\nWe want to only call this for the main onboarding flow as these PRs should be prioritized for the tutorial.\nFYI, putOnboarding will be called with all the repos in VSCode, even if they have already been onboarded."}
{"comment": {"body": "Thanks @jeffrey-ng !", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2368#issuecomment-1190615682"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2368#pullrequestreview-1045488952", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2368#pullrequestreview-1045501150", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2368#pullrequestreview-1045502897", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2368#pullrequestreview-1045504460", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2368#pullrequestreview-1045509886", "body": ""}
{"title": "update templates", "number": 2369, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2369"}
{"title": "Disable CodeCov upload", "number": 237, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/237"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/237#pullrequestreview-871265587", "body": ""}
{"title": "Fix incorrect font size", "number": 2370, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2370", "body": "This broke one of the onboarding UIs a bit."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2370#pullrequestreview-1045498296", "body": ""}
{"title": "Don't trim thread titles", "number": 2371, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2371", "body": "As requested by KCH"}
{"comment": {"body": "what's the idea?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2371#issuecomment-1190676136"}}
{"comment": {"body": "@kaych ^?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2371#issuecomment-1190831352"}}
{"comment": {"body": "I think the idea is that the clients will be responsible for clipping the titles depending on the individual client UIs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2371#issuecomment-1190844324"}}
{"comment": {"body": "Sounds like the client change need to be in place and deployed to user first?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2371#issuecomment-1191006178"}}
{"comment": {"body": "The clients already handle/truncate the title strings. AFAIK we were sending the full strings before, trimming it was a recent regression.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2371#issuecomment-1191086313"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2371#pullrequestreview-1047340160", "body": ""}
{"comment": {"body": "Agree that we should remove limit if client is doing so, but just incase somebody dumps a 1MB comment (it'll happen) should we limit to something reasonable like idk 2K?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2371#discussion_r927256859"}}
{"title": "Lower Github rate limit", "number": 2372, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2372"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2372#pullrequestreview-**********", "body": ""}
{"title": "Drop threshold to 500", "number": 2373, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2373"}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/2372", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2373#issuecomment-**********"}}
{"title": "Fix build", "number": 2374, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2374", "body": "sorry "}
{"title": "Address team members with no accounts", "number": 2375, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2375", "body": "Longer standing problem is that this stuff is not apparently clear.\nAs of now we have:\n1. Identities\n2. Persons\n3. TeamMembers\n4. Teams\nAmongst those we have:\n1. Teams that are deleted\n2. Team Members that are not part of the team.\n3. Team Members that have identities but do not have persons.\nNot sure what else I'm missing, but this rabbit hole is getting bigger...\nWe need to encapsulate this sort of logic into some sort of Kotlin DSL pattern."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2375#pullrequestreview-**********", "body": ""}
{"comment": {"body": "nice", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2375#discussion_r926119593"}}
{"title": "Remove intercom from doepicshit landing page", "number": 2376, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2376", "body": "Remove Intercom from , but keep it in the download / extensions pages."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2376#pullrequestreview-1045773996", "body": ""}
{"title": "Update button styles", "number": 2377, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2377", "body": "Old\n\nButton Changes\n\nOn Disclosure Hover\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2377#pullrequestreview-1045773430", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2377#pullrequestreview-1045789443", "body": ""}
{"title": "VSCode 'Current File' panel, v2", "number": 2378, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378", "body": "Updates to VSCode 'Current File' panel:\n\nSet correct title\nAdd resizing bar so the Current File panel can be resized\nAdd loading and \"no threads\" states"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#pullrequestreview-1046943345", "body": ""}
{"comment": {"body": "can you extract these percentage values into variable consts and/or add a comment explaining exactly what the percentages represent? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#discussion_r926966217"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#pullrequestreview-1046943795", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#pullrequestreview-1046944740", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#pullrequestreview-1048202434", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#pullrequestreview-1048202516", "body": ""}
{"comment": {"body": "Done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#discussion_r927857859"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#pullrequestreview-1059278254", "body": ""}
{"comment": {"body": "@unblocked-app[bot] @unblocked-app[bot]\n\n--\n\nComment edited using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a7fc52e4-8801-4155-bbf1-5889a61662df?message=7ac7f843-5633-4150-8ba6-5b94e54f72e7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#discussion_r935931078"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#pullrequestreview-1059502202", "body": ""}
{"comment": {"body": "test\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/a7fc52e4-8801-4155-bbf1-5889a61662df?message=26fec6ca-fcf1-42dd-a315-fc70bf193b1f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2378#discussion_r936083417"}}
{"title": "Add loading view to dashboard", "number": 2379, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2379", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2379#pullrequestreview-1045785320", "body": "Nice! Got to get this into other places as well.\nPotentially could also use 3rd party libraries such as  which may help with adding new views?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2379#pullrequestreview-1046761792", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2379#pullrequestreview-1046835573", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2379#pullrequestreview-1046836311", "body": ""}
{"title": "Make incremental builds smart with openapi generation", "number": 238, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/238", "body": "Openapi tasks are dumb as shit when it comes to deleting generated output directories.\nWe now have added a custom openapi incremental cleanup task that monitors change to a spec file, and if there's a change, it will wipe out the generated directory.\nMultiple people have complained about problems with stagnant types lying around so this fixes that.\nExecuting incrementally\nMODIFIED: private.yml\nDeleting openapi generated directory: /Users/<USER>/chapter2/unblocked/apiservice/build/generated because spec file has changed: /Users/<USER>/chapter2/unblocked/api/private.yml"}
{"comment": {"body": "> Openapi tasks are dumb as shit\n\nYou had me at hello", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/238#issuecomment-1028551677"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/238#pullrequestreview-871316807", "body": ""}
{"title": "make templates more gmail friendly", "number": 2380, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2380"}
{"title": "Fix Exception in onboarding caused by git log", "number": 2381, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381", "body": "use git user information for this repo\nlimit use of log\nremove ls-files, which is unbounded\nremove use of exec because it does not handle shell escaping properly\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1045814093", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1045816477", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1045816575", "body": "With comment.s"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1045837079", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1045899105", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1046820494", "body": ""}
{"comment": {"body": "This code was here on purpose -- we ran git to ensure it actually worked, I don't remember the exact scenario where this failed but it happened once.  Maybe the scenario where git isn't installed (ie on a blank Mac, where 'git' is a stub that loads the command line tools)...\r\n\r\nAnyways, this is probably fine for now.  When we strip out the git extension we'll have to rewrite this anyways, because we'll need to do more elaborate manual finding/verification of the git binary...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r926879330"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1046828621", "body": ""}
{"comment": {"body": "I'll revert, thought it was dead code \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/76efe21f-352c-4f8e-a408-2f2842bec4eb?message=262c3094-93e2-4ed9-aa55-83e7b8198d1f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r926885236"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1046830402", "body": ""}
{"comment": {"body": "To be honest I'm not sure that it's really accomplishing much at this point, so feel free to rip it out if you want.  I don't think the error is actually shown in a UI nicely, it just generates a log that we could identify.  So the value is pretty minimal.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/76efe21f-352c-4f8e-a408-2f2842bec4eb?message=bac91e63-a775-456f-973e-63245e77514f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r926886459"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1046848053", "body": ""}
{"comment": {"body": "ok.\n\n\n\nin the happy case this call is just adding latency on every git call.\n\n\n\nin the sad case the exception is not much of an improvement.\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/76efe21f-352c-4f8e-a408-2f2842bec4eb?message=d6906449-9927-4e3c-955a-30fb3dd40e88).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r926898653"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047203618", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047204067", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047204982", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047233067", "body": ""}
{"comment": {"body": "Would it make sense to make the number an optional arg that defaults to 10 \r\n\r\nie \r\n```\r\nrecentFilesForAuthor(repoRoot: string, names: string[], limit?: = 10)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927172758"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047233128", "body": ""}
{"comment": {"body": "What does this do? Remove?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927172810"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047233636", "body": ""}
{"comment": {"body": "Do we not want to prioritize showing the readme file first if it exists in the list?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927173263"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047233865", "body": ""}
{"comment": {"body": "Do we know what the limits are on the stdout buffer size (the bug that originally started this?) -- I'm wondering if 1000 results might be enought to trigger it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927173457"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047234449", "body": "Looks good"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047235962", "body": ""}
{"comment": {"body": "```node\r\n> ['foo', '', 'bar'].filter(i => i)\r\n[ 'foo', 'bar' ]\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927175126"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047236056", "body": ""}
{"comment": {"body": "no need right now, but can in future", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927175214"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047236398", "body": ""}
{"comment": {"body": "we can't, unless we exhaustively search all files, which we can't do for performance reasons on large repos like `expo/expo`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927175537"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047236824", "body": ""}
{"comment": {"body": "It's much larger than 1000, since our repo has 26911 commits", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927175898"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047246682", "body": ""}
{"comment": {"body": "We have a util for that, `ArrayUtis.compact`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927183928"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#pullrequestreview-1047247396", "body": ""}
{"comment": {"body": "\"Returns an array with undefined/null values removed\" -- so wouldn't work in this case.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a8531ded-3fcb-444b-b99b-c12692686d94?message=ac4ba5a0-0eeb-4401-a47d-63a9eb55dcb8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2381#discussion_r927184508"}}
{"title": "Fix for Unrecognized hub thread action errors", "number": 2382, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2382", "body": "Fixes spam errors.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2382#pullrequestreview-1045899904", "body": ""}
{"title": "Fix thread unread count and partition team members", "number": 2383, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2383"}
{"title": "Admin web improvements", "number": 2384, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2384", "body": "cleanup person page\nshow onboarding install intent time\ncleanup stats page\nshow datatable footer info"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2384#pullrequestreview-1046045512", "body": ""}
{"title": "Clean up sendinblue", "number": 2385, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2385"}
{"title": "Allow Notifications banner when notifications turned off", "number": 2386, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386", "body": "\n"}
{"comment": {"body": "Use of \"since\" is wrong:\r\n- you cannot say \"since a _relative time_\"\r\n- you cannot say \"since a _duration_\"\r\n\r\n\r\nAlternatives:\r\n- you can say \"since a _time_\"\r\n  - \"since 9:05 am\"\r\n  - \"since yesterday\"\r\n- you can say \"in the last _duration_\"\r\n  - \"in the last 2 minutes\"\r\n  - \"in the last 4 days\"", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#issuecomment-1191818849"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047219263", "body": ""}
{"comment": {"body": "Did you mean to use `isDateInTHisWeek` here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927162014"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047219760", "body": ""}
{"comment": {"body": "`numberOfWeeksBetween` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927162373"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047221899", "body": ""}
{"comment": {"body": "This will not really be accurate, if you have more unreads then the clipped number of threads we display...  not sure if this matters or not.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927164056"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047222206", "body": ""}
{"comment": {"body": "We have the same issue in the dashboard (the \"unread\" pill).  The only efficient way to deal with this is to return the unread count as a part of the GET /threads/mine call.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927164325"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047222995", "body": ""}
{"comment": {"body": "Also, here we're adding (new + old) unreads -- isn't this the same as `unreads` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927164913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047224841", "body": ""}
{"comment": {"body": "That turns out to be a bit confusing because it's using the week bounds of \"monday - sunday\", meaning if the last missed event occured on sunday and it's monday then it would roll us into \"next week\", when really it should say \"in the last 2 days\".\r\n\r\nI'm going to remove the `isDateInThisWeek` function to remove the confusion", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927166250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047225817", "body": ""}
{"comment": {"body": "Just as a side note, this file is getting pretty out of hand -- you can see the problem that pop up where one change causes diff chaos throughout the file.  We should maybe start decomposing this a bit?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927167013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047226167", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047226600", "body": ""}
{"comment": {"body": "(Actually it's not the file that's out of hand so much as we've got a lot of stuff happening in one View tree.  They could be decomposed into mini views that exist in the same file, if they're all associated...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927167673"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-**********", "body": ""}
{"comment": {"body": "We're not quite adding new + old unreads, we're adding new + old missed notifications. It's a subtle difference to account for user interactions with the system settings, or modifying the unread status of threads out-of-band, such as through the dashboard. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927168423"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Agreed. I'll start pulling some of these components out into their own View files in a follow-up. There's generally quite a bit of code cleanup that needs to be done in the Hub.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927169270"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Is this a comment to capture the \"weeks\" computation in a variable?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927170425"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047230810", "body": ""}
{"comment": {"body": "No there seemed to be a helper method below to calculate this, which we weren't using and though the intention might have been to use it.  I get the difference between calendar weeks and (days/7) weeks though so we can probably just remove the helper below.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/301a0108-17b5-4a00-ad74-2caad8b22f96?message=b1390d77-0e8f-4352-9ad0-309bae2e1979).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927170974"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047239824", "body": ""}
{"comment": {"body": "Ah I think I see an issue now.\r\n\r\nLet's say I received multiple notifications for a single thread. Now instead of saying:\r\n\r\n`You've missed 1 discussion today`\r\n\r\nit will say\r\n\r\n`You've missed N discussions today`\r\n\r\nMissed discussions is probably an analogue for read status?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927178337"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#pullrequestreview-1047353653", "body": ""}
{"comment": {"body": "Test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/809ab02e-f618-4c9a-9d11-7819f9a3cb89?message=b6a5cc69-da4f-47ae-8f07-4c5760e45a11).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2386#discussion_r927266835"}}
{"title": "Source marks disappear from the entire repo with uncommitted changes", "number": 2387, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2387", "body": "Stupid typo. This needs tests so badly.\nThe impact of this bug was pretty awful:\n1. any uncommitted changes in the repo would cause all sourcemarks to disappear\n2. even without uncommitted changes, the rendered sourcemark line position was the original sourcemark, instead of the line position resolve for the HEAD commit.\n"}
{"comment": {"body": "Oooof! Thanks for catching this though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2387#issuecomment-1191935815"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2387#pullrequestreview-1047064372", "body": "Yikes"}
{"title": "UNB-466 [Onboarding] always close the sidebar and fix filtering logic", "number": 2388, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2388", "body": ""}
{"comment": {"body": "> What happens when none of the SourceMarks are 'ok'?\r\n\r\nRight now we fall back on the first thread in the list of all threads (i.e. it would return the most recent thread, regardless of resolution status). I suspect once we build up the read-only sm views that we can prioritize showing those first??\r\n\r\nEdit: Should note that even if the user happens to run into an unresolvable SM, they can still click through to the next onboarding view. Obviously we want to avoid this, but if they happen to run into this state, at the very least the onboarding can still proceed forward via the View Discussion button:\r\n<img width=\"1034\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/180313052-a1acb7df-7600-4914-b69d-f9d81fad9095.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2388#issuecomment-1191917509"}}
{"comment": {"body": "Makes sense. Agree that read-only view is important for this to work reliably.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-425/show-the-source-code-known-to-have-the-sourcemark-in-a-read-only", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2388#issuecomment-1191938033"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2388#pullrequestreview-1047096560", "body": "What happens when none of the SourceMarks are 'ok'?"}
{"title": "Animate notifications onboarding panel", "number": 2389, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2389", "body": "Using native GIF rendering. Seems to be ok"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2389#pullrequestreview-1050173256", "body": ""}
{"comment": {"body": "@matthewjamesadam without this, SwiftUI causes the `NSPopover` to do that weird rendering thing we saw previously. It looks like `VideoPlayer` has an underlying bug in whatever AppKit view it's using where autolayout is turned off. Some combination of `NSPopover` and the view controller stack we're using seems to require autolayout.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2389#discussion_r929375615"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2389#pullrequestreview-1051294650", "body": ""}
{"comment": {"body": "Update: I tried wrapping `AVPlayerView` to render in SwiftUI and it looks like the bug is pretty deep in AppKit. Settings layout properties on the player view doesn't fix the issue. Went the GIF route in the end", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2389#discussion_r930184466"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2389#pullrequestreview-1051608436", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2389#pullrequestreview-1051615837", "body": ""}
{"title": "Re-add login endpoint to bounce the browser and set cookies", "number": 239, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/239", "body": "Problem\nBecause /login/options is now an API call, there's no way to set the secret cookie, which is necessary to prevent XSS attacks. \nProposal\nRe-introduce the /login endpoint, and modify the /login/options response to contain urls that point to the /login endpoint. \nWhen browsers hit the /logon endpoint, the service will set the secret cookie and redirect the browser to the auth provider. \nThe flow now looks like this:"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/239#pullrequestreview-872138610", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/239#pullrequestreview-872167976", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/239#pullrequestreview-872168484", "body": ""}
{"comment": {"body": "toString?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/239#discussion_r798793676"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/239#pullrequestreview-872540443", "body": ""}
{"comment": {"body": "I've typed everything through as far as it can go without performing a string transformation to get input validation enforcement at the api level", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/239#discussion_r799062597"}}
{"title": "Dashboard bug fixes", "number": 2390, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2390"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2390#pullrequestreview-1047201424", "body": ""}
{"title": "Github App onboarding for web extension", "number": 2391, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391", "body": "Setup basic onboarding flow for web extension.\n\nTODO in subsequent PRs: \n\n"}
{"comment": {"body": "Going to merge this in first. Will follow up on any further comments in another PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#issuecomment-1193011316"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1047205527", "body": ""}
{"comment": {"body": "Pulled from VSCode RepoStore... We may want to refactor this out in the future.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927151445"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1047206037", "body": ""}
{"comment": {"body": "This new state was a major pain point.\r\nWe needed a way to represent \"no\" repos without throwing errors as that would kill the stream.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927151811"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1047213546", "body": ""}
{"comment": {"body": "Do not show sidebar on App installation page", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927157580"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1047216019", "body": ""}
{"comment": {"body": "RepoStore would normally call updateRepo on TeamStore change.\r\nIn this case where we are installing / configuring an existing org, TeamStore will not be updated.\r\n\r\nTherefore, we need to manually trigger updateRepo.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927159466"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1047217383", "body": ""}
{"comment": {"body": "We want two distinct sidebars in this one class based on the InstallationState stream.\r\n\r\nTherefore we have two child streams that split based on a filter on requiresInstallation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927160538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1047264376", "body": ""}
{"comment": {"body": "Added a filterMissingRepo stream operator to help cleanup the boilerplate code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927198003"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048214659", "body": ""}
{"comment": {"body": "is this something we want to standardize? Feels weird that we're adding this format as a one-off ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927866493"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048214994", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048216954", "body": ""}
{"comment": {"body": "is there a reason why we're doing this instead of margin css spacing? not saying it's wrong it just feels kind of antipattern to what we've been doing so far(??)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927868034"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048217554", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048245826", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048256244", "body": ""}
{"comment": {"body": "It's a good point. We need to go through the same standardization process I believe you and Ben did for dashboard?\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927894532"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048261472", "body": ""}
{"comment": {"body": "I realize this was taken from VSCode, but I don't understand why the lifetime for this stream (the Subscription) is owned by someone outside of this class.  I would expect that this class would subscribe to the team store while it is active, ie while its `stream` has any subscribers?\n\nMaybe something to look into after this lands, we should definitely merge the common logic between this and the VSCode RepoStore together, as they share a lot.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927898170"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048262316", "body": ""}
{"comment": {"body": "\r\nI was playing around with the padding / line break but found it strange that we were setting random padding values to the sides just to get a line break.\r\n\r\nThat's partially why in previous PRs I added so many random $spacer values into our constants...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927898712"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048267092", "body": ""}
{"comment": {"body": "This seems related to the previous comments you made about changing *how* the stream's lifecycle should be handled?\r\n\r\naka it should only do stuff when it has listeners (e.g. APIDataStream, etc...)\r\n\r\nI haven't thought enough about how to rewrite this with that pattern. Can we chat about this next week? Honestly not at my best right now... had/have a headache.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927902008"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048267619", "body": ""}
{"comment": {"body": "Oh yeah for sure, not a problem\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0771915c-8941-4028-96be-79e13862ddbe?message=d384012a-3e24-409e-84b6-0704a538d63a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927902342"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048375978", "body": ""}
{"comment": {"body": "is there a particular reason for this change? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927974406"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048376115", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048395646", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#pullrequestreview-1048397070", "body": ""}
{"comment": {"body": "Changed this to match the primer docs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2391#discussion_r927989091"}}
{"title": "Add streaming requests for pull requests and pull request comments", "number": 2392, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2392", "body": "Part of the effort to move towards bulk ingestion of pull requests."}
{"title": "Create redis cache for bulk ingested pull requests and review comments", "number": 2393, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2393", "body": "The idea is here is to allow quick caching of pull requests and comments during bulk ingestion. When individual pull request ingestion jobs are being processed, the cache can be checked and IFF there's a miss will we go out to the API.\nThis cache will also be written to by webhook handlers while we're undergoing bulk ingestion, so that we don't lose updates while an object is in the cache but not in the database.\nThis cache is designed to handle multiple versions of the same object written to the cache. The idea is to not evict on write (as that is slower and can't be atomic without some scripting as I understand) but instead just returns the latest version on read.\nEach key-value has a TTL of 1 week, which should give us plenty of time to write it to the database."}
{"comment": {"body": "We're going to try a version where we write objects directly to the database during bulk ingest", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2393#issuecomment-1194749830"}}
{"title": "Change discussion to messages in allow banner text", "number": 2394, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2394"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2394#pullrequestreview-1047251846", "body": ""}
{"title": "Filter deleted threads from gutter renderer", "number": 2395, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2395", "body": "This means that hard-deleted threads will no longer appear in the editor gutter, which matches the behaviour in the web extension"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2395#pullrequestreview-1047267264", "body": ""}
{"title": "Automate secrets deployment", "number": 2396, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2396", "body": "There are so many ways this can be fucked if we continue to do this manually.\nAutomation now, automation for life."}
{"title": "Admin web compare versions to generate release notes", "number": 2397, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2397", "body": "Very basic release note helper tool:\n1. Copy and paste Product SHAs from two versions into the from and to fields\n2. Click Compare which brings you to GitHub's commit compare view\n\n\n "}
{"title": "Optimize cache lookup performance of RepoResolver", "number": 2398, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2398", "body": "Eliminates 3 git calls everytime we update sourcemarks for a file.\n(update = load file, edit file, save file, or create thread in file)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2398#pullrequestreview-**********", "body": ""}
{"comment": {"body": "The longest matching Git repo path to account for submodules.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2398#discussion_r927278885"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2398#pullrequestreview-**********", "body": ""}
{"title": "re-enable auto-ingestion", "number": 2399, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2399"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2399#pullrequestreview-**********", "body": ""}
{"title": "Code block", "number": 24, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24", "body": "\nSetup Codeblocks with highlight.js\nMoved highlighting to web worker to avoid slowdowns when dealing with large chunks of code.\nWill start off initially with unhighlighted code and swap out highlighted code once ready."}
{"comment": {"body": "This looks good. In playing with it though, I think we need a max-height on these code snippets, with a reveal more/all to cap really long code blocks.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#issuecomment-1007779353"}}
{"comment": {"body": "Cool!\r\n\r\nOne thought: syntax hilighting hundreds of languages can't be cheap space-wise.  How much larger is the bundle with highlight.js added?  I'm wondering if this is something that the service should be doing, at least for the web.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#issuecomment-1007784169"}}
{"comment": {"body": "> \r\n\r\nNot sure what magic I did but \r\n\r\n main: 317KiB\r\nPR: 251KiB\r\n\r\nMight be related to some of the tsconfig changes I made?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#issuecomment-1007803260"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#pullrequestreview-*********", "body": ""}
{"comment": {"body": "This probably needs some comments?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#discussion_r780564232"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#pullrequestreview-*********", "body": ""}
{"comment": {"body": "This is effectively untyped, right?  This is the same issue as we ran into with the VSCode extension webview message passing.  We should probably eventually try to use a similar approach, where the message types are shared and some serialization/type-checking is done to ensure it's correct at runtime?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#discussion_r780565012"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#pullrequestreview-847053459", "body": ""}
{"comment": {"body": "In this case no. WorkerProps is actually used in both the worker & component.\r\n\r\nThe way it's enforced is that the react world interacts with a `HighlightWorker` which has an override for the postMessage, not a generic Worker.\r\n\r\nYou can work around this but there's some safety here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#discussion_r780571064"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#pullrequestreview-847058220", "body": ""}
{"comment": {"body": "Ah yeah OK, that works.  Can we type the messages going the other way (the hilighted text going back to the react view) as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#discussion_r780574907"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#pullrequestreview-847062795", "body": ""}
{"comment": {"body": "Added typing for onmessage which should be the full loop.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#discussion_r780578448"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#pullrequestreview-847063499", "body": ""}
{"comment": {"body": "Can we comment where this is used in the code?  Might help future engineers figure out why we have this, where it's used, and whether they can get rid of it or not...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#discussion_r780579002"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/24#pullrequestreview-847064442", "body": ""}
{"title": "Rename k8s secret to GITHUB_APP_KEY", "number": 240, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/240", "body": "Update global config environment variable name\nFix up readme"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/240#pullrequestreview-871371886", "body": ""}
{"title": "revert changes", "number": 2400, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2400"}
{"title": "Fix onboarding step", "number": 2401, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2401"}
{"comment": {"body": "Closing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2401#issuecomment-1192982965"}}
{"title": "Fix onboarding", "number": 2402, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2402"}
{"comment": {"body": "> Should all these fns just call the log fn on line 90 instead of Runner.run directly? That way we don't have to keep repeating the same filtering logic everywhere and we don't run the risk of missing adding this code somewhere\r\n\r\noh, I missed that. that's another unbounded call to git. I'll cleanup", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2402#issuecomment-1192812189"}}
{"comment": {"body": "@kaych going to clean in follow up. refactor is going to be quite large. let's get this in then I'll fix up?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2402#issuecomment-1192943612"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2402#pullrequestreview-1048223691", "body": ""}
{"comment": {"body": "You can definitely wrap this in `ArrayUtils.compact(ArrayUtils.distinct(...` I think the description of the utility fn just needs to be updated, `lodash.compact` removes all falsey values including empty strings ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2402#discussion_r927872728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2402#pullrequestreview-1048226890", "body": "Should all these fns just call the log fn on line 90 instead of Runner.run directly? That way we don't have to keep repeating the same filtering logic everywhere and we don't run the risk of missing adding this code somewhere"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2402#pullrequestreview-1048237971", "body": ""}
{"comment": {"body": "I'll move all these to the sourcemark GitRunner, which uses `RunnerResult`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2402#discussion_r927882483"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2402#pullrequestreview-1048447543", "body": "@kaych going to clean in follow up. refactor is going to be quite large. let's get this in then I'll fix up?\n\nIs it? I'm only suggesting consolidating the duplicated logic into one place?"}
{"title": "Don't show select checkbox if there are <1 contributors", "number": 2403, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2403", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2403#pullrequestreview-1048305375", "body": ""}
{"title": "UNB-469 Open activity bar at end of onboarding", "number": 2404, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2404", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2404#pullrequestreview-1048287154", "body": ""}
{"comment": {"body": "ignore all sidebar clicks for now, now that it is closed throughout onboarding", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2404#discussion_r927916144"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2404#pullrequestreview-1048304992", "body": ""}
{"comment": {"body": "Is this a temporary change? Should we just remove the code instead of commenting out?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2404#discussion_r927928219"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2404#pullrequestreview-1048305031", "body": ""}
{"title": "Enable campaigns for dev", "number": 2405, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2405"}
{"title": "Hide extension banner once extension is installed", "number": 2406, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2406"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2406#pullrequestreview-1048398472", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2406#pullrequestreview-1048398512", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2406#pullrequestreview-1048403447", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2406#pullrequestreview-1048404440", "body": ""}
{"comment": {"body": "FYI, this localStorage is on the content script and will apply to each domain.\r\n\r\naka github.com & getunblocked.com will each have their own storage.\r\nIf you want a single storage for the extension itself, regardless of domain, use the storageTransport.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2406#discussion_r927994726"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2406#pullrequestreview-1048405497", "body": ""}
{"title": "disable verbose", "number": 2407, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2407"}
{"title": "Allow for senders to be provided via email templates", "number": 2408, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2408"}
{"title": "Refactor to separate shell-Git and extension-plugin-Git and fix blame bugs", "number": 2409, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409", "body": "Moves Git functionality to Git-layer\n\n\nFixes bugs in Git blame:\n\nWe were only looking at the first line in range\nLine range was off by one, so we were blaming the wrong line\n\nShould have excluding whitespace and detecting moves\n\n\nWe were excluding Git identities that have a no-reply email. This is\n  fine for people that do not have unblocked accounts, but not for\n  people who have unblocked accounts.\n\n\nAdd tests\n\n\nRelated to:\n  "}
{"comment": {"body": "I will pay you money to get this pr in :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#issuecomment-**********"}}
{"comment": {"body": "sync up, fixed a conflict.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#issuecomment-**********"}}
{"comment": {"body": "Ahh dang.  For top-level modules, and module inter-dependencies, we try to use file mapping, that's why you don't see so many relative file references when importing in the TS code. Anyone referencing anything in the `git` module should use something like `@import {thing} from '@git'`.\r\n\r\nI can look into fixing this up later today if you're busy.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1049839603", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1049845107", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1049861462", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1049862840", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1049862995", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1049865039", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1049866196", "body": ""}
{"comment": {"body": "Thanks for separating this out -- we'll need to figure out the best way to replicate this stuff without the extension.  Some of it (like finding the git binary) we can just copy what is in the git extension itself...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#discussion_r929163980"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1049866948", "body": ""}
{"comment": {"body": "Yeah more work needed, but it's a start ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#discussion_r929164555"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1049873721", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2409#pullrequestreview-1050032842", "body": ""}
{"title": "Implements github install access token request", "number": 241, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/241", "body": "Implements two requests:\ninstallations() -> returns list of installs for github app (unpaginated for now, 100 results max)\ninstallationAccessToken(installationId: String) -> returns install access token for install specific queries \nI have not implemented the token cache yet. The GitHubAppClient is dumb and is not cache aware. I'll drop a caching layer in front of the client in GitHubApiImpl"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/241#pullrequestreview-871402225", "body": "nice!"}
{"title": "Add prCommentUrl to MessageLinks model", "number": 2410, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410", "body": "Right now there's only a PrComment model on the Thread object, meaning that the hyperlinks in each message UI always leads to the first comment of the pr comment thread. Each message should have its own PrComment model and further its own commentHtmlUrl.\n\nThere's a bit of client side work to migrate from using the deprecated fields in the prComment model."}
{"comment": {"body": "> Do you know at what point it is safe to get rid of this structure from the client perspective? https://linear.app/unblocked/issue/UNB-489/obsolete-threadprcomment-field\r\n> \r\n> <img alt=\"image\" width=\"1099\" src=\"https://user-images.githubusercontent.com/1798345/180847925-78ee27e5-174e-46f3-ba50-4be6ac80f03c.png\">\r\n\r\n@matthewjamesadam  or @pwerry might have a better answer, but I think it's basically after we have some confidence that all the existing clients before the build with this PR change are no longer in use(?) I'm not really sure how to measure that ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#issuecomment-1195767579"}}
{"comment": {"body": "> @matthewjamesadam or @pwerry might have a better answer, but I think it's basically after we have some confidence that all the existing clients before the build with this PR change are no longer in use(?) I'm not really sure how to measure that\r\n\r\nThere's no great way to answer this right now that doesn't involve quite a bit of manual work.  I think the correct workflow right now is:\r\n\r\n* Track which build is the last build # that used this API, for all clients\r\n* Give some time (2 weeks? I don't know) for people to upgrade\r\n* Look in the logs to verify that nobody is using any clients <= to those builds (for each client), for some amount of time (a week?)\r\n* Mark those builds as being unusable in our versioning system, so those clients stop working\r\n* Remove those deprecated properties from the API spec\r\n\r\nLots of manual work.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#issuecomment-1195779474"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1048429970", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1048443479", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1048444548", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1048467149", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1048467679", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1049772579", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1049774918", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1049777220", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1049878522", "body": ""}
{"comment": {"body": "If we're passing in firstMessage, we could remove this prop as well", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#discussion_r929172347"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1049879673", "body": ""}
{"comment": {"body": "Should this be `PR #` like the other clients?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#discussion_r929173105"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1049879956", "body": ""}
{"comment": {"body": "Same as above. PR #", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#discussion_r929173292"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1049880021", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1049884428", "body": "Do you know at what point it is safe to get rid of this structure from the client perspective?\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#pullrequestreview-1049884983", "body": ""}
{"comment": {"body": "No. On the web we format it differently so that we show the full title and the pr #\r\n<img width=\"836\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/180848019-f03120b4-bc7f-43bf-8315-609b8f7727f0.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2410#discussion_r929176841"}}
{"title": "NSCache makes Peter sad", "number": 2411, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2411", "body": "This PR dumps our use of NSCache for the data stores. There is migration logic so that we don't end up logging people out and trashing their data when they upgrade.\nThe code repetition is a bit hairy. I'm going to spend a morning next week DRYing up this codebase, including this tomfoolery"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2411#pullrequestreview-1049717606", "body": ""}
{"comment": {"body": "Would it be possible to have this migration code in a single file?\r\n\r\nLogic seems pretty similar across all the caches and would be simpler in terms of cleanup.\r\n\r\nNot necessary to do but just a thought.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2411#discussion_r929057652"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2411#pullrequestreview-1049755553", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2411#pullrequestreview-1049781339", "body": ""}
{"comment": {"body": "Yeah I do want to do this in a followup. The logic between files is just different enough that it's not a simple abstraction and may require a bit more refactoring. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2411#discussion_r929103725"}}
{"title": "Mark users as internal", "number": 2412, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2412", "body": "Dennis ask, i'm not doing this shit manually."}
{"title": "Secrets were being printed out during deployment", "number": 2413, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2413"}
{"title": "VSCode 'Current File' UI V3", "number": 2414, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2414", "body": "Final updates for this UI:\n\nAdd as a webview in the file explorer.  This was more involved then I expected, I had to refactor some of the existing UI, but the guts of the code is reused in the two views.\nAdd an animated image to the 'no threads in this file' state\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2414#pullrequestreview-**********", "body": ""}
{"comment": {"body": "I'd really like to find a different way to manage images inside of webviews -- this pattern is not awesome.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2414#discussion_r929068406"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2414#pullrequestreview-**********", "body": ""}
{"comment": {"body": "This was factored out of SidebarWebviewProvider, so that the sidebar and explorer current file panes can use the same handler logic.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2414#discussion_r929069299"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2414#pullrequestreview-**********", "body": ""}
{"title": "Update signature", "number": 2415, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2415", "body": "Changes it to:\n"}
{"title": "Admin web reorder team members so Unblocked users are shown first", "number": 2416, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2416"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2416#pullrequestreview-**********", "body": ""}
{"title": "Always check for updates", "number": 2417, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2417"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2417#pullrequestreview-**********", "body": ""}
{"title": "Enable sendinblue in production", "number": 2418, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2418"}
{"title": "Update web to use commentHtmlUrl", "number": 2419, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2419", "body": "Missed Web client for :https://github.com/NextChapterSoftware/unblocked/pull/2311"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2419#pullrequestreview-1049806836", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2419#pullrequestreview-1049807431", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2419#pullrequestreview-1049813616", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2419#pullrequestreview-1049814810", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2419#pullrequestreview-1049830345", "body": ""}
{"title": "TeamMembers API", "number": 242, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/242"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/242#pullrequestreview-872030014", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/242#pullrequestreview-872180018", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/242#pullrequestreview-872188077", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/242#pullrequestreview-872442537", "body": ""}
{"title": "Show upgrade regardless of onboarding status", "number": 2420, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2420"}
{"title": "Git utility to get file content at any revision", "number": 2421, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2421", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2421#pullrequestreview-1049856280", "body": ""}
{"title": "add download button for versions", "number": 2422, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422", "body": "\nAdds a download button to version page. Download links are generate using S3 signed urls valid for one hour."}
{"comment": {"body": "What's different between the download button and the link to the installer package?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#issuecomment-1194451816"}}
{"comment": {"body": "> What's different between the download button and the link to the installer package?\r\n\r\nWas going to make the exact same comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#issuecomment-1194453827"}}
{"comment": {"body": "Link to installer package would work only when something is released. This one works regardless of the state and downloads it directly from S3. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#issuecomment-1194460265"}}
{"comment": {"body": "The download link is a public address. \r\nUnreleased artifacts go into `builds/` s3 directory which is not visible to CloudFront. Only content of `releases/` directory are visible and work with the public link. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#issuecomment-1194461781"}}
{"comment": {"body": "> Link to installer package would work only when something is released. This one works regardless of the state and downloads it directly from S3.\r\n\r\nSo we just need to release to `internal` for the download link to work.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#issuecomment-1194465590"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#pullrequestreview-1049896988", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#pullrequestreview-1049914978", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#pullrequestreview-1050052685", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#pullrequestreview-1050102927", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#pullrequestreview-1051423928", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2422#pullrequestreview-1051538879", "body": ""}
{"title": "Make threads searchable by PR title edit", "number": 2423, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2423", "body": "I've ranked it at the same level as thread title but we can change later.\nWill require re-indexing (but not re-ingestion)"}
{"title": "Only spit exceptions when necessary when inserting versions", "number": 2424, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2424", "body": "Massive amount of exceptions in logz.io that is polluting our environments.\nWe should be using insertIgnore to handle uniqueness failures for now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2424#pullrequestreview-1049992418", "body": ""}
{"title": "Add styled modal component to vscode", "number": 2425, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2425", "body": "\n(Part of work to support the mentions invite flow feature)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2425#pullrequestreview-1050010483", "body": ""}
{"comment": {"body": "What's a stacked button?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2425#discussion_r929262927"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2425#pullrequestreview-1050011356", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2425#pullrequestreview-1050013199", "body": ""}
{"comment": {"body": "it's styling the buttons so they're stacked (instead of side by side), ie \r\n<img width=\"364\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/180867201-e980d7a7-139f-499c-b47f-cd72da70f493.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2425#discussion_r929264900"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2425#pullrequestreview-1050092200", "body": ""}
{"comment": {"body": "This generally looks good, I do get a bit nervous about having platform-specific implementations of these components -- it means the components will be slightly different in functionality, and depending on where you import from you get something different.  Is there a way we can unify this behaviour so that the styling is correct for each platform but the functionality is consistent?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2425#discussion_r929315109"}}
{"title": "Fetch lots more source marks", "number": 2426, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2426", "body": "Hacky fix"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2426#pullrequestreview-1050012236", "body": ""}
{"title": "Support for multiple root commits", "number": 2427, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427", "body": "Just used for debug right now. Simple fix."}
{"comment": {"body": "auto merge fail :(", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#issuecomment-1194760598"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#pullrequestreview-1050125608", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#pullrequestreview-1050126581", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#pullrequestreview-1050146062", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#pullrequestreview-1050182444", "body": ""}
{"comment": {"body": "I don't think I understand this, is this supposed to be here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#discussion_r929382964"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#pullrequestreview-1050182769", "body": ""}
{"comment": {"body": "From Rashin's comment\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/2427#discussion_r929343080", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#discussion_r929383230"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#pullrequestreview-1050183188", "body": ""}
{"comment": {"body": "hmm, I can make it handle unassigned instead. Will be cleaner...\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#discussion_r929383571"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#pullrequestreview-1050183687", "body": ""}
{"comment": {"body": "I think the intention here should be that if there's no root commit, the entire function returns `undefined` ?  I have no idea what conditions this might happen for, but I'd imagine we don't want to resolve with a repo with a `000000` root commit ...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#discussion_r929383951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2427#pullrequestreview-1050184016", "body": "A comment about the default value, I'll let you decide what to do for that case..."}
{"title": "Login dialog after web extension installation", "number": 2428, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428", "body": "\n\nNext steps are to get a recommended repo and redirect to repo.\nComplete unb-494"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052788518", "body": ""}
{"comment": {"body": "This means we'll now wait up to 1 minute while logging in to exchange the token?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931259593"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052802842", "body": ""}
{"comment": {"body": "Yup. We'll wait for up to a minute now after the login popup appears.\r\n\r\n20s felt too short to me. This is noticeable when one installs the Web extension and they are *not* logged into GH.\r\nIn that case, I actually had to log into GH within the popup which meant opening 1password, 2fa etc...\r\n\r\nThis actually took me just longer than 20s which meant I hit the timeout error state.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931269663"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052827614", "body": ""}
{"comment": {"body": "nit, I'm not sure why all these types have 'Utils' in their names?  This just seems like an 'AuthState' to me?  or \"AuthOperationState\" ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931287530"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052829818", "body": ""}
{"comment": {"body": "There's an empty file named `InstallDialogUtils.ts` in this PR, remove it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931289121"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052836221", "body": ""}
{"comment": {"body": "Have it scoped to the class name right now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931293746"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052837209", "body": ""}
{"comment": {"body": "Kinda unusual to define the sizing this way?  Wouldn't we just set margins on the dialog content to get the sizing we want, instead of setting absolute min/max sizes?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931294502"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052839887", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052842741", "body": ""}
{"comment": {"body": "This is another time where we're manually/explicitly telling a store when a UI that impacts a stream/store is active.  Instead `AuthUtils.AuthUtilsStateStream` should become active when anyone is subscribed to it (the natural way xstream works).\r\n\r\nUp to you if this is worth changing, or should go into a follow-on PR, etc.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931298634"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052846858", "body": ""}
{"comment": {"body": "Probably shouldn't be using `br` for this, and should use a max-width (or margin, etc) on the component instead to word-wrap?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931301569"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052847834", "body": ""}
{"comment": {"body": "Follow up. I want to chat with you first on that pattern. In my mind, it's quite a departure from the existing class based architecture that's being used all the \"Utils\" files.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931302203"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052848125", "body": ""}
{"comment": {"body": "I'm not sure I love doing spacing this way?  I'd rather get precise spacing using `<p>` and-or manual margins?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931302405"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052858996", "body": ""}
{"comment": {"body": "Once we have the correct line height, this works pretty well with matching the designs in Figma.\r\nRemoves the need from changing the padding between different instances of `<p>` or other elements.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931310044"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052860426", "body": ""}
{"comment": {"body": "Yeah for sure.  I do think the current mechanism makes less sense with our current architecture, since we don't require messaging like we used to.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931311034"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052861998", "body": ""}
{"comment": {"body": "This is primarily used to handle the loading state where the loader's intrinsic size is too small.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931312154"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052866309", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052868420", "body": ""}
{"comment": {"body": "It somehow feels wrong \ud83d\ude06 \r\n\r\nWould it make sense to wrap this in a custom component?\r\n\r\n```\r\n    <Paragraph belowLines={2}/>\r\n      Onboarding to Unblocked currently requires the...\r\n   <Paragraph>\r\n   <Paragraph>\r\n     More stuff\r\n   </Paragraph>\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r931316767"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1052877856", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#pullrequestreview-1346738993", "body": ""}
{"comment": {"body": "Test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2428#discussion_r1140707771"}}
{"title": "Update text for missing redirect flow", "number": 2429, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2429", "body": "After GH App installation in web extension, we want the popup page to end at this install/redirect UI.\nWeb extension will eventually close the popup once it's received a notification that the GH app has been installed\n\nVSCode GH App Installation triggers same component but has a redirectLocation which renders different messaging."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2429#pullrequestreview-1050196172", "body": ""}
{"comment": {"body": "We should probably make this explicit with a parameter...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2429#discussion_r929393912"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2429#pullrequestreview-1050196199", "body": ""}
{"title": "Person API implementation", "number": 243, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/243"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/243#pullrequestreview-872033076", "body": ""}
{"title": "Support for multiple root commits 2", "number": 2430, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2430", "body": "Support for multiple root commits\nMock out logger\nBetter undefined behaviour"}
{"title": "Mock out Winston logger in all tests", "number": 2431, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2431", "body": "This mocks out the logger globally so we don't have to keep adding it everywhere"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2431#pullrequestreview-1050198440", "body": ""}
{"title": "Update intro state to Sofia pro", "number": 2432, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2432", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2432#pullrequestreview-1051341529", "body": ""}
{"comment": {"body": "So this overrides whatever the surrounding font is, so that buttons always use the GitHub default font?  Will this make some odd UIs where button fonts don't match their surroundings?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2432#discussion_r930217398"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2432#pullrequestreview-1051342399", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2432#pullrequestreview-1051379325", "body": ""}
{"comment": {"body": "That was explicitly done for the screenshots above.\r\n\r\nI don't think we ever want to have Sofia pro in the buttons? @benedict-jw ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2432#discussion_r930244072"}}
{"title": "Add version info description", "number": 2433, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2433", "body": "Part 1 of a multi-part endeavour. This adds an optional description text field to the VersionInfoModel, which is expected to store Markdown text. \nThe Hub will render the markdown using a 3rd party Markdown rendering engine like  \nNext after this PR: Adding description editing capabilities to the admin console, and giving the Hub rendering super-powers.\nThen after that: description composites at the API layer when the client is > 1 version behind"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2433#pullrequestreview-1050448632", "body": ""}
{"title": "Fixes hub going to the background when system prefs dismissed", "number": 2434, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2434", "body": "Absolutely gruesome hack, but it works, and doesn't seem to do much to CPU and power (I guess Task is pretty intelligent):\n\nNot enough to just reset the window level. To AppKit, all bets are off once the popover's window level is set manually. We initially set it to .normal to facilitate the system prefs z-order on top, but then to reset the level we have to close and re-open the hub with the animation flag turned off.\nThere's really no other way to listen for active status of another app besides polling it. The alternative is a much more gruesome and definitely not future proof KVO hack."}
{"comment": {"body": "> If performance is ever an issue, instead of generating N tasks, we could have a single task that calls n callbacks.\r\n\r\nIt's not generating N tasks in this case though? Or do you mean in case there are multiple callers to this function?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2434#issuecomment-1195680246"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2434#pullrequestreview-1051249570", "body": "If performance is ever an issue, instead of generating N tasks, we could have a single task that calls n callbacks."}
{"title": "PullRequestStore.findOpen only returns pull requests with threads", "number": 2435, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2435", "body": "For bulk ingestion of pull requests, we're going to start creating database records for every pull request, not just the ones with threads.\nThe getPullRequests API operation returns all open PRs. Let's make sure it keeps returning PRs with threads, since it's used to create tree nodes in the sidebar."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2435#pullrequestreview-1051557102", "body": ""}
{"title": "API to recommend repos by conversation count", "number": 2436, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2436", "body": "Introduce 3 new APIs:\n- getRepo\n- getRepoStats\n- getAllRepoStats\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2436#pullrequestreview-**********", "body": ""}
{"title": "Stupid little utility to recommend contributor based off name", "number": 2437, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437", "body": "Utility that attempts to use git log -> contributors to map a name to an email.\nNecessary so that users don't have to explicitly type in emails all the freaking type when they're @mention'ing a team member without an account."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437#pullrequestreview-**********", "body": ""}
{"comment": {"body": "A bit naive, but whatever. It's going to make a suggestion,  correctness is not guaranteed.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437#discussion_r930092246"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437#pullrequestreview-**********", "body": "Nice"}
{"comment": {"body": "Do we really need a 5000 line file for the unit test? Clip it to 50?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437#discussion_r930116797"}}
{"comment": {"body": "Could be multiple emails for a given name. So the value could be a distinct array of Contributors. Maybe, not sure it\u2019s worth it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437#discussion_r930120367"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437#pullrequestreview-**********", "body": ""}
{"comment": {"body": "@rasharab\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/914726a2-9418-40ea-b2eb-acb0454c8a9b?message=572fd692-3b4e-4649-88c2-6e11fe6887c0).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437#discussion_r939013554"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437#pullrequestreview-1063658374", "body": ""}
{"comment": {"body": "@rasharab\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/19fb14aa-a6ab-49c7-b77b-9154a9442699?message=08f8d0d6-d886-4495-951a-a476b393e527).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2437#discussion_r939019253"}}
{"title": "Remove Current File UI image", "number": 2438, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438", "body": "Fixes UNB-497\n\nRemove the animated image in the Current File UI\nSet the background for the 'Contact Support' pane to match the Current File UI background.  This only takes effect in the regular Sidebar UI\n\n"}
{"comment": {"body": "Padding looks a bit off. Tight to bottom of sidebar without image\r\n\r\n![CleanShot 2022-07-26 at 10 07 53@2x](https://user-images.githubusercontent.com/1553313/181068134-8e5694ae-7edd-43b0-948b-c54b12449803.png)\r\n.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#issuecomment-1195749885"}}
{"comment": {"body": "> Padding looks a bit off. Tight to bottom of sidebar without image\r\n> \r\n> ![CleanShot 2022-07-26 at 10 07 53@2x](https://user-images.githubusercontent.com/1553313/181068134-8e5694ae-7edd-43b0-948b-c54b12449803.png) .\r\n\r\nThe bar is resizable, ie there is no bottom padding, because it depends on how big the user has sized the panel to", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#issuecomment-1195755105"}}
{"comment": {"body": "> <img alt=\"image\" width=\"265\" src=\"https://user-images.githubusercontent.com/13431372/181068146-27d49eae-bd5e-44b5-a414-25de4c5c43b2.png\">\r\n> \r\n> Ah interesting, are we no longer calling this `Unblocked Insights` ? I think I personally like having the just file name there a bit better, but the tradeoff is I feel like it's less clear that this panel is Unblocked-specific UI(??)\r\n\r\nWe call it 'Unblocked Insights' in the file explorer.  In our own sidebar we do not.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#issuecomment-1195755521"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#pullrequestreview-1051338195", "body": ""}
{"comment": {"body": "All I did here is add a separate top-level div, so we can target the background of the 'Contact Support' button in the sidebar frame.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#discussion_r930215144"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#pullrequestreview-1051344949", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#pullrequestreview-1051345883", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#pullrequestreview-1051347478", "body": "\nAh interesting, are we no longer calling this Unblocked Insights ? I think I personally like having the just file name there a bit better, but the tradeoff is I feel like it's less clear that this panel is Unblocked-specific UI(??)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#pullrequestreview-1051347892", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2438#pullrequestreview-1051352371", "body": ""}
{"title": "Only scope to github.com", "number": 2439, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2439", "body": "\n\nScope web extension to just github.com, and not subdomains.\naka will not show up in docs.github.com or api.github.com etc..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2439#pullrequestreview-1051357917", "body": ""}
{"title": "Improve documentation", "number": 244, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/244"}
{"title": "Allow for retrieving all contributors associated with name", "number": 2440, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2440"}
{"title": "Threads are searchable by pull request number", "number": 2441, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2441"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2441#pullrequestreview-1051418228", "body": "nice. great tests"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2441#pullrequestreview-1051428281", "body": ""}
{"comment": {"body": "test comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2441#discussion_r930279196"}}
{"title": "Disable turbo events more aggressively", "number": 2442, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2442", "body": "Update selector to be more generic.\nWe were also only disabling turbo when one entered the files pages in a PR.\nUpdated to disable turbo on the anchor elements the moment one enters the PR view.\nFixes UNB-498"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2442#pullrequestreview-1052886310", "body": ""}
{"title": "Implementation of getRepo", "number": 2443, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2443", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2443#pullrequestreview-1051492821", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2443#pullrequestreview-1051559004", "body": ""}
{"title": "Fix client pagination bug", "number": 2444, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2444", "body": "The core issue is described here: \n\nThe expected behaviour for pagination is:\n* For each per-second poll, the client makes up to n page fetches.  We store the lastModified from the last-fetched page.\n* On the next poll, we use this lastModified to see if there are any more pages to fetch.\n* The expectation is that this will let us eventually load all the data, loading roughly n pages every second or two\nThe bug is a corner-case in this situation, heres the scenario we hit yesterday: we load 5 pages per poll, 500 SMs per page, and the Unblocked repo has 2017 SMs:\n* On the first poll, we will load all 5 pages, fetching (500+500+500+500+17)=2017 SMs.  However, since the client never got an empty page, we consider that we still have more data to load, and nothing is returned to the SourceMark engine.  We store the lastModified from that final 17-SM page.\n* On the next channel poll, we use that lastModified in the channel poll call, which indicates (correctly) that there is no more data to load for that collection.  So the SourceMark store is not told that it needs to do a fetch, so it never fetches the final (empty) page, and never returns the data to the SourceMark engine.\n* The data will only be sent to the SourceMark engine once there is another channel poll trigger for sourcemarks (ie, some client updates SMs).\n* The tests I wrote were correct, but tested paging when there was one additional page, so it worked as expected.\n* Customers would only have run into this if they had (5*x) pages of SM data.\nI did a hacky fix for this yesterday, today Ill fix this properly in VSCode.  Theres a separate question about how we should do paged loading on polling, which maybe wont be relevant once we extract the SM engine out of the IDE.\n\nThis PR fixes pagination to re-poll correctly while we are in the middle of loading data.  Also added a test case for this exact scenario."}
{"comment": {"body": "Superseded by https://github.com/NextChapterSoftware/unblocked/pull/3311", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2444#issuecomment-1281581774"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2444#pullrequestreview-1051630397", "body": ""}
{"comment": {"body": "This mock API wasn't acting the same as our APIs -- if there was no more data to return, this would return a null `ifModifiedSince`, whereas it should return the last-added item's `ifModifiedSince`.  This fixes the mock.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2444#discussion_r930423917"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2444#pullrequestreview-1051631622", "body": ""}
{"comment": {"body": "The changes to this file are undoing a hack we had added earlier: we previously had some APIs (notably the getTeamMembers API) that didn't correctly implement polling, and would return a null `lastModified` on a GET call.  These hacks worked around them.\r\n\r\nThis change undoes the hacks.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2444#discussion_r930424721"}}
{"title": "Upsert PullRequest model on receiving a webhook", "number": 2445, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445", "body": "Fixes UNB-406\nCurrently, we're only updating the state when we receive a pull request webhook. This PR updates that to upsert on every webhook: create if it doesn't exist or update it if the updatedAt field is newer than what we've stored in the db.\nThis way we fix the issue of titles not updating when they're changed, plus this is more in-line with our planned bulk ingestion of pull requests where we create a pull request model for every pull request."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#pullrequestreview-1052934101", "body": "looks good - minor stuff inline"}
{"comment": {"body": "We should make it non-nullable eventually? Would require a re-ingest to backfill. Not sure if worth it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#discussion_r931363545"}}
{"comment": {"body": "Be really cool if we had a generic pattern for upsert, as we use this in many places... will give it some thought...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#discussion_r931411596"}}
{"comment": {"body": "I wonder if should we create a team member in this case?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#discussion_r931418380"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#pullrequestreview-1053029888", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#pullrequestreview-1053064215", "body": ""}
{"comment": {"body": "yah we can we can do a (bulk) reingest, wouldn't be that costly (relatively speaking)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#discussion_r931466109"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#pullrequestreview-1053149380", "body": ""}
{"comment": {"body": "I'll do in a follow up PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#discussion_r931526382"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#pullrequestreview-1054675434", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/2464\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4fbfd20b-9812-4183-8db6-4b07cd7818eb?message=2cb1906f-8270-45a6-be9e-537d8ef0f3b7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#discussion_r932622918"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#pullrequestreview-1054744611", "body": ""}
{"comment": {"body": "https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4fbfd20b-9812-4183-8db6-4b07cd7818eb?org=NextChapterSoftware&repo=unblocked", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2445#discussion_r932665673"}}
{"title": "Speed up initial sidebar injection", "number": 2446, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2446", "body": "Remove unused tabID (from older architecture where we needed to communicate with background script).\nThis allows us to trigger an initial load of the sidebar, injecting it into the dom before navigation events are dispatched from background script.\nFixes UNB-496"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2446#pullrequestreview-1052885573", "body": ""}
{"title": "Implementation of getRepoStats and getAllRepoStats", "number": 2447, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2447", "body": "fixes: "}
{"title": "fix path in s3 download link", "number": 2448, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2448", "body": "Fixed the path to include the S3 directory\nAdded tests for it"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2448#pullrequestreview-1051601069", "body": ""}
{"title": "Fix editor indentation issues", "number": 2449, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449", "body": "Fixes UNB-484\nFixes issues with text editor indentation:\n* When an editor is associated with a file outside of a known repo (ie, an unsaved file, or one in an unusual folder), indent with no content so we are consistent.\n* Render indentation immediately when an editor opens\n* Reduce the debounce time on editor creation/deletion/focus events, so that indentation can happen quicker"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#pullrequestreview-1051621986", "body": ""}
{"comment": {"body": "Reduced debouncing here causes indentation to take effect quicker.  We still need the debouncer to glob discrete editor events (create, destroy, focus) together.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#discussion_r930417590"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#pullrequestreview-1051662740", "body": ""}
{"comment": {"body": "With this change, we will *always* indent, even if there are no source marks for the file?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#discussion_r930447914"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#pullrequestreview-1051663361", "body": ""}
{"comment": {"body": "Yes -- that has always been our goal, since sourcemarks may take awhile to load, and we don't want to wait to indent until we've loaded them (indenting after the fact is annoying)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/276342c8-bdcd-440d-a548-be200af01709?message=eaaaf307-ef16-427f-aa91-9e234e333494).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#discussion_r930448406"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#pullrequestreview-1051663680", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#pullrequestreview-1051663775", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#pullrequestreview-1051666793", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2449#pullrequestreview-1051668210", "body": ""}
{"title": "Add client workspace file", "number": 245, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/245", "body": "Opens clients, shared code, and API folder in a single VSCode window"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/245#pullrequestreview-872488990", "body": ""}
{"title": "forgot to assert", "number": 2450, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2450", "body": "Forgot to add the the assert condition and also the platform in url"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2450#pullrequestreview-1051646978", "body": ""}
{"title": "Adds update description text to hub", "number": 2451, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2451", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2451#pullrequestreview-1053287776", "body": ""}
{"comment": {"body": "We're dumping the visualEffect view. It doesn't seem to play nice with `NSHostingView` during layout", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2451#discussion_r931627027"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2451#pullrequestreview-1053288923", "body": ""}
{"comment": {"body": "Note: the old update behaviour is preserved if no description text is available", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2451#discussion_r931627809"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2451#pullrequestreview-1053299883", "body": ""}
{"comment": {"body": "If your last GIF there's a wonky bullet alignment. Is that just markdown or something else?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2451#discussion_r931635296"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2451#pullrequestreview-1053301518", "body": ""}
{"title": "Proper Cleanup in create discussion", "number": 2452, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2452", "body": "After CreateDiscussion dialog is dismissed, we need to make sure that the CreateDiscussionUtils.createDiscussionStateStream stream is cleaned up.\nIf not, this instance will be shared with subsequent instances of CreateDiscussion on the same file causing some unexpected behaviours (e.g. cannot create a second thread on file without refresh)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2452#pullrequestreview-1051717617", "body": ""}
{"title": "Fix git/utils module references", "number": 2453, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2453", "body": "The VSCode 'git' module was created without a top-level path module name, so everyone referenced it using relative paths.  Plus, everything in the 'git' module was referencing 'utils' by relative paths.  Fix these.\nThere are still a couple places where 'git' directly references files in 'sourcemark', which I think doesn't make sense -- sourcemark uses git, not the other way around?  So I think we should move those files into git?  I'm not 100% sure though."}
{"comment": {"body": "Merging this, LMK if anyone has problems with this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2453#issuecomment-1197270521"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2453#pullrequestreview-1053030713", "body": ""}
{"comment": {"body": "Mocks out image files in tests", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2453#discussion_r931439937"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2453#pullrequestreview-1053034360", "body": ""}
{"comment": {"body": "This never worked correctly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2453#discussion_r931442964"}}
{"title": "Fix bad overflow value", "number": 2454, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2454", "body": "Prevents a double scrollbar in the sidebar, somehow got into a previous commit.  I never noticed it until I debugged on a smaller screen."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2454#pullrequestreview-1051778116", "body": ""}
{"title": "Only run ingestion on the first repo on each loop", "number": 2455, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2455", "body": "When Clio onboarded today, the highest priority repo was ingested first but an error was returned by one of the GitHub's API operations. The onboarding sync job continued with the other repos before returning to the first one, which caused ingesting the first repo to come right at the end.\nThis PR changes it so that the job only attempts the highest priority repo on each loop before exiting. This way the job keeps attempting the highest priority jobs and only moves onto the lower priority repos once done."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2455#pullrequestreview-1051942446", "body": ""}
{"comment": {"body": "`prIngestionStore.getPullRequestIngestionsForOnboarding()` returns the repos in order of priority", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2455#discussion_r930659840"}}
{"title": "Add version markdown editor to admin console", "number": 2456, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2456", "body": "Totally functional but I need CSS help because this looks horrific. @kaych @jeffrey-ng @matthewjamesadam  please save me from myself \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2456#pullrequestreview-1053059884", "body": ""}
{"comment": {"body": "Why return the object, when the caller does not use it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2456#discussion_r931462085"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2456#pullrequestreview-1053240039", "body": ""}
{"comment": {"body": "Using it in test to validate results, but I guess we can do that in other ways", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2456#discussion_r931591184"}}
{"title": "Incident: disable recommendations during ingestion", "number": 2457, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2457"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2457#pullrequestreview-1053211308", "body": ""}
{"title": "DB Index on MessageModel", "number": 2458, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2458", "body": "Optimize these queries:\n\n\nWarning\nThe biggest risk here is that the index is not created in the background, meaning that the DB locks the MessageModel for writes; reads continue."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2458#pullrequestreview-**********", "body": ""}
{"title": "Revert \"DB Index on MessageModel\"", "number": 2459, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2459", "body": "If necessary, reverts NextChapterSoftware/unblocked#2458"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2459#pullrequestreview-**********", "body": ""}
{"title": "Add health checks", "number": 246, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/246", "body": "Mahdi needs this for kube related checks..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/246#pullrequestreview-872285827", "body": "Awesome. Thank you."}
{"title": "Refactor Thread Sidebar", "number": 2460, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2460", "body": "Refactor ThreadsSidebar away from manually sending load events to observing stream listeners."}
{"comment": {"body": "Logic doesn't change much. Moving away from UI explicitly telling store when it's active.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2460#issuecomment-**********"}}
{"title": "Add support for error handling in proxy service", "number": 2461, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2461", "body": "PromiseProxy service can now transport errors.\nErrors only go one direction though. From Service -> Client."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2461#pullrequestreview-1053347585", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2461#pullrequestreview-1053353303", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2461#pullrequestreview-1054430482", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2461#pullrequestreview-1054430761", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2461#pullrequestreview-1054563740", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2461#pullrequestreview-1059523451", "body": ""}
{"title": "DB Index on SourcePointModel", "number": 2462, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2462", "body": "All significant queries on SourcePointModel use the mark clause.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2462#pullrequestreview-1053379397", "body": ""}
{"title": "Ingestion checks for existing pull request model before going to GitHub API", "number": 2463, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2463", "body": "No need to go out to the GitHub API if the pull request model exists. This is part of the effort to make ingestion faster."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2463#pullrequestreview-1054532600", "body": "one question about pr authors below..."}
{"comment": {"body": "minor: try to avoid returning DAO if possible.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2463#discussion_r932525743"}}
{"comment": {"body": "minor: let's add context, otherwise we have no idea what team/repo/pr this message is for", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2463#discussion_r932533462"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2463#pullrequestreview-1054562808", "body": ""}
{"comment": {"body": "Added to the `ingestThread` method that calls this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2463#discussion_r932544881"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2463#pullrequestreview-1054570791", "body": ""}
{"title": "Create team member with null role for PR author if not found", "number": 2464, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2464"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2464#pullrequestreview-1054679086", "body": ""}
{"comment": {"body": "Is it possible that the PR author isn't a team member? Is that what a null role indicates?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2464#discussion_r932625464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2464#pullrequestreview-1054740121", "body": ""}
{"comment": {"body": "Important to note that although all past members will have a null role; not all members with a null role are past members.\r\n\r\nA null role just means that we don't know yet, that's all.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2464#discussion_r932662693"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2464#pullrequestreview-1054756104", "body": ""}
{"title": "DB Index on SourceMarkModel", "number": 2465, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2465", "body": "indexes created based on N-worst performance analysis in PROD\n  \nreorder queries to ensure that query planner uses new repo index"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2465#pullrequestreview-1054877861", "body": ""}
{"title": "DB Index on TeamModel", "number": 2466, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2466", "body": "indexes created based on N-worst performance analysis in PROD\n  \nall slow queries covered"}
{"title": "Use DB Index on TeamMemberModel", "number": 2467, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2467", "body": "reorder queries to ensure that query planner uses existing compound {team, identity} index\n  "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2467#pullrequestreview-1054879902", "body": "My understanding is that db engines are smart enough to use the right index regardless of clause order, but if this works then I'm wrong :)"}
{"title": "DB Index on ThreadModel", "number": 2468, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2468", "body": "indexes created based on N-worst performance analysis in PROD\n  \n\n\nreorder queries to ensure that query planner uses {id} or {team, pullRequest} indexes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2468#pullrequestreview-1054875836", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2468#pullrequestreview-1054876041", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2468#pullrequestreview-1054876500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2468#pullrequestreview-1054877792", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2468#pullrequestreview-1054879105", "body": ""}
{"title": "Add execution query planning to database test framework", "number": 2469, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2469", "body": "Judging by the prs I saw last week, when necessary, we need to validate queries are appropriately using database indices when we need them through tests.\nUnlike Skywagon, throwing exceptions is an OPT-IN approach. You will get errors logged out, but exceptions are now configurable.\nTo that effect\n1. This pr piggy backs on Exposed's interceptor framework to allow us to explain all test queries before they are executed. We parse the resulting plan results in order to determine if we're doing a sequential scan of tables or doing some sort of query that is hitting a lot of rows.\n2. We use coroutine context elements to hold on to the interceptors in order to eliminate the need for explicitly passing interceptors around all over the place.\n3. Add the ability to configure how a database test is set up via a configuration argument:\nfun deletingTeamCascades() = suspendingDatabaseTest({\n        throwOnSlowQuery = true\n    }) {\n    }\n4. Add ability to disable query planning (i.e. for DAO queries that are localized to tests)\nwithStatementInterceptorsDisabled {\n            // ... expect team and sourceMark to be deleted\n            suspendedTransaction {\n                assertThat(TeamDAO.findById(teamId)).isNull()\n                assertThat(SourceMarkDAO.find { SourceMarkModel.group eq markID }.count()).isZero\n                assertThat(SourceMarkDAO.find { SourceMarkModel.team eq teamId }.count()).isZero\n                assertThat(SourceMarkDAO.find { SourceMarkModel.message eq messageID }.count()).isZero\n            }\n        }\nTESTING:\n1. Took a look at a few of the prs that @richiebres created, and validated that this pr correctly catches queries that are not using indices and that fucked us last week.\ni.e.\ncom.nextchaptersoftware.db.interceptors.explain.SlowQueryException: \n           Sql:\n            [SELECT teammodel.id, teammodel.\"createdAt\", teammodel.\"modifiedAt\", teammodel.\"displayName\", teammodel.provider, teammodel.\"providerExternalId\", teammodel.\"providerLogin\", teammodel.\"providerDisplayName\", teammodel.\"providerAvatarUrl\", teammodel.\"providerHtmlUrl\", teammodel.\"providerExternalInstallationId\", teammodel.\"deletedProviderExternalId\", teammodel.\"deletedProviderExternalInstallationId\", teammodel.\"subscribedReleaseChannel\" FROM teammodel WHERE (teammodel.provider = ?) AND (teammodel.\"providerExternalId\" = ?) AND (teammodel.\"deletedProviderExternalId\" IS NULL)]\n           Line:\n            [Seq Scan on teammodel  (cost=10000000000.00..10000000082.46 rows=1 width=328)]\n           Plan:\n            [Seq Scan on teammodel  (cost=10000000000.00..10000000082.46 rows=1 width=328)\n  Filter: ((\"deletedProviderExternalId\" IS NULL) AND (provider = 1) AND (\"providerExternalId\" = '7376'::text))\n]\n           Match:\n            [Seq Scan]"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2469#pullrequestreview-**********", "body": "Very impressive Rashin!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2469#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2469#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2469#pullrequestreview-**********", "body": ""}
{"title": "Implement createThread and updateThread operations", "number": 247, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/247", "body": "Attempts to move data store logic into the database layer (package com.nextchaptersoftware.db.stores). If we're OK with this pattern, I'll start writing tests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/247#pullrequestreview-872328270", "body": "Fan of this model for many reasons."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/247#pullrequestreview-872370334", "body": ""}
{"comment": {"body": "Only downside is that we're still returning these DAOs. What I'm proposing in #220 is to go one step further and return data classes. But this is definitely in the right direction.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/247#discussion_r798939217"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/247#pullrequestreview-872522853", "body": ""}
{"comment": {"body": "These tests kind of already covered by `ThreadServiceTests`, lets remove them when we decouple the API layer from the DB and mock the database layer.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/247#discussion_r799048937"}}
{"title": "Move away from logging sql queries as it slows ci", "number": 2470, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2470"}
{"comment": {"body": "\ud83d\udcaf Was thinking about doing this. Thanks!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2470#issuecomment-1200452292"}}
{"title": "Sourcemark file count stats", "number": 2471, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2471", "body": "Just logging basically. Aggregated in Timelion in Logzio"}
{"title": "Add bulk pull request ingestion", "number": 2472, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2472", "body": "Adds a background job that does bulk ingestion\nTODO\n- ~Richie to confirm file sha sentinel value~ Using ffffffffffffffffffffffffffffffffffffffff\n- Replace queues with FIFO queues"}
{"title": "Invitation flow when @-mentioning team members", "number": 2473, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473#pullrequestreview-1059407789", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473#pullrequestreview-1059408409", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473#pullrequestreview-1059412162", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473#pullrequestreview-1059412944", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473#pullrequestreview-1059430840", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473#pullrequestreview-1059432817", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Have run into this a few times as well :(\r\n\r\nI'll look into refactoring how modals work so that openModal doesn't rerender whenever we modify modals...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473#discussion_r936039022"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2473#pullrequestreview-**********", "body": ""}
{"title": "Ensure we send appropriate emails if team member does not have an account", "number": 2474, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2474", "body": "ount"}
{"title": "Jeff/unb 499 gh app installation failure in vscode", "number": 2475, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475", "body": "Timeout GH App installation spinner after 1 minute."}
{"comment": {"body": "<img width=\"438\" alt=\"CleanShot 2022-08-04 at 15 27 53@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-0cea08c0-9ae9-4194-8dbe-b14bbe4f5870.png\">\r\n<img width=\"699\" alt=\"CleanShot 2022-08-04 at 15 27 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-35cecc9f-9b7a-472e-ac50-dc23f20ab14e.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#issuecomment-**********"}}
{"comment": {"body": "Latest styling:\r\n\r\n<img width=\"692\" alt=\"CleanShot 2022-08-04 at 22 31 11@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-929a78f3-debf-438d-a15b-8e105fad8197.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#issuecomment-1206064418"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1062664889", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1062683845", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1062690608", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1062695629", "body": ""}
{"comment": {"body": "Feel like this should be a pro-regular icon (i.e. the outline version). And have it inherit the same color as the text??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#discussion_r938329290"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/2475#pullrequestreview-1062696830", "body": ""}
