{"title": "Re-add Archived Threads UI to dashboard", "number": 1376, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376", "body": "This ended up being a little bit more involved then you might guess.  The Archived UI works differently then the other thread UIs: the archived list is loaded only when you navigate to it, and is not a live UI (ie it does not have a push channel).  So I couldn't reuse the same data loading machinery.\nI ended up making a separate store for the archived threads, that loads the threads, joins in the team members, and produces a stream that is compatible with the rest of the UI."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979250711", "body": ""}
{"comment": {"body": "Almost everything in here is just refactoring, making most of the shared team member joining logic accessible to the `ArchivedThreadListStore` above.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#discussion_r877531233"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979252019", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979253517", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979253728", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979258498", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979268455", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979269074", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979271872", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979279797", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979280109", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979280979", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979285702", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979297870", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979299841", "body": ""}
{"comment": {"body": "Since we're never fetching updates to archived threads, then why is this needed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#discussion_r877564416"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979300336", "body": ""}
{"comment": {"body": "Ah it's not, actually, I can remove it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#discussion_r877564788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979346987", "body": ""}
{"comment": {"body": "Ah I'm wrong, I think we do need this, because we're muxing all the archived threads for all teams into one list.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#discussion_r877599556"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-979354132", "body": ""}
{"comment": {"body": "ok, god I hate mixing teams. makes no sense.\r\n\r\nAnyway, you'll need to add recommendations rank sort below this one.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#discussion_r877605121"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-*********", "body": ""}
{"comment": {"body": "\ud83d\udc4d I'll add it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#discussion_r877687274"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-*********", "body": ""}
{"comment": {"body": "fwiw I think this item takes into account 0 being falsey, in other words I'm pretty sure you can just pass in unreads.length and the component would know to not render `(0)` ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#discussion_r878325067"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1376#pullrequestreview-*********", "body": ""}
{"title": "Fix thread bubble alignment", "number": 1377, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1377", "body": "Lifts it up a tad and capitalizes the thread title"}
{"title": "AddInvitesEndpoint", "number": 1378, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1378", "body": "I don't generally get the opportunity to get reviewed by @richiebres \nThe honor is all mine!!!1"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1378#pullrequestreview-*********", "body": "looks good"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1378#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1378#pullrequestreview-979289768", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1378#pullrequestreview-979290599", "body": ""}
{"title": "Rename team to recommended in dashboard", "number": 1379, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1379"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1379#pullrequestreview-979389403", "body": ""}
{"title": "Start discussion form UI", "number": 138, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138", "body": "vscode:\n\nstorybook:\n\n* NOTE: built in the test-theme.scss into the vscode storybook \nThis is only the UI form, with mocked data, no backend implementation yet.\nStill TODO (in ensuing PRs):\n* Fix state management so state of the form persists when webview reloads (i.e. on tab change)\n* Add generate-api to generate API models\n* Layer 'find team members' logic to populate list of collaborators \n* Add user icons to UI (will need to revise existing web/ component per designs and move it to the shared/ folder once that's set up) \n* Add fontawesome icons to vscode and to UI (move to shared/ etc)"}
{"comment": {"body": "Sorry for adding extra work @kaych , you're going to have to remove changes to `/vscode/package-lock.json` and merge/rebase from main...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#issuecomment-1023749059"}}
{"comment": {"body": "FWIW, I ran into this exact issue with the StartDiscussion story: https://github.com/storybookjs/storybook/issues/14346#issuecomment-808874430", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#issuecomment-1023823725"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-864282295", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-864282388", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865488635", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865532482", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865532635", "body": ""}
{"comment": {"body": "For web, we had the option for this number to \"grow\". Do we want that here as well?\r\n\r\naka 1 min ago -> 2 min ago...\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#discussion_r794073625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865562814", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865563069", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865565113", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865575231", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865576204", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865587132", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865588943", "body": ""}
{"comment": {"body": "as in the autoupdating? I guess I'm not sure how necessary it is in this view \r\n\r\n![image](https://user-images.githubusercontent.com/13431372/151467586-56813762-3207-40bf-a28c-f762cfbf4c84.png)\r\n\r\nif anything, if the list updates (i.e. I pushed a new commit) maybe the component would rerender given a pusher event? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#discussion_r794117209"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865589137", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865590547", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-865619662", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/138#pullrequestreview-868254025", "body": ""}
{"title": "Admin web PR re-ingestion button", "number": 1380, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1380"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1380#pullrequestreview-979389717", "body": ""}
{"title": "Fix Thread-to-TeamMember joins", "number": 1381, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1381", "body": "Fixes this:\nhttps://admin.dev.getunblocked.com/teams/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/repos/cecea018-fdc0-46aa-a160-8c35ba197164/threads\nBreaking change was here:\nhttps://github.com/NextChapterSoftware/unblocked/pull/1346"}
{"title": "Hook up invite email to temp UI", "number": 1382, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1382"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1382#pullrequestreview-979420000", "body": ""}
{"title": "Show PR author in admin web", "number": 1383, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1383", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1383#pullrequestreview-979431866", "body": ""}
{"title": "Stabilize flaky test", "number": 1384, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1384", "body": "Because the operations here are asynchronous, it's hard to deterministically tell which push channels will be called in which order -- they may be polled at the same time, they may be polled one after another.  As long as both channels are actually polled, this should pass.\nLonger-term, the plan is to define these async tests using fake timers, which would allow fully deterministic behaviour, but we need to upgrade to Jest 28 before we can do that, and that will take a fair bit of effort."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1384#pullrequestreview-979471933", "body": ""}
{"title": "Correctly set lastMessageCreatedAt for PR ignested threads", "number": 1385, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1385", "body": "\nThis is causing unnecessary pushes when an author posts a reply to a PR thread from unblocked."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1385#pullrequestreview-979506891", "body": "With comments"}
{"comment": {"body": "`maxOfOrNull` haha I can't believe this is stdlib ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1385#discussion_r877723859"}}
{"comment": {"body": "Do we have to check for archived as well here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1385#discussion_r877724625"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1385#pullrequestreview-979510535", "body": ""}
{"comment": {"body": "Archived is a prop of thread so I don't think so. I think its still fine to set this field for archived threads.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1385#discussion_r877727986"}}
{"title": "Recommendation engine with Social Comment Network model", "number": 1386, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1386", "body": "generates recommendation model from admin web for now\n\nThe social comment network is the first of at least 8 signals that we'll use for recommendation purposes. See for context:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1386#pullrequestreview-980523647", "body": "SHIPIT"}
{"title": "Before and After cursors for more efficient polling and paging", "number": 1387, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1387", "body": "Motivation\n\nallows for paging forwards and backwards from any thread list item\nfacilitates very efficient polling of an existing list view using combination of If-Modfied-Since and before. only one subscription is necessary\n\nSee also\n\n"}
{"title": "Hold content view in memory instead of recreating on popup.", "number": 1388, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1388", "body": "Makes things appear faster"}
{"title": "Simplify Notification Service", "number": 1389, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1389", "body": "Enqueuing from ApiService should be a simple event, there should be zero processing on the apiservice side.\nThe NotificationService should now hold sole responsibility of generating the actual template that is pushed to SES.\nTo that end:\n1. We have Polymorphic Payload types that are sent to noficiation service.\n2. We have Payload handlers in notification service that manage the actual pushing to ses."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1389#pullrequestreview-980585730", "body": "very clean, nice work!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1389#pullrequestreview-980591394", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1389#pullrequestreview-980591914", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1389#pullrequestreview-980592127", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1389#pullrequestreview-980596348", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1389#pullrequestreview-980597498", "body": ""}
{"title": "Add openapi linter", "number": 139, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/139", "body": "This pr solves the annoying problem of inconsistency in api specs.\nWe are now going to use zally:\n\nThe nice thing about Zally is that they use kotlin, so I've done the following:\n1. Piggy back on gradle buildSrc to generate Codeswell specific gradle plugin that we can use to lint our openapi spec via our own tasks (zallyLint)\n2. Allow for custom configuration of ruleset for Zally.\nTo run: ./gradlew :api:zallyLint\nI'm going to in a followup pr fix our openapi spec and code and reduce the maximum lint errors allowed before a gradle build will fail."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/139#pullrequestreview-864254227", "body": ""}
{"comment": {"body": "Pretty neat way of enforcing casing on various types in openspec api...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/139#discussion_r793159019"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/139#pullrequestreview-864254602", "body": ""}
{"comment": {"body": "This max number of failures allowed will be reduced in another pr.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/139#discussion_r793159296"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/139#pullrequestreview-864259108", "body": ""}
{"title": "Update search threads operation to return ThreadInfos", "number": 1390, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1390", "body": "This will break the searchThreads operations"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1390#pullrequestreview-980623924", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1390#pullrequestreview-980699450", "body": "would prefer not to version yet..."}
{"title": "Admin web actions menu", "number": 1391, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1391", "body": "Just refactoring to introduce the new component. No new functionality.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1391#pullrequestreview-980702909", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1391#pullrequestreview-980708691", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1391#pullrequestreview-980708806", "body": ""}
{"title": "Add defensive networking", "number": 1392, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392", "body": "As with any resilience stuff, this can be a bit tricky to follow. There are two central ideas here:\n\nEverything is gated on auth, which blocks\nFailures are retried with backoff until they succeed\n\nThe fact that everything is gated on auth and that auth will put the breaks on when there's a 401 will prevent runaway retries. I've added \"fetching\" flags to all the stores so that attempts don't stack, and there is only ever 1 \"retrying\" task in flight for a store at one time. \nThere is currently no special handling for 400 range errors. Arguably the request should abort, but doing so will leave the client is an invalid state since there is no error state UX at the moment. \nTechnically none of the requests the client makes should result in a 400 though. If that occurs, then it's programmer error."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#pullrequestreview-980739126", "body": ""}
{"comment": {"body": "Don't allow a retry if the task was cancelled. Abort immediately", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#discussion_r878599116"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#pullrequestreview-980739527", "body": ""}
{"comment": {"body": "This is necessary to ensure we're actually waiting on a refresh result. The auth task only takes care of initial setup, and then the result is memoized", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#discussion_r878599439"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#pullrequestreview-980739944", "body": ""}
{"comment": {"body": "Calling this when the hub is opened to ensure the latest data is always available. It's a safety measure - we may be able to remove this ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#discussion_r878599820"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#pullrequestreview-980740045", "body": ""}
{"comment": {"body": "This fetching pattern is used in a few places and could be abstracted, but the logic is not exactly the same in all cases. Feels like a Series B problem", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#discussion_r878599934"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#pullrequestreview-980745284", "body": ""}
{"comment": {"body": "This is a little bit janky because it doesn't guarantee that the upstream caller has the latest response off the network. It's not wired up as a CVS so there is no push action here. But that should be ok since this is called every time threads refresh, and teams don't change that often....\r\n\r\nThere's really only two way to resolve this issue:\r\n- load off network on the first time (previous behaviour), which blocks the interface from loading until the Hub is network connected\r\n- wire teams up as a CVS, which would have the effect of bubbling combine up through the other fetchers. This makes things pretty messy", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#discussion_r878602761"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#pullrequestreview-980745866", "body": ""}
{"comment": {"body": "Yes you are reading this right - it will retry essentially forever. But this is ok because all things will eventually block on token refresh (which will also retry forever until the refresh token expires)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1392#discussion_r878603664"}}
{"title": "Build hub and extension for each installer build", "number": 1393, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1393"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1393#pullrequestreview-986895268", "body": ""}
{"comment": {"body": "I'm guessing we should remove this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1393#discussion_r883158155"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1393#pullrequestreview-986895502", "body": ""}
{"comment": {"body": "(after this is merged it won't matter I guess)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1393#discussion_r883158334"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1393#pullrequestreview-986895983", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1393#pullrequestreview-986899072", "body": ""}
{"comment": {"body": "Oh yeah, thanks. Harmless once this is merged and deleted, but I'll clean up in another PR I've planned to save us some minutes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1393#discussion_r883161332"}}
{"title": "Lookup ThreadRank from SocialNetworkModel on thread creation", "number": 1394, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1394", "body": "Replaces random ThreadRank with value from SocialCommentNetwork."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1394#pullrequestreview-980741486", "body": ""}
{"title": "update", "number": 1395, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1395"}
{"title": "Grid visualization of social comment network", "number": 1396, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1396", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1396#pullrequestreview-980755975", "body": ""}
{"title": "Admin web triggers ThreadRank backfill", "number": 1397, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1397", "body": "Very very inefficient, but I don't care -- just for backfilling once off.\nPG still handles it in a few seconds.\nAfter this lands, people will see recommended threads in dashboard."}
{"title": "Re-add search to dashboard and VSCode", "number": 1398, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398#pullrequestreview-980780545", "body": ""}
{"comment": {"body": "This is basically ValueCacheStream without any typing assumptions.  I think we should probably migrate everything to this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398#discussion_r878637778"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398#pullrequestreview-983700468", "body": ""}
{"comment": {"body": "what's the plan here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398#discussion_r880842513"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398#pullrequestreview-983701342", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398#pullrequestreview-983768729", "body": ""}
{"comment": {"body": "Ah yeah, I'm working on a PR that fixes this (basically gets all the web extension working)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398#discussion_r880890070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398#pullrequestreview-983802319", "body": ""}
{"comment": {"body": "test test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1398#discussion_r880913993"}}
{"title": "Pop open hub on first load", "number": 1399, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1399"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1399#pullrequestreview-980781442", "body": ""}
{"title": "Update Stories", "number": 14, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/14"}
{"title": "Successful responses dont require setting status code 200", "number": 140, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140", "body": "Lets simplify the code here"}
{"comment": {"body": "Oof. Auto merge fail \ud83e\udd26\u200d\u2642\ufe0f\r\nCouple of minor comments here @davidkwlam", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140#issuecomment-1022889569"}}
{"comment": {"body": "That's on me for enabling auto merge :). I'll address in another PR. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140#issuecomment-1022891850"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140#pullrequestreview-864419477", "body": ""}
{"comment": {"body": "201?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140#discussion_r793286012"}}
{"comment": {"body": "Not sure what the value of 422 is. Just use 400 IMO.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140#discussion_r793286261"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140#pullrequestreview-864425912", "body": ""}
{"comment": {"body": "https://github.com/Chapter2Inc/codeswell/pull/149", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140#discussion_r793290632"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140#pullrequestreview-864425936", "body": ""}
{"comment": {"body": "https://github.com/Chapter2Inc/codeswell/pull/149", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/140#discussion_r793290653"}}
{"title": "Do not expose internal exceptions in API responses", "number": 1400, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1400"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1400#pullrequestreview-980834686", "body": ""}
{"title": "StatusPages cleanup", "number": 1401, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1401"}
{"title": "Relabel Team to Recommended in WebExt and VSCode", "number": 1402, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1402"}
{"comment": {"body": "> Does this need to be comprehensive (variable renaming etc)?\r\n\r\ntook a quick look, but looked involved, so not for now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1402#issuecomment-1133653438"}}
{"comment": {"body": "I don't feel strongly that we need to rename everything.  \"Teams\" is not a bad name for this to begin with, and we may change this label again soon enough.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1402#issuecomment-1135408965"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1402#pullrequestreview-980838595", "body": "Does this need to be comprehensive (variable renaming etc)?"}
{"title": "Add contentBasedDeduplication option", "number": 1403, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1403", "body": "I've often asked myself what life would be like without @matthewjamesadam @mahdi-torabi @pwerry @davidkwlam @kaych @dennispi @richiebres @jeffrey-ng @benedict-jw @kaych\nIt would not be as fun, that's for sure."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1403#pullrequestreview-980849849", "body": ""}
{"title": "PR creator is a PR thread participant", "number": 1404, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1404", "body": "Comments made on a PR should include the PR creator as a thread participant.\nThe comment is made on the creator's PR so it is relevant to the creator,\nnot just the user who started the comment.\nFrom ."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1404#pullrequestreview-981143329", "body": ""}
{"title": "Recommendations use 16 week decay constant", "number": 1405, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1405", "body": "Using 4 month (16w) decay constant, because 1 month (4w) decay was much too aggressive.\nAfter N Months | 4 Month Decay    | 1 Month Decay\n-----------------|------------------|----------------\n0                | 1.000            | 1.000\n1                | 0.939            | 0.368\n2                | 0.779            | 0.018\n3                | 0.570            | 0.000\n4                | 0.368            | 0.000\n5                | 0.210            | 0.000\n6                | 0.105            | 0.000\nFrom ."}
{"title": "[RFC] Apply ThreadRank to every thread and remove minimum filter", "number": 1406, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1406", "body": "Downside is that ThreadRankModel is now dense and scales with number of\nthreads and team members, so the storage complexity is now O(N*T),\nwhere N is thread count and T is team size.\nAlso takes longer to run recommendation engine because it has to\npersist a lot more data (mostly rows with weight 0.0) to DB.\nRecommending all threads is conceptually inconsistent. An alternative solution\nwould be to incorporate signals other than SocialCommentNetwork. For\nexample, recommend threads for files that you have created insight bubbles\nfor, or recommend threads from people that you have interacted with in\nprevious Unblocked conversations.\nFrom ."}
{"comment": {"body": "Taking a different approach:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1517", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1406#issuecomment-1141466370"}}
{"title": "Implements team invites, missing error states", "number": 1407, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1407", "body": "Currently working for thread view invites. Adding to onboarding next"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1407#pullrequestreview-988242294", "body": ""}
{"title": "Debug pusher when threads change", "number": 1408, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1408", "body": "Attempt to debug this:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1408#pullrequestreview-982598807", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1408#pullrequestreview-982599498", "body": ""}
{"title": "Drop lock to 1 minute expiration", "number": 1409, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1409", "body": "And allow pull request ingestion to renew at the end of each loop.\nThis is to prevent PR ingestion from being locked out for 10 minutes if the SCM service is killed for whatever reason."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1409#pullrequestreview-985039419", "body": ""}
{"title": "Add codegen to vscode", "number": 141, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141", "body": "Add api codegen to vscode/ directory"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865195663", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865345648", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865350509", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865354946", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865356447", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865362502", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865365583", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865388695", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865390299", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865392458", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865393441", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#pullrequestreview-865475257", "body": ""}
{"comment": {"body": "No need for this in vscode world.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/141#discussion_r794031725"}}
{"title": "Show absolute time in admin web", "number": 1410, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1410", "body": "help with debugging time-related issues, like pusher updates\nalso fix this page, which currently fails to load;\n  "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1410#pullrequestreview-983531881", "body": ""}
{"title": "Clean up stuff", "number": 1413, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1413", "body": "Move specific sqs models out of aws library"}
{"title": "Archive outdated threads for closed PRs", "number": 1414, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1414", "body": "Also adds an isOutdated property to ThreadModel"}
{"comment": {"body": "We need to expose _why_ the thread was archived in the archived thread UI. It's supposed to say one of: \"_archived by Pete 3d ago_\" or \"_auto-archived 3d ago_\".\r\n\r\nWas thinking this could be modelled by adding an `archivedBy`, which would be a nullable `TeamMember` ID; and if null then it's been auto-archived by the system.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1414#issuecomment-1136281502"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1414#pullrequestreview-983653835", "body": "this is great"}
{"comment": {"body": "nice", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1414#discussion_r880809044"}}
{"comment": {"body": "not sure I understand this one", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1414#discussion_r880811403"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1414#pullrequestreview-983665257", "body": ""}
{"comment": {"body": "It's an edge case:\r\n\r\n1.) PR thread goes outdated due to a push\r\n2.) Unblocked updates the thread by marking it outdated\r\n3.) PR is merged\r\n4.) Unblocked archives the thread\r\n5.) Customer un-archives the thread\r\n6.) Someone adds a reply\r\n7.) Unblocked re-archives the thread", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1414#discussion_r880817570"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1414#pullrequestreview-983701050", "body": ""}
{"comment": {"body": "I see. Ok, then \u2014 similar to https://github.com/NextChapterSoftware/unblocked/pull/1414#issuecomment-1136281502 \u2014 we might need to introduce { `thread.unarchivedBy`, `thread.unarchivedAt` } fields, or something like that. If `thread.unarchivedBy` is non-null then don't auto-archive.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1414#discussion_r880842960"}}
{"title": "Cleanup serializer usages", "number": 1415, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1415", "body": "Weve been using two types of serializers:\n1. The standard Json.calls\n2. The custom .encode/.decode calls that add support for UUID etc. serialization\nWere now standardizing to use the latter and moving away from string conversions to/from UUID which is not a great practice.\nAlso adding custom lint rule to prevent this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1415#pullrequestreview-983740109", "body": ""}
{"title": "Use fat threads in web extension", "number": 1416, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1416"}
{"comment": {"body": "Renamed a bunch of `thread` to `threadInfo` where it would help with legibility.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1416#issuecomment-1136430041"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1416#pullrequestreview-983833478", "body": "lg aside from the one question about the search"}
{"comment": {"body": "does this not need `search.query` anymore?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1416#discussion_r880937910"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1416#pullrequestreview-983850202", "body": ""}
{"comment": {"body": "Piping query throughout all the streams was causing a lot of stream typing problems, and was only used for a temporary label where I didn't think it was super valuable.  I'm going to talk to Ben when he gets back and make sure we need this...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1416#discussion_r880948165"}}
{"title": "[Onboarding] Stepper component", "number": 1417, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1417", "body": "\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1417#pullrequestreview-983808609", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1417#pullrequestreview-983973069", "body": "Nice "}
{"title": "Add Repo reference to Thread", "number": 1418, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1418", "body": "First part of a multi-part change. This change:\n- adds repo reference as nullable to thread\n- sets the repo reference on thread during API thread creation\n- sets the repo reference on thread during PR ingest thread creation\nNext changes:\n- add migration to backfill repoID on thread from SourceMark\n- make repo reference non-nullable on Thread\n- remove unnecessary joins on SourceMarkModel for all thread-related SQL queries\n- remove migration\nMotivation\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1418#pullrequestreview-983823139", "body": ""}
{"title": "I need to trigger a whole bunch of test builds with workflow changes.", "number": 1419, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1419", "body": "This is temp while I get things working under a docker environment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1419#pullrequestreview-983841186", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1419#pullrequestreview-983855509", "body": ""}
{"title": "Fix up openapi", "number": 142, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/142", "body": "This pr fixes all the high severity issues with our openspec api and actually caught a bug!\nWe will eventually fix the lower priority ones as well.\nIt also does two things:\n1. Makes sure the task is always run (regardless of whether gradle input has not changed)\n2. Make sure compile task is dependent on lint task."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/142#pullrequestreview-864299450", "body": ""}
{"comment": {"body": "bug!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/142#discussion_r793193940"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/142#pullrequestreview-864299482", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/142#pullrequestreview-864302844", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/142#pullrequestreview-864302935", "body": ""}
{"comment": {"body": "Me again ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/142#discussion_r793196564"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/142#pullrequestreview-864303018", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/142#pullrequestreview-864311385", "body": ""}
{"title": "cleanup", "number": 1420, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1420"}
{"title": "Migration to backfill repoID on thread from SourceMark", "number": 1421, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1421", "body": "Ran it locally, and worked well."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1421#pullrequestreview-984207045", "body": ""}
{"comment": {"body": "Oolala UPDATE JOIN", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1421#discussion_r881213086"}}
{"title": "Create API endpoint for triggering PR ingestion at onboarding", "number": 1422, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1422", "body": "First pass at creating an endpoint for the clients to call during onboarding. Next PR will take a look at the team member calling this endpoint and trigger ingestion for their PRs first.\nThis endpoint is safe to retry."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1422#pullrequestreview-983867087", "body": ""}
{"title": "GitHub PR reviews API", "number": 1423, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1423", "body": ""}
{"comment": {"body": "close for now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1423#issuecomment-1149190229"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1423#pullrequestreview-983888913", "body": ""}
{"comment": {"body": "will probably dump this on new PR model\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1423#discussion_r880977504"}}
{"title": "Add centralized utility for generating dashboard urls", "number": 1424, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1424"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1424#pullrequestreview-983934396", "body": ""}
{"comment": {"body": "Builder is kind of an anti-pattern in Kotlin (see https://stackoverflow.com/a/36150989). Could be data class with default parameters, and a computable property instead of the `build()` function. Apart from being terser, advantage of data class is that you get \u201ccopy\u201d and \u201cequality comparisons\u201d for free.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1424#discussion_r881027686"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1424#pullrequestreview-983943383", "body": ""}
{"title": "Singularize endpoint", "number": 1425, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1425", "body": "We have a combination of plural vs singular on dashboard endpoints.\nWe should choose one or the other.\nSingular for dashboard.\nStrangely, we've chosen plural for apiservice. :)"}
{"comment": {"body": "I think we can drop the `repo` from this now, actually.  The clients can get the thread data directly from the team and thread IDs...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1425#issuecomment-1136515896"}}
{"comment": {"body": "> I think we can drop the `repo` from this now, actually. The clients can get the thread data directly from the team and thread IDs...\r\n\r\nDone", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1425#issuecomment-1136520141"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1425#pullrequestreview-983950309", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1425#pullrequestreview-983958134", "body": ""}
{"title": "Add search pull requests", "number": 1426, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1426", "body": "This will be used during onboarding where we want to ingest the onboarding team member's PRs first. \nNote that we need to use the user-to-server token for this request because the team token doesn't work unless we grant read-only access to contents to our GitHub app:\n\nI don't think we want to do that because if we do I think we'll get access to source code:\n"}
{"comment": {"body": "Not sure if you still want to go ahead with this change. One alternative we discussed was to _filter_ each PR from the V1 stage into either a priority-Q or a regular-Q. Put in priority-Q if:\r\n\r\n- PR author is a current team member; and\r\n- PR repo is a \"prioritized\"  repo", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1426#issuecomment-1137654642"}}
{"title": "Add client version and agent headers", "number": 1427, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1427", "body": "Add the following headers to all TS client requests:\n* X-Unblocked-Client-Version: the client version (right now, the commit SHA)\n* X-Unblocked-Client-Agent: the client app (hub, web-extension, web-dashboard, vscode)\nThis was a bit more involved then I thought it would be:\n* For some reason all of the runtime Environment code and types were in /sharedConfigs, which makes no sense.  I moved them into /shared/config and mapped @config to that folder.\n* Added DefinePlugin definitions for each app to define app type and commit SHA.\n* Removed mergeWithCustomize usage in VSCode as it seemed to break DefinePlugin's behaviour\n* Add hooks into BaseAPI, adding a middleware step that adds the headers from the build config"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1427#pullrequestreview-983967112", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1427#pullrequestreview-985026085", "body": ""}
{"title": "Implements hub menu display", "number": 1428, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428", "body": "AppKit and personal pride are juxtaposed to create a solution worthy of Apple's greatest hits.\nThere are two menus in the app: \n1. The main app menu\n\nThis is a pretty gross hack. It has to be rendered as a popover because a regular NSMenu is totally broken within a popover. Additionally it's not possible to get a very custom feel using SwiftUI and NSMenu together without a ton of extra work. But it works, so meh.\n--\n2. The menu bar right-click menu\n\nMore filth. To get this to work properly we have to detect the right click event on the status bar, then quickly swap in the menu and trigger another click. The power of christ compels you!\nRemaining\nNone of the items except logout and quit are wired up. We need to add install an install verification check for the VSCode extension, then link out to the dashboard to do the extension detections dance.\nAdditionally, I need to implement the actual settings window:\n"}
{"comment": {"body": "I have some thoughts about how to refactor all of the popup/menu management, but we'll leave that for later after everything is wired up. I started on it for 30 minutes and broke a bunch of stuff", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#issuecomment-1136691192"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#pullrequestreview-983967029", "body": ""}
{"comment": {"body": "Not used yet, but will be in a follow on PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#discussion_r881059065"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#pullrequestreview-983967065", "body": ""}
{"comment": {"body": "ditto", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#discussion_r881059102"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#pullrequestreview-983974638", "body": ""}
{"comment": {"body": "For some reason async is not enough. We have to defer slightly. I suspect AppKit is not doing runLoop things here and is instead working with global events", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#discussion_r881065157"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#pullrequestreview-983980200", "body": ""}
{"comment": {"body": "I see what you did here!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#discussion_r881069836"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#pullrequestreview-983982935", "body": ""}
{"comment": {"body": "                            wut", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1428#discussion_r881072022"}}
{"title": "Add welcome email", "number": 1429, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1429"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1429#pullrequestreview-984161627", "body": ""}
{"comment": {"body": "Maybe \"Unknown\" or something would be better here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1429#discussion_r881177007"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1429#pullrequestreview-984161857", "body": ""}
{"comment": {"body": "Or fallback to email?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1429#discussion_r881177192"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1429#pullrequestreview-984162246", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1429#pullrequestreview-984164593", "body": ""}
{"comment": {"body": "Yeah. Good point!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1429#discussion_r881179482"}}
{"title": "Fix up naming of pods which was resulting in network controllers etc. being created on every deployemnt", "number": 143, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/143", "body": "We were using fullNameOverride with the name having gitsha.\nYou can't do that, as that will cause the kube controllers to delete and recreate controllers etc."}
{"title": "Remove UnreadStore", "number": 1430, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1430", "body": "This is pretty much dead code now.  Use the existing thread listing to show the unread number, instead of loading all the Unread data."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1430#pullrequestreview-984186366", "body": ""}
{"title": "remove unnecessary joins on SourceMarkModel for all thread-related SQL queries", "number": 1431, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1431", "body": "make repo reference non-nullable on Thread\nremove unnecessary joins on SourceMarkModel for all thread-related SQL queries\nremove migration"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1431#pullrequestreview-984209130", "body": ""}
{"title": "Use fat threads API for web extension source mark renderer", "number": 1432, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1432", "body": "This is the last place that uses the old thread stores for data fetching.  Once this is in I will start cutting out the dead code."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1432#pullrequestreview-985022108", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1432#pullrequestreview-985202121", "body": ""}
{"comment": {"body": "@jeffrey-ng does this look right to you?\r\n\r\nThe `blobUnloaded` event was never being received by the background script, so we would never unsubscribe from any subscriptions when you navigate away from a page.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1432#discussion_r881920009"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1432#pullrequestreview-985244703", "body": ""}
{"comment": {"body": "Makes sense.\r\n\r\nWhenever we remove a listener, we should typically send an event just before to clean things up.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1432#discussion_r881951254"}}
{"title": "Fix notification", "number": 1433, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1433"}
{"title": "Fix admin web Repo>Threads page", "number": 1434, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1434", "body": "Fixes this:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1434#pullrequestreview-984293495", "body": ""}
{"title": "Fix emails", "number": 1435, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1435"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1435#pullrequestreview-984979104", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1435#pullrequestreview-985195608", "body": ""}
{"title": "[Onboarding] Tutorial skeleton and first stage", "number": 1436, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1436", "body": "Add skeleton architecture for the onboarding tutorial steps, with the Import Knowledge steps built out as a proof of concept\n* NOTE: This is barely styled without additional functionality, just bare bones architecture to walk through the first few steps -- will go back over and style properly after\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1436#pullrequestreview-985283613", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1436#pullrequestreview-985284505", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1436#pullrequestreview-985306409", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1436#pullrequestreview-985329092", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1436#pullrequestreview-985338520", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1436#pullrequestreview-985364475", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1436#pullrequestreview-985444128", "body": ""}
{"title": "Add call headers to honeycomb", "number": 1437, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1437", "body": "A wonderful man by the name of @matthewjamesadam added some very helpful headers that we want to include as part of our honeycomb traces!\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1437#pullrequestreview-985304623", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1437#pullrequestreview-985304965", "body": ""}
{"comment": {"body": "What is a `.kt.kt` file?  Is this a mistake and this file should be renamed/removed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1437#discussion_r881994973"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1437#pullrequestreview-985305124", "body": ""}
{"comment": {"body": "oops", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1437#discussion_r881995110"}}
{"title": "Split PR ingestion into separate jobs", "number": 1439, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1439", "body": "First step towards prioritizing ingesting PRs for the onboarding customer + repo"}
{"title": "Jeff/integrate auth", "number": 144, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/144"}
{"comment": {"body": "Closed in favour of https://github.com/Chapter2Inc/codeswell/pull/155", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/144#issuecomment-1023744869"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/144#pullrequestreview-864342343", "body": ""}
{"comment": {"body": "Should ideally be same origin", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/144#discussion_r793226767"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/144#pullrequestreview-864342394", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/144#pullrequestreview-864342610", "body": ""}
{"comment": {"body": "Should be false", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/144#discussion_r793227004"}}
{"title": "Remove ThreadStore", "number": 1440, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1440", "body": "Remove the old ThreadStore and RepoThreadStore.\nNote that thread mutation operations are still in ThreadStore.ts.  We can move them later if we want."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1440#pullrequestreview-985346847", "body": ""}
{"title": "Create API endpoint to allow client to give a hint to the server about which repo to ingest PRs first", "number": 1441, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441", "body": "Called by the client as early as possible during onboarding so that PR ingestion can begin."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-985358309", "body": ""}
{"comment": {"body": "Main change here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#discussion_r882034060"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-985365253", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-985366743", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-985367078", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-985367603", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-985502979", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-985503046", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-985504542", "body": ""}
{"comment": {"body": "I could add a unique index for this but didn't want o over optimize until we have a better sense of how this will be used", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#discussion_r882139645"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-986840021", "body": "API looks fine to me."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-986852164", "body": ""}
{"comment": {"body": "should be list right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#discussion_r883125786"}}
{"comment": {"body": "maybe add to the list if it exists?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#discussion_r883126251"}}
{"comment": {"body": "I see now, you're creating a row for each url. might be easier to just encode the URL as string list.\r\n\r\nWe have precedent for this in `SourcePointModel.snippetJson`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#discussion_r883129829"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-986864988", "body": ""}
{"comment": {"body": "Yeah, I'm just unclear how this new model might be used later. If the idea was to use this as away to add additional properties/metadata/attributes then putting all repos on the same model means we lose the ability to apply per-repo properties.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#discussion_r883136505"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-986869313", "body": ""}
{"comment": {"body": "interesting. I have no idea how this is going to evolve yet.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#discussion_r883140094"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#pullrequestreview-*********", "body": ""}
{"comment": {"body": "let's just get it in, and we can iterate later when things settle down \u2705 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1441#discussion_r883140411"}}
{"title": "Add hasAccount to TeamMember API", "number": 1442, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442", "body": "Motivation: to support .\nAlso remove the includeDeactivated query parameter (aka isCurrentMember); there really is no valid\nuse case for excluding deactived team members, and some clients (eg: Hub) were not including these\nwhich would cause a bug when rendering past discussions with deactivated team members."}
{"comment": {"body": "so we're no longer returning team members that are !isCurrentMember ? \r\n\r\nI think this means you also need to update the `DiscussionThreadParticipantSection` file and change the boolean from `teamMember.isCurrentMember` to `teamMember.hasAccount` on line 10 (the code renders a different icon in vscode for users that dont have accounts) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442#issuecomment-**********"}}
{"comment": {"body": "> so we're no longer returning team members that are !isCurrentMember ?\r\n\r\nWe are. We just _always_ return them now.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442#issuecomment-**********"}}
{"comment": {"body": "> I think this means you also need to update the `DiscussionThreadParticipantSection` file and change the boolean from `teamMember.isCurrentMember` to `teamMember.hasAccount` on line 10 (the code renders a different icon in vscode for users that dont have accounts)\r\n\r\n@kaych Thanks! Added a separate status in https://github.com/NextChapterSoftware/unblocked/pull/1442/commits/dbac7c44ea77416b6770164bc6eb659f1fe5575c. lmk if that looks ok?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442#pullrequestreview-*********", "body": ""}
{"comment": {"body": "you could probably define the user first to avoid repetition\r\nie\r\n```\r\nconst user = { name: displayName, iconUrl: avatarUrl };\r\n...\r\nreturn <StatusIcon user={user} ... \r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442#discussion_r883010517"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1442#pullrequestreview-986698963", "body": ""}
{"title": "Setup code for Installations Wizard", "number": 1443, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443", "body": "Conditionally render project selector based on installation state.\n\nPoll for installation data and populate view\n\nFinal styling is still TODO. Should come soon as we consolidate onboarding css."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#pullrequestreview-985518558", "body": ""}
{"comment": {"body": "Temporarily until we API is implemented.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#discussion_r882150299"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#pullrequestreview-985552367", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#pullrequestreview-985552578", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#pullrequestreview-986725944", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#pullrequestreview-986727683", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#pullrequestreview-986737137", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#pullrequestreview-986741631", "body": ""}
{"comment": {"body": "I'm guessing this is where we'll add a call to `refresh()` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#discussion_r883041623"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1443#pullrequestreview-986754963", "body": "Good to go once a couple things are addressed"}
{"title": "Add web urls to API", "number": 1444, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1444"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1444#pullrequestreview-985479894", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1444#pullrequestreview-985488045", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1444#pullrequestreview-985489523", "body": ""}
{"title": "Stop channel polling when focus lost in dashboard/VSCode", "number": 1445, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1445", "body": "For VSCode, channel polling will stop for a given workspace if that workspace is not the focused window (ie, you focus another window, or you minimize the workspace window).\nFor the dashboard, channel polling will stop whenever the dashboard tab loses focus, or whenever the browser window loses focus (ie you focus another window, or minimize the browser).\n@jeffrey-ng I don't know how to handle this in the web extension, off the top of my head.  Any suggestions?\nIt's unclear to me if the browser behaviour is actually what we want.  It's pretty common to keep browser windows visible by tiling windows or having them side-by-side, while you work on other things, and if those windows don't update it might be surprising.  If that's the case, we should be able to use the page visibility API instead of the focus API:  -- this will tell us if the window is completely invisible (viewing another tab, or the window is minimized)."}
{"comment": {"body": "We could potentially access the visibility API from the content scripts and notify the background script whenever this updates? Would require keeping track of which tab is \"visible\"\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1445#issuecomment-1138847668"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1445#pullrequestreview-986580180", "body": ""}
{"comment": {"body": "I think this should live within Web and not shared?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1445#discussion_r882925457"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1445#pullrequestreview-986580283", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1445#pullrequestreview-986591115", "body": ""}
{"comment": {"body": "I put it in shared because I thought it might be used by the web extension as well, but ... maybe not?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1445#discussion_r882933380"}}
{"title": "Single git hub client for all background jobs", "number": 1446, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1446", "body": "Fewer GitHub client instances in this case, so maybe it'll help with memory issues. Does clean up the code though."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1446#pullrequestreview-985578657", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1446#pullrequestreview-985578726", "body": ""}
{"title": "Update Person Spec for Tutorial", "number": 1447, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1447", "body": "Add hasSeenTutorial flag in person model.\nAdd updatePersonTutorial PUT request."}
{"comment": {"body": "Open to naming suggestions...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1447#issuecomment-1137897587"}}
{"comment": {"body": "This might or might not be related as well to some api endpoints david is adding for onboarding.\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1441/files\r\n\r\nThis seems fine to me....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1447#issuecomment-1137899276"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1447#pullrequestreview-985532659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1447#pullrequestreview-985537117", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1447#pullrequestreview-985573339", "body": ""}
{"title": "Record who archived a thread", "number": 1448, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1448"}
{"comment": {"body": "is there any client work needed for this? ie `Archived by: ...` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1448#issuecomment-1138774307"}}
{"comment": {"body": "> is there any client work needed for this? ie `Archived by: ...` ?\r\n\r\nYup, not included in this PR. Also the terminology will need to be changed as per https://chapter2global.slack.com/archives/C02GEN8LFGT/p1652393321894119\r\n\r\nTracking as issue https://github.com/NextChapterSoftware/unblocked/issues/1462 @kaych ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1448#issuecomment-1138789006"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1448#pullrequestreview-986505062", "body": ""}
{"comment": {"body": "We do check that the thread is in the team (and auth should handle checking the caller is a member of the team) but I guess we don't validate that the caller is the author", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1448#discussion_r882875913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1448#pullrequestreview-986519427", "body": ""}
{"comment": {"body": "Correct. The authorization check for deleting threads is only client-side right now, which is not enough. Am planning to do this in another PR later today.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1448#discussion_r882882167"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1448#pullrequestreview-986537994", "body": ""}
{"comment": {"body": "I think we have the same problem for deleting messages", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1448#discussion_r882896264"}}
{"title": "Hook up poller \"before\" cursor to threadBundleStore", "number": 1449, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1449", "body": "Clients are now free to pass before query parameter to pusher for the /threads/mine and /threads/recommended channels; in addition to if-modified-since as before.\nDesign here: \nFuture (hopefully before launch ), we should structure the poller APIs so that they take require arguments, as the channel part of the API is totally unstructured  it's just a string."}
{"comment": {"body": "> Future (hopefully before launch \ud83d\ude2c), we should structure the poller APIs so that they take require arguments, as the `channel` part of the API is totally unstructured \u2014 it's just a string.\r\n\r\nI remember a bunch of us talked about this back when we first built out the channel polling API... any thoughts on how this could be done?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1449#issuecomment-1138013942"}}
{"comment": {"body": "> > Future (hopefully before launch \ud83d\ude2c), we should structure the poller APIs so that they take require arguments, as the `channel` part of the API is totally unstructured \u2014 it's just a string.\r\n> \r\n> I remember a bunch of us talked about this back when we first built out the channel polling API... any thoughts on how this could be done?\r\n\r\nOpenAPI generators don\u2019t allow for polymorphism, which is what we need. Don\u2019t have any good ideas, apart from a brute force approach with a set of keys to lists of well structured objects. Like this:\r\n\r\n```\r\nChannels:\r\n- repoChannels: List<RepoChannel>\r\n- myThreadChannels: List<MyThreadChannel>\r\n- teamMemberChannels: List<TeamMemberChannel>\r\n\u2026\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1449#issuecomment-1138077505"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1449#pullrequestreview-985605790", "body": ""}
{"title": "Add title property to chat API model", "number": 145, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/145", "body": "https://github.com/Chapter2Inc/codeswell/pull/122#discussion_r791938298"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/145#pullrequestreview-864415435", "body": ""}
{"title": "Hub preferences", "number": 1450, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-985582289", "body": ""}
{"comment": {"body": "Temporary until we get the dashboard and github links into `ThreadInfo`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r882200216"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-985582944", "body": ""}
{"comment": {"body": "Will do something like this:\r\n`\"\\(dashboardUrl.appendingQueryParameter(githubUrl))\"` ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r882200713"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-985586415", "body": ""}
{"comment": {"body": "Done here https://github.com/NextChapterSoftware/unblocked/pull/1444", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r882203594"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-985592775", "body": ""}
{"comment": {"body": "Follow on: https://github.com/NextChapterSoftware/unblocked/pull/1452", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r882209065"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-985703617", "body": ""}
{"comment": {"body": "Isn't the normal way to do this to use the `onChange` View decorator?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r882299103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-985704177", "body": ""}
{"comment": {"body": "Yowza.  Maybe reformat this kind of thing in the future?  It's pretty much impossible to parse on a single line.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r882299515"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-985718771", "body": ""}
{"comment": {"body": "More readable but not any less gross: https://github.com/NextChapterSoftware/unblocked/pull/1456\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r882310836"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-985718913", "body": ""}
{"comment": {"body": "Also - my link didn't render as a link :( \n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r882310963"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-985719764", "body": ""}
{"comment": {"body": "Yeah but then you have to do it on the view. This way you can do it directly on the binding. I find it reads a little better, but if it feels wrong I can revert", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r882311681"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-986790361", "body": ""}
{"comment": {"body": "It feels a bit odd to manually handle these events and make calls ... wouldn't the idiomatic way to do this be to have this View run against a binding in the UserPreferences object?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r883078390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#pullrequestreview-986804493", "body": ""}
{"comment": {"body": "Yup that's a good point. I'll try to wire that up", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1450#discussion_r883088977"}}
{"title": "Clean up PR ingestion logic", "number": 1451, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1451"}
{"title": "Use fat thread links", "number": 1452, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1452"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1452#pullrequestreview-985593163", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1452#pullrequestreview-985595905", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1452#pullrequestreview-985598043", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1452#pullrequestreview-985598161", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1452#pullrequestreview-985617148", "body": ""}
{"title": "Use server urls", "number": 1453, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1453", "body": "Follow on from https://github.com/NextChapterSoftware/unblocked/pull/1444"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1453#pullrequestreview-985601398", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1453#pullrequestreview-985601579", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1453#pullrequestreview-985602774", "body": ""}
{"title": "Make app open less obnoxious", "number": 1454, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1454"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1454#pullrequestreview-985598314", "body": ""}
{"title": "Use pre-loaded thread data when opening discussion views in VSCode", "number": 1455, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1455", "body": "In most cases, we already have the thread data available:\n* Sidebar already has fat threads loaded\n* Text editor gutter items already have fat threads loaded\nso we can view whatever data we have, while loading and channel polling in the background.  The result is very quick thread views.  The first view can still take a few seconds, after that they're all sub-second.\n@jeffrey-ng @kaych for the sidebar I'm doing what we were doing before, and funneling the ThreadInfoAggregate data through the sidebar webview.  This is a pretty sad way of doing this.  LMK if you think this is an immediate problem, otherwise I think we should refactor this some other time.\nNext PR will warm up the sourcemark engine for each repo on startup, which should remove the remaining initial lag."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1455#pullrequestreview-985687882", "body": "Lgtm"}
{"title": "Hi Matt :)", "number": 1456, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1456"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1456#pullrequestreview-986469998", "body": ""}
{"title": "Pull request ingestion during onboarding uses its own queue", "number": 1457, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1457", "body": "PRs ingested during onboarding should use their own queue so that we can prioritize the onboarding customer's PRs over others.\nNeeds the queue to be created first."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1457#pullrequestreview-*********", "body": "Looks good but the first deployment will fail. Once this is merged I need to deploy changes to service accounts manually (eksctl limitation)"}
{"title": "Fix bug where before cursor only returned unread threads", "number": 1458, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1458", "body": "Logic when dealing with UN-read is head-wrecking. Tests prove it works now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1458#pullrequestreview-*********", "body": ""}
{"title": "Revert \"temp fix while I address a bug\"", "number": 1459, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1459", "body": "Reverts NextChapterSoftware/unblocked#1365"}
{"title": "Add rabc rules for deployer", "number": 146, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/146"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/146#pullrequestreview-*********", "body": ""}
{"title": "Reduce parallelization", "number": 1460, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1460"}
{"title": "Revert \"Revert \"temp fix while I address a bug (#1365)\" (#1459)\"", "number": 1461, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1461", "body": "This reverts commit 5a58a3c64840a2d0393120c667a893735c4f7768."}
{"title": "Disabling flaky tests", "number": 1463, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1463"}
{"title": "Fix logic for getting commit and line range", "number": 1464, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1464", "body": "I introduced a bug yesterday where we're not returning the right line range for the commit. Problem is that the call to get commit and line range are separate, when they are really a package. \nIf the PR is merged, then we want the sha of the merge commit plus the final line range (comment.line).\nIf the PR is open, then we want the sha of the original commit plus the original line range (comment.originalLine)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1464#pullrequestreview-986867037", "body": "nice"}
{"title": "Schema fix", "number": 1465, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1465", "body": "My bad. Didn't review properly."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1465#pullrequestreview-986645344", "body": ""}
{"title": "Change default version for hub builds", "number": 1466, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1466"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1466#pullrequestreview-986658879", "body": ""}
{"title": "Lint out delays in tests", "number": 1467, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1467"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1467#pullrequestreview-986660873", "body": "Guilty"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1467#pullrequestreview-986661891", "body": ""}
{"title": "Fix delay style tests and un-disable", "number": 1468, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1468"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1468#pullrequestreview-986668065", "body": ""}
{"title": "fix missing permissions for notification service", "number": 1469, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1469", "body": "This fixes the permission error in Notification service. Prod deployments should start working after this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1469#pullrequestreview-986695093", "body": ""}
{"title": "Add body property to message API model", "number": 147, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/147", "body": "https://github.com/Chapter2Inc/codeswell/pull/122/files#r791938511"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/147#pullrequestreview-864369618", "body": ""}
{"title": "Warm up sourcemark engine on startup", "number": 1470, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470", "body": "Whenever the repo list changes, tell the SME to process that repo, by referencing the calculator for that repo.  This makes the first view for a thread pretty quick."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#pullrequestreview-986668972", "body": ""}
{"comment": {"body": "@richiebres is this OK?  This uses the htmlUrl from a Repo as the clone URL?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#discussion_r882988162"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#pullrequestreview-986670426", "body": ""}
{"comment": {"body": "I think we don't need to worry about unsubscribing here, because we want to monitor this while the workspace is open...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#discussion_r882989772"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#pullrequestreview-986676292", "body": ""}
{"comment": {"body": "when would this ever _change_? maybe we just need this to be initialized?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#discussion_r882994014"}}
{"comment": {"body": "Calculator doesn't actually look at this value. All good.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#discussion_r882995861"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#pullrequestreview-986679859", "body": ""}
{"comment": {"body": "oh, I see, you thinking when one of the workspace repos has _not_ been onboarded, but then a user installs the Unblocked GitHub app on the repo.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#discussion_r882996731"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#pullrequestreview-986681133", "body": ""}
{"comment": {"body": "make sense. but are these listeners cleaned up at least when the workspace/folder is closed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#discussion_r882997763"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#pullrequestreview-986714310", "body": ""}
{"comment": {"body": "Yeah -- the extension lifetime (the entire JS environment) is tied to the workspace so everything is destroyed on shutdown.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#discussion_r883022681"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#pullrequestreview-986716696", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#pullrequestreview-986781582", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1470#pullrequestreview-986868685", "body": ""}
{"title": "Use fat thread cursors", "number": 1471, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1471"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1471#pullrequestreview-986754227", "body": ""}
{"comment": {"body": "@matthewjamesadam these are the fixes that solve the window preference issues you pointed out this morning", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1471#discussion_r883051457"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1471#pullrequestreview-986772218", "body": ""}
{"title": "Separate IPC port user defaults for each env", "number": 1472, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1472", "body": "Debugging VSCode against dev (my normal workflow) was getting annoying, because on each run it would connect to the installed hub app (running against prod), and try to use its token against the dev env, which would fail.  So I'd have to log in.\nThis PR adds a per-environment user defaults key for the port, so VSCode will only use tokens for the right env."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1472#pullrequestreview-986868037", "body": ""}
{"comment": {"body": "This works well. Wonder if it would be weird to do something like:\r\n```swift\r\nextension String {\r\n    func withEnvironmentPrefix() -> String {\r\n        \"\\(UnblockedEnvironment.environment().name)-\\(self)\"\r\n    }\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1472#discussion_r883139022"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1472#pullrequestreview-986868159", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1472#pullrequestreview-986869265", "body": ""}
{"comment": {"body": "I thought about that!  But I've generally shied away from adding niche functionality to String, as it adds all kinds of junk to the String class, and in the IDE if you type in `myString.`, the suggestion menu can get super cluttered.  I'm happy to do it if you feel it's better though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1472#discussion_r883140043"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1472#pullrequestreview-987708409", "body": ""}
{"comment": {"body": "Nope I had the same thought. Better to keep thing narrowly scoped. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1472#discussion_r883752057"}}
{"title": "Bump marketing version to experiment with actions", "number": 1473, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1473"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1473#pullrequestreview-986846668", "body": ""}
{"title": "Change coment label", "number": 1474, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1474", "body": "As per "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1474#pullrequestreview-986860429", "body": ""}
{"title": "Make preferences window floating", "number": 1475, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1475", "body": "This makes the preferences window stay on top of all other windows until it is closed or minimized"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1475#pullrequestreview-986914142", "body": ""}
{"title": "[Onboarding] Import knowledge steps", "number": 1476, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1476", "body": "\n\n\n\n\nFunnel in data for the first tutorial section (Import Knowledge)\nIs happy path (clicking through the flow using the buttons) opens the first thread in the sidebar)\nRefactor openFileEditor functionality out of the DiscussionThreadCommand to a common place to be reused during the tutorial\nAdd condition to turn off opening the file editor when opening a discussion thread (we don't want to do this during the tutorial)\nHandle events in the sidebar triggering the next tutorial view\n\nNOTE:\n* The tutorial currently doesn't handle the sourcemark tooltip click (i.e. opening the thread via the gutter tooltip won't trigger the next tutorial view) -- will layer on support for this since implementing this will be a bit more involved. The flow works fine clicking the tutorial button"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1476#pullrequestreview-986916971", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1476#pullrequestreview-987856593", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1476#pullrequestreview-987928694", "body": ""}
{"title": "Create ThreadUnreads for PR ingested messages after the initial ingestion", "number": 1477, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477", "body": "Don't create any ThreadUnreads during the initial PR ingestion. After the initial ingestion, when a new message is created for:\nA new thread (no other messages): \n- Create a ThreadUnread for the new message author, with lastReadMessage set to the new message\n- Create a ThreadUnread for the PR author, with lastReadMessage set to null (if creator != message author)\nAn existing thread (with existing messages)\n- Upsert a ThreadUnread for each thread participant, with lastReadMessage set to\n  - the new message, for the new message's author\n  - the last message in the thread before new message creating, for all other thread participants\nThe goal here is to the have all participants (minus the new message author) get a notification that the thread has a message that is unread."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#pullrequestreview-986930874", "body": "looks good so far!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#pullrequestreview-989724391", "body": ""}
{"comment": {"body": "I get the search one. But why are the other two outside the trx:\r\n- `threadStore.updateLastMessageCreatedAt(threadId = threadId)`\r\n- `threadUnreadService.setLatestMessageAllThreadUnreads(threadId = threadId)`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#discussion_r885115965"}}
{"comment": {"body": "fallback here is weird. Is there some scenario where this can actually happen, or you just being defensive?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#discussion_r885123903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#pullrequestreview-989807479", "body": ""}
{"comment": {"body": "These functions determine the latest message in the thread by reading from the database. When I stick them in the same transaction used for creating messages, I've observed that the messages haven't been created by the time these functions are called. I think it's because Exposed defers all model inserts to the end of the transaction.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#discussion_r885180538"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#pullrequestreview-989809093", "body": ""}
{"comment": {"body": "Just being defensive", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#discussion_r885181715"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#pullrequestreview-989811676", "body": ""}
{"comment": {"body": "Can you try calling `flushCache()` before these functions; if that works then we can move these into the transaction for consistency.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#discussion_r885183714"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#pullrequestreview-989816889", "body": ""}
{"comment": {"body": "Just tried and it doesn't work. Now I suspect it's because we're using DSL for some inserts.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#discussion_r885187501"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#pullrequestreview-989822869", "body": ""}
{"comment": {"body": "Damn. Ok. Thanks for trying. Would definitely like to address this, but let\u2019s get it in first.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#discussion_r885191880"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1477#pullrequestreview-989823084", "body": ""}
{"title": "use on-demand instances to see if builds get better", "number": 1478, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1478"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1478#pullrequestreview-986929486", "body": ""}
{"title": "Add initial team invite page", "number": 1479, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479", "body": "This is going to involve a few more pull requests, but this is the basic UI for the team invite page.\nWhat this pr does is the following:\n1. Add basic Landing page navigation (this is up in the air, I can possibly modify the existing Home Navigation, but it looks quite different)\n2. Add TeamInvite page with optional users parameter (for storybooks)\n3. Modify UserIconStack component to take in custom maxSize.\nTODO:\n1. Figure out how to get actual team members for an unauthorized user. (no idea)\n2. Integrate some sort of expiry token to Team Invite page.\n3. Make this stuff responsive.\nSTORYBOOK:\n\nLIVE WEBPAGE (WITH NAVIGATION BUT NO USERS YET):\n\nFIGMA:\n"}
{"comment": {"body": "Cool! \ud83c\udf89 \r\n\r\nRight now this is built into the same bundle as the rest of the dashboard.  I'm wondering if we should ship this in its own bundle?  That way it can easily have its own routing table, it doesn't need to boot up auth or anything else... ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#issuecomment-**********"}}
{"comment": {"body": "> Cool! \ud83c\udf89\r\n> \r\n> Right now this is built into the same bundle as the rest of the dashboard. I'm wondering if we should ship this in its own bundle? That way it can easily have its own routing table, it doesn't need to boot up auth or anything else...\r\n\r\nI'm curious what the implications are when you create another bundle in terms of will it just work if we deploy it to s3 with our existing web deployment workflow.\r\nI'll try this out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#issuecomment-**********"}}
{"comment": {"body": "> I'm curious what the implications are when you create another bundle in terms of will it just work if we deploy it to s3 with our existing web deployment workflow. I'll try this out.\r\n\r\nIt should work fine.  The main reason for making a separate bundle would be to allow this to live as a separate asset in S3 (with a different URL from the dashboard), and to allow code-spitting, so users don't need to download the entirety of the dashboard just to see the landing page.\r\n\r\nIt's trivial to try out, just modify the `entry` section in `webpack.base.js`, to point to each separate entry point you want to build.  You can take a look at the VSCode `webpack.base.js` to see how we did it there, for the webviews.\r\n\r\n(If we're OK sending people to the dashboard URL for the landing page, then this may not be worth worrying about for now)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#issuecomment-1139754181"}}
{"comment": {"body": "> > I'm curious what the implications are when you create another bundle in terms of will it just work if we deploy it to s3 with our existing web deployment workflow. I'll try this out.\r\n> \r\n> It should work fine. The main reason for making a separate bundle would be to allow this to live as a separate asset in S3 (with a different URL from the dashboard), and to allow code-spitting, so users don't need to download the entirety of the dashboard just to see the landing page.\r\n> \r\n> It's trivial to try out, just modify the `entry` section in `webpack.base.js`, to point to each separate entry point you want to build. You can take a look at the VSCode `webpack.base.js` to see how we did it there, for the webviews.\r\n> \r\n> (If we're OK sending people to the dashboard URL for the landing page, then this may not be worth worrying about for now)\r\n\r\nNo, you have a solid point. This is a first step towards a proper split.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#issuecomment-1139770139"}}
{"comment": {"body": "Submitted fixes for the iconSize based off what you guys brought up.\r\nI have a few follow up prs:\r\n1. Split into separate bundle.\r\n2. Team Customizations", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#issuecomment-1139937221"}}
{"comment": {"body": "Nicely done! \ud83c\udf89 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#issuecomment-1139975303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986924874", "body": ""}
{"comment": {"body": "This looks like the wrong asset \ud83d\ude2a I think this should be the empty envelope (see the top right section in the email figma) \r\n<img width=\"320\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170604705-0fc16976-8ffc-46c7-8320-dbe91adf7aae.png\">\r\n\r\nAdditionally we'd need to absolutely the org icon over this image so it's customized to each user. I think this requires a bit of additional backend/API work to grab this icon from GitHub\r\n\r\nI think on the interim we can either leave it blank or use the unblocked icon as a placeholder (I do this in the email templates atm)\r\n<img width=\"345\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170604852-aa7ace21-d2d6-4403-96c0-e0547047cf37.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883182086"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986925392", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986925813", "body": ""}
{"comment": {"body": "https://www.figma.com/file/qViiyIpROqSPQF5hspSM7P/Unblocked---VSCode---Onboarding?node-id=1478%3A253696\r\n\r\nThis is a placeholder. I suspect we're going to transition to org asset (going to confirm with Ben).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883182902"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986926396", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986926434", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986926541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986927260", "body": ""}
{"comment": {"body": "<img width=\"66\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170605266-3db74d88-e088-456b-8c27-b80361f62652.png\">\r\nI'm pretty sure the design is meant to depict ^ as the org asset, as in, that's a live asset, not a placeholder icon. Hence I think we could probably front load the layout work now and use the empty envelope since that's what we'll be using in the end anyway", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883184017"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986927871", "body": ""}
{"comment": {"body": "Egads, didn't read your comment well.\r\nNow I get it. \r\nFuck, that thing is going to have to be componentized to layer the org icon on top.\r\nthanks kay.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883184573"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986933938", "body": ""}
{"comment": {"body": "Fixed. Going to have to do this properly in another pr w/regards to overlying team icon.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883189507"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986935736", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-986936583", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-987082524", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-987180114", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-987622385", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-987788648", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-987997242", "body": ""}
{"comment": {"body": "It's unclear to me whether or not this is the better approach vs a separate prop. I don't love that the prop takes two different types but I can see from a external use perspective how this might be simpler ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883919962"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-987999410", "body": ""}
{"comment": {"body": "I think this should work similarly to how the other fns in this file work with the type assertions, ie\r\n`function isCustomSize(size: IconSize | number): size is number {` or `function isIconSize(size: IconSize | number): size is string {`\r\n\r\nand then this way you shouldn't need the typecasting on line 75", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883921136"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-988000471", "body": ""}
{"comment": {"body": "Roger", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883921674"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-988000971", "body": ""}
{"comment": {"body": "Having two variables is confusing IMHO, in particular if someone provides values for two props.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883921979"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-988009414", "body": ""}
{"comment": {"body": "FWIW I have a slight preference for defining the fn as `isCustomSize` because that should be the exceptional case, i.e.\r\n```\r\nif (isCustomSize) { \r\n    // do the exceptional thing and add a style tag \r\n} else { \r\n    // otherwise do the default behaviour \r\n}\r\n```\r\n\r\nFeel free to disagree tho!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883927565"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#pullrequestreview-988013390", "body": ""}
{"comment": {"body": "Completely agree..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1479#discussion_r883929652"}}
{"title": "getSourcemark operation hits database", "number": 148, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/148", "body": "Will wire up putSourcemark to the database in a later PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/148#pullrequestreview-864422471", "body": ""}
{"title": "Ad hoc signing for PR builds", "number": 1480, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1480"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1480#pullrequestreview-987883334", "body": ""}
{"title": "Add abs date to messages and touch up placeholders and icons", "number": 1481, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1481", "body": "Add abs date to each message timestamp (on hover)\nUpdate sidebar icons per new designs\nAdd missing placeholders to search inputs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1481#pullrequestreview-987963028", "body": ""}
{"title": "Reverting API service back to 1 instance", "number": 1482, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1482", "body": "I had scaled API service up during our last incident. It's causing annoying alarms in Dev due to capacity so I am scaling it back to 2. \nI don't want to waste money adding another Kube instance in Dev until we really need it. Right now this is happening because during deployments we 3 active API instances due to the 50% rollover config"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1482#pullrequestreview-987882771", "body": ""}
{"title": "Preflight requests were failing because these headers were not being allowed", "number": 1483, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1483", "body": "Noticed it in local stack."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1483#pullrequestreview-987930275", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1483#pullrequestreview-987930333", "body": ""}
{"title": "Installation styles and functionality", "number": 1484, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484", "body": "Updated styling for Project Selector & org installation.\n\n\nOn ProjectSelection, new workspace should automatically bring up org installation flow on launch. There could be some delay as we wait for the extension to initialize...\nAt the end of installation, we will conditionally start tutorial (currently hard coded to always start tutorial)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-987941815", "body": ""}
{"comment": {"body": "@kaych I'll put more thought into how to refactor these out.\r\nThe only difference right now is the Props for the postMessage navigation event...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#discussion_r883888920"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988174169", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988174326", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988174969", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988177697", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988178288", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988198124", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988198806", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988203410", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988203505", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988208267", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988238833", "body": ""}
{"comment": {"body": "Is there a reason this is async, but the one below isn't?  Also you can just do `cleanupInitialListener?.unsubscribe()` so you don't need to `if` ...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#discussion_r884033776"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1484#pullrequestreview-988241411", "body": "Looks good once the next/prev button stuff is figured out"}
{"title": "Did not know about typescript predicates", "number": 1485, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1485", "body": "Kay's suggesiton. :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1485#pullrequestreview-988021518", "body": ""}
{"title": "Set marketing version", "number": 1486, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1486"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1486#pullrequestreview-988061025", "body": ""}
{"title": "Admin web can clear busy lock", "number": 1487, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1487"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1487#pullrequestreview-988066092", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1487#pullrequestreview-988078081", "body": ""}
{"title": "Add WebviewPanelProxy", "number": 1488, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1488", "body": "Add a new class that acts as a proxy for WebviewPanels.  This is used when panels are shared and reused (by using WebviewPanelTracker).\nThe key problem is that when you use WebviewPanelTracker, multiple clients can end up with access to a WebviewPanel.  They may all call methods on the panel and be in conflict.\nI noticed this was a problem because I noticed that when you viewed a number of threads in a row, without closing the discussion thread UI, we would continue to poll for all the threads that you had ever viewed in that editor.  Only once the editor was closed, would polling for those threads stop.  The problem is that every DiscussionThreadCommand invocation was handling WebviewPanel.onDidDispose, and so would only be notified once the editor was finally closed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1488#pullrequestreview-989550339", "body": ""}
{"title": "Add link to comment footer", "number": 1489, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1489", "body": "Adding icon to start of footer will come in a later PR (requires figuring out where to host the icon)."}
{"comment": {"body": "> requires figuring out where to host the icon\r\n\r\nUse the Unblocked GitHub App icon for consistency? Then GitHub hosts it.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1489#issuecomment-1140060707"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1489#pullrequestreview-988157592", "body": ""}
{"title": "Fix status codes in putSourcemark", "number": 149, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/149", "body": "Addresses comments in https://github.com/Chapter2Inc/codeswell/pull/140"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/149#pullrequestreview-864491823", "body": ""}
{"title": "change ec2 runner to a new version with fallback support", "number": 1490, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1490", "body": "https://github.com/NextChapterSoftware/ec2-action-builder/pull/2\nThis is to address InsufficientCapacity errors thrown by AWS when using Spot instances. I will not merge the PR above until we are confident these new changes are stable."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1490#pullrequestreview-1022305318", "body": ""}
{"comment": {"body": "@matthewjamesadam does this sound right :)\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/246344e5-c546-49e0-bafe-f14ebb5e884c?message=3ce723b1-0752-4c46-8a9c-644558bdae4a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1490#discussion_r908875263"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1490#pullrequestreview-988151296", "body": ""}
{"comment": {"body": "I'd like to keep the main branch in the other repo unchanged for now as to have a revet option. If these changes hold up for a week I will merge the other PR and switch this back to main", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1490#discussion_r883991884"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1490#pullrequestreview-988152900", "body": ""}
{"title": "Set marketing version", "number": 1491, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1491", "body": "Not sure how the last one merged. Must have set to auto :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1491#pullrequestreview-988144446", "body": ""}
{"title": "disable ip filter on dashboard", "number": 1492, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1492", "body": "This disables IP filters on Dashboard in prod and dev. I am keeping the WAF because we will add DDoS protection rules to it soon."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1492#pullrequestreview-988185616", "body": ""}
{"title": "Landing Page Deployment", "number": 1493, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1493", "body": "This pr segregates web builds into two bundles:\n1. Dashboard\n2. Landing \nThis pr also deploys landing page separately from dashboard.\nBasically, this has set up the framework by which we can split landing page into its own package.\nTESTED:\n\n"}
{"comment": {"body": "@matthewjamesadam , let me know if this is something you're kosher with. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1493#issuecomment-1141546609"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1493#pullrequestreview-988552746", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1493#pullrequestreview-989550208", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1493#pullrequestreview-989723482", "body": ""}
{"title": "Setup configuration Hub API", "number": 1494, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1494", "body": "When VSCode plugin initializes, it reaches out to the hub app and check if onboarding should occur.\nGeneric getConfiguration API to allow for changes in future"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1494#pullrequestreview-988229454", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1494#pullrequestreview-988242668", "body": ""}
{"comment": {"body": "I don't understand why this is a stream response?  I think this should be a simple client -> server request/response, that makes the code simpler?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1494#discussion_r884035668"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1494#pullrequestreview-988254345", "body": ""}
{"comment": {"body": "It has to be a stream because of the state complexity with the hub. Imagine if vscode is already up and the hub wants to kick it into the onboarding state. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1494#discussion_r884041457"}}
{"title": "Fix workflow notifications", "number": 1495, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1495"}
{"title": "add landing page deployment permissions to deploybot", "number": 1496, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1496", "body": "I have deployed this change from my machine. Deploybot user doesn't have access to management namespace by design to prevent it from altering its own permissions."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1496#pullrequestreview-988202722", "body": ""}
{"title": "Adds swift GRPC configuration endpoint", "number": 1497, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1497"}
{"title": "Update action instead of bool", "number": 1498, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1498", "body": "Use enum instead of boolean"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1498#pullrequestreview-988244673", "body": ""}
{"title": "[RFC] Links proposal for team and threads", "number": 1499, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1499"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1499#pullrequestreview-988249682", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1499#pullrequestreview-988279527", "body": "Comment about GitHub url. Maybe add description cos not clear."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1499#pullrequestreview-988284313", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1499#pullrequestreview-988373811", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1499#pullrequestreview-988539338", "body": ""}
{"title": "Introduce Side Navigator and Frame", "number": 15, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836710479", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836723954", "body": ""}
{"comment": {"body": "The way you've structured the Nav components, you have to build a structure here and pass it in at the top level as a prop.  I'm wondering if we should do this, or if we should just pass props in directly to child components, the resulting JSX would look something like:\r\n\r\n```\r\n<Navigator>\r\n    <NavigatorSection title=\"My Conversations\">\r\n        <NavigatorRow name=\"All\" href=\"#\" icon={} />\r\n        <NavigatorRow name=\"Questions I've Asked\" href=\"#\" icon={} />\r\n    </NavigatorSection >\r\n</Navigator>\r\n````\r\n\r\nThe way you're doing it is probably better if the parent and children are tightly coupled (ie if complicated state is shared between them) as the parent generates its own children and can easily share state.  The option above is maybe better if we want to allow more flexibility in this UI (if we want to add something other than rows/sections in the nav), and I have a slight preference for its syntax but it doesn't matter much.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772602201"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Is there a reason the alpha variants differ so much between colours?  ie `white`, `blue-crayola`, and `sea-foam` all have a different set of available opacities.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772605499"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-*********", "body": ""}
{"comment": {"body": "This kind of thing could be wrapped in a component....\r\n\r\n```\r\n<Desktop>\r\n  (desktop content)\r\n</Desktop>\r\n<Mobile>\r\n  (mobile content)\r\n</Mobile>\r\n```\r\n\r\nor something like that, then the components would only render content at the appropriate time.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772613116"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836739095", "body": ""}
{"comment": {"body": "(something like this would probably be easier to read then decoding tailwind CSS as well)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772613287"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836740277", "body": ""}
{"comment": {"body": "Never mind, my idea would still render to the dom so there'd be no performance gain really.  Might still be easier to read.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772614195"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836742638", "body": ""}
{"comment": {"body": "This will eventually need a callback when the item is clicked/selected, right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772615931"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836825898", "body": ""}
{"comment": {"body": "The navigator's current `children` prop refers to the actual \"Content\" of the page that's related to the selected navigator item.\r\n\r\nThe reason `children` is included within this component is I expect the actual React Router routes will be rendered as children.\r\n\r\nWill revisit this when trying to integrate react router.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772676569"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836826571", "body": ""}
{"comment": {"body": "@benedict-jw Thoughts here?\r\n\r\nCurrently based on the designs, we have a \"random\" assortment of alpha values for each base colour. Should we be consistent and generate 100 - 1 for each of the base colour?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772677006"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836827095", "body": ""}
{"comment": {"body": "Desktop content === mobile content.\r\n\r\nNo differentiation right now as this component only renders *Children* prop once.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772677464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836827265", "body": ""}
{"comment": {"body": "Will refactor so that navigator's are easier to read and differentiate.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772677601"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836827408", "body": ""}
{"comment": {"body": "Yeah. Will handle when adding react router.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772677694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836829058", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#pullrequestreview-836832159", "body": ""}
{"comment": {"body": "I was using opacity for different purposes, and adjusting based on what felt right. What's currently defined is what I have used. If we think it is best to have a defined set of values, then I would expand all base colours, maybe at 5% increments?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/15#discussion_r772681259"}}
{"title": "[RFC] Login Options Proposal", "number": 150, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/150", "body": "The current /login API spec causes some issues for the clients.\nIt currently redirects users to the desired auth location by leveraging 302 redirects. This is done by changing the browser's location to /login, not calling the API with fetch like all other APIs.\nThis is not the intended use case for the openAPI spec & codegen.\nTo work around this, we introduced additional tooling codegen templates so to expose the /login URL without actually making the network request.\nProposal:\nInstead of a /login endpoint that redirects users to the desired location, introduce a /login/options endpoint that returns a list of URLs that the client can navigate to for authentication.\n\nWill allow the backend to determine what auth options are available.\nWill allow the backend to construct these URLs which provides flexibility.\nCan include extra metadata & context (aka descriptions, errors, flags, etc.)\nRequires no custom codegen templates (difficult to maintain in long term)\nNo strange 302 behaviours from the openAPI perspective."}
{"comment": {"body": "Based off a discussion with @matthewjamesadam from a PR Review for this auth code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/150#issuecomment-1023550894"}}
{"comment": {"body": "If agreed, will are make the relevant backend + client changes in this PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/150#issuecomment-1023554743"}}
{"comment": {"body": "> true, but this is also true before your change :)\r\n\r\nRight...\r\n\r\nHere's the codegen. Base is copied from openapi gh & modified.\r\n\r\nhttps://github.com/Chapter2Inc/codeswell/tree/main/openapi/template/typescript-fetch", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/150#issuecomment-1023572677"}}
{"comment": {"body": "Way better. Please wait until https://github.com/Chapter2Inc/codeswell/pull/133 lands before implementing. I'm happy to do that implementation as well", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/150#issuecomment-1023574523"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/150#pullrequestreview-865336051", "body": "Will allow the backend to determine what auth options are available.\n\nyup, we should use this to hide Bitbucket right now.\nworth it for this alone.\n\nWill allow the backend to construct these URLs which provides flexibility.\n\ntrue, but this is also true before your change :) \n\nRequires no custom codegen templates (difficult to maintain in long term)\n\nyou have a link to this, just for my interest?"}
{"title": "Add avatar to footer", "number": 1500, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1500", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1500#pullrequestreview-988274905", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1500#pullrequestreview-989693739", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1500#pullrequestreview-989694186", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1500#pullrequestreview-989696608", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1500#pullrequestreview-990576763", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1500#pullrequestreview-990581257", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1500#pullrequestreview-990600634", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1500#pullrequestreview-990986047", "body": ""}
{"title": "Try again", "number": 1501, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1501"}
{"title": "Try again", "number": 1502, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1502"}
{"title": "Fix build", "number": 1503, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1503"}
{"title": "Setup hub connection for onboarding", "number": 1504, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1504", "body": "Setup stream for onboarding events with hub."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1504#pullrequestreview-989685872", "body": ""}
{"title": "Add initial value to search stream", "number": 1505, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1505", "body": "Sidebar search stream had no initial value.\nTherefore, a downstream Stream.Combine would not combine streams as it was missing an event/value until someone typed into the search bar.\nAdding an initial undefined value to search stream to kick off thread sidebar loading."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1505#pullrequestreview-988663981", "body": ""}
{"title": "Don't show archived threads in the gutter", "number": 1506, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1506"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1506#pullrequestreview-988675102", "body": ""}
{"title": "Use 'before' in thread fetching, and subscribe to all individual thread/PR channels", "number": 1507, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507", "body": "Fixes UNB-181 .\nThis isn't a huge PR but it's got a lot of changes:\n- Each target for ChannelPoller can now specify a list of channels that it wants to be notified on.  These lists are dynamic, so on every poll, every target can specify a new list.  Needed for the below stuff.\n- The thread list stores now hold onto the last returned set of threads, and:\n  - Uses the last element's cursor for subsequent channel polls: /threads/mine?before=lastElementCursor\n  - Subscribes to every thread's individual thread channels by ID: /threads/threadId\n- The PR store now holds onto the last returned set of PRs, and:\n  - Subscribes to every PR's individual PR channel by ID: '/pullRequests/`\nThe end result is:\n* We use before when channel polling, which makes channel polling more efficient\n* Clients are notified when threads or PRs are deleted, and re-fetch the listings to update the UI\nSome thoughts:\n* The ChannelPoller and API cache/streaming stuff has reasonably good test coverage, so I'm not super worried about regressions\n* However, the individual stores have no tests, which is a pretty significant weakness.  Post-preview, we should write tests for each store, as the logic within the stores is pretty important to get right.  We can do this by mocking out the individual APIs in question, plus the poller API."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1002099712", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1002107302", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1003156602", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1003183011", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1003194477", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1003196024", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1005011520", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1005054235", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1005055833", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1005066680", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1005067361", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1005074772", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1005075324", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1005075817", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1507#pullrequestreview-1005088345", "body": ""}
{"title": "Refactor wizard button", "number": 1508, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508", "body": "Refactor wizard button into common component.\nStill has individual implementations for tutorial & wizard due to type differences."}
{"comment": {"body": "> Yeah this will definitely cause lots of conflicts with my current work, not sure if we should wait until my PR is in or just deal with them\r\n\r\nFWIW, it's a pretty simple change to handle this. I can handle it after you merge your PR in.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#issuecomment-1141396924"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#pullrequestreview-1002097229", "body": ""}
{"comment": {"body": "Test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/12836500-726c-4ed1-be14-43760b1e639a?message=9679537f-b0ab-48b0-b554-0c63e5695a6c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#discussion_r894032344"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#pullrequestreview-989603420", "body": ""}
{"comment": {"body": "I'm surprised ts or the browser isn't complaining about this, i.e. passing through the full set of props into the button component. Would prefer to destructure the non-button props out to avoid, i.e.\r\n\r\n```\r\nconst { postMessage, $case, ...buttonProps } = props;\r\n...\r\n<BaseButton {...buttonProps}>", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#discussion_r885017517"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#pullrequestreview-989604295", "body": ""}
{"comment": {"body": "It's kind of unclear to me whether or not adding these layers of component wrapping is actually saving us anything vs having two sets of buttons that are composed of similar parts ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#discussion_r885018191"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#pullrequestreview-989605164", "body": "Yeah this will definitely cause lots of conflicts with my current work, not sure if we should wait until my PR is in or just deal with them"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#pullrequestreview-989611674", "body": ""}
{"comment": {"body": "Wanted to ensure the styling was the same. Should make subsequent changes simpler with this approach.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#discussion_r885023897"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#pullrequestreview-989617568", "body": ""}
{"comment": {"body": "That's the thing, the styling is just the button. Like as far as I can tell, the only thing we're saving is the content (i.e. 'Continue ->') -- which I guess if we wanted to, we could make that into its own component \n\n\n\nThese extra layers of component wrappers feels like we're overcomplicating things a bit, it should just be simple button composition \n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/be6579c8-7b91-4680-80b7-e1575754141f?message=9531d5a9-84d2-42fc-89ac-a5037a5e03d5).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1508#discussion_r885028348"}}
{"title": "findSourceMarks does not return marks for archived threads", "number": 1509, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1509"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1509#pullrequestreview-989619984", "body": ""}
{"comment": {"body": "@pwerry this is a fundamental issue we keep running into. Pusher and API work well together when we subscribe to the entire collection; however as soon as the collection is filtered server-side by the API, then poller must also filter. Items that were in the filtered set, but are subsequently removed are \"lost\".", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1509#discussion_r885030255"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1509#pullrequestreview-989674312", "body": ""}
{"comment": {"body": "I think there are only a few ways around this:\r\n\r\n1. Always filter client side. But this creates issues with windowing when you end up filtering because it requires the client to fetch until it fulfills its rendering needs\r\n2. Filter server side but ensure that `lastModified` is correct so we don't enter infinite poll-fetch loops. This might require a separate endpoint to actually retrieve archived data\r\n3. Separate APIs for archived that are subscribed to", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1509#discussion_r885072864"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1509#pullrequestreview-989675447", "body": "Looks ok to me with comment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1509#pullrequestreview-989677238", "body": ""}
{"comment": {"body": "\u2191 perfect insight bubble use case", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1509#discussion_r885075163"}}
{"title": "Hook up GET chat operations to the database", "number": 151, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865328126", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865332078", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865347734", "body": ""}
{"comment": {"body": "Let's make this pattern obvious in the spec. Add a comment to the YAML?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#discussion_r793944620"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865396260", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865399186", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865400267", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865405049", "body": ""}
{"comment": {"body": "Done in https://github.com/Chapter2Inc/codeswell/pull/151/commits/911ab1807d599944c5549e7deb25b6773ec2e86c", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#discussion_r793982279"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865479958", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865480276", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865480407", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865480689", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865481854", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865482279", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865484822", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865500213", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865585648", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865595173", "body": "one more comment..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865642568", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865642646", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-865663969", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/151#pullrequestreview-866689197", "body": ""}
{"title": "Introduce RepoUrl utility", "number": 1510, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1510", "body": "Will be used in follow up PRs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1510#pullrequestreview-989670595", "body": ""}
{"title": "Implement getInstallations API", "number": 1512, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#pullrequestreview-989617146", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#pullrequestreview-989671643", "body": "InstallationsApiDelegateImpl looks long but I see you have a note there to refactor"}
{"comment": {"body": "Why the double digit ordinals?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#discussion_r885070778"}}
{"comment": {"body": "Simple unit test for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#discussion_r885071920"}}
{"comment": {"body": "Oh I see, the api impl test is covering", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#discussion_r885072162"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#pullrequestreview-989680758", "body": ""}
{"comment": {"body": "So I just moved this, which means we can't actually ever change this as these values are already in the DB.\r\n\r\nTo answer your question, honestly, I have no idea why I created like this. The values don't matter as long as they never change. I think I had some vague notion that inspecting enums raw in the DB might be more self-describing if the enum values were _globally_ unique across all DB enums, which is why I have tended to set the DB number offset to be completely different for each enum. (Probably a dumb idea.)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#discussion_r885077949"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#pullrequestreview-989681315", "body": ""}
{"comment": {"body": "I'll add a test at this layer too. Makes more sense at this level in the stack.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#discussion_r885078464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#pullrequestreview-989710958", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#pullrequestreview-989716764", "body": ""}
{"comment": {"body": "added tests in https://github.com/NextChapterSoftware/unblocked/pull/1512/commits/b86f91b575a679e99a7f5cbc957bbc10a276d9d6", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1512#discussion_r885109059"}}
{"title": "adding flag to gate prod deployments", "number": 1514, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1514", "body": "To disable prod deploys update SUSPEND_PROD_DEPLOYS secret value to true\nTo re-enable prod deploys update SUSPEND_PROD_DEPLOYS secret value to false"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1514#pullrequestreview-989623113", "body": ""}
{"title": "Maybe fix flaky test", "number": 1515, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1515", "body": "This test keeps flakying in CI and I have no idea why, since it is not time dependent.\nApplying a team clause (which is more realistic anyway) might stabilize."}
{"comment": {"body": "> No harm in this change, but this doesn't feel right. There has to be some kind of race or write fail happening...\r\n\r\nagree, been staring at this thing for a while. even ran through debugger to sanity check, but dates look nowhere near close enough to race. stumped.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1515#issuecomment-1141489970"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1515#pullrequestreview-989671002", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1515#pullrequestreview-989677269", "body": "No harm in this change, but this doesn't feel right. There has to be some kind of race or write fail happening..."}
{"title": "correct a mistake in secret name", "number": 1516, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1516", "body": "Fixing a copy pasted mistake"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1516#pullrequestreview-989649228", "body": ""}
{"title": "Recommended threads collection shows unranked threads", "number": 1517, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1517", "body": "Motivation\n1. For demos with users (like Dennis) that have low PR engagement. Product doesn't demo well when there are zero recommendations.\n2. For first launch before PRs have been ingested, we should show something in the recommended collection. This will quickly change to real recommendations after ingestion has run, which takes a few minutes."}
{"comment": {"body": "won't work, fuck", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1517#issuecomment-1141494401"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1517#pullrequestreview-989674525", "body": ""}
{"title": "this is to deal with any whitespaces.", "number": 1518, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1518", "body": "Changed the comparison function to deal with any leading or trailing spaces."}
{"comment": {"body": "Ah logic error. I'll fix it ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1518#issuecomment-1141471037"}}
{"comment": {"body": "Fixed the expression ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1518#issuecomment-1141472568"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1518#pullrequestreview-989689000", "body": ""}
{"title": "Move build setup to a run script build phase", "number": 1519, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1519", "body": "@matthewjamesadam suggested this. Makes a good deal of sense to roll this into the xcodebuild"}
{"comment": {"body": "I'm abandoning this for now. `xcodebuild` is doing something stupid in CI. Might be env specific", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1519#issuecomment-1141514072"}}
{"comment": {"body": "Closing for now. Might take another crack at this when time permits", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1519#issuecomment-1141541498"}}
{"title": "Add ability to configure logging for openapi linter", "number": 152, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/152", "body": "```\n\nTask :api:zallyLint FAILED\n\n\nSEVERITY COUNT\n\nMUST        2\nSHOULD     55\n\nMUST\nResult(id=M010, url=, title=Check case of various terms, description=Path segment 'exchange-blah' does not match camelCase ('[a-z][a-z0-9](?:[A-Z0-9]+[a-z0-9])') but seems to be kebab-case ('[a-z][a-z0-9](?:-[a-z0-9]+)*'), violationType=MUST, pointer=/paths/~1preauth~1exchange-blah, lines=54..75)\nResult(id=129, url=, title=Lowercase words with hyphens, description=Use lowercase separate words with hyphens for path segments, violationType=MUST, pointer=/paths/~1preauth~1exchange-blah, lines=54..75)\nThe violation report can be found at /Users/<USER>/chapter2/codeswell/zally/violation.json\nspec has 2 MUST violations.but only 1 violations allowed\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/152#pullrequestreview-865352847", "body": "Hero!"}
{"title": "Fix cloudfront edge lambdas for landing page", "number": 1520, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520", "body": "The current cloudfront edge functions had problems because they made assumptions that all the uris were rooted at some app path (i.e. /dashboard )\nSince landing page is at root (/) we needed to add special handlers to determine whether its a landing page request or an app request.\nWeve added comprehensive tests to ensure this does not break backwards compatibility.\nTesting:\n\n\n\nTODO:\nThese functions need to be documented. Took me a little while to figure out what the purpose of these were."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-989698738", "body": ""}
{"comment": {"body": "Are we doing this for every request ? Cloud watch could become expensive ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885093430"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-989699184", "body": ""}
{"comment": {"body": "Same as my last comment. We might need to remove these at some point if they are being triggered on every request.\r\n\r\nS3 access logs would be cheaper", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885093849"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-989699729", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990820941", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/e92510c0-1dc3-499e-a222-7e2cafdd13fc)\n\n\n\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/618ae2ca-1dc2-4a2f-9361-51a1df13984e?message=f08be989-59b8-46f2-9805-8346be16116c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885893120"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990824867", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/dd29c499-b960-407a-a5c8-f22d21d93de8)\n\n\n\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/618ae2ca-1dc2-4a2f-9361-51a1df13984e?message=647bc2b5-8991-4ce2-8b5d-3550b1ea3d1b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885895926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990825114", "body": ""}
{"comment": {"body": "ffsda\r\n\r\n\r\n\r\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/9764e88d-76ed-44e1-80b2-b493268dc371)\r\n\r\n\r\n\r\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/618ae2ca-1dc2-4a2f-9361-51a1df13984e?message=ed39a641-b40d-42f4-b8f4-87ebddf77109).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885896112"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990825319", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/f4ea188e-ba36-4929-bfee-a8aaffcb438a)\n\n\n\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/24ce0afb-7990-43df-8c10-485bf72a2299?message=2413da47-26d3-4ad0-8288-03c028230738).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885896269"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990827876", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/e6b2db8c-ee10-4787-92dc-820245f28741)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/e596fe8a-2688-45b8-829b-97649dc3a9f8?message=9abf97ec-289a-4e8c-a933-cea4af1cb2eb).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885898088"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990829021", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/0694bedb-8e4e-4474-a15d-17c2861d02d9)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/e596fe8a-2688-45b8-829b-97649dc3a9f8?message=09d98d04-38a5-426b-b577-b68011b24eaf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885898920"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990848020", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/60b028ae-cded-4b72-a5c2-f8af3a0fc9bb)\r\n\r\n\r\n\r\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/618ae2ca-1dc2-4a2f-9361-51a1df13984e?message=47489300-a9ad-40d8-bd83-54559a670f45).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885912666"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990848470", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/dc42c5e5-22dd-4a7d-a07d-dc74b062a6e9)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/017fd953-8924-498a-b8ad-2816cf47a75a?message=0e17e38d-3a73-4005-b564-74efea8a331d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885913199"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990850033", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/2ad097ad-b321-4fa5-ba7e-e3e99fe25a2b)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/017fd953-8924-498a-b8ad-2816cf47a75a?message=db7ea3dc-aea4-4a98-999b-05ba0a0320db).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885914156"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990873899", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/2b9728ec-277e-41d5-a3f2-43ff27f057ab)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/017fd953-8924-498a-b8ad-2816cf47a75a?message=39c4e65c-27e9-4898-a5b0-bcca35fcedc9).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885932908"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990874762", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/e23880e2-68ce-410c-bd03-800470fb5094)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/017fd953-8924-498a-b8ad-2816cf47a75a?message=0ccac185-733d-43cb-8dbe-b77a46020b65).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885933509"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990875035", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/3c03c310-c97d-48bc-91e7-62df2282001f)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/017fd953-8924-498a-b8ad-2816cf47a75a?message=29c9c692-21fc-492a-afdd-e99c5276d966).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885933754"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990875254", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/05761125-47fc-4e48-9db3-9e6d71062183)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/017fd953-8924-498a-b8ad-2816cf47a75a?message=bd2901e1-088c-4c3c-aad7-7f2857a27a12).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885933906"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990880895", "body": ""}
{"comment": {"body": "\n\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/6aad20fc-9ee1-4f04-be8d-cb67c452a604)\n\n\n\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/618ae2ca-1dc2-4a2f-9361-51a1df13984e?message=ac3c372e-97c8-4564-a9ce-cdd38241243d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885938047"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990882842", "body": ""}
{"comment": {"body": "\n\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/c890ec6e-a3ff-4ac9-9496-124bd14c4b91)\n\n\n\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/618ae2ca-1dc2-4a2f-9361-51a1df13984e?message=12ea1877-b6eb-4deb-a91a-4cebeeb4ff26).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885939577"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990895319", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/867d738a-1677-41e2-abbd-1e77868fb92c)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/017fd953-8924-498a-b8ad-2816cf47a75a?message=fa18e7c6-acc8-4cd4-b15b-035bb9c29173).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885948287"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990897397", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/d27be7a4-02ea-4a79-a137-c90abc4a5c7b)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/017fd953-8924-498a-b8ad-2816cf47a75a?message=72dd13af-3f99-4c8e-a65b-12cc686f420b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885949641"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990898222", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/6be9c7b1-0d52-454a-9f33-e128207ff8be)\n\n\n\n###### This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/017fd953-8924-498a-b8ad-2816cf47a75a?message=7f1728f2-cc8c-4f26-88e3-c53e22c075ec).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885950267"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-990950099", "body": ""}
{"comment": {"body": "\n\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/34f021b1-8b6f-4226-90a2-b3a969e76861)\n\n\n\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/618ae2ca-1dc2-4a2f-9361-51a1df13984e?message=9abc72eb-635e-432c-817c-ae5e67f674b6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r885989319"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#pullrequestreview-993124803", "body": ""}
{"comment": {"body": "![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/f504a2a5-cbc2-4de8-b688-1736380344e7)\r\n\r\ntesting\r\n\r\n\r\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/618ae2ca-1dc2-4a2f-9361-51a1df13984e?message=8cc65a1b-eb68-46ad-b9a4-18d12541a92e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1520#discussion_r887655048"}}
{"title": "So the clients have something to render until the link is wired up", "number": 1521, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1521"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1521#pullrequestreview-989703514", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1521#pullrequestreview-989705712", "body": ""}
{"title": "Fake demo user comment network", "number": 1522, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1522", "body": "fake...\n\nPrevious approaches messed up the algorithm:\n- https://github.com/NextChapterSoftware/unblocked/pull/1406\n- https://github.com/NextChapterSoftware/unblocked/pull/1517"}
{"title": "Manually invite user", "number": 1523, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523", "body": "Add ability to manually invite user when creating discussion in VSCode.\nWeb extension next...\n\nFixes "}
{"comment": {"body": "Is the design like this?? Feels weird to me that the input isn't in an input box and that the checkmark isn't there until blur(??)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#issuecomment-1142605342"}}
{"comment": {"body": "> Is the design like this?? Feels weird to me that the input isn't in an input box and that the checkmark isn't there until blur(??)\r\n\r\nThere are no designs for this :)\r\n\r\nThis is just a first draft to get the feature in. We need this and it unblocks other work.\r\n\r\nWhat's the purpose of having the input box at the beginning? Once you've entered an email, it's automatically checked off.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#issuecomment-1142735830"}}
{"comment": {"body": "Updated input state to include a checkbox for consistency.\r\nNot toggleable until user enters email.\r\n\r\n\r\n<img width=\"396\" alt=\"CleanShot 2022-05-31 at 16 56 29@2x\" src=\"https://user-images.githubusercontent.com/1553313/171302343-b3dc1af6-cc13-417e-b1a1-530cee56645b.png\">\r\n\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#issuecomment-1142786504"}}
{"comment": {"body": "Can we hold off on merging this until after the demo tomorrow? Unclear to me what kind of work this is blocking but this feels like pretty major behavioural feature for something that isn't properly mocked out. I would feel better to wait until after the demo ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#issuecomment-1142860573"}}
{"comment": {"body": "@benedict-jw for eyes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#issuecomment-1142860826"}}
{"comment": {"body": "> We need this and it unblocks other work.\r\n\r\n@jeffrey-ng what work is this blocking? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#issuecomment-1142927464"}}
{"comment": {"body": "> > We need this and it unblocks other work.\r\n> \r\n> @jeffrey-ng what work is this blocking?\r\n\r\nBlocks this https://github.com/NextChapterSoftware/unblocked/issues/1513\r\n\r\nBut not super urgent. Just for our release.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#issuecomment-1142928911"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#pullrequestreview-991108398", "body": ""}
{"comment": {"body": "This is a big one, can you make an issue and link here so this is properly tracked? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#discussion_r886098769"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#pullrequestreview-991109019", "body": ""}
{"comment": {"body": "did the importing not work??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#discussion_r886099231"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#pullrequestreview-991156200", "body": ""}
{"comment": {"body": "There's an importing issue that's causing the storybook to fail. This was done to temporarily relieve the issue. \n\n\n\nThe way things are setup, this imports AssetURL stuff which pulls in the API and causes issues.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/d0f3c1bf-a61f-46a2-9bbe-3260eab0d831?message=4156881d-3d67-4e75-bae0-6341414b1636).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#discussion_r886133608"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#pullrequestreview-992570776", "body": ""}
{"comment": {"body": "@jeffrey-ng can you make this - want to make sure this doesn't get lost ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#discussion_r887221593"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1523#pullrequestreview-992609170", "body": "Ok re: this being temporary implementation. Ben/Dennis probably will have stronger indicators re: how this is supposed to behave \nIMO a possible final version could look similar to the invite page in onboarding where it's a separate section and is an actual recognizable input element so we can leverage the placeholder as a prompt \n"}
{"title": "Download invite API (teamless)", "number": 1524, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1524"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1524#pullrequestreview-989714933", "body": ""}
{"title": "[Onboarding] Create Insight steps", "number": 1525, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525", "body": "\n\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-989723752", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991164682", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991239494", "body": ""}
{"comment": {"body": "When I tested this out, I didn't notice it highlighted?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#discussion_r886197979"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991240213", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991240931", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991241501", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991243853", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991246113", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991248865", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991251633", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991252347", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991252484", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991253228", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991254912", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-991258704", "body": ""}
{"comment": {"body": "are you on the right branch? this is what I see \r\n<img width=\"427\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171301208-7314a20c-ee6d-43f5-974c-d11690498388.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#discussion_r886212933"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992639035", "body": ""}
{"comment": {"body": "Check for stderr.\r\n\r\nIF it exists, should fail promise.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#discussion_r887269760"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992639746", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992643049", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992644184", "body": ""}
{"comment": {"body": "This looks like an common pattern throughout the file\r\n\r\nShould address in separate PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#discussion_r887273382"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992644402", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992646119", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992646858", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992656675", "body": "Some comments but good to go once addressed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992659076", "body": ""}
{"comment": {"body": "[hello \r\n\r\ntest](https://www.githubstatus.com/)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#discussion_r887284252"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#pullrequestreview-992669243", "body": ""}
{"comment": {"body": "[hello\r\ntest](https://www.google.com/)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1525#discussion_r887292032"}}
{"title": "fix prod deploy flag", "number": 1526, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1526"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1526#pullrequestreview-989739102", "body": ""}
{"title": "Fix findRepo api", "number": 1527, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1527", "body": ""}
{"title": "Repo sync updates existing repos", "number": 1528, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1528", "body": "Repo names, owner names, repo urls can change at any time in the SCM.\nSince we rely on these properties for repo matching, we must update these properties."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1528#pullrequestreview-989796516", "body": ""}
{"comment": {"body": "Is this not much more inefficient than a batch query?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1528#discussion_r885172120"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1528#pullrequestreview-989805398", "body": ""}
{"comment": {"body": "yes. And actually just realized this is going to _unconditionally_ update every repo, invoking pusher. Will rework to make no-op update where possible.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1528#discussion_r885178886"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1528#pullrequestreview-989806026", "body": ""}
{"comment": {"body": "Wow good spot. These bugs are subtle...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1528#discussion_r885179351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1528#pullrequestreview-989816672", "body": ""}
{"comment": {"body": "Interesting: turns out the Exposed is smart enough to avoid the update. Added a test to prove that it's a no-op:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1528/commits/386394dc6ed9648ebcb4e84a17aca197b993ef3a", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1528#discussion_r885187355"}}
{"title": "Admin web: clickable rows and more buttons", "number": 1529, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1529"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1529#pullrequestreview-989804240", "body": ""}
{"title": "temp fix while we investigate", "number": 153, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/153", "body": "This is a temp workaround until I figure out why we don't get ingress traffic from ELBs. Seems Calico policy applies to Services too!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/153#pullrequestreview-865461914", "body": ""}
{"title": "Service provides Web Extension urls", "number": 1530, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1530"}
{"title": "Fix ordering of cloudfront behaviours", "number": 1531, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1531", "body": "Cloudfront is dependent on ordering to determine which behaviour takes precedences when doing route matching.\nWe need to ensure root wildcard matching is last element in list."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1531#pullrequestreview-990871817", "body": ""}
{"title": "Potentially fix logout + debugging logs", "number": 1532, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1532", "body": "Potentially fix issue with unexpected logouts.\nAlso added logging to help debug the situation.\nRemoved unnecessary logout operation for baseAPi"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1532#pullrequestreview-990902204", "body": ""}
{"title": "Update PR number in CodeBlock", "number": 1534, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1534", "body": "Add PR title to VSCode and web extension.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1534#pullrequestreview-991105049", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1534#pullrequestreview-991105135", "body": ""}
{"title": "Fix source mark rendering", "number": 1535, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1535", "body": "Problem:\n- thread info subscription is skipped if an existing subscription exists,\n  however the existing thread info is cleared. this means that any update\n  will cause the thread info to be cleared, and the only way for the info\n  to be shown is for the thread info object to change on the server.\nSolutions:\n- clear the existing subscription to froce a re-subscription. simple and\n  seems to work, so went with this for now.\n- do not clear the thread info on update. tried this, but hard to track all\n  the places when shared memory is mutated, and hence fragile."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1535#pullrequestreview-991088958", "body": ""}
{"title": "Log ip addresses in honeycomb", "number": 1536, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1536"}
{"title": "Remove default title from web extension", "number": 1537, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1537"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1537#pullrequestreview-991105622", "body": "is it blank, or is there some placeholder text? like New Discussion ?"}
{"title": "keep WAF access logs", "number": 1538, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1538", "body": "2 hours of digging through logs for 10 lines of code...Ugh I don't like AWS docs!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1538#pullrequestreview-991121339", "body": ""}
{"title": "Don't open file in sourcemark tooltip click", "number": 1539, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1539"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1539#pullrequestreview-991127316", "body": ""}
{"title": "Add shared web components", "number": 154, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/154", "body": "Add ability to share web components across projects.\n\nShared components can go into /shared/webComponents.  This is a project that has its own storybook and tests, so we can test shared stuff in isolation.\nProjects using these components add path definitions: '@shared-web-components -> ../shared/webComponents, like we do for other path dependencies\nThis required moving all our NPM dependencies to the root, so that a consistent set of module dependencies got imported into each project.  Big change!\n\nTodo:\n* CI tweaks: we aren't running storybook/chromatic for the shared components in CI yet, that will come in the next PR"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/154#pullrequestreview-865550516", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/154#pullrequestreview-865551377", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/154#pullrequestreview-865558921", "body": ""}
{"title": "Strip unblocked signature when ingesting a comment", "number": 1540, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1540", "body": "Fixes a bug where Unblocked includes the signature when ingesting a comment that has one:\n\nThis happens when someone posts a message to a PR thread from Unblocked, then goes to GitHub to edit it. Our current ingestion logic sees that the message has been edited and re-ingests, but also includes the signature that was appended at creation time."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1540#pullrequestreview-993835470", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1540#pullrequestreview-993835471", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1540#pullrequestreview-993926246", "body": ""}
{"title": "Add additional cloudfront headers being sent to origin", "number": 1541, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1541"}
{"title": "Update tutorial steps for new onboarding mocks", "number": 1542, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1542", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1542#pullrequestreview-992663103", "body": ""}
{"title": "add www to apex redirect lambda edge", "number": 1543, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543", "body": "Added lambda edge function to redirect calls to landing page / and /dashboard to domain apex. \ne.g \n to \n to \nwww.getunblocked.com/ to \n to \n to \nwww.getunblocked.com/dashboard to \nDeployed to Dev and works as expected."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543#pullrequestreview-991220081", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543#pullrequestreview-991220771", "body": ""}
{"comment": {"body": "Should we have a basic test for this just to ensure that this doesn't get broken in the future?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543#discussion_r886182585"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543#pullrequestreview-991220951", "body": "A test would b enice, but I\"ll let it pass. :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543#pullrequestreview-991221017", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543#pullrequestreview-991221549", "body": ""}
{"comment": {"body": "I'll add some tests before merging. I also need to enable integration tests again. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543#discussion_r886183240"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543#pullrequestreview-991421865", "body": ""}
{"comment": {"body": "Added tests", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1543#discussion_r886339762"}}
{"title": "Fix icon stacking", "number": 1544, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1544", "body": "before:\n\nafter:\n\nbug in recent logic migration change:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1544#pullrequestreview-991228184", "body": ""}
{"title": "Fix thread loading issue", "number": 1545, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545", "body": "We were not keying thread channels by their repoIDs while the requests themselves were scoped to repoIDs.\nThis was causing loading issues with web extension when one had multiple tabs open with different repos.\nCould also cause infinite loops since clients were observing all changes, not ones scoped to the repo."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#pullrequestreview-991234916", "body": ""}
{"comment": {"body": "This will produce:\r\n```\r\nrepoIds=<123>&repoIds=<321>&repoIds=etc...\r\n```\r\n\r\nDon't we instead want:\r\n```\r\nrepoIds=123,321,etc...\r\n```\r\n\r\n?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#discussion_r886194361"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#pullrequestreview-991259936", "body": ""}
{"comment": {"body": "no, what Jeff has is correct ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#discussion_r886213984"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#pullrequestreview-991260038", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#pullrequestreview-991263135", "body": ""}
{"comment": {"body": "@pwerry in general we need to move to a more well-structure poller request.\r\n\r\nWe discussed options here:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1449#issuecomment-1138077505", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#discussion_r886216629"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#pullrequestreview-992565408", "body": ""}
{"comment": {"body": "foodies <img width=\"141\" alt=\"Screen Shot 2022-06-01 at 12 20 42\" src=\"https://user-images.githubusercontent.com/1798345/171485030-83c1f3e5-3475-40bf-b291-ce2591188a32.png\">\n\n<a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>\u00a0\u00a0This comment was edited from .\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2e1cad5a-0ca1-4b03-be3f-c6dcd8b9be3c?message=95ecdef3-e6fc-47cd-8e31-4ca20a18275b).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#discussion_r887217689"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#pullrequestreview-992568108", "body": ""}
{"comment": {"body": "poodo\n\n<a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>\u00a0\u00a0This comment was created from .\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2e1cad5a-0ca1-4b03-be3f-c6dcd8b9be3c?message=c7a29539-b5bf-4208-9efd-9b1e471e9e6a).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#discussion_r887219661"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#pullrequestreview-992570402", "body": ""}
{"comment": {"body": "lines <img width=\"342\" alt=\"Screen Shot 2022-06-01 at 12 25 42\" src=\"https://user-images.githubusercontent.com/1798345/171485841-7da7be22-9a17-412e-a5cb-06ad2f9871e2.png\">\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2e1cad5a-0ca1-4b03-be3f-c6dcd8b9be3c?message=ebe92d7a-7854-41d5-a38e-11e2ec636554).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1545#discussion_r887221342"}}
{"title": "Update Version", "number": 1547, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1547"}
{"title": "Wire up onboarding", "number": 1548, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1548"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1548#pullrequestreview-991268418", "body": ""}
{"comment": {"body": "Ignore the installer changes for now please", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1548#discussion_r886220928"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1548#pullrequestreview-991289983", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1548#pullrequestreview-991291977", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1548#pullrequestreview-991292659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1548#pullrequestreview-991443158", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1548#pullrequestreview-991446529", "body": ""}
{"title": "Add a manual script", "number": 1549, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1549", "body": "Useful for iterating on the installer locally."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1549#pullrequestreview-991487354", "body": ""}
{"title": "Setup end to end auth with github", "number": 155, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/155"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/155#pullrequestreview-865589851", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/155#pullrequestreview-868160059", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/155#pullrequestreview-868166194", "body": ""}
{"comment": {"body": "I have no idea what this does but trust this is intentional \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/155#discussion_r795888754"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/155#pullrequestreview-868167047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/155#pullrequestreview-868180197", "body": ""}
{"comment": {"body": "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite\r\n\r\nLax is the default value for sameSite.\r\n\r\nNone: Cookies will be sent in all contexts. -- Chrome doesn't accept None + Secure cookies\r\nLax: Cookies are not sent on normal cross-site subrequests (for example to load images or frames into a third party site), but are sent when a user is navigating to the origin site (i.e., when following a link).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/155#discussion_r795895814"}}
{"title": "[WOLF] Non team email invites", "number": 1550, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1550", "body": "Peter has asked for email invites that do not require a team but have an authorized user with it.\nI'm trying to give him that."}
{"title": "Fixes for source mark rendering", "number": 1551, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1551", "body": "Fixes\n- Marks are cached once calculated in file-to-marks cache. The cache is used\n  to lookup marks when calling getSourceMarksForFile. Problem is that this\n  does not account for new marks generated by the server. So we hack for now\n  by augmenting the cache with a traversal over all sourcemarks matching the\n  file path.\n- After creating a discussion, poll until we observe a sourcemark associated with\n  the new thread returned from the calculator. Poll for 10 seconds hoping for the\n  mark to become available."}
{"title": "Enable manual triggering of the installer workflow for a branch, tag, or SHA", "number": 1552, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1552"}
{"title": "Overlay sourcemark creation", "number": 1553, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1553", "body": "When the user makes a new thread, overlay the thread and sourcemark in the TextEditorSourceMarks class, so we can render it immediately.\nThis code got pretty gross because we have to construct a fake ThreadInfoAggregate from the bits and pieces of data we have lying around.  The actual overlay code is not so bad."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1553#pullrequestreview-991432296", "body": ""}
{"comment": {"body": "Gross code to make a fake ThreadInfo / ThreadInfoAggregate for the created thread.  This would be far less bad if `createThread` returned a full `ThreadInfo`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1553#discussion_r886347295"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1553#pullrequestreview-991434818", "body": ""}
{"title": "Update readme with env switch choices", "number": 1554, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1554"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1554#pullrequestreview-992274047", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1554#pullrequestreview-992274242", "body": ""}
{"title": "Change getInstallations response", "number": 1555, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1555", "body": ""}
{"comment": {"body": "related to https://github.com/NextChapterSoftware/unblocked/issues/1533", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1555#issuecomment-1143894757"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1555#pullrequestreview-992418490", "body": ""}
{"title": "Cleanup hierarchy", "number": 1556, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1556"}
{"title": "Add User default when plugin installed", "number": 1557, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1557", "body": "Write to user default on VSCode plugin load. \nTODO: When can we clean this up? Can the hub app check for this once in a while?"}
{"comment": {"body": "Nice - now we also need one for \"extension uninstall\" event", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1557#issuecomment-1143942135"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1557#pullrequestreview-992448620", "body": ""}
{"title": "Nuke team cache when logging out or bad things will happen", "number": 1558, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1558"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1558#pullrequestreview-992449096", "body": ""}
{"title": "Possible fix for ordering issue", "number": 1559, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1559", "body": "Distinct was necessary in a previous iteration, but no longer needed.\n  removing this might address ordering, since distinct can have an\n  ordering effect in some cases. Test still pass without it.\nAlso cleanup unnecessary join in archived threads query."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1559#pullrequestreview-992451703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1559#pullrequestreview-992460264", "body": ""}
{"title": "Add support for encrypted local and ci secrets", "number": 156, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/156", "body": "This pr does the following:\n1. We use ansible vault to store our secrets in the repo (it's fucking easy...)\n2. We use an ansible playbook to auto-descrypt secrets into ~/.secrets directory\n3. We update GlobalConfig to optionally load a conf file from an OPTIONAL ~/.secrets/secrets.conf\n4. We add a ServiceInitializer that can also load java system properties from an OPTIONAL ~/.secrets/secrets.properties into the java process system configuration.\n5. We update github actions to load playbook.\n6. We add logzio helper utilities / appenders.\n4 is necessary for Logz.io appender as it needs to load it from system properties.\nAll you need to do:\n1. brew install ansible\n2. make setup-local-env (use password in 1password)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/156#pullrequestreview-865626169", "body": ""}
{"comment": {"body": "Nice - beat me to it!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/156#discussion_r794149541"}}
{"title": "Try again", "number": 1560, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1560"}
{"title": "Use installations API", "number": 1562, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1562", "body": "Integrate real installations API instead of mock data."}
{"comment": {"body": "Should allow us to start testing end to end", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1562#issuecomment-1144018536"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1562#pullrequestreview-992542098", "body": ""}
{"title": "Fix cache path", "number": 1563, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1563"}
{"title": "Cleanup", "number": 1564, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1564"}
{"title": "adding dummy error images to static site bucket", "number": 1565, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1565", "body": "Added dummy error images \nModified ci-web workflow to include the new error assets and email assets in landing page deployments"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1565#pullrequestreview-992657912", "body": ""}
{"title": "Convert HTML img tags to proto image inline elements", "number": 1566, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1566", "body": "Many images in GitHub PR comments are HTML tags. Our proto spec doesn't have support for HTML elements, and our clients don't render HTML even if our spec did have support. \nThis PR will convert HTML blocks that contain only img tags to proto image inline elements so that clients can render them. Note that this is a lossy conversion: any size attributes in the original HTML will be dropped.\nIf the HTML block contains anything other than un-nested img tags, it will be converted to a text inline element. This is so that if we edit in unblocked and post back to GH it will be correctly rendered in GitHub."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1566#pullrequestreview-992814356", "body": ""}
{"title": "Fixed landing page and dashboard deployment jobs", "number": 1567, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1567", "body": "Fixed a mistake in directory paths. Deployed it to both Dev and Prod. Works as expected"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1567#pullrequestreview-992732486", "body": ""}
{"title": "Replace line breaks with spaces for thread titles", "number": 1568, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-992706637", "body": ""}
{"comment": {"body": "agree with line break truncation.\r\n\r\nHowever, shouldn't clipping be client-side presentation concern? For example on mobile we want 50 characters, VSCode 80 characters, dashboard 120 characters, etc. The API can't know that ahead of time.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r887318836"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-992710082", "body": ""}
{"comment": {"body": "yeah, the more I think about it, the client should be in control here. It can use break-word semantics, use the full title in on-hover tooltips, and show as much or as little as it wants depending on the viewable area.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r887321411"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-992717170", "body": ""}
{"comment": {"body": "@jeffrey-ng @kaych @matthewjamesadam if I remove the clipping here, how much would it take to get the clients to clip?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r887326662"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-992798789", "body": ""}
{"comment": {"body": "We *could* do a word break for number of characters with `ch` but that only works well with monospace fonts.\r\n\r\nWe would probably just have a max width for the views instead of limiting char length.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r887390864"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-992800098", "body": ""}
{"comment": {"body": "Pass the whole title and let the clients decide how much data to present. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r887391902"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-992804160", "body": ""}
{"comment": {"body": "So if I remove the truncation here in this PR, will that break the clients or is that a safe change?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r887395007"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-992804657", "body": ""}
{"comment": {"body": "just remove the `.take(threadTitleLength)` part you mean?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b082daaa-cc56-4d74-bfd7-4febd5dda14d?message=ab8844c3-11fc-4d3a-ab0e-ef1b5a3c6a05).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r887395420"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-992805528", "body": ""}
{"comment": {"body": "yea", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r887396124"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-992805692", "body": ""}
{"comment": {"body": "do. it.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b082daaa-cc56-4d74-bfd7-4febd5dda14d?message=8fec6f05-57a6-4870-a954-e352490a0fca).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r887396266"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-994228104", "body": ""}
{"comment": {"body": "Going to remove the clipping logic in a separate PR that will include the necessary client-side changes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r888439593"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#pullrequestreview-994251429", "body": ""}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/1612\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/b082daaa-cc56-4d74-bfd7-4febd5dda14d?message=32472d6d-0d29-4d84-ac33-3caeb3320b1f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1568#discussion_r888456789"}}
{"title": "Drop local test database", "number": 157, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/157"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/157#pullrequestreview-865569689", "body": ""}
{"title": "Refactor ValueCacheStream to use enums", "number": 1570, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1570", "body": "Refactor ValueCacheStream away from \"initialized\" to enums.\nNo logical change. Consolidates the approach and should hopefully clean up future code."}
{"comment": {"body": "I wanted to do this for an in progress PR. I don\u2019t think we need to do DataCacheStream quite yet as we may be moving it to use APICacheStream instead? Matt was partially working on this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1570#issuecomment-1144261550"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1570#pullrequestreview-992863817", "body": ""}
{"title": "Consistently update isUnread when updating latestMessage or latestReadMessage", "number": 1571, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1571", "body": "The approach I took is: anywhere we mutate latestMessage or latestReadMessage we now always update isUnread too."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1571#pullrequestreview-992806364", "body": ""}
{"comment": {"body": "This is the bug fix. Specially the `isUnread=null` path.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1571#discussion_r887396779"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1571#pullrequestreview-992808032", "body": ""}
{"comment": {"body": "Yikes this one trips me up sometimes too", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1571#discussion_r887398046"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1571#pullrequestreview-992810858", "body": ""}
{"title": "Add ability to do unauthorized team queries for invites", "number": 1572, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1572", "body": "Pretty straighforward.\nTeam request -> link invite -> store payload in redis associated with a 30-day expiry id.\nUnauth'd client -> link -> get payload in redis associated with invite id (since it's 30 day limited, if not found, we abort)"}
{"title": "implement custom error images", "number": 1574, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1574", "body": "Return custom placeholder images with 200 code for images when:\n- A request is not authorized (e.g image added through Unblocked rendered on Github PR UI)\n- 404 image not found (e.g asset was deleted from unblocked but there's a reference to it in a PR discussion\ne.g for 401 "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1574#pullrequestreview-993920517", "body": ""}
{"title": "Fix hacked user", "number": 158, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/158"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/158#pullrequestreview-865569526", "body": ""}
{"title": "Update apiservice README for java", "number": 159, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/159"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/159#pullrequestreview-865573345", "body": ""}
{"title": "PullRequestIngestionOnboardingService sends events to priority queue", "number": 1598, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598", "body": "Large PR due to refactor (sorry) to support better testability."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994047935", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994273701", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994280692", "body": "I think generally we should try to use our Repo DB ID when're possible, then fallback to using a RepoUrls object or a repo external ID. but I would try to stay away from custom url parsing, or using extract owner/repo parts."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994292633", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994293191", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994309211", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994359416", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994370391", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994407311", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994453468", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994453985", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994454072", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994454820", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-994455850", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1598#pullrequestreview-998902012", "body": ""}
{"title": "[Onboarding] Outline for Stay Connected steps", "number": 1599, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1599", "body": "Add outline for the last onboarding tutorial step; will fill in each view consequently\n\n\n\nAlso some other polish:\n* Highlight sourcemark in file editor when the thread is opened\n* Close file editor when thread is archived\n* Don't clear the message editor on submit in the Start Discussion form -- if there's a lag, it looks inconsistent with the title field still populated; the webview panel will close so clearing it is moot\n* Clean up a bit of the listener logic"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1599#pullrequestreview-*********", "body": ""}
{"title": "Integrate Router and Mocked Auth", "number": 16, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16", "body": "Added support for react router.\nIncluded basic implementation of auth blocking for routing purposes.\nRefactored Navigator to be router friendly.\nTemporary \"Login\" page. No real auth. State handled in memory.\n\nRunning npm run start will now properly render navigator with correct routing.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Something worth thinking about is how this decomposes further as the app get larger.  Are there subfolders for each feature area?\r\n```\r\n  /web/src/views/login/<login stuff>\r\n  /web/src/views/settings/<settings stuff>\r\n```\r\n\r\nIf so is the `/views` top-level folder worth having, or should we promote the different feature areas to be directly under `/src` ?  Or we can get rid of /src and have everything under /vscode directly?  Not urgent to figure out now of course.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r773499723"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Good point. I can imagine this getting quite unwieldy soon.\r\n\r\nI'm still not 100% sure how this should work.\r\nI just moved \"App\" into this folder and this is the root node which holds the root router.\r\n\r\nI'm thinking of renaming or adding a new folder for \"features\" where we can add the top level feature areas?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r773509577"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-838691202", "body": ""}
{"comment": {"body": "My vote would be something like:\r\n* Get rid of `/src` as I'm not sure it's serving much of a purpose\r\n* Have top-level folders for things that cut across the entire app: `/components`, `/store`, `/utils`, etc\r\n* Have top-level folders for features named after the features themselves: `/login`, `/chat`, `/blah`...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774055724"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-838875848", "body": ""}
{"comment": {"body": "Keeping the src may be useful for CI purposes.\r\n\r\nFor example, we may be able to limit the necessary actions if a PR only included non-src changes.\r\n\r\nAlso, tsconfig is using the src folder as root right now. Not sure if that's a big deal though.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774206479"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-838886768", "body": ""}
{"comment": {"body": "The tsconfig root shouldn't matter at all as we're using webpack for bundling -- ie we're not actually running tsc directly.  Since we're defining import paths everywhere (ie `@components`, `@utils` etc) there really isn't a single project root.\r\n\r\nNot a huge deal to me either way, maybe Kay will have an opinion", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774215199"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839515630", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839520884", "body": ""}
{"comment": {"body": "I think neither of us is really sold on using a context here, might be worth figuring out alternatives?  The only alternative that comes to mind off the top of my head is using props directly...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774699956"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839525860", "body": ""}
{"comment": {"body": "Yeah. This was to work around passing props n-levels deep.\r\nWill go back to doing so for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774703557"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839528995", "body": ""}
{"comment": {"body": "Tell me if you think this makes any sense: if we split this component up into two separate components (NavigationLinkRow, NavigationClickRow (or maybe just NavigationRow)) then I think some of the code here gets cleaned up and the inputs into the components become simpler?\r\n\r\n(hmm, that might require us moving the Navigation component to taking in sections/row components as children, instead of taking a data structure and generating children, though...)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774705818"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839531084", "body": ""}
{"comment": {"body": "Oh I should have said, it's not a prerequisite to getting this PR in, we can look at alternatives later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774707498"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839560046", "body": ""}
{"comment": {"body": "Doesn't matter for this PR, but what is `userName` supposed to represent?  And I'm interested in how you think we'd deal with `lang` here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774729123"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839561218", "body": ""}
{"comment": {"body": "I know this is a placeholder login page so it doesn't matter for this PR, but I think controlled components feel more react-ish for form processing?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774729950"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839561970", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839562069", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839586726", "body": ""}
{"comment": {"body": "Yeah. I wasn't 100% sure on how to tackle having the section/rows as components instead of props since we still wanted the children to be contents of the Navigator/frame.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774749247"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839587022", "body": ""}
{"comment": {"body": "Ahh ignore that. Test props so I can differentiate between the routes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774749471"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839588234", "body": ""}
{"comment": {"body": "Was experimenting with this approach.\r\n\r\nThe benefit I see here is it's more\r\n standard web and having the form allows for certain functionalities such as pressing enter to submit form and these tooltips on form inputs.\r\n\r\n<img width=\"592\" alt=\"Screen Shot 2021-12-23 at 11 35 50 AM\" src=\"https://user-images.githubusercontent.com/1553313/147284916-5a6070ec-ee72-491f-9f18-de07ae0e81cb.png\">\r\n ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774750340"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839589039", "body": ""}
{"comment": {"body": "Yeah. I wasn't 100% sure on how to tackle having the section/rows as components instead of props since we still wanted the children to be contents of the Navigator/frame.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774750935"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#pullrequestreview-839591454", "body": ""}
{"comment": {"body": "Not sure about the tooltip, but you can handle `onSubmit` on the form to get the regular form submit event...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/16#discussion_r774752796"}}
{"title": "Hook up getMessages operation to the database", "number": 160, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/160", "body": "Basically the same as https://github.com/Chapter2Inc/codeswell/pull/151 but for getMessages"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/160#pullrequestreview-866453700", "body": ""}
{"comment": {"body": "For these sort of duplicated requests in tests, we should probably abstract the endpoint requests such that it'll be only one point where we change in the future, rather than several points in a test.\r\n\r\ni.e. TestAuthService.\r\n\r\nJust a thought really.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/160#discussion_r794708698"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/160#pullrequestreview-866454422", "body": ""}
{"comment": {"body": "Should be ordered by createdAt, but I have a change that does this. I'll update this test.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/160#discussion_r794709202"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/160#pullrequestreview-866456443", "body": ""}
{"comment": {"body": "Yep, agree. Goal is to move in that direction as I/we write more tests.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/160#discussion_r794710593"}}
{"title": "AddTeamInvitePage", "number": 1601, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1601", "body": "More work needing to be done to place team icon on top.\n"}
{"title": "trying to see if cache busting headers work", "number": 1602, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1602", "body": "If this doesn't work then we need another lambda for Origin response."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1602#pullrequestreview-994117910", "body": ""}
{"title": "Take over the screen and show the hub like a boss", "number": 1603, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1603", "body": "Ignore the fact that the settings button triggers it. That was just a convenience for dev purposes and has been reverted"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1603#pullrequestreview-994172311", "body": "Not too bad"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1603#pullrequestreview-994220257", "body": ""}
{"comment": {"body": "For reasons I'm not super clear on, the minY for the window is actually off screen by about 20 points or so", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1603#discussion_r888433676"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1603#pullrequestreview-994221110", "body": ""}
{"comment": {"body": "Grabbing the window is potentially dangerous, but for now at least it produces the best result", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1603#discussion_r888434298"}}
{"title": "Drop looping to 5 seconds", "number": 1604, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1604", "body": "We raised it to 10 seconds a while back because the SCM service was OOMing. We've made some improvements since, so I'd like to try dropping it down to see if it's better now."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1604#pullrequestreview-994170659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1604#pullrequestreview-994170748", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1604#pullrequestreview-994170832", "body": ""}
{"title": "HTTP clients should retry", "number": 1605, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1605", "body": "fixes "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1605#pullrequestreview-994208340", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1605#pullrequestreview-994208980", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1605#pullrequestreview-994209398", "body": ""}
{"title": "HTTP clients should expect success throwing client/server exceptions as necessary", "number": 1606, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1606", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1606#pullrequestreview-994208149", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1606#pullrequestreview-994209100", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1606#pullrequestreview-994210039", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1606#pullrequestreview-994211281", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1606#pullrequestreview-994218524", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1606#pullrequestreview-994265511", "body": ""}
{"title": "API Reference Documentation", "number": 161, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/161", "body": "  As seen on Friday demo \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/161#pullrequestreview-866684987", "body": " cool!"}
{"title": "UNB-169 VSCode Install sidebar", "number": 1610, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610", "body": "New Install sidebar for VSCode.\nEmpty Repo State is not done yet: Should be related to \nAlso fixes UNB-103\n"}
{"comment": {"body": "Still noticing these bugs: https://github.com/NextChapterSoftware/unblocked/issues/1561\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#issuecomment-1145367303"}}
{"comment": {"body": "General q: how well does this resize? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#issuecomment-1145465920"}}
{"comment": {"body": "> General q: how well does this resize?\r\n\r\nFor now, with the entire sidebar.\r\nWe can update the styling if we want a fixed size? I would hope we could set the fixed size on the window though and not the actual inner view.\r\n<img width=\"2074\" alt=\"CleanShot 2022-06-02 at 19 38 14@2x\" src=\"https://user-images.githubusercontent.com/1553313/171775917-6e75084a-f1f8-4dda-aeb6-c826b526f11f.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#issuecomment-1145529392"}}
{"comment": {"body": "From the sidebar:\r\n\r\n\r\n<img width=\"1119\" alt=\"CleanShot 2022-06-07 at 14 26 25@2x\" src=\"https://user-images.githubusercontent.com/1553313/172486655-37f7e01f-ccaf-4630-88a5-a906d1fdf0d6.png\">\r\n\r\n<img width=\"759\" alt=\"CleanShot 2022-06-07 at 14 37 12@2x\" src=\"https://user-images.githubusercontent.com/1553313/172487165-bf0520a4-9291-4ea8-9e68-045852386053.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#issuecomment-1149193899"}}
{"comment": {"body": "Also addresses this: https://linear.app/unblocked/issue/UNB-103/vscode-display-a-ui-when-repo-resolution-fails", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#issuecomment-1150220192"}}
{"comment": {"body": "Will we ever need the \"select a project to use with Unblocked\" in the sidebar? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#issuecomment-1150341646"}}
{"comment": {"body": "> Will we ever need the \"select a project to use with Unblocked\" in the sidebar?\r\n\r\nBasically for this use case https://linear.app/unblocked/issue/UNB-103/vscode-display-a-ui-when-repo-resolution-fails\r\nIf you open up a VSCode workspace / file without a git repo, we will show that page in the sidebar.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#issuecomment-1150344432"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-1000222668", "body": ""}
{"comment": {"body": "This is pretty much just copied from the InstallationWizardCOmmand, right?  Not any significant changes?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#discussion_r892712157"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-1000225734", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-1000357557", "body": ""}
{"comment": {"body": "Pretty much. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#discussion_r892802788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-994319333", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-994320203", "body": ""}
{"comment": {"body": "would they ever see both the installation sidebar and the installation wizard UI on the right? i.e. simultaneously?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#discussion_r888511793"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-994320780", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-994383530", "body": ""}
{"comment": {"body": "Technically no.\r\n\r\nThe installation wizard UI is part of onboarding and is really only triggered by the hub app.\r\n\r\nThe sidebar is how most people will deal with installations moving forward.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#discussion_r888564084"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-998631000", "body": ""}
{"comment": {"body": "This is fine for now, but something to think about: is managing state this way better then managing it with a single state value and view?\r\n\r\n```\r\ntype SidebarState =\r\n  { $case: 'unauthenticated' } |\r\n  { $case: 'uninstalled' } |\r\n  { $case: 'ready', values: ... }\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#discussion_r891559814"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-998638083", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1610#pullrequestreview-998870829", "body": ""}
{"title": "Clip thread title client-side", "number": 1612, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1612", "body": "Needs client-side work"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1612#pullrequestreview-994267836", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1612#pullrequestreview-994270120", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1612#pullrequestreview-994270651", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1612#pullrequestreview-994274744", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1612#pullrequestreview-994276584", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1612#pullrequestreview-994370782", "body": ""}
{"title": "fix dev and prod configs", "number": 1613, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1613"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1613#pullrequestreview-994252383", "body": ""}
{"title": "fix stuff", "number": 1614, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1614"}
{"title": "Remove stale entires", "number": 1615, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1615"}
{"title": "Make onboarding shiny and chrome", "number": 1616, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1616", "body": "Don't merge this until Jeff is ready to roll"}
{"title": "Time for a clean shave", "number": 1617, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1617", "body": ":)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1617#pullrequestreview-994265957", "body": "HR alert"}
{"title": "AddTeamIcon", "number": 1618, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1618", "body": ""}
{"comment": {"body": "I know you merged this yesterday, just wanted to say this looks good \ud83c\udf89 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1618#issuecomment-1146368839"}}
{"title": "Accidentally broke post-login popup", "number": 1619, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1619"}
{"title": "Add \"format: uuid\" to properties, path parameters, and query parameters", "number": 162, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162", "body": "For languages that support UUID, these properties and parameters will be automatically converted to UUIDs, avoiding the need to convert between string and UUID everywhere when referring these.\nFor languages that don't support UUID (like javascript) these stay as strings.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#pullrequestreview-866691325", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#pullrequestreview-866692354", "body": "Oh cool, no need for mustache template change.\nDoesn't work for comma-separated lists of UUIDs. We can figure that out later though.\nyaml\n        - in: query\n          name: sourcemark\n          required: true\n          schema:\n            type: string\n          description: A comma separated list of sourcemark IDs"}
{"comment": {"body": "missed this one", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#discussion_r794876524"}}
{"comment": {"body": "missed this one", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#discussion_r794877157"}}
{"comment": {"body": "maybe we can define a \"uuidList\" format? (for future)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#discussion_r794878234"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#pullrequestreview-866693564", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#pullrequestreview-866697661", "body": ""}
{"comment": {"body": "Yup, looking into how we can support this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#discussion_r794881072"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#pullrequestreview-866698414", "body": ""}
{"comment": {"body": "thanks!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#discussion_r794881590"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#pullrequestreview-866698482", "body": ""}
{"comment": {"body": "thanks!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/162#discussion_r794881634"}}
{"title": "Reset onboarding menu item", "number": 1620, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1620", "body": "\nPotentially useful for demos. Not actually wired up yet because we need another API to do this.\nThis is a secret menu item only accessible with an option-command-control click"}
{"comment": {"body": "Do it in admin web", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1620#issuecomment-1145434302"}}
{"title": "Fix loading landing environment", "number": 1621, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1621"}
{"title": "Retry Refresh Auth UNB-166", "number": 1622, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1622", "body": "Attempt to fix issues with retrying refresh auth.\nDifficult to reproduce as there are potentially CORS issues causing this..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1622#pullrequestreview-1003224911", "body": ""}
{"comment": {"body": "Bumped this up to max 5 with backoff.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/766a49dd-13d7-4b6c-b205-cd7e2216d961?message=be6890b5-4544-4495-b4db-4681f43f5a6e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1622#discussion_r894830500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1622#pullrequestreview-995549689", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1622#pullrequestreview-995549800", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1622#pullrequestreview-995963694", "body": ""}
{"comment": {"body": "A limited number of retries won't deal with degraded network scenarios. For refresh, you'll probably want to retry forever with a backoff scheme until you receive a 401", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1622#discussion_r889629552"}}
{"title": "Update Person's \"Seen tutorial\" in VSCode", "number": 1623, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1623", "body": "Update persons model with \"seen tutorial\" on Tutorial load."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1623#pullrequestreview-995098302", "body": ""}
{"title": "Reset tutorial onbaording from admin web", "number": 1624, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1624", "body": "related https://github.com/NextChapterSoftware/unblocked/pull/1620, https://github.com/NextChapterSoftware/unblocked/pull/1623"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1624#pullrequestreview-994329844", "body": ""}
{"title": "Add basic welcome email handler", "number": 1625, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1625"}
{"title": "Clean up more stuff", "number": 1626, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1626"}
{"title": "Find OnboardingModel from repo url", "number": 1627, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1627"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1627#pullrequestreview-994409980", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1627#pullrequestreview-994412309", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1627#pullrequestreview-994417459", "body": ""}
{"title": "Add team avatar urls", "number": 1628, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1628"}
{"title": "Try again", "number": 1629, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1629"}
{"title": "Switch domain getunblocked", "number": 163, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/163", "body": "Updated Dev and SecOps config with new hostedZones \nDeployed changes to Dev and generated new wildcard cert\nUpdated dns name prefix in apiservice helm chart \nUpdated Dev's wildcard cert ARN in helm values file \nUpdated EKS installation instructions to use new domain\nThese changes have been deployed and last piece of the puzzle is to roll out apiservice with the new domain and cert."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/163#pullrequestreview-866701554", "body": ""}
{"title": "build notification", "number": 1630, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1630"}
{"title": "Update ci", "number": 1631, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1631"}
{"title": "TestEmail2", "number": 1632, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1632", "body": "Revert \"Try again (#1629)\"\nRevert \"Add team avatar urls (#1628)\"\nUpdate ci"}
{"title": "update", "number": 1633, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1633"}
{"title": "[IGNORE] Testing ingestion of open PRs", "number": 1635, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1635"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1635#pullrequestreview-995306220", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1635#pullrequestreview-995356645", "body": ""}
{"title": "Fix look of landing page on mobile", "number": 1636, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1636"}
{"title": "Use originalLine and originalStartLine for the source snippet", "number": 1637, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1637", "body": "This is more accurate since line and startLine can change, but we use it for processing the diffHunk which does not change."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1637#pullrequestreview-995581795", "body": ""}
{"title": "Add hub highlight API", "number": 1638, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1638", "body": "Add API to trigger hub highlight.\n@kaych Please add this when you work on the relevant step in the tutorial?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1638#pullrequestreview-995552417", "body": ""}
{"title": "Update", "number": 1639, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1639"}
{"title": "Add CI run to shared web components", "number": 164, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/164", "body": "Add prettier, ESLint, and test support to shared web components\nAdd CI run to shared web components"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/164#pullrequestreview-866768989", "body": ""}
{"title": "Update hasSeenTutorial flag", "number": 1640, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1640"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1640#pullrequestreview-995559995", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1640#pullrequestreview-995563104", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1640#pullrequestreview-995568456", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1640#pullrequestreview-995575931", "body": ""}
{"title": "Return data objects from PersonStore", "number": 1641, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1641"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1641#pullrequestreview-995592351", "body": "Thanks man"}
{"title": "Don't resubscribe to thread changes", "number": 1642, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1642", "body": "More permanent fix for the temporary fix in this PR: https://github.com/NextChapterSoftware/unblocked/pull/1535\nEssentially, instead of unsubscribing and resubscribing to the thread stream every time the sourcemarks change, we can just reuse the cached thread data we had before."}
{"comment": {"body": "> hmm, I tried this at first but did not work. Are you sure it works?\r\n\r\nIt seems to work fine for me, maybe there's a case I haven't tried yet?  General editing and such seems to be fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1642#issuecomment-1148878278"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1642#pullrequestreview-997440176", "body": "hmm, I tried this at first but did not work. Are you sure it works?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1642#pullrequestreview-997441291", "body": ""}
{"comment": {"body": "should we handle the case where threadInfo is null?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1642#discussion_r890719301"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1642#pullrequestreview-998433347", "body": ""}
{"comment": {"body": "If `threadInfo` is undefined, this will do what we want: we'll get a SourceMarkInfo with an undefined threadInfo.  This should only happen if we haven't yet loaded thread data for this sourcemark, it means we should expect the thread data to come in later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1642#discussion_r891422782"}}
{"title": "Fix flickering SourceMark gutter icons", "number": 1643, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1643", "body": "SourceMark gutter icons in VSCode were flickering often.  The source is unexpected:\n\nEvery 30s or so we refresh our auth token\nEvery time we refresh our auth token, we refresh the TeamStore (fetch a new set of Teams)\nWhenever the set of Teams changes, we refresh the RepoStore (to resolve workspace repos)\nWhenever the set of Repos changes, we re-load all our SourceMark information for all open files.\n\nIt is expected that 3 and 4 will be very infrequent (never, really) -- the mechanism is mainly in place so that we correctly load all our SourceMarks only after we've authed, loaded teams, and resolved repos.  However, the getTeams API call is now returning teams with an invite URL, which changes on every call, so every time our token refreshed, all of the above would trigger.\nThe fix is to fix the Teams stream so only update when the actual set of teams change."}
{"comment": {"body": "That\u2019s pretty shocking. Why are we changing the invite url on every call @pwerry @rasharab? Change it once a week or something?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1643#issuecomment-1146433170"}}
{"comment": {"body": "I talked to @rasharab about this and he's going to think about a solution for it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1643#issuecomment-1146440140"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1643#pullrequestreview-995619863", "body": ""}
{"title": "fix stuff", "number": 1644, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1644"}
{"title": "Hide VSCode scrollbars unless mouse is over element", "number": 1645, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1645", "body": "Merry Christmas @benedict-jw !!\nHide scrollbars in VSCode webview UIs unless the mouse is hovering overtop.\nFor the #root item, the scrollbars should fade out.\nFor non-root scrollable items, there is no fadeout, but they should automatically be hidden.  There's no way to get this to work automatically.  We can add special styling as a sub-element later if we want to."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1645#pullrequestreview-995758860", "body": ""}
{"title": "Adds web extension links to threads and messages", "number": 1646, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646", "body": "Completes UNB-196\nThis turned into a bit of a beast because in order to populate the provider extension, the repo url is required. The common point of action was the ThreadInfo object, so I added the repo in the decorator.\nTo call attention to a few things:\n- The messages API seems to have allowed for messages without source marks and threads without messages. I don't think these are legal states? The ThreadInfo decorator certainly implies that.\n- I modified some of the message API tests to work within the bounds of legal states"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-995946452", "body": ""}
{"comment": {"body": "I don't see why this was optional", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#discussion_r889603327"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-995947047", "body": ""}
{"comment": {"body": "We still need to echo back the latestModified value when no thread exists. But is this state even legal? Maybe this should just throw?\r\n\r\nPreviously a test was actually testing a thread with no messages (also illegal?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#discussion_r889604259"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-995947232", "body": ""}
{"comment": {"body": "No messages for a thread is illegal? I modified this test so that the thread contained only a _deleted_ message", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#discussion_r889604481"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-995953478", "body": ""}
{"comment": {"body": "Backfill. Repo is was only recently added to thread. Fairly sure we backfilled DB, I think, so if stuff starts breaking then this could be why.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#discussion_r889613469"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-995953819", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-995953916", "body": ""}
{"comment": {"body": "Technically this state is only prevented by the client, and the API layer. Nothing in the data model prevents this. We could set up a DB constraint, but probably overkill. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#discussion_r889614115"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#pullrequestreview-995954060", "body": ""}
{"comment": {"body": "The ThreadInfo decorator makes it illegal for a thread not to have a sourcemark, and then implicitly a message", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1646#discussion_r889614336"}}
{"title": "Fix codesigning in ci", "number": 1647, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1647"}
{"title": "Missed keychain setup in installer script", "number": 1648, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1648"}
{"title": "[WIP] Encrypt user secrets stored in the DB", "number": 1649, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1649"}
{"title": "Create TestApiClient to consolidate API calls in unit tests", "number": 165, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/165", "body": "Just DRYing up the code as suggested by the lovely @rasharab https://github.com/Chapter2Inc/codeswell/pull/160#discussion_r794708698"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/165#pullrequestreview-866752740", "body": "THANK YOU"}
{"title": "Encrypt user secrets stored in the DB", "number": 1650, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650", "body": "Adds logic to encrypt the GitHub user-to-server token that we use for posting messages from Unblocked to GitHub. This uses asymmetric encryption, where the API service encrypts the user token with the public key before storing it in the database. The SCM service is the only service with access to the private key, which will be used to decrypt the token."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650#pullrequestreview-1000256996", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/2749fced-9038-4489-b5f9-0d20b162ec84)\n\n\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/b51c2f84-5455-4789-b2f6-24dfa464c24e?message=9344b7f9-5836-4513-ba60-2fbd439e556d).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650#discussion_r892736274"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650#pullrequestreview-997354423", "body": ""}
{"comment": {"body": "Nice - this is definitely good enough for now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650#discussion_r890649148"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650#pullrequestreview-997438420", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650#pullrequestreview-998343831", "body": ""}
{"comment": {"body": "Question.\r\nCould this potentially lead to multiple instances of BouncyCastleProvider being added to what looks like some sort of singleton?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650#discussion_r891359909"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650#pullrequestreview-998377369", "body": ""}
{"comment": {"body": "Ah good question. Turns out `addProvider` checks for an existing instance before adding. From the javadoc:\r\n\r\n```\r\nReturns:\r\nthe preference position in which the provider was added, or -1 if the provider was not added because it is already installed.\r\n```\r\n\r\nJust for my own sanity, I ran:\r\n\r\n```\r\n@Test\r\nfun addProvider() {\r\n    println(Security.getProviders().map { it.name })\r\n    println(Security.addProvider(BouncyCastleProvider()))\r\n    println(Security.getProviders().map { it.name })\r\n    println(Security.addProvider(BouncyCastleProvider()))\r\n    println(Security.addProvider(BouncyCastleProvider()))\r\n    println(Security.getProviders().map { it.name })\r\n}\r\n```\r\n\r\nwhich printed the following where `BC` is the bouncy castle provider:\r\n\r\n```\r\n[SUN, SunRsaSign, SunEC, SunJSSE, SunJCE, SunJGSS, SunSASL, XMLDSig, SunPCSC, JdkLDAP, JdkSASL, Apple, SunPKCS11]\r\n14\r\n[SUN, SunRsaSign, SunEC, SunJSSE, SunJCE, SunJGSS, SunSASL, XMLDSig, SunPCSC, JdkLDAP, JdkSASL, Apple, SunPKCS11, BC]\r\n-1\r\n-1\r\n[SUN, SunRsaSign, SunEC, SunJSSE, SunJCE, SunJGSS, SunSASL, XMLDSig, SunPCSC, JdkLDAP, JdkSASL, Apple, SunPKCS11, BC]\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1650#discussion_r891383529"}}
{"title": "Hub copy", "number": 1651, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1651", "body": "\"along side\" is always \"alongside\"\n  \navoid use passive voice"}
{"comment": {"body": "FYI @benedict-jw update the designs to match....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1651#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1651#pullrequestreview-998392876", "body": ""}
{"title": "[Onboarding] Invite page", "number": 1652, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652", "body": "\n\n\n\nAdd CopyInput component for copy fields\nAdd email list validation to TextInput component \nCombine the TeamStore stream into the wizard stream listener for team data \nFetch a list of repo collaborators to populate the team list; add custom sort to order the emails by occurrence and filter common domains like gmail and hotmail to the end of the list"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-1002097126", "body": ""}
{"comment": {"body": "bump\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/8c1e67b4-850f-46a2-b658-cbd9779dc650?message=029aa76b-fe47-4558-b681-ea108d33e4fc).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#discussion_r894032256"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997079905", "body": ""}
{"comment": {"body": "I guess this is the downside of having an uncontrolled input... is it worth converting this to be a controlled input?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#discussion_r890464683"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997081624", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997082275", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997083780", "body": ""}
{"comment": {"body": "Should `props.onChange` be called here too, since the value has changed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#discussion_r890466789"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997105553", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997171689", "body": ""}
{"comment": {"body": "This is just temporary?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#discussion_r890520422"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997174806", "body": ""}
{"comment": {"body": "I'm wondering if this (& the below) should be in its own `CopyInput.scss` file, so it's easier to identify that it's tied to a different component?\r\n(editing test)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#discussion_r890522768"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997304242", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#pullrequestreview-997305264", "body": ""}
{"comment": {"body": "hmmm, there would be type issues I think, since `onChange` takes a ChangeEvent and the keydown event is a KeyboardEvent \r\n\r\nI guess `onChange` isnt triggered if the value is manually updated via the ref?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1652#discussion_r890611322"}}
