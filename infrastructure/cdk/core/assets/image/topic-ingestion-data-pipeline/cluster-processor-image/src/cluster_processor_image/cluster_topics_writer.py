import json
import os
import logging
from typing import List

from embedding_utils.embedding_generator import EmbeddingGenerator
from pandas import Data<PERSON>rame
from tqdm import tqdm

from cluster_processor_image.cluster_topics_writer_constants import CLUSTER_WRITER_UPSERT_BATCH_SIZE
from cluster_processor_image.path_constants import PROCESS_OUTPUT_DIRECTORY, PROCESS_OUTPUT_TOPICS_BASE_NAME
from cluster_processor_image.dataframe_constants import (
    CLUSTER_COLUMN,
    CLUSTER_TOPICS_COLUMN,
    DOC_TOPICS_COLUMN,
    DOC_ID_COLUMN,
    DOC_COLUMN,
)
from cluster_processor_image.topic_vector import TopicVector

from pinecone_utils.pinecone_vector_store import PineconeVectorStore
from vector_store_utils.vector_store_types import BaseDocument


class ClusterTopicsWriter:
    def write_cluster_topics(self, df: DataFrame):
        raise NotImplementedError("Subclasses must implement this.")


class PineconeClusterTopicsWriter(ClusterTopicsWriter):
    def __init__(
            self,
            repo_id: str,
            pinecone_store: PineconeVectorStore,
            embedder: EmbeddingGenerator,
            upsert_batch_size: int = CLUSTER_WRITER_UPSERT_BATCH_SIZE,
    ):
        self.__repo_id = repo_id
        self.__vector_store = pinecone_store
        self.__embedder = embedder
        self.__upsert_batch_size = upsert_batch_size

    def write_cluster_topics(self, df: DataFrame):
        # 1) Build all TopicVector instances for rows that have topics
        vectors: List[TopicVector] = []
        filtered_df = df[df[DOC_TOPICS_COLUMN].notna()]
        for _, row in filtered_df.iterrows():
            vectors.append(
                TopicVector(
                    repo_id=self.__repo_id,
                    doc_uuid=row[DOC_ID_COLUMN],
                    doc_cluster_id=row[CLUSTER_COLUMN],
                    doc_content=row[DOC_COLUMN].split("CONTENT:")[1],
                    doc_topics=row[DOC_TOPICS_COLUMN],
                    embedder=self.__embedder,
                )
            )

        # 2) Chunk into batches
        def _batched_vectors():
            for i in tqdm(
                    range(0, len(vectors), self.__upsert_batch_size),
                    desc=f"Processing Batches of size {self.__upsert_batch_size}",
            ):
                yield vectors[i : i + self.__upsert_batch_size]

        # 3) Convert each batch into BaseDocument and upsert via PineconeVectorStore
        for batch in _batched_vectors():
            docs: List[BaseDocument] = []
            for v in batch:
                vec_dict = v.to_dict()  # {"id", "values", "sparse_values"?, "metadata"}
                docs.append(
                    BaseDocument(
                        id=vec_dict["id"],
                        namespace=self.__repo_id,
                        metadata=vec_dict["metadata"],
                        embedding=vec_dict["values"],
                        sparse_embedding=vec_dict.get("sparse_values"),
                    )
                )
            # call the new add() API
            self.__vector_store.add(documents=docs, namespace=self.__repo_id)


class FileClusterTopicsWriter(ClusterTopicsWriter):
    __output_directory: str
    __output_base_name: str

    def __init__(
        self, output_directory: str = PROCESS_OUTPUT_DIRECTORY, output_base_name: str = PROCESS_OUTPUT_TOPICS_BASE_NAME
    ):
        self.__output_directory = output_directory
        self.__output_base_name = output_base_name

    def write_cluster_topics(self, df: DataFrame):
        if not os.path.exists(self.__output_directory):
            os.mkdir(self.__output_directory)

        for c in tqdm(df[CLUSTER_COLUMN].unique(), desc="Writing cluster topics..."):
            topics = df.query(f"{CLUSTER_COLUMN} == {c}")[CLUSTER_TOPICS_COLUMN].iloc[0]
            try:
                output_file = os.path.join(self.__output_directory, f"{self.__output_base_name}-{c}.txt")
                with open(output_file, "w") as f:
                    f.write(topics)
            except Exception as e:
                logging.exception(f"Failed to process cluster topics '{c}'. Exception: {str(e)}")

            try:
                topics_json = json.loads(topics)
                output_file = os.path.join(self.__output_directory, f"{self.__output_base_name}-{c}.json")
                with open(output_file, "w") as f:
                    json.dump(topics_json, f)
            except Exception as e:
                logging.exception(f"Failed to process cluster topics '{c}'. Exception: {str(e)}")

        clusters = []
        filtered_df = df[df[DOC_TOPICS_COLUMN].notna()]
        for index, row in filtered_df.iterrows():
            clusters.append(
                {
                    "doc_cluster_id": row[CLUSTER_COLUMN],
                    "doc_content": row[DOC_COLUMN].split("CONTENT:")[1],
                    "doc_topics": row[DOC_TOPICS_COLUMN],
                }
            )

        keyword_to_document_map = {}
        for cluster in clusters:
            doc_topics_list = json.loads(cluster["doc_topics"])
            for topic in doc_topics_list:
                if topic not in keyword_to_document_map:
                    keyword_to_document_map[topic] = [cluster["doc_content"]]
                keyword_to_document_map[topic].append(cluster["doc_content"])

        try:
            output_file = os.path.join(self.__output_directory, "fine-tuning.jsonl")
            with open(output_file, "w") as f:
                for item in keyword_to_document_map:
                    query = {"query": "Explain " + item, "relevant_passages": keyword_to_document_map[item]}
                    json.dump(query, f)
                    f.write("\n")
        except Exception as e:
            logging.exception(f"Failed to process cluster topics. Exception: {str(e)}")
