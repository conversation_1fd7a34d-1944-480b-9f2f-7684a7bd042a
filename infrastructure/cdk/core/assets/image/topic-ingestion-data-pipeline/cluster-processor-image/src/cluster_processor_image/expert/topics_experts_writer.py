import dataclasses
import json
import logging
import os
from typing import List

from pandas import Data<PERSON>rame

from cluster_processor_image.dataframe_constants import DOC_TOPIC_COLUMN, DOC_TOPIC_EXPERTS_COLUMN
from cluster_processor_image.expert.topic_expert_types import TopicExperts
from cluster_processor_image.path_constants import (
    PROCESS_OUTPUT_DIRECTORY,
    PROCESS_OUTPUT_EXPERTS_BASE_NAME,
)


class TopicsExpertsWriter:
    def write_topics_experts(self, df: DataFrame):
        pass


class FileTopicsExpertsWriter(TopicsExpertsWriter):
    __output_directory: str
    __output_base_name: str

    def __init__(
        self, output_directory: str = PROCESS_OUTPUT_DIRECTORY, output_base_name: str = PROCESS_OUTPUT_EXPERTS_BASE_NAME
    ):
        self.__output_directory = output_directory
        self.__output_base_name = output_base_name

    def write_topics_experts(self, df: DataFrame):
        if not os.path.exists(PROCESS_OUTPUT_DIRECTORY):
            os.mkdir(PROCESS_OUTPUT_DIRECTORY)

        topics_experts: List[dict] = []

        # Iterate over each group and write to a file
        for topic, experts in df.groupby(DOC_TOPIC_COLUMN)[DOC_TOPIC_EXPERTS_COLUMN]:
            topics_experts.append(experts.iloc[0])

        try:
            topics_experts_dict = {"topic_experts": topics_experts}
            output_file = os.path.join(self.__output_directory, f"{self.__output_base_name}.json")
            with open(output_file, "w") as f:
                json.dump(topics_experts_dict, f)
        except Exception as e:
            logging.exception(f"Failed to process topics experts. Exception: {str(e)}")
