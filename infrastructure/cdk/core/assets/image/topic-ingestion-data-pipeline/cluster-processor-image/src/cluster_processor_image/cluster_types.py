import json
from dataclasses import dataclass

from typing import List, Any
from integration_utils.message import MessageInterface


@dataclass(order=True)
class EmbeddedDoc:
    message: MessageInterface
    embedding: List[float]


@dataclass(order=True)
class TopicClusterResultItem:
    name: str
    score: float
    # summary: str
    doc_ids: List[str]

    @staticmethod
    def from_json(json_str: str, json_field: str = "topics") -> list[Any]:
        results_json = json.loads(json_str)
        return [TopicClusterResultItem(**topic) for topic in results_json[json_field]]
