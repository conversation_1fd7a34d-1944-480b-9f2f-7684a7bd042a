{"title": "update get_release_tag script", "number": 8, "htmlUrl": "https://bitbucket.org/levl/eros-models/pull-requests/8", "body": ""}
{"title": "L2 model feature branch", "number": 1, "htmlUrl": "https://bitbucket.org/levl/l2-typing-model-status/pull-requests/1", "body": "test tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\ntest tag\n\n"}
{"title": "Feature/upload download scripts", "number": 1, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1", "body": "Added download script\nAdd upload script\n\nI want to use these scripts in recording. They might be needed to be as part of the automatic recording in some manner in the future."}
{"comment": {"body": "Beautiful. What about rising speed? \\(`http://wifi.levl.tech:10103/download?speed=500&raise=100` will download in a speed that starts at 500/bps and accelerates at 100/bps^2\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1/_/diff#comment-140277542"}}
{"comment": {"body": "No need for now, but good point", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1/_/diff#comment-140277754"}}
{"comment": {"body": "See if you can use bash process substitution \\([https://www.gnu.org/software/bash/manual/html\\_node/Process-Substitution.html](https://www.gnu.org/software/bash/manual/html_node/Process-Substitution.html)\\) `<(cat /dev/urandom)` instead of creating a file with `dd`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1/_/diff#comment-140278354"}}
{"comment": {"body": "I haven\u2019t seen an option to read from stdin and it fails when reading from /dev/random \\(`\"data=@/dev/urandom\"`\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1/_/diff#comment-140278820"}}
{"title": "Revert \"Feature/recording server client (pull request #8)\"", "number": 10, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/10", "body": "This reverts pull request #8.\n\nAdd an option to send CFRs from the server to some PC inside levl.\nSimple publisher, subscriber threads integrate into worker\nThis is on purpose without SSH tunnels since it looks like throughput via SSH in Windows is very low.\npublisher & subscriber work. Integration into cfr_worker.py would be tested later.\n\n"}
{"title": "User cooperation", "number": 100, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100", "body": "If a user is supposed to be sending CFRs, it better be doing it at a good enough rate (for now, its expected to send 400 CFRs in the last 10 seconds, so an average of 40/s), otherwise, it gets failed authentication."}
{"comment": {"body": "This code should be moved to the relevant states code - otherwise it\u2019s difficult to understand to which states it refers.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148467967"}}
{"comment": {"body": "Does the responsiveness mechanism also handle a user connected without any received packets? If we allow connected users for a long time we should also make it visible in the UI \\(need to ask Mich which state to show in the UI\\).  \n", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148471135"}}
{"comment": {"body": "It doesn\u2019t care about connections, only about users that we decided we should be monitoring that are not cooperating well enough. The UI shows \u201cTraining\u201d or \u201cClassifying\u201d.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148471467"}}
{"comment": {"body": "It doesn\u2019t refer to any state", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148471588"}}
{"comment": {"body": "It should be done in state.periodic\\_state\\_management..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148472694"}}
{"comment": {"body": "This line doesn\u2019t refer to any specific user, it\u2019s just iterating over all users that are identified as uncooperative. I agree that the actual \u201cfailuring\u201d of a user should be done by the current state though, but we\u2019re already discussing that below", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148473032"}}
{"comment": {"body": "As far as I know it doesn\u2019t show that, since this is not the state.. but even if so, please talk to Mich, we discussed it a few days ago and he agreed that we cannot show training/classifying for a long time, it looks bad. There should be a different state shown to the user, I think Mich mentioned QUEUED or someth,", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148473187"}}
{"comment": {"body": "Alright. But we already did show training/classifying for a long time anyway, it\u2019s not relevant to this PR. Please open an issue", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148473322"}}
{"comment": {"body": "I mention it here since here you remove the connection without data mechanism.. which took care of that..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148473545"}}
{"comment": {"body": "This mechanism never worked because there was a bug that always caused timeout to be infinite, once I fixed the inifinite timeout bug, this mechanism would kick users out for no good reason because they didn\u2019t get their scheduling because we\u2019re only recording one user at a time, causing all users but the first one to get priority to fail authentication, so this mechanism was disabled \\(CCP-58\\). This PR is here to fix CCP-58 by only kicking out users that are prioritized and are not cooperating instead.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148473842"}}
{"comment": {"body": "The thing is, state.periodic\\_state\\_management is called per user anyway. So I don\u2019t think you should add another API method to the states, I think that inside the states code, we should check if the user is unresponsive, and then act accordingly..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148473884"}}
{"comment": {"body": "I see.. didn\u2019t realize it was disabled. It did work initially BTW, but ok then this can be done in another issue.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/100/_/diff#comment-148474170"}}
{"title": "Release/charter poc", "number": 1000, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1000", "body": "no need to wait for DUID for RPi, we have WPS\nfilter classification events\n\n"}
{"title": "Release/charter poc", "number": 1001, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1001", "body": "no need to wait for DUID for RPi, we have WPS\nfilter classification events\n\n"}
{"title": "UI Analytics per device merge", "number": 1002, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1002", "body": ""}
{"comment": {"body": "reuse total query with option in the where clause", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1002/_/diff#comment-226734340"}}
{"title": "Snapshot system state - Events snapshot and DB backup with dummy replay flow (Initial commit)", "number": 1003, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1003", "body": "For the replay system, we need to store the system events and system state (for now only DB), so we will be able to replay the events with db snapshot both in high speed and try to achieve the previous state or try to investigate features/system accuracy.\n\nThe snapshot is done to json files with {timestamp, event} rows using same mechanism as pcap writer. All the events are stored (we can replay only subset of those) and this can be enhanced.\nThe system state stored is the DB (devices, history log, decision log, dhcp). Both caches and sessions have not significant data in time.\nState management moved to be event driven, so all system is event driven.\nFuture: Upload to Cloud storage.\nsnapshots and replay folders added\nrun_server added replay flag with desired folder containing the events to ingest\nreplay thread load files according the flag and ingests to data processor\nFuture: add configuration file for rate control and windowing and etc..\nWe can restore the DB state using the existing run_server -R (copy the sql snap to /tmp and compress tar -cjvf file.tar.bz2 20210427-201657.sql) before running with replay flag and thus try to achieve previous system state\nInitial split of Events in Data processor to 2 Queues\n\n"}
{"comment": {"body": "If the difference between `verbose` or not is `-Fc -v` argument, the branches can be simplified with more code reuse", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1003/_/diff#comment-220996995"}}
{"comment": {"body": "The priority queue pattern requires listening to both queues at the same time, not one after the other, as now for internal events, the system may wait 1 seconds since external events queue is empty.\n\nI was suggesting something like a `select` on both queues and them processes them by the priority.  \nIf `select` is not available, I see that [PriorityQueue](https://docs.python.org/3/library/queue.html#queue.PriorityQueue) is available for this matter exactly.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1003/_/diff#comment-223793271"}}
{"title": "Remove MAC based classification for wired Android devices", "number": 1004, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1004", "body": "Don't use previous mac addresses as identifieres (either solid or weighted) when it's an Android device connected with a wired adapter."}
{"title": "Feature/add timeout on stream agent", "number": 1005, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1005", "body": "If the stream sockets get into timeout states, the serving threads never close. This happens, for example, when the AP reboots.\nIve modified the sockets to have timeouts (60 seconds), making sure that the serving threads close.\nIssue discovered with ticket "}
{"comment": {"body": "Can you explain what this and the change in `chunk = stream.read()` do? ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1005/_/diff#comment-220312719"}}
{"comment": {"body": "This causes the `read` to be non-blocking, i.e. `read` returns all the data that there\u2019s in the buffer.\n\nWe still do `select` on the socket, so there\u2019s no busy waiting except for 2 second `select` timeout.\n\nWhat I wanted to enforce here is to do periodic `read`, so that `socketserver.socket.timeout` would arise upon timeouted socket, which is not triggered with `select`.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1005/_/diff#comment-220314378"}}
{"title": "Compare Only Allowed Manufacturers", "number": 1006, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1006", "body": "On initial manufacturer comparison in canonical_device_model_compare, determine no-match only if manufacturers are part of comparable group"}
{"comment": {"body": "Oops, last commit was supposed to go to branch `remove_mac_based_classification_for_wired_android_devices`. Leaving it here as these PRs will all be merged now", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1006/_/diff#comment-220144510"}}
{"title": "Ignore null ID for /state/device/ API", "number": 1007, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1007", "body": "Non-identified devices dont have levl IDs, so API is called with null. So dont return 404 for those requests."}
{"title": "Enable ICMP TS for wired devices", "number": 1008, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1008", "body": "Triggering pinger and arper for wired devices, not only wireless.\nEnabled flag for icmp_ts.\n\n"}
{"title": "Feature/project genesis phase 3", "number": 1009, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1009", "body": "Genesis phase 3 implementation\n\n\nCan train, classify, store artifacts and some logs\n\nClassifier is cascade of Multiclass classifier and OneClass classifier\n\n\n\nGenesis is its own service, hosting all supplement services:\n\nmlflow server\nminio server\nredis server\nrediscommander server\nrabbitmq server\n\n\n\nGenesis service\n\nAPI transport: AMQP\nAPI provider: nameko\n\nAPIs:\n\n\ndata_endpoint\n\nJust puts CFRs in redis\n\n\n\ntraining_start\n\nAsync command to start training device belonging to a group\n\n\n\ntraining_cancel\n\nAsync command to stop training device belonging to a group and/or remove model\n\n\n\nclassification_get\n\nSync command to get classification result for device belonging to a group\n\n\n\n\n\nTraining:\n\nWorks with Celery (over AMQP transport)\n\nStarts worker per group\n\nChecks that the devices in that group have enough data, based on DeviceClustering module\nOn enough data, starts training a classifier on that group\nStores artifacts and logs in redis and mlflow\n\n\n\nClassification:\n\nChecks that the device can be tested (have group model) and has enough data\nreplies CANT DECIDE, HAVE CANDIDATE, NO CANDIDATE\n\nSupplement services:\n\n\nMLFlow\n\nFramework for archiving ML related logs, artifacts, for reproducibility, and useful UI\nClears some logs from the console\nUses DB (local sqlite3 file) and artifacts store (minio, file store service with s3-like API)\n\n\n\nRedis\n\nGood file store\nhas RedisCommander for web interface to the datastore\n\n\n\nRabbitMQ\n\nBroker for AMQP transport\n\n\n\nKnown issues:\n\nChannel 40 support only\nNo snapshot/restore functionality\n\n"}
{"title": "Added proper parsing of WiFi settings", "number": 101, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/101", "body": "Should probably solve CCP-73. Will test later tonight.\nThis also restores report_connections because I was not aware it was in such wide usage outside of the agent.\n"}
{"comment": {"body": "i was wondering about this. thanks", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/101/_/diff#comment-148565504"}}
{"comment": {"body": "well done!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/101/_/diff#comment-148565729"}}
{"comment": {"body": "What\u2019s the status on it? Are we still planning to merge it?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/101/_/diff#comment-149776546"}}
{"comment": {"body": "This PR is ready for review.\n\nIt doesn\u2019t solve CCP-73, I\u2019m going to push this to a later PR since it requires further analysis.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/101/_/diff#comment-154671068"}}
{"title": "Update Static CFR phase test threshold", "number": 1010, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1010", "body": "Update phase comparison threshold as a response to ."}
{"comment": {"body": "what was the phase threshold before?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1010/_/diff#comment-221023912"}}
{"comment": {"body": "Same as the amplitude, 0.98", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1010/_/diff#comment-221026416"}}
{"title": "config change", "number": 1011, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1011", "body": ""}
{"title": "Changes from PR-920 and PR-956 (guglielmo) that are needed here too", "number": 1012, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1012", "body": "PR-920\nPR-956\nBoth changes were merged to guglielmo, and itll be better to merge them to plume as well, especially if well run them in switch mode"}
{"title": "ifconfig.co hope it wont die soon", "number": 1013, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1013", "body": ""}
{"comment": {"body": "@{6085103f5797db006947d59a} Should we merge this or wait for the multiple services check?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1013/_/diff#comment-223711770"}}
{"comment": {"body": "I have just added it  so you can take a look and merge", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1013/_/diff#comment-223713551"}}
{"title": "Feature/plume is now enterprise mode", "number": 1014, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1014", "body": "Plumes AP to tell that it's hospitality agent\nFix request_ip feature not used in hospitality mode\nlast seen logic is canceled in switch mode\n\n"}
{"comment": {"body": "we won\u2019t flash firmware to plume.. do we need to update this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1014/_/diff#comment-221744492"}}
{"comment": {"body": "We need hospitality in SSID because:\n\n![](https://bitbucket.org/repo/8X5z9dk/images/2628970769-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1014/_/diff#comment-221744854"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} I understand, but we will change bootstrap in Plume with SSID and not flash it.. does this fix required?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1014/_/diff#comment-221746235"}}
{"comment": {"body": "@{5f82bf320756940075db755e} yes, we can change just the bootstrap.sh for the ssid issue", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1014/_/diff#comment-221755322"}}
{"title": "CFR should not be in enabled endpoints", "number": 1015, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1015", "body": "Fixed issue introduced in "}
{"title": "Some fixes for guglielmo - DUID, TCP_TS, IPv4 feature, History times", "number": 1016, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1016", "body": "Some fixes for Guglielmo:\n\nRemoved DUID from the mandatory condition for iOS devices, it takes forever in their network configuration and increases our decision times.\nRemoved the TCP_TS from the solid identifiers after it caused a double model there today in the morning\nFixed requested_ipv4 feature to work (Copied it from PR-1014, thanks @{5b41d9de10d57114135eca66} )\nBrought back the history writing for specific timezone - thats what Guglielmo saw until now, I dont want to change it at the last week of their testings (until now this change was anyway locally in their server)\n\n"}
{"comment": {"body": "we want to enable android hostname?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1016/_/diff#comment-221744905"}}
{"comment": {"body": "It was enabled all this time, let\u2019s keep it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1016/_/diff#comment-221746967"}}
{"title": "Modifications for tap mode router", "number": 1017, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1017", "body": "Modifications for hybrid router-tap mode:\n\nRemoved \"hospitality\" validation in SSID names,\ndisabled pinging from AP,\ndisabled TCP TS and ICMP TS\n\nPlume bridge mode:\n\neth0 (wan) and eth1 (lan) are bridged\nbr-demo is now DHCPv4 client and not server. DHCPv6 server remains\nAdd DHCP relay so that DHCP packets go through AP CPU\nall.sh will update the IP address of the DHCP server for the DHCP relay\n\n\n\nFor testing, R1 is also in bridge mode\n\n\n"}
{"comment": {"body": "Dont' we need to rollback some last commits?\n\nfor example?\n\n```\n\nexport CURRENT_ROUTER_DEFAULT_SSID_5_GHZ=\"Levl Eval 5GHz\"\n\n26\n+Added:export CURRENT_ROUTER_DEFAULT_SSID_2_4_GHZ=\"Levl-hospitality 2.4GHz\"\n\n27\n+Added:export CURRENT_ROUTER_DEFAULT_SSID_5_GHZ=\"Levl-hospitality 5GHz\"\n28\n28\n Unchanged:export CURRENT_ROUTER_MAC_2_4_GHZ=\"00:03:7f:a4:64:81\"\n29\n29\n Unchanged:export CURRENT_ROUTER_MAC_5_GHZ=\"00:03:7f:a4:54:81\"\n30\n30\n Unchanged:export CURRENT_ROUTER_CHANNEL_2_4_GHZ=\"6\"\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1017/_/diff#comment-221991532"}}
{"comment": {"body": "Where is this? is this in release/plume\\_poc?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1017/_/diff#comment-221993489"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} [https://bitbucket.org/levl/comcast/pull-requests/1014](https://bitbucket.org/levl/comcast/pull-requests/1014){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1017/_/diff#comment-221994048"}}
{"comment": {"body": "Do the DHCPv6 server just works out of the box? Or did you have to do something to enable it?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1017/_/diff#comment-222450490"}}
{"comment": {"body": "Out of the box. I just had to disable the DHCPv4 server", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1017/_/diff#comment-222450506"}}
{"title": "important to recording system: correct read signature", "number": 1018, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1018", "body": ""}
{"title": "Consistent device detection times", "number": 1019, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1019", "body": ""}
{"title": "CCP-64 implement duration feature", "number": 102, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/102", "body": "The duration feature runs on RadioTap packets. From each packet it retrieves type, subtype and duration. \nWe expect that each (type, subtype) -> duration will be the same in model and in classification.\nFor train we use the most common duration per type-subtype combination, provided its 90% of the durations for this combination; \nFor classify we use the most common duration from the durations that were received, per type-subtype combination.\nSome type-subtype combinations have durations that are too unreliable, so we ignore these tuples (see unreliables)."}
{"title": "caps filtering for non-tap only", "number": 1020, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1020", "body": "Issue arose in this bug: "}
{"comment": {"body": "We should ignore None for all CAPS\u2026 and thus remain with Physical hoping\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1020/_/diff#comment-222560527"}}
{"title": "laptop_sanity1&2&functional", "number": 1021, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1021", "body": "Created branch + added initial code to find duplicate models in pg\ncosmetics + ToDos\npg_testing_2.py in pg directory\nminor changes\nDavid added few more functionalities - still need to connect to generate.py\"\ninitial api tesitng\ncommitning today's progress\ninitial api night testing\n.\nimplement dataclass for Device obj\ncommiting today's progress\nlocal changhes for api tests\nwork on hist file with pandas for test enforce\nneed Itey's help with parsing the df from request\nremote control module, consult dev about the dataclass\nremote control module, consult dev about the dataclass\nrenaming, reorganizing, todays progress\nadded rpi funcs, router_utils, set_utils\nserver_utils, router_utils, setup_utils, minor progress on rpi&tests\nsmall midifications for remote_rpi control\nupdates to setuputils, tests need some work\nadded current_ssid.sh\nrouter changes\nfirst btu simple e2e test for rpi and utils fixes\nadded nightly test for nuriel\nqa_tools/api/laptops_remote/init.py\nqa_tools/api/laptops_remote/init.py\nlaptop remote & router init updtae\ncfr_nightly changed name, onboard rpi2 modified, setuprouter fixed\nNuriel's test nightly CFR, modify basic test, modify router setup\nlinux automation sanity 1\ndeleted unnecesary files, modified random problem\ncomplete test case test_rpi.py\nlaptop_sanity1&2&functional\n\n"}
{"title": "The self.data_processor.dhcp_requested_ips wasn't containg the first DHCP request from the device", "number": 1022, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1022", "body": "Issue arose in bug  and it happens when the DHCP lease pool is full.\n"}
{"title": "support static IP in switch mode", "number": 1023, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1023", "body": ""}
{"comment": {"body": "Shouldn\u2019t the target be **release/plume\\_poc**?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1023/_/diff#comment-224101428"}}
{"comment": {"body": "Please fix the Git LFS files that turned non-LFS here", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1023/_/diff#comment-224299021"}}
{"title": "Static IP for switch mode in bridge as well", "number": 1024, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024", "body": ""}
{"comment": {"body": "What is the difference from PR #1023?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-224323603"}}
{"comment": {"body": "lfs files", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-224339423"}}
{"comment": {"body": "why do we need enterprise mode which only enabled wifi 5ghz?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225323706"}}
{"comment": {"body": "why remove this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225323997"}}
{"comment": {"body": "is this relevant?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225324609"}}
{"comment": {"body": "it supports both, we wanted to reduce memory consumption. in router mode it probably not a problem", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225330322"}}
{"comment": {"body": "It was relevant for bridge mode, because arp replies where not sent to the device any more.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225330763"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Can I uncomment this, then?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225331339"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Can I revert to interface `2` then?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225331604"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} I wouldn\u2019t touch it at this point as we run most of our tests in this configuration.\n\n\u200c\n\nAnything you think might be problematic here?  \nI think it was originally included to prevent some bad RPI ARP formats.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225335120"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} I think that it was so that we wait until we get ARP replies from the device, after we arpinged it. In case we got ARP probes to a dead device", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225335943"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} By a dead device you mean devices that were badly configured on the network \\(like bad DNS server, etc\\)?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1024/_/diff#comment-225339496"}}
{"title": "Replay System - Not for merge yet", "number": 1025, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1025", "body": "move state management to event\nFix state event management\nEnhance the buffer writer to support not only dpkt/pcap files\nsnapshot events and db periodically\ninitial replay capabilities\nAdding replay thread and initial flow with events ingestion\nAdding replay thread and initial flow with events ingestion\ncleanup code\ncr fixes\nsplit events in data processor - internal/external\nNow using the packets timestamp for external events, and internal time counting for internal events\nChanged the StateManagementEvent handling\nNow using the real time for CFR packets as well\n\n"}
{"comment": {"body": "this should not be indented. It should be outside the if", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1025/_/diff#comment-231767294"}}
{"title": "Genesis doesn't support non-5GHz CFRs, so don't send them to genesis", "number": 1026, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1026", "body": ""}
{"title": "Feature/refactor", "number": 1027, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1027", "body": "This is the first version of our testing infrastructure, at the coming versions, well add more tests and support more OS versions. \nCurrently, we support Raspbarian, Ubuntu(16-20), win10, and macOS-Catalina.  \nAlthough this is not the PR we wished for, and theres a lot of work ahead; considering the design and better test syntax, we look forward to your reviews."}
{"title": "Adding additional engeniuses configuration", "number": 1028, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1028", "body": "We have some new EnGeniuses  :slight_smile: Lets use them\n(Took the rnd.yml and ovpn certs from @{5dbeb866c424110de52552cc} 's PR in eros-data-collector)"}
{"title": "freeze keras version", "number": 1029, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1029", "body": "New version causes known issue: "}
{"title": "Flag for user cooperation", "number": 103, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/103", "body": ""}
{"title": "Master", "number": 1030, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1030", "body": "Genesis doesn't support non-5GHz CFRs, so don't send them to genesis\nCopied the configuration for our additional engeniuses from eros-data-collector\nfreeze keras version\n\n"}
{"title": "Feature/improve genesis and QOL changes", "number": 1031, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1031", "body": "QOL changes:\n\nenv stuff\ndelete volume only if it exists\nchange to apt-get update && apt-get install\n\n\n\nGenesis fixes:\n\nFix exception of missing parameter\nRedis memory limit\nSync with genesis service bringup\n\n\n\n"}
{"title": "Master", "number": 1032, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1032", "body": "env stuff\ndelete volume only if it exists\nSync with genesis service bringup\nchange to apt-get update && apt-get install\nFix exception\nredis memory limit\nAdd comment\n\n"}
{"title": "Phy neural network noam", "number": 1033, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1033", "body": "\n\nFirst commit of BF classification using the new infrastructure\nFixed bug in filtering nc,fb,cw\nPossible separation in Hawkeye data, 20MHz\nFixed a bug in which the test data always included the training file\n\n"}
{"title": "Ikeja user and timezone", "number": 1034, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1034", "body": ""}
{"title": "Feature/11.4 fw", "number": 1035, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1035", "body": "Introducing the build server of the qsdk 11.4 for engenius and cypress APs.\nThis QSDK is mainly for the extra CFR information provided in the new SDK.\nTheres an example script (rec_rcc.sh) on how to work with new RCC modes.\nTheres need in different parsing/setting of htmode for AX since for 11.4, its ht20/ht40/ht80/etc instead of he20/he40/he80/etc\nExtra fun stuff:\n\n1G RAM for Engenius APs\nLEDs working (at least for Engenius)\nBuild information (git commit and date) in target (in /build.txt)\n\nBuild documentation: \nExample build.txt\n\nroot@WAP386:~# cat /build.txt\nqsdk=11.4_00001.2\nqsdk-git=17d60368588bffd6aa5cdb7a299908d93c0c5e7a\nqsdk-build-date=Sun Jun 13 13:43:48 UTC 2021\nvanilla-img-git=4583a52007f9e5dec6950bdd78fb8cc1133e0d25\nvanilla-img-build-date=Mon Jun 14 09:32:29 UTC 2021\nlevl-patches-img-git=4583a52007f9e5dec6950bdd78fb8cc1133e0d25\nlevl-patches-img-build-date=Mon Jun 14 10:22:37 UTC 2021\nengenius-levl-patches-git=695dd60e4b34f3adad34f6e02adc95a2da267b2a\nengenius-levl-patches-build-date=Wed Jun 16 14:12:08 UTC 2021\n\n"}
{"title": "Added an API key for Ikeja", "number": 1036, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1036", "body": ""}
{"title": "Added an API key for DT pilot", "number": 1037, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1037", "body": ""}
{"title": "Feature/sna device retention", "number": 1038, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1038", "body": "just_connected devices: don't use devices older than 30 days for matching\nflask all devices filter synced with db retention policty\nUpdate fingerbank key\nNo asizeof\n\n"}
{"title": "Feature/plume dogfood nadav", "number": 1039, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1039", "body": "vault changes\nget devices fix\nget_history\nadded addresses\nfixed structures\nfix func signiture\nfix func signiture\nfixes\n\n"}
{"comment": {"body": "Add the vault id", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1039/_/diff#comment-*********"}}
{"comment": {"body": "Update the ALTER method", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1039/_/diff#comment-*********"}}
{"comment": {"body": "If we\u2019re already editing this method, let\u2019s specify the types:\n\ntimestamp: float, address: str", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1039/_/diff#comment-*********"}}
{"comment": {"body": "to test `None`, you should use `if vault_id is None`. If `vault_id` is 0 or `\u201d\u201d` or `False`, `not vault_id` would return True", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1039/_/diff#comment-231108299"}}
{"comment": {"body": "I dont think we should do it.  \nbecause vault\\_id is like device\\_id, you cant add a row without vault\\_id", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1039/_/diff#comment-232000598"}}
{"comment": {"body": "Declining this PR for now since the comments were fixed and the branch is not relevant anymore", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1039/_/diff#comment-232297503"}}
{"title": "one line fix in tee_replay", "number": 104, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/104", "body": "Broke tee_replay in script form by forgetting to call init()"}
{"title": "Ikeja retention policy", "number": 1040, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1040", "body": "Not updating last seen of devices that we last saw before the retention policy. Made sure we use the same time constant in all places.\nNote that the retention policy affects us in three places:\n\nmanagement flask - should I present the device in the UI?\nmatching flow - should I look at the device as a candidate? (Added here)\ndata processor - should I update the last seen time of a device?\n\n"}
{"title": "Ikeja pilot logs", "number": 1041, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1041", "body": "Now well see only WARNING logs. Changed our important logs to that severity levl."}
{"title": "Feature/ikeja fingerbank bypass", "number": 1042, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1042", "body": "Add fingerbank bypass based on DHCP and DNS\nWith call metrics\n\n"}
{"title": "workaround IKEJA exceptions", "number": 1043, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1043", "body": ""}
{"title": "Genesis and QOL improvements", "number": 1044, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1044", "body": "Mostly exception error handling caught in  and \n\nMore healthcheck before starting the server (checking celery and redis)\nShow genesis channel limitation warning only once\nap agent: increase timeout of cfr rate checking\n\n\n\nQOL:\n\nAdd genesis exclusive mode\nFix one class classification step\n\n\n\n"}
{"comment": {"body": "Out of interest, why was that needed?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1044/_/diff#comment-*********"}}
{"comment": {"body": "Why is that?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1044/_/diff#comment-*********"}}
{"comment": {"body": "`ap_agent.py` here is listening to`streamer_agent.py` which was printing data about the CFR rate every ~1.2s. This `select` timed out in the 1st second and printed 0 CFR rate and then the next `select` didn\u2019t time out and printed the real CFR rate. rinse and repeat.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1044/_/diff#comment-231608894"}}
{"comment": {"body": "Takes time to gather 500 CFRs for 1 device. I see that it often takes 8-9 seconds to collect that much data.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1044/_/diff#comment-231608970"}}
{"title": "Master", "number": 1045, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1045", "body": ".gitattributes\nEngenius vanilla FW\nignore\nMove files around\nqsdk image\nMore restructring\nFix levl image compilation\nadd image extraction; vanilla works\nFix building image scripts\nleds are important, aren't they?\nAdd build info inside image\nQuotes\nUpdate build arguments to be distinct\nhe80 to ht80 under qsdk 11.4\nParse UCI based on qsdk ver\nHT80 fixes\nmake defconfig after config change\nadd rcc script\nfix all.sh\nFix CFR parsing with new QSDK\nadd cfo helper methods\nrecording system changes\nFix system\nFix backward compatibility\nadd DMAHeader_v5\neg1 and eg13 are ht80\nFix bdwlan.bin copying to wifi firmware Improve detection of qsdk version during setup.sh\nMinor fixes\nFix squashfs path\nWIFI0 -> WIFI1\nMore healthcheck before starting the server (checking celery and redis) More exception handling\nShow genesis channel limitation warning only once\nFix redis checking\nap agent: increase timeout of cfr rate checking\nAdd genesis exclusive mode Fix one class classification\nreference dataset class needs to be persistent during training scope\n\n"}
{"title": "Release/plume poc", "number": 1046, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1046", "body": "On plume we disable physical layer data\nplume credentials\nHandle corrupted jsons by logging and returning\nRe-disable static cfr for plume\n\nRevert \"History log in UTC timestamp\"\nThis reverts commit 7d22cc9e\n\n\nBrought back history file tz, updated to east coast\n\nDisable CFR extraction in the first place\nNetBIOS timediff to 20 minutes\nPrioritize identification of Ubuntu Linux over fingerbank\nAdd \"iRobot\" to list of device types supporting dhcpv6\nDon't use previous mac addresses as identifieres (either solid or weighted) when it's an Android device connected with a wired adapter\nOn initial manufacturer comparison in canonical_device_model_compare, determine no-match only if manufacturers are part of comparable group\nIgnore null ID\nTriggering pinger and arper for wired devices, not only wireless. Enabled flag for icmp_ts\nDisable MCLR as well for wired Android devices\nDisable ICMP TS until cache time limits for wired devices is sorted out\nChanges from PR-920 and PR-956 (guglielmo) that are needed here too\nAP to tell that it's hospitality agent\nHandle hospitality error in agent side\nautogen files\nKeyError\nKeep fake SSID for request_ip feature\nCancel last seen logic\nModifications for hybrid router-tap mode: Removed \"hospitality\" validation in SSID names, disabled pinging from AP, disabled TCP TS and ICMP TS\nDisable SNMP as well\nrollback \"hospitality\" ssid\nAndroid SSID filter should work for real SSIDs\nAdd DHCP Relay option to routers DHCP Relay config will disable DHCP server on demo DHCP Relay config will enable DHCP client on demo plume AP is bridged all.sh will setup the DHCP server IP of the DHCP relay\nAutogenerated files\nRevert switch_active changes since we're not active\nR1 has bridge configuration, for testing\nR1 is for cwd8\nDefault, not 0.0.0.0\nThe self.data_processor.dhcp_requested_ips wasn't containg the first DHCP request from the device\n\n"}
{"title": "Release/plume poc", "number": 1047, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1047", "body": "On plume we disable physical layer data\nplume credentials\nHandle corrupted jsons by logging and returning\nRe-disable static cfr for plume\n\nRevert \"History log in UTC timestamp\"\nThis reverts commit 7d22cc9e\n\n\nBrought back history file tz, updated to east coast\n\nDisable CFR extraction in the first place\nNetBIOS timediff to 20 minutes\nPrioritize identification of Ubuntu Linux over fingerbank\nAdd \"iRobot\" to list of device types supporting dhcpv6\nDon't use previous mac addresses as identifieres (either solid or weighted) when it's an Android device connected with a wired adapter\nOn initial manufacturer comparison in canonical_device_model_compare, determine no-match only if manufacturers are part of comparable group\nIgnore null ID\nTriggering pinger and arper for wired devices, not only wireless. Enabled flag for icmp_ts\nDisable MCLR as well for wired Android devices\nDisable ICMP TS until cache time limits for wired devices is sorted out\nChanges from PR-920 and PR-956 (guglielmo) that are needed here too\nAP to tell that it's hospitality agent\nHandle hospitality error in agent side\nautogen files\nKeyError\nKeep fake SSID for request_ip feature\nCancel last seen logic\nModifications for hybrid router-tap mode: Removed \"hospitality\" validation in SSID names, disabled pinging from AP, disabled TCP TS and ICMP TS\nDisable SNMP as well\nrollback \"hospitality\" ssid\nAndroid SSID filter should work for real SSIDs\nAdd DHCP Relay option to routers DHCP Relay config will disable DHCP server on demo DHCP Relay config will enable DHCP client on demo plume AP is bridged all.sh will setup the DHCP server IP of the DHCP relay\nAutogenerated files\nRevert switch_active changes since we're not active\nR1 has bridge configuration, for testing\nR1 is for cwd8\nDefault, not 0.0.0.0\nThe self.data_processor.dhcp_requested_ips wasn't containg the first DHCP request from the device\n\n"}
{"comment": {"body": "remove", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1047/_/diff#comment-231922343"}}
{"comment": {"body": "We should rethink that - it\u2019s best that the logs are in UTC timezone", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1047/_/diff#comment-231922659"}}
{"comment": {"body": "i\u2019m now seeing that it\u2019s a wrong condition. `is_real_ssid is not None` should be just `is_real_ssid`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1047/_/diff#comment-231923123"}}
{"title": "Update phase comparison threshold as a response to ", "number": 1048, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1048", "body": "Adding API key for Guglielmo\nUpdate the last seen when we update the last connected time, just in case\nGuglielmo TZ set for Italy Bologna\nFixed Bologna -> Rome, it didn't exist in the pytz available options\nNow ignoring icmpv6 ns packets that caused FP in Guglielmo, they can be sent by anyone and to anyone and can't be a parameter in our decision\nUpdated Guglielmo ns extraction to match the one in charter\nFixed _is_windows_10() method to first check the device type manufacturer before checking the type model\nWe wrongly looked at the src port all this time. We didn't notice so far because in many cases (for some reason) sport=dport=137\nconflict fix\nSome fixes for guglielmo - removed duid from mandatory condition. disabled tcp timestamp as a solid identifier. fixed requested_ip feature that was not really used. added the changes needed for management_flask to show the history log in italy time\nFinalize will reset the state_mgt_count, for the scenario where a device has continous detection\nAdded a user name and password for ikeja\nUpdated the UI timezone to Johannesburg\nUpdated docker-compose\nUpdated rnd.yml and server_list\nTrying to improve performance\nAp update fix\nAdded an API key for Ikeja\nAdded a retention policy of 1 day\nImport error\nTrying to improve performance - disabling monitoring management event\nNot updating last seen of devices that we last saw before the retention policy. Made sure we use the same time constant in all places\nRetention policy from 1 day to 3 days\nNow we'll see only WARN logs\nChanged important logs to warning so we'll see them\nAdd bypass based on DHCP and DNS\nIncreased the UI sending and refreshing timeouts\nfixed called mac\ncleanup\nNot calling get_frontend_signal_strength() if running in tap configuration\nDisabled verbose logs\nworkaround exceptions\nRetry under OrderedDict mutated during iteration exception\nNow parsing empty duid\nNow handling classification failure devices loading\nikeja performance improvements and parsing exceptions\ncatch key error exception on state periodic management and stat in tick stack\ncatch key error exception on state periodic management and stat in tick stack\nRemove traces\nadjust debug levels\nIdentifier mathcing debug log\nFixed exception in compare function\nprint exception details\nFix match function of windowds DUID\nFixed identification of android devices which might cause apple device to identified as android\nAdd bypass for Huawei androids\nDo not query device history in get all devices\nikeja perf imporvements\n\n"}
{"title": "Release/guglielmo poc", "number": 1049, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1049", "body": "Adding API key for Guglielmo\nUpdate the last seen when we update the last connected time, just in case\nGuglielmo TZ set for Italy Bologna\nFixed Bologna -> Rome, it didn't exist in the pytz available options\nNow ignoring icmpv6 ns packets that caused FP in Guglielmo, they can be sent by anyone and to anyone and can't be a parameter in our decision\nUpdated Guglielmo ns extraction to match the one in charter\nFixed _is_windows_10() method to first check the device type manufacturer before checking the type model\nWe wrongly looked at the src port all this time. We didn't notice so far because in many cases (for some reason) sport=dport=137\nconflict fix\nSome fixes for guglielmo - removed duid from mandatory condition. disabled tcp timestamp as a solid identifier. fixed requested_ip feature that was not really used. added the changes needed for management_flask to show the history log in italy time\nFinalize will reset the state_mgt_count, for the scenario where a device has continous detection\n\n"}
{"title": "Feature/device type preconfiguration", "number": 105, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/105", "body": "Laying some foundation for getting the phone model during training and record it\nUsing this recording, the models will be manipulated to known behavior of given phone models\nKnown phones are currently in a CSV file. This is a short term solution and will move a more large scale solution\nA followup PR will come with appropriate modifications to libfingerprinting (cant avoid that)\n\nLet me know if you dont like something/want me to move pieces of code\n\n"}
{"comment": {"body": "Could you please elaborate some more on what\u2019s going on here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/105/_/diff#comment-148623837"}}
{"comment": {"body": "Sure.\n\nThe android app now sends its manufacturer and model in addition to its ID to the dialog board. Then the board sends to comcastpi this extended string, which in turn send it to the server.  \nThis is done to concatenate the manufacturer and the model to the user in the DB. Then, given the known behavior of the models \\(which is loaded from the CSV table\\), we can update the training/classification parts of the BLE with the known behaviors of those models.\n\nThe current behavior to be modified is to pre-load whether the device is 0 phone slope \\(in case of CFO\\) and/or has 1 lobe when it\u2019s WIFIing \\(while BLEing\\).\n\nThis is eventually to reduce false positives since we don\u2019t know how the device behaves so we have a large acceptance range for CFO. This method gives us the option to decrease the acceptance range for CFO.\n\nIf you have more more question, please ask", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/105/_/diff#comment-148625842"}}
{"title": "Plume master merges", "number": 1050, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1050", "body": "On plume we disable physical layer data\nplume credentials\nHandle corrupted jsons by logging and returning\nRe-disable static cfr for plume\n\nRevert \"History log in UTC timestamp\"\nThis reverts commit 7d22cc9e\n\n\nBrought back history file tz, updated to east coast\n\nDisable CFR extraction in the first place\nNetBIOS timediff to 20 minutes\nPrioritize identification of Ubuntu Linux over fingerbank\nAdd \"iRobot\" to list of device types supporting dhcpv6\nDon't use previous mac addresses as identifieres (either solid or weighted) when it's an Android device connected with a wired adapter\nOn initial manufacturer comparison in canonical_device_model_compare, determine no-match only if manufacturers are part of comparable group\nIgnore null ID\nTriggering pinger and arper for wired devices, not only wireless. Enabled flag for icmp_ts\nDisable MCLR as well for wired Android devices\nDisable ICMP TS until cache time limits for wired devices is sorted out\nChanges from PR-920 and PR-956 (guglielmo) that are needed here too\nAP to tell that it's hospitality agent\nHandle hospitality error in agent side\nautogen files\nKeyError\nKeep fake SSID for request_ip feature\nCancel last seen logic\nModifications for hybrid router-tap mode: Removed \"hospitality\" validation in SSID names, disabled pinging from AP, disabled TCP TS and ICMP TS\nDisable SNMP as well\nrollback \"hospitality\" ssid\nAndroid SSID filter should work for real SSIDs\nAdd DHCP Relay option to routers DHCP Relay config will disable DHCP server on demo DHCP Relay config will enable DHCP client on demo plume AP is bridged all.sh will setup the DHCP server IP of the DHCP relay\nAutogenerated files\nRevert switch_active changes since we're not active\nR1 has bridge configuration, for testing\nR1 is for cwd8\nDefault, not 0.0.0.0\nThe self.data_processor.dhcp_requested_ips wasn't containg the first DHCP request from the device\n\n"}
{"comment": {"body": "Not relevant any more to master for r1", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1050/_/diff#comment-231948712"}}
{"title": "Master -> Plume dogfood switch mode", "number": 1051, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1051", "body": ""}
{"title": "pr changes", "number": 1052, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1052", "body": ""}
{"title": "Guglielmo -> Master", "number": 1053, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1053", "body": "Took the changes from PR-1049 that are relevant to master, and not specific changes for Guglielmo."}
{"title": "Feature/plume dogfood ui", "number": 1054, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1054", "body": ""}
{"title": "Feature/cfr payload same indicator", "number": 1055, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1055", "body": "With the new CFR capture method, RCC, were able to capture 802.11AX 80MHZ STS 2 packets, but after some time it hung in the sense that a corrupt CFR packet had been delivered (with valid CFR packet headers).\n  The reason for this is yet to be determined and the mitigation is power cycle or running wifi on the router.\n\nThis PR adds a simple textual indicator to tell whether the CFR stream is valid (in the previous line sense) or corrupt:\n\nTrue means corrupt. False means valid\n\n\n"}
{"comment": {"body": "call `reset()`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1055/_/diff#comment-233409223"}}
{"comment": {"body": "This is the way to find out whether we got corrupted packets?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1055/_/diff#comment-233412978"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1055/_/diff#comment-233542855"}}
{"comment": {"body": "called", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1055/_/diff#comment-233554756"}}
{"title": "autogen", "number": 1056, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1056", "body": ""}
{"title": "when in rcc, don't bf", "number": 1057, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1057", "body": "BF appears to be the reason for RCC issues.\nMaybe this was noted here:\n\n"}
{"title": "Revert \"when in rcc, don't bf (pull request #1057)\"", "number": 1058, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1058", "body": ""}
{"title": "Feature/replace redis with minio storage for cfr", "number": 1059, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1059", "body": "Fixes to last merge stuff and other changes that enable CFR capturing (as it was widely disabled)\n\nCaptured CFRs on genesis service would be stored permanently on disk (instead of redis) since redis stores everything on RAM. This would make the system more stable. (cfr_db.py module)\n\nIt basically implements a time series DB on minio\nAlso compress the CFRs for the sake of storage. Its a small CPU penalty\n\n\n\n"}
{"title": "No changes test PR, please ignore", "number": 106, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/106", "body": ""}
{"title": "Feature/merge and genesis fixes", "number": 1060, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1060", "body": "Exception fixes CFR not working fix\nFix CFR prioritizing Remove socket timeout\nUse compression on CfrEvent\nMore time to gather CFRs for genesis exclusive\nFix decompress\nFix bool and NoneType\n\n"}
{"title": "Recording system/extend indicator to ac", "number": 1061, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1061", "body": "Indicator would test either preamble_type 2 (ac) or preamble_type 3 (ax)\ncomments\n\n"}
{"title": "Genesis/ignore matching for connected devices", "number": 1062, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1062", "body": "System would pass to genesis a list of levl ids to ignore along with the classification request\nIf there's no MC but there are multiple OCC, use the OCC created the earliest in that group\nFix pandas 1.3.0 issue with pickle loading\nFix mlflow memory leak with autolog() functions\n\n"}
{"title": "Genesis/replace minio service", "number": 1063, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1063", "body": "update gitignore\n\nReplace minio server with cloudserver due to licensing issues\n\n\nChanges to the default config file are:\n\nLogging level at warning\ncloudserver added to restEndpoints\n\n\n\n\n\nUpdate tls generation with support for aws dns (and add pdev0 and pdev1)\n\n\n"}
{"title": "Remove unused sections in cloudserver config", "number": 1064, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1064", "body": "Forgot these changes in previous PR"}
{"title": "Genesis/1st logging effort", "number": 1065, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1065", "body": "Add logging of requirements from  of genesis decision making\nThe logs are stored in session data column under debug_log field\n\nRelevant query is like this:\nselect device_id, decision_timestamp, decision_duration, cast(session_data as json)-'debug_log' from decision_log order by decision_timestamp desc\n\n\nAdd traces for genesis training/classification\n\nTraces are dumped to ccpilot/recordings/%date%/genesis_traces\nLoad traces help functions in genesis_service/debug_tools/traces/load_trace.py\n\n\n\n"}
{"title": "Genesis/dump traces", "number": 1066, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1066", "body": "module to dump genesis traces\nIntegrate into training and classification\n\n"}
{"title": "Genesis/stop cfr collection after genesis done", "number": 1067, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1067", "body": "Add delegate to query readiness of data collection for some levl id\nmonitor genesis data collection during classification_success state\n\n"}
{"title": "Recsys: scapy 2.4.5 is broken so set version 2.4.4", "number": 1068, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1068", "body": "This exception started:\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.8/multiprocessing/process.py\", line 315, in _bootstrap\n    self.run()\n  File \"/usr/local/lib/python3.8/multiprocessing/process.py\", line 108, in run\n    self._target(*self._args, **self._kwargs)\n  File \"/root/src/recording_system/cfr_server/parsers.py\", line 160, in _run\n    pkt, metadata = reader.read_packet()\n  File \"/usr/local/lib/python3.8/site-packages/scapy/utils.py\", line 1264, in read_packet\n    Packet,\nNameError: name 'Packet' is not defined\nwhich didnt happen in old recsys.\nTheres a known issue in scapy version 2.4.5: \nFix is pending, so Ill just lower version to 2.4.4 where it doesnt happen."}
{"title": "Monitoring migrate", "number": 1069, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1069", "body": "Collect stuff from old monitoring server so that we can easily migrate it\nUse docker with docker-compose instead of natively installing system services"}
{"comment": {"body": "missing local telegraf", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1069/_/diff#comment-241123909"}}
{"comment": {"body": "this is not the correct conf file", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1069/_/diff#comment-241125627"}}
{"title": "App transmits phone id and rpi reconnects", "number": 107, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/107", "body": "Cant stop app from transmitting. The same problem was also present on the bosch app.\nRestored TID in essence by transmitting the phone ID in advs"}
{"comment": {"body": "The adv packets are now 4 bytes longer. How does it affect the dialog and IQ throughput?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/107/_/diff#comment-148636521"}}
{"comment": {"body": "Haven\u2019t noticed anything wrong with the training, nor the classification.  \nIf we discover that that is a problem we can move temperature data to the connection.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/107/_/diff#comment-148640940"}}
{"comment": {"body": "Got it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/107/_/diff#comment-148641224"}}
{"title": "New servers", "number": 1070, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1070", "body": ""}
{"title": "Genesis/fix ccp 745", "number": 1071, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1071", "body": "Check if levl ID exists before deletion in genesis (Fixes  )\nDon't try to train if there are 0 devices\n\n"}
{"title": "Fixed the decision time to take the actual current time to calculate the difference", "number": 1072, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1072", "body": "When we migrated the replay component, we changed our system to use internal_time values as much as possible to not be dependent on the actual now time. That was done to allow us to replay the events without being dependent on real times.\nIn that specific calculation, we do want to use the real now time, because we subtract it from the time from the packet. So the time we calculate with the internal time is on a total different timeline from it. In addition, this wont harm our replay ability so we can bring back this calculation to what it was before."}
{"title": "snapshot monitoring from Azure", "number": 1073, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1073", "body": ""}
{"comment": {"body": "Duplicate of [https://bitbucket.org/levl/comcast/pull-requests/1069](https://bitbucket.org/levl/comcast/pull-requests/1069){: data-inline-card='' } ?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1073/_/diff#comment-241122619"}}
{"comment": {"body": "No. This is preliminary snapshot of monitoring before docker and etc..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1073/_/diff#comment-241123814"}}
{"title": "CFO feature", "number": 1074, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1074", "body": "After the last few weeks, we can review and merge this feature to our master branch.\nSome general notes:\n\nThe feature can be turned on / off by using the USE_CFO_FEATURE in the docker-compose. Its set default to off.\nThe feature requires 100 CFR packets to calculate everything needed.\nWe have an ongoing learning of this feature. Every 5 minutes, if we got enough CFR packets, well re-build it. This time gap, of course, can be changed and modified as we wish. In addition, when a device is disconnected from the system, were taking the last CFR packets we got from it and re-build the feature to have the most up to date data.\nThe system was checked with the feature being the only one working, and with the feature working with the entire system and other feature. The results were quite good, and matched the devices matrix that can be found here.\nEven though we got good results, its still a statistic feature, with many FP results. Therefore, its set default to not work, until we decide otherwise.\nThe feature was tested with temperature changes and time differences. The results were good.\n\n"}
{"comment": {"body": "This is not critical, but using dataframes here seems like an overkill to me. Those objects are very heavy to create and you are not actually using any special functionality that the dataframe have.\n\nI would alternatively go maybe with something like a dataclass to represent the model parameters. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1074/_/diff#comment-241196161"}}
{"comment": {"body": "Nice catch!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1074/_/diff#comment-241196234"}}
{"title": "Not calling genesis adapter if it's not initialized", "number": 1075, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1075", "body": ""}
{"title": "Research framework improvement", "number": 1076, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1076", "body": "Some improvement Ive added to NN code:\n\nmulticlass handle different train and test DBs\nparallel trials\n\n\n\nAdd jupyter server option (just run workspace/phy_snr/jupyter/run.sh and launch the link printed)\n\n\n"}
{"title": "Feature/dt pilot notifications", "number": 1077, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1077", "body": "Added notifications sending when a device is onboarding, connected again and disconnected. The notifications are sent to an hard-coded url, sent by DT.\nThis branch is above dt_pilot_2 tag, so after this is merged we can release another tag and upgrade the server."}
{"title": "Feature/add cfo investigation fields", "number": 1078, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1078", "body": "Added CFO value, router uptime, router version columns\nAdded oven temp field\nAdded router_temp_wifi0, router_temp_wifi1 columns (to be updated every 5 sec)\n\n"}
{"title": "changed channels for routers 1, 2, 7, 13", "number": 1079, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1079", "body": "@{5dbeb866c424110de52552cc}"}
{"title": "Bugfix/expected train classify duration", "number": 108, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/108", "body": "Quick fix to handle duration uncertainty on server + reliance of feature on capture duration"}
{"title": "Recsys web UI enhancements", "number": 1080, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1080", "body": "Add wifi0/1 temp updates + router uptime and ver to web ui\nFix oven_set_temp default value errors.\n\nThe upper central card will look like this:\n\n"}
{"title": "Add env var to determine minimum periodicity", "number": 1081, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1081", "body": "To decrease CFR rate to 10 pkts/sec, run the rec server from a context where MINIMUM_PERIODICITY is set to 100 as such:\nexport MINIMUM_PERIODICITY=100\n/run_server.sh\n"}
{"title": "CFR companion app - fit to cofig", "number": 1082, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1082", "body": "Small changes to make current app work with our APs and recsys being accessible over VPN"}
{"title": "Research/cfo temperature analysis", "number": 1083, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1083", "body": "Initial framework to get statistics about recordings\n\nModify and run phy_neural_network/main_daily_recording_test.py\n\n\n\nAdded regressor for gaussian mixture of linear models with EM solver\n\n\n"}
{"title": "Update newly added recsys column types", "number": 1084, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1084", "body": "Recsys: update wifi0/1 temp types, router uptime -> boot time\nAdded dry run function to assess bigquery query size\n\nrouter_uptime, router_temp_wifi0 and router_temp_wifi1 will be deprecated and replaced by router_boot_time, router_thermal_zone_wifi0, router_thermal_zone_wifi1 accordingly."}
{"title": "Research/cfo temperature analysis", "number": 1085, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1085", "body": "Refactor daily reports code\n\nPlot RSSI range, temperature range, CFO range\n\n\n\nFix mixture regressor issues and add prior to 2nd cluster\n\nNotebook for CFO phone temperature analysis\n\n"}
{"title": "Recsys - Don't Crash on Connectivity Errors", "number": 1086, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1086", "body": "added try... except to connection callbacks.\n"}
{"title": "Research/cfo router temperature model", "number": 1087, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1087", "body": "Refactor LinearMixtureRegressor module\n\nAdd conjugate prior to mixture, L2 regularization\nAdd MSE based stopping criterion\nVectorize operations (much faster now)\nSince M step is linear regression, its a composite of LinearRegressor\nAdd special case for 0 CFO in LinearMixture0SlopeRegressor\n\n\n\nCFO model in cfo_model.py\n\n\nV2 and V2.1\n\nV2 with EM clustering in train/classify CFO_OneClassClassifier_v2\nV2.1 with LR in training CFO_OneClassClassifier_v2_1, clustering outside model CFO_Filter_v2_1\n\n\n\nhas serialization/deserialization methods\n\n\n\n\nCFO evaluation in main_cfo_model.py\n\n\n"}
{"comment": {"body": "Move code to different folder call it:  \nCFO\\_MODEL", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1087/_/diff#comment-249456666"}}
{"title": "Recsys - Handle short term connectivity loss", "number": 1088, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1088", "body": "Handle case of network reset on router, where pings are answered again quickly but SSH doesn't. Then router is considered up but the subprocess call is failed and recsys server crashes."}
{"title": "Research/cfo only lr", "number": 1089, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1089", "body": "Mixture owns LR\nFix copying and array shapes\nFix late initialize of phi\nVectorized everything\nMore stable results Fix mixture API\nFix source of singularity\nSome refactor to linear regression Add CFO model 2.1 with filter outside and simple LR model\nFixes\n\n"}
{"title": "Detect death of cfr streamer and relaunch", "number": 109, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/109", "body": ""}
{"title": "Disable Genenis by default", "number": 1090, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1090", "body": ""}
{"title": "CFO approximation POC", "number": 1091, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1091", "body": ""}
{"title": "Fix update prints of dhcp_transaction_id and nb_transaction_id", "number": 1092, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1092", "body": ""}
{"title": "Nitzan/phy nn gal", "number": 1093, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1093", "body": "added cfo estimator folder\n\ntensor_logs\nkeras model in .h5 format\ntrain script\nestimator class\n\n\n\nadded multiple TestRecordingDB_*.py files for different recording scenarios/configurations\n\n\n"}
{"comment": {"body": "This file appears as non draft in cfo\\_estimator\\_from\\_csi. can we remove this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1093/_/diff#comment-249540577"}}
{"comment": {"body": "`recording_id is a dictionary`", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1093/_/diff#comment-249541950"}}
{"comment": {"body": "respond to some small comments but looks ok", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1093/_/diff#comment-249542178"}}
{"comment": {"body": "This is the one I\u2019m using for training new networks. It includes visuals and more. Unlike the non-draft version which is strictly for reproducing the model.  ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1093/_/diff#comment-250092919"}}
{"title": "moved RecordingDB and datasets", "number": 1094, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1094", "body": "moved RecordingDB and datasets\n\n"}
{"title": "Research/cfo model review", "number": 1095, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1095", "body": "Refactor serialization of CFO models (@{5dbeb866c424110de52552cc}  requested)\nFix datasets moving\n\n"}
{"title": "Merge research master with master", "number": 1096, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096", "body": "The work for cfo model is in cfo_model\nThe work for CFO estimator is in cfo_estimator_from_csi\n\n"}
{"comment": {"body": "@{5b41d9de10d57114135eca66}  update PR after you merge PR #1097", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-251415231"}}
{"comment": {"body": "git lfs if needed at all", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254139239"}}
{"comment": {"body": "git lfs the model", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254139343"}}
{"comment": {"body": "Should this be here? move to workspace if not\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254139664"}}
{"comment": {"body": "And please move this dir to workspace as well. Thanks!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254140036"}}
{"comment": {"body": "Others depend on this project, so I won\u2019t move it at this time.\n\nI\u2019ll write it down for next PR", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254140514"}}
{"comment": {"body": "it\u2019s fine here", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254140573"}}
{"comment": {"body": "LFSed", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254142847"}}
{"comment": {"body": "LFSed - it\u2019s needed for debugging", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254142876"}}
{"comment": {"body": "And shove this one in workspace too please!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254143026"}}
{"comment": {"body": "The dir, not just the file", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254143081"}}
{"comment": {"body": "it\u2019s a reusable module.. it doesn\u2019t belong in workspace", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254143235"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} It\u2019s used outside of workspace?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254143429"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} yes", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1096/_/diff#comment-254143528"}}
{"title": "Research/cfo est and model", "number": 1097, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1097", "body": "Add main for evaluating CFO model with and without CFO estimator\nMild refactor to estimator to simplify pipeline\n\n"}
{"comment": {"body": "Please add short description\\(sentence or two \\) about the model tested  and reference to research page.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1097/_/diff#comment-251414939"}}
{"title": "Device typing system V1", "number": 1098, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098", "body": "Contains also some changes from the master_research branch on the workspace and phy_nueral_network directories. Please ignore those"}
{"comment": {"body": "git lfs for pickle and pcaps?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098/_/diff#comment-*********"}}
{"comment": {"body": "We\u2019ll fix that later. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098/_/diff#comment-*********"}}
{"comment": {"body": "Need to keep this?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098/_/diff#comment-*********"}}
{"comment": {"body": "Not really. But it would probably be nice to have also a main to call fingerbank", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098/_/diff#comment-*********"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} It really doesn\u2019t make any difference once files pushed to origin. The branch must be deleted and a fresh branch with git lfs\u2019d files should be created and merged, otherwise the files will remain in git history forever. So if you don\u2019t plan on doing that \\(new branch\\), don\u2019t bother.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098/_/diff#comment-*********"}}
{"comment": {"body": "Is the device\\_typing matching supposed to support only L2?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098/_/diff#comment-*********"}}
{"comment": {"body": "For now it\u2019s just L2 but it will be extended in the future with L1 feature and upper layers", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098/_/diff#comment-*********"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Migrated them to LFS", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098/_/diff#comment-252550416"}}
{"comment": {"body": "Let\u2019s discuss your vision for that tomorrow.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1098/_/diff#comment-252551550"}}
{"title": "Feture/mdns device info typing", "number": 1099, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1099", "body": "adding apple_device_repository and mdns parsing of the device-info\nadding apple_device_repository and mdns parsing of the device-info\n\n"}
{"comment": {"body": "16 is just the type of TXT fields in DNS/mDNS\n\nWe need to also to verify the service name matches the pattern \\*.\\_device\\_info.\\*", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1099/_/diff#comment-252622953"}}
{"comment": {"body": "remove comments :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1099/_/diff#comment-252687141"}}
{"comment": {"body": "Can you download the html content instead of having it hardcoded? This py file is 150KB :flushed: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1099/_/diff#comment-252688659"}}
{"comment": {"body": "It\u2019s more complex html and will require more adaptations to generate it fully automatically..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1099/_/diff#comment-252709344"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1099/_/diff#comment-252709420"}}
{"comment": {"body": "Will remove after the integration with device typing", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1099/_/diff#comment-252709467"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Ok, at least move it to lfs to avoid making git work hard on revision management.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1099/_/diff#comment-252709752"}}
{"title": "Feature/recording server client", "number": 11, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/11", "body": "Merged in revert-pr-8 (pull request #10)\nRevert \"Feature/recording server client (pull request #8)\"\nApproved-by: <NAME_EMAIL> (cherry picked from commit 44053a7c5df0277135e07b2963556eccc403da6a)\n\n\nRevert \"Merged in revert-pr-8 (pull request #10)\"\nThis reverts commit 40fdec5e9d15e1c41d9288fbe40179c7ca32f61a.\n\n\n"}
{"title": "Decide default value of COOPERATION_DISABLE var in compose file, not Python", "number": 110, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/110", "body": "This reverts commit d8ec2dd2d9370e8c58eef22874a5dc9edb5ab380 \nDocker-compose will set a variable to empty rather than non-existent, so relying on .get is not perfect in this situation"}
{"comment": {"body": "Beautiful code!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/110/_/diff#comment-148673910"}}
{"title": "Fing HW Mac Type Classification", "number": 1100, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1100", "body": "Add Fing type classifier with with input from transient_id, bonjour_uids and dhcpv6_duid"}
{"comment": {"body": "let\u2019s go without agent modifications.. we can set hardcoded or on ap connection render uuid..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1100/_/diff#comment-252708811"}}
{"comment": {"body": "It\u2019s already there, and if it is empty we render one in server. I will add render on connection creation.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1100/_/diff#comment-252709185"}}
{"comment": {"body": "That is, we have full compatability with unpatched AP.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1100/_/diff#comment-252709309"}}
{"title": "add amazon and google logos", "number": 1101, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1101", "body": ""}
{"title": "add tarcker and fix dns parser", "number": 1102, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1102", "body": ""}
{"title": "Fing result to be saved separately", "number": 1103, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1103", "body": "Save fing response to separate field device_hw_mac_type_info"}
{"title": "Release/amazon poc", "number": 1104, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1104", "body": "add support for device type identification and the binary-class approach for one-class classification\nadded necessary files\nadd necessary files to parse new RCC mode\nupdate\nupdate the CFR packet class to compl with the V5 header version\nupdate files\nfix binary class plotting\nupdate\nupdate\nAdd a Legacy/RCC data mode on the parameters page\nadd new multiclass main to allow training and testing from different data sets\nupdate\nmulticlass handle different train and test DBs\nlogging and DB fixes\nAdd jupyter server\nenv changes\nparallel trials\nchanged paths\nCleanup\nwork and makedirs fix\nFix bugs\nAdd accuracy to plot title\nwork\nAdd linear regression mixture solver with EM\nDaily recording test\ninitial statistics about recordings\nForgot file\nUpdate docker-compose of jupyter\nCleanup\nPlot RSSI range, temperature range, CFO range\nFix mixture regressor issues\nFix plots and reports\nQuerying and plotting improvements\nreports into functions\nFix and improve mixture model Notebook for CFO phone temperature analysis\nUpdated notebook\nRefactor RegressionMixture module\nScale prior\nCFO evaluation\nAdd conjugate prior to mixture Add MSE based stopping criterion\nFix bug in LRM Add code for better CFO model\nFix stop criterion in EM Update cfo model testing\nReplace EM with KMeans in classification\nUpdate dataset for CFO model\nCFO data not all training data; Router prior\nMove CFO model to separate file\nCleanup, comments\nMixture owns LR\nFix copying and array shapes\nFix late initialize of phi\nVectorized everything\nMore stable results Fix mixture API\nFix source of singularity\nSome refactor to linear regression Add CFO model 2.1 with filter outside and simple LR model\nFixes\nAPI call fixes\nupdates\nFix PR comments\nadded cfo estimator folder\nuse .h5 instead of SavedModel format\nzip logs\nfixed add_recording\ncheckin\nupdate\nupdate\nnew funtions and processing method\nupdate\nupdates\nAll changes\nupdate\nall data\ndelete files\nremove\ninitial labels\ndata\nremomve file\nrename\nfeature_extractor.py\nchanges\nremove import\nFix labels\nstore oui data as string\nuse string features\nstore result in pickle format\nLookup from the generated db\nrename file\nrename file\nfix import\npath for typing models\nRemoved unused imports\nAdd manufacturer and OS labels\nAdd vendor and OS to model\nUppercase typo\nsave model also in CSV human readable\nmatch result to systemm format\niPhone without model is not useful\niPhone SE - 2nd gen\niPhone 8 plus\nSamsung galaxy name convention\nadded plot dendrogram\nUpdate UI timezone and user/password\nadded apple protocol support for rule based logic\nfix\nadded device type release date to rule based tables\noperating mode parsing\nmodels\nrevert docker compose changes\nprotect against exceptions\nrevert backup of db data\nreove pcap files from repository\nfiles to lfs\nutils\nadding apple_device_repository and mdns parsing of the device-info\nadding apple_device_repository and mdns parsing of the device-info\nunique table of apple modesl\nlookup apple csv repository\nfix parsing and added logging\nstrict matching\nadd to db the mdns device info\nnew model + labels\nlabelling\nUpdate model\nMutli hits support\nAdd Fing type classifier with with input from transient_id, bonjour_uids and dhcpv6_duid\nfix db update for mdns device info\nnew data\nwtf? remove filter caps\nremove duplicate apple table\nget already existing router unique id\nadded script print_model_conflicts.py\nfixed sasmung in lab_labels.csv\nadd debug\nfix offset issue generated mac\nadd amazon and google logos\nremove device type column\nadd tarcker and fix dns parser\nremove prijnts\nremove file not required\nmDNS model name\nadded phone to labels/lab_labels.csv\nadded macbook to lab labels\nsave fing response to separate field device_hw_mac_type_info\nAmazon device for all mazon devices\nfing integration\nCorrect oreder with user agent\nfixed bad parsing\nAdd more models\nparse model info also for macbooks\nActually update ongoing bonjour and device info\nfixed bonjour for iOS 15 for the 10000th time\nremove redundent tags\nenhanced again bonjour uid extraction\nsave also rank to db\nfurther fix managment flask\nSometimes model returns blank from fingerbank\ncorrect order in managment flask\nlatest model\n\n"}
{"title": "CFO model v2.x.1 + v3", "number": 1105, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1105", "body": "Add models 2.x.1 with channel shaping\nAdd temperature/channel analysis and confusion matrix for 2.x.1\nFix bugs in LRM\nAdd model v3, extending model v2.1.1, with online training and variable shaping (which variables are accounted for)\n\n"}
{"comment": {"body": "add documentation explaining:", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1105/_/diff#comment-*********"}}
{"title": "fix device_type_model None", "number": 1106, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1106", "body": "add support for device type identification and the binary-class approach for one-class classification\nadded necessary files\nadd necessary files to parse new RCC mode\nupdate\nupdate the CFR packet class to compl with the V5 header version\nupdate files\nfix binary class plotting\nupdate\nupdate\nAdd a Legacy/RCC data mode on the parameters page\nadd new multiclass main to allow training and testing from different data sets\nupdate\nmulticlass handle different train and test DBs\nlogging and DB fixes\nAdd jupyter server\nenv changes\nparallel trials\nchanged paths\nCleanup\nwork and makedirs fix\nFix bugs\nAdd accuracy to plot title\nwork\nAdd linear regression mixture solver with EM\nDaily recording test\ninitial statistics about recordings\nForgot file\nUpdate docker-compose of jupyter\nCleanup\nPlot RSSI range, temperature range, CFO range\nFix mixture regressor issues\nFix plots and reports\nQuerying and plotting improvements\nreports into functions\nFix and improve mixture model Notebook for CFO phone temperature analysis\nUpdated notebook\nRefactor RegressionMixture module\nScale prior\nCFO evaluation\nAdd conjugate prior to mixture Add MSE based stopping criterion\nFix bug in LRM Add code for better CFO model\nFix stop criterion in EM Update cfo model testing\nReplace EM with KMeans in classification\nUpdate dataset for CFO model\nCFO data not all training data; Router prior\nMove CFO model to separate file\nCleanup, comments\nMixture owns LR\nFix copying and array shapes\nFix late initialize of phi\nVectorized everything\nMore stable results Fix mixture API\nFix source of singularity\nSome refactor to linear regression Add CFO model 2.1 with filter outside and simple LR model\nFixes\nAPI call fixes\nupdates\nFix PR comments\nadded cfo estimator folder\nuse .h5 instead of SavedModel format\nzip logs\nfixed add_recording\ncheckin\nupdate\nupdate\nnew funtions and processing method\nupdate\nupdates\nAll changes\nupdate\nall data\ndelete files\nremove\ninitial labels\ndata\nremomve file\nrename\nfeature_extractor.py\nchanges\nremove import\nFix labels\nstore oui data as string\nuse string features\nstore result in pickle format\nLookup from the generated db\nrename file\nrename file\nfix import\npath for typing models\nRemoved unused imports\nAdd manufacturer and OS labels\nAdd vendor and OS to model\nUppercase typo\nsave model also in CSV human readable\nmatch result to systemm format\niPhone without model is not useful\niPhone SE - 2nd gen\niPhone 8 plus\nSamsung galaxy name convention\nadded plot dendrogram\nUpdate UI timezone and user/password\nadded apple protocol support for rule based logic\nfix\nadded device type release date to rule based tables\noperating mode parsing\nmodels\nrevert docker compose changes\nprotect against exceptions\nrevert backup of db data\nreove pcap files from repository\nfiles to lfs\nutils\nadding apple_device_repository and mdns parsing of the device-info\nadding apple_device_repository and mdns parsing of the device-info\nunique table of apple modesl\nlookup apple csv repository\nfix parsing and added logging\nstrict matching\nadd to db the mdns device info\nnew model + labels\nlabelling\nUpdate model\nMutli hits support\nAdd Fing type classifier with with input from transient_id, bonjour_uids and dhcpv6_duid\nfix db update for mdns device info\nnew data\nwtf? remove filter caps\nremove duplicate apple table\nget already existing router unique id\nadded script print_model_conflicts.py\nfixed sasmung in lab_labels.csv\nadd debug\nfix offset issue generated mac\nadd amazon and google logos\nremove device type column\nadd tarcker and fix dns parser\nremove prijnts\nremove file not required\nmDNS model name\nadded phone to labels/lab_labels.csv\nadded macbook to lab labels\nsave fing response to separate field device_hw_mac_type_info\nAmazon device for all mazon devices\nfing integration\nCorrect oreder with user agent\nfixed bad parsing\nAdd more models\nparse model info also for macbooks\nActually update ongoing bonjour and device info\nfixed bonjour for iOS 15 for the 10000th time\nremove redundent tags\nenhanced again bonjour uid extraction\nsave also rank to db\nfurther fix managment flask\nSometimes model returns blank from fingerbank\ncorrect order in managment flask\nlatest model\nfix device_type_model None\n\n"}
{"title": "Model name in history and API", "number": 1107, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1107", "body": "Correct model name in history file\nRemove model type from api\n\n"}
{"title": "Android non-random hostname model lookup", "number": 1108, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1108", "body": "Add model lookup by non-random name for android devices\nUse inferred model first if Amazon device\n\n"}
{"title": "Feautre/ios version in display", "number": 1109, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1109", "body": ""}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/**********-Screen%20Shot%202021-10-07%20at%209.25.13.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1109/_/diff#comment-*********"}}
{"comment": {"body": "This looks really good.\n\nCurrently only affects dashboard, do we want it in API and/or history?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1109/_/diff#comment-253190225"}}
{"comment": {"body": "Having it just in the UI is fine for now.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1109/_/diff#comment-253191501"}}
{"title": "Fix prolonged locking of the DB", "number": 111, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/111", "body": "The bug: after training, sometimes classification times out since the insertion of packets to DB takes a lot of time and it grabs a the DB lock.\nWe have seen this problem in the BLE server."}
{"comment": {"body": "don\u2019t you want to connect outside the loop?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/111/_/diff#comment-148779693"}}
{"comment": {"body": "That\u2019s the fix.. when we connect outside the loop we grab the DB lock for too long.. see description and comment in code.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/111/_/diff#comment-148779945"}}
{"comment": {"body": "Yeah, missed that", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/111/_/diff#comment-148780027"}}
{"title": "L2 advanced rules", "number": 1110, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1110", "body": ""}
{"title": "Hotfix: fixed path for android static IP typing", "number": 1111, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1111", "body": ""}
{"comment": {"body": "Don\u2019t you want to add the `and device_inf_os == \"Android\"` here as well?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1111/_/diff#comment-*********"}}
{"title": "Iphone L2 model in history", "number": 1112, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1112", "body": "update fing key\nfix l2_model based typing in history file\n\n"}
{"title": "DB compatibility rc20->rc21+", "number": 1113, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1113", "body": "make history table compatible between versions"}
{"title": "Making sure fingerbank result are actually written to DB", "number": 1114, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1114", "body": ""}
{"title": "Integrate CFO Temperature Model and Estimation", "number": 1115, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1115", "body": "Integrate CFO model and CSI->CFO estimation.\n\nTODO:\n\nInclude multi band+channel model.\nOngoing updates.\n\n* Most of the volume of the PR is research code which is not integrated into the actual system. We might want to integrate research_master into master first to make this PR more readable."}
{"title": "Dynamically load L2 + merge local changes from production", "number": 1116, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1116", "body": ""}
{"comment": {"body": "Add `device_addr` as input to `_get_device_model_from_fingerbank_for_display` in the `write_device_line` function.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1116/_/diff#comment-*********"}}
{"comment": {"body": "Done.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1116/_/diff#comment-*********"}}
{"title": "More data", "number": 1117, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1117", "body": ""}
{"title": "L2 probe model", "number": 1118, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118", "body": ""}
{"comment": {"body": "git lfs pcap and pickles? \\(which are not already lfs\u2019d\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-*********"}}
{"comment": {"body": "To ignore or not to ignore?\n\n\\(If not \u2192 lfs\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-*********"}}
{"comment": {"body": "No chance that a useful packet will be overwritten by one without the required data? \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254168611"}}
{"comment": {"body": "Do you want to add it to the `summary()` func to make sure it\u2019s included in ret prints?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254171367"}}
{"comment": {"body": "ignore is also take effect against lfs, so if you ignore a file it\u2019s not going to lfs unless you do --force\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254338685"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} I was probably misunderstood: Currently you don\u2019t ignore this pcaps. But some of them are also not lfs\u2019d. So I\u2019m only saying - if you keep them in git then lfs them.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254373461"}}
{"comment": {"body": "There is a lot of information here and there is no need to print it in the summary", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254396337"}}
{"comment": {"body": "It might be that we would need to collect a couple of packets instead of a single one.\n\nBut for the iOS/iPhone case there is only a single probe request so for now it should be fine.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254396593"}}
{"comment": {"body": "Done.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254398301"}}
{"comment": {"body": "Can also do pcaps in device\\_typing/test\\_data and model pickle.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254404516"}}
{"comment": {"body": "The model pickle is in LFS.\n\nFor test\\_data there is not need as there are small files < 1KB", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254404957"}}
{"comment": {"body": ":thumbsup:  It wasn\u2019t clear in the Bitbucket view", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1118/_/diff#comment-254406184"}}
{"title": "Feature/device typing with slopes", "number": 1119, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119", "body": "Add L1 device typing with basic capabilities\n\nCode is in device_typing_l1 folder. Ignore other changes as the branch is just not up to date with master!\nAdd pipeline artifacts in device_typing_l1/model and device_typing_l1/data\n\n\n\nL1 typing is integrated into the flow of device_typing but results are not considered!\n\n\n\n"}
{"comment": {"body": "what are the changes that aren\u2019t in the typing slopes? relevant?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255513674"}}
{"comment": {"body": "I described it in the PR description. The `feature/device_typing` branch is not in sync with master and these are files from master.\n\nThe relevant changes are in device\\_typing\\_l1 folder.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255521391"}}
{"comment": {"body": "can\u2019t this be dropped in the SQL query?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255729971"}}
{"comment": {"body": "it\u2019s too specific for this task to change the SQL query. Anyway, it\u2019s a small optimization as there are only about 1k entries in `db.db`.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255733909"}}
{"comment": {"body": "I know. How show we proceed to merge this to branch?\n\nLet\u2019s discuss this later today\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255735490"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} ok", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255735578"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66}  Please change back the plotting and work with slopes to work with freq and theta and not x and y peaks. This transformation helps better to look at the feature space", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255788423"}}
{"comment": {"body": "lab labeled dataset?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255800239"}}
{"comment": {"body": "why not use tra?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255801345"}}
{"comment": {"body": "why not convert to df?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255802505"}}
{"comment": {"body": "Very Cool!!!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255802895"}}
{"comment": {"body": "might be ok for now", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255810027"}}
{"comment": {"body": "what do you mean `tra`?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255810208"}}
{"comment": {"body": "no particular reason to", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255810266"}}
{"comment": {"body": "thanks!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255810296"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} line 105", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255813128"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} it\u2019s just for deserialization of the feature. training returns a pickled feature", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255813332"}}
{"comment": {"body": "Approved, but lets discuss the PR", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1119/_/diff#comment-255822190"}}
{"title": "Sometimes insertion of DB grabs the DB lock repeatedly, we need to allow others more access to DB", "number": 112, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/112", "body": "Same bug, different scenario:\nSometimes a single user gets the DB lock repeatedly while inserting packets to DB. Adding a small sleep so other operations can access the DB (as far as I know theres no order when waiting for a lock).\n"}
{"title": "Feature/cfo simple v3 1", "number": 1120, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1120", "body": "Add version 3.1 with \"simple\" rules:\n\nDont learn router temperature and just use the prior\nLearn only the bias\nOnline train only if the router temperature is close to previously learnt router temperature\n\n\n\nEvaluation with model 3.1\n\n\n"}
{"comment": {"body": "Add in comment that trains only bias", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1120/_/diff#comment-256026434"}}
{"comment": {"body": "can be asserted?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1120/_/diff#comment-256026891"}}
{"comment": {"body": "add in comment that trains only bias of phone", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1120/_/diff#comment-256027442"}}
{"comment": {"body": "it\u2019ll be fine even if train is not called.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1120/_/diff#comment-256743086"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1120/_/diff#comment-256743169"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1120/_/diff#comment-256743190"}}
{"title": "Detection of iOS version", "number": 1121, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1121", "body": "Introduce data from user-agents\nUsing behavioral properties to conclude iOS version\n\n"}
{"title": "Nitzan/phy nn gal", "number": 1122, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1122", "body": "added recordings to db\nmoved all dbs to datasets/db\nadded hdf5 db creation and trainer\n\n"}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1122/_/diff#comment-256721586"}}
{"title": "recsys: Record radiotap from all bands simultaneously and write band identifier to radiotap table", "number": 1123, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1123", "body": ""}
{"comment": {"body": "What happened until now? Didn\u2019t we have a worker for each iface/band?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1123/_/diff#comment-257037925"}}
{"comment": {"body": "We were recording only on one band \\(you would select in the UI wether to record 2.4 or 5\\).\n\nI want to record all bands simultaneously all the time.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1123/_/diff#comment-257038440"}}
{"title": "more path fixes", "number": 1124, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1124", "body": "important path fixes"}
{"title": "added a filter for WPS type OUI", "number": 1125, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1125", "body": ""}
{"comment": {"body": "It looks there is some problem with the pcap files\n\nThey look to be removed from git LFS and added as regular git files:\n\n![](https://bitbucket.org/repo/8X5z9dk/images/486623510-Screen%20Shot%202021-10-26%20at%2017.38.45.png)\ncan you try to revert this change?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1125/_/diff#comment-257033085"}}
{"comment": {"body": "i\u2019ll try now", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1125/_/diff#comment-257033562"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1125/_/diff#comment-257039529"}}
{"comment": {"body": "Awesome!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1125/_/diff#comment-257039593"}}
{"title": "filter WPS data in the right place + fix vendor dict to include OUI type", "number": 1126, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1126", "body": ""}
{"comment": {"body": "Break the WPS to remove only the unique fields. maybe others give typing info", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1126/_/diff#comment-257187249"}}
{"comment": {"body": "It will a bit harder as we don\u2019t currently parse that field.\n\nMaybe we can do it as a second step here.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1126/_/diff#comment-*********"}}
{"comment": {"body": "Nitzan said he can do it easily. If not, I agree we can write a comment and do it later", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1126/_/diff#comment-*********"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1126/_/diff#comment-*********"}}
{"title": "Feature/device typing", "number": 1127, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1127", "body": "added google taxonomy data\nremove the use of bidict\n\n"}
{"comment": {"body": "Beautiful!!!!!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1127/_/diff#comment-*********"}}
{"title": "reduced number of duplicates in L2 model, removed rows with nan info from model, fixed naming of Adnroid in model OS", "number": 1128, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1128", "body": ""}
{"title": "[Snyk] Security upgrade python from 3.8-buster to 3.9-slim", "number": 1129, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1129", "body": "As this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nKeeping your Docker base image up-to-date means youll benefit from security fixes in the latest version of your chosen image.\nChanges included in this PR\n\nccpilot/docker/classifier/Dockerfile\n\nWe recommend upgrading to python:3.9-slim, as this image has only 37 known vulnerabilities. To do this, merge this pull request, then verify your application still works as expected.\nSome of the most important vulnerabilities in your base image include:\n| Severity                                                                                                                 | Priority Score / 1000  | Issue                                                                | Exploit Maturity      |\n| :------:                                                                                                                 | :--------------------  | :----                                                                | :---------------      |\n|    | 500  | Integer Overflow or Wraparound SNYK-DEBIAN10-GLIBC-1315333   | No Known Exploit   |\n|    | 500  | Buffer Overflow SNYK-DEBIAN10-PYTHON27-1063178   | No Known Exploit   |\n|    | 500  | Buffer Overflow SNYK-DEBIAN10-PYTHON27-1063178   | No Known Exploit   |\n|    | 500  | Buffer Overflow SNYK-DEBIAN10-PYTHON27-1063178   | No Known Exploit   |\n|    | 500  | Buffer Overflow SNYK-DEBIAN10-PYTHON27-1063178   | No Known Exploit   |\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings"}
{"title": "Add progress logs to BLE server", "number": 113, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/113", "body": "Using same mechanism as in WIFI. \nPacket type for IQ is actually dict, so I added the KEY for BLE in an indirect way."}
{"title": "update CSISequence class", "number": 1130, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1130", "body": ""}
{"title": "Feature/slopes feature classifier", "number": 1131, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1131", "body": "!!!READ DESCRIPTION!!!\n\nLarge scale analysis of using testers instead of slopes\nLots of the files are merged from amazon POC - ignore them\n\nLook only in directories\n\n\ndevice_typing_l1:\n\nPipeline includes generating angle testers and their thresholds\nFinal confusion matrix in device_typing_l1/model_testers/slopes_testers_table.csv\n\n\n\nworkspace/wifi_slopes\n\nRefactor SDK_FFT for angle tester, but with same preprocessing\n\n\n\n\n\n"}
{"comment": {"body": "SVM?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1131/_/diff#comment-260002836"}}
{"comment": {"body": "Please separate mid results and plots from Model DB Files", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1131/_/diff#comment-*********"}}
{"comment": {"body": "moved files to intermediate folder in model\\_testers", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1131/_/diff#comment-*********"}}
{"comment": {"body": "svm?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1131/_/diff#comment-*********"}}
{"title": "[Snyk] Security upgrade starkbank-ecdsa from 1.1.1 to 2.0.1", "number": 1132, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1132", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nccpilot/docker/classifier/requirements.txt\n\n\n\nWarning\n```\nsendgrid 6.9.0 has requirement starkbank-ecdsa=1.0.0, but you have starkbank-ecdsa 2.0.2.\npython-jose 3.3.0 requires rsa, which is not installed.\nnumba 0.47.0 requires llvmlite, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Priority Score ()                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|-------------------------|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n  |  656/1000  Why?* Recently disclosed, Has a fix available, CVSS 7.4  | Improper Verification of Cryptographic Signature  SNYK-PYTHON-STARKBANKECDSA-1913041 |  starkbank-ecdsa: 1.1.1 - 2.0.1  |  No  | No Known Exploit \n(*) Note that the real score may have changed since the PR was raised.\nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the effected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "added csi2cfo estimator v2 + some updates", "number": 1133, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1133", "body": ""}
{"comment": {"body": "beautiful!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1133/_/diff#comment-*********"}}
{"title": "Feature/keras model to tflite2", "number": 1134, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1134", "body": "Add script to convert Tensorflow model to TFLite (.tflite) and TFLite-Micro (.cc) formats\n\nJust call ./to_tflite.sh v1 for v1 (and same for v2, etc..)\nAdd convereted models of v1 and v2\n\n\n\n"}
{"title": "added cfo live plot + mixed math tools to levlmath", "number": 1135, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1135", "body": ""}
{"title": "cfo model update", "number": 1136, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1136", "body": "and backports"}
{"title": "Feature/cfo estimator model filtering", "number": 1137, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1137", "body": "Add 3.1 filters for cfo models\n\nBased on nitzans magic numbers for accepting CFO range and RSSI filter\n\n\n\nAdd minimum number of packets for model 3.1\n\nUpdated page of v3.1: \nBugfixes in the model\n\n"}
{"title": "Pilot/dt typing", "number": 1138, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1138", "body": "disable sesning\nrenable CFR\nCFR capturing changes\ncapture CFR only for a limited time post connection\npilot changes\nchange tz to berlin\n\n"}
{"title": "Slopes typing model", "number": 1139, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1139", "body": "With feature extraction improvement, more lab data (with CFR rate of 5ms interval), kiosk data and confusion matrices"}
{"title": "Added session-id, adv-timeout, conn-timeout", "number": 114, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/114", "body": "Devices now transmit a lock-decided session id instead of device-id.\nThis is needed because multiple devices can share the same ID on frauds.\nFixed nordic to use the new adv format.\nAdded an adv timeout.\nAdded connection timeout after 30 seconds in bosch repo. UI now show it."}
{"title": "Master into research_master", "number": 1140, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1140", "body": "integrate v2.1 of cfo model, dual identifiers - actual extracted CFOs and CFR-based estimaded ones\nsmall fixes\nmove to param_list, multiple bands, debug info, support exclusive and shadow mode\nupdate prints, fix serialization of dict\nadd cfo median logging\nadd mae metric\nExclusive feature to affect unique, solid, and activity filtering\nExtract temps from source AP, CFO additional by default\nadd git lfs'd model\nfilter to include probe requests\nfix typo\nfix filter\nrecord radiotap on all bands\nactually read from multiple streamms\nWrite band to DB\nL2 model\nenable CFR capturing for sensing\nbackground periodicity\nclear log on output file\nincrease history len to update threshold\nsmooth out decision\ndifferent update rule for threshold up/down\nchange starting threshold\nthreshold for self motion\nlower debug level\nreduce debug level\nQuite an important debug print\ndo not display motion state of not online devices\nescape unicode characters in dhcp_vendor\nlow snr\nthreshold cliff\nless prints\nhystersis for motion result\nsleep icon + sleep stet hystersis\npresent device sleeping if nor CFR\nadd low SNR symbol\nset threshold for low snr\nadjust RSSI levels\nbetter low rssi condition\nreverse condition\nchanged self motion threshold from 25 to 20 in 5G\nchanged snr threshold from -82 to -80\nfixed time reporting\nremoved the most annoying print\nupdate params\nlarge window + SNR hystersis\nmore deviceS\nfix display of band\nprint debug RSSI\nless jitter in sleep state\nAdd output result to printout\nios 15.1 wispr\nfix zero packets after filtering + typo\ndisable sesning\nrenable CFR\nfix conflict\nfix conflicts\nCFR capturing changes\ncapture CFR only for a limited time post connection\npilot changes\nchange tz to berlin\nupdate fing api key\nAdd devices\nfixes in labels and new model adding kiosk data\ncomment out production key\nPrefer Galaxy S on Galaxy Note\nModel small adjustments\nmodel naming convention\ncombined model name\nRename back to Samsung Galaxy\nActually infer manufacturer from L2 model\nremove spamming debug log\nWhitespace strip all data\nfilter result by manufacture\nsupport also 3 model options\nFixed API to contain all information about a device\nfixed missing parameter in history file generation\nre-add DT API key\nAdd known ipad device\nmore devices\nbonjour uid in history table\nstay with only better_l2_model\nPixel preferenecs\ncatch ecxception\nFixed ios15 / iphone bug\nReturn vendor, not OS\nAdd a bit more labels\ntwo same devices\ndeleted unused code\nUpgrade find_conflicts to print a score rather than True/False result\nAdd directed/global probe as a feature\nKeep also an L2 model for investigation\nUse ext_caps original values\nAdd tag list as a feature\noperating mode notif have become redundant\nAdd QUIC capturing\nAdd utility to downsize pcap files\nnew lab data\nadjust labels\nrebuild models\nAdd QUIC parsing of UA\nMake use of QUIC user-agent in fingerbank\nProper dissect QUIC packets\nUse probe matches where no assoc matches\nhandle older android versions\nmake sure return UA is printable\nrefine print of android verison in UI\nRun user-agent extraction from main\nimprove printing of OS versions\ntry/except block for quick\nignore cronet in quic UA\nAbstraction of QUIC version crypto\nQUIC version Q050 parse\nfixed function name\nremove print\nrefined when to use L2 model\n\n"}
{"title": "Merge master into research master", "number": 1141, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1141", "body": "integrate v2.1 of cfo model, dual identifiers - actual extracted CFOs and CFR-based estimaded ones\nsmall fixes\nmove to param_list, multiple bands, debug info, support exclusive and shadow mode\nupdate prints, fix serialization of dict\nadd cfo median logging\nadd mae metric\nExclusive feature to affect unique, solid, and activity filtering\nExtract temps from source AP, CFO additional by default\nadd git lfs'd model\nfilter to include probe requests\nfix typo\nfix filter\nrecord radiotap on all bands\nactually read from multiple streamms\nWrite band to DB\nL2 model\nenable CFR capturing for sensing\nbackground periodicity\nclear log on output file\nincrease history len to update threshold\nsmooth out decision\ndifferent update rule for threshold up/down\nchange starting threshold\nthreshold for self motion\nlower debug level\nreduce debug level\nQuite an important debug print\ndo not display motion state of not online devices\nescape unicode characters in dhcp_vendor\nlow snr\nthreshold cliff\nless prints\nhystersis for motion result\nsleep icon + sleep stet hystersis\npresent device sleeping if nor CFR\nadd low SNR symbol\nset threshold for low snr\nadjust RSSI levels\nbetter low rssi condition\nreverse condition\nchanged self motion threshold from 25 to 20 in 5G\nchanged snr threshold from -82 to -80\nfixed time reporting\nremoved the most annoying print\nupdate params\nlarge window + SNR hystersis\nmore deviceS\nfix display of band\nprint debug RSSI\nless jitter in sleep state\nAdd output result to printout\nios 15.1 wispr\nfix zero packets after filtering + typo\ndisable sesning\nrenable CFR\nfix conflict\nfix conflicts\nCFR capturing changes\ncapture CFR only for a limited time post connection\npilot changes\nchange tz to berlin\nupdate fing api key\nAdd devices\nfixes in labels and new model adding kiosk data\ncomment out production key\nPrefer Galaxy S on Galaxy Note\nModel small adjustments\nmodel naming convention\ncombined model name\nRename back to Samsung Galaxy\nActually infer manufacturer from L2 model\nremove spamming debug log\nWhitespace strip all data\nfilter result by manufacture\nsupport also 3 model options\nFixed API to contain all information about a device\nfixed missing parameter in history file generation\nre-add DT API key\nAdd known ipad device\nmore devices\nbonjour uid in history table\nstay with only better_l2_model\nPixel preferenecs\ncatch ecxception\nFixed ios15 / iphone bug\nReturn vendor, not OS\nAdd a bit more labels\ntwo same devices\ndeleted unused code\nUpgrade find_conflicts to print a score rather than True/False result\nAdd directed/global probe as a feature\nKeep also an L2 model for investigation\nUse ext_caps original values\nAdd tag list as a feature\noperating mode notif have become redundant\nAdd QUIC capturing\nAdd utility to downsize pcap files\nnew lab data\nadjust labels\nrebuild models\nAdd QUIC parsing of UA\nMake use of QUIC user-agent in fingerbank\nProper dissect QUIC packets\nUse probe matches where no assoc matches\nhandle older android versions\nmake sure return UA is printable\nrefine print of android verison in UI\nRun user-agent extraction from main\nimprove printing of OS versions\ntry/except block for quick\nignore cronet in quic UA\nAbstraction of QUIC version crypto\nQUIC version Q050 parse\nfixed function name\nremove print\nrefined when to use L2 model\n\n"}
{"title": "Grisha/l2 typing with router info", "number": 1142, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1142", "body": "Update caps parser to also parse beacons\n\nAlso update endianess of some fields to match with wireshark\n\n\n\nAdd script to record beacons and packets of 3rd party router\n\n\nAdd parsing and analysis of beacons + association packets\n\nOutputs data for this page tree: \n\n\n\n"}
{"comment": {"body": "Eros is going to be based on this code. Please approve @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} @{5d74d49897d8980d8eacd7f8} ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1142/_/diff#comment-303018894"}}
{"title": "Noam/explicit bf", "number": 1143, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1143", "body": "Explicit beamforming initial commit, porting from Levl repository\nExplicit beamforming move from levl\nAdded reciprocity feature simulation\nAdded compression and quantization according to the 802.11ac specifications\nAdded ofdm-rx simulation and minor changes in reciprocity feature\nRssi difference back up\nAdded support to HE (802.11ax)\nRSSI difference between several devices + MU Reciprocity\nAdded MU Reciprocity\nsuccessfuly parsed MU bf report with nc = 1\nfew bugs fixed - byte order and length of sc_tag\nAction No Ack Parsing from db\nSNR Algorithm, training and classification, very raw\nUpdated parse action no acks\nSmall improvements to snr algorithm, including dimensions distribution distance calculation\nsnr algorithm updated distribution\nVersion before segmented regression\nClassification and training for segmented regression\nAdded evaluation over folders, initial version\nAdded prior to segmented regression, still needs adaptation for breakpoints next to the end\nHard prior algorithm\nAdded types of segmented regression, reduced to the case of one breakpoint, new classes for training and classification parameters\nAdded parallelization of different training sessions\nAutomated picking of training sessions, improved runtime by performing parsing of the pcap files only once and saving it to pickles\nAdded unified performance report\nReplaced scipy's decimate with simple average\nAdded Average TPR and FPR in the final report\nChanged Averaging method, averaging is done by time windows\nMinor changes to training and classification parameters classes\nChanged training session picking, added validation of training session\nAdded maximum value of 53.75dB in the built model, according to the maximum possible STA SNR\nAdded db parsing to pickle, mac addr list is read from the data\nAdded all files before merge pull\nSmall changes in db data handling, computation of total fpr\nStart of version 2 - full ap snr range was not necessarily present during training, model can extrapolate depending on zone learned. If linear zone detected, extrapolate for smaller ap snrs. If saturation zone detected, extrapolate for higher ap snrs. If meaningful breakpoint detected, extrapolate to all snrs\nAdded variance condition on AP SNR for extrapolation\nRemoved types of segmented regression. In the case of extrapolation to unobserved snrs, when comparing to linear regression of slope 1 or zero, the segmented regression should be with segments of slopes 1 and 0.\nAdded Not Enough Data statistics\nFirst sketch of online training\nFixed update bug when N was run-over\nAdded case in online training when variance condition was not met in the initial training session\nFixed some bugs, added case where invalid training session was learned\nRemoved duplication of codes by adding some functions\nChanged min ap snr variance to 25, meets TPR requirements\nRevised online training - same mechanism as initial training, based on running averages per bins of ap snrs\nAdded indices of subcarriers between V matrices and delta SNR and computation of the absolute frequency response of each path\nAdded all changes before checkout to cypress\nAdded V Dimensions classifier\nChange explicit BF\nIgnore beamforming stuff\n\n"}
{"title": "bug fixed", "number": 1144, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1144", "body": ""}
{"title": "Grisha/cfo from bf estimator", "number": 1145, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1145", "body": "Add the pipeline of CFO estimation from BF:\n\n\nRead from BQ to parquet on S3\n\nMerge BF with CFO data based on server timestamp\n\n\n\nparse the BF compressed messages and store on parquet on S3\n\nTrain a model on decompressed bits or V matrix\n\n\n\nPage: "}
{"title": "L2 devices db", "number": 1146, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1146", "body": "added new pcaps and and changed model name to devices_db canonical name\nfixed some samsung devices\nfixing android model name\nhandles GLiNet\nadded readme\nremoved timestamp column\nfixed Microsoft surface label\ndeleted old labels folders\nchange output names to lowercase L\ndefault contain only EnGenius data\nmerged all pcaps to 1 pcap per platform\nadded mergecap command to readme\nadded new data\ncreate script that download pcaps from S3\n\n"}
{"comment": {"body": "do we need the files under `labels_michael` directory?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1146/_/diff#comment-308382390"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Please let us know if the files are relevant. We\u2019d want to leave the repo as clean as possible", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1146/_/diff#comment-308476984"}}
{"comment": {"body": "Why are there so many pcap files?\n\nWe cannot have a pcap file per device. Please merge them, for each category, under a single file. \\(Use `mergecap` if necessary\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1146/_/diff#comment-308530335"}}
{"comment": {"body": "I got no idea what those file are. @{621df03094f7e20069fd6ab2} ?\n\nWe should keep just a single set of labels.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1146/_/diff#comment-308530563"}}
{"comment": {"body": "Those are original labels before fixes of Dror for identifier and model name according devices db.. They can be removed.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1146/_/diff#comment-308531440"}}
{"comment": {"body": "Agree, no need to store separate files, preferred to store the pcap files in S3. Thus can be used in other sub-systems \\(UT, Sparrow\u2026\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1146/_/diff#comment-308531665"}}
{"comment": {"body": "That\u2019s why we have history in git :upside_down: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1146/_/diff#comment-308531755"}}
{"title": "fixed vendor_ouis_20722_8", "number": 1147, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1147", "body": "fixed vendor_ouis_20722_8\nadded new data\n\n"}
{"comment": {"body": "The noise is actually not eliminated from the 20722\\_8 OUI.\n\nFor example the values \\(00**13**00\\) and \\(00**12**00\\) are also noisy here.\n\n```\n33,4,0,5,vendor_ouis_20722_8|001300$vendor_ouis_36940_4|07$vendor_ouis_4120_2|000010000000$vendor_ouis_6130_10|00010400000000$,0-1-45-127-191-221-221-221-221,nan,nan,nan,nan,nan,0000080400000040,15,842039311,nan,nan,nan,Apple,Apple OS,iPhone X\n33,4,0,5,vendor_ouis_20722_8|001200$vendor_ouis_36940_4|07$vendor_ouis_4120_2|010010000000$vendor_ouis_6130_10|00010400000000$,0-1-45-127-191-221-221-221-221,nan,nan,nan,nan,nan,0000080400000040,271,846233871,nan,nan,nan,Apple,Apple OS,iPhone X\n```\n\nI think we need to remove all the value of this OUI and leave it blank for just have this OUI / not have this OUI.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1147/_/diff#comment-311893242"}}
{"comment": {"body": "Fixed in the new commit", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1147/_/diff#comment-311896992"}}
{"comment": {"body": "Are we merging this to the master?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1147/_/diff#comment-313785674"}}
{"title": "fix radiotap iter_information_elements", "number": 1148, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1148", "body": "The iter_information_elements function of the radiotaps parsed the FCS at the end of the packet as if it was IE. This created extra l2 model signatures with false information element.\n\n:warning: This needs to be fixed in eros repo too"}
{"title": "L2 textual fingerprints", "number": 1149, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1149", "body": "The purpose is to have a quick way to lookup and compare 802.11 caps / L2 fingerprints, please consider the following suggest for a string representation. For even quicker lookup, we can compare hashes of the fingerprints instead of the full fingerprints.\n\n{'meta_mac': 'ec:89:14:c2:2b:f5', 'ieee802_caps': '12548', 'listen_interval': '768', 'ht_caps': '289', 'ext_caps': '0100008000000040', 'rsn_caps': 'ac02', 'subtype': '0', 'directed': '1', 'power_caps_0': '4', 'power_caps_1': '20', 'band': '24', 'vendor_ouis': 'vendor_ouis_20722_2|000100$vendor_ouis_11306301_130|01000000$', 'tag_list': '0-1-50-33-221-45-127-221-48-59'}\nWould be represented as:\nASSOC*24*4*20*768*12548*0-1-50-33-221-45-127-221-48-59*289*0100008000000040*0100008000000040***ac02**vendor_ouis_20722_2|000100$vendor_ouis_11306301_130|01000000$\n\n\n{'meta_mac': '40:4e:36:d2:7c:01', 'ht_caps': '417', 'ext_caps': '04000a820040004080', 'vht_caps': '4403', 'vht_caps_full': '2457436467', 'subtype': '4', 'directed': '0', 'band': '24', 'vendor_ouis': 'vendor_ouis_20722_8|$vendor_ouis_5271450_16|20$', 'tag_list': '0-1-50-3-45-127-191-221-767-107-221'}\nWould be represented as\nPROBE*24*0*0-1-50-3-45-127-191-221-767-107-221*417*04000a820040004080*04000a820040004080*2457436467****vendor_ouis_20722_8|$vendor_ouis_5271450_16|20$\n"}
{"title": "Bugfix/tolerant reg tests", "number": 115, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/115", "body": "Main diff: Timing tests raise warnings instead of exceptions; slowed down playback rate to x1.5 instead of x3 so that connections last enough time for algorithm to not be interrupted.\nArtifacting logs.\nDont exit on 'docker volume prune' fail\n\n"}
{"title": "fixing band selection", "number": 1150, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1150", "body": ""}
{"title": "again: fix band selection", "number": 1151, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1151", "body": ""}
{"title": "fixing endianess of capfp features", "number": 1152, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1152", "body": ""}
{"title": "bugfix/MEROSP-1171+MEROSP-1173, added surface for glinet", "number": 1153, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1153", "body": "bugfix/MEROSP-1171+bugfix/MEROSP-1173, added surface for glinet\nnew model and label fixing\nseperated model creation and uploading to s3\nforce subtype to int\n\n"}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} @{5d74d49897d8980d8eacd7f8}   \nCan you both review the changes in the PR and approve them?\n\nThank you", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1153/_/diff#comment-317263708"}}
{"comment": {"body": "Can a default reviewer approve the PR?  \nI want to merge it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1153/_/diff#comment-317559601"}}
{"title": "MEROSP-1227 and MEROSP-1236 update l2 model with new devices", "number": 1154, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1154", "body": "update l2 model with new devices - glinet\nclean code\n\n"}
{"title": "added option for raw statistics", "number": 1155, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1155", "body": "added option for raw statistics\nfixed save_and_print function\nfixed lab labels\n\n"}
{"comment": {"body": "Great PR!!!!! ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1155/_/diff#comment-317909873"}}
{"title": "Feautre/adjust to family names", "number": 1156, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1156", "body": "adjust to family names\nmissed changes\nupdated to latest device db\nrename devices db file\n\n"}
{"title": "L2 new model after family", "number": 1157, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1157", "body": "adjust to family names\nmissed changes\nupdated to latest device db\nrename devices db file\nsorted results\nfixed lab labels\nremoved pickle\nedited readme\nall glinet lab recordings\n\n"}
{"comment": {"body": "can someone approve/comment this PR?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1157/_/diff#comment-319090459"}}
{"comment": {"body": "finally the expected results are sorted! that\u2019s great!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1157/_/diff#comment-319104324"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} @{5f82bf320756940075db755e} ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1157/_/diff#comment-319104571"}}
{"title": "L2 dry run", "number": 1158, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1158", "body": "eng dogfood data test\nfixed surface\niphone 5 in glinet from cujo\narrange result columns\nchanged file_name to file_suffix\n\n"}
{"comment": {"body": "where is this module located?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1158/_/diff#comment-319207720"}}
{"comment": {"body": "in the folder with the other relevant .py files", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1158/_/diff#comment-319212587"}}
{"comment": {"body": "Can someone approve my PR???", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1158/_/diff#comment-319459897"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} @{5f82bf320756940075db755e} ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1158/_/diff#comment-319464564"}}
{"title": "new l2 model with cujo dry run", "number": 1159, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1159", "body": "cujo dry run\npixel 3 data - MEROSP-1289\nfixed bug in dogfood\n\n"}
{"title": "Fixed fixed model", "number": 116, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/116", "body": ""}
{"comment": {"body": "model model\\*", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/116/_/diff#comment-149182273"}}
{"title": "L2 dry run 20220731", "number": 1160, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1160", "body": "2022-07-31 cujo dry run data and added manual labels\nadded option for investigation in the features data\nadded iphone6s data from cujo\nadded data from cujo of apple watch - assigned to apple watch series 7\nfixed apple watch new fp - fe:7d:34:a7:18:64, ee:26:c3:a4:0b:ee, 18:7e:b9:26:d0:9f - split to 7,6, and se\n\n"}
{"comment": {"body": "the 4th column is missing. is that on purpose?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1160/_/diff#comment-320069694"}}
{"comment": {"body": "yes,  \nthis is the identifier column, and we don\u2019t know them for those mac addresses", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1160/_/diff#comment-320070066"}}
{"title": "CUJO model update 2022/08/01 with CUJO labels", "number": 1161, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1161", "body": ""}
{"comment": {"body": "redundant, they are like the last two rows", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1161/_/diff#comment-320431085"}}
{"comment": {"body": "Fixed.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1161/_/diff#comment-320462921"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4}   \nI don\u2019t see here iPhone 7 that we saw in the result logs\n\nApple\tApple OS\tiPhone 7\tiPhone9,3\tee:fb:7a:47:74:46\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1161/_/diff#comment-320466432"}}
{"title": "l2 new model - separate pcaps", "number": 1162, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1162", "body": "MEROSP-1320 - split pcap file to its origin pcap files\nremoved malformed packets\n\n"}
{"title": "updated galaxsy s20 fe canonical name and fp", "number": 1163, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1163", "body": "updated galaxsy s20 fe canonical name\nnew model\n\n"}
{"title": "new dry run from cujo", "number": 1164, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1164", "body": "new dry run from cujo"}
{"comment": {"body": "filter is a python builtin, use another name", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1164/_/diff#comment-322438226"}}
{"title": "L2 model fixing", "number": 1165, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1165", "body": "added MacBook with M1 data\nfixed some canonical naming\nremoved google data\nignoring - fixed power caps - {: data-inline-card='' } \n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28534](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28534){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1165/_/diff#comment-325070299"}}
{"comment": {"body": "Very good job :clap: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1165/_/diff#comment-325187252"}}
{"title": "MEROSP-1450 - new canonical names for MacBooks", "number": 1166, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1166", "body": ""}
{"title": "pixel 2 L2 data for glinet", "number": 1167, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1167", "body": "pixel 2 L2 data for glinet"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30099](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30099){: data-inline-card='' }  \n[https://bitbucket.org/levl/eros-classifier/pull-requests/730](https://bitbucket.org/levl/eros-classifier/pull-requests/730){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1167/_/diff#comment-326757311"}}
{"title": "MEROSP-1556 L2 dry run 2022 09 04", "number": 1168, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1168", "body": "MEROSP-1556\n\nnew devices and data from cujo\nadded lab data for galaxy s21 and s22\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36503](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36503){: data-inline-card='' } Passed", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1168/_/diff#comment-331370991"}}
{"title": "added amazon fire data for glinet", "number": 1169, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1169", "body": "added amazon fire data for glinet"}
{"title": "Fix two way snr exception", "number": 117, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/117", "body": "As discussed with Noam:\n(1) Handle case of segmented regression with not enough values\n(2) Add min participation for training (at least 250 participating packets)\nWhen not enough values for segmented regression or less than 250 participating packets, model is not created and we return NEED_MORE_DATA."}
{"title": "new iphone 14 and apple watch series 8 data", "number": 1170, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1170", "body": "new iphone 14 and apple watch series 8 lab data:\n\ngli\neng\n\n"}
{"comment": {"body": "classifier PR: [https://bitbucket.org/levl/eros-classifier/pull-requests/825](https://bitbucket.org/levl/eros-classifier/pull-requests/825){: data-inline-card='' }   \n  \nreport portal: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/40304](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/40304){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1170/_/diff#comment-336372869"}}
{"comment": {"body": "id missing here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1170/_/diff#comment-336373319"}}
{"comment": {"body": "no, It changed position.\n\nI sorted by ID, so now it\u2019s between 146 and 400", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1170/_/diff#comment-336374133"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} there\u2019s no ID for the apple watch 8", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1170/_/diff#comment-336374339"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66}   \nIndeed, This device has not been inserted to the lab inventory yet.  \n  \nThe ID is not required for the L2 model creation, it\u2019s for tracking", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1170/_/diff#comment-336374398"}}
{"title": "Grisha/ipad vs iphone add performance", "number": 1171, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1171", "body": "Add another analysis stage called performance\n\nAdd metrics calculation API for model performance:\n\nExample panel for the report: {: data-inline-card='' }  under Charts/Performance metrics\n\n\n\n\n"}
{"title": "Grisha/ipad vs iphone add performance", "number": 1172, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1172", "body": "Add another analysis stage called performance\nAdd metrics calculation API for model performance\n\nExample panel for the report:  under Charts/Performance metrics:\n\n"}
{"comment": {"body": "Could you please include the unnormalized confusion matrix?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1172/_/diff#comment-360237259"}}
{"comment": {"body": "do you plan to add the accuracy and precision to the confusion matrix in the next commit?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1172/_/diff#comment-360237363"}}
{"comment": {"body": "Sure", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1172/_/diff#comment-360237917"}}
{"comment": {"body": "That\u2019s something I wanted to discuss about in the sync. It\u2019s taking a tremendous amount of time to visualize it on wandb, so I won\u2019t add the precision, sensitivity and accuracy to the same plot, as requested", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1172/_/diff#comment-360237988"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Why is it so complex? ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1172/_/diff#comment-360243502"}}
{"comment": {"body": "@{63553fd9b0b6ef035648ab9a} Having a column/row being visually different from the rest of the table is not stright forward. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1172/_/diff#comment-360247978"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} Why does it need to be visually different?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1172/_/diff#comment-360251243"}}
{"comment": {"body": "I\u2019ve added another panel. There\u2019s no visual distinction between the sensitivity/precision and the rest of the CM", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1172/_/diff#comment-360687509"}}
{"title": "Grisha/ipad vs iphone binary class", "number": 1173, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1173", "body": "Task\nChange pipeline to work with multiclass task\nOutput\nReport of model on wandb: \nExample graph:\n\nRan on a small dataset, just for the sake of development.\nChangelog\n\n\nAdd architecture called MLP3 for classification\nLabel is the model type (now called so instead of just model)\n\nSave prediction data in parquet format\n\nMore comfortable/efficient than CSV\n\n\n\nDisable balancing by downsampling of the data as it does not scale with 12.2 data\n\nAlso, replace (fix) nested balance function, but now really use it\n\n\n\nNew source:\n\nFix compatibility with new datasource\nUse ver 12.2 data\n\n\n\nTraining pipeline works\n\nTest is done on unseen router (eg.7) and unseen devices\n\nFix analysis and performance of multiclass task\n\nHopefully it still works with regression (antenna distance) task\n\n\n\nFix sorted data in the preprocessing step (Thanks, @{63553fd9b0b6ef035648ab9a} !) \n\n\n"}
{"comment": {"body": "why do you use string concatenation here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1173/_/diff#comment-375068747"}}
{"comment": {"body": "Visually it would be clearer, in my opinion", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1173/_/diff#comment-375750828"}}
{"title": "Grisha/LDI-847 preprocessing update", "number": 1174, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1174", "body": "Data selection based on more than 3 recordings of the same configuration\nPerform downsampling, but uniform, to shorten training time\nParallelize another step in persistence of the data\nUpdate kernel and preprocessing func\nBuild charts from the code\nFix regression: Fix performance code for antenna distance\nAdd CFO to report\n\n"}
{"comment": {"body": "Please refrain from using Caps for variable names.  \nThis is against pep8 and will not pass flake8 validation.  \nYou don\u2019t have to bother with this now in Comcast repo but know, that such code wont pass the pipeline in griffin\\_ml.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/1174/_/diff#comment-383636454"}}
{"title": "Added hack to make sure that the agent still sends CFRs", "number": 118, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/118", "body": "It looks like the problem is that the server gives up too quickly, so the watchdog isnt given time to handle this.\nRestarting the CFR timer before we change stations solves this in observed cases cases.\nIdeally we would adjust the watchdog to give up before the server does, or the server to be more tolerant, but we dont have time for this now, and the agent is going to be overhauled soon after V1."}
{"title": "Tee stdout to stderr for more logs", "number": 119, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/119", "body": ""}
{"title": "Reopen PR #8", "number": 12, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/12", "body": "\n"}
{"title": "Fixed phone to show disconnection on BLE stop", "number": 120, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/120", "body": "Notice that most changed code is just indented one level less."}
{"title": "CCP-82 allow tracing of all operations", "number": 121, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/121", "body": "Keep trace of all operations in DB: metadata and packets.\nAllow a url request to create a local pickle with operation metadata and packets.\n  The pickle is created in ccpilot/trace_info/\n  In the URL we provide the op_id (as can be seen in logs) and user.\n  URL example:\n\nAllow a url request to get all operations per user (if you want to see which operations the user ran and their op_ids).\n  In the URI we provide the user.\n  URL example: \nImplement a mechanism to run the operation pickle: operation_replay.py\n  Note that the env. variables should be the same as in levl_processor of course (same features enabled).\n\nUsage Notes:\n\nTheres currently no specific GUI that creates the URLs.. Just put the relevant URL in the browser.\nVery important: The packets are inserted to the DB asynchronously of the operation (this was not changed), since it takes time - so when an operation ends (for example training), the training packets are NOT in the db yet - so wait as bit, lets say 20-30 seconds from end of operation, before creating an operation pickle, so the data will be in the DB.\n\n"}
{"title": "Yair wifi sim", "number": 122, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/122", "body": "Add linear trend with sine on top to the phase, as well as LOS channel, generating multiple transmitters and plotting all of them together.\n"}
{"title": "Added option to record locally from the recsys", "number": 123, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123", "body": ""}
{"comment": {"body": "What\u2019s the use case for this?\n\nShouldn\u2019t it be an option in the web interface instead of env variable?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149577376"}}
{"comment": {"body": "what\u2019s the benefit of yaml over the regular pickle?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149577499"}}
{"comment": {"body": "Running experimental recordings that shouldn\u2019t be a part of the database", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149577679"}}
{"comment": {"body": "Pickles are annoying because they\u2019re tied to a specific version of the code \\(imports wise\\). You do as much as rename a directory and you\u2019ve broken backwards compatibility", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149578074"}}
{"comment": {"body": "YAML\u2019s are also human-readable which is nice", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149578235"}}
{"comment": {"body": "Adding to this, yaml is a well known format and package, and has built-in support for multiple documents in a file.\n\nThe yaml module even has a `load_all` function.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149578520"}}
{"comment": {"body": "Also YAML is accessible from all programming languages, pickle is Python specific", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149579546"}}
{"comment": {"body": "Hmmm\u2026. I would argue for JSON instead. It\u2019s readable, backward compatible and Pandas can read JSON files \\(in 1 liner\\) but not YAML.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149579616"}}
{"comment": {"body": "You can\u2019t do multiple documents easily with JSON, because you need `{` and `}` or `[` and `]` to make it valid JSON, so if you have software that constantly adds documents to a file and is killed in the middle, the entire JSON wouldn\u2019t load. With YAML just the last one will be corrupt", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149581386"}}
{"comment": {"body": "I believe this:\n\n```\nimport yaml\ndf = pd.DataFrame(yaml.safe_load_all(open(\"bla.yml\", 'r')))\n```\n\nShould work", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149581787"}}
{"comment": {"body": "This is not true for JSON. YAML is not purposed for serialization, unlike JSON.\n\nI\u2019m not going to argue further in this matter. Don\u2019t force unnecessary technology. Please be careful with this.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149583208"}}
{"comment": {"body": "And what about the web interface option?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149583342"}}
{"comment": {"body": "Can be added in the future. Nuriel wants this ASAP", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149586348"}}
{"comment": {"body": "Sure", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/123/_/diff#comment-149587713"}}
{"title": "Fix bug: allow None result and None model for trace information", "number": 124, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/124", "body": "(1) Result (and model) can be None when operation is killed or theres an exception;\n(2) None model should not be turned to bytes\nThis fixes a bug in inserting such data to DB."}
{"title": "The Tee is dead; long live the Tee", "number": 125, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125", "body": "Replaced Tee with TCPDump and added a split_stream module.\nThis change allows the recording to be less noticeable by the application, and allows for multiple tcp sessions and a more stable connection.\nThe output of the recording is a single pcap file containing all relevant streams, instead of one pickle per port.\nWeve looked for a tool that can replay these, but unlike its name tcpreplay does not do the job.\n\nSome pre-processing needs to be done prior to replay. (split_pcap_to_stream)\nIt is heavy because it needs to track the whole TCP session from start to finish, for each session. (also, scapy is super slow)\n(Its a separate script so that we dont need to process the pcap for each replay)\nThe preprocessesing script creates a directory and in it a separate pcap for each TCP session.\nThe replay script consumes this directory."}
{"comment": {"body": "Didn\u2019t you delete tee\\_replay.py?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149676654"}}
{"comment": {"body": "Not that I can tell. Should I?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149679975"}}
{"comment": {"body": "I\u2019ll do it in another branch for smooth transition of the tests.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149681268"}}
{"comment": {"body": "Note that doing that will require you to re-record some test cases.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149691001"}}
{"comment": {"body": "A comment explaining wtf this is would be great. I can\u2019t even begin to think about how to Google this", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149709696"}}
{"comment": {"body": "Isn\u2019t this a bit naive? How do you handle retransmissions? Out of order packets?  \n  \nOr maybe I\u2019m just not understanding the contents of `self.pcap_file` ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149710165"}}
{"comment": {"body": "The TCP stream, on all its retransmissions, out-of-orders and what not, is handled later anyway, isn\u2019t it? So we can be naive this once.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149768364"}}
{"comment": {"body": "The function name is literally longer than this snippet", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149779486"}}
{"comment": {"body": "Ummmm, no, Omer is right actually, this might cause duplicates or worse.  \nI should probably reconstruct the stream, right?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149780257"}}
{"comment": {"body": "teeCPDUMP", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/125/_/diff#comment-149927350"}}
{"title": "Fix two bugs: in segmented regression and in Two-way SNR feature", "number": 126, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/126", "body": "(1) When the Access Point, phone and their surrounding are still (no movement), the AP values are the same, so there's no delta - we should not attempt to do a regression.\n(2) The TwoWaySNRFeature code accidentally uses the segmented regression params even if there's a better approximation (I used the wrong constants in such scenario).  \nAlso fixed a small typo in two unrelated files."}
{"comment": {"body": "I think that such errors should be caught by the system so that we can investigate them later.\n\nEither raise an exception or log a warning. I was able to use the latter to capture such error with the large scale.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/126/_/diff#comment-149850922"}}
{"comment": {"body": "But this is not a bug Grisha. It just means we cannot do a segmented regression in such cases, which is ok.  \nThere are cases where there\u2019s no variance between the Access Point SNR values, so we cannot do a segmented regression.  \nIn such cases we use another method \\(see below in the code\\).", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/126/_/diff#comment-149852383"}}
{"comment": {"body": "I\u2019ll add a debug log stating why regression cannot be done.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/126/_/diff#comment-149852645"}}
{"title": "Modify snr avgs to an ongoing computation, so number of participants can be checked quicker", "number": 127, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/127", "body": "Modification for runtime performance improvement.\nBefore creating a Two Way SNR model, we check the number of snr packets that actually participated in the SNR averages.\nCurrently in order to know how many participants we have, we compute the snr avgs for all the data from scratch.\nThe fix keeps track on snr avgs so that the info is readily available and doesnt need to be calculated from scratch every time a model is requested."}
{"title": "Kill cfr_test_app on agent exit", "number": 128, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/128", "body": "Also fixed minor bug in the pc metadata agent."}
{"comment": {"body": "Revolutionary", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/128/_/diff#comment-149926644"}}
{"comment": {"body": "The largest of revolutions start with a simple peasant standing his ground\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/128/_/diff#comment-149926961"}}
{"title": "Levl malware", "number": 129, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/129", "body": "Implemented required parts for the levl malware app:\nAdded levl_malware app\nMinor changes\nAdded nodejs script for the rpi, more modifications to the android app.\nMalware is now working\nWill now search for a lock named target\n\n"}
{"title": "Added duration field", "number": 13, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/13", "body": "Field asked for by Yair."}
{"comment": {"body": "The code dis-reuse hurts my eyes", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/13/_/diff#comment-143122745"}}
{"comment": {"body": "I want to keep the code here for\u2026 reasons\u2026", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/13/_/diff#comment-*********"}}
{"comment": {"body": "Fine. I\u2019ll get off your back. But remember that people were executed for less.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/13/_/diff#comment-*********"}}
{"comment": {"body": "Wat", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/13/_/diff#comment-*********"}}
{"title": "Fix bug", "number": 130, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/130", "body": "Fixing syntax error - @{5b41d9de10d57114135eca66} note this affects last nights large scale results for Two Way SNR Feature, since the segmented regression was not used.\nI missed it yesterday because apparently I couldnt see logs using the tmp file I ran."}
{"title": "SmartLock app now has different names according to flavours", "number": 131, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/131", "body": ""}
{"title": "Feature/large scale confusion matrix", "number": 132, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/132", "body": "Start foundation for large scale for ccpilot wifi\nThis PR creates a manual pipeline:\n\nlarge_scale_downloader.py\ntrain_classify_pipeline.py\ngenerate_confusion_matrix.py\n\nGeneral notes and features:\n\nSupport only for RadioTap features\nRadioTap dataframes are synthesized in the meaning that they also have an association packet prepended to them\ntrain/classify has traceback logs so that source files can be loaded easily\nNumpy runtime warnings are not allowed. They are captured and handled as NOT MATCH\n\n\nId love to hear suggestions for module organization, runtime improvement, etc.\n\n"}
{"comment": {"body": "BUMP", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/132/_/diff#comment-150710718"}}
{"comment": {"body": "Is this really needed?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/132/_/diff#comment-150712045"}}
{"comment": {"body": "I don\u2019t see a workaround for this. Setting up python path just to run this script is annoying", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/132/_/diff#comment-150713522"}}
{"comment": {"body": "You can modify PYTHONPATH globally on your machine to contain the comcast repo with this UI.\n\n![](https://bitbucket.org/repo/8X5z9dk/images/1167570709-image.png)\n\u200c\n\nWe want to treat the root of the comcast repo as if it was \u201csite-packages\u201d - a directory containing packages you installed that you can just import - but since we don\u2019t want to actually install them - we have to do this PYTHONPATH step", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/132/_/diff#comment-150720803"}}
{"comment": {"body": "I see. I\u2019ll look into it", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/132/_/diff#comment-150721444"}}
{"title": "Feature/CCP-90 continue vdim feature implementation", "number": 133, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/133", "body": "Insert Noam's code, make feature work."}
{"comment": {"body": "Is there a way to integrate the code above with TwoWaySNR so that `.to_scapy()` is called once?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/133/_/diff#comment-150120901"}}
{"comment": {"body": "These features were actually together at the beginning of time. Then it was decided to separate them to different features.\n\nI guess I can try to share some struct between these two features, so that only one to\\_scapy\\(\\) can be done. I\u2019ll check.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/133/_/diff#comment-150124221"}}
{"comment": {"body": "I\u2019ll try to change to dkpt usage, but it will be in a different PR and I\u2019ll modify for both features.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/133/_/diff#comment-150158161"}}
{"title": "Big BLE change", "number": 134, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134", "body": "Way too many changes to list , but include:\n\nSync of states values between backend and lock client\nFixed states machine for BLE connections\nDisplay of progress of training in phone app\nHandling of disconnections (Phone  Lock, Lock  Agent, Agent  Cloud). Coupled with branch in bosch_integration: mich/keepalive_timers\n\n"}
{"comment": {"body": "Didn\u2019t it use the library\u2019s indication?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150614410"}}
{"comment": {"body": "What part introduces the user to the DB now?  \nNo other part in the code links the device type to the user.\n\nAlso, if unused, delete commented code.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150615924"}}
{"comment": {"body": "We have progress indication only the training part of the library.. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150615962"}}
{"comment": {"body": "Yeah. It\u2019s now disabled. Need to resolve it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616062"}}
{"comment": {"body": "This comment is for state JUST\\_CONNECTED \\(not DISCONNECTED\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616160"}}
{"comment": {"body": "`new_state` is always None. Did you mean to do that?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616194"}}
{"comment": {"body": ":disappointed: \n\nMight want to add a task to Jira", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616517"}}
{"comment": {"body": "I\u2019m not sure Disconnected is a good state name for BLE. There are no connections in BLE.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616573"}}
{"comment": {"body": "Regarding bosch\\_integration: mich/keepalive\\_timers. I don\u2019t see the need for multiple branches. It can be merged into comcast\\_app", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616636"}}
{"comment": {"body": "This entire function is not supposed to be called anymore. I\u2019ll just remove it.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616641"}}
{"comment": {"body": "I wanted to keep the comcast\u2019s master in sync with bosch\\_integartions' comcast\\_app. One I merge this PR to master, I\u2019ll merge the new branch into comcast\\_app.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616752"}}
{"comment": {"body": "I think that this PR changes that. It adds a lot of state to the BLE.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616762"}}
{"comment": {"body": "Well, there is a connection in a BLE and we need to maintain this state somehow, otherwise we cannot spit the data into operations \\(training/classification;/etc\\)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150616818"}}
{"comment": {"body": "I think this confuses the connection with the client PC and the connection with a specific user.. The state machine is per user.  \nThe connection of the server and the client PC can remain for weeks, and we might have a specific user make many operations: train, train again if first train fails, classify several times..", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150617444"}}
{"comment": {"body": "What\u2019s the trigger of a connect and disconnect for user?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150617507"}}
{"comment": {"body": "Connection and Disconnection events of phone to the lock", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150618850"}}
{"comment": {"body": "I see. Wasn\u2019t aware we send those to the server.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150619116"}}
{"comment": {"body": "They were added as part of this PR :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150619235"}}
{"comment": {"body": "Ok so if we now have connect and disconnect, why don\u2019t we do exactly the same as in WIFI state machine..?  \nWhere we only hold connected users, and disconnected users are not kept in memory?\n\nAnd the first status is JustConnected..?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150620000"}}
{"comment": {"body": "FYI, This PR was merge into ble branch. Hopefully during the data we can also push it the master after further testing of the wifi part.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/134/_/diff#comment-150625533"}}
{"title": "Disconnect from lock on error", "number": 135, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/135", "body": ""}
{"comment": {"body": "What\u2019s this mitigation method trying to fix?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/135/_/diff#comment-150625371"}}
{"comment": {"body": "In general any error currently just causes the app to get stuck in the \u201cauthenticating\u201d screen until manual intervention.\n\nSpecifically:\n\nA cryptic error in one specific phone \\(not the model, but specifically #27\\) makes us unable to read keys from the keystore.  \nThis error was not reproduced on other phones of the same model.  \nThis explains it pretty well: \\(looks like an OS issue\\)\n\n[https://stackoverflow.com/questions/58999536/android-security-keystoreexception-62](https://stackoverflow.com/questions/58999536/android-security-keystoreexception-62)", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/135/_/diff#comment-150625536"}}
{"title": "Persistent DB flag", "number": 136, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/136", "body": ""}
{"comment": {"body": "Can you make the persistent option to be the default?\n\n   \nIn such case, we will need to have just a reset flag.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/136/_/diff#comment-150630184"}}
{"comment": {"body": "Sorry for the delay, didn\u2019t notice the message. Fixed in b4808e7ecf6556e629d77a51b5383b16f75c70c7", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/136/_/diff#comment-150631293"}}
{"title": "Fix tests get history", "number": 137, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/137", "body": "Tests failed because devices disconnected too late and there was not enough time to sample the expected user history. This happens on our jenkins nodes and not necessarily when tests are run natively.\nThis is just a simple temporary solution. Tee based tests are now very prone to be affected by load on executor. This should be fixed when moving to tcpdump based tests by detaching the connection reporting stream from the cfr/radiotap streams."}
{"title": "Print Classification result per feature for the BLE classifier", "number": 138, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/138", "body": ""}
{"title": "Wifi Simulations", "number": 139, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/139", "body": "To see run options, type python sim_802_11.py -h and choose the parameters you like. Then you can choose your favorite and run. If you want to see pix, for example, type:  \npython sim_802_11.py -ps -figs 123"}
{"title": "SCRIPT_DIR SCRIPT_DIR", "number": 14, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/14", "body": "              \n@{5dc7c317ea86a50c6c4c7244}"}
{"title": "Guarantee order of locking so we don't need to sleep", "number": 140, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/140", "body": ""}
{"comment": {"body": "How much effort do you see we invest into the FIFO locking scheme? Would this module be deleted in the near future?\n\nForcing an order on the release of the lock means that there\u2019s a design problem in the threading communication. Using the FIFO hides this problem.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/140/_/diff#comment-150697536"}}
{"comment": {"body": "Following my headline comment, if we keep this module, we want to have it behave like regular Lock model, with proper blocking mechanism and timeouts. Right now, blocking does not work here and there\u2019s no timeout", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/140/_/diff#comment-150697790"}}
{"comment": {"body": "The threading locking scheme is FIFO in the current implementation but they don\u2019t guarantee it will remain the same. So the FIFO on the DB lock is to prevent starvation.   \n  \nIn a second though, I don\u2019t like that we block the db because of writing the debug information. I will remove the write to the DB altogether. ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/140/_/diff#comment-150709444"}}
{"title": "Lock sends disconnection reason to client and CRSSI to server", "number": 141, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/141", "body": ""}
{"comment": {"body": "I don't see the lock sending a DISCONNECT\\_REASON here. Who\u2019s responsible for doing that?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/141/_/diff#comment-150716267"}}
{"comment": {"body": "how is this relevant to the PR?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/141/_/diff#comment-150746806"}}
{"comment": {"body": "You\u2019re right; renamed PR", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/141/_/diff#comment-150747015"}}
{"comment": {"body": "The board does that. It\u2019s in bosch\\_integration", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/141/_/diff#comment-150752427"}}
{"title": "Further extended capabilities feature (to include: Power cap, Supported channels, Vendor", "number": 142, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/142", "body": ""}
{"comment": {"body": "This is not production code, so this probably doesn\u2019t matter, but scapy is slow and you can skip this conversion.  \nScapy has `RawPcapWriter`, but I wouldn\u2019t be surprised to find out that `wrpcap` can handle `bytes` as well.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/142/_/diff#comment-150962386"}}
{"comment": {"body": "WTF happened to phone #86 vs phone #86 in the confusion matrix?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/142/_/diff#comment-150972584"}}
{"title": "Temporary disable background db insertion", "number": 143, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/143", "body": "Disabled background db insertion, as it locks the db and causes timeouts on other actions running (training / classification)\nNo need to run run_server.sh with root to clear db\n\n"}
{"comment": {"body": "So we\u2019re cool with not recording Dor\u2019s tests? I\u2019m not following the decision behind this hotfix, which looks like it\u2019ll cause more trouble with research and root cause analysis", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/143/_/diff#comment-150976889"}}
{"comment": {"body": "Yes. We will fix that later today.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/143/_/diff#comment-150977752"}}
{"comment": {"body": "Michael the background insertion was fixed previously by adding a very small sleep, to allow other processes to get the db sooner. What was the problem with this small sleep? It\u2019s a background process..  \n", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/143/_/diff#comment-150979736"}}
{"comment": {"body": "Instead of commenting out all in classify and initial train processes, can be done in the bg\\_insertion\\_processes in one place.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/143/_/diff#comment-150983390"}}
{"comment": {"body": "That doesn\u2019t really help.. all the operations are stuck", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/143/_/diff#comment-150988238"}}
{"title": "Feature/large scale confusion matrix with cfr", "number": 144, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/144", "body": "This PR mainly does the following:\n\n\nExtend large scale to use also CFRPackets from CFR packets DB\n\n\nSync between the tables based on server timestamp and then merge both table\n\nHotfix the timestamp difference so that RadioTaps and CFRPackets interleave instead of having all of the RadioTaps and then all of the CFRPackets, per query\n\n\n\nIgnore classification timeouts since DB has no-the-best data\n\n\n\n\nMore data for large scale\n\n\nBetter runtime of the whole operation\n\nLoad pickles in the new processes instead of loading in the main process and distributing the data\nImprove SDK_FFT runtime\n\n\n\nAdd replay option for large scale results based on traceback file\n\n\nCurrent runtime is about 4 hours on 48 cores for large_scale_ver2_query. For large_scale_ver1_query, it is ~2.25 hours.\n\nLet me know what yall think"}
{"comment": {"body": "Beep", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/144/_/diff#comment-151567507"}}
{"title": "Ble dash updates", "number": 145, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/145", "body": "Added production mode for the server, some fixes to the UI for BLE.\nSome state machine changes, changed all server time to use UTC time, translation to local time is done on the client side\nUpdated gitignore\nMore bugfixes regarding model deletion\nBugfixes\n\n"}
{"title": "Temporary: save operation trace information into local pickle files", "number": 146, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/146", "body": ""}
{"comment": {"body": "Design-wise, `trace_info.write_operation_trace` and `insert_trace_info_and_packets_to_db` should be the same API, just different backend implementation.\n\nDebugging-traces-wise, I favor DB-solution instead of many-not-indexed-files since files are damn difficult to index and query. When will it change to DB backend?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/146/_/diff#comment-151259295"}}
{"comment": {"body": "When we have time for it..  \nWe just need a quick solution to collect the data at this stage without hitting the system performance", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/146/_/diff#comment-151263326"}}
{"comment": {"body": "This is one of the things that will hurt us deeply if we don\u2019t address it early", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/146/_/diff#comment-151270032"}}
{"comment": {"body": "Mich please also consider re-enabling the db usage and re-testing with a sleep in the bg\\_insertion\\_process.  \nI did test it before and after the change and it did fix the problem.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/146/_/diff#comment-151271189"}}
{"title": "Train error not ending train", "number": 147, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/147", "body": "Ble trainer returned a bad value when an error occurred.\nBLETrainer.ERROR contains a dict of result error and model none, should use it as is."}
{"comment": {"body": "That\u2019s something I fixed some time ago. It doesn\u2019t make sense that the API returns a dict with \u201cresult\u201d key at one time and a `BLETrainer.ERROR` in another time. API should be consistent.\n\nThe API caller doesn\u2019t check the \u201cresult\u201d key in the return value?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/147/_/diff#comment-151351346"}}
{"comment": {"body": "class BLETrainer:\n\n\u2026\n\nERROR = \\{  \n\"result\": Trainer.Result.LEVL\\_TRAINING\\_ERROR,  \n\"model\": None  \n\\}\n\n\u200c\n\nThe BleTrainer.ERROR is a dict", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/147/_/diff#comment-151353107"}}
{"comment": {"body": ":flushed: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/147/_/diff#comment-151354189"}}
{"comment": {"body": " :slight_smile: No worries, the result conversions are confusing", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/147/_/diff#comment-151355596"}}
{"title": "Force App User to Enable Location Services", "number": 148, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/148", "body": "Added checking and monitoring of location setting status. User must enable location to use app."}
{"title": "Don't default server poorly (or at all), grp required (force user to enter --server)", "number": 149, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/149", "body": ""}
{"title": "Fix radiotap, add prioritization mechanism", "number": 15, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/15", "body": ""}
{"title": "bugfix CCP-109 - Enum values shouldn't be tuples", "number": 150, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/150", "body": ""}
{"title": "Connection Resilience w/ Remote Server", "number": 151, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/151", "body": "This is a possible solution to issues where iq samples from lock werent handled by script because it submit was blocked on put. This ultimately caused a connection failure.\nIt seems to solve these issues as far as I tested, and I even noticed some events where packets werent released from queue and the non-blocking put allowed the queue to keep aggregating packets, that were eventually sent to the server."}
{"title": "Working with new AdvertisingSet API seems to solve advertisement issues", "number": 152, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/152", "body": ""}
{"comment": {"body": "Are we connectable?\n\nEdit: nvm just realised it matches the old behavior", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/152/_/diff#comment-151876698"}}
{"comment": {"body": "We shouldn\u2019t be connectable.  \nActually making it not connectable decreases the size of the packets by 2 bytes, so we need to change the embedded app as well. It\u2019s better to do it in a separate PR.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/152/_/diff#comment-151893319"}}
{"comment": {"body": "![](https://bitbucket.org/repo/8X5z9dk/images/1693794722-image.png)\n\u200c\n\nIf my understanding is correct, this will not run well on Android 9.0 and lower?\n\n![](https://bitbucket.org/repo/8X5z9dk/images/3857820673-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/152/_/diff#comment-151967170"}}
{"comment": {"body": "Hmm  \nJust checked and the actual API features we are using were introduced in api 26, so we actually can compile as at version 26 and it should be fine.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/152/_/diff#comment-151974241"}}
{"comment": {"body": "Well It\u2019s looks like it\u2019s not the API change that did the trick but stopping the advertisements before they reach their timeout :man_facepalming: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/152/_/diff#comment-152013190"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} The API change makes the app incompatible with Android versions < 8. If that\u2019s reasonable, please note that we have devices with older Android versions \\(for ex. those that caused [CCP-104](https://jira.levl.tech/browse/CCP-104)\\). If not, should we use the old API anyway?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/152/_/diff#comment-152015082"}}
{"title": "Remove user bug CCP 107", "number": 153, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/153", "body": "Fixed remove user bug CCP-107\nFixed device type flow \nBLE training now takes 1 minute (cancel model verification and down to 600 packets)\nSplit configuration to two dicts\n\n"}
{"title": "Moved registering of mLocationSwitchStateReceiver() to onResume()", "number": 154, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/154", "body": "On certain scenarios the app crashed on pause because the receiver-to-be-unregistered wasnt registered."}
{"title": "Add min and max for specific tuples in duration feature", "number": 155, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/155", "body": ""}
{"title": "Feature/CCP-112 wifi bit in ble v2", "number": 156, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/156", "body": "Set wifi bit in android app, masked in LSB of pattern.\n\ndocker volume instead of directory\n\nBecause of Windows reasons. IDK. Internet told me to do it and it works this way.\n\n\n\nIQ stream is not in endless loop\n\n\n"}
{"title": "Feature/CCP-115 complete table of configurations", "number": 157, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/157", "body": "debug CFO model in training\nno unused members in preconfiguration\nComplete wifi table (for BLE) with samsungs, LGs and Pixels\n\n"}
{"title": "Raspi deploy", "number": 158, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/158", "body": "\n\nRaspberry Pi deploy script\nIQ stream parsing and CFR stream parsing now share a lot of code\n\nAdded script to deploy (rsync.sh ) our code on the Raspberry Pi. Once its deployed, you should ssh into the Raspberry Pi and ./run.sh -s Server it.\n./run.sh builds and launches a Docker container that runs the comcastpi.py script. It runs the container with the --restart always flag which causes the container to automatically run after a reboot. This makes sure the Raspberry Pi will always attempt to reconnect to the server after being disconnected/connected from power.\nBonus changes:\nAffix Streams\ncomcastpi.py will now send the IQ stream in this format:\nb'LEVL_IQ' + pickle.dumps(iq_dict) + b'QI_LVEL'\nThe reason for this change is that I wanted to send empty IQ packets every second to trigger the comcastpi.py to detect server disconnections (TCP in Python problematic in that sense, data needs to be sent to detect disconnections) whenever theres no real IQ. The problem was that the current IQ parsing code didnt really like me doing that. I tried to change it to support empty IQ but then I realized its mostly a duplicate of the CFR stream parsing so I just extracted the PREFIX  POSTFIX parsing to a more generic affix_stream module that can be used by both IQ and CFR.\nrun_server.sh bug\ndocker volume rm docker_postgres-data which makes total sense and was added by @{5b41d9de10d57114135eca66} for Windows support (:nauseated_face:) was a bit too strict and failed to run when the volume didnt exist. Added || true to overcome issue.\nlogs and keys\nThe comcastpi.py polluted its own directory with logs and keys and that was causing me issues with the Docker build script (which copies the entire rpi_smartlock directory). Moved them to a different directory.\nStill unimplemented, will be added in future pull requests:\nLinux will hand out a kinda random /dev/ttyUSB* to our Dialog board. We need the comcastpi.py script to automatically detect the correct tty in case it changes."}
{"comment": {"body": "I\u2019ll also generate a Raspberry Pi image we can just flash on SD cards with our codes ready for deployment once it\u2019s more tested and we feel confident in it.\n\nIn any case the image will always attempt to connect to our VPN network \\(just figured out while typing this that it might be problematic security-wise so maybe we\u2019ll have it in its own, isolated, subnet\\) the moment it gets access to the internet. This will allow us to SSH into it and upgrade it \\(using `rsync.sh`\\) with any new code we\u2019ll develop.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/158/_/diff#comment-152057215"}}
{"comment": {"body": "yo dawg, I heard you like iq\\_parser", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/158/_/diff#comment-152114569"}}
{"comment": {"body": "Good job!", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/158/_/diff#comment-152114692"}}
{"comment": {"body": ":man_shrugging: ", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/158/_/diff#comment-152301477"}}
{"title": "android app sends beacon id", "number": 159, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/159", "body": "comcastpi handle beacon id.\nThis does not completely fix the timing feature"}
{"title": "Removed SSH tunnel in favour of VPN", "number": 16, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/16", "body": "This minor change increase system performance by removing the SSH tunnel from the router to the system in favour of the VPN.\nThat means that:\n\nCommands run by the system no longer pass through 2 layers of SSH key exchange and 2 layers of TCP (the VPN session is persistent and uses UDP) - much faster and fewer round-trips\nThere is no longer need to run recsys_tunnel.sh on the router. (The system pings the router to make sure it is still up instead of detecting the tunnel)\n\nScheduler ticks also should feel snappier.\n"}
{"title": "Add initial auth0 integration to ble", "number": 160, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/160", "body": "New login process:\n\nApp logs in using auth0. Sends token to our auth server. Receives key\nApp tries to authenticate with lock\nLock challenges app.\nLock verifies the challenge response with the auth server\n\nGoes with "}
{"comment": {"body": "We have a postgresql DB already. What\u2019s the reason for using a different DB provider?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/160/_/diff#comment-152174344"}}
{"comment": {"body": "PGQL requires a lot of setup and is overkill for this use case.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/160/_/diff#comment-152174589"}}
{"title": "Fixed CCP-117: Allow adding of models to db after removal from UI", "number": 161, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/161", "body": ""}
{"title": "Updated app version", "number": 162, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/162", "body": ""}
{"title": "Fixed signed ID issue", "number": 163, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/163", "body": ""}
{"title": "Fixed signing and multiple logins", "number": 164, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/164", "body": "Fixed issue preventing multiple login attempts without restarting the app.\nTweaked gradle script to sign the app properly"}
{"title": "Feature/CCP-121 complete preconfiguration flow", "number": 165, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/165", "body": "Fix wifi table entry\nSome more debugging\n\n"}
{"title": "Flash dialog via pi", "number": 166, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/166", "body": "DO NOT ATTEMPT TO REVIEW THIS PR\nReview this commit instead: 3377b21284b7a2cfa8ac33da96c4f9fe0ab5bb43\n\nAdded script to flash binaries to Dialog via Raspberry Pi\n\nThe rest of the diff in this PR is simply files imported from the Dialog SDK and other JLink related stuff. Uninsteresting: cec1d1d77b18b08097441bf5d9471f3991684b67\n"}
{"title": "usbo{n,ff}", "number": 167, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/167", "body": ""}
{"title": "Added dockerfile for the auth server", "number": 168, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/168", "body": ""}
{"comment": {"body": "most of our containers moved to docker-compose. What\u2019s the reason, if any, for not using docker-compose here?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/168/_/diff#comment-152549153"}}
{"comment": {"body": "@{5b41d9de10d57114135eca66} missed your comments, writing a new PR", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/168/_/diff#comment-152552404"}}
{"comment": {"body": "It was one less file to write", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/168/_/diff#comment-152552629"}}
{"title": "Connect AD users to model built in classification server.", "number": 169, "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/169", "body": "Now running both authentication, both of the user credentials and the fingerprint verification on the server and classification is failed if one of them fails."}
{"comment": {"body": "Did you give up on enforcing a single user?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/169/_/diff#comment-152561429"}}
{"comment": {"body": "Still haven\u2019t addressed that.", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/169/_/diff#comment-152561907"}}
{"comment": {"body": "I have fixed the SQL escaping issues. Please re-review", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/169/_/diff#comment-152597636"}}
{"comment": {"body": "`parameters` is unused?", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/169/_/diff#comment-152599639"}}
{"comment": {"body": "effectively only 1 command can be executed with params, with the current implementation", "htmlUrl": "https://bitbucket.org/levl/comcast/pull-requests/169/_/diff#comment-152599947"}}
