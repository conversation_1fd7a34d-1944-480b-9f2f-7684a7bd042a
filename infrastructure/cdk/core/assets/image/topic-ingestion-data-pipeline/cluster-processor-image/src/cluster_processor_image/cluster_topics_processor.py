import json
import logging

from langchain.chains.base import Chain
from langchain.chains.summarize import load_summarize_chain
from langchain.schema import Document
from bertopic.vectorizers import ClassTfidfTransformer
from langchain import PromptTemplate
from langchain_community.chat_models import Chat<PERSON>penAI
from pandas import DataFrame
from sklearn.feature_extraction.text import CountVectorizer
from tqdm import tqdm
from typing import List

from cluster_processor_image.cluster_types import TopicClusterResultItem
from cluster_processor_image.dataframe_constants import (
    DOC_COLUMN,
    CLUSTER_COLUMN,
    CLUSTER_TOPICS_COLUMN,
    DOC_ID_COLUMN,
    DOC_TOPICS_COLUMN,
)
from cluster_processor_image.openai_constants import OPENAI_API_KEY, DEFAULT_OPENAI_CHAT_MODEL, DEFAULT_MAX_TOKENS
from cluster_processor_image.cluster_constants import CLUSTER_MAX_DOCUMENTS


class ClusterTopicsProcessor:
    __max_docs: int

    def __init__(self, max_docs: int = CLUSTER_MAX_DOCUMENTS):
        self.__max_docs = max_docs

    def truncate_docs(self, docs: List[str]) -> List[str]:
        return docs[: self.__max_docs]

    def process_cluster_topics(self, df: DataFrame) -> DataFrame:
        pass


class OpenAIClusterTopicsProcessor(ClusterTopicsProcessor):
    _llm_chain: Chain

    _topic_prompt = """
A TOPIC must have the following characteristics:
1. It must be a system-level component or theme.
2. It must have a maximum of 2 words.
3. It should be descriptive and non-technical.
4. It should not be a verb, and if it is a verb, convert it to a noun.
5. If it is camelCase or PascalCase, convert it to a space-separated word. Ensure that the resulting word is in a format where each word is separated by a space and represents a meaningful component of the original word.

These are examples of a TOPIC:
1. Amazon Glacier
2. Channel Assembly
4. Topic Cluster
3. SES

These are examples of what is NOT a TOPIC;
1. aws-java-sdk-v2
2. software.amazon.awssdk:sts
3. vscode.WebviewMaker
4. ghsa-8cf7-32gw-wr33
5. v2.2.3
6. maxUnavailable
7. vscode.serviceProvider
"""

    _topic_doc_prompt = """
A DOCUMENT is a representation of source code with the following structure:
FILE_PATH: <FILE PATH>
FILE_NAME: <FILE NAME>
CONTENT: <FILE CONTENTS>
"""

    _topic_doc_ids_prompt = """
A TOPIC doc_id is an identifier for a DOCUMENT that a TOPIC originated from.
"""

    _topic_keywords_prompt = """
A TOPIC keyword is a variation or a permutation of a TOPIC that would help in searching for a TOPIC in a body of text.

These are examples of TOPIC to keyword mappings:
"Source Code management" -> ["Source Code",  "SourceCode"]
"PostgresQL" -> ["PostgresQL", "pgsql", "database"]
"Redis" -> ["Redis", "Database cache"]
"""

    _topic_summary_prompt = """
A TOPIC summary has the following characteristics:
1. It describes the TOPIC in a condensed format with several sub-themes.
2. It must take into account the OVERALL UNDERSTANDING and OBJECTIVES for that TOPIC.
3. It does not provide an introduction or a conclusion, it only describes the TOPIC.
4. It does not mention the word "topic" or "document" or "documents" when describing the TOPIC.
"""

    _map_prompt = f"""Please analyze the provided DOCUMENT, and extract the main topics or functionalities it represents.

{_topic_doc_prompt}

Follow these steps:
1. Identify the high-level OBJECTIVES that are evident in the code and the OVERALL UNDERSTANDING of the code's purpose and the topics it addresses.
2. From the OVERALL UNDERSTANDING and OBJECTIVES, provide a list of TOPICS that best describe them in the TOPICS section.
3. Output the DOCUMENT FILE_ID into the DOC_ID section.

{_topic_prompt}

The output should be this JSON template:
{{{{
OVERALL UNDERSTANDING
OBJECTIVES
TOPICS
DOC_ID
}}}}

DOCUMENT:
"{{text}}"
    """

    _combine_prompt = f"""Given the following ordered lists of SOURCE topics delimited by triple backquotes, extract topics or themes that are shared across the lists making sure there are no duplicates.

{_topic_prompt}

{_topic_doc_ids_prompt}

Follow this process:
1. Before extracting topics, look at the all the SOURCE topics and deduplicate SOURCE topics that have similar names, or are lexically similar, or are similar in substance or meaning.
2. Ensure that you group the doc_ids together during deduplication.
3. Ensure that you merge summaries together during deduplication.
4. After extracting topics, ensure each EXTRACTED topic contains no more than 2 words, and if it does not, summarize the EXTRACTED topic into 2 words.
5. Prioritize EXTRACTED topics that include unique terms you are unfamiliar with.
6. Show EXTRACTED topics with descending relevancy scores using a floating point number between 0 and 1.
7. Output the list of EXTRACTED topics as a JSON array with keys, "name" and "score" and "doc_ids".
8. Relevancy refers to how well a topic is represented across all the topics.
9. If two topics share a relevancy score, disambiguate them by a finer precision floating point number.

SOURCE TOPICS:
```{{text}}```

For the response:
1. Output the EXTRACTED topics using the following JSON schema:
{{{{topics: [{{{{"name": <topic>, "score": <score>, "doc_ids": <doc_ids>}}}}] }}}}
2. Do not provide an introduction or a conclusion, only provide the JSON array of EXTRACTED topics satisfying the schema.
3. Limit the size of doc_ids array to a maximum of 10 entries.

EXAMPLE JSON OUTPUT (Up to 30 EXTRACTED topics):
{{{{
topics: [{{{{"name": "Topic1", "score": .8, "doc_ids": [1, 2, 4]}}}}, {{{{"name": "Topic2", "score": .5, "doc_ids": [1, 2]}}}}]
}}}}
"""

    _combine_prompt_template = PromptTemplate(template=_combine_prompt, input_variables=["text"])
    _map_prompt_template = PromptTemplate(template=_map_prompt, input_variables=["text"])

    def __init__(self, openai_api_key: str = OPENAI_API_KEY, max_docs: int = CLUSTER_MAX_DOCUMENTS):
        super().__init__(max_docs=max_docs)
        self._chat_openai_llm = ChatOpenAI(
            openai_api_key=openai_api_key,
            model=DEFAULT_OPENAI_CHAT_MODEL,
            temperature=0,
            max_tokens=DEFAULT_MAX_TOKENS,
            response_format="json",
        ).bind(response_format={"type": "json_object"})
        self._llm_chain = load_summarize_chain(
            llm=self._chat_openai_llm,
            chain_type="map_reduce",
            map_prompt=self._map_prompt_template,
            combine_prompt=self._combine_prompt_template,
            verbose=True,
        )

    def get_cluster_topics(self, df: DataFrame) -> DataFrame:
        for c in tqdm(df[CLUSTER_COLUMN].unique(), desc="Generating cluser topics..."):
            docs = [
                f"####\n{dfc[DOC_COLUMN]}" for dfc in df.query(f"{CLUSTER_COLUMN} == {c}").to_dict(orient="records")
            ]
            truncated_docs = self.truncate_docs(docs=docs)
            llm_docs = [Document(page_content=truncated_doc) for truncated_doc in truncated_docs]
            results = self._llm_chain.run(input_documents=llm_docs, token_max=5000)
            df.loc[df[CLUSTER_COLUMN] == c, CLUSTER_TOPICS_COLUMN] = results

            logging.info(f"Cluster {c} Results: {results}")
            cluster_topics = TopicClusterResultItem.from_json(results)

            # Dictionary to store mapping of doc_ids to topic item names
            doc_ids_to_topics = {}

            # Iterate over the list and populate the dictionary
            for item in cluster_topics:
                for doc_id in item.doc_ids:
                    if doc_id not in doc_ids_to_topics:
                        doc_ids_to_topics[doc_id] = []
                    doc_ids_to_topics[doc_id].append(item.name)

            for doc_id, topics in doc_ids_to_topics.items():
                # Convert to str as Pandas cannot handle variable sized arrays
                if topics:
                    df.loc[df[DOC_ID_COLUMN] == doc_id, DOC_TOPICS_COLUMN] = json.dumps(topics)

        return df


class TfidfClusterTopicsProcessor(ClusterTopicsProcessor):
    def get_cluster_topics(self, df: DataFrame) -> DataFrame:
        documents_per_cluster = df.groupby([CLUSTER_COLUMN], as_index=False).agg({DOC_COLUMN: " ".join})
        count_vectorizer = CountVectorizer(stop_words="english")
        count_matrix = count_vectorizer.fit_transform(raw_documents=documents_per_cluster.doc)
        count_words = count_vectorizer.get_feature_names_out()
        ctfidf = (
            ClassTfidfTransformer(bm25_weighting=True, reduce_frequent_words=True).fit_transform(count_matrix).toarray()
        )
        words_per_cluster = {
            cluster_id: [count_words[index] for index in ctfidf[cluster_id].argsort()[-30:]]
            for cluster_id in documents_per_cluster[CLUSTER_COLUMN]
        }
        df[CLUSTER_TOPICS_COLUMN] = df[CLUSTER_COLUMN].map(lambda cluster_id: words_per_cluster[cluster_id][:])
        return df


class CountClusterTopicsProcessor(ClusterTopicsProcessor):
    def get_cluster_topics(self, df: DataFrame) -> DataFrame:
        documents_per_cluster = df.groupby([CLUSTER_COLUMN], as_index=False).agg({DOC_COLUMN: " ".join})
        count_vectorizer = CountVectorizer(stop_words="english")
        count_matrix = count_vectorizer.fit_transform(raw_documents=documents_per_cluster.doc)
        count_vectors = count_matrix.toarray()
        count_words = count_vectorizer.get_feature_names_out()
        words_per_cluster = {
            cluster_id: [count_words[index] for index in count_vectors[cluster_id].argsort()[-50:]]
            for cluster_id in documents_per_cluster[CLUSTER_COLUMN]
        }
        df[CLUSTER_TOPICS_COLUMN] = df[CLUSTER_COLUMN].map(lambda cluster_id: words_per_cluster[cluster_id][:])
        return df
