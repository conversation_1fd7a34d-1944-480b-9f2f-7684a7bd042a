{"comment": {"body": "Transcription is asynchronous to thread creation.\r\nDoes this get called on thread DAO changes?\r\nWe currently are refreshing the Thread modifiedAt once transcription is completion.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#discussion_r1026965967"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#pullrequestreview-1186924759", "body": ""}
{"comment": {"body": "We'll need to fire an index event once VideoTranscriptionDAO.content is set (i.e. once we have results). That'll come in a follow up PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#discussion_r1026966409"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#pullrequestreview-1186926855", "body": ""}
{"comment": {"body": "Lmk if you have thoughts about the best place for that. My guess is its going to be somewhere in the transcription serivce.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#discussion_r1026967019"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#pullrequestreview-1186929525", "body": ""}
{"comment": {"body": "If you do that, I'm going to have to add access to the search index hooks queue.\r\nProbably VideoTarnscriptionModelService.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#discussion_r1026968799"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#pullrequestreview-1186929696", "body": ""}
{"comment": {"body": "Yeah we'll need to do that", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#discussion_r1026968913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#pullrequestreview-1186930088", "body": ""}
{"comment": {"body": "Anyways, this PR adds logic to the event handler but we still need to add logic that fire the events.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#discussion_r1026969183"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#pullrequestreview-1186936850", "body": ""}
{"comment": {"body": "handsome.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#discussion_r1026973280"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3720#pullrequestreview-1186937035", "body": ""}
{"title": "Search indexing from transcription service", "number": 3721, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3721"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3721#pullrequestreview-1186935054", "body": ""}
{"title": "Deploy source agent", "number": 3722, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3722"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3722#pullrequestreview-1186967006", "body": ""}
{"comment": {"body": "lol, you actually added it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3722#discussion_r1026989918"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3722#pullrequestreview-1186967910", "body": ""}
{"title": "Scoring updates + BERT investigations", "number": 3723, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3723"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3723#pullrequestreview-1186962648", "body": ""}
{"title": "Emit search index event once transcription is done", "number": 3724, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3724", "body": "Part 2 of https://github.com/NextChapterSoftware/unblocked/pull/3721"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3724#pullrequestreview-1186963382", "body": "handsome is, as handsome does. Thanks."}
{"title": "Fix horizontal scroll view bug", "number": 3725, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3725", "body": "The topic pill horizontal scroller had a bug where occasionally, when you scrolled to the right of the view, the right edge would continue to display with a faded edge and the right chevron button.  The problem is that the scroll region can have sub-pixel widths -- this is a quick fix to ensure we've scrolled at least one pixel away from the right side."}
{"title": "Allow triggering search re-indexing for an individual thread", "number": 3726, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3726"}
{"title": "optimize web builds", "number": 3727, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3727"}
{"title": "Updated scoring + BERT investigations", "number": 3728, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3728", "body": "The previous PR had some old updates in a previous commit that changed the stop words. Sorry for the need to reapprove."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3728#pullrequestreview-1187012666", "body": ""}
{"title": "Fixes disappearing window bug during permissions dialog", "number": 3729, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3729", "body": "Downside: the video app will now appear in the dock and have its own menu bar. Not the end of the world because the user won't be able to get themselves into trouble, but not ideal"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3729#pullrequestreview-1187141337", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3729#pullrequestreview-1187141366", "body": ""}
{"comment": {"body": "I removed the storyboard so now this is necessary to boot the app", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3729#discussion_r1027161684"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3729#pullrequestreview-1187141419", "body": ""}
{"title": "need this to fix CI", "number": 373, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/373", "body": "\nLast run of CI/CD failed because of this missing permission."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/373#pullrequestreview-886294015", "body": ""}
{"title": "Fix incremental sourcemark calc when files have moved", "number": 3730, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3730", "body": "Addresses these:\n- \n- \n- "}
{"title": "Ensure transcription service aborts on error", "number": 3731, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3731", "body": "There were a few problems when we added search indexing support on transcriptions:\n\nWe were not using the correct permission set to access the search indexing queue.\nWe were swallowing transcription service exceptions such that the service was not properly aborting and not indicating a major problem."}
{"title": "Standardize code", "number": 3732, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3732", "body": "Remove code dupe"}
{"title": "Fix unread styling", "number": 3733, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3733"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3733#pullrequestreview-1188771501", "body": ""}
{"title": "Don't index closed pull requests", "number": 3734, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3734", "body": "The code changes weren't landed so we shouldn't return these in search results."}
{"title": "Start camera and mic by default", "number": 3735, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3735"}
{"title": "Styling fixes for dashboard topics", "number": 3736, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3736"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3736#pullrequestreview-1188829090", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3736#pullrequestreview-1188832730", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3736#pullrequestreview-1188833340", "body": ""}
{"title": "Save the last selected camera and mic choice", "number": 3737, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3737"}
{"title": "ingest image links", "number": 3738, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3738"}
{"title": "Handle slack rate limiting", "number": 3739, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3739", "body": "Slack has thoroughly thought of rate limiting when generating their clients.\n"}
{"title": "ah forgot this one", "number": 374, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/374"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/374#pullrequestreview-886309517", "body": ""}
{"title": "Fix chrome slurps", "number": 3740, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3740"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3740#pullrequestreview-1188961732", "body": ""}
{"title": "Fixes annoying menu dismissal bug on the video preview window", "number": 3741, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3741"}
{"title": "Add context menu to dashboard pr view and fix vscode tab focus", "number": 3742, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3742", "body": "Add context menu UI item to the PR view\nSet up UI architecture but will need API to send back the links instead of constructing them on the clients (see: )\n\n\nFix vscode editor focus preservation (see: )"}
{"comment": {"body": "Someone means business...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3742#issuecomment-1322818126"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3742#pullrequestreview-1189081979", "body": ""}
{"title": "Auth tokens can have multiple scoped resources", "number": 3743, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3743", "body": "Motivation is for sourcemark cloud auth, which needs long-lived access to multiple resources.\n\nImpact: Tiny chance that an in-flight long-lived token will be invalidated. So if somebody is\ncurrently streaming a video while this change goes live, then the auth request will be rejected."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3743#pullrequestreview-1189051799", "body": ""}
{"comment": {"body": "This will break existing clients I think?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3743#discussion_r1028600330"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3743#pullrequestreview-1189066128", "body": ""}
{"comment": {"body": "Yes, called out in PR description. Will only affect people who happen to be watching a video while the change is deployed. I could make more reslient, but don't think it's worth guarding against.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3743#discussion_r1028611445"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3743#pullrequestreview-1189066316", "body": ""}
{"title": "Sentry Releases for web and vscode", "number": 3744, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3744", "body": "This pr does sentry releases and ensures that we get metrics associated with exceptions etc. per release.\nAlso, uploads sourcemaps so that stack traces can be correctly disambiguated.\n\nTested a web deployment and validated."}
{"title": "Segregate web from vscode", "number": 3745, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3745"}
{"comment": {"body": "good idea", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3745#issuecomment-1322803872"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3745#pullrequestreview-1189067231", "body": ""}
{"title": "Persist repo isPublic field and show in adminweb", "number": 3746, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3746", "body": "Source agent can only run on public repos. So we need this to know if we can run the source agent."}
{"title": "Admin web button to trigger sourcemark recalculation on public repo", "number": 3747, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3747", "body": ""}
{"title": "Fix word stemming in dev and prod", "number": 3748, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3748", "body": "The default stemming dictionary used in dev & prod is simple. Let's specify the dictionary to use so that input words are stemmed during indexing."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3748#pullrequestreview-1189106948", "body": ""}
{"title": "Increase screen capture error timeout to 2 seconds", "number": 3749, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3749"}
{"title": "Update", "number": 375, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/375"}
{"title": "Add SQS infra config for sourcemark repo recalculation events", "number": 3750, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3750"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3750#pullrequestreview-1190627581", "body": "Part of this will be deployed by CI. I will need to push the EKS portion locally."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3750#pullrequestreview-1190627790", "body": ""}
{"title": "Remove unnecessary queue access from DEV telemetry/auth services", "number": 3751, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3751"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3751#pullrequestreview-1190617919", "body": ""}
{"title": "Remove unnecessary queue access from PROD telemetry/auth services", "number": 3752, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3752"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3752#pullrequestreview-1190624948", "body": "I'll need to push these locally."}
{"title": "Remove code dupe", "number": 3753, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3753"}
{"title": "Order related topics by frequency of occurence", "number": 3754, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3754", "body": "First stab at a better ordering of the topic pills in VS code"}
{"title": "create search indexing priority queue", "number": 3755, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3755", "body": "Added search indexing priority queue. It doesn't need any EKS/IAM permission changes since we are using wildcards to specify queue names in service account configs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3755#pullrequestreview-**********", "body": "thanks!"}
{"title": "Consolidate eslint configs", "number": 3756, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3756", "body": "We had 6 eslint config files and inheritance was broken\nNow one file"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3756#pullrequestreview-**********", "body": ""}
{"title": "fix search indexing deadletter queues", "number": 3757, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3757", "body": "Ops forgot this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3757#pullrequestreview-**********", "body": ""}
{"title": "Fix lint after fix lint config", "number": 3758, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3758", "body": "Need help with this  \n\n[x] shared\nwasn't able to address Unexpected any, so these are still warnings; 26 of them in shared.\n[x] source-agent\n[x] vscode\n[x] web\n[x] web-extension"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3758#pullrequestreview-1190721366", "body": ""}
{"comment": {"body": "@kaych is what I did here correct?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3758#discussion_r1029783433"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3758#pullrequestreview-1190722670", "body": ""}
{"comment": {"body": "We have 26 of these in `shared` -- too many for me to fix, so explicitly classifying as warn. Note that these were warnings before, so no actual change in behaviour.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3758#discussion_r1029784433"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3758#pullrequestreview-1190726174", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3758#pullrequestreview-1190728124", "body": ""}
{"title": "Fix deadletter queue", "number": 3759, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3759"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3759#pullrequestreview-1190641481", "body": ""}
{"title": "Github Actions now has support for local path references!", "number": 376, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/376", "body": "No more having to deal with branch bullshit whne using resusable workflows or actions.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/376#pullrequestreview-886352238", "body": ""}
{"title": "Partial revert: allow auth service to access slack queues", "number": 3760, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3760", "body": "See dependency in projects/services/authservice/src/main/kotlin/com/nextchaptersoftware/authservice/Module.kt"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3760#pullrequestreview-1190680776", "body": ""}
{"title": "Order topics based on filepath", "number": 3761, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3761", "body": "The core problem we're trying to solve here is ordering the topics in a sensible way. Related topics that are most relevant should appear first:\n\nOne very crude but cheap way to infer relevance is to check for the presence of the topic in the file path. \nThis PR adds a new property to the TopicsFilter so that we can order the topics returned by the api service such that topics that appear in the file path appear first. Needs a client side change to supply the file path."}
{"comment": {"body": "Isn't the file path implied by the file you've selected in the IDE?  Ie do we need this to be in the API at all?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3761#issuecomment-1324251926"}}
{"comment": {"body": "Oh I see, I think your aim here is to sort the topics server-side.  I wonder if this data can be filled as part of thread creation and ingestion -- all threads and PRs are ultimately related to files when they are created.  ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3761#issuecomment-1324258318"}}
{"comment": {"body": "@matthewjamesadam we could do this client side, but I guess that would need to be baked into each client.\r\n\r\nAlternatively we could do what you suggest and check the file paths in the source points rather than ask the client to provide one. This would be a no-op for files that don't have threads and only pull requests but maybe that's ok?\r\n\r\nThis is an optional change to the `TopicsFilter` so we can back this out if we want to go with your suggestion.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3761#issuecomment-1324262896"}}
{"comment": {"body": "Closing this for now since https://github.com/NextChapterSoftware/unblocked/pull/3766 has improved it, but we can reopen if we want to try this again.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3761#issuecomment-1329457258"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3761#pullrequestreview-1190797738", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3761#pullrequestreview-1190799825", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3761#pullrequestreview-1190800322", "body": ""}
{"title": "Fix sentry", "number": 3762, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3762"}
{"title": "Connect vscode topics pills to dashboard", "number": 3763, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3763", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3763#pullrequestreview-1190867860", "body": ""}
{"title": "Sort topics by sourcemark filepaths", "number": 3764, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#pullrequestreview-1190843598", "body": ""}
{"comment": {"body": "could be 100s (or 1000s) of points -- do we really need them all?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029872278"}}
{"comment": {"body": "Don't follow this: `filePaths.any` returns a boolean so it doesn't really sort, except at a very coarse-grained level. Not really clear what you're trying to do", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029873227"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#pullrequestreview-1190845676", "body": ""}
{"comment": {"body": "no guarantee that the file exists on the client btw", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029873794"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#pullrequestreview-1190849780", "body": ""}
{"comment": {"body": "yeah probably just the latest point, let me fix", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029876848"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#pullrequestreview-1190850386", "body": ""}
{"comment": {"body": "Yeah just trying a cheap method to sort the topics in a way that moves topics to the front of the list when they appear in a file path.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029877283"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#pullrequestreview-1190852015", "body": ""}
{"comment": {"body": "Where is this called from?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029878392"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#pullrequestreview-1190852357", "body": ""}
{"comment": {"body": "Client/API service", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029878636"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#pullrequestreview-1190852694", "body": ""}
{"comment": {"body": "I mean which client, where in the client. Which view?\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029878854"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#pullrequestreview-1190853533", "body": ""}
{"comment": {"body": "getRelatedTopics operation serving\r\n\r\n<img width=\"315\" alt=\"CleanShot 2022-11-22 at 14 30 11@2x\" src=\"https://user-images.githubusercontent.com/1924615/203434528-9652f649-4186-4c59-9cf7-1e6806e9e665.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029879425"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#pullrequestreview-1190857334", "body": ""}
{"comment": {"body": "chatted irl -- more accurate and efficient to do client side", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3764#discussion_r1029882120"}}
{"title": "Backfill to populate repo visibility on teams", "number": 3765, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3765", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3765#pullrequestreview-1190871526", "body": ""}
{"title": "Set topic score using topic occurence frequency in getRelatedTopics", "number": 3766, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3766", "body": "The client is sorting by score."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3766#pullrequestreview-1190872531", "body": ""}
{"title": "[BREAKS API ON MAIN] Add properties to Topic model", "number": 3767, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3767", "body": "The explorer UI in the dashboard necessitates a TopicInfo wrapper type to return additional data:\n\nTopics should also have a list of experts:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3767#pullrequestreview-1190900409", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3767#pullrequestreview-1190907744", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3767#pullrequestreview-1190936619", "body": ""}
{"title": "Add log line to understand why slack thread isnt being ingested", "number": 3768, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3768"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3768#pullrequestreview-1190939784", "body": ""}
{"title": "Assume message is top level message if it is the only one in the thread", "number": 3769, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3769"}
{"title": "finally fixed it", "number": 377, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/377", "body": "So turns out Kubernetes has combined API_GROUP and VERSION fields into one when running kubectl api-resources -o wide. RABC only cares about API group and I had to remove the version"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/377#pullrequestreview-886356791", "body": ""}
{"title": "Plumb sourcemark event parameters from event to agent", "number": 3770, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#pullrequestreview-1190980621", "body": ""}
{"comment": {"body": "@mahdi-torabi much easier for me to use specific the short image name as opposed to what you have here:\r\nhttps://www.notion.so/Kube-SQS-Job-Controller-7681556783b9486cbf0f7a1281c3fdeb?d=94a623ce473f4e71a64f3742418b9c86#84cc499872e44e8191eeafb386e2679b", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#discussion_r1029975982"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#pullrequestreview-1190980801", "body": ""}
{"comment": {"body": "As per Queue Task Spec here:\r\nhttps://www.notion.so/nextchaptersoftware/Kube-SQS-Job-Controller-7681556783b9486cbf0f7a1281c3fdeb#84d7e2e788044e61b6cda567b6ac2948", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#discussion_r1029976146"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#pullrequestreview-1190982982", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#pullrequestreview-1190985327", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#pullrequestreview-1190993567", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#pullrequestreview-1191241161", "body": ""}
{"comment": {"body": "We can still make the short name work but it requires some changes to our CI-CD pipeline to tag images or maintain a latest-dev latest-prod pointers ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#discussion_r1030164280"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#pullrequestreview-1191242854", "body": ""}
{"comment": {"body": "I'd say for the time being we can do `source-agent:{image_tag}`._ I can take care of adding image tag to pod environment variables for all services \n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#discussion_r1030165532"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#pullrequestreview-1191916137", "body": ""}
{"comment": {"body": "Changed here: https://github.com/NextChapterSoftware/unblocked/pull/3779", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3770#discussion_r1030624401"}}
{"title": "ActiveMQ cdk", "number": 3771, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3771"}
{"title": "ActiveMQ passwords", "number": 3772, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3772"}
{"title": "UpdateEKS", "number": 3773, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3773", "body": "Update eks\nUpdate eks\nUpdate values\nMore values"}
{"title": "Fix infra", "number": 3774, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3774"}
{"title": "Add activemq dependnecy", "number": 3775, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3775"}
{"title": "EnableActiveMQ", "number": 3776, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3776"}
{"title": "Fix build", "number": 3777, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3777"}
{"title": "Fix auth service", "number": 3778, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3778"}
{"title": "Add image tag to sourcemark job spec", "number": 3779, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3779", "body": "Per https://github.com/NextChapterSoftware/unblocked/pull/3770#discussion_r1030165532"}
{"title": "Fix build and rm log", "number": 378, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/378"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/378#pullrequestreview-886387917", "body": ""}
{"title": "Move ot cheaper prod instance", "number": 3780, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3780"}
{"title": "Remove topic limit", "number": 3781, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3781"}
{"title": "Introduce repo request authorization", "number": 3782, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782", "body": "If the token has a repo claim, then we authorize the repo IDs found in the request path and requests query parameters.\nNothing sets the claim right now; but source mark scheduler will in a following change.\nedit"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#pullrequestreview-1192123148", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#pullrequestreview-1192127357", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#pullrequestreview-1192128064", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#pullrequestreview-1192138722", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#pullrequestreview-1192140847", "body": ""}
{"comment": {"body": "This might throw", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#discussion_r1030783009"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#pullrequestreview-1192143851", "body": ""}
{"comment": {"body": "See above: https://github.com/NextChapterSoftware/unblocked/pull/3782#discussion_r1030781427\r\n\r\nNo change in behaviour and intended.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#discussion_r1030785230"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#pullrequestreview-1192144884", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3782#pullrequestreview-1192145319", "body": ""}
{"title": "Fix smaller screen rendering for pr info views", "number": 3783, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3783"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3783#pullrequestreview-1192154298", "body": ""}
{"title": "Basic route for web video composer", "number": 3784, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3784", "body": "Setup basic route for web video composer. Set this up so there's a route hub app can point towards.\nTo be implemented once we introduce a \"VideoMetadataAPI\""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3784#pullrequestreview-1192166476", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3784#pullrequestreview-1192167368", "body": ""}
{"comment": {"body": "fwiw this pattern doesn't actually redirect to index when the flag is false :( it just renders nothing, which is probably fine for now. a user would need to know the exact route to hit this dead end ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3784#discussion_r1030801812"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3784#pullrequestreview-1192168375", "body": ""}
{"comment": {"body": "what's the difference between a videoId and a videoMetadataId ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3784#discussion_r1030802494"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3784#pullrequestreview-1193671172", "body": ""}
{"comment": {"body": "the metadata id is supposed to reference these models here: https://github.com/NextChapterSoftware/unblocked/pull/3789\r\n\r\nThis is before the video walkthrough thread is created.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3784#discussion_r1031884555"}}
{"title": "Delete search entries for closed PRs", "number": 3785, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3785", "body": "Removes closed PRs from search results"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3785#pullrequestreview-1192197206", "body": ""}
{"title": "Remove unused import", "number": 3786, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3786"}
{"title": "Add priority queue support", "number": 3787, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3787", "body": "Priority queue support needs to be enabled via ActiveMQ configuration files.\nNamely:\n       policyEntry queue=\"\" prioritizedMessages=\"true\"/"}
{"title": "Null repoIds parameter means all repos", "number": 3788, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3788"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3788#pullrequestreview-1192304241", "body": "yeah, this is consistent with other apis (eg: getThreads)"}
{"title": "Video Walkthrough draft API", "number": 3789, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192326994", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192329711", "body": ""}
{"comment": {"body": "probably easier to handle on the client/server if required; don't need to handle nulls", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030919402"}}
{"comment": {"body": "missing: `fileHash`, `repoId`, and make them required", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030920436"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192334631", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192366022", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192366156", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192369341", "body": ""}
{"comment": {"body": "This is a copy and paste from the existing `RepoFileReference` type. If there's missing information here we should figure out what is needed @jeffrey-ng ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030943067"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192375702", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192377248", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192396448", "body": ""}
{"comment": {"body": "Based on https://www.notion.so/Proposal-Decouple-OpenAPI-Request-and-Response-types-d1aa260369084655b1a9d1dc684f07ab\r\n\r\nshould this be a request model instead of reusing the response model?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030964329"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192396608", "body": ""}
{"comment": {"body": "Or is that just for Puts, and not posts?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030964452"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192397408", "body": ""}
{"comment": {"body": "2 things:\r\n1. It's just PUTs\r\n2. Going down the Create* route makes the spec messy, so we're opting to do it only when it's necessary to do so", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030965147"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192397590", "body": ""}
{"comment": {"body": "Set to required now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030965282"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192397652", "body": ""}
{"comment": {"body": "In this case, fileHash and repoId aren't necessary since we have a sourceMark.\r\n\r\n@richiebres fileHash and repoId are necessary for protobuf communication between vscode and video app, not API service.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030965354"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192398010", "body": ""}
{"comment": {"body": "All properties should be required.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030965602"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192398783", "body": ""}
{"comment": {"body": "woops good catch", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030966176"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192398881", "body": ""}
{"comment": {"body": "@richiebres We're modelling the Contributor model from `git/contributor.ts` into the API so that we can render the following UI in dashboard. \r\n<img width=\"479\" alt=\"CleanShot 2022-11-23 at 16 44 42@2x\" src=\"https://user-images.githubusercontent.com/1553313/203669859-5267c854-cc5b-4e20-bd36-28cf2e1c5a14.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1030966257"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192542278", "body": ""}
{"comment": {"body": "Where does the SourceMark come from?\r\n\r\nAPI service does require fileHash and repoId.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#discussion_r1031070219"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1192545087", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1193433922", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1193457121", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1193462749", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1193463214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1193469330", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1193475221", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1193495308", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1193495550", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3789#pullrequestreview-1193513289", "body": ""}
{"title": "Try again", "number": 379, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/379"}
{"title": "Add VideoDraftModel", "number": 3790, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3790", "body": "Will add tests in the next PR with the Store implementation"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3790#pullrequestreview-1193545645", "body": ""}
{"comment": {"body": "Bound to person instead of team. Team has not yet been determined when this model is used", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3790#discussion_r1031781212"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3790#pullrequestreview-1193681120", "body": ""}
{"title": "Add insight sort dropdown to topic view", "number": 3791, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3791", "body": "\n\nRefactors existing sorting code used in vscode (the logic was moved into a shared Utils helper)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3791#pullrequestreview-1192366637", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3791#pullrequestreview-1192366885", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3791#pullrequestreview-1192370872", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3791#pullrequestreview-1192371523", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3791#pullrequestreview-1192372067", "body": ""}
{"title": "Move topic mapping logic to its own lib", "number": 3792, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3792"}
{"title": "Add more event configuration", "number": 3793, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3793"}
{"title": "Map topics to threads and pull requests when created", "number": 3794, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3794"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3794#pullrequestreview-1192494828", "body": ""}
{"comment": {"body": "Good stuff :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3794#discussion_r1031035244"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3794#pullrequestreview-1192495066", "body": ""}
{"comment": {"body": "testin, testing..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3794#discussion_r1031035445"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3794#pullrequestreview-1193556857", "body": ""}
{"comment": {"body": "Testing 2222", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3794#discussion_r1031787991"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3794#pullrequestreview-1193594642", "body": ""}
{"comment": {"body": "test", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3794#discussion_r1031814512"}}
{"title": "Move remaining stuff to standard event queues", "number": 3795, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3795"}
{"title": "LeveragePriorityQueues", "number": 3796, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3796"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3796#pullrequestreview-1192471616", "body": ""}
{"comment": {"body": "Testing pr ingestion", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3796#discussion_r1031021051"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3796#pullrequestreview-1192471750", "body": ""}
{"comment": {"body": "test2", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3796#discussion_r1031021162"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3796#pullrequestreview-1192472534", "body": ""}
{"comment": {"body": "test3", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3796#discussion_r1031021547"}}
{"title": "Fix local stack", "number": 3797, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3797"}
{"title": "Auth Service internal API vends token with {team, repo, resource list}", "number": 3798, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3798", "body": "For now admin web service is minting tokens."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3798#pullrequestreview-1192625238", "body": ""}
{"comment": {"body": "Should be removed once admin web is no longer minting auth tokens.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3798#discussion_r1031128291"}}
{"title": "Remove SM agent default fallback parameters", "number": 3799, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3799", "body": "This is driven from events now."}
{"title": "Message & Thread Views", "number": 38, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38", "body": "Basic message view to render string based content and potential snippets.\nMakes little assumptions on the models and props are meant to be temporary until we work on models. \nThe hope is to front-load some of the UI styling work for when we start implementing feature work end to end.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-853477332", "body": ""}
{"comment": {"body": "Fix lint.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r785239710"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-853478336", "body": ""}
{"comment": {"body": "This would be a fun one to write unit tests or chromatic tests for...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r785240559"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-853481090", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-853481987", "body": ""}
{"comment": {"body": "We can mock it out. Updating.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r785243140"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-853487023", "body": ""}
{"comment": {"body": "Handled within .storybook/preview.js for storybook.\r\n\r\nAdded unit test for relative time.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r785247400"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-853488073", "body": ""}
{"comment": {"body": "Cool!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r785248327"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-854720691", "body": ""}
{"comment": {"body": "Is this image actually used in the UI?  Should we host an image instead so we aren't relying on something that the indepdendent might remove?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r786243713"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-854720966", "body": ""}
{"comment": {"body": "Same comment here about external images...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r786243916"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-854721013", "body": ""}
{"comment": {"body": "... and here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r786243942"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-854721081", "body": ""}
{"comment": {"body": "... and here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r786243990"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-854722065", "body": ""}
{"comment": {"body": "Fair. These are just \"mocked\" images that were used within the UserIcon stories.\r\nDo we have a place to host images right now? Anything from October demo domain?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#discussion_r786244674"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-854727240", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-854728467", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/38#pullrequestreview-855726160", "body": ""}
{"title": "Add push channel checking to client data cache", "number": 380, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380", "body": "This adds a check to the getChannelsModifiedSince call to pre-check if data has changed on a channel, before fetching.  In this PR every store will make a separate call to getChannelsModifiedSince.  This includes the following work:\n\nAdded a test UI (under the 'Group conversations' option in the web dashboard) that displays the current set of threads, and a button to add a thread.\nAdd ability for client tests to override the base request URL for mocking\nFix bug where CORS disallowed POST, breaking the channel API\nFix bug where CORS disallowed sending If-Modified-Since, breaking the channel API\nFix bug where the API service interpreted message content as a number array instead of an encoded string\n\nThere is a unit test for DataCacheStore that tests this.\nNext PRs will add:\n* Multiplexing the getChannelsModifiedSince so that we will make a single call for all stores\n* Add a unit test specifically for ThreadStore, with a mock server implementation"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-886683946", "body": ""}
{"comment": {"body": "Note: I'll probably move this stuff out into a separate service mock module in my next PR, which will mock both the Threads and Push APIs at the same time.  They kind of have to be mocked together because they work together as an API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r809561002"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-886684473", "body": ""}
{"comment": {"body": "This is an example of how an API store file might work.  Simple set of traits to define the store, and then mutation functions.  Right now the mutation function does nothing but call through to the API, but it could be hooked into the store to mutate the local data immediately.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r809561442"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-886684718", "body": ""}
{"comment": {"body": "And this shows how to use the store.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r809561640"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-887759655", "body": ""}
{"comment": {"body": "Temporarily replace \"Discovered: Group\" tab with the test UI", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r810341561"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-887790313", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-887796572", "body": ""}
{"comment": {"body": "This type (and the function below) are adapters to merge the `raw` API call return values to what the data cache expects.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r810368752"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-887797124", "body": ""}
{"comment": {"body": "This will eventually handle deletions too, but for now we don't mark deleted objects in the model, so we only handle create/update", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r810369173"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-888941384", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-888942481", "body": ""}
{"comment": {"body": "We should add tests to detect unexpected CORS behaviour", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r811313673"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-888952929", "body": ""}
{"comment": {"body": "Plan for the future is to implement this as a pub/sub so we can multiplex channels?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r811321193"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-890090796", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-890091686", "body": ""}
{"comment": {"body": "Agreed, but this PR has sprawled enough as-is, I'd like to consider that a separate issue for later...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r812161033"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-890092239", "body": ""}
{"comment": {"body": "Yes, I'm working on this right now on a branch off of this PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r812161401"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-890092923", "body": ""}
{"comment": {"body": "(This will be a bit of a project I think .. mimicking browser CORS behaviour is difficult)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r812161873"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-890162822", "body": ""}
{"comment": {"body": "Not necessarily for this PR but we need to think about how to catch exceptions from the network requests.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r812212042"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-890164636", "body": ""}
{"comment": {"body": "Yes, definitely, I've got that in the next PR I'm working on.  The pollers need to catch at the lowest level so that polling doesn't break, but higher levels can handle it too if they need to take a particular action.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r812213347"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-890167170", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-890169990", "body": ""}
{"comment": {"body": "would be nice if we could define parameters with 'inout' like swift.\r\n\r\nWould it make sense to return a new values array instead of mutating the reference?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r812217101"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#pullrequestreview-890207249", "body": ""}
{"comment": {"body": "I don't feel super strongly about this either way since it's effectively a private function used in one place, so the risk of \"using it wrong\" by assuming the mutability behaviour is different is pretty much zero.  It's slightly more efficient this way but I doubt that matters much.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/380#discussion_r812244223"}}
{"title": "Persistant VSCode to Video App Connection", "number": 3800, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800", "body": "VSCode will now always try to connect with video app. This is in preparation for hub-initiated video walkthroughs where VSCode still needs to act as a file mark reference provider.\nFile Marks are no longer created on the fly but generated by the walkthrough initiator with data (file hash, commit hash, repo Id) provided by VSCode during the walkthrough."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-**********", "body": ""}
{"comment": {"body": "not supposed to reuse numerals in photos. supposed to use `reserved`\r\n\r\nunless you are sure that this is safe for all clients...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#discussion_r1031773550"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-**********", "body": ""}
{"comment": {"body": "In this case, the clients are being updated in lockstep so this should be fine?\r\nThere should never really be a mismatch in versions for hub and vscode, especially for this spec.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#discussion_r1031810273"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1193591459", "body": ""}
{"comment": {"body": "yup, sure. in future, we should just burn the numerals; there are lots of them :) ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#discussion_r1031811941"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1193592061", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1193594477", "body": ""}
{"comment": {"body": "So in this case, I would remove sourceMarkId and set fileHash as 7?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#discussion_r1031814389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1193637758", "body": ""}
{"comment": {"body": "Like this:\r\n\r\n```proto\r\nreserved = 2; // was sourceMarkId\r\nstring fileHash = 7;\r\n```\r\n\r\nSee also https://developers.google.com/protocol-buffers/docs/proto3#reserved\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#discussion_r1031849412"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1193689259", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1193690176", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1193716804", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1193717793", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1194715455", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1194721178", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3800#pullrequestreview-1194726945", "body": ""}
{"title": "Add batch queue processing", "number": 3801, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3801"}
{"title": "Visible insights: Extract commits for the visible part of a file", "number": 3802, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3802", "body": ""}
{"comment": {"body": "It looks like you're implementing this by only fetching and returning the items that are within the view, all the way down to the sourcemark engine? It might be easier (for now) for the SM engine to continue to return all items (as it does today) and then have the IDE filter the returned items as needed while scrolling.  It will likely be a lot faster, and I think the code will be a lot more straightforward.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3802#issuecomment-1326808962"}}
{"comment": {"body": "> It looks like you're implementing this by only fetching and returning the items that are within the view, all the way down to the sourcemark engine? It might be easier (for now) for the SM engine to continue to return all items (as it does today) and then have the IDE filter the returned items as needed while scrolling. It will likely be a lot faster, and I think the code will be a lot more straightforward.\r\n\r\n@matthewjamesadam This has nothing to do with sourcemark engine; just a pure Git thing.\r\n\r\nActually, the way this will work is exactly how you are suggesting. We collect the blame information up-front once, then the intention is that the IDE editor selects the commits in the Range:\r\n1. get blame commits by line for a file (note: this returns commit for _all_ lines)\r\n2. IDE will index the array as needed `commitsByLine.slice(start, end)`\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3802#issuecomment-1326887837"}}
{"comment": {"body": "Note that the `commitsByLine.slice(start, end)` will return duplicate commit hashes, since commits can edit multiple lines in the file. One obvious way to address this is to deduplicate `ArrayUtils.distinct( commitsByLine.slice(start, end) )`.\r\n\r\nHowever, I think it might be interesting to use the frequency of the commits in the line range as input to the relevancy engine. For example, if 90% of the visible source lines are due to `CommitA`, then the insights associated with `CommitA` should be ranked higher. Can chat more on Monday when you are back if this is not clear?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3802#issuecomment-1326889173"}}
{"title": "Add video draft store", "number": 3803, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3803"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3803#pullrequestreview-1193705144", "body": ""}
{"title": "Transactional consumers", "number": 3804, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3804"}
{"title": "Remote client config capabilities should be displayed as tristate booleans", "number": 3805, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3805", "body": "One of UNSET | OFF | ON.\n\n"}
{"comment": {"body": "slight nit to order this by `(OFF | UNSET | ON)` ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3805#issuecomment-1326811921"}}
{"comment": {"body": "> slight nit to order this by `(OFF | UNSET | ON)`\r\n\r\nupdated", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3805#issuecomment-1326817150"}}
{"title": "Missed a file update in previous PR", "number": 3806, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3806", "body": "(Botched rebase, lost all these changes somehow from #3805)"}
{"title": "Dont return PR-associated slack threads from getTopicRelatedInsights", "number": 3807, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3807", "body": "A little hacky but this operation is going to be replaced soon."}
{"title": "Add video draft service", "number": 3808, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3808"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3808#pullrequestreview-1193720863", "body": "This pr makes me happy."}
{"title": "Set rollback cause", "number": 3809, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3809"}
{"title": "Fuzzy string compare for detecting moved code and modified code", "number": 381, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/381", "body": "To be used when trying to find a line matching the original text sequence in a changeset."}
{"comment": {"body": "Are you planning on moving forward with a kotlin implementation of some kind?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/381#issuecomment-1043650856"}}
{"comment": {"body": "@matthewjamesadam Nope. This one just happens to also be needed for PR ingestion. Right now we drop all \"outdated\" comments. But we found that often these outdated comments have just been moved a few lines, or subtly edited (typo fix).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/381#issuecomment-1043662117"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/381#pullrequestreview-886689985", "body": "Rockstar"}
{"title": "Allow readonly mode to access GET /config", "number": 3810, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3810", "body": "Missed this when we created the endpoint, so GET /config is currently throwing 401 for the config endpoint when in readonly mode."}
{"title": "Should not be storing redis cluster info in helm charts", "number": 3811, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3811", "body": "No need to be putting thist stuff there"}
{"title": "Reindexing event should be low priority", "number": 3812, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3812"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3812#pullrequestreview-1193707862", "body": ""}
{"title": "Video Draft API Implementation", "number": 3813, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3813"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3813#pullrequestreview-1193724796", "body": ""}
{"comment": {"body": "This is essentially the security check since it's resolved from the identityId in the principal", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3813#discussion_r1031928451"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3813#pullrequestreview-1193725907", "body": ""}
{"title": "Add filter by type dropdown", "number": 3814, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814", "body": "\n\nAdd InsightFilterUtils for filtering by type helpers \nRefactor sorting/filtering dropdowns into own file \nThese filters are done client-side but should ultimately be part of the API"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193721189", "body": ""}
{"comment": {"body": "It is kind of weird the naming is different here.\r\nVideo vs. walkthrough.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#discussion_r1031925785"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193725058", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193725700", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193727346", "body": ""}
{"comment": {"body": "+1", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#discussion_r1031930430"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193727930", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193730284", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193731152", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193738502", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193743923", "body": ""}
{"comment": {"body": "Remove", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#discussion_r1031943798"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1193744080", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1194698821", "body": ""}
{"comment": {"body": "It's mapping the thread type to the label type; it seems we call them videos in the UI so I don't think we should rename the label enum just to match the thread type naming?\r\n<img width=\"388\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/204038111-1fa6b753-abb1-46d1-a667-d35f225193bf.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#discussion_r1032637420"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1194716005", "body": ""}
{"comment": {"body": "IMO, an insight === thread? \r\nTherefore, InsightType === ThreadType?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#discussion_r1032650207"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1194716878", "body": ""}
{"comment": {"body": "not necessarily. a Pull Request is an aggregate of threads but we still classify them as an Insight type \r\n\r\neither way, I think we're comparing the API naming to the UI naming, which won't always match. In this case, ThreadType vs the LabelEnum; it's fine that these aren't 1:1 IMO", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#discussion_r1032650958"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1194718351", "body": ""}
{"comment": {"body": "Just curious. Is there ever a situation where this occurs?\r\n\r\nBased on `filterableTypes`, this would never occur? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#discussion_r1032652103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1194720235", "body": ""}
{"comment": {"body": "I guess not right now. I built a cut of this with an All option before removing.. but I guess I don't see the harm of keeping this to be thorough? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#discussion_r1032653477"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3814#pullrequestreview-1194721060", "body": ""}
{"title": "Event queue should not be waiting for messages", "number": 3815, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3815", "body": "Theres no point in waiting on messages and it affects coroutine timeouts."}
{"title": "Update topic mappings after indexing", "number": 3816, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3816"}
{"comment": {"body": "https://github.com/NextChapterSoftware/unblocked/pull/3823", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3816#issuecomment-1327851223"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3816#pullrequestreview-1193728391", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3816#pullrequestreview-1193728659", "body": ""}
{"comment": {"body": "This is not a long lived operation btw?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3816#discussion_r1031931340"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3816#pullrequestreview-**********", "body": ""}
{"comment": {"body": "No its quick but best to punt to the topic service", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3816#discussion_r1031931470"}}
{"title": "persistent send", "number": 3817, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3817"}
{"title": "Convert to batch processor", "number": 3818, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3818"}
{"title": "Disable health checks for logs", "number": 3819, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3819", "body": "HealthCheck logs are inundating logz.io.\n"}
{"title": "Remove debug lines", "number": 3820, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3820"}
{"title": "Create topic mapping queue", "number": 3821, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3821"}
{"comment": {"body": "Going to try this with a background job instead.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3821#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3821#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Next PR will replace this log line with actual logic", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3821#discussion_r1032642210"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3821#pullrequestreview-**********", "body": "All looks good to me."}
{"title": "Obsolete the deprecated PullRequest descriptionMessage field", "number": 3822, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3822", "body": "Was deprecated in August by Kay.\nDefinitely safe to remove now."}
{"comment": {"body": "Cannot be changed without breaking clients. All of these APIs would need to be versioned.\r\n\r\n```\r\n--------------------------------------------------------------------------\r\n--                            What's Changed                            --\r\n--------------------------------------------------------------------------\r\n- GET    /teams/{teamId}/threads/mine\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].pullRequest.description (object)\r\n- GET    /teams/{teamId}/threads/byPullRequests\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].pullRequest.description (object)\r\n- POST   /teams/{teamId}/threads/byCommit\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].pullRequest.description (object)\r\n- GET    /teams/{teamId}/pullRequests/{pullRequestId}/threads\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].pullRequest.description (object)\r\n- POST   /teams/{teamId}/threads\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].pullRequest.description (object)\r\n- GET    /teams/{teamId}/threads/recommended\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].pullRequest.description (object)\r\n- GET    /teams/{teamId}/threads/archived\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].pullRequest.description (object)\r\n- GET    /teams/{teamId}/threads/search\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].pullRequest.description (object)\r\n- GET    /teams/{teamId}/threads/{threadId}\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: pullRequest.description (object)\r\n- GET    /teams/{teamId}/pullRequests\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].description (object)\r\n- POST   /teams/{teamId}/pullRequestsForCommits\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].description (object)\r\n- GET    /teams/{teamId}/pullRequests/{pullRequestId}\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: description (object)\r\n- PUT    /teams/{teamId}/pullRequests/{pullRequestId}\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: description (object)\r\n- PUT    /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId}\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: threads[n].pullRequest.description (object)\r\n- POST   /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId}\r\n  Return Type:\r\n    - Changed 201 Created\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: threads[n].pullRequest.description (object)\r\n- GET    /teams/{teamId}/pullRequests/{pullRequestId}/info\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: pullRequest.description (object)\r\n- POST   /teams/{teamId}/topics/{topicId}/related\r\n  Return Type:\r\n    - Changed 200 OK\r\n      Media types:\r\n        - Changed application/json\r\n          Schema: Broken compatibility\r\n          Missing property: [n].pullRequest.description (object)\r\n\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3822#issuecomment-1327896582"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3822#pullrequestreview-1194728376", "body": "Thanks!"}
{"title": "Create topic mapping job", "number": 3823, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3823", "body": "To keep topic mappings up to date as new pull requests and threads are created"}
{"title": "Shuffle stuff around", "number": 3824, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3824"}
{"title": "Handle when threadTs is null", "number": 3825, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3825", "body": "Turns out we're skipping over single message threads."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3825#pullrequestreview-1194782377", "body": ""}
{"title": "Revert \"Dont return PR-associated slack threads from getTopicRelatedInsights (#3807)", "number": 3826, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3826", "body": "It pains me to remove this beautiful ~hack~ code."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3826#pullrequestreview-1194789756", "body": ""}
{"title": "The getRepoSourceMarks contains redundant points[].overrideSnippetEncoded", "number": 3827, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3827", "body": "\nExpected to pretty dramatically reduce getRepoSourceMarks response payload size."}
{"comment": {"body": "\r\n\r\nId | Request Duration (ms) | Response Duration (ms) | Duration (ms) | Response Body Size (bytes) | Compressed Request Size (bytes)\r\n-- | -- | -- | -- | -- | --\r\nBEFORE | 4,381 | 5,229 | 44,563 | 171,785,614 | 22,049,310\r\nAFTER | 4,152 | 3,697 | 38,512 | 83,964,154 | 18,300,235\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3827#issuecomment-1327942166"}}
{"title": "Send Contributors as part of repo file ref", "number": 3828, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3828", "body": "Send Contributors as part of RepoFileReference.\nThis will be used as part of https://github.com/NextChapterSoftware/unblocked/pull/3813"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3828#pullrequestreview-1198507130", "body": "Can we merge this ASAP please?"}
{"title": "The sourcepoint response can be further optimized to exclude isOriginal unless true", "number": 3829, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3829"}
{"title": "Update Link API", "number": 383, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/383", "body": "Update Link API as a link in markdown contain Different styling ~types~"}
{"comment": {"body": "Did not actually deprecate values in protobuf as we haven't really gotten started yet.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/383#issuecomment-1045070214"}}
{"comment": {"body": "Honstly, do not deprecate shit until we have to.\r\nWaste of time.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/383#issuecomment-1045074475"}}
{"comment": {"body": "We had this originally as:\r\n```proto\r\nmessage Link {\r\n   required FormattedText text = 1;\r\n   required string url = 2;\r\n   optional string title = 3;\r\n }\r\n\r\n message FormattedText {\r\n   repeated FormattedSegment segments = 1;\r\n }\r\n\r\nmessage FormattedSegment {\r\n   required PlainSegment segment = 1;\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/383#issuecomment-1045134913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/383#pullrequestreview-887688026", "body": ""}
{"title": "Prefer trusted sourcepoints only if they have a better snippet than the untrusted original", "number": 3830, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3830", "body": "Fixes these 500s:\n- \n- \nExamples where a trusted original point has a null snippet:\n- \n- \n- \n- \n- "}
{"title": "add repo to be used for job queue controller", "number": 3831, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3831"}
{"title": "Remove version info dupes", "number": 3832, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3832"}
{"title": "Update dashboard topics view", "number": 3833, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3833", "body": "\nRight now it's missing most of the data in the designs (waiting for API changes in https://github.com/NextChapterSoftware/unblocked/pull/3767)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3833#pullrequestreview-1196698332", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3833#pullrequestreview-1196699313", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3833#pullrequestreview-1196714240", "body": ""}
{"comment": {"body": "This is basically doing what `makeSortFn` and `byValue` do...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3833#discussion_r1034170584"}}
{"title": "Topic exploration", "number": 3834, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3834", "body": "Notebooks for various features/sources."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3834#pullrequestreview-1198214281", "body": ""}
{"title": "Start data pipeline", "number": 3835, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3835"}
{"title": "Fix service template", "number": 3836, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3836"}
{"title": "Add slack data service", "number": 3837, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3837"}
{"title": "update docs", "number": 3838, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3838"}
{"title": "Slack data permissions", "number": 3839, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3839"}
{"title": "Fix readme", "number": 384, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/384"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/384#pullrequestreview-887716146", "body": ""}
{"title": "Update to new gradle", "number": 3840, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3840"}
{"title": "Add new GitHubPullRequestEvent.Action", "number": 3841, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3841", "body": "Fixes "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3841#pullrequestreview-1196559866", "body": ""}
{"title": "open stomp port", "number": 3842, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3842", "body": "open stomp port needed by controller"}
{"comment": {"body": "The diff shows only changes to security group rules. I am going to force merge this ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3842#issuecomment-1329799969"}}
{"title": "Fix popover alignment", "number": 3843, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3843"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3843#pullrequestreview-1196594364", "body": ""}
{"title": "Insight search API", "number": 3844, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3844", "body": "A top level search API returning insights.\nNeeds to handle filtering by:\n* string query (regular string search)\n* sort (i.e. by recency)\n* insight types\n* repoIds\n* topicIds\n* topic expert Ids\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3844#pullrequestreview-1196623842", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3844#pullrequestreview-1196659926", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3844#pullrequestreview-1196721422", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3844#pullrequestreview-1198574954", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3844#pullrequestreview-1201809525", "body": ""}
{"comment": {"body": "The idea is that this return object would optionally have a `message`/`messages` property containing content (a `Message`?) that match a given search query. We can add these additional optional properties to this return obj as the designs and mocks become more refined/finalized ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3844#discussion_r1037663724"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3844#pullrequestreview-1202970325", "body": ""}
{"title": "Basic Web composer for Video Walkthroughs", "number": 3845, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845", "body": "Basic web composer for walkthroughs.\nPR is mainly to setup UI for testing integration with hub app.\nStyling is quite rough due and will be updated once final designs are available.\nSeparate PR to implement contributors. Requires significant refactoring as logic is duplicated in a few places right now.\n"}
{"comment": {"body": "Are these utils being used in the vscode UI? Is there a screenshot for that UI to make sure the existing UI isn't affected? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#issuecomment-1332567661"}}
{"comment": {"body": "> Are these utils being used in the vscode UI? Is there a screenshot for that UI to make sure the existing UI isn't affected?\r\n\r\nThey're being used and have been verified. \r\nThe actual types haven't changed so it shouldn't affect the UI... Have also verified VSCode.\r\n\r\n<img width=\"1247\" alt=\"CleanShot 2022-11-30 at 11 17 32@2x\" src=\"https://user-images.githubusercontent.com/1553313/204888561-c6dacd53-c5cf-48e2-b20f-f62317ecd9b5.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#issuecomment-1332621504"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1196717729", "body": ""}
{"comment": {"body": "Based on the code references from VSCode, we try to determine what repo this walkthrough belongs to", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1034173245"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1196718159", "body": ""}
{"comment": {"body": "If there aren't any references to repos in the video, we ask the user to choose a specific repo out of the full list of repos for the team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1034173551"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1196718437", "body": ""}
{"comment": {"body": "To be implemented. This will likely require a large refactor.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1034173741"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199809161", "body": ""}
{"comment": {"body": "small nit but in the stores we've been calling these fns `aggregate<ModelName>` (see: ThreadInfoAggregate, PullRequestInfoAggregate, etc)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1036300713"}}
{"comment": {"body": "the term `position` is still confusing/hard to parse for me; they're timestamps right? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1036301615"}}
{"comment": {"body": "TODO to have better error paging(?)\r\n\r\nWe could probably have a generic one for all the cases where an expected param is undefined in the url. Or use our own hook wrapper to handle the error there and otherwise ensure param (i.e. teamId) is always there", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1036306909"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199869236", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199871674", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199872315", "body": ""}
{"comment": {"body": "Yup. Will be addressing this when actually filling out the web composer. This PR was mainly plumbing the data.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1036344903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199872875", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199879543", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199890662", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199891214", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199891238", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199892331", "body": "Very nice"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199917539", "body": ""}
{"comment": {"body": "Relative position from the start date in seconds", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1036375840"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199918731", "body": ""}
{"comment": {"body": "so it's the relative time from the start? `timeElapsed`? position implies physical position which I don't think this is. Unless I'm still confused about what this is", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1036376700"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#pullrequestreview-1199922842", "body": ""}
{"comment": {"body": "I'm not sure the naming pattern 100% applies here.\r\n\r\nIn this case, we have multiple functions that translate/transform different API models (3 in total...) into a common model for the UI to use.\r\n\r\nWe're not aggregating multiple models together.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3845#discussion_r1036379490"}}
{"title": "Ability for job config to specify default image tag and/or registry", "number": 3846, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3846"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3846#pullrequestreview-1196633880", "body": ""}
{"comment": {"body": "no longer specifying the tag", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3846#discussion_r1034112453"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3846#pullrequestreview-1196634986", "body": ""}
{"comment": {"body": "Based off what we discussed, this seems fine.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3846#discussion_r1034113244"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3846#pullrequestreview-1196635032", "body": ""}
{"title": "Add jobId to the job config", "number": 3847, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3847", "body": "Unique identifier used by the job for telemetry purposes."}
{"title": "Refactor Insight specific components and types", "number": 3848, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3848", "body": "Mostly just moving things around; in anticipation for reuse for incoming views"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3848#pullrequestreview-1196713367", "body": ""}
{"title": "Add walkthrough IPC to hub", "number": 3849, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196722692", "body": ""}
{"comment": {"body": "TODO: pull out teamId and set initializer context", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#discussion_r1034177001"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196734421", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196736868", "body": ""}
{"comment": {"body": "What is this passive state used for?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#discussion_r1034187898"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196738395", "body": ""}
{"comment": {"body": "If the video app has already been initialized but hub is not initializer, are you allowed to start walkthrough?\r\n\r\ne.g. VSCode had launched and initialized video app but did not start the actual recording yet.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#discussion_r1034189135"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196746167", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196746420", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196886107", "body": ""}
{"comment": {"body": "it signifies that the Hub is not the initializer and so passively listens for events like `running` to disable its UI", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#discussion_r1034298406"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196886772", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196887435", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1196888569", "body": ""}
{"comment": {"body": "Nope, `isRunning` is set to true regardless of whether the Hub is the initializer. This means if VSCode swoops in as the initializer the button will be disabled. But there might be some state logic missing here so I'll double check that", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#discussion_r1034300369"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1197984209", "body": ""}
{"comment": {"body": "Example:\r\n* Hub launches and initializes Video App. Video app === \"initialized\", and broadcasts this to all clients, including VSCode. Hub is now in `waitingToBeInitializer`\r\n* User could still technically go to VSCode and click the video button and re-initialize? In this case, both clients believe they are the initializer now.\r\n* Once Video app is done, both clients will launch the composer.\r\n\r\nTakeaways:\r\n* Start button should *only* be enabled in clients if they receive and \"uninitialized\" state?\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#discussion_r1035038572"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1198180130", "body": ""}
{"comment": {"body": "This is not quite how it works. The flow is as follows:\r\n1. User clicks \"Start Walkthrough\" in the Hub\r\n2. The hub enters `waitingToBeInitializer` state, and sends the `Initialize` request to the walkthrough app. The walkthrough app responds.\r\n3. The hub receives the `Initialized` message from the video app, and upgrades itself to the `initialized` state\r\n4. The walkthrough app now knows that the Hub is the initializer, and will ignore initialized messages from other clients\r\n\r\nBut at a high level you're right, even with this flow it's still possible that a client's \"Start\" button is enabled and will be ignored. I think that means anything but \"uninitialized\" needs to disable the button.\r\n\r\nI'll make that change", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#discussion_r1035165265"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1198333089", "body": ""}
{"comment": {"body": "Maybe add `becomeInitializerAfterRestart = false` here as well?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#discussion_r1035271414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1198333135", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#pullrequestreview-1198334108", "body": ""}
{"comment": {"body": "Yeah - we also have to set the \"running\" state for all states other than uninitialized", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3849#discussion_r1035272052"}}
{"title": "Fix inline links", "number": 385, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/385", "body": "Did not update rendering for links when updating spec.\nWorkflows will now run on common folder changes."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/385#pullrequestreview-887771565", "body": ""}
{"title": "Fixing test??", "number": 3850, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3850"}
{"title": "Update icons", "number": 3851, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3851", "body": "Update icons per updated designs:\n\ndashboard:\n\nvscode:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3851#pullrequestreview-1196756959", "body": "Man, does kay ever stop producing.\nThank you kay..."}
{"title": "Don't deploy sourcemaps in dashboard or VSCode extension", "number": 3852, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3852"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3852#pullrequestreview-1198101415", "body": "You complete me."}
{"title": "Add dashboard url to video draft", "number": 3853, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3853"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3853#pullrequestreview-1198109304", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3853#pullrequestreview-1198334462", "body": ""}
{"comment": {"body": "To be clear, this is an override? if not provided, video will be written to tmp?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3853#discussion_r1035272293"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3853#pullrequestreview-1198335292", "body": ""}
{"comment": {"body": "That's correct. In practice the Hub also writes to /tmp/, but to a specialized area of /tmp/ that the sandbox can access", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3853#discussion_r1035272847"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3853#pullrequestreview-1198335337", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3853#pullrequestreview-1198336452", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3853#pullrequestreview-1198431876", "body": ""}
{"title": "Make TopicModel.repo optional", "number": 3854, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3854", "body": "Step 1 on on the path to removing repo from TopicModel:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3854#pullrequestreview-1198107140", "body": ""}
{"title": "Add TopicModel.source property", "number": 3855, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3855", "body": "This will let us specify where the topic came from. The intention here is the data pipeline will store generated topics in the database by calling TopicStore.createTopics, specifying the source.\nIn the next PR, I'll add the ability to set a config flag that controls the source of the topics that are returned to clients. This way we can compare the topics between difference sources."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3855#pullrequestreview-1198242452", "body": ""}
{"title": "Fix 500 in getRepoSourceMarks operation affecting Sentry", "number": 3856, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3856"}
{"title": "Improve job event logging", "number": 3857, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3857"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3857#pullrequestreview-**********", "body": ""}
{"comment": {"body": "You could use the Masked type from the hoplite library.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3857#discussion_r1035220162"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3857#pullrequestreview-**********", "body": ""}
{"title": "Add controller service account", "number": 3858, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3858", "body": "Adding service accounts for Job controllers:\n- Gets access to activemq crews \n- Gets read-only permission on ECR registry"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3858#pullrequestreview-**********", "body": ""}
{"title": "Setup source agent to run against PROD", "number": 3859, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3859", "body": "Kind of hard to test that this will work, until we deploy it.\nSetting the variable on my machine:\nset -x JOB_CONTROLLER_ENV prod\nResults in this log output:\nBuild SHA 939057d9fb5951d8eea14fb3b2d82374e2bb21ea\nApp type source-agent\nProduct Number 1\nProduct Version development\nUsing environment prod from JOB_CONTROLLER_ENV environment variable"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3859#pullrequestreview-**********", "body": ""}
{"comment": {"body": "these web pack file are just used for simulating different environments locally", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3859#discussion_r1035267650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3859#pullrequestreview-1198328153", "body": ""}
{"comment": {"body": "Just used locally.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3859#discussion_r1035267981"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3859#pullrequestreview-1198661448", "body": ""}
{"comment": {"body": "Any particular reason we didn't just use UNBLOCKED_ENV, as it already existed below?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3859#discussion_r1035511566"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3859#pullrequestreview-1199779312", "body": ""}
{"comment": {"body": "That was what we had initially, however, the [kube-job-queue-controller](https://github.com/NextChapterSoftware/kube-job-queue-controller) is agnostic to Unblocked. I think Mahdi wants to open-source it at some point. As a result, we choose a job-controller-specific variable.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3859#discussion_r1036279780"}}
{"title": "Message Editor Translator", "number": 386, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386", "body": "Add logic to translate MessageEditor to Protobuf Block\nAdded support for quotes blocks in message view.\nDiscussion thread in stories so that editor markdown will populate the thread UI"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890159440", "body": ""}
{"comment": {"body": "yeah, interested in why there needs to be any translation logic here \r\n\r\nIt would be nice if the MessageEditor took the protobuf shape and translated it within the component to render, and then passed back up the value in the same protobuf shape (?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812209578"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890169247", "body": ""}
{"comment": {"body": "The problem there is that it means the editor would be regenerating the protobuf models on every single edit, since there's no natural point otherwise to send the changed values.  It feels expensive to do all this translation on every character you type.  I guess some alternatives might be:\r\n\r\n1) Add the ability to only send the changed values when the editor loses focus (onblur) -- this will add eventing confusion when you click on a 'submit' button though, which we've run into before.\r\n2) Debounce the events so we only update if you haven't typed for 100ms?  This will cut down the events quite a bit but might be confusing or odd.\r\n3) ...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812216564"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890172431", "body": ""}
{"comment": {"body": "I'm actually thinking of updating the messageEditor / adding an abstraction layer to *own* the submit button.\r\nThat way, the discussion thread doesn't need to be aware of translations.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812218830"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890173258", "body": ""}
{"comment": {"body": "(Will be a separate PR as I'm thinking of refactoring discussion thread some more.)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812219455"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890176637", "body": ""}
{"comment": {"body": "> It would be nice if the MessageEditor took the protobuf shape and translated it within the component to render, and then passed back up the value in the same protobuf shape (?)\r\n\r\nI don't think the MessageEditor ever *needs* to take protobuf as an input?\r\n\r\nIMO, I actually think the MessageEditor should not expose its internal state to consumer components. The slate data model should be completely abstracted away from the rest of the system.\r\n\r\nAnother thought is to refactor MessageEditor to \"InternalMessageEditor\" and have MessageEditor as a container which exposes a friendly API and handles the actual editor content state.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812221856"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890180592", "body": ""}
{"comment": {"body": "> I'm actually thinking of updating the messageEditor / adding an abstraction layer to _own_ the submit button.\r\n> That way, the discussion thread doesn't need to be aware of translations.\r\n\r\nMy gut feeling is that this is fixing this issue at the wrong level, but happy to hear ideas if you think this is the right direction to go.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812224714"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890188141", "body": ""}
{"comment": {"body": "> My gut feeling is that this is fixing this issue at the wrong level, but happy to hear ideas if you think this is the right direction to go.\r\n\r\nI'll need to give this some more thought but I believe it leads to more predictable UX and state than having an 'onBlur' or debouncer.\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812230200"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890196147", "body": ""}
{"comment": {"body": "Any ideas here? Looking for a more efficient way of determining if the editor is empty. @matthewjamesadam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812236126"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890197600", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890198741", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890315806", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890316044", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890322161", "body": ""}
{"comment": {"body": "This brings up an interesting question: I think with this PR, LinkElement will be treated as a block element.  Does it actually render correctly?\r\nI think this property shouldn't be a boolean, it should be an enum:\r\n* `block`: single-line block element\r\n* `blockMultiline`: multi-line block element\r\n* `inline`: inline element\r\n\r\nIn the `CreateSlateEditor` function we'd need to override editor.isInline to return the right value.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812328261"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890342846", "body": ""}
{"comment": {"body": "Renders correctly.\r\n<img width=\"1055\" alt=\"CleanShot 2022-02-22 at 12 39 19@2x\" src=\"https://user-images.githubusercontent.com/1553313/155215435-378f7156-a2ce-44ad-b5a7-b583e6612c56.png\">\r\n\r\nYou can't actually click on the link though. Read something about the editor needing to be in readonly mode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812343448"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890349503", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890351045", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890361025", "body": ""}
{"comment": {"body": "Interesting. I do think we need to implement isInline correct as per: https://docs.slatejs.org/concepts/02-nodes#blocks-vs.-inlines\r\n\r\nI suspect there may be some operations/transforms that don't work correctly if we don't implement this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812356457"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890362835", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890374971", "body": ""}
{"comment": {"body": "Hmm interesting.  You can simplify this implementation by removing the root Text.isText case (as all root level items are block elements) and simplifying the paragraph case:\r\n```\r\n    return content.some(node => {\r\n        Editor.isBlock(editor, node) &&\r\n        node.type === 'paragraph' &&\r\n        node.children.some(child => Text.isText(child) && child.text.length > 0);\r\n    })\r\n```\r\n\r\nBut I'm not sure if this is testing the right thing.  Basically we want to test that we have anything *other* then an empty set of elements, or a single empty paragraph element.  A single image element would be considered content, for instance.  Maybe this is more accurate:\r\n\r\n```\r\n    return content.some(node => {\r\n        Editor.isBlock(editor, node) &&\r\n        (node.type !== 'paragraph' ||\r\n        node.children.some(child => Text.isText(child) && child.text.length > 0));\r\n    })\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812366731"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890377927", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890381655", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890397033", "body": ""}
{"comment": {"body": "This seems wrong?  Why are we wrapping a link in a paragraph, wouldn't that always promote inline links to block elements?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812382819"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890398689", "body": ""}
{"comment": {"body": "Oh I think I get it.  I think our assumption is that this will actually never happen (because Links will always be within a ParagraphElement or QuoteElement or whatever), this is just for completeness's sake.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812383994"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890400407", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890401519", "body": ""}
{"comment": {"body": "We currently do not have a top `Link` Block in the spec. I'll make the change to support this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812386237"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890403670", "body": ""}
{"comment": {"body": "No I don't think we need (or want) a top-level Link object... but without top-level links I think this code is effectively not needed?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812387905"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890414993", "body": ""}
{"comment": {"body": "You may also be able to just use `Editor.isEmpty(editor, editor)`, though I have no idea what the logic for it actually does :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812393786"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890419183", "body": "Looks good once the last few issues are dealt with "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#pullrequestreview-890425449", "body": ""}
{"comment": {"body": "Yeah.\nThe question is if we ever want a standalone Link element to live outside a paragraph.\n\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/386#discussion_r812402199"}}
{"title": "Slack in the cloud", "number": 3860, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3860"}
{"title": "Enable verbose source agent logging", "number": 3861, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3861"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3861#pullrequestreview-1198391926", "body": ""}
{"title": "Add topic source to team settings model", "number": 3862, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3862", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3862#pullrequestreview-1198414800", "body": ""}
{"title": "Remove create PR", "number": 3863, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3863", "body": "No longer using create PR flow.\nRemoving to lower cost of maintenance."}
{"comment": {"body": "I think you also need to do a search for `unblocked-vscode.createPullRequest` and remove references to the command??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3863#issuecomment-1331374818"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3863#pullrequestreview-1198412297", "body": ""}
{"title": "Fix buckets", "number": 3864, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3864"}
{"title": "Fix rate limiting", "number": 3865, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3865"}
{"title": "Add contributors to RepoFileReference in IPC protocol", "number": 3866, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3866"}
{"title": "java 19 upgrade", "number": 3867, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3867"}
{"comment": {"body": "Too many bugs with gradle and junit!\r\nNOt working..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3867#issuecomment-1332535199"}}
{"title": "Add insights count column", "number": 3868, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3868", "body": "with mock data:\n\n\nNote: hiding the Experts column until experts are implemented \nNote: right now insights count is hardcoded to return 0, David will be working on the service implementation of this count to backfill. Should still be safe to merge since this is under a feature flag"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3868#pullrequestreview-1199794459", "body": ""}
{"comment": {"body": "Remove.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3868#discussion_r1036290464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3868#pullrequestreview-1199795633", "body": ""}
{"comment": {"body": "Nevermind... Looks like this is used for Experts.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3868#discussion_r1036291294"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3868#pullrequestreview-1199796257", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3868#pullrequestreview-1199797206", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3868#pullrequestreview-1199804547", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3868#pullrequestreview-1200060283", "body": ""}
{"title": "Source agent limited to 2 hours and runs in verbose mode", "number": 3869, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3869"}
{"title": "Update kube-setup.md", "number": 387, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/387", "body": "Missing an S"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/387#pullrequestreview-890078140", "body": ""}
{"title": "Add view-scoping option to VSCode explorer insights panel", "number": 3870, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870", "body": "The explorer insights panel now has an option to display only the PRs and threads relevant to the visible part of the focused editor.  The hope is that this will provide more relevant results in the UI.\nSome implementation details:\n- The ActiveFileManager now publishes a second stream (activeRangeStream) that is updated with the currently-active file and view range.\n- A new stream (ViewScopedInsightStream) uses this to understand what file and line range are being viewed.\n- ViewScopedInsightStream also subscribes to the currently-active file view, and whenever that file changes, queries the blame commit list for the file (this is in the helper CurrentFileCommitStream).\n- ViewScopedInsightStream uses this blame and view information to filter down the global set of threads and PRs.\n- An option was added to the dropdown menu to turn this feature on and off.  This necessitated reimplementing the menu using the custom Dropdown components instead of the HTML Select.  The dropdown menu now looks like this:\n\nFixes "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1198491962", "body": ""}
{"comment": {"body": "The `DropdownGroup` item here is very simple, just a header for the group.  We could evolve this to optionally contain children that would be indented, but for my purposes I actually didn't want that, since the checkboxes naturally indent the child items.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#discussion_r1035382820"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1198492445", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1198659134", "body": ""}
{"comment": {"body": "This is TBD -- I may need to totally redo how webviews work for this to ever work correctly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#discussion_r1035509917"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1198659730", "body": ""}
{"comment": {"body": "This thing could do with tests, but it has so many dependencies that mocking everything out and building it was going to take a ton of time.  This feature is a bit of an experiment, if we keep it I'll write tests for this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#discussion_r1035510385"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199734288", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199748180", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199748681", "body": ""}
{"comment": {"body": "How do we want to support fileMarks?\r\n\r\n`threadInfo.mark?.isFileMark || (...existing logic)`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#discussion_r1036257915"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199778708", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199784359", "body": ""}
{"comment": {"body": "We will filter them out completely, at least for now.  It doesn't seem right to show them when the user has selected to only show the items in the view area, especially when the point is to try to cut down the number of items shown.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#discussion_r1036283322"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199785878", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199792123", "body": ""}
{"comment": {"body": "Instead of sending this mocked active range, would it make sense to make range optional and just send filePath?\r\n\r\nConsumer can then handle undefined range however it likes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#discussion_r1036288882"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199792600", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199796198", "body": ""}
{"comment": {"body": "I have an alternative approach that might make more sense that I'll do in a future PR -- I was trying to avoid optionality here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#discussion_r1036291662"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3870#pullrequestreview-1199929977", "body": ""}
{"title": "up replica count", "number": 3871, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3871"}
{"title": "Remove log for walkthrough app", "number": 3872, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3872", "body": "Due to lifecycle changes with VSCode -> Walkthrough app connection, no longer need to log errors on connection failures. \nThis is now an expected error case as VSCode constantly tries connecting with walkthrough app."}
{"comment": {"body": "- https://sentry.io/organizations/nextchaptersoftware/issues/3750037967\r\n- https://sentry.io/organizations/nextchaptersoftware/issues/3761430710\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3872#issuecomment-1331464943"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3872#pullrequestreview-1198505221", "body": ""}
{"title": "Upload video for composer", "number": 3873, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3873"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3873#pullrequestreview-1199993237", "body": ""}
{"comment": {"body": "Probably best if this handles specific HTTP errors as non-retryable failures (non-500)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3873#discussion_r1036428185"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3873#pullrequestreview-1201611786", "body": ""}
{"comment": {"body": "Not anything that needs to be fixed here, but something to consider is how the failure mode for each of these steps works, especially if this fails later in the pipeline.  ie, if we create an asset, and then fail to upload the video, does the asset eventually get cleaned up, or should we do that ourselves?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3873#discussion_r1037525559"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3873#pullrequestreview-1201612853", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3873#pullrequestreview-1201616325", "body": ""}
{"comment": {"body": "Agreed - we haven't come to any conclusions on this yet. I'd like to chat in person with you and Jeff because there are other issues at play (like deleting when the thread is posted, whether a draft should \"age out\", etc)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3873#discussion_r1037528667"}}
{"title": "Return topics based on team setting", "number": 3874, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3874", "body": "Follow up from https://github.com/NextChapterSoftware/unblocked/pull/3862 where the API will now return topics based on the source type chosen for a team. Currently the only option is Default but as we add additional topic generation sources, we will have others to select from."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3874#pullrequestreview-1198769927", "body": ""}
{"title": "Add real team member to source agent api auth token to get pusher working", "number": 3875, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3875"}
{"title": "Optimize", "number": 3876, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3876"}
{"title": "Should be SOURCEMARK_DEBUG not SOURCEMARK_VERBOSE", "number": 3877, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3877"}
{"title": "machine learning stuff should not be mixed with ci stuff", "number": 3878, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3878"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3878#pullrequestreview-1198760155", "body": ""}
{"title": "Reduce eslint warnings", "number": 3879, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3879", "body": "Gets rid of almost all the eslint warnings:\n\nUse unknown where appropriate\nUse real types where appropriate\nFix code when appropriate\nDisable lint warnings when we're implementing an untyped JS API and types are meaningless (ie winston)\n\nThis doesn't fix the log sanitizer, will do that next or get Rashin to do it."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3879#pullrequestreview-1198705881", "body": ""}
{"title": "make cdk diff less noisy", "number": 388, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/388", "body": "To address this thread: "}
{"comment": {"body": "thanks Mahdi", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/388#issuecomment-1048039191"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/388#pullrequestreview-890133615", "body": ""}
{"title": "activemq hostname", "number": 3880, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3880"}
{"title": "cleanup pip", "number": 3881, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3881"}
{"title": "Move topics page to team level", "number": 3882, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3882"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3882#pullrequestreview-1199968783", "body": ""}
{"title": "Add slack metadata", "number": 3883, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3883"}
{"title": "Batch source-agent jobs for all repos", "number": 3884, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3884", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3884#pullrequestreview-1199972611", "body": ""}
{"title": "Fix content type", "number": 3885, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3885"}
{"title": "Support more languages more reliably in VSCode", "number": 3886, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886", "body": "Support more languages, more reliably, in VSCode's syntax hilighter.\n\nAlways pass the file path into the shiki highlighter.  Before, we were submitting three different values (VSCode's language ID, the file extension, and the file extension with a dot before it), so the highlighter sometimes worked in some UIs, for some languages, and didn't for others.\nAdd mappings for more languages.  This gets a bunch of common languages working correctly."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#pullrequestreview-1200034219", "body": ""}
{"comment": {"body": "consider using this resource:\r\nhttps://github.com/withgraphite/language-services/blob/main/src/ext.ts", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#discussion_r1036456961"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#pullrequestreview-1200035012", "body": ""}
{"comment": {"body": "The language definitions on the right don't map to a meaningful shiki parser though...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#discussion_r1036457525"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#pullrequestreview-1200047413", "body": ""}
{"comment": {"body": "Right, but can we use highlight.js instead?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#discussion_r1036466329"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#pullrequestreview-1200047644", "body": ""}
{"comment": {"body": "No, because there is no way for highlight.js to use the VSCode theming.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#discussion_r1036466482"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#pullrequestreview-1200049244", "body": ""}
{"comment": {"body": "We could potentially map the token definitions between VSCode and highlight.js so that we could use the VSCode theme colours in a highlight.js context, but that would be a sizeable task.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#discussion_r1036467610"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3886#pullrequestreview-1202937812", "body": ""}
{"title": "Jeff/draft web composer", "number": 3887, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3887"}
{"title": "Just show the topic name on the topics page", "number": 3888, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3888"}
{"title": "Allow the Hub to smoothly transition through Walkthrough permissions mode", "number": 3889, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889#pullrequestreview-1200067584", "body": ""}
{"comment": {"body": "Might be able to just collapse these into a single value if we take care of app launch elsewhere. Keeping here for now even if redundant", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889#discussion_r1036480062"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889#pullrequestreview-1200067938", "body": ""}
{"comment": {"body": "This is the secret to a smooth transition", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889#discussion_r1036480317"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889#pullrequestreview-1200079432", "body": ""}
{"comment": {"body": "If this video app is restarted and this value is stored in memory, how is this ever true?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889#discussion_r1036487750"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889#pullrequestreview-1200375301", "body": ""}
{"comment": {"body": "The \"restart\" path confusingly follows a different flow. After a restart we call startWalkthrough, which pushes the state to .wantsToBeInitializer. That was the reason behind my consolidation comment above. The tricky thing is that they _are_ slightly different. One requires an app restart and immediate transition to waiting, the other does not. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889#discussion_r1036698647"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3889#pullrequestreview-1201720139", "body": ""}
{"title": "Add Tree View and Store", "number": 389, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/389", "body": "This pr is likely going to get ripped apart. :)\nThis pr achieves the following:\n1. Add TreeView and TreeViewNode shared components.\n2. Add simple store to manage state for now. (not zustand).\nIn flux (to be probably changed as I work on this shit more on the VScode end of things)\n1. On the VScode end, currently using modeling behaviour similar to how VSCode does it with a tree hierarchy.\n2. On the UI end, the final state is a flattened version of the VScode model.\n"}
{"comment": {"body": "Moved away from zustand for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/389#issuecomment-1049045701"}}
{"comment": {"body": "Going to merge this in unless there are objections soon.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/389#issuecomment-1049055633"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/389#pullrequestreview-890231858", "body": ""}
{"comment": {"body": "WIP... \r\nNot set in stone...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/389#discussion_r812262350"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/389#pullrequestreview-890232525", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/389#pullrequestreview-891785723", "body": ""}
{"comment": {"body": "We might want to use a `Map<string, TreeViewNodeState>` instead of setting values on a raw object.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/389#discussion_r813390388"}}
{"title": "Refactor Contributors", "number": 3890, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890", "body": "An attempt to consolidate majority of state / logic when it comes to contributors.\nMajority of state and logic at the view level is now held within a ContributorsContext\nThis context provides the list of selected contributors and multiple utilities to child components which are accessed by context hooks.\nThe full list of contributors itself is managed outside this context and is passed in as it can be updated outside the context of the webview (e.g. VSCode)\nThis PR does not fully implement contributors on the dashboard. Is primarily focused on refactoring contributors on VSCode + Web Extension first."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-1201820719", "body": ""}
{"comment": {"body": "It feels a bit strange to have this pass the values directly into the child via a fn like this -- shouldn't any children use `useContributorsContext` to get these values?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#discussion_r1037672112"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Yes and no.\r\n\r\nThe main reason for doing this are for components where we render the provider and want to use its value in the same return function.\r\n\r\ne.g.\r\n\r\n```\r\n            <ContributorsContextProvider contributors={contributorsList} addContributor={onAddContributor}>\r\n                {({ selectedContributors }) => (\r\n                    <>\r\n                        <ContributorsList ref={contributorsListRef} />\r\n                        <div>---</div>\r\n                        <div>Selected emails: {selectedContributors.map((gc) => gc.id).join()}</div>\r\n                    </>\r\n                )}\r\n            </ContributorsContextProvider>\r\n            ```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#discussion_r1038354205"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Yeah I saw that -- just seems unusual for a context provider to not only provide values through a context but also pass them directly to children.  It does make the basic case syntax a bit uglier because you can't just pass children directly to the object.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#discussion_r1038428117"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-**********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-1203187082", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-1203220092", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-1203220219", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-1203229923", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3890#pullrequestreview-1203229943", "body": ""}
{"title": "Allow creating topics with score", "number": 3891, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3891"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3891#pullrequestreview-1200139957", "body": ""}
{"title": "Add histogram enum", "number": 3892, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3892", "body": "Renaming Default to UserSelected since this is more accurate, and adding a Histogram source so that we can compare the histogram approach to topic generation vs. other (ex ML) approaches."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3892#pullrequestreview-**********", "body": ""}
{"title": "Do not trigger jobs if no sourcemarks or no active team members", "number": 3893, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3893", "body": "no threads: no sourcemarks, just a waste of resources\nno active team member with account: cannot successfully auth with API"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3893#pullrequestreview-**********", "body": ""}
{"title": "Show top N topics generated by histogram method", "number": 3894, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3894", "body": "The data is a little stale so we'll need to rerun the generation logic, but this adds the logic to show it."}
{"title": "Sourcemark DiffParser handle embedded carriage return characters", "number": 3895, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3895", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3895#pullrequestreview-**********", "body": ""}
{"comment": {"body": "The previous implementation couldn't handle this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3895#discussion_r1036594806"}}
{"title": "Make all dropdown items full width", "number": 3896, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3896", "body": "Dropdown refactor from earlier broke the width implementation of the children items.\nbefore:\n\nafter: \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3896#pullrequestreview-1201536765", "body": ""}
{"title": "SnippetFinder: selection range is outside snippet range", "number": 3897, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3897", "body": "This change just adds debugging so we can track down the source of the errors.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3897#pullrequestreview-1201439648", "body": ""}
{"title": "Add video tab to hub", "number": 3898, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3898", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3898#pullrequestreview-1201722983", "body": ""}
{"comment": {"body": "Why is this a separate popover at this level?\r\n\r\nI'm a bit surprised we couldn't have this popup live *within* WalkthroughLauncherView with SwiftUI?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3898#discussion_r1037601330"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3898#pullrequestreview-1201726139", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3898#pullrequestreview-1201726620", "body": ""}
{"comment": {"body": "It seems to be the only way to get a native looking menu that obeys the correct anchoring and dismissal rules. Using the SwiftUI popover component will display the little arrow thingy, so it doesn't feel like a menu, and it's not customizable. Using SwiftUI menu components is even worse because then you're stuck with a horrible looking menu that doesn't even look macOS native", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3898#discussion_r1037603859"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3898#pullrequestreview-1201728724", "body": ""}
{"comment": {"body": "If you're wondering whether the implementation of this could live in `WalkthroughLauncherView`, it definitely could. The decision to keep all \"windows\" tracked in the app delegate was arbitrary.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3898#discussion_r1037605212"}}
{"title": "Remove score", "number": 3899, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3899"}
{"title": "Add secrets service to auth design", "number": 39, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39", "body": "This change adds 2 new services, the secret service and the identity service.\nProblem\nThe auth service is a publicly exposed service. It should not house secrets, and especially not secrets used to mint identity tokens. \nIt should also not interact with GitHub on the final validation step because if popped it could \"fake\" the identity validation step with GitHub and launch a confused deputy attack on the secret service. \nProposal\nDefence in depth. We introduce secret service that manages interactions with GitHub to perform the code exchange and identity verification, and then finalizes the transaction by minting a codeswell  identity token.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39#pullrequestreview-853891947", "body": ""}
{"comment": {"body": "Don\u2019t see the point of the secret service as it has the same threat boundary as the identity service. Neither persist secrets. Neither are public facing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39#discussion_r785645279"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39#pullrequestreview-853902683", "body": ""}
{"comment": {"body": "I'm ok consolidating it into a single service. The point is to separate the auth service and the api service from the 2 private keys we will be managing:\r\n1. Private GitHub App key\r\n2. Private CodeSwell user token key", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39#discussion_r785652632"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39#pullrequestreview-853903556", "body": ""}
{"comment": {"body": "I split identity because minting tokens seems like a natural threat boundary separate from interactions with internet facing services, either calling in or going out", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39#discussion_r785653255"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39#pullrequestreview-853915021", "body": ""}
{"comment": {"body": "*Update*: removed identity service. Secret service does all the identity things now", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39#discussion_r785661688"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/39#pullrequestreview-854545778", "body": ""}
{"title": "Update location of message transformer", "number": 390, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/390", "body": "MessageTransformer was being exported within shared webComponents\nThis means when MessageTransformer is pulled in by Mock data generator which is used by VSCode, it pulls in web views which causes runtime issues.\n\n\nUpdated location of MessageTransformer into utils to fix issue."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/390#pullrequestreview-890253084", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/390#pullrequestreview-890255659", "body": ""}
{"title": "Return insight count for topics", "number": 3900, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3900", "body": "Likely going to replace ThreadTopicModel and PullRequestTopicModel with a single join table (InsightTopicModel?) so this code will likely go away, but let's add this so that we can show the insight count in the meantime."}
{"title": "Don't re-fetch topics whenever auth token refreshes", "number": 3901, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3901", "body": "We had combined fetching the topics with aggregating the topics and team members into one step, so whenever the team member list changed, it would trigger re-fetching the topics.\nThis separates the two steps out into separate streams, so now we only re-fetch topics when the logged in user changes, and we aggregate whenever the team member list changes."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3901#pullrequestreview-1201529664", "body": ""}
{"title": "Better source-agent logging", "number": 3902, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3902", "body": "consistently add repo fields as metadata\nallow 5 seconds for logs to flush (hopefully)"}
{"title": "Refactor using InsightStore", "number": 3903, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903", "body": "Use getTopicRelatedInsights temporarily until the new search API (https://github.com/NextChapterSoftware/unblocked/pull/3844)\nRefactor TopicView to use new store \nNo change in UI"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903#pullrequestreview-1202804247", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903#pullrequestreview-1202806843", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903#pullrequestreview-1202813317", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903#pullrequestreview-1203015904", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903#pullrequestreview-1203021620", "body": ""}
{"comment": {"body": "This is using an effect to re-store the resolved topic/insights in state separate from the stream, is there a reason we wouldn't just use the stream directly? It means we wouldn't need this block at all?\r\n\r\nDown below instead of testing whether matchingTopic exists and insights are populated, we'd do something like:\r\n```\r\nif (topicState.$case !== 'ready' || insightState.$case !== 'ready') {\r\n  return <LoadingSummaryViewList />\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903#discussion_r1038487655"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903#pullrequestreview-1203021690", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903#pullrequestreview-1203026101", "body": ""}
{"comment": {"body": "Not a big deal either way but setting the store value in state means I don't need to check for the `ready` case of the data stores every time I want to access the value property. The `topic` is the matching found topic (i.e. the topicsState is a list of topics, not a single topic). And the insights are accessed in the sorting/filter functions.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3903#discussion_r1038492935"}}
{"title": "Reduce top spammy logs", "number": 3904, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3904", "body": "\nFeel free to push back @davidkwlam @rasharab if you need these"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3904#pullrequestreview-1201688946", "body": ""}
{"title": "Web composer style", "number": 3905, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201860488", "body": ""}
{"comment": {"body": "what is supposed to happen if the user clicks this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037701058"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201860595", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201860814", "body": ""}
{"comment": {"body": "how come we're sizing this here and not on the icon(?) ie `size={20}`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037701314"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201861098", "body": ""}
{"comment": {"body": "Hmm how come this is styled manually like this and not via `ThreadIcon`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037701528"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201861433", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201862047", "body": ""}
{"comment": {"body": "you probably need to get a state.prevRoute here to navigate to (there are a couple examples in the web directory)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037702327"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201865140", "body": ""}
{"comment": {"body": "It's disabled. Not click pointer & greyed out. So more for messaging ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037704737"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201865315", "body": ""}
{"comment": {"body": "Icon within the title", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037704860"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201865621", "body": ""}
{"comment": {"body": "But the wording implies it's actionable no? Or can you send a screenshot of what this looks like", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037705108"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201865744", "body": ""}
{"comment": {"body": "sorry yeah. updated my question", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037705210"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201865790", "body": ""}
{"comment": {"body": "Thought about that. The only entry point to this is actually from the hub. User should not be navigating to this UI from the dashboard.\r\naka there isn't a prevRoute in this case?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037705240"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1201867495", "body": ""}
{"comment": {"body": "should the user even be allowed to close this view then? (i.e. don't pass in an onClose at all?) like if they hit close before saving then is the form/video just gone(??) \r\n\r\nwhat happens after they've filled in this form and hit save? do they get redirected to the discussion view? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1037706551"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1202820008", "body": ""}
{"comment": {"body": "There's a whole draft idea Peter and I are brainstorming. Will utilize the existing draft API but will be coming later.\r\n\r\nWe want to get the core feature out first. Closing before saving will close the form/video. \r\n\r\nThat's the same behaviour we have everywhere right now. We could add some safeguards with a popup in the future but not in scope.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1038357288"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1202822209", "body": ""}
{"comment": {"body": "This is the last item for a search input. So it's actionable if you search.\r\n\r\n\r\n<img width=\"1057\" alt=\"CleanShot 2022-12-02 at 09 19 33@2x\" src=\"https://user-images.githubusercontent.com/1553313/205349076-0c1f37d9-cb72-4c9b-90a9-5850fd6d1659.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1038358838"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1202825747", "body": ""}
{"comment": {"body": "The base component has a size which was set for VSCode initially\r\nOverriding with CSS for web as it's a pretty nested shared component.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1038361290"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1202832538", "body": ""}
{"comment": {"body": "This isn't a thread yet. I don't have the data necessary to use a ThreadIcon.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1038365972"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1202833440", "body": ""}
{"comment": {"body": "`Icon icon={'videoWalkthrough'} />` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1038366637"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1202833779", "body": ""}
{"comment": {"body": "I could refactor it but isn't a straightforward conversion to using threadType as archivedAt is necessary.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1038366837"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1202936730", "body": ""}
{"comment": {"body": "That's what I basically have\r\n```                    <Icon icon={CustomIconType.videoWalkthrough} size=\"xLarge\" />```\r\n\r\nThe colour was off though which is why I have this color override.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1038436396"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1203231091", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1203247856", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1203249140", "body": ""}
{"comment": {"body": "Should we add colour as an optional parameter to the `Icon` component so we can pass this in explicitly without having to override it in CSS like this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1038638898"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1203250255", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#pullrequestreview-1214078616", "body": ""}
{"comment": {"body": "How is the color off? If that's the only thing, maybe it's the svg we need to update? I think all the icons should be the same ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3905#discussion_r1046215968"}}
{"title": "remove all sqs permissions", "number": 3906, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3906", "body": "That was a lot of text!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3906#pullrequestreview-1201782041", "body": "Fuck yeah!!!"}
{"title": "Context logger has JOB_ID", "number": 3907, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3907"}
{"title": "Only load highlight.js once in the dashboard", "number": 3908, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3908", "body": "Fixes UNB-792\nWe were loading the highlight.js web worker bundle for every single separate CodeBlock element.  This bundle is big, the data was uncached, so a lot of network traffic happened, and the JS VM got seriously bogged down.  Debugging the dashboard was basically impossible, and the browser would occasionally kill the tab from memory and CPU pressure.\nWe didn't notice this before, because in most UIs we only syntax-hilighted once code block at a time (in the discussion thread view).  Now that we display groups of threads and PRs together this is a real problem.\nThis PR ensures the bundle is only loaded once\n- Use PromiseProxy to proxy promise requests from the dashboard to the web worker and back\n- Had to move LazyValue into the shared utils lib, but that's where it should have been in the first place"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3908#pullrequestreview-1201800945", "body": ""}
{"comment": {"body": "This is goofy, but because all our code depends on all our other code, this web worker ends up depending on `FetchProxy`, which uses a bunch of DOM APIs, even though it will end up getting tree-shaken out in production builds.  This gets builds working correctly.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3908#discussion_r1037657802"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3908#pullrequestreview-1202842090", "body": ""}
{"title": "Add avatarUrl to Team API model", "number": 3909, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3909"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3909#pullrequestreview-1201817122", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3909#pullrequestreview-1201817475", "body": ""}
{"title": "Muxer for channel-changed requests", "number": 391, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391", "body": "Add muxing of channel requests.  Included work:\n\nAdd ChannelPoller class, which tracks all channel updates globally.  This is generally used as a static instance so channel polling is shared.  In unit tests, a unique ChannelPoller is passed in for each test, so that channel polling state doesn't bleed between tests.\nAdd a mock \"PetStore\" API with a getCats and getDogs endpoint and /cats and /dogs channels.  This lets us test that channel muxing works as expected.  This is just a fake API without any spec, I thought this would be easier to test, as the test won't have to be changed whenever our API changes.\nAdd a bunch of unit tests for this that work against the mock PetStore API.  These verify that fetching and channel polling behaviour works as expected."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391#pullrequestreview-891799945", "body": ""}
{"comment": {"body": "So we're making each request sequentially?\r\n\r\nWould it make sense to batch requests make them run in parallel?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391#discussion_r813403250"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391#pullrequestreview-891802049", "body": ""}
{"comment": {"body": "I wondered about that but thought I'd keep it simple for a first go, but I can't see how parallelizing this would cause any problems.  I'll add that on a follow-on PR?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391#discussion_r813404921"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391#pullrequestreview-891803300", "body": ""}
{"comment": {"body": "Different data sets may require different polling intervals?\r\nFor example, if I'm viewing messages in a thread conversation, I would expect things to be quite responsive.\r\nOther areas such as new threads appearing? That could happen less frequently. \r\n\r\nThis is definitely an optimization that doesn't need to be done now but something to think about. If we have many channels, we may unnecessarily saturate our connection bandwidth.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391#discussion_r813405853"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391#pullrequestreview-891806304", "body": ""}
{"comment": {"body": "Yeah I thought about this too but it was going to be fairly complicated to implement.  It's also not clear to me that this is the best way to optimize channel polling -- if we're optimizing this call, we might be better off adding long polling or websockets for this call.  Definitely something to keep in mind for later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391#discussion_r813408071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/391#pullrequestreview-891817187", "body": "Good stuff!\nAre you setting up a pet store for our upcoming interview process?"}
{"title": "Add avatar to walkthrough team selector", "number": 3910, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3910"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3910#pullrequestreview-1201864751", "body": ""}
{"comment": {"body": "nit: Do we want to prevent team selection if we're missing the avatarURL? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3910#discussion_r1037704399"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3910#pullrequestreview-1201864797", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3910#pullrequestreview-1201873074", "body": ""}
{"comment": {"body": "The avatarUrl is a required field", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3910#discussion_r1037710824"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3910#pullrequestreview-1201874688", "body": ""}
{"comment": {"body": "Ah crap this points to another bug though. It shouldn't be `teams?.first` it should be `selectedTeam` \ud83e\udd26\u200d\u2642\ufe0f", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3910#discussion_r1037712048"}}
{"title": "Update Draft Url", "number": 3911, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3911"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3911#pullrequestreview-1201852998", "body": ""}
{"title": "Add team to dashboard url path for videoDraft", "number": 3912, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3912"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3912#pullrequestreview-1201859857", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3912#pullrequestreview-1201860145", "body": ""}
{"comment": {"body": "Jeff this test confirms it all works. Please confirm the path meets your expectations", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3912#discussion_r1037700782"}}
{"title": "Persist job status", "number": 3913, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3913"}
{"title": "Fetch AssetResponse", "number": 3914, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3914", "body": "Need to use getAssetResponse Url, not create"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3914#pullrequestreview-1201953522", "body": "Quick, sweep this under the rug!"}
{"title": "Fix avatars in team selector", "number": 3915, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3915"}
{"title": "VSCode foregrounds itself on walkthrough complete", "number": 3916, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3916", "body": "Instead of depending on Walkthrough app to foreground VScode (which we cannot depend on anymore since hub can start a walkthrough), VSCode foregrounds itself on complete video."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3916#pullrequestreview-1201963132", "body": ""}
{"title": "optimize histogram", "number": 3917, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3917"}
{"title": "Update", "number": 3918, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3918"}
{"title": "Create topic page", "number": 3919, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3919"}
{"title": "Add PR ingestion runner", "number": 392, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/392", "body": "Adds ability to grab PR comments for a repo from the GitHub API and create the necessary DB models. This is to unblock sourcemark development.\nRuns in the background on application start up (fire and forget). Once we have an admin console, we can migrate this logic to there.\nNotes\n- message bodies have not been serialized to our protobuf form\n- outdated comments are not imported"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/392#pullrequestreview-890537579", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/392#pullrequestreview-890545011", "body": "minor comments. feel free to ignore"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/392#pullrequestreview-890574187", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/392#pullrequestreview-890617787", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/392#pullrequestreview-890656071", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/392#pullrequestreview-891483570", "body": ""}
{"title": "Show the correct topics", "number": 3920, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3920"}
{"title": "Create ExpertsRecommendationService", "number": 3921, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3921", "body": "A first attempt at identifying experts for a topic. \nThis approach looks at the pull request creators, reviewers, and message authors and counts how often they appear, with a bias toward PR creators and reviewers. Outliers that contribute the most are surfaced experts.\nThis is very much a work in progress."}
{"comment": {"body": "@rasharab Yup, my ultimate plan is to do this either as a background job or event driven with everything contained in the Topics service.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3921#issuecomment-1335550861"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3921#pullrequestreview-1202797907", "body": "Question. Are we planning on moving these sort of things to the topic service we created a while back? I can imagine a deferred event based system similar to what you did with search indexing?"}
{"title": "Fix counts", "number": 3922, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3922"}
{"title": "Add transaction", "number": 3923, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3923"}
{"title": "UNB-794 / Dont always fetch slack installation", "number": 3924, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3924", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3924#pullrequestreview-1202942720", "body": ""}
{"title": "remove sqs queues from dev", "number": 3925, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3925", "body": "Since our removalPolicy was set to retain I still need to delete those queues manually from AWS console."}
{"title": "Add completionUrl parameter to login endpoints", "number": 3926, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3926", "body": "At the moment there is no way to redirect back to the original landing url following a 401 response. The change will allow us to implement a passthrough url embedded in the OAuth flow's /login/exchange redirect, which the dashboard can then extract and redirect to after the exchange is completed."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3926#pullrequestreview-1202991788", "body": ""}
{"title": "Add ability to trigger search reindexing for all teams", "number": 3927, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3927"}
{"title": "Implements completionUrl for login", "number": 3928, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3928"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3928#pullrequestreview-1205096516", "body": ""}
{"comment": {"body": "So this is being appended to the final url as a query parameter?\r\n\r\naka something like `http://localhost:9000/login/exchange?code=CODE&completionUrl=COMPLETIONURLBASE64`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3928#discussion_r1039973504"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3928#pullrequestreview-1205096901", "body": "I think this looks good but may be worth someone more competent in Kotlin to review."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3928#pullrequestreview-1205099056", "body": ""}
{"comment": {"body": "That is the hope. I'm worried that GitHub may strip query parameters on the return trip but we will only know that through testing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3928#discussion_r1039975190"}}
{"title": "Source agent exit status 1 when something catastrophic goes wrong", "number": 3929, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3929"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3929#pullrequestreview-1203119790", "body": ""}
{"title": "Adding skeleton for thread sidebar in vscode", "number": 393, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/393", "body": "Note.\nAll of this is boilerplate."}
{"comment": {"body": "Going to merge this in unless there's objections as I'm blocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/393#issuecomment-1049056553"}}
{"title": "Add reindexing button", "number": 3930, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3930"}
{"title": "Gracefully handle termination signals", "number": 3931, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3931"}
{"title": "Remove TopicModel.repo", "number": 3932, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3932", "body": "Next PR will add a migration to drop the column."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3932#pullrequestreview-1203199441", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3932#pullrequestreview-1203208772", "body": ""}
{"title": "Drop TopicModel.repoId column", "number": 3933, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3933"}
{"title": "David/add button", "number": 3934, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3934"}
{"title": "Fix missing titles", "number": 3935, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3935", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3935#pullrequestreview-1203255973", "body": ""}
{"title": "Upload the video before bouncing to the browser", "number": 3936, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936", "body": "Upload before opening browser. Does not deal with failure states (needs some design work)\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#pullrequestreview-1206968688", "body": ""}
{"comment": {"body": "This and the fixed height are required to place the content in the right place. If left to SwiftUI, it will leave a big chunk of whitespace at the _bottom_ of the window equivalent to the height of the titlebar. Nothing except a fixed height can resolve this issue :( ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#discussion_r1041224926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#pullrequestreview-1206971945", "body": ""}
{"comment": {"body": "This automatically animates the progress bar", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#discussion_r1041227169"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#pullrequestreview-1206975911", "body": ""}
{"comment": {"body": "It's weird that this isn't async, but there isn't really a better way to handle cancellation. The only option available to us is to pass a _Task_ around for cancellation. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#discussion_r1041229801"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#pullrequestreview-1207100067", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#pullrequestreview-1207107042", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#pullrequestreview-1207107165", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#pullrequestreview-1207154414", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3936#pullrequestreview-1207155028", "body": ""}
{"title": "reduce prod cluster size by removing one of the read-only instances", "number": 3937, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3937"}
{"title": "disabled datapipeline stack, resized dev RDS and removed prod sqs queues", "number": 3938, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3938", "body": "Resized Dev RDS \nDisabled datapipeline stack. I need to sync with Rashin to figure out what's the story there. \nRemoved all SQS queues from prod"}
{"title": "Update launch approach", "number": 3939, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3939", "body": "Default to applications path to launch walkthrough app instead of mdfind & open -b with bundle ID."}
{"comment": {"body": "> LGTM. Should we move the path-handling (ie, \"where is the hub app?\") and maybe the opening stuff into a helper or utility class?\r\n\r\nWill get that out immediately after this. Want to get a build out to test / Brent.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3939#issuecomment-1337825048"}}
{"comment": {"body": "It's probably worth trying to provoke various error cases to see how this reacts", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3939#issuecomment-1337858478"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3939#pullrequestreview-1204965519", "body": "LGTM.  Should we move the path-handling (ie, \"where is the hub app?\") and maybe the opening stuff into a helper or utility class?"}
{"title": "Add isOriginal property to SourcePointModel", "number": 394, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/394", "body": "This will let us reset and rebuild SourceMarks"}
{"comment": {"body": "> This will let us reset and rebuild SourceMarks\r\n\r\nAlso allows to get the topological path of commits between the original source point and the current commit SHA. Without knowing the original source path it's not possible to construct that path, unless we rely on timestamps.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/394#issuecomment-1049026850"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/394#pullrequestreview-891270099", "body": ""}
{"title": "Add Walkthrough Path Helper", "number": 3940, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3940", "body": "Based on feedback here: https://github.com/NextChapterSoftware/unblocked/pull/3939\nAdded utility helper for Walkthrough App"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3940#pullrequestreview-1205069689", "body": ""}
{"title": "Adds unblocked watermark to video composition", "number": 3941, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3941", "body": "Blends difference in pixels (poor-man's vibrancy). \nwidth = 10% screen size\nalpha = 40% + difference blending\n"}
{"comment": {"body": "Nice toque!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3941#issuecomment-1338450741"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3941#pullrequestreview-1205111424", "body": ""}
{"title": "Add TopicExpertModel", "number": 3942, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3942", "body": "This will allow us to create system recommended topic experts and let users to manually remove/add team members as topic experts."}
{"title": "Fix main break", "number": 3943, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3943"}
{"comment": {"body": "No idea how this happened when the merge build was successful...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3943#issuecomment-1338110098"}}
{"title": "Return topics experts", "number": 3944, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3944"}
{"title": "Refactor dashboard under team context", "number": 3945, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945", "body": "Search UI implies search tokens that are scoped under a team\nRefactor add adshboard routes and UIs to be under the /team/:teamId base route\nAdd TeamContext to hold state of currently selected team\nUpdate UI to include marker for user's team, as well as a team selector:\n\nNB: this inner dropdown only shows when there is >1 team \n\nNOTES\nIgnore the HomeSearch component and all the changes to the SearchContext.tsx file for now. I'm not using that component anywhere, more important to get the team refactor reviewed first"}
{"comment": {"body": "A bit of an aside, but we seem to have two `TeamContext`s now, which is confusing.  It looks like the old one is mostly used for invitations and @-mentions in the message editor and such.  As a follow-on task we should probably at least rename that (as a deprecated API), or potentially replace it with this new TeamContext.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#issuecomment-1338749504"}}
{"comment": {"body": "I believe there will need to be some updates on the backend as well. We have a bunch of link models with urls to the dashboard.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#issuecomment-1340081170"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1205785674", "body": ""}
{"comment": {"body": "I think we've generally discussed that topics will likely eventually need a display title property anyways (as stored on the service), so this will fit in nicely when that happens...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#discussion_r1040445674"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1205787367", "body": ""}
{"comment": {"body": "I know you mentioned not reviewing these files -- but I do think this is maybe telling us that we should just hold the state entirely in here and have a single onValueChanged callback.  I'm not sure I understand the value in having separate add/remove and value callbacks and methods.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#discussion_r1040446757"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1205789958", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1205792787", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1205796614", "body": ""}
{"comment": {"body": "We may want to map the URL path a bit more deliberately here, I could see things like `/settings` and `/share` unexpectedly existing in the middle of other routes.  Probably not a huge issue for now but something to think about.  We could either use regexes, or parse the URL path more manually or something...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#discussion_r1040452932"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1205798483", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1205816519", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1207401535", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1207417973", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1207433140", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1207434625", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1207442810", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210539969", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210540767", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210542499", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210547812", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210553667", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210877606", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210879079", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210882715", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210884923", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210885450", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210898841", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210899546", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210973190", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1210973847", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1212246565", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1212246908", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1212248533", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1212249481", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3945#pullrequestreview-1212250519", "body": ""}
{"title": "Update web contributors", "number": 3946, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3946", "body": "Update styling and functionality for contributors list a bit closer to design.\n\nTODO: Still some work in regards to the \"add\" button.\nNeeds refactoring of TeamMemberDropdown which will be a separate PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3946#pullrequestreview-1205427877", "body": ""}
{"comment": {"body": "Is it safe to have crypto here?\r\n\r\nEverything seems to build fine...\r\nAnd have tested out vscode and no obvious issues so far?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3946#discussion_r1040190875"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3946#pullrequestreview-1205463427", "body": ""}
{"comment": {"body": "You're using the standard web crypto API, and node has an implementation of it, so should be fine.  We definitely should test this on each platform though.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3946#discussion_r1040214938"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3946#pullrequestreview-1205820305", "body": ""}
{"comment": {"body": "I kind of have a preference to wrap these free-floating functions in a `GitContributorUtils` object, so we don't have to export and import all of them individually.  I'll leave it up to you whichever you prefer...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3946#discussion_r1040469161"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3946#pullrequestreview-1205822399", "body": ""}
{"title": "Filter pull request comments in GitHubAppClient.V3Org.pullRequestComments", "number": 3947, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3947", "body": "This was causing bulk ingestion of pull request TLCs to fail.\n"}
{"title": "Add topic pipeline", "number": 3948, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3948"}
{"comment": {"body": "All aboard the train to topic town \ud83d\ude85 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3948#issuecomment-1338350415"}}
{"title": "Add model for login state", "number": 3949, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3949", "body": "Model to add login state. Used to spec out state object being passed around in login urls.\nOne note, completion url will be base 64 encoded on the client side when passed to service.\nThis is necessary as the url is being passed as part of the query parameter (thought we were doing body at first)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3949#pullrequestreview-1205539284", "body": ""}
{"title": "Add getSourceMarks operation and add SourcePoints to SourceMark", "number": 395, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395", "body": "Part of the plan here:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-1144649475", "body": ""}
{"comment": {"body": "@matthewjamesadam : just working my way through the api spec, glad to know what the bar is for adding top level nouns are like **Topics**, right from VS code. :-)\n\n--\n\nComment posted using [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/5b99f0b7-9cf8-48ca-8d5b-38a9d329f8b7?message=1f4fd72d-548e-43de-b3be-29f193bf4bdf).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r997397871"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891276642", "body": ""}
{"comment": {"body": "Let\u2019s also create a top-level group called \u201cSourceMarks\u201d for this in `x-tagGroups`\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813030357"}}
{"comment": {"body": "Add isOriginal from other PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813046514"}}
{"comment": {"body": "Bit concerned about clients needing to pass the entire SP array as part of an UpdateMessageRequest. It would be weird for the client to have to serialize that entire array when they just want to edit message text.\r\n\r\nAlso there\u2019s a race, where another client could add new SourcePoints to the mark for new commits while you edit the message. In that case your message edit will appear to nuke the points added by another client.\r\n\r\nNot sure how to address this yet. One not so great option is to define two SourceMark models:\r\n- SourceMark \u2014 the model you have below, containing many points\r\n- InitialSourceMark \u2014 contains one `initialSourcePoint: SourcePoint` field, for use only in message updates and creates.\r\n\r\nNaming of InitialSourceMark sucks. Maybe shallowSourceMark or something.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813062845"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891330118", "body": ""}
{"comment": {"body": "Alternatively, keep one SourceMark model, which has two fields:\r\n- initialSourcePoint: SourcePoint (required)\r\n- otherSourcePoints: List<SourcePoint> (optional)\r\n\r\nClients are not required to specify the otherSourcePoints when editing, but that\u2019s a weird convention. Not a fan of this option.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813069097"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891341966", "body": ""}
{"comment": {"body": "Another benefit of splitting is that the update/create SourceMark requests can be forced to specify their single sourcemark as \u201coriginal\u201d and the server can validate this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813077612"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891406952", "body": ""}
{"comment": {"body": "This API needs to be last-modified fetchable, like getThreads, I think?  So it should take in an optional `If-Modified-Since` header, and return a `Last-Modified` header?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813124730"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891408529", "body": ""}
{"comment": {"body": "(This can go into a future PR if you want to get this stuff in first, of course, just thought I'd bring it up)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813125863"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891419606", "body": ""}
{"comment": {"body": "Thanks Matt! Let's add it now while we're in the area", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813133854"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891516365", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891574938", "body": "pending renaming changes  \nnice work Dave!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891588902", "body": ""}
{"comment": {"body": "Chatted in IRL\r\n\r\n> Bit concerned about clients needing to pass the entire SP array as part of an UpdateMessageRequest. It would be weird for the client to have to serialize that entire array when they just want to edit message text.\r\n\r\nSolution here is to change `UpdateMessageRequest` to have a list called `onlyNewSourceMarks` which the client will add to only if it needs new sourcemarks to be created as part of the update. The backend will be responsible for making sure all the required SourceMarks exist (and throw an error if the message body references a non existent SourceMark)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813252226"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891590169", "body": ""}
{"comment": {"body": "@matthewjamesadam @jeffrey-ng note changes, lmk if any concerns", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813253152"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891590492", "body": ""}
{"comment": {"body": "^(result of https://github.com/NextChapterSoftware/unblocked/pull/395/files#r813062845)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813253391"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891591474", "body": ""}
{"comment": {"body": "Also note the new `NewSourceMark` type", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813254117"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891652665", "body": ""}
{"comment": {"body": "Not sure what the experience is for updating a source mark is... When updating a message, there are three things in relation to a source mark.\r\n\r\n1. Update its location\r\n2. Create a new source mark in addition to the original one\r\n3. Delete the source mark?\r\n\r\n1 makes sense. Does 2 & 3 make sense? Can we have a message with multiple source marks or no source marks?\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813297788"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891654251", "body": ""}
{"comment": {"body": "`NewSourceMark` only needs a single `SourcePoint` and not a list since we're creating a new `SourceMark` from this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813299186"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891658729", "body": ""}
{"comment": {"body": "> Can we have a message with multiple source marks or no source marks?\r\n\r\nYes, a message can have multiple source marks (at least, that's what I understand) and a message can have no source marks (ex any message that is not the first one in a thread).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813302534"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-891659419", "body": ""}
{"comment": {"body": "> Delete the source mark?\r\n\r\nThe back end will inspect the body on update and delete any source marks in the DB that are no longer referenced by the newly updated message.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r813303084"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#pullrequestreview-893210849", "body": ""}
{"comment": {"body": "really awesome work here \u2014 kudos @davidkwlam \ud83c\udf89 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/395#discussion_r814430731"}}
{"title": "Wrong quueu", "number": 3950, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3950"}
{"title": "Update ActiveFileManager to correctly handle webviews", "number": 3951, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3951", "body": "Update ActiveFileManager to correctly handle interactions between text editors and webviews.  The general rule is: when a non-text-editor (ie a webview) is foregrounded, continue to display the last-focused text editor, as long as that text editor is visible.  This ensures that when our webviews are focused, we continue to display the insights from the last-focused editor.  If only a webview is visible, we will display all insights for the file associated with that webview.  I think this gives the best balance of behaviours, at least for now.\nThe ActiveFileManager has some subtle behaviour that could easily regress, so I decided to write unit tests.  Unfortunately this required mocking out a lot of VSCode interfaces (window, TextEditor, TextDocument, and others).  I made interfaces (like ITextEditor, ITextDocument, etc) for the various VSCode APIs, and associated mock classes (MockWindow, MockTextEditor), which we can use to mock these out in tests.  We can expand these interfaces and mocks and reuse them to write tests elsewhere."}
{"comment": {"body": "> The general rule is: when a non-text-editor (ie a webview) is foregrounded, continue to display the last-focused text editor, as long as that text editor is visible.\r\n\r\nHow does this work if there was no initial text editor? If we open a thread *with* a local file, the associated text editor would open and the active file manager should pick that up.\r\nBut if we open a thread with read only file, does that still work?\r\n\r\nhttps://user-images.githubusercontent.com/1553313/206030799-9cec44be-da88-47a6-9754-a3ee3de1274d.mp4\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3951#issuecomment-1340056965"}}
{"comment": {"body": "> > The general rule is: when a non-text-editor (ie a webview) is foregrounded, continue to display the last-focused text editor, as long as that text editor is visible.\r\n> \r\n> How does this work if there was no initial text editor? If we open a thread _with_ a local file, the associated text editor would open and the active file manager should pick that up. But if we open a thread with read only file, does that still work?\r\n\r\nExcellent question.  I will need to take a look at that, it might not work right.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3951#issuecomment-1340062188"}}
{"comment": {"body": "> > > The general rule is: when a non-text-editor (ie a webview) is foregrounded, continue to display the last-focused text editor, as long as that text editor is visible.\r\n> > \r\n> > \r\n> > How does this work if there was no initial text editor? If we open a thread _with_ a local file, the associated text editor would open and the active file manager should pick that up. But if we open a thread with read only file, does that still work?\r\n> \r\n> Excellent question. I will need to take a look at that, it might not work right.\r\n\r\nThis was a problem that would show up if you view a thread directly, without first having an editor with that file open, by clicking on it in the hub, or by clicking on it in the sidebar.  I think I've fixed this by reverting some of the logic, so we are once again holding the file association state for webviews.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3951#issuecomment-1340097818"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3951#pullrequestreview-1207389265", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3951#pullrequestreview-1207389532", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3951#pullrequestreview-1207390215", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3951#pullrequestreview-1207396247", "body": ""}
{"title": "[BREAKS API ON MAIN] Pass the client token through the state parameter", "number": 3952, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3952"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3952#pullrequestreview-1206892494", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3952#pullrequestreview-1206946778", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3952#pullrequestreview-1206986455", "body": ""}
{"title": "Add debugging", "number": 3953, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3953"}
{"title": "Debugging", "number": 3954, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3954"}
{"title": "Fix s3 processing", "number": 3955, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3955"}
{"title": "Add Topic Pipeline CDK Infra", "number": 3956, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3956", "body": "notebook\nBasic sagemaker step function pipeline\nAdd addtional processing jobs\nAdd docker images\nAdd model check\nFix bugs with glue s3 buckets\nAdd python scripts\nImprove stat machine\nTypescript code\nUpdate\nMove files around\nAdd trainer image\nBertopic part 1\nComment\nTopic output\nAdd processing job sagemaker step function\nUpdate processing script\nAdd ssl\nAdd lambda support\nMore fixes\nFix queue name\nLint\nAdd docker image construct\nUpdate script\nFix payload\nTry agian\nCrap never ends\nTest deployment"}
{"title": "Fix test", "number": 3957, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3957"}
{"title": "Redeploy", "number": 3958, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3958"}
{"title": "redeploy", "number": 3959, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3959"}
{"title": "Refactor Message Editor and MessageList", "number": 396, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396", "body": "Updates message editor API so that editor  Block translation is localized to the MessageEditor.\nIntroduced hooks for getContent & multiple callbacks to get hasContent state and caching purposes.\nNOTE: Removes JsonValue constraint for useWebviewState"}
{"comment": {"body": "want to get this in before my PR goes in because there are quite a few conflicts (I'm largely refactoring the StartDiscussion UI). that or my PR goes in first and this work updates that code \r\n\r\nis there still work to be done in the StartDiscussion.tsx file after this PR? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#issuecomment-1049093440"}}
{"comment": {"body": "> want to get this in before my PR goes in because there are quite a few conflicts (I'm largely refactoring the StartDiscussion UI). that or my PR goes in first and this work updates that code\r\n> \r\n> is there still work to be done in the StartDiscussion.tsx file after this PR?\r\n\r\nAt this point, no.\r\nMain updates were for compatibility with new editor API.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#issuecomment-1049202812"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891423291", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891493762", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891507261", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891662397", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891662998", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891778902", "body": ""}
{"comment": {"body": "We'll need to introduce a store to fetch thread participants.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813385242"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891809142", "body": ""}
{"comment": {"body": "maybe this can be passed up via the forwarded ref?? ie `ref.current?.hasContent`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813410211"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891810465", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891813029", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891813827", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891815141", "body": ""}
{"comment": {"body": "Should we just drop this cachedContent stuff for now in light of this discussion? https://chapter2global.slack.com/archives/C02US6PHTHR/p1645645419678579", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813415140"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891818881", "body": ""}
{"comment": {"body": "That was my initial thought but hasContent won't actually update and trigger a re render using `useImperativeHandle`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813418241"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891820763", "body": ""}
{"comment": {"body": "The plan is for ThreadParticipants to be a part of Thread: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1645115014626219", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813419721"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891823053", "body": ""}
{"comment": {"body": "does it not rerender even when passing in the boolean as a dependency? ie\r\n```\r\nuseImperativeHandle(ref, () => ({\r\n    hasContent: this.state.hasContent, // or wherever this value comes from\r\n}), [hasContent]);\r\n```\r\n\r\nor wait maybe I'm misreading..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813421551"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891823769", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891824343", "body": ""}
{"comment": {"body": "I'd lean towards keeping this since it's already here?`retainContextWhenHidden` will be a temporary workaround.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813422527"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891826432", "body": ""}
{"comment": {"body": "I remember reading that a while back -- will that be a list of models or a list of ids? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813424255"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891827010", "body": ""}
{"comment": {"body": "That's fie -- I'd maybe add some comments here to outline what these values are for.  initialContent vs cachedContent is pretty confusing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813424754"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891827433", "body": ""}
{"comment": {"body": "yeah and they're of different types too which is weird/not super easy to understand on first pass ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813425095"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891831515", "body": ""}
{"comment": {"body": "Yup. Had something very similar but the value within `MessageEditorController` didn't update.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813428615"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891833139", "body": ""}
{"comment": {"body": "It would need to be the models and not ID else we'd still need to call `getThreadParticipants", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813430018"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891834682", "body": ""}
{"comment": {"body": "The idea is that the client will keep a live (or read-through) cache of team members, mapped by ID, and the Thread will provide a list of IDs.  So from a thread, you'd ask the TeamMemberStore for all the relevant members by their ID.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813431283"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891839623", "body": ""}
{"comment": {"body": "So instead of calling `getThreadParticipants`, we would have a store that calls `getTeamMembers`?\nWith this, there's no need for the `ThreadParticipant` concept?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813435355"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891840473", "body": ""}
{"comment": {"body": "Yes, pretty much.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813436026"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891851341", "body": ""}
{"comment": {"body": "> Keep in mind that useRef doesn\u2019t notify you when its content changes. Mutating the .current property doesn\u2019t cause a re-render. If you want to run some code when React attaches or detaches a ref to a DOM node, you may want to use a [callback ref](https://reactjs.org/docs/hooks-faq.html#how-can-i-measure-a-dom-node)[](https://reactjs.org/docs/hooks-reference.html#useimperativehandle) instead.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813444888"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891863932", "body": "I'm good with this once you've addressed any outstanding issues from Kay"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891864677", "body": ""}
{"comment": {"body": "I can look into this but I've experimented a bit without success. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#discussion_r813455434"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/396#pullrequestreview-891877940", "body": ""}
{"title": "Fix json parsing", "number": 3960, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3960"}
{"title": "Fix action", "number": 3961, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3961"}
{"title": "Redeploy agian", "number": 3962, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3962"}
{"title": "Do not use null check", "number": 3963, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3963"}
{"title": "increase entries", "number": 3964, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3964"}
{"title": "Remove no longer needed code", "number": 3965, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3965"}
{"title": "Capitalize titles", "number": 3966, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3966"}
{"title": "Redirect user to page after auth", "number": 3967, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3967", "body": "If one tries to navigate to a page but is rejected due to RequireAuth, we will now redirect user to page post auth.\nWe have introduced a \"clientState\" option to loginOptions that allows the client to push stateful data within the \"state\" query parameter that is passed from unblocked to Github and back.\n\nEncode currentRoute when redirected from RequireAuth to Login\nShuttle route through auth flow\nAt LoginExchange, decode state and redirect to desired route.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3967#pullrequestreview-1207131194", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3967#pullrequestreview-1207149764", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3967#pullrequestreview-1207193853", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3967#pullrequestreview-1207193913", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3967#pullrequestreview-1207195145", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3967#pullrequestreview-1207220912", "body": ""}
{"title": "Web compose polish", "number": 3968, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3968", "body": "Slight polish\nMoved add button.\n\nUnsaved changes dialog: \n"}
{"comment": {"body": "@benedict-jw Will need some guidance on the unsaved dialog.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3968#issuecomment-1339887085"}}
{"comment": {"body": "Updated dialog\r\n<img width=\"1326\" alt=\"CleanShot 2022-12-06 at 11 59 18@2x\" src=\"https://user-images.githubusercontent.com/1553313/206010455-d0030bae-5c47-4e8f-9f4e-783b79349b44.png\">\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3968#issuecomment-1339934760"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3968#pullrequestreview-1207198842", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3968#pullrequestreview-1207412867", "body": ""}
{"comment": {"body": "You might need to use `useNavigationBlocker` to also tap into the native browser navigation (i.e. hitting the back button)?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3968#discussion_r1041514664"}}
{"title": "Generate topic experts", "number": 3969, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/3969"}
{"title": "No bother in naming", "number": 397, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/397"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/397#pullrequestreview-891512467", "body": ""}
