{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-958100312", "body": ""}
{"comment": {"body": "@mahdi-torabi FYI I had to add this, it was throwing errors when I tried sending messages on the standard `unblocked_pr_comments` queue. I don't know why it doesn't just ignore the field for non-FIFO queues, but I guess they went for fast-fail. https://docs.aws.amazon.com/AWSJavaSDK/latest/javadoc/com/amazonaws/services/sqs/model/SendMessageRequest.html#getMessageGroupId--\r\n\r\nFWIW AWS recommends checking the queue name to see if it is fifo https://docs.aws.amazon.com/AWSSimpleQueueService/latest/APIReference/API_GetQueueAttributes.html", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r862049227"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-958103470", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-964755967", "body": ""}
{"comment": {"body": "\r\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/6e607002-8730-4562-860a-d5a1c39c266e)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r866896701"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-964761615", "body": ""}
{"comment": {"body": "asdf", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r866900638"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-964763802", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/0e8c9e64-07a5-4c5e-b1b5-c333b83ffddd)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r866902204"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-964862421", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/9ae02b95-9e59-4b94-8556-8564a046a8ad)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r866969973"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-*********", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/799c5917-10ce-44cf-ae1d-cdad24649fb2)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r866970502"}}
{"title": "add new queue and servie account permissions", "number": 1052, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1052", "body": "Added new standard queue for PR comments \nAdded SQS SendMessage permissions to apiservice for both notifications queue and pr comments \nAdded ReceiveMessage permissions to scmservice\nModified existing perms to use wildcards in queue name postfixes \n\nEKS changes have already been deployed. Once CDK stuff are deployed I'll get you queue URLs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1052#pullrequestreview-*********", "body": ""}
{"title": "Ability to edit title of anchor message", "number": 1053, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1053", "body": "\n\n\n\nEditing anchor message now also enables the ability to edit the title of the thread. Save button saves both\nRefactor Input component into shared/ and style for each client\nRemove EditableInput title from web and web-extension"}
{"comment": {"body": "Nice! Couple of questions:\r\n\r\nFor GitHub, are we able to use their message editor, including their editing controls, or is that a long term goal?\r\n\r\nFor VSCode, can we add a margin between the title field and the description field?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1053#issuecomment-1112520114"}}
{"comment": {"body": "@benedict-jw styling - pushed a fix for the bug. re: GH message editor, definitely a long term goal. IIRC GitHub doesn't expose their editor as a component to be used externally so we might have to build out our own version of it :/ cc: @jeffrey-ng ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1053#issuecomment-1112533670"}}
{"comment": {"body": "> ...so we might have to build out our own version of it :/ cc: @jeffrey-ng\r\n\r\nDarn \u2014\u00a0that's unfortunate. Their design library has the component spec'd out at least, but good to know that it's a long term goal.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1053#issuecomment-1112543472"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1053#pullrequestreview-956902355", "body": ""}
{"title": "Fix issues with authentication parameters context being reset", "number": 1054, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1054", "body": "Tracked down the call that was resetting the Authentication parameters context.\nThe identityIdOrNull call in Monitoring.kt was resetting it.\nIt makes a call to application.principal(), which does some weird shit:\n```\npublic val ApplicationCall.authentication: AuthenticationContext\n    get() = AuthenticationContext.from(this)\npublic inline fun  ApplicationCall.principal(): P? = authentication.principal()\n```\nIm going to file a bug with the Ktors team, until then, this resolves the issues with tests and application."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1054#pullrequestreview-956877094", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1054#pullrequestreview-956942617", "body": ""}
{"title": "temp fix for a bug in CloudFront and CDK", "number": 1055, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1055", "body": "We were hitting a bug in CloudFormation. It didn't come up earlier because we only had one Fifo queue and no standard queues.  Added a workaround until they fix it. \nDeployed these changes manually and queues have been created. \nDev: \nProd: "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1055#pullrequestreview-956894840", "body": ""}
{"title": "Thread & Multiple source points for web extension", "number": 1056, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056", "body": "Add thread metadata to source points.\nSupport multiple source points on a single row.\n"}
{"comment": {"body": "Updated hover states\r\n\r\nhttps://user-images.githubusercontent.com/1553313/165863911-841a30c3-dd24-4097-8068-7f259e0eadd6.mp4\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#issuecomment-1112746360"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958387989", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958388272", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958388630", "body": ""}
{"comment": {"body": "not necessarily for this PR but I feel like we should have a shared keyword or a common note for when we do this, because I feel like we have several versions of the same comment sprinkled throughout our extension style files", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862254776"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958389701", "body": ""}
{"comment": {"body": "will `sourceMarkThreads` ever be empty/do we need to do a check for that?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862255716"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958390049", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958390501", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958390589", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958391778", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958392494", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958392904", "body": ""}
{"comment": {"body": "\r\nWe don't want a common classname for all these custom styles. I think the ub-prefix should be clear enough for our use case without adding too much complexity. I didn't want to build out a whole system for the few places we do this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862258410"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958393114", "body": ""}
{"comment": {"body": "It's technically guaranteed as this component would not render without at least one. There isn't a great way to type that tough without adding a bunch of boilerplate...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862258567"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958393197", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958393407", "body": ""}
{"comment": {"body": "This is getting messy -- build/dist, we should probably make this consistent...?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862258864"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958394280", "body": ""}
{"comment": {"body": "I have a dist folder within the build folder...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862259602"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958400768", "body": ""}
{"comment": {"body": "The stuff in this file is wild.  Well done.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862265103"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958400933", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-958410020", "body": ""}
{"comment": {"body": "Are there parts of this that can be refactored into a hook? Aka the highlighting logic.. just trying to see how we can extend this to vscode ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862273784"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959254068", "body": ""}
{"comment": {"body": "It looks like the hilighting logic is pretty tightly tied to GH's web structure.  I don't see how we can reuse much of this but I'll let Jeff be the judge here...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862974421"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959281976", "body": ""}
{"comment": {"body": "Some of the range stuff could be pulled out potentially? I think it may be better to reafactor that out when working on VSCode as I'm not too sure what's necessary.\r\n\r\n None of the rendering stuff should be shared. That's all GH specific.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862994271"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959282388", "body": ""}
{"comment": {"body": "~Ok aside from just the highlighting logic is there anything we can reuse? Maybe I'm just naively understanding the code but could we not pass through the highlighting fn into these components for reuse?~\r\n\r\nEdit: ok just read Jeff's comment, that's fine", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r862994593"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959346955", "body": ""}
{"comment": {"body": "nono not a common classname but a common phrase as a comment for when we copy the exact GH styling, we do this in a lot of places. It's not a pressing issue but it would be helpful for legibility/uniformity as a long term thing IMO\r\n\r\nedit: the prefix is another approach -- I would argue then we should prefix it with `.gh--` or something of the like to indicate it's a copied GH styling. Again it's not a pressing or blocking thing, just something I've been thinking about since noticed it's come up a lot ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r863039973"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959352848", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959353433", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959411323", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959581852", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959583646", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959603172", "body": ""}
{"comment": {"body": "FYI since Maps are held by reference there's not any real benefit to using `reduce` here over iterating.  Doesn't really matter either way.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r863218006"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959605728", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959606769", "body": ""}
{"comment": {"body": "This is only called in /blob pages, so we know for sure this is true?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r863220696"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959618889", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-959660391", "body": ""}
{"comment": {"body": "According to the npm package, yes...\r\n\r\nI've played around and it seems correct, for at least blob pages.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r863259800"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-966514899", "body": ""}
{"comment": {"body": "test test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r868222070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-966517441", "body": ""}
{"comment": {"body": "hello matt\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r868223069"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#pullrequestreview-966518391", "body": ""}
{"comment": {"body": "very interesting\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1056#discussion_r868223435"}}
{"title": "[DO NOT MERGE] Validate message content", "number": 1057, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1057", "body": "We're seeing some instances where the message content coming from clients can't be deserialized to our protobuf format. We need to deserialize before we can post the message back to GitHub (if the message is for a PR ingested thread)\nAdding some checks here to validate the content at the API layer before doing anything with it so that we fast fail."}
{"comment": {"body": "I think this has been largely fixed with the move to proto3. Let's revisit this if this becomes an issue, we have logs that will surface.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1057#issuecomment-1123989942"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1057#pullrequestreview-956932520", "body": "let's catch and 400. otherwise good "}
{"comment": {"body": "This will 500 right? This will cause client to retry, which is pointless, since it's not a server issue.\r\n\r\nI think we should catch in the exception handler and respond with 400, since the client made a bad request.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1057#discussion_r861235279"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1057#pullrequestreview-956934044", "body": ""}
{"comment": {"body": "see `Application.configureStatusPages()` where you can inspect the exception", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1057#discussion_r861236438"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1057#pullrequestreview-957018689", "body": ""}
{"comment": {"body": "maybe add an ApiImplTest for this \ud83d\ude2c ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1057#discussion_r861295242"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1057#pullrequestreview-957175937", "body": ""}
{"title": "remove retool access", "number": 1058, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1058", "body": "Removed retool user and public key \nRemoved bastion host access to prod db \n\nThis change will cause our bastion host to be recreated. I will merge it after hours to avoid breaking our CI/CD"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1058#pullrequestreview-956967130", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1058#pullrequestreview-957189201", "body": ""}
{"title": "Update unread marker in sidebar", "number": 1059, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1059", "body": "Per new designs from Ben:\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1059#pullrequestreview-956992152", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1059#pullrequestreview-956994876", "body": ""}
{"comment": {"body": "Looking at this in VSCode, 6pt spacer is unevenly distributing the discussion icon between the unread dot and the label. There's more space between the unread dot and the icon, than the unread dot and the label.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1059#discussion_r861278386"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1059#pullrequestreview-957050504", "body": ""}
{"title": "Revert \"hack\"", "number": 106, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/106", "body": "This reverts commit b87ca28f9d5a3d93fd1d3d3171f40795ee3d1f95 from https://github.com/Chapter2Inc/codeswell/pull/105.\nDo not merge this right away. Once everyone has run ./gradlew check locally then this can be safely merged."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/106#pullrequestreview-860145384", "body": ""}
{"title": "Sidebar extension Styling", "number": 1060, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060", "body": "Slight tweaks to sidebar styling.\nRemove subheader\nFixed header height to match GH header height\nIncrease sidebar width to 260\n"}
{"comment": {"body": "Updated sizes\r\n\r\n<img width=\"676\" alt=\"CleanShot 2022-04-28 at 14 55 38@2x\" src=\"https://user-images.githubusercontent.com/1553313/165854409-64154f30-c62e-4feb-b1b3-084ed87fb125.png\">\r\n:", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060#issuecomment-1112688276"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060#pullrequestreview-957053860", "body": ""}
{"comment": {"body": "rm?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060#discussion_r861320351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060#pullrequestreview-957054554", "body": ""}
{"comment": {"body": "would have thought stylelint would fail the build here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060#discussion_r861320858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060#pullrequestreview-957054665", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060#pullrequestreview-957060384", "body": ""}
{"comment": {"body": "There's a bug with style lint right now for web extension... It's fixed in my other PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060#discussion_r861325150"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1060#pullrequestreview-957094975", "body": ""}
{"title": "Suppress flaky BackgroundJobsTest", "number": 1061, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1061", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1061#pullrequestreview-957029667", "body": ""}
{"title": "Remove hardcoded fixtures from DEV", "number": 1062, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1062", "body": "Still need for local env, so not removing much here.\nNote: I do think we have some lingering dependencies from client code on repo and team IDs. I don't think we'll fully realize those dependencies until we nuke the DEV DB."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1062#pullrequestreview-957143222", "body": ""}
{"title": "Open discussion based on URL for web extension", "number": 1063, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1063", "body": "Based on threadID in #, open discussion.\nOnly works for thread IDs atm. Not sure how we're going to support messages with hash parameters...\nLogic currently owned by extension's sidebar as there's some state that needs to be modified there.\nTODO: better error state.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1063#pullrequestreview-957082792", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1063#pullrequestreview-957092887", "body": ""}
{"title": "Show person identities, and some admin fixes", "number": 1065, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1065"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1065#pullrequestreview-957082681", "body": ""}
{"comment": {"body": "fyi @davidkwlam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1065#discussion_r861335730"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1065#pullrequestreview-957082971", "body": ""}
{"title": "Kinda fix the duplicate person bug", "number": 1066, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1066", "body": "I have low confidence in this change."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1066#pullrequestreview-957144294", "body": "This works because we're only sucking down emails that are validated. It will break / be open to hijack attacks if we ever stop doing this. I wonder how we can codify this..."}
{"title": "Add pre-auth endpoints", "number": 107, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107", "body": "Problem\nNative clients can't participate in the OAuth flow. In order for a native client to obtain an access token, they must piggyback on the OAuth flow and obtain the token out-of-band. \nProposal\nWe introduce two new endpoints: /preauth and /preauth/exchange. These endpoints facilitate the exchange of an initial client secret, which becomes associated with the server generated client nonce and piggybacks the auth flow until finally being associated with an authentication event. \nThe native client can then poll in the background while the user authenticates through the browser, and then upgrade their preauth token for a general access token.\nThe flow is illustrated in github_app_based_user_auth_sequence.puml\nAdditionally\nThis PR includes a few other minor additions:\n1. Adoption of API changes introduced in https://github.com/Chapter2Inc/codeswell/pull/96\n2. Introduction of an AuthenticationConfig for hocon-based environment loading of secrets\n3. Slight refactor of token configuration to make it more portable for testing"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861267813", "body": ""}
{"comment": {"body": "So auth0 OSS, and not the actual saas offering? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r790971110"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861270418", "body": ""}
{"comment": {"body": "Are we exchanging the `PreAuthTokenSecret` with this endpoint?\r\n\r\nSee no \"parameters\" for this secret. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r790972903"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861317731", "body": ""}
{"comment": {"body": "So we set the `PreAuthTokenSecret`.token as the bearer here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791006732"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861345816", "body": ""}
{"comment": {"body": "Should this be required?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791027011"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861349812", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861426075", "body": ""}
{"comment": {"body": "What do you mean?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791085123"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861427699", "body": ""}
{"comment": {"body": "The secret is extracted from the preauth jwt, which is included in the AuthorizationHeader for this request. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791086285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861427798", "body": ""}
{"comment": {"body": "That's correct", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791086348"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861428248", "body": ""}
{"comment": {"body": "What parameter are you referring to?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791086726"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861439208", "body": ""}
{"comment": {"body": "Looks like you're referencing an auth0 library?\r\n\r\nSeems like we're pulling in references to both the ktor.jwt and auth0.jwt.\r\n\r\nWas wondering if that was intentional", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791094898"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861439989", "body": ""}
{"comment": {"body": "The query parameter client-type.\r\n\r\nIf it's not set, does the backend infer based on user-agent?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791095464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861442351", "body": ""}
{"comment": {"body": "Ktor uses auth0 interchangeably. If you peek under the hood at the types that the Ktor API expects, you'll see they are all `com.auth0` types wrapped. Ktor doesn't provide any facilities for actual JWT creation and decoding.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791097131"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#pullrequestreview-861457457", "body": ""}
{"comment": {"body": "I think this is a reasonable thing for us to do. Let's imagine a user clicks on a link for a protected resource outside the browser. If we weren't using user agent, then the web-app would have to make a determination about client-type, and it would need to inspect the url for the presence of the parameter. Feels messy when the service can just use a sensible default based on the UAProf", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/107#discussion_r791108180"}}
{"title": "Handle top-level image", "number": 1070, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1070"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1070#pullrequestreview-957118010", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1070#pullrequestreview-957125195", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1070#pullrequestreview-957145087", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1070#pullrequestreview-957145546", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1070#pullrequestreview-957175352", "body": ""}
{"title": "Threads page correctly filters by repo", "number": 1071, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1071"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1071#pullrequestreview-957145303", "body": ""}
{"comment": {"body": "ewwwww", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1071#discussion_r861383559"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1071#pullrequestreview-957145450", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1071#pullrequestreview-957160858", "body": ""}
{"comment": {"body": "It tops out at 3; after that Kotlin best practice is to use data class as container. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1071#discussion_r861395482"}}
{"title": "Remove unused field", "number": 1072, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1072"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1072#pullrequestreview-957134445", "body": "looks good.\nas discussed, let's move prCommentHtmlUrl to MessageModel"}
{"title": "Slack notification for releases", "number": 1073, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1073", "body": "Setup slack notification for client releases to #client-releases"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1073#pullrequestreview-958095577", "body": ""}
{"title": "Some extension style fixes", "number": 1074, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1074", "body": "Remove the logout button (flagged off for non-local environments)\nMake sure the thread title doesn't wrap\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1074#pullrequestreview-957136484", "body": ""}
{"title": "Point local services to LocalStack AWS services", "number": 1075, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075", "body": "Modified AWS provider to support a special local region. This region is used to point AWS clients to local instances of aws services run via localstack\nChanged our configs so we only have to supply full queue names e.g email_notifications-.fifo or 'unblocked_pr_comments-standard'\nModified global, prod and local configs to reflect the change mentioned above\nRemoved queue configs from prod since it's now identical to global one\nAdded a special config to local config file with region set to local\nModified notification service to reflect AWSCLI and Config changes\nUpdated notification service tests and MockAWSProvider class\nAdded an init script to localstack docker compose config to create queues used for running local services\nAdded a health check to docker compose files to prevent services from running until localstack has finished init\nRemoved the last run config based on profiles. We no longer need it and instead we only need to set dummy environment values for environment variables e.g look at .run/NotificationService.run.xml\n\nTested locally and it works. I can run tests as well as running local services against localstack."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#pullrequestreview-**********", "body": ""}
{"comment": {"body": "Jeff is REALLy handsome\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4b1091f7-3c12-42b0-bba1-e33eff1ca1b3?message=2295b8ad-c7e8-4a9d-bfa8-72f8de223eaa).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#discussion_r904194602"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#pullrequestreview-1015866582", "body": ""}
{"comment": {"body": "test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/ecd27483-240c-4fb2-b8ab-9fd8e8953132?message=0787e1ea-8daf-40bf-81d2-ff3e56a189fa).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#discussion_r904221270"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#pullrequestreview-957254709", "body": ""}
{"comment": {"body": "localstack doesn't care about the actual values but AWS cli/client-lib requires them. They need to be set for other services as needed. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#discussion_r861464689"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#pullrequestreview-957254961", "body": ""}
{"comment": {"body": "Init script used to configure local stack services like creating queues or buckets etc. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#discussion_r861464872"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#pullrequestreview-957255317", "body": ""}
{"comment": {"body": "`local` region is special and AWSProvider class uses it to figure out when to point things at localstack (docker compose service running locally) endpoint", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#discussion_r861465115"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1075#pullrequestreview-957257394", "body": "LGTM I'll let you get this in before my PR"}
{"title": "Add support for Localstack api to local services stack", "number": 1077, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077", "body": "Modified Make target for dropping local-stack db so it removes orphaned services first\nUpdated AWSProvider class to accept env var overrides for AWS endpoint. This is used within local compose environment\nAdded new env vars for Localstack AWS services to compose config\nFixed a mistake in notification service dockerfile. We were pushing the wrong jar and somehow it wasn't caught by checks. I am not sure how we even managed to deploy the service this far."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#pullrequestreview-958043202", "body": ""}
{"comment": {"body": "Not sure how the heck the service didn't crash! ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#discussion_r862009224"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#pullrequestreview-958045717", "body": ""}
{"comment": {"body": "We need to extend our health probes so in any service that needs a queue it would check it as part of deepcheck", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#discussion_r862011024"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#pullrequestreview-958046045", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#pullrequestreview-958047008", "body": ""}
{"comment": {"body": "I'll tell you how. :) It was running the pusherservice. :)\r\nWe bundle up all the jars together and then cherry pick the jar we want when we build the image.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#discussion_r862011904"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#pullrequestreview-958047112", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#pullrequestreview-958053492", "body": ""}
{"comment": {"body": "I see. I think the other part of this was that notification service has Redis and DB access which it doesn't need (for some Goddamn reason it still hits the DB on startup)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#discussion_r862016588"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#pullrequestreview-964892338", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/0c95ccf9-cf6d-4308-977d-02e039100122)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#discussion_r866990632"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#pullrequestreview-964904562", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/06d88d62-cc89-49bc-9244-7cbde1017cc3)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1077#discussion_r866999076"}}
{"title": "Don't post messages to github if we cant deserialize", "number": 1078, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1078"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1078#pullrequestreview-958054531", "body": ""}
{"title": "Better logging", "number": 1079, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1079"}
{"title": "Add token refresh endpoint", "number": 108, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/108"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/108#pullrequestreview-861351163", "body": ""}
{"comment": {"body": "RefreshToken should also be required?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/108#discussion_r791030848"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/108#pullrequestreview-861352566", "body": ""}
{"comment": {"body": "Unless we want the client to successfully authenticate but have a limited auth session.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/108#discussion_r791031912"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/108#pullrequestreview-861431287", "body": ""}
{"comment": {"body": "Good point, I had initially included the preauth token in this but separated it into its own type. I don't think there is a use case where we wouldn't send back a refresh token", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/108#discussion_r791088955"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/108#pullrequestreview-861442137", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/108#pullrequestreview-861442924", "body": ""}
{"title": "Convert Message.proto to proto3", "number": 1080, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#pullrequestreview-958660187", "body": ""}
{"comment": {"body": "If there are no `required` fields, then what is the meaning of `optional`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#discussion_r862549043"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#pullrequestreview-958692698", "body": ""}
{"comment": {"body": "@matthewjamesadam and I are just trying something out. On the server side, it doesn't add much other than the additional property `has<field>` that lets us check that `<field>` was set by the client.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#discussion_r862574265"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#pullrequestreview-959259969", "body": ""}
{"comment": {"body": "There is a difference:\r\n\r\n* Non-optional means \"I expect that this is a part of the message\". If the value isn't a part of the message, on deserialization a default value will be provided (this seems consistent between kotlin and TS).  In effect, a default value (ie, zero or empty string) is considered the same thing as a value that isn't provided at all.\r\n* `optional` means \"I expect that this value might not be defined or provided, and I will need to be able to tell the difference between an unset value and a default value\".  In TS this is deserialized as an optional value (ie `my value?: string | undefined`).  In kotlin (I believe) an unset value will be deserialized to the default value, but you can use the `hasField` accessor to determine if it was set or not.\r\n\r\nThe expectations here are different then OpenAPI, for sure.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#discussion_r862978544"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#pullrequestreview-959272658", "body": ""}
{"comment": {"body": "Woah, this is really informative. We should probably tell the group about this, as this is well worded.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#discussion_r862987501"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#pullrequestreview-959415222", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#pullrequestreview-959418446", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#pullrequestreview-964067070", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1080#pullrequestreview-964730547", "body": ""}
{"title": "More style fixes on clients", "number": 1081, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1081", "body": "Fix web dashboard code block overflow, \n\n\n\n\nFix line height in message view component\n\n\n\n\nAdd link to pr comment in message if it exists\n"}
{"comment": {"body": "Most context or \"more\" menus in VSCode just look like the OS menus. Can we follow these styles? \r\n<img width=\"264\" alt=\"CleanShot 2022-04-29 at 15 00 16@2x\" src=\"https://user-images.githubusercontent.com/13353189/166074463-2b8df5a1-cccf-4ec2-935c-7cb8626d06df.png\">\r\n\r\n ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1081#issuecomment-1113788481"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1081#pullrequestreview-958388843", "body": ""}
{"title": "Use query params instead of hash", "number": 1082, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1082", "body": "Hashes were conflicting with GH.\nSwitch to query parameters which uses keys to prevent conflicts. Seems to work as expected. No reloads"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1082#pullrequestreview-958298810", "body": "And this doesn't trigger a refresh, correct? I'll pull this into mine once it's merged"}
{"title": "Show UI when sourcepoint can't be resolved", "number": 1083, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1083", "body": "\nI used a TextDocumentContentProvider to provide text content to an editor without needing to save it on disk.  I might change this to a webview, which would allow word-wrapping."}
{"comment": {"body": "Note: Dennis wants this checked in with this placeholder UI for demoing on Monday.  We will iterate on the actual UI displayed next week.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1083#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1083#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Note: providing a real path here causes problems, as it enables syntax hilighting depending on the file extension.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1083#discussion_r862191676"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1083#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Can we set the title for this window?\r\n\r\nI think it's currently set to `.`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1083#discussion_r862254532"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1083#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1083#pullrequestreview-*********", "body": ""}
{"comment": {"body": "We can't -- it has to take the file name.  We'll almost certainly switch this over to a webview next week to show a more detailed UI, which we *can* set the title for.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1083#discussion_r862255120"}}
{"title": "Handle multiple original sourcepoints", "number": 1084, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084", "body": "This changes also prunes original sourcepoints that do not exist in\nthe current Git workspace, which will prevent points added \"after\"\nthe current revision from being considered for recalculation.\nRelated:\n- https://chapter2global.slack.com/archives/C036YH3QF7T/p1651265002423059\n- https://github.com/NextChapterSoftware/unblocked/pull/1043\n- https://github.com/NextChapterSoftware/unblocked/pull/1048#discussion_r860429652"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#pullrequestreview-958344534", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#pullrequestreview-958391186", "body": ""}
{"comment": {"body": "You okay wiht this spitting out an exception and aborting early? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#discussion_r862257052"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#pullrequestreview-958395164", "body": ""}
{"comment": {"body": "it doesn't throw", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#discussion_r862260329"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#pullrequestreview-958395985", "body": ""}
{"comment": {"body": "ah, I see what you mean. yup, I'll wrap...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#discussion_r862260946"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#pullrequestreview-958396071", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#pullrequestreview-958397070", "body": ""}
{"comment": {"body": "done https://github.com/NextChapterSoftware/unblocked/pull/1084/commits/eb64c0cdd80ee4591f225ead055e34e62bb3eaa1", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#discussion_r862261942"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#pullrequestreview-958397102", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#pullrequestreview-964733734", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/4bd800cd-8ae4-4eb4-b124-9318a4b1e40b)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/f62890a7-c9fb-47c5-b38b-fcf5a29265c5)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/fcd4d79c-274e-4cc3-857e-0e35e38315da)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1084#discussion_r866882088"}}
{"title": "Link to unread pages", "number": 1085, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1085"}
{"title": "Updated slack notify", "number": 1086, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1086"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1086#pullrequestreview-958349049", "body": ""}
{"title": "Fix slack notify", "number": 1088, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1088", "body": "Slack notify failing due to invalid JSON from commit message.\nTemporarily removing to unblock."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1088#pullrequestreview-958399892", "body": ""}
{"title": "Apply markdown to trimmed text only", "number": 1089, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1089", "body": "FormattedText inline elements from our clients can include leading or trailing whitespaces. When we convert to markdown, this can cause issues. For example:\n**_This is my text _**\nrenders to\nThis is my text \ninstead of\nThis is my text \nbecause of the the whitespace before the closing _.\nThis PR strips the whitespace before applying the markdown formatting then adds it back."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1089#pullrequestreview-*********", "body": ""}
{"title": "cleanup build-config to make network stack more generic", "number": 109, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/109", "body": "Fixed incorrect environment name in Dev json \nGrouped a all AWS related params (account ID, root region etc)\nChanged the coreVPC build-config to include VPC peering targets instead of having the hardcoded \nDefined a simplified interface for config expected by network-stack. This way we can call it with both build-config object types (regular and secOps types)\nCleanup unused configs \nContext json was updated as a result of changing some code around VPC imports but no functionality has been changed. \n\nMade sure the new changes are compatible with Dev (no changes) and also applied the coreVPC (network-stack) change to SecOps (prep for OpenVPN work)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/109#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Thanks. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/109#discussion_r790979760"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/109#pullrequestreview-*********", "body": ""}
{"title": "Add sourcemarks page", "number": 1090, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1090"}
{"title": "Post messages to GitHub through the SCM service", "number": 1091, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1091", "body": "Moves it out of the API service to make creating, updating, and deleting messages snappier."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1091#pullrequestreview-959375818", "body": ""}
{"title": "Admin web searches more things", "number": 1092, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1092"}
{"title": "Admin statistics page", "number": 1093, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1093"}
{"title": "Sourcemark engine handles whole-line code moves", "number": 1094, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1094", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1094#pullrequestreview-961191554", "body": "GIDDYUP"}
{"comment": {"body": "I guess this function is fine without a return type?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1094#discussion_r864335054"}}
{"comment": {"body": "or is it inferred...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1094#discussion_r864335497"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1094#pullrequestreview-961197103", "body": ""}
{"comment": {"body": "Yeah, if it isn't explicitly defined, it is inferred from the union of all values that were actually returned.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1094#discussion_r864339721"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1094#pullrequestreview-961199112", "body": ""}
{"comment": {"body": "I like return types for public functions/methods because it makes it easier to consume other modules, but I think we can be more lax for private functions/methods.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1094#discussion_r864341391"}}
{"title": "Self-hosted runners for service builds", "number": 1095, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1095", "body": "Added IAM roles and policies needed for deploybot to create and manage EC2 instances\nModified services workflow to use a self hosted runner when running service builds (drops our build time to 10 mins) \n\nTODOs:\n- Docker pulls are taking about 1 min on the self hosted runner. I'll cache those images with the next AMI \n- We need to optimize the EC2 cost (currently 5 cents a build) and either use spot instances or purchase long term EC2 allocation"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1095#pullrequestreview-959605277", "body": ""}
{"title": "Using Embedded Postgres to parallelize tests", "number": 1096, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1096", "body": "This pr moves us away from using docker for postgres for tests.\nWe are now adding a custom Database base test class with initializer that uses an embedded postgres process for tests. \nThis allows each db test to run independent of the next when it comes to db modifications.\nIn tandem:\n1. We are moving to Junit5 (Jupiter), which has proper parallelization framework.\n2. We are using embedded postgres libraries.\n3. Adding dependency on a good retry library (Failsafe, resilience4j is a piece of shit comparatively)\nOther benefits:\n1. No more reliance on static initialization of Postgres for tests.\nIn summary, this basically means much faster test runs (assuming we move to custom test runners with more cores (github actions test runners are pieces of shit) @mahdi-torabi"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1096#pullrequestreview-958653235", "body": "cool.\nany system resource constraints on the parallelization? (I ask because I see the socket code)"}
{"comment": {"body": "is it possible to declare dependencies in the root project that all sub-projects inherit?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1096#discussion_r862543491"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1096#pullrequestreview-958658647", "body": ""}
{"comment": {"body": "although, on second thought, we probably have services that don't need DB (so no zonky for webhook-service)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1096#discussion_r862547878"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1096#pullrequestreview-958658684", "body": ""}
{"comment": {"body": "Yeah, trying to figure that out ATM.\r\nI hated doing that, just trying to get this going quickly as It's a massive merge conflict hell for me. :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1096#discussion_r862547904"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1096#pullrequestreview-964732802", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/1127409c-81bd-46dc-807d-9d7e6bd1a15e)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/68b58d24-303b-4492-87e3-b9de87e938cb)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/a3f1eb49-04ba-4e2b-b639-d8d5112331b1)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1096#discussion_r866881658"}}
{"title": "Save pr comment URL on MessageModel", "number": 1097, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1097"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1097#pullrequestreview-958659797", "body": ""}
{"title": "SM engine ignores space changes", "number": 1098, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1098"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1098#pullrequestreview-959567037", "body": ""}
{"title": "Fix sourcemark admin page", "number": 1099, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1099", "body": "Fixes this broken page:\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1099#pullrequestreview-*********", "body": ""}
{"title": "Update ci main", "number": 11, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/11"}
{"title": "Cdk openvpn setup", "number": 110, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/110", "body": "Some cosmetic changes in database-stack\nAdded Openvpn stack to create the following:\nSecurity Group\nEC2 Instance \nAllocate elastic IP\nCreate DNS A record pointing to elastic ip \n\nThese changes are deployed to us-west-2 region in sec-ops account only. We do not support running VPN service in other regions and the code will throw an exception if anyone attempts to do so. \nIMPORTANT NOTE: Due to limitations in AWS CDK, peering connections and static routes for OpenVPN traffic are setup manually."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/110#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/110#pullrequestreview-*********", "body": "Minor comment"}
{"title": "Ktor 2.0.1", "number": 1100, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1100"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1100#pullrequestreview-*********", "body": ""}
{"title": "update", "number": 1101, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1101"}
{"title": "Add Title to slack releases", "number": 1102, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1102"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1102#pullrequestreview-959272650", "body": ""}
{"title": "Update navigate path in dashboard thread view", "number": 1105, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1105", "body": "\nNOTE: this still isn't perfect (if the user is looking at a team discussion, this would navigate back to /mine regardless) but at least is more accurate than what it was. I'm not sure how to accurately do this without having to do a bunch of prop passing or param state to know whether or not it's a /team or /mine thread - decided to forgo doing any of this since we're rethinking the sidebar organization anyway"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1105#pullrequestreview-959389470", "body": ""}
{"title": "Fix MessageEditor spacing", "number": 1106, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1106", "body": "Make sure that all clients have a consistent minimum height for the message editor\nClicking on the padding area for a message editor now focuses the editor\n\nI copied the styling here for each of the clients -- there actually is no other shared CSS for the message editor. I don't know, maybe we should make one...?\n\n\n"}
{"comment": {"body": "Regarding shared styles, my intuition is that each client might have slightly different values here. VSCode font sizing and line height is different than GitHub's for example. \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1106#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1106#pullrequestreview-*********", "body": ""}
{"title": "Create new queues and service account for search", "number": 1107, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1107", "body": "Added two new standard queues pr_ingestion and search_indexing\nAdded Producer/Consumer permissions on pr_ingestion to scm service\nAdded Producer permission on search_indexing to API service\nCreated a new service account for Search Service with Postgres permission\nAdded Consumer permission on search_indexing to Search service (to be created)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1107#pullrequestreview-*********", "body": "Thanks!"}
{"title": "Add better messaging", "number": 1108, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1108"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1108#pullrequestreview-*********", "body": ""}
{"title": "Pass in vertical align value to sm icon", "number": 1109, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1109", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1109#pullrequestreview-*********", "body": ""}
{"title": "Add authentication state model to facilitate auth flow", "number": 111, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/111", "body": "Problem\nDuring the authentication flow there are 2 stateful requirements:\n1. We must be able to validate the nonce and secret we're seeing from the client. If either are invalid, we need to drop the authentication flow.\n2. We need the ability to tie a successful authentication event to a pre-auth token, so that a native client can perform an access token exchange out-of-band of the OAuth flow.\nProposal\nIntroduce an AuthenticationState model that associates an Identity, nonce, and secret. The presence of the Identity denotes a successful authentication event. \nIdeally, this model would auto-expire after some period of time. Additionally, when an authentication flow completes, all previous AuthenticationState objects for that Identity should be deleted."}
{"comment": {"body": "To clarify, this model is just to simplify the backend for this part of the auth process?\r\n<img width=\"2343\" alt=\"CleanShot 2022-01-24 at 12 01 05@2x\" src=\"https://user-images.githubusercontent.com/1553313/150855745-08b87fec-2bb1-491f-bf58-fa13facaf1b9.png\">\r\n\r\nThis doesn't handle the preach situation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/111#issuecomment-1020491663"}}
{"comment": {"body": "> To clarify, this model is just to simplify the backend for this part of the auth process? <img alt=\"CleanShot 2022-01-24 at 12 01 05@2x\" width=\"2000\" src=\"https://user-images.githubusercontent.com/1553313/150855745-08b87fec-2bb1-491f-bf58-fa13facaf1b9.png\">\r\n> \r\n> This doesn't handle the preach situation.\r\n\r\nIt does - the initial secret that the service returns from `/preauth` is passed to `/login`, which connects the auth flow all the way to `/preauth/exchange`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/111#issuecomment-1020546307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/111#pullrequestreview-861519475", "body": ""}
{"title": "Load threads and team member data for realz", "number": 1110, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110", "body": "Summary\nThis PR sets up the stores and rips down all the data. It doesn't poll yet, so there are no notifications (coming next). Current behaviour is that it will re-fetch the data every time the hub is opened, so you may see some cached data load in followed by an update that is currently unanimated.\nCaveats\nWe're currently using AsyncImage for avatar rendering, which is a macOS 12.0 API. If the user is on 11.0 they will see a grey circle. We'll have to implement this component ourselves (or borrow).\nThe networking is pretty heavy given the lack of polling, so we're ripping down all the threads on every request. Overall this can take up to 10 seconds to complete (like the dashboard). \nPreviews\n\n\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-959518741", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-959593664", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-959672166", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960741824", "body": ""}
{"comment": {"body": "It's safe to have this in our repo for all time, right?  Your UUID will probably change next time we drop the DB?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864022315"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960743965", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960745733", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960775722", "body": ""}
{"comment": {"body": "It might be unexpected that calling this could async wait forever (for auth to succeed)... depends on context I guess?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864046716"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960799932", "body": ""}
{"comment": {"body": "Doesn't look like these (load/saveToDisk) are being used right now, I'm guessing that will come later?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864064166"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960801479", "body": ""}
{"comment": {"body": "Do we need to make sure folderURLs isn't empty?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864065242"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960803893", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960810959", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960975369", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960977653", "body": ""}
{"comment": {"body": "I'm guessing that this is meant to eventually contain a teamId?  ie there would be a separate TeamMemberStore and cache per team?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864166096"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960980684", "body": ""}
{"comment": {"body": "UUID doesn't really give anyone anything, but yeah this is for godmode stuff and so will become irrelevant soon", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864167606"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960981210", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960981979", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960992551", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-960998541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961001145", "body": ""}
{"comment": {"body": "I think the API call with these parameters is guaranteed to succeed but I'll add a check here for correctness", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864188303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961002935", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961003991", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961005998", "body": ""}
{"comment": {"body": "That was the original plan but I'm not sure if it actually makes sense to do that. Currently I'm grouping everything together", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864193787"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961006853", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961012211", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961020048", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961097184", "body": ""}
{"comment": {"body": "Correct - but as discussed I'm going to refactor this behaviour to leverage an auth state publisher and flatMap to produce the appropriate downstream publisher that allows the UX to react", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864265823"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961098746", "body": ""}
{"comment": {"body": "they're being used in 2 places - see `ThreadStore`, `TeamMemberStore`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r864267007"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961107238", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961109073", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961111894", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961116477", "body": "Lots of little nit-picky things but this is looking really good.\nOne thing to keep an eye on is how much boilerplate is being added to each store, with publishing, fetching, caching, etc -- there may be ways to (optionally) factor that into the base protocol so it doesn't have to be reimplemented in each store."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961136960", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961144616", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961151643", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-961152971", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-964154539", "body": ""}
{"comment": {"body": "Test test \n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r866470076"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-964155904", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r866471247"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-976110107", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r875274297"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-979296846", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r877562094"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-980501959", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r878409525"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-983802513", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r880914149"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#pullrequestreview-984160606", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1110#discussion_r881176204"}}
{"title": "Add emails to createThread request for invite", "number": 1111, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1111", "body": "Upon thread creation, users can choose to invite both Unblocked members and non-UB members (i.e. via git). We want to send notification emails for both these kinds of users, but given that the email content will be different in each case, we will need some way to differentiate between UB and non UB members.\nProposal is to add an optional emails list view to the createThread request - this will be a list of non-UB email addresses. The participants field will remain as is, being a list of UB user uuids to both send a notification email and add an unread to the user. \nIf the API is agreed upon, then I'll start working on the implementation side."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1111#pullrequestreview-959541994", "body": ""}
{"title": "Update exposed", "number": 1112, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1112"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1112#pullrequestreview-959565763", "body": ""}
{"title": "Add search service", "number": 1113, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1113", "body": "This service will be responsible for indexing. It won't be publicly accessible, will be triggered by events from the search_indexing queue, and doesn't need redis (so I didn't include any of the secrets)."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1113#pullrequestreview-959603926", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1113#pullrequestreview-959605701", "body": "Looks good."}
{"title": "Fix breadcrumb", "number": 1114, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1114", "body": "Broke with Ktor 2.0.1 upgrade!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1114#pullrequestreview-959611226", "body": ""}
{"title": "Slim down commit info to just title", "number": 1115, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1115", "body": "Instead of outputting entire commit message, just use the title ( aka first line)"}
{"comment": {"body": "> Will this still fail if I put odd characters in my PR title?\r\n\r\nFingers crossed. That's why we still have the `toJSON` wrapper ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1115#issuecomment-1115427425"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1115#pullrequestreview-959617926", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1115#pullrequestreview-959617949", "body": "Will this still fail if I put odd characters in my PR title?"}
{"title": "Revert \"Ktor 2.0.1 (#1100)\"", "number": 1116, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1116", "body": "This reverts commit b0d99a05af07607345d70eb41b95a0e0b9407c59."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1116#pullrequestreview-959628880", "body": ""}
{"title": "Add basic support for underline rendering in message editor", "number": 1117, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117", "body": "Slowly address this list:\n\nSimple pr, add underline.\n"}
{"comment": {"body": "I think you might need to also update the MessageView as well to render this -- `InlineTextRenderer` should be the place.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#issuecomment-1116374358"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-959637589", "body": ""}
{"comment": {"body": "Some of the changes we've made (not sure where) have dragged in dependencies that are forcing me to intorduce these polyfills.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r863242604"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-960650309", "body": ""}
{"comment": {"body": "Can we put a comment to this effect please?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r863958612"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-960651486", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-963826555", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r866225317"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-963826720", "body": ""}
{"comment": {"body": "1) helo\n2) hello\n3) hello3\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r866225445"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-964737949", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/7bc9a69d-1598-4ef8-900a-32933a9b9aff)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/d3e1e361-9401-4097-a54d-eec52e169d15)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/31146a03-ef9c-451f-8714-bd2685af2f4f)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r866884443"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-964739472", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/dae7a4d6-2360-4830-9b17-38e92a092686)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/fd6481f9-76f3-41be-9896-e4e39a02d4a9)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/487f4a80-d0c7-49b3-9614-2636bed6c899)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r866885565"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-964748788", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/7a809f8f-82c0-4d3b-9821-cea1f2b0a021)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r866891990"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-964750428", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/75a47eff-3e79-48df-ad4b-df42a0c31ca0)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r866893062"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-964964358", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r867042418"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-964965028", "body": ""}
{"comment": {"body": "test test test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r867042883"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#pullrequestreview-964966807", "body": ""}
{"comment": {"body": "tet test test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1117#discussion_r867044112"}}
{"title": "Only refresh teams when auth", "number": 1118, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1118", "body": "Should not be refreshing teams until authenticated."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1118#pullrequestreview-959653645", "body": ""}
{"title": "Remove unused Auth and Git interfaces", "number": 1119, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1119", "body": "Removing dead code that uses getCurrentRepo\nInstallCommand was used before current auth flow.\ngetCurrentRepoUrl not used anywhere."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1119#pullrequestreview-959656755", "body": ""}
{"title": "Make service deployment a reusable workflow", "number": 112, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/112", "body": "This is Phase 1 of componentizing service deployment by using a reusable workflow.\nBuilding service image/deployemnt to dev has now been moved to a reusable workflow.\nNext phase is to make deployment each environment a composite action."}
{"title": "Fix copy and paste", "number": 1120, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1120", "body": "preventdefault cancels normal browser event handling."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1120#pullrequestreview-960731863", "body": ""}
{"title": "Enable parallel gradle builds", "number": 1122, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1122"}
{"title": "add a repo for search service", "number": 1123, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1123", "body": "Service deploys are failing because the repo doesn't exist yet."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1123#pullrequestreview-959687727", "body": "Thanks!"}
{"title": "Add request metadata to ktor exception handler", "number": 1124, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1124"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1124#pullrequestreview-959971530", "body": ""}
{"title": "Add X-Request-ID to api service", "number": 1125, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1125", "body": "server responds with request ID as X-Request-ID header\nif client does not pass X-Request-ID header, then server generates one.\n\nExample of server generated ID:\n"}
{"comment": {"body": "for some reason it's failing the `VideoRecordingApiDelegateImplTest`\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1125#issuecomment-1116454967"}}
{"comment": {"body": "@pwerry you ok with this: https://github.com/NextChapterSoftware/unblocked/pull/1125/commits/9b0c6bfda011b76f9206adbb8fd012b9cf2ec956", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1125#issuecomment-1116883414"}}
{"comment": {"body": "Why the exception hack?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1125#issuecomment-1116892297"}}
{"comment": {"body": "related to this\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1147\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1125#issuecomment-1116893014"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1125#pullrequestreview-959975291", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1125#pullrequestreview-959975412", "body": ""}
{"comment": {"body": "Follow industry convention", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1125#discussion_r863501178"}}
{"title": "Test CI", "number": 1126, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1126", "body": "ignore"}
{"title": "Use correct repo in multi-repo VSCode workspaces", "number": 1127, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#pullrequestreview-960709798", "body": ""}
{"comment": {"body": "This fixes a startup auth bug Jeff figured out: on startup, if the auth token had expired, we would *not* immediate get a refresh token.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#discussion_r864001232"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#pullrequestreview-960710869", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#pullrequestreview-960713004", "body": ""}
{"comment": {"body": "Cleaned up as part of trying to debug startup.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#discussion_r864002288"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#pullrequestreview-960713833", "body": ""}
{"comment": {"body": "Join in the VSCode Repository object", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#discussion_r864002637"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#pullrequestreview-960719625", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#pullrequestreview-964154424", "body": ""}
{"comment": {"body": "Hallooooooo\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#discussion_r866469982"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#pullrequestreview-964965716", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#discussion_r867043388"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#pullrequestreview-964966571", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1127#discussion_r867043974"}}
{"title": "incident-00000001-rca-do-NOT-merege", "number": 1128, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1128"}
{"title": "Reverting", "number": 1129, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1129"}
{"title": "Componentize deployment of steps to environment", "number": 113, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/113"}
{"title": "Fix underline", "number": 1130, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1130"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1130#pullrequestreview-960797621", "body": ""}
{"title": "Right size services", "number": 1131, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131", "body": "Added more CPU to api service\nChanged API service count in Dev to 2 (to avoid interruptions since we are using it for demos and testing)\nAdded more memory to SCM service to address crashloops\nChanged deployment method to allow 50% unavailable\nRegenerated helm charts"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#pullrequestreview-1022305695", "body": ""}
{"comment": {"body": "@PadraigK\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/5db0369a-dfb0-4e1c-ba8f-c5ec70380b4c?message=e5913986-0b29-41e2-a9bb-220714dd11b7).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#discussion_r908875580"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#pullrequestreview-1023778638", "body": ""}
{"comment": {"body": "@PadraigK\n\n@PadraigK\n\n@mahdi-torabi\n\n\n\n@PadraigK\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment edited from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/a1c10fed-7a4e-4b8f-831b-196927e358a8?message=9e94960d-29e6-45c7-af43-7878a31a10e2).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#discussion_r910250519"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#pullrequestreview-1023784243", "body": ""}
{"comment": {"body": "@PadraigK\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;Comment posted from [Unblocked](https://dev.getunblocked.com/dashboard/team/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/thread/5db0369a-dfb0-4e1c-ba8f-c5ec70380b4c?message=f3b4630e-b563-4c93-80a2-7cd76e7c6f77).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#discussion_r910254479"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#pullrequestreview-960804375", "body": ""}
{"comment": {"body": "Take down half and bring up half of the new instances on each deploy", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#discussion_r864067159"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#pullrequestreview-960805816", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1131#pullrequestreview-960808213", "body": ""}
{"title": "Remove TeamID with AllReposStore for Web", "number": 1132, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1132", "body": "Fixes #1121\nRemoves hard coded TEAM_ID values in the web.\nFollowing similar pattern to other clients where we grab the list of relevant teams and using those teams, fetch the relevant repositories."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1132#pullrequestreview-960913485", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1132#pullrequestreview-960918647", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1132#pullrequestreview-960945629", "body": ""}
{"title": "Fix duplicate person issue", "number": 1133, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1133"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1133#pullrequestreview-960846498", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1133#pullrequestreview-960855742", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1133#pullrequestreview-960918358", "body": ""}
{"title": "Fix dialog close with callback", "number": 1134, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1134", "body": "The onClose callback was not being invoked when the DialogHeader button was being clicked because the callback wasn't being passed through into the component, resulting in the hash not clearing properly\n\nInstead, of defining the callback in the dialog component, we could set the callback into a state and then call it close in one place \nNOTE: Because we're setting a function into the state, we have to add an extra layer of functional wrapping so that the cb is not called immediately - but we only have to do this in one place"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1134#pullrequestreview-960918037", "body": ""}
{"title": "handle empty env vars in helm", "number": 1136, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1136", "body": "We were not handling empty environment variable dictionaries properly. This was breaking search service deployments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1136#pullrequestreview-960916870", "body": ""}
{"title": "Address singleOrNull bugs by forbidding them via linter rule", "number": 1137, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1137", "body": "Spoke with Richie, he feels single() use does have its purposes but singleOrNull is evil."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1137#pullrequestreview-960950534", "body": ""}
{"comment": {"body": "```suggestion\r\n                val errorMessage = \"Usage of .singleOrNull() is forbidden due to potential for bugs. Use single() or firstOrNull().\"\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1137#discussion_r864153488"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1137#pullrequestreview-960964593", "body": ""}
{"title": "Dropdown component/styling", "number": 1138, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138", "body": "Refactor Dropdown into its own component and add styling for each client:\nweb extension:\n\nvscode:\n\nweb dashboard (no concrete designs for this yet, leave as simple box):\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138#pullrequestreview-961011514", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138#pullrequestreview-961011562", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138#pullrequestreview-961013987", "body": ""}
{"comment": {"body": "The hover state also doesn't seem to be working for me.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138#discussion_r864201847"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138#pullrequestreview-961075260", "body": ""}
{"comment": {"body": "pull and try now?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138#discussion_r864250584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138#pullrequestreview-961202748", "body": ""}
{"comment": {"body": "Hover works now! I also updated the padding to 4pts around.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138#discussion_r864344390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1138#pullrequestreview-961202821", "body": ""}
{"title": "Introduce Source Mark models", "number": 114, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/114", "body": "Adds these with tests\n- RepoModel\n- SourceMarkAccessModel\n- SourceMarkModel\n- SourcePointModel\n- SourceVectorModel\n"}
{"comment": {"body": "Is the overall model structure documented anywhere?  I guess my main question is whether a SourceMark represents a single logical \"point in code over time\", or whether it is the container that holds all of the code references, plus other content (chats, media, etc) ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/114#issuecomment-1020630373"}}
{"comment": {"body": "> Is the overall model structure documented anywhere? I guess my main question is whether a SourceMark represents a single logical \"point in code over time\", or whether it is the container that holds all of the code references, plus other content (chats, media, etc) ?\r\n\r\n@matthewjamesadam Added diagram to help visualize. A SourceMark is associated to a Chats; but does not contain a Chat. Every Chat (or other content entity) _has a_ SourceMark reference. This will be obvious in next PR.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/114#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/114#pullrequestreview-861601561", "body": ""}
{"title": "Disable redis health check for search service", "number": 1141, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1141", "body": "Apologies for the hackyness \n"}
{"comment": {"body": "just do this:\r\n```kt\r\nServiceLifecycle(listOf(PostgresHealthChecker())\r\n```\r\n\r\ninstead of calling `ServiceLifecycle.instance`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1141#issuecomment-**********"}}
{"comment": {"body": "@richiebres Fixed. Might just be me, but I find unnecessary singleton patterns like this confusing. Would make more sense to have the constructor with default parameters.\r\n\r\nAgain, might just be me inferring too much from code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1141#issuecomment-**********"}}
{"comment": {"body": "> @richiebres Fixed. Might just be me, but I find unnecessary singleton patterns like this confusing. Would make more sense to have the constructor with default parameters.\r\n> \r\n> Again, might just be me inferring too much from code.\r\n\r\nDo it.\r\nAt the time when I extracted all this functionality out I didnt really think of doing that, but that's fine by me.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1141#issuecomment-1116656852"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1141#pullrequestreview-961074664", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1141#pullrequestreview-961118873", "body": "Thank you!"}
{"title": "Spots for everyone!", "number": 1142, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1142"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1142#pullrequestreview-962455088", "body": ""}
{"title": "Update Safari archive CI", "number": 1143, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1143", "body": "Setup Safari CI to generate a development package.\nMay require one to allow apps from unsigned developers in system preferences."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1143#pullrequestreview-962683538", "body": ""}
{"comment": {"body": "Introducing new build check script. Used only in PRs to verify base web extension builds.\r\n\r\nDoes not do any bundling.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1143#discussion_r865418130"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1143#pullrequestreview-962684603", "body": ""}
{"comment": {"body": "Using development, not developer-id since we are not notarizing yet...(or ever.)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1143#discussion_r865418881"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1143#pullrequestreview-964971200", "body": ""}
{"title": "Fix slack button", "number": 1144, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1144", "body": "Used the incorrect type of button...\nUsing accessory button which doesn't require additional backend work."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1144#pullrequestreview-*********", "body": ""}
{"title": "Ignore all whitespace in FuzzyCompare", "number": 1145, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1145", "body": "Motivation for completely ignoring whitespace is to account for code equivalence.\nFor example, this:\nc\n    if ( a == \"\" ) { return; }\nMeans the same thing as this in most languages:\nc\nif(a==\"\"){return;}\nWill be used in follow up change."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1145#pullrequestreview-*********", "body": "Great idea"}
{"title": "Ignore space when detecting moves", "number": 1146, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1146"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1146#pullrequestreview-*********", "body": ""}
{"title": "Stop using default exception handlers", "number": 1147, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1147"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1147#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1147#pullrequestreview-*********", "body": ""}
{"title": "Stop using default exception handlers", "number": 1148, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1148", "body": "Resurrected from the crypt"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1148#pullrequestreview-961325883", "body": ""}
{"comment": {"body": "nice", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1148#discussion_r864445366"}}
{"title": "SourceMark engine tracks moves across files", "number": 1149, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1149"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1149#pullrequestreview-*********", "body": ""}
{"title": "Clean up helm deployment to make it per environment...", "number": 115, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/115", "body": "We are now adding per environment helm overrides."}
{"title": "SourceMark engine accounts for fuzzy moves", "number": 1150, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1150"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1150#pullrequestreview-*********", "body": ""}
{"title": "Create search threads operation", "number": 1151, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151", "body": "Creating a search endpoint stub so that we can start implementing the frontend for Search V1. \nSearch V1 lists search results as a list of threads, so this endpoint returns thread IDs.\nI've modelled it after GitHub's search API where it's a GET operation with the search query as a query parameter q. This stubbed endpoint will search thread titles only. Follow up PR will add the actual search logic.\nThis operation is not paginated but probably should be. I can include in this PR if that is easier."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-961433012", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962141273", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962143155", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962152684", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962153463", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962205423", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962208303", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962224926", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962228242", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962230897", "body": ""}
{"comment": {"body": "do we need to sanitize the input here to prevent SQL attacks?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#discussion_r865089035"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962236907", "body": ""}
{"comment": {"body": "I believe exposed sanitizes, but let me confirm. \r\n\r\nRELEASE BOBBY TABLES.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#discussion_r865095459"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962265324", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962269660", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962273872", "body": ""}
{"comment": {"body": "Yeah confirmed Exposed parameterizes. The resulting SQL also confirms:\r\n\r\n```\r\nSELECT DISTINCT threadmodel.id \r\nFROM sourcemarkmodel \r\nINNER JOIN threadmodel ON threadmodel.id = sourcemarkmodel.thread \r\nWHERE (sourcemarkmodel.team = '769ce466-87b3-4ed4-8de7-c5b5ebd5f8f1') \r\n    AND (sourcemarkmodel.repo = 'eee5c907-549b-43d8-b91d-f4dc4c56fe42') \r\n    AND (threadmodel.title LIKE '%sense''); DROP TABLE personmodel; --%')\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#discussion_r865123240"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962274931", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962358566", "body": ""}
{"comment": {"body": "thanks", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#discussion_r865183586"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1151#pullrequestreview-962491986", "body": ""}
{"title": "FormatJSON", "number": 1152, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1152", "body": "Invalid JSON from copy"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1152#pullrequestreview-962075887", "body": ""}
{"title": "resize instance and use our own fork of the plugin for added security", "number": 1153, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1153"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1153#pullrequestreview-962131787", "body": ""}
{"title": "Disable flaky test", "number": 1154, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1154", "body": "Sorry Pete. Seems very similar to https://github.com/NextChapterSoftware/unblocked/pull/1061\n\nhttps://chapter2global.slack.com/archives/C031MKRPZSQ/p1651638697864359"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1154#pullrequestreview-962235776", "body": ""}
{"title": "Fix dropdown elements", "number": 1156, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1156", "body": "Link in dropdown was broken -- need to render actual anchor elements and typecast the matching props into the elements"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1156#pullrequestreview-962642919", "body": ""}
{"title": "Configure to send emails to non UB users", "number": 1157, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1157", "body": "Add NotificationService to send invite emails to non UB users on thread creation \nEmail content right now is just raw text, will iterate on\nAll emails will only be <NAME_EMAIL> for now, I want to test what the email actually looks like before blasting to the rest of the team (sending actual emails can't be tested locally)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1157#pullrequestreview-962708263", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1157#pullrequestreview-962709142", "body": "Looks good to me. Just one minor question"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1157#pullrequestreview-962715277", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1157#pullrequestreview-962715341", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1157#pullrequestreview-962717537", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1157#pullrequestreview-962731839", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1157#pullrequestreview-962923176", "body": ""}
{"title": "Adding Monkey service (my favorite service so far)", "number": 1158, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158", "body": "Create empty noop Monkey service\nCreate helm charts with an internal dedicated ALB endpoint for the Monkey\nCreate SSL certs and cnames for the Monkey\n\nNote: this service is currently noop. We will add the logic to it soon."}
{"comment": {"body": "Recent god mode changes seem to have broken lambda tests. Disabled them for now. \r\nI'll fix them in a separate PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#issuecomment-1118006321"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-962731200", "body": ""}
{"comment": {"body": "intentional?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r865453760"}}
{"comment": {"body": "why do we need this endpoint to be exposed at all?\r\n\r\nI thought it would be an internal service that has no endpoints, running a background process.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r865454121"}}
{"comment": {"body": "don't need ktor server, just a client", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r865454762"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-962781558", "body": ""}
{"comment": {"body": "I figured we would need some endpoint for the GitHub actions to hit (actions now run on Ec2 so they are on internal network) to see if a test run has passed. \r\nSomething like this: https://chapter2global.slack.com/archives/C02SL9D0YHM/p1651698177736489?thread_ts=1651694101.567149&cid=C02SL9D0YHM", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r865494224"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-962781755", "body": ""}
{"comment": {"body": "[was thinking for this use-case ](https://chapter2global.slack.com/archives/C02SL9D0YHM/p1651698177736489?thread_ts=1651694101.567149&cid=C02SL9D0YHM)\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r865494375"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-962786537", "body": ""}
{"comment": {"body": "Yep. There's some funkyness going on with God mode. I am digging into it. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r865496993"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-962786582", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-963459261", "body": ""}
{"comment": {"body": "The monkey runs constantly, simulating customer traffic.\r\n\r\nIf the simulated traffic causes the service to crash, then that will be enough to block promotion.\r\n\r\nOtherwise, if the simulated traffic causes the service to send non-2xx responses, then we should detect that error rate and use it to block promotion.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r865972480"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-963763464", "body": ""}
{"comment": {"body": "Block how ? Deployments are done through helm. Helm has no visibility into client logs. Neither does GitHub actions job. \r\nWe need a more concrete way for coordinating deployments. Relying on services crashing (or not crashing) doesn't feel right. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r866181554"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-963767015", "body": ""}
{"comment": {"body": "Having a way to know we have had one clean set of tests during a certain period is super useful. Makes deployment coordinations much easier. I can also use it to implement the deployment switch. \r\nIt's literally an un-authenticated HTTP Get endpoint that would spit out something like \r\n\r\n```\r\n{\r\n  lastRunTimestamp: \"12345667\"\r\n  success: true\r\n  deployToProd: true          // this would be the deployment switch which we can add later.\r\n}\r\n```\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r866184031"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-963772638", "body": ""}
{"comment": {"body": "> Block how ?\r\n\r\nWas thinking cloudwatch integration or something, but hadn't given it much thought.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r866187643"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-963774482", "body": ""}
{"comment": {"body": "> It's literally an un-authenticated HTTP Get endpoint that would spit out something like ...\r\n\r\nI mean, if we're making API requests to check if the service is operating normally, then we might as well just skip the indirection and make API requests to the unblocked service itself right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#discussion_r866188988"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1158#pullrequestreview-963788193", "body": ""}
{"title": "Temporarily reduce background polling interval on SCM service", "number": 1159, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1159", "body": "Note: not touching the GitHubInstallationMaintenanceJob which is already at 10s interval. We can adjust that one next if we need to.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1159#pullrequestreview-962729996", "body": ""}
{"title": "Refactor suspendedTransactionWithLogger to use outside test package", "number": 116, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/116"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/116#pullrequestreview-861618617", "body": ""}
{"title": "Custom request builder", "number": 1160, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160", "body": "This wires up the correct auth headers, and includes a wrapper to handle responses with the last modified header. Comments inline that describe specifics about intended usage and where we're going next.\nI decided not to override execute for logging. We don't have access to request headers at that stage so what we log is a bit limited. Instead we can get that information from CFNetwork debugging or via Xcode's HTTP tool. ~One thing I will add next is the x-b3-traceId header so we can see request data flow all the way through~ done "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962733466", "body": ""}
{"comment": {"body": "A little verbose - but these are here in preparation for eventual codegen changes. We'll create token provider slots for different auth types through codegen and then fill them from the app delegate with things that adhere to the `AuthTokenProvider` protocol (which will also be codegen)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r865455698"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962733807", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962735918", "body": ""}
{"comment": {"body": "The swift5 generator creates top level APIs that look like this wrapper and call down to concrete request builder implementations. For APIs where we need to handle the modified since header, we'll call the request builder API directly instead of the top level API, then pass the request builder to this guy. So it will look something like this:\r\n\r\n\r\n```swift\r\nlet requestBuilder = getThreadsWithRequestBuilder(teamId: teamId, xUnblockedIfModifiedSince: since)\r\nlet wrapped = try await getWrapped(requestBuilder: requestBuilder)\r\n\r\nif let modifiedSince = wrapped.lastModifiedSince {\r\n    // do something\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r865457635"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962737740", "body": ""}
{"comment": {"body": "Spoke with Matt yesterday about `waitForAuth()`. We probably need some kind of `switchToLatest()` mechanic, or else a guarantee that \"same-type\" requests don't stack up behind this gate. I'll probably tackle that as a final step after getting \"pusher\" working", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r865459110"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962737996", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962738295", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962827694", "body": ""}
{"comment": {"body": "Trivial thing, but why is the exchange token above fetched through a separate method, while the refresh token is held via the getAuthToken() method?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r865532675"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962828565", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962829007", "body": ""}
{"comment": {"body": "Ah is this because the returned value is in a header and isn't returned by default by the async operation?  Kind of unfortunate I guess but this makes sense.  Add this as a comment here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r865533776"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-962829949", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-963719774", "body": ""}
{"comment": {"body": "The refresh token is actually a part of the general token response, but this raises a good point that the encapsulation is all wrong here. The AuthStore shouldn't bleed the auth token abstraction and should instead provide an interface the token itself.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r866150910"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-963721146", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-963722291", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-963830379", "body": ""}
{"comment": {"body": "This is a test please ignore\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r866227924"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-963833784", "body": ""}
{"comment": {"body": "Bumping thread for testing, please ignore\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r866230439"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-963979671", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-963981039", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-963982430", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964062817", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r866396090"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964069582", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r866401253"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964125810", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r866447216"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964126080", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#discussion_r866447424"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964144445", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964198591", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964931086", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964931363", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964931592", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1160#pullrequestreview-964932180", "body": ""}
{"title": "Fix some display issues", "number": 1161, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1161", "body": "Fix ugly overflow \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1161#pullrequestreview-962830070", "body": ""}
{"title": "fix docker file", "number": 1162, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1162"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1162#pullrequestreview-962757271", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1162#pullrequestreview-962757419", "body": "the shittiest language on earth"}
{"title": "Show mine threads only", "number": 1163, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163", "body": "Quick change to filter on threads the user belongs to"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#pullrequestreview-962771715", "body": ""}
{"comment": {"body": "O(n) is better than O(n\u00b2)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#discussion_r865486296"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#pullrequestreview-963757637", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#pullrequestreview-964062249", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#discussion_r866395500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#pullrequestreview-964065129", "body": ""}
{"comment": {"body": "bump\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#discussion_r866397889"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#pullrequestreview-964065844", "body": ""}
{"comment": {"body": "boop\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#discussion_r866398341"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#pullrequestreview-964122040", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#discussion_r866444205"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#pullrequestreview-964980848", "body": ""}
{"comment": {"body": "FYI I don't think this is any more efficient -- it's probably worse.\r\n\r\nTo build the Set you need O(n), every time you call `intersects`.  So you're still doing O(n^2) operation.  To get the benefit of the set, I think you need to create and reuse the set for the sequence outside of the individual `intersects` call.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#discussion_r867053845"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#pullrequestreview-966491960", "body": ""}
{"comment": {"body": "test test test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#discussion_r868212150"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#pullrequestreview-966515943", "body": ""}
{"comment": {"body": "test teestttteststest\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1163#discussion_r868222446"}}
{"title": "Add slatejs handling for ordered and unordered blocks.", "number": 1164, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164", "body": "BACKGROUND:\nOriginally wrote my own, but I found a cleaner and non-opinionated library that we're now using to add list-block functionality. It allows us to provide our types via an interface.\n\nThis implementation also has a ton of useful slatejs common utilities that we should consider using in the future:\n\nCHANGES\n1. Define custom list block types:\n- ListItemElement\n- ListItemTextElement\n- OrderedListElement\n- UnorderedListElement\n2. Define Schema that specifies our types and passes that to the library which will resultantly use our types in generating nodes.\n3. Wrap our editor with lists functionality via a callback withLists. This adds normalizations that we need to ensure everything looks right.\n5. Add a boolean to ElementTraits to indicate these new types are plugin types and they should not be normalized via Unblocked handlers. The library's normalization utilities will handle all edge cases for lists blocks.\n6. Upgrade our slate versions.\n7. Move MessageEditorToolbar into own react component.\n8. Make BlockButton behaviour more configurable.\n9. Add serialization\nPROOF THIS WORKS"}
{"comment": {"body": "Follow-up issues that need to be addressed as well:\r\nhttps://github.com/NextChapterSoftware/unblocked/issues/1169", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#issuecomment-1118852013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-962820765", "body": ""}
{"comment": {"body": "This was a bug, should be returnign false if we weren't handling normalization.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r865526911"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-962824481", "body": ""}
{"comment": {"body": "This schema is passed into library. It allows us to define the types the library is to use for instantiating the nodes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r865529995"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966542851", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966828425", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966844213", "body": ""}
{"comment": {"body": "It's a bit unfortunate that we have to do all this type casting here -- means we're losing some of the value of the typing system.  Not sure if there's any way to avoid this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868474470"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966846854", "body": ""}
{"comment": {"body": "Yeah, I tried a few things but by no means am I a typescript expert.\r\nI was trying to create a set of types shared across all of the files (rather than string literals), but I'm not sure this is helping all that much...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868477245"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966846964", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966849292", "body": ""}
{"comment": {"body": "I think we should define a single type for our editor (`CustomEditor`?) that is a `ReactEditor & ListsEditor` and put it in `MessageEditorTypes.ts`?  The editor type can then be used everywhere?  The point is that this is typing our editor as a whole, right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868479862"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966849733", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966849917", "body": ""}
{"comment": {"body": "Correct.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868480522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966850223", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966850768", "body": ""}
{"comment": {"body": "Shouldn't this be `else if (isActive)` ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868481433"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966851551", "body": ""}
{"comment": {"body": "Hmmm, good point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868482291"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966852069", "body": "Looks good  sorry for taking so long to review it.  A bunch of relatively minor suggestions, take them for what they're worth!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966954061", "body": ""}
{"comment": {"body": "\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/99cc3b81-5db4-495f-89ce-480c50513caf)\n\n![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/36571862-ef0c-466b-875d-f0d48ba452fe)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868596679"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966954478", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/6ed73652-0b4e-4b90-8d4c-7ffb43e3fe1d)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868596909"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966954878", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/652e8e13-ec3a-47f0-84b0-dbb9dc83a939)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868597080"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966958979", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/441e3861-6305-47f7-9dbc-d5a638af268b)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868599056"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966962179", "body": ""}
{"comment": {"body": "- asdfdsa\n- asdffdas- What is this?\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868600636"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966962652", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/77dfac24-941d-4c4a-b724-f451d01832a0)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868600849"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966991105", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/98335f32-b9d4-418e-9d96-2fef08618cc3)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868616285"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966993171", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/93536ad6-1712-4caa-8a02-9f8fff1e40c8)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868617647"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#pullrequestreview-966995597", "body": ""}
{"comment": {"body": "![](https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/c7e7b8bb-b589-4b7f-aee8-3c729a4bca26)\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1164#discussion_r868619229"}}
{"title": "Build prod environment clients", "number": 1165, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165", "body": "Create prod builds for all apps.\nAll client apps have a consistent way to determine which environment to use:\n1. If the build explicitly specifies an environment, use that.  This is used for local (developer) builds, so we target and run builds against the correct environment when debugging.\n2. If there is an UNBLOCKED_DEV environment variable override, use that.  This is for VSCode.\n3. If we are in a browser, and the browser URL matches a dashboard URL, use that.  This allows the web dashboard to use the correct environment for whatever host it's running from.\n4. Fail over to prod.\nSome caveats:\n* We still don't know how to override the environment for the web extension.  Web extensions are browser-based and can't read environment variables.  We're looking into local storage.\n* We still don't have a way of running the dashboard against dev or prod.  This is complicated because of login redirects."}
{"comment": {"body": "OK @jeffrey-ng feel free to review this and +1 it if you think we should get this in.  We can fix the remaining issues in subsequent PRs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165#issuecomment-1118735662"}}
{"comment": {"body": "Waiting to merge until we have ingested PR data in `prod`.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165#issuecomment-1118769712"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165#pullrequestreview-962823820", "body": ""}
{"comment": {"body": "FYI @mahdi-torabi this still does two separate CI builds for the web UI (dev and prod), which we don't really need.  The same build should work in both environments.  We should rejig this to only build once and deploy to both locations?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165#discussion_r865529466"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165#pullrequestreview-962824097", "body": ""}
{"comment": {"body": "FYI @jeffrey-ng there's no easy way to switch this when building for different environments.  I'd appreciate some help here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165#discussion_r865529693"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165#pullrequestreview-963581860", "body": ""}
{"comment": {"body": "Can do. If you don't mind getting this in first w/o web extensions, I can refactor this to better switch between environments.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165#discussion_r866056127"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1165#pullrequestreview-963790198", "body": ""}
{"title": "Enable PR ingestion in prod", "number": 1166, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1166"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1166#pullrequestreview-963663624", "body": ""}
{"title": "Remove perf-related indexes and make columns non-nullable", "number": 1167, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1167"}
{"comment": {"body": "clean this one up?\r\n```kt\r\n    val prCommentHtmlUrl = text(\"commentHtmlUrl\").nullable() // TODO move to Message\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1167#issuecomment-1118854672"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1167#pullrequestreview-963706512", "body": "I love you @davidkwlam"}
{"title": "Setup VSCode Chromatic", "number": 117, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/117"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/117#pullrequestreview-862936168", "body": ""}
{"title": "Disable Keep Alive", "number": 1170, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1170", "body": "Disabling Keep Alive will allow us to specify the hosts.\nThere's a risk that background service workers for extension gets cleaned up so need to keep an eye on that. Cache should still make the UX seamless though...\nThis will scope the scary dialogs from Safari to just GH.com"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1170#pullrequestreview-963759239", "body": ""}
{"comment": {"body": "Keeping around for now just in case we need the logic.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1170#discussion_r866178518"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1170#pullrequestreview-963759603", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1170#pullrequestreview-963761331", "body": ""}
{"title": "allow vpn access to prod db while we prepare it for dogfooding", "number": 1171, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1171"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1171#pullrequestreview-963769962", "body": ""}
{"title": "Make admin auth safe", "number": 1172, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1172", "body": "This modifies godmode so that it only uses identities that already exist keyed off the external user ID."}
{"comment": {"body": "If it's too much of a PITA to get the external ID, then we could also use SCM username, which the service could easily resolve to external ID. Only is needed...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1172#issuecomment-1118907109"}}
{"comment": {"body": "> If it's too much of a PITA to get the external ID, then we could also use SCM username, which the service could easily resolve to external ID. Only is needed...\r\n\r\nExternal ID is stable (github ID) so don't think it's necessary to resolve by username. This is very much a dev tool and not meant for real consumption", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1172#issuecomment-1118913560"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1172#pullrequestreview-963778106", "body": "yeah this is cleaner, and easier to reason about "}
{"title": "Update hub admin auth", "number": 1173, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1173"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1173#pullrequestreview-*********", "body": ""}
{"title": "Add producer permission on search_indexing queue to scm service", "number": 1174, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1174", "body": "Added write only perm on serch indexing queue to scm service account in dev and prod.\nChanges have been deployed to both environments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1174#pullrequestreview-*********", "body": "Thanks!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1174#pullrequestreview-*********", "body": ""}
{"title": "Add token prefix to all token providers", "number": 1176, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1176", "body": "We're only using token prefix for VSCode.\nThis wasn't a problem when we just had localhost & dev but the introduction of prod is leading to some clashes due to the similar domain suffix."}
{"comment": {"body": "Not necessary as web should have different storage containers for each domain including subdomains. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1176#issuecomment-**********"}}
{"title": "Consolidate ArrayHelpers", "number": 1177, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1177", "body": "Had ArrayExtensions within VSCode that would be useful elsewhere.\nConsolidated into ArrayUtils under shared utils."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1177#pullrequestreview-963968166", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1177#pullrequestreview-963968560", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1177#pullrequestreview-963977376", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1177#pullrequestreview-964978738", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1177#pullrequestreview-979296050", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1177#pullrequestreview-979296156", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1177#pullrequestreview-979296566", "body": ""}
{"title": "Index message body and thread titles", "number": 1178, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1178"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1178#pullrequestreview-964050533", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1178#pullrequestreview-964051340", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1178#pullrequestreview-964051727", "body": "I'm fine with this, minor ocmments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1178#pullrequestreview-964051853", "body": ""}
{"title": "Do not crash when failing to render SM snippet", "number": 1179, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1179", "body": "Do not crash when failing to render SM snippet, from \nExpose Initial PR Ingestion Complete on Repos Page\nremove spam logs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1179#pullrequestreview-964066769", "body": ""}
{"title": "Setup Basic Auth for web with mocked endpoints", "number": 118, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118", "body": "Some setup for web client.\nThere are some incorrect API calls while the backend is in flight.\nHave bypassed parts of authentication to demo flow.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-862935686", "body": ""}
{"comment": {"body": "Are you thinking we should be organizing by domain (`src/auth`) instead of by type (`src/store`) ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#discussion_r792172507"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-864091752", "body": ""}
{"comment": {"body": "Auth started growing larger and larger so I pulled it out specifically.\r\nI'm not 100% sure how this will impact the overall folder structure in the future.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#discussion_r793038088"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-864348110", "body": ""}
{"comment": {"body": "What was the reason for using a custom template for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#discussion_r793231333"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-864348977", "body": ""}
{"comment": {"body": "I do think we should either stick with domain or type organization -- mixing the two seems complicated?  There's probably a way to make this work either way, let's discuss tomorrow?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#discussion_r793232055"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-864377843", "body": ""}
{"comment": {"body": "(Won't let that hold this back or anything though -- I'll finish reviewing this in the morning)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#discussion_r793254828"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-864416783", "body": ""}
{"comment": {"body": "For `/login` we actually want to open the URL in the browser, not call it as a GET api.\r\n\r\nI added a `apiname*URL*` variant which returns the URL of the request.\r\nThis is used in my next PR: https://github.com/Chapter2Inc/codeswell/pull/144/files\r\n\r\n```\r\n signin: (provider) => {\r\n        // TODO: Allow for provider selection\r\n        window.location.href = API.auth.getLoginUrl({provider })\r\n    },\r\n    ```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#discussion_r793283929"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865190403", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865192722", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865200143", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865200695", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865203552", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865208669", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865210229", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865241775", "body": ""}
{"comment": {"body": "Might be worth a comment or two here explaining why the two tokens are stored in different stores", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#discussion_r793865855"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865246733", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865270186", "body": ""}
{"comment": {"body": "Would it make more sense to GET this API and then jump to a URL it returns, instead of baking the URL in our API definition?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#discussion_r793885748"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/118#pullrequestreview-865290974", "body": ""}
{"title": "Echo modified since request parameter if result size is 0", "number": 1180, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1180", "body": "Summary\nBefore this change we weren't sending back the X-Unblocked-Last-Modified header when the size of the result collection was 0. This is not correct behaviour because APIs with \"modified since\" semantics are expected to behave like streams, meaning it should not be possible for a value to be inserted with a modifiedAt value in the past. \nClients should therefore treat the API result as a correct representation.\nI've also noticed some inconsistencies in how we're dealing with empty responses in the service. getThreadMessages for example will send back a 404 if it can't find any new messages, whereas getThreads will happily return an empty collection.\nNot sure which style is better, but we should try to be consistent\nAlso\nI added a push channel for /repos since it was the only one not implemented according to the spec."}
{"comment": {"body": "> Not sure which style is better, but we should try to be consistent\r\n\r\n- `200 []` for empty collection\r\n- `404` should be reserved for the collection not existing (eg: when you get messages of non-existent thread)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1180#issuecomment-1119214875"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1180#pullrequestreview-964134407", "body": ""}
{"title": "Use last modified in hub", "number": 1181, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181", "body": "This is the necessary precursor for push updates to the thread list. Thread and Unreads lastModified values are cached, and new results are correctly merged, ordered, and limited with previous results."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964147083", "body": ""}
{"comment": {"body": "I had to do this because the generated code always base64 encodes the headers set by the request function. IMO the way this behaves is wrong - APIs should default to no-op when the standard or API contract doesn't require it, and provide options to do this sort of thing in case flexibility is needed. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866464061"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964147413", "body": ""}
{"comment": {"body": "This is not used. I can dump it if anyone has opinions", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866464331"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964147722", "body": ""}
{"comment": {"body": "Useful in the case where equatable elements are not precisely equal and conflict resolution is required", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866464562"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964147802", "body": ""}
{"comment": {"body": "Not used, could dump it", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866464622"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964194179", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866501698"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964196191", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866503210"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964198262", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866504868"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964199089", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866505545"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964857589", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866965599"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964858260", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866966317"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964860042", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866968001"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964878040", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866980874"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964896425", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866993517"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964897608", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866994357"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964905432", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r866999860"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964911435", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r867004601"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964919102", "body": ""}
{"comment": {"body": "comment\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r867010109"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964920439", "body": ""}
{"comment": {"body": "change\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r867011070"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964930086", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r867017797"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964932398", "body": ""}
{"comment": {"body": "jhgjhg\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r867019864"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964986120", "body": ""}
{"comment": {"body": "asdfasdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r867057113"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964986237", "body": ""}
{"comment": {"body": "asdfasdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r867057197"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-964986369", "body": ""}
{"comment": {"body": "sadfasdf\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r867057279"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966619965", "body": ""}
{"comment": {"body": "Remove one.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r868267346"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966629405", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966871737", "body": ""}
{"comment": {"body": "Is this eventually going to be moved into the pusher service?  Feels a bit odd to hold onto this here...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r868506678"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966872738", "body": ""}
{"comment": {"body": "I'd make it explicit this is an ISO8601 date in the method names, so it's clear what's happening.  Date/string translation can mean almost anything otherwise...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r868507935"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966877853", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966877993", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966881348", "body": ""}
{"comment": {"body": "There's different calls", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r868518492"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966882431", "body": ""}
{"comment": {"body": "`PusherService` is only responsible for channel queries and has no real insight into expected types, so I'm not sure that's the right place for results data like this. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r868519832"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966883092", "body": ""}
{"comment": {"body": "Oh ... I misunderstood what this was.  So is there expected to only ever be a single ThreadStore, and it internally indexes data by team?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#discussion_r868520621"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1181#pullrequestreview-966884307", "body": ""}
{"title": "Ktor HttpClients must be closed", "number": 1182, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1182", "body": "Ktor HttpClients hold onto native resources that must be explicitly closed.\nThis is one source of memory leaks for SCM service.\nThe main indicator was the Non-Heap memory increase over time as I was monitoring the SCMService.\nThe other major concern, even after this fix, is that I'm still noticing leaks when creating Ktor clients.\nMy fear is that there's an underlying leak in the Ktor HttpClient even post close. :(\nDebugging native memory allocations is nigh impossible in java land.\n"}
{"comment": {"body": "This does not fix it entirely. There are still leaks going on but it seems to be outside of ear we're doing", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1182#issuecomment-1119234856"}}
{"comment": {"body": "What the heck this is nuts!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1182#issuecomment-1119234979"}}
{"comment": {"body": "Please teach me too", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1182#issuecomment-1119257642"}}
{"comment": {"body": "What a machine", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1182#issuecomment-1119257733"}}
{"comment": {"body": "Honestly, there's more work to be done. It's still leaking bad on scmservice. Fuck. \r\nMight have to file a bug with Ktor team.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1182#issuecomment-1119280490"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1182#pullrequestreview-964153135", "body": "cool. would really like to understand how you figured this out tomorrow. teach me"}
{"title": "Update CIDR list for me and david", "number": 1183, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1183"}
{"title": "Update dennis cidr", "number": 1184, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1184"}
{"title": "Fix SM engine x-file move processing", "number": 1185, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1185"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1185#pullrequestreview-964176531", "body": ""}
{"comment": {"body": "not sure why yet, but this cache interferes with the proper operation of the cross-file move algorithm, so disabling for now. this has very minor performance impact.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1185#discussion_r866487662"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1185#pullrequestreview-964176710", "body": ""}
{"comment": {"body": "logging fix. I was logging a promise, instead of the object.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1185#discussion_r866487795"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1185#pullrequestreview-964176875", "body": ""}
{"comment": {"body": "this was just dumb logic. inverted.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1185#discussion_r866487901"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1185#pullrequestreview-964177434", "body": ""}
{"title": "Adds pusher service for threads", "number": 1186, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964209501", "body": ""}
{"comment": {"body": "weird that you need to pass in `*ModifiedSince` just to unsubscribe", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r866513349"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964640215", "body": ""}
{"comment": {"body": "Yup I'm going to refactor that", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r866818227"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964861501", "body": ""}
{"comment": {"body": "yeah, hurry up and do it ffs", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r866969247"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964862239", "body": ""}
{"comment": {"body": "c'mon Pete!\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r866969824"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964862473", "body": ""}
{"comment": {"body": "hello - what you waiting for\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r866970013"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964862862", "body": ""}
{"comment": {"body": "still waiting... \ud83e\udd71 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r866970281"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964863962", "body": ""}
{"comment": {"body": "ok, last time", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r866971033"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964873425", "body": ""}
{"comment": {"body": "test from dashboard\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r866977699"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964891036", "body": ""}
{"comment": {"body": "What I've learned is that **Pete is slow** when replying to _unblocked messages_\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r866989765"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-964988742", "body": ""}
{"comment": {"body": "Because I don't have **Notifications**\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867058882"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965070518", "body": ""}
{"comment": {"body": "This thread seems to be missing a sourcepoint\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867130814"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965073380", "body": ""}
{"comment": {"body": "hi\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867132794"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965079629", "body": ""}
{"comment": {"body": "asdfs\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867138638"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965202003", "body": ""}
{"comment": {"body": "from dash\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867225967"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965202339", "body": ""}
{"comment": {"body": "dash 2\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867226207"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965204071", "body": ""}
{"comment": {"body": "dash 3\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867227154"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965205485", "body": ""}
{"comment": {"body": "dash 4\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867228270"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965209110", "body": ""}
{"comment": {"body": "dash 5\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867231034"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965210160", "body": ""}
{"comment": {"body": "dash 6\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867231849"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965226177", "body": ""}
{"comment": {"body": "Hello Peter\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867243475"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965226658", "body": ""}
{"comment": {"body": "Peter!\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867243841"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965240283", "body": ""}
{"comment": {"body": "dash 7\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867254977"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965241159", "body": ""}
{"comment": {"body": "dash 8\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867255652"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-965243301", "body": ""}
{"comment": {"body": "dash 9\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r867257350"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-966637990", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-966641772", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-966857262", "body": ""}
{"comment": {"body": "Does this actually run the tasks in parallel?  I am not a swift concurrency expert, but shouldn't we use a task group for that?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r868488688"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-966859623", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-966861527", "body": ""}
{"comment": {"body": "I'm guessing this will need to take in (and return) the lastModified?  IE the API GET call needs to take in the current lastModified value, and return it?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r868493989"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-966864653", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-966868509", "body": ""}
{"comment": {"body": "This is an interesting nuance. A task group achieves pretty close to the same thing as this code. When you create a `Task` instance, it begins executing immediately. And since this is not a stream, it means we need to wait until everything is done. \r\n\r\nSo this could certainly be re-written as: \r\n```swift\r\ntry await withTaskGroup(of: Void) { group in\r\n    forEach {\r\n        group.addTask {\r\n            try await action(element)\r\n        }\r\n    }\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r868502518"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-966879150", "body": ""}
{"comment": {"body": "For map functions this is a lot more gruesome because the results in the task group don't automatically \"collect\", so you still have to iterate over the group sequence to get things out the other end like:\r\n\r\n```swift\r\nfor try await result in group {\r\n    collection.add(result)\r\n}\r\nreturn result\r\n```\r\n\r\nVery manual, very ugly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r868515925"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-966917244", "body": ""}
{"comment": {"body": "Yeah sorry this is pretty unclear. The behaviour right now is that the \"subscriber\" is responsible for \"pushing\" the updated modified since value back to the PushService by re-subscribing on the same channel. This works because `PushService` only supports a single subscriber per channel at the moment, but I suspect this will always be the case because it's only the stores that are interested in channel update events. So the handler here is really just a tickle to tell the subscriber that something has changed since the requested modifiedSince time, and that it should go fetch the updated data and then re-subscribe with the new modifiedSince value", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r868570679"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-991995623", "body": ""}
{"comment": {"body": "How about this comment? Did it show up in GH?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ebd6a593-b289-4731-8ff6-660c7628c626?message=df3994e6-c936-4897-9920-1c201647a129).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r886823091"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-991996165", "body": ""}
{"comment": {"body": "It certainly did!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r886823493"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#pullrequestreview-993009632", "body": ""}
{"comment": {"body": "test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ebd6a593-b289-4731-8ff6-660c7628c626?message=d7f4f43d-e7f5-4d94-b1b4-77f54cee0942).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1186#discussion_r887570715"}}
{"title": "Resize nodes in Dev and Prod to address deployment failures", "number": 1187, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1187", "body": "Prod cluster was under powered. This was causing failures during deployment where we need 30% additional overhead to bring up new instances. \nI have resized both Dev and Prod cluster to use Compute Optimized c5a nodes. This should address some performance issue as well. Old nodes were t3 class which provided burstable performance whereas new C5a ones will ensure baseline cpu performance."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1187#pullrequestreview-964726229", "body": ""}
{"title": "Revert \"Echo modified since request parameter if result size is 0\"", "number": 1188, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1188", "body": "Broke demos. Will re-instate after"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1188#pullrequestreview-964887493", "body": ""}
{"title": "Remove unnecesary makefile targets", "number": 1189, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1189", "body": "We are no longer using docker for postgres tests. \nWe are using embedded postgres.\nAlso, i created a standard ci dockerfile that is streamlines for tests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1189#pullrequestreview-964894631", "body": "thanks"}
{"title": "Perform AuthenticationState lookup for preauth exchange step", "number": 119, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/119", "body": "Problem\nWe have to exchange the preauth exchange token for an access token by looking up the associated AuthenticationState to see if a previous authentication event has completed for that client secret"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/119#pullrequestreview-861660546", "body": ""}
{"title": "Bring back \"Echo modified since request parameter if result size is 0\"\"", "number": 1190, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1190", "body": "Reverts NextChapterSoftware/unblocked#1188"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1190#pullrequestreview-964958040", "body": ""}
{"title": "Debug unread status in admin web", "number": 1191, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1191", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1191#pullrequestreview-965061974", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1191#pullrequestreview-965068834", "body": ""}
{"comment": {"body": "Ace", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1191#discussion_r867130192"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1191#pullrequestreview-965068974", "body": ""}
{"title": "Setup stores for search and implement basic web UI", "number": 1192, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192", "body": "\nSetup shared stores for search for clients.\nBasic web UI. \nTODO: Add clear action to input component."}
{"comment": {"body": "Search API will soon be moving to returning thread models, not just an ID.\r\nThis should simplify the store logic quite a bit.\r\n\r\n@matthewjamesadam Will address your comments with GH issues to be addressed in future PRs.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#issuecomment-1122667827"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-965079858", "body": ""}
{"comment": {"body": "We have individual store instances for each teamID as API request is team specific. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r867138795"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-965086487", "body": ""}
{"comment": {"body": "Need to aggregate all the search results from the individual stores.\r\n\r\nThis occurs when a workspace has multiple teams.\r\n1. Dashboard as user can span multiple teams.\r\n2. VSCode workspace with multiple repos belonging to multiple teams.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r867143339"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-966659811", "body": ""}
{"comment": {"body": "we do have a usetimeout hook idk if it's worth using here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r868298887"}}
{"comment": {"body": "I know we also have a Spinner component -- we should consolidate??", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r868304871"}}
{"comment": {"body": "IMO this whole thing should be its own component since it has its own state (I know you mentioned you were working on this)... as in, I would rather we build a separate `SearchInput` component that wraps a simple text `Input` vs adding the logic into that component", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r868305636"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-966726455", "body": ""}
{"comment": {"body": "Yup. This will be coming in the updated Input component.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r868357351"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-966727674", "body": ""}
{"comment": {"body": "It will be the Input component but it *won't* have its own state. Input state will still live in the parent component.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r868358996"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-966769250", "body": ""}
{"comment": {"body": "I looked into it but it doesn't quite fit the use case.\r\n\r\nI want the timeout to be refreshed and cleaned up whenever the incoming value changes.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r868399900"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-966772096", "body": ""}
{"comment": {"body": "yeah i dont think it should go into the Input component. loading states, icons, etc don't make sense for other types of inputs, so I still think it's better to write a wrapper that renders an Input component and all the loading spinners/icons etc ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r868402888"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-966839324", "body": ""}
{"comment": {"body": "Hmm okay. Let's have this chat over in this PR as it shouldn't technically block this one?\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1216", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r868469373"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-966916271", "body": "Matt probably best to look over the store logic but  to unblock() your work"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-968467206", "body": ""}
{"comment": {"body": "I think the logic here is wrong.  Won't this be called each time `value` changes, meaning that every time it changes, setTimeout is run, a new timer is created, and after the delay the value is set?  If so, this isn't debouncing at all, it's just changing the values after a delay, at the same rate.\r\n\r\nWe should be do something like: store the handler value in a useRef value, so that every time this is called, we can clear the old timer if it exists.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r869721022"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-968467738", "body": ""}
{"comment": {"body": "(There is also already a debouncer helper (`Debouncer.ts`).  Not sure if it's useful in context of a hook, you could maybe set that in the ref instead of the timer itself.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r869721360"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-968471018", "body": ""}
{"comment": {"body": "It feels like it may make the code simpler if we just moved this into the query, instead of having this as a set of separate stores and joining the streams together?\r\n\r\nImagine if `updateSearch` took in a set of all of the teams/repos we wanted to query on.  It could issue `n` queries in parallel, await on all of them, and issue the results, very easily.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r869723751"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#pullrequestreview-968566776", "body": ""}
{"comment": {"body": "If we *do* keep this structure, this method should probably be factored out into a generic stream operator.  A single method that joins n `Stream<ValueCacheState<T[]>>` into a single `Stream<ValueCacheState<T[]>>` is useful.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1192#discussion_r869791925"}}
{"title": "Handle non-standard UUIDs, cheers Apple!", "number": 1193, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1193"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1193#pullrequestreview-965099427", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1193#pullrequestreview-965099658", "body": ""}
{"title": "Add producer permission on search_indexing and pr_ingestion queues to admin service", "number": 1194, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1194", "body": "I'd like to be able to trigger re-indexing or re-ingestion from the admin console"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1194#pullrequestreview-965109146", "body": ""}
{"title": "Update Ben cidr", "number": 1196, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1196"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1196#pullrequestreview-965192700", "body": ""}
{"title": "Add notifications", "number": 1197, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1197", "body": "Summary\nThere were a few pusher related bugs that are fixed in this PR around updating unread status.\nI've added notifications, which always fire when there are new unread threads. Notifications permissions are requested when the app starts for the first time - we might want to think about whether that's the best time to do that. The permissions request experience for notifications on macOS is a part of the notifications system. A notification like alert is presented asking for notification permissions, and an option button appears on the notification to allow/deny."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1197#pullrequestreview-966642504", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1197#pullrequestreview-966657419", "body": ""}
{"title": "Clarify unread states", "number": 1198, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1198", "body": "Make it easier to understand.\nIf this makes sense, then I'll push this data type through to clients on next change."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1198#pullrequestreview-965250538", "body": ""}
{"title": "Add admin console button to trigger search reindexing for a repo", "number": 1199, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#pullrequestreview-965233710", "body": ""}
{"comment": {"body": "Oops forgot to figure this out...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#discussion_r867249336"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#pullrequestreview-965234377", "body": ""}
{"comment": {"body": "why only anchor?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#discussion_r867251002"}}
{"comment": {"body": "this doesn't _clear_ the search index for deleted threads and messages; do we need to do that? if so, then might be clearer to _send_ a `repoId` instead and let downstream handle it? just a thought, this is good too.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#discussion_r867252619"}}
{"comment": {"body": "fine for now. I think we should use html at some stage, which allow us to send aux requests and partially update HTML components (in this case a button) without having to reload a page. see https://htmx.org/", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#discussion_r867253899"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#pullrequestreview-965235728", "body": ""}
{"comment": {"body": "There's probably a better place to put this", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#discussion_r867251101"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#pullrequestreview-965240105", "body": ""}
{"comment": {"body": "I'm thinking in a menu on the right side of page, under the related objects.\r\nfine for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#discussion_r867254842"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#pullrequestreview-965240340", "body": ""}
{"comment": {"body": "The event handler will clear any entries for the thread or message", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#discussion_r867255020"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#pullrequestreview-965245752", "body": ""}
{"comment": {"body": "Good point. For now I wanted to start with indexing a thread and its messages just under the repo of the anchor (initial) sourcemark. If we start to have threads with sourcemarks from, say, two repos, then we'll need to index under both.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#discussion_r867259342"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#pullrequestreview-965248877", "body": ""}
{"comment": {"body": "Also we only soft delete threads and messages, so triggering indexing on a deleted thread will clear the index of its entries\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1199#discussion_r867261929"}}
{"title": "Break", "number": 12, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/12"}
{"title": "Introduce Chat db models", "number": 120, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/120", "body": "New models in green:\n- ChatModel\n- ChatMessageModel\n- ChatParticipantModel\n\nTODO\n\n[ ] tests"}
{"comment": {"body": "I think this model is pretty much bang on. We may want to create another level of indirection for `ChatMessageContent` to give the clients some flexibility in how they load messages. Imagine a user scrolls quickly through some content - we might want to show some basic metadata without loading all the content, especially if it's large. \r\n\r\nIt might be useful to model the pusher data for `ChatChannel` and `ChatChannelEvent` to see if there are any missing pieces to facilitate things like notification content, deletes/edits, etc. \r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/120#issuecomment-1020783340"}}
{"comment": {"body": "> I think this model is pretty much bang on. We may want to create another level of indirection for `ChatMessageContent` to give the clients some flexibility in how they load messages. Imagine a user scrolls quickly through some content - we might want to show some basic metadata without loading all the content, especially if it's large.\r\n> It might be useful to model the pusher data for `ChatChannel` and `ChatChannelEvent` to see if there are any missing pieces to facilitate things like notification content, deletes/edits, etc.\r\n\r\nI think it'd be a good exercise next to see how this data should be modelled in the API.  That would include modelling the workflow for updating/fetching them, as you're saying.  Since the chat content is relatively so small, I'm not sure that separating it out will be particularly beneficial overall...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/120#issuecomment-1020785710"}}
{"comment": {"body": "One additional thing: I'm wondering if SourceMark is the wrong name for the top-level container.  In my mind SourceMark feels like the piece that is associated with code-evolving-over-time (ie, what SourceVector is now), and the top-level container should be named something more generic like Annotation, Knowledge, Discussion, or something...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/120#issuecomment-1020786493"}}
{"comment": {"body": "> One additional thing: I'm wondering if SourceMark is the wrong name for the top-level container. In my mind SourceMark feels like the piece that is associated with code-evolving-over-time (ie, what SourceVector is now), and the top-level container should be named something more generic like Annotation, Knowledge, Discussion, or something...\r\n\r\nThe _only_ difference between SourceMark and SourceVector is that SourceMark contains multiple SourceVectors. If we didn't have the requirement that there are multiple code-evolving-over-time-things per \"annotation\", then there would be no reason for this distinction. But I think we need this afaict.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/120#issuecomment-1020815428"}}
{"comment": {"body": "Other thing we need to think about is how to represent screenshares in the chat/message model\r\n\r\n![CleanShot 2022-01-25 at 08 55 31@2x](https://user-images.githubusercontent.com/1924615/151022817-d07b0123-879b-4202-9893-0673ca05084e.png)\r\n.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/120#issuecomment-1021401916"}}
{"comment": {"body": "> The _only_ difference between SourceMark and SourceVector is that SourceMark contains multiple SourceVectors. If we didn't have the requirement that there are multiple code-evolving-over-time-things per \"annotation\", then there would be no reason for this distinction. But I think we need this afaict.\r\n\r\nI understand the structure and it makes sense, what does make sense to me is the naming:\r\n- I think SourceVector should be called SourceMark, since it is the thing that tracks a single point of code through time\r\n- I think SourceMark should be called something more generic like Annotation, as it is not strongly associated with a particular point in code (or *any* point in code, necessarily).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/120#issuecomment-1021436088"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/120#pullrequestreview-861756141", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/120#pullrequestreview-862596399", "body": ""}
{"title": "Sort thread messages by message created at", "number": 1200, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1200"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1200#pullrequestreview-965250767", "body": ""}
{"comment": {"body": "Test?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1200#discussion_r867263480"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1200#pullrequestreview-965250824", "body": ""}
{"comment": {"body": "sure\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1200#discussion_r867263539"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1200#pullrequestreview-965257756", "body": ""}
{"comment": {"body": "added here https://github.com/NextChapterSoftware/unblocked/pull/1200/commits/554a493d55f10bad6e86d15bded7fcbc9a4785d6", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1200#discussion_r867269517"}}
{"title": "Expose unread status in API and clients", "number": 1201, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1201"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1201#pullrequestreview-965251146", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1201#pullrequestreview-965251202", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1201#pullrequestreview-965293423", "body": ""}
{"comment": {"body": "@richiebres I think this might have inverted the unread logic?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1201#discussion_r867307197"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1201#pullrequestreview-965293593", "body": ""}
{"comment": {"body": "Ah wait maybe not...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1201#discussion_r867307402"}}
{"title": "Visual cleanup", "number": 1202, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1202", "body": "Use correct assets, colors, and app icon"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1202#pullrequestreview-965291738", "body": ""}
{"comment": {"body": "Defensive coding because the service wasn't ordering messages correctly in the response (this has also been fixed)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1202#discussion_r867305246"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1202#pullrequestreview-966658673", "body": ""}
{"title": "Fix extensionbuilds", "number": 1203, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1203", "body": "Safari has a strange behaviour compared to chrome where \"all_urls\" is necessary or else the react components get unmounted...\nThere were also CORS issues with prod builds with include credential. Not necessary for web extension.\nWill need to dig into this a lot closer. Pushing this change in for now to unblock."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1203#pullrequestreview-966854080", "body": ""}
{"comment": {"body": "FYI @jeffrey-ng we don't really need to do this, prod is assumed if no other environment is specified anywhere.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1203#discussion_r868485021"}}
{"title": "Enable auth logging to debug 401 causes", "number": 1204, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1204", "body": "change\n\nlog trace io.ktor.auth.jwt\nlog team claims violation\n\nmotivation\n\nlot's of unexplained 401s\n  \n\nresult\nspits out logs like this now for corrupt JWT, and expired JWT:\napiservice_1           | 04:47:18 | TRACE | i.k.a.jwt: Token verification failed: The Token's Signature resulted invalid when verified using the Algorithm: SHA256withRSA\napiservice_1           | 04:46:59 | TRACE | i.k.a.jwt: Token verification failed: The Token has expired on Mon May 09 04:46:31 UTC 2022."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1204#pullrequestreview-965586346", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1204#pullrequestreview-965586611", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1204#pullrequestreview-965620001", "body": ""}
{"title": "Hub Auth", "number": 1205, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003340967", "body": ""}
{"comment": {"body": "test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=b759d34f-542a-4880-9c14-4710e3b72cb8).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894902015"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003341810", "body": ""}
{"comment": {"body": "another test\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=1bb3cbd1-8809-42e5-a8c8-ead3ca29dcdd).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894902650"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003342412", "body": ""}
{"comment": {"body": "boop\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=6db33925-e3fe-427e-a54f-34a2ef593220).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894903113"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003343484", "body": ""}
{"comment": {"body": "try this\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=76011618-b9fc-4f54-80d9-6ba928f1e493).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894903964"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003344007", "body": ""}
{"comment": {"body": "and this\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=cb911deb-e297-4e61-b5b7-4fda94949886).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894904344"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003345014", "body": ""}
{"comment": {"body": "ping\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=fa9da539-4d8a-4c40-84e8-b8d7b063af47).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894905071"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003345423", "body": ""}
{"comment": {"body": "boop\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=b68a1217-ad62-4fbf-b6c0-32b7801f6463).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894905350"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003346098", "body": ""}
{"comment": {"body": "asdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=788ff6c7-da40-4861-ba90-430876f582ec).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894905814"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003347293", "body": ""}
{"comment": {"body": "another\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=26ffdb35-c78a-47d5-90c1-d5834e135a7f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894906727"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003347891", "body": ""}
{"comment": {"body": "basdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=694bc887-21cc-4c8d-97a9-68eca3bdb0c1).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894907195"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003349012", "body": ""}
{"comment": {"body": "weird\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=ef1b689d-8713-492e-810d-d355c2d107e6).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894908034"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003350808", "body": ""}
{"comment": {"body": "probably won't work\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=73a70ccd-2f1d-4582-b14c-03db7dd2d6e5).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894909391"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003351182", "body": ""}
{"comment": {"body": "again\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=3ca34a6f-31ab-4702-910c-efe157a82a7c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894909675"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003353095", "body": ""}
{"comment": {"body": "sample\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=ed011aa3-db84-4b8f-9a04-960b619b9f5e).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894911132"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003354835", "body": ""}
{"comment": {"body": "asfa\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=ac41e640-670c-489b-bb1c-79be42467389).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894912509"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003356438", "body": ""}
{"comment": {"body": "gasdfasdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=9c69847a-82d1-4cca-8694-828320640ab3).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894913814"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003359329", "body": ""}
{"comment": {"body": "asdfas\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=e1e8dea3-040f-4337-b987-19d62dfd5c7f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894915928"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003360216", "body": ""}
{"comment": {"body": "asdfa\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=5452e439-2bb1-4107-a2d1-0c743e14b65f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894916584"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003360499", "body": ""}
{"comment": {"body": "fasdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=157679d3-fa73-4456-a6f1-3d9f59ebd581).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894916802"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-1003361222", "body": ""}
{"comment": {"body": "asdf\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/0b86c00f-4579-40d1-8240-ef44e24f20b1?message=bd87e21a-32a6-404f-b350-10a34b8c415f).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r894917340"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-966974413", "body": ""}
{"comment": {"body": "Not now but would it make more sense to just disable the base64 url encoding in the codeegen by updating the templates?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868606818"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-966992282", "body": ""}
{"comment": {"body": "Can we refactor this out in a separate function?\r\n\r\ne.g. `setupInitialAuth`?\r\nJust so there's some context when we look back at this code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868618436"}}
{"comment": {"body": "To be clear, it is waiting on the `startBackgroundRefresh` task to refresh the token? \r\nOr is it waiting for `refreshTokenIfExpired`?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868623278"}}
{"comment": {"body": "Not now but we'll need to *eventually* time this out.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868627232"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-966998726", "body": ""}
{"comment": {"body": "For sure. I think we can basically nuke most of this file by updating the templates", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868621316"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-967019030", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-967022551", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-967033517", "body": ""}
{"comment": {"body": "Super painful because init accesses isolated properties, which means factoring out to another function would require init to be async. If the factored function is nonisolated then the compiler complains about access to isolated properties. We might have to figure out a better pattern for this...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868663166"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-967034257", "body": ""}
{"comment": {"body": "`refreshTokenIfExpired` is the function that ultimately handles refreshing the token, but it happens in two places:\r\n1. the first time the auth store is \"waited\" on\r\n2. in the background refresh task", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868665341"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-967034342", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-967034938", "body": ""}
{"comment": {"body": "I knew I was forgetting something important...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868667305"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-967147017", "body": ""}
{"comment": {"body": "ping\n\n\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868794751"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-967147623", "body": ""}
{"comment": {"body": "Peter told me to comment here. \n\n\n\nPS: Rashin is awesome!\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r868795269"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-968011583", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r869394827"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-968022233", "body": ""}
{"comment": {"body": "The quick brown fox jumped over the lazy dog.\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r869405219"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-971436506", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r871793949"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-972548925", "body": ""}
{"comment": {"body": "test\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r872605960"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-972558003", "body": ""}
{"comment": {"body": "hi\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r872611961"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#pullrequestreview-972558225", "body": ""}
{"comment": {"body": "ping\n\n###### This comment was created from unblocked.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1205#discussion_r872612112"}}
{"title": "Correct retry semantics", "number": 1206, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1206"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1206#pullrequestreview-965637261", "body": ""}
{"title": "Finish indexing messages before taking the next batch of messages off the queue", "number": 1207, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1207", "body": "The search service is getting killed because its using too much CPU. I suspect it's because when we throw two thousand events onto the queue at once, it leads to that number of coroutines being launched at the same time. \nNot entirely sure, but let's try finishing processing one batch of events before taking the next batch of events off the queue. This will slow down indexing, so maybe the answer is scaling the instances."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1207#pullrequestreview-966250949", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1207#pullrequestreview-966445942", "body": ""}
{"title": "Use failsafe library being used elsewhere.", "number": 1208, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1208", "body": "Move to failsafe library which has superseded resilience4j as the predominant failure library used by big corps etc.\nAlready using this elsewhere, and works well."}
{"comment": {"body": "And this is why I love Rashin so much", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1208#issuecomment-1120728153"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1208#pullrequestreview-965683974", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1208#pullrequestreview-965692284", "body": ""}
{"title": "Fix unread status in TS clients", "number": 1209, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1209"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1209#pullrequestreview-966447998", "body": ""}
{"comment": {"body": "The majority of threads don't have an associated Unread object -- if that's the case we should consider them read, not unread.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1209#discussion_r868191919"}}
