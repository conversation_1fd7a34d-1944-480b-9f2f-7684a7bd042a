[tool.poetry]
name = "cluster-processor-image"
version = "1.0.0"
description = ""
authors = []
packages = [
    { include = "cluster_processor_image", from = "src" },
]

[tool.poetry.dependencies]
accelerate = "^0"
bertopic = "^0.16.0"
boto3 = '^1'
botocore = '^1'
hdbscan = "^0"
huggingface_hub = "0.25.2"
instructorembedding = "^1"
langchain = '^0'
llama-index-core = '^0'
nltk = "^3"
numpy = '<2'
pandas = "^2"
python = ">=3.10.12,<3.14"
retrying = "^1"
sentence-transformers = '2.2.2'
umap-learn = "^0"
unblocked-aws-utils = "^0"
unblocked-embedding-utils = "^0"
unblocked-file-utils = "^0"
unblocked-git-utils = "^0"
unblocked-integration-utils = "^0"
unblocked-partition-utils = "^0"
unblocked-pinecone-utils = "^0"
unblocked-text-utils = "^0"
unblocked-uuid-utils = "^0"

[tool.poetry.group.dev.dependencies]
altair = "^5"
black = "^24"
matplotlib = "^3"
pytest = "^8"
wordcloud = "^1"
seaborn = "^0"

[tool.black]
extend-exclude = '__pycache__'
include = '\.pyi?$'
line-length = 120

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
