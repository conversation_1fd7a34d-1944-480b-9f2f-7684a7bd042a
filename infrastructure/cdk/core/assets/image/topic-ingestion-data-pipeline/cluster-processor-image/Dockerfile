FROM --platform="linux/amd64" python:3.11-slim-bookworm

# Necessary for HDBSCAN
RUN apt-get update \
  && apt-get -y install gcc  \
  && apt-get clean  \
  && apt-get autoclean \
  && apt-get autoremove

# Install Git
RUN apt-get install -y git

# Use Poetry to pin dependencies for sanity,
# and keep the development environment consistent with the production environment.
RUN pip3 install poetry==2.1.2

# Set up JFrog artifactory configuration such that we can pull from our private repositories
ARG JFROG_PASSWORD
RUN poetry config  http-basic.jfrog-server "<EMAIL>" "$JFROG_PASSWORD"

ARG ENVIRONMENT
ENV ENVIRONMENT=$ENVIRONMENT
RUN git config --global http.proxy  "http://proxy.$ENVIRONMENT.getunblocked.com:80" \
    && git config --global https.proxy "http://proxy.$ENVIRONMENT.getunblocked.com:80"

WORKDIR /code

# Install dependencies
COPY . /code

# The install of torch is required for using CUDA (problem with poetry installation)
RUN POETRY_VIRTUALENVS_CREATE=false poetry install --no-cache --no-interaction --without dev \
    && pip3 install torch==2.7.0 --upgrade --no-cache-dir
RUN poetry config virtualenvs.create false

# Add a Python script and configure Docker to run it
ENTRYPOINT ["poetry", "run", "python", "/code/src/cluster_processor_image/process_topics.py"]
