import asyncio
import logging
import time

import os

from langchain_core.documents import Document
from lightrag.lightrag import LightRAG, QueryParam
from lightrag.llm import gpt_4o_mini_complete, gpt_4o_complete

from source_code_processor_lightrag.document_loader import DirectoryDocumentLoader
from source_code_processor_lightrag.graphrag_constants import GRAPHRAG_ALLOWED_NODES
from source_code_processor_lightrag.neo4j_constants import NEO4J_URI, NEO4J_PASSWORD, NEO4J_USERNAME, NEO4J_DATABASE

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
)


async def process_repo(repo_dir: str, rag: LightRAG, concurrency_limit: int = 10):
    semaphore = asyncio.Semaphore(concurrency_limit)

    documents = DirectoryDocumentLoader(directory_path=repo_dir).load_documents()

    async def rag_insert(document: Document):
        async with semaphore:
            await rag.ainsert(document.page_content)

    tasks = [rag_insert(document) for document in documents]
    await asyncio.gather(*tasks)


async def main():
    logging.info("starting processing")

    working_dir = "./working_dir"
    if not os.path.exists(working_dir):
        os.mkdir(working_dir)

    repo_dir = "/Users/<USER>/chapter2/unblocked/projects/libs/lib-slack-ingestion"

    os.environ["NEO4J_URI"] = NEO4J_URI
    os.environ["NEO4J_USERNAME"] = NEO4J_USERNAME
    os.environ["NEO4J_PASSWORD"] = NEO4J_PASSWORD
    os.environ["NEO4J_DATABASE"] = NEO4J_DATABASE

    rag = LightRAG(
        working_dir=working_dir,
        llm_model_func=gpt_4o_mini_complete,
        graph_storage="Neo4JStorage",
        log_level="DEBUG",
        addon_params={"entity_types": GRAPHRAG_ALLOWED_NODES},
    )

    # start_time = time.time()
    # await process_repo(repo_dir=repo_dir, rag=rag, concurrency_limit=10)
    # elapsed_time = time.time() - start_time
    # logging.info(f"Execution time: {elapsed_time:.2f} seconds")

    result = await rag.aquery_with_separate_keyword_extraction(
        query="Can you generate a mermaid interaction diagram for how Slack Ingestion works including major and minor entities?",
        prompt="Answer the Question using the following Context. Do not inject any speculative information not mentioned. You can generate diagrams as well.",
        param=QueryParam(mode="mix"),
    )

    print(result)


if __name__ == "__main__":
    asyncio.run(main())
