import logging

from langchain.agents import <PERSON>E<PERSON><PERSON><PERSON>
from langchain_community.graphs.graph_document import GraphDocument
from langchain_core.embeddings import Embeddings
from langchain_core.language_models import BaseChatModel
from langchain_core.vectorstores import VectorStore
from langchain_neo4j.graphs.graph_store import GraphStore
from langchain_openai import ChatOpenAI
from langchain_openai import OpenAIEmbeddings

from source_code_processor.agent_executor_factory import Neo4jAgentExecutorFactory
from source_code_processor.document_loader import ChunkedDocumentLoader, DirectoryDocumentLoader
from source_code_processor.graph_document_loader import LLMGraphDocumentLoader
from source_code_processor.graph_manager import Graph<PERSON>anager
from source_code_processor.graph_store_factory import Neo4jGraphStoreFactory
from source_code_processor.graphrag_constants import (
    GRAPHRAG_DOCUMENT_INSTRUCTIONS,
    GRAPHRAG_ALLOWED_NODES,
    GRAPHRAG_ALLOWED_RELATIONSHIPS,
    GRAPHRAG_NODE_PROPERTIES,
    GRAPHRAG_RELATIONSHIP_PROPERTIES,
)
from source_code_processor.neo4j_constants import NEO4J_URI, NEO4J_PASSWORD, NEO4J_USERNAME, NEO4J_DATABASE
from source_code_processor.openai_constants import OPENAI_API_KEY, DEFAULT_OPENAI_CHAT_MODEL
from source_code_processor.pickle_utils import save_object_to_pickle, load_object_from_pickle
from source_code_processor.vector_store_factory import Neo4jVectorStoreFactory

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
)


def create_llm(openai_api_key: str, model_name: str) -> BaseChatModel:
    return ChatOpenAI(model_name=model_name, openai_api_key=openai_api_key, verbose=True)


def create_embeddings(openai_api_key: str) -> Embeddings:
    return OpenAIEmbeddings(openai_api_key=openai_api_key)


def load_graph_documents(repo_dir: str, llm: BaseChatModel) -> list[GraphDocument]:
    documents = ChunkedDocumentLoader(
        document_loader=DirectoryDocumentLoader(directory_path=repo_dir),
    ).load_and_chunk()

    graph_documents = LLMGraphDocumentLoader(
        llm=llm,
        allowed_nodes=GRAPHRAG_ALLOWED_NODES,
        allowed_relationships=GRAPHRAG_ALLOWED_RELATIONSHIPS,
        node_properties=GRAPHRAG_NODE_PROPERTIES,
        relationship_properties=GRAPHRAG_RELATIONSHIP_PROPERTIES,
        additional_instructions=GRAPHRAG_DOCUMENT_INSTRUCTIONS,
    ).load_graph_documents(
        documents=documents,
    )

    return graph_documents


def create_graph_store(
    uri: str,
    username: str,
    password: str,
    database: str,
    graph_documents: list[GraphDocument],
) -> GraphStore:
    graph_store = Neo4jGraphStoreFactory(
        uri=uri,
        username=username,
        password=password,
        database=database,
    ).get_graph_store(
        graph_documents=graph_documents,
    )

    return graph_store


def create_vector_store(
    uri: str,
    username: str,
    password: str,
    database: str,
    embeddings: Embeddings,
    node_label: str,
    text_node_properties: list[str],
    embedding_node_property: str,
) -> VectorStore:
    vector_store = Neo4jVectorStoreFactory(
        uri=uri,
        username=username,
        password=password,
        database=database,
        embeddings=embeddings,
        node_label=node_label,
        text_node_properties=text_node_properties,
        embedding_node_property=embedding_node_property,
    ).get_vector_store()

    return vector_store


def mutate_graph(llm: BaseChatModel, graph_store: GraphStore, uri: str, username: str, password: str):
    graph_manager = GraphManager(
        graph_store=graph_store,
        uri=uri,
        username=username,
        password=password,
    )
    graph_manager.mutate_graph_with_similarity()
    graph_manager.mutate_graph_with_communities()
    graph_manager.mutate_graph_with_community_summaries(llm=llm)


def create_agent_executor(
    llm: BaseChatModel,
    vector_store: VectorStore,
    graph_store: GraphStore,
) -> AgentExecutor:
    return Neo4jAgentExecutorFactory(
        graph_store=graph_store,
        vector_store=vector_store,
        llm=llm,
    ).create_agent_executor()


def answer_query(
    agent_executor: AgentExecutor,
    query: str,
) -> str:
    return agent_executor.run(query)


def main():
    logging.info("starting processing")

    repo_dir = "/Users/<USER>/chapter2/unblocked/projects/libs/lib-slack-bot/src/main/kotlin/com/nextchaptersoftware/slack/bot/models/payload"

    llm = create_llm(openai_api_key=OPENAI_API_KEY, model_name=DEFAULT_OPENAI_CHAT_MODEL)
    embeddings = create_embeddings(openai_api_key=OPENAI_API_KEY)
    # graph_documents = load_graph_documents(repo_dir=repo_dir, llm=llm)
    # save_object_to_pickle(
    #     obj=graph_documents,
    #     file_path="/Users/<USER>/chapter2/unblocked/infrastructure/cdk/core/assets/image/graphrag-ingestion-data-pipeline/graphrag-processor-image/graph_documents.pkl"
    # )
    graph_documents = load_object_from_pickle(
        file_path="/Users/<USER>/chapter2/unblocked/infrastructure/cdk/core/assets/image/graphrag-ingestion-data-pipeline/graphrag-processor-image/graph_documents.pkl",
    )
    graph_store = create_graph_store(
        uri=NEO4J_URI,
        username=NEO4J_USERNAME,
        password=NEO4J_PASSWORD,
        database=NEO4J_DATABASE,
        graph_documents=graph_documents,
    )
    vector_store = create_vector_store(
        uri=NEO4J_URI,
        username=NEO4J_USERNAME,
        password=NEO4J_PASSWORD,
        database=NEO4J_DATABASE,
        embeddings=embeddings,
        node_label="__Entity__",
        text_node_properties=["description", "definition"],
        embedding_node_property="embedding",
    )
    # mutate_graph(llm=llm, graph_store=graph_store, uri=NEO4J_URI, username=NEO4J_USERNAME, password=NEO4J_PASSWORD)
    agent_executor = create_agent_executor(
        llm=llm,
        vector_store=vector_store,
        graph_store=graph_store,
    )
    answer_query(
        agent_executor=agent_executor, query="What are some classes that extend SlackBotMemberJoinedChannelPayload?"
    )


if __name__ == "__main__":
    main()
