from abc import ABC, abstractmethod
from typing import List

from langchain_community.graphs.graph_document import GraphDocument
from langchain_neo4j import Neo4jGraph
from langchain_neo4j.graphs.graph_store import GraphStore


class GraphStoreFactoryInterface(ABC):
    """
    Interface for managing graph operations with a backend.
    """

    @abstractmethod
    def get_graph_store(
        self, graph_documents: List[GraphDocument], include_source: bool = True, base_entity_label: bool = True
    ) -> GraphStore:
        """
        Load graph documents into the underlying graph backend.

        Args:
            graph_documents (List[GraphDocument]): A list of LangChain GraphDocument objects.
            include_source (bool): Whether to include source metadata in the graph.
            base_entity_label (bool): Whether to use the base entity label for the graph nodes.
        """
        pass


class Neo4jGraphStoreFactory(GraphStoreFactoryInterface):
    """
    Neo4j implementation of the GraphManager interface using LangChain's Neo4jGraph.
    """

    def __init__(self, uri: str, username: str, password: str, database: str = "neo4j"):
        """
        Initializes the Neo4j graph manager.

        Args:
            uri (str): Neo4j connection URI (e.g., "bolt://localhost:7687").
            username (str): Username for Neo4j authentication.
            password (str): Password for Neo4j authentication.
            database (str): Neo4j database name (default is "neo4j").
        """
        self.uri = uri
        self.username = username
        self.password = password
        self.database = database

    def get_graph_store(
        self, graph_documents: List[GraphDocument], include_source: bool = True, base_entity_label: bool = True
    ) -> GraphStore:
        """
        Loads the graph documents into the Neo4j database.

        Args:
            graph_documents (List[GraphDocument]): A list of LangChain GraphDocument objects.
            include_source (bool): Whether to include source metadata in the graph.
            base_entity_label (bool): Whether to use the base entity label for the graph nodes.
        """
        graph = Neo4jGraph(url=self.uri, username=self.username, password=self.password, database=self.database)

        # Optionally add source metadata to each node and edge
        graph.add_graph_documents(
            graph_documents=graph_documents, include_source=False, baseEntityLabel=base_entity_label
        )

        return graph
