import asyncio
import logging
import time

import os

from langchain_core.documents import Document
from neo4j import GraphDatabase, Driver
from neo4j_graphrag.experimental.components.text_splitters.fixed_size_splitter import FixedSizeSplitter
from neo4j_graphrag.generation import GraphRAG
from neo4j_graphrag.embeddings import OpenAIEmbeddings
from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
from neo4j_graphrag.generation import RagTemplate
from neo4j_graphrag.indexes import create_vector_index, create_fulltext_index
from neo4j_graphrag.llm import OpenAIL<PERSON>
from neo4j_graphrag.retrievers import VectorR<PERSON>riever, VectorCypherRetriever, HybridCypherRetriever

from source_code_processor_neo4j.document_loader import DirectoryDocumentLoader
from source_code_processor_neo4j.graphrag_constants import (
    GRAPHRAG_ALLOWED_NODES,
    GRAPHRAG_ALLOWED_RELATIONSHIPS,
    GRAPHRAG_POTENTIAL_SCHEMA,
    GRAPHRAG_PROMPT_TEMPLATE,
)
from source_code_processor_neo4j.neo4j_constants import NEO4J_URI, NEO4J_PASSWORD, NEO4J_USERNAME, NEO4J_DATABASE
from source_code_processor_neo4j.openai_constants import OPENAI_API_KEY

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
)

CYPHER_RETRIEVER_QUERY = """
    //1) Go out 2-3 hops in the entity graph and get relationships
    WITH node AS chunk
    MATCH (chunk)<-[:FROM_CHUNK]-()-[relList:!FROM_CHUNK]-{1,2}()
    UNWIND relList AS rel

    //2) collect relationships and text chunks
    WITH collect(DISTINCT chunk) AS chunks,
      collect(DISTINCT rel) AS rels

    //3) format and return context
    RETURN '=== text ===\n' +
           apoc.text.join([c in chunks | c.text], '\n---\n') + '\n\n=== kg_rels ===\n' +
           apoc.text.join([r in rels | startNode(r).name + ' - ' + type(r) + ' -> ' + endNode(r).name ], '\n---\n') AS info
"""


async def process_repo(repo_dir: str, kg_builder: SimpleKGPipeline, concurrency_limit: int = 10):
    semaphore = asyncio.Semaphore(concurrency_limit)

    documents = DirectoryDocumentLoader(directory_path=repo_dir).load_documents()

    async def rag_insert(document: Document):
        async with semaphore:
            await kg_builder.run_async(text=document.page_content)

    tasks = [rag_insert(document) for document in documents]
    await asyncio.gather(*tasks)


# https://github.com/neo4j-product-examples/graphrag-python-examples/blob/2c2a4b6c23fae2b6317c688252175c23fc728a07/end-to-end-lupus.ipynb#L236
async def main():
    logging.info("starting processing")

    repo_dir = "/Users/<USER>/chapter2/unblocked/projects/libs/lib-slack-ingestion"

    os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY
    os.environ["NEO4J_URI"] = NEO4J_URI
    os.environ["NEO4J_USERNAME"] = NEO4J_USERNAME
    os.environ["NEO4J_PASSWORD"] = NEO4J_PASSWORD
    os.environ["NEO4J_DATABASE"] = NEO4J_DATABASE

    # Create an Embedder object
    embedder = OpenAIEmbeddings(model="text-embedding-3-large")

    # Instantiate the LLM
    llm = OpenAILLM(
        model_name="gpt-4o",
        model_params={
            "max_tokens": 2000,
            "response_format": {"type": "json_object"},
            "temperature": 0,
        },
    )

    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USERNAME, NEO4J_PASSWORD), database=NEO4J_DATABASE)

    # # Instantiate the SimpleKGPipeline
    kg_builder = SimpleKGPipeline(
        llm=llm,
        driver=driver,
        embedder=embedder,
        entities=GRAPHRAG_ALLOWED_NODES,
        relations=GRAPHRAG_ALLOWED_RELATIONSHIPS,
        on_error="IGNORE",
        from_pdf=False,
        neo4j_database=NEO4J_DATABASE,
        # text_splitter=FixedSizeSplitter(chunk_size=1250, chunk_overlap=100),
    )

    # start_time = time.time()
    # await process_repo(repo_dir=repo_dir, kg_builder=kg_builder, concurrency_limit=10)
    # elapsed_time = time.time() - start_time
    # logging.info(f"Execution time: {elapsed_time:.2f} seconds")

    # https://github.com/neo4j-product-examples/graphrag-python-examples/blob/2c2a4b6c23fae2b6317c688252175c23fc728a07/webinar_demo/Demo_QA.ipynb#L71
    vector_index_name = "text_embeddings"
    create_vector_index(
        driver=driver,
        name="text_embeddings",
        label="Chunk",
        embedding_property="embedding",
        dimensions=3072,
        similarity_fn="cosine",
    )
    vector_retriever = VectorRetriever(
        driver=driver,
        index_name=vector_index_name,
        embedder=embedder,
        return_properties=["text"],
    )

    fulltext_index_name = "fulltext_index"
    create_fulltext_index(driver=driver, name=fulltext_index_name, label="Chunk", node_properties=["text"])


    q = "What are the various class types for message analysis results during slack ingestion?"
    # q = "Can you generate a mermaid graph diagram for how Slack Ingestion works including the prominent classes paying attention to interactions and flow. Include descriptions on the edges"

    vector_res = vector_retriever.search(
        query_text=q,
        top_k=3,
    )

    for i in vector_res.items:
        print("====\n" + i.model_dump_json())

    vc_retriever = VectorCypherRetriever(
        driver,
        index_name=vector_index_name,
        embedder=embedder,
        retrieval_query=CYPHER_RETRIEVER_QUERY,
    )

    hc_retriever = HybridCypherRetriever(
        driver=driver,
        vector_index_name=vector_index_name,
        fulltext_index_name=fulltext_index_name,
        retrieval_query=CYPHER_RETRIEVER_QUERY,
        embedder=embedder,
    )

    rag_template = RagTemplate(
        template="""Answer the Question using the following Context. Only respond with information mentioned in the Context. Do not inject any speculative information or entities not mentioned.
    Output the results in JSON.

    # Question:
    {query_text}

    # Context:
    {context}

    # Answer:
    """,
        expected_inputs=["query_text", "context"],
    )

    # Instantiate the LLM
    qa_llm = OpenAILLM(
        model_name="gpt-4o-mini",
        model_params={
            "temperature": 0,
        },
    )

    v_rag = GraphRAG(llm=qa_llm, retriever=vector_retriever, prompt_template=rag_template)
    vc_rag = GraphRAG(llm=qa_llm, retriever=vc_retriever, prompt_template=rag_template)
    hc_rag = GraphRAG(llm=qa_llm, retriever=hc_retriever, prompt_template=rag_template)

    v_rag_result = v_rag.search(query_text=q, retriever_config={"top_k": 10}, return_context=True)
    vc_rag_result = vc_rag.search(query_text=q, retriever_config={"top_k": 10}, return_context=True)
    hc_rag_result = hc_rag.search(query_text=q, retriever_config={"top_k": 10}, return_context=True)

    print(f"Vector Response: \n{v_rag_result.answer}")
    print("\n===========================\n")
    print(f"Vector + Cypher Response: \n{vc_rag_result.answer}")
    print("\n===========================\n")
    print(f"Hybrid FullText + Vector + Cypher Response: \n{hc_rag_result.answer}")


if __name__ == "__main__":
    asyncio.run(main())
