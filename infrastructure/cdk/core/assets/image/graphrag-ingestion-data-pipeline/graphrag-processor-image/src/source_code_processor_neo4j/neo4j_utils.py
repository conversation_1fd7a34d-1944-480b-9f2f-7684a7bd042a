from neo4j import GraphDatabase


# Function to create a database
def create_database(uri, username, password, database_name):
    # Connect to the Neo4j instance
    driver = GraphDatabase.driver(uri, auth=(username, password))
    try:
        with driver.session() as session:
            # Execute the Cypher command to create a database
            session.run(f"CREATE DATABASE {database_name}")
            print(f"Database '{database_name}' created successfully.")
    except Exception as e:
        print(f"Failed to create database '{database_name}': {e}")
    finally:
        # Close the connection
        driver.close()
