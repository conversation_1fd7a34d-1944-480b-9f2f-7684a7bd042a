# Define custom instructions for source code-related entity extraction
GRAPHRAG_PROMPT_TEMPLATE = """
You are a software engineer tasked with analyzing source code
and structuring it in a property graph to inform further software development, refactoring, and architectural insights.

Extract the entities (nodes) and specify their type from the following Input code.
Also extract the relationships between these nodes. The relationship direction goes from the start node to the end node.

Return result as JSON using the following format:
{{"nodes": [ {{"id": "0", "label": "the type of entity", "properties": {{"name": "name of entity" }} }}],
  "relationships": [{{"type": "TYPE_OF_RELATIONSHIP", "start_node_id": "0", "end_node_id": "1", "properties": {{"details": "Description of the relationship"}} }}] }}

- Use only the information from the Input code. Do not add any additional information.
- If the input code is empty, return an empty JSON.
- Create as many nodes and relationships as needed to provide a rich representation of the source code structure.
- The property graph will be used to analyze software structure, dependencies, and interactions, so ensure entity types are meaningful and general enough for broad software analysis.
- Multiple codebases and repositories will be ingested, and we are using this property graph to connect information, so ensure consistency in entity types.

Use only the following nodes and relationships (if provided):
{schema}

Assign a unique ID (string) to each node and reuse it to define relationships.
Respect the source and target node types for relationships and
the relationship direction.

Make sure you adhere to the following rules to produce valid JSON objects:
- Do not return any additional information other than the JSON in it.
- Omit any backticks around the JSON - simply output the JSON on its own.
- The JSON object must not wrapped into a list - it is its own JSON object.
- Property names must be enclosed in double quotes

### **Examples:**
{examples}

### **Input Source Code:**
{text}
"""

GRAPHRAG_ALLOWED_NODES = [
    "class",  # Represents a Java class
    "interface",  # Represents a Java interface
    "enum",  # Represents a Java enum
    "annotation",  # Represents a Java annotation
    "function",  # Represents a Java method
    "constructor",  # Represents a Java class constructor
    "field",  # Represents a class field (variable)
]

GRAPHRAG_ALLOWED_RELATIONSHIPS = [
    "extends",  # Class extends another class
    "implements",  # Class implements an interface
    "calls",  # Function/method calls another function/method
    "defines",  # A class/module/package defines a function/class/interface
    "overrides",  # A function overrides another function
    "declares",  # A class declares a constructor, field, or function
    "has_field",  # A class has a field (variable)
    "annotates",  # A class, function, or field is annotated with an annotation
    "contained_in",  # A function, field, or constructor is contained in a class
]

GRAPHRAG_POTENTIAL_SCHEMA = potential_schema = [
    ("class", "extends", "class"),  # A class extends another class (inheritance)
    ("class", "implements", "interface"),  # A class implements an interface
    ("class", "defines", "function"),  # A class defines a function (method)
    ("class", "overrides", "function"),  # A class overrides a function from a superclass
    ("class", "calls", "function"),  # A class calls a function (method invocation)
    ("interface", "extends", "interface"),  # An interface extends another interface
    ("interface", "defines", "function"),  # An interface defines a function (abstract method)
    ("function", "calls", "function"),  # A function (method) calls another function
    ("function", "overrides", "function"),  # A function (method) overrides another function in a subclass
]
