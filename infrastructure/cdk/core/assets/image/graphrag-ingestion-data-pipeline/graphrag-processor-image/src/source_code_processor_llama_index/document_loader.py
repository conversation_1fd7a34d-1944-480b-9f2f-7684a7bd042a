from abc import ABC, abstractmethod
from llama_index.core.schema import BaseNode
from llama_index.core import Document
from llama_index.core import SimpleDirectoryReader
from llama_index.core.node_parser import SimpleNodeParser


class DocumentLoaderInterface(ABC):
    @abstractmethod
    def load_documents(self) -> list[Document]:
        """
        Abstract method to load a set of documents.

        Returns:
            list[Document]: A list of LangChain `Document` objects.
        """
        pass


class DirectoryDocumentLoader(DocumentLoaderInterface):
    def __init__(self, directory_path: str):
        """
        Initializes the document loader with a directory path.

        Args:
            directory_path (str): The path to the directory containing documents.
        """
        self.directory_path = directory_path

    def load_documents(self) -> list[Document]:
        """
        Loads documents from the directory using LangChain's `DirectoryLoader`.

        Returns:
            list[Document]: A list of LangChain `Document` objects.
        """
        loader = SimpleDirectoryReader(
            self.directory_path,
            required_exts=[".txt", ".kt"],
            recursive=True,
        )
        return loader.load_data()


class ChunkedDocumentLoader:
    def __init__(self, document_loader: DocumentLoaderInterface, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        A wrapper around any document loader to add chunking functionality.

        Args:
            document_loader: An instance of a document loader.
            chunk_size (int): Maximum number of tokens per chunk.
            chunk_overlap (int): Number of tokens to overlap between chunks.
        """
        self.document_loader = document_loader
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

    def load_and_chunk(self) -> list[BaseNode]:
        """
        Loads documents using the wrapped loader and chunks them.

        Returns:
            list[Document]: A list of LlamaIndex `Document` objects, chunked by token counts.
        """
        # Load documents using the provided loader
        documents = self.document_loader.load_documents()

        # Apply chunking
        return self.chunk_documents(documents)

    def chunk_documents(self, documents: list[Document]) -> list[BaseNode]:
        """
        Splits the loaded documents into smaller chunks based on token counts.

        Args:
            documents (list[Document]): A list of LlamaIndex `Document` objects.

        Returns:
            list[Document]: A list of chunked `Document` objects.
        """
        parser = SimpleNodeParser(chunk_size=self.chunk_size, chunk_overlap=self.chunk_overlap)
        return parser.get_nodes_from_documents(documents)
