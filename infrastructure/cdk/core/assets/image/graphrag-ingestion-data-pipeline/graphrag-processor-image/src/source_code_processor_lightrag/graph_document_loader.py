from abc import ABC, abstractmethod
from typing import Optional
from langchain.schema import Document
from langchain_community.graphs.graph_document import GraphDocument
from langchain_core.language_models import BaseChatModel
from langchain_experimental.graph_transformers import LLMGraphTransformer

import logging


class GraphDocumentLoaderInterface(ABC):
    """
    Interface for loading LangChain GraphDocument objects.
    """

    @abstractmethod
    def load_graph_documents(self, documents: list[Document]) -> list[GraphDocument]:
        """
        Load and return a list of LangChain GraphDocument objects from input documents.

        Args:
            documents (list[Document]): A list of input LangChain Document objects.

        Returns:
            list[GraphDocument]: A list of graph documents.
        """
        pass


class LLMGraphDocumentLoader(GraphDocumentLoaderInterface):
    """
    Concrete implementation of GraphDocumentLoader using LLMGraphTransformer.
    """

    def __init__(
        self,
        llm: BaseChatModel,
        allowed_nodes: Optional[list[str]] = None,
        allowed_relationships: Optional[list[str]] = None,
        node_properties: Optional[list[str]] = None,
        relationship_properties: Optional[list[str]] = None,
        additional_instructions: Optional[str] = None,
    ):
        """
        Initializes the loader with source documents and configuration.

        Args:
            llm (BaseChatModel): The language model to use for graph transformation.
            allowed_nodes (Optional[list[str]]): A list of allowed node types.
            allowed_relationships (Optional[list[str]]): A list of allowed relationship types.
            node_properties (Optional[list[str]]): A list of node properties to include.
            relationship_properties (Optional[list[str]]): A list of relationship properties to include.
        """
        self.llm = llm
        self.allowed_nodes = allowed_nodes
        self.allowed_relationships = allowed_relationships
        self.node_properties = node_properties
        self.relationship_properties = relationship_properties
        self.additional_instructions = additional_instructions

    def load_graph_documents(self, documents: list[Document]) -> list[GraphDocument]:
        """
        Converts input documents into LangChain GraphDocument objects.

        Args:
            documents (list[Document]): A list of input LangChain documents.

        Returns:
            list[GraphDocument]: A list of graph documents.
        """
        transformer = LLMGraphTransformer(
            llm=self.llm,
            allowed_nodes=self.allowed_nodes,
            allowed_relationships=self.allowed_relationships,
            node_properties=self.node_properties,
            relationship_properties=self.relationship_properties,
            additional_instructions=self.additional_instructions or "",
        )

        # Convert the graph data into GraphDocument objects
        graph_documents = transformer.convert_to_graph_documents(documents=documents)
        updated_graph_documents = self.__validate_and_fill_missing_fields(
            graph_documents=graph_documents,
            required_fields=self.node_properties,
        )

        return updated_graph_documents

    def __validate_and_fill_missing_fields(
        self, graph_documents: list[GraphDocument], required_fields: list[str]
    ) -> list[GraphDocument]:
        """
        Ensures all nodes in a list of GraphDocument objects have the required fields.
        Fills missing fields with empty strings for nodes that lack them.

        :param graph_documents: A list of LangChain GraphDocument objects.
        :param required_fields: A list of required fields for each node.
        :return: A list of GraphDocument objects with missing fields filled.
        """
        for graph_document in graph_documents:
            for node in graph_document.nodes:
                # Ensure all required fields exist in the node
                for field in required_fields:
                    if field not in node.properties:
                        node.properties[field] = ""

        return graph_documents
