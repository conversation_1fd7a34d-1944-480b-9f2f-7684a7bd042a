from abc import ABC, abstractmethod

from langchain.vectorstores.base import VectorStore
from langchain_community.vectorstores import Neo4jVector
from langchain_core.embeddings import Embeddings


class VectorStoreFactoryInterface(ABC):
    """
    Interface for managing vector stores.
    """

    @abstractmethod
    def get_vector_store(self) -> VectorStore:
        """
        Retrieves a LangChain vector store.

        Returns:
            VectorStore: A LangChain VectorStore instance.
        """
        pass


class Neo4jVectorStoreFactory(VectorStoreFactoryInterface):
    """
    Neo4j implementation of the VectorStoreFactoryInterface.
    """

    def __init__(
        self,
        uri: str,
        username: str,
        password: str,
        database: str,
        embeddings: Embeddings,
        node_label: str,
        text_node_properties: list[str],
        embedding_node_property: str,
    ):
        """
        Initializes the Neo4jVectorStore with connection details, embeddings, and graph metadata.

        Args:
            uri (str): Neo4j connection URI (e.g., "bolt://localhost:7687").
            username (str): <PERSON>rna<PERSON> for Neo4j authentication.
            password (str): Password for Neo4j authentication.
            database (str): Neo4j database name.
            embeddings (Embeddings): A LangChain embeddings for generating vector embeddings.
            node_label (str): Label for nodes in the graph.
            text_node_properties (List[str]): Properties of nodes that contain text data.
            embedding_node_property (str): Property where the embeddings are stored.
        """
        self.embeddings = embeddings
        self.uri = uri
        self.username = username
        self.password = password
        self.database = database
        self.node_label = node_label
        self.text_node_properties = text_node_properties
        self.embedding_node_property = embedding_node_property

    def get_vector_store(self) -> VectorStore:
        """
        Retrieves a LangChain vector store based on the Neo4j graph.

        Returns:
            VectorStore: A LangChain Neo4jVectorStore instance.
        """
        return Neo4jVector.from_existing_graph(
            embedding=self.embeddings,
            url=self.uri,
            username=self.username,
            password=self.password,
            node_label=self.node_label,
            text_node_properties=self.text_node_properties,
            embedding_node_property=self.embedding_node_property,
        )
