from typing import Any

from graphdatascience import GraphDataScience
from langchain_community.graphs.graph_store import GraphStore
from langchain_core.language_models import BaseChatModel
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate


# https://hackernoon.com/building-knowledge-graphs-for-rag-exploring-graphrag-with-neo4j-and-langchain
class GraphManager:
    """
    Manages entity resolution through similarity mutation and merging of nodes.
    """

    def __init__(self, graph_store: GraphStore, uri: str, username: str, password: str):
        self.gds = GraphDataScience(endpoint=uri, auth=(username, password))
        self.graph_store = graph_store

    def mutate_graph_with_similarity(self, embedding_property: str = "embedding", similarity_threshold: float = 0.8):
        """
        Mutates the graph by creating similarity relationships between nodes based on embeddings.

        :param embedding_property: The property name where node embeddings are stored.
        :param similarity_threshold: The threshold above which similarity relationships are created.
        """
        self.gds.graph.drop(graph="entities")
        graph_project, result = self.gds.graph.project(
            "entities",  # Graph name
            "__Entity__",  # Node projection
            "*",  # Relationship projection
            nodeProperties=[embedding_property],  # Configuration parameters
        )

        self.gds.knn.mutate(
            G=graph_project,
            nodeProperties=[embedding_property],
            mutateRelationshipType="SIMILAR",
            mutateProperty="score",
            similarityCutoff=similarity_threshold,
        )

        self.gds.wcc.write(G=graph_project, writeProperty="wcc", relationshipTypes=["SIMILAR"])

    def mutate_graph_with_communities(self):
        """
        Uses the Louvain algorithm to detect communities within the graph.
        """
        self.gds.graph.drop(graph="communities")
        graph_project, result = self.gds.graph.project(
            "communities",  # Graph name
            "__Entity__",  # Node projection
            {
                "_ALL_": {
                    "type": "*",
                    "orientation": "UNDIRECTED",
                    "properties": {"weight": {"property": "*", "aggregation": "COUNT"}},
                }
            },
        )

        self.gds.leiden.write(
            G=graph_project,
            writeProperty="communities",
            includeIntermediateCommunities=True,
            relationshipWeightProperty="weight",
        )

    def mutate_graph_with_community_summaries(self, llm: BaseChatModel):
        self.graph_store.query(
            """
            MATCH (e:`__Entity__`)
            UNWIND range(0, size(e.communities) - 1 , 1) AS index
            CALL {
              WITH e, index
              WITH e, index
              WHERE index = 0
              MERGE (c:`__Community__` {id: toString(index) + '-' + toString(e.communities[index])})
              ON CREATE SET c.level = index
              MERGE (e)-[:IN_COMMUNITY]->(c)
              RETURN count(*) AS count_0
            }
            CALL {
              WITH e, index
              WITH e, index
              WHERE index > 0
              MERGE (current:`__Community__` {id: toString(index) + '-' + toString(e.communities[index])})
              ON CREATE SET current.level = index
              MERGE (previous:`__Community__` {id: toString(index - 1) + '-' + toString(e.communities[index - 1])})
              ON CREATE SET previous.level = index - 1
              MERGE (previous)-[:IN_COMMUNITY]->(current)
              RETURN count(*) AS count_1
            }
            RETURN count(*)
            """
        )

        community_info = self.graph_store.query(
            """
            MATCH (c:`__Community__`)<-[:IN_COMMUNITY*]-(e:__Entity__)
            WHERE c.level IN [0,1,4]
            WITH c, collect(e ) AS nodes
            WHERE size(nodes) > 1
            CALL apoc.path.subgraphAll(nodes[0], {
             whitelistNodes:nodes
            })
            YIELD relationships
            RETURN c.id AS communityId,
                   [n in nodes | {id: n.id, description: n.description, type: [el in labels(n) WHERE el <> '__Entity__'][0]}] AS nodes,
                   [r in relationships | {start: startNode(r).id, type: type(r), end: endNode(r).id, description: r.description}] AS rels
            """
        )

        community_template = """Based on the provided nodes and relationships that belong to the same graph community,
        generate a natural language summary of the provided information:
        {community_info}

        Summary:"""

        community_prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "Given an input triples, generate the information summary. No pre-amble.",
                ),
                ("human", community_template),
            ]
        )

        community_chain = community_prompt | llm | StrOutputParser()

        def prepare_string(data):
            nodes_str = "Nodes are:\n"
            for node in data["nodes"]:
                node_id = node["id"]
                node_type = node["type"]
                if "description" in node and node["description"]:
                    node_description = f", description: {node['description']}"
                else:
                    node_description = ""
                nodes_str += f"id: {node_id}, type: {node_type}{node_description}\n"

            rels_str = "Relationships are:\n"
            for rel in data["rels"]:
                start = rel["start"]
                end = rel["end"]
                rel_type = rel["type"]
                if "description" in rel and rel["description"]:
                    description = f", description: {rel['description']}"
                else:
                    description = ""
                rels_str += f"({start})-[:{rel_type}]->({end}){description}\n"

            return nodes_str + "\n" + rels_str

        summaries: list[dict[str, Any]] = []
        for community in community_info:
            stringify_info = prepare_string(community)
            summary = community_chain.invoke({"community_info": stringify_info})
            summaries.append({"community": community["communityId"], "summary": summary})

        self.graph_store.query(
            """
            UNWIND $data AS row
            MERGE (c:__Community__ {id:row.community})
            SET c.summary = row.summary
        """,
            params={"data": summaries},
        )
