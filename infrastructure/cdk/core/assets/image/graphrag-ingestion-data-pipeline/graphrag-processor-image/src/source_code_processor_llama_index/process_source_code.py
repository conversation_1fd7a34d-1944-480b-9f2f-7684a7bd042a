import asyncio
import logging
import re

import os

from typing import Any

from llama_index.core import PropertyGraphIndex, StorageContext, load_index_from_storage
from llama_index.llms.openai import OpenAI

from source_code_processor_llama_index.document_loader import <PERSON><PERSON><PERSON>ume<PERSON><PERSON><PERSON><PERSON>, ChunkedDocumentLoader
from source_code_processor_llama_index.neo4j_constants import NEO4J_URI, NEO4J_PASSWORD, NEO4J_USERNAME, NEO4J_DATABASE
from source_code_processor_llama_index.openai_constants import OPENAI_API_KEY, DEFAULT_OPENAI_CHAT_MODEL
from source_code_processor_llama_index.graph_rag_store import GraphRAGStore
from source_code_processor_llama_index.graph_rag_extractor import GraphRAGExtractor
from source_code_processor_llama_index.graph_rag_query_engine import GraphRAGQueryEngine

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
)

KG_TRIPLET_EXTRACT_TMPL = """
-Goal-
Given a text document, identify all entities and their entity types from the text and all relationships among the identified entities.
Given the text, extract up to {max_knowledge_triplets} entity-relation triplets.

-Steps-
1. Identify all entities. For each identified entity, extract the following information:
- entity_name: Name of the entity, capitalized
- entity_type: Type of the entity
- entity_description: Comprehensive description of the entity's attributes and activities
Format each entity as ("entity"$$$$"<entity_name>"$$$$"<entity_type>"$$$$"<entity_description>")

2. From the entities identified in step 1, identify all pairs of (source_entity, target_entity) that are *clearly related* to each other.
For each pair of related entities, extract the following information:
- source_entity: name of the source entity, as identified in step 1
- target_entity: name of the target entity, as identified in step 1
- relation: relationship between source_entity and target_entity
- relationship_description: explanation as to why you think the source entity and the target entity are related to each other

Format each relationship as ("relationship"$$$$"<source_entity>"$$$$"<target_entity>"$$$$"<relation>"$$$$"<relationship_description>")

3. When finished, output.

-Real Data-
######################
text: {text}
######################
output:"""

entity_pattern = r'\("entity"\$\$\$\$"(.+?)"\$\$\$\$"(.+?)"\$\$\$\$"(.+?)"\)'
relationship_pattern = r'\("relationship"\$\$\$\$"(.+?)"\$\$\$\$"(.+?)"\$\$\$\$"(.+?)"\$\$\$\$"(.+?)"\)'


def parse_fn(response_str: str) -> Any:
    entities = re.findall(entity_pattern, response_str)
    relationships = re.findall(relationship_pattern, response_str)
    return entities, relationships


async def main():
    logging.info("starting processing")

    working_dir = "./working_dir"
    if not os.path.exists(working_dir):
        os.mkdir(working_dir)

    repo_dir = "/Users/<USER>/chapter2/unblocked/projects/libs/lib-slack-ingestion"

    os.environ["NEO4J_URI"] = NEO4J_URI
    os.environ["NEO4J_USERNAME"] = NEO4J_USERNAME
    os.environ["NEO4J_PASSWORD"] = NEO4J_PASSWORD
    os.environ["NEO4J_DATABASE"] = NEO4J_DATABASE
    os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY

    nodes = ChunkedDocumentLoader(document_loader=DirectoryDocumentLoader(directory_path=repo_dir)).load_and_chunk()

    graph_store = GraphRAGStore(
        username=NEO4J_USERNAME,
        password=NEO4J_PASSWORD,
        url=NEO4J_URI,
        database=NEO4J_DATABASE,
    )

    llm = OpenAI(model=DEFAULT_OPENAI_CHAT_MODEL)

    kg_extractor = GraphRAGExtractor(
        llm=llm,
        extract_prompt=KG_TRIPLET_EXTRACT_TMPL,
        max_paths_per_chunk=2,
        parse_fn=parse_fn,
    )

    # index = PropertyGraphIndex(
    #     nodes=nodes,
    #     kg_extractors=[kg_extractor],
    #     property_graph_store=graph_store,
    #     show_progress=True,
    # )
    # index.storage_context.persist(persist_dir=working_dir)

    # index = PropertyGraphIndex(
    #     nodes=nodes,
    #     kg_extractors=[kg_extractor],
    #     show_progress=True,
    # )
    # index.storage_context.persist(persist_dir=working_dir)

    index = load_index_from_storage(
        StorageContext.from_defaults(persist_dir=working_dir),
    )

    # graph_store.build_communities()
    # query_engine = GraphRAGQueryEngine(
    #     graph_store=graph_store,
    #     llm=llm,
    #     index=index,
    #     similarity_top_k=10,
    # )

    response = index.as_query_engine(
        llm=llm,
        similarity_top_k=20,
    ).query(
        str_or_query_bundle="Can you generate a mermaid interaction diagram for how Slack Ingestion works including major and minor entities?",
    )

    # response = query_engine.query(
    #     str_or_query_bundle="Can you generate a mermaid interaction diagram for how Slack Ingestion works including major and minor entities?",
    # )

    print(response)


if __name__ == "__main__":
    asyncio.run(main())
