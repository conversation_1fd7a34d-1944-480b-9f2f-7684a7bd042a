import logging
import pickle


def save_object_to_pickle(obj, file_path):
    """
    Saves a Python object to a pickle file.

    Args:
        obj: The Python object to save.
        file_path (str): The path to the pickle file.
    """
    with open(file_path, "wb") as f:
        pickle.dump(obj, f)
    logging.info(f"Object saved to {file_path}")


def load_object_from_pickle(file_path):
    """
    Loads a Python object from a pickle file.

    Args:
        file_path (str): The path to the pickle file.

    Returns:
        The loaded Python object.
    """
    with open(file_path, "rb") as f:
        obj = pickle.load(f)
    logging.info(f"Object loaded from {file_path}")
    return obj
