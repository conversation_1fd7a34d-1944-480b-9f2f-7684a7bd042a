# Define custom instructions for source code-related entity extraction
GRAPHRAG_DOCUMENT_INSTRUCTIONS = """
- Extract entities from source code such as types, classes, interfaces, functions, packages, and modules.
- Represent each entity as a node with the following required properties:
  - name: The name of the entity.
  - definition: The specific code or signature defining the entity.
  - description: Comprehensive summary of the entity's purpose, functionality and themes.
  - docstring: Any associated documentation string explaining the entity.
  - keywords: High-level key words that summarize the main concepts, themes, or topics of the entity.
- Identify relationships such as:
  - 'inherits_from' for inheritance between types or classes.
  - 'calls' for function invocations.
  - 'defines' for relationships between modules and their contained entities.
  - 'imports' for module or package imports.
  - 'overrides' for functions that override others.
- Each relationship must have the following required properties:
  - description: Contextual information about the relationship.
  - definition: Specific details or conditions defining the relationship.
- Ensure all required properties are included for each entity and relationship.
"""

GRAPHRAG_ALLOWED_NODES = ["class", "interface", "function", "package", "module"]
GRAPHRAG_ALLOWED_RELATIONSHIPS = ["inherits_from", "calls", "defines", "imports", "overrides"]
GRAPHRAG_NODE_PROPERTIES = ["name", "definition", "docstring", "description", "keywords"]
GRAPHRAG_RELATIONSHIP_PROPERTIES = ["description", "definition"]
