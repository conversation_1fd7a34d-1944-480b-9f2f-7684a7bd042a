from abc import ABC, abstractmethod
from langchain.agents import AgentExecutor


class QueryProcessorInterface(ABC):
    """
    Interface for processing queries using a LangChain AgentExecutor.
    """

    @abstractmethod
    def process_query(self, query: str) -> str:
        """
        Processes a query using the AgentExecutor.

        Args:
            query (str): The query to process.

        Returns:
            str: The result of the processed query.
        """
        pass


class Neo4jQueryProcessor(QueryProcessorInterface):
    """
    Concrete implementation of QueryProcessorInterface using a LangChain AgentExecutor.
    """

    def __init__(self, agent_executor: AgentExecutor):
        """
        Initializes the Neo4jQueryProcessor with an AgentExecutor.

        Args:
            agent_executor (AgentExecutor): A LangChain AgentExecutor for processing queries.
        """
        self.agent_executor = agent_executor

    def process_query(self, query: str) -> str:
        """
        Processes a query using the provided AgentExecutor.

        Args:
            query (str): The query to process.

        Returns:
            str: The result of the processed query.
        """
        try:
            result = self.agent_executor.run(query)
            return result
        except Exception as e:
            raise RuntimeError(f"Error processing query: {e}")
