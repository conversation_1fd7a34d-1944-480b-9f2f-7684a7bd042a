from abc import ABC, abstractmethod
from typing import List
from langchain.schema import Document
from langchain_community.document_loaders import (
    DirectoryLoader,
    TextLoader,
    PyPDFLoader,
    UnstructuredWordDocumentLoader,
)
from langchain_text_splitters import TokenTextSplitter


class DocumentLoaderInterface(ABC):
    @abstractmethod
    def load_documents(self) -> List[Document]:
        """
        Abstract method to load a set of documents.

        Returns:
            List[Document]: A list of LangChain `Document` objects.
        """
        pass


class DirectoryDocumentLoader(DocumentLoaderInterface):
    def __init__(self, directory_path: str):
        """
        Initializes the document loader with a directory path.

        Args:
            directory_path (str): The path to the directory containing documents.
        """
        self.directory_path = directory_path

    def load_documents(self) -> List[Document]:
        """
        Loads documents from the directory using LangChain's `DirectoryLoader`.

        Returns:
            List[Document]: A list of Lang<PERSON>hain `Document` objects.
        """
        loader = DirectoryLoader(
            self.directory_path,
            glob="**/*",  # Recursively load all files in the directory
            loader_cls=lambda path: self.get_loader(path),
            silent_errors=True,
        )
        return loader.load()

    @staticmethod
    def get_loader(file_path: str):
        """
        Returns the appropriate loader for a given file type.

        Args:
            file_path (str): Path to the file.

        Returns:
            DocumentLoader: An instance of a file-specific loader.
        """
        if file_path.endswith(".txt"):
            return TextLoader(file_path)
        elif file_path.endswith(".pdf"):
            return PyPDFLoader(file_path)
        elif file_path.endswith(".docx"):
            return UnstructuredWordDocumentLoader(file_path)
        elif file_path.endswith(".kt"):
            return TextLoader(file_path)
        else:
            raise Exception("Invalid file type")


class ChunkedDocumentLoader:
    def __init__(self, document_loader: DocumentLoaderInterface, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        A wrapper around any document loader to add chunking functionality.

        Args:
            document_loader: An instance of a document loader.
            chunk_size (int): Maximum number of tokens per chunk.
            chunk_overlap (int): Number of tokens to overlap between chunks.
        """
        self.document_loader = document_loader
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

    def load_and_chunk(self) -> List[Document]:
        """
        Loads documents using the wrapped loader and chunks them.

        Returns:
            List[Document]: A list of LangChain `Document` objects, chunked by token counts.
        """
        # Load documents using the provided loader
        documents = self.document_loader.load_documents()

        # Apply chunking
        return self.chunk_documents(documents)

    def chunk_documents(self, documents: List[Document]) -> List[Document]:
        """
        Splits the loaded documents into smaller chunks based on token counts.

        Args:
            documents (List[Document]): A list of LangChain `Document` objects.

        Returns:
            List[Document]: A list of chunked `Document` objects.
        """
        text_splitter = TokenTextSplitter(chunk_size=self.chunk_size, chunk_overlap=self.chunk_overlap)
        return text_splitter.split_documents(documents)
