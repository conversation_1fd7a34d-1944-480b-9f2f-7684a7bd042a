import os

from aws_utils.aws_secrets_utils import get_aws_secret

EMBEDDING_CONTENT_ENCRYPTION_KEY = get_aws_secret(
    secret_name="EMBEDDING_CONTENT_ENCRYPTION_KEY", default=os.environ.get("EMBEDDING_CONTENT_ENCRYPTION_KEY")
)

PINECONE_SERVERLESS_API_KEY = get_aws_secret(secret_name="pinecone-serverless-api-key")


def get_pinecone_api_key() -> str:
    secret = os.environ.get("PINECONE_SERVERLESS_API_KEY")
    if secret:
        return secret
    return PINECONE_SERVERLESS_API_KEY
