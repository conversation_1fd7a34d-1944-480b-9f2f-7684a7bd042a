FROM --platform="linux/amd64" python:3.11-slim-bookworm

# Necessary for bertopic
RUN apt-get update \
  && apt-get clean  \
  && apt-get autoclean \
  && apt-get autoremove

# Install Git
RUN apt-get install -y git

# Tree-sitter workarounds
# - Install build tools (gcc) to build from source if necessary.
# - Install tree-sitter-languages using pip
RUN apt-get install -y build-essential
RUN pip3 install tree-sitter-languages==1.7.0

# Use Poetry to pin dependencies for sanity,
# and keep the development environment consistent with the production environment.
RUN pip3 install poetry==2.0.1

# Set up JFrog artifactory configuration such that we can pull from our private repositories
ARG JFROG_PASSWORD
RUN poetry config  http-basic.jfrog-server "<EMAIL>" "$JFROG_PASSWORD"

ARG ENVIRONMENT
ENV ENVIRONMENT=$ENVIRONMENT
RUN git config --global http.proxy  "http://proxy.$ENVIRONMENT.getunblocked.com:80" \
    && git config --global https.proxy "http://proxy.$ENVIRONMENT.getunblocked.com:80"

WORKDIR /code

# Install dependencies
COPY . /code

# The install of torch is required for using CUDA (problem with poetry installation)
RUN POETRY_VIRTUALENVS_CREATE=false poetry install --no-cache --no-interaction --without dev
RUN poetry config virtualenvs.create false

# Add a Python script and configure Docker to run it
ENTRYPOINT ["poetry", "run", "python", "/code/src/source_code_processor/process_source_code.py"]
