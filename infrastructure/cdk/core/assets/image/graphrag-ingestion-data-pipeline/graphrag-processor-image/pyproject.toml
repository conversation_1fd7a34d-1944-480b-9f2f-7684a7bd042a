[tool.poetry]
name = "source-code-ingest"
version = "1.0.0"
description = ""
authors = []
readme = "README.md"
packages = [
    { include = "source_code_processor", from = "src" },
]

[tool.poetry.dependencies]
brotli = "^1"
langchain = '^0'
langchain-experimental = '^0'
langchain-neo4j = "^0"
langchain-openai = '^0'
lightrag-hku = "^1"
llama-index = "^0"
openai = "^1"
neo4j = "^5"
python = ">=3.10.12,<3.13"
unblocked-aws-utils = "^0"
graphdatascience = "^1"
aioboto3 = "^13.4.0"
ollama = "^0.4.7"
nano-vectordb = "^0"
#neo4j-graphrag = "^1"
numpy = ">=1.26.4,<2.0.0"
llama-index-graph-stores-neo4j = "^0.4.6"
graspologic = "^3.4.1"
future = "^1.0.0"

[tool.poetry.group.dev.dependencies]
black = "^24"
pytest = "^8"

[tool.black]
extend-exclude = '__pycache__'
include = '\.pyi?$'
line-length = 120

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
