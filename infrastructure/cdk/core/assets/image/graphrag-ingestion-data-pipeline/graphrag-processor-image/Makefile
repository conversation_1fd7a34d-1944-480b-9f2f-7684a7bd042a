check: format test

# To run locally replace these variables from your own local stack repo and org as necessary.
# You can get these values from your local Admin Web.
repoUrl = 'https://github.com/NextChapterSoftware/raycast.git'
repoFullName = 'NextChapterSoftware/raycast'
repoId = '36ad35ad-fb6c-46e5-9018-d8cf7c8b6c7d'
orgId = 'b0549615-71a0-4d50-a32e-7a863d862fd0'

ENV_VARS=\
	export AWS_PROFILE=dev; \
	export ENVIRONMENT=dev; \
	export PROCESS_PINECONE_NAMESPACE=$(orgId); \
	export PROCESS_REPO_HTTP_CLONE_URL=$(repoUrl); \
	export PROCESS_REPO_FULL_NAME=$(repoFullName); \
	export PROCESS_REPO_ID=$(repoId); \
	export PROCESS_ORG_ID=$(orgId); \
	export PROCESS_S3_OUTPUT='s3://code-ingestion-data-pipeline-sandbox-dev-us-west-2/009c8f4a-2a67-46e8-88f4-35b78157f38f'; \

run:
	$(ENV_VARS) poetry run python ./src/source_code_processor/process_source_code.py

docker-build:
	$(eval JFROG_PASSWORD := $(shell security find-generic-password -a "<EMAIL>" -l "poetry-repository-jfrog-server" -w 2>/dev/null))
	@JFROG_PASSWORD=$(JFROG_PASSWORD) docker-compose build

docker-run:
	docker-compose up

format:
	poetry run black src

test:
	poetry run pytest ./src/source_code_processor/test/unit


.PHONY: check format run docker-build docker-run test
