[tool.poetry]
name = "source-code-ingest"
version = "1.0.0"
description = ""
authors = []
readme = "README.md"
packages = [
    { include = "graphrag_neo4j_inference", from = "src" },
]

[tool.poetry.dependencies]
asyncio = "^3"
brotli = "^1"
fastapi = "^0"
langchain = '^0'
neo4j = "^5"
neo4j-graphrag = "^1"
openai = "^1"
python = ">=3.10.12,<3.13"
unblocked-aws-utils = "^0"
uvicorn = "^0"

[tool.poetry.group.dev.dependencies]
black = "^24"
pytest = "^8"

[tool.black]
extend-exclude = '__pycache__'
include = '\.pyi?$'
line-length = 120

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
