check: format test

ENV_VARS=\
	export AWS_PROFILE=dev; \
	export ENVIRONMENT=dev;

run:
	$(ENV_VARS) poetry run python ./src/graphrag_neo4j_inference/process_source_code.py

docker-build:
	$(eval JFROG_PASSWORD := $(shell security find-generic-password -a "<EMAIL>" -l "poetry-repository-jfrog-server" -w 2>/dev/null))
	@JFROG_PASSWORD=$(JFROG_PASSWORD) docker-compose build

docker-run:

	docker-compose up

format:
	poetry run black src

test:
	poetry run pytest ./src/graphrag_neo4j_inference/test/unit


.PHONY: check format run docker-build docker-run test
