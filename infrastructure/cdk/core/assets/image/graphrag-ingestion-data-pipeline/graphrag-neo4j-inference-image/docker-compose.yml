version: '3.9'
services:
    graphrag-neo4j-inference:
        platform: linux/amd64
        build:
            context: ./
            dockerfile: Dockerfile
            args:
                JFROG_PASSWORD: '${JFROG_PASSWORD}'
                ENVIRONMENT: 'dev'
        ports:
            - '8091:8080'
        volumes:
            - ~/.aws:/root/.aws:ro
        environment:
            - AWS_PROFILE=dev
            - NEO4J_PASSWORD=${NEO4J_PASSWORD}
