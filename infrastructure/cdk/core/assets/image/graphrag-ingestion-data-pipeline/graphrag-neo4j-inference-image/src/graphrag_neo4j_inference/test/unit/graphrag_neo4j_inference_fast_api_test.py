import unittest
from fastapi.testclient import TestClient

from graphrag_neo4j_inference.neo4j_constants import DEFAULT_NEO4J_VECTOR_INDEX, DEFAULT_NEO4J_FULLTEXT_INDEX
from ...graphrag_neo4j_fast_api import app


class TestGraphRagFastAPI(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """
        Set up the test client for all tests.
        """
        cls.client = TestClient(app)

    def test_search_endpoint(self):
        """
        Test the /search endpoint with a valid GraphRagRequest.
        """
        request_payload = {
            "query": "Can you generate a mermaid diagram for how Slack Ingestion works including major and minor entities paying attention to minute interactions?",
            "parameters": {
                "max_tokens": 2000,
                "top_k": 10,
                "vector_index_name": DEFAULT_NEO4J_VECTOR_INDEX,
                "fulltext_index_name": DEFAULT_NEO4J_FULLTEXT_INDEX,
            },
        }

        response = self.client.post("/invocations", json=request_payload)

        # Validate response
        self.assertEqual(response.status_code, 200, f"Unexpected status code: {response.status_code}")
        response_data = response.json()

        # Check the response structure
        self.assertIn("response", response_data, "Missing 'response' in the response body")
        self.assertIsInstance(response_data["response"], str, "The 'response' field should be a string")

    def test_search_endpoint_with_optional_parameters(self):
        """
        Test the /search endpoint with missing optional parameters.
        """
        request_payload = {
            "query": "Can you generate a mermaid interaction diagram for how Slack Ingestion works including major and minor entities paying attention to minute interactions?",
        }

        response = self.client.post("/invocations", json=request_payload)

        # Validate response
        self.assertEqual(response.status_code, 200, f"Unexpected status code: {response.status_code}")
        response_data = response.json()

        # Check the response structure
        self.assertIn("response", response_data, "Missing 'response' in the response body")
        self.assertIsInstance(response_data["response"], str, "The 'response' field should be a string")


if __name__ == "__main__":
    unittest.main()
