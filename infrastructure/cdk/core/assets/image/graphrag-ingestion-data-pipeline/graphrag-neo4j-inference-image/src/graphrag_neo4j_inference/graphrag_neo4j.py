import logging

from typing import Optional

from neo4j import GraphDatabase
from neo4j_graphrag.generation import GraphRAG
from neo4j_graphrag.embeddings import OpenAIEmbeddings
from neo4j_graphrag.generation import RagTemplate
from neo4j_graphrag.llm import OpenAILLM
from neo4j_graphrag.retrievers import HybridCypherRetriever

from graphrag_neo4j_inference.neo4j_constants import (
    NEO4J_URI,
    NEO4J_PASSWORD,
    NEO4J_USERNAME,
    NEO4J_DATABASE,
    DEFAULT_NEO4J_TOP_K,
)
from graphrag_neo4j_inference.openai_constants import (
    OPENAI_API_KEY,
    DEFAULT_OPENAI_CHAT_MODEL,
    DEFAULT_OPENAI_MAX_TOKENS,
    DEFAULT_OPENAI_TEMPERATURE,
)

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
)

CYPHER_RETRIEVER_QUERY = """
    // 1) Go out 2-3 hops in the entity graph and get relationships
    WITH node AS chunk
    MATCH (chunk)<-[:FROM_CHUNK]-()-[relList:!FROM_CHUNK]-{1,2}()
    UNWIND relList AS rel

    // 2) Collect relationships and text chunks
    WITH collect(DISTINCT chunk) AS chunks,
      collect(DISTINCT rel) AS rels

    // 3) Format and return context
    RETURN '=== text ===\n' +
           apoc.text.join([c in chunks | c.text], '\n---\n') + '\n\n=== kg_rels ===\n' +
           apoc.text.join([r in rels | startNode(r).name + ' - ' + type(r) + ' -> ' + endNode(r).name ], '\n---\n') AS info
"""

RAG_TEMPLATE = RagTemplate(
    template="""Answer the Question using the following Context. Only respond with information mentioned in the Context. Do not inject any speculative information or entities not mentioned.
    Output the results in JSON.

    # Question:
    {query_text}

    # Context:
    {context}

    # Answer:
    """,
    expected_inputs=["query_text", "context"],
)


class GraphRagNeo4j:
    def __init__(
        self,
        vector_index_name: str,
        fulltext_index_name: str,
        llm_model_name: str = DEFAULT_OPENAI_CHAT_MODEL,
        max_tokens: int = DEFAULT_OPENAI_MAX_TOKENS,
        temperature: float = DEFAULT_OPENAI_TEMPERATURE,
        top_k: int = DEFAULT_NEO4J_TOP_K,
    ):
        """
        Initialize the GraphRagInference class.

        Args:
            vector_index_name (str): The name of the vector index in Neo4j.
            fulltext_index_name (str): The name of the fulltext index in Neo4j.
            llm_model_name (str): Name of the OpenAI model to use. Defaults to "gpt-4o".
            max_tokens (int): Maximum tokens for LLM responses. Defaults to 2000.
            temperature (float): Temperature for LLM responses. Defaults to 0.0.
            top_k (int): Number of top results to retrieve. Defaults to 10.
        """
        self.driver = GraphDatabase.driver(
            uri=NEO4J_URI, auth=(NEO4J_USERNAME, NEO4J_PASSWORD), database=NEO4J_DATABASE
        )

        # Initialize components
        self.embedder = OpenAIEmbeddings(model="text-embedding-3-large", api_key=OPENAI_API_KEY)
        self.llm = OpenAILLM(
            api_key=OPENAI_API_KEY,
            model_name=llm_model_name,
            model_params={"max_tokens": max_tokens, "temperature": temperature},
        )
        self.retriever = HybridCypherRetriever(
            driver=self.driver,
            vector_index_name=vector_index_name,
            fulltext_index_name=fulltext_index_name,
            retrieval_query=CYPHER_RETRIEVER_QUERY,
            embedder=self.embedder,
        )
        self.hc_rag = GraphRAG(llm=self.llm, retriever=self.retriever, prompt_template=RAG_TEMPLATE)
        self.top_k = top_k

    def search(self, query_text: str) -> str:
        """
        Perform a RAG-based search.

        Args:
            query_text (str): The question/query to ask.

        Returns:
            str: The RAG response.
        """
        result = self.hc_rag.search(query_text=query_text, retriever_config={"top_k": self.top_k}, return_context=True)
        return result.answer

    def close(self):
        """Close the Neo4j driver."""
        self.driver.close()

    def __enter__(self):
        """Enable context manager protocol."""
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        """Ensure the driver is closed when exiting the context."""
        self.close()
