import os

from aws_utils.aws_secrets_utils import get_aws_secret

NEO4J_URI = os.environ.get("NEO4J_URI", default="neo4j+s://20ccec38.databases.neo4j.io")
NEO4J_USERNAME = os.environ.get("NEO4J_USERNAME", default="neo4j")
NEO4J_PASSWORD = get_aws_secret(secret_name="neo4j-unblocked-token", default=os.environ.get("NEO4J_PASSWORD"))
NEO4J_DATABASE = os.environ.get("NEO4J_DATABASE", default="neo4j")

DEFAULT_NEO4J_VECTOR_INDEX = "text_embeddings"
DEFAULT_NEO4J_FULLTEXT_INDEX = "fulltext_index"
DEFAULT_NEO4J_TOP_K = 10
