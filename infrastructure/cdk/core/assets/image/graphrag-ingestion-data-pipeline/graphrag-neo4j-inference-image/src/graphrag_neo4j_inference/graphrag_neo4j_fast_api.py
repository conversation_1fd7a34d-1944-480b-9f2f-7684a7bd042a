import os
import logging
from pathlib import Path

import uvicorn

from fastapi import FastAPI, HTTPException
from graphrag_neo4j_inference.graphrag_api_types import GraphRagRequest, GraphRagResponse, GraphRagParameters
from graphrag_neo4j_inference.graphrag_neo4j import GraphRagNeo4j
from graphrag_neo4j_inference.neo4j_constants import (
    DEFAULT_NEO4J_VECTOR_INDEX,
    DEFAULT_NEO4J_FULLTEXT_INDEX,
    DEFAULT_NEO4J_TOP_K,
)
from graphrag_neo4j_inference.openai_constants import DEFAULT_OPENAI_CHAT_MODEL, DEFAULT_OPENAI_MAX_TOKENS

# Initialize FastAPI app
app = FastAPI()


@app.get("/ping")
async def ping():
    return {"message": "ok"}


@app.post("/", response_model=GraphRagResponse)
@app.post("/invocations", response_model=GraphRagResponse)
@app.post("/search", response_model=GraphRagResponse)
async def search(request: GraphRagRequest):
    """
    Endpoint to perform RAG-based search using a provided query and parameters.

    Args:
        request (GraphRagRequest): Query input containing the question and optional parameters.

    Returns:
        GraphRagResponse: Response containing the answer.
    """
    try:
        # Extract parameters from the request
        query_text = request.query
        params = request.parameters or GraphRagParameters()

        # Instantiate the GraphRagInference instance at query time
        with GraphRagNeo4j(
            vector_index_name=params.vector_index_name or DEFAULT_NEO4J_VECTOR_INDEX,
            fulltext_index_name=params.fulltext_index_name or DEFAULT_NEO4J_FULLTEXT_INDEX,
            llm_model_name=DEFAULT_OPENAI_CHAT_MODEL,
            max_tokens=params.max_tokens or DEFAULT_OPENAI_MAX_TOKENS,
            top_k=params.top_k or DEFAULT_NEO4J_TOP_K,
        ) as inference:
            result = inference.search(query_text)
            return GraphRagResponse(response=result)
    except Exception as e:
        logger.error(f"Error during search: {e}", exc_info=True)  # Logs full stack trace
        raise HTTPException(status_code=500, detail=str(e))


def start_server():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    log_config_path = f"{current_dir}/log_conf.yaml"
    # port 8080 required by sagemaker
    # https://docs.aws.amazon.com/sagemaker/latest/dg/your-algorithms-inference-code.html
    uvicorn.run(f"{Path(__file__).stem}:app", host="0.0.0.0", port=8080, reload=False, log_config=log_config_path)
