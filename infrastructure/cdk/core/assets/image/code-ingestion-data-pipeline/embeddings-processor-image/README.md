# Prerequisites

Install [poetry](https://python-poetry.org/).

```shell
curl -sSL https://install.python-poetry.org | python3 - --version 1.6.1

```

# Develop

Run tests and reformat code.

```shell
make
```

Run the application.

```shell
make run
```

Run tests with [pytest](https://docs.pytest.org/).

```shell
make test
```

Reformat code with [black](https://github.com/psf/black).

```shell
make format
```

# Dependencies

Add new dependency with [poetry](https://python-poetry.org/).

```shell
poetry add <package>
```

Update dependencies with [poetry](https://python-poetry.org/).

```shell
poetry update
```
