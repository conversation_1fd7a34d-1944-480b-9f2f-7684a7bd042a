"""
config_loader.py
----------------
Environment→dataclass loader with:
• case-insensitive enums (EmbeddingPlatform, EmbeddingModel, IncrementalMode)
• field-only default system
"""

import os, json, base64
from dataclasses import dataclass, field, fields
from enum import Enum
from typing import List, Optional, Dict, Any

from text_utils.compression import Compression


# ─────────────────────────── Enums ──────────────────────────── #
class IncrementalMode(str, Enum):
    Full = "Full"
    Incremental = "Incremental"

    @classmethod
    def from_string(cls, s: str) -> "IncrementalMode":
        for m in cls:
            if m.name.lower() == s.lower() or m.value.lower() == s.lower():
                return m
        raise ValueError(f"No IncrementalMode matching '{s}'")


class EmbeddingPlatform(str, Enum):
    Pinecone = "Pinecone"
    OpenSearch = "OpenSearch"

    @classmethod
    def from_string(cls, s: str) -> "EmbeddingPlatform":
        for m in cls:
            if m.name.lower() == s.lower() or m.value.lower() == s.lower():
                return m
        raise ValueError(f"No EmbeddingPlatform matching '{s}'")


class EmbeddingModel(str, Enum):
    E5MISTRAL = ("E5Mistral", 4096)

    def __new__(cls, val: str, dim: int):
        obj = str.__new__(cls, val)
        obj._value_, obj._dim = val, dim
        return obj

    @property
    def dimension(self) -> int:
        return self._dim

    @classmethod
    def from_string(cls, s: str) -> "EmbeddingModel":
        for m in cls:
            if m.name.lower() == s.lower() or m.value.lower() == s.lower():
                return m
        raise ValueError(f"No EmbeddingModel matching '{s}'")


# ───────────────── EmbeddingPlatformConfig ──────────────────── #
@dataclass(frozen=True, slots=True)
class EmbeddingPlatformConfig:
    platform: EmbeddingPlatform
    index_name: str
    model: EmbeddingModel


# ───────────────────── ConfigLoader ──────────────────────────── #
@dataclass
class ConfigLoader:
    # Required
    document_key_prefix: str = field(default="", metadata={"env": "PROCESS_DOCUMENT_KEY_PREFIX", "required": True})
    document_source: str = field(default="", metadata={"env": "PROCESS_DOCUMENT_SOURCE", "required": True})
    document_type: str = field(default="", metadata={"env": "PROCESS_DOCUMENT_TYPE", "required": True})
    embedding_namespace: str = field(default="", metadata={"env": "PROCESS_EMBEDDING_NAMESPACE", "required": True})
    embedding_platform_configs: List[EmbeddingPlatformConfig] = field(
        default_factory=list,
        metadata={"env": "PROCESS_EMBEDDING_PLATFORM_CONFIGS", "required": True, "decode_b64": True},
    )
    incremental_mode: IncrementalMode = field(
        default=IncrementalMode.Full,
        metadata={"env": "PROCESS_INCREMENTAL_MODE", "required": True},
    )
    insight_type: str = field(default="", metadata={"env": "PROCESS_INSIGHT_TYPE", "required": True})
    repo_full_name: str = field(default="", metadata={"env": "PROCESS_REPO_FULL_NAME", "required": True})
    repo_http_clone_url: str = field(default="", metadata={"env": "PROCESS_REPO_HTTP_CLONE_URL", "required": True})
    repo_id: str = field(default="", metadata={"env": "PROCESS_REPO_ID", "required": True})
    org_id: str = field(default="", metadata={"env": "PROCESS_ORG_ID", "required": True})

    # Optional with defaults
    environment: str = field(default=os.getenv("ENVIRONMENT", "prod"), metadata={"env": "ENVIRONMENT"})
    llm_endpoint_url: str = field(
        default_factory=lambda: f"https://ml.alb.{os.getenv('ENVIRONMENT', 'prod')}.gcp.getunblocked.com/api/ml/transformers/llama-31-8B",
        metadata={"env": "PROCESS_LLM_ENDPOINT_URL"},
    )
    doc_markdown_llm_endpoint_url: str = field(
        default_factory=lambda: f"https://ml.alb.{os.getenv('ENVIRONMENT', 'prod')}.getunblocked.com/api/ml/models/doc-markdown",
        metadata={"env": "PROCESS_DOC_MARKDOWN_LLM_ENDPOINT_URL"},
    )
    e5_mistral_endpoint_url: str = field(
        default_factory=lambda: f"https://ml.pipeline.alb.{os.getenv('ENVIRONMENT', 'prod')}.getunblocked.com/api/ml/embeddings/e5-mistral",
        metadata={"env": "PROCESS_E5_MISTRAL_ENDPOINT_URL"},
    )
    opensearch_endpoint_url: str = field(default="", metadata={"env": "PROCESS_OPENSEARCH_ENDPOINT_URL"})
    repo_clone_auth: str = field(default="", metadata={"env": "PROCESS_REPO_CLONE_AUTH"})
    repo_http_proxy_url: Optional[str] = field(default=None, metadata={"env": "PROCESS_REPO_HTTP_PROXY_URL"})
    installation_id: str = field(default="", metadata={"env": "PROCESS_INSTALLATION_ID"})
    s3_output: Optional[str] = field(default="", metadata={"env": "PROCESS_S3_OUTPUT"})
    max_threads: int = field(default=1, metadata={"env": "PROCESS_MAX_THREADS"})

    incremental_since_timestamp: Optional[str] = field(
        default=None, metadata={"env": "PROCESS_INCREMENTAL_SINCE_TIMESTAMP"}
    )
    incremental_since_commit_sha: Optional[str] = field(
        default=None, metadata={"env": "PROCESS_INCREMENTAL_SINCE_COMMIT_SHA"}
    )
    activemq_hosts: Optional[List[str]] = field(default=None, metadata={"env": "PROCESS_ACTIVEMQ_HOSTS", "split": True})
    activemq_queue: Optional[str] = field(default=None, metadata={"env": "PROCESS_ACTIVEMQ_QUEUE"})
    activemq_event_type: Optional[str] = field(default=None, metadata={"env": "PROCESS_ACTIVEMQ_EVENT_TYPE"})

    # --------------- post-init loader ---------------- #
    def __post_init__(self):
        payload: Dict[str, str] = json.loads(os.getenv("BATCH_JOB_ENVIRONMENT", "{}"))

        for f in fields(self):
            meta = f.metadata
            env_key = meta.get("env")
            if not env_key:
                continue

            raw = payload.get(env_key) or os.getenv(env_key) or getattr(self, f.name)

            if raw and meta.get("decode_b64"):
                raw = json.loads(Compression.decompress_from_base64(raw))
            if raw and meta.get("split"):
                raw = raw.split(",")

            # special conversions
            if f.name == "embedding_platform_configs" and raw:
                raw = [self._parse_platform_config(item) for item in raw]
            elif f.name == "incremental_mode" and raw:
                raw = IncrementalMode.from_string(raw)
            elif f.type is int and isinstance(raw, str) and raw.isdigit():
                raw = int(raw)

            object.__setattr__(self, f.name, raw)

        self._assert_required()

    # -------- helpers --------
    @staticmethod
    def _parse_platform_config(item: dict) -> EmbeddingPlatformConfig:
        return EmbeddingPlatformConfig(
            platform=EmbeddingPlatform.from_string(item["platform"]),
            index_name=item["indexName"],
            model=EmbeddingModel.from_string(item["model"]),
        )

    def _assert_required(self):
        missing = [f.metadata["env"] for f in fields(self) if f.metadata.get("required") and not getattr(self, f.name)]
        if missing:
            raise ValueError("Missing env vars: " + ", ".join(missing))
