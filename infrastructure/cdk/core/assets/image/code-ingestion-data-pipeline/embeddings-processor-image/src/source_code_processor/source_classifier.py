from classifier_utils.source_classifier import SourceClassifier


def get_source_classifiers(llm_endpoint_url: str) -> dict[str, SourceClassifier]:
    # source_classifier = LLMSourceClassifier(llm_endpoint_url=llm_endpoint_url)
    return {
        # ".json": source_classifier,
        # ".xml": source_classifier,
        # ".yaml": source_classifier,
        # ".jsonl": source_classifier,
        # ".txt": source_classifier,
        # ".sql": source_classifier,
        # "": source_classifier,
    }
