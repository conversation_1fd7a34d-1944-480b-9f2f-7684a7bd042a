import time
import unittest

from source_code_processor.timeout import Timeout


class TestTimeout(unittest.TestCase):
    def test_timeout_exceeded(self):
        # Test that a TimeoutError is raised when a function takes too long
        with self.assertRaises(Timeout.TimeoutError):
            with Timeout(seconds=1):
                time.sleep(2)

    def test_timeout_not_exceeded(self):
        # Test that no TimeoutError is raised when a function takes less time than the timeout
        with Timeout(seconds=2):
            time.sleep(1)
