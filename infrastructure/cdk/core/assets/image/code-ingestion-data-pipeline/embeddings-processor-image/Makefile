include local.env
export

check: format test

run:
	env && poetry run python ./src/source_code_processor/process_source_code.py

docker-build:
	$(eval JFROG_PASSWORD := $(shell security find-generic-password -a "<EMAIL>" -l "poetry-repository-jfrog-server" -w 2>/dev/null))
	@JFROG_PASSWORD=$(JFROG_PASSWORD) docker compose build

docker-run:
	docker compose up

format:
	poetry run black src

test:
	poetry run pytest ./src/source_code_processor/test/unit

.PHONY: check format run docker-build docker-run test
