[tool.poetry]
name = "pull-request-summary"
version = "1.0.0"
description = ""
authors = []
readme = "README.md"
packages = [
    { include = "pull_requests_processor", from = "src" },
]

[tool.poetry.dependencies]
accelerate = "^0"
anthropic = "^0"
boto3 = "^1"
brotli = "^1"
huggingface_hub = "0.25.2"
langchain = '^0'
llama-index-core = '^0'
more-itertools = "^9"
numpy = '<2'
pydantic = ">=2.9,<3.0"
python = ">=3.10.12,<3.14"
sentence-transformers = '2.2.2'
tiktoken = "^0"
torch = "^2"
tqdm = "^4"
transformers = "^4"
unblocked-aws-utils = "^0"
unblocked-file-utils = "^0"
unblocked-git-utils = "^0"
unblocked-llm-content-utils = "^0"
unblocked-llm-prompt-utils = "^0"
unblocked-partition-utils = "^0"
unidiff = "^0"

[tool.poetry.group.dev.dependencies]
black = "^24"
pytest = "^8"

[tool.black]
extend-exclude = '__pycache__'
include = '\.pyi?$'
line-length = 120

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
