{"state":"Merged","mergedAt":"2022-09-06T01:59:26Z","number":2864,"body":"(From the beach \uD83C\uDFD6 )\r\n![57A67F55-CF5D-46B8-813D-7E99CFCD39C8](https://user-images.githubusercontent.com/1798345/188530635-da62b49b-eec8-45df-bdc6-87e55207ceb6.jpeg)\r\n","mergeCommitSha":"1a116954ff241ddf0b64657b37788d42d7eb8f2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2864","title":"Add last message created at to admin thread view","createdAt":"2022-09-06T01:42:10Z"}
{"state":"Merged","mergedAt":"2022-09-07T20:49:49Z","number":2865,"body":"This reverts commit 5d505cb07bdb47f8909273549a20c120cffa94c7.\r\n\r\nAccidentally pushed to main...\r\n\r\n\r\nPR to cache isExpanded state in VSCode sidebar.\r\nThis state does *not* sync up with the explorer tab.\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/188546315-b6031a3e-a66e-43af-b583-63075820412a.mp4\r\n\r\n","mergeCommitSha":"5bc20d63cd5d7dd3c1029c3d5dcc6f7738d8f0c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2865","title":"Cache Sidebar expanded states","createdAt":"2022-09-06T04:21:31Z"}
{"state":"Merged","mergedAt":"2022-09-06T18:32:30Z","number":2866,"body":"Moving auth service code out of API service and into a separate Gradle project","mergeCommitSha":"b9230ec92b058911775ac3dd62d274e25aab5ef3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2866","title":"WIP: move auth code out of api service","createdAt":"2022-09-06T04:43:29Z"}
{"state":"Merged","mergedAt":"2022-09-06T05:40:35Z","number":2867,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1662432593844369?thread_ts=1662402012.678769&cid=C02HEVCCJA3","mergeCommitSha":"edb75d8e124cf81ce1c3f5f912780a2503476ab1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2867","title":"Thread queries return different content for same thread","createdAt":"2022-09-06T04:46:06Z"}
{"state":"Merged","mergedAt":"2022-09-30T22:17:01Z","number":2868,"body":"Reverts NextChapterSoftware/unblocked#2867\r\n\r\n### Background\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1662432593844369?thread_ts=1662402012.678769&cid=C02HEVCCJA3\r\n\r\n### Problem this addresses\r\nClients cannot share caches if the content returned from different thread API endpoints is different for the same key (thread ID).\r\n\r\n### Client change needed\r\nhttps://github.com/NextChapterSoftware/unblocked/blob/21c8c5935b78423abd4c029056f364b5fef7c2a1/shared/stores/ThreadListStore.ts#L35-L66\r\n\r\n### Timeline\r\nWe should merge this, but first, we need to adjust the client sorting and wait for that change to propagate sufficiently to the population. Wait for this change to be adopted: https://github.com/NextChapterSoftware/unblocked/pull/2880.\r\n","mergeCommitSha":"4778f9739d593a0a376c5fc8d3727e3567adf095","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2868","title":"Revert \"Thread queries return different content for same thread\"","createdAt":"2022-09-06T05:40:52Z"}
{"state":"Merged","mergedAt":"2022-09-06T21:27:02Z","number":2869,"mergeCommitSha":"d29dbf2aec5208534b4fd0b5a75a8de560c67734","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2869","title":"Wire up TLC create, update, and delete operations","createdAt":"2022-09-06T17:02:49Z"}
{"state":"Merged","mergedAt":"2022-02-09T17:21:43Z","number":287,"body":"## Summary\r\nThis PR implements the `/install/state` api. Following the flow laid out in https://www.notion.so/nextchaptersoftware/GitHub-App-Install-Flow-ae89ddfcbb6348bfa2e1a7dbcbbbc9d3. \r\n\r\nThe main business logic can be inferred from `Install.kt`\r\n\r\nThis should be enough to plumb the install flow through the current client demo.\r\n\r\n## Implemented\r\n- Org specific install url based on repo url\r\n- User specific install url based on authenticated identity\r\n- Org installation status based on `TeamMember` association\r\n\r\n## Remaining\r\n- Webhook callback for new install\r\n- Polling fallback for new installs <-- let's do this first\r\n- `TeamInstallation` model\r\n- `TeamMember` and `Team` creation after installation\r\n- Possibly a client install completion API\r\n- Utilize the `commitSha` parameter to detect forks \r\n","mergeCommitSha":"5113dcccbeada20747f8c780b7174215fdff4100","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/287","title":"Implement install state API","createdAt":"2022-02-09T00:58:22Z"}
{"state":"Merged","mergedAt":"2022-09-06T20:30:50Z","number":2870,"body":"Part of the work to validate spec changes against previous _non-obsolete_ `Stable` releases.\nhttps://www.notion.so/nextchaptersoftware/API-Version-Compatibility-Process-31d3bf9e50bf4f48814a7adbf0a45e7d","mergeCommitSha":"c88940b23bde8e20664d6606fb68fefcd185f478","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2870","title":"Installer job tags repo with build number","createdAt":"2022-09-06T17:34:45Z"}
{"state":"Merged","mergedAt":"2022-09-06T21:24:34Z","number":2871,"body":"Adding additional logs to narrow down auth errors.\r\n\r\n```\r\nERR [Extension Host] Refresh current person failed. Reverting previous auth changes ResponseError: Response returned an error code\r\n\tat qa.request (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:900942)\r\n\tat process.processTicksAndRejections (node:internal/process/task_queues:96:5)\r\n\tat async qa.getPersonRaw (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:965875)\r\n\tat async qa.getPerson (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:966000)\r\n\tat async jn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1020227)\r\n\tat async An (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1018956)\r\n\tat async Mn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1019759)\r\nconsole.ts:137 [Extension Host] Refresh current person failed. Reverting previous auth changes ResponseError: Response returned an error code\r\n\tat qa.request (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:900942)\r\n\tat process.processTicksAndRejections (node:internal/process/task_queues:96:5)\r\n\tat async qa.getPersonRaw (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:965875)\r\n\tat async qa.getPerson (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:966000)\r\n\tat async jn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1020227)\r\n\tat async An (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1018956)\r\n\tat async Mn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1019759)\r\ny @ console.ts:137\r\nlog.ts:313   ERR [Extension Host] Refresh Auth failed. 0 attempt. ResponseError: Response returned an error code\r\n\tat qa.request (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:900942)\r\n\tat process.processTicksAndRejections (node:internal/process/task_queues:96:5)\r\n\tat async qa.getPersonRaw (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:965875)\r\n\tat async qa.getPerson (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:966000)\r\n\tat async jn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1020227)\r\n\tat async An (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1018956)\r\n\tat async Mn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1019759)\r\nconsole.ts:137 [Extension Host] Refresh Auth failed. 0 attempt. ResponseError: Response returned an error code\r\n\tat qa.request (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:900942)\r\n\tat process.processTicksAndRejections (node:internal/process/task_queues:96:5)\r\n\tat async qa.getPersonRaw (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:965875)\r\n\tat async qa.getPerson (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:966000)\r\n\tat async jn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1020227)\r\n\tat async An (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1018956)\r\n\tat async Mn (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.377/extension.js:2:1019759)\r\ny @ console.ts:137\r\nnotificationsAlerts.ts:42 Fetching teams before auth: ResponseError: Response returned an error code\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nn\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n```","mergeCommitSha":"03172ff86af6691530f4b14a708406e9d6584082","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2871","title":"Additional logging for auth issue","createdAt":"2022-09-06T20:14:15Z"}
{"state":"Merged","mergedAt":"2022-09-06T21:26:15Z","number":2872,"mergeCommitSha":"8a2ae9399aaad70516b558c561361d303c127690","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2872","title":"Remove unnecessary attributes","createdAt":"2022-09-06T21:21:32Z"}
{"state":"Merged","mergedAt":"2022-09-06T21:44:28Z","number":2873,"body":"### problem\r\n\r\n```sh\r\n$ git tag -l\r\nv-hub-undefined\r\nv-vscode-undefined\r\n```\r\n\r\n### changes\r\nUse the `context.runNumber` instead, which comes from here:\r\nhttps://github.com/actions/toolkit/blob/bc4be505973a6a7344bfd71e1b32f77e1755310c/packages/github/src/context.ts#L19","mergeCommitSha":"c570af37fd6dd06ce19b510782da9b2fdc56e39f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2873","title":"Fix CI installer script","createdAt":"2022-09-06T21:40:35Z"}
{"state":"Merged","mergedAt":"2022-09-07T01:05:40Z","number":2874,"mergeCommitSha":"ee572b214debe60464c892f0a495bc4104bbc7f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2874","title":"Min OS version check in installer","createdAt":"2022-09-06T22:53:49Z"}
{"state":"Merged","mergedAt":"2022-09-07T01:14:45Z","number":2875,"body":"Tested against dev/prod.","mergeCommitSha":"d7fc94cbbe5e6abeb7d3384a8217f9fe4e37f2de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2875","title":"Add logging parameters for rds","createdAt":"2022-09-07T01:09:54Z"}
{"state":"Merged","mergedAt":"2022-09-07T02:25:12Z","number":2876,"mergeCommitSha":"757eca7c65ab1cdc9219a875f5778ddeef1c159e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2876","title":"Add logging around database locks","createdAt":"2022-09-07T02:13:00Z"}
{"state":"Merged","mergedAt":"2022-09-07T02:28:47Z","number":2877,"body":"- update\r\n- Update\r\n","mergeCommitSha":"9e3c50fa32420c538936f90bc038d54710e5cc94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2877","title":"TryAgain","createdAt":"2022-09-07T02:28:36Z"}
{"state":"Merged","mergedAt":"2022-09-07T21:08:51Z","number":2878,"body":"This PR modifies the post-install hub popover behaviour as follows:\r\n1. When Hub launches, foregrounds installer if it is running. If not, pops open hub\r\n2. After foregrounding installer, waits for either the installer to terminate or go into the background, then pops open hub\r\n\r\nIn this demo please ignore the fact that the installer hasn't run, this is just to show how the new hub behaviour works assuming the installer has done its thing.\r\n\r\n![CleanShot 2022-09-07 at 08 41 19](https://user-images.githubusercontent.com/858772/188921374-7921e3e5-3569-4df1-ab2f-e3b58fb60094.gif)\r\n\r\n","mergeCommitSha":"6169f6a29f9f14fb5ad0c37ea271000198d13832","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2878","title":"Open the hub regardless of installer state","createdAt":"2022-09-07T02:32:25Z"}
{"state":"Merged","mergedAt":"2022-09-07T04:37:47Z","number":2879,"body":"### Plan\r\n\r\n_This change:_\r\n1. change the `DELETE` cascade to `SET_NULL` for SourceMarkGroup references\r\n2. make references to SourceMarkGroup nullable, needed for above\r\n3. remove code that creates groups or queries groups\r\n4. add migration code to DROP SourceMarkGroup columns\r\n5. add migration code to DROP SourceMarkGroup table\r\n\r\n_Next change:_ (https://github.com/NextChapterSoftware/unblocked/pull/2863)\r\n1. remove SourceMarkGroup columns schema\r\n1. remove SourceMarkGroup table schema\r\n\r\n_Then:_\r\n1. Run migration to drop columns and tables","mergeCommitSha":"06851c42075b38aab22537712bed0e0fee0aff22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2879","title":"Remove SourceMarkGroup usage and prepare for drop","createdAt":"2022-09-07T02:47:13Z"}
{"state":"Merged","mergedAt":"2022-02-09T04:28:09Z","number":288,"body":"When we get all comment threads for a pull request, GitHub limits us to 100 of each per pull request. This PR adds logic to page when a PR exceeds those limits, producing all threads and comments for a PR given a PR number.\r\n\r\nNot the nicest code in the world...but there are tests!","mergeCommitSha":"4f038d0eb4e8a84c63e95b4472299c5fa7e521f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/288","title":"Add logic to page review threads and comments for a pull request","createdAt":"2022-02-09T01:43:06Z"}
{"state":"Merged","mergedAt":"2022-09-07T20:53:26Z","number":2880,"body":"See https://github.com/NextChapterSoftware/unblocked/pull/2868 for context.\r\n\r\nUp until now, all of the thread listings (mine, recommended, PR, archived) have shared a single sorting algorithm.  This worked fine up until recently, as we returned ThreadInfos with differing shapes depending on the API being called . We want to make all of the thread APIs return consistent data, so we need to sort differently for each list:\r\n\r\n* Mine/PR sorts by unread state, then by last-modified, then by ID\r\n* Recommended sorts by rank, then by last-modified, then by ID\r\n* Archived sorts by archival date, then by last-modified, then by ID\r\n\r\nOnce this ships out and all users have upgraded we can change our APIs to return consistent threads.\r\n\r\n","mergeCommitSha":"4fe58e7f05dd72efe06c38413d77c629b7f836fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2880","title":"Unique sort for each thread listing","createdAt":"2022-09-07T02:50:33Z"}
{"state":"Merged","mergedAt":"2022-09-07T04:48:34Z","number":2881,"body":"![CleanShot 2022-09-06 at 21 21 04](https://user-images.githubusercontent.com/858772/188788604-22e24640-0a41-40dc-ace0-d14cb7b18ebc.gif)\r\n","mergeCommitSha":"1b5f3fc6e3fd03d270e384c611b00c94bbe1b206","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2881","title":"Confetti blassssst","createdAt":"2022-09-07T04:25:58Z"}
{"state":"Closed","mergedAt":null,"number":2882,"body":"Update usage of threadsAPI and enable code lens","mergeCommitSha":"72ef7948519a94f45bee4e4aa3c5a032c29e75a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2882","title":"Update code lens to use new threads","createdAt":"2022-09-07T05:30:54Z"}
{"state":"Merged","mergedAt":"2022-09-07T16:21:05Z","number":2883,"body":"Adding anti-affinity to API service to help spread them over the maximum number of Kube nodes possible. I would like to first test this with one service and if all goes well do the same for rest. ","mergeCommitSha":"90a6135179b5808b4b8f3746e3167fccb5009a17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2883","title":"Add anti-affinity to api service","createdAt":"2022-09-07T06:21:43Z"}
{"state":"Merged","mergedAt":"2022-09-12T15:46:53Z","number":2884,"body":"<img width=\"456\" alt=\"CleanShot 2022-09-07 at 15 10 30@2x\" src=\"https://user-images.githubusercontent.com/858772/188992586-f679061a-200e-42ad-9bfe-24c7250ab402.png\">\r\n<img width=\"329\" alt=\"CleanShot 2022-09-07 at 15 10 23@2x\" src=\"https://user-images.githubusercontent.com/858772/188992591-7b92e719-bef7-4906-ace4-d4a3a3d3e195.png\">\r\n<img width=\"309\" alt=\"CleanShot 2022-09-07 at 15 10 06@2x\" src=\"https://user-images.githubusercontent.com/858772/188992604-a566201c-946e-4f1a-929c-95d924e623cf.png\">\r\n<img width=\"320\" alt=\"CleanShot 2022-09-07 at 15 09 56@2x\" src=\"https://user-images.githubusercontent.com/858772/188992609-4c872b59-1553-4ad9-886e-ae14332808f1.png\">\r\n","mergeCommitSha":"b60e2508f88320612ca61aaa3aae0240b444e359","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2884","title":"Sharing video buttons","createdAt":"2022-09-07T16:23:45Z"}
{"state":"Merged","mergedAt":"2022-09-07T17:43:10Z","number":2885,"body":"Same anti-affinity change but for all other services. ","mergeCommitSha":"02d1477126a07592f86c3a537ed1974abe833ffc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2885","title":"adding anti-affinity for the rest of our services","createdAt":"2022-09-07T16:57:47Z"}
{"state":"Closed","mergedAt":null,"number":2886,"body":"Reverts NextChapterSoftware/unblocked#2881","mergeCommitSha":"00b24058da52c3f5760526752bab4151109175c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2886","title":"Revert \"Confetti blassssst\"","createdAt":"2022-09-07T17:17:38Z"}
{"state":"Merged","mergedAt":"2022-09-07T18:31:34Z","number":2887,"body":"Update button and editor styles in VSCode.\r\n\r\n<img width=\"1606\" alt=\"Screen Shot 2022-09-07 at 11 04 47 AM\" src=\"https://user-images.githubusercontent.com/2133518/188948034-7243f995-8453-431b-9d50-c2ce6d8b28a8.png\">\r\n<img width=\"1606\" alt=\"Screen Shot 2022-09-07 at 11 04 57 AM\" src=\"https://user-images.githubusercontent.com/2133518/188948047-b12febfd-13c7-45ce-9181-de23249182ab.png\">\r\n","mergeCommitSha":"45bc765b961585d54c63dac7f9d32a62a4218b3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2887","title":"VSCode button and message editor rounding","createdAt":"2022-09-07T18:06:16Z"}
{"state":"Merged","mergedAt":"2022-09-07T20:34:31Z","number":2888,"body":"This was preventing Kim from Expo from marking a thread as being read.\r\n\r\nWhat happened was that the last message of a thread was deleted in GitHub. When we got the webhook, we correctly updated all `ThreadUnreadModel.lastestMessage` but we didn't check `ThreadUnreadModel.latestReadMessage` to see if that was still pointing to the deleted message.\r\n\r\nThis change should address that. Essentially, if `latestReadMessage.createdAt` > `latestMessage.createdAt`, that must mean `latestReadMessage` was deleted and we can safely assume `latestReadMessage` should be `latestMessage` (since if you've read a message that is more recent than `latestMessage` then you must've read `latestMessage`).\r\n\r\nWith this change, the `updateThreadUnread` operation should start working for the above edge case.","mergeCommitSha":"e68671f7f419745c89691ed3d827809ece078a49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2888","title":"Set ThreadUnreadModel.latestReadMessage to latestMessage if latestReadMessage is deleted and after latestMessage","createdAt":"2022-09-07T18:16:07Z"}
{"state":"Merged","mergedAt":"2022-09-09T19:04:15Z","number":2889,"body":"Revamp sidebar per new designs:\r\n<img width=\"266\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/188939207-cb44d397-e47e-446d-9b02-2c5c1c09271d.png\">\r\n\r\nAdd context menu to each row:\r\n<img width=\"303\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/188939579-ca783166-0b4d-4674-ba42-6002c602f0da.png\">\r\n\r\nAdd unreads dropdown:\r\n<img width=\"425\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/188939634-237bf520-6374-4153-abdb-d46bc9ecc57f.png\">\r\n","mergeCommitSha":"1d110cdc9e71de64aa1bb6bdeffe628b99ecea48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2889","title":"Filter unreads on web extension","createdAt":"2022-09-07T18:53:18Z"}
{"state":"Merged","mergedAt":"2022-02-09T17:27:58Z","number":289,"body":"Part 2 of https://github.com/NextChapterSoftware/unblocked/pull/288. Allows for paging the batch query. ","mergeCommitSha":"c51f4ee5d8e6343409197cd3d8c7236f34002103","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/289","title":"Add logic to page batch requests of pull request","createdAt":"2022-02-09T07:09:50Z"}
{"state":"Merged","mergedAt":"2022-09-07T22:56:06Z","number":2890,"body":"When user was either in non-auth state or non-installed state, explorers sidebars were broken.\r\n\r\nHooking into existing sidebar logic to render additional sidebar states.\r\n\r\n<img width=\"370\" alt=\"CleanShot 2022-09-07 at 11 56 02@2x\" src=\"https://user-images.githubusercontent.com/1553313/188956773-f7423dd0-4a5d-4389-93d6-d68a76c08196.png\">\r\n<img width=\"444\" alt=\"CleanShot 2022-09-07 at 11 47 07@2x\" src=\"https://user-images.githubusercontent.com/1553313/188956875-a60cc8d4-48a3-49e7-a984-86c57cd534dc.png\">\r\n","mergeCommitSha":"fe598b7e8ed4eb244e76f08994234762e0ec4821","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2890","title":"Additional Explorer States","createdAt":"2022-09-07T19:01:05Z"}
{"state":"Merged","mergedAt":"2022-09-07T19:21:18Z","number":2891,"body":"<img width=\"510\" alt=\"CleanShot 2022-09-07 at 12 10 13@2x\" src=\"https://user-images.githubusercontent.com/858772/188958284-b04a5fe3-872e-4da0-ae13-16ed24dfed73.png\">\r\n<img width=\"482\" alt=\"CleanShot 2022-09-07 at 12 10 46@2x\" src=\"https://user-images.githubusercontent.com/858772/188958369-c1efb079-ff12-456c-a173-730886f317c9.png\">\r\n","mergeCommitSha":"a133cfa0379c2f7f4bd636c5c2eee0587bc10e10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2891","title":"All discussions are read","createdAt":"2022-09-07T19:09:57Z"}
{"state":"Merged","mergedAt":"2022-09-08T05:17:58Z","number":2892,"body":"Add schema management based off platformVersion.\r\nFor local/ci development, we use the old methodology.","mergeCommitSha":"f12dde4e5ce82c3df55c508cf1bb3fdf22747661","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2892","title":"First stab at better schema updates","createdAt":"2022-09-07T21:30:53Z"}
{"state":"Merged","mergedAt":"2022-09-07T22:00:35Z","number":2893,"body":"Will let us see which threads were archived due to low relevance in the admin console","mergeCommitSha":"4b0268bb29e937cff3a665e21581d39553f21cee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2893","title":"Add LowRelevance trait to ThreadPage","createdAt":"2022-09-07T21:44:40Z"}
{"state":"Merged","mergedAt":"2022-09-07T22:59:26Z","number":2894,"body":"Add some basic unit tests for ThreadStore.  This tests loading, channel push, and updating the thread listing.  I'll add more tests later as we integrate this with other parts of the client apps.\r\n\r\nBuilding mocks for this was a bit of a challenge.  I took the approach that the ThreadStore takes in a config object, representing the store's dependencies (channel poller, thread API, team member stream).  This lets us inject simple mocks for the tests easily, and the resulting tests work without needing odd timing hacks or anything.  The default config uses the dependencies you'd expect (the actual thread API, ChannelPoller, etc).\r\n","mergeCommitSha":"c8f69d7c40f856abb6e7305d0bd7fe59e0e65c1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2894","title":"Add ThreadStore tests","createdAt":"2022-09-07T21:45:13Z"}
{"state":"Merged","mergedAt":"2022-09-07T22:54:58Z","number":2895,"body":"#2863 is done ✅ ","mergeCommitSha":"d5b7144bc79b4154d46e9f9d46deff128b33e401","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2895","title":"Remove schema migration which has already completed","createdAt":"2022-09-07T21:58:13Z"}
{"state":"Merged","mergedAt":"2022-09-07T22:24:44Z","number":2896,"body":"Some links were incorrectly showing the \"past members\".","mergeCommitSha":"cd4a02fae55c9eb65be67df5bcb18f6a9088a366","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2896","title":"Fix team member page link in adminweb","createdAt":"2022-09-07T22:05:13Z"}
{"state":"Merged","mergedAt":"2022-09-07T23:03:34Z","number":2897,"mergeCommitSha":"1eef0833e3f9e43d60a6cf6dd6fede5f936970fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2897","title":"Add titles to pr filter button group","createdAt":"2022-09-07T22:50:14Z"}
{"state":"Merged","mergedAt":"2022-09-07T23:18:11Z","number":2898,"mergeCommitSha":"3c14f6393e9b17f9faa10f998e224d3134d5dc80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2898","title":"Add LowRelevance trait to ThreadsPage","createdAt":"2022-09-07T22:50:51Z"}
{"state":"Merged","mergedAt":"2022-09-08T00:27:19Z","number":2899,"body":"Going to be using build number for schmea update.","mergeCommitSha":"025733d05d7852462009820ce3793ccae9052cd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2899","title":"Use github build number and pass it to services.","createdAt":"2022-09-07T22:51:12Z"}
{"state":"Closed","mergedAt":null,"number":29,"mergeCommitSha":"afc1553b4c3993a22a7adfd3b89d74e0f7b85309","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/29","title":"Setup unfinished stitches demo","createdAt":"2022-01-11T17:17:27Z"}
{"state":"Merged","mergedAt":"2022-02-09T17:17:17Z","number":290,"body":"CDK code cleanup part 3 (DNS and Certs)\r\n- Added config to use AcmStack for new certs\r\n- New certs include subject alts for user-friendly domains and subdomains\r\n- Refactored DnsStack to create A and CName records from config. This is mainly intended for managing  CName user-friendly names\r\n- Cleaned up build config to move all DNS related configs under a single class. This touched most stacks.\r\n\r\nCerts have been generated and all necessary user-friendly DNS records have been created. My next PR will switch APIService from existing cert to new certs.\r\n\r\n\r\nRelated PR: https://github.com/NextChapterSoftware/unblocked/pull/291","mergeCommitSha":"bbd94a452649bc47653d2ace519ef429fb3bb67e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/290","title":"Move dev and prod certs to  AcmStack and refactor DnsStack","createdAt":"2022-02-09T07:52:32Z"}
{"state":"Merged","mergedAt":"2022-09-07T23:39:25Z","number":2900,"body":"We are currently not creating these for @mentions in GitHub messages. This PR fixes that so that unread notifications get the @ treatment.","mergeCommitSha":"6c2a9b999213f239de98e9591164ac15edd4563d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2900","title":"Create MessageMentionModels for each @mentioned team member in GitHub ingested messages","createdAt":"2022-09-07T22:58:36Z"}
{"state":"Merged","mergedAt":"2022-09-12T18:27:54Z","number":2901,"body":"When a section is collapsed in a PR, our intersection observer will never observe it.\r\n\r\nAdd a mutation observer which restarts the intersection observer whenever there is a change to '.js_discussions' (scoped area where discussions are contained)","mergeCommitSha":"d9b2cbea4a82192d6b8a13bab3dd1be4deda08f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2901","title":"Handle collapsed sections for Notification Sync","createdAt":"2022-09-07T23:50:54Z"}
{"state":"Merged","mergedAt":"2022-09-08T02:20:12Z","number":2902,"mergeCommitSha":"900dd5f048282009468ef2fba346bdbbd770c804","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2902","title":"Remove no longer used property","createdAt":"2022-09-07T23:56:08Z"}
{"state":"Merged","mergedAt":"2022-09-08T00:04:47Z","number":2903,"body":"The motivation is to understand the dataset better for certain repos.\r\n\r\n<img width=\"899\" alt=\"Screen Shot 2022-09-07 at 16 58 33\" src=\"https://user-images.githubusercontent.com/1798345/189004637-93d829c5-83db-4666-a96a-8af05c11ba4d.png\">\r\n\r\n","mergeCommitSha":"70788a99ec5000a008172cb31c3caae527528f55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2903","title":"Add more repo stats","createdAt":"2022-09-07T23:58:16Z"}
{"state":"Merged","mergedAt":"2022-09-08T02:13:52Z","number":2904,"body":"Also fix auth service for local stack.\r\n\r\n<img width=\"363\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/189016732-21f5f34e-f439-412f-9eed-391ba9a341d8.png\">\r\n","mergeCommitSha":"52ddebe615066d7dd3aa745961f0fcb9736e30e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2904","title":"Add service platform information to honeycomb","createdAt":"2022-09-08T01:57:02Z"}
{"state":"Closed","mergedAt":null,"number":2905,"body":"Maybe?","mergeCommitSha":"0d3b9a7c08d95a1868d575ca40ec9b6bf796d14d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2905","title":"Archive themis-continuous-integration-updater threads","createdAt":"2022-09-08T05:54:27Z"}
{"state":"Merged","mergedAt":"2022-09-08T06:07:12Z","number":2906,"mergeCommitSha":"25958a43eb1453078138b1e9826567ce95af6043","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2906","title":"wrong logging context","createdAt":"2022-09-08T06:03:39Z"}
{"state":"Merged","mergedAt":"2022-09-08T06:23:55Z","number":2907,"mergeCommitSha":"04c0067c823281a5e714ea2215fed5c8f6ba7b38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2907","title":"Fix prod build number","createdAt":"2022-09-08T06:23:28Z"}
{"state":"Merged","mergedAt":"2022-09-08T06:26:41Z","number":2908,"mergeCommitSha":"ed675e4f1cbf6ccf3b55682fedceb52829eda0df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2908","title":"Ensure we log build number","createdAt":"2022-09-08T06:25:36Z"}
{"state":"Merged","mergedAt":"2022-09-08T13:56:39Z","number":2909,"body":"As our esteemed friend, @richiebres brought up, we're still doing some database locks (and in some cases, very inefficiently).\r\n\r\nIt does not make sense to do these operations outside of the schema manager seeing as they're all inter-related.\r\nFor example, triggers are dependent on stored procedures and tables being created.\r\n\r\nWe are now centralizing schema updates so that they're all contained in a singular lock.\r\n\r\nTESTING:\r\n1. Deleted local database and started all services concurrently via docker and confirmed that they all started correctly, databases were populated correctly etc.","mergeCommitSha":"62ca82bb435c634649193426d541f3d05c08e4eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2909","title":"Centralize schema updates and ensure they’re all wrapped in same lock","createdAt":"2022-09-08T07:46:01Z"}
{"state":"Merged","mergedAt":"2022-02-09T17:26:26Z","number":291,"body":"This is needed to fix SSL errors thrown in browser when using second level subdomains  e.g *.us-west-2.dev.getunblocked.com. \r\n\r\nhttps://chapter2global.slack.com/archives/C02T1N1LK19/p1644365865448009?thread_ts=1644365249.772729&cid=C02T1N1LK19","mergeCommitSha":"dfad4b0f36a9a6f7b120fdcef396a82364bed386","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/291","title":"move API service to new SSL cert. ","createdAt":"2022-02-09T07:55:35Z"}
{"state":"Merged","mergedAt":"2022-09-08T16:19:15Z","number":2910,"mergeCommitSha":"3928f55bfb996a374ebb5ce7b31569ac16229793","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2910","title":"Show number of threads created by author","createdAt":"2022-09-08T16:02:54Z"}
{"state":"Merged","mergedAt":"2022-09-08T16:30:09Z","number":2911,"body":"Inverted the logic by mistake.","mergeCommitSha":"00151eaf7a26f2ca73fcbf751c1282db5842702c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2911","title":"Fix team member page","createdAt":"2022-09-08T16:29:15Z"}
{"state":"Merged","mergedAt":"2022-09-08T17:20:55Z","number":2912,"mergeCommitSha":"487ee2ae5f86b4794bfe19e44ba113147e8901a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2912","title":"Cleanup schema managaer","createdAt":"2022-09-08T16:58:54Z"}
{"state":"Merged","mergedAt":"2022-09-08T17:30:23Z","number":2913,"mergeCommitSha":"6919a6767f5a929ba0f1117ddf3f250d031f3817","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2913","title":"Admin console schema lock cleanup","createdAt":"2022-09-08T17:21:41Z"}
{"state":"Merged","mergedAt":"2022-09-08T19:00:09Z","number":2914,"body":"Can be used to point users to files with lot's of threads.\r\n\r\nReturns this:\r\n```js\r\n[\r\n    { file: FilePath { value: 'src/game/actions.ts' }, count: 4 },\r\n    { file: FilePath { value: 'src/game/game2048.ts' }, count: 4 },\r\n    { file: FilePath { value: 'src/helpers/syntax.ts' }, count: 3 },\r\n    { file: FilePath { value: 'src/helpers/random.ts' }, count: 2 },\r\n    { file: FilePath { value: 'src/helpers/eventCache.ts' }, count: 2 },\r\n    { file: FilePath { value: 'README.md' }, count: 1 },\r\n    { file: FilePath { value: 'src/game/enums.ts' }, count: 1 },\r\n]\r\n```","mergeCommitSha":"3ffb165e9000c0d462eeb0798ae4da601c6f9748","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2914","title":"Provide an API to get the top-N files with source marks.","createdAt":"2022-09-08T18:38:11Z"}
{"state":"Merged","mergedAt":"2022-09-13T22:03:44Z","number":2915,"body":"This addresses the issue where @mentions in comments created from GitHub don't get the same treatment as @mentions in comments created from Unblocked.","mergeCommitSha":"71d4a587deee5e7ad194c08611a6810088f65ef4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2915","title":"Convert mentions in ingested comments to inline elements","createdAt":"2022-09-08T18:56:07Z"}
{"state":"Merged","mergedAt":"2022-09-09T20:51:28Z","number":2916,"body":"Implements the `updatePullRequest` operation","mergeCommitSha":"466736ce3c5356eadf605f7a3a3a197b77b53157","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2916","title":"Add ability to update pull request description","createdAt":"2022-09-08T19:50:57Z"}
{"state":"Merged","mergedAt":"2022-09-12T18:34:16Z","number":2917,"body":"Show confirmation or error messages after git operations\r\n<img width=\"666\" alt=\"CleanShot 2022-09-08 at 13 41 14@2x\" src=\"https://user-images.githubusercontent.com/1553313/189224300-f81d5aa2-a5b6-49d9-b179-f4ef7a7a3984.png\">\r\n<img width=\"528\" alt=\"CleanShot 2022-09-08 at 13 40 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/189224303-162d6e44-8443-4009-b6a6-e4d809bd69f8.png\">\r\n","mergeCommitSha":"a8faaa586eb815d34917abc3794031f30bce2a2d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2917","title":"Update Error messages on git operations","createdAt":"2022-09-08T20:56:22Z"}
{"state":"Merged","mergedAt":"2022-09-08T22:07:23Z","number":2918,"mergeCommitSha":"0fbbf74f9e0741122c830f0c9b909b40fe488157","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2918","title":"Do not update schema if later buildnumber operation has been registered","createdAt":"2022-09-08T21:01:00Z"}
{"state":"Merged","mergedAt":"2022-09-08T22:41:25Z","number":2919,"mergeCommitSha":"43f647cbc8b6d42bc038b3bc651c390a5152e669","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2919","title":"upgrade detekt","createdAt":"2022-09-08T22:12:04Z"}
{"state":"Merged","mergedAt":"2022-02-09T18:04:22Z","number":292,"body":"Rename health probe endpoints to include `__` at the beginning of their endpoint path. This is an unwritten standard in Kube world. I'll updated the APM probes once this has been deployed. ","mergeCommitSha":"7dc8bc322b1119e076be67aa3abcc343d6a14626","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/292","title":"following standards in kube world, I have renamed health probe endpoints","createdAt":"2022-02-09T17:25:24Z"}
{"state":"Merged","mergedAt":"2022-09-09T22:42:54Z","number":2920,"mergeCommitSha":"e1c34ae990a23d08ab21ff55573f0443ed493cd7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2920","title":"Grid and Stage layouts for main window","createdAt":"2022-09-08T22:39:22Z"}
{"state":"Merged","mergedAt":"2022-09-09T22:23:36Z","number":2921,"mergeCommitSha":"294345dea0d8c6c9deae7e17c3f504a1440efab0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2921","title":"Fix rendering issues","createdAt":"2022-09-08T22:39:24Z"}
{"state":"Merged","mergedAt":"2022-09-08T23:52:55Z","number":2922,"body":"1 gradle invocation rather than 2.\r\n\r\nOLD:\r\n```\r\nrasharab@Rashins-MacBook-Pro api % time make generate-api\r\n/Users/<USER>/chapter2/unblocked/api//../gradlew openApiGenerateShared\r\n\r\nBUILD SUCCESSFUL in 1s\r\n9 actionable tasks: 9 up-to-date\r\n/Applications/Xcode.app/Contents/Developer/usr/bin/make -C /Users/<USER>/chapter2/unblocked/api/../common generate-common-protos\r\n/Users/<USER>/chapter2/unblocked/common//../gradlew generateProto\r\n\r\nBUILD SUCCESSFUL in 1s\r\n11 actionable tasks: 11 up-to-date\r\nmake generate-api  1.33s user 0.17s system 47% cpu 3.152 total\r\n```\r\n\r\nNEW:\r\n```\r\nrasharab@Rashins-MacBook-Pro api % time make generate-api\r\ncd /Users/<USER>/chapter2/unblocked/api/.. && ./gradlew openApiGenerateShared generateProto\r\n\r\nBUILD SUCCESSFUL in 1s\r\n26 actionable tasks: 26 up-to-date\r\nmake generate-api  0.63s user 0.08s system 46% cpu 1.515 total\r\n```","mergeCommitSha":"efb20fb6c233b188ee6f24c9357d81ba47c1c5b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2922","title":"Improve generate-api call","createdAt":"2022-09-08T23:37:15Z"}
{"state":"Merged","mergedAt":"2022-09-12T23:51:42Z","number":2923,"body":"Never used.\r\n\r\nCleans up server, client and API spec.\r\n\r\nNon-breaking.","mergeCommitSha":"938d7773f11ae4ef7f78f83756afa98eda387033","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2923","title":"Remove anchor sourcemark concept","createdAt":"2022-09-08T23:38:32Z"}
{"state":"Merged","mergedAt":"2022-09-13T00:32:36Z","number":2924,"mergeCommitSha":"db4a3174c8ccc89aae19612cef94e51e54341483","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2924","title":"Drop SourceMarkModel isAnchor field","createdAt":"2022-09-08T23:49:23Z"}
{"state":"Merged","mergedAt":"2022-09-09T05:04:34Z","number":2925,"body":"When we generate fatjars, the plugin will generate zips and tars that we do not need.","mergeCommitSha":"fed139f6760661055d249b7b335a914100e133ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2925","title":"Ensure we do not zip and tar","createdAt":"2022-09-09T04:47:04Z"}
{"state":"Merged","mergedAt":"2022-09-10T00:25:28Z","number":2926,"mergeCommitSha":"b8eee6e7ce3448abb395b77f3f95cf67bd71a82d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2926","title":"Test remote cache","createdAt":"2022-09-09T07:41:45Z"}
{"state":"Merged","mergedAt":"2022-09-12T18:38:42Z","number":2927,"body":"<img width=\"513\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/189417420-6192ae22-f394-4e31-930c-6d11b53656c4.png\">\r\n\r\n* Refactored out the list of team members into its own component from the current MentionDropdown\r\n* Refactored out the keyDown listener to the TeamMemberDropdown component \r\n* Implemented using the existing zustand mention store but we should probably refactor this out (can be done separately)","mergeCommitSha":"a8299457cb925b512e156d4e8b8120c1df7ad3f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2927","title":"Add team members to invite dropdown","createdAt":"2022-09-09T18:17:49Z"}
{"state":"Merged","mergedAt":"2022-09-09T18:29:20Z","number":2928,"body":"fixes: https://linear.app/unblocked/issue/UNB-637/local-changes-cause-sm-resolution-to-stop-working","mergeCommitSha":"410b1973cea9c2a494174767b2146da7424e9a7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2928","title":"Fix SM engine lookup for file when any SM region is edited in an uncommitted file","createdAt":"2022-09-09T18:24:42Z"}
{"state":"Merged","mergedAt":"2022-09-13T20:25:18Z","number":2929,"body":"Highlights VSCode code blocks\r\n\r\nWorks well for single lines\r\n<img width=\"655\" alt=\"CleanShot 2022-09-09 at 11 36 14@2x\" src=\"https://user-images.githubusercontent.com/1553313/189421361-ee95ce93-fb24-424e-888c-efbbc9e044fb.png\">\r\n\r\nFor multi lines, snippets currently do not include buffer so it highlights the entire range... \r\nEither we add more buffer or we remove highlighting.\r\n<img width=\"655\" alt=\"CleanShot 2022-09-09 at 11 36 07@2x\" src=\"https://user-images.githubusercontent.com/1553313/189421334-2cc0eba2-e15a-46e8-86f9-376cdb013895.png\">\r\n\r\nRelated to shiki changes here:\r\nhttps://github.com/matthewjamesadam/shiki/pull/1\r\n\r\nShikiBundle changes need to be merged in first as we need to update package.json link\r\nhttps://github.com/NextChapterSoftware/ShikiBundle/pull/2\r\n","mergeCommitSha":"5e61924cba90a6cc08283d0cb7b3804dd5cccab1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2929","title":"Highlight VSCode code blocks","createdAt":"2022-09-09T18:45:13Z"}
{"state":"Merged","mergedAt":"2022-02-09T17:55:02Z","number":293,"body":"Removed the old cert. All services have been switched to use the new one. \r\n\r\nThis change has been deployed to all environments. ","mergeCommitSha":"50ac0415da371b412423bdefdc569a9324bfbd2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/293","title":"Remove old unused ssl cert","createdAt":"2022-02-09T17:52:55Z"}
{"state":"Merged","mergedAt":"2022-09-12T15:58:44Z","number":2930,"mergeCommitSha":"0e32ed7ba5adc74db7a32bd8a4c8f3b00fa4c10c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2930","title":"Add padding to multi-line snippets for comments ingested from GitHub","createdAt":"2022-09-09T19:12:41Z"}
{"state":"Merged","mergedAt":"2022-09-09T20:35:13Z","number":2931,"body":"Adds the cdk stack to create an ec2 instance with a gradle build cache node.\r\nhttp://gradlecache.secops.getunblocked.com/","mergeCommitSha":"2e0290c8ffad8877f4cc37f3ada36ba6bc2040bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2931","title":"AddGradleBuildCacheNodeStack","createdAt":"2022-09-09T20:26:35Z"}
{"state":"Merged","mergedAt":"2022-09-09T22:13:37Z","number":2932,"mergeCommitSha":"7d42a85d6b599b26df347859fcc05a62ea05b4a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2932","title":"Fix highlight tracking issues","createdAt":"2022-09-09T20:53:38Z"}
{"state":"Merged","mergedAt":"2022-09-12T18:14:02Z","number":2933,"body":"This factors out all of the VSCode git extension usage into a helper class, so that we can swap it out with a custom variant when the git extension is disabled:\r\n\r\n* `IGitProvider` is an interface to whoever is providing workspace git functionality.  Right now the only things this provides is the path to the git binary, and the set of git repos in the workspace.\r\n* `VSCodeGitProvider` implements this interface using VSCode's git extension\r\n* I am working on a second implementation that finds repos using git directly.\r\n\r\nSome notes:\r\n* I changed the resolved repo model (`ClientRepoAggregate`) a little bit -- this now contains both the published workspace repo, plus a `GitRunner` for that repo.  We were recreating GitRunners all over the place, which would reduce the value of caching and such.  The idea is that everyone can use this one GitRunner for each repo.","mergeCommitSha":"3a42c0ab8d61e84aff2a7b9a9bfd424ebde3089b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2933","title":"Factor out dependencies on VSCode git extension","createdAt":"2022-09-09T21:28:06Z"}
{"state":"Merged","mergedAt":"2022-09-09T22:45:00Z","number":2934,"body":"reported in slack channel https://chapter2global.slack.com/archives/C02HEVCCJA3/p1662602605379659\r\n\r\nwe dont send daily digest emails right now so we should remove the setting from the UI until implemented:\r\n\r\n<img width=\"1504\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/189454848-bdd2394a-06fb-498a-8a40-9f3e553f62aa.png\">\r\n\r\nNOTE: should revert this commit when the emails are implemented.\r\n","mergeCommitSha":"ecd215e66fd671cb1c854ed4902cfb16f3982242","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2934","title":"Remove digest email setting for now","createdAt":"2022-09-09T22:37:46Z"}
{"state":"Merged","mergedAt":"2022-09-10T00:33:01Z","number":2935,"mergeCommitSha":"826c77ade4727022993e043d7181b7508d6d70e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2935","title":"Fix uploads","createdAt":"2022-09-10T00:32:52Z"}
{"state":"Merged","mergedAt":"2022-09-10T00:42:17Z","number":2936,"body":"SM engine was tracking file renames in an incomplete way. The impact was that it would track the point\nup until it was renamed only. This resulted in failures to display the point in files after the rename.\n\nRelated to #2928.","mergeCommitSha":"3432fca02187a41451e7e80382f9f5df98216b41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2936","title":"Fix for SM commit path stitching to address issue with file renames","createdAt":"2022-09-10T00:41:07Z"}
{"state":"Merged","mergedAt":"2022-09-10T00:55:29Z","number":2937,"mergeCommitSha":"d6755a063fcef7d1cda803cd007f0194f7ee59e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2937","title":"preint environment","createdAt":"2022-09-10T00:49:10Z"}
{"state":"Closed","mergedAt":null,"number":2938,"mergeCommitSha":"0e699f4d9e54351bfa7fe8f853dc3a31d747de12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2938","title":"Compile search service","createdAt":"2022-09-10T01:02:07Z"}
{"state":"Merged","mergedAt":"2022-09-10T05:17:50Z","number":2939,"body":"We are now using the cache task.\r\nIt's a bit of a hack, but it's the only good one until we come up with another technique.","mergeCommitSha":"b7a53ec88d5eab9460b52cae8c5fc2ea53fc0ad9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2939","title":"Do not use upload action for saving and restoring artifacts as it is slow","createdAt":"2022-09-10T02:53:04Z"}
{"state":"Merged","mergedAt":"2022-02-09T19:29:17Z","number":294,"body":"This is canonical way of doing errors via openapi specs.\r\n\r\nIt is recommended to provide a default error response.\r\nAlso adding a ktor Status Page which handles exceptions that bubble up from api calls.\r\n","mergeCommitSha":"9285113115a0249e71146c6eb9c4114e2bcf1b38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/294","title":"Standardize error handling across api","createdAt":"2022-02-09T19:09:05Z"}
{"state":"Merged","mergedAt":"2022-09-10T19:07:09Z","number":2940,"mergeCommitSha":"bf176a24f98da88c35d7633fea4250bbdfa0d113","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2940","title":"Change server configuration for cache node","createdAt":"2022-09-10T19:06:48Z"}
{"state":"Merged","mergedAt":"2022-09-10T19:51:20Z","number":2941,"mergeCommitSha":"923822efb28a42965c542f620751f9566bcb70e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2941","title":"Do not cache apps","createdAt":"2022-09-10T19:50:14Z"}
{"state":"Merged","mergedAt":"2022-09-10T20:33:04Z","number":2942,"mergeCommitSha":"8cbb4b5d254cc191ff71af4d334fafa9b92f26fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2942","title":"Increase cache size","createdAt":"2022-09-10T20:21:36Z"}
{"state":"Merged","mergedAt":"2022-09-10T22:50:06Z","number":2943,"body":"Why the hell we didn't use this before is beyond me.","mergeCommitSha":"c4b6fe6c138bff0e1140e13216e431d349419551","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2943","title":"Enable local build cache","createdAt":"2022-09-10T22:22:23Z"}
{"state":"Merged","mergedAt":"2022-09-10T23:31:44Z","number":2944,"mergeCommitSha":"6196e8e79ebd5f23e629388875a2b521dd53371c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2944","title":"Use m6a instance type","createdAt":"2022-09-10T23:31:35Z"}
{"state":"Merged","mergedAt":"2022-09-11T04:51:28Z","number":2945,"body":"I just discovered that we were not exposing __deepcheck endpoints to grafana for any other public service other than apiserivce. \r\n\r\nThis PR adds necessary ALB routing rules to expose those endpoints. These endpoints are gated by WAF and require a special header. That's to avoid having someone DDoS our service considering these checks are expensive. ","mergeCommitSha":"79d868556bac29c2f04554c9fd9e0fbff3993a34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2945","title":"expose deepcheck for all public facing services","createdAt":"2022-09-11T04:43:43Z"}
{"state":"Merged","mergedAt":"2022-09-11T16:44:53Z","number":2946,"mergeCommitSha":"84fded759627f3f43ffed28cad01d335cf468fde","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2946","title":"Update shell script for buld cache node","createdAt":"2022-09-11T16:30:57Z"}
{"state":"Merged","mergedAt":"2022-09-11T19:51:08Z","number":2947,"body":"Addresses two warnings:\r\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\r\n!!                                                                            !!\r\n!!  Node 17 has reached end-of-life on 2022-06-01 and is not supported.       !!\r\n!!  Please upgrade to a supported node version as soon as possible.           !!\r\n!!                                                                            !!\r\n!!  This software is currently running on node v17.4.0.                       !!\r\n!!  As of the current release of this software, supported node releases are:  !!\r\n!!  - ^18.0.0 (Planned end-of-life: 2025-04-30)                               !!\r\n!!  - ^16.3.0 (Planned end-of-life: 2023-09-11)                               !!\r\n!!  - ^14.6.0 (Planned end-of-life: 2023-04-30)                               !!\r\n!!                                                                            !!\r\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\r\n\r\n\r\nAnd addresses warning during cdk runtime of later version of cdk available.\r\n\r\nSimply update node by using any of the following:\r\n`nvm install 18`\r\n`brew upgrade node`\r\n","mergeCommitSha":"dccc6ffabd9b3cd2effaf6c70bae9e07780aff9d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2947","title":"Update cdk dependencies","createdAt":"2022-09-11T16:39:18Z"}
{"state":"Merged","mergedAt":"2022-09-12T17:57:24Z","number":2948,"body":"Hide Insights button when missing repo.\r\n\r\nOccurs when unauthenticated / org not installed / repo not authenticated.","mergeCommitSha":"9702525d1aea4e9b296dc637500fa95390c5d075","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2948","title":"Hide Insights button when missing repo","createdAt":"2022-09-11T20:47:19Z"}
{"state":"Merged","mergedAt":"2022-09-12T22:36:11Z","number":2949,"body":"This is a follow-on from the previous PR (https://github.com/NextChapterSoftware/unblocked/pull/2933) -- it replaces usage of VSCode's fetch/checkout/pull git operations with our own.\r\n\r\nThe ops themselves are trivial, almost all the code here is to deal with error handling.\r\n\r\nI added a couple parsers, one that returns an error code for various failure conditions, one that returns an error message.  TBH I think the error message one may end up being an antipattern, as the message you want to display often depends on the context that the message is generated in, but for now it's fine.","mergeCommitSha":"df8cc0b454790401ceb5bbcdfc639cae9ff19de5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2949","title":"Add fetch/checkout/pull git ops to VSCode","createdAt":"2022-09-12T04:39:06Z"}
{"state":"Merged","mergedAt":"2022-02-09T20:19:09Z","number":295,"body":"No need to have so many files, we can consolidate","mergeCommitSha":"b11f95fdd0e095f706acbcb131f83d31cb3bf602","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/295","title":"Clean up PullRequestReviewThreadService","createdAt":"2022-02-09T19:42:17Z"}
{"state":"Merged","mergedAt":"2022-09-12T05:22:20Z","number":2950,"mergeCommitSha":"29f728605f44b2007db40452fad17e273546ff79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2950","title":"Specify build cache directory","createdAt":"2022-09-12T05:16:52Z"}
{"state":"Closed","mergedAt":null,"number":2951,"mergeCommitSha":"6b1e91482fc6c395b9194cc1d1707795ad30e273","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2951","title":"Video Chat Layouts & Screen Sharing UX","createdAt":"2022-09-12T17:01:33Z"}
{"state":"Merged","mergedAt":"2022-09-12T17:18:43Z","number":2952,"mergeCommitSha":"829acdfe4b1fc8c920da88782679d04e23ebdf2c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2952","title":"Cleanup gradle home cache","createdAt":"2022-09-12T17:11:48Z"}
{"state":"Merged","mergedAt":"2022-09-13T00:07:32Z","number":2953,"mergeCommitSha":"2e6789f589a4d5186df2e38d7e92ad206221669e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2953","title":"Remove the unused message parameter from SourceMarkModel","createdAt":"2022-09-12T17:38:16Z"}
{"state":"Merged","mergedAt":"2022-09-12T17:39:10Z","number":2954,"body":"This reverts commit 829acdfe4b1fc8c920da88782679d04e23ebdf2c.\r\n","mergeCommitSha":"ce7a74df85b76ca7e126250b4f4bee972f577a66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2954","title":"Revert \"Cleanup gradle home cache (#2952)\"","createdAt":"2022-09-12T17:39:05Z"}
{"state":"Merged","mergedAt":"2022-09-23T05:00:36Z","number":2955,"mergeCommitSha":"7db7946aab15fc474f10be9b6493ef3f80ea52c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2955","title":"Video App Layout & Screen Share","createdAt":"2022-09-12T18:13:29Z"}
{"state":"Merged","mergedAt":"2022-09-12T19:14:29Z","number":2956,"body":"- Updated construct module to get latest bug fixes for CDK \r\n- Created a new EFS stack to provision an EFS storage in each EKS VPC. This EFS storage is intended for holding copies of git checkouts \r\n- EFS storage is configured with intelligent tiering. It moves untouched files to lower storage tier after 14 days and returns them to primary after the first access \r\n- Added configuration for creating EFS controller service account to eksctl config \r\n- Added yaml for storage class definitions \r\n\r\nAll changes above have been deployed to both Dev and prod. ","mergeCommitSha":"4ec676d011041f88e06e43546956f4b83786bf6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2956","title":"Add efs csi driver","createdAt":"2022-09-12T18:21:06Z"}
{"state":"Merged","mergedAt":"2022-09-13T00:33:44Z","number":2957,"mergeCommitSha":"b8d4c4e4ba0d751104e7f344fd24c24c639db4ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2957","title":"DROP SourceMarkModel message column","createdAt":"2022-09-12T19:01:54Z"}
{"state":"Merged","mergedAt":"2022-09-12T22:05:17Z","number":2958,"body":"The problem was ultimately due to incorrect transaction manager being used, seeing as we're using unique databases per test.\r\n\r\nI tracked it down to this line in Suspended.kt in Exposed.\r\n\r\n```\r\n    suspend fun newScope(_tx: Transaction?): T {\r\n        val manager = (_tx?.db ?: db ?: TransactionManager.currentDefaultDatabase.get())?.transactionManager ?: TransactionManager.manager\r\n        ...\r\n        return TransactionScope(tx, newContext + element).body()\r\n    }\r\n```\r\n\r\nBy default, the transaction db was null, the TransactionManger.currentDefaultDabase was null, so it would use whatever was set as the global TransactionManger.manager, which is based off a threadLocal value which does not work for coroutines!\r\n\r\nTo get around this, we are now doing several things.\r\n1. All database tests now set a database coroutine context, and that is used, when available, for suspendedTransaction.\r\n2. All client/server tests need to ensure that any ApplicationTestEngine calls are injected with the correct Database context. That is guaranteed now by a DatabaseContextPlugin.\r\n\r\nAll tests on my local machine now run under 3 minutes 40 seconds.","mergeCommitSha":"ca6183d1c18bb814ce043f7d34d268fc1fa1d6b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2958","title":"[BUG FIX] Ensure correct database context is used for tests","createdAt":"2022-09-12T20:40:58Z"}
{"state":"Merged","mergedAt":"2022-09-13T16:11:22Z","number":2959,"body":"Add Insights and PR to status bar.\r\n\r\nTODO: Add unblocked icons using woff\r\nhttps://code.visualstudio.com/api/extension-guides/product-icon-theme\r\n\r\n<img width=\"366\" alt=\"CleanShot 2022-09-12 at 13 44 36@2x\" src=\"https://user-images.githubusercontent.com/1553313/189755162-be3ed0c9-3307-4ea9-a9ca-c1b562cf0102.png\">\r\n","mergeCommitSha":"1681bd0b39577bf52bdc009efdc51ab8fb88fed8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2959","title":"Status bar insights and PR","createdAt":"2022-09-12T20:44:54Z"}
{"state":"Merged","mergedAt":"2022-02-09T20:21:49Z","number":296,"body":"- Update\r\n- update\r\n","mergeCommitSha":"041836861cdc8d400b102315d2ebec619a41759d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/296","title":"Minor cleanup","createdAt":"2022-02-09T20:14:27Z"}
{"state":"Merged","mergedAt":"2022-09-12T20:59:50Z","number":2960,"mergeCommitSha":"0f0f0f6580accab4d046e0787200b869e6319891","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2960","title":"Fix nginx config","createdAt":"2022-09-12T20:58:01Z"}
{"state":"Merged","mergedAt":"2022-09-12T22:26:08Z","number":2961,"mergeCommitSha":"44ae8c5a2a5500807c36f18131e837aa908fbf60","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2961","title":"Fix VSCode create discussion","createdAt":"2022-09-12T22:15:07Z"}
{"state":"Merged","mergedAt":"2022-09-13T00:03:45Z","number":2962,"mergeCommitSha":"cd2586936918b20c19ff831fd0132ed0a20f09f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2962","title":"Try to eliminate jdk download","createdAt":"2022-09-12T22:28:11Z"}
{"state":"Merged","mergedAt":"2022-09-14T18:10:29Z","number":2963,"body":"Fixes UNB-636\r\n\r\n`ThreadUnread`s are already handled correctly for archived threads.","mergeCommitSha":"ee67875d8f0401ffcf16aec086c9cebcd04e77a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2963","title":"Restore archived thread when creating a reply","createdAt":"2022-09-12T23:04:07Z"}
{"state":"Merged","mergedAt":"2022-09-13T20:33:54Z","number":2964,"body":"Fixes UNB-455\r\n\r\nAdds `CustomGitProvider`, which is a `GitProvider` with custom code for finding the git binary and finding git repos in the workspace.  The code is pretty heavily commented so I won't outline its behaviour much here.\r\n\r\nI'm considering how to add some amount of automated testing to this -- it should be possible since this doesn't really depend on VSCode at all.  Maybe we could add canned zip files with sample folder structures that we can run off of...","mergeCommitSha":"589e479118e717c3ff251534b259630017225c6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2964","title":"Add custom git provider to VSCode","createdAt":"2022-09-12T23:08:14Z"}
{"state":"Merged","mergedAt":"2022-09-13T04:28:46Z","number":2965,"mergeCommitSha":"da39b31f8955d2455a6a937a1c2536a6d1787279","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2965","title":"Strip signatures from thread titles when updating the first message","createdAt":"2022-09-12T23:24:46Z"}
{"state":"Merged","mergedAt":"2022-09-13T21:41:41Z","number":2966,"body":"## Summary\r\nUpgrades now happen automatically as soon as the download is finished. The \"post-upgrade\" launch generates a system notification that an upgrade has occurred. Click on that notification displays the upgrade dialog with markdown text. If the user ignores the notification, the hub will display the upgrade content until the continue button is clicked. When the Hub is in the \"Just Upgraded\" state, the menu bar icon pulses gently to let the user know something happened.\r\n\r\n### Pulsing upgrade icon\r\nhttps://user-images.githubusercontent.com/858772/189991273-e305319f-ea00-4d27-9315-d351c663d4df.mp4\r\n\r\n### Ignore notification flow\r\nhttps://user-images.githubusercontent.com/858772/189991286-20605cfc-a91a-44d7-b0cc-d4e4171d2c54.mp4\r\n\r\n### Click notification flow\r\nhttps://user-images.githubusercontent.com/858772/189991341-fdfcee2c-d403-4b77-8175-b71ae8491798.mp4\r\n\r\n","mergeCommitSha":"1eebf274c04e6b09f63f82c4ff0b87f5ce26b2e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2966","title":"Upgrade silently in the background then show notification","createdAt":"2022-09-12T23:36:32Z"}
{"state":"Merged","mergedAt":"2022-09-14T21:52:01Z","number":2967,"body":"Fixes UNB-626\r\n\r\n@benedict-jw :\r\n* Where in the menu should this appear?  I put it at the bottom, but we can put it in a different location if we want to\r\n* The label is a default from our existing command -- do we want `Unblocked:` at the beginning?\r\n\r\nhttps://user-images.githubusercontent.com/2133518/189781429-ba605347-51de-45fd-a5b0-45af95c595ff.mp4\r\n\r\n","mergeCommitSha":"c265490cfb6abfcedc1dc97320a41768b2ece47f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2967","title":"Add 'Start Discussion' command to editor context menu","createdAt":"2022-09-13T00:28:18Z"}
{"state":"Merged","mergedAt":"2022-09-13T00:37:31Z","number":2968,"mergeCommitSha":"d0d40dadd2dca63ffc33630f0402fc90046ca1cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2968","title":"update","createdAt":"2022-09-13T00:37:23Z"}
{"state":"Merged","mergedAt":"2022-09-13T01:59:23Z","number":2969,"mergeCommitSha":"e55b1d5211b7ed8c093a2cea3b44aab9bdf01b75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2969","title":"Try setup java cache","createdAt":"2022-09-13T01:48:06Z"}
{"state":"Merged","mergedAt":"2022-02-09T22:07:39Z","number":297,"body":"We're going to create Identity models for each comment author that doesn't already exist in our system, and then a TeamMember which will be used for the author field of the message object. We need some additional fields from the GitHub API for the Identity model, this PR grabs those fields.\n","mergeCommitSha":"20aa6068e87f36ecaf4c1802ce8b6e228ead3955","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/297","title":"Get additional fields required to create Identity models","createdAt":"2022-02-09T21:55:02Z"}
{"state":"Merged","mergedAt":"2022-09-13T04:21:31Z","number":2970,"body":"<img width=\"521\" alt=\"Screen Shot 2022-09-12 at 20 18 27\" src=\"https://user-images.githubusercontent.com/1798345/189799962-59fec734-4a7c-48fb-8c00-9359a8060953.png\">\r\n","mergeCommitSha":"a7b0fdd813d80b2be2c55426027ab2c2f45a75eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2970","title":"Render Client Version Breakdown","createdAt":"2022-09-13T03:17:46Z"}
{"state":"Merged","mergedAt":"2022-09-13T05:19:28Z","number":2971,"mergeCommitSha":"279c838198b413e50a83a32e44c8a8c392375760","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2971","title":"Version adoption metrics cleanup","createdAt":"2022-09-13T05:08:33Z"}
{"state":"Merged","mergedAt":"2022-09-13T05:54:08Z","number":2972,"mergeCommitSha":"fdc3120b6101b12908150a4cb847f118158f5e12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2972","title":"Remove completed migrations","createdAt":"2022-09-13T05:48:59Z"}
{"state":"Merged","mergedAt":"2022-09-13T18:31:18Z","number":2973,"body":"Fixes UNB-606\r\n\r\n* Remove the Unblocked logo\r\n* Add a 'Powered by Unblocked' label to the end of the stacked label (for single threads), or below all other content (for multi-threads).\r\n\r\nSingle-thread:\r\n<img width=\"730\" alt=\"Screen Shot 2022-09-13 at 9 12 19 AM\" src=\"https://user-images.githubusercontent.com/2133518/189955800-db4f69ad-f9bd-42be-acdb-f0161848a805.png\">\r\n\r\nMulti-thread:\r\n<img width=\"603\" alt=\"Screen Shot 2022-09-13 at 9 11 52 AM\" src=\"https://user-images.githubusercontent.com/2133518/189955796-d7332fea-9fa3-4be2-a387-798803a52311.png\">\r\n","mergeCommitSha":"ec92e4ad6900972c92aad8e22e31c02300010177","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2973","title":"Update VSCode tooltips","createdAt":"2022-09-13T16:26:34Z"}
{"state":"Merged","mergedAt":"2022-09-13T18:37:30Z","number":2974,"mergeCommitSha":"12d8aa84cb922107f315ba305633b3a817ec97e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2974","title":"Ensure eips ahve names","createdAt":"2022-09-13T18:30:33Z"}
{"state":"Merged","mergedAt":"2022-09-16T23:06:28Z","number":2975,"body":"Updated CSS for detailView to support banners.\r\nUpdated CSS for Icon and general padding to discussion thread.\r\nAdded restoration banner to discussion thread.\r\nAdded restoration button to summary discussion threads. \r\n\r\n<img width=\"643\" alt=\"CleanShot 2022-09-13 at 11 32 03@2x\" src=\"https://user-images.githubusercontent.com/1553313/189982469-3b75c121-2a39-4f17-89d3-3ca4715ef0c0.png\">\r\n<img width=\"1221\" alt=\"CleanShot 2022-09-13 at 11 29 10@2x\" src=\"https://user-images.githubusercontent.com/1553313/189982495-d9d5af2f-a599-40d8-a752-9d44d5fe121b.png\">\r\n<img width=\"714\" alt=\"CleanShot 2022-09-13 at 11 31 54@2x\" src=\"https://user-images.githubusercontent.com/1553313/189982501-f3649729-fa67-4f0f-959f-b188de3b3d13.png\">\r\n<img width=\"1238\" alt=\"CleanShot 2022-09-13 at 11 28 40@2x\" src=\"https://user-images.githubusercontent.com/1553313/189982505-729998fc-023f-4fab-a8da-a9ec71c24c02.png\">\r\n<img width=\"1242\" alt=\"CleanShot 2022-09-13 at 11 28 37@2x\" src=\"https://user-images.githubusercontent.com/1553313/189982517-143f9c38-1c5f-410f-97e7-7474e24c06be.png\">\r\n<img width=\"1242\" alt=\"CleanShot 2022-09-13 at 11 34 27@2x\" src=\"https://user-images.githubusercontent.com/1553313/189982829-21c3e11e-bd0e-4b05-97b8-286d075dc026.png\">\r\n","mergeCommitSha":"1f958796c52d4e724094deef30fadacc66a04250","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2975","title":"Restore discussion on dashboard","createdAt":"2022-09-13T18:41:51Z"}
{"state":"Merged","mergedAt":"2022-09-13T22:48:38Z","number":2976,"mergeCommitSha":"938a8a1f243f202503966cd26052d88ee8d8e99e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2976","title":"Update tool cache","createdAt":"2022-09-13T18:48:07Z"}
{"state":"Merged","mergedAt":"2022-09-13T22:40:52Z","number":2977,"mergeCommitSha":"5b835cb3c9be462702a1914cd9b79668b5ee9977","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2977","title":"Remove migration","createdAt":"2022-09-13T22:35:52Z"}
{"state":"Merged","mergedAt":"2022-09-13T23:28:46Z","number":2978,"body":"Fix scrolling issue in unresolved code block","mergeCommitSha":"7d060ad0c1a2713e0c17812fea5e7fa66ba24216","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2978","title":"Fix scrolling","createdAt":"2022-09-13T22:41:03Z"}
{"state":"Merged","mergedAt":"2022-09-13T23:02:56Z","number":2979,"mergeCommitSha":"b4ec8ad9157e51deda166db2640e23a00eafb44e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2979","title":"Fix lint","createdAt":"2022-09-13T22:57:23Z"}
{"state":"Merged","mergedAt":"2022-02-10T18:22:13Z","number":298,"body":"Move the MessageView to the shared/ directory and create a vscode styled version\r\n*Note that the CodeBlock components are too different client per client, so I moved that to be passed into the component as children (so the MessageView component doesn't need to understand the CodeBlock properties)\r\n\r\nshared (barebones):\r\n![image](https://user-images.githubusercontent.com/13431372/153296195-e70c8b8c-96da-436b-b549-d167d2557b36.png)\r\n\r\nweb:\r\n![image](https://user-images.githubusercontent.com/13431372/153298842-e4b30d6a-4d5d-49b7-9bce-1060eb25ec82.png)\r\n\r\nvscode:\r\n![image](https://user-images.githubusercontent.com/13431372/153298890-722f6562-29f1-4ee7-a8fd-4a4cf0ee0d8e.png)\r\n\r\n","mergeCommitSha":"5ad77dd92d2b3197fb17f5932078eff38a71f073","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/298","title":"Move MessageView container to shared and add to vscode UI","createdAt":"2022-02-09T22:12:53Z"}
{"state":"Merged","mergedAt":"2022-09-13T23:03:40Z","number":2980,"mergeCommitSha":"dab40437ea2d3b529ca71a7a37f7a87ca0a8cfba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2980","title":"Move to primed docker image","createdAt":"2022-09-13T22:58:30Z"}
{"state":"Merged","mergedAt":"2022-09-13T23:10:18Z","number":2981,"mergeCommitSha":"6e9c0dc02a0063bdd1fb2f7dfa88d03258c61f11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2981","title":"Remove ls","createdAt":"2022-09-13T23:10:10Z"}
{"state":"Merged","mergedAt":"2022-09-15T20:36:17Z","number":2982,"body":"Running into interesting bug where API requests are blocked indefinitely by a refresh auth promise.\r\nThis occurs since refresh auth process had it's own retry logic which could loop forever.\r\n\r\nRemove refresh auth's retry logic and depend on generic API retry logic.\r\n\r\n","mergeCommitSha":"9fd7f85067586e980f06335b0c00aa7895a09144","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2982","title":" Disable specific refresh retry","createdAt":"2022-09-13T23:40:25Z"}
{"state":"Merged","mergedAt":"2022-09-14T16:08:29Z","number":2983,"body":"## Bug\r\nPrevious published version was racing against update to latest version info causing the update text to be prematurely dismissed. ","mergeCommitSha":"d22d17195a90b02e8586cb569bfd7af4c14203aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2983","title":"Fix updated state bug","createdAt":"2022-09-13T23:42:04Z"}
{"state":"Merged","mergedAt":"2022-09-14T01:11:25Z","number":2984,"body":"Need to migrate existing comments to create mentions blocks","mergeCommitSha":"27e85bdc52b767b7f3551752918667249fc97a2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2984","title":"Backfill mentions for ingested comments","createdAt":"2022-09-13T23:54:56Z"}
{"state":"Merged","mergedAt":"2022-09-14T01:19:58Z","number":2985,"mergeCommitSha":"f9690fa02d4a9ab5123d5a8e54353eea3321d11a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2985","title":"Use s3 pagination apis","createdAt":"2022-09-14T00:35:37Z"}
{"state":"Merged","mergedAt":"2022-09-14T03:48:40Z","number":2986,"body":"https://www.notion.so/nextchaptersoftware/Engagement-Score-bc769924dc4d4bd08c296d8c0bbd31a0","mergeCommitSha":"12ef239e7083bdba442ce6f24cafc9f0dee16c69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2986","title":"Engagement score in admin web","createdAt":"2022-09-14T03:13:36Z"}
{"state":"Closed","mergedAt":null,"number":2987,"body":"https://www.notion.so/nextchaptersoftware/Address-Source-Mark-Scaling-Issues-48e123a953e841ac950da37356c9bdef","mergeCommitSha":"ffdae520c10ae87d0ce052b82c663526501aeebf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2987","title":"Optimize source point snippets and file paths","createdAt":"2022-09-14T03:53:24Z"}
{"state":"Merged","mergedAt":"2022-09-14T04:32:21Z","number":2988,"body":"Reverts NextChapterSoftware/unblocked#2986\r\n\r\nNeeds work.","mergeCommitSha":"6e1428fc6759267ac1c6b6294804065a1b36b393","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2988","title":"Revert \"Engagement score in admin web\"","createdAt":"2022-09-14T04:32:13Z"}
{"state":"Merged","mergedAt":"2022-09-30T20:52:30Z","number":2989,"body":"Fixes UNB-591\r\n\r\nCache ThreadInfos so that we aren't constantly thrashing the fetchThreads API whenever people change files.","mergeCommitSha":"08a46c9164117084bc05a037b2c607f558d82ed0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2989","title":"Cache ThreadInfos","createdAt":"2022-09-14T05:08:16Z"}
{"state":"Merged","mergedAt":"2022-02-10T21:18:42Z","number":299,"body":"- Added api.getunblocked.com user friendly alias records for apiservi…ce.us-west-2.prod.getunblocked.com\r\n- Deleted manually created `dashboard.getunblocked.com` alias and re-created it in CDK code\r\n- Added ability to create IAM groups, attach policies for groups and add users to each group\r\n- Re-created  all existing manually created IAM groups and their respective policies relating to automation work (admin groups are still manually managed)\r\n- Deployed all these changes to all envs\r\n\r\nBesides some cosmetic code cleanup, I think we are ready to start implementing CI/CD for infra...yaaay!","mergeCommitSha":"7a25abac05b220ffe9ab8c5d9e3114dad2204b91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/299","title":"Managing root (Management) account components (DNS and IAM) using CDK","createdAt":"2022-02-09T22:21:49Z"}
{"state":"Merged","mergedAt":"2022-09-14T05:55:53Z","number":2990,"body":"Slight update to spinner keyframe?\r\n\r\nCSS animations for spinners are already the \"best\" technique possible as it takes advantage of GPU acceleration. Using SVGs, animations, should be more taxing...\r\n\r\nOf course, if you're GPU bound and CPU bound, everything is going to be slow so the only alternative would be to *not* have a spinner.\r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1663128358429189\r\n\r\nTested this with GPU acceleration disabled. \r\n<img width=\"639\" alt=\"CleanShot 2022-09-13 at 22 10 46@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-6cd1dfc4-bd4a-4960-b425-106620d35710.png\">\r\n\r\nBefore:\r\n<img width=\"859\" alt=\"CleanShot 2022-09-13 at 22 12 05@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-cd4bda4a-d9bc-4b5a-b889-76781338408e.png\">\r\n\r\nAfter:\r\n<img width=\"858\" alt=\"CleanShot 2022-09-13 at 22 11 14@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-ae11b9bb-eb65-4f3a-987f-2e6dfbd3f17a.png\">\r\n\r\nBut not sure if that's due to the changes or just random variance... \r\n","mergeCommitSha":"40a933f846e8e6f22aa4c5142a8b687bab0b1ac4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2990","title":"Update spin keyframe","createdAt":"2022-09-14T05:15:28Z"}
{"state":"Merged","mergedAt":"2022-09-14T05:24:09Z","number":2991,"mergeCommitSha":"1a78467e5b46f2658021b8c7b44bcdf331363aaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2991","title":"Update readme for java tool caching","createdAt":"2022-09-14T05:21:51Z"}
{"state":"Merged","mergedAt":"2022-09-14T07:38:01Z","number":2992,"body":"### Motivation\r\n\r\nGiven a repo that is fully up to date wrt the source marks — meaning the latest\r\npoint has been calculated, upstreamed, and downloaded for all marks — then why does\r\nfull recalculation take so long? Something is wrong here because the only work needed\r\nis to rebuild a point cache per file from the downloaded dataset.\r\n\r\n### Changes\r\n\r\nTurns out that we were not leveraging the pre-calculated points very much. Now we\r\ncheck to see if the HEAD commit has been previously calculated for a point based on\r\nthe point file-content hash.\r\n\r\n### Results\r\n\r\nHard to estimate from the debugger environment, but looks like ~2X faster.","mergeCommitSha":"0bf1334d5cb2e93347565628100d0e7aebd7e7aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2992","title":"SourceMark Engine Performance Optimization","createdAt":"2022-09-14T07:27:40Z"}
{"state":"Merged","mergedAt":"2022-09-14T18:42:59Z","number":2993,"body":"This reverts commit 27e85bdc52b767b7f3551752918667249fc97a2e.","mergeCommitSha":"69288e5c229e5ed159f209cd55105db061d9ffac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2993","title":"Remove no longer needed migrator","createdAt":"2022-09-14T18:31:10Z"}
{"state":"Merged","mergedAt":"2022-09-14T19:30:50Z","number":2994,"body":"Bye bye snippets prototype.","mergeCommitSha":"e39b1c15f5ceed679ccb4080892d27d13728b417","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2994","title":"[BREAKS API ON MAIN] Delete snippet libs","createdAt":"2022-09-14T19:07:23Z"}
{"state":"Closed","mergedAt":null,"number":2995,"mergeCommitSha":"82c30ae1423d55d64b915431b126c983916cf63f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2995","title":"Puts video app and hub into same app group to share container","createdAt":"2022-09-14T19:07:26Z"}
{"state":"Merged","mergedAt":"2022-09-14T19:53:46Z","number":2996,"body":"- explicitly add `--no-pager`\n- remove spammy log\n\nBetter solutions here:\nhttps://linear.app/unblocked/issue/UNB-655/sm-engine-needs-better-handling-for-very-large-diffs","mergeCommitSha":"17473b7c0b3e6f27eedf0096940e39de649f46f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2996","title":"Bandaid for handling very large diffs in SM engine","createdAt":"2022-09-14T19:40:35Z"}
{"state":"Merged","mergedAt":"2022-09-14T22:17:54Z","number":2997,"mergeCommitSha":"9b5b86bd02ff1f551ac53e1dd838258f6777cae0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2997","title":"Add slack service","createdAt":"2022-09-14T21:18:40Z"}
{"state":"Merged","mergedAt":"2022-09-14T22:18:06Z","number":2998,"body":"Main changes:\r\n\r\n- Adds a `TeamMember.ignoreThreads` property and the ability to toggle this field from the admin console\r\n- Adds a `ArchivedReason.IgnoredAuthor` enum to be used when archiving a thread created by a team member where ignoreThreads is true\r\n\r\nhttps://chapter2global.slack.com/archives/C02VCS8L4R5/p1662657157423509?thread_ts=1662591126.673819&cid=C02VCS8L4R5","mergeCommitSha":"bf50e8f713008098d64b57aa7be92ce4a6ade14a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2998","title":"Archive threads started by team member where ignoreThreads is true","createdAt":"2022-09-14T21:21:46Z"}
{"state":"Merged","mergedAt":"2022-09-19T23:43:13Z","number":2999,"body":"When creating an insight bubble from VSCode, inject additional context below and after.\r\n\r\n<img width=\"642\" alt=\"CleanShot 2022-09-14 at 15 13 31@2x\" src=\"https://user-images.githubusercontent.com/1553313/190277531-45aa1306-b3f9-481a-bfe6-8e9ed2e4cba1.png\">\r\n<img width=\"1178\" alt=\"CleanShot 2022-09-14 at 15 11 00@2x\" src=\"https://user-images.githubusercontent.com/1553313/190277539-752f7824-68fd-43e7-9894-70e43efd4b61.png\">\r\n<img width=\"1192\" alt=\"CleanShot 2022-09-14 at 15 11 13@2x\" src=\"https://user-images.githubusercontent.com/1553313/190277546-732ade19-e0c5-490f-8dec-d1e69f63afa7.png\">\r\n<img width=\"1205\" alt=\"CleanShot 2022-09-14 at 15 10 42@2x\" src=\"https://user-images.githubusercontent.com/1553313/190277553-2aad3765-3ee1-4829-a1a1-5d2dfda7aada.png\">\r\n","mergeCommitSha":"f4f226a684308626c7ea35a2f86d83ebc50e0a42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2999","title":"Additional context for blue bubble snippets ","createdAt":"2022-09-14T22:59:36Z"}
{"state":"Merged","mergedAt":"2021-12-10T00:51:26Z","number":3,"mergeCommitSha":"fd64c90851b82d5ccdee3c6243073408bde5899a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3","title":"Add Jest test","createdAt":"2021-12-10T00:38:30Z"}
{"state":"Merged","mergedAt":"2022-01-13T19:36:57Z","number":30,"body":"Updating web codebase to use Sass.\r\n\r\nUsing GH Primer (https://primer.style/css/) to influence variables.","mergeCommitSha":"391c9db7915721811631df8bb5f79cf9900976ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/30","title":"Sass","createdAt":"2022-01-12T19:17:23Z"}
{"state":"Merged","mergedAt":"2022-02-09T23:09:30Z","number":300,"mergeCommitSha":"08f0bdac239cc9301cf8af62e7848531a25ce67d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/300","title":"Fix zally","createdAt":"2022-02-09T22:45:03Z"}
{"state":"Merged","mergedAt":"2022-09-14T23:20:14Z","number":3000,"mergeCommitSha":"298e49c05da7b12ce1edbf95503d68b33caa7a55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3000","title":"Add \"Ignored Author\" trait to threads page","createdAt":"2022-09-14T23:05:10Z"}
{"state":"Merged","mergedAt":"2022-09-14T23:32:12Z","number":3001,"mergeCommitSha":"8cca338e924117369e11d3e812dfbbcfcfc44e09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3001","title":"Remove migration","createdAt":"2022-09-14T23:24:46Z"}
{"state":"Merged","mergedAt":"2022-09-26T21:55:44Z","number":3002,"mergeCommitSha":"85aa28a9647017dfe9ac190d8ae6997bd342c00d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3002","title":"Set PullRequest.ingestionInProgress","createdAt":"2022-09-14T23:50:34Z"}
{"state":"Merged","mergedAt":"2022-09-15T00:46:35Z","number":3003,"body":"This pr copies over some utilities I created for slack api usage using Slack java sdk.\r\n\r\nIt also cleans up how we're doing webhook posts such that we are now using Slack's models and apis.\r\nWe should be using the canonical Payload model that slack provides for webhook posts **which has a dsl!** for block generation.\r\n\r\nShould make richie happy there...\r\nConfirmed new webhook apis are working as I sent myself one. :)\r\n\r\n<img width=\"669\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/190283363-8cf31a09-6a1e-48c7-a2b1-cc810de21a13.png\">\r\n","mergeCommitSha":"aebfc72d9fc15c766dabd5fc13e35317f6b4908d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3003","title":"Add slack client apis","createdAt":"2022-09-15T00:02:05Z"}
{"state":"Merged","mergedAt":"2022-09-15T00:22:15Z","number":3004,"mergeCommitSha":"505cfd3fef1cc801dda935c2a54c6d1e0cd057a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3004","title":"update","createdAt":"2022-09-15T00:22:08Z"}
{"state":"Merged","mergedAt":"2022-09-15T18:58:51Z","number":3005,"body":"The editor toolbar buttons don't work.  This regressed here: https://github.com/NextChapterSoftware/unblocked/pull/2927\r\n\r\nThe issue is that the @-mention dropdown captures mouse input regardless of whether it is open or not.  I don't know if this solution is optimal but it seems to work.","mergeCommitSha":"8e7f5c69e6a6de1d14adc7ebf29efca0bbdaeecb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3005","title":"Fix message editor buttons","createdAt":"2022-09-15T04:31:40Z"}
{"state":"Merged","mergedAt":"2022-09-15T05:17:29Z","number":3006,"mergeCommitSha":"97c464dd8854cd9d5a029dfe1ef3e2f732334868","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3006","title":"Persist isTrusted field for new blue insight points","createdAt":"2022-09-15T05:08:04Z"}
{"state":"Merged","mergedAt":"2022-09-16T07:04:16Z","number":3007,"body":"fixes https://linear.app/unblocked/issue/UNB-593/persist-threadisdeleted-and-threadarchivedat-on-sourcemarkmodel\r\n\r\nFollow-up: migration to backfill isArchived and isDeleted on SM. can happen in the follow-up - we can ship with this and everything will still work fine.\r\n\r\n---\r\n\r\n### Impact\r\nExperience when you attempt to view a thread that has been archived from Search.\r\n\r\n<img width=\"1427\" alt=\"Screen Shot 2022-09-15 at 17 27 12\" src=\"https://user-images.githubusercontent.com/1798345/190531246-0d4cfd90-a763-4b78-814e-58e06759bd21.png\">\r\n","mergeCommitSha":"7aeb06eef7fa79916641e98bbfae8594dcc00f6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3007","title":"Source marks are archived when threads are archived","createdAt":"2022-09-15T05:33:35Z"}
{"state":"Merged","mergedAt":"2022-09-16T00:01:31Z","number":3008,"body":"This spec defines 3 RPC interfaces:\r\n1. Join an existing channel\r\n2. Start a new channel\r\n3. Listen for video events\r\n\r\nClients should launch the video app and then connect to the event stream before issuing join/new channel requests. ","mergeCommitSha":"f8e9e57f614d181a061e68f3d27bb21a5fa7d9ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3008","title":"Video Chat IPC Spec","createdAt":"2022-09-15T16:21:37Z"}
{"state":"Merged","mergedAt":"2022-09-16T17:41:10Z","number":3009,"body":"This is necessary so that the Hub can sniff the IPC channel for the video chat app","mergeCommitSha":"a5fdf1c61721aa7b539e852dbca6775bcae348e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3009","title":"Share app group between Hub and Video App","createdAt":"2022-09-15T16:24:46Z"}
{"state":"Merged","mergedAt":"2022-02-09T23:32:00Z","number":301,"body":"Was not initializaing logs api correctly.","mergeCommitSha":"5c42bdb9766155372b52e4607990eafcc44132fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/301","title":"Fix logging","createdAt":"2022-02-09T23:22:55Z"}
{"state":"Merged","mergedAt":"2022-09-15T21:38:33Z","number":3010,"mergeCommitSha":"b3a1c1b95af76c6070e9d1f3de511800f4101737","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3010","title":"[BREAKS API ON MAIN] Remove 'allof' in API","createdAt":"2022-09-15T18:40:00Z"}
{"state":"Merged","mergedAt":"2022-09-15T22:09:38Z","number":3011,"body":"We've hit constant problems with code generation related to combine operations (allOf, oneOf, anyOf).\r\nThis pr stops that insanity via adding rules to the openapi linter.\r\n\r\nWe have two linters:\r\n1. Zally (this guy does general styling checks (i.e. do we have tags/descriptions/etc) and is casing correct. It is not extensible.\r\n2. OpenApi Linter (this guy is extensible and has some additional checks that zally doesn't do).\r\n\r\n```\r\n-------- -----\r\nSECTION COUNT\r\n-------- -----\r\nOperations   36\r\nModels     11\r\n--------------\r\nOperations\r\n--------------\r\n*ERROR* in Operation GET /teams/{teamId}/threads/mine 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/mine 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/mine 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/byPullRequests 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/byPullRequests 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/byPullRequests 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation POST /teams/{teamId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation POST /teams/{teamId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation POST /teams/{teamId}/threads 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/recommended 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/recommended 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/recommended 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/archived 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/archived 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/archived 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/search 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/search 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/search 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/{threadId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/{threadId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation GET /teams/{teamId}/threads/{threadId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/info 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/info 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation GET /teams/{teamId}/pullRequests/{pullRequestId}/info 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation PUT /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation PUT /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation PUT /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation POST /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Operation POST /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Operation POST /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Operation GET /teams/{teamId}/videoRecordings/{recordingId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/videoChannelParticipantId)\r\n*ERROR* in Operation PUT /teams/{teamId}/videoRecordings/{recordingId} 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/videoChannelParticipantId)\r\n*ERROR* in Operation PUT /teams/{teamId}/videoRecordings/{recordingId}/stop 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/videoChannelParticipantId)\r\n--------------\r\nModels\r\n--------------\r\n*ERROR* in Model 'ThreadInfo', property 'property/sourceMark', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Model 'ThreadInfo', property 'property/rank', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Model 'ThreadInfo', property 'property/unread', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Model 'PullRequestInfo', property 'property/sourceMark', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Model 'PullRequestInfo', property 'property/rank', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Model 'PullRequestInfo', property 'property/unread', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Model 'PullRequestBlock', property 'property/sourceMark', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/sourceMark)\r\n*ERROR* in Model 'PullRequestBlock', property 'property/rank', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/rank)\r\n*ERROR* in Model 'PullRequestBlock', property 'property/unread', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/unread)\r\n*ERROR* in Model 'UpdateThreadUnreadRequest', property 'property/latestReadMessage', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/latestReadMessage)\r\n*ERROR* in Model 'VideoRecording', property 'property/videoChannelParticipantId', field 'allOf' -> Using combine operators that are forbidden due to issues with openAPI code generation. (context: property/videoChannelParticipantId)\r\n\r\nFAILURE: Build failed with an exception.\r\n```","mergeCommitSha":"35b818855c899d8e7fd20c89b0732c487ad7a79b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3011","title":"Ensure we validate that openapi spec is not using nasty ops","createdAt":"2022-09-15T20:29:55Z"}
{"state":"Merged","mergedAt":"2022-09-21T23:49:17Z","number":3012,"body":"Fixes UNB-355\r\n\r\nWhen text is pasted or dropped, parse out links.  This can be extended to parse out other kinds of text in the future.  For now this is pretty simple.","mergeCommitSha":"e8675095959a1f6e252ff96978e11dff2e7d68da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3012","title":"Handle pasted links in message editor","createdAt":"2022-09-15T21:05:02Z"}
{"state":"Merged","mergedAt":"2022-09-16T21:37:37Z","number":3013,"body":"* Add `slack` ThreadType\r\n* Add `slack` property onto `ThreadInfo`, which provides slack metadata when a thread originates from Slack.  I just put the channel name onto this for now.\r\n* Add `getThreadsForCommit` API call.  This returns a series of `ThreadInfo`s for a given commit.\r\n\r\nPending questions:\r\n* What kind of pagination/continuation do we need for `getThreadsForCommit`?","mergeCommitSha":"5e8eb4883daa05b220d221ab1b4acfb344e730a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3013","title":"[BREAKS API ON MAIN] Slack threads API proposal","createdAt":"2022-09-15T21:52:06Z"}
{"state":"Merged","mergedAt":"2022-09-16T23:55:44Z","number":3014,"body":"Adds restore discussion banner to VSCode threads.\r\n\r\n\r\n<img width=\"800\" alt=\"CleanShot 2022-09-15 at 15 11 53@2x\" src=\"https://user-images.githubusercontent.com/1553313/190517795-7f1759ac-40fa-4b4c-8fc3-833e289932f4.png\">\r\n\r\n\r\nDependant on https://github.com/NextChapterSoftware/unblocked/pull/2975 for banner content","mergeCommitSha":"92ea5eda9c8b55dad8d7e15c55b8a333c43bc215","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3014","title":"Jeff/unb 647 restore discussion on vscode","createdAt":"2022-09-15T22:12:18Z"}
{"state":"Merged","mergedAt":"2022-09-15T22:18:38Z","number":3015,"mergeCommitSha":"80eacfd8c7eeb48ada9f8c710a312329e23ed099","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3015","title":"Matt should be made aware that his changes are nice","createdAt":"2022-09-15T22:16:31Z"}
{"state":"Merged","mergedAt":"2022-09-15T22:17:14Z","number":3016,"mergeCommitSha":"850faf41cee411efd76c277d796f53e730cf966b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3016","title":"Fix test","createdAt":"2022-09-15T22:16:48Z"}
{"state":"Merged","mergedAt":"2022-09-15T22:27:51Z","number":3017,"mergeCommitSha":"06b8e3b30cf7df9977a05ac7978a703d00f59bd6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3017","title":"APICompatTest can include breaking change text anywhere in the message","createdAt":"2022-09-15T22:17:59Z"}
{"state":"Merged","mergedAt":"2022-09-15T22:50:26Z","number":3018,"body":"Server is already doing the right thing (200). Spec was wrong.\n\nNo client impact.","mergeCommitSha":"092ae317fa3d8c135403f0334093b023b91cd232","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3018","title":"Correct API spec thread API response codes [BREAKS API ON MAIN]","createdAt":"2022-09-15T22:40:43Z"}
{"state":"Merged","mergedAt":"2022-09-16T19:07:05Z","number":3019,"body":"Just add skeleton for slack libs. No code.","mergeCommitSha":"97d48ead41f53ecf834bc02838dea25963ca55a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3019","title":"Add libs","createdAt":"2022-09-16T18:48:16Z"}
{"state":"Merged","mergedAt":"2022-02-10T00:10:54Z","number":302,"body":"…sert on nullable fields","mergeCommitSha":"b4ecf2caa43db7a51c0682b76beff62e0c512172","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/302","title":"Bug with our config loader means that strict mode will incorrectly assert on nullable fields","createdAt":"2022-02-10T00:00:04Z"}
{"state":"Merged","mergedAt":"2022-09-16T22:09:01Z","number":3020,"body":"Mark sourcemarks as archived or deleted as appropriate if their related thread is also archived or deleted.\r\n\r\nTODO\r\n- [x] refactor query to store\r\n- [x] tests for above","mergeCommitSha":"0c6f9c8a94780b6a4ff1bd7152909e36746dee76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3020","title":"Backfill to mark sourcemarks as archived or deleted","createdAt":"2022-09-16T19:16:44Z"}
{"state":"Merged","mergedAt":"2022-09-19T21:39:02Z","number":3021,"body":"- Refactored workflows to reduce repetition in workflow definition \r\n- Took the image build step out of deploy workflow \r\n- Added support for environments and concurrency models to address concurrent Kube deployments \r\nThis might break CI but unfortunately there's no way for me to test the deployment portion without a merge to main. ","mergeCommitSha":"fb1347d91001585e6cc632e8f9b38258a7e52641","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3021","title":"Environment support for Kube deployments ","createdAt":"2022-09-16T20:02:18Z"}
{"state":"Merged","mergedAt":"2022-09-21T19:57:09Z","number":3022,"body":"Extension button now toggles sidebar.\r\nCurrently somewhat hidden to test out feature. By default, sidebar is still rendered.\r\n\r\nAlso includes unread count within badge. As part of this work, refactored out common sidebar streams for both VSCode + Web Extension.\r\n\r\n\r\n<img width=\"442\" alt=\"CleanShot 2022-09-19 at 14 59 38@2x\" src=\"https://user-images.githubusercontent.com/1553313/191127458-00273d2b-4d69-4fb6-9ed2-d498c00a0c5e.png\">\r\n<img width=\"637\" alt=\"CleanShot 2022-09-19 at 14 59 33@2x\" src=\"https://user-images.githubusercontent.com/1553313/191127464-ba156557-c540-4ed2-bfb6-fc3f398be358.png\">\r\n\r\n\r\n","mergeCommitSha":"49c68e2bcb157b18f81bb5495c51ab7b95fee642","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3022","title":"Toggle web extension sidebar","createdAt":"2022-09-16T20:35:24Z"}
{"state":"Merged","mergedAt":"2022-09-16T22:00:22Z","number":3023,"body":"Only a couple places that needed fixing.","mergeCommitSha":"3b1ec4eedeea3b2bc868bea004390aa0d7da25e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3023","title":"Clients should tolerate unknown API enum values","createdAt":"2022-09-16T21:14:27Z"}
{"state":"Merged","mergedAt":"2022-09-16T21:43:42Z","number":3024,"body":"Safari web extension now sends proper product agent.\r\n\r\nDownside, requires two webpack builds during CI now but I think that's unavoidable?\r\n\r\n<img width=\"681\" alt=\"CleanShot 2022-09-16 at 14 35 57@2x\" src=\"https://user-images.githubusercontent.com/1553313/190790568-6ec1a7f0-0e24-4de9-a40f-38e7bccd1826.png\">\r\n","mergeCommitSha":"5ff3f1d1687ae13b73e71b8817b351705518b539","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3024","title":"Update safari build scripts","createdAt":"2022-09-16T21:37:36Z"}
{"state":"Merged","mergedAt":"2022-09-19T17:04:15Z","number":3025,"body":"As discussed.  We don't need to build packages for VSCode builds, as they're built as part of the Installer build.  This will speed up VSCode PR builds and `main` builds.\r\n\r\nThe existing Build step already builds and tests.","mergeCommitSha":"13489af20f427afaece04795b061fb9137e6fca0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3025","title":"Remove Package step from VSCode CI builds","createdAt":"2022-09-16T22:50:36Z"}
{"state":"Merged","mergedAt":"2022-09-17T02:19:21Z","number":3026,"body":"### Problem\r\nThe cache keys for the SourceMark Git LRU cache are not small, because the key is literally the git command string. For a large repo, this adds up.\r\n\r\n### Solution\r\nHash the command strings, and encode as binary string to pack efficiently.\r\n\r\n### Risks\r\n- Insignificant risk that a collision will occur. Weird shit would happen in this case.\r\n- CPU time taken by hash function might negatively impact performance.\r\n\r\n\r\nhttps://linear.app/unblocked/issue/UNB-665/compress-sourcemark-lru-data","mergeCommitSha":"bdad35dc5c3c50bd0982280eccd61a6f3211b5a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3026","title":"Compress sourcemark LRU cache keys","createdAt":"2022-09-17T00:01:49Z"}
{"state":"Merged","mergedAt":"2022-09-18T21:58:36Z","number":3027,"mergeCommitSha":"1e265c57fc43b25fb25f39b36477dda9d1bbadee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3027","title":"Move scm file","createdAt":"2022-09-18T21:58:26Z"}
{"state":"Merged","mergedAt":"2022-09-18T22:01:34Z","number":3028,"body":"This reverts commit 1e265c57fc43b25fb25f39b36477dda9d1bbadee.\r\n","mergeCommitSha":"fc90d743365f8ea329aad503802ab0df99c0b26b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3028","title":"Revert \"Move scm file (#3027)\"","createdAt":"2022-09-18T22:01:20Z"}
{"state":"Merged","mergedAt":"2022-09-18T22:45:52Z","number":3029,"mergeCommitSha":"8eeba748b2d207beb4b5e5dcb5b5fa828e10e8cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3029","title":"Fix naming for our internal slack stuff","createdAt":"2022-09-18T22:35:05Z"}
{"state":"Merged","mergedAt":"2022-02-15T23:50:07Z","number":303,"body":"This adds two store wrappers to facilitate polling / fetching / caching data stores:\r\n\r\n`createDataCacheStore` -- this is the equivalent of `createVanillaStore`, this creates a non-react store based on a set of traits.  The store begins polling immediately when created.  The returned object has the same API as `createVanillaStore`, but when you call `store.destroy()` polling stops.\r\n\r\n`useDataCacheStore` -- this is the equivalent of `create` and `useStore` in a single hook.  This creates a store based on a set of traits.  The store begins polling immediately when created, and is stopped when the view is unmounted.  Both a sliced and unsliced variant of the function are available.","mergeCommitSha":"723eb7c866a351310500b909094f72f0fd01bab6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/303","title":"Add DataCacheStore and tests","createdAt":"2022-02-10T00:10:12Z"}
{"state":"Merged","mergedAt":"2022-09-24T00:11:37Z","number":3030,"body":"The IPC service is implemented but currently leverages a dummy publisher to yield responses. All requests to the service will yield a dummy response and have no practical effect. \r\n\r\nThe next PR will implement the Hub-side.\r\n\r\nIPC setup is as follows:\r\n\r\n- Service boots up\r\n- Service drops the IPC port to `~/Library/Group\\ Containers/2FNXZ6K9M4.com.nextchaptersoftware.shared/Library/Preferences/2FNXZ6K9M4.com.nextchaptersoftware.shared.plist`\r\n- Client must sniff this port directly with `defaults read ~/Library/Group\\ Containers/2FNXZ6K9M4.com.nextchaptersoftware.shared/Library/Preferences/2FNXZ6K9M4.com.nextchaptersoftware.shared` or similar","mergeCommitSha":"f53868487b64a08cdb500a13564c1dde90d31f2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3030","title":"Video app IPC service implementation","createdAt":"2022-09-19T18:22:20Z"}