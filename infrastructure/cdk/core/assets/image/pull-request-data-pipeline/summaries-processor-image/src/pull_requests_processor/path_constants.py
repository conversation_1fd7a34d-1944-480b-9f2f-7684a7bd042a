import os

# AWS
PROCESS_INPUT_DIRECTORY = "/opt/ml/processing/inputs/s3/input"
if os.getenv("PROCESS_ENV") == "local":
    # Local
    PROCESS_INPUT_DIRECTORY = os.path.join(os.path.dirname(__file__), "test", "unit", "fixtures", "glue1")

PROCESS_INPUT_GLOB = "{}/run*".format(PROCESS_INPUT_DIRECTORY)

# In AWS
PROCESS_OUTPUT_DIRECTORY = "/opt/ml/processing/outputs/s3/output"
if os.getenv("PROCESS_ENV") == "local":
    # Local
    PROCESS_OUTPUT_DIRECTORY = "/tmp/unzipped"
    if not os.path.exists(PROCESS_OUTPUT_DIRECTORY):
        os.mkdir(PROCESS_OUTPUT_DIRECTORY)
