{"state":"Merged","mergedAt":"2022-05-04T03:32:17Z","number":1125,"body":"- server responds with request ID as `X-Request-ID` header\r\n- if client does not pass `X-Request-ID` header, then server generates one.\r\n\r\n\r\nExample of server generated ID:\r\n\r\n\r\n<img width=\"409\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/166411214-63f584cc-6c1b-48a3-979b-28d57d3f4e00.png\">\r\n","mergeCommitSha":"1b94297d8bbe361df57fd542e4426791713f9431","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1125","title":"Add X-Request-ID to api service","createdAt":"2022-05-03T06:15:21Z"}
{"state":"Closed","mergedAt":null,"number":1126,"body":"ignore","mergeCommitSha":"c061bebe294ab078d441612d75be31071c4fc76c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1126","title":"Test CI","createdAt":"2022-05-03T16:12:05Z"}
{"state":"Merged","mergedAt":"2022-05-03T17:21:05Z","number":1127,"mergeCommitSha":"bd87bc7c421d6222aec71636ae4a283657def49f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1127","title":"Use correct repo in multi-repo VSCode workspaces","createdAt":"2022-05-03T17:11:16Z"}
{"state":"Closed","mergedAt":null,"number":1128,"mergeCommitSha":"21161f358d0ae10f997fa71d6d0e26fddb6675e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1128","title":"incident-00000001-rca-do-NOT-merege","createdAt":"2022-05-03T17:11:28Z"}
{"state":"Closed","mergedAt":null,"number":1129,"mergeCommitSha":"c74467fafc8d9c252712df1f8aee15a1412ce189","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1129","title":"Reverting","createdAt":"2022-05-03T17:26:01Z"}
{"state":"Merged","mergedAt":"2022-01-24T21:47:50Z","number":113,"mergeCommitSha":"fea72bbb32377e4d7acd1eaaa27ac1d13a01f043","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/113","title":"Componentize deployment of steps to environment","createdAt":"2022-01-24T21:33:56Z"}
{"state":"Merged","mergedAt":"2022-05-03T18:22:21Z","number":1130,"mergeCommitSha":"a4dc98c8ba9310980b17caccfeac71b79ae2da0b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1130","title":"Fix underline","createdAt":"2022-05-03T18:12:00Z"}
{"state":"Merged","mergedAt":"2022-05-03T18:50:42Z","number":1131,"body":"- Added more CPU to api service\r\n- Changed API service count in Dev to 2 (to avoid interruptions since we are using it for demos and testing)\r\n- Added more memory to SCM service to address crashloops\r\n- Changed deployment method to allow 50% unavailable\r\n- Regenerated helm charts","mergeCommitSha":"df86a4393193b5aad10f3da9e1d8d467f4d282fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1131","title":"Right size services","createdAt":"2022-05-03T18:20:37Z"}
{"state":"Merged","mergedAt":"2022-05-03T20:11:52Z","number":1132,"body":"Fixes #1121\r\n\r\nRemoves hard coded TEAM_ID values in the web.\r\nFollowing similar pattern to other clients where we grab the list of relevant teams and using those teams, fetch the relevant repositories.","mergeCommitSha":"2be44c5adc12027d4f3e352cd63fad11db2bd5d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1132","title":"Remove TeamID with AllReposStore for Web","createdAt":"2022-05-03T18:28:17Z"}
{"state":"Merged","mergedAt":"2022-05-03T21:51:08Z","number":1133,"mergeCommitSha":"ec4931696040f54dd676956773524b2d671836af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1133","title":"Fix duplicate person issue","createdAt":"2022-05-03T18:43:50Z"}
{"state":"Merged","mergedAt":"2022-05-03T20:38:55Z","number":1134,"body":"The `onClose` callback was not being invoked when the DialogHeader button was being clicked because the callback wasn't being passed through into the component, resulting in the hash not clearing properly\r\n<img width=\"1290\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/166520792-9cb72ef3-5e26-456f-9295-6e7493e44612.png\">\r\n\r\nInstead, of defining the callback in the dialog component, we could set the callback into a state and then call it close in one place \r\n\r\nNOTE: Because we're setting a function into the state, we have to add an extra layer of functional wrapping so that the cb is not called immediately - but we only have to do this in one place\r\n","mergeCommitSha":"91cdf145fa719fc0bdd668742ecdc1a3b5d54b1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1134","title":"Fix dialog close with callback","createdAt":"2022-05-03T18:44:37Z"}
{"state":"Merged","mergedAt":"2022-05-03T19:48:53Z","number":1136,"body":"We were not handling empty environment variable dictionaries properly. This was breaking search service deployments. ","mergeCommitSha":"64d3b453e6b704cc9baab318a03524d756ab90ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1136","title":"handle empty env vars in helm","createdAt":"2022-05-03T19:38:41Z"}
{"state":"Merged","mergedAt":"2022-05-03T20:01:01Z","number":1137,"body":"Spoke with Richie, he feels single() use does have its purposes but singleOrNull is evil.","mergeCommitSha":"fcd4d52fa25d063f476ebb5cc43cb3f85f0c1d10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1137","title":"Address singleOrNull bugs by forbidding them via linter rule","createdAt":"2022-05-03T19:46:31Z"}
{"state":"Merged","mergedAt":"2022-05-04T17:08:16Z","number":1138,"body":"Refactor Dropdown into its own component and add styling for each client:\r\n\r\nweb extension:\r\n<img width=\"355\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/166554769-77a91ec1-f5d5-46a1-b4be-d34e0f28f15f.png\">\r\n\r\nvscode:\r\n<img width=\"327\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/166554914-b03a4bbf-7561-4ff2-a7b3-094f85fc4e00.png\">\r\n\r\nweb dashboard (no concrete designs for this yet, leave as simple box):\r\n<img width=\"242\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/166554989-f1a9291b-2bd2-4413-84d9-c74812413d4b.png\">\r\n","mergeCommitSha":"3fe5a72c67eecd6c5734087c5d3ee3dc26ba1421","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1138","title":"Dropdown component/styling","createdAt":"2022-05-03T19:52:47Z"}
{"state":"Merged","mergedAt":"2022-01-24T23:09:30Z","number":114,"body":"Adds these with tests\r\n- RepoModel\r\n- SourceMarkAccessModel\r\n- SourceMarkModel\r\n- SourcePointModel\r\n- SourceVectorModel\r\n\r\n<img width=\"1639\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/150880011-24a8417d-c7c1-4cb0-a0c9-fd49cdccd7e6.png\">\r\n","mergeCommitSha":"c6331719d56103c7db88f9803263ba7a10b8240d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/114","title":"Introduce Source Mark models","createdAt":"2022-01-24T22:40:20Z"}
{"state":"Merged","mergedAt":"2022-05-03T21:46:20Z","number":1141,"body":"Apologies for the hackyness \r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1651608699082479?thread_ts=**********.643029&cid=C02HEVCCJA3","mergeCommitSha":"fb38761819f817fa0ac9e144438490c921bd933a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1141","title":"Disable redis health check for search service","createdAt":"2022-05-03T20:46:06Z"}
{"state":"Closed","mergedAt":null,"number":1142,"mergeCommitSha":"e689e9ce49ebf7835925afba6f9b2cc81243e75b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1142","title":"Spots for everyone!","createdAt":"2022-05-03T21:50:36Z"}
{"state":"Merged","mergedAt":"2022-05-06T23:18:14Z","number":1143,"body":"Setup Safari CI to generate a development package.\r\n\r\nMay require one to allow apps from unsigned developers in system preferences.","mergeCommitSha":"5a7fa11a3a6045f8f8c859aa86d68cc9a44a9d00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1143","title":"Update Safari archive CI","createdAt":"2022-05-03T22:38:37Z"}
{"state":"Merged","mergedAt":"2022-05-03T23:36:54Z","number":1144,"body":"Used the incorrect type of button...\r\n\r\nUsing accessory button which doesn't require additional backend work.","mergeCommitSha":"a9ee4fc0675a355024bf6d9ac0ce37e517fcb683","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1144","title":"Fix slack button","createdAt":"2022-05-03T23:24:29Z"}
{"state":"Merged","mergedAt":"2022-05-04T04:05:32Z","number":1145,"body":"Motivation for _completely_ ignoring whitespace is to account for code equivalence.\r\n\r\nFor example, this:\r\n```c\r\n    if ( a == \"\" ) { return; }\r\n```\r\nMeans the same thing as this in most languages:\r\n```c\r\nif(a==\"\"){return;}\r\n```\r\n\r\nWill be used in follow up change.","mergeCommitSha":"fed9f2ef666a4e8046d3e680a24ec0c11d4d3667","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1145","title":"Ignore all whitespace in FuzzyCompare","createdAt":"2022-05-04T00:46:09Z"}
{"state":"Merged","mergedAt":"2022-05-04T05:23:31Z","number":1146,"mergeCommitSha":"5455506e5d30c714f3258b95d7d06695e5a05bd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1146","title":"Ignore space when detecting moves","createdAt":"2022-05-04T01:12:13Z"}
{"state":"Merged","mergedAt":"2022-05-04T04:09:50Z","number":1147,"mergeCommitSha":"6e9caa50162360b4c82e957eac127ab648aeec25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1147","title":"Stop using default exception handlers","createdAt":"2022-05-04T03:16:37Z"}
{"state":"Merged","mergedAt":"2022-05-04T04:26:04Z","number":1148,"body":"Resurrected from the crypt","mergeCommitSha":"e6cca284084bd193f72689897663ad759dc7f0bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1148","title":"Stop using default exception handlers","createdAt":"2022-05-04T04:15:16Z"}
{"state":"Merged","mergedAt":"2022-05-04T07:16:16Z","number":1149,"mergeCommitSha":"e65f227b52a31acbd3b781a3f8a9c10d72fdbe49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1149","title":"SourceMark engine tracks moves across files","createdAt":"2022-05-04T05:43:05Z"}
{"state":"Merged","mergedAt":"2022-01-24T23:01:38Z","number":115,"body":"We are now adding per environment helm overrides.","mergeCommitSha":"e94c906e777500fee88fe97e96a0db9930825ca3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/115","title":"Clean up helm deployment to make it per environment...","createdAt":"2022-01-24T22:45:47Z"}
{"state":"Merged","mergedAt":"2022-05-04T07:24:03Z","number":1150,"mergeCommitSha":"b70a875b7f00612f016f7f8143fcee8f859bc79e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1150","title":"SourceMark engine accounts for fuzzy moves","createdAt":"2022-05-04T07:03:52Z"}
{"state":"Merged","mergedAt":"2022-05-04T18:40:26Z","number":1151,"body":"Creating a search endpoint stub so that we can start implementing the frontend for Search V1. \r\n\r\nSearch V1 lists search results as a list of threads, so this endpoint returns thread IDs.\r\n\r\nI've modelled it after [GitHub's search API](https://docs.github.com/en/rest/search#search-issues-and-pull-requests) where it's a GET operation with the search query as a query parameter `q`. This stubbed endpoint will search thread titles only. Follow up PR will add the actual search logic.\r\n\r\nThis operation is not paginated but probably should be. I can include in this PR if that is easier.","mergeCommitSha":"f7b162a8fd5da7926876e728d5d52223a508fb91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1151","title":"Create search threads operation","createdAt":"2022-05-04T07:13:50Z"}
{"state":"Merged","mergedAt":"2022-05-04T15:39:35Z","number":1152,"body":"Invalid JSON from copy","mergeCommitSha":"61c6db0298ae4aa7e73b005aff0eba7b6e83ecaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1152","title":"FormatJSON","createdAt":"2022-05-04T15:23:48Z"}
{"state":"Merged","mergedAt":"2022-05-04T16:27:19Z","number":1153,"mergeCommitSha":"33c7fb828ff4be767e045db815803946ee3d67bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1153","title":"resize instance and use our own fork of the plugin for added security","createdAt":"2022-05-04T16:09:25Z"}
{"state":"Merged","mergedAt":"2022-05-04T18:02:09Z","number":1154,"body":"Sorry Pete. Seems very similar to https://github.com/NextChapterSoftware/unblocked/pull/1061\r\n\r\n- https://chapter2global.slack.com/archives/C031MKRPZSQ/p1651638697864359","mergeCommitSha":"a796df89819b9655191189b1004d7cbb4af1721c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1154","title":"Disable flaky test","createdAt":"2022-05-04T17:08:42Z"}
{"state":"Merged","mergedAt":"2022-05-04T21:19:46Z","number":1156,"body":"Link in dropdown was broken -- need to render actual anchor elements and typecast the matching props into the elements","mergeCommitSha":"1aeed0f4ae6165712e9fa8093ca54b5c44b9da8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1156","title":"Fix dropdown elements","createdAt":"2022-05-04T19:50:24Z"}
{"state":"Merged","mergedAt":"2022-05-05T18:13:12Z","number":1157,"body":"* Add NotificationService to send invite emails to non UB users on thread creation \r\n* Email content right now is just raw text, will iterate on\r\n* All emails will only be sent to `<EMAIL>` for now, I want to test what the email actually looks like before blasting to the rest of the team (sending actual emails can't be tested locally)","mergeCommitSha":"09f3274384d50465c554f67e7e74d62b67312426","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1157","title":"Configure to send emails to non UB users","createdAt":"2022-05-04T22:27:22Z"}
{"state":"Merged","mergedAt":"2022-05-06T18:01:11Z","number":1158,"body":"- Create empty noop Monkey service\r\n- Create helm charts with an internal dedicated ALB endpoint for the Monkey\r\n- Create SSL certs and cnames for the Monkey\r\n\r\nNote: this service is currently noop. We will add the logic to it soon. ","mergeCommitSha":"3058e66ad593fde6c3607fd82e57d2df59439ae2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1158","title":"Adding Monkey service (my favorite service so far)","createdAt":"2022-05-04T22:44:09Z"}
{"state":"Merged","mergedAt":"2022-05-04T23:22:55Z","number":1159,"body":"Note: not touching the GitHubInstallationMaintenanceJob which is already at 10s interval. We can adjust that one next if we need to.\r\n\r\nhttps://getunblocked.grafana.net/d/eIvzXO_7k/service-pod-resources-overview?orgId=1&from=now-3d&to=now&var-datasource=grafanacloud-getunblocked-prom&var-cluster=dev-us-west-2&var-namespace=default&var-service=scmservice&viewPanel=4","mergeCommitSha":"7ba00b661a4bea39c5aa934ab92ede03b3f7643d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1159","title":"Temporarily reduce background polling interval on SCM service","createdAt":"2022-05-04T23:10:20Z"}
{"state":"Merged","mergedAt":"2022-01-24T23:12:27Z","number":116,"mergeCommitSha":"6a44cbe2ee2ee1cfb05459250a7143094c99a1d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/116","title":"Refactor suspendedTransactionWithLogger to use outside test package","createdAt":"2022-01-24T23:04:27Z"}
{"state":"Merged","mergedAt":"2022-05-05T17:53:53Z","number":1160,"body":"This wires up the correct auth headers, and includes a wrapper to handle responses with the last modified header. Comments inline that describe specifics about intended usage and where we're going next.\r\n\r\nI decided _not_ to override `execute` for logging. We don't have access to request headers at that stage so what we log is a bit limited. Instead we can get that information from CFNetwork debugging or via Xcode's HTTP tool. ~One thing I will add next is the x-b3-traceId header so we can see request data flow all the way through~ done ✅","mergeCommitSha":"1ed400c555697596de8d73ad91811af37fe37ae8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1160","title":"Custom request builder","createdAt":"2022-05-04T23:17:18Z"}
{"state":"Merged","mergedAt":"2022-05-05T16:03:40Z","number":1161,"body":"Fix ugly overflow \r\n<img width=\"278\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/166844792-aef47bee-4b46-427c-9c79-1a9f58feedfb.png\">\r\n","mergeCommitSha":"b4f46078e56c57768898df8723f9d5749429eb57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1161","title":"Fix some display issues","createdAt":"2022-05-05T00:05:05Z"}
{"state":"Merged","mergedAt":"2022-05-05T00:15:12Z","number":1162,"mergeCommitSha":"f7c370ab7eec073e8700ac03251438b85ca392e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1162","title":"fix docker file","createdAt":"2022-05-05T00:14:36Z"}
{"state":"Merged","mergedAt":"2022-05-05T18:05:24Z","number":1163,"body":"Quick change to filter on threads the user belongs to","mergeCommitSha":"1dba38ec1db96f68f0225591dfdd07664b580175","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1163","title":"Show mine threads only","createdAt":"2022-05-05T00:25:48Z"}
{"state":"Merged","mergedAt":"2022-05-09T22:57:33Z","number":1164,"body":"**BACKGROUND**:\r\nOriginally wrote my own, but I found a cleaner and non-opinionated library that we're now using to add list-block functionality. It allows us to provide our types via an interface.\r\n\r\nhttps://github.com/prezly/slate/tree/master/packages/slate-lists\r\n\r\nThis implementation also has a ton of useful slatejs common utilities that we should consider using in the future:\r\nhttps://github.com/prezly/slate/tree/master/packages/slate-commons/src/commands\r\n\r\n**CHANGES**\r\n1. Define custom list block types:\r\n- ListItemElement\r\n- ListItemTextElement\r\n- OrderedListElement\r\n- UnorderedListElement\r\n2. Define Schema that specifies our types and passes that to the library which will resultantly use our types in generating nodes.\r\n3. Wrap our editor with lists functionality via a callback withLists. This adds normalizations that we need to ensure everything looks right.\r\n5. Add a boolean to ElementTraits to indicate these new types are plugin types and they should not be normalized via Unblocked handlers. The library's normalization utilities will handle all edge cases for lists blocks.\r\n6. Upgrade our slate versions.\r\n7. Move MessageEditorToolbar into own react component.\r\n8. Make BlockButton behaviour more configurable.\r\n9. Add serialization\r\n\r\n**PROOF THIS WORKS**\r\n![CleanShot 2022-05-05 at 13 17 17](https://user-images.githubusercontent.com/3806658/167018562-b7386513-28fa-4aef-9a9f-4123274bcdc4.gif)\r\n\r\n\r\n![CleanShot 2022-05-04 at 19 53 50](https://user-images.githubusercontent.com/3806658/166858151-45d1058f-aced-4db9-9f40-cf4aaa348ce9.gif)\r\n![CleanShot 2022-05-04 at 19 51 16](https://user-images.githubusercontent.com/3806658/166858163-1733d050-6a25-43ab-9b65-11cfc3b83d18.gif)\r\n\r\n","mergeCommitSha":"617d88e748d1989ae134d913f1a4a35f60212126","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1164","title":"Add slatejs handling for ordered and unordered blocks.","createdAt":"2022-05-05T02:54:58Z"}
{"state":"Merged","mergedAt":"2022-05-05T18:38:44Z","number":1165,"body":"Create `prod` builds for all apps.\r\n\r\nAll client apps have a consistent way to determine which environment to use:\r\n1. If the build explicitly specifies an environment, use that.  This is used for local (developer) builds, so we target and run builds against the correct environment when debugging.\r\n2. If there is an UNBLOCKED_DEV environment variable override, use that.  This is for VSCode.\r\n3. If we are in a browser, and the browser URL matches a dashboard URL, use that.  This allows the web dashboard to use the correct environment for whatever host it's running from.\r\n4. Fail over to prod.\r\n\r\nSome caveats:\r\n* We still don't know how to override the environment for the web extension.  Web extensions are browser-based and can't read environment variables.  We're looking into local storage.\r\n* We still don't have a way of running the dashboard against dev or prod.  This is complicated because of login redirects.","mergeCommitSha":"75c7e6ed7f2607477b6807630dee460e182020b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1165","title":"Build prod environment clients","createdAt":"2022-05-05T03:03:44Z"}
{"state":"Merged","mergedAt":"2022-05-05T18:34:02Z","number":1166,"mergeCommitSha":"1143b3607f1654a9819b6812218bae7b1252d2f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1166","title":"Enable PR ingestion in prod","createdAt":"2022-05-05T16:27:07Z"}
{"state":"Merged","mergedAt":"2022-05-05T17:49:48Z","number":1167,"mergeCommitSha":"253ebf52237aa895bb8c8ca245c8a66ac8048aa6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1167","title":"Remove perf-related indexes and make columns non-nullable","createdAt":"2022-05-05T17:02:18Z"}
{"state":"Merged","mergedAt":"2022-01-26T00:13:53Z","number":117,"mergeCommitSha":"7a33a312b5b724c2e5eddf805917922280d21fe1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/117","title":"Setup VSCode Chromatic","createdAt":"2022-01-24T23:42:10Z"}
{"state":"Merged","mergedAt":"2022-05-05T21:19:46Z","number":1170,"body":"Disabling Keep Alive will allow us to specify the hosts.\r\n\r\nThere's a risk that background service workers for extension gets cleaned up so need to keep an eye on that. Cache should still make the UX seamless though...\r\n\r\nThis will scope the scary dialogs from Safari to just GH.com","mergeCommitSha":"3f2fc6c87a771c6f3d3cbfeab4ab3cb4ecfb2475","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1170","title":"Disable Keep Alive","createdAt":"2022-05-05T18:02:24Z"}
{"state":"Merged","mergedAt":"2022-05-05T18:24:37Z","number":1171,"mergeCommitSha":"24aea585e378fd0ba38cd395c5d63704f55e519f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1171","title":"allow vpn access to prod db while we prepare it for dogfooding","createdAt":"2022-05-05T18:12:31Z"}
{"state":"Merged","mergedAt":"2022-05-05T18:47:04Z","number":1172,"body":"This modifies godmode so that it only uses identities that already exist keyed off the external user ID. ","mergeCommitSha":"f496122432fdaeaf222ae2a600e3bc8d6042465f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1172","title":"Make admin auth safe","createdAt":"2022-05-05T18:14:45Z"}
{"state":"Merged","mergedAt":"2022-05-05T18:32:30Z","number":1173,"mergeCommitSha":"50e6f7aa2a73d1e1f3730c2e9aa5e487b51a8e16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1173","title":"Update hub admin auth","createdAt":"2022-05-05T18:22:35Z"}
{"state":"Merged","mergedAt":"2022-05-05T19:53:01Z","number":1174,"body":"Added write only perm on serch indexing queue to scm service account in dev and prod.\r\nChanges have been deployed to both environments.","mergeCommitSha":"b5f5b1daccbcfd9e111565df20dc0232a675f5a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1174","title":"Add producer permission on search_indexing queue to scm service","createdAt":"2022-05-05T19:50:59Z"}
{"state":"Closed","mergedAt":null,"number":1176,"body":"We're only using token prefix for VSCode.\r\n\r\nThis wasn't a problem when we just had localhost & dev but the introduction of prod is leading to some clashes due to the similar domain suffix.\r\n\r\n","mergeCommitSha":"631e9955c1e650247fbac74040d7cbb36b8e2c13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1176","title":"Add token prefix to all token providers","createdAt":"2022-05-05T20:19:37Z"}
{"state":"Merged","mergedAt":"2022-05-06T18:02:00Z","number":1177,"body":"Had ArrayExtensions within VSCode that would be useful elsewhere.\r\n\r\nConsolidated into ArrayUtils under shared utils.\r\n","mergeCommitSha":"86abc4dfaee3b99acc1144ab885e2f0d1d3189d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1177","title":"Consolidate ArrayHelpers","createdAt":"2022-05-05T21:15:06Z"}
{"state":"Merged","mergedAt":"2022-05-06T22:02:05Z","number":1178,"mergeCommitSha":"6dd285f2e8c582b20190fedf8866d4ce65c1b61d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1178","title":"Index message body and thread titles","createdAt":"2022-05-05T22:34:26Z"}
{"state":"Merged","mergedAt":"2022-05-06T00:00:15Z","number":1179,"body":"- Do not crash when failing to render SM snippet, from https://github.com/NextChapterSoftware/unblocked/issues/1175#issuecomment-**********\r\n- Expose Initial PR Ingestion Complete on Repos Page\r\n- remove spam logs","mergeCommitSha":"edecc52b28d3f25ef654f03963ef41a344a03f71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1179","title":"Do not crash when failing to render SM snippet","createdAt":"2022-05-05T23:47:13Z"}
{"state":"Merged","mergedAt":"2022-01-27T18:59:02Z","number":118,"body":"Some setup for web client.\r\n\r\nThere are some incorrect API calls while the backend is in flight.\r\nHave bypassed parts of authentication to demo flow.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/151036134-c1536f71-c127-45e1-b4a2-2a0bbaa90069.mp4\r\n\r\n\r\n","mergeCommitSha":"4a7fd15220c89b56a7badca8d95c88ae932dfa53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/118","title":"Setup Basic Auth for web with mocked endpoints","createdAt":"2022-01-24T23:43:33Z"}
{"state":"Merged","mergedAt":"2022-05-06T03:19:40Z","number":1180,"body":"## Summary\r\n\r\nBefore this change we weren't sending back the `X-Unblocked-Last-Modified` header when the size of the result collection was 0. This is not correct behaviour because APIs with \"modified since\" semantics are expected to behave like streams, meaning it should not be possible for a value to be inserted with a `modifiedAt` value in the past. \r\n\r\nClients should therefore treat the API result as a correct representation.\r\n\r\nI've also noticed some inconsistencies in how we're dealing with empty responses in the service. `getThreadMessages` for example will send back a 404 if it can't find any new messages, whereas `getThreads` will happily return an empty collection.\r\n\r\nNot sure which style is better, but we should try to be consistent\r\n\r\n## Also\r\n\r\nI added a push channel for `/repos` since it was the only one not implemented according to the spec. ","mergeCommitSha":"f8f2036d34dfcadd985acdf9c39ab660b8a32673","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1180","title":"Echo modified since request parameter if result size is 0","createdAt":"2022-05-06T01:55:05Z"}
{"state":"Merged","mergedAt":"2022-05-09T22:51:11Z","number":1181,"body":"This is the necessary precursor for push updates to the thread list. Thread and Unreads `lastModified` values are cached, and new results are correctly merged, ordered, and limited with previous results.","mergeCommitSha":"e94aa76e83dc38d1b66bc1daeff3cb0bf4daf926","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1181","title":"Use last modified in hub","createdAt":"2022-05-06T03:21:40Z"}
{"state":"Merged","mergedAt":"2022-05-06T04:00:43Z","number":1182,"body":"Ktor HttpClients hold onto native resources that must be explicitly closed.\r\nThis is one source of memory leaks for SCM service.\r\nThe main indicator was the Non-Heap memory increase over time as I was monitoring the SCMService.\r\n\r\nThe other major concern, even after this fix, is that I'm still noticing leaks when creating Ktor clients.\r\nMy fear is that there's an underlying leak in the Ktor HttpClient even post close. :(\r\nDebugging native memory allocations is nigh impossible in java land.\r\n\r\n<img width=\"1143\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/167063071-254a66d2-e156-45c8-9c56-ada766ceaf16.png\">\r\n","mergeCommitSha":"69a7831dd19052f1b8abf06d70a541d33b691ff9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1182","title":"Ktor HttpClients must be closed","createdAt":"2022-05-06T03:35:39Z"}
{"state":"Merged","mergedAt":"2022-05-06T03:44:20Z","number":1183,"mergeCommitSha":"ebad06aa4b84ab6dcfa9699b5c01c90170e06dd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1183","title":"Update CIDR list for me and david","createdAt":"2022-05-06T03:43:59Z"}
{"state":"Merged","mergedAt":"2022-05-06T04:24:26Z","number":1184,"mergeCommitSha":"d9347feb5ae63f75a168b85e15171f6d74fea39a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1184","title":"Update dennis cidr","createdAt":"2022-05-06T04:24:09Z"}
{"state":"Merged","mergedAt":"2022-05-06T04:57:30Z","number":1185,"mergeCommitSha":"c2e19e309e15fc779ac5f41997bc2e136f2b3486","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1185","title":"Fix SM engine x-file move processing","createdAt":"2022-05-06T04:49:35Z"}
{"state":"Merged","mergedAt":"2022-05-09T23:44:45Z","number":1186,"mergeCommitSha":"c501c178f45469f8a25fce19624e90c2cfb2e0c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1186","title":"Adds pusher service for threads","createdAt":"2022-05-06T05:48:55Z"}
{"state":"Merged","mergedAt":"2022-05-06T14:44:07Z","number":1187,"body":"Prod cluster was under powered. This was causing failures during deployment where we need 30% additional overhead to bring up new instances. \r\n\r\nI have resized both Dev and Prod cluster to use Compute Optimized `c5a` nodes. This should address some performance issue as well. Old nodes were t3 class which provided burstable performance whereas new C5a ones will ensure baseline cpu performance. ","mergeCommitSha":"b6d043a38202591ba14d43d340cd6de38d51fb1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1187","title":"Resize nodes in Dev and Prod to address deployment failures ","createdAt":"2022-05-06T10:57:27Z"}
{"state":"Merged","mergedAt":"2022-05-06T16:38:03Z","number":1188,"body":"Broke demos. Will re-instate after","mergeCommitSha":"a34badd48c0df6280a7077652a539485bfcbede8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1188","title":"Revert \"Echo modified since request parameter if result size is 0\"","createdAt":"2022-05-06T16:24:30Z"}
{"state":"Merged","mergedAt":"2022-05-06T16:41:47Z","number":1189,"body":"We are no longer using docker for postgres tests. \r\nWe are using embedded postgres.\r\nAlso, i created a standard ci dockerfile that is streamlines for tests.","mergeCommitSha":"3236b6c33d9b4c30a2fe16e3a016c6ee3da68e99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1189","title":"Remove unnecesary makefile targets","createdAt":"2022-05-06T16:28:39Z"}
{"state":"Merged","mergedAt":"2022-01-25T01:43:21Z","number":119,"body":"## Problem\r\nWe have to exchange the preauth exchange token for an access token by looking up the associated `AuthenticationState` to see if a previous authentication event has completed for that client `secret`","mergeCommitSha":"b37954b212fc22263e00735d50dbf7f3be672913","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/119","title":"Perform `AuthenticationState` lookup for preauth exchange step","createdAt":"2022-01-24T23:45:39Z"}
{"state":"Merged","mergedAt":"2022-05-06T18:02:19Z","number":1190,"body":"Reverts NextChapterSoftware/unblocked#1188","mergeCommitSha":"817278a7f3a650e2ce257eb4ef9217acea614d48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1190","title":"Bring back \"Echo modified since request parameter if result size is 0\"\"","createdAt":"2022-05-06T17:33:49Z"}
{"state":"Merged","mergedAt":"2022-05-06T19:00:45Z","number":1191,"body":"<img width=\"1703\" alt=\"Screen Shot 2022-05-06 at 11 51 39\" src=\"https://user-images.githubusercontent.com/1798345/167200133-757c87f3-73bd-4134-8f39-232f14d91a8d.png\">\r\n","mergeCommitSha":"3b069858a63d6f0b97ddc82bd6ea252bd694ecb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1191","title":"Debug unread status in admin web","createdAt":"2022-05-06T18:32:50Z"}
{"state":"Merged","mergedAt":"2022-05-10T17:21:02Z","number":1192,"body":"<img width=\"590\" alt=\"CleanShot 2022-05-06 at 11 15 57@2x\" src=\"https://user-images.githubusercontent.com/1553313/167195359-59887067-79c8-4018-96a5-46c9cb77a3a1.png\">\r\n\r\nSetup shared stores for search for clients.\r\n\r\nBasic web UI. \r\n\r\nTODO: Add clear action to input component.\r\n","mergeCommitSha":"223094b86a3bb60fd8bf71ed81b673688e9eca89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1192","title":"Setup stores for search and implement basic web UI","createdAt":"2022-05-06T18:48:45Z"}
{"state":"Merged","mergedAt":"2022-05-06T19:28:33Z","number":1193,"mergeCommitSha":"8213976e3a048683c0452ff711f2e8dd95a8ebe1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1193","title":"Handle non-standard UUIDs, cheers Apple!","createdAt":"2022-05-06T19:02:24Z"}
{"state":"Merged","mergedAt":"2022-05-06T19:24:39Z","number":1194,"body":"I'd like to be able to trigger re-indexing or re-ingestion from the admin console","mergeCommitSha":"34599056c625958ac0c52b5a01b833d57c0e1093","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1194","title":"Add producer permission on search_indexing and pr_ingestion queues to admin service","createdAt":"2022-05-06T19:21:29Z"}
{"state":"Merged","mergedAt":"2022-05-06T21:11:46Z","number":1196,"mergeCommitSha":"a3be0d1c2e68f476fdb9f1cf4ee8e95d78b4a6d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1196","title":"Update Ben cidr","createdAt":"2022-05-06T21:10:51Z"}
{"state":"Merged","mergedAt":"2022-05-09T23:54:34Z","number":1197,"body":"## Summary\r\nThere were a few pusher related bugs that are fixed in this PR around updating unread status.\r\n\r\nI've added notifications, which always fire when there are new unread threads. Notifications permissions are requested when the app starts for the first time - we might want to think about whether that's the best time to do that. The permissions request experience for notifications on macOS is a part of the notifications system. A notification like alert is presented asking for notification permissions, and an option button appears on the notification to allow/deny.\r\n\r\n","mergeCommitSha":"16bf570d9ca68d2ef4c670108ec2ee79134f7dee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1197","title":"Add notifications","createdAt":"2022-05-06T21:53:45Z"}
{"state":"Merged","mergedAt":"2022-05-06T23:35:13Z","number":1198,"body":"Make it easier to understand.\n\nIf this makes sense, then I'll push this data type through to clients on next change.","mergeCommitSha":"fc0cae8f9998ec19bc5859ab9ceb22399fe3763b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1198","title":"Clarify unread states","createdAt":"2022-05-06T22:02:27Z"}
{"state":"Merged","mergedAt":"2022-05-06T23:23:03Z","number":1199,"mergeCommitSha":"820e2c301faffabe142c5ee142e5f9c9f045f83e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1199","title":"Add admin console button to trigger search reindexing for a repo","createdAt":"2022-05-06T22:28:28Z"}
{"state":"Merged","mergedAt":"2021-12-14T19:22:17Z","number":12,"mergeCommitSha":"255f6607a90855df486682f32a465660d18bcba1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/12","title":"Break","createdAt":"2021-12-14T19:10:24Z"}
{"state":"Merged","mergedAt":"2022-01-25T17:11:28Z","number":120,"body":"New models in green:\r\n- ChatModel\r\n- ChatMessageModel\r\n- ChatParticipantModel\r\n\r\n<img width=\"1240\" alt=\"Screen Shot 2022-01-24 at 18 32 17\" src=\"https://user-images.githubusercontent.com/1798345/150900039-c7b84781-3151-4ef4-b967-89e3fc91d21d.png\">\r\n\r\n### TODO\r\n- [ ] tests","mergeCommitSha":"43f3d9af74b16225ce6b3f145f7ceb543bea62ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/120","title":"Introduce Chat db models","createdAt":"2022-01-25T02:34:12Z"}
{"state":"Merged","mergedAt":"2022-05-07T01:50:04Z","number":1200,"mergeCommitSha":"306ef4651993350bebb48b575a793cbeb015afd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1200","title":"Sort thread messages by message created at","createdAt":"2022-05-06T22:56:06Z"}
{"state":"Merged","mergedAt":"2022-05-07T00:03:35Z","number":1201,"mergeCommitSha":"9f7ec13d1b1edc706ff2a7ec55ff832e6ec4919c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1201","title":"Expose unread status in API and clients","createdAt":"2022-05-06T23:16:02Z"}
{"state":"Merged","mergedAt":"2022-05-10T00:15:00Z","number":1202,"body":"Use correct assets, colors, and app icon","mergeCommitSha":"ab769a4ed254125123312b0ec6f38f5388b34c7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1202","title":"Visual cleanup","createdAt":"2022-05-07T05:11:57Z"}
{"state":"Merged","mergedAt":"2022-05-07T16:42:59Z","number":1203,"body":"Safari has a strange behaviour compared to chrome where \"all_urls\" is necessary or else the react components get unmounted...\r\n\r\nThere were also CORS issues with prod builds with include credential. Not necessary for web extension.\r\n\r\nWill need to dig into this a lot closer. Pushing this change in for now to unblock.\r\n","mergeCommitSha":"610d0cd030d5dd3ee2a8dc011ad4219bc3872a73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1203","title":"Fix extensionbuilds","createdAt":"2022-05-07T16:03:31Z"}
{"state":"Merged","mergedAt":"2022-05-09T05:52:14Z","number":1204,"body":"### change\r\n- log trace `io.ktor.auth.jwt`\r\n- log team claims violation\r\n\r\n### motivation\r\n- lot's of unexplained 401s\r\n  https://app.logz.io/#/goto/53cfc0d7e64068bca8550ba42733b6e3?switchToAccountId=411850\r\n\r\n\r\n### result\r\nspits out logs like this now for corrupt JWT, and expired JWT:\r\n```\r\napiservice_1           | 04:47:18 | TRACE | i.k.a.jwt: Token verification failed: The Token's Signature resulted invalid when verified using the Algorithm: SHA256withRSA\r\napiservice_1           | 04:46:59 | TRACE | i.k.a.jwt: Token verification failed: The Token has expired on Mon May 09 04:46:31 UTC 2022.\r\n```","mergeCommitSha":"8d047941e9a202f4eaee0e620eb15551aacf9801","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1204","title":"Enable auth logging to debug 401 causes","createdAt":"2022-05-09T04:40:30Z"}
{"state":"Merged","mergedAt":"2022-05-10T00:54:57Z","number":1205,"mergeCommitSha":"3733bbf04aeb93aa5dbf6dc422af7825d56869b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1205","title":"Hub Auth","createdAt":"2022-05-09T05:03:22Z"}
{"state":"Merged","mergedAt":"2022-05-09T06:47:05Z","number":1206,"mergeCommitSha":"73ae0f53188c5625be3cf27c4980dc5ac915220d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1206","title":"Correct retry semantics","createdAt":"2022-05-09T06:14:11Z"}
{"state":"Merged","mergedAt":"2022-05-09T16:24:47Z","number":1207,"body":"The search service is getting killed because its using too much CPU. I suspect it's because when we throw two thousand events onto the queue at once, it leads to that number of coroutines being launched at the same time. \r\n\r\nNot entirely sure, but let's try finishing processing one batch of events before taking the next batch of events off the queue. This will slow down indexing, so maybe the answer is scaling the instances.","mergeCommitSha":"ee601f83869f8447b475cb1b873b3270d1c161d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1207","title":"Finish indexing messages before taking the next batch of messages off the queue","createdAt":"2022-05-09T06:32:22Z"}
{"state":"Merged","mergedAt":"2022-05-09T07:26:32Z","number":1208,"body":"Move to failsafe library which has superseded resilience4j as the predominant failure library used by big corps etc.\r\n\r\nAlready using this elsewhere, and works well.","mergeCommitSha":"3fefda41e0a508d4f7890fefc4112f3d0ef31809","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1208","title":"Use failsafe library being used elsewhere.","createdAt":"2022-05-09T07:04:01Z"}
{"state":"Merged","mergedAt":"2022-05-09T16:28:25Z","number":1209,"mergeCommitSha":"bf4332ea27746b3c7c51ff02cb32f38e0a679a4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1209","title":"Fix unread status in TS clients","createdAt":"2022-05-09T16:19:25Z"}
{"state":"Merged","mergedAt":"2022-01-25T20:58:03Z","number":121,"body":"- Added a new stack to manage route setup between EKS and VPN\r\n- Extended EKS config object to include new params needed for route setup\r\n- Modified existing NetworkStack to support both route creation with and without peering connection creation. It now can\r\ncreate routes for an existing peering connection (e.g a cross account one that can't be automated)\r\n- Modified dev cdk config to include new info needed for above changes\r\n- Finally modified EKS cluster to take down public endpoint. Now EKS cluster can only be access using VPN\r\n\r\nAll above changes have been applied and work as expected.\r\n\r\n**IMPORTANT NOTE**: Continuous deployment to Kubernetes is broken until we setup OpenVPN for GitHub Actions","mergeCommitSha":"a1f48d8c363a7fb1fd4c18360e7fe9ed4175f820","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/121","title":"Cdk update routes existing peering (VPN config setup)","createdAt":"2022-01-25T07:51:47Z"}
{"state":"Merged","mergedAt":"2022-05-09T20:53:15Z","number":1210,"body":"@jeffrey-ng @kaych either of you know what's up with this?  I'm guessing this was temporary while the repo channel didn't work?","mergeCommitSha":"94e00e8af7314c206bd73b7ce48de3e2037e8bac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1210","title":"Fix incorrect push channel in repo store","createdAt":"2022-05-09T17:02:28Z"}
{"state":"Merged","mergedAt":"2022-05-18T20:30:21Z","number":1211,"body":"Based on https://www.notion.so/nextchaptersoftware/Recommendation-Engine-18047c5f07844b9d9bef343add8a7f0f","mergeCommitSha":"f739de977850d6d2859fffeb82b62c2327356c44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1211","title":"Thread recommendation model","createdAt":"2022-05-09T17:20:31Z"}
{"state":"Closed","mergedAt":null,"number":1212,"mergeCommitSha":"b2f5870d605cfcb7efc6eb39cdf4426fdd2d4ed0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1212","title":"[RFC] Separate APIs for my/recommended threads","createdAt":"2022-05-09T17:20:32Z"}
{"state":"Merged","mergedAt":"2022-05-09T18:05:08Z","number":1213,"body":"Sadly, AWS is not keeping this library up to date and it’s against older versions of postgres.\r\nIt has interesting failover mechanics that deal well with Aurora, but we need a library that is not stagnant atm.\r\nWhen AWS does decide to fully support their jdbc, then we'll move to it.\r\n\r\nAlso upping kotlin versions.\r\n","mergeCommitSha":"7a43a40553fecd3487550e9019ffd1d046d443f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1213","title":"Move away from aws postgres library","createdAt":"2022-05-09T17:22:05Z"}
{"state":"Merged","mergedAt":"2022-05-09T18:10:56Z","number":1214,"body":"The old query was joining sourcemark with message in addition to thread. We just want it to join on thread only\r\n\r\nOld:\r\n```\r\nSELECT threadmodel.id, messagemodel.id \r\nFROM threadmodel \r\nINNER JOIN messagemodel ON threadmodel.id = messagemodel.thread \r\nINNER JOIN sourcemarkmodel ON threadmodel.id = sourcemarkmodel.thread AND messagemodel.id = sourcemarkmodel.message \r\nWHERE (sourcemarkmodel.repo = 'fb06ca32-cdf0-4bcc-9e08-c8c675768235') AND (sourcemarkmodel.\"isAnchor\" = TRUE)\r\n```\r\n\r\nNew:\r\n```\r\nSELECT threadmodel.id, messagemodel.id \r\nFROM threadmodel \r\nINNER JOIN messagemodel ON threadmodel.id = messagemodel.thread \r\nINNER JOIN sourcemarkmodel ON threadmodel.id = sourcemarkmodel.thread \r\nWHERE (sourcemarkmodel.repo = 'fb06ca32-cdf0-4bcc-9e08-c8c675768235') AND (sourcemarkmodel.\"isAnchor\" = TRUE)\r\n```\r\n","mergeCommitSha":"91c05f5a688ab9da25a52777d263b56adb6e615d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1214","title":"Fix join used for re-indexing","createdAt":"2022-05-09T17:55:20Z"}
{"state":"Merged","mergedAt":"2022-05-09T19:11:17Z","number":1215,"body":"Clean up gradle files.","mergeCommitSha":"0fcadfd5e823c5880100bb4769dc3710dab6846c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1215","title":"Gradle files referencing stale versions","createdAt":"2022-05-09T18:06:40Z"}
{"state":"Merged","mergedAt":"2022-05-11T21:29:35Z","number":1216,"body":"Added some functionality to Input component to handle icons and actions. Primarily doing this as setup for search input.\r\n\r\nStyling is most likely a WIP with padding and multiple action/icon states.\r\n\r\n<img width=\"290\" alt=\"CleanShot 2022-05-09 at 11 53 39@2x\" src=\"https://user-images.githubusercontent.com/1553313/167478759-ed4dc080-f242-4a98-b1df-a3b2cdd7dc72.png\">\r\n<img width=\"549\" alt=\"CleanShot 2022-05-09 at 11 51 04@2x\" src=\"https://user-images.githubusercontent.com/1553313/167478785-98eae35a-f7d3-4d61-a3e9-ee209b17e7ed.png\">\r\n<img width=\"578\" alt=\"CleanShot 2022-05-09 at 11 35 17@2x\" src=\"https://user-images.githubusercontent.com/1553313/167478820-54224362-9b85-446b-b1f1-1436c345e981.png\">\r\n\r\n\r\n","mergeCommitSha":"27012549097ead5513db83dab8f9fd2d5a3db849","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1216","title":"Updated Input","createdAt":"2022-05-09T18:59:06Z"}
{"state":"Merged","mergedAt":"2022-05-09T19:58:01Z","number":1217,"mergeCommitSha":"fd8a29efbf0ac3570361397d522731814ffb2c80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1217","title":"Make sure we format before we lint in parallel","createdAt":"2022-05-09T19:18:26Z"}
{"state":"Merged","mergedAt":"2022-05-09T20:44:43Z","number":1218,"mergeCommitSha":"e5b0dc5695822dd3de5325dab797c599202ca869","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1218","title":"Move to coroutine friendly client","createdAt":"2022-05-09T20:20:34Z"}
{"state":"Merged","mergedAt":"2022-05-09T22:36:27Z","number":1219,"body":"First pass at adding author to search. This is kind of hacky but works. It will have to change when we implement [ranking search results](https://www.postgresql.org/docs/current/textsearch-controls.html) which I will be tackling next.","mergeCommitSha":"c7ce780b3ec851d8571dc8046711474cb430daef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1219","title":"Add author to search vector","createdAt":"2022-05-09T21:15:06Z"}
{"state":"Merged","mergedAt":"2022-01-25T19:01:17Z","number":122,"body":"Building off of https://github.com/Chapter2Inc/codeswell/pull/120.\r\n\r\nDefinitely not final, but just want to put something up to start wiring things together. This can/will change based on what clients need.\r\n","mergeCommitSha":"ae694f5b9e250656dd747a4c834854b5bf2f69ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/122","title":"Add chat and message endpoint stubs","createdAt":"2022-01-25T17:02:06Z"}
{"state":"Merged","mergedAt":"2022-05-10T01:03:40Z","number":1220,"mergeCommitSha":"114e8b7d45d23b042b4edd32829580b2dd7d6498","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1220","title":"More visual fixes","createdAt":"2022-05-09T21:31:23Z"}