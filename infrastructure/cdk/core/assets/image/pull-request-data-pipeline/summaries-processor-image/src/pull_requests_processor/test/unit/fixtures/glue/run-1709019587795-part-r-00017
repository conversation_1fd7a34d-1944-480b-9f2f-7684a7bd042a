{"state":"Merged","mergedAt":"2022-02-15T19:24:54Z","number":334,"body":"Pulls all the pieces together to give us `modifiedSince` queries for a particular channel. The current implementation maps a channel directly to a DB model, which might need refactoring for API models that span multiple DB models. Would be fairly trivial to modify to accept a list of predicates and queries. \r\n\r\nNext up I'm going to create several fixtures that we can load locally and in dev, and then introduce a hidden auth endpoint to skip the auth flow so we can start integrating with the web client. ","mergeCommitSha":"969dda66e9d4a3f5f549f3a5bf7454743c6a7acf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/334","title":"Adds pushChannelService for /threads","createdAt":"2022-02-14T05:02:18Z"}
{"state":"Merged","mergedAt":"2022-10-19T19:37:12Z","number":3340,"mergeCommitSha":"2c427151e5334af63cb395390132b95921c01307","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3340","title":"Add slack firendly unicode","createdAt":"2022-10-19T19:24:23Z"}
{"state":"Merged","mergedAt":"2022-10-19T21:32:22Z","number":3341,"mergeCommitSha":"2692cb6b1d5ac7db694b5c43a8dbc1135088e4fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3341","title":"Uninstall slack team","createdAt":"2022-10-19T20:30:03Z"}
{"state":"Merged","mergedAt":"2022-10-19T21:20:12Z","number":3342,"mergeCommitSha":"0613c8060048d2ceca5197ab5443b4f772135ae8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3342","title":"Include slack messages in PR message count","createdAt":"2022-10-19T20:52:46Z"}
{"state":"Merged","mergedAt":"2022-10-25T16:45:32Z","number":3343,"body":"Created new APIs and deprecated old ones:\r\n\r\n- getSourceMarks → getRepoSourceMarks\r\n- findSourceMarks → findRepoSourceMarks\r\n- putSourcePoints → putRepoSourcePoints\r\n- createThread → createThreadV2\r\n- createMessage → createMessageV2\r\n\r\nFollow-up changes will:\r\n- implement new APIs\r\n- adopt new APIs in web\r\n- adopt new APIs in web-ext\r\n- adopt new APIs in VSCode and support FileMarks\r\n- optimize storage of points on the server (jsonSnippet -> jsonSnippetBlob, migrate originalFilePath to SourceMark maybe)\r\n\r\nLater performance improvements:\r\n- asynchronously process upstreamed points","mergeCommitSha":"e33bade0fd7747f1a9261fb3d5a0c5ec7a483086","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3343","title":"New sourcepoint API that is more efficient and supports video FileMarks","createdAt":"2022-10-19T21:14:09Z"}
{"state":"Merged","mergedAt":"2022-10-20T23:54:04Z","number":3344,"body":"Refactor common aspects of `KnowledgeFormCollaborators` into a separate file to be reused.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/197031176-8d6c6327-9978-407d-9f8d-dd8f3c8dfe4c.mp4\r\n\r\n","mergeCommitSha":"ee54dba19b42cd79508e13428fffc1f5a2983520","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3344","title":"Refactored knowledge form","createdAt":"2022-10-19T21:15:22Z"}
{"state":"Merged","mergedAt":"2022-10-20T21:22:59Z","number":3345,"mergeCommitSha":"3896fdf109509c7b95477c8055713d6e23f851e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3345","title":"Embed walkthrough app in customers builds","createdAt":"2022-10-19T21:35:54Z"}
{"state":"Merged","mergedAt":"2022-10-19T22:40:34Z","number":3346,"mergeCommitSha":"9b91a196f54dacc2880344bffd73d3b19210dba9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3346","title":"minor refactor","createdAt":"2022-10-19T22:28:34Z"}
{"state":"Merged","mergedAt":"2022-10-19T23:43:05Z","number":3347,"mergeCommitSha":"e4ec3c83f6c5d21a086e7b1facbbed2c2721ca25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3347","title":"Add more logging","createdAt":"2022-10-19T23:19:19Z"}
{"state":"Merged","mergedAt":"2022-10-20T15:15:33Z","number":3348,"mergeCommitSha":"28d4f92367fc730ee39c7025b3bba57af42eb184","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3348","title":"Update slack scope","createdAt":"2022-10-20T00:41:38Z"}
{"state":"Merged","mergedAt":"2022-10-20T18:08:38Z","number":3349,"body":"GH Spinner style was being overwritten by our css.\r\n\r\nAdd a wrapper class which allows us to inject classnames.\r\n\r\nhttps://app.intercom.com/a/inbox/crhakcyc/inbox/admin/5620832/conversation/298\r\n<img width=\"701\" alt=\"CleanShot 2022-10-19 at 21 59 05@2x\" src=\"https://user-images.githubusercontent.com/1553313/196862401-c7b79273-370c-45e3-9c36-8be7b29551df.png\">\r\n<img width=\"465\" alt=\"CleanShot 2022-10-19 at 21 59 12@2x\" src=\"https://user-images.githubusercontent.com/1553313/196862404-07d4983e-64be-4df9-996f-1525b865458d.png\">\r\n","mergeCommitSha":"120f8919ec665df2debe130450c685e9a2831e9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3349","title":"Fix Extension Spinners","createdAt":"2022-10-20T05:18:40Z"}
{"state":"Merged","mergedAt":"2022-02-14T19:34:25Z","number":335,"body":"So we can skip installation and auth etc after we drop a db.\r\n\r\nNext - I'm going to introduce an endpoint that allows certain environments to vend auth tokens for identities skipping OAuth","mergeCommitSha":"3b207892725b893f14b75c8190a5fa619680a635","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/335","title":"Adds ability to pre-populate database with unblocked team members","createdAt":"2022-02-14T06:17:21Z"}
{"state":"Merged","mergedAt":"2022-10-20T18:55:24Z","number":3350,"body":"The correct way to remove a version from a channel is to mark the version as obsolete.\r\n\r\nThis change prevents us from inadvertently breaking clients.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-694/check-that-api-spec-is-compatible-with-released-versions","mergeCommitSha":"e1d7656c7358fb4399286055fbb5b41d9aa6858e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3350","title":"Versions can be added to a channel but cannot be removed","createdAt":"2022-10-20T15:48:48Z"}
{"state":"Merged","mergedAt":"2022-10-20T19:07:47Z","number":3351,"body":"https://linear.app/unblocked/issue/UNB-694/check-that-api-spec-is-compatible-with-released-versions\r\n\r\n### Results\r\nTurns out we have broken _every_ Stable released version!\r\n\r\n<img width=\"580\" alt=\"Screen Shot 2022-10-20 at 08 54 35\" src=\"https://user-images.githubusercontent.com/1798345/196998219-d348a2cc-1495-4101-83fc-0e3f5442fdd2.png\">\r\n\r\n","mergeCommitSha":"455417e847084dba608a5f2a346118aeb62cf5ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3351","title":"Check that API spec is compatible with released versions","createdAt":"2022-10-20T15:48:50Z"}
{"state":"Closed","mergedAt":null,"number":3352,"body":"- VSCode is now aware of SourceMarks/SourcePoints and FileMarks/FilePoints for Video Walkthrough\r\n- Deals with sparse file-path and sparse snippets\r\n- Unzips compressed and encoded snippets for SourceMarks on-demand\r\n- Uses more efficient upload API with better error handling","mergeCommitSha":"b1f4f77fd33812300f85ad011c0a6beb5e57c191","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3352","title":"Adopt new SourceMark APIs in VSCode","createdAt":"2022-10-20T16:01:55Z"}
{"state":"Merged","mergedAt":"2022-10-20T16:57:05Z","number":3353,"mergeCommitSha":"00cb0b7276a86d2c59eacfbd2419cb59103c5dc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3353","title":"Up specs of prod","createdAt":"2022-10-20T16:49:22Z"}
{"state":"Merged","mergedAt":"2022-10-20T19:53:52Z","number":3354,"body":"`ThreadsApiDelegateImpl` was missing the actual metadata creation step. Got missed due to confusion over `MessageService`'s many `createMessage` functions","mergeCommitSha":"639428fc072587d95b1454ed09e4b1fedf2a7e11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3354","title":"Actually create videometadata in new threads","createdAt":"2022-10-20T18:38:49Z"}
{"state":"Merged","mergedAt":"2022-10-20T19:06:54Z","number":3355,"body":"Add auth install tests for slack.","mergeCommitSha":"ba035b83d05a8a0041bba9dfbc44a6f0f77f6a16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3355","title":"Imprvoe tests and logging","createdAt":"2022-10-20T18:49:36Z"}
{"state":"Merged","mergedAt":"2022-10-20T20:36:30Z","number":3356,"mergeCommitSha":"b20dfe83475b43e90dd99b6ea3996df96b6e29f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3356","title":"Use Flac because VSCode has licensing restrictions with AAC","createdAt":"2022-10-20T19:44:35Z"}
{"state":"Merged","mergedAt":"2022-10-21T00:26:40Z","number":3357,"body":"The service is now guaranteed to respond with the expected _model type_ and _status code_ as defined by the open API spec,\neliminating tedious code and a class of bugs.\n\nhttps://linear.app/unblocked/issue/UNB-152/api-service-does-not-validate-output-encoding","mergeCommitSha":"abe027127f40d4f95de65e2cc38a3746cf904d36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3357","title":"API service validates output encoding","createdAt":"2022-10-20T20:35:23Z"}
{"state":"Merged","mergedAt":"2022-10-20T21:51:46Z","number":3358,"mergeCommitSha":"f5b9522d1bfe88a36a6588da40d1a9815c11cbc5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3358","title":"Delete configuration when slack team is uninstalled","createdAt":"2022-10-20T20:48:37Z"}
{"state":"Merged","mergedAt":"2022-10-21T00:28:19Z","number":3359,"mergeCommitSha":"afc7dfc922fff848a1c2397c44bcfe304df1708b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3359","title":"Update API implementation to conform to generated interface","createdAt":"2022-10-20T21:37:45Z"}
{"state":"Merged","mergedAt":"2022-02-15T05:28:39Z","number":336,"body":"## Problem\r\nIt is currently not possible to authenticate with the service without stepping through the SCM auth flow. This impacts client development velocity and prevents us from writing FVTs. \r\n\r\n## Proposal\r\nGive clients a faster path to assume an identity for development purposes. Even more useful when used in tandem with https://github.com/NextChapterSoftware/unblocked/pull/335","mergeCommitSha":"eaefc798fa52d9800e7e5e3ffac2b75b4d16c4b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/336","title":"Add admin auth endpoint","createdAt":"2022-02-14T16:07:58Z"}
{"state":"Merged","mergedAt":"2022-10-24T18:27:33Z","number":3360,"body":"Basic UI for CodeReferences.\r\n\r\nTODO: \r\n* FileIcons\r\n* Navigation to SourcePoints\r\n\r\n<img width=\"702\" alt=\"CleanShot 2022-10-20 at 14 38 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/197064600-b41a32c0-ddc1-4d34-a7b3-27b68d83090d.png\">\r\n<img width=\"728\" alt=\"CleanShot 2022-10-20 at 14 06 44@2x\" src=\"https://user-images.githubusercontent.com/1553313/197064608-00e0890a-e303-462e-bd23-df9356ec5052.png\">\r\n","mergeCommitSha":"a51fd8ae658863683fa5340b980f655ca20dfe68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3360","title":"VideoMetadata - CodeReferences UI","createdAt":"2022-10-20T21:42:40Z"}
{"state":"Merged","mergedAt":"2022-10-20T21:52:59Z","number":3361,"mergeCommitSha":"a4f61b229da109c689e9ebfc49606f41c44a8ec0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3361","title":"Only configure audio when source is available","createdAt":"2022-10-20T21:44:26Z"}
{"state":"Merged","mergedAt":"2022-10-20T22:55:59Z","number":3362,"mergeCommitSha":"24bd91402986009a0c3d24a4120a7bdc4d5ba613","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3362","title":"Do not return slack threads for uninstalled teams","createdAt":"2022-10-20T22:34:46Z"}
{"state":"Merged","mergedAt":"2022-10-20T23:10:00Z","number":3363,"body":"- Add status information\r\n- Add granular information\r\n","mergeCommitSha":"a7c8dbff2fd960b9688039ce28ef5d0cd48b2cda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3363","title":"AddSlackIngestionSTatus","createdAt":"2022-10-20T22:48:22Z"}
{"state":"Merged","mergedAt":"2022-10-21T15:28:09Z","number":3364,"body":"<img width=\"1499\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197073566-210a06a8-6a7f-4aa2-9270-e034bda04485.png\">\r\n\r\n<img width=\"1494\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197073628-539795be-a070-434d-9340-ba3f606f2db8.png\">\r\n\r\n<img width=\"732\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197073643-ecee7ff2-19f3-4414-9918-27a8fecbb507.png\">\r\n\r\n<img width=\"1204\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197073685-187f01bd-2b64-4cba-9ae7-eae789585642.png\">\r\n\r\n<img width=\"613\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197073706-6cb92672-0e81-40c0-9807-28520660f0b3.png\">\r\n\r\n<img width=\"566\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197074312-c811ccc6-eeec-4f45-8bb9-fb4f2cb38432.png\">\r\n\r\n","mergeCommitSha":"bfc7f8e7f8a7307247142621ff2f10e797d56eb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3364","title":"[Slack] Auth/configure in dashboard","createdAt":"2022-10-20T22:56:32Z"}
{"state":"Merged","mergedAt":"2022-10-21T00:27:32Z","number":3365,"mergeCommitSha":"ce452fb26da26fd7b97bda25dde2656c70513535","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3365","title":"Support 3xx redirect output in mustache","createdAt":"2022-10-20T23:08:27Z"}
{"state":"Merged","mergedAt":"2022-10-20T23:11:08Z","number":3366,"mergeCommitSha":"24d3c6e4391b2c62c13a9ff3f87c49289e45acf1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3366","title":"Re-add networking entitlements to video app","createdAt":"2022-10-20T23:10:29Z"}
{"state":"Merged","mergedAt":"2022-10-20T23:18:06Z","number":3367,"body":"Temporarily enable video for prod. \r\n\r\nAllow us to get a test build.","mergeCommitSha":"238e04211ea1ee195c7d45f0d00842d0b5b7ec7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3367","title":"Temp enalbe video for prod","createdAt":"2022-10-20T23:17:32Z"}
{"state":"Closed","mergedAt":null,"number":3368,"mergeCommitSha":"8061a9caaea131eb2bc6639378cd764b68484aed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3368","title":"Jeff/revert video prod","createdAt":"2022-10-20T23:19:24Z"}
{"state":"Merged","mergedAt":"2022-10-20T23:25:14Z","number":3369,"mergeCommitSha":"d2a70ae0326ac7f6a97a843e5f1c882e0ed4214c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3369","title":"add one more link","createdAt":"2022-10-20T23:24:15Z"}
{"state":"Merged","mergedAt":"2022-02-14T17:16:12Z","number":337,"body":"`https://apiservice.us-west-2.dev.getunblocked.com/login/github?clientType=web` currently returning 500 with `{\"status\":500,\"detail\":\"You should set secure cookie only via secure transport (HTTPS)\"}`\r\n\r\nAssuming our API service is behind a load balancer that could be stripping away HTTPS, add XForwardedHeaderSupport to support secure cookies.\r\n\r\nhttps://github.com/ktorio/ktor/issues/311","mergeCommitSha":"0e597a3ecad21d934ff85879d28a4724d87fc14d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/337","title":"Install XForwardedHeaderSupport","createdAt":"2022-02-14T17:09:20Z"}
{"state":"Merged","mergedAt":"2022-10-21T18:26:38Z","number":3370,"body":"Logic that goes in the TopicExtractorService to come later in a separate PR","mergeCommitSha":"2a12f34b282e83a7de713716c6c4e626763be555","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3370","title":"Create stub TopicExtractionJob","createdAt":"2022-10-20T23:32:19Z"}
{"state":"Merged","mergedAt":"2022-10-21T00:55:50Z","number":3371,"mergeCommitSha":"255c461a965efb669ec5db6806df18c0605ad52e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3371","title":"Re-enable gradle cache","createdAt":"2022-10-21T00:33:25Z"}
{"state":"Merged","mergedAt":"2022-10-21T00:57:13Z","number":3372,"body":"From #3334","mergeCommitSha":"8d93884175206d64a761f5bfb71feff5766b88e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3372","title":"Fix Topic build breaks","createdAt":"2022-10-21T00:38:58Z"}
{"state":"Merged","mergedAt":"2022-10-21T00:56:16Z","number":3373,"mergeCommitSha":"3e976815730975009f353b8834bfd4499ee5bfbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3373","title":"Fix bug","createdAt":"2022-10-21T00:56:03Z"}
{"state":"Merged","mergedAt":"2022-10-21T01:09:57Z","number":3374,"body":"- Update\r\n- Fix bug\r\n","mergeCommitSha":"7f339cdaf813e438b17ba15c6ed7a85ea247c877","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3374","title":"FixBugs","createdAt":"2022-10-21T01:09:46Z"}
{"state":"Merged","mergedAt":"2022-10-21T01:39:52Z","number":3375,"mergeCommitSha":"81d2994dc007acf4b1cf9e191e89f8b013345aed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3375","title":"Fix test","createdAt":"2022-10-21T01:39:46Z"}
{"state":"Merged","mergedAt":"2022-10-21T17:35:13Z","number":3376,"mergeCommitSha":"50038aeb7abedd6f2feb571856b62d8cfd17fc15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3376","title":"Remove api config settings","createdAt":"2022-10-21T02:35:43Z"}
{"state":"Merged","mergedAt":"2022-10-21T03:19:37Z","number":3377,"mergeCommitSha":"0b8712d8e78520ed228ac089b62c616e410a5ff1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3377","title":"Fix build","createdAt":"2022-10-21T03:19:30Z"}
{"state":"Merged","mergedAt":"2022-10-22T17:30:09Z","number":3378,"body":"Shares the authToken between the native and web clients in the mobile app.","mergeCommitSha":"8bbda0a95451ca038048b7de2f5ef48783331f5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3378","title":"Adds SSO between native client and webview","createdAt":"2022-10-21T05:35:00Z"}
{"state":"Merged","mergedAt":"2022-10-21T15:13:55Z","number":3379,"mergeCommitSha":"dbd949d66c4e6e5833dc8934b330ecd322aa8fcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3379","title":"Add async event dequeue","createdAt":"2022-10-21T14:47:26Z"}
{"state":"Merged","mergedAt":"2022-02-14T18:01:36Z","number":338,"body":"Last web deploy failed. This is to fix the CI job for it. \r\n- Distro ID was changed since our last deploy. We were forced to recreate them in order to use a more up to date CDK resource\r\n- Corrected some copy paste mistakes ","mergeCommitSha":"a7a4ad1cb0f5faea5251d465dfce9b7b8275788f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/338","title":"fix web deployment","createdAt":"2022-02-14T17:46:45Z"}
{"state":"Merged","mergedAt":"2022-10-21T16:29:59Z","number":3380,"mergeCommitSha":"1e9b48410cad1187faf9eac586f7d3611c405c3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3380","title":"Remove camera view if capture is disabled","createdAt":"2022-10-21T16:16:13Z"}
{"state":"Merged","mergedAt":"2022-10-25T16:47:56Z","number":3381,"mergeCommitSha":"dba8255443262a867e8c314a541aba9f06c4fd9d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3381","title":"Implement new SourcePoint APIs","createdAt":"2022-10-21T17:43:17Z"}
{"state":"Merged","mergedAt":"2022-10-24T15:46:49Z","number":3382,"body":"* On initial auth, preselect `All public channels` and prompt user to save \r\n* Fix navigation route blocking to omit dashboardBaseName from transition\r\n* Fix save button rendering as enabled on load\r\n* Clear state banner when the team changes ","mergeCommitSha":"f0e15c732caf3e56ba9937d57d2e384d4be7f6b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3382","title":"[Slack] Dashboard bug fixes","createdAt":"2022-10-21T17:48:10Z"}
{"state":"Merged","mergedAt":"2022-10-21T17:57:09Z","number":3383,"mergeCommitSha":"f6abd7d8db417191294008326f1b1e353904f0e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3383","title":"Fix slack ingestion","createdAt":"2022-10-21T17:53:40Z"}
{"state":"Merged","mergedAt":"2022-10-21T18:22:41Z","number":3384,"mergeCommitSha":"d52fabc29c6f4919b95a1c6e6ba456c91123f6bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3384","title":"Remove slack ingestion fixtures","createdAt":"2022-10-21T18:13:16Z"}
{"state":"Merged","mergedAt":"2022-10-21T19:20:53Z","number":3385,"mergeCommitSha":"f760c1c50e88a71c83aca33efdd7fe374ebd9269","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3385","title":"Add local slack auth","createdAt":"2022-10-21T18:34:30Z"}
{"state":"Merged","mergedAt":"2022-10-21T21:11:30Z","number":3386,"body":"- FixIdentityUniqueIndex\r\n- update\r\n","mergeCommitSha":"4c055cb324389116c036f6031d748abc9b6f91fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3386","title":"FixIdenityUniqueIndex","createdAt":"2022-10-21T18:55:56Z"}
{"state":"Merged","mergedAt":"2022-10-21T19:31:38Z","number":3387,"mergeCommitSha":"7e7b8217cece21349c2a74719262cde7cf72ae60","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3387","title":"Fix auth tests","createdAt":"2022-10-21T19:19:14Z"}
{"state":"Merged","mergedAt":"2022-10-21T21:32:35Z","number":3388,"body":"https://linear.app/unblocked/issue/UNB-697/increase-clickable-area-for-collapsible-section-headers\r\nhttps://linear.app/unblocked/issue/UNB-698/slack-icon-and-chevron-are-not-vertically-centered\r\nhttps://linear.app/unblocked/issue/UNB-699/drop-file-name-size-to-11pt","mergeCommitSha":"84b9f604295555af9242330d27d1e37e2e47ee52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3388","title":"[Slack] Feedback in thread views","createdAt":"2022-10-21T19:58:41Z"}
{"state":"Merged","mergedAt":"2022-10-21T21:16:39Z","number":3389,"mergeCommitSha":"b5db62e83c07bf8840b6396b48bf2653d8b15ef8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3389","title":"reenable","createdAt":"2022-10-21T21:16:33Z"}
{"state":"Merged","mergedAt":"2022-02-14T20:17:14Z","number":339,"mergeCommitSha":"6ef3fa4c23347c7bb226b53bb741c115421cd74d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/339","title":"Revert code default","createdAt":"2022-02-14T19:43:49Z"}
{"state":"Closed","mergedAt":null,"number":3390,"body":"The blue bubble video still needs to be done, but I'm waiting for the change in the product before I recapture that.\r\n\r\nhttps://user-images.githubusercontent.com/13353189/197296113-2fd7bdb7-c090-4fca-9943-b34f9dcd08c3.mp4\r\nhttps://user-images.githubusercontent.com/13353189/197296116-1c8de8cf-2467-490a-b708-44e64676711a.mp4\r\n\r\n","mergeCommitSha":"9cae9e507bbf6681efc4fe413551d15a67bd3c97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3390","title":"Updating insight bubble and hub onboarding videos","createdAt":"2022-10-21T22:04:21Z"}
{"state":"Closed","mergedAt":null,"number":3391,"body":"per new designs/blue bubble rebranding","mergeCommitSha":"1ff11d2d173af338ee9977f82fd0a72a7f97fbf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3391","title":"Rebrand discussions to context/insights","createdAt":"2022-10-21T22:25:13Z"}
{"state":"Merged","mergedAt":"2022-10-21T23:20:31Z","number":3392,"mergeCommitSha":"e9bc801f60a4160c9a502447500a3361a381ea45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3392","title":"Ensure we rebuild things when template files change","createdAt":"2022-10-21T23:05:30Z"}
{"state":"Merged","mergedAt":"2022-10-25T18:48:29Z","number":3393,"body":"This allows us to handle very large stdout/stderr results (the exceptional case), while not unnecessarily allocating large buffers for the general case, where little-to-no data is output.","mergeCommitSha":"83f8ea4f48f078963610ec5ccdcaeca4c7a017d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3393","title":"Remove preallocated buffer in Runner","createdAt":"2022-10-21T23:46:09Z"}
{"state":"Closed","mergedAt":null,"number":3394,"body":"Initial backgroundtask for notifications without using apns.\r\n\r\nonce we confirm that these notifications are actually going to fire in a meaningful way, we can wire up the existing notification infra from the hub app.\r\n\r\nif they don't work from background fetch, we will just move forward with apns server. I am likely to just use https://github.com/parse-community/node-apn/blob/HEAD/doc/apn.markdown ","mergeCommitSha":"8bc1c0ec6696ab13738e1519527d4a3c4549a46d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3394","title":"Unblocked-Mobile-3: Notifications","createdAt":"2022-10-23T22:16:49Z"}
{"state":"Merged","mergedAt":"2022-10-24T03:28:04Z","number":3395,"mergeCommitSha":"bc7543d04a170a4ab45f34edeacc671038901d43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3395","title":"API Versioning: Expose release versions","createdAt":"2022-10-24T02:47:42Z"}
{"state":"Merged","mergedAt":"2022-10-24T05:22:48Z","number":3396,"body":"- Use real live non-obsolete stable versions in API compatibility test.\r\n- Tests start at a minimum version number because earlier versions are technically broken but not marked as obsolete yet.\r\n\r\nNote: The API test is expected to fail until the parent PR in this stack is merged to PROD.","mergeCommitSha":"23040ac7fde43ef745fe51846d9365af09dd4cb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3396","title":"API Versioning: Replace hardcoded live versions with real versions","createdAt":"2022-10-24T02:47:44Z"}
{"state":"Merged","mergedAt":"2022-10-25T16:47:22Z","number":3397,"body":"The `getRepoSourceMarks` API was never released, so that's why it's safe to break on main.","mergeCommitSha":"497bb2b5a05ae7609f7bf3ef8e416acd4a445838","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3397","title":"[BREAKS API ON MAIN] Change response object on getRepoSourceMarks","createdAt":"2022-10-24T03:09:12Z"}
{"state":"Merged","mergedAt":"2022-10-24T17:14:05Z","number":3398,"body":"Last night's deployment issue was caused by the `build_attempt` potion of the docker image tag being updated on a re-run. This is a new bug and I don't recall seeing it before. It could be caused by Github deprecating set::output method. I'd like to try the new approach for setting job outputs ","mergeCommitSha":"23c88b6d09eed59ad0d08aa5861ee64bff1a63a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3398","title":"Fix deployments number","createdAt":"2022-10-24T05:16:33Z"}
{"state":"Merged","mergedAt":"2022-10-24T18:22:18Z","number":3399,"body":"- Revert \"Remove slack ingestion fixtures (#3384)\"\r\n- Slack prod ingestion\r\n","mergeCommitSha":"6c74d781b4020ac7621b1d9c96588a5c37482b24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3399","title":"HardcodeCreds","createdAt":"2022-10-24T18:14:49Z"}
{"state":"Merged","mergedAt":"2022-01-13T23:58:31Z","number":34,"body":"Basic skeleton for Gradle.\r\n1. We can change the module names and whatever, don't care.","mergeCommitSha":"559a37588973cf9dd6b6b496b79316e1fe478a2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/34","title":"Add gradle skeleton","createdAt":"2022-01-13T23:39:58Z"}
{"state":"Merged","mergedAt":"2022-02-16T23:34:53Z","number":340,"body":"First draft of message models.\r\nBased on discussions with Matt and Rashin.\r\n\r\nMimics SlateJS data models as mentioned here: https://www.notion.so/nextchaptersoftware/Message-Data-Model-3bedd661a3cf48a292c444de915cd264\r\n\r\nEDIT:\r\n\r\nBased on IRL discussions, we are moving the message model into a separate API spec.\r\nAll interactions with messages from the base API will treat messageContent as a raw json string.","mergeCommitSha":"0283a2eb379e74906368656f9d4317fc0d9c1f0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/340","title":"Message Models","createdAt":"2022-02-14T19:51:13Z"}
{"state":"Merged","mergedAt":"2022-10-24T19:55:49Z","number":3400,"mergeCommitSha":"9149088f59c1343378c91a12dfb29a65f2e40794","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3400","title":"[BREAKS API ON MAIN] Add channels ingestion api","createdAt":"2022-10-24T18:35:44Z"}
{"state":"Merged","mergedAt":"2022-10-24T19:25:13Z","number":3401,"mergeCommitSha":"9cd67546ee4e9512213a7f005e92e2b559a7cc3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3401","title":"Attempt to fix helm. (We need to lock down on helm version)","createdAt":"2022-10-24T18:47:20Z"}
{"state":"Merged","mergedAt":"2022-10-24T19:55:29Z","number":3402,"mergeCommitSha":"2144695f279d032f75b595ceea8839db0cafbac0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3402","title":"Fix hardcoded slack","createdAt":"2022-10-24T19:47:58Z"}
{"state":"Merged","mergedAt":"2022-10-25T17:04:38Z","number":3403,"body":"per designs:\r\n\r\n<img width=\"1131\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197624642-72182940-9105-4364-adaf-20f9535638e1.png\">\r\n\r\nAlso:\r\n* Fix small bug in user icon stack rendering\r\n* Make slack config labels clickable","mergeCommitSha":"31a7cd45b65045a0a59ee0f76715cacfe2b8ce7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3403","title":"Update slack thread rendering on dashboard","createdAt":"2022-10-24T20:39:30Z"}
{"state":"Merged","mergedAt":"2022-10-24T22:57:54Z","number":3404,"body":"A slack installation url is always the same installurl for an SCM team.\r\nJust return all slack teams and their installation status, so client can handle it on its end.","mergeCommitSha":"0c638aac9138587301da86d0a58271979f6f76de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3404","title":"[BREAKS API ON MAIN] Slack installations api cleanup","createdAt":"2022-10-24T20:43:10Z"}
{"state":"Merged","mergedAt":"2022-10-24T23:37:28Z","number":3405,"mergeCommitSha":"e9613d3c70b13753bf7f78f9ca28480d2f4216c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3405","title":"Add TopicHistogramModel","createdAt":"2022-10-24T21:28:58Z"}
{"state":"Closed","mergedAt":null,"number":3406,"body":"Issue occurred when installations state & auth state were out of sync.\r\n\r\nNow, whenever auth state is updated, installations should try to refresh if there's a change.","mergeCommitSha":"a195bc6d2d5ed569b5a833e1808cfc484cd4fb95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3406","title":"Update installations status on auth updates","createdAt":"2022-10-24T21:34:43Z"}
{"state":"Closed","mergedAt":null,"number":3407,"mergeCommitSha":"62e8cabb32f3e183ea591733cb8372c9ec02c3d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3407","title":"WIP: Add CoreNLP","createdAt":"2022-10-24T22:49:36Z"}
{"state":"Merged","mergedAt":"2022-10-26T19:59:21Z","number":3408,"body":"Per slack thread: https://chapter2global.slack.com/archives/C02GEN8LFGT/p1666392216723019\r\n\r\n```\r\nInsight (umbrella term for all content) \r\n\r\nNote (blue bubbles)\r\nVideo (video icon)\r\nPull Request conversation (purple bubbles)\r\nHistorical Pull Requests\r\nSlack thread (/conversation — when topics come online)\r\n...\r\n```\r\n\r\nOutstanding work:\r\n- [x] Hub text\r\n- ~~[ ] Email copy~~\r\n- [ ] Dashboard routing ","mergeCommitSha":"a0a77641ff65b075d4fe07f6f583bda486f2d227","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3408","title":"Unify product terms in clients","createdAt":"2022-10-24T23:00:05Z"}
{"state":"Merged","mergedAt":"2022-10-25T00:10:26Z","number":3409,"mergeCommitSha":"4150628acd88c3430f90e1f452fd9c2d60556736","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3409","title":"Add an index on ThreadSearchModel.thread","createdAt":"2022-10-24T23:58:56Z"}
{"state":"Merged","mergedAt":"2022-02-14T20:38:16Z","number":341,"body":"This pr adds some experimental ktlint rules, one of which ensures trailing commas and fixes code that doesn't have it.\r\n","mergeCommitSha":"72729f68d88c50481b3038d4380669162a431ab5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/341","title":"Add experiemnal ktlint rules","createdAt":"2022-02-14T20:24:41Z"}
{"state":"Merged","mergedAt":"2022-10-26T17:51:50Z","number":3410,"body":"Adding this call for this week's sprint.  The idea is that the client will call this, providing the set of folders in the repo.  The service will do the work to generate the top `n` topics and return them.\r\n\r\nI'm keeping this as simple as possible for now.  Maybe the request/response should be objects?","mergeCommitSha":"b4483774c1754e53717153e152aac014e6597571","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3410","title":"Add 'getRecommendedTopics' API","createdAt":"2022-10-25T00:04:30Z"}
{"state":"Merged","mergedAt":"2022-10-25T00:29:43Z","number":3411,"mergeCommitSha":"f757af1d4d0537e11d049265fe6a74b06fedf936","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3411","title":"Fix indices","createdAt":"2022-10-25T00:27:21Z"}
{"state":"Merged","mergedAt":"2022-10-25T16:10:41Z","number":3412,"body":"- TS version of Kotlin's takeWhile, which is pretty useful\n  https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/take-while.html\n\n- going to be used in follow up PR. factoring out here to make review easier.","mergeCommitSha":"f39e872e0fbad917a282247c85077deba1c0fac5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3412","title":"Typescript version of takeWhile","createdAt":"2022-10-25T04:30:18Z"}
{"state":"Merged","mergedAt":"2022-10-25T21:58:54Z","number":3413,"mergeCommitSha":"00534f8f5f927f9a4f9bafe06d2438ac6e44413c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3413","title":"Update SlackChannelIngestionService to save topic histogram","createdAt":"2022-10-25T06:28:28Z"}
{"state":"Merged","mergedAt":"2022-10-28T00:29:12Z","number":3414,"body":"Add a series of commands that display a topic extraction and review UI.  There is a separate command for each different king of source data:\r\n\r\n* Local git (git commit messages and bodies)\r\n* PRs\r\n* Slack\r\n* PRs and Slack\r\n\r\nhttps://user-images.githubusercontent.com/2133518/198416983-df761dad-0668-4524-a3b3-5f90e5121857.mp4\r\n\r\n","mergeCommitSha":"897082e2dd58b37265a8c933d79095ba225b2a81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3414","title":"VSCode topic extraction commands","createdAt":"2022-10-25T16:22:49Z"}
{"state":"Merged","mergedAt":"2022-10-25T17:33:44Z","number":3415,"mergeCommitSha":"4470548315722c9c324db295cc7a9f6e2c0d938c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3415","title":"Record when ingestion was last finished","createdAt":"2022-10-25T17:21:01Z"}
{"state":"Merged","mergedAt":"2022-10-25T20:24:44Z","number":3416,"body":"This should address the problems of deployments getting into a bad state requiring explicit rollbacks","mergeCommitSha":"5595a0c71958bb419f55d8154e24c69f0c30bb51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3416","title":"prevent deployment cancelation before rollback","createdAt":"2022-10-25T20:03:58Z"}
{"state":"Merged","mergedAt":"2022-10-25T21:11:18Z","number":3417,"body":"This reverts commit ec87cd057463fc2d2e002fba6692a1c3d05d8b23.\r\n\r\nNot sure what I was thinking. This was just a super dumb bug.\r\n\r\n⚠️ So I fucked up and used this on the unblocked repo on DEV, which nuked _**all**_ sourcepoints for blue bubble threads.","mergeCommitSha":"618e1030f779d4657f7a7c2edca4b81751ca4883","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3417","title":"Revert \"Clearing generated point should also clear trusted points (#2642)\"","createdAt":"2022-10-25T20:35:31Z"}
{"state":"Merged","mergedAt":"2022-10-25T20:51:50Z","number":3418,"body":"<img width=\"1303\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/197877438-758ea8a7-edc2-4aca-98e9-20273891837d.png\">\r\n","mergeCommitSha":"c4f2116ab6c74bee875598d7137235683825cffb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3418","title":"Remove subicons for slack users","createdAt":"2022-10-25T20:40:06Z"}
{"state":"Merged","mergedAt":"2022-10-25T21:09:10Z","number":3419,"mergeCommitSha":"403a29866f36869133a3a0577137ad0c9d2e9dee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3419","title":"Discussion count in stats page should not include slack threads","createdAt":"2022-10-25T21:04:18Z"}
{"state":"Merged","mergedAt":"2022-02-14T21:32:17Z","number":342,"body":"The inimitable Peter Werry once told me, a \"Last-Modified\" response header is better than no header.\r\nZally allows high customization for cases for various types, including header types.","mergeCommitSha":"af266512cac805769c493070b29e1d40dd21d3d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/342","title":"Allow hyphenated header names for responses","createdAt":"2022-02-14T21:30:04Z"}
{"state":"Merged","mergedAt":"2022-10-25T23:03:37Z","number":3420,"body":"This should never happen, but add resilience by falling back on any point if there are no original points.","mergeCommitSha":"3753bdac8ecff99c69794e727018321eceb7427a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3420","title":"API falls back on any sourcepoint if none exist","createdAt":"2022-10-25T21:25:43Z"}
{"state":"Merged","mergedAt":"2022-10-25T23:02:51Z","number":3421,"body":"There's a bunch of things going on in this PR:\r\n\r\n1. Sandbox is removed from Video App\r\n2. This allowed us to ditch all the goofy hacks needed to embed sandboxed apps, so the extra schemes, targets, and configurations are gone.\r\n3. New Accessibility permission added to permissions drop down and is a requirement to run the app\r\n4. New accessibility observer class to walk the browser view tree, looking for an (undocumented) `AXWebArea` element, which has a magical (also undocumented) `AXURL` attribute. This works for both Chrome and Safari","mergeCommitSha":"152c00723525436e21bd19a0f46dfa99e2e81228","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3421","title":"Add URL slupring","createdAt":"2022-10-25T21:26:59Z"}
{"state":"Merged","mergedAt":"2022-10-25T23:03:22Z","number":3422,"body":"<img width=\"253\" alt=\"CleanShot 2022-10-25 at 15 40 30@2x\" src=\"https://user-images.githubusercontent.com/858772/197895540-80b3045e-7b93-48f4-82d5-aef55e7fbfa2.png\">\r\n\r\n<img width=\"400\" alt=\"CleanShot 2022-10-25 at 15 53 01@2x\" src=\"https://user-images.githubusercontent.com/858772/197896996-fd8f5167-9e05-4cfb-9088-73d217d3cb4c.png\">\r\n\r\n\r\n<img width=\"480\" alt=\"CleanShot 2022-10-25 at 15 41 08@2x\" src=\"https://user-images.githubusercontent.com/858772/197895602-ae7b2116-e750-42a5-981f-20d2ba6d7a6b.png\">\r\n","mergeCommitSha":"22ac31314548310d3a77b27cf8ff6566b3e0dfb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3422","title":"Change 'discussions' to 'insights' in Hub","createdAt":"2022-10-25T22:40:15Z"}
{"state":"Merged","mergedAt":"2022-10-25T23:03:57Z","number":3423,"mergeCommitSha":"ff6331a92bcbd00e1abaa972e90bddcf693a2201","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3423","title":"Dont do it in the constructor","createdAt":"2022-10-25T22:46:05Z"}
{"state":"Closed","mergedAt":null,"number":3424,"body":"A few problems needed to be solved:\r\n1. State changes are not causing re-renders on dropdown.\r\n2. Need to make loading text sticky on bottom of list.","mergeCommitSha":"4d23adcda63e967e55893846d16b057b0ed86726","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3424","title":"Add slack channel selection on load","createdAt":"2022-10-25T22:53:57Z"}
{"state":"Merged","mergedAt":"2022-10-25T23:36:22Z","number":3425,"mergeCommitSha":"4d7f688b5f45dcd16e6a4681a119018aa21d157f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3425","title":"Remove log attribute","createdAt":"2022-10-25T23:26:02Z"}
{"state":"Merged","mergedAt":"2022-10-27T16:24:26Z","number":3426,"body":"Generate and open urls for RepoFileRefernces on web.\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/197903723-9871a542-20a0-41eb-8e7f-69ed1e14d95b.mp4\r\n\r\n","mergeCommitSha":"0fb1dc54bd9835bf0ac02cb2269fe84cacec7570","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3426","title":"Setup basic web navigation for repo references","createdAt":"2022-10-25T23:39:46Z"}
{"state":"Closed","mergedAt":null,"number":3427,"body":"Now I just need to figure out how to run this new test by itself, it isn't clear to me how TopicApiDelegateImplTest is getting run. \r\n\r\nTest with Gradle only seems to run the compat test?","mergeCommitSha":"3e547ad240f05606821f7cb15e7dac60317ecad3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3427","title":"TopicService: Add identity to TopicModel + API Test","createdAt":"2022-10-26T00:03:10Z"}
{"state":"Merged","mergedAt":"2022-10-26T00:32:18Z","number":3428,"body":"Fixes problem with zip64 causing caching to go bork bork because the files are so large.\r\nDisable zip64 jar caching.","mergeCommitSha":"a886ea8fc40504106ce62dfceba97b30e0621d73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3428","title":"Do not cache shadowjar","createdAt":"2022-10-26T00:14:42Z"}
{"state":"Merged","mergedAt":"2022-10-27T19:54:30Z","number":3429,"body":"- Allows creation of a Blue Bubble with arbitrary numbers of FileMarks\r\n- GZIP encoded snippets for blue bubbles","mergeCommitSha":"2870ca28e1fc408e6aaa755d9ace6ee218992601","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3429","title":"Use new FileMark-supported thread and message APIs","createdAt":"2022-10-26T02:11:01Z"}
{"state":"Merged","mergedAt":"2022-02-14T21:46:32Z","number":343,"body":"Fix version","mergeCommitSha":"436554a5d365b36191b9ad44fc28402d895591d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/343","title":"FixStuff2","createdAt":"2022-02-14T21:38:08Z"}
{"state":"Merged","mergedAt":"2022-10-26T02:39:43Z","number":3430,"mergeCommitSha":"6a912d34f4bd69612f6ab31f64ac892cc69997a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3430","title":"Reduce jar size for standford models","createdAt":"2022-10-26T02:28:05Z"}
{"state":"Merged","mergedAt":"2022-10-26T02:54:53Z","number":3431,"mergeCommitSha":"7140f298c470fcc398f2c42e406e8343be7ee9c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3431","title":"Disable docker caching","createdAt":"2022-10-26T02:54:28Z"}
{"state":"Merged","mergedAt":"2022-10-26T02:58:34Z","number":3432,"mergeCommitSha":"23c254175944edcf225c8702a43b3d6c810cf012","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3432","title":"Trigger on workflow change","createdAt":"2022-10-26T02:58:10Z"}
{"state":"Merged","mergedAt":"2022-10-26T05:00:13Z","number":3433,"body":"We need to get this out to customers as quickly as possible so that we catch them before they update to Ventura. Apple makes the claim that apps signed by the same team should be able to update each other without jumping through this hoop, but that's clearly wrong, or at least there's more to the story (probably sandboxing issues)\r\n\r\nOnly place I could find where this is documented:\r\n\r\nhttps://developer.apple.com/videos/play/wwdc2022/10096/?time=314\r\n\r\n","mergeCommitSha":"25198f646244fd0b57954de22c4ff73a8e0431dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3433","title":"Allow updates from helper process on Ventura","createdAt":"2022-10-26T04:33:06Z"}
{"state":"Merged","mergedAt":"2022-10-26T05:28:33Z","number":3434,"mergeCommitSha":"a6d46b314cc3bd30ea7aff602ed55155af36e785","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3434","title":"Add AllowedPackages to security exception so installer can do its thing","createdAt":"2022-10-26T05:27:22Z"}
{"state":"Merged","mergedAt":"2022-10-26T05:48:01Z","number":3435,"mergeCommitSha":"e0d6c818b0097cb144443f681684cc7094ec8baf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3435","title":"Bumpity Bump","createdAt":"2022-10-26T05:47:42Z"}
{"state":"Merged","mergedAt":"2022-10-26T16:12:53Z","number":3436,"mergeCommitSha":"63037d016517deee38416726f47cb900157fedc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3436","title":"Try Apple's broken-looking configuration for NSUpdateSecurityPolicy","createdAt":"2022-10-26T16:11:51Z"}
{"state":"Merged","mergedAt":"2022-10-26T16:14:27Z","number":3437,"mergeCommitSha":"5e56d0babc8631e0e9e89961296493594eec0853","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3437","title":"Bumpity Bump","createdAt":"2022-10-26T16:14:11Z"}
{"state":"Merged","mergedAt":"2022-10-26T16:16:39Z","number":3438,"mergeCommitSha":"0f8e220a7c41ad00adfc8ca78534b05dac68835b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3438","title":"Revert","createdAt":"2022-10-26T16:16:24Z"}
{"state":"Merged","mergedAt":"2022-10-26T17:00:49Z","number":3439,"mergeCommitSha":"c3fcc9e0d285f98bb376b654a0ff2e8d9cfa2c1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3439","title":"Verification bump","createdAt":"2022-10-26T17:00:41Z"}
{"state":"Merged","mergedAt":"2022-02-14T22:51:42Z","number":344,"mergeCommitSha":"3a10c61ca87b8e0b47ddb4590fd33b60e8b8fb7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/344","title":"Fix header params being included in locations","createdAt":"2022-02-14T22:46:54Z"}
{"state":"Merged","mergedAt":"2022-10-26T20:34:00Z","number":3440,"body":"<img width=\"355\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/198099779-818e242e-5888-475f-9da2-a98b2b20bdbc.png\">","mergeCommitSha":"ebab3227d3350af036bb626a269a3e9a7b8e59d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3440","title":"Add stream for slack channels loading state","createdAt":"2022-10-26T17:54:51Z"}
{"state":"Merged","mergedAt":"2022-10-26T19:39:43Z","number":3441,"mergeCommitSha":"07526010c255772fb2dd7ba21a4688d98d723982","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3441","title":"Show multiple FileMarks in admin web","createdAt":"2022-10-26T18:37:02Z"}
{"state":"Merged","mergedAt":"2022-10-26T20:56:40Z","number":3442,"mergeCommitSha":"57e17f423496694fd86b82f0fdb7ea8d04b596c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3442","title":"Create PullRequestTopicHistogramService","createdAt":"2022-10-26T18:41:02Z"}
{"state":"Merged","mergedAt":"2022-10-26T21:18:31Z","number":3443,"body":"Does the following:\r\n1. Adds the app group to all embedded apps\r\n2. Adds the team-identifier entitlement  to all the apps\r\n3. Adds the `NSUpdateSecurityPolicy` exception to all the apps\r\n4. Sets all apps to be UI agents and drops the LSBackground setting for the helper app","mergeCommitSha":"c636260aa342dec80fb971da58aedac974483278","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3443","title":"Trying all the things for install fix","createdAt":"2022-10-26T18:41:29Z"}
{"state":"Merged","mergedAt":"2022-10-26T22:42:09Z","number":3444,"body":"Just playing around social network model to show recommended peers.\nMight drive some funtionality in future, might go nowhere.","mergeCommitSha":"1bbbb17457b764f1c376631880e3489bf20d99f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3444","title":"Show recommended peers in admin web","createdAt":"2022-10-26T19:07:22Z"}
{"state":"Merged","mergedAt":"2022-10-26T20:37:01Z","number":3445,"body":"We currently have a t3.medium with an older copy of prod DB setup for ML work. The team needs access to latest data. Looking at the replica instance, its CPU usage doesn't exceed 15% so instead of spending money on something that is barely used we should add another read replica. \r\n\r\nPusher has been hammering the read replica so by doing this for a price difference of 2 cents an hour we double our read-only capacity. Also the team gets access to latest data for their research queries","mergeCommitSha":"78ee02e604611ddf6eeb430656cf9ee4f44972a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3445","title":"add more reader instances","createdAt":"2022-10-26T19:53:09Z"}
{"state":"Merged","mergedAt":"2022-10-26T21:02:22Z","number":3446,"mergeCommitSha":"ee06c33e3d75ed62883933bea2010967e1cc38a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3446","title":"Implement getRecommendedTopics","createdAt":"2022-10-26T20:16:53Z"}
{"state":"Merged","mergedAt":"2022-10-26T20:45:05Z","number":3447,"body":"Replace /discussions routes with /insights\r\nAdd Redirects for old routes \r\nFormat routing better leveraging the `<Outlet />` component","mergeCommitSha":"b766a68b28b22511268416623110b487bf0ca108","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3447","title":"Update dashboard routes with insights","createdAt":"2022-10-26T20:31:17Z"}
{"state":"Merged","mergedAt":"2022-11-04T23:09:19Z","number":3448,"body":"Setup VSCode to reconnect to video app after permissions mode are validated.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/*********-ccea1374-e070-4fff-b9e1-be8d87e832df.mp4\r\n\r\n","mergeCommitSha":"2977d0c413ae660e71762d88f5d3e506aea466a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3448","title":"Add Unconnected Permissions State","createdAt":"2022-10-26T21:14:48Z"}
{"state":"Closed","mergedAt":null,"number":3449,"body":"Holding onto this if current configuration doesn't work","mergeCommitSha":"820cf6085ffeb928eeb9c04a174e2115c7216412","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3449","title":"Slightly different NSUpdateSecurityPolicy config","createdAt":"2022-10-26T21:26:37Z"}
{"state":"Merged","mergedAt":"2022-02-15T01:01:51Z","number":345,"body":"Since the existing VScode Tree View is pretty much unconfigurable, we are going to have to do our implementation.\r\n\r\nTo that end, this pr includes a basic subset of components for a TreeView Editor.\r\n\r\n1. A TreeItem which is a node item in the tree.\r\n2. A TreeItemCaret which indicates expansion of tree node.\r\n\r\nNext Steps:\r\n1. Planning to add a TreeView component.\r\n2. Planning on piggy-backing off existing VSCode node provider models for tree view.\r\n3. Planning on webview with this stuff.\r\n4. Styling.\r\n\r\n<img width=\"690\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/153961234-64faa1fe-277d-41f6-991c-44eb9539c6ab.png\">\r\n","mergeCommitSha":"a6ef72ad9680ef07351167ebdb3309e0b7474ed2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/345","title":"Add treeview components","createdAt":"2022-02-14T23:01:06Z"}
{"state":"Merged","mergedAt":"2022-10-26T21:29:07Z","number":3450,"mergeCommitSha":"b99195bc8b66c35f4fd63d3353d0fc1051aec450","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3450","title":"Bump","createdAt":"2022-10-26T21:28:33Z"}
{"state":"Merged","mergedAt":"2022-10-26T22:39:17Z","number":3451,"body":"https://linear.app/unblocked/issue/UNB-709/get-threadsmine-is-returning-500-if-any-of-the-threads-are-missing\r\nhttps://linear.app/unblocked/issue/UNB-716/recommended-thread-500s\r\n\r\nThe goal is to goal prevent API from throwing 500. I'll fix the core issue next.","mergeCommitSha":"7c077a1bd0c0bf9cb8ea1a1e975d876f3a873be9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3451","title":"GET threads/mine is returning 500 if any of the threads are missing sourcepoints","createdAt":"2022-10-26T22:23:04Z"}
{"state":"Merged","mergedAt":"2022-10-31T18:53:22Z","number":3452,"body":"* Remove title field from blue bubbles; double block content as title\r\n    * NOTE: There's some temporary client logic to handle this but this should ultimately be handled by the backend (linear task: https://linear.app/unblocked/issue/UNB-703/backfill-thread-titles-with-block-content-if-createthreadrequesttitle)\r\n* Remove the title field from the message editing form \r\n* Collapse the code block in thread creation by default (only in vscode)\r\n\r\n<img width=\"515\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/198151587-64b83733-1ebd-42e5-8ca6-1ea47bbb32be.png\">\r\n<img width=\"627\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/198151612-6d51835a-d557-4488-9057-d0c453d218f7.png\">\r\n","mergeCommitSha":"068bf8143b2b3aa2785e335c9e8ae30777225576","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3452","title":"Update create blue bubble forms","createdAt":"2022-10-26T22:40:43Z"}
{"state":"Closed","mergedAt":null,"number":3453,"body":"HTTP request bomb results in VSCode hang, caused by PUT /logs, caused by sourcepoint snippet check failure\n\nhttps://linear.app/unblocked/issue/UNB-714/fix-snippet-check-failure","mergeCommitSha":"3de72b9764b73d614bcc5479efd9a615a0f62b19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3453","title":"Fix Snippet check failure","createdAt":"2022-10-26T22:41:57Z"}
{"state":"Merged","mergedAt":"2022-10-26T23:52:07Z","number":3454,"mergeCommitSha":"4212c26bc07deb5c6bc0163e9000d3e99c5b6fa8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3454","title":"Reduce chunk size","createdAt":"2022-10-26T23:16:06Z"}
{"state":"Merged","mergedAt":"2022-11-08T18:12:00Z","number":3455,"body":"Throttle the amount of logs we send over the network from VSCode, to a maximum of 5 per second for each context.","mergeCommitSha":"c594f86116a1c000d6230cb46c972983ba696c5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3455","title":"Throttle network logging","createdAt":"2022-10-27T00:14:52Z"}
{"state":"Merged","mergedAt":"2022-10-27T00:49:06Z","number":3456,"mergeCommitSha":"fda6f14014b0639b9b39d70608bbb36fe64f9770","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3456","title":"Remove debug logging","createdAt":"2022-10-27T00:48:40Z"}
{"state":"Merged","mergedAt":"2022-10-27T03:47:11Z","number":3457,"body":"The way we are doing upserts is plain wrong in the SQL world because we are not providing enough locking pragmas and using the correct isolation to ensure concurrent updates do not hit transactional conflicts (duplicate key conflicts)\r\nI actually noticed this out in the wild so it's not just theory.\r\n\r\nhttps://sqlperformance.com/2020/09/locking/upsert-anti-pattern\r\n\r\nThe correct approach is to use the Postgres Insert On Conflict paradigm.\r\nTo achieve this:\r\n1. We add extension functions to do upserts.\r\n2. We updated the Entity Cache to handle upsert statements.\r\n3. We update some non-critical code (slack models) to test out this logic.\r\n\r\nThis highly optimizes the logic as we do not have to do a select/insert/fetch.","mergeCommitSha":"754b4eab034a9b23eedbe5fbd6304c238e4764e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3457","title":"Add upsert functionality to Exposed and fix/optimize upserts","createdAt":"2022-10-27T02:02:04Z"}
{"state":"Merged","mergedAt":"2022-10-27T05:50:44Z","number":3458,"mergeCommitSha":"3d1bc5c5e950ecdf4bd1da8e922df379fa226cbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3458","title":"Drop chunk size to 5","createdAt":"2022-10-27T05:48:35Z"}
{"state":"Merged","mergedAt":"2022-10-27T06:00:01Z","number":3459,"mergeCommitSha":"11da018ebe2b38a5db263af1be7d04087d6043ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3459","title":"Need more admin console memory","createdAt":"2022-10-27T05:56:15Z"}
{"state":"Closed","mergedAt":null,"number":346,"mergeCommitSha":"a3431514200b5aa6c87e88ce76d86a29f6a2ce61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/346","title":"update","createdAt":"2022-02-14T23:20:17Z"}
{"state":"Merged","mergedAt":"2022-10-27T06:18:06Z","number":3460,"mergeCommitSha":"207a65ba70342d5d781b149656d927ef9e2bcc1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3460","title":"udpate","createdAt":"2022-10-27T06:16:34Z"}
{"state":"Merged","mergedAt":"2022-10-27T06:38:52Z","number":3461,"body":"- udpate\r\n- add honeycomb to admin\r\n","mergeCommitSha":"b960280539eec7a88adee37601118b4201cbf823","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3461","title":"AddHoneycomb","createdAt":"2022-10-27T06:36:50Z"}
{"state":"Merged","mergedAt":"2022-10-27T22:19:51Z","number":3462,"body":"Temporary system to enable feature flags per team on VSCode","mergeCommitSha":"d145dc9d95ff54025ca5c250a2dccbde74c28712","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3462","title":"EV based feature flags for VSCode","createdAt":"2022-10-27T18:11:12Z"}
{"state":"Merged","mergedAt":"2022-10-27T21:03:48Z","number":3463,"body":"Applies the same common word filtering and lemmatization to the topics received from the client as we do to PR and slack content. \r\n\r\nThis means the response from the server may not be a strict subset of the inputs. For example if one the input topics is `sourcemarks` it will be returned as `sourcemark` by the server.\r\n\r\nAlso this will occur in the API server so we're using the same Mr. Stanford library as the admin console.","mergeCommitSha":"66973dbea01f38fd377622a6f0d94ae1fb2c9cd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3463","title":"Lemmatize and remove common words from topics send from client","createdAt":"2022-10-27T18:26:05Z"}
{"state":"Merged","mergedAt":"2022-10-27T18:58:11Z","number":3464,"body":"Adding top level slack thread url.\r\nBoth client and backend.","mergeCommitSha":"2ccf6f0e2f66f87b399b56352defa683cd968a46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3464","title":"Add basic top-level slack thread url","createdAt":"2022-10-27T18:34:49Z"}
{"state":"Merged","mergedAt":"2022-10-27T18:54:44Z","number":3465,"body":"The new DB point should get the snippet from API point or the parent API mark.","mergeCommitSha":"5199a75c6bd8f58aad5af3a53225d22c7921fd75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3465","title":"Fix CreateThread snippet persistence on point","createdAt":"2022-10-27T18:52:33Z"}
{"state":"Closed","mergedAt":null,"number":3466,"body":"When we changed the message top margin for the wide viewport, the narrow viewport was also affected as a breakpoint didn't exist. This fixes it. It goes from 8pts to 2pts.\r\n\r\n![CleanShot 2022-10-27 at 14 17 18](https://user-images.githubusercontent.com/13353189/198400088-0e727b95-928d-4168-9c48-0e8d504243b6.gif)\r\n","mergeCommitSha":"59a69d1bc465f9de1c020223a6ea46e4df4e97ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3466","title":"Adding breakpoint for message top margin","createdAt":"2022-10-27T21:22:22Z"}
{"state":"Merged","mergedAt":"2022-10-27T22:03:58Z","number":3467,"mergeCommitSha":"384c95886b550b64d186504fd9e62b6064750ace","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3467","title":"trace logging","createdAt":"2022-10-27T21:49:57Z"}
{"state":"Merged","mergedAt":"2022-10-31T18:55:40Z","number":3468,"body":"### Preamble\r\n\r\nThere doesn't seem to be a (documented) method of untangling the TCC subject from the host app. The impact is that it's `Unblocked.app` asking for permissions for Camera/Mic/Recording, and `Unblocked Video.app` asking for Accessibility permissions, even though it's really `Unblocked Video.app` actually using those permissions. Seems like a bug in TCC.\r\n\r\n### Summary\r\nThis PR does 3 things:\r\n1. Polls the background for accessibility and recording permissions updates from the video app. We're doing this because on Monterey the video app can continue on without restarting once recording permissions are granted to the host app (Unblocked.app).\r\n2. On Ventura a restart is required, but it will restart _the wrong app_ (Unblocked.app instead of Unblocked Video.app). This PR drops a default in shared user prefs that the video app needs a restart, then the hub picks that up when it restarts and restarts the video app\r\n3. A sandbox breakout is required to do this, so there's a `temporary-exception` entitlement added that is scoped to the video app.\r\n\r\n\r\nVerified that this works.","mergeCommitSha":"c87482762320c79bebb3cf6009a838bc6d0f126e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3468","title":"Restart walkthrough app after recording permissions granted","createdAt":"2022-10-27T21:51:53Z"}
{"state":"Merged","mergedAt":"2022-10-27T21:59:15Z","number":3469,"mergeCommitSha":"010b31bcc616563226935824e184408138ba4a19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3469","title":"up api service","createdAt":"2022-10-27T21:59:02Z"}
{"state":"Merged","mergedAt":"2022-02-14T23:26:26Z","number":347,"mergeCommitSha":"5cc48d21bdc5d6ec31698690db101123e203b68b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/347","title":"Fix unstable test","createdAt":"2022-02-14T23:22:03Z"}
{"state":"Merged","mergedAt":"2022-10-27T22:06:13Z","number":3470,"body":"<img width=\"1102\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/198406072-ba6c87d5-24cd-49bd-929c-42be69382435.png\">\r\n\r\nFix redirect links; should also default to redirecting back to index instead of `Nothing to see here`","mergeCommitSha":"684bbdf74256b6e73bb2c85ae7ba3a9eeb41f4ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3470","title":"Fix broken dashboard redirect","createdAt":"2022-10-27T21:59:04Z"}
{"state":"Merged","mergedAt":"2022-10-27T22:18:23Z","number":3471,"body":"I previously introduced an optimization to skip sanitization of a mark (which is expensive) if\nwe had already sanitized at least one point in the _repo_. See #3309.\n\nHowever, if the sanitized point happened to be a commit that was subsequently forcibly\noverwritten by another commit (due to rebase, common for Graphite workflows) then the sanitizer\nskipped sanitization of points are actually in the tree. The SM engine then thought that there\nwere no trusted points in the tree.\n\nSimple fix; only skip point sanitization if we have already sanitized at least one point in\nthe _tree_, not _repo_.\n\nhttps://linear.app/unblocked/issue/UNB-707/sourcemark-resolution-fault-for-several-sidebar-threads","mergeCommitSha":"e6c7d31f57d23673eefb875be5b9b27322ee5ec0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3471","title":"SourceMark resolution faulted incorrectly when a trusted point existed for a commit outside tree","createdAt":"2022-10-27T22:14:06Z"}
{"state":"Merged","mergedAt":"2022-10-27T22:42:41Z","number":3472,"mergeCommitSha":"b5e086fdf92b4d68c1c16241b8424790937f0b28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3472","title":"rename stuff","createdAt":"2022-10-27T22:36:02Z"}
{"state":"Merged","mergedAt":"2022-10-31T20:57:22Z","number":3473,"body":"Backup launch command for Walkthrough App when `open -b` fails. ","mergeCommitSha":"eb29a5ab77e33c445562db067e2cd860d8e6f7c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3473","title":"Backup video app launch","createdAt":"2022-10-27T22:41:27Z"}
{"state":"Merged","mergedAt":"2022-10-27T23:34:50Z","number":3474,"mergeCommitSha":"3bb16bdbace3ccc40be353c047eb6adc7520f7fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3474","title":"Dont return single character topics","createdAt":"2022-10-27T23:26:13Z"}
{"state":"Merged","mergedAt":"2022-11-01T18:24:26Z","number":3475,"body":"Added File icons.\r\n\r\nManually mapping file types -> Icons.\r\n\r\nUsing list from `https://github.com/dyne/file-extension-list`\r\n\r\n```\r\ngroupings could be as broad as:\r\nimages\r\ncode\r\nvideos\r\nother files (blank)\r\n```\r\n\r\n<img width=\"304\" alt=\"CleanShot 2022-10-31 at 17 29 44@2x\" src=\"https://user-images.githubusercontent.com/1553313/199133778-78cbefd3-fafa-498a-85e4-9db1d4df702a.png\">\r\n\r\n","mergeCommitSha":"82405701fd24cd7f0c588ea4e9a92a71fffcfeba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3475","title":"Video metadata file icons","createdAt":"2022-10-27T23:30:55Z"}
{"state":"Merged","mergedAt":"2022-10-28T18:13:02Z","number":3476,"mergeCommitSha":"d87f2749c3577996c6361b640c122557c0a6e915","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3476","title":"Backfill thread titles with block content if CreateThreadRequest.title is empty","createdAt":"2022-10-27T23:38:31Z"}
{"state":"Merged","mergedAt":"2022-10-27T23:50:24Z","number":3477,"mergeCommitSha":"5f535176bea30b5b990471f9d92117515146bdb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3477","title":"Bump chunk size up to 25","createdAt":"2022-10-27T23:43:28Z"}
{"state":"Merged","mergedAt":"2022-10-27T23:50:40Z","number":3478,"mergeCommitSha":"281d150a1111dcd646334fbac11ed9e5bd5b2a62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3478","title":"Increase slack polling","createdAt":"2022-10-27T23:50:24Z"}
{"state":"Merged","mergedAt":"2022-10-28T00:44:53Z","number":3479,"body":"* enable for our team first, if it works as expected I'll make another PR to uncomment Expo's team ","mergeCommitSha":"2285d2e5e1cc1fb6bf1052d9b10bcecd8005eea6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3479","title":"Add whitelist for slack UI ","createdAt":"2022-10-27T23:51:18Z"}
{"state":"Closed","mergedAt":null,"number":348,"mergeCommitSha":"cd27728e1fa1c21741eec65624965c3e1976904e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/348","title":"Use correct java","createdAt":"2022-02-14T23:46:42Z"}
{"state":"Merged","mergedAt":"2022-10-28T19:21:38Z","number":3480,"mergeCommitSha":"645daa779c1781d3b85a36fed478e8c726e1a70e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3480","title":"Enable slack for expo team ","createdAt":"2022-10-28T01:00:18Z"}
{"state":"Merged","mergedAt":"2022-10-28T01:59:50Z","number":3481,"body":"Should be using bagtch insert\r\nSome slow queries\r\n","mergeCommitSha":"be474cda961307485b6918c74e0e36ea13084d30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3481","title":"Message mentions is slow","createdAt":"2022-10-28T01:30:52Z"}
{"state":"Merged","mergedAt":"2022-10-28T02:11:23Z","number":3482,"body":"Token doesn't really change unless user changes it.\r\nWith that in mind, We should be caching token.","mergeCommitSha":"e7e6ab204ca6d562bf08569ed159c3ac1ed4f07e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3482","title":"Optimize user token","createdAt":"2022-10-28T02:02:21Z"}
{"state":"Merged","mergedAt":"2022-10-28T03:57:26Z","number":3483,"mergeCommitSha":"8d4a0cc94a071a10a2ac68aacd186ecb083d4f06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3483","title":"Bump prod admin console memory","createdAt":"2022-10-28T03:52:13Z"}
{"state":"Merged","mergedAt":"2022-10-28T04:10:08Z","number":3484,"mergeCommitSha":"39aa8ec3967e5d0b674d06017f606a733dd3f79e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3484","title":"Be smart with mentions","createdAt":"2022-10-28T04:09:01Z"}
{"state":"Merged","mergedAt":"2022-11-02T21:38:57Z","number":3485,"body":"#### Proposal\r\n\r\nGet remote-controlled client config for the currently authorized user. This API operation takes no explicit parameters. The remote config service implicitly uses the authorized _person_, _identity_, and _team(s)_ to control behaviour.\r\n\r\nThis API is user-centric, not team-centric in order to provide a consistent experience to the authorized user. The consequence for a user who is a current member of multiple teams is that property conflicts can occur for team properties. The remote config service resolves the conflicts deterministically.\r\n\r\n#### Sample response\r\n\r\n```json\r\n{\r\n  \"capabilities\": {\r\n    \"VideoFeatureEnabled\": true,\r\n    \"NetworkLoggingEnabled\": false\r\n  },\r\n  \"quantities\": {\r\n    \"NetworkLoggingLimitPerMinute\": 60,\r\n    \"ApiRetryBackoffMilliseconds\": 300,\r\n    \"MaxSidebarThreads\": 20\r\n  }\r\n}\r\n```\r\n\r\n#### Admin web changes\r\n\r\nNew **Config** page for global config. The **Team** and **Person** pages are also updated.\r\n\r\n<img width=\"856\" alt=\"Screenshot 2022-11-02 at 08 19 32\" src=\"https://user-images.githubusercontent.com/1798345/199528907-c547b8f8-62ed-4c49-bced-c76f92adfe09.png\">\r\n\r\nhttps://linear.app/unblocked/issue/UNB-238/control-client-polling-using-remote-client-config","mergeCommitSha":"0a0f49bcf7cb87bbdd9fa2ed8c5337920e2a52a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3485","title":"Remote controlled client config","createdAt":"2022-10-28T07:03:30Z"}
{"state":"Merged","mergedAt":"2022-10-28T18:22:47Z","number":3486,"mergeCommitSha":"68a9669d937c5a42093464f1edf92c7a266c71b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3486","title":"Always make sure there are teams to nav","createdAt":"2022-10-28T17:55:16Z"}
{"state":"Merged","mergedAt":"2022-10-28T18:57:39Z","number":3487,"body":"Determined that a large amount of time was used to get message permalink.\r\nThat permalink can easily be deterministically resolved once we have the base protocol and host information.\r\n\r\nCut local ingestion from 13 minutes to 4 minutes.","mergeCommitSha":"a67b7277f9934a1a76983405e8b6fdf27ff2e0f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3487","title":"Optimize slack ingestion","createdAt":"2022-10-28T18:49:08Z"}
{"state":"Merged","mergedAt":"2022-10-28T20:03:23Z","number":3488,"mergeCommitSha":"fc4d34333cd51c7a34871f9b724c714ee1ecb546","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3488","title":"Save identity for topic","createdAt":"2022-10-28T18:49:41Z"}
{"state":"Closed","mergedAt":null,"number":3489,"body":"- Optimize slack ingestion\r\n- update\r\n","mergeCommitSha":"42045443d1f6370757459e5d979607c43ff7d61c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3489","title":"MinorCleanup","createdAt":"2022-10-28T19:02:01Z"}
{"state":"Merged","mergedAt":"2022-02-14T23:52:22Z","number":349,"mergeCommitSha":"8294b31cd54689273343a3d19ed0a1ebc98e3a1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/349","title":"Add readme","createdAt":"2022-02-14T23:47:39Z"}
{"state":"Merged","mergedAt":"2022-10-28T19:11:57Z","number":3490,"mergeCommitSha":"ee587eaa303aff936c219efd3e4c42c69a3eb31e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3490","title":"update","createdAt":"2022-10-28T19:03:08Z"}
{"state":"Merged","mergedAt":"2022-10-28T20:08:51Z","number":3491,"mergeCommitSha":"cc97cb0b8ebcd16855d1fba0c5cbf981be29d15e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3491","title":"Increase slack ingestion memory","createdAt":"2022-10-28T20:08:47Z"}
{"state":"Merged","mergedAt":"2022-10-28T21:27:24Z","number":3492,"body":"* Fix search results self-refreshing on search (refreshing itself to the full list despite the input value)\r\n* Allow option to not close the dropdown on item click (i.e. for multiple selects)\r\n* Fix autofocus on the text search input ","mergeCommitSha":"9b65375c61b4ec2d75c7ab0409747a6959adbc0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3492","title":"Slack dashboard bug fixes","createdAt":"2022-10-28T20:48:42Z"}
{"state":"Merged","mergedAt":"2022-10-28T21:34:32Z","number":3493,"mergeCommitSha":"9ea72b4b0b800b4cb42d5dad40c803e01bb6f6a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3493","title":"Allow getRecommendedTopics when impersonating","createdAt":"2022-10-28T20:49:18Z"}
{"state":"Merged","mergedAt":"2022-10-28T21:02:04Z","number":3494,"body":"- Should be parsing all slack pull requests in a message.\r\n- Optimize pull requsts\r\n","mergeCommitSha":"cae2191a039b4bffe22cad9a70b6ec3ce38a7fed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3494","title":"SlackPullRequests","createdAt":"2022-10-28T20:55:17Z"}
{"state":"Merged","mergedAt":"2022-10-28T21:22:27Z","number":3495,"body":"This reverts commit 24b51c7868e874a6cd059e475705b20652f4f57b.\r\n\r\nBroke the hub build.","mergeCommitSha":"533884ba38d621a1e4cdd320dfbae0ae8cb6c1a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3495","title":"Revert \"Fix open api code gen (#3252)\"","createdAt":"2022-10-28T21:09:22Z"}
{"state":"Merged","mergedAt":"2022-10-28T21:12:39Z","number":3496,"mergeCommitSha":"d5ad976fb1ee641e759bc6d9fcc55ecbd84da259","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3496","title":"Sort files","createdAt":"2022-10-28T21:12:04Z"}
{"state":"Merged","mergedAt":"2022-10-28T23:13:13Z","number":3497,"body":"This attempts to flip the accessibility switch on every app that the user brings to the foreground and will slurp all the URLs.\r\n\r\nTested and confirmed working with:\r\n- Slack\r\n- Notion\r\n- Linear\r\n- VSCode\r\n- Safari","mergeCommitSha":"1f3dadd78a43ff5023b40e31b066239ce46455cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3497","title":"Let's slurp EVERYTHING","createdAt":"2022-10-28T21:46:02Z"}
{"state":"Merged","mergedAt":"2022-11-10T17:13:11Z","number":3498,"body":"Add basic support for Video transcription.\r\n\r\nTranscription rows highlight as video plays\r\n\r\n<img width=\"1485\" alt=\"CleanShot 2022-10-28 at 14 20 19@2x\" src=\"https://user-images.githubusercontent.com/1553313/198737293-bd7c27b5-246e-4bd8-a668-9ba273fb0565.png\">\r\n\r\nhttps://user-images.githubusercontent.com/1553313/198737461-a13f3319-367b-40e5-837e-b8d4f01101aa.mp4\r\n\r\n\r\n\r\n","mergeCommitSha":"efdcac953fdd064304ad1a8048130e3737fa4de2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3498","title":"Support Transcriptions in Video","createdAt":"2022-10-28T22:04:53Z"}
{"state":"Merged","mergedAt":"2022-10-28T22:13:18Z","number":3499,"mergeCommitSha":"0a64e2990635ad37c57d2402992006dc40748203","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3499","title":"Bump displayed number of recommendations","createdAt":"2022-10-28T22:07:18Z"}
{"state":"Merged","mergedAt":"2022-01-14T21:57:39Z","number":35,"body":"![image](https://user-images.githubusercontent.com/13431372/149426854-e9ba856e-7b63-44ef-8e0a-ab9baaf9ff2d.png)\r\n","mergeCommitSha":"9771e9196033dc873adea12f53dd388c1f56e585","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/35","title":"User icons and stack","createdAt":"2022-01-13T23:50:12Z"}
{"state":"Merged","mergedAt":"2022-02-15T19:11:27Z","number":350,"body":"Tested this with Peter's If-Modified-Since header paramter.\r\nAlso cleaned shit up. :)\r\n\r\nFYI (if not required):\r\n\r\n    suspend fun login(context: PipelineContext<Unit, ApplicationCall>, input: Paths.login, ifModifiedSince: kotlin.String?)\r\n","mergeCommitSha":"76ffee11d38e6b2443b5fc8ebc1a99e018e9f035","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/350","title":"This pr adds code generator for header parameters","createdAt":"2022-02-15T18:48:46Z"}
{"state":"Merged","mergedAt":"2022-10-29T00:03:44Z","number":3500,"mergeCommitSha":"665a3f17e2c20cceaae71563d57b8d4921b32f02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3500","title":"Update TopicsPage","createdAt":"2022-10-28T23:33:36Z"}
{"state":"Merged","mergedAt":"2022-11-01T18:17:28Z","number":3501,"body":"`webFileReference` can be quite noisy and send multiple events in a row for the same source.\r\nThis should help dedupe incoming video references.\r\n\r\nSince references only have a start date and not end date, this should be safe to remove.","mergeCommitSha":"cca04e766f240b3073200fd23cef43dd02ce1476","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3501","title":"Dedupe incoming video references","createdAt":"2022-10-28T23:33:57Z"}
{"state":"Merged","mergedAt":"2022-10-31T17:18:10Z","number":3502,"body":"fixes https://linear.app/unblocked/issue/UNB-725","mergeCommitSha":"8560a41580055d6afc40099fbd3423b18077bdd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3502","title":"Unit tests for snippets and fix web extension createThread 500s","createdAt":"2022-10-31T06:28:48Z"}
{"state":"Merged","mergedAt":"2022-10-31T17:57:36Z","number":3503,"mergeCommitSha":"865fb05d90d96c4bad072320da55da2f04668bf6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3503","title":"Adds embedded video app as dependency, and magically things now build","createdAt":"2022-10-31T16:28:03Z"}
{"state":"Merged","mergedAt":"2022-10-31T17:52:07Z","number":3504,"mergeCommitSha":"b1b54b1f71ddc5b02bc70daac687a49f6a24d043","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3504","title":"Whitelist userclouds team for slack","createdAt":"2022-10-31T17:40:03Z"}
{"state":"Merged","mergedAt":"2022-10-31T19:24:13Z","number":3505,"body":"When one navigates to the same file multiple times within a walkthrough, we generate multiple file references.\r\nWe do *not* want duplicate file marks when viewing the same file multiple times.","mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3505","title":"Dedupe file marks","createdAt":"2022-10-31T17:40:18Z"}
{"state":"Merged","mergedAt":"2022-10-31T18:02:08Z","number":3506,"mergeCommitSha":"2ec8f54cf27b5d2d8bb5a11cd1befac7397c91eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3506","title":"Minor admin console changes","createdAt":"2022-10-31T17:52:16Z"}
{"state":"Merged","mergedAt":"2022-10-31T18:12:01Z","number":3507,"body":"per designs:\r\n<img width=\"1837\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199076282-1c2cc98c-31e7-4eeb-bf52-aef4722ef9ff.png\">\r\n\r\nThis PR just moves existing views around and adds a new base route (i.e. no new functionality)","mergeCommitSha":"85b1ce9cef10f947620089a458eb0885310f6f63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3507","title":"Move invite teammates page to its own base route ","createdAt":"2022-10-31T17:57:24Z"}
{"state":"Merged","mergedAt":"2022-10-31T18:26:51Z","number":3508,"body":"currently the dev/prod dashboard routes will be broken because we need the react router definition of location, not the window location definition","mergeCommitSha":"99b482bcd21ccfd10d57d246fed64e674fd0ffc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3508","title":"Fix location definition","createdAt":"2022-10-31T18:21:03Z"}
{"state":"Merged","mergedAt":"2022-11-01T03:28:46Z","number":3509,"mergeCommitSha":"b4e8b8161e99c7a73bb3c10347f322f9596981db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3509","title":"Reduce polling rate for ingestions","createdAt":"2022-10-31T18:39:06Z"}
{"state":"Merged","mergedAt":"2022-02-15T19:09:09Z","number":351,"body":"Adds a service class to retrieve comments from the rest api. Handles pagination.","mergeCommitSha":"348c3b4f40bb3461b0bc653a9a2237922d368553","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/351","title":"Add logic to retrieve paginated comments from the GitHub rest api","createdAt":"2022-02-15T18:52:52Z"}
{"state":"Merged","mergedAt":"2022-10-31T18:57:42Z","number":3510,"mergeCommitSha":"cf3dc9498578d8d059f1a8fa473fe1a4cdf64c03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3510","title":"Upgrade admin deps","createdAt":"2022-10-31T18:50:53Z"}
{"state":"Merged","mergedAt":"2022-10-31T21:02:49Z","number":3511,"mergeCommitSha":"a76ddb40813006c8cd5c37818830b8d9521d311d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3511","title":"Upgrade kotlin releases","createdAt":"2022-10-31T19:05:50Z"}
{"state":"Merged","mergedAt":"2022-10-31T20:55:43Z","number":3512,"body":"Now that we're rebranding discussions into notes in the UI, this is a good time to remove all instances of old code associated with the previous iteration of 'notes' to avoid confusion (this was a half-baked feature anyway/was never implemented in the API). ","mergeCommitSha":"fccf79df7a321231028c85f5a77eea9badccf450","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3512","title":"Cleanup notes/ directory in vscode","createdAt":"2022-10-31T20:18:35Z"}
{"state":"Merged","mergedAt":"2022-10-31T21:03:32Z","number":3513,"body":"Almost everything is a rename. The main change is in `SearchEventPayload`.","mergeCommitSha":"4b33723890219a222c7768d71e48f1d64dc932c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3513","title":"Generalize search index event to support indexing non-thread objects","createdAt":"2022-10-31T20:47:29Z"}
{"state":"Merged","mergedAt":"2022-10-31T21:03:54Z","number":3514,"mergeCommitSha":"935a931cb2794b897fc4d33570efbf8f4b010e6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3514","title":"update","createdAt":"2022-10-31T21:03:47Z"}
{"state":"Merged","mergedAt":"2022-11-01T00:34:50Z","number":3515,"body":"This is an extension of Jeff/Matt work to get us to 6.2.0.\r\n\r\nWe needed to also get Unblocked Hub building and this PR includes these changes.\r\nNamely:\r\n1. Introduce a template fix that they have fixed in mainline but have not released yet for:\r\nhttps://github.com/OpenAPITools/openapi-generator/pull/13617\r\n2. Introduce minor api change fixes in Hub code.\r\n\r\nTesting:\r\nConfirmed Unblocked Hub works properly via Xcode build and run.\r\n","mergeCommitSha":"a93b5c3ce12c7f9eb1e3fddedd6e9c9930c50332","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3515","title":"Upgrade to openapi 6.2.0","createdAt":"2022-10-31T21:40:50Z"}
{"state":"Merged","mergedAt":"2022-11-01T18:07:55Z","number":3516,"body":"vscode:\r\n<img width=\"1055\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199119358-31e22946-cd6c-4564-825a-b80194dc8c71.png\">\r\n<img width=\"654\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199119600-c2f7e5ea-1d47-4f87-88d5-afa7c5d24ac1.png\">\r\n\r\n\r\ndashboard:\r\n<img width=\"1234\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199119385-181d144b-e6cf-421e-b5ba-c98b8a3760bf.png\">\r\n<img width=\"930\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199119411-3c773594-bdc3-46c0-834c-a9ffbb190c07.png\">\r\n\r\nextension:\r\n<img width=\"1016\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199119268-0b8ec23e-b303-465e-a2ba-a5d6e6a591de.png\">\r\n<img width=\"673\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199119286-aaad5d83-07ea-4586-b45a-06c3c22bacb4.png\">\r\n","mergeCommitSha":"5cf165439b6abedb3eecc1a330641bdb14c2ae50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3516","title":"Replace blue bubble icons","createdAt":"2022-10-31T22:07:05Z"}
{"state":"Merged","mergedAt":"2022-11-02T18:28:41Z","number":3517,"mergeCommitSha":"bbcab5a9d998360cc6bafd643cae9224f123dbce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3517","title":"Sets up recorder for camera mixing","createdAt":"2022-10-31T22:23:46Z"}
{"state":"Merged","mergedAt":"2022-11-01T17:07:47Z","number":3518,"mergeCommitSha":"e1e4ec82f60718536e733c93b9a699db845027a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3518","title":"Replace threadId property with id property","createdAt":"2022-11-01T06:27:14Z"}
{"state":"Merged","mergedAt":"2022-11-01T17:28:48Z","number":3519,"mergeCommitSha":"58ac57193addd78b0ee1e437b2bfca935a370800","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3519","title":"Fix swift incremental","createdAt":"2022-11-01T17:17:23Z"}
{"state":"Merged","mergedAt":"2022-02-15T23:46:40Z","number":352,"body":"Helper class that will hit the GitHub rest API to get file hashes for files in a pull request until either all files have been retrieved or there are no more files in the pull request. This will be used by the `PullRequestReviewThreadService`.","mergeCommitSha":"9e3fb5e988d5bd62b94a163b4911812e702065f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/352","title":"Add logic to retrieve paginated files from the GitHub rest api","createdAt":"2022-02-15T21:49:24Z"}
{"state":"Merged","mergedAt":"2022-11-01T17:42:22Z","number":3520,"mergeCommitSha":"266cf76fd2bc274111d7ccd127e1c5000ade31df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3520","title":"Add files","createdAt":"2022-11-01T17:42:05Z"}
{"state":"Merged","mergedAt":"2022-11-01T17:53:46Z","number":3521,"body":"This reverts commit 6c74d781b4020ac7621b1d9c96588a5c37482b24.\r\n\r\n# Conflicts:\r\n#\tprojects/libs/lib-slack-ingestion/src/main/kotlin/com/nextchaptersoftware/slack/ingestion/fixtures/SlackIngestionFixtures.kt\r\n","mergeCommitSha":"2953996a6eb284fcfc943d5c97c827ce9e59d4d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3521","title":"Revert \"HardcodeCreds (#3399)\"","createdAt":"2022-11-01T17:44:54Z"}
{"state":"Merged","mergedAt":"2022-11-02T21:02:00Z","number":3522,"body":"<img width=\"596\" alt=\"CleanShot 2022-11-01 at 10 33 37@2x\" src=\"https://user-images.githubusercontent.com/1553313/199305509-8b05aed7-872d-4630-9c56-7c5f2e32115f.png\">\r\n","mergeCommitSha":"0ea618074bf79f2dcdd3f9e312fb6b2f687652e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3522","title":"Update Hub Notes Icon","createdAt":"2022-11-01T18:03:04Z"}
{"state":"Merged","mergedAt":"2022-11-01T19:41:34Z","number":3523,"body":"They literally released this today.\r\n\r\nhttps://github.com/OpenAPITools/openapi-generator/releases/tag/v6.2.1\r\n\r\nWe need one of the swift generator fixes.\r\n\r\nThis addresses one of the annoyances with 6.2.0, where we had to maintain a custom runtime.mustache file for swift generation.\r\n","mergeCommitSha":"e8b37a15182410d56004d5109ef6dff7090d8752","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3523","title":"Move to openapi release with bug fixes we need","createdAt":"2022-11-01T18:10:51Z"}
{"state":"Merged","mergedAt":"2022-11-03T22:04:39Z","number":3524,"body":"Adding a `GET` operation to support this UI:\r\n\r\n<img width=\"322\" alt=\"CleanShot 2022-10-31 at 15 24 24@2x\" src=\"https://user-images.githubusercontent.com/1924615/199305627-ec34fa48-dee4-42bf-a343-c45ad699b481.png\">\r\n\r\nResponse is an array of heterogenous objects, which for now is one of two types: `ThreadInfo` and `PullRequest`. This is modelled with a `Content` object that has optional `thread` and `pullRequest` fields since `oneOf` isn't an option for us.\r\n\r\nNotes:\r\n- Turns out `repoID(s)` are not required here since `topicId` is an ID for the database model which has a repo property, so all topics in the database are repo scoped\r\n- `Content.pullRequest` is a `PullRequest` object instead of a `PullRequestInfo` (with threads and TLCs) which matches the existing `getPullRequests`","mergeCommitSha":"a8a2f3c5107cbff70ac43dfeb5a02233167ba7ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3524","title":"Add getTopicRelatedInsights operation","createdAt":"2022-11-01T18:13:37Z"}