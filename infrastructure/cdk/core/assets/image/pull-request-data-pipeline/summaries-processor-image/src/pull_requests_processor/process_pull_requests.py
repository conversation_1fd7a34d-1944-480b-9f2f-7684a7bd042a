import logging
import tempfile
from datetime import datetime
from typing import List

import git_utils.git_utils as git
from llm_content_utils.chatml_content_handler import <PERSON>t<PERSON><PERSON><PERSON>ntHand<PERSON>
from pull_requests_processor.config_loader import Config<PERSON>oader
from pull_requests_processor.llm_constants import LLM_MAX_NEW_TOKENS, LLAMA_MAX_INPUT_TOKENS
from pull_requests_processor.llm_pull_request_summary_prompts import (
    PullRequestSummaryCombinePrompt,
    PullRequestSummaryMapPrompt,
    PullRequestSummarySystemPrompt,
)
from pull_requests_processor.llm_pull_requests_processor_engine import LLMPullRequestsProcessorEngine
from pull_requests_processor.path_constants import PROCESS_INPUT_GLOB
from pull_requests_processor.pull_request_constants import DEFAULT_BATCH_SIZE
from pull_requests_processor.pull_request_loader import PullRequestLoader
from pull_requests_processor.pull_request_summary_results_writer import PullRequestSummaryResultsWriter
from pull_requests_processor.pull_request_types import PullRequestSummaryResult

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
)


def load_config() -> ConfigLoader:
    """Load task config from environment variables."""
    return ConfigLoader()


def clone_repo(repo_http_clone_url: str, repo_clone_auth: str = None) -> str:
    """Clone the repository into the local directory."""
    local_dir = tempfile.mkdtemp()

    options = []
    if repo_clone_auth:
        options.append(f"--config http.extraHeader='Authorization: {repo_clone_auth}'")

    git.clone_with_config(
        repo_http_clone_url=repo_http_clone_url,
        multi_options=options,
        local_dir=local_dir,
    )
    return local_dir


def summarize_pull_requests(
    repo_dir: str,
    llm_endpoint_url: str,
    input_glob: str,
    oldest_pr_number: int,
    oldest_datetime: datetime,
    batch_size: int = DEFAULT_BATCH_SIZE,
) -> List[PullRequestSummaryResult]:
    pull_request_loader = PullRequestLoader()
    pull_requests = pull_request_loader.load(
        input_glob=input_glob,
        oldest_pr_number=oldest_pr_number,
        oldest_datetime=oldest_datetime,
    )
    pull_request_processor_engine = LLMPullRequestsProcessorEngine(
        llm_endpoint_url=llm_endpoint_url,
        llm_combine_prompt=PullRequestSummaryCombinePrompt(),
        llm_map_prompt=PullRequestSummaryMapPrompt(),
        content_handler=ChatMLContentHandler(system_prompt=PullRequestSummarySystemPrompt().get_prompt_template()),
        max_new_tokens=LLM_MAX_NEW_TOKENS,
        max_tokens=LLAMA_MAX_INPUT_TOKENS,
    )
    # pull_request_processor_engine = OpenAIPullRequestsProcessorEngine()
    # pull_request_processor_engine = OctoAIPullRequestsProcessorEngine()
    pull_request_summary_results = pull_request_processor_engine.process_pull_requests(
        repo_dir=repo_dir,
        pull_requests=pull_requests,
        batch_size=batch_size,
    )

    sorted_pull_request_summary_results = sorted(pull_request_summary_results, key=lambda x: x.pull_request.number)
    print(sorted_pull_request_summary_results)
    return sorted_pull_request_summary_results


def write_pull_request_summary_results(pull_request_summary_results: List[PullRequestSummaryResult]):
    pull_request_summary_results_writer = PullRequestSummaryResultsWriter()
    pull_request_summary_results_writer.write(pull_request_summary_results=pull_request_summary_results)


def main() -> None:
    logging.info("Loading task config ...")
    config = load_config()

    logging.info(f"Cloning repository {config.repo_id} ...")
    repo_dir = clone_repo(repo_http_clone_url=config.repo_http_clone_url, repo_clone_auth=config.repo_clone_auth)

    logging.info("Summarizing pull requests ...")
    pull_request_summary_results = summarize_pull_requests(
        repo_dir=repo_dir,
        llm_endpoint_url=config.llm_endpoint_url,
        input_glob=PROCESS_INPUT_GLOB,
        oldest_pr_number=config.oldest_pr_number,
        oldest_datetime=config.oldest_datetime,
    )

    logging.info("Writing pull requests ...")
    write_pull_request_summary_results(pull_request_summary_results=pull_request_summary_results)


if __name__ == "__main__":
    main()
