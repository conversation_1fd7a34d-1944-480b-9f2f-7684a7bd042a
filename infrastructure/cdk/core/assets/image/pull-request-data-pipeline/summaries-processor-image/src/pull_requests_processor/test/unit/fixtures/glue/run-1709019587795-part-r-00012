{"state":"Merged","mergedAt":"2022-08-04T18:03:23Z","number":2512,"body":"![CleanShot 2022-08-04 at 10 28 19](https://user-images.githubusercontent.com/********/182913722-001c178f-de77-442c-8ab7-7f87bf6fb45d.gif)\r\n","mergeCommitSha":"b476f185efc25506c63574ec8029eb704e16ed60","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2512","title":"UNB-490 Fix top level dashboard search ","createdAt":"2022-08-04T17:29:21Z"}
{"state":"Merged","mergedAt":"2022-08-04T19:59:37Z","number":2513,"body":"Fix some bugs:\r\n\r\n* Remove the \"no\" auto-fetch UI option, since there's a cancel button always added.  I think I merged the PR before pushing my final change in this.\r\n* When resolution git operations (fetch/pull/checkout) fail, show an error UI.  Show special UIs for \"workspace is dirty\" and \"unauthed\" cases, otherwise show git output.  This follows what VSCode does internally.\r\n* Disable the git action buttons while the git action is running","mergeCommitSha":"880b17b052b4fc9f0294d4d279551cdbf1d456b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2513","title":"Fix bugs in SM read-only resolution UI","createdAt":"2022-08-04T18:09:34Z"}
{"state":"Merged","mergedAt":"2022-08-04T19:44:52Z","number":2514,"body":"- Add CDK code to create S3 buckets \r\n- Added config to create a bucket for external alb access logs\r\n- Added annotation to enable ALB access logs in Dev. If it works fine I'll make another PR for Prod","mergeCommitSha":"c3cb4a25f981d0c8936d26bb1d9cf6eccc09297d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2514","title":"enable alb access logs in dev","createdAt":"2022-08-04T19:11:45Z"}
{"state":"Merged","mergedAt":"2022-08-05T00:00:55Z","number":2515,"body":"- backfills file hashes for points, and marks them as trusted.\r\n\r\n- upstreams original points from client. this needs another change on the apiservice to allow this and upsert instead of insert. I'll do that in separate change here: https://github.com/NextChapterSoftware/unblocked/pull/2516\r\n\r\n- reduces upstream batch size because putSourcepoints currently takes up to 5.2 seconds in Honeycomb\r\n  https://ui.honeycomb.io/unblocked/environments/production/result/6N8Kzm92gY1?hideMarkers&useStackedGraphs\r\n","mergeCommitSha":"955e13be1933cbabb02411c9e38204840d69a30c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2515","title":"Sanitize source points in source mark engine","createdAt":"2022-08-04T21:20:35Z"}
{"state":"Merged","mergedAt":"2022-08-04T23:42:53Z","number":2516,"body":"- Allow original points to be uploaded\r\n\r\n- TODO: Upsert points to avoid duplicates in a follow up change","mergeCommitSha":"8d15bff503996b5a83a67e9f451dfa378bf254c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2516","title":"Allow original points to be uploaded","createdAt":"2022-08-04T21:23:28Z"}
{"state":"Merged","mergedAt":"2022-08-04T22:15:14Z","number":2517,"body":"- Fixed alb access log s3 permissions (https://docs.aws.amazon.com/elasticloadbalancing/latest/application/load-balancer-access-logs.html#access-logging-bucket-permissions)\r\n- Added annotation for prod alb logging","mergeCommitSha":"4e4aeb7acb1e01c57c46ccfefd28d7fc25c33425","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2517","title":"rollout access logs to prod","createdAt":"2022-08-04T21:41:09Z"}
{"state":"Closed","mergedAt":null,"number":2518,"body":"Tests pass but I am not sure what would happen to existing comments with old signature moving forward.\r\n\r\nDo we need to handle those just like we do legacy ones now ?","mergeCommitSha":"311443ddeae4608df53ec4451ef66f7c97432fff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2518","title":"Change unblocked comment branding","createdAt":"2022-08-04T22:34:59Z"}
{"state":"Merged","mergedAt":"2022-08-04T22:35:54Z","number":2519,"body":"This directory should never have been submitted..","mergeCommitSha":"7f3cd7dcce29553c9da2945818882bcfe4aa6446","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2519","title":"whoops","createdAt":"2022-08-04T22:35:33Z"}
{"state":"Merged","mergedAt":"2022-02-04T07:47:32Z","number":252,"mergeCommitSha":"4e8c9f077537459f227baa64f2415b53fc140ff7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/252","title":"Use wrapRow in Person API to simplify","createdAt":"2022-02-04T06:44:25Z"}
{"state":"Merged","mergedAt":"2022-08-05T00:03:15Z","number":2520,"mergeCommitSha":"03aaf27d7e3426f7a8d411a3fbb8c827dc305b43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2520","title":"Use compound cursor paging for getSourceMarks API","createdAt":"2022-08-04T22:48:24Z"}
{"state":"Merged","mergedAt":"2022-08-16T21:29:52Z","number":2521,"mergeCommitSha":"80b8726d3248189e06246feb34e1969422e06d71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2521","title":"POST /modifiedSince with /threads/{threadId} returns a result if thread unread is modified","createdAt":"2022-08-04T22:52:04Z"}
{"state":"Merged","mergedAt":"2022-08-08T22:46:49Z","number":2522,"body":"First draft to support.\r\n<img width=\"399\" alt=\"CleanShot 2022-08-04 at 16 35 33@2x\" src=\"https://user-images.githubusercontent.com/1553313/182972673-b589eab0-ee17-4ca6-9c3f-8d299c9c9647.png\">\r\n ","mergeCommitSha":"4fdab61407a1039530d0225237f9fd7b3583ea49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2522","title":"Spec changes to support Related Pull Requests","createdAt":"2022-08-04T23:36:02Z"}
{"state":"Merged","mergedAt":"2022-08-05T00:41:08Z","number":2523,"mergeCommitSha":"ae1037041ecf038fcf277dd8ceccc2f7e90053cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2523","title":"Fix SourceMarksApiDelegateImplTest and add isTrusted flag to admin","createdAt":"2022-08-05T00:24:58Z"}
{"state":"Merged","mergedAt":"2022-08-05T02:20:07Z","number":2524,"body":"(let's see if Matt sees this :)\r\n\r\nHis willingness to provide wonderful input led to this pr.","mergeCommitSha":"ceddbf2b03fd479832bc08b4dc2087ab3312a8f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2524","title":"Matt Adam is a lovely dude","createdAt":"2022-08-05T02:03:44Z"}
{"state":"Merged","mergedAt":"2022-08-05T03:45:37Z","number":2525,"body":"- we were comparing the newly minted trusted point against the cached point to\n  gate upload; problem was the cache was shared so we were just comparing the\n  point to itself, which means we would never upload.","mergeCommitSha":"eca23efb7dbc5059757902efd75c1ede735430c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2525","title":"Defensive copy point to ensure trusted points are uploaded","createdAt":"2022-08-05T03:33:15Z"}
{"state":"Merged","mergedAt":"2022-08-05T07:52:23Z","number":2526,"mergeCommitSha":"a9623a2ae0864a1b0d3da1ec064f1eedb333a733","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2526","title":"Fix sourcemark pusher channel","createdAt":"2022-08-05T05:43:08Z"}
{"state":"Merged","mergedAt":"2022-08-05T07:39:05Z","number":2527,"body":"https://linear.app/unblocked/issue/UNB-220/api-to-expose-teamteamidrecommendedreviews","mergeCommitSha":"b8a6f5f24492f239e9f47158fad4dae6b7dd5855","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2527","title":"API to expose author recommendations based on Social comment network","createdAt":"2022-08-05T07:03:30Z"}
{"state":"Merged","mergedAt":"2022-08-05T16:40:56Z","number":2528,"mergeCommitSha":"435c3c9205c869b40b49184e63c851e33ed359af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2528","title":"Web has no notion of contriubtors","createdAt":"2022-08-05T16:12:50Z"}
{"state":"Merged","mergedAt":"2022-08-05T17:33:01Z","number":2529,"mergeCommitSha":"c151ca5265ed411586ce8ed61933592eeedc04e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2529","title":"Allow rerunning just bulk pr ingestion","createdAt":"2022-08-05T17:13:42Z"}
{"state":"Merged","mergedAt":"2022-02-04T16:54:39Z","number":253,"mergeCommitSha":"2ff01f8c2354b8878fe0767faa868fde3df49744","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/253","title":"ThreadPArticipants API","createdAt":"2022-02-04T08:38:23Z"}
{"state":"Merged","mergedAt":"2022-08-08T20:25:11Z","number":2530,"mergeCommitSha":"1740b8c7cea4afda7f63117cfa199d13ba1c1ace","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2530","title":"Add filter for current team members for mentions","createdAt":"2022-08-05T17:38:58Z"}
{"state":"Merged","mergedAt":"2022-08-05T18:44:18Z","number":2531,"body":"We also are standardizing triggers.\r\n","mergeCommitSha":"8434693595cb5da37b701bffdceb03c9e4177b87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2531","title":"Make sure we send a thread invite if user is mentioned that does not have account","createdAt":"2022-08-05T18:21:54Z"}
{"state":"Merged","mergedAt":"2022-08-05T20:14:53Z","number":2532,"mergeCommitSha":"e4fda874524d366490e8e2218d928890e63f25a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2532","title":"Minor logging changes","createdAt":"2022-08-05T20:05:41Z"}
{"state":"Merged","mergedAt":"2022-08-11T21:10:26Z","number":2533,"body":"* Needs to support PR description and message body contents\r\n<img width=\"523\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/*********-1e05b3e2-9e40-4624-bd2d-5376e2f38dba.png\">\r\n\r\n* Needs to support embedded threads:\r\n<img width=\"526\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/*********-1aae6ceb-1746-4af7-95dc-01d9c3fa0490.png\">\r\n\r\n* Needs to support code-level comments (i.e. regular unblocked discussions):\r\n<img width=\"527\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/*********-d789d3ce-b013-43cc-8b5e-2879e77ea6c5.png\">\r\n\r\n\r\nWill fill in the CRUD operations once the models are more stable.","mergeCommitSha":"e434b3ecfefb0e37e6339472bfcdc28adb036258","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2533","title":"[RFC] PullRequestMessage model spec ","createdAt":"2022-08-05T20:42:42Z"}
{"state":"Closed","mergedAt":null,"number":2534,"mergeCommitSha":"11f8710406801b99467aec3c9891b5fb742f9dc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2534","title":"Gate auth expiry before requests","createdAt":"2022-08-05T21:32:02Z"}
{"state":"Merged","mergedAt":"2022-08-05T22:15:20Z","number":2535,"body":"Allows for temporarily pausing bulk ingestion","mergeCommitSha":"44dcaa0f8c01c8a0e9d2c64cf14b9fc38a370ea0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2535","title":"Toggle bulk ingestion flag","createdAt":"2022-08-05T22:03:21Z"}
{"state":"Merged","mergedAt":"2022-08-05T22:45:30Z","number":2536,"mergeCommitSha":"a39925cf8de0f73bea7af7d402e827c2ec6e0424","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2536","title":"Show bulk ingestion API responses in admin cons","createdAt":"2022-08-05T22:30:28Z"}
{"state":"Merged","mergedAt":"2022-08-05T23:28:52Z","number":2537,"body":"Probably the reason for 502s","mergeCommitSha":"2d773aeec57a219aa16c391cb2d4dbcadfa515c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2537","title":"Dont sort comments for repo","createdAt":"2022-08-05T23:27:29Z"}
{"state":"Merged","mergedAt":"2022-08-06T01:08:59Z","number":2538,"mergeCommitSha":"1a93c89df14cad21494e492144fffab28005b9a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2538","title":"Add thread count to admin","createdAt":"2022-08-06T00:30:40Z"}
{"state":"Merged","mergedAt":"2022-08-09T16:59:35Z","number":2539,"mergeCommitSha":"2c4a17b01c0e34443b5fc3ef346746729f9c0d5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2539","title":"UNB-524 Fix search navigation","createdAt":"2022-08-06T00:54:53Z"}
{"state":"Closed","mergedAt":null,"number":254,"body":"Playing around with this to see if this is something we want to do to address https://github.com/NextChapterSoftware/unblocked/issues/220. Maybe there's a way we can define the data class without all the boilerplate?","mergeCommitSha":"c0c4bd33d8d4e3353910aff1555908f9396e8335","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/254","title":"[DO NOT MERGE] Make MessageDAO internal and expose Message as a data class","createdAt":"2022-02-04T18:58:43Z"}
{"state":"Merged","mergedAt":"2022-08-06T01:53:44Z","number":2540,"mergeCommitSha":"d1e956423b88dc697a52c7ad60bf95d4b605fe6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2540","title":"Permanently delete team","createdAt":"2022-08-06T01:04:50Z"}
{"state":"Merged","mergedAt":"2022-08-06T04:11:10Z","number":2541,"body":"fixes: https://linear.app/unblocked/issue/UNB-393/admin-web-needs-a-confirmation-prompt-for-dangerous-changes\r\n\r\n<img width=\"669\" alt=\"Screen Shot 2022-08-05 at 20 30 53\" src=\"https://user-images.githubusercontent.com/1798345/183232083-701f5a1b-001b-4d65-a233-87c8604340e8.png\">\r\n\r\n","mergeCommitSha":"84ad2b4dfd1183298ecea5a7bebe5fd9c16bb616","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2541","title":"Admin confirmation modal","createdAt":"2022-08-06T03:35:57Z"}
{"state":"Merged","mergedAt":"2022-08-06T04:47:26Z","number":2542,"mergeCommitSha":"27015813489539b6b13225225d1dafcffbd06590","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2542","title":"Cleanup unused action","createdAt":"2022-08-06T04:46:04Z"}
{"state":"Merged","mergedAt":"2022-08-06T06:23:00Z","number":2543,"body":"- fix styling for release description\n- fix styling for toggle list","mergeCommitSha":"be844f7ef686d761bac9a53d4c5b3563bb1aa10f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2543","title":"Cleanup admin rendering","createdAt":"2022-08-06T05:59:11Z"}
{"state":"Merged","mergedAt":"2022-08-07T03:19:54Z","number":2544,"mergeCommitSha":"e686cb8dcaf786b3e82767c82d981faacd015491","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2544","title":"Add ability to trigger bulk ingestion for all repos in a team","createdAt":"2022-08-07T02:42:24Z"}
{"state":"Merged","mergedAt":"2022-08-09T05:58:03Z","number":2545,"mergeCommitSha":"943e22fc54c19b0e9da5833aa4caa392a4e20be7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2545","title":"Add description to pull request model","createdAt":"2022-08-07T04:25:20Z"}
{"state":"Merged","mergedAt":"2022-08-08T23:09:16Z","number":2546,"body":"Setup TLC PR Sidebar with local data.\r\nCurrently showing current commit (should hopefully be a PR commit?) and corresponding diff stats.\r\n\r\nHidden behind feature flag\r\n\r\n<img width=\"325\" alt=\"CleanShot 2022-08-08 at 10 23 15@2x\" src=\"https://user-images.githubusercontent.com/1553313/183480033-6424543a-575a-40aa-989e-65768cb9ae94.png\">\r\n","mergeCommitSha":"c0b1b1d3c5891b269f4b2b56165fb01a4a4b47f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2546","title":"Basic TLC PR Sidebar with local data","createdAt":"2022-08-08T17:42:05Z"}
{"state":"Closed","mergedAt":null,"number":2547,"body":"MessageContent + Metadata should be under one MessageData object.\r\nThis is reducing duplication and ensuring easier rollout of changes when we plan to add reactions etc. to messages.\r\n\r\nChose to follow the optional model for versioning (I'm not going to solve versioning in this pr :))\r\nWe will eventually deprecate/remove the old fields (messageContent) once clients have all been updated to a recent version.\r\n\r\nAnnoyingly, half of this is updating code generated swift files, which sucks.","mergeCommitSha":"47a6a5815fa70ba7c212a28fb8e3db89b1646710","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2547","title":"Move all related message data under one body","createdAt":"2022-08-08T18:43:04Z"}
{"state":"Merged","mergedAt":"2022-08-08T21:15:44Z","number":2548,"body":"For some reason was removed in this commit https://github.com/NextChapterSoftware/unblocked/commit/cef86a02a47af9595b114627c5b8141b4cfcd4c7#diff-4052666403b79a8657c45e1e49df92662b7b3879fdbd9d95df02dcc83eccb565L282","mergeCommitSha":"aa332af14ed8f68eabf0727280fc06add236d528","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2548","title":"Fix deleting messages on vscode","createdAt":"2022-08-08T20:48:55Z"}
{"state":"Merged","mergedAt":"2022-08-08T22:32:20Z","number":2549,"body":"Add apis to create/delete reactions.","mergeCommitSha":"ee8127b81197c788fb882fdbcdc9efb4dc3843b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2549","title":"Add github reactions apis","createdAt":"2022-08-08T22:02:57Z"}
{"state":"Merged","mergedAt":"2022-02-04T22:38:43Z","number":255,"body":"![image](https://user-images.githubusercontent.com/********/152586545-7310b52e-160e-433a-b21d-dab538bc6268.png)\r\n\r\nNOTES\r\n* General rule of thumb is that webview code cannot have any dependencies or subdependencies to the vscode package \r\n* Needed to separate our the api models from the `api/index.ts` because of the above issue (the generated models go into their own exported module since they need to reference in the webview code)\r\n    * Should we follow the same pattern in `web/`? (the subdependency thing won't be an issue for web but just for consistency?)\r\n* Not sure about the `messageEditor/` being its own top level directory but I figured if it's temporary then we can just remove it all in one go when it's ready to be removed. I can also see a case where maybe we have a `playground/` top level directory with sandbox code in it(?)","mergeCommitSha":"fecf137516b5491f83bbe9d568b12ed0d79c4679","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/255","title":"Reconfigure vscode directory into semantic structure","createdAt":"2022-02-04T19:01:58Z"}
{"state":"Merged","mergedAt":"2022-08-09T21:51:45Z","number":2550,"body":"This is where we'll store top-level comments and reviews","mergeCommitSha":"d17eb91cacaa77412d52cabeac883b2a5efd0b1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2550","title":"Add PullRequestCommentModel and PullRequestReviewModel","createdAt":"2022-08-08T22:05:05Z"}
{"state":"Merged","mergedAt":"2022-08-08T22:45:58Z","number":2551,"mergeCommitSha":"ab7220018a9dd81c42b9b8dcd821f397cb190010","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2551","title":"Rename variable","createdAt":"2022-08-08T22:44:51Z"}
{"state":"Merged","mergedAt":"2022-08-09T16:39:52Z","number":2552,"body":"reduce pod rollover percentage. We are hitting CPU limits on our kube cluster due to concurrent deploys","mergeCommitSha":"9234d48df48959361d23d922fadf0ddce0336981","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2552","title":"Reduce pod rollover percentage to 20%","createdAt":"2022-08-08T23:28:00Z"}
{"state":"Merged","mergedAt":"2022-08-09T17:28:17Z","number":2553,"body":"https://www.notion.so/nextchaptersoftware/Source-mark-snippet-file-content-hash-problem-0218f3fe87524323bd11bc5572088e97","mergeCommitSha":"15aecd79701eba35d6060d21f89bfed5f47f1c41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2553","title":"Do not trust points where the file content hash has changed","createdAt":"2022-08-09T00:45:55Z"}
{"state":"Merged","mergedAt":"2022-08-09T01:26:11Z","number":2554,"body":"Includes one of my changes that I needed.","mergeCommitSha":"e5fc83b746a1f9712b254436afc4ea45eaadeada","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2554","title":"upgrade exposed","createdAt":"2022-08-09T01:04:35Z"}
{"state":"Merged","mergedAt":"2022-08-10T18:55:48Z","number":2555,"mergeCommitSha":"4f9eee330e6a71392854932f7a29840f9b5002ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2555","title":"Implement getPullRequestsForCommits","createdAt":"2022-08-09T06:50:58Z"}
{"state":"Merged","mergedAt":"2022-08-09T18:54:36Z","number":2556,"body":"Include MergedAt & mergeCommitSha for TLC ","mergeCommitSha":"b6e751c5a6e19c39012fde955e6fb0fe532b5d08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2556","title":"Update PR Spec","createdAt":"2022-08-09T17:05:12Z"}
{"state":"Merged","mergedAt":"2022-08-09T18:55:02Z","number":2557,"body":"Fixes UNB-526\r\n\r\nUse input colours instead of dropdown colours.  Looks good on lots of themes.\r\n\r\n<img width=\"1449\" alt=\"Screen Shot 2022-08-09 at 10 13 35 AM\" src=\"https://user-images.githubusercontent.com/2133518/183715468-6bd4058a-38c6-4d3b-8e84-7e7754c2b759.png\">\r\n<img width=\"1449\" alt=\"Screen Shot 2022-08-09 at 10 14 17 AM\" src=\"https://user-images.githubusercontent.com/2133518/183715487-5df69e50-5384-43b6-a542-bc39874cb0e7.png\">\r\n<img width=\"1449\" alt=\"Screen Shot 2022-08-09 at 10 14 45 AM\" src=\"https://user-images.githubusercontent.com/2133518/183715493-d2359ef1-1e0f-4f52-a883-0688e5d8b9e2.png\">\r\n<img width=\"1449\" alt=\"Screen Shot 2022-08-09 at 10 14 56 AM\" src=\"https://user-images.githubusercontent.com/2133518/183715496-8800a970-03ed-47d2-9fc9-d88010259bfd.png\">\r\n\r\n","mergeCommitSha":"06a140ada32df68a6b4ffce14b9ecf0e290b71ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2557","title":"Fix colours in git clipboard view","createdAt":"2022-08-09T17:16:38Z"}
{"state":"Merged","mergedAt":"2022-08-09T18:49:13Z","number":2558,"mergeCommitSha":"667ce7724afa2518e5d82620ee9a498ff63b27ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2558","title":"Clear calculated source points from team","createdAt":"2022-08-09T18:04:23Z"}
{"state":"Merged","mergedAt":"2022-08-09T18:48:33Z","number":2559,"body":"<img width=\"997\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/183731494-583fe7ec-9b2f-4c12-b1cb-6234a972e4c7.png\">\r\n","mergeCommitSha":"f3af9f5610ae8cb90153c9215a92c3ad8c4f7d23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2559","title":"Encode and decode to escape ","createdAt":"2022-08-09T18:16:11Z"}
{"state":"Merged","mergedAt":"2022-02-08T01:01:55Z","number":256,"body":"A few tweaks to MessageEditor to make it behave more like other editors:\r\n\r\n* Block-level toolbar buttons (quote and code, right now) now hilight properly when the cursor is within a quote/code block, and in this state clicking on the button reverts the block to a plain paragraph (ie, acts as a toggle)\r\n* Deleting the beginning of a custom block reverts the block to a plain paragraph\r\n* Hitting enter a second time at the end of a custom block closes the block and creates a new plain paragraph (how you can 'exit' out of a block).\r\n* Ensure there is an empty editable paragraph at the end of documents, so you can always add stuff at the end.","mergeCommitSha":"b452de2e1d53998062f5162d6709447e0a0b3793","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/256","title":"Matt/message editor tweaks","createdAt":"2022-02-04T19:23:31Z"}
{"state":"Merged","mergedAt":"2022-08-09T19:58:38Z","number":2560,"mergeCommitSha":"192fc4782b38c6137b688874fc0c6a294f5f155b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2560","title":"DiffStatParser: Reduce spammy errors","createdAt":"2022-08-09T19:06:35Z"}
{"state":"Merged","mergedAt":"2022-08-11T05:28:59Z","number":2561,"body":"* Remove the branch name from the action button\r\n* Truncate the branch name in the commandline\r\n\r\n<img width=\"1417\" alt=\"Screen Shot 2022-08-09 at 12 10 25 PM\" src=\"https://user-images.githubusercontent.com/2133518/183742174-11361c16-bfe0-4228-b773-a8406082c85a.png\">\r\n","mergeCommitSha":"6830e4d0cf3b10bcdda20244b25853d8490c2199","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2561","title":"Fix truncation on git action UI","createdAt":"2022-08-09T19:13:02Z"}
{"state":"Merged","mergedAt":"2022-08-09T23:33:24Z","number":2562,"mergeCommitSha":"efe3e074a0b66adf97e6c9fdd88057aa80d50de4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2562","title":"Add message reaction models","createdAt":"2022-08-09T19:38:03Z"}
{"state":"Merged","mergedAt":"2022-08-10T16:51:25Z","number":2563,"body":"* Rename and restructure some props to anticipate the TLC work (i.e. generalize the MessageView to handle TLC UI)\r\n* UI changes should be net zero","mergeCommitSha":"efbbe3b6619e5ad9298ee4a6dbbcb5e876553270","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2563","title":"Refactor MessageView properties","createdAt":"2022-08-09T20:30:17Z"}
{"state":"Merged","mergedAt":"2022-08-09T21:29:57Z","number":2564,"body":"Fixes UNB-507\r\n\r\nReproduction steps:\r\n\r\n* Open a file with source marks\r\n* Cut code such that you cut a sourcemark -> verify that the sourcemark does *not* end up repositioning elsewhere\r\n* Paste the code in the file somewhere else\r\n\r\nPreviously, the pasted code would not end up having a sourcemark.  With this fix, it does.  Effectively we cache the loaded thread data so that even when the sourcemark disappears, we can reuse the thread data after.","mergeCommitSha":"398ef6c6de47cf624e6c484b0de82ba7f13d5c4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2564","title":"Fix SourceMark thread caching","createdAt":"2022-08-09T20:58:15Z"}
{"state":"Merged","mergedAt":"2022-08-12T20:53:01Z","number":2565,"body":"<img width=\"1066\" alt=\"CleanShot 2022-08-09 at 14 05 43@2x\" src=\"https://user-images.githubusercontent.com/1553313/183761344-c98f82ed-c1aa-4f0e-aa57-c236dbd43f89.png\">\r\n\r\n<img width=\"493\" alt=\"CleanShot 2022-08-10 at 15 58 02@2x\" src=\"https://user-images.githubusercontent.com/1553313/184037427-902eb10f-4798-49df-8ac6-5c92d6e36487.png\">\r\n\r\n","mergeCommitSha":"9702967405bb452eed8603b349350a9918d89f8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2565","title":"Basic PR sidebar styling","createdAt":"2022-08-09T21:04:50Z"}
{"state":"Merged","mergedAt":"2022-08-10T01:38:05Z","number":2566,"mergeCommitSha":"41ba51fbc2d9dbcb5ffcadfe173cdefdcad571b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2566","title":"Add issue comment webhook handler stub","createdAt":"2022-08-09T23:00:07Z"}
{"state":"Merged","mergedAt":"2022-08-09T23:09:43Z","number":2567,"mergeCommitSha":"01a9d80531def60a6d7ea249177f7de8dac46be4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2567","title":"update github queues","createdAt":"2022-08-09T23:05:52Z"}
{"state":"Merged","mergedAt":"2022-08-10T16:30:27Z","number":2568,"mergeCommitSha":"b19d3e41d553c8bab3c36798a51c2966539c1b55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2568","title":"Try github reaction handler","createdAt":"2022-08-09T23:35:27Z"}
{"state":"Merged","mergedAt":"2022-08-10T21:03:13Z","number":2569,"body":"Fixes UNB-511\r\n\r\nThis got pretty involved.  The goal for the sidebar views is to track the file that currently has focus, meaning:\r\n* If the focused tab is a text editor, display sourcemarks for the file open in the editor\r\n* If the focused tab is a webview, and that webview is associated with a file in some way (say, a discussion view on a thread, anchored via a sourcemark to a file), then display sourcemarks for that file.\r\n\r\nThere are multiple problems here:\r\n* VSCode does not have a single unified API that allows you to track the focused tab, across both TextEditors and webviews.  The official guidance is to use window.activeTextEditor to track the focused tab when it is a text editor, and WebviewPanel.active to track the active webview\r\n* Webviews have no notion of an associated file.\r\n\r\nThis PR does the following:\r\n* Adds a property `WebviewContentController.associatedFilePath` -- this allows any webview to declare that it has an associated file.  Internally the WebviewContentController tracks its own focus state, plus this new filePath property, to understand if the focused webview is associated with a file.\r\n* Adds a new class `ActiveFileManager`, which muxes both the `window.activeTextEditor` state, plus all `WebviewContentController.associatedFilePath` states, to resolve one single active file path.\r\n* Update `TextEditorSourceMarkManager` to use this.\r\n\r\nPhew.","mergeCommitSha":"a1a63a5566a3756f8620d5ac5b91bf1d6bbf568e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2569","title":"Add ActiveFileManager","createdAt":"2022-08-09T23:42:17Z"}
{"state":"Merged","mergedAt":"2022-02-05T01:31:07Z","number":257,"body":"## Changes\r\n\r\n1. moved the submodule in order to restructure all GitHub-related graphql information under one directory:\r\n\r\n```\r\napiservice/src/main/resources/\r\n└── github-graphql\r\n    ├── .graphqlconfig\r\n    ├── queries\r\n    └── schema\r\n```\r\n\r\n2. Introduce V3 (REST) and V4 (GraphQL) sub-clients in `GitHubAppClient`\r\n\r\n## Action needed\r\nDevelopers need to force update once this change is merged due to the submodule move:\r\n```sh\r\nrm -rf .git/modules/\r\nrm -rf apiservice/src/main/resources/github-graphql-schema/\r\ngit submodule update --init --force\r\n```\r\n","mergeCommitSha":"f8e545fd2df41c8df287e6415bcbf85f4c303286","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/257","title":"GraphQL client with auth","createdAt":"2022-02-04T23:34:27Z"}
{"state":"Merged","mergedAt":"2022-08-10T21:42:44Z","number":2570,"mergeCommitSha":"cdb41daebb4b48fe07f0d8aaed73c519b6c325ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2570","title":"Add reactions api","createdAt":"2022-08-10T17:36:47Z"}
{"state":"Merged","mergedAt":"2022-08-10T18:22:36Z","number":2571,"body":"A regression was introduced where we were getting the diff between commits, but _only for the from-point file_. This worked fine if the point does not move between files.\r\n\r\nThis change gets the diff between commits, but this time including all the files in the diff. This in turn allows the RangeAdjust class to track the point across multiple files.\r\n\r\n> **Note**\r\n> Will need to clear out all old generated sourcepoints from all teams via admin web after this lands, forcing the VSCode extensions to recalculate all points again.","mergeCommitSha":"ede50bbb7bbb57886a2b267c889c7b746d04979f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2571","title":"Fix sourcemark move between files detection","createdAt":"2022-08-10T18:02:05Z"}
{"state":"Merged","mergedAt":"2022-08-10T20:08:20Z","number":2572,"body":"Impact of this change:\n1. Warnings will be logged: `\"Installation permissions are invalid\"`,\n   which is useful for knowing which Org have not yet accepted the new permissions.\n2. Orgs that do not accept the permissions will no longer be \"maintained\" by the\n   `GitHubInstallationMaintenanceJob` background job.","mergeCommitSha":"90d07e48cb2a0c3f1626eed9fb145d3390e66f26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2572","title":"GitHub app validation includes issues:read","createdAt":"2022-08-10T19:27:02Z"}
{"state":"Merged","mergedAt":"2022-08-10T21:09:14Z","number":2573,"body":"Current route is `getunblocked.com/getunblocked`\r\n\r\n<img width=\"1508\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/184005470-a3cd382d-a22d-4bff-ad9f-a5fb41eec096.png\">\r\n","mergeCommitSha":"acb649219c962ab3248b49dc47c019273153832d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2573","title":"Add universal download page","createdAt":"2022-08-10T19:43:18Z"}
{"state":"Merged","mergedAt":"2022-08-10T20:13:04Z","number":2574,"mergeCommitSha":"15e019d1c6a73b7287173126530a206bdc4182f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2574","title":"Update service permissions","createdAt":"2022-08-10T19:48:43Z"}
{"state":"Merged","mergedAt":"2022-08-10T20:47:46Z","number":2575,"body":"Turbo is a collection of libraries that handle server side rendering and other \"magic\".\r\nIt optimizes load times by doing a bunch of caching and partial Dom changes. \r\n\r\nBasically SPA without being a SPA.\r\n\r\nThis PR removes a hack where we disabled turbo for a few pages (caused slow load times in PR review) and potentially fixes some other navigation issues.\r\n\r\n\r\nhttps://turbo.hotwired.dev/","mergeCommitSha":"69f06b652e92b713e2873c4579e2028cb07fb424","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2575","title":"Remove turbo","createdAt":"2022-08-10T20:14:14Z"}
{"state":"Merged","mergedAt":"2022-08-10T20:49:25Z","number":2576,"body":"https://github.com/NextChapterSoftware/unblocked/runs/**********?check_suite_focus=true","mergeCommitSha":"d945728ec7f5db7dec20db5237cf8898fa1cfdfb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2576","title":"Fix flaky test","createdAt":"2022-08-10T20:33:16Z"}
{"state":"Merged","mergedAt":"2022-08-10T20:48:15Z","number":2577,"body":"https://app.logz.io/#/goto/dd9c1ce85840a21185b63c9f30957762?switchToAccountId=411850\n\nThe `DiscussionHelpers` class is trying to fetch content for a commit that does not exist in the repo, which should never happen.\n\nThe resolution is expected to be `notInRepo`. We'll see...","mergeCommitSha":"36dc42641666d3356503c164c7d203b9b373be03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2577","title":"Expose exception to understand read-only faults","createdAt":"2022-08-10T20:38:22Z"}
{"state":"Merged","mergedAt":"2022-08-10T21:37:14Z","number":2578,"body":"Slower but needed for re-ingestion of pull requests to back fill `mergedAt` and `mergeCommitSha`","mergeCommitSha":"3338b6e7b82990501f90d06d83c35e9d60838193","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2578","title":"Use upsert instead of insertIgnore","createdAt":"2022-08-10T20:49:26Z"}
{"state":"Merged","mergedAt":"2022-08-10T22:13:21Z","number":2579,"body":"In the case where there are multiple original points (occurring when Unblocked\r\ningests pre-merge and post-merge points), then we were incorrectly choosing\r\nthe _first_ point which may not even exist in the repo. As a result, there was\r\nan asymmetry between the resolution returned from the `CommitLocation` and the\r\noriginal point returned alongside it.\r\n\r\n\r\n❌ broken on the left \r\n✅ fix on the right\r\n<img width=\"2559\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/184020826-615d23d5-8d51-4740-a527-08fffa1140e3.png\">\r\n","mergeCommitSha":"639a887b57f840cd563ebe4adad4db4f5ced0f4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2579","title":"Fix for read-only faults","createdAt":"2022-08-10T21:09:22Z"}
{"state":"Closed","mergedAt":null,"number":258,"mergeCommitSha":"d258ea402b30bdae1eef6625216b5b111471694b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/258","title":"Ensure createdAt has microsecond precision","createdAt":"2022-02-05T01:05:54Z"}
{"state":"Merged","mergedAt":"2022-08-10T21:18:13Z","number":2580,"body":"Reverts NextChapterSoftware/unblocked#2572","mergeCommitSha":"b4c60905c189a3d7acbb230004254f6e557b6745","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2580","title":"Revert \"GitHub app validation includes issues:read\"","createdAt":"2022-08-10T21:18:06Z"}
{"state":"Merged","mergedAt":"2022-08-10T22:38:39Z","number":2581,"mergeCommitSha":"ccc3bf9ec231801035313d88d017a5eeeaa204ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2581","title":"Upsert pull request comments from webhooks","createdAt":"2022-08-10T21:58:33Z"}
{"state":"Merged","mergedAt":"2022-08-10T23:35:44Z","number":2582,"mergeCommitSha":"d6a359c9113d6f9b9f4b3d7826777313dd0c8b2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2582","title":"Add serial name","createdAt":"2022-08-10T23:12:23Z"}
{"state":"Merged","mergedAt":"2022-08-11T05:28:17Z","number":2583,"body":"Intercom was not enabled in Installation Sidebar.\r\n\r\n","mergeCommitSha":"f0ae09a5155f793e6db62e1368e7567a4994d5a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2583","title":"Enable Intercom","createdAt":"2022-08-10T23:25:03Z"}
{"state":"Merged","mergedAt":"2022-08-11T03:22:48Z","number":2584,"mergeCommitSha":"98a5f04b1a1ffc5f321731f5ce4a3204fcbabff5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2584","title":"Get pull request number from the issue comment","createdAt":"2022-08-10T23:38:03Z"}
{"state":"Merged","mergedAt":"2022-08-11T04:23:58Z","number":2585,"mergeCommitSha":"c9543e9483b412f37731a2dad1fb8bfce8f86623","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2585","title":"Bulk ingest top level comments","createdAt":"2022-08-11T00:10:32Z"}
{"state":"Merged","mergedAt":"2022-08-11T05:42:34Z","number":2586,"body":"Problem is that the App client fails with 401 on _every_ call that is made outside of an installed app. It cannot even be used for public endpoints.\r\n\r\nFor now, use a no-auth client, which sounds a bit crazy but is actually totally fine at our scale.\r\n\r\nLonger term fix is to use a GitHub OAuth App, called \"Unblocked Helper\", that will perform these authenticated requests in place of the no-auth client. (This is what we did in Skywagon.)\r\n\r\nhttps://linear.app/unblocked/issue/UNB-460/expo-onboarding-clicking-install-should-go-to-the-github-org-install","mergeCommitSha":"7585c49ce6ec76f8d24e6545949049e85987c3d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2586","title":"Clicking install should go to the GitHub org install page, not the org selection page","createdAt":"2022-08-11T00:57:17Z"}
{"state":"Merged","mergedAt":"2022-08-11T06:22:30Z","number":2587,"mergeCommitSha":"11650f5fb3f11ea23751ae4058693914e943ecee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2587","title":"Set MessageModel.prReviewId during ingestion","createdAt":"2022-08-11T06:01:10Z"}
{"state":"Merged","mergedAt":"2022-08-11T17:40:12Z","number":2588,"mergeCommitSha":"dccd43cc76f999f76166b48917d6c45c48928d87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2588","title":"Change min date for metrics charts","createdAt":"2022-08-11T06:52:04Z"}
{"state":"Merged","mergedAt":"2022-08-12T06:48:33Z","number":2589,"body":"https://linear.app/unblocked/issue/UNB-489/obsolete-threadprcomment-field","mergeCommitSha":"118b4bd8688985d0e2f39056902d536602a8db4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2589","title":"Obsolete Thread.prComment field","createdAt":"2022-08-11T14:17:52Z"}
{"state":"Merged","mergedAt":"2022-02-07T16:21:14Z","number":259,"mergeCommitSha":"d55d51074d193c439c4742b0e209c32b1bc0fae9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/259","title":"Fixes token leeway incorrect units","createdAt":"2022-02-05T01:20:52Z"}
{"state":"Merged","mergedAt":"2022-08-11T15:24:38Z","number":2590,"mergeCommitSha":"1441244d501615fe874f9f622881625795e66af0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2590","title":"Client linting","createdAt":"2022-08-11T14:31:55Z"}
{"state":"Closed","mergedAt":null,"number":2591,"body":"<img width=\"580\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/184194911-50ede0f2-a4ab-46bc-847e-a094cfdf0381.png\">\r\n","mergeCommitSha":"8e26d06ad3c3117039ec759f7f5645b1d2e7b283","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2591","title":"Strip newlines","createdAt":"2022-08-11T17:21:29Z"}
{"state":"Merged","mergedAt":"2022-08-11T18:06:31Z","number":2592,"body":"This TS code:\r\n```\r\nconst activeFile = activeWebviewFile ?? window.activeTextEditor?.document?.fileName;\r\n```\r\n\r\nresults in this JS code:\r\n\r\n```\r\nconst activeFile = activeWebviewFile ?? vscode__WEBPACK_IMPORTED_MODULE_1__.window.activeTextEditor.document.fileName;\r\n```\r\n\r\nie, the optional chaining is simply being removed by TypeScript.  The resulting code is not valid or safe, in that whenever document is falsy JS will stop executing.  The upshot of this is that on startup, if you have no editor focused, you will see this error:\r\n\r\n<img width=\"1417\" alt=\"Screen Shot 2022-08-11 at 10 34 51 AM\" src=\"https://user-images.githubusercontent.com/2133518/184200606-3eae9a5c-4b4f-4b3b-a387-555ed3d58fa4.png\">\r\n\r\nThis PR does the optional-chain checking manually.  I am going to see if there's a better solution.","mergeCommitSha":"f668d9d005257bb1be7139f9f1e9272ee03fbf87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2592","title":"Work around TS optional-chaining bug","createdAt":"2022-08-11T17:50:06Z"}
{"state":"Merged","mergedAt":"2022-08-11T19:04:09Z","number":2593,"mergeCommitSha":"95373bcf49dc471808257eaa75a800bc769ab041","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2593","title":"Flush last set of changes for reactions","createdAt":"2022-08-11T18:13:41Z"}
{"state":"Merged","mergedAt":"2022-08-11T19:02:44Z","number":2594,"mergeCommitSha":"cc8c03c11609f38ed251c2f3daba05f2c433222e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2594","title":"Ability to set hasSeenTutorial on person in admin web","createdAt":"2022-08-11T18:58:14Z"}
{"state":"Merged","mergedAt":"2022-08-11T20:10:11Z","number":2595,"body":"Fixes UNB-535\r\n\r\nThis happened under one circumstance: when two UIs referenced the same file (and thus the same TextEditorSourceMarks), the first was a webview, and the second a text editor.  This happens when you display a discussion view.","mergeCommitSha":"2c6ead64da0543704a20cbddf72bd211c2f8e9e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2595","title":"Fix SourceMark display when TextEditor is loaded after Webview","createdAt":"2022-08-11T19:21:49Z"}
{"state":"Merged","mergedAt":"2022-08-11T22:10:12Z","number":2596,"body":"Missing a couple tests but I'll add these later. I want to get this out so that Kay can build against real data in dev.","mergeCommitSha":"1a9e768d5a0c63e7174b659c374844be2c6e1928","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2596","title":"Implement getPullRequestInfo endpoint","createdAt":"2022-08-11T21:45:25Z"}
{"state":"Merged","mergedAt":"2022-08-11T21:51:14Z","number":2597,"body":"This would occasionally cause some lines in the editor to not have the space reserved for the gutter icons (ie, the indentation would look wrong).","mergeCommitSha":"713ee5549962afdcf177e36cf154fba278cedb3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2597","title":"Fix bug that caused some lines in the editor to not render gutter","createdAt":"2022-08-11T21:50:28Z"}
{"state":"Merged","mergedAt":"2022-08-11T23:22:55Z","number":2598,"body":"A harness we're going to be using for training data for some experiments we're planning on running.","mergeCommitSha":"c41c4cde6d627717d7dfefe0bcc0004c253acf46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2598","title":"Add slack extractor","createdAt":"2022-08-11T22:57:13Z"}
{"state":"Merged","mergedAt":"2022-08-11T23:41:28Z","number":2599,"mergeCommitSha":"734086996aa024a577bb99d12dc1e5d58673f66d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2599","title":"New person slack message shows their team memberships too","createdAt":"2022-08-11T23:25:32Z"}
{"state":"Merged","mergedAt":"2022-01-10T19:03:41Z","number":26,"body":"Update Component and other subdirectories to use PascalCase.\r\n\r\nAlso removed @auth and moved to store.\r\n\r\n**UPDATE**\r\n\r\nNeeded to set `git config core.ignorecase false` for git to handle the change to Pascalcase.","mergeCommitSha":"9d620388010b88efd8f539b18cd22f1fc1ff23f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/26","title":"Update File Names","createdAt":"2022-01-10T17:31:27Z"}
{"state":"Merged","mergedAt":"2022-02-06T06:30:12Z","number":260,"body":"Different systems have different timestamp precisions. Postgres supports microsecond precision, so let's make sure we use that everywhere.\r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1643999653314339","mergeCommitSha":"87c6c9cc170e7bb9a8262e762424eb7e3bf31c47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/260","title":"Create Instant.nowWithMicrosecondPrecision","createdAt":"2022-02-05T06:19:40Z"}
{"state":"Merged","mergedAt":"2022-08-12T03:08:27Z","number":2600,"body":"Follow up from https://github.com/NextChapterSoftware/unblocked/pull/2596","mergeCommitSha":"530d2da3531a7df96b8cb9ee89a612ae016b0498","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2600","title":"Add test","createdAt":"2022-08-11T23:31:42Z"}
{"state":"Merged","mergedAt":"2022-08-12T01:21:28Z","number":2601,"mergeCommitSha":"03d708660dac2692489e5060ddc2a405ea782759","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2601","title":"Add training data","createdAt":"2022-08-12T00:48:46Z"}
{"state":"Merged","mergedAt":"2022-08-12T06:13:20Z","number":2602,"body":"### problem\r\n- https://app.intercom.com/a/inbox/crhakcyc/inbox/admin/5614544/conversation/113\r\n\r\n### changes\r\n\r\n- team member page shows these links\r\n\r\n  <img width=\"240\" alt=\"Screen Shot 2022-08-11 at 17 55 10\" src=\"https://user-images.githubusercontent.com/1798345/184265838-4b77bdf2-ca4d-49fa-9537-eee43d725992.png\">\r\n\r\n- mine\r\n\r\n  <img width=\"1702\" alt=\"Screen Shot 2022-08-11 at 17 55 37\" src=\"https://user-images.githubusercontent.com/1798345/184265874-047a5bd5-cef0-4656-a0fa-d30393382b6c.png\">\r\n\r\n- recommended\r\n\r\n  <img width=\"1691\" alt=\"Screen Shot 2022-08-11 at 17 55 54\" src=\"https://user-images.githubusercontent.com/1798345/184265894-8f6c50ef-d2b5-40a5-85ba-5552560cddad.png\">\r\n\r\n","mergeCommitSha":"648b27f3d71c34bad95578a2593d814cd485bcb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2602","title":"Poor mans login-as-user: Show mine and recommended threads in admin web","createdAt":"2022-08-12T00:54:34Z"}
{"state":"Merged","mergedAt":"2022-08-12T04:22:29Z","number":2603,"body":"Fixes UNB-534","mergeCommitSha":"f14ab79d3e6efc971b9cfcdeed29068521802f5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2603","title":"Fix thread titles","createdAt":"2022-08-12T03:08:04Z"}
{"state":"Merged","mergedAt":"2022-08-12T17:07:22Z","number":2604,"mergeCommitSha":"564accc5b58f252e1b6df1b3366ec9047130414f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2604","title":"Fix triggerRepoBulkPrIngestion","createdAt":"2022-08-12T04:54:22Z"}
{"state":"Merged","mergedAt":"2022-08-12T17:32:21Z","number":2605,"body":"Fixes UNB-539\r\n\r\nTemp change to disable signatures for expo. \r\n\r\nWe'll remove this later today when I do it right: https://linear.app/unblocked/issue/UNB-541/create-team-settings-in-admin-console","mergeCommitSha":"f6f25c51fbaf5c7d077504962c5bff75879df4e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2605","title":"Disable unblocked signatures for comments posted from Unblocked for Expo","createdAt":"2022-08-12T16:55:13Z"}
{"state":"Merged","mergedAt":"2022-08-12T18:44:33Z","number":2606,"mergeCommitSha":"ac63ca8f31a57c18b97c635c247ad4503559b195","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2606","title":"Add handler stub for GitHub review events","createdAt":"2022-08-12T17:19:41Z"}
{"state":"Closed","mergedAt":null,"number":2607,"mergeCommitSha":"688a411f0ed7e9955eb0abd9c9a67bfcda0bbece","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2607","title":"Rename PullRequestGroup to PullRequestComment","createdAt":"2022-08-12T17:53:25Z"}
{"state":"Merged","mergedAt":"2022-08-16T18:49:33Z","number":2608,"mergeCommitSha":"8ff258b6a6fd74e167398fee39b3d443a0b13f1a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2608","title":"[TLC] Refactor models to leverage Message model","createdAt":"2022-08-12T19:21:09Z"}
{"state":"Merged","mergedAt":"2022-08-13T06:46:00Z","number":2609,"mergeCommitSha":"07b6ebfd78bca49eff1bd4bb4655e28d93590b81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2609","title":"Make Message API models reusable for TLCs and CLCs","createdAt":"2022-08-12T21:25:43Z"}
{"state":"Merged","mergedAt":"2022-02-06T02:52:54Z","number":261,"mergeCommitSha":"ba6c6e1ecd3a245259465748614e5f749b487e84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/261","title":"Update secret","createdAt":"2022-02-06T02:52:45Z"}
{"state":"Merged","mergedAt":"2022-08-12T22:35:52Z","number":2610,"body":"This allows us to do some complicated distance queries to validate the hypothesis of slack -> source code.","mergeCommitSha":"433802eab4bd6a383c24b9e99611bdf1429c903f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2610","title":"lucene index of source code","createdAt":"2022-08-12T21:29:22Z"}
{"state":"Merged","mergedAt":"2022-08-12T22:11:25Z","number":2611,"body":"Settings not used yet (follow up PR)","mergeCommitSha":"fd4afda36889d095b3147a40fd85d70a0875d37d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2611","title":"Add TeamSettingsModel and the ability to edit from Admin Console","createdAt":"2022-08-12T21:41:46Z"}
{"state":"Merged","mergedAt":"2022-08-13T00:47:42Z","number":2612,"body":"Fixes UNB-541","mergeCommitSha":"1cc9b99c031fb36f4cef03185639cae78ffae47f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2612","title":"Use team settings to decide whether to add a comment signature","createdAt":"2022-08-12T22:48:56Z"}
{"state":"Merged","mergedAt":"2022-08-13T04:51:47Z","number":2613,"mergeCommitSha":"be03a034a98aa0fcaa5c27128163ef8841097e65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2613","title":"update","createdAt":"2022-08-13T04:51:37Z"}
{"state":"Merged","mergedAt":"2022-08-13T05:45:33Z","number":2614,"mergeCommitSha":"630859a03fe0b9615f7958817aa45f0cdf43d7b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2614","title":"Api service should be following package naming similar to other services","createdAt":"2022-08-13T05:15:14Z"}
{"state":"Merged","mergedAt":"2022-08-13T20:01:14Z","number":2615,"body":"Lucene, for whatever reason, does not do proper segementation of periods in source code.\r\nSo fuck that, we'll do our own.","mergeCommitSha":"b6e3b5883c12b7cedfb13214454ee4968c4eb62b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2615","title":"Index source code properly","createdAt":"2022-08-13T07:35:32Z"}
{"state":"Merged","mergedAt":"2022-08-16T16:20:16Z","number":2616,"mergeCommitSha":"625e2169a48764438cf01643661f2047ffcfdea6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2616","title":"Confetti for everyone","createdAt":"2022-08-15T16:27:46Z"}
{"state":"Merged","mergedAt":"2022-08-15T18:04:33Z","number":2617,"body":"Reverts NextChapterSoftware/unblocked#2609","mergeCommitSha":"f7c3dd7d0e5a88d46ebcf9bf5dbf080431526cc6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2617","title":"Revert \"Make Message API models reusable for TLCs and CLCs\"","createdAt":"2022-08-15T17:48:17Z"}
{"state":"Merged","mergedAt":"2022-08-15T19:36:49Z","number":2618,"body":"### Changes\r\n1. Reapply original change: Revert \"Revert \"Make Message API models reusable for TLCs and CLCs (#2609)\" (#2617)\"\r\n2. Make `Message.threadId` optional.\r\n3. Continue to provide `threadId` value on the wire in order to not break legacy clients.\r\n\r\n### Follow-up plan\r\n- In a week or so, remove the deprecated `Message.threadId` field entirely.","mergeCommitSha":"78e82b73b52b27bdd20ce69b1b2c69d043962531","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2618","title":"Make Message API models reusable for TLCs and CLCs - V2","createdAt":"2022-08-15T18:51:02Z"}
{"state":"Merged","mergedAt":"2022-09-15T23:15:35Z","number":2619,"mergeCommitSha":"9b9b5886f417f9c35e69bc48a323671c80970849","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2619","title":"Obsolete Message.threadId [BREAKS API ON MAIN]","createdAt":"2022-08-15T19:03:35Z"}
{"state":"Merged","mergedAt":"2022-02-08T19:23:08Z","number":262,"body":"This is probably going to be a contentious PR but I don't care as this project is still in huge flux.\r\nIf it's too contentious, I'm going to drop it and move on with my life. :)\r\n\r\n1. Add shared client logger using winston (which allows for toggling/chaining transports and created child loggers).\r\n2. Add api endpoints for SAAS logging which will be authenticated in due time.\r\n3. Add custom winston transport that can send to a backing api endpoint to log to logz.io (this is entirely configurable and the client can control whether they want that..)\r\n4. Update webpack/tsconfig to allow for winston transports (console and file (for vscode) and maybe logz.io)\r\n\r\nTesting:\r\n1. Validated VSCode\r\n2. Validated Web\r\n\r\n### **Web Logging:**\r\n***From Browser***\r\n<img width=\"1508\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/152708692-39f25bda-85df-41c0-a41e-7223b769814e.png\">\r\n\r\n\r\n***From Logz.io***\r\n<img width=\"1307\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/152708709-db618921-dadc-4464-a5ff-d627ebf1cf14.png\">\r\n\r\n### **VSCode Logging**\r\n***From IDE***\r\n<img width=\"1270\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/152708748-7051cf84-590e-4b7b-8ae3-750e9f7fc155.png\">\r\n\r\n\r\n***From Logz.io***\r\n<img width=\"1264\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/152711625-3b426996-f895-4f04-a509-12ee719d152b.png\">\r\n\r\n\r\n***From File***\r\n<img width=\"806\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/152711768-6ae2d870-5f00-490e-a5e6-ebaf1c6bb734.png\">\r\n","mergeCommitSha":"a431c540e81343cc6bbb7031517a81d7e9f5817c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/262","title":"Add client side logging","createdAt":"2022-02-07T00:32:31Z"}
{"state":"Merged","mergedAt":"2022-08-16T13:15:13Z","number":2620,"body":"Fair bit of functionlity happening in admin-web that I'd like to take a look at.\nMight as well do it for all services?","mergeCommitSha":"909eb81b8c4f4623854865d876d09ceb9ed4afbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2620","title":"Add Honeycomb to all other web services","createdAt":"2022-08-15T19:13:41Z"}
{"state":"Merged","mergedAt":"2022-08-15T23:11:50Z","number":2621,"mergeCommitSha":"587ce78fa373638b1112e87bf9002ce55ba62f8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2621","title":"Add slack message analyzer","createdAt":"2022-08-15T20:38:53Z"}
{"state":"Merged","mergedAt":"2022-08-18T17:14:53Z","number":2622,"body":"First pass at updated sidebar resizer.\r\n\r\nThere are some QOL items that should still be added but not blocking.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/184717120-d83a0cd7-8c49-49e9-ac3b-73df8a2b5548.mp4\r\n\r\n","mergeCommitSha":"7ce24eeff1180272cac8ce4872dc061f2d48dffd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2622","title":"Updated Sidebar Resizer","createdAt":"2022-08-15T20:58:10Z"}
{"state":"Merged","mergedAt":"2022-08-15T23:13:05Z","number":2623,"body":"\r\nFixes UNB-546","mergeCommitSha":"c38d7776e8c0e2080416e5049fe192ed8f5f7950","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2623","title":"Fix some onboarding state bugs","createdAt":"2022-08-15T22:10:41Z"}
{"state":"Merged","mergedAt":"2022-08-17T01:52:13Z","number":2624,"mergeCommitSha":"ad81508ce205172039197b29429563f5c37bc792","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2624","title":"Upsert PullRequestReviewModel on webhook","createdAt":"2022-08-15T22:14:59Z"}
{"state":"Merged","mergedAt":"2022-08-16T00:24:42Z","number":2625,"mergeCommitSha":"5b6b2828e10766d120d8f371cfde9bb92a751c78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2625","title":"Bump main","createdAt":"2022-08-16T00:24:29Z"}
{"state":"Merged","mergedAt":"2022-08-17T06:53:02Z","number":2626,"body":"Part of https://linear.app/unblocked/issue/UNB-543\r\n\r\nIntroduces a snippet matching algorithm for cases where the untrusted and trusted\r\npoints have different file content hashes.\r\n- Match first on the snippet, then match using only the selected lines.\r\n- Prefer matches closest to where the original snippet is expected to\r\n  be when there are multiple matches.\r\n\r\n\r\n\r\nFile              | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s \r\n------------------|---------|----------|---------|---------|-------------------\r\nAll files         |     100 |      100 |     100 |     100 |                   \r\n SnippetFinder.ts |     100 |      100 |     100 |     100 |                   ","mergeCommitSha":"1b87bac636a9cbe3f4356035ea8004f262a18fc6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2626","title":"Sourcemark snippet finder","createdAt":"2022-08-16T06:04:41Z"}
{"state":"Merged","mergedAt":"2022-08-16T17:32:08Z","number":2627,"body":"Not sure why I named it `reviewed` before since `commented` is what the GitHub API calls it.\r\n\r\nShould be a safe change since no clients are using this.\r\n\r\nhttps://docs.github.com/en/rest/pulls/reviews#list-reviews-for-a-pull-request","mergeCommitSha":"36c698e5b82b68755b6cb4a518b9275fe62e3d64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2627","title":"David/rename review status","createdAt":"2022-08-16T16:37:59Z"}
{"state":"Merged","mergedAt":"2022-08-17T00:57:11Z","number":2628,"mergeCommitSha":"67b0a70d75574338cac1d404e80711c18f690b07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2628","title":"Add analyzer ast filter","createdAt":"2022-08-16T17:25:27Z"}
{"state":"Merged","mergedAt":"2022-08-16T21:04:40Z","number":2629,"body":"Please ignore the part where I'm manually restarting the hub in this gif (have to do that because the /Applications version is launched instead of my debug build)\r\n\r\n![CleanShot 2022-08-16 at 11 06 21](https://user-images.githubusercontent.com/858772/184949202-70eff125-c8ac-40e0-84ad-4c851f55eede.gif)\r\n","mergeCommitSha":"96949f901558c49b59ae86a2f538ccd8ca9308e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2629","title":"Upgrade progress and confirmation","createdAt":"2022-08-16T18:02:35Z"}
{"state":"Merged","mergedAt":"2022-02-07T18:12:26Z","number":263,"body":"## Problem\r\nPrevious implementation required adding the token for each new API frontend. \r\n\r\n## Changes\r\nThis PR implements a `TokenAuthProvider` which gives us ktor client DSL powers to load tokens at the client level. \r\n\r\n## Next\r\nThrow a cache in front of the `V3().installationAccessToken(installationId).token` call","mergeCommitSha":"ae4586997e993b73dbb3825007a72ab18ee155b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/263","title":"Add token auth provider for GQL client","createdAt":"2022-02-07T18:01:35Z"}
{"state":"Merged","mergedAt":"2022-08-16T20:46:06Z","number":2630,"body":"Reverts NextChapterSoftware/unblocked#2608","mergeCommitSha":"18aa1f825d6ad7acc9d3f3fe5be753e26cb9d92d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2630","title":"Revert \"[TLC] Refactor models to leverage Message model\"","createdAt":"2022-08-16T20:45:56Z"}
{"state":"Merged","mergedAt":"2022-08-16T21:46:37Z","number":2631,"body":"macOS Bug. Sound prefs do not show up in this panel unless the badge permission was also requested. \r\n\r\n![CleanShot 2022-08-16 at 13 58 13@2x](https://user-images.githubusercontent.com/858772/184983943-4ff220fd-c807-4100-90d6-1c5205fde680.png)\r\ne","mergeCommitSha":"96b4d8e87c9289445c34eef82edcaf3c92ece71e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2631","title":"Ask for badge permissions so that the sound settings show up in system prefs :facepalm:","createdAt":"2022-08-16T20:57:29Z"}
{"state":"Merged","mergedAt":"2022-08-16T23:52:06Z","number":2632,"mergeCommitSha":"c98ddb32f0b2a0fcd6de6d2ae7c693472101ec59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2632","title":"[TLC] Try Message model refactor again","createdAt":"2022-08-16T20:58:24Z"}
{"state":"Merged","mergedAt":"2022-08-16T21:43:58Z","number":2633,"body":"![CleanShot 2022-08-16 at 14 37 32@2x](https://user-images.githubusercontent.com/858772/184989785-b2fa649c-7398-4e44-9c9e-cc6e3790c7ba.png)\r\n","mergeCommitSha":"4ec2c1d07b45f5f1c945ab7366a7e78fd527e4a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2633","title":"Add skip VSCode setup option for 2nd-nth users","createdAt":"2022-08-16T21:36:49Z"}
{"state":"Merged","mergedAt":"2022-08-16T23:58:53Z","number":2634,"body":"![CleanShot 2022-08-16 at 16 16 54@2x](https://user-images.githubusercontent.com/858772/185001283-f3fde194-68b1-4028-bd95-e5ee76c1046d.png)\r\n","mergeCommitSha":"049d72d2d3047f5ea908b69e077637a986c17b11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2634","title":"Add skip link to install extensions onboarding view","createdAt":"2022-08-16T23:16:14Z"}
{"state":"Closed","mergedAt":null,"number":2635,"body":"Does not work end-to-end due to lack of Ktor support for compressed requests.\r\n\r\nThis is needed to get it working:\r\nhttps://linear.app/unblocked/issue/UNB-551/ktor-based-services-should-accept-and-decompress-compressed-request","mergeCommitSha":"123f0b18e7b08a1ff75c5c7b6d8bd855b215cc89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2635","title":"Hacky Test DO NOT MERGE: Gzip request bodies in VSCode","createdAt":"2022-08-17T00:02:32Z"}
{"state":"Merged","mergedAt":"2022-08-17T02:23:14Z","number":2636,"body":"https://app.logz.io/#/goto/dbdf1c1d2319a6a07574e77dc17b5a65?switchToAccountId=411850","mergeCommitSha":"103e3cc7cf7a3f4e86d7a99c984fefa3c8ae78fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2636","title":"pull_request_review_id can be null \uD83D\uDC4E ","createdAt":"2022-08-17T02:07:08Z"}
{"state":"Merged","mergedAt":"2022-08-17T04:58:43Z","number":2637,"body":"Un. Real.","mergeCommitSha":"080efdace9fbb9af53b788cb13b277b031b30adf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2637","title":"GitHub review webhooks have state in lowercase while GitHub API returns it in all caps","createdAt":"2022-08-17T03:33:00Z"}
{"state":"Merged","mergedAt":"2022-08-17T16:14:23Z","number":2638,"mergeCommitSha":"879291fc3cb4bda504cceb2a2fd1270ca25a5684","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2638","title":"Ingest pull request reviews","createdAt":"2022-08-17T03:43:32Z"}
{"state":"Merged","mergedAt":"2022-08-17T05:36:00Z","number":2639,"body":"https://linear.app/unblocked/issue/UNB-549/source-mark-engine-using-more-than-10-mb-memory-buffer-for-diffs","mergeCommitSha":"66745235c2f1ad9a18df95c46de3740301612f24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2639","title":"Source mark engine using more than 10 MB memory buffer for diffs resulting in failures [ERR_CHILD_PROCESS_STDIO_MAXBUFFER]","createdAt":"2022-08-17T04:11:24Z"}
{"state":"Merged","mergedAt":"2022-02-08T16:46:53Z","number":264,"body":"Logging out on a single tab should trigger all tabs to logout.\r\nAdded a window event to make sure all tabs are listening to sign out event.\r\n\r\nAlso fixed refresh logic so that new tabs will use the refresh token on init to get its own session storage base token.\r\n\r\nRefactor API layer into Shared.","mergeCommitSha":"bd14d0b865c052597d8d0a4c0c0855b65c467131","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/264","title":"Web Auth Updates","createdAt":"2022-02-07T19:20:51Z"}
{"state":"Merged","mergedAt":"2022-08-17T06:52:36Z","number":2640,"body":"Warnings will be logged: `\"Installation permissions are invalid\"`,\nwhich is useful for knowing which Orgs have not yet accepted the new permissions.\n\nUnlike #2572, the installation will not be uninstalled.\n","mergeCommitSha":"200eb923c4d3f003e5692904e0594cec070892ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2640","title":"GitHub app validation should include issues:read","createdAt":"2022-08-17T06:35:45Z"}
{"state":"Merged","mergedAt":"2022-08-23T23:19:04Z","number":2641,"body":"Review, but don't merge until _all_ PROD teams have accepted the latest set of permissions.\r\n\r\nValidate by checking for this message I Log:\r\n```\r\nInstallation permissions are invalid\r\n```\r\n\r\nLast remaining org, Clio:\r\n```json\r\n{\r\n  \"id\": ********,\r\n  \"app_id\": 166219,\r\n  \"target_type\": \"Organization\",\r\n  \"target_id\": 30769,\r\n  \"account\": {\r\n    \"login\": \"clio\",\r\n    \"id\": 30769,\r\n    \"type\": \"Organization\"\r\n  },\r\n  \"permissions\": {\r\n    \"members\": \"read\",\r\n    \"metadata\": \"read\",\r\n    \"pull_requests\": \"write\"\r\n  },\r\n  \"created_at\": \"2022-07-26T22:02:29Z\",\r\n  \"updated_at\": \"2022-07-26T22:02:29Z\"\r\n}\r\n```","mergeCommitSha":"c6db3976099eb42e05de0cb41165bccd6740ee17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2641","title":"Require that installations have GitHub app issues:read permission","createdAt":"2022-08-17T06:39:49Z"}
{"state":"Merged","mergedAt":"2022-08-17T16:45:29Z","number":2642,"body":"Right now the admin web button clears points that are not original;\nhowever this does not capture all of the possible generated points,\nbecause the trusted-original points are also generated.\n\nMy plan is to release recent sourcemark algorithm changes making sure\nthat most VSCode clients have upgraded, then clear the generated points.","mergeCommitSha":"ec87cd057463fc2d2e002fba6692a1c3d05d8b23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2642","title":"Clearing generated point should also clear trusted points","createdAt":"2022-08-17T15:21:51Z"}
{"state":"Merged","mergedAt":"2022-08-17T20:01:25Z","number":2643,"body":"Just the API implementation here. Client code in subsequent PR","mergeCommitSha":"ae253befd62bd98667c69061e1602e599a56c50b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2643","title":"Add clearUnreads operation","createdAt":"2022-08-17T15:59:55Z"}
{"state":"Merged","mergedAt":"2022-08-17T17:38:21Z","number":2644,"body":"https://ui.honeycomb.io/unblocked/environments/local/datasets/service/result/mL2knS89WMK/trace/CSzZcNWtJrj?fields[]=c_name&fields[]=c_service.name&span=525d31e7b48a389c","mergeCommitSha":"b1954cbd520fa62084bef8c8b4100a9cf46fdf3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2644","title":"Add content length to honeycomb","createdAt":"2022-08-17T17:21:38Z"}
{"state":"Merged","mergedAt":"2022-08-19T18:59:51Z","number":2645,"mergeCommitSha":"fdc12c7ce034f7bcdecbaf1083f2244323e1a358","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2645","title":"Check if review comment body is null or blank","createdAt":"2022-08-17T19:00:17Z"}
{"state":"Merged","mergedAt":"2022-08-17T19:59:49Z","number":2646,"mergeCommitSha":"74a16081fe2f5fd39d8a6eeb2acb7721ee0d9cd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2646","title":"Limit pusher logs","createdAt":"2022-08-17T19:36:15Z"}
{"state":"Merged","mergedAt":"2022-08-17T20:34:55Z","number":2647,"body":"<img width=\"488\" alt=\"CleanShot 2022-08-17 at 13 20 09@2x\" src=\"https://user-images.githubusercontent.com/858772/185235264-54dccfdc-c0a0-4079-937d-4b6ec6f8a2e6.png\">\r\n","mergeCommitSha":"fa7c9035281ae360526ca715ed2e7191e487e8a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2647","title":"Mark individual message as read","createdAt":"2022-08-17T20:18:12Z"}
{"state":"Merged","mergedAt":"2022-08-18T22:11:07Z","number":2648,"body":"There's a pretty savage hack in this one. What we'd ideally like is a `ContextMenu` instance to pop up when the hamburger menu is left-clicked, but SwiftUI has no affordance for triggering a context menu programatically (it only works with a right-click). \r\n\r\nI explored a few options:\r\n1. Emulate a right-click on the view - turns out to be pretty tricky because SwiftUI views are not really views, so you have to dispatch the event to the global event system. I couldn't figure out how to make this work (tried `NSEvent` based APIs and `NSApp.postEvent` with no luck)\r\n2. Attaching an `NSPopover` or `NSPopUpButtonCell` to an AppKit bridge view contained within the `LazyVStack` is fraught with peril. SwiftUI seems to have a race-condition bug where `NSViewRepresentable` objects sometimes don't actually get attached to the window. \r\n3. The race-condition bug was solved by deferring the view capture until it gets attached to the window, but it can be dealloc'd at any point in the future so attaching to this view is still perilous. Instead, we attach the popover to the window view and then offset the popover to the correct location. \r\n\r\nThere's one downside to this, which is that an `NSPopover` on top of another `NSPopover` allows events to flow down to the `NSPopover` underneath. This means that when a user clicks on the section header menu, they can continue to scroll the content underneath, but the menu remains in place. Normal context menu behaviour is that the window underneath stops receiving events until the menu is dismissed. This can be achieved using the `popUpContextMenu` api, but the frame isn't adjustable. Additionally, context menus don't support custom layouts. \r\n\r\nIf this behaviour is a problem then we can explore some hacky window solutions for dropping scroll events or something\r\n\r\n![CleanShot 2022-08-18 at 13 57 19](https://user-images.githubusercontent.com/858772/185494127-bb3dbbf7-2dc0-4029-be14-ec0fcb796700.gif)\r\n\r\n","mergeCommitSha":"34980500a27878416b1891f16279c74f00655615","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2648","title":"Mark all as read","createdAt":"2022-08-17T20:29:37Z"}
{"state":"Merged","mergedAt":"2022-08-17T22:36:44Z","number":2649,"body":"* Hook up to sidebar click\r\n* `PullRequestInfoView` houses a `PullRequestBlocks` list which renders a list of `PullRequestBlockView`s\r\n* Entry to this UI hides behind the same prSidebar feature flag\r\n\r\n\r\nOutstanding work (to be handled in ensuing PRs):\r\n- [x] Rows in the sidebar need to have a selected state\r\n- [x] Calendar/most recent pull requests filter should be sorted first (date | discussions | changes)\r\n- [ ] Update open file listener logic to handle focusing into PR Info panel\r\n- [ ] Add overlay to PR Info CRUD operations to immediate client operations\r\n- [ ] Add pusher channel for `pullRequestInfo/:pullRequestInfoId` to get updates for PR Info events\r\n- [ ] PR links in discussions should link to the PrInfoView \r\n\r\nCan ship without:\r\n- [ ] Collapsing thread blocks \r\n- [ ] Sticky title/number on scroll\r\n\r\nDeferring:\r\n- [ ] Outdated/Resolved UI ","mergeCommitSha":"9b9ba1391ff1f3e77ce3e225c77a8230c2f06210","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2649","title":"Add PullRequestInfo UI","createdAt":"2022-08-17T20:49:20Z"}
{"state":"Merged","mergedAt":"2022-02-07T21:41:49Z","number":265,"body":"## Problem\r\nCalls to app install GQL APIs are making calls to the install access token API every time. We should cache those tokens and blow them when they're about to expire.\r\n\r\n## What's changed\r\nIntroduced `InstallationTokenCache` to manage install tokens. This cache is not distributed so every service instance will have to load its own tokens. GitHub does not implement token families so this is ok.\r\n\r\n## What's next\r\nUse Redis","mergeCommitSha":"33ebe1fdf61960ef6c45864f2ec539a635d448dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/265","title":"Add install token cache","createdAt":"2022-02-07T19:29:47Z"}
{"state":"Merged","mergedAt":"2022-08-17T21:33:34Z","number":2650,"body":"Register if message is part of a thrad.","mergeCommitSha":"ac2a4e08b7cb6091d23493ac865ebf580db21715","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2650","title":"Slack analysis improvements","createdAt":"2022-08-17T20:53:12Z"}
{"state":"Merged","mergedAt":"2022-08-17T22:07:48Z","number":2651,"mergeCommitSha":"6017dbc166f3b11b129e01b73a745f2f98b05c08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2651","title":"Set PullRequestReviewModel.createdAt to review.submittedAt","createdAt":"2022-08-17T20:55:15Z"}
{"state":"Merged","mergedAt":"2022-08-17T22:33:28Z","number":2652,"body":"This was simply a mischaracterization, where we incorrectly treated a mark\nas `fault` instead of `notInRepo`. This specific case occurs when none of\nthe original points exist in the repo.\n\nhttps://linear.app/unblocked/issue/UNB-550/read-only-faults-on-2048","mergeCommitSha":"29892b8e6cfc761faa5df8e01b7c18c79ab501d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2652","title":"Fix read-only faults when original source point is not in repo","createdAt":"2022-08-17T22:12:32Z"}
{"state":"Merged","mergedAt":"2022-08-17T22:31:52Z","number":2653,"mergeCommitSha":"962adf3f1d08bf697169034d58d54758ef10b173","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2653","title":"update ktor version","createdAt":"2022-08-17T22:13:34Z"}
{"state":"Merged","mergedAt":"2022-08-17T23:16:57Z","number":2654,"body":"First pass at returning the comment counts with the `getPullRequestsForCommits` response.","mergeCommitSha":"d6936ccf0bfbf57e156d5f199940d1c79d431a59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2654","title":"Return comment count for pull requests","createdAt":"2022-08-17T22:23:54Z"}
{"state":"Merged","mergedAt":"2022-08-17T22:57:21Z","number":2655,"body":"…ation\r\n\r\nAs of 2.0.0 embedded postgres, they're providing apple silicon binaries for embedded postgres.","mergeCommitSha":"2b0d999e54c38cced8cdfb033acd74c60da7aa1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2655","title":"Move to native embedded postgres rather than x86 which was using emulation","createdAt":"2022-08-17T22:38:45Z"}
{"state":"Merged","mergedAt":"2022-08-17T23:19:20Z","number":2656,"body":"<img width=\"425\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/185258992-898420f4-f550-4638-986a-d8cacd9f86ec.png\">\r\n","mergeCommitSha":"b452e20e614e806b039f05f2866e497f2c2dea63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2656","title":"[TLC] Style selected PR row","createdAt":"2022-08-17T23:08:07Z"}
{"state":"Merged","mergedAt":"2022-08-18T16:21:08Z","number":2657,"body":"If one's repo was on an older commit, we would not show PR data from that commit to repo's tip of main.\r\n\r\nUpdated git log to include `--all` to fetch all commits (that are available to local git)\r\nUpdated getFolder to handle missing filePath by recursively traversing filePath string\r\n\r\nIn screenshot, I'm on a very old commit where `RelatedPullRequestPane` does not exist.\r\n<img width=\"1727\" alt=\"CleanShot 2022-08-17 at 16 09 51@2x\" src=\"https://user-images.githubusercontent.com/1553313/185259640-0388688d-d0f2-4a19-9e6a-9863a62423fe.png\">\r\n\r\n","mergeCommitSha":"d43fc97d1bd39d1936673b9864ffd439fc0c37cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2657","title":"Get log data for all","createdAt":"2022-08-17T23:13:57Z"}
{"state":"Merged","mergedAt":"2022-08-18T16:44:12Z","number":2658,"body":" Assets were not being uploaded at all for pr info view.\n\nAlso reducing code duplication","mergeCommitSha":"8097d7bacc43ae92937bb46a8a9f730c1e7d8277","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2658","title":"Fix pr view asset uploads","createdAt":"2022-08-18T00:45:34Z"}
{"state":"Closed","mergedAt":null,"number":2659,"body":"remaining work in this PR:\r\n- [ ] client version store tests\r\n- [ ] add to the team members page\r\n- [x] refactor plugin\r\n- [x] test for write-through redis cache, and maybe refactor for reuse","mergeCommitSha":"a482c1e8eb56a8943075889ae6a743f4e085602d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2659","title":"Show user client versions in admin web","createdAt":"2022-08-18T16:29:45Z"}
{"state":"Merged","mergedAt":"2022-02-07T22:25:05Z","number":266,"body":"- Created a new stack for handling cert issue requests\r\n- Added support for email cert validation (used in prod to create xyz.getunblocked.com certs when needed)\r\n- Created a separate stack for WAF (allows using a signle WAF for all static sites)\r\n- Refactored the StaticSite stack to support creation of multiple sites\r\n- Changed build config data structures as well as environment configs files to support above changes\r\n- Renamed/recreated our existing static site to `dashboard.{ENV}.getunblocked.com\r\n- Added 'dashboard.getunblocked.com` to prod static site SSL cert and CloudFront (no DNS record for it yet)\r\n\r\nI will be removing cert creation code in DNS Stack in favor of the new ACM stack at a later time.\r\n\r\nAll changes have been deployed to both Dev and Prod. ","mergeCommitSha":"40cad5943c06072a1184f8c8c97824572057e323","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/266","title":"Improve code structure to support CI/CD (Part 1 - Static Site)","createdAt":"2022-02-07T21:50:48Z"}
{"state":"Merged","mergedAt":"2022-08-18T18:44:53Z","number":2660,"body":"This channel can be used to check whether `/pullRequests/{pullRequestId}/info` has updates. Can be used by the PR view to allow refreshing if any of the following are modified:\r\n\r\n- Pull request (i.e. title, description, etc)\r\n- Pull request top-level comments & reviews\r\n- Pull request threads","mergeCommitSha":"ceb24a6b44b767a2e2f72faafe514b0a3df5f144","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2660","title":"Create PullRequestInfo pusher channel stub","createdAt":"2022-08-18T17:12:55Z"}
{"state":"Merged","mergedAt":"2022-08-18T18:48:59Z","number":2661,"mergeCommitSha":"28ef7f21fc47ea61e36825453dcb859333dddfdb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2661","title":"Refactor admin web badge","createdAt":"2022-08-18T18:33:12Z"}
{"state":"Merged","mergedAt":"2022-08-19T17:13:57Z","number":2662,"body":"* Fixes the min-width of the deletion column to align the rows\r\n<img width=\"368\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/185474763-f05ea8c3-3259-45ce-adb3-c429db6485c5.png\">\r\n\r\n\r\n* This does mean that for diffs with smaller digit diffs that the spacing would seem more wider than necessary but I think this is good enough for the majority of cases -- we'll need to revisit the logic here soon\r\n<img width=\"369\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/185474802-c4a4bba0-6419-47a4-9118-27622b39ee09.png\">\r\n","mergeCommitSha":"edd516c688f53e4bc7bcd83785ff51b363ca521c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2662","title":"[TLC] Adjust column widths","createdAt":"2022-08-18T19:07:29Z"}
{"state":"Merged","mergedAt":"2022-08-18T23:33:24Z","number":2663,"body":"Add overlay support for creating message.","mergeCommitSha":"a81dd2110c16604215a1f9a365a4713ca3cea5ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2663","title":"Support creating message in PR View with Overlay","createdAt":"2022-08-18T19:08:59Z"}
{"state":"Merged","mergedAt":"2022-08-18T20:16:18Z","number":2664,"body":"The download will be retried again when the user clicks on the upgrade button. This first download attempt is more of a convenience feature so that upgrading feels snappy when the dialog is presented - but if it fails we have a fallback (show spinner on upgrade button to signal work is being done)","mergeCommitSha":"061963a7e41ad24f32db34fe1853efb6bb70367d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2664","title":"Showing the upgrade window shouldn't depend on successful download","createdAt":"2022-08-18T20:04:30Z"}
{"state":"Merged","mergedAt":"2022-08-18T20:50:21Z","number":2665,"body":"There may be more to this issue. Strongly suspect a failure to write the hub app is at fault, which might cause the existing application to crash. We'll try to repro and follow up in another PR","mergeCommitSha":"664cd42f10704365cb3be2d16370dc999598f1ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2665","title":"Fix post-install app re-launch delay","createdAt":"2022-08-18T20:48:48Z"}
{"state":"Merged","mergedAt":"2022-08-18T21:57:10Z","number":2666,"body":"We are several million events over our quota.\r\nOne more month of this and they will suspend our account.\r\n\r\nThe idiomatic way of doing this for opentelemetry is via samplers.\r\nSince all of the existing opentelemetry samplers do not meet our needs, wrote my own that also uses baggage information to determine if something should be sampled.\r\nWe are using baggages because they are the paradigm for inheriting attributes between nested spans.\r\n\r\nThe flow in a nutshell:\r\n1. At top level we provide a rule based sampler that does not sample for deepcheck, shallowcheck apis.\r\n2. On top level ktor span creation, we create a baggage context that holds all http attributes.\r\n","mergeCommitSha":"71b8d65f0212a4433dbd8685bc819009a4d93f5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2666","title":"Address serious issues with Honeycomb overages","createdAt":"2022-08-18T21:02:29Z"}
{"state":"Closed","mergedAt":null,"number":2667,"mergeCommitSha":"8b702b3f251a14230feff958417c02fd4210b07c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2667","title":"WIP: Test jar diff","createdAt":"2022-08-18T21:34:38Z"}
{"state":"Merged","mergedAt":"2022-08-18T23:14:16Z","number":2668,"mergeCommitSha":"0911d1fb510b15c32fce5883a2027654677f466d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2668","title":"Introduce redis-backed write-through cache","createdAt":"2022-08-18T21:43:40Z"}
{"state":"Merged","mergedAt":"2022-08-19T00:16:18Z","number":2669,"body":"https://linear.app/unblocked/issue/UNB-432/admin-web-shows-users-latest-client-versions\r\n\r\n<img width=\"1128\" alt=\"Screen Shot 2022-08-18 at 17 15 51\" src=\"https://user-images.githubusercontent.com/1798345/185516258-2ff3f9de-0a77-488b-baac-9484c9ec894d.png\">\r\n","mergeCommitSha":"860e9499edf1040d98bffe57c071a0db70b4cf92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2669","title":"Show user client versions in admin web","createdAt":"2022-08-18T21:43:43Z"}
{"state":"Merged","mergedAt":"2022-02-07T23:47:45Z","number":267,"body":"Adds the ability to query the GitHub GraphQL api for a single pull request's review comment threads.  \r\n\r\nThe plan is to implement a batched versioned of this query so that we can grab review comment threads for multiple PRs more efficiently (to come in a separate PR). \r\n\r\nHowever, the API has page size limitations so this query will be called if individual PRs in the batch results have too many review threads.","mergeCommitSha":"4788d3e7e906ee32f6d8ee0c183ee05f0dfb2fbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/267","title":"Add PullRequest graphql query","createdAt":"2022-02-07T22:47:40Z"}
{"state":"Merged","mergedAt":"2022-08-18T22:25:40Z","number":2670,"body":".splice(-1) was only taking the last element...\r\nI had this fixed in my original branch. Not sure how this got in as is.\r\n\r\nAlso added extra check for empty strings.","mergeCommitSha":"f4705473d1d5de9b2ae44122fc5b6d95740d1984","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2670","title":"Fix Branch Issue","createdAt":"2022-08-18T21:51:14Z"}
{"state":"Merged","mergedAt":"2022-08-19T03:50:38Z","number":2671,"mergeCommitSha":"313fd8c2c3b1528d0311cea6eb44027a28fbcd83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2671","title":"PullRequestInfo pusher channel also checks comments, reviews, and threads modifiedAt","createdAt":"2022-08-18T22:25:26Z"}
{"state":"Merged","mergedAt":"2022-08-19T03:35:00Z","number":2672,"body":"![CleanShot 2022-08-18 at 16 00 40](https://user-images.githubusercontent.com/858772/185509738-04ceaf13-19fb-4226-adfd-96525ff27837.gif)\r\n","mergeCommitSha":"6821b23862f34877809d9b055824c84bcc6daa9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2672","title":"Adds unread only filter to hub","createdAt":"2022-08-18T22:56:15Z"}
{"state":"Merged","mergedAt":"2022-08-24T21:03:57Z","number":2673,"body":"If a pull request is not yet fully ingested, then this flag will be `true`. This will allow clients to show a loading state for a PR while awaiting ingestion.\r\n\r\nAt the same time, if the backend returns a PR where this flag is `true`, it will trigger  prioritized ingestion for this PR so that it can be ingested immediately. Once done, the PR's push channel will fire and clients can refresh to dismiss the loading state.","mergeCommitSha":"a150c3e4c7a008abb2212cec0f4eeb35570edf62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2673","title":"Add PullRequest api model property to signal when a PR is awaiting ingestion","createdAt":"2022-08-18T23:43:11Z"}
{"state":"Closed","mergedAt":null,"number":2674,"body":"* Resizer bars now push adjacent bars when resizing, up to the minimum panel size\r\n* Reorganize the code a bit -- extract the calculating bits into a separate file\r\n* Tests\r\n\r\nhttps://user-images.githubusercontent.com/2133518/185513980-bee0d76f-a955-497b-b940-44647b2a8872.mp4\r\n\r\n\r\n","mergeCommitSha":"04f03663cc8ade0898a1b794ebb4072d5d0ea8fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2674","title":"Sidebar resizer bar updates","createdAt":"2022-08-18T23:49:47Z"}
{"state":"Merged","mergedAt":"2022-08-19T05:00:33Z","number":2675,"body":"Setup Delete and Update","mergeCommitSha":"417103d0e3e104a254a11264a351916661e2111d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2675","title":"Additional commands for PR Overlay","createdAt":"2022-08-18T23:57:36Z"}
{"state":"Merged","mergedAt":"2022-08-19T22:56:02Z","number":2676,"body":"This is not working right and I have no idea why. All we get is a trace with:\r\n```\r\nERR syntax error\r\n```\r\n\r\nFull trace:\r\n```\r\nunblocked-apiservice-1           | 00:04:27 | ERROR | c.n.a.p.ClientVersionMetrics: Failed to record client version metric\r\nunblocked-apiservice-1           | { http.req.route=/api/teams/:id/threads/recommended, http.req.query=repoIds=aa57c947-b8a9-4078-9131-a5bff86e2918&repoIds=fb06ca32-cdf0-4bcc-9e08-c8c675768235&limit=11, requestId=17729abad3d43167, http.req.path=/api/teams/c9c47779-235f-4ca9-aefb-96758fa96650/threads/recommended, http.req.method=GET, http.req.header.User-Agent=node-fetch/1.0 (+https://github.com/bitinn/node-fetch) }\r\nunblocked-apiservice-1           | io.lettuce.core.RedisCommandExecutionException: ERR syntax error\r\nunblocked-apiservice-1           | \tat io.lettuce.core.internal.ExceptionFactory.createExecutionException(ExceptionFactory.java:147)\r\nunblocked-apiservice-1           | \tat io.lettuce.core.internal.ExceptionFactory.createExecutionException(ExceptionFactory.java:116)\r\nunblocked-apiservice-1           | \tat io.lettuce.core.RedisPublisher$SubscriptionCommand.doOnComplete(RedisPublisher.java:757)\r\nunblocked-apiservice-1           | \tat io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:65)\r\nunblocked-apiservice-1           | \tat io.lettuce.core.protocol.CommandHandler.complete(CommandHandler.java:747)\r\nunblocked-apiservice-1           | \tat io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:682)\r\nunblocked-apiservice-1           | \tat io.lettuce.core.protocol.CommandHandler.channelRead(CommandHandler.java:599)\r\nunblocked-apiservice-1           | \tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)\r\nunblocked-apiservice-1           | \tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)\r\nunblocked-apiservice-1           | \tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)\r\nunblocked-apiservice-1           | \tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\r\nunblocked-apiservice-1           | \tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)\r\nunblocked-apiservice-1           | \tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)\r\nunblocked-apiservice-1           | \tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\r\nunblocked-apiservice-1           | \tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\r\nunblocked-apiservice-1           | \tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:722)\r\nunblocked-apiservice-1           | \tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:658)\r\nunblocked-apiservice-1           | \tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:584)\r\nunblocked-apiservice-1           | \tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:496)\r\nunblocked-apiservice-1           | \tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\r\nunblocked-apiservice-1           | \tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\r\nunblocked-apiservice-1           | \tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\r\nunblocked-apiservice-1           | \tat java.base/java.lang.Thread.run(Thread.java:833)\r\n```","mergeCommitSha":"9f1937134957bd8388b6174a2b6e55ca54b68856","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2676","title":"Use redis write-through cache for ClientVersionMetrics","createdAt":"2022-08-19T00:06:31Z"}
{"state":"Merged","mergedAt":"2022-08-19T22:15:01Z","number":2677,"body":"![CleanShot 2022-08-18 at 17 46 13](https://user-images.githubusercontent.com/********/185518751-823eda0f-aa31-475e-bc87-41d5b2b4ec96.gif)\r\n","mergeCommitSha":"c8c72396d603c1ae046334823c8e586bff52d39a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2677","title":"Add sticky header on scroll","createdAt":"2022-08-19T00:46:51Z"}
{"state":"Merged","mergedAt":"2022-08-19T01:42:06Z","number":2678,"mergeCommitSha":"14c9e898015db89ecaba286580de61255315098a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2678","title":"Test for ClientVersionStore and better pill rendering","createdAt":"2022-08-19T01:31:33Z"}
{"state":"Merged","mergedAt":"2022-08-19T02:47:26Z","number":2679,"mergeCommitSha":"c2b78076cae94293c837fe47c218f560bb66972a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2679","title":"Add client version metrics to team members page","createdAt":"2022-08-19T02:15:11Z"}
{"state":"Merged","mergedAt":"2022-08-19T16:10:02Z","number":2680,"body":"Addresses comments from https://github.com/NextChapterSoftware/unblocked/pull/2663","mergeCommitSha":"628a078f928ad4a354464ec2aa94cca666611e52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2680","title":"Address comments from previous PR","createdAt":"2022-08-19T05:05:02Z"}
{"state":"Merged","mergedAt":"2022-08-19T18:17:01Z","number":2681,"body":"<img width=\"484\" alt=\"CleanShot 2022-08-19 at 09 06 58@2x\" src=\"https://user-images.githubusercontent.com/858772/185660895-cca337b0-1bb3-4236-ab10-d5dbcd01104f.png\">\r\n\r\nAlso fixes the hamburger menu rendering issue","mergeCommitSha":"47c257219c1fb7288f23c2813a71e68a470df815","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2681","title":"Add mention indicators","createdAt":"2022-08-19T16:06:46Z"}
{"state":"Merged","mergedAt":"2022-08-19T19:00:02Z","number":2682,"body":"Small optimization to avoid a bunch of unnecessary DB hits if threadIds passed is empty","mergeCommitSha":"c0d4dc3a287140c9be65defc883e7dd26d199d15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2682","title":"Return immediately from decorateThreads if threadIds is empty","createdAt":"2022-08-19T17:08:56Z"}
{"state":"Merged","mergedAt":"2022-08-19T17:47:58Z","number":2683,"body":"- Add failing test\r\n- Fix for ClientVersionModel failing to update to newer version","mergeCommitSha":"2a9bf2243476b8d77c309f0ef29d9b9dc6cad5e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2683","title":"Fix for ClientVersionModel failing to update to newer version","createdAt":"2022-08-19T17:31:53Z"}
{"state":"Merged","mergedAt":"2022-08-19T20:17:52Z","number":2684,"body":"When opening discussion from hub, VSCode will now foreground workspace instance\r\n\r\nhttps://user-images.githubusercontent.com/1553313/185695501-75c351e1-df68-41f7-9aa3-0f6e172be826.mp4\r\n","mergeCommitSha":"dfb0ffd5d561dc337e5fd24ab88e9464fb26406c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2684","title":"Open VSCode to foregroudn","createdAt":"2022-08-19T19:43:50Z"}
{"state":"Merged","mergedAt":"2022-08-19T21:47:07Z","number":2685,"body":"<img width=\"482\" alt=\"CleanShot 2022-08-19 at 12 44 35@2x\" src=\"https://user-images.githubusercontent.com/858772/185695927-59246738-1262-43ef-8ee9-0a70eca45559.png\">\r\n<img width=\"471\" alt=\"CleanShot 2022-08-19 at 12 46 30@2x\" src=\"https://user-images.githubusercontent.com/858772/185695979-8e123c77-e5c2-4290-bc2c-464ad9b257a3.png\">\r\n\r\n","mergeCommitSha":"fc50e9e39e5997d7fae724be7e24347c918a119f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2685","title":"Add unread disclosures to team header in hub","createdAt":"2022-08-19T19:46:08Z"}
{"state":"Merged","mergedAt":"2022-08-19T20:11:59Z","number":2686,"mergeCommitSha":"69de7f0312800c0103dc5e396f32f37170c3aabe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2686","title":"Only send click event to a single vscode instance","createdAt":"2022-08-19T19:51:11Z"}
{"state":"Merged","mergedAt":"2022-08-19T21:13:36Z","number":2687,"mergeCommitSha":"dfc4626e40d565522a37ebf14a392efbf03b2708","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2687","title":"PullRequestInfo should include reviews without threads","createdAt":"2022-08-19T20:20:50Z"}
{"state":"Merged","mergedAt":"2022-08-19T21:14:47Z","number":2688,"body":"I'm not sure if anything else needs to be done to enable this?","mergeCommitSha":"d18f2fc0bc2bffc780345ede8b7f38d3e84c73ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2688","title":"Allow API and admin service to send priority and standard PR ingestion events","createdAt":"2022-08-19T21:09:32Z"}
{"state":"Merged","mergedAt":"2022-08-22T18:13:37Z","number":2689,"body":"When clicking on the same PR in the sidebar, PR View would show loading state and never update.\r\n\r\nFix was to add onUpdate() to be called on `OpenPullRequestInfoCommand`\r\n\r\n","mergeCommitSha":"48669bafb3d87e301ae7e31f979f536560a54593","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2689","title":"Update pr info loading state","createdAt":"2022-08-19T21:27:47Z"}
{"state":"Merged","mergedAt":"2022-02-08T00:58:51Z","number":269,"body":"## Preamble\r\nSee: https://www.notion.so/nextchaptersoftware/GitHub-App-Install-Flow-ae89ddfcbb6348bfa2e1a7dbcbbbc9d3\r\n\r\n## Problem\r\nAfter the user auth flow, the client must understand whether it needs to push the user to install the GitHub App on an org. 4 states are possible:\r\n\r\n1. The user does not belong to any orgs with the app installed\r\n2. The user belongs to an org with the app installed, but not for the specific org they're interested in\r\n3. The app is installed for a particular org the user is interested in, but it lacks permissions for a repo they're interested in\r\n4. The app is installed for a particular org and has permission to the repo the user is interested in\r\n\r\n\r\n## Proposal \r\nStates (2)-(4) rely on the client passing a `repoUrl` to derive the org and repo. Unfortunately, we don't have control over the repos that the user gives permission to, so we will have to perform an extra bit of work to validate whether the app has access to the repo they're interested in _after_ the installation completes, and possibly hint to the user that they may have to correct the error with a link to the app configuration page. \r\n\r\nTo facilitate these flows we need a single API `/install/status/{provider}` that accepts an optional `repoUrl` and returns an `InstallState` object letting the client know where the user is in the install flow, either generally or for a particular org/repo. \r\n\r\nInstall states are:\r\n```\r\n[notInstalled, installed, configured]\r\n```\r\n\r\nBehaviour if the `repoUrl` parameter is included:\r\n- if not installed for org, return `notInstalled` with org install url\r\n- if installed for org but not configured for repo, return `installed` with app config url\r\n- if installed for org and configured for repo, return `configured` with app config url\r\n\r\nBehaviour if `repoUrl` parameter is omitted:\r\n- if user has no installed orgs, return `notInstalled` with personalized install url\r\n- if user has installed org but no configured repos (not sure if possible?), return `installed` with personalized install url\r\n- if user has installed org with configured repo(s), return `configured` with personalized install url\r\n\r\n","mergeCommitSha":"c5ae1dda732ee9e26b6d20eda68dfd33edccb1a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/269","title":"App install API ","createdAt":"2022-02-07T23:42:31Z"}
{"state":"Merged","mergedAt":"2022-08-19T22:39:18Z","number":2690,"body":"Changes\n- Persist only the biggest product number when there are multiple concurrent clients\n- Write through cache is updated with stored value\n\nProblem\n- A user has two VSCode extension clients, likely two VSCode workspaces, open at the same time.\n  The requests from each client are clobbering each other constantly.\n  This change reduces the DB thrashing.\n  https://ui.honeycomb.io/unblocked/environments/production/result/BV5wcFngy7o?useLogScale&useStackedGraphs\n","mergeCommitSha":"4dd8b7d52a01faf483023bda366e5cdc7e210589","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2690","title":"Persist only the biggest product number when there are multiple concurrent clients","createdAt":"2022-08-19T21:28:15Z"}
{"state":"Merged","mergedAt":"2022-08-19T22:09:45Z","number":2691,"body":"This reverts commit dfc4626e40d565522a37ebf14a392efbf03b2708.","mergeCommitSha":"ed85c442c37754133fe6d8ebb7d378d130af7e1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2691","title":"Revert \"PullRequestInfo should include reviews without threads (#2687)\"","createdAt":"2022-08-19T21:47:02Z"}
{"state":"Merged","mergedAt":"2022-08-23T16:26:47Z","number":2692,"body":"Sort wasn't syncing properly across explorer sidebar and unblocked sidebar.\r\n\r\nRefactored sort into its own stream & removed sort state within react component.","mergeCommitSha":"1a7fd090943b20e844c43ee2c9c889094cca44d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2692","title":"Sync PR Sidebar sort across sidebars","createdAt":"2022-08-19T22:08:11Z"}
{"state":"Merged","mergedAt":"2022-08-20T00:25:12Z","number":2693,"body":"Best-effort truncation of log json blobs to 5k.","mergeCommitSha":"c5c048ed1223bda0e8671462956795133faf2d27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2693","title":"Fix issue where logs were taking down api service","createdAt":"2022-08-19T22:30:15Z"}
{"state":"Merged","mergedAt":"2022-08-19T23:25:11Z","number":2694,"body":"![CleanShot 2022-08-19 at 15 26 12](https://user-images.githubusercontent.com/********/185715297-195a9a37-596a-4ff1-b423-e8dd13d0aadd.gif)\r\n","mergeCommitSha":"5815c340e7443ce517102c5d7c2347eac2d63a3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2694","title":"[TLC] Discussion pr links should link to UB PR View","createdAt":"2022-08-19T22:32:50Z"}
{"state":"Merged","mergedAt":"2022-08-19T23:59:36Z","number":2695,"body":"In openapitools we trust","mergeCommitSha":"1b0e91025b1e5d8282a4ac81a8759e0556c0b080","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2695","title":"API Backwards Compatibility checks","createdAt":"2022-08-19T23:01:40Z"}
{"state":"Merged","mergedAt":"2022-08-22T19:43:29Z","number":2696,"body":"<img width=\"527\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/185719921-75b74af5-4dbf-4feb-b122-95fab8467ebb.png\">\r\n\r\n<img width=\"335\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/185719939-62b2515c-9b7d-4e3a-aae4-f47dbec8e021.png\">\r\n\r\n<img width=\"362\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/185720350-64d08ba0-4309-4e1e-a83b-a13a751113c9.png\">\r\n\r\n","mergeCommitSha":"2a11b2ba639ac4e0db4c717c735283818a4d18c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2696","title":"Add thread level pr link to discussion UI","createdAt":"2022-08-19T23:49:34Z"}