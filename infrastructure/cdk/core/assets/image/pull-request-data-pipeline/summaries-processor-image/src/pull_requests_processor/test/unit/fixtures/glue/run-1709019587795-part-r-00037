{"state":"Merged","mergedAt":"2023-10-06T01:55:19Z","number":8560,"mergeCommitSha":"f4d9ce1ff0c8cbe34da62f335f95b2ac2696a937","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8560","title":"Log baby log","createdAt":"2023-10-06T01:48:23Z"}
{"state":"Merged","mergedAt":"2023-10-06T02:29:58Z","number":8561,"body":"- Log baby log\r\n- log\r\n- More logging\r\n","mergeCommitSha":"9154c3c3fb6205edc08083eca7510148ea828394","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8561","title":"MoreLogs","createdAt":"2023-10-06T02:29:00Z"}
{"state":"Merged","mergedAt":"2023-10-06T02:43:49Z","number":8562,"mergeCommitSha":"9142e47e6fccc3e6e82b2e28ca4e73c2d77814fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8562","title":"Fix lint","createdAt":"2023-10-06T02:43:27Z"}
{"state":"Merged","mergedAt":"2023-10-06T03:18:24Z","number":8563,"mergeCommitSha":"459c59cdec59fd4e205446e4f28c85cc284b054f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8563","title":"[SKIP TESTS] Turn off code ingestion","createdAt":"2023-10-06T03:17:43Z"}
{"state":"Merged","mergedAt":"2023-10-06T03:18:55Z","number":8564,"mergeCommitSha":"8b1155fa99b7871bdf0ba65aa8d43c2bedeca66e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8564","title":"Reduce verbosity","createdAt":"2023-10-06T03:18:50Z"}
{"state":"Merged","mergedAt":"2023-10-06T04:07:55Z","number":8565,"mergeCommitSha":"6917de193809fde990cd616fa4b627c27a6e615e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8565","title":"New 20 node Pinecone index and re-enable code ingestion","createdAt":"2023-10-06T03:22:27Z"}
{"state":"Merged","mergedAt":"2023-10-06T03:59:02Z","number":8566,"body":"This entire repo is generated code. We need to skip most of it.\nhttps://github.com/envoy/google-api-java-client-services","mergeCommitSha":"ee0ce639ef6edd91840cfa37a376ff9015ca1102","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8566","title":"Skip more types of generated files","createdAt":"2023-10-06T03:52:00Z"}
{"state":"Merged","mergedAt":"2023-10-10T17:32:55Z","number":8567,"body":"Risky business, needs testing with backend data moving it through the transition states","mergeCommitSha":"1c0d27e803bce9a1d0dc87a86e81291ee97167e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8567","title":"Drop onboarding early for standard teams","createdAt":"2023-10-06T05:54:16Z"}
{"state":"Closed","mergedAt":null,"number":8568,"body":"https://chapter2global.slack.com/archives/C05VA36SDNY/p1696615164835319","mergeCommitSha":"6f2eb00363991bfef0d384e9b945fb0c57dacc96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8568","title":"API to return previous questions","createdAt":"2023-10-06T06:31:20Z"}
{"state":"Merged","mergedAt":"2023-10-06T17:24:18Z","number":8569,"mergeCommitSha":"0959806257351a0b3cb650f12039757286b4ab7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8569","title":"Disable pull request summary ingestion","createdAt":"2023-10-06T17:19:55Z"}
{"state":"Merged","mergedAt":"2022-04-11T18:27:31Z","number":857,"body":"Ensure keys like 'environment' do not conflict.","mergeCommitSha":"201b65b7c7d674890676a704f2bc0d09a5edc13d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/857","title":"Client logger keys should be distinguishable","createdAt":"2022-04-11T18:14:10Z"}
{"state":"Merged","mergedAt":"2023-10-06T18:24:57Z","number":8570,"mergeCommitSha":"fe95ff601a3266daac3bc3aad4c3ffabc7535c90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8570","title":"Expose full git clone command in RepoPage as convenience","createdAt":"2023-10-06T18:05:22Z"}
{"state":"Merged","mergedAt":"2023-10-06T18:55:20Z","number":8571,"body":"[SKIP TESTS] ","mergeCommitSha":"946a4a0fc2b318ff9099ede33e0f9d22589d3626","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8571","title":"[SKIP TESTS] Feedback votes","createdAt":"2023-10-06T18:28:33Z"}
{"state":"Merged","mergedAt":"2023-10-06T19:10:52Z","number":8572,"body":"Teams from GitHub accounts can be created in two ways:\r\n 1. from GitHub webhook in scm-service\r\n 2. from createScmInstallation API in api-service\r\n\r\nThe second path had a bug. If the scm-service won, then we're good. If the api-service won then the team will appear to be disconnected. This change fixes the second path.","mergeCommitSha":"e8b22704cffa774d65eed46bea756e52d55c49ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8572","title":"Fix GitHub Org team creation race","createdAt":"2023-10-06T19:00:21Z"}
{"state":"Merged","mergedAt":"2023-10-06T19:09:08Z","number":8573,"body":"Reverts NextChapterSoftware/unblocked#7647","mergeCommitSha":"56e76ca59b3254e25c35b7e8454f28b912e9dd22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8573","title":"Revert \"More realistic delete repo unit test to exercise DB slow query\"","createdAt":"2023-10-06T19:08:54Z"}
{"state":"Merged","mergedAt":"2023-10-06T19:33:38Z","number":8574,"mergeCommitSha":"8380e8f855ead97fa93a08f74d010d006995dc2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8574","title":"lint","createdAt":"2023-10-06T19:33:31Z"}
{"state":"Merged","mergedAt":"2023-10-06T19:51:41Z","number":8575,"mergeCommitSha":"ba34b95554eaadd6a9cf0c58bafd4b5ba166e9f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8575","title":"Hack to restart GitHub install sync","createdAt":"2023-10-06T19:47:16Z"}
{"state":"Merged","mergedAt":"2023-10-06T21:52:30Z","number":8576,"body":"* Rename Inbox -> My Discussions\r\n* Remove Recommended in light mode\r\n* Remove Recently Deleted in light mode (note: may look into re-adding this in the future, to handle the cases where users delete a QA thread and want to resurrect it)\r\n* Rename Team Settings -> Team Integrations in light mode\r\n* Team Settings UI: Remove Components navigator in light mode\r\n* Team Settings UI: Navigate to correct default location (components in team mode, provider in light mode)\r\n","mergeCommitSha":"9eef3455aae6d95178c8cd3850fca234592e21bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8576","title":"More dashboard light mode stuff","createdAt":"2023-10-06T20:30:40Z"}
{"state":"Merged","mergedAt":"2023-10-06T20:35:38Z","number":8577,"mergeCommitSha":"2af1c621435b7058193e7e6a78a6d2596f318b9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8577","title":"Try 4xlarge","createdAt":"2023-10-06T20:35:34Z"}
{"state":"Merged","mergedAt":"2023-10-06T20:53:12Z","number":8578,"body":"Dev already has this. ","mergeCommitSha":"a3e45248fdb712d11a85edccf1bd05940e8334ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8578","title":"Allow vpn access to prod redis","createdAt":"2023-10-06T20:52:10Z"}
{"state":"Merged","mergedAt":"2023-10-06T21:10:37Z","number":8579,"mergeCommitSha":"68d531323e7c2d77f9d73ce933d9f576582e8d28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8579","title":"Automatically move team to full mode if criteria satisfied","createdAt":"2023-10-06T21:00:35Z"}
{"state":"Merged","mergedAt":"2022-04-11T19:16:32Z","number":858,"body":"For cleanup etc..\r\n","mergeCommitSha":"dd8e069e4fe1bf01b546370d0e4adc68691dc93f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/858","title":"Add ability to delete asset","createdAt":"2022-04-11T19:05:54Z"}
{"state":"Merged","mergedAt":"2023-10-06T22:55:54Z","number":8580,"body":"<img width=\"1869\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/f7fbbc3c-a1d4-41cb-80a1-605ddc6342ea\">\r\n\r\n","mergeCommitSha":"d85f7da9fda9698d9f5ac7c9e291aa1db3be085f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8580","title":"Move qa input to view","createdAt":"2023-10-06T21:11:21Z"}
{"state":"Merged","mergedAt":"2023-10-07T05:36:48Z","number":8581,"body":"Just looks at PR count for now. Will investigate how to get comment counts.","mergeCommitSha":"95689b36930a13b7de01a34bc7ba978b062fce0b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8581","title":"Implement teamSatisfiesCriteria","createdAt":"2023-10-06T22:07:15Z"}
{"state":"Open","mergedAt":null,"number":8582,"body":"Instead of having the welcome screen live at the beginning on the onboarding flow, generate a welcome step factory and show welcome *after* login in onboarding window.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/6edbb476-2ec4-4f9f-bdf6-68ac5764615d\r\n\r\n","mergeCommitSha":"9a223ee30570aca28a799292779eb9732f6a8ff1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8582","title":"Move welcome step as part of onboarding steps","createdAt":"2023-10-06T22:08:39Z"}
{"state":"Merged","mergedAt":"2023-10-06T23:14:41Z","number":8583,"mergeCommitSha":"3a59274b65e7a742ce0c8903ebabbe30074b52e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8583","title":"Try create or replace","createdAt":"2023-10-06T23:14:36Z"}
{"state":"Merged","mergedAt":"2023-10-08T22:29:16Z","number":8584,"body":"Next PR will generate the events and add handler logic","mergeCommitSha":"3922e55e96b586a5c2a560c63ba256602f8049e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8584","title":"Add config.queue.migrationEventsQueueName","createdAt":"2023-10-06T23:38:44Z"}
{"state":"Merged","mergedAt":"2023-10-06T23:58:48Z","number":8585,"mergeCommitSha":"f17e4e1a73946d252455ae5324414e38e5a71f9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8585","title":"[SKIP TESTS] - Wrap team mode in config at API layer","createdAt":"2023-10-06T23:45:37Z"}
{"state":"Merged","mergedAt":"2023-10-07T00:29:15Z","number":8586,"body":"<img width=\"724\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/6536a844-8372-4562-9a6c-a79ed8166c8c\">\r\n","mergeCommitSha":"f0b3c36931ebdf7e4d8d8e59379f279fe263c217","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8586","title":"Fix qa suggestions","createdAt":"2023-10-06T23:52:13Z"}
{"state":"Merged","mergedAt":"2023-10-07T00:07:00Z","number":8587,"mergeCommitSha":"a73e8df209e20ddb94e36676360be15847dd10aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8587","title":"[SKIP TESTS] - Remove enabled confirmation dialog","createdAt":"2023-10-07T00:04:35Z"}
{"state":"Merged","mergedAt":"2023-10-07T03:38:01Z","number":8588,"mergeCommitSha":"a17c7849b03b3f74c5fa2cfe7e98482e7144271a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8588","title":"Reduce tuple creation for slack","createdAt":"2023-10-07T02:27:20Z"}
{"state":"Merged","mergedAt":"2023-10-07T03:10:40Z","number":8589,"mergeCommitSha":"9e0569936bbae26dc8e58d27182f334b3f60ab43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8589","title":"[SKIP TESTS] - Allow team disablement in all envs but Prod","createdAt":"2023-10-07T02:37:27Z"}
{"state":"Merged","mergedAt":"2022-04-11T20:08:50Z","number":859,"body":"Splitting these changes out of a larger PR to make easier to review.\n\nThese will be used in a follow on PR.","mergeCommitSha":"5d1c3326273f13a03caffaa7eeb943a6cc58b12c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/859","title":"Some more utils","createdAt":"2022-04-11T19:43:19Z"}
{"state":"Merged","mergedAt":"2023-10-07T03:38:39Z","number":8590,"mergeCommitSha":"c2c6591f2d14be5c64e3af9868eff562aa226bea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8590","title":"Schema optimizations 2","createdAt":"2023-10-07T03:17:53Z"}
{"state":"Merged","mergedAt":"2023-10-10T05:15:24Z","number":8591,"mergeCommitSha":"f3d9986c42b6e5e91617db0eb0eab767630810c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8591","title":"Remove 'My Profile' in light mode","createdAt":"2023-10-07T03:20:44Z"}
{"state":"Merged","mergedAt":"2023-10-07T03:53:46Z","number":8592,"mergeCommitSha":"da7e623b519777a03e9ad6d67254d66aacbdaeb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8592","title":"[SKIP TESTS] - actually fix team enablement","createdAt":"2023-10-07T03:30:44Z"}
{"state":"Merged","mergedAt":"2023-10-07T03:46:44Z","number":8593,"body":"- Schema optimizations 2\r\n- Clean up\r\n- Commit\r\n","mergeCommitSha":"1b148420ea9c99db6ecf4e19c1bf62da35135990","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8593","title":"SchemaOoptimizations2","createdAt":"2023-10-07T03:45:40Z"}
{"state":"Merged","mergedAt":"2023-10-07T04:28:18Z","number":8594,"body":"As informed by pg log query analysis","mergeCommitSha":"eedf00179c4be7b742ad00d498af26aec3ff3b1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8594","title":"Index optimization","createdAt":"2023-10-07T04:28:04Z"}
{"state":"Merged","mergedAt":"2023-10-07T05:29:17Z","number":8595,"mergeCommitSha":"3b065738ab6d0fde57ac96f744c9485a9c6cd002","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8595","title":"Fix slow query 2","createdAt":"2023-10-07T05:28:57Z"}
{"state":"Merged","mergedAt":"2023-10-07T06:11:14Z","number":8596,"body":"Okay, this one is super insidious and is a really frequently called operation:\r\n```\r\nTeamMemberModel\r\n    .innerJoin(IdentityModel)\r\n    .slice(TeamMemberModel.columns + IdentityModel.columns)\r\n    .select(\r\n        AndOp(\r\n            listOfNotNull(\r\n                TeamMemberModel.team eq teamId,\r\n                IdentityModel.provider eq provider,\r\n                additionalWhereClause,\r\n            ),\r\n        ),\r\n    )\r\n    .map {\r\n        TeamMemberAndIdentity(\r\n            TeamMemberDAO.wrapRow(it).asDataModel(),\r\n            IdentityDAO.wrapRow(it).asDataModel(),\r\n        )\r\n    }\r\n```\r\nThe Identity join is a full table scan.\r\nThe reason is the where clause IdentityModel.provider eq provider\r\nTo get around that, you have to create a compound index not only on provider but also the primary key.\r\nindex(isUnique = true, id, provider)\r\nIt's surprising the query planner does not correctly optimize for this. (edited)\r\n","mergeCommitSha":"56e6b7e7ce4feb98e5d0994f26af5e6b290c9674","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8596","title":"Fix slow queries for TeamMember to IdentityModel join","createdAt":"2023-10-07T06:03:45Z"}
{"state":"Open","mergedAt":null,"number":8597,"body":"I don't believe we call this anymore","mergeCommitSha":"f8b88b181bd67411cd0b029c07a2fce1a8eb7927","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8597","title":"Depcrecate getInsightSearchSuggestions","createdAt":"2023-10-07T16:52:52Z"}
{"state":"Merged","mergedAt":"2023-10-10T16:55:11Z","number":8598,"body":"https://app.intercom.com/a/inbox/crhakcyc/inbox/shared/mentions/conversation/894?view=List","mergeCommitSha":"9fb82f670bda50bb8137f012b8cdd77bff1747cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8598","title":"Fix copy input button","createdAt":"2023-10-09T02:02:16Z"}
{"state":"Merged","mergedAt":"2023-10-09T17:47:50Z","number":8599,"mergeCommitSha":"e0e7ad66acb733fb3717347a8f26f5dc13a7e5ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8599","title":"[SKIP TESTS] Stop code ingestion","createdAt":"2023-10-09T17:47:38Z"}
{"state":"Merged","mergedAt":"2022-01-20T16:55:57Z","number":86,"body":"This change re-introduces auth templating for endpoints, and makes all endpoints opt-out.\r\n","mergeCommitSha":"99705c05a59bde52c471dac87846721b87002c51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/86","title":"Add authenticated testing endpoint","createdAt":"2022-01-19T23:54:49Z"}
{"state":"Merged","mergedAt":"2022-04-12T18:55:33Z","number":860,"body":"1. Plumbing the TextEditorSourceMarkManager to the SourceMarkProvider\n2. Introduce RepoResolver which wraps RepoStore and providing a way to lookup repo information quickly\n3. Introduce getLocationForSourceMark\n4. Support for uncommitted repo changes","mergeCommitSha":"bf852afc0268a6509f6eeab9f6536bb03fd48c79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/860","title":"Support for uncommitted changes and plumbing","createdAt":"2022-04-11T19:53:18Z"}
{"state":"Merged","mergedAt":"2023-10-09T22:24:04Z","number":8600,"mergeCommitSha":"4995abc579180c8f70227127c861e65537e9c509","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8600","title":"Add admin console button to trigger message truncation","createdAt":"2023-10-09T19:43:51Z"}
{"state":"Merged","mergedAt":"2023-10-10T17:44:05Z","number":8601,"mergeCommitSha":"b11068d7aaad6b733461c65a3eb41e2414528c86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8601","title":"Skip IDE install for users not on an enabled Full mode team","createdAt":"2023-10-09T20:49:03Z"}
{"state":"Merged","mergedAt":"2023-10-10T17:45:27Z","number":8602,"mergeCommitSha":"331f4e2ae385a1729e1bb0f9ef474bbd329cc981","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8602","title":"Drop video button from hub in standard mode","createdAt":"2023-10-09T21:02:25Z"}
{"state":"Merged","mergedAt":"2023-10-10T17:46:59Z","number":8603,"mergeCommitSha":"a09cb5363f62f1e8600dd53371ad8fc107ef18cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8603","title":"Remove threads hamburger menu in standard mode","createdAt":"2023-10-09T21:35:02Z"}
{"state":"Merged","mergedAt":"2023-10-10T17:47:55Z","number":8604,"mergeCommitSha":"0d8dc53f18652a83e9b0d7fea06d6327ab9b76e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8604","title":"Opening threads defaults to dashboard in standard mode","createdAt":"2023-10-09T22:01:33Z"}
{"state":"Merged","mergedAt":"2023-10-10T17:30:03Z","number":8605,"mergeCommitSha":"bff511b60e3fab75b973ac89791bd051a643ade2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8605","title":"Add ability to reword sample question for a topic","createdAt":"2023-10-09T22:16:24Z"}
{"state":"Merged","mergedAt":"2023-10-10T03:12:33Z","number":8606,"mergeCommitSha":"df6dc1136d7468b316fea050bd7c254837e4d3ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8606","title":"trying to fix the secrets action","createdAt":"2023-10-10T03:12:26Z"}
{"state":"Merged","mergedAt":"2023-10-10T03:28:51Z","number":8607,"mergeCommitSha":"c7d272ec05ccf1b36edfe6096da030bf76ff1639","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8607","title":"disable secrets deployments","createdAt":"2023-10-10T03:28:44Z"}
{"state":"Merged","mergedAt":"2023-10-11T21:43:33Z","number":8609,"mergeCommitSha":"dcc2409c39dfca50d0cda53c5536f85a153d3364","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8609","title":"Update NotionPageIngestionService to retrieve all blocks for a page","createdAt":"2023-10-10T05:22:23Z"}
{"state":"Merged","mergedAt":"2022-04-11T20:54:52Z","number":861,"body":"<img width=\"270\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/162825820-49cb346f-1025-40a6-8184-b42c3ca42ac8.png\">\r\n","mergeCommitSha":"c110b191cc29c8671a6953a40d738aa3decbb242","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/861","title":"Update fontawesome lib with thumbtack fix","createdAt":"2022-04-11T20:25:08Z"}
{"state":"Merged","mergedAt":"2023-10-10T06:04:19Z","number":8610,"mergeCommitSha":"28e00e77dabe0bce6d9924dc64f87c6d8fdf0392","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8610","title":"[SKIP TESTS] Wrap in coroutine","createdAt":"2023-10-10T05:43:06Z"}
{"state":"Closed","mergedAt":null,"number":8611,"body":"Let's not go buck-wild","mergeCommitSha":"b4c8765764edc6d58a73661716e5c407433f0a6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8611","title":"Double consumer count to 2 for the migration event queue","createdAt":"2023-10-10T15:47:03Z"}
{"state":"Merged","mergedAt":"2023-10-10T18:39:24Z","number":8612,"mergeCommitSha":"1833f12db7277812502257306ce96c2a55d9b313","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8612","title":"Deploy secrest","createdAt":"2023-10-10T17:02:16Z"}
{"state":"Merged","mergedAt":"2023-10-10T17:16:35Z","number":8613,"mergeCommitSha":"0b4ee4b3aff71ad6d9a6975f30519d6b52fe6c37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8613","title":"Submodule bump includes new transition flow onboarding assets","createdAt":"2023-10-10T17:05:29Z"}
{"state":"Closed","mergedAt":null,"number":8614,"mergeCommitSha":"26380baf48e7b6b4189f91335d4aefae08a77a0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8614","title":"Add PersonalScmAccounts client capability feature flag","createdAt":"2023-10-10T17:11:21Z"}
{"state":"Merged","mergedAt":"2023-10-10T18:59:54Z","number":8615,"body":"<img width=\"398\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/8e3dff87-73f6-47c7-8a0b-120008a6d89a\">\r\n<img width=\"1498\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/f4c6bdf2-de71-4c8a-a63a-99d3dd40be99\">\r\n","mergeCommitSha":"7acde545768d74ca46e96abb78195a437b004cfe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8615","title":"Add ability to add new team","createdAt":"2023-10-10T17:13:30Z"}
{"state":"Closed","mergedAt":null,"number":8616,"mergeCommitSha":"bc69bb19d3fc5c25404f958254775ba204fb55fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8616","title":"New transition onboarding flow","createdAt":"2023-10-10T17:34:23Z"}
{"state":"Merged","mergedAt":"2023-10-10T18:17:27Z","number":8617,"mergeCommitSha":"53c3ee43b13a4b1aef7e14a0ec3d3330bf0e049c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8617","title":"Fix TopicPage to show generated sample question","createdAt":"2023-10-10T18:05:41Z"}
{"state":"Merged","mergedAt":"2023-10-10T18:46:40Z","number":8618,"mergeCommitSha":"d3624d6c656d0726578b1cdd563080507590d33b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8618","title":"Feedback","createdAt":"2023-10-10T18:15:00Z"}
{"state":"Merged","mergedAt":"2023-10-10T18:32:26Z","number":8619,"mergeCommitSha":"547998902a9c6b6d17d9772f770d048b134ca7f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8619","title":"New 40 node Pinecone index and re-enable code ingestion","createdAt":"2023-10-10T18:27:59Z"}
{"state":"Merged","mergedAt":"2022-04-11T22:11:35Z","number":862,"body":"WE already correctly name it  for presign url when we specify the contentDisposition. Not sure why I ever did this. :)","mergeCommitSha":"ca429571fc6dcf584dd32557251350188fb1ecd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/862","title":"Stupid Rashin should not be using customer names for s3 keys","createdAt":"2022-04-11T21:39:29Z"}
{"state":"Merged","mergedAt":"2023-10-10T19:18:56Z","number":8620,"mergeCommitSha":"e8f17805465cc41b233ed0793dd8e519bc3256bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8620","title":"Update processing wording","createdAt":"2023-10-10T18:51:01Z"}
{"state":"Merged","mergedAt":"2023-10-10T19:58:56Z","number":8621,"mergeCommitSha":"37b46446a0f7f4e6e7ac1379d29d18b17bf9eb7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8621","title":"Optimize query","createdAt":"2023-10-10T19:12:21Z"}
{"state":"Merged","mergedAt":"2023-10-10T20:10:07Z","number":8622,"mergeCommitSha":"2c8b0311fa8191182c6b8d85c6349736eff29f72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8622","title":"Missing install redirect","createdAt":"2023-10-10T19:57:31Z"}
{"state":"Merged","mergedAt":"2023-10-10T20:41:54Z","number":8623,"mergeCommitSha":"ea27786e4ad2de4ec0a3bf4612513a173ad87e01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8623","title":"Reduce dead tuple generation","createdAt":"2023-10-10T20:16:22Z"}
{"state":"Merged","mergedAt":"2023-10-10T20:27:53Z","number":8624,"mergeCommitSha":"517b5d8b452dc6db4cc50060fff4339cee5902a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8624","title":"Ramp up code ingestion","createdAt":"2023-10-10T20:24:16Z"}
{"state":"Merged","mergedAt":"2023-10-10T20:58:18Z","number":8625,"mergeCommitSha":"4cb772ca2d24eb1e1def30245f7f033ee1ed5d89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8625","title":"Revert redirect change","createdAt":"2023-10-10T20:46:01Z"}
{"state":"Merged","mergedAt":"2023-10-10T21:52:38Z","number":8627,"mergeCommitSha":"b45fb96ac8bc85ba5c64e09dd70d4c91a0d0f8b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8627","title":"Fix dashboard inputs","createdAt":"2023-10-10T21:17:31Z"}
{"state":"Merged","mergedAt":"2023-10-10T22:11:09Z","number":8628,"body":"- More tuple dedupe\r\n- Lint\r\n","mergeCommitSha":"918bef230b80ca5277af866f0e024cd4dba1d391","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8628","title":"More tuple dedupes in ingestion pipelines","createdAt":"2023-10-10T21:40:50Z"}
{"state":"Merged","mergedAt":"2023-10-11T00:40:04Z","number":8629,"mergeCommitSha":"916d5f72b1c254019d338f31ad2ad5e671a70ce2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8629","title":"Update processing complete email template","createdAt":"2023-10-10T21:47:40Z"}
{"state":"Merged","mergedAt":"2022-04-13T16:15:08Z","number":863,"body":"Send current user's name, email, ID, and avatar to Intercom.\r\n\r\nI implemented this by adding a `person` prop to the `IntercomProvider`.  In the web we populate this directly from the AuthStore, in VSCode we pipe this through the webview properties, add it to the webview context, and then pipe it into the IntercomProvider from there.\r\n\r\n\r\n<img width=\"1425\" alt=\"Screen Shot 2022-04-11 at 3 17 28 PM\" src=\"https://user-images.githubusercontent.com/2133518/*********-7e328760-aaef-40f7-934d-a1c1beee6f59.png\">\r\n","mergeCommitSha":"b25bfd06c5087b35d6965681f64fcf26b51a32f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/863","title":"Send current user information to Intercom","createdAt":"2022-04-11T22:23:09Z"}
{"state":"Merged","mergedAt":"2023-10-10T23:56:56Z","number":8630,"mergeCommitSha":"567d10e380bb957cc9b3b299c769ce898fd6d537","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8630","title":"Yet another internal test account","createdAt":"2023-10-10T23:08:55Z"}
{"state":"Merged","mergedAt":"2023-10-10T23:17:54Z","number":8631,"mergeCommitSha":"16a5cd814c9fceba22ea87b494b2a41d8625856a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8631","title":"Work mem configuration for database","createdAt":"2023-10-10T23:09:33Z"}
{"state":"Merged","mergedAt":"2023-10-10T23:19:48Z","number":8632,"body":"- Fix Bitbucket personal repos bug\r\n\r\n- Allow Bitbucket Workspace in DEV environment","mergeCommitSha":"28b23cec6fc50070ae35b0f4375b9135a385bd91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8632","title":"Fix Bitbucket personal repos bug","createdAt":"2023-10-10T23:10:10Z"}
{"state":"Merged","mergedAt":"2023-10-12T18:16:19Z","number":8633,"mergeCommitSha":"3ef7a9e33cc04e8be10aa3e7e2c942cf03c02ad4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8633","title":"Release team modes to PROD","createdAt":"2023-10-10T23:12:22Z"}
{"state":"Merged","mergedAt":"2023-10-11T17:24:15Z","number":8634,"body":"This PR does 2 things:\r\n- Removes logic that sent out the onboarded email campaign as soon as a person is onboarded (there's other logic for this)\r\n- Adds email list ids for standard mode teams","mergeCommitSha":"dc571eaaf5a7059e0ce0548bbc426cb7fca025dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8634","title":"Email campaign for standard mode","createdAt":"2023-10-10T23:38:34Z"}
{"state":"Merged","mergedAt":"2023-10-11T00:05:37Z","number":8635,"mergeCommitSha":"7a5713a253f90bdcef90b86772e047b43ff98fc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8635","title":"Responsive fixes","createdAt":"2023-10-10T23:40:46Z"}
{"state":"Merged","mergedAt":"2023-10-11T00:22:30Z","number":8636,"body":"Every team that has already been enabled (manually by Doug) will be set to `Full` mode.\n\nAll remaining teams will be set to `Standard` mode.","mergeCommitSha":"bf081a7a2b2fe7f0522024bbba8967a866b7b5f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8636","title":"Grandfather all PROD enabled teams as Full mode","createdAt":"2023-10-11T00:00:16Z"}
{"state":"Merged","mergedAt":"2023-10-11T00:50:17Z","number":8637,"mergeCommitSha":"b8a6edeed70173f5c94c5400e50c004c9678e8ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8637","title":"Up button radius","createdAt":"2023-10-11T00:17:05Z"}
{"state":"Merged","mergedAt":"2023-10-11T01:48:31Z","number":8638,"mergeCommitSha":"9e29d90c65cf287250c5838bfa95eaf75f076f34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8638","title":"Add index","createdAt":"2023-10-11T01:48:25Z"}
{"state":"Merged","mergedAt":"2023-10-11T16:28:17Z","number":8639,"body":"- Add index\r\n- More indices\r\n","mergeCommitSha":"fafde9ea32f6e1d012538ad261602a85d8ef9cb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8639","title":"AddIndex","createdAt":"2023-10-11T02:18:26Z"}
{"state":"Merged","mergedAt":"2022-04-12T19:25:19Z","number":864,"body":"We'll need this ID to keep [messages synced with the GH comment](https://www.notion.so/nextchaptersoftware/6-weeks-out-Known-Engineering-Work-2b92784732a94816bfc478a60e586abf#4cec76618904493293705dfc4c04401c)\r\n\r\nCreate https://docs.github.com/en/rest/reference/pulls#create-a-review-comment-for-a-pull-request\r\nUpdate https://docs.github.com/en/rest/reference/pulls#update-a-review-comment-for-a-pull-request\r\nDelete https://docs.github.com/en/rest/reference/pulls#delete-a-review-comment-for-a-pull-request","mergeCommitSha":"ee72aa8a3d8ca827a2b7779781e86e98bb4f8670","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/864","title":"Save PR comment ID on MessageModel","createdAt":"2022-04-11T23:38:12Z"}
{"state":"Merged","mergedAt":"2023-10-11T02:19:35Z","number":8640,"mergeCommitSha":"71e94e3243638e60fa848db6026b6088cb216b66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8640","title":"More indices","createdAt":"2023-10-11T02:19:24Z"}
{"state":"Merged","mergedAt":"2023-10-11T17:08:13Z","number":8641,"mergeCommitSha":"a7e5767e6c26f141599958a2b22b76f36ed62766","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8641","title":"Add ability prioritize sample questions for onboarding","createdAt":"2023-10-11T03:50:56Z"}
{"state":"Merged","mergedAt":"2023-10-11T04:06:20Z","number":8642,"mergeCommitSha":"12605315c35e149d742ea5500cb39445516c7169","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8642","title":"Pick full team as first team if one exists","createdAt":"2023-10-11T04:05:28Z"}
{"state":"Merged","mergedAt":"2023-10-11T16:56:00Z","number":8643,"body":"When you ask a question in VSCode, we will display the new question in a split view beside the current file, not in a new editor in the same group.","mergeCommitSha":"34940824a303ee1a02c9bb832bc46fbc7218b684","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8643","title":"'Ask a Question' in VSCode displays beside file","createdAt":"2023-10-11T16:22:11Z"}
{"state":"Merged","mergedAt":"2023-10-11T17:09:32Z","number":8644,"mergeCommitSha":"893d4bcfbffe8cee0f345386e009da39cc29fb15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8644","title":"Install plugins as soon as full team is enabled","createdAt":"2023-10-11T17:05:01Z"}
{"state":"Merged","mergedAt":"2023-10-11T17:53:38Z","number":8645,"body":"We had some legacy stuff here that needed to be removed as well.\r\n\r\nThe fix is straightfoward:\r\n- When the app boots, it checks whether it should show the upgrade UX, and if so, will set a default that is picked up throughout the app. \r\n- We're just going to check whether we're in an onboarding state here (showPopover is a proxy for this) and not activate the upgrade UX if that's the case.","mergeCommitSha":"ee17da3ee86657975c00a592cc6f24bca272c4c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8645","title":"Do not show upgrade UX while in onboarding","createdAt":"2023-10-11T17:47:30Z"}
{"state":"Merged","mergedAt":"2023-10-11T18:02:03Z","number":8646,"mergeCommitSha":"b99cbe7871b97f8eb3ce223c77643a459dfbb910","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8646","title":"Allow team disablement in all envs but prod","createdAt":"2023-10-11T17:59:20Z"}
{"state":"Merged","mergedAt":"2023-10-11T18:33:40Z","number":8647,"body":"For small viewports.","mergeCommitSha":"d9b02201677bc7d001dec1cd792b92f1427f18a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8647","title":"Fix icon positioning and missing animation","createdAt":"2023-10-11T18:17:59Z"}
{"state":"Merged","mergedAt":"2023-10-11T19:34:04Z","number":8648,"body":"A _disabled_ repo is one that GitHub has blocked access to because it has violated term of service.\r\n\r\nFixes https://chapter2global.slack.com/archives/C02HEVCCJA3/p1696488907212659","mergeCommitSha":"582da0ecdbecfd4e3a0ac0a652dc28e523311c6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8648","title":"Ignore all archived, forked, and disabled repos","createdAt":"2023-10-11T18:26:46Z"}
{"state":"Merged","mergedAt":"2023-10-11T18:47:58Z","number":8649,"mergeCommitSha":"1aa780360ff64088f301117b13efff71068abc5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8649","title":"Actually disable team","createdAt":"2023-10-11T18:27:19Z"}
{"state":"Merged","mergedAt":"2022-04-12T15:56:02Z","number":865,"body":"Web extension MV3 has moved background scripts to service workers. Chromium has a built in 5 minute lifespan for a service worker causing our data, polling, streams, etc... to go away after 5 minutes.\r\n\r\nControversial issue that has not been addressed in a couple years: https://bugs.chromium.org/p/chromium/issues/detail?id=1152255#c25\r\n\r\nUsing the following workaround to keep service worker alive. Requires some additional permissions in manifest.json.\r\n\r\nhttps://stackoverflow.com/questions/66618136/persistent-service-worker-in-chrome-extension/66618269#66618269","mergeCommitSha":"0143e71f31e7515ab6b30d6157f5c60815288118","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/865","title":"Persist web extension web worker","createdAt":"2022-04-11T23:52:37Z"}
{"state":"Merged","mergedAt":"2023-10-11T19:57:26Z","number":8650,"body":"Also increase the frequency temporarily.","mergeCommitSha":"721d8b12a979a0d7f8107586c218d7d2970ad715","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8650","title":"Fix background SCM installation job","createdAt":"2023-10-11T18:34:11Z"}
{"state":"Merged","mergedAt":"2023-10-11T19:06:04Z","number":8651,"mergeCommitSha":"af5768df2bec6273f571c2a3f54abba4e7a0e8ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8651","title":"Update Notion models and event handling","createdAt":"2023-10-11T18:39:23Z"}
{"state":"Merged","mergedAt":"2023-10-11T19:08:14Z","number":8652,"body":"We should not be doing unnecessary updates against db during team membership maintenacne.\r\n","mergeCommitSha":"4968fc84b045b41754fd6c2d9f4abcc88c5cdb02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8652","title":"Fix dead tuple generation due to unnessary updates for team members","createdAt":"2023-10-11T18:44:30Z"}
{"state":"Merged","mergedAt":"2023-10-18T02:38:13Z","number":8653,"body":"Avoid triggering sandbox errors in IntelliJ on Sonoma.\r\n\r\nPreviously we had been installing the node runtime into the hub's sandbox container:\r\n```\r\n~/Library/Containers/com.nextchaptersoftware.UnblockedHub/Data/Library/Application Support/Node/bin/node\r\n```\r\n\r\nwith this PR we now install into the old non-sandbox folder:\r\n```\r\n~/Library/Application Support/com.nextchaptersoftware.UnblockedHub/Node/bin/node\r\n```\r\n\r\nThe one trick is that we had to change the node install version file, to force a reinstall -- otherwise the hub will never trigger the installer and node will never be installed into the new location.\r\n\r\nThe IntelliJ plugin will try the non-sandbox folder first, and fail over to the sandbox folder (we need to do this for upgrade scenarios).","mergeCommitSha":"05d52fd817d7316d983e00ca12675b680cd6ebca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8653","title":"IntelliJ plugin runs on Sonoma without triggering sandbox errors","createdAt":"2023-10-11T18:44:39Z"}
{"state":"Merged","mergedAt":"2023-10-11T20:06:21Z","number":8654,"mergeCommitSha":"87a38e861ecdec3949f4a75b983306c67cb73c87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8654","title":"Handle missing workspace icon for Notion installation","createdAt":"2023-10-11T19:53:39Z"}
{"state":"Merged","mergedAt":"2023-10-11T20:17:21Z","number":8655,"body":"Notion workspaces aren't guaranteed to have avatars.\r\n\r\n<img width=\"808\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/09ebd5c9-776c-4939-a195-b9ac61203da0\">\r\n","mergeCommitSha":"30df3e52ab8942bdd13c331f3232e2394dc9f905","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8655","title":"Add optional icon support for Notion","createdAt":"2023-10-11T19:58:41Z"}
{"state":"Merged","mergedAt":"2023-10-11T20:47:06Z","number":8656,"body":"For now just this team:\n\n- Granite\n  https://admin.prod.getunblocked.com/teams/2adf67a2-1c94-4ed8-8f1a-3367d75dc744","mergeCommitSha":"40c7c76ca323b914612941937781c472a8cdb9e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8656","title":"Throttle code ingestion of problematic teams until we have repo limits","createdAt":"2023-10-11T20:02:56Z"}
{"state":"Merged","mergedAt":"2023-10-11T20:21:15Z","number":8657,"mergeCommitSha":"9d5658f5df4dba6142c45795d29fe7cc46f4d21d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8657","title":"Fix broken team store, to fix onboarding","createdAt":"2023-10-11T20:06:44Z"}
{"state":"Merged","mergedAt":"2023-10-11T20:25:33Z","number":8658,"mergeCommitSha":"5c2f04b951749bd577ce483747e36fb1dc9528b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8658","title":"Handle missing avatar icon for Notion user","createdAt":"2023-10-11T20:13:11Z"}
{"state":"Merged","mergedAt":"2023-10-12T00:05:58Z","number":8659,"mergeCommitSha":"9768503ea5ac1dfacca0bbfad23534c05ce7ba02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8659","title":"Move to java 21","createdAt":"2023-10-11T20:15:55Z"}
{"state":"Merged","mergedAt":"2022-04-12T06:11:43Z","number":866,"body":"We no longer need to hardcode the repo in our config files.","mergeCommitSha":"7d7e6291a482d2f035ddc19c7eabf3163f157e62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/866","title":"Run PR ingestion on all repos","createdAt":"2022-04-12T00:39:20Z"}
{"state":"Merged","mergedAt":"2023-10-11T20:38:26Z","number":8660,"body":"before:\r\n<img width=\"312\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/b9a22e3e-5cba-42ac-a6eb-efbc0cba57a4\">\r\n\r\nafter:\r\n<img width=\"315\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/0c7d8378-1181-4169-9199-0e1f4705459d\">\r\n","mergeCommitSha":"525b3abf6100608a782f22c7ea9bb4384a7bcea4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8660","title":"Align add icon with rest of dropdown","createdAt":"2023-10-11T20:28:01Z"}
{"state":"Merged","mergedAt":"2023-10-11T21:28:25Z","number":8661,"mergeCommitSha":"4d61eaccff6480ee5c716771c477d017d3ad6c52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8661","title":"Use auth state publisher for install signal","createdAt":"2023-10-11T21:17:08Z"}
{"state":"Merged","mergedAt":"2023-10-11T22:05:01Z","number":8662,"mergeCommitSha":"c5b7a9d0bd1a14c4d38ce32c1b16797375ef5449","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8662","title":"Skip suspended GitHub installations","createdAt":"2023-10-11T21:46:23Z"}
{"state":"Open","mergedAt":null,"number":8663,"body":"https://chapter2global.slack.com/archives/C03NVHV37EF/p1700175773644569?thread_ts=**********.265879&cid=C03NVHV37EF","mergeCommitSha":"fd3eac2f7085d356738e061174d896aca7c9de4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8663","title":"Automatically suspend GitHub installations from accounts that have been created in the last 30 days","createdAt":"2023-10-11T22:43:53Z"}
{"state":"Merged","mergedAt":"2023-10-12T00:26:32Z","number":8664,"mergeCommitSha":"a65b934f8c5281831d8a953b322a4df7cf475a9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8664","title":"Allow GetUnblocked test team in DEV","createdAt":"2023-10-11T23:44:46Z"}
{"state":"Merged","mergedAt":"2023-10-12T19:14:42Z","number":8665,"mergeCommitSha":"c597e2e68c820f5d445927f7aa0f124b3cf50b22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8665","title":"Address schema update deadlocks","createdAt":"2023-10-11T23:53:02Z"}
{"state":"Merged","mergedAt":"2023-10-12T00:42:34Z","number":8666,"body":"- Slack bot only responds when it has references (aka adds value)\r\n\r\n- Rename for clarity","mergeCommitSha":"7cfc0aeb8d8188148d55e5bea60748ce027cdcfe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8666","title":"Slack bot only responds when it has references (aka adds value)","createdAt":"2023-10-12T00:12:51Z"}
{"state":"Merged","mergedAt":"2023-10-12T00:56:18Z","number":8667,"body":"We were including all documents included in the _question_ prompt, which is unrelated.\n\nWe should only include the documents referenced in the _answer_.","mergeCommitSha":"67b37986fcdb3bc121cc51ce80c00803ea385406","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8667","title":"Evaluate Slack bot answers based on the documents referenced in the answer","createdAt":"2023-10-12T00:18:24Z"}
{"state":"Merged","mergedAt":"2023-10-12T03:32:10Z","number":8668,"body":"Addresses a bug introduced in #8662.","mergeCommitSha":"57455ceff4ea0630f4396b2e1c918f315d3156d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8668","title":"Properly handle scenario where install transitions from installed to suspended","createdAt":"2023-10-12T00:42:10Z"}
{"state":"Merged","mergedAt":"2023-10-12T03:12:08Z","number":8669,"mergeCommitSha":"1c4368b92c99b3cae382294786258af7bd1c0e44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8669","title":"De-dupe the team avatar in the hub if it's a personal org","createdAt":"2023-10-12T03:01:58Z"}
{"state":"Merged","mergedAt":"2022-04-12T18:17:38Z","number":867,"mergeCommitSha":"3a3a8e1068e18447ec267c048813b57ae4702f43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/867","title":"Add ability to debug storybook","createdAt":"2022-04-12T18:17:21Z"}
{"state":"Merged","mergedAt":"2023-10-12T04:01:59Z","number":8670,"body":"This rule caused a ton of false alarms. Added an exception for `containerd` process.","mergeCommitSha":"5a9bab617f56d1ccd545391a5354306d76cce562","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8670","title":"Adding exception for containerd","createdAt":"2023-10-12T04:01:49Z"}
{"state":"Merged","mergedAt":"2023-10-12T05:06:28Z","number":8671,"body":"Code ingestion is currently stalled in PROD because the optimal repo to ingest happens to be from a soft deleted team. Since code ingestion refuses to process anything from deleted teams it exits early. Then the loop continues forever.","mergeCommitSha":"efdc6a100382bbba0cfe54472f58397d929dda09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8671","title":"Code ingestion must skip deleted and uninstalled teams to avoid deadlock","createdAt":"2023-10-12T04:42:05Z"}
{"state":"Merged","mergedAt":"2023-10-12T15:47:19Z","number":8672,"body":"In DEV, GitHub maintenance has been broken for quite some time because the GitHub Enterprise instance has been offline. This resulted in a connection failure:\n\n```\ni.k.c.n.s.ConnectTimeoutException: Connect timeout has expired [url=https://ghe.secops.getunblocked.com/api/v3/app/installations?per_page=100, connect_timeout=unknown ms]\n```\n\nHowever, instead of just preventing GHE App installations from running, it inadvertently prevented GitHub Cloud App installations from running too.","mergeCommitSha":"7eee16ac18700eb5499294c52fcdc0fb471bd460","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8672","title":"Fix GitHub installation background maintenance crash","createdAt":"2023-10-12T15:30:08Z"}
{"state":"Merged","mergedAt":"2023-10-12T17:35:26Z","number":8673,"body":"Also disable embedding in prod","mergeCommitSha":"99c4d3e9fa0e1cb3de6ec6339c02da990bd7de4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8673","title":"Convert Notion blocks to markdown","createdAt":"2023-10-12T16:06:48Z"}
{"state":"Merged","mergedAt":"2023-10-12T17:45:18Z","number":8674,"body":"This is so that we know which oauth tokens to clear when an installation is deleted","mergeCommitSha":"423c6d741995e0a1210190629351694b7d4f253b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8674","title":"Set Identity.externalTeamId to installationExternalId for notion","createdAt":"2023-10-12T17:25:47Z"}
{"state":"Merged","mergedAt":"2023-10-13T01:38:39Z","number":8675,"body":"Added `reset-values` flag to helm which should force and upgrade with values specified in code. I also set the max scale for scm service auto-scaler to 3 as a test to see if this works. ","mergeCommitSha":"b8937015d9715d98b7ee4df703c317ea111e5090","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8675","title":"Adding reset-values flag to helm command","createdAt":"2023-10-12T17:34:26Z"}
{"state":"Merged","mergedAt":"2023-10-12T18:37:53Z","number":8676,"body":"Also temp fix for icon rendering on download installer page.","mergeCommitSha":"7775c14a653ace9f8453404dea92e02342f33f08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8676","title":"Fix notion rendering for primary team member","createdAt":"2023-10-12T18:25:51Z"}
{"state":"Merged","mergedAt":"2023-10-12T18:54:24Z","number":8677,"body":"GitHub installation maintenance works incrementally installing account since the last time that it ran. However, since most personal organizations were created long before the last maintenance window, this logic was temporarily invalid.\n\nSolution is just to force the maintenance to run once by resetting it's cache, then this will correct itself going forward.","mergeCommitSha":"eed9f434750ae17d9b3a3c9eaed90870a75107a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8677","title":"Force restart GitHub installation maintenance","createdAt":"2023-10-12T18:53:47Z"}
{"state":"Merged","mergedAt":"2023-10-12T19:44:26Z","number":8678,"mergeCommitSha":"7be0a776fa4fa01ce2e4b33b41f55c6f593fcea5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8678","title":"[SKIP TESTS] Enable serial schema updates","createdAt":"2023-10-12T19:44:15Z"}
{"state":"Merged","mergedAt":"2023-10-12T20:24:38Z","number":8679,"mergeCommitSha":"447dd3fe8efa93f04dfb36aa1adf74d27abf498e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8679","title":"Move everything to serial schema updates","createdAt":"2023-10-12T20:08:03Z"}
{"state":"Merged","mergedAt":"2022-04-12T21:50:15Z","number":868,"body":"Replace almost all `console` usages with logger.\r\n\r\nI think the logger usage should be something like:\r\n\r\n`log.debug('message')` -- simply log a single string\r\n`log.error('message', e)` -- log an exception `e`, we should generally include a message too indicating more about the message.  The logs will include the exception details and call stack, if whoever threw the exception used `new Error()`.\r\n`log.warn('message', { collection, of, things, to log})` -- log with a collection of associated objects, useful if you have a simple object that you want to log everything for\r\n`log.error('message', e, { collection, of, things, to, log})` -- log an exception `e` with additional data -- the additional collection will be merged into the exception data for logging purposes.\r\nFuture PR will clean up the last couple instances and (hopefully) add a linter rule.","mergeCommitSha":"a99b3476c6b2b6f9430665ce158b94ba2c3e5f1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/868","title":"Use logger instead of console","createdAt":"2022-04-12T20:30:42Z"}
{"state":"Merged","mergedAt":"2023-10-12T20:49:29Z","number":8680,"mergeCommitSha":"f193b439d6e38a93b245bbfe089eba94e3643c3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8680","title":"Add blog redirect for backwards compatibility","createdAt":"2023-10-12T20:36:50Z"}
{"state":"Merged","mergedAt":"2023-10-13T22:03:28Z","number":8681,"mergeCommitSha":"ac12896c5639b64a755c8bcfa9ede541eb1b22b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8681","title":"SCM listInstallations API is personal account aware","createdAt":"2023-10-12T20:52:59Z"}
{"state":"Merged","mergedAt":"2023-10-12T22:58:25Z","number":8682,"mergeCommitSha":"a174b85740386a27b2b50fc085c9e4247a2da537","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8682","title":"Remove team eligibility mode flag","createdAt":"2023-10-12T21:51:28Z"}
{"state":"Merged","mergedAt":"2023-10-12T22:07:12Z","number":8683,"mergeCommitSha":"01b627fbc079b9ef7563a00a502e89c974df7982","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8683","title":"Update blog routes","createdAt":"2023-10-12T21:54:22Z"}
{"state":"Merged","mergedAt":"2023-10-12T23:11:00Z","number":8684,"mergeCommitSha":"ab197553d90450c1de7330ba2fd4e301ed6f2a75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8684","title":"Detect Bitbucket forks and filter them out","createdAt":"2023-10-12T22:29:58Z"}
{"state":"Merged","mergedAt":"2023-10-15T21:15:45Z","number":8685,"body":"<img width=\"900\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/df57ffe3-f614-4dc3-9ab4-3847798dd2de\">\r\n","mergeCommitSha":"41eef5965183630a002d5c3fb32195da72fa5de1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8685","title":"Re-organize the teams page to match our process","createdAt":"2023-10-12T22:31:14Z"}
{"state":"Merged","mergedAt":"2023-10-12T23:02:38Z","number":8686,"body":"We're putting less upsert load on Pinecone upsert API so we can crank up both the code ingestion worker concurrency, and reduce the refresh period to 5 days.","mergeCommitSha":"590f5b3e686ffe72484642b1dc05ce37efa119ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8686","title":"With incremental enabled make code fresher","createdAt":"2023-10-12T23:01:58Z"}
{"state":"Merged","mergedAt":"2023-10-17T23:39:39Z","number":8687,"body":"@dennispi @pwerry can you review/finalize the wording? Peter, Dennis was wondering what you meant by \"authorized\".\r\n\r\nLanding page:\r\n![CleanShot 2023-10-12 at 16 21 23@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/9a44d9db-cb74-4394-8579-af49b309ab3b)\r\n\r\nPricing page:\r\n![CleanShot 2023-10-12 at 16 22 03@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/2345ded1-d25c-4750-abcf-4631729cce2c)\r\n","mergeCommitSha":"a0a155e1b5a81f7e99b7f24e040db15bfedb9222","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8687","title":"Add section on how we treat your data to landing and pricing page","createdAt":"2023-10-12T23:23:07Z"}
{"state":"Merged","mergedAt":"2023-10-12T23:45:49Z","number":8688,"body":"Currently, empty repos will never be processed by a code ingestion worker, by design. This means that the latest processed commit SHA will always be undefined for empty repos.\n\nThis value is used by the repo completion handler to determine when all repos have been ingested for a team. As a result, the team will never be auto-enabled.\n\nThis change sets a sentinel value as the processed commit SHA so that auto-enabling logic will treat the empty repo as enabled.","mergeCommitSha":"325152baa98e9b743cf96ba9ba0da26d1e6cd93c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8688","title":"Prevent code ingest from stalling auto-enabling teams","createdAt":"2023-10-12T23:33:15Z"}
{"state":"Merged","mergedAt":"2023-10-12T23:49:34Z","number":8689,"mergeCommitSha":"e61ac9a4e89abb55f203ffc868cb8cc49de540ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8689","title":"Bad dev config","createdAt":"2023-10-12T23:49:11Z"}
{"state":"Merged","mergedAt":"2022-04-12T21:56:46Z","number":869,"body":"`/logs` endpoint is now authenticated.","mergeCommitSha":"824f6263508c19cac6b1f28b6e75bd1e6e687614","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/869","title":"Add identity to client logs","createdAt":"2022-04-12T21:45:47Z"}
{"state":"Merged","mergedAt":"2023-10-13T00:28:57Z","number":8690,"body":"For large repos, massive load on database w.r.t. topic mapping.\r\nAlso bugs related to doing in queries wiith 70k ids.\r\nMove to small granular approach.\r\n","mergeCommitSha":"cf4fedfebae64a8969aad20e94e64d7de74e5797","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8690","title":"Move to queue based approach for bulk topic mapping","createdAt":"2023-10-12T23:51:46Z"}
{"state":"Merged","mergedAt":"2023-10-13T00:30:23Z","number":8691,"mergeCommitSha":"bd65c768fb74b2da79c57830f97ce90007b3e852","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8691","title":"Debug repo code ingestion completion","createdAt":"2023-10-13T00:28:45Z"}
{"state":"Merged","mergedAt":"2023-10-13T02:00:45Z","number":8692,"mergeCommitSha":"772f5a2733b469d035fe4fb114d9579772076a70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8692","title":"Bulk insert optimizations","createdAt":"2023-10-13T01:35:39Z"}
{"state":"Merged","mergedAt":"2023-10-16T16:23:34Z","number":8693,"body":"First pass at creating a content partitioner. The code ain't pretty but it's tested!","mergeCommitSha":"b7217078409bbf1b7969f030d5709a4cda12e8fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8693","title":"Create ContentPartitioningService","createdAt":"2023-10-13T05:27:42Z"}
{"state":"Merged","mergedAt":"2023-10-13T16:33:38Z","number":8694,"mergeCommitSha":"c700649547f65663ecc77976a6c7ac01de96c4d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8694","title":"Temporary logging to debug customer scenario","createdAt":"2023-10-13T16:16:32Z"}
{"state":"Closed","mergedAt":null,"number":8695,"body":"TODO Needs a couple of tests\r\n\r\nWhen a user Oaths, we create an identity and team member for that provider. However the team member is immediately associated with the primary member, so there is about a gap until the primary team member is associated with the this new member. \r\n\r\nI'm not sure if this is the right move, but wanted to get some feedback before closing or adding tests and merging.","mergeCommitSha":"896668ea9e1faa33376faf3eaa5b0aad726c7341","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8695","title":"[RFC] Align identity during the Notion Oauth dance","createdAt":"2023-10-13T16:25:30Z"}
{"state":"Merged","mergedAt":"2023-10-16T17:57:14Z","number":8696,"body":"This tooltip showed up all the time and was annoying, so we're removing it.","mergeCommitSha":"cd6b5eee921761f63927c1300ad964adef75b8de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8696","title":"Remove VSCode topic/expert tooltip","createdAt":"2023-10-13T16:56:12Z"}
{"state":"Merged","mergedAt":"2023-10-13T17:10:46Z","number":8697,"body":"[SKIP TESTS]","mergeCommitSha":"3c359fb2fad182230495c66775a3403c167abcb4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8697","title":"[SKIP TESTS] Enable rewriteBatcedInserts","createdAt":"2023-10-13T16:59:20Z"}
{"state":"Merged","mergedAt":"2023-10-13T20:09:08Z","number":8698,"mergeCommitSha":"57cee25799ab81b48431ede9eb9e16636da8d7de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8698","title":"Fix simple onboarding flow to get users unstuck","createdAt":"2023-10-13T17:11:49Z"}
{"state":"Merged","mergedAt":"2023-10-13T17:53:21Z","number":8699,"body":"[SKIP TESTS] ","mergeCommitSha":"96aa4efd417613e3de0fbc1c5909e7bc5276c164","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8699","title":"[SKIP TESTS] Remove db connectino overrides","createdAt":"2023-10-13T17:53:15Z"}
{"state":"Merged","mergedAt":"2022-01-20T00:04:00Z","number":87,"body":"- Added new root account Id config param to secOps config class\r\n- Created IAM role for cross account access to ECR\r\n- Updated sec-ops account json to include root account ID\r\nAll changes have been deployed to dev and I have validated `deploybot` user's access to ECR.\r\n\r\nDeploybot arn: `arn:aws:iam::************:role/EcrDeployerRole`","mergeCommitSha":"a3e69432fcb1753748b73320772c7d7d64bccbac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/87","title":"added a role to grant deploybot ECR power user access","createdAt":"2022-01-20T00:00:13Z"}
{"state":"Merged","mergedAt":"2022-04-13T02:30:48Z","number":870,"body":"![CleanShot 2022-04-12 at 15 47 15](https://user-images.githubusercontent.com/3806658/*********-ca880e5e-9d81-46ba-a8fe-b56832eee64b.gif)\r\n","mergeCommitSha":"25d47927f3aad2826d04642d3503f40e25332753","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/870","title":"Add basic Image Drag & Drop","createdAt":"2022-04-12T22:38:37Z"}
{"state":"Merged","mergedAt":"2023-10-13T22:57:39Z","number":8700,"body":"These pages will be ingested on their own so there's no point in retrieving them as a child block.","mergeCommitSha":"2958819303fef7603991abd6f639955b4a23c345","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8700","title":"Dont get children for child_page or child_database block","createdAt":"2023-10-13T18:24:05Z"}
{"state":"Merged","mergedAt":"2023-10-13T21:50:47Z","number":8701,"mergeCommitSha":"67bb6032d579118c5d3526201e5d050491569291","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8701","title":"Smooth over rough edges for existing stuck users","createdAt":"2023-10-13T21:25:29Z"}
{"state":"Merged","mergedAt":"2023-10-13T22:47:30Z","number":8702,"body":"- https://app.intercom.com/a/inbox/crhakcyc/inbox/conversation/900\n- https://docs.github.com/en/authentication/troubleshooting-ssh/using-ssh-over-the-https-port","mergeCommitSha":"a7adaab73992fc8685abac0d78160f57e2d4a94a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8702","title":"Handle public SCM subdomain aliases in findInstallationsAndRepos","createdAt":"2023-10-13T22:24:45Z"}
{"state":"Merged","mergedAt":"2023-10-13T23:04:26Z","number":8703,"mergeCommitSha":"037a5b2044b65b79ad308a2ca4b8060de3cd7356","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8703","title":"Pop open hub during onboarding if app restarts","createdAt":"2023-10-13T23:03:46Z"}
{"state":"Merged","mergedAt":"2023-10-15T17:19:16Z","number":8704,"body":"@pwerry @matthewjamesadam can one of you help by stacking on this to remove the `PendingTeamStore` in the mac app?","mergeCommitSha":"707e05e0817d51d675ea55569c083342096a79c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8704","title":"Introduce getTeamStatus and deprecate getPendingTeams","createdAt":"2023-10-14T00:14:39Z"}
{"state":"Merged","mergedAt":"2023-10-14T05:22:40Z","number":8705,"mergeCommitSha":"fcce20a379028a1510eb5f4a4740d1f08b75b0f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8705","title":"Skip creating PullRequestModels for standard teams","createdAt":"2023-10-14T00:49:47Z"}
{"state":"Merged","mergedAt":"2023-10-14T05:29:52Z","number":8706,"mergeCommitSha":"06f3ad03487e32577574d91efbcf0c5bddd17db0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8706","title":"Stop deactivating the bot during SCM member sync","createdAt":"2023-10-14T05:03:52Z"}
{"state":"Merged","mergedAt":"2023-10-16T05:14:07Z","number":8707,"mergeCommitSha":"9442667a2024b9f5dc35a22ce4a14ba7c9721027","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8707","title":"Clean up pull requests for standard mode teams","createdAt":"2023-10-14T17:10:31Z"}
{"state":"Merged","mergedAt":"2023-10-15T09:54:15Z","number":8708,"body":"Add scheduling support fo ractivemq.","mergeCommitSha":"9422b79b8a951b32f68fdb18c8a2cbb654f4d04d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8708","title":"ActiveMQScheduling","createdAt":"2023-10-15T09:38:11Z"}
{"state":"Merged","mergedAt":"2023-10-15T09:57:21Z","number":8709,"mergeCommitSha":"a8576a1683adb6f070253d2cea529fb40a9db3cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8709","title":"Recreate containers","createdAt":"2023-10-15T09:57:10Z"}
{"state":"Merged","mergedAt":"2022-04-13T00:01:02Z","number":871,"body":"Whenever the resolved workspace repos change, re-fetch and render sourcemarks.\r\n\r\nThis still doesn't work as expected.  On startup, even with a resolved repo, the sourcemark engine returns an empty set of sourcemarks (attn @richiebres ) but with this PR it should at least be easier to test.","mergeCommitSha":"7bff65342e89c67f203c14b2febc39c146ec6394","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/871","title":"Re-resolve SourceMarks when resolved repos changes","createdAt":"2022-04-12T22:55:03Z"}
{"state":"Merged","mergedAt":"2023-10-15T19:00:06Z","number":8710,"body":"Fixes a bug where impersonating a person would use their Linear identity, which makes no sense.","mergeCommitSha":"90e61e3d8832c3bcd04de8b21db3a91389d60b4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8710","title":"Only impersonate SCM identities","createdAt":"2023-10-15T17:59:49Z"}
{"state":"Merged","mergedAt":"2023-10-15T20:09:11Z","number":8711,"body":"With Rashin's changes (https://github.com/NextChapterSoftware/unblocked/pull/8708) we can re-enqueue with a delay if Notion gives us a 429.","mergeCommitSha":"813921a24f562ee44444b9208e56c6a7e9fed98d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8711","title":"Create and handle notion page ingestion events","createdAt":"2023-10-15T19:23:31Z"}
{"state":"Merged","mergedAt":"2023-10-16T05:06:05Z","number":8712,"mergeCommitSha":"cd2138fb444da48464df77e54bbf98a7293679d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8712","title":"Chains. Also, removed flow completions (because they're broken).","createdAt":"2023-10-15T20:51:10Z"}
{"state":"Merged","mergedAt":"2023-10-15T21:14:44Z","number":8713,"mergeCommitSha":"59744e60630d19cada48c638d0c69fc7c8b35e4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8713","title":"Release lock after ingesting page","createdAt":"2023-10-15T21:03:39Z"}
{"state":"Merged","mergedAt":"2023-10-15T22:23:24Z","number":8714,"mergeCommitSha":"22113a5baf7e6f781948ebff3947973af5ea785c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8714","title":"Naive implementation of missingValidRepos case of getTeamStatus","createdAt":"2023-10-15T21:36:01Z"}
{"state":"Merged","mergedAt":"2023-10-15T22:19:02Z","number":8715,"body":"- remove mode (not that useful)\n- fix deleted teams showing up in wrong queries\n- add last active at","mergeCommitSha":"bc620d7871f0d6e5fb1a9b0bc863d117e5682601","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8715","title":"Team page improvements and fixes","createdAt":"2023-10-15T22:16:21Z"}
{"state":"Merged","mergedAt":"2023-10-16T00:37:25Z","number":8716,"mergeCommitSha":"d310326a46721c73f4d16fd5de812139580a55ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8716","title":"Order teams in teams page by last active","createdAt":"2023-10-16T00:34:59Z"}
{"state":"Merged","mergedAt":"2023-10-16T03:26:44Z","number":8717,"mergeCommitSha":"9e6890882135da87cdb7220dd492ab0f71e58c8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8717","title":"Fix reorder","createdAt":"2023-10-16T03:26:13Z"}
{"state":"Merged","mergedAt":"2023-10-16T04:04:45Z","number":8718,"mergeCommitSha":"7559df3637935e9a3c52c0ec6561806ccaee2489","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8718","title":"Add DynamoDB KT utils for get/put item","createdAt":"2023-10-16T03:58:08Z"}
{"state":"Closed","mergedAt":null,"number":8719,"mergeCommitSha":"66265f44d6dc2e17f0765e3ecab3f9f708b3e486","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8719","title":"Add enqueuedAt to NotionPageIngestionEvent","createdAt":"2023-10-16T05:20:46Z"}
{"state":"Merged","mergedAt":"2022-04-12T23:50:00Z","number":872,"body":"* Edits and Deletes should not need to require the entire model to trigger overlay logic, tweak the overlay logic to support partials to update \r\n* Fix some styling issues ","mergeCommitSha":"f40a3fb0761c57482747ef0b0a746a30fd5daa08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/872","title":"Update overlay stream logic to support partials","createdAt":"2022-04-12T23:01:22Z"}
{"state":"Merged","mergedAt":"2023-10-16T15:40:54Z","number":8720,"mergeCommitSha":"39fb3bb33d59085c0c51866bdb5f1c0ab09c088f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8720","title":"Temp fix for AtlassianOauthTokenProvider to get the correct creds","createdAt":"2023-10-16T05:33:31Z"}
{"state":"Merged","mergedAt":"2023-10-16T17:45:24Z","number":8721,"mergeCommitSha":"9f188b2622fc30e8506569ea080a97bc43d570ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8721","title":"Introduce IncrementalPartitionTracking to track embedded partitions of large source documents","createdAt":"2023-10-16T06:27:20Z"}
{"state":"Merged","mergedAt":"2023-10-16T06:41:54Z","number":8722,"mergeCommitSha":"e2b303770f20fa8aee7d2e904fb3a52fe9d3fca3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8722","title":"Teams that uninstall the SCM provider are inactive","createdAt":"2023-10-16T06:41:28Z"}
{"state":"Merged","mergedAt":"2023-10-16T17:47:06Z","number":8723,"body":"Addresses https://app.logz.io/#/goto/b20fcddd00618497c631c3961e0eae8f?switchToAccountId=411850","mergeCommitSha":"00e0d041ddf610dfdccc850d71a18e9b4e473211","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8723","title":"Ignore Jira issue without author","createdAt":"2023-10-16T16:16:35Z"}
{"state":"Merged","mergedAt":"2023-10-16T18:33:04Z","number":8724,"mergeCommitSha":"c7ba0f51d1b7a9ce433dc5bfee29b26da97f3b54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8724","title":"Rm unused api and related client code","createdAt":"2023-10-16T17:52:14Z"}
{"state":"Merged","mergedAt":"2023-10-16T17:55:39Z","number":8725,"mergeCommitSha":"4d742fbabbed46cc5748bde62555c01ede698310","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8725","title":"Add class for embedding config","createdAt":"2023-10-16T17:54:27Z"}
{"state":"Merged","mergedAt":"2023-10-16T19:03:35Z","number":8726,"mergeCommitSha":"49d3951ada3c38634e0feb12869dae948cef6062","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8726","title":"Add more properties","createdAt":"2023-10-16T17:57:56Z"}
{"state":"Merged","mergedAt":"2023-10-17T17:30:43Z","number":8727,"mergeCommitSha":"28e18d384a9eb15d9b9662045691046458fd3513","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8727","title":"Put max document length into config","createdAt":"2023-10-16T18:32:04Z"}
{"state":"Merged","mergedAt":"2023-10-16T23:08:50Z","number":8728,"mergeCommitSha":"30a1f4e09378d78507cb1f66b1467c3ad5ea87e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8728","title":"Refactor hashing usage to use types not strings","createdAt":"2023-10-16T18:37:24Z"}
{"state":"Merged","mergedAt":"2023-10-16T18:38:58Z","number":8729,"mergeCommitSha":"6752e61a52f16979edf70acc33fe6e721e97d46d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8729","title":"[SKIP TESTS] Fix config","createdAt":"2023-10-16T18:38:43Z"}
{"state":"Merged","mergedAt":"2022-04-13T00:22:33Z","number":873,"body":"This pr adds the ability to provide a cloudfront id as an additional parameters when generated presigned urls.\r\n\r\ni.e. \r\n`https://user-image-assets-dev-us-west-2.s3.us-west-2.amazonaws.com/8732449f-13cc-4b31-89fc-be47e88f2a4a/0eef3487-5eab-40ca-9197-afc706045b2c/fe899e62-e73b-400e-825a-4ce4a3cd80e1?response-content-disposition=attachment%3B%20filename%3D%22asset%22&response-content-type=image%2Fjpg&X-Amz-Cf-Id=crapper&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220413T000219Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Credential=AKIAU5GZKJTIXDHUKZIX%2F20220413%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=a249e0039e868164971b0e78f25d41bb8c4794dd24c7c9b14ee56c73d4ec2e44`","mergeCommitSha":"a937ecea8ce65b8ea83a473029e55adb18158b24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/873","title":"Add ability to add overrides for s3 presigned urls","createdAt":"2022-04-13T00:01:35Z"}
{"state":"Closed","mergedAt":null,"number":8730,"body":"Reduce compute load at scale.","mergeCommitSha":"70ff683ca92faeb6f2cdbe0dc84f5056b33bbfc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8730","title":"Apple indices","createdAt":"2023-10-16T18:42:05Z"}
{"state":"Merged","mergedAt":"2023-10-16T19:58:07Z","number":8731,"mergeCommitSha":"df8eb9f93cfbd6c2def3161a658f4b194d560fe1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8731","title":"Disallow self-impersonation","createdAt":"2023-10-16T19:23:32Z"}
{"state":"Merged","mergedAt":"2023-10-16T20:13:56Z","number":8732,"mergeCommitSha":"2526d54fe38f16556d8dffe9b534bfa70c1e3372","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8732","title":"[SKIP TESTS] Update PeoplePage.MAX_PEOPLE","createdAt":"2023-10-16T20:11:47Z"}
{"state":"Merged","mergedAt":"2023-10-16T20:20:34Z","number":8733,"mergeCommitSha":"374f171a2c40150248849a08c174e7c1342c94ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8733","title":"Fix deployment code","createdAt":"2023-10-16T20:19:01Z"}
{"state":"Merged","mergedAt":"2023-10-16T20:56:58Z","number":8734,"body":"Looking at pagination...","mergeCommitSha":"3499fd255591f739f5d7e5b04d20e0fbe15b7dd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8734","title":"[SKIP TESTS] Update PeoplePage.MAX_PEOPLE","createdAt":"2023-10-16T20:49:45Z"}
{"state":"Merged","mergedAt":"2023-10-16T21:06:56Z","number":8735,"body":"Need to add rope_factor etc.\r\nReduced to 16k for testing purposes, as even at that size, positional interpolation is taking too long.\r\n\r\nhttps://ml.alb.dev.getunblocked.com/api/ml/transformers/codellama-34b-instruct\r\n\r\nBODY:\r\n```\r\n{\r\n    \"inputs\": \"<s>[INST] <<SYS>>\\nYou are an AWS Expert who gives very detailed answer to question.\\n<</SYS>>\\n\\nShould I rather use AWS CDK or Terraform? Please give 15 reasons why. This should be around 4 paragraphs.[/INST]\",\r\n    \"parameters\": {\r\n        \"max_new_tokens\": 4000,\r\n        \"top_p\": 0.8,\r\n        \"temperature\": 0.05\r\n    }\r\n}\r\n```","mergeCommitSha":"c0cfe17a4e3b6e551e7d38da1a8a4d833e1f11b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8735","title":"Rope instance for doug","createdAt":"2023-10-16T20:55:37Z"}
{"state":"Merged","mergedAt":"2023-10-16T21:25:18Z","number":8736,"mergeCommitSha":"72c43b3b6fa5f4c3a2d9033476e9f51738a1ca5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8736","title":"Update poetry","createdAt":"2023-10-16T21:25:08Z"}
{"state":"Closed","mergedAt":null,"number":8739,"mergeCommitSha":"6dd8c13a90cb9e215e44fddd13cfa16a80cb9705","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8739","title":"Log partitions for notion page","createdAt":"2023-10-16T22:23:13Z"}
{"state":"Merged","mergedAt":"2022-04-14T00:16:21Z","number":874,"body":"* Feature parity with web/vscode\r\n* Added logic to support modal stacking and click events (i.e. click outside should only close the top-most stacked modal), add helper to close all modals ","mergeCommitSha":"87cf9d73ffa6bc0addd93df75e22f8c0d2373fde","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/874","title":"[Extension] Add message deleting, thread archiving/restoring","createdAt":"2022-04-13T00:08:58Z"}
{"state":"Merged","mergedAt":"2023-10-16T23:30:57Z","number":8740,"body":"Integrated under a server-side config flag for:\n - confluence\n - notion\n - stack overflow\n - webpages\n\nThe incremental implementation will come next.","mergeCommitSha":"1829bf67de26df8b1b059f909c5cae73003b0ea4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8740","title":"Incremental documentation parititioning stub integration","createdAt":"2023-10-16T22:46:41Z"}
{"state":"Merged","mergedAt":"2023-11-06T19:27:26Z","number":8741,"body":"When the user makes an add-on question to an existing QA thread, include updated context in the message.  The service can use this, or not, as needed.\r\n\r\nThis adds the context onto the `createMessage` request object, but nothing is done with it yet.","mergeCommitSha":"0241034a4ed74d7224c61491feff0543180734e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8741","title":"Send context on add-on questions","createdAt":"2023-10-16T23:05:10Z"}
{"state":"Merged","mergedAt":"2023-10-18T02:37:34Z","number":8742,"body":"If the hub isn't running, and the `/tmp` IPC port file is not written, on Sonoma this resulted in the dreaded Sonoma sandbox popup, because we ended up trying to access the hub's user defaults.\r\n\r\nRemove all user defaults access.  We will only use the `/tmp` port file.","mergeCommitSha":"830c601a16814610631bc8b55a80da61c0f4b1a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8742","title":"IDEs no longer query hub user defaults","createdAt":"2023-10-16T23:59:45Z"}
{"state":"Merged","mergedAt":"2023-10-17T17:05:36Z","number":8743,"mergeCommitSha":"bec9d7940d1732560a50e7cefcf931a8b40a65a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8743","title":"Missing some template fields on copy","createdAt":"2023-10-17T00:17:33Z"}
{"state":"Merged","mergedAt":"2023-10-17T06:55:52Z","number":8744,"mergeCommitSha":"23704e854362770df294c8f5686571c8a5d075c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8744","title":"Incremental partitioning implementation","createdAt":"2023-10-17T00:28:21Z"}
{"state":"Merged","mergedAt":"2023-10-17T06:20:36Z","number":8745,"mergeCommitSha":"df815b03c68c69a81ee8e7767f88ff7acbc87ee1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8745","title":"Replace string type with Base64Compressed to avoid encoding mistakes","createdAt":"2023-10-17T05:03:53Z"}
{"state":"Merged","mergedAt":"2023-10-17T14:26:40Z","number":8746,"body":"These are massive useless repos.","mergeCommitSha":"84598861a85ed66875b57be738200b618eb1bc71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8746","title":"Drop MLCommons results repos","createdAt":"2023-10-17T06:54:02Z"}
{"state":"Merged","mergedAt":"2023-10-17T07:40:57Z","number":8747,"body":"```\r\ns.a.a.s.d.m.DynamoDbException: User: arn:aws:sts::************:assumed-role/eksctl-dev-addon-iamserviceaccount-default-s-Role1-1OAFN2R9LGRU7/aws-sdk-java-************* is not authorized to perform: dynamodb:GetItem on resource: arn:aws:dynamodb:us-west-2:************:table/documentPartitions because no identity-based policy allows the dynamodb:GetItem action (Service: DynamoDb, Status Code: 400, Request ID: MH0OTUQ6NN3CRFV5V3V73R2AC3VV4KQNSO5AEMVJF66Q9ASUAAJG)\r\n\tat s.a.a.c.i.h.CombinedResponseHandler.handleErrorResponse(CombinedResponseHandler.java:125)\r\n\tat s.a.a.c.i.h.CombinedResponseHandler.handleResponse(CombinedResponseHandler.java:82)\r\n\tat s.a.a.c.i.h.CombinedResponseHandler.handle(CombinedResponseHandler.java:60)\r\n\tat s.a.a.c.i.h.CombinedResponseHandler.handle(CombinedResponseHandler.java:41)\r\n\tat s.a.a.c.i.h.p.s.HandleResponseStage.execute(HandleResponseStage.java:40)\r\n\tat s.a.a.c.i.h.p.s.HandleResponseStage.execute(HandleResponseStage.java:30)\r\n\tat s.a.a.c.i.h.p.RequestPipelineBuilder.execute(RequestPipelineBuilder.java:206)\r\n\tat s.a.a.c.i.h.p.s.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:72)\r\n\tat s.a.a.c.i.h.p.s.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:42)\r\n\tat s.a.a.c.i.h.p.s.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:78)\r\n\tat s.a.a.c.i.h.p.s.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:40)\r\n\tat s.a.a.c.i.h.p.s.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:52)\r\n\tat s.a.a.c.i.h.p.s.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:37)\r\n\tat s.a.a.c.i.h.p.s.RetryableStage.execute(RetryableStage.java:81)\r\n\tat s.a.a.c.i.h.p.s.RetryableStage.execute(RetryableStage.java:36)\r\n\tat s.a.a.c.i.h.p.RequestPipelineBuilder.execute(RequestPipelineBuilder.java:206)\r\n\tat s.a.a.c.i.h.StreamManagingStage.execute(StreamManagingStage.java:56)\r\n```","mergeCommitSha":"0bc91e23639fbe58e64cbafaeac5b11a7f2d979b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8747","title":"Allow KT kube services to Get/Put items in DynamoDB","createdAt":"2023-10-17T07:39:18Z"}
{"state":"Merged","mergedAt":"2023-10-17T08:04:06Z","number":8748,"mergeCommitSha":"8997ea0bf90176342c68c6a86e6a63b99fb8e16d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8748","title":"Fix upsert of base64 metadata content to Pinecone","createdAt":"2023-10-17T08:01:39Z"}
{"state":"Merged","mergedAt":"2023-10-17T08:21:24Z","number":8749,"mergeCommitSha":"7305ed4a1205dea923a33e0099732f6b3f14ef15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8749","title":"Notion icon in admin","createdAt":"2023-10-17T08:15:29Z"}
{"state":"Merged","mergedAt":"2022-04-13T04:35:31Z","number":875,"body":"You can turn source marks on and off now in VSCode using commands, rather then editing the code.","mergeCommitSha":"995f6469eece2f89a17462c9d3afaa600ea7bd0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/875","title":"Install VSCode commands to toggle SM engine and clear sourcepoints","createdAt":"2022-04-13T00:26:01Z"}
{"state":"Merged","mergedAt":"2023-10-17T08:42:31Z","number":8750,"mergeCommitSha":"478583c41d2e46c4adea6a2a8a63daed9b3d5a34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8750","title":"Fix test","createdAt":"2023-10-17T08:41:34Z"}
{"state":"Merged","mergedAt":"2023-10-17T16:24:42Z","number":8751,"mergeCommitSha":"429701cda593ad4106e98777eb569250702808a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8751","title":"[SKIP TESTS] Handle edge case for word splitting in ContentPartitioningService","createdAt":"2023-10-17T16:24:00Z"}
{"state":"Merged","mergedAt":"2023-10-17T16:56:37Z","number":8752,"body":"I messed up the permissions in the previous PR #8747","mergeCommitSha":"4bae7f4a8e81acca10035ddde06d1a5066d9def7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8752","title":"Search service has access to dynamo","createdAt":"2023-10-17T16:24:38Z"}
{"state":"Merged","mergedAt":"2023-10-18T19:46:46Z","number":8753,"mergeCommitSha":"89e154fc684fe32357c089265b79fc30468b991d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8753","title":"Enable incremental document partitioning in PROD","createdAt":"2023-10-17T17:02:10Z"}
{"state":"Merged","mergedAt":"2023-10-17T17:13:40Z","number":8754,"mergeCommitSha":"efd0e417fd07ae728447df177dcf022d86875fe9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8754","title":"Cleanup sanitizer","createdAt":"2023-10-17T17:13:24Z"}
{"state":"Merged","mergedAt":"2023-10-17T22:56:11Z","number":8755,"mergeCommitSha":"5339c07662572a43d30a085c1dd0d1f9531032cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8755","title":"Handle router hash navigation","createdAt":"2023-10-17T17:23:03Z"}
{"state":"Merged","mergedAt":"2023-10-18T20:29:17Z","number":8756,"body":"*NOTE: This must wait for https://github.com/NextChapterSoftware/documentation/pull/22 to merge, and then this branch need to update the links to point to the docs.","mergeCommitSha":"e36e967ac3f28d3e45e84612c8d577b028ce2fe3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8756","title":"Show notion","createdAt":"2023-10-17T17:27:42Z"}
{"state":"Merged","mergedAt":"2023-10-18T19:49:37Z","number":8757,"mergeCommitSha":"8e0566447c94f8f79ef503d7e62b175e3ad3886d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8757","title":"Enable notion","createdAt":"2023-10-17T17:45:00Z"}
{"state":"Merged","mergedAt":"2023-10-18T17:02:34Z","number":8758,"body":"Shift+Enter now adds a line to the QA input in the IDEs.\r\n\r\nFixes this issue: https://app.intercom.com/a/inbox/crhakcyc/inbox/shared/all/conversation/898?view=List\r\n\r\n","mergeCommitSha":"6bbf5986698b3d793333d046a32817c91eab117a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8758","title":"Allow multi-line input in explorer insight UI","createdAt":"2023-10-17T20:00:38Z"}
{"state":"Merged","mergedAt":"2023-10-17T20:40:42Z","number":8759,"mergeCommitSha":"9a1b434aac194d8b2fdd7d009005d7157c43f182","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8759","title":"Do not delete cdk repo images","createdAt":"2023-10-17T20:23:18Z"}
{"state":"Merged","mergedAt":"2022-04-13T01:08:47Z","number":876,"mergeCommitSha":"931394b7706cf5bae1420a28498de3e74e33b3e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/876","title":"changing header name","createdAt":"2022-04-13T00:58:15Z"}
{"state":"Merged","mergedAt":"2023-10-17T21:17:31Z","number":8760,"mergeCommitSha":"3b616c638e229c6d9ef774b1bdbf2e4349536dfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8760","title":"Fix topic parser","createdAt":"2023-10-17T21:17:24Z"}
{"state":"Merged","mergedAt":"2023-10-17T21:24:22Z","number":8761,"body":"Was false \uD83E\uDD26‍♂️","mergeCommitSha":"c2eb1643088dcb42e99ada65fc77e289fdb70a57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8761","title":"Default isPending team setting to true","createdAt":"2023-10-17T21:21:22Z"}
{"state":"Merged","mergedAt":"2023-10-17T21:48:00Z","number":8762,"mergeCommitSha":"5f20ee9fe54ca69e3b24fd2bb09382794b2bec3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8762","title":"Fix Linear oauth via Connected Accounts","createdAt":"2023-10-17T21:23:55Z"}
{"state":"Merged","mergedAt":"2023-10-17T21:57:36Z","number":8763,"body":"Type safe chaining. I have lots of ideas for additional chain capabilities but will take the \"add them when we need them\" approach.","mergeCommitSha":"59dd33154cfef2d3552096f24c9e7e616be94ce3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8763","title":"Use chains in dev","createdAt":"2023-10-17T21:30:53Z"}
{"state":"Merged","mergedAt":"2023-10-17T22:05:39Z","number":8764,"body":"Tests for #8761 ","mergeCommitSha":"89d80b46c4e73d31b7074517fae910a0385aa30b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8764","title":"Tests for pending teams enable settings bug","createdAt":"2023-10-17T21:34:46Z"}
{"state":"Merged","mergedAt":"2023-10-17T23:53:10Z","number":8765,"mergeCommitSha":"21b06668b30bb1242162b123da0e1286a6103180","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8765","title":"Move interface default implementations to extension functions","createdAt":"2023-10-17T21:59:19Z"}
{"state":"Merged","mergedAt":"2023-10-18T02:32:49Z","number":8766,"body":"Having a hard time testing this on local dev however. yolo.","mergeCommitSha":"934d24500ea8d1d197f610201752544918ffcc23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8766","title":"Return a List from HF-TGI","createdAt":"2023-10-17T22:02:12Z"}
{"state":"Merged","mergedAt":"2023-10-17T23:08:31Z","number":8767,"mergeCommitSha":"50c6382c38d15abd13a3ec1341b8edc3690bdf92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8767","title":"Funnel through team id","createdAt":"2023-10-17T22:06:08Z"}
{"state":"Merged","mergedAt":"2023-10-17T22:33:47Z","number":8768,"mergeCommitSha":"48eac621294247b98accc101b2f0f068c3addbd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8768","title":"Improve documentation recall by entitling each partition","createdAt":"2023-10-17T22:13:47Z"}
{"state":"Merged","mergedAt":"2023-10-18T03:43:42Z","number":8769,"body":"Pending teams will (soon) no longer be authorized in the JWT _teams_ claim.\nAs a result, none of the authorized `/api/teams/:id` endpoints will work for pending teams.\n\nSince the `getTeamStatus` endpoint is specifically designed for pending teams, we remove auth from it.\nTechnically anyone can retrieve status information about any team, but the response is anemic.","mergeCommitSha":"5a077ced27a3e13ef50d8719f22d12394b8c338d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8769","title":"[BREAKS API ON MAIN] Remove auth from getTeamStatus","createdAt":"2023-10-17T22:45:08Z"}
{"state":"Merged","mergedAt":"2022-04-13T02:31:41Z","number":877,"mergeCommitSha":"35d491efb6145b8decc96e6301a4fe46c1ea9e55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/877","title":"Add amazon headers","createdAt":"2022-04-13T02:20:49Z"}
{"state":"Merged","mergedAt":"2023-10-17T23:27:21Z","number":8771,"mergeCommitSha":"68e324b998f6c7e1e37ed36f48473b3ae706cc28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8771","title":"Introduce abstract content partitioning","createdAt":"2023-10-17T23:13:01Z"}
{"state":"Merged","mergedAt":"2023-10-18T01:55:38Z","number":8772,"mergeCommitSha":"31608c851778c3faab0ddfe30fae3c70849fdea0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8772","title":"Convert Notion bulleted lists to proper markdown","createdAt":"2023-10-17T23:18:53Z"}
{"state":"Merged","mergedAt":"2023-10-18T05:54:34Z","number":8773,"mergeCommitSha":"c27beb604424bf2bf90e051e1aea608743b25292","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8773","title":"Enable chaining in production","createdAt":"2023-10-17T23:30:22Z"}
{"state":"Merged","mergedAt":"2023-10-18T00:20:57Z","number":8774,"mergeCommitSha":"cb8179340513cf32de02a64447b7ea9e52b10c6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8774","title":"Disable gh caching","createdAt":"2023-10-18T00:09:32Z"}
{"state":"Merged","mergedAt":"2023-10-18T06:49:45Z","number":8775,"mergeCommitSha":"080467c8d46b6e0ccb5610e196ce2ca96d4f2229","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8775","title":"Add inline references resolver template type","createdAt":"2023-10-18T00:18:36Z"}
{"state":"Merged","mergedAt":"2023-10-18T14:34:42Z","number":8776,"body":"Debugging https://app.intercom.com/a/inbox/crhakcyc/inbox/shared/all/conversation/910?view=List\r\n\r\nFew things:\r\n1. We're sending Hub IDE notifications when the IDE version is already correct. This is likely related to this https://github.com/NextChapterSoftware/unblocked/commit/c37bb935922d26fd85ccff0e82e7b3b37b77e440.\r\nWe are not checking if the IDE version is out of date before sending the hub notification. \r\n\r\n2. The notification body content we are sending should only occur if the user has multiple IDE types that require updating. In situations where we have multiple IDEs of the same type, aka multiple VSCode instances, we should send the notification message for a single IDE type.\r\n\r\n","mergeCommitSha":"37e8c640f5e5e47fdb673b5c79b0b0d0551e0e48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8776","title":"Check if IDE update is necessary before sending hub notification","createdAt":"2023-10-18T00:26:30Z"}
{"state":"Merged","mergedAt":"2023-10-18T06:38:01Z","number":8777,"body":"The algorithm repeatedly splits and merges structures of markdown documents (heading, paragraphs, sentences) to shape the chunks into nicely sized partitions.\r\n\r\nDoesn't have support yet for block structures like: list groups, code fencing, and quote blocks.","mergeCommitSha":"cebcb63cf11966c4e2b5ef87b6940d86cf585caf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8777","title":"Introduce markdown partitioner","createdAt":"2023-10-18T00:38:26Z"}
{"state":"Merged","mergedAt":"2023-10-18T04:37:21Z","number":8778,"mergeCommitSha":"66354d564cc99546a46ce5fc925cecad8754af56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8778","title":"Convert Notion tables, dividers, and links to markdown","createdAt":"2023-10-18T02:32:24Z"}
{"state":"Merged","mergedAt":"2023-10-18T18:10:21Z","number":8779,"body":"and add a test!","mergeCommitSha":"fb3a86481a02519286ea2f6cdb23bf28762ebe1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8779","title":"Update InstructApi to correct deserialization code","createdAt":"2023-10-18T03:09:47Z"}
{"state":"Merged","mergedAt":"2022-04-13T03:29:55Z","number":878,"mergeCommitSha":"15c0b6d909019540e77d963aedeb8c197f9681c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/878","title":"removing the extra header","createdAt":"2022-04-13T03:01:15Z"}
{"state":"Merged","mergedAt":"2023-10-18T05:15:54Z","number":8780,"mergeCommitSha":"e4e6fa6a6e753255e45f037b43994077846a0575","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8780","title":"Add Notion to admin console","createdAt":"2023-10-18T05:02:48Z"}
{"state":"Merged","mergedAt":"2023-10-18T06:31:00Z","number":8781,"mergeCommitSha":"146503fdb47344fdc4366e3ed2a43b5abcbd6e03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8781","title":"Retry Notion page ingestion event handling if lock renew fails","createdAt":"2023-10-18T05:28:36Z"}
{"state":"Merged","mergedAt":"2023-10-18T07:48:06Z","number":8782,"mergeCommitSha":"398ee327bebefc44a461310e6c0c7669118b8882","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8782","title":"Implements inline references resolver","createdAt":"2023-10-18T06:27:01Z"}
{"state":"Merged","mergedAt":"2023-10-18T07:19:40Z","number":8783,"mergeCommitSha":"6ecc9ba220ad7f3a1538e1d7097b7bc384f601e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8783","title":"Handle Notion numbered list items","createdAt":"2023-10-18T06:55:31Z"}
{"state":"Merged","mergedAt":"2023-10-18T08:16:09Z","number":8784,"body":"Harmless, but very annoying.","mergeCommitSha":"53410f0eadafb5ccd779f00ee03a1f6bd7a5b8dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8784","title":"Fix truncation warning","createdAt":"2023-10-18T07:56:23Z"}
{"state":"Merged","mergedAt":"2023-10-18T17:34:14Z","number":8785,"body":"Fix follow up with tests.","mergeCommitSha":"e170da2bedce4274b25ff0cf9dfa6543323d1a16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8785","title":"Fix getTeamStatus 404","createdAt":"2023-10-18T17:32:13Z"}
{"state":"Merged","mergedAt":"2023-10-18T18:10:33Z","number":8786,"mergeCommitSha":"d3ee93893c9b32fdab5a5d5e2e5306de9267cbb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8786","title":"[SKIP TESTS] Remove Notion dynamo entries when dropping embeddings","createdAt":"2023-10-18T17:49:12Z"}
{"state":"Merged","mergedAt":"2023-10-20T15:46:32Z","number":8787,"body":"Add a view to display when there are no supported repos in an org/team.\r\n\r\n![CleanShot 2023-10-18 at 10 40 36@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/874c456a-0143-4d8c-bd1b-133f3a2c575b)\r\n![CleanShot 2023-10-18 at 10 49 14@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/3d469ad9-bae9-4c00-badc-49fc69ab2f00)\r\n![CleanShot 2023-10-18 at 16 10 59@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/5b8cea93-ca1a-4cb5-88e0-65a7ad542237)\r\n\r\n\r\nThis involved a lot of changes:\r\n\r\n* In the `PendingProcessing` view (displayed when a team is in the pending state), we now query for the team's status (`getTeamStatus`) every thirty seconds, and whenever the UI refocuses.  If this returns `missingValidRepos` (there are no usable repos) or `uninstalled` (user has uninstalled the GH app), we render the `UnsupportedRepos` view.\r\n* `UnsupportedRepos` has three forms: \"internal\" (configuration is done internally in UB, this is for bitbucket), \"external\" (configuration is done externally, in the SCM), and \"uninstalled\" (SCM is uninstalled and needs reinstallation).\r\n* The external UI displays two buttons, allowing reconfiguring the existing team in the SCM, and adding a new team in the SCM\r\n* The internal UI lets you reconfigure the team in UB, and add from a list of teams.  This internal UI reuses the existing Connection Row UI from the onboarding UI.\r\n* The uninstalled UI lets you reinstall the team in the SCM.\r\n* As part of this work, I removed the Personal Org warning UI, as we should never point anyone towards it anymore.","mergeCommitSha":"acef0a7e5d90e04ea9ef8623c53dc40a7166ea3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8787","title":"Unsupported repos view","createdAt":"2023-10-18T17:53:54Z"}
{"state":"Merged","mergedAt":"2023-10-18T19:47:50Z","number":8788,"body":"`AnimatingHeightSizer` animates changes in a component's height.  We were inadvertently animating the initial sizing as well, which caused navigation in the landing page to jump to the wrong point.  This fixes the issue, the initial render will size without animating.","mergeCommitSha":"8d3bb823219c7cc89aa80e87b79a8cd954f3b803","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8788","title":"Fix landing page animation jitter","createdAt":"2023-10-18T18:54:35Z"}
{"state":"Merged","mergedAt":"2023-10-18T19:31:04Z","number":8789,"body":"This will help with cleanup after","mergeCommitSha":"e082ce93b8110df2869944e0439ea513a4ff961f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8789","title":"Add source document ID to embedding metadata for document sources","createdAt":"2023-10-18T19:13:26Z"}
{"state":"Merged","mergedAt":"2022-04-13T16:14:59Z","number":879,"body":"fix git commit ordering bugs","mergeCommitSha":"cb044a73a1c5a2fa99e07797873e491b75d9809a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/879","title":"Bug fixes","createdAt":"2022-04-13T06:59:47Z"}
{"state":"Merged","mergedAt":"2023-10-19T02:28:36Z","number":8790,"mergeCommitSha":"2f8dc8f752351ef9704757a64bea034a84d1e481","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8790","title":"Update unblocked topic classification to use llama2","createdAt":"2023-10-18T19:18:38Z"}
{"state":"Merged","mergedAt":"2023-10-18T19:18:55Z","number":8791,"mergeCommitSha":"aa4ffe519bc3aa76dd3c3c998aded1c615e2201a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8791","title":"topic generation from repos","createdAt":"2023-10-18T19:18:48Z"}
{"state":"Merged","mergedAt":"2023-10-18T23:56:34Z","number":8792,"mergeCommitSha":"a6f180e9bf4f273f9f2c2783af52ba1340ec820a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8792","title":"Hack to set correct team during onboarding","createdAt":"2023-10-18T21:13:36Z"}
{"state":"Merged","mergedAt":"2023-10-18T21:50:08Z","number":8793,"mergeCommitSha":"345270c907a7bcc9cab7a455e0f4d10f22c76652","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8793","title":"Increase IntegrationEvent consumer count to 2","createdAt":"2023-10-18T21:32:03Z"}
{"state":"Merged","mergedAt":"2023-10-18T22:31:58Z","number":8794,"body":"https://www.notion.so/nextchaptersoftware/Notion-Integration-5cf66f33eb2f4893937f941e01f092fb","mergeCommitSha":"2bb2500b4485593e50b398a68f6101c82c5813a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8794","title":"Schedule a reingest 60 minutes after Notion oauth","createdAt":"2023-10-18T22:08:34Z"}
{"state":"Merged","mergedAt":"2023-10-18T23:47:19Z","number":8795,"mergeCommitSha":"fff2ed71a1168a6dc11b7e1e57a82672ae2b5084","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8795","title":"Up the inline resolution timeout","createdAt":"2023-10-18T23:46:54Z"}
{"state":"Merged","mergedAt":"2023-10-20T16:43:46Z","number":8796,"body":"<img width=\"1503\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/7897d79d-8ef3-4fa0-a044-9f158d0f6e92\">\r\n<img width=\"1503\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/d8666eef-6af5-45ed-9cc0-b772e6b8f6b1\">\r\n<img width=\"1497\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/0a43dcd0-f166-4a2c-8adc-fa8cdd7419a5\">\r\n\r\nLight mode:\r\n<img width=\"1500\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/c353ed84-b45e-4689-a552-9c1f408e10f4\">\r\n<img width=\"1497\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/632813dd-d240-4fb4-b121-e088da8fb26b\">\r\n\r\nAlso updated some landing page views that will be linkable via the nav:\r\n<img width=\"1507\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/e937bcd3-7dd6-47e7-a9d0-4642ae368c8b\">\r\n<img width=\"1505\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/6a0cc7a9-e71c-474d-8eba-048812b5fa32\">\r\n \r\nNote that we're keeping the top bar in smaller viewports:\r\n<img width=\"685\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/835d1380-8c0b-44d5-a717-6dbbc3547310\">\r\n<img width=\"766\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/44aee1e2-5d45-4e8f-8d50-c76201d085c7\">\r\n","mergeCommitSha":"50fb0fae9d59c27c327f9d4dda4b7db46c982c44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8796","title":"Update dashboard nav","createdAt":"2023-10-18T23:50:23Z"}
{"state":"Merged","mergedAt":"2023-10-19T06:22:50Z","number":8797,"mergeCommitSha":"48e4ef8d83ae470ea07c94e518b38c1a3df5ff45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8797","title":"[SKIP TESTS] Show which teams are full mode on Teams page","createdAt":"2023-10-18T23:50:36Z"}
{"state":"Merged","mergedAt":"2023-10-19T02:40:08Z","number":8798,"mergeCommitSha":"38cb141573a1e39c88277d6d2255107ac3ce37f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8798","title":"Inline references for all","createdAt":"2023-10-19T02:17:59Z"}
{"state":"Merged","mergedAt":"2023-10-19T04:45:49Z","number":8799,"mergeCommitSha":"19361755a78edabde9ad26e392de0872f74510b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8799","title":"Trailing reference error","createdAt":"2023-10-19T04:43:17Z"}
{"state":"Merged","mergedAt":"2022-01-20T19:44:08Z","number":88,"body":"This PR adds a couple of additions to support fuzzy searching to enable returning results even when a query is misspelled. \r\n\r\nThe next steps are to implement the models. As we build those, we can drop in this logic and enable search.\r\n\r\n[Start here to review](https://github.com/Chapter2Inc/codeswell/pull/88/files#r788360472)\r\n\r\n***\r\n\r\nAnother application of fuzzy search is providing search suggestions as the user types into the search bar. The client would hit the backend on every keystroke (perhaps debouncing) and get back a list of fuzzy-matched suggested search terms that we know will return results. This would require maintaining a table of suggested terms, but that table wouldn't need to always be 100% up to date. This is described in more detail in section F.31.5 of the [pg_trgm docs](https://www.postgresql.org/docs/9.6/pgtrgm.html).","mergeCommitSha":"ac72b54e1d3100eb47917c847436e2137f66932b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/88","title":"Add support for fuzzy search","createdAt":"2022-01-20T05:17:51Z"}
{"state":"Merged","mergedAt":"2022-04-13T16:03:52Z","number":880,"body":"Seems CloudFront still needs the value to be signed. I am getting unsigned payload errors so if this doesn't fix it I'll abandon this approach and fallback to generating signed urls in Lambda. ","mergeCommitSha":"769d8de80c3727ac87b89e88471265caf5b033fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/880","title":"adding header param back. ","createdAt":"2022-04-13T15:44:07Z"}
{"state":"Merged","mergedAt":"2023-10-19T12:34:04Z","number":8800,"body":"- Clean up pipelines\r\n- Update\r\n","mergeCommitSha":"52f6ed7ac6209e1f0030e8fc5be50c51fbbb038a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8800","title":"CleanupPipelines","createdAt":"2023-10-19T12:08:03Z"}
{"state":"Merged","mergedAt":"2023-10-19T13:13:08Z","number":8801,"body":"[SKIP TESTS]","mergeCommitSha":"e208ae36f4c1ab6744d597c3f33c1061ae2930cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8801","title":"[SKIP TESTS] Fix topics","createdAt":"2023-10-19T13:12:05Z"}
{"state":"Merged","mergedAt":"2023-10-19T18:09:16Z","number":8802,"body":"Addresses cases where the model gets confused about the task if the classification doc is difficult enough.\r\n\r\nNote that this type to type prompt thing is a pretty cool feature of some leading prompt frameworks that I am trying to replicate here, but maybe should just be a feature of @pwerry prompts?\r\n\r\nTail classifications metrics: https://console.statsig.com/pe6l8RnBK3jpVFgSbpx4S/metrics/events","mergeCommitSha":"e7349df708f4d6f05472a90cbc3508e5539d57eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8802","title":"Improve the sandbox around the topic classification prompt","createdAt":"2023-10-19T17:13:30Z"}
{"state":"Merged","mergedAt":"2023-10-19T19:07:56Z","number":8803,"body":"Change the node versions we use for CI building:\r\n\r\n* Sourcemark agent and our \"shared\" test CI builds will be pinned to node 20\r\n* VSCode is bumped from node 16 to 18, as the node that ships with VSCode was bumped earlier this year.\r\n\r\nI have *not* changed the IntelliJ node, it will still use a pinned version of node 18.  This makes sense as the IDEs largely share code and will have an equal node version.","mergeCommitSha":"d26b2d883169919eb02550c8b19a7d539b2d105f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8803","title":"Bump/standardize node versions","createdAt":"2023-10-19T18:42:36Z"}
{"state":"Merged","mergedAt":"2023-10-19T20:14:09Z","number":8804,"mergeCommitSha":"79f915453a53fe1be8aa0021fa1a5f77b496ecb5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8804","title":"Add topic ingestion admin","createdAt":"2023-10-19T19:14:23Z"}
{"state":"Merged","mergedAt":"2023-10-19T22:45:51Z","number":8805,"mergeCommitSha":"06d7a5946b7d1382a9f5cbf54c899cae68cccc8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8805","title":"Perform deep repo check for getTeamStatus","createdAt":"2023-10-19T19:53:26Z"}
{"state":"Merged","mergedAt":"2023-10-19T20:05:34Z","number":8806,"mergeCommitSha":"9243e0b195c96ad7fef92acde09f9436ae7004fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8806","title":"Remove reaction permission","createdAt":"2023-10-19T20:05:22Z"}
{"state":"Merged","mergedAt":"2023-10-19T23:15:06Z","number":8807,"body":"Also added a test, both of these are a WIP as we are still learning the Code-Llama prompt format.","mergeCommitSha":"2e2cd759b17e6e264afec82d3d0835cfc30a88f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8807","title":"Change topic classification prompt to be less clever","createdAt":"2023-10-19T20:38:01Z"}
{"state":"Merged","mergedAt":"2023-10-19T20:53:17Z","number":8808,"mergeCommitSha":"0f159fb0f4d47a47af14e96d2734fe09601db964","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8808","title":"Remove llms","createdAt":"2023-10-19T20:53:11Z"}
{"state":"Merged","mergedAt":"2023-10-19T21:43:13Z","number":8809,"mergeCommitSha":"333b2ba241a003f01b381c20551629a0ae9400f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8809","title":"Source code fixes","createdAt":"2023-10-19T21:43:02Z"}
{"state":"Merged","mergedAt":"2022-04-13T18:12:30Z","number":881,"mergeCommitSha":"1ed2c7e0adef5a4b98ca214390b18346e535bef8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/881","title":"Add exception errors for apiservice","createdAt":"2022-04-13T17:43:08Z"}
{"state":"Merged","mergedAt":"2023-10-19T23:30:29Z","number":8810,"mergeCommitSha":"5de184507d0478f87bee4332cca6f338509ee54b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8810","title":"Fix EmbeddingData source document ID","createdAt":"2023-10-19T21:54:30Z"}
{"state":"Merged","mergedAt":"2023-10-20T17:57:54Z","number":8811,"body":"Next PR will add logic to emit an event to let us to clean up embeddings for documents (confluence, notion, stackoverflow) that no longer exist.","mergeCommitSha":"66d7aed928f8d5ac8938744a1011f4f17025136a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8811","title":"Add DocumentListPassthroughStep","createdAt":"2023-10-19T22:28:01Z"}
{"state":"Merged","mergedAt":"2023-10-19T22:30:53Z","number":8812,"mergeCommitSha":"9d683bca74da6257ccb870e15fd5c4d265178217","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8812","title":"Fix test","createdAt":"2023-10-19T22:30:43Z"}
{"state":"Merged","mergedAt":"2023-10-20T01:54:11Z","number":8813,"mergeCommitSha":"9de951b6af2b0d726a8a4096ded76abe13f82213","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8813","title":"Remove calls to pending teams API in the Hub","createdAt":"2023-10-19T22:47:48Z"}
{"state":"Merged","mergedAt":"2023-10-20T00:02:46Z","number":8814,"mergeCommitSha":"d8927244f6516a4875aebd4e4b76c85e303c7fda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8814","title":"Make hub updates silent if there is no release description","createdAt":"2023-10-19T23:21:20Z"}
{"state":"Merged","mergedAt":"2023-10-20T02:27:25Z","number":8815,"mergeCommitSha":"2f69786512b9455171064c8960afe0726da71bfb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8815","title":"Topic pipeline v2","createdAt":"2023-10-20T02:11:47Z"}
{"state":"Merged","mergedAt":"2023-10-20T14:12:52Z","number":8816,"body":"Addresses race between checking for repo status and creating the repos.\nhttps://github.com/NextChapterSoftware/unblocked/pull/8805#discussion_r1366125889","mergeCommitSha":"f01f5c77b9959e73a49fba5eaf17cc762bb0c51a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8816","title":"Wait for team to be initialized","createdAt":"2023-10-20T07:27:39Z"}
{"state":"Merged","mergedAt":"2023-10-20T14:18:36Z","number":8817,"mergeCommitSha":"3fd8264fbdd5ec571da003af388466dbd8131047","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8817","title":"Remove p4de instances","createdAt":"2023-10-20T14:17:16Z"}
{"state":"Merged","mergedAt":"2023-10-20T18:13:40Z","number":8818,"mergeCommitSha":"88739e1723be2f663e3d0ca4c89e256f96dc7a1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8818","title":"Fix popover width and add chevron","createdAt":"2023-10-20T17:13:14Z"}
{"state":"Merged","mergedAt":"2023-10-20T17:52:43Z","number":8819,"mergeCommitSha":"1d359930a1c2fb5b021afb8b0d16b48f1aec7ec2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8819","title":"Log when a document is skipped over for embedding","createdAt":"2023-10-20T17:38:22Z"}
{"state":"Merged","mergedAt":"2022-04-13T18:02:30Z","number":882,"body":"- Subscribes to store initialization\r\n- On store initialization the calculator runs a full recalculation,\r\n  and also cancels the subscription. We only want to run this once\r\n  as all subsequent calculations should be incremental.\r\n- All public calculator APIs block on initialization.","mergeCommitSha":"244c8b65e749347830fce668cb4cfeb7a9a4b25e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/882","title":"[RFC] Run recalculator when source marks store has been initialized","createdAt":"2022-04-13T18:00:36Z"}
{"state":"Merged","mergedAt":"2023-10-20T19:57:52Z","number":8820,"body":"The underlying issue is that the `TopicViewModel` was only capable of loading once. This is problematic when a team switch occurs during onboarding, which is possible in circumstances where a user initially enters onboarding using a person org, and then a team org is enabled. \r\n\r\nSee additional line level commentary for more details","mergeCommitSha":"e3ad9a07766c704f5f1e6cb8933ce0896548abb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8820","title":"Fix topic view model issues","createdAt":"2023-10-20T18:41:42Z"}
{"state":"Merged","mergedAt":"2023-10-20T18:54:01Z","number":8821,"mergeCommitSha":"cab434c936236a45667c0be2544bc5b4e44be5a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8821","title":"Bump down z-index","createdAt":"2023-10-20T18:42:38Z"}
{"state":"Merged","mergedAt":"2023-10-20T20:19:16Z","number":8822,"mergeCommitSha":"57e4192cfc822e389e3276656f6c5bec5891f5eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8822","title":"Centralize the IP addresses that Unblocked service calls originate from","createdAt":"2023-10-20T18:47:11Z"}
{"state":"Merged","mergedAt":"2023-10-20T19:04:32Z","number":8823,"body":"<img width=\"427\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/dc97a07b-df2f-49f3-9974-78eb6a2d4c31\">\r\n","mergeCommitSha":"6878c69d684517d25236291be7c8159ed2aa48ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8823","title":"Change provider text to icon","createdAt":"2023-10-20T18:53:56Z"}
{"state":"Merged","mergedAt":"2023-10-20T20:26:52Z","number":8824,"mergeCommitSha":"7875b0ef3f957f04207656c70496d9c2531fe56c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8824","title":"Log document id for stack overflow question","createdAt":"2023-10-20T18:54:09Z"}
{"state":"Merged","mergedAt":"2023-10-20T20:18:36Z","number":8825,"body":"https://github.com/NextChapterSoftware/unblocked/assets/********/3e28cf76-aeda-48bf-bffe-a2ab3bac5615\r\n\r\n","mergeCommitSha":"93bee225521b4f2caff611221a794062322f4fb0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8825","title":"Both team and name need to ellipse","createdAt":"2023-10-20T19:45:29Z"}
{"state":"Merged","mergedAt":"2023-10-20T19:58:39Z","number":8826,"mergeCommitSha":"bb88e383f64e700532506183b5d9142268acd619","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8826","title":"Fix notifications enabled content","createdAt":"2023-10-20T19:53:13Z"}
{"state":"Merged","mergedAt":"2023-10-20T21:48:39Z","number":8827,"mergeCommitSha":"341b5e17a1646080c6dbe137daf488de8041119c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8827","title":"Introduce GET /api/meta no-auth endpoint to vend public IP config","createdAt":"2023-10-20T19:57:16Z"}
{"state":"Merged","mergedAt":"2023-10-20T21:00:16Z","number":8828,"body":"Used in the navbar as below, among a few other places:\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/5182ed5f-9b8e-4017-9d6b-da87217b1c1c\r\n\r\n","mergeCommitSha":"1001ac2f073fde5c8479af17e6abf5ad4574de6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8828","title":"Animate expandable sections open/close","createdAt":"2023-10-20T20:28:40Z"}
{"state":"Merged","mergedAt":"2023-10-20T20:58:36Z","number":8829,"mergeCommitSha":"c1acdef74e4e1474daa6fa40ff51b6507af8cdfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8829","title":"Update processing header text","createdAt":"2023-10-20T20:32:08Z"}
{"state":"Merged","mergedAt":"2022-04-13T18:57:18Z","number":883,"body":"Index the TextEditorSourceMark instances by file path, not by document.  I thought the documents were stable, but they can be removed, so the file path is a better index.  It also matches how the rest of our SourceMark code works anyways.","mergeCommitSha":"66f6e45ef37bd0c33e899e7d7e13e1ab1879ccd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/883","title":"TextEditorSourceMarkManager indexes by file path","createdAt":"2022-04-13T18:05:42Z"}
{"state":"Merged","mergedAt":"2023-10-20T20:58:49Z","number":8830,"mergeCommitSha":"96441daf8d407d6238aaf88cd329a5f8d46dd9e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8830","title":"Fix team selector menu width","createdAt":"2023-10-20T20:47:32Z"}
{"state":"Merged","mergedAt":"2023-10-20T22:14:09Z","number":8831,"body":"When your team goes from 'pending' to 'ok', we redirected between the index and pending states endlessly.\r\n\r\nThe main problem is that the `TeamStore` only used to think that its data would change if the overall list of teams had changed -- but we now want to take `isPending` into account too.\r\n\r\nAlso, when the Pending page thinks that the team is no longer pending, we now force the team store to refresh before navigating to the index.","mergeCommitSha":"4f87eac045e3aa3c534847057f2840b300967234","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8831","title":"Fix endless redirects after exiting pending state in the dashboard","createdAt":"2023-10-20T21:34:56Z"}
{"state":"Merged","mergedAt":"2023-10-20T22:21:49Z","number":8832,"body":"Fixes broken team equality checks\n","mergeCommitSha":"524101f0dced350ffcaa5a8b289ef80ddc331cd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8832","title":"Fixes broken team equality checks","createdAt":"2023-10-20T22:12:51Z"}
{"state":"Merged","mergedAt":"2023-10-20T22:20:52Z","number":8833,"mergeCommitSha":"720cfea60cfac57649a9b4bbc5cdd1f62cfc4a58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8833","title":"[SKIP TESTS] Fix logging in StackOverflowTeamsEmbeddingService","createdAt":"2023-10-20T22:20:44Z"}
{"state":"Merged","mergedAt":"2023-10-20T23:00:20Z","number":8834,"mergeCommitSha":"ab624c64f937960470017f785098bd5d40352bbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8834","title":"Dont skip embedding short Stack Overflow documents","createdAt":"2023-10-20T22:46:10Z"}
{"state":"Merged","mergedAt":"2023-10-23T17:54:04Z","number":8835,"body":"\r\n<img width=\"935\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/9912a4e2-72e1-4154-b54d-eaeb30baa301\">\r\n\r\n","mergeCommitSha":"3b67eddd278bbce231b7a2a56ac5607d94eee2f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8835","title":"Move team selector menu to right edge","createdAt":"2023-10-20T23:05:31Z"}
{"state":"Merged","mergedAt":"2023-10-20T23:45:39Z","number":8836,"mergeCommitSha":"2bd2cb9c94dfc862a56e2ee6201499d9e6f4435a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8836","title":"Reduce scopes more","createdAt":"2023-10-20T23:45:21Z"}
{"state":"Merged","mergedAt":"2023-10-21T00:06:16Z","number":8837,"mergeCommitSha":"d25e4f594d3d516886f3e3b9f42a2dcbe2848553","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8837","title":"Remove more permissions","createdAt":"2023-10-20T23:51:56Z"}
{"state":"Merged","mergedAt":"2023-10-21T00:22:58Z","number":8838,"mergeCommitSha":"e804718d7f5a96c4a54bb275caeb8323f7b8cbcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8838","title":"Yet more slack scope reductions","createdAt":"2023-10-21T00:22:45Z"}
{"state":"Merged","mergedAt":"2023-10-21T02:25:38Z","number":8839,"mergeCommitSha":"1c2f5b3b4309f486d47daeed1b1b1693053d8da4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8839","title":"Topics writer","createdAt":"2023-10-21T02:23:06Z"}
{"state":"Merged","mergedAt":"2022-04-13T22:05:02Z","number":884,"body":"- Subscribes to store initialization.\r\n- On store initialization the calculator runs a full recalculation,\r\n  and also cancels the subscription. We only want to run this once\r\n  as all subsequent calculations should be incremental.\r\n- All public calculator APIs block on initialization.\r\n\r\nhttps://user-images.githubusercontent.com/1798345/163258526-2c025882-f016-46e8-bb5a-62ff7f3bdf9a.mov\r\n\r\n\r\n","mergeCommitSha":"11a413caa99582f16c72dd3ae79cc56f5d26fa62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/884","title":"Run recalculator when source marks store has been initialized","createdAt":"2022-04-13T18:06:54Z"}
{"state":"Merged","mergedAt":"2023-10-21T05:44:51Z","number":8840,"mergeCommitSha":"3fae55cfcfb803adba21b62b0b3e8b8ac488d837","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8840","title":"Remove DEV release notes, so silent updates","createdAt":"2023-10-21T02:54:26Z"}
{"state":"Merged","mergedAt":"2023-10-23T07:15:44Z","number":8841,"body":"[SKIP TESTS]","mergeCommitSha":"7c5561c58aa1f3d35ac3cf98ce71196b8dce8b6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8841","title":"Topic service ingestion pipeline","createdAt":"2023-10-21T03:41:13Z"}
{"state":"Merged","mergedAt":"2023-10-21T06:44:52Z","number":8842,"body":"Pinecone and Postgres do not share any traits.\n\nBetter to separate this hierarchy.","mergeCommitSha":"e7adc6f839df204c7fec5b7fa98bba61ffde5591","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8842","title":"Remove DocumentQueryService interface","createdAt":"2023-10-21T06:27:39Z"}
{"state":"Merged","mergedAt":"2023-10-21T07:51:35Z","number":8843,"body":"Currently integrated into the Document Retrieval page only.","mergeCommitSha":"947ca637f3bff6918c5b2d45cc4c5521144102b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8843","title":"Pinecone search takes into account document type and provider type","createdAt":"2023-10-21T07:16:44Z"}
{"state":"Merged","mergedAt":"2023-10-21T16:44:57Z","number":8844,"mergeCommitSha":"687edd898fb90288f2ba7faf53c6eec577c350e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8844","title":"Drop inline reference resolver output if no entities found","createdAt":"2023-10-21T16:42:19Z"}
{"state":"Merged","mergedAt":"2023-10-21T16:53:57Z","number":8845,"mergeCommitSha":"1545fce38e520a7dbd887c3e48b920d2b2272994","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8845","title":"Fix type selection on Document Retrieval page","createdAt":"2023-10-21T16:47:07Z"}
{"state":"Merged","mergedAt":"2023-10-21T17:53:38Z","number":8846,"mergeCommitSha":"dc7afacb4cf641f4a18c77043d499f2c88efead6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8846","title":"[SKIP TESTS] Log no entities detected","createdAt":"2023-10-21T17:52:44Z"}
{"state":"Merged","mergedAt":"2023-10-22T05:52:30Z","number":8847,"mergeCommitSha":"e4a0cec62dfeec1b8ee2d5265703d7ef7aac811f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8847","title":"Semantic search takes into account document type and provider type","createdAt":"2023-10-22T04:06:11Z"}
{"state":"Merged","mergedAt":"2023-10-22T04:54:14Z","number":8848,"body":"<img width=\"592\" alt=\"Screenshot 2023-10-21 at 21 45 43\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/e2e80883-b1fd-48be-8462-9bf141390940\">\r\n","mergeCommitSha":"aab60e9a7224983746519a023c99bdb1d83deb75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8848","title":"Fix typo in IDE auth error","createdAt":"2023-10-22T04:53:09Z"}
{"state":"Merged","mergedAt":"2023-10-22T15:19:35Z","number":8849,"mergeCommitSha":"98b7c16f948e723009c941cc2766280bbf9fd59c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8849","title":"[SKIP TESTS] Fix inline refs garbage response","createdAt":"2023-10-22T15:19:06Z"}
{"state":"Merged","mergedAt":"2022-04-13T18:39:07Z","number":885,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1649869555571799","mergeCommitSha":"a13bd62de4f120326476c7a6a2d1fd1a8c526f58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/885","title":"Temporarily disable PR ingestion","createdAt":"2022-04-13T18:25:45Z"}
{"state":"Merged","mergedAt":"2023-10-22T17:01:43Z","number":8850,"mergeCommitSha":"fef971dc2bbb232ad49abf7d741e9478b1417048","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8850","title":"Fix doc type template issue","createdAt":"2023-10-22T16:56:50Z"}
{"state":"Merged","mergedAt":"2023-10-22T18:54:08Z","number":8851,"mergeCommitSha":"e6849b315010db9fecc2348f2d362c06611d88b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8851","title":"Refactor document selection","createdAt":"2023-10-22T17:44:59Z"}
{"state":"Merged","mergedAt":"2023-10-23T02:58:30Z","number":8852,"body":"Refactoring\r\n\r\n- rename DocumentType.Document to DocumentType.Documentation\r\n- refactor content sanitization\r\n- use custom serializers by default\r\n","mergeCommitSha":"b4e080cc0fb185ffdbcc271d13e7846da0b7d7ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8852","title":"Refactoring","createdAt":"2023-10-22T19:57:47Z"}
{"state":"Merged","mergedAt":"2023-10-22T23:14:13Z","number":8853,"mergeCommitSha":"e99edb6477191cd4dfdb72fbc0613d79935ee8c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8853","title":"[SKIP TESTS] More protection from GPT 3.5 nonsense","createdAt":"2023-10-22T23:07:12Z"}
{"state":"Merged","mergedAt":"2023-10-25T17:49:24Z","number":8854,"mergeCommitSha":"6dd0e2c56e80e0e05dbc4b92e42220c77dc7003b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8854","title":"[SKIP TESTS] Log inline references prompt","createdAt":"2023-10-22T23:40:43Z"}
{"state":"Merged","mergedAt":"2023-10-23T04:53:12Z","number":8855,"mergeCommitSha":"a152ed427882fa4d7a2d98d5dde7c548227c2dad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8855","title":"Increase IntegrationEvent consumer count to 4","createdAt":"2023-10-23T03:42:39Z"}
{"state":"Merged","mergedAt":"2023-10-23T20:42:31Z","number":8856,"body":"The idea is that PRs that were closed or are still open can pollute the knowledge base. The naive bot thinks the open/closed PRs are fact.","mergeCommitSha":"2ca81f1f46108907102437029293c4006c91aea6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8856","title":"[RFC] Only embed merged PRs","createdAt":"2023-10-23T05:31:25Z"}
{"state":"Merged","mergedAt":"2023-10-23T18:08:13Z","number":8857,"mergeCommitSha":"f320d2c1f15cf3e69008494069ffc45b07a68371","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8857","title":"Tighten up some of the code ingestion embeddings","createdAt":"2023-10-23T05:41:44Z"}
{"state":"Merged","mergedAt":"2023-10-23T07:35:07Z","number":8858,"mergeCommitSha":"64483a3e61ffb846a70718a7ba71c776dc5f9e31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8858","title":"Fix exceptions","createdAt":"2023-10-23T07:34:48Z"}
{"state":"Merged","mergedAt":"2023-10-23T08:08:30Z","number":8859,"mergeCommitSha":"a0f03699553e6fb499b17e6fad12065bb6a81119","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8859","title":"Update statemachine and other stuff","createdAt":"2023-10-23T08:08:09Z"}
{"state":"Merged","mergedAt":"2022-04-13T18:35:28Z","number":886,"body":"Shows up in Code -> Settings\r\n<img width=\"1552\" alt=\"Screen Shot 2022-04-13 at 11 31 04 AM\" src=\"https://user-images.githubusercontent.com/2133518/163247014-4f976abd-d9c4-465a-b6b7-0fcc862df7ba.png\">\r\n","mergeCommitSha":"b88755fe87bdbcf0101760732883417cf6524684","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/886","title":"Use setting for enabling SM engine","createdAt":"2022-04-13T18:28:51Z"}
{"state":"Merged","mergedAt":"2023-10-23T16:22:40Z","number":8860,"body":"The current inheritance hierarchy led to quite an uptick in invalid state exceptions.\r\nMove to a limited inheritance hierarchy that addresses the original author’s intentions.\r\n","mergeCommitSha":"bed1fdb2c766eb630f9169124436ee3ba9dfa9ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8860","title":"Fix really confusing inheritance model with buggy invalid state exceptions","createdAt":"2023-10-23T08:31:50Z"}
{"state":"Merged","mergedAt":"2023-10-23T08:45:16Z","number":8861,"mergeCommitSha":"117fdab049e8f3e0137a48ba6b585329e2f5d469","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8861","title":"Field is incorreclty name","createdAt":"2023-10-23T08:45:04Z"}
{"state":"Merged","mergedAt":"2023-10-23T09:21:25Z","number":8862,"mergeCommitSha":"5e29f5ecccbfe76f000cb3aacc0f884ddd0ccc1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8862","title":"Fix topic docker","createdAt":"2023-10-23T09:21:18Z"}
{"state":"Merged","mergedAt":"2023-10-23T09:33:38Z","number":8863,"mergeCommitSha":"b74d427c44d58d6a1172c48eead0ebe22ee4bac9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8863","title":"Try agian","createdAt":"2023-10-23T09:33:30Z"}
{"state":"Merged","mergedAt":"2023-10-23T10:19:14Z","number":8864,"mergeCommitSha":"20b2f1186b78f55a4984da1d30515a868d9530bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8864","title":"Fix code","createdAt":"2023-10-23T10:16:38Z"}
{"state":"Merged","mergedAt":"2024-01-08T18:32:53Z","number":8865,"body":"If an integration is removed, we don't want to include the documents in the search results for a Q&A. Since bulk deletion in pinecone destroys performance, lets filter them from the results after search but before we run inference. \r\n\r\nWe'll also want clear these individual records form pinecone + dynamo, but I'll do that in a follow up PR. For now let's log it out to make sure its working well before we start deleting records.\r\n\r\nSidenote: I think this is a change that would benefit from an integration test.\r\n\r\nTODO:\r\n- move repo filter to the SearchResultsFilter\r\n- add logic for filtering out websites or google docs when integration is removed","mergeCommitSha":"8c879e3652ab4f0d751fd286aa353cf7cc969874","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8865","title":"Filter out SearchResults if integration or confluence space has been removed","createdAt":"2023-10-23T16:09:44Z"}
{"state":"Merged","mergedAt":"2023-10-23T16:58:24Z","number":8866,"mergeCommitSha":"6523ac76f937ce7d6dc655e579346b3d3cd20db8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8866","title":"Remove reactions permissions","createdAt":"2023-10-23T16:58:18Z"}
{"state":"Merged","mergedAt":"2023-10-23T17:25:03Z","number":8867,"mergeCommitSha":"aaac43424632e25bbd61a0ee13283d8fa4104113","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8867","title":"Remove reactions","createdAt":"2023-10-23T17:24:45Z"}
{"state":"Open","mergedAt":null,"number":8868,"body":"- Fix the docker ECR password warning\r\n- Fix the workflow not supporting \"pre\" action","mergeCommitSha":"f3fc57a6600eefc4e3b32dd828c58c11ddb75435","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8868","title":"getting rid of some annoying warnings","createdAt":"2023-10-23T17:56:30Z"}
{"state":"Merged","mergedAt":"2023-10-27T17:20:16Z","number":8869,"body":"Make TeamSelector open on hover for larger viewports:\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/********/7a81724c-f2f6-4aea-8bd0-293f60555e40\r\n\r\n","mergeCommitSha":"be701750f1e73ff677971662160b3519a35d44c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8869","title":"Add hover support for dropdowns","createdAt":"2023-10-23T18:08:40Z"}
{"state":"Merged","mergedAt":"2022-04-14T19:11:53Z","number":887,"body":"<img width=\"142\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/163269938-eacd322c-d872-44ea-93bd-624a7fb256cf.png\">\r\n<img width=\"245\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/163269957-b63daba5-3935-4b4d-850e-427d0a80f4ac.png\">\r\n<img width=\"261\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/163269986-6521d687-8a14-4030-8c1e-0dbc4fc6e034.png\">\r\n\r\n * Add hover to open and pinning ability, save to local storage on change to persist state\r\n * Is missing repo name (Jeff to add later) \r\n * Using temporary logo file \r\n * NOTE: Some views/components on GH look like they're styled inline which will possibly break our layout logic ","mergeCommitSha":"d19478f4bc4976b9fc204ecbf1c2f055e8b28b02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/887","title":"[Extension] Sidebar styling","createdAt":"2022-04-13T21:17:07Z"}
{"state":"Merged","mergedAt":"2023-10-23T19:04:35Z","number":8870,"mergeCommitSha":"828924bf0e4280ce981890c6fd6c52feac818e95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8870","title":"Sendgrid should not fail email sending","createdAt":"2023-10-23T18:35:01Z"}
{"state":"Merged","mergedAt":"2023-10-23T20:13:13Z","number":8871,"body":"First step towards removing Jira and Linear issues that are not marked as `done`/`completed`. Follow up PR will remove embeddings for non-completed tasks.","mergeCommitSha":"9837bf0f3e8ba7500882b65a75ad54b9499d986d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8871","title":"Retrieve Jira and Linear issue states","createdAt":"2023-10-23T19:04:35Z"}
{"state":"Merged","mergedAt":"2023-10-23T20:17:42Z","number":8872,"mergeCommitSha":"d71d66e3c278783c01dc321187e00bc91181a5b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8872","title":"Increase slack version","createdAt":"2023-10-23T19:34:09Z"}
{"state":"Merged","mergedAt":"2023-10-24T23:27:23Z","number":8873,"body":"There are border and margin styles coming from the theme file that are concatenating on the landing dividers. This overrides it.","mergeCommitSha":"8f4f69db5c6e3eced60ae7c2226c29e69872fedd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8873","title":"fix clashing border and margin styles","createdAt":"2023-10-23T21:27:47Z"}
{"state":"Merged","mergedAt":"2023-10-23T23:02:17Z","number":8874,"body":"- Deployed us-east-2 prod cluster\r\n- Deployed all controllers except Falco to the cluster\r\n- Configured Grafana monitoring. \r\n\r\nNext step is to configure VPC to VPC cross region network and modify the CDK code to setup the VPN routes. ","mergeCommitSha":"28ba97f1f61d045bac8f43163cd89ab88f73d2fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8874","title":"Configs needed to deploy east-2 prod cluster","createdAt":"2023-10-23T22:27:59Z"}
{"state":"Merged","mergedAt":"2023-10-23T23:29:08Z","number":8875,"mergeCommitSha":"5e3dd23996918537abb53e882040a75cf583fd37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8875","title":"Refactor classes to lib-partition for reuse","createdAt":"2023-10-23T23:12:27Z"}
{"state":"Merged","mergedAt":"2023-10-24T00:14:56Z","number":8876,"mergeCommitSha":"b9209c51326f8a26b2ab01d69488926bd09a33ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8876","title":"Skip ingesting incomplete Jira and Linear issues","createdAt":"2023-10-23T23:33:47Z"}
{"state":"Merged","mergedAt":"2023-10-24T23:47:40Z","number":8877,"body":"<img width=\"678\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/04046903-8113-47d8-84b0-6ec7d52d4028\">\r\n","mergeCommitSha":"3297f65166104822edfba83f53e19d75c30ba0b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8877","title":"Use textarea for QA input for multiline input","createdAt":"2023-10-23T23:44:46Z"}
{"state":"Merged","mergedAt":"2023-10-24T07:07:38Z","number":8878,"mergeCommitSha":"62e0f2617d5f3473d9bc0aa4567b088f2d5f0d7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8878","title":"Introduce RepoSummary and IntegrationSummary to answer what unblocked has access to","createdAt":"2023-10-24T00:23:01Z"}
{"state":"Merged","mergedAt":"2023-10-24T17:01:12Z","number":8879,"mergeCommitSha":"59cbec050914c3b68ed1de5bb1291b5f8cf92983","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8879","title":"Topic Completion eent","createdAt":"2023-10-24T02:21:42Z"}
{"state":"Merged","mergedAt":"2022-04-13T22:29:31Z","number":888,"body":"Handle file save event, and re-request sourcemarks on the relevant file.  Handle debounced file editing events, but for now take no action.","mergeCommitSha":"00ede0fd0c77b88acfd80eaeb60a1acb4cc7af70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/888","title":"Source mark actions on file save and edit","createdAt":"2022-04-13T21:26:03Z"}
{"state":"Merged","mergedAt":"2023-10-24T06:57:08Z","number":8880,"mergeCommitSha":"8c27f9dc40db0f65359bdb9e72a3ca314bde92d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8880","title":"Inline refs cleaner service","createdAt":"2023-10-24T06:42:19Z"}
{"state":"Merged","mergedAt":"2023-10-24T08:44:14Z","number":8881,"mergeCommitSha":"aed5689044d94e6af24f79e0698d66bd24225363","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8881","title":"More inline cleanup","createdAt":"2023-10-24T08:43:14Z"}
{"state":"Merged","mergedAt":"2023-10-24T13:30:57Z","number":8882,"mergeCommitSha":"bef78fd5d8c403916da9222ed55b89e55ca3de7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8882","title":"Delete not completed Jira and Linear issues","createdAt":"2023-10-24T12:51:41Z"}
{"state":"Merged","mergedAt":"2023-10-24T16:39:05Z","number":8883,"mergeCommitSha":"3f09d1aeb37702c4e0c2c9a1a875065643239d0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8883","title":"Add button to reingest linear team","createdAt":"2023-10-24T16:09:28Z"}
{"state":"Merged","mergedAt":"2023-10-24T16:16:58Z","number":8884,"mergeCommitSha":"786d6cab78d63f15474b28219028e5d995e86862","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8884","title":"Hack for unusable SearchResult POS","createdAt":"2023-10-24T16:15:54Z"}
{"state":"Merged","mergedAt":"2023-10-24T16:19:45Z","number":8885,"mergeCommitSha":"911fc4cf321456692614c4e6842cbf4ee92bcf52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8885","title":"Remove debug","createdAt":"2023-10-24T16:18:47Z"}
{"state":"Merged","mergedAt":"2023-10-24T17:32:56Z","number":8886,"body":"![CleanShot 2023-10-24 at 09 24 18@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/4840a8c7-03c9-40aa-84c4-cac922e417df)\r\n","mergeCommitSha":"d2729973b9004d333088e33d98daa2f4862c5049","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8886","title":"Remove repetitive sentence on scm error pages","createdAt":"2023-10-24T16:24:36Z"}
{"state":"Merged","mergedAt":"2023-10-24T19:54:20Z","number":8887,"mergeCommitSha":"0189acaba9b534772c1bdc8fb22ed3d9f10a5d79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8887","title":"Remove trailing references step","createdAt":"2023-10-24T18:01:51Z"}
{"state":"Merged","mergedAt":"2023-10-24T19:26:50Z","number":8888,"mergeCommitSha":"24a5c1c35268f5cae28fd6c48add231d750b0784","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8888","title":"Update default sample question","createdAt":"2023-10-24T18:04:21Z"}
{"state":"Merged","mergedAt":"2023-10-24T19:22:32Z","number":8889,"body":"## Changes\r\n\r\n- style as a question and answer\r\n- use a variety of questions\r\n- use the repo full name\r\n- for debugging, use the question part of the summary as the document label\r\n\r\n## Alternatives\r\n\r\nOne suggestion was to generate a separate embedding for each of the question variants.\r\nChoosing not to take that approach because:\r\n - it is likely that a given user question will match multiple embedding _for the same set of repos_ having the effect of drowning the prompt with duplicate repos and starving the prompt of a diversity of repos.\r\n - increase in embedding count (minor)\r\n\r\n\r\n## Storage estimates\r\n\r\n- the static title part of each partition is approximately 173 characters\r\n- leaving 2127 characters for the repo list body (2300 - 173)\r\n- for AppDirect (1789 repos) there are 57433 bytes of repo body\r\n- which is approx 27 repo summary embeddings in total","mergeCommitSha":"718892c8296b2293a19476b1ad9048cc1beb5aa6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8889","title":"Refine repo summary generation","createdAt":"2023-10-24T18:10:10Z"}
{"state":"Merged","mergedAt":"2022-04-14T04:32:37Z","number":889,"body":"- Fixed the logic in lookup lambda function to lookup assets via assets service\r\n- Changed how we handle paths and cache keys in Lookup lambda. This caused us a bit of a pain but turns out\r\n  Cloudfront does some weird stuff with request.uri and request.origin.custom.path fields\r\n- Changed Auth lambda to remove `/assets` from uris before forwarding requests to lookup (so we get cache hits)\r\n- Had to change a lot of unit tests for both Auth and Lookup lambdas\r\n- Added a very simple Upload/Download integration test (not hooked up into CI/CD yet) so we can make sure everything is working in dev before pushing changes to prod.\r\n- Enabled caching on /assets\r\n- Added an origin request policy to CloudFront so we can pass query strings to Lookup lambda\r\n- Had to modify auth lambda to use query strings to pass a couple of pieces of info to Lookup lambda. The reason for this is that querystrings are not used as part of cache key so we won't get a miss everytime we forward an auth token to backend\r\n- A whole bunch of hacks (cdk exports) to break some CDK stack dependencies.\r\n\r\nThere's still some cleanup work to be done but this is fully functional in Dev. I'll be doing those cleanups as well as enabling integration tests (I'll add one for each asset type) in my next PR.","mergeCommitSha":"a81c1759e0535b03979afa0dd8a4ce8262d94768","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/889","title":"fix asset lookup and retrieval from s3","createdAt":"2022-04-13T22:30:14Z"}
{"state":"Merged","mergedAt":"2023-10-24T18:49:35Z","number":8890,"mergeCommitSha":"e9285af21c1342a76b94a9317d98a3ab0e168feb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8890","title":"Fix main","createdAt":"2023-10-24T18:49:29Z"}
{"state":"Merged","mergedAt":"2023-10-24T18:54:12Z","number":8891,"body":"- Fix main\r\n- Fix test\r\n","mergeCommitSha":"03412c1fe29e9fb7f43083b08678982a3379cac2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8891","title":"FixMain","createdAt":"2023-10-24T18:53:59Z"}
{"state":"Merged","mergedAt":"2023-10-24T22:07:25Z","number":8892,"body":"The min rank here is 0.9, so that we only consider sample questions that retrieve highly ranked documents. ","mergeCommitSha":"12a35cbe9f83149aaff251d3f62dee2397cebadb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8892","title":"Prioritize topics that have highly-ranked documents for onboarding sample questions","createdAt":"2023-10-24T21:49:35Z"}
{"state":"Merged","mergedAt":"2023-10-24T23:01:55Z","number":8893,"body":"We are moving to a customizable way of extracting topics.\r\n\r\n[SKIP TESTS]","mergeCommitSha":"e92f055fcc074d76f874866020f4f70558d91974","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8893","title":"Move away from terrible prompts for topic extraction","createdAt":"2023-10-24T22:36:20Z"}
{"state":"Merged","mergedAt":"2023-10-24T23:02:11Z","number":8894,"mergeCommitSha":"1665b8ef499892333797c3fe5076628e0cf47bb0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8894","title":"Clean up Linear admin console pages","createdAt":"2023-10-24T22:48:30Z"}
{"state":"Merged","mergedAt":"2023-10-24T23:27:33Z","number":8895,"mergeCommitSha":"12141315dc7099d971561c5f6dbb2cc8f0139f82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8895","title":"Handle pluralized component names","createdAt":"2023-10-24T23:02:48Z"}
{"state":"Merged","mergedAt":"2023-10-26T23:47:11Z","number":8896,"body":"* Remove old reference UIs from the sidebar and dropdown menus\r\n* Add a new inline reference UI (`MessageReferenceList`).  If there are 3 or less references, we display them in a single row.  If there are 4 or more references, we display them in a collapsed list that wraps.\r\n* Add `ExpandableWrappedList`, a component that displays a wrapped list of items, that is collapsed by default.\r\n* Add `ReferenceButton`, a pill button for rendering reference links\r\n\r\n![CleanShot 2023-10-24 at 16 07 16@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/574bf6be-0e57-4b7b-88b8-481f60f6ff30)\r\n![CleanShot 2023-10-24 at 16 06 49@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/917df2d9-a8e9-41f9-bf45-b202f29f2297)\r\n![CleanShot 2023-10-24 at 15 42 33@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/32a0a1da-d76a-49ed-bc1f-1a0d0da992ff)\r\n\r\n","mergeCommitSha":"7f5ff65a93285c4a3dbdee6d4c29cbcb5fe4cc0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8896","title":"Inline reference UI","createdAt":"2023-10-24T23:11:01Z"}
{"state":"Merged","mergedAt":"2023-10-24T23:20:47Z","number":8897,"mergeCommitSha":"59a926eec0369351d85c59b735d0796264d8fbd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8897","title":"Prompt hack Cohere reranker to promote repo summaries","createdAt":"2023-10-24T23:11:36Z"}
{"state":"Merged","mergedAt":"2023-10-25T15:56:20Z","number":8898,"body":"see: https://github.com/NextChapterSoftware/unblocked/pull/8877#discussion_r1370957102","mergeCommitSha":"b1de65db22bc8366225a4c766d596569b0fccaa0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8898","title":"Restore removed code for resizing observer","createdAt":"2023-10-25T00:07:58Z"}
{"state":"Merged","mergedAt":"2022-04-14T21:10:00Z","number":890,"body":"Creates a thread discussion given highlighted code in GH.\r\n\r\nAlso setting up some work for source mark resolution (Partially used in this PR but lot of setup for next PR) \r\n\r\nhttps://user-images.githubusercontent.com/1553313/163280614-0c13940d-5f3f-4eb2-bf78-be0b9430bc1e.mp4","mergeCommitSha":"64bdfd21c625768e0d9076d46dc0e6f86cb109f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/890","title":"Web extension - Basic discussion creation","createdAt":"2022-04-13T22:30:16Z"}
{"state":"Merged","mergedAt":"2023-10-25T01:24:34Z","number":8900,"body":"[SKIP TESTS]","mergeCommitSha":"c028db3f18cbb4941fb41d6477d49e76b89f0ec9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8900","title":"[SKIP TESTS] Fix confidence score","createdAt":"2023-10-25T01:24:09Z"}
{"state":"Merged","mergedAt":"2023-10-25T03:33:37Z","number":8901,"body":"Not sure why, but we’re only limitig topics to 4???\r\n","mergeCommitSha":"6d28b93c416baa7e7fc39f7d1d6c08927a7f6498","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8901","title":"[BUG FIX] Topics should be sorted by score","createdAt":"2023-10-25T02:48:49Z"}
{"state":"Merged","mergedAt":"2023-10-25T03:38:46Z","number":8902,"mergeCommitSha":"f54754a569126eb6eb4b722c42ce4b27f7b1cb64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8902","title":"Fix topics page","createdAt":"2023-10-25T03:28:10Z"}
{"state":"Merged","mergedAt":"2023-10-25T04:10:08Z","number":8903,"mergeCommitSha":"775c78e0a061acab51c5c7007b97e7e7373f6ae5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8903","title":"Topic extraction template update","createdAt":"2023-10-25T04:07:26Z"}
{"state":"Merged","mergedAt":"2023-10-25T04:19:22Z","number":8904,"mergeCommitSha":"b394fb17ca04ed3b6c84dcde40477a8a477e7cbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8904","title":"Increase tmimeout","createdAt":"2023-10-25T04:19:16Z"}
{"state":"Merged","mergedAt":"2023-10-25T04:41:30Z","number":8905,"body":"We should not be updating thread until the entire response and topics pipeline has completed.\r\nShoudl probably add topics generation as a followup chainable step I guess…\r\n","mergeCommitSha":"148c1664fe5528622aa2a9fe94757923e17ff613","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8905","title":"REferenceResolverStep introduced regression","createdAt":"2023-10-25T04:40:55Z"}
{"state":"Merged","mergedAt":"2023-10-25T15:56:52Z","number":8906,"body":"![CleanShot 2023-10-24 at 21 49 43@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/f73cfffe-c815-4edd-81ff-48f5156ead32)\r\n","mergeCommitSha":"0ee992c3d9557aa8742465eb385eb1d823aa2956","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8906","title":"add notion icon to landing page","createdAt":"2023-10-25T04:50:36Z"}
{"state":"Merged","mergedAt":"2023-10-25T19:06:07Z","number":8907,"body":"- `Issue`s will represent all existing (jira, linear, github) issues, as well as all future issues (asana, trello, bitbucket, etc).\r\n\r\n- No longer exposed to clients. This gives us more flexibility to change it.\r\n\r\n- Planning to add lot's more \"Summary\" types to this enum.","mergeCommitSha":"c0a15bfc84cdf2f57040491441ba0f6423fe262d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8907","title":"Cleanup InsightType by removing superfluous fields","createdAt":"2023-10-25T05:54:58Z"}
{"state":"Merged","mergedAt":"2023-10-25T06:39:04Z","number":8908,"mergeCommitSha":"c6775ec7a55453292e85c7931734018baf9ca23b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8908","title":"Fix topic genertion","createdAt":"2023-10-25T06:38:29Z"}
{"state":"Merged","mergedAt":"2023-10-25T07:19:33Z","number":8909,"mergeCommitSha":"d1244519450c658d2cb48635062735ca190e079e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8909","title":"Fix base test","createdAt":"2023-10-25T07:19:18Z"}
{"state":"Merged","mergedAt":"2022-04-14T18:08:34Z","number":891,"body":"Adds basic support for copy and paste and loading images with source files.\r\nI plan to transition this over to asset service soon.\r\n\r\n![CleanShot 2022-04-13 at 16 40 47](https://user-images.githubusercontent.com/3806658/163287117-5dd8853d-b8f6-4ef7-8d15-11c1fd9c703e.gif)\r\n","mergeCommitSha":"73fad316f5ad8b20d10c035c441b7f35dec26edd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/891","title":"Add copy and paste support for images and load images into editor","createdAt":"2022-04-13T23:18:16Z"}
{"state":"Merged","mergedAt":"2023-10-25T09:44:07Z","number":8910,"mergeCommitSha":"60cbfbefac00c935d0dba6dfd77ac62f8cfd90b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8910","title":"Fix topic ompletion","createdAt":"2023-10-25T09:42:06Z"}
{"state":"Merged","mergedAt":"2023-10-26T22:31:50Z","number":8911,"body":"All of this code is dashboard-only.  Nearly all of these UIs are already only viewable in full mode, so we can remove these.","mergeCommitSha":"771a0f04d1d71db037cc9bef73ea28e8c6252bbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8911","title":"Remove FeatureTopicDescriptions usage in TS clients","createdAt":"2023-10-25T17:37:10Z"}
{"state":"Merged","mergedAt":"2023-10-25T18:52:17Z","number":8912,"mergeCommitSha":"e6b06f244b4900d247cf69243f944c8a3d657269","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8912","title":"Reduce tokens","createdAt":"2023-10-25T18:47:45Z"}
{"state":"Merged","mergedAt":"2023-10-25T19:26:25Z","number":8913,"mergeCommitSha":"e92a55883586bb75a2a6d8ab66d74f1b22cccc4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8913","title":"Fix bug","createdAt":"2023-10-25T19:18:08Z"}
{"state":"Merged","mergedAt":"2023-10-26T05:08:54Z","number":8914,"body":"- Add the ability to Skip ML stack deployments \r\n- Updated us-east-2 cdk config \r\n- Made the EKS API endpoint private \r\n- Modified Network stack and EKS VPN stack to deal with a bug \r\nAll changes for us-east-2 have been deployed. There's a small change to us-west-2 to add the routes for east-2 EKS cluster that hasn't been deployed. \r\n- Added a new stack to create peering connections between east-2 EKS VPC and west-2 core VPC\r\n- Added configuration so that west-2 VPC creates necessary routing for east-2 EKS VPC \r\n\r\n```\r\nStack NetworkStack\r\nSecurity Group Changes\r\n┌───┬──────────────────────────────────┬─────┬───────────┬──────────────┐\r\n│   │ Group                            │ Dir │ Protocol  │ Peer         │\r\n├───┼──────────────────────────────────┼─────┼───────────┼──────────────┤\r\n│ + │ ${CORE-VPC.DefaultSecurityGroup} │ In  │ ICMP 8--1 │ 10.20.0.0/16 │\r\n└───┴──────────────────────────────────┴─────┴───────────┴──────────────┘\r\n(NOTE: There may be security-related changes not in this list. See https://github.com/aws/aws-cdk/issues/1299)\r\n\r\nResources\r\n[-] AWS::EC2::Route VPCPeeringRouteBetweenEKSAndCore-10.0.0.0--19 VPCPeeringRouteBetweenEKSAndCore1000019 destroy\r\n[-] AWS::EC2::Route VPCPeeringRouteBetweenEKSAndCore-10.0.64.0--19 VPCPeeringRouteBetweenEKSAndCore10064019 destroy\r\n[+] AWS::EC2::SecurityGroupIngress DefaultSecurityGroup/from 10.20.0.0_16:ICMP Type 8 DefaultSecurityGroupfrom10200016ICMPType824FADCFA\r\n[+] AWS::EC2::Route VPCPeeringRouteBetweenCoreAndCORE-US-EAST-2-10.5.96.0--20 VPCPeeringRouteBetweenCoreAndCOREUSEAST210596020\r\n[+] AWS::EC2::Route VPCPeeringRouteBetweenCoreAndCORE-US-EAST-2-10.5.112.0--20 VPCPeeringRouteBetweenCoreAndCOREUSEAST2105112020\r\n[+] AWS::EC2::Route VPCPeeringRouteBetweenCoreAndCORE-US-EAST-2-10.5.128.0--20 VPCPeeringRouteBetweenCoreAndCOREUSEAST2105128020\r\n[+] AWS::EC2::Route VPCPeeringRouteBetweenCoreAndCORE-US-EAST-2-10.5.48.0--20 VPCPeeringRouteBetweenCoreAndCOREUSEAST210548020\r\n[+] AWS::EC2::Route VPCPeeringRouteBetweenCoreAndCORE-US-EAST-2-10.5.64.0--20 VPCPeeringRouteBetweenCoreAndCOREUSEAST210564020\r\n[+] AWS::EC2::Route VPCPeeringRouteBetweenCoreAndCORE-US-EAST-2-10.5.80.0--20 VPCPeeringRouteBetweenCoreAndCOREUSEAST210580020\r\n[+] AWS::EC2::Route VPCPeeringRouteBetweenCoreAndCORE-US-EAST-2-10.5.0.0--20 VPCPeeringRouteBetweenCoreAndCOREUSEAST21050020\r\n[+] AWS::EC2::Route VPCPeeringRouteBetweenCoreAndCORE-US-EAST-2-10.5.16.0--20 VPCPeeringRouteBetweenCoreAndCOREUSEAST210516020\r\n[+] AWS::EC2::Route VPCPeeringRouteBetweenCoreAndCORE-US-EAST-2-10.5.32.0--20 VPCPeeringRouteBetweenCoreAndCOREUSEAST210532020\r\n```","mergeCommitSha":"c0efb4e7ea43f80010e5dd80ad6c7355e45550f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8914","title":"Setup east 2 prod network","createdAt":"2023-10-25T19:50:21Z"}
{"state":"Merged","mergedAt":"2023-10-25T20:06:33Z","number":8915,"body":"PollingBackgroundJob should never die with timeout\r\n","mergeCommitSha":"5e7016efd50f8ac416706cab69a4e7d5b14a7929","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8915","title":"PollingBackgroundJob was dying with timeout","createdAt":"2023-10-25T19:52:17Z"}
{"state":"Merged","mergedAt":"2023-10-25T20:29:19Z","number":8916,"mergeCommitSha":"21440a2dbcf0c53621aa93e196a39597ae2fe72d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8916","title":"[SKIP TESTS] Fix admin console","createdAt":"2023-10-25T20:25:48Z"}
{"state":"Merged","mergedAt":"2023-10-25T21:30:25Z","number":8917,"mergeCommitSha":"0e690f1f857b5e0af22b4258f98ab4b960d99936","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8917","title":"Fix insight type deserialization","createdAt":"2023-10-25T21:07:24Z"}
{"state":"Merged","mergedAt":"2023-10-26T02:15:23Z","number":8918,"body":"- use for onboarding repo selection\n- use for any other purposes (topic generation)","mergeCommitSha":"2abb4ddb42b5f485ce3d7310288db1ed41b25bd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8918","title":"Record SCM repo activity to use as a signal for repo selection","createdAt":"2023-10-25T21:08:47Z"}
{"state":"Merged","mergedAt":"2023-10-27T15:57:24Z","number":8919,"body":"* `Message` gains an optional `suggestions` property, of type `MessageSuggestions`.\r\n* `MessageSuggestions` has an array of `FollowOnSuggestion` objects\r\n* Each `FollowOnSuggestion` has a `question` string.\r\n\r\nWe could collapse this down to an array of strings maybe (I'm open to doing so) but I figured we may need to add extra properties to the suggestions, and moving from an array of strings to objects requires a revision...","mergeCommitSha":"b6cf3e141ad43c04d5ae12aa642507d715149844","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8919","title":"Follow-on suggestion API","createdAt":"2023-10-25T21:12:58Z"}
{"state":"Merged","mergedAt":"2022-04-13T23:57:14Z","number":892,"body":"Fixes `PUT /sourcepoints API is failing: see logzio for invalid json response body at https://dev.getunblocked.com/api/teams/c9c47779-235f-4ca9-aefb-96758fa96650/sourcepoints reason: Unexpected end of JSON input`\r\n\r\nhttps://www.notion.so/nextchaptersoftware/SourceMark-Open-Issues-3cdea5e5cd37494c94b2899d259408bf#4a12079a2db242d384a56beaadf3600a","mergeCommitSha":"34637c3262d3914a9aa6d664b3d1da28e0187d14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/892","title":"204s shouldn't specify content","createdAt":"2022-04-13T23:30:41Z"}
{"state":"Merged","mergedAt":"2023-10-25T21:20:45Z","number":8920,"mergeCommitSha":"b164c880b2d7c35dd54b1dde28256291b476e1a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8920","title":"Improve query","createdAt":"2023-10-25T21:20:15Z"}
{"state":"Merged","mergedAt":"2023-10-25T22:27:12Z","number":8921,"body":"Change the TS linter so that when you pass a static method as a function, you are forced to use the arrow syntax.  Because of legacy JS crap, unbound static methods don't have a reliable `this` property, which causes bugs like this: https://github.com/NextChapterSoftware/unblocked/pull/8913\r\n\r\nThis is a syntax we barely ever use though, but this will prevent future foot-shoots.","mergeCommitSha":"3cf82372b13b9438d63f59e467d0ec04a732401b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8921","title":"Disallow unbound static methods in TS","createdAt":"2023-10-25T22:08:54Z"}
{"state":"Merged","mergedAt":"2023-10-25T23:35:51Z","number":8922,"body":"Recombine document partitions in the Q&A prompt","mergeCommitSha":"3d5e740041d66c359ae26f90b84c42cccd3485a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8922","title":"Recombine document partitions in the Q&A prompt","createdAt":"2023-10-25T22:26:34Z"}
{"state":"Merged","mergedAt":"2023-10-26T00:46:00Z","number":8923,"mergeCommitSha":"9ca4265fccf3e449e3d5fc187f61c7131430d7f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8923","title":"Fix test","createdAt":"2023-10-26T00:45:52Z"}
{"state":"Merged","mergedAt":"2023-10-26T02:34:54Z","number":8924,"body":"When a repo is detected to be empty, then we skip ingestion and mark the `codeIngestLastCommitSha` with a EMPTY_REPO_COMMIT_SHA sentinel value to indicate that the repo should not be considered for reingestion until the next round of ingestions (5 days currently).\n\nHowever, by the time we re-ingest the user may have added commits to the repo. In order to account for this it's important that we do not send the nonsensical EMPTY_REPO_COMMIT_SHA to the code ingestion runner, since Git wil barf on it.","mergeCommitSha":"f9beec42b1ef86afac249b870b699bdcbbc7233d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8924","title":"Account for EMPTY_REPO_COMMIT_SHA in CodeIngestionStateMachineProvider","createdAt":"2023-10-26T02:03:40Z"}
{"state":"Merged","mergedAt":"2023-10-26T05:42:36Z","number":8925,"mergeCommitSha":"7d7e805b61e5f1d0685f3fd8dada3d793549a2ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8925","title":"Remove completed migration and make column non-nullable","createdAt":"2023-10-26T02:23:56Z"}
{"state":"Merged","mergedAt":"2023-10-26T03:19:09Z","number":8926,"body":"Clipping the sanitized input caused a lot of issues in many places:\r\n\r\n- Message clipping in the prompt\r\n- Question clipping in the prompt\r\n- Re-rank clipping (should be done elsewhere)\r\n- Admin console doc rendering\r\n\r\nThere are other places where doc clipping might be useful but it should be managed on a case-by-case basis since the docs are ostensibly _already clipped_","mergeCommitSha":"dbb37063d8561c995d6497d3a3673255b077773e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8926","title":"Remove sanitizer output max size","createdAt":"2023-10-26T02:34:26Z"}
{"state":"Merged","mergedAt":"2023-10-26T04:27:25Z","number":8927,"mergeCommitSha":"dc5af08718bd711dd7413edef56680e4ee5337a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8927","title":"[SKIP TESTS] First stab at topic page improvements","createdAt":"2023-10-26T03:58:50Z"}
{"state":"Merged","mergedAt":"2023-10-26T04:12:51Z","number":8928,"mergeCommitSha":"bebea697580b42a9bb730e00e90fcece628cadc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8928","title":"More verbose logs","createdAt":"2023-10-26T04:12:28Z"}
{"state":"Merged","mergedAt":"2023-10-26T04:46:10Z","number":8929,"body":"<img width=\"832\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/d677ec32-3b10-4249-a46f-96358245b63c\">\r\n","mergeCommitSha":"294aaca268e436ec1b79bbc961388532519d9ba4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8929","title":"Restore classname","createdAt":"2023-10-26T04:34:42Z"}
{"state":"Merged","mergedAt":"2022-04-14T00:09:37Z","number":893,"body":"- all error objects have the exact same shape in the unblocked API, so there is no point in documenting\r\n- all standard http code should be handled in a standard way (ie. don't retry 4xx, do retry 5xx)\r\n- only _non-standard_ codes should be documented, the rest is just debt","mergeCommitSha":"c2bbdcf8756567ed30f3044a2547a86952e68803","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/893","title":"Remove unnecessary error response codes","createdAt":"2022-04-13T23:58:46Z"}
{"state":"Merged","mergedAt":"2023-10-26T22:31:11Z","number":8930,"body":"If you try to install our GH app when you are not an org owner, you can't install it, you can ask your org admin to install it.  After you make the request, you are redirected back to Unblocked, and now display this UI.\r\n\r\n![CleanShot 2023-10-26 at 13 18 10@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/d1686d38-6994-4da6-8194-dc055674e881)\r\n","mergeCommitSha":"e53c0610ee92652d480a6def6182634dbe9e594f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8930","title":"Add view to display when waiting for GH admin to approve the GH app","createdAt":"2023-10-26T04:48:29Z"}
{"state":"Merged","mergedAt":"2023-10-26T06:24:30Z","number":8931,"mergeCommitSha":"a689caed946bd031294464810e85c9e08facd37f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8931","title":"Writ efiles out","createdAt":"2023-10-26T06:24:16Z"}
{"state":"Merged","mergedAt":"2023-10-26T06:56:44Z","number":8932,"mergeCommitSha":"847057ac622113f1c0f347d5a16f41d2b21e9fb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8932","title":"Clean up output","createdAt":"2023-10-26T06:56:28Z"}
{"state":"Merged","mergedAt":"2023-10-26T07:44:40Z","number":8933,"mergeCommitSha":"388c1bca5a7cf351ce9b1ef7077b98fcf81a8e9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8933","title":"Minor tweaks to repo page","createdAt":"2023-10-26T07:20:15Z"}
{"state":"Merged","mergedAt":"2023-10-26T08:49:42Z","number":8934,"mergeCommitSha":"8cb0694b614043017cb00bde9d229af253ac5544","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8934","title":"Add topics repo pages","createdAt":"2023-10-26T07:45:50Z"}
{"state":"Merged","mergedAt":"2023-10-26T09:00:59Z","number":8935,"mergeCommitSha":"6cbc05b5d89cfe11df3e2e18a4db6080f71e2484","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8935","title":"hdba","createdAt":"2023-10-26T08:59:21Z"}
{"state":"Merged","mergedAt":"2023-10-26T09:28:18Z","number":8936,"mergeCommitSha":"2689c8d29bb84aff7a025aaa7ca6ea62e60006cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8936","title":"Use instructor","createdAt":"2023-10-26T09:28:11Z"}
{"state":"Merged","mergedAt":"2023-10-27T01:33:51Z","number":8937,"mergeCommitSha":"c00bae1d9d611dd90c044680c20cda897e28d0ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8937","title":"Fix admin console routes","createdAt":"2023-10-26T15:54:32Z"}
{"state":"Merged","mergedAt":"2023-10-26T18:41:27Z","number":8938,"body":"- https://ui.honeycomb.io/unblocked/environments/development/result/Gedb8dAQKcF/trace/354tGpZX4A2?fields[]=c_name&fields[]=c_service.name&span=195de3dfa6284e46\r\n- https://chapter2global.slack.com/archives/C02HEVCCJA3/p1698342617890829","mergeCommitSha":"b3810110bf07a935aad3e8777b18eeffe07d8373","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8938","title":"Fix Bitbucket repos API projection bug","createdAt":"2023-10-26T17:55:37Z"}
{"state":"Merged","mergedAt":"2023-10-26T19:16:36Z","number":8939,"mergeCommitSha":"dd0eb3268bd895de34440c0309cbc8817a2e1c4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8939","title":"Remove keywords","createdAt":"2023-10-26T19:13:17Z"}
{"state":"Merged","mergedAt":"2022-04-14T00:19:43Z","number":894,"body":"Don't think we need pararam, only header.","mergeCommitSha":"8a69bad8c542744ff88987f12c2ca479fafe87d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/894","title":"Remove param","createdAt":"2022-04-14T00:08:58Z"}
{"state":"Merged","mergedAt":"2023-10-26T20:06:33Z","number":8940,"mergeCommitSha":"8bfad0140ab7ac661983fe928482aee8822b9803","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8940","title":"Remove VSCode folder uploader","createdAt":"2023-10-26T19:54:09Z"}
{"state":"Merged","mergedAt":"2023-10-26T22:09:51Z","number":8941,"body":"## Added new properties\r\n - createdAt\r\n - lastActiveAt\r\n - hasCompletedProcessing\r\n - isEmpty\r\n","mergeCommitSha":"1f11b5af8068cd766b142a9893dd5784ba6324e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8941","title":"Support repo selection views by exposing properties","createdAt":"2023-10-26T20:20:25Z"}
{"state":"Merged","mergedAt":"2023-10-27T21:55:27Z","number":8942,"body":"I've always gotten confused which id (person, teamMember, identity) log data is stored with, which makes debugging annoying.  The person ID is what we show in intercom, and is usually what I start out with when trying to troubleshoot customer issues, so logging it would be helpful.","mergeCommitSha":"4c7232e0051315109ed5dec95d3d77bdc2e56b7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8942","title":"Add personId to client logs","createdAt":"2023-10-26T20:27:02Z"}
{"state":"Merged","mergedAt":"2023-10-26T21:56:58Z","number":8943,"body":"<img width=\"1497\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/56f848af-8f1a-470f-9487-1d11ffd04989\">\r\n","mergeCommitSha":"5a8700f595f48152996f5eff94986243fa0ff0e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8943","title":"Always show menu popover","createdAt":"2023-10-26T20:46:19Z"}
{"state":"Merged","mergedAt":"2023-10-26T21:41:54Z","number":8944,"mergeCommitSha":"735805efe35dc1377978006f49df73373f346198","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8944","title":"Admin console improvements","createdAt":"2023-10-26T21:30:11Z"}
{"state":"Merged","mergedAt":"2023-11-15T06:44:48Z","number":8945,"body":"The existing field `isScmConnected` was overloaded to simultaneously mean both:\n\n - user authorized the repo from the SCM, for GitHub\n - user selected the repo in Unblocked, for other SCM providers\n\nNow that user can select repos to use in the GitHub case, we introduce a separate DB field to model that state:\n\n - `isScmConnected` — user authorized the repo from the SCM (always true when not GitHub)\n - `isSelected` — user selected the repo in Unblocked","mergeCommitSha":"e670692a54eae466132e77eddbbd259cbf4b975e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8945","title":"Persist user selected repos","createdAt":"2023-10-26T22:02:14Z"}
{"state":"Merged","mergedAt":"2023-10-26T22:15:56Z","number":8946,"mergeCommitSha":"10f457d17ec3c651845e507ae95f267550c38aa3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8946","title":"Remove unused navigator files","createdAt":"2023-10-26T22:04:45Z"}
{"state":"Merged","mergedAt":"2023-10-26T23:07:55Z","number":8947,"mergeCommitSha":"bb15ba4c2cb44ea00e24dfc4aa073fcea47ae84c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8947","title":"Mahdi alert","createdAt":"2023-10-26T22:20:14Z"}
{"state":"Merged","mergedAt":"2023-10-26T22:37:01Z","number":8948,"mergeCommitSha":"8cab57cd279146a29d533c4aa0ba36f726a5c99b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8948","title":"Fix ecr replication for east","createdAt":"2023-10-26T22:36:54Z"}
{"state":"Merged","mergedAt":"2023-10-27T03:13:29Z","number":8949,"body":"Adding quote from Trevin at Big Cartel.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/13353189/50c1b47f-12c3-448a-bfce-c17dabff1dea\r\n\r\n","mergeCommitSha":"74abb3fad284f82933d570a09611cbb67f46ea64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8949","title":"Adding Big Cartel to landing page","createdAt":"2023-10-26T23:49:41Z"}
{"state":"Merged","mergedAt":"2022-04-19T20:36:04Z","number":895,"body":"## Summary\r\n\r\nExpected behaviour:\r\n1. The first time the app is launched, the permissions request sheet will be presented\r\n2. Clicking allow on the mic and camera permissions brings up the usual permissions actions dialogs. Clicking allow will turn the button to a green check back on the request sheet\r\n3. Clicking on screen recording will take you to system preferences with screen recording selected\r\n4. Modifying screen recording settings should prompt to quit and restart the app. \r\n5. After restarting the app you should not see the sheet, but the permissions should be set \r\n\r\nThings that aren't completely wired up yet:\r\n- Starting the camera as soon as permissions are given. It's not clear if we even want to do that\r\n- Quitting the app from settings seems to fail when the permissions sheet is up. There's apparently an AppKit hack for that.\r\n\r\n\r\nTo play around with the sheet toggle the mic button (for now)","mergeCommitSha":"4065120fbf03f8db7c0a4665490bc99294d11396","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/895","title":"App permissions","createdAt":"2022-04-14T00:14:51Z"}